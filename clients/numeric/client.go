package numeric

import (
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"strconv"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc" //
	pb "golang.52tt.com/protocol/services/numericsvr"
)

const (
	serviceName = "numeric"
)

type UserNumericValue = pb.PersonalNumeric

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewNumericSvrClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() pb.NumericSvrClient { return c.Stub().(pb.NumericSvrClient) }

func (c *Client) GetPersonalNumeric(ctx context.Context, uid uint32) (uint32, uint32, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().GetPersonalNumeric(ctx, &pb.GetPersonalNumericReq{
		Uid: uid,
	}, grpc.WaitForReady(true))
	return r.GetConsumeNumeric(), r.GetCharmNumeric(), protocol.ToServerError(err)
}

func (c *Client) BatchGetPersonalNumeric(ctx context.Context, uidList []uint32) (map[uint32]*UserNumericValue, protocol.ServerError) {
	userMap := make(map[uint32]*UserNumericValue)
	if len(uidList) > 0 {
		ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", "0"))
		r, err := c.typedStub().BatchGetPersonalNumeric(ctx, &pb.BatchGetPersonalNumericReq{
			UidList: uidList,
		})

		if err != nil {
			return userMap, protocol.ToServerError(err)
		}
		for _, u := range r.NumericList {
			userMap[u.GetUid()] = u
		}
	}
	return userMap, nil
}

func (c *Client) GetPersonalNumericData(ctx context.Context, uid uint32) (*pb.GetPersonalNumericResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	r, err := c.typedStub().GetPersonalNumeric(ctx, &pb.GetPersonalNumericReq{
		Uid: uid,
	}, grpc.WaitForReady(true))
	return r, protocol.ToServerError(err)
}

// Deprecated:
// AddUserNumeric is deprecated, use numeric-go instead
func (c *Client) AddUserNumeric(ctx context.Context, Req *pb.AddUserNumericReq) (*pb.AddUserNumericResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", "0"))
	r, err := c.typedStub().AddUserNumeric(ctx, Req)
	if err != nil {
		return r, protocol.ToServerError(err)
	}
	return r, nil
}

func (c *Client) GetGuildGiftTotalValue(ctx context.Context, guildId uint32) (*pb.GetGuildGiftTotalValueResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", "0"))
	r, err := c.typedStub().GetGuildGiftTotalValue(ctx, &pb.GetGuildGiftTotalValueReq{GuildId: guildId})
	if nil != err {
		return nil, protocol.ToServerError(err)
	}

	return r, nil
}

func (c *Client) GetRankList(ctx context.Context, req *pb.GetRankListReq) (*pb.GetRankListResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(req.GetUid()))))
	r, err := c.typedStub().GetRankList(ctx, req, grpc.WaitForReady(true))
	return r, protocol.ToServerError(err)
}
