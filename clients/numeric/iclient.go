// Code generated by quicksilver-cli. DO NOT EDIT.
package numeric

import (
	"context"
	"golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/numericsvr"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetGuildGiftTotalValue(ctx context.Context, guildId uint32) (*pb.GetGuildGiftTotalValueResp, protocol.ServerError)
	GetPersonalNumeric(ctx context.Context, uid uint32) (uint32, uint32, protocol.ServerError)
	GetPersonalNumericData(ctx context.Context, uid uint32) (*pb.GetPersonalNumericResp, protocol.ServerError)
	GetRankList(ctx context.Context, req *pb.GetRankListReq) (*pb.GetRankListResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
