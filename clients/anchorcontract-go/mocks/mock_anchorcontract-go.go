// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/anchorcontract-go (interfaces: IClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	anchorcontract_go "golang.52tt.com/protocol/services/anchorcontract-go"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

func (m *MockIClient) CheckIsTotalNewMultiAnchor(ctx context.Context, req *anchorcontract_go.CheckIsTotalNewMultiAnchorReq) (*anchorcontract_go.CheckIsTotalNewMultiAnchorResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) GetContractChangeInfo(ctx context.Context, req *anchorcontract_go.GetContractChangeInfoReq) (*anchorcontract_go.GetContractChangeInfoResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) GetNegotiateReasonType(ctx context.Context, req *anchorcontract_go.GetNegotiateReasonTypeReq) (*anchorcontract_go.GetNegotiateReasonTypeResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) GetRejectReason(ctx context.Context, req *anchorcontract_go.GetRejectReasonReq) (*anchorcontract_go.GetRejectReasonResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) HandleContractChange(ctx context.Context, req *anchorcontract_go.HandleContractChangeReq) (*anchorcontract_go.HandleContractChangeResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) InviteMemberChangeWorkerType(ctx context.Context, req *anchorcontract_go.InviteMemberChangeWorkerTypeReq) (*anchorcontract_go.InviteMemberChangeWorkerTypeResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) GetNeedConfirmWorkerType(ctx context.Context, req *anchorcontract_go.GetNeedConfirmWorkerTypeReq) (*anchorcontract_go.GetNeedConfirmWorkerTypeResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) ModifyWorkerType(ctx context.Context, req *anchorcontract_go.ModifyWorkerTypeReq) (*anchorcontract_go.ModifyWorkerTypeResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) CensorVideo(ctx context.Context, req *anchorcontract_go.CensorVideoReq) (*anchorcontract_go.CensorVideoResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) CheckUserGreatLiveAnchor(ctx context.Context, req *anchorcontract_go.CheckUserGreatLiveAnchorReq) (*anchorcontract_go.CheckUserGreatLiveAnchorResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) ContractClaimObsToken(ctx context.Context, req *anchorcontract_go.ContractClaimObsTokenReq) (*anchorcontract_go.ContractClaimObsTokenResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) GetAnchorAgentUid(ctx context.Context, uids []uint32) (*anchorcontract_go.GetAnchorAgentUidResp, error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) GetSignEsportAuditToken(ctx context.Context, applyId uint32) (string, error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) GetCancelContractApplyList(ctx context.Context, req *anchorcontract_go.GetCancelContractApplyListReq) (*anchorcontract_go.GetCancelContractApplyListResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) ApplyCancelContractNew(ctx context.Context, req *anchorcontract_go.ApplyCancelContractNewReq) protocol.ServerError {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) ApplySignContract(ctx context.Context, req *anchorcontract_go.ApplySignContractReq) protocol.ServerError {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) GetContractWorkerConfigs(ctx context.Context, req *anchorcontract_go.GetContractWorkerConfigsReq) (*anchorcontract_go.GetContractWorkerConfigsResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) CheckCanApplyCancelContractV2(ctx context.Context, req *anchorcontract_go.CheckCanApplyCancelContractV2Req) (*anchorcontract_go.CheckCanApplyCancelContractV2Resp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) GetCancelContractTypeList(ctx context.Context, req *anchorcontract_go.GetCancelContractTypeListReq) (*anchorcontract_go.GetCancelContractTypeListResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) GetGuildSignRight(ctx context.Context, req *anchorcontract_go.GetGuildSignRightReq) (*anchorcontract_go.GetGuildSignRightResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) SetGuildCancelContractType(ctx context.Context, req *anchorcontract_go.SetGuildCancelContractTypeReq) (*anchorcontract_go.SetGuildCancelContractTypeResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) UpdateGuildSignRight(ctx context.Context, req *anchorcontract_go.UpdateGuildSignRightReq) (*anchorcontract_go.UpdateGuildSignRightResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) InvitePromote(ctx context.Context, req *anchorcontract_go.InvitePromoteReq) (*anchorcontract_go.InvitePromoteResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) GetUserPromoteInviteInfo(ctx context.Context, req *anchorcontract_go.GetUserPromoteInviteInfoReq) (*anchorcontract_go.GetUserPromoteInviteInfoResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) ProcPromoteInvite(ctx context.Context, req *anchorcontract_go.ProcPromoteInviteReq) (*anchorcontract_go.ProcPromoteInviteResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) GetCancelPayAmount(ctx context.Context, req *anchorcontract_go.GetCancelPayAmountReq) (*anchorcontract_go.GetCancelPayAmountResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) LockCancelPayAmount(ctx context.Context, req *anchorcontract_go.LockCancelPayAmountReq) (*anchorcontract_go.LockCancelPayAmountResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

func (m *MockIClient) GetGuildContractByCond(ctx context.Context, req *anchorcontract_go.GetGuildContractByCondReq) (*anchorcontract_go.GetGuildContractByCondResp, protocol.ServerError) {
	//TODO implement me
	panic("implement me")
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddSignWhiteUid mocks base method.
func (m *MockIClient) AddSignWhiteUid(arg0 context.Context, arg1 *anchorcontract_go.AddSignWhiteUidReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddSignWhiteUid", arg0, arg1)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// AddSignWhiteUid indicates an expected call of AddSignWhiteUid.
func (mr *MockIClientMockRecorder) AddSignWhiteUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSignWhiteUid", reflect.TypeOf((*MockIClient)(nil).AddSignWhiteUid), arg0, arg1)
}

// ApplySignDoyen mocks base method.
func (m *MockIClient) ApplySignDoyen(arg0 context.Context, arg1 *anchorcontract_go.ApplySignDoyenReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplySignDoyen", arg0, arg1)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// ApplySignDoyen indicates an expected call of ApplySignDoyen.
func (mr *MockIClientMockRecorder) ApplySignDoyen(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplySignDoyen", reflect.TypeOf((*MockIClient)(nil).ApplySignDoyen), arg0, arg1)
}

// ApplySignEsport mocks base method.
func (m *MockIClient) ApplySignEsport(arg0 context.Context, arg1 *anchorcontract_go.ApplySignEsportReq) (*anchorcontract_go.ApplySignEsportResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplySignEsport", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.ApplySignEsportResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ApplySignEsport indicates an expected call of ApplySignEsport.
func (mr *MockIClientMockRecorder) ApplySignEsport(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplySignEsport", reflect.TypeOf((*MockIClient)(nil).ApplySignEsport), arg0, arg1)
}

// BatchGetAnchorIdentity mocks base method.
func (m *MockIClient) BatchGetAnchorIdentity(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.BatchGetAnchorIdentityReq) (*anchorcontract_go.BatchGetAnchorIdentityResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAnchorIdentity", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGetAnchorIdentityResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetAnchorIdentity indicates an expected call of BatchGetAnchorIdentity.
func (mr *MockIClientMockRecorder) BatchGetAnchorIdentity(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAnchorIdentity", reflect.TypeOf((*MockIClient)(nil).BatchGetAnchorIdentity), arg0, arg1, arg2)
}

// BatchGetApplyBlacklist mocks base method.
func (m *MockIClient) BatchGetApplyBlacklist(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.BatchGetApplyBlacklistReq) (*anchorcontract_go.BatchGetApplyBlacklistResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetApplyBlacklist", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGetApplyBlacklistResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetApplyBlacklist indicates an expected call of BatchGetApplyBlacklist.
func (mr *MockIClientMockRecorder) BatchGetApplyBlacklist(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetApplyBlacklist", reflect.TypeOf((*MockIClient)(nil).BatchGetApplyBlacklist), arg0, arg1, arg2)
}

// BatchGetContractInfo mocks base method.
func (m *MockIClient) BatchGetContractInfo(arg0 context.Context, arg1 *anchorcontract_go.BatchGetContractInfoReq) (*anchorcontract_go.BatchGetContractInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetContractInfo", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGetContractInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetContractInfo indicates an expected call of BatchGetContractInfo.
func (mr *MockIClientMockRecorder) BatchGetContractInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetContractInfo", reflect.TypeOf((*MockIClient)(nil).BatchGetContractInfo), arg0, arg1)
}

// BatchGetLiveAnchorCert mocks base method.
func (m *MockIClient) BatchGetLiveAnchorCert(arg0 context.Context, arg1 []uint32) (map[uint32]*anchorcontract_go.BatchGetLiveAnchorCertResp_LiveAnchorCertInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetLiveAnchorCert", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*anchorcontract_go.BatchGetLiveAnchorCertResp_LiveAnchorCertInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetLiveAnchorCert indicates an expected call of BatchGetLiveAnchorCert.
func (mr *MockIClientMockRecorder) BatchGetLiveAnchorCert(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetLiveAnchorCert", reflect.TypeOf((*MockIClient)(nil).BatchGetLiveAnchorCert), arg0, arg1)
}

// BatchGetUserAnchorIdentityLog mocks base method.
func (m *MockIClient) BatchGetUserAnchorIdentityLog(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.BatchGetUserAnchorIdentityLogReq) (*anchorcontract_go.BatchGetUserAnchorIdentityLogResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserAnchorIdentityLog", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGetUserAnchorIdentityLogResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserAnchorIdentityLog indicates an expected call of BatchGetUserAnchorIdentityLog.
func (mr *MockIClientMockRecorder) BatchGetUserAnchorIdentityLog(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserAnchorIdentityLog", reflect.TypeOf((*MockIClient)(nil).BatchGetUserAnchorIdentityLog), arg0, arg1, arg2)
}

// BatchGetUserApplySignRecord mocks base method.
func (m *MockIClient) BatchGetUserApplySignRecord(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.BatchGetUserApplySignRecordReq) (*anchorcontract_go.BatchGetUserApplySignRecordResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserApplySignRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGetUserApplySignRecordResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserApplySignRecord indicates an expected call of BatchGetUserApplySignRecord.
func (mr *MockIClientMockRecorder) BatchGetUserApplySignRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserApplySignRecord", reflect.TypeOf((*MockIClient)(nil).BatchGetUserApplySignRecord), arg0, arg1, arg2)
}

// BatchGetUserContract mocks base method.
func (m *MockIClient) BatchGetUserContract(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.BatchGetUserContractReq) (*anchorcontract_go.BatchGetUserContractResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserContract", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGetUserContractResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserContract indicates an expected call of BatchGetUserContract.
func (mr *MockIClientMockRecorder) BatchGetUserContract(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserContract", reflect.TypeOf((*MockIClient)(nil).BatchGetUserContract), arg0, arg1, arg2)
}

// BatchGetUserContractCacheInfo mocks base method.
func (m *MockIClient) BatchGetUserContractCacheInfo(arg0 context.Context, arg1 *anchorcontract_go.BatchGetUserContractCacheInfoReq) (*anchorcontract_go.BatchGetUserContractCacheInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserContractCacheInfo", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGetUserContractCacheInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserContractCacheInfo indicates an expected call of BatchGetUserContractCacheInfo.
func (mr *MockIClientMockRecorder) BatchGetUserContractCacheInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserContractCacheInfo", reflect.TypeOf((*MockIClient)(nil).BatchGetUserContractCacheInfo), arg0, arg1)
}

// BatchGetUserExamineCert mocks base method.
func (m *MockIClient) BatchGetUserExamineCert(arg0 context.Context, arg1 []uint32) (*anchorcontract_go.BatchGetUserExamineCertResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserExamineCert", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGetUserExamineCertResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserExamineCert indicates an expected call of BatchGetUserExamineCert.
func (mr *MockIClientMockRecorder) BatchGetUserExamineCert(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserExamineCert", reflect.TypeOf((*MockIClient)(nil).BatchGetUserExamineCert), arg0, arg1)
}

// BatchGetUserLiveAnchorExamine mocks base method.
func (m *MockIClient) BatchGetUserLiveAnchorExamine(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.BatchGetUserLiveAnchorExamineReq) (*anchorcontract_go.BatchGetUserLiveAnchorExamineResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserLiveAnchorExamine", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGetUserLiveAnchorExamineResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserLiveAnchorExamine indicates an expected call of BatchGetUserLiveAnchorExamine.
func (mr *MockIClientMockRecorder) BatchGetUserLiveAnchorExamine(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserLiveAnchorExamine", reflect.TypeOf((*MockIClient)(nil).BatchGetUserLiveAnchorExamine), arg0, arg1, arg2)
}

// BatchGuildExtensionContract mocks base method.
func (m *MockIClient) BatchGuildExtensionContract(arg0 context.Context, arg1 *anchorcontract_go.BatchGuildExtensionContractReq) (*anchorcontract_go.BatchGuildExtensionContractResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGuildExtensionContract", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.BatchGuildExtensionContractResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGuildExtensionContract indicates an expected call of BatchGuildExtensionContract.
func (mr *MockIClientMockRecorder) BatchGuildExtensionContract(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGuildExtensionContract", reflect.TypeOf((*MockIClient)(nil).BatchGuildExtensionContract), arg0, arg1)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// CancelContractByUid mocks base method.
func (m *MockIClient) CancelContractByUid(arg0 context.Context, arg1 *anchorcontract_go.CancelContractByUidReq) (*anchorcontract_go.CancelContractByUidResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelContractByUid", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.CancelContractByUidResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CancelContractByUid indicates an expected call of CancelContractByUid.
func (mr *MockIClientMockRecorder) CancelContractByUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelContractByUid", reflect.TypeOf((*MockIClient)(nil).CancelContractByUid), arg0, arg1)
}

// CheckCanApplySign mocks base method.
func (m *MockIClient) CheckCanApplySign(arg0 context.Context, arg1 *anchorcontract_go.CheckCanApplySignReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckCanApplySign", arg0, arg1)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// CheckCanApplySign indicates an expected call of CheckCanApplySign.
func (mr *MockIClientMockRecorder) CheckCanApplySign(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCanApplySign", reflect.TypeOf((*MockIClient)(nil).CheckCanApplySign), arg0, arg1)
}

// CheckIfGreatLiveAnchor mocks base method.
func (m *MockIClient) CheckIfGreatLiveAnchor(arg0 context.Context, arg1 *anchorcontract_go.CheckIfGreatLiveAnchorReq) (*anchorcontract_go.CheckIfGreatLiveAnchorResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfGreatLiveAnchor", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.CheckIfGreatLiveAnchorResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckIfGreatLiveAnchor indicates an expected call of CheckIfGreatLiveAnchor.
func (mr *MockIClientMockRecorder) CheckIfGreatLiveAnchor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfGreatLiveAnchor", reflect.TypeOf((*MockIClient)(nil).CheckIfGreatLiveAnchor), arg0, arg1)
}

// CheckIsSignWhiteUid mocks base method.
func (m *MockIClient) CheckIsSignWhiteUid(arg0 context.Context, arg1 *anchorcontract_go.CheckIsSignWhiteUidReq) (*anchorcontract_go.CheckIsSignWhiteUidResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIsSignWhiteUid", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.CheckIsSignWhiteUidResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRecommendTopGuildList indicates an expected call of GetRecommendTopGuildList.
func (mr *MockIClientMockRecorder) GetRecommendTopGuildList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecommendTopGuildList", reflect.TypeOf((*MockIClient)(nil).GetRecommendTopGuildList), arg0, arg1)
}

// GetRecommendTopGuildList mocks base method.
func (m *MockIClient) GetRecommendTopGuildList(arg0 context.Context, arg1 *anchorcontract_go.GetRecommendTopGuildListReq) (*anchorcontract_go.GetRecommendTopGuildListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecommendTopGuildList", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.GetRecommendTopGuildListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckIsSignWhiteUid indicates an expected call of CheckIsSignWhiteUid.
func (mr *MockIClientMockRecorder) CheckIsSignWhiteUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIsSignWhiteUid", reflect.TypeOf((*MockIClient)(nil).CheckIsSignWhiteUid), arg0, arg1)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DelSignWhiteUid mocks base method.
func (m *MockIClient) DelSignWhiteUid(arg0 context.Context, arg1 *anchorcontract_go.DelSignWhiteUidReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelSignWhiteUid", arg0, arg1)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// DelSignWhiteUid indicates an expected call of DelSignWhiteUid.
func (mr *MockIClientMockRecorder) DelSignWhiteUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelSignWhiteUid", reflect.TypeOf((*MockIClient)(nil).DelSignWhiteUid), arg0, arg1)
}

// GetAllApplyBlacklist mocks base method.
func (m *MockIClient) GetAllApplyBlacklist(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.GetAllApplyBlacklistReq) (*anchorcontract_go.GetAllApplyBlacklistResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllApplyBlacklist", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.GetAllApplyBlacklistResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllApplyBlacklist indicates an expected call of GetAllApplyBlacklist.
func (mr *MockIClientMockRecorder) GetAllApplyBlacklist(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllApplyBlacklist", reflect.TypeOf((*MockIClient)(nil).GetAllApplyBlacklist), arg0, arg1, arg2)
}

// GetAllApplySignRecord mocks base method.
func (m *MockIClient) GetAllApplySignRecord(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.GetAllApplySignRecordReq) (*anchorcontract_go.GetAllApplySignRecordResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllApplySignRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.GetAllApplySignRecordResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllApplySignRecord indicates an expected call of GetAllApplySignRecord.
func (mr *MockIClientMockRecorder) GetAllApplySignRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllApplySignRecord", reflect.TypeOf((*MockIClient)(nil).GetAllApplySignRecord), arg0, arg1, arg2)
}

// GetAllLiveAnchorExamine mocks base method.
func (m *MockIClient) GetAllLiveAnchorExamine(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.GetAllLiveAnchorExamineReq) (*anchorcontract_go.GetAllLiveAnchorExamineResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllLiveAnchorExamine", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.GetAllLiveAnchorExamineResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllLiveAnchorExamine indicates an expected call of GetAllLiveAnchorExamine.
func (mr *MockIClientMockRecorder) GetAllLiveAnchorExamine(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllLiveAnchorExamine", reflect.TypeOf((*MockIClient)(nil).GetAllLiveAnchorExamine), arg0, arg1, arg2)
}

// GetAnchorCertListByItemId mocks base method.
func (m *MockIClient) GetAnchorCertListByItemId(arg0 context.Context, arg1 *anchorcontract_go.GetAnchorCertListByItemIdReq) (*anchorcontract_go.GetAnchorCertListByItemIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorCertListByItemId", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.GetAnchorCertListByItemIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAnchorCertListByItemId indicates an expected call of GetAnchorCertListByItemId.
func (mr *MockIClientMockRecorder) GetAnchorCertListByItemId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorCertListByItemId", reflect.TypeOf((*MockIClient)(nil).GetAnchorCertListByItemId), arg0, arg1)
}

// GetAnchorCertTaskInfo mocks base method.
func (m *MockIClient) GetAnchorCertTaskInfo(arg0 context.Context, arg1 uint32) (*anchorcontract_go.GetAnchorCertTaskInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorCertTaskInfo", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.GetAnchorCertTaskInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAnchorCertTaskInfo indicates an expected call of GetAnchorCertTaskInfo.
func (mr *MockIClientMockRecorder) GetAnchorCertTaskInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorCertTaskInfo", reflect.TypeOf((*MockIClient)(nil).GetAnchorCertTaskInfo), arg0, arg1)
}

// GetContract mocks base method.
func (m *MockIClient) GetContract(arg0 context.Context, arg1 *anchorcontract_go.GetContractReq) (*anchorcontract_go.GetContractResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContract", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.GetContractResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetContract indicates an expected call of GetContract.
func (mr *MockIClientMockRecorder) GetContract(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContract", reflect.TypeOf((*MockIClient)(nil).GetContract), arg0, arg1)
}

// GetGuildAnchorExtInfoList mocks base method.
func (m *MockIClient) GetGuildAnchorExtInfoList(arg0 context.Context, arg1 *anchorcontract_go.GetGuildAnchorExtInfoListReq) (*anchorcontract_go.GetGuildAnchorExtInfoListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildAnchorExtInfoList", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildAnchorExtInfoListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildAnchorExtInfoList indicates an expected call of GetGuildAnchorExtInfoList.
func (mr *MockIClientMockRecorder) GetGuildAnchorExtInfoList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildAnchorExtInfoList", reflect.TypeOf((*MockIClient)(nil).GetGuildAnchorExtInfoList), arg0, arg1)
}

// GetGuildAnchorIdentity mocks base method.
func (m *MockIClient) GetGuildAnchorIdentity(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.GetGuildAnchorIdentityReq) (*anchorcontract_go.GetGuildAnchorIdentityResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildAnchorIdentity", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildAnchorIdentityResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildAnchorIdentity indicates an expected call of GetGuildAnchorIdentity.
func (mr *MockIClientMockRecorder) GetGuildAnchorIdentity(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildAnchorIdentity", reflect.TypeOf((*MockIClient)(nil).GetGuildAnchorIdentity), arg0, arg1, arg2)
}

// GetGuildAnchorIdentityLog mocks base method.
func (m *MockIClient) GetGuildAnchorIdentityLog(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.GetGuildAnchorIdentityLogReq) (*anchorcontract_go.GetGuildAnchorIdentityLogResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildAnchorIdentityLog", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildAnchorIdentityLogResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildAnchorIdentityLog indicates an expected call of GetGuildAnchorIdentityLog.
func (mr *MockIClientMockRecorder) GetGuildAnchorIdentityLog(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildAnchorIdentityLog", reflect.TypeOf((*MockIClient)(nil).GetGuildAnchorIdentityLog), arg0, arg1, arg2)
}

// GetGuildApplySignRecord mocks base method.
func (m *MockIClient) GetGuildApplySignRecord(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.GetGuildApplySignRecordReq) (*anchorcontract_go.GetGuildApplySignRecordResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildApplySignRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildApplySignRecordResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildApplySignRecord indicates an expected call of GetGuildApplySignRecord.
func (mr *MockIClientMockRecorder) GetGuildApplySignRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildApplySignRecord", reflect.TypeOf((*MockIClient)(nil).GetGuildApplySignRecord), arg0, arg1, arg2)
}

// GetGuildApplySignRecordCnt mocks base method.
func (m *MockIClient) GetGuildApplySignRecordCnt(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.GetGuildApplySignRecordCntReq) (*anchorcontract_go.GetGuildApplySignRecordCntResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildApplySignRecordCnt", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildApplySignRecordCntResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildApplySignRecordCnt indicates an expected call of GetGuildApplySignRecordCnt.
func (mr *MockIClientMockRecorder) GetGuildApplySignRecordCnt(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildApplySignRecordCnt", reflect.TypeOf((*MockIClient)(nil).GetGuildApplySignRecordCnt), arg0, arg1, arg2)
}

// GetGuildApplySignRecordList mocks base method.
func (m *MockIClient) GetGuildApplySignRecordList(arg0 context.Context, arg1 *anchorcontract_go.GetGuildApplySignRecordListReq) (*anchorcontract_go.GetGuildApplySignRecordListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildApplySignRecordList", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildApplySignRecordListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildApplySignRecordList indicates an expected call of GetGuildApplySignRecordList.
func (mr *MockIClientMockRecorder) GetGuildApplySignRecordList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildApplySignRecordList", reflect.TypeOf((*MockIClient)(nil).GetGuildApplySignRecordList), arg0, arg1)
}

// GetGuildCancelSignRecordList mocks base method.
func (m *MockIClient) GetGuildCancelSignRecordList(arg0 context.Context, arg1 *anchorcontract_go.GetGuildCancelSignRecordListReq) (*anchorcontract_go.GetGuildCancelSignRecordListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildCancelSignRecordList", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildCancelSignRecordListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildCancelSignRecordList indicates an expected call of GetGuildCancelSignRecordList.
func (mr *MockIClientMockRecorder) GetGuildCancelSignRecordList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildCancelSignRecordList", reflect.TypeOf((*MockIClient)(nil).GetGuildCancelSignRecordList), arg0, arg1)
}

// GetGuildContract mocks base method.
func (m *MockIClient) GetGuildContract(arg0 context.Context, arg1, arg2, arg3 uint32) (*anchorcontract_go.GetGuildContractResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildContract", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildContractResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildContract indicates an expected call of GetGuildContract.
func (mr *MockIClientMockRecorder) GetGuildContract(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildContract", reflect.TypeOf((*MockIClient)(nil).GetGuildContract), arg0, arg1, arg2, arg3)
}

// GetGuildContractByIdentity mocks base method.
func (m *MockIClient) GetGuildContractByIdentity(arg0 context.Context, arg1 *anchorcontract_go.GetGuildContractByIdentityReq) (*anchorcontract_go.GetGuildContractByIdentityResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildContractByIdentity", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildContractByIdentityResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildContractByIdentity indicates an expected call of GetGuildContractByIdentity.
func (mr *MockIClientMockRecorder) GetGuildContractByIdentity(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildContractByIdentity", reflect.TypeOf((*MockIClient)(nil).GetGuildContractByIdentity), arg0, arg1)
}

// GetGuildContractSum mocks base method.
func (m *MockIClient) GetGuildContractSum(arg0 context.Context, arg1 uint32) (*anchorcontract_go.GetGuildContractSumResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildContractSum", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildContractSumResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildContractSum indicates an expected call of GetGuildContractSum.
func (mr *MockIClientMockRecorder) GetGuildContractSum(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildContractSum", reflect.TypeOf((*MockIClient)(nil).GetGuildContractSum), arg0, arg1)
}

// GetGuildLiveAnchorExamine mocks base method.
func (m *MockIClient) GetGuildLiveAnchorExamine(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.GetGuildLiveAnchorExamineReq) (*anchorcontract_go.GetGuildLiveAnchorExamineResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildLiveAnchorExamine", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.GetGuildLiveAnchorExamineResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildLiveAnchorExamine indicates an expected call of GetGuildLiveAnchorExamine.
func (mr *MockIClientMockRecorder) GetGuildLiveAnchorExamine(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildLiveAnchorExamine", reflect.TypeOf((*MockIClient)(nil).GetGuildLiveAnchorExamine), arg0, arg1, arg2)
}

// GetIdentityChangeHistory mocks base method.
func (m *MockIClient) GetIdentityChangeHistory(arg0 context.Context, arg1 *anchorcontract_go.GetIdentityChangeHistoryReq) (*anchorcontract_go.GetIdentityChangeHistoryResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIdentityChangeHistory", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.GetIdentityChangeHistoryResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetIdentityChangeHistory indicates an expected call of GetIdentityChangeHistory.
func (mr *MockIClientMockRecorder) GetIdentityChangeHistory(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIdentityChangeHistory", reflect.TypeOf((*MockIClient)(nil).GetIdentityChangeHistory), arg0, arg1)
}

// GetMultiPlayerCenterEntry mocks base method.
func (m *MockIClient) GetMultiPlayerCenterEntry(arg0 context.Context, arg1 uint32) (string, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiPlayerCenterEntry", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetMultiPlayerCenterEntry indicates an expected call of GetMultiPlayerCenterEntry.
func (mr *MockIClientMockRecorder) GetMultiPlayerCenterEntry(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiPlayerCenterEntry", reflect.TypeOf((*MockIClient)(nil).GetMultiPlayerCenterEntry), arg0, arg1)
}

// GetRadioLiveAnchorExamine mocks base method.
func (m *MockIClient) GetRadioLiveAnchorExamine(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRadioLiveAnchorExamine", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRadioLiveAnchorExamine indicates an expected call of GetRadioLiveAnchorExamine.
func (mr *MockIClientMockRecorder) GetRadioLiveAnchorExamine(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRadioLiveAnchorExamine", reflect.TypeOf((*MockIClient)(nil).GetRadioLiveAnchorExamine), arg0, arg1)
}

// GetUserAnchorIdentityLog mocks base method.
func (m *MockIClient) GetUserAnchorIdentityLog(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.GetUserAnchorIdentityLogReq) (*anchorcontract_go.GetUserAnchorIdentityLogResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAnchorIdentityLog", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.GetUserAnchorIdentityLogResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserAnchorIdentityLog indicates an expected call of GetUserAnchorIdentityLog.
func (mr *MockIClientMockRecorder) GetUserAnchorIdentityLog(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAnchorIdentityLog", reflect.TypeOf((*MockIClient)(nil).GetUserAnchorIdentityLog), arg0, arg1, arg2)
}

// GetUserApplySignRecord mocks base method.
func (m *MockIClient) GetUserApplySignRecord(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.GetUserApplySignRecordReq) (*anchorcontract_go.GetUserApplySignRecordResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserApplySignRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.GetUserApplySignRecordResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserApplySignRecord indicates an expected call of GetUserApplySignRecord.
func (mr *MockIClientMockRecorder) GetUserApplySignRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserApplySignRecord", reflect.TypeOf((*MockIClient)(nil).GetUserApplySignRecord), arg0, arg1, arg2)
}

// GetUserContract mocks base method.
func (m *MockIClient) GetUserContract(arg0 context.Context, arg1, arg2 uint32) (*anchorcontract_go.GetUserContractResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserContract", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.GetUserContractResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserContract indicates an expected call of GetUserContract.
func (mr *MockIClientMockRecorder) GetUserContract(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserContract", reflect.TypeOf((*MockIClient)(nil).GetUserContract), arg0, arg1, arg2)
}

// GetUserContractCacheInfo mocks base method.
func (m *MockIClient) GetUserContractCacheInfo(arg0 context.Context, arg1, arg2 uint32) (*anchorcontract_go.ContractCacheInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserContractCacheInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.ContractCacheInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserContractCacheInfo indicates an expected call of GetUserContractCacheInfo.
func (mr *MockIClientMockRecorder) GetUserContractCacheInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserContractCacheInfo", reflect.TypeOf((*MockIClient)(nil).GetUserContractCacheInfo), arg0, arg1, arg2)
}

// GetUserExamineCert mocks base method.
func (m *MockIClient) GetUserExamineCert(arg0 context.Context, arg1 uint32) (*anchorcontract_go.GetUserExamineCertResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserExamineCert", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.GetUserExamineCertResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserExamineCert indicates an expected call of GetUserExamineCert.
func (mr *MockIClientMockRecorder) GetUserExamineCert(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExamineCert", reflect.TypeOf((*MockIClient)(nil).GetUserExamineCert), arg0, arg1)
}

// GuildExtensionContract mocks base method.
func (m *MockIClient) GuildExtensionContract(arg0 context.Context, arg1 *anchorcontract_go.GuildExtensionContractReq) (*anchorcontract_go.GuildExtensionContractResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GuildExtensionContract", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.GuildExtensionContractResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GuildExtensionContract indicates an expected call of GuildExtensionContract.
func (mr *MockIClientMockRecorder) GuildExtensionContract(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GuildExtensionContract", reflect.TypeOf((*MockIClient)(nil).GuildExtensionContract), arg0, arg1)
}

// HandleApplyBlackInfo mocks base method.
func (m *MockIClient) HandleApplyBlackInfo(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.HandleApplyBlackInfoReq) (*anchorcontract_go.HandleApplyBlackInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleApplyBlackInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.HandleApplyBlackInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// HandleApplyBlackInfo indicates an expected call of HandleApplyBlackInfo.
func (mr *MockIClientMockRecorder) HandleApplyBlackInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleApplyBlackInfo", reflect.TypeOf((*MockIClient)(nil).HandleApplyBlackInfo), arg0, arg1, arg2)
}

// HandleFocusAnchor mocks base method.
func (m *MockIClient) HandleFocusAnchor(arg0 context.Context, arg1 *anchorcontract_go.HandleFocusAnchorReq) (*anchorcontract_go.HandleFocusAnchorResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleFocusAnchor", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.HandleFocusAnchorResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// HandleFocusAnchor indicates an expected call of HandleFocusAnchor.
func (mr *MockIClientMockRecorder) HandleFocusAnchor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleFocusAnchor", reflect.TypeOf((*MockIClient)(nil).HandleFocusAnchor), arg0, arg1)
}

// HandlerCancelContractApply mocks base method.
func (m *MockIClient) HandlerCancelContractApply(arg0 context.Context, arg1 *anchorcontract_go.HandlerCancelContractApplyReq) (*anchorcontract_go.HandlerCancelContractApplyResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandlerCancelContractApply", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.HandlerCancelContractApplyResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// HandlerCancelContractApply indicates an expected call of HandlerCancelContractApply.
func (mr *MockIClientMockRecorder) HandlerCancelContractApply(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlerCancelContractApply", reflect.TypeOf((*MockIClient)(nil).HandlerCancelContractApply), arg0, arg1)
}

// OfficialHandleApplySign mocks base method.
func (m *MockIClient) OfficialHandleApplySign(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.OfficialHandleApplySignReq) (*anchorcontract_go.OfficialHandleApplySignResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfficialHandleApplySign", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.OfficialHandleApplySignResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// OfficialHandleApplySign indicates an expected call of OfficialHandleApplySign.
func (mr *MockIClientMockRecorder) OfficialHandleApplySign(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficialHandleApplySign", reflect.TypeOf((*MockIClient)(nil).OfficialHandleApplySign), arg0, arg1, arg2)
}

// OfficialHandleApplySignEsport mocks base method.
func (m *MockIClient) OfficialHandleApplySignEsport(arg0 context.Context, arg1 *anchorcontract_go.OfficialHandleApplySignEsportReq) (*anchorcontract_go.OfficialHandleApplySignEsportResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfficialHandleApplySignEsport", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.OfficialHandleApplySignEsportResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// OfficialHandleApplySignEsport indicates an expected call of OfficialHandleApplySignEsport.
func (mr *MockIClientMockRecorder) OfficialHandleApplySignEsport(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfficialHandleApplySignEsport", reflect.TypeOf((*MockIClient)(nil).OfficialHandleApplySignEsport), arg0, arg1)
}

// PresidentHandleApplySign mocks base method.
func (m *MockIClient) PresidentHandleApplySign(arg0 context.Context, arg1 *anchorcontract_go.PresidentHandleApplySignReq) (*anchorcontract_go.PresidentHandleApplySignResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PresidentHandleApplySign", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.PresidentHandleApplySignResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// PresidentHandleApplySign indicates an expected call of PresidentHandleApplySign.
func (mr *MockIClientMockRecorder) PresidentHandleApplySign(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PresidentHandleApplySign", reflect.TypeOf((*MockIClient)(nil).PresidentHandleApplySign), arg0, arg1)
}

// ReclaimAnchorIdentity mocks base method.
func (m *MockIClient) ReclaimAnchorIdentity(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.ReclaimAnchorIdentityReq) (*anchorcontract_go.ReclaimAnchorIdentityResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReclaimAnchorIdentity", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.ReclaimAnchorIdentityResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ReclaimAnchorIdentity indicates an expected call of ReclaimAnchorIdentity.
func (mr *MockIClientMockRecorder) ReclaimAnchorIdentity(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReclaimAnchorIdentity", reflect.TypeOf((*MockIClient)(nil).ReclaimAnchorIdentity), arg0, arg1, arg2)
}

// ReclaimGuildAllAnchorIdentity mocks base method.
func (m *MockIClient) ReclaimGuildAllAnchorIdentity(arg0 context.Context, arg1 *anchorcontract_go.ReclaimGuildAllAnchorIdentityReq) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReclaimGuildAllAnchorIdentity", arg0, arg1)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// ReclaimGuildAllAnchorIdentity indicates an expected call of ReclaimGuildAllAnchorIdentity.
func (mr *MockIClientMockRecorder) ReclaimGuildAllAnchorIdentity(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReclaimGuildAllAnchorIdentity", reflect.TypeOf((*MockIClient)(nil).ReclaimGuildAllAnchorIdentity), arg0, arg1)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UpdateLiveAnchorExamineStatus mocks base method.
func (m *MockIClient) UpdateLiveAnchorExamineStatus(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.UpdateLiveAnchorExamineStatusReq) (*anchorcontract_go.UpdateLiveAnchorExamineStatusResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLiveAnchorExamineStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.UpdateLiveAnchorExamineStatusResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateLiveAnchorExamineStatus indicates an expected call of UpdateLiveAnchorExamineStatus.
func (mr *MockIClientMockRecorder) UpdateLiveAnchorExamineStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLiveAnchorExamineStatus", reflect.TypeOf((*MockIClient)(nil).UpdateLiveAnchorExamineStatus), arg0, arg1, arg2)
}

// UpdateLiveAnchorExamineTime mocks base method.
func (m *MockIClient) UpdateLiveAnchorExamineTime(arg0 context.Context, arg1 uint32, arg2 *anchorcontract_go.UpdateLiveAnchorExamineTimeReq) (*anchorcontract_go.UpdateLiveAnchorExamineTimeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLiveAnchorExamineTime", arg0, arg1, arg2)
	ret0, _ := ret[0].(*anchorcontract_go.UpdateLiveAnchorExamineTimeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateLiveAnchorExamineTime indicates an expected call of UpdateLiveAnchorExamineTime.
func (mr *MockIClientMockRecorder) UpdateLiveAnchorExamineTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLiveAnchorExamineTime", reflect.TypeOf((*MockIClient)(nil).UpdateLiveAnchorExamineTime), arg0, arg1, arg2)
}

// UpdateRadioLiveAnchorExamine mocks base method.
func (m *MockIClient) UpdateRadioLiveAnchorExamine(arg0 context.Context, arg1 uint32) (*anchorcontract_go.UpdateRadioLiveAnchorExamineResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRadioLiveAnchorExamine", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.UpdateRadioLiveAnchorExamineResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRadioLiveAnchorExamine indicates an expected call of UpdateRadioLiveAnchorExamine.
func (mr *MockIClientMockRecorder) UpdateRadioLiveAnchorExamine(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRadioLiveAnchorExamine", reflect.TypeOf((*MockIClient)(nil).UpdateRadioLiveAnchorExamine), arg0, arg1)
}

// UpdateRemark mocks base method.
func (m *MockIClient) UpdateRemark(arg0 context.Context, arg1 *anchorcontract_go.UpdateRemarkReq) (*anchorcontract_go.UpdateRemarkResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRemark", arg0, arg1)
	ret0, _ := ret[0].(*anchorcontract_go.UpdateRemarkResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateRemark indicates an expected call of UpdateRemark.
func (mr *MockIClientMockRecorder) UpdateRemark(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRemark", reflect.TypeOf((*MockIClient)(nil).UpdateRemark), arg0, arg1)
}

// UpdateSignedAnchorAgentId mocks base method.
func (m *MockIClient) UpdateSignedAnchorAgentId(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 []uint32) (*anchorcontract_go.UpdateSignedAnchorAgentIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSignedAnchorAgentId", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*anchorcontract_go.UpdateSignedAnchorAgentIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateSignedAnchorAgentId indicates an expected call of UpdateSignedAnchorAgentId.
func (mr *MockIClientMockRecorder) UpdateSignedAnchorAgentId(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSignedAnchorAgentId", reflect.TypeOf((*MockIClient)(nil).UpdateSignedAnchorAgentId), arg0, arg1, arg2, arg3, arg4)
}
