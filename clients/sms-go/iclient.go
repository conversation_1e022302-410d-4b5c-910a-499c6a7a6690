package sms_go

import (
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/sms-go"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	SendSms(ctx context.Context, phone, verifyCodeKey, verifyCodeUsage string, smsType, marketId uint32, paramList []string, withoutCooldown bool) protocol.ServerError
	SendVoiceVerifyCode(ctx context.Context, phone, verifyCode string, uid uint32) protocol.ServerError
	SendSmsV2(ctx context.Context, req *pb.SendSmsReq) protocol.ServerError
	SendVoiceVerifyCodeV2(ctx context.Context, req *pb.SendVoiceVerifyCodeReq) protocol.ServerError
	SendMarketingSms(ctx context.Context, req *pb.SendMarketingSmsReq) protocol.ServerError
	RecordVerifyCodePass(ctx context.Context, req *pb.RecordVerifyCodePassReq) protocol.ServerError
	SendCommonSms(ctx context.Context, req *pb.SendCommonSmsReq) protocol.ServerError
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}

const (
	SMS_TYPE_TT_ACCOUNT                      = 1  //2019.7.29: 可能已不再使用
	SMS_TYPE_TT_REG                          = 2  // 您正在注册帐号，验证码 %1%
	SMS_TYPE_TT_RESET_PWD                    = 3  // 您正在重置密码，验证码：%1%
	SMS_TYPE_CLAM_DEFAULT                    = 4  //
	SMS_TYPE_SERVER_FAIL                     = 5  //运维报警
	SMS_TYPE_DISK_OVERUSED                   = 6  //运维报警
	SMS_TYPE_PHONE_DISBIND                   = 7  // 解绑手机
	SMS_TYPE_TT_BIND_PHONE                   = 8  //绑定手机
	SMS_TYPE_VERIFY_CODE_WITH_REASON         = 9  // 带原因字串的验证码
	SMS_TYPE_PASSWORD_BEEN_RESET             = 10 // 密码被重置
	SMS_TYPE_THRESHOLD_EXCEED_NTFY           = 11 //   ######，将超过####阀值，请留意。
	SMS_TYPE_VERIFY_CODE_FOR_R_ASSISTANT     = 12 // 【R助手】验证码%1%（%2%）
	SMS_TYPE_GAME_AWARD_DIGITMONTRI_20070115 = 13 //恭喜你在《数码宝贝tri》游戏活动中获得%1%奖品，请于1月15日前联系游戏运营团队QQ%2%进行领取
	SMS_TYPE_LOGIN_DEVICE_CHANGE_ALARM       = 14 //您的帐号 [#id#]在#time#时通过#kind#手机登陆，若不是你本人操作请立即修改密码。
	SMS_TYPE_SUBCODE_4_BOY_GALLANTRY_ODE     = 15 //《少年歌行》预约验证码
	SMS_TYPE_SUBCODE_4_LingNengShijie        = 16 //【前进吧悟空】恭喜获得灵能世界的预约资格！！您的公测验证码为：$code$。关注灵能小福星微信公众号，可获取更多福利！
	SMS_TYPE_NTFY_UNREG_AUDIT_PASS           = 17 // 账号注销审核成功
	SMS_TYPE_NTFY_UNREG_AUDIT_DENY           = 18 // 账号注销审核失败
	SMS_TYPE_NTFY_RECOVERY_PASSWD            = 19 // 通过密保问题重置密码
	SMS_TYPE_CODE_SHAONIANGEXING_TEST        = 20 // 《少年歌行》情缘测试H5活动: 【少年歌行】恭喜您获得江湖伙伴！！您的领取验证码为：$code$。关注《少年歌行官方手游》微信公众号，可获取更多江湖情报！
	SMS_TYPE_VOICE_VERIFY_CODE               = 21 //语音验证码站位
	SMS_TYPE_ChanganShiErShiCen_SubCode      = 22 //长安十二时辰, 预约验证码
	SMS_TYPE_VC_LOGIN_WITH_ABNORMAL_DEV      = 23 //deprecated, 验证码: 异常设备登录
	SMS_TYPE_VC_UNREGISTER                   = 24 //验证码: 注销账号
	SMS_TYPE_VC_REBIND_PHONE                 = 25 //验证码: 手机换绑
	SMS_TYPE_VC_LOGIN                        = 26 //deprecated, 验证码: 登录
	SMS_TYPE_VC_WITH_DOWITH                  = 27 //您的账号正在 %1%，验证码：%2%
	SMS_TYPE_VC_LOGIN_SDK_DBBACKEND          = 28 //您正在请求数据后台登录，验证码：%1%
	SMS_TYPE_VC_REALNAME_CERT                = 29 //您正在进行实名认证，验证码 %1%
	SMS_TYPE_VC_CAMPUS_RECRUIT               = 30 // 2019 校园招聘 主力 验证码
	SMS_TYPE_VC_TEST_4_QianXingZhuiZong      = 31 // 【潜行追踪】暗号不要外泄！您的验证码为：$code$。
	SMS_TYPE_VC_USER_SCORE_WITHDRAW          = 32 // 个人积分体现
	SMS_TYPE_VC_REBIND_PHONE2                = 33 //验证码: 账号%1%正在换绑手机号，验证码：%2%
	SMS_TYPE_VC_BIND_PHONE                   = 34 //验证码: 账号%1%正在绑定手机，验证码：%2%
	SMS_TYPE_VC_RESET_PWD                    = 35 //验证码: 账号%1%正在重置密码，验证码：%2%
	SMS_TYPE_VC_UNREGISTER2                  = 36 //验证码: 账号%1%正在进行账号注销，验证码：%2%
	SMS_TYPE_VC_USER_SCORE_WITHDRAW2         = 37 // 账号%1%正在进行个人积分提现，验证码：%2%
	SMS_TYPE_VC_ACCOUNT_DOING                = 38 // 您的账号%1%正在进行%2%，验证码：%3%
	SMS_TYPE_DC_ALARM                        = 39 //【趣丸网】数据中心告警:%1%
	SMS_TYPE_NTFY_APPEAL_ACCEPTED            = 40 // 申诉流水号%1%，账号%2%提交申诉成功，审核结果请留意您的短信
	SMS_TYPE_NTFY_APPEAL_DENY                = 41 // 申诉流水号%1%，账号%2%申诉失败，原因为：%3%
	SMS_TYPE_NTFY_APPEAL_DETACH3RDPARTY      = 42 // 申诉成功-解除第三方账号绑定
	SMS_TYPE_NTFY_APPEAL_UNBINDPHONE         = 43 // 申诉成功-解除手机号绑定
	SMS_TYPE_VC_LOGIN_WITH_ABNORMAL_DEV2     = 44 //您的账号%1%正在异常设备登录，验证码：%2%
	SMS_TYPE_VC_LOGIN2                       = 45 //您的账号%1%正在登录，验证码：%2%
	SMS_TYPE_VC_ANCHOR_SCORE_WITHDRAW        = 46 // 语音主播奖励积分提现
	SMS_TYPE_ChildrenRefund_47               = 47
	SMS_TYPE_ChildrenRefund_48               = 48
	SMS_TYPE_ChildrenRefund_49               = 49
	SMS_TYPE_ChildrenRefund_50               = 50
	SMS_TYPE_ChildrenRefund_51               = 51
	SMS_TYPE_ChildrenRefund_52               = 52
	SMS_TYPE_ChildrenRefund_53               = 53
	SMS_TYPE_ChildrenRefund_54               = 54
	SMS_TYPE_ChildrenRefund_55               = 55
	SMS_TYPE_ChildrenRefund_56               = 56
	SMS_TYPE_ChildrenRefund_57               = 57
	SMS_TYPE_ChildrenRefund_58               = 58
	SMS_TYPE_ChildrenRefund_59               = 59
	SMS_TYPE_MARKETING                       = 60 // 营销短信
	SMS_TYPE_VC_MASKED_PK_SCORE_WITHDRAW     = 61 // 蒙面pk积分提现
	SMS_TYPE_WEFLY_MON_ALARM                 = 62 // 起飞平台警告短信
	SMS_TYPE_LOGIN_FAIL_DEVICE_CHANGE_ALARM  = 66 // //您的帐号 [#id#]在#time#时通过#kind#手机登陆失败，若不是你本人操作请立即修改密码。
	SMS_TYPE_ChildrenRefund_67               = 67
	SMS_TYPE_ChildrenRefund_68               = 68
	SMS_TYPE_ChildrenRefund_69               = 69
	SMS_TYPE_ChildrenRefund_70               = 70
	SMS_TYPE_ChildrenRefund_71               = 71
	SMS_TYPE_ChildrenRefund_72               = 72
	SMS_TYPE_ChildrenRefund_73               = 73
	SMS_TYPE_ChildrenRefund_74               = 74
	SMS_TYPE_ChildrenRefund_75               = 75
	SMS_TYPE_ChildrenRefund_76               = 76
	SMS_TYPE_ChildrenRefund_77               = 77
	SMS_TYPE_ChildrenRefund_78               = 78
	SMS_TYPE_ChildrenRefund_79               = 79
	SMS_TYPE_FRAUDDEVICEREBINDPHONEFAIL_80   = 80
	SMS_TYPE_PIDACCOUNTBINDTTACCOUNT_81      = 81
)
