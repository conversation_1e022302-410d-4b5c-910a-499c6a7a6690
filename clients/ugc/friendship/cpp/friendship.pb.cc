// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: friendship.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "friendship.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace ugc {
namespace friendship {
class FollowUserReqDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<FollowUserReq> {
} _FollowUserReq_default_instance_;
class FollowUserRespDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<FollowUserResp> {
} _FollowUserResp_default_instance_;
class UnfollowUserReqDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<UnfollowUserReq> {
} _UnfollowUserReq_default_instance_;
class UnfollowUserRespDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<UnfollowUserResp> {
} _UnfollowUserResp_default_instance_;
class FollowingDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<Following> {
} _Following_default_instance_;
class CountsDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<Counts> {
} _Counts_default_instance_;
class LoadMore_CreateAtAndUidKeyDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<LoadMore_CreateAtAndUidKey> {
} _LoadMore_CreateAtAndUidKey_default_instance_;
class LoadMore_SequenceIdKeyDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<LoadMore_SequenceIdKey> {
} _LoadMore_SequenceIdKey_default_instance_;
class LoadMore_IDKeyDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<LoadMore_IDKey> {
} _LoadMore_IDKey_default_instance_;
class LoadMoreDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<LoadMore> {
  public:
  const ::ugc::friendship::LoadMore_CreateAtAndUidKey* create_at_and_uid_key_;
  const ::ugc::friendship::LoadMore_IDKey* id_key_;
  const ::ugc::friendship::LoadMore_SequenceIdKey* sequence_id_key_;
} _LoadMore_default_instance_;
class GetFollowingListReqDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<GetFollowingListReq> {
  public:
  ::google::protobuf::uint32 followed_by_uid_;
  ::google::protobuf::uint32 following_uid_;
} _GetFollowingListReq_default_instance_;
class GetFollowingListRespDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<GetFollowingListResp> {
} _GetFollowingListResp_default_instance_;
class GetBiFollowingReqDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<GetBiFollowingReq> {
} _GetBiFollowingReq_default_instance_;
class GetBiFollowingRespDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<GetBiFollowingResp> {
} _GetBiFollowingResp_default_instance_;
class BatchGetBiFollowingReqDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<BatchGetBiFollowingReq> {
} _BatchGetBiFollowingReq_default_instance_;
class BatchGetBiFollowingRespDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<BatchGetBiFollowingResp> {
} _BatchGetBiFollowingResp_default_instance_;
class GetUserCountsReqDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<GetUserCountsReq> {
} _GetUserCountsReq_default_instance_;
class GetUserCountsRespDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<GetUserCountsResp> {
} _GetUserCountsResp_default_instance_;
class BatchGetUserCountsReqDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<BatchGetUserCountsReq> {
} _BatchGetUserCountsReq_default_instance_;
class BatchGetUserCountsRespDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<BatchGetUserCountsResp> {
} _BatchGetUserCountsResp_default_instance_;
class SyncFromFriendListReq_FriendDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<SyncFromFriendListReq_Friend> {
} _SyncFromFriendListReq_Friend_default_instance_;
class SyncFromFriendListReqDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<SyncFromFriendListReq> {
} _SyncFromFriendListReq_default_instance_;
class SyncFromFriendListRespDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<SyncFromFriendListResp> {
} _SyncFromFriendListResp_default_instance_;
class GetSynchronizedSequenceReqDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<GetSynchronizedSequenceReq> {
} _GetSynchronizedSequenceReq_default_instance_;
class GetSynchronizedSequenceRespDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<GetSynchronizedSequenceResp> {
} _GetSynchronizedSequenceResp_default_instance_;

namespace protobuf_friendship_2eproto {


namespace {

::google::protobuf::Metadata file_level_metadata[28];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[2];

}  // namespace

const ::google::protobuf::uint32 TableStruct::offsets[] = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FollowUserReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FollowUserReq, uid_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FollowUserReq, following_uid_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FollowUserReq, sequence_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FollowUserReq, source_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FollowUserResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FollowUserResp, affected_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FollowUserResp, is_first_time_follow_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnfollowUserReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnfollowUserReq, uid_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnfollowUserReq, following_uid_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnfollowUserReq, sequence_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnfollowUserReq, hard_delete_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnfollowUserResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnfollowUserResp, affected_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Following, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Following, from_uid_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Following, to_uid_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Following, dropped_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Following, create_at_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Following, sequence_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Following, id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Counts, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Counts, following_count_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Counts, follower_count_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(LoadMore_CreateAtAndUidKey, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(LoadMore_CreateAtAndUidKey, last_create_at_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(LoadMore_CreateAtAndUidKey, last_uid_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(LoadMore_SequenceIdKey, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(LoadMore_SequenceIdKey, last_sequence_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(LoadMore_IDKey, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(LoadMore_IDKey, last_object_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(LoadMore, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(LoadMore, _oneof_case_[0]),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(LoadMore, sort_),
  PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET((&_LoadMore_default_instance_), create_at_and_uid_key_),
  PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET((&_LoadMore_default_instance_), id_key_),
  PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET((&_LoadMore_default_instance_), sequence_id_key_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(LoadMore, order_by_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetFollowingListReq, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetFollowingListReq, _oneof_case_[0]),
  PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET((&_GetFollowingListReq_default_instance_), followed_by_uid_),
  PROTO2_GENERATED_DEFAULT_ONEOF_FIELD_OFFSET((&_GetFollowingListReq_default_instance_), following_uid_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetFollowingListReq, including_dropped_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetFollowingListReq, load_more_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetFollowingListReq, count_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetFollowingListReq, request_type_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetFollowingListResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetFollowingListResp, following_list_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetFollowingListResp, load_more_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetBiFollowingReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetBiFollowingReq, uid_a_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetBiFollowingReq, uid_b_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetBiFollowingResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetBiFollowingResp, a_to_b_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetBiFollowingResp, b_to_a_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BatchGetBiFollowingReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BatchGetBiFollowingReq, from_uid_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BatchGetBiFollowingReq, to_uid_list_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BatchGetBiFollowingResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BatchGetBiFollowingResp, following_list_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BatchGetBiFollowingResp, follows_me_list_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetUserCountsReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetUserCountsReq, uid_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetUserCountsResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetUserCountsResp, user_counts_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BatchGetUserCountsReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BatchGetUserCountsReq, uid_list_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BatchGetUserCountsResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BatchGetUserCountsResp, user_counts_map_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SyncFromFriendListReq_Friend, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SyncFromFriendListReq_Friend, uid_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SyncFromFriendListReq_Friend, is_delete_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SyncFromFriendListReq_Friend, create_at_timestamp_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SyncFromFriendListReq_Friend, create_or_delete_seq_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SyncFromFriendListReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SyncFromFriendListReq, uid_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SyncFromFriendListReq, friend_list_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SyncFromFriendListReq, max_ugc_sequence_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SyncFromFriendListReq, add_reverse_sequence_zero_following_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SyncFromFriendListResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetSynchronizedSequenceReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetSynchronizedSequenceReq, uid_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetSynchronizedSequenceResp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(GetSynchronizedSequenceResp, sequence_id_),
};

static const ::google::protobuf::internal::MigrationSchema schemas[] = {
  { 0, -1, sizeof(FollowUserReq)},
  { 8, -1, sizeof(FollowUserResp)},
  { 14, -1, sizeof(UnfollowUserReq)},
  { 22, -1, sizeof(UnfollowUserResp)},
  { 27, -1, sizeof(Following)},
  { 37, -1, sizeof(Counts)},
  { 43, -1, sizeof(LoadMore_CreateAtAndUidKey)},
  { 49, -1, sizeof(LoadMore_SequenceIdKey)},
  { 54, -1, sizeof(LoadMore_IDKey)},
  { 59, -1, sizeof(LoadMore)},
  { 68, -1, sizeof(GetFollowingListReq)},
  { 78, -1, sizeof(GetFollowingListResp)},
  { 84, -1, sizeof(GetBiFollowingReq)},
  { 90, -1, sizeof(GetBiFollowingResp)},
  { 96, -1, sizeof(BatchGetBiFollowingReq)},
  { 102, -1, sizeof(BatchGetBiFollowingResp)},
  { 108, -1, sizeof(GetUserCountsReq)},
  { 113, -1, sizeof(GetUserCountsResp)},
  { 118, -1, sizeof(BatchGetUserCountsReq)},
  { 123, -1, sizeof(BatchGetUserCountsResp)},
  { 128, -1, sizeof(SyncFromFriendListReq_Friend)},
  { 136, -1, sizeof(SyncFromFriendListReq)},
  { 144, -1, sizeof(SyncFromFriendListResp)},
  { 148, -1, sizeof(GetSynchronizedSequenceReq)},
  { 153, -1, sizeof(GetSynchronizedSequenceResp)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&_FollowUserReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_FollowUserResp_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_UnfollowUserReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_UnfollowUserResp_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_Following_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_Counts_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_LoadMore_CreateAtAndUidKey_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_LoadMore_SequenceIdKey_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_LoadMore_IDKey_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_LoadMore_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_GetFollowingListReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_GetFollowingListResp_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_GetBiFollowingReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_GetBiFollowingResp_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_BatchGetBiFollowingReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_BatchGetBiFollowingResp_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_GetUserCountsReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_GetUserCountsResp_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_BatchGetUserCountsReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_BatchGetUserCountsResp_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_SyncFromFriendListReq_Friend_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_SyncFromFriendListReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_SyncFromFriendListResp_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_GetSynchronizedSequenceReq_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_GetSynchronizedSequenceResp_default_instance_),
};

namespace {

void protobuf_AssignDescriptors() {
  AddDescriptors();
  ::google::protobuf::MessageFactory* factory = NULL;
  AssignDescriptors(
      "friendship.proto", schemas, file_default_instances, TableStruct::offsets, factory,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 28);
  const ::google::protobuf::Descriptor* BatchGetBiFollowingResp_FollowingListEntry_descriptor = protobuf_friendship_2eproto::file_level_metadata[15].descriptor;
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        BatchGetBiFollowingResp_FollowingListEntry_descriptor,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::uint32,
            ::ugc::friendship::Following,
            ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                BatchGetBiFollowingResp_FollowingListEntry_descriptor));
  const ::google::protobuf::Descriptor* BatchGetBiFollowingResp_FollowsMeListEntry_descriptor = protobuf_friendship_2eproto::file_level_metadata[16].descriptor;
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        BatchGetBiFollowingResp_FollowsMeListEntry_descriptor,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::uint32,
            ::ugc::friendship::Following,
            ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                BatchGetBiFollowingResp_FollowsMeListEntry_descriptor));
  const ::google::protobuf::Descriptor* BatchGetUserCountsResp_UserCountsMapEntry_descriptor = protobuf_friendship_2eproto::file_level_metadata[21].descriptor;
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
        BatchGetUserCountsResp_UserCountsMapEntry_descriptor,
        ::google::protobuf::internal::MapEntry<
            ::google::protobuf::uint32,
            ::ugc::friendship::Counts,
            ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
            ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
            0>::CreateDefaultInstance(
                BatchGetUserCountsResp_UserCountsMapEntry_descriptor));
}

}  // namespace

void TableStruct::Shutdown() {
  _FollowUserReq_default_instance_.Shutdown();
  delete file_level_metadata[0].reflection;
  _FollowUserResp_default_instance_.Shutdown();
  delete file_level_metadata[1].reflection;
  _UnfollowUserReq_default_instance_.Shutdown();
  delete file_level_metadata[2].reflection;
  _UnfollowUserResp_default_instance_.Shutdown();
  delete file_level_metadata[3].reflection;
  _Following_default_instance_.Shutdown();
  delete file_level_metadata[4].reflection;
  _Counts_default_instance_.Shutdown();
  delete file_level_metadata[5].reflection;
  _LoadMore_CreateAtAndUidKey_default_instance_.Shutdown();
  delete file_level_metadata[6].reflection;
  _LoadMore_SequenceIdKey_default_instance_.Shutdown();
  delete file_level_metadata[7].reflection;
  _LoadMore_IDKey_default_instance_.Shutdown();
  delete file_level_metadata[8].reflection;
  _LoadMore_default_instance_.Shutdown();
  delete file_level_metadata[9].reflection;
  _GetFollowingListReq_default_instance_.Shutdown();
  delete file_level_metadata[10].reflection;
  _GetFollowingListResp_default_instance_.Shutdown();
  delete file_level_metadata[11].reflection;
  _GetBiFollowingReq_default_instance_.Shutdown();
  delete file_level_metadata[12].reflection;
  _GetBiFollowingResp_default_instance_.Shutdown();
  delete file_level_metadata[13].reflection;
  _BatchGetBiFollowingReq_default_instance_.Shutdown();
  delete file_level_metadata[14].reflection;
  _BatchGetBiFollowingResp_default_instance_.Shutdown();
  delete file_level_metadata[17].reflection;
  _GetUserCountsReq_default_instance_.Shutdown();
  delete file_level_metadata[18].reflection;
  _GetUserCountsResp_default_instance_.Shutdown();
  delete file_level_metadata[19].reflection;
  _BatchGetUserCountsReq_default_instance_.Shutdown();
  delete file_level_metadata[20].reflection;
  _BatchGetUserCountsResp_default_instance_.Shutdown();
  delete file_level_metadata[22].reflection;
  _SyncFromFriendListReq_Friend_default_instance_.Shutdown();
  delete file_level_metadata[23].reflection;
  _SyncFromFriendListReq_default_instance_.Shutdown();
  delete file_level_metadata[24].reflection;
  _SyncFromFriendListResp_default_instance_.Shutdown();
  delete file_level_metadata[25].reflection;
  _GetSynchronizedSequenceReq_default_instance_.Shutdown();
  delete file_level_metadata[26].reflection;
  _GetSynchronizedSequenceResp_default_instance_.Shutdown();
  delete file_level_metadata[27].reflection;
}

void TableStruct::InitDefaultsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::internal::InitProtobufDefaults();
  _FollowUserReq_default_instance_.DefaultConstruct();
  _FollowUserResp_default_instance_.DefaultConstruct();
  _UnfollowUserReq_default_instance_.DefaultConstruct();
  _UnfollowUserResp_default_instance_.DefaultConstruct();
  _Following_default_instance_.DefaultConstruct();
  _Counts_default_instance_.DefaultConstruct();
  _LoadMore_CreateAtAndUidKey_default_instance_.DefaultConstruct();
  _LoadMore_SequenceIdKey_default_instance_.DefaultConstruct();
  _LoadMore_IDKey_default_instance_.DefaultConstruct();
  _LoadMore_default_instance_.DefaultConstruct();
  _GetFollowingListReq_default_instance_.DefaultConstruct();
  _GetFollowingListResp_default_instance_.DefaultConstruct();
  _GetBiFollowingReq_default_instance_.DefaultConstruct();
  _GetBiFollowingResp_default_instance_.DefaultConstruct();
  _BatchGetBiFollowingReq_default_instance_.DefaultConstruct();
  _BatchGetBiFollowingResp_default_instance_.DefaultConstruct();
  _GetUserCountsReq_default_instance_.DefaultConstruct();
  _GetUserCountsResp_default_instance_.DefaultConstruct();
  _BatchGetUserCountsReq_default_instance_.DefaultConstruct();
  _BatchGetUserCountsResp_default_instance_.DefaultConstruct();
  _SyncFromFriendListReq_Friend_default_instance_.DefaultConstruct();
  _SyncFromFriendListReq_default_instance_.DefaultConstruct();
  _SyncFromFriendListResp_default_instance_.DefaultConstruct();
  _GetSynchronizedSequenceReq_default_instance_.DefaultConstruct();
  _GetSynchronizedSequenceResp_default_instance_.DefaultConstruct();
  _LoadMore_default_instance_.create_at_and_uid_key_ = const_cast< ::ugc::friendship::LoadMore_CreateAtAndUidKey*>(
      ::ugc::friendship::LoadMore_CreateAtAndUidKey::internal_default_instance());
  _LoadMore_default_instance_.id_key_ = const_cast< ::ugc::friendship::LoadMore_IDKey*>(
      ::ugc::friendship::LoadMore_IDKey::internal_default_instance());
  _LoadMore_default_instance_.sequence_id_key_ = const_cast< ::ugc::friendship::LoadMore_SequenceIdKey*>(
      ::ugc::friendship::LoadMore_SequenceIdKey::internal_default_instance());
  _GetFollowingListReq_default_instance_.followed_by_uid_ = 0u;
  _GetFollowingListReq_default_instance_.following_uid_ = 0u;
  _GetFollowingListReq_default_instance_.get_mutable()->load_more_ = const_cast< ::ugc::friendship::LoadMore*>(
      ::ugc::friendship::LoadMore::internal_default_instance());
  _GetFollowingListResp_default_instance_.get_mutable()->load_more_ = const_cast< ::ugc::friendship::LoadMore*>(
      ::ugc::friendship::LoadMore::internal_default_instance());
  _GetBiFollowingResp_default_instance_.get_mutable()->a_to_b_ = const_cast< ::ugc::friendship::Following*>(
      ::ugc::friendship::Following::internal_default_instance());
  _GetBiFollowingResp_default_instance_.get_mutable()->b_to_a_ = const_cast< ::ugc::friendship::Following*>(
      ::ugc::friendship::Following::internal_default_instance());
  _GetUserCountsResp_default_instance_.get_mutable()->user_counts_ = const_cast< ::ugc::friendship::Counts*>(
      ::ugc::friendship::Counts::internal_default_instance());
}

void InitDefaults() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &TableStruct::InitDefaultsImpl);
}
void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] = {
      "\n\020friendship.proto\022\016ugc.friendship\"p\n\rFo"
      "llowUserReq\022\013\n\003uid\030\001 \001(\r\022\025\n\rfollowing_ui"
      "d\030\002 \001(\r\022\023\n\013sequence_id\030\003 \001(\004\022&\n\006source\030\004"
      " \001(\0162\026.ugc.friendship.Source\"@\n\016FollowUs"
      "erResp\022\020\n\010affected\030\001 \001(\010\022\034\n\024is_first_tim"
      "e_follow\030\002 \001(\010\"_\n\017UnfollowUserReq\022\013\n\003uid"
      "\030\001 \001(\r\022\025\n\rfollowing_uid\030\002 \001(\r\022\023\n\013sequenc"
      "e_id\030\003 \001(\004\022\023\n\013hard_delete\030\004 \001(\010\"$\n\020Unfol"
      "lowUserResp\022\020\n\010affected\030\001 \001(\010\"r\n\tFollowi"
      "ng\022\020\n\010from_uid\030\001 \001(\r\022\016\n\006to_uid\030\002 \001(\r\022\017\n\007"
      "dropped\030\003 \001(\010\022\021\n\tcreate_at\030\004 \001(\004\022\023\n\013sequ"
      "ence_id\030\005 \001(\004\022\n\n\002id\030\006 \001(\t\"9\n\006Counts\022\027\n\017f"
      "ollowing_count\030\001 \001(\r\022\026\n\016follower_count\030\002"
      " \001(\r\"\253\003\n\010LoadMore\022+\n\004sort\030\001 \001(\0162\035.ugc.fr"
      "iendship.LoadMore.Sort\022K\n\025create_at_and_"
      "uid_key\030\002 \001(\0132*.ugc.friendship.LoadMore."
      "CreateAtAndUidKeyH\000\0220\n\006id_key\030\003 \001(\0132\036.ug"
      "c.friendship.LoadMore.IDKeyH\000\022A\n\017sequenc"
      "e_id_key\030\004 \001(\0132&.ugc.friendship.LoadMore"
      ".SequenceIdKeyH\000\032=\n\021CreateAtAndUidKey\022\026\n"
      "\016last_create_at\030\001 \001(\004\022\020\n\010last_uid\030\002 \001(\r\032"
      ")\n\rSequenceIdKey\022\030\n\020last_sequence_id\030\001 \001"
      "(\004\032\037\n\005IDKey\022\026\n\016last_object_id\030\001 \001(\t\"\031\n\004S"
      "ort\022\007\n\003ASC\020\000\022\010\n\004DESC\020\001B\n\n\010order_by\"\260\001\n\023G"
      "etFollowingListReq\022\031\n\017followed_by_uid\030\001 "
      "\001(\rH\000\022\027\n\rfollowing_uid\030\002 \001(\rH\000\022\031\n\021includ"
      "ing_dropped\030\003 \001(\010\022+\n\tload_more\030\n \001(\0132\030.u"
      "gc.friendship.LoadMore\022\r\n\005count\030\013 \001(\rB\016\n"
      "\014request_type\"v\n\024GetFollowingListResp\0221\n"
      "\016following_list\030\001 \003(\0132\031.ugc.friendship.F"
      "ollowing\022+\n\tload_more\030\002 \001(\0132\030.ugc.friend"
      "ship.LoadMore\"1\n\021GetBiFollowingReq\022\r\n\005ui"
      "d_a\030\001 \001(\r\022\r\n\005uid_b\030\002 \001(\r\"j\n\022GetBiFollowi"
      "ngResp\022)\n\006a_to_b\030\001 \001(\0132\031.ugc.friendship."
      "Following\022)\n\006b_to_a\030\002 \001(\0132\031.ugc.friendsh"
      "ip.Following\"\?\n\026BatchGetBiFollowingReq\022\020"
      "\n\010from_uid\030\001 \001(\r\022\023\n\013to_uid_list\030\002 \001(\r\"\344\002"
      "\n\027BatchGetBiFollowingResp\022R\n\016following_l"
      "ist\030\001 \003(\0132:.ugc.friendship.BatchGetBiFol"
      "lowingResp.FollowingListEntry\022S\n\017follows"
      "_me_list\030\002 \003(\0132:.ugc.friendship.BatchGet"
      "BiFollowingResp.FollowsMeListEntry\032O\n\022Fo"
      "llowingListEntry\022\013\n\003key\030\001 \001(\r\022(\n\005value\030\002"
      " \001(\0132\031.ugc.friendship.Following:\0028\001\032O\n\022F"
      "ollowsMeListEntry\022\013\n\003key\030\001 \001(\r\022(\n\005value\030"
      "\002 \001(\0132\031.ugc.friendship.Following:\0028\001\"\037\n\020"
      "GetUserCountsReq\022\013\n\003uid\030\001 \001(\r\"@\n\021GetUser"
      "CountsResp\022+\n\013user_counts\030\001 \001(\0132\026.ugc.fr"
      "iendship.Counts\")\n\025BatchGetUserCountsReq"
      "\022\020\n\010uid_list\030\001 \003(\r\"\272\001\n\026BatchGetUserCount"
      "sResp\022R\n\017user_counts_map\030\002 \003(\01329.ugc.fri"
      "endship.BatchGetUserCountsResp.UserCount"
      "sMapEntry\032L\n\022UserCountsMapEntry\022\013\n\003key\030\001"
      " \001(\r\022%\n\005value\030\002 \001(\0132\026.ugc.friendship.Cou"
      "nts:\0028\001\"\223\002\n\025SyncFromFriendListReq\022\013\n\003uid"
      "\030\001 \001(\r\022A\n\013friend_list\030\002 \003(\0132,.ugc.friend"
      "ship.SyncFromFriendListReq.Friend\022\030\n\020max"
      "_ugc_sequence\030\003 \001(\004\022+\n#add_reverse_seque"
      "nce_zero_following\030\004 \001(\010\032c\n\006Friend\022\013\n\003ui"
      "d\030\001 \001(\r\022\021\n\tis_delete\030\002 \001(\010\022\033\n\023create_at_"
      "timestamp\030\003 \001(\004\022\034\n\024create_or_delete_seq\030"
      "\004 \001(\004\"\030\n\026SyncFromFriendListResp\")\n\032GetSy"
      "nchronizedSequenceReq\022\013\n\003uid\030\001 \001(\r\"2\n\033Ge"
      "tSynchronizedSequenceResp\022\023\n\013sequence_id"
      "\030\001 \001(\004*-\n\006Source\022\020\n\014USER_OPERATE\020\000\022\021\n\rFR"
      "IEND_VERIFY\020\0012\253\004\n\nFriendship\022M\n\nFollowUs"
      "er\022\035.ugc.friendship.FollowUserReq\032\036.ugc."
      "friendship.FollowUserResp\"\000\022S\n\014UnfollowU"
      "ser\022\037.ugc.friendship.UnfollowUserReq\032 .u"
      "gc.friendship.UnfollowUserResp\"\000\022_\n\020GetF"
      "ollowingList\022#.ugc.friendship.GetFollowi"
      "ngListReq\032$.ugc.friendship.GetFollowingL"
      "istResp\"\000\022V\n\rGetUserCounts\022 .ugc.friends"
      "hip.GetUserCountsReq\032!.ugc.friendship.Ge"
      "tUserCountsResp\"\000\022e\n\022BatchGetUserCounts\022"
      "%.ugc.friendship.BatchGetUserCountsReq\032&"
      ".ugc.friendship.BatchGetUserCountsResp\"\000"
      "\022Y\n\016GetBiFollowing\022!.ugc.friendship.GetB"
      "iFollowingReq\032\".ugc.friendship.GetBiFoll"
      "owingResp\"\0002\353\001\n\014Synchronizer\022t\n\027GetSynch"
      "ronizedSequence\022*.ugc.friendship.GetSync"
      "hronizedSequenceReq\032+.ugc.friendship.Get"
      "SynchronizedSequenceResp\"\000\022e\n\022SyncFromFr"
      "iendList\022%.ugc.friendship.SyncFromFriend"
      "ListReq\032&.ugc.friendship.SyncFromFriendL"
      "istResp\"\000B,Z*golang.52tt.com/services/ugc/protoco"
      "l/go/friendshipb\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 3463);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "friendship.proto", &protobuf_RegisterTypes);
  ::google::protobuf::internal::OnShutdown(&TableStruct::Shutdown);
}

void AddDescriptors() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;

}  // namespace protobuf_friendship_2eproto

const ::google::protobuf::EnumDescriptor* LoadMore_Sort_descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_enum_descriptors[0];
}
bool LoadMore_Sort_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const LoadMore_Sort LoadMore::ASC;
const LoadMore_Sort LoadMore::DESC;
const LoadMore_Sort LoadMore::Sort_MIN;
const LoadMore_Sort LoadMore::Sort_MAX;
const int LoadMore::Sort_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900
const ::google::protobuf::EnumDescriptor* Source_descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_enum_descriptors[1];
}
bool Source_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FollowUserReq::kUidFieldNumber;
const int FollowUserReq::kFollowingUidFieldNumber;
const int FollowUserReq::kSequenceIdFieldNumber;
const int FollowUserReq::kSourceFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FollowUserReq::FollowUserReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.FollowUserReq)
}
FollowUserReq::FollowUserReq(const FollowUserReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&uid_, &from.uid_,
    reinterpret_cast<char*>(&source_) -
    reinterpret_cast<char*>(&uid_) + sizeof(source_));
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.FollowUserReq)
}

void FollowUserReq::SharedCtor() {
  ::memset(&uid_, 0, reinterpret_cast<char*>(&source_) -
    reinterpret_cast<char*>(&uid_) + sizeof(source_));
  _cached_size_ = 0;
}

FollowUserReq::~FollowUserReq() {
  // @@protoc_insertion_point(destructor:ugc.friendship.FollowUserReq)
  SharedDtor();
}

void FollowUserReq::SharedDtor() {
}

void FollowUserReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* FollowUserReq::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[0].descriptor;
}

const FollowUserReq& FollowUserReq::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

FollowUserReq* FollowUserReq::New(::google::protobuf::Arena* arena) const {
  FollowUserReq* n = new FollowUserReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void FollowUserReq::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.FollowUserReq)
  ::memset(&uid_, 0, reinterpret_cast<char*>(&source_) -
    reinterpret_cast<char*>(&uid_) + sizeof(source_));
}

bool FollowUserReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.FollowUserReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 uid = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &uid_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint32 following_uid = 2;
      case 2: {
        if (tag == 16u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &following_uid_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 sequence_id = 3;
      case 3: {
        if (tag == 24u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &sequence_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .ugc.friendship.Source source = 4;
      case 4: {
        if (tag == 32u) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_source(static_cast< ::ugc::friendship::Source >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.FollowUserReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.FollowUserReq)
  return false;
#undef DO_
}

void FollowUserReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.FollowUserReq)
  // uint32 uid = 1;
  if (this->uid() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->uid(), output);
  }

  // uint32 following_uid = 2;
  if (this->following_uid() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->following_uid(), output);
  }

  // uint64 sequence_id = 3;
  if (this->sequence_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(3, this->sequence_id(), output);
  }

  // .ugc.friendship.Source source = 4;
  if (this->source() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      4, this->source(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.FollowUserReq)
}

::google::protobuf::uint8* FollowUserReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.FollowUserReq)
  // uint32 uid = 1;
  if (this->uid() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->uid(), target);
  }

  // uint32 following_uid = 2;
  if (this->following_uid() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->following_uid(), target);
  }

  // uint64 sequence_id = 3;
  if (this->sequence_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(3, this->sequence_id(), target);
  }

  // .ugc.friendship.Source source = 4;
  if (this->source() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      4, this->source(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.FollowUserReq)
  return target;
}

size_t FollowUserReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.FollowUserReq)
  size_t total_size = 0;

  // uint32 uid = 1;
  if (this->uid() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->uid());
  }

  // uint32 following_uid = 2;
  if (this->following_uid() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->following_uid());
  }

  // uint64 sequence_id = 3;
  if (this->sequence_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->sequence_id());
  }

  // .ugc.friendship.Source source = 4;
  if (this->source() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->source());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void FollowUserReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.FollowUserReq)
  GOOGLE_DCHECK_NE(&from, this);
  const FollowUserReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FollowUserReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.FollowUserReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.FollowUserReq)
    MergeFrom(*source);
  }
}

void FollowUserReq::MergeFrom(const FollowUserReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.FollowUserReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.uid() != 0) {
    set_uid(from.uid());
  }
  if (from.following_uid() != 0) {
    set_following_uid(from.following_uid());
  }
  if (from.sequence_id() != 0) {
    set_sequence_id(from.sequence_id());
  }
  if (from.source() != 0) {
    set_source(from.source());
  }
}

void FollowUserReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.FollowUserReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FollowUserReq::CopyFrom(const FollowUserReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.FollowUserReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FollowUserReq::IsInitialized() const {
  return true;
}

void FollowUserReq::Swap(FollowUserReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void FollowUserReq::InternalSwap(FollowUserReq* other) {
  std::swap(uid_, other->uid_);
  std::swap(following_uid_, other->following_uid_);
  std::swap(sequence_id_, other->sequence_id_);
  std::swap(source_, other->source_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata FollowUserReq::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[0];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// FollowUserReq

// uint32 uid = 1;
void FollowUserReq::clear_uid() {
  uid_ = 0u;
}
::google::protobuf::uint32 FollowUserReq::uid() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.FollowUserReq.uid)
  return uid_;
}
void FollowUserReq::set_uid(::google::protobuf::uint32 value) {
  
  uid_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.FollowUserReq.uid)
}

// uint32 following_uid = 2;
void FollowUserReq::clear_following_uid() {
  following_uid_ = 0u;
}
::google::protobuf::uint32 FollowUserReq::following_uid() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.FollowUserReq.following_uid)
  return following_uid_;
}
void FollowUserReq::set_following_uid(::google::protobuf::uint32 value) {
  
  following_uid_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.FollowUserReq.following_uid)
}

// uint64 sequence_id = 3;
void FollowUserReq::clear_sequence_id() {
  sequence_id_ = GOOGLE_ULONGLONG(0);
}
::google::protobuf::uint64 FollowUserReq::sequence_id() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.FollowUserReq.sequence_id)
  return sequence_id_;
}
void FollowUserReq::set_sequence_id(::google::protobuf::uint64 value) {
  
  sequence_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.FollowUserReq.sequence_id)
}

// .ugc.friendship.Source source = 4;
void FollowUserReq::clear_source() {
  source_ = 0;
}
::ugc::friendship::Source FollowUserReq::source() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.FollowUserReq.source)
  return static_cast< ::ugc::friendship::Source >(source_);
}
void FollowUserReq::set_source(::ugc::friendship::Source value) {
  
  source_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.FollowUserReq.source)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FollowUserResp::kAffectedFieldNumber;
const int FollowUserResp::kIsFirstTimeFollowFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FollowUserResp::FollowUserResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.FollowUserResp)
}
FollowUserResp::FollowUserResp(const FollowUserResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&affected_, &from.affected_,
    reinterpret_cast<char*>(&is_first_time_follow_) -
    reinterpret_cast<char*>(&affected_) + sizeof(is_first_time_follow_));
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.FollowUserResp)
}

void FollowUserResp::SharedCtor() {
  ::memset(&affected_, 0, reinterpret_cast<char*>(&is_first_time_follow_) -
    reinterpret_cast<char*>(&affected_) + sizeof(is_first_time_follow_));
  _cached_size_ = 0;
}

FollowUserResp::~FollowUserResp() {
  // @@protoc_insertion_point(destructor:ugc.friendship.FollowUserResp)
  SharedDtor();
}

void FollowUserResp::SharedDtor() {
}

void FollowUserResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* FollowUserResp::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[1].descriptor;
}

const FollowUserResp& FollowUserResp::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

FollowUserResp* FollowUserResp::New(::google::protobuf::Arena* arena) const {
  FollowUserResp* n = new FollowUserResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void FollowUserResp::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.FollowUserResp)
  ::memset(&affected_, 0, reinterpret_cast<char*>(&is_first_time_follow_) -
    reinterpret_cast<char*>(&affected_) + sizeof(is_first_time_follow_));
}

bool FollowUserResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.FollowUserResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // bool affected = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &affected_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool is_first_time_follow = 2;
      case 2: {
        if (tag == 16u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &is_first_time_follow_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.FollowUserResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.FollowUserResp)
  return false;
#undef DO_
}

void FollowUserResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.FollowUserResp)
  // bool affected = 1;
  if (this->affected() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->affected(), output);
  }

  // bool is_first_time_follow = 2;
  if (this->is_first_time_follow() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(2, this->is_first_time_follow(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.FollowUserResp)
}

::google::protobuf::uint8* FollowUserResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.FollowUserResp)
  // bool affected = 1;
  if (this->affected() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->affected(), target);
  }

  // bool is_first_time_follow = 2;
  if (this->is_first_time_follow() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(2, this->is_first_time_follow(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.FollowUserResp)
  return target;
}

size_t FollowUserResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.FollowUserResp)
  size_t total_size = 0;

  // bool affected = 1;
  if (this->affected() != 0) {
    total_size += 1 + 1;
  }

  // bool is_first_time_follow = 2;
  if (this->is_first_time_follow() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void FollowUserResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.FollowUserResp)
  GOOGLE_DCHECK_NE(&from, this);
  const FollowUserResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FollowUserResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.FollowUserResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.FollowUserResp)
    MergeFrom(*source);
  }
}

void FollowUserResp::MergeFrom(const FollowUserResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.FollowUserResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.affected() != 0) {
    set_affected(from.affected());
  }
  if (from.is_first_time_follow() != 0) {
    set_is_first_time_follow(from.is_first_time_follow());
  }
}

void FollowUserResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.FollowUserResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FollowUserResp::CopyFrom(const FollowUserResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.FollowUserResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FollowUserResp::IsInitialized() const {
  return true;
}

void FollowUserResp::Swap(FollowUserResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void FollowUserResp::InternalSwap(FollowUserResp* other) {
  std::swap(affected_, other->affected_);
  std::swap(is_first_time_follow_, other->is_first_time_follow_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata FollowUserResp::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[1];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// FollowUserResp

// bool affected = 1;
void FollowUserResp::clear_affected() {
  affected_ = false;
}
bool FollowUserResp::affected() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.FollowUserResp.affected)
  return affected_;
}
void FollowUserResp::set_affected(bool value) {
  
  affected_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.FollowUserResp.affected)
}

// bool is_first_time_follow = 2;
void FollowUserResp::clear_is_first_time_follow() {
  is_first_time_follow_ = false;
}
bool FollowUserResp::is_first_time_follow() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.FollowUserResp.is_first_time_follow)
  return is_first_time_follow_;
}
void FollowUserResp::set_is_first_time_follow(bool value) {
  
  is_first_time_follow_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.FollowUserResp.is_first_time_follow)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int UnfollowUserReq::kUidFieldNumber;
const int UnfollowUserReq::kFollowingUidFieldNumber;
const int UnfollowUserReq::kSequenceIdFieldNumber;
const int UnfollowUserReq::kHardDeleteFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

UnfollowUserReq::UnfollowUserReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.UnfollowUserReq)
}
UnfollowUserReq::UnfollowUserReq(const UnfollowUserReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&uid_, &from.uid_,
    reinterpret_cast<char*>(&hard_delete_) -
    reinterpret_cast<char*>(&uid_) + sizeof(hard_delete_));
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.UnfollowUserReq)
}

void UnfollowUserReq::SharedCtor() {
  ::memset(&uid_, 0, reinterpret_cast<char*>(&hard_delete_) -
    reinterpret_cast<char*>(&uid_) + sizeof(hard_delete_));
  _cached_size_ = 0;
}

UnfollowUserReq::~UnfollowUserReq() {
  // @@protoc_insertion_point(destructor:ugc.friendship.UnfollowUserReq)
  SharedDtor();
}

void UnfollowUserReq::SharedDtor() {
}

void UnfollowUserReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* UnfollowUserReq::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[2].descriptor;
}

const UnfollowUserReq& UnfollowUserReq::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

UnfollowUserReq* UnfollowUserReq::New(::google::protobuf::Arena* arena) const {
  UnfollowUserReq* n = new UnfollowUserReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void UnfollowUserReq::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.UnfollowUserReq)
  ::memset(&uid_, 0, reinterpret_cast<char*>(&hard_delete_) -
    reinterpret_cast<char*>(&uid_) + sizeof(hard_delete_));
}

bool UnfollowUserReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.UnfollowUserReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 uid = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &uid_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint32 following_uid = 2;
      case 2: {
        if (tag == 16u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &following_uid_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 sequence_id = 3;
      case 3: {
        if (tag == 24u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &sequence_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool hard_delete = 4;
      case 4: {
        if (tag == 32u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &hard_delete_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.UnfollowUserReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.UnfollowUserReq)
  return false;
#undef DO_
}

void UnfollowUserReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.UnfollowUserReq)
  // uint32 uid = 1;
  if (this->uid() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->uid(), output);
  }

  // uint32 following_uid = 2;
  if (this->following_uid() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->following_uid(), output);
  }

  // uint64 sequence_id = 3;
  if (this->sequence_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(3, this->sequence_id(), output);
  }

  // bool hard_delete = 4;
  if (this->hard_delete() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(4, this->hard_delete(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.UnfollowUserReq)
}

::google::protobuf::uint8* UnfollowUserReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.UnfollowUserReq)
  // uint32 uid = 1;
  if (this->uid() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->uid(), target);
  }

  // uint32 following_uid = 2;
  if (this->following_uid() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->following_uid(), target);
  }

  // uint64 sequence_id = 3;
  if (this->sequence_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(3, this->sequence_id(), target);
  }

  // bool hard_delete = 4;
  if (this->hard_delete() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(4, this->hard_delete(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.UnfollowUserReq)
  return target;
}

size_t UnfollowUserReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.UnfollowUserReq)
  size_t total_size = 0;

  // uint32 uid = 1;
  if (this->uid() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->uid());
  }

  // uint32 following_uid = 2;
  if (this->following_uid() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->following_uid());
  }

  // uint64 sequence_id = 3;
  if (this->sequence_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->sequence_id());
  }

  // bool hard_delete = 4;
  if (this->hard_delete() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void UnfollowUserReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.UnfollowUserReq)
  GOOGLE_DCHECK_NE(&from, this);
  const UnfollowUserReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const UnfollowUserReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.UnfollowUserReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.UnfollowUserReq)
    MergeFrom(*source);
  }
}

void UnfollowUserReq::MergeFrom(const UnfollowUserReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.UnfollowUserReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.uid() != 0) {
    set_uid(from.uid());
  }
  if (from.following_uid() != 0) {
    set_following_uid(from.following_uid());
  }
  if (from.sequence_id() != 0) {
    set_sequence_id(from.sequence_id());
  }
  if (from.hard_delete() != 0) {
    set_hard_delete(from.hard_delete());
  }
}

void UnfollowUserReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.UnfollowUserReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UnfollowUserReq::CopyFrom(const UnfollowUserReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.UnfollowUserReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UnfollowUserReq::IsInitialized() const {
  return true;
}

void UnfollowUserReq::Swap(UnfollowUserReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void UnfollowUserReq::InternalSwap(UnfollowUserReq* other) {
  std::swap(uid_, other->uid_);
  std::swap(following_uid_, other->following_uid_);
  std::swap(sequence_id_, other->sequence_id_);
  std::swap(hard_delete_, other->hard_delete_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata UnfollowUserReq::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[2];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// UnfollowUserReq

// uint32 uid = 1;
void UnfollowUserReq::clear_uid() {
  uid_ = 0u;
}
::google::protobuf::uint32 UnfollowUserReq::uid() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.UnfollowUserReq.uid)
  return uid_;
}
void UnfollowUserReq::set_uid(::google::protobuf::uint32 value) {
  
  uid_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.UnfollowUserReq.uid)
}

// uint32 following_uid = 2;
void UnfollowUserReq::clear_following_uid() {
  following_uid_ = 0u;
}
::google::protobuf::uint32 UnfollowUserReq::following_uid() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.UnfollowUserReq.following_uid)
  return following_uid_;
}
void UnfollowUserReq::set_following_uid(::google::protobuf::uint32 value) {
  
  following_uid_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.UnfollowUserReq.following_uid)
}

// uint64 sequence_id = 3;
void UnfollowUserReq::clear_sequence_id() {
  sequence_id_ = GOOGLE_ULONGLONG(0);
}
::google::protobuf::uint64 UnfollowUserReq::sequence_id() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.UnfollowUserReq.sequence_id)
  return sequence_id_;
}
void UnfollowUserReq::set_sequence_id(::google::protobuf::uint64 value) {
  
  sequence_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.UnfollowUserReq.sequence_id)
}

// bool hard_delete = 4;
void UnfollowUserReq::clear_hard_delete() {
  hard_delete_ = false;
}
bool UnfollowUserReq::hard_delete() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.UnfollowUserReq.hard_delete)
  return hard_delete_;
}
void UnfollowUserReq::set_hard_delete(bool value) {
  
  hard_delete_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.UnfollowUserReq.hard_delete)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int UnfollowUserResp::kAffectedFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

UnfollowUserResp::UnfollowUserResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.UnfollowUserResp)
}
UnfollowUserResp::UnfollowUserResp(const UnfollowUserResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  affected_ = from.affected_;
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.UnfollowUserResp)
}

void UnfollowUserResp::SharedCtor() {
  affected_ = false;
  _cached_size_ = 0;
}

UnfollowUserResp::~UnfollowUserResp() {
  // @@protoc_insertion_point(destructor:ugc.friendship.UnfollowUserResp)
  SharedDtor();
}

void UnfollowUserResp::SharedDtor() {
}

void UnfollowUserResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* UnfollowUserResp::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[3].descriptor;
}

const UnfollowUserResp& UnfollowUserResp::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

UnfollowUserResp* UnfollowUserResp::New(::google::protobuf::Arena* arena) const {
  UnfollowUserResp* n = new UnfollowUserResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void UnfollowUserResp::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.UnfollowUserResp)
  affected_ = false;
}

bool UnfollowUserResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.UnfollowUserResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // bool affected = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &affected_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.UnfollowUserResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.UnfollowUserResp)
  return false;
#undef DO_
}

void UnfollowUserResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.UnfollowUserResp)
  // bool affected = 1;
  if (this->affected() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->affected(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.UnfollowUserResp)
}

::google::protobuf::uint8* UnfollowUserResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.UnfollowUserResp)
  // bool affected = 1;
  if (this->affected() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->affected(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.UnfollowUserResp)
  return target;
}

size_t UnfollowUserResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.UnfollowUserResp)
  size_t total_size = 0;

  // bool affected = 1;
  if (this->affected() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void UnfollowUserResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.UnfollowUserResp)
  GOOGLE_DCHECK_NE(&from, this);
  const UnfollowUserResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const UnfollowUserResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.UnfollowUserResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.UnfollowUserResp)
    MergeFrom(*source);
  }
}

void UnfollowUserResp::MergeFrom(const UnfollowUserResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.UnfollowUserResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.affected() != 0) {
    set_affected(from.affected());
  }
}

void UnfollowUserResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.UnfollowUserResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UnfollowUserResp::CopyFrom(const UnfollowUserResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.UnfollowUserResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UnfollowUserResp::IsInitialized() const {
  return true;
}

void UnfollowUserResp::Swap(UnfollowUserResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void UnfollowUserResp::InternalSwap(UnfollowUserResp* other) {
  std::swap(affected_, other->affected_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata UnfollowUserResp::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[3];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// UnfollowUserResp

// bool affected = 1;
void UnfollowUserResp::clear_affected() {
  affected_ = false;
}
bool UnfollowUserResp::affected() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.UnfollowUserResp.affected)
  return affected_;
}
void UnfollowUserResp::set_affected(bool value) {
  
  affected_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.UnfollowUserResp.affected)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Following::kFromUidFieldNumber;
const int Following::kToUidFieldNumber;
const int Following::kDroppedFieldNumber;
const int Following::kCreateAtFieldNumber;
const int Following::kSequenceIdFieldNumber;
const int Following::kIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Following::Following()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.Following)
}
Following::Following(const Following& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.id().size() > 0) {
    id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.id_);
  }
  ::memcpy(&from_uid_, &from.from_uid_,
    reinterpret_cast<char*>(&dropped_) -
    reinterpret_cast<char*>(&from_uid_) + sizeof(dropped_));
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.Following)
}

void Following::SharedCtor() {
  id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&from_uid_, 0, reinterpret_cast<char*>(&dropped_) -
    reinterpret_cast<char*>(&from_uid_) + sizeof(dropped_));
  _cached_size_ = 0;
}

Following::~Following() {
  // @@protoc_insertion_point(destructor:ugc.friendship.Following)
  SharedDtor();
}

void Following::SharedDtor() {
  id_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void Following::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Following::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[4].descriptor;
}

const Following& Following::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

Following* Following::New(::google::protobuf::Arena* arena) const {
  Following* n = new Following;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Following::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.Following)
  id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&from_uid_, 0, reinterpret_cast<char*>(&dropped_) -
    reinterpret_cast<char*>(&from_uid_) + sizeof(dropped_));
}

bool Following::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.Following)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 from_uid = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &from_uid_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint32 to_uid = 2;
      case 2: {
        if (tag == 16u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &to_uid_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool dropped = 3;
      case 3: {
        if (tag == 24u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &dropped_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 create_at = 4;
      case 4: {
        if (tag == 32u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &create_at_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 sequence_id = 5;
      case 5: {
        if (tag == 40u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &sequence_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string id = 6;
      case 6: {
        if (tag == 50u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_id()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->id().data(), this->id().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "ugc.friendship.Following.id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.Following)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.Following)
  return false;
#undef DO_
}

void Following::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.Following)
  // uint32 from_uid = 1;
  if (this->from_uid() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->from_uid(), output);
  }

  // uint32 to_uid = 2;
  if (this->to_uid() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->to_uid(), output);
  }

  // bool dropped = 3;
  if (this->dropped() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(3, this->dropped(), output);
  }

  // uint64 create_at = 4;
  if (this->create_at() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(4, this->create_at(), output);
  }

  // uint64 sequence_id = 5;
  if (this->sequence_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(5, this->sequence_id(), output);
  }

  // string id = 6;
  if (this->id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->id().data(), this->id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.friendship.Following.id");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->id(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.Following)
}

::google::protobuf::uint8* Following::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.Following)
  // uint32 from_uid = 1;
  if (this->from_uid() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->from_uid(), target);
  }

  // uint32 to_uid = 2;
  if (this->to_uid() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->to_uid(), target);
  }

  // bool dropped = 3;
  if (this->dropped() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(3, this->dropped(), target);
  }

  // uint64 create_at = 4;
  if (this->create_at() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(4, this->create_at(), target);
  }

  // uint64 sequence_id = 5;
  if (this->sequence_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(5, this->sequence_id(), target);
  }

  // string id = 6;
  if (this->id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->id().data(), this->id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.friendship.Following.id");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->id(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.Following)
  return target;
}

size_t Following::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.Following)
  size_t total_size = 0;

  // string id = 6;
  if (this->id().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->id());
  }

  // uint32 from_uid = 1;
  if (this->from_uid() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->from_uid());
  }

  // uint32 to_uid = 2;
  if (this->to_uid() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->to_uid());
  }

  // uint64 create_at = 4;
  if (this->create_at() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->create_at());
  }

  // uint64 sequence_id = 5;
  if (this->sequence_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->sequence_id());
  }

  // bool dropped = 3;
  if (this->dropped() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Following::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.Following)
  GOOGLE_DCHECK_NE(&from, this);
  const Following* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Following>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.Following)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.Following)
    MergeFrom(*source);
  }
}

void Following::MergeFrom(const Following& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.Following)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.id().size() > 0) {

    id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.id_);
  }
  if (from.from_uid() != 0) {
    set_from_uid(from.from_uid());
  }
  if (from.to_uid() != 0) {
    set_to_uid(from.to_uid());
  }
  if (from.create_at() != 0) {
    set_create_at(from.create_at());
  }
  if (from.sequence_id() != 0) {
    set_sequence_id(from.sequence_id());
  }
  if (from.dropped() != 0) {
    set_dropped(from.dropped());
  }
}

void Following::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.Following)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Following::CopyFrom(const Following& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.Following)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Following::IsInitialized() const {
  return true;
}

void Following::Swap(Following* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Following::InternalSwap(Following* other) {
  id_.Swap(&other->id_);
  std::swap(from_uid_, other->from_uid_);
  std::swap(to_uid_, other->to_uid_);
  std::swap(create_at_, other->create_at_);
  std::swap(sequence_id_, other->sequence_id_);
  std::swap(dropped_, other->dropped_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Following::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[4];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Following

// uint32 from_uid = 1;
void Following::clear_from_uid() {
  from_uid_ = 0u;
}
::google::protobuf::uint32 Following::from_uid() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.Following.from_uid)
  return from_uid_;
}
void Following::set_from_uid(::google::protobuf::uint32 value) {
  
  from_uid_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.Following.from_uid)
}

// uint32 to_uid = 2;
void Following::clear_to_uid() {
  to_uid_ = 0u;
}
::google::protobuf::uint32 Following::to_uid() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.Following.to_uid)
  return to_uid_;
}
void Following::set_to_uid(::google::protobuf::uint32 value) {
  
  to_uid_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.Following.to_uid)
}

// bool dropped = 3;
void Following::clear_dropped() {
  dropped_ = false;
}
bool Following::dropped() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.Following.dropped)
  return dropped_;
}
void Following::set_dropped(bool value) {
  
  dropped_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.Following.dropped)
}

// uint64 create_at = 4;
void Following::clear_create_at() {
  create_at_ = GOOGLE_ULONGLONG(0);
}
::google::protobuf::uint64 Following::create_at() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.Following.create_at)
  return create_at_;
}
void Following::set_create_at(::google::protobuf::uint64 value) {
  
  create_at_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.Following.create_at)
}

// uint64 sequence_id = 5;
void Following::clear_sequence_id() {
  sequence_id_ = GOOGLE_ULONGLONG(0);
}
::google::protobuf::uint64 Following::sequence_id() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.Following.sequence_id)
  return sequence_id_;
}
void Following::set_sequence_id(::google::protobuf::uint64 value) {
  
  sequence_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.Following.sequence_id)
}

// string id = 6;
void Following::clear_id() {
  id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& Following::id() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.Following.id)
  return id_.GetNoArena();
}
void Following::set_id(const ::std::string& value) {
  
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.friendship.Following.id)
}
#if LANG_CXX11
void Following::set_id(::std::string&& value) {
  
  id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.friendship.Following.id)
}
#endif
void Following::set_id(const char* value) {
  
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.friendship.Following.id)
}
void Following::set_id(const char* value, size_t size) {
  
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.friendship.Following.id)
}
::std::string* Following::mutable_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.friendship.Following.id)
  return id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* Following::release_id() {
  // @@protoc_insertion_point(field_release:ugc.friendship.Following.id)
  
  return id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void Following::set_allocated_id(::std::string* id) {
  if (id != NULL) {
    
  } else {
    
  }
  id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), id);
  // @@protoc_insertion_point(field_set_allocated:ugc.friendship.Following.id)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Counts::kFollowingCountFieldNumber;
const int Counts::kFollowerCountFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Counts::Counts()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.Counts)
}
Counts::Counts(const Counts& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&following_count_, &from.following_count_,
    reinterpret_cast<char*>(&follower_count_) -
    reinterpret_cast<char*>(&following_count_) + sizeof(follower_count_));
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.Counts)
}

void Counts::SharedCtor() {
  ::memset(&following_count_, 0, reinterpret_cast<char*>(&follower_count_) -
    reinterpret_cast<char*>(&following_count_) + sizeof(follower_count_));
  _cached_size_ = 0;
}

Counts::~Counts() {
  // @@protoc_insertion_point(destructor:ugc.friendship.Counts)
  SharedDtor();
}

void Counts::SharedDtor() {
}

void Counts::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Counts::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[5].descriptor;
}

const Counts& Counts::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

Counts* Counts::New(::google::protobuf::Arena* arena) const {
  Counts* n = new Counts;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Counts::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.Counts)
  ::memset(&following_count_, 0, reinterpret_cast<char*>(&follower_count_) -
    reinterpret_cast<char*>(&following_count_) + sizeof(follower_count_));
}

bool Counts::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.Counts)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 following_count = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &following_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint32 follower_count = 2;
      case 2: {
        if (tag == 16u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &follower_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.Counts)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.Counts)
  return false;
#undef DO_
}

void Counts::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.Counts)
  // uint32 following_count = 1;
  if (this->following_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->following_count(), output);
  }

  // uint32 follower_count = 2;
  if (this->follower_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->follower_count(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.Counts)
}

::google::protobuf::uint8* Counts::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.Counts)
  // uint32 following_count = 1;
  if (this->following_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->following_count(), target);
  }

  // uint32 follower_count = 2;
  if (this->follower_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->follower_count(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.Counts)
  return target;
}

size_t Counts::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.Counts)
  size_t total_size = 0;

  // uint32 following_count = 1;
  if (this->following_count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->following_count());
  }

  // uint32 follower_count = 2;
  if (this->follower_count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->follower_count());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Counts::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.Counts)
  GOOGLE_DCHECK_NE(&from, this);
  const Counts* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Counts>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.Counts)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.Counts)
    MergeFrom(*source);
  }
}

void Counts::MergeFrom(const Counts& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.Counts)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.following_count() != 0) {
    set_following_count(from.following_count());
  }
  if (from.follower_count() != 0) {
    set_follower_count(from.follower_count());
  }
}

void Counts::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.Counts)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Counts::CopyFrom(const Counts& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.Counts)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Counts::IsInitialized() const {
  return true;
}

void Counts::Swap(Counts* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Counts::InternalSwap(Counts* other) {
  std::swap(following_count_, other->following_count_);
  std::swap(follower_count_, other->follower_count_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Counts::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[5];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Counts

// uint32 following_count = 1;
void Counts::clear_following_count() {
  following_count_ = 0u;
}
::google::protobuf::uint32 Counts::following_count() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.Counts.following_count)
  return following_count_;
}
void Counts::set_following_count(::google::protobuf::uint32 value) {
  
  following_count_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.Counts.following_count)
}

// uint32 follower_count = 2;
void Counts::clear_follower_count() {
  follower_count_ = 0u;
}
::google::protobuf::uint32 Counts::follower_count() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.Counts.follower_count)
  return follower_count_;
}
void Counts::set_follower_count(::google::protobuf::uint32 value) {
  
  follower_count_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.Counts.follower_count)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int LoadMore_CreateAtAndUidKey::kLastCreateAtFieldNumber;
const int LoadMore_CreateAtAndUidKey::kLastUidFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

LoadMore_CreateAtAndUidKey::LoadMore_CreateAtAndUidKey()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.LoadMore.CreateAtAndUidKey)
}
LoadMore_CreateAtAndUidKey::LoadMore_CreateAtAndUidKey(const LoadMore_CreateAtAndUidKey& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&last_create_at_, &from.last_create_at_,
    reinterpret_cast<char*>(&last_uid_) -
    reinterpret_cast<char*>(&last_create_at_) + sizeof(last_uid_));
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.LoadMore.CreateAtAndUidKey)
}

void LoadMore_CreateAtAndUidKey::SharedCtor() {
  ::memset(&last_create_at_, 0, reinterpret_cast<char*>(&last_uid_) -
    reinterpret_cast<char*>(&last_create_at_) + sizeof(last_uid_));
  _cached_size_ = 0;
}

LoadMore_CreateAtAndUidKey::~LoadMore_CreateAtAndUidKey() {
  // @@protoc_insertion_point(destructor:ugc.friendship.LoadMore.CreateAtAndUidKey)
  SharedDtor();
}

void LoadMore_CreateAtAndUidKey::SharedDtor() {
}

void LoadMore_CreateAtAndUidKey::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* LoadMore_CreateAtAndUidKey::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[6].descriptor;
}

const LoadMore_CreateAtAndUidKey& LoadMore_CreateAtAndUidKey::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

LoadMore_CreateAtAndUidKey* LoadMore_CreateAtAndUidKey::New(::google::protobuf::Arena* arena) const {
  LoadMore_CreateAtAndUidKey* n = new LoadMore_CreateAtAndUidKey;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void LoadMore_CreateAtAndUidKey::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.LoadMore.CreateAtAndUidKey)
  ::memset(&last_create_at_, 0, reinterpret_cast<char*>(&last_uid_) -
    reinterpret_cast<char*>(&last_create_at_) + sizeof(last_uid_));
}

bool LoadMore_CreateAtAndUidKey::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.LoadMore.CreateAtAndUidKey)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint64 last_create_at = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &last_create_at_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint32 last_uid = 2;
      case 2: {
        if (tag == 16u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &last_uid_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.LoadMore.CreateAtAndUidKey)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.LoadMore.CreateAtAndUidKey)
  return false;
#undef DO_
}

void LoadMore_CreateAtAndUidKey::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.LoadMore.CreateAtAndUidKey)
  // uint64 last_create_at = 1;
  if (this->last_create_at() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->last_create_at(), output);
  }

  // uint32 last_uid = 2;
  if (this->last_uid() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->last_uid(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.LoadMore.CreateAtAndUidKey)
}

::google::protobuf::uint8* LoadMore_CreateAtAndUidKey::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.LoadMore.CreateAtAndUidKey)
  // uint64 last_create_at = 1;
  if (this->last_create_at() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->last_create_at(), target);
  }

  // uint32 last_uid = 2;
  if (this->last_uid() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->last_uid(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.LoadMore.CreateAtAndUidKey)
  return target;
}

size_t LoadMore_CreateAtAndUidKey::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.LoadMore.CreateAtAndUidKey)
  size_t total_size = 0;

  // uint64 last_create_at = 1;
  if (this->last_create_at() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->last_create_at());
  }

  // uint32 last_uid = 2;
  if (this->last_uid() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->last_uid());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void LoadMore_CreateAtAndUidKey::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.LoadMore.CreateAtAndUidKey)
  GOOGLE_DCHECK_NE(&from, this);
  const LoadMore_CreateAtAndUidKey* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const LoadMore_CreateAtAndUidKey>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.LoadMore.CreateAtAndUidKey)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.LoadMore.CreateAtAndUidKey)
    MergeFrom(*source);
  }
}

void LoadMore_CreateAtAndUidKey::MergeFrom(const LoadMore_CreateAtAndUidKey& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.LoadMore.CreateAtAndUidKey)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.last_create_at() != 0) {
    set_last_create_at(from.last_create_at());
  }
  if (from.last_uid() != 0) {
    set_last_uid(from.last_uid());
  }
}

void LoadMore_CreateAtAndUidKey::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.LoadMore.CreateAtAndUidKey)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LoadMore_CreateAtAndUidKey::CopyFrom(const LoadMore_CreateAtAndUidKey& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.LoadMore.CreateAtAndUidKey)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LoadMore_CreateAtAndUidKey::IsInitialized() const {
  return true;
}

void LoadMore_CreateAtAndUidKey::Swap(LoadMore_CreateAtAndUidKey* other) {
  if (other == this) return;
  InternalSwap(other);
}
void LoadMore_CreateAtAndUidKey::InternalSwap(LoadMore_CreateAtAndUidKey* other) {
  std::swap(last_create_at_, other->last_create_at_);
  std::swap(last_uid_, other->last_uid_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata LoadMore_CreateAtAndUidKey::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[6];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// LoadMore_CreateAtAndUidKey

// uint64 last_create_at = 1;
void LoadMore_CreateAtAndUidKey::clear_last_create_at() {
  last_create_at_ = GOOGLE_ULONGLONG(0);
}
::google::protobuf::uint64 LoadMore_CreateAtAndUidKey::last_create_at() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.LoadMore.CreateAtAndUidKey.last_create_at)
  return last_create_at_;
}
void LoadMore_CreateAtAndUidKey::set_last_create_at(::google::protobuf::uint64 value) {
  
  last_create_at_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.LoadMore.CreateAtAndUidKey.last_create_at)
}

// uint32 last_uid = 2;
void LoadMore_CreateAtAndUidKey::clear_last_uid() {
  last_uid_ = 0u;
}
::google::protobuf::uint32 LoadMore_CreateAtAndUidKey::last_uid() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.LoadMore.CreateAtAndUidKey.last_uid)
  return last_uid_;
}
void LoadMore_CreateAtAndUidKey::set_last_uid(::google::protobuf::uint32 value) {
  
  last_uid_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.LoadMore.CreateAtAndUidKey.last_uid)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int LoadMore_SequenceIdKey::kLastSequenceIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

LoadMore_SequenceIdKey::LoadMore_SequenceIdKey()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.LoadMore.SequenceIdKey)
}
LoadMore_SequenceIdKey::LoadMore_SequenceIdKey(const LoadMore_SequenceIdKey& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  last_sequence_id_ = from.last_sequence_id_;
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.LoadMore.SequenceIdKey)
}

void LoadMore_SequenceIdKey::SharedCtor() {
  last_sequence_id_ = GOOGLE_ULONGLONG(0);
  _cached_size_ = 0;
}

LoadMore_SequenceIdKey::~LoadMore_SequenceIdKey() {
  // @@protoc_insertion_point(destructor:ugc.friendship.LoadMore.SequenceIdKey)
  SharedDtor();
}

void LoadMore_SequenceIdKey::SharedDtor() {
}

void LoadMore_SequenceIdKey::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* LoadMore_SequenceIdKey::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[7].descriptor;
}

const LoadMore_SequenceIdKey& LoadMore_SequenceIdKey::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

LoadMore_SequenceIdKey* LoadMore_SequenceIdKey::New(::google::protobuf::Arena* arena) const {
  LoadMore_SequenceIdKey* n = new LoadMore_SequenceIdKey;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void LoadMore_SequenceIdKey::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.LoadMore.SequenceIdKey)
  last_sequence_id_ = GOOGLE_ULONGLONG(0);
}

bool LoadMore_SequenceIdKey::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.LoadMore.SequenceIdKey)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint64 last_sequence_id = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &last_sequence_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.LoadMore.SequenceIdKey)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.LoadMore.SequenceIdKey)
  return false;
#undef DO_
}

void LoadMore_SequenceIdKey::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.LoadMore.SequenceIdKey)
  // uint64 last_sequence_id = 1;
  if (this->last_sequence_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->last_sequence_id(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.LoadMore.SequenceIdKey)
}

::google::protobuf::uint8* LoadMore_SequenceIdKey::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.LoadMore.SequenceIdKey)
  // uint64 last_sequence_id = 1;
  if (this->last_sequence_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->last_sequence_id(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.LoadMore.SequenceIdKey)
  return target;
}

size_t LoadMore_SequenceIdKey::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.LoadMore.SequenceIdKey)
  size_t total_size = 0;

  // uint64 last_sequence_id = 1;
  if (this->last_sequence_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->last_sequence_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void LoadMore_SequenceIdKey::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.LoadMore.SequenceIdKey)
  GOOGLE_DCHECK_NE(&from, this);
  const LoadMore_SequenceIdKey* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const LoadMore_SequenceIdKey>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.LoadMore.SequenceIdKey)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.LoadMore.SequenceIdKey)
    MergeFrom(*source);
  }
}

void LoadMore_SequenceIdKey::MergeFrom(const LoadMore_SequenceIdKey& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.LoadMore.SequenceIdKey)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.last_sequence_id() != 0) {
    set_last_sequence_id(from.last_sequence_id());
  }
}

void LoadMore_SequenceIdKey::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.LoadMore.SequenceIdKey)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LoadMore_SequenceIdKey::CopyFrom(const LoadMore_SequenceIdKey& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.LoadMore.SequenceIdKey)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LoadMore_SequenceIdKey::IsInitialized() const {
  return true;
}

void LoadMore_SequenceIdKey::Swap(LoadMore_SequenceIdKey* other) {
  if (other == this) return;
  InternalSwap(other);
}
void LoadMore_SequenceIdKey::InternalSwap(LoadMore_SequenceIdKey* other) {
  std::swap(last_sequence_id_, other->last_sequence_id_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata LoadMore_SequenceIdKey::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[7];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// LoadMore_SequenceIdKey

// uint64 last_sequence_id = 1;
void LoadMore_SequenceIdKey::clear_last_sequence_id() {
  last_sequence_id_ = GOOGLE_ULONGLONG(0);
}
::google::protobuf::uint64 LoadMore_SequenceIdKey::last_sequence_id() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.LoadMore.SequenceIdKey.last_sequence_id)
  return last_sequence_id_;
}
void LoadMore_SequenceIdKey::set_last_sequence_id(::google::protobuf::uint64 value) {
  
  last_sequence_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.LoadMore.SequenceIdKey.last_sequence_id)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int LoadMore_IDKey::kLastObjectIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

LoadMore_IDKey::LoadMore_IDKey()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.LoadMore.IDKey)
}
LoadMore_IDKey::LoadMore_IDKey(const LoadMore_IDKey& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  last_object_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.last_object_id().size() > 0) {
    last_object_id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.last_object_id_);
  }
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.LoadMore.IDKey)
}

void LoadMore_IDKey::SharedCtor() {
  last_object_id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

LoadMore_IDKey::~LoadMore_IDKey() {
  // @@protoc_insertion_point(destructor:ugc.friendship.LoadMore.IDKey)
  SharedDtor();
}

void LoadMore_IDKey::SharedDtor() {
  last_object_id_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void LoadMore_IDKey::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* LoadMore_IDKey::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[8].descriptor;
}

const LoadMore_IDKey& LoadMore_IDKey::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

LoadMore_IDKey* LoadMore_IDKey::New(::google::protobuf::Arena* arena) const {
  LoadMore_IDKey* n = new LoadMore_IDKey;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void LoadMore_IDKey::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.LoadMore.IDKey)
  last_object_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool LoadMore_IDKey::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.LoadMore.IDKey)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string last_object_id = 1;
      case 1: {
        if (tag == 10u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_last_object_id()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->last_object_id().data(), this->last_object_id().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "ugc.friendship.LoadMore.IDKey.last_object_id"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.LoadMore.IDKey)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.LoadMore.IDKey)
  return false;
#undef DO_
}

void LoadMore_IDKey::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.LoadMore.IDKey)
  // string last_object_id = 1;
  if (this->last_object_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->last_object_id().data(), this->last_object_id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.friendship.LoadMore.IDKey.last_object_id");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->last_object_id(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.LoadMore.IDKey)
}

::google::protobuf::uint8* LoadMore_IDKey::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.LoadMore.IDKey)
  // string last_object_id = 1;
  if (this->last_object_id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->last_object_id().data(), this->last_object_id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "ugc.friendship.LoadMore.IDKey.last_object_id");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->last_object_id(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.LoadMore.IDKey)
  return target;
}

size_t LoadMore_IDKey::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.LoadMore.IDKey)
  size_t total_size = 0;

  // string last_object_id = 1;
  if (this->last_object_id().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->last_object_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void LoadMore_IDKey::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.LoadMore.IDKey)
  GOOGLE_DCHECK_NE(&from, this);
  const LoadMore_IDKey* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const LoadMore_IDKey>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.LoadMore.IDKey)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.LoadMore.IDKey)
    MergeFrom(*source);
  }
}

void LoadMore_IDKey::MergeFrom(const LoadMore_IDKey& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.LoadMore.IDKey)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.last_object_id().size() > 0) {

    last_object_id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.last_object_id_);
  }
}

void LoadMore_IDKey::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.LoadMore.IDKey)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LoadMore_IDKey::CopyFrom(const LoadMore_IDKey& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.LoadMore.IDKey)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LoadMore_IDKey::IsInitialized() const {
  return true;
}

void LoadMore_IDKey::Swap(LoadMore_IDKey* other) {
  if (other == this) return;
  InternalSwap(other);
}
void LoadMore_IDKey::InternalSwap(LoadMore_IDKey* other) {
  last_object_id_.Swap(&other->last_object_id_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata LoadMore_IDKey::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[8];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// LoadMore_IDKey

// string last_object_id = 1;
void LoadMore_IDKey::clear_last_object_id() {
  last_object_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& LoadMore_IDKey::last_object_id() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.LoadMore.IDKey.last_object_id)
  return last_object_id_.GetNoArena();
}
void LoadMore_IDKey::set_last_object_id(const ::std::string& value) {
  
  last_object_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.friendship.LoadMore.IDKey.last_object_id)
}
#if LANG_CXX11
void LoadMore_IDKey::set_last_object_id(::std::string&& value) {
  
  last_object_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.friendship.LoadMore.IDKey.last_object_id)
}
#endif
void LoadMore_IDKey::set_last_object_id(const char* value) {
  
  last_object_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.friendship.LoadMore.IDKey.last_object_id)
}
void LoadMore_IDKey::set_last_object_id(const char* value, size_t size) {
  
  last_object_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.friendship.LoadMore.IDKey.last_object_id)
}
::std::string* LoadMore_IDKey::mutable_last_object_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.friendship.LoadMore.IDKey.last_object_id)
  return last_object_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* LoadMore_IDKey::release_last_object_id() {
  // @@protoc_insertion_point(field_release:ugc.friendship.LoadMore.IDKey.last_object_id)
  
  return last_object_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void LoadMore_IDKey::set_allocated_last_object_id(::std::string* last_object_id) {
  if (last_object_id != NULL) {
    
  } else {
    
  }
  last_object_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), last_object_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.friendship.LoadMore.IDKey.last_object_id)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int LoadMore::kSortFieldNumber;
const int LoadMore::kCreateAtAndUidKeyFieldNumber;
const int LoadMore::kIdKeyFieldNumber;
const int LoadMore::kSequenceIdKeyFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

LoadMore::LoadMore()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.LoadMore)
}
LoadMore::LoadMore(const LoadMore& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  sort_ = from.sort_;
  clear_has_order_by();
  switch (from.order_by_case()) {
    case kCreateAtAndUidKey: {
      mutable_create_at_and_uid_key()->::ugc::friendship::LoadMore_CreateAtAndUidKey::MergeFrom(from.create_at_and_uid_key());
      break;
    }
    case kIdKey: {
      mutable_id_key()->::ugc::friendship::LoadMore_IDKey::MergeFrom(from.id_key());
      break;
    }
    case kSequenceIdKey: {
      mutable_sequence_id_key()->::ugc::friendship::LoadMore_SequenceIdKey::MergeFrom(from.sequence_id_key());
      break;
    }
    case ORDER_BY_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.LoadMore)
}

void LoadMore::SharedCtor() {
  sort_ = 0;
  clear_has_order_by();
  _cached_size_ = 0;
}

LoadMore::~LoadMore() {
  // @@protoc_insertion_point(destructor:ugc.friendship.LoadMore)
  SharedDtor();
}

void LoadMore::SharedDtor() {
  if (has_order_by()) {
    clear_order_by();
  }
}

void LoadMore::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* LoadMore::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[9].descriptor;
}

const LoadMore& LoadMore::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

LoadMore* LoadMore::New(::google::protobuf::Arena* arena) const {
  LoadMore* n = new LoadMore;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void LoadMore::clear_order_by() {
// @@protoc_insertion_point(one_of_clear_start:ugc.friendship.LoadMore)
  switch (order_by_case()) {
    case kCreateAtAndUidKey: {
      delete order_by_.create_at_and_uid_key_;
      break;
    }
    case kIdKey: {
      delete order_by_.id_key_;
      break;
    }
    case kSequenceIdKey: {
      delete order_by_.sequence_id_key_;
      break;
    }
    case ORDER_BY_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = ORDER_BY_NOT_SET;
}


void LoadMore::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.LoadMore)
  sort_ = 0;
  clear_order_by();
}

bool LoadMore::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.LoadMore)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .ugc.friendship.LoadMore.Sort sort = 1;
      case 1: {
        if (tag == 8u) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_sort(static_cast< ::ugc::friendship::LoadMore_Sort >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .ugc.friendship.LoadMore.CreateAtAndUidKey create_at_and_uid_key = 2;
      case 2: {
        if (tag == 18u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_create_at_and_uid_key()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .ugc.friendship.LoadMore.IDKey id_key = 3;
      case 3: {
        if (tag == 26u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_id_key()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .ugc.friendship.LoadMore.SequenceIdKey sequence_id_key = 4;
      case 4: {
        if (tag == 34u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_sequence_id_key()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.LoadMore)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.LoadMore)
  return false;
#undef DO_
}

void LoadMore::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.LoadMore)
  // .ugc.friendship.LoadMore.Sort sort = 1;
  if (this->sort() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->sort(), output);
  }

  // .ugc.friendship.LoadMore.CreateAtAndUidKey create_at_and_uid_key = 2;
  if (has_create_at_and_uid_key()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *order_by_.create_at_and_uid_key_, output);
  }

  // .ugc.friendship.LoadMore.IDKey id_key = 3;
  if (has_id_key()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *order_by_.id_key_, output);
  }

  // .ugc.friendship.LoadMore.SequenceIdKey sequence_id_key = 4;
  if (has_sequence_id_key()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *order_by_.sequence_id_key_, output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.LoadMore)
}

::google::protobuf::uint8* LoadMore::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.LoadMore)
  // .ugc.friendship.LoadMore.Sort sort = 1;
  if (this->sort() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->sort(), target);
  }

  // .ugc.friendship.LoadMore.CreateAtAndUidKey create_at_and_uid_key = 2;
  if (has_create_at_and_uid_key()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *order_by_.create_at_and_uid_key_, false, target);
  }

  // .ugc.friendship.LoadMore.IDKey id_key = 3;
  if (has_id_key()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *order_by_.id_key_, false, target);
  }

  // .ugc.friendship.LoadMore.SequenceIdKey sequence_id_key = 4;
  if (has_sequence_id_key()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *order_by_.sequence_id_key_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.LoadMore)
  return target;
}

size_t LoadMore::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.LoadMore)
  size_t total_size = 0;

  // .ugc.friendship.LoadMore.Sort sort = 1;
  if (this->sort() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->sort());
  }

  switch (order_by_case()) {
    // .ugc.friendship.LoadMore.CreateAtAndUidKey create_at_and_uid_key = 2;
    case kCreateAtAndUidKey: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *order_by_.create_at_and_uid_key_);
      break;
    }
    // .ugc.friendship.LoadMore.IDKey id_key = 3;
    case kIdKey: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *order_by_.id_key_);
      break;
    }
    // .ugc.friendship.LoadMore.SequenceIdKey sequence_id_key = 4;
    case kSequenceIdKey: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          *order_by_.sequence_id_key_);
      break;
    }
    case ORDER_BY_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void LoadMore::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.LoadMore)
  GOOGLE_DCHECK_NE(&from, this);
  const LoadMore* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const LoadMore>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.LoadMore)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.LoadMore)
    MergeFrom(*source);
  }
}

void LoadMore::MergeFrom(const LoadMore& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.LoadMore)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.sort() != 0) {
    set_sort(from.sort());
  }
  switch (from.order_by_case()) {
    case kCreateAtAndUidKey: {
      mutable_create_at_and_uid_key()->::ugc::friendship::LoadMore_CreateAtAndUidKey::MergeFrom(from.create_at_and_uid_key());
      break;
    }
    case kIdKey: {
      mutable_id_key()->::ugc::friendship::LoadMore_IDKey::MergeFrom(from.id_key());
      break;
    }
    case kSequenceIdKey: {
      mutable_sequence_id_key()->::ugc::friendship::LoadMore_SequenceIdKey::MergeFrom(from.sequence_id_key());
      break;
    }
    case ORDER_BY_NOT_SET: {
      break;
    }
  }
}

void LoadMore::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.LoadMore)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LoadMore::CopyFrom(const LoadMore& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.LoadMore)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LoadMore::IsInitialized() const {
  return true;
}

void LoadMore::Swap(LoadMore* other) {
  if (other == this) return;
  InternalSwap(other);
}
void LoadMore::InternalSwap(LoadMore* other) {
  std::swap(sort_, other->sort_);
  std::swap(order_by_, other->order_by_);
  std::swap(_oneof_case_[0], other->_oneof_case_[0]);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata LoadMore::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[9];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// LoadMore

// .ugc.friendship.LoadMore.Sort sort = 1;
void LoadMore::clear_sort() {
  sort_ = 0;
}
::ugc::friendship::LoadMore_Sort LoadMore::sort() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.LoadMore.sort)
  return static_cast< ::ugc::friendship::LoadMore_Sort >(sort_);
}
void LoadMore::set_sort(::ugc::friendship::LoadMore_Sort value) {
  
  sort_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.LoadMore.sort)
}

// .ugc.friendship.LoadMore.CreateAtAndUidKey create_at_and_uid_key = 2;
bool LoadMore::has_create_at_and_uid_key() const {
  return order_by_case() == kCreateAtAndUidKey;
}
void LoadMore::set_has_create_at_and_uid_key() {
  _oneof_case_[0] = kCreateAtAndUidKey;
}
void LoadMore::clear_create_at_and_uid_key() {
  if (has_create_at_and_uid_key()) {
    delete order_by_.create_at_and_uid_key_;
    clear_has_order_by();
  }
}
 const ::ugc::friendship::LoadMore_CreateAtAndUidKey& LoadMore::create_at_and_uid_key() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.LoadMore.create_at_and_uid_key)
  return has_create_at_and_uid_key()
      ? *order_by_.create_at_and_uid_key_
      : ::ugc::friendship::LoadMore_CreateAtAndUidKey::default_instance();
}
::ugc::friendship::LoadMore_CreateAtAndUidKey* LoadMore::mutable_create_at_and_uid_key() {
  if (!has_create_at_and_uid_key()) {
    clear_order_by();
    set_has_create_at_and_uid_key();
    order_by_.create_at_and_uid_key_ = new ::ugc::friendship::LoadMore_CreateAtAndUidKey;
  }
  // @@protoc_insertion_point(field_mutable:ugc.friendship.LoadMore.create_at_and_uid_key)
  return order_by_.create_at_and_uid_key_;
}
::ugc::friendship::LoadMore_CreateAtAndUidKey* LoadMore::release_create_at_and_uid_key() {
  // @@protoc_insertion_point(field_release:ugc.friendship.LoadMore.create_at_and_uid_key)
  if (has_create_at_and_uid_key()) {
    clear_has_order_by();
    ::ugc::friendship::LoadMore_CreateAtAndUidKey* temp = order_by_.create_at_and_uid_key_;
    order_by_.create_at_and_uid_key_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void LoadMore::set_allocated_create_at_and_uid_key(::ugc::friendship::LoadMore_CreateAtAndUidKey* create_at_and_uid_key) {
  clear_order_by();
  if (create_at_and_uid_key) {
    set_has_create_at_and_uid_key();
    order_by_.create_at_and_uid_key_ = create_at_and_uid_key;
  }
  // @@protoc_insertion_point(field_set_allocated:ugc.friendship.LoadMore.create_at_and_uid_key)
}

// .ugc.friendship.LoadMore.IDKey id_key = 3;
bool LoadMore::has_id_key() const {
  return order_by_case() == kIdKey;
}
void LoadMore::set_has_id_key() {
  _oneof_case_[0] = kIdKey;
}
void LoadMore::clear_id_key() {
  if (has_id_key()) {
    delete order_by_.id_key_;
    clear_has_order_by();
  }
}
 const ::ugc::friendship::LoadMore_IDKey& LoadMore::id_key() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.LoadMore.id_key)
  return has_id_key()
      ? *order_by_.id_key_
      : ::ugc::friendship::LoadMore_IDKey::default_instance();
}
::ugc::friendship::LoadMore_IDKey* LoadMore::mutable_id_key() {
  if (!has_id_key()) {
    clear_order_by();
    set_has_id_key();
    order_by_.id_key_ = new ::ugc::friendship::LoadMore_IDKey;
  }
  // @@protoc_insertion_point(field_mutable:ugc.friendship.LoadMore.id_key)
  return order_by_.id_key_;
}
::ugc::friendship::LoadMore_IDKey* LoadMore::release_id_key() {
  // @@protoc_insertion_point(field_release:ugc.friendship.LoadMore.id_key)
  if (has_id_key()) {
    clear_has_order_by();
    ::ugc::friendship::LoadMore_IDKey* temp = order_by_.id_key_;
    order_by_.id_key_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void LoadMore::set_allocated_id_key(::ugc::friendship::LoadMore_IDKey* id_key) {
  clear_order_by();
  if (id_key) {
    set_has_id_key();
    order_by_.id_key_ = id_key;
  }
  // @@protoc_insertion_point(field_set_allocated:ugc.friendship.LoadMore.id_key)
}

// .ugc.friendship.LoadMore.SequenceIdKey sequence_id_key = 4;
bool LoadMore::has_sequence_id_key() const {
  return order_by_case() == kSequenceIdKey;
}
void LoadMore::set_has_sequence_id_key() {
  _oneof_case_[0] = kSequenceIdKey;
}
void LoadMore::clear_sequence_id_key() {
  if (has_sequence_id_key()) {
    delete order_by_.sequence_id_key_;
    clear_has_order_by();
  }
}
 const ::ugc::friendship::LoadMore_SequenceIdKey& LoadMore::sequence_id_key() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.LoadMore.sequence_id_key)
  return has_sequence_id_key()
      ? *order_by_.sequence_id_key_
      : ::ugc::friendship::LoadMore_SequenceIdKey::default_instance();
}
::ugc::friendship::LoadMore_SequenceIdKey* LoadMore::mutable_sequence_id_key() {
  if (!has_sequence_id_key()) {
    clear_order_by();
    set_has_sequence_id_key();
    order_by_.sequence_id_key_ = new ::ugc::friendship::LoadMore_SequenceIdKey;
  }
  // @@protoc_insertion_point(field_mutable:ugc.friendship.LoadMore.sequence_id_key)
  return order_by_.sequence_id_key_;
}
::ugc::friendship::LoadMore_SequenceIdKey* LoadMore::release_sequence_id_key() {
  // @@protoc_insertion_point(field_release:ugc.friendship.LoadMore.sequence_id_key)
  if (has_sequence_id_key()) {
    clear_has_order_by();
    ::ugc::friendship::LoadMore_SequenceIdKey* temp = order_by_.sequence_id_key_;
    order_by_.sequence_id_key_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
void LoadMore::set_allocated_sequence_id_key(::ugc::friendship::LoadMore_SequenceIdKey* sequence_id_key) {
  clear_order_by();
  if (sequence_id_key) {
    set_has_sequence_id_key();
    order_by_.sequence_id_key_ = sequence_id_key;
  }
  // @@protoc_insertion_point(field_set_allocated:ugc.friendship.LoadMore.sequence_id_key)
}

bool LoadMore::has_order_by() const {
  return order_by_case() != ORDER_BY_NOT_SET;
}
void LoadMore::clear_has_order_by() {
  _oneof_case_[0] = ORDER_BY_NOT_SET;
}
LoadMore::OrderByCase LoadMore::order_by_case() const {
  return LoadMore::OrderByCase(_oneof_case_[0]);
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GetFollowingListReq::kFollowedByUidFieldNumber;
const int GetFollowingListReq::kFollowingUidFieldNumber;
const int GetFollowingListReq::kIncludingDroppedFieldNumber;
const int GetFollowingListReq::kLoadMoreFieldNumber;
const int GetFollowingListReq::kCountFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GetFollowingListReq::GetFollowingListReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.GetFollowingListReq)
}
GetFollowingListReq::GetFollowingListReq(const GetFollowingListReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_load_more()) {
    load_more_ = new ::ugc::friendship::LoadMore(*from.load_more_);
  } else {
    load_more_ = NULL;
  }
  ::memcpy(&including_dropped_, &from.including_dropped_,
    reinterpret_cast<char*>(&count_) -
    reinterpret_cast<char*>(&including_dropped_) + sizeof(count_));
  clear_has_request_type();
  switch (from.request_type_case()) {
    case kFollowedByUid: {
      set_followed_by_uid(from.followed_by_uid());
      break;
    }
    case kFollowingUid: {
      set_following_uid(from.following_uid());
      break;
    }
    case REQUEST_TYPE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.GetFollowingListReq)
}

void GetFollowingListReq::SharedCtor() {
  ::memset(&load_more_, 0, reinterpret_cast<char*>(&count_) -
    reinterpret_cast<char*>(&load_more_) + sizeof(count_));
  clear_has_request_type();
  _cached_size_ = 0;
}

GetFollowingListReq::~GetFollowingListReq() {
  // @@protoc_insertion_point(destructor:ugc.friendship.GetFollowingListReq)
  SharedDtor();
}

void GetFollowingListReq::SharedDtor() {
  if (this != internal_default_instance()) {
    delete load_more_;
  }
  if (has_request_type()) {
    clear_request_type();
  }
}

void GetFollowingListReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetFollowingListReq::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[10].descriptor;
}

const GetFollowingListReq& GetFollowingListReq::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

GetFollowingListReq* GetFollowingListReq::New(::google::protobuf::Arena* arena) const {
  GetFollowingListReq* n = new GetFollowingListReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void GetFollowingListReq::clear_request_type() {
// @@protoc_insertion_point(one_of_clear_start:ugc.friendship.GetFollowingListReq)
  switch (request_type_case()) {
    case kFollowedByUid: {
      // No need to clear
      break;
    }
    case kFollowingUid: {
      // No need to clear
      break;
    }
    case REQUEST_TYPE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = REQUEST_TYPE_NOT_SET;
}


void GetFollowingListReq::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.GetFollowingListReq)
  if (GetArenaNoVirtual() == NULL && load_more_ != NULL) {
    delete load_more_;
  }
  load_more_ = NULL;
  ::memset(&including_dropped_, 0, reinterpret_cast<char*>(&count_) -
    reinterpret_cast<char*>(&including_dropped_) + sizeof(count_));
  clear_request_type();
}

bool GetFollowingListReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.GetFollowingListReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 followed_by_uid = 1;
      case 1: {
        if (tag == 8u) {
          clear_request_type();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &request_type_.followed_by_uid_)));
          set_has_followed_by_uid();
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint32 following_uid = 2;
      case 2: {
        if (tag == 16u) {
          clear_request_type();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &request_type_.following_uid_)));
          set_has_following_uid();
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool including_dropped = 3;
      case 3: {
        if (tag == 24u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &including_dropped_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .ugc.friendship.LoadMore load_more = 10;
      case 10: {
        if (tag == 82u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_load_more()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint32 count = 11;
      case 11: {
        if (tag == 88u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.GetFollowingListReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.GetFollowingListReq)
  return false;
#undef DO_
}

void GetFollowingListReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.GetFollowingListReq)
  // uint32 followed_by_uid = 1;
  if (has_followed_by_uid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->followed_by_uid(), output);
  }

  // uint32 following_uid = 2;
  if (has_following_uid()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->following_uid(), output);
  }

  // bool including_dropped = 3;
  if (this->including_dropped() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(3, this->including_dropped(), output);
  }

  // .ugc.friendship.LoadMore load_more = 10;
  if (this->has_load_more()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, *this->load_more_, output);
  }

  // uint32 count = 11;
  if (this->count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(11, this->count(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.GetFollowingListReq)
}

::google::protobuf::uint8* GetFollowingListReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.GetFollowingListReq)
  // uint32 followed_by_uid = 1;
  if (has_followed_by_uid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->followed_by_uid(), target);
  }

  // uint32 following_uid = 2;
  if (has_following_uid()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->following_uid(), target);
  }

  // bool including_dropped = 3;
  if (this->including_dropped() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(3, this->including_dropped(), target);
  }

  // .ugc.friendship.LoadMore load_more = 10;
  if (this->has_load_more()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        10, *this->load_more_, false, target);
  }

  // uint32 count = 11;
  if (this->count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(11, this->count(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.GetFollowingListReq)
  return target;
}

size_t GetFollowingListReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.GetFollowingListReq)
  size_t total_size = 0;

  // .ugc.friendship.LoadMore load_more = 10;
  if (this->has_load_more()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->load_more_);
  }

  // bool including_dropped = 3;
  if (this->including_dropped() != 0) {
    total_size += 1 + 1;
  }

  // uint32 count = 11;
  if (this->count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->count());
  }

  switch (request_type_case()) {
    // uint32 followed_by_uid = 1;
    case kFollowedByUid: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->followed_by_uid());
      break;
    }
    // uint32 following_uid = 2;
    case kFollowingUid: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt32Size(
          this->following_uid());
      break;
    }
    case REQUEST_TYPE_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetFollowingListReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.GetFollowingListReq)
  GOOGLE_DCHECK_NE(&from, this);
  const GetFollowingListReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GetFollowingListReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.GetFollowingListReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.GetFollowingListReq)
    MergeFrom(*source);
  }
}

void GetFollowingListReq::MergeFrom(const GetFollowingListReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.GetFollowingListReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_load_more()) {
    mutable_load_more()->::ugc::friendship::LoadMore::MergeFrom(from.load_more());
  }
  if (from.including_dropped() != 0) {
    set_including_dropped(from.including_dropped());
  }
  if (from.count() != 0) {
    set_count(from.count());
  }
  switch (from.request_type_case()) {
    case kFollowedByUid: {
      set_followed_by_uid(from.followed_by_uid());
      break;
    }
    case kFollowingUid: {
      set_following_uid(from.following_uid());
      break;
    }
    case REQUEST_TYPE_NOT_SET: {
      break;
    }
  }
}

void GetFollowingListReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.GetFollowingListReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetFollowingListReq::CopyFrom(const GetFollowingListReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.GetFollowingListReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetFollowingListReq::IsInitialized() const {
  return true;
}

void GetFollowingListReq::Swap(GetFollowingListReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void GetFollowingListReq::InternalSwap(GetFollowingListReq* other) {
  std::swap(load_more_, other->load_more_);
  std::swap(including_dropped_, other->including_dropped_);
  std::swap(count_, other->count_);
  std::swap(request_type_, other->request_type_);
  std::swap(_oneof_case_[0], other->_oneof_case_[0]);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata GetFollowingListReq::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[10];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// GetFollowingListReq

// uint32 followed_by_uid = 1;
bool GetFollowingListReq::has_followed_by_uid() const {
  return request_type_case() == kFollowedByUid;
}
void GetFollowingListReq::set_has_followed_by_uid() {
  _oneof_case_[0] = kFollowedByUid;
}
void GetFollowingListReq::clear_followed_by_uid() {
  if (has_followed_by_uid()) {
    request_type_.followed_by_uid_ = 0u;
    clear_has_request_type();
  }
}
::google::protobuf::uint32 GetFollowingListReq::followed_by_uid() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.GetFollowingListReq.followed_by_uid)
  if (has_followed_by_uid()) {
    return request_type_.followed_by_uid_;
  }
  return 0u;
}
void GetFollowingListReq::set_followed_by_uid(::google::protobuf::uint32 value) {
  if (!has_followed_by_uid()) {
    clear_request_type();
    set_has_followed_by_uid();
  }
  request_type_.followed_by_uid_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.GetFollowingListReq.followed_by_uid)
}

// uint32 following_uid = 2;
bool GetFollowingListReq::has_following_uid() const {
  return request_type_case() == kFollowingUid;
}
void GetFollowingListReq::set_has_following_uid() {
  _oneof_case_[0] = kFollowingUid;
}
void GetFollowingListReq::clear_following_uid() {
  if (has_following_uid()) {
    request_type_.following_uid_ = 0u;
    clear_has_request_type();
  }
}
::google::protobuf::uint32 GetFollowingListReq::following_uid() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.GetFollowingListReq.following_uid)
  if (has_following_uid()) {
    return request_type_.following_uid_;
  }
  return 0u;
}
void GetFollowingListReq::set_following_uid(::google::protobuf::uint32 value) {
  if (!has_following_uid()) {
    clear_request_type();
    set_has_following_uid();
  }
  request_type_.following_uid_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.GetFollowingListReq.following_uid)
}

// bool including_dropped = 3;
void GetFollowingListReq::clear_including_dropped() {
  including_dropped_ = false;
}
bool GetFollowingListReq::including_dropped() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.GetFollowingListReq.including_dropped)
  return including_dropped_;
}
void GetFollowingListReq::set_including_dropped(bool value) {
  
  including_dropped_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.GetFollowingListReq.including_dropped)
}

// .ugc.friendship.LoadMore load_more = 10;
bool GetFollowingListReq::has_load_more() const {
  return this != internal_default_instance() && load_more_ != NULL;
}
void GetFollowingListReq::clear_load_more() {
  if (GetArenaNoVirtual() == NULL && load_more_ != NULL) delete load_more_;
  load_more_ = NULL;
}
const ::ugc::friendship::LoadMore& GetFollowingListReq::load_more() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.GetFollowingListReq.load_more)
  return load_more_ != NULL ? *load_more_
                         : *::ugc::friendship::LoadMore::internal_default_instance();
}
::ugc::friendship::LoadMore* GetFollowingListReq::mutable_load_more() {
  
  if (load_more_ == NULL) {
    load_more_ = new ::ugc::friendship::LoadMore;
  }
  // @@protoc_insertion_point(field_mutable:ugc.friendship.GetFollowingListReq.load_more)
  return load_more_;
}
::ugc::friendship::LoadMore* GetFollowingListReq::release_load_more() {
  // @@protoc_insertion_point(field_release:ugc.friendship.GetFollowingListReq.load_more)
  
  ::ugc::friendship::LoadMore* temp = load_more_;
  load_more_ = NULL;
  return temp;
}
void GetFollowingListReq::set_allocated_load_more(::ugc::friendship::LoadMore* load_more) {
  delete load_more_;
  load_more_ = load_more;
  if (load_more) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:ugc.friendship.GetFollowingListReq.load_more)
}

// uint32 count = 11;
void GetFollowingListReq::clear_count() {
  count_ = 0u;
}
::google::protobuf::uint32 GetFollowingListReq::count() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.GetFollowingListReq.count)
  return count_;
}
void GetFollowingListReq::set_count(::google::protobuf::uint32 value) {
  
  count_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.GetFollowingListReq.count)
}

bool GetFollowingListReq::has_request_type() const {
  return request_type_case() != REQUEST_TYPE_NOT_SET;
}
void GetFollowingListReq::clear_has_request_type() {
  _oneof_case_[0] = REQUEST_TYPE_NOT_SET;
}
GetFollowingListReq::RequestTypeCase GetFollowingListReq::request_type_case() const {
  return GetFollowingListReq::RequestTypeCase(_oneof_case_[0]);
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GetFollowingListResp::kFollowingListFieldNumber;
const int GetFollowingListResp::kLoadMoreFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GetFollowingListResp::GetFollowingListResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.GetFollowingListResp)
}
GetFollowingListResp::GetFollowingListResp(const GetFollowingListResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      following_list_(from.following_list_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_load_more()) {
    load_more_ = new ::ugc::friendship::LoadMore(*from.load_more_);
  } else {
    load_more_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.GetFollowingListResp)
}

void GetFollowingListResp::SharedCtor() {
  load_more_ = NULL;
  _cached_size_ = 0;
}

GetFollowingListResp::~GetFollowingListResp() {
  // @@protoc_insertion_point(destructor:ugc.friendship.GetFollowingListResp)
  SharedDtor();
}

void GetFollowingListResp::SharedDtor() {
  if (this != internal_default_instance()) {
    delete load_more_;
  }
}

void GetFollowingListResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetFollowingListResp::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[11].descriptor;
}

const GetFollowingListResp& GetFollowingListResp::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

GetFollowingListResp* GetFollowingListResp::New(::google::protobuf::Arena* arena) const {
  GetFollowingListResp* n = new GetFollowingListResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void GetFollowingListResp::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.GetFollowingListResp)
  following_list_.Clear();
  if (GetArenaNoVirtual() == NULL && load_more_ != NULL) {
    delete load_more_;
  }
  load_more_ = NULL;
}

bool GetFollowingListResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.GetFollowingListResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .ugc.friendship.Following following_list = 1;
      case 1: {
        if (tag == 10u) {
          DO_(input->IncrementRecursionDepth());
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_following_list()));
        } else {
          goto handle_unusual;
        }
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // .ugc.friendship.LoadMore load_more = 2;
      case 2: {
        if (tag == 18u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_load_more()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.GetFollowingListResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.GetFollowingListResp)
  return false;
#undef DO_
}

void GetFollowingListResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.GetFollowingListResp)
  // repeated .ugc.friendship.Following following_list = 1;
  for (unsigned int i = 0, n = this->following_list_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->following_list(i), output);
  }

  // .ugc.friendship.LoadMore load_more = 2;
  if (this->has_load_more()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->load_more_, output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.GetFollowingListResp)
}

::google::protobuf::uint8* GetFollowingListResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.GetFollowingListResp)
  // repeated .ugc.friendship.Following following_list = 1;
  for (unsigned int i = 0, n = this->following_list_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, this->following_list(i), false, target);
  }

  // .ugc.friendship.LoadMore load_more = 2;
  if (this->has_load_more()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->load_more_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.GetFollowingListResp)
  return target;
}

size_t GetFollowingListResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.GetFollowingListResp)
  size_t total_size = 0;

  // repeated .ugc.friendship.Following following_list = 1;
  {
    unsigned int count = this->following_list_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->following_list(i));
    }
  }

  // .ugc.friendship.LoadMore load_more = 2;
  if (this->has_load_more()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->load_more_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetFollowingListResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.GetFollowingListResp)
  GOOGLE_DCHECK_NE(&from, this);
  const GetFollowingListResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GetFollowingListResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.GetFollowingListResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.GetFollowingListResp)
    MergeFrom(*source);
  }
}

void GetFollowingListResp::MergeFrom(const GetFollowingListResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.GetFollowingListResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  following_list_.MergeFrom(from.following_list_);
  if (from.has_load_more()) {
    mutable_load_more()->::ugc::friendship::LoadMore::MergeFrom(from.load_more());
  }
}

void GetFollowingListResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.GetFollowingListResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetFollowingListResp::CopyFrom(const GetFollowingListResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.GetFollowingListResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetFollowingListResp::IsInitialized() const {
  return true;
}

void GetFollowingListResp::Swap(GetFollowingListResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void GetFollowingListResp::InternalSwap(GetFollowingListResp* other) {
  following_list_.UnsafeArenaSwap(&other->following_list_);
  std::swap(load_more_, other->load_more_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata GetFollowingListResp::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[11];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// GetFollowingListResp

// repeated .ugc.friendship.Following following_list = 1;
int GetFollowingListResp::following_list_size() const {
  return following_list_.size();
}
void GetFollowingListResp::clear_following_list() {
  following_list_.Clear();
}
const ::ugc::friendship::Following& GetFollowingListResp::following_list(int index) const {
  // @@protoc_insertion_point(field_get:ugc.friendship.GetFollowingListResp.following_list)
  return following_list_.Get(index);
}
::ugc::friendship::Following* GetFollowingListResp::mutable_following_list(int index) {
  // @@protoc_insertion_point(field_mutable:ugc.friendship.GetFollowingListResp.following_list)
  return following_list_.Mutable(index);
}
::ugc::friendship::Following* GetFollowingListResp::add_following_list() {
  // @@protoc_insertion_point(field_add:ugc.friendship.GetFollowingListResp.following_list)
  return following_list_.Add();
}
::google::protobuf::RepeatedPtrField< ::ugc::friendship::Following >*
GetFollowingListResp::mutable_following_list() {
  // @@protoc_insertion_point(field_mutable_list:ugc.friendship.GetFollowingListResp.following_list)
  return &following_list_;
}
const ::google::protobuf::RepeatedPtrField< ::ugc::friendship::Following >&
GetFollowingListResp::following_list() const {
  // @@protoc_insertion_point(field_list:ugc.friendship.GetFollowingListResp.following_list)
  return following_list_;
}

// .ugc.friendship.LoadMore load_more = 2;
bool GetFollowingListResp::has_load_more() const {
  return this != internal_default_instance() && load_more_ != NULL;
}
void GetFollowingListResp::clear_load_more() {
  if (GetArenaNoVirtual() == NULL && load_more_ != NULL) delete load_more_;
  load_more_ = NULL;
}
const ::ugc::friendship::LoadMore& GetFollowingListResp::load_more() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.GetFollowingListResp.load_more)
  return load_more_ != NULL ? *load_more_
                         : *::ugc::friendship::LoadMore::internal_default_instance();
}
::ugc::friendship::LoadMore* GetFollowingListResp::mutable_load_more() {
  
  if (load_more_ == NULL) {
    load_more_ = new ::ugc::friendship::LoadMore;
  }
  // @@protoc_insertion_point(field_mutable:ugc.friendship.GetFollowingListResp.load_more)
  return load_more_;
}
::ugc::friendship::LoadMore* GetFollowingListResp::release_load_more() {
  // @@protoc_insertion_point(field_release:ugc.friendship.GetFollowingListResp.load_more)
  
  ::ugc::friendship::LoadMore* temp = load_more_;
  load_more_ = NULL;
  return temp;
}
void GetFollowingListResp::set_allocated_load_more(::ugc::friendship::LoadMore* load_more) {
  delete load_more_;
  load_more_ = load_more;
  if (load_more) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:ugc.friendship.GetFollowingListResp.load_more)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GetBiFollowingReq::kUidAFieldNumber;
const int GetBiFollowingReq::kUidBFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GetBiFollowingReq::GetBiFollowingReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.GetBiFollowingReq)
}
GetBiFollowingReq::GetBiFollowingReq(const GetBiFollowingReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&uid_a_, &from.uid_a_,
    reinterpret_cast<char*>(&uid_b_) -
    reinterpret_cast<char*>(&uid_a_) + sizeof(uid_b_));
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.GetBiFollowingReq)
}

void GetBiFollowingReq::SharedCtor() {
  ::memset(&uid_a_, 0, reinterpret_cast<char*>(&uid_b_) -
    reinterpret_cast<char*>(&uid_a_) + sizeof(uid_b_));
  _cached_size_ = 0;
}

GetBiFollowingReq::~GetBiFollowingReq() {
  // @@protoc_insertion_point(destructor:ugc.friendship.GetBiFollowingReq)
  SharedDtor();
}

void GetBiFollowingReq::SharedDtor() {
}

void GetBiFollowingReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetBiFollowingReq::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[12].descriptor;
}

const GetBiFollowingReq& GetBiFollowingReq::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

GetBiFollowingReq* GetBiFollowingReq::New(::google::protobuf::Arena* arena) const {
  GetBiFollowingReq* n = new GetBiFollowingReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void GetBiFollowingReq::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.GetBiFollowingReq)
  ::memset(&uid_a_, 0, reinterpret_cast<char*>(&uid_b_) -
    reinterpret_cast<char*>(&uid_a_) + sizeof(uid_b_));
}

bool GetBiFollowingReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.GetBiFollowingReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 uid_a = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &uid_a_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint32 uid_b = 2;
      case 2: {
        if (tag == 16u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &uid_b_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.GetBiFollowingReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.GetBiFollowingReq)
  return false;
#undef DO_
}

void GetBiFollowingReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.GetBiFollowingReq)
  // uint32 uid_a = 1;
  if (this->uid_a() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->uid_a(), output);
  }

  // uint32 uid_b = 2;
  if (this->uid_b() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->uid_b(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.GetBiFollowingReq)
}

::google::protobuf::uint8* GetBiFollowingReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.GetBiFollowingReq)
  // uint32 uid_a = 1;
  if (this->uid_a() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->uid_a(), target);
  }

  // uint32 uid_b = 2;
  if (this->uid_b() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->uid_b(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.GetBiFollowingReq)
  return target;
}

size_t GetBiFollowingReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.GetBiFollowingReq)
  size_t total_size = 0;

  // uint32 uid_a = 1;
  if (this->uid_a() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->uid_a());
  }

  // uint32 uid_b = 2;
  if (this->uid_b() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->uid_b());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetBiFollowingReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.GetBiFollowingReq)
  GOOGLE_DCHECK_NE(&from, this);
  const GetBiFollowingReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GetBiFollowingReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.GetBiFollowingReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.GetBiFollowingReq)
    MergeFrom(*source);
  }
}

void GetBiFollowingReq::MergeFrom(const GetBiFollowingReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.GetBiFollowingReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.uid_a() != 0) {
    set_uid_a(from.uid_a());
  }
  if (from.uid_b() != 0) {
    set_uid_b(from.uid_b());
  }
}

void GetBiFollowingReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.GetBiFollowingReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetBiFollowingReq::CopyFrom(const GetBiFollowingReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.GetBiFollowingReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetBiFollowingReq::IsInitialized() const {
  return true;
}

void GetBiFollowingReq::Swap(GetBiFollowingReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void GetBiFollowingReq::InternalSwap(GetBiFollowingReq* other) {
  std::swap(uid_a_, other->uid_a_);
  std::swap(uid_b_, other->uid_b_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata GetBiFollowingReq::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[12];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// GetBiFollowingReq

// uint32 uid_a = 1;
void GetBiFollowingReq::clear_uid_a() {
  uid_a_ = 0u;
}
::google::protobuf::uint32 GetBiFollowingReq::uid_a() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.GetBiFollowingReq.uid_a)
  return uid_a_;
}
void GetBiFollowingReq::set_uid_a(::google::protobuf::uint32 value) {
  
  uid_a_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.GetBiFollowingReq.uid_a)
}

// uint32 uid_b = 2;
void GetBiFollowingReq::clear_uid_b() {
  uid_b_ = 0u;
}
::google::protobuf::uint32 GetBiFollowingReq::uid_b() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.GetBiFollowingReq.uid_b)
  return uid_b_;
}
void GetBiFollowingReq::set_uid_b(::google::protobuf::uint32 value) {
  
  uid_b_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.GetBiFollowingReq.uid_b)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GetBiFollowingResp::kAToBFieldNumber;
const int GetBiFollowingResp::kBToAFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GetBiFollowingResp::GetBiFollowingResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.GetBiFollowingResp)
}
GetBiFollowingResp::GetBiFollowingResp(const GetBiFollowingResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_a_to_b()) {
    a_to_b_ = new ::ugc::friendship::Following(*from.a_to_b_);
  } else {
    a_to_b_ = NULL;
  }
  if (from.has_b_to_a()) {
    b_to_a_ = new ::ugc::friendship::Following(*from.b_to_a_);
  } else {
    b_to_a_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.GetBiFollowingResp)
}

void GetBiFollowingResp::SharedCtor() {
  ::memset(&a_to_b_, 0, reinterpret_cast<char*>(&b_to_a_) -
    reinterpret_cast<char*>(&a_to_b_) + sizeof(b_to_a_));
  _cached_size_ = 0;
}

GetBiFollowingResp::~GetBiFollowingResp() {
  // @@protoc_insertion_point(destructor:ugc.friendship.GetBiFollowingResp)
  SharedDtor();
}

void GetBiFollowingResp::SharedDtor() {
  if (this != internal_default_instance()) {
    delete a_to_b_;
  }
  if (this != internal_default_instance()) {
    delete b_to_a_;
  }
}

void GetBiFollowingResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetBiFollowingResp::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[13].descriptor;
}

const GetBiFollowingResp& GetBiFollowingResp::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

GetBiFollowingResp* GetBiFollowingResp::New(::google::protobuf::Arena* arena) const {
  GetBiFollowingResp* n = new GetBiFollowingResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void GetBiFollowingResp::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.GetBiFollowingResp)
  if (GetArenaNoVirtual() == NULL && a_to_b_ != NULL) {
    delete a_to_b_;
  }
  a_to_b_ = NULL;
  if (GetArenaNoVirtual() == NULL && b_to_a_ != NULL) {
    delete b_to_a_;
  }
  b_to_a_ = NULL;
}

bool GetBiFollowingResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.GetBiFollowingResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .ugc.friendship.Following a_to_b = 1;
      case 1: {
        if (tag == 10u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_a_to_b()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .ugc.friendship.Following b_to_a = 2;
      case 2: {
        if (tag == 18u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_b_to_a()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.GetBiFollowingResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.GetBiFollowingResp)
  return false;
#undef DO_
}

void GetBiFollowingResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.GetBiFollowingResp)
  // .ugc.friendship.Following a_to_b = 1;
  if (this->has_a_to_b()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->a_to_b_, output);
  }

  // .ugc.friendship.Following b_to_a = 2;
  if (this->has_b_to_a()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->b_to_a_, output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.GetBiFollowingResp)
}

::google::protobuf::uint8* GetBiFollowingResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.GetBiFollowingResp)
  // .ugc.friendship.Following a_to_b = 1;
  if (this->has_a_to_b()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->a_to_b_, false, target);
  }

  // .ugc.friendship.Following b_to_a = 2;
  if (this->has_b_to_a()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->b_to_a_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.GetBiFollowingResp)
  return target;
}

size_t GetBiFollowingResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.GetBiFollowingResp)
  size_t total_size = 0;

  // .ugc.friendship.Following a_to_b = 1;
  if (this->has_a_to_b()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->a_to_b_);
  }

  // .ugc.friendship.Following b_to_a = 2;
  if (this->has_b_to_a()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->b_to_a_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetBiFollowingResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.GetBiFollowingResp)
  GOOGLE_DCHECK_NE(&from, this);
  const GetBiFollowingResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GetBiFollowingResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.GetBiFollowingResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.GetBiFollowingResp)
    MergeFrom(*source);
  }
}

void GetBiFollowingResp::MergeFrom(const GetBiFollowingResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.GetBiFollowingResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_a_to_b()) {
    mutable_a_to_b()->::ugc::friendship::Following::MergeFrom(from.a_to_b());
  }
  if (from.has_b_to_a()) {
    mutable_b_to_a()->::ugc::friendship::Following::MergeFrom(from.b_to_a());
  }
}

void GetBiFollowingResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.GetBiFollowingResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetBiFollowingResp::CopyFrom(const GetBiFollowingResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.GetBiFollowingResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetBiFollowingResp::IsInitialized() const {
  return true;
}

void GetBiFollowingResp::Swap(GetBiFollowingResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void GetBiFollowingResp::InternalSwap(GetBiFollowingResp* other) {
  std::swap(a_to_b_, other->a_to_b_);
  std::swap(b_to_a_, other->b_to_a_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata GetBiFollowingResp::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[13];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// GetBiFollowingResp

// .ugc.friendship.Following a_to_b = 1;
bool GetBiFollowingResp::has_a_to_b() const {
  return this != internal_default_instance() && a_to_b_ != NULL;
}
void GetBiFollowingResp::clear_a_to_b() {
  if (GetArenaNoVirtual() == NULL && a_to_b_ != NULL) delete a_to_b_;
  a_to_b_ = NULL;
}
const ::ugc::friendship::Following& GetBiFollowingResp::a_to_b() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.GetBiFollowingResp.a_to_b)
  return a_to_b_ != NULL ? *a_to_b_
                         : *::ugc::friendship::Following::internal_default_instance();
}
::ugc::friendship::Following* GetBiFollowingResp::mutable_a_to_b() {
  
  if (a_to_b_ == NULL) {
    a_to_b_ = new ::ugc::friendship::Following;
  }
  // @@protoc_insertion_point(field_mutable:ugc.friendship.GetBiFollowingResp.a_to_b)
  return a_to_b_;
}
::ugc::friendship::Following* GetBiFollowingResp::release_a_to_b() {
  // @@protoc_insertion_point(field_release:ugc.friendship.GetBiFollowingResp.a_to_b)
  
  ::ugc::friendship::Following* temp = a_to_b_;
  a_to_b_ = NULL;
  return temp;
}
void GetBiFollowingResp::set_allocated_a_to_b(::ugc::friendship::Following* a_to_b) {
  delete a_to_b_;
  a_to_b_ = a_to_b;
  if (a_to_b) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:ugc.friendship.GetBiFollowingResp.a_to_b)
}

// .ugc.friendship.Following b_to_a = 2;
bool GetBiFollowingResp::has_b_to_a() const {
  return this != internal_default_instance() && b_to_a_ != NULL;
}
void GetBiFollowingResp::clear_b_to_a() {
  if (GetArenaNoVirtual() == NULL && b_to_a_ != NULL) delete b_to_a_;
  b_to_a_ = NULL;
}
const ::ugc::friendship::Following& GetBiFollowingResp::b_to_a() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.GetBiFollowingResp.b_to_a)
  return b_to_a_ != NULL ? *b_to_a_
                         : *::ugc::friendship::Following::internal_default_instance();
}
::ugc::friendship::Following* GetBiFollowingResp::mutable_b_to_a() {
  
  if (b_to_a_ == NULL) {
    b_to_a_ = new ::ugc::friendship::Following;
  }
  // @@protoc_insertion_point(field_mutable:ugc.friendship.GetBiFollowingResp.b_to_a)
  return b_to_a_;
}
::ugc::friendship::Following* GetBiFollowingResp::release_b_to_a() {
  // @@protoc_insertion_point(field_release:ugc.friendship.GetBiFollowingResp.b_to_a)
  
  ::ugc::friendship::Following* temp = b_to_a_;
  b_to_a_ = NULL;
  return temp;
}
void GetBiFollowingResp::set_allocated_b_to_a(::ugc::friendship::Following* b_to_a) {
  delete b_to_a_;
  b_to_a_ = b_to_a;
  if (b_to_a) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:ugc.friendship.GetBiFollowingResp.b_to_a)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BatchGetBiFollowingReq::kFromUidFieldNumber;
const int BatchGetBiFollowingReq::kToUidListFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BatchGetBiFollowingReq::BatchGetBiFollowingReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.BatchGetBiFollowingReq)
}
BatchGetBiFollowingReq::BatchGetBiFollowingReq(const BatchGetBiFollowingReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&from_uid_, &from.from_uid_,
    reinterpret_cast<char*>(&to_uid_list_) -
    reinterpret_cast<char*>(&from_uid_) + sizeof(to_uid_list_));
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.BatchGetBiFollowingReq)
}

void BatchGetBiFollowingReq::SharedCtor() {
  ::memset(&from_uid_, 0, reinterpret_cast<char*>(&to_uid_list_) -
    reinterpret_cast<char*>(&from_uid_) + sizeof(to_uid_list_));
  _cached_size_ = 0;
}

BatchGetBiFollowingReq::~BatchGetBiFollowingReq() {
  // @@protoc_insertion_point(destructor:ugc.friendship.BatchGetBiFollowingReq)
  SharedDtor();
}

void BatchGetBiFollowingReq::SharedDtor() {
}

void BatchGetBiFollowingReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BatchGetBiFollowingReq::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[14].descriptor;
}

const BatchGetBiFollowingReq& BatchGetBiFollowingReq::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

BatchGetBiFollowingReq* BatchGetBiFollowingReq::New(::google::protobuf::Arena* arena) const {
  BatchGetBiFollowingReq* n = new BatchGetBiFollowingReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void BatchGetBiFollowingReq::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.BatchGetBiFollowingReq)
  ::memset(&from_uid_, 0, reinterpret_cast<char*>(&to_uid_list_) -
    reinterpret_cast<char*>(&from_uid_) + sizeof(to_uid_list_));
}

bool BatchGetBiFollowingReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.BatchGetBiFollowingReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 from_uid = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &from_uid_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint32 to_uid_list = 2;
      case 2: {
        if (tag == 16u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &to_uid_list_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.BatchGetBiFollowingReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.BatchGetBiFollowingReq)
  return false;
#undef DO_
}

void BatchGetBiFollowingReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.BatchGetBiFollowingReq)
  // uint32 from_uid = 1;
  if (this->from_uid() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->from_uid(), output);
  }

  // uint32 to_uid_list = 2;
  if (this->to_uid_list() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->to_uid_list(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.BatchGetBiFollowingReq)
}

::google::protobuf::uint8* BatchGetBiFollowingReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.BatchGetBiFollowingReq)
  // uint32 from_uid = 1;
  if (this->from_uid() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->from_uid(), target);
  }

  // uint32 to_uid_list = 2;
  if (this->to_uid_list() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->to_uid_list(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.BatchGetBiFollowingReq)
  return target;
}

size_t BatchGetBiFollowingReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.BatchGetBiFollowingReq)
  size_t total_size = 0;

  // uint32 from_uid = 1;
  if (this->from_uid() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->from_uid());
  }

  // uint32 to_uid_list = 2;
  if (this->to_uid_list() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->to_uid_list());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BatchGetBiFollowingReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.BatchGetBiFollowingReq)
  GOOGLE_DCHECK_NE(&from, this);
  const BatchGetBiFollowingReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BatchGetBiFollowingReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.BatchGetBiFollowingReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.BatchGetBiFollowingReq)
    MergeFrom(*source);
  }
}

void BatchGetBiFollowingReq::MergeFrom(const BatchGetBiFollowingReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.BatchGetBiFollowingReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.from_uid() != 0) {
    set_from_uid(from.from_uid());
  }
  if (from.to_uid_list() != 0) {
    set_to_uid_list(from.to_uid_list());
  }
}

void BatchGetBiFollowingReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.BatchGetBiFollowingReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BatchGetBiFollowingReq::CopyFrom(const BatchGetBiFollowingReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.BatchGetBiFollowingReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BatchGetBiFollowingReq::IsInitialized() const {
  return true;
}

void BatchGetBiFollowingReq::Swap(BatchGetBiFollowingReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void BatchGetBiFollowingReq::InternalSwap(BatchGetBiFollowingReq* other) {
  std::swap(from_uid_, other->from_uid_);
  std::swap(to_uid_list_, other->to_uid_list_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata BatchGetBiFollowingReq::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[14];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// BatchGetBiFollowingReq

// uint32 from_uid = 1;
void BatchGetBiFollowingReq::clear_from_uid() {
  from_uid_ = 0u;
}
::google::protobuf::uint32 BatchGetBiFollowingReq::from_uid() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.BatchGetBiFollowingReq.from_uid)
  return from_uid_;
}
void BatchGetBiFollowingReq::set_from_uid(::google::protobuf::uint32 value) {
  
  from_uid_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.BatchGetBiFollowingReq.from_uid)
}

// uint32 to_uid_list = 2;
void BatchGetBiFollowingReq::clear_to_uid_list() {
  to_uid_list_ = 0u;
}
::google::protobuf::uint32 BatchGetBiFollowingReq::to_uid_list() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.BatchGetBiFollowingReq.to_uid_list)
  return to_uid_list_;
}
void BatchGetBiFollowingReq::set_to_uid_list(::google::protobuf::uint32 value) {
  
  to_uid_list_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.BatchGetBiFollowingReq.to_uid_list)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if PROTOBUF_INLINE_NOT_IN_HEADERS
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if PROTOBUF_INLINE_NOT_IN_HEADERS
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BatchGetBiFollowingResp::kFollowingListFieldNumber;
const int BatchGetBiFollowingResp::kFollowsMeListFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BatchGetBiFollowingResp::BatchGetBiFollowingResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.BatchGetBiFollowingResp)
}
BatchGetBiFollowingResp::BatchGetBiFollowingResp(const BatchGetBiFollowingResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  const ::google::protobuf::Descriptor*& BatchGetBiFollowingResp_FollowingListEntry_descriptor = protobuf_friendship_2eproto::file_level_metadata[15].descriptor;
  const ::google::protobuf::Descriptor*& BatchGetBiFollowingResp_FollowsMeListEntry_descriptor = protobuf_friendship_2eproto::file_level_metadata[16].descriptor;
  following_list_.SetAssignDescriptorCallback(
      protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce);
  following_list_.SetEntryDescriptor(
      &BatchGetBiFollowingResp_FollowingListEntry_descriptor);
  following_list_.MergeFrom(from.following_list_);
  follows_me_list_.SetAssignDescriptorCallback(
      protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce);
  follows_me_list_.SetEntryDescriptor(
      &BatchGetBiFollowingResp_FollowsMeListEntry_descriptor);
  follows_me_list_.MergeFrom(from.follows_me_list_);
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.BatchGetBiFollowingResp)
}

void BatchGetBiFollowingResp::SharedCtor() {
  const ::google::protobuf::Descriptor*& BatchGetBiFollowingResp_FollowingListEntry_descriptor = protobuf_friendship_2eproto::file_level_metadata[15].descriptor;
  const ::google::protobuf::Descriptor*& BatchGetBiFollowingResp_FollowsMeListEntry_descriptor = protobuf_friendship_2eproto::file_level_metadata[16].descriptor;
  following_list_.SetAssignDescriptorCallback(
      protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce);
  following_list_.SetEntryDescriptor(
      &BatchGetBiFollowingResp_FollowingListEntry_descriptor);
  follows_me_list_.SetAssignDescriptorCallback(
      protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce);
  follows_me_list_.SetEntryDescriptor(
      &BatchGetBiFollowingResp_FollowsMeListEntry_descriptor);
  _cached_size_ = 0;
}

BatchGetBiFollowingResp::~BatchGetBiFollowingResp() {
  // @@protoc_insertion_point(destructor:ugc.friendship.BatchGetBiFollowingResp)
  SharedDtor();
}

void BatchGetBiFollowingResp::SharedDtor() {
}

void BatchGetBiFollowingResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BatchGetBiFollowingResp::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[17].descriptor;
}

const BatchGetBiFollowingResp& BatchGetBiFollowingResp::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

BatchGetBiFollowingResp* BatchGetBiFollowingResp::New(::google::protobuf::Arena* arena) const {
  BatchGetBiFollowingResp* n = new BatchGetBiFollowingResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void BatchGetBiFollowingResp::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.BatchGetBiFollowingResp)
  following_list_.Clear();
  follows_me_list_.Clear();
}

bool BatchGetBiFollowingResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.BatchGetBiFollowingResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<uint32, .ugc.friendship.Following> following_list = 1;
      case 1: {
        if (tag == 10u) {
          DO_(input->IncrementRecursionDepth());
          BatchGetBiFollowingResp_FollowingListEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::uint32, ::ugc::friendship::Following,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following > > parser(&following_list_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // map<uint32, .ugc.friendship.Following> follows_me_list = 2;
      case 2: {
        if (tag == 18u) {
          DO_(input->IncrementRecursionDepth());
          BatchGetBiFollowingResp_FollowsMeListEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::uint32, ::ugc::friendship::Following,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following > > parser(&follows_me_list_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.BatchGetBiFollowingResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.BatchGetBiFollowingResp)
  return false;
#undef DO_
}

void BatchGetBiFollowingResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.BatchGetBiFollowingResp)
  // map<uint32, .ugc.friendship.Following> following_list = 1;
  if (!this->following_list().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterministic() &&
        this->following_list().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->following_list().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >::const_iterator
          it = this->following_list().begin();
          it != this->following_list().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<BatchGetBiFollowingResp_FollowingListEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(following_list_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<BatchGetBiFollowingResp_FollowingListEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >::const_iterator
          it = this->following_list().begin();
          it != this->following_list().end(); ++it) {
        entry.reset(following_list_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            1, *entry, output);
      }
    }
  }

  // map<uint32, .ugc.friendship.Following> follows_me_list = 2;
  if (!this->follows_me_list().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterministic() &&
        this->follows_me_list().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->follows_me_list().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >::const_iterator
          it = this->follows_me_list().begin();
          it != this->follows_me_list().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<BatchGetBiFollowingResp_FollowsMeListEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(follows_me_list_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<BatchGetBiFollowingResp_FollowsMeListEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >::const_iterator
          it = this->follows_me_list().begin();
          it != this->follows_me_list().end(); ++it) {
        entry.reset(follows_me_list_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.BatchGetBiFollowingResp)
}

::google::protobuf::uint8* BatchGetBiFollowingResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.BatchGetBiFollowingResp)
  // map<uint32, .ugc.friendship.Following> following_list = 1;
  if (!this->following_list().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->following_list().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->following_list().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >::const_iterator
          it = this->following_list().begin();
          it != this->following_list().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<BatchGetBiFollowingResp_FollowingListEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(following_list_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<BatchGetBiFollowingResp_FollowingListEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >::const_iterator
          it = this->following_list().begin();
          it != this->following_list().end(); ++it) {
        entry.reset(following_list_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       1, *entry, deterministic, target);
;
      }
    }
  }

  // map<uint32, .ugc.friendship.Following> follows_me_list = 2;
  if (!this->follows_me_list().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->follows_me_list().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->follows_me_list().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >::const_iterator
          it = this->follows_me_list().begin();
          it != this->follows_me_list().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<BatchGetBiFollowingResp_FollowsMeListEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(follows_me_list_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<BatchGetBiFollowingResp_FollowsMeListEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >::const_iterator
          it = this->follows_me_list().begin();
          it != this->follows_me_list().end(); ++it) {
        entry.reset(follows_me_list_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.BatchGetBiFollowingResp)
  return target;
}

size_t BatchGetBiFollowingResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.BatchGetBiFollowingResp)
  size_t total_size = 0;

  // map<uint32, .ugc.friendship.Following> following_list = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->following_list_size());
  {
    ::google::protobuf::scoped_ptr<BatchGetBiFollowingResp_FollowingListEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >::const_iterator
        it = this->following_list().begin();
        it != this->following_list().end(); ++it) {
      entry.reset(following_list_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  // map<uint32, .ugc.friendship.Following> follows_me_list = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->follows_me_list_size());
  {
    ::google::protobuf::scoped_ptr<BatchGetBiFollowingResp_FollowsMeListEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >::const_iterator
        it = this->follows_me_list().begin();
        it != this->follows_me_list().end(); ++it) {
      entry.reset(follows_me_list_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BatchGetBiFollowingResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.BatchGetBiFollowingResp)
  GOOGLE_DCHECK_NE(&from, this);
  const BatchGetBiFollowingResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BatchGetBiFollowingResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.BatchGetBiFollowingResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.BatchGetBiFollowingResp)
    MergeFrom(*source);
  }
}

void BatchGetBiFollowingResp::MergeFrom(const BatchGetBiFollowingResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.BatchGetBiFollowingResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  following_list_.MergeFrom(from.following_list_);
  follows_me_list_.MergeFrom(from.follows_me_list_);
}

void BatchGetBiFollowingResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.BatchGetBiFollowingResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BatchGetBiFollowingResp::CopyFrom(const BatchGetBiFollowingResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.BatchGetBiFollowingResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BatchGetBiFollowingResp::IsInitialized() const {
  return true;
}

void BatchGetBiFollowingResp::Swap(BatchGetBiFollowingResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void BatchGetBiFollowingResp::InternalSwap(BatchGetBiFollowingResp* other) {
  following_list_.Swap(&other->following_list_);
  follows_me_list_.Swap(&other->follows_me_list_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata BatchGetBiFollowingResp::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[17];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// BatchGetBiFollowingResp

// map<uint32, .ugc.friendship.Following> following_list = 1;
int BatchGetBiFollowingResp::following_list_size() const {
  return following_list_.size();
}
void BatchGetBiFollowingResp::clear_following_list() {
  following_list_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >&
BatchGetBiFollowingResp::following_list() const {
  // @@protoc_insertion_point(field_map:ugc.friendship.BatchGetBiFollowingResp.following_list)
  return following_list_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >*
BatchGetBiFollowingResp::mutable_following_list() {
  // @@protoc_insertion_point(field_mutable_map:ugc.friendship.BatchGetBiFollowingResp.following_list)
  return following_list_.MutableMap();
}

// map<uint32, .ugc.friendship.Following> follows_me_list = 2;
int BatchGetBiFollowingResp::follows_me_list_size() const {
  return follows_me_list_.size();
}
void BatchGetBiFollowingResp::clear_follows_me_list() {
  follows_me_list_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >&
BatchGetBiFollowingResp::follows_me_list() const {
  // @@protoc_insertion_point(field_map:ugc.friendship.BatchGetBiFollowingResp.follows_me_list)
  return follows_me_list_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Following >*
BatchGetBiFollowingResp::mutable_follows_me_list() {
  // @@protoc_insertion_point(field_mutable_map:ugc.friendship.BatchGetBiFollowingResp.follows_me_list)
  return follows_me_list_.MutableMap();
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GetUserCountsReq::kUidFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GetUserCountsReq::GetUserCountsReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.GetUserCountsReq)
}
GetUserCountsReq::GetUserCountsReq(const GetUserCountsReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  uid_ = from.uid_;
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.GetUserCountsReq)
}

void GetUserCountsReq::SharedCtor() {
  uid_ = 0u;
  _cached_size_ = 0;
}

GetUserCountsReq::~GetUserCountsReq() {
  // @@protoc_insertion_point(destructor:ugc.friendship.GetUserCountsReq)
  SharedDtor();
}

void GetUserCountsReq::SharedDtor() {
}

void GetUserCountsReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetUserCountsReq::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[18].descriptor;
}

const GetUserCountsReq& GetUserCountsReq::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

GetUserCountsReq* GetUserCountsReq::New(::google::protobuf::Arena* arena) const {
  GetUserCountsReq* n = new GetUserCountsReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void GetUserCountsReq::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.GetUserCountsReq)
  uid_ = 0u;
}

bool GetUserCountsReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.GetUserCountsReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 uid = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &uid_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.GetUserCountsReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.GetUserCountsReq)
  return false;
#undef DO_
}

void GetUserCountsReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.GetUserCountsReq)
  // uint32 uid = 1;
  if (this->uid() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->uid(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.GetUserCountsReq)
}

::google::protobuf::uint8* GetUserCountsReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.GetUserCountsReq)
  // uint32 uid = 1;
  if (this->uid() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->uid(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.GetUserCountsReq)
  return target;
}

size_t GetUserCountsReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.GetUserCountsReq)
  size_t total_size = 0;

  // uint32 uid = 1;
  if (this->uid() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->uid());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetUserCountsReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.GetUserCountsReq)
  GOOGLE_DCHECK_NE(&from, this);
  const GetUserCountsReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GetUserCountsReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.GetUserCountsReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.GetUserCountsReq)
    MergeFrom(*source);
  }
}

void GetUserCountsReq::MergeFrom(const GetUserCountsReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.GetUserCountsReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.uid() != 0) {
    set_uid(from.uid());
  }
}

void GetUserCountsReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.GetUserCountsReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetUserCountsReq::CopyFrom(const GetUserCountsReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.GetUserCountsReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetUserCountsReq::IsInitialized() const {
  return true;
}

void GetUserCountsReq::Swap(GetUserCountsReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void GetUserCountsReq::InternalSwap(GetUserCountsReq* other) {
  std::swap(uid_, other->uid_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata GetUserCountsReq::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[18];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// GetUserCountsReq

// uint32 uid = 1;
void GetUserCountsReq::clear_uid() {
  uid_ = 0u;
}
::google::protobuf::uint32 GetUserCountsReq::uid() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.GetUserCountsReq.uid)
  return uid_;
}
void GetUserCountsReq::set_uid(::google::protobuf::uint32 value) {
  
  uid_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.GetUserCountsReq.uid)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GetUserCountsResp::kUserCountsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GetUserCountsResp::GetUserCountsResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.GetUserCountsResp)
}
GetUserCountsResp::GetUserCountsResp(const GetUserCountsResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_user_counts()) {
    user_counts_ = new ::ugc::friendship::Counts(*from.user_counts_);
  } else {
    user_counts_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.GetUserCountsResp)
}

void GetUserCountsResp::SharedCtor() {
  user_counts_ = NULL;
  _cached_size_ = 0;
}

GetUserCountsResp::~GetUserCountsResp() {
  // @@protoc_insertion_point(destructor:ugc.friendship.GetUserCountsResp)
  SharedDtor();
}

void GetUserCountsResp::SharedDtor() {
  if (this != internal_default_instance()) {
    delete user_counts_;
  }
}

void GetUserCountsResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetUserCountsResp::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[19].descriptor;
}

const GetUserCountsResp& GetUserCountsResp::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

GetUserCountsResp* GetUserCountsResp::New(::google::protobuf::Arena* arena) const {
  GetUserCountsResp* n = new GetUserCountsResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void GetUserCountsResp::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.GetUserCountsResp)
  if (GetArenaNoVirtual() == NULL && user_counts_ != NULL) {
    delete user_counts_;
  }
  user_counts_ = NULL;
}

bool GetUserCountsResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.GetUserCountsResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .ugc.friendship.Counts user_counts = 1;
      case 1: {
        if (tag == 10u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_user_counts()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.GetUserCountsResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.GetUserCountsResp)
  return false;
#undef DO_
}

void GetUserCountsResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.GetUserCountsResp)
  // .ugc.friendship.Counts user_counts = 1;
  if (this->has_user_counts()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->user_counts_, output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.GetUserCountsResp)
}

::google::protobuf::uint8* GetUserCountsResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.GetUserCountsResp)
  // .ugc.friendship.Counts user_counts = 1;
  if (this->has_user_counts()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->user_counts_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.GetUserCountsResp)
  return target;
}

size_t GetUserCountsResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.GetUserCountsResp)
  size_t total_size = 0;

  // .ugc.friendship.Counts user_counts = 1;
  if (this->has_user_counts()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->user_counts_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetUserCountsResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.GetUserCountsResp)
  GOOGLE_DCHECK_NE(&from, this);
  const GetUserCountsResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GetUserCountsResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.GetUserCountsResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.GetUserCountsResp)
    MergeFrom(*source);
  }
}

void GetUserCountsResp::MergeFrom(const GetUserCountsResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.GetUserCountsResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_user_counts()) {
    mutable_user_counts()->::ugc::friendship::Counts::MergeFrom(from.user_counts());
  }
}

void GetUserCountsResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.GetUserCountsResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetUserCountsResp::CopyFrom(const GetUserCountsResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.GetUserCountsResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetUserCountsResp::IsInitialized() const {
  return true;
}

void GetUserCountsResp::Swap(GetUserCountsResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void GetUserCountsResp::InternalSwap(GetUserCountsResp* other) {
  std::swap(user_counts_, other->user_counts_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata GetUserCountsResp::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[19];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// GetUserCountsResp

// .ugc.friendship.Counts user_counts = 1;
bool GetUserCountsResp::has_user_counts() const {
  return this != internal_default_instance() && user_counts_ != NULL;
}
void GetUserCountsResp::clear_user_counts() {
  if (GetArenaNoVirtual() == NULL && user_counts_ != NULL) delete user_counts_;
  user_counts_ = NULL;
}
const ::ugc::friendship::Counts& GetUserCountsResp::user_counts() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.GetUserCountsResp.user_counts)
  return user_counts_ != NULL ? *user_counts_
                         : *::ugc::friendship::Counts::internal_default_instance();
}
::ugc::friendship::Counts* GetUserCountsResp::mutable_user_counts() {
  
  if (user_counts_ == NULL) {
    user_counts_ = new ::ugc::friendship::Counts;
  }
  // @@protoc_insertion_point(field_mutable:ugc.friendship.GetUserCountsResp.user_counts)
  return user_counts_;
}
::ugc::friendship::Counts* GetUserCountsResp::release_user_counts() {
  // @@protoc_insertion_point(field_release:ugc.friendship.GetUserCountsResp.user_counts)
  
  ::ugc::friendship::Counts* temp = user_counts_;
  user_counts_ = NULL;
  return temp;
}
void GetUserCountsResp::set_allocated_user_counts(::ugc::friendship::Counts* user_counts) {
  delete user_counts_;
  user_counts_ = user_counts;
  if (user_counts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:ugc.friendship.GetUserCountsResp.user_counts)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BatchGetUserCountsReq::kUidListFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BatchGetUserCountsReq::BatchGetUserCountsReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.BatchGetUserCountsReq)
}
BatchGetUserCountsReq::BatchGetUserCountsReq(const BatchGetUserCountsReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      uid_list_(from.uid_list_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.BatchGetUserCountsReq)
}

void BatchGetUserCountsReq::SharedCtor() {
  _cached_size_ = 0;
}

BatchGetUserCountsReq::~BatchGetUserCountsReq() {
  // @@protoc_insertion_point(destructor:ugc.friendship.BatchGetUserCountsReq)
  SharedDtor();
}

void BatchGetUserCountsReq::SharedDtor() {
}

void BatchGetUserCountsReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BatchGetUserCountsReq::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[20].descriptor;
}

const BatchGetUserCountsReq& BatchGetUserCountsReq::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

BatchGetUserCountsReq* BatchGetUserCountsReq::New(::google::protobuf::Arena* arena) const {
  BatchGetUserCountsReq* n = new BatchGetUserCountsReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void BatchGetUserCountsReq::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.BatchGetUserCountsReq)
  uid_list_.Clear();
}

bool BatchGetUserCountsReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.BatchGetUserCountsReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated uint32 uid_list = 1;
      case 1: {
        if (tag == 10u) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, this->mutable_uid_list())));
        } else if (tag == 8u) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 1, 10u, input, this->mutable_uid_list())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.BatchGetUserCountsReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.BatchGetUserCountsReq)
  return false;
#undef DO_
}

void BatchGetUserCountsReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.BatchGetUserCountsReq)
  // repeated uint32 uid_list = 1;
  if (this->uid_list_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(1, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_uid_list_cached_byte_size_);
  }
  for (int i = 0; i < this->uid_list_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32NoTag(
      this->uid_list(i), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.BatchGetUserCountsReq)
}

::google::protobuf::uint8* BatchGetUserCountsReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.BatchGetUserCountsReq)
  // repeated uint32 uid_list = 1;
  if (this->uid_list_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      1,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _uid_list_cached_byte_size_, target);
  }
  for (int i = 0; i < this->uid_list_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt32NoTagToArray(this->uid_list(i), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.BatchGetUserCountsReq)
  return target;
}

size_t BatchGetUserCountsReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.BatchGetUserCountsReq)
  size_t total_size = 0;

  // repeated uint32 uid_list = 1;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      UInt32Size(this->uid_list_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _uid_list_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BatchGetUserCountsReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.BatchGetUserCountsReq)
  GOOGLE_DCHECK_NE(&from, this);
  const BatchGetUserCountsReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BatchGetUserCountsReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.BatchGetUserCountsReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.BatchGetUserCountsReq)
    MergeFrom(*source);
  }
}

void BatchGetUserCountsReq::MergeFrom(const BatchGetUserCountsReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.BatchGetUserCountsReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  uid_list_.MergeFrom(from.uid_list_);
}

void BatchGetUserCountsReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.BatchGetUserCountsReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BatchGetUserCountsReq::CopyFrom(const BatchGetUserCountsReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.BatchGetUserCountsReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BatchGetUserCountsReq::IsInitialized() const {
  return true;
}

void BatchGetUserCountsReq::Swap(BatchGetUserCountsReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void BatchGetUserCountsReq::InternalSwap(BatchGetUserCountsReq* other) {
  uid_list_.UnsafeArenaSwap(&other->uid_list_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata BatchGetUserCountsReq::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[20];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// BatchGetUserCountsReq

// repeated uint32 uid_list = 1;
int BatchGetUserCountsReq::uid_list_size() const {
  return uid_list_.size();
}
void BatchGetUserCountsReq::clear_uid_list() {
  uid_list_.Clear();
}
::google::protobuf::uint32 BatchGetUserCountsReq::uid_list(int index) const {
  // @@protoc_insertion_point(field_get:ugc.friendship.BatchGetUserCountsReq.uid_list)
  return uid_list_.Get(index);
}
void BatchGetUserCountsReq::set_uid_list(int index, ::google::protobuf::uint32 value) {
  uid_list_.Set(index, value);
  // @@protoc_insertion_point(field_set:ugc.friendship.BatchGetUserCountsReq.uid_list)
}
void BatchGetUserCountsReq::add_uid_list(::google::protobuf::uint32 value) {
  uid_list_.Add(value);
  // @@protoc_insertion_point(field_add:ugc.friendship.BatchGetUserCountsReq.uid_list)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
BatchGetUserCountsReq::uid_list() const {
  // @@protoc_insertion_point(field_list:ugc.friendship.BatchGetUserCountsReq.uid_list)
  return uid_list_;
}
::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
BatchGetUserCountsReq::mutable_uid_list() {
  // @@protoc_insertion_point(field_mutable_list:ugc.friendship.BatchGetUserCountsReq.uid_list)
  return &uid_list_;
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if PROTOBUF_INLINE_NOT_IN_HEADERS
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BatchGetUserCountsResp::kUserCountsMapFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BatchGetUserCountsResp::BatchGetUserCountsResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.BatchGetUserCountsResp)
}
BatchGetUserCountsResp::BatchGetUserCountsResp(const BatchGetUserCountsResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  const ::google::protobuf::Descriptor*& BatchGetUserCountsResp_UserCountsMapEntry_descriptor = protobuf_friendship_2eproto::file_level_metadata[21].descriptor;
  user_counts_map_.SetAssignDescriptorCallback(
      protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce);
  user_counts_map_.SetEntryDescriptor(
      &BatchGetUserCountsResp_UserCountsMapEntry_descriptor);
  user_counts_map_.MergeFrom(from.user_counts_map_);
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.BatchGetUserCountsResp)
}

void BatchGetUserCountsResp::SharedCtor() {
  const ::google::protobuf::Descriptor*& BatchGetUserCountsResp_UserCountsMapEntry_descriptor = protobuf_friendship_2eproto::file_level_metadata[21].descriptor;
  user_counts_map_.SetAssignDescriptorCallback(
      protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce);
  user_counts_map_.SetEntryDescriptor(
      &BatchGetUserCountsResp_UserCountsMapEntry_descriptor);
  _cached_size_ = 0;
}

BatchGetUserCountsResp::~BatchGetUserCountsResp() {
  // @@protoc_insertion_point(destructor:ugc.friendship.BatchGetUserCountsResp)
  SharedDtor();
}

void BatchGetUserCountsResp::SharedDtor() {
}

void BatchGetUserCountsResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BatchGetUserCountsResp::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[22].descriptor;
}

const BatchGetUserCountsResp& BatchGetUserCountsResp::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

BatchGetUserCountsResp* BatchGetUserCountsResp::New(::google::protobuf::Arena* arena) const {
  BatchGetUserCountsResp* n = new BatchGetUserCountsResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void BatchGetUserCountsResp::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.BatchGetUserCountsResp)
  user_counts_map_.Clear();
}

bool BatchGetUserCountsResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.BatchGetUserCountsResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // map<uint32, .ugc.friendship.Counts> user_counts_map = 2;
      case 2: {
        if (tag == 18u) {
          DO_(input->IncrementRecursionDepth());
          BatchGetUserCountsResp_UserCountsMapEntry::Parser< ::google::protobuf::internal::MapField<
              ::google::protobuf::uint32, ::ugc::friendship::Counts,
              ::google::protobuf::internal::WireFormatLite::TYPE_UINT32,
              ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
              0 >,
            ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Counts > > parser(&user_counts_map_);
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
              input, &parser));
        } else {
          goto handle_unusual;
        }
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.BatchGetUserCountsResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.BatchGetUserCountsResp)
  return false;
#undef DO_
}

void BatchGetUserCountsResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.BatchGetUserCountsResp)
  // map<uint32, .ugc.friendship.Counts> user_counts_map = 2;
  if (!this->user_counts_map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Counts >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (output->IsSerializationDeterministic() &&
        this->user_counts_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->user_counts_map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Counts >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Counts >::const_iterator
          it = this->user_counts_map().begin();
          it != this->user_counts_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<BatchGetUserCountsResp_UserCountsMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(user_counts_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
      }
    } else {
      ::google::protobuf::scoped_ptr<BatchGetUserCountsResp_UserCountsMapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Counts >::const_iterator
          it = this->user_counts_map().begin();
          it != this->user_counts_map().end(); ++it) {
        entry.reset(user_counts_map_.NewEntryWrapper(
            it->first, it->second));
        ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
            2, *entry, output);
      }
    }
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.BatchGetUserCountsResp)
}

::google::protobuf::uint8* BatchGetUserCountsResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.BatchGetUserCountsResp)
  // map<uint32, .ugc.friendship.Counts> user_counts_map = 2;
  if (!this->user_counts_map().empty()) {
    typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Counts >::const_pointer
        ConstPtr;
    typedef ::google::protobuf::internal::SortItem< ::google::protobuf::uint32, ConstPtr > SortItem;
    typedef ::google::protobuf::internal::CompareByFirstField<SortItem> Less;

    if (deterministic &&
        this->user_counts_map().size() > 1) {
      ::google::protobuf::scoped_array<SortItem> items(
          new SortItem[this->user_counts_map().size()]);
      typedef ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Counts >::size_type size_type;
      size_type n = 0;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Counts >::const_iterator
          it = this->user_counts_map().begin();
          it != this->user_counts_map().end(); ++it, ++n) {
        items[n] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[n], Less());
      ::google::protobuf::scoped_ptr<BatchGetUserCountsResp_UserCountsMapEntry> entry;
      for (size_type i = 0; i < n; i++) {
        entry.reset(user_counts_map_.NewEntryWrapper(
            items[i].second->first, items[i].second->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
      }
    } else {
      ::google::protobuf::scoped_ptr<BatchGetUserCountsResp_UserCountsMapEntry> entry;
      for (::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Counts >::const_iterator
          it = this->user_counts_map().begin();
          it != this->user_counts_map().end(); ++it) {
        entry.reset(user_counts_map_.NewEntryWrapper(
            it->first, it->second));
        target = ::google::protobuf::internal::WireFormatLite::
                   InternalWriteMessageNoVirtualToArray(
                       2, *entry, deterministic, target);
;
      }
    }
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.BatchGetUserCountsResp)
  return target;
}

size_t BatchGetUserCountsResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.BatchGetUserCountsResp)
  size_t total_size = 0;

  // map<uint32, .ugc.friendship.Counts> user_counts_map = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->user_counts_map_size());
  {
    ::google::protobuf::scoped_ptr<BatchGetUserCountsResp_UserCountsMapEntry> entry;
    for (::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Counts >::const_iterator
        it = this->user_counts_map().begin();
        it != this->user_counts_map().end(); ++it) {
      entry.reset(user_counts_map_.NewEntryWrapper(it->first, it->second));
      total_size += ::google::protobuf::internal::WireFormatLite::
          MessageSizeNoVirtual(*entry);
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BatchGetUserCountsResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.BatchGetUserCountsResp)
  GOOGLE_DCHECK_NE(&from, this);
  const BatchGetUserCountsResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BatchGetUserCountsResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.BatchGetUserCountsResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.BatchGetUserCountsResp)
    MergeFrom(*source);
  }
}

void BatchGetUserCountsResp::MergeFrom(const BatchGetUserCountsResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.BatchGetUserCountsResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  user_counts_map_.MergeFrom(from.user_counts_map_);
}

void BatchGetUserCountsResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.BatchGetUserCountsResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BatchGetUserCountsResp::CopyFrom(const BatchGetUserCountsResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.BatchGetUserCountsResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BatchGetUserCountsResp::IsInitialized() const {
  return true;
}

void BatchGetUserCountsResp::Swap(BatchGetUserCountsResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void BatchGetUserCountsResp::InternalSwap(BatchGetUserCountsResp* other) {
  user_counts_map_.Swap(&other->user_counts_map_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata BatchGetUserCountsResp::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[22];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// BatchGetUserCountsResp

// map<uint32, .ugc.friendship.Counts> user_counts_map = 2;
int BatchGetUserCountsResp::user_counts_map_size() const {
  return user_counts_map_.size();
}
void BatchGetUserCountsResp::clear_user_counts_map() {
  user_counts_map_.Clear();
}
 const ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Counts >&
BatchGetUserCountsResp::user_counts_map() const {
  // @@protoc_insertion_point(field_map:ugc.friendship.BatchGetUserCountsResp.user_counts_map)
  return user_counts_map_.GetMap();
}
 ::google::protobuf::Map< ::google::protobuf::uint32, ::ugc::friendship::Counts >*
BatchGetUserCountsResp::mutable_user_counts_map() {
  // @@protoc_insertion_point(field_mutable_map:ugc.friendship.BatchGetUserCountsResp.user_counts_map)
  return user_counts_map_.MutableMap();
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SyncFromFriendListReq_Friend::kUidFieldNumber;
const int SyncFromFriendListReq_Friend::kIsDeleteFieldNumber;
const int SyncFromFriendListReq_Friend::kCreateAtTimestampFieldNumber;
const int SyncFromFriendListReq_Friend::kCreateOrDeleteSeqFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SyncFromFriendListReq_Friend::SyncFromFriendListReq_Friend()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.SyncFromFriendListReq.Friend)
}
SyncFromFriendListReq_Friend::SyncFromFriendListReq_Friend(const SyncFromFriendListReq_Friend& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&uid_, &from.uid_,
    reinterpret_cast<char*>(&create_or_delete_seq_) -
    reinterpret_cast<char*>(&uid_) + sizeof(create_or_delete_seq_));
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.SyncFromFriendListReq.Friend)
}

void SyncFromFriendListReq_Friend::SharedCtor() {
  ::memset(&uid_, 0, reinterpret_cast<char*>(&create_or_delete_seq_) -
    reinterpret_cast<char*>(&uid_) + sizeof(create_or_delete_seq_));
  _cached_size_ = 0;
}

SyncFromFriendListReq_Friend::~SyncFromFriendListReq_Friend() {
  // @@protoc_insertion_point(destructor:ugc.friendship.SyncFromFriendListReq.Friend)
  SharedDtor();
}

void SyncFromFriendListReq_Friend::SharedDtor() {
}

void SyncFromFriendListReq_Friend::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SyncFromFriendListReq_Friend::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[23].descriptor;
}

const SyncFromFriendListReq_Friend& SyncFromFriendListReq_Friend::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

SyncFromFriendListReq_Friend* SyncFromFriendListReq_Friend::New(::google::protobuf::Arena* arena) const {
  SyncFromFriendListReq_Friend* n = new SyncFromFriendListReq_Friend;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SyncFromFriendListReq_Friend::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.SyncFromFriendListReq.Friend)
  ::memset(&uid_, 0, reinterpret_cast<char*>(&create_or_delete_seq_) -
    reinterpret_cast<char*>(&uid_) + sizeof(create_or_delete_seq_));
}

bool SyncFromFriendListReq_Friend::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.SyncFromFriendListReq.Friend)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 uid = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &uid_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool is_delete = 2;
      case 2: {
        if (tag == 16u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &is_delete_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 create_at_timestamp = 3;
      case 3: {
        if (tag == 24u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &create_at_timestamp_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 create_or_delete_seq = 4;
      case 4: {
        if (tag == 32u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &create_or_delete_seq_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.SyncFromFriendListReq.Friend)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.SyncFromFriendListReq.Friend)
  return false;
#undef DO_
}

void SyncFromFriendListReq_Friend::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.SyncFromFriendListReq.Friend)
  // uint32 uid = 1;
  if (this->uid() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->uid(), output);
  }

  // bool is_delete = 2;
  if (this->is_delete() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(2, this->is_delete(), output);
  }

  // uint64 create_at_timestamp = 3;
  if (this->create_at_timestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(3, this->create_at_timestamp(), output);
  }

  // uint64 create_or_delete_seq = 4;
  if (this->create_or_delete_seq() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(4, this->create_or_delete_seq(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.SyncFromFriendListReq.Friend)
}

::google::protobuf::uint8* SyncFromFriendListReq_Friend::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.SyncFromFriendListReq.Friend)
  // uint32 uid = 1;
  if (this->uid() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->uid(), target);
  }

  // bool is_delete = 2;
  if (this->is_delete() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(2, this->is_delete(), target);
  }

  // uint64 create_at_timestamp = 3;
  if (this->create_at_timestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(3, this->create_at_timestamp(), target);
  }

  // uint64 create_or_delete_seq = 4;
  if (this->create_or_delete_seq() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(4, this->create_or_delete_seq(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.SyncFromFriendListReq.Friend)
  return target;
}

size_t SyncFromFriendListReq_Friend::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.SyncFromFriendListReq.Friend)
  size_t total_size = 0;

  // uint32 uid = 1;
  if (this->uid() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->uid());
  }

  // bool is_delete = 2;
  if (this->is_delete() != 0) {
    total_size += 1 + 1;
  }

  // uint64 create_at_timestamp = 3;
  if (this->create_at_timestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->create_at_timestamp());
  }

  // uint64 create_or_delete_seq = 4;
  if (this->create_or_delete_seq() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->create_or_delete_seq());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SyncFromFriendListReq_Friend::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.SyncFromFriendListReq.Friend)
  GOOGLE_DCHECK_NE(&from, this);
  const SyncFromFriendListReq_Friend* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SyncFromFriendListReq_Friend>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.SyncFromFriendListReq.Friend)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.SyncFromFriendListReq.Friend)
    MergeFrom(*source);
  }
}

void SyncFromFriendListReq_Friend::MergeFrom(const SyncFromFriendListReq_Friend& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.SyncFromFriendListReq.Friend)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.uid() != 0) {
    set_uid(from.uid());
  }
  if (from.is_delete() != 0) {
    set_is_delete(from.is_delete());
  }
  if (from.create_at_timestamp() != 0) {
    set_create_at_timestamp(from.create_at_timestamp());
  }
  if (from.create_or_delete_seq() != 0) {
    set_create_or_delete_seq(from.create_or_delete_seq());
  }
}

void SyncFromFriendListReq_Friend::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.SyncFromFriendListReq.Friend)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SyncFromFriendListReq_Friend::CopyFrom(const SyncFromFriendListReq_Friend& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.SyncFromFriendListReq.Friend)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SyncFromFriendListReq_Friend::IsInitialized() const {
  return true;
}

void SyncFromFriendListReq_Friend::Swap(SyncFromFriendListReq_Friend* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SyncFromFriendListReq_Friend::InternalSwap(SyncFromFriendListReq_Friend* other) {
  std::swap(uid_, other->uid_);
  std::swap(is_delete_, other->is_delete_);
  std::swap(create_at_timestamp_, other->create_at_timestamp_);
  std::swap(create_or_delete_seq_, other->create_or_delete_seq_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SyncFromFriendListReq_Friend::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[23];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// SyncFromFriendListReq_Friend

// uint32 uid = 1;
void SyncFromFriendListReq_Friend::clear_uid() {
  uid_ = 0u;
}
::google::protobuf::uint32 SyncFromFriendListReq_Friend::uid() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.SyncFromFriendListReq.Friend.uid)
  return uid_;
}
void SyncFromFriendListReq_Friend::set_uid(::google::protobuf::uint32 value) {
  
  uid_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.SyncFromFriendListReq.Friend.uid)
}

// bool is_delete = 2;
void SyncFromFriendListReq_Friend::clear_is_delete() {
  is_delete_ = false;
}
bool SyncFromFriendListReq_Friend::is_delete() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.SyncFromFriendListReq.Friend.is_delete)
  return is_delete_;
}
void SyncFromFriendListReq_Friend::set_is_delete(bool value) {
  
  is_delete_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.SyncFromFriendListReq.Friend.is_delete)
}

// uint64 create_at_timestamp = 3;
void SyncFromFriendListReq_Friend::clear_create_at_timestamp() {
  create_at_timestamp_ = GOOGLE_ULONGLONG(0);
}
::google::protobuf::uint64 SyncFromFriendListReq_Friend::create_at_timestamp() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.SyncFromFriendListReq.Friend.create_at_timestamp)
  return create_at_timestamp_;
}
void SyncFromFriendListReq_Friend::set_create_at_timestamp(::google::protobuf::uint64 value) {
  
  create_at_timestamp_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.SyncFromFriendListReq.Friend.create_at_timestamp)
}

// uint64 create_or_delete_seq = 4;
void SyncFromFriendListReq_Friend::clear_create_or_delete_seq() {
  create_or_delete_seq_ = GOOGLE_ULONGLONG(0);
}
::google::protobuf::uint64 SyncFromFriendListReq_Friend::create_or_delete_seq() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.SyncFromFriendListReq.Friend.create_or_delete_seq)
  return create_or_delete_seq_;
}
void SyncFromFriendListReq_Friend::set_create_or_delete_seq(::google::protobuf::uint64 value) {
  
  create_or_delete_seq_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.SyncFromFriendListReq.Friend.create_or_delete_seq)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SyncFromFriendListReq::kUidFieldNumber;
const int SyncFromFriendListReq::kFriendListFieldNumber;
const int SyncFromFriendListReq::kMaxUgcSequenceFieldNumber;
const int SyncFromFriendListReq::kAddReverseSequenceZeroFollowingFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SyncFromFriendListReq::SyncFromFriendListReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.SyncFromFriendListReq)
}
SyncFromFriendListReq::SyncFromFriendListReq(const SyncFromFriendListReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      friend_list_(from.friend_list_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&uid_, &from.uid_,
    reinterpret_cast<char*>(&max_ugc_sequence_) -
    reinterpret_cast<char*>(&uid_) + sizeof(max_ugc_sequence_));
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.SyncFromFriendListReq)
}

void SyncFromFriendListReq::SharedCtor() {
  ::memset(&uid_, 0, reinterpret_cast<char*>(&max_ugc_sequence_) -
    reinterpret_cast<char*>(&uid_) + sizeof(max_ugc_sequence_));
  _cached_size_ = 0;
}

SyncFromFriendListReq::~SyncFromFriendListReq() {
  // @@protoc_insertion_point(destructor:ugc.friendship.SyncFromFriendListReq)
  SharedDtor();
}

void SyncFromFriendListReq::SharedDtor() {
}

void SyncFromFriendListReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SyncFromFriendListReq::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[24].descriptor;
}

const SyncFromFriendListReq& SyncFromFriendListReq::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

SyncFromFriendListReq* SyncFromFriendListReq::New(::google::protobuf::Arena* arena) const {
  SyncFromFriendListReq* n = new SyncFromFriendListReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SyncFromFriendListReq::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.SyncFromFriendListReq)
  friend_list_.Clear();
  ::memset(&uid_, 0, reinterpret_cast<char*>(&max_ugc_sequence_) -
    reinterpret_cast<char*>(&uid_) + sizeof(max_ugc_sequence_));
}

bool SyncFromFriendListReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.SyncFromFriendListReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 uid = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &uid_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .ugc.friendship.SyncFromFriendListReq.Friend friend_list = 2;
      case 2: {
        if (tag == 18u) {
          DO_(input->IncrementRecursionDepth());
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_friend_list()));
        } else {
          goto handle_unusual;
        }
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // uint64 max_ugc_sequence = 3;
      case 3: {
        if (tag == 24u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &max_ugc_sequence_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool add_reverse_sequence_zero_following = 4;
      case 4: {
        if (tag == 32u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &add_reverse_sequence_zero_following_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.SyncFromFriendListReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.SyncFromFriendListReq)
  return false;
#undef DO_
}

void SyncFromFriendListReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.SyncFromFriendListReq)
  // uint32 uid = 1;
  if (this->uid() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->uid(), output);
  }

  // repeated .ugc.friendship.SyncFromFriendListReq.Friend friend_list = 2;
  for (unsigned int i = 0, n = this->friend_list_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->friend_list(i), output);
  }

  // uint64 max_ugc_sequence = 3;
  if (this->max_ugc_sequence() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(3, this->max_ugc_sequence(), output);
  }

  // bool add_reverse_sequence_zero_following = 4;
  if (this->add_reverse_sequence_zero_following() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(4, this->add_reverse_sequence_zero_following(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.SyncFromFriendListReq)
}

::google::protobuf::uint8* SyncFromFriendListReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.SyncFromFriendListReq)
  // uint32 uid = 1;
  if (this->uid() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->uid(), target);
  }

  // repeated .ugc.friendship.SyncFromFriendListReq.Friend friend_list = 2;
  for (unsigned int i = 0, n = this->friend_list_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, this->friend_list(i), false, target);
  }

  // uint64 max_ugc_sequence = 3;
  if (this->max_ugc_sequence() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(3, this->max_ugc_sequence(), target);
  }

  // bool add_reverse_sequence_zero_following = 4;
  if (this->add_reverse_sequence_zero_following() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(4, this->add_reverse_sequence_zero_following(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.SyncFromFriendListReq)
  return target;
}

size_t SyncFromFriendListReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.SyncFromFriendListReq)
  size_t total_size = 0;

  // repeated .ugc.friendship.SyncFromFriendListReq.Friend friend_list = 2;
  {
    unsigned int count = this->friend_list_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->friend_list(i));
    }
  }

  // uint32 uid = 1;
  if (this->uid() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->uid());
  }

  // bool add_reverse_sequence_zero_following = 4;
  if (this->add_reverse_sequence_zero_following() != 0) {
    total_size += 1 + 1;
  }

  // uint64 max_ugc_sequence = 3;
  if (this->max_ugc_sequence() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->max_ugc_sequence());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SyncFromFriendListReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.SyncFromFriendListReq)
  GOOGLE_DCHECK_NE(&from, this);
  const SyncFromFriendListReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SyncFromFriendListReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.SyncFromFriendListReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.SyncFromFriendListReq)
    MergeFrom(*source);
  }
}

void SyncFromFriendListReq::MergeFrom(const SyncFromFriendListReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.SyncFromFriendListReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  friend_list_.MergeFrom(from.friend_list_);
  if (from.uid() != 0) {
    set_uid(from.uid());
  }
  if (from.add_reverse_sequence_zero_following() != 0) {
    set_add_reverse_sequence_zero_following(from.add_reverse_sequence_zero_following());
  }
  if (from.max_ugc_sequence() != 0) {
    set_max_ugc_sequence(from.max_ugc_sequence());
  }
}

void SyncFromFriendListReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.SyncFromFriendListReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SyncFromFriendListReq::CopyFrom(const SyncFromFriendListReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.SyncFromFriendListReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SyncFromFriendListReq::IsInitialized() const {
  return true;
}

void SyncFromFriendListReq::Swap(SyncFromFriendListReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SyncFromFriendListReq::InternalSwap(SyncFromFriendListReq* other) {
  friend_list_.UnsafeArenaSwap(&other->friend_list_);
  std::swap(uid_, other->uid_);
  std::swap(add_reverse_sequence_zero_following_, other->add_reverse_sequence_zero_following_);
  std::swap(max_ugc_sequence_, other->max_ugc_sequence_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SyncFromFriendListReq::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[24];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// SyncFromFriendListReq

// uint32 uid = 1;
void SyncFromFriendListReq::clear_uid() {
  uid_ = 0u;
}
::google::protobuf::uint32 SyncFromFriendListReq::uid() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.SyncFromFriendListReq.uid)
  return uid_;
}
void SyncFromFriendListReq::set_uid(::google::protobuf::uint32 value) {
  
  uid_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.SyncFromFriendListReq.uid)
}

// repeated .ugc.friendship.SyncFromFriendListReq.Friend friend_list = 2;
int SyncFromFriendListReq::friend_list_size() const {
  return friend_list_.size();
}
void SyncFromFriendListReq::clear_friend_list() {
  friend_list_.Clear();
}
const ::ugc::friendship::SyncFromFriendListReq_Friend& SyncFromFriendListReq::friend_list(int index) const {
  // @@protoc_insertion_point(field_get:ugc.friendship.SyncFromFriendListReq.friend_list)
  return friend_list_.Get(index);
}
::ugc::friendship::SyncFromFriendListReq_Friend* SyncFromFriendListReq::mutable_friend_list(int index) {
  // @@protoc_insertion_point(field_mutable:ugc.friendship.SyncFromFriendListReq.friend_list)
  return friend_list_.Mutable(index);
}
::ugc::friendship::SyncFromFriendListReq_Friend* SyncFromFriendListReq::add_friend_list() {
  // @@protoc_insertion_point(field_add:ugc.friendship.SyncFromFriendListReq.friend_list)
  return friend_list_.Add();
}
::google::protobuf::RepeatedPtrField< ::ugc::friendship::SyncFromFriendListReq_Friend >*
SyncFromFriendListReq::mutable_friend_list() {
  // @@protoc_insertion_point(field_mutable_list:ugc.friendship.SyncFromFriendListReq.friend_list)
  return &friend_list_;
}
const ::google::protobuf::RepeatedPtrField< ::ugc::friendship::SyncFromFriendListReq_Friend >&
SyncFromFriendListReq::friend_list() const {
  // @@protoc_insertion_point(field_list:ugc.friendship.SyncFromFriendListReq.friend_list)
  return friend_list_;
}

// uint64 max_ugc_sequence = 3;
void SyncFromFriendListReq::clear_max_ugc_sequence() {
  max_ugc_sequence_ = GOOGLE_ULONGLONG(0);
}
::google::protobuf::uint64 SyncFromFriendListReq::max_ugc_sequence() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.SyncFromFriendListReq.max_ugc_sequence)
  return max_ugc_sequence_;
}
void SyncFromFriendListReq::set_max_ugc_sequence(::google::protobuf::uint64 value) {
  
  max_ugc_sequence_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.SyncFromFriendListReq.max_ugc_sequence)
}

// bool add_reverse_sequence_zero_following = 4;
void SyncFromFriendListReq::clear_add_reverse_sequence_zero_following() {
  add_reverse_sequence_zero_following_ = false;
}
bool SyncFromFriendListReq::add_reverse_sequence_zero_following() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.SyncFromFriendListReq.add_reverse_sequence_zero_following)
  return add_reverse_sequence_zero_following_;
}
void SyncFromFriendListReq::set_add_reverse_sequence_zero_following(bool value) {
  
  add_reverse_sequence_zero_following_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.SyncFromFriendListReq.add_reverse_sequence_zero_following)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SyncFromFriendListResp::SyncFromFriendListResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.SyncFromFriendListResp)
}
SyncFromFriendListResp::SyncFromFriendListResp(const SyncFromFriendListResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.SyncFromFriendListResp)
}

void SyncFromFriendListResp::SharedCtor() {
  _cached_size_ = 0;
}

SyncFromFriendListResp::~SyncFromFriendListResp() {
  // @@protoc_insertion_point(destructor:ugc.friendship.SyncFromFriendListResp)
  SharedDtor();
}

void SyncFromFriendListResp::SharedDtor() {
}

void SyncFromFriendListResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SyncFromFriendListResp::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[25].descriptor;
}

const SyncFromFriendListResp& SyncFromFriendListResp::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

SyncFromFriendListResp* SyncFromFriendListResp::New(::google::protobuf::Arena* arena) const {
  SyncFromFriendListResp* n = new SyncFromFriendListResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SyncFromFriendListResp::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.SyncFromFriendListResp)
}

bool SyncFromFriendListResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.SyncFromFriendListResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0 ||
        ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
        ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.SyncFromFriendListResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.SyncFromFriendListResp)
  return false;
#undef DO_
}

void SyncFromFriendListResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.SyncFromFriendListResp)
  // @@protoc_insertion_point(serialize_end:ugc.friendship.SyncFromFriendListResp)
}

::google::protobuf::uint8* SyncFromFriendListResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.SyncFromFriendListResp)
  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.SyncFromFriendListResp)
  return target;
}

size_t SyncFromFriendListResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.SyncFromFriendListResp)
  size_t total_size = 0;

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SyncFromFriendListResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.SyncFromFriendListResp)
  GOOGLE_DCHECK_NE(&from, this);
  const SyncFromFriendListResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SyncFromFriendListResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.SyncFromFriendListResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.SyncFromFriendListResp)
    MergeFrom(*source);
  }
}

void SyncFromFriendListResp::MergeFrom(const SyncFromFriendListResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.SyncFromFriendListResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
}

void SyncFromFriendListResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.SyncFromFriendListResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SyncFromFriendListResp::CopyFrom(const SyncFromFriendListResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.SyncFromFriendListResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SyncFromFriendListResp::IsInitialized() const {
  return true;
}

void SyncFromFriendListResp::Swap(SyncFromFriendListResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SyncFromFriendListResp::InternalSwap(SyncFromFriendListResp* other) {
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SyncFromFriendListResp::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[25];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// SyncFromFriendListResp

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GetSynchronizedSequenceReq::kUidFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GetSynchronizedSequenceReq::GetSynchronizedSequenceReq()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.GetSynchronizedSequenceReq)
}
GetSynchronizedSequenceReq::GetSynchronizedSequenceReq(const GetSynchronizedSequenceReq& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  uid_ = from.uid_;
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.GetSynchronizedSequenceReq)
}

void GetSynchronizedSequenceReq::SharedCtor() {
  uid_ = 0u;
  _cached_size_ = 0;
}

GetSynchronizedSequenceReq::~GetSynchronizedSequenceReq() {
  // @@protoc_insertion_point(destructor:ugc.friendship.GetSynchronizedSequenceReq)
  SharedDtor();
}

void GetSynchronizedSequenceReq::SharedDtor() {
}

void GetSynchronizedSequenceReq::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetSynchronizedSequenceReq::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[26].descriptor;
}

const GetSynchronizedSequenceReq& GetSynchronizedSequenceReq::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

GetSynchronizedSequenceReq* GetSynchronizedSequenceReq::New(::google::protobuf::Arena* arena) const {
  GetSynchronizedSequenceReq* n = new GetSynchronizedSequenceReq;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void GetSynchronizedSequenceReq::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.GetSynchronizedSequenceReq)
  uid_ = 0u;
}

bool GetSynchronizedSequenceReq::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.GetSynchronizedSequenceReq)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 uid = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &uid_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.GetSynchronizedSequenceReq)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.GetSynchronizedSequenceReq)
  return false;
#undef DO_
}

void GetSynchronizedSequenceReq::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.GetSynchronizedSequenceReq)
  // uint32 uid = 1;
  if (this->uid() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->uid(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.GetSynchronizedSequenceReq)
}

::google::protobuf::uint8* GetSynchronizedSequenceReq::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.GetSynchronizedSequenceReq)
  // uint32 uid = 1;
  if (this->uid() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->uid(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.GetSynchronizedSequenceReq)
  return target;
}

size_t GetSynchronizedSequenceReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.GetSynchronizedSequenceReq)
  size_t total_size = 0;

  // uint32 uid = 1;
  if (this->uid() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->uid());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetSynchronizedSequenceReq::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.GetSynchronizedSequenceReq)
  GOOGLE_DCHECK_NE(&from, this);
  const GetSynchronizedSequenceReq* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GetSynchronizedSequenceReq>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.GetSynchronizedSequenceReq)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.GetSynchronizedSequenceReq)
    MergeFrom(*source);
  }
}

void GetSynchronizedSequenceReq::MergeFrom(const GetSynchronizedSequenceReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.GetSynchronizedSequenceReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.uid() != 0) {
    set_uid(from.uid());
  }
}

void GetSynchronizedSequenceReq::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.GetSynchronizedSequenceReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetSynchronizedSequenceReq::CopyFrom(const GetSynchronizedSequenceReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.GetSynchronizedSequenceReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetSynchronizedSequenceReq::IsInitialized() const {
  return true;
}

void GetSynchronizedSequenceReq::Swap(GetSynchronizedSequenceReq* other) {
  if (other == this) return;
  InternalSwap(other);
}
void GetSynchronizedSequenceReq::InternalSwap(GetSynchronizedSequenceReq* other) {
  std::swap(uid_, other->uid_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata GetSynchronizedSequenceReq::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[26];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// GetSynchronizedSequenceReq

// uint32 uid = 1;
void GetSynchronizedSequenceReq::clear_uid() {
  uid_ = 0u;
}
::google::protobuf::uint32 GetSynchronizedSequenceReq::uid() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.GetSynchronizedSequenceReq.uid)
  return uid_;
}
void GetSynchronizedSequenceReq::set_uid(::google::protobuf::uint32 value) {
  
  uid_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.GetSynchronizedSequenceReq.uid)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GetSynchronizedSequenceResp::kSequenceIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GetSynchronizedSequenceResp::GetSynchronizedSequenceResp()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_friendship_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:ugc.friendship.GetSynchronizedSequenceResp)
}
GetSynchronizedSequenceResp::GetSynchronizedSequenceResp(const GetSynchronizedSequenceResp& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  sequence_id_ = from.sequence_id_;
  // @@protoc_insertion_point(copy_constructor:ugc.friendship.GetSynchronizedSequenceResp)
}

void GetSynchronizedSequenceResp::SharedCtor() {
  sequence_id_ = GOOGLE_ULONGLONG(0);
  _cached_size_ = 0;
}

GetSynchronizedSequenceResp::~GetSynchronizedSequenceResp() {
  // @@protoc_insertion_point(destructor:ugc.friendship.GetSynchronizedSequenceResp)
  SharedDtor();
}

void GetSynchronizedSequenceResp::SharedDtor() {
}

void GetSynchronizedSequenceResp::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GetSynchronizedSequenceResp::descriptor() {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[27].descriptor;
}

const GetSynchronizedSequenceResp& GetSynchronizedSequenceResp::default_instance() {
  protobuf_friendship_2eproto::InitDefaults();
  return *internal_default_instance();
}

GetSynchronizedSequenceResp* GetSynchronizedSequenceResp::New(::google::protobuf::Arena* arena) const {
  GetSynchronizedSequenceResp* n = new GetSynchronizedSequenceResp;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void GetSynchronizedSequenceResp::Clear() {
// @@protoc_insertion_point(message_clear_start:ugc.friendship.GetSynchronizedSequenceResp)
  sequence_id_ = GOOGLE_ULONGLONG(0);
}

bool GetSynchronizedSequenceResp::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:ugc.friendship.GetSynchronizedSequenceResp)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint64 sequence_id = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &sequence_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:ugc.friendship.GetSynchronizedSequenceResp)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:ugc.friendship.GetSynchronizedSequenceResp)
  return false;
#undef DO_
}

void GetSynchronizedSequenceResp::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:ugc.friendship.GetSynchronizedSequenceResp)
  // uint64 sequence_id = 1;
  if (this->sequence_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->sequence_id(), output);
  }

  // @@protoc_insertion_point(serialize_end:ugc.friendship.GetSynchronizedSequenceResp)
}

::google::protobuf::uint8* GetSynchronizedSequenceResp::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:ugc.friendship.GetSynchronizedSequenceResp)
  // uint64 sequence_id = 1;
  if (this->sequence_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->sequence_id(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:ugc.friendship.GetSynchronizedSequenceResp)
  return target;
}

size_t GetSynchronizedSequenceResp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ugc.friendship.GetSynchronizedSequenceResp)
  size_t total_size = 0;

  // uint64 sequence_id = 1;
  if (this->sequence_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->sequence_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GetSynchronizedSequenceResp::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:ugc.friendship.GetSynchronizedSequenceResp)
  GOOGLE_DCHECK_NE(&from, this);
  const GetSynchronizedSequenceResp* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GetSynchronizedSequenceResp>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:ugc.friendship.GetSynchronizedSequenceResp)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:ugc.friendship.GetSynchronizedSequenceResp)
    MergeFrom(*source);
  }
}

void GetSynchronizedSequenceResp::MergeFrom(const GetSynchronizedSequenceResp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ugc.friendship.GetSynchronizedSequenceResp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.sequence_id() != 0) {
    set_sequence_id(from.sequence_id());
  }
}

void GetSynchronizedSequenceResp::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:ugc.friendship.GetSynchronizedSequenceResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GetSynchronizedSequenceResp::CopyFrom(const GetSynchronizedSequenceResp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ugc.friendship.GetSynchronizedSequenceResp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetSynchronizedSequenceResp::IsInitialized() const {
  return true;
}

void GetSynchronizedSequenceResp::Swap(GetSynchronizedSequenceResp* other) {
  if (other == this) return;
  InternalSwap(other);
}
void GetSynchronizedSequenceResp::InternalSwap(GetSynchronizedSequenceResp* other) {
  std::swap(sequence_id_, other->sequence_id_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata GetSynchronizedSequenceResp::GetMetadata() const {
  protobuf_friendship_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_friendship_2eproto::file_level_metadata[27];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// GetSynchronizedSequenceResp

// uint64 sequence_id = 1;
void GetSynchronizedSequenceResp::clear_sequence_id() {
  sequence_id_ = GOOGLE_ULONGLONG(0);
}
::google::protobuf::uint64 GetSynchronizedSequenceResp::sequence_id() const {
  // @@protoc_insertion_point(field_get:ugc.friendship.GetSynchronizedSequenceResp.sequence_id)
  return sequence_id_;
}
void GetSynchronizedSequenceResp::set_sequence_id(::google::protobuf::uint64 value) {
  
  sequence_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.friendship.GetSynchronizedSequenceResp.sequence_id)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace friendship
}  // namespace ugc

// @@protoc_insertion_point(global_scope)
