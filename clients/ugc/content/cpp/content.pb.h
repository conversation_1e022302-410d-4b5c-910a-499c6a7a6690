// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: content.proto

#ifndef PROTOBUF_content_2eproto__INCLUDED
#define PROTOBUF_content_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3002000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3002000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
namespace ugc {
namespace content {
class AddAttitudeReq;
class AddAttitudeReqDefaultTypeInternal;
extern AddAttitudeReqDefaultTypeInternal _AddAttitudeReq_default_instance_;
class AddAttitudeResp;
class AddAttitudeRespDefaultTypeInternal;
extern AddAttitudeRespDefaultTypeInternal _AddAttitudeResp_default_instance_;
class AddCommentReq;
class AddCommentReqDefaultTypeInternal;
extern AddCommentReqDefaultTypeInternal _AddCommentReq_default_instance_;
class AddCommentResp;
class AddCommentRespDefaultTypeInternal;
extern AddCommentRespDefaultTypeInternal _AddCommentResp_default_instance_;
class AddPostDirectlyReq;
class AddPostDirectlyReqDefaultTypeInternal;
extern AddPostDirectlyReqDefaultTypeInternal _AddPostDirectlyReq_default_instance_;
class AddPostDirectlyResp;
class AddPostDirectlyRespDefaultTypeInternal;
extern AddPostDirectlyRespDefaultTypeInternal _AddPostDirectlyResp_default_instance_;
class AddPostReq;
class AddPostReqDefaultTypeInternal;
extern AddPostReqDefaultTypeInternal _AddPostReq_default_instance_;
class AddPostResp;
class AddPostRespDefaultTypeInternal;
extern AddPostRespDefaultTypeInternal _AddPostResp_default_instance_;
class AppReportReq;
class AppReportReqDefaultTypeInternal;
extern AppReportReqDefaultTypeInternal _AppReportReq_default_instance_;
class AppReportResp;
class AppReportRespDefaultTypeInternal;
extern AppReportRespDefaultTypeInternal _AppReportResp_default_instance_;
class AttachmentInfo;
class AttachmentInfoDefaultTypeInternal;
extern AttachmentInfoDefaultTypeInternal _AttachmentInfo_default_instance_;
class AttitudeUserInfo;
class AttitudeUserInfoDefaultTypeInternal;
extern AttitudeUserInfoDefaultTypeInternal _AttitudeUserInfo_default_instance_;
class BanCommentByIdReq;
class BanCommentByIdReqDefaultTypeInternal;
extern BanCommentByIdReqDefaultTypeInternal _BanCommentByIdReq_default_instance_;
class BanCommentByIdResp;
class BanCommentByIdRespDefaultTypeInternal;
extern BanCommentByIdRespDefaultTypeInternal _BanCommentByIdResp_default_instance_;
class BanPostByIdReq;
class BanPostByIdReqDefaultTypeInternal;
extern BanPostByIdReqDefaultTypeInternal _BanPostByIdReq_default_instance_;
class BanPostByIdResp;
class BanPostByIdRespDefaultTypeInternal;
extern BanPostByIdRespDefaultTypeInternal _BanPostByIdResp_default_instance_;
class BatchGetCommentByIdsReq;
class BatchGetCommentByIdsReqDefaultTypeInternal;
extern BatchGetCommentByIdsReqDefaultTypeInternal _BatchGetCommentByIdsReq_default_instance_;
class BatchGetCommentByIdsResp;
class BatchGetCommentByIdsRespDefaultTypeInternal;
extern BatchGetCommentByIdsRespDefaultTypeInternal _BatchGetCommentByIdsResp_default_instance_;
class BatchGetPostListByIdReq;
class BatchGetPostListByIdReqDefaultTypeInternal;
extern BatchGetPostListByIdReqDefaultTypeInternal _BatchGetPostListByIdReq_default_instance_;
class BatchGetPostListByIdResp;
class BatchGetPostListByIdRespDefaultTypeInternal;
extern BatchGetPostListByIdRespDefaultTypeInternal _BatchGetPostListByIdResp_default_instance_;
class CommentInfo;
class CommentInfoDefaultTypeInternal;
extern CommentInfoDefaultTypeInternal _CommentInfo_default_instance_;
class DelAttitudeReq;
class DelAttitudeReqDefaultTypeInternal;
extern DelAttitudeReqDefaultTypeInternal _DelAttitudeReq_default_instance_;
class DelAttitudeResp;
class DelAttitudeRespDefaultTypeInternal;
extern DelAttitudeRespDefaultTypeInternal _DelAttitudeResp_default_instance_;
class DelCommentReq;
class DelCommentReqDefaultTypeInternal;
extern DelCommentReqDefaultTypeInternal _DelCommentReq_default_instance_;
class DelCommentResp;
class DelCommentRespDefaultTypeInternal;
extern DelCommentRespDefaultTypeInternal _DelCommentResp_default_instance_;
class DelPostReq;
class DelPostReqDefaultTypeInternal;
extern DelPostReqDefaultTypeInternal _DelPostReq_default_instance_;
class DelPostResp;
class DelPostRespDefaultTypeInternal;
extern DelPostRespDefaultTypeInternal _DelPostResp_default_instance_;
class GenerateNewPostIdReq;
class GenerateNewPostIdReqDefaultTypeInternal;
extern GenerateNewPostIdReqDefaultTypeInternal _GenerateNewPostIdReq_default_instance_;
class GenerateNewPostIdResp;
class GenerateNewPostIdRespDefaultTypeInternal;
extern GenerateNewPostIdRespDefaultTypeInternal _GenerateNewPostIdResp_default_instance_;
class GetAttitudeUserListReq;
class GetAttitudeUserListReqDefaultTypeInternal;
extern GetAttitudeUserListReqDefaultTypeInternal _GetAttitudeUserListReq_default_instance_;
class GetAttitudeUserListResp;
class GetAttitudeUserListRespDefaultTypeInternal;
extern GetAttitudeUserListRespDefaultTypeInternal _GetAttitudeUserListResp_default_instance_;
class GetCommentByIdReq;
class GetCommentByIdReqDefaultTypeInternal;
extern GetCommentByIdReqDefaultTypeInternal _GetCommentByIdReq_default_instance_;
class GetCommentByIdResp;
class GetCommentByIdRespDefaultTypeInternal;
extern GetCommentByIdRespDefaultTypeInternal _GetCommentByIdResp_default_instance_;
class GetCommentListReq;
class GetCommentListReqDefaultTypeInternal;
extern GetCommentListReqDefaultTypeInternal _GetCommentListReq_default_instance_;
class GetCommentListResp;
class GetCommentListRespDefaultTypeInternal;
extern GetCommentListRespDefaultTypeInternal _GetCommentListResp_default_instance_;
class GetPostByIdReq;
class GetPostByIdReqDefaultTypeInternal;
extern GetPostByIdReqDefaultTypeInternal _GetPostByIdReq_default_instance_;
class GetPostByIdResp;
class GetPostByIdRespDefaultTypeInternal;
extern GetPostByIdRespDefaultTypeInternal _GetPostByIdResp_default_instance_;
class MarkAttachmentUploadedReq;
class MarkAttachmentUploadedReqDefaultTypeInternal;
extern MarkAttachmentUploadedReqDefaultTypeInternal _MarkAttachmentUploadedReq_default_instance_;
class MarkAttachmentUploadedResp;
class MarkAttachmentUploadedRespDefaultTypeInternal;
extern MarkAttachmentUploadedRespDefaultTypeInternal _MarkAttachmentUploadedResp_default_instance_;
class PostInfo;
class PostInfoDefaultTypeInternal;
extern PostInfoDefaultTypeInternal _PostInfo_default_instance_;
class ReportPostShareReq;
class ReportPostShareReqDefaultTypeInternal;
extern ReportPostShareReqDefaultTypeInternal _ReportPostShareReq_default_instance_;
class ReportPostShareResp;
class ReportPostShareRespDefaultTypeInternal;
extern ReportPostShareRespDefaultTypeInternal _ReportPostShareResp_default_instance_;
class ReportPostViewReq;
class ReportPostViewReqDefaultTypeInternal;
extern ReportPostViewReqDefaultTypeInternal _ReportPostViewReq_default_instance_;
class ReportPostViewResp;
class ReportPostViewRespDefaultTypeInternal;
extern ReportPostViewRespDefaultTypeInternal _ReportPostViewResp_default_instance_;
class UpdateAttachmentStatusReq;
class UpdateAttachmentStatusReqDefaultTypeInternal;
extern UpdateAttachmentStatusReqDefaultTypeInternal _UpdateAttachmentStatusReq_default_instance_;
class UpdateAttachmentStatusResp;
class UpdateAttachmentStatusRespDefaultTypeInternal;
extern UpdateAttachmentStatusRespDefaultTypeInternal _UpdateAttachmentStatusResp_default_instance_;
class UpdateVideoUrlReq;
class UpdateVideoUrlReqDefaultTypeInternal;
extern UpdateVideoUrlReqDefaultTypeInternal _UpdateVideoUrlReq_default_instance_;
class UpdateVideoUrlResp;
class UpdateVideoUrlRespDefaultTypeInternal;
extern UpdateVideoUrlRespDefaultTypeInternal _UpdateVideoUrlResp_default_instance_;
}  // namespace content
}  // namespace ugc

namespace ugc {
namespace content {

namespace protobuf_content_2eproto {
// Internal implementation detail -- do not call these.
struct TableStruct {
  static const ::google::protobuf::uint32 offsets[];
  static void InitDefaultsImpl();
  static void Shutdown();
};
void AddDescriptors();
void InitDefaults();
}  // namespace protobuf_content_2eproto

enum AttachmentInfo_AttachmentType {
  AttachmentInfo_AttachmentType_NONE = 0,
  AttachmentInfo_AttachmentType_IMAGE = 1,
  AttachmentInfo_AttachmentType_GIF = 2,
  AttachmentInfo_AttachmentType_VIDEO = 3,
  AttachmentInfo_AttachmentType_CMS = 4,
  AttachmentInfo_AttachmentType_AttachmentInfo_AttachmentType_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  AttachmentInfo_AttachmentType_AttachmentInfo_AttachmentType_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool AttachmentInfo_AttachmentType_IsValid(int value);
const AttachmentInfo_AttachmentType AttachmentInfo_AttachmentType_AttachmentType_MIN = AttachmentInfo_AttachmentType_NONE;
const AttachmentInfo_AttachmentType AttachmentInfo_AttachmentType_AttachmentType_MAX = AttachmentInfo_AttachmentType_CMS;
const int AttachmentInfo_AttachmentType_AttachmentType_ARRAYSIZE = AttachmentInfo_AttachmentType_AttachmentType_MAX + 1;

const ::google::protobuf::EnumDescriptor* AttachmentInfo_AttachmentType_descriptor();
inline const ::std::string& AttachmentInfo_AttachmentType_Name(AttachmentInfo_AttachmentType value) {
  return ::google::protobuf::internal::NameOfEnum(
    AttachmentInfo_AttachmentType_descriptor(), value);
}
inline bool AttachmentInfo_AttachmentType_Parse(
    const ::std::string& name, AttachmentInfo_AttachmentType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<AttachmentInfo_AttachmentType>(
    AttachmentInfo_AttachmentType_descriptor(), name, value);
}
enum PostInfo_PostType {
  PostInfo_PostType_NONE = 0,
  PostInfo_PostType_TEXT = 1,
  PostInfo_PostType_IMAGE = 2,
  PostInfo_PostType_VIDEO = 3,
  PostInfo_PostType_CMS = 4,
  PostInfo_PostType_PostInfo_PostType_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  PostInfo_PostType_PostInfo_PostType_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool PostInfo_PostType_IsValid(int value);
const PostInfo_PostType PostInfo_PostType_PostType_MIN = PostInfo_PostType_NONE;
const PostInfo_PostType PostInfo_PostType_PostType_MAX = PostInfo_PostType_CMS;
const int PostInfo_PostType_PostType_ARRAYSIZE = PostInfo_PostType_PostType_MAX + 1;

const ::google::protobuf::EnumDescriptor* PostInfo_PostType_descriptor();
inline const ::std::string& PostInfo_PostType_Name(PostInfo_PostType value) {
  return ::google::protobuf::internal::NameOfEnum(
    PostInfo_PostType_descriptor(), value);
}
inline bool PostInfo_PostType_Parse(
    const ::std::string& name, PostInfo_PostType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<PostInfo_PostType>(
    PostInfo_PostType_descriptor(), name, value);
}
enum AddPostDirectlyReq_Availability {
  AddPostDirectlyReq_Availability_ALL = 0,
  AddPostDirectlyReq_Availability_ANDROID = 1,
  AddPostDirectlyReq_Availability_IOS = 2,
  AddPostDirectlyReq_Availability_AddPostDirectlyReq_Availability_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  AddPostDirectlyReq_Availability_AddPostDirectlyReq_Availability_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool AddPostDirectlyReq_Availability_IsValid(int value);
const AddPostDirectlyReq_Availability AddPostDirectlyReq_Availability_Availability_MIN = AddPostDirectlyReq_Availability_ALL;
const AddPostDirectlyReq_Availability AddPostDirectlyReq_Availability_Availability_MAX = AddPostDirectlyReq_Availability_IOS;
const int AddPostDirectlyReq_Availability_Availability_ARRAYSIZE = AddPostDirectlyReq_Availability_Availability_MAX + 1;

const ::google::protobuf::EnumDescriptor* AddPostDirectlyReq_Availability_descriptor();
inline const ::std::string& AddPostDirectlyReq_Availability_Name(AddPostDirectlyReq_Availability value) {
  return ::google::protobuf::internal::NameOfEnum(
    AddPostDirectlyReq_Availability_descriptor(), value);
}
inline bool AddPostDirectlyReq_Availability_Parse(
    const ::std::string& name, AddPostDirectlyReq_Availability* value) {
  return ::google::protobuf::internal::ParseNamedEnum<AddPostDirectlyReq_Availability>(
    AddPostDirectlyReq_Availability_descriptor(), name, value);
}
enum ReportPostViewReq_ViewType {
  ReportPostViewReq_ViewType_NONE = 0,
  ReportPostViewReq_ViewType_NEW = 1,
  ReportPostViewReq_ViewType_ReportPostViewReq_ViewType_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  ReportPostViewReq_ViewType_ReportPostViewReq_ViewType_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool ReportPostViewReq_ViewType_IsValid(int value);
const ReportPostViewReq_ViewType ReportPostViewReq_ViewType_ViewType_MIN = ReportPostViewReq_ViewType_NONE;
const ReportPostViewReq_ViewType ReportPostViewReq_ViewType_ViewType_MAX = ReportPostViewReq_ViewType_NEW;
const int ReportPostViewReq_ViewType_ViewType_ARRAYSIZE = ReportPostViewReq_ViewType_ViewType_MAX + 1;

const ::google::protobuf::EnumDescriptor* ReportPostViewReq_ViewType_descriptor();
inline const ::std::string& ReportPostViewReq_ViewType_Name(ReportPostViewReq_ViewType value) {
  return ::google::protobuf::internal::NameOfEnum(
    ReportPostViewReq_ViewType_descriptor(), value);
}
inline bool ReportPostViewReq_ViewType_Parse(
    const ::std::string& name, ReportPostViewReq_ViewType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<ReportPostViewReq_ViewType>(
    ReportPostViewReq_ViewType_descriptor(), name, value);
}
enum ContentStatus {
  CONTENT_STATUS_NONE = 0,
  CONTENT_STATUS_UNDER_REVIEW = 1,
  CONTENT_STATUS_UNDER_REVIEW_AND_PREPARED = 2,
  CONTENT_STATUS_SUSPICIOUS = 3,
  CONTENT_STATUS_ILLEGAL = 4,
  CONTENT_STATUS_NORMAL = 5,
  CONTENT_STATUS_DELETED = 6,
  CONTENT_STATUS_BANNED = 7,
  ContentStatus_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  ContentStatus_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool ContentStatus_IsValid(int value);
const ContentStatus ContentStatus_MIN = CONTENT_STATUS_NONE;
const ContentStatus ContentStatus_MAX = CONTENT_STATUS_BANNED;
const int ContentStatus_ARRAYSIZE = ContentStatus_MAX + 1;

const ::google::protobuf::EnumDescriptor* ContentStatus_descriptor();
inline const ::std::string& ContentStatus_Name(ContentStatus value) {
  return ::google::protobuf::internal::NameOfEnum(
    ContentStatus_descriptor(), value);
}
inline bool ContentStatus_Parse(
    const ::std::string& name, ContentStatus* value) {
  return ::google::protobuf::internal::ParseNamedEnum<ContentStatus>(
    ContentStatus_descriptor(), name, value);
}
// ===================================================================

class AttachmentInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.AttachmentInfo) */ {
 public:
  AttachmentInfo();
  virtual ~AttachmentInfo();

  AttachmentInfo(const AttachmentInfo& from);

  inline AttachmentInfo& operator=(const AttachmentInfo& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AttachmentInfo& default_instance();

  static inline const AttachmentInfo* internal_default_instance() {
    return reinterpret_cast<const AttachmentInfo*>(
               &_AttachmentInfo_default_instance_);
  }

  void Swap(AttachmentInfo* other);

  // implements Message ----------------------------------------------

  inline AttachmentInfo* New() const PROTOBUF_FINAL { return New(NULL); }

  AttachmentInfo* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const AttachmentInfo& from);
  void MergeFrom(const AttachmentInfo& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(AttachmentInfo* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  typedef AttachmentInfo_AttachmentType AttachmentType;
  static const AttachmentType NONE =
    AttachmentInfo_AttachmentType_NONE;
  static const AttachmentType IMAGE =
    AttachmentInfo_AttachmentType_IMAGE;
  static const AttachmentType GIF =
    AttachmentInfo_AttachmentType_GIF;
  static const AttachmentType VIDEO =
    AttachmentInfo_AttachmentType_VIDEO;
  static const AttachmentType CMS =
    AttachmentInfo_AttachmentType_CMS;
  static inline bool AttachmentType_IsValid(int value) {
    return AttachmentInfo_AttachmentType_IsValid(value);
  }
  static const AttachmentType AttachmentType_MIN =
    AttachmentInfo_AttachmentType_AttachmentType_MIN;
  static const AttachmentType AttachmentType_MAX =
    AttachmentInfo_AttachmentType_AttachmentType_MAX;
  static const int AttachmentType_ARRAYSIZE =
    AttachmentInfo_AttachmentType_AttachmentType_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  AttachmentType_descriptor() {
    return AttachmentInfo_AttachmentType_descriptor();
  }
  static inline const ::std::string& AttachmentType_Name(AttachmentType value) {
    return AttachmentInfo_AttachmentType_Name(value);
  }
  static inline bool AttachmentType_Parse(const ::std::string& name,
      AttachmentType* value) {
    return AttachmentInfo_AttachmentType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // string key = 1;
  void clear_key();
  static const int kKeyFieldNumber = 1;
  const ::std::string& key() const;
  void set_key(const ::std::string& value);
  #if LANG_CXX11
  void set_key(::std::string&& value);
  #endif
  void set_key(const char* value);
  void set_key(const char* value, size_t size);
  ::std::string* mutable_key();
  ::std::string* release_key();
  void set_allocated_key(::std::string* key);

  // string content = 3;
  void clear_content();
  static const int kContentFieldNumber = 3;
  const ::std::string& content() const;
  void set_content(const ::std::string& value);
  #if LANG_CXX11
  void set_content(::std::string&& value);
  #endif
  void set_content(const char* value);
  void set_content(const char* value, size_t size);
  ::std::string* mutable_content();
  ::std::string* release_content();
  void set_allocated_content(::std::string* content);

  // string extra = 4;
  void clear_extra();
  static const int kExtraFieldNumber = 4;
  const ::std::string& extra() const;
  void set_extra(const ::std::string& value);
  #if LANG_CXX11
  void set_extra(::std::string&& value);
  #endif
  void set_extra(const char* value);
  void set_extra(const char* value, size_t size);
  ::std::string* mutable_extra();
  ::std::string* release_extra();
  void set_allocated_extra(::std::string* extra);

  // string param = 10;
  void clear_param();
  static const int kParamFieldNumber = 10;
  const ::std::string& param() const;
  void set_param(const ::std::string& value);
  #if LANG_CXX11
  void set_param(::std::string&& value);
  #endif
  void set_param(const char* value);
  void set_param(const char* value, size_t size);
  ::std::string* mutable_param();
  ::std::string* release_param();
  void set_allocated_param(::std::string* param);

  // .ugc.content.AttachmentInfo.AttachmentType type = 2;
  void clear_type();
  static const int kTypeFieldNumber = 2;
  ::ugc::content::AttachmentInfo_AttachmentType type() const;
  void set_type(::ugc::content::AttachmentInfo_AttachmentType value);

  // .ugc.content.ContentStatus status = 5;
  void clear_status();
  static const int kStatusFieldNumber = 5;
  ::ugc::content::ContentStatus status() const;
  void set_status(::ugc::content::ContentStatus value);

  // @@protoc_insertion_point(class_scope:ugc.content.AttachmentInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr key_;
  ::google::protobuf::internal::ArenaStringPtr content_;
  ::google::protobuf::internal::ArenaStringPtr extra_;
  ::google::protobuf::internal::ArenaStringPtr param_;
  int type_;
  int status_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class PostInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.PostInfo) */ {
 public:
  PostInfo();
  virtual ~PostInfo();

  PostInfo(const PostInfo& from);

  inline PostInfo& operator=(const PostInfo& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const PostInfo& default_instance();

  static inline const PostInfo* internal_default_instance() {
    return reinterpret_cast<const PostInfo*>(
               &_PostInfo_default_instance_);
  }

  void Swap(PostInfo* other);

  // implements Message ----------------------------------------------

  inline PostInfo* New() const PROTOBUF_FINAL { return New(NULL); }

  PostInfo* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const PostInfo& from);
  void MergeFrom(const PostInfo& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(PostInfo* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  typedef PostInfo_PostType PostType;
  static const PostType NONE =
    PostInfo_PostType_NONE;
  static const PostType TEXT =
    PostInfo_PostType_TEXT;
  static const PostType IMAGE =
    PostInfo_PostType_IMAGE;
  static const PostType VIDEO =
    PostInfo_PostType_VIDEO;
  static const PostType CMS =
    PostInfo_PostType_CMS;
  static inline bool PostType_IsValid(int value) {
    return PostInfo_PostType_IsValid(value);
  }
  static const PostType PostType_MIN =
    PostInfo_PostType_PostType_MIN;
  static const PostType PostType_MAX =
    PostInfo_PostType_PostType_MAX;
  static const int PostType_ARRAYSIZE =
    PostInfo_PostType_PostType_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  PostType_descriptor() {
    return PostInfo_PostType_descriptor();
  }
  static inline const ::std::string& PostType_Name(PostType value) {
    return PostInfo_PostType_Name(value);
  }
  static inline bool PostType_Parse(const ::std::string& name,
      PostType* value) {
    return PostInfo_PostType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // repeated .ugc.content.AttachmentInfo attachments = 5;
  int attachments_size() const;
  void clear_attachments();
  static const int kAttachmentsFieldNumber = 5;
  const ::ugc::content::AttachmentInfo& attachments(int index) const;
  ::ugc::content::AttachmentInfo* mutable_attachments(int index);
  ::ugc::content::AttachmentInfo* add_attachments();
  ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >*
      mutable_attachments();
  const ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >&
      attachments() const;

  // string post_id = 1;
  void clear_post_id();
  static const int kPostIdFieldNumber = 1;
  const ::std::string& post_id() const;
  void set_post_id(const ::std::string& value);
  #if LANG_CXX11
  void set_post_id(::std::string&& value);
  #endif
  void set_post_id(const char* value);
  void set_post_id(const char* value, size_t size);
  ::std::string* mutable_post_id();
  ::std::string* release_post_id();
  void set_allocated_post_id(::std::string* post_id);

  // string topic_id = 2;
  void clear_topic_id();
  static const int kTopicIdFieldNumber = 2;
  const ::std::string& topic_id() const;
  void set_topic_id(const ::std::string& value);
  #if LANG_CXX11
  void set_topic_id(::std::string&& value);
  #endif
  void set_topic_id(const char* value);
  void set_topic_id(const char* value, size_t size);
  ::std::string* mutable_topic_id();
  ::std::string* release_topic_id();
  void set_allocated_topic_id(::std::string* topic_id);

  // string content = 4;
  void clear_content();
  static const int kContentFieldNumber = 4;
  const ::std::string& content() const;
  void set_content(const ::std::string& value);
  #if LANG_CXX11
  void set_content(::std::string&& value);
  #endif
  void set_content(const char* value);
  void set_content(const char* value, size_t size);
  ::std::string* mutable_content();
  ::std::string* release_content();
  void set_allocated_content(::std::string* content);

  // .ugc.content.PostInfo.PostType post_type = 3;
  void clear_post_type();
  static const int kPostTypeFieldNumber = 3;
  ::ugc::content::PostInfo_PostType post_type() const;
  void set_post_type(::ugc::content::PostInfo_PostType value);

  // uint32 comment_count = 7;
  void clear_comment_count();
  static const int kCommentCountFieldNumber = 7;
  ::google::protobuf::uint32 comment_count() const;
  void set_comment_count(::google::protobuf::uint32 value);

  // uint64 create_at = 6;
  void clear_create_at();
  static const int kCreateAtFieldNumber = 6;
  ::google::protobuf::uint64 create_at() const;
  void set_create_at(::google::protobuf::uint64 value);

  // uint32 attitude_count = 8;
  void clear_attitude_count();
  static const int kAttitudeCountFieldNumber = 8;
  ::google::protobuf::uint32 attitude_count() const;
  void set_attitude_count(::google::protobuf::uint32 value);

  // uint32 view_count = 9;
  void clear_view_count();
  static const int kViewCountFieldNumber = 9;
  ::google::protobuf::uint32 view_count() const;
  void set_view_count(::google::protobuf::uint32 value);

  // uint32 user_id = 10;
  void clear_user_id();
  static const int kUserIdFieldNumber = 10;
  ::google::protobuf::uint32 user_id() const;
  void set_user_id(::google::protobuf::uint32 value);

  // .ugc.content.ContentStatus status = 11;
  void clear_status();
  static const int kStatusFieldNumber = 11;
  ::ugc::content::ContentStatus status() const;
  void set_status(::ugc::content::ContentStatus value);

  // uint32 top_level_comment_count = 12;
  void clear_top_level_comment_count();
  static const int kTopLevelCommentCountFieldNumber = 12;
  ::google::protobuf::uint32 top_level_comment_count() const;
  void set_top_level_comment_count(::google::protobuf::uint32 value);

  // uint32 share_count = 13;
  void clear_share_count();
  static const int kShareCountFieldNumber = 13;
  ::google::protobuf::uint32 share_count() const;
  void set_share_count(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:ugc.content.PostInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo > attachments_;
  ::google::protobuf::internal::ArenaStringPtr post_id_;
  ::google::protobuf::internal::ArenaStringPtr topic_id_;
  ::google::protobuf::internal::ArenaStringPtr content_;
  int post_type_;
  ::google::protobuf::uint32 comment_count_;
  ::google::protobuf::uint64 create_at_;
  ::google::protobuf::uint32 attitude_count_;
  ::google::protobuf::uint32 view_count_;
  ::google::protobuf::uint32 user_id_;
  int status_;
  ::google::protobuf::uint32 top_level_comment_count_;
  ::google::protobuf::uint32 share_count_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class CommentInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.CommentInfo) */ {
 public:
  CommentInfo();
  virtual ~CommentInfo();

  CommentInfo(const CommentInfo& from);

  inline CommentInfo& operator=(const CommentInfo& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CommentInfo& default_instance();

  static inline const CommentInfo* internal_default_instance() {
    return reinterpret_cast<const CommentInfo*>(
               &_CommentInfo_default_instance_);
  }

  void Swap(CommentInfo* other);

  // implements Message ----------------------------------------------

  inline CommentInfo* New() const PROTOBUF_FINAL { return New(NULL); }

  CommentInfo* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const CommentInfo& from);
  void MergeFrom(const CommentInfo& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(CommentInfo* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .ugc.content.AttachmentInfo attachments = 7;
  int attachments_size() const;
  void clear_attachments();
  static const int kAttachmentsFieldNumber = 7;
  const ::ugc::content::AttachmentInfo& attachments(int index) const;
  ::ugc::content::AttachmentInfo* mutable_attachments(int index);
  ::ugc::content::AttachmentInfo* add_attachments();
  ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >*
      mutable_attachments();
  const ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >&
      attachments() const;

  // repeated .ugc.content.CommentInfo sub_comments = 11;
  int sub_comments_size() const;
  void clear_sub_comments();
  static const int kSubCommentsFieldNumber = 11;
  const ::ugc::content::CommentInfo& sub_comments(int index) const;
  ::ugc::content::CommentInfo* mutable_sub_comments(int index);
  ::ugc::content::CommentInfo* add_sub_comments();
  ::google::protobuf::RepeatedPtrField< ::ugc::content::CommentInfo >*
      mutable_sub_comments();
  const ::google::protobuf::RepeatedPtrField< ::ugc::content::CommentInfo >&
      sub_comments() const;

  // string comment_id = 1;
  void clear_comment_id();
  static const int kCommentIdFieldNumber = 1;
  const ::std::string& comment_id() const;
  void set_comment_id(const ::std::string& value);
  #if LANG_CXX11
  void set_comment_id(::std::string&& value);
  #endif
  void set_comment_id(const char* value);
  void set_comment_id(const char* value, size_t size);
  ::std::string* mutable_comment_id();
  ::std::string* release_comment_id();
  void set_allocated_comment_id(::std::string* comment_id);

  // string post_id = 2;
  void clear_post_id();
  static const int kPostIdFieldNumber = 2;
  const ::std::string& post_id() const;
  void set_post_id(const ::std::string& value);
  #if LANG_CXX11
  void set_post_id(::std::string&& value);
  #endif
  void set_post_id(const char* value);
  void set_post_id(const char* value, size_t size);
  ::std::string* mutable_post_id();
  ::std::string* release_post_id();
  void set_allocated_post_id(::std::string* post_id);

  // string conversation_id = 3;
  void clear_conversation_id();
  static const int kConversationIdFieldNumber = 3;
  const ::std::string& conversation_id() const;
  void set_conversation_id(const ::std::string& value);
  #if LANG_CXX11
  void set_conversation_id(::std::string&& value);
  #endif
  void set_conversation_id(const char* value);
  void set_conversation_id(const char* value, size_t size);
  ::std::string* mutable_conversation_id();
  ::std::string* release_conversation_id();
  void set_allocated_conversation_id(::std::string* conversation_id);

  // string content = 4;
  void clear_content();
  static const int kContentFieldNumber = 4;
  const ::std::string& content() const;
  void set_content(const ::std::string& value);
  #if LANG_CXX11
  void set_content(::std::string&& value);
  #endif
  void set_content(const char* value);
  void set_content(const char* value, size_t size);
  ::std::string* mutable_content();
  ::std::string* release_content();
  void set_allocated_content(::std::string* content);

  // uint32 user_id = 5;
  void clear_user_id();
  static const int kUserIdFieldNumber = 5;
  ::google::protobuf::uint32 user_id() const;
  void set_user_id(::google::protobuf::uint32 value);

  // uint32 reply_to_user_id = 6;
  void clear_reply_to_user_id();
  static const int kReplyToUserIdFieldNumber = 6;
  ::google::protobuf::uint32 reply_to_user_id() const;
  void set_reply_to_user_id(::google::protobuf::uint32 value);

  // uint32 comment_count = 8;
  void clear_comment_count();
  static const int kCommentCountFieldNumber = 8;
  ::google::protobuf::uint32 comment_count() const;
  void set_comment_count(::google::protobuf::uint32 value);

  // uint32 attitude_count = 9;
  void clear_attitude_count();
  static const int kAttitudeCountFieldNumber = 9;
  ::google::protobuf::uint32 attitude_count() const;
  void set_attitude_count(::google::protobuf::uint32 value);

  // uint64 create_at = 12;
  void clear_create_at();
  static const int kCreateAtFieldNumber = 12;
  ::google::protobuf::uint64 create_at() const;
  void set_create_at(::google::protobuf::uint64 value);

  // .ugc.content.ContentStatus status = 10;
  void clear_status();
  static const int kStatusFieldNumber = 10;
  ::ugc::content::ContentStatus status() const;
  void set_status(::ugc::content::ContentStatus value);

  // @@protoc_insertion_point(class_scope:ugc.content.CommentInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo > attachments_;
  ::google::protobuf::RepeatedPtrField< ::ugc::content::CommentInfo > sub_comments_;
  ::google::protobuf::internal::ArenaStringPtr comment_id_;
  ::google::protobuf::internal::ArenaStringPtr post_id_;
  ::google::protobuf::internal::ArenaStringPtr conversation_id_;
  ::google::protobuf::internal::ArenaStringPtr content_;
  ::google::protobuf::uint32 user_id_;
  ::google::protobuf::uint32 reply_to_user_id_;
  ::google::protobuf::uint32 comment_count_;
  ::google::protobuf::uint32 attitude_count_;
  ::google::protobuf::uint64 create_at_;
  int status_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class BatchGetPostListByIdReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.BatchGetPostListByIdReq) */ {
 public:
  BatchGetPostListByIdReq();
  virtual ~BatchGetPostListByIdReq();

  BatchGetPostListByIdReq(const BatchGetPostListByIdReq& from);

  inline BatchGetPostListByIdReq& operator=(const BatchGetPostListByIdReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BatchGetPostListByIdReq& default_instance();

  static inline const BatchGetPostListByIdReq* internal_default_instance() {
    return reinterpret_cast<const BatchGetPostListByIdReq*>(
               &_BatchGetPostListByIdReq_default_instance_);
  }

  void Swap(BatchGetPostListByIdReq* other);

  // implements Message ----------------------------------------------

  inline BatchGetPostListByIdReq* New() const PROTOBUF_FINAL { return New(NULL); }

  BatchGetPostListByIdReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const BatchGetPostListByIdReq& from);
  void MergeFrom(const BatchGetPostListByIdReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(BatchGetPostListByIdReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string post_id_list = 1;
  int post_id_list_size() const;
  void clear_post_id_list();
  static const int kPostIdListFieldNumber = 1;
  const ::std::string& post_id_list(int index) const;
  ::std::string* mutable_post_id_list(int index);
  void set_post_id_list(int index, const ::std::string& value);
  void set_post_id_list(int index, const char* value);
  void set_post_id_list(int index, const char* value, size_t size);
  ::std::string* add_post_id_list();
  void add_post_id_list(const ::std::string& value);
  void add_post_id_list(const char* value);
  void add_post_id_list(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& post_id_list() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_post_id_list();

  // @@protoc_insertion_point(class_scope:ugc.content.BatchGetPostListByIdReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::std::string> post_id_list_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class BatchGetPostListByIdResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.BatchGetPostListByIdResp) */ {
 public:
  BatchGetPostListByIdResp();
  virtual ~BatchGetPostListByIdResp();

  BatchGetPostListByIdResp(const BatchGetPostListByIdResp& from);

  inline BatchGetPostListByIdResp& operator=(const BatchGetPostListByIdResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BatchGetPostListByIdResp& default_instance();

  static inline const BatchGetPostListByIdResp* internal_default_instance() {
    return reinterpret_cast<const BatchGetPostListByIdResp*>(
               &_BatchGetPostListByIdResp_default_instance_);
  }

  void Swap(BatchGetPostListByIdResp* other);

  // implements Message ----------------------------------------------

  inline BatchGetPostListByIdResp* New() const PROTOBUF_FINAL { return New(NULL); }

  BatchGetPostListByIdResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const BatchGetPostListByIdResp& from);
  void MergeFrom(const BatchGetPostListByIdResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(BatchGetPostListByIdResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .ugc.content.PostInfo post_list = 1;
  int post_list_size() const;
  void clear_post_list();
  static const int kPostListFieldNumber = 1;
  const ::ugc::content::PostInfo& post_list(int index) const;
  ::ugc::content::PostInfo* mutable_post_list(int index);
  ::ugc::content::PostInfo* add_post_list();
  ::google::protobuf::RepeatedPtrField< ::ugc::content::PostInfo >*
      mutable_post_list();
  const ::google::protobuf::RepeatedPtrField< ::ugc::content::PostInfo >&
      post_list() const;

  // @@protoc_insertion_point(class_scope:ugc.content.BatchGetPostListByIdResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::ugc::content::PostInfo > post_list_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AddPostDirectlyReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.AddPostDirectlyReq) */ {
 public:
  AddPostDirectlyReq();
  virtual ~AddPostDirectlyReq();

  AddPostDirectlyReq(const AddPostDirectlyReq& from);

  inline AddPostDirectlyReq& operator=(const AddPostDirectlyReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AddPostDirectlyReq& default_instance();

  static inline const AddPostDirectlyReq* internal_default_instance() {
    return reinterpret_cast<const AddPostDirectlyReq*>(
               &_AddPostDirectlyReq_default_instance_);
  }

  void Swap(AddPostDirectlyReq* other);

  // implements Message ----------------------------------------------

  inline AddPostDirectlyReq* New() const PROTOBUF_FINAL { return New(NULL); }

  AddPostDirectlyReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const AddPostDirectlyReq& from);
  void MergeFrom(const AddPostDirectlyReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(AddPostDirectlyReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  typedef AddPostDirectlyReq_Availability Availability;
  static const Availability ALL =
    AddPostDirectlyReq_Availability_ALL;
  static const Availability ANDROID =
    AddPostDirectlyReq_Availability_ANDROID;
  static const Availability IOS =
    AddPostDirectlyReq_Availability_IOS;
  static inline bool Availability_IsValid(int value) {
    return AddPostDirectlyReq_Availability_IsValid(value);
  }
  static const Availability Availability_MIN =
    AddPostDirectlyReq_Availability_Availability_MIN;
  static const Availability Availability_MAX =
    AddPostDirectlyReq_Availability_Availability_MAX;
  static const int Availability_ARRAYSIZE =
    AddPostDirectlyReq_Availability_Availability_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Availability_descriptor() {
    return AddPostDirectlyReq_Availability_descriptor();
  }
  static inline const ::std::string& Availability_Name(Availability value) {
    return AddPostDirectlyReq_Availability_Name(value);
  }
  static inline bool Availability_Parse(const ::std::string& name,
      Availability* value) {
    return AddPostDirectlyReq_Availability_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // repeated .ugc.content.AttachmentInfo attachments = 5;
  int attachments_size() const;
  void clear_attachments();
  static const int kAttachmentsFieldNumber = 5;
  const ::ugc::content::AttachmentInfo& attachments(int index) const;
  ::ugc::content::AttachmentInfo* mutable_attachments(int index);
  ::ugc::content::AttachmentInfo* add_attachments();
  ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >*
      mutable_attachments();
  const ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >&
      attachments() const;

  // string topic_id = 2;
  void clear_topic_id();
  static const int kTopicIdFieldNumber = 2;
  const ::std::string& topic_id() const;
  void set_topic_id(const ::std::string& value);
  #if LANG_CXX11
  void set_topic_id(::std::string&& value);
  #endif
  void set_topic_id(const char* value);
  void set_topic_id(const char* value, size_t size);
  ::std::string* mutable_topic_id();
  ::std::string* release_topic_id();
  void set_allocated_topic_id(::std::string* topic_id);

  // string content = 4;
  void clear_content();
  static const int kContentFieldNumber = 4;
  const ::std::string& content() const;
  void set_content(const ::std::string& value);
  #if LANG_CXX11
  void set_content(::std::string&& value);
  #endif
  void set_content(const char* value);
  void set_content(const char* value, size_t size);
  ::std::string* mutable_content();
  ::std::string* release_content();
  void set_allocated_content(::std::string* content);

  // uint32 user_id = 1;
  void clear_user_id();
  static const int kUserIdFieldNumber = 1;
  ::google::protobuf::uint32 user_id() const;
  void set_user_id(::google::protobuf::uint32 value);

  // .ugc.content.PostInfo.PostType type = 3;
  void clear_type();
  static const int kTypeFieldNumber = 3;
  ::ugc::content::PostInfo_PostType type() const;
  void set_type(::ugc::content::PostInfo_PostType value);

  // uint64 create_at = 6;
  void clear_create_at();
  static const int kCreateAtFieldNumber = 6;
  ::google::protobuf::uint64 create_at() const;
  void set_create_at(::google::protobuf::uint64 value);

  // uint32 availability_mask = 7;
  void clear_availability_mask();
  static const int kAvailabilityMaskFieldNumber = 7;
  ::google::protobuf::uint32 availability_mask() const;
  void set_availability_mask(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:ugc.content.AddPostDirectlyReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo > attachments_;
  ::google::protobuf::internal::ArenaStringPtr topic_id_;
  ::google::protobuf::internal::ArenaStringPtr content_;
  ::google::protobuf::uint32 user_id_;
  int type_;
  ::google::protobuf::uint64 create_at_;
  ::google::protobuf::uint32 availability_mask_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AddPostDirectlyResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.AddPostDirectlyResp) */ {
 public:
  AddPostDirectlyResp();
  virtual ~AddPostDirectlyResp();

  AddPostDirectlyResp(const AddPostDirectlyResp& from);

  inline AddPostDirectlyResp& operator=(const AddPostDirectlyResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AddPostDirectlyResp& default_instance();

  static inline const AddPostDirectlyResp* internal_default_instance() {
    return reinterpret_cast<const AddPostDirectlyResp*>(
               &_AddPostDirectlyResp_default_instance_);
  }

  void Swap(AddPostDirectlyResp* other);

  // implements Message ----------------------------------------------

  inline AddPostDirectlyResp* New() const PROTOBUF_FINAL { return New(NULL); }

  AddPostDirectlyResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const AddPostDirectlyResp& from);
  void MergeFrom(const AddPostDirectlyResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(AddPostDirectlyResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string post_id = 1;
  void clear_post_id();
  static const int kPostIdFieldNumber = 1;
  const ::std::string& post_id() const;
  void set_post_id(const ::std::string& value);
  #if LANG_CXX11
  void set_post_id(::std::string&& value);
  #endif
  void set_post_id(const char* value);
  void set_post_id(const char* value, size_t size);
  ::std::string* mutable_post_id();
  ::std::string* release_post_id();
  void set_allocated_post_id(::std::string* post_id);

  // @@protoc_insertion_point(class_scope:ugc.content.AddPostDirectlyResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr post_id_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GenerateNewPostIdReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.GenerateNewPostIdReq) */ {
 public:
  GenerateNewPostIdReq();
  virtual ~GenerateNewPostIdReq();

  GenerateNewPostIdReq(const GenerateNewPostIdReq& from);

  inline GenerateNewPostIdReq& operator=(const GenerateNewPostIdReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GenerateNewPostIdReq& default_instance();

  static inline const GenerateNewPostIdReq* internal_default_instance() {
    return reinterpret_cast<const GenerateNewPostIdReq*>(
               &_GenerateNewPostIdReq_default_instance_);
  }

  void Swap(GenerateNewPostIdReq* other);

  // implements Message ----------------------------------------------

  inline GenerateNewPostIdReq* New() const PROTOBUF_FINAL { return New(NULL); }

  GenerateNewPostIdReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GenerateNewPostIdReq& from);
  void MergeFrom(const GenerateNewPostIdReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GenerateNewPostIdReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:ugc.content.GenerateNewPostIdReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GenerateNewPostIdResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.GenerateNewPostIdResp) */ {
 public:
  GenerateNewPostIdResp();
  virtual ~GenerateNewPostIdResp();

  GenerateNewPostIdResp(const GenerateNewPostIdResp& from);

  inline GenerateNewPostIdResp& operator=(const GenerateNewPostIdResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GenerateNewPostIdResp& default_instance();

  static inline const GenerateNewPostIdResp* internal_default_instance() {
    return reinterpret_cast<const GenerateNewPostIdResp*>(
               &_GenerateNewPostIdResp_default_instance_);
  }

  void Swap(GenerateNewPostIdResp* other);

  // implements Message ----------------------------------------------

  inline GenerateNewPostIdResp* New() const PROTOBUF_FINAL { return New(NULL); }

  GenerateNewPostIdResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GenerateNewPostIdResp& from);
  void MergeFrom(const GenerateNewPostIdResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GenerateNewPostIdResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string post_id = 1;
  void clear_post_id();
  static const int kPostIdFieldNumber = 1;
  const ::std::string& post_id() const;
  void set_post_id(const ::std::string& value);
  #if LANG_CXX11
  void set_post_id(::std::string&& value);
  #endif
  void set_post_id(const char* value);
  void set_post_id(const char* value, size_t size);
  ::std::string* mutable_post_id();
  ::std::string* release_post_id();
  void set_allocated_post_id(::std::string* post_id);

  // @@protoc_insertion_point(class_scope:ugc.content.GenerateNewPostIdResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr post_id_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AddPostReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.AddPostReq) */ {
 public:
  AddPostReq();
  virtual ~AddPostReq();

  AddPostReq(const AddPostReq& from);

  inline AddPostReq& operator=(const AddPostReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AddPostReq& default_instance();

  static inline const AddPostReq* internal_default_instance() {
    return reinterpret_cast<const AddPostReq*>(
               &_AddPostReq_default_instance_);
  }

  void Swap(AddPostReq* other);

  // implements Message ----------------------------------------------

  inline AddPostReq* New() const PROTOBUF_FINAL { return New(NULL); }

  AddPostReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const AddPostReq& from);
  void MergeFrom(const AddPostReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(AddPostReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string topic_id = 2;
  void clear_topic_id();
  static const int kTopicIdFieldNumber = 2;
  const ::std::string& topic_id() const;
  void set_topic_id(const ::std::string& value);
  #if LANG_CXX11
  void set_topic_id(::std::string&& value);
  #endif
  void set_topic_id(const char* value);
  void set_topic_id(const char* value, size_t size);
  ::std::string* mutable_topic_id();
  ::std::string* release_topic_id();
  void set_allocated_topic_id(::std::string* topic_id);

  // string content = 4;
  void clear_content();
  static const int kContentFieldNumber = 4;
  const ::std::string& content() const;
  void set_content(const ::std::string& value);
  #if LANG_CXX11
  void set_content(::std::string&& value);
  #endif
  void set_content(const char* value);
  void set_content(const char* value, size_t size);
  ::std::string* mutable_content();
  ::std::string* release_content();
  void set_allocated_content(::std::string* content);

  // string antispam_label_info = 8;
  void clear_antispam_label_info();
  static const int kAntispamLabelInfoFieldNumber = 8;
  const ::std::string& antispam_label_info() const;
  void set_antispam_label_info(const ::std::string& value);
  #if LANG_CXX11
  void set_antispam_label_info(::std::string&& value);
  #endif
  void set_antispam_label_info(const char* value);
  void set_antispam_label_info(const char* value, size_t size);
  ::std::string* mutable_antispam_label_info();
  ::std::string* release_antispam_label_info();
  void set_allocated_antispam_label_info(::std::string* antispam_label_info);

  // string device_id = 20;
  void clear_device_id();
  static const int kDeviceIdFieldNumber = 20;
  const ::std::string& device_id() const;
  void set_device_id(const ::std::string& value);
  #if LANG_CXX11
  void set_device_id(::std::string&& value);
  #endif
  void set_device_id(const char* value);
  void set_device_id(const char* value, size_t size);
  ::std::string* mutable_device_id();
  ::std::string* release_device_id();
  void set_allocated_device_id(::std::string* device_id);

  // uint32 user_id = 1;
  void clear_user_id();
  static const int kUserIdFieldNumber = 1;
  ::google::protobuf::uint32 user_id() const;
  void set_user_id(::google::protobuf::uint32 value);

  // .ugc.content.PostInfo.PostType type = 3;
  void clear_type();
  static const int kTypeFieldNumber = 3;
  ::ugc::content::PostInfo_PostType type() const;
  void set_type(::ugc::content::PostInfo_PostType value);

  // uint32 attachment_image_count = 5;
  void clear_attachment_image_count();
  static const int kAttachmentImageCountFieldNumber = 5;
  ::google::protobuf::uint32 attachment_image_count() const;
  void set_attachment_image_count(::google::protobuf::uint32 value);

  // uint32 attachment_video_count = 6;
  void clear_attachment_video_count();
  static const int kAttachmentVideoCountFieldNumber = 6;
  ::google::protobuf::uint32 attachment_video_count() const;
  void set_attachment_video_count(::google::protobuf::uint32 value);

  // .ugc.content.ContentStatus status = 7;
  void clear_status();
  static const int kStatusFieldNumber = 7;
  ::ugc::content::ContentStatus status() const;
  void set_status(::ugc::content::ContentStatus value);

  // uint32 platform = 21;
  void clear_platform();
  static const int kPlatformFieldNumber = 21;
  ::google::protobuf::uint32 platform() const;
  void set_platform(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:ugc.content.AddPostReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr topic_id_;
  ::google::protobuf::internal::ArenaStringPtr content_;
  ::google::protobuf::internal::ArenaStringPtr antispam_label_info_;
  ::google::protobuf::internal::ArenaStringPtr device_id_;
  ::google::protobuf::uint32 user_id_;
  int type_;
  ::google::protobuf::uint32 attachment_image_count_;
  ::google::protobuf::uint32 attachment_video_count_;
  int status_;
  ::google::protobuf::uint32 platform_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AddPostResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.AddPostResp) */ {
 public:
  AddPostResp();
  virtual ~AddPostResp();

  AddPostResp(const AddPostResp& from);

  inline AddPostResp& operator=(const AddPostResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AddPostResp& default_instance();

  static inline const AddPostResp* internal_default_instance() {
    return reinterpret_cast<const AddPostResp*>(
               &_AddPostResp_default_instance_);
  }

  void Swap(AddPostResp* other);

  // implements Message ----------------------------------------------

  inline AddPostResp* New() const PROTOBUF_FINAL { return New(NULL); }

  AddPostResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const AddPostResp& from);
  void MergeFrom(const AddPostResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(AddPostResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string attachment_image_keys = 10;
  int attachment_image_keys_size() const;
  void clear_attachment_image_keys();
  static const int kAttachmentImageKeysFieldNumber = 10;
  const ::std::string& attachment_image_keys(int index) const;
  ::std::string* mutable_attachment_image_keys(int index);
  void set_attachment_image_keys(int index, const ::std::string& value);
  void set_attachment_image_keys(int index, const char* value);
  void set_attachment_image_keys(int index, const char* value, size_t size);
  ::std::string* add_attachment_image_keys();
  void add_attachment_image_keys(const ::std::string& value);
  void add_attachment_image_keys(const char* value);
  void add_attachment_image_keys(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& attachment_image_keys() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_attachment_image_keys();

  // repeated string attachment_video_keys = 11;
  int attachment_video_keys_size() const;
  void clear_attachment_video_keys();
  static const int kAttachmentVideoKeysFieldNumber = 11;
  const ::std::string& attachment_video_keys(int index) const;
  ::std::string* mutable_attachment_video_keys(int index);
  void set_attachment_video_keys(int index, const ::std::string& value);
  void set_attachment_video_keys(int index, const char* value);
  void set_attachment_video_keys(int index, const char* value, size_t size);
  ::std::string* add_attachment_video_keys();
  void add_attachment_video_keys(const ::std::string& value);
  void add_attachment_video_keys(const char* value);
  void add_attachment_video_keys(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& attachment_video_keys() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_attachment_video_keys();

  // string post_id = 1;
  void clear_post_id();
  static const int kPostIdFieldNumber = 1;
  const ::std::string& post_id() const;
  void set_post_id(const ::std::string& value);
  #if LANG_CXX11
  void set_post_id(::std::string&& value);
  #endif
  void set_post_id(const char* value);
  void set_post_id(const char* value, size_t size);
  ::std::string* mutable_post_id();
  ::std::string* release_post_id();
  void set_allocated_post_id(::std::string* post_id);

  // string image_upload_token = 12;
  void clear_image_upload_token();
  static const int kImageUploadTokenFieldNumber = 12;
  const ::std::string& image_upload_token() const;
  void set_image_upload_token(const ::std::string& value);
  #if LANG_CXX11
  void set_image_upload_token(::std::string&& value);
  #endif
  void set_image_upload_token(const char* value);
  void set_image_upload_token(const char* value, size_t size);
  ::std::string* mutable_image_upload_token();
  ::std::string* release_image_upload_token();
  void set_allocated_image_upload_token(::std::string* image_upload_token);

  // string video_upload_token = 13;
  void clear_video_upload_token();
  static const int kVideoUploadTokenFieldNumber = 13;
  const ::std::string& video_upload_token() const;
  void set_video_upload_token(const ::std::string& value);
  #if LANG_CXX11
  void set_video_upload_token(::std::string&& value);
  #endif
  void set_video_upload_token(const char* value);
  void set_video_upload_token(const char* value, size_t size);
  ::std::string* mutable_video_upload_token();
  ::std::string* release_video_upload_token();
  void set_allocated_video_upload_token(::std::string* video_upload_token);

  // uint64 post_create_at = 3;
  void clear_post_create_at();
  static const int kPostCreateAtFieldNumber = 3;
  ::google::protobuf::uint64 post_create_at() const;
  void set_post_create_at(::google::protobuf::uint64 value);

  // .ugc.content.ContentStatus status = 2;
  void clear_status();
  static const int kStatusFieldNumber = 2;
  ::ugc::content::ContentStatus status() const;
  void set_status(::ugc::content::ContentStatus value);

  // @@protoc_insertion_point(class_scope:ugc.content.AddPostResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::std::string> attachment_image_keys_;
  ::google::protobuf::RepeatedPtrField< ::std::string> attachment_video_keys_;
  ::google::protobuf::internal::ArenaStringPtr post_id_;
  ::google::protobuf::internal::ArenaStringPtr image_upload_token_;
  ::google::protobuf::internal::ArenaStringPtr video_upload_token_;
  ::google::protobuf::uint64 post_create_at_;
  int status_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class MarkAttachmentUploadedReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.MarkAttachmentUploadedReq) */ {
 public:
  MarkAttachmentUploadedReq();
  virtual ~MarkAttachmentUploadedReq();

  MarkAttachmentUploadedReq(const MarkAttachmentUploadedReq& from);

  inline MarkAttachmentUploadedReq& operator=(const MarkAttachmentUploadedReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MarkAttachmentUploadedReq& default_instance();

  static inline const MarkAttachmentUploadedReq* internal_default_instance() {
    return reinterpret_cast<const MarkAttachmentUploadedReq*>(
               &_MarkAttachmentUploadedReq_default_instance_);
  }

  void Swap(MarkAttachmentUploadedReq* other);

  // implements Message ----------------------------------------------

  inline MarkAttachmentUploadedReq* New() const PROTOBUF_FINAL { return New(NULL); }

  MarkAttachmentUploadedReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const MarkAttachmentUploadedReq& from);
  void MergeFrom(const MarkAttachmentUploadedReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(MarkAttachmentUploadedReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .ugc.content.AttachmentInfo attachment_info_list = 3;
  int attachment_info_list_size() const;
  void clear_attachment_info_list();
  static const int kAttachmentInfoListFieldNumber = 3;
  const ::ugc::content::AttachmentInfo& attachment_info_list(int index) const;
  ::ugc::content::AttachmentInfo* mutable_attachment_info_list(int index);
  ::ugc::content::AttachmentInfo* add_attachment_info_list();
  ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >*
      mutable_attachment_info_list();
  const ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >&
      attachment_info_list() const;

  // string post_id = 1;
  void clear_post_id();
  static const int kPostIdFieldNumber = 1;
  const ::std::string& post_id() const;
  void set_post_id(const ::std::string& value);
  #if LANG_CXX11
  void set_post_id(::std::string&& value);
  #endif
  void set_post_id(const char* value);
  void set_post_id(const char* value, size_t size);
  ::std::string* mutable_post_id();
  ::std::string* release_post_id();
  void set_allocated_post_id(::std::string* post_id);

  // string comment_id = 2;
  void clear_comment_id();
  static const int kCommentIdFieldNumber = 2;
  const ::std::string& comment_id() const;
  void set_comment_id(const ::std::string& value);
  #if LANG_CXX11
  void set_comment_id(::std::string&& value);
  #endif
  void set_comment_id(const char* value);
  void set_comment_id(const char* value, size_t size);
  ::std::string* mutable_comment_id();
  ::std::string* release_comment_id();
  void set_allocated_comment_id(::std::string* comment_id);

  // @@protoc_insertion_point(class_scope:ugc.content.MarkAttachmentUploadedReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo > attachment_info_list_;
  ::google::protobuf::internal::ArenaStringPtr post_id_;
  ::google::protobuf::internal::ArenaStringPtr comment_id_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class MarkAttachmentUploadedResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.MarkAttachmentUploadedResp) */ {
 public:
  MarkAttachmentUploadedResp();
  virtual ~MarkAttachmentUploadedResp();

  MarkAttachmentUploadedResp(const MarkAttachmentUploadedResp& from);

  inline MarkAttachmentUploadedResp& operator=(const MarkAttachmentUploadedResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MarkAttachmentUploadedResp& default_instance();

  static inline const MarkAttachmentUploadedResp* internal_default_instance() {
    return reinterpret_cast<const MarkAttachmentUploadedResp*>(
               &_MarkAttachmentUploadedResp_default_instance_);
  }

  void Swap(MarkAttachmentUploadedResp* other);

  // implements Message ----------------------------------------------

  inline MarkAttachmentUploadedResp* New() const PROTOBUF_FINAL { return New(NULL); }

  MarkAttachmentUploadedResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const MarkAttachmentUploadedResp& from);
  void MergeFrom(const MarkAttachmentUploadedResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(MarkAttachmentUploadedResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // uint64 post_create_at = 1;
  void clear_post_create_at();
  static const int kPostCreateAtFieldNumber = 1;
  ::google::protobuf::uint64 post_create_at() const;
  void set_post_create_at(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:ugc.content.MarkAttachmentUploadedResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::uint64 post_create_at_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class BanPostByIdReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.BanPostByIdReq) */ {
 public:
  BanPostByIdReq();
  virtual ~BanPostByIdReq();

  BanPostByIdReq(const BanPostByIdReq& from);

  inline BanPostByIdReq& operator=(const BanPostByIdReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BanPostByIdReq& default_instance();

  static inline const BanPostByIdReq* internal_default_instance() {
    return reinterpret_cast<const BanPostByIdReq*>(
               &_BanPostByIdReq_default_instance_);
  }

  void Swap(BanPostByIdReq* other);

  // implements Message ----------------------------------------------

  inline BanPostByIdReq* New() const PROTOBUF_FINAL { return New(NULL); }

  BanPostByIdReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const BanPostByIdReq& from);
  void MergeFrom(const BanPostByIdReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(BanPostByIdReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string post_id = 1;
  void clear_post_id();
  static const int kPostIdFieldNumber = 1;
  const ::std::string& post_id() const;
  void set_post_id(const ::std::string& value);
  #if LANG_CXX11
  void set_post_id(::std::string&& value);
  #endif
  void set_post_id(const char* value);
  void set_post_id(const char* value, size_t size);
  ::std::string* mutable_post_id();
  ::std::string* release_post_id();
  void set_allocated_post_id(::std::string* post_id);

  // bool is_ban = 2;
  void clear_is_ban();
  static const int kIsBanFieldNumber = 2;
  bool is_ban() const;
  void set_is_ban(bool value);

  // @@protoc_insertion_point(class_scope:ugc.content.BanPostByIdReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr post_id_;
  bool is_ban_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class BanPostByIdResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.BanPostByIdResp) */ {
 public:
  BanPostByIdResp();
  virtual ~BanPostByIdResp();

  BanPostByIdResp(const BanPostByIdResp& from);

  inline BanPostByIdResp& operator=(const BanPostByIdResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BanPostByIdResp& default_instance();

  static inline const BanPostByIdResp* internal_default_instance() {
    return reinterpret_cast<const BanPostByIdResp*>(
               &_BanPostByIdResp_default_instance_);
  }

  void Swap(BanPostByIdResp* other);

  // implements Message ----------------------------------------------

  inline BanPostByIdResp* New() const PROTOBUF_FINAL { return New(NULL); }

  BanPostByIdResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const BanPostByIdResp& from);
  void MergeFrom(const BanPostByIdResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(BanPostByIdResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:ugc.content.BanPostByIdResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DelPostReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.DelPostReq) */ {
 public:
  DelPostReq();
  virtual ~DelPostReq();

  DelPostReq(const DelPostReq& from);

  inline DelPostReq& operator=(const DelPostReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DelPostReq& default_instance();

  static inline const DelPostReq* internal_default_instance() {
    return reinterpret_cast<const DelPostReq*>(
               &_DelPostReq_default_instance_);
  }

  void Swap(DelPostReq* other);

  // implements Message ----------------------------------------------

  inline DelPostReq* New() const PROTOBUF_FINAL { return New(NULL); }

  DelPostReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const DelPostReq& from);
  void MergeFrom(const DelPostReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(DelPostReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string post_id = 1;
  void clear_post_id();
  static const int kPostIdFieldNumber = 1;
  const ::std::string& post_id() const;
  void set_post_id(const ::std::string& value);
  #if LANG_CXX11
  void set_post_id(::std::string&& value);
  #endif
  void set_post_id(const char* value);
  void set_post_id(const char* value, size_t size);
  ::std::string* mutable_post_id();
  ::std::string* release_post_id();
  void set_allocated_post_id(::std::string* post_id);

  // @@protoc_insertion_point(class_scope:ugc.content.DelPostReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr post_id_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DelPostResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.DelPostResp) */ {
 public:
  DelPostResp();
  virtual ~DelPostResp();

  DelPostResp(const DelPostResp& from);

  inline DelPostResp& operator=(const DelPostResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DelPostResp& default_instance();

  static inline const DelPostResp* internal_default_instance() {
    return reinterpret_cast<const DelPostResp*>(
               &_DelPostResp_default_instance_);
  }

  void Swap(DelPostResp* other);

  // implements Message ----------------------------------------------

  inline DelPostResp* New() const PROTOBUF_FINAL { return New(NULL); }

  DelPostResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const DelPostResp& from);
  void MergeFrom(const DelPostResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(DelPostResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:ugc.content.DelPostResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GetPostByIdReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.GetPostByIdReq) */ {
 public:
  GetPostByIdReq();
  virtual ~GetPostByIdReq();

  GetPostByIdReq(const GetPostByIdReq& from);

  inline GetPostByIdReq& operator=(const GetPostByIdReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetPostByIdReq& default_instance();

  static inline const GetPostByIdReq* internal_default_instance() {
    return reinterpret_cast<const GetPostByIdReq*>(
               &_GetPostByIdReq_default_instance_);
  }

  void Swap(GetPostByIdReq* other);

  // implements Message ----------------------------------------------

  inline GetPostByIdReq* New() const PROTOBUF_FINAL { return New(NULL); }

  GetPostByIdReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetPostByIdReq& from);
  void MergeFrom(const GetPostByIdReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetPostByIdReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string post_id = 1;
  void clear_post_id();
  static const int kPostIdFieldNumber = 1;
  const ::std::string& post_id() const;
  void set_post_id(const ::std::string& value);
  #if LANG_CXX11
  void set_post_id(::std::string&& value);
  #endif
  void set_post_id(const char* value);
  void set_post_id(const char* value, size_t size);
  ::std::string* mutable_post_id();
  ::std::string* release_post_id();
  void set_allocated_post_id(::std::string* post_id);

  // @@protoc_insertion_point(class_scope:ugc.content.GetPostByIdReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr post_id_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GetPostByIdResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.GetPostByIdResp) */ {
 public:
  GetPostByIdResp();
  virtual ~GetPostByIdResp();

  GetPostByIdResp(const GetPostByIdResp& from);

  inline GetPostByIdResp& operator=(const GetPostByIdResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetPostByIdResp& default_instance();

  static inline const GetPostByIdResp* internal_default_instance() {
    return reinterpret_cast<const GetPostByIdResp*>(
               &_GetPostByIdResp_default_instance_);
  }

  void Swap(GetPostByIdResp* other);

  // implements Message ----------------------------------------------

  inline GetPostByIdResp* New() const PROTOBUF_FINAL { return New(NULL); }

  GetPostByIdResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetPostByIdResp& from);
  void MergeFrom(const GetPostByIdResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetPostByIdResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .ugc.content.PostInfo post = 1;
  bool has_post() const;
  void clear_post();
  static const int kPostFieldNumber = 1;
  const ::ugc::content::PostInfo& post() const;
  ::ugc::content::PostInfo* mutable_post();
  ::ugc::content::PostInfo* release_post();
  void set_allocated_post(::ugc::content::PostInfo* post);

  // @@protoc_insertion_point(class_scope:ugc.content.GetPostByIdResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::ugc::content::PostInfo* post_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class UpdateAttachmentStatusReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.UpdateAttachmentStatusReq) */ {
 public:
  UpdateAttachmentStatusReq();
  virtual ~UpdateAttachmentStatusReq();

  UpdateAttachmentStatusReq(const UpdateAttachmentStatusReq& from);

  inline UpdateAttachmentStatusReq& operator=(const UpdateAttachmentStatusReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const UpdateAttachmentStatusReq& default_instance();

  static inline const UpdateAttachmentStatusReq* internal_default_instance() {
    return reinterpret_cast<const UpdateAttachmentStatusReq*>(
               &_UpdateAttachmentStatusReq_default_instance_);
  }

  void Swap(UpdateAttachmentStatusReq* other);

  // implements Message ----------------------------------------------

  inline UpdateAttachmentStatusReq* New() const PROTOBUF_FINAL { return New(NULL); }

  UpdateAttachmentStatusReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const UpdateAttachmentStatusReq& from);
  void MergeFrom(const UpdateAttachmentStatusReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(UpdateAttachmentStatusReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string post_id = 1;
  void clear_post_id();
  static const int kPostIdFieldNumber = 1;
  const ::std::string& post_id() const;
  void set_post_id(const ::std::string& value);
  #if LANG_CXX11
  void set_post_id(::std::string&& value);
  #endif
  void set_post_id(const char* value);
  void set_post_id(const char* value, size_t size);
  ::std::string* mutable_post_id();
  ::std::string* release_post_id();
  void set_allocated_post_id(::std::string* post_id);

  // string comment_id = 2;
  void clear_comment_id();
  static const int kCommentIdFieldNumber = 2;
  const ::std::string& comment_id() const;
  void set_comment_id(const ::std::string& value);
  #if LANG_CXX11
  void set_comment_id(::std::string&& value);
  #endif
  void set_comment_id(const char* value);
  void set_comment_id(const char* value, size_t size);
  ::std::string* mutable_comment_id();
  ::std::string* release_comment_id();
  void set_allocated_comment_id(::std::string* comment_id);

  // string attachment_key = 3;
  void clear_attachment_key();
  static const int kAttachmentKeyFieldNumber = 3;
  const ::std::string& attachment_key() const;
  void set_attachment_key(const ::std::string& value);
  #if LANG_CXX11
  void set_attachment_key(::std::string&& value);
  #endif
  void set_attachment_key(const char* value);
  void set_attachment_key(const char* value, size_t size);
  ::std::string* mutable_attachment_key();
  ::std::string* release_attachment_key();
  void set_allocated_attachment_key(::std::string* attachment_key);

  // .ugc.content.ContentStatus status = 4;
  void clear_status();
  static const int kStatusFieldNumber = 4;
  ::ugc::content::ContentStatus status() const;
  void set_status(::ugc::content::ContentStatus value);

  // @@protoc_insertion_point(class_scope:ugc.content.UpdateAttachmentStatusReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr post_id_;
  ::google::protobuf::internal::ArenaStringPtr comment_id_;
  ::google::protobuf::internal::ArenaStringPtr attachment_key_;
  int status_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class UpdateAttachmentStatusResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.UpdateAttachmentStatusResp) */ {
 public:
  UpdateAttachmentStatusResp();
  virtual ~UpdateAttachmentStatusResp();

  UpdateAttachmentStatusResp(const UpdateAttachmentStatusResp& from);

  inline UpdateAttachmentStatusResp& operator=(const UpdateAttachmentStatusResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const UpdateAttachmentStatusResp& default_instance();

  static inline const UpdateAttachmentStatusResp* internal_default_instance() {
    return reinterpret_cast<const UpdateAttachmentStatusResp*>(
               &_UpdateAttachmentStatusResp_default_instance_);
  }

  void Swap(UpdateAttachmentStatusResp* other);

  // implements Message ----------------------------------------------

  inline UpdateAttachmentStatusResp* New() const PROTOBUF_FINAL { return New(NULL); }

  UpdateAttachmentStatusResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const UpdateAttachmentStatusResp& from);
  void MergeFrom(const UpdateAttachmentStatusResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(UpdateAttachmentStatusResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:ugc.content.UpdateAttachmentStatusResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class UpdateVideoUrlReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.UpdateVideoUrlReq) */ {
 public:
  UpdateVideoUrlReq();
  virtual ~UpdateVideoUrlReq();

  UpdateVideoUrlReq(const UpdateVideoUrlReq& from);

  inline UpdateVideoUrlReq& operator=(const UpdateVideoUrlReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const UpdateVideoUrlReq& default_instance();

  static inline const UpdateVideoUrlReq* internal_default_instance() {
    return reinterpret_cast<const UpdateVideoUrlReq*>(
               &_UpdateVideoUrlReq_default_instance_);
  }

  void Swap(UpdateVideoUrlReq* other);

  // implements Message ----------------------------------------------

  inline UpdateVideoUrlReq* New() const PROTOBUF_FINAL { return New(NULL); }

  UpdateVideoUrlReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const UpdateVideoUrlReq& from);
  void MergeFrom(const UpdateVideoUrlReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(UpdateVideoUrlReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string post_id = 1;
  void clear_post_id();
  static const int kPostIdFieldNumber = 1;
  const ::std::string& post_id() const;
  void set_post_id(const ::std::string& value);
  #if LANG_CXX11
  void set_post_id(::std::string&& value);
  #endif
  void set_post_id(const char* value);
  void set_post_id(const char* value, size_t size);
  ::std::string* mutable_post_id();
  ::std::string* release_post_id();
  void set_allocated_post_id(::std::string* post_id);

  // string comment_id = 2;
  void clear_comment_id();
  static const int kCommentIdFieldNumber = 2;
  const ::std::string& comment_id() const;
  void set_comment_id(const ::std::string& value);
  #if LANG_CXX11
  void set_comment_id(::std::string&& value);
  #endif
  void set_comment_id(const char* value);
  void set_comment_id(const char* value, size_t size);
  ::std::string* mutable_comment_id();
  ::std::string* release_comment_id();
  void set_allocated_comment_id(::std::string* comment_id);

  // string attachment_key = 3;
  void clear_attachment_key();
  static const int kAttachmentKeyFieldNumber = 3;
  const ::std::string& attachment_key() const;
  void set_attachment_key(const ::std::string& value);
  #if LANG_CXX11
  void set_attachment_key(::std::string&& value);
  #endif
  void set_attachment_key(const char* value);
  void set_attachment_key(const char* value, size_t size);
  ::std::string* mutable_attachment_key();
  ::std::string* release_attachment_key();
  void set_allocated_attachment_key(::std::string* attachment_key);

  // string new_url = 4;
  void clear_new_url();
  static const int kNewUrlFieldNumber = 4;
  const ::std::string& new_url() const;
  void set_new_url(const ::std::string& value);
  #if LANG_CXX11
  void set_new_url(::std::string&& value);
  #endif
  void set_new_url(const char* value);
  void set_new_url(const char* value, size_t size);
  ::std::string* mutable_new_url();
  ::std::string* release_new_url();
  void set_allocated_new_url(::std::string* new_url);

  // @@protoc_insertion_point(class_scope:ugc.content.UpdateVideoUrlReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr post_id_;
  ::google::protobuf::internal::ArenaStringPtr comment_id_;
  ::google::protobuf::internal::ArenaStringPtr attachment_key_;
  ::google::protobuf::internal::ArenaStringPtr new_url_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class UpdateVideoUrlResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.UpdateVideoUrlResp) */ {
 public:
  UpdateVideoUrlResp();
  virtual ~UpdateVideoUrlResp();

  UpdateVideoUrlResp(const UpdateVideoUrlResp& from);

  inline UpdateVideoUrlResp& operator=(const UpdateVideoUrlResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const UpdateVideoUrlResp& default_instance();

  static inline const UpdateVideoUrlResp* internal_default_instance() {
    return reinterpret_cast<const UpdateVideoUrlResp*>(
               &_UpdateVideoUrlResp_default_instance_);
  }

  void Swap(UpdateVideoUrlResp* other);

  // implements Message ----------------------------------------------

  inline UpdateVideoUrlResp* New() const PROTOBUF_FINAL { return New(NULL); }

  UpdateVideoUrlResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const UpdateVideoUrlResp& from);
  void MergeFrom(const UpdateVideoUrlResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(UpdateVideoUrlResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:ugc.content.UpdateVideoUrlResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------


// -------------------------------------------------------------------

class ReportPostViewReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.ReportPostViewReq) */ {
 public:
  ReportPostViewReq();
  virtual ~ReportPostViewReq();

  ReportPostViewReq(const ReportPostViewReq& from);

  inline ReportPostViewReq& operator=(const ReportPostViewReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ReportPostViewReq& default_instance();

  static inline const ReportPostViewReq* internal_default_instance() {
    return reinterpret_cast<const ReportPostViewReq*>(
               &_ReportPostViewReq_default_instance_);
  }

  void Swap(ReportPostViewReq* other);

  // implements Message ----------------------------------------------

  inline ReportPostViewReq* New() const PROTOBUF_FINAL { return New(NULL); }

  ReportPostViewReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ReportPostViewReq& from);
  void MergeFrom(const ReportPostViewReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ReportPostViewReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------


  typedef ReportPostViewReq_ViewType ViewType;
  static const ViewType NONE =
    ReportPostViewReq_ViewType_NONE;
  static const ViewType NEW =
    ReportPostViewReq_ViewType_NEW;
  static inline bool ViewType_IsValid(int value) {
    return ReportPostViewReq_ViewType_IsValid(value);
  }
  static const ViewType ViewType_MIN =
    ReportPostViewReq_ViewType_ViewType_MIN;
  static const ViewType ViewType_MAX =
    ReportPostViewReq_ViewType_ViewType_MAX;
  static const int ViewType_ARRAYSIZE =
    ReportPostViewReq_ViewType_ViewType_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  ViewType_descriptor() {
    return ReportPostViewReq_ViewType_descriptor();
  }
  static inline const ::std::string& ViewType_Name(ViewType value) {
    return ReportPostViewReq_ViewType_Name(value);
  }
  static inline bool ViewType_Parse(const ::std::string& name,
      ViewType* value) {
    return ReportPostViewReq_ViewType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // map<string, .ugc.content.ReportPostViewReq.ViewType> post_ids = 2;
  int post_ids_size() const;
  void clear_post_ids();
  static const int kPostIdsFieldNumber = 2;
  const ::google::protobuf::Map< ::std::string, ::ugc::content::ReportPostViewReq_ViewType >&
      post_ids() const;
  ::google::protobuf::Map< ::std::string, ::ugc::content::ReportPostViewReq_ViewType >*
      mutable_post_ids();

  // uint32 user_id = 1;
  void clear_user_id();
  static const int kUserIdFieldNumber = 1;
  ::google::protobuf::uint32 user_id() const;
  void set_user_id(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:ugc.content.ReportPostViewReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::ugc::content::ReportPostViewReq_ViewType,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 >
      ReportPostViewReq_PostIdsEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::ugc::content::ReportPostViewReq_ViewType,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_ENUM,
      0 > post_ids_;
  ::google::protobuf::uint32 user_id_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ReportPostViewResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.ReportPostViewResp) */ {
 public:
  ReportPostViewResp();
  virtual ~ReportPostViewResp();

  ReportPostViewResp(const ReportPostViewResp& from);

  inline ReportPostViewResp& operator=(const ReportPostViewResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ReportPostViewResp& default_instance();

  static inline const ReportPostViewResp* internal_default_instance() {
    return reinterpret_cast<const ReportPostViewResp*>(
               &_ReportPostViewResp_default_instance_);
  }

  void Swap(ReportPostViewResp* other);

  // implements Message ----------------------------------------------

  inline ReportPostViewResp* New() const PROTOBUF_FINAL { return New(NULL); }

  ReportPostViewResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ReportPostViewResp& from);
  void MergeFrom(const ReportPostViewResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ReportPostViewResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:ugc.content.ReportPostViewResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ReportPostShareReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.ReportPostShareReq) */ {
 public:
  ReportPostShareReq();
  virtual ~ReportPostShareReq();

  ReportPostShareReq(const ReportPostShareReq& from);

  inline ReportPostShareReq& operator=(const ReportPostShareReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ReportPostShareReq& default_instance();

  static inline const ReportPostShareReq* internal_default_instance() {
    return reinterpret_cast<const ReportPostShareReq*>(
               &_ReportPostShareReq_default_instance_);
  }

  void Swap(ReportPostShareReq* other);

  // implements Message ----------------------------------------------

  inline ReportPostShareReq* New() const PROTOBUF_FINAL { return New(NULL); }

  ReportPostShareReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ReportPostShareReq& from);
  void MergeFrom(const ReportPostShareReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ReportPostShareReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string post_id = 2;
  void clear_post_id();
  static const int kPostIdFieldNumber = 2;
  const ::std::string& post_id() const;
  void set_post_id(const ::std::string& value);
  #if LANG_CXX11
  void set_post_id(::std::string&& value);
  #endif
  void set_post_id(const char* value);
  void set_post_id(const char* value, size_t size);
  ::std::string* mutable_post_id();
  ::std::string* release_post_id();
  void set_allocated_post_id(::std::string* post_id);

  // uint32 user_id = 1;
  void clear_user_id();
  static const int kUserIdFieldNumber = 1;
  ::google::protobuf::uint32 user_id() const;
  void set_user_id(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:ugc.content.ReportPostShareReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr post_id_;
  ::google::protobuf::uint32 user_id_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class ReportPostShareResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.ReportPostShareResp) */ {
 public:
  ReportPostShareResp();
  virtual ~ReportPostShareResp();

  ReportPostShareResp(const ReportPostShareResp& from);

  inline ReportPostShareResp& operator=(const ReportPostShareResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ReportPostShareResp& default_instance();

  static inline const ReportPostShareResp* internal_default_instance() {
    return reinterpret_cast<const ReportPostShareResp*>(
               &_ReportPostShareResp_default_instance_);
  }

  void Swap(ReportPostShareResp* other);

  // implements Message ----------------------------------------------

  inline ReportPostShareResp* New() const PROTOBUF_FINAL { return New(NULL); }

  ReportPostShareResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ReportPostShareResp& from);
  void MergeFrom(const ReportPostShareResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ReportPostShareResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:ugc.content.ReportPostShareResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AddCommentReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.AddCommentReq) */ {
 public:
  AddCommentReq();
  virtual ~AddCommentReq();

  AddCommentReq(const AddCommentReq& from);

  inline AddCommentReq& operator=(const AddCommentReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AddCommentReq& default_instance();

  static inline const AddCommentReq* internal_default_instance() {
    return reinterpret_cast<const AddCommentReq*>(
               &_AddCommentReq_default_instance_);
  }

  void Swap(AddCommentReq* other);

  // implements Message ----------------------------------------------

  inline AddCommentReq* New() const PROTOBUF_FINAL { return New(NULL); }

  AddCommentReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const AddCommentReq& from);
  void MergeFrom(const AddCommentReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(AddCommentReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .ugc.content.AttachmentInfo attachments = 7;
  int attachments_size() const;
  void clear_attachments();
  static const int kAttachmentsFieldNumber = 7;
  const ::ugc::content::AttachmentInfo& attachments(int index) const;
  ::ugc::content::AttachmentInfo* mutable_attachments(int index);
  ::ugc::content::AttachmentInfo* add_attachments();
  ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >*
      mutable_attachments();
  const ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >&
      attachments() const;

  // string post_id = 2;
  void clear_post_id();
  static const int kPostIdFieldNumber = 2;
  const ::std::string& post_id() const;
  void set_post_id(const ::std::string& value);
  #if LANG_CXX11
  void set_post_id(::std::string&& value);
  #endif
  void set_post_id(const char* value);
  void set_post_id(const char* value, size_t size);
  ::std::string* mutable_post_id();
  ::std::string* release_post_id();
  void set_allocated_post_id(::std::string* post_id);

  // string reply_to_comment_id = 3;
  void clear_reply_to_comment_id();
  static const int kReplyToCommentIdFieldNumber = 3;
  const ::std::string& reply_to_comment_id() const;
  void set_reply_to_comment_id(const ::std::string& value);
  #if LANG_CXX11
  void set_reply_to_comment_id(::std::string&& value);
  #endif
  void set_reply_to_comment_id(const char* value);
  void set_reply_to_comment_id(const char* value, size_t size);
  ::std::string* mutable_reply_to_comment_id();
  ::std::string* release_reply_to_comment_id();
  void set_allocated_reply_to_comment_id(::std::string* reply_to_comment_id);

  // string conversation_id = 4;
  void clear_conversation_id();
  static const int kConversationIdFieldNumber = 4;
  const ::std::string& conversation_id() const;
  void set_conversation_id(const ::std::string& value);
  #if LANG_CXX11
  void set_conversation_id(::std::string&& value);
  #endif
  void set_conversation_id(const char* value);
  void set_conversation_id(const char* value, size_t size);
  ::std::string* mutable_conversation_id();
  ::std::string* release_conversation_id();
  void set_allocated_conversation_id(::std::string* conversation_id);

  // string content = 5;
  void clear_content();
  static const int kContentFieldNumber = 5;
  const ::std::string& content() const;
  void set_content(const ::std::string& value);
  #if LANG_CXX11
  void set_content(::std::string&& value);
  #endif
  void set_content(const char* value);
  void set_content(const char* value, size_t size);
  ::std::string* mutable_content();
  ::std::string* release_content();
  void set_allocated_content(::std::string* content);

  // uint32 user_id = 1;
  void clear_user_id();
  static const int kUserIdFieldNumber = 1;
  ::google::protobuf::uint32 user_id() const;
  void set_user_id(::google::protobuf::uint32 value);

  // .ugc.content.ContentStatus status = 6;
  void clear_status();
  static const int kStatusFieldNumber = 6;
  ::ugc::content::ContentStatus status() const;
  void set_status(::ugc::content::ContentStatus value);

  // @@protoc_insertion_point(class_scope:ugc.content.AddCommentReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo > attachments_;
  ::google::protobuf::internal::ArenaStringPtr post_id_;
  ::google::protobuf::internal::ArenaStringPtr reply_to_comment_id_;
  ::google::protobuf::internal::ArenaStringPtr conversation_id_;
  ::google::protobuf::internal::ArenaStringPtr content_;
  ::google::protobuf::uint32 user_id_;
  int status_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AddCommentResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.AddCommentResp) */ {
 public:
  AddCommentResp();
  virtual ~AddCommentResp();

  AddCommentResp(const AddCommentResp& from);

  inline AddCommentResp& operator=(const AddCommentResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AddCommentResp& default_instance();

  static inline const AddCommentResp* internal_default_instance() {
    return reinterpret_cast<const AddCommentResp*>(
               &_AddCommentResp_default_instance_);
  }

  void Swap(AddCommentResp* other);

  // implements Message ----------------------------------------------

  inline AddCommentResp* New() const PROTOBUF_FINAL { return New(NULL); }

  AddCommentResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const AddCommentResp& from);
  void MergeFrom(const AddCommentResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(AddCommentResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string comment_id = 1;
  void clear_comment_id();
  static const int kCommentIdFieldNumber = 1;
  const ::std::string& comment_id() const;
  void set_comment_id(const ::std::string& value);
  #if LANG_CXX11
  void set_comment_id(::std::string&& value);
  #endif
  void set_comment_id(const char* value);
  void set_comment_id(const char* value, size_t size);
  ::std::string* mutable_comment_id();
  ::std::string* release_comment_id();
  void set_allocated_comment_id(::std::string* comment_id);

  // .ugc.content.CommentInfo comment = 2;
  bool has_comment() const;
  void clear_comment();
  static const int kCommentFieldNumber = 2;
  const ::ugc::content::CommentInfo& comment() const;
  ::ugc::content::CommentInfo* mutable_comment();
  ::ugc::content::CommentInfo* release_comment();
  void set_allocated_comment(::ugc::content::CommentInfo* comment);

  // @@protoc_insertion_point(class_scope:ugc.content.AddCommentResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr comment_id_;
  ::ugc::content::CommentInfo* comment_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GetCommentListReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.GetCommentListReq) */ {
 public:
  GetCommentListReq();
  virtual ~GetCommentListReq();

  GetCommentListReq(const GetCommentListReq& from);

  inline GetCommentListReq& operator=(const GetCommentListReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetCommentListReq& default_instance();

  static inline const GetCommentListReq* internal_default_instance() {
    return reinterpret_cast<const GetCommentListReq*>(
               &_GetCommentListReq_default_instance_);
  }

  void Swap(GetCommentListReq* other);

  // implements Message ----------------------------------------------

  inline GetCommentListReq* New() const PROTOBUF_FINAL { return New(NULL); }

  GetCommentListReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetCommentListReq& from);
  void MergeFrom(const GetCommentListReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetCommentListReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string post_id = 1;
  void clear_post_id();
  static const int kPostIdFieldNumber = 1;
  const ::std::string& post_id() const;
  void set_post_id(const ::std::string& value);
  #if LANG_CXX11
  void set_post_id(::std::string&& value);
  #endif
  void set_post_id(const char* value);
  void set_post_id(const char* value, size_t size);
  ::std::string* mutable_post_id();
  ::std::string* release_post_id();
  void set_allocated_post_id(::std::string* post_id);

  // string conversation_id = 2;
  void clear_conversation_id();
  static const int kConversationIdFieldNumber = 2;
  const ::std::string& conversation_id() const;
  void set_conversation_id(const ::std::string& value);
  #if LANG_CXX11
  void set_conversation_id(::std::string&& value);
  #endif
  void set_conversation_id(const char* value);
  void set_conversation_id(const char* value, size_t size);
  ::std::string* mutable_conversation_id();
  ::std::string* release_conversation_id();
  void set_allocated_conversation_id(::std::string* conversation_id);

  // string loadMore = 3;
  void clear_loadmore();
  static const int kLoadMoreFieldNumber = 3;
  const ::std::string& loadmore() const;
  void set_loadmore(const ::std::string& value);
  #if LANG_CXX11
  void set_loadmore(::std::string&& value);
  #endif
  void set_loadmore(const char* value);
  void set_loadmore(const char* value, size_t size);
  ::std::string* mutable_loadmore();
  ::std::string* release_loadmore();
  void set_allocated_loadmore(::std::string* loadmore);

  // uint32 count = 4;
  void clear_count();
  static const int kCountFieldNumber = 4;
  ::google::protobuf::uint32 count() const;
  void set_count(::google::protobuf::uint32 value);

  // uint32 user_id = 5;
  void clear_user_id();
  static const int kUserIdFieldNumber = 5;
  ::google::protobuf::uint32 user_id() const;
  void set_user_id(::google::protobuf::uint32 value);

  // bool ascending = 6;
  void clear_ascending();
  static const int kAscendingFieldNumber = 6;
  bool ascending() const;
  void set_ascending(bool value);

  // bool sub_comments_ascending = 7;
  void clear_sub_comments_ascending();
  static const int kSubCommentsAscendingFieldNumber = 7;
  bool sub_comments_ascending() const;
  void set_sub_comments_ascending(bool value);

  // @@protoc_insertion_point(class_scope:ugc.content.GetCommentListReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr post_id_;
  ::google::protobuf::internal::ArenaStringPtr conversation_id_;
  ::google::protobuf::internal::ArenaStringPtr loadmore_;
  ::google::protobuf::uint32 count_;
  ::google::protobuf::uint32 user_id_;
  bool ascending_;
  bool sub_comments_ascending_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GetCommentListResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.GetCommentListResp) */ {
 public:
  GetCommentListResp();
  virtual ~GetCommentListResp();

  GetCommentListResp(const GetCommentListResp& from);

  inline GetCommentListResp& operator=(const GetCommentListResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetCommentListResp& default_instance();

  static inline const GetCommentListResp* internal_default_instance() {
    return reinterpret_cast<const GetCommentListResp*>(
               &_GetCommentListResp_default_instance_);
  }

  void Swap(GetCommentListResp* other);

  // implements Message ----------------------------------------------

  inline GetCommentListResp* New() const PROTOBUF_FINAL { return New(NULL); }

  GetCommentListResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetCommentListResp& from);
  void MergeFrom(const GetCommentListResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetCommentListResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .ugc.content.CommentInfo comment_list = 1;
  int comment_list_size() const;
  void clear_comment_list();
  static const int kCommentListFieldNumber = 1;
  const ::ugc::content::CommentInfo& comment_list(int index) const;
  ::ugc::content::CommentInfo* mutable_comment_list(int index);
  ::ugc::content::CommentInfo* add_comment_list();
  ::google::protobuf::RepeatedPtrField< ::ugc::content::CommentInfo >*
      mutable_comment_list();
  const ::google::protobuf::RepeatedPtrField< ::ugc::content::CommentInfo >&
      comment_list() const;

  // string load_more = 2;
  void clear_load_more();
  static const int kLoadMoreFieldNumber = 2;
  const ::std::string& load_more() const;
  void set_load_more(const ::std::string& value);
  #if LANG_CXX11
  void set_load_more(::std::string&& value);
  #endif
  void set_load_more(const char* value);
  void set_load_more(const char* value, size_t size);
  ::std::string* mutable_load_more();
  ::std::string* release_load_more();
  void set_allocated_load_more(::std::string* load_more);

  // @@protoc_insertion_point(class_scope:ugc.content.GetCommentListResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::ugc::content::CommentInfo > comment_list_;
  ::google::protobuf::internal::ArenaStringPtr load_more_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DelCommentReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.DelCommentReq) */ {
 public:
  DelCommentReq();
  virtual ~DelCommentReq();

  DelCommentReq(const DelCommentReq& from);

  inline DelCommentReq& operator=(const DelCommentReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DelCommentReq& default_instance();

  static inline const DelCommentReq* internal_default_instance() {
    return reinterpret_cast<const DelCommentReq*>(
               &_DelCommentReq_default_instance_);
  }

  void Swap(DelCommentReq* other);

  // implements Message ----------------------------------------------

  inline DelCommentReq* New() const PROTOBUF_FINAL { return New(NULL); }

  DelCommentReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const DelCommentReq& from);
  void MergeFrom(const DelCommentReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(DelCommentReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string post_id = 1;
  void clear_post_id();
  static const int kPostIdFieldNumber = 1;
  const ::std::string& post_id() const;
  void set_post_id(const ::std::string& value);
  #if LANG_CXX11
  void set_post_id(::std::string&& value);
  #endif
  void set_post_id(const char* value);
  void set_post_id(const char* value, size_t size);
  ::std::string* mutable_post_id();
  ::std::string* release_post_id();
  void set_allocated_post_id(::std::string* post_id);

  // string comment_id = 2;
  void clear_comment_id();
  static const int kCommentIdFieldNumber = 2;
  const ::std::string& comment_id() const;
  void set_comment_id(const ::std::string& value);
  #if LANG_CXX11
  void set_comment_id(::std::string&& value);
  #endif
  void set_comment_id(const char* value);
  void set_comment_id(const char* value, size_t size);
  ::std::string* mutable_comment_id();
  ::std::string* release_comment_id();
  void set_allocated_comment_id(::std::string* comment_id);

  // string conversation_id = 3;
  void clear_conversation_id();
  static const int kConversationIdFieldNumber = 3;
  const ::std::string& conversation_id() const;
  void set_conversation_id(const ::std::string& value);
  #if LANG_CXX11
  void set_conversation_id(::std::string&& value);
  #endif
  void set_conversation_id(const char* value);
  void set_conversation_id(const char* value, size_t size);
  ::std::string* mutable_conversation_id();
  ::std::string* release_conversation_id();
  void set_allocated_conversation_id(::std::string* conversation_id);

  // @@protoc_insertion_point(class_scope:ugc.content.DelCommentReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr post_id_;
  ::google::protobuf::internal::ArenaStringPtr comment_id_;
  ::google::protobuf::internal::ArenaStringPtr conversation_id_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DelCommentResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.DelCommentResp) */ {
 public:
  DelCommentResp();
  virtual ~DelCommentResp();

  DelCommentResp(const DelCommentResp& from);

  inline DelCommentResp& operator=(const DelCommentResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DelCommentResp& default_instance();

  static inline const DelCommentResp* internal_default_instance() {
    return reinterpret_cast<const DelCommentResp*>(
               &_DelCommentResp_default_instance_);
  }

  void Swap(DelCommentResp* other);

  // implements Message ----------------------------------------------

  inline DelCommentResp* New() const PROTOBUF_FINAL { return New(NULL); }

  DelCommentResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const DelCommentResp& from);
  void MergeFrom(const DelCommentResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(DelCommentResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:ugc.content.DelCommentResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GetCommentByIdReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.GetCommentByIdReq) */ {
 public:
  GetCommentByIdReq();
  virtual ~GetCommentByIdReq();

  GetCommentByIdReq(const GetCommentByIdReq& from);

  inline GetCommentByIdReq& operator=(const GetCommentByIdReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetCommentByIdReq& default_instance();

  static inline const GetCommentByIdReq* internal_default_instance() {
    return reinterpret_cast<const GetCommentByIdReq*>(
               &_GetCommentByIdReq_default_instance_);
  }

  void Swap(GetCommentByIdReq* other);

  // implements Message ----------------------------------------------

  inline GetCommentByIdReq* New() const PROTOBUF_FINAL { return New(NULL); }

  GetCommentByIdReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetCommentByIdReq& from);
  void MergeFrom(const GetCommentByIdReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetCommentByIdReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string comment_id = 1;
  void clear_comment_id();
  static const int kCommentIdFieldNumber = 1;
  const ::std::string& comment_id() const;
  void set_comment_id(const ::std::string& value);
  #if LANG_CXX11
  void set_comment_id(::std::string&& value);
  #endif
  void set_comment_id(const char* value);
  void set_comment_id(const char* value, size_t size);
  ::std::string* mutable_comment_id();
  ::std::string* release_comment_id();
  void set_allocated_comment_id(::std::string* comment_id);

  // @@protoc_insertion_point(class_scope:ugc.content.GetCommentByIdReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr comment_id_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GetCommentByIdResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.GetCommentByIdResp) */ {
 public:
  GetCommentByIdResp();
  virtual ~GetCommentByIdResp();

  GetCommentByIdResp(const GetCommentByIdResp& from);

  inline GetCommentByIdResp& operator=(const GetCommentByIdResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetCommentByIdResp& default_instance();

  static inline const GetCommentByIdResp* internal_default_instance() {
    return reinterpret_cast<const GetCommentByIdResp*>(
               &_GetCommentByIdResp_default_instance_);
  }

  void Swap(GetCommentByIdResp* other);

  // implements Message ----------------------------------------------

  inline GetCommentByIdResp* New() const PROTOBUF_FINAL { return New(NULL); }

  GetCommentByIdResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetCommentByIdResp& from);
  void MergeFrom(const GetCommentByIdResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetCommentByIdResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .ugc.content.CommentInfo comment = 1;
  bool has_comment() const;
  void clear_comment();
  static const int kCommentFieldNumber = 1;
  const ::ugc::content::CommentInfo& comment() const;
  ::ugc::content::CommentInfo* mutable_comment();
  ::ugc::content::CommentInfo* release_comment();
  void set_allocated_comment(::ugc::content::CommentInfo* comment);

  // @@protoc_insertion_point(class_scope:ugc.content.GetCommentByIdResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::ugc::content::CommentInfo* comment_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class BatchGetCommentByIdsReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.BatchGetCommentByIdsReq) */ {
 public:
  BatchGetCommentByIdsReq();
  virtual ~BatchGetCommentByIdsReq();

  BatchGetCommentByIdsReq(const BatchGetCommentByIdsReq& from);

  inline BatchGetCommentByIdsReq& operator=(const BatchGetCommentByIdsReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BatchGetCommentByIdsReq& default_instance();

  static inline const BatchGetCommentByIdsReq* internal_default_instance() {
    return reinterpret_cast<const BatchGetCommentByIdsReq*>(
               &_BatchGetCommentByIdsReq_default_instance_);
  }

  void Swap(BatchGetCommentByIdsReq* other);

  // implements Message ----------------------------------------------

  inline BatchGetCommentByIdsReq* New() const PROTOBUF_FINAL { return New(NULL); }

  BatchGetCommentByIdsReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const BatchGetCommentByIdsReq& from);
  void MergeFrom(const BatchGetCommentByIdsReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(BatchGetCommentByIdsReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string comment_id_list = 1;
  int comment_id_list_size() const;
  void clear_comment_id_list();
  static const int kCommentIdListFieldNumber = 1;
  const ::std::string& comment_id_list(int index) const;
  ::std::string* mutable_comment_id_list(int index);
  void set_comment_id_list(int index, const ::std::string& value);
  void set_comment_id_list(int index, const char* value);
  void set_comment_id_list(int index, const char* value, size_t size);
  ::std::string* add_comment_id_list();
  void add_comment_id_list(const ::std::string& value);
  void add_comment_id_list(const char* value);
  void add_comment_id_list(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& comment_id_list() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_comment_id_list();

  // @@protoc_insertion_point(class_scope:ugc.content.BatchGetCommentByIdsReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::std::string> comment_id_list_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------


// -------------------------------------------------------------------

class BatchGetCommentByIdsResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.BatchGetCommentByIdsResp) */ {
 public:
  BatchGetCommentByIdsResp();
  virtual ~BatchGetCommentByIdsResp();

  BatchGetCommentByIdsResp(const BatchGetCommentByIdsResp& from);

  inline BatchGetCommentByIdsResp& operator=(const BatchGetCommentByIdsResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BatchGetCommentByIdsResp& default_instance();

  static inline const BatchGetCommentByIdsResp* internal_default_instance() {
    return reinterpret_cast<const BatchGetCommentByIdsResp*>(
               &_BatchGetCommentByIdsResp_default_instance_);
  }

  void Swap(BatchGetCommentByIdsResp* other);

  // implements Message ----------------------------------------------

  inline BatchGetCommentByIdsResp* New() const PROTOBUF_FINAL { return New(NULL); }

  BatchGetCommentByIdsResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const BatchGetCommentByIdsResp& from);
  void MergeFrom(const BatchGetCommentByIdsResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(BatchGetCommentByIdsResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  // map<string, .ugc.content.CommentInfo> comment_infos = 1;
  int comment_infos_size() const;
  void clear_comment_infos();
  static const int kCommentInfosFieldNumber = 1;
  const ::google::protobuf::Map< ::std::string, ::ugc::content::CommentInfo >&
      comment_infos() const;
  ::google::protobuf::Map< ::std::string, ::ugc::content::CommentInfo >*
      mutable_comment_infos();

  // @@protoc_insertion_point(class_scope:ugc.content.BatchGetCommentByIdsResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  typedef ::google::protobuf::internal::MapEntryLite<
      ::std::string, ::ugc::content::CommentInfo,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 >
      BatchGetCommentByIdsResp_CommentInfosEntry;
  ::google::protobuf::internal::MapField<
      ::std::string, ::ugc::content::CommentInfo,
      ::google::protobuf::internal::WireFormatLite::TYPE_STRING,
      ::google::protobuf::internal::WireFormatLite::TYPE_MESSAGE,
      0 > comment_infos_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class BanCommentByIdReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.BanCommentByIdReq) */ {
 public:
  BanCommentByIdReq();
  virtual ~BanCommentByIdReq();

  BanCommentByIdReq(const BanCommentByIdReq& from);

  inline BanCommentByIdReq& operator=(const BanCommentByIdReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BanCommentByIdReq& default_instance();

  static inline const BanCommentByIdReq* internal_default_instance() {
    return reinterpret_cast<const BanCommentByIdReq*>(
               &_BanCommentByIdReq_default_instance_);
  }

  void Swap(BanCommentByIdReq* other);

  // implements Message ----------------------------------------------

  inline BanCommentByIdReq* New() const PROTOBUF_FINAL { return New(NULL); }

  BanCommentByIdReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const BanCommentByIdReq& from);
  void MergeFrom(const BanCommentByIdReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(BanCommentByIdReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string comment_id = 1;
  void clear_comment_id();
  static const int kCommentIdFieldNumber = 1;
  const ::std::string& comment_id() const;
  void set_comment_id(const ::std::string& value);
  #if LANG_CXX11
  void set_comment_id(::std::string&& value);
  #endif
  void set_comment_id(const char* value);
  void set_comment_id(const char* value, size_t size);
  ::std::string* mutable_comment_id();
  ::std::string* release_comment_id();
  void set_allocated_comment_id(::std::string* comment_id);

  // bool is_ban = 2;
  void clear_is_ban();
  static const int kIsBanFieldNumber = 2;
  bool is_ban() const;
  void set_is_ban(bool value);

  // bool is_delete = 3;
  void clear_is_delete();
  static const int kIsDeleteFieldNumber = 3;
  bool is_delete() const;
  void set_is_delete(bool value);

  // @@protoc_insertion_point(class_scope:ugc.content.BanCommentByIdReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr comment_id_;
  bool is_ban_;
  bool is_delete_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class BanCommentByIdResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.BanCommentByIdResp) */ {
 public:
  BanCommentByIdResp();
  virtual ~BanCommentByIdResp();

  BanCommentByIdResp(const BanCommentByIdResp& from);

  inline BanCommentByIdResp& operator=(const BanCommentByIdResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BanCommentByIdResp& default_instance();

  static inline const BanCommentByIdResp* internal_default_instance() {
    return reinterpret_cast<const BanCommentByIdResp*>(
               &_BanCommentByIdResp_default_instance_);
  }

  void Swap(BanCommentByIdResp* other);

  // implements Message ----------------------------------------------

  inline BanCommentByIdResp* New() const PROTOBUF_FINAL { return New(NULL); }

  BanCommentByIdResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const BanCommentByIdResp& from);
  void MergeFrom(const BanCommentByIdResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(BanCommentByIdResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:ugc.content.BanCommentByIdResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AddAttitudeReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.AddAttitudeReq) */ {
 public:
  AddAttitudeReq();
  virtual ~AddAttitudeReq();

  AddAttitudeReq(const AddAttitudeReq& from);

  inline AddAttitudeReq& operator=(const AddAttitudeReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AddAttitudeReq& default_instance();

  static inline const AddAttitudeReq* internal_default_instance() {
    return reinterpret_cast<const AddAttitudeReq*>(
               &_AddAttitudeReq_default_instance_);
  }

  void Swap(AddAttitudeReq* other);

  // implements Message ----------------------------------------------

  inline AddAttitudeReq* New() const PROTOBUF_FINAL { return New(NULL); }

  AddAttitudeReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const AddAttitudeReq& from);
  void MergeFrom(const AddAttitudeReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(AddAttitudeReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string post_id = 1;
  void clear_post_id();
  static const int kPostIdFieldNumber = 1;
  const ::std::string& post_id() const;
  void set_post_id(const ::std::string& value);
  #if LANG_CXX11
  void set_post_id(::std::string&& value);
  #endif
  void set_post_id(const char* value);
  void set_post_id(const char* value, size_t size);
  ::std::string* mutable_post_id();
  ::std::string* release_post_id();
  void set_allocated_post_id(::std::string* post_id);

  // string comment_id = 2;
  void clear_comment_id();
  static const int kCommentIdFieldNumber = 2;
  const ::std::string& comment_id() const;
  void set_comment_id(const ::std::string& value);
  #if LANG_CXX11
  void set_comment_id(::std::string&& value);
  #endif
  void set_comment_id(const char* value);
  void set_comment_id(const char* value, size_t size);
  ::std::string* mutable_comment_id();
  ::std::string* release_comment_id();
  void set_allocated_comment_id(::std::string* comment_id);

  // uint32 user_id = 3;
  void clear_user_id();
  static const int kUserIdFieldNumber = 3;
  ::google::protobuf::uint32 user_id() const;
  void set_user_id(::google::protobuf::uint32 value);

  // uint32 attitude_type = 4;
  void clear_attitude_type();
  static const int kAttitudeTypeFieldNumber = 4;
  ::google::protobuf::uint32 attitude_type() const;
  void set_attitude_type(::google::protobuf::uint32 value);

  // bool is_first_time = 5;
  void clear_is_first_time();
  static const int kIsFirstTimeFieldNumber = 5;
  bool is_first_time() const;
  void set_is_first_time(bool value);

  // uint32 target_user_id = 6;
  void clear_target_user_id();
  static const int kTargetUserIdFieldNumber = 6;
  ::google::protobuf::uint32 target_user_id() const;
  void set_target_user_id(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:ugc.content.AddAttitudeReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr post_id_;
  ::google::protobuf::internal::ArenaStringPtr comment_id_;
  ::google::protobuf::uint32 user_id_;
  ::google::protobuf::uint32 attitude_type_;
  bool is_first_time_;
  ::google::protobuf::uint32 target_user_id_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AddAttitudeResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.AddAttitudeResp) */ {
 public:
  AddAttitudeResp();
  virtual ~AddAttitudeResp();

  AddAttitudeResp(const AddAttitudeResp& from);

  inline AddAttitudeResp& operator=(const AddAttitudeResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AddAttitudeResp& default_instance();

  static inline const AddAttitudeResp* internal_default_instance() {
    return reinterpret_cast<const AddAttitudeResp*>(
               &_AddAttitudeResp_default_instance_);
  }

  void Swap(AddAttitudeResp* other);

  // implements Message ----------------------------------------------

  inline AddAttitudeResp* New() const PROTOBUF_FINAL { return New(NULL); }

  AddAttitudeResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const AddAttitudeResp& from);
  void MergeFrom(const AddAttitudeResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(AddAttitudeResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:ugc.content.AddAttitudeResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DelAttitudeReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.DelAttitudeReq) */ {
 public:
  DelAttitudeReq();
  virtual ~DelAttitudeReq();

  DelAttitudeReq(const DelAttitudeReq& from);

  inline DelAttitudeReq& operator=(const DelAttitudeReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DelAttitudeReq& default_instance();

  static inline const DelAttitudeReq* internal_default_instance() {
    return reinterpret_cast<const DelAttitudeReq*>(
               &_DelAttitudeReq_default_instance_);
  }

  void Swap(DelAttitudeReq* other);

  // implements Message ----------------------------------------------

  inline DelAttitudeReq* New() const PROTOBUF_FINAL { return New(NULL); }

  DelAttitudeReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const DelAttitudeReq& from);
  void MergeFrom(const DelAttitudeReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(DelAttitudeReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string post_id = 1;
  void clear_post_id();
  static const int kPostIdFieldNumber = 1;
  const ::std::string& post_id() const;
  void set_post_id(const ::std::string& value);
  #if LANG_CXX11
  void set_post_id(::std::string&& value);
  #endif
  void set_post_id(const char* value);
  void set_post_id(const char* value, size_t size);
  ::std::string* mutable_post_id();
  ::std::string* release_post_id();
  void set_allocated_post_id(::std::string* post_id);

  // string comment_id = 2;
  void clear_comment_id();
  static const int kCommentIdFieldNumber = 2;
  const ::std::string& comment_id() const;
  void set_comment_id(const ::std::string& value);
  #if LANG_CXX11
  void set_comment_id(::std::string&& value);
  #endif
  void set_comment_id(const char* value);
  void set_comment_id(const char* value, size_t size);
  ::std::string* mutable_comment_id();
  ::std::string* release_comment_id();
  void set_allocated_comment_id(::std::string* comment_id);

  // uint32 user_id = 3;
  void clear_user_id();
  static const int kUserIdFieldNumber = 3;
  ::google::protobuf::uint32 user_id() const;
  void set_user_id(::google::protobuf::uint32 value);

  // uint32 target_user_id = 4;
  void clear_target_user_id();
  static const int kTargetUserIdFieldNumber = 4;
  ::google::protobuf::uint32 target_user_id() const;
  void set_target_user_id(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:ugc.content.DelAttitudeReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr post_id_;
  ::google::protobuf::internal::ArenaStringPtr comment_id_;
  ::google::protobuf::uint32 user_id_;
  ::google::protobuf::uint32 target_user_id_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class DelAttitudeResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.DelAttitudeResp) */ {
 public:
  DelAttitudeResp();
  virtual ~DelAttitudeResp();

  DelAttitudeResp(const DelAttitudeResp& from);

  inline DelAttitudeResp& operator=(const DelAttitudeResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const DelAttitudeResp& default_instance();

  static inline const DelAttitudeResp* internal_default_instance() {
    return reinterpret_cast<const DelAttitudeResp*>(
               &_DelAttitudeResp_default_instance_);
  }

  void Swap(DelAttitudeResp* other);

  // implements Message ----------------------------------------------

  inline DelAttitudeResp* New() const PROTOBUF_FINAL { return New(NULL); }

  DelAttitudeResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const DelAttitudeResp& from);
  void MergeFrom(const DelAttitudeResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(DelAttitudeResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:ugc.content.DelAttitudeResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AttitudeUserInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.AttitudeUserInfo) */ {
 public:
  AttitudeUserInfo();
  virtual ~AttitudeUserInfo();

  AttitudeUserInfo(const AttitudeUserInfo& from);

  inline AttitudeUserInfo& operator=(const AttitudeUserInfo& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AttitudeUserInfo& default_instance();

  static inline const AttitudeUserInfo* internal_default_instance() {
    return reinterpret_cast<const AttitudeUserInfo*>(
               &_AttitudeUserInfo_default_instance_);
  }

  void Swap(AttitudeUserInfo* other);

  // implements Message ----------------------------------------------

  inline AttitudeUserInfo* New() const PROTOBUF_FINAL { return New(NULL); }

  AttitudeUserInfo* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const AttitudeUserInfo& from);
  void MergeFrom(const AttitudeUserInfo& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(AttitudeUserInfo* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // uint32 user_id = 1;
  void clear_user_id();
  static const int kUserIdFieldNumber = 1;
  ::google::protobuf::uint32 user_id() const;
  void set_user_id(::google::protobuf::uint32 value);

  // uint32 attitude_type = 2;
  void clear_attitude_type();
  static const int kAttitudeTypeFieldNumber = 2;
  ::google::protobuf::uint32 attitude_type() const;
  void set_attitude_type(::google::protobuf::uint32 value);

  // uint64 time = 3;
  void clear_time();
  static const int kTimeFieldNumber = 3;
  ::google::protobuf::uint64 time() const;
  void set_time(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:ugc.content.AttitudeUserInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::uint32 user_id_;
  ::google::protobuf::uint32 attitude_type_;
  ::google::protobuf::uint64 time_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GetAttitudeUserListReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.GetAttitudeUserListReq) */ {
 public:
  GetAttitudeUserListReq();
  virtual ~GetAttitudeUserListReq();

  GetAttitudeUserListReq(const GetAttitudeUserListReq& from);

  inline GetAttitudeUserListReq& operator=(const GetAttitudeUserListReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetAttitudeUserListReq& default_instance();

  static inline const GetAttitudeUserListReq* internal_default_instance() {
    return reinterpret_cast<const GetAttitudeUserListReq*>(
               &_GetAttitudeUserListReq_default_instance_);
  }

  void Swap(GetAttitudeUserListReq* other);

  // implements Message ----------------------------------------------

  inline GetAttitudeUserListReq* New() const PROTOBUF_FINAL { return New(NULL); }

  GetAttitudeUserListReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetAttitudeUserListReq& from);
  void MergeFrom(const GetAttitudeUserListReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetAttitudeUserListReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string post_id = 1;
  void clear_post_id();
  static const int kPostIdFieldNumber = 1;
  const ::std::string& post_id() const;
  void set_post_id(const ::std::string& value);
  #if LANG_CXX11
  void set_post_id(::std::string&& value);
  #endif
  void set_post_id(const char* value);
  void set_post_id(const char* value, size_t size);
  ::std::string* mutable_post_id();
  ::std::string* release_post_id();
  void set_allocated_post_id(::std::string* post_id);

  // string comment_id = 2;
  void clear_comment_id();
  static const int kCommentIdFieldNumber = 2;
  const ::std::string& comment_id() const;
  void set_comment_id(const ::std::string& value);
  #if LANG_CXX11
  void set_comment_id(::std::string&& value);
  #endif
  void set_comment_id(const char* value);
  void set_comment_id(const char* value, size_t size);
  ::std::string* mutable_comment_id();
  ::std::string* release_comment_id();
  void set_allocated_comment_id(::std::string* comment_id);

  // @@protoc_insertion_point(class_scope:ugc.content.GetAttitudeUserListReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr post_id_;
  ::google::protobuf::internal::ArenaStringPtr comment_id_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class GetAttitudeUserListResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.GetAttitudeUserListResp) */ {
 public:
  GetAttitudeUserListResp();
  virtual ~GetAttitudeUserListResp();

  GetAttitudeUserListResp(const GetAttitudeUserListResp& from);

  inline GetAttitudeUserListResp& operator=(const GetAttitudeUserListResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const GetAttitudeUserListResp& default_instance();

  static inline const GetAttitudeUserListResp* internal_default_instance() {
    return reinterpret_cast<const GetAttitudeUserListResp*>(
               &_GetAttitudeUserListResp_default_instance_);
  }

  void Swap(GetAttitudeUserListResp* other);

  // implements Message ----------------------------------------------

  inline GetAttitudeUserListResp* New() const PROTOBUF_FINAL { return New(NULL); }

  GetAttitudeUserListResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetAttitudeUserListResp& from);
  void MergeFrom(const GetAttitudeUserListResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetAttitudeUserListResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .ugc.content.AttitudeUserInfo attitude_user_list = 1;
  int attitude_user_list_size() const;
  void clear_attitude_user_list();
  static const int kAttitudeUserListFieldNumber = 1;
  const ::ugc::content::AttitudeUserInfo& attitude_user_list(int index) const;
  ::ugc::content::AttitudeUserInfo* mutable_attitude_user_list(int index);
  ::ugc::content::AttitudeUserInfo* add_attitude_user_list();
  ::google::protobuf::RepeatedPtrField< ::ugc::content::AttitudeUserInfo >*
      mutable_attitude_user_list();
  const ::google::protobuf::RepeatedPtrField< ::ugc::content::AttitudeUserInfo >&
      attitude_user_list() const;

  // @@protoc_insertion_point(class_scope:ugc.content.GetAttitudeUserListResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::ugc::content::AttitudeUserInfo > attitude_user_list_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AppReportReq : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.AppReportReq) */ {
 public:
  AppReportReq();
  virtual ~AppReportReq();

  AppReportReq(const AppReportReq& from);

  inline AppReportReq& operator=(const AppReportReq& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AppReportReq& default_instance();

  static inline const AppReportReq* internal_default_instance() {
    return reinterpret_cast<const AppReportReq*>(
               &_AppReportReq_default_instance_);
  }

  void Swap(AppReportReq* other);

  // implements Message ----------------------------------------------

  inline AppReportReq* New() const PROTOBUF_FINAL { return New(NULL); }

  AppReportReq* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const AppReportReq& from);
  void MergeFrom(const AppReportReq& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(AppReportReq* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string post_id = 1;
  void clear_post_id();
  static const int kPostIdFieldNumber = 1;
  const ::std::string& post_id() const;
  void set_post_id(const ::std::string& value);
  #if LANG_CXX11
  void set_post_id(::std::string&& value);
  #endif
  void set_post_id(const char* value);
  void set_post_id(const char* value, size_t size);
  ::std::string* mutable_post_id();
  ::std::string* release_post_id();
  void set_allocated_post_id(::std::string* post_id);

  // string comment_id = 2;
  void clear_comment_id();
  static const int kCommentIdFieldNumber = 2;
  const ::std::string& comment_id() const;
  void set_comment_id(const ::std::string& value);
  #if LANG_CXX11
  void set_comment_id(::std::string&& value);
  #endif
  void set_comment_id(const char* value);
  void set_comment_id(const char* value, size_t size);
  ::std::string* mutable_comment_id();
  ::std::string* release_comment_id();
  void set_allocated_comment_id(::std::string* comment_id);

  // string content = 5;
  void clear_content();
  static const int kContentFieldNumber = 5;
  const ::std::string& content() const;
  void set_content(const ::std::string& value);
  #if LANG_CXX11
  void set_content(::std::string&& value);
  #endif
  void set_content(const char* value);
  void set_content(const char* value, size_t size);
  ::std::string* mutable_content();
  ::std::string* release_content();
  void set_allocated_content(::std::string* content);

  // uint32 from_user_id = 3;
  void clear_from_user_id();
  static const int kFromUserIdFieldNumber = 3;
  ::google::protobuf::uint32 from_user_id() const;
  void set_from_user_id(::google::protobuf::uint32 value);

  // uint32 report_type = 4;
  void clear_report_type();
  static const int kReportTypeFieldNumber = 4;
  ::google::protobuf::uint32 report_type() const;
  void set_report_type(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:ugc.content.AppReportReq)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr post_id_;
  ::google::protobuf::internal::ArenaStringPtr comment_id_;
  ::google::protobuf::internal::ArenaStringPtr content_;
  ::google::protobuf::uint32 from_user_id_;
  ::google::protobuf::uint32 report_type_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// -------------------------------------------------------------------

class AppReportResp : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:ugc.content.AppReportResp) */ {
 public:
  AppReportResp();
  virtual ~AppReportResp();

  AppReportResp(const AppReportResp& from);

  inline AppReportResp& operator=(const AppReportResp& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const AppReportResp& default_instance();

  static inline const AppReportResp* internal_default_instance() {
    return reinterpret_cast<const AppReportResp*>(
               &_AppReportResp_default_instance_);
  }

  void Swap(AppReportResp* other);

  // implements Message ----------------------------------------------

  inline AppReportResp* New() const PROTOBUF_FINAL { return New(NULL); }

  AppReportResp* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const AppReportResp& from);
  void MergeFrom(const AppReportResp& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output)
      const PROTOBUF_FINAL {
    return InternalSerializeWithCachedSizesToArray(
        ::google::protobuf::io::CodedOutputStream::IsDefaultSerializationDeterministic(), output);
  }
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(AppReportResp* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:ugc.content.AppReportResp)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable int _cached_size_;
  friend struct  protobuf_content_2eproto::TableStruct;
};
// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// AttachmentInfo

// string key = 1;
inline void AttachmentInfo::clear_key() {
  key_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AttachmentInfo::key() const {
  // @@protoc_insertion_point(field_get:ugc.content.AttachmentInfo.key)
  return key_.GetNoArena();
}
inline void AttachmentInfo::set_key(const ::std::string& value) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AttachmentInfo.key)
}
#if LANG_CXX11
inline void AttachmentInfo::set_key(::std::string&& value) {
  
  key_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AttachmentInfo.key)
}
#endif
inline void AttachmentInfo::set_key(const char* value) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AttachmentInfo.key)
}
inline void AttachmentInfo::set_key(const char* value, size_t size) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AttachmentInfo.key)
}
inline ::std::string* AttachmentInfo::mutable_key() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AttachmentInfo.key)
  return key_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AttachmentInfo::release_key() {
  // @@protoc_insertion_point(field_release:ugc.content.AttachmentInfo.key)
  
  return key_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AttachmentInfo::set_allocated_key(::std::string* key) {
  if (key != NULL) {
    
  } else {
    
  }
  key_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), key);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AttachmentInfo.key)
}

// .ugc.content.AttachmentInfo.AttachmentType type = 2;
inline void AttachmentInfo::clear_type() {
  type_ = 0;
}
inline ::ugc::content::AttachmentInfo_AttachmentType AttachmentInfo::type() const {
  // @@protoc_insertion_point(field_get:ugc.content.AttachmentInfo.type)
  return static_cast< ::ugc::content::AttachmentInfo_AttachmentType >(type_);
}
inline void AttachmentInfo::set_type(::ugc::content::AttachmentInfo_AttachmentType value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AttachmentInfo.type)
}

// string content = 3;
inline void AttachmentInfo::clear_content() {
  content_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AttachmentInfo::content() const {
  // @@protoc_insertion_point(field_get:ugc.content.AttachmentInfo.content)
  return content_.GetNoArena();
}
inline void AttachmentInfo::set_content(const ::std::string& value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AttachmentInfo.content)
}
#if LANG_CXX11
inline void AttachmentInfo::set_content(::std::string&& value) {
  
  content_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AttachmentInfo.content)
}
#endif
inline void AttachmentInfo::set_content(const char* value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AttachmentInfo.content)
}
inline void AttachmentInfo::set_content(const char* value, size_t size) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AttachmentInfo.content)
}
inline ::std::string* AttachmentInfo::mutable_content() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AttachmentInfo.content)
  return content_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AttachmentInfo::release_content() {
  // @@protoc_insertion_point(field_release:ugc.content.AttachmentInfo.content)
  
  return content_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AttachmentInfo::set_allocated_content(::std::string* content) {
  if (content != NULL) {
    
  } else {
    
  }
  content_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), content);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AttachmentInfo.content)
}

// string extra = 4;
inline void AttachmentInfo::clear_extra() {
  extra_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AttachmentInfo::extra() const {
  // @@protoc_insertion_point(field_get:ugc.content.AttachmentInfo.extra)
  return extra_.GetNoArena();
}
inline void AttachmentInfo::set_extra(const ::std::string& value) {
  
  extra_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AttachmentInfo.extra)
}
#if LANG_CXX11
inline void AttachmentInfo::set_extra(::std::string&& value) {
  
  extra_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AttachmentInfo.extra)
}
#endif
inline void AttachmentInfo::set_extra(const char* value) {
  
  extra_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AttachmentInfo.extra)
}
inline void AttachmentInfo::set_extra(const char* value, size_t size) {
  
  extra_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AttachmentInfo.extra)
}
inline ::std::string* AttachmentInfo::mutable_extra() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AttachmentInfo.extra)
  return extra_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AttachmentInfo::release_extra() {
  // @@protoc_insertion_point(field_release:ugc.content.AttachmentInfo.extra)
  
  return extra_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AttachmentInfo::set_allocated_extra(::std::string* extra) {
  if (extra != NULL) {
    
  } else {
    
  }
  extra_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), extra);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AttachmentInfo.extra)
}

// .ugc.content.ContentStatus status = 5;
inline void AttachmentInfo::clear_status() {
  status_ = 0;
}
inline ::ugc::content::ContentStatus AttachmentInfo::status() const {
  // @@protoc_insertion_point(field_get:ugc.content.AttachmentInfo.status)
  return static_cast< ::ugc::content::ContentStatus >(status_);
}
inline void AttachmentInfo::set_status(::ugc::content::ContentStatus value) {
  
  status_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AttachmentInfo.status)
}

// string param = 10;
inline void AttachmentInfo::clear_param() {
  param_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AttachmentInfo::param() const {
  // @@protoc_insertion_point(field_get:ugc.content.AttachmentInfo.param)
  return param_.GetNoArena();
}
inline void AttachmentInfo::set_param(const ::std::string& value) {
  
  param_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AttachmentInfo.param)
}
#if LANG_CXX11
inline void AttachmentInfo::set_param(::std::string&& value) {
  
  param_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AttachmentInfo.param)
}
#endif
inline void AttachmentInfo::set_param(const char* value) {
  
  param_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AttachmentInfo.param)
}
inline void AttachmentInfo::set_param(const char* value, size_t size) {
  
  param_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AttachmentInfo.param)
}
inline ::std::string* AttachmentInfo::mutable_param() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AttachmentInfo.param)
  return param_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AttachmentInfo::release_param() {
  // @@protoc_insertion_point(field_release:ugc.content.AttachmentInfo.param)
  
  return param_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AttachmentInfo::set_allocated_param(::std::string* param) {
  if (param != NULL) {
    
  } else {
    
  }
  param_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), param);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AttachmentInfo.param)
}

// -------------------------------------------------------------------

// PostInfo

// string post_id = 1;
inline void PostInfo::clear_post_id() {
  post_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& PostInfo::post_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.PostInfo.post_id)
  return post_id_.GetNoArena();
}
inline void PostInfo::set_post_id(const ::std::string& value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.PostInfo.post_id)
}
#if LANG_CXX11
inline void PostInfo::set_post_id(::std::string&& value) {
  
  post_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.PostInfo.post_id)
}
#endif
inline void PostInfo::set_post_id(const char* value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.PostInfo.post_id)
}
inline void PostInfo::set_post_id(const char* value, size_t size) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.PostInfo.post_id)
}
inline ::std::string* PostInfo::mutable_post_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.PostInfo.post_id)
  return post_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* PostInfo::release_post_id() {
  // @@protoc_insertion_point(field_release:ugc.content.PostInfo.post_id)
  
  return post_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PostInfo::set_allocated_post_id(::std::string* post_id) {
  if (post_id != NULL) {
    
  } else {
    
  }
  post_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.PostInfo.post_id)
}

// string topic_id = 2;
inline void PostInfo::clear_topic_id() {
  topic_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& PostInfo::topic_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.PostInfo.topic_id)
  return topic_id_.GetNoArena();
}
inline void PostInfo::set_topic_id(const ::std::string& value) {
  
  topic_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.PostInfo.topic_id)
}
#if LANG_CXX11
inline void PostInfo::set_topic_id(::std::string&& value) {
  
  topic_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.PostInfo.topic_id)
}
#endif
inline void PostInfo::set_topic_id(const char* value) {
  
  topic_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.PostInfo.topic_id)
}
inline void PostInfo::set_topic_id(const char* value, size_t size) {
  
  topic_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.PostInfo.topic_id)
}
inline ::std::string* PostInfo::mutable_topic_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.PostInfo.topic_id)
  return topic_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* PostInfo::release_topic_id() {
  // @@protoc_insertion_point(field_release:ugc.content.PostInfo.topic_id)
  
  return topic_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PostInfo::set_allocated_topic_id(::std::string* topic_id) {
  if (topic_id != NULL) {
    
  } else {
    
  }
  topic_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), topic_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.PostInfo.topic_id)
}

// .ugc.content.PostInfo.PostType post_type = 3;
inline void PostInfo::clear_post_type() {
  post_type_ = 0;
}
inline ::ugc::content::PostInfo_PostType PostInfo::post_type() const {
  // @@protoc_insertion_point(field_get:ugc.content.PostInfo.post_type)
  return static_cast< ::ugc::content::PostInfo_PostType >(post_type_);
}
inline void PostInfo::set_post_type(::ugc::content::PostInfo_PostType value) {
  
  post_type_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.PostInfo.post_type)
}

// string content = 4;
inline void PostInfo::clear_content() {
  content_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& PostInfo::content() const {
  // @@protoc_insertion_point(field_get:ugc.content.PostInfo.content)
  return content_.GetNoArena();
}
inline void PostInfo::set_content(const ::std::string& value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.PostInfo.content)
}
#if LANG_CXX11
inline void PostInfo::set_content(::std::string&& value) {
  
  content_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.PostInfo.content)
}
#endif
inline void PostInfo::set_content(const char* value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.PostInfo.content)
}
inline void PostInfo::set_content(const char* value, size_t size) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.PostInfo.content)
}
inline ::std::string* PostInfo::mutable_content() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.PostInfo.content)
  return content_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* PostInfo::release_content() {
  // @@protoc_insertion_point(field_release:ugc.content.PostInfo.content)
  
  return content_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PostInfo::set_allocated_content(::std::string* content) {
  if (content != NULL) {
    
  } else {
    
  }
  content_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), content);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.PostInfo.content)
}

// repeated .ugc.content.AttachmentInfo attachments = 5;
inline int PostInfo::attachments_size() const {
  return attachments_.size();
}
inline void PostInfo::clear_attachments() {
  attachments_.Clear();
}
inline const ::ugc::content::AttachmentInfo& PostInfo::attachments(int index) const {
  // @@protoc_insertion_point(field_get:ugc.content.PostInfo.attachments)
  return attachments_.Get(index);
}
inline ::ugc::content::AttachmentInfo* PostInfo::mutable_attachments(int index) {
  // @@protoc_insertion_point(field_mutable:ugc.content.PostInfo.attachments)
  return attachments_.Mutable(index);
}
inline ::ugc::content::AttachmentInfo* PostInfo::add_attachments() {
  // @@protoc_insertion_point(field_add:ugc.content.PostInfo.attachments)
  return attachments_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >*
PostInfo::mutable_attachments() {
  // @@protoc_insertion_point(field_mutable_list:ugc.content.PostInfo.attachments)
  return &attachments_;
}
inline const ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >&
PostInfo::attachments() const {
  // @@protoc_insertion_point(field_list:ugc.content.PostInfo.attachments)
  return attachments_;
}

// uint64 create_at = 6;
inline void PostInfo::clear_create_at() {
  create_at_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 PostInfo::create_at() const {
  // @@protoc_insertion_point(field_get:ugc.content.PostInfo.create_at)
  return create_at_;
}
inline void PostInfo::set_create_at(::google::protobuf::uint64 value) {
  
  create_at_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.PostInfo.create_at)
}

// uint32 comment_count = 7;
inline void PostInfo::clear_comment_count() {
  comment_count_ = 0u;
}
inline ::google::protobuf::uint32 PostInfo::comment_count() const {
  // @@protoc_insertion_point(field_get:ugc.content.PostInfo.comment_count)
  return comment_count_;
}
inline void PostInfo::set_comment_count(::google::protobuf::uint32 value) {
  
  comment_count_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.PostInfo.comment_count)
}

// uint32 attitude_count = 8;
inline void PostInfo::clear_attitude_count() {
  attitude_count_ = 0u;
}
inline ::google::protobuf::uint32 PostInfo::attitude_count() const {
  // @@protoc_insertion_point(field_get:ugc.content.PostInfo.attitude_count)
  return attitude_count_;
}
inline void PostInfo::set_attitude_count(::google::protobuf::uint32 value) {
  
  attitude_count_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.PostInfo.attitude_count)
}

// uint32 view_count = 9;
inline void PostInfo::clear_view_count() {
  view_count_ = 0u;
}
inline ::google::protobuf::uint32 PostInfo::view_count() const {
  // @@protoc_insertion_point(field_get:ugc.content.PostInfo.view_count)
  return view_count_;
}
inline void PostInfo::set_view_count(::google::protobuf::uint32 value) {
  
  view_count_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.PostInfo.view_count)
}

// uint32 user_id = 10;
inline void PostInfo::clear_user_id() {
  user_id_ = 0u;
}
inline ::google::protobuf::uint32 PostInfo::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.PostInfo.user_id)
  return user_id_;
}
inline void PostInfo::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.PostInfo.user_id)
}

// .ugc.content.ContentStatus status = 11;
inline void PostInfo::clear_status() {
  status_ = 0;
}
inline ::ugc::content::ContentStatus PostInfo::status() const {
  // @@protoc_insertion_point(field_get:ugc.content.PostInfo.status)
  return static_cast< ::ugc::content::ContentStatus >(status_);
}
inline void PostInfo::set_status(::ugc::content::ContentStatus value) {
  
  status_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.PostInfo.status)
}

// uint32 top_level_comment_count = 12;
inline void PostInfo::clear_top_level_comment_count() {
  top_level_comment_count_ = 0u;
}
inline ::google::protobuf::uint32 PostInfo::top_level_comment_count() const {
  // @@protoc_insertion_point(field_get:ugc.content.PostInfo.top_level_comment_count)
  return top_level_comment_count_;
}
inline void PostInfo::set_top_level_comment_count(::google::protobuf::uint32 value) {
  
  top_level_comment_count_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.PostInfo.top_level_comment_count)
}

// uint32 share_count = 13;
inline void PostInfo::clear_share_count() {
  share_count_ = 0u;
}
inline ::google::protobuf::uint32 PostInfo::share_count() const {
  // @@protoc_insertion_point(field_get:ugc.content.PostInfo.share_count)
  return share_count_;
}
inline void PostInfo::set_share_count(::google::protobuf::uint32 value) {
  
  share_count_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.PostInfo.share_count)
}

// -------------------------------------------------------------------

// CommentInfo

// string comment_id = 1;
inline void CommentInfo::clear_comment_id() {
  comment_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& CommentInfo::comment_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.CommentInfo.comment_id)
  return comment_id_.GetNoArena();
}
inline void CommentInfo::set_comment_id(const ::std::string& value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.CommentInfo.comment_id)
}
#if LANG_CXX11
inline void CommentInfo::set_comment_id(::std::string&& value) {
  
  comment_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.CommentInfo.comment_id)
}
#endif
inline void CommentInfo::set_comment_id(const char* value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.CommentInfo.comment_id)
}
inline void CommentInfo::set_comment_id(const char* value, size_t size) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.CommentInfo.comment_id)
}
inline ::std::string* CommentInfo::mutable_comment_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.CommentInfo.comment_id)
  return comment_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* CommentInfo::release_comment_id() {
  // @@protoc_insertion_point(field_release:ugc.content.CommentInfo.comment_id)
  
  return comment_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CommentInfo::set_allocated_comment_id(::std::string* comment_id) {
  if (comment_id != NULL) {
    
  } else {
    
  }
  comment_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), comment_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.CommentInfo.comment_id)
}

// string post_id = 2;
inline void CommentInfo::clear_post_id() {
  post_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& CommentInfo::post_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.CommentInfo.post_id)
  return post_id_.GetNoArena();
}
inline void CommentInfo::set_post_id(const ::std::string& value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.CommentInfo.post_id)
}
#if LANG_CXX11
inline void CommentInfo::set_post_id(::std::string&& value) {
  
  post_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.CommentInfo.post_id)
}
#endif
inline void CommentInfo::set_post_id(const char* value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.CommentInfo.post_id)
}
inline void CommentInfo::set_post_id(const char* value, size_t size) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.CommentInfo.post_id)
}
inline ::std::string* CommentInfo::mutable_post_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.CommentInfo.post_id)
  return post_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* CommentInfo::release_post_id() {
  // @@protoc_insertion_point(field_release:ugc.content.CommentInfo.post_id)
  
  return post_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CommentInfo::set_allocated_post_id(::std::string* post_id) {
  if (post_id != NULL) {
    
  } else {
    
  }
  post_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.CommentInfo.post_id)
}

// string conversation_id = 3;
inline void CommentInfo::clear_conversation_id() {
  conversation_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& CommentInfo::conversation_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.CommentInfo.conversation_id)
  return conversation_id_.GetNoArena();
}
inline void CommentInfo::set_conversation_id(const ::std::string& value) {
  
  conversation_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.CommentInfo.conversation_id)
}
#if LANG_CXX11
inline void CommentInfo::set_conversation_id(::std::string&& value) {
  
  conversation_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.CommentInfo.conversation_id)
}
#endif
inline void CommentInfo::set_conversation_id(const char* value) {
  
  conversation_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.CommentInfo.conversation_id)
}
inline void CommentInfo::set_conversation_id(const char* value, size_t size) {
  
  conversation_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.CommentInfo.conversation_id)
}
inline ::std::string* CommentInfo::mutable_conversation_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.CommentInfo.conversation_id)
  return conversation_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* CommentInfo::release_conversation_id() {
  // @@protoc_insertion_point(field_release:ugc.content.CommentInfo.conversation_id)
  
  return conversation_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CommentInfo::set_allocated_conversation_id(::std::string* conversation_id) {
  if (conversation_id != NULL) {
    
  } else {
    
  }
  conversation_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), conversation_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.CommentInfo.conversation_id)
}

// string content = 4;
inline void CommentInfo::clear_content() {
  content_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& CommentInfo::content() const {
  // @@protoc_insertion_point(field_get:ugc.content.CommentInfo.content)
  return content_.GetNoArena();
}
inline void CommentInfo::set_content(const ::std::string& value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.CommentInfo.content)
}
#if LANG_CXX11
inline void CommentInfo::set_content(::std::string&& value) {
  
  content_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.CommentInfo.content)
}
#endif
inline void CommentInfo::set_content(const char* value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.CommentInfo.content)
}
inline void CommentInfo::set_content(const char* value, size_t size) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.CommentInfo.content)
}
inline ::std::string* CommentInfo::mutable_content() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.CommentInfo.content)
  return content_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* CommentInfo::release_content() {
  // @@protoc_insertion_point(field_release:ugc.content.CommentInfo.content)
  
  return content_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CommentInfo::set_allocated_content(::std::string* content) {
  if (content != NULL) {
    
  } else {
    
  }
  content_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), content);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.CommentInfo.content)
}

// uint32 user_id = 5;
inline void CommentInfo::clear_user_id() {
  user_id_ = 0u;
}
inline ::google::protobuf::uint32 CommentInfo::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.CommentInfo.user_id)
  return user_id_;
}
inline void CommentInfo::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.CommentInfo.user_id)
}

// uint32 reply_to_user_id = 6;
inline void CommentInfo::clear_reply_to_user_id() {
  reply_to_user_id_ = 0u;
}
inline ::google::protobuf::uint32 CommentInfo::reply_to_user_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.CommentInfo.reply_to_user_id)
  return reply_to_user_id_;
}
inline void CommentInfo::set_reply_to_user_id(::google::protobuf::uint32 value) {
  
  reply_to_user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.CommentInfo.reply_to_user_id)
}

// repeated .ugc.content.AttachmentInfo attachments = 7;
inline int CommentInfo::attachments_size() const {
  return attachments_.size();
}
inline void CommentInfo::clear_attachments() {
  attachments_.Clear();
}
inline const ::ugc::content::AttachmentInfo& CommentInfo::attachments(int index) const {
  // @@protoc_insertion_point(field_get:ugc.content.CommentInfo.attachments)
  return attachments_.Get(index);
}
inline ::ugc::content::AttachmentInfo* CommentInfo::mutable_attachments(int index) {
  // @@protoc_insertion_point(field_mutable:ugc.content.CommentInfo.attachments)
  return attachments_.Mutable(index);
}
inline ::ugc::content::AttachmentInfo* CommentInfo::add_attachments() {
  // @@protoc_insertion_point(field_add:ugc.content.CommentInfo.attachments)
  return attachments_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >*
CommentInfo::mutable_attachments() {
  // @@protoc_insertion_point(field_mutable_list:ugc.content.CommentInfo.attachments)
  return &attachments_;
}
inline const ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >&
CommentInfo::attachments() const {
  // @@protoc_insertion_point(field_list:ugc.content.CommentInfo.attachments)
  return attachments_;
}

// uint32 comment_count = 8;
inline void CommentInfo::clear_comment_count() {
  comment_count_ = 0u;
}
inline ::google::protobuf::uint32 CommentInfo::comment_count() const {
  // @@protoc_insertion_point(field_get:ugc.content.CommentInfo.comment_count)
  return comment_count_;
}
inline void CommentInfo::set_comment_count(::google::protobuf::uint32 value) {
  
  comment_count_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.CommentInfo.comment_count)
}

// uint32 attitude_count = 9;
inline void CommentInfo::clear_attitude_count() {
  attitude_count_ = 0u;
}
inline ::google::protobuf::uint32 CommentInfo::attitude_count() const {
  // @@protoc_insertion_point(field_get:ugc.content.CommentInfo.attitude_count)
  return attitude_count_;
}
inline void CommentInfo::set_attitude_count(::google::protobuf::uint32 value) {
  
  attitude_count_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.CommentInfo.attitude_count)
}

// .ugc.content.ContentStatus status = 10;
inline void CommentInfo::clear_status() {
  status_ = 0;
}
inline ::ugc::content::ContentStatus CommentInfo::status() const {
  // @@protoc_insertion_point(field_get:ugc.content.CommentInfo.status)
  return static_cast< ::ugc::content::ContentStatus >(status_);
}
inline void CommentInfo::set_status(::ugc::content::ContentStatus value) {
  
  status_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.CommentInfo.status)
}

// repeated .ugc.content.CommentInfo sub_comments = 11;
inline int CommentInfo::sub_comments_size() const {
  return sub_comments_.size();
}
inline void CommentInfo::clear_sub_comments() {
  sub_comments_.Clear();
}
inline const ::ugc::content::CommentInfo& CommentInfo::sub_comments(int index) const {
  // @@protoc_insertion_point(field_get:ugc.content.CommentInfo.sub_comments)
  return sub_comments_.Get(index);
}
inline ::ugc::content::CommentInfo* CommentInfo::mutable_sub_comments(int index) {
  // @@protoc_insertion_point(field_mutable:ugc.content.CommentInfo.sub_comments)
  return sub_comments_.Mutable(index);
}
inline ::ugc::content::CommentInfo* CommentInfo::add_sub_comments() {
  // @@protoc_insertion_point(field_add:ugc.content.CommentInfo.sub_comments)
  return sub_comments_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::ugc::content::CommentInfo >*
CommentInfo::mutable_sub_comments() {
  // @@protoc_insertion_point(field_mutable_list:ugc.content.CommentInfo.sub_comments)
  return &sub_comments_;
}
inline const ::google::protobuf::RepeatedPtrField< ::ugc::content::CommentInfo >&
CommentInfo::sub_comments() const {
  // @@protoc_insertion_point(field_list:ugc.content.CommentInfo.sub_comments)
  return sub_comments_;
}

// uint64 create_at = 12;
inline void CommentInfo::clear_create_at() {
  create_at_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 CommentInfo::create_at() const {
  // @@protoc_insertion_point(field_get:ugc.content.CommentInfo.create_at)
  return create_at_;
}
inline void CommentInfo::set_create_at(::google::protobuf::uint64 value) {
  
  create_at_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.CommentInfo.create_at)
}

// -------------------------------------------------------------------

// BatchGetPostListByIdReq

// repeated string post_id_list = 1;
inline int BatchGetPostListByIdReq::post_id_list_size() const {
  return post_id_list_.size();
}
inline void BatchGetPostListByIdReq::clear_post_id_list() {
  post_id_list_.Clear();
}
inline const ::std::string& BatchGetPostListByIdReq::post_id_list(int index) const {
  // @@protoc_insertion_point(field_get:ugc.content.BatchGetPostListByIdReq.post_id_list)
  return post_id_list_.Get(index);
}
inline ::std::string* BatchGetPostListByIdReq::mutable_post_id_list(int index) {
  // @@protoc_insertion_point(field_mutable:ugc.content.BatchGetPostListByIdReq.post_id_list)
  return post_id_list_.Mutable(index);
}
inline void BatchGetPostListByIdReq::set_post_id_list(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:ugc.content.BatchGetPostListByIdReq.post_id_list)
  post_id_list_.Mutable(index)->assign(value);
}
inline void BatchGetPostListByIdReq::set_post_id_list(int index, const char* value) {
  post_id_list_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:ugc.content.BatchGetPostListByIdReq.post_id_list)
}
inline void BatchGetPostListByIdReq::set_post_id_list(int index, const char* value, size_t size) {
  post_id_list_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:ugc.content.BatchGetPostListByIdReq.post_id_list)
}
inline ::std::string* BatchGetPostListByIdReq::add_post_id_list() {
  // @@protoc_insertion_point(field_add_mutable:ugc.content.BatchGetPostListByIdReq.post_id_list)
  return post_id_list_.Add();
}
inline void BatchGetPostListByIdReq::add_post_id_list(const ::std::string& value) {
  post_id_list_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:ugc.content.BatchGetPostListByIdReq.post_id_list)
}
inline void BatchGetPostListByIdReq::add_post_id_list(const char* value) {
  post_id_list_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:ugc.content.BatchGetPostListByIdReq.post_id_list)
}
inline void BatchGetPostListByIdReq::add_post_id_list(const char* value, size_t size) {
  post_id_list_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:ugc.content.BatchGetPostListByIdReq.post_id_list)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
BatchGetPostListByIdReq::post_id_list() const {
  // @@protoc_insertion_point(field_list:ugc.content.BatchGetPostListByIdReq.post_id_list)
  return post_id_list_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
BatchGetPostListByIdReq::mutable_post_id_list() {
  // @@protoc_insertion_point(field_mutable_list:ugc.content.BatchGetPostListByIdReq.post_id_list)
  return &post_id_list_;
}

// -------------------------------------------------------------------

// BatchGetPostListByIdResp

// repeated .ugc.content.PostInfo post_list = 1;
inline int BatchGetPostListByIdResp::post_list_size() const {
  return post_list_.size();
}
inline void BatchGetPostListByIdResp::clear_post_list() {
  post_list_.Clear();
}
inline const ::ugc::content::PostInfo& BatchGetPostListByIdResp::post_list(int index) const {
  // @@protoc_insertion_point(field_get:ugc.content.BatchGetPostListByIdResp.post_list)
  return post_list_.Get(index);
}
inline ::ugc::content::PostInfo* BatchGetPostListByIdResp::mutable_post_list(int index) {
  // @@protoc_insertion_point(field_mutable:ugc.content.BatchGetPostListByIdResp.post_list)
  return post_list_.Mutable(index);
}
inline ::ugc::content::PostInfo* BatchGetPostListByIdResp::add_post_list() {
  // @@protoc_insertion_point(field_add:ugc.content.BatchGetPostListByIdResp.post_list)
  return post_list_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::ugc::content::PostInfo >*
BatchGetPostListByIdResp::mutable_post_list() {
  // @@protoc_insertion_point(field_mutable_list:ugc.content.BatchGetPostListByIdResp.post_list)
  return &post_list_;
}
inline const ::google::protobuf::RepeatedPtrField< ::ugc::content::PostInfo >&
BatchGetPostListByIdResp::post_list() const {
  // @@protoc_insertion_point(field_list:ugc.content.BatchGetPostListByIdResp.post_list)
  return post_list_;
}

// -------------------------------------------------------------------

// AddPostDirectlyReq

// uint32 user_id = 1;
inline void AddPostDirectlyReq::clear_user_id() {
  user_id_ = 0u;
}
inline ::google::protobuf::uint32 AddPostDirectlyReq::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostDirectlyReq.user_id)
  return user_id_;
}
inline void AddPostDirectlyReq::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AddPostDirectlyReq.user_id)
}

// string topic_id = 2;
inline void AddPostDirectlyReq::clear_topic_id() {
  topic_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AddPostDirectlyReq::topic_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostDirectlyReq.topic_id)
  return topic_id_.GetNoArena();
}
inline void AddPostDirectlyReq::set_topic_id(const ::std::string& value) {
  
  topic_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AddPostDirectlyReq.topic_id)
}
#if LANG_CXX11
inline void AddPostDirectlyReq::set_topic_id(::std::string&& value) {
  
  topic_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AddPostDirectlyReq.topic_id)
}
#endif
inline void AddPostDirectlyReq::set_topic_id(const char* value) {
  
  topic_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AddPostDirectlyReq.topic_id)
}
inline void AddPostDirectlyReq::set_topic_id(const char* value, size_t size) {
  
  topic_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AddPostDirectlyReq.topic_id)
}
inline ::std::string* AddPostDirectlyReq::mutable_topic_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AddPostDirectlyReq.topic_id)
  return topic_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AddPostDirectlyReq::release_topic_id() {
  // @@protoc_insertion_point(field_release:ugc.content.AddPostDirectlyReq.topic_id)
  
  return topic_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AddPostDirectlyReq::set_allocated_topic_id(::std::string* topic_id) {
  if (topic_id != NULL) {
    
  } else {
    
  }
  topic_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), topic_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AddPostDirectlyReq.topic_id)
}

// .ugc.content.PostInfo.PostType type = 3;
inline void AddPostDirectlyReq::clear_type() {
  type_ = 0;
}
inline ::ugc::content::PostInfo_PostType AddPostDirectlyReq::type() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostDirectlyReq.type)
  return static_cast< ::ugc::content::PostInfo_PostType >(type_);
}
inline void AddPostDirectlyReq::set_type(::ugc::content::PostInfo_PostType value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AddPostDirectlyReq.type)
}

// string content = 4;
inline void AddPostDirectlyReq::clear_content() {
  content_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AddPostDirectlyReq::content() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostDirectlyReq.content)
  return content_.GetNoArena();
}
inline void AddPostDirectlyReq::set_content(const ::std::string& value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AddPostDirectlyReq.content)
}
#if LANG_CXX11
inline void AddPostDirectlyReq::set_content(::std::string&& value) {
  
  content_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AddPostDirectlyReq.content)
}
#endif
inline void AddPostDirectlyReq::set_content(const char* value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AddPostDirectlyReq.content)
}
inline void AddPostDirectlyReq::set_content(const char* value, size_t size) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AddPostDirectlyReq.content)
}
inline ::std::string* AddPostDirectlyReq::mutable_content() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AddPostDirectlyReq.content)
  return content_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AddPostDirectlyReq::release_content() {
  // @@protoc_insertion_point(field_release:ugc.content.AddPostDirectlyReq.content)
  
  return content_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AddPostDirectlyReq::set_allocated_content(::std::string* content) {
  if (content != NULL) {
    
  } else {
    
  }
  content_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), content);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AddPostDirectlyReq.content)
}

// repeated .ugc.content.AttachmentInfo attachments = 5;
inline int AddPostDirectlyReq::attachments_size() const {
  return attachments_.size();
}
inline void AddPostDirectlyReq::clear_attachments() {
  attachments_.Clear();
}
inline const ::ugc::content::AttachmentInfo& AddPostDirectlyReq::attachments(int index) const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostDirectlyReq.attachments)
  return attachments_.Get(index);
}
inline ::ugc::content::AttachmentInfo* AddPostDirectlyReq::mutable_attachments(int index) {
  // @@protoc_insertion_point(field_mutable:ugc.content.AddPostDirectlyReq.attachments)
  return attachments_.Mutable(index);
}
inline ::ugc::content::AttachmentInfo* AddPostDirectlyReq::add_attachments() {
  // @@protoc_insertion_point(field_add:ugc.content.AddPostDirectlyReq.attachments)
  return attachments_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >*
AddPostDirectlyReq::mutable_attachments() {
  // @@protoc_insertion_point(field_mutable_list:ugc.content.AddPostDirectlyReq.attachments)
  return &attachments_;
}
inline const ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >&
AddPostDirectlyReq::attachments() const {
  // @@protoc_insertion_point(field_list:ugc.content.AddPostDirectlyReq.attachments)
  return attachments_;
}

// uint64 create_at = 6;
inline void AddPostDirectlyReq::clear_create_at() {
  create_at_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 AddPostDirectlyReq::create_at() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostDirectlyReq.create_at)
  return create_at_;
}
inline void AddPostDirectlyReq::set_create_at(::google::protobuf::uint64 value) {
  
  create_at_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AddPostDirectlyReq.create_at)
}

// uint32 availability_mask = 7;
inline void AddPostDirectlyReq::clear_availability_mask() {
  availability_mask_ = 0u;
}
inline ::google::protobuf::uint32 AddPostDirectlyReq::availability_mask() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostDirectlyReq.availability_mask)
  return availability_mask_;
}
inline void AddPostDirectlyReq::set_availability_mask(::google::protobuf::uint32 value) {
  
  availability_mask_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AddPostDirectlyReq.availability_mask)
}

// -------------------------------------------------------------------

// AddPostDirectlyResp

// string post_id = 1;
inline void AddPostDirectlyResp::clear_post_id() {
  post_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AddPostDirectlyResp::post_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostDirectlyResp.post_id)
  return post_id_.GetNoArena();
}
inline void AddPostDirectlyResp::set_post_id(const ::std::string& value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AddPostDirectlyResp.post_id)
}
#if LANG_CXX11
inline void AddPostDirectlyResp::set_post_id(::std::string&& value) {
  
  post_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AddPostDirectlyResp.post_id)
}
#endif
inline void AddPostDirectlyResp::set_post_id(const char* value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AddPostDirectlyResp.post_id)
}
inline void AddPostDirectlyResp::set_post_id(const char* value, size_t size) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AddPostDirectlyResp.post_id)
}
inline ::std::string* AddPostDirectlyResp::mutable_post_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AddPostDirectlyResp.post_id)
  return post_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AddPostDirectlyResp::release_post_id() {
  // @@protoc_insertion_point(field_release:ugc.content.AddPostDirectlyResp.post_id)
  
  return post_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AddPostDirectlyResp::set_allocated_post_id(::std::string* post_id) {
  if (post_id != NULL) {
    
  } else {
    
  }
  post_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AddPostDirectlyResp.post_id)
}

// -------------------------------------------------------------------

// GenerateNewPostIdReq

// -------------------------------------------------------------------

// GenerateNewPostIdResp

// string post_id = 1;
inline void GenerateNewPostIdResp::clear_post_id() {
  post_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& GenerateNewPostIdResp::post_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.GenerateNewPostIdResp.post_id)
  return post_id_.GetNoArena();
}
inline void GenerateNewPostIdResp::set_post_id(const ::std::string& value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.GenerateNewPostIdResp.post_id)
}
#if LANG_CXX11
inline void GenerateNewPostIdResp::set_post_id(::std::string&& value) {
  
  post_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.GenerateNewPostIdResp.post_id)
}
#endif
inline void GenerateNewPostIdResp::set_post_id(const char* value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.GenerateNewPostIdResp.post_id)
}
inline void GenerateNewPostIdResp::set_post_id(const char* value, size_t size) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.GenerateNewPostIdResp.post_id)
}
inline ::std::string* GenerateNewPostIdResp::mutable_post_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.GenerateNewPostIdResp.post_id)
  return post_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* GenerateNewPostIdResp::release_post_id() {
  // @@protoc_insertion_point(field_release:ugc.content.GenerateNewPostIdResp.post_id)
  
  return post_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void GenerateNewPostIdResp::set_allocated_post_id(::std::string* post_id) {
  if (post_id != NULL) {
    
  } else {
    
  }
  post_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.GenerateNewPostIdResp.post_id)
}

// -------------------------------------------------------------------

// AddPostReq

// uint32 user_id = 1;
inline void AddPostReq::clear_user_id() {
  user_id_ = 0u;
}
inline ::google::protobuf::uint32 AddPostReq::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostReq.user_id)
  return user_id_;
}
inline void AddPostReq::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AddPostReq.user_id)
}

// string topic_id = 2;
inline void AddPostReq::clear_topic_id() {
  topic_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AddPostReq::topic_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostReq.topic_id)
  return topic_id_.GetNoArena();
}
inline void AddPostReq::set_topic_id(const ::std::string& value) {
  
  topic_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AddPostReq.topic_id)
}
#if LANG_CXX11
inline void AddPostReq::set_topic_id(::std::string&& value) {
  
  topic_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AddPostReq.topic_id)
}
#endif
inline void AddPostReq::set_topic_id(const char* value) {
  
  topic_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AddPostReq.topic_id)
}
inline void AddPostReq::set_topic_id(const char* value, size_t size) {
  
  topic_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AddPostReq.topic_id)
}
inline ::std::string* AddPostReq::mutable_topic_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AddPostReq.topic_id)
  return topic_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AddPostReq::release_topic_id() {
  // @@protoc_insertion_point(field_release:ugc.content.AddPostReq.topic_id)
  
  return topic_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AddPostReq::set_allocated_topic_id(::std::string* topic_id) {
  if (topic_id != NULL) {
    
  } else {
    
  }
  topic_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), topic_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AddPostReq.topic_id)
}

// .ugc.content.PostInfo.PostType type = 3;
inline void AddPostReq::clear_type() {
  type_ = 0;
}
inline ::ugc::content::PostInfo_PostType AddPostReq::type() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostReq.type)
  return static_cast< ::ugc::content::PostInfo_PostType >(type_);
}
inline void AddPostReq::set_type(::ugc::content::PostInfo_PostType value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AddPostReq.type)
}

// string content = 4;
inline void AddPostReq::clear_content() {
  content_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AddPostReq::content() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostReq.content)
  return content_.GetNoArena();
}
inline void AddPostReq::set_content(const ::std::string& value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AddPostReq.content)
}
#if LANG_CXX11
inline void AddPostReq::set_content(::std::string&& value) {
  
  content_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AddPostReq.content)
}
#endif
inline void AddPostReq::set_content(const char* value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AddPostReq.content)
}
inline void AddPostReq::set_content(const char* value, size_t size) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AddPostReq.content)
}
inline ::std::string* AddPostReq::mutable_content() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AddPostReq.content)
  return content_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AddPostReq::release_content() {
  // @@protoc_insertion_point(field_release:ugc.content.AddPostReq.content)
  
  return content_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AddPostReq::set_allocated_content(::std::string* content) {
  if (content != NULL) {
    
  } else {
    
  }
  content_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), content);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AddPostReq.content)
}

// uint32 attachment_image_count = 5;
inline void AddPostReq::clear_attachment_image_count() {
  attachment_image_count_ = 0u;
}
inline ::google::protobuf::uint32 AddPostReq::attachment_image_count() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostReq.attachment_image_count)
  return attachment_image_count_;
}
inline void AddPostReq::set_attachment_image_count(::google::protobuf::uint32 value) {
  
  attachment_image_count_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AddPostReq.attachment_image_count)
}

// uint32 attachment_video_count = 6;
inline void AddPostReq::clear_attachment_video_count() {
  attachment_video_count_ = 0u;
}
inline ::google::protobuf::uint32 AddPostReq::attachment_video_count() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostReq.attachment_video_count)
  return attachment_video_count_;
}
inline void AddPostReq::set_attachment_video_count(::google::protobuf::uint32 value) {
  
  attachment_video_count_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AddPostReq.attachment_video_count)
}

// .ugc.content.ContentStatus status = 7;
inline void AddPostReq::clear_status() {
  status_ = 0;
}
inline ::ugc::content::ContentStatus AddPostReq::status() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostReq.status)
  return static_cast< ::ugc::content::ContentStatus >(status_);
}
inline void AddPostReq::set_status(::ugc::content::ContentStatus value) {
  
  status_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AddPostReq.status)
}

// string antispam_label_info = 8;
inline void AddPostReq::clear_antispam_label_info() {
  antispam_label_info_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AddPostReq::antispam_label_info() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostReq.antispam_label_info)
  return antispam_label_info_.GetNoArena();
}
inline void AddPostReq::set_antispam_label_info(const ::std::string& value) {
  
  antispam_label_info_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AddPostReq.antispam_label_info)
}
#if LANG_CXX11
inline void AddPostReq::set_antispam_label_info(::std::string&& value) {
  
  antispam_label_info_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AddPostReq.antispam_label_info)
}
#endif
inline void AddPostReq::set_antispam_label_info(const char* value) {
  
  antispam_label_info_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AddPostReq.antispam_label_info)
}
inline void AddPostReq::set_antispam_label_info(const char* value, size_t size) {
  
  antispam_label_info_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AddPostReq.antispam_label_info)
}
inline ::std::string* AddPostReq::mutable_antispam_label_info() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AddPostReq.antispam_label_info)
  return antispam_label_info_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AddPostReq::release_antispam_label_info() {
  // @@protoc_insertion_point(field_release:ugc.content.AddPostReq.antispam_label_info)
  
  return antispam_label_info_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AddPostReq::set_allocated_antispam_label_info(::std::string* antispam_label_info) {
  if (antispam_label_info != NULL) {
    
  } else {
    
  }
  antispam_label_info_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), antispam_label_info);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AddPostReq.antispam_label_info)
}

// string device_id = 20;
inline void AddPostReq::clear_device_id() {
  device_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AddPostReq::device_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostReq.device_id)
  return device_id_.GetNoArena();
}
inline void AddPostReq::set_device_id(const ::std::string& value) {
  
  device_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AddPostReq.device_id)
}
#if LANG_CXX11
inline void AddPostReq::set_device_id(::std::string&& value) {
  
  device_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AddPostReq.device_id)
}
#endif
inline void AddPostReq::set_device_id(const char* value) {
  
  device_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AddPostReq.device_id)
}
inline void AddPostReq::set_device_id(const char* value, size_t size) {
  
  device_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AddPostReq.device_id)
}
inline ::std::string* AddPostReq::mutable_device_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AddPostReq.device_id)
  return device_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AddPostReq::release_device_id() {
  // @@protoc_insertion_point(field_release:ugc.content.AddPostReq.device_id)
  
  return device_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AddPostReq::set_allocated_device_id(::std::string* device_id) {
  if (device_id != NULL) {
    
  } else {
    
  }
  device_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), device_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AddPostReq.device_id)
}

// uint32 platform = 21;
inline void AddPostReq::clear_platform() {
  platform_ = 0u;
}
inline ::google::protobuf::uint32 AddPostReq::platform() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostReq.platform)
  return platform_;
}
inline void AddPostReq::set_platform(::google::protobuf::uint32 value) {
  
  platform_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AddPostReq.platform)
}

// -------------------------------------------------------------------

// AddPostResp

// string post_id = 1;
inline void AddPostResp::clear_post_id() {
  post_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AddPostResp::post_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostResp.post_id)
  return post_id_.GetNoArena();
}
inline void AddPostResp::set_post_id(const ::std::string& value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AddPostResp.post_id)
}
#if LANG_CXX11
inline void AddPostResp::set_post_id(::std::string&& value) {
  
  post_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AddPostResp.post_id)
}
#endif
inline void AddPostResp::set_post_id(const char* value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AddPostResp.post_id)
}
inline void AddPostResp::set_post_id(const char* value, size_t size) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AddPostResp.post_id)
}
inline ::std::string* AddPostResp::mutable_post_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AddPostResp.post_id)
  return post_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AddPostResp::release_post_id() {
  // @@protoc_insertion_point(field_release:ugc.content.AddPostResp.post_id)
  
  return post_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AddPostResp::set_allocated_post_id(::std::string* post_id) {
  if (post_id != NULL) {
    
  } else {
    
  }
  post_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AddPostResp.post_id)
}

// .ugc.content.ContentStatus status = 2;
inline void AddPostResp::clear_status() {
  status_ = 0;
}
inline ::ugc::content::ContentStatus AddPostResp::status() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostResp.status)
  return static_cast< ::ugc::content::ContentStatus >(status_);
}
inline void AddPostResp::set_status(::ugc::content::ContentStatus value) {
  
  status_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AddPostResp.status)
}

// uint64 post_create_at = 3;
inline void AddPostResp::clear_post_create_at() {
  post_create_at_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 AddPostResp::post_create_at() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostResp.post_create_at)
  return post_create_at_;
}
inline void AddPostResp::set_post_create_at(::google::protobuf::uint64 value) {
  
  post_create_at_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AddPostResp.post_create_at)
}

// repeated string attachment_image_keys = 10;
inline int AddPostResp::attachment_image_keys_size() const {
  return attachment_image_keys_.size();
}
inline void AddPostResp::clear_attachment_image_keys() {
  attachment_image_keys_.Clear();
}
inline const ::std::string& AddPostResp::attachment_image_keys(int index) const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostResp.attachment_image_keys)
  return attachment_image_keys_.Get(index);
}
inline ::std::string* AddPostResp::mutable_attachment_image_keys(int index) {
  // @@protoc_insertion_point(field_mutable:ugc.content.AddPostResp.attachment_image_keys)
  return attachment_image_keys_.Mutable(index);
}
inline void AddPostResp::set_attachment_image_keys(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:ugc.content.AddPostResp.attachment_image_keys)
  attachment_image_keys_.Mutable(index)->assign(value);
}
inline void AddPostResp::set_attachment_image_keys(int index, const char* value) {
  attachment_image_keys_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:ugc.content.AddPostResp.attachment_image_keys)
}
inline void AddPostResp::set_attachment_image_keys(int index, const char* value, size_t size) {
  attachment_image_keys_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AddPostResp.attachment_image_keys)
}
inline ::std::string* AddPostResp::add_attachment_image_keys() {
  // @@protoc_insertion_point(field_add_mutable:ugc.content.AddPostResp.attachment_image_keys)
  return attachment_image_keys_.Add();
}
inline void AddPostResp::add_attachment_image_keys(const ::std::string& value) {
  attachment_image_keys_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:ugc.content.AddPostResp.attachment_image_keys)
}
inline void AddPostResp::add_attachment_image_keys(const char* value) {
  attachment_image_keys_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:ugc.content.AddPostResp.attachment_image_keys)
}
inline void AddPostResp::add_attachment_image_keys(const char* value, size_t size) {
  attachment_image_keys_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:ugc.content.AddPostResp.attachment_image_keys)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
AddPostResp::attachment_image_keys() const {
  // @@protoc_insertion_point(field_list:ugc.content.AddPostResp.attachment_image_keys)
  return attachment_image_keys_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
AddPostResp::mutable_attachment_image_keys() {
  // @@protoc_insertion_point(field_mutable_list:ugc.content.AddPostResp.attachment_image_keys)
  return &attachment_image_keys_;
}

// repeated string attachment_video_keys = 11;
inline int AddPostResp::attachment_video_keys_size() const {
  return attachment_video_keys_.size();
}
inline void AddPostResp::clear_attachment_video_keys() {
  attachment_video_keys_.Clear();
}
inline const ::std::string& AddPostResp::attachment_video_keys(int index) const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostResp.attachment_video_keys)
  return attachment_video_keys_.Get(index);
}
inline ::std::string* AddPostResp::mutable_attachment_video_keys(int index) {
  // @@protoc_insertion_point(field_mutable:ugc.content.AddPostResp.attachment_video_keys)
  return attachment_video_keys_.Mutable(index);
}
inline void AddPostResp::set_attachment_video_keys(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:ugc.content.AddPostResp.attachment_video_keys)
  attachment_video_keys_.Mutable(index)->assign(value);
}
inline void AddPostResp::set_attachment_video_keys(int index, const char* value) {
  attachment_video_keys_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:ugc.content.AddPostResp.attachment_video_keys)
}
inline void AddPostResp::set_attachment_video_keys(int index, const char* value, size_t size) {
  attachment_video_keys_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AddPostResp.attachment_video_keys)
}
inline ::std::string* AddPostResp::add_attachment_video_keys() {
  // @@protoc_insertion_point(field_add_mutable:ugc.content.AddPostResp.attachment_video_keys)
  return attachment_video_keys_.Add();
}
inline void AddPostResp::add_attachment_video_keys(const ::std::string& value) {
  attachment_video_keys_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:ugc.content.AddPostResp.attachment_video_keys)
}
inline void AddPostResp::add_attachment_video_keys(const char* value) {
  attachment_video_keys_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:ugc.content.AddPostResp.attachment_video_keys)
}
inline void AddPostResp::add_attachment_video_keys(const char* value, size_t size) {
  attachment_video_keys_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:ugc.content.AddPostResp.attachment_video_keys)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
AddPostResp::attachment_video_keys() const {
  // @@protoc_insertion_point(field_list:ugc.content.AddPostResp.attachment_video_keys)
  return attachment_video_keys_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
AddPostResp::mutable_attachment_video_keys() {
  // @@protoc_insertion_point(field_mutable_list:ugc.content.AddPostResp.attachment_video_keys)
  return &attachment_video_keys_;
}

// string image_upload_token = 12;
inline void AddPostResp::clear_image_upload_token() {
  image_upload_token_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AddPostResp::image_upload_token() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostResp.image_upload_token)
  return image_upload_token_.GetNoArena();
}
inline void AddPostResp::set_image_upload_token(const ::std::string& value) {
  
  image_upload_token_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AddPostResp.image_upload_token)
}
#if LANG_CXX11
inline void AddPostResp::set_image_upload_token(::std::string&& value) {
  
  image_upload_token_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AddPostResp.image_upload_token)
}
#endif
inline void AddPostResp::set_image_upload_token(const char* value) {
  
  image_upload_token_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AddPostResp.image_upload_token)
}
inline void AddPostResp::set_image_upload_token(const char* value, size_t size) {
  
  image_upload_token_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AddPostResp.image_upload_token)
}
inline ::std::string* AddPostResp::mutable_image_upload_token() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AddPostResp.image_upload_token)
  return image_upload_token_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AddPostResp::release_image_upload_token() {
  // @@protoc_insertion_point(field_release:ugc.content.AddPostResp.image_upload_token)
  
  return image_upload_token_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AddPostResp::set_allocated_image_upload_token(::std::string* image_upload_token) {
  if (image_upload_token != NULL) {
    
  } else {
    
  }
  image_upload_token_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), image_upload_token);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AddPostResp.image_upload_token)
}

// string video_upload_token = 13;
inline void AddPostResp::clear_video_upload_token() {
  video_upload_token_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AddPostResp::video_upload_token() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddPostResp.video_upload_token)
  return video_upload_token_.GetNoArena();
}
inline void AddPostResp::set_video_upload_token(const ::std::string& value) {
  
  video_upload_token_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AddPostResp.video_upload_token)
}
#if LANG_CXX11
inline void AddPostResp::set_video_upload_token(::std::string&& value) {
  
  video_upload_token_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AddPostResp.video_upload_token)
}
#endif
inline void AddPostResp::set_video_upload_token(const char* value) {
  
  video_upload_token_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AddPostResp.video_upload_token)
}
inline void AddPostResp::set_video_upload_token(const char* value, size_t size) {
  
  video_upload_token_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AddPostResp.video_upload_token)
}
inline ::std::string* AddPostResp::mutable_video_upload_token() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AddPostResp.video_upload_token)
  return video_upload_token_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AddPostResp::release_video_upload_token() {
  // @@protoc_insertion_point(field_release:ugc.content.AddPostResp.video_upload_token)
  
  return video_upload_token_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AddPostResp::set_allocated_video_upload_token(::std::string* video_upload_token) {
  if (video_upload_token != NULL) {
    
  } else {
    
  }
  video_upload_token_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), video_upload_token);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AddPostResp.video_upload_token)
}

// -------------------------------------------------------------------

// MarkAttachmentUploadedReq

// string post_id = 1;
inline void MarkAttachmentUploadedReq::clear_post_id() {
  post_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MarkAttachmentUploadedReq::post_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.MarkAttachmentUploadedReq.post_id)
  return post_id_.GetNoArena();
}
inline void MarkAttachmentUploadedReq::set_post_id(const ::std::string& value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.MarkAttachmentUploadedReq.post_id)
}
#if LANG_CXX11
inline void MarkAttachmentUploadedReq::set_post_id(::std::string&& value) {
  
  post_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.MarkAttachmentUploadedReq.post_id)
}
#endif
inline void MarkAttachmentUploadedReq::set_post_id(const char* value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.MarkAttachmentUploadedReq.post_id)
}
inline void MarkAttachmentUploadedReq::set_post_id(const char* value, size_t size) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.MarkAttachmentUploadedReq.post_id)
}
inline ::std::string* MarkAttachmentUploadedReq::mutable_post_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.MarkAttachmentUploadedReq.post_id)
  return post_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MarkAttachmentUploadedReq::release_post_id() {
  // @@protoc_insertion_point(field_release:ugc.content.MarkAttachmentUploadedReq.post_id)
  
  return post_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MarkAttachmentUploadedReq::set_allocated_post_id(::std::string* post_id) {
  if (post_id != NULL) {
    
  } else {
    
  }
  post_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.MarkAttachmentUploadedReq.post_id)
}

// string comment_id = 2;
inline void MarkAttachmentUploadedReq::clear_comment_id() {
  comment_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MarkAttachmentUploadedReq::comment_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.MarkAttachmentUploadedReq.comment_id)
  return comment_id_.GetNoArena();
}
inline void MarkAttachmentUploadedReq::set_comment_id(const ::std::string& value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.MarkAttachmentUploadedReq.comment_id)
}
#if LANG_CXX11
inline void MarkAttachmentUploadedReq::set_comment_id(::std::string&& value) {
  
  comment_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.MarkAttachmentUploadedReq.comment_id)
}
#endif
inline void MarkAttachmentUploadedReq::set_comment_id(const char* value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.MarkAttachmentUploadedReq.comment_id)
}
inline void MarkAttachmentUploadedReq::set_comment_id(const char* value, size_t size) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.MarkAttachmentUploadedReq.comment_id)
}
inline ::std::string* MarkAttachmentUploadedReq::mutable_comment_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.MarkAttachmentUploadedReq.comment_id)
  return comment_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MarkAttachmentUploadedReq::release_comment_id() {
  // @@protoc_insertion_point(field_release:ugc.content.MarkAttachmentUploadedReq.comment_id)
  
  return comment_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MarkAttachmentUploadedReq::set_allocated_comment_id(::std::string* comment_id) {
  if (comment_id != NULL) {
    
  } else {
    
  }
  comment_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), comment_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.MarkAttachmentUploadedReq.comment_id)
}

// repeated .ugc.content.AttachmentInfo attachment_info_list = 3;
inline int MarkAttachmentUploadedReq::attachment_info_list_size() const {
  return attachment_info_list_.size();
}
inline void MarkAttachmentUploadedReq::clear_attachment_info_list() {
  attachment_info_list_.Clear();
}
inline const ::ugc::content::AttachmentInfo& MarkAttachmentUploadedReq::attachment_info_list(int index) const {
  // @@protoc_insertion_point(field_get:ugc.content.MarkAttachmentUploadedReq.attachment_info_list)
  return attachment_info_list_.Get(index);
}
inline ::ugc::content::AttachmentInfo* MarkAttachmentUploadedReq::mutable_attachment_info_list(int index) {
  // @@protoc_insertion_point(field_mutable:ugc.content.MarkAttachmentUploadedReq.attachment_info_list)
  return attachment_info_list_.Mutable(index);
}
inline ::ugc::content::AttachmentInfo* MarkAttachmentUploadedReq::add_attachment_info_list() {
  // @@protoc_insertion_point(field_add:ugc.content.MarkAttachmentUploadedReq.attachment_info_list)
  return attachment_info_list_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >*
MarkAttachmentUploadedReq::mutable_attachment_info_list() {
  // @@protoc_insertion_point(field_mutable_list:ugc.content.MarkAttachmentUploadedReq.attachment_info_list)
  return &attachment_info_list_;
}
inline const ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >&
MarkAttachmentUploadedReq::attachment_info_list() const {
  // @@protoc_insertion_point(field_list:ugc.content.MarkAttachmentUploadedReq.attachment_info_list)
  return attachment_info_list_;
}

// -------------------------------------------------------------------

// MarkAttachmentUploadedResp

// uint64 post_create_at = 1;
inline void MarkAttachmentUploadedResp::clear_post_create_at() {
  post_create_at_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 MarkAttachmentUploadedResp::post_create_at() const {
  // @@protoc_insertion_point(field_get:ugc.content.MarkAttachmentUploadedResp.post_create_at)
  return post_create_at_;
}
inline void MarkAttachmentUploadedResp::set_post_create_at(::google::protobuf::uint64 value) {
  
  post_create_at_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.MarkAttachmentUploadedResp.post_create_at)
}

// -------------------------------------------------------------------

// BanPostByIdReq

// string post_id = 1;
inline void BanPostByIdReq::clear_post_id() {
  post_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& BanPostByIdReq::post_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.BanPostByIdReq.post_id)
  return post_id_.GetNoArena();
}
inline void BanPostByIdReq::set_post_id(const ::std::string& value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.BanPostByIdReq.post_id)
}
#if LANG_CXX11
inline void BanPostByIdReq::set_post_id(::std::string&& value) {
  
  post_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.BanPostByIdReq.post_id)
}
#endif
inline void BanPostByIdReq::set_post_id(const char* value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.BanPostByIdReq.post_id)
}
inline void BanPostByIdReq::set_post_id(const char* value, size_t size) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.BanPostByIdReq.post_id)
}
inline ::std::string* BanPostByIdReq::mutable_post_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.BanPostByIdReq.post_id)
  return post_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* BanPostByIdReq::release_post_id() {
  // @@protoc_insertion_point(field_release:ugc.content.BanPostByIdReq.post_id)
  
  return post_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BanPostByIdReq::set_allocated_post_id(::std::string* post_id) {
  if (post_id != NULL) {
    
  } else {
    
  }
  post_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.BanPostByIdReq.post_id)
}

// bool is_ban = 2;
inline void BanPostByIdReq::clear_is_ban() {
  is_ban_ = false;
}
inline bool BanPostByIdReq::is_ban() const {
  // @@protoc_insertion_point(field_get:ugc.content.BanPostByIdReq.is_ban)
  return is_ban_;
}
inline void BanPostByIdReq::set_is_ban(bool value) {
  
  is_ban_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.BanPostByIdReq.is_ban)
}

// -------------------------------------------------------------------

// BanPostByIdResp

// -------------------------------------------------------------------

// DelPostReq

// string post_id = 1;
inline void DelPostReq::clear_post_id() {
  post_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& DelPostReq::post_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.DelPostReq.post_id)
  return post_id_.GetNoArena();
}
inline void DelPostReq::set_post_id(const ::std::string& value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.DelPostReq.post_id)
}
#if LANG_CXX11
inline void DelPostReq::set_post_id(::std::string&& value) {
  
  post_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.DelPostReq.post_id)
}
#endif
inline void DelPostReq::set_post_id(const char* value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.DelPostReq.post_id)
}
inline void DelPostReq::set_post_id(const char* value, size_t size) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.DelPostReq.post_id)
}
inline ::std::string* DelPostReq::mutable_post_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.DelPostReq.post_id)
  return post_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* DelPostReq::release_post_id() {
  // @@protoc_insertion_point(field_release:ugc.content.DelPostReq.post_id)
  
  return post_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void DelPostReq::set_allocated_post_id(::std::string* post_id) {
  if (post_id != NULL) {
    
  } else {
    
  }
  post_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.DelPostReq.post_id)
}

// -------------------------------------------------------------------

// DelPostResp

// -------------------------------------------------------------------

// GetPostByIdReq

// string post_id = 1;
inline void GetPostByIdReq::clear_post_id() {
  post_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& GetPostByIdReq::post_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.GetPostByIdReq.post_id)
  return post_id_.GetNoArena();
}
inline void GetPostByIdReq::set_post_id(const ::std::string& value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.GetPostByIdReq.post_id)
}
#if LANG_CXX11
inline void GetPostByIdReq::set_post_id(::std::string&& value) {
  
  post_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.GetPostByIdReq.post_id)
}
#endif
inline void GetPostByIdReq::set_post_id(const char* value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.GetPostByIdReq.post_id)
}
inline void GetPostByIdReq::set_post_id(const char* value, size_t size) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.GetPostByIdReq.post_id)
}
inline ::std::string* GetPostByIdReq::mutable_post_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.GetPostByIdReq.post_id)
  return post_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* GetPostByIdReq::release_post_id() {
  // @@protoc_insertion_point(field_release:ugc.content.GetPostByIdReq.post_id)
  
  return post_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void GetPostByIdReq::set_allocated_post_id(::std::string* post_id) {
  if (post_id != NULL) {
    
  } else {
    
  }
  post_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.GetPostByIdReq.post_id)
}

// -------------------------------------------------------------------

// GetPostByIdResp

// .ugc.content.PostInfo post = 1;
inline bool GetPostByIdResp::has_post() const {
  return this != internal_default_instance() && post_ != NULL;
}
inline void GetPostByIdResp::clear_post() {
  if (GetArenaNoVirtual() == NULL && post_ != NULL) delete post_;
  post_ = NULL;
}
inline const ::ugc::content::PostInfo& GetPostByIdResp::post() const {
  // @@protoc_insertion_point(field_get:ugc.content.GetPostByIdResp.post)
  return post_ != NULL ? *post_
                         : *::ugc::content::PostInfo::internal_default_instance();
}
inline ::ugc::content::PostInfo* GetPostByIdResp::mutable_post() {
  
  if (post_ == NULL) {
    post_ = new ::ugc::content::PostInfo;
  }
  // @@protoc_insertion_point(field_mutable:ugc.content.GetPostByIdResp.post)
  return post_;
}
inline ::ugc::content::PostInfo* GetPostByIdResp::release_post() {
  // @@protoc_insertion_point(field_release:ugc.content.GetPostByIdResp.post)
  
  ::ugc::content::PostInfo* temp = post_;
  post_ = NULL;
  return temp;
}
inline void GetPostByIdResp::set_allocated_post(::ugc::content::PostInfo* post) {
  delete post_;
  post_ = post;
  if (post) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:ugc.content.GetPostByIdResp.post)
}

// -------------------------------------------------------------------

// UpdateAttachmentStatusReq

// string post_id = 1;
inline void UpdateAttachmentStatusReq::clear_post_id() {
  post_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& UpdateAttachmentStatusReq::post_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.UpdateAttachmentStatusReq.post_id)
  return post_id_.GetNoArena();
}
inline void UpdateAttachmentStatusReq::set_post_id(const ::std::string& value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.UpdateAttachmentStatusReq.post_id)
}
#if LANG_CXX11
inline void UpdateAttachmentStatusReq::set_post_id(::std::string&& value) {
  
  post_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.UpdateAttachmentStatusReq.post_id)
}
#endif
inline void UpdateAttachmentStatusReq::set_post_id(const char* value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.UpdateAttachmentStatusReq.post_id)
}
inline void UpdateAttachmentStatusReq::set_post_id(const char* value, size_t size) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.UpdateAttachmentStatusReq.post_id)
}
inline ::std::string* UpdateAttachmentStatusReq::mutable_post_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.UpdateAttachmentStatusReq.post_id)
  return post_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* UpdateAttachmentStatusReq::release_post_id() {
  // @@protoc_insertion_point(field_release:ugc.content.UpdateAttachmentStatusReq.post_id)
  
  return post_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void UpdateAttachmentStatusReq::set_allocated_post_id(::std::string* post_id) {
  if (post_id != NULL) {
    
  } else {
    
  }
  post_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.UpdateAttachmentStatusReq.post_id)
}

// string comment_id = 2;
inline void UpdateAttachmentStatusReq::clear_comment_id() {
  comment_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& UpdateAttachmentStatusReq::comment_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.UpdateAttachmentStatusReq.comment_id)
  return comment_id_.GetNoArena();
}
inline void UpdateAttachmentStatusReq::set_comment_id(const ::std::string& value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.UpdateAttachmentStatusReq.comment_id)
}
#if LANG_CXX11
inline void UpdateAttachmentStatusReq::set_comment_id(::std::string&& value) {
  
  comment_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.UpdateAttachmentStatusReq.comment_id)
}
#endif
inline void UpdateAttachmentStatusReq::set_comment_id(const char* value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.UpdateAttachmentStatusReq.comment_id)
}
inline void UpdateAttachmentStatusReq::set_comment_id(const char* value, size_t size) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.UpdateAttachmentStatusReq.comment_id)
}
inline ::std::string* UpdateAttachmentStatusReq::mutable_comment_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.UpdateAttachmentStatusReq.comment_id)
  return comment_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* UpdateAttachmentStatusReq::release_comment_id() {
  // @@protoc_insertion_point(field_release:ugc.content.UpdateAttachmentStatusReq.comment_id)
  
  return comment_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void UpdateAttachmentStatusReq::set_allocated_comment_id(::std::string* comment_id) {
  if (comment_id != NULL) {
    
  } else {
    
  }
  comment_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), comment_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.UpdateAttachmentStatusReq.comment_id)
}

// string attachment_key = 3;
inline void UpdateAttachmentStatusReq::clear_attachment_key() {
  attachment_key_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& UpdateAttachmentStatusReq::attachment_key() const {
  // @@protoc_insertion_point(field_get:ugc.content.UpdateAttachmentStatusReq.attachment_key)
  return attachment_key_.GetNoArena();
}
inline void UpdateAttachmentStatusReq::set_attachment_key(const ::std::string& value) {
  
  attachment_key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.UpdateAttachmentStatusReq.attachment_key)
}
#if LANG_CXX11
inline void UpdateAttachmentStatusReq::set_attachment_key(::std::string&& value) {
  
  attachment_key_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.UpdateAttachmentStatusReq.attachment_key)
}
#endif
inline void UpdateAttachmentStatusReq::set_attachment_key(const char* value) {
  
  attachment_key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.UpdateAttachmentStatusReq.attachment_key)
}
inline void UpdateAttachmentStatusReq::set_attachment_key(const char* value, size_t size) {
  
  attachment_key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.UpdateAttachmentStatusReq.attachment_key)
}
inline ::std::string* UpdateAttachmentStatusReq::mutable_attachment_key() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.UpdateAttachmentStatusReq.attachment_key)
  return attachment_key_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* UpdateAttachmentStatusReq::release_attachment_key() {
  // @@protoc_insertion_point(field_release:ugc.content.UpdateAttachmentStatusReq.attachment_key)
  
  return attachment_key_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void UpdateAttachmentStatusReq::set_allocated_attachment_key(::std::string* attachment_key) {
  if (attachment_key != NULL) {
    
  } else {
    
  }
  attachment_key_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), attachment_key);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.UpdateAttachmentStatusReq.attachment_key)
}

// .ugc.content.ContentStatus status = 4;
inline void UpdateAttachmentStatusReq::clear_status() {
  status_ = 0;
}
inline ::ugc::content::ContentStatus UpdateAttachmentStatusReq::status() const {
  // @@protoc_insertion_point(field_get:ugc.content.UpdateAttachmentStatusReq.status)
  return static_cast< ::ugc::content::ContentStatus >(status_);
}
inline void UpdateAttachmentStatusReq::set_status(::ugc::content::ContentStatus value) {
  
  status_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.UpdateAttachmentStatusReq.status)
}

// -------------------------------------------------------------------

// UpdateAttachmentStatusResp

// -------------------------------------------------------------------

// UpdateVideoUrlReq

// string post_id = 1;
inline void UpdateVideoUrlReq::clear_post_id() {
  post_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& UpdateVideoUrlReq::post_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.UpdateVideoUrlReq.post_id)
  return post_id_.GetNoArena();
}
inline void UpdateVideoUrlReq::set_post_id(const ::std::string& value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.UpdateVideoUrlReq.post_id)
}
#if LANG_CXX11
inline void UpdateVideoUrlReq::set_post_id(::std::string&& value) {
  
  post_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.UpdateVideoUrlReq.post_id)
}
#endif
inline void UpdateVideoUrlReq::set_post_id(const char* value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.UpdateVideoUrlReq.post_id)
}
inline void UpdateVideoUrlReq::set_post_id(const char* value, size_t size) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.UpdateVideoUrlReq.post_id)
}
inline ::std::string* UpdateVideoUrlReq::mutable_post_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.UpdateVideoUrlReq.post_id)
  return post_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* UpdateVideoUrlReq::release_post_id() {
  // @@protoc_insertion_point(field_release:ugc.content.UpdateVideoUrlReq.post_id)
  
  return post_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void UpdateVideoUrlReq::set_allocated_post_id(::std::string* post_id) {
  if (post_id != NULL) {
    
  } else {
    
  }
  post_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.UpdateVideoUrlReq.post_id)
}

// string comment_id = 2;
inline void UpdateVideoUrlReq::clear_comment_id() {
  comment_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& UpdateVideoUrlReq::comment_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.UpdateVideoUrlReq.comment_id)
  return comment_id_.GetNoArena();
}
inline void UpdateVideoUrlReq::set_comment_id(const ::std::string& value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.UpdateVideoUrlReq.comment_id)
}
#if LANG_CXX11
inline void UpdateVideoUrlReq::set_comment_id(::std::string&& value) {
  
  comment_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.UpdateVideoUrlReq.comment_id)
}
#endif
inline void UpdateVideoUrlReq::set_comment_id(const char* value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.UpdateVideoUrlReq.comment_id)
}
inline void UpdateVideoUrlReq::set_comment_id(const char* value, size_t size) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.UpdateVideoUrlReq.comment_id)
}
inline ::std::string* UpdateVideoUrlReq::mutable_comment_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.UpdateVideoUrlReq.comment_id)
  return comment_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* UpdateVideoUrlReq::release_comment_id() {
  // @@protoc_insertion_point(field_release:ugc.content.UpdateVideoUrlReq.comment_id)
  
  return comment_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void UpdateVideoUrlReq::set_allocated_comment_id(::std::string* comment_id) {
  if (comment_id != NULL) {
    
  } else {
    
  }
  comment_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), comment_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.UpdateVideoUrlReq.comment_id)
}

// string attachment_key = 3;
inline void UpdateVideoUrlReq::clear_attachment_key() {
  attachment_key_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& UpdateVideoUrlReq::attachment_key() const {
  // @@protoc_insertion_point(field_get:ugc.content.UpdateVideoUrlReq.attachment_key)
  return attachment_key_.GetNoArena();
}
inline void UpdateVideoUrlReq::set_attachment_key(const ::std::string& value) {
  
  attachment_key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.UpdateVideoUrlReq.attachment_key)
}
#if LANG_CXX11
inline void UpdateVideoUrlReq::set_attachment_key(::std::string&& value) {
  
  attachment_key_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.UpdateVideoUrlReq.attachment_key)
}
#endif
inline void UpdateVideoUrlReq::set_attachment_key(const char* value) {
  
  attachment_key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.UpdateVideoUrlReq.attachment_key)
}
inline void UpdateVideoUrlReq::set_attachment_key(const char* value, size_t size) {
  
  attachment_key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.UpdateVideoUrlReq.attachment_key)
}
inline ::std::string* UpdateVideoUrlReq::mutable_attachment_key() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.UpdateVideoUrlReq.attachment_key)
  return attachment_key_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* UpdateVideoUrlReq::release_attachment_key() {
  // @@protoc_insertion_point(field_release:ugc.content.UpdateVideoUrlReq.attachment_key)
  
  return attachment_key_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void UpdateVideoUrlReq::set_allocated_attachment_key(::std::string* attachment_key) {
  if (attachment_key != NULL) {
    
  } else {
    
  }
  attachment_key_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), attachment_key);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.UpdateVideoUrlReq.attachment_key)
}

// string new_url = 4;
inline void UpdateVideoUrlReq::clear_new_url() {
  new_url_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& UpdateVideoUrlReq::new_url() const {
  // @@protoc_insertion_point(field_get:ugc.content.UpdateVideoUrlReq.new_url)
  return new_url_.GetNoArena();
}
inline void UpdateVideoUrlReq::set_new_url(const ::std::string& value) {
  
  new_url_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.UpdateVideoUrlReq.new_url)
}
#if LANG_CXX11
inline void UpdateVideoUrlReq::set_new_url(::std::string&& value) {
  
  new_url_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.UpdateVideoUrlReq.new_url)
}
#endif
inline void UpdateVideoUrlReq::set_new_url(const char* value) {
  
  new_url_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.UpdateVideoUrlReq.new_url)
}
inline void UpdateVideoUrlReq::set_new_url(const char* value, size_t size) {
  
  new_url_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.UpdateVideoUrlReq.new_url)
}
inline ::std::string* UpdateVideoUrlReq::mutable_new_url() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.UpdateVideoUrlReq.new_url)
  return new_url_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* UpdateVideoUrlReq::release_new_url() {
  // @@protoc_insertion_point(field_release:ugc.content.UpdateVideoUrlReq.new_url)
  
  return new_url_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void UpdateVideoUrlReq::set_allocated_new_url(::std::string* new_url) {
  if (new_url != NULL) {
    
  } else {
    
  }
  new_url_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), new_url);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.UpdateVideoUrlReq.new_url)
}

// -------------------------------------------------------------------

// UpdateVideoUrlResp

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ReportPostViewReq

// uint32 user_id = 1;
inline void ReportPostViewReq::clear_user_id() {
  user_id_ = 0u;
}
inline ::google::protobuf::uint32 ReportPostViewReq::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.ReportPostViewReq.user_id)
  return user_id_;
}
inline void ReportPostViewReq::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.ReportPostViewReq.user_id)
}

// map<string, .ugc.content.ReportPostViewReq.ViewType> post_ids = 2;
inline int ReportPostViewReq::post_ids_size() const {
  return post_ids_.size();
}
inline void ReportPostViewReq::clear_post_ids() {
  post_ids_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::ugc::content::ReportPostViewReq_ViewType >&
ReportPostViewReq::post_ids() const {
  // @@protoc_insertion_point(field_map:ugc.content.ReportPostViewReq.post_ids)
  return post_ids_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::ugc::content::ReportPostViewReq_ViewType >*
ReportPostViewReq::mutable_post_ids() {
  // @@protoc_insertion_point(field_mutable_map:ugc.content.ReportPostViewReq.post_ids)
  return post_ids_.MutableMap();
}

// -------------------------------------------------------------------

// ReportPostViewResp

// -------------------------------------------------------------------

// ReportPostShareReq

// uint32 user_id = 1;
inline void ReportPostShareReq::clear_user_id() {
  user_id_ = 0u;
}
inline ::google::protobuf::uint32 ReportPostShareReq::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.ReportPostShareReq.user_id)
  return user_id_;
}
inline void ReportPostShareReq::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.ReportPostShareReq.user_id)
}

// string post_id = 2;
inline void ReportPostShareReq::clear_post_id() {
  post_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ReportPostShareReq::post_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.ReportPostShareReq.post_id)
  return post_id_.GetNoArena();
}
inline void ReportPostShareReq::set_post_id(const ::std::string& value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.ReportPostShareReq.post_id)
}
#if LANG_CXX11
inline void ReportPostShareReq::set_post_id(::std::string&& value) {
  
  post_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.ReportPostShareReq.post_id)
}
#endif
inline void ReportPostShareReq::set_post_id(const char* value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.ReportPostShareReq.post_id)
}
inline void ReportPostShareReq::set_post_id(const char* value, size_t size) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.ReportPostShareReq.post_id)
}
inline ::std::string* ReportPostShareReq::mutable_post_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.ReportPostShareReq.post_id)
  return post_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ReportPostShareReq::release_post_id() {
  // @@protoc_insertion_point(field_release:ugc.content.ReportPostShareReq.post_id)
  
  return post_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ReportPostShareReq::set_allocated_post_id(::std::string* post_id) {
  if (post_id != NULL) {
    
  } else {
    
  }
  post_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.ReportPostShareReq.post_id)
}

// -------------------------------------------------------------------

// ReportPostShareResp

// -------------------------------------------------------------------

// AddCommentReq

// uint32 user_id = 1;
inline void AddCommentReq::clear_user_id() {
  user_id_ = 0u;
}
inline ::google::protobuf::uint32 AddCommentReq::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddCommentReq.user_id)
  return user_id_;
}
inline void AddCommentReq::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AddCommentReq.user_id)
}

// string post_id = 2;
inline void AddCommentReq::clear_post_id() {
  post_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AddCommentReq::post_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddCommentReq.post_id)
  return post_id_.GetNoArena();
}
inline void AddCommentReq::set_post_id(const ::std::string& value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AddCommentReq.post_id)
}
#if LANG_CXX11
inline void AddCommentReq::set_post_id(::std::string&& value) {
  
  post_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AddCommentReq.post_id)
}
#endif
inline void AddCommentReq::set_post_id(const char* value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AddCommentReq.post_id)
}
inline void AddCommentReq::set_post_id(const char* value, size_t size) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AddCommentReq.post_id)
}
inline ::std::string* AddCommentReq::mutable_post_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AddCommentReq.post_id)
  return post_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AddCommentReq::release_post_id() {
  // @@protoc_insertion_point(field_release:ugc.content.AddCommentReq.post_id)
  
  return post_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AddCommentReq::set_allocated_post_id(::std::string* post_id) {
  if (post_id != NULL) {
    
  } else {
    
  }
  post_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AddCommentReq.post_id)
}

// string reply_to_comment_id = 3;
inline void AddCommentReq::clear_reply_to_comment_id() {
  reply_to_comment_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AddCommentReq::reply_to_comment_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddCommentReq.reply_to_comment_id)
  return reply_to_comment_id_.GetNoArena();
}
inline void AddCommentReq::set_reply_to_comment_id(const ::std::string& value) {
  
  reply_to_comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AddCommentReq.reply_to_comment_id)
}
#if LANG_CXX11
inline void AddCommentReq::set_reply_to_comment_id(::std::string&& value) {
  
  reply_to_comment_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AddCommentReq.reply_to_comment_id)
}
#endif
inline void AddCommentReq::set_reply_to_comment_id(const char* value) {
  
  reply_to_comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AddCommentReq.reply_to_comment_id)
}
inline void AddCommentReq::set_reply_to_comment_id(const char* value, size_t size) {
  
  reply_to_comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AddCommentReq.reply_to_comment_id)
}
inline ::std::string* AddCommentReq::mutable_reply_to_comment_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AddCommentReq.reply_to_comment_id)
  return reply_to_comment_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AddCommentReq::release_reply_to_comment_id() {
  // @@protoc_insertion_point(field_release:ugc.content.AddCommentReq.reply_to_comment_id)
  
  return reply_to_comment_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AddCommentReq::set_allocated_reply_to_comment_id(::std::string* reply_to_comment_id) {
  if (reply_to_comment_id != NULL) {
    
  } else {
    
  }
  reply_to_comment_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), reply_to_comment_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AddCommentReq.reply_to_comment_id)
}

// string conversation_id = 4;
inline void AddCommentReq::clear_conversation_id() {
  conversation_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AddCommentReq::conversation_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddCommentReq.conversation_id)
  return conversation_id_.GetNoArena();
}
inline void AddCommentReq::set_conversation_id(const ::std::string& value) {
  
  conversation_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AddCommentReq.conversation_id)
}
#if LANG_CXX11
inline void AddCommentReq::set_conversation_id(::std::string&& value) {
  
  conversation_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AddCommentReq.conversation_id)
}
#endif
inline void AddCommentReq::set_conversation_id(const char* value) {
  
  conversation_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AddCommentReq.conversation_id)
}
inline void AddCommentReq::set_conversation_id(const char* value, size_t size) {
  
  conversation_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AddCommentReq.conversation_id)
}
inline ::std::string* AddCommentReq::mutable_conversation_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AddCommentReq.conversation_id)
  return conversation_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AddCommentReq::release_conversation_id() {
  // @@protoc_insertion_point(field_release:ugc.content.AddCommentReq.conversation_id)
  
  return conversation_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AddCommentReq::set_allocated_conversation_id(::std::string* conversation_id) {
  if (conversation_id != NULL) {
    
  } else {
    
  }
  conversation_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), conversation_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AddCommentReq.conversation_id)
}

// string content = 5;
inline void AddCommentReq::clear_content() {
  content_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AddCommentReq::content() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddCommentReq.content)
  return content_.GetNoArena();
}
inline void AddCommentReq::set_content(const ::std::string& value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AddCommentReq.content)
}
#if LANG_CXX11
inline void AddCommentReq::set_content(::std::string&& value) {
  
  content_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AddCommentReq.content)
}
#endif
inline void AddCommentReq::set_content(const char* value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AddCommentReq.content)
}
inline void AddCommentReq::set_content(const char* value, size_t size) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AddCommentReq.content)
}
inline ::std::string* AddCommentReq::mutable_content() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AddCommentReq.content)
  return content_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AddCommentReq::release_content() {
  // @@protoc_insertion_point(field_release:ugc.content.AddCommentReq.content)
  
  return content_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AddCommentReq::set_allocated_content(::std::string* content) {
  if (content != NULL) {
    
  } else {
    
  }
  content_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), content);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AddCommentReq.content)
}

// .ugc.content.ContentStatus status = 6;
inline void AddCommentReq::clear_status() {
  status_ = 0;
}
inline ::ugc::content::ContentStatus AddCommentReq::status() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddCommentReq.status)
  return static_cast< ::ugc::content::ContentStatus >(status_);
}
inline void AddCommentReq::set_status(::ugc::content::ContentStatus value) {
  
  status_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AddCommentReq.status)
}

// repeated .ugc.content.AttachmentInfo attachments = 7;
inline int AddCommentReq::attachments_size() const {
  return attachments_.size();
}
inline void AddCommentReq::clear_attachments() {
  attachments_.Clear();
}
inline const ::ugc::content::AttachmentInfo& AddCommentReq::attachments(int index) const {
  // @@protoc_insertion_point(field_get:ugc.content.AddCommentReq.attachments)
  return attachments_.Get(index);
}
inline ::ugc::content::AttachmentInfo* AddCommentReq::mutable_attachments(int index) {
  // @@protoc_insertion_point(field_mutable:ugc.content.AddCommentReq.attachments)
  return attachments_.Mutable(index);
}
inline ::ugc::content::AttachmentInfo* AddCommentReq::add_attachments() {
  // @@protoc_insertion_point(field_add:ugc.content.AddCommentReq.attachments)
  return attachments_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >*
AddCommentReq::mutable_attachments() {
  // @@protoc_insertion_point(field_mutable_list:ugc.content.AddCommentReq.attachments)
  return &attachments_;
}
inline const ::google::protobuf::RepeatedPtrField< ::ugc::content::AttachmentInfo >&
AddCommentReq::attachments() const {
  // @@protoc_insertion_point(field_list:ugc.content.AddCommentReq.attachments)
  return attachments_;
}

// -------------------------------------------------------------------

// AddCommentResp

// string comment_id = 1;
inline void AddCommentResp::clear_comment_id() {
  comment_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AddCommentResp::comment_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddCommentResp.comment_id)
  return comment_id_.GetNoArena();
}
inline void AddCommentResp::set_comment_id(const ::std::string& value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AddCommentResp.comment_id)
}
#if LANG_CXX11
inline void AddCommentResp::set_comment_id(::std::string&& value) {
  
  comment_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AddCommentResp.comment_id)
}
#endif
inline void AddCommentResp::set_comment_id(const char* value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AddCommentResp.comment_id)
}
inline void AddCommentResp::set_comment_id(const char* value, size_t size) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AddCommentResp.comment_id)
}
inline ::std::string* AddCommentResp::mutable_comment_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AddCommentResp.comment_id)
  return comment_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AddCommentResp::release_comment_id() {
  // @@protoc_insertion_point(field_release:ugc.content.AddCommentResp.comment_id)
  
  return comment_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AddCommentResp::set_allocated_comment_id(::std::string* comment_id) {
  if (comment_id != NULL) {
    
  } else {
    
  }
  comment_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), comment_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AddCommentResp.comment_id)
}

// .ugc.content.CommentInfo comment = 2;
inline bool AddCommentResp::has_comment() const {
  return this != internal_default_instance() && comment_ != NULL;
}
inline void AddCommentResp::clear_comment() {
  if (GetArenaNoVirtual() == NULL && comment_ != NULL) delete comment_;
  comment_ = NULL;
}
inline const ::ugc::content::CommentInfo& AddCommentResp::comment() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddCommentResp.comment)
  return comment_ != NULL ? *comment_
                         : *::ugc::content::CommentInfo::internal_default_instance();
}
inline ::ugc::content::CommentInfo* AddCommentResp::mutable_comment() {
  
  if (comment_ == NULL) {
    comment_ = new ::ugc::content::CommentInfo;
  }
  // @@protoc_insertion_point(field_mutable:ugc.content.AddCommentResp.comment)
  return comment_;
}
inline ::ugc::content::CommentInfo* AddCommentResp::release_comment() {
  // @@protoc_insertion_point(field_release:ugc.content.AddCommentResp.comment)
  
  ::ugc::content::CommentInfo* temp = comment_;
  comment_ = NULL;
  return temp;
}
inline void AddCommentResp::set_allocated_comment(::ugc::content::CommentInfo* comment) {
  delete comment_;
  comment_ = comment;
  if (comment) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AddCommentResp.comment)
}

// -------------------------------------------------------------------

// GetCommentListReq

// string post_id = 1;
inline void GetCommentListReq::clear_post_id() {
  post_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& GetCommentListReq::post_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.GetCommentListReq.post_id)
  return post_id_.GetNoArena();
}
inline void GetCommentListReq::set_post_id(const ::std::string& value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.GetCommentListReq.post_id)
}
#if LANG_CXX11
inline void GetCommentListReq::set_post_id(::std::string&& value) {
  
  post_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.GetCommentListReq.post_id)
}
#endif
inline void GetCommentListReq::set_post_id(const char* value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.GetCommentListReq.post_id)
}
inline void GetCommentListReq::set_post_id(const char* value, size_t size) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.GetCommentListReq.post_id)
}
inline ::std::string* GetCommentListReq::mutable_post_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.GetCommentListReq.post_id)
  return post_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* GetCommentListReq::release_post_id() {
  // @@protoc_insertion_point(field_release:ugc.content.GetCommentListReq.post_id)
  
  return post_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void GetCommentListReq::set_allocated_post_id(::std::string* post_id) {
  if (post_id != NULL) {
    
  } else {
    
  }
  post_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.GetCommentListReq.post_id)
}

// string conversation_id = 2;
inline void GetCommentListReq::clear_conversation_id() {
  conversation_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& GetCommentListReq::conversation_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.GetCommentListReq.conversation_id)
  return conversation_id_.GetNoArena();
}
inline void GetCommentListReq::set_conversation_id(const ::std::string& value) {
  
  conversation_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.GetCommentListReq.conversation_id)
}
#if LANG_CXX11
inline void GetCommentListReq::set_conversation_id(::std::string&& value) {
  
  conversation_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.GetCommentListReq.conversation_id)
}
#endif
inline void GetCommentListReq::set_conversation_id(const char* value) {
  
  conversation_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.GetCommentListReq.conversation_id)
}
inline void GetCommentListReq::set_conversation_id(const char* value, size_t size) {
  
  conversation_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.GetCommentListReq.conversation_id)
}
inline ::std::string* GetCommentListReq::mutable_conversation_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.GetCommentListReq.conversation_id)
  return conversation_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* GetCommentListReq::release_conversation_id() {
  // @@protoc_insertion_point(field_release:ugc.content.GetCommentListReq.conversation_id)
  
  return conversation_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void GetCommentListReq::set_allocated_conversation_id(::std::string* conversation_id) {
  if (conversation_id != NULL) {
    
  } else {
    
  }
  conversation_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), conversation_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.GetCommentListReq.conversation_id)
}

// string loadMore = 3;
inline void GetCommentListReq::clear_loadmore() {
  loadmore_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& GetCommentListReq::loadmore() const {
  // @@protoc_insertion_point(field_get:ugc.content.GetCommentListReq.loadMore)
  return loadmore_.GetNoArena();
}
inline void GetCommentListReq::set_loadmore(const ::std::string& value) {
  
  loadmore_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.GetCommentListReq.loadMore)
}
#if LANG_CXX11
inline void GetCommentListReq::set_loadmore(::std::string&& value) {
  
  loadmore_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.GetCommentListReq.loadMore)
}
#endif
inline void GetCommentListReq::set_loadmore(const char* value) {
  
  loadmore_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.GetCommentListReq.loadMore)
}
inline void GetCommentListReq::set_loadmore(const char* value, size_t size) {
  
  loadmore_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.GetCommentListReq.loadMore)
}
inline ::std::string* GetCommentListReq::mutable_loadmore() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.GetCommentListReq.loadMore)
  return loadmore_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* GetCommentListReq::release_loadmore() {
  // @@protoc_insertion_point(field_release:ugc.content.GetCommentListReq.loadMore)
  
  return loadmore_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void GetCommentListReq::set_allocated_loadmore(::std::string* loadmore) {
  if (loadmore != NULL) {
    
  } else {
    
  }
  loadmore_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), loadmore);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.GetCommentListReq.loadMore)
}

// uint32 count = 4;
inline void GetCommentListReq::clear_count() {
  count_ = 0u;
}
inline ::google::protobuf::uint32 GetCommentListReq::count() const {
  // @@protoc_insertion_point(field_get:ugc.content.GetCommentListReq.count)
  return count_;
}
inline void GetCommentListReq::set_count(::google::protobuf::uint32 value) {
  
  count_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.GetCommentListReq.count)
}

// uint32 user_id = 5;
inline void GetCommentListReq::clear_user_id() {
  user_id_ = 0u;
}
inline ::google::protobuf::uint32 GetCommentListReq::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.GetCommentListReq.user_id)
  return user_id_;
}
inline void GetCommentListReq::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.GetCommentListReq.user_id)
}

// bool ascending = 6;
inline void GetCommentListReq::clear_ascending() {
  ascending_ = false;
}
inline bool GetCommentListReq::ascending() const {
  // @@protoc_insertion_point(field_get:ugc.content.GetCommentListReq.ascending)
  return ascending_;
}
inline void GetCommentListReq::set_ascending(bool value) {
  
  ascending_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.GetCommentListReq.ascending)
}

// bool sub_comments_ascending = 7;
inline void GetCommentListReq::clear_sub_comments_ascending() {
  sub_comments_ascending_ = false;
}
inline bool GetCommentListReq::sub_comments_ascending() const {
  // @@protoc_insertion_point(field_get:ugc.content.GetCommentListReq.sub_comments_ascending)
  return sub_comments_ascending_;
}
inline void GetCommentListReq::set_sub_comments_ascending(bool value) {
  
  sub_comments_ascending_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.GetCommentListReq.sub_comments_ascending)
}

// -------------------------------------------------------------------

// GetCommentListResp

// repeated .ugc.content.CommentInfo comment_list = 1;
inline int GetCommentListResp::comment_list_size() const {
  return comment_list_.size();
}
inline void GetCommentListResp::clear_comment_list() {
  comment_list_.Clear();
}
inline const ::ugc::content::CommentInfo& GetCommentListResp::comment_list(int index) const {
  // @@protoc_insertion_point(field_get:ugc.content.GetCommentListResp.comment_list)
  return comment_list_.Get(index);
}
inline ::ugc::content::CommentInfo* GetCommentListResp::mutable_comment_list(int index) {
  // @@protoc_insertion_point(field_mutable:ugc.content.GetCommentListResp.comment_list)
  return comment_list_.Mutable(index);
}
inline ::ugc::content::CommentInfo* GetCommentListResp::add_comment_list() {
  // @@protoc_insertion_point(field_add:ugc.content.GetCommentListResp.comment_list)
  return comment_list_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::ugc::content::CommentInfo >*
GetCommentListResp::mutable_comment_list() {
  // @@protoc_insertion_point(field_mutable_list:ugc.content.GetCommentListResp.comment_list)
  return &comment_list_;
}
inline const ::google::protobuf::RepeatedPtrField< ::ugc::content::CommentInfo >&
GetCommentListResp::comment_list() const {
  // @@protoc_insertion_point(field_list:ugc.content.GetCommentListResp.comment_list)
  return comment_list_;
}

// string load_more = 2;
inline void GetCommentListResp::clear_load_more() {
  load_more_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& GetCommentListResp::load_more() const {
  // @@protoc_insertion_point(field_get:ugc.content.GetCommentListResp.load_more)
  return load_more_.GetNoArena();
}
inline void GetCommentListResp::set_load_more(const ::std::string& value) {
  
  load_more_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.GetCommentListResp.load_more)
}
#if LANG_CXX11
inline void GetCommentListResp::set_load_more(::std::string&& value) {
  
  load_more_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.GetCommentListResp.load_more)
}
#endif
inline void GetCommentListResp::set_load_more(const char* value) {
  
  load_more_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.GetCommentListResp.load_more)
}
inline void GetCommentListResp::set_load_more(const char* value, size_t size) {
  
  load_more_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.GetCommentListResp.load_more)
}
inline ::std::string* GetCommentListResp::mutable_load_more() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.GetCommentListResp.load_more)
  return load_more_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* GetCommentListResp::release_load_more() {
  // @@protoc_insertion_point(field_release:ugc.content.GetCommentListResp.load_more)
  
  return load_more_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void GetCommentListResp::set_allocated_load_more(::std::string* load_more) {
  if (load_more != NULL) {
    
  } else {
    
  }
  load_more_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), load_more);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.GetCommentListResp.load_more)
}

// -------------------------------------------------------------------

// DelCommentReq

// string post_id = 1;
inline void DelCommentReq::clear_post_id() {
  post_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& DelCommentReq::post_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.DelCommentReq.post_id)
  return post_id_.GetNoArena();
}
inline void DelCommentReq::set_post_id(const ::std::string& value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.DelCommentReq.post_id)
}
#if LANG_CXX11
inline void DelCommentReq::set_post_id(::std::string&& value) {
  
  post_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.DelCommentReq.post_id)
}
#endif
inline void DelCommentReq::set_post_id(const char* value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.DelCommentReq.post_id)
}
inline void DelCommentReq::set_post_id(const char* value, size_t size) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.DelCommentReq.post_id)
}
inline ::std::string* DelCommentReq::mutable_post_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.DelCommentReq.post_id)
  return post_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* DelCommentReq::release_post_id() {
  // @@protoc_insertion_point(field_release:ugc.content.DelCommentReq.post_id)
  
  return post_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void DelCommentReq::set_allocated_post_id(::std::string* post_id) {
  if (post_id != NULL) {
    
  } else {
    
  }
  post_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.DelCommentReq.post_id)
}

// string comment_id = 2;
inline void DelCommentReq::clear_comment_id() {
  comment_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& DelCommentReq::comment_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.DelCommentReq.comment_id)
  return comment_id_.GetNoArena();
}
inline void DelCommentReq::set_comment_id(const ::std::string& value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.DelCommentReq.comment_id)
}
#if LANG_CXX11
inline void DelCommentReq::set_comment_id(::std::string&& value) {
  
  comment_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.DelCommentReq.comment_id)
}
#endif
inline void DelCommentReq::set_comment_id(const char* value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.DelCommentReq.comment_id)
}
inline void DelCommentReq::set_comment_id(const char* value, size_t size) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.DelCommentReq.comment_id)
}
inline ::std::string* DelCommentReq::mutable_comment_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.DelCommentReq.comment_id)
  return comment_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* DelCommentReq::release_comment_id() {
  // @@protoc_insertion_point(field_release:ugc.content.DelCommentReq.comment_id)
  
  return comment_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void DelCommentReq::set_allocated_comment_id(::std::string* comment_id) {
  if (comment_id != NULL) {
    
  } else {
    
  }
  comment_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), comment_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.DelCommentReq.comment_id)
}

// string conversation_id = 3;
inline void DelCommentReq::clear_conversation_id() {
  conversation_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& DelCommentReq::conversation_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.DelCommentReq.conversation_id)
  return conversation_id_.GetNoArena();
}
inline void DelCommentReq::set_conversation_id(const ::std::string& value) {
  
  conversation_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.DelCommentReq.conversation_id)
}
#if LANG_CXX11
inline void DelCommentReq::set_conversation_id(::std::string&& value) {
  
  conversation_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.DelCommentReq.conversation_id)
}
#endif
inline void DelCommentReq::set_conversation_id(const char* value) {
  
  conversation_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.DelCommentReq.conversation_id)
}
inline void DelCommentReq::set_conversation_id(const char* value, size_t size) {
  
  conversation_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.DelCommentReq.conversation_id)
}
inline ::std::string* DelCommentReq::mutable_conversation_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.DelCommentReq.conversation_id)
  return conversation_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* DelCommentReq::release_conversation_id() {
  // @@protoc_insertion_point(field_release:ugc.content.DelCommentReq.conversation_id)
  
  return conversation_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void DelCommentReq::set_allocated_conversation_id(::std::string* conversation_id) {
  if (conversation_id != NULL) {
    
  } else {
    
  }
  conversation_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), conversation_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.DelCommentReq.conversation_id)
}

// -------------------------------------------------------------------

// DelCommentResp

// -------------------------------------------------------------------

// GetCommentByIdReq

// string comment_id = 1;
inline void GetCommentByIdReq::clear_comment_id() {
  comment_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& GetCommentByIdReq::comment_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.GetCommentByIdReq.comment_id)
  return comment_id_.GetNoArena();
}
inline void GetCommentByIdReq::set_comment_id(const ::std::string& value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.GetCommentByIdReq.comment_id)
}
#if LANG_CXX11
inline void GetCommentByIdReq::set_comment_id(::std::string&& value) {
  
  comment_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.GetCommentByIdReq.comment_id)
}
#endif
inline void GetCommentByIdReq::set_comment_id(const char* value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.GetCommentByIdReq.comment_id)
}
inline void GetCommentByIdReq::set_comment_id(const char* value, size_t size) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.GetCommentByIdReq.comment_id)
}
inline ::std::string* GetCommentByIdReq::mutable_comment_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.GetCommentByIdReq.comment_id)
  return comment_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* GetCommentByIdReq::release_comment_id() {
  // @@protoc_insertion_point(field_release:ugc.content.GetCommentByIdReq.comment_id)
  
  return comment_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void GetCommentByIdReq::set_allocated_comment_id(::std::string* comment_id) {
  if (comment_id != NULL) {
    
  } else {
    
  }
  comment_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), comment_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.GetCommentByIdReq.comment_id)
}

// -------------------------------------------------------------------

// GetCommentByIdResp

// .ugc.content.CommentInfo comment = 1;
inline bool GetCommentByIdResp::has_comment() const {
  return this != internal_default_instance() && comment_ != NULL;
}
inline void GetCommentByIdResp::clear_comment() {
  if (GetArenaNoVirtual() == NULL && comment_ != NULL) delete comment_;
  comment_ = NULL;
}
inline const ::ugc::content::CommentInfo& GetCommentByIdResp::comment() const {
  // @@protoc_insertion_point(field_get:ugc.content.GetCommentByIdResp.comment)
  return comment_ != NULL ? *comment_
                         : *::ugc::content::CommentInfo::internal_default_instance();
}
inline ::ugc::content::CommentInfo* GetCommentByIdResp::mutable_comment() {
  
  if (comment_ == NULL) {
    comment_ = new ::ugc::content::CommentInfo;
  }
  // @@protoc_insertion_point(field_mutable:ugc.content.GetCommentByIdResp.comment)
  return comment_;
}
inline ::ugc::content::CommentInfo* GetCommentByIdResp::release_comment() {
  // @@protoc_insertion_point(field_release:ugc.content.GetCommentByIdResp.comment)
  
  ::ugc::content::CommentInfo* temp = comment_;
  comment_ = NULL;
  return temp;
}
inline void GetCommentByIdResp::set_allocated_comment(::ugc::content::CommentInfo* comment) {
  delete comment_;
  comment_ = comment;
  if (comment) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:ugc.content.GetCommentByIdResp.comment)
}

// -------------------------------------------------------------------

// BatchGetCommentByIdsReq

// repeated string comment_id_list = 1;
inline int BatchGetCommentByIdsReq::comment_id_list_size() const {
  return comment_id_list_.size();
}
inline void BatchGetCommentByIdsReq::clear_comment_id_list() {
  comment_id_list_.Clear();
}
inline const ::std::string& BatchGetCommentByIdsReq::comment_id_list(int index) const {
  // @@protoc_insertion_point(field_get:ugc.content.BatchGetCommentByIdsReq.comment_id_list)
  return comment_id_list_.Get(index);
}
inline ::std::string* BatchGetCommentByIdsReq::mutable_comment_id_list(int index) {
  // @@protoc_insertion_point(field_mutable:ugc.content.BatchGetCommentByIdsReq.comment_id_list)
  return comment_id_list_.Mutable(index);
}
inline void BatchGetCommentByIdsReq::set_comment_id_list(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:ugc.content.BatchGetCommentByIdsReq.comment_id_list)
  comment_id_list_.Mutable(index)->assign(value);
}
inline void BatchGetCommentByIdsReq::set_comment_id_list(int index, const char* value) {
  comment_id_list_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:ugc.content.BatchGetCommentByIdsReq.comment_id_list)
}
inline void BatchGetCommentByIdsReq::set_comment_id_list(int index, const char* value, size_t size) {
  comment_id_list_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:ugc.content.BatchGetCommentByIdsReq.comment_id_list)
}
inline ::std::string* BatchGetCommentByIdsReq::add_comment_id_list() {
  // @@protoc_insertion_point(field_add_mutable:ugc.content.BatchGetCommentByIdsReq.comment_id_list)
  return comment_id_list_.Add();
}
inline void BatchGetCommentByIdsReq::add_comment_id_list(const ::std::string& value) {
  comment_id_list_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:ugc.content.BatchGetCommentByIdsReq.comment_id_list)
}
inline void BatchGetCommentByIdsReq::add_comment_id_list(const char* value) {
  comment_id_list_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:ugc.content.BatchGetCommentByIdsReq.comment_id_list)
}
inline void BatchGetCommentByIdsReq::add_comment_id_list(const char* value, size_t size) {
  comment_id_list_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:ugc.content.BatchGetCommentByIdsReq.comment_id_list)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
BatchGetCommentByIdsReq::comment_id_list() const {
  // @@protoc_insertion_point(field_list:ugc.content.BatchGetCommentByIdsReq.comment_id_list)
  return comment_id_list_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
BatchGetCommentByIdsReq::mutable_comment_id_list() {
  // @@protoc_insertion_point(field_mutable_list:ugc.content.BatchGetCommentByIdsReq.comment_id_list)
  return &comment_id_list_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// BatchGetCommentByIdsResp

// map<string, .ugc.content.CommentInfo> comment_infos = 1;
inline int BatchGetCommentByIdsResp::comment_infos_size() const {
  return comment_infos_.size();
}
inline void BatchGetCommentByIdsResp::clear_comment_infos() {
  comment_infos_.Clear();
}
inline const ::google::protobuf::Map< ::std::string, ::ugc::content::CommentInfo >&
BatchGetCommentByIdsResp::comment_infos() const {
  // @@protoc_insertion_point(field_map:ugc.content.BatchGetCommentByIdsResp.comment_infos)
  return comment_infos_.GetMap();
}
inline ::google::protobuf::Map< ::std::string, ::ugc::content::CommentInfo >*
BatchGetCommentByIdsResp::mutable_comment_infos() {
  // @@protoc_insertion_point(field_mutable_map:ugc.content.BatchGetCommentByIdsResp.comment_infos)
  return comment_infos_.MutableMap();
}

// -------------------------------------------------------------------

// BanCommentByIdReq

// string comment_id = 1;
inline void BanCommentByIdReq::clear_comment_id() {
  comment_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& BanCommentByIdReq::comment_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.BanCommentByIdReq.comment_id)
  return comment_id_.GetNoArena();
}
inline void BanCommentByIdReq::set_comment_id(const ::std::string& value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.BanCommentByIdReq.comment_id)
}
#if LANG_CXX11
inline void BanCommentByIdReq::set_comment_id(::std::string&& value) {
  
  comment_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.BanCommentByIdReq.comment_id)
}
#endif
inline void BanCommentByIdReq::set_comment_id(const char* value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.BanCommentByIdReq.comment_id)
}
inline void BanCommentByIdReq::set_comment_id(const char* value, size_t size) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.BanCommentByIdReq.comment_id)
}
inline ::std::string* BanCommentByIdReq::mutable_comment_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.BanCommentByIdReq.comment_id)
  return comment_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* BanCommentByIdReq::release_comment_id() {
  // @@protoc_insertion_point(field_release:ugc.content.BanCommentByIdReq.comment_id)
  
  return comment_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BanCommentByIdReq::set_allocated_comment_id(::std::string* comment_id) {
  if (comment_id != NULL) {
    
  } else {
    
  }
  comment_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), comment_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.BanCommentByIdReq.comment_id)
}

// bool is_ban = 2;
inline void BanCommentByIdReq::clear_is_ban() {
  is_ban_ = false;
}
inline bool BanCommentByIdReq::is_ban() const {
  // @@protoc_insertion_point(field_get:ugc.content.BanCommentByIdReq.is_ban)
  return is_ban_;
}
inline void BanCommentByIdReq::set_is_ban(bool value) {
  
  is_ban_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.BanCommentByIdReq.is_ban)
}

// bool is_delete = 3;
inline void BanCommentByIdReq::clear_is_delete() {
  is_delete_ = false;
}
inline bool BanCommentByIdReq::is_delete() const {
  // @@protoc_insertion_point(field_get:ugc.content.BanCommentByIdReq.is_delete)
  return is_delete_;
}
inline void BanCommentByIdReq::set_is_delete(bool value) {
  
  is_delete_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.BanCommentByIdReq.is_delete)
}

// -------------------------------------------------------------------

// BanCommentByIdResp

// -------------------------------------------------------------------

// AddAttitudeReq

// string post_id = 1;
inline void AddAttitudeReq::clear_post_id() {
  post_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AddAttitudeReq::post_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddAttitudeReq.post_id)
  return post_id_.GetNoArena();
}
inline void AddAttitudeReq::set_post_id(const ::std::string& value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AddAttitudeReq.post_id)
}
#if LANG_CXX11
inline void AddAttitudeReq::set_post_id(::std::string&& value) {
  
  post_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AddAttitudeReq.post_id)
}
#endif
inline void AddAttitudeReq::set_post_id(const char* value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AddAttitudeReq.post_id)
}
inline void AddAttitudeReq::set_post_id(const char* value, size_t size) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AddAttitudeReq.post_id)
}
inline ::std::string* AddAttitudeReq::mutable_post_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AddAttitudeReq.post_id)
  return post_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AddAttitudeReq::release_post_id() {
  // @@protoc_insertion_point(field_release:ugc.content.AddAttitudeReq.post_id)
  
  return post_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AddAttitudeReq::set_allocated_post_id(::std::string* post_id) {
  if (post_id != NULL) {
    
  } else {
    
  }
  post_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AddAttitudeReq.post_id)
}

// string comment_id = 2;
inline void AddAttitudeReq::clear_comment_id() {
  comment_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AddAttitudeReq::comment_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddAttitudeReq.comment_id)
  return comment_id_.GetNoArena();
}
inline void AddAttitudeReq::set_comment_id(const ::std::string& value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AddAttitudeReq.comment_id)
}
#if LANG_CXX11
inline void AddAttitudeReq::set_comment_id(::std::string&& value) {
  
  comment_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AddAttitudeReq.comment_id)
}
#endif
inline void AddAttitudeReq::set_comment_id(const char* value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AddAttitudeReq.comment_id)
}
inline void AddAttitudeReq::set_comment_id(const char* value, size_t size) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AddAttitudeReq.comment_id)
}
inline ::std::string* AddAttitudeReq::mutable_comment_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AddAttitudeReq.comment_id)
  return comment_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AddAttitudeReq::release_comment_id() {
  // @@protoc_insertion_point(field_release:ugc.content.AddAttitudeReq.comment_id)
  
  return comment_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AddAttitudeReq::set_allocated_comment_id(::std::string* comment_id) {
  if (comment_id != NULL) {
    
  } else {
    
  }
  comment_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), comment_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AddAttitudeReq.comment_id)
}

// uint32 user_id = 3;
inline void AddAttitudeReq::clear_user_id() {
  user_id_ = 0u;
}
inline ::google::protobuf::uint32 AddAttitudeReq::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddAttitudeReq.user_id)
  return user_id_;
}
inline void AddAttitudeReq::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AddAttitudeReq.user_id)
}

// uint32 attitude_type = 4;
inline void AddAttitudeReq::clear_attitude_type() {
  attitude_type_ = 0u;
}
inline ::google::protobuf::uint32 AddAttitudeReq::attitude_type() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddAttitudeReq.attitude_type)
  return attitude_type_;
}
inline void AddAttitudeReq::set_attitude_type(::google::protobuf::uint32 value) {
  
  attitude_type_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AddAttitudeReq.attitude_type)
}

// bool is_first_time = 5;
inline void AddAttitudeReq::clear_is_first_time() {
  is_first_time_ = false;
}
inline bool AddAttitudeReq::is_first_time() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddAttitudeReq.is_first_time)
  return is_first_time_;
}
inline void AddAttitudeReq::set_is_first_time(bool value) {
  
  is_first_time_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AddAttitudeReq.is_first_time)
}

// uint32 target_user_id = 6;
inline void AddAttitudeReq::clear_target_user_id() {
  target_user_id_ = 0u;
}
inline ::google::protobuf::uint32 AddAttitudeReq::target_user_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AddAttitudeReq.target_user_id)
  return target_user_id_;
}
inline void AddAttitudeReq::set_target_user_id(::google::protobuf::uint32 value) {
  
  target_user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AddAttitudeReq.target_user_id)
}

// -------------------------------------------------------------------

// AddAttitudeResp

// -------------------------------------------------------------------

// DelAttitudeReq

// string post_id = 1;
inline void DelAttitudeReq::clear_post_id() {
  post_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& DelAttitudeReq::post_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.DelAttitudeReq.post_id)
  return post_id_.GetNoArena();
}
inline void DelAttitudeReq::set_post_id(const ::std::string& value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.DelAttitudeReq.post_id)
}
#if LANG_CXX11
inline void DelAttitudeReq::set_post_id(::std::string&& value) {
  
  post_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.DelAttitudeReq.post_id)
}
#endif
inline void DelAttitudeReq::set_post_id(const char* value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.DelAttitudeReq.post_id)
}
inline void DelAttitudeReq::set_post_id(const char* value, size_t size) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.DelAttitudeReq.post_id)
}
inline ::std::string* DelAttitudeReq::mutable_post_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.DelAttitudeReq.post_id)
  return post_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* DelAttitudeReq::release_post_id() {
  // @@protoc_insertion_point(field_release:ugc.content.DelAttitudeReq.post_id)
  
  return post_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void DelAttitudeReq::set_allocated_post_id(::std::string* post_id) {
  if (post_id != NULL) {
    
  } else {
    
  }
  post_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.DelAttitudeReq.post_id)
}

// string comment_id = 2;
inline void DelAttitudeReq::clear_comment_id() {
  comment_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& DelAttitudeReq::comment_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.DelAttitudeReq.comment_id)
  return comment_id_.GetNoArena();
}
inline void DelAttitudeReq::set_comment_id(const ::std::string& value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.DelAttitudeReq.comment_id)
}
#if LANG_CXX11
inline void DelAttitudeReq::set_comment_id(::std::string&& value) {
  
  comment_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.DelAttitudeReq.comment_id)
}
#endif
inline void DelAttitudeReq::set_comment_id(const char* value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.DelAttitudeReq.comment_id)
}
inline void DelAttitudeReq::set_comment_id(const char* value, size_t size) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.DelAttitudeReq.comment_id)
}
inline ::std::string* DelAttitudeReq::mutable_comment_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.DelAttitudeReq.comment_id)
  return comment_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* DelAttitudeReq::release_comment_id() {
  // @@protoc_insertion_point(field_release:ugc.content.DelAttitudeReq.comment_id)
  
  return comment_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void DelAttitudeReq::set_allocated_comment_id(::std::string* comment_id) {
  if (comment_id != NULL) {
    
  } else {
    
  }
  comment_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), comment_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.DelAttitudeReq.comment_id)
}

// uint32 user_id = 3;
inline void DelAttitudeReq::clear_user_id() {
  user_id_ = 0u;
}
inline ::google::protobuf::uint32 DelAttitudeReq::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.DelAttitudeReq.user_id)
  return user_id_;
}
inline void DelAttitudeReq::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.DelAttitudeReq.user_id)
}

// uint32 target_user_id = 4;
inline void DelAttitudeReq::clear_target_user_id() {
  target_user_id_ = 0u;
}
inline ::google::protobuf::uint32 DelAttitudeReq::target_user_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.DelAttitudeReq.target_user_id)
  return target_user_id_;
}
inline void DelAttitudeReq::set_target_user_id(::google::protobuf::uint32 value) {
  
  target_user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.DelAttitudeReq.target_user_id)
}

// -------------------------------------------------------------------

// DelAttitudeResp

// -------------------------------------------------------------------

// AttitudeUserInfo

// uint32 user_id = 1;
inline void AttitudeUserInfo::clear_user_id() {
  user_id_ = 0u;
}
inline ::google::protobuf::uint32 AttitudeUserInfo::user_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AttitudeUserInfo.user_id)
  return user_id_;
}
inline void AttitudeUserInfo::set_user_id(::google::protobuf::uint32 value) {
  
  user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AttitudeUserInfo.user_id)
}

// uint32 attitude_type = 2;
inline void AttitudeUserInfo::clear_attitude_type() {
  attitude_type_ = 0u;
}
inline ::google::protobuf::uint32 AttitudeUserInfo::attitude_type() const {
  // @@protoc_insertion_point(field_get:ugc.content.AttitudeUserInfo.attitude_type)
  return attitude_type_;
}
inline void AttitudeUserInfo::set_attitude_type(::google::protobuf::uint32 value) {
  
  attitude_type_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AttitudeUserInfo.attitude_type)
}

// uint64 time = 3;
inline void AttitudeUserInfo::clear_time() {
  time_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 AttitudeUserInfo::time() const {
  // @@protoc_insertion_point(field_get:ugc.content.AttitudeUserInfo.time)
  return time_;
}
inline void AttitudeUserInfo::set_time(::google::protobuf::uint64 value) {
  
  time_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AttitudeUserInfo.time)
}

// -------------------------------------------------------------------

// GetAttitudeUserListReq

// string post_id = 1;
inline void GetAttitudeUserListReq::clear_post_id() {
  post_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& GetAttitudeUserListReq::post_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.GetAttitudeUserListReq.post_id)
  return post_id_.GetNoArena();
}
inline void GetAttitudeUserListReq::set_post_id(const ::std::string& value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.GetAttitudeUserListReq.post_id)
}
#if LANG_CXX11
inline void GetAttitudeUserListReq::set_post_id(::std::string&& value) {
  
  post_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.GetAttitudeUserListReq.post_id)
}
#endif
inline void GetAttitudeUserListReq::set_post_id(const char* value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.GetAttitudeUserListReq.post_id)
}
inline void GetAttitudeUserListReq::set_post_id(const char* value, size_t size) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.GetAttitudeUserListReq.post_id)
}
inline ::std::string* GetAttitudeUserListReq::mutable_post_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.GetAttitudeUserListReq.post_id)
  return post_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* GetAttitudeUserListReq::release_post_id() {
  // @@protoc_insertion_point(field_release:ugc.content.GetAttitudeUserListReq.post_id)
  
  return post_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void GetAttitudeUserListReq::set_allocated_post_id(::std::string* post_id) {
  if (post_id != NULL) {
    
  } else {
    
  }
  post_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.GetAttitudeUserListReq.post_id)
}

// string comment_id = 2;
inline void GetAttitudeUserListReq::clear_comment_id() {
  comment_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& GetAttitudeUserListReq::comment_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.GetAttitudeUserListReq.comment_id)
  return comment_id_.GetNoArena();
}
inline void GetAttitudeUserListReq::set_comment_id(const ::std::string& value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.GetAttitudeUserListReq.comment_id)
}
#if LANG_CXX11
inline void GetAttitudeUserListReq::set_comment_id(::std::string&& value) {
  
  comment_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.GetAttitudeUserListReq.comment_id)
}
#endif
inline void GetAttitudeUserListReq::set_comment_id(const char* value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.GetAttitudeUserListReq.comment_id)
}
inline void GetAttitudeUserListReq::set_comment_id(const char* value, size_t size) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.GetAttitudeUserListReq.comment_id)
}
inline ::std::string* GetAttitudeUserListReq::mutable_comment_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.GetAttitudeUserListReq.comment_id)
  return comment_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* GetAttitudeUserListReq::release_comment_id() {
  // @@protoc_insertion_point(field_release:ugc.content.GetAttitudeUserListReq.comment_id)
  
  return comment_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void GetAttitudeUserListReq::set_allocated_comment_id(::std::string* comment_id) {
  if (comment_id != NULL) {
    
  } else {
    
  }
  comment_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), comment_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.GetAttitudeUserListReq.comment_id)
}

// -------------------------------------------------------------------

// GetAttitudeUserListResp

// repeated .ugc.content.AttitudeUserInfo attitude_user_list = 1;
inline int GetAttitudeUserListResp::attitude_user_list_size() const {
  return attitude_user_list_.size();
}
inline void GetAttitudeUserListResp::clear_attitude_user_list() {
  attitude_user_list_.Clear();
}
inline const ::ugc::content::AttitudeUserInfo& GetAttitudeUserListResp::attitude_user_list(int index) const {
  // @@protoc_insertion_point(field_get:ugc.content.GetAttitudeUserListResp.attitude_user_list)
  return attitude_user_list_.Get(index);
}
inline ::ugc::content::AttitudeUserInfo* GetAttitudeUserListResp::mutable_attitude_user_list(int index) {
  // @@protoc_insertion_point(field_mutable:ugc.content.GetAttitudeUserListResp.attitude_user_list)
  return attitude_user_list_.Mutable(index);
}
inline ::ugc::content::AttitudeUserInfo* GetAttitudeUserListResp::add_attitude_user_list() {
  // @@protoc_insertion_point(field_add:ugc.content.GetAttitudeUserListResp.attitude_user_list)
  return attitude_user_list_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::ugc::content::AttitudeUserInfo >*
GetAttitudeUserListResp::mutable_attitude_user_list() {
  // @@protoc_insertion_point(field_mutable_list:ugc.content.GetAttitudeUserListResp.attitude_user_list)
  return &attitude_user_list_;
}
inline const ::google::protobuf::RepeatedPtrField< ::ugc::content::AttitudeUserInfo >&
GetAttitudeUserListResp::attitude_user_list() const {
  // @@protoc_insertion_point(field_list:ugc.content.GetAttitudeUserListResp.attitude_user_list)
  return attitude_user_list_;
}

// -------------------------------------------------------------------

// AppReportReq

// string post_id = 1;
inline void AppReportReq::clear_post_id() {
  post_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AppReportReq::post_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AppReportReq.post_id)
  return post_id_.GetNoArena();
}
inline void AppReportReq::set_post_id(const ::std::string& value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AppReportReq.post_id)
}
#if LANG_CXX11
inline void AppReportReq::set_post_id(::std::string&& value) {
  
  post_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AppReportReq.post_id)
}
#endif
inline void AppReportReq::set_post_id(const char* value) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AppReportReq.post_id)
}
inline void AppReportReq::set_post_id(const char* value, size_t size) {
  
  post_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AppReportReq.post_id)
}
inline ::std::string* AppReportReq::mutable_post_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AppReportReq.post_id)
  return post_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AppReportReq::release_post_id() {
  // @@protoc_insertion_point(field_release:ugc.content.AppReportReq.post_id)
  
  return post_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AppReportReq::set_allocated_post_id(::std::string* post_id) {
  if (post_id != NULL) {
    
  } else {
    
  }
  post_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), post_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AppReportReq.post_id)
}

// string comment_id = 2;
inline void AppReportReq::clear_comment_id() {
  comment_id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AppReportReq::comment_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AppReportReq.comment_id)
  return comment_id_.GetNoArena();
}
inline void AppReportReq::set_comment_id(const ::std::string& value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AppReportReq.comment_id)
}
#if LANG_CXX11
inline void AppReportReq::set_comment_id(::std::string&& value) {
  
  comment_id_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AppReportReq.comment_id)
}
#endif
inline void AppReportReq::set_comment_id(const char* value) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AppReportReq.comment_id)
}
inline void AppReportReq::set_comment_id(const char* value, size_t size) {
  
  comment_id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AppReportReq.comment_id)
}
inline ::std::string* AppReportReq::mutable_comment_id() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AppReportReq.comment_id)
  return comment_id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AppReportReq::release_comment_id() {
  // @@protoc_insertion_point(field_release:ugc.content.AppReportReq.comment_id)
  
  return comment_id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AppReportReq::set_allocated_comment_id(::std::string* comment_id) {
  if (comment_id != NULL) {
    
  } else {
    
  }
  comment_id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), comment_id);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AppReportReq.comment_id)
}

// uint32 from_user_id = 3;
inline void AppReportReq::clear_from_user_id() {
  from_user_id_ = 0u;
}
inline ::google::protobuf::uint32 AppReportReq::from_user_id() const {
  // @@protoc_insertion_point(field_get:ugc.content.AppReportReq.from_user_id)
  return from_user_id_;
}
inline void AppReportReq::set_from_user_id(::google::protobuf::uint32 value) {
  
  from_user_id_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AppReportReq.from_user_id)
}

// uint32 report_type = 4;
inline void AppReportReq::clear_report_type() {
  report_type_ = 0u;
}
inline ::google::protobuf::uint32 AppReportReq::report_type() const {
  // @@protoc_insertion_point(field_get:ugc.content.AppReportReq.report_type)
  return report_type_;
}
inline void AppReportReq::set_report_type(::google::protobuf::uint32 value) {
  
  report_type_ = value;
  // @@protoc_insertion_point(field_set:ugc.content.AppReportReq.report_type)
}

// string content = 5;
inline void AppReportReq::clear_content() {
  content_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& AppReportReq::content() const {
  // @@protoc_insertion_point(field_get:ugc.content.AppReportReq.content)
  return content_.GetNoArena();
}
inline void AppReportReq::set_content(const ::std::string& value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:ugc.content.AppReportReq.content)
}
#if LANG_CXX11
inline void AppReportReq::set_content(::std::string&& value) {
  
  content_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:ugc.content.AppReportReq.content)
}
#endif
inline void AppReportReq::set_content(const char* value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:ugc.content.AppReportReq.content)
}
inline void AppReportReq::set_content(const char* value, size_t size) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:ugc.content.AppReportReq.content)
}
inline ::std::string* AppReportReq::mutable_content() {
  
  // @@protoc_insertion_point(field_mutable:ugc.content.AppReportReq.content)
  return content_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* AppReportReq::release_content() {
  // @@protoc_insertion_point(field_release:ugc.content.AppReportReq.content)
  
  return content_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void AppReportReq::set_allocated_content(::std::string* content) {
  if (content != NULL) {
    
  } else {
    
  }
  content_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), content);
  // @@protoc_insertion_point(field_set_allocated:ugc.content.AppReportReq.content)
}

// -------------------------------------------------------------------

// AppReportResp

#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)


}  // namespace content
}  // namespace ugc

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::ugc::content::AttachmentInfo_AttachmentType> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::ugc::content::AttachmentInfo_AttachmentType>() {
  return ::ugc::content::AttachmentInfo_AttachmentType_descriptor();
}
template <> struct is_proto_enum< ::ugc::content::PostInfo_PostType> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::ugc::content::PostInfo_PostType>() {
  return ::ugc::content::PostInfo_PostType_descriptor();
}
template <> struct is_proto_enum< ::ugc::content::AddPostDirectlyReq_Availability> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::ugc::content::AddPostDirectlyReq_Availability>() {
  return ::ugc::content::AddPostDirectlyReq_Availability_descriptor();
}
template <> struct is_proto_enum< ::ugc::content::ReportPostViewReq_ViewType> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::ugc::content::ReportPostViewReq_ViewType>() {
  return ::ugc::content::ReportPostViewReq_ViewType_descriptor();
}
template <> struct is_proto_enum< ::ugc::content::ContentStatus> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::ugc::content::ContentStatus>() {
  return ::ugc::content::ContentStatus_descriptor();
}

}  // namespace protobuf
}  // namespace google
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_content_2eproto__INCLUDED
