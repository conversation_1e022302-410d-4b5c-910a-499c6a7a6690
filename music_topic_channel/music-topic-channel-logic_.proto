syntax = "proto3";

package ga.music_topic_channel;

import "ga_base.proto";
import "topic_channel/topic_channel_.proto";
import "channel_play/channel-play_.proto";
import "im/im.proto";
import "muse_interest_hub_logic/muse_interest_hub_logic.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/music-topic-channel";

//获取筛选器
message GetMusicChannelFilterV2Req{
  BaseReq base_req = 1;
  string filter_type = 2;//默认音乐首页
}

message GetMusicChannelFilterV2Resp{
  BaseResp base_resp = 1;
  repeated FilterItem filter_items = 2;
  message FilterItem {
    string title = 1;
    string filter_item_type = 2;//首页Item HOME_FILTER_ITEM,页面房间Item PAGE_FILTER_ITERM,页面帖子 PAGE_POST
    string filter_id = 3;//具体接口的通用参数，后续可能还需要不同类型有不同参数
    repeated FilterSubItem filter_sub_items = 4;
    string tip = 5;
    uint32 filter_attr_type = 6; // 标签属性  FilterAttrType
    SameCityTitle city_title = 7; // 标签是同城属性则使用此结构体信息替换title
  }

  message FilterSubItem {
    string title = 1;
    string filter_sub_id = 2;//具体接口的通用参数，后续可能还需要不同类型有不同参数
    string filter_sub_item_type = 3;//SUB_ITEM_POST帖子,SUB_ITEM_MUSIC音乐流
  }

  enum FilterItemType {
    HOME_FILTER_ITEM = 0;
    PAGE_FILTER_ITERM = 1;
    PAGE_POST = 2;
  }
}

// 标签属性
enum FilterAttrType{
  FILTER_ATTR_TYPE_UNEXPECTED = 0; // 默认
  FILTER_ATTR_TYPE_SAME_CITY = 1; // 同城属性tab 根据是否打开定位授权 展示同城tab的城市信息
}

// 标签是同城则使用此结构体信息替换title
message SameCityTitle{
  string city_name = 1;
  string province_name = 2;
}

//房间流请求
message ListHobbyChannelV2Req {
  BaseReq base_req = 1;

  uint32  count = 2; // 每页多少个

  int32 sex = 3; // 性别 0-不过滤 1-选择Female 2-选择Male

  string filter_id = 4;

  bool is_start_load = 5; // true 代表第一次开始获取和下拉刷新

  repeated uint32 expose_channel_ids = 6;//推荐曝光上报

  repeated string filter_sub_ids = 7;//二级多选

  string channel_package_id = 8;        // 渠道包id

  bool is_user_location_auth_open = 9; // 用户定位授权是否开启
  NotExposeChannelList not_expose_channel = 10; //未曝光的房间

  FilterModel filter_model = 11; // 筛选方式
  repeated uint32 tab_ids = 12;
  repeated channel_play.FilterBlockOption block_option = 13;
  repeated topic_channel.GameLabel labels = 14; // 标签筛选
}

enum FilterModel {
  MtModel = 0; // mt筛选，默认筛选方式
  KhModel = 1; // 开黑筛选方式
}

//音乐首页控件
message GetMusicHomePageViewV2Req{
  BaseReq base_req = 1;
}

message GetMusicHomePageViewV2Resp{
  BaseResp base_resp = 1;
  MusicHomePageType type = 2;//首页空间展示类型
  repeated MusicHomePageV2View full_type_views = 3;
  repeated MusicHomePageV2View scroll_type_views = 4;

  enum MusicHomePageType {
    MusicHomePage_Full = 0;
    MusicHomePage_Scroll = 1;
  }

  message MusicHomePageV2View{
    string title = 1;//主标题
    string sub_title = 2;//副标题
    string icon = 3;//图标
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    ActionType actionType = 4;
    repeated MusicHomePageV2View sub_views = 5;
    string quick_match_id = 6;//Quick_Match使用
    string url = 7;//Url使用
    uint32 tag_id = 8;//你行你唱的TagId=GameType
    string bg_img = 9;
    string view_id = 10;
  }

  enum ActionType {
    Sing_A_Round_Dialog = 0;
    Quick_Match = 1;
    Url = 2;
    Sing_A_Round_Quick_Match = 3;
  }
}



//获取对话框
message GetMusicHomePageDialogV2Req{
  BaseReq base_req = 1;
  MusicHomePageDialogType type = 2;
}

enum MusicHomePageDialogType {
  Ktv_Dialog = 0;
}

message GetMusicHomePageDialogV2Resp{
  BaseResp base_resp = 1;
  repeated DialogView views = 2;

  message DialogView{
    string title = 1;
    string sub_title = 2;
    string image = 3;
    string back_color = 4;
    string quick_match_id = 5;
  }
}




//快速匹配
message QuickMatchHobbyChannelV2Req {
  BaseReq base_req = 1;

  string quick_match_id = 2;//后台指定过滤id

  string channel_package_id = 3;        // 渠道包id
}

message QuickMatchHobbyChannelV2Resp {
  BaseResp base_resp = 1;

  uint32 channel_id = 2;
  string footprint = 3;//推荐上报
}

// 重逢互动 push
message ReunionInteractionPush {
  ReunionUser join_user = 1; // 进房用户
  ReunionUser reunion_user = 2; // 与进房用户重逢的用户
  string text = 3;
  string mic_text = 4;
  string meta_id = 5;   //透传给客户端,客户端上报到数仓
}

message ReunionUser {
  uint32 uid = 1;
  string username = 2;
  string nickname = 3;
  int32 sex = 4;
}


//发布房间
message PublishMusicChannelReq {
  BaseReq base_req = 1;

  uint32 channel_id = 2;
  uint32 tab_id = 3;               // 主题房所选分类id
  repeated topic_channel.BlockOption block_options = 4;  // 用户所选的标签信息, see topic-channel_.proto
  string channel_name = 5;     // 主题房名
  bool show_geo_info = 6;                     //是否显示地理信息
  uint32 jump_tab_id = 7;                     // 跳转tabId
}

message PublishMusicChannelResp {
  BaseResp base_resp = 1;
  uint32 change_cool_down = 2;         // 房间修改发布信息剩余冻结时长
  uint32 freeze_duration = 3;         // 房间发布信息剩余冻结时长
  uint32 auto_dismiss_duration = 4;   // 自动取消时长
}

//房间发布取消
message CancelMusicChannelPublishReq {
  BaseReq base_req = 1;
  uint32 channel_id = 2;
}

message CancelMusicChannelPublishResp {
  BaseResp base_resp = 1;
}

message GetMusicFilterItemByIdsReq{
  BaseReq base_req = 1;
  repeated string filter_ids = 2;
}

message GetMusicFilterItemByIdsResp{
  BaseResp base_resp = 1;
  map<string, MusicFilterItem> filter_map = 2;//key:filterId
}

message MusicFilterItem{
  string filter_id = 1;
  string name = 2;
  string icon = 3;//图标
  repeated string images = 4;//一般没值，live返回2张图片，第一张为未选中状态，第二张为选中状态
  repeated MusicFilterItem sub_filters = 5;
  uint32 filter_attr_type = 6; // 标签属性  FilterAttrType
  SameCityTitle city_title = 7; // 标签是同城属性则使用此结构体信息替换title
}

message ListMusicChannelsReq{
  BaseReq base_req = 1;
  string filter_id = 2;//一级筛选
  repeated string sub_filter_ids = 3;//二级筛选
  uint32 count = 4;//每页数量
  bool is_start_load = 5; // true 代表第一次开始获取和下拉刷新
  repeated uint32 expose_channel_ids = 6;//推荐曝光上报
  string channel_package_id = 7;        // 渠道包id
  bool is_user_location_auth_open = 8; // 用户定位授权是否开启
  NotExposeChannelList not_expose_channel = 9; //未曝光的房间

  FilterModel filter_model = 10; // 筛选方式
  repeated uint32 tab_ids = 11;
  repeated channel_play.FilterBlockOption block_option = 12;
  repeated topic_channel.GameLabel labels = 13; // 标签筛选
  repeated ClassifyLabellist classify_labels = 14; // 分类下的标签列表
  repeated string shield_filter_words = 15;  // 用户设置的屏蔽词
}

message ClassifyLabellist {
  string classify_name = 1; // 分类名称
  repeated topic_channel.GameLabel classify_labels = 2; // 分类下的标签列表
}

// 未曝光的房间列表
message NotExposeChannelList {
  string filter_id = 1;
  repeated uint32 not_expose_channel_id_list = 2; //未曝光的房间ID
}

message ListMusicChannelsResp{
  BaseResp base_resp = 1;
  repeated MusicChannel channels = 2;
  bool is_bottom_reach = 3;
}

message MusicChannel{
  uint32 channel_id = 1;
  string channel_name = 2;//房间名
  uint32 channel_member_count = 3;//在房人数

  string tab_icon = 4;//左上角icon
  string tab_desc = 5;//左上角文案 e.g. K歌•合唱

  string owner_account = 6;//房主头像
  int32 owner_sex = 7;//房主性别
  repeated string accounts = 8;//其他头像
  string status = 9;//房间状态 e.g. 合唱中

  string song = 10;//当前歌曲

  MusicChannelReview review = 11;//重逢

  MusicChannelLabel label = 12;//房间标签

  KtvGlory glory = 13;//称号

  MusicChannelPersonalCert personal_cert = 14;//个人认证标签

  //非业务必须字段，埋点需要
  string footprint = 15; //推荐trace id
  uint32 tab_id = 16;//玩法id
  uint64 region_id = 17;//blockId与elemId的拼接或者你行你唱的tagId,客户端埋点需要

  uint32 channel_level_id = 18;

  //非业务必须字段，埋点需要
  string tab_name = 19;

  MusicPia pia = 20;//pia戏特殊结构，不是pia戏，为空
  MusicInteresting interesting = 21;//兴趣特殊结构，不是兴趣，为空

  string logo = 22;//左边方形图片，优先房主展示
  MusicSocial music_social = 23;//社群
  muse_interest_hub_logic.TopicChannelSameCityInfo same_city = 24; // 同城信息
  uint32 channel_type = 25; // 房间类型
  string topic = 26;//房间话题
  string topic_icon = 27;//话题icon
  int32 topic_type = 28;//0-话题，1-热聊话题
  repeated MuseShiningPoint shining_point = 29; // 闪光点
}

message MuseShiningPoint {
  string shining_point_id = 1; // 闪光点id
  string shining_point_name = 2; // 闪光点名称
  uint32 shining_point_cert_type = 3; // 闪光点 认证类型 ShiningPointCertInfo muse_shining_point_logic.proto
}

message MusicSocialRankHonorSignInfo{
  string icon = 1; /* icon */
  repeated string style_color_list = 2; /* 样式 底色 */
  string text = 3; /* 文案 */
}

message MusicSocial {
  string member_label_bg = 2;
  string member_label_text = 3;
  MusicSocialRankHonorSignInfo rank_sign_info = 4; // 榜单（周榜）的荣誉标识
}

message MusicPia {
  repeated string label = 1; //剧本标签
  string name = 2; // 剧本名称
}

message MusicInteresting {
  string topic = 1; // 话题
}

enum MusicChannelLabel {
  MusicChannelLabelNone = 0;
  MusicChannelLabelQuality = 1;//优质
  MusicChannelLabelHot = 2;//热门
}

//重逢
message MusicChannelReview{
  string review_account = 1;//重逢头像
  string review_desc = 2;//重逢文案
  int32 review_sex = 3;//重逢用户性别
}

message KtvGlory {
  string glory_name = 1; // 称号名称
  string glory_img = 2; // 头标
  string glory_bg_img = 3; // 背景颜色
  uint32 glory_rank = 4; // 排行
}

message MusicChannelPersonalCert{
  string icon = 1;
  string text = 2;
  repeated string color = 3;
  string text_shadow_color = 4;
}

message GetTabPublishHotRcmdReq{
  BaseReq base_req = 1;
  uint32 tab_id = 2;
}

message GetTabPublishHotRcmdResp{
  BaseResp base_resp = 1;
  repeated TabPublishHotRcmd items = 2;
}

message TabPublishHotRcmd{
  string id = 1;
  string name = 2;
  string hint = 3;
  repeated MusicBlock blocks = 4;
}

message MusicBlock {
  uint32 block_id = 1;
  uint32 element_id = 2;
}

message GetResourceConfigByChannelIdReq{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
}
message GetResourceConfigByChannelIdResp{
  ga.BaseResp base_resp = 1;
  string text = 2;
  string icon = 3;
  string jump_url = 4;
}

message MuseGetTopicChannelInfoRequest{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  enum ChannelInfoType {
    CHANNEL_INFO_TYPE_NORMAL_UNSPECIFIED = 0;
    CHANNEL_INFO_TYPE_PLAY_TYPE = 1;  // 指定返回切换玩法类型房间数据
  }
  ChannelInfoType channel_info_type = 3; // 获取房间信息类型， V-5.0.2之前没有这个字段
  uint32 channel_type = 4; // 房间类型,see enum ChannelType
}

message MuseGetTopicChannelInfoResponse{
  ga.BaseResp base_resp = 1;
  uint32 channel_id = 2;
  uint32 tab_id = 3;                     // 主题房所选分类id
  string tab_name = 4;                   // 主题房的标签名
  bool is_in_ground = 5;                 //
  bool is_private = 6;                   // 主题房是否私密房间
  string head_desc = 7;                  // 5vs5 | 匹配模式 | 安卓QQ 那行字
  repeated MusePlayingOption playing_option = 8;      // 找陌生人玩&找好友玩
  MuseSwitchPlayInfo switch_play_info = 9;   // 切换玩法需要的玩法和玩法对应房间模式
  enum TabType {
    TAB_TYPE_NORMAL_UNSPECIFIED = 0; //普通分类
    TAB_TYPE_GAME = 1; //游戏分类
    TAB_TYPE_MINI_GAME = 2; // 小游戏
  }
  TabType tab_type = 10;                 // 主题房标签类型
  string welcome_text = 11;              // 欢迎语
  // 从房间内跳到指定外部第三方游戏相关配置(即下载游戏)
  MuseThirdPartyGame third_party_game = 12;
  uint32 shift_room_duration = 13;      //房间转移时长测试
  uint32 freeze_duration = 14;           //冻结时长
  uint32 tag_id = 15;                    //游戏id
  string team_desc = 16;                 //小队被移除但房间的小队信息要在房间内保留（[区服][模式]-[人数]，正在找：[想找分路1]、[想找分路2]，地图：[地图1]、[地图2]）
  bool show_team_desc = 17;              //是否该有小队信息，没有小队服务了只能自己判断了

  bool show_publish_button = 18;    //是否展示发布按钮

  MuseCategoryType category_type = 19; //标识当前tab所属的分类类别，1 一起开黑
}
enum MuseCategoryType {
  MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_UNSPECIFIED = 0;
  MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_GANGUP_TYPE = 1;
  MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_CHAT_TYPE = 2;  // 旧版扩列聊天类型
  MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_FUN_GAME_TYPE = 3; // 趣味玩法
  MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_CASUAL_INTERACTION_TYPE = 4; // 休闲互动
  MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_MUSIC_TYPE = 5; // 听歌唱歌
  MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_MELEE_TYPE = 6; // 团战
  MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_ESCAPE_ROOM_TYPE = 7; // 密室逃脱
  MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_GROUP_CHAT_TYPE = 8; // 群聊派对
  MUSE_SOCIAL_COMMUNITY_CATEGORY_TYPE_NEW_CHAT_TYPE = 9; //新扩列聊天分类
}

enum MusePlayingOption {
  MUSE_SOCIAL_COMMUNITY_PLAYING_OPTION_AT_MAIN_PAGE_UNSPECIFIED = 0;       //默认大厅找人玩
  MUSE_SOCIAL_COMMUNITY_PLAYING_OPTION_FRIEND = 1;             //找好友玩
}
message MuseSwitchPlayInfo {
  uint32 room_model = 3; /* see EChannelMicMode */
}
message MuseThirdPartyGame {
  string label_url = 1; // 图标
  string public_url = 2; // 公屏图片

  message MuseGameBaseInfo {
    string platform = 1;
    string jump_url = 2; // 跳转地址
    string download_url = 3; // 下载地址
    string package_name = 4; // ','号分割
  }
  repeated MuseGameBaseInfo game_base_info = 3; // 第三方游戏基础信息
}

// 主题房处罚类型
enum TopicChannelPunishType{
  UnknownPunishType = 0;
  WarnType = 1; // 主题房全体用户警告 TopicChannelUserWarnNotifyInChannelMsg
  KickOutChannelType = 2; // 连带麦上用户限制进房5min TopicChannelUserWarnNotifyMsg
  PublishChannelLimit = 3; // 限制发布主题房 TopicChannelUserWarnNotifyMsg
}

// 主题房处罚推送
message TopicChannelUserWarnNotifyMsg{
  uint32 punish_type = 1; // TopicChannelPunishType
  uint32 channel_id = 2;
  repeated uint32 uid_list = 3;
  repeated ga.im.RichTextElement in_value = 4;  // 弹窗文案
}

// 主题房处罚推送房间
message TopicChannelUserWarnNotifyInChannelMsg{
  uint32 channel_id = 2;
  repeated ga.im.RichTextElement in_value = 4;  // 弹窗文案
}

message TopicChannelUserWarnLinkJumpURL{
  map<string, string> jump_url_map = 1 [deprecated = true]; // 跳转链接map（根据market_id、clienttype），proto序列化后放在富文本的RichTextLink，jump_url中
}

message GetAssociateRevChannelsRequest{
  ga.BaseReq base_req = 1;
  string next_token = 2;
  uint32 limit = 3;
  uint32 channel_id = 4;
  uint32 tab_id = 5;
}

message GetAssociateRevChannelsResponse{
  ga.BaseResp base_resp = 1;
  string next_token = 2;
  string title = 3;
  repeated MuseRevChannel channels = 4;
}

message MuseRevChannel{
  uint32 channel_id = 1;
  string simple_desc = 2;//左上角描述
  string channel_name = 3;
  string channel_owner_account = 4;
  int32 channel_owner_sex = 5;
  string channel_owner_name = 6;

  MuseRevChannel channel_hot = 7[deprecated = true];

  string channel_icon_md5 = 8;
  MuseRevChannelHot rev_channel_hot = 9;
  uint32 channel_type = 10;
}

message MuseRevChannelHot{
  string icon = 1;
  int64 count = 2;
}

message ListMuseSocialCommunityChannelsRequest{
  ga.BaseReq base_req = 1;
  string social_community_id = 2;
}

message ListMuseSocialCommunityChannelsResponse{
  ga.BaseResp base_resp = 1;
  repeated MuseSocialCommunityChannelInfo channels = 2;
}

message MuseSocialCommunityChannelInfo {
  uint32 channel_id = 1;
  string channel_name = 2;//房间名
  uint32 channel_member_count = 3;//在房人数

  string tab_icon = 4;//左上角icon
  string tab_desc = 5;//左上角文案 e.g. K歌•合唱

  string owner_account = 6;//房主头像
  int32 owner_sex = 7;//房主性别
  repeated string accounts = 8;//其他头像
  string status = 9;//房间状态 e.g. 合唱中

  string song = 10;//当前歌曲

  uint32 label = 12;//房间标签,1-优质，2-热门

  KtvGlory glory = 13;//称号

  MusicChannelPersonalCert personal_cert = 14;//个人认证标签

  uint32 tab_id = 16;//玩法id
  uint64 region_id = 17;//blockId与elemId的拼接或者你行你唱的tagId,客户端埋点需要

  uint32 channel_level_id = 18;

  //非业务必须字段，埋点需要
  string tab_name = 19;

  MusicPia pia = 20;//pia戏特殊结构，不是pia戏，为空
  MusicInteresting interesting = 21;//兴趣特殊结构，不是兴趣，为空

  string logo = 22;//左边方形图片，优先房主展示
  MuseSocialCommunityChannelLabel muse_label = 23;//房间社群标签
  uint32 channel_type = 25; // 房间类型
  string topic = 26;//房间话题
  string topic_icon = 27;//话题icon
  int32 topic_type = 28;//0-话题，1-热聊话题
}

message MuseSocialCommunityChannelLabel{
  string account = 1;
  string text = 2;
}

// 非上麦好友邀请进房 push
message MTInviteUserEnterChannelWelcomePush {
  string text = 1;
}


//主题房内用户累计关注推送

message FollowUserInfo {
  uint32 uid=1;
  string nickname=2;
  uint32 channel_role=3;   //channel_.proto     EChannelAdminRoleType
  string account=4;

}

message UserFollowInChannel{
  uint32 scene=1;   //1--关注房主   2--累计关注非房主
  repeated FollowUserInfo follow_users=2;
  repeated FollowUserInfo followed_user=3;
  uint32 origin_source=4;// ugc_.proto    Source
}

enum UserFollowInChannelScene{
  USER_FOLLOW_IN_CHANNEL_SCENE_UNSPECIFIED=0;
  USER_FOLLOW_IN_CHANNEL_SCENE_OWNER=1;
  USER_FOLLOW_IN_CHANNEL_SCENE_NORMAL=2;
}


message PreferenceKeywords{
  repeated PreferenceKeyword preference_keywords=1;
}

message PreferenceKeyword{
  string id=1;
  string keyword=2;
}

message ListChannelPreferenceKeywordsRequest{
  ga.BaseReq base_req = 1;
  int32 tab_id=2;
}

message ListChannelPreferenceKeywordsResponse{
  ga.BaseResp base_resp = 1;
  repeated  PreferenceKeywords preference_keywords=2;
}
