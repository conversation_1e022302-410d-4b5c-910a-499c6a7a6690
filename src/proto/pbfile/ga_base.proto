syntax="proto2";

package ga;

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app";

message AntispamToken {
    optional string token = 1;
}

message AntispamVerifyCodeInfo {
    optional string captcha_id = 1;
    optional string validate = 2;
}

message GpsLocation {
    optional double gps_longitude = 1;  // 经度
    optional double gps_latitude = 2;   // 纬度
}

message ShumeiAntispam {
    optional string sm_device_id = 1;   // 数美SDK生成的设备 ID
}

message AntispamInfomation {
    optional GpsLocation gps_location = 1;        // 行为发生时用户所在的GPS地理位置信息
    optional ShumeiAntispam sm_antispam = 2;      // 数美反垃圾
    optional string net = 3;					  // 4G/wifi 网络状态
    optional string language = 4;				  // cn/zh 这个可能对于国内app没啥使用度
    optional string ssid = 5;					  // 链接wifi的名称 比如 TP_Link
    optional string free_storage = 6; 			  // 剩余硬盘容量
    optional string resolution = 7;				  // 屏幕分辨率
    optional string system_volume = 8;			  // 目前手机音量
    optional string total_storage = 9;			  // 手机总容量
    optional string mac = 10;					  // 这个比较常规，设备网络唯一标识
    optional string battery_state = 11;			  // 目前充电状态（在充电,未充电）
    optional string memory = 12;				  // 内存数量
    optional string device_carrier = 13;		  // 网络运营商
    optional string battery_level = 14;			  // 电池剩余容量
    optional string device_name = 15; 			  // 设备名称（比如 龚磊的iphone)
    optional bool is_disable = 17; // 是否启用 关闭shumei
}

// 所有请求包都需要有这个字段
message BaseReq {
    enum MarketId {
        MARKET_NONE = 0;  // TT原版
        MARKET_LITE = 1;  // 轻量版，删除某些功能，例如欢城。
        MARKET_HUANYOU = 2; // 欢游，TT的紧急替代版本。
        MARKET_ZAIYA = 3;   // 在呀，TT的紧急替代版本。
		
        MARKET_GAME_SDK = 12; // sdk 客户端, 值与其 appid 相同
    }
    optional uint32 app_id = 1;    // APPID 用于标识请求者的应用,TT应用可以不填,默认的TT的APPID为0,欢城APPID为11 具体参见protodef.h
    optional uint32 market_id = 2; // market id 用于标识市场买量包，TT可不填，默认为0。有些买量包希望能够独立升级，所以会使用该id来区分。@see MarketId.
    optional AntispamToken antispam_token = 3; // 易盾反垃圾token，服务器根据token判断用户本次行为是否异常
    optional AntispamVerifyCodeInfo verify_code_info = 4; // 易盾验证码校验信息
    optional AntispamInfomation antispam_info = 5;       // 用于统一放置反垃圾、反作弊的相关信息
    optional ShuMeiVerifyInfo verify_info = 6; /* 数美验证 滑块二次验证 */
    optional string request_id = 7; /* 请求跟踪id */
}

// 所有返回包都需要都这个字段
message BaseResp {
    enum Result{
        SUCCESS = 0;
        SESSION_ERR = -1;
        SESSION_EXPIRED = -2;
    }
    required int32 ret      = 1;      // 返回码：0 为成功， <0皆为失败
    required string err_msg = 2;      // 错误信息
    optional uint32 app_id   = 3;     // APPID 用于标识请求者的应用,TT应用可以不填,默认的TT的APPID为0,欢城APPID为11 具体参见protodef.h
    optional bytes err_info = 4;      // 额外的错误信息 json
    optional string success_msg = 5;  // 成功后也有可能会有 toast 信息
    // 6 去哪里了???，https://gitlab.ttyuyin.com/tt-protocols/app.git 上面有6的
    optional string request_id = 7;   // 请求跟踪id
}

message RespWithBaseRespOnly {
    required BaseResp base_resp = 1;
}

//-------------------------------------------------------------------
// base
//-------------------------------------------------------------------
//联系人结构
message Contact{
    enum ContactType {
        COMMON = 0;     // 普通用户
        OFFICIAL = 1;   // 官方号
        SYSTEM = 2;     // 系统账号
    }
    required string user_account = 1; //账号
    required string account_alias = 2; //账号别名
    required uint32 uid = 3;
    required string nick_name = 4; // 昵称
    required string nick_remark = 5;//昵称备注
    required string nick_pinyin = 6;//昵称拼音
    required uint32 user_sex = 7; //用户性别
    required string face_md5 = 8; //用户头像
    required string cover_md5 = 9;//用户封面
    required uint32 user_type = 10;//联系人类型, see enum ContactType
    required uint32 source_flag = 11;//来源标识,掩码.e.g: 同一公会、手机联系人
    required uint32 relationship = 12; //与该联系人关系: 0 自己  1 好友  2、待验证：我发起的  3、待验证：对方发起的 4 陌生人 5、工会好友
    required uint32 add_time = 13; //添加时间
    required bool is_star_marked = 14;//星标好友
    required bool is_banked = 15;//黑名单
    required string signature = 16;//签名
    required string remark_pinyin = 17;//备注拼音
    optional uint32 guild_id = 18;		// 工会id
    optional GrowInfo grow_info = 19;
    optional uint32 friend_src_type = 20; // contact.proto FRIEND_SRC_TYPE
}

//公会成员
message GuildMember {
    enum ROLE_TYPE {
        NOT_MEMBER = 0;		// 非公会成员
        CHAIRMAN = 1;		// 会长
        ADMIN = 2;			// 管理员
        COMMON = 3;			// 普通成员
        OFFICIAL = 4;       // 官员
    }

    enum OptInvalidType {
        CHECK_IN = 1;		// 签到
        DONATE = 2;			// 捐献
        //xxx = 4;
    }

    required uint32 uid = 1;
    required string name = 2;		// 昵称
    required uint32 role = 3;
    required string remark = 4;		// 勋章、称号（资深玩家）
    required string account = 5;	// 用户唯一账号
    required uint32 guild_id = 6;	// 工会id
    required string face_md5 = 7;	// 头像md5
    required uint32 sex = 8;		// 性别
    required string pinyin = 9;		//客户端排序用的拼音
    required bool enabled_prefix = 10; //马甲开关
    required uint32 permission_bit = 11;	// 工会权限
    required uint32 activeness  = 12; //活跃度
    optional GrowInfo grow_info = 13;

    // 1.8.1 新增
    repeated uint32 owner_guild_group_id_list = 14; //此人是哪些公会群的群主
    repeated uint32 manager_guild_group_id_list = 15; //此人是哪些公会群的管理员

    optional string account_alias = 16; //账号别名
    optional uint32 join_at = 17; // 加入公会时间
    optional string title = 18;	// 公会称号
    optional uint32 permission_bit_v2 = 19; // 公会新权限
    optional string official_name = 20;     //官员名称
    optional uint32 member_level = 21;	// 公会成员等级
    optional uint32 opt_invalid = 22;		// OptInvalidType 签到、捐献等是否有效

}

//公会群
message GuildGroup {
    enum MemberRole {
        NONE = 0;		// 未加入此群
        OWNER = 1;		// 群主
        ADMIN = 2;		// 管理员
        COMMON = 3;		// 普通成员
    }
    enum GroupType {
        Group_TYPE_GUILD_MAIN = 0; //公会主群
        Group_TYPE_GAME_MAIN = 1;	   //游戏主群
        Group_TYPE_GAME = 2;		//游戏群
        Group_TYPE_TEMP = 3;		//临时群
        Group_TYPE_TGROUP = 4;		// 群组
    }
    enum RECV_MSG_OPT {
        DEFAULT_RECV = 0;
        NOT_RECV = 1;
    }
    required uint32 group_id = 1;
    required string name = 2;
    required uint32 member_count = 3;
    required uint32 my_role = 4;          	// 我在此群的角色。如果未加入群，此字段无意义
    required uint32 game_id = 5;			// 游戏id，如果为0则表示主群
    required uint32 create_time = 6;		// 群创建时间
    required uint32 need_verify = 7;		// 加入本群是否需要验证
    required uint32 group_type = 8;			// 群类型
    required string group_account = 9;		// 群帐号
    required string my_group_card = 10;		// 我的群名片（备注）
    required uint32 my_join_time = 11;		// 我入群的时间
    required bool is_all_mute = 12;			// 全员禁言
    optional uint32 owner_uid = 13;			// 群主uid
    repeated uint32 admin_uid_list = 14;	// 群管理uid
    required string face_md5 = 15;			// 头像md5
    optional uint32 recv_msg_opt = 16;		// 接收群消息设置（0:正常接收，1:完全不接收本群消息）
    optional string game_name = 17;         // 游戏名
}

//公会签到信息
message GuildCheckin {
    required uint32 uid = 1;
    required string user_account = 2;
    required string user_nick = 3;
    required uint32 days = 4;           	// 连续签到天数
    optional uint32 accum_days = 5;			// 累计签到天数
    optional uint32 member_lv = 6;		// 公会成员等级
}

//公会捐献信息
message GuildDonate {
    required uint32 uid = 1;
    required string user_account = 2;
    required string user_nick = 3;
    required uint32 days = 4;           	// 连续捐献天数
    optional uint32 accum_donate_value = 5;	// 累计捐献值
    optional uint32 last_donate_value = 6;	// 上次捐献值
    optional uint32 member_lv = 7;			// 公会成员等级
}

// 公会成员贡献信息
message GuildMemberContribution {
    required uint32 uid = 1;
    required string user_account = 2;
    required string user_nick = 3;
    optional uint32 total_contribution = 4;	// 历史总贡献
    optional uint32 valid_contribution = 5;	// 当前可用贡献
    required uint32 member_lv = 6;			// 个人等级
    optional uint32 max_member_lv = 7;		// 最大个人等级
    optional uint32 cur_lv_contribution = 8;	// 当前等级的贡献进度
    optional uint32 max_lv_contribution = 9;	// 当前等级的贡献最大值
}

message GroupOrder {
    repeated uint32 group_id = 1;
    required uint32 game_id = 2;
}

// 工会详细信息
// 1.8.1 起公会信息同步不返回这个结构体，其他地方还在用
message GuildDetailInfo {
    required uint32 guild_id = 1;             //内部使用的id，不可改
    required uint32 guild_display_id = 2;     //给用户看到的公会id
    required string name = 3;
    required string desc = 4;
    required GuildMember chairman = 5;     //会长
    required uint32 member_count = 6;      //人数
    required string icon_id = 7;           //头像id
    required uint32 create_date = 8;       //创建时间
    repeated Game game_list = 9;         //公会游戏列表
    //    repeated GuildMember index_mem_list = 10;	// **暂时不用到** 工会首页前几个会员
    repeated GuildPhoto index_photo_list = 11;	// 工会首页前几张缩略图
    required uint32 gift_count = 12;            //礼物总数
    required uint32 voice_update_time = 13;     //声音最后更新时间
    required uint32 voice_length = 14;          //声音长度（秒）
    required uint32 guild_group_id = 15;        //工会总群
    required uint32 game_limit = 16;             //能关联的最大游戏数
    // required GuildGroup guild_group = 17;
    repeated GuildCheckin checkin_top_n_list = 18;		// 工会签到列表前几个
    required uint32 checkin_num = 19;			// 签到人数
    optional uint32 my_checkin_days = 20;		// 我的签到天数
    required uint32 my_role = 21;				// 我在工会的角色（常量定义在GuildMem)
    required string guild_prefix = 22;			// 工会马甲前缀
    optional GuildNotice guild_notice = 23;		// 工会公告
    required string guild_manifesto = 24;		// 公会宣言
    required bool need_verify = 25;				// 加入公会是否需要验证
    repeated GuildGameStatInfo game_stat_list = 26;	// 公会游戏统计信息
    repeated GroupOrder order_group_id = 27;		// 群排序
    optional uint32 my_supplement_days = 28; // 可补签的天数
    optional uint32 my_supplement_price = 29; // 补签费用
    optional uint32 my_supplemented_days = 30; // 补签后连续签到天数
    optional uint32 gold_storage= 31; // 工会金库
    repeated GuildDonate donate_top_n_list = 32;		// 工会捐献列表前几个 （3.3.0废弃）
    optional uint32 donate_num = 33;			// 捐献人数     （3.3.0废弃）
    optional uint32 my_donate_days = 34;		// 我的捐献天数 （3.3.0废弃）
    optional uint32 album_num = 35;				// 相册数量
    optional uint32 star_level = 36;			// 工会星级
    optional uint32 checkin_yesterday = 37;				// 昨日签到数
    optional uint32 donate_yesterday = 38;				// 昨日捐献数 （3.3.0废弃）
    optional uint32 home_channel_id = 39;      //公会总房间
}



// 从1.8.1 新增
// ***** GuildDetailInfo分拆成以下4个 ******//
message GuildGeneralInfoSync {
    required uint32 guild_id = 1;             //内部使用的id，不可改
    required uint32 guild_display_id = 2;     //给用户看到的公会id
    required string name = 3;
    required string desc = 4;
    required uint32 create_date = 5;       //创建时间
    required uint32 guild_group_id = 6;        //工会总群
    required uint32 game_limit = 7;             //能关联的最大游戏数
    required uint32 my_role = 8;               // 我在工会的角色（常量定义在GuildMem)
    required bool need_verify = 9;             // 加入公会是否需要验证
    required string guild_prefix = 10;
    optional string guild_manifesto = 11;       // 公会宣言
}

// 从1.8.1 新增
message GuildExtraInfoSync {
    optional string icon_id = 1;           //头像id
    optional GuildNotice guild_notice = 2;     // 工会公告
    repeated GroupOrder order_group_id = 3;        // 群排序
}

// 从1.8.1 新增
message GuildGameInfoSync {
    repeated Game game_list = 1;         //公会游戏列表
}

// 从1.8.1 新增
message GuildRedPointInfoSync {
    repeated uint32 red_point_list = 1;     // 红点（有更新的地方）
}

// 从1.8.1 新增
message GuildNumbersInfoSync {
    required uint32 member_count = 1;      //人数
    required uint32 gift_count = 2;            //礼物总数
}
// ***** GuildDetailInfo 分拆 END ******//

//
message GuildPhoto {
    required string thumb_url = 1;   	// 缩略图url
    required uint32 photo_id = 2;   	// 图片id
    required string photo_url = 3;		// 照片url
    required uint32 album_id = 4;
    required uint32 guild_id = 5;
    required string creator_account = 6;
    required uint32 create_time = 7;
}

message SimpleGame {
    required uint32 game_id = 1;
    required string game_name = 2;
    required string game_icon_url = 3;
}

message Game {
    enum GAME_SOURCE_TYPE {
        OFFICAL_LINK = 1;		// 官方平台链接
        GUILD_OFFICAL_AUTH = 2;	// 公会自定义链接，手游吧官方认证
        GUILD_UNKNOWN = 3;		// 公会自定义外链
    }
    enum GAME_STATUS {
        NORMAL = 0;        // 正式上线
        COMING = 1;         // 即将测试
        COMMING_DELETE = 2; // 删档内测
        DOWN   = 3;          // 下架
    }
    required uint32 game_id = 1;
    required string game_pkg = 2;
    required string game_name = 3;
    required string game_icon = 4;
    required string game_url = 5;     //下载链接，可修改
    required string game_desc = 6;    //描述，可修改
    required bool use_custom_url = 7;        //使用所设置的url
    required string game_ver = 8;       //版本
    required string game_source = 9;    //来源
    required string game_size = 10;        //大小
    required uint32 game_source_type = 11;	// 游戏来源标志
    optional uint32 enter_guild_count = 12;		// 入驻公会数量
    optional bool is_deleted = 13;				// 是否已经被删除
    optional uint32 download_times = 14;   //公会内的下载次数
    optional uint32 game_status = 15;
    optional string game_summary = 16;  // 一句话简介
    optional string area_url = 17;    //专区链接
}

//用户的游戏（包括用户关注的游戏和他本机安装的游戏）
message UserGame {
    required uint32 id = 1; 				// 游戏id
    required uint32 type = 2; // 游戏类型(动作、解谜、角色扮演等）
    required uint32 subscribe_type = 3;//关注类型: 0未关注  1已关注
    required string name = 4; //游戏名称
    required bool in_common = 5;//共有
    required string icon = 6; // 图标
}

//用户本地游戏
message LocalGame {
    required uint32 id = 1;        // Deprecated!
    required string package = 2;
    required string name = 3;
    required string version = 4;
    required bool uninstalled = 5; //暂时只上报收到明确收到删除广播的游戏.以避免多段登录下的误报
}

// 申请加入工会的申请列表
message GuildApplyStatus {
    enum STATUS_TYPE {
        APPLY = 0;
        CHAIRMAN_ACCEPT = 1;
    }
    required uint32 apply_id = 1; //申请记录id
    required uint32 guild_id = 2;
    required uint32 guild_display_id = 3;
    required string name = 4;
    required uint32 status = 5;
}

// 临时群成员信息
message GroupMemSimpleInfo {
    enum MemberRole {
        OWNER = 1;		// 群主
        COMMON = 3;		// 普通成员
    }
    required string user_account = 1;
    required uint32 sex = 2;
    required string user_nick = 3;
    required string nick_pinyin = 4;
    required uint32 role = 5;
    required string face_md5 = 6;		// 头像md5
}

// 公会公告
message GuildNotice {
    required string content = 1;		// 公告内容
    required uint32 create_time = 2;	// 公告创建时间
    required uint32 id = 3;				// 公告ID
    required string title = 4;			// 公告标题
    required uint32 last_update_time = 5;	// 最后修改时间
    required uint32 creator_uid = 6;	// 创建者uid
    required uint32 notice_type = 7; //公告类型
    optional string creator_nick_name = 8; //创建者昵称
}

// 礼包结构
message GiftPackage {
    required uint32 gift_pkg_id = 1;				// 礼包id
    required uint32 game_id = 2;					// 对应游戏id
    required uint32 exchange_begin_time = 3;		// 礼包兑换开始时间
    required uint32 exchange_end_time = 4;			// 礼包过期时间
    required string name = 5;						// 礼包名称
    required string intro = 6;						// 礼包介绍
    required uint32 is_exceed = 7;					// 是否已过期
    required string icon_url = 8;					// 礼包图片url
}

// 1.8.1 去掉啦啦啦
message GuildGroupMember {
    enum MemberType {
        NONE = 0;
        OWNER = 1;
        ADMIN = 2;
        MEMBER = 3;
    }
    required uint32 uid = 1;
    required uint32 role = 2;
    required string group_card = 3;
    required bool is_mute = 4;		// true:禁言  false:未禁言
    required uint32 join_at = 5;	// 入群时间
}

// 1.8.1新增
// 公会群成员信息
message GuildGroupMemberV2 {
    enum MemberType {
        NONE = 0;
        OWNER = 1;
        ADMIN = 2;
        MEMBER = 3;
    }
    required GuildMember member_info = 1;
    required uint32 role = 2;
    required bool is_mute = 3;      // true:禁言  false:未禁言
    required uint32 join_at = 4;    // 入群时间

    optional uint32 online_status      = 5;    // 在线状态 0 离线 1在线
    optional uint32 online_status_ts   = 6;    // 在线状态变化的时间 只有离线用户才会有值 表示最后离线的时间
}


// 公会游戏统计信息
message GuildGameStatInfo {
    required uint32 game_id = 1;
    required uint32 user_count = 2;
    required uint32 giftpkg_count = 3;
}

//游戏圈用户
message GameCircleUser {
    required uint32 uid = 1;
    required string account = 2;
    required string name = 3;
    required uint32 sex = 4;                  //性别
    required uint32 guild_id = 5;             //公会id
    required string guild_name = 6;           //公会名
    required uint32 guild_role = 7;           //公会角色
    optional GrowInfo grow_info = 8;
}

// 任务奖励
message MissionBonus {
    optional uint32 exp = 1;            // 经验值(0或未填表示无该奖励)
    optional uint32 red_diamonds = 2;   // 红钻(0或未填表示无该奖励)
}

// 任务
message Mission {
    required string key = 1;            // 任务KEY, 全局惟一(不同日期、同一个日常任务的key不同)
    required string name = 2;           // 任务名
    required string description = 3;    // 任务描述
    required MissionBonus bonus = 4;    // 奖励
    required int64 start_time = 5;      // 任务开始时间, UNIX TIMESTAMP
    required uint32 mission_id = 6;     // 任务id
}

message MissionConf {
    required uint32 mission_conf_id = 1;
    required string name = 2;
    required string description = 3;
    required int64 start_time = 4;
    required int64 expire_time = 5;
    required uint32 min_client_version = 6;
    optional uint32 reset_time = 7;     //任务重置时间。目前从凌晨开始的秒数
}

message RichAndCharmTopRank {
    optional uint32 rich_day_top_rank  = 1;	   // 土豪日榜排名 0表示无效
    optional uint32 charm_day_top_rank = 2;	   // 魅力日榜排名 0表示无效
    optional uint32 rich_week_top_rank  = 3;   // 土豪周榜排名 0表示无效
    optional uint32 charm_week_top_rank = 4;   // 魅力周榜排名 0表示无效
}


// 房间VIP等级ID (注意ID大小不一定代表等级高低)
enum EChannelMemberVipLevelID
{
    ENUM_CHANNEL_MEMBER_VIP_invalid = 0; // 0是无效值
    ENUM_CHANNEL_MEMBER_VIP_normal = 1;  // 平民 不是VIP
    ENUM_CHANNEL_MEMBER_VIP_silver = 2;  // VIP尊荣白银卡
    ENUM_CHANNEL_MEMBER_VIP_gold = 3;    // VIP奢华黄金卡
    ENUM_CHANNEL_MEMBER_VIP_pt = 4;      // VIP高贵铂金卡
    ENUM_CHANNEL_MEMBER_VIP_diamond = 5; // VIP巅峰钻石卡
    ENUM_CHANNEL_MEMBER_VIP_black = 6;   // VIP至尊黑卡
    ENUM_CHANNEL_MEMBER_VIP_polaris = 7; // VIP北极星卡
    ENUM_CHANNEL_MEMBER_VIP_phenix = 8;  // VIP凤凰卡
    ENUM_CHANNEL_MEMBER_VIP_dragon = 9;  // VIP龙神卡
}

// 房间成员等级的可选数据字段
message ChannelMemberVipLevel
{
    optional uint32 curr_vip_level_id = 1;	  // 当前VIP等级ID EChannelMemberVipLevelID
    optional uint32 curr_vip_value = 2;	      // 当前VIP等级下的数值
    optional string curr_vip_name = 3;	      // 当前VIP等级名字

    optional uint32 next_vip_level_id = 4;	  // 下一个VIP等级ID EChannelMemberVipLevelID
    optional uint32 next_vip_value = 5;	      // 下一个VIP等级需要的数值
    optional string next_vip_name = 6;	      // 下一个VIP等级名字
}

message MyGrowInfo {
    required uint32 exp = 1;                        // 总经验
    required uint32 lv = 2;                         // 等级
    required uint32 current_lv_exp_min = 3;         // 本等级最小经验值
    required uint32 current_lv_exp_max = 4;         // 本等级最大经验值
    required uint32 red_diamonds = 5;               // 红钻数
    repeated uint32 medal_list = 6;                 // 勋章列表
    repeated uint32 taillight_medal_list = 7;       // 用于显示尾灯的勋章列表

    optional uint32 score = 8;						// 积分
    optional uint32 rich_level = 9;					// 土豪等级
    optional uint32 charm_level = 10;				// 魅力等级
}

message GrowInfo{
    required uint32 level = 1;                      // 等级
    repeated uint32 medal_list = 2;                 // 勋章列表
    required uint32 uid = 3;
    repeated uint32 taillight_medal_list = 4;       // 用于显示尾灯的勋章列表

    optional uint32 rich_level = 5;					// 土豪等级 只有主动获取用户信息时才会填哦 比如 CMD_GetUserDetail
    optional uint32 charm_level = 6;				// 魅力等级 只有主动获取用户信息时才会填哦 比如 CMD_GetUserDetail
    optional RichAndCharmTopRank rich_charm_rank = 7;  // 土豪/魅力榜单排名信息 只有主动获取用户信息时才会填哦 比如 CMD_GetUserDetail
}

message GuildOfficialMember{
    required uint32 official_id = 1;
    required uint32 permission = 2;
    required string official_name = 3;
    required uint32 member_total = 4;
    repeated GuildMember member_list = 5;
}

// 勋章配置
message MedalConfig{
    required uint32 medal_id = 1;
    required string name = 2;
    repeated string description = 3;
    required string icon = 4;
    required string border_icon = 5;
    required string title = 6;
    required string content = 7;
    optional string title_icon = 8;  //标题图标 目前在游戏圈标题使用,并非所有勋章都需要
    optional bool is_allow_set_taillight = 9; // 是否允许设置尾灯
    optional string desc_url = 10;
}

//TT游戏标签
message TTagInfo
{
    optional uint32 tag_id = 1;
    optional string tag_name= 2;
    optional uint32 tag_type = 3;
}


//============ 游戏圈
//开服信息   -- v1.5
message NewServerInfo {
    required uint32 start_time = 1;      //开服时间
    required string name = 2;            //服务器名字
    required string desc = 3;            //服务器描述
}
//新游  -- v1.5
message NewGame {
    required Game game = 1;
    repeated string game_tag = 2;                   //游戏标签
    repeated NewServerInfo server_info_list = 3;    //新服列表
    optional string test_time	= 4;				//开测时间
    repeated TTagInfo tt_tag_list	= 5;			//TT tags
}
//=========== 公众号
//公众号 -- v1.5
message OfficialAccountInfo {
    enum AccountType {
        SYSTEM = 50;
        CIRCLE = 101;
    }

    required uint32 id                  = 1;
    required string account             = 2;
    required uint32 account_type		= 3;
    required string name           		= 4;
    required string pinyin         		= 5;
    required string face_md5			= 6;    // 以圈子头像作为后备
    required uint32 active              = 7;    // 激活状态。 1 接收消息， 2 拒收消息
    optional string authority           = 8;    // 帐号主体
    optional string authority_url       = 9;    // 帐号主体url
    required OfficialAccountConfig config = 10; // 公众号配置
    optional bool is_authenticated      = 11;   // 已通过认证（带v)
    required string intro               = 12;   //公众号简介
}

message OfficialAccountSimpleInfo {
    required uint32 id					= 1;		// 公众号id
    required string account				= 2;		// 公众号帐号
    required string name				= 3;		// 公众号名称
}

message OfficialAccountConfig {
    required bool show_profile		    = 1;	//是否显示资料
    required bool show_keyboard			= 2;	//是否显示键盘
    repeated MenuItem menu_item_list    = 3;	//公众号菜单
    optional string notification_document = 4;  //开关消息通知的文案(用key还是直接用本地化字串呢?)
    optional string unfollow_document   = 5;    //取消关注的文案(用key还是直接用本地化字串呢?)
    optional bool fix_in_im             = 6;    //是否强制在IM页中显示
    optional uint32 auto_top			= 7;	//是否自动置顶(>0时，值越大，置顶优先级越高)
    optional bool ios_check_lmt			= 8;	// ios 审核时隐藏此公众号
}

message MenuItem{
    required string name                = 1;    // 菜单名
    optional ControlBlock ctrl_block    = 2;    // 控制命令
    repeated MenuItem children          = 3;    // 子菜单
    optional uint32 required_android_version = 4;    // 需要Android TT的最低版本
    optional uint32 required_iphone_version  = 5;    // 需要iPhone TT的最低版本
    optional bool ios_check_lmt			= 6;	// ios审核时，隐藏此按钮
}

//控制块 -- v1.5
message ControlBlock{
    required uint32 cmd = 1;        //控制命令
    optional bytes cmd_body = 2;    //控制内容
}

//游戏圈帖子请求类型 -- v1.5
enum GameCircleTopicListReqType{
    HOT_TOPICS = 1;
    NEW_TOPICS = 2;
    CONTACT_TOPICS = 3;
    LAST_UPDATE_TOPICS = 4;
    HIGHLIGHT_TOPICS = 5;
}

//推荐圈子列表请求类型  -- v1.5
enum GameCircleTopListReqType{
    NORMAL = 1;
    NEW_GAMES = 2;
}

//统计来源页面，目前拉圈子列表有用  -- v1.5
enum STAT_FROM_PAGE_TYPE{
    FROM_NEW_GAME = 1; //来自新游
    FROM_MY_CIRCLES = 2; //来自我的圈子
    FROM_RECOMMEND = 3; //来自推荐关注
    FROM_OFF_ACCT = 4; //来自公众号
    FROM_WEB_VIEW = 5; //来自内嵌网页
    FROM_SDK_VIEW = 6; //来自SDK
    FROM_TT_SHARE = 7; // 来自TT内部分享
}


//公众号消息结构 (从IM消息扩展) -- v1.5
message OfficialMessageBody{
    enum OfficialMessageType{
        TEXT = 1;
        PICTURE_AND_TEXT = 2;
        GAME_DOWNLOAD = 3;      // v2.4新增
        TEXT_JUMP = 4;
    }
    required uint32 message_type        = 1;
    required string title               = 2;
    required uint32 date                = 3;
    required string content             = 4;        //内容
    optional string intro               = 5;        //简介
    optional string img                 = 6;
    optional ControlBlock ctrl_block    = 7;
    optional uint32 allow_retransmit    = 8;        //允许转发
}

message OfficialMessageSingle{
    required uint32 message_id = 1;
    required OfficialMessageBody message = 2;
}

message OfficialMessageBunch{
    required uint32 message_id = 1;
    repeated OfficialMessageBody children_messages    = 2;
}

message TopicUpdateInfo{
    required uint32 circle_id = 1 ;  //游戏圈id
    required uint32 newest_topic_id = 2 ; //游戏圈最新主题id

}

//////////////////////////////////////////////////
// 鉴于圈子数量日益增长, 加上之前定的一些结构体大而全
// 很多无用信息需要返回, 这里开始将圈子的协议进行一些重构, 现在iOS版本上测试

message CircleGame{
    required uint32 game_id 					= 1;
    required string game_name 					= 2;
    required string game_package				= 3;
    repeated string game_tag_list                               = 4;	//游戏标签
    required uint32 game_status 				= 5;	//使用Game类中的GAME_STATUS枚举
    required string summary 					= 6;	//一句话描述
    repeated NewServerInfo server_info_list		        = 7;	//新服列表, deprecated
    optional string test_time					= 8;	//开测时间
    repeated TTagInfo tt_tag_list				= 9;	//TT tags
}

message Circle {
    enum CIRCLE_TYPE {
        TYPE_GAME_CIRCLE = 1;       //游戏圈
        TYPE_OFFICIAL_CIRCLE = 2;   //官方圈
    }
    required uint32                     id                  = 1;
    required string                     name                = 2;
    required uint32                     type                = 3;
    required string                     icon_url            = 4;
    optional CircleGame                 game_info           = 5;
    optional string                     background_url      = 6;
    optional OfficialAccountSimpleInfo  official_account    = 7;
}

// 注意这里不要轻易添加其他业务的数据, 可能造成无用的开销
message CircleUser {
    required uint32 uid         = 1;
    required string account     = 2;
    required string name        = 3;
    required uint32 sex         = 4;
    optional GrowInfo grow_info = 5;
}

message CircleTopicGameDownloadInfo {
    required uint32 game_id = 1;
    required string name = 2;
    required string icon = 3;
    required string description = 4;
    required uint32 download_times = 5;
}

//// ------ 实时语音基本类型 BEGIN ------ ////

message Session {
    required uint32 session_id		= 1;		// sessionId
    required uint32 create_time		= 2;		// 创建时间
    repeated string member_account_list	= 3;	// 成员列表
}

// 群内的实时语音
message GroupSession {
    required uint32 group_id		= 1;
    repeated Session session		= 2;		// REPEATED, 可扩展成多个
}

enum SESSION_TYPE {
    SESSION_TYPE_GROUP = 0;
    SESSION_TYPE_1V1 = 1;
}

// 我当前所在的Session
message MySession {
    optional uint32 group_id 			= 1;	// 如果是群实时语音，则group_id > 0
    repeated string ref_user_account 	= 2;	// 如果是1V1聊天，则会带上2个人的account
    required Session session			= 3;	//
    required uint32 session_type 		= 4;	// 房间类型
}

message GenericMember {
    required uint32 uid       = 1;
    required string account   = 2;
    required string nick_name = 3;
}

message AttachmentCache{
    required string property = 1;
    required string data = 2;
}

//// ------ 实时语音基本类型  END  ------ ////

//验证码信息
message CAPTCHAInfo {
    required bytes verify_code = 1; //验证码
    required uint64 pic_key = 2;	//验证码key
    required uint32 pic_timestamp = 3;	//验证码timestamp
}

//游戏页榜单类型
enum GAME_LIST_TYPE{
    NEW_GAME_LIST = 0;  //新游榜
    HOT_GAME_LIST = 1;  //热游榜
    GUILD_GAME_LIST = 2;//公会人气榜
    BUILT_IN_TEST = 3; //内测
}

//// ------ 礼物 BEGIN ------ ////

// 购买礼物的货币
enum PRESENT_PRICE_TYPE {
    PRESENT_PRICE_UNKNOWN = 0;
    PRESENT_PRICE_RED_DIAMOND = 1;
    PRESENT_PRICE_TBEAN = 2;
}

// 礼物类型
enum PresentTagType {
    PRESENT_TAG_NORMAL = 0;			// 普通礼物
    PRESENT_TAG_CHANNEL_DATING = 1;	// 相亲礼物
    PRESENT_TAG_NOBILITY = 2;       // 贵族礼物
    PRESENT_TAG_ADD_GROUP = 3;      // 语音直播房加团礼物
    PERSENT_TAG_DRAW = 4;           // 涂鸦礼物
}

// 礼物播放特效
enum PRESENT_SHOW_EFFECT_TYPE
{
    PRESENT_SHOW_EFFECT_DEFAULT = 0;
    PRESENT_SHOW_EFFECT_FULL_SCREEN = 1;	// 全屏(送出火箭)
    PRESENT_SHOW_EFFECT_FLOW_1 = 2;			// 流光1(礼物总值10000-99999 红钻/T豆)
    PRESENT_SHOW_EFFECT_FLOW_2 = 3;			// 流光2(礼物总值不少于 100000 红钻/T豆)
}

// 礼物播放特效
enum PRESENT_SHOW_EFFECT_TYPE_V2
{
    PRESENT_SHOW_EFFECT_V2_DEFAULT = 0;
    PRESENT_SHOW_EFFECT_V2_FULL_SCREEN = 1;	// 全屏(送出火箭)
    PRESENT_SHOW_EFFECT_V2_FLOW = 2;		// 流光
}

message PresentItemBriefConfig
{
    required uint32 item_id = 1;
    required string name = 2;			// 名称
    required string icon_url = 3;		// 图标url
    required uint32 score = 4;			// 积分
    required uint32 charm = 5;			// 魅力值
    required string msg_icon_url = 6;	// 消息中显示的小图标url
}

message PresentItemConfig
{
    required uint32 item_id = 1;
    required string name = 2;			// 名称
    required string icon_url = 3;		// 图标url
    required uint32 price = 4;			// 价格
    required uint32 score = 5;			// 积分
    required uint32 charm = 6;			// 魅力值
    required uint32 rank = 7;			// 排名
    required uint32 effect_begin = 8;	        // 上架时间
    required uint32 effect_end = 9;		// 下架时间
    required string msg_icon_url = 10;	        // 消息中显示的小图标url
    optional uint32 price_type = 11;	        // PresentPriceType
    optional uint32 rich_value = 12;	        // 土豪值
    optional bool is_del = 13;			// 是否已删除
    optional bytes video_effect_url = 15;	// 特效url
    optional bool show_batch_option = 16;        // 是否展示批量送礼选项
    optional uint32 item_tag = 17;		// PresentTagType
    optional uint32 nobility_level = 18; //使用这个礼物的最低贵族等级
}

// 单种礼物数量
message PresentCount{
    required uint32 item_id = 1;
    required uint32 count = 2;
}

message PresentSummary {
    repeated PresentCount present_count_list = 1;
    optional uint32 total_value = 2; // 礼物总价值
    optional uint32 total_count = 3; // 礼物总数量

    optional int64 total_value64 = 4; // 64位的礼物总价值
    optional int64 total_count64 = 5; // 64位的礼物总数量
}

//// ------ 礼物流光 BEGIN ------ ////
message PresentFlowConfig {
    required uint32 flow_id = 1;
    required string url = 2;
    required string md5 = 3;
}

//用户头像挂饰
message UserHeadwearInfo{
    required uint32 headwear_id = 1;
    required uint32 suite_id = 2;
    required uint32 expire_time = 3;    //expire timestamp
    required string name = 4;
    required string img = 5;           //img key
    required uint32 level = 6;
    optional string static_img = 7;     //列表展示用
}

// 客户端去下载的资源文件
message DownloadSourceInfo
{
    enum DOWNLOAD_SOURCE_TYPE
    {
        DOWNLOAD_SOURCE_NONE = 0;
        DOWNLOAD_SOURCE_DATING_GAME_HAT = 1;	//相亲房的帽子
        DOWNLOAD_SOURCE_EFFECTS = 2;	//牵手动画
        DOWNLOAD_SOURCE_PRESENT_RUNWAY_ROCKET = 3;	// 房间礼物火箭跑道的火箭冲天动画
    }

    required uint32 source_type = 1;	// DOWNLOAD_SOURCE_TYPE
    optional uint32 source_id = 2;
    required string url = 3;
    required string	md5 = 4;
}

// 用户进房特效类型
enum UserDecorationType
{
    INVALID = 0;
    DECORATION_TYPE_ENTER_SPECIAL_EFFECT = 1;	// 房间坐骑
    DECORATION_TYPE_ENTER_FUN_MESSAGE = 2;
}

// 进房坐骑特效
message EnterSpecialEffectDetail
{
    required string name = 1;	// 特效名称
    required string desc = 2;	// 特效描述
    required string preview_url = 3; // 特效预览图地址(为空时，客户端会自己生成)
}

message DecorationConfig
{
    required string id = 1;
    required uint32 type = 2;	// UserDecorationType
    required string ver = 3;
    required bytes detail = 4;	// 需要根据type来判断是哪个pb结构
    optional uint32 rich_level = 5;
}

message UserDecorationInfo
{
    required uint32 uid = 1;
    required DecorationConfig decoration_cfg = 2;
    required uint32 effect_end = 3;
    required bool actived = 4;
}
