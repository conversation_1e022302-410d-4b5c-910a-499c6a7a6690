syntax = "proto2";

package ga;
import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/auth";

enum THIRD_PARTY_TYPE {
    QQ = 1;     // QQ登录
    WECHAT = 2; // 微信登录
}

enum PACKAGE_TYPE {
    GUILD_PKG = 1;      // 公会包
    GUILD_USER_PKG = 2; // 公会+用户
}

message AuthCommInfo {
    required string user_source = 1;
    required string direct_to = 2;
    optional string idfa = 3; // idfa only for IOS
    optional string mac = 4;  // eth0 MAC地址原值

    // 统计数据
    optional string brand = 5;     // 手机品牌，如Apple
    optional uint32 width = 6;     // 分辨率宽度
    optional uint32 height = 7;    // 分辨率高度
    optional string network = 8;   // 联网方式：WIFI、3G、4G、5G
    optional double longitude = 9; // 经度
    optional double latitude = 10; // 纬度
    optional string province = 11; // 省份
    optional string city = 12;     // 城市
}

message AuthInfo {

    enum CUSTOM_PERMISSION {
        GAME_CIRCLE_MUTE = 1;               //游戏圈禁言，取值1,2,4,8...
        GAME_CIRCLE_UNMUTE = 2;             //游戏圈取消禁言
        GAME_CIRCLE_HIGHTLIGHT = 4;         // 加精
        GAME_CIRCLE_CANCLE_HIGHTLIGHT = 8;  // 取消加精
        GAME_CIRCLE_DEL_TOPIC_COMMENT = 16; // 删除主题评论
    }

    enum USER_TYPE {
        COMMON = 0;  // 普通
        OFFICAL = 1; // 官方帐号
        WATCHER = 2; // 观察者
        OFFICAL_KEFU = 3; // 客服人员号 一般用于客服使用特殊版本的TT
        ROBOT = 4;        // 机器人号
    }

    enum REG_SOURCE_TYPE {
        REG_SOURCE_PHONE = 0;  // TT客户端手机注册
        REG_SOURCE_SDK = 1;    //游戏SDK
        REG_SOURCE_WECHAT = 2; // TT第三方微信
        REG_SOURCE_QQ = 3;     // TT第三方QQ
        REG_SOURCE_APPLEID = 4;     // TT第三方APPLE
        REG_SOURCE_QQ_MINI = 5;     // TT第三方QQ小程序
        REG_SOURCE_WECHAT_MINI = 6;     // TT第三方微信小程序
        REG_SOURCE_TT_WEB_SDK = 7;     // TT WEB SDK
    }

    required uint32 uid = 2;         // uid
    required string nick_name = 3;   // 昵称
    required string user_phone = 4;  // 手机
    required uint32 user_sex = 5;    //用户性别
    required bool has_face = 6;      //用户是否有自定义头像
    required string session_key = 7; // 通信用的加密key

    required uint32 is_kicked = 9; //上次登出是否被踢出 0：正常登出  1：被踢出

    required uint32 lastest_version = 10; // 当前最新的版本
    required string ticket = 11;          // ticket
    required string user_account = 12;    //唯一账号
    required bytes small_face = 13;
    required string signature = 14;
    required uint32 current_guild_id = 15;
    required bool verify = 16;
    // 标志用户这次登录的唯一key，每次登录都不一样
    required string login_key = 17;
    required uint32 current_guild_role = 18;
    required string alias = 19;
    required uint32 permission = 20; // 工会自定义权限

    required string album_token = 21; // 上传相片token
    required uint32 im_sync_key = 22; // IM消息最新的synckey

    optional uint32 show_game_circle = 23; // 该用户是否显示游戏圈  0:关，1:开

    // 是否需要弹"root"的警告 0:不用,
    // 因为Android客户端pbnano无法区分0和optional字段没填 1 << 0: None 1 << 1:
    // KK, 1 << 2: ???
    optional uint32 root_alert_mask = 24;
    optional uint32 custom_permission = 25; // 自定义权限
    optional string invite_code = 26;       // 邀请码

    // 第三方账号类型 see THIRD_PARTY_TYPE
    optional uint32 third_party_type = 27;
    // 用于生成web token, deprecated, use web_secore_token instead.
    optional string web_token_seed = 28;
    optional uint64 server_time_ms = 29; // server time, in micro-second
    optional bool password_set = 30;     // 该账号是否已经设置过密码
    optional uint32 user_type = 31;      // 用户类型 see USER_TYPE
    optional bool prefix_valid = 32;     //是否使用公会马甲
    optional string original_nickname = 33;  //无马甲昵称
    optional uint32 permission_v2 = 34;      // 新公会权限
    optional uint64 reg_time = 35;           //注册时间
    optional uint32 reg_source = 36;         //用户来源, see REG_SOURCE_TYPE
    optional uint32 last_quit_guild_id = 37; // 上一个退出的公会的id
    // TT内web使用的token, 只能在https的页面中使用
    optional string web_secure_token = 38;
    optional string client_ip = 39;  //
    optional bool simple_pwd = 40;   //密码是否过于简单 true 简单
    optional bool popup_window = 41; // 是否需要弹出 实名认证 窗口
    optional string face_md5 = 42;   // 用户头像的MD5
}

message AuthReq {
    enum LOGIN_TYPE {
        MANUAL = 1;     // 手工登录
        RE_LOGIN = 2;   // 重登录
        AUTO_LOGIN = 3; // 自动登录
    }
    enum LOGIN_ACCOUNT_TYPE {
        INVALID = 0;
        PHONE = 1;      // 手机号
        ACCOUNT = 2;    // TT 账号
        THIRDPARTY = 3; // 第三方账号
        TT_FUZZY = 4; // 模糊 TT帐号 或者 手机号 （不支持第三方帐号）
    }
    required BaseReq base_req = 1;
    // 用户帐号（如果account存在，则此属性设置为空）
    required string user_phone = 2;
    required string pwd = 3; // 密码
    // 登录类型： 1手工登录， 2重登陆， 3自动登录
    required uint32 login_type = 4;
    //账号类型： 0/1手机号， 2：TT账号 SEE LOGIN_ACCOUNT_TYPE
    optional uint32 login_account_type = 5;

    optional string imei = 10;         // imei
    optional string os_ver = 11;       // 操作系统版本
    optional string os_type = 12;      // 操作系统类型
    optional string device_model = 13; // 机器型号

    optional string signature = 14;       //签名
    optional string device_info = 15;     //设备信息
    optional uint32 is_emulator = 16;     //是否模拟器
    optional string device_id = 17;       //设备号
    optional uint32 is_upgrade = 18;      //是否升级后第一次登录
    optional string current_channel = 19; //当前包渠道

    // 第三方账号类型 第三方账号类型 see THIRD_PARTY_TYPE
    optional uint32 third_party_type = 20;
    optional string origin_channel = 21;        // 1.8.1 源包渠道
    optional AuthCommInfo auth_comm_info = 22;  // 登录共有信息
    optional bool is_quick_login_from_sdk = 23; // 从SDK快速登录
    optional string idfa = 24;                  // idfa only for IOS
    // 如果先验证了验证码再进行登录, 则提交该字段(pwd字段需要填入一样的值)
    optional string verify_code_credentials = 25;
}

message AuthResp {
    required BaseResp base_resp = 1;
    required AuthInfo auth_info = 2;
    required uint32 login_type = 3;
    optional uint32 login_account_type = 4; //账号类型： 0/1手机号， 2：TT账号 SEE LOGIN_ACCOUNT_TYPE
    optional string password = 5;           // 账号存在时，返回用户密码
}

// 通过第三方账号登录系统
message ThirdPartyAuthReq {

    required BaseReq base_req = 1;
    required uint32 third_party_type = 2; // 第三方账号类型 see THIRD_PARTY_TYPE
    required string open_id = 3; // 客户端第三方认证获得的open_id
    required string access_token = 4; // 客户端第三方认证获得的access_token

    optional string imei = 10;         // imei
    optional string os_ver = 11;       // 操作系统版本 //不填
    optional string os_type = 12;      // 操作系统类型 //不填
    optional string device_model = 13; // 机器型号 //不填

    optional string signature = 14;            //签名
    optional string device_info = 15;          //设备信息
    optional uint32 is_emulator = 16;          //是否模拟器
    optional string device_id = 17;            //设备号
    optional string current_channel = 18;      //当前包渠道
    optional string origin_channel = 19;       // 1.8.1 源包渠道
    optional AuthCommInfo auth_comm_info = 20; // 登录共有信息
    optional string idfa = 21;                 // idfa only for IOS
    optional string thirdparty_app_id = 22;
}

message ThirdPartyAuthResp {
    required BaseResp base_resp = 1;
    required bool has_account = 2;   // 账号是否已存在
    optional AuthInfo auth_info = 3; // 账号存在时，返回用户信息
    optional string password = 4;    // 账号存在时，返回用户密码
}

// 中国移动 一件登陆
message ChinaMobileAuthReq {

    required BaseReq base_req = 1;
    required string china_mobile_token = 2; // 从中国移动SDK 获取的token值

    optional string imei = 3;         // imei
    optional string os_ver = 4;       // 操作系统版本 //不填
    optional string os_type = 5;      // 操作系统类型 //不填
    optional string device_model = 6; // 机器型号 //不填

    optional string signature = 7;             //签名
    optional string device_info = 8;           //设备信息
    optional uint32 is_emulator = 9;           //是否模拟器
    optional string device_id = 10;            //设备号
    optional string current_channel = 11;      //当前包渠道
    optional string origin_channel = 12;       // 1.8.1 源包渠道
    optional AuthCommInfo auth_comm_info = 13; // 登录共有信息
    optional string idfa = 14;                 // idfa only for IOS

    // only for IOS 特殊的付费版TT填写（填写中国移动分配的APPID）
    optional string special_ext_appid = 15;
}

message ChinaMobileAuthResp {
    required BaseResp base_resp = 1;
    required string phone_num = 2;   // 手机号
    required bool has_account = 3;   // 手机号 对应的账号是否已存在
    optional AuthInfo auth_info = 4; // 账号存在时，返回用户信息
    optional string password = 5;    // 账号存在时，返回用户密码
}

// 中国联通 一键登陆
message ChinaUnicomAuthReq {

    required BaseReq base_req = 1;
    required string appid = 2; // 当前APP在创蓝对应的appid 
    required string china_unicom_token = 3; // 从创蓝SDK 获取的token值
    required string telecom = 4; // 运营商
    required string timestamp = 5; // 从创蓝SDK获取的UNIX时间戳，毫秒级，有效期3min
    required string randoms = 6; // 从创蓝SDK获取的随机数
    required string sign = 7; // 从创蓝SDK获取的签名
    required string version = 8; // 从创蓝SDK获取的SDK版本号
    required string device = 9; // 从创蓝SDK获取的设备型号

    optional string imei = 10;         // imei
    optional string os_ver = 11;       // 操作系统版本 //不填
    optional string os_type = 12;      // 操作系统类型 //不填
    optional string device_model = 13; // 机器型号 //不填

    optional string signature = 14;             //签名
    optional string device_info = 15;           //设备信息
    optional uint32 is_emulator = 16;           //是否模拟器
    optional string device_id = 17;            //设备号
    optional string current_channel = 18;      //当前包渠道
    optional string origin_channel = 19;       // 1.8.1 源包渠道
    optional AuthCommInfo auth_comm_info = 20; // 登录共有信息
    optional string idfa = 21;                 // idfa only for IOS
}

message ChinaUnicomAuthResp {
    required BaseResp base_resp = 1;
    required string phone_num = 2;   // 手机号
    required bool has_account = 3;   // 手机号 对应的账号是否已存在
    optional AuthInfo auth_info = 4; // 账号存在时，返回用户信息
    optional string password = 5;    // 账号存在时，返回用户密码
}


message CheckPhoneExistReq {
    required BaseReq base_req = 1;
    required string phone = 2;
    required string sec_key = 3;
}

message CheckPhoneExistResp {
    required BaseResp base_resp = 1;
    optional string nick = 2;
    optional bytes small_face = 3;
}

message RegReq {

    required BaseReq base_req = 1;
    required string phone = 2;
    required string pwd = 3;
    required string verify_code = 4;
    optional string nick_name = 5;
    optional uint32 user_sex = 6;
    optional uint32 guild_id = 7;
    optional string reg_invite_code = 8; // 注册时填写的邀请码
    optional uint32 package_type = 9;    // 客户端包类型

    optional string signature = 10;       //签名
    optional string device_info = 11;     //设备信息
    optional uint32 is_emulator = 12;     //是否模拟器
    optional string device_id = 13;       //设备号
    optional string current_channel = 14; //安装包当前渠道号
    optional string imei = 15;
    optional string origin_channel = 16; // 1.8.1 源包渠道
    optional bytes feature = 17;
    optional string os_ver = 18;               // 操作系统版本
    optional string os_type = 19;              // 操作系统类型
    optional string device_model = 20;         // 机器型号
    optional AuthCommInfo auth_comm_info = 21; // 登录共有信息
    // 如果先验证了验证码再进行注册, 则提交该字段并传空的verify_code
    optional string verify_code_credentials = 22;
}

// ret = 21 需要返回  nick_name 和 small_face
message RegResp {
    required BaseResp base_resp = 1;
    required AuthInfo auth_info = 2;

    // 如果填了reg_invite_code，且邀请码正确，则返回以下字段
    optional uint32 invite_guild = 3;
    optional string invite_user_account = 4;
    optional string invite_user_nick = 5;

    optional string reg_invite_code = 6;           // req的邀请码
    optional uint32 reg_invite_room_displayid = 7; // req的邀请码 对应的房间ID
}

// 通过第三方账号注册用户
message ThirdPartyRegReq {

    required BaseReq base_req = 1;
    required uint32 third_party_type = 2; // 第三方账号类型
    required string open_id = 3; // 客户端第三方认证获得的open_id
    required string access_token = 4; // 客户端第三方认证获得的access_token
    required string nick_name = 5;
    required uint32 user_sex = 6;
    optional uint32 guild_id = 7;
    optional string reg_invite_code = 8; // 注册时填写的邀请码
    optional uint32 package_type = 9;    // 客户端包类型

    optional string signature = 10;   //签名
    optional string device_info = 11; //设备信息
    optional uint32 is_emulator = 12; //是否模拟器
    optional string device_id = 13;   //设备号
    optional string imei = 14;
    optional string current_channel = 15; //安装包当前渠道号
    optional string origin_channel = 16;  // 1.8.1 源包渠道
    optional bytes feature = 17;
    optional string os_ver = 18;               // 操作系统版本
    optional string os_type = 19;              // 操作系统类型
    optional string device_model = 20;         // 机器型号
    optional AuthCommInfo auth_comm_info = 21; // 登录共有信息
    optional string thirdparty_app_id = 22;

    optional string user_phone = 23;  // 用户的手机号
    optional string verify_code = 24; // 如果有手机号那么要填验证码
}

message ThirdPartyRegResp {
    required BaseResp base_resp = 1;
    required uint32 third_party_type = 2; // 第三方账号类型
    required AuthInfo auth_info = 3;
    required string password = 4;

    // 如果填了reg_invite_code，且邀请码正确，则返回以下字段
    optional uint32 invite_guild = 5;
    optional string invite_user_account = 6;
    optional string invite_user_nick = 7;

    optional string reg_invite_code = 8;           // req的邀请码
    optional uint32 reg_invite_room_displayid = 9; // req的邀请码 对应的房间ID
}

// 通过中国移动SDK 注册用户
message ChinaMobileRegReq {

    required BaseReq base_req = 1;
    required string china_mobile_token = 2; // 从中国移动SDK 获取的token值
    required string china_mobile_phone = 3; // 手机号

    required string nick_name = 5;
    required uint32 user_sex = 6;
    optional uint32 guild_id = 7;
    optional string reg_invite_code = 8; // 注册时填写的邀请码
    optional uint32 package_type = 9;    // 客户端包类型

    optional string signature = 10;   //签名
    optional string device_info = 11; //设备信息
    optional uint32 is_emulator = 12; //是否模拟器
    optional string device_id = 13;   //设备号
    optional string imei = 14;
    optional string current_channel = 15; //安装包当前渠道号
    optional string origin_channel = 16;  // 1.8.1 源包渠道
    optional bytes feature = 17;
    optional string os_ver = 18;               // 操作系统版本
    optional string os_type = 19;              // 操作系统类型
    optional string device_model = 20;         // 机器型号
    optional AuthCommInfo auth_comm_info = 21; // 登录共有信息
}

message ChinaMobileRegResp {
    required BaseResp base_resp = 1;
    required AuthInfo auth_info = 2;
    required string password = 3;

    // 如果填了reg_invite_code，且邀请码正确，则返回以下字段
    optional uint32 invite_guild = 5;
    optional string invite_user_account = 6;
    optional string invite_user_nick = 7;
    optional string reg_invite_code = 8;           // req的邀请码
    optional uint32 reg_invite_room_displayid = 9; // req的邀请码 对应的房间ID
}

// 通过创蓝SDK 注册用户
message ChinaUnicomRegReq {

    required BaseReq base_req = 1;
    required string china_unicom_token = 2; // 从创蓝SDK 获取的token值
    required string china_unicom_phone = 3; // 手机号

    required string nick_name = 4;
    required uint32 user_sex = 5;
    optional uint32 guild_id = 6;
    optional string reg_invite_code = 7; // 注册时填写的邀请码
    optional uint32 package_type = 8;    // 客户端包类型

    optional string signature = 9;   //签名
    optional string device_info = 10; //设备信息
    optional uint32 is_emulator = 11; //是否模拟器
    optional string device_id = 12;   //设备号
    optional string imei = 13;
    optional string current_channel = 14; //安装包当前渠道号
    optional string origin_channel = 15;  // 1.8.1 源包渠道
    optional bytes feature = 16;
    optional string os_ver = 17;               // 操作系统版本
    optional string os_type = 18;              // 操作系统类型
    optional string device_model = 19;         // 机器型号
    optional AuthCommInfo auth_comm_info = 20; // 登录共有信息
}

message ChinaUnicomRegResp {
    required BaseResp base_resp = 1;
    required AuthInfo auth_info = 2;
    required string password = 3;

    // 如果填了reg_invite_code，且邀请码正确，则返回以下字段
    optional uint32 invite_guild = 5;
    optional string invite_user_account = 6;
    optional string invite_user_nick = 7;
    optional string reg_invite_code = 8;           // req的邀请码
    optional uint32 reg_invite_room_displayid = 9; // req的邀请码 对应的房间ID
}


//已存在用户注册的立刻登录(重置密码 + 登录)
message RegExistPhoneReq {
    required BaseReq base_req = 1;
    required string new_pwd = 2;
    required string phone = 3;
    required string verify_code = 4;

    optional string signature = 5;   //签名
    optional string device_info = 6; //设备信息
    optional uint32 is_emulator = 7; //是否模拟器
    optional string device_id = 8;   //设备号
    optional string imei = 9;
    optional string os_ver = 10;               //操作系统版本
    optional string os_type = 11;              //操作系统类型
    optional string device_model = 12;         //机器型号
    optional string current_channel = 13;      //当前包渠道
    optional string origin_channel = 14;       //源包渠道
    optional AuthCommInfo auth_comm_info = 15; // 登录共有信息
}

message RegExistPhoneResp {
    required BaseResp base_resp = 1;
    required AuthInfo auth_info = 2;
}

//已存在用户注册的 继续注册(重置原账户 + 登录)
message RegResetAccountReq {
    required BaseReq base_req = 1;
    required string phone = 2;
    required string pwd = 3;
    required string verify_code = 4;
    optional string nick_name = 5;
    optional uint32 user_sex = 6;
    optional uint32 guild_id = 7;
}

message RegResetAccountResp {
    required BaseResp base_resp = 1;
    required AuthInfo auth_info = 2;
}

enum USAGE {
    REG = 1; // 亦可用于登录
    RESET_PWD = 2;
    BIND_PHONE = 3;
    REBIND_PHONE = 4;
}

//无法获取session_key场合也可以使用的协议需要客户端生成sec_key
message AccountVerifyCodeReq {
    required BaseReq base_req = 1;
    required string phone = 2;
    required string sec_key = 3;
    required uint32 usage = 4;
    required uint32 retry_count = 5;
}

message AccountVerifyCodeResp {
    required BaseResp base_resp = 1;
    optional string uuid = 2; // 本次验证码token兑换凭证, SubmitVerifyCode时上报
}

// 提交验证码
message SubmitVerifyCodeReq {
    required BaseReq base_req = 1;
    required string uuid = 2;
    required string code = 3;
    required string sec_key = 4;
}

message SubmitVerifyCodeResp {
    required BaseResp base_resp = 1;
    required string verify_code_credentials = 2;
    // 如果请求的验证码中usage为REG, 则会返回该手机号的注册状态
    optional bool phone_registered = 3;
}

// 通用敏感操作验证码 发送接口
message GeneralSendVerifyCodeReq {
    required BaseReq base_req = 1;
    required int32 errcode = 2; // 废弃字段 不要用了 填0就好
    optional string phone = 3;
    // 一定要填  see E_SENSTIVE_VERIFYCODE_OP_TYPE
    optional uint32 verify_op_type = 4;
    optional uint32 code_type = 5; //E_VERIFYCODE_TYPE 0或者不填短信，1语音
}
message GeneralSendVerifyCodeResp { required BaseResp base_resp = 1; }

// 通用敏感操作验证码 验证接口
message GeneralCheckVerifyCodeReq {
    required BaseReq base_req = 1;
    required int32 errcode = 2; // 废弃字段 不要用了 填0就好
    required string verifycode = 3;
    // 一定要填 see E_SENSTIVE_VERIFYCODE_OP_TYPE。 验证接口 与
    // 发生接口的 opType要匹配
    optional uint32 verify_op_type = 4;
}
message GeneralCheckVerifyCodeResp { required BaseResp base_resp = 1; }

//已登录用户修改密码
//流程:填完新旧密码,提交. 新旧密码不能一样.
//修改成功需要重新登录,已经下发短信通知用户
message ModifyPWDReq {
    required BaseReq base_req = 1;
    required string new_pwd = 2;

    // client需要根据password_set(登录AutnInfo中带回,
    // 然后本地维护状态)来决定是否填充下列字段
    // 服务器将根据用户设置密码状态进行或者跳过旧密码检查
    // 如果未设置过密码, old_pwd填写任意字符串
    required string old_pwd = 3;
}

message ModifyPWDResp { required BaseResp base_resp = 1; }

message ModifySexReq {
    required BaseReq base_req = 1;
    required uint32 user_sex = 2; //用户性别
}

message ModifySexResp { required BaseResp base_resp = 1; }

//未登录用户重置密码
message ResetPWDReq {
    required BaseReq base_req = 1;
    required string new_pwd = 2;
    required string phone = 3;
    required string verify_code = 4;
    required string sec_key = 5;
}

message ResetPWDResp { required BaseResp base_resp = 1; }

//完善用户信息
message UserRegCompleteReq {
    required BaseReq base_req = 1;
    required string nick_name = 3;
    required uint32 user_sex = 5;
}

message UserRegCompleteResp {
    required BaseResp base_resp = 1;
    required string user_account = 2;
    required string nick_name = 3;
}

message ModifyVerifyReq {
    required BaseReq base_req = 1;
    required bool should_verify = 2;
}

message ModifyVerifyResp { required BaseResp base_resp = 1; }

message CheckVerifyCodeReq {
    required BaseReq base_req = 1;
    required string phone = 2;
    required string verify_code = 3;
    required string sec_key = 4;
}

message CheckVerifyCodeResp { required BaseResp base_resp = 1; }

message ReportCrashReq {
    required BaseReq base_req = 1;
    required string crash_key = 2;
    required string crash_time = 3;
    required uint32 crash_uid = 4;
}

message ReportCrashResp {
    required BaseResp base_resp = 1;
    required string crash_key = 2;
    required string crash_time = 3;
    required uint32 crash_uid = 4;
}

message CheckUpgradeReq {
    enum NETWORK_STATE {
        WIFI = 1;
        MOBILE = 2;
    }
    required BaseReq base_req = 1;
    required string curr_ver = 2; //当前版本
    required uint32 ver_code = 3; // version code
    required uint32 network_state = 4;
    required uint32 guild_id = 5;
    required string device_id = 6; //设备号
    required string channel = 7;   //渠道
    optional bool manual = 8;
}

message CheckUpgradeResp {
    required BaseResp base_resp = 1;
    required string latest_ver = 2;       // 最新版本
    required bool should_prompt = 3;      // 是否弹框提示
    required bool force_upgrade = 4;      // 强升
    required string url = 5;              // 下载地址
    required bool red_point = 6;          // 红点提示
    required string title = 7;            // 升级提示title
    required string content = 8;          // 升级提示content
    required bool should_upgrade = 9;     // 是否需要升级
    optional uint32 latest_ver_code = 10; // 最新版本的version code
    optional string file_md5 = 11;        // 文件md5
    optional string head_md5 = 12;        // 文件前100KB的md5

    // 应该提示升级的包，一定天数后增大提示密度
    optional uint32 high_freq_prompt_after = 13;
    // 增大提示密度情况下，提示的间隔时间(小时)
    optional uint32 high_freq_prompt_interval = 14;
}

message UploadLogReq {
    enum UPLOAD_RESULT {
        SUCC = 0;
        FAIL = 1;
        NOT_WIFI = 2;
        NO_LOG = 3;
    }
    required BaseReq base_req = 1;
    required uint32 uid = 2;
    required uint32 result = 3;
    repeated string key_list = 4;
    required string manufacturer = 5; //厂商
    required string product = 6;
    required string os_ver = 7;   //系统版本
    required string hw_model = 8; //机型
    required string operator = 9; //运营商
    required string network = 10; //网络
    optional bytes pull_log_context = 11;
}

message UploadLogResp { required BaseResp base_resp = 1; }

message LogoutReq { required BaseReq base_req = 1; }

message LogoutResp { required BaseResp base_resp = 1; }

// 绑定手机
message BindPhoneReq {
    required BaseReq base_req = 1;
    required string phone = 2;       // 待绑定的手机
    required string verify_code = 3; // 待绑定的手机收到的验证码

    // client需要根据password_set(登录AuthInfo中带回,
    // 然后本地维护状态)来填充下列字段之一:
    // 未设置过密码的用户, 必须填写该字段设置密码
    optional string new_password_md5 = 4;
    // 设置过密码的用户, 必须填写该字段进行密码确认
    optional string confirm_password_md5 = 5;
}

message BindPhoneResp { required BaseResp base_resp = 1; }

message RebindPhoneReq {
    required BaseReq base_req = 1;
    required string phone = 2;       // 手机号
    required string verify_code = 3; // 手机收到的验证码
    required bool is_bind = 4;       // 0.解绑 1.绑定
}

message RebindPhoneResp {
    required BaseResp base_resp = 1;
    optional string phone = 2; // 用户当前绑定的手机号
    optional bool is_bind = 3; // 0.解绑 1.绑定
}

// 获取验证码
message GetCAPTCHAReq {
    required BaseReq base_req = 1;
    required uint32 verify_reason = 2; //验证原因：1登录
    required string pwd = 3;           // 密码
    // 用户手机号（如果TT账号，则此属性为TT账号）
    optional string user_phone = 4;
    //账号类型： 1 手机号， 2 TT账号 3 第三方账号
    optional uint32 login_account_type = 5;
    optional uint32 third_party_type = 6; // 第三方账号类型
    optional string open_id = 7; // 客户端第三方认证获得的open_id
    optional string access_token = 8; // 客户端第三方认证获得的access_token
    optional string thirdparty_app_id = 9; // 第三方登录所用的appid
    optional string china_mobile_token = 10; // 中国移动一键登录时 用到的token
    optional string china_unicom_token = 11; // 中国联通一键登录时 用到的token
}

message GetCAPTCHAResp {
    required BaseResp base_resp = 1;
    required CAPTCHAInfo captcha_info = 2; //验证码信息
}

// 校验验证码
message VerifyCAPTCHAReq {
    required BaseReq base_req = 1;
    required uint32 verify_reason = 2;     //验证原因：1登录
    required string pwd = 3;               // 密码
    required CAPTCHAInfo captcha_info = 4; //验证码信息
    // 用户手机号（如果TT账号，则此属性为TT账号）
    optional string user_phone = 5;
    //账号类型： 1 手机号， 2 TT账号 3 第三方账号
    optional uint32 login_account_type = 6;
    optional uint32 third_party_type = 7; // 第三方账号类型
    optional string open_id = 8; // 客户端第三方认证获得的open_id
    optional string access_token = 9; // 客户端第三方认证获得的access_token
    optional string thirdparty_app_id = 10; // 第三方登录所用的appid
    optional string china_mobile_token = 11; // 中国移动一键登录时 用到的token
    optional string china_unicom_token = 12; // 中国联通一键登录时 用到的token
}

message VerifyCAPTCHAResp { required BaseResp base_resp = 1; }

/**    实名认证 BEGIN    **/
enum E_REALNAME_AUTH_STATUS {
    ENUM_REALNAME_AUTH_NONE = 1; // 没有提交 ----------------    status_tips:
                                 // 未认证
    ENUM_REALNAME_AUTH_CHECK = 2; // 审核中 ----------------    status_tips:
                                  // 审核中
    ENUM_REALNAME_AUTH_PASS = 3; // 通过 ----------------    status_tips: 已认证
    ENUM_REALNAME_AUTH_UNAPPROVE = 4; // 审核未通过 ----------------
                                      // status_tips: 审核未通过
    ENUM_REALNAME_AUTH_NEED_ADDED = 5; // 待补充(已在提现实名或直播实名的状态)
                                       // ---------------- status_tips: 待补充
    ENUM_REALNAME_AUTH_UNVALID = 6; // 已认证 (身份证过期) ----------------
                                    // status_tips: 已认证（身份证过期，请更新）
}

enum E_REALNAME_AUTH_VERSION {
    ENUM_REALNAME_AUTH_V_1 = 1;
    ENUM_REALNAME_AUTH_V_2 = 2;
}

message RealNameAuthInfo {
    optional string auth_phone = 1;
    optional string name = 2;         // 姓名
    optional string identity_num = 3; // 身份证号码
}

message GetRealNameAuthStateReq { required BaseReq base_req = 1; }

message GetRealNameAuthStateResp {

    required BaseResp base_resp = 1;
    optional bool finish_phone_auth = 2;
    optional string finish_phone_num = 3;

    optional bool is_need_perposed_check = 4; // 是否开启前置检查
    // 当前实名认证状态         ----------------- E_REALNAME_AUTH_STATUS
    optional uint32 status = 5;
    // 认证状态对应的文案       ----------------- E_REALNAME_AUTH_STATUS
    optional string status_tips = 6;
    optional RealNameAuthInfo realname_info = 7;
    // see E_REALNAME_AUTH_VERSION 当前使用的实名认证版本
    optional uint32 realname_version = 8;
}

message GetUserIdentityInfoReq { required BaseReq base_req = 1; }

message GetUserIdentityInfoResp {
    required BaseResp base_resp = 1;
    required bool is_adult = 2;
    optional uint32 age = 3;

    // 身份证是否在有效期，true:有效，未过期。
    required bool is_identity_valid = 4;

    // 未认证/审核不过/审核中/待补充/身份证过期 为true 充值时需要提示文案
    /*optional string identity_valid_time = 4;  //
     * 身份证有效期的截至日期(0000-00-00),永久有效位 0000-00-00*/
    required bool is_need_prompt = 5;
}
// realnameauth  third version

// 查询 faceId token
message GetRealNameAuthTokenReq {

    required BaseReq base_req = 1;
    required RealNameAuthInfo realname_info = 2;
}

message GetRealNameAuthTokenResp {
    required BaseResp base_resp = 1;
    required string faceid_token = 2;
    required uint32 result_code = 3; // faceid 返回状态码
    required string result_msg = 4;
    // 当前实名认证状态，see E_REALNAME_AUTH_STATUS 如果 已认证，token为空
    required uint32 curr_status = 5;
    required RealNameAuthInfo realname_info = 6;
}

message ApplyRealNameAuthDataReq {
    required BaseReq base_req = 1;
    required string faceid_token = 2;
    required string faceid_check_data = 3;
}

message ApplyRealNameAuthDataResp {
    required BaseResp base_resp = 1;
    required uint32 result_code = 2; // faceid 返回状态码
    required string result_msg = 3;
}

//已实名用户通过uid获取token
message GetRealNameFaceIdTokenByUidReq
{
  	required BaseReq base_req = 1;
}

message GetRealNameFaceIdTokenByUidResp
{
	required BaseResp base_resp = 1;
	required string faceid_token = 2;
	optional uint32 result_code = 3;
	optional string result_msg  = 4;
}
/**    实名认证 END    **/

message RegisterApnsDeviceTokenReq {
    enum CMD {
        Register = 1;   // 注册token
        Unregister = 2; // 注销token, 用户手动关闭推送时使用
    }

    enum OS_Type {
        AUTO = 0; // depends on terminal type in service packet header
        iOS = 1;
        OSX = 2; // reserved
        Android = 3;
    }

    enum DeviceTokenType {
        APNs = 0;       // 苹果推送(iOS/macos)
        UPUSH = 1;      // 友盟推送
        OPPO_PUSH = 2;  // Oppo推送(ColorOS)
        VIVO_PUSH = 3;  // Vivo推送(Funtouch OS)
        GETUI_PUSH = 4; // 个推
    }

    required BaseReq base_req = 1;   // Aaaaaaa
    required uint32 cmd = 2;         // Register or Unregister
    required bytes device_token = 3; // original 32 bytes device token
    required uint32 os_type = 4;     // @see OS_Type
    required string os_ver = 5;      // e.g. `8.0`
    optional string sound = 6; // name of sound file in the bundle, e.g. `msg`
    // name of voip sound file in the bundle, e.g. `ring`
    optional string voip_sound = 7;
    optional string bundle_id = 8;                  // bundle id
    optional DeviceTokenType device_token_type = 9; // type of the device token
}

message RegisterApnsDeviceTokenResp {
    required BaseResp base_resp = 1; // Aaaaaaa
}

/* Parent Guardian BEGIN*/
message GetParentGuardianStateReq { required BaseReq base_req = 1; }

message GetParentGuardianStateResp {
    required BaseResp base_resp = 1;
    required bool on_off = 2; // auth:已开启；false:未开启
}

message SwitchParentGuardianReq {
    required BaseReq base_req = 1;
    required bool on_off = 2; // true:开启；false:关闭
    required string password = 3;
}

message SwitchParentGuardianResp { required BaseResp base_resp = 1; }

//查询今日申诉次数有无超过限制
message CheckAppealCntIsOverLimitReq {
	required BaseReq base_req = 1;
}

message CheckAppealCntIsOverLimitResp {
	required BaseResp base_resp = 1;
	required bool is_over_limit = 2;
}

//忘记密码申诉扫脸验证
message ParentGuardianCheckFaceReq {
	required BaseReq base_req = 1;
    required string faceid_check_data = 2;
    required string faceid_token = 3;
}

message ParentGuardianCheckFaceResp {
	required BaseResp base_resp = 1;
	required bool is_pass = 2;
}

//申诉成功后强制退出家长模式
message ForceOffParentGuardianReq {
	required BaseReq base_req = 1;
}

message ForceOffParentGuardianResp {
	required BaseResp base_resp = 1;
}

/* Parent Guardian END*/

// 敏感操作 需要验证码操作类型
enum E_SENSTIVE_VERIFYCODE_OP_TYPE {
    ENUM_SENSTIVE_OP_GUILD_CHAIRMAN_OP = 1;  // 通用会长操作
    ENUM_SENSTIVE_OP_GUILD_SEND_VOUCHER = 2; // 会长发代金券 操作
    ENUM_SENSTIVE_OP_GUILD_DISMISS = 3;      // 公会解散 操作

    ENUM_SENSTIVE_OP_GUILD_COMMISSION_WITHDRAW = 4; // 会长佣金 提现
    ENUM_SENSTIVE_OP_USER_SCORE_WITHDRAW = 5;       // 用户积分 提现
    ENUM_SENSTIVE_OP_UPDATE_WITHDRAW_PROFILE = 6;   // 更新个人提现信息

    ENUM_SENSTIVE_OP_REALNAME_VERIFY_PREPOSED = 7; // 实名验证 前置检查
    ENUM_SENSTIVE_OP_ANOMALY_DEVICE_LOGIN = 8;     // 异常设备登陆检查
    ENUM_SENSTIVE_OP_MODIFY_PWD = 9;               // 修改密码

   
    ENUM_SENSTIVE_OP_REALNAME_VERIFY_PHONE = 10;  // 实名验证 实名手机号 绑定与验证

    ENUM_SENSTIVE_OP_REALNAME_BIND_PHONE = 11; // 登陆需要绑定实名手机
    ENUM_SENSTIVE_OP_UNREGISTER = 12;          // 注销账号
    
    ENUM_SENSTIVE_OP_FACEID_REALNAME_VERIFY_PHONE = 13; // 第三代实名认证 实名验证 实名手机号
	
    ENUM_ANCHOR_CONTRACT_ANCHOR_OPT = 14;   // 主播签约系统 歌手操作
    ENUM_ANCHOR_CONTRACT_GUILDOWN_OPT = 15; // 主播签约系统 会长操作
}

enum E_VERIFYCODE_TYPE
{
    ENUM_MESSAGE_CODE = 0; //短信验证码
    ENUM_VOICE_CODE   = 1; //语音验证码
}

message ThridpartVerifyCheckReq {
    required BaseReq base_req = 1;

    required uint32 third_party_type = 2; // 第三方账号类型 see THIRD_PARTY_TYPE
    required string open_id = 3; // 客户端第三方认证获得的open_id
    required string access_token = 4; // 客户端第三方认证获得的access_token

    required uint32 verify_op_type = 5; // see E_SENSTIVE_VERIFYCODE_OP_TYPE

    optional string thirdparty_app_id = 6;
}

message ThridpartVerifyCheckResp { required BaseResp base_resp = 1; }

message GetUnregApplyAuditStatusReq { required BaseReq base_req = 1; }

message GetUnregApplyAuditStatusResp {
    enum AuditStatus {
        ST_NIL = 0;         // 空，无申请
        ST_IN_PROGRESS = 1; // 审核中
        ST_ACCEPT = 2;      // 申请 通过
        ST_REJECT = 3;      // 申请 被 拒绝
    }
    required BaseResp base_resp = 1;
    required uint32 status = 2; // AuditStatus
}

//无法获取session_key场合也可以使用的协议需要客户端生成sec_key
message AccountVoiceVerifyCodeReq {
    required BaseReq base_req = 1;
    required string phone = 2;
    required string sec_key = 3;
    required uint32 usage = 4;
    required uint32 retry_count = 5;
}

message AccountVoiceVerifyCodeResp {
    required BaseResp base_resp = 1;
    optional string uuid = 2; // 本次验证码token兑换凭证, SubmitVerifyCode时上报
}

enum ENUM_ContractStatus
{
  ENUM_ContractStatus_Invalid = 0;                  // 无效的状态
  ENUM_ContractStatus_UnApply = 1;                  // 未申请
  ENUM_ContractStatus_Apply = 2;                    // 申请中
  ENUM_ContractStatus_Sign = 3;                     // 已签约          
  ENUM_ContractStatus_IdentitySign = 4;             // 身份证已被已签约
}

message ContractInfo
{
  required uint32 guild_id = 1;
  required string guild_name = 2;
}

message GetContractInfoReq
{
  required BaseReq base_req = 1;
  required uint32 actor_uid = 2;
}

message GetContractInfoResp
{
  required BaseResp base_resp = 1;
  required uint32 contract_status = 2;          // see ENUM_ContractStatus
  required string status_tip = 3;
  optional ContractInfo contract_info = 4;
}




// 客户端登录如果收到ACCOUNT_LOGIN_NEED_BIND_PHONE
// -157错误，可用此结构解析BaseResp中的err_info
message LoginNeedBindPhoneErrorInfo {
  required string security_center_auth_token = 1;  // 该token仅用于登录安全中心
}
