syntax = "proto2";

package ga.topic_channel;

import "ga_base.proto";
import "channel_.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/topic-channel";

message TopicChannelInfo {
    required uint32 channel_id              = 1;  // 频道id
    required uint32 display_id              = 2;  // 显示Id
    required uint32 app_id                  = 3;  // 频道对应的APPID
    required bool   has_pwd                 = 4;  // 是否有密码
    required uint32 channel_type            = 5;  // 频道类型 see ga::ChannelType, 开黑房那些
    required uint32 bind_id                 = 6;
    required uint32 switch_flag             = 7;  // 各种房间开关的标记
    required uint32 creator_uid             = 8;
    required string icon_md5                = 10; // 房间图标
    required string topic_title             = 11; // 房间话题描述（标题）
    required string name                    = 13; // 名字

    enum TopicChannelType {
        NORMAL = 0;
        RECOMMEND_CHANNEL = 1;
    }
    required TopicChannelType topic_channel_type  = 14; // 房间是否为推荐房
    required uint32 online_count                 = 15;  // 房间在线人数
    required uint32 male_count                   = 16;  // 在线人数男人数
    required uint32 female_count                 = 17;  // 在线人数女人数
}

//message TopicChannelMember {
//    uint32 uid = 1;
//    uint32 sex = 2;
//    string account = 3;
//}

message ChannelOwnerInfo {
    required uint32 uid = 1;     //
    required uint32 sex = 2;     //性别
    required string account = 3; //
    repeated string tags = 4;    //用户填写的标签
}


message TopicChannelTab {
    required uint32 id = 1;
    required string name = 2;
}


message RecommendItem {
    required TopicChannelInfo channel_info = 1; //房间信息
    required TopicChannelTab  tab_info = 2;     //房间标签信息
    required ChannelOwnerInfo user_info = 3;    //房间创建者的信息
}

//直接按照返回的传回即可，不需对此另外赋值
message ListRecommendTopicChannelLoadMore {
    message All {
        optional uint32 num = 1;
        repeated uint32 recommendation = 2;
    }

    message ByTabLoadMoreItem {
        required uint64 cursor = 1;
        required uint32 last_value = 2;
        required uint32 last_index = 3;
        required int64  last_count = 4;
        required bool   the_end = 5;
    }

    message ByTabLoadMore {
        optional ByTabLoadMoreItem newborn = 1;
        optional ByTabLoadMoreItem sink = 2;
        optional ByTabLoadMoreItem big = 3;
    }

    optional All all = 1;
    optional ByTabLoadMore by_tab = 2;
}

//获取【约玩】推荐房间列表
message ListRecommendTopicChannelReq {
    required ga.BaseReq base_req = 1;
    required uint32 tab_id   = 2;                                //指定类型获取推荐列表
    optional uint32 count = 3;                                   //获取几个, 默认10
    optional ListRecommendTopicChannelLoadMore load_more = 10;   //首次获取/下拉刷新置为null，下次直接传回response返回的load_more即可
    enum ChannelTabType {
        NORMAL = 0; //普通分类。
        GAME = 1;   //游戏分类。
    }
    optional ChannelTabType tab_type = 11;
}


message ListRecommendTopicChannelResp {
    required ga.BaseResp base_resp = 1;

    repeated RecommendItem items = 2;

    optional ListRecommendTopicChannelLoadMore load_more = 10; //返回的load_more为null表示已加载完没有下一页
    optional bool load_finish = 11;                            //返回true表示没有下一页了
}

//创建主题房
message CreateTopicChannelReq {
    required ga.BaseReq base_req = 1;
    optional string name = 2;       // 主题房名
    required uint32 tab_id = 3;     // 主题房所选分类id
    required uint32 channel_id = 4; // 房间id，如有就传过来

    message TagOption {
        required uint32 option_id = 2;      //对应选择那一块tag的id
        required uint32 item_id = 3;        //对应哪个标签的id
    }
    message RoomName {
        required uint32 content_id = 1;     //对应的文案id
        repeated TagOption option = 2;      //用户选中的标签数据
    }
    optional RoomName room_name = 5;        //拼接出来的主题房名，新版本游戏标签下用
    optional uint32 room_name_version = 6;  //指定标签下房间名配置版本号
}

message CreateTopicChannelResp {
    required ga.BaseResp base_resp = 1;
    required uint32 channel_id = 2;             // 房间id
    required EChannelMicMode old_mic_mode = 3;  // 房间是什么类型
    required EChannelMicMode new_mic_mode = 4;  // 房间需要切成什么类型的房间
    optional uint32 room_name_version = 5;      // 指定标签下房间名配置版本号
}

message GetTopicChannelInfoReq {
    required ga.BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message GetTopicChannelInfoResp {
    required ga.BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    required uint32 tab_id = 3;         // 主题房所选分类id
    required string tab_name = 4;       // 主题房的标签名
    required bool is_in_ground = 5;     // 主题房是否显示在广场
}

//取消主题房在大厅显示
message HideTopicChannelReq {
    required ga.BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message HideTopicChannelResp {
    required ga.BaseResp base_resp = 1;
}

//20秒调用一次，房主与主题房保持心跳，超时会停止展示在大厅
message KeepAliveTopicChannelReq {
    required ga.BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message KeepAliveTopicChannelResp {
    required ga.BaseResp base_resp = 1;
    required bool the_end = 2;  //若为true，不需要再发心跳
}

// Tab 是主题房间分类管理中的一种类型。
message Tab {
    required uint32 id        = 1; // id 是类型的唯一标识。
    required string name      = 2; // name 代表主题。
    //bool   is_game          = 3; // is_game 代表是否为游戏分类。
    required string image_uri = 4; // image_uri 代表背景用图的URI。
    required uint32 version   = 5; // version 是房间示例名称的版本。

    enum TabType {
        NORMAL = 0; //普通分类。
        GAME = 1;   //游戏分类。
    }
    optional TabType tab_type = 6;
    enum RoomNameType {
        DEFAULT = 0;    //默认推荐文案
        SPLICE = 1;     //需要拼接
    }
    optional uint32 tag_id    = 7; // 对应游戏标签的id
    optional RoomNameType room_name_type = 8;
    optional uint32 room_name_version = 9; // 主题房对应类型取名配置的版本号
}

// TabTopicChannelReq 是用于获取【约玩】主题房间分类列表的请求。
message TabTopicChannelReq {
    required ga.BaseReq base_req = 1;
    optional uint32     page     = 2; // 第几页，从0开始。
    optional uint32     count    = 3; // 每页多少个。

    enum ReturnedMode {
        NORMAL = 0;    // 普通分类。
        RECOMMEND = 1; // 普通分类+推荐分类。
    }
    optional ReturnedMode returned_mode = 4; // returned_mode用于指定返回模式，即返回中包含哪些分类。
}

// TabTopicChannelResp 是用于获取【约玩】主题房间分类列表的响应。
message TabTopicChannelResp {
    required ga.BaseResp  base_resp = 1;
    repeated Tab tabs               = 2;
    required uint32 total           = 3;
    optional uint32 index           = 4; //默认下标
}

// GetTopicChannelRoomNameReq 是用于获取最新房间示例名称的请求。
message GetTopicChannelRoomNameReq {
    required ga.BaseReq  base_req   = 1;
    repeated uint32 tab_id          = 2;
}

// ChannelNameInfo 主题房标签对应的示例名称信息。
message ChannelNameInfo {
    required uint32 tab_id    = 1;
    repeated string room_name = 2;
    required uint32 version   = 3;
}

// GetTopicChannelRoomNameResp 是用于获取最新房间示例名称的响应。
message GetTopicChannelRoomNameResp {
    required ga.BaseResp base_resp = 1;
    repeated ChannelNameInfo info  = 2;
}

// 获取房间文明公约提示
message GetRoomProxyTipReq {
    required ga.BaseReq base_req   = 1;
}

message GetRoomProxyTipResp {
    required ga.BaseResp base_resp = 1;
    required string url = 2;            //跳转url
    required string title = 3;          //标题
    enum ShowType {
        SHOW_ALWAYS = 0;                //每次弹
        CAN_HIDE = 1;                   //之前同意过就不用弹了
    }
    required ShowType show_type = 4;
}

message GetChannelRoomNameConfigReq {
     required ga.BaseReq  base_req   = 1;
     required uint32 tab_id = 2;
}

message GetChannelRoomNameConfigResp {
    required ga.BaseResp base_resp = 1;
    optional RoomNameConfigure room_name_configure = 2; //channel room name配置信息
}

message TagOption {
    message TagItem {
        required string src_string = 1;      //客户端的显示的标签字符串
        optional string splice_string = 2;   //选中后拼接用的标签字符串
        optional uint32 item_id = 3;
    }

    required uint32 option_id = 1;           //模板id
    optional string source_title = 2;        //对应原来游戏标签的标题
    required string client_title = 3;        //客户端显示的标题
    repeated TagItem tag_items = 4;          //标签的对应关系
}

message TagContent {
    message SequenceItem {
        enum ItemType {     //拼接类型
                SELF = 0;       //使用自身文案(text)拼接
                TAG = 1;        //使用标签的名称拼接
                TEXT = 2;       //使用指定文案作分隔符拼接
        }
        optional uint32 option_id = 1;           //对应的标签模板id
        optional string split_text = 2;          //对应的分隔文案
        required ItemType type = 3;              //类型
    }
    required string text = 1;                //文案
    repeated SequenceItem sequence = 2;     //拼接规则
    required uint32 content_id = 3;
}
// 用于自动生成房间名的配置
message RoomNameConfigure {
    optional uint32 tab_id = 1;                  //主题房的tab id
    repeated TagOption tag_option = 2;          //标签选项数据
    repeated TagContent tag_content = 3;        //文案数据
    optional uint32 version = 4;               //主题房对应类型取名配置的版本号
}
