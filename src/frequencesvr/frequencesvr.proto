syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package frequencesvr;

message GetFrequenceReq{
    required uint32 svr_type = 1;
    required string fre_key = 2;
}

message GetFrequenceResp{
    required int32 left_time = 1;
}

message SetFrequenceReq{
    required uint32 svr_type = 1;
    required string fre_key = 2;
    optional string fre_value = 3;
}

message FrequenceData{
	required int32 limit_start_time = 1;
	required int32 count = 2;
}

message CleanFrequenceReq {
	required uint32 svr_type = 1;
	required string fre_key = 2;
}

message CleanFrequenceResp {
}




//新增内容，改部分数据操作以mysql+memcache构成

message FrequenceInfo {
	required uint32 trigger_limit = 1;
	required uint32 interval_limit = 2;
	required uint32 limit_times = 3;
	required string fre_name = 4;
}

message AddLimitReq {
	required FrequenceInfo fre_info = 1;
}

message AddLimitResp {
}


message GetAllLimitInfoReq {

}

message GetAllLimitInfoResp {
	repeated FrequenceInfo fre_info_list = 1;
}


message RemoveLimitReq {
	required string fre_name = 1;
}


message RemoveLimitResp {

}

message GetLimitInfoReq{
	required string fre_name = 1;
}


message GetLimitInfoResp{
	required FrequenceInfo fre_info = 1;
}

message TriggerLimitReq {
	required string fre_name = 1;
	required string key_word = 2;
}

message TriggerLimitResp {
	required bool is_limited = 1;
}

service FrequenceSvr{
    option( tlvpickle.Magic ) = 15170;

    rpc GetFrequence ( GetFrequenceReq ) returns ( GetFrequenceResp ) {
        option( tlvpickle.CmdID ) = 1;										// 命令号
	    option( tlvpickle.OptString ) = "s:k:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-s <svrType> -k <key>";	// 测试工具的命令号帮助	
    }

    rpc SetFrequence ( SetFrequenceReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 2;										// 命令号
	    option( tlvpickle.OptString ) = "s:k:v";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-s <svrType> -k <key> -v <value>";	// 测试工具的命令号帮助	
    }

    rpc CleanFrequence ( CleanFrequenceReq ) returns ( CleanFrequenceResp ) {
        option( tlvpickle.CmdID ) = 3;									// 命令号
	    option( tlvpickle.OptString ) = "s:k:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-s <svrType> -k <key>";			// 测试工具的命令号帮助	
    }


	/////////////////////////////////////////////////////////////////////////////////////

	rpc AddLimit ( AddLimitReq ) returns ( AddLimitResp ) {
        option( tlvpickle.CmdID ) = 4;									// 命令号
	    option( tlvpickle.OptString ) = "n:t:i:m:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-n <fre_name> -t <trigger_limit> -i <interval_limit> -m <limit_time>";			// 测试工具的命令号帮助	
    }

	rpc GetAllLimitInfo ( GetAllLimitInfoReq ) returns ( GetAllLimitInfoResp ) {
        option( tlvpickle.CmdID ) = 5;									// 命令号
	    option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";			// 测试工具的命令号帮助	
    }

	rpc RemoveLimit ( RemoveLimitReq ) returns ( RemoveLimitResp ) {
        option( tlvpickle.CmdID ) = 6;									// 命令号
	    option( tlvpickle.OptString ) = "n:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-n <fre_name>";			// 测试工具的命令号帮助	
    }

	rpc GetLimitInfo ( GetLimitInfoReq ) returns ( GetLimitInfoResp ) {
        option( tlvpickle.CmdID ) = 7;									// 命令号
	    option( tlvpickle.OptString ) = "n:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-n <fre_name>";			// 测试工具的命令号帮助	
    }

	rpc TriggerLimit ( TriggerLimitReq ) returns ( TriggerLimitResp ) {
        option( tlvpickle.CmdID ) = 8;									// 命令号
	    option( tlvpickle.OptString ) = "n:u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-n <fre_name> -u <keyword>";			// 测试工具的命令号帮助	
    }
}