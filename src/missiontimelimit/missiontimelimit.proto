syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package missiontimelimit;

// 限时任务子类型
enum TIME_LIMIT_MISSION_SUB_TYPE {
	TIME_LIMIT_SUB_TYPE_INVALID = 0;
	TIME_LIMIT_GAME_CONTINUOUS_LOGIN = 1;		// 游戏连续登录
	TIME_LIMIT_GAME_ACCUM_LOGIN = 2;			// 游戏累计登录
	TIME_LIMIT_GAME_ACCUM_RECHARGE = 3;			// 游戏累计充值
	TIME_LIMIT_GAME_RECHARGE = 4;				// 游戏单笔充值
}

// 事件类型
enum EVENT_TYPE {
	EVENT_INVALID = 0;
	GAME_LOGIN = 1;				// 游戏登陆事件
	GAME_RECHARGE = 2;			// 游戏充值事件
	
	EVENT_UNLIMIT = 100;			// 所有事件类型
}

// 任务状态
enum TIME_LIMIT_MISSION_STATUS {
	TL_STATUS_INVALID = 0;
    TL_IN_PROGRESS = 1;		// 任务进行中
    TL_FINISHED = 2;		// 任务已经完成, 奖励未领取
    TL_COLLECTED = 3;		// 已领取奖励
}

message Platform {
    enum Values {
		Platform_Unknown = 0;
        Android = 1;
        iOS = 2;
    }
}

message TLMissionAcceptLimit
{
	uint32 count = 1;	// 可接受任务的人数
	uint32 date = 2;	// 日期格式：YYYYMMDD
}

// 限时任务定义
message TLMissionConfig {
	uint32 mission_id = 1;			// id
	string name = 2;				// 名称
	string sub_name = 3;			// 子名称（副标题）
	string strategy = 4;			// 攻略
	uint32 exp = 5;					// 经验奖励
	uint32 red_diamond = 6;			// 红钻奖励
	uint32 repeat_count = 7;		// 需要执行的次数
	uint32 extend_value = 8;
	uint32 add_time = 9;            // 任务添加的时间
	uint32 show_time = 10;			// 任务可见的起始时间
	uint32 effect_begin = 11;       // 任务生效起始时间
	uint32 effect_end = 12;         // 任务生效结束时间
	uint32 event_type = 13;			// 关联的事件类型
	uint32 sub_type = 14;			// 任务子类型
	uint64 ly_game_id = 15;			// 联运游戏ID
	repeated TLMissionAcceptLimit accept_limit_list = 16;	// 每日接受任务的人数限制
	uint32 show_index = 17;			// 显示位置
	bool is_hidden = 18;			// 是否隐藏（下架，只有可领奖的用户可见）
}

// 获取限时任务列表
message GetTimeLimitMissionListReq {
	uint32 last_update_time = 1;	// 0.强制获取限时游戏任务
	uint32 event_type = 2;	//EVENT_TYPE
}

message GetTimeLimitMissionListResp {
	uint32 update_time = 1;
	repeated TLMissionConfig mission_list = 2;	//update_time与请求的last_update_time一致时，不会返回任务数据
}

message GetTimeLimitMissionByIdReq {
	uint32 mission_id = 1;
	bool is_force = 2;	// 直接从db查，已删除或过期的任务也显示
}

message GetTimeLimitMissionByIdResp {
	TLMissionConfig mission_config = 1;
}

message AddTimeLimitMissionReq {
	TLMissionConfig mission_config = 1;
}

message AddTimeLimitMissionResp {
	uint32 mission_id = 1;
}

message DelTimeLimitMissionReq {
	uint32 mission_id = 1;
}

message UpdateTimeLimitMissionReq {
	TLMissionConfig mission_config = 1;
}

message GetTimeLimitMissionConfigUpdateTimeReq {

}

message GetTimeLimitMissionConfigUpdateTimeResp {
	uint32 update_time = 1;
}

message UserLoginExtend {
	uint32 login_count = 1;
	uint32 last_login_at = 2;
}

message UserRechargeExtend {
	uint32 recharge_count = 1;
	uint32 last_recharge_at = 2;
}

// 用户的限时任务信息
message UserTLMission {
	uint32 uid = 1;
	uint32 mission_id = 2;			// id
	uint32 finish_count = 3;		// 完成次数
	uint32 status = 4;				// 状态
	uint32 expire_time = 5;			// 过期时间
	uint32 accept_time = 6;			// 接受时间
	bytes extend = 7;
}

// 用户限时任务详情
message UserTLMissionInfo {
	uint32 mission_id = 1;
	string name = 2;						// 名称
	string sub_name = 3;					// 子名称（副标题）
	string strategy = 4;					// 攻略
	uint32 exp = 5;							// 经验奖励
	uint32 red_diamond = 6;					// 红钻奖励
	uint32 required_count = 7;				// 需要次数
	uint32 show_time = 8;					// 任务可见的起始时间
	uint32 effect_begin = 9;				// 任务生效起始时间
	uint32 effect_end = 10;					// 任务生效结束时间
	uint32 accepted_count = 11;				// 已接受任务的人数
	TLMissionAcceptLimit accept_limit = 12;				// 接受任务的人数限制
	UserTLMission user_mission = 13;		// 用户的限时任务信息
	bool is_hidden = 14;			// 是否隐藏（下架，只有可领奖的用户可见）
	uint64 ly_game_id = 15;					// 联运游戏ID
}

// 获取用户的限时任务列表
message GetUserTimeLimitMissionListReq {
	uint32 uid = 1;
	uint32 platform = 2;
}

message GetUserTimeLimitMissionListResp {
	repeated UserTLMissionInfo mission_list = 1;
}

message GetUserTimeLimitMissionByIdReq {
	uint32 uid = 1;
	uint32 platform = 2;
	uint32 mission_id = 3;
}

message GetUserTimeLimitMissionByIdResp {
	UserTLMissionInfo user_mission = 1;
}

// 通知限时任务事件
message HandleEventReq {
	uint32 uid = 1;
	uint32 event_type = 2;	// EVENT_TYPE
	uint64 ly_game_id = 3;	// 游戏id
	uint32 recharge_penny = 4;	//总充值金额，单位：分
	uint32 level = 5;		// 等级（游戏、欢城、tt）
	uint32 cash_recharge_penny = 6;	// 现金充值金额，单位：分
}

message ResetInProgressMissionReq {
	uint32 uid = 1;
	uint32 mission_id = 2;
	uint32 event_type = 3;
}

message AcceptTimeLimitMissionReq {
	uint32 uid = 1;
	uint32 mission_id = 2;
	uint32 platform = 3;
}

message CollectBonusReq {
	uint32 uid = 1;
	uint32 mission_id = 2;
}

message IncreaseMissionFinishCountReq {
	uint32 uid = 1;
	uint32 mission_id = 2;
	uint32 finish_count =  3;
}

service MissionTimeLimit {

	option( tlvpickle.Magic ) = 15590;		// 服务监听端口号

	rpc GetTimeLimitMissionList( GetTimeLimitMissionListReq ) returns( GetTimeLimitMissionListResp ) {
		option( tlvpickle.CmdID ) = 1;			// 命令号
        option( tlvpickle.OptString ) = "t:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <event_type>";	// 测试工具的命令号帮助
	}
	
	rpc GetTimeLimitMissionById( GetTimeLimitMissionByIdReq ) returns( GetTimeLimitMissionByIdResp ) {
		option( tlvpickle.CmdID ) = 2;			// 命令号
        option( tlvpickle.OptString ) = "m:t:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-m <mission_id> -t <is_force>";	// 测试工具的命令号帮助
	}
	
	rpc AddTimeLimitMission( AddTimeLimitMissionReq ) returns( AddTimeLimitMissionResp ) {
		option( tlvpickle.CmdID ) = 3;			// 命令号
        option( tlvpickle.OptString ) = "";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助
	}
	
	rpc GetTimeLimitMissionConfigUpdateTime( GetTimeLimitMissionConfigUpdateTimeReq ) returns( GetTimeLimitMissionConfigUpdateTimeResp ) {
		option( tlvpickle.CmdID ) = 4;			// 命令号
        option( tlvpickle.OptString ) = "";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助
	}
	
	rpc UpdateTimeLimitMission( UpdateTimeLimitMissionReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 5;			// 命令号
        option( tlvpickle.OptString ) = "m:e:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-m <mission_id> -e <effect_end>";	// 测试工具的命令号帮助
	}
		
	rpc GetUserTimeLimitMissionList( GetUserTimeLimitMissionListReq ) returns( GetUserTimeLimitMissionListResp ) {
		option( tlvpickle.CmdID ) = 6;			// 命令号
        option( tlvpickle.OptString ) = "u:p:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> [-p <platform 1.android 2.ios>]";	// 测试工具的命令号帮助
	}
	
	rpc GetUserTimeLimitMissionById( GetUserTimeLimitMissionByIdReq ) returns( GetUserTimeLimitMissionByIdResp ) {
		option( tlvpickle.CmdID ) = 7;			// 命令号
        option( tlvpickle.OptString ) = "u:p:m:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -m <mission_id> [-p <platform 1.android 2.ios>]";	// 测试工具的命令号帮助
	}
	
	rpc HandleEvent( HandleEventReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 9;					// 命令号
        option( tlvpickle.OptString ) = "u:t:g:n:m:l:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -t <event_type> [-g <game_id> -n <recharge_penny> -m <cash_recharge_penny> -l <level>]";	// 测试工具的命令号帮助
    }
	
	rpc ResetInProgressMission( ResetInProgressMissionReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 10;					// 命令号
        option( tlvpickle.OptString ) = "u:m:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> [-m <mission_id>]";	// 测试工具的命令号帮助
    }
	
	rpc AcceptTimeLimitMission( AcceptTimeLimitMissionReq ) returns ( GetUserTimeLimitMissionByIdResp ) {
        option( tlvpickle.CmdID ) = 11;					// 命令号
        option( tlvpickle.OptString ) = "u:m:p:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -m <mission_id> [-p <platform 1.android 2.ios>]";	// 测试工具的命令号帮助
    }
	
	rpc CollectBonus( CollectBonusReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 12;					// 命令号
        option( tlvpickle.OptString ) = "u:m:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -m <mission_id> ";	// 测试工具的命令号帮助
    }
	
	rpc DelTimeLimitMission( DelTimeLimitMissionReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 13;					// 命令号
        option( tlvpickle.OptString ) = "m:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-m <mission_id> ";	// 测试工具的命令号帮助
    }
	
	rpc IncreaseMissionFinishCount( IncreaseMissionFinishCountReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 14;					// 命令号
        option( tlvpickle.OptString ) = "m:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -m <mission_id> -n <finish_count>";	// 测试工具的命令号帮助
    }
}