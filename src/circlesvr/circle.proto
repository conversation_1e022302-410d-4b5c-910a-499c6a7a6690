syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package Circle;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list

enum CIRCLE_TYPE {
    GAME_CIRCLE = 1;		// 特定游戏的游戏圈
    CUSTOM_CIRCLE = 2;		// 自定义圈, 不关联特定游戏
}

enum TOPIC_TYPE {
    USER_TOPIC = 0;           // 用户主题
    OFFICIAL_TOPIC = 1;		// 官方旧主题
    OFFICIAL_NEW_TOPIC = 2;		// 官方新主题, 1.5版启用
}

enum TOPIC_SORT_TYPE {
	BY_NEWTON = 1;			//热贴列表
	BY_CREATETIME = 2;     //按创建时间降序列表
	BY_PLAYPARNER = 3;		//玩伴贴子表
	BY_LASTTIME = 4;        //最后回复时间降序表
	BY_HIGHLIGHT = 5;      //精华贴子表
	BY_ACTIVITY = 6;      //活动置顶贴子表
	BY_OFFICIAL = 7;		//官主主题列表
    BY_CREATETIME_OPT = 8;
    BY_COMMENTTIME_OPT = 9;
}

enum CIRCLE_OWNER_TYPE {
	OWNER = 1;			// 圈主
	VICE_OWNER = 2;		// 副圈主
}

enum AUDIT_TAGS {
	AUDIT_ALREADY		= 1;	//是否已审核
	AUDIT_PASS			= 2;	//是否通过
	AUDIT_WEB_REPORT	= 4;	//是否从web举报
	AUDIT_TT_DEL		= 8;	//是否从TT删除
	AUDIT_WEB_DEL		= 16;	//是否从web删除
	AUDIT_TT_REPORT		= 32;	//是否从TT举报
}

enum AUDIT_TOPIC_TYPE {
	AUDIT_TOPIC_NONE		= 0;	//未分类
	AUDIT_TOPIC_KEFU		= 1;	//客服类问题
	AUDIT_TOPIC_NOT_KEFU	= 2;	//非客服类问题
}

enum CIRCLE_ID {
    ANNOUNCEMENT_CIRCLE_ID = 100000;		// 公告圈ID
}

/**
 *	圈子成员
 */
message StCircleMember {
	required uint32 uid = 1;
}

/**
 *	圈子
 */
message StCircle {
	required uint32 circle_id = 1;			// 圈子id
	required uint32 type = 2;				// 圈子类型, see CIRCLE_TYPE
	required uint32 game_id = 3;			// 游戏id
	required string name = 4;				// 圈子名
	required string icon = 5;				// 圈子图标
	required uint32 member_count = 6;		// 成员数量
	required uint32 today_topic_count = 7;	// 当日主题数
	required uint32 rank = 8;				// 排序等级
	optional uint32 parner_count = 9;		//玩伴关注数
	optional uint32 owner = 10;             // 圈主
	optional bool is_follow = 11;
	repeated uint32 viceowner = 12;         // 副圈主
	optional uint32 topic_count = 13;		//主题数
}

/**
 *	主题
 */
 // 主题基本信息
message StTopicBase {

    enum TOPIC_STATUS {
        NORMAL = 0;			// 正常
        DELETED = 1;		// 主题被删除
    }

	required uint32 topic_id = 1;		// 主题id
	required uint32 circle_id = 2;		// 圈子id
	required uint32 topic_type = 3;		// 主题类型, see TOPIC_TYPE
	required string title = 4;			// 标题
	required uint32 creator = 5;		// 发布者uid
	required uint32 create_time = 6;	// 发布时间
	required uint32 like_count = 7;		// 点赞数
	required uint32 comment_count = 8;	// 评论数
    required uint32 msg_seq_id = 9;     // 发消息者timeline中的seqId
    required uint32 status = 10;        // 状态, see TOPIC_STATUS
	repeated string img_list = 11;		// 图片列表
	optional uint32 offical_report_count = 12;		// 被官方举报次数
	optional uint32 tag = 13;			// 帖子状态
	optional uint32 last_time = 14;     // 最后评论时间
	optional string device_id = 15;		// 发布者device id
}

// 主题简要信息
message StTopicBrief {
	required StTopicBase base = 1;			// 主题基本部分
	required string	content_preview = 2;	// 内容预览
}

// 主题详细信息
message StTopicDetail {
	required StTopicBase base = 1;			// 主题基本部分
	required string content = 2;			// 完整内容
}

/**
 *	评论
 */
message StComment {
	enum COMMENT_STATUS {
        NORMAL = 0;			// 评论正常
        DELETED = 1;		// 评论被删除
	}
	
	required uint32 comment_id = 1;     // 评论id
	required uint32 circle_id = 2;      // 圈子id
	required uint32 topic_id = 3;       // 主题id
	required string content = 4;        // 评论内容
	required uint32 creator = 5;        // 评论者
	required uint32 create_time = 6;    // 评论时间
	required uint32 status = 7;         // 评论状态
	required uint32 msg_seq_id = 8;     // 发消息者timeline中的seqId
	required uint32 ref_comment_id = 9; // 该评论回复的目标评论id
	required uint32 like_count = 10;    // 点赞数
	optional uint32 offical_report = 11;// 官方举报
	repeated string img_list = 12;		// 评论图片
	optional int32 floor     = 13;      // 楼层 默认-1为老数据没有楼层概念, 0为回复评论也没有楼层概念 
	optional uint32 parent_comment_id = 14; // 该评论回复的祖先目标评论ID 0默认表示没有
	optional uint32 comment_type      = 15; // see ga::CircleTopicCommentType
	optional string device_id = 16;		// 评论者device id
}


message StCommentWithRef {
    required StComment comment = 1;
    optional StComment ref_comment = 2; // 被引用的评论
}

/**
 *	Request & Response
 */
// 创建一个游戏圈子
message CreateGameCircleReq {
    required uint32 game_id = 1;
    required string name = 2;
    required string icon = 3;
}

message CreateGameCircleResp {
    required uint32 circle_id = 1;
}

message GetCircleReq {
    required uint32 circle_id = 1;
}

message CreateNonGameCircleReq {
    required string name = 1;
    required string icon = 2;
}

// 根据游戏id获取圈子
message GetCircleByGameIdReq{
	required uint32 game_id = 1;
}


message CreateNonGameCircleResp {
    required uint32 circle_id = 1;
}

// 推荐圈子
message GetRecommendCirclesReq {
	required uint32 count = 1;	// 数量
	optional uint32 type = 2;	//圈子类型，0表示不限制
}

message GetRecommendCirclesResp {
	repeated StCircle circle_list = 1;	// 圈子列表
}

// 获取有对应游戏的圈子
message GetGameCircleListReq{
	required uint32 offset = 1;
	required uint32 limit = 2;
}

message GetGameCircleListResp{
	repeated StCircle circle_list = 1;
}

// 批量根据游戏id获取圈子
message BatGetCircleListByGameIdListReq{
	repeated uint32 game_id_list = 1;	//	游戏id列表
	optional uint32 guild_id = 2;   //取玩伴关注数
}
message BatGetCircleListByGameIdListResp{
	repeated StCircle circle_list = 1;	// 圈子列表
}

// 用户进驻的圈子
message GetUserCirclesReq {
	optional bool bymyorder = 1;
}

message GetUserCirclesResp {
	repeated StCircle circle_list = 1;
}

// 进驻圈子, 支持批量
message JoinCirclesReq {
    repeated uint32 circle_id_list = 1;
    optional uint32 guild_id = 2;
}

message JoinCirclesResp {
	repeated uint32 success_circle_list = 1;
	repeated uint32 failed_circle_list = 2;
}

// 退出圈子
message QuitCircleReq {
    required uint32 circle_id = 1;
    optional uint32 guild_id = 2;
}

message QuitCircleResp {
}

// 发表主题
message CreateTopicReq {
	required uint32 circle_id = 1;		// 圈子id
	required uint32 topic_type = 2;		// 主题类型, see TOPIC_TYPE
	required string title = 3;			// 标题
	required string content = 4;		// 主题内容
    required uint32 msg_seq_id = 5;     // 发消息者timeline中的seqId
	repeated string img_list = 6;		// 图片列表(may be full url)
	optional uint32 guild_id = 7;
	optional string user_name = 8;		//用户账号名称
	optional string device_id = 9;		//用户设备id
}

message CreateTopicResp {
	required uint32 topic_id = 1;           // 主题id
    optional uint32 remain_cooldown = 2;    // 发生ERR_CIRCLE_POST_TOPIC_COOLINGDOWN错误时的剩余冷却时间
}

// 删除主题
message DeleteTopicReq {
	required uint32 circle_id = 1;
	required uint32 topic_id = 2;
}

message DeleteTopicResp {
}

// 发表评论
message CreateTopicCommentReq {
	required uint32 circle_id = 1;		// 圈子id
	required uint32 topic_id = 2;		// 主题id
	required string content = 3;		// 评论内容
	required uint32 msg_seq_id = 4;		// 发消息者timeline中的seqId
	optional uint32 ref_comment_id = 5;	// 引用的评论id
	optional uint32 user_type = 6; 		//用户类型
	optional string user_name = 7;		//用户账号名称
	repeated string img_list = 8;		// 图片列表(may be full url)
	optional uint32 anti_ad_flag = 9;   // 反广告标识 0x01表示不参与自动反广告 其他标识待定
	optional string device_id = 10;		// 用户设备id
}

message CreateTopicCommentResp {
	required uint32 comment_id = 1;         // 评论id
    optional uint32 remain_cooldown = 2;    // 发生ERR_CIRCLE_POST_COMMENT_COOLINGDOWN错误时的剩余冷却时间
}

// 删除评论
message DeleteTopicCommentReq {
	required uint32 circle_id = 1;		// 圈子id
	required uint32 topic_id = 2;		// 主题id
	required uint32 comment_id = 3;		// 评论id
}

message DeleteTopicCommentResp {
}

// 点赞
message LikeReq {
	required uint32 circle_id = 1;		// 圈子id
	required uint32 topic_id = 2;		// 主题id
	optional uint32 comment_id = 3;		// 评论id
}

message LikeResp {
    required uint32 is_first_time_like = 1; // 是否首次赞该主题/评论
}

// 取消赞
message CancelLikeReq {
	required uint32 circle_id = 1;		// 圈子id
	required uint32 topic_id = 2;		// 主题id
	optional uint32 comment_id = 3;		// 评论id
}

message CancelLikeResp {

}

// 获取主题列表
message GetTopicsReq {
	required uint32 circle_id = 1;		// 圈子id
	required uint32 start_topic_id = 2;	// 起始主题id, 若从头查起, 则传0
	required uint32 count = 3;			// 数量
	optional uint32 sorttype = 4;       // TOPIC_SORT_TYPE
	optional uint32 userfrom = 5;		// 调用来源
    optional uint32 client_type = 6;    // see protodef
}

message GetTopicsResp {
	repeated StTopicBrief topic_brief_list = 1;		// 查列表只会返回brief
    repeated CircleImageAttr image_attr_list = 2;   // 相关主题图片的属性
}

// 查询主题详情
message GetTopicDetailReq {
	required uint32 circle_id = 1;		// 圈子id
	required uint32 topic_id = 2;		// 主题id
}

message GetTopicDetailResp {
	required StTopicDetail topic_detail = 1;	  // 主题详情
    repeated CircleImageAttr image_attr_list = 2;   // 相关主题图片的属性
}

// 获取主题的点赞列表
message GetTopicLikersReq {
	required uint32 circle_id = 1;
	required uint32 topic_id = 2;
	required uint32 offset = 3;
	required uint32 limit = 4;
}

message GetTopicLikersResp {
	repeated uint32 uid_list = 1;
}

// 获取某主题中所有被引用评论的作者列表
message GetTopicReferencedCommentCreatorsReq {
	required uint32 circle_id = 1;
	required uint32 topic_id = 2;
}

message GetTopicReferencedCommentCreatorsResp {
	repeated uint32 uid_list = 1;
}

// 查询评论列表(该接口已淘汰)
message GetTopicCommentsReq {
	required uint32 circle_id = 1;			// 圈子id
	required uint32 topic_id = 2;			// 主题id
	required uint32 start_comment_id = 3;	// 起始评论id, 若从头查起, 则传0
	required uint32 count = 4;				// 数量
	optional bool order_desc = 5;
}

message GetTopicCommentsResp {
	repeated StCommentWithRef comment_list = 1;	// 评论列表
}

// 获取单个评论的信息 (该接口已淘汰)
message GetCommentDetailReq {
    required uint32 circle_id = 1;      // 圈子id
    required uint32 topic_id = 2;       // 主题id
    required uint32 comment_id = 3;     // 评论id
}

message GetCommentDetailResp {
    required StCommentWithRef comment = 1;
}

message CheckLikeReq {
    required uint32 circle_id = 1;      // 圈子id
    required uint32 topic_id = 2;       // 主题id
    required uint32 comment_id = 3;     // 评论id
}

enum LIKE_STATUS {
    LIKED = 1;              // 赞过且没取消
    LIKED_BUT_CANCELED = 2; // 赞过但取消了
    NOT_LIKED = 3;          // 没赞过
}

enum CIRCLE_TOPIC_TAG {
    HIGHLIGHT = 1;
    TOP = 2;
    ACTIVITY = 4;
    GAME_DOWNLOAD = 8;
    SHIELD_ON_ANDROID = 16;
    SHIELD_ON_IOS = 32;
}

message CheckLikeResp {

    required uint32 like_status = 1;
}

// 批量查询同一圈子下多个主题的点赞状态
message CheckCircleTopicsLikeReq {
    required uint32 circle_id = 1;
    repeated uint32 topic_id_list = 2;
}

message CheckCircleTopicsLikeResp {
    repeated uint32 like_status_list = 1;
}

// 举报某条主题
message ReportTopicReq {
	required uint32 circle_id = 1;
	required uint32 topic_id = 2;
}

message ReportTopicResp {
}

// 判断帐号是否官方帐号
message CheckOfficalUidReq {
}

message CheckOfficalUidResp {
	required bool yes = 1;
}

// 获取一个圈子下, 指定区间中被删除的主题id列表
message GetDeletedTopicIdListReq {
	required uint32 circle_id = 1;		// 圈子id
	required uint32 min_topic_id = 2;	// 区间最小
	required uint32 max_topic_id = 3;	// 区间最大
}

message GetDeletedTopicIdListResp {
	repeated uint32 deleted_topic_id_list = 3;	// 主题id列表
}

// 查用户游戏圈权限
message GetUserCirclePermissionReq {
}

message GetUserCirclePermissionResp {
	required uint32 permission = 1;
}

// 设置游戏权限
message SetUserCircleMuteReq {
	required uint32 circle_id = 1;
	required uint32 how_long = 2;
}

message SetUserCircleMuteResp {
}

// 获取官方主题列表
message GetOfficialTopicsReq {
	required uint32 circle_id = 1;		// 圈子id
	required uint32 start_topic_id = 2;	// 起始主题id, 若从头查起, 则传0
	required uint32 count = 3;			// 数量
	required bool ignore_deleted = 4;	// 是否忽略已删除的主题
}

message GetOfficialTopicsResp {
	repeated StTopicBrief topic_brief_list = 1;		// 查列表只会返回brief
}

message UpdateTopicReq {
	required uint32 circle_id = 1;
	required uint32 topic_id = 2;
	optional string title = 3;
	optional string content = 4;
	optional uint32 status = 5;
	repeated string img_list = 6;
}

message UpdateTopicResp {
}

message StTopicDisplayOrder {
	required uint32 topic_id = 1;
	required uint32 display_order = 2;
}

// 更新主题排序(批量支持)
message UpdateTopicDisplayOrderReq {
	required uint32 circle_id = 1;		// 圈子ID
	repeated StTopicDisplayOrder topic_display_order_list = 2;	// 排序列表
}

message UpdateTopicDisplayOrderResp {
}

// 查圈主
message GetCircleOwnerReq {
	required uint32 circle_id = 1;
}

message GetCircleOwnerResp {
	required uint32 uid = 1;
	required uint32 permission = 2;
}

// 设置圈主
message SetCircleOwnerReq {
	required uint32 circle_id = 1;
	required uint32 permission = 2;
}

message SetCircleOwnerResp {
}

// 删除圈主
message DeleteCircleOwnerReq {
	required uint32 circle_id = 1;
}

message DeleteCircleOwnerResp {
}

message CirclePermission {
	required uint32 circle_id = 1;
	required uint32 permission = 2;
}

// 查用户的游戏圈权限列表(circle_id = 0表示所有圈子， !=0 表示是某个圈子的圈主)
message GetUserCirclePermissionListReq {
}

message GetUserCirclePermissionListResp {
	repeated CirclePermission permission_list = 1;
}

message OfficalReportCommentsReq {
	required uint32 circle_id = 1;
	required uint32 topic_id = 2;
	required uint32 comment_id = 3;
}

message OfficalReportCommentsResp {
}

message OfficalReportUserCircleAllTopicReq {
	required uint32 circle_id = 1;
}

message OfficalReportUserCircleAllTopicResp {
}

message OfficalReportUserCircleAllCommentReq {
	required uint32 circle_id = 1;
}

message OfficalReportUserCircleAllCommentResp {
}

message CheckUserCircleMuteReq {
	required uint32 circle_id = 1;
}

message CheckUserCircleMuteResp {
	required bool is_mute = 1;
}

message AddCircleTopicTagReq {
	required uint32 circle_id = 1;
	required uint32 topic_id = 2;
	required uint32 tag = 3;
}

message AddCircleTopicTagResp {
}

message DelCircleTopicTagReq {
	required uint32 circle_id = 1;
	required uint32 topic_id = 2;
	required uint32 tag = 3;
}

message DelCircleTopicTagResp {
}

message GetCircleMuteUsersReq {
}

message CircleMuteUser {
	required uint32 uid = 1;
	required uint32 circle_id = 2;
	required uint32 forever_mute = 3;
	required uint32 mute_end_time = 4;
}

message GetCircleMuteUsersResp {
	repeated CircleMuteUser user_list = 1;
}

//
message GetCircleMemberListReq {
	required uint32 circle_id = 1;
	required uint32 offset = 2;
	required uint32 limit = 3;
}

message GetCircleMemberListResp {
	repeated StCircleMember mem_list = 1;
}

message CircleUserCoolList {
	required uint32 circle_id = 1;
	repeated uint32 topic_id = 2;
}

message CircleNewTopicCache {
	required uint32 topic_id = 1;
	required uint32 timer = 2;
}

message CircleUpdateInfo{
	required uint32 circle_id = 1;
	required uint32 newest_topic_id = 2;
}

message GetCircleNewTopicInfoReq{
	repeated CircleUpdateInfo updateinfo = 1;
}

message GetCircleNewTopicInfoRsp{
	repeated CircleUpdateInfo newupdateinfo = 1;
	optional uint32 newest_circle_id = 2;
}

message SetMyCircleOrderReq{
	repeated uint32 new_circle_id_list = 1;
}

message SetMyCircleOrderRsp{
}

// 设置副圈主
message SetCircleViceOwnerReq {
	required uint32 circle_id = 1;
	required uint32 uid = 2;
	required uint32 permission = 3;
}

message SetCircleViceOwnerResp {
}

// 查副圈主
message GetCircleViceOwnerReq {
	required uint32 circle_id = 1;
}

message CircleOwner {
	required uint32 uid = 1;
	required uint32 type = 2;		// 1:圈主   2:副圈主
	required uint32 permission = 3;
}

message GetCircleViceOwnerResp {
	repeated CircleOwner owner_list = 1;
}

// 删除副圈主
message DelCircleViceOwnerReq {
	required uint32 circle_id = 1;
	required uint32 uid = 2;
}

message DelCircleViceOwnerResp {
}

message GetCirclesByIdListReq {
	repeated uint32 circle_id_list = 1;
}

message GetCirclesByIdListResp {
	repeated StCircle circle_list = 1;
}

message SearchCircleByNameReq {
	required string key_word = 1;
}

message SearchCircleByNameResp {
	repeated StCircle circle_list = 1;
}

message CircleImageAttr {
    required string key = 1;
    required uint32 width = 2;
    required uint32 height = 3;
    optional string format = 4;
    optional string color_model = 5;
    optional uint32 frame_number = 6;
}

message ModifyCircleNameReq{
    required uint32 circle_id = 1;
    required string name = 2;
}

message ModifyCircleIconReq{
    required uint32 circle_id = 1;
    required string icon = 2;
}

message SetTopCircleRankReq
{
    repeated uint32 circle_id_list = 1;
}

message UpdateCircleTopicSortIdReq
{
    required uint32 circle_id = 1;
}

message GetCircleTopicCountReq
{
    required uint32 circle_id = 1;
}

message GetCircleTopicCountResp
{
    required uint32 topic_count = 1;
}

message ModifyExtraMemberCountReq
{
    required uint32 game_id = 1;
    required uint32 extra_member_count = 2;
}

message ModifyExtraMemberCountResp
{
}

message GetExtraMemberCountsReq
{
    repeated uint32 game_id_list = 1;
}

message ExtraMemberCountInfo
{
    required uint32 game_id = 1;
    required uint32 extra_member_count = 2;
}

message GetExtraMemberCountsResp
{
    repeated ExtraMemberCountInfo member_count_list =1;
}

//审核圈子主题
message AuditCircleTopicReq
{
	required uint32 topic_id = 1;
    required uint32 circle_id = 2;
	required uint32 audit_flag = 3;
	required string audit_user_name = 4;
	required string audit_annotation = 5;
}

//审核圈子评论
message AuditCircleTopicCommentReq
{
	required uint32 comment_id = 1;
	required uint32 topic_id = 2;
    required uint32 circle_id = 3;
	required uint32 audit_flag = 4;
	required string audit_user_name = 5;
	required string audit_annotation = 6;
}

//根据圈子类型查询圈子
message GetCirclesByTypeReq {
	required uint32 type = 1;
	required uint32 offset = 2;
	required uint32 limit = 3;
}

message GetCirclesByTypeResp {
	repeated StCircle circle_list = 1;	// 圈子列表
}

//获取推荐的主题列表
message GetRecommentTopicListReq {
	required uint32 circle_id = 1;
	required uint32 limit = 2;
}

message GetRecommentTopicListResp {
	repeated StTopicDetail topic_list = 1;	// 主题列表
}

message GetCircleTopicByTimeReq
{
	required uint32 circle_id = 1;
	required uint32 s_time = 2;
	required uint32 e_time = 3;
}


message CircleTopicPartInfo
{
	required uint32 creator = 1;
	required uint32 create_time = 2;
	required uint32 like_count = 3;
	required uint32 comment_count = 4;
	required string title = 5;
	required string content = 6;
	repeated string img_list = 7;
	optional uint32 topic_id = 8;
	optional string device_id = 9;
}

message GetCircleTopicByTimeResp
{
	repeated CircleTopicPartInfo topic_part_info = 1;
}

// 后台恢复主题
message AuditRestoreCircleTopicReq {
	required uint32 circle_id = 1;
	required uint32 topic_id = 2;
	required uint32 restore_flag = 3;	//需要恢复的标记位
	required string audit_user_name = 4;
	required string audit_annotation = 5;
}

message AuditRestoreTopicCommentReq{
	required uint32 circle_id = 1;
	required uint32 topic_id = 2;
    required uint32 comment_id = 3;
	required uint32 restore_flag = 4;
	optional string audit_user_name = 5;
	optional string audit_annotation = 6;
}
message AuditRestoreTopicCommentResp{
}


// 后台设置评论类型
message SetAuditTopicTypeReq {
	required uint32 circle_id = 1;
	required uint32 topic_id = 2;
	required uint32 audit_topic_type = 3;	//AUDIT_TOPIC_TYPE
}

// 内容审核后台创建评论
message CreateAuditTopicCommentReq {
	required uint32 circle_id = 1;
	required uint32 topic_id = 2;
	required string content = 3;	// 后台评论内容
	repeated string img_list = 4;		// 图片列表(may be full url)
	optional uint32 ref_comment_id =5;		// 引用的评论id
	optional uint32 user_type = 6; 		//用户类型
	optional string user_name = 7;		//用户账号名称
	optional string audit_user_name = 8;	//后台审核的账号
}


// 新版带楼层带回复列表的评论 2016-9-7 Add

message StSimpleCommentInfo {

	required uint32 comment_id = 1;     // 评论id
	required uint32 circle_id = 2;      // 圈子id
	required uint32 topic_id = 3;       // 主题id
	required uint32 creator = 4;        // 评论者
	required uint32 create_time = 5;    // 评论时间
	required uint32 status = 6;         // 评论状态( ga circle2 TopicCommentStatusV3 mask)
	required uint32 msg_seq_id = 7;     // 发消息者timeline中的seqId
	required uint32 ref_comment_id = 8; // 该评论回复的目标评论id
	optional uint32 offical_report = 9; // 官方举报
	required uint32 parent_comment_id = 10; // 该评论回复的祖先目标评论ID 0默认表示没有
	required uint32 comment_type      = 11; // see ga circle2 TopicCommentType
}

message StCommentWithSimpleRefInfo {
	required StComment comment = 1;
	optional StSimpleCommentInfo ref_comment_info = 2; // 如果该条评论是回复其他的评论 那么此处有值：回复的目标评论的信息
}

message StCommentWithReply {
	required StCommentWithSimpleRefInfo comment = 1;
	required uint32 reply_comment_total_cnt = 2;
	repeated StCommentWithSimpleRefInfo reply_comment_list = 3; // 如果该条评论被其他人回复过 此处有值 值为回复列表 默认为3条
}

// 获取评论列表 2016-9-7 Add
message GetCommentListReq
{
	required uint32 circle_id = 1;         // 圈子id
	required uint32 topic_id = 2;          // 主题id
	required uint32 start_comment_id = 3;  // 
	required uint32 count = 4;             // 
	required uint32 uid = 5;               // uid
	optional bool	include_start_id = 6;  // 是否包含 start_comment_id 本身，默认不包含
	optional bool	is_desc = 7;           // 是否降序排列即时间从新往旧排列 默认为否 
}

message GetCommentListResp
{
	repeated StCommentWithReply comment_list = 1;
	optional uint32 nomal_comment_left_cnt = 2;  // 从 start_comment_id 开始还剩余多少评论 即剩余多少楼层
}


// 获取指定评论的回复列表 2016-9-7 Add
message GetCommentReplyListReq
{
	required uint32 circle_id = 1;         // 圈子id
	required uint32 topic_id = 2;          // 主题id
	required uint32 parent_comment_id = 3; // 指定的评论
	required uint32 start_reply_comment_id = 4;  // 
	required uint32 count = 5;                   // 
	required uint32 uid = 6;                     // uid
}

message GetCommentReplyListResp
{
	repeated StCommentWithSimpleRefInfo reply_comment_list = 1;
	required uint32 reply_comment_total_cnt = 2;
	optional uint32 reply_comment_left_cnt = 3; // 从 start_reply_comment_id 开始还剩余多少回复
	
}

message ReportUserVisitCircleReq
{
	required uint32 uid = 1;
	required uint32 circle_id = 2;
}

message GetUserVisitCircleRecentlyReq
{
	required uint32 uid = 1;
	required uint32 start = 2;
	required uint32 limit = 3;
}
message GetUserVisitCircleRecentlyResp
{
	repeated StCircle circle_list = 1;
}

message AddHotCircleReq
{
	required uint32 circle_id = 1;
	optional uint32 weight = 2;
	required bool add = 3;
}

message GetHotCircleReq
{
	required uint32 start = 1;
	required uint32 limit = 2;
}

message GetHotCircleResp
{
	repeated StCircle hot_circle_list = 1;
	repeated uint32 weight_list = 2;
}

message GetMostTopicCircleReq
{
	required uint32 limit = 1;
}
message GetMostTopicCircleResp
{
	repeated StCircle mosttopic_circle_list = 1;
}

message GetTopicCountByTagReq{
	repeated uint32 circle_id = 1;
	required uint32 tag = 2;
}

message CircleTopicCount{
	required uint32 circle_id = 1;
	required uint32 count = 2;
}

message GetTopicCountByTagResp{
	repeated CircleTopicCount list = 1;
}

message TopicIDList {
	repeated uint32 topic_id = 1;
}

// Circle服务
service Circle {
	option( tlvpickle.Magic ) = 14980;		// 服务监听端口号

    rpc CreateGameCircle( CreateGameCircleReq ) returns( CreateGameCircleResp ) {
        option( tlvpickle.CmdID ) = 1;              // 命令号
        option( tlvpickle.OptString ) = "g:n:i:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id> -n <name> -i <icon>";    // 测试工具的命令号帮助
    }

	/**
	 *	获取推荐的圈子列表
	 */
	rpc GetRecommendCircles( GetRecommendCirclesReq ) returns( GetRecommendCirclesResp ) {
		option( tlvpickle.CmdID ) = 2;			// 命令号
        option( tlvpickle.OptString ) = "";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";			// 测试工具的命令号帮助
	}

	/**
	 *	获取用户已进驻的圈子列表
	 */
	rpc GetUserCircles( GetUserCirclesReq ) returns( GetUserCirclesResp ) {
		option( tlvpickle.CmdID ) = 3;			// 命令号
        option( tlvpickle.OptString ) = "u:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";	// 测试工具的命令号帮助
	}

	/**
	 *	用户进驻圈子
	 */
	rpc JoinCircles( JoinCirclesReq ) returns( JoinCirclesResp ) {
		option( tlvpickle.CmdID ) = 4;			// 命令号
        option( tlvpickle.OptString ) = "u:x:";	// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u <uid> -x <circle_id>";	// 测试工具的命令号帮助
	}

	/**
	 *	用户退出一个圈子
	 */
	rpc QuitCircle( QuitCircleReq ) returns( QuitCircleResp ) {
		option( tlvpickle.CmdID ) = 5;			// 命令号
        option( tlvpickle.OptString ) = "u:x:";	// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u <uid> -x <circle_id>";	// 测试工具的命令号帮助
	}

	/**
	 *	创建主题
	 */
	rpc CreateTopic( CreateTopicReq ) returns( CreateTopicResp ) {
		option( tlvpickle.CmdID ) = 6;			// 命令号
        option( tlvpickle.OptString ) = "u:x:t:e:s:p:";	// 测试工具的命令号参数， 注意最后的冒号
		option( tlvpickle.Usage ) = "-u <uid> -x <circle_id> -t <title> -e <content> -s <seq_id> -p <type>";	// 测试工具的命令号帮助
	}

	/**
	 *	删除主题
	 */
	rpc DeleteTopic( DeleteTopicReq ) returns( DeleteTopicResp ) {
		option( tlvpickle.CmdID ) = 7;			// 命令号
        option( tlvpickle.OptString ) = "x:t:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -t <topic_id>";		// 测试工具的命令号帮助
	}

	/**
	 *	创建评论
	 */
	rpc CreateTopicComment( CreateTopicCommentReq ) returns( CreateTopicCommentResp ) {
		option( tlvpickle.CmdID ) = 8;			// 命令号
        option( tlvpickle.OptString ) = "u:x:t:s:n:r:p";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -x <circle_id> -t <topic_id> -s <seq_id> -n <content> -r <ref_comment_id> -p <user_type>" ;	// 测试工具的命令号帮助
	}

	/**
	 *	删除评论
	 */
	rpc DeleteTopicComment( DeleteTopicCommentReq ) returns( DeleteTopicCommentResp ) {
		option( tlvpickle.CmdID ) = 9;			// 命令号
        option( tlvpickle.OptString ) = "x:t:n:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -t <topic_id> -n <comment_id>";		// 测试工具的命令号帮助
	}

	/**
	 * 	点了个赞
	 */
	rpc Like( LikeReq ) returns( LikeResp ) {
		option( tlvpickle.CmdID ) = 10;			// 命令号
        option( tlvpickle.OptString ) = "u:x:t:n:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -x <circle_id> -t <topic_id> -n <comment_id>";		// 测试工具的命令号帮助
	}

	/**
	 *	取消赞
	 */
	rpc CancelLike( CancelLikeReq ) returns( CancelLikeResp ) {
		option( tlvpickle.CmdID ) = 11;			// 命令号
        option( tlvpickle.OptString ) = "u:x:t:n:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -x <circle_id> -t <topic_id> -n <comment_id>";		// 测试工具的命令号帮助
	}

	/**
	 *	获取圈子的主题列表
	 */
	rpc GetTopics( GetTopicsReq ) returns( GetTopicsResp ) {
		option( tlvpickle.CmdID ) = 12;			// 命令号
        option( tlvpickle.OptString ) = "x:s:n:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -s <start_with> -n <count>";			// 测试工具的命令号帮助
	}

	/**
	 *	获取特定主题的详细信息
	 */
	rpc GetTopicDetail( GetTopicDetailReq ) returns( GetTopicDetailResp ) {
		option( tlvpickle.CmdID ) = 13;			// 命令号
        option( tlvpickle.OptString ) = "x:t:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -t <topic_id>";			// 测试工具的命令号帮助
	}

	/**
	 *	获取特定主题的评论列表 (该接口已淘汰)
	 */
	rpc GetTopicComments( GetTopicCommentsReq ) returns( GetTopicCommentsResp ) {
		option( tlvpickle.CmdID ) = 14;			// 命令号
        option( tlvpickle.OptString ) = "x:t:s:n:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -t <topic_id> -s <start_with> -n <count>";			// 测试工具的命令号帮助
	}

    /**
     *  根据圈子id查询圈子
     */
    rpc GetCircle( GetCircleReq ) returns( StCircle ) {
        option( tlvpickle.CmdID ) = 15;                 // 命令号
        option( tlvpickle.OptString ) = "x:n:";           // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id>";   // 测试工具的命令号帮助
    }

    /**
     *  单查评论详情 (该接口已淘汰)
     */
    rpc GetCommentDetail( GetCommentDetailReq ) returns( GetCommentDetailResp ) {
        option( tlvpickle.CmdID ) = 16;			// 命令号
        option( tlvpickle.OptString ) = "x:t:n:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -t <topic_id> -n <comment_id>";		// 测试工具的命令号帮助
    }

    rpc CheckLike( CheckLikeReq ) returns( CheckLikeResp ) {
        option( tlvpickle.CmdID ) = 17;			// 命令号
        option( tlvpickle.OptString ) = "u:x:t:n:";	// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -x <circle_id> -t <topic_id> -n <comment_id>";		// 测试工具的命令号帮助
    }

    rpc CheckCircleTopicsLike( CheckCircleTopicsLikeReq ) returns( CheckCircleTopicsLikeResp ) {
        option( tlvpickle.CmdID ) = 18;         // 命令号
        option( tlvpickle.OptString ) = "u:x:t:";     // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -x <circle_id> -t <topic_id_list>";         // 测试工具的命令号帮助
    }

    rpc GetTopicLikers( GetTopicLikersReq ) returns( GetTopicLikersResp ) {
    	option( tlvpickle.CmdID ) = 19;         // 命令号
        option( tlvpickle.OptString ) = "x:t:l:o:";     // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -t <topic_id> -o <offset> -l <limit>";
    }

    rpc GetTopicReferencedCommentCreators( GetTopicReferencedCommentCreatorsReq ) returns( GetTopicReferencedCommentCreatorsResp ) {
    	option( tlvpickle.CmdID ) = 20;         // 命令号
        option( tlvpickle.OptString ) = "x:t:";     // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -t <topic_id>";
    }

    rpc CreateNonGameCircle( CreateNonGameCircleReq ) returns( CreateNonGameCircleResp ) {
        option( tlvpickle.CmdID ) = 21;         // 命令号
        option( tlvpickle.OptString ) = "n:i:";     // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-n <circle_name> -i <icon>";
    }

    rpc ReportTopic( ReportTopicReq ) returns( ReportTopicResp ) {
        option( tlvpickle.CmdID ) = 22;         // 命令号
        option( tlvpickle.OptString ) = "u:c:t:";     // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -c <circle_id> -t <topic_id>";
    }

    rpc CheckOfficalUid( CheckOfficalUidReq ) returns( CheckOfficalUidResp ) {
        option( tlvpickle.CmdID ) = 23;         // 命令号
        option( tlvpickle.OptString ) = "u:";     // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc GetDeletedTopicIdList( GetDeletedTopicIdListReq ) returns( GetDeletedTopicIdListResp ) {
    	option( tlvpickle.CmdID ) = 24;				// 命令号
    	option( tlvpickle.OptString ) = "x:s:e:";	// 参数
    	option( tlvpickle.Usage ) = "-x <circle_id> -s <min_topic_id> -e <max_topic_id>";
    }

    rpc GetUserCirclePermission( GetUserCirclePermissionReq ) returns (GetUserCirclePermissionResp) {
    	option( tlvpickle.CmdID ) = 25;				// 命令号
    	option( tlvpickle.OptString ) = "u:";		// 参数
    	option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc SetUserCircleMute( SetUserCircleMuteReq ) returns (SetUserCircleMuteResp) {
    	option( tlvpickle.CmdID ) = 26;				// 命令号
    	option( tlvpickle.OptString ) = "u:c:l:";		// 参数
    	option( tlvpickle.Usage ) = "-u <uid> -c <circle_id> -l <how_long>";
    }

    rpc GetOfficialTopics( GetOfficialTopicsReq ) returns( GetOfficialTopicsResp ) {
		option( tlvpickle.CmdID ) = 27;					// 命令号
        option( tlvpickle.OptString ) = "x:s:n:i:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -s <start_with> -n <count> -i <ignore_deleted>";	// 测试工具的命令号帮助
	}

	rpc UpdateTopic( UpdateTopicReq ) returns( UpdateTopicResp ) {
		option( tlvpickle.CmdID ) = 28;				// 命令号
        option( tlvpickle.OptString ) = "x:t:i:e:s:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -t <topic_id> -i <title> -e <content> -s <status>";				// 测试工具的命令号帮助
	}

	rpc UpdateTopicDisplayOrder( UpdateTopicDisplayOrderReq ) returns( UpdateTopicDisplayOrderResp ) {
		option( tlvpickle.CmdID ) = 29;				// 命令号
        option( tlvpickle.OptString ) = "x:t:o:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -t <topic_id1,topic_id2,...> -o <display_order1,display_order2,...>";				// 测试工具的命令号帮助
	}



	rpc GetCircleOwner( GetCircleOwnerReq ) returns( GetCircleOwnerResp ) {
		option( tlvpickle.CmdID ) = 30;			// 命令号
        option( tlvpickle.OptString ) = "x:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id>";			// 测试工具的命令号帮助
	}

	rpc SetCircleOwner( SetCircleOwnerReq ) returns( SetCircleOwnerResp ) {
		option( tlvpickle.CmdID ) = 31;			// 命令号
        option( tlvpickle.OptString ) = "x:u:p:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -u <uid> -p <permission>";			// 测试工具的命令号帮助
	}

	rpc DeleteCircleOwner( DeleteCircleOwnerReq ) returns( DeleteCircleOwnerResp ) {
		option( tlvpickle.CmdID ) = 32;			// 命令号
        option( tlvpickle.OptString ) = "x:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id>";			// 测试工具的命令号帮助
	}

	rpc GetUserCirclePermissionList( GetUserCirclePermissionListReq ) returns( GetUserCirclePermissionListResp ) {
		option( tlvpickle.CmdID ) = 33;			// 命令号
        option( tlvpickle.OptString ) = "u:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";			// 测试工具的命令号帮助
	}

	rpc OfficalReportComments(OfficalReportCommentsReq) returns (OfficalReportCommentsResp) {
		option( tlvpickle.CmdID ) = 34;					// 命令号
        option( tlvpickle.OptString ) = "x:t:m:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -t <topic_id> -m <comment_id>";	// 测试工具的命令号帮助
	}

	rpc OfficalReportUserCircleAllTopic(OfficalReportUserCircleAllTopicReq) returns (OfficalReportUserCircleAllTopicResp) {
		option( tlvpickle.CmdID ) = 35;					// 命令号
        option( tlvpickle.OptString ) = "u:x:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -x <circle_id>";	// 测试工具的命令号帮助
	}

	rpc OfficalReportUserCircleAllComment(OfficalReportUserCircleAllCommentReq) returns(OfficalReportUserCircleAllCommentResp) {
		option( tlvpickle.CmdID ) = 36;					// 命令号
        option( tlvpickle.OptString ) = "u:x:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -x <circle_id>";	// 测试工具的命令号帮助
	}

	rpc CheckUserCircleMute( CheckUserCircleMuteReq ) returns ( CheckUserCircleMuteResp ) {
		option( tlvpickle.CmdID ) = 37;					// 命令号
        option( tlvpickle.OptString ) = "u:x:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -x <circle_id>";	// 测试工具的命令号帮助
	}

	rpc AddCircleTopicTag( AddCircleTopicTagReq ) returns ( AddCircleTopicTagResp ) {
		option( tlvpickle.CmdID ) = 38;					// 命令号
        option( tlvpickle.OptString ) = "x:t:g:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -t <topic_id> -g <tag>";	// 测试工具的命令号帮助
	}

	rpc DelCircleTopicTag( DelCircleTopicTagReq ) returns ( DelCircleTopicTagResp ) {
		option( tlvpickle.CmdID ) = 39;					// 命令号
        option( tlvpickle.OptString ) = "x:t:g:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -t <topic_id> -g <tag>";	// 测试工具的命令号帮助
	}

	rpc GetCircleMuteUsers( GetCircleMuteUsersReq ) returns ( GetCircleMuteUsersResp ) {
		option( tlvpickle.CmdID ) = 40;					// 命令号
        option( tlvpickle.OptString ) = "";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";	// 测试工具的命令号帮助
	}

	rpc BatGetCircleListByGameIdList( BatGetCircleListByGameIdListReq ) returns ( BatGetCircleListByGameIdListResp ){
		option( tlvpickle.CmdID ) = 41;					// 命令号
        option( tlvpickle.OptString ) = "g:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <gameId1,gameId2,gameId3>";	// 测试工具的命令号帮助
	}

    /**
    *	根据游戏id查询圈子
    */
    rpc GetCircleByGameId( GetCircleByGameIdReq ) returns ( StCircle ){
    	option( tlvpickle.CmdID ) = 42;                 // 命令号
        option( tlvpickle.OptString ) = "g:";           // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id>";
    }


    // 查询包含游戏的圈子列表
    rpc GetGameCircleList( GetGameCircleListReq ) returns ( GetGameCircleListResp ){
    	option( tlvpickle.CmdID ) = 43;                 // 命令号
        option( tlvpickle.OptString ) = "o:l:";           // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-o <offset> -l <limit>";
    }

    // 分页查询圈子的成员列表
    rpc GetCircleMemberList( GetCircleMemberListReq ) returns ( GetCircleMemberListResp ) {
    	option( tlvpickle.CmdID ) = 44;                 // 命令号
        option( tlvpickle.OptString ) = "x:o:l:";           // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -o <offset> -l <limit>";
    }

	/**
	 *	获取圈子变更信息
	 */
	rpc GetCircleNewTopicInfo( GetCircleNewTopicInfoReq ) returns( GetCircleNewTopicInfoRsp ) {
		option( tlvpickle.CmdID ) = 45;			// 命令号
        option( tlvpickle.OptString ) = "x:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> ";			// 测试工具的命令号帮助
	}

	/**
	 *	设置我的圈子顺序
	 */
	rpc SetMyCircleOrder( SetMyCircleOrderReq ) returns( SetMyCircleOrderRsp ) {
		option( tlvpickle.CmdID ) = 46;			// 命令号
        option( tlvpickle.OptString ) = "x:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> ";			// 测试工具的命令号帮助
	}

	/**
	 *	设置副圈主
	 */
	rpc SetCircleViceOwner( SetCircleViceOwnerReq ) returns( SetCircleViceOwnerResp ) {
		option( tlvpickle.CmdID ) = 47;			// 命令号
        option( tlvpickle.OptString ) = "x:u:p:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -u <uid> -p <permission>";			// 测试工具的命令号帮助
	}

	/**
	 *	查副圈主列表
	 */
	rpc GetCircleViceOwner( GetCircleViceOwnerReq ) returns( GetCircleViceOwnerResp ) {
		option( tlvpickle.CmdID ) = 48;			// 命令号
        option( tlvpickle.OptString ) = "x:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id>";			// 测试工具的命令号帮助
	}

	/**
	 *	查副圈主列表
	 */
	rpc DelCircleViceOwner( DelCircleViceOwnerReq ) returns( DelCircleViceOwnerResp ) {
		option( tlvpickle.CmdID ) = 49;			// 命令号
        option( tlvpickle.OptString ) = "x:u:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -u <uid>";			// 测试工具的命令号帮助
	}

	rpc GetCirclesByIdList( GetCirclesByIdListReq ) returns( GetCirclesByIdListResp ) {
		option( tlvpickle.CmdID ) = 50;						// 命令号
        option( tlvpickle.OptString ) = "x:";				// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id_list>";	// 测试工具的命令号帮助
	}

	rpc SearchCircleByName( SearchCircleByNameReq ) returns( SearchCircleByNameResp ) {
		option( tlvpickle.CmdID ) = 51;						// 命令号
        option( tlvpickle.OptString ) = "k:";				// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-k <key_words>";		// 测试工具的命令号帮助
	}

	rpc ModifyCircleName( ModifyCircleNameReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 52;						// 命令号
        option( tlvpickle.OptString ) = "n:";				// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-n <name>";		// 测试工具的命令号帮助
	}

	rpc ModifyCircleIcon( ModifyCircleIconReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 53;						// 命令号
        option( tlvpickle.OptString ) = "x:u:i:";			// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -u <uid> -i <icon>";	// 测试工具的命令号帮助
	}

	rpc SetTopCircleRank( SetTopCircleRankReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 54;						// 命令号
        option( tlvpickle.OptString ) = "l:";				// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-l <circle_id;circle_id;....>";		// 测试工具的命令号帮助
	}

    rpc UpdateCircleTopicSortId( UpdateCircleTopicSortIdReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 55;						// 命令号
        option( tlvpickle.OptString ) = "x:";				// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id>";		// 测试工具的命令号帮助
    }

    rpc TestCircleTopicOpt( tlvpickle.SKBuiltinEmpty_PB ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 56;					                    	// 命令号
        option( tlvpickle.OptString ) = "x:n:";				                    // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -n <count_to_test>";		// 测试工具的命令号帮助
    }

    rpc GetCircleTopicCount( GetCircleTopicCountReq ) returns( GetCircleTopicCountResp ) {
        option( tlvpickle.CmdID ) = 57;                     // 命令号
        option( tlvpickle.OptString ) = "x:";               // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id>";		// 测试工具的命令号帮助
    }
    rpc ModifyExtraMemberCount( ModifyExtraMemberCountReq ) returns( ModifyExtraMemberCountResp ) {
        option( tlvpickle.CmdID ) = 58;                     // 命令号
        option( tlvpickle.OptString ) = "g:t:";               // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id> -t <extra_member_count>";		// 测试工具的命令号帮助
    }
    rpc GetExtraMemberCounts(GetExtraMemberCountsReq) returns(GetExtraMemberCountsResp){
        option( tlvpickle.CmdID ) = 59;                     // 命令号
        option( tlvpickle.OptString ) = "g:";               // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id1,game_id2,game_id3,...>";		// 测试工具的命令号帮助
    }
	
	rpc AuditCircleTopic( AuditCircleTopicReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 60;					                    	// 命令号
        option( tlvpickle.OptString ) = "u:x:t:g:a:";				                    // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <audit_user_name> -x <circle_id> -t <topic_id> -g <audit_flag> -a <annotation>";		// 测试工具的命令号帮助
    }
	
	rpc AuditCircleTopicComment( AuditCircleTopicCommentReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 61;					                    	// 命令号
        option( tlvpickle.OptString ) = "u:x:t:n:g:a:";				                    // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <audit_user_name> -x <circle_id> -t <topic_id> -n <comment_id> -g <audit_flag> -a annotation";		// 测试工具的命令号帮助
    }
	
	rpc GetCirclesByType( GetCirclesByTypeReq ) returns( GetCirclesByTypeResp ) {
		option( tlvpickle.CmdID ) = 62;			// 命令号
        option( tlvpickle.OptString ) = "t:o:n:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-t <circle_type> -o <offset> -n <limit>";			// 测试工具的命令号帮助
	}
	
	rpc GetRecommentTopicList( GetRecommentTopicListReq ) returns( GetRecommentTopicListResp ) {
		option( tlvpickle.CmdID ) = 63;			// 命令号
        option( tlvpickle.OptString ) = "x:n:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -n <limit>";			// 测试工具的命令号帮助
	}


	rpc GetCircleTopicByTime( GetCircleTopicByTimeReq ) returns( GetCircleTopicByTimeResp ) {
		option( tlvpickle.CmdID ) = 64;			// 命令号
        option( tlvpickle.OptString ) = "";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";			// 测试工具的命令号帮助
	}
	
	rpc AuditRestoreCircleTopic( AuditRestoreCircleTopicReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 65;			// 命令号
        option( tlvpickle.OptString ) = "u:x:t:g:a:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <audit_user_name> -x <circle_id> -t <topic_id> -g <restore_flag> -a <annotation>";			// 测试工具的命令号帮助
	}
	
	rpc SetAuditTopicType( SetAuditTopicTypeReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 66;			// 命令号
        option( tlvpickle.OptString ) = "x:t:a:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-x <circle_id> -t <topic_id> -a <audit_comment_type>";			// 测试工具的命令号帮助
	}
	
	rpc CreateAuditTopicComment( CreateAuditTopicCommentReq ) returns( CreateTopicCommentResp ) {
		option( tlvpickle.CmdID ) = 67;			// 命令号
        option( tlvpickle.OptString ) = "u:x:t:a:r:";		// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -x <circle_id> -t <topic_id> -a <content> -r <ref_comment_id>";			// 测试工具的命令号帮助
	}
	
	
	// 新版的获取主题的评论列表
	rpc GetCommentList( GetCommentListReq ) returns( GetCommentListResp ) {
		option( tlvpickle.CmdID ) = 68;		
        option( tlvpickle.OptString ) = "u:x:t:g:a:";		
        option( tlvpickle.Usage ) = "-u <uid> -x <circle_id> -t <topic_id> -g <start comment id> -a <count>";
	}
	
	// 新版的获取主题内指定评论的回复列表
	rpc GetCommentReplyList( GetCommentReplyListReq ) returns( GetCommentReplyListResp ) {
		option( tlvpickle.CmdID ) = 69;		
        option( tlvpickle.OptString ) = "u:x:t:p:g:a:";		
        option( tlvpickle.Usage ) = "-u <uid> -x <circle_id> -t <topic_id> -p <parent comment id> -g <start reply comment id> -a <count>";
	}
	
	rpc GetUserVisitCircleRecently ( GetUserVisitCircleRecentlyReq ) returns( GetUserVisitCircleRecentlyResp ) {
		option( tlvpickle.CmdID ) = 70;		
        option( tlvpickle.OptString ) = "u:s:l:";		
        option( tlvpickle.Usage ) = "-u <uid> -s<start> -l<limit>";
	}
	
	rpc ReportUserVisitCircle ( ReportUserVisitCircleReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 71;		
        option( tlvpickle.OptString ) = "u:x:";
        option( tlvpickle.Usage ) = "-u <uid> -x<circle_id> ";
	}
	rpc AddHotCircle ( AddHotCircleReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
		option( tlvpickle.CmdID ) = 72;		
        option( tlvpickle.OptString ) = "x:w:";
        option( tlvpickle.Usage ) = "-x<circle_id> -w<weight>";
	}

	rpc GetHotCircle ( GetHotCircleReq ) returns( GetHotCircleResp ) {
		option( tlvpickle.CmdID ) = 73;		
        option( tlvpickle.OptString ) = "s:l:";
        option( tlvpickle.Usage ) = "-s<start> -l<limit> ";
	}
    
	rpc GetTopicCountByTag(GetTopicCountByTagReq) returns(GetTopicCountByTagResp) {
		option( tlvpickle.CmdID ) = 74;
        option( tlvpickle.OptString ) = "x:t:";
        option( tlvpickle.Usage ) = "-x<circle_id> -t<tags> ";
	}
    
    //后台 恢复 评论
    rpc AuditRestoreTopicComment ( AuditRestoreTopicCommentReq ) returns ( AuditRestoreTopicCommentResp ) {
        option( tlvpickle.CmdID ) = 75;
        option( tlvpickle.OptString ) = "x:t:m:r:n:s:";
        option( tlvpickle.Usage ) = "-x <circle id> -t <topic id> -m <comment id> -r <restore flag> [-n <user> -s <annotation>]";
    }

	rpc GetMostTopicCircle ( GetMostTopicCircleReq ) returns ( GetMostTopicCircleResp ) {
        option( tlvpickle.CmdID ) = 76;
        option( tlvpickle.OptString ) = "c:";
        option( tlvpickle.Usage ) = "-c<count>";
    }
	
}
