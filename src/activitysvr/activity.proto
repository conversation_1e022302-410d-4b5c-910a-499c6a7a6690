syntax="proto2";


package Activity;

//svrkit support
import "common/tlvpickle/skbuiltintype.proto";

///
/// 与appsvr后台接口
///


// offer category
message OfferCategoryInfo {
    required uint32 id 	  = 1;
    required string name 	= 2;
}

message OfferCategoryList {
    repeated OfferCategoryInfo list = 1;
}

enum ACTIVITY_STATUS{
    ACTIVITY_ST_TOOPEN      = 0x0001;
    ACTIVITY_ST_OPENING     = 0x0002;
    ACTIVITY_ST_OVER        = 0x0004;
    ACTIVITY_ST_ADMINSTOP   = 0x0008;
    ACTIVITY_ST_DELETE      = 0x0010;

    ACTIVITY_ST_ALL         = 0x001F; // =ACTIVITY_ST_TOOPEN | ACTIVITY_ST_OPENING | ACTIVITY_ST_OVER | ACTIVITY_ST_ADMINSTOP | ACTIVITY_ST_DELETE
}

//
message ActivityOfferInfo{
    required uint32 game_id         = 1 ;
    optional uint32 activity_id     = 2 ;
    required uint32 category_id     = 3 ;
    optional uint32 amount          = 4 ;
    optional string offer_desc      = 5;
    optional string offer_notes     = 6;
    optional string offer_appendix  = 7;
}


// 活动
message ActivityInfo {
    required uint32 activity_id 	= 1 ;
    optional string name 			     = 2 ;
    optional string description  = 3 ;
    optional string banner       = 4 ;
    optional int64 begin_time		  = 5 ;	// unix timestamp, 秒
    optional int64 end_time		    = 6 ;	// unix timestamp, 秒
    optional uint32 status 			   = 7 ;	// ACTIVITY_STATUS
    repeated ActivityOfferInfo offers_list  = 8 ;
}

message CreateActivityReq {
    required ActivityInfo activity  = 1;
}

message CreateActivityResp {
    required uint32 activity_id		= 1;
}

message UpdateActivityReq {
    required ActivityInfo activity  = 1;
}

message UpdateActivityResp {
    required uint32 activity_id		= 1;
}

message DeleteActivityReq {
    required uint32 activity_id 	= 1;
    repeated uint32 gameid_list  = 2;
}

message GetActivityListReq {
    optional uint32 status      = 1;     //ACTIVITY_STATUS
    optional uint32 from_index  = 2;
    optional uint32 count       = 3;
    optional bool reverse       = 4;	// true: 将按照beginTime进行逆序查询
    optional uint32 game_id     = 5;    //如果不存在，只返回ActivityInfo、无ActivityOfferInfo
                                        //如果存在，game_id = -1，返回所有ActivityOfferInfo
                                        //如果存在，game_id ！= -1，返回所有具体的ActivityOfferInfo
}

message GetActivityListResp {
    repeated ActivityInfo activity_list = 1;
}

//福利活动类型
enum OFFEREVENT_TYPE{
    OFFER_EVENT_TYPE_NULL = 0;  //无效值
    OFFER_EVENT_TYPE_GAME = 1;    //游戏 福利
    OFFER_EVENT_TYPE_GLOBAL = 2;  //全局/平台 福利，不绑定游戏

    OFFER_EVENT_TYPE_ALL = 3;
}

enum OFFEREVENT_STATUS{
    OFFER_EVENT_STATUS_OFFLINE = 0x01;    //不在线
    OFFER_EVENT_STATUS_ONLINE = 0x02;     //在线
    OFFER_EVENT_STATUS_ALL = 0x03; //OFFER_EVENT_STATUS_OFFLINE | OFFER_EVENT_STATUS_ONLINE
}

//福利活动
message OfferEventElem{
    optional uint32 id 	            = 1;
    optional string name            = 2;
    optional string description     = 3;
    optional string notes           = 4;
    optional int64 begin_time		= 5;
    optional int64 end_time		    = 6;
    optional int64 now_time         = 7;
    optional string banner          = 8;
    optional string detail_href     = 9;
    optional uint32 type 	        = 10;
    optional uint32 rank 	        = 11;
    optional uint32 game_id         = 12;
    optional uint32 status          = 13;

}

message CreateOfferEventReq{
    required OfferEventElem offer_event = 1;
}

message CreateOfferEventRsp{
    required uint32 id = 1;
}

message ModifyOfferEventReq{
    required OfferEventElem offer_event = 1;
}

message DelOfferEventReq{
    required uint32 id = 1;
}

message GetOfferEventReq{
    required uint32 status = 1;  //取值 OFFER_EVENT_STATUS_ALL
    required uint32 type = 2;   //OFFEREVENT_TYPE
    repeated uint32 gameid_list = 3;
    optional uint32 from_index  = 4;
    optional uint32 count       = 5;
}

message GetOfferEventRsp{
    repeated OfferEventElem event_list = 1;
}

//福利活动的游戏榜
message GameRankElem{
    required uint32 game_id = 1;
    required uint32 rank    = 2;
}

message AddGameRankReq{
    required GameRankElem gamerank = 1;
}

message ModifyGameRankReq{
    required GameRankElem gamerank = 1;
}

message DelGameRankReq{
    repeated uint32 gameid_list = 1;
}

message GetGameRankReq{
    optional uint32 from_index = 1;
    optional uint32 count      = 2;
}

message GetGameRankRsp{
    repeated GameRankElem gamerank_list = 1;
}


message GetGameOfferReq {
    required uint32 game_id = 1;
}

message GameOfferInfo{
    required uint32 game_id           = 1 ;
    required string activity_name     = 2 ;
    optional string activity_desc     = 3 ; //未呈现
    optional string offer_desc     = 4 ;   //优惠 描述
    optional string offer_notes    = 5 ;   //优惠 备注、要求
    optional uint32  offer_categoryid = 6 ;  // 优惠 类型：下载送代金券、自定义代金券、充值抽奖获取代金券
    //optional string activity_banner   = 4 ;
    //optional int64  begin_time		  = 5 ;	     // unix timestamp, 秒
    //optional int64  end_time		  = 6 ;	     // unix timestamp, 秒
    //optional int64  now_time		  = 7 ;
    //optional uint32  offer_category_id = 8 ;     // 福利活动类型
    //optional string offer_category_name   = 9 ;  //优惠种类
    //optional string offer_category_desc   = 10 ;
    //optional uint32 offer_amount   = 11 ;   //优惠 数量
    //optional string offer_desc     = 12 ;   //优惠 描述
    //optional string offer_notes    = 13 ;   //优惠 备注、要求
    //optional string offer_appendix = 14 ;   //优惠 补充说明
    //optional string bannel_optop   = 15;    //上级bannel
    //optional string bannel_inplace = 16;    //详情bannel
    //optional string detail_href    = 17;    //详情链接
}

message GetGameOfferRsp {
    repeated GameOfferInfo game_offer = 1 ;
}


/*-----------------*/
//First voucher
/*-----------------*/

message FirstVoucherAccountPassword{
	required string account = 1;
	required string password = 2;
}

message FirstVoucherProduct{
	required uint32 product_id = 1;
	required uint32 price = 2;
	required uint32 worth = 3;
	required uint32 game_id = 4;
	required uint64 expire_time = 5;
}

message GetFirstVoucherProductsReq {
    required uint32 index = 1;
    required uint32 limit = 2;
}

//**---each game return the last record---**/

message GetFirstVoucherProductsResp {
	repeated FirstVoucherProduct product_list = 1;
}

message AddFirstVoucherProductReq{
	required uint32 price = 1;
	required uint32 worth = 2;
	required uint32 game_id = 3;
	required uint64 expire_time = 4;
}

message AddFirstVoucherProductResp{
	required uint32 product_id = 1;
}

message ModifyFirstVoucherProductReq{
	required uint32 product_id = 1;
	optional uint32 invalid = 2;
	optional uint32 price = 3;
	optional uint32 worth = 4;
	optional uint64 expire_time = 5;
}


message GetGameFirstVoucherProductReq{
	required uint32 game_id = 1;
}

message GetGameFirstVoucherProductResp{
	required FirstVoucherProduct product = 1;
}

message DeleteFirstVoucherProductReq{
	required uint32 product_id = 1;
}

message FirstVoucherItem{
	required string item_hash	= 1;	// hash, 唯一标识
	required bytes item_binary 	= 2;	// 物品内容
}


message AddFirstVoucherItemReq{
	repeated FirstVoucherItem item_list = 1;
	required uint32 product_id 	= 2;	// 所属的商品ID
}

message AddFirstVoucherItemResp {
    repeated FirstVoucherItem success_product_item_list = 1;
    repeated FirstVoucherItem failed_product_item_list = 2;
}

message GetFirstVoucherItemReq{
	optional uint32 product_id = 1;
	optional uint32 status = 2;
}

message GetFirstVoucherItemResp{
	repeated FirstVoucherItem item_list = 1;
}

message DeleteFirstVoucherItemReq {
    required uint32 product_id = 1;
    repeated string item_hash_list = 2;
}


message FirstVoucherUserItem{
	required uint32 uid	= 1;	// uid
	required uint64 timestamp = 2;	// 获取时间
	required FirstVoucherProduct product_snapshot = 3;	// 商品快照
	required FirstVoucherItem item_snapshot	= 4;	// 物品快照
	required string order_id = 5;	// 订单号
}

message PurchaseFirstVoucherItemReq {
	required uint32 uid = 1;
	required uint32 product_id = 2;
}

message PurchaseFirstVoucherItemResp {
	required FirstVoucherUserItem first_voucher_item = 1;
}

message GetFirstVoucherUserItemReq{
	required uint32 uid = 1;
}

message GetFirstVoucherUserItemResp{
	repeated FirstVoucherUserItem user_item_list = 1;
}

message ActBannerInfo{
    required uint32 id          = 1;    //banner id
    required string action_url  = 2;    //跳转url
    optional string title       = 3;    //ios标题
    optional string sub_title   = 4;    //ios副标题
    optional string icon_url    = 5;    //ios图标链接
    optional string cover_url   = 6;    //android图片链接
    optional uint32 android_version = 7;//android最低生效版本
    optional uint32 ios_version = 8;    //ios最低生效版本
    optional uint32 rank = 9;           //排序
}

message UpdateActBannerReq{
    required ActBannerInfo banner = 1;
}

message GetActBannersResp{
    repeated ActBannerInfo banner_list = 1;
}

message DelActBannerReq{
    required uint32 id = 1;
}

message GetActBannerOpTimeResp{
    required uint32 op_time = 1;
}

message ActSplashScreenInfo{
    required uint32 id              = 1;
    required string screen_url      = 2;
    required string action_url      = 3;
    required string start_time      = 4;
    required string end_time        = 5;
    required uint32 continue_time   = 6;
	
	optional uint32 client_type     = 7; // ANDROID = 0,IOS = 1, SEE protodef.h CLIENT_TYPE
	optional uint32 app_id     = 8; 
	optional uint32 market_id  = 9; 
}

message UpdateActSplashScreenReq{
    required ActSplashScreenInfo splash_screen = 1;
}

message GetActSplashScreenResp{
    optional ActSplashScreenInfo splash_screen = 1;      // 废弃字段
    optional ActSplashScreenInfo splash_screen_ios = 2;  // 废弃字段 	
	
    repeated ActSplashScreenInfo splash_screen_list =3;  // 新的数据
}

message DelActSplashScreenReq{
    required uint32 id = 1;
}

message ActSplashScreenOpTime
{
	optional uint32 app_id      = 1;
	optional uint32 market_id   = 2;
	optional uint32 client_type = 3;
	optional uint32 op_time     = 4;
}

message GetActSplashScreenOpTimeResp{
    optional uint32 op_time = 1;       // optime_list里面 安卓最大的时间戳
    optional uint32 op_time_ios = 2;   // optime_list里面 IOS最大的时间戳 

    repeated ActSplashScreenOpTime optime_list =3;
}

message GetActSplashScreenV2Resp{
  repeated ActSplashScreenInfo splash_screen_list = 1;  // 新的数据
}

message GetGamePreorderResultReq{
    required uint32 game_id =1;
}

message GamePreorderResult{
    required uint32 obj_id = 1;
    required uint32 count = 2;
}

message GetGamePreorderResultResp{
    repeated GamePreorderResult result_list = 1;
}

message GetGamePreorderUserSpptReq{
    required uint32 game_id = 1;
    required uint32 uid = 2;
}

message GetGamePreorderUserSpptResp{
    required uint32 obj_id = 1;
    required uint32 count = 2;
}

message SetGamePreorderUserSpptReq{
    required uint32 game_id = 1;
    required uint32 uid = 2;
    required uint32 obj_id = 3;
    required uint32 count = 4;
}




/*-----------------*/
//QQCar race
/*-----------------*/


message InviterInfo {
	required uint32 uid = 1;
	required uint32 invite_num = 2;
}

message GetQQCarActivityRankResp {
	repeated InviterInfo invite_infos = 1;
}

message NotifyInviteRecordReq {
	required uint32 uid = 1;
	required uint32 inviter_uid = 2;
}

message GetPrizeInfoReq {
	required uint32 uid = 1;
}

enum PrizeType {
	REGISTER = 1;
	INVITE = 2;
	DAYRANK = 3;
	TOTALRANK = 4;
}

message GetPrizeInfoResp {
	required uint32 day_rank = 1;   //存放可领取次数
	required uint32 invite_num = 2;  //存放可领取次数
	required uint32 total_rank = 3;  //存放排名，大于0可领取，领取完归零
	required uint32 frist_register = 4;  //大于0可领取
	required uint32 day_invite = 5; //当日邀请人数，展示用
	required uint32 total_invite = 6; //总邀请人数 ， 展示用
}

message SetUidPairQQReq {
	required uint32 uid = 1;
	required string qq = 2;
}



message TakePrizeReq {
	required uint32 uid = 1;
	required uint32 prize_type = 2;  //PrizeType
	required string qq_number = 3;
}

message TakePrizeResp {
}

service Activity {
	/*  Server listening port */
	option( tlvpickle.Magic ) = 15201;

    // activities 100~
    rpc GetGameOffer( GetGameOfferReq ) returns (GetGameOfferRsp) {
        option( tlvpickle.CmdID ) = 101;
        option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g <game id>";
    }

    // activities 1~
    rpc GetOfferCategoryList ( tlvpickle.SKBuiltinEmpty_PB ) returns ( OfferCategoryList ) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    // activities 10 ~
    rpc CreateActivity ( CreateActivityReq ) returns (CreateActivityResp) {
	    option( tlvpickle.CmdID ) = 11;
	    option( tlvpickle.OptString ) = "o:p:q:r:s:t:h:i:j:k:l:";
	    option( tlvpickle.Usage ) = "-o <name> -p <desc> -q <banner> -r <begin_time> -s <end_time> -t <game_id> -h <category_id> -i <amount> -j <desc> -k <notes> -l <appendix>";
    }

    rpc UpdateActivity( UpdateActivityReq ) returns ( UpdateActivityResp ) {
	    option( tlvpickle.CmdID ) = 12;
	    option( tlvpickle.OptString ) = "n:o:p:q:r:s:t:h:i:j:k:l:";
	    option( tlvpickle.Usage ) = "-n <activity_id> -o <name> -p <desc> -q <banner> -r <begin_time> -s <end_time> -t <game_id> -h <category_id> -i <amount> -j <desc> -k <notes> -l <appendix>";
    }

    rpc DeleteActivity( DeleteActivityReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
	    option( tlvpickle.CmdID ) = 13;
	    option( tlvpickle.OptString ) = "a:g:";
	    option( tlvpickle.Usage ) = "-a <activity_id> -g <id1> -g <id2> -g <id3> ...";
    }

    rpc GetActivityList( GetActivityListReq ) returns ( GetActivityListResp ) {
	    option( tlvpickle.CmdID ) = 14;
	    option( tlvpickle.OptString ) = "s:l:n:r:g:";
	    option( tlvpickle.Usage ) = "-s <status 1/2/3/4/8/16/31> -l <from_idx> -n <count> -r <time_reverse:0/1> -g <game_id> ";
    }

    //福利活动 20~
    rpc CreateOfferEvent ( CreateOfferEventReq ) returns (CreateOfferEventRsp) {
        option( tlvpickle.CmdID ) = 21;
        option( tlvpickle.OptString ) = "o:p:q:r:s:t:h:i:j:k:l:";
        option( tlvpickle.Usage ) = "-o <name> -p <desc> -q <notes> -r <begin_time:Ns> -s <end_time:Ns> -t <banner> -h <detail_href> -i <type:1/2/3> -j <rank> -k <game_id> -l <status:1/2/3>";
    }

    rpc ModifyOfferEvent ( ModifyOfferEventReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 22;
        option( tlvpickle.OptString ) = "n:o:p:q:r:s:t:h:i:j:k:l:";
        option( tlvpickle.Usage ) = "-o <name> -p <desc> -q <notes> -r <begin_time:Ns> -s <end_time:Ns> -t <banner> -h <detail_href> -i <type:1/2/3> -j <rank> -k <game_id> -l <status:1/2/3>";
    }

    rpc DelOfferEvent ( DelOfferEventReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 23;
        option( tlvpickle.OptString ) = "n:";
        option( tlvpickle.Usage ) = "-n <id>";
    }

    rpc GetOfferEvent ( GetOfferEventReq ) returns ( GetOfferEventRsp ) {
        option( tlvpickle.CmdID ) = 24;
        option( tlvpickle.OptString ) = "o:p:q:r:s:";
        option( tlvpickle.Usage ) = " -o <status> -p <type> -q <game_id> -r <from_index> -s <count>";
    }

    //福利活动的游戏榜 30~
    rpc AddGameRank ( AddGameRankReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
        option( tlvpickle.CmdID ) = 31;
        option( tlvpickle.OptString ) = "g:r:";
        option( tlvpickle.Usage ) = "-g <gameid> -r <rank>";
    }

    rpc ModifyGameRank( ModifyGameRankReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
        option( tlvpickle.CmdID ) = 32;
        option( tlvpickle.OptString ) = "g:r:";
        option( tlvpickle.Usage ) = "-g <gameid> -r <rank>";

    }

    rpc DelGameRank ( DelGameRankReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 33;
        option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g <gameid>";
    }

    rpc GetGameRank( GetGameRankReq ) returns( GetGameRankRsp ){
        option( tlvpickle.CmdID ) = 34;
        option( tlvpickle.OptString ) = "s:t:";
        option( tlvpickle.Usage ) = "-s <start> -t <count>";
    }

    rpc GetActBanners(tlvpickle.SKBuiltinEmpty_PB) returns(GetActBannersResp){
        option( tlvpickle.CmdID ) = 35;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";   
    }

    rpc GetActBannerOpTime(tlvpickle.SKBuiltinEmpty_PB) returns (GetActBannerOpTimeResp){
        option( tlvpickle.CmdID ) = 36;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";         
    }

    rpc UpdateActBanner(UpdateActBannerReq) returns (tlvpickle.SKBuiltinEmpty_PB){
        option( tlvpickle.CmdID ) = 37;
        option( tlvpickle.OptString ) = "n:j:p:r:";
        option( tlvpickle.Usage ) = "-n <banner_id> -j <action_url> -p <cover_url> -r <rank>";        
    }

    rpc DelActBanner(DelActBannerReq) returns (tlvpickle.SKBuiltinEmpty_PB){
        option( tlvpickle.CmdID ) = 38;
        option( tlvpickle.OptString ) = "i:";
        option( tlvpickle.Usage ) = "-i <banner_id>";        
    }

    rpc GetActSplashScreen(tlvpickle.SKBuiltinEmpty_PB) returns(GetActSplashScreenResp){
        option( tlvpickle.CmdID ) = 39;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";   
    }

    rpc GetActSplashScreenOpTime(tlvpickle.SKBuiltinEmpty_PB) returns (GetActSplashScreenOpTimeResp){
        option( tlvpickle.CmdID ) = 40;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";         
    }

    rpc UpdateActSplashScreen(UpdateActSplashScreenReq) returns (tlvpickle.SKBuiltinEmpty_PB){
        option( tlvpickle.CmdID ) = 41;
        option( tlvpickle.OptString ) = "n:j:p:s:e:x:a:m:";
        option( tlvpickle.Usage ) = "-n <screen_id> -j <action_url> -p <screen_url> -s <start_time> -e <end_time> -x <clientType 0=andriod 1=ios> -a <app id> -m <market_id>";
    }

    rpc DelActSplashScreen(DelActSplashScreenReq) returns (tlvpickle.SKBuiltinEmpty_PB){
        option( tlvpickle.CmdID ) = 42;
        option( tlvpickle.OptString ) = "i:";
        option( tlvpickle.Usage ) = "-i <screen_id>";        
    }

  rpc GetActSplashScreenV2(tlvpickle.SKBuiltinEmpty_PB) returns(GetActSplashScreenV2Resp){
    option( tlvpickle.CmdID ) = 43;
    option( tlvpickle.OptString ) = "";
    option( tlvpickle.Usage ) = "";
  }
	//For first voucher


    rpc GetFirstVoucherProducts( GetFirstVoucherProductsReq ) returns ( GetFirstVoucherProductsResp ) {
	    option( tlvpickle.CmdID ) = 201;
	    option( tlvpickle.OptString ) = "";
	    option( tlvpickle.Usage ) = "";
    }

	 rpc AddFirstVoucherProduct( AddFirstVoucherProductReq ) returns ( AddFirstVoucherProductResp ) {
	    option( tlvpickle.CmdID ) = 202;
	    option( tlvpickle.OptString ) = "";
	    option( tlvpickle.Usage ) = "";
    }

	 rpc ModifyFirstVoucherProduct( ModifyFirstVoucherProductReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
	    option( tlvpickle.CmdID ) = 203;
	    option( tlvpickle.OptString ) = "";
	    option( tlvpickle.Usage ) = "";
    }

	 rpc GetGameFirstVoucherProduct( GetGameFirstVoucherProductReq ) returns ( GetGameFirstVoucherProductResp ) {
	    option( tlvpickle.CmdID ) = 204;
	    option( tlvpickle.OptString ) = "";
	    option( tlvpickle.Usage ) = "";
    }

	rpc DeleteFirstVoucherProduct( DeleteFirstVoucherProductReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
	    option( tlvpickle.CmdID ) = 205;
	    option( tlvpickle.OptString ) = "";
	    option( tlvpickle.Usage ) = "";
    }

	 rpc AddFirstVoucherItem( AddFirstVoucherItemReq ) returns ( AddFirstVoucherItemResp ) {
	    option( tlvpickle.CmdID ) = 206;
	    option( tlvpickle.OptString ) = "";
	    option( tlvpickle.Usage ) = "";
    }

	 rpc GetFirstVoucherItem( GetFirstVoucherItemReq ) returns ( GetFirstVoucherItemResp ) {
	    option( tlvpickle.CmdID ) = 207;
	    option( tlvpickle.OptString ) = "";
	    option( tlvpickle.Usage ) = "";
    }

	 rpc DeleteFirstVoucherItem( DeleteFirstVoucherItemReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
	    option( tlvpickle.CmdID ) = 208;
	    option( tlvpickle.OptString ) = "";
	    option( tlvpickle.Usage ) = "";
    }

	 rpc PurchaseFirstVoucherItem( PurchaseFirstVoucherItemReq ) returns ( PurchaseFirstVoucherItemResp ) {
	    option( tlvpickle.CmdID ) = 209;
	    option( tlvpickle.OptString ) = "";
	    option( tlvpickle.Usage ) = "";
    }

	 rpc GetFirstVoucherUserItem( GetFirstVoucherUserItemReq ) returns ( GetFirstVoucherUserItemResp ) {
	    option( tlvpickle.CmdID ) = 210;
	    option( tlvpickle.OptString ) = "";
	    option( tlvpickle.Usage ) = "";
    }

    
    rpc SetGamePreorderUserSppt( SetGamePreorderUserSpptReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 211;
        option( tlvpickle.OptString ) = "g:u:o:n";
        option( tlvpickle.Usage ) = "-g<game_id> -u<uid> -o<ojb_id> -n<count>";
    } 

    rpc GetGamePreorderUserSppt( GetGamePreorderUserSpptReq ) returns ( GetGamePreorderUserSpptResp ) {
        option( tlvpickle.CmdID ) = 212;
        option( tlvpickle.OptString ) = "g:u:";
        option( tlvpickle.Usage ) = "-g<game_id> -u<uid>";
    }    
    
    rpc GetGamePreorderResult( GetGamePreorderResultReq ) returns ( GetGamePreorderResultResp ) {
        option( tlvpickle.CmdID ) = 213;
        option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g<game_id>";
    }      


	///////////////////////////////////////QQ CAR RACE /////////////////////////////////////

	rpc GetQQCarActivityDayRank( tlvpickle.SKBuiltinEmpty_PB ) returns ( GetQQCarActivityRankResp ) {
        option( tlvpickle.CmdID ) = 240;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

	rpc GetQQCarActivityTotalRank( tlvpickle.SKBuiltinEmpty_PB ) returns ( GetQQCarActivityRankResp ) {
        option( tlvpickle.CmdID ) = 241;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

	rpc NotifyInviteRecord( NotifyInviteRecordReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 242;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

	rpc SetUidPairQQ ( SetUidPairQQReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 243;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

	rpc GetPrizeInfo ( GetPrizeInfoReq ) returns ( GetPrizeInfoResp ) {
        option( tlvpickle.CmdID ) = 244;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

	rpc TakePrize ( TakePrizeReq ) returns ( TakePrizeResp ) {
        option( tlvpickle.CmdID ) = 245;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

	
}
