syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package Medal;

// =============================================================================
//
// 勋章定义
//
// =============================================================================
message PMedal{
	required uint32 medal_id = 1;
	required string name = 2;
	repeated string desc = 3;
	required string icon = 4;
	required string border_icon = 5;
	required string url = 6;
	required uint32 expire_time = 7;
	required uint32 buff_exp_rate = 8;
	required uint32 buff_currency_rate = 9;
	required string title = 10;
	required string content = 11;
	optional string title_icon = 12;
	optional bool is_allow_set_taillight = 13;
}



// =============================================================================
//
// 获取勋章定义最后更新时间
//
// =============================================================================
message GetMedalConfigUpdateTimeReq {

}

message GetMedalConfigUpdateTimeResp {
	required uint32 update_time = 1;
}


// =============================================================================
//
// 获取勋章定义列表
//
// =============================================================================
message GetMedalConfigListReq {

}

message GetMedalConfigListResp {
	repeated PMedal medal_list = 1;
}


















// =============================================================================
//
// 获取用户勋章列表
//
// =============================================================================

message UserMedal{
	required uint32 uid = 1;
	required uint32 medal_id = 2;
	required uint32 award_time = 3;				// 获取勋章的时间
	required uint32 expire_time = 4;			// 勋章过期时间
	required uint32 buff_exp = 5;			// 勋章为用户带来的经验加成
	required uint32 buff_currency = 6;		// 勋章为用户带来的红钻加成
}

message UserTaillightMedal{
	required uint32 uid = 1;
	required uint32 medal_id = 2;
}

message GetUserMedalListReq{
	required uint32 uid = 1;
}

message GetUserMedalListResp{
	repeated UserMedal user_medal_list = 1;
}

message BatGetUserMedalListReq {
	repeated uint32 uid_list = 1;
}

message BatGetUserMedalListResp {
	repeated UserMedal user_medal_list = 1;
}

// 增加获取尾灯信息
message GetUserMedalListWhitTaillightReq
{
	required uint32 uid = 1;
}

message GetUserMedalListWhitTaillightResp
{
	repeated UserMedal user_medal_list = 1;
	repeated UserTaillightMedal taillight_medal_list = 2;
}

message BatGetUserMedalListWhitTaillightReq
{
	repeated uint32 uid_list = 1;
}
message BatGetUserMedalListWhitTaillightResp
{
	repeated UserMedal user_medal_list = 1;
	repeated UserTaillightMedal taillight_medal_list = 2;
}

// ===============================================================================
//
// 授予用户勋章
//
// ===============================================================================

message AwardMedalToUserReq {
	required uint32 uid = 1;
	required uint32 medal_id = 2;
	required uint32 expire_time = 3;
	optional bool is_extension = 4;
}

message AwardMedalToUserResp {

}

//回退用户勋章的时间
message ReduceUserMedalReq {
	required uint32 uid = 1;
	required uint32 medal_id = 2;
	required uint32 expire_time = 3;
}

message ReduceUserMedalResp {

}

// 设置尾灯
message SetUserMedalTaillightReq
{
	required uint32 uid = 1;
	repeated uint32 taillight_medalid_list = 2;
}

message SetUserMedalTaillightResp
{
}

// 删除勋章
message DeleteMedalReq
{
	required uint32 medal_id = 1;
}


service Medal {
	option( tlvpickle.Magic ) = 15060;		// 服务监听端口号

	rpc GetUserMedalList( GetUserMedalListReq ) returns( GetUserMedalListResp ){
		option( tlvpickle.CmdID ) = 1;										// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";	// 测试工具的命令号帮助
	}

	rpc AwardMedalToUser( AwardMedalToUserReq ) returns ( AwardMedalToUserResp ){
		option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "u:m:t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -m <medal id> -t <expire time>";	// 测试工具的命令号帮助
	}

	rpc GetMedalConfigUpdateTime( GetMedalConfigUpdateTimeReq ) returns ( GetMedalConfigUpdateTimeResp ){
		option( tlvpickle.CmdID ) = 3;										// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";								// 测试工具的命令号帮助
	}

	rpc GetMedalConfigList( GetMedalConfigListReq ) returns ( GetMedalConfigListResp ){
		option( tlvpickle.CmdID ) = 4;										// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";								// 测试工具的命令号帮助
	}

	rpc BatGetUserMedalList( BatGetUserMedalListReq ) returns ( BatGetUserMedalListResp ){
		option( tlvpickle.CmdID ) = 5;										// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid list>";
	}

	// 在获取勋章列表的基础上 获取用户的勋章尾灯设置
	rpc GetUserMedalListWhitTaillight( GetUserMedalListWhitTaillightReq ) returns( GetUserMedalListWhitTaillightResp ){
		option( tlvpickle.CmdID ) = 6;									// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";	// 测试工具的命令号帮助
	}

	// 在批量获取勋章列表的基础上 获取用户的勋章尾灯设置
	rpc BatGetUserMedalListWhitTaillight( BatGetUserMedalListWhitTaillightReq ) returns ( BatGetUserMedalListWhitTaillightResp ){
		option( tlvpickle.CmdID ) = 7;									// 命令号
        option( tlvpickle.OptString ) = "u:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid list>";
	}

	// 设置尾灯
	rpc SetUserMedalTaillight( SetUserMedalTaillightReq ) returns ( SetUserMedalTaillightResp ){
		option( tlvpickle.CmdID ) = 8;							// 命令号
        option( tlvpickle.OptString ) = "u:m:";					// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -m <medal id>";	// 测试工具的命令号帮助
	}

	rpc AddMedal( PMedal ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 9;							// 命令号
        option( tlvpickle.OptString ) = "u:";					// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";	// 测试工具的命令号帮助
	}

	rpc UpdateMedal( PMedal ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 10;							// 命令号
        option( tlvpickle.OptString ) = "u:";					// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";	// 测试工具的命令号帮助
	}

	rpc DeleteMedal( DeleteMedalReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ){
		option( tlvpickle.CmdID ) = 11;							// 命令号
        option( tlvpickle.OptString ) = "u:";					// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid>";	// 测试工具的命令号帮助
	}

	rpc ReduceUserMedal( ReduceUserMedalReq ) returns ( ReduceUserMedalResp ){
		option( tlvpickle.CmdID ) = 12;										// 命令号
        option( tlvpickle.OptString ) = "u:m:t:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -m <medal id> -t <expire time>";	// 测试工具的命令号帮助
	}
}
