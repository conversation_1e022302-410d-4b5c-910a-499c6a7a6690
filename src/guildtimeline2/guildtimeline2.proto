syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package GuildTimeline2;									

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list


//公会游戏
message stGuildGame 
{
    required uint32 game_id = 1; // 游戏id
    required string name = 2;
    required string icon_id = 3;    //标识头像资源id
}

message CheckInMem 
{
	required uint32 uid = 1;		// uid
	required uint32 days = 2;		// 连续签到次数
}

message DonateMem 
{
	required uint32 uid = 1;		// uid
	required uint32 days = 2;		// 连续签到次数
}


message stGuildMember {
    required uint32 uid = 1;
    required string name = 2;
	required uint32 role = 3;
	required string remark = 4;		// 勋章（资深玩家）
	required uint32 seq_id = 5;		// 序列号ID
	required string account = 6;	// 用户唯一账号
}


//*****
// 同步的数据类型
//*****

message stGuildCheckinNumSyncInfo 
{
	required uint32 last_update_time = 1;			// 最后更新签到的时间
	required uint32 last_seq_id = 2;				// 最后更新签到的seq
	required uint32 checkin_num = 3;				// 签到人数
}

message stGuildCheckinTopUserSyncInfo 
{
	required uint32 last_update_time = 1;			// 最后更新签到的时间
	required uint32 last_seq_id = 2;				// 最后更新签到的seq
	repeated CheckInMem checkin_top_n_list = 3;		// 签到的前几个人的uid
}

message stGuildDonateNumSyncInfo 
{
	required uint32 last_update_time = 1;			// 最后更新捐献的时间
	required uint32 last_seq_id = 2;				// 最后更新捐献的seq
	required uint32 donate_num = 3;					// 捐献人数
}

message stGuildDonateTopUserSyncInfo 
{
	required uint32 last_update_time = 1;			// 最后更新捐献的时间
	required uint32 last_seq_id = 2;				// 最后更新捐献的seq
	repeated DonateMem donate_top_n_list = 3;		// 捐献的前几个人的uid
}

message stGuildBaseSyncInfo 
{
	required uint32 last_update_time = 1;   // 最后更新的时间
	required uint32 last_seq_id = 2;		// 最后更新的seq
    required uint32 guild_id = 3;           //内部使用的id，不可改
    required uint32 guild_display_id = 4;   //给用户看到的公会id
	required uint32 guild_groupID = 5;   // 公会总群ID
	required uint32 game_sizelimit = 6;  // 公会游戏数上限
    required uint32 create_date = 7;	//  创建时间
	required string guild_prefix = 8;	//  工会马甲前缀
    required string name = 9;
    required string desc = 10;
	required string manifesto = 11;     //  公会宣言
	required bool is_needverify = 12;	//  是否需要验证
}

message stGuildExtraSyncInfo 
{
	required uint32 last_update_time = 1;	// 最后更新的时间
	required uint32 last_seq_id = 2;		// 最后更新的seq
	required string icon_id = 3;		    // 公会图像id
}

message stGuildNumberDataSyncInfo 
{
	required uint32 last_update_time = 1;	// 最后更新的时间
	required uint32 last_seq_id = 2;		// 最后更新的seq
    required uint32 member_count = 3;       // 公会总人数
}

// 新版的保存群组变化的timeline sync结构
// 不再使用旧的通用增量timeline结构存储
message stGuildGroupSyncInfo 
{
	required uint32 group_id = 1;
	required uint32 last_seq_id  = 2;
	required uint32 last_update_time = 3;	// 最后更新的时间
	required bool isDel = 4;
}

message stGuildBatchGroupSyncInfo 
{
	repeated stGuildGroupSyncInfo groupInfo_list = 1;
}

message stGuildSpecialFlagSync
{
	required uint32 guildid = 1;
}

message stGuildGameSyncInfo 
{
	required uint32 last_update_time = 1;			// 最后更新的时间
	required uint32 last_seq_id = 2;				// 最后更新的seq
	repeated stGuildGame game_list = 3;	//公会游戏
}

message stTimelineDataGiftpkg
{
	required uint32 game_id = 1;
}
//
//*******************************************

// 通用序号增量数据变化[比如 公会or公会群管理员的 增加和删除]


// 更新工会签到人数变化的Seq
message UpdateGuildCheckinNumSeqReq 
{
	required uint32 guild_id = 1;					// 工会ID
	required stGuildCheckinNumSyncInfo num_info = 2;
}
// 更新工会签到TopN用户列表变化的Seq
message UpdateGuildCheckinTopUserSeqReq 
{
	required uint32 guild_id = 1;					// 工会ID
	required stGuildCheckinTopUserSyncInfo top_info = 2;
}

// 更新工会捐献人数变化的Seq
message UpdateGuildDonateNumSeqReq 
{
	required uint32 guild_id = 1;					// 工会ID
	required stGuildDonateNumSyncInfo num_info = 2;
}
// 更新工会捐献TopN用户列表变化的Seq
message UpdateGuildDonateTopUserSeqReq 
{
	required uint32 guild_id = 1;					// 工会ID
	required stGuildDonateTopUserSyncInfo top_info = 2;
}

// 公会基本信息变化
message UpdateGuildBaseInfoSeqReq {
	required uint32 guild_id = 1;					// 工会ID
	required stGuildBaseSyncInfo baseinfo = 2;
}

// 公会附加信息变化
message UpdateGuildExtraInfoSeqReq {
	required uint32 guild_id = 1;					// 工会ID
	required stGuildExtraSyncInfo extrainfo = 2;
}

// 公会数字类信息变化(包括公会成员数量)
message UpdateGuildNumberInfoSeqReq {
	required uint32 guild_id = 1;					// 工会ID
	required stGuildNumberDataSyncInfo numberInfo = 2;
}

// 公会成员信息变化(目前只对管理员)
message UpdateGuildMemberInfoReq 
{
	required uint32 guild_id = 1;		// 工会ID
	required uint32 uid = 2;			// 成员uid
	required stGuildMember mem_info = 3;// 成员资料
}

message stCommonIncrTimelineData 
{
	required string key_prefix = 1;		// 如"detail","group","mem","checkin","game","album","giftpkg"
	required uint32 key_id = 2;			// 如[群ID],[成员uid],[游戏id],[相册id],[礼包id]
	required bool is_deleted = 3;		// 是否删除
	optional bytes bin_value = 4;		// timeline内容
}

message UpdateCommonIncrTimelineSeqReq 
{
	required uint32 guild_id = 1;
	required uint32 seq_id = 2;
	required stCommonIncrTimelineData data = 3;
}
message GetCommonTimelineBySeqReq 
{
	required uint32 guild_id = 1;
	required uint32 seq_id = 2;
}

message GetCommonTimelineBySeqResp 
{
	repeated uint32 seq_id_list = 1;			// 序列号
	repeated stCommonIncrTimelineData data_list = 2;		// seq
}

message BatchDeleteCommonTimelineReq 
{
	required uint32 guild_id = 1;
	repeated uint32 seq_id_list = 2;
}

message GetSyncUpdateInfoBySeqReq 
{
	required uint32 guild_id = 1;	// 工会ID
	required uint32 seq_id = 2;		// seq_id
}

message GetSyncUpdateInfoBySeqResp 
{
	optional stGuildBaseSyncInfo guild_baseinf = 1;
	optional stGuildExtraSyncInfo guild_extrinf = 2;
	optional stGuildCheckinNumSyncInfo checkin_num = 3;
	optional stGuildCheckinTopUserSyncInfo checkin_topn = 4;
	optional stGuildNumberDataSyncInfo guild_numdata = 5;
	optional stGuildBatchGroupSyncInfo group_info = 6;
	optional stGuildSpecialFlagSync flagSync = 7;
	optional stGuildDonateNumSyncInfo donate_num = 8;
	optional stGuildDonateTopUserSyncInfo donate_topn = 9;
}
//*******************************************





message CheckGuildDataIsExistReq 
{
	required uint32 guild_id = 1;	// 公会ID
}

message CheckGuildDataIsExistResp
{
	required uint32 guild_id = 1;	// 公会ID
	required bool is_Exist = 2;     // 公会信息是否存在
}

message CleanGuildAllTimelineReq 
{
	required uint32 guild_id = 1;	// 公会ID
}

// 该接口暂时不启用
message UpdateGuildGroupInfoReq 
{
	required uint32 guild_id = 1;		    // 工会ID
	required uint32 group_id = 2;		    // 群ID
	required stGuildGroupSyncInfo info = 3;	// 群同步信息
}

// 获取签到最后更新时间
message GetGuildCheckInLastTimeReq 
{
	required uint32 guild_id = 1;		    // 工会ID
}
message GetGuildCheckInLastTimeResp
{
	required uint32 guild_id = 1;		    // 工会ID
	required uint32 last_update_time = 2;   // 
}

// 获取捐献最后更新时间
message GetGuildDonateLastTimeReq 
{
	required uint32 guild_id = 1;		    // 工会ID
}
message GetGuildDonateLastTimeResp
{
	required uint32 guild_id = 1;		    // 工会ID
	required uint32 last_update_time = 2;   // 
}

//////////////////
service GuildTimeline2 {
    option( tlvpickle.Magic ) = 15140;		// 服务监听端口号
	
	// 签到总数的变化
    rpc UpdateGuildCheckinNumSeq(UpdateGuildCheckinNumSeqReq) returns(tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 1;										// 命令号
        option( tlvpickle.OptString ) = "g:s:x:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -s <seq_id> -x <num>";	// 测试工具的命令号帮助
    }
	
	// 签到TOPN的列表的变化
    rpc UpdateGuildCheckinTopUserSeq(UpdateGuildCheckinTopUserSeqReq) returns(tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "g:s:u";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -s <seq_id> -u <uid>";	// 测试工具的命令号帮助
    }
	
	// 公会基本信息变化
    rpc UpdateGuildBaseInfoSeq(UpdateGuildBaseInfoSeqReq) returns(tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 3;										// 命令号
        option( tlvpickle.OptString ) = "g:s:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -s <seq_id>";			// 测试工具的命令号帮助
    }
	
	// 公会附加信息变化
	rpc UpdateGuildExtraInfoSeq(UpdateGuildExtraInfoSeqReq) returns(tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 4;										// 命令号
        option( tlvpickle.OptString ) = "g:s:x:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -s <seq_id> -x <icon_id>";	// 测试工具的命令号帮助
    }
	
	// 公会数字类信息变化
	rpc UpdateGuildNumberInfoSeq(UpdateGuildNumberInfoSeqReq) returns(tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 5;										// 命令号
        option( tlvpickle.OptString ) = "g:s:x:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -s <seq_id> -x <memberNum>";	// 测试工具的命令号帮助
    }
	
	// 
	// 公会成员信息变化(目前只针对有权限的管理员)
	// 注意该接口没有使用！！！！
    rpc UpdateGuildMemberInfo(UpdateGuildMemberInfoReq) returns (tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 6;										// 命令号
        option( tlvpickle.OptString ) = "g:u:s:d:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -u <uid> -s <seq_id> -d <is_Del>";	// 测试工具的命令号帮助
    }
	
	// 通用的增量时间线变化
    rpc UpdateCommonIncrTimelineSeq(UpdateCommonIncrTimelineSeqReq) returns (tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 7;																		// 命令号
        option( tlvpickle.OptString ) = "g:s:p:u:d:";														// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -s <seq_id> -p <prefix> -u <id> -d <is_delete>";			// 测试工具的命令号帮助
    }
	
	rpc GetCommonTimelineBySeq(GetCommonTimelineBySeqReq) returns (GetCommonTimelineBySeqResp) {
        option( tlvpickle.CmdID ) = 8;										// 命令号
        option( tlvpickle.OptString ) = "g:s:";								// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -s <seq_id>";			// 测试工具的命令号帮助
    }
	
    rpc BatchDeleteCommonTimeline(BatchDeleteCommonTimelineReq) returns (tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 9;																		// 命令号
        option( tlvpickle.OptString ) = "g:s:";																// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -s <seq_id>";											// 测试工具的命令号帮助
    }
	
	//根据SEQ序号获取最新SYNC数据
	rpc GetSyncUpdateInfoBySeq(GetSyncUpdateInfoBySeqReq) returns(GetSyncUpdateInfoBySeqResp) 
	{
        option( tlvpickle.CmdID ) = 10;										// 命令号
        option( tlvpickle.OptString ) = "g:s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -s <seq_id>";			// 测试工具的命令号帮助
    }
	
	//检测公会信息是否存在
	rpc CheckGuildDataIsExist(CheckGuildDataIsExistReq) returns(CheckGuildDataIsExistResp) 
	{
        option( tlvpickle.CmdID ) = 11;							// 命令号
        option( tlvpickle.OptString ) = "g:";					// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id>";			// 测试工具的命令号帮助
    }
	
	// 清除公会全部timeline数据 只在公会解散时被调用
	rpc CleanGuildAllTimeline(CleanGuildAllTimelineReq) returns(tlvpickle.SKBuiltinEmpty_PB) 
	{
        option( tlvpickle.CmdID ) = 12;							// 命令号
        option( tlvpickle.OptString ) = "g:";					// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id>";			// 测试工具的命令号帮助
    }
	
	// 该接口暂时不启用！！！
	// 公会群更新（新版不使用通用timeline增量结构存储）
	rpc UpdateGuildGroupInfo(UpdateGuildGroupInfoReq) returns(tlvpickle.SKBuiltinEmpty_PB) 
	{
        option( tlvpickle.CmdID ) = 13;							// 命令号
        option( tlvpickle.OptString ) = "g:p:s:x:";					// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -p <group_id> -s <seqid> -x <isdel>";			// 测试工具的命令号帮助
    }
	
	// 获取公会最后签到更新时间
	rpc GetGuildCheckInLastTime(GetGuildCheckInLastTimeReq) returns(GetGuildCheckInLastTimeResp) 
	{
        option( tlvpickle.CmdID ) = 14;							// 命令号
        option( tlvpickle.OptString ) = "g:";					// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> ";			// 测试工具的命令号帮助
    }
	
	// 捐献总数的变化
    rpc UpdateGuildDonateNumSeq(UpdateGuildDonateNumSeqReq) returns(tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 15;										// 命令号
        option( tlvpickle.OptString ) = "g:s:x:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -s <seq_id> -x <num>";	// 测试工具的命令号帮助
    }
	
	// 捐献TOPN的列表的变化
    rpc UpdateGuildDonateTopUserSeq(UpdateGuildDonateTopUserSeqReq) returns(tlvpickle.SKBuiltinEmpty_PB) {
        option( tlvpickle.CmdID ) = 16;										// 命令号
        option( tlvpickle.OptString ) = "g:s:u";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> -s <seq_id> -u <uid>";	// 测试工具的命令号帮助
    }
	
	// 获取公会最后捐献更新时间
	rpc GetGuildDonateLastTime(GetGuildDonateLastTimeReq) returns(GetGuildDonateLastTimeResp) 
	{
        option( tlvpickle.CmdID ) = 17;							// 命令号
        option( tlvpickle.OptString ) = "g:";					// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id> ";			// 测试工具的命令号帮助
    }
}
