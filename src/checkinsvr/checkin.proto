syntax="proto3";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";  

// namespace
package checkin;

enum CheckInType{
    checkin_none =          0;
    game_conglin  = 1;  //丛林法则签到
    cf_match      = 2;  //cf比赛礼包领取记录
    game_zhongchao = 3; //中超荣耀签到
    cf_giftpkg     = 4; //cf礼包领取活动
	game_id5		= 5; //第五人格拉新活动
    aliveness_check   = 6;    //checksync次数统计,每天最多统计6次(db数据已清空)
    tbean_first_recharge = 7;   //T豆首充活动
    chat_anniversary = 8;         // 2018 娱乐房周年活动
    cf_ten_anniversary = 9;       // 2018 CF十周年活动
    mid_autumn_recharge = 10;     // 2018 中秋充值活动
    mid_autumn_recharge_new = 11;   //改需求后方便测试
	
	// 1000000以上的是每日签到(DailyCheckInType)，用于读取奖励配置
	daily_checkin_begin = 1000000;
	
	// 2000000以上的是虚拟的签到类型，仅用于读取奖励配置
	act_daily_checkin_login = 2000001;
}

enum AwardIdType{
    award_none =  0;
    sign =  1;      //签到活动
    grab =  2;      //抢码活动
    channel = 3;    //房间活动
    live =    4;    //直播间活动
    cf_new=    5;   //cf比赛新用户礼包
    cf_sign =   6;  //cf比赛报名礼包
    cf_share=   7;  //cf比赛分享礼包
	id5_channel_online_invitee		= 8; // 《第五人格》房间在线代金券(被邀请者)
	id5_channel_online_inviter		= 9; // 《第五人格》房间在线代金券(邀请者)
	id5_quickmatch	= 10; // 《第五人格》匹配礼包
	id5_new_invite = 11; // 《第五人格》邀请开黑礼包
    tbean_recharge_low_gift = 12;   //T豆首充活动1元奖励
    tbean_recharge_high_gift = 13;  //T豆首充活动10元奖励
}

//《第五人格》
enum GameId5CheckInFlag{
	id5_flag_none = 0;
	id5_flag_hold_mic = 1;			// 房间上麦
	id5_flag_channel_online = 2;	// 房间在线
}

// 每日签到
enum DailyCheckInType{
	daily_checkin_none = 0;
	daily_checkin_login = 1;	// tt登录
}

// 签到奖励类型
enum CheckInCommonAwardType{
	Common_Award_None = 0;
	Common_Award_Backpack_Item = 1;	// 背包包裹
	Common_Award_Headwear = 2;		// 麦位框
	Common_Award_Decoration = 3;	// 座驾
	Common_Award_Red_Diamond = 4;	// 红钻
	Common_Award_Exp = 5;			// 经验
	Common_Award_Medal = 6;			// 勋章
}

// 通用签到奖励
message CheckInCommonAward
{
	uint32 checkin_type = 1;
	uint32 sub_id = 2;
	uint32 award_type = 3;	//see CheckInCommonAwardType
	uint32 award_id = 4;
	string str_award_id = 5;
    uint32 award_num = 6;
	uint32 award_time = 7;
	uint32 uniq_id = 8;
}

// 签到配置
message CheckInBriefConfig
{
	uint32 checkin_type = 1;	// CheckInType
	uint32 sub_id = 2;
	string icon_url = 3;
	string award_icon_url = 4;
	string award_msg = 5;
	string background_url = 6;
	string award_button_url = 7;
	string msg_color = 8;
}

message CheckInCommonConfig
{
	CheckInBriefConfig brief_cfg = 1;
	repeated CheckInCommonAward award_list = 2;
}

//签到详细信息

message CheckInDetailInfo{
    uint32 check_in_time = 1;
    uint32 check_in_value = 2;
}

message DoCheckInReq{
    uint32 uid = 1;
    uint32 check_in_type = 2;
	uint32 check_in_value = 3;
	string device_id = 4;
}

message GetUserCheckInTimeReq{
    uint32 uid = 1;
    uint32 check_in_type = 2;
}

message GetUserCheckInTimeResp{
    repeated uint32 time_list = 1; //时间戳列表
}

message GetUserCheckInDetailResp{
    repeated CheckInDetailInfo time_list = 1;
}

message FetchAwardReq{
    uint32 uid = 1;
    uint32 check_in_type = 2;
    uint32 award_id = 3;
    string device_id = 4;
    uint32 status = 5;
}

message UserAwardInfo{
    uint32 uid = 1;
    uint32 check_in_type = 2;
    uint32 award_id = 3;
    uint32 fetch_time = 4;
}

message UserDelayAwardInfo{
    uint32 uid = 1;
    uint32 check_in_type = 2;
    uint32 award_id = 3;
	string device_id = 4;
	uint32 uniq_id = 5;
	uint32 delay_fetch_time = 6;
    uint32 tbean_num = 7;
}

message GetAwardHistoryReq{
    uint32 check_in_type = 1;
    uint32 limit = 2;
}

message GetAwardHistoryResp{
    repeated UserAwardInfo history_list = 1;
}

message GetUserAwardByUidReq{
    uint32 uid = 1;
    uint32 check_in_type = 2;
}

message GetUserAwardByUidResp
{
    repeated UserAwardInfo award_list = 1;
}

message GetUserAwardByDevIdReq{
    string device_id = 1;
    uint32 check_in_type = 2;
}

message GetUserAwardByDevIdResp{
    repeated UserAwardInfo award_list = 1;
}

message AddDelayAwardReq{
    uint32 uid = 1;
    uint32 check_in_type = 2;
    uint32 award_id = 3;
    string device_id = 4;
	uint32 delay_fetch_second = 5;
}

message UserDailyCheckInInfo
{
	uint32 uid = 1;
	uint32 type = 2;	// DailyCheckInType
	uint32 last_checkin_at = 3;
	uint32 cont_checkin_days = 4;	// 连续签到天数
}

// tt登录签到
message LoginCheckInMissionConfig
{
	enum LoginExtraAwardType{
		Extra_Award_None = 0;
		Extra_Award_Backpack_Item = 1;	// 背包包裹
	}

	uint32 type = 1;	// DailyCheckInType
	uint32 exp = 2;
	uint32 red_diamond = 3;
	uint32 cont_days = 4;	// 额外奖励的连续签到天数
	uint32 extra_award_type = 5;	// 额外奖励类型
	uint32 extra_award_item_id = 6;	// 额外奖励id
	uint32 extra_award_item_count = 7;	// 额外奖励数量
	string extra_award_item_url = 8;	// 额外奖励的图标url
	string icon_url = 9;	// 弹窗的图标url
}

// 每日签到
message GetDailyCheckInReq
{
	uint32 uid = 1;
	uint32 type = 2;	// DailyCheckInType
}

message GetDailyCheckInResp
{
	UserDailyCheckInInfo checkin_info = 1;
}

message DoDailyCheckInReq
{
	uint32 uid = 1;
	uint32 type = 2;	// DailyCheckInType
}

message DoDailyCheckInResp
{
	UserDailyCheckInInfo checkin_info = 1;
	bytes award_info = 2;
	bool already_notify = 3;
}

// 获取签到配置列表
message GetCheckInCommonCfgListReq
{
	uint32 check_in_type = 1;
}

message GetCheckInCommonCfgListResp
{
	repeated CheckInCommonConfig cfg_list = 1;
}

message GetCheckInCommonCfgByIdReq
{
	uint32 check_in_type = 1;
	uint32 sub_id = 2;
}

message GetCheckInCommonCfgByIdResp
{
	CheckInCommonConfig cfg_info = 1;
}

// 更新签到奖励
message UpdateCheckInCommonAwardReq
{
	CheckInCommonAward award_info = 1;
}

message UpdateCheckInCommonAwardResp
{
}

// 更新签到配置
message UpdateCheckInBriefCfgReq
{
	CheckInBriefConfig cfg_info = 1;
}

message UpdateCheckInBriefCfgResp
{
}

message FirstRechargeAwardReq
{
	uint32 uid = 1;
	uint32 recharge_num = 2;
	string recharge_type = 3;
	string recharge_order_id = 4;
}
message FirstRechargeAwardResp
{
	uint32 award_pkg_id = 1;
}

service checkin {
    option( tlvpickle.Magic ) = 15576; 

    rpc DoCheckIn( DoCheckInReq ) returns( tlvpickle.SKBuiltinEmpty_PB ){
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "u:t:n:";
        option( tlvpickle.Usage ) = "-u<uid> -t<checkin_type> [-n checkin_value]";
    }

    rpc GetUserCheckInTime(GetUserCheckInTimeReq) returns( GetUserCheckInTimeResp ){
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:t:";
        option( tlvpickle.Usage ) = "-u<uid> -t<checkin_type>";
    }

    rpc FetchAward(FetchAwardReq) returns(tlvpickle.SKBuiltinEmpty_PB)
    {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "u:t:a:s:";
        option( tlvpickle.Usage ) = "-u<uid> -t<ckeckin_type> -a<award_id> -s<status>";
    }

    rpc GetAwardHistory(GetAwardHistoryReq) returns(GetAwardHistoryResp)
    {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "t:l:";
        option( tlvpickle.Usage ) = "-t<ckeckin_type> -l<limit>";
    }

    rpc GetUserAwardByUid(GetUserAwardByUidReq) returns(GetUserAwardByUidResp)
    {
        option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "u:t:";
        option( tlvpickle.Usage ) = "-u<uid> -t<checkin_type>";   
    }

    rpc GetUserAwardByDevId(GetUserAwardByDevIdReq) returns(GetUserAwardByDevIdResp)
    {
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "d:t:";
        option( tlvpickle.Usage ) = "-d<dev_id> -t<checkin_type>";
    }

	rpc AddDelayAward(AddDelayAwardReq) returns(tlvpickle.SKBuiltinEmpty_PB)
    {
        option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "u:t:a:n:";
        option( tlvpickle.Usage ) = "-u<uid> -t<checkin_type> -a<award_id> -n<delay_fetch_time>";
    }
	
	rpc GetDailyCheckIn(GetDailyCheckInReq) returns(GetDailyCheckInResp)
    {
        option( tlvpickle.CmdID ) = 8;
        option( tlvpickle.OptString ) = "u:t:";
        option( tlvpickle.Usage ) = "-u<uid> -t<daily_checkin_type>";
    }

	rpc DoDailyCheckIn(DoDailyCheckInReq) returns(DoDailyCheckInResp)
    {
        option( tlvpickle.CmdID ) = 9;
        option( tlvpickle.OptString ) = "u:t:";
        option( tlvpickle.Usage ) = "-u<uid> -t<daily_checkin_type>";
    }

    rpc GetUserCheckInDetail(GetUserCheckInTimeReq) returns( GetUserCheckInDetailResp ){
        option( tlvpickle.CmdID ) = 10;
        option( tlvpickle.OptString ) = "u:t:";
        option( tlvpickle.Usage ) = "-u<uid> -t<checkin_type>";
    }
	
	rpc GetCheckInCommonCfgList(GetCheckInCommonCfgListReq) returns( GetCheckInCommonCfgListResp ){
        option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "t:";
        option( tlvpickle.Usage ) = "-t<checkin_type>";
    }
	
	rpc GetCheckInCommonCfgById(GetCheckInCommonCfgByIdReq) returns( GetCheckInCommonCfgByIdResp ){
        option( tlvpickle.CmdID ) = 12;
        option( tlvpickle.OptString ) = "t:s:";
        option( tlvpickle.Usage ) = "-t<checkin_type> -s <sub_id>";
    }
	
	rpc UpdateCheckInCommonAward(UpdateCheckInCommonAwardReq) returns( UpdateCheckInCommonAwardResp ){
        option( tlvpickle.CmdID ) = 13;
        option( tlvpickle.OptString ) = "t:s:a:b:p:n:m:";
        option( tlvpickle.Usage ) = "-t<checkin_type> -s <sub_id> -a<award_id> -b <str_award_id> -p<award_type> -n <award_num> -m<award_time>";
    }
	
	rpc UpdateCheckInBriefCfg(UpdateCheckInBriefCfgReq) returns( UpdateCheckInBriefCfgResp ){
        option( tlvpickle.CmdID ) = 14;
        option( tlvpickle.OptString ) = "t:s:a:b:m:";
        option( tlvpickle.Usage ) = "-t<checkin_type> -s <sub_id> -a<award_icon_url> -b <icon_url> -m<award_msg>";
    }

    rpc FirstRechargeAward(FirstRechargeAwardReq) returns( FirstRechargeAwardResp ){
        option( tlvpickle.CmdID ) = 30;
        option( tlvpickle.OptString ) = "u:n:o:t:";
        option( tlvpickle.Usage ) = "-u <uid> -n<recharge_num> -o <order_id> -t <recharge_type>";
    }

}
