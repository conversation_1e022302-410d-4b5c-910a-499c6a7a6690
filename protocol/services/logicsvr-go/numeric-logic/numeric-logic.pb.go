// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/logicsvr-go/numeric-logic/numeric-logic.proto

package numeric_logic // import "golang.52tt.com/protocol/services/logicsvr-go/numeric-logic"

/*
buf:lint:ignore PACKAGE_SAME_DIRECTORY
*/

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import numeric_logic "golang.52tt.com/protocol/app/numeric-logic"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// NumericLogicClient is the client API for NumericLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type NumericLogicClient interface {
	// 设置页面 获取签约用户财富值控制开关状态
	GetUserRichSwitch(ctx context.Context, in *numeric_logic.GetUserRichSwitchReq, opts ...grpc.CallOption) (*numeric_logic.GetUserRichSwitchResp, error)
	// 设置页面 变更签约用户财富值控制开关状态
	SetUserRichSwitch(ctx context.Context, in *numeric_logic.SetUserRichSwitchReq, opts ...grpc.CallOption) (*numeric_logic.SetUserRichSwitchResp, error)
	GetUserNumericLock(ctx context.Context, in *numeric_logic.GetUserNumericLockReq, opts ...grpc.CallOption) (*numeric_logic.GetUserNumericLockResp, error)
	SetUserNumericLock(ctx context.Context, in *numeric_logic.SetUserNumericLockReq, opts ...grpc.CallOption) (*numeric_logic.SetUserNumericLockResp, error)
	// 获取荣誉榜单
	GetUserGloryRank(ctx context.Context, in *numeric_logic.GetUserGloryRankReq, opts ...grpc.CallOption) (*numeric_logic.GetUserGloryRankResp, error)
	// 获取用户VIP礼包信息
	GetUserVipGiftPackageInfo(ctx context.Context, in *numeric_logic.GetUserVipGiftPackageInfoReq, opts ...grpc.CallOption) (*numeric_logic.GetUserVipGiftPackageInfoResp, error)
}

type numericLogicClient struct {
	cc *grpc.ClientConn
}

func NewNumericLogicClient(cc *grpc.ClientConn) NumericLogicClient {
	return &numericLogicClient{cc}
}

func (c *numericLogicClient) GetUserRichSwitch(ctx context.Context, in *numeric_logic.GetUserRichSwitchReq, opts ...grpc.CallOption) (*numeric_logic.GetUserRichSwitchResp, error) {
	out := new(numeric_logic.GetUserRichSwitchResp)
	err := c.cc.Invoke(ctx, "/logic.NumericLogic/GetUserRichSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericLogicClient) SetUserRichSwitch(ctx context.Context, in *numeric_logic.SetUserRichSwitchReq, opts ...grpc.CallOption) (*numeric_logic.SetUserRichSwitchResp, error) {
	out := new(numeric_logic.SetUserRichSwitchResp)
	err := c.cc.Invoke(ctx, "/logic.NumericLogic/SetUserRichSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericLogicClient) GetUserNumericLock(ctx context.Context, in *numeric_logic.GetUserNumericLockReq, opts ...grpc.CallOption) (*numeric_logic.GetUserNumericLockResp, error) {
	out := new(numeric_logic.GetUserNumericLockResp)
	err := c.cc.Invoke(ctx, "/logic.NumericLogic/GetUserNumericLock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericLogicClient) SetUserNumericLock(ctx context.Context, in *numeric_logic.SetUserNumericLockReq, opts ...grpc.CallOption) (*numeric_logic.SetUserNumericLockResp, error) {
	out := new(numeric_logic.SetUserNumericLockResp)
	err := c.cc.Invoke(ctx, "/logic.NumericLogic/SetUserNumericLock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericLogicClient) GetUserGloryRank(ctx context.Context, in *numeric_logic.GetUserGloryRankReq, opts ...grpc.CallOption) (*numeric_logic.GetUserGloryRankResp, error) {
	out := new(numeric_logic.GetUserGloryRankResp)
	err := c.cc.Invoke(ctx, "/logic.NumericLogic/GetUserGloryRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericLogicClient) GetUserVipGiftPackageInfo(ctx context.Context, in *numeric_logic.GetUserVipGiftPackageInfoReq, opts ...grpc.CallOption) (*numeric_logic.GetUserVipGiftPackageInfoResp, error) {
	out := new(numeric_logic.GetUserVipGiftPackageInfoResp)
	err := c.cc.Invoke(ctx, "/logic.NumericLogic/GetUserVipGiftPackageInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NumericLogicServer is the server API for NumericLogic service.
type NumericLogicServer interface {
	// 设置页面 获取签约用户财富值控制开关状态
	GetUserRichSwitch(context.Context, *numeric_logic.GetUserRichSwitchReq) (*numeric_logic.GetUserRichSwitchResp, error)
	// 设置页面 变更签约用户财富值控制开关状态
	SetUserRichSwitch(context.Context, *numeric_logic.SetUserRichSwitchReq) (*numeric_logic.SetUserRichSwitchResp, error)
	GetUserNumericLock(context.Context, *numeric_logic.GetUserNumericLockReq) (*numeric_logic.GetUserNumericLockResp, error)
	SetUserNumericLock(context.Context, *numeric_logic.SetUserNumericLockReq) (*numeric_logic.SetUserNumericLockResp, error)
	// 获取荣誉榜单
	GetUserGloryRank(context.Context, *numeric_logic.GetUserGloryRankReq) (*numeric_logic.GetUserGloryRankResp, error)
	// 获取用户VIP礼包信息
	GetUserVipGiftPackageInfo(context.Context, *numeric_logic.GetUserVipGiftPackageInfoReq) (*numeric_logic.GetUserVipGiftPackageInfoResp, error)
}

func RegisterNumericLogicServer(s *grpc.Server, srv NumericLogicServer) {
	s.RegisterService(&_NumericLogic_serviceDesc, srv)
}

func _NumericLogic_GetUserRichSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(numeric_logic.GetUserRichSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericLogicServer).GetUserRichSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.NumericLogic/GetUserRichSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericLogicServer).GetUserRichSwitch(ctx, req.(*numeric_logic.GetUserRichSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericLogic_SetUserRichSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(numeric_logic.SetUserRichSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericLogicServer).SetUserRichSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.NumericLogic/SetUserRichSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericLogicServer).SetUserRichSwitch(ctx, req.(*numeric_logic.SetUserRichSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericLogic_GetUserNumericLock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(numeric_logic.GetUserNumericLockReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericLogicServer).GetUserNumericLock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.NumericLogic/GetUserNumericLock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericLogicServer).GetUserNumericLock(ctx, req.(*numeric_logic.GetUserNumericLockReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericLogic_SetUserNumericLock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(numeric_logic.SetUserNumericLockReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericLogicServer).SetUserNumericLock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.NumericLogic/SetUserNumericLock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericLogicServer).SetUserNumericLock(ctx, req.(*numeric_logic.SetUserNumericLockReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericLogic_GetUserGloryRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(numeric_logic.GetUserGloryRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericLogicServer).GetUserGloryRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.NumericLogic/GetUserGloryRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericLogicServer).GetUserGloryRank(ctx, req.(*numeric_logic.GetUserGloryRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericLogic_GetUserVipGiftPackageInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(numeric_logic.GetUserVipGiftPackageInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericLogicServer).GetUserVipGiftPackageInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.NumericLogic/GetUserVipGiftPackageInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericLogicServer).GetUserVipGiftPackageInfo(ctx, req.(*numeric_logic.GetUserVipGiftPackageInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _NumericLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.NumericLogic",
	HandlerType: (*NumericLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserRichSwitch",
			Handler:    _NumericLogic_GetUserRichSwitch_Handler,
		},
		{
			MethodName: "SetUserRichSwitch",
			Handler:    _NumericLogic_SetUserRichSwitch_Handler,
		},
		{
			MethodName: "GetUserNumericLock",
			Handler:    _NumericLogic_GetUserNumericLock_Handler,
		},
		{
			MethodName: "SetUserNumericLock",
			Handler:    _NumericLogic_SetUserNumericLock_Handler,
		},
		{
			MethodName: "GetUserGloryRank",
			Handler:    _NumericLogic_GetUserGloryRank_Handler,
		},
		{
			MethodName: "GetUserVipGiftPackageInfo",
			Handler:    _NumericLogic_GetUserVipGiftPackageInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/logicsvr-go/numeric-logic/numeric-logic.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/logicsvr-go/numeric-logic/numeric-logic.proto", fileDescriptor_numeric_logic_b01b4f8c1c61abd3)
}

var fileDescriptor_numeric_logic_b01b4f8c1c61abd3 = []byte{
	// 340 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x93, 0xcb, 0x4a, 0x03, 0x31,
	0x14, 0x86, 0x19, 0xb0, 0xa2, 0x83, 0x0b, 0xcd, 0xce, 0x2e, 0x0b, 0x56, 0x37, 0x4d, 0xa0, 0x22,
	0x08, 0xea, 0xc6, 0x4d, 0x11, 0x44, 0xa4, 0x41, 0x17, 0x6e, 0x4a, 0x0c, 0x69, 0x1a, 0x66, 0x3a,
	0x67, 0x9a, 0xa4, 0x2d, 0x7d, 0x02, 0x5f, 0xc5, 0xcb, 0xce, 0x8d, 0xf8, 0x14, 0xfa, 0x2c, 0xc2,
	0xec, 0x65, 0x2e, 0xb6, 0x96, 0x3a, 0x43, 0x5c, 0x0d, 0xe7, 0xcc, 0xc7, 0xff, 0xfd, 0x81, 0xc4,
	0x3f, 0xb5, 0x96, 0x8c, 0xc6, 0x8a, 0x07, 0x46, 0x85, 0x13, 0xa1, 0x49, 0x08, 0x52, 0x71, 0x33,
	0xd1, 0x2d, 0x09, 0x24, 0x1a, 0x0f, 0x85, 0x56, 0xbc, 0x95, 0xed, 0x96, 0x27, 0x1c, 0x6b, 0xb0,
	0x80, 0x6a, 0xd9, 0x50, 0x3f, 0xae, 0x08, 0x91, 0xcc, 0x8a, 0x29, 0x9b, 0x11, 0x88, 0xad, 0x82,
	0xc8, 0xfc, 0x7c, 0xf3, 0x80, 0x7a, 0xa3, 0x48, 0xed, 0xfd, 0xe1, 0xe8, 0xe5, 0x4c, 0xfb, 0xb3,
	0xe6, 0x6f, 0x5d, 0xe5, 0x3f, 0x2e, 0xd3, 0x3d, 0x02, 0x7f, 0xa7, 0x23, 0xec, 0x8d, 0x11, 0xba,
	0xab, 0xf8, 0x80, 0x4e, 0x95, 0xe5, 0x03, 0xd4, 0xc4, 0x92, 0xe1, 0xa5, 0x34, 0xbc, 0x02, 0x75,
	0xc5, 0xa8, 0xbe, 0xef, 0xc4, 0x99, 0xb8, 0xb1, 0xf9, 0xf5, 0xf1, 0x8e, 0xd7, 0x36, 0x1e, 0x13,
	0x2f, 0x15, 0x52, 0x17, 0x21, 0x75, 0x14, 0xd2, 0x2a, 0xe1, 0x53, 0xe2, 0x21, 0xed, 0xa3, 0xa2,
	0xd4, 0xfc, 0xe0, 0x3c, 0x40, 0xe5, 0xd5, 0x7f, 0x51, 0xa9, 0xf2, 0xc0, 0x0d, 0x5c, 0x38, 0x9f,
	0x73, 0x27, 0x75, 0x72, 0x52, 0x57, 0x27, 0xad, 0x74, 0xbe, 0x24, 0x1e, 0x0a, 0xfd, 0xed, 0xa2,
	0x58, 0x27, 0x04, 0x3d, 0xeb, 0xb2, 0x28, 0x40, 0x7b, 0xa5, 0xe5, 0xe7, 0x4c, 0xea, 0x6b, 0xba,
	0x60, 0x0b, 0xdb, 0x6b, 0xe2, 0xa1, 0x07, 0xcf, 0xdf, 0x2d, 0x98, 0x5b, 0x15, 0x77, 0x54, 0xdf,
	0x5e, 0x33, 0x1e, 0x30, 0x29, 0x2e, 0xa2, 0x3e, 0x20, 0x5c, 0x1a, 0xb8, 0x0a, 0xa7, 0x05, 0xc8,
	0xbf, 0xf8, 0x45, 0x93, 0xb7, 0xc4, 0x3b, 0x3f, 0xbb, 0x3b, 0x91, 0x10, 0xb2, 0x48, 0xe2, 0xa3,
	0xb6, 0xb5, 0x98, 0xc3, 0x90, 0x64, 0x77, 0x9d, 0x43, 0x48, 0x8c, 0xd0, 0x13, 0xc5, 0x85, 0x29,
	0x7f, 0x8a, 0xf7, 0xeb, 0x19, 0x7c, 0xf8, 0x1d, 0x00, 0x00, 0xff, 0xff, 0xdd, 0xbf, 0x7f, 0xf3,
	0xbd, 0x03, 0x00, 0x00,
}
