// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channel-wedding-conf/channel-wedding-conf.proto

package channel_wedding_conf // import "golang.52tt.com/protocol/services/channel-wedding-conf"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type WeddingAwardType int32

const (
	WeddingAwardType_WEDDING_AWARD_TYPE_UNSPECIFIED       WeddingAwardType = 0
	WeddingAwardType_WEDDING_AWARD_TYPE_HEADWEAR          WeddingAwardType = 1
	WeddingAwardType_WEDDING_AWARD_TYPE_VA_FOLLOW_CHANNEL WeddingAwardType = 2
	WeddingAwardType_WEDDING_AWARD_TYPE_VA_BACKGROUND     WeddingAwardType = 3
	WeddingAwardType_WEDDING_AWARD_TYPE_FELLOW_LIGATURE   WeddingAwardType = 4
)

var WeddingAwardType_name = map[int32]string{
	0: "WEDDING_AWARD_TYPE_UNSPECIFIED",
	1: "WEDDING_AWARD_TYPE_HEADWEAR",
	2: "WEDDING_AWARD_TYPE_VA_FOLLOW_CHANNEL",
	3: "WEDDING_AWARD_TYPE_VA_BACKGROUND",
	4: "WEDDING_AWARD_TYPE_FELLOW_LIGATURE",
}
var WeddingAwardType_value = map[string]int32{
	"WEDDING_AWARD_TYPE_UNSPECIFIED":       0,
	"WEDDING_AWARD_TYPE_HEADWEAR":          1,
	"WEDDING_AWARD_TYPE_VA_FOLLOW_CHANNEL": 2,
	"WEDDING_AWARD_TYPE_VA_BACKGROUND":     3,
	"WEDDING_AWARD_TYPE_FELLOW_LIGATURE":   4,
}

func (x WeddingAwardType) String() string {
	return proto.EnumName(WeddingAwardType_name, int32(x))
}
func (WeddingAwardType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{0}
}

// 婚礼场景动画枚举
type WeddingScene int32

const (
	WeddingScene_WEDDING_SCENE_UNSPECIFIED       WeddingScene = 0
	WeddingScene_WEDDING_SCENE_BRIDE_GROOM_ENTER WeddingScene = 1
	WeddingScene_WEDDING_SCENE_EXCHANGE_RING     WeddingScene = 2
	WeddingScene_WEDDING_SCENE_HIGHLIGHT         WeddingScene = 3
	WeddingScene_WEDDING_SCENE_GROUP_PHOTO       WeddingScene = 4
)

var WeddingScene_name = map[int32]string{
	0: "WEDDING_SCENE_UNSPECIFIED",
	1: "WEDDING_SCENE_BRIDE_GROOM_ENTER",
	2: "WEDDING_SCENE_EXCHANGE_RING",
	3: "WEDDING_SCENE_HIGHLIGHT",
	4: "WEDDING_SCENE_GROUP_PHOTO",
}
var WeddingScene_value = map[string]int32{
	"WEDDING_SCENE_UNSPECIFIED":       0,
	"WEDDING_SCENE_BRIDE_GROOM_ENTER": 1,
	"WEDDING_SCENE_EXCHANGE_RING":     2,
	"WEDDING_SCENE_HIGHLIGHT":         3,
	"WEDDING_SCENE_GROUP_PHOTO":       4,
}

func (x WeddingScene) String() string {
	return proto.EnumName(WeddingScene_name, int32(x))
}
func (WeddingScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{1}
}

// 获取婚礼主题配置
type GetThemeCfgReq struct {
	ThemeId              uint32   `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetThemeCfgReq) Reset()         { *m = GetThemeCfgReq{} }
func (m *GetThemeCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetThemeCfgReq) ProtoMessage()    {}
func (*GetThemeCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{0}
}
func (m *GetThemeCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetThemeCfgReq.Unmarshal(m, b)
}
func (m *GetThemeCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetThemeCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetThemeCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetThemeCfgReq.Merge(dst, src)
}
func (m *GetThemeCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetThemeCfgReq.Size(m)
}
func (m *GetThemeCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetThemeCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetThemeCfgReq proto.InternalMessageInfo

func (m *GetThemeCfgReq) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

type GetThemeCfgResp struct {
	ThemeCfg             *ThemeCfg `protobuf:"bytes,1,opt,name=theme_cfg,json=themeCfg,proto3" json:"theme_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetThemeCfgResp) Reset()         { *m = GetThemeCfgResp{} }
func (m *GetThemeCfgResp) String() string { return proto.CompactTextString(m) }
func (*GetThemeCfgResp) ProtoMessage()    {}
func (*GetThemeCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{1}
}
func (m *GetThemeCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetThemeCfgResp.Unmarshal(m, b)
}
func (m *GetThemeCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetThemeCfgResp.Marshal(b, m, deterministic)
}
func (dst *GetThemeCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetThemeCfgResp.Merge(dst, src)
}
func (m *GetThemeCfgResp) XXX_Size() int {
	return xxx_messageInfo_GetThemeCfgResp.Size(m)
}
func (m *GetThemeCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetThemeCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetThemeCfgResp proto.InternalMessageInfo

func (m *GetThemeCfgResp) GetThemeCfg() *ThemeCfg {
	if m != nil {
		return m.ThemeCfg
	}
	return nil
}

// 获取婚礼主题配置列表
type GetThemeCfgListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetThemeCfgListReq) Reset()         { *m = GetThemeCfgListReq{} }
func (m *GetThemeCfgListReq) String() string { return proto.CompactTextString(m) }
func (*GetThemeCfgListReq) ProtoMessage()    {}
func (*GetThemeCfgListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{2}
}
func (m *GetThemeCfgListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetThemeCfgListReq.Unmarshal(m, b)
}
func (m *GetThemeCfgListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetThemeCfgListReq.Marshal(b, m, deterministic)
}
func (dst *GetThemeCfgListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetThemeCfgListReq.Merge(dst, src)
}
func (m *GetThemeCfgListReq) XXX_Size() int {
	return xxx_messageInfo_GetThemeCfgListReq.Size(m)
}
func (m *GetThemeCfgListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetThemeCfgListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetThemeCfgListReq proto.InternalMessageInfo

type GetThemeCfgListResp struct {
	ThemeCfgList           []*ThemeCfg `protobuf:"bytes,1,rep,name=theme_cfg_list,json=themeCfgList,proto3" json:"theme_cfg_list,omitempty"`
	ThemeTitleSelectedIcon string      `protobuf:"bytes,2,opt,name=theme_title_selected_icon,json=themeTitleSelectedIcon,proto3" json:"theme_title_selected_icon,omitempty"`
	ThemeTitleBgIcon       string      `protobuf:"bytes,3,opt,name=theme_title_bg_icon,json=themeTitleBgIcon,proto3" json:"theme_title_bg_icon,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}    `json:"-"`
	XXX_unrecognized       []byte      `json:"-"`
	XXX_sizecache          int32       `json:"-"`
}

func (m *GetThemeCfgListResp) Reset()         { *m = GetThemeCfgListResp{} }
func (m *GetThemeCfgListResp) String() string { return proto.CompactTextString(m) }
func (*GetThemeCfgListResp) ProtoMessage()    {}
func (*GetThemeCfgListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{3}
}
func (m *GetThemeCfgListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetThemeCfgListResp.Unmarshal(m, b)
}
func (m *GetThemeCfgListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetThemeCfgListResp.Marshal(b, m, deterministic)
}
func (dst *GetThemeCfgListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetThemeCfgListResp.Merge(dst, src)
}
func (m *GetThemeCfgListResp) XXX_Size() int {
	return xxx_messageInfo_GetThemeCfgListResp.Size(m)
}
func (m *GetThemeCfgListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetThemeCfgListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetThemeCfgListResp proto.InternalMessageInfo

func (m *GetThemeCfgListResp) GetThemeCfgList() []*ThemeCfg {
	if m != nil {
		return m.ThemeCfgList
	}
	return nil
}

func (m *GetThemeCfgListResp) GetThemeTitleSelectedIcon() string {
	if m != nil {
		return m.ThemeTitleSelectedIcon
	}
	return ""
}

func (m *GetThemeCfgListResp) GetThemeTitleBgIcon() string {
	if m != nil {
		return m.ThemeTitleBgIcon
	}
	return ""
}

// 主题配置
type ThemeCfg struct {
	ThemeId                            uint32                `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	ThemeName                          string                `protobuf:"bytes,2,opt,name=theme_name,json=themeName,proto3" json:"theme_name,omitempty"`
	PriceInfo                          *WeddingPriceInfo     `protobuf:"bytes,3,opt,name=price_info,json=priceInfo,proto3" json:"price_info,omitempty"`
	ThemeRoomResource                  *ResourceCfg          `protobuf:"bytes,4,opt,name=theme_room_resource,json=themeRoomResource,proto3" json:"theme_room_resource,omitempty"`
	SceneCfgList                       []*WeddingSceneCfg    `protobuf:"bytes,5,rep,name=scene_cfg_list,json=sceneCfgList,proto3" json:"scene_cfg_list,omitempty"`
	ThemeLevelCfgList                  []*ThemeLevelCfg      `protobuf:"bytes,6,rep,name=theme_level_cfg_list,json=themeLevelCfgList,proto3" json:"theme_level_cfg_list,omitempty"`
	WeddingPreviewCfg                  *WeddingPreviewCfg    `protobuf:"bytes,7,opt,name=wedding_preview_cfg,json=weddingPreviewCfg,proto3" json:"wedding_preview_cfg,omitempty"`
	MemorialVideoResource              *ResourceCfg          `protobuf:"bytes,8,opt,name=memorial_video_resource,json=memorialVideoResource,proto3" json:"memorial_video_resource,omitempty"`
	ChairResCfg                        *ChairGameResourceCfg `protobuf:"bytes,9,opt,name=chair_res_cfg,json=chairResCfg,proto3" json:"chair_res_cfg,omitempty"`
	SelectedThemeTitleIcon             string                `protobuf:"bytes,10,opt,name=selected_theme_title_icon,json=selectedThemeTitleIcon,proto3" json:"selected_theme_title_icon,omitempty"`
	UnselectedThemeTitleIcon           string                `protobuf:"bytes,11,opt,name=unselected_theme_title_icon,json=unselectedThemeTitleIcon,proto3" json:"unselected_theme_title_icon,omitempty"`
	ExamplePhoto                       string                `protobuf:"bytes,12,opt,name=example_photo,json=examplePhoto,proto3" json:"example_photo,omitempty"`
	ThemeBackground                    string                `protobuf:"bytes,13,opt,name=theme_background,json=themeBackground,proto3" json:"theme_background,omitempty"`
	ThemePreviewText                   string                `protobuf:"bytes,14,opt,name=theme_preview_text,json=themePreviewText,proto3" json:"theme_preview_text,omitempty"`
	RewardInfoDesc                     string                `protobuf:"bytes,21,opt,name=reward_info_desc,json=rewardInfoDesc,proto3" json:"reward_info_desc,omitempty"`
	RewardInfoList                     []*FinishWeddingAward `protobuf:"bytes,15,rep,name=reward_info_list,json=rewardInfoList,proto3" json:"reward_info_list,omitempty"`
	WeddingHallComingBackground        string                `protobuf:"bytes,16,opt,name=wedding_hall_coming_background,json=weddingHallComingBackground,proto3" json:"wedding_hall_coming_background,omitempty"`
	MailLadyLeftBgIcon                 string                `protobuf:"bytes,17,opt,name=mail_lady_left_bg_icon,json=mailLadyLeftBgIcon,proto3" json:"mail_lady_left_bg_icon,omitempty"`
	MailLadyRightBgIcon                string                `protobuf:"bytes,18,opt,name=mail_lady_right_bg_icon,json=mailLadyRightBgIcon,proto3" json:"mail_lady_right_bg_icon,omitempty"`
	ThemeIcon                          string                `protobuf:"bytes,19,opt,name=theme_icon,json=themeIcon,proto3" json:"theme_icon,omitempty"`
	IsDeleted                          bool                  `protobuf:"varint,20,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	WeddingGiftIds                     []uint32              `protobuf:"varint,22,rep,packed,name=wedding_gift_ids,json=weddingGiftIds,proto3" json:"wedding_gift_ids,omitempty"`
	PreProgressPanelResourceUrl        string                `protobuf:"bytes,23,opt,name=pre_progress_panel_resource_url,json=preProgressPanelResourceUrl,proto3" json:"pre_progress_panel_resource_url,omitempty"`
	PreProgressPanelResourceMd5        string                `protobuf:"bytes,24,opt,name=pre_progress_panel_resource_md5,json=preProgressPanelResourceMd5,proto3" json:"pre_progress_panel_resource_md5,omitempty"`
	MvpSettlementResource              *ResourceCfg          `protobuf:"bytes,25,opt,name=mvp_settlement_resource,json=mvpSettlementResource,proto3" json:"mvp_settlement_resource,omitempty"`
	PreProgressPresentTargetVal        uint32                `protobuf:"varint,26,opt,name=pre_progress_present_target_val,json=preProgressPresentTargetVal,proto3" json:"pre_progress_present_target_val,omitempty"`
	PreProgressClothesExtraDurationCfg map[uint32]uint32     `protobuf:"bytes,27,rep,name=pre_progress_clothes_extra_duration_cfg,json=preProgressClothesExtraDurationCfg,proto3" json:"pre_progress_clothes_extra_duration_cfg,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral               struct{}              `json:"-"`
	XXX_unrecognized                   []byte                `json:"-"`
	XXX_sizecache                      int32                 `json:"-"`
}

func (m *ThemeCfg) Reset()         { *m = ThemeCfg{} }
func (m *ThemeCfg) String() string { return proto.CompactTextString(m) }
func (*ThemeCfg) ProtoMessage()    {}
func (*ThemeCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{4}
}
func (m *ThemeCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ThemeCfg.Unmarshal(m, b)
}
func (m *ThemeCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ThemeCfg.Marshal(b, m, deterministic)
}
func (dst *ThemeCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ThemeCfg.Merge(dst, src)
}
func (m *ThemeCfg) XXX_Size() int {
	return xxx_messageInfo_ThemeCfg.Size(m)
}
func (m *ThemeCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_ThemeCfg.DiscardUnknown(m)
}

var xxx_messageInfo_ThemeCfg proto.InternalMessageInfo

func (m *ThemeCfg) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *ThemeCfg) GetThemeName() string {
	if m != nil {
		return m.ThemeName
	}
	return ""
}

func (m *ThemeCfg) GetPriceInfo() *WeddingPriceInfo {
	if m != nil {
		return m.PriceInfo
	}
	return nil
}

func (m *ThemeCfg) GetThemeRoomResource() *ResourceCfg {
	if m != nil {
		return m.ThemeRoomResource
	}
	return nil
}

func (m *ThemeCfg) GetSceneCfgList() []*WeddingSceneCfg {
	if m != nil {
		return m.SceneCfgList
	}
	return nil
}

func (m *ThemeCfg) GetThemeLevelCfgList() []*ThemeLevelCfg {
	if m != nil {
		return m.ThemeLevelCfgList
	}
	return nil
}

func (m *ThemeCfg) GetWeddingPreviewCfg() *WeddingPreviewCfg {
	if m != nil {
		return m.WeddingPreviewCfg
	}
	return nil
}

func (m *ThemeCfg) GetMemorialVideoResource() *ResourceCfg {
	if m != nil {
		return m.MemorialVideoResource
	}
	return nil
}

func (m *ThemeCfg) GetChairResCfg() *ChairGameResourceCfg {
	if m != nil {
		return m.ChairResCfg
	}
	return nil
}

func (m *ThemeCfg) GetSelectedThemeTitleIcon() string {
	if m != nil {
		return m.SelectedThemeTitleIcon
	}
	return ""
}

func (m *ThemeCfg) GetUnselectedThemeTitleIcon() string {
	if m != nil {
		return m.UnselectedThemeTitleIcon
	}
	return ""
}

func (m *ThemeCfg) GetExamplePhoto() string {
	if m != nil {
		return m.ExamplePhoto
	}
	return ""
}

func (m *ThemeCfg) GetThemeBackground() string {
	if m != nil {
		return m.ThemeBackground
	}
	return ""
}

func (m *ThemeCfg) GetThemePreviewText() string {
	if m != nil {
		return m.ThemePreviewText
	}
	return ""
}

func (m *ThemeCfg) GetRewardInfoDesc() string {
	if m != nil {
		return m.RewardInfoDesc
	}
	return ""
}

func (m *ThemeCfg) GetRewardInfoList() []*FinishWeddingAward {
	if m != nil {
		return m.RewardInfoList
	}
	return nil
}

func (m *ThemeCfg) GetWeddingHallComingBackground() string {
	if m != nil {
		return m.WeddingHallComingBackground
	}
	return ""
}

func (m *ThemeCfg) GetMailLadyLeftBgIcon() string {
	if m != nil {
		return m.MailLadyLeftBgIcon
	}
	return ""
}

func (m *ThemeCfg) GetMailLadyRightBgIcon() string {
	if m != nil {
		return m.MailLadyRightBgIcon
	}
	return ""
}

func (m *ThemeCfg) GetThemeIcon() string {
	if m != nil {
		return m.ThemeIcon
	}
	return ""
}

func (m *ThemeCfg) GetIsDeleted() bool {
	if m != nil {
		return m.IsDeleted
	}
	return false
}

func (m *ThemeCfg) GetWeddingGiftIds() []uint32 {
	if m != nil {
		return m.WeddingGiftIds
	}
	return nil
}

func (m *ThemeCfg) GetPreProgressPanelResourceUrl() string {
	if m != nil {
		return m.PreProgressPanelResourceUrl
	}
	return ""
}

func (m *ThemeCfg) GetPreProgressPanelResourceMd5() string {
	if m != nil {
		return m.PreProgressPanelResourceMd5
	}
	return ""
}

func (m *ThemeCfg) GetMvpSettlementResource() *ResourceCfg {
	if m != nil {
		return m.MvpSettlementResource
	}
	return nil
}

func (m *ThemeCfg) GetPreProgressPresentTargetVal() uint32 {
	if m != nil {
		return m.PreProgressPresentTargetVal
	}
	return 0
}

func (m *ThemeCfg) GetPreProgressClothesExtraDurationCfg() map[uint32]uint32 {
	if m != nil {
		return m.PreProgressClothesExtraDurationCfg
	}
	return nil
}

type AwardInfo struct {
	AwardType            uint32   `protobuf:"varint,1,opt,name=award_type,json=awardType,proto3" json:"award_type,omitempty"`
	AwardId              string   `protobuf:"bytes,2,opt,name=award_id,json=awardId,proto3" json:"award_id,omitempty"`
	AwardIdFemale        string   `protobuf:"bytes,3,opt,name=award_id_female,json=awardIdFemale,proto3" json:"award_id_female,omitempty"`
	Amount               uint32   `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
	Level                uint32   `protobuf:"varint,5,opt,name=level,proto3" json:"level,omitempty"`
	LvName               string   `protobuf:"bytes,6,opt,name=lv_name,json=lvName,proto3" json:"lv_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardInfo) Reset()         { *m = AwardInfo{} }
func (m *AwardInfo) String() string { return proto.CompactTextString(m) }
func (*AwardInfo) ProtoMessage()    {}
func (*AwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{5}
}
func (m *AwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardInfo.Unmarshal(m, b)
}
func (m *AwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardInfo.Marshal(b, m, deterministic)
}
func (dst *AwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardInfo.Merge(dst, src)
}
func (m *AwardInfo) XXX_Size() int {
	return xxx_messageInfo_AwardInfo.Size(m)
}
func (m *AwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AwardInfo proto.InternalMessageInfo

func (m *AwardInfo) GetAwardType() uint32 {
	if m != nil {
		return m.AwardType
	}
	return 0
}

func (m *AwardInfo) GetAwardId() string {
	if m != nil {
		return m.AwardId
	}
	return ""
}

func (m *AwardInfo) GetAwardIdFemale() string {
	if m != nil {
		return m.AwardIdFemale
	}
	return ""
}

func (m *AwardInfo) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *AwardInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *AwardInfo) GetLvName() string {
	if m != nil {
		return m.LvName
	}
	return ""
}

type FinishWeddingAward struct {
	AwardAnimation       *ResourceCfg `protobuf:"bytes,1,opt,name=award_animation,json=awardAnimation,proto3" json:"award_animation,omitempty"`
	TopText              string       `protobuf:"bytes,2,opt,name=top_text,json=topText,proto3" json:"top_text,omitempty"`
	BottomText           string       `protobuf:"bytes,3,opt,name=bottom_text,json=bottomText,proto3" json:"bottom_text,omitempty"`
	AwardInfo            *AwardInfo   `protobuf:"bytes,4,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"` // Deprecated: Do not use.
	AwardList            []*AwardInfo `protobuf:"bytes,5,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *FinishWeddingAward) Reset()         { *m = FinishWeddingAward{} }
func (m *FinishWeddingAward) String() string { return proto.CompactTextString(m) }
func (*FinishWeddingAward) ProtoMessage()    {}
func (*FinishWeddingAward) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{6}
}
func (m *FinishWeddingAward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FinishWeddingAward.Unmarshal(m, b)
}
func (m *FinishWeddingAward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FinishWeddingAward.Marshal(b, m, deterministic)
}
func (dst *FinishWeddingAward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FinishWeddingAward.Merge(dst, src)
}
func (m *FinishWeddingAward) XXX_Size() int {
	return xxx_messageInfo_FinishWeddingAward.Size(m)
}
func (m *FinishWeddingAward) XXX_DiscardUnknown() {
	xxx_messageInfo_FinishWeddingAward.DiscardUnknown(m)
}

var xxx_messageInfo_FinishWeddingAward proto.InternalMessageInfo

func (m *FinishWeddingAward) GetAwardAnimation() *ResourceCfg {
	if m != nil {
		return m.AwardAnimation
	}
	return nil
}

func (m *FinishWeddingAward) GetTopText() string {
	if m != nil {
		return m.TopText
	}
	return ""
}

func (m *FinishWeddingAward) GetBottomText() string {
	if m != nil {
		return m.BottomText
	}
	return ""
}

// Deprecated: Do not use.
func (m *FinishWeddingAward) GetAwardInfo() *AwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

func (m *FinishWeddingAward) GetAwardList() []*AwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

type ResourceCfg struct {
	ResourceType         uint32   `protobuf:"varint,1,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	ResourceUrl          string   `protobuf:"bytes,2,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	ResourceMd5          string   `protobuf:"bytes,3,opt,name=resource_md5,json=resourceMd5,proto3" json:"resource_md5,omitempty"`
	ResourcePng          string   `protobuf:"bytes,4,opt,name=resource_png,json=resourcePng,proto3" json:"resource_png,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResourceCfg) Reset()         { *m = ResourceCfg{} }
func (m *ResourceCfg) String() string { return proto.CompactTextString(m) }
func (*ResourceCfg) ProtoMessage()    {}
func (*ResourceCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{7}
}
func (m *ResourceCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResourceCfg.Unmarshal(m, b)
}
func (m *ResourceCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResourceCfg.Marshal(b, m, deterministic)
}
func (dst *ResourceCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResourceCfg.Merge(dst, src)
}
func (m *ResourceCfg) XXX_Size() int {
	return xxx_messageInfo_ResourceCfg.Size(m)
}
func (m *ResourceCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_ResourceCfg.DiscardUnknown(m)
}

var xxx_messageInfo_ResourceCfg proto.InternalMessageInfo

func (m *ResourceCfg) GetResourceType() uint32 {
	if m != nil {
		return m.ResourceType
	}
	return 0
}

func (m *ResourceCfg) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *ResourceCfg) GetResourceMd5() string {
	if m != nil {
		return m.ResourceMd5
	}
	return ""
}

func (m *ResourceCfg) GetResourcePng() string {
	if m != nil {
		return m.ResourcePng
	}
	return ""
}

// 婚礼场景动画配置
type WeddingSceneCfg struct {
	Scene                uint32                 `protobuf:"varint,1,opt,name=scene,proto3" json:"scene,omitempty"`
	Resource             *ResourceCfg           `protobuf:"bytes,2,opt,name=resource,proto3" json:"resource,omitempty"`
	BoneCfgList          []*WeddingSceneBoneCfg `protobuf:"bytes,3,rep,name=bone_cfg_list,json=boneCfgList,proto3" json:"bone_cfg_list,omitempty"`
	SceneIcon            string                 `protobuf:"bytes,4,opt,name=scene_icon,json=sceneIcon,proto3" json:"scene_icon,omitempty"`
	ClipImSmallPic       string                 `protobuf:"bytes,5,opt,name=clip_im_small_pic,json=clipImSmallPic,proto3" json:"clip_im_small_pic,omitempty"`
	ClipDefaultPic       string                 `protobuf:"bytes,6,opt,name=clip_default_pic,json=clipDefaultPic,proto3" json:"clip_default_pic,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *WeddingSceneCfg) Reset()         { *m = WeddingSceneCfg{} }
func (m *WeddingSceneCfg) String() string { return proto.CompactTextString(m) }
func (*WeddingSceneCfg) ProtoMessage()    {}
func (*WeddingSceneCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{8}
}
func (m *WeddingSceneCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingSceneCfg.Unmarshal(m, b)
}
func (m *WeddingSceneCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingSceneCfg.Marshal(b, m, deterministic)
}
func (dst *WeddingSceneCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingSceneCfg.Merge(dst, src)
}
func (m *WeddingSceneCfg) XXX_Size() int {
	return xxx_messageInfo_WeddingSceneCfg.Size(m)
}
func (m *WeddingSceneCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingSceneCfg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingSceneCfg proto.InternalMessageInfo

func (m *WeddingSceneCfg) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *WeddingSceneCfg) GetResource() *ResourceCfg {
	if m != nil {
		return m.Resource
	}
	return nil
}

func (m *WeddingSceneCfg) GetBoneCfgList() []*WeddingSceneBoneCfg {
	if m != nil {
		return m.BoneCfgList
	}
	return nil
}

func (m *WeddingSceneCfg) GetSceneIcon() string {
	if m != nil {
		return m.SceneIcon
	}
	return ""
}

func (m *WeddingSceneCfg) GetClipImSmallPic() string {
	if m != nil {
		return m.ClipImSmallPic
	}
	return ""
}

func (m *WeddingSceneCfg) GetClipDefaultPic() string {
	if m != nil {
		return m.ClipDefaultPic
	}
	return ""
}

type WeddingScenePreview struct {
	SceneAnimation       *ResourceCfg `protobuf:"bytes,1,opt,name=scene_animation,json=sceneAnimation,proto3" json:"scene_animation,omitempty"`
	SmallIcon            string       `protobuf:"bytes,2,opt,name=small_icon,json=smallIcon,proto3" json:"small_icon,omitempty"`
	ZoomAnimation        *ResourceCfg `protobuf:"bytes,3,opt,name=zoom_animation,json=zoomAnimation,proto3" json:"zoom_animation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *WeddingScenePreview) Reset()         { *m = WeddingScenePreview{} }
func (m *WeddingScenePreview) String() string { return proto.CompactTextString(m) }
func (*WeddingScenePreview) ProtoMessage()    {}
func (*WeddingScenePreview) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{9}
}
func (m *WeddingScenePreview) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingScenePreview.Unmarshal(m, b)
}
func (m *WeddingScenePreview) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingScenePreview.Marshal(b, m, deterministic)
}
func (dst *WeddingScenePreview) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingScenePreview.Merge(dst, src)
}
func (m *WeddingScenePreview) XXX_Size() int {
	return xxx_messageInfo_WeddingScenePreview.Size(m)
}
func (m *WeddingScenePreview) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingScenePreview.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingScenePreview proto.InternalMessageInfo

func (m *WeddingScenePreview) GetSceneAnimation() *ResourceCfg {
	if m != nil {
		return m.SceneAnimation
	}
	return nil
}

func (m *WeddingScenePreview) GetSmallIcon() string {
	if m != nil {
		return m.SmallIcon
	}
	return ""
}

func (m *WeddingScenePreview) GetZoomAnimation() *ResourceCfg {
	if m != nil {
		return m.ZoomAnimation
	}
	return nil
}

type WeddingSceneBoneCfg struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	SeqIndex             uint32   `protobuf:"varint,2,opt,name=seq_index,json=seqIndex,proto3" json:"seq_index,omitempty"`
	AnimationName        string   `protobuf:"bytes,3,opt,name=animation_name,json=animationName,proto3" json:"animation_name,omitempty"`
	BoneId               uint32   `protobuf:"varint,4,opt,name=bone_id,json=boneId,proto3" json:"bone_id,omitempty"`
	BaseBoneId           uint32   `protobuf:"varint,5,opt,name=base_bone_id,json=baseBoneId,proto3" json:"base_bone_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingSceneBoneCfg) Reset()         { *m = WeddingSceneBoneCfg{} }
func (m *WeddingSceneBoneCfg) String() string { return proto.CompactTextString(m) }
func (*WeddingSceneBoneCfg) ProtoMessage()    {}
func (*WeddingSceneBoneCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{10}
}
func (m *WeddingSceneBoneCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingSceneBoneCfg.Unmarshal(m, b)
}
func (m *WeddingSceneBoneCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingSceneBoneCfg.Marshal(b, m, deterministic)
}
func (dst *WeddingSceneBoneCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingSceneBoneCfg.Merge(dst, src)
}
func (m *WeddingSceneBoneCfg) XXX_Size() int {
	return xxx_messageInfo_WeddingSceneBoneCfg.Size(m)
}
func (m *WeddingSceneBoneCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingSceneBoneCfg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingSceneBoneCfg proto.InternalMessageInfo

func (m *WeddingSceneBoneCfg) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *WeddingSceneBoneCfg) GetSeqIndex() uint32 {
	if m != nil {
		return m.SeqIndex
	}
	return 0
}

func (m *WeddingSceneBoneCfg) GetAnimationName() string {
	if m != nil {
		return m.AnimationName
	}
	return ""
}

func (m *WeddingSceneBoneCfg) GetBoneId() uint32 {
	if m != nil {
		return m.BoneId
	}
	return 0
}

func (m *WeddingSceneBoneCfg) GetBaseBoneId() uint32 {
	if m != nil {
		return m.BaseBoneId
	}
	return 0
}

type WeddingPriceInfo struct {
	Price                uint32              `protobuf:"varint,1,opt,name=price,proto3" json:"price,omitempty"`
	PriceType            uint32              `protobuf:"varint,2,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	NormalTimePrice      []*WeddingPriceItem `protobuf:"bytes,3,rep,name=normal_time_price,json=normalTimePrice,proto3" json:"normal_time_price,omitempty"`
	HotTimePrice         []*WeddingPriceItem `protobuf:"bytes,4,rep,name=hot_time_price,json=hotTimePrice,proto3" json:"hot_time_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *WeddingPriceInfo) Reset()         { *m = WeddingPriceInfo{} }
func (m *WeddingPriceInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingPriceInfo) ProtoMessage()    {}
func (*WeddingPriceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{11}
}
func (m *WeddingPriceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPriceInfo.Unmarshal(m, b)
}
func (m *WeddingPriceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPriceInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingPriceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPriceInfo.Merge(dst, src)
}
func (m *WeddingPriceInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingPriceInfo.Size(m)
}
func (m *WeddingPriceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPriceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPriceInfo proto.InternalMessageInfo

func (m *WeddingPriceInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *WeddingPriceInfo) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *WeddingPriceInfo) GetNormalTimePrice() []*WeddingPriceItem {
	if m != nil {
		return m.NormalTimePrice
	}
	return nil
}

func (m *WeddingPriceInfo) GetHotTimePrice() []*WeddingPriceItem {
	if m != nil {
		return m.HotTimePrice
	}
	return nil
}

type WeddingPriceItem struct {
	Price                uint32   `protobuf:"varint,1,opt,name=price,proto3" json:"price,omitempty"`
	GiftId               uint32   `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingPriceItem) Reset()         { *m = WeddingPriceItem{} }
func (m *WeddingPriceItem) String() string { return proto.CompactTextString(m) }
func (*WeddingPriceItem) ProtoMessage()    {}
func (*WeddingPriceItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{12}
}
func (m *WeddingPriceItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPriceItem.Unmarshal(m, b)
}
func (m *WeddingPriceItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPriceItem.Marshal(b, m, deterministic)
}
func (dst *WeddingPriceItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPriceItem.Merge(dst, src)
}
func (m *WeddingPriceItem) XXX_Size() int {
	return xxx_messageInfo_WeddingPriceItem.Size(m)
}
func (m *WeddingPriceItem) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPriceItem.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPriceItem proto.InternalMessageInfo

func (m *WeddingPriceItem) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *WeddingPriceItem) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type ThemeLevelCfg struct {
	Level                             uint32                    `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	RoomBackgroundPicture             string                    `protobuf:"bytes,2,opt,name=room_background_picture,json=roomBackgroundPicture,proto3" json:"room_background_picture,omitempty"`
	RoomBackgroundMp4Url              string                    `protobuf:"bytes,3,opt,name=room_background_mp4_url,json=roomBackgroundMp4Url,proto3" json:"room_background_mp4_url,omitempty"`
	GuestDressCfgMap                  map[uint32]*GuestDressCfg `protobuf:"bytes,4,rep,name=guest_dress_cfg_map,json=guestDressCfgMap,proto3" json:"guest_dress_cfg_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	WeddingPreview                    *WeddingScenePreview      `protobuf:"bytes,5,opt,name=wedding_preview,json=weddingPreview,proto3" json:"wedding_preview,omitempty"`
	WeddingHallGoingBackground        string                    `protobuf:"bytes,6,opt,name=wedding_hall_going_background,json=weddingHallGoingBackground,proto3" json:"wedding_hall_going_background,omitempty"`
	CertificatePic                    string                    `protobuf:"bytes,7,opt,name=certificate_pic,json=certificatePic,proto3" json:"certificate_pic,omitempty"`
	PresentId                         uint32                    `protobuf:"varint,8,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	PresentDay                        uint32                    `protobuf:"varint,9,opt,name=present_day,json=presentDay,proto3" json:"present_day,omitempty"`
	SpecialBackgroundPicture          string                    `protobuf:"bytes,10,opt,name=special_background_picture,json=specialBackgroundPicture,proto3" json:"special_background_picture,omitempty"`
	SpecialBackgroundMp4Url           string                    `protobuf:"bytes,11,opt,name=special_background_mp4_url,json=specialBackgroundMp4Url,proto3" json:"special_background_mp4_url,omitempty"`
	FellowSpaceWeddingElement         string                    `protobuf:"bytes,12,opt,name=fellow_space_wedding_element,json=fellowSpaceWeddingElement,proto3" json:"fellow_space_wedding_element,omitempty"`
	FellowSpaceWeddingBackground      string                    `protobuf:"bytes,13,opt,name=fellow_space_wedding_background,json=fellowSpaceWeddingBackground,proto3" json:"fellow_space_wedding_background,omitempty"`
	FellowHouseSpaceWeddingBackground string                    `protobuf:"bytes,14,opt,name=fellow_house_space_wedding_background,json=fellowHouseSpaceWeddingBackground,proto3" json:"fellow_house_space_wedding_background,omitempty"`
	FellowSpaceWeddingColor           string                    `protobuf:"bytes,15,opt,name=fellow_space_wedding_color,json=fellowSpaceWeddingColor,proto3" json:"fellow_space_wedding_color,omitempty"`
	FellowSpaceWeddingCertificate     string                    `protobuf:"bytes,16,opt,name=fellow_space_wedding_certificate,json=fellowSpaceWeddingCertificate,proto3" json:"fellow_space_wedding_certificate,omitempty"`
	BuyWeddingPresentId               uint32                    `protobuf:"varint,17,opt,name=buy_wedding_present_id,json=buyWeddingPresentId,proto3" json:"buy_wedding_present_id,omitempty"`
	XXX_NoUnkeyedLiteral              struct{}                  `json:"-"`
	XXX_unrecognized                  []byte                    `json:"-"`
	XXX_sizecache                     int32                     `json:"-"`
}

func (m *ThemeLevelCfg) Reset()         { *m = ThemeLevelCfg{} }
func (m *ThemeLevelCfg) String() string { return proto.CompactTextString(m) }
func (*ThemeLevelCfg) ProtoMessage()    {}
func (*ThemeLevelCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{13}
}
func (m *ThemeLevelCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ThemeLevelCfg.Unmarshal(m, b)
}
func (m *ThemeLevelCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ThemeLevelCfg.Marshal(b, m, deterministic)
}
func (dst *ThemeLevelCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ThemeLevelCfg.Merge(dst, src)
}
func (m *ThemeLevelCfg) XXX_Size() int {
	return xxx_messageInfo_ThemeLevelCfg.Size(m)
}
func (m *ThemeLevelCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_ThemeLevelCfg.DiscardUnknown(m)
}

var xxx_messageInfo_ThemeLevelCfg proto.InternalMessageInfo

func (m *ThemeLevelCfg) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *ThemeLevelCfg) GetRoomBackgroundPicture() string {
	if m != nil {
		return m.RoomBackgroundPicture
	}
	return ""
}

func (m *ThemeLevelCfg) GetRoomBackgroundMp4Url() string {
	if m != nil {
		return m.RoomBackgroundMp4Url
	}
	return ""
}

func (m *ThemeLevelCfg) GetGuestDressCfgMap() map[uint32]*GuestDressCfg {
	if m != nil {
		return m.GuestDressCfgMap
	}
	return nil
}

func (m *ThemeLevelCfg) GetWeddingPreview() *WeddingScenePreview {
	if m != nil {
		return m.WeddingPreview
	}
	return nil
}

func (m *ThemeLevelCfg) GetWeddingHallGoingBackground() string {
	if m != nil {
		return m.WeddingHallGoingBackground
	}
	return ""
}

func (m *ThemeLevelCfg) GetCertificatePic() string {
	if m != nil {
		return m.CertificatePic
	}
	return ""
}

func (m *ThemeLevelCfg) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *ThemeLevelCfg) GetPresentDay() uint32 {
	if m != nil {
		return m.PresentDay
	}
	return 0
}

func (m *ThemeLevelCfg) GetSpecialBackgroundPicture() string {
	if m != nil {
		return m.SpecialBackgroundPicture
	}
	return ""
}

func (m *ThemeLevelCfg) GetSpecialBackgroundMp4Url() string {
	if m != nil {
		return m.SpecialBackgroundMp4Url
	}
	return ""
}

func (m *ThemeLevelCfg) GetFellowSpaceWeddingElement() string {
	if m != nil {
		return m.FellowSpaceWeddingElement
	}
	return ""
}

func (m *ThemeLevelCfg) GetFellowSpaceWeddingBackground() string {
	if m != nil {
		return m.FellowSpaceWeddingBackground
	}
	return ""
}

func (m *ThemeLevelCfg) GetFellowHouseSpaceWeddingBackground() string {
	if m != nil {
		return m.FellowHouseSpaceWeddingBackground
	}
	return ""
}

func (m *ThemeLevelCfg) GetFellowSpaceWeddingColor() string {
	if m != nil {
		return m.FellowSpaceWeddingColor
	}
	return ""
}

func (m *ThemeLevelCfg) GetFellowSpaceWeddingCertificate() string {
	if m != nil {
		return m.FellowSpaceWeddingCertificate
	}
	return ""
}

func (m *ThemeLevelCfg) GetBuyWeddingPresentId() uint32 {
	if m != nil {
		return m.BuyWeddingPresentId
	}
	return 0
}

// 抢椅子 椅子资源配置
type ChairGameResourceCfg struct {
	ChairPic             string   `protobuf:"bytes,1,opt,name=chair_pic,json=chairPic,proto3" json:"chair_pic,omitempty"`
	SittingPoseFemaleId  uint32   `protobuf:"varint,2,opt,name=sitting_pose_female_id,json=sittingPoseFemaleId,proto3" json:"sitting_pose_female_id,omitempty"`
	SittingPoseMaleId    uint32   `protobuf:"varint,3,opt,name=sitting_pose_male_id,json=sittingPoseMaleId,proto3" json:"sitting_pose_male_id,omitempty"`
	StandbyFemaleId      uint32   `protobuf:"varint,4,opt,name=standby_female_id,json=standbyFemaleId,proto3" json:"standby_female_id,omitempty"`
	StandbyMaleId        uint32   `protobuf:"varint,5,opt,name=standby_male_id,json=standbyMaleId,proto3" json:"standby_male_id,omitempty"`
	FailFemaleIds        []uint32 `protobuf:"varint,6,rep,packed,name=fail_female_ids,json=failFemaleIds,proto3" json:"fail_female_ids,omitempty"`
	FailMaleIds          []uint32 `protobuf:"varint,7,rep,packed,name=fail_male_ids,json=failMaleIds,proto3" json:"fail_male_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChairGameResourceCfg) Reset()         { *m = ChairGameResourceCfg{} }
func (m *ChairGameResourceCfg) String() string { return proto.CompactTextString(m) }
func (*ChairGameResourceCfg) ProtoMessage()    {}
func (*ChairGameResourceCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{14}
}
func (m *ChairGameResourceCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChairGameResourceCfg.Unmarshal(m, b)
}
func (m *ChairGameResourceCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChairGameResourceCfg.Marshal(b, m, deterministic)
}
func (dst *ChairGameResourceCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChairGameResourceCfg.Merge(dst, src)
}
func (m *ChairGameResourceCfg) XXX_Size() int {
	return xxx_messageInfo_ChairGameResourceCfg.Size(m)
}
func (m *ChairGameResourceCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_ChairGameResourceCfg.DiscardUnknown(m)
}

var xxx_messageInfo_ChairGameResourceCfg proto.InternalMessageInfo

func (m *ChairGameResourceCfg) GetChairPic() string {
	if m != nil {
		return m.ChairPic
	}
	return ""
}

func (m *ChairGameResourceCfg) GetSittingPoseFemaleId() uint32 {
	if m != nil {
		return m.SittingPoseFemaleId
	}
	return 0
}

func (m *ChairGameResourceCfg) GetSittingPoseMaleId() uint32 {
	if m != nil {
		return m.SittingPoseMaleId
	}
	return 0
}

func (m *ChairGameResourceCfg) GetStandbyFemaleId() uint32 {
	if m != nil {
		return m.StandbyFemaleId
	}
	return 0
}

func (m *ChairGameResourceCfg) GetStandbyMaleId() uint32 {
	if m != nil {
		return m.StandbyMaleId
	}
	return 0
}

func (m *ChairGameResourceCfg) GetFailFemaleIds() []uint32 {
	if m != nil {
		return m.FailFemaleIds
	}
	return nil
}

func (m *ChairGameResourceCfg) GetFailMaleIds() []uint32 {
	if m != nil {
		return m.FailMaleIds
	}
	return nil
}

// 嘉宾服装配置
type GuestDressCfg struct {
	DressText              string        `protobuf:"bytes,1,opt,name=dress_text,json=dressText,proto3" json:"dress_text,omitempty"`
	SuitCfg                *GuestSuitCfg `protobuf:"bytes,2,opt,name=suit_cfg,json=suitCfg,proto3" json:"suit_cfg,omitempty"`
	WeddingDress           *ResourceCfg  `protobuf:"bytes,3,opt,name=wedding_dress,json=weddingDress,proto3" json:"wedding_dress,omitempty"`
	SmallIcon              string        `protobuf:"bytes,4,opt,name=small_icon,json=smallIcon,proto3" json:"small_icon,omitempty"`
	ClothesUpgradePopupPng string        `protobuf:"bytes,5,opt,name=clothes_upgrade_popup_png,json=clothesUpgradePopupPng,proto3" json:"clothes_upgrade_popup_png,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}      `json:"-"`
	XXX_unrecognized       []byte        `json:"-"`
	XXX_sizecache          int32         `json:"-"`
}

func (m *GuestDressCfg) Reset()         { *m = GuestDressCfg{} }
func (m *GuestDressCfg) String() string { return proto.CompactTextString(m) }
func (*GuestDressCfg) ProtoMessage()    {}
func (*GuestDressCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{15}
}
func (m *GuestDressCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuestDressCfg.Unmarshal(m, b)
}
func (m *GuestDressCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuestDressCfg.Marshal(b, m, deterministic)
}
func (dst *GuestDressCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuestDressCfg.Merge(dst, src)
}
func (m *GuestDressCfg) XXX_Size() int {
	return xxx_messageInfo_GuestDressCfg.Size(m)
}
func (m *GuestDressCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_GuestDressCfg.DiscardUnknown(m)
}

var xxx_messageInfo_GuestDressCfg proto.InternalMessageInfo

func (m *GuestDressCfg) GetDressText() string {
	if m != nil {
		return m.DressText
	}
	return ""
}

func (m *GuestDressCfg) GetSuitCfg() *GuestSuitCfg {
	if m != nil {
		return m.SuitCfg
	}
	return nil
}

func (m *GuestDressCfg) GetWeddingDress() *ResourceCfg {
	if m != nil {
		return m.WeddingDress
	}
	return nil
}

func (m *GuestDressCfg) GetSmallIcon() string {
	if m != nil {
		return m.SmallIcon
	}
	return ""
}

func (m *GuestDressCfg) GetClothesUpgradePopupPng() string {
	if m != nil {
		return m.ClothesUpgradePopupPng
	}
	return ""
}

// 套装配置
type GuestSuitCfg struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string   `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	ItemIds              []uint32 `protobuf:"varint,3,rep,packed,name=item_ids,json=itemIds,proto3" json:"item_ids,omitempty"`
	DurationSec          uint32   `protobuf:"varint,4,opt,name=duration_sec,json=durationSec,proto3" json:"duration_sec,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuestSuitCfg) Reset()         { *m = GuestSuitCfg{} }
func (m *GuestSuitCfg) String() string { return proto.CompactTextString(m) }
func (*GuestSuitCfg) ProtoMessage()    {}
func (*GuestSuitCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{16}
}
func (m *GuestSuitCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuestSuitCfg.Unmarshal(m, b)
}
func (m *GuestSuitCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuestSuitCfg.Marshal(b, m, deterministic)
}
func (dst *GuestSuitCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuestSuitCfg.Merge(dst, src)
}
func (m *GuestSuitCfg) XXX_Size() int {
	return xxx_messageInfo_GuestSuitCfg.Size(m)
}
func (m *GuestSuitCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_GuestSuitCfg.DiscardUnknown(m)
}

var xxx_messageInfo_GuestSuitCfg proto.InternalMessageInfo

func (m *GuestSuitCfg) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GuestSuitCfg) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *GuestSuitCfg) GetItemIds() []uint32 {
	if m != nil {
		return m.ItemIds
	}
	return nil
}

func (m *GuestSuitCfg) GetDurationSec() uint32 {
	if m != nil {
		return m.DurationSec
	}
	return 0
}

// 婚礼预告配置
type WeddingPreviewCfg struct {
	Resource             *ResourceCfg `protobuf:"bytes,1,opt,name=resource,proto3" json:"resource,omitempty"`
	CpBoneId             uint32       `protobuf:"varint,2,opt,name=cp_bone_id,json=cpBoneId,proto3" json:"cp_bone_id,omitempty"`
	ItemIds              []uint32     `protobuf:"varint,3,rep,packed,name=item_ids,json=itemIds,proto3" json:"item_ids,omitempty"`
	BaseCpBoneId         uint32       `protobuf:"varint,4,opt,name=base_cp_bone_id,json=baseCpBoneId,proto3" json:"base_cp_bone_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *WeddingPreviewCfg) Reset()         { *m = WeddingPreviewCfg{} }
func (m *WeddingPreviewCfg) String() string { return proto.CompactTextString(m) }
func (*WeddingPreviewCfg) ProtoMessage()    {}
func (*WeddingPreviewCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{17}
}
func (m *WeddingPreviewCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPreviewCfg.Unmarshal(m, b)
}
func (m *WeddingPreviewCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPreviewCfg.Marshal(b, m, deterministic)
}
func (dst *WeddingPreviewCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPreviewCfg.Merge(dst, src)
}
func (m *WeddingPreviewCfg) XXX_Size() int {
	return xxx_messageInfo_WeddingPreviewCfg.Size(m)
}
func (m *WeddingPreviewCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPreviewCfg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPreviewCfg proto.InternalMessageInfo

func (m *WeddingPreviewCfg) GetResource() *ResourceCfg {
	if m != nil {
		return m.Resource
	}
	return nil
}

func (m *WeddingPreviewCfg) GetCpBoneId() uint32 {
	if m != nil {
		return m.CpBoneId
	}
	return 0
}

func (m *WeddingPreviewCfg) GetItemIds() []uint32 {
	if m != nil {
		return m.ItemIds
	}
	return nil
}

func (m *WeddingPreviewCfg) GetBaseCpBoneId() uint32 {
	if m != nil {
		return m.BaseCpBoneId
	}
	return 0
}

type WeddingRankBg struct {
	Lv                   uint32   `protobuf:"varint,1,opt,name=lv,proto3" json:"lv,omitempty"`
	BgUrl                string   `protobuf:"bytes,2,opt,name=bg_url,json=bgUrl,proto3" json:"bg_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingRankBg) Reset()         { *m = WeddingRankBg{} }
func (m *WeddingRankBg) String() string { return proto.CompactTextString(m) }
func (*WeddingRankBg) ProtoMessage()    {}
func (*WeddingRankBg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{18}
}
func (m *WeddingRankBg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingRankBg.Unmarshal(m, b)
}
func (m *WeddingRankBg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingRankBg.Marshal(b, m, deterministic)
}
func (dst *WeddingRankBg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingRankBg.Merge(dst, src)
}
func (m *WeddingRankBg) XXX_Size() int {
	return xxx_messageInfo_WeddingRankBg.Size(m)
}
func (m *WeddingRankBg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingRankBg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingRankBg proto.InternalMessageInfo

func (m *WeddingRankBg) GetLv() uint32 {
	if m != nil {
		return m.Lv
	}
	return 0
}

func (m *WeddingRankBg) GetBgUrl() string {
	if m != nil {
		return m.BgUrl
	}
	return ""
}

type WeddingRankBgList struct {
	WeddingRankBgList    []*WeddingRankBg `protobuf:"bytes,1,rep,name=wedding_rank_bg_list,json=weddingRankBgList,proto3" json:"wedding_rank_bg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *WeddingRankBgList) Reset()         { *m = WeddingRankBgList{} }
func (m *WeddingRankBgList) String() string { return proto.CompactTextString(m) }
func (*WeddingRankBgList) ProtoMessage()    {}
func (*WeddingRankBgList) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{19}
}
func (m *WeddingRankBgList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingRankBgList.Unmarshal(m, b)
}
func (m *WeddingRankBgList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingRankBgList.Marshal(b, m, deterministic)
}
func (dst *WeddingRankBgList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingRankBgList.Merge(dst, src)
}
func (m *WeddingRankBgList) XXX_Size() int {
	return xxx_messageInfo_WeddingRankBgList.Size(m)
}
func (m *WeddingRankBgList) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingRankBgList.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingRankBgList proto.InternalMessageInfo

func (m *WeddingRankBgList) GetWeddingRankBgList() []*WeddingRankBg {
	if m != nil {
		return m.WeddingRankBgList
	}
	return nil
}

type GetAllWeddingRankBackgroundReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllWeddingRankBackgroundReq) Reset()         { *m = GetAllWeddingRankBackgroundReq{} }
func (m *GetAllWeddingRankBackgroundReq) String() string { return proto.CompactTextString(m) }
func (*GetAllWeddingRankBackgroundReq) ProtoMessage()    {}
func (*GetAllWeddingRankBackgroundReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{20}
}
func (m *GetAllWeddingRankBackgroundReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllWeddingRankBackgroundReq.Unmarshal(m, b)
}
func (m *GetAllWeddingRankBackgroundReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllWeddingRankBackgroundReq.Marshal(b, m, deterministic)
}
func (dst *GetAllWeddingRankBackgroundReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllWeddingRankBackgroundReq.Merge(dst, src)
}
func (m *GetAllWeddingRankBackgroundReq) XXX_Size() int {
	return xxx_messageInfo_GetAllWeddingRankBackgroundReq.Size(m)
}
func (m *GetAllWeddingRankBackgroundReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllWeddingRankBackgroundReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllWeddingRankBackgroundReq proto.InternalMessageInfo

type GetAllWeddingRankBackgroundResp struct {
	WeddingRankBgMap     map[uint32]*WeddingRankBgList `protobuf:"bytes,1,rep,name=wedding_rank_bg_map,json=weddingRankBgMap,proto3" json:"wedding_rank_bg_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetAllWeddingRankBackgroundResp) Reset()         { *m = GetAllWeddingRankBackgroundResp{} }
func (m *GetAllWeddingRankBackgroundResp) String() string { return proto.CompactTextString(m) }
func (*GetAllWeddingRankBackgroundResp) ProtoMessage()    {}
func (*GetAllWeddingRankBackgroundResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{21}
}
func (m *GetAllWeddingRankBackgroundResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllWeddingRankBackgroundResp.Unmarshal(m, b)
}
func (m *GetAllWeddingRankBackgroundResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllWeddingRankBackgroundResp.Marshal(b, m, deterministic)
}
func (dst *GetAllWeddingRankBackgroundResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllWeddingRankBackgroundResp.Merge(dst, src)
}
func (m *GetAllWeddingRankBackgroundResp) XXX_Size() int {
	return xxx_messageInfo_GetAllWeddingRankBackgroundResp.Size(m)
}
func (m *GetAllWeddingRankBackgroundResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllWeddingRankBackgroundResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllWeddingRankBackgroundResp proto.InternalMessageInfo

func (m *GetAllWeddingRankBackgroundResp) GetWeddingRankBgMap() map[uint32]*WeddingRankBgList {
	if m != nil {
		return m.WeddingRankBgMap
	}
	return nil
}

type GetThemeFinishedAwardCfgReq struct {
	ThemeId              uint32   `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetThemeFinishedAwardCfgReq) Reset()         { *m = GetThemeFinishedAwardCfgReq{} }
func (m *GetThemeFinishedAwardCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetThemeFinishedAwardCfgReq) ProtoMessage()    {}
func (*GetThemeFinishedAwardCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{22}
}
func (m *GetThemeFinishedAwardCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetThemeFinishedAwardCfgReq.Unmarshal(m, b)
}
func (m *GetThemeFinishedAwardCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetThemeFinishedAwardCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetThemeFinishedAwardCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetThemeFinishedAwardCfgReq.Merge(dst, src)
}
func (m *GetThemeFinishedAwardCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetThemeFinishedAwardCfgReq.Size(m)
}
func (m *GetThemeFinishedAwardCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetThemeFinishedAwardCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetThemeFinishedAwardCfgReq proto.InternalMessageInfo

func (m *GetThemeFinishedAwardCfgReq) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

type FinishWeddingAwardList struct {
	ThemeId              uint32                `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	AwardList            []*FinishWeddingAward `protobuf:"bytes,2,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *FinishWeddingAwardList) Reset()         { *m = FinishWeddingAwardList{} }
func (m *FinishWeddingAwardList) String() string { return proto.CompactTextString(m) }
func (*FinishWeddingAwardList) ProtoMessage()    {}
func (*FinishWeddingAwardList) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{23}
}
func (m *FinishWeddingAwardList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FinishWeddingAwardList.Unmarshal(m, b)
}
func (m *FinishWeddingAwardList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FinishWeddingAwardList.Marshal(b, m, deterministic)
}
func (dst *FinishWeddingAwardList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FinishWeddingAwardList.Merge(dst, src)
}
func (m *FinishWeddingAwardList) XXX_Size() int {
	return xxx_messageInfo_FinishWeddingAwardList.Size(m)
}
func (m *FinishWeddingAwardList) XXX_DiscardUnknown() {
	xxx_messageInfo_FinishWeddingAwardList.DiscardUnknown(m)
}

var xxx_messageInfo_FinishWeddingAwardList proto.InternalMessageInfo

func (m *FinishWeddingAwardList) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *FinishWeddingAwardList) GetAwardList() []*FinishWeddingAward {
	if m != nil {
		return m.AwardList
	}
	return nil
}

type GetThemeFinishedAwardCfgResp struct {
	ThemeFinishedAwardMap map[uint32]*FinishWeddingAwardList `protobuf:"bytes,1,rep,name=theme_finished_award_map,json=themeFinishedAwardMap,proto3" json:"theme_finished_award_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral  struct{}                           `json:"-"`
	XXX_unrecognized      []byte                             `json:"-"`
	XXX_sizecache         int32                              `json:"-"`
}

func (m *GetThemeFinishedAwardCfgResp) Reset()         { *m = GetThemeFinishedAwardCfgResp{} }
func (m *GetThemeFinishedAwardCfgResp) String() string { return proto.CompactTextString(m) }
func (*GetThemeFinishedAwardCfgResp) ProtoMessage()    {}
func (*GetThemeFinishedAwardCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e, []int{24}
}
func (m *GetThemeFinishedAwardCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetThemeFinishedAwardCfgResp.Unmarshal(m, b)
}
func (m *GetThemeFinishedAwardCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetThemeFinishedAwardCfgResp.Marshal(b, m, deterministic)
}
func (dst *GetThemeFinishedAwardCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetThemeFinishedAwardCfgResp.Merge(dst, src)
}
func (m *GetThemeFinishedAwardCfgResp) XXX_Size() int {
	return xxx_messageInfo_GetThemeFinishedAwardCfgResp.Size(m)
}
func (m *GetThemeFinishedAwardCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetThemeFinishedAwardCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetThemeFinishedAwardCfgResp proto.InternalMessageInfo

func (m *GetThemeFinishedAwardCfgResp) GetThemeFinishedAwardMap() map[uint32]*FinishWeddingAwardList {
	if m != nil {
		return m.ThemeFinishedAwardMap
	}
	return nil
}

func init() {
	proto.RegisterType((*GetThemeCfgReq)(nil), "channel_wedding_conf.GetThemeCfgReq")
	proto.RegisterType((*GetThemeCfgResp)(nil), "channel_wedding_conf.GetThemeCfgResp")
	proto.RegisterType((*GetThemeCfgListReq)(nil), "channel_wedding_conf.GetThemeCfgListReq")
	proto.RegisterType((*GetThemeCfgListResp)(nil), "channel_wedding_conf.GetThemeCfgListResp")
	proto.RegisterType((*ThemeCfg)(nil), "channel_wedding_conf.ThemeCfg")
	proto.RegisterMapType((map[uint32]uint32)(nil), "channel_wedding_conf.ThemeCfg.PreProgressClothesExtraDurationCfgEntry")
	proto.RegisterType((*AwardInfo)(nil), "channel_wedding_conf.AwardInfo")
	proto.RegisterType((*FinishWeddingAward)(nil), "channel_wedding_conf.FinishWeddingAward")
	proto.RegisterType((*ResourceCfg)(nil), "channel_wedding_conf.ResourceCfg")
	proto.RegisterType((*WeddingSceneCfg)(nil), "channel_wedding_conf.WeddingSceneCfg")
	proto.RegisterType((*WeddingScenePreview)(nil), "channel_wedding_conf.WeddingScenePreview")
	proto.RegisterType((*WeddingSceneBoneCfg)(nil), "channel_wedding_conf.WeddingSceneBoneCfg")
	proto.RegisterType((*WeddingPriceInfo)(nil), "channel_wedding_conf.WeddingPriceInfo")
	proto.RegisterType((*WeddingPriceItem)(nil), "channel_wedding_conf.WeddingPriceItem")
	proto.RegisterType((*ThemeLevelCfg)(nil), "channel_wedding_conf.ThemeLevelCfg")
	proto.RegisterMapType((map[uint32]*GuestDressCfg)(nil), "channel_wedding_conf.ThemeLevelCfg.GuestDressCfgMapEntry")
	proto.RegisterType((*ChairGameResourceCfg)(nil), "channel_wedding_conf.ChairGameResourceCfg")
	proto.RegisterType((*GuestDressCfg)(nil), "channel_wedding_conf.GuestDressCfg")
	proto.RegisterType((*GuestSuitCfg)(nil), "channel_wedding_conf.GuestSuitCfg")
	proto.RegisterType((*WeddingPreviewCfg)(nil), "channel_wedding_conf.WeddingPreviewCfg")
	proto.RegisterType((*WeddingRankBg)(nil), "channel_wedding_conf.WeddingRankBg")
	proto.RegisterType((*WeddingRankBgList)(nil), "channel_wedding_conf.WeddingRankBgList")
	proto.RegisterType((*GetAllWeddingRankBackgroundReq)(nil), "channel_wedding_conf.GetAllWeddingRankBackgroundReq")
	proto.RegisterType((*GetAllWeddingRankBackgroundResp)(nil), "channel_wedding_conf.GetAllWeddingRankBackgroundResp")
	proto.RegisterMapType((map[uint32]*WeddingRankBgList)(nil), "channel_wedding_conf.GetAllWeddingRankBackgroundResp.WeddingRankBgMapEntry")
	proto.RegisterType((*GetThemeFinishedAwardCfgReq)(nil), "channel_wedding_conf.GetThemeFinishedAwardCfgReq")
	proto.RegisterType((*FinishWeddingAwardList)(nil), "channel_wedding_conf.FinishWeddingAwardList")
	proto.RegisterType((*GetThemeFinishedAwardCfgResp)(nil), "channel_wedding_conf.GetThemeFinishedAwardCfgResp")
	proto.RegisterMapType((map[uint32]*FinishWeddingAwardList)(nil), "channel_wedding_conf.GetThemeFinishedAwardCfgResp.ThemeFinishedAwardMapEntry")
	proto.RegisterEnum("channel_wedding_conf.WeddingAwardType", WeddingAwardType_name, WeddingAwardType_value)
	proto.RegisterEnum("channel_wedding_conf.WeddingScene", WeddingScene_name, WeddingScene_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelWeddingConfClient is the client API for ChannelWeddingConf service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelWeddingConfClient interface {
	// 获取婚礼主题配置
	GetThemeCfg(ctx context.Context, in *GetThemeCfgReq, opts ...grpc.CallOption) (*GetThemeCfgResp, error)
	// 获取婚礼主题配置列表
	GetThemeCfgList(ctx context.Context, in *GetThemeCfgListReq, opts ...grpc.CallOption) (*GetThemeCfgListResp, error)
	// 获取全量的婚礼榜单背景资源
	GetAllWeddingRankBackground(ctx context.Context, in *GetAllWeddingRankBackgroundReq, opts ...grpc.CallOption) (*GetAllWeddingRankBackgroundResp, error)
	// 获取婚礼主题的奖励配置
	GetThemeFinishedAwardCfg(ctx context.Context, in *GetThemeFinishedAwardCfgReq, opts ...grpc.CallOption) (*GetThemeFinishedAwardCfgResp, error)
}

type channelWeddingConfClient struct {
	cc *grpc.ClientConn
}

func NewChannelWeddingConfClient(cc *grpc.ClientConn) ChannelWeddingConfClient {
	return &channelWeddingConfClient{cc}
}

func (c *channelWeddingConfClient) GetThemeCfg(ctx context.Context, in *GetThemeCfgReq, opts ...grpc.CallOption) (*GetThemeCfgResp, error) {
	out := new(GetThemeCfgResp)
	err := c.cc.Invoke(ctx, "/channel_wedding_conf.ChannelWeddingConf/GetThemeCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingConfClient) GetThemeCfgList(ctx context.Context, in *GetThemeCfgListReq, opts ...grpc.CallOption) (*GetThemeCfgListResp, error) {
	out := new(GetThemeCfgListResp)
	err := c.cc.Invoke(ctx, "/channel_wedding_conf.ChannelWeddingConf/GetThemeCfgList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingConfClient) GetAllWeddingRankBackground(ctx context.Context, in *GetAllWeddingRankBackgroundReq, opts ...grpc.CallOption) (*GetAllWeddingRankBackgroundResp, error) {
	out := new(GetAllWeddingRankBackgroundResp)
	err := c.cc.Invoke(ctx, "/channel_wedding_conf.ChannelWeddingConf/GetAllWeddingRankBackground", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingConfClient) GetThemeFinishedAwardCfg(ctx context.Context, in *GetThemeFinishedAwardCfgReq, opts ...grpc.CallOption) (*GetThemeFinishedAwardCfgResp, error) {
	out := new(GetThemeFinishedAwardCfgResp)
	err := c.cc.Invoke(ctx, "/channel_wedding_conf.ChannelWeddingConf/GetThemeFinishedAwardCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelWeddingConfServer is the server API for ChannelWeddingConf service.
type ChannelWeddingConfServer interface {
	// 获取婚礼主题配置
	GetThemeCfg(context.Context, *GetThemeCfgReq) (*GetThemeCfgResp, error)
	// 获取婚礼主题配置列表
	GetThemeCfgList(context.Context, *GetThemeCfgListReq) (*GetThemeCfgListResp, error)
	// 获取全量的婚礼榜单背景资源
	GetAllWeddingRankBackground(context.Context, *GetAllWeddingRankBackgroundReq) (*GetAllWeddingRankBackgroundResp, error)
	// 获取婚礼主题的奖励配置
	GetThemeFinishedAwardCfg(context.Context, *GetThemeFinishedAwardCfgReq) (*GetThemeFinishedAwardCfgResp, error)
}

func RegisterChannelWeddingConfServer(s *grpc.Server, srv ChannelWeddingConfServer) {
	s.RegisterService(&_ChannelWeddingConf_serviceDesc, srv)
}

func _ChannelWeddingConf_GetThemeCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetThemeCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingConfServer).GetThemeCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_conf.ChannelWeddingConf/GetThemeCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingConfServer).GetThemeCfg(ctx, req.(*GetThemeCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingConf_GetThemeCfgList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetThemeCfgListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingConfServer).GetThemeCfgList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_conf.ChannelWeddingConf/GetThemeCfgList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingConfServer).GetThemeCfgList(ctx, req.(*GetThemeCfgListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingConf_GetAllWeddingRankBackground_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllWeddingRankBackgroundReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingConfServer).GetAllWeddingRankBackground(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_conf.ChannelWeddingConf/GetAllWeddingRankBackground",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingConfServer).GetAllWeddingRankBackground(ctx, req.(*GetAllWeddingRankBackgroundReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWeddingConf_GetThemeFinishedAwardCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetThemeFinishedAwardCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingConfServer).GetThemeFinishedAwardCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding_conf.ChannelWeddingConf/GetThemeFinishedAwardCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingConfServer).GetThemeFinishedAwardCfg(ctx, req.(*GetThemeFinishedAwardCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelWeddingConf_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_wedding_conf.ChannelWeddingConf",
	HandlerType: (*ChannelWeddingConfServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetThemeCfg",
			Handler:    _ChannelWeddingConf_GetThemeCfg_Handler,
		},
		{
			MethodName: "GetThemeCfgList",
			Handler:    _ChannelWeddingConf_GetThemeCfgList_Handler,
		},
		{
			MethodName: "GetAllWeddingRankBackground",
			Handler:    _ChannelWeddingConf_GetAllWeddingRankBackground_Handler,
		},
		{
			MethodName: "GetThemeFinishedAwardCfg",
			Handler:    _ChannelWeddingConf_GetThemeFinishedAwardCfg_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/channel-wedding-conf/channel-wedding-conf.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/channel-wedding-conf/channel-wedding-conf.proto", fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e)
}

var fileDescriptor_channel_wedding_conf_315ef5b97a19fd5e = []byte{
	// 2757 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x59, 0xdd, 0x6e, 0x1b, 0xc9,
	0xb1, 0x36, 0x29, 0x59, 0x3f, 0x25, 0x51, 0xa2, 0x5a, 0x7f, 0x14, 0x65, 0x5b, 0xf2, 0x78, 0x6d,
	0xcb, 0x3e, 0xbb, 0x32, 0x8e, 0xd7, 0x5e, 0xac, 0xcf, 0x1e, 0xef, 0x01, 0x45, 0x52, 0x14, 0xcf,
	0xea, 0x87, 0x19, 0x51, 0xeb, 0x6c, 0x10, 0x60, 0x30, 0x9a, 0x69, 0x92, 0x03, 0xcf, 0x9f, 0x66,
	0x9a, 0xb4, 0xb5, 0xc8, 0x5d, 0x80, 0xdc, 0x24, 0x48, 0x1e, 0x60, 0x2f, 0x92, 0xdb, 0xe4, 0x26,
	0x40, 0xb0, 0x40, 0x90, 0x07, 0xc8, 0x4d, 0x10, 0x20, 0xcf, 0x91, 0x37, 0x08, 0x72, 0x15, 0x74,
	0x75, 0xcf, 0x70, 0x48, 0x0d, 0x25, 0x7a, 0xaf, 0xa4, 0xa9, 0xfe, 0xea, 0x9b, 0xea, 0xaa, 0xea,
	0xea, 0xaa, 0x21, 0x7c, 0xc9, 0xd8, 0xb3, 0x8b, 0xae, 0x65, 0xbc, 0x0d, 0x2d, 0xbb, 0x47, 0x83,
	0x67, 0x46, 0x47, 0x77, 0x5d, 0x6a, 0x7f, 0xf2, 0x8e, 0x9a, 0xa6, 0xe5, 0xb6, 0x3f, 0x31, 0x3c,
	0xb7, 0x95, 0x2a, 0xdc, 0xf5, 0x03, 0x8f, 0x79, 0x64, 0x45, 0xae, 0x69, 0x72, 0x4d, 0xe3, 0x6b,
	0xc5, 0xdd, 0x21, 0x56, 0xfa, 0x9e, 0x51, 0x37, 0xb4, 0x3c, 0xf7, 0x99, 0xe7, 0x33, 0xcb, 0x73,
	0xc3, 0xe8, 0xaf, 0x60, 0x51, 0xfe, 0x0b, 0x16, 0x6a, 0x94, 0x35, 0x3b, 0xd4, 0xa1, 0xe5, 0x56,
	0x5b, 0xa5, 0x17, 0x64, 0x03, 0x66, 0x18, 0x7f, 0xd4, 0x2c, 0xb3, 0x90, 0xd9, 0xce, 0xec, 0xe4,
	0xd4, 0x69, 0x7c, 0xae, 0x9b, 0xca, 0x31, 0x2c, 0x0e, 0x80, 0x43, 0x9f, 0x7c, 0x01, 0xb3, 0x02,
	0x6d, 0xb4, 0xda, 0x08, 0x9f, 0x7b, 0x7e, 0x6f, 0x37, 0xcd, 0xb2, 0xdd, 0x58, 0x4d, 0xd0, 0x97,
	0x5b, 0x6d, 0x65, 0x05, 0x48, 0x82, 0xef, 0xd0, 0x0a, 0x99, 0x4a, 0x2f, 0x94, 0xbf, 0x66, 0x60,
	0xf9, 0x8a, 0x38, 0xf4, 0x49, 0x05, 0x16, 0xe2, 0x57, 0x69, 0xb6, 0x15, 0xb2, 0x42, 0x66, 0x7b,
	0x62, 0x8c, 0xf7, 0xcd, 0xb3, 0x04, 0x13, 0x79, 0x05, 0x1b, 0x82, 0x85, 0x59, 0xcc, 0xa6, 0x5a,
	0x48, 0x6d, 0x6a, 0x30, 0x6a, 0x6a, 0x96, 0xe1, 0xb9, 0x85, 0xec, 0x76, 0x66, 0x67, 0x56, 0x5d,
	0x43, 0x40, 0x93, 0xaf, 0x9f, 0xca, 0xe5, 0xba, 0xe1, 0xb9, 0xe4, 0x13, 0x58, 0x4e, 0xaa, 0x9e,
	0xb7, 0x85, 0xd2, 0x04, 0x2a, 0xe5, 0xfb, 0x4a, 0x7b, 0x6d, 0x0e, 0x57, 0xbe, 0x5f, 0x80, 0x99,
	0xc8, 0x88, 0x6b, 0xbc, 0x4a, 0xee, 0x02, 0x88, 0x25, 0x57, 0x77, 0xa8, 0x34, 0x41, 0x38, 0xf5,
	0x58, 0x77, 0x28, 0xa9, 0x02, 0xf8, 0x81, 0x65, 0x50, 0xcd, 0x72, 0x5b, 0x1e, 0xbe, 0x6c, 0xee,
	0xf9, 0xa3, 0xf4, 0x2d, 0xbf, 0x11, 0x0f, 0x0d, 0x0e, 0xaf, 0xbb, 0x2d, 0x4f, 0x9d, 0xf5, 0xa3,
	0x7f, 0xc9, 0x8f, 0x22, 0xe3, 0x03, 0xcf, 0x73, 0xb4, 0x80, 0x86, 0x5e, 0x37, 0x30, 0x68, 0x61,
	0x12, 0xf9, 0xee, 0xa7, 0xf3, 0xa9, 0x12, 0xc5, 0xbd, 0xb8, 0x84, 0xda, 0xaa, 0xe7, 0x39, 0x91,
	0x94, 0x7c, 0x05, 0x0b, 0xa1, 0x41, 0xdd, 0x44, 0x40, 0x6e, 0x63, 0x40, 0x1e, 0x5e, 0x6b, 0xdd,
	0x29, 0x57, 0xc1, 0xb8, 0x84, 0xf2, 0x3f, 0x8c, 0x4b, 0x13, 0x56, 0x84, 0x7d, 0x36, 0xed, 0x51,
	0xbb, 0x4f, 0x39, 0x85, 0x94, 0x0f, 0xae, 0x89, 0xf1, 0x21, 0x57, 0xe8, 0x9b, 0x18, 0x3d, 0x22,
	0xeb, 0x1b, 0x58, 0x8e, 0x14, 0xfc, 0x80, 0xf6, 0x2c, 0xfa, 0x0e, 0x13, 0x75, 0x1a, 0x77, 0xfd,
	0xf8, 0x06, 0x2f, 0x22, 0x1e, 0x89, 0xdf, 0x0d, 0x8b, 0xc8, 0x37, 0xb0, 0xee, 0x50, 0xc7, 0x0b,
	0x2c, 0xdd, 0xd6, 0x7a, 0x96, 0x49, 0xbd, 0xbe, 0x4b, 0x67, 0xc6, 0x75, 0xe9, 0x6a, 0xc4, 0xf0,
	0x35, 0x27, 0x88, 0xdd, 0x7a, 0x0c, 0x39, 0xa3, 0xa3, 0x5b, 0x01, 0x67, 0x44, 0x6b, 0x67, 0x91,
	0xf0, 0x69, 0x3a, 0x61, 0x99, 0x43, 0x6b, 0xba, 0x43, 0x93, 0xcc, 0x73, 0x48, 0xa0, 0xd2, 0x90,
	0x9b, 0xfa, 0x0a, 0x36, 0xe2, 0x2c, 0x4f, 0xe6, 0x2f, 0x26, 0x2f, 0x88, 0x8c, 0x8f, 0x00, 0xcd,
	0x38, 0x89, 0x31, 0xe3, 0x5f, 0xc3, 0x66, 0xd7, 0x1d, 0xad, 0x3c, 0x87, 0xca, 0x85, 0x3e, 0x64,
	0x48, 0xfd, 0x01, 0xe4, 0xe8, 0x7b, 0xdd, 0xf1, 0x6d, 0xaa, 0xf9, 0x1d, 0x8f, 0x79, 0x85, 0x79,
	0x54, 0x98, 0x97, 0xc2, 0x06, 0x97, 0x91, 0x27, 0x20, 0x8e, 0x8e, 0x76, 0xae, 0x1b, 0x6f, 0xdb,
	0x81, 0xd7, 0x75, 0xcd, 0x42, 0x0e, 0x71, 0x8b, 0x28, 0xdf, 0x8b, 0xc5, 0xe4, 0x63, 0x20, 0x02,
	0x1a, 0xc5, 0x92, 0xd1, 0xf7, 0xac, 0xb0, 0x90, 0x38, 0x7f, 0x32, 0x42, 0x4d, 0xfa, 0x9e, 0x91,
	0x1d, 0xc8, 0x07, 0xf4, 0x9d, 0x1e, 0x98, 0x78, 0x72, 0x34, 0x93, 0x86, 0x46, 0x61, 0x15, 0xb1,
	0x0b, 0x42, 0xce, 0xcf, 0x45, 0x85, 0x86, 0x06, 0x51, 0x07, 0x91, 0x98, 0x77, 0x8b, 0x98, 0x77,
	0x3b, 0xe9, 0x4e, 0xdf, 0xb7, 0x5c, 0x2b, 0xec, 0xc8, 0x44, 0x29, 0x71, 0xcd, 0x24, 0x27, 0x66,
	0x5e, 0x19, 0xee, 0x45, 0x2a, 0x1d, 0xdd, 0xb6, 0x35, 0xc3, 0x73, 0xf8, 0xff, 0x89, 0x4d, 0xe6,
	0xd1, 0x96, 0x4d, 0x89, 0x3a, 0xd0, 0x6d, 0xbb, 0x8c, 0x98, 0xc4, 0x86, 0x9f, 0xc3, 0x9a, 0xa3,
	0x5b, 0xb6, 0x66, 0xeb, 0xe6, 0xa5, 0x66, 0xd3, 0x16, 0x8b, 0x8b, 0xce, 0x12, 0x2a, 0x13, 0xbe,
	0x7a, 0xa8, 0x9b, 0x97, 0x87, 0xb4, 0xc5, 0x44, 0xd9, 0x21, 0x2f, 0x60, 0xbd, 0xaf, 0x13, 0x58,
	0xed, 0x4e, 0x5f, 0x89, 0xa0, 0xd2, 0x72, 0xa4, 0xa4, 0xf2, 0x45, 0xa9, 0x15, 0x17, 0x21, 0x04,
	0x2e, 0x27, 0x8a, 0x50, 0xb4, 0x6c, 0x85, 0x9a, 0x49, 0x6d, 0xca, 0xa8, 0x59, 0x58, 0xd9, 0xce,
	0xec, 0xcc, 0xa8, 0xb3, 0x56, 0x58, 0x11, 0x02, 0xee, 0xea, 0x68, 0xb3, 0x6d, 0xab, 0xc5, 0x34,
	0xcb, 0x0c, 0x0b, 0x6b, 0xdb, 0x13, 0x3b, 0x39, 0x75, 0x41, 0xca, 0x6b, 0x56, 0x8b, 0xd5, 0xcd,
	0x90, 0x54, 0x60, 0xcb, 0x0f, 0x78, 0x00, 0xbd, 0x76, 0x40, 0xc3, 0x50, 0xf3, 0x75, 0xee, 0xdc,
	0xe8, 0xec, 0x68, 0xdd, 0xc0, 0x2e, 0xac, 0x0b, 0xbf, 0xf8, 0x01, 0x6d, 0x48, 0x54, 0x83, 0x83,
	0xa2, 0xfc, 0x3e, 0x0b, 0xec, 0x9b, 0x58, 0x1c, 0xf3, 0x65, 0xa1, 0x70, 0x3d, 0xcb, 0x91, 0xf9,
	0x12, 0xcf, 0x70, 0xcf, 0xd7, 0x42, 0xca, 0x98, 0x4d, 0x1d, 0xea, 0xb2, 0xfe, 0x19, 0xde, 0x18,
	0xff, 0x0c, 0xf7, 0xfc, 0xd3, 0x98, 0x20, 0x3e, 0xc3, 0x57, 0x0c, 0x0c, 0x68, 0xc8, 0x5f, 0xc0,
	0xf4, 0xa0, 0x4d, 0x99, 0xd6, 0xd3, 0xed, 0x42, 0x11, 0x6f, 0x81, 0x01, 0x03, 0x05, 0xa8, 0x89,
	0x98, 0xaf, 0x75, 0x9b, 0x7c, 0x97, 0x81, 0xc7, 0x03, 0x34, 0x86, 0xed, 0xb1, 0x0e, 0x0d, 0x35,
	0xfa, 0x9e, 0x05, 0xba, 0x66, 0x76, 0x03, 0x9d, 0xdf, 0xe5, 0x58, 0x24, 0x36, 0x31, 0x5f, 0xf7,
	0xaf, 0xbf, 0x0b, 0x77, 0x1b, 0xfd, 0xb7, 0x95, 0x05, 0x57, 0x95, 0x53, 0x55, 0x24, 0x53, 0xb9,
	0xd5, 0xae, 0xba, 0x2c, 0xb8, 0x54, 0x15, 0xff, 0x46, 0x60, 0xf1, 0x0c, 0x1e, 0x8f, 0x49, 0x47,
	0xf2, 0x30, 0xf1, 0x96, 0x5e, 0xca, 0x8b, 0x8f, 0xff, 0x4b, 0x56, 0xe0, 0x76, 0x4f, 0xb7, 0xbb,
	0xe2, 0xbe, 0xcb, 0xa9, 0xe2, 0xe1, 0x7f, 0xb2, 0x9f, 0x67, 0x94, 0xef, 0x33, 0x30, 0x5b, 0x8a,
	0x8e, 0x12, 0x4f, 0x3c, 0x1d, 0x4f, 0x26, 0xbb, 0xf4, 0xa9, 0x24, 0x98, 0x45, 0x49, 0xf3, 0xd2,
	0xa7, 0xfc, 0x5a, 0x15, 0xcb, 0x96, 0x29, 0x6f, 0xce, 0x69, 0x7c, 0xae, 0x9b, 0xe4, 0x11, 0x2c,
	0x46, 0x4b, 0x5a, 0x8b, 0x3a, 0xba, 0x4d, 0xe5, 0x4d, 0x9d, 0x93, 0x88, 0x7d, 0x14, 0x92, 0x35,
	0x98, 0xd2, 0x1d, 0xaf, 0xeb, 0x32, 0xbc, 0x0b, 0x73, 0xaa, 0x7c, 0xe2, 0x16, 0xe2, 0x55, 0x54,
	0xb8, 0x2d, 0x2c, 0xc4, 0x07, 0xb2, 0x0e, 0xd3, 0x76, 0x4f, 0xdc, 0xd4, 0x53, 0xc8, 0x36, 0x65,
	0xf7, 0xf8, 0x35, 0xad, 0xfc, 0x2e, 0x0b, 0xe4, 0x6a, 0x59, 0x20, 0xff, 0x1f, 0x59, 0xa1, 0xbb,
	0x96, 0x83, 0x4e, 0x91, 0x5d, 0xd2, 0x18, 0xb9, 0xb5, 0x80, 0x9a, 0xa5, 0x48, 0x11, 0x7b, 0x08,
	0xcf, 0x17, 0x45, 0x4f, 0x6e, 0x96, 0x79, 0x3e, 0xd6, 0xba, 0x2d, 0x98, 0x3b, 0xf7, 0x18, 0xf3,
	0x1c, 0xb1, 0x2a, 0x36, 0x0a, 0x42, 0x84, 0x80, 0xbd, 0xc8, 0x8f, 0xd8, 0x45, 0x88, 0x5b, 0x7f,
	0x2b, 0xdd, 0x84, 0xd8, 0xf9, 0x7b, 0xd9, 0x42, 0x46, 0x3a, 0x1b, 0x63, 0xf1, 0x65, 0xc4, 0x91,
	0xb8, 0xeb, 0x6f, 0xe2, 0x90, 0xfa, 0xbc, 0x24, 0x2a, 0xdf, 0x65, 0x60, 0x2e, 0xb1, 0x3f, 0x7e,
	0x3d, 0xc4, 0x47, 0x36, 0x11, 0xde, 0xf9, 0x48, 0x88, 0x11, 0xbe, 0x0f, 0xf3, 0x03, 0xd5, 0x41,
	0x6c, 0x7c, 0x2e, 0x48, 0x54, 0x83, 0x24, 0x84, 0x1f, 0xfd, 0x89, 0x41, 0x08, 0x3f, 0xea, 0x49,
	0x88, 0xef, 0xb6, 0xd1, 0x01, 0x09, 0x48, 0xc3, 0x6d, 0x2b, 0xbf, 0xcf, 0xc2, 0xe2, 0x50, 0x8b,
	0xc2, 0x73, 0x00, 0x9b, 0x14, 0x69, 0x99, 0x78, 0x20, 0xaf, 0x61, 0x26, 0x2e, 0x14, 0xd9, 0x71,
	0x83, 0x19, 0xab, 0x90, 0x23, 0xc8, 0x9d, 0x7b, 0xc9, 0xae, 0x69, 0x02, 0x3d, 0xf9, 0xe4, 0xe6,
	0xae, 0x69, 0xcf, 0x13, 0x9d, 0xd3, 0xdc, 0xb9, 0xd7, 0x6f, 0x9c, 0xee, 0x02, 0x88, 0x2e, 0x0c,
	0x2b, 0xb7, 0xd8, 0xd8, 0x2c, 0x4a, 0xb0, 0x72, 0x3f, 0x81, 0x25, 0xc3, 0xb6, 0x7c, 0xcd, 0x72,
	0xb4, 0xd0, 0xe1, 0x17, 0x91, 0x6f, 0x19, 0x98, 0xd2, 0xb3, 0xea, 0x02, 0x5f, 0xa8, 0x3b, 0xa7,
	0x5c, 0xdc, 0xb0, 0x0c, 0x5e, 0xc5, 0x11, 0x6a, 0xd2, 0x96, 0xde, 0xb5, 0x19, 0x22, 0xa7, 0xfa,
	0xc8, 0x8a, 0x10, 0x37, 0x2c, 0x43, 0xf9, 0x5b, 0x06, 0x96, 0x93, 0x86, 0xc9, 0x6b, 0x97, 0x67,
	0xbb, 0xb0, 0xe5, 0x87, 0x64, 0x3b, 0x6a, 0xf6, 0xb3, 0x9d, 0xef, 0x0b, 0x0d, 0x4e, 0x74, 0xe6,
	0xb3, 0x28, 0xc1, 0x7d, 0x1d, 0xc0, 0xc2, 0xb7, 0xbc, 0x93, 0xed, 0xbf, 0x69, 0x62, 0xdc, 0x37,
	0xe5, 0xb8, 0x62, 0xfc, 0x22, 0xe5, 0x0f, 0x43, 0x9b, 0x91, 0x5e, 0xee, 0x17, 0x80, 0x4c, 0xb2,
	0x00, 0x6c, 0xc2, 0x6c, 0x48, 0x2f, 0x34, 0xcb, 0x35, 0xe9, 0x7b, 0x59, 0xbc, 0x66, 0x42, 0x7a,
	0x51, 0xe7, 0xcf, 0xe4, 0x21, 0x2c, 0xc4, 0xf6, 0x88, 0x22, 0x11, 0x95, 0x9c, 0x48, 0x8a, 0x2d,
	0xfd, 0x3a, 0x4c, 0x63, 0x06, 0x58, 0x66, 0x54, 0x73, 0xf8, 0x63, 0xdd, 0x24, 0xdb, 0x30, 0x7f,
	0xae, 0x87, 0x54, 0x8b, 0x56, 0x45, 0xe9, 0x01, 0x2e, 0xdb, 0x43, 0x84, 0xf2, 0xcf, 0x0c, 0xe4,
	0x87, 0xdb, 0x7c, 0x6e, 0x29, 0x36, 0xfa, 0x91, 0xa5, 0xf8, 0xc0, 0x1d, 0x28, 0x06, 0x07, 0x3c,
	0x5b, 0xc2, 0x54, 0x31, 0x10, 0xe0, 0xc1, 0x52, 0x61, 0xc9, 0xf5, 0x02, 0x47, 0xb7, 0x35, 0x66,
	0x61, 0x4b, 0xc5, 0x09, 0x44, 0x2a, 0x8e, 0x33, 0x5e, 0x30, 0xea, 0xa8, 0x8b, 0x82, 0xa0, 0x69,
	0xf1, 0xc6, 0x8b, 0xbf, 0xf2, 0x10, 0x16, 0x3a, 0x1e, 0x4b, 0x12, 0x4e, 0x7e, 0x10, 0xe1, 0x7c,
	0xc7, 0x63, 0x31, 0x9b, 0x52, 0x1a, 0xda, 0x2a, 0xa3, 0xce, 0x88, 0xad, 0xae, 0xc3, 0xb4, 0xec,
	0x3b, 0xe4, 0x3e, 0xa7, 0xda, 0xd8, 0x6f, 0x28, 0xff, 0x9a, 0x81, 0xdc, 0xc0, 0x90, 0x30, 0x22,
	0xaa, 0x9f, 0xc1, 0x3a, 0xce, 0x45, 0xfd, 0xf6, 0x8c, 0x67, 0x3f, 0xeb, 0x06, 0xd1, 0x40, 0xb6,
	0xca, 0x97, 0xfb, 0x9d, 0x59, 0x43, 0x2c, 0x92, 0x97, 0x57, 0xf5, 0x1c, 0xff, 0x05, 0x16, 0x2a,
	0x11, 0xf9, 0x95, 0x41, 0xbd, 0x23, 0xff, 0x05, 0xaf, 0x58, 0x1d, 0x58, 0x6e, 0x77, 0x69, 0xc8,
	0x34, 0x53, 0x5c, 0xeb, 0xad, 0xb6, 0xe6, 0xe8, 0xbe, 0x74, 0xd6, 0xab, 0x31, 0x66, 0x9d, 0xdd,
	0x1a, 0x57, 0xaf, 0xe0, 0xc5, 0xdb, 0x6a, 0x1f, 0xe9, 0xbe, 0xb8, 0xb6, 0xf3, 0xed, 0x21, 0x31,
	0x51, 0x61, 0x71, 0x68, 0x00, 0xc2, 0xa4, 0x1a, 0xab, 0xdc, 0xc8, 0x53, 0x1d, 0xf7, 0x70, 0xd1,
	0x29, 0x2f, 0xc1, 0xdd, 0x81, 0xd6, 0xb6, 0xed, 0x0d, 0x75, 0xb6, 0xa2, 0x68, 0x14, 0x13, 0x9d,
	0x6d, 0xcd, 0x1b, 0x6c, 0x6c, 0x1f, 0xc3, 0xa2, 0x41, 0x03, 0x66, 0xb5, 0x2c, 0x43, 0x67, 0x14,
	0x2b, 0xcd, 0xb4, 0xac, 0x34, 0x7d, 0x31, 0xaf, 0x49, 0x98, 0xc4, 0xa2, 0x77, 0xb2, 0x4c, 0x1c,
	0xad, 0x30, 0x89, 0x51, 0x52, 0x37, 0xf9, 0xbd, 0x17, 0x2d, 0x9b, 0xfa, 0x25, 0x4e, 0x4a, 0x39,
	0x35, 0xd2, 0xa8, 0xe8, 0x97, 0xe4, 0x7f, 0xa1, 0x18, 0xfa, 0xd4, 0xe0, 0x63, 0x5a, 0x4a, 0x6c,
	0xc5, 0xf4, 0x53, 0x90, 0x88, 0xab, 0xe1, 0xfd, 0x22, 0x55, 0x3b, 0x8a, 0xb0, 0x18, 0x7f, 0xd6,
	0xaf, 0x68, 0xcb, 0x20, 0xff, 0x1f, 0xdc, 0x69, 0x51, 0xdb, 0xf6, 0xde, 0x69, 0xa1, 0xaf, 0x1b,
	0x34, 0xf6, 0x33, 0x15, 0xbd, 0xa2, 0x1c, 0x86, 0x36, 0x04, 0xe6, 0x94, 0x43, 0xa4, 0xf3, 0xab,
	0x02, 0x40, 0xaa, 0xb0, 0x95, 0x4a, 0x70, 0x65, 0x50, 0xba, 0x73, 0x95, 0x23, 0xe1, 0xeb, 0x06,
	0x3c, 0x94, 0x34, 0x1d, 0xaf, 0x1b, 0xd2, 0xd1, 0x64, 0x62, 0x90, 0xba, 0x2f, 0xc0, 0x07, 0x1c,
	0x3b, 0x82, 0xf1, 0x0b, 0x28, 0xa6, 0x1a, 0x66, 0x78, 0xb6, 0x17, 0x14, 0x16, 0x85, 0x5b, 0xae,
	0xda, 0x54, 0xe6, 0xcb, 0xa4, 0x06, 0xdb, 0xe9, 0xca, 0xfd, 0xc0, 0xcb, 0xd1, 0xe8, 0x6e, 0x0a,
	0x45, 0x1f, 0x44, 0x3e, 0x85, 0xb5, 0xf3, 0xee, 0xa5, 0x96, 0x48, 0xef, 0x28, 0x4d, 0x96, 0x30,
	0x0d, 0x96, 0xcf, 0xbb, 0x97, 0xfd, 0x59, 0x5e, 0x24, 0x4c, 0xb1, 0x03, 0xab, 0xa9, 0x47, 0x27,
	0xa5, 0x45, 0x7d, 0x95, 0x6c, 0x51, 0x47, 0x7e, 0x82, 0x18, 0x60, 0x4b, 0xf6, 0xb1, 0x7f, 0xc9,
	0xc2, 0x4a, 0xda, 0x70, 0xce, 0x6f, 0x10, 0x31, 0xdf, 0xf3, 0xac, 0xcf, 0xe0, 0x4e, 0x67, 0x50,
	0xc0, 0xf3, 0xfd, 0x53, 0x58, 0x0b, 0x2d, 0xc6, 0x70, 0x43, 0x5e, 0x48, 0x65, 0xe7, 0xda, 0x2f,
	0x6c, 0xcb, 0x72, 0xb5, 0xe1, 0x85, 0x54, 0x34, 0xb0, 0x75, 0x93, 0x3c, 0x83, 0x95, 0x01, 0xa5,
	0x48, 0x65, 0x02, 0x55, 0x96, 0x12, 0x2a, 0x47, 0x42, 0xe1, 0x29, 0x2c, 0x85, 0x4c, 0x77, 0xcd,
	0xf3, 0xcb, 0xc4, 0x0b, 0xc4, 0x55, 0xb4, 0x28, 0x17, 0x62, 0xf2, 0x47, 0x10, 0x89, 0x62, 0x5e,
	0x71, 0x2d, 0xe5, 0xa4, 0xf8, 0x28, 0xc6, 0xb5, 0xf8, 0xdc, 0x19, 0x13, 0x86, 0xf8, 0xed, 0x26,
	0xa7, 0xe6, 0xb8, 0x38, 0xa2, 0x0b, 0x89, 0x02, 0x28, 0xd0, 0x62, 0xd4, 0x34, 0xa2, 0xe6, 0xb8,
	0x50, 0x50, 0x85, 0xca, 0x6f, 0xb2, 0x90, 0x1b, 0x70, 0x2c, 0xaf, 0x03, 0xa2, 0x56, 0x62, 0x7f,
	0x2b, 0xbc, 0x36, 0x8b, 0x12, 0x6c, 0x6f, 0x5f, 0xc3, 0x4c, 0xd8, 0xb5, 0x18, 0x4e, 0x42, 0x22,
	0x5c, 0xca, 0x35, 0xe1, 0x3a, 0xed, 0x5a, 0x8c, 0x47, 0x6b, 0x3a, 0x14, 0xff, 0x90, 0x7d, 0xc8,
	0x45, 0x28, 0xe4, 0x1c, 0xbf, 0x97, 0x98, 0x97, 0x2b, 0x68, 0xe8, 0x50, 0xcf, 0x32, 0x39, 0xdc,
	0xb3, 0xbc, 0x82, 0x8d, 0x68, 0x82, 0xeb, 0xfa, 0xed, 0x40, 0x37, 0xa9, 0xe6, 0x7b, 0x7e, 0xd7,
	0xc7, 0x96, 0x54, 0xf4, 0x64, 0x6b, 0x12, 0x70, 0x26, 0xd6, 0x1b, 0x7c, 0x99, 0x77, 0xa7, 0x0c,
	0xe6, 0x93, 0xa6, 0x13, 0x02, 0x93, 0xd8, 0x5f, 0x08, 0x4f, 0xe0, 0xff, 0x5c, 0x96, 0xe8, 0x95,
	0xf0, 0x7f, 0x3e, 0x33, 0x58, 0x8c, 0x3a, 0xe8, 0xe8, 0x09, 0x74, 0xf4, 0x34, 0x7f, 0xe6, 0x81,
	0xb8, 0x0f, 0xf3, 0xf1, 0x04, 0x19, 0x52, 0x43, 0xc6, 0x7f, 0x2e, 0x92, 0x9d, 0x52, 0x43, 0xf9,
	0x53, 0x06, 0x96, 0xae, 0x7c, 0x0e, 0x1b, 0xe8, 0x7f, 0x33, 0x1f, 0xde, 0xff, 0xde, 0x01, 0x30,
	0xfc, 0xb8, 0xc5, 0x91, 0x2d, 0x94, 0xe1, 0x8b, 0x06, 0xe7, 0x3a, 0x83, 0x1f, 0xc2, 0x22, 0x76,
	0x47, 0x09, 0x6d, 0x61, 0x33, 0x36, 0x4d, 0x65, 0xc9, 0xa0, 0x7c, 0x06, 0x39, 0x69, 0xb3, 0xaa,
	0xbb, 0x6f, 0xf7, 0xda, 0x64, 0x01, 0xb2, 0x76, 0x4f, 0x9e, 0xec, 0xac, 0xdd, 0x23, 0xab, 0x30,
	0x75, 0xde, 0x4e, 0x0c, 0x13, 0xb7, 0xcf, 0xdb, 0x67, 0x81, 0xad, 0x58, 0xf1, 0x5e, 0x85, 0x5e,
	0xf4, 0x59, 0x32, 0xda, 0x52, 0xa0, 0xbb, 0x6f, 0xb5, 0xf3, 0x81, 0x4f, 0xcf, 0x0f, 0xae, 0xbd,
	0x44, 0x05, 0x4d, 0xfc, 0xf5, 0xb0, 0xcf, 0xaa, 0x6c, 0xc3, 0xbd, 0x1a, 0x65, 0x25, 0xdb, 0x4e,
	0x22, 0xe3, 0xfa, 0xaa, 0xd2, 0x0b, 0xe5, 0x97, 0x59, 0xd8, 0xba, 0x16, 0x12, 0xfa, 0xe4, 0xdb,
	0xfe, 0xc7, 0xcd, 0xc8, 0x36, 0xde, 0x45, 0x08, 0xd3, 0xbe, 0x1a, 0x91, 0xff, 0xd7, 0x73, 0x0e,
	0x9a, 0xde, 0xef, 0x2b, 0xde, 0x0d, 0x89, 0x8b, 0x36, 0xac, 0xa6, 0x42, 0x53, 0xea, 0xe8, 0xeb,
	0xc1, 0x3a, 0xfa, 0x78, 0x0c, 0x9f, 0xe1, 0x37, 0xff, 0x44, 0x2d, 0xfd, 0x1c, 0x36, 0xa3, 0x5f,
	0x04, 0xc4, 0x8c, 0x4d, 0x4d, 0x9c, 0x30, 0x6f, 0xfe, 0xc9, 0xe2, 0x67, 0xb0, 0x76, 0x75, 0x2a,
	0xc7, 0xc8, 0x5e, 0xf3, 0x45, 0xbe, 0x36, 0x30, 0xe8, 0x66, 0x3f, 0xf0, 0x4b, 0x60, 0x62, 0xe2,
	0xfd, 0x6d, 0x16, 0xee, 0x8c, 0x36, 0x3c, 0xf4, 0xc9, 0x2f, 0x32, 0x50, 0x10, 0x56, 0xb4, 0xe4,
	0xb2, 0x26, 0xde, 0xdc, 0x0f, 0xe4, 0xd1, 0xc8, 0x40, 0x8e, 0xa4, 0xdd, 0xbd, 0xba, 0x12, 0x87,
	0x72, 0x95, 0xa5, 0xad, 0x15, 0x7b, 0x50, 0x1c, 0xad, 0x94, 0x12, 0xd4, 0xbd, 0xc1, 0xa0, 0x7e,
	0x3c, 0xae, 0x77, 0x86, 0x22, 0xfb, 0xf4, 0x1f, 0xfd, 0x79, 0xa6, 0x14, 0x7f, 0xd5, 0x51, 0xe0,
	0xde, 0x9b, 0x6a, 0xa5, 0x52, 0x3f, 0xae, 0x69, 0xa5, 0x37, 0x25, 0xb5, 0xa2, 0x35, 0xbf, 0x69,
	0x54, 0xb5, 0xb3, 0xe3, 0xd3, 0x46, 0xb5, 0x5c, 0xdf, 0xaf, 0x57, 0x2b, 0xf9, 0x5b, 0x64, 0x0b,
	0x36, 0x53, 0x30, 0x07, 0xd5, 0x52, 0xe5, 0x4d, 0xb5, 0xa4, 0xe6, 0x33, 0x64, 0x07, 0x3e, 0x4a,
	0x01, 0x7c, 0x5d, 0xd2, 0xf6, 0x4f, 0x0e, 0x0f, 0x4f, 0xde, 0x68, 0xe5, 0x83, 0xd2, 0xf1, 0x71,
	0xf5, 0x30, 0x9f, 0x25, 0x1f, 0xc1, 0x76, 0x3a, 0x72, 0xaf, 0x54, 0xfe, 0xaa, 0xa6, 0x9e, 0x9c,
	0x1d, 0x57, 0xf2, 0x13, 0xe4, 0x11, 0x28, 0x29, 0xa8, 0xfd, 0x2a, 0x92, 0x1d, 0xd6, 0x6b, 0xa5,
	0xe6, 0x99, 0x5a, 0xcd, 0x4f, 0x3e, 0xfd, 0x63, 0x06, 0xe6, 0x93, 0x5d, 0x34, 0xb9, 0x0b, 0x1b,
	0x91, 0xe2, 0x69, 0xb9, 0x7a, 0x3c, 0xbc, 0x91, 0x07, 0xb0, 0x35, 0xb8, 0xbc, 0xa7, 0xd6, 0x2b,
	0x55, 0xad, 0xa6, 0x9e, 0x9c, 0x1c, 0x69, 0xd5, 0xe3, 0x66, 0x95, 0x6f, 0x26, 0xb1, 0x5b, 0x01,
	0xaa, 0xfe, 0x98, 0xdb, 0x5f, 0xab, 0x6a, 0x6a, 0xfd, 0xb8, 0x96, 0xcf, 0x92, 0x4d, 0x58, 0x1f,
	0x04, 0x1c, 0xd4, 0x6b, 0x07, 0x87, 0xf5, 0xda, 0x41, 0x33, 0x3f, 0x71, 0xd5, 0x02, 0xbe, 0xa9,
	0x86, 0xd6, 0x38, 0x38, 0x69, 0x9e, 0xe4, 0x27, 0x9f, 0xff, 0x7a, 0x12, 0x48, 0x59, 0x84, 0x2f,
	0xee, 0xd4, 0xdc, 0x16, 0xf9, 0x29, 0xcc, 0x25, 0x7e, 0x86, 0x23, 0x1f, 0x5d, 0x9f, 0x87, 0xe2,
	0x28, 0x16, 0x1f, 0x8e, 0x81, 0x0a, 0x7d, 0xe5, 0x16, 0xe9, 0x0c, 0xfc, 0x96, 0x88, 0x27, 0x72,
	0xe7, 0x46, 0x5d, 0xf9, 0x13, 0x61, 0xf1, 0xc9, 0x98, 0x48, 0x7c, 0xd3, 0xaf, 0x32, 0x58, 0x3d,
	0x46, 0x95, 0x3d, 0xf2, 0xe2, 0x07, 0x54, 0xca, 0x8b, 0xe2, 0xcb, 0x1f, 0x54, 0x5f, 0x95, 0x5b,
	0xe4, 0xe7, 0x19, 0x28, 0x8c, 0x3a, 0xbc, 0xe4, 0xbf, 0x3f, 0xf4, 0xb0, 0x5f, 0x14, 0x9f, 0x7f,
	0x78, 0x7d, 0x50, 0x6e, 0x15, 0x37, 0xff, 0xfd, 0xe7, 0xbf, 0x37, 0xd7, 0x60, 0x25, 0xed, 0x07,
	0xe6, 0xbd, 0xcf, 0x7f, 0xf2, 0x59, 0xdb, 0xb3, 0x75, 0xb7, 0xbd, 0xfb, 0xf2, 0x39, 0x63, 0xbb,
	0x86, 0xe7, 0x3c, 0xc3, 0x5f, 0x8b, 0x0d, 0xcf, 0x7e, 0x16, 0xd2, 0xa0, 0x67, 0x19, 0x34, 0x4c,
	0xfd, 0x69, 0xfa, 0x7c, 0x0a, 0x71, 0x9f, 0xfe, 0x27, 0x00, 0x00, 0xff, 0xff, 0x61, 0x31, 0x6f,
	0xea, 0xdd, 0x1e, 0x00, 0x00,
}
