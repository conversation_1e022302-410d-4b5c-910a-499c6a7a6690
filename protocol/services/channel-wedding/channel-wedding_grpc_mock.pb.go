// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/channel-wedding/channel-wedding.proto

package channel_wedding

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	grpc "google.golang.org/grpc"
)

// MockChannelWeddingClient is a mock of ChannelWeddingClient interface.
type MockChannelWeddingClient struct {
	ctrl     *gomock.Controller
	recorder *MockChannelWeddingClientMockRecorder
}

// MockChannelWeddingClientMockRecorder is the mock recorder for MockChannelWeddingClient.
type MockChannelWeddingClientMockRecorder struct {
	mock *MockChannelWeddingClient
}

// NewMockChannelWeddingClient creates a new mock instance.
func NewMockChannelWeddingClient(ctrl *gomock.Controller) *MockChannelWeddingClient {
	mock := &MockChannelWeddingClient{ctrl: ctrl}
	mock.recorder = &MockChannelWeddingClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelWeddingClient) EXPECT() *MockChannelWeddingClientMockRecorder {
	return m.recorder
}

// AddWeddingBridesmaidMan mocks base method.
func (m *MockChannelWeddingClient) AddWeddingBridesmaidMan(ctx context.Context, in *AddWeddingBridesmaidManReq, opts ...grpc.CallOption) (*AddWeddingBridesmaidManResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddWeddingBridesmaidMan", varargs...)
	ret0, _ := ret[0].(*AddWeddingBridesmaidManResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddWeddingBridesmaidMan indicates an expected call of AddWeddingBridesmaidMan.
func (mr *MockChannelWeddingClientMockRecorder) AddWeddingBridesmaidMan(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddWeddingBridesmaidMan", reflect.TypeOf((*MockChannelWeddingClient)(nil).AddWeddingBridesmaidMan), varargs...)
}

// BatchGetChannelWeddingSimpleInfo mocks base method.
func (m *MockChannelWeddingClient) BatchGetChannelWeddingSimpleInfo(ctx context.Context, in *BatchGetChannelWeddingSimpleInfoReq, opts ...grpc.CallOption) (*BatchGetChannelWeddingSimpleInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetChannelWeddingSimpleInfo", varargs...)
	ret0, _ := ret[0].(*BatchGetChannelWeddingSimpleInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelWeddingSimpleInfo indicates an expected call of BatchGetChannelWeddingSimpleInfo.
func (mr *MockChannelWeddingClientMockRecorder) BatchGetChannelWeddingSimpleInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelWeddingSimpleInfo", reflect.TypeOf((*MockChannelWeddingClient)(nil).BatchGetChannelWeddingSimpleInfo), varargs...)
}

// BatchGetUserWeddingPose mocks base method.
func (m *MockChannelWeddingClient) BatchGetUserWeddingPose(ctx context.Context, in *BatchGetUserWeddingPoseReq, opts ...grpc.CallOption) (*BatchGetUserWeddingPoseResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetUserWeddingPose", varargs...)
	ret0, _ := ret[0].(*BatchGetUserWeddingPoseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserWeddingPose indicates an expected call of BatchGetUserWeddingPose.
func (mr *MockChannelWeddingClientMockRecorder) BatchGetUserWeddingPose(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserWeddingPose", reflect.TypeOf((*MockChannelWeddingClient)(nil).BatchGetUserWeddingPose), varargs...)
}

// BatchGetWeddingHappiness mocks base method.
func (m *MockChannelWeddingClient) BatchGetWeddingHappiness(ctx context.Context, in *BatchGetWeddingHappinessRequest, opts ...grpc.CallOption) (*BatchGetWeddingHappinessResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetWeddingHappiness", varargs...)
	ret0, _ := ret[0].(*BatchGetWeddingHappinessResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetWeddingHappiness indicates an expected call of BatchGetWeddingHappiness.
func (mr *MockChannelWeddingClientMockRecorder) BatchGetWeddingHappiness(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetWeddingHappiness", reflect.TypeOf((*MockChannelWeddingClient)(nil).BatchGetWeddingHappiness), varargs...)
}

// FixReservePresentCountOrder mocks base method.
func (m *MockChannelWeddingClient) FixReservePresentCountOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FixReservePresentCountOrder", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.EmptyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FixReservePresentCountOrder indicates an expected call of FixReservePresentCountOrder.
func (mr *MockChannelWeddingClientMockRecorder) FixReservePresentCountOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FixReservePresentCountOrder", reflect.TypeOf((*MockChannelWeddingClient)(nil).FixReservePresentCountOrder), varargs...)
}

// GetChannelWeddingInfo mocks base method.
func (m *MockChannelWeddingClient) GetChannelWeddingInfo(ctx context.Context, in *GetChannelWeddingInfoReq, opts ...grpc.CallOption) (*GetChannelWeddingInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelWeddingInfo", varargs...)
	ret0, _ := ret[0].(*GetChannelWeddingInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelWeddingInfo indicates an expected call of GetChannelWeddingInfo.
func (mr *MockChannelWeddingClientMockRecorder) GetChannelWeddingInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelWeddingInfo", reflect.TypeOf((*MockChannelWeddingClient)(nil).GetChannelWeddingInfo), varargs...)
}

// GetChannelWeddingRankEntryInfo mocks base method.
func (m *MockChannelWeddingClient) GetChannelWeddingRankEntryInfo(ctx context.Context, in *GetChannelWeddingRankEntryInfoReq, opts ...grpc.CallOption) (*GetChannelWeddingRankEntryInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelWeddingRankEntryInfo", varargs...)
	ret0, _ := ret[0].(*GetChannelWeddingRankEntryInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelWeddingRankEntryInfo indicates an expected call of GetChannelWeddingRankEntryInfo.
func (mr *MockChannelWeddingClientMockRecorder) GetChannelWeddingRankEntryInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelWeddingRankEntryInfo", reflect.TypeOf((*MockChannelWeddingClient)(nil).GetChannelWeddingRankEntryInfo), varargs...)
}

// GetChannelWeddingRankInfo mocks base method.
func (m *MockChannelWeddingClient) GetChannelWeddingRankInfo(ctx context.Context, in *GetChannelWeddingRankInfoReq, opts ...grpc.CallOption) (*GetChannelWeddingRankInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelWeddingRankInfo", varargs...)
	ret0, _ := ret[0].(*GetChannelWeddingRankInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelWeddingRankInfo indicates an expected call of GetChannelWeddingRankInfo.
func (mr *MockChannelWeddingClientMockRecorder) GetChannelWeddingRankInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelWeddingRankInfo", reflect.TypeOf((*MockChannelWeddingClient)(nil).GetChannelWeddingRankInfo), varargs...)
}

// GetUserChannelWeddingInfo mocks base method.
func (m *MockChannelWeddingClient) GetUserChannelWeddingInfo(ctx context.Context, in *GetUserChannelWeddingInfoReq, opts ...grpc.CallOption) (*GetUserChannelWeddingInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserChannelWeddingInfo", varargs...)
	ret0, _ := ret[0].(*GetUserChannelWeddingInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserChannelWeddingInfo indicates an expected call of GetUserChannelWeddingInfo.
func (mr *MockChannelWeddingClientMockRecorder) GetUserChannelWeddingInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserChannelWeddingInfo", reflect.TypeOf((*MockChannelWeddingClient)(nil).GetUserChannelWeddingInfo), varargs...)
}

// GetUserWeddingCertificate mocks base method.
func (m *MockChannelWeddingClient) GetUserWeddingCertificate(ctx context.Context, in *GetUserWeddingCertificateReq, opts ...grpc.CallOption) (*GetUserWeddingCertificateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserWeddingCertificate", varargs...)
	ret0, _ := ret[0].(*GetUserWeddingCertificateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserWeddingCertificate indicates an expected call of GetUserWeddingCertificate.
func (mr *MockChannelWeddingClientMockRecorder) GetUserWeddingCertificate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserWeddingCertificate", reflect.TypeOf((*MockChannelWeddingClient)(nil).GetUserWeddingCertificate), varargs...)
}

// GetUserWeddingPoseList mocks base method.
func (m *MockChannelWeddingClient) GetUserWeddingPoseList(ctx context.Context, in *GetUserWeddingPoseListReq, opts ...grpc.CallOption) (*GetUserWeddingPoseListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserWeddingPoseList", varargs...)
	ret0, _ := ret[0].(*GetUserWeddingPoseListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserWeddingPoseList indicates an expected call of GetUserWeddingPoseList.
func (mr *MockChannelWeddingClientMockRecorder) GetUserWeddingPoseList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserWeddingPoseList", reflect.TypeOf((*MockChannelWeddingClient)(nil).GetUserWeddingPoseList), varargs...)
}

// GetUserWeddingWeddingClips mocks base method.
func (m *MockChannelWeddingClient) GetUserWeddingWeddingClips(ctx context.Context, in *GetUserWeddingWeddingClipsReq, opts ...grpc.CallOption) (*GetUserWeddingWeddingClipsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserWeddingWeddingClips", varargs...)
	ret0, _ := ret[0].(*GetUserWeddingWeddingClipsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserWeddingWeddingClips indicates an expected call of GetUserWeddingWeddingClips.
func (mr *MockChannelWeddingClientMockRecorder) GetUserWeddingWeddingClips(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserWeddingWeddingClips", reflect.TypeOf((*MockChannelWeddingClient)(nil).GetUserWeddingWeddingClips), varargs...)
}

// GetWeddingGroupPhotoSeatList mocks base method.
func (m *MockChannelWeddingClient) GetWeddingGroupPhotoSeatList(ctx context.Context, in *GetWeddingGroupPhotoSeatListReq, opts ...grpc.CallOption) (*GetWeddingGroupPhotoSeatListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWeddingGroupPhotoSeatList", varargs...)
	ret0, _ := ret[0].(*GetWeddingGroupPhotoSeatListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingGroupPhotoSeatList indicates an expected call of GetWeddingGroupPhotoSeatList.
func (mr *MockChannelWeddingClientMockRecorder) GetWeddingGroupPhotoSeatList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingGroupPhotoSeatList", reflect.TypeOf((*MockChannelWeddingClient)(nil).GetWeddingGroupPhotoSeatList), varargs...)
}

// GetWeddingHighLightPresent mocks base method.
func (m *MockChannelWeddingClient) GetWeddingHighLightPresent(ctx context.Context, in *GetWeddingHighLightPresentRequest, opts ...grpc.CallOption) (*GetWeddingHighLightPresentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWeddingHighLightPresent", varargs...)
	ret0, _ := ret[0].(*GetWeddingHighLightPresentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingHighLightPresent indicates an expected call of GetWeddingHighLightPresent.
func (mr *MockChannelWeddingClientMockRecorder) GetWeddingHighLightPresent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingHighLightPresent", reflect.TypeOf((*MockChannelWeddingClient)(nil).GetWeddingHighLightPresent), varargs...)
}

// GetWeddingRecordByTimeRange mocks base method.
func (m *MockChannelWeddingClient) GetWeddingRecordByTimeRange(ctx context.Context, in *GetWeddingRecordByTimeRangeReq, opts ...grpc.CallOption) (*GetWeddingRecordByTimeRangeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWeddingRecordByTimeRange", varargs...)
	ret0, _ := ret[0].(*GetWeddingRecordByTimeRangeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingRecordByTimeRange indicates an expected call of GetWeddingRecordByTimeRange.
func (mr *MockChannelWeddingClientMockRecorder) GetWeddingRecordByTimeRange(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingRecordByTimeRange", reflect.TypeOf((*MockChannelWeddingClient)(nil).GetWeddingRecordByTimeRange), varargs...)
}

// GetWeddingScheduleList mocks base method.
func (m *MockChannelWeddingClient) GetWeddingScheduleList(ctx context.Context, in *GetWeddingScheduleListReq, opts ...grpc.CallOption) (*GetWeddingScheduleListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWeddingScheduleList", varargs...)
	ret0, _ := ret[0].(*GetWeddingScheduleListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingScheduleList indicates an expected call of GetWeddingScheduleList.
func (mr *MockChannelWeddingClientMockRecorder) GetWeddingScheduleList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingScheduleList", reflect.TypeOf((*MockChannelWeddingClient)(nil).GetWeddingScheduleList), varargs...)
}

// PageGetGoingWeddingList mocks base method.
func (m *MockChannelWeddingClient) PageGetGoingWeddingList(ctx context.Context, in *PageGetGoingWeddingListReq, opts ...grpc.CallOption) (*PageGetGoingWeddingListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PageGetGoingWeddingList", varargs...)
	ret0, _ := ret[0].(*PageGetGoingWeddingListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PageGetGoingWeddingList indicates an expected call of PageGetGoingWeddingList.
func (mr *MockChannelWeddingClientMockRecorder) PageGetGoingWeddingList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PageGetGoingWeddingList", reflect.TypeOf((*MockChannelWeddingClient)(nil).PageGetGoingWeddingList), varargs...)
}

// ReportWeddingBridesmaidMan mocks base method.
func (m *MockChannelWeddingClient) ReportWeddingBridesmaidMan(ctx context.Context, in *ReportWeddingBridesmaidManReq, opts ...grpc.CallOption) (*ReportWeddingBridesmaidManResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReportWeddingBridesmaidMan", varargs...)
	ret0, _ := ret[0].(*ReportWeddingBridesmaidManResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportWeddingBridesmaidMan indicates an expected call of ReportWeddingBridesmaidMan.
func (mr *MockChannelWeddingClientMockRecorder) ReportWeddingBridesmaidMan(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportWeddingBridesmaidMan", reflect.TypeOf((*MockChannelWeddingClient)(nil).ReportWeddingBridesmaidMan), varargs...)
}

// ReportWeddingScenePic mocks base method.
func (m *MockChannelWeddingClient) ReportWeddingScenePic(ctx context.Context, in *ReportWeddingScenePicReq, opts ...grpc.CallOption) (*ReportWeddingScenePicResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReportWeddingScenePic", varargs...)
	ret0, _ := ret[0].(*ReportWeddingScenePicResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportWeddingScenePic indicates an expected call of ReportWeddingScenePic.
func (mr *MockChannelWeddingClientMockRecorder) ReportWeddingScenePic(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportWeddingScenePic", reflect.TypeOf((*MockChannelWeddingClient)(nil).ReportWeddingScenePic), varargs...)
}

// ReservePresentTimeRangeCount mocks base method.
func (m *MockChannelWeddingClient) ReservePresentTimeRangeCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReservePresentTimeRangeCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReservePresentTimeRangeCount indicates an expected call of ReservePresentTimeRangeCount.
func (mr *MockChannelWeddingClientMockRecorder) ReservePresentTimeRangeCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReservePresentTimeRangeCount", reflect.TypeOf((*MockChannelWeddingClient)(nil).ReservePresentTimeRangeCount), varargs...)
}

// ReservePresentTimeRangeOrderIds mocks base method.
func (m *MockChannelWeddingClient) ReservePresentTimeRangeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReservePresentTimeRangeOrderIds", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReservePresentTimeRangeOrderIds indicates an expected call of ReservePresentTimeRangeOrderIds.
func (mr *MockChannelWeddingClientMockRecorder) ReservePresentTimeRangeOrderIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReservePresentTimeRangeOrderIds", reflect.TypeOf((*MockChannelWeddingClient)(nil).ReservePresentTimeRangeOrderIds), varargs...)
}

// ReserveWedding mocks base method.
func (m *MockChannelWeddingClient) ReserveWedding(ctx context.Context, in *ReserveWeddingReq, opts ...grpc.CallOption) (*ReserveWeddingResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReserveWedding", varargs...)
	ret0, _ := ret[0].(*ReserveWeddingResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReserveWedding indicates an expected call of ReserveWedding.
func (mr *MockChannelWeddingClientMockRecorder) ReserveWedding(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReserveWedding", reflect.TypeOf((*MockChannelWeddingClient)(nil).ReserveWedding), varargs...)
}

// SendWeddingReservePresent mocks base method.
func (m *MockChannelWeddingClient) SendWeddingReservePresent(ctx context.Context, in *SendWeddingReservePresentReq, opts ...grpc.CallOption) (*SendWeddingReservePresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendWeddingReservePresent", varargs...)
	ret0, _ := ret[0].(*SendWeddingReservePresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendWeddingReservePresent indicates an expected call of SendWeddingReservePresent.
func (mr *MockChannelWeddingClientMockRecorder) SendWeddingReservePresent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendWeddingReservePresent", reflect.TypeOf((*MockChannelWeddingClient)(nil).SendWeddingReservePresent), varargs...)
}

// SetUserWeddingGroupPhotoSeat mocks base method.
func (m *MockChannelWeddingClient) SetUserWeddingGroupPhotoSeat(ctx context.Context, in *SetUserWeddingGroupPhotoSeatReq, opts ...grpc.CallOption) (*SetUserWeddingGroupPhotoSeatResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserWeddingGroupPhotoSeat", varargs...)
	ret0, _ := ret[0].(*SetUserWeddingGroupPhotoSeatResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserWeddingGroupPhotoSeat indicates an expected call of SetUserWeddingGroupPhotoSeat.
func (mr *MockChannelWeddingClientMockRecorder) SetUserWeddingGroupPhotoSeat(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserWeddingGroupPhotoSeat", reflect.TypeOf((*MockChannelWeddingClient)(nil).SetUserWeddingGroupPhotoSeat), varargs...)
}

// SetUserWeddingOrientation mocks base method.
func (m *MockChannelWeddingClient) SetUserWeddingOrientation(ctx context.Context, in *SetUserWeddingOrientationReq, opts ...grpc.CallOption) (*SetUserWeddingOrientationResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserWeddingOrientation", varargs...)
	ret0, _ := ret[0].(*SetUserWeddingOrientationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserWeddingOrientation indicates an expected call of SetUserWeddingOrientation.
func (mr *MockChannelWeddingClientMockRecorder) SetUserWeddingOrientation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserWeddingOrientation", reflect.TypeOf((*MockChannelWeddingClient)(nil).SetUserWeddingOrientation), varargs...)
}

// SetUserWeddingPose mocks base method.
func (m *MockChannelWeddingClient) SetUserWeddingPose(ctx context.Context, in *SetUserWeddingPoseReq, opts ...grpc.CallOption) (*SetUserWeddingPoseResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserWeddingPose", varargs...)
	ret0, _ := ret[0].(*SetUserWeddingPoseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserWeddingPose indicates an expected call of SetUserWeddingPose.
func (mr *MockChannelWeddingClientMockRecorder) SetUserWeddingPose(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserWeddingPose", reflect.TypeOf((*MockChannelWeddingClient)(nil).SetUserWeddingPose), varargs...)
}

// SwitchWeddingStage mocks base method.
func (m *MockChannelWeddingClient) SwitchWeddingStage(ctx context.Context, in *SwitchWeddingStageReq, opts ...grpc.CallOption) (*SwitchWeddingStageResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SwitchWeddingStage", varargs...)
	ret0, _ := ret[0].(*SwitchWeddingStageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SwitchWeddingStage indicates an expected call of SwitchWeddingStage.
func (mr *MockChannelWeddingClientMockRecorder) SwitchWeddingStage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwitchWeddingStage", reflect.TypeOf((*MockChannelWeddingClient)(nil).SwitchWeddingStage), varargs...)
}

// TakeWeddingGroupPhoto mocks base method.
func (m *MockChannelWeddingClient) TakeWeddingGroupPhoto(ctx context.Context, in *TakeWeddingGroupPhotoReq, opts ...grpc.CallOption) (*TakeWeddingGroupPhotoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TakeWeddingGroupPhoto", varargs...)
	ret0, _ := ret[0].(*TakeWeddingGroupPhotoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TakeWeddingGroupPhoto indicates an expected call of TakeWeddingGroupPhoto.
func (mr *MockChannelWeddingClientMockRecorder) TakeWeddingGroupPhoto(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TakeWeddingGroupPhoto", reflect.TypeOf((*MockChannelWeddingClient)(nil).TakeWeddingGroupPhoto), varargs...)
}

// TestAfterFreeWeddingImXml mocks base method.
func (m *MockChannelWeddingClient) TestAfterFreeWeddingImXml(ctx context.Context, in *TestAfterFreeWeddingImXmlReq, opts ...grpc.CallOption) (*TestAfterFreeWeddingImXmlResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestAfterFreeWeddingImXml", varargs...)
	ret0, _ := ret[0].(*TestAfterFreeWeddingImXmlResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestAfterFreeWeddingImXml indicates an expected call of TestAfterFreeWeddingImXml.
func (mr *MockChannelWeddingClientMockRecorder) TestAfterFreeWeddingImXml(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestAfterFreeWeddingImXml", reflect.TypeOf((*MockChannelWeddingClient)(nil).TestAfterFreeWeddingImXml), varargs...)
}

// TestMvpSettlement mocks base method.
func (m *MockChannelWeddingClient) TestMvpSettlement(ctx context.Context, in *TestMvpSettlementReq, opts ...grpc.CallOption) (*TestMvpSettlementResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestMvpSettlement", varargs...)
	ret0, _ := ret[0].(*TestMvpSettlementResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestMvpSettlement indicates an expected call of TestMvpSettlement.
func (mr *MockChannelWeddingClientMockRecorder) TestMvpSettlement(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestMvpSettlement", reflect.TypeOf((*MockChannelWeddingClient)(nil).TestMvpSettlement), varargs...)
}

// TestWeddingScenePicIm mocks base method.
func (m *MockChannelWeddingClient) TestWeddingScenePicIm(ctx context.Context, in *TestWeddingScenePicImReq, opts ...grpc.CallOption) (*TestWeddingScenePicImResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestWeddingScenePicIm", varargs...)
	ret0, _ := ret[0].(*TestWeddingScenePicImResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestWeddingScenePicIm indicates an expected call of TestWeddingScenePicIm.
func (mr *MockChannelWeddingClientMockRecorder) TestWeddingScenePicIm(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestWeddingScenePicIm", reflect.TypeOf((*MockChannelWeddingClient)(nil).TestWeddingScenePicIm), varargs...)
}

// MockChannelWeddingServer is a mock of ChannelWeddingServer interface.
type MockChannelWeddingServer struct {
	ctrl     *gomock.Controller
	recorder *MockChannelWeddingServerMockRecorder
}

// MockChannelWeddingServerMockRecorder is the mock recorder for MockChannelWeddingServer.
type MockChannelWeddingServerMockRecorder struct {
	mock *MockChannelWeddingServer
}

// NewMockChannelWeddingServer creates a new mock instance.
func NewMockChannelWeddingServer(ctrl *gomock.Controller) *MockChannelWeddingServer {
	mock := &MockChannelWeddingServer{ctrl: ctrl}
	mock.recorder = &MockChannelWeddingServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelWeddingServer) EXPECT() *MockChannelWeddingServerMockRecorder {
	return m.recorder
}

// AddWeddingBridesmaidMan mocks base method.
func (m *MockChannelWeddingServer) AddWeddingBridesmaidMan(ctx context.Context, in *AddWeddingBridesmaidManReq) (*AddWeddingBridesmaidManResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddWeddingBridesmaidMan", ctx, in)
	ret0, _ := ret[0].(*AddWeddingBridesmaidManResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddWeddingBridesmaidMan indicates an expected call of AddWeddingBridesmaidMan.
func (mr *MockChannelWeddingServerMockRecorder) AddWeddingBridesmaidMan(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddWeddingBridesmaidMan", reflect.TypeOf((*MockChannelWeddingServer)(nil).AddWeddingBridesmaidMan), ctx, in)
}

// BatchGetChannelWeddingSimpleInfo mocks base method.
func (m *MockChannelWeddingServer) BatchGetChannelWeddingSimpleInfo(ctx context.Context, in *BatchGetChannelWeddingSimpleInfoReq) (*BatchGetChannelWeddingSimpleInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelWeddingSimpleInfo", ctx, in)
	ret0, _ := ret[0].(*BatchGetChannelWeddingSimpleInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelWeddingSimpleInfo indicates an expected call of BatchGetChannelWeddingSimpleInfo.
func (mr *MockChannelWeddingServerMockRecorder) BatchGetChannelWeddingSimpleInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelWeddingSimpleInfo", reflect.TypeOf((*MockChannelWeddingServer)(nil).BatchGetChannelWeddingSimpleInfo), ctx, in)
}

// BatchGetUserWeddingPose mocks base method.
func (m *MockChannelWeddingServer) BatchGetUserWeddingPose(ctx context.Context, in *BatchGetUserWeddingPoseReq) (*BatchGetUserWeddingPoseResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserWeddingPose", ctx, in)
	ret0, _ := ret[0].(*BatchGetUserWeddingPoseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserWeddingPose indicates an expected call of BatchGetUserWeddingPose.
func (mr *MockChannelWeddingServerMockRecorder) BatchGetUserWeddingPose(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserWeddingPose", reflect.TypeOf((*MockChannelWeddingServer)(nil).BatchGetUserWeddingPose), ctx, in)
}

// BatchGetWeddingHappiness mocks base method.
func (m *MockChannelWeddingServer) BatchGetWeddingHappiness(ctx context.Context, in *BatchGetWeddingHappinessRequest) (*BatchGetWeddingHappinessResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetWeddingHappiness", ctx, in)
	ret0, _ := ret[0].(*BatchGetWeddingHappinessResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetWeddingHappiness indicates an expected call of BatchGetWeddingHappiness.
func (mr *MockChannelWeddingServerMockRecorder) BatchGetWeddingHappiness(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetWeddingHappiness", reflect.TypeOf((*MockChannelWeddingServer)(nil).BatchGetWeddingHappiness), ctx, in)
}

// FixReservePresentCountOrder mocks base method.
func (m *MockChannelWeddingServer) FixReservePresentCountOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FixReservePresentCountOrder", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.EmptyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FixReservePresentCountOrder indicates an expected call of FixReservePresentCountOrder.
func (mr *MockChannelWeddingServerMockRecorder) FixReservePresentCountOrder(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FixReservePresentCountOrder", reflect.TypeOf((*MockChannelWeddingServer)(nil).FixReservePresentCountOrder), ctx, in)
}

// GetChannelWeddingInfo mocks base method.
func (m *MockChannelWeddingServer) GetChannelWeddingInfo(ctx context.Context, in *GetChannelWeddingInfoReq) (*GetChannelWeddingInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelWeddingInfo", ctx, in)
	ret0, _ := ret[0].(*GetChannelWeddingInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelWeddingInfo indicates an expected call of GetChannelWeddingInfo.
func (mr *MockChannelWeddingServerMockRecorder) GetChannelWeddingInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelWeddingInfo", reflect.TypeOf((*MockChannelWeddingServer)(nil).GetChannelWeddingInfo), ctx, in)
}

// GetChannelWeddingRankEntryInfo mocks base method.
func (m *MockChannelWeddingServer) GetChannelWeddingRankEntryInfo(ctx context.Context, in *GetChannelWeddingRankEntryInfoReq) (*GetChannelWeddingRankEntryInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelWeddingRankEntryInfo", ctx, in)
	ret0, _ := ret[0].(*GetChannelWeddingRankEntryInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelWeddingRankEntryInfo indicates an expected call of GetChannelWeddingRankEntryInfo.
func (mr *MockChannelWeddingServerMockRecorder) GetChannelWeddingRankEntryInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelWeddingRankEntryInfo", reflect.TypeOf((*MockChannelWeddingServer)(nil).GetChannelWeddingRankEntryInfo), ctx, in)
}

// GetChannelWeddingRankInfo mocks base method.
func (m *MockChannelWeddingServer) GetChannelWeddingRankInfo(ctx context.Context, in *GetChannelWeddingRankInfoReq) (*GetChannelWeddingRankInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelWeddingRankInfo", ctx, in)
	ret0, _ := ret[0].(*GetChannelWeddingRankInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelWeddingRankInfo indicates an expected call of GetChannelWeddingRankInfo.
func (mr *MockChannelWeddingServerMockRecorder) GetChannelWeddingRankInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelWeddingRankInfo", reflect.TypeOf((*MockChannelWeddingServer)(nil).GetChannelWeddingRankInfo), ctx, in)
}

// GetUserChannelWeddingInfo mocks base method.
func (m *MockChannelWeddingServer) GetUserChannelWeddingInfo(ctx context.Context, in *GetUserChannelWeddingInfoReq) (*GetUserChannelWeddingInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserChannelWeddingInfo", ctx, in)
	ret0, _ := ret[0].(*GetUserChannelWeddingInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserChannelWeddingInfo indicates an expected call of GetUserChannelWeddingInfo.
func (mr *MockChannelWeddingServerMockRecorder) GetUserChannelWeddingInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserChannelWeddingInfo", reflect.TypeOf((*MockChannelWeddingServer)(nil).GetUserChannelWeddingInfo), ctx, in)
}

// GetUserWeddingCertificate mocks base method.
func (m *MockChannelWeddingServer) GetUserWeddingCertificate(ctx context.Context, in *GetUserWeddingCertificateReq) (*GetUserWeddingCertificateResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserWeddingCertificate", ctx, in)
	ret0, _ := ret[0].(*GetUserWeddingCertificateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserWeddingCertificate indicates an expected call of GetUserWeddingCertificate.
func (mr *MockChannelWeddingServerMockRecorder) GetUserWeddingCertificate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserWeddingCertificate", reflect.TypeOf((*MockChannelWeddingServer)(nil).GetUserWeddingCertificate), ctx, in)
}

// GetUserWeddingPoseList mocks base method.
func (m *MockChannelWeddingServer) GetUserWeddingPoseList(ctx context.Context, in *GetUserWeddingPoseListReq) (*GetUserWeddingPoseListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserWeddingPoseList", ctx, in)
	ret0, _ := ret[0].(*GetUserWeddingPoseListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserWeddingPoseList indicates an expected call of GetUserWeddingPoseList.
func (mr *MockChannelWeddingServerMockRecorder) GetUserWeddingPoseList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserWeddingPoseList", reflect.TypeOf((*MockChannelWeddingServer)(nil).GetUserWeddingPoseList), ctx, in)
}

// GetUserWeddingWeddingClips mocks base method.
func (m *MockChannelWeddingServer) GetUserWeddingWeddingClips(ctx context.Context, in *GetUserWeddingWeddingClipsReq) (*GetUserWeddingWeddingClipsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserWeddingWeddingClips", ctx, in)
	ret0, _ := ret[0].(*GetUserWeddingWeddingClipsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserWeddingWeddingClips indicates an expected call of GetUserWeddingWeddingClips.
func (mr *MockChannelWeddingServerMockRecorder) GetUserWeddingWeddingClips(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserWeddingWeddingClips", reflect.TypeOf((*MockChannelWeddingServer)(nil).GetUserWeddingWeddingClips), ctx, in)
}

// GetWeddingGroupPhotoSeatList mocks base method.
func (m *MockChannelWeddingServer) GetWeddingGroupPhotoSeatList(ctx context.Context, in *GetWeddingGroupPhotoSeatListReq) (*GetWeddingGroupPhotoSeatListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingGroupPhotoSeatList", ctx, in)
	ret0, _ := ret[0].(*GetWeddingGroupPhotoSeatListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingGroupPhotoSeatList indicates an expected call of GetWeddingGroupPhotoSeatList.
func (mr *MockChannelWeddingServerMockRecorder) GetWeddingGroupPhotoSeatList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingGroupPhotoSeatList", reflect.TypeOf((*MockChannelWeddingServer)(nil).GetWeddingGroupPhotoSeatList), ctx, in)
}

// GetWeddingHighLightPresent mocks base method.
func (m *MockChannelWeddingServer) GetWeddingHighLightPresent(ctx context.Context, in *GetWeddingHighLightPresentRequest) (*GetWeddingHighLightPresentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingHighLightPresent", ctx, in)
	ret0, _ := ret[0].(*GetWeddingHighLightPresentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingHighLightPresent indicates an expected call of GetWeddingHighLightPresent.
func (mr *MockChannelWeddingServerMockRecorder) GetWeddingHighLightPresent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingHighLightPresent", reflect.TypeOf((*MockChannelWeddingServer)(nil).GetWeddingHighLightPresent), ctx, in)
}

// GetWeddingRecordByTimeRange mocks base method.
func (m *MockChannelWeddingServer) GetWeddingRecordByTimeRange(ctx context.Context, in *GetWeddingRecordByTimeRangeReq) (*GetWeddingRecordByTimeRangeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingRecordByTimeRange", ctx, in)
	ret0, _ := ret[0].(*GetWeddingRecordByTimeRangeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingRecordByTimeRange indicates an expected call of GetWeddingRecordByTimeRange.
func (mr *MockChannelWeddingServerMockRecorder) GetWeddingRecordByTimeRange(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingRecordByTimeRange", reflect.TypeOf((*MockChannelWeddingServer)(nil).GetWeddingRecordByTimeRange), ctx, in)
}

// GetWeddingScheduleList mocks base method.
func (m *MockChannelWeddingServer) GetWeddingScheduleList(ctx context.Context, in *GetWeddingScheduleListReq) (*GetWeddingScheduleListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingScheduleList", ctx, in)
	ret0, _ := ret[0].(*GetWeddingScheduleListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingScheduleList indicates an expected call of GetWeddingScheduleList.
func (mr *MockChannelWeddingServerMockRecorder) GetWeddingScheduleList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingScheduleList", reflect.TypeOf((*MockChannelWeddingServer)(nil).GetWeddingScheduleList), ctx, in)
}

// PageGetGoingWeddingList mocks base method.
func (m *MockChannelWeddingServer) PageGetGoingWeddingList(ctx context.Context, in *PageGetGoingWeddingListReq) (*PageGetGoingWeddingListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PageGetGoingWeddingList", ctx, in)
	ret0, _ := ret[0].(*PageGetGoingWeddingListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PageGetGoingWeddingList indicates an expected call of PageGetGoingWeddingList.
func (mr *MockChannelWeddingServerMockRecorder) PageGetGoingWeddingList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PageGetGoingWeddingList", reflect.TypeOf((*MockChannelWeddingServer)(nil).PageGetGoingWeddingList), ctx, in)
}

// ReportWeddingBridesmaidMan mocks base method.
func (m *MockChannelWeddingServer) ReportWeddingBridesmaidMan(ctx context.Context, in *ReportWeddingBridesmaidManReq) (*ReportWeddingBridesmaidManResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportWeddingBridesmaidMan", ctx, in)
	ret0, _ := ret[0].(*ReportWeddingBridesmaidManResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportWeddingBridesmaidMan indicates an expected call of ReportWeddingBridesmaidMan.
func (mr *MockChannelWeddingServerMockRecorder) ReportWeddingBridesmaidMan(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportWeddingBridesmaidMan", reflect.TypeOf((*MockChannelWeddingServer)(nil).ReportWeddingBridesmaidMan), ctx, in)
}

// ReportWeddingScenePic mocks base method.
func (m *MockChannelWeddingServer) ReportWeddingScenePic(ctx context.Context, in *ReportWeddingScenePicReq) (*ReportWeddingScenePicResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportWeddingScenePic", ctx, in)
	ret0, _ := ret[0].(*ReportWeddingScenePicResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportWeddingScenePic indicates an expected call of ReportWeddingScenePic.
func (mr *MockChannelWeddingServerMockRecorder) ReportWeddingScenePic(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportWeddingScenePic", reflect.TypeOf((*MockChannelWeddingServer)(nil).ReportWeddingScenePic), ctx, in)
}

// ReservePresentTimeRangeCount mocks base method.
func (m *MockChannelWeddingServer) ReservePresentTimeRangeCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReservePresentTimeRangeCount", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReservePresentTimeRangeCount indicates an expected call of ReservePresentTimeRangeCount.
func (mr *MockChannelWeddingServerMockRecorder) ReservePresentTimeRangeCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReservePresentTimeRangeCount", reflect.TypeOf((*MockChannelWeddingServer)(nil).ReservePresentTimeRangeCount), ctx, in)
}

// ReservePresentTimeRangeOrderIds mocks base method.
func (m *MockChannelWeddingServer) ReservePresentTimeRangeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReservePresentTimeRangeOrderIds", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReservePresentTimeRangeOrderIds indicates an expected call of ReservePresentTimeRangeOrderIds.
func (mr *MockChannelWeddingServerMockRecorder) ReservePresentTimeRangeOrderIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReservePresentTimeRangeOrderIds", reflect.TypeOf((*MockChannelWeddingServer)(nil).ReservePresentTimeRangeOrderIds), ctx, in)
}

// ReserveWedding mocks base method.
func (m *MockChannelWeddingServer) ReserveWedding(ctx context.Context, in *ReserveWeddingReq) (*ReserveWeddingResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReserveWedding", ctx, in)
	ret0, _ := ret[0].(*ReserveWeddingResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReserveWedding indicates an expected call of ReserveWedding.
func (mr *MockChannelWeddingServerMockRecorder) ReserveWedding(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReserveWedding", reflect.TypeOf((*MockChannelWeddingServer)(nil).ReserveWedding), ctx, in)
}

// SendWeddingReservePresent mocks base method.
func (m *MockChannelWeddingServer) SendWeddingReservePresent(ctx context.Context, in *SendWeddingReservePresentReq) (*SendWeddingReservePresentResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendWeddingReservePresent", ctx, in)
	ret0, _ := ret[0].(*SendWeddingReservePresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendWeddingReservePresent indicates an expected call of SendWeddingReservePresent.
func (mr *MockChannelWeddingServerMockRecorder) SendWeddingReservePresent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendWeddingReservePresent", reflect.TypeOf((*MockChannelWeddingServer)(nil).SendWeddingReservePresent), ctx, in)
}

// SetUserWeddingGroupPhotoSeat mocks base method.
func (m *MockChannelWeddingServer) SetUserWeddingGroupPhotoSeat(ctx context.Context, in *SetUserWeddingGroupPhotoSeatReq) (*SetUserWeddingGroupPhotoSeatResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserWeddingGroupPhotoSeat", ctx, in)
	ret0, _ := ret[0].(*SetUserWeddingGroupPhotoSeatResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserWeddingGroupPhotoSeat indicates an expected call of SetUserWeddingGroupPhotoSeat.
func (mr *MockChannelWeddingServerMockRecorder) SetUserWeddingGroupPhotoSeat(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserWeddingGroupPhotoSeat", reflect.TypeOf((*MockChannelWeddingServer)(nil).SetUserWeddingGroupPhotoSeat), ctx, in)
}

// SetUserWeddingOrientation mocks base method.
func (m *MockChannelWeddingServer) SetUserWeddingOrientation(ctx context.Context, in *SetUserWeddingOrientationReq) (*SetUserWeddingOrientationResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserWeddingOrientation", ctx, in)
	ret0, _ := ret[0].(*SetUserWeddingOrientationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserWeddingOrientation indicates an expected call of SetUserWeddingOrientation.
func (mr *MockChannelWeddingServerMockRecorder) SetUserWeddingOrientation(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserWeddingOrientation", reflect.TypeOf((*MockChannelWeddingServer)(nil).SetUserWeddingOrientation), ctx, in)
}

// SetUserWeddingPose mocks base method.
func (m *MockChannelWeddingServer) SetUserWeddingPose(ctx context.Context, in *SetUserWeddingPoseReq) (*SetUserWeddingPoseResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserWeddingPose", ctx, in)
	ret0, _ := ret[0].(*SetUserWeddingPoseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserWeddingPose indicates an expected call of SetUserWeddingPose.
func (mr *MockChannelWeddingServerMockRecorder) SetUserWeddingPose(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserWeddingPose", reflect.TypeOf((*MockChannelWeddingServer)(nil).SetUserWeddingPose), ctx, in)
}

// SwitchWeddingStage mocks base method.
func (m *MockChannelWeddingServer) SwitchWeddingStage(ctx context.Context, in *SwitchWeddingStageReq) (*SwitchWeddingStageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SwitchWeddingStage", ctx, in)
	ret0, _ := ret[0].(*SwitchWeddingStageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SwitchWeddingStage indicates an expected call of SwitchWeddingStage.
func (mr *MockChannelWeddingServerMockRecorder) SwitchWeddingStage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwitchWeddingStage", reflect.TypeOf((*MockChannelWeddingServer)(nil).SwitchWeddingStage), ctx, in)
}

// TakeWeddingGroupPhoto mocks base method.
func (m *MockChannelWeddingServer) TakeWeddingGroupPhoto(ctx context.Context, in *TakeWeddingGroupPhotoReq) (*TakeWeddingGroupPhotoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TakeWeddingGroupPhoto", ctx, in)
	ret0, _ := ret[0].(*TakeWeddingGroupPhotoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TakeWeddingGroupPhoto indicates an expected call of TakeWeddingGroupPhoto.
func (mr *MockChannelWeddingServerMockRecorder) TakeWeddingGroupPhoto(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TakeWeddingGroupPhoto", reflect.TypeOf((*MockChannelWeddingServer)(nil).TakeWeddingGroupPhoto), ctx, in)
}

// TestAfterFreeWeddingImXml mocks base method.
func (m *MockChannelWeddingServer) TestAfterFreeWeddingImXml(ctx context.Context, in *TestAfterFreeWeddingImXmlReq) (*TestAfterFreeWeddingImXmlResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestAfterFreeWeddingImXml", ctx, in)
	ret0, _ := ret[0].(*TestAfterFreeWeddingImXmlResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestAfterFreeWeddingImXml indicates an expected call of TestAfterFreeWeddingImXml.
func (mr *MockChannelWeddingServerMockRecorder) TestAfterFreeWeddingImXml(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestAfterFreeWeddingImXml", reflect.TypeOf((*MockChannelWeddingServer)(nil).TestAfterFreeWeddingImXml), ctx, in)
}

// TestMvpSettlement mocks base method.
func (m *MockChannelWeddingServer) TestMvpSettlement(ctx context.Context, in *TestMvpSettlementReq) (*TestMvpSettlementResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestMvpSettlement", ctx, in)
	ret0, _ := ret[0].(*TestMvpSettlementResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestMvpSettlement indicates an expected call of TestMvpSettlement.
func (mr *MockChannelWeddingServerMockRecorder) TestMvpSettlement(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestMvpSettlement", reflect.TypeOf((*MockChannelWeddingServer)(nil).TestMvpSettlement), ctx, in)
}

// TestWeddingScenePicIm mocks base method.
func (m *MockChannelWeddingServer) TestWeddingScenePicIm(ctx context.Context, in *TestWeddingScenePicImReq) (*TestWeddingScenePicImResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestWeddingScenePicIm", ctx, in)
	ret0, _ := ret[0].(*TestWeddingScenePicImResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestWeddingScenePicIm indicates an expected call of TestWeddingScenePicIm.
func (mr *MockChannelWeddingServerMockRecorder) TestWeddingScenePicIm(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestWeddingScenePicIm", reflect.TypeOf((*MockChannelWeddingServer)(nil).TestWeddingScenePicIm), ctx, in)
}
