// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channel-wedding/channel-wedding.proto

package channel_wedding // import "golang.52tt.com/protocol/services/channel-wedding"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 婚礼阶段枚举
type WeddingStage int32

const (
	WeddingStage_WEDDING_STAGE_UNSPECIFIED       WeddingStage = 0
	WeddingStage_WEDDING_STAGE_WELCOME_GUEST     WeddingStage = 1
	WeddingStage_WEDDING_STAGE_BRIDE_GROOM_ENTER WeddingStage = 2
	WeddingStage_WEDDING_STAGE_LOVE_DECLARATION  WeddingStage = 3
	WeddingStage_WEDDING_STAGE_EXCHANGE_RING     WeddingStage = 4
	WeddingStage_WEDDING_STAGE_HIGHLIGHT         WeddingStage = 5
	WeddingStage_WEDDING_STAGE_GROUP_PHOTO       WeddingStage = 6
)

var WeddingStage_name = map[int32]string{
	0: "WEDDING_STAGE_UNSPECIFIED",
	1: "WEDDING_STAGE_WELCOME_GUEST",
	2: "WEDDING_STAGE_BRIDE_GROOM_ENTER",
	3: "WEDDING_STAGE_LOVE_DECLARATION",
	4: "WEDDING_STAGE_EXCHANGE_RING",
	5: "WEDDING_STAGE_HIGHLIGHT",
	6: "WEDDING_STAGE_GROUP_PHOTO",
}
var WeddingStage_value = map[string]int32{
	"WEDDING_STAGE_UNSPECIFIED":       0,
	"WEDDING_STAGE_WELCOME_GUEST":     1,
	"WEDDING_STAGE_BRIDE_GROOM_ENTER": 2,
	"WEDDING_STAGE_LOVE_DECLARATION":  3,
	"WEDDING_STAGE_EXCHANGE_RING":     4,
	"WEDDING_STAGE_HIGHLIGHT":         5,
	"WEDDING_STAGE_GROUP_PHOTO":       6,
}

func (x WeddingStage) String() string {
	return proto.EnumName(WeddingStage_name, int32(x))
}
func (WeddingStage) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{0}
}

// 合影留恋子阶段枚举
type GroupPhotoSubStage int32

const (
	GroupPhotoSubStage_GROUP_PHOTO_SUB_STAGE_UNSPECIFIED GroupPhotoSubStage = 0
	GroupPhotoSubStage_GROUP_PHOTO_SUB_STAGE_PHOTOGRAPH  GroupPhotoSubStage = 1
)

var GroupPhotoSubStage_name = map[int32]string{
	0: "GROUP_PHOTO_SUB_STAGE_UNSPECIFIED",
	1: "GROUP_PHOTO_SUB_STAGE_PHOTOGRAPH",
}
var GroupPhotoSubStage_value = map[string]int32{
	"GROUP_PHOTO_SUB_STAGE_UNSPECIFIED": 0,
	"GROUP_PHOTO_SUB_STAGE_PHOTOGRAPH":  1,
}

func (x GroupPhotoSubStage) String() string {
	return proto.EnumName(GroupPhotoSubStage_name, int32(x))
}
func (GroupPhotoSubStage) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{1}
}

// 婚礼场景动画枚举
type WeddingScene int32

const (
	WeddingScene_WEDDING_SCENE_UNSPECIFIED       WeddingScene = 0
	WeddingScene_WEDDING_SCENE_BRIDE_GROOM_ENTER WeddingScene = 1
	WeddingScene_WEDDING_SCENE_EXCHANGE_RING     WeddingScene = 2
	WeddingScene_WEDDING_SCENE_HIGHLIGHT         WeddingScene = 3
	WeddingScene_WEDDING_SCENE_GROUP_PHOTO       WeddingScene = 4
)

var WeddingScene_name = map[int32]string{
	0: "WEDDING_SCENE_UNSPECIFIED",
	1: "WEDDING_SCENE_BRIDE_GROOM_ENTER",
	2: "WEDDING_SCENE_EXCHANGE_RING",
	3: "WEDDING_SCENE_HIGHLIGHT",
	4: "WEDDING_SCENE_GROUP_PHOTO",
}
var WeddingScene_value = map[string]int32{
	"WEDDING_SCENE_UNSPECIFIED":       0,
	"WEDDING_SCENE_BRIDE_GROOM_ENTER": 1,
	"WEDDING_SCENE_EXCHANGE_RING":     2,
	"WEDDING_SCENE_HIGHLIGHT":         3,
	"WEDDING_SCENE_GROUP_PHOTO":       4,
}

func (x WeddingScene) String() string {
	return proto.EnumName(WeddingScene_name, int32(x))
}
func (WeddingScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{2}
}

type WeddingThemeType int32

const (
	WeddingThemeType_WEDDING_THEME_TYPE_UNSPECIFIED WeddingThemeType = 0
	WeddingThemeType_WEDDING_THEME_TYPE_FREE        WeddingThemeType = 1
	WeddingThemeType_WEDDING_THEME_TYPE_PAY         WeddingThemeType = 2
)

var WeddingThemeType_name = map[int32]string{
	0: "WEDDING_THEME_TYPE_UNSPECIFIED",
	1: "WEDDING_THEME_TYPE_FREE",
	2: "WEDDING_THEME_TYPE_PAY",
}
var WeddingThemeType_value = map[string]int32{
	"WEDDING_THEME_TYPE_UNSPECIFIED": 0,
	"WEDDING_THEME_TYPE_FREE":        1,
	"WEDDING_THEME_TYPE_PAY":         2,
}

func (x WeddingThemeType) String() string {
	return proto.EnumName(WeddingThemeType_name, int32(x))
}
func (WeddingThemeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{3}
}

// 婚礼房阶段配置
type WeddingStageCfg struct {
	Stage                uint32   `protobuf:"varint,1,opt,name=stage,proto3" json:"stage,omitempty"`
	StageName            string   `protobuf:"bytes,2,opt,name=stage_name,json=stageName,proto3" json:"stage_name,omitempty"`
	SubStage             uint32   `protobuf:"varint,3,opt,name=sub_stage,json=subStage,proto3" json:"sub_stage,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingStageCfg) Reset()         { *m = WeddingStageCfg{} }
func (m *WeddingStageCfg) String() string { return proto.CompactTextString(m) }
func (*WeddingStageCfg) ProtoMessage()    {}
func (*WeddingStageCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{0}
}
func (m *WeddingStageCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingStageCfg.Unmarshal(m, b)
}
func (m *WeddingStageCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingStageCfg.Marshal(b, m, deterministic)
}
func (dst *WeddingStageCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingStageCfg.Merge(dst, src)
}
func (m *WeddingStageCfg) XXX_Size() int {
	return xxx_messageInfo_WeddingStageCfg.Size(m)
}
func (m *WeddingStageCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingStageCfg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingStageCfg proto.InternalMessageInfo

func (m *WeddingStageCfg) GetStage() uint32 {
	if m != nil {
		return m.Stage
	}
	return 0
}

func (m *WeddingStageCfg) GetStageName() string {
	if m != nil {
		return m.StageName
	}
	return ""
}

func (m *WeddingStageCfg) GetSubStage() uint32 {
	if m != nil {
		return m.SubStage
	}
	return 0
}

// 当前婚礼阶段信息
type WeddingStageInfo struct {
	StageCfgList         []*WeddingStageCfg `protobuf:"bytes,1,rep,name=stage_cfg_list,json=stageCfgList,proto3" json:"stage_cfg_list,omitempty"`
	CurrStage            uint32             `protobuf:"varint,2,opt,name=curr_stage,json=currStage,proto3" json:"curr_stage,omitempty"`
	SubStage             uint32             `protobuf:"varint,3,opt,name=sub_stage,json=subStage,proto3" json:"sub_stage,omitempty"`
	StageStartTs         int64              `protobuf:"varint,4,opt,name=stage_start_ts,json=stageStartTs,proto3" json:"stage_start_ts,omitempty"`
	StageEndTs           int64              `protobuf:"varint,5,opt,name=stage_end_ts,json=stageEndTs,proto3" json:"stage_end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *WeddingStageInfo) Reset()         { *m = WeddingStageInfo{} }
func (m *WeddingStageInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingStageInfo) ProtoMessage()    {}
func (*WeddingStageInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{1}
}
func (m *WeddingStageInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingStageInfo.Unmarshal(m, b)
}
func (m *WeddingStageInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingStageInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingStageInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingStageInfo.Merge(dst, src)
}
func (m *WeddingStageInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingStageInfo.Size(m)
}
func (m *WeddingStageInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingStageInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingStageInfo proto.InternalMessageInfo

func (m *WeddingStageInfo) GetStageCfgList() []*WeddingStageCfg {
	if m != nil {
		return m.StageCfgList
	}
	return nil
}

func (m *WeddingStageInfo) GetCurrStage() uint32 {
	if m != nil {
		return m.CurrStage
	}
	return 0
}

func (m *WeddingStageInfo) GetSubStage() uint32 {
	if m != nil {
		return m.SubStage
	}
	return 0
}

func (m *WeddingStageInfo) GetStageStartTs() int64 {
	if m != nil {
		return m.StageStartTs
	}
	return 0
}

func (m *WeddingStageInfo) GetStageEndTs() int64 {
	if m != nil {
		return m.StageEndTs
	}
	return 0
}

// [废弃]婚礼等级信息
type WeddingLevelInfo struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	GroomClothes         []uint32 `protobuf:"varint,2,rep,packed,name=groom_clothes,json=groomClothes,proto3" json:"groom_clothes,omitempty"`
	BrideClothes         []uint32 `protobuf:"varint,3,rep,packed,name=bride_clothes,json=brideClothes,proto3" json:"bride_clothes,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingLevelInfo) Reset()         { *m = WeddingLevelInfo{} }
func (m *WeddingLevelInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingLevelInfo) ProtoMessage()    {}
func (*WeddingLevelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{2}
}
func (m *WeddingLevelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingLevelInfo.Unmarshal(m, b)
}
func (m *WeddingLevelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingLevelInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingLevelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingLevelInfo.Merge(dst, src)
}
func (m *WeddingLevelInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingLevelInfo.Size(m)
}
func (m *WeddingLevelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingLevelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingLevelInfo proto.InternalMessageInfo

func (m *WeddingLevelInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *WeddingLevelInfo) GetGroomClothes() []uint32 {
	if m != nil {
		return m.GroomClothes
	}
	return nil
}

func (m *WeddingLevelInfo) GetBrideClothes() []uint32 {
	if m != nil {
		return m.BrideClothes
	}
	return nil
}

type WeddingSceneBoneCfg struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	SeqIndex             uint32   `protobuf:"varint,2,opt,name=seq_index,json=seqIndex,proto3" json:"seq_index,omitempty"`
	AnimationName        string   `protobuf:"bytes,3,opt,name=animation_name,json=animationName,proto3" json:"animation_name,omitempty"`
	BoneId               uint32   `protobuf:"varint,4,opt,name=bone_id,json=boneId,proto3" json:"bone_id,omitempty"`
	BaseBoneId           uint32   `protobuf:"varint,5,opt,name=base_bone_id,json=baseBoneId,proto3" json:"base_bone_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingSceneBoneCfg) Reset()         { *m = WeddingSceneBoneCfg{} }
func (m *WeddingSceneBoneCfg) String() string { return proto.CompactTextString(m) }
func (*WeddingSceneBoneCfg) ProtoMessage()    {}
func (*WeddingSceneBoneCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{3}
}
func (m *WeddingSceneBoneCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingSceneBoneCfg.Unmarshal(m, b)
}
func (m *WeddingSceneBoneCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingSceneBoneCfg.Marshal(b, m, deterministic)
}
func (dst *WeddingSceneBoneCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingSceneBoneCfg.Merge(dst, src)
}
func (m *WeddingSceneBoneCfg) XXX_Size() int {
	return xxx_messageInfo_WeddingSceneBoneCfg.Size(m)
}
func (m *WeddingSceneBoneCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingSceneBoneCfg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingSceneBoneCfg proto.InternalMessageInfo

func (m *WeddingSceneBoneCfg) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *WeddingSceneBoneCfg) GetSeqIndex() uint32 {
	if m != nil {
		return m.SeqIndex
	}
	return 0
}

func (m *WeddingSceneBoneCfg) GetAnimationName() string {
	if m != nil {
		return m.AnimationName
	}
	return ""
}

func (m *WeddingSceneBoneCfg) GetBoneId() uint32 {
	if m != nil {
		return m.BoneId
	}
	return 0
}

func (m *WeddingSceneBoneCfg) GetBaseBoneId() uint32 {
	if m != nil {
		return m.BaseBoneId
	}
	return 0
}

type WeddingSceneCfg struct {
	Scene                uint32                 `protobuf:"varint,1,opt,name=scene,proto3" json:"scene,omitempty"`
	SceneResource        string                 `protobuf:"bytes,2,opt,name=scene_resource,json=sceneResource,proto3" json:"scene_resource,omitempty"`
	SceneResourceMd5     string                 `protobuf:"bytes,3,opt,name=scene_resource_md5,json=sceneResourceMd5,proto3" json:"scene_resource_md5,omitempty"`
	BoneCfgList          []*WeddingSceneBoneCfg `protobuf:"bytes,4,rep,name=bone_cfg_list,json=boneCfgList,proto3" json:"bone_cfg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *WeddingSceneCfg) Reset()         { *m = WeddingSceneCfg{} }
func (m *WeddingSceneCfg) String() string { return proto.CompactTextString(m) }
func (*WeddingSceneCfg) ProtoMessage()    {}
func (*WeddingSceneCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{4}
}
func (m *WeddingSceneCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingSceneCfg.Unmarshal(m, b)
}
func (m *WeddingSceneCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingSceneCfg.Marshal(b, m, deterministic)
}
func (dst *WeddingSceneCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingSceneCfg.Merge(dst, src)
}
func (m *WeddingSceneCfg) XXX_Size() int {
	return xxx_messageInfo_WeddingSceneCfg.Size(m)
}
func (m *WeddingSceneCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingSceneCfg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingSceneCfg proto.InternalMessageInfo

func (m *WeddingSceneCfg) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *WeddingSceneCfg) GetSceneResource() string {
	if m != nil {
		return m.SceneResource
	}
	return ""
}

func (m *WeddingSceneCfg) GetSceneResourceMd5() string {
	if m != nil {
		return m.SceneResourceMd5
	}
	return ""
}

func (m *WeddingSceneCfg) GetBoneCfgList() []*WeddingSceneBoneCfg {
	if m != nil {
		return m.BoneCfgList
	}
	return nil
}

type WeddingLevelClothes struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	GroomClothes         []uint32 `protobuf:"varint,2,rep,packed,name=groom_clothes,json=groomClothes,proto3" json:"groom_clothes,omitempty"`
	BrideClothes         []uint32 `protobuf:"varint,3,rep,packed,name=bride_clothes,json=brideClothes,proto3" json:"bride_clothes,omitempty"`
	GroomsmanClothes     []uint32 `protobuf:"varint,4,rep,packed,name=groomsman_clothes,json=groomsmanClothes,proto3" json:"groomsman_clothes,omitempty"`
	BridesmaidClothes    []uint32 `protobuf:"varint,5,rep,packed,name=bridesmaid_clothes,json=bridesmaidClothes,proto3" json:"bridesmaid_clothes,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingLevelClothes) Reset()         { *m = WeddingLevelClothes{} }
func (m *WeddingLevelClothes) String() string { return proto.CompactTextString(m) }
func (*WeddingLevelClothes) ProtoMessage()    {}
func (*WeddingLevelClothes) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{5}
}
func (m *WeddingLevelClothes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingLevelClothes.Unmarshal(m, b)
}
func (m *WeddingLevelClothes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingLevelClothes.Marshal(b, m, deterministic)
}
func (dst *WeddingLevelClothes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingLevelClothes.Merge(dst, src)
}
func (m *WeddingLevelClothes) XXX_Size() int {
	return xxx_messageInfo_WeddingLevelClothes.Size(m)
}
func (m *WeddingLevelClothes) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingLevelClothes.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingLevelClothes proto.InternalMessageInfo

func (m *WeddingLevelClothes) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *WeddingLevelClothes) GetGroomClothes() []uint32 {
	if m != nil {
		return m.GroomClothes
	}
	return nil
}

func (m *WeddingLevelClothes) GetBrideClothes() []uint32 {
	if m != nil {
		return m.BrideClothes
	}
	return nil
}

func (m *WeddingLevelClothes) GetGroomsmanClothes() []uint32 {
	if m != nil {
		return m.GroomsmanClothes
	}
	return nil
}

func (m *WeddingLevelClothes) GetBridesmaidClothes() []uint32 {
	if m != nil {
		return m.BridesmaidClothes
	}
	return nil
}

// 婚礼房等级背景配置
type WeddingLevelBackgroundCfg struct {
	Level                    uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	BackgroundPicture        string   `protobuf:"bytes,2,opt,name=background_picture,json=backgroundPicture,proto3" json:"background_picture,omitempty"`
	BackgroundMp4Url         string   `protobuf:"bytes,3,opt,name=background_mp4_url,json=backgroundMp4Url,proto3" json:"background_mp4_url,omitempty"`
	SpecialBackgroundPicture string   `protobuf:"bytes,4,opt,name=special_background_picture,json=specialBackgroundPicture,proto3" json:"special_background_picture,omitempty"`
	SpecialBackgroundMp4Url  string   `protobuf:"bytes,5,opt,name=special_background_mp4_url,json=specialBackgroundMp4Url,proto3" json:"special_background_mp4_url,omitempty"`
	XXX_NoUnkeyedLiteral     struct{} `json:"-"`
	XXX_unrecognized         []byte   `json:"-"`
	XXX_sizecache            int32    `json:"-"`
}

func (m *WeddingLevelBackgroundCfg) Reset()         { *m = WeddingLevelBackgroundCfg{} }
func (m *WeddingLevelBackgroundCfg) String() string { return proto.CompactTextString(m) }
func (*WeddingLevelBackgroundCfg) ProtoMessage()    {}
func (*WeddingLevelBackgroundCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{6}
}
func (m *WeddingLevelBackgroundCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingLevelBackgroundCfg.Unmarshal(m, b)
}
func (m *WeddingLevelBackgroundCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingLevelBackgroundCfg.Marshal(b, m, deterministic)
}
func (dst *WeddingLevelBackgroundCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingLevelBackgroundCfg.Merge(dst, src)
}
func (m *WeddingLevelBackgroundCfg) XXX_Size() int {
	return xxx_messageInfo_WeddingLevelBackgroundCfg.Size(m)
}
func (m *WeddingLevelBackgroundCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingLevelBackgroundCfg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingLevelBackgroundCfg proto.InternalMessageInfo

func (m *WeddingLevelBackgroundCfg) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *WeddingLevelBackgroundCfg) GetBackgroundPicture() string {
	if m != nil {
		return m.BackgroundPicture
	}
	return ""
}

func (m *WeddingLevelBackgroundCfg) GetBackgroundMp4Url() string {
	if m != nil {
		return m.BackgroundMp4Url
	}
	return ""
}

func (m *WeddingLevelBackgroundCfg) GetSpecialBackgroundPicture() string {
	if m != nil {
		return m.SpecialBackgroundPicture
	}
	return ""
}

func (m *WeddingLevelBackgroundCfg) GetSpecialBackgroundMp4Url() string {
	if m != nil {
		return m.SpecialBackgroundMp4Url
	}
	return ""
}

type WeddingResource struct {
	ResourceUrl          string   `protobuf:"bytes,1,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	ResourceMd5          string   `protobuf:"bytes,2,opt,name=resource_md5,json=resourceMd5,proto3" json:"resource_md5,omitempty"`
	CpBoneId             uint32   `protobuf:"varint,3,opt,name=cp_bone_id,json=cpBoneId,proto3" json:"cp_bone_id,omitempty"`
	ItemIds              []uint32 `protobuf:"varint,4,rep,packed,name=item_ids,json=itemIds,proto3" json:"item_ids,omitempty"`
	BaseCpBoneId         uint32   `protobuf:"varint,5,opt,name=base_cp_bone_id,json=baseCpBoneId,proto3" json:"base_cp_bone_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingResource) Reset()         { *m = WeddingResource{} }
func (m *WeddingResource) String() string { return proto.CompactTextString(m) }
func (*WeddingResource) ProtoMessage()    {}
func (*WeddingResource) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{7}
}
func (m *WeddingResource) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingResource.Unmarshal(m, b)
}
func (m *WeddingResource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingResource.Marshal(b, m, deterministic)
}
func (dst *WeddingResource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingResource.Merge(dst, src)
}
func (m *WeddingResource) XXX_Size() int {
	return xxx_messageInfo_WeddingResource.Size(m)
}
func (m *WeddingResource) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingResource.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingResource proto.InternalMessageInfo

func (m *WeddingResource) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *WeddingResource) GetResourceMd5() string {
	if m != nil {
		return m.ResourceMd5
	}
	return ""
}

func (m *WeddingResource) GetCpBoneId() uint32 {
	if m != nil {
		return m.CpBoneId
	}
	return 0
}

func (m *WeddingResource) GetItemIds() []uint32 {
	if m != nil {
		return m.ItemIds
	}
	return nil
}

func (m *WeddingResource) GetBaseCpBoneId() uint32 {
	if m != nil {
		return m.BaseCpBoneId
	}
	return 0
}

// 婚礼房主题配置
type WeddingRoomThemeCfg struct {
	ThemeId                uint32                       `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	ThemeResource          string                       `protobuf:"bytes,2,opt,name=theme_resource,json=themeResource,proto3" json:"theme_resource,omitempty"`
	ThemeResourceMd5       string                       `protobuf:"bytes,3,opt,name=theme_resource_md5,json=themeResourceMd5,proto3" json:"theme_resource_md5,omitempty"`
	SceneCfgList           []*WeddingSceneCfg           `protobuf:"bytes,4,rep,name=scene_cfg_list,json=sceneCfgList,proto3" json:"scene_cfg_list,omitempty"`
	ChairGameResource      string                       `protobuf:"bytes,5,opt,name=chair_game_resource,json=chairGameResource,proto3" json:"chair_game_resource,omitempty"`
	ChairGameResourceMd5   string                       `protobuf:"bytes,6,opt,name=chair_game_resource_md5,json=chairGameResourceMd5,proto3" json:"chair_game_resource_md5,omitempty"`
	LevelClothesList       []*WeddingLevelClothes       `protobuf:"bytes,7,rep,name=level_clothes_list,json=levelClothesList,proto3" json:"level_clothes_list,omitempty"`
	LevelBackgroundList    []*WeddingLevelBackgroundCfg `protobuf:"bytes,8,rep,name=level_background_list,json=levelBackgroundList,proto3" json:"level_background_list,omitempty"`
	WeddingPreviewResource *WeddingResource             `protobuf:"bytes,9,opt,name=wedding_preview_resource,json=weddingPreviewResource,proto3" json:"wedding_preview_resource,omitempty"`
	ChairResourceId        uint32                       `protobuf:"varint,10,opt,name=chair_resource_id,json=chairResourceId,proto3" json:"chair_resource_id,omitempty"`
	IsFreeTheme            bool                         `protobuf:"varint,11,opt,name=is_free_theme,json=isFreeTheme,proto3" json:"is_free_theme,omitempty"`
	ChairResCfg            *ChairGameResourceCfg        `protobuf:"bytes,12,opt,name=chair_res_cfg,json=chairResCfg,proto3" json:"chair_res_cfg,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                     `json:"-"`
	XXX_unrecognized       []byte                       `json:"-"`
	XXX_sizecache          int32                        `json:"-"`
}

func (m *WeddingRoomThemeCfg) Reset()         { *m = WeddingRoomThemeCfg{} }
func (m *WeddingRoomThemeCfg) String() string { return proto.CompactTextString(m) }
func (*WeddingRoomThemeCfg) ProtoMessage()    {}
func (*WeddingRoomThemeCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{8}
}
func (m *WeddingRoomThemeCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingRoomThemeCfg.Unmarshal(m, b)
}
func (m *WeddingRoomThemeCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingRoomThemeCfg.Marshal(b, m, deterministic)
}
func (dst *WeddingRoomThemeCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingRoomThemeCfg.Merge(dst, src)
}
func (m *WeddingRoomThemeCfg) XXX_Size() int {
	return xxx_messageInfo_WeddingRoomThemeCfg.Size(m)
}
func (m *WeddingRoomThemeCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingRoomThemeCfg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingRoomThemeCfg proto.InternalMessageInfo

func (m *WeddingRoomThemeCfg) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *WeddingRoomThemeCfg) GetThemeResource() string {
	if m != nil {
		return m.ThemeResource
	}
	return ""
}

func (m *WeddingRoomThemeCfg) GetThemeResourceMd5() string {
	if m != nil {
		return m.ThemeResourceMd5
	}
	return ""
}

func (m *WeddingRoomThemeCfg) GetSceneCfgList() []*WeddingSceneCfg {
	if m != nil {
		return m.SceneCfgList
	}
	return nil
}

func (m *WeddingRoomThemeCfg) GetChairGameResource() string {
	if m != nil {
		return m.ChairGameResource
	}
	return ""
}

func (m *WeddingRoomThemeCfg) GetChairGameResourceMd5() string {
	if m != nil {
		return m.ChairGameResourceMd5
	}
	return ""
}

func (m *WeddingRoomThemeCfg) GetLevelClothesList() []*WeddingLevelClothes {
	if m != nil {
		return m.LevelClothesList
	}
	return nil
}

func (m *WeddingRoomThemeCfg) GetLevelBackgroundList() []*WeddingLevelBackgroundCfg {
	if m != nil {
		return m.LevelBackgroundList
	}
	return nil
}

func (m *WeddingRoomThemeCfg) GetWeddingPreviewResource() *WeddingResource {
	if m != nil {
		return m.WeddingPreviewResource
	}
	return nil
}

func (m *WeddingRoomThemeCfg) GetChairResourceId() uint32 {
	if m != nil {
		return m.ChairResourceId
	}
	return 0
}

func (m *WeddingRoomThemeCfg) GetIsFreeTheme() bool {
	if m != nil {
		return m.IsFreeTheme
	}
	return false
}

func (m *WeddingRoomThemeCfg) GetChairResCfg() *ChairGameResourceCfg {
	if m != nil {
		return m.ChairResCfg
	}
	return nil
}

// 抢椅子 椅子资源配置
type ChairGameResourceCfg struct {
	ChairPic             string   `protobuf:"bytes,1,opt,name=chair_pic,json=chairPic,proto3" json:"chair_pic,omitempty"`
	SittingPoseFemaleId  uint32   `protobuf:"varint,2,opt,name=sitting_pose_female_id,json=sittingPoseFemaleId,proto3" json:"sitting_pose_female_id,omitempty"`
	SittingPoseMaleId    uint32   `protobuf:"varint,3,opt,name=sitting_pose_male_id,json=sittingPoseMaleId,proto3" json:"sitting_pose_male_id,omitempty"`
	StandbyFemaleId      uint32   `protobuf:"varint,4,opt,name=standby_female_id,json=standbyFemaleId,proto3" json:"standby_female_id,omitempty"`
	StandbyMaleId        uint32   `protobuf:"varint,5,opt,name=standby_male_id,json=standbyMaleId,proto3" json:"standby_male_id,omitempty"`
	FailFemaleIds        []uint32 `protobuf:"varint,6,rep,packed,name=fail_female_ids,json=failFemaleIds,proto3" json:"fail_female_ids,omitempty"`
	FailMaleIds          []uint32 `protobuf:"varint,7,rep,packed,name=fail_male_ids,json=failMaleIds,proto3" json:"fail_male_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChairGameResourceCfg) Reset()         { *m = ChairGameResourceCfg{} }
func (m *ChairGameResourceCfg) String() string { return proto.CompactTextString(m) }
func (*ChairGameResourceCfg) ProtoMessage()    {}
func (*ChairGameResourceCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{9}
}
func (m *ChairGameResourceCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChairGameResourceCfg.Unmarshal(m, b)
}
func (m *ChairGameResourceCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChairGameResourceCfg.Marshal(b, m, deterministic)
}
func (dst *ChairGameResourceCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChairGameResourceCfg.Merge(dst, src)
}
func (m *ChairGameResourceCfg) XXX_Size() int {
	return xxx_messageInfo_ChairGameResourceCfg.Size(m)
}
func (m *ChairGameResourceCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_ChairGameResourceCfg.DiscardUnknown(m)
}

var xxx_messageInfo_ChairGameResourceCfg proto.InternalMessageInfo

func (m *ChairGameResourceCfg) GetChairPic() string {
	if m != nil {
		return m.ChairPic
	}
	return ""
}

func (m *ChairGameResourceCfg) GetSittingPoseFemaleId() uint32 {
	if m != nil {
		return m.SittingPoseFemaleId
	}
	return 0
}

func (m *ChairGameResourceCfg) GetSittingPoseMaleId() uint32 {
	if m != nil {
		return m.SittingPoseMaleId
	}
	return 0
}

func (m *ChairGameResourceCfg) GetStandbyFemaleId() uint32 {
	if m != nil {
		return m.StandbyFemaleId
	}
	return 0
}

func (m *ChairGameResourceCfg) GetStandbyMaleId() uint32 {
	if m != nil {
		return m.StandbyMaleId
	}
	return 0
}

func (m *ChairGameResourceCfg) GetFailFemaleIds() []uint32 {
	if m != nil {
		return m.FailFemaleIds
	}
	return nil
}

func (m *ChairGameResourceCfg) GetFailMaleIds() []uint32 {
	if m != nil {
		return m.FailMaleIds
	}
	return nil
}

// 婚礼新人信息
type WeddingCpMemInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingCpMemInfo) Reset()         { *m = WeddingCpMemInfo{} }
func (m *WeddingCpMemInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingCpMemInfo) ProtoMessage()    {}
func (*WeddingCpMemInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{10}
}
func (m *WeddingCpMemInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingCpMemInfo.Unmarshal(m, b)
}
func (m *WeddingCpMemInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingCpMemInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingCpMemInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingCpMemInfo.Merge(dst, src)
}
func (m *WeddingCpMemInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingCpMemInfo.Size(m)
}
func (m *WeddingCpMemInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingCpMemInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingCpMemInfo proto.InternalMessageInfo

func (m *WeddingCpMemInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 新人纪念视频
type WeddingMemorialVideo struct {
	ResourceUrl          string   `protobuf:"bytes,1,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	ResourceMd5          string   `protobuf:"bytes,2,opt,name=resource_md5,json=resourceMd5,proto3" json:"resource_md5,omitempty"`
	ResourceJson         string   `protobuf:"bytes,3,opt,name=resource_json,json=resourceJson,proto3" json:"resource_json,omitempty"`
	UserPictures         []string `protobuf:"bytes,4,rep,name=user_pictures,json=userPictures,proto3" json:"user_pictures,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingMemorialVideo) Reset()         { *m = WeddingMemorialVideo{} }
func (m *WeddingMemorialVideo) String() string { return proto.CompactTextString(m) }
func (*WeddingMemorialVideo) ProtoMessage()    {}
func (*WeddingMemorialVideo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{11}
}
func (m *WeddingMemorialVideo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingMemorialVideo.Unmarshal(m, b)
}
func (m *WeddingMemorialVideo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingMemorialVideo.Marshal(b, m, deterministic)
}
func (dst *WeddingMemorialVideo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingMemorialVideo.Merge(dst, src)
}
func (m *WeddingMemorialVideo) XXX_Size() int {
	return xxx_messageInfo_WeddingMemorialVideo.Size(m)
}
func (m *WeddingMemorialVideo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingMemorialVideo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingMemorialVideo proto.InternalMessageInfo

func (m *WeddingMemorialVideo) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *WeddingMemorialVideo) GetResourceMd5() string {
	if m != nil {
		return m.ResourceMd5
	}
	return ""
}

func (m *WeddingMemorialVideo) GetResourceJson() string {
	if m != nil {
		return m.ResourceJson
	}
	return ""
}

func (m *WeddingMemorialVideo) GetUserPictures() []string {
	if m != nil {
		return m.UserPictures
	}
	return nil
}

// 骨骼配置
type WeddingBoneCfg struct {
	MaleBoneId           uint32   `protobuf:"varint,1,opt,name=male_bone_id,json=maleBoneId,proto3" json:"male_bone_id,omitempty"`
	FemaleBoneId         uint32   `protobuf:"varint,2,opt,name=female_bone_id,json=femaleBoneId,proto3" json:"female_bone_id,omitempty"`
	BaseMaleBoneId       uint32   `protobuf:"varint,3,opt,name=base_male_bone_id,json=baseMaleBoneId,proto3" json:"base_male_bone_id,omitempty"`
	BaseFemaleBoneId     uint32   `protobuf:"varint,4,opt,name=base_female_bone_id,json=baseFemaleBoneId,proto3" json:"base_female_bone_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingBoneCfg) Reset()         { *m = WeddingBoneCfg{} }
func (m *WeddingBoneCfg) String() string { return proto.CompactTextString(m) }
func (*WeddingBoneCfg) ProtoMessage()    {}
func (*WeddingBoneCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{12}
}
func (m *WeddingBoneCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingBoneCfg.Unmarshal(m, b)
}
func (m *WeddingBoneCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingBoneCfg.Marshal(b, m, deterministic)
}
func (dst *WeddingBoneCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingBoneCfg.Merge(dst, src)
}
func (m *WeddingBoneCfg) XXX_Size() int {
	return xxx_messageInfo_WeddingBoneCfg.Size(m)
}
func (m *WeddingBoneCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingBoneCfg.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingBoneCfg proto.InternalMessageInfo

func (m *WeddingBoneCfg) GetMaleBoneId() uint32 {
	if m != nil {
		return m.MaleBoneId
	}
	return 0
}

func (m *WeddingBoneCfg) GetFemaleBoneId() uint32 {
	if m != nil {
		return m.FemaleBoneId
	}
	return 0
}

func (m *WeddingBoneCfg) GetBaseMaleBoneId() uint32 {
	if m != nil {
		return m.BaseMaleBoneId
	}
	return 0
}

func (m *WeddingBoneCfg) GetBaseFemaleBoneId() uint32 {
	if m != nil {
		return m.BaseFemaleBoneId
	}
	return 0
}

// 幸福值等级配置信息
type HappinessLevelInfo struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	LevelValue           uint32   `protobuf:"varint,2,opt,name=level_value,json=levelValue,proto3" json:"level_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HappinessLevelInfo) Reset()         { *m = HappinessLevelInfo{} }
func (m *HappinessLevelInfo) String() string { return proto.CompactTextString(m) }
func (*HappinessLevelInfo) ProtoMessage()    {}
func (*HappinessLevelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{13}
}
func (m *HappinessLevelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HappinessLevelInfo.Unmarshal(m, b)
}
func (m *HappinessLevelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HappinessLevelInfo.Marshal(b, m, deterministic)
}
func (dst *HappinessLevelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HappinessLevelInfo.Merge(dst, src)
}
func (m *HappinessLevelInfo) XXX_Size() int {
	return xxx_messageInfo_HappinessLevelInfo.Size(m)
}
func (m *HappinessLevelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_HappinessLevelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_HappinessLevelInfo proto.InternalMessageInfo

func (m *HappinessLevelInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *HappinessLevelInfo) GetLevelValue() uint32 {
	if m != nil {
		return m.LevelValue
	}
	return 0
}

// 幸福值配置信息
type HappinessConfigInfo struct {
	Config               []*HappinessLevelInfo `protobuf:"bytes,1,rep,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *HappinessConfigInfo) Reset()         { *m = HappinessConfigInfo{} }
func (m *HappinessConfigInfo) String() string { return proto.CompactTextString(m) }
func (*HappinessConfigInfo) ProtoMessage()    {}
func (*HappinessConfigInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{14}
}
func (m *HappinessConfigInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HappinessConfigInfo.Unmarshal(m, b)
}
func (m *HappinessConfigInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HappinessConfigInfo.Marshal(b, m, deterministic)
}
func (dst *HappinessConfigInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HappinessConfigInfo.Merge(dst, src)
}
func (m *HappinessConfigInfo) XXX_Size() int {
	return xxx_messageInfo_HappinessConfigInfo.Size(m)
}
func (m *HappinessConfigInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_HappinessConfigInfo.DiscardUnknown(m)
}

var xxx_messageInfo_HappinessConfigInfo proto.InternalMessageInfo

func (m *HappinessConfigInfo) GetConfig() []*HappinessLevelInfo {
	if m != nil {
		return m.Config
	}
	return nil
}

// 婚礼信息
type WeddingInfo struct {
	Cid                  uint32                `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	WeddingId            int64                 `protobuf:"varint,2,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	StageInfo            *WeddingStageInfo     `protobuf:"bytes,3,opt,name=stage_info,json=stageInfo,proto3" json:"stage_info,omitempty"`
	ThemeCfg             *WeddingRoomThemeCfg  `protobuf:"bytes,4,opt,name=theme_cfg,json=themeCfg,proto3" json:"theme_cfg,omitempty"`
	Bride                *WeddingCpMemInfo     `protobuf:"bytes,5,opt,name=bride,proto3" json:"bride,omitempty"`
	Groom                *WeddingCpMemInfo     `protobuf:"bytes,6,opt,name=groom,proto3" json:"groom,omitempty"`
	StartTime            int64                 `protobuf:"varint,7,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64                 `protobuf:"varint,8,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	WeddingMemorialVideo *WeddingMemorialVideo `protobuf:"bytes,9,opt,name=wedding_memorial_video,json=weddingMemorialVideo,proto3" json:"wedding_memorial_video,omitempty"`
	ChairGameEntry       bool                  `protobuf:"varint,10,opt,name=chair_game_entry,json=chairGameEntry,proto3" json:"chair_game_entry,omitempty"`
	BridesmaidManList    []uint32              `protobuf:"varint,11,rep,packed,name=bridesmaid_man_list,json=bridesmaidManList,proto3" json:"bridesmaid_man_list,omitempty"`
	CurrLevel            uint32                `protobuf:"varint,12,opt,name=curr_level,json=currLevel,proto3" json:"curr_level,omitempty"`
	HappinessConfig      *HappinessConfigInfo  `protobuf:"bytes,13,opt,name=happiness_config,json=happinessConfig,proto3" json:"happiness_config,omitempty"`
	CurrHappinessValue   uint32                `protobuf:"varint,14,opt,name=curr_happiness_value,json=currHappinessValue,proto3" json:"curr_happiness_value,omitempty"`
	BoneCfg              *WeddingBoneCfg       `protobuf:"bytes,15,opt,name=bone_cfg,json=boneCfg,proto3" json:"bone_cfg,omitempty"`
	ReserveTime          int64                 `protobuf:"varint,16,opt,name=reserve_time,json=reserveTime,proto3" json:"reserve_time,omitempty"`
	PlanId               uint32                `protobuf:"varint,17,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *WeddingInfo) Reset()         { *m = WeddingInfo{} }
func (m *WeddingInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingInfo) ProtoMessage()    {}
func (*WeddingInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{15}
}
func (m *WeddingInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingInfo.Unmarshal(m, b)
}
func (m *WeddingInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingInfo.Merge(dst, src)
}
func (m *WeddingInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingInfo.Size(m)
}
func (m *WeddingInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingInfo proto.InternalMessageInfo

func (m *WeddingInfo) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *WeddingInfo) GetWeddingId() int64 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

func (m *WeddingInfo) GetStageInfo() *WeddingStageInfo {
	if m != nil {
		return m.StageInfo
	}
	return nil
}

func (m *WeddingInfo) GetThemeCfg() *WeddingRoomThemeCfg {
	if m != nil {
		return m.ThemeCfg
	}
	return nil
}

func (m *WeddingInfo) GetBride() *WeddingCpMemInfo {
	if m != nil {
		return m.Bride
	}
	return nil
}

func (m *WeddingInfo) GetGroom() *WeddingCpMemInfo {
	if m != nil {
		return m.Groom
	}
	return nil
}

func (m *WeddingInfo) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *WeddingInfo) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *WeddingInfo) GetWeddingMemorialVideo() *WeddingMemorialVideo {
	if m != nil {
		return m.WeddingMemorialVideo
	}
	return nil
}

func (m *WeddingInfo) GetChairGameEntry() bool {
	if m != nil {
		return m.ChairGameEntry
	}
	return false
}

func (m *WeddingInfo) GetBridesmaidManList() []uint32 {
	if m != nil {
		return m.BridesmaidManList
	}
	return nil
}

func (m *WeddingInfo) GetCurrLevel() uint32 {
	if m != nil {
		return m.CurrLevel
	}
	return 0
}

func (m *WeddingInfo) GetHappinessConfig() *HappinessConfigInfo {
	if m != nil {
		return m.HappinessConfig
	}
	return nil
}

func (m *WeddingInfo) GetCurrHappinessValue() uint32 {
	if m != nil {
		return m.CurrHappinessValue
	}
	return 0
}

func (m *WeddingInfo) GetBoneCfg() *WeddingBoneCfg {
	if m != nil {
		return m.BoneCfg
	}
	return nil
}

func (m *WeddingInfo) GetReserveTime() int64 {
	if m != nil {
		return m.ReserveTime
	}
	return 0
}

func (m *WeddingInfo) GetPlanId() uint32 {
	if m != nil {
		return m.PlanId
	}
	return 0
}

// 获取房间婚礼信息请求
type GetChannelWeddingInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelWeddingInfoReq) Reset()         { *m = GetChannelWeddingInfoReq{} }
func (m *GetChannelWeddingInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelWeddingInfoReq) ProtoMessage()    {}
func (*GetChannelWeddingInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{16}
}
func (m *GetChannelWeddingInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelWeddingInfoReq.Unmarshal(m, b)
}
func (m *GetChannelWeddingInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelWeddingInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelWeddingInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelWeddingInfoReq.Merge(dst, src)
}
func (m *GetChannelWeddingInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelWeddingInfoReq.Size(m)
}
func (m *GetChannelWeddingInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelWeddingInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelWeddingInfoReq proto.InternalMessageInfo

func (m *GetChannelWeddingInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelWeddingInfoReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

// 获取房间婚礼信息响应
type GetChannelWeddingInfoResp struct {
	WeddingInfo          *WeddingInfo             `protobuf:"bytes,1,opt,name=wedding_info,json=weddingInfo,proto3" json:"wedding_info,omitempty"`
	PresentCountInfo     *WeddingPresentCountInfo `protobuf:"bytes,2,opt,name=present_count_info,json=presentCountInfo,proto3" json:"present_count_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetChannelWeddingInfoResp) Reset()         { *m = GetChannelWeddingInfoResp{} }
func (m *GetChannelWeddingInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelWeddingInfoResp) ProtoMessage()    {}
func (*GetChannelWeddingInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{17}
}
func (m *GetChannelWeddingInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelWeddingInfoResp.Unmarshal(m, b)
}
func (m *GetChannelWeddingInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelWeddingInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelWeddingInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelWeddingInfoResp.Merge(dst, src)
}
func (m *GetChannelWeddingInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelWeddingInfoResp.Size(m)
}
func (m *GetChannelWeddingInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelWeddingInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelWeddingInfoResp proto.InternalMessageInfo

func (m *GetChannelWeddingInfoResp) GetWeddingInfo() *WeddingInfo {
	if m != nil {
		return m.WeddingInfo
	}
	return nil
}

func (m *GetChannelWeddingInfoResp) GetPresentCountInfo() *WeddingPresentCountInfo {
	if m != nil {
		return m.PresentCountInfo
	}
	return nil
}

// 获取新人正在进行的婚礼信息请求
type GetUserChannelWeddingInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserChannelWeddingInfoReq) Reset()         { *m = GetUserChannelWeddingInfoReq{} }
func (m *GetUserChannelWeddingInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserChannelWeddingInfoReq) ProtoMessage()    {}
func (*GetUserChannelWeddingInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{18}
}
func (m *GetUserChannelWeddingInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserChannelWeddingInfoReq.Unmarshal(m, b)
}
func (m *GetUserChannelWeddingInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserChannelWeddingInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserChannelWeddingInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserChannelWeddingInfoReq.Merge(dst, src)
}
func (m *GetUserChannelWeddingInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserChannelWeddingInfoReq.Size(m)
}
func (m *GetUserChannelWeddingInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserChannelWeddingInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserChannelWeddingInfoReq proto.InternalMessageInfo

func (m *GetUserChannelWeddingInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 获取新人正在进行的婚礼信息响应
type GetUserChannelWeddingInfoResp struct {
	WeddingInfo          *WeddingInfo `protobuf:"bytes,1,opt,name=wedding_info,json=weddingInfo,proto3" json:"wedding_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserChannelWeddingInfoResp) Reset()         { *m = GetUserChannelWeddingInfoResp{} }
func (m *GetUserChannelWeddingInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserChannelWeddingInfoResp) ProtoMessage()    {}
func (*GetUserChannelWeddingInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{19}
}
func (m *GetUserChannelWeddingInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserChannelWeddingInfoResp.Unmarshal(m, b)
}
func (m *GetUserChannelWeddingInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserChannelWeddingInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserChannelWeddingInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserChannelWeddingInfoResp.Merge(dst, src)
}
func (m *GetUserChannelWeddingInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserChannelWeddingInfoResp.Size(m)
}
func (m *GetUserChannelWeddingInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserChannelWeddingInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserChannelWeddingInfoResp proto.InternalMessageInfo

func (m *GetUserChannelWeddingInfoResp) GetWeddingInfo() *WeddingInfo {
	if m != nil {
		return m.WeddingInfo
	}
	return nil
}

type SimpleWeddingInfo struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	WeddingId            int64    `protobuf:"varint,2,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	StartTime            int64    `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64    `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	CurrStage            uint32   `protobuf:"varint,5,opt,name=curr_stage,json=currStage,proto3" json:"curr_stage,omitempty"`
	PlanId               uint32   `protobuf:"varint,6,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SimpleWeddingInfo) Reset()         { *m = SimpleWeddingInfo{} }
func (m *SimpleWeddingInfo) String() string { return proto.CompactTextString(m) }
func (*SimpleWeddingInfo) ProtoMessage()    {}
func (*SimpleWeddingInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{20}
}
func (m *SimpleWeddingInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleWeddingInfo.Unmarshal(m, b)
}
func (m *SimpleWeddingInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleWeddingInfo.Marshal(b, m, deterministic)
}
func (dst *SimpleWeddingInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleWeddingInfo.Merge(dst, src)
}
func (m *SimpleWeddingInfo) XXX_Size() int {
	return xxx_messageInfo_SimpleWeddingInfo.Size(m)
}
func (m *SimpleWeddingInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleWeddingInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleWeddingInfo proto.InternalMessageInfo

func (m *SimpleWeddingInfo) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SimpleWeddingInfo) GetWeddingId() int64 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

func (m *SimpleWeddingInfo) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *SimpleWeddingInfo) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *SimpleWeddingInfo) GetCurrStage() uint32 {
	if m != nil {
		return m.CurrStage
	}
	return 0
}

func (m *SimpleWeddingInfo) GetPlanId() uint32 {
	if m != nil {
		return m.PlanId
	}
	return 0
}

// 批量获取房间婚礼信息请求
type BatchGetChannelWeddingSimpleInfoReq struct {
	OpUid                uint32   `protobuf:"varint,1,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	CidList              []uint32 `protobuf:"varint,2,rep,packed,name=cid_list,json=cidList,proto3" json:"cid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelWeddingSimpleInfoReq) Reset()         { *m = BatchGetChannelWeddingSimpleInfoReq{} }
func (m *BatchGetChannelWeddingSimpleInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelWeddingSimpleInfoReq) ProtoMessage()    {}
func (*BatchGetChannelWeddingSimpleInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{21}
}
func (m *BatchGetChannelWeddingSimpleInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelWeddingSimpleInfoReq.Unmarshal(m, b)
}
func (m *BatchGetChannelWeddingSimpleInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelWeddingSimpleInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelWeddingSimpleInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelWeddingSimpleInfoReq.Merge(dst, src)
}
func (m *BatchGetChannelWeddingSimpleInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelWeddingSimpleInfoReq.Size(m)
}
func (m *BatchGetChannelWeddingSimpleInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelWeddingSimpleInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelWeddingSimpleInfoReq proto.InternalMessageInfo

func (m *BatchGetChannelWeddingSimpleInfoReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *BatchGetChannelWeddingSimpleInfoReq) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

// 批量获取房间婚礼信息响应
type BatchGetChannelWeddingSimpleInfoResp struct {
	WeddingInfoList      []*SimpleWeddingInfo `protobuf:"bytes,1,rep,name=wedding_info_list,json=weddingInfoList,proto3" json:"wedding_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatchGetChannelWeddingSimpleInfoResp) Reset()         { *m = BatchGetChannelWeddingSimpleInfoResp{} }
func (m *BatchGetChannelWeddingSimpleInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelWeddingSimpleInfoResp) ProtoMessage()    {}
func (*BatchGetChannelWeddingSimpleInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{22}
}
func (m *BatchGetChannelWeddingSimpleInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelWeddingSimpleInfoResp.Unmarshal(m, b)
}
func (m *BatchGetChannelWeddingSimpleInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelWeddingSimpleInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelWeddingSimpleInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelWeddingSimpleInfoResp.Merge(dst, src)
}
func (m *BatchGetChannelWeddingSimpleInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelWeddingSimpleInfoResp.Size(m)
}
func (m *BatchGetChannelWeddingSimpleInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelWeddingSimpleInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelWeddingSimpleInfoResp proto.InternalMessageInfo

func (m *BatchGetChannelWeddingSimpleInfoResp) GetWeddingInfoList() []*SimpleWeddingInfo {
	if m != nil {
		return m.WeddingInfoList
	}
	return nil
}

// 预约婚礼请求
type ReserveWeddingReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	StartTime            int64    `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	ThemeId              string   `protobuf:"bytes,4,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"` // Deprecated: Do not use.
	BrideUid             uint32   `protobuf:"varint,5,opt,name=bride_uid,json=brideUid,proto3" json:"bride_uid,omitempty"`
	GroomUid             uint32   `protobuf:"varint,6,opt,name=groom_uid,json=groomUid,proto3" json:"groom_uid,omitempty"`
	WeddingThemeId       uint32   `protobuf:"varint,7,opt,name=wedding_theme_id,json=weddingThemeId,proto3" json:"wedding_theme_id,omitempty"`
	EndTime              int64    `protobuf:"varint,8,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	PlanId               uint32   `protobuf:"varint,9,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`
	ThemeType            uint32   `protobuf:"varint,10,opt,name=theme_type,json=themeType,proto3" json:"theme_type,omitempty"`
	BridesmaidUidList    []uint32 `protobuf:"varint,11,rep,packed,name=bridesmaid_uid_list,json=bridesmaidUidList,proto3" json:"bridesmaid_uid_list,omitempty"`
	SuitExtraSendSec     uint32   `protobuf:"varint,12,opt,name=suit_extra_send_sec,json=suitExtraSendSec,proto3" json:"suit_extra_send_sec,omitempty"`
	InitHappinessValue   uint32   `protobuf:"varint,13,opt,name=init_happiness_value,json=initHappinessValue,proto3" json:"init_happiness_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReserveWeddingReq) Reset()         { *m = ReserveWeddingReq{} }
func (m *ReserveWeddingReq) String() string { return proto.CompactTextString(m) }
func (*ReserveWeddingReq) ProtoMessage()    {}
func (*ReserveWeddingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{23}
}
func (m *ReserveWeddingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReserveWeddingReq.Unmarshal(m, b)
}
func (m *ReserveWeddingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReserveWeddingReq.Marshal(b, m, deterministic)
}
func (dst *ReserveWeddingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReserveWeddingReq.Merge(dst, src)
}
func (m *ReserveWeddingReq) XXX_Size() int {
	return xxx_messageInfo_ReserveWeddingReq.Size(m)
}
func (m *ReserveWeddingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReserveWeddingReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReserveWeddingReq proto.InternalMessageInfo

func (m *ReserveWeddingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReserveWeddingReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *ReserveWeddingReq) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

// Deprecated: Do not use.
func (m *ReserveWeddingReq) GetThemeId() string {
	if m != nil {
		return m.ThemeId
	}
	return ""
}

func (m *ReserveWeddingReq) GetBrideUid() uint32 {
	if m != nil {
		return m.BrideUid
	}
	return 0
}

func (m *ReserveWeddingReq) GetGroomUid() uint32 {
	if m != nil {
		return m.GroomUid
	}
	return 0
}

func (m *ReserveWeddingReq) GetWeddingThemeId() uint32 {
	if m != nil {
		return m.WeddingThemeId
	}
	return 0
}

func (m *ReserveWeddingReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ReserveWeddingReq) GetPlanId() uint32 {
	if m != nil {
		return m.PlanId
	}
	return 0
}

func (m *ReserveWeddingReq) GetThemeType() uint32 {
	if m != nil {
		return m.ThemeType
	}
	return 0
}

func (m *ReserveWeddingReq) GetBridesmaidUidList() []uint32 {
	if m != nil {
		return m.BridesmaidUidList
	}
	return nil
}

func (m *ReserveWeddingReq) GetSuitExtraSendSec() uint32 {
	if m != nil {
		return m.SuitExtraSendSec
	}
	return 0
}

func (m *ReserveWeddingReq) GetInitHappinessValue() uint32 {
	if m != nil {
		return m.InitHappinessValue
	}
	return 0
}

// 预约婚礼响应
type ReserveWeddingResp struct {
	WeddingId            int64    `protobuf:"varint,1,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReserveWeddingResp) Reset()         { *m = ReserveWeddingResp{} }
func (m *ReserveWeddingResp) String() string { return proto.CompactTextString(m) }
func (*ReserveWeddingResp) ProtoMessage()    {}
func (*ReserveWeddingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{24}
}
func (m *ReserveWeddingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReserveWeddingResp.Unmarshal(m, b)
}
func (m *ReserveWeddingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReserveWeddingResp.Marshal(b, m, deterministic)
}
func (dst *ReserveWeddingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReserveWeddingResp.Merge(dst, src)
}
func (m *ReserveWeddingResp) XXX_Size() int {
	return xxx_messageInfo_ReserveWeddingResp.Size(m)
}
func (m *ReserveWeddingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReserveWeddingResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReserveWeddingResp proto.InternalMessageInfo

func (m *ReserveWeddingResp) GetWeddingId() int64 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

// 婚礼进行中上报新增伴郎伴娘请求
type ReportWeddingBridesmaidManReq struct {
	PlanId               uint32   `protobuf:"varint,1,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	BridesmaidUidList    []uint32 `protobuf:"varint,3,rep,packed,name=bridesmaid_uid_list,json=bridesmaidUidList,proto3" json:"bridesmaid_uid_list,omitempty"`
	BuyerUid             uint32   `protobuf:"varint,4,opt,name=buyer_uid,json=buyerUid,proto3" json:"buyer_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportWeddingBridesmaidManReq) Reset()         { *m = ReportWeddingBridesmaidManReq{} }
func (m *ReportWeddingBridesmaidManReq) String() string { return proto.CompactTextString(m) }
func (*ReportWeddingBridesmaidManReq) ProtoMessage()    {}
func (*ReportWeddingBridesmaidManReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{25}
}
func (m *ReportWeddingBridesmaidManReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportWeddingBridesmaidManReq.Unmarshal(m, b)
}
func (m *ReportWeddingBridesmaidManReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportWeddingBridesmaidManReq.Marshal(b, m, deterministic)
}
func (dst *ReportWeddingBridesmaidManReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportWeddingBridesmaidManReq.Merge(dst, src)
}
func (m *ReportWeddingBridesmaidManReq) XXX_Size() int {
	return xxx_messageInfo_ReportWeddingBridesmaidManReq.Size(m)
}
func (m *ReportWeddingBridesmaidManReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportWeddingBridesmaidManReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportWeddingBridesmaidManReq proto.InternalMessageInfo

func (m *ReportWeddingBridesmaidManReq) GetPlanId() uint32 {
	if m != nil {
		return m.PlanId
	}
	return 0
}

func (m *ReportWeddingBridesmaidManReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *ReportWeddingBridesmaidManReq) GetBridesmaidUidList() []uint32 {
	if m != nil {
		return m.BridesmaidUidList
	}
	return nil
}

func (m *ReportWeddingBridesmaidManReq) GetBuyerUid() uint32 {
	if m != nil {
		return m.BuyerUid
	}
	return 0
}

// 婚礼进行中新增伴郎伴娘响应
type ReportWeddingBridesmaidManResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportWeddingBridesmaidManResp) Reset()         { *m = ReportWeddingBridesmaidManResp{} }
func (m *ReportWeddingBridesmaidManResp) String() string { return proto.CompactTextString(m) }
func (*ReportWeddingBridesmaidManResp) ProtoMessage()    {}
func (*ReportWeddingBridesmaidManResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{26}
}
func (m *ReportWeddingBridesmaidManResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportWeddingBridesmaidManResp.Unmarshal(m, b)
}
func (m *ReportWeddingBridesmaidManResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportWeddingBridesmaidManResp.Marshal(b, m, deterministic)
}
func (dst *ReportWeddingBridesmaidManResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportWeddingBridesmaidManResp.Merge(dst, src)
}
func (m *ReportWeddingBridesmaidManResp) XXX_Size() int {
	return xxx_messageInfo_ReportWeddingBridesmaidManResp.Size(m)
}
func (m *ReportWeddingBridesmaidManResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportWeddingBridesmaidManResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportWeddingBridesmaidManResp proto.InternalMessageInfo

// 切换婚礼阶段请求
type SwitchWeddingStageReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	Stage                uint32   `protobuf:"varint,3,opt,name=stage,proto3" json:"stage,omitempty"`
	SubStage             uint32   `protobuf:"varint,4,opt,name=sub_stage,json=subStage,proto3" json:"sub_stage,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchWeddingStageReq) Reset()         { *m = SwitchWeddingStageReq{} }
func (m *SwitchWeddingStageReq) String() string { return proto.CompactTextString(m) }
func (*SwitchWeddingStageReq) ProtoMessage()    {}
func (*SwitchWeddingStageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{27}
}
func (m *SwitchWeddingStageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchWeddingStageReq.Unmarshal(m, b)
}
func (m *SwitchWeddingStageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchWeddingStageReq.Marshal(b, m, deterministic)
}
func (dst *SwitchWeddingStageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchWeddingStageReq.Merge(dst, src)
}
func (m *SwitchWeddingStageReq) XXX_Size() int {
	return xxx_messageInfo_SwitchWeddingStageReq.Size(m)
}
func (m *SwitchWeddingStageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchWeddingStageReq.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchWeddingStageReq proto.InternalMessageInfo

func (m *SwitchWeddingStageReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SwitchWeddingStageReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SwitchWeddingStageReq) GetStage() uint32 {
	if m != nil {
		return m.Stage
	}
	return 0
}

func (m *SwitchWeddingStageReq) GetSubStage() uint32 {
	if m != nil {
		return m.SubStage
	}
	return 0
}

// 切换婚礼阶段响应
type SwitchWeddingStageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchWeddingStageResp) Reset()         { *m = SwitchWeddingStageResp{} }
func (m *SwitchWeddingStageResp) String() string { return proto.CompactTextString(m) }
func (*SwitchWeddingStageResp) ProtoMessage()    {}
func (*SwitchWeddingStageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{28}
}
func (m *SwitchWeddingStageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchWeddingStageResp.Unmarshal(m, b)
}
func (m *SwitchWeddingStageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchWeddingStageResp.Marshal(b, m, deterministic)
}
func (dst *SwitchWeddingStageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchWeddingStageResp.Merge(dst, src)
}
func (m *SwitchWeddingStageResp) XXX_Size() int {
	return xxx_messageInfo_SwitchWeddingStageResp.Size(m)
}
func (m *SwitchWeddingStageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchWeddingStageResp.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchWeddingStageResp proto.InternalMessageInfo

// 拍合照
type TakeWeddingGroupPhotoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	PhotoRul             string   `protobuf:"bytes,3,opt,name=photo_rul,json=photoRul,proto3" json:"photo_rul,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TakeWeddingGroupPhotoReq) Reset()         { *m = TakeWeddingGroupPhotoReq{} }
func (m *TakeWeddingGroupPhotoReq) String() string { return proto.CompactTextString(m) }
func (*TakeWeddingGroupPhotoReq) ProtoMessage()    {}
func (*TakeWeddingGroupPhotoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{29}
}
func (m *TakeWeddingGroupPhotoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TakeWeddingGroupPhotoReq.Unmarshal(m, b)
}
func (m *TakeWeddingGroupPhotoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TakeWeddingGroupPhotoReq.Marshal(b, m, deterministic)
}
func (dst *TakeWeddingGroupPhotoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TakeWeddingGroupPhotoReq.Merge(dst, src)
}
func (m *TakeWeddingGroupPhotoReq) XXX_Size() int {
	return xxx_messageInfo_TakeWeddingGroupPhotoReq.Size(m)
}
func (m *TakeWeddingGroupPhotoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TakeWeddingGroupPhotoReq.DiscardUnknown(m)
}

var xxx_messageInfo_TakeWeddingGroupPhotoReq proto.InternalMessageInfo

func (m *TakeWeddingGroupPhotoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TakeWeddingGroupPhotoReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *TakeWeddingGroupPhotoReq) GetPhotoRul() string {
	if m != nil {
		return m.PhotoRul
	}
	return ""
}

type TakeWeddingGroupPhotoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TakeWeddingGroupPhotoResp) Reset()         { *m = TakeWeddingGroupPhotoResp{} }
func (m *TakeWeddingGroupPhotoResp) String() string { return proto.CompactTextString(m) }
func (*TakeWeddingGroupPhotoResp) ProtoMessage()    {}
func (*TakeWeddingGroupPhotoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{30}
}
func (m *TakeWeddingGroupPhotoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TakeWeddingGroupPhotoResp.Unmarshal(m, b)
}
func (m *TakeWeddingGroupPhotoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TakeWeddingGroupPhotoResp.Marshal(b, m, deterministic)
}
func (dst *TakeWeddingGroupPhotoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TakeWeddingGroupPhotoResp.Merge(dst, src)
}
func (m *TakeWeddingGroupPhotoResp) XXX_Size() int {
	return xxx_messageInfo_TakeWeddingGroupPhotoResp.Size(m)
}
func (m *TakeWeddingGroupPhotoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TakeWeddingGroupPhotoResp.DiscardUnknown(m)
}

var xxx_messageInfo_TakeWeddingGroupPhotoResp proto.InternalMessageInfo

// 获取用户可切换的姿势列表
type GetUserWeddingPoseListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserWeddingPoseListReq) Reset()         { *m = GetUserWeddingPoseListReq{} }
func (m *GetUserWeddingPoseListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserWeddingPoseListReq) ProtoMessage()    {}
func (*GetUserWeddingPoseListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{31}
}
func (m *GetUserWeddingPoseListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserWeddingPoseListReq.Unmarshal(m, b)
}
func (m *GetUserWeddingPoseListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserWeddingPoseListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserWeddingPoseListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserWeddingPoseListReq.Merge(dst, src)
}
func (m *GetUserWeddingPoseListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserWeddingPoseListReq.Size(m)
}
func (m *GetUserWeddingPoseListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserWeddingPoseListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserWeddingPoseListReq proto.InternalMessageInfo

func (m *GetUserWeddingPoseListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserWeddingPoseListReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GetUserWeddingPoseListReq) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type GetUserWeddingPoseListResp struct {
	PoseList             []uint32 `protobuf:"varint,1,rep,packed,name=pose_list,json=poseList,proto3" json:"pose_list,omitempty"`
	CurrPose             uint32   `protobuf:"varint,2,opt,name=curr_pose,json=currPose,proto3" json:"curr_pose,omitempty"`
	Orientation          uint32   `protobuf:"varint,3,opt,name=orientation,proto3" json:"orientation,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserWeddingPoseListResp) Reset()         { *m = GetUserWeddingPoseListResp{} }
func (m *GetUserWeddingPoseListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserWeddingPoseListResp) ProtoMessage()    {}
func (*GetUserWeddingPoseListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{32}
}
func (m *GetUserWeddingPoseListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserWeddingPoseListResp.Unmarshal(m, b)
}
func (m *GetUserWeddingPoseListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserWeddingPoseListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserWeddingPoseListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserWeddingPoseListResp.Merge(dst, src)
}
func (m *GetUserWeddingPoseListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserWeddingPoseListResp.Size(m)
}
func (m *GetUserWeddingPoseListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserWeddingPoseListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserWeddingPoseListResp proto.InternalMessageInfo

func (m *GetUserWeddingPoseListResp) GetPoseList() []uint32 {
	if m != nil {
		return m.PoseList
	}
	return nil
}

func (m *GetUserWeddingPoseListResp) GetCurrPose() uint32 {
	if m != nil {
		return m.CurrPose
	}
	return 0
}

func (m *GetUserWeddingPoseListResp) GetOrientation() uint32 {
	if m != nil {
		return m.Orientation
	}
	return 0
}

// 切换姿势
type SetUserWeddingPoseReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	Pose                 uint32   `protobuf:"varint,3,opt,name=pose,proto3" json:"pose,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserWeddingPoseReq) Reset()         { *m = SetUserWeddingPoseReq{} }
func (m *SetUserWeddingPoseReq) String() string { return proto.CompactTextString(m) }
func (*SetUserWeddingPoseReq) ProtoMessage()    {}
func (*SetUserWeddingPoseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{33}
}
func (m *SetUserWeddingPoseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserWeddingPoseReq.Unmarshal(m, b)
}
func (m *SetUserWeddingPoseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserWeddingPoseReq.Marshal(b, m, deterministic)
}
func (dst *SetUserWeddingPoseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserWeddingPoseReq.Merge(dst, src)
}
func (m *SetUserWeddingPoseReq) XXX_Size() int {
	return xxx_messageInfo_SetUserWeddingPoseReq.Size(m)
}
func (m *SetUserWeddingPoseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserWeddingPoseReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserWeddingPoseReq proto.InternalMessageInfo

func (m *SetUserWeddingPoseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserWeddingPoseReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SetUserWeddingPoseReq) GetPose() uint32 {
	if m != nil {
		return m.Pose
	}
	return 0
}

type SetUserWeddingPoseResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserWeddingPoseResp) Reset()         { *m = SetUserWeddingPoseResp{} }
func (m *SetUserWeddingPoseResp) String() string { return proto.CompactTextString(m) }
func (*SetUserWeddingPoseResp) ProtoMessage()    {}
func (*SetUserWeddingPoseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{34}
}
func (m *SetUserWeddingPoseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserWeddingPoseResp.Unmarshal(m, b)
}
func (m *SetUserWeddingPoseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserWeddingPoseResp.Marshal(b, m, deterministic)
}
func (dst *SetUserWeddingPoseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserWeddingPoseResp.Merge(dst, src)
}
func (m *SetUserWeddingPoseResp) XXX_Size() int {
	return xxx_messageInfo_SetUserWeddingPoseResp.Size(m)
}
func (m *SetUserWeddingPoseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserWeddingPoseResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserWeddingPoseResp proto.InternalMessageInfo

// 切换朝向
type SetUserWeddingOrientationReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	Orientation          uint32   `protobuf:"varint,3,opt,name=orientation,proto3" json:"orientation,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserWeddingOrientationReq) Reset()         { *m = SetUserWeddingOrientationReq{} }
func (m *SetUserWeddingOrientationReq) String() string { return proto.CompactTextString(m) }
func (*SetUserWeddingOrientationReq) ProtoMessage()    {}
func (*SetUserWeddingOrientationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{35}
}
func (m *SetUserWeddingOrientationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserWeddingOrientationReq.Unmarshal(m, b)
}
func (m *SetUserWeddingOrientationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserWeddingOrientationReq.Marshal(b, m, deterministic)
}
func (dst *SetUserWeddingOrientationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserWeddingOrientationReq.Merge(dst, src)
}
func (m *SetUserWeddingOrientationReq) XXX_Size() int {
	return xxx_messageInfo_SetUserWeddingOrientationReq.Size(m)
}
func (m *SetUserWeddingOrientationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserWeddingOrientationReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserWeddingOrientationReq proto.InternalMessageInfo

func (m *SetUserWeddingOrientationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserWeddingOrientationReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SetUserWeddingOrientationReq) GetOrientation() uint32 {
	if m != nil {
		return m.Orientation
	}
	return 0
}

type SetUserWeddingOrientationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserWeddingOrientationResp) Reset()         { *m = SetUserWeddingOrientationResp{} }
func (m *SetUserWeddingOrientationResp) String() string { return proto.CompactTextString(m) }
func (*SetUserWeddingOrientationResp) ProtoMessage()    {}
func (*SetUserWeddingOrientationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{36}
}
func (m *SetUserWeddingOrientationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserWeddingOrientationResp.Unmarshal(m, b)
}
func (m *SetUserWeddingOrientationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserWeddingOrientationResp.Marshal(b, m, deterministic)
}
func (dst *SetUserWeddingOrientationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserWeddingOrientationResp.Merge(dst, src)
}
func (m *SetUserWeddingOrientationResp) XXX_Size() int {
	return xxx_messageInfo_SetUserWeddingOrientationResp.Size(m)
}
func (m *SetUserWeddingOrientationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserWeddingOrientationResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserWeddingOrientationResp proto.InternalMessageInfo

type UserWeddingPose struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Pose                 uint32   `protobuf:"varint,2,opt,name=pose,proto3" json:"pose,omitempty"`
	Orientation          uint32   `protobuf:"varint,3,opt,name=orientation,proto3" json:"orientation,omitempty"`
	PoseBoneId           uint32   `protobuf:"varint,4,opt,name=pose_bone_id,json=poseBoneId,proto3" json:"pose_bone_id,omitempty"`
	BasePoseBoneId       uint32   `protobuf:"varint,5,opt,name=base_pose_bone_id,json=basePoseBoneId,proto3" json:"base_pose_bone_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserWeddingPose) Reset()         { *m = UserWeddingPose{} }
func (m *UserWeddingPose) String() string { return proto.CompactTextString(m) }
func (*UserWeddingPose) ProtoMessage()    {}
func (*UserWeddingPose) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{37}
}
func (m *UserWeddingPose) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserWeddingPose.Unmarshal(m, b)
}
func (m *UserWeddingPose) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserWeddingPose.Marshal(b, m, deterministic)
}
func (dst *UserWeddingPose) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserWeddingPose.Merge(dst, src)
}
func (m *UserWeddingPose) XXX_Size() int {
	return xxx_messageInfo_UserWeddingPose.Size(m)
}
func (m *UserWeddingPose) XXX_DiscardUnknown() {
	xxx_messageInfo_UserWeddingPose.DiscardUnknown(m)
}

var xxx_messageInfo_UserWeddingPose proto.InternalMessageInfo

func (m *UserWeddingPose) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserWeddingPose) GetPose() uint32 {
	if m != nil {
		return m.Pose
	}
	return 0
}

func (m *UserWeddingPose) GetOrientation() uint32 {
	if m != nil {
		return m.Orientation
	}
	return 0
}

func (m *UserWeddingPose) GetPoseBoneId() uint32 {
	if m != nil {
		return m.PoseBoneId
	}
	return 0
}

func (m *UserWeddingPose) GetBasePoseBoneId() uint32 {
	if m != nil {
		return m.BasePoseBoneId
	}
	return 0
}

// 批量获取用户姿势
type BatchGetUserWeddingPoseReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	UidList              []uint32 `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserWeddingPoseReq) Reset()         { *m = BatchGetUserWeddingPoseReq{} }
func (m *BatchGetUserWeddingPoseReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserWeddingPoseReq) ProtoMessage()    {}
func (*BatchGetUserWeddingPoseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{38}
}
func (m *BatchGetUserWeddingPoseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserWeddingPoseReq.Unmarshal(m, b)
}
func (m *BatchGetUserWeddingPoseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserWeddingPoseReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserWeddingPoseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserWeddingPoseReq.Merge(dst, src)
}
func (m *BatchGetUserWeddingPoseReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserWeddingPoseReq.Size(m)
}
func (m *BatchGetUserWeddingPoseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserWeddingPoseReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserWeddingPoseReq proto.InternalMessageInfo

func (m *BatchGetUserWeddingPoseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchGetUserWeddingPoseReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *BatchGetUserWeddingPoseReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetUserWeddingPoseResp struct {
	UserPoseList         []*UserWeddingPose `protobuf:"bytes,1,rep,name=user_pose_list,json=userPoseList,proto3" json:"user_pose_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchGetUserWeddingPoseResp) Reset()         { *m = BatchGetUserWeddingPoseResp{} }
func (m *BatchGetUserWeddingPoseResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserWeddingPoseResp) ProtoMessage()    {}
func (*BatchGetUserWeddingPoseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{39}
}
func (m *BatchGetUserWeddingPoseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserWeddingPoseResp.Unmarshal(m, b)
}
func (m *BatchGetUserWeddingPoseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserWeddingPoseResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserWeddingPoseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserWeddingPoseResp.Merge(dst, src)
}
func (m *BatchGetUserWeddingPoseResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserWeddingPoseResp.Size(m)
}
func (m *BatchGetUserWeddingPoseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserWeddingPoseResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserWeddingPoseResp proto.InternalMessageInfo

func (m *BatchGetUserWeddingPoseResp) GetUserPoseList() []*UserWeddingPose {
	if m != nil {
		return m.UserPoseList
	}
	return nil
}

// 获取房间合照麦位位置映射请求
type GetWeddingGroupPhotoSeatListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeddingGroupPhotoSeatListReq) Reset()         { *m = GetWeddingGroupPhotoSeatListReq{} }
func (m *GetWeddingGroupPhotoSeatListReq) String() string { return proto.CompactTextString(m) }
func (*GetWeddingGroupPhotoSeatListReq) ProtoMessage()    {}
func (*GetWeddingGroupPhotoSeatListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{40}
}
func (m *GetWeddingGroupPhotoSeatListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingGroupPhotoSeatListReq.Unmarshal(m, b)
}
func (m *GetWeddingGroupPhotoSeatListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingGroupPhotoSeatListReq.Marshal(b, m, deterministic)
}
func (dst *GetWeddingGroupPhotoSeatListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingGroupPhotoSeatListReq.Merge(dst, src)
}
func (m *GetWeddingGroupPhotoSeatListReq) XXX_Size() int {
	return xxx_messageInfo_GetWeddingGroupPhotoSeatListReq.Size(m)
}
func (m *GetWeddingGroupPhotoSeatListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingGroupPhotoSeatListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingGroupPhotoSeatListReq proto.InternalMessageInfo

func (m *GetWeddingGroupPhotoSeatListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetWeddingGroupPhotoSeatListReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

// 合照麦位位置映射
type WeddingGroupPhotoSeat struct {
	MicId                uint32   `protobuf:"varint,1,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingGroupPhotoSeat) Reset()         { *m = WeddingGroupPhotoSeat{} }
func (m *WeddingGroupPhotoSeat) String() string { return proto.CompactTextString(m) }
func (*WeddingGroupPhotoSeat) ProtoMessage()    {}
func (*WeddingGroupPhotoSeat) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{41}
}
func (m *WeddingGroupPhotoSeat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingGroupPhotoSeat.Unmarshal(m, b)
}
func (m *WeddingGroupPhotoSeat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingGroupPhotoSeat.Marshal(b, m, deterministic)
}
func (dst *WeddingGroupPhotoSeat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingGroupPhotoSeat.Merge(dst, src)
}
func (m *WeddingGroupPhotoSeat) XXX_Size() int {
	return xxx_messageInfo_WeddingGroupPhotoSeat.Size(m)
}
func (m *WeddingGroupPhotoSeat) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingGroupPhotoSeat.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingGroupPhotoSeat proto.InternalMessageInfo

func (m *WeddingGroupPhotoSeat) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

// 获取房间合照麦位位置映射响应
type GetWeddingGroupPhotoSeatListResp struct {
	SeatList             []*WeddingGroupPhotoSeat `protobuf:"bytes,1,rep,name=seat_list,json=seatList,proto3" json:"seat_list,omitempty"`
	PoseConfirmedUidList []uint32                 `protobuf:"varint,2,rep,packed,name=pose_confirmed_uid_list,json=poseConfirmedUidList,proto3" json:"pose_confirmed_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetWeddingGroupPhotoSeatListResp) Reset()         { *m = GetWeddingGroupPhotoSeatListResp{} }
func (m *GetWeddingGroupPhotoSeatListResp) String() string { return proto.CompactTextString(m) }
func (*GetWeddingGroupPhotoSeatListResp) ProtoMessage()    {}
func (*GetWeddingGroupPhotoSeatListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{42}
}
func (m *GetWeddingGroupPhotoSeatListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingGroupPhotoSeatListResp.Unmarshal(m, b)
}
func (m *GetWeddingGroupPhotoSeatListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingGroupPhotoSeatListResp.Marshal(b, m, deterministic)
}
func (dst *GetWeddingGroupPhotoSeatListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingGroupPhotoSeatListResp.Merge(dst, src)
}
func (m *GetWeddingGroupPhotoSeatListResp) XXX_Size() int {
	return xxx_messageInfo_GetWeddingGroupPhotoSeatListResp.Size(m)
}
func (m *GetWeddingGroupPhotoSeatListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingGroupPhotoSeatListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingGroupPhotoSeatListResp proto.InternalMessageInfo

func (m *GetWeddingGroupPhotoSeatListResp) GetSeatList() []*WeddingGroupPhotoSeat {
	if m != nil {
		return m.SeatList
	}
	return nil
}

func (m *GetWeddingGroupPhotoSeatListResp) GetPoseConfirmedUidList() []uint32 {
	if m != nil {
		return m.PoseConfirmedUidList
	}
	return nil
}

// 设置用户合照位置请求
type SetUserWeddingGroupPhotoSeatReq struct {
	Uid                  uint32                   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32                   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	SeatList             []*WeddingGroupPhotoSeat `protobuf:"bytes,3,rep,name=seat_list,json=seatList,proto3" json:"seat_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *SetUserWeddingGroupPhotoSeatReq) Reset()         { *m = SetUserWeddingGroupPhotoSeatReq{} }
func (m *SetUserWeddingGroupPhotoSeatReq) String() string { return proto.CompactTextString(m) }
func (*SetUserWeddingGroupPhotoSeatReq) ProtoMessage()    {}
func (*SetUserWeddingGroupPhotoSeatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{43}
}
func (m *SetUserWeddingGroupPhotoSeatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserWeddingGroupPhotoSeatReq.Unmarshal(m, b)
}
func (m *SetUserWeddingGroupPhotoSeatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserWeddingGroupPhotoSeatReq.Marshal(b, m, deterministic)
}
func (dst *SetUserWeddingGroupPhotoSeatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserWeddingGroupPhotoSeatReq.Merge(dst, src)
}
func (m *SetUserWeddingGroupPhotoSeatReq) XXX_Size() int {
	return xxx_messageInfo_SetUserWeddingGroupPhotoSeatReq.Size(m)
}
func (m *SetUserWeddingGroupPhotoSeatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserWeddingGroupPhotoSeatReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserWeddingGroupPhotoSeatReq proto.InternalMessageInfo

func (m *SetUserWeddingGroupPhotoSeatReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserWeddingGroupPhotoSeatReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SetUserWeddingGroupPhotoSeatReq) GetSeatList() []*WeddingGroupPhotoSeat {
	if m != nil {
		return m.SeatList
	}
	return nil
}

// 设置用户合照位置响应
type SetUserWeddingGroupPhotoSeatResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserWeddingGroupPhotoSeatResp) Reset()         { *m = SetUserWeddingGroupPhotoSeatResp{} }
func (m *SetUserWeddingGroupPhotoSeatResp) String() string { return proto.CompactTextString(m) }
func (*SetUserWeddingGroupPhotoSeatResp) ProtoMessage()    {}
func (*SetUserWeddingGroupPhotoSeatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{44}
}
func (m *SetUserWeddingGroupPhotoSeatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserWeddingGroupPhotoSeatResp.Unmarshal(m, b)
}
func (m *SetUserWeddingGroupPhotoSeatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserWeddingGroupPhotoSeatResp.Marshal(b, m, deterministic)
}
func (dst *SetUserWeddingGroupPhotoSeatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserWeddingGroupPhotoSeatResp.Merge(dst, src)
}
func (m *SetUserWeddingGroupPhotoSeatResp) XXX_Size() int {
	return xxx_messageInfo_SetUserWeddingGroupPhotoSeatResp.Size(m)
}
func (m *SetUserWeddingGroupPhotoSeatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserWeddingGroupPhotoSeatResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserWeddingGroupPhotoSeatResp proto.InternalMessageInfo

// 增加婚礼伴郎伴娘请求
type AddWeddingBridesmaidManReq struct {
	WeddingId            uint32   `protobuf:"varint,1,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	BridesmaidUidList    []uint32 `protobuf:"varint,3,rep,packed,name=bridesmaid_uid_list,json=bridesmaidUidList,proto3" json:"bridesmaid_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddWeddingBridesmaidManReq) Reset()         { *m = AddWeddingBridesmaidManReq{} }
func (m *AddWeddingBridesmaidManReq) String() string { return proto.CompactTextString(m) }
func (*AddWeddingBridesmaidManReq) ProtoMessage()    {}
func (*AddWeddingBridesmaidManReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{45}
}
func (m *AddWeddingBridesmaidManReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddWeddingBridesmaidManReq.Unmarshal(m, b)
}
func (m *AddWeddingBridesmaidManReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddWeddingBridesmaidManReq.Marshal(b, m, deterministic)
}
func (dst *AddWeddingBridesmaidManReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddWeddingBridesmaidManReq.Merge(dst, src)
}
func (m *AddWeddingBridesmaidManReq) XXX_Size() int {
	return xxx_messageInfo_AddWeddingBridesmaidManReq.Size(m)
}
func (m *AddWeddingBridesmaidManReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddWeddingBridesmaidManReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddWeddingBridesmaidManReq proto.InternalMessageInfo

func (m *AddWeddingBridesmaidManReq) GetWeddingId() uint32 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

func (m *AddWeddingBridesmaidManReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *AddWeddingBridesmaidManReq) GetBridesmaidUidList() []uint32 {
	if m != nil {
		return m.BridesmaidUidList
	}
	return nil
}

// 增加婚礼伴郎伴娘响应
type AddWeddingBridesmaidManResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddWeddingBridesmaidManResp) Reset()         { *m = AddWeddingBridesmaidManResp{} }
func (m *AddWeddingBridesmaidManResp) String() string { return proto.CompactTextString(m) }
func (*AddWeddingBridesmaidManResp) ProtoMessage()    {}
func (*AddWeddingBridesmaidManResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{46}
}
func (m *AddWeddingBridesmaidManResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddWeddingBridesmaidManResp.Unmarshal(m, b)
}
func (m *AddWeddingBridesmaidManResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddWeddingBridesmaidManResp.Marshal(b, m, deterministic)
}
func (dst *AddWeddingBridesmaidManResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddWeddingBridesmaidManResp.Merge(dst, src)
}
func (m *AddWeddingBridesmaidManResp) XXX_Size() int {
	return xxx_messageInfo_AddWeddingBridesmaidManResp.Size(m)
}
func (m *AddWeddingBridesmaidManResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddWeddingBridesmaidManResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddWeddingBridesmaidManResp proto.InternalMessageInfo

type WeddingSchedule struct {
	PlanId               uint32   `protobuf:"varint,1,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`
	WeddingId            uint32   `protobuf:"varint,2,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	BrideUid             uint32   `protobuf:"varint,3,opt,name=bride_uid,json=brideUid,proto3" json:"bride_uid,omitempty"`
	GroomUid             uint32   `protobuf:"varint,4,opt,name=groom_uid,json=groomUid,proto3" json:"groom_uid,omitempty"`
	StartTime            int64    `protobuf:"varint,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64    `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingSchedule) Reset()         { *m = WeddingSchedule{} }
func (m *WeddingSchedule) String() string { return proto.CompactTextString(m) }
func (*WeddingSchedule) ProtoMessage()    {}
func (*WeddingSchedule) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{47}
}
func (m *WeddingSchedule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingSchedule.Unmarshal(m, b)
}
func (m *WeddingSchedule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingSchedule.Marshal(b, m, deterministic)
}
func (dst *WeddingSchedule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingSchedule.Merge(dst, src)
}
func (m *WeddingSchedule) XXX_Size() int {
	return xxx_messageInfo_WeddingSchedule.Size(m)
}
func (m *WeddingSchedule) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingSchedule.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingSchedule proto.InternalMessageInfo

func (m *WeddingSchedule) GetPlanId() uint32 {
	if m != nil {
		return m.PlanId
	}
	return 0
}

func (m *WeddingSchedule) GetWeddingId() uint32 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

func (m *WeddingSchedule) GetBrideUid() uint32 {
	if m != nil {
		return m.BrideUid
	}
	return 0
}

func (m *WeddingSchedule) GetGroomUid() uint32 {
	if m != nil {
		return m.GroomUid
	}
	return 0
}

func (m *WeddingSchedule) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *WeddingSchedule) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetWeddingScheduleListReq struct {
	MinBeginTime         int64    `protobuf:"varint,1,opt,name=min_begin_time,json=minBeginTime,proto3" json:"min_begin_time,omitempty"`
	MaxBeginTime         int64    `protobuf:"varint,2,opt,name=max_begin_time,json=maxBeginTime,proto3" json:"max_begin_time,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeddingScheduleListReq) Reset()         { *m = GetWeddingScheduleListReq{} }
func (m *GetWeddingScheduleListReq) String() string { return proto.CompactTextString(m) }
func (*GetWeddingScheduleListReq) ProtoMessage()    {}
func (*GetWeddingScheduleListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{48}
}
func (m *GetWeddingScheduleListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingScheduleListReq.Unmarshal(m, b)
}
func (m *GetWeddingScheduleListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingScheduleListReq.Marshal(b, m, deterministic)
}
func (dst *GetWeddingScheduleListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingScheduleListReq.Merge(dst, src)
}
func (m *GetWeddingScheduleListReq) XXX_Size() int {
	return xxx_messageInfo_GetWeddingScheduleListReq.Size(m)
}
func (m *GetWeddingScheduleListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingScheduleListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingScheduleListReq proto.InternalMessageInfo

func (m *GetWeddingScheduleListReq) GetMinBeginTime() int64 {
	if m != nil {
		return m.MinBeginTime
	}
	return 0
}

func (m *GetWeddingScheduleListReq) GetMaxBeginTime() int64 {
	if m != nil {
		return m.MaxBeginTime
	}
	return 0
}

func (m *GetWeddingScheduleListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetWeddingScheduleListResp struct {
	ScheduleList         []*WeddingSchedule `protobuf:"bytes,1,rep,name=schedule_list,json=scheduleList,proto3" json:"schedule_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetWeddingScheduleListResp) Reset()         { *m = GetWeddingScheduleListResp{} }
func (m *GetWeddingScheduleListResp) String() string { return proto.CompactTextString(m) }
func (*GetWeddingScheduleListResp) ProtoMessage()    {}
func (*GetWeddingScheduleListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{49}
}
func (m *GetWeddingScheduleListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingScheduleListResp.Unmarshal(m, b)
}
func (m *GetWeddingScheduleListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingScheduleListResp.Marshal(b, m, deterministic)
}
func (dst *GetWeddingScheduleListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingScheduleListResp.Merge(dst, src)
}
func (m *GetWeddingScheduleListResp) XXX_Size() int {
	return xxx_messageInfo_GetWeddingScheduleListResp.Size(m)
}
func (m *GetWeddingScheduleListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingScheduleListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingScheduleListResp proto.InternalMessageInfo

func (m *GetWeddingScheduleListResp) GetScheduleList() []*WeddingSchedule {
	if m != nil {
		return m.ScheduleList
	}
	return nil
}

// 结婚证
type WeddingCertificate struct {
	WeddingId            uint32   `protobuf:"varint,1,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	GroomUid             uint32   `protobuf:"varint,2,opt,name=groom_uid,json=groomUid,proto3" json:"groom_uid,omitempty"`
	BrideUid             uint32   `protobuf:"varint,3,opt,name=bride_uid,json=brideUid,proto3" json:"bride_uid,omitempty"`
	WeddingTime          int64    `protobuf:"varint,4,opt,name=wedding_time,json=weddingTime,proto3" json:"wedding_time,omitempty"`
	WeddingThemeId       uint32   `protobuf:"varint,5,opt,name=wedding_theme_id,json=weddingThemeId,proto3" json:"wedding_theme_id,omitempty"`
	PicUrl               string   `protobuf:"bytes,6,opt,name=pic_url,json=picUrl,proto3" json:"pic_url,omitempty"`
	WeddingLv            uint32   `protobuf:"varint,7,opt,name=wedding_lv,json=weddingLv,proto3" json:"wedding_lv,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingCertificate) Reset()         { *m = WeddingCertificate{} }
func (m *WeddingCertificate) String() string { return proto.CompactTextString(m) }
func (*WeddingCertificate) ProtoMessage()    {}
func (*WeddingCertificate) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{50}
}
func (m *WeddingCertificate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingCertificate.Unmarshal(m, b)
}
func (m *WeddingCertificate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingCertificate.Marshal(b, m, deterministic)
}
func (dst *WeddingCertificate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingCertificate.Merge(dst, src)
}
func (m *WeddingCertificate) XXX_Size() int {
	return xxx_messageInfo_WeddingCertificate.Size(m)
}
func (m *WeddingCertificate) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingCertificate.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingCertificate proto.InternalMessageInfo

func (m *WeddingCertificate) GetWeddingId() uint32 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

func (m *WeddingCertificate) GetGroomUid() uint32 {
	if m != nil {
		return m.GroomUid
	}
	return 0
}

func (m *WeddingCertificate) GetBrideUid() uint32 {
	if m != nil {
		return m.BrideUid
	}
	return 0
}

func (m *WeddingCertificate) GetWeddingTime() int64 {
	if m != nil {
		return m.WeddingTime
	}
	return 0
}

func (m *WeddingCertificate) GetWeddingThemeId() uint32 {
	if m != nil {
		return m.WeddingThemeId
	}
	return 0
}

func (m *WeddingCertificate) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *WeddingCertificate) GetWeddingLv() uint32 {
	if m != nil {
		return m.WeddingLv
	}
	return 0
}

type GetUserWeddingCertificateReq struct {
	UidA                 uint32   `protobuf:"varint,1,opt,name=uid_a,json=uidA,proto3" json:"uid_a,omitempty"`
	UidB                 uint32   `protobuf:"varint,2,opt,name=uid_b,json=uidB,proto3" json:"uid_b,omitempty"`
	BeginTime            int64    `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserWeddingCertificateReq) Reset()         { *m = GetUserWeddingCertificateReq{} }
func (m *GetUserWeddingCertificateReq) String() string { return proto.CompactTextString(m) }
func (*GetUserWeddingCertificateReq) ProtoMessage()    {}
func (*GetUserWeddingCertificateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{51}
}
func (m *GetUserWeddingCertificateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserWeddingCertificateReq.Unmarshal(m, b)
}
func (m *GetUserWeddingCertificateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserWeddingCertificateReq.Marshal(b, m, deterministic)
}
func (dst *GetUserWeddingCertificateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserWeddingCertificateReq.Merge(dst, src)
}
func (m *GetUserWeddingCertificateReq) XXX_Size() int {
	return xxx_messageInfo_GetUserWeddingCertificateReq.Size(m)
}
func (m *GetUserWeddingCertificateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserWeddingCertificateReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserWeddingCertificateReq proto.InternalMessageInfo

func (m *GetUserWeddingCertificateReq) GetUidA() uint32 {
	if m != nil {
		return m.UidA
	}
	return 0
}

func (m *GetUserWeddingCertificateReq) GetUidB() uint32 {
	if m != nil {
		return m.UidB
	}
	return 0
}

func (m *GetUserWeddingCertificateReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

type GetUserWeddingCertificateResp struct {
	WeddingCertificate   *WeddingCertificate `protobuf:"bytes,1,opt,name=wedding_certificate,json=weddingCertificate,proto3" json:"wedding_certificate,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetUserWeddingCertificateResp) Reset()         { *m = GetUserWeddingCertificateResp{} }
func (m *GetUserWeddingCertificateResp) String() string { return proto.CompactTextString(m) }
func (*GetUserWeddingCertificateResp) ProtoMessage()    {}
func (*GetUserWeddingCertificateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{52}
}
func (m *GetUserWeddingCertificateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserWeddingCertificateResp.Unmarshal(m, b)
}
func (m *GetUserWeddingCertificateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserWeddingCertificateResp.Marshal(b, m, deterministic)
}
func (dst *GetUserWeddingCertificateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserWeddingCertificateResp.Merge(dst, src)
}
func (m *GetUserWeddingCertificateResp) XXX_Size() int {
	return xxx_messageInfo_GetUserWeddingCertificateResp.Size(m)
}
func (m *GetUserWeddingCertificateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserWeddingCertificateResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserWeddingCertificateResp proto.InternalMessageInfo

func (m *GetUserWeddingCertificateResp) GetWeddingCertificate() *WeddingCertificate {
	if m != nil {
		return m.WeddingCertificate
	}
	return nil
}

type WeddingScenePic struct {
	Scene                uint32   `protobuf:"varint,1,opt,name=scene,proto3" json:"scene,omitempty"`
	PicUrl               string   `protobuf:"bytes,2,opt,name=pic_url,json=picUrl,proto3" json:"pic_url,omitempty"`
	CreateTime           int64    `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	SceneIcon            string   `protobuf:"bytes,4,opt,name=scene_icon,json=sceneIcon,proto3" json:"scene_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingScenePic) Reset()         { *m = WeddingScenePic{} }
func (m *WeddingScenePic) String() string { return proto.CompactTextString(m) }
func (*WeddingScenePic) ProtoMessage()    {}
func (*WeddingScenePic) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{53}
}
func (m *WeddingScenePic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingScenePic.Unmarshal(m, b)
}
func (m *WeddingScenePic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingScenePic.Marshal(b, m, deterministic)
}
func (dst *WeddingScenePic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingScenePic.Merge(dst, src)
}
func (m *WeddingScenePic) XXX_Size() int {
	return xxx_messageInfo_WeddingScenePic.Size(m)
}
func (m *WeddingScenePic) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingScenePic.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingScenePic proto.InternalMessageInfo

func (m *WeddingScenePic) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *WeddingScenePic) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *WeddingScenePic) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *WeddingScenePic) GetSceneIcon() string {
	if m != nil {
		return m.SceneIcon
	}
	return ""
}

// 婚礼片段信息
type WeddingClipInfo struct {
	ThemeId              uint32             `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	ThemeName            string             `protobuf:"bytes,2,opt,name=theme_name,json=themeName,proto3" json:"theme_name,omitempty"`
	WeddingId            uint32             `protobuf:"varint,3,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	ScenePicList         []*WeddingScenePic `protobuf:"bytes,4,rep,name=scene_pic_list,json=scenePicList,proto3" json:"scene_pic_list,omitempty"`
	Cid                  uint32             `protobuf:"varint,5,opt,name=cid,proto3" json:"cid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *WeddingClipInfo) Reset()         { *m = WeddingClipInfo{} }
func (m *WeddingClipInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingClipInfo) ProtoMessage()    {}
func (*WeddingClipInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{54}
}
func (m *WeddingClipInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingClipInfo.Unmarshal(m, b)
}
func (m *WeddingClipInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingClipInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingClipInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingClipInfo.Merge(dst, src)
}
func (m *WeddingClipInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingClipInfo.Size(m)
}
func (m *WeddingClipInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingClipInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingClipInfo proto.InternalMessageInfo

func (m *WeddingClipInfo) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *WeddingClipInfo) GetThemeName() string {
	if m != nil {
		return m.ThemeName
	}
	return ""
}

func (m *WeddingClipInfo) GetWeddingId() uint32 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

func (m *WeddingClipInfo) GetScenePicList() []*WeddingScenePic {
	if m != nil {
		return m.ScenePicList
	}
	return nil
}

func (m *WeddingClipInfo) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

// 获取用户婚礼沉淀信息请求
type GetUserWeddingWeddingClipsReq struct {
	UidA                 uint32   `protobuf:"varint,1,opt,name=uid_a,json=uidA,proto3" json:"uid_a,omitempty"`
	UidB                 uint32   `protobuf:"varint,2,opt,name=uid_b,json=uidB,proto3" json:"uid_b,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserWeddingWeddingClipsReq) Reset()         { *m = GetUserWeddingWeddingClipsReq{} }
func (m *GetUserWeddingWeddingClipsReq) String() string { return proto.CompactTextString(m) }
func (*GetUserWeddingWeddingClipsReq) ProtoMessage()    {}
func (*GetUserWeddingWeddingClipsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{55}
}
func (m *GetUserWeddingWeddingClipsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserWeddingWeddingClipsReq.Unmarshal(m, b)
}
func (m *GetUserWeddingWeddingClipsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserWeddingWeddingClipsReq.Marshal(b, m, deterministic)
}
func (dst *GetUserWeddingWeddingClipsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserWeddingWeddingClipsReq.Merge(dst, src)
}
func (m *GetUserWeddingWeddingClipsReq) XXX_Size() int {
	return xxx_messageInfo_GetUserWeddingWeddingClipsReq.Size(m)
}
func (m *GetUserWeddingWeddingClipsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserWeddingWeddingClipsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserWeddingWeddingClipsReq proto.InternalMessageInfo

func (m *GetUserWeddingWeddingClipsReq) GetUidA() uint32 {
	if m != nil {
		return m.UidA
	}
	return 0
}

func (m *GetUserWeddingWeddingClipsReq) GetUidB() uint32 {
	if m != nil {
		return m.UidB
	}
	return 0
}

// 获取用户婚礼沉淀信息响应
type GetUserWeddingWeddingClipsResp struct {
	WeddingClipList      []*WeddingClipInfo `protobuf:"bytes,1,rep,name=wedding_clip_list,json=weddingClipList,proto3" json:"wedding_clip_list,omitempty"`
	GroupPhotoList       []*WeddingClipInfo `protobuf:"bytes,2,rep,name=group_photo_list,json=groupPhotoList,proto3" json:"group_photo_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetUserWeddingWeddingClipsResp) Reset()         { *m = GetUserWeddingWeddingClipsResp{} }
func (m *GetUserWeddingWeddingClipsResp) String() string { return proto.CompactTextString(m) }
func (*GetUserWeddingWeddingClipsResp) ProtoMessage()    {}
func (*GetUserWeddingWeddingClipsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{56}
}
func (m *GetUserWeddingWeddingClipsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserWeddingWeddingClipsResp.Unmarshal(m, b)
}
func (m *GetUserWeddingWeddingClipsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserWeddingWeddingClipsResp.Marshal(b, m, deterministic)
}
func (dst *GetUserWeddingWeddingClipsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserWeddingWeddingClipsResp.Merge(dst, src)
}
func (m *GetUserWeddingWeddingClipsResp) XXX_Size() int {
	return xxx_messageInfo_GetUserWeddingWeddingClipsResp.Size(m)
}
func (m *GetUserWeddingWeddingClipsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserWeddingWeddingClipsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserWeddingWeddingClipsResp proto.InternalMessageInfo

func (m *GetUserWeddingWeddingClipsResp) GetWeddingClipList() []*WeddingClipInfo {
	if m != nil {
		return m.WeddingClipList
	}
	return nil
}

func (m *GetUserWeddingWeddingClipsResp) GetGroupPhotoList() []*WeddingClipInfo {
	if m != nil {
		return m.GroupPhotoList
	}
	return nil
}

// 上报婚礼场景片段图片
type ReportWeddingScenePicReq struct {
	OpUid                uint32   `protobuf:"varint,1,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	WeddingId            uint32   `protobuf:"varint,3,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	Scene                uint32   `protobuf:"varint,4,opt,name=scene,proto3" json:"scene,omitempty"`
	PicUrl               string   `protobuf:"bytes,5,opt,name=pic_url,json=picUrl,proto3" json:"pic_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportWeddingScenePicReq) Reset()         { *m = ReportWeddingScenePicReq{} }
func (m *ReportWeddingScenePicReq) String() string { return proto.CompactTextString(m) }
func (*ReportWeddingScenePicReq) ProtoMessage()    {}
func (*ReportWeddingScenePicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{57}
}
func (m *ReportWeddingScenePicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportWeddingScenePicReq.Unmarshal(m, b)
}
func (m *ReportWeddingScenePicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportWeddingScenePicReq.Marshal(b, m, deterministic)
}
func (dst *ReportWeddingScenePicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportWeddingScenePicReq.Merge(dst, src)
}
func (m *ReportWeddingScenePicReq) XXX_Size() int {
	return xxx_messageInfo_ReportWeddingScenePicReq.Size(m)
}
func (m *ReportWeddingScenePicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportWeddingScenePicReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportWeddingScenePicReq proto.InternalMessageInfo

func (m *ReportWeddingScenePicReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *ReportWeddingScenePicReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *ReportWeddingScenePicReq) GetWeddingId() uint32 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

func (m *ReportWeddingScenePicReq) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *ReportWeddingScenePicReq) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

type ReportWeddingScenePicResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportWeddingScenePicResp) Reset()         { *m = ReportWeddingScenePicResp{} }
func (m *ReportWeddingScenePicResp) String() string { return proto.CompactTextString(m) }
func (*ReportWeddingScenePicResp) ProtoMessage()    {}
func (*ReportWeddingScenePicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{58}
}
func (m *ReportWeddingScenePicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportWeddingScenePicResp.Unmarshal(m, b)
}
func (m *ReportWeddingScenePicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportWeddingScenePicResp.Marshal(b, m, deterministic)
}
func (dst *ReportWeddingScenePicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportWeddingScenePicResp.Merge(dst, src)
}
func (m *ReportWeddingScenePicResp) XXX_Size() int {
	return xxx_messageInfo_ReportWeddingScenePicResp.Size(m)
}
func (m *ReportWeddingScenePicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportWeddingScenePicResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportWeddingScenePicResp proto.InternalMessageInfo

type PageGetGoingWeddingListReq struct {
	PageNum              uint32   `protobuf:"varint,1,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PageGetGoingWeddingListReq) Reset()         { *m = PageGetGoingWeddingListReq{} }
func (m *PageGetGoingWeddingListReq) String() string { return proto.CompactTextString(m) }
func (*PageGetGoingWeddingListReq) ProtoMessage()    {}
func (*PageGetGoingWeddingListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{59}
}
func (m *PageGetGoingWeddingListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PageGetGoingWeddingListReq.Unmarshal(m, b)
}
func (m *PageGetGoingWeddingListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PageGetGoingWeddingListReq.Marshal(b, m, deterministic)
}
func (dst *PageGetGoingWeddingListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PageGetGoingWeddingListReq.Merge(dst, src)
}
func (m *PageGetGoingWeddingListReq) XXX_Size() int {
	return xxx_messageInfo_PageGetGoingWeddingListReq.Size(m)
}
func (m *PageGetGoingWeddingListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PageGetGoingWeddingListReq.DiscardUnknown(m)
}

var xxx_messageInfo_PageGetGoingWeddingListReq proto.InternalMessageInfo

func (m *PageGetGoingWeddingListReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *PageGetGoingWeddingListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type PageGetGoingWeddingListResp struct {
	WeddingList          []*GoingWeddingInfo `protobuf:"bytes,1,rep,name=wedding_list,json=weddingList,proto3" json:"wedding_list,omitempty"`
	HasMore              bool                `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *PageGetGoingWeddingListResp) Reset()         { *m = PageGetGoingWeddingListResp{} }
func (m *PageGetGoingWeddingListResp) String() string { return proto.CompactTextString(m) }
func (*PageGetGoingWeddingListResp) ProtoMessage()    {}
func (*PageGetGoingWeddingListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{60}
}
func (m *PageGetGoingWeddingListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PageGetGoingWeddingListResp.Unmarshal(m, b)
}
func (m *PageGetGoingWeddingListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PageGetGoingWeddingListResp.Marshal(b, m, deterministic)
}
func (dst *PageGetGoingWeddingListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PageGetGoingWeddingListResp.Merge(dst, src)
}
func (m *PageGetGoingWeddingListResp) XXX_Size() int {
	return xxx_messageInfo_PageGetGoingWeddingListResp.Size(m)
}
func (m *PageGetGoingWeddingListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PageGetGoingWeddingListResp.DiscardUnknown(m)
}

var xxx_messageInfo_PageGetGoingWeddingListResp proto.InternalMessageInfo

func (m *PageGetGoingWeddingListResp) GetWeddingList() []*GoingWeddingInfo {
	if m != nil {
		return m.WeddingList
	}
	return nil
}

func (m *PageGetGoingWeddingListResp) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

type GoingWeddingInfo struct {
	WeddingId            uint32   `protobuf:"varint,1,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	WeddingPlanId        uint32   `protobuf:"varint,2,opt,name=wedding_plan_id,json=weddingPlanId,proto3" json:"wedding_plan_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ThemeId              uint32   `protobuf:"varint,4,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	ThemeType            uint32   `protobuf:"varint,5,opt,name=theme_type,json=themeType,proto3" json:"theme_type,omitempty"`
	CurrLevel            uint32   `protobuf:"varint,6,opt,name=curr_level,json=currLevel,proto3" json:"curr_level,omitempty"`
	CurrStage            uint32   `protobuf:"varint,7,opt,name=curr_stage,json=currStage,proto3" json:"curr_stage,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoingWeddingInfo) Reset()         { *m = GoingWeddingInfo{} }
func (m *GoingWeddingInfo) String() string { return proto.CompactTextString(m) }
func (*GoingWeddingInfo) ProtoMessage()    {}
func (*GoingWeddingInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{61}
}
func (m *GoingWeddingInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoingWeddingInfo.Unmarshal(m, b)
}
func (m *GoingWeddingInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoingWeddingInfo.Marshal(b, m, deterministic)
}
func (dst *GoingWeddingInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoingWeddingInfo.Merge(dst, src)
}
func (m *GoingWeddingInfo) XXX_Size() int {
	return xxx_messageInfo_GoingWeddingInfo.Size(m)
}
func (m *GoingWeddingInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GoingWeddingInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GoingWeddingInfo proto.InternalMessageInfo

func (m *GoingWeddingInfo) GetWeddingId() uint32 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

func (m *GoingWeddingInfo) GetWeddingPlanId() uint32 {
	if m != nil {
		return m.WeddingPlanId
	}
	return 0
}

func (m *GoingWeddingInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GoingWeddingInfo) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *GoingWeddingInfo) GetThemeType() uint32 {
	if m != nil {
		return m.ThemeType
	}
	return 0
}

func (m *GoingWeddingInfo) GetCurrLevel() uint32 {
	if m != nil {
		return m.CurrLevel
	}
	return 0
}

func (m *GoingWeddingInfo) GetCurrStage() uint32 {
	if m != nil {
		return m.CurrStage
	}
	return 0
}

type GetWeddingRecordByTimeRangeReq struct {
	BeginTime            int64    `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeddingRecordByTimeRangeReq) Reset()         { *m = GetWeddingRecordByTimeRangeReq{} }
func (m *GetWeddingRecordByTimeRangeReq) String() string { return proto.CompactTextString(m) }
func (*GetWeddingRecordByTimeRangeReq) ProtoMessage()    {}
func (*GetWeddingRecordByTimeRangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{62}
}
func (m *GetWeddingRecordByTimeRangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingRecordByTimeRangeReq.Unmarshal(m, b)
}
func (m *GetWeddingRecordByTimeRangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingRecordByTimeRangeReq.Marshal(b, m, deterministic)
}
func (dst *GetWeddingRecordByTimeRangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingRecordByTimeRangeReq.Merge(dst, src)
}
func (m *GetWeddingRecordByTimeRangeReq) XXX_Size() int {
	return xxx_messageInfo_GetWeddingRecordByTimeRangeReq.Size(m)
}
func (m *GetWeddingRecordByTimeRangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingRecordByTimeRangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingRecordByTimeRangeReq proto.InternalMessageInfo

func (m *GetWeddingRecordByTimeRangeReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetWeddingRecordByTimeRangeReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetWeddingRecordByTimeRangeResp struct {
	CertificateList      []*WeddingCertificate `protobuf:"bytes,1,rep,name=certificate_list,json=certificateList,proto3" json:"certificate_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetWeddingRecordByTimeRangeResp) Reset()         { *m = GetWeddingRecordByTimeRangeResp{} }
func (m *GetWeddingRecordByTimeRangeResp) String() string { return proto.CompactTextString(m) }
func (*GetWeddingRecordByTimeRangeResp) ProtoMessage()    {}
func (*GetWeddingRecordByTimeRangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{63}
}
func (m *GetWeddingRecordByTimeRangeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingRecordByTimeRangeResp.Unmarshal(m, b)
}
func (m *GetWeddingRecordByTimeRangeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingRecordByTimeRangeResp.Marshal(b, m, deterministic)
}
func (dst *GetWeddingRecordByTimeRangeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingRecordByTimeRangeResp.Merge(dst, src)
}
func (m *GetWeddingRecordByTimeRangeResp) XXX_Size() int {
	return xxx_messageInfo_GetWeddingRecordByTimeRangeResp.Size(m)
}
func (m *GetWeddingRecordByTimeRangeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingRecordByTimeRangeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingRecordByTimeRangeResp proto.InternalMessageInfo

func (m *GetWeddingRecordByTimeRangeResp) GetCertificateList() []*WeddingCertificate {
	if m != nil {
		return m.CertificateList
	}
	return nil
}

type TestWeddingScenePicImReq struct {
	Scene                uint32   `protobuf:"varint,1,opt,name=scene,proto3" json:"scene,omitempty"`
	PicUrlList           []string `protobuf:"bytes,2,rep,name=pic_url_list,json=picUrlList,proto3" json:"pic_url_list,omitempty"`
	FromUid              uint32   `protobuf:"varint,3,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,4,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	WeddingThemeId       uint32   `protobuf:"varint,5,opt,name=wedding_theme_id,json=weddingThemeId,proto3" json:"wedding_theme_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestWeddingScenePicImReq) Reset()         { *m = TestWeddingScenePicImReq{} }
func (m *TestWeddingScenePicImReq) String() string { return proto.CompactTextString(m) }
func (*TestWeddingScenePicImReq) ProtoMessage()    {}
func (*TestWeddingScenePicImReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{64}
}
func (m *TestWeddingScenePicImReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestWeddingScenePicImReq.Unmarshal(m, b)
}
func (m *TestWeddingScenePicImReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestWeddingScenePicImReq.Marshal(b, m, deterministic)
}
func (dst *TestWeddingScenePicImReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestWeddingScenePicImReq.Merge(dst, src)
}
func (m *TestWeddingScenePicImReq) XXX_Size() int {
	return xxx_messageInfo_TestWeddingScenePicImReq.Size(m)
}
func (m *TestWeddingScenePicImReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestWeddingScenePicImReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestWeddingScenePicImReq proto.InternalMessageInfo

func (m *TestWeddingScenePicImReq) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *TestWeddingScenePicImReq) GetPicUrlList() []string {
	if m != nil {
		return m.PicUrlList
	}
	return nil
}

func (m *TestWeddingScenePicImReq) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *TestWeddingScenePicImReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *TestWeddingScenePicImReq) GetWeddingThemeId() uint32 {
	if m != nil {
		return m.WeddingThemeId
	}
	return 0
}

type TestWeddingScenePicImResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestWeddingScenePicImResp) Reset()         { *m = TestWeddingScenePicImResp{} }
func (m *TestWeddingScenePicImResp) String() string { return proto.CompactTextString(m) }
func (*TestWeddingScenePicImResp) ProtoMessage()    {}
func (*TestWeddingScenePicImResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{65}
}
func (m *TestWeddingScenePicImResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestWeddingScenePicImResp.Unmarshal(m, b)
}
func (m *TestWeddingScenePicImResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestWeddingScenePicImResp.Marshal(b, m, deterministic)
}
func (dst *TestWeddingScenePicImResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestWeddingScenePicImResp.Merge(dst, src)
}
func (m *TestWeddingScenePicImResp) XXX_Size() int {
	return xxx_messageInfo_TestWeddingScenePicImResp.Size(m)
}
func (m *TestWeddingScenePicImResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestWeddingScenePicImResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestWeddingScenePicImResp proto.InternalMessageInfo

type GetWeddingHighLightPresentRequest struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	WeddingId            uint32   `protobuf:"varint,2,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeddingHighLightPresentRequest) Reset()         { *m = GetWeddingHighLightPresentRequest{} }
func (m *GetWeddingHighLightPresentRequest) String() string { return proto.CompactTextString(m) }
func (*GetWeddingHighLightPresentRequest) ProtoMessage()    {}
func (*GetWeddingHighLightPresentRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{66}
}
func (m *GetWeddingHighLightPresentRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingHighLightPresentRequest.Unmarshal(m, b)
}
func (m *GetWeddingHighLightPresentRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingHighLightPresentRequest.Marshal(b, m, deterministic)
}
func (dst *GetWeddingHighLightPresentRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingHighLightPresentRequest.Merge(dst, src)
}
func (m *GetWeddingHighLightPresentRequest) XXX_Size() int {
	return xxx_messageInfo_GetWeddingHighLightPresentRequest.Size(m)
}
func (m *GetWeddingHighLightPresentRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingHighLightPresentRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingHighLightPresentRequest proto.InternalMessageInfo

func (m *GetWeddingHighLightPresentRequest) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GetWeddingHighLightPresentRequest) GetWeddingId() uint32 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

type GetWeddingHighLightPresentResponse struct {
	Toast                string   `protobuf:"bytes,1,opt,name=toast,proto3" json:"toast,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeddingHighLightPresentResponse) Reset()         { *m = GetWeddingHighLightPresentResponse{} }
func (m *GetWeddingHighLightPresentResponse) String() string { return proto.CompactTextString(m) }
func (*GetWeddingHighLightPresentResponse) ProtoMessage()    {}
func (*GetWeddingHighLightPresentResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{67}
}
func (m *GetWeddingHighLightPresentResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeddingHighLightPresentResponse.Unmarshal(m, b)
}
func (m *GetWeddingHighLightPresentResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeddingHighLightPresentResponse.Marshal(b, m, deterministic)
}
func (dst *GetWeddingHighLightPresentResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeddingHighLightPresentResponse.Merge(dst, src)
}
func (m *GetWeddingHighLightPresentResponse) XXX_Size() int {
	return xxx_messageInfo_GetWeddingHighLightPresentResponse.Size(m)
}
func (m *GetWeddingHighLightPresentResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeddingHighLightPresentResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeddingHighLightPresentResponse proto.InternalMessageInfo

func (m *GetWeddingHighLightPresentResponse) GetToast() string {
	if m != nil {
		return m.Toast
	}
	return ""
}

type BatchGetWeddingHappinessRequest struct {
	PlanIdList           []uint32 `protobuf:"varint,1,rep,packed,name=plan_id_list,json=planIdList,proto3" json:"plan_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetWeddingHappinessRequest) Reset()         { *m = BatchGetWeddingHappinessRequest{} }
func (m *BatchGetWeddingHappinessRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetWeddingHappinessRequest) ProtoMessage()    {}
func (*BatchGetWeddingHappinessRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{68}
}
func (m *BatchGetWeddingHappinessRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetWeddingHappinessRequest.Unmarshal(m, b)
}
func (m *BatchGetWeddingHappinessRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetWeddingHappinessRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetWeddingHappinessRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetWeddingHappinessRequest.Merge(dst, src)
}
func (m *BatchGetWeddingHappinessRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetWeddingHappinessRequest.Size(m)
}
func (m *BatchGetWeddingHappinessRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetWeddingHappinessRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetWeddingHappinessRequest proto.InternalMessageInfo

func (m *BatchGetWeddingHappinessRequest) GetPlanIdList() []uint32 {
	if m != nil {
		return m.PlanIdList
	}
	return nil
}

type BatchGetWeddingHappinessResponse struct {
	Happiness            map[uint32]uint32 `protobuf:"bytes,1,rep,name=happiness,proto3" json:"happiness,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetWeddingHappinessResponse) Reset()         { *m = BatchGetWeddingHappinessResponse{} }
func (m *BatchGetWeddingHappinessResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetWeddingHappinessResponse) ProtoMessage()    {}
func (*BatchGetWeddingHappinessResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{69}
}
func (m *BatchGetWeddingHappinessResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetWeddingHappinessResponse.Unmarshal(m, b)
}
func (m *BatchGetWeddingHappinessResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetWeddingHappinessResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetWeddingHappinessResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetWeddingHappinessResponse.Merge(dst, src)
}
func (m *BatchGetWeddingHappinessResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetWeddingHappinessResponse.Size(m)
}
func (m *BatchGetWeddingHappinessResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetWeddingHappinessResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetWeddingHappinessResponse proto.InternalMessageInfo

func (m *BatchGetWeddingHappinessResponse) GetHappiness() map[uint32]uint32 {
	if m != nil {
		return m.Happiness
	}
	return nil
}

type GetChannelWeddingRankInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelWeddingRankInfoReq) Reset()         { *m = GetChannelWeddingRankInfoReq{} }
func (m *GetChannelWeddingRankInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelWeddingRankInfoReq) ProtoMessage()    {}
func (*GetChannelWeddingRankInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{70}
}
func (m *GetChannelWeddingRankInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelWeddingRankInfoReq.Unmarshal(m, b)
}
func (m *GetChannelWeddingRankInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelWeddingRankInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelWeddingRankInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelWeddingRankInfoReq.Merge(dst, src)
}
func (m *GetChannelWeddingRankInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelWeddingRankInfoReq.Size(m)
}
func (m *GetChannelWeddingRankInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelWeddingRankInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelWeddingRankInfoReq proto.InternalMessageInfo

func (m *GetChannelWeddingRankInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelWeddingRankInfoResp struct {
	RankList             []*WeddingRankInfo `protobuf:"bytes,1,rep,name=rank_list,json=rankList,proto3" json:"rank_list,omitempty"`
	RankDesc             string             `protobuf:"bytes,2,opt,name=rank_desc,json=rankDesc,proto3" json:"rank_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetChannelWeddingRankInfoResp) Reset()         { *m = GetChannelWeddingRankInfoResp{} }
func (m *GetChannelWeddingRankInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelWeddingRankInfoResp) ProtoMessage()    {}
func (*GetChannelWeddingRankInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{71}
}
func (m *GetChannelWeddingRankInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelWeddingRankInfoResp.Unmarshal(m, b)
}
func (m *GetChannelWeddingRankInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelWeddingRankInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelWeddingRankInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelWeddingRankInfoResp.Merge(dst, src)
}
func (m *GetChannelWeddingRankInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelWeddingRankInfoResp.Size(m)
}
func (m *GetChannelWeddingRankInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelWeddingRankInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelWeddingRankInfoResp proto.InternalMessageInfo

func (m *GetChannelWeddingRankInfoResp) GetRankList() []*WeddingRankInfo {
	if m != nil {
		return m.RankList
	}
	return nil
}

func (m *GetChannelWeddingRankInfoResp) GetRankDesc() string {
	if m != nil {
		return m.RankDesc
	}
	return ""
}

type WeddingRankList struct {
	RankList             []*WeddingRankInfo `protobuf:"bytes,1,rep,name=rank_list,json=rankList,proto3" json:"rank_list,omitempty"`
	NearNDay             uint32             `protobuf:"varint,2,opt,name=near_n_day,json=nearNDay,proto3" json:"near_n_day,omitempty"`
	Limit                uint32             `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	MinScore             uint32             `protobuf:"varint,4,opt,name=min_score,json=minScore,proto3" json:"min_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *WeddingRankList) Reset()         { *m = WeddingRankList{} }
func (m *WeddingRankList) String() string { return proto.CompactTextString(m) }
func (*WeddingRankList) ProtoMessage()    {}
func (*WeddingRankList) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{72}
}
func (m *WeddingRankList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingRankList.Unmarshal(m, b)
}
func (m *WeddingRankList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingRankList.Marshal(b, m, deterministic)
}
func (dst *WeddingRankList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingRankList.Merge(dst, src)
}
func (m *WeddingRankList) XXX_Size() int {
	return xxx_messageInfo_WeddingRankList.Size(m)
}
func (m *WeddingRankList) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingRankList.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingRankList proto.InternalMessageInfo

func (m *WeddingRankList) GetRankList() []*WeddingRankInfo {
	if m != nil {
		return m.RankList
	}
	return nil
}

func (m *WeddingRankList) GetNearNDay() uint32 {
	if m != nil {
		return m.NearNDay
	}
	return 0
}

func (m *WeddingRankList) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *WeddingRankList) GetMinScore() uint32 {
	if m != nil {
		return m.MinScore
	}
	return 0
}

type WeddingRankInfo struct {
	BrideUid             uint32   `protobuf:"varint,1,opt,name=bride_uid,json=brideUid,proto3" json:"bride_uid,omitempty"`
	GroomUid             uint32   `protobuf:"varint,2,opt,name=groom_uid,json=groomUid,proto3" json:"groom_uid,omitempty"`
	HappinessVal         uint32   `protobuf:"varint,3,opt,name=happiness_val,json=happinessVal,proto3" json:"happiness_val,omitempty"`
	ThemeId              uint32   `protobuf:"varint,4,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	ThemeName            string   `protobuf:"bytes,5,opt,name=theme_name,json=themeName,proto3" json:"theme_name,omitempty"`
	WeddingBgUrl         string   `protobuf:"bytes,6,opt,name=wedding_bg_url,json=weddingBgUrl,proto3" json:"wedding_bg_url,omitempty"`
	BrideAccount         string   `protobuf:"bytes,7,opt,name=bride_account,json=brideAccount,proto3" json:"bride_account,omitempty"`
	GroomAccount         string   `protobuf:"bytes,8,opt,name=groom_account,json=groomAccount,proto3" json:"groom_account,omitempty"`
	WeddingLv            uint32   `protobuf:"varint,9,opt,name=wedding_lv,json=weddingLv,proto3" json:"wedding_lv,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingRankInfo) Reset()         { *m = WeddingRankInfo{} }
func (m *WeddingRankInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingRankInfo) ProtoMessage()    {}
func (*WeddingRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{73}
}
func (m *WeddingRankInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingRankInfo.Unmarshal(m, b)
}
func (m *WeddingRankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingRankInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingRankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingRankInfo.Merge(dst, src)
}
func (m *WeddingRankInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingRankInfo.Size(m)
}
func (m *WeddingRankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingRankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingRankInfo proto.InternalMessageInfo

func (m *WeddingRankInfo) GetBrideUid() uint32 {
	if m != nil {
		return m.BrideUid
	}
	return 0
}

func (m *WeddingRankInfo) GetGroomUid() uint32 {
	if m != nil {
		return m.GroomUid
	}
	return 0
}

func (m *WeddingRankInfo) GetHappinessVal() uint32 {
	if m != nil {
		return m.HappinessVal
	}
	return 0
}

func (m *WeddingRankInfo) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

func (m *WeddingRankInfo) GetThemeName() string {
	if m != nil {
		return m.ThemeName
	}
	return ""
}

func (m *WeddingRankInfo) GetWeddingBgUrl() string {
	if m != nil {
		return m.WeddingBgUrl
	}
	return ""
}

func (m *WeddingRankInfo) GetBrideAccount() string {
	if m != nil {
		return m.BrideAccount
	}
	return ""
}

func (m *WeddingRankInfo) GetGroomAccount() string {
	if m != nil {
		return m.GroomAccount
	}
	return ""
}

func (m *WeddingRankInfo) GetWeddingLv() uint32 {
	if m != nil {
		return m.WeddingLv
	}
	return 0
}

type WeddingRankFirstPlaceInfo struct {
	Info                 *WeddingRankInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	NearNDay             uint32           `protobuf:"varint,2,opt,name=near_n_day,json=nearNDay,proto3" json:"near_n_day,omitempty"`
	MinScore             uint32           `protobuf:"varint,3,opt,name=min_score,json=minScore,proto3" json:"min_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *WeddingRankFirstPlaceInfo) Reset()         { *m = WeddingRankFirstPlaceInfo{} }
func (m *WeddingRankFirstPlaceInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingRankFirstPlaceInfo) ProtoMessage()    {}
func (*WeddingRankFirstPlaceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{74}
}
func (m *WeddingRankFirstPlaceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingRankFirstPlaceInfo.Unmarshal(m, b)
}
func (m *WeddingRankFirstPlaceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingRankFirstPlaceInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingRankFirstPlaceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingRankFirstPlaceInfo.Merge(dst, src)
}
func (m *WeddingRankFirstPlaceInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingRankFirstPlaceInfo.Size(m)
}
func (m *WeddingRankFirstPlaceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingRankFirstPlaceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingRankFirstPlaceInfo proto.InternalMessageInfo

func (m *WeddingRankFirstPlaceInfo) GetInfo() *WeddingRankInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *WeddingRankFirstPlaceInfo) GetNearNDay() uint32 {
	if m != nil {
		return m.NearNDay
	}
	return 0
}

func (m *WeddingRankFirstPlaceInfo) GetMinScore() uint32 {
	if m != nil {
		return m.MinScore
	}
	return 0
}

type GetChannelWeddingRankEntryInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelWeddingRankEntryInfoReq) Reset()         { *m = GetChannelWeddingRankEntryInfoReq{} }
func (m *GetChannelWeddingRankEntryInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelWeddingRankEntryInfoReq) ProtoMessage()    {}
func (*GetChannelWeddingRankEntryInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{75}
}
func (m *GetChannelWeddingRankEntryInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelWeddingRankEntryInfoReq.Unmarshal(m, b)
}
func (m *GetChannelWeddingRankEntryInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelWeddingRankEntryInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelWeddingRankEntryInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelWeddingRankEntryInfoReq.Merge(dst, src)
}
func (m *GetChannelWeddingRankEntryInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelWeddingRankEntryInfoReq.Size(m)
}
func (m *GetChannelWeddingRankEntryInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelWeddingRankEntryInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelWeddingRankEntryInfoReq proto.InternalMessageInfo

func (m *GetChannelWeddingRankEntryInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelWeddingRankEntryInfoResp struct {
	IsShow bool `protobuf:"varint,1,opt,name=is_show,json=isShow,proto3" json:"is_show,omitempty"`
	// 榜单榜首账号信息
	RankInfo             *WeddingRankInfo `protobuf:"bytes,2,opt,name=rank_info,json=rankInfo,proto3" json:"rank_info,omitempty"`
	H5Url                string           `protobuf:"bytes,3,opt,name=h5_url,json=h5Url,proto3" json:"h5_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetChannelWeddingRankEntryInfoResp) Reset()         { *m = GetChannelWeddingRankEntryInfoResp{} }
func (m *GetChannelWeddingRankEntryInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelWeddingRankEntryInfoResp) ProtoMessage()    {}
func (*GetChannelWeddingRankEntryInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{76}
}
func (m *GetChannelWeddingRankEntryInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelWeddingRankEntryInfoResp.Unmarshal(m, b)
}
func (m *GetChannelWeddingRankEntryInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelWeddingRankEntryInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelWeddingRankEntryInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelWeddingRankEntryInfoResp.Merge(dst, src)
}
func (m *GetChannelWeddingRankEntryInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelWeddingRankEntryInfoResp.Size(m)
}
func (m *GetChannelWeddingRankEntryInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelWeddingRankEntryInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelWeddingRankEntryInfoResp proto.InternalMessageInfo

func (m *GetChannelWeddingRankEntryInfoResp) GetIsShow() bool {
	if m != nil {
		return m.IsShow
	}
	return false
}

func (m *GetChannelWeddingRankEntryInfoResp) GetRankInfo() *WeddingRankInfo {
	if m != nil {
		return m.RankInfo
	}
	return nil
}

func (m *GetChannelWeddingRankEntryInfoResp) GetH5Url() string {
	if m != nil {
		return m.H5Url
	}
	return ""
}

type TestAfterFreeWeddingImXmlReq struct {
	BrideUid             uint32   `protobuf:"varint,1,opt,name=bride_uid,json=brideUid,proto3" json:"bride_uid,omitempty"`
	GroomUid             uint32   `protobuf:"varint,2,opt,name=groom_uid,json=groomUid,proto3" json:"groom_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestAfterFreeWeddingImXmlReq) Reset()         { *m = TestAfterFreeWeddingImXmlReq{} }
func (m *TestAfterFreeWeddingImXmlReq) String() string { return proto.CompactTextString(m) }
func (*TestAfterFreeWeddingImXmlReq) ProtoMessage()    {}
func (*TestAfterFreeWeddingImXmlReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{77}
}
func (m *TestAfterFreeWeddingImXmlReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestAfterFreeWeddingImXmlReq.Unmarshal(m, b)
}
func (m *TestAfterFreeWeddingImXmlReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestAfterFreeWeddingImXmlReq.Marshal(b, m, deterministic)
}
func (dst *TestAfterFreeWeddingImXmlReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestAfterFreeWeddingImXmlReq.Merge(dst, src)
}
func (m *TestAfterFreeWeddingImXmlReq) XXX_Size() int {
	return xxx_messageInfo_TestAfterFreeWeddingImXmlReq.Size(m)
}
func (m *TestAfterFreeWeddingImXmlReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestAfterFreeWeddingImXmlReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestAfterFreeWeddingImXmlReq proto.InternalMessageInfo

func (m *TestAfterFreeWeddingImXmlReq) GetBrideUid() uint32 {
	if m != nil {
		return m.BrideUid
	}
	return 0
}

func (m *TestAfterFreeWeddingImXmlReq) GetGroomUid() uint32 {
	if m != nil {
		return m.GroomUid
	}
	return 0
}

type TestAfterFreeWeddingImXmlResp struct {
	Xml                  string   `protobuf:"bytes,1,opt,name=xml,proto3" json:"xml,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestAfterFreeWeddingImXmlResp) Reset()         { *m = TestAfterFreeWeddingImXmlResp{} }
func (m *TestAfterFreeWeddingImXmlResp) String() string { return proto.CompactTextString(m) }
func (*TestAfterFreeWeddingImXmlResp) ProtoMessage()    {}
func (*TestAfterFreeWeddingImXmlResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{78}
}
func (m *TestAfterFreeWeddingImXmlResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestAfterFreeWeddingImXmlResp.Unmarshal(m, b)
}
func (m *TestAfterFreeWeddingImXmlResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestAfterFreeWeddingImXmlResp.Marshal(b, m, deterministic)
}
func (dst *TestAfterFreeWeddingImXmlResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestAfterFreeWeddingImXmlResp.Merge(dst, src)
}
func (m *TestAfterFreeWeddingImXmlResp) XXX_Size() int {
	return xxx_messageInfo_TestAfterFreeWeddingImXmlResp.Size(m)
}
func (m *TestAfterFreeWeddingImXmlResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestAfterFreeWeddingImXmlResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestAfterFreeWeddingImXmlResp proto.InternalMessageInfo

func (m *TestAfterFreeWeddingImXmlResp) GetXml() string {
	if m != nil {
		return m.Xml
	}
	return ""
}

type SendWeddingReservePresentReq struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	WeddingId            uint32   `protobuf:"varint,2,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendWeddingReservePresentReq) Reset()         { *m = SendWeddingReservePresentReq{} }
func (m *SendWeddingReservePresentReq) String() string { return proto.CompactTextString(m) }
func (*SendWeddingReservePresentReq) ProtoMessage()    {}
func (*SendWeddingReservePresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{79}
}
func (m *SendWeddingReservePresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendWeddingReservePresentReq.Unmarshal(m, b)
}
func (m *SendWeddingReservePresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendWeddingReservePresentReq.Marshal(b, m, deterministic)
}
func (dst *SendWeddingReservePresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendWeddingReservePresentReq.Merge(dst, src)
}
func (m *SendWeddingReservePresentReq) XXX_Size() int {
	return xxx_messageInfo_SendWeddingReservePresentReq.Size(m)
}
func (m *SendWeddingReservePresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendWeddingReservePresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendWeddingReservePresentReq proto.InternalMessageInfo

func (m *SendWeddingReservePresentReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SendWeddingReservePresentReq) GetWeddingId() uint32 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

func (m *SendWeddingReservePresentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SendWeddingReservePresentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendWeddingReservePresentResp) Reset()         { *m = SendWeddingReservePresentResp{} }
func (m *SendWeddingReservePresentResp) String() string { return proto.CompactTextString(m) }
func (*SendWeddingReservePresentResp) ProtoMessage()    {}
func (*SendWeddingReservePresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{80}
}
func (m *SendWeddingReservePresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendWeddingReservePresentResp.Unmarshal(m, b)
}
func (m *SendWeddingReservePresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendWeddingReservePresentResp.Marshal(b, m, deterministic)
}
func (dst *SendWeddingReservePresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendWeddingReservePresentResp.Merge(dst, src)
}
func (m *SendWeddingReservePresentResp) XXX_Size() int {
	return xxx_messageInfo_SendWeddingReservePresentResp.Size(m)
}
func (m *SendWeddingReservePresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendWeddingReservePresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendWeddingReservePresentResp proto.InternalMessageInfo

// 婚礼收送礼值
type WeddingPresentVal struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PresentVal           uint32   `protobuf:"varint,2,opt,name=present_val,json=presentVal,proto3" json:"present_val,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingPresentVal) Reset()         { *m = WeddingPresentVal{} }
func (m *WeddingPresentVal) String() string { return proto.CompactTextString(m) }
func (*WeddingPresentVal) ProtoMessage()    {}
func (*WeddingPresentVal) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{81}
}
func (m *WeddingPresentVal) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPresentVal.Unmarshal(m, b)
}
func (m *WeddingPresentVal) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPresentVal.Marshal(b, m, deterministic)
}
func (dst *WeddingPresentVal) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPresentVal.Merge(dst, src)
}
func (m *WeddingPresentVal) XXX_Size() int {
	return xxx_messageInfo_WeddingPresentVal.Size(m)
}
func (m *WeddingPresentVal) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPresentVal.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPresentVal proto.InternalMessageInfo

func (m *WeddingPresentVal) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *WeddingPresentVal) GetPresentVal() uint32 {
	if m != nil {
		return m.PresentVal
	}
	return 0
}

type WeddingPresentLevel struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Lv                   uint32   `protobuf:"varint,2,opt,name=lv,proto3" json:"lv,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeddingPresentLevel) Reset()         { *m = WeddingPresentLevel{} }
func (m *WeddingPresentLevel) String() string { return proto.CompactTextString(m) }
func (*WeddingPresentLevel) ProtoMessage()    {}
func (*WeddingPresentLevel) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{82}
}
func (m *WeddingPresentLevel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPresentLevel.Unmarshal(m, b)
}
func (m *WeddingPresentLevel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPresentLevel.Marshal(b, m, deterministic)
}
func (dst *WeddingPresentLevel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPresentLevel.Merge(dst, src)
}
func (m *WeddingPresentLevel) XXX_Size() int {
	return xxx_messageInfo_WeddingPresentLevel.Size(m)
}
func (m *WeddingPresentLevel) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPresentLevel.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPresentLevel proto.InternalMessageInfo

func (m *WeddingPresentLevel) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *WeddingPresentLevel) GetLv() uint32 {
	if m != nil {
		return m.Lv
	}
	return 0
}

// 婚礼收送礼物值信息(计数器)
type WeddingPresentCountInfo struct {
	UserPresentValList   []*WeddingPresentVal `protobuf:"bytes,1,rep,name=user_present_val_list,json=userPresentValList,proto3" json:"user_present_val_list,omitempty"`
	MvpUid               uint32               `protobuf:"varint,2,opt,name=mvp_uid,json=mvpUid,proto3" json:"mvp_uid,omitempty"`
	TopRecvPresentLv     *WeddingPresentLevel `protobuf:"bytes,3,opt,name=top_recv_present_lv,json=topRecvPresentLv,proto3" json:"top_recv_present_lv,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *WeddingPresentCountInfo) Reset()         { *m = WeddingPresentCountInfo{} }
func (m *WeddingPresentCountInfo) String() string { return proto.CompactTextString(m) }
func (*WeddingPresentCountInfo) ProtoMessage()    {}
func (*WeddingPresentCountInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{83}
}
func (m *WeddingPresentCountInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeddingPresentCountInfo.Unmarshal(m, b)
}
func (m *WeddingPresentCountInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeddingPresentCountInfo.Marshal(b, m, deterministic)
}
func (dst *WeddingPresentCountInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeddingPresentCountInfo.Merge(dst, src)
}
func (m *WeddingPresentCountInfo) XXX_Size() int {
	return xxx_messageInfo_WeddingPresentCountInfo.Size(m)
}
func (m *WeddingPresentCountInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeddingPresentCountInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeddingPresentCountInfo proto.InternalMessageInfo

func (m *WeddingPresentCountInfo) GetUserPresentValList() []*WeddingPresentVal {
	if m != nil {
		return m.UserPresentValList
	}
	return nil
}

func (m *WeddingPresentCountInfo) GetMvpUid() uint32 {
	if m != nil {
		return m.MvpUid
	}
	return 0
}

func (m *WeddingPresentCountInfo) GetTopRecvPresentLv() *WeddingPresentLevel {
	if m != nil {
		return m.TopRecvPresentLv
	}
	return nil
}

// 测试婚礼mvp结算请求
type TestMvpSettlementReq struct {
	WeddingId            uint32   `protobuf:"varint,1,opt,name=wedding_id,json=weddingId,proto3" json:"wedding_id,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	MvpUid               uint32   `protobuf:"varint,3,opt,name=mvp_uid,json=mvpUid,proto3" json:"mvp_uid,omitempty"`
	ThemeId              uint32   `protobuf:"varint,4,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestMvpSettlementReq) Reset()         { *m = TestMvpSettlementReq{} }
func (m *TestMvpSettlementReq) String() string { return proto.CompactTextString(m) }
func (*TestMvpSettlementReq) ProtoMessage()    {}
func (*TestMvpSettlementReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{84}
}
func (m *TestMvpSettlementReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestMvpSettlementReq.Unmarshal(m, b)
}
func (m *TestMvpSettlementReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestMvpSettlementReq.Marshal(b, m, deterministic)
}
func (dst *TestMvpSettlementReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestMvpSettlementReq.Merge(dst, src)
}
func (m *TestMvpSettlementReq) XXX_Size() int {
	return xxx_messageInfo_TestMvpSettlementReq.Size(m)
}
func (m *TestMvpSettlementReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestMvpSettlementReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestMvpSettlementReq proto.InternalMessageInfo

func (m *TestMvpSettlementReq) GetWeddingId() uint32 {
	if m != nil {
		return m.WeddingId
	}
	return 0
}

func (m *TestMvpSettlementReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *TestMvpSettlementReq) GetMvpUid() uint32 {
	if m != nil {
		return m.MvpUid
	}
	return 0
}

func (m *TestMvpSettlementReq) GetThemeId() uint32 {
	if m != nil {
		return m.ThemeId
	}
	return 0
}

type TestMvpSettlementResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestMvpSettlementResp) Reset()         { *m = TestMvpSettlementResp{} }
func (m *TestMvpSettlementResp) String() string { return proto.CompactTextString(m) }
func (*TestMvpSettlementResp) ProtoMessage()    {}
func (*TestMvpSettlementResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_wedding_1641b6bc240a13b9, []int{85}
}
func (m *TestMvpSettlementResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestMvpSettlementResp.Unmarshal(m, b)
}
func (m *TestMvpSettlementResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestMvpSettlementResp.Marshal(b, m, deterministic)
}
func (dst *TestMvpSettlementResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestMvpSettlementResp.Merge(dst, src)
}
func (m *TestMvpSettlementResp) XXX_Size() int {
	return xxx_messageInfo_TestMvpSettlementResp.Size(m)
}
func (m *TestMvpSettlementResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestMvpSettlementResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestMvpSettlementResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*WeddingStageCfg)(nil), "channel_wedding.WeddingStageCfg")
	proto.RegisterType((*WeddingStageInfo)(nil), "channel_wedding.WeddingStageInfo")
	proto.RegisterType((*WeddingLevelInfo)(nil), "channel_wedding.WeddingLevelInfo")
	proto.RegisterType((*WeddingSceneBoneCfg)(nil), "channel_wedding.WeddingSceneBoneCfg")
	proto.RegisterType((*WeddingSceneCfg)(nil), "channel_wedding.WeddingSceneCfg")
	proto.RegisterType((*WeddingLevelClothes)(nil), "channel_wedding.WeddingLevelClothes")
	proto.RegisterType((*WeddingLevelBackgroundCfg)(nil), "channel_wedding.WeddingLevelBackgroundCfg")
	proto.RegisterType((*WeddingResource)(nil), "channel_wedding.WeddingResource")
	proto.RegisterType((*WeddingRoomThemeCfg)(nil), "channel_wedding.WeddingRoomThemeCfg")
	proto.RegisterType((*ChairGameResourceCfg)(nil), "channel_wedding.ChairGameResourceCfg")
	proto.RegisterType((*WeddingCpMemInfo)(nil), "channel_wedding.WeddingCpMemInfo")
	proto.RegisterType((*WeddingMemorialVideo)(nil), "channel_wedding.WeddingMemorialVideo")
	proto.RegisterType((*WeddingBoneCfg)(nil), "channel_wedding.WeddingBoneCfg")
	proto.RegisterType((*HappinessLevelInfo)(nil), "channel_wedding.HappinessLevelInfo")
	proto.RegisterType((*HappinessConfigInfo)(nil), "channel_wedding.HappinessConfigInfo")
	proto.RegisterType((*WeddingInfo)(nil), "channel_wedding.WeddingInfo")
	proto.RegisterType((*GetChannelWeddingInfoReq)(nil), "channel_wedding.GetChannelWeddingInfoReq")
	proto.RegisterType((*GetChannelWeddingInfoResp)(nil), "channel_wedding.GetChannelWeddingInfoResp")
	proto.RegisterType((*GetUserChannelWeddingInfoReq)(nil), "channel_wedding.GetUserChannelWeddingInfoReq")
	proto.RegisterType((*GetUserChannelWeddingInfoResp)(nil), "channel_wedding.GetUserChannelWeddingInfoResp")
	proto.RegisterType((*SimpleWeddingInfo)(nil), "channel_wedding.SimpleWeddingInfo")
	proto.RegisterType((*BatchGetChannelWeddingSimpleInfoReq)(nil), "channel_wedding.BatchGetChannelWeddingSimpleInfoReq")
	proto.RegisterType((*BatchGetChannelWeddingSimpleInfoResp)(nil), "channel_wedding.BatchGetChannelWeddingSimpleInfoResp")
	proto.RegisterType((*ReserveWeddingReq)(nil), "channel_wedding.ReserveWeddingReq")
	proto.RegisterType((*ReserveWeddingResp)(nil), "channel_wedding.ReserveWeddingResp")
	proto.RegisterType((*ReportWeddingBridesmaidManReq)(nil), "channel_wedding.ReportWeddingBridesmaidManReq")
	proto.RegisterType((*ReportWeddingBridesmaidManResp)(nil), "channel_wedding.ReportWeddingBridesmaidManResp")
	proto.RegisterType((*SwitchWeddingStageReq)(nil), "channel_wedding.SwitchWeddingStageReq")
	proto.RegisterType((*SwitchWeddingStageResp)(nil), "channel_wedding.SwitchWeddingStageResp")
	proto.RegisterType((*TakeWeddingGroupPhotoReq)(nil), "channel_wedding.TakeWeddingGroupPhotoReq")
	proto.RegisterType((*TakeWeddingGroupPhotoResp)(nil), "channel_wedding.TakeWeddingGroupPhotoResp")
	proto.RegisterType((*GetUserWeddingPoseListReq)(nil), "channel_wedding.GetUserWeddingPoseListReq")
	proto.RegisterType((*GetUserWeddingPoseListResp)(nil), "channel_wedding.GetUserWeddingPoseListResp")
	proto.RegisterType((*SetUserWeddingPoseReq)(nil), "channel_wedding.SetUserWeddingPoseReq")
	proto.RegisterType((*SetUserWeddingPoseResp)(nil), "channel_wedding.SetUserWeddingPoseResp")
	proto.RegisterType((*SetUserWeddingOrientationReq)(nil), "channel_wedding.SetUserWeddingOrientationReq")
	proto.RegisterType((*SetUserWeddingOrientationResp)(nil), "channel_wedding.SetUserWeddingOrientationResp")
	proto.RegisterType((*UserWeddingPose)(nil), "channel_wedding.UserWeddingPose")
	proto.RegisterType((*BatchGetUserWeddingPoseReq)(nil), "channel_wedding.BatchGetUserWeddingPoseReq")
	proto.RegisterType((*BatchGetUserWeddingPoseResp)(nil), "channel_wedding.BatchGetUserWeddingPoseResp")
	proto.RegisterType((*GetWeddingGroupPhotoSeatListReq)(nil), "channel_wedding.GetWeddingGroupPhotoSeatListReq")
	proto.RegisterType((*WeddingGroupPhotoSeat)(nil), "channel_wedding.WeddingGroupPhotoSeat")
	proto.RegisterType((*GetWeddingGroupPhotoSeatListResp)(nil), "channel_wedding.GetWeddingGroupPhotoSeatListResp")
	proto.RegisterType((*SetUserWeddingGroupPhotoSeatReq)(nil), "channel_wedding.SetUserWeddingGroupPhotoSeatReq")
	proto.RegisterType((*SetUserWeddingGroupPhotoSeatResp)(nil), "channel_wedding.SetUserWeddingGroupPhotoSeatResp")
	proto.RegisterType((*AddWeddingBridesmaidManReq)(nil), "channel_wedding.AddWeddingBridesmaidManReq")
	proto.RegisterType((*AddWeddingBridesmaidManResp)(nil), "channel_wedding.AddWeddingBridesmaidManResp")
	proto.RegisterType((*WeddingSchedule)(nil), "channel_wedding.WeddingSchedule")
	proto.RegisterType((*GetWeddingScheduleListReq)(nil), "channel_wedding.GetWeddingScheduleListReq")
	proto.RegisterType((*GetWeddingScheduleListResp)(nil), "channel_wedding.GetWeddingScheduleListResp")
	proto.RegisterType((*WeddingCertificate)(nil), "channel_wedding.WeddingCertificate")
	proto.RegisterType((*GetUserWeddingCertificateReq)(nil), "channel_wedding.GetUserWeddingCertificateReq")
	proto.RegisterType((*GetUserWeddingCertificateResp)(nil), "channel_wedding.GetUserWeddingCertificateResp")
	proto.RegisterType((*WeddingScenePic)(nil), "channel_wedding.WeddingScenePic")
	proto.RegisterType((*WeddingClipInfo)(nil), "channel_wedding.WeddingClipInfo")
	proto.RegisterType((*GetUserWeddingWeddingClipsReq)(nil), "channel_wedding.GetUserWeddingWeddingClipsReq")
	proto.RegisterType((*GetUserWeddingWeddingClipsResp)(nil), "channel_wedding.GetUserWeddingWeddingClipsResp")
	proto.RegisterType((*ReportWeddingScenePicReq)(nil), "channel_wedding.ReportWeddingScenePicReq")
	proto.RegisterType((*ReportWeddingScenePicResp)(nil), "channel_wedding.ReportWeddingScenePicResp")
	proto.RegisterType((*PageGetGoingWeddingListReq)(nil), "channel_wedding.PageGetGoingWeddingListReq")
	proto.RegisterType((*PageGetGoingWeddingListResp)(nil), "channel_wedding.PageGetGoingWeddingListResp")
	proto.RegisterType((*GoingWeddingInfo)(nil), "channel_wedding.GoingWeddingInfo")
	proto.RegisterType((*GetWeddingRecordByTimeRangeReq)(nil), "channel_wedding.GetWeddingRecordByTimeRangeReq")
	proto.RegisterType((*GetWeddingRecordByTimeRangeResp)(nil), "channel_wedding.GetWeddingRecordByTimeRangeResp")
	proto.RegisterType((*TestWeddingScenePicImReq)(nil), "channel_wedding.TestWeddingScenePicImReq")
	proto.RegisterType((*TestWeddingScenePicImResp)(nil), "channel_wedding.TestWeddingScenePicImResp")
	proto.RegisterType((*GetWeddingHighLightPresentRequest)(nil), "channel_wedding.GetWeddingHighLightPresentRequest")
	proto.RegisterType((*GetWeddingHighLightPresentResponse)(nil), "channel_wedding.GetWeddingHighLightPresentResponse")
	proto.RegisterType((*BatchGetWeddingHappinessRequest)(nil), "channel_wedding.BatchGetWeddingHappinessRequest")
	proto.RegisterType((*BatchGetWeddingHappinessResponse)(nil), "channel_wedding.BatchGetWeddingHappinessResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "channel_wedding.BatchGetWeddingHappinessResponse.HappinessEntry")
	proto.RegisterType((*GetChannelWeddingRankInfoReq)(nil), "channel_wedding.GetChannelWeddingRankInfoReq")
	proto.RegisterType((*GetChannelWeddingRankInfoResp)(nil), "channel_wedding.GetChannelWeddingRankInfoResp")
	proto.RegisterType((*WeddingRankList)(nil), "channel_wedding.WeddingRankList")
	proto.RegisterType((*WeddingRankInfo)(nil), "channel_wedding.WeddingRankInfo")
	proto.RegisterType((*WeddingRankFirstPlaceInfo)(nil), "channel_wedding.WeddingRankFirstPlaceInfo")
	proto.RegisterType((*GetChannelWeddingRankEntryInfoReq)(nil), "channel_wedding.GetChannelWeddingRankEntryInfoReq")
	proto.RegisterType((*GetChannelWeddingRankEntryInfoResp)(nil), "channel_wedding.GetChannelWeddingRankEntryInfoResp")
	proto.RegisterType((*TestAfterFreeWeddingImXmlReq)(nil), "channel_wedding.TestAfterFreeWeddingImXmlReq")
	proto.RegisterType((*TestAfterFreeWeddingImXmlResp)(nil), "channel_wedding.TestAfterFreeWeddingImXmlResp")
	proto.RegisterType((*SendWeddingReservePresentReq)(nil), "channel_wedding.SendWeddingReservePresentReq")
	proto.RegisterType((*SendWeddingReservePresentResp)(nil), "channel_wedding.SendWeddingReservePresentResp")
	proto.RegisterType((*WeddingPresentVal)(nil), "channel_wedding.WeddingPresentVal")
	proto.RegisterType((*WeddingPresentLevel)(nil), "channel_wedding.WeddingPresentLevel")
	proto.RegisterType((*WeddingPresentCountInfo)(nil), "channel_wedding.WeddingPresentCountInfo")
	proto.RegisterType((*TestMvpSettlementReq)(nil), "channel_wedding.TestMvpSettlementReq")
	proto.RegisterType((*TestMvpSettlementResp)(nil), "channel_wedding.TestMvpSettlementResp")
	proto.RegisterEnum("channel_wedding.WeddingStage", WeddingStage_name, WeddingStage_value)
	proto.RegisterEnum("channel_wedding.GroupPhotoSubStage", GroupPhotoSubStage_name, GroupPhotoSubStage_value)
	proto.RegisterEnum("channel_wedding.WeddingScene", WeddingScene_name, WeddingScene_value)
	proto.RegisterEnum("channel_wedding.WeddingThemeType", WeddingThemeType_name, WeddingThemeType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelWeddingClient is the client API for ChannelWedding service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelWeddingClient interface {
	// 获取房间婚礼信息
	GetChannelWeddingInfo(ctx context.Context, in *GetChannelWeddingInfoReq, opts ...grpc.CallOption) (*GetChannelWeddingInfoResp, error)
	// 获取用户正在进行的婚礼信息
	GetUserChannelWeddingInfo(ctx context.Context, in *GetUserChannelWeddingInfoReq, opts ...grpc.CallOption) (*GetUserChannelWeddingInfoResp, error)
	// 批量获取房间婚礼简略信息
	BatchGetChannelWeddingSimpleInfo(ctx context.Context, in *BatchGetChannelWeddingSimpleInfoReq, opts ...grpc.CallOption) (*BatchGetChannelWeddingSimpleInfoResp, error)
	// 预约婚礼
	ReserveWedding(ctx context.Context, in *ReserveWeddingReq, opts ...grpc.CallOption) (*ReserveWeddingResp, error)
	// 婚礼进行中上报新增伴郎伴娘
	ReportWeddingBridesmaidMan(ctx context.Context, in *ReportWeddingBridesmaidManReq, opts ...grpc.CallOption) (*ReportWeddingBridesmaidManResp, error)
	// 切换婚礼阶段
	SwitchWeddingStage(ctx context.Context, in *SwitchWeddingStageReq, opts ...grpc.CallOption) (*SwitchWeddingStageResp, error)
	// 拍合照
	TakeWeddingGroupPhoto(ctx context.Context, in *TakeWeddingGroupPhotoReq, opts ...grpc.CallOption) (*TakeWeddingGroupPhotoResp, error)
	// 获取用户可切换的姿势列表
	GetUserWeddingPoseList(ctx context.Context, in *GetUserWeddingPoseListReq, opts ...grpc.CallOption) (*GetUserWeddingPoseListResp, error)
	// 切换姿势
	SetUserWeddingPose(ctx context.Context, in *SetUserWeddingPoseReq, opts ...grpc.CallOption) (*SetUserWeddingPoseResp, error)
	// 切换朝向
	SetUserWeddingOrientation(ctx context.Context, in *SetUserWeddingOrientationReq, opts ...grpc.CallOption) (*SetUserWeddingOrientationResp, error)
	// 批量获取用户姿势
	BatchGetUserWeddingPose(ctx context.Context, in *BatchGetUserWeddingPoseReq, opts ...grpc.CallOption) (*BatchGetUserWeddingPoseResp, error)
	// 获取房间合照麦位位置
	GetWeddingGroupPhotoSeatList(ctx context.Context, in *GetWeddingGroupPhotoSeatListReq, opts ...grpc.CallOption) (*GetWeddingGroupPhotoSeatListResp, error)
	// 设置用户合照位置
	SetUserWeddingGroupPhotoSeat(ctx context.Context, in *SetUserWeddingGroupPhotoSeatReq, opts ...grpc.CallOption) (*SetUserWeddingGroupPhotoSeatResp, error)
	// 增加婚礼伴郎伴娘
	AddWeddingBridesmaidMan(ctx context.Context, in *AddWeddingBridesmaidManReq, opts ...grpc.CallOption) (*AddWeddingBridesmaidManResp, error)
	// 根据开始时间范围获取婚礼场次列表
	GetWeddingScheduleList(ctx context.Context, in *GetWeddingScheduleListReq, opts ...grpc.CallOption) (*GetWeddingScheduleListResp, error)
	// 获取用户结婚证
	GetUserWeddingCertificate(ctx context.Context, in *GetUserWeddingCertificateReq, opts ...grpc.CallOption) (*GetUserWeddingCertificateResp, error)
	// 获取用户婚礼沉淀信息
	GetUserWeddingWeddingClips(ctx context.Context, in *GetUserWeddingWeddingClipsReq, opts ...grpc.CallOption) (*GetUserWeddingWeddingClipsResp, error)
	// 上报婚礼场景片段图片
	ReportWeddingScenePic(ctx context.Context, in *ReportWeddingScenePicReq, opts ...grpc.CallOption) (*ReportWeddingScenePicResp, error)
	// 分页获取进行中的婚礼列表
	PageGetGoingWeddingList(ctx context.Context, in *PageGetGoingWeddingListReq, opts ...grpc.CallOption) (*PageGetGoingWeddingListResp, error)
	// 根据给定时间范围获取结婚证列表
	GetWeddingRecordByTimeRange(ctx context.Context, in *GetWeddingRecordByTimeRangeReq, opts ...grpc.CallOption) (*GetWeddingRecordByTimeRangeResp, error)
	// 测试婚礼场景im图片
	TestWeddingScenePicIm(ctx context.Context, in *TestWeddingScenePicImReq, opts ...grpc.CallOption) (*TestWeddingScenePicImResp, error)
	// 获取婚礼高光时刻礼物
	GetWeddingHighLightPresent(ctx context.Context, in *GetWeddingHighLightPresentRequest, opts ...grpc.CallOption) (*GetWeddingHighLightPresentResponse, error)
	BatchGetWeddingHappiness(ctx context.Context, in *BatchGetWeddingHappinessRequest, opts ...grpc.CallOption) (*BatchGetWeddingHappinessResponse, error)
	// 获取房间爱侣榜信息
	GetChannelWeddingRankInfo(ctx context.Context, in *GetChannelWeddingRankInfoReq, opts ...grpc.CallOption) (*GetChannelWeddingRankInfoResp, error)
	// 获取房间爱侣榜入口信息
	GetChannelWeddingRankEntryInfo(ctx context.Context, in *GetChannelWeddingRankEntryInfoReq, opts ...grpc.CallOption) (*GetChannelWeddingRankEntryInfoResp, error)
	// 发送预约礼物
	SendWeddingReservePresent(ctx context.Context, in *SendWeddingReservePresentReq, opts ...grpc.CallOption) (*SendWeddingReservePresentResp, error)
	// 测试免费婚礼付费引流IM
	TestAfterFreeWeddingImXml(ctx context.Context, in *TestAfterFreeWeddingImXmlReq, opts ...grpc.CallOption) (*TestAfterFreeWeddingImXmlResp, error)
	// 测试婚礼mvp结算
	TestMvpSettlement(ctx context.Context, in *TestMvpSettlementReq, opts ...grpc.CallOption) (*TestMvpSettlementResp, error)
	// ===================== 预约礼物对账 =========================
	// 婚礼房预约礼物->礼物
	ReservePresentTimeRangeCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	// 获取礼物订单
	ReservePresentTimeRangeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 补单
	FixReservePresentCountOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
}

type channelWeddingClient struct {
	cc *grpc.ClientConn
}

func NewChannelWeddingClient(cc *grpc.ClientConn) ChannelWeddingClient {
	return &channelWeddingClient{cc}
}

func (c *channelWeddingClient) GetChannelWeddingInfo(ctx context.Context, in *GetChannelWeddingInfoReq, opts ...grpc.CallOption) (*GetChannelWeddingInfoResp, error) {
	out := new(GetChannelWeddingInfoResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/GetChannelWeddingInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) GetUserChannelWeddingInfo(ctx context.Context, in *GetUserChannelWeddingInfoReq, opts ...grpc.CallOption) (*GetUserChannelWeddingInfoResp, error) {
	out := new(GetUserChannelWeddingInfoResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/GetUserChannelWeddingInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) BatchGetChannelWeddingSimpleInfo(ctx context.Context, in *BatchGetChannelWeddingSimpleInfoReq, opts ...grpc.CallOption) (*BatchGetChannelWeddingSimpleInfoResp, error) {
	out := new(BatchGetChannelWeddingSimpleInfoResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/BatchGetChannelWeddingSimpleInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) ReserveWedding(ctx context.Context, in *ReserveWeddingReq, opts ...grpc.CallOption) (*ReserveWeddingResp, error) {
	out := new(ReserveWeddingResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/ReserveWedding", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) ReportWeddingBridesmaidMan(ctx context.Context, in *ReportWeddingBridesmaidManReq, opts ...grpc.CallOption) (*ReportWeddingBridesmaidManResp, error) {
	out := new(ReportWeddingBridesmaidManResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/ReportWeddingBridesmaidMan", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) SwitchWeddingStage(ctx context.Context, in *SwitchWeddingStageReq, opts ...grpc.CallOption) (*SwitchWeddingStageResp, error) {
	out := new(SwitchWeddingStageResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/SwitchWeddingStage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) TakeWeddingGroupPhoto(ctx context.Context, in *TakeWeddingGroupPhotoReq, opts ...grpc.CallOption) (*TakeWeddingGroupPhotoResp, error) {
	out := new(TakeWeddingGroupPhotoResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/TakeWeddingGroupPhoto", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) GetUserWeddingPoseList(ctx context.Context, in *GetUserWeddingPoseListReq, opts ...grpc.CallOption) (*GetUserWeddingPoseListResp, error) {
	out := new(GetUserWeddingPoseListResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/GetUserWeddingPoseList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) SetUserWeddingPose(ctx context.Context, in *SetUserWeddingPoseReq, opts ...grpc.CallOption) (*SetUserWeddingPoseResp, error) {
	out := new(SetUserWeddingPoseResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/SetUserWeddingPose", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) SetUserWeddingOrientation(ctx context.Context, in *SetUserWeddingOrientationReq, opts ...grpc.CallOption) (*SetUserWeddingOrientationResp, error) {
	out := new(SetUserWeddingOrientationResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/SetUserWeddingOrientation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) BatchGetUserWeddingPose(ctx context.Context, in *BatchGetUserWeddingPoseReq, opts ...grpc.CallOption) (*BatchGetUserWeddingPoseResp, error) {
	out := new(BatchGetUserWeddingPoseResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/BatchGetUserWeddingPose", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) GetWeddingGroupPhotoSeatList(ctx context.Context, in *GetWeddingGroupPhotoSeatListReq, opts ...grpc.CallOption) (*GetWeddingGroupPhotoSeatListResp, error) {
	out := new(GetWeddingGroupPhotoSeatListResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/GetWeddingGroupPhotoSeatList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) SetUserWeddingGroupPhotoSeat(ctx context.Context, in *SetUserWeddingGroupPhotoSeatReq, opts ...grpc.CallOption) (*SetUserWeddingGroupPhotoSeatResp, error) {
	out := new(SetUserWeddingGroupPhotoSeatResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/SetUserWeddingGroupPhotoSeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) AddWeddingBridesmaidMan(ctx context.Context, in *AddWeddingBridesmaidManReq, opts ...grpc.CallOption) (*AddWeddingBridesmaidManResp, error) {
	out := new(AddWeddingBridesmaidManResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/AddWeddingBridesmaidMan", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) GetWeddingScheduleList(ctx context.Context, in *GetWeddingScheduleListReq, opts ...grpc.CallOption) (*GetWeddingScheduleListResp, error) {
	out := new(GetWeddingScheduleListResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/GetWeddingScheduleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) GetUserWeddingCertificate(ctx context.Context, in *GetUserWeddingCertificateReq, opts ...grpc.CallOption) (*GetUserWeddingCertificateResp, error) {
	out := new(GetUserWeddingCertificateResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/GetUserWeddingCertificate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) GetUserWeddingWeddingClips(ctx context.Context, in *GetUserWeddingWeddingClipsReq, opts ...grpc.CallOption) (*GetUserWeddingWeddingClipsResp, error) {
	out := new(GetUserWeddingWeddingClipsResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/GetUserWeddingWeddingClips", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) ReportWeddingScenePic(ctx context.Context, in *ReportWeddingScenePicReq, opts ...grpc.CallOption) (*ReportWeddingScenePicResp, error) {
	out := new(ReportWeddingScenePicResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/ReportWeddingScenePic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) PageGetGoingWeddingList(ctx context.Context, in *PageGetGoingWeddingListReq, opts ...grpc.CallOption) (*PageGetGoingWeddingListResp, error) {
	out := new(PageGetGoingWeddingListResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/PageGetGoingWeddingList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) GetWeddingRecordByTimeRange(ctx context.Context, in *GetWeddingRecordByTimeRangeReq, opts ...grpc.CallOption) (*GetWeddingRecordByTimeRangeResp, error) {
	out := new(GetWeddingRecordByTimeRangeResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/GetWeddingRecordByTimeRange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) TestWeddingScenePicIm(ctx context.Context, in *TestWeddingScenePicImReq, opts ...grpc.CallOption) (*TestWeddingScenePicImResp, error) {
	out := new(TestWeddingScenePicImResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/TestWeddingScenePicIm", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) GetWeddingHighLightPresent(ctx context.Context, in *GetWeddingHighLightPresentRequest, opts ...grpc.CallOption) (*GetWeddingHighLightPresentResponse, error) {
	out := new(GetWeddingHighLightPresentResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/GetWeddingHighLightPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) BatchGetWeddingHappiness(ctx context.Context, in *BatchGetWeddingHappinessRequest, opts ...grpc.CallOption) (*BatchGetWeddingHappinessResponse, error) {
	out := new(BatchGetWeddingHappinessResponse)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/BatchGetWeddingHappiness", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) GetChannelWeddingRankInfo(ctx context.Context, in *GetChannelWeddingRankInfoReq, opts ...grpc.CallOption) (*GetChannelWeddingRankInfoResp, error) {
	out := new(GetChannelWeddingRankInfoResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/GetChannelWeddingRankInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) GetChannelWeddingRankEntryInfo(ctx context.Context, in *GetChannelWeddingRankEntryInfoReq, opts ...grpc.CallOption) (*GetChannelWeddingRankEntryInfoResp, error) {
	out := new(GetChannelWeddingRankEntryInfoResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/GetChannelWeddingRankEntryInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) SendWeddingReservePresent(ctx context.Context, in *SendWeddingReservePresentReq, opts ...grpc.CallOption) (*SendWeddingReservePresentResp, error) {
	out := new(SendWeddingReservePresentResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/SendWeddingReservePresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) TestAfterFreeWeddingImXml(ctx context.Context, in *TestAfterFreeWeddingImXmlReq, opts ...grpc.CallOption) (*TestAfterFreeWeddingImXmlResp, error) {
	out := new(TestAfterFreeWeddingImXmlResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/TestAfterFreeWeddingImXml", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) TestMvpSettlement(ctx context.Context, in *TestMvpSettlementReq, opts ...grpc.CallOption) (*TestMvpSettlementResp, error) {
	out := new(TestMvpSettlementResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/TestMvpSettlement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) ReservePresentTimeRangeCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/ReservePresentTimeRangeCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) ReservePresentTimeRangeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/ReservePresentTimeRangeOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelWeddingClient) FixReservePresentCountOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/channel_wedding.ChannelWedding/FixReservePresentCountOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelWeddingServer is the server API for ChannelWedding service.
type ChannelWeddingServer interface {
	// 获取房间婚礼信息
	GetChannelWeddingInfo(context.Context, *GetChannelWeddingInfoReq) (*GetChannelWeddingInfoResp, error)
	// 获取用户正在进行的婚礼信息
	GetUserChannelWeddingInfo(context.Context, *GetUserChannelWeddingInfoReq) (*GetUserChannelWeddingInfoResp, error)
	// 批量获取房间婚礼简略信息
	BatchGetChannelWeddingSimpleInfo(context.Context, *BatchGetChannelWeddingSimpleInfoReq) (*BatchGetChannelWeddingSimpleInfoResp, error)
	// 预约婚礼
	ReserveWedding(context.Context, *ReserveWeddingReq) (*ReserveWeddingResp, error)
	// 婚礼进行中上报新增伴郎伴娘
	ReportWeddingBridesmaidMan(context.Context, *ReportWeddingBridesmaidManReq) (*ReportWeddingBridesmaidManResp, error)
	// 切换婚礼阶段
	SwitchWeddingStage(context.Context, *SwitchWeddingStageReq) (*SwitchWeddingStageResp, error)
	// 拍合照
	TakeWeddingGroupPhoto(context.Context, *TakeWeddingGroupPhotoReq) (*TakeWeddingGroupPhotoResp, error)
	// 获取用户可切换的姿势列表
	GetUserWeddingPoseList(context.Context, *GetUserWeddingPoseListReq) (*GetUserWeddingPoseListResp, error)
	// 切换姿势
	SetUserWeddingPose(context.Context, *SetUserWeddingPoseReq) (*SetUserWeddingPoseResp, error)
	// 切换朝向
	SetUserWeddingOrientation(context.Context, *SetUserWeddingOrientationReq) (*SetUserWeddingOrientationResp, error)
	// 批量获取用户姿势
	BatchGetUserWeddingPose(context.Context, *BatchGetUserWeddingPoseReq) (*BatchGetUserWeddingPoseResp, error)
	// 获取房间合照麦位位置
	GetWeddingGroupPhotoSeatList(context.Context, *GetWeddingGroupPhotoSeatListReq) (*GetWeddingGroupPhotoSeatListResp, error)
	// 设置用户合照位置
	SetUserWeddingGroupPhotoSeat(context.Context, *SetUserWeddingGroupPhotoSeatReq) (*SetUserWeddingGroupPhotoSeatResp, error)
	// 增加婚礼伴郎伴娘
	AddWeddingBridesmaidMan(context.Context, *AddWeddingBridesmaidManReq) (*AddWeddingBridesmaidManResp, error)
	// 根据开始时间范围获取婚礼场次列表
	GetWeddingScheduleList(context.Context, *GetWeddingScheduleListReq) (*GetWeddingScheduleListResp, error)
	// 获取用户结婚证
	GetUserWeddingCertificate(context.Context, *GetUserWeddingCertificateReq) (*GetUserWeddingCertificateResp, error)
	// 获取用户婚礼沉淀信息
	GetUserWeddingWeddingClips(context.Context, *GetUserWeddingWeddingClipsReq) (*GetUserWeddingWeddingClipsResp, error)
	// 上报婚礼场景片段图片
	ReportWeddingScenePic(context.Context, *ReportWeddingScenePicReq) (*ReportWeddingScenePicResp, error)
	// 分页获取进行中的婚礼列表
	PageGetGoingWeddingList(context.Context, *PageGetGoingWeddingListReq) (*PageGetGoingWeddingListResp, error)
	// 根据给定时间范围获取结婚证列表
	GetWeddingRecordByTimeRange(context.Context, *GetWeddingRecordByTimeRangeReq) (*GetWeddingRecordByTimeRangeResp, error)
	// 测试婚礼场景im图片
	TestWeddingScenePicIm(context.Context, *TestWeddingScenePicImReq) (*TestWeddingScenePicImResp, error)
	// 获取婚礼高光时刻礼物
	GetWeddingHighLightPresent(context.Context, *GetWeddingHighLightPresentRequest) (*GetWeddingHighLightPresentResponse, error)
	BatchGetWeddingHappiness(context.Context, *BatchGetWeddingHappinessRequest) (*BatchGetWeddingHappinessResponse, error)
	// 获取房间爱侣榜信息
	GetChannelWeddingRankInfo(context.Context, *GetChannelWeddingRankInfoReq) (*GetChannelWeddingRankInfoResp, error)
	// 获取房间爱侣榜入口信息
	GetChannelWeddingRankEntryInfo(context.Context, *GetChannelWeddingRankEntryInfoReq) (*GetChannelWeddingRankEntryInfoResp, error)
	// 发送预约礼物
	SendWeddingReservePresent(context.Context, *SendWeddingReservePresentReq) (*SendWeddingReservePresentResp, error)
	// 测试免费婚礼付费引流IM
	TestAfterFreeWeddingImXml(context.Context, *TestAfterFreeWeddingImXmlReq) (*TestAfterFreeWeddingImXmlResp, error)
	// 测试婚礼mvp结算
	TestMvpSettlement(context.Context, *TestMvpSettlementReq) (*TestMvpSettlementResp, error)
	// ===================== 预约礼物对账 =========================
	// 婚礼房预约礼物->礼物
	ReservePresentTimeRangeCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	// 获取礼物订单
	ReservePresentTimeRangeOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 补单
	FixReservePresentCountOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
}

func RegisterChannelWeddingServer(s *grpc.Server, srv ChannelWeddingServer) {
	s.RegisterService(&_ChannelWedding_serviceDesc, srv)
}

func _ChannelWedding_GetChannelWeddingInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelWeddingInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).GetChannelWeddingInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/GetChannelWeddingInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).GetChannelWeddingInfo(ctx, req.(*GetChannelWeddingInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_GetUserChannelWeddingInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserChannelWeddingInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).GetUserChannelWeddingInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/GetUserChannelWeddingInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).GetUserChannelWeddingInfo(ctx, req.(*GetUserChannelWeddingInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_BatchGetChannelWeddingSimpleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelWeddingSimpleInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).BatchGetChannelWeddingSimpleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/BatchGetChannelWeddingSimpleInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).BatchGetChannelWeddingSimpleInfo(ctx, req.(*BatchGetChannelWeddingSimpleInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_ReserveWedding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReserveWeddingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).ReserveWedding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/ReserveWedding",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).ReserveWedding(ctx, req.(*ReserveWeddingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_ReportWeddingBridesmaidMan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportWeddingBridesmaidManReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).ReportWeddingBridesmaidMan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/ReportWeddingBridesmaidMan",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).ReportWeddingBridesmaidMan(ctx, req.(*ReportWeddingBridesmaidManReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_SwitchWeddingStage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchWeddingStageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).SwitchWeddingStage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/SwitchWeddingStage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).SwitchWeddingStage(ctx, req.(*SwitchWeddingStageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_TakeWeddingGroupPhoto_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TakeWeddingGroupPhotoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).TakeWeddingGroupPhoto(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/TakeWeddingGroupPhoto",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).TakeWeddingGroupPhoto(ctx, req.(*TakeWeddingGroupPhotoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_GetUserWeddingPoseList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserWeddingPoseListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).GetUserWeddingPoseList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/GetUserWeddingPoseList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).GetUserWeddingPoseList(ctx, req.(*GetUserWeddingPoseListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_SetUserWeddingPose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserWeddingPoseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).SetUserWeddingPose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/SetUserWeddingPose",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).SetUserWeddingPose(ctx, req.(*SetUserWeddingPoseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_SetUserWeddingOrientation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserWeddingOrientationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).SetUserWeddingOrientation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/SetUserWeddingOrientation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).SetUserWeddingOrientation(ctx, req.(*SetUserWeddingOrientationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_BatchGetUserWeddingPose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserWeddingPoseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).BatchGetUserWeddingPose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/BatchGetUserWeddingPose",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).BatchGetUserWeddingPose(ctx, req.(*BatchGetUserWeddingPoseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_GetWeddingGroupPhotoSeatList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWeddingGroupPhotoSeatListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).GetWeddingGroupPhotoSeatList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/GetWeddingGroupPhotoSeatList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).GetWeddingGroupPhotoSeatList(ctx, req.(*GetWeddingGroupPhotoSeatListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_SetUserWeddingGroupPhotoSeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserWeddingGroupPhotoSeatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).SetUserWeddingGroupPhotoSeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/SetUserWeddingGroupPhotoSeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).SetUserWeddingGroupPhotoSeat(ctx, req.(*SetUserWeddingGroupPhotoSeatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_AddWeddingBridesmaidMan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddWeddingBridesmaidManReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).AddWeddingBridesmaidMan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/AddWeddingBridesmaidMan",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).AddWeddingBridesmaidMan(ctx, req.(*AddWeddingBridesmaidManReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_GetWeddingScheduleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWeddingScheduleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).GetWeddingScheduleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/GetWeddingScheduleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).GetWeddingScheduleList(ctx, req.(*GetWeddingScheduleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_GetUserWeddingCertificate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserWeddingCertificateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).GetUserWeddingCertificate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/GetUserWeddingCertificate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).GetUserWeddingCertificate(ctx, req.(*GetUserWeddingCertificateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_GetUserWeddingWeddingClips_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserWeddingWeddingClipsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).GetUserWeddingWeddingClips(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/GetUserWeddingWeddingClips",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).GetUserWeddingWeddingClips(ctx, req.(*GetUserWeddingWeddingClipsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_ReportWeddingScenePic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportWeddingScenePicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).ReportWeddingScenePic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/ReportWeddingScenePic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).ReportWeddingScenePic(ctx, req.(*ReportWeddingScenePicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_PageGetGoingWeddingList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PageGetGoingWeddingListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).PageGetGoingWeddingList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/PageGetGoingWeddingList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).PageGetGoingWeddingList(ctx, req.(*PageGetGoingWeddingListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_GetWeddingRecordByTimeRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWeddingRecordByTimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).GetWeddingRecordByTimeRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/GetWeddingRecordByTimeRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).GetWeddingRecordByTimeRange(ctx, req.(*GetWeddingRecordByTimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_TestWeddingScenePicIm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestWeddingScenePicImReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).TestWeddingScenePicIm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/TestWeddingScenePicIm",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).TestWeddingScenePicIm(ctx, req.(*TestWeddingScenePicImReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_GetWeddingHighLightPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWeddingHighLightPresentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).GetWeddingHighLightPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/GetWeddingHighLightPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).GetWeddingHighLightPresent(ctx, req.(*GetWeddingHighLightPresentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_BatchGetWeddingHappiness_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetWeddingHappinessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).BatchGetWeddingHappiness(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/BatchGetWeddingHappiness",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).BatchGetWeddingHappiness(ctx, req.(*BatchGetWeddingHappinessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_GetChannelWeddingRankInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelWeddingRankInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).GetChannelWeddingRankInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/GetChannelWeddingRankInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).GetChannelWeddingRankInfo(ctx, req.(*GetChannelWeddingRankInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_GetChannelWeddingRankEntryInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelWeddingRankEntryInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).GetChannelWeddingRankEntryInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/GetChannelWeddingRankEntryInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).GetChannelWeddingRankEntryInfo(ctx, req.(*GetChannelWeddingRankEntryInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_SendWeddingReservePresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendWeddingReservePresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).SendWeddingReservePresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/SendWeddingReservePresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).SendWeddingReservePresent(ctx, req.(*SendWeddingReservePresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_TestAfterFreeWeddingImXml_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestAfterFreeWeddingImXmlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).TestAfterFreeWeddingImXml(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/TestAfterFreeWeddingImXml",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).TestAfterFreeWeddingImXml(ctx, req.(*TestAfterFreeWeddingImXmlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_TestMvpSettlement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestMvpSettlementReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).TestMvpSettlement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/TestMvpSettlement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).TestMvpSettlement(ctx, req.(*TestMvpSettlementReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_ReservePresentTimeRangeCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).ReservePresentTimeRangeCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/ReservePresentTimeRangeCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).ReservePresentTimeRangeCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_ReservePresentTimeRangeOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).ReservePresentTimeRangeOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/ReservePresentTimeRangeOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).ReservePresentTimeRangeOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelWedding_FixReservePresentCountOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelWeddingServer).FixReservePresentCountOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_wedding.ChannelWedding/FixReservePresentCountOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelWeddingServer).FixReservePresentCountOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelWedding_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_wedding.ChannelWedding",
	HandlerType: (*ChannelWeddingServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChannelWeddingInfo",
			Handler:    _ChannelWedding_GetChannelWeddingInfo_Handler,
		},
		{
			MethodName: "GetUserChannelWeddingInfo",
			Handler:    _ChannelWedding_GetUserChannelWeddingInfo_Handler,
		},
		{
			MethodName: "BatchGetChannelWeddingSimpleInfo",
			Handler:    _ChannelWedding_BatchGetChannelWeddingSimpleInfo_Handler,
		},
		{
			MethodName: "ReserveWedding",
			Handler:    _ChannelWedding_ReserveWedding_Handler,
		},
		{
			MethodName: "ReportWeddingBridesmaidMan",
			Handler:    _ChannelWedding_ReportWeddingBridesmaidMan_Handler,
		},
		{
			MethodName: "SwitchWeddingStage",
			Handler:    _ChannelWedding_SwitchWeddingStage_Handler,
		},
		{
			MethodName: "TakeWeddingGroupPhoto",
			Handler:    _ChannelWedding_TakeWeddingGroupPhoto_Handler,
		},
		{
			MethodName: "GetUserWeddingPoseList",
			Handler:    _ChannelWedding_GetUserWeddingPoseList_Handler,
		},
		{
			MethodName: "SetUserWeddingPose",
			Handler:    _ChannelWedding_SetUserWeddingPose_Handler,
		},
		{
			MethodName: "SetUserWeddingOrientation",
			Handler:    _ChannelWedding_SetUserWeddingOrientation_Handler,
		},
		{
			MethodName: "BatchGetUserWeddingPose",
			Handler:    _ChannelWedding_BatchGetUserWeddingPose_Handler,
		},
		{
			MethodName: "GetWeddingGroupPhotoSeatList",
			Handler:    _ChannelWedding_GetWeddingGroupPhotoSeatList_Handler,
		},
		{
			MethodName: "SetUserWeddingGroupPhotoSeat",
			Handler:    _ChannelWedding_SetUserWeddingGroupPhotoSeat_Handler,
		},
		{
			MethodName: "AddWeddingBridesmaidMan",
			Handler:    _ChannelWedding_AddWeddingBridesmaidMan_Handler,
		},
		{
			MethodName: "GetWeddingScheduleList",
			Handler:    _ChannelWedding_GetWeddingScheduleList_Handler,
		},
		{
			MethodName: "GetUserWeddingCertificate",
			Handler:    _ChannelWedding_GetUserWeddingCertificate_Handler,
		},
		{
			MethodName: "GetUserWeddingWeddingClips",
			Handler:    _ChannelWedding_GetUserWeddingWeddingClips_Handler,
		},
		{
			MethodName: "ReportWeddingScenePic",
			Handler:    _ChannelWedding_ReportWeddingScenePic_Handler,
		},
		{
			MethodName: "PageGetGoingWeddingList",
			Handler:    _ChannelWedding_PageGetGoingWeddingList_Handler,
		},
		{
			MethodName: "GetWeddingRecordByTimeRange",
			Handler:    _ChannelWedding_GetWeddingRecordByTimeRange_Handler,
		},
		{
			MethodName: "TestWeddingScenePicIm",
			Handler:    _ChannelWedding_TestWeddingScenePicIm_Handler,
		},
		{
			MethodName: "GetWeddingHighLightPresent",
			Handler:    _ChannelWedding_GetWeddingHighLightPresent_Handler,
		},
		{
			MethodName: "BatchGetWeddingHappiness",
			Handler:    _ChannelWedding_BatchGetWeddingHappiness_Handler,
		},
		{
			MethodName: "GetChannelWeddingRankInfo",
			Handler:    _ChannelWedding_GetChannelWeddingRankInfo_Handler,
		},
		{
			MethodName: "GetChannelWeddingRankEntryInfo",
			Handler:    _ChannelWedding_GetChannelWeddingRankEntryInfo_Handler,
		},
		{
			MethodName: "SendWeddingReservePresent",
			Handler:    _ChannelWedding_SendWeddingReservePresent_Handler,
		},
		{
			MethodName: "TestAfterFreeWeddingImXml",
			Handler:    _ChannelWedding_TestAfterFreeWeddingImXml_Handler,
		},
		{
			MethodName: "TestMvpSettlement",
			Handler:    _ChannelWedding_TestMvpSettlement_Handler,
		},
		{
			MethodName: "ReservePresentTimeRangeCount",
			Handler:    _ChannelWedding_ReservePresentTimeRangeCount_Handler,
		},
		{
			MethodName: "ReservePresentTimeRangeOrderIds",
			Handler:    _ChannelWedding_ReservePresentTimeRangeOrderIds_Handler,
		},
		{
			MethodName: "FixReservePresentCountOrder",
			Handler:    _ChannelWedding_FixReservePresentCountOrder_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/channel-wedding/channel-wedding.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/channel-wedding/channel-wedding.proto", fileDescriptor_channel_wedding_1641b6bc240a13b9)
}

var fileDescriptor_channel_wedding_1641b6bc240a13b9 = []byte{
	// 4578 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x3b, 0x4d, 0x6f, 0xdc, 0x48,
	0x76, 0x66, 0xb7, 0x5a, 0x6a, 0x3d, 0x7d, 0xb5, 0x68, 0xc9, 0x6a, 0xb5, 0xec, 0x91, 0x4c, 0xdb,
	0x13, 0xaf, 0x67, 0x2d, 0xcf, 0xf8, 0x03, 0x1b, 0xcc, 0xee, 0x26, 0x2b, 0xc9, 0x2d, 0xa9, 0x37,
	0xd6, 0x47, 0xa8, 0x96, 0xe7, 0x0b, 0x58, 0x86, 0x22, 0x4b, 0x2d, 0xc6, 0xdd, 0x24, 0xcd, 0x62,
	0xb7, 0xad, 0xdd, 0x7c, 0x03, 0x41, 0x90, 0x8f, 0xcb, 0x22, 0x87, 0x20, 0x97, 0x00, 0xb9, 0xe4,
	0x90, 0x43, 0x02, 0x04, 0x41, 0x16, 0x1b, 0x60, 0x81, 0x3d, 0x04, 0x39, 0xe5, 0x92, 0x3f, 0xb0,
	0xf9, 0x0b, 0x41, 0x90, 0x53, 0x4e, 0x41, 0xbd, 0xaa, 0x62, 0x93, 0x6c, 0x92, 0x6a, 0xcf, 0xcc,
	0x9e, 0xd4, 0xf5, 0xea, 0xd5, 0x7b, 0x55, 0xef, 0xab, 0x5e, 0xbd, 0x47, 0xc1, 0xd3, 0x30, 0x7c,
	0xf4, 0xba, 0xef, 0x58, 0xaf, 0xa8, 0xd3, 0x1d, 0x90, 0xe0, 0x91, 0x75, 0x61, 0xba, 0x2e, 0xe9,
	0x3e, 0x7c, 0x43, 0x6c, 0xdb, 0x71, 0x3b, 0xe9, 0xf1, 0xa6, 0x1f, 0x78, 0xa1, 0xa7, 0x2e, 0x08,
	0xb0, 0x21, 0xc0, 0x8d, 0xcd, 0x14, 0x19, 0xf2, 0x36, 0x24, 0x2e, 0x75, 0x3c, 0xf7, 0x91, 0xe7,
	0x87, 0x8e, 0xe7, 0x52, 0xf9, 0x97, 0x13, 0x18, 0xc1, 0x0f, 0x88, 0xe5, 0xb9, 0x96, 0xd3, 0x25,
	0x0f, 0x07, 0x8f, 0x13, 0x03, 0x8e, 0xaf, 0x59, 0xb0, 0xf0, 0x09, 0x67, 0x75, 0x12, 0x9a, 0x1d,
	0xb2, 0x73, 0xde, 0x51, 0x97, 0xa0, 0x42, 0xd9, 0xef, 0xba, 0xb2, 0xa1, 0xdc, 0x9f, 0xd3, 0xf9,
	0x40, 0xbd, 0x05, 0x80, 0x3f, 0x0c, 0xd7, 0xec, 0x91, 0x7a, 0x69, 0x43, 0xb9, 0x3f, 0xad, 0x4f,
	0x23, 0xe4, 0xd0, 0xec, 0x11, 0x75, 0x0d, 0xa6, 0x69, 0xff, 0xcc, 0xe0, 0x0b, 0xcb, 0xb8, 0xb0,
	0x4a, 0xfb, 0x67, 0x48, 0x54, 0xfb, 0x85, 0x02, 0xb5, 0x38, 0x97, 0x96, 0x7b, 0xee, 0xa9, 0xbb,
	0x30, 0xcf, 0x09, 0x5a, 0xe7, 0x1d, 0xa3, 0xeb, 0xd0, 0xb0, 0xae, 0x6c, 0x94, 0xef, 0xcf, 0x3c,
	0xde, 0xd8, 0x4c, 0xc9, 0x60, 0x33, 0xb5, 0x41, 0x7d, 0x96, 0x8a, 0x5f, 0x2f, 0x1c, 0x1a, 0xb2,
	0x8d, 0x59, 0xfd, 0x20, 0x10, 0xac, 0x4b, 0xc8, 0x7a, 0x9a, 0x41, 0x10, 0xbf, 0x70, 0x63, 0xea,
	0x5d, 0xb9, 0x07, 0x1a, 0x9a, 0x41, 0x68, 0x84, 0xb4, 0x3e, 0xb1, 0xa1, 0xdc, 0x2f, 0x0b, 0x0e,
	0x27, 0x0c, 0xd8, 0xa6, 0xea, 0x06, 0xf0, 0xb1, 0x41, 0x5c, 0x9b, 0xe1, 0x54, 0x10, 0x87, 0x8b,
	0xa3, 0xe9, 0xda, 0x6d, 0xaa, 0x05, 0xd1, 0xf9, 0x5e, 0x90, 0x01, 0xe9, 0xe2, 0xf9, 0x96, 0xa0,
	0xd2, 0x65, 0x03, 0x29, 0x46, 0x1c, 0xa8, 0x77, 0x60, 0xae, 0x13, 0x78, 0x5e, 0xcf, 0xb0, 0xba,
	0x5e, 0x78, 0x41, 0x68, 0xbd, 0xb4, 0x51, 0xbe, 0x3f, 0xa7, 0xcf, 0x22, 0x70, 0x87, 0xc3, 0x18,
	0xd2, 0x59, 0xe0, 0xd8, 0x24, 0x42, 0x2a, 0x73, 0x24, 0x04, 0x0a, 0x24, 0xed, 0xef, 0x15, 0xb8,
	0x2e, 0x25, 0x63, 0x11, 0x97, 0x6c, 0x7b, 0xae, 0x54, 0x5f, 0x06, 0x5f, 0x26, 0x06, 0xf2, 0xda,
	0x70, 0x5c, 0x9b, 0xbc, 0x15, 0x42, 0xaa, 0x52, 0xf2, 0xba, 0xc5, 0xc6, 0xea, 0x3d, 0x98, 0x37,
	0x5d, 0xa7, 0x67, 0x32, 0x43, 0xe2, 0xfa, 0x2d, 0xa3, 0x7e, 0xe7, 0x22, 0x28, 0xea, 0x78, 0x05,
	0xa6, 0xce, 0x3c, 0x97, 0x18, 0x8e, 0x8d, 0x62, 0x9a, 0xd3, 0x27, 0xd9, 0xb0, 0x65, 0x33, 0x01,
	0x9d, 0x99, 0x94, 0x18, 0x72, 0xb6, 0x82, 0xb3, 0xc0, 0x60, 0xdb, 0x88, 0xa1, 0xfd, 0xbb, 0x32,
	0xb4, 0x33, 0xb6, 0x59, 0x69, 0x67, 0xec, 0x77, 0x64, 0x67, 0x6c, 0xc0, 0xf6, 0x82, 0x3f, 0x8c,
	0x80, 0x50, 0xaf, 0x1f, 0x58, 0xd2, 0xd6, 0xe6, 0x10, 0xaa, 0x0b, 0xa0, 0xfa, 0x4d, 0x50, 0x93,
	0x68, 0x46, 0xcf, 0x7e, 0x26, 0xb6, 0x5d, 0x4b, 0xa0, 0x1e, 0xd8, 0xcf, 0xd4, 0x7d, 0x98, 0xc3,
	0xbd, 0x45, 0xa6, 0x36, 0x81, 0xa6, 0x76, 0x37, 0xd7, 0xd4, 0x62, 0x02, 0xd5, 0x67, 0xce, 0xf8,
	0x0f, 0x66, 0x6d, 0xda, 0x7f, 0x0e, 0xa5, 0x8e, 0xaa, 0x96, 0x2a, 0xfb, 0x25, 0x6b, 0x5b, 0xfd,
	0x00, 0x16, 0x71, 0x11, 0xed, 0x99, 0x6e, 0x84, 0x38, 0x81, 0x88, 0xb5, 0x68, 0x42, 0x22, 0x3f,
	0x04, 0x15, 0x17, 0xd3, 0x9e, 0xe9, 0xd8, 0x11, 0x76, 0x05, 0xb1, 0x17, 0x87, 0x33, 0xd2, 0x92,
	0xfe, 0xb4, 0x04, 0xab, 0xf1, 0x33, 0x6d, 0x9b, 0xd6, 0xab, 0x4e, 0xe0, 0xf5, 0x5d, 0x3b, 0xdf,
	0x9e, 0x18, 0x8b, 0x08, 0xcd, 0xf0, 0x1d, 0x2b, 0xec, 0x07, 0x52, 0x55, 0x8b, 0xc3, 0x99, 0x63,
	0x3e, 0xc1, 0xd4, 0x15, 0x43, 0xef, 0xf9, 0x4f, 0x8d, 0x7e, 0xd0, 0x95, 0xea, 0x1a, 0xce, 0x1c,
	0xf8, 0x4f, 0x4f, 0x83, 0xae, 0xfa, 0x1d, 0x68, 0x50, 0x9f, 0x58, 0x8e, 0xd9, 0x35, 0x32, 0x98,
	0x4c, 0xe0, 0xaa, 0xba, 0xc0, 0xd8, 0x1e, 0xe1, 0xf5, 0xed, 0xcc, 0xd5, 0x92, 0x67, 0x05, 0x57,
	0xaf, 0x8c, 0xac, 0xe6, 0xac, 0xb5, 0x9f, 0x0c, 0x0d, 0x35, 0xb2, 0xb5, 0xdb, 0x30, 0x1b, 0x59,
	0x19, 0x23, 0xa1, 0x20, 0x89, 0x19, 0x09, 0x63, 0x3b, 0x8e, 0xa3, 0x30, 0x43, 0x2c, 0x25, 0x51,
	0x98, 0x0d, 0xde, 0x04, 0xb0, 0xfc, 0xc8, 0x45, 0x44, 0x24, 0xb2, 0x7c, 0xee, 0x20, 0xea, 0x2a,
	0x54, 0x9d, 0x90, 0xf4, 0x0c, 0xc7, 0x96, 0x6a, 0x9d, 0x62, 0xe3, 0x96, 0x4d, 0xd5, 0x7b, 0xb0,
	0x80, 0xde, 0x15, 0x5b, 0xcd, 0x1d, 0x0c, 0x9d, 0x6e, 0x47, 0x50, 0xd0, 0xfe, 0xab, 0x12, 0x59,
	0xa6, 0xee, 0x79, 0xbd, 0xf6, 0x05, 0xe9, 0xa1, 0x9b, 0xad, 0x42, 0x35, 0x64, 0xbf, 0xd9, 0x3a,
	0xae, 0xc2, 0x29, 0x1c, 0xb7, 0x6c, 0xe6, 0x6b, 0x7c, 0x2a, 0xed, 0x6b, 0x08, 0x8d, 0xfb, 0x5a,
	0x12, 0x2d, 0xee, 0x6b, 0x09, 0x54, 0x76, 0xce, 0x5d, 0xe9, 0xc0, 0x29, 0x67, 0xdb, 0x28, 0x74,
	0x36, 0x1e, 0xd7, 0xc5, 0x2f, 0x8c, 0xeb, 0x9b, 0x70, 0xdd, 0xba, 0x30, 0x9d, 0xc0, 0xe8, 0x98,
	0xf1, 0x1d, 0x72, 0xfd, 0x2d, 0xe2, 0xd4, 0x9e, 0x19, 0xdb, 0xe5, 0x33, 0x58, 0xc9, 0xc0, 0xc7,
	0xad, 0x4e, 0xe2, 0x9a, 0xa5, 0x91, 0x35, 0x6c, 0xbb, 0x3a, 0xa8, 0x68, 0xd1, 0xd2, 0x4d, 0xf8,
	0x96, 0xa7, 0x8a, 0xe3, 0x43, 0xdc, 0xf5, 0xf5, 0x5a, 0x37, 0x36, 0xc2, 0xad, 0xff, 0x00, 0x96,
	0x39, 0xcd, 0x98, 0xfd, 0x21, 0xd9, 0x2a, 0x92, 0x7d, 0x50, 0x48, 0x36, 0xe1, 0x7d, 0xfa, 0xf5,
	0x6e, 0x12, 0x86, 0xf4, 0x3f, 0x87, 0xba, 0x58, 0x69, 0xf8, 0x01, 0x19, 0x38, 0xe4, 0xcd, 0x50,
	0x3e, 0xd3, 0x1b, 0x4a, 0x91, 0xb0, 0xe5, 0xd1, 0xf5, 0x1b, 0x62, 0xe2, 0x98, 0x13, 0x88, 0xc4,
	0xf8, 0x00, 0xb8, 0x6c, 0x87, 0x12, 0x74, 0xec, 0x3a, 0xa0, 0xdd, 0x2c, 0xe0, 0x84, 0xc4, 0x6c,
	0xd9, 0xaa, 0x06, 0x73, 0x0e, 0x35, 0xce, 0x03, 0x42, 0x0c, 0x34, 0x83, 0xfa, 0xcc, 0x86, 0x72,
	0xbf, 0xaa, 0xcf, 0x38, 0x74, 0x37, 0x20, 0x04, 0x2d, 0x50, 0x6d, 0xc1, 0x5c, 0x44, 0x8f, 0x99,
	0x44, 0x7d, 0x16, 0x37, 0x78, 0x6f, 0x64, 0x83, 0x3b, 0x69, 0xed, 0x60, 0xec, 0x95, 0x2c, 0x77,
	0xce, 0x3b, 0xda, 0x4f, 0x4b, 0xb0, 0x94, 0x85, 0xc5, 0x2e, 0x37, 0xce, 0xc3, 0x77, 0x2c, 0xe1,
	0x9d, 0x55, 0x04, 0x1c, 0x3b, 0x96, 0xfa, 0x04, 0x6e, 0x50, 0x27, 0x0c, 0x51, 0x58, 0x1e, 0x25,
	0xc6, 0x39, 0xe9, 0x99, 0x5d, 0x3c, 0x15, 0xbf, 0x06, 0xaf, 0x8b, 0xd9, 0x63, 0x8f, 0x92, 0x5d,
	0x9c, 0x6b, 0xd9, 0xea, 0x23, 0x58, 0x4a, 0x2c, 0x92, 0x4b, 0xb8, 0xdb, 0x2e, 0xc6, 0x96, 0x1c,
	0xf0, 0x05, 0x0f, 0x60, 0x91, 0x86, 0xa6, 0x6b, 0x9f, 0x5d, 0xc6, 0x18, 0xf0, 0x5b, 0x72, 0x41,
	0x4c, 0x44, 0xc4, 0xdf, 0x07, 0x09, 0x8a, 0xe8, 0x72, 0x87, 0x9e, 0x13, 0xe0, 0x83, 0x08, 0xef,
	0xdc, 0x74, 0xba, 0x43, 0x82, 0xb4, 0x3e, 0x89, 0xa1, 0x61, 0x8e, 0x81, 0x25, 0x39, 0xca, 0xd4,
	0x80, 0x78, 0x11, 0xd6, 0x14, 0x62, 0xcd, 0x30, 0x20, 0x27, 0x45, 0xb5, 0xbb, 0x51, 0x86, 0xb2,
	0xe3, 0x1f, 0x90, 0x1e, 0x66, 0x28, 0x35, 0x28, 0xf7, 0xa3, 0xa0, 0xc0, 0x7e, 0x6a, 0x7f, 0xa7,
	0xc0, 0x92, 0x40, 0x3b, 0x20, 0x3d, 0x2f, 0x70, 0xcc, 0xee, 0x4b, 0xc7, 0x26, 0xde, 0xd7, 0x14,
	0x02, 0xef, 0xc0, 0x5c, 0x84, 0xf2, 0xdb, 0xd4, 0x73, 0x45, 0x0c, 0x89, 0xd6, 0x7d, 0x9f, 0x7a,
	0x2e, 0x43, 0xea, 0x53, 0x12, 0xc8, 0x70, 0xcf, 0xc3, 0xe1, 0xb4, 0x3e, 0xcb, 0x80, 0x22, 0xc4,
	0x53, 0xed, 0x9f, 0x14, 0x98, 0x17, 0x1b, 0x95, 0x79, 0xcf, 0x06, 0xcc, 0xa2, 0x00, 0x64, 0x8c,
	0xe4, 0xc7, 0x02, 0x06, 0x13, 0x31, 0xf6, 0x2e, 0xcc, 0x0b, 0x51, 0x4a, 0x1c, 0x6e, 0x01, 0xb3,
	0x1c, 0x2a, 0xb0, 0xbe, 0x01, 0x8b, 0x18, 0x6e, 0x13, 0x88, 0x5c, 0xef, 0xf3, 0x6c, 0xe2, 0x60,
	0x88, 0xfa, 0x10, 0xae, 0x23, 0x6a, 0x8a, 0x2a, 0x57, 0x7b, 0x8d, 0x4d, 0xed, 0xc6, 0x28, 0x6b,
	0xbf, 0x01, 0xea, 0xbe, 0xe9, 0xfb, 0x8e, 0x4b, 0x28, 0xbd, 0x2a, 0x4f, 0x5c, 0x87, 0x19, 0x1e,
	0x42, 0x06, 0x66, 0xb7, 0x2f, 0xd3, 0x5a, 0x40, 0xd0, 0x4b, 0x06, 0xd1, 0x74, 0xb8, 0x1e, 0x11,
	0xdb, 0xf1, 0xdc, 0x73, 0xa7, 0x83, 0xd4, 0xbe, 0x0d, 0x93, 0x16, 0x8e, 0x44, 0x36, 0x7d, 0x67,
	0xc4, 0xcf, 0x46, 0xb7, 0xa0, 0x8b, 0x25, 0xda, 0xcf, 0x27, 0x61, 0x46, 0x48, 0x55, 0x1a, 0x88,
	0x35, 0x34, 0x10, 0xcb, 0xb1, 0x59, 0xb2, 0x2d, 0x23, 0x8f, 0x10, 0x5f, 0x59, 0x9f, 0x16, 0x90,
	0x96, 0xad, 0x7e, 0x4f, 0x3e, 0x12, 0x1c, 0xf7, 0xdc, 0x43, 0xa1, 0xcd, 0x3c, 0xbe, 0x5d, 0x98,
	0xcf, 0x23, 0x7f, 0xfe, 0x8e, 0x40, 0x96, 0x5b, 0x30, 0xcd, 0xef, 0x1a, 0x16, 0x2a, 0x26, 0x90,
	0x40, 0x6e, 0x14, 0x8e, 0x5f, 0x73, 0x3a, 0xbf, 0xe4, 0x98, 0x21, 0x7c, 0x0b, 0x2a, 0x98, 0xe3,
	0xa0, 0x53, 0x15, 0xf0, 0x8f, 0x1c, 0x41, 0xe7, 0xf8, 0x6c, 0x21, 0xa6, 0x52, 0x78, 0x5f, 0x8c,
	0xb7, 0x10, 0xf1, 0xc5, 0xdb, 0x88, 0x3d, 0x20, 0x9c, 0x1e, 0xa9, 0x4f, 0x71, 0xa9, 0x20, 0xa4,
	0xed, 0xf4, 0x08, 0xbb, 0x81, 0xf1, 0xe5, 0xc0, 0x26, 0xab, 0x38, 0x39, 0x45, 0x5c, 0x1b, 0xa7,
	0xbe, 0x00, 0x19, 0x87, 0x8d, 0x9e, 0x70, 0x38, 0x63, 0xc0, 0x3c, 0x4e, 0xc4, 0xf1, 0x7b, 0x79,
	0x7b, 0x48, 0xb8, 0xa7, 0xbe, 0xf4, 0x26, 0xcb, 0x69, 0xef, 0x43, 0x2d, 0x76, 0x23, 0x12, 0x37,
	0x0c, 0x2e, 0x31, 0x92, 0x57, 0xf5, 0xf9, 0xe8, 0x2a, 0x6c, 0x32, 0x28, 0xbb, 0x6b, 0x63, 0x09,
	0x23, 0x4b, 0x31, 0xf1, 0xba, 0x9a, 0x49, 0x67, 0x8c, 0x07, 0xa6, 0x9b, 0x78, 0x73, 0x71, 0xc3,
	0x9d, 0x1d, 0xbe, 0xb9, 0xd0, 0xa6, 0xd4, 0x23, 0xa8, 0x5d, 0x48, 0x2b, 0x33, 0x84, 0x39, 0xce,
	0xe5, 0xe8, 0x32, 0xc3, 0x88, 0xf5, 0x85, 0x8b, 0x24, 0x50, 0xfd, 0x10, 0x96, 0x90, 0xdf, 0x90,
	0x2a, 0x77, 0x8b, 0x79, 0xe4, 0xac, 0xb2, 0xb9, 0x88, 0x0e, 0xba, 0x87, 0xfa, 0x31, 0x54, 0x65,
	0xc6, 0x5f, 0x5f, 0x40, 0xd6, 0xeb, 0x79, 0xa2, 0x94, 0x79, 0xfe, 0x94, 0xc8, 0xf3, 0x45, 0x24,
	0x23, 0xc1, 0x80, 0x70, 0x9d, 0xd5, 0x50, 0x67, 0x33, 0x02, 0x86, 0x7a, 0x5b, 0x81, 0x29, 0xbf,
	0x6b, 0xba, 0xcc, 0x09, 0x16, 0xf9, 0x53, 0x88, 0x0d, 0x5b, 0xb6, 0xf6, 0x6b, 0x50, 0xdf, 0x23,
	0xe1, 0x0e, 0xe7, 0x14, 0xf3, 0x25, 0x9d, 0xbc, 0x1e, 0x8d, 0xb7, 0xd2, 0xc1, 0x4a, 0x91, 0x83,
	0x69, 0xff, 0xac, 0xc0, 0x6a, 0x0e, 0x01, 0xea, 0xab, 0xbf, 0x0e, 0xb3, 0x91, 0xfb, 0x31, 0x0f,
	0x53, 0xf0, 0x64, 0x37, 0xf3, 0x4e, 0x86, 0xeb, 0x66, 0xde, 0xc4, 0x3c, 0xfa, 0x25, 0xa8, 0x3e,
	0x3b, 0x87, 0x1b, 0x1a, 0x96, 0xd7, 0x77, 0x43, 0x4e, 0xa6, 0x84, 0x64, 0xee, 0xe7, 0x91, 0x39,
	0xe6, 0x2b, 0x76, 0xd8, 0x02, 0x24, 0x59, 0xf3, 0x53, 0x10, 0xed, 0x43, 0xb8, 0xb9, 0x47, 0xc2,
	0x53, 0x4a, 0x82, 0x31, 0x8f, 0xae, 0xfd, 0x16, 0xdc, 0x2a, 0x58, 0xf1, 0x35, 0x9c, 0x55, 0xfb,
	0x17, 0x05, 0x16, 0x4f, 0x9c, 0x9e, 0xdf, 0x25, 0x5f, 0x29, 0xa6, 0x25, 0x9d, 0xbb, 0x5c, 0xe4,
	0xdc, 0x13, 0x49, 0xe7, 0x4e, 0x56, 0x26, 0x2a, 0xe9, 0xca, 0x44, 0xcc, 0x86, 0x26, 0x13, 0x36,
	0xf4, 0x09, 0xdc, 0xd9, 0x36, 0x43, 0xeb, 0x62, 0xc4, 0x0e, 0xf8, 0x69, 0xa4, 0x4c, 0x97, 0x61,
	0xd2, 0xf3, 0x8d, 0xa1, 0x58, 0x2b, 0x9e, 0x7f, 0xea, 0xe0, 0x4b, 0xc2, 0x72, 0x44, 0xbe, 0xc9,
	0x9f, 0x9b, 0x53, 0x96, 0x83, 0x79, 0xa3, 0x36, 0x80, 0xbb, 0x57, 0x13, 0xa6, 0xbe, 0x7a, 0x08,
	0x8b, 0x71, 0xd1, 0xc7, 0xab, 0x33, 0xda, 0x88, 0xfc, 0x47, 0x44, 0xac, 0x2f, 0xc4, 0xb4, 0x80,
	0x7c, 0x7f, 0x56, 0x86, 0x45, 0x9d, 0x7b, 0x4f, 0x94, 0x86, 0x8e, 0xe5, 0x0e, 0x57, 0x09, 0xff,
	0x56, 0xec, 0x6d, 0x83, 0xcf, 0xc2, 0xed, 0x52, 0x5d, 0x19, 0xbe, 0x6f, 0xd6, 0x60, 0x9a, 0xbf,
	0xac, 0xfb, 0x51, 0x8a, 0x55, 0x45, 0x00, 0x93, 0xd3, 0x1a, 0x4c, 0xf3, 0xb7, 0x79, 0x3f, 0x52,
	0x40, 0x15, 0x01, 0x6c, 0xf2, 0x3e, 0xd4, 0xa4, 0x04, 0x22, 0x06, 0x53, 0x3c, 0x07, 0x10, 0xf0,
	0xb6, 0xe0, 0x51, 0x10, 0xdc, 0x63, 0x0a, 0x9e, 0x8e, 0x2b, 0x98, 0x9d, 0x8a, 0x53, 0x0d, 0x2f,
	0x7d, 0x22, 0x92, 0x6b, 0x7e, 0xed, 0xb5, 0x2f, 0x7d, 0x92, 0x8a, 0xc6, 0x7d, 0xa9, 0xcc, 0x91,
	0x68, 0x7c, 0xca, 0xd5, 0xca, 0xd2, 0x10, 0xda, 0x77, 0x42, 0x83, 0xbc, 0x0d, 0x03, 0xd3, 0xa0,
	0x6c, 0x3b, 0x94, 0x58, 0x22, 0x2c, 0xd7, 0xd8, 0x54, 0x93, 0xcd, 0x9c, 0x10, 0xd7, 0x3e, 0x21,
	0x16, 0x0b, 0xa6, 0x8e, 0xeb, 0x84, 0x23, 0xc1, 0x74, 0x8e, 0x07, 0x53, 0x36, 0x97, 0x0c, 0xa6,
	0xda, 0x13, 0x50, 0xd3, 0xea, 0xa3, 0x7e, 0xca, 0x6f, 0x94, 0x94, 0xdf, 0x68, 0x7f, 0xa5, 0xc0,
	0x2d, 0x9d, 0xf8, 0x5e, 0x10, 0xca, 0x38, 0x1b, 0xbf, 0x46, 0x98, 0x01, 0xc4, 0xe4, 0xa3, 0x24,
	0xe4, 0x33, 0x6a, 0x07, 0x39, 0x22, 0x29, 0xe7, 0x89, 0x84, 0x69, 0xbe, 0x7f, 0x49, 0x02, 0x54,
	0xee, 0x84, 0xd0, 0x3c, 0x03, 0x9c, 0x3a, 0xb6, 0xb6, 0x01, 0xef, 0x15, 0x6d, 0x8c, 0xfa, 0x9a,
	0x0b, 0xcb, 0x27, 0x6f, 0x9c, 0xd0, 0xba, 0x88, 0xa7, 0x2a, 0xe3, 0xda, 0x6c, 0x54, 0x3f, 0x2d,
	0xc7, 0xeb, 0xa7, 0x89, 0x3a, 0xe4, 0x44, 0xaa, 0x40, 0x5a, 0x87, 0x1b, 0x59, 0xfc, 0xa8, 0xaf,
	0x7d, 0x01, 0xf5, 0xb6, 0xf9, 0x4a, 0xca, 0x7d, 0x2f, 0xf0, 0xfa, 0xfe, 0xf1, 0x85, 0x17, 0x8e,
	0x7b, 0x9f, 0x30, 0xb6, 0x3e, 0xc3, 0x37, 0x82, 0xbe, 0xac, 0xb7, 0x54, 0x11, 0xa0, 0xf7, 0xbb,
	0xda, 0x1a, 0xac, 0xe6, 0x10, 0xa7, 0xbe, 0xf6, 0x9b, 0x78, 0x11, 0xb1, 0x00, 0x2d, 0xaf, 0x01,
	0x8f, 0x12, 0x26, 0xdc, 0x71, 0x59, 0xd7, 0xa0, 0x4c, 0xc9, 0x5b, 0x21, 0x05, 0xf6, 0x53, 0x7b,
	0x0b, 0x8d, 0x3c, 0x92, 0xd4, 0xc7, 0xad, 0xb2, 0xb7, 0x56, 0x14, 0x6d, 0xe6, 0xf4, 0xaa, 0x2f,
	0x10, 0xf0, 0x89, 0xc7, 0x62, 0x29, 0x03, 0xc8, 0xfa, 0x25, 0x03, 0x30, 0x0a, 0xea, 0x06, 0xcc,
	0x78, 0x81, 0x43, 0xdc, 0x10, 0x6b, 0x95, 0x82, 0x63, 0x1c, 0xa4, 0x1d, 0xc1, 0xf2, 0xc9, 0x08,
	0xe7, 0x71, 0x0f, 0xa2, 0xc2, 0x04, 0xb2, 0xe5, 0x74, 0xf1, 0x37, 0x6a, 0x2c, 0x83, 0x20, 0xf5,
	0x35, 0x1b, 0x6e, 0x26, 0x67, 0x8e, 0x86, 0xfb, 0x18, 0x97, 0xe3, 0xd5, 0x07, 0x5a, 0x87, 0x5b,
	0x05, 0x5c, 0xa8, 0xcf, 0x9e, 0x72, 0x0b, 0xa9, 0xed, 0x65, 0xb0, 0x96, 0x47, 0x2b, 0x0d, 0x8f,
	0x76, 0x35, 0x73, 0xf6, 0xd4, 0x42, 0x4d, 0x25, 0x1f, 0x3c, 0xc0, 0x60, 0xa9, 0x47, 0x54, 0x02,
	0xad, 0x32, 0x7c, 0x44, 0x1d, 0x47, 0xa8, 0xda, 0x17, 0xd0, 0x90, 0x97, 0xd2, 0x97, 0xd4, 0xcf,
	0x2a, 0x54, 0x53, 0x11, 0x61, 0xaa, 0x2f, 0x6e, 0x3c, 0x02, 0x6b, 0xb9, 0xc4, 0xa9, 0xaf, 0xee,
	0xc2, 0x3c, 0x7f, 0x6b, 0x26, 0xec, 0x2e, 0xab, 0x7c, 0x92, 0x5e, 0xcd, 0x9f, 0xa3, 0xc2, 0x3a,
	0xb5, 0x26, 0xac, 0xef, 0x91, 0x70, 0xc4, 0x8f, 0x4e, 0x88, 0x19, 0xbe, 0x83, 0xc7, 0x68, 0x9b,
	0xb0, 0x9c, 0x49, 0x83, 0x5d, 0xf5, 0x3d, 0xc7, 0x1a, 0x06, 0xca, 0x4a, 0xcf, 0xb1, 0x5a, 0xb6,
	0xf6, 0x37, 0x0a, 0x6c, 0x14, 0xf3, 0xa5, 0xbe, 0xba, 0x03, 0xd3, 0x94, 0x98, 0x61, 0xfc, 0x78,
	0xef, 0xe7, 0x25, 0x51, 0x49, 0x12, 0x7a, 0x95, 0x0a, 0x42, 0xea, 0x33, 0x58, 0x41, 0x19, 0x61,
	0x32, 0x1f, 0xf4, 0x48, 0x2c, 0x06, 0xf3, 0x1c, 0x63, 0x89, 0x4d, 0xef, 0xc8, 0x59, 0x11, 0x86,
	0xb5, 0x3f, 0x53, 0x60, 0x3d, 0x69, 0xa6, 0x29, 0x0e, 0x63, 0x6a, 0x38, 0x71, 0x86, 0xf2, 0x97,
	0x3b, 0x83, 0xa6, 0xc1, 0x46, 0xf1, 0x5e, 0xa8, 0xaf, 0xfd, 0x2e, 0x34, 0xb6, 0x6c, 0x3b, 0xef,
	0xc2, 0x1a, 0xbd, 0xf1, 0xe6, 0xe2, 0x99, 0xe2, 0x57, 0xbe, 0xb6, 0xb4, 0x5b, 0xb0, 0x96, 0xcb,
	0x9e, 0xfa, 0xda, 0xcf, 0xe2, 0x5d, 0x94, 0x0b, 0x62, 0xf7, 0xbb, 0x24, 0xff, 0x12, 0x1d, 0x4d,
	0x6b, 0x13, 0x9b, 0x4d, 0xe4, 0x46, 0xe5, 0xa2, 0xdc, 0x68, 0x22, 0x95, 0x1b, 0x25, 0x73, 0xb2,
	0x4a, 0x51, 0x42, 0x3c, 0x99, 0x48, 0x88, 0xb4, 0x4b, 0xbc, 0x52, 0x52, 0x27, 0x90, 0x0e, 0x72,
	0x17, 0xe6, 0x7b, 0x8e, 0x6b, 0x9c, 0x91, 0x8e, 0xe3, 0xf2, 0xd5, 0x3c, 0xa5, 0x98, 0xed, 0x39,
	0xee, 0x36, 0x03, 0x22, 0x75, 0x86, 0x65, 0xbe, 0x8d, 0x63, 0x95, 0x04, 0x96, 0xf9, 0x76, 0x88,
	0x25, 0x6c, 0xaa, 0x3c, 0x7c, 0x6e, 0x58, 0x78, 0xf5, 0x64, 0xb2, 0xa6, 0xbe, 0xda, 0x84, 0x39,
	0x2a, 0x60, 0xe3, 0xb5, 0x22, 0x05, 0xb2, 0x3e, 0x4b, 0x63, 0xa4, 0xb4, 0xff, 0x51, 0x40, 0x95,
	0x35, 0x02, 0x12, 0x84, 0xce, 0xb9, 0x63, 0x99, 0x21, 0xb9, 0xca, 0x6c, 0x12, 0xc2, 0x2e, 0xa5,
	0x84, 0x5d, 0xa8, 0xa6, 0xdb, 0xc3, 0x27, 0x52, 0xec, 0xfd, 0x21, 0x1f, 0x41, 0x28, 0x89, 0xac,
	0x44, 0xb6, 0x92, 0x99, 0xc8, 0x32, 0x43, 0x72, 0x2c, 0xac, 0xee, 0xf1, 0x7a, 0xf9, 0xa4, 0xef,
	0x58, 0xa7, 0x41, 0x37, 0xbe, 0xfd, 0xee, 0x40, 0x64, 0xc1, 0x72, 0xfb, 0x2f, 0x06, 0x5a, 0x27,
	0x7a, 0xfa, 0x8d, 0x1e, 0x9d, 0xe9, 0xf5, 0x3a, 0x54, 0x98, 0xe1, 0x9b, 0xe2, 0xe0, 0x13, 0x7d,
	0xc7, 0xde, 0x92, 0xc0, 0x33, 0x79, 0xf1, 0xf4, 0x1d, 0x7b, 0x9b, 0x31, 0x8a, 0xe9, 0x55, 0x24,
	0xfb, 0x67, 0x52, 0xa9, 0x5a, 0x3f, 0x7a, 0x31, 0x66, 0x31, 0xa2, 0xbe, 0xda, 0x86, 0xeb, 0x72,
	0xa3, 0xd6, 0x70, 0x4a, 0x3c, 0x1c, 0xef, 0xe4, 0x56, 0x73, 0x62, 0x54, 0xd4, 0x37, 0x23, 0x30,
	0xed, 0x0f, 0x52, 0xad, 0xcb, 0x63, 0xc7, 0xca, 0x69, 0x5d, 0xc6, 0x24, 0x58, 0x4a, 0x48, 0x70,
	0x1d, 0x66, 0xac, 0x80, 0x98, 0x21, 0x89, 0x9f, 0x0c, 0x38, 0x48, 0xbe, 0x14, 0x79, 0xcf, 0xc4,
	0xb1, 0x3c, 0x57, 0x34, 0xb8, 0xa6, 0x11, 0xd2, 0xb2, 0x3c, 0x57, 0xfb, 0xb7, 0xe1, 0x16, 0x76,
	0xba, 0x8e, 0x8f, 0xef, 0xd8, 0x82, 0xb6, 0x4e, 0xf4, 0xbc, 0x88, 0xb7, 0xea, 0x11, 0x82, 0x6d,
	0xdc, 0xa4, 0x39, 0x96, 0xd3, 0xe6, 0x18, 0xf5, 0x6f, 0xd8, 0x59, 0xc6, 0xee, 0xdf, 0x1c, 0x3b,
	0x96, 0xe8, 0xdf, 0x1c, 0x3b, 0x16, 0x5e, 0x19, 0x22, 0x1a, 0x56, 0x86, 0xd7, 0x5b, 0x2b, 0xad,
	0xc0, 0xd8, 0x99, 0xe8, 0x3b, 0x99, 0x8a, 0xf6, 0xaf, 0x0a, 0xbc, 0x57, 0x44, 0x8b, 0xfa, 0xea,
	0x8b, 0xe1, 0x23, 0xd6, 0xea, 0x3a, 0xfe, 0x58, 0x7e, 0x2d, 0xa5, 0x1b, 0x3d, 0x61, 0x19, 0x00,
	0x4f, 0xf3, 0x7d, 0xa8, 0x75, 0xd8, 0x75, 0x61, 0xf0, 0x6c, 0x3a, 0xba, 0xf9, 0xc6, 0x21, 0x36,
	0xdf, 0x89, 0x2e, 0x1a, 0x0c, 0x13, 0x3f, 0x56, 0xa0, 0x9e, 0x78, 0x80, 0x44, 0x12, 0xcc, 0x7f,
	0xd5, 0x67, 0x3e, 0x8d, 0x8b, 0xd4, 0x18, 0x99, 0xe8, 0x44, 0x8e, 0x89, 0x56, 0xe2, 0x26, 0xca,
	0x9e, 0x02, 0x39, 0x5b, 0xa2, 0xbe, 0xd6, 0x86, 0xc6, 0xb1, 0xd9, 0x21, 0x7b, 0x24, 0xdc, 0xf3,
	0x86, 0xa2, 0x96, 0x81, 0x7b, 0x15, 0xaa, 0x3e, 0x7e, 0x18, 0xd2, 0xef, 0x49, 0x4b, 0x64, 0xe3,
	0xc3, 0x7e, 0x0f, 0x53, 0x7a, 0xfc, 0xbc, 0xc2, 0xf9, 0x61, 0x94, 0xb5, 0x33, 0xc0, 0x89, 0xf3,
	0x43, 0xa2, 0xfd, 0x1e, 0xac, 0xe5, 0x52, 0xa5, 0xbe, 0xfa, 0x7c, 0x18, 0xdc, 0x62, 0xaa, 0x1b,
	0x2d, 0xca, 0xc6, 0x17, 0x27, 0x8a, 0x40, 0xa8, 0xb7, 0x55, 0xa8, 0x5e, 0x98, 0xd4, 0xe8, 0x79,
	0xa2, 0x3b, 0x5d, 0xd5, 0xa7, 0x2e, 0x4c, 0x7a, 0xe0, 0x05, 0x44, 0xfb, 0x6f, 0x05, 0x6a, 0xe9,
	0xc5, 0x57, 0xc5, 0xea, 0xf7, 0x61, 0x21, 0xea, 0xbc, 0x89, 0x5b, 0x97, 0x1f, 0x6b, 0x4e, 0xb6,
	0xd3, 0xa2, 0xcb, 0x57, 0xee, 0x73, 0xa8, 0x1c, 0x01, 0xe1, 0x45, 0x83, 0x44, 0xdd, 0x22, 0xcb,
	0x79, 0xb1, 0x36, 0x50, 0x49, 0xd7, 0x06, 0x92, 0x95, 0xd7, 0xc9, 0x74, 0xe5, 0x35, 0x59, 0x72,
	0x9a, 0x4a, 0x95, 0x9c, 0xb4, 0xcf, 0xd1, 0x6b, 0xa2, 0x47, 0xbc, 0xe5, 0x05, 0xf6, 0xf6, 0x25,
	0x8b, 0x40, 0xba, 0xe9, 0xf2, 0x07, 0x6e, 0x32, 0x06, 0x2b, 0xa9, 0x18, 0x9c, 0xb8, 0xdc, 0x4b,
	0xc9, 0xcb, 0xfd, 0x75, 0x3c, 0x07, 0xce, 0xa0, 0x8d, 0x75, 0xa5, 0x5a, 0x2c, 0x30, 0xc7, 0xd5,
	0x3a, 0x56, 0x74, 0x5e, 0x88, 0x2d, 0x46, 0x47, 0xfa, 0x07, 0x05, 0xea, 0x6d, 0x42, 0xd3, 0x36,
	0xdb, 0xea, 0xb1, 0x93, 0x64, 0xc7, 0x68, 0xf6, 0x74, 0xe1, 0x0e, 0x30, 0xf4, 0xe1, 0x69, 0x1d,
	0xb8, 0x17, 0x48, 0x8b, 0x39, 0x0f, 0xc4, 0x6d, 0xcc, 0x15, 0x37, 0xc5, 0xc6, 0xcc, 0x09, 0x97,
	0x61, 0x32, 0xf4, 0x62, 0x39, 0x51, 0x25, 0xf4, 0xf2, 0x8a, 0x45, 0x99, 0x77, 0x2c, 0x3e, 0xb8,
	0xb3, 0xf7, 0x8b, 0x5e, 0x76, 0x7b, 0x28, 0xc0, 0x7d, 0xa7, 0x73, 0xf1, 0xc2, 0xe9, 0x5c, 0x84,
	0xa2, 0xf8, 0xaa, 0x93, 0xd7, 0x7d, 0x32, 0x8c, 0xaa, 0x85, 0xe5, 0xcb, 0xb8, 0xc5, 0x6a, 0x1f,
	0x83, 0x56, 0x44, 0x95, 0xfa, 0x9e, 0x4b, 0x09, 0x13, 0x56, 0xe8, 0x99, 0xa8, 0x0e, 0x16, 0x15,
	0xf8, 0x40, 0xdb, 0x81, 0x75, 0xf9, 0x7a, 0x92, 0x04, 0x64, 0x61, 0x48, 0xee, 0x87, 0xc9, 0x93,
	0x3b, 0x42, 0xfc, 0xdd, 0x0e, 0x3c, 0x07, 0x45, 0x25, 0xfd, 0x5c, 0x81, 0x8d, 0x7c, 0x2a, 0x82,
	0xff, 0x0f, 0x60, 0x3a, 0x2a, 0x47, 0x09, 0x93, 0xf8, 0xde, 0x88, 0x49, 0x5c, 0x45, 0x65, 0xd8,
	0x4b, 0xc0, 0xae, 0x86, 0x3e, 0x24, 0xd9, 0xf8, 0x0e, 0xcc, 0x27, 0x27, 0x99, 0x20, 0x5f, 0x91,
	0x4b, 0x29, 0xc8, 0x57, 0xe4, 0x92, 0xc9, 0x20, 0xde, 0x6c, 0xe3, 0x83, 0x8f, 0x4b, 0xbf, 0xaa,
	0x68, 0xdf, 0xc5, 0x14, 0x27, 0x59, 0x32, 0xd5, 0x4d, 0xf7, 0x95, 0xac, 0xc4, 0x26, 0xbd, 0x5d,
	0x49, 0x79, 0xbb, 0xf6, 0x23, 0xbc, 0xf7, 0xf2, 0x96, 0x53, 0x5f, 0xfd, 0x2e, 0x4c, 0x07, 0xa6,
	0xfb, 0x6a, 0xac, 0x2b, 0x2a, 0x5a, 0x58, 0x65, 0x4b, 0x64, 0x6d, 0x04, 0x97, 0xdb, 0x84, 0x5a,
	0xe2, 0xba, 0xc7, 0xc9, 0xe7, 0x84, 0x5a, 0xda, 0xdf, 0xc6, 0x3e, 0x68, 0x91, 0x0b, 0xbe, 0x22,
	0xbf, 0x9b, 0x00, 0x2e, 0x31, 0x03, 0xc3, 0x35, 0x6c, 0xf3, 0x52, 0x86, 0x75, 0x06, 0x39, 0x7c,
	0x6e, 0xa2, 0x18, 0xbb, 0x4e, 0xcf, 0x09, 0x65, 0xf9, 0x0b, 0x07, 0x6c, 0x8f, 0x2c, 0xbb, 0xa7,
	0x96, 0x17, 0x44, 0xe5, 0xaf, 0x9e, 0xe3, 0x9e, 0xb0, 0xb1, 0xf6, 0xd3, 0x52, 0x62, 0x8f, 0x18,
	0x88, 0x13, 0x89, 0xaf, 0x52, 0xf4, 0x3e, 0x49, 0xa7, 0xcc, 0x77, 0x60, 0x2e, 0x51, 0xda, 0x14,
	0x1b, 0x99, 0xbd, 0x88, 0x15, 0x35, 0xc7, 0x8a, 0xc0, 0x98, 0x3e, 0x55, 0xd2, 0xe9, 0xd3, 0x5d,
	0x90, 0x5e, 0x6d, 0x9c, 0x75, 0x62, 0xe9, 0xb2, 0xbc, 0xad, 0xb6, 0x3b, 0x2c, 0xe5, 0x8b, 0x3e,
	0xea, 0x32, 0x2d, 0x6c, 0xb4, 0x60, 0x2c, 0x9e, 0x16, 0x1f, 0x75, 0x6d, 0x71, 0xd8, 0xf0, 0xf3,
	0x30, 0x89, 0x54, 0xe5, 0x48, 0x08, 0x94, 0x48, 0xc9, 0xf4, 0x7b, 0x3a, 0x9d, 0x7e, 0xff, 0x85,
	0x12, 0x7d, 0xbc, 0xc5, 0x64, 0xb7, 0xeb, 0x04, 0x34, 0x3c, 0xee, 0x9a, 0x16, 0x6f, 0xa7, 0x3e,
	0x85, 0x89, 0x58, 0xf3, 0xe4, 0x6a, 0x25, 0x23, 0xf6, 0x15, 0x0a, 0x4e, 0xa8, 0xb2, 0x9c, 0x52,
	0xe5, 0x36, 0x06, 0xb1, 0x51, 0x5b, 0x47, 0xa7, 0x1b, 0xd3, 0x5f, 0xfe, 0x52, 0xc1, 0x98, 0x55,
	0x48, 0x84, 0xfa, 0x2c, 0x97, 0x71, 0xa8, 0x41, 0x2f, 0xbc, 0x37, 0x48, 0xa2, 0xaa, 0x4f, 0x3a,
	0xf4, 0xe4, 0xc2, 0x7b, 0x13, 0x99, 0x77, 0xac, 0xb7, 0x35, 0xa6, 0x79, 0xa3, 0xcc, 0x96, 0x61,
	0xf2, 0xe2, 0x59, 0xec, 0xfb, 0xb4, 0xca, 0xc5, 0x33, 0x96, 0x21, 0x7d, 0x0a, 0x37, 0x59, 0xec,
	0xde, 0x3a, 0x0f, 0x49, 0xb0, 0x1b, 0x90, 0xa8, 0xe3, 0xd1, 0xfb, 0xb4, 0xd7, 0x65, 0x87, 0xfa,
	0xd2, 0x06, 0xab, 0x7d, 0x04, 0xb7, 0x0a, 0x28, 0x53, 0x9f, 0xc5, 0xaa, 0xb7, 0x3d, 0xf9, 0xd1,
	0x05, 0xfb, 0xa9, 0x99, 0x70, 0xf3, 0x84, 0xb8, 0xf6, 0xb0, 0x1c, 0x4f, 0x82, 0x01, 0x19, 0x5e,
	0x15, 0xef, 0x7c, 0x4d, 0x64, 0xbc, 0x98, 0xb1, 0xc2, 0x98, 0xcb, 0x82, 0xfa, 0xda, 0x2e, 0x2c,
	0x26, 0x1b, 0x84, 0xcc, 0xaf, 0x46, 0xab, 0x39, 0xeb, 0x30, 0x23, 0x5b, 0x8e, 0xcc, 0x19, 0xc5,
	0x97, 0x0c, 0x7e, 0xb4, 0x44, 0xfb, 0x56, 0xf4, 0xdd, 0x9a, 0xa0, 0xc3, 0x53, 0x99, 0x51, 0x4a,
	0xf3, 0x50, 0xea, 0x0e, 0x04, 0x81, 0x52, 0x77, 0xa0, 0xfd, 0x42, 0x81, 0x95, 0x9c, 0x16, 0xa5,
	0x7a, 0x0a, 0xcb, 0xbc, 0xb2, 0x37, 0x64, 0x5d, 0xdc, 0xc6, 0x1a, 0x39, 0x8a, 0xae, 0x62, 0x89,
	0x2f, 0x1a, 0x63, 0xe8, 0x5b, 0x81, 0xa9, 0xde, 0xc0, 0x8f, 0x69, 0x71, 0xb2, 0x37, 0xc0, 0xfc,
	0xfc, 0x04, 0xae, 0x87, 0x9e, 0x6f, 0x04, 0xc4, 0x1a, 0x44, 0x3c, 0xbb, 0x03, 0xf1, 0x09, 0xc4,
	0xdd, 0x2b, 0xb8, 0xe1, 0x81, 0xf5, 0x5a, 0xe8, 0xf9, 0x3a, 0xb1, 0x06, 0x12, 0x38, 0xd0, 0x7e,
	0x04, 0x4b, 0xcc, 0x30, 0x0e, 0x06, 0xfe, 0x09, 0x09, 0xc3, 0x2e, 0xe9, 0x09, 0xed, 0xbe, 0x73,
	0x1d, 0x2a, 0xb6, 0xed, 0x72, 0x62, 0xdb, 0xf9, 0x61, 0x50, 0x5b, 0x81, 0xe5, 0x0c, 0xe6, 0xd4,
	0x7f, 0xf0, 0xbf, 0x0a, 0xcc, 0xc6, 0xfb, 0x14, 0xea, 0x2d, 0x58, 0xfd, 0xa4, 0xf9, 0xfc, 0x79,
	0xeb, 0x70, 0xcf, 0x38, 0x69, 0x6f, 0xed, 0x35, 0x8d, 0xd3, 0xc3, 0x93, 0xe3, 0xe6, 0x4e, 0x6b,
	0xb7, 0xd5, 0x7c, 0x5e, 0xbb, 0xa6, 0xae, 0xc3, 0x5a, 0x72, 0xfa, 0x93, 0xe6, 0x8b, 0x9d, 0xa3,
	0x83, 0xa6, 0xb1, 0x77, 0xda, 0x3c, 0x69, 0xd7, 0x14, 0xf5, 0x0e, 0xac, 0x27, 0x11, 0xb6, 0xf5,
	0xd6, 0xf3, 0xa6, 0xb1, 0xa7, 0x1f, 0x1d, 0x1d, 0x18, 0xcd, 0xc3, 0x76, 0x53, 0xaf, 0x95, 0x54,
	0x0d, 0xde, 0x4b, 0x22, 0xbd, 0x38, 0x7a, 0xd9, 0x34, 0x9e, 0x37, 0x77, 0x5e, 0x6c, 0xe9, 0x5b,
	0xed, 0xd6, 0xd1, 0x61, 0xad, 0x3c, 0xca, 0xa9, 0xf9, 0xe9, 0xce, 0xfe, 0xd6, 0xe1, 0x5e, 0xd3,
	0xd0, 0x5b, 0x87, 0x7b, 0xb5, 0x09, 0x75, 0x0d, 0x56, 0x92, 0x08, 0xfb, 0xad, 0xbd, 0xfd, 0x17,
	0xad, 0xbd, 0xfd, 0x76, 0xad, 0x32, 0x7a, 0x8c, 0x3d, 0xfd, 0xe8, 0xf4, 0xd8, 0x38, 0xde, 0x3f,
	0x6a, 0x1f, 0xd5, 0x26, 0x1f, 0x98, 0xa0, 0xc6, 0x0a, 0x86, 0xf2, 0x0b, 0xf2, 0x7b, 0x70, 0x3b,
	0x86, 0x66, 0x9c, 0x9c, 0x6e, 0x67, 0xca, 0xe0, 0x2e, 0x6c, 0x64, 0xa3, 0xe1, 0x78, 0x4f, 0xdf,
	0x3a, 0xde, 0xaf, 0x29, 0x0f, 0xfe, 0x31, 0x26, 0x59, 0xcc, 0x56, 0xe3, 0x5b, 0xda, 0x69, 0x1e,
	0xa6, 0xa9, 0xc6, 0x05, 0x87, 0xd3, 0xa3, 0x82, 0x53, 0x12, 0x42, 0x41, 0xa4, 0xa4, 0x50, 0x4a,
	0x09, 0xa1, 0x20, 0xc2, 0x50, 0x28, 0xe5, 0xd1, 0x1d, 0xc4, 0x85, 0x32, 0xf1, 0xc0, 0x8b, 0x3e,
	0x2b, 0x6b, 0x47, 0x4f, 0x94, 0x98, 0xa6, 0xda, 0xfb, 0xcd, 0x83, 0xa6, 0xd1, 0xfe, 0xec, 0x38,
	0xbd, 0xf3, 0x18, 0xcf, 0x18, 0xce, 0xae, 0xde, 0x6c, 0xd6, 0x14, 0xb5, 0x01, 0x37, 0x32, 0x26,
	0x8f, 0xb7, 0x3e, 0xab, 0x95, 0x1e, 0xff, 0xf5, 0x2d, 0x98, 0x4f, 0x5e, 0x0c, 0xaa, 0x0f, 0xcb,
	0x99, 0x5f, 0x4c, 0xa8, 0xdf, 0x18, 0x7d, 0x2b, 0xe6, 0x7c, 0x9a, 0xd1, 0x78, 0x30, 0x2e, 0x2a,
	0xf5, 0xb5, 0x6b, 0xea, 0xef, 0x44, 0xad, 0xb1, 0x0c, 0xae, 0x0f, 0xb3, 0x48, 0xe5, 0x7e, 0x19,
	0xd1, 0xd8, 0x7c, 0x17, 0x74, 0xe4, 0xfe, 0xe3, 0x58, 0x42, 0x9d, 0xd7, 0xc6, 0x57, 0x9f, 0xe6,
	0x66, 0xcf, 0x05, 0x9f, 0x14, 0x34, 0x9e, 0x7d, 0x89, 0x55, 0xb8, 0xa7, 0x2f, 0x60, 0x3e, 0xd9,
	0x21, 0x56, 0x47, 0x23, 0xec, 0xc8, 0x17, 0x00, 0x8d, 0x3b, 0x57, 0xe2, 0x20, 0xf1, 0xdf, 0x87,
	0x46, 0x7e, 0xbf, 0x56, 0xdd, 0xcc, 0x20, 0x52, 0xd0, 0x75, 0x6e, 0x3c, 0x7a, 0x27, 0x7c, 0xdc,
	0x40, 0x07, 0xd4, 0xd1, 0xf6, 0xac, 0x3a, 0xda, 0x81, 0xc8, 0xec, 0x19, 0x37, 0x7e, 0x65, 0x2c,
	0x3c, 0x64, 0xe4, 0xc3, 0x72, 0x66, 0x43, 0x36, 0xc3, 0x94, 0xf3, 0xba, 0xc2, 0x19, 0xa6, 0x9c,
	0xdf, 0xe3, 0xbd, 0xa6, 0x52, 0xb8, 0x91, 0xdd, 0x92, 0x55, 0x1f, 0xe4, 0x19, 0xe6, 0x68, 0x3b,
	0xb8, 0xf1, 0xc1, 0xd8, 0xb8, 0x91, 0x3c, 0x47, 0xe6, 0xb3, 0xe4, 0x99, 0xd5, 0x12, 0xcc, 0x92,
	0x67, 0x76, 0x27, 0x16, 0x1d, 0x35, 0xb7, 0x4b, 0x9a, 0xe1, 0xa8, 0x45, 0x7d, 0xdb, 0x0c, 0x47,
	0x2d, 0x6e, 0xc0, 0x5e, 0x53, 0x07, 0xb0, 0x92, 0xd3, 0x7c, 0x54, 0x3f, 0xc8, 0x75, 0xb4, 0x8c,
	0x03, 0x7f, 0x73, 0x7c, 0x64, 0xe4, 0xfb, 0xc7, 0x0a, 0xbe, 0x57, 0x73, 0xdb, 0x82, 0xea, 0x87,
	0x59, 0xea, 0x2a, 0xea, 0x5e, 0x36, 0x3e, 0x7a, 0xc7, 0x15, 0xd1, 0x3e, 0x8a, 0x3a, 0x6e, 0x19,
	0xfb, 0xb8, 0xa2, 0x59, 0x98, 0xb1, 0x8f, 0x2b, 0x5b, 0x7a, 0xa8, 0x87, 0x9c, 0xae, 0x5a, 0x86,
	0x1e, 0xf2, 0xdb, 0x7f, 0x19, 0x7a, 0x28, 0x6a, 0xd6, 0x49, 0xdf, 0xca, 0xe8, 0x39, 0x65, 0xfb,
	0x56, 0x76, 0x5f, 0x2c, 0xdb, 0xb7, 0x72, 0x1a, 0x59, 0x89, 0xbb, 0x29, 0xa3, 0x13, 0xf5, 0xf0,
	0x0a, 0x3f, 0x4d, 0xb6, 0x6e, 0xf2, 0xef, 0xa6, 0xec, 0x06, 0x0c, 0x0f, 0xd5, 0xf9, 0x65, 0x79,
	0xf5, 0x2a, 0x7a, 0xa9, 0x7e, 0x40, 0x46, 0xa8, 0x2e, 0xae, 0xf9, 0xf3, 0x08, 0x9a, 0x59, 0xc7,
	0xce, 0x88, 0xa0, 0x79, 0x25, 0xf8, 0x8c, 0x08, 0x9a, 0x5f, 0x1a, 0x47, 0xeb, 0xca, 0x29, 0x63,
	0x67, 0x58, 0x57, 0x7e, 0x19, 0x3d, 0xc3, 0xba, 0x0a, 0xaa, 0xe3, 0xda, 0x35, 0xf5, 0x8f, 0x14,
	0x58, 0x2b, 0x28, 0xb8, 0xaa, 0x8f, 0x0a, 0xec, 0x26, 0xab, 0xf4, 0xdb, 0xf8, 0xf0, 0xdd, 0x16,
	0x44, 0x17, 0x56, 0x56, 0x41, 0x33, 0xeb, 0xc2, 0xca, 0x29, 0xd4, 0x66, 0x5d, 0x58, 0xb9, 0x35,
	0xd2, 0x6b, 0xea, 0x9f, 0x28, 0xf1, 0x4e, 0x6e, 0xba, 0xa0, 0xa9, 0x3e, 0x2e, 0x38, 0x44, 0x4e,
	0x4d, 0xb5, 0xf1, 0xe4, 0x9d, 0xd6, 0xf0, 0x5a, 0xa3, 0x76, 0x4d, 0xfd, 0x43, 0x05, 0xea, 0x79,
	0x25, 0xc9, 0x8c, 0xd0, 0x76, 0x45, 0x25, 0x35, 0x23, 0xb4, 0x5d, 0x55, 0xef, 0x8c, 0xbc, 0x3d,
	0xbb, 0xb4, 0x98, 0xed, 0xed, 0xb9, 0x55, 0xcc, 0x6c, 0x6f, 0xcf, 0xaf, 0x5a, 0x6a, 0xd7, 0xd4,
	0x3f, 0xe7, 0x5d, 0xb8, 0x82, 0x42, 0x4d, 0xb6, 0x3e, 0x8a, 0xcb, 0x43, 0xd9, 0xfa, 0xb8, 0xa2,
	0x1a, 0x24, 0x2f, 0xfb, 0x9c, 0x82, 0x45, 0xe6, 0x65, 0x9f, 0x5f, 0x3f, 0xc9, 0xbc, 0xec, 0x8b,
	0x6a, 0x21, 0xc8, 0x3d, 0xb7, 0x88, 0x93, 0xc1, 0xbd, 0xa8, 0x94, 0x94, 0xc1, 0xbd, 0xb0, 0x3e,
	0xa4, 0x5d, 0x53, 0x6d, 0x58, 0x1c, 0x79, 0xac, 0xab, 0xf7, 0x32, 0xc9, 0xa4, 0xab, 0x09, 0x8d,
	0xf7, 0xc7, 0x41, 0x43, 0x2e, 0x27, 0x70, 0x33, 0x79, 0xf6, 0x28, 0x1c, 0x60, 0xe1, 0x45, 0x5d,
	0xdd, 0xd4, 0xe5, 0x7f, 0x98, 0xbf, 0x7c, 0xbc, 0x99, 0x08, 0x2e, 0x37, 0x12, 0x53, 0x88, 0x2e,
	0x88, 0x7e, 0x06, 0xeb, 0x39, 0x44, 0x8f, 0x02, 0x9b, 0x04, 0x2d, 0x9b, 0x16, 0xd1, 0x4d, 0x4e,
	0xc9, 0x15, 0x82, 0xf4, 0x29, 0xac, 0xed, 0x3a, 0x6f, 0x93, 0xd4, 0x91, 0x35, 0xe2, 0xa9, 0x37,
	0x13, 0x6b, 0x75, 0xe2, 0x77, 0x4d, 0x8b, 0x33, 0x1d, 0xdd, 0x71, 0xb3, 0xe7, 0x87, 0x97, 0x9c,
	0x6c, 0xe3, 0xc6, 0xff, 0xfd, 0xe4, 0x3f, 0xda, 0x8b, 0xb0, 0x90, 0xfa, 0x17, 0xfe, 0xed, 0x27,
	0x9f, 0x7f, 0xd4, 0xf1, 0xba, 0xa6, 0xdb, 0xd9, 0x7c, 0xf6, 0x38, 0x0c, 0x37, 0x2d, 0xaf, 0xf7,
	0x08, 0xff, 0xc9, 0xde, 0xf2, 0xba, 0x8f, 0xd8, 0x16, 0x1c, 0x8b, 0xd0, 0xf4, 0xff, 0xfd, 0x9f,
	0x4d, 0x22, 0xca, 0x93, 0xff, 0x0f, 0x00, 0x00, 0xff, 0xff, 0x9b, 0xc3, 0x03, 0xf6, 0x30, 0x40,
	0x00, 0x00,
}
