// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/numeric-go/numeric-go.proto

package numeric_go

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	grpc "google.golang.org/grpc"
)

// MockNumericGoClient is a mock of NumericGoClient interface.
type MockNumericGoClient struct {
	ctrl     *gomock.Controller
	recorder *MockNumericGoClientMockRecorder
}

// MockNumericGoClientMockRecorder is the mock recorder for MockNumericGoClient.
type MockNumericGoClientMockRecorder struct {
	mock *MockNumericGoClient
}

// NewMockNumericGoClient creates a new mock instance.
func NewMockNumericGoClient(ctrl *gomock.Controller) *MockNumericGoClient {
	mock := &MockNumericGoClient{ctrl: ctrl}
	mock.recorder = &MockNumericGoClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNumericGoClient) EXPECT() *MockNumericGoClientMockRecorder {
	return m.recorder
}

// AddUserNumeric mocks base method.
func (m *MockNumericGoClient) AddUserNumeric(ctx context.Context, in *AddUserNumericReq, opts ...grpc.CallOption) (*AddUserNumericResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddUserNumeric", varargs...)
	ret0, _ := ret[0].(*AddUserNumericResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserNumeric indicates an expected call of AddUserNumeric.
func (mr *MockNumericGoClientMockRecorder) AddUserNumeric(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserNumeric", reflect.TypeOf((*MockNumericGoClient)(nil).AddUserNumeric), varargs...)
}

// BatchGetPersonalNumeric mocks base method.
func (m *MockNumericGoClient) BatchGetPersonalNumeric(ctx context.Context, in *BatchGetPersonalNumericReq, opts ...grpc.CallOption) (*BatchGetPersonalNumericResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetPersonalNumeric", varargs...)
	ret0, _ := ret[0].(*BatchGetPersonalNumericResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPersonalNumeric indicates an expected call of BatchGetPersonalNumeric.
func (mr *MockNumericGoClientMockRecorder) BatchGetPersonalNumeric(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPersonalNumeric", reflect.TypeOf((*MockNumericGoClient)(nil).BatchGetPersonalNumeric), varargs...)
}

// BatchGetUserNumericLock mocks base method.
func (m *MockNumericGoClient) BatchGetUserNumericLock(ctx context.Context, in *BatchGetUserNumericLockReq, opts ...grpc.CallOption) (*BatchGetUserNumericLockResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetUserNumericLock", varargs...)
	ret0, _ := ret[0].(*BatchGetUserNumericLockResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserNumericLock indicates an expected call of BatchGetUserNumericLock.
func (mr *MockNumericGoClientMockRecorder) BatchGetUserNumericLock(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserNumericLock", reflect.TypeOf((*MockNumericGoClient)(nil).BatchGetUserNumericLock), varargs...)
}

// BatchRecordSendGiftEvent mocks base method.
func (m *MockNumericGoClient) BatchRecordSendGiftEvent(ctx context.Context, in *BatchRecordSendGiftEventReq, opts ...grpc.CallOption) (*BatchRecordSendGiftEventResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchRecordSendGiftEvent", varargs...)
	ret0, _ := ret[0].(*BatchRecordSendGiftEventResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchRecordSendGiftEvent indicates an expected call of BatchRecordSendGiftEvent.
func (mr *MockNumericGoClientMockRecorder) BatchRecordSendGiftEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchRecordSendGiftEvent", reflect.TypeOf((*MockNumericGoClient)(nil).BatchRecordSendGiftEvent), varargs...)
}

// CleanUserRichTask mocks base method.
func (m *MockNumericGoClient) CleanUserRichTask(ctx context.Context, in *CleanUserRichTaskReq, opts ...grpc.CallOption) (*CleanUserRichTaskResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CleanUserRichTask", varargs...)
	ret0, _ := ret[0].(*CleanUserRichTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CleanUserRichTask indicates an expected call of CleanUserRichTask.
func (mr *MockNumericGoClientMockRecorder) CleanUserRichTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CleanUserRichTask", reflect.TypeOf((*MockNumericGoClient)(nil).CleanUserRichTask), varargs...)
}

// CreateCleanUserRichTask mocks base method.
func (m *MockNumericGoClient) CreateCleanUserRichTask(ctx context.Context, in *CreateCleanUserRichTaskReq, opts ...grpc.CallOption) (*CreateCleanUserRichTaskResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateCleanUserRichTask", varargs...)
	ret0, _ := ret[0].(*CreateCleanUserRichTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCleanUserRichTask indicates an expected call of CreateCleanUserRichTask.
func (mr *MockNumericGoClientMockRecorder) CreateCleanUserRichTask(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCleanUserRichTask", reflect.TypeOf((*MockNumericGoClient)(nil).CreateCleanUserRichTask), varargs...)
}

// GetCleanUserRichRecords mocks base method.
func (m *MockNumericGoClient) GetCleanUserRichRecords(ctx context.Context, in *GetCleanUserRichRecordsReq, opts ...grpc.CallOption) (*GetCleanUserRichRecordsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCleanUserRichRecords", varargs...)
	ret0, _ := ret[0].(*GetCleanUserRichRecordsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCleanUserRichRecords indicates an expected call of GetCleanUserRichRecords.
func (mr *MockNumericGoClientMockRecorder) GetCleanUserRichRecords(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCleanUserRichRecords", reflect.TypeOf((*MockNumericGoClient)(nil).GetCleanUserRichRecords), varargs...)
}

// GetPersonalNumeric mocks base method.
func (m *MockNumericGoClient) GetPersonalNumeric(ctx context.Context, in *GetPersonalNumericReq, opts ...grpc.CallOption) (*GetPersonalNumericResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPersonalNumeric", varargs...)
	ret0, _ := ret[0].(*GetPersonalNumericResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersonalNumeric indicates an expected call of GetPersonalNumeric.
func (mr *MockNumericGoClientMockRecorder) GetPersonalNumeric(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalNumeric", reflect.TypeOf((*MockNumericGoClient)(nil).GetPersonalNumeric), varargs...)
}

// GetPersonalNumericV2 mocks base method.
func (m *MockNumericGoClient) GetPersonalNumericV2(ctx context.Context, in *GetPersonalNumericV2Req, opts ...grpc.CallOption) (*GetPersonalNumericV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPersonalNumericV2", varargs...)
	ret0, _ := ret[0].(*GetPersonalNumericV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersonalNumericV2 indicates an expected call of GetPersonalNumericV2.
func (mr *MockNumericGoClientMockRecorder) GetPersonalNumericV2(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalNumericV2", reflect.TypeOf((*MockNumericGoClient)(nil).GetPersonalNumericV2), varargs...)
}

// GetPresentRichOrderCount mocks base method.
func (m *MockNumericGoClient) GetPresentRichOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentRichOrderCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentRichOrderCount indicates an expected call of GetPresentRichOrderCount.
func (mr *MockNumericGoClientMockRecorder) GetPresentRichOrderCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentRichOrderCount", reflect.TypeOf((*MockNumericGoClient)(nil).GetPresentRichOrderCount), varargs...)
}

// GetPresentRichOrderList mocks base method.
func (m *MockNumericGoClient) GetPresentRichOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPresentRichOrderList", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentRichOrderList indicates an expected call of GetPresentRichOrderList.
func (mr *MockNumericGoClientMockRecorder) GetPresentRichOrderList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentRichOrderList", reflect.TypeOf((*MockNumericGoClient)(nil).GetPresentRichOrderList), varargs...)
}

// GetUserNumericLock mocks base method.
func (m *MockNumericGoClient) GetUserNumericLock(ctx context.Context, in *GetUserNumericLockReq, opts ...grpc.CallOption) (*GetUserNumericLockResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserNumericLock", varargs...)
	ret0, _ := ret[0].(*GetUserNumericLockResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserNumericLock indicates an expected call of GetUserNumericLock.
func (mr *MockNumericGoClientMockRecorder) GetUserNumericLock(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserNumericLock", reflect.TypeOf((*MockNumericGoClient)(nil).GetUserNumericLock), varargs...)
}

// GetUserRichSwitch mocks base method.
func (m *MockNumericGoClient) GetUserRichSwitch(ctx context.Context, in *GetUserRichSwitchReq, opts ...grpc.CallOption) (*GetUserRichSwitchResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserRichSwitch", varargs...)
	ret0, _ := ret[0].(*GetUserRichSwitchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserRichSwitch indicates an expected call of GetUserRichSwitch.
func (mr *MockNumericGoClientMockRecorder) GetUserRichSwitch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRichSwitch", reflect.TypeOf((*MockNumericGoClient)(nil).GetUserRichSwitch), varargs...)
}

// GetVipSince mocks base method.
func (m *MockNumericGoClient) GetVipSince(ctx context.Context, in *GetVipSinceReq, opts ...grpc.CallOption) (*GetVipSinceResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVipSince", varargs...)
	ret0, _ := ret[0].(*GetVipSinceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVipSince indicates an expected call of GetVipSince.
func (mr *MockNumericGoClientMockRecorder) GetVipSince(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVipSince", reflect.TypeOf((*MockNumericGoClient)(nil).GetVipSince), varargs...)
}

// RecordSendGiftEvent mocks base method.
func (m *MockNumericGoClient) RecordSendGiftEvent(ctx context.Context, in *RecordSendGiftEventReq, opts ...grpc.CallOption) (*RecordSendGiftEventResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecordSendGiftEvent", varargs...)
	ret0, _ := ret[0].(*RecordSendGiftEventResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordSendGiftEvent indicates an expected call of RecordSendGiftEvent.
func (mr *MockNumericGoClientMockRecorder) RecordSendGiftEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordSendGiftEvent", reflect.TypeOf((*MockNumericGoClient)(nil).RecordSendGiftEvent), varargs...)
}

// ReplacePresentRichOrder mocks base method.
func (m *MockNumericGoClient) ReplacePresentRichOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReplacePresentRichOrder", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.EmptyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReplacePresentRichOrder indicates an expected call of ReplacePresentRichOrder.
func (mr *MockNumericGoClientMockRecorder) ReplacePresentRichOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReplacePresentRichOrder", reflect.TypeOf((*MockNumericGoClient)(nil).ReplacePresentRichOrder), varargs...)
}

// SetUserNumericLock mocks base method.
func (m *MockNumericGoClient) SetUserNumericLock(ctx context.Context, in *SetUserNumericLockReq, opts ...grpc.CallOption) (*SetUserNumericLockResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserNumericLock", varargs...)
	ret0, _ := ret[0].(*SetUserNumericLockResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserNumericLock indicates an expected call of SetUserNumericLock.
func (mr *MockNumericGoClientMockRecorder) SetUserNumericLock(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserNumericLock", reflect.TypeOf((*MockNumericGoClient)(nil).SetUserNumericLock), varargs...)
}

// SetUserRichSwitch mocks base method.
func (m *MockNumericGoClient) SetUserRichSwitch(ctx context.Context, in *SetUserRichSwitchReq, opts ...grpc.CallOption) (*SetUserRichSwitchResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserRichSwitch", varargs...)
	ret0, _ := ret[0].(*SetUserRichSwitchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserRichSwitch indicates an expected call of SetUserRichSwitch.
func (mr *MockNumericGoClientMockRecorder) SetUserRichSwitch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserRichSwitch", reflect.TypeOf((*MockNumericGoClient)(nil).SetUserRichSwitch), varargs...)
}

// TriggerRichLevelBreakingNews mocks base method.
func (m *MockNumericGoClient) TriggerRichLevelBreakingNews(ctx context.Context, in *TriggerRichLevelBreakingNewsReq, opts ...grpc.CallOption) (*TriggerRichLevelBreakingNewsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TriggerRichLevelBreakingNews", varargs...)
	ret0, _ := ret[0].(*TriggerRichLevelBreakingNewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerRichLevelBreakingNews indicates an expected call of TriggerRichLevelBreakingNews.
func (mr *MockNumericGoClientMockRecorder) TriggerRichLevelBreakingNews(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerRichLevelBreakingNews", reflect.TypeOf((*MockNumericGoClient)(nil).TriggerRichLevelBreakingNews), varargs...)
}

// UseRichCard mocks base method.
func (m *MockNumericGoClient) UseRichCard(ctx context.Context, in *UseRichCardReq, opts ...grpc.CallOption) (*UseRichCardResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UseRichCard", varargs...)
	ret0, _ := ret[0].(*UseRichCardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UseRichCard indicates an expected call of UseRichCard.
func (mr *MockNumericGoClientMockRecorder) UseRichCard(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UseRichCard", reflect.TypeOf((*MockNumericGoClient)(nil).UseRichCard), varargs...)
}

// MockNumericGoServer is a mock of NumericGoServer interface.
type MockNumericGoServer struct {
	ctrl     *gomock.Controller
	recorder *MockNumericGoServerMockRecorder
}

// MockNumericGoServerMockRecorder is the mock recorder for MockNumericGoServer.
type MockNumericGoServerMockRecorder struct {
	mock *MockNumericGoServer
}

// NewMockNumericGoServer creates a new mock instance.
func NewMockNumericGoServer(ctrl *gomock.Controller) *MockNumericGoServer {
	mock := &MockNumericGoServer{ctrl: ctrl}
	mock.recorder = &MockNumericGoServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNumericGoServer) EXPECT() *MockNumericGoServerMockRecorder {
	return m.recorder
}

// AddUserNumeric mocks base method.
func (m *MockNumericGoServer) AddUserNumeric(ctx context.Context, in *AddUserNumericReq) (*AddUserNumericResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserNumeric", ctx, in)
	ret0, _ := ret[0].(*AddUserNumericResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserNumeric indicates an expected call of AddUserNumeric.
func (mr *MockNumericGoServerMockRecorder) AddUserNumeric(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserNumeric", reflect.TypeOf((*MockNumericGoServer)(nil).AddUserNumeric), ctx, in)
}

// BatchGetPersonalNumeric mocks base method.
func (m *MockNumericGoServer) BatchGetPersonalNumeric(ctx context.Context, in *BatchGetPersonalNumericReq) (*BatchGetPersonalNumericResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPersonalNumeric", ctx, in)
	ret0, _ := ret[0].(*BatchGetPersonalNumericResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPersonalNumeric indicates an expected call of BatchGetPersonalNumeric.
func (mr *MockNumericGoServerMockRecorder) BatchGetPersonalNumeric(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPersonalNumeric", reflect.TypeOf((*MockNumericGoServer)(nil).BatchGetPersonalNumeric), ctx, in)
}

// BatchGetUserNumericLock mocks base method.
func (m *MockNumericGoServer) BatchGetUserNumericLock(ctx context.Context, in *BatchGetUserNumericLockReq) (*BatchGetUserNumericLockResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserNumericLock", ctx, in)
	ret0, _ := ret[0].(*BatchGetUserNumericLockResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserNumericLock indicates an expected call of BatchGetUserNumericLock.
func (mr *MockNumericGoServerMockRecorder) BatchGetUserNumericLock(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserNumericLock", reflect.TypeOf((*MockNumericGoServer)(nil).BatchGetUserNumericLock), ctx, in)
}

// BatchRecordSendGiftEvent mocks base method.
func (m *MockNumericGoServer) BatchRecordSendGiftEvent(ctx context.Context, in *BatchRecordSendGiftEventReq) (*BatchRecordSendGiftEventResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchRecordSendGiftEvent", ctx, in)
	ret0, _ := ret[0].(*BatchRecordSendGiftEventResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchRecordSendGiftEvent indicates an expected call of BatchRecordSendGiftEvent.
func (mr *MockNumericGoServerMockRecorder) BatchRecordSendGiftEvent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchRecordSendGiftEvent", reflect.TypeOf((*MockNumericGoServer)(nil).BatchRecordSendGiftEvent), ctx, in)
}

// CleanUserRichTask mocks base method.
func (m *MockNumericGoServer) CleanUserRichTask(ctx context.Context, in *CleanUserRichTaskReq) (*CleanUserRichTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CleanUserRichTask", ctx, in)
	ret0, _ := ret[0].(*CleanUserRichTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CleanUserRichTask indicates an expected call of CleanUserRichTask.
func (mr *MockNumericGoServerMockRecorder) CleanUserRichTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CleanUserRichTask", reflect.TypeOf((*MockNumericGoServer)(nil).CleanUserRichTask), ctx, in)
}

// CreateCleanUserRichTask mocks base method.
func (m *MockNumericGoServer) CreateCleanUserRichTask(ctx context.Context, in *CreateCleanUserRichTaskReq) (*CreateCleanUserRichTaskResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCleanUserRichTask", ctx, in)
	ret0, _ := ret[0].(*CreateCleanUserRichTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCleanUserRichTask indicates an expected call of CreateCleanUserRichTask.
func (mr *MockNumericGoServerMockRecorder) CreateCleanUserRichTask(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCleanUserRichTask", reflect.TypeOf((*MockNumericGoServer)(nil).CreateCleanUserRichTask), ctx, in)
}

// GetCleanUserRichRecords mocks base method.
func (m *MockNumericGoServer) GetCleanUserRichRecords(ctx context.Context, in *GetCleanUserRichRecordsReq) (*GetCleanUserRichRecordsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCleanUserRichRecords", ctx, in)
	ret0, _ := ret[0].(*GetCleanUserRichRecordsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCleanUserRichRecords indicates an expected call of GetCleanUserRichRecords.
func (mr *MockNumericGoServerMockRecorder) GetCleanUserRichRecords(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCleanUserRichRecords", reflect.TypeOf((*MockNumericGoServer)(nil).GetCleanUserRichRecords), ctx, in)
}

// GetPersonalNumeric mocks base method.
func (m *MockNumericGoServer) GetPersonalNumeric(ctx context.Context, in *GetPersonalNumericReq) (*GetPersonalNumericResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersonalNumeric", ctx, in)
	ret0, _ := ret[0].(*GetPersonalNumericResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersonalNumeric indicates an expected call of GetPersonalNumeric.
func (mr *MockNumericGoServerMockRecorder) GetPersonalNumeric(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalNumeric", reflect.TypeOf((*MockNumericGoServer)(nil).GetPersonalNumeric), ctx, in)
}

// GetPersonalNumericV2 mocks base method.
func (m *MockNumericGoServer) GetPersonalNumericV2(ctx context.Context, in *GetPersonalNumericV2Req) (*GetPersonalNumericV2Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersonalNumericV2", ctx, in)
	ret0, _ := ret[0].(*GetPersonalNumericV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersonalNumericV2 indicates an expected call of GetPersonalNumericV2.
func (mr *MockNumericGoServerMockRecorder) GetPersonalNumericV2(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalNumericV2", reflect.TypeOf((*MockNumericGoServer)(nil).GetPersonalNumericV2), ctx, in)
}

// GetPresentRichOrderCount mocks base method.
func (m *MockNumericGoServer) GetPresentRichOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentRichOrderCount", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentRichOrderCount indicates an expected call of GetPresentRichOrderCount.
func (mr *MockNumericGoServerMockRecorder) GetPresentRichOrderCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentRichOrderCount", reflect.TypeOf((*MockNumericGoServer)(nil).GetPresentRichOrderCount), ctx, in)
}

// GetPresentRichOrderList mocks base method.
func (m *MockNumericGoServer) GetPresentRichOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentRichOrderList", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentRichOrderList indicates an expected call of GetPresentRichOrderList.
func (mr *MockNumericGoServerMockRecorder) GetPresentRichOrderList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentRichOrderList", reflect.TypeOf((*MockNumericGoServer)(nil).GetPresentRichOrderList), ctx, in)
}

// GetUserNumericLock mocks base method.
func (m *MockNumericGoServer) GetUserNumericLock(ctx context.Context, in *GetUserNumericLockReq) (*GetUserNumericLockResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserNumericLock", ctx, in)
	ret0, _ := ret[0].(*GetUserNumericLockResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserNumericLock indicates an expected call of GetUserNumericLock.
func (mr *MockNumericGoServerMockRecorder) GetUserNumericLock(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserNumericLock", reflect.TypeOf((*MockNumericGoServer)(nil).GetUserNumericLock), ctx, in)
}

// GetUserRichSwitch mocks base method.
func (m *MockNumericGoServer) GetUserRichSwitch(ctx context.Context, in *GetUserRichSwitchReq) (*GetUserRichSwitchResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRichSwitch", ctx, in)
	ret0, _ := ret[0].(*GetUserRichSwitchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserRichSwitch indicates an expected call of GetUserRichSwitch.
func (mr *MockNumericGoServerMockRecorder) GetUserRichSwitch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRichSwitch", reflect.TypeOf((*MockNumericGoServer)(nil).GetUserRichSwitch), ctx, in)
}

// GetVipSince mocks base method.
func (m *MockNumericGoServer) GetVipSince(ctx context.Context, in *GetVipSinceReq) (*GetVipSinceResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVipSince", ctx, in)
	ret0, _ := ret[0].(*GetVipSinceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVipSince indicates an expected call of GetVipSince.
func (mr *MockNumericGoServerMockRecorder) GetVipSince(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVipSince", reflect.TypeOf((*MockNumericGoServer)(nil).GetVipSince), ctx, in)
}

// RecordSendGiftEvent mocks base method.
func (m *MockNumericGoServer) RecordSendGiftEvent(ctx context.Context, in *RecordSendGiftEventReq) (*RecordSendGiftEventResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordSendGiftEvent", ctx, in)
	ret0, _ := ret[0].(*RecordSendGiftEventResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordSendGiftEvent indicates an expected call of RecordSendGiftEvent.
func (mr *MockNumericGoServerMockRecorder) RecordSendGiftEvent(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordSendGiftEvent", reflect.TypeOf((*MockNumericGoServer)(nil).RecordSendGiftEvent), ctx, in)
}

// ReplacePresentRichOrder mocks base method.
func (m *MockNumericGoServer) ReplacePresentRichOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReplacePresentRichOrder", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.EmptyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReplacePresentRichOrder indicates an expected call of ReplacePresentRichOrder.
func (mr *MockNumericGoServerMockRecorder) ReplacePresentRichOrder(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReplacePresentRichOrder", reflect.TypeOf((*MockNumericGoServer)(nil).ReplacePresentRichOrder), ctx, in)
}

// SetUserNumericLock mocks base method.
func (m *MockNumericGoServer) SetUserNumericLock(ctx context.Context, in *SetUserNumericLockReq) (*SetUserNumericLockResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserNumericLock", ctx, in)
	ret0, _ := ret[0].(*SetUserNumericLockResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserNumericLock indicates an expected call of SetUserNumericLock.
func (mr *MockNumericGoServerMockRecorder) SetUserNumericLock(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserNumericLock", reflect.TypeOf((*MockNumericGoServer)(nil).SetUserNumericLock), ctx, in)
}

// SetUserRichSwitch mocks base method.
func (m *MockNumericGoServer) SetUserRichSwitch(ctx context.Context, in *SetUserRichSwitchReq) (*SetUserRichSwitchResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserRichSwitch", ctx, in)
	ret0, _ := ret[0].(*SetUserRichSwitchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserRichSwitch indicates an expected call of SetUserRichSwitch.
func (mr *MockNumericGoServerMockRecorder) SetUserRichSwitch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserRichSwitch", reflect.TypeOf((*MockNumericGoServer)(nil).SetUserRichSwitch), ctx, in)
}

// TriggerRichLevelBreakingNews mocks base method.
func (m *MockNumericGoServer) TriggerRichLevelBreakingNews(ctx context.Context, in *TriggerRichLevelBreakingNewsReq) (*TriggerRichLevelBreakingNewsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TriggerRichLevelBreakingNews", ctx, in)
	ret0, _ := ret[0].(*TriggerRichLevelBreakingNewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerRichLevelBreakingNews indicates an expected call of TriggerRichLevelBreakingNews.
func (mr *MockNumericGoServerMockRecorder) TriggerRichLevelBreakingNews(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerRichLevelBreakingNews", reflect.TypeOf((*MockNumericGoServer)(nil).TriggerRichLevelBreakingNews), ctx, in)
}

// UseRichCard mocks base method.
func (m *MockNumericGoServer) UseRichCard(ctx context.Context, in *UseRichCardReq) (*UseRichCardResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UseRichCard", ctx, in)
	ret0, _ := ret[0].(*UseRichCardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UseRichCard indicates an expected call of UseRichCard.
func (mr *MockNumericGoServerMockRecorder) UseRichCard(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UseRichCard", reflect.TypeOf((*MockNumericGoServer)(nil).UseRichCard), ctx, in)
}
