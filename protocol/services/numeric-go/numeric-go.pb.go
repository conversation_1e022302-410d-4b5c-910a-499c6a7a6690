// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/numeric-go/numeric-go.proto

package numeric_go // import "golang.52tt.com/protocol/services/numeric-go"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 变更类型
type NumericT int32

const (
	NumericT_UnknownT NumericT = 0
	NumericT_Rich     NumericT = 1
	NumericT_Charm    NumericT = 2
)

var NumericT_name = map[int32]string{
	0: "UnknownT",
	1: "Rich",
	2: "Charm",
}
var NumericT_value = map[string]int32{
	"UnknownT": 0,
	"Rich":     1,
	"Charm":    2,
}

func (x NumericT) String() string {
	return proto.EnumName(NumericT_name, int32(x))
}
func (NumericT) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{0}
}

// 来源
type SourceT int32

const (
	SourceT_SourceUnknownT           SourceT = 0
	SourceT_SourceTPresentTBean      SourceT = 1
	SourceT_SourceTPresentRedDiamond SourceT = 2
	SourceT_SourceTRichCard          SourceT = 3
	SourceT_SourceTESport            SourceT = 4
	SourceT_SourceTBean              SourceT = 5
	SourceT_SourceTKnight            SourceT = 6
	SourceT_SourceTManual            SourceT = 7
	SourceT_SourceTCleanRich         SourceT = 8
)

var SourceT_name = map[int32]string{
	0: "SourceUnknownT",
	1: "SourceTPresentTBean",
	2: "SourceTPresentRedDiamond",
	3: "SourceTRichCard",
	4: "SourceTESport",
	5: "SourceTBean",
	6: "SourceTKnight",
	7: "SourceTManual",
	8: "SourceTCleanRich",
}
var SourceT_value = map[string]int32{
	"SourceUnknownT":           0,
	"SourceTPresentTBean":      1,
	"SourceTPresentRedDiamond": 2,
	"SourceTRichCard":          3,
	"SourceTESport":            4,
	"SourceTBean":              5,
	"SourceTKnight":            6,
	"SourceTManual":            7,
	"SourceTCleanRich":         8,
}

func (x SourceT) String() string {
	return proto.EnumName(SourceT_name, int32(x))
}
func (SourceT) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{1}
}

// 财富魅力值锁状态
type LockStatus int32

const (
	// see numeric-logic.proto LockStatus
	LockStatus_LOCK_STATUS_UNSPECIFIED LockStatus = 0
	LockStatus_LOCK_STATUS_ENABLE      LockStatus = 1
	LockStatus_LOCK_STATUS_DISABLE     LockStatus = 2
)

var LockStatus_name = map[int32]string{
	0: "LOCK_STATUS_UNSPECIFIED",
	1: "LOCK_STATUS_ENABLE",
	2: "LOCK_STATUS_DISABLE",
}
var LockStatus_value = map[string]int32{
	"LOCK_STATUS_UNSPECIFIED": 0,
	"LOCK_STATUS_ENABLE":      1,
	"LOCK_STATUS_DISABLE":     2,
}

func (x LockStatus) String() string {
	return proto.EnumName(LockStatus_name, int32(x))
}
func (LockStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{2}
}

// 签约身份类型
// from anchorcontract-go.proto.SIGN_ANCHOR_IDENTITY
type ContractIdentity int32

const (
	ContractIdentity_CONTRACT_MULTIPLAYER ContractIdentity = 0
	ContractIdentity_CONTRACT_RADIO_LIVE  ContractIdentity = 1
)

var ContractIdentity_name = map[int32]string{
	0: "CONTRACT_MULTIPLAYER",
	1: "CONTRACT_RADIO_LIVE",
}
var ContractIdentity_value = map[string]int32{
	"CONTRACT_MULTIPLAYER": 0,
	"CONTRACT_RADIO_LIVE":  1,
}

func (x ContractIdentity) String() string {
	return proto.EnumName(ContractIdentity_name, int32(x))
}
func (ContractIdentity) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{3}
}

type RichSwitchChangeType int32

const (
	RichSwitchChangeType_RICH_SWITCH_CHANGE_TYPE_UNKNOWN         RichSwitchChangeType = 0
	RichSwitchChangeType_RICH_SWITCH_CHANGE_TYPE_MANUAL          RichSwitchChangeType = 1
	RichSwitchChangeType_RICH_SWITCH_CHANGE_TYPE_CONTRACT_CANCEL RichSwitchChangeType = 2
	RichSwitchChangeType_RICH_SWITCH_CHANGE_TYPE_CONTRACT_RENEW  RichSwitchChangeType = 3
)

var RichSwitchChangeType_name = map[int32]string{
	0: "RICH_SWITCH_CHANGE_TYPE_UNKNOWN",
	1: "RICH_SWITCH_CHANGE_TYPE_MANUAL",
	2: "RICH_SWITCH_CHANGE_TYPE_CONTRACT_CANCEL",
	3: "RICH_SWITCH_CHANGE_TYPE_CONTRACT_RENEW",
}
var RichSwitchChangeType_value = map[string]int32{
	"RICH_SWITCH_CHANGE_TYPE_UNKNOWN":         0,
	"RICH_SWITCH_CHANGE_TYPE_MANUAL":          1,
	"RICH_SWITCH_CHANGE_TYPE_CONTRACT_CANCEL": 2,
	"RICH_SWITCH_CHANGE_TYPE_CONTRACT_RENEW":  3,
}

func (x RichSwitchChangeType) String() string {
	return proto.EnumName(RichSwitchChangeType_name, int32(x))
}
func (RichSwitchChangeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{4}
}

type CleanUserRichTaskReq_Operation int32

const (
	CleanUserRichTaskReq_OPERATION_APPROVAL CleanUserRichTaskReq_Operation = 0
	CleanUserRichTaskReq_OPERATION_CANCEL   CleanUserRichTaskReq_Operation = 1
)

var CleanUserRichTaskReq_Operation_name = map[int32]string{
	0: "OPERATION_APPROVAL",
	1: "OPERATION_CANCEL",
}
var CleanUserRichTaskReq_Operation_value = map[string]int32{
	"OPERATION_APPROVAL": 0,
	"OPERATION_CANCEL":   1,
}

func (x CleanUserRichTaskReq_Operation) String() string {
	return proto.EnumName(CleanUserRichTaskReq_Operation_name, int32(x))
}
func (CleanUserRichTaskReq_Operation) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{33, 0}
}

type UseRichCardReq_Opt int32

const (
	UseRichCardReq_OPT_PREPROCESS UseRichCardReq_Opt = 0
	UseRichCardReq_OPT_PROCESS    UseRichCardReq_Opt = 1
	UseRichCardReq_OPT_ROLLBACK   UseRichCardReq_Opt = 2
)

var UseRichCardReq_Opt_name = map[int32]string{
	0: "OPT_PREPROCESS",
	1: "OPT_PROCESS",
	2: "OPT_ROLLBACK",
}
var UseRichCardReq_Opt_value = map[string]int32{
	"OPT_PREPROCESS": 0,
	"OPT_PROCESS":    1,
	"OPT_ROLLBACK":   2,
}

func (x UseRichCardReq_Opt) String() string {
	return proto.EnumName(UseRichCardReq_Opt_name, int32(x))
}
func (UseRichCardReq_Opt) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{35, 0}
}

type GetPersonalNumericReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPersonalNumericReq) Reset()         { *m = GetPersonalNumericReq{} }
func (m *GetPersonalNumericReq) String() string { return proto.CompactTextString(m) }
func (*GetPersonalNumericReq) ProtoMessage()    {}
func (*GetPersonalNumericReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{0}
}
func (m *GetPersonalNumericReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPersonalNumericReq.Unmarshal(m, b)
}
func (m *GetPersonalNumericReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPersonalNumericReq.Marshal(b, m, deterministic)
}
func (dst *GetPersonalNumericReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPersonalNumericReq.Merge(dst, src)
}
func (m *GetPersonalNumericReq) XXX_Size() int {
	return xxx_messageInfo_GetPersonalNumericReq.Size(m)
}
func (m *GetPersonalNumericReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPersonalNumericReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPersonalNumericReq proto.InternalMessageInfo

func (m *GetPersonalNumericReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPersonalNumericResp struct {
	Rich                 uint64   `protobuf:"varint,1,opt,name=rich,proto3" json:"rich,omitempty"`
	Charm                uint64   `protobuf:"varint,2,opt,name=charm,proto3" json:"charm,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPersonalNumericResp) Reset()         { *m = GetPersonalNumericResp{} }
func (m *GetPersonalNumericResp) String() string { return proto.CompactTextString(m) }
func (*GetPersonalNumericResp) ProtoMessage()    {}
func (*GetPersonalNumericResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{1}
}
func (m *GetPersonalNumericResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPersonalNumericResp.Unmarshal(m, b)
}
func (m *GetPersonalNumericResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPersonalNumericResp.Marshal(b, m, deterministic)
}
func (dst *GetPersonalNumericResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPersonalNumericResp.Merge(dst, src)
}
func (m *GetPersonalNumericResp) XXX_Size() int {
	return xxx_messageInfo_GetPersonalNumericResp.Size(m)
}
func (m *GetPersonalNumericResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPersonalNumericResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPersonalNumericResp proto.InternalMessageInfo

func (m *GetPersonalNumericResp) GetRich() uint64 {
	if m != nil {
		return m.Rich
	}
	return 0
}

func (m *GetPersonalNumericResp) GetCharm() uint64 {
	if m != nil {
		return m.Charm
	}
	return 0
}

type GetPersonalNumericV2Req struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPersonalNumericV2Req) Reset()         { *m = GetPersonalNumericV2Req{} }
func (m *GetPersonalNumericV2Req) String() string { return proto.CompactTextString(m) }
func (*GetPersonalNumericV2Req) ProtoMessage()    {}
func (*GetPersonalNumericV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{2}
}
func (m *GetPersonalNumericV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPersonalNumericV2Req.Unmarshal(m, b)
}
func (m *GetPersonalNumericV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPersonalNumericV2Req.Marshal(b, m, deterministic)
}
func (dst *GetPersonalNumericV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPersonalNumericV2Req.Merge(dst, src)
}
func (m *GetPersonalNumericV2Req) XXX_Size() int {
	return xxx_messageInfo_GetPersonalNumericV2Req.Size(m)
}
func (m *GetPersonalNumericV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPersonalNumericV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetPersonalNumericV2Req proto.InternalMessageInfo

func (m *GetPersonalNumericV2Req) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPersonalNumericV2Resp struct {
	Rich                 uint64   `protobuf:"varint,1,opt,name=rich,proto3" json:"rich,omitempty"`
	Charm                uint64   `protobuf:"varint,2,opt,name=charm,proto3" json:"charm,omitempty"`
	RichLevel            *Level   `protobuf:"bytes,3,opt,name=rich_level,json=richLevel,proto3" json:"rich_level,omitempty"`
	CharmLevel           *Level   `protobuf:"bytes,4,opt,name=charm_level,json=charmLevel,proto3" json:"charm_level,omitempty"`
	VipLevel             *Level   `protobuf:"bytes,5,opt,name=vip_level,json=vipLevel,proto3" json:"vip_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPersonalNumericV2Resp) Reset()         { *m = GetPersonalNumericV2Resp{} }
func (m *GetPersonalNumericV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetPersonalNumericV2Resp) ProtoMessage()    {}
func (*GetPersonalNumericV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{3}
}
func (m *GetPersonalNumericV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPersonalNumericV2Resp.Unmarshal(m, b)
}
func (m *GetPersonalNumericV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPersonalNumericV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetPersonalNumericV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPersonalNumericV2Resp.Merge(dst, src)
}
func (m *GetPersonalNumericV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetPersonalNumericV2Resp.Size(m)
}
func (m *GetPersonalNumericV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPersonalNumericV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPersonalNumericV2Resp proto.InternalMessageInfo

func (m *GetPersonalNumericV2Resp) GetRich() uint64 {
	if m != nil {
		return m.Rich
	}
	return 0
}

func (m *GetPersonalNumericV2Resp) GetCharm() uint64 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *GetPersonalNumericV2Resp) GetRichLevel() *Level {
	if m != nil {
		return m.RichLevel
	}
	return nil
}

func (m *GetPersonalNumericV2Resp) GetCharmLevel() *Level {
	if m != nil {
		return m.CharmLevel
	}
	return nil
}

func (m *GetPersonalNumericV2Resp) GetVipLevel() *Level {
	if m != nil {
		return m.VipLevel
	}
	return nil
}

type Level struct {
	MainLevel            uint32   `protobuf:"varint,1,opt,name=main_level,json=mainLevel,proto3" json:"main_level,omitempty"`
	SubLevel             uint32   `protobuf:"varint,2,opt,name=sub_level,json=subLevel,proto3" json:"sub_level,omitempty"`
	SubLevel2            uint32   `protobuf:"varint,6,opt,name=sub_level2,json=subLevel2,proto3" json:"sub_level2,omitempty"`
	LevelText            string   `protobuf:"bytes,3,opt,name=level_text,json=levelText,proto3" json:"level_text,omitempty"`
	LevelName            string   `protobuf:"bytes,4,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`
	ServerLevel          uint64   `protobuf:"varint,5,opt,name=server_level,json=serverLevel,proto3" json:"server_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Level) Reset()         { *m = Level{} }
func (m *Level) String() string { return proto.CompactTextString(m) }
func (*Level) ProtoMessage()    {}
func (*Level) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{4}
}
func (m *Level) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Level.Unmarshal(m, b)
}
func (m *Level) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Level.Marshal(b, m, deterministic)
}
func (dst *Level) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Level.Merge(dst, src)
}
func (m *Level) XXX_Size() int {
	return xxx_messageInfo_Level.Size(m)
}
func (m *Level) XXX_DiscardUnknown() {
	xxx_messageInfo_Level.DiscardUnknown(m)
}

var xxx_messageInfo_Level proto.InternalMessageInfo

func (m *Level) GetMainLevel() uint32 {
	if m != nil {
		return m.MainLevel
	}
	return 0
}

func (m *Level) GetSubLevel() uint32 {
	if m != nil {
		return m.SubLevel
	}
	return 0
}

func (m *Level) GetSubLevel2() uint32 {
	if m != nil {
		return m.SubLevel2
	}
	return 0
}

func (m *Level) GetLevelText() string {
	if m != nil {
		return m.LevelText
	}
	return ""
}

func (m *Level) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *Level) GetServerLevel() uint64 {
	if m != nil {
		return m.ServerLevel
	}
	return 0
}

type GetVipSinceReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVipSinceReq) Reset()         { *m = GetVipSinceReq{} }
func (m *GetVipSinceReq) String() string { return proto.CompactTextString(m) }
func (*GetVipSinceReq) ProtoMessage()    {}
func (*GetVipSinceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{5}
}
func (m *GetVipSinceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVipSinceReq.Unmarshal(m, b)
}
func (m *GetVipSinceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVipSinceReq.Marshal(b, m, deterministic)
}
func (dst *GetVipSinceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVipSinceReq.Merge(dst, src)
}
func (m *GetVipSinceReq) XXX_Size() int {
	return xxx_messageInfo_GetVipSinceReq.Size(m)
}
func (m *GetVipSinceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVipSinceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetVipSinceReq proto.InternalMessageInfo

func (m *GetVipSinceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetVipSinceResp struct {
	VipLevel             *Level   `protobuf:"bytes,1,opt,name=vip_level,json=vipLevel,proto3" json:"vip_level,omitempty"`
	BeVipTime            uint64   `protobuf:"varint,2,opt,name=be_vip_time,json=beVipTime,proto3" json:"be_vip_time,omitempty"`
	BeVipDays            uint32   `protobuf:"varint,3,opt,name=be_vip_days,json=beVipDays,proto3" json:"be_vip_days,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVipSinceResp) Reset()         { *m = GetVipSinceResp{} }
func (m *GetVipSinceResp) String() string { return proto.CompactTextString(m) }
func (*GetVipSinceResp) ProtoMessage()    {}
func (*GetVipSinceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{6}
}
func (m *GetVipSinceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVipSinceResp.Unmarshal(m, b)
}
func (m *GetVipSinceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVipSinceResp.Marshal(b, m, deterministic)
}
func (dst *GetVipSinceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVipSinceResp.Merge(dst, src)
}
func (m *GetVipSinceResp) XXX_Size() int {
	return xxx_messageInfo_GetVipSinceResp.Size(m)
}
func (m *GetVipSinceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVipSinceResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetVipSinceResp proto.InternalMessageInfo

func (m *GetVipSinceResp) GetVipLevel() *Level {
	if m != nil {
		return m.VipLevel
	}
	return nil
}

func (m *GetVipSinceResp) GetBeVipTime() uint64 {
	if m != nil {
		return m.BeVipTime
	}
	return 0
}

func (m *GetVipSinceResp) GetBeVipDays() uint32 {
	if m != nil {
		return m.BeVipDays
	}
	return 0
}

type BatchGetPersonalNumericReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetPersonalNumericReq) Reset()         { *m = BatchGetPersonalNumericReq{} }
func (m *BatchGetPersonalNumericReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetPersonalNumericReq) ProtoMessage()    {}
func (*BatchGetPersonalNumericReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{7}
}
func (m *BatchGetPersonalNumericReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetPersonalNumericReq.Unmarshal(m, b)
}
func (m *BatchGetPersonalNumericReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetPersonalNumericReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetPersonalNumericReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetPersonalNumericReq.Merge(dst, src)
}
func (m *BatchGetPersonalNumericReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetPersonalNumericReq.Size(m)
}
func (m *BatchGetPersonalNumericReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetPersonalNumericReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetPersonalNumericReq proto.InternalMessageInfo

func (m *BatchGetPersonalNumericReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type PersonalNumeric struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Charm                uint64   `protobuf:"varint,2,opt,name=charm,proto3" json:"charm,omitempty"`
	Rich                 uint64   `protobuf:"varint,3,opt,name=rich,proto3" json:"rich,omitempty"`
	RichLevel            *Level   `protobuf:"bytes,5,opt,name=rich_level,json=richLevel,proto3" json:"rich_level,omitempty"`
	CharmLevel           *Level   `protobuf:"bytes,6,opt,name=charm_level,json=charmLevel,proto3" json:"charm_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PersonalNumeric) Reset()         { *m = PersonalNumeric{} }
func (m *PersonalNumeric) String() string { return proto.CompactTextString(m) }
func (*PersonalNumeric) ProtoMessage()    {}
func (*PersonalNumeric) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{8}
}
func (m *PersonalNumeric) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PersonalNumeric.Unmarshal(m, b)
}
func (m *PersonalNumeric) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PersonalNumeric.Marshal(b, m, deterministic)
}
func (dst *PersonalNumeric) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PersonalNumeric.Merge(dst, src)
}
func (m *PersonalNumeric) XXX_Size() int {
	return xxx_messageInfo_PersonalNumeric.Size(m)
}
func (m *PersonalNumeric) XXX_DiscardUnknown() {
	xxx_messageInfo_PersonalNumeric.DiscardUnknown(m)
}

var xxx_messageInfo_PersonalNumeric proto.InternalMessageInfo

func (m *PersonalNumeric) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PersonalNumeric) GetCharm() uint64 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *PersonalNumeric) GetRich() uint64 {
	if m != nil {
		return m.Rich
	}
	return 0
}

func (m *PersonalNumeric) GetRichLevel() *Level {
	if m != nil {
		return m.RichLevel
	}
	return nil
}

func (m *PersonalNumeric) GetCharmLevel() *Level {
	if m != nil {
		return m.CharmLevel
	}
	return nil
}

type BatchGetPersonalNumericResp struct {
	NumericList          []*PersonalNumeric `protobuf:"bytes,1,rep,name=numeric_list,json=numericList,proto3" json:"numeric_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchGetPersonalNumericResp) Reset()         { *m = BatchGetPersonalNumericResp{} }
func (m *BatchGetPersonalNumericResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetPersonalNumericResp) ProtoMessage()    {}
func (*BatchGetPersonalNumericResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{9}
}
func (m *BatchGetPersonalNumericResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetPersonalNumericResp.Unmarshal(m, b)
}
func (m *BatchGetPersonalNumericResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetPersonalNumericResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetPersonalNumericResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetPersonalNumericResp.Merge(dst, src)
}
func (m *BatchGetPersonalNumericResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetPersonalNumericResp.Size(m)
}
func (m *BatchGetPersonalNumericResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetPersonalNumericResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetPersonalNumericResp proto.InternalMessageInfo

func (m *BatchGetPersonalNumericResp) GetNumericList() []*PersonalNumeric {
	if m != nil {
		return m.NumericList
	}
	return nil
}

type AddUserNumericReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RichValue            uint64   `protobuf:"varint,3,opt,name=rich_value,json=richValue,proto3" json:"rich_value,omitempty"`
	CharmValue           uint64   `protobuf:"varint,4,opt,name=charm_value,json=charmValue,proto3" json:"charm_value,omitempty"`
	OrderId              string   `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserNumericReq) Reset()         { *m = AddUserNumericReq{} }
func (m *AddUserNumericReq) String() string { return proto.CompactTextString(m) }
func (*AddUserNumericReq) ProtoMessage()    {}
func (*AddUserNumericReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{10}
}
func (m *AddUserNumericReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserNumericReq.Unmarshal(m, b)
}
func (m *AddUserNumericReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserNumericReq.Marshal(b, m, deterministic)
}
func (dst *AddUserNumericReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserNumericReq.Merge(dst, src)
}
func (m *AddUserNumericReq) XXX_Size() int {
	return xxx_messageInfo_AddUserNumericReq.Size(m)
}
func (m *AddUserNumericReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserNumericReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserNumericReq proto.InternalMessageInfo

func (m *AddUserNumericReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserNumericReq) GetRichValue() uint64 {
	if m != nil {
		return m.RichValue
	}
	return 0
}

func (m *AddUserNumericReq) GetCharmValue() uint64 {
	if m != nil {
		return m.CharmValue
	}
	return 0
}

func (m *AddUserNumericReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type AddUserNumericResp struct {
	FinalRichValue       uint64   `protobuf:"varint,1,opt,name=final_rich_value,json=finalRichValue,proto3" json:"final_rich_value,omitempty"`
	FinalCharmValue      uint64   `protobuf:"varint,2,opt,name=final_charm_value,json=finalCharmValue,proto3" json:"final_charm_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserNumericResp) Reset()         { *m = AddUserNumericResp{} }
func (m *AddUserNumericResp) String() string { return proto.CompactTextString(m) }
func (*AddUserNumericResp) ProtoMessage()    {}
func (*AddUserNumericResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{11}
}
func (m *AddUserNumericResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserNumericResp.Unmarshal(m, b)
}
func (m *AddUserNumericResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserNumericResp.Marshal(b, m, deterministic)
}
func (dst *AddUserNumericResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserNumericResp.Merge(dst, src)
}
func (m *AddUserNumericResp) XXX_Size() int {
	return xxx_messageInfo_AddUserNumericResp.Size(m)
}
func (m *AddUserNumericResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserNumericResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserNumericResp proto.InternalMessageInfo

func (m *AddUserNumericResp) GetFinalRichValue() uint64 {
	if m != nil {
		return m.FinalRichValue
	}
	return 0
}

func (m *AddUserNumericResp) GetFinalCharmValue() uint64 {
	if m != nil {
		return m.FinalCharmValue
	}
	return 0
}

type GetUserRichSwitchReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRichSwitchReq) Reset()         { *m = GetUserRichSwitchReq{} }
func (m *GetUserRichSwitchReq) String() string { return proto.CompactTextString(m) }
func (*GetUserRichSwitchReq) ProtoMessage()    {}
func (*GetUserRichSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{12}
}
func (m *GetUserRichSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRichSwitchReq.Unmarshal(m, b)
}
func (m *GetUserRichSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRichSwitchReq.Marshal(b, m, deterministic)
}
func (dst *GetUserRichSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRichSwitchReq.Merge(dst, src)
}
func (m *GetUserRichSwitchReq) XXX_Size() int {
	return xxx_messageInfo_GetUserRichSwitchReq.Size(m)
}
func (m *GetUserRichSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRichSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRichSwitchReq proto.InternalMessageInfo

func (m *GetUserRichSwitchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserRichSwitchResp struct {
	Enable               bool     `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRichSwitchResp) Reset()         { *m = GetUserRichSwitchResp{} }
func (m *GetUserRichSwitchResp) String() string { return proto.CompactTextString(m) }
func (*GetUserRichSwitchResp) ProtoMessage()    {}
func (*GetUserRichSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{13}
}
func (m *GetUserRichSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRichSwitchResp.Unmarshal(m, b)
}
func (m *GetUserRichSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRichSwitchResp.Marshal(b, m, deterministic)
}
func (dst *GetUserRichSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRichSwitchResp.Merge(dst, src)
}
func (m *GetUserRichSwitchResp) XXX_Size() int {
	return xxx_messageInfo_GetUserRichSwitchResp.Size(m)
}
func (m *GetUserRichSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRichSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRichSwitchResp proto.InternalMessageInfo

func (m *GetUserRichSwitchResp) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

type UserSwitchStatus struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Enable               bool     `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserSwitchStatus) Reset()         { *m = UserSwitchStatus{} }
func (m *UserSwitchStatus) String() string { return proto.CompactTextString(m) }
func (*UserSwitchStatus) ProtoMessage()    {}
func (*UserSwitchStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{14}
}
func (m *UserSwitchStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSwitchStatus.Unmarshal(m, b)
}
func (m *UserSwitchStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSwitchStatus.Marshal(b, m, deterministic)
}
func (dst *UserSwitchStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSwitchStatus.Merge(dst, src)
}
func (m *UserSwitchStatus) XXX_Size() int {
	return xxx_messageInfo_UserSwitchStatus.Size(m)
}
func (m *UserSwitchStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSwitchStatus.DiscardUnknown(m)
}

var xxx_messageInfo_UserSwitchStatus proto.InternalMessageInfo

func (m *UserSwitchStatus) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserSwitchStatus) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

type SetUserRichSwitchReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Enable               bool     `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserRichSwitchReq) Reset()         { *m = SetUserRichSwitchReq{} }
func (m *SetUserRichSwitchReq) String() string { return proto.CompactTextString(m) }
func (*SetUserRichSwitchReq) ProtoMessage()    {}
func (*SetUserRichSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{15}
}
func (m *SetUserRichSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserRichSwitchReq.Unmarshal(m, b)
}
func (m *SetUserRichSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserRichSwitchReq.Marshal(b, m, deterministic)
}
func (dst *SetUserRichSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserRichSwitchReq.Merge(dst, src)
}
func (m *SetUserRichSwitchReq) XXX_Size() int {
	return xxx_messageInfo_SetUserRichSwitchReq.Size(m)
}
func (m *SetUserRichSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserRichSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserRichSwitchReq proto.InternalMessageInfo

func (m *SetUserRichSwitchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserRichSwitchReq) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

type SetUserRichSwitchResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserRichSwitchResp) Reset()         { *m = SetUserRichSwitchResp{} }
func (m *SetUserRichSwitchResp) String() string { return proto.CompactTextString(m) }
func (*SetUserRichSwitchResp) ProtoMessage()    {}
func (*SetUserRichSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{16}
}
func (m *SetUserRichSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserRichSwitchResp.Unmarshal(m, b)
}
func (m *SetUserRichSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserRichSwitchResp.Marshal(b, m, deterministic)
}
func (dst *SetUserRichSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserRichSwitchResp.Merge(dst, src)
}
func (m *SetUserRichSwitchResp) XXX_Size() int {
	return xxx_messageInfo_SetUserRichSwitchResp.Size(m)
}
func (m *SetUserRichSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserRichSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserRichSwitchResp proto.InternalMessageInfo

// 新财富魅力值锁
type UserNumericLock struct {
	BeanRichLock         LockStatus `protobuf:"varint,1,opt,name=bean_rich_lock,json=beanRichLock,proto3,enum=numeric_go.LockStatus" json:"bean_rich_lock,omitempty"`
	DiamondRichLock      LockStatus `protobuf:"varint,2,opt,name=diamond_rich_lock,json=diamondRichLock,proto3,enum=numeric_go.LockStatus" json:"diamond_rich_lock,omitempty"`
	BeanCharmLock        LockStatus `protobuf:"varint,3,opt,name=bean_charm_lock,json=beanCharmLock,proto3,enum=numeric_go.LockStatus" json:"bean_charm_lock,omitempty"`
	DiamondCharmLock     LockStatus `protobuf:"varint,4,opt,name=diamond_charm_lock,json=diamondCharmLock,proto3,enum=numeric_go.LockStatus" json:"diamond_charm_lock,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UserNumericLock) Reset()         { *m = UserNumericLock{} }
func (m *UserNumericLock) String() string { return proto.CompactTextString(m) }
func (*UserNumericLock) ProtoMessage()    {}
func (*UserNumericLock) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{17}
}
func (m *UserNumericLock) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserNumericLock.Unmarshal(m, b)
}
func (m *UserNumericLock) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserNumericLock.Marshal(b, m, deterministic)
}
func (dst *UserNumericLock) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserNumericLock.Merge(dst, src)
}
func (m *UserNumericLock) XXX_Size() int {
	return xxx_messageInfo_UserNumericLock.Size(m)
}
func (m *UserNumericLock) XXX_DiscardUnknown() {
	xxx_messageInfo_UserNumericLock.DiscardUnknown(m)
}

var xxx_messageInfo_UserNumericLock proto.InternalMessageInfo

func (m *UserNumericLock) GetBeanRichLock() LockStatus {
	if m != nil {
		return m.BeanRichLock
	}
	return LockStatus_LOCK_STATUS_UNSPECIFIED
}

func (m *UserNumericLock) GetDiamondRichLock() LockStatus {
	if m != nil {
		return m.DiamondRichLock
	}
	return LockStatus_LOCK_STATUS_UNSPECIFIED
}

func (m *UserNumericLock) GetBeanCharmLock() LockStatus {
	if m != nil {
		return m.BeanCharmLock
	}
	return LockStatus_LOCK_STATUS_UNSPECIFIED
}

func (m *UserNumericLock) GetDiamondCharmLock() LockStatus {
	if m != nil {
		return m.DiamondCharmLock
	}
	return LockStatus_LOCK_STATUS_UNSPECIFIED
}

// 获取用户财富魅力值锁状态
type GetUserNumericLockReq struct {
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserNumericLockReq) Reset()         { *m = GetUserNumericLockReq{} }
func (m *GetUserNumericLockReq) String() string { return proto.CompactTextString(m) }
func (*GetUserNumericLockReq) ProtoMessage()    {}
func (*GetUserNumericLockReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{18}
}
func (m *GetUserNumericLockReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserNumericLockReq.Unmarshal(m, b)
}
func (m *GetUserNumericLockReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserNumericLockReq.Marshal(b, m, deterministic)
}
func (dst *GetUserNumericLockReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserNumericLockReq.Merge(dst, src)
}
func (m *GetUserNumericLockReq) XXX_Size() int {
	return xxx_messageInfo_GetUserNumericLockReq.Size(m)
}
func (m *GetUserNumericLockReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserNumericLockReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserNumericLockReq proto.InternalMessageInfo

func (m *GetUserNumericLockReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserNumericLockResp struct {
	Locks                *UserNumericLock `protobuf:"bytes,2,opt,name=locks,proto3" json:"locks,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserNumericLockResp) Reset()         { *m = GetUserNumericLockResp{} }
func (m *GetUserNumericLockResp) String() string { return proto.CompactTextString(m) }
func (*GetUserNumericLockResp) ProtoMessage()    {}
func (*GetUserNumericLockResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{19}
}
func (m *GetUserNumericLockResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserNumericLockResp.Unmarshal(m, b)
}
func (m *GetUserNumericLockResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserNumericLockResp.Marshal(b, m, deterministic)
}
func (dst *GetUserNumericLockResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserNumericLockResp.Merge(dst, src)
}
func (m *GetUserNumericLockResp) XXX_Size() int {
	return xxx_messageInfo_GetUserNumericLockResp.Size(m)
}
func (m *GetUserNumericLockResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserNumericLockResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserNumericLockResp proto.InternalMessageInfo

func (m *GetUserNumericLockResp) GetLocks() *UserNumericLock {
	if m != nil {
		return m.Locks
	}
	return nil
}

// 设置用户财富魅力值锁状态
type SetUserNumericLockReq struct {
	Uid                  uint32           `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Locks                *UserNumericLock `protobuf:"bytes,3,opt,name=locks,proto3" json:"locks,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SetUserNumericLockReq) Reset()         { *m = SetUserNumericLockReq{} }
func (m *SetUserNumericLockReq) String() string { return proto.CompactTextString(m) }
func (*SetUserNumericLockReq) ProtoMessage()    {}
func (*SetUserNumericLockReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{20}
}
func (m *SetUserNumericLockReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserNumericLockReq.Unmarshal(m, b)
}
func (m *SetUserNumericLockReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserNumericLockReq.Marshal(b, m, deterministic)
}
func (dst *SetUserNumericLockReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserNumericLockReq.Merge(dst, src)
}
func (m *SetUserNumericLockReq) XXX_Size() int {
	return xxx_messageInfo_SetUserNumericLockReq.Size(m)
}
func (m *SetUserNumericLockReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserNumericLockReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserNumericLockReq proto.InternalMessageInfo

func (m *SetUserNumericLockReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserNumericLockReq) GetLocks() *UserNumericLock {
	if m != nil {
		return m.Locks
	}
	return nil
}

type SetUserNumericLockResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserNumericLockResp) Reset()         { *m = SetUserNumericLockResp{} }
func (m *SetUserNumericLockResp) String() string { return proto.CompactTextString(m) }
func (*SetUserNumericLockResp) ProtoMessage()    {}
func (*SetUserNumericLockResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{21}
}
func (m *SetUserNumericLockResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserNumericLockResp.Unmarshal(m, b)
}
func (m *SetUserNumericLockResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserNumericLockResp.Marshal(b, m, deterministic)
}
func (dst *SetUserNumericLockResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserNumericLockResp.Merge(dst, src)
}
func (m *SetUserNumericLockResp) XXX_Size() int {
	return xxx_messageInfo_SetUserNumericLockResp.Size(m)
}
func (m *SetUserNumericLockResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserNumericLockResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserNumericLockResp proto.InternalMessageInfo

// 批量获取用户财富魅力值锁状态
type BatchGetUserNumericLockReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserNumericLockReq) Reset()         { *m = BatchGetUserNumericLockReq{} }
func (m *BatchGetUserNumericLockReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserNumericLockReq) ProtoMessage()    {}
func (*BatchGetUserNumericLockReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{22}
}
func (m *BatchGetUserNumericLockReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserNumericLockReq.Unmarshal(m, b)
}
func (m *BatchGetUserNumericLockReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserNumericLockReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserNumericLockReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserNumericLockReq.Merge(dst, src)
}
func (m *BatchGetUserNumericLockReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserNumericLockReq.Size(m)
}
func (m *BatchGetUserNumericLockReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserNumericLockReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserNumericLockReq proto.InternalMessageInfo

func (m *BatchGetUserNumericLockReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetUserNumericLockResp struct {
	LockMap              map[uint32]*UserNumericLock `protobuf:"bytes,2,rep,name=lock_map,json=lockMap,proto3" json:"lock_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *BatchGetUserNumericLockResp) Reset()         { *m = BatchGetUserNumericLockResp{} }
func (m *BatchGetUserNumericLockResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserNumericLockResp) ProtoMessage()    {}
func (*BatchGetUserNumericLockResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{23}
}
func (m *BatchGetUserNumericLockResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserNumericLockResp.Unmarshal(m, b)
}
func (m *BatchGetUserNumericLockResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserNumericLockResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserNumericLockResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserNumericLockResp.Merge(dst, src)
}
func (m *BatchGetUserNumericLockResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserNumericLockResp.Size(m)
}
func (m *BatchGetUserNumericLockResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserNumericLockResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserNumericLockResp proto.InternalMessageInfo

func (m *BatchGetUserNumericLockResp) GetLockMap() map[uint32]*UserNumericLock {
	if m != nil {
		return m.LockMap
	}
	return nil
}

type UserGiftEventInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	AddValue             uint64   `protobuf:"varint,3,opt,name=add_value,json=addValue,proto3" json:"add_value,omitempty"`
	FinalValue           uint64   `protobuf:"varint,4,opt,name=final_value,json=finalValue,proto3" json:"final_value,omitempty"`
	LevelChange          bool     `protobuf:"varint,5,opt,name=level_change,json=levelChange,proto3" json:"level_change,omitempty"`
	BeforeValue          uint64   `protobuf:"varint,6,opt,name=before_value,json=beforeValue,proto3" json:"before_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserGiftEventInfo) Reset()         { *m = UserGiftEventInfo{} }
func (m *UserGiftEventInfo) String() string { return proto.CompactTextString(m) }
func (*UserGiftEventInfo) ProtoMessage()    {}
func (*UserGiftEventInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{24}
}
func (m *UserGiftEventInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserGiftEventInfo.Unmarshal(m, b)
}
func (m *UserGiftEventInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserGiftEventInfo.Marshal(b, m, deterministic)
}
func (dst *UserGiftEventInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserGiftEventInfo.Merge(dst, src)
}
func (m *UserGiftEventInfo) XXX_Size() int {
	return xxx_messageInfo_UserGiftEventInfo.Size(m)
}
func (m *UserGiftEventInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserGiftEventInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserGiftEventInfo proto.InternalMessageInfo

func (m *UserGiftEventInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserGiftEventInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UserGiftEventInfo) GetAddValue() uint64 {
	if m != nil {
		return m.AddValue
	}
	return 0
}

func (m *UserGiftEventInfo) GetFinalValue() uint64 {
	if m != nil {
		return m.FinalValue
	}
	return 0
}

func (m *UserGiftEventInfo) GetLevelChange() bool {
	if m != nil {
		return m.LevelChange
	}
	return false
}

func (m *UserGiftEventInfo) GetBeforeValue() uint64 {
	if m != nil {
		return m.BeforeValue
	}
	return 0
}

type RecordSendGiftEventReq struct {
	GiverUid             uint32   `protobuf:"varint,1,opt,name=giver_uid,json=giverUid,proto3" json:"giver_uid,omitempty"`
	ReceiverUid          uint32   `protobuf:"varint,2,opt,name=receiver_uid,json=receiverUid,proto3" json:"receiver_uid,omitempty"`
	RichValue            uint64   `protobuf:"varint,3,opt,name=rich_value,json=richValue,proto3" json:"rich_value,omitempty"`
	GiverGuild           uint32   `protobuf:"varint,4,opt,name=giver_guild,json=giverGuild,proto3" json:"giver_guild,omitempty"`
	ReceiverGuild        uint32   `protobuf:"varint,5,opt,name=receiver_guild,json=receiverGuild,proto3" json:"receiver_guild,omitempty"`
	CharmValue           uint64   `protobuf:"varint,6,opt,name=charm_value,json=charmValue,proto3" json:"charm_value,omitempty"`
	OrderId              string   `protobuf:"bytes,7,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,8,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelGuild         uint32   `protobuf:"varint,9,opt,name=channel_guild,json=channelGuild,proto3" json:"channel_guild,omitempty"`
	PriceType            uint32   `protobuf:"varint,10,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	GiftId               uint32   `protobuf:"varint,11,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordSendGiftEventReq) Reset()         { *m = RecordSendGiftEventReq{} }
func (m *RecordSendGiftEventReq) String() string { return proto.CompactTextString(m) }
func (*RecordSendGiftEventReq) ProtoMessage()    {}
func (*RecordSendGiftEventReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{25}
}
func (m *RecordSendGiftEventReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordSendGiftEventReq.Unmarshal(m, b)
}
func (m *RecordSendGiftEventReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordSendGiftEventReq.Marshal(b, m, deterministic)
}
func (dst *RecordSendGiftEventReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordSendGiftEventReq.Merge(dst, src)
}
func (m *RecordSendGiftEventReq) XXX_Size() int {
	return xxx_messageInfo_RecordSendGiftEventReq.Size(m)
}
func (m *RecordSendGiftEventReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordSendGiftEventReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecordSendGiftEventReq proto.InternalMessageInfo

func (m *RecordSendGiftEventReq) GetGiverUid() uint32 {
	if m != nil {
		return m.GiverUid
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetReceiverUid() uint32 {
	if m != nil {
		return m.ReceiverUid
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetRichValue() uint64 {
	if m != nil {
		return m.RichValue
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetGiverGuild() uint32 {
	if m != nil {
		return m.GiverGuild
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetReceiverGuild() uint32 {
	if m != nil {
		return m.ReceiverGuild
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetCharmValue() uint64 {
	if m != nil {
		return m.CharmValue
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *RecordSendGiftEventReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetChannelGuild() uint32 {
	if m != nil {
		return m.ChannelGuild
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *RecordSendGiftEventReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type RecordSendGiftEventResp struct {
	LevelChangeUidList   []uint32 `protobuf:"varint,1,rep,packed,name=level_change_uid_list,json=levelChangeUidList,proto3" json:"level_change_uid_list,omitempty"`
	RealCharm            uint64   `protobuf:"varint,2,opt,name=real_charm,json=realCharm,proto3" json:"real_charm,omitempty"`
	GiverCurrAllRich     uint64   `protobuf:"varint,3,opt,name=giver_curr_all_rich,json=giverCurrAllRich,proto3" json:"giver_curr_all_rich,omitempty"`
	ReceiverCurrAllCharm uint64   `protobuf:"varint,4,opt,name=receiver_curr_all_charm,json=receiverCurrAllCharm,proto3" json:"receiver_curr_all_charm,omitempty"`
	RealRich             uint64   `protobuf:"varint,5,opt,name=real_rich,json=realRich,proto3" json:"real_rich,omitempty"`
	BeforeRich           uint64   `protobuf:"varint,6,opt,name=before_rich,json=beforeRich,proto3" json:"before_rich,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordSendGiftEventResp) Reset()         { *m = RecordSendGiftEventResp{} }
func (m *RecordSendGiftEventResp) String() string { return proto.CompactTextString(m) }
func (*RecordSendGiftEventResp) ProtoMessage()    {}
func (*RecordSendGiftEventResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{26}
}
func (m *RecordSendGiftEventResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordSendGiftEventResp.Unmarshal(m, b)
}
func (m *RecordSendGiftEventResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordSendGiftEventResp.Marshal(b, m, deterministic)
}
func (dst *RecordSendGiftEventResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordSendGiftEventResp.Merge(dst, src)
}
func (m *RecordSendGiftEventResp) XXX_Size() int {
	return xxx_messageInfo_RecordSendGiftEventResp.Size(m)
}
func (m *RecordSendGiftEventResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordSendGiftEventResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecordSendGiftEventResp proto.InternalMessageInfo

func (m *RecordSendGiftEventResp) GetLevelChangeUidList() []uint32 {
	if m != nil {
		return m.LevelChangeUidList
	}
	return nil
}

func (m *RecordSendGiftEventResp) GetRealCharm() uint64 {
	if m != nil {
		return m.RealCharm
	}
	return 0
}

func (m *RecordSendGiftEventResp) GetGiverCurrAllRich() uint64 {
	if m != nil {
		return m.GiverCurrAllRich
	}
	return 0
}

func (m *RecordSendGiftEventResp) GetReceiverCurrAllCharm() uint64 {
	if m != nil {
		return m.ReceiverCurrAllCharm
	}
	return 0
}

func (m *RecordSendGiftEventResp) GetRealRich() uint64 {
	if m != nil {
		return m.RealRich
	}
	return 0
}

func (m *RecordSendGiftEventResp) GetBeforeRich() uint64 {
	if m != nil {
		return m.BeforeRich
	}
	return 0
}

// 用户批量送礼物的行为
// 仅记录红钻类型的行为 T豆行为通过MQ消费记录
type BatchRecordSendGiftEventReq struct {
	GiverUserInfo        *UserGiftEventInfo   `protobuf:"bytes,1,opt,name=giver_user_info,json=giverUserInfo,proto3" json:"giver_user_info,omitempty"`
	ReceiverUserInfoList []*UserGiftEventInfo `protobuf:"bytes,2,rep,name=receiver_user_info_list,json=receiverUserInfoList,proto3" json:"receiver_user_info_list,omitempty"`
	OrderId              string               `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ChannelId            uint32               `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelGuild         uint32               `protobuf:"varint,5,opt,name=channel_guild,json=channelGuild,proto3" json:"channel_guild,omitempty"`
	PriceType            uint32               `protobuf:"varint,6,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	GiftId               uint32               `protobuf:"varint,7,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatchRecordSendGiftEventReq) Reset()         { *m = BatchRecordSendGiftEventReq{} }
func (m *BatchRecordSendGiftEventReq) String() string { return proto.CompactTextString(m) }
func (*BatchRecordSendGiftEventReq) ProtoMessage()    {}
func (*BatchRecordSendGiftEventReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{27}
}
func (m *BatchRecordSendGiftEventReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchRecordSendGiftEventReq.Unmarshal(m, b)
}
func (m *BatchRecordSendGiftEventReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchRecordSendGiftEventReq.Marshal(b, m, deterministic)
}
func (dst *BatchRecordSendGiftEventReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchRecordSendGiftEventReq.Merge(dst, src)
}
func (m *BatchRecordSendGiftEventReq) XXX_Size() int {
	return xxx_messageInfo_BatchRecordSendGiftEventReq.Size(m)
}
func (m *BatchRecordSendGiftEventReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchRecordSendGiftEventReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchRecordSendGiftEventReq proto.InternalMessageInfo

func (m *BatchRecordSendGiftEventReq) GetGiverUserInfo() *UserGiftEventInfo {
	if m != nil {
		return m.GiverUserInfo
	}
	return nil
}

func (m *BatchRecordSendGiftEventReq) GetReceiverUserInfoList() []*UserGiftEventInfo {
	if m != nil {
		return m.ReceiverUserInfoList
	}
	return nil
}

func (m *BatchRecordSendGiftEventReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *BatchRecordSendGiftEventReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BatchRecordSendGiftEventReq) GetChannelGuild() uint32 {
	if m != nil {
		return m.ChannelGuild
	}
	return 0
}

func (m *BatchRecordSendGiftEventReq) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *BatchRecordSendGiftEventReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type BatchRecordSendGiftEventResp struct {
	GiverUserInfo        *UserGiftEventInfo   `protobuf:"bytes,1,opt,name=giver_user_info,json=giverUserInfo,proto3" json:"giver_user_info,omitempty"`
	ReceiverUserInfoList []*UserGiftEventInfo `protobuf:"bytes,2,rep,name=receiver_user_info_list,json=receiverUserInfoList,proto3" json:"receiver_user_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatchRecordSendGiftEventResp) Reset()         { *m = BatchRecordSendGiftEventResp{} }
func (m *BatchRecordSendGiftEventResp) String() string { return proto.CompactTextString(m) }
func (*BatchRecordSendGiftEventResp) ProtoMessage()    {}
func (*BatchRecordSendGiftEventResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{28}
}
func (m *BatchRecordSendGiftEventResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchRecordSendGiftEventResp.Unmarshal(m, b)
}
func (m *BatchRecordSendGiftEventResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchRecordSendGiftEventResp.Marshal(b, m, deterministic)
}
func (dst *BatchRecordSendGiftEventResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchRecordSendGiftEventResp.Merge(dst, src)
}
func (m *BatchRecordSendGiftEventResp) XXX_Size() int {
	return xxx_messageInfo_BatchRecordSendGiftEventResp.Size(m)
}
func (m *BatchRecordSendGiftEventResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchRecordSendGiftEventResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchRecordSendGiftEventResp proto.InternalMessageInfo

func (m *BatchRecordSendGiftEventResp) GetGiverUserInfo() *UserGiftEventInfo {
	if m != nil {
		return m.GiverUserInfo
	}
	return nil
}

func (m *BatchRecordSendGiftEventResp) GetReceiverUserInfoList() []*UserGiftEventInfo {
	if m != nil {
		return m.ReceiverUserInfoList
	}
	return nil
}

type GetCleanUserRichRecordsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OpTimeStart          uint32   `protobuf:"varint,2,opt,name=op_time_start,json=opTimeStart,proto3" json:"op_time_start,omitempty"`
	OpTimeEnd            uint32   `protobuf:"varint,3,opt,name=op_time_end,json=opTimeEnd,proto3" json:"op_time_end,omitempty"`
	Offset               uint32   `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCleanUserRichRecordsReq) Reset()         { *m = GetCleanUserRichRecordsReq{} }
func (m *GetCleanUserRichRecordsReq) String() string { return proto.CompactTextString(m) }
func (*GetCleanUserRichRecordsReq) ProtoMessage()    {}
func (*GetCleanUserRichRecordsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{29}
}
func (m *GetCleanUserRichRecordsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCleanUserRichRecordsReq.Unmarshal(m, b)
}
func (m *GetCleanUserRichRecordsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCleanUserRichRecordsReq.Marshal(b, m, deterministic)
}
func (dst *GetCleanUserRichRecordsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCleanUserRichRecordsReq.Merge(dst, src)
}
func (m *GetCleanUserRichRecordsReq) XXX_Size() int {
	return xxx_messageInfo_GetCleanUserRichRecordsReq.Size(m)
}
func (m *GetCleanUserRichRecordsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCleanUserRichRecordsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCleanUserRichRecordsReq proto.InternalMessageInfo

func (m *GetCleanUserRichRecordsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetCleanUserRichRecordsReq) GetOpTimeStart() uint32 {
	if m != nil {
		return m.OpTimeStart
	}
	return 0
}

func (m *GetCleanUserRichRecordsReq) GetOpTimeEnd() uint32 {
	if m != nil {
		return m.OpTimeEnd
	}
	return 0
}

func (m *GetCleanUserRichRecordsReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetCleanUserRichRecordsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetCleanUserRichRecordsResp struct {
	Records              []*GetCleanUserRichRecordsResp_CleanUserRichRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	Total                uint32                                             `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                           `json:"-"`
	XXX_unrecognized     []byte                                             `json:"-"`
	XXX_sizecache        int32                                              `json:"-"`
}

func (m *GetCleanUserRichRecordsResp) Reset()         { *m = GetCleanUserRichRecordsResp{} }
func (m *GetCleanUserRichRecordsResp) String() string { return proto.CompactTextString(m) }
func (*GetCleanUserRichRecordsResp) ProtoMessage()    {}
func (*GetCleanUserRichRecordsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{30}
}
func (m *GetCleanUserRichRecordsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCleanUserRichRecordsResp.Unmarshal(m, b)
}
func (m *GetCleanUserRichRecordsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCleanUserRichRecordsResp.Marshal(b, m, deterministic)
}
func (dst *GetCleanUserRichRecordsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCleanUserRichRecordsResp.Merge(dst, src)
}
func (m *GetCleanUserRichRecordsResp) XXX_Size() int {
	return xxx_messageInfo_GetCleanUserRichRecordsResp.Size(m)
}
func (m *GetCleanUserRichRecordsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCleanUserRichRecordsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCleanUserRichRecordsResp proto.InternalMessageInfo

func (m *GetCleanUserRichRecordsResp) GetRecords() []*GetCleanUserRichRecordsResp_CleanUserRichRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

func (m *GetCleanUserRichRecordsResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetCleanUserRichRecordsResp_CleanUserRichRecord struct {
	Uid                  uint32             `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Tid                  string             `protobuf:"bytes,2,opt,name=tid,proto3" json:"tid,omitempty"`
	Nickname             string             `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	ContractGuildId      uint32             `protobuf:"varint,4,opt,name=contract_guild_id,json=contractGuildId,proto3" json:"contract_guild_id,omitempty"`
	ContractIdentity     []ContractIdentity `protobuf:"varint,5,rep,packed,name=contract_identity,json=contractIdentity,proto3,enum=numeric_go.ContractIdentity" json:"contract_identity,omitempty"`
	ContractTime         uint32             `protobuf:"varint,6,opt,name=contract_time,json=contractTime,proto3" json:"contract_time,omitempty"`
	CurrRich             uint64             `protobuf:"varint,7,opt,name=curr_rich,json=currRich,proto3" json:"curr_rich,omitempty"`
	CurrRichLevel        *Level             `protobuf:"bytes,8,opt,name=curr_rich_level,json=currRichLevel,proto3" json:"curr_rich_level,omitempty"`
	BeforeRich           uint64             `protobuf:"varint,9,opt,name=before_rich,json=beforeRich,proto3" json:"before_rich,omitempty"`
	BeforeRichLevel      *Level             `protobuf:"bytes,10,opt,name=before_rich_level,json=beforeRichLevel,proto3" json:"before_rich_level,omitempty"`
	CleanRich            uint64             `protobuf:"varint,11,opt,name=clean_rich,json=cleanRich,proto3" json:"clean_rich,omitempty"`
	OpTime               uint32             `protobuf:"varint,12,opt,name=op_time,json=opTime,proto3" json:"op_time,omitempty"`
	Operator             string             `protobuf:"bytes,13,opt,name=operator,proto3" json:"operator,omitempty"`
	OrderId              uint32             `protobuf:"varint,14,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Filename             string             `protobuf:"bytes,15,opt,name=filename,proto3" json:"filename,omitempty"`
	FileUrl              string             `protobuf:"bytes,16,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	ContractGuildShortId uint32             `protobuf:"varint,17,opt,name=contract_guild_short_id,json=contractGuildShortId,proto3" json:"contract_guild_short_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) Reset() {
	*m = GetCleanUserRichRecordsResp_CleanUserRichRecord{}
}
func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) String() string {
	return proto.CompactTextString(m)
}
func (*GetCleanUserRichRecordsResp_CleanUserRichRecord) ProtoMessage() {}
func (*GetCleanUserRichRecordsResp_CleanUserRichRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{30, 0}
}
func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCleanUserRichRecordsResp_CleanUserRichRecord.Unmarshal(m, b)
}
func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCleanUserRichRecordsResp_CleanUserRichRecord.Marshal(b, m, deterministic)
}
func (dst *GetCleanUserRichRecordsResp_CleanUserRichRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCleanUserRichRecordsResp_CleanUserRichRecord.Merge(dst, src)
}
func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) XXX_Size() int {
	return xxx_messageInfo_GetCleanUserRichRecordsResp_CleanUserRichRecord.Size(m)
}
func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCleanUserRichRecordsResp_CleanUserRichRecord.DiscardUnknown(m)
}

var xxx_messageInfo_GetCleanUserRichRecordsResp_CleanUserRichRecord proto.InternalMessageInfo

func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) GetTid() string {
	if m != nil {
		return m.Tid
	}
	return ""
}

func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) GetContractGuildId() uint32 {
	if m != nil {
		return m.ContractGuildId
	}
	return 0
}

func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) GetContractIdentity() []ContractIdentity {
	if m != nil {
		return m.ContractIdentity
	}
	return nil
}

func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) GetContractTime() uint32 {
	if m != nil {
		return m.ContractTime
	}
	return 0
}

func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) GetCurrRich() uint64 {
	if m != nil {
		return m.CurrRich
	}
	return 0
}

func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) GetCurrRichLevel() *Level {
	if m != nil {
		return m.CurrRichLevel
	}
	return nil
}

func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) GetBeforeRich() uint64 {
	if m != nil {
		return m.BeforeRich
	}
	return 0
}

func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) GetBeforeRichLevel() *Level {
	if m != nil {
		return m.BeforeRichLevel
	}
	return nil
}

func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) GetCleanRich() uint64 {
	if m != nil {
		return m.CleanRich
	}
	return 0
}

func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) GetOpTime() uint32 {
	if m != nil {
		return m.OpTime
	}
	return 0
}

func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) GetOrderId() uint32 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) GetFilename() string {
	if m != nil {
		return m.Filename
	}
	return ""
}

func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

func (m *GetCleanUserRichRecordsResp_CleanUserRichRecord) GetContractGuildShortId() uint32 {
	if m != nil {
		return m.ContractGuildShortId
	}
	return 0
}

type CreateCleanUserRichTaskReq struct {
	Items                []*CreateCleanUserRichTaskReq_Item `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Operator             string                             `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	Filename             string                             `protobuf:"bytes,3,opt,name=filename,proto3" json:"filename,omitempty"`
	FileUrl              string                             `protobuf:"bytes,4,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *CreateCleanUserRichTaskReq) Reset()         { *m = CreateCleanUserRichTaskReq{} }
func (m *CreateCleanUserRichTaskReq) String() string { return proto.CompactTextString(m) }
func (*CreateCleanUserRichTaskReq) ProtoMessage()    {}
func (*CreateCleanUserRichTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{31}
}
func (m *CreateCleanUserRichTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateCleanUserRichTaskReq.Unmarshal(m, b)
}
func (m *CreateCleanUserRichTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateCleanUserRichTaskReq.Marshal(b, m, deterministic)
}
func (dst *CreateCleanUserRichTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateCleanUserRichTaskReq.Merge(dst, src)
}
func (m *CreateCleanUserRichTaskReq) XXX_Size() int {
	return xxx_messageInfo_CreateCleanUserRichTaskReq.Size(m)
}
func (m *CreateCleanUserRichTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateCleanUserRichTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateCleanUserRichTaskReq proto.InternalMessageInfo

func (m *CreateCleanUserRichTaskReq) GetItems() []*CreateCleanUserRichTaskReq_Item {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *CreateCleanUserRichTaskReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *CreateCleanUserRichTaskReq) GetFilename() string {
	if m != nil {
		return m.Filename
	}
	return ""
}

func (m *CreateCleanUserRichTaskReq) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

type CreateCleanUserRichTaskReq_Item struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CleanRich            uint64   `protobuf:"varint,2,opt,name=clean_rich,json=cleanRich,proto3" json:"clean_rich,omitempty"`
	CleanAll             bool     `protobuf:"varint,3,opt,name=clean_all,json=cleanAll,proto3" json:"clean_all,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateCleanUserRichTaskReq_Item) Reset()         { *m = CreateCleanUserRichTaskReq_Item{} }
func (m *CreateCleanUserRichTaskReq_Item) String() string { return proto.CompactTextString(m) }
func (*CreateCleanUserRichTaskReq_Item) ProtoMessage()    {}
func (*CreateCleanUserRichTaskReq_Item) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{31, 0}
}
func (m *CreateCleanUserRichTaskReq_Item) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateCleanUserRichTaskReq_Item.Unmarshal(m, b)
}
func (m *CreateCleanUserRichTaskReq_Item) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateCleanUserRichTaskReq_Item.Marshal(b, m, deterministic)
}
func (dst *CreateCleanUserRichTaskReq_Item) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateCleanUserRichTaskReq_Item.Merge(dst, src)
}
func (m *CreateCleanUserRichTaskReq_Item) XXX_Size() int {
	return xxx_messageInfo_CreateCleanUserRichTaskReq_Item.Size(m)
}
func (m *CreateCleanUserRichTaskReq_Item) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateCleanUserRichTaskReq_Item.DiscardUnknown(m)
}

var xxx_messageInfo_CreateCleanUserRichTaskReq_Item proto.InternalMessageInfo

func (m *CreateCleanUserRichTaskReq_Item) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CreateCleanUserRichTaskReq_Item) GetCleanRich() uint64 {
	if m != nil {
		return m.CleanRich
	}
	return 0
}

func (m *CreateCleanUserRichTaskReq_Item) GetCleanAll() bool {
	if m != nil {
		return m.CleanAll
	}
	return false
}

type CreateCleanUserRichTaskResp struct {
	OrderId              uint32                              `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Items                []*CreateCleanUserRichTaskResp_Item `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *CreateCleanUserRichTaskResp) Reset()         { *m = CreateCleanUserRichTaskResp{} }
func (m *CreateCleanUserRichTaskResp) String() string { return proto.CompactTextString(m) }
func (*CreateCleanUserRichTaskResp) ProtoMessage()    {}
func (*CreateCleanUserRichTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{32}
}
func (m *CreateCleanUserRichTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateCleanUserRichTaskResp.Unmarshal(m, b)
}
func (m *CreateCleanUserRichTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateCleanUserRichTaskResp.Marshal(b, m, deterministic)
}
func (dst *CreateCleanUserRichTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateCleanUserRichTaskResp.Merge(dst, src)
}
func (m *CreateCleanUserRichTaskResp) XXX_Size() int {
	return xxx_messageInfo_CreateCleanUserRichTaskResp.Size(m)
}
func (m *CreateCleanUserRichTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateCleanUserRichTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateCleanUserRichTaskResp proto.InternalMessageInfo

func (m *CreateCleanUserRichTaskResp) GetOrderId() uint32 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *CreateCleanUserRichTaskResp) GetItems() []*CreateCleanUserRichTaskResp_Item {
	if m != nil {
		return m.Items
	}
	return nil
}

type CreateCleanUserRichTaskResp_Item struct {
	Uid                  uint32             `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CleanRich            uint64             `protobuf:"varint,2,opt,name=clean_rich,json=cleanRich,proto3" json:"clean_rich,omitempty"`
	ContractGuildId      uint32             `protobuf:"varint,4,opt,name=contract_guild_id,json=contractGuildId,proto3" json:"contract_guild_id,omitempty"`
	ContractIdentity     []ContractIdentity `protobuf:"varint,5,rep,packed,name=contract_identity,json=contractIdentity,proto3,enum=numeric_go.ContractIdentity" json:"contract_identity,omitempty"`
	ContractTime         uint32             `protobuf:"varint,6,opt,name=contract_time,json=contractTime,proto3" json:"contract_time,omitempty"`
	ContractGuildShortId uint32             `protobuf:"varint,7,opt,name=contract_guild_short_id,json=contractGuildShortId,proto3" json:"contract_guild_short_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CreateCleanUserRichTaskResp_Item) Reset()         { *m = CreateCleanUserRichTaskResp_Item{} }
func (m *CreateCleanUserRichTaskResp_Item) String() string { return proto.CompactTextString(m) }
func (*CreateCleanUserRichTaskResp_Item) ProtoMessage()    {}
func (*CreateCleanUserRichTaskResp_Item) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{32, 0}
}
func (m *CreateCleanUserRichTaskResp_Item) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateCleanUserRichTaskResp_Item.Unmarshal(m, b)
}
func (m *CreateCleanUserRichTaskResp_Item) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateCleanUserRichTaskResp_Item.Marshal(b, m, deterministic)
}
func (dst *CreateCleanUserRichTaskResp_Item) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateCleanUserRichTaskResp_Item.Merge(dst, src)
}
func (m *CreateCleanUserRichTaskResp_Item) XXX_Size() int {
	return xxx_messageInfo_CreateCleanUserRichTaskResp_Item.Size(m)
}
func (m *CreateCleanUserRichTaskResp_Item) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateCleanUserRichTaskResp_Item.DiscardUnknown(m)
}

var xxx_messageInfo_CreateCleanUserRichTaskResp_Item proto.InternalMessageInfo

func (m *CreateCleanUserRichTaskResp_Item) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CreateCleanUserRichTaskResp_Item) GetCleanRich() uint64 {
	if m != nil {
		return m.CleanRich
	}
	return 0
}

func (m *CreateCleanUserRichTaskResp_Item) GetContractGuildId() uint32 {
	if m != nil {
		return m.ContractGuildId
	}
	return 0
}

func (m *CreateCleanUserRichTaskResp_Item) GetContractIdentity() []ContractIdentity {
	if m != nil {
		return m.ContractIdentity
	}
	return nil
}

func (m *CreateCleanUserRichTaskResp_Item) GetContractTime() uint32 {
	if m != nil {
		return m.ContractTime
	}
	return 0
}

func (m *CreateCleanUserRichTaskResp_Item) GetContractGuildShortId() uint32 {
	if m != nil {
		return m.ContractGuildShortId
	}
	return 0
}

type CleanUserRichTaskReq struct {
	OrderId              uint32                         `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Opt                  CleanUserRichTaskReq_Operation `protobuf:"varint,2,opt,name=opt,proto3,enum=numeric_go.CleanUserRichTaskReq_Operation" json:"opt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *CleanUserRichTaskReq) Reset()         { *m = CleanUserRichTaskReq{} }
func (m *CleanUserRichTaskReq) String() string { return proto.CompactTextString(m) }
func (*CleanUserRichTaskReq) ProtoMessage()    {}
func (*CleanUserRichTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{33}
}
func (m *CleanUserRichTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CleanUserRichTaskReq.Unmarshal(m, b)
}
func (m *CleanUserRichTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CleanUserRichTaskReq.Marshal(b, m, deterministic)
}
func (dst *CleanUserRichTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CleanUserRichTaskReq.Merge(dst, src)
}
func (m *CleanUserRichTaskReq) XXX_Size() int {
	return xxx_messageInfo_CleanUserRichTaskReq.Size(m)
}
func (m *CleanUserRichTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CleanUserRichTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_CleanUserRichTaskReq proto.InternalMessageInfo

func (m *CleanUserRichTaskReq) GetOrderId() uint32 {
	if m != nil {
		return m.OrderId
	}
	return 0
}

func (m *CleanUserRichTaskReq) GetOpt() CleanUserRichTaskReq_Operation {
	if m != nil {
		return m.Opt
	}
	return CleanUserRichTaskReq_OPERATION_APPROVAL
}

type CleanUserRichTaskResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CleanUserRichTaskResp) Reset()         { *m = CleanUserRichTaskResp{} }
func (m *CleanUserRichTaskResp) String() string { return proto.CompactTextString(m) }
func (*CleanUserRichTaskResp) ProtoMessage()    {}
func (*CleanUserRichTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{34}
}
func (m *CleanUserRichTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CleanUserRichTaskResp.Unmarshal(m, b)
}
func (m *CleanUserRichTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CleanUserRichTaskResp.Marshal(b, m, deterministic)
}
func (dst *CleanUserRichTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CleanUserRichTaskResp.Merge(dst, src)
}
func (m *CleanUserRichTaskResp) XXX_Size() int {
	return xxx_messageInfo_CleanUserRichTaskResp.Size(m)
}
func (m *CleanUserRichTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CleanUserRichTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_CleanUserRichTaskResp proto.InternalMessageInfo

type UseRichCardReq struct {
	OrderId              string             `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Uid                  uint32             `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	AddRich              uint64             `protobuf:"varint,3,opt,name=add_rich,json=addRich,proto3" json:"add_rich,omitempty"`
	Opt                  UseRichCardReq_Opt `protobuf:"varint,4,opt,name=opt,proto3,enum=numeric_go.UseRichCardReq_Opt" json:"opt,omitempty"`
	CardId               uint32             `protobuf:"varint,5,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	UseCount             uint32             `protobuf:"varint,6,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	CardName             string             `protobuf:"bytes,7,opt,name=card_name,json=cardName,proto3" json:"card_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UseRichCardReq) Reset()         { *m = UseRichCardReq{} }
func (m *UseRichCardReq) String() string { return proto.CompactTextString(m) }
func (*UseRichCardReq) ProtoMessage()    {}
func (*UseRichCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{35}
}
func (m *UseRichCardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UseRichCardReq.Unmarshal(m, b)
}
func (m *UseRichCardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UseRichCardReq.Marshal(b, m, deterministic)
}
func (dst *UseRichCardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UseRichCardReq.Merge(dst, src)
}
func (m *UseRichCardReq) XXX_Size() int {
	return xxx_messageInfo_UseRichCardReq.Size(m)
}
func (m *UseRichCardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UseRichCardReq.DiscardUnknown(m)
}

var xxx_messageInfo_UseRichCardReq proto.InternalMessageInfo

func (m *UseRichCardReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *UseRichCardReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UseRichCardReq) GetAddRich() uint64 {
	if m != nil {
		return m.AddRich
	}
	return 0
}

func (m *UseRichCardReq) GetOpt() UseRichCardReq_Opt {
	if m != nil {
		return m.Opt
	}
	return UseRichCardReq_OPT_PREPROCESS
}

func (m *UseRichCardReq) GetCardId() uint32 {
	if m != nil {
		return m.CardId
	}
	return 0
}

func (m *UseRichCardReq) GetUseCount() uint32 {
	if m != nil {
		return m.UseCount
	}
	return 0
}

func (m *UseRichCardReq) GetCardName() string {
	if m != nil {
		return m.CardName
	}
	return ""
}

type UseRichCardResp struct {
	AddRich              uint64   `protobuf:"varint,1,opt,name=add_rich,json=addRich,proto3" json:"add_rich,omitempty"`
	FinalRich            uint64   `protobuf:"varint,2,opt,name=final_rich,json=finalRich,proto3" json:"final_rich,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UseRichCardResp) Reset()         { *m = UseRichCardResp{} }
func (m *UseRichCardResp) String() string { return proto.CompactTextString(m) }
func (*UseRichCardResp) ProtoMessage()    {}
func (*UseRichCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{36}
}
func (m *UseRichCardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UseRichCardResp.Unmarshal(m, b)
}
func (m *UseRichCardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UseRichCardResp.Marshal(b, m, deterministic)
}
func (dst *UseRichCardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UseRichCardResp.Merge(dst, src)
}
func (m *UseRichCardResp) XXX_Size() int {
	return xxx_messageInfo_UseRichCardResp.Size(m)
}
func (m *UseRichCardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UseRichCardResp.DiscardUnknown(m)
}

var xxx_messageInfo_UseRichCardResp proto.InternalMessageInfo

func (m *UseRichCardResp) GetAddRich() uint64 {
	if m != nil {
		return m.AddRich
	}
	return 0
}

func (m *UseRichCardResp) GetFinalRich() uint64 {
	if m != nil {
		return m.FinalRich
	}
	return 0
}

// 触发财富等级升级全服公告
type TriggerRichLevelBreakingNewsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AfterRich            uint64   `protobuf:"varint,3,opt,name=after_rich,json=afterRich,proto3" json:"after_rich,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TriggerRichLevelBreakingNewsReq) Reset()         { *m = TriggerRichLevelBreakingNewsReq{} }
func (m *TriggerRichLevelBreakingNewsReq) String() string { return proto.CompactTextString(m) }
func (*TriggerRichLevelBreakingNewsReq) ProtoMessage()    {}
func (*TriggerRichLevelBreakingNewsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{37}
}
func (m *TriggerRichLevelBreakingNewsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TriggerRichLevelBreakingNewsReq.Unmarshal(m, b)
}
func (m *TriggerRichLevelBreakingNewsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TriggerRichLevelBreakingNewsReq.Marshal(b, m, deterministic)
}
func (dst *TriggerRichLevelBreakingNewsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TriggerRichLevelBreakingNewsReq.Merge(dst, src)
}
func (m *TriggerRichLevelBreakingNewsReq) XXX_Size() int {
	return xxx_messageInfo_TriggerRichLevelBreakingNewsReq.Size(m)
}
func (m *TriggerRichLevelBreakingNewsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TriggerRichLevelBreakingNewsReq.DiscardUnknown(m)
}

var xxx_messageInfo_TriggerRichLevelBreakingNewsReq proto.InternalMessageInfo

func (m *TriggerRichLevelBreakingNewsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TriggerRichLevelBreakingNewsReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TriggerRichLevelBreakingNewsReq) GetAfterRich() uint64 {
	if m != nil {
		return m.AfterRich
	}
	return 0
}

type TriggerRichLevelBreakingNewsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TriggerRichLevelBreakingNewsResp) Reset()         { *m = TriggerRichLevelBreakingNewsResp{} }
func (m *TriggerRichLevelBreakingNewsResp) String() string { return proto.CompactTextString(m) }
func (*TriggerRichLevelBreakingNewsResp) ProtoMessage()    {}
func (*TriggerRichLevelBreakingNewsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_go_7aaeda62c8bfb2aa, []int{38}
}
func (m *TriggerRichLevelBreakingNewsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TriggerRichLevelBreakingNewsResp.Unmarshal(m, b)
}
func (m *TriggerRichLevelBreakingNewsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TriggerRichLevelBreakingNewsResp.Marshal(b, m, deterministic)
}
func (dst *TriggerRichLevelBreakingNewsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TriggerRichLevelBreakingNewsResp.Merge(dst, src)
}
func (m *TriggerRichLevelBreakingNewsResp) XXX_Size() int {
	return xxx_messageInfo_TriggerRichLevelBreakingNewsResp.Size(m)
}
func (m *TriggerRichLevelBreakingNewsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TriggerRichLevelBreakingNewsResp.DiscardUnknown(m)
}

var xxx_messageInfo_TriggerRichLevelBreakingNewsResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*GetPersonalNumericReq)(nil), "numeric_go.GetPersonalNumericReq")
	proto.RegisterType((*GetPersonalNumericResp)(nil), "numeric_go.GetPersonalNumericResp")
	proto.RegisterType((*GetPersonalNumericV2Req)(nil), "numeric_go.GetPersonalNumericV2Req")
	proto.RegisterType((*GetPersonalNumericV2Resp)(nil), "numeric_go.GetPersonalNumericV2Resp")
	proto.RegisterType((*Level)(nil), "numeric_go.Level")
	proto.RegisterType((*GetVipSinceReq)(nil), "numeric_go.GetVipSinceReq")
	proto.RegisterType((*GetVipSinceResp)(nil), "numeric_go.GetVipSinceResp")
	proto.RegisterType((*BatchGetPersonalNumericReq)(nil), "numeric_go.BatchGetPersonalNumericReq")
	proto.RegisterType((*PersonalNumeric)(nil), "numeric_go.PersonalNumeric")
	proto.RegisterType((*BatchGetPersonalNumericResp)(nil), "numeric_go.BatchGetPersonalNumericResp")
	proto.RegisterType((*AddUserNumericReq)(nil), "numeric_go.AddUserNumericReq")
	proto.RegisterType((*AddUserNumericResp)(nil), "numeric_go.AddUserNumericResp")
	proto.RegisterType((*GetUserRichSwitchReq)(nil), "numeric_go.GetUserRichSwitchReq")
	proto.RegisterType((*GetUserRichSwitchResp)(nil), "numeric_go.GetUserRichSwitchResp")
	proto.RegisterType((*UserSwitchStatus)(nil), "numeric_go.UserSwitchStatus")
	proto.RegisterType((*SetUserRichSwitchReq)(nil), "numeric_go.SetUserRichSwitchReq")
	proto.RegisterType((*SetUserRichSwitchResp)(nil), "numeric_go.SetUserRichSwitchResp")
	proto.RegisterType((*UserNumericLock)(nil), "numeric_go.UserNumericLock")
	proto.RegisterType((*GetUserNumericLockReq)(nil), "numeric_go.GetUserNumericLockReq")
	proto.RegisterType((*GetUserNumericLockResp)(nil), "numeric_go.GetUserNumericLockResp")
	proto.RegisterType((*SetUserNumericLockReq)(nil), "numeric_go.SetUserNumericLockReq")
	proto.RegisterType((*SetUserNumericLockResp)(nil), "numeric_go.SetUserNumericLockResp")
	proto.RegisterType((*BatchGetUserNumericLockReq)(nil), "numeric_go.BatchGetUserNumericLockReq")
	proto.RegisterType((*BatchGetUserNumericLockResp)(nil), "numeric_go.BatchGetUserNumericLockResp")
	proto.RegisterMapType((map[uint32]*UserNumericLock)(nil), "numeric_go.BatchGetUserNumericLockResp.LockMapEntry")
	proto.RegisterType((*UserGiftEventInfo)(nil), "numeric_go.UserGiftEventInfo")
	proto.RegisterType((*RecordSendGiftEventReq)(nil), "numeric_go.RecordSendGiftEventReq")
	proto.RegisterType((*RecordSendGiftEventResp)(nil), "numeric_go.RecordSendGiftEventResp")
	proto.RegisterType((*BatchRecordSendGiftEventReq)(nil), "numeric_go.BatchRecordSendGiftEventReq")
	proto.RegisterType((*BatchRecordSendGiftEventResp)(nil), "numeric_go.BatchRecordSendGiftEventResp")
	proto.RegisterType((*GetCleanUserRichRecordsReq)(nil), "numeric_go.GetCleanUserRichRecordsReq")
	proto.RegisterType((*GetCleanUserRichRecordsResp)(nil), "numeric_go.GetCleanUserRichRecordsResp")
	proto.RegisterType((*GetCleanUserRichRecordsResp_CleanUserRichRecord)(nil), "numeric_go.GetCleanUserRichRecordsResp.CleanUserRichRecord")
	proto.RegisterType((*CreateCleanUserRichTaskReq)(nil), "numeric_go.CreateCleanUserRichTaskReq")
	proto.RegisterType((*CreateCleanUserRichTaskReq_Item)(nil), "numeric_go.CreateCleanUserRichTaskReq.Item")
	proto.RegisterType((*CreateCleanUserRichTaskResp)(nil), "numeric_go.CreateCleanUserRichTaskResp")
	proto.RegisterType((*CreateCleanUserRichTaskResp_Item)(nil), "numeric_go.CreateCleanUserRichTaskResp.Item")
	proto.RegisterType((*CleanUserRichTaskReq)(nil), "numeric_go.CleanUserRichTaskReq")
	proto.RegisterType((*CleanUserRichTaskResp)(nil), "numeric_go.CleanUserRichTaskResp")
	proto.RegisterType((*UseRichCardReq)(nil), "numeric_go.UseRichCardReq")
	proto.RegisterType((*UseRichCardResp)(nil), "numeric_go.UseRichCardResp")
	proto.RegisterType((*TriggerRichLevelBreakingNewsReq)(nil), "numeric_go.TriggerRichLevelBreakingNewsReq")
	proto.RegisterType((*TriggerRichLevelBreakingNewsResp)(nil), "numeric_go.TriggerRichLevelBreakingNewsResp")
	proto.RegisterEnum("numeric_go.NumericT", NumericT_name, NumericT_value)
	proto.RegisterEnum("numeric_go.SourceT", SourceT_name, SourceT_value)
	proto.RegisterEnum("numeric_go.LockStatus", LockStatus_name, LockStatus_value)
	proto.RegisterEnum("numeric_go.ContractIdentity", ContractIdentity_name, ContractIdentity_value)
	proto.RegisterEnum("numeric_go.RichSwitchChangeType", RichSwitchChangeType_name, RichSwitchChangeType_value)
	proto.RegisterEnum("numeric_go.CleanUserRichTaskReq_Operation", CleanUserRichTaskReq_Operation_name, CleanUserRichTaskReq_Operation_value)
	proto.RegisterEnum("numeric_go.UseRichCardReq_Opt", UseRichCardReq_Opt_name, UseRichCardReq_Opt_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// NumericGoClient is the client API for NumericGo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type NumericGoClient interface {
	// 获取财富值魅力值
	GetPersonalNumeric(ctx context.Context, in *GetPersonalNumericReq, opts ...grpc.CallOption) (*GetPersonalNumericResp, error)
	// 获取财富值、魅力值、财富等级、魅力等级、VIP等级
	GetPersonalNumericV2(ctx context.Context, in *GetPersonalNumericV2Req, opts ...grpc.CallOption) (*GetPersonalNumericV2Resp, error)
	// 批量获取财富值魅力值
	BatchGetPersonalNumeric(ctx context.Context, in *BatchGetPersonalNumericReq, opts ...grpc.CallOption) (*BatchGetPersonalNumericResp, error)
	// 增加财富值魅力值
	AddUserNumeric(ctx context.Context, in *AddUserNumericReq, opts ...grpc.CallOption) (*AddUserNumericResp, error)
	// 获取成为VIP的时间
	GetVipSince(ctx context.Context, in *GetVipSinceReq, opts ...grpc.CallOption) (*GetVipSinceResp, error)
	// 获取财富值控制开关状态（废弃）
	GetUserRichSwitch(ctx context.Context, in *GetUserRichSwitchReq, opts ...grpc.CallOption) (*GetUserRichSwitchResp, error)
	// 设置是否打开关闭财富值累加（废弃）
	SetUserRichSwitch(ctx context.Context, in *SetUserRichSwitchReq, opts ...grpc.CallOption) (*SetUserRichSwitchResp, error)
	// 获取用户财富魅力值锁状态
	GetUserNumericLock(ctx context.Context, in *GetUserNumericLockReq, opts ...grpc.CallOption) (*GetUserNumericLockResp, error)
	// 设置用户财富魅力值锁状态
	SetUserNumericLock(ctx context.Context, in *SetUserNumericLockReq, opts ...grpc.CallOption) (*SetUserNumericLockResp, error)
	// 批量获取用户财富魅力值锁状态
	BatchGetUserNumericLock(ctx context.Context, in *BatchGetUserNumericLockReq, opts ...grpc.CallOption) (*BatchGetUserNumericLockResp, error)
	// 送礼增加财富值
	RecordSendGiftEvent(ctx context.Context, in *RecordSendGiftEventReq, opts ...grpc.CallOption) (*RecordSendGiftEventResp, error)
	// 批量送礼增加财富值
	BatchRecordSendGiftEvent(ctx context.Context, in *BatchRecordSendGiftEventReq, opts ...grpc.CallOption) (*BatchRecordSendGiftEventResp, error)
	// 获取清理财富值记录
	GetCleanUserRichRecords(ctx context.Context, in *GetCleanUserRichRecordsReq, opts ...grpc.CallOption) (*GetCleanUserRichRecordsResp, error)
	// 创建清除财富值任务
	CreateCleanUserRichTask(ctx context.Context, in *CreateCleanUserRichTaskReq, opts ...grpc.CallOption) (*CreateCleanUserRichTaskResp, error)
	// 执行清理财富值
	CleanUserRichTask(ctx context.Context, in *CleanUserRichTaskReq, opts ...grpc.CallOption) (*CleanUserRichTaskResp, error)
	// 背包使用财富卡
	UseRichCard(ctx context.Context, in *UseRichCardReq, opts ...grpc.CallOption) (*UseRichCardResp, error)
	// 触发财富等级升级全服公告
	TriggerRichLevelBreakingNews(ctx context.Context, in *TriggerRichLevelBreakingNewsReq, opts ...grpc.CallOption) (*TriggerRichLevelBreakingNewsResp, error)
	GetPresentRichOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetPresentRichOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	ReplacePresentRichOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
}

type numericGoClient struct {
	cc *grpc.ClientConn
}

func NewNumericGoClient(cc *grpc.ClientConn) NumericGoClient {
	return &numericGoClient{cc}
}

func (c *numericGoClient) GetPersonalNumeric(ctx context.Context, in *GetPersonalNumericReq, opts ...grpc.CallOption) (*GetPersonalNumericResp, error) {
	out := new(GetPersonalNumericResp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/GetPersonalNumeric", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) GetPersonalNumericV2(ctx context.Context, in *GetPersonalNumericV2Req, opts ...grpc.CallOption) (*GetPersonalNumericV2Resp, error) {
	out := new(GetPersonalNumericV2Resp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/GetPersonalNumericV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) BatchGetPersonalNumeric(ctx context.Context, in *BatchGetPersonalNumericReq, opts ...grpc.CallOption) (*BatchGetPersonalNumericResp, error) {
	out := new(BatchGetPersonalNumericResp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/BatchGetPersonalNumeric", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) AddUserNumeric(ctx context.Context, in *AddUserNumericReq, opts ...grpc.CallOption) (*AddUserNumericResp, error) {
	out := new(AddUserNumericResp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/AddUserNumeric", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) GetVipSince(ctx context.Context, in *GetVipSinceReq, opts ...grpc.CallOption) (*GetVipSinceResp, error) {
	out := new(GetVipSinceResp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/GetVipSince", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) GetUserRichSwitch(ctx context.Context, in *GetUserRichSwitchReq, opts ...grpc.CallOption) (*GetUserRichSwitchResp, error) {
	out := new(GetUserRichSwitchResp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/GetUserRichSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) SetUserRichSwitch(ctx context.Context, in *SetUserRichSwitchReq, opts ...grpc.CallOption) (*SetUserRichSwitchResp, error) {
	out := new(SetUserRichSwitchResp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/SetUserRichSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) GetUserNumericLock(ctx context.Context, in *GetUserNumericLockReq, opts ...grpc.CallOption) (*GetUserNumericLockResp, error) {
	out := new(GetUserNumericLockResp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/GetUserNumericLock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) SetUserNumericLock(ctx context.Context, in *SetUserNumericLockReq, opts ...grpc.CallOption) (*SetUserNumericLockResp, error) {
	out := new(SetUserNumericLockResp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/SetUserNumericLock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) BatchGetUserNumericLock(ctx context.Context, in *BatchGetUserNumericLockReq, opts ...grpc.CallOption) (*BatchGetUserNumericLockResp, error) {
	out := new(BatchGetUserNumericLockResp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/BatchGetUserNumericLock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) RecordSendGiftEvent(ctx context.Context, in *RecordSendGiftEventReq, opts ...grpc.CallOption) (*RecordSendGiftEventResp, error) {
	out := new(RecordSendGiftEventResp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/RecordSendGiftEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) BatchRecordSendGiftEvent(ctx context.Context, in *BatchRecordSendGiftEventReq, opts ...grpc.CallOption) (*BatchRecordSendGiftEventResp, error) {
	out := new(BatchRecordSendGiftEventResp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/BatchRecordSendGiftEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) GetCleanUserRichRecords(ctx context.Context, in *GetCleanUserRichRecordsReq, opts ...grpc.CallOption) (*GetCleanUserRichRecordsResp, error) {
	out := new(GetCleanUserRichRecordsResp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/GetCleanUserRichRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) CreateCleanUserRichTask(ctx context.Context, in *CreateCleanUserRichTaskReq, opts ...grpc.CallOption) (*CreateCleanUserRichTaskResp, error) {
	out := new(CreateCleanUserRichTaskResp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/CreateCleanUserRichTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) CleanUserRichTask(ctx context.Context, in *CleanUserRichTaskReq, opts ...grpc.CallOption) (*CleanUserRichTaskResp, error) {
	out := new(CleanUserRichTaskResp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/CleanUserRichTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) UseRichCard(ctx context.Context, in *UseRichCardReq, opts ...grpc.CallOption) (*UseRichCardResp, error) {
	out := new(UseRichCardResp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/UseRichCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) TriggerRichLevelBreakingNews(ctx context.Context, in *TriggerRichLevelBreakingNewsReq, opts ...grpc.CallOption) (*TriggerRichLevelBreakingNewsResp, error) {
	out := new(TriggerRichLevelBreakingNewsResp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/TriggerRichLevelBreakingNews", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) GetPresentRichOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/GetPresentRichOrderCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) GetPresentRichOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/GetPresentRichOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericGoClient) ReplacePresentRichOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/numeric_go.NumericGo/ReplacePresentRichOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NumericGoServer is the server API for NumericGo service.
type NumericGoServer interface {
	// 获取财富值魅力值
	GetPersonalNumeric(context.Context, *GetPersonalNumericReq) (*GetPersonalNumericResp, error)
	// 获取财富值、魅力值、财富等级、魅力等级、VIP等级
	GetPersonalNumericV2(context.Context, *GetPersonalNumericV2Req) (*GetPersonalNumericV2Resp, error)
	// 批量获取财富值魅力值
	BatchGetPersonalNumeric(context.Context, *BatchGetPersonalNumericReq) (*BatchGetPersonalNumericResp, error)
	// 增加财富值魅力值
	AddUserNumeric(context.Context, *AddUserNumericReq) (*AddUserNumericResp, error)
	// 获取成为VIP的时间
	GetVipSince(context.Context, *GetVipSinceReq) (*GetVipSinceResp, error)
	// 获取财富值控制开关状态（废弃）
	GetUserRichSwitch(context.Context, *GetUserRichSwitchReq) (*GetUserRichSwitchResp, error)
	// 设置是否打开关闭财富值累加（废弃）
	SetUserRichSwitch(context.Context, *SetUserRichSwitchReq) (*SetUserRichSwitchResp, error)
	// 获取用户财富魅力值锁状态
	GetUserNumericLock(context.Context, *GetUserNumericLockReq) (*GetUserNumericLockResp, error)
	// 设置用户财富魅力值锁状态
	SetUserNumericLock(context.Context, *SetUserNumericLockReq) (*SetUserNumericLockResp, error)
	// 批量获取用户财富魅力值锁状态
	BatchGetUserNumericLock(context.Context, *BatchGetUserNumericLockReq) (*BatchGetUserNumericLockResp, error)
	// 送礼增加财富值
	RecordSendGiftEvent(context.Context, *RecordSendGiftEventReq) (*RecordSendGiftEventResp, error)
	// 批量送礼增加财富值
	BatchRecordSendGiftEvent(context.Context, *BatchRecordSendGiftEventReq) (*BatchRecordSendGiftEventResp, error)
	// 获取清理财富值记录
	GetCleanUserRichRecords(context.Context, *GetCleanUserRichRecordsReq) (*GetCleanUserRichRecordsResp, error)
	// 创建清除财富值任务
	CreateCleanUserRichTask(context.Context, *CreateCleanUserRichTaskReq) (*CreateCleanUserRichTaskResp, error)
	// 执行清理财富值
	CleanUserRichTask(context.Context, *CleanUserRichTaskReq) (*CleanUserRichTaskResp, error)
	// 背包使用财富卡
	UseRichCard(context.Context, *UseRichCardReq) (*UseRichCardResp, error)
	// 触发财富等级升级全服公告
	TriggerRichLevelBreakingNews(context.Context, *TriggerRichLevelBreakingNewsReq) (*TriggerRichLevelBreakingNewsResp, error)
	GetPresentRichOrderCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetPresentRichOrderList(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	ReplacePresentRichOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
}

func RegisterNumericGoServer(s *grpc.Server, srv NumericGoServer) {
	s.RegisterService(&_NumericGo_serviceDesc, srv)
}

func _NumericGo_GetPersonalNumeric_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPersonalNumericReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).GetPersonalNumeric(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/GetPersonalNumeric",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).GetPersonalNumeric(ctx, req.(*GetPersonalNumericReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_GetPersonalNumericV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPersonalNumericV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).GetPersonalNumericV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/GetPersonalNumericV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).GetPersonalNumericV2(ctx, req.(*GetPersonalNumericV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_BatchGetPersonalNumeric_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetPersonalNumericReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).BatchGetPersonalNumeric(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/BatchGetPersonalNumeric",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).BatchGetPersonalNumeric(ctx, req.(*BatchGetPersonalNumericReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_AddUserNumeric_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserNumericReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).AddUserNumeric(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/AddUserNumeric",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).AddUserNumeric(ctx, req.(*AddUserNumericReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_GetVipSince_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVipSinceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).GetVipSince(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/GetVipSince",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).GetVipSince(ctx, req.(*GetVipSinceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_GetUserRichSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRichSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).GetUserRichSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/GetUserRichSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).GetUserRichSwitch(ctx, req.(*GetUserRichSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_SetUserRichSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserRichSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).SetUserRichSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/SetUserRichSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).SetUserRichSwitch(ctx, req.(*SetUserRichSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_GetUserNumericLock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserNumericLockReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).GetUserNumericLock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/GetUserNumericLock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).GetUserNumericLock(ctx, req.(*GetUserNumericLockReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_SetUserNumericLock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserNumericLockReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).SetUserNumericLock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/SetUserNumericLock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).SetUserNumericLock(ctx, req.(*SetUserNumericLockReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_BatchGetUserNumericLock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserNumericLockReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).BatchGetUserNumericLock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/BatchGetUserNumericLock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).BatchGetUserNumericLock(ctx, req.(*BatchGetUserNumericLockReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_RecordSendGiftEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordSendGiftEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).RecordSendGiftEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/RecordSendGiftEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).RecordSendGiftEvent(ctx, req.(*RecordSendGiftEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_BatchRecordSendGiftEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchRecordSendGiftEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).BatchRecordSendGiftEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/BatchRecordSendGiftEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).BatchRecordSendGiftEvent(ctx, req.(*BatchRecordSendGiftEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_GetCleanUserRichRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCleanUserRichRecordsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).GetCleanUserRichRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/GetCleanUserRichRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).GetCleanUserRichRecords(ctx, req.(*GetCleanUserRichRecordsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_CreateCleanUserRichTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCleanUserRichTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).CreateCleanUserRichTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/CreateCleanUserRichTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).CreateCleanUserRichTask(ctx, req.(*CreateCleanUserRichTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_CleanUserRichTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanUserRichTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).CleanUserRichTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/CleanUserRichTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).CleanUserRichTask(ctx, req.(*CleanUserRichTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_UseRichCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UseRichCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).UseRichCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/UseRichCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).UseRichCard(ctx, req.(*UseRichCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_TriggerRichLevelBreakingNews_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerRichLevelBreakingNewsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).TriggerRichLevelBreakingNews(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/TriggerRichLevelBreakingNews",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).TriggerRichLevelBreakingNews(ctx, req.(*TriggerRichLevelBreakingNewsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_GetPresentRichOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).GetPresentRichOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/GetPresentRichOrderCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).GetPresentRichOrderCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_GetPresentRichOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).GetPresentRichOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/GetPresentRichOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).GetPresentRichOrderList(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericGo_ReplacePresentRichOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericGoServer).ReplacePresentRichOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/numeric_go.NumericGo/ReplacePresentRichOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericGoServer).ReplacePresentRichOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _NumericGo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "numeric_go.NumericGo",
	HandlerType: (*NumericGoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPersonalNumeric",
			Handler:    _NumericGo_GetPersonalNumeric_Handler,
		},
		{
			MethodName: "GetPersonalNumericV2",
			Handler:    _NumericGo_GetPersonalNumericV2_Handler,
		},
		{
			MethodName: "BatchGetPersonalNumeric",
			Handler:    _NumericGo_BatchGetPersonalNumeric_Handler,
		},
		{
			MethodName: "AddUserNumeric",
			Handler:    _NumericGo_AddUserNumeric_Handler,
		},
		{
			MethodName: "GetVipSince",
			Handler:    _NumericGo_GetVipSince_Handler,
		},
		{
			MethodName: "GetUserRichSwitch",
			Handler:    _NumericGo_GetUserRichSwitch_Handler,
		},
		{
			MethodName: "SetUserRichSwitch",
			Handler:    _NumericGo_SetUserRichSwitch_Handler,
		},
		{
			MethodName: "GetUserNumericLock",
			Handler:    _NumericGo_GetUserNumericLock_Handler,
		},
		{
			MethodName: "SetUserNumericLock",
			Handler:    _NumericGo_SetUserNumericLock_Handler,
		},
		{
			MethodName: "BatchGetUserNumericLock",
			Handler:    _NumericGo_BatchGetUserNumericLock_Handler,
		},
		{
			MethodName: "RecordSendGiftEvent",
			Handler:    _NumericGo_RecordSendGiftEvent_Handler,
		},
		{
			MethodName: "BatchRecordSendGiftEvent",
			Handler:    _NumericGo_BatchRecordSendGiftEvent_Handler,
		},
		{
			MethodName: "GetCleanUserRichRecords",
			Handler:    _NumericGo_GetCleanUserRichRecords_Handler,
		},
		{
			MethodName: "CreateCleanUserRichTask",
			Handler:    _NumericGo_CreateCleanUserRichTask_Handler,
		},
		{
			MethodName: "CleanUserRichTask",
			Handler:    _NumericGo_CleanUserRichTask_Handler,
		},
		{
			MethodName: "UseRichCard",
			Handler:    _NumericGo_UseRichCard_Handler,
		},
		{
			MethodName: "TriggerRichLevelBreakingNews",
			Handler:    _NumericGo_TriggerRichLevelBreakingNews_Handler,
		},
		{
			MethodName: "GetPresentRichOrderCount",
			Handler:    _NumericGo_GetPresentRichOrderCount_Handler,
		},
		{
			MethodName: "GetPresentRichOrderList",
			Handler:    _NumericGo_GetPresentRichOrderList_Handler,
		},
		{
			MethodName: "ReplacePresentRichOrder",
			Handler:    _NumericGo_ReplacePresentRichOrder_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/numeric-go/numeric-go.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/numeric-go/numeric-go.proto", fileDescriptor_numeric_go_7aaeda62c8bfb2aa)
}

var fileDescriptor_numeric_go_7aaeda62c8bfb2aa = []byte{
	// 2725 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x3a, 0xcd, 0x73, 0xe3, 0x48,
	0xf5, 0x23, 0x3b, 0x89, 0xed, 0x97, 0x38, 0x56, 0x7a, 0xb2, 0x89, 0xc7, 0x99, 0x8f, 0x8c, 0xe6,
	0xf7, 0xdb, 0x0d, 0xd9, 0x9d, 0xcc, 0x12, 0xd8, 0x82, 0x85, 0x61, 0x0b, 0xc7, 0x63, 0x32, 0xae,
	0xf1, 0xd8, 0x41, 0xb6, 0x33, 0xb5, 0x5b, 0x6c, 0x09, 0x45, 0xea, 0x24, 0x22, 0xb2, 0xa4, 0x95,
	0xe4, 0xec, 0xa6, 0xb8, 0xc0, 0x85, 0xaf, 0x0b, 0x07, 0x4e, 0x14, 0x17, 0xce, 0x5c, 0xb9, 0x50,
	0xc5, 0x11, 0xaa, 0x28, 0x8e, 0x5c, 0xf9, 0x47, 0xb8, 0x52, 0xfd, 0x5a, 0x92, 0x25, 0x59, 0xb2,
	0xb3, 0x9c, 0xf6, 0xa6, 0x7e, 0x9f, 0xfd, 0x3e, 0xfa, 0xf5, 0x7b, 0x6d, 0xc3, 0xbe, 0xef, 0x3f,
	0xfb, 0x6c, 0x62, 0x68, 0x57, 0x9e, 0x61, 0x5e, 0x53, 0xf7, 0x99, 0x35, 0x19, 0x53, 0xd7, 0xd0,
	0x9e, 0x5e, 0xd8, 0xb1, 0xcf, 0x03, 0xc7, 0xb5, 0x7d, 0x9b, 0x40, 0x00, 0x51, 0x2e, 0xec, 0xc6,
	0x41, 0x8a, 0xcf, 0xa5, 0x9a, 0x6d, 0x69, 0x86, 0x49, 0x9f, 0x5e, 0x1f, 0x26, 0x16, 0x9c, 0x57,
	0xfa, 0x1a, 0xbc, 0x75, 0x4c, 0xfd, 0x13, 0xea, 0x7a, 0xb6, 0xa5, 0x9a, 0x3d, 0x2e, 0x48, 0xa6,
	0x9f, 0x11, 0x11, 0x8a, 0x13, 0x43, 0xaf, 0x0b, 0xbb, 0xc2, 0x5e, 0x55, 0x66, 0x9f, 0xd2, 0x11,
	0x6c, 0x65, 0x91, 0x7a, 0x0e, 0x21, 0xb0, 0xe4, 0x1a, 0xda, 0x25, 0x12, 0x2f, 0xc9, 0xf8, 0x4d,
	0x36, 0x61, 0x59, 0xbb, 0x54, 0xdd, 0x71, 0xbd, 0x80, 0x40, 0xbe, 0x90, 0xde, 0x85, 0xed, 0x59,
	0x19, 0xa7, 0x87, 0xd9, 0x0a, 0xff, 0x2d, 0x40, 0x3d, 0x9b, 0xfa, 0xcb, 0xe8, 0x24, 0xef, 0x03,
	0x30, 0xac, 0x62, 0xd2, 0x6b, 0x6a, 0xd6, 0x8b, 0xbb, 0xc2, 0xde, 0xea, 0xe1, 0xc6, 0xc1, 0xd4,
	0x67, 0x07, 0x5d, 0x86, 0x90, 0x2b, 0x8c, 0x08, 0x3f, 0xc9, 0x21, 0xac, 0x22, 0x6b, 0xc0, 0xb2,
	0x94, 0xc7, 0x02, 0x48, 0xc5, 0x79, 0x0e, 0xa0, 0x72, 0x6d, 0x38, 0x01, 0xc7, 0x72, 0x1e, 0x47,
	0xf9, 0xda, 0x70, 0xf0, 0x4b, 0xfa, 0x9b, 0x00, 0xcb, 0x9c, 0xf3, 0x01, 0xc0, 0x58, 0x35, 0xac,
	0x80, 0x95, 0xdb, 0x5f, 0x61, 0x10, 0x8e, 0xde, 0x81, 0x8a, 0x37, 0x39, 0x0b, 0xb0, 0x05, 0xc4,
	0x96, 0xbd, 0xc9, 0x59, 0xc4, 0x1b, 0x21, 0x0f, 0xeb, 0x2b, 0x9c, 0x37, 0xc4, 0x1e, 0x32, 0x34,
	0xa2, 0x14, 0x9f, 0x7e, 0xe1, 0xa3, 0xe9, 0x15, 0xb9, 0x82, 0x90, 0x21, 0xfd, 0xc2, 0x9f, 0xa2,
	0x2d, 0x75, 0x4c, 0xd1, 0xcc, 0x10, 0xdd, 0x53, 0xc7, 0x94, 0x3c, 0x86, 0x35, 0x8f, 0xba, 0xd7,
	0xd4, 0x8d, 0x59, 0xb5, 0x24, 0xaf, 0x72, 0x18, 0xb7, 0x42, 0x82, 0xf5, 0x63, 0xea, 0x9f, 0x1a,
	0xce, 0xc0, 0xb0, 0x34, 0x9a, 0x1d, 0xc6, 0x9f, 0x0b, 0x50, 0x4b, 0x10, 0x79, 0x4e, 0xd2, 0x5b,
	0xc2, 0x42, 0x6f, 0x91, 0x87, 0xb0, 0x7a, 0x46, 0x15, 0xc6, 0xe2, 0x1b, 0x63, 0x1a, 0xc4, 0xb7,
	0x72, 0x46, 0x4f, 0x0d, 0x67, 0x68, 0x8c, 0x69, 0x0c, 0xaf, 0xab, 0x37, 0x1e, 0x5a, 0x5a, 0x0d,
	0xf0, 0x2f, 0xd4, 0x1b, 0x4f, 0xfa, 0x16, 0x34, 0x8e, 0x54, 0x5f, 0xbb, 0xcc, 0xce, 0xf5, 0x7b,
	0x50, 0x9e, 0x18, 0xba, 0x62, 0x1a, 0x9e, 0x5f, 0x17, 0x76, 0x8b, 0x7b, 0x55, 0xb9, 0x34, 0x31,
	0xf4, 0xae, 0xe1, 0xf9, 0xd2, 0x9f, 0x05, 0xa8, 0xa5, 0x38, 0x66, 0x4d, 0xcc, 0x49, 0xbc, 0x30,
	0x45, 0x8b, 0xb1, 0x14, 0x4d, 0x26, 0xe3, 0xf2, 0x97, 0x4f, 0xc6, 0x95, 0x5b, 0x24, 0xa3, 0xf4,
	0x29, 0xec, 0xe4, 0x9a, 0xeb, 0x39, 0xe4, 0x23, 0x58, 0x0b, 0xd9, 0x23, 0x9b, 0x57, 0x0f, 0x77,
	0xe2, 0x32, 0xd3, 0x6c, 0xab, 0x01, 0x0e, 0x9d, 0xf2, 0x33, 0x01, 0x36, 0x9a, 0xba, 0x3e, 0xf2,
	0xa8, 0x3b, 0xaf, 0x62, 0xb0, 0xfc, 0x42, 0x63, 0xaf, 0x55, 0x73, 0x42, 0x03, 0x37, 0xa0, 0x65,
	0xa7, 0x0c, 0x40, 0x1e, 0x85, 0x96, 0x71, 0xfc, 0x12, 0xe2, 0xb9, 0x19, 0x9c, 0xe0, 0x1e, 0x94,
	0x6d, 0x57, 0xa7, 0xae, 0x62, 0xe8, 0xe8, 0xaa, 0x8a, 0x5c, 0xc2, 0x75, 0x47, 0x97, 0x7e, 0x02,
	0x24, 0xbd, 0x03, 0xcf, 0x21, 0x7b, 0x20, 0x9e, 0x1b, 0x96, 0x6a, 0x2a, 0x31, 0xb5, 0xbc, 0x40,
	0xac, 0x23, 0x5c, 0x8e, 0x74, 0xef, 0xc3, 0x06, 0xa7, 0x8c, 0xef, 0x80, 0x47, 0xaf, 0x86, 0x88,
	0x56, 0xb4, 0x0d, 0x69, 0x0f, 0x36, 0x8f, 0xa9, 0xcf, 0x74, 0x31, 0xfe, 0xc1, 0xe7, 0x86, 0xaf,
	0x5d, 0x66, 0xa7, 0xfa, 0x33, 0xac, 0xa6, 0x69, 0x4a, 0xcf, 0x21, 0x5b, 0xb0, 0x42, 0x2d, 0xf5,
	0xcc, 0xe4, 0xdb, 0x29, 0xcb, 0xc1, 0x4a, 0x7a, 0x0e, 0x22, 0xa3, 0xe6, 0x94, 0x03, 0x5f, 0xf5,
	0x27, 0x5e, 0x86, 0x1f, 0xa7, 0xdc, 0x85, 0x04, 0xf7, 0xf7, 0x61, 0x73, 0x70, 0xab, 0x8d, 0xe5,
	0x4a, 0xd8, 0x86, 0xb7, 0x06, 0x59, 0x1b, 0x96, 0x7e, 0x5f, 0x80, 0x5a, 0xcc, 0xbb, 0x5d, 0x5b,
	0xbb, 0x22, 0xcf, 0x61, 0xfd, 0x8c, 0xaa, 0x16, 0x77, 0xae, 0x69, 0x6b, 0x57, 0xa8, 0x61, 0xfd,
	0x70, 0x2b, 0x91, 0x8c, 0xb6, 0x76, 0xc5, 0x0d, 0x91, 0xd7, 0x18, 0x35, 0x93, 0x8b, 0xdc, 0x47,
	0xb0, 0xa1, 0x1b, 0xea, 0xd8, 0xb6, 0xf4, 0x98, 0x80, 0xc2, 0x5c, 0x01, 0xb5, 0x80, 0x21, 0x92,
	0xf1, 0x11, 0xd4, 0x70, 0x07, 0xc1, 0x81, 0x60, 0x12, 0x8a, 0x73, 0x25, 0x54, 0x19, 0x39, 0x86,
	0x12, 0xf9, 0x5f, 0x00, 0x09, 0xf7, 0x10, 0x13, 0xb1, 0x34, 0x57, 0x84, 0x18, 0x70, 0x44, 0x52,
	0x82, 0x3b, 0x33, 0xe5, 0x9d, 0x98, 0xdf, 0x0b, 0xd3, 0x84, 0x78, 0x85, 0x77, 0xe6, 0x0c, 0xa9,
	0xe7, 0x90, 0xaf, 0xc3, 0x32, 0x53, 0xee, 0x21, 0x75, 0xea, 0xf0, 0xa5, 0xe9, 0x39, 0xa5, 0xf4,
	0xa3, 0x28, 0x58, 0x8b, 0xf4, 0x4e, 0xa5, 0x17, 0x6f, 0x2d, 0xbd, 0x0e, 0x5b, 0x83, 0xcc, 0xad,
	0xc6, 0x8b, 0x67, 0x86, 0xf2, 0x39, 0xc5, 0xf3, 0x1f, 0xc2, 0xb4, 0x0e, 0x65, 0xf9, 0xa0, 0x0f,
	0x65, 0xa6, 0x5b, 0x19, 0xab, 0x4e, 0xbd, 0x80, 0x35, 0xe8, 0x9b, 0xf1, 0x8d, 0xce, 0x61, 0xc5,
	0x00, 0xbd, 0x56, 0x9d, 0xb6, 0xe5, 0xbb, 0x37, 0x72, 0xc9, 0xe4, 0xab, 0xc6, 0x1b, 0x58, 0x8b,
	0x23, 0x98, 0x63, 0xae, 0xe8, 0x4d, 0x78, 0x10, 0xae, 0xe8, 0x0d, 0x73, 0xcc, 0xf4, 0xac, 0x2f,
	0x72, 0x0c, 0x52, 0x7e, 0xa7, 0xf0, 0x6d, 0x41, 0xfa, 0xbb, 0x00, 0x1b, 0x0c, 0x7d, 0x6c, 0x9c,
	0xfb, 0xed, 0x6b, 0x6a, 0xf9, 0x1d, 0xeb, 0xdc, 0xce, 0x38, 0x67, 0xf7, 0xa0, 0x7c, 0x31, 0x31,
	0x4c, 0x5d, 0x89, 0xc2, 0x51, 0xc2, 0x75, 0x47, 0x67, 0xf7, 0xb8, 0xaa, 0xeb, 0x89, 0x5a, 0x58,
	0x56, 0x75, 0x3d, 0x2a, 0x85, 0xbc, 0x1c, 0x25, 0x4a, 0x21, 0x82, 0x38, 0xc1, 0x63, 0x58, 0xe3,
	0x57, 0xb5, 0x76, 0xa9, 0x5a, 0x17, 0x14, 0xcb, 0x61, 0x59, 0x5e, 0x45, 0x58, 0x0b, 0x41, 0x8c,
	0xe4, 0x8c, 0x9e, 0xdb, 0x2e, 0x0d, 0x84, 0xac, 0xf0, 0xeb, 0x9a, 0xc3, 0x78, 0x25, 0xfb, 0x4f,
	0x01, 0xb6, 0x64, 0xaa, 0xd9, 0xae, 0x3e, 0xa0, 0x96, 0x1e, 0x19, 0xc3, 0xc2, 0xb8, 0x03, 0x95,
	0x0b, 0x83, 0xdd, 0xf5, 0x53, 0x8b, 0xca, 0x08, 0x18, 0x19, 0x3a, 0x13, 0xed, 0x52, 0x8d, 0x46,
	0x78, 0x6e, 0xda, 0x6a, 0x08, 0x1b, 0xdd, 0xaa, 0xd6, 0x73, 0xf1, 0xe8, 0x0e, 0x34, 0xb0, 0x2a,
	0x03, 0x82, 0x8e, 0x19, 0x84, 0xfc, 0x3f, 0xac, 0x47, 0x2a, 0x38, 0xcd, 0x32, 0xd2, 0x54, 0x43,
	0x28, 0x27, 0x4b, 0xdd, 0x19, 0x2b, 0x73, 0xef, 0x8c, 0x52, 0xe2, 0xce, 0x60, 0x5b, 0x64, 0xde,
	0xb3, 0xa8, 0xc9, 0x90, 0x65, 0xde, 0x23, 0x04, 0x90, 0x8e, 0x4e, 0x9e, 0x40, 0x35, 0x44, 0xf3,
	0x0d, 0x54, 0x90, 0x62, 0x2d, 0x00, 0x72, 0xfd, 0x0f, 0x00, 0x1c, 0xd7, 0xd0, 0xa8, 0xe2, 0xdf,
	0x38, 0xb4, 0x0e, 0x5c, 0x06, 0x42, 0x86, 0x37, 0x0e, 0x25, 0xdb, 0x50, 0xba, 0x30, 0xce, 0x7d,
	0x26, 0x7f, 0x15, 0x71, 0x2b, 0x6c, 0xd9, 0xd1, 0xa5, 0xdf, 0x16, 0x60, 0x3b, 0xd3, 0xf3, 0x58,
	0x0a, 0xde, 0x8a, 0xc7, 0x56, 0x49, 0x1d, 0x27, 0x12, 0x0b, 0xf2, 0x88, 0x9f, 0x2c, 0xf4, 0x36,
	0x0d, 0x6f, 0xaf, 0xb0, 0x1d, 0x62, 0x10, 0xac, 0x52, 0xe4, 0x29, 0xdc, 0xe5, 0xde, 0xd6, 0x26,
	0xae, 0xab, 0xa8, 0x26, 0xbf, 0x10, 0x83, 0xa8, 0x88, 0x88, 0x6a, 0x4d, 0x5c, 0xb7, 0x69, 0xe2,
	0x8d, 0x48, 0x3e, 0x80, 0xed, 0xc8, 0xf7, 0x11, 0x07, 0x17, 0xcd, 0x33, 0x71, 0x33, 0x44, 0x07,
	0x5c, 0x5c, 0xcb, 0x0e, 0xa0, 0x4a, 0x2e, 0x9b, 0x37, 0x87, 0x65, 0x06, 0x40, 0x99, 0x8f, 0x20,
	0xc8, 0x3c, 0x8e, 0x0e, 0x02, 0xc5, 0x41, 0x8c, 0x40, 0xfa, 0x57, 0x21, 0x28, 0x0e, 0x39, 0x09,
	0xd9, 0x86, 0x5a, 0x90, 0x90, 0x1e, 0x8b, 0xa6, 0x75, 0x6e, 0x07, 0x8d, 0xe2, 0x83, 0xf4, 0x99,
	0x4d, 0x1c, 0x4a, 0xb9, 0xca, 0xb3, 0xd6, 0xa3, 0x2e, 0x9e, 0xd1, 0x61, 0xcc, 0xb6, 0x48, 0x12,
	0x77, 0x2f, 0x2f, 0x39, 0x0b, 0xc4, 0x45, 0xa6, 0x87, 0x12, 0xd1, 0xff, 0xf1, 0x2c, 0x2b, 0xce,
	0xcb, 0xb2, 0xa5, 0x85, 0x59, 0xb6, 0xbc, 0x30, 0xcb, 0x56, 0xe6, 0x64, 0x59, 0x29, 0x91, 0x65,
	0x7f, 0x15, 0xe0, 0x7e, 0xbe, 0x4f, 0x3d, 0xe7, 0x2b, 0xed, 0x54, 0xe9, 0x8f, 0x02, 0x34, 0x8e,
	0xa9, 0xdf, 0x32, 0xa9, 0x6a, 0x85, 0x2d, 0x09, 0x37, 0xc4, 0xcb, 0xee, 0x6a, 0x24, 0xa8, 0xda,
	0x7c, 0x22, 0x50, 0x3c, 0x5f, 0x75, 0xfd, 0xb0, 0x2e, 0xd9, 0x38, 0x14, 0x0c, 0x18, 0x88, 0x4d,
	0x06, 0x21, 0x0d, 0xb5, 0xf4, 0x70, 0x32, 0xe0, 0x14, 0x6d, 0x0b, 0x3b, 0x23, 0xfb, 0xfc, 0xdc,
	0xa3, 0x7e, 0x10, 0xaa, 0x60, 0xc5, 0x5a, 0x7a, 0xd3, 0x18, 0x1b, 0x7e, 0x10, 0x1f, 0xbe, 0x90,
	0x7e, 0xb7, 0x02, 0x3b, 0xb9, 0x5b, 0xf4, 0x1c, 0x32, 0x82, 0x92, 0xcb, 0x97, 0x41, 0x53, 0xfd,
	0xdd, 0xb8, 0x23, 0xe6, 0x70, 0x1e, 0x64, 0x20, 0xe4, 0x50, 0x16, 0xdb, 0x8c, 0x6f, 0xfb, 0x6a,
	0x38, 0xff, 0xf1, 0x45, 0xe3, 0x17, 0xcb, 0x70, 0x37, 0x83, 0x2d, 0xc3, 0x51, 0x22, 0x14, 0xfd,
	0xa0, 0x6c, 0x57, 0x64, 0xf6, 0x49, 0x1a, 0x50, 0xb6, 0x0c, 0xed, 0x0a, 0x07, 0x3f, 0x9e, 0xc0,
	0xd1, 0x9a, 0xf5, 0xc6, 0x9a, 0x6d, 0xf9, 0xae, 0xaa, 0xf9, 0x4a, 0x74, 0x9b, 0x71, 0xef, 0xd4,
	0x42, 0xc4, 0x71, 0x70, 0xab, 0x75, 0x62, 0xb4, 0x86, 0x4e, 0x2d, 0xdf, 0xf0, 0x6f, 0xea, 0xcb,
	0xbb, 0xc5, 0xbd, 0xf5, 0xc3, 0xfb, 0x71, 0xd3, 0x5b, 0x01, 0x51, 0x27, 0xa0, 0x91, 0x45, 0x2d,
	0x05, 0xc1, 0x93, 0x11, 0x8a, 0xc2, 0x29, 0x6f, 0x25, 0x38, 0x19, 0x01, 0x10, 0x07, 0xbd, 0x1d,
	0xa8, 0x60, 0x85, 0xc2, 0xa2, 0x52, 0xe2, 0x35, 0x87, 0x01, 0xb0, 0xe6, 0x7c, 0x08, 0xb5, 0x08,
	0x19, 0x8c, 0x4b, 0xe5, 0xbc, 0x71, 0xa9, 0x1a, 0x72, 0xf1, 0x29, 0x2b, 0x55, 0xae, 0x2a, 0xe9,
	0x72, 0x45, 0xbe, 0x07, 0x1b, 0x31, 0x82, 0x40, 0x3a, 0xe4, 0x49, 0xaf, 0x4d, 0x39, 0xa3, 0x41,
	0x5d, 0x33, 0xc3, 0xe6, 0x19, 0xef, 0x86, 0x25, 0xb9, 0x82, 0x10, 0x94, 0xbe, 0x0d, 0xa5, 0x20,
	0x4b, 0xeb, 0x6b, 0x41, 0x1a, 0xf2, 0xc1, 0xb6, 0x01, 0x65, 0xdb, 0xa1, 0xae, 0xea, 0xdb, 0x6e,
	0xbd, 0xca, 0xe3, 0x14, 0xae, 0x13, 0x45, 0x68, 0x9d, 0x37, 0x1b, 0x61, 0x11, 0x6a, 0x40, 0xf9,
	0xdc, 0x30, 0x29, 0x86, 0xb7, 0xc6, 0xd9, 0xc2, 0x35, 0x63, 0x63, 0xdf, 0xca, 0xc4, 0x35, 0xeb,
	0x22, 0xaf, 0x5d, 0x6c, 0x3d, 0x72, 0x4d, 0x76, 0x11, 0xa4, 0x22, 0xef, 0x5d, 0xda, 0x2e, 0x16,
	0x9a, 0x0d, 0x54, 0xb0, 0x99, 0x88, 0xff, 0x80, 0x21, 0x3b, 0xba, 0xf4, 0xab, 0x02, 0x34, 0x5a,
	0x2e, 0x55, 0x7d, 0x9a, 0x48, 0xc7, 0xa1, 0xea, 0x61, 0x87, 0xd8, 0x84, 0x65, 0xc3, 0xa7, 0xe3,
	0xf0, 0x48, 0xbc, 0x9b, 0xc8, 0x8b, 0x5c, 0xb6, 0x83, 0x8e, 0x4f, 0xc7, 0x32, 0xe7, 0x4c, 0xb8,
	0xa1, 0x90, 0x72, 0x43, 0xdc, 0xd6, 0xe2, 0x1c, 0x5b, 0x97, 0x12, 0xb6, 0x36, 0x86, 0xb0, 0xc4,
	0x34, 0x64, 0x8f, 0xad, 0xb1, 0x58, 0x15, 0xd2, 0xb1, 0x62, 0x29, 0x88, 0x68, 0xd5, 0xe4, 0xcf,
	0x49, 0x65, 0xb9, 0x8c, 0x80, 0xa6, 0x69, 0x4a, 0xbf, 0x2e, 0xc2, 0x4e, 0xae, 0x4d, 0x9e, 0x93,
	0x88, 0x99, 0x90, 0x8c, 0xd9, 0x51, 0xe8, 0x26, 0x5e, 0x42, 0xdf, 0xbb, 0x95, 0x9b, 0x3c, 0x27,
	0xee, 0xa7, 0xc6, 0x6f, 0x0a, 0xff, 0xab, 0x55, 0x5f, 0xe5, 0x43, 0x3f, 0x27, 0x2d, 0x4b, 0x73,
	0xd2, 0xf2, 0x4f, 0x02, 0x6c, 0x66, 0x26, 0xe4, 0x9c, 0x20, 0x3c, 0x87, 0xa2, 0xed, 0xf8, 0xc1,
	0x5c, 0xba, 0x9f, 0x30, 0x26, 0x2b, 0x47, 0xfb, 0x98, 0x84, 0x86, 0x6d, 0xc9, 0x8c, 0x4d, 0xfa,
	0x10, 0x2a, 0x11, 0x84, 0x6c, 0x01, 0xe9, 0x9f, 0xb4, 0xe5, 0xe6, 0xb0, 0xd3, 0xef, 0x29, 0xcd,
	0x93, 0x13, 0xb9, 0x7f, 0xda, 0xec, 0x8a, 0x77, 0xc8, 0x26, 0x88, 0x53, 0x78, 0xab, 0xd9, 0x6b,
	0xb5, 0xbb, 0xa2, 0xc0, 0x26, 0xf1, 0xcc, 0xf0, 0x4a, 0x7f, 0x28, 0xc0, 0xfa, 0xc8, 0xc3, 0x52,
	0xd2, 0x52, 0x5d, 0x3d, 0x6b, 0xff, 0xb1, 0xee, 0x63, 0x76, 0x14, 0xbc, 0x07, 0x6c, 0xcc, 0x88,
	0x37, 0x80, 0x25, 0x55, 0xc7, 0x91, 0x9a, 0xbc, 0xcf, 0x8d, 0xe5, 0xf3, 0xef, 0xc3, 0xd4, 0x95,
	0x1d, 0x53, 0x78, 0xd0, 0x77, 0x7c, 0x34, 0x90, 0xd5, 0x29, 0x4d, 0x75, 0xf5, 0xf0, 0x41, 0xa6,
	0x2a, 0xaf, 0xb0, 0x25, 0x9f, 0x6e, 0x26, 0x1e, 0x55, 0x34, 0x7b, 0x62, 0xf9, 0x41, 0x0c, 0xcb,
	0x13, 0x8f, 0xb6, 0xd8, 0x1a, 0x4f, 0x0c, 0xe3, 0xc2, 0x23, 0xca, 0x9b, 0xf2, 0x32, 0x03, 0xf4,
	0xd4, 0x31, 0x95, 0x9e, 0x43, 0xb1, 0xef, 0xf8, 0x84, 0xc0, 0x7a, 0xff, 0x64, 0xa8, 0x9c, 0xc8,
	0xed, 0x13, 0xb9, 0xdf, 0x6a, 0x0f, 0x06, 0xe2, 0x1d, 0x52, 0x83, 0x55, 0x0e, 0xe3, 0x00, 0x81,
	0x88, 0xb0, 0xc6, 0x00, 0x72, 0xbf, 0xdb, 0x3d, 0x6a, 0xb6, 0x5e, 0x89, 0x05, 0xe9, 0x15, 0x3e,
	0x53, 0x4c, 0xf7, 0xca, 0x8f, 0x58, 0x64, 0xb0, 0x90, 0x34, 0xf8, 0x01, 0xc0, 0xf4, 0x7d, 0x28,
	0x3c, 0x03, 0xd1, 0xcb, 0x90, 0xf4, 0x19, 0x3c, 0x1a, 0xba, 0xc6, 0xc5, 0x05, 0x9d, 0x5e, 0x0c,
	0x47, 0x2e, 0x55, 0xaf, 0x0c, 0xeb, 0xa2, 0x47, 0x3f, 0xf7, 0x72, 0x1f, 0xb9, 0x62, 0xfd, 0x5e,
	0x21, 0xdd, 0xef, 0x3d, 0x00, 0x50, 0xcf, 0x7d, 0xea, 0xc6, 0x03, 0x50, 0x41, 0x08, 0xaa, 0x94,
	0x60, 0x77, 0xbe, 0x4a, 0xcf, 0xd9, 0x7f, 0x0a, 0xe5, 0x60, 0x2c, 0x1d, 0x92, 0x35, 0x28, 0x8f,
	0xac, 0x2b, 0xcb, 0xfe, 0xdc, 0x1a, 0x8a, 0x77, 0x48, 0x19, 0x96, 0x18, 0x9b, 0x28, 0x90, 0x0a,
	0x2c, 0x63, 0x53, 0x2e, 0x16, 0xf6, 0xff, 0x29, 0x40, 0x69, 0x60, 0x4f, 0x5c, 0x8d, 0x0e, 0x99,
	0x57, 0xf9, 0x67, 0x8c, 0x69, 0x1b, 0xee, 0x06, 0xe8, 0x13, 0x97, 0x7a, 0xd4, 0xf2, 0x87, 0x47,
	0x54, 0xb5, 0x44, 0x81, 0xdc, 0x87, 0x7a, 0x12, 0x21, 0x53, 0xfd, 0x05, 0x7f, 0xfc, 0x10, 0x0b,
	0xe4, 0x2e, 0xd4, 0x02, 0x6c, 0xe8, 0x6d, 0xb1, 0x48, 0x36, 0xa0, 0x1a, 0x00, 0xdb, 0x03, 0xc7,
	0x76, 0x7d, 0x71, 0x89, 0x05, 0x2d, 0x00, 0xa1, 0xd8, 0xe5, 0x18, 0xcd, 0x2b, 0xcb, 0xb8, 0xb8,
	0xf4, 0xc5, 0x95, 0x18, 0xe8, 0xb5, 0x6a, 0x4d, 0x54, 0x53, 0x2c, 0xb1, 0x53, 0x11, 0x80, 0x5a,
	0x61, 0x4d, 0x12, 0xcb, 0xfb, 0x9f, 0x00, 0x4c, 0x9f, 0x62, 0xc8, 0x0e, 0x6c, 0x77, 0xfb, 0xad,
	0x57, 0xca, 0x60, 0xd8, 0x1c, 0x8e, 0x06, 0xca, 0xa8, 0x37, 0x38, 0x69, 0xb7, 0x3a, 0x3f, 0xe8,
	0xb4, 0x5f, 0x88, 0x77, 0xd8, 0x71, 0x8b, 0x23, 0xdb, 0xbd, 0xe6, 0x51, 0xb7, 0x2d, 0x0a, 0xcc,
	0xdc, 0x38, 0xfc, 0x45, 0x67, 0x80, 0x88, 0xc2, 0x7e, 0x1b, 0xc4, 0x74, 0x81, 0x22, 0x75, 0xd8,
	0x6c, 0xf5, 0x7b, 0x43, 0xb9, 0xd9, 0x1a, 0x2a, 0xaf, 0x47, 0xdd, 0x61, 0xe7, 0xa4, 0xdb, 0xfc,
	0xb8, 0x2d, 0x73, 0xaf, 0x45, 0x18, 0xb9, 0xf9, 0xa2, 0xd3, 0x57, 0xba, 0x9d, 0xd3, 0xb6, 0x28,
	0xec, 0xff, 0x45, 0x80, 0xcd, 0xe9, 0xe3, 0x19, 0x1f, 0xd3, 0xb0, 0x4b, 0x7f, 0x02, 0x8f, 0xe4,
	0x4e, 0xeb, 0xa5, 0x32, 0x78, 0xd3, 0x19, 0xb6, 0x5e, 0x2a, 0xad, 0x97, 0xcd, 0xde, 0x71, 0x5b,
	0x19, 0x7e, 0x7c, 0xd2, 0x56, 0x46, 0xbd, 0x57, 0xbd, 0xfe, 0x9b, 0x9e, 0x78, 0x87, 0x48, 0xf0,
	0x30, 0x8f, 0xe8, 0x75, 0xb3, 0x37, 0x6a, 0x76, 0x45, 0x81, 0xbc, 0x0b, 0xef, 0xe4, 0xd1, 0x44,
	0x5b, 0x0a, 0xea, 0x48, 0x81, 0xec, 0xc3, 0xdb, 0x0b, 0x89, 0xe5, 0x76, 0xaf, 0xfd, 0x46, 0x2c,
	0x1e, 0xfe, 0xb2, 0x06, 0x95, 0x20, 0xb3, 0x8e, 0x6d, 0xf2, 0x29, 0x90, 0xd9, 0xf7, 0x62, 0xf2,
	0x38, 0xd5, 0xc0, 0xce, 0x3e, 0x9f, 0x37, 0xa4, 0x45, 0x24, 0x9e, 0x23, 0xdd, 0x21, 0x1a, 0xbe,
	0xa2, 0xce, 0xfc, 0x98, 0x43, 0x9e, 0xcc, 0xe7, 0xc6, 0x1f, 0x87, 0x1a, 0xff, 0xb7, 0x98, 0x08,
	0x95, 0x98, 0xb0, 0x9d, 0xf3, 0xf0, 0x4d, 0xde, 0xce, 0x7a, 0x5a, 0xca, 0xb0, 0xe6, 0x9d, 0x5b,
	0xd1, 0xa1, 0xb6, 0x1f, 0xc2, 0x7a, 0xf2, 0x11, 0x9a, 0x24, 0xe6, 0x9e, 0x99, 0x27, 0xf2, 0xc6,
	0xc3, 0x79, 0x68, 0x14, 0xf9, 0x12, 0x56, 0x63, 0xbf, 0x95, 0x90, 0x46, 0xca, 0xee, 0xd8, 0x2f,
	0x2d, 0x8d, 0x9d, 0x5c, 0x1c, 0x4a, 0xfa, 0x04, 0x36, 0x66, 0xde, 0xa2, 0xc9, 0x6e, 0x8a, 0x67,
	0xe6, 0xed, 0xb8, 0xf1, 0x78, 0x01, 0x45, 0x28, 0x7b, 0x30, 0x5f, 0xf6, 0x60, 0xa1, 0xec, 0x41,
	0x8e, 0x6c, 0x9e, 0x86, 0xe9, 0xb7, 0xe7, 0xac, 0x6d, 0x25, 0x1f, 0x22, 0x67, 0xd2, 0x30, 0xeb,
	0x29, 0x13, 0xc5, 0x0f, 0x16, 0x88, 0x1f, 0x2c, 0x16, 0x3f, 0xc8, 0x13, 0x1f, 0x4b, 0xc0, 0xb4,
	0x8e, 0xb7, 0x6f, 0xf5, 0xb6, 0x99, 0x93, 0x80, 0xd9, 0xda, 0x7e, 0x0c, 0x77, 0x33, 0x26, 0x7d,
	0x92, 0xd8, 0x6a, 0xf6, 0xf3, 0x4a, 0xe3, 0xc9, 0x42, 0x1a, 0xd4, 0x60, 0x43, 0x3d, 0xef, 0x41,
	0x81, 0xcc, 0x6e, 0x34, 0x47, 0xd7, 0xde, 0xed, 0x08, 0x43, 0x07, 0xe6, 0x8c, 0xc9, 0x49, 0x07,
	0xe6, 0x3f, 0x14, 0x24, 0x1d, 0x38, 0x67, 0xe6, 0xe6, 0xda, 0x72, 0x5a, 0xeb, 0xa4, 0xb6, 0xfc,
	0x31, 0x25, 0xa9, 0x6d, 0x4e, 0x9f, 0xce, 0x8f, 0xcd, 0xac, 0x9e, 0xdd, 0x45, 0x4d, 0x66, 0xf2,
	0xd8, 0xe4, 0xc9, 0x7e, 0x09, 0xab, 0xb1, 0x46, 0x28, 0x59, 0x38, 0x92, 0xdd, 0x5c, 0x63, 0x27,
	0x17, 0x87, 0x92, 0x7e, 0x0a, 0xf7, 0xe7, 0xb5, 0x24, 0x24, 0x31, 0xbf, 0x2d, 0xe8, 0x97, 0x1a,
	0xef, 0xdd, 0x9e, 0x18, 0x95, 0xf7, 0xf9, 0x4f, 0xfe, 0x41, 0xff, 0x61, 0x68, 0x97, 0x7d, 0xd6,
	0xd8, 0xf2, 0x36, 0xf2, 0x1e, 0xe6, 0x29, 0xfe, 0x81, 0xe1, 0xf4, 0xf0, 0x80, 0x4d, 0x06, 0x32,
	0xbb, 0x6c, 0x99, 0x9a, 0xad, 0x04, 0x0a, 0xc9, 0xa3, 0x1a, 0xbd, 0x9d, 0x21, 0x90, 0x3f, 0xe2,
	0xe5, 0xcb, 0x4b, 0xa2, 0xfa, 0xbc, 0xb9, 0xf6, 0xa6, 0x22, 0x65, 0xea, 0x98, 0xaa, 0x46, 0xd3,
	0x62, 0xc9, 0xfd, 0x04, 0x5f, 0x40, 0x85, 0xa8, 0xd9, 0x5d, 0xb6, 0xc7, 0x8e, 0x7f, 0xc3, 0x45,
	0x1e, 0x1d, 0x7c, 0xf2, 0xde, 0x85, 0x6d, 0xaa, 0xd6, 0xc5, 0xc1, 0x07, 0x87, 0xbe, 0x7f, 0xa0,
	0xd9, 0xe3, 0x67, 0xf8, 0xff, 0x0c, 0xcd, 0x36, 0x9f, 0x79, 0xd4, 0xbd, 0x36, 0x34, 0xea, 0xc5,
	0xfe, 0xf8, 0x71, 0xb6, 0x82, 0xd8, 0x6f, 0xfc, 0x37, 0x00, 0x00, 0xff, 0xff, 0x4d, 0xbc, 0xd3,
	0x1b, 0x27, 0x22, 0x00, 0x00,
}
