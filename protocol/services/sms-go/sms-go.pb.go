// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/sms/sms-go.proto

package sms_go // import "golang.52tt.com/protocol/services/sms-go"

/*
namespace
buf:lint:ignore DIRECTORY_SAME_PACKAGE
*/

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 专用错误码从-20开始
// buf:lint:ignore ENUM_PASCAL_CASE
type ERR_SMS int32

const (
	ERR_SMS_ERR_SMS_OK                         ERR_SMS = 0
	ERR_SMS_ERR_SMS_VERFIYCODE_VALIDATE_FAIL   ERR_SMS = -20
	ERR_SMS_ERR_SMS_TOO_MANY_PHONE             ERR_SMS = -21
	ERR_SMS_ERR_SMS_SEND_SMS_FREQ              ERR_SMS = -22
	ERR_SMS_ERR_SMS_TYPE_INVALID               ERR_SMS = -23
	ERR_SMS_ERR_SMS_NO_ENOUGH_PARAMS           ERR_SMS = -24
	ERR_SMS_ERR_SMS_SEND_SMS_THRESHHOLD_EXCEED ERR_SMS = -25
	ERR_SMS_ERR_SMS_INVALID_PHONE              ERR_SMS = -26
	ERR_SMS_ERR_SMS_INVALID_VERIFYCODE         ERR_SMS = -27
	ERR_SMS_ERR_SMS_NOT_SUPPORT_INN            ERR_SMS = -28
	ERR_SMS_ERR_SMS_REDIS_ERROR                ERR_SMS = -29
	ERR_SMS_ERR_SMS_PARSE_FROM_PB_ERROR        ERR_SMS = -30
	ERR_SMS_ERR_VALID_APK_URL_CANNOT_REACH     ERR_SMS = -31
	ERR_SMS_ERR_VALID_APK_URL_NOT_APK          ERR_SMS = -32
	ERR_SMS_ERR_SMS_INVALID_SENDER             ERR_SMS = -33
)

var ERR_SMS_name = map[int32]string{
	0:   "ERR_SMS_OK",
	-20: "ERR_SMS_VERFIYCODE_VALIDATE_FAIL",
	-21: "ERR_SMS_TOO_MANY_PHONE",
	-22: "ERR_SMS_SEND_SMS_FREQ",
	-23: "ERR_SMS_TYPE_INVALID",
	-24: "ERR_SMS_NO_ENOUGH_PARAMS",
	-25: "ERR_SMS_SEND_SMS_THRESHHOLD_EXCEED",
	-26: "ERR_SMS_INVALID_PHONE",
	-27: "ERR_SMS_INVALID_VERIFYCODE",
	-28: "ERR_SMS_NOT_SUPPORT_INN",
	-29: "ERR_SMS_REDIS_ERROR",
	-30: "ERR_SMS_PARSE_FROM_PB_ERROR",
	-31: "ERR_VALID_APK_URL_CANNOT_REACH",
	-32: "ERR_VALID_APK_URL_NOT_APK",
	-33: "ERR_SMS_INVALID_SENDER",
}
var ERR_SMS_value = map[string]int32{
	"ERR_SMS_OK":                         0,
	"ERR_SMS_VERFIYCODE_VALIDATE_FAIL":   -20,
	"ERR_SMS_TOO_MANY_PHONE":             -21,
	"ERR_SMS_SEND_SMS_FREQ":              -22,
	"ERR_SMS_TYPE_INVALID":               -23,
	"ERR_SMS_NO_ENOUGH_PARAMS":           -24,
	"ERR_SMS_SEND_SMS_THRESHHOLD_EXCEED": -25,
	"ERR_SMS_INVALID_PHONE":              -26,
	"ERR_SMS_INVALID_VERIFYCODE":         -27,
	"ERR_SMS_NOT_SUPPORT_INN":            -28,
	"ERR_SMS_REDIS_ERROR":                -29,
	"ERR_SMS_PARSE_FROM_PB_ERROR":        -30,
	"ERR_VALID_APK_URL_CANNOT_REACH":     -31,
	"ERR_VALID_APK_URL_NOT_APK":          -32,
	"ERR_SMS_INVALID_SENDER":             -33,
}

func (x ERR_SMS) String() string {
	return proto.EnumName(ERR_SMS_name, int32(x))
}
func (ERR_SMS) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{0}
}

type AuthSmsResp_Result int32

const (
	AuthSmsResp_SUCCESS           AuthSmsResp_Result = 0
	AuthSmsResp_INVALID_APP_INFO  AuthSmsResp_Result = 1
	AuthSmsResp_INVALID_CLIENT_IP AuthSmsResp_Result = 2
)

var AuthSmsResp_Result_name = map[int32]string{
	0: "SUCCESS",
	1: "INVALID_APP_INFO",
	2: "INVALID_CLIENT_IP",
}
var AuthSmsResp_Result_value = map[string]int32{
	"SUCCESS":           0,
	"INVALID_APP_INFO":  1,
	"INVALID_CLIENT_IP": 2,
}

func (x AuthSmsResp_Result) String() string {
	return proto.EnumName(AuthSmsResp_Result_name, int32(x))
}
func (AuthSmsResp_Result) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{33, 0}
}

// 单发短信
type SendSmsReq struct {
	Phone           string   `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	SmsType         uint32   `protobuf:"varint,2,opt,name=sms_type,json=smsType,proto3" json:"sms_type,omitempty"`
	ParamList       []string `protobuf:"bytes,3,rep,name=param_list,json=paramList,proto3" json:"param_list,omitempty"`
	WithoutCooldown bool     `protobuf:"varint,4,opt,name=without_cooldown,json=withoutCooldown,proto3" json:"without_cooldown,omitempty"`
	VerifyCodeKey   string   `protobuf:"bytes,5,opt,name=verify_code_key,json=verifyCodeKey,proto3" json:"verify_code_key,omitempty"`
	VerifyCodeUsage string   `protobuf:"bytes,6,opt,name=verify_code_usage,json=verifyCodeUsage,proto3" json:"verify_code_usage,omitempty"`
	MarketId        uint32   `protobuf:"varint,7,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	// 	uint32 retry_times = 4;	//当前的重试次数(start with 0)
	BizId                uint32   `protobuf:"varint,8,opt,name=biz_id,json=bizId,proto3" json:"biz_id,omitempty"`
	ExtBizId             uint32   `protobuf:"varint,9,opt,name=ext_biz_id,json=extBizId,proto3" json:"ext_biz_id,omitempty"`
	RequestId            string   `protobuf:"bytes,10,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	CreateTime           uint64   `protobuf:"varint,11,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendSmsReq) Reset()         { *m = SendSmsReq{} }
func (m *SendSmsReq) String() string { return proto.CompactTextString(m) }
func (*SendSmsReq) ProtoMessage()    {}
func (*SendSmsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{0}
}
func (m *SendSmsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendSmsReq.Unmarshal(m, b)
}
func (m *SendSmsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendSmsReq.Marshal(b, m, deterministic)
}
func (dst *SendSmsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendSmsReq.Merge(dst, src)
}
func (m *SendSmsReq) XXX_Size() int {
	return xxx_messageInfo_SendSmsReq.Size(m)
}
func (m *SendSmsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendSmsReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendSmsReq proto.InternalMessageInfo

func (m *SendSmsReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *SendSmsReq) GetSmsType() uint32 {
	if m != nil {
		return m.SmsType
	}
	return 0
}

func (m *SendSmsReq) GetParamList() []string {
	if m != nil {
		return m.ParamList
	}
	return nil
}

func (m *SendSmsReq) GetWithoutCooldown() bool {
	if m != nil {
		return m.WithoutCooldown
	}
	return false
}

func (m *SendSmsReq) GetVerifyCodeKey() string {
	if m != nil {
		return m.VerifyCodeKey
	}
	return ""
}

func (m *SendSmsReq) GetVerifyCodeUsage() string {
	if m != nil {
		return m.VerifyCodeUsage
	}
	return ""
}

func (m *SendSmsReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *SendSmsReq) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *SendSmsReq) GetExtBizId() uint32 {
	if m != nil {
		return m.ExtBizId
	}
	return 0
}

func (m *SendSmsReq) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *SendSmsReq) GetCreateTime() uint64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type SendSmsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendSmsResp) Reset()         { *m = SendSmsResp{} }
func (m *SendSmsResp) String() string { return proto.CompactTextString(m) }
func (*SendSmsResp) ProtoMessage()    {}
func (*SendSmsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{1}
}
func (m *SendSmsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendSmsResp.Unmarshal(m, b)
}
func (m *SendSmsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendSmsResp.Marshal(b, m, deterministic)
}
func (dst *SendSmsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendSmsResp.Merge(dst, src)
}
func (m *SendSmsResp) XXX_Size() int {
	return xxx_messageInfo_SendSmsResp.Size(m)
}
func (m *SendSmsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendSmsResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendSmsResp proto.InternalMessageInfo

// 用户信息
type UserInfo struct {
	Phone                string   `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Sign                 string   `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign,omitempty"`
	ParamList            []string `protobuf:"bytes,4,rep,name=param_list,json=paramList,proto3" json:"param_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfo) Reset()         { *m = UserInfo{} }
func (m *UserInfo) String() string { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()    {}
func (*UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{2}
}
func (m *UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo.Unmarshal(m, b)
}
func (m *UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo.Marshal(b, m, deterministic)
}
func (dst *UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo.Merge(dst, src)
}
func (m *UserInfo) XXX_Size() int {
	return xxx_messageInfo_UserInfo.Size(m)
}
func (m *UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo proto.InternalMessageInfo

func (m *UserInfo) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *UserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserInfo) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

func (m *UserInfo) GetParamList() []string {
	if m != nil {
		return m.ParamList
	}
	return nil
}

// 通用短信请求，必须指定模板的usage(不清楚时找短信负责人确认）内容，可发行业短信及营销短信
type SendCommonSmsReq struct {
	UserInfoList         []*UserInfo `protobuf:"bytes,1,rep,name=user_info_list,json=userInfoList,proto3" json:"user_info_list,omitempty"`
	Usage                string      `protobuf:"bytes,2,opt,name=usage,proto3" json:"usage,omitempty"`
	WithoutCooldown      bool        `protobuf:"varint,3,opt,name=without_cooldown,json=withoutCooldown,proto3" json:"without_cooldown,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SendCommonSmsReq) Reset()         { *m = SendCommonSmsReq{} }
func (m *SendCommonSmsReq) String() string { return proto.CompactTextString(m) }
func (*SendCommonSmsReq) ProtoMessage()    {}
func (*SendCommonSmsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{3}
}
func (m *SendCommonSmsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendCommonSmsReq.Unmarshal(m, b)
}
func (m *SendCommonSmsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendCommonSmsReq.Marshal(b, m, deterministic)
}
func (dst *SendCommonSmsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendCommonSmsReq.Merge(dst, src)
}
func (m *SendCommonSmsReq) XXX_Size() int {
	return xxx_messageInfo_SendCommonSmsReq.Size(m)
}
func (m *SendCommonSmsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendCommonSmsReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendCommonSmsReq proto.InternalMessageInfo

func (m *SendCommonSmsReq) GetUserInfoList() []*UserInfo {
	if m != nil {
		return m.UserInfoList
	}
	return nil
}

func (m *SendCommonSmsReq) GetUsage() string {
	if m != nil {
		return m.Usage
	}
	return ""
}

func (m *SendCommonSmsReq) GetWithoutCooldown() bool {
	if m != nil {
		return m.WithoutCooldown
	}
	return false
}

type ErrorPhone struct {
	Phone                string   `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	Code                 int32    `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ErrorPhone) Reset()         { *m = ErrorPhone{} }
func (m *ErrorPhone) String() string { return proto.CompactTextString(m) }
func (*ErrorPhone) ProtoMessage()    {}
func (*ErrorPhone) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{4}
}
func (m *ErrorPhone) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ErrorPhone.Unmarshal(m, b)
}
func (m *ErrorPhone) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ErrorPhone.Marshal(b, m, deterministic)
}
func (dst *ErrorPhone) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ErrorPhone.Merge(dst, src)
}
func (m *ErrorPhone) XXX_Size() int {
	return xxx_messageInfo_ErrorPhone.Size(m)
}
func (m *ErrorPhone) XXX_DiscardUnknown() {
	xxx_messageInfo_ErrorPhone.DiscardUnknown(m)
}

var xxx_messageInfo_ErrorPhone proto.InternalMessageInfo

func (m *ErrorPhone) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *ErrorPhone) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

// 通用短信应答
type SendCommonSmsResp struct {
	Result               int32         `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	ErrorPhones          []*ErrorPhone `protobuf:"bytes,2,rep,name=error_phones,json=errorPhones,proto3" json:"error_phones,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SendCommonSmsResp) Reset()         { *m = SendCommonSmsResp{} }
func (m *SendCommonSmsResp) String() string { return proto.CompactTextString(m) }
func (*SendCommonSmsResp) ProtoMessage()    {}
func (*SendCommonSmsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{5}
}
func (m *SendCommonSmsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendCommonSmsResp.Unmarshal(m, b)
}
func (m *SendCommonSmsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendCommonSmsResp.Marshal(b, m, deterministic)
}
func (dst *SendCommonSmsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendCommonSmsResp.Merge(dst, src)
}
func (m *SendCommonSmsResp) XXX_Size() int {
	return xxx_messageInfo_SendCommonSmsResp.Size(m)
}
func (m *SendCommonSmsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendCommonSmsResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendCommonSmsResp proto.InternalMessageInfo

func (m *SendCommonSmsResp) GetResult() int32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *SendCommonSmsResp) GetErrorPhones() []*ErrorPhone {
	if m != nil {
		return m.ErrorPhones
	}
	return nil
}

type SendRandomVerifyCodeReq struct {
	Phone                string   `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	ExpireTtl            uint32   `protobuf:"varint,2,opt,name=expire_ttl,json=expireTtl,proto3" json:"expire_ttl,omitempty"`
	Length               uint32   `protobuf:"varint,3,opt,name=length,proto3" json:"length,omitempty"`
	Usage                string   `protobuf:"bytes,4,opt,name=usage,proto3" json:"usage,omitempty"`
	ParamLists           []string `protobuf:"bytes,5,rep,name=param_lists,json=paramLists,proto3" json:"param_lists,omitempty"`
	Uid                  uint32   `protobuf:"varint,6,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendRandomVerifyCodeReq) Reset()         { *m = SendRandomVerifyCodeReq{} }
func (m *SendRandomVerifyCodeReq) String() string { return proto.CompactTextString(m) }
func (*SendRandomVerifyCodeReq) ProtoMessage()    {}
func (*SendRandomVerifyCodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{6}
}
func (m *SendRandomVerifyCodeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendRandomVerifyCodeReq.Unmarshal(m, b)
}
func (m *SendRandomVerifyCodeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendRandomVerifyCodeReq.Marshal(b, m, deterministic)
}
func (dst *SendRandomVerifyCodeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendRandomVerifyCodeReq.Merge(dst, src)
}
func (m *SendRandomVerifyCodeReq) XXX_Size() int {
	return xxx_messageInfo_SendRandomVerifyCodeReq.Size(m)
}
func (m *SendRandomVerifyCodeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendRandomVerifyCodeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendRandomVerifyCodeReq proto.InternalMessageInfo

func (m *SendRandomVerifyCodeReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *SendRandomVerifyCodeReq) GetExpireTtl() uint32 {
	if m != nil {
		return m.ExpireTtl
	}
	return 0
}

func (m *SendRandomVerifyCodeReq) GetLength() uint32 {
	if m != nil {
		return m.Length
	}
	return 0
}

func (m *SendRandomVerifyCodeReq) GetUsage() string {
	if m != nil {
		return m.Usage
	}
	return ""
}

func (m *SendRandomVerifyCodeReq) GetParamLists() []string {
	if m != nil {
		return m.ParamLists
	}
	return nil
}

func (m *SendRandomVerifyCodeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SendRandomVerifyCodeResp struct {
	Result               int32    `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	VerifyCode           string   `protobuf:"bytes,2,opt,name=verify_code,json=verifyCode,proto3" json:"verify_code,omitempty"`
	Sid                  string   `protobuf:"bytes,3,opt,name=sid,proto3" json:"sid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendRandomVerifyCodeResp) Reset()         { *m = SendRandomVerifyCodeResp{} }
func (m *SendRandomVerifyCodeResp) String() string { return proto.CompactTextString(m) }
func (*SendRandomVerifyCodeResp) ProtoMessage()    {}
func (*SendRandomVerifyCodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{7}
}
func (m *SendRandomVerifyCodeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendRandomVerifyCodeResp.Unmarshal(m, b)
}
func (m *SendRandomVerifyCodeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendRandomVerifyCodeResp.Marshal(b, m, deterministic)
}
func (dst *SendRandomVerifyCodeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendRandomVerifyCodeResp.Merge(dst, src)
}
func (m *SendRandomVerifyCodeResp) XXX_Size() int {
	return xxx_messageInfo_SendRandomVerifyCodeResp.Size(m)
}
func (m *SendRandomVerifyCodeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendRandomVerifyCodeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendRandomVerifyCodeResp proto.InternalMessageInfo

func (m *SendRandomVerifyCodeResp) GetResult() int32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *SendRandomVerifyCodeResp) GetVerifyCode() string {
	if m != nil {
		return m.VerifyCode
	}
	return ""
}

func (m *SendRandomVerifyCodeResp) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

type ValidateVerifyCodeReq struct {
	Phone                string   `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	Usage                string   `protobuf:"bytes,2,opt,name=usage,proto3" json:"usage,omitempty"`
	VerifyCode           string   `protobuf:"bytes,3,opt,name=verify_code,json=verifyCode,proto3" json:"verify_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ValidateVerifyCodeReq) Reset()         { *m = ValidateVerifyCodeReq{} }
func (m *ValidateVerifyCodeReq) String() string { return proto.CompactTextString(m) }
func (*ValidateVerifyCodeReq) ProtoMessage()    {}
func (*ValidateVerifyCodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{8}
}
func (m *ValidateVerifyCodeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ValidateVerifyCodeReq.Unmarshal(m, b)
}
func (m *ValidateVerifyCodeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ValidateVerifyCodeReq.Marshal(b, m, deterministic)
}
func (dst *ValidateVerifyCodeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidateVerifyCodeReq.Merge(dst, src)
}
func (m *ValidateVerifyCodeReq) XXX_Size() int {
	return xxx_messageInfo_ValidateVerifyCodeReq.Size(m)
}
func (m *ValidateVerifyCodeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidateVerifyCodeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ValidateVerifyCodeReq proto.InternalMessageInfo

func (m *ValidateVerifyCodeReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *ValidateVerifyCodeReq) GetUsage() string {
	if m != nil {
		return m.Usage
	}
	return ""
}

func (m *ValidateVerifyCodeReq) GetVerifyCode() string {
	if m != nil {
		return m.VerifyCode
	}
	return ""
}

type ValidateVerifyCodeResp struct {
	Result               int32    `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ValidateVerifyCodeResp) Reset()         { *m = ValidateVerifyCodeResp{} }
func (m *ValidateVerifyCodeResp) String() string { return proto.CompactTextString(m) }
func (*ValidateVerifyCodeResp) ProtoMessage()    {}
func (*ValidateVerifyCodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{9}
}
func (m *ValidateVerifyCodeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ValidateVerifyCodeResp.Unmarshal(m, b)
}
func (m *ValidateVerifyCodeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ValidateVerifyCodeResp.Marshal(b, m, deterministic)
}
func (dst *ValidateVerifyCodeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidateVerifyCodeResp.Merge(dst, src)
}
func (m *ValidateVerifyCodeResp) XXX_Size() int {
	return xxx_messageInfo_ValidateVerifyCodeResp.Size(m)
}
func (m *ValidateVerifyCodeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidateVerifyCodeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ValidateVerifyCodeResp proto.InternalMessageInfo

func (m *ValidateVerifyCodeResp) GetResult() int32 {
	if m != nil {
		return m.Result
	}
	return 0
}

type DirectSendSmsReq struct {
	Phone                string   `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	BizId                uint32   `protobuf:"varint,3,opt,name=biz_id,json=bizId,proto3" json:"biz_id,omitempty"`
	ExtBizId             uint32   `protobuf:"varint,4,opt,name=ext_biz_id,json=extBizId,proto3" json:"ext_biz_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DirectSendSmsReq) Reset()         { *m = DirectSendSmsReq{} }
func (m *DirectSendSmsReq) String() string { return proto.CompactTextString(m) }
func (*DirectSendSmsReq) ProtoMessage()    {}
func (*DirectSendSmsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{10}
}
func (m *DirectSendSmsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DirectSendSmsReq.Unmarshal(m, b)
}
func (m *DirectSendSmsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DirectSendSmsReq.Marshal(b, m, deterministic)
}
func (dst *DirectSendSmsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DirectSendSmsReq.Merge(dst, src)
}
func (m *DirectSendSmsReq) XXX_Size() int {
	return xxx_messageInfo_DirectSendSmsReq.Size(m)
}
func (m *DirectSendSmsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DirectSendSmsReq.DiscardUnknown(m)
}

var xxx_messageInfo_DirectSendSmsReq proto.InternalMessageInfo

func (m *DirectSendSmsReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *DirectSendSmsReq) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *DirectSendSmsReq) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *DirectSendSmsReq) GetExtBizId() uint32 {
	if m != nil {
		return m.ExtBizId
	}
	return 0
}

type DirectSendSmsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DirectSendSmsResp) Reset()         { *m = DirectSendSmsResp{} }
func (m *DirectSendSmsResp) String() string { return proto.CompactTextString(m) }
func (*DirectSendSmsResp) ProtoMessage()    {}
func (*DirectSendSmsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{11}
}
func (m *DirectSendSmsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DirectSendSmsResp.Unmarshal(m, b)
}
func (m *DirectSendSmsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DirectSendSmsResp.Marshal(b, m, deterministic)
}
func (dst *DirectSendSmsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DirectSendSmsResp.Merge(dst, src)
}
func (m *DirectSendSmsResp) XXX_Size() int {
	return xxx_messageInfo_DirectSendSmsResp.Size(m)
}
func (m *DirectSendSmsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DirectSendSmsResp.DiscardUnknown(m)
}

var xxx_messageInfo_DirectSendSmsResp proto.InternalMessageInfo

// //////////////////////////////////////////////
// 验证码相关逻辑， 暂时放在此server， 以后独立
// //////////////////////////////////////////////
// 为uid生成验证码
type CreateVerifyCodeReq struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	CodeLen              uint32   `protobuf:"varint,2,opt,name=code_len,json=codeLen,proto3" json:"code_len,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateVerifyCodeReq) Reset()         { *m = CreateVerifyCodeReq{} }
func (m *CreateVerifyCodeReq) String() string { return proto.CompactTextString(m) }
func (*CreateVerifyCodeReq) ProtoMessage()    {}
func (*CreateVerifyCodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{12}
}
func (m *CreateVerifyCodeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateVerifyCodeReq.Unmarshal(m, b)
}
func (m *CreateVerifyCodeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateVerifyCodeReq.Marshal(b, m, deterministic)
}
func (dst *CreateVerifyCodeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateVerifyCodeReq.Merge(dst, src)
}
func (m *CreateVerifyCodeReq) XXX_Size() int {
	return xxx_messageInfo_CreateVerifyCodeReq.Size(m)
}
func (m *CreateVerifyCodeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateVerifyCodeReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateVerifyCodeReq proto.InternalMessageInfo

func (m *CreateVerifyCodeReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *CreateVerifyCodeReq) GetCodeLen() uint32 {
	if m != nil {
		return m.CodeLen
	}
	return 0
}

type CreateVerifyCodeResp struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	VerifyCode           string   `protobuf:"bytes,2,opt,name=verify_code,json=verifyCode,proto3" json:"verify_code,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,3,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateVerifyCodeResp) Reset()         { *m = CreateVerifyCodeResp{} }
func (m *CreateVerifyCodeResp) String() string { return proto.CompactTextString(m) }
func (*CreateVerifyCodeResp) ProtoMessage()    {}
func (*CreateVerifyCodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{13}
}
func (m *CreateVerifyCodeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateVerifyCodeResp.Unmarshal(m, b)
}
func (m *CreateVerifyCodeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateVerifyCodeResp.Marshal(b, m, deterministic)
}
func (dst *CreateVerifyCodeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateVerifyCodeResp.Merge(dst, src)
}
func (m *CreateVerifyCodeResp) XXX_Size() int {
	return xxx_messageInfo_CreateVerifyCodeResp.Size(m)
}
func (m *CreateVerifyCodeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateVerifyCodeResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateVerifyCodeResp proto.InternalMessageInfo

func (m *CreateVerifyCodeResp) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *CreateVerifyCodeResp) GetVerifyCode() string {
	if m != nil {
		return m.VerifyCode
	}
	return ""
}

func (m *CreateVerifyCodeResp) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

type CheckUrlValidApkUrlReq struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUrlValidApkUrlReq) Reset()         { *m = CheckUrlValidApkUrlReq{} }
func (m *CheckUrlValidApkUrlReq) String() string { return proto.CompactTextString(m) }
func (*CheckUrlValidApkUrlReq) ProtoMessage()    {}
func (*CheckUrlValidApkUrlReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{14}
}
func (m *CheckUrlValidApkUrlReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUrlValidApkUrlReq.Unmarshal(m, b)
}
func (m *CheckUrlValidApkUrlReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUrlValidApkUrlReq.Marshal(b, m, deterministic)
}
func (dst *CheckUrlValidApkUrlReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUrlValidApkUrlReq.Merge(dst, src)
}
func (m *CheckUrlValidApkUrlReq) XXX_Size() int {
	return xxx_messageInfo_CheckUrlValidApkUrlReq.Size(m)
}
func (m *CheckUrlValidApkUrlReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUrlValidApkUrlReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUrlValidApkUrlReq proto.InternalMessageInfo

func (m *CheckUrlValidApkUrlReq) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type CheckUrlValidApkUrlResp struct {
	ContentLength        uint32   `protobuf:"varint,1,opt,name=content_length,json=contentLength,proto3" json:"content_length,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUrlValidApkUrlResp) Reset()         { *m = CheckUrlValidApkUrlResp{} }
func (m *CheckUrlValidApkUrlResp) String() string { return proto.CompactTextString(m) }
func (*CheckUrlValidApkUrlResp) ProtoMessage()    {}
func (*CheckUrlValidApkUrlResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{15}
}
func (m *CheckUrlValidApkUrlResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUrlValidApkUrlResp.Unmarshal(m, b)
}
func (m *CheckUrlValidApkUrlResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUrlValidApkUrlResp.Marshal(b, m, deterministic)
}
func (dst *CheckUrlValidApkUrlResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUrlValidApkUrlResp.Merge(dst, src)
}
func (m *CheckUrlValidApkUrlResp) XXX_Size() int {
	return xxx_messageInfo_CheckUrlValidApkUrlResp.Size(m)
}
func (m *CheckUrlValidApkUrlResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUrlValidApkUrlResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUrlValidApkUrlResp proto.InternalMessageInfo

func (m *CheckUrlValidApkUrlResp) GetContentLength() uint32 {
	if m != nil {
		return m.ContentLength
	}
	return 0
}

type DownLoadUrlReq struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Timeout              uint32   `protobuf:"varint,2,opt,name=timeout,proto3" json:"timeout,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DownLoadUrlReq) Reset()         { *m = DownLoadUrlReq{} }
func (m *DownLoadUrlReq) String() string { return proto.CompactTextString(m) }
func (*DownLoadUrlReq) ProtoMessage()    {}
func (*DownLoadUrlReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{16}
}
func (m *DownLoadUrlReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DownLoadUrlReq.Unmarshal(m, b)
}
func (m *DownLoadUrlReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DownLoadUrlReq.Marshal(b, m, deterministic)
}
func (dst *DownLoadUrlReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DownLoadUrlReq.Merge(dst, src)
}
func (m *DownLoadUrlReq) XXX_Size() int {
	return xxx_messageInfo_DownLoadUrlReq.Size(m)
}
func (m *DownLoadUrlReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DownLoadUrlReq.DiscardUnknown(m)
}

var xxx_messageInfo_DownLoadUrlReq proto.InternalMessageInfo

func (m *DownLoadUrlReq) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *DownLoadUrlReq) GetTimeout() uint32 {
	if m != nil {
		return m.Timeout
	}
	return 0
}

type DownLoadUrlResp struct {
	Msg                  string   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DownLoadUrlResp) Reset()         { *m = DownLoadUrlResp{} }
func (m *DownLoadUrlResp) String() string { return proto.CompactTextString(m) }
func (*DownLoadUrlResp) ProtoMessage()    {}
func (*DownLoadUrlResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{17}
}
func (m *DownLoadUrlResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DownLoadUrlResp.Unmarshal(m, b)
}
func (m *DownLoadUrlResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DownLoadUrlResp.Marshal(b, m, deterministic)
}
func (dst *DownLoadUrlResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DownLoadUrlResp.Merge(dst, src)
}
func (m *DownLoadUrlResp) XXX_Size() int {
	return xxx_messageInfo_DownLoadUrlResp.Size(m)
}
func (m *DownLoadUrlResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DownLoadUrlResp.DiscardUnknown(m)
}

var xxx_messageInfo_DownLoadUrlResp proto.InternalMessageInfo

func (m *DownLoadUrlResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type DownLoadUrlByteReq struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Timeout              uint32   `protobuf:"varint,2,opt,name=timeout,proto3" json:"timeout,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DownLoadUrlByteReq) Reset()         { *m = DownLoadUrlByteReq{} }
func (m *DownLoadUrlByteReq) String() string { return proto.CompactTextString(m) }
func (*DownLoadUrlByteReq) ProtoMessage()    {}
func (*DownLoadUrlByteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{18}
}
func (m *DownLoadUrlByteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DownLoadUrlByteReq.Unmarshal(m, b)
}
func (m *DownLoadUrlByteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DownLoadUrlByteReq.Marshal(b, m, deterministic)
}
func (dst *DownLoadUrlByteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DownLoadUrlByteReq.Merge(dst, src)
}
func (m *DownLoadUrlByteReq) XXX_Size() int {
	return xxx_messageInfo_DownLoadUrlByteReq.Size(m)
}
func (m *DownLoadUrlByteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DownLoadUrlByteReq.DiscardUnknown(m)
}

var xxx_messageInfo_DownLoadUrlByteReq proto.InternalMessageInfo

func (m *DownLoadUrlByteReq) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *DownLoadUrlByteReq) GetTimeout() uint32 {
	if m != nil {
		return m.Timeout
	}
	return 0
}

type DownLoadUrlByteResp struct {
	Msg                  []byte   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DownLoadUrlByteResp) Reset()         { *m = DownLoadUrlByteResp{} }
func (m *DownLoadUrlByteResp) String() string { return proto.CompactTextString(m) }
func (*DownLoadUrlByteResp) ProtoMessage()    {}
func (*DownLoadUrlByteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{19}
}
func (m *DownLoadUrlByteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DownLoadUrlByteResp.Unmarshal(m, b)
}
func (m *DownLoadUrlByteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DownLoadUrlByteResp.Marshal(b, m, deterministic)
}
func (dst *DownLoadUrlByteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DownLoadUrlByteResp.Merge(dst, src)
}
func (m *DownLoadUrlByteResp) XXX_Size() int {
	return xxx_messageInfo_DownLoadUrlByteResp.Size(m)
}
func (m *DownLoadUrlByteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DownLoadUrlByteResp.DiscardUnknown(m)
}

var xxx_messageInfo_DownLoadUrlByteResp proto.InternalMessageInfo

func (m *DownLoadUrlByteResp) GetMsg() []byte {
	if m != nil {
		return m.Msg
	}
	return nil
}

type Foo struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Foo) Reset()         { *m = Foo{} }
func (m *Foo) String() string { return proto.CompactTextString(m) }
func (*Foo) ProtoMessage()    {}
func (*Foo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{20}
}
func (m *Foo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Foo.Unmarshal(m, b)
}
func (m *Foo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Foo.Marshal(b, m, deterministic)
}
func (dst *Foo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Foo.Merge(dst, src)
}
func (m *Foo) XXX_Size() int {
	return xxx_messageInfo_Foo.Size(m)
}
func (m *Foo) XXX_DiscardUnknown() {
	xxx_messageInfo_Foo.DiscardUnknown(m)
}

var xxx_messageInfo_Foo proto.InternalMessageInfo

// post url 接口
type PostUrlDataReq struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	HeadInfoList         [][]byte `protobuf:"bytes,2,rep,name=head_info_list,json=headInfoList,proto3" json:"head_info_list,omitempty"`
	DataInfo             []byte   `protobuf:"bytes,3,opt,name=data_info,json=dataInfo,proto3" json:"data_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostUrlDataReq) Reset()         { *m = PostUrlDataReq{} }
func (m *PostUrlDataReq) String() string { return proto.CompactTextString(m) }
func (*PostUrlDataReq) ProtoMessage()    {}
func (*PostUrlDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{21}
}
func (m *PostUrlDataReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostUrlDataReq.Unmarshal(m, b)
}
func (m *PostUrlDataReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostUrlDataReq.Marshal(b, m, deterministic)
}
func (dst *PostUrlDataReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostUrlDataReq.Merge(dst, src)
}
func (m *PostUrlDataReq) XXX_Size() int {
	return xxx_messageInfo_PostUrlDataReq.Size(m)
}
func (m *PostUrlDataReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PostUrlDataReq.DiscardUnknown(m)
}

var xxx_messageInfo_PostUrlDataReq proto.InternalMessageInfo

func (m *PostUrlDataReq) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *PostUrlDataReq) GetHeadInfoList() [][]byte {
	if m != nil {
		return m.HeadInfoList
	}
	return nil
}

func (m *PostUrlDataReq) GetDataInfo() []byte {
	if m != nil {
		return m.DataInfo
	}
	return nil
}

type PostUrlDataResp struct {
	RespMsg              []byte   `protobuf:"bytes,1,opt,name=resp_msg,json=respMsg,proto3" json:"resp_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostUrlDataResp) Reset()         { *m = PostUrlDataResp{} }
func (m *PostUrlDataResp) String() string { return proto.CompactTextString(m) }
func (*PostUrlDataResp) ProtoMessage()    {}
func (*PostUrlDataResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{22}
}
func (m *PostUrlDataResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostUrlDataResp.Unmarshal(m, b)
}
func (m *PostUrlDataResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostUrlDataResp.Marshal(b, m, deterministic)
}
func (dst *PostUrlDataResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostUrlDataResp.Merge(dst, src)
}
func (m *PostUrlDataResp) XXX_Size() int {
	return xxx_messageInfo_PostUrlDataResp.Size(m)
}
func (m *PostUrlDataResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PostUrlDataResp.DiscardUnknown(m)
}

var xxx_messageInfo_PostUrlDataResp proto.InternalMessageInfo

func (m *PostUrlDataResp) GetRespMsg() []byte {
	if m != nil {
		return m.RespMsg
	}
	return nil
}

type SendVoiceVerifyCodeReq struct {
	Phone                string   `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	VerifyCode           string   `protobuf:"bytes,2,opt,name=verify_code,json=verifyCode,proto3" json:"verify_code,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	NationCode           string   `protobuf:"bytes,5,opt,name=nation_code,json=nationCode,proto3" json:"nation_code,omitempty"`
	VoiceType            uint32   `protobuf:"varint,6,opt,name=voice_type,json=voiceType,proto3" json:"voice_type,omitempty"`
	ParamList            []string `protobuf:"bytes,7,rep,name=param_list,json=paramList,proto3" json:"param_list,omitempty"`
	BizId                uint32   `protobuf:"varint,8,opt,name=biz_id,json=bizId,proto3" json:"biz_id,omitempty"`
	ExtBizId             uint32   `protobuf:"varint,9,opt,name=ext_biz_id,json=extBizId,proto3" json:"ext_biz_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendVoiceVerifyCodeReq) Reset()         { *m = SendVoiceVerifyCodeReq{} }
func (m *SendVoiceVerifyCodeReq) String() string { return proto.CompactTextString(m) }
func (*SendVoiceVerifyCodeReq) ProtoMessage()    {}
func (*SendVoiceVerifyCodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{23}
}
func (m *SendVoiceVerifyCodeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendVoiceVerifyCodeReq.Unmarshal(m, b)
}
func (m *SendVoiceVerifyCodeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendVoiceVerifyCodeReq.Marshal(b, m, deterministic)
}
func (dst *SendVoiceVerifyCodeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendVoiceVerifyCodeReq.Merge(dst, src)
}
func (m *SendVoiceVerifyCodeReq) XXX_Size() int {
	return xxx_messageInfo_SendVoiceVerifyCodeReq.Size(m)
}
func (m *SendVoiceVerifyCodeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendVoiceVerifyCodeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendVoiceVerifyCodeReq proto.InternalMessageInfo

func (m *SendVoiceVerifyCodeReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *SendVoiceVerifyCodeReq) GetVerifyCode() string {
	if m != nil {
		return m.VerifyCode
	}
	return ""
}

func (m *SendVoiceVerifyCodeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendVoiceVerifyCodeReq) GetNationCode() string {
	if m != nil {
		return m.NationCode
	}
	return ""
}

func (m *SendVoiceVerifyCodeReq) GetVoiceType() uint32 {
	if m != nil {
		return m.VoiceType
	}
	return 0
}

func (m *SendVoiceVerifyCodeReq) GetParamList() []string {
	if m != nil {
		return m.ParamList
	}
	return nil
}

func (m *SendVoiceVerifyCodeReq) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *SendVoiceVerifyCodeReq) GetExtBizId() uint32 {
	if m != nil {
		return m.ExtBizId
	}
	return 0
}

type SendVoiceVerifyCodeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendVoiceVerifyCodeResp) Reset()         { *m = SendVoiceVerifyCodeResp{} }
func (m *SendVoiceVerifyCodeResp) String() string { return proto.CompactTextString(m) }
func (*SendVoiceVerifyCodeResp) ProtoMessage()    {}
func (*SendVoiceVerifyCodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{24}
}
func (m *SendVoiceVerifyCodeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendVoiceVerifyCodeResp.Unmarshal(m, b)
}
func (m *SendVoiceVerifyCodeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendVoiceVerifyCodeResp.Marshal(b, m, deterministic)
}
func (dst *SendVoiceVerifyCodeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendVoiceVerifyCodeResp.Merge(dst, src)
}
func (m *SendVoiceVerifyCodeResp) XXX_Size() int {
	return xxx_messageInfo_SendVoiceVerifyCodeResp.Size(m)
}
func (m *SendVoiceVerifyCodeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendVoiceVerifyCodeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendVoiceVerifyCodeResp proto.InternalMessageInfo

type SendSmsWithProviderReq struct {
	Provider             string   `protobuf:"bytes,1,opt,name=provider,proto3" json:"provider,omitempty"`
	Phones               []string `protobuf:"bytes,2,rep,name=phones,proto3" json:"phones,omitempty"`
	SmsType              uint32   `protobuf:"varint,3,opt,name=sms_type,json=smsType,proto3" json:"sms_type,omitempty"`
	ParamList            []string `protobuf:"bytes,4,rep,name=param_list,json=paramList,proto3" json:"param_list,omitempty"`
	MarketId             uint32   `protobuf:"varint,5,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendSmsWithProviderReq) Reset()         { *m = SendSmsWithProviderReq{} }
func (m *SendSmsWithProviderReq) String() string { return proto.CompactTextString(m) }
func (*SendSmsWithProviderReq) ProtoMessage()    {}
func (*SendSmsWithProviderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{25}
}
func (m *SendSmsWithProviderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendSmsWithProviderReq.Unmarshal(m, b)
}
func (m *SendSmsWithProviderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendSmsWithProviderReq.Marshal(b, m, deterministic)
}
func (dst *SendSmsWithProviderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendSmsWithProviderReq.Merge(dst, src)
}
func (m *SendSmsWithProviderReq) XXX_Size() int {
	return xxx_messageInfo_SendSmsWithProviderReq.Size(m)
}
func (m *SendSmsWithProviderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendSmsWithProviderReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendSmsWithProviderReq proto.InternalMessageInfo

func (m *SendSmsWithProviderReq) GetProvider() string {
	if m != nil {
		return m.Provider
	}
	return ""
}

func (m *SendSmsWithProviderReq) GetPhones() []string {
	if m != nil {
		return m.Phones
	}
	return nil
}

func (m *SendSmsWithProviderReq) GetSmsType() uint32 {
	if m != nil {
		return m.SmsType
	}
	return 0
}

func (m *SendSmsWithProviderReq) GetParamList() []string {
	if m != nil {
		return m.ParamList
	}
	return nil
}

func (m *SendSmsWithProviderReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type SendSmsWithProviderResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendSmsWithProviderResp) Reset()         { *m = SendSmsWithProviderResp{} }
func (m *SendSmsWithProviderResp) String() string { return proto.CompactTextString(m) }
func (*SendSmsWithProviderResp) ProtoMessage()    {}
func (*SendSmsWithProviderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{26}
}
func (m *SendSmsWithProviderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendSmsWithProviderResp.Unmarshal(m, b)
}
func (m *SendSmsWithProviderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendSmsWithProviderResp.Marshal(b, m, deterministic)
}
func (dst *SendSmsWithProviderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendSmsWithProviderResp.Merge(dst, src)
}
func (m *SendSmsWithProviderResp) XXX_Size() int {
	return xxx_messageInfo_SendSmsWithProviderResp.Size(m)
}
func (m *SendSmsWithProviderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendSmsWithProviderResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendSmsWithProviderResp proto.InternalMessageInfo

type RecordVerifyCodePassReq struct {
	VerifyCodeKey        string   `protobuf:"bytes,1,opt,name=verify_code_key,json=verifyCodeKey,proto3" json:"verify_code_key,omitempty"`
	VerifyAt             uint32   `protobuf:"varint,2,opt,name=verify_at,json=verifyAt,proto3" json:"verify_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordVerifyCodePassReq) Reset()         { *m = RecordVerifyCodePassReq{} }
func (m *RecordVerifyCodePassReq) String() string { return proto.CompactTextString(m) }
func (*RecordVerifyCodePassReq) ProtoMessage()    {}
func (*RecordVerifyCodePassReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{27}
}
func (m *RecordVerifyCodePassReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordVerifyCodePassReq.Unmarshal(m, b)
}
func (m *RecordVerifyCodePassReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordVerifyCodePassReq.Marshal(b, m, deterministic)
}
func (dst *RecordVerifyCodePassReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordVerifyCodePassReq.Merge(dst, src)
}
func (m *RecordVerifyCodePassReq) XXX_Size() int {
	return xxx_messageInfo_RecordVerifyCodePassReq.Size(m)
}
func (m *RecordVerifyCodePassReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordVerifyCodePassReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecordVerifyCodePassReq proto.InternalMessageInfo

func (m *RecordVerifyCodePassReq) GetVerifyCodeKey() string {
	if m != nil {
		return m.VerifyCodeKey
	}
	return ""
}

func (m *RecordVerifyCodePassReq) GetVerifyAt() uint32 {
	if m != nil {
		return m.VerifyAt
	}
	return 0
}

type RecordVerifyCodePassResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordVerifyCodePassResp) Reset()         { *m = RecordVerifyCodePassResp{} }
func (m *RecordVerifyCodePassResp) String() string { return proto.CompactTextString(m) }
func (*RecordVerifyCodePassResp) ProtoMessage()    {}
func (*RecordVerifyCodePassResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{28}
}
func (m *RecordVerifyCodePassResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordVerifyCodePassResp.Unmarshal(m, b)
}
func (m *RecordVerifyCodePassResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordVerifyCodePassResp.Marshal(b, m, deterministic)
}
func (dst *RecordVerifyCodePassResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordVerifyCodePassResp.Merge(dst, src)
}
func (m *RecordVerifyCodePassResp) XXX_Size() int {
	return xxx_messageInfo_RecordVerifyCodePassResp.Size(m)
}
func (m *RecordVerifyCodePassResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordVerifyCodePassResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecordVerifyCodePassResp proto.InternalMessageInfo

// 多个phone 对应一个message 或者
// 多个phone 对应 同样数量的message，对应关系发送
type SendMarketingSmsReq struct {
	Phones               []string `protobuf:"bytes,1,rep,name=phones,proto3" json:"phones,omitempty"`
	Messages             []string `protobuf:"bytes,2,rep,name=messages,proto3" json:"messages,omitempty"`
	BizId                uint32   `protobuf:"varint,3,opt,name=biz_id,json=bizId,proto3" json:"biz_id,omitempty"`
	ExtBizId             uint32   `protobuf:"varint,4,opt,name=ext_biz_id,json=extBizId,proto3" json:"ext_biz_id,omitempty"`
	AppId                string   `protobuf:"bytes,5,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendMarketingSmsReq) Reset()         { *m = SendMarketingSmsReq{} }
func (m *SendMarketingSmsReq) String() string { return proto.CompactTextString(m) }
func (*SendMarketingSmsReq) ProtoMessage()    {}
func (*SendMarketingSmsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{29}
}
func (m *SendMarketingSmsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendMarketingSmsReq.Unmarshal(m, b)
}
func (m *SendMarketingSmsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendMarketingSmsReq.Marshal(b, m, deterministic)
}
func (dst *SendMarketingSmsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendMarketingSmsReq.Merge(dst, src)
}
func (m *SendMarketingSmsReq) XXX_Size() int {
	return xxx_messageInfo_SendMarketingSmsReq.Size(m)
}
func (m *SendMarketingSmsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendMarketingSmsReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendMarketingSmsReq proto.InternalMessageInfo

func (m *SendMarketingSmsReq) GetPhones() []string {
	if m != nil {
		return m.Phones
	}
	return nil
}

func (m *SendMarketingSmsReq) GetMessages() []string {
	if m != nil {
		return m.Messages
	}
	return nil
}

func (m *SendMarketingSmsReq) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *SendMarketingSmsReq) GetExtBizId() uint32 {
	if m != nil {
		return m.ExtBizId
	}
	return 0
}

func (m *SendMarketingSmsReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

type SendMarketingPhoneErrResult struct {
	Phone                string   `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	Code                 int32    `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendMarketingPhoneErrResult) Reset()         { *m = SendMarketingPhoneErrResult{} }
func (m *SendMarketingPhoneErrResult) String() string { return proto.CompactTextString(m) }
func (*SendMarketingPhoneErrResult) ProtoMessage()    {}
func (*SendMarketingPhoneErrResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{30}
}
func (m *SendMarketingPhoneErrResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendMarketingPhoneErrResult.Unmarshal(m, b)
}
func (m *SendMarketingPhoneErrResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendMarketingPhoneErrResult.Marshal(b, m, deterministic)
}
func (dst *SendMarketingPhoneErrResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendMarketingPhoneErrResult.Merge(dst, src)
}
func (m *SendMarketingPhoneErrResult) XXX_Size() int {
	return xxx_messageInfo_SendMarketingPhoneErrResult.Size(m)
}
func (m *SendMarketingPhoneErrResult) XXX_DiscardUnknown() {
	xxx_messageInfo_SendMarketingPhoneErrResult.DiscardUnknown(m)
}

var xxx_messageInfo_SendMarketingPhoneErrResult proto.InternalMessageInfo

func (m *SendMarketingPhoneErrResult) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *SendMarketingPhoneErrResult) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

type SendMarketingSmsResp struct {
	ReqResult            int32                          `protobuf:"varint,1,opt,name=req_result,json=reqResult,proto3" json:"req_result,omitempty"`
	ErrPhones            []*SendMarketingPhoneErrResult `protobuf:"bytes,2,rep,name=err_phones,json=errPhones,proto3" json:"err_phones,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *SendMarketingSmsResp) Reset()         { *m = SendMarketingSmsResp{} }
func (m *SendMarketingSmsResp) String() string { return proto.CompactTextString(m) }
func (*SendMarketingSmsResp) ProtoMessage()    {}
func (*SendMarketingSmsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{31}
}
func (m *SendMarketingSmsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendMarketingSmsResp.Unmarshal(m, b)
}
func (m *SendMarketingSmsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendMarketingSmsResp.Marshal(b, m, deterministic)
}
func (dst *SendMarketingSmsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendMarketingSmsResp.Merge(dst, src)
}
func (m *SendMarketingSmsResp) XXX_Size() int {
	return xxx_messageInfo_SendMarketingSmsResp.Size(m)
}
func (m *SendMarketingSmsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendMarketingSmsResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendMarketingSmsResp proto.InternalMessageInfo

func (m *SendMarketingSmsResp) GetReqResult() int32 {
	if m != nil {
		return m.ReqResult
	}
	return 0
}

func (m *SendMarketingSmsResp) GetErrPhones() []*SendMarketingPhoneErrResult {
	if m != nil {
		return m.ErrPhones
	}
	return nil
}

type AuthSmsReq struct {
	AppId                string   `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppSecret            string   `protobuf:"bytes,2,opt,name=app_secret,json=appSecret,proto3" json:"app_secret,omitempty"`
	ClientIp             string   `protobuf:"bytes,3,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AuthSmsReq) Reset()         { *m = AuthSmsReq{} }
func (m *AuthSmsReq) String() string { return proto.CompactTextString(m) }
func (*AuthSmsReq) ProtoMessage()    {}
func (*AuthSmsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{32}
}
func (m *AuthSmsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuthSmsReq.Unmarshal(m, b)
}
func (m *AuthSmsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuthSmsReq.Marshal(b, m, deterministic)
}
func (dst *AuthSmsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuthSmsReq.Merge(dst, src)
}
func (m *AuthSmsReq) XXX_Size() int {
	return xxx_messageInfo_AuthSmsReq.Size(m)
}
func (m *AuthSmsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AuthSmsReq.DiscardUnknown(m)
}

var xxx_messageInfo_AuthSmsReq proto.InternalMessageInfo

func (m *AuthSmsReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *AuthSmsReq) GetAppSecret() string {
	if m != nil {
		return m.AppSecret
	}
	return ""
}

func (m *AuthSmsReq) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

type AuthSmsResp struct {
	Result               int32    `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	BizId                uint32   `protobuf:"varint,2,opt,name=biz_id,json=bizId,proto3" json:"biz_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AuthSmsResp) Reset()         { *m = AuthSmsResp{} }
func (m *AuthSmsResp) String() string { return proto.CompactTextString(m) }
func (*AuthSmsResp) ProtoMessage()    {}
func (*AuthSmsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{33}
}
func (m *AuthSmsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuthSmsResp.Unmarshal(m, b)
}
func (m *AuthSmsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuthSmsResp.Marshal(b, m, deterministic)
}
func (dst *AuthSmsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuthSmsResp.Merge(dst, src)
}
func (m *AuthSmsResp) XXX_Size() int {
	return xxx_messageInfo_AuthSmsResp.Size(m)
}
func (m *AuthSmsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AuthSmsResp.DiscardUnknown(m)
}

var xxx_messageInfo_AuthSmsResp proto.InternalMessageInfo

func (m *AuthSmsResp) GetResult() int32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func (m *AuthSmsResp) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

type GetPhoneSendCountReq struct {
	Phone                string   `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPhoneSendCountReq) Reset()         { *m = GetPhoneSendCountReq{} }
func (m *GetPhoneSendCountReq) String() string { return proto.CompactTextString(m) }
func (*GetPhoneSendCountReq) ProtoMessage()    {}
func (*GetPhoneSendCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{34}
}
func (m *GetPhoneSendCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPhoneSendCountReq.Unmarshal(m, b)
}
func (m *GetPhoneSendCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPhoneSendCountReq.Marshal(b, m, deterministic)
}
func (dst *GetPhoneSendCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPhoneSendCountReq.Merge(dst, src)
}
func (m *GetPhoneSendCountReq) XXX_Size() int {
	return xxx_messageInfo_GetPhoneSendCountReq.Size(m)
}
func (m *GetPhoneSendCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPhoneSendCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPhoneSendCountReq proto.InternalMessageInfo

func (m *GetPhoneSendCountReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

type GetPhoneSendCountResp struct {
	Count                int64    `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPhoneSendCountResp) Reset()         { *m = GetPhoneSendCountResp{} }
func (m *GetPhoneSendCountResp) String() string { return proto.CompactTextString(m) }
func (*GetPhoneSendCountResp) ProtoMessage()    {}
func (*GetPhoneSendCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{35}
}
func (m *GetPhoneSendCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPhoneSendCountResp.Unmarshal(m, b)
}
func (m *GetPhoneSendCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPhoneSendCountResp.Marshal(b, m, deterministic)
}
func (dst *GetPhoneSendCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPhoneSendCountResp.Merge(dst, src)
}
func (m *GetPhoneSendCountResp) XXX_Size() int {
	return xxx_messageInfo_GetPhoneSendCountResp.Size(m)
}
func (m *GetPhoneSendCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPhoneSendCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPhoneSendCountResp proto.InternalMessageInfo

func (m *GetPhoneSendCountResp) GetCount() int64 {
	if m != nil {
		return m.Count
	}
	return 0
}

type ClearPhoneSendCountReq struct {
	Phone                string   `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearPhoneSendCountReq) Reset()         { *m = ClearPhoneSendCountReq{} }
func (m *ClearPhoneSendCountReq) String() string { return proto.CompactTextString(m) }
func (*ClearPhoneSendCountReq) ProtoMessage()    {}
func (*ClearPhoneSendCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{36}
}
func (m *ClearPhoneSendCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearPhoneSendCountReq.Unmarshal(m, b)
}
func (m *ClearPhoneSendCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearPhoneSendCountReq.Marshal(b, m, deterministic)
}
func (dst *ClearPhoneSendCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearPhoneSendCountReq.Merge(dst, src)
}
func (m *ClearPhoneSendCountReq) XXX_Size() int {
	return xxx_messageInfo_ClearPhoneSendCountReq.Size(m)
}
func (m *ClearPhoneSendCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearPhoneSendCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClearPhoneSendCountReq proto.InternalMessageInfo

func (m *ClearPhoneSendCountReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

type ClearPhoneSendCountResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearPhoneSendCountResp) Reset()         { *m = ClearPhoneSendCountResp{} }
func (m *ClearPhoneSendCountResp) String() string { return proto.CompactTextString(m) }
func (*ClearPhoneSendCountResp) ProtoMessage()    {}
func (*ClearPhoneSendCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sms_go_35a7b61c8a478788, []int{37}
}
func (m *ClearPhoneSendCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearPhoneSendCountResp.Unmarshal(m, b)
}
func (m *ClearPhoneSendCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearPhoneSendCountResp.Marshal(b, m, deterministic)
}
func (dst *ClearPhoneSendCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearPhoneSendCountResp.Merge(dst, src)
}
func (m *ClearPhoneSendCountResp) XXX_Size() int {
	return xxx_messageInfo_ClearPhoneSendCountResp.Size(m)
}
func (m *ClearPhoneSendCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearPhoneSendCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_ClearPhoneSendCountResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*SendSmsReq)(nil), "sms_go.SendSmsReq")
	proto.RegisterType((*SendSmsResp)(nil), "sms_go.SendSmsResp")
	proto.RegisterType((*UserInfo)(nil), "sms_go.UserInfo")
	proto.RegisterType((*SendCommonSmsReq)(nil), "sms_go.SendCommonSmsReq")
	proto.RegisterType((*ErrorPhone)(nil), "sms_go.ErrorPhone")
	proto.RegisterType((*SendCommonSmsResp)(nil), "sms_go.SendCommonSmsResp")
	proto.RegisterType((*SendRandomVerifyCodeReq)(nil), "sms_go.SendRandomVerifyCodeReq")
	proto.RegisterType((*SendRandomVerifyCodeResp)(nil), "sms_go.SendRandomVerifyCodeResp")
	proto.RegisterType((*ValidateVerifyCodeReq)(nil), "sms_go.ValidateVerifyCodeReq")
	proto.RegisterType((*ValidateVerifyCodeResp)(nil), "sms_go.ValidateVerifyCodeResp")
	proto.RegisterType((*DirectSendSmsReq)(nil), "sms_go.DirectSendSmsReq")
	proto.RegisterType((*DirectSendSmsResp)(nil), "sms_go.DirectSendSmsResp")
	proto.RegisterType((*CreateVerifyCodeReq)(nil), "sms_go.CreateVerifyCodeReq")
	proto.RegisterType((*CreateVerifyCodeResp)(nil), "sms_go.CreateVerifyCodeResp")
	proto.RegisterType((*CheckUrlValidApkUrlReq)(nil), "sms_go.CheckUrlValidApkUrlReq")
	proto.RegisterType((*CheckUrlValidApkUrlResp)(nil), "sms_go.CheckUrlValidApkUrlResp")
	proto.RegisterType((*DownLoadUrlReq)(nil), "sms_go.DownLoadUrlReq")
	proto.RegisterType((*DownLoadUrlResp)(nil), "sms_go.DownLoadUrlResp")
	proto.RegisterType((*DownLoadUrlByteReq)(nil), "sms_go.DownLoadUrlByteReq")
	proto.RegisterType((*DownLoadUrlByteResp)(nil), "sms_go.DownLoadUrlByteResp")
	proto.RegisterType((*Foo)(nil), "sms_go.Foo")
	proto.RegisterType((*PostUrlDataReq)(nil), "sms_go.PostUrlDataReq")
	proto.RegisterType((*PostUrlDataResp)(nil), "sms_go.PostUrlDataResp")
	proto.RegisterType((*SendVoiceVerifyCodeReq)(nil), "sms_go.SendVoiceVerifyCodeReq")
	proto.RegisterType((*SendVoiceVerifyCodeResp)(nil), "sms_go.SendVoiceVerifyCodeResp")
	proto.RegisterType((*SendSmsWithProviderReq)(nil), "sms_go.SendSmsWithProviderReq")
	proto.RegisterType((*SendSmsWithProviderResp)(nil), "sms_go.SendSmsWithProviderResp")
	proto.RegisterType((*RecordVerifyCodePassReq)(nil), "sms_go.RecordVerifyCodePassReq")
	proto.RegisterType((*RecordVerifyCodePassResp)(nil), "sms_go.RecordVerifyCodePassResp")
	proto.RegisterType((*SendMarketingSmsReq)(nil), "sms_go.SendMarketingSmsReq")
	proto.RegisterType((*SendMarketingPhoneErrResult)(nil), "sms_go.SendMarketingPhoneErrResult")
	proto.RegisterType((*SendMarketingSmsResp)(nil), "sms_go.SendMarketingSmsResp")
	proto.RegisterType((*AuthSmsReq)(nil), "sms_go.AuthSmsReq")
	proto.RegisterType((*AuthSmsResp)(nil), "sms_go.AuthSmsResp")
	proto.RegisterType((*GetPhoneSendCountReq)(nil), "sms_go.GetPhoneSendCountReq")
	proto.RegisterType((*GetPhoneSendCountResp)(nil), "sms_go.GetPhoneSendCountResp")
	proto.RegisterType((*ClearPhoneSendCountReq)(nil), "sms_go.ClearPhoneSendCountReq")
	proto.RegisterType((*ClearPhoneSendCountResp)(nil), "sms_go.ClearPhoneSendCountResp")
	proto.RegisterEnum("sms_go.ERR_SMS", ERR_SMS_name, ERR_SMS_value)
	proto.RegisterEnum("sms_go.AuthSmsResp_Result", AuthSmsResp_Result_name, AuthSmsResp_Result_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SmsClient is the client API for Sms service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SmsClient interface {
	SendCommonSms(ctx context.Context, in *SendCommonSmsReq, opts ...grpc.CallOption) (*SendCommonSmsResp, error)
	SendRandomVerifyCode(ctx context.Context, in *SendRandomVerifyCodeReq, opts ...grpc.CallOption) (*SendRandomVerifyCodeResp, error)
	ValidateVerifyCode(ctx context.Context, in *ValidateVerifyCodeReq, opts ...grpc.CallOption) (*ValidateVerifyCodeResp, error)
	SendSms(ctx context.Context, in *SendSmsReq, opts ...grpc.CallOption) (*SendSmsResp, error)
	// 这个接口测试用, 业务不要调用该接口
	SendSmsWithProvider(ctx context.Context, in *SendSmsWithProviderReq, opts ...grpc.CallOption) (*SendSmsWithProviderResp, error)
	CheckUrlValidApkUrl(ctx context.Context, in *CheckUrlValidApkUrlReq, opts ...grpc.CallOption) (*CheckUrlValidApkUrlResp, error)
	DownLoadUrl(ctx context.Context, in *DownLoadUrlReq, opts ...grpc.CallOption) (*DownLoadUrlResp, error)
	DirectSendSms(ctx context.Context, in *DirectSendSmsReq, opts ...grpc.CallOption) (*DirectSendSmsResp, error)
	PostUrlData(ctx context.Context, in *PostUrlDataReq, opts ...grpc.CallOption) (*PostUrlDataResp, error)
	DownLoadUrlByte(ctx context.Context, in *DownLoadUrlByteReq, opts ...grpc.CallOption) (*DownLoadUrlByteResp, error)
	SendVoiceVerifyCode(ctx context.Context, in *SendVoiceVerifyCodeReq, opts ...grpc.CallOption) (*SendVoiceVerifyCodeResp, error)
	RecordVerifyCodePass(ctx context.Context, in *RecordVerifyCodePassReq, opts ...grpc.CallOption) (*RecordVerifyCodePassResp, error)
	SendMarketingSms(ctx context.Context, in *SendMarketingSmsReq, opts ...grpc.CallOption) (*SendMarketingSmsResp, error)
	AuthSms(ctx context.Context, in *AuthSmsReq, opts ...grpc.CallOption) (*AuthSmsResp, error)
	GetPhoneSendCount(ctx context.Context, in *GetPhoneSendCountReq, opts ...grpc.CallOption) (*GetPhoneSendCountResp, error)
	ClearPhoneSendCount(ctx context.Context, in *ClearPhoneSendCountReq, opts ...grpc.CallOption) (*ClearPhoneSendCountResp, error)
}

type smsClient struct {
	cc *grpc.ClientConn
}

func NewSmsClient(cc *grpc.ClientConn) SmsClient {
	return &smsClient{cc}
}

func (c *smsClient) SendCommonSms(ctx context.Context, in *SendCommonSmsReq, opts ...grpc.CallOption) (*SendCommonSmsResp, error) {
	out := new(SendCommonSmsResp)
	err := c.cc.Invoke(ctx, "/sms_go.Sms/SendCommonSms", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) SendRandomVerifyCode(ctx context.Context, in *SendRandomVerifyCodeReq, opts ...grpc.CallOption) (*SendRandomVerifyCodeResp, error) {
	out := new(SendRandomVerifyCodeResp)
	err := c.cc.Invoke(ctx, "/sms_go.Sms/SendRandomVerifyCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) ValidateVerifyCode(ctx context.Context, in *ValidateVerifyCodeReq, opts ...grpc.CallOption) (*ValidateVerifyCodeResp, error) {
	out := new(ValidateVerifyCodeResp)
	err := c.cc.Invoke(ctx, "/sms_go.Sms/ValidateVerifyCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) SendSms(ctx context.Context, in *SendSmsReq, opts ...grpc.CallOption) (*SendSmsResp, error) {
	out := new(SendSmsResp)
	err := c.cc.Invoke(ctx, "/sms_go.Sms/SendSms", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) SendSmsWithProvider(ctx context.Context, in *SendSmsWithProviderReq, opts ...grpc.CallOption) (*SendSmsWithProviderResp, error) {
	out := new(SendSmsWithProviderResp)
	err := c.cc.Invoke(ctx, "/sms_go.Sms/SendSmsWithProvider", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) CheckUrlValidApkUrl(ctx context.Context, in *CheckUrlValidApkUrlReq, opts ...grpc.CallOption) (*CheckUrlValidApkUrlResp, error) {
	out := new(CheckUrlValidApkUrlResp)
	err := c.cc.Invoke(ctx, "/sms_go.Sms/CheckUrlValidApkUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) DownLoadUrl(ctx context.Context, in *DownLoadUrlReq, opts ...grpc.CallOption) (*DownLoadUrlResp, error) {
	out := new(DownLoadUrlResp)
	err := c.cc.Invoke(ctx, "/sms_go.Sms/DownLoadUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) DirectSendSms(ctx context.Context, in *DirectSendSmsReq, opts ...grpc.CallOption) (*DirectSendSmsResp, error) {
	out := new(DirectSendSmsResp)
	err := c.cc.Invoke(ctx, "/sms_go.Sms/DirectSendSms", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) PostUrlData(ctx context.Context, in *PostUrlDataReq, opts ...grpc.CallOption) (*PostUrlDataResp, error) {
	out := new(PostUrlDataResp)
	err := c.cc.Invoke(ctx, "/sms_go.Sms/PostUrlData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) DownLoadUrlByte(ctx context.Context, in *DownLoadUrlByteReq, opts ...grpc.CallOption) (*DownLoadUrlByteResp, error) {
	out := new(DownLoadUrlByteResp)
	err := c.cc.Invoke(ctx, "/sms_go.Sms/DownLoadUrlByte", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) SendVoiceVerifyCode(ctx context.Context, in *SendVoiceVerifyCodeReq, opts ...grpc.CallOption) (*SendVoiceVerifyCodeResp, error) {
	out := new(SendVoiceVerifyCodeResp)
	err := c.cc.Invoke(ctx, "/sms_go.Sms/SendVoiceVerifyCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) RecordVerifyCodePass(ctx context.Context, in *RecordVerifyCodePassReq, opts ...grpc.CallOption) (*RecordVerifyCodePassResp, error) {
	out := new(RecordVerifyCodePassResp)
	err := c.cc.Invoke(ctx, "/sms_go.Sms/RecordVerifyCodePass", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) SendMarketingSms(ctx context.Context, in *SendMarketingSmsReq, opts ...grpc.CallOption) (*SendMarketingSmsResp, error) {
	out := new(SendMarketingSmsResp)
	err := c.cc.Invoke(ctx, "/sms_go.Sms/SendMarketingSms", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) AuthSms(ctx context.Context, in *AuthSmsReq, opts ...grpc.CallOption) (*AuthSmsResp, error) {
	out := new(AuthSmsResp)
	err := c.cc.Invoke(ctx, "/sms_go.Sms/AuthSms", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) GetPhoneSendCount(ctx context.Context, in *GetPhoneSendCountReq, opts ...grpc.CallOption) (*GetPhoneSendCountResp, error) {
	out := new(GetPhoneSendCountResp)
	err := c.cc.Invoke(ctx, "/sms_go.Sms/GetPhoneSendCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsClient) ClearPhoneSendCount(ctx context.Context, in *ClearPhoneSendCountReq, opts ...grpc.CallOption) (*ClearPhoneSendCountResp, error) {
	out := new(ClearPhoneSendCountResp)
	err := c.cc.Invoke(ctx, "/sms_go.Sms/ClearPhoneSendCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SmsServer is the server API for Sms service.
type SmsServer interface {
	SendCommonSms(context.Context, *SendCommonSmsReq) (*SendCommonSmsResp, error)
	SendRandomVerifyCode(context.Context, *SendRandomVerifyCodeReq) (*SendRandomVerifyCodeResp, error)
	ValidateVerifyCode(context.Context, *ValidateVerifyCodeReq) (*ValidateVerifyCodeResp, error)
	SendSms(context.Context, *SendSmsReq) (*SendSmsResp, error)
	// 这个接口测试用, 业务不要调用该接口
	SendSmsWithProvider(context.Context, *SendSmsWithProviderReq) (*SendSmsWithProviderResp, error)
	CheckUrlValidApkUrl(context.Context, *CheckUrlValidApkUrlReq) (*CheckUrlValidApkUrlResp, error)
	DownLoadUrl(context.Context, *DownLoadUrlReq) (*DownLoadUrlResp, error)
	DirectSendSms(context.Context, *DirectSendSmsReq) (*DirectSendSmsResp, error)
	PostUrlData(context.Context, *PostUrlDataReq) (*PostUrlDataResp, error)
	DownLoadUrlByte(context.Context, *DownLoadUrlByteReq) (*DownLoadUrlByteResp, error)
	SendVoiceVerifyCode(context.Context, *SendVoiceVerifyCodeReq) (*SendVoiceVerifyCodeResp, error)
	RecordVerifyCodePass(context.Context, *RecordVerifyCodePassReq) (*RecordVerifyCodePassResp, error)
	SendMarketingSms(context.Context, *SendMarketingSmsReq) (*SendMarketingSmsResp, error)
	AuthSms(context.Context, *AuthSmsReq) (*AuthSmsResp, error)
	GetPhoneSendCount(context.Context, *GetPhoneSendCountReq) (*GetPhoneSendCountResp, error)
	ClearPhoneSendCount(context.Context, *ClearPhoneSendCountReq) (*ClearPhoneSendCountResp, error)
}

func RegisterSmsServer(s *grpc.Server, srv SmsServer) {
	s.RegisterService(&_Sms_serviceDesc, srv)
}

func _Sms_SendCommonSms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendCommonSmsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).SendCommonSms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/SendCommonSms",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).SendCommonSms(ctx, req.(*SendCommonSmsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_SendRandomVerifyCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendRandomVerifyCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).SendRandomVerifyCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/SendRandomVerifyCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).SendRandomVerifyCode(ctx, req.(*SendRandomVerifyCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_ValidateVerifyCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateVerifyCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).ValidateVerifyCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/ValidateVerifyCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).ValidateVerifyCode(ctx, req.(*ValidateVerifyCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_SendSms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSmsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).SendSms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/SendSms",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).SendSms(ctx, req.(*SendSmsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_SendSmsWithProvider_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSmsWithProviderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).SendSmsWithProvider(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/SendSmsWithProvider",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).SendSmsWithProvider(ctx, req.(*SendSmsWithProviderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_CheckUrlValidApkUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUrlValidApkUrlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).CheckUrlValidApkUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/CheckUrlValidApkUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).CheckUrlValidApkUrl(ctx, req.(*CheckUrlValidApkUrlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_DownLoadUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownLoadUrlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).DownLoadUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/DownLoadUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).DownLoadUrl(ctx, req.(*DownLoadUrlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_DirectSendSms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DirectSendSmsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).DirectSendSms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/DirectSendSms",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).DirectSendSms(ctx, req.(*DirectSendSmsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_PostUrlData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PostUrlDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).PostUrlData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/PostUrlData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).PostUrlData(ctx, req.(*PostUrlDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_DownLoadUrlByte_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownLoadUrlByteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).DownLoadUrlByte(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/DownLoadUrlByte",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).DownLoadUrlByte(ctx, req.(*DownLoadUrlByteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_SendVoiceVerifyCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendVoiceVerifyCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).SendVoiceVerifyCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/SendVoiceVerifyCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).SendVoiceVerifyCode(ctx, req.(*SendVoiceVerifyCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_RecordVerifyCodePass_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordVerifyCodePassReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).RecordVerifyCodePass(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/RecordVerifyCodePass",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).RecordVerifyCodePass(ctx, req.(*RecordVerifyCodePassReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_SendMarketingSms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMarketingSmsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).SendMarketingSms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/SendMarketingSms",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).SendMarketingSms(ctx, req.(*SendMarketingSmsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_AuthSms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthSmsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).AuthSms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/AuthSms",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).AuthSms(ctx, req.(*AuthSmsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_GetPhoneSendCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPhoneSendCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).GetPhoneSendCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/GetPhoneSendCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).GetPhoneSendCount(ctx, req.(*GetPhoneSendCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sms_ClearPhoneSendCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearPhoneSendCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsServer).ClearPhoneSendCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sms_go.Sms/ClearPhoneSendCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsServer).ClearPhoneSendCount(ctx, req.(*ClearPhoneSendCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Sms_serviceDesc = grpc.ServiceDesc{
	ServiceName: "sms_go.Sms",
	HandlerType: (*SmsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendCommonSms",
			Handler:    _Sms_SendCommonSms_Handler,
		},
		{
			MethodName: "SendRandomVerifyCode",
			Handler:    _Sms_SendRandomVerifyCode_Handler,
		},
		{
			MethodName: "ValidateVerifyCode",
			Handler:    _Sms_ValidateVerifyCode_Handler,
		},
		{
			MethodName: "SendSms",
			Handler:    _Sms_SendSms_Handler,
		},
		{
			MethodName: "SendSmsWithProvider",
			Handler:    _Sms_SendSmsWithProvider_Handler,
		},
		{
			MethodName: "CheckUrlValidApkUrl",
			Handler:    _Sms_CheckUrlValidApkUrl_Handler,
		},
		{
			MethodName: "DownLoadUrl",
			Handler:    _Sms_DownLoadUrl_Handler,
		},
		{
			MethodName: "DirectSendSms",
			Handler:    _Sms_DirectSendSms_Handler,
		},
		{
			MethodName: "PostUrlData",
			Handler:    _Sms_PostUrlData_Handler,
		},
		{
			MethodName: "DownLoadUrlByte",
			Handler:    _Sms_DownLoadUrlByte_Handler,
		},
		{
			MethodName: "SendVoiceVerifyCode",
			Handler:    _Sms_SendVoiceVerifyCode_Handler,
		},
		{
			MethodName: "RecordVerifyCodePass",
			Handler:    _Sms_RecordVerifyCodePass_Handler,
		},
		{
			MethodName: "SendMarketingSms",
			Handler:    _Sms_SendMarketingSms_Handler,
		},
		{
			MethodName: "AuthSms",
			Handler:    _Sms_AuthSms_Handler,
		},
		{
			MethodName: "GetPhoneSendCount",
			Handler:    _Sms_GetPhoneSendCount_Handler,
		},
		{
			MethodName: "ClearPhoneSendCount",
			Handler:    _Sms_ClearPhoneSendCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/sms/sms-go.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/sms/sms-go.proto", fileDescriptor_sms_go_35a7b61c8a478788)
}

var fileDescriptor_sms_go_35a7b61c8a478788 = []byte{
	// 1901 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x58, 0x5b, 0x73, 0xdb, 0xb8,
	0x15, 0xb6, 0x2c, 0xcb, 0x96, 0x8e, 0x6c, 0x87, 0x86, 0x6f, 0xb2, 0xbc, 0x8e, 0x5d, 0x64, 0x2f,
	0x6e, 0xba, 0xb1, 0x3b, 0x69, 0x77, 0x9f, 0xfa, 0x10, 0x59, 0xa6, 0x62, 0x4d, 0x64, 0x49, 0x85,
	0x24, 0x77, 0xd3, 0xce, 0x94, 0xc3, 0x88, 0x88, 0xcc, 0x46, 0x22, 0x19, 0x02, 0x72, 0xe2, 0x7d,
	0xea, 0x7b, 0x9f, 0xfb, 0x0b, 0xfa, 0x03, 0xfa, 0x2b, 0xfa, 0x3b, 0x7a, 0xbf, 0xb7, 0x0f, 0x7d,
	0xe8, 0x73, 0x3b, 0x00, 0x41, 0x91, 0x94, 0x28, 0xaf, 0x77, 0x3d, 0xe3, 0x19, 0xe0, 0xe0, 0x03,
	0xce, 0x0d, 0x38, 0xe7, 0xa3, 0xe0, 0x90, 0xf3, 0xd3, 0xb7, 0x63, 0xbb, 0xff, 0x86, 0xd9, 0xc3,
	0x1b, 0xea, 0x9f, 0xb2, 0x11, 0x13, 0xff, 0x4f, 0x06, 0xee, 0x89, 0xe7, 0xbb, 0xdc, 0x45, 0xcb,
	0x6c, 0xc4, 0x8c, 0x81, 0x8b, 0xff, 0xbd, 0x08, 0xd0, 0xa1, 0x8e, 0xd5, 0x19, 0x31, 0x42, 0xdf,
	0xa2, 0x2d, 0xc8, 0x79, 0xd7, 0xae, 0x43, 0x4b, 0x99, 0xa3, 0xcc, 0x71, 0x81, 0x04, 0x13, 0xb4,
	0x07, 0x79, 0x01, 0xe7, 0xb7, 0x1e, 0x2d, 0x2d, 0x1e, 0x65, 0x8e, 0xd7, 0xc8, 0x0a, 0x1b, 0xb1,
	0xee, 0xad, 0x47, 0xd1, 0x01, 0x80, 0x67, 0xfa, 0xe6, 0xc8, 0x18, 0xda, 0x8c, 0x97, 0xb2, 0x47,
	0xd9, 0xe3, 0x02, 0x29, 0x48, 0x49, 0xc3, 0x66, 0x1c, 0x7d, 0x1b, 0xb4, 0x77, 0x36, 0xbf, 0x76,
	0xc7, 0xdc, 0xe8, 0xbb, 0xee, 0xd0, 0x72, 0xdf, 0x39, 0xa5, 0xa5, 0xa3, 0xcc, 0x71, 0x9e, 0x3c,
	0x50, 0xf2, 0xaa, 0x12, 0xa3, 0x8f, 0xe1, 0xc1, 0x0d, 0xf5, 0xed, 0xd7, 0xb7, 0x46, 0xdf, 0xb5,
	0xa8, 0xf1, 0x86, 0xde, 0x96, 0x72, 0xd2, 0x88, 0xb5, 0x40, 0x5c, 0x75, 0x2d, 0xfa, 0x82, 0xde,
	0xa2, 0xc7, 0xb0, 0x11, 0xc7, 0x8d, 0x99, 0x39, 0xa0, 0xa5, 0x65, 0x89, 0x7c, 0x10, 0x21, 0x7b,
	0x42, 0x8c, 0xf6, 0xa1, 0x30, 0x32, 0xfd, 0x37, 0x94, 0x1b, 0xb6, 0x55, 0x5a, 0x91, 0x96, 0xe7,
	0x03, 0x41, 0xdd, 0x42, 0xdb, 0xb0, 0xfc, 0xca, 0xfe, 0x52, 0xac, 0xe4, 0xe5, 0x4a, 0xee, 0x95,
	0xfd, 0x65, 0xdd, 0x42, 0x1f, 0x00, 0xd0, 0xf7, 0xdc, 0x50, 0x4b, 0x85, 0x60, 0x13, 0x7d, 0xcf,
	0xcf, 0xe4, 0xea, 0x01, 0x80, 0x4f, 0xdf, 0x8e, 0x29, 0x93, 0x47, 0x82, 0x54, 0x5b, 0x50, 0x92,
	0xba, 0x85, 0x0e, 0xa1, 0xd8, 0xf7, 0xa9, 0xc9, 0xa9, 0xc1, 0xed, 0x11, 0x2d, 0x15, 0x8f, 0x32,
	0xc7, 0x4b, 0x04, 0x02, 0x51, 0xd7, 0x1e, 0x51, 0xbc, 0x06, 0xc5, 0x49, 0xb8, 0x99, 0x87, 0x29,
	0xe4, 0x7b, 0x8c, 0xfa, 0x75, 0xe7, 0xb5, 0x3b, 0x27, 0xf6, 0x1a, 0x64, 0xc7, 0xb6, 0xa5, 0xc2,
	0x2e, 0x86, 0x08, 0xc1, 0x12, 0xb3, 0x07, 0x4e, 0x29, 0x2b, 0x61, 0x72, 0x3c, 0x95, 0x86, 0xa5,
	0xa9, 0x34, 0xe0, 0x5f, 0x64, 0x40, 0x13, 0x6a, 0xab, 0xee, 0x68, 0xe4, 0x3a, 0x2a, 0xd7, 0x9f,
	0xc3, 0xfa, 0x98, 0x51, 0xdf, 0xb0, 0x9d, 0xd7, 0x6e, 0xb0, 0x2f, 0x73, 0x94, 0x3d, 0x2e, 0x3e,
	0xd5, 0x4e, 0x82, 0xbb, 0x71, 0x12, 0x5a, 0x46, 0x56, 0xc7, 0x6a, 0x24, 0x73, 0xba, 0x05, 0xb9,
	0x20, 0xe8, 0x8b, 0x81, 0x9d, 0x72, 0x92, 0x9a, 0xe9, 0x6c, 0x6a, 0xa6, 0xf1, 0xe7, 0x00, 0xba,
	0xef, 0xbb, 0x7e, 0x5b, 0x3a, 0x98, 0xee, 0x36, 0x82, 0x25, 0x91, 0x5e, 0xa9, 0x23, 0x47, 0xe4,
	0x18, 0xbf, 0x82, 0x8d, 0x29, 0x27, 0x98, 0x87, 0x76, 0x60, 0xd9, 0xa7, 0x6c, 0x3c, 0xe4, 0x72,
	0x7f, 0x8e, 0xa8, 0x19, 0xfa, 0x0c, 0x56, 0xa9, 0x50, 0x62, 0xc8, 0xf3, 0x58, 0x69, 0x51, 0xfa,
	0x86, 0x42, 0xdf, 0x22, 0x03, 0x48, 0x91, 0x4e, 0xc6, 0x0c, 0xff, 0x3a, 0x03, 0xbb, 0x42, 0x09,
	0x31, 0x1d, 0xcb, 0x1d, 0x5d, 0x4d, 0xee, 0xd3, 0xfc, 0xc7, 0x71, 0x20, 0xee, 0x8b, 0x67, 0xfb,
	0xd4, 0xe0, 0x7c, 0xa8, 0xf2, 0x54, 0x08, 0x24, 0x5d, 0x3e, 0x14, 0xf6, 0x0d, 0xa9, 0x33, 0xe0,
	0xd7, 0x32, 0x1a, 0x6b, 0x44, 0xcd, 0xa2, 0x28, 0x2e, 0xc5, 0xa3, 0x78, 0x08, 0xc5, 0x28, 0x8f,
	0xac, 0x94, 0x93, 0x89, 0x84, 0x49, 0x22, 0x59, 0x78, 0x1d, 0x96, 0x27, 0xd7, 0x01, 0x53, 0x28,
	0xa5, 0x1b, 0x7c, 0x47, 0x70, 0x0e, 0xa1, 0x18, 0x7b, 0x43, 0x2a, 0x91, 0x10, 0xbd, 0x1e, 0xa1,
	0x86, 0xd9, 0x96, 0xba, 0x62, 0x62, 0x88, 0x2d, 0xd8, 0xbe, 0x32, 0x87, 0xb6, 0x65, 0x72, 0x7a,
	0x9f, 0xa8, 0xa4, 0x5f, 0x92, 0x29, 0xbd, 0xd9, 0x69, 0xbd, 0xf8, 0xbb, 0xb0, 0x93, 0xa6, 0x65,
	0xbe, 0x2b, 0xf8, 0x2d, 0x68, 0xe7, 0xb6, 0x4f, 0xfb, 0xfc, 0x2b, 0xab, 0x18, 0x82, 0x25, 0x4e,
	0xdf, 0x73, 0x65, 0x91, 0x1c, 0xc7, 0x6a, 0x40, 0x76, 0x7e, 0x0d, 0x58, 0x4a, 0xd6, 0x00, 0xbc,
	0x09, 0x1b, 0x53, 0x2a, 0x99, 0x87, 0xcf, 0x60, 0xb3, 0x2a, 0x9f, 0x79, 0x32, 0x3a, 0x1a, 0x64,
	0x45, 0x25, 0x0b, 0x0c, 0x11, 0x43, 0x51, 0x4c, 0x65, 0xe1, 0x1a, 0x52, 0x27, 0x2c, 0xa6, 0x62,
	0xde, 0xa0, 0x0e, 0xfe, 0x19, 0x6c, 0xcd, 0x9e, 0xc1, 0xbc, 0x94, 0x43, 0xbe, 0x32, 0x81, 0x87,
	0x50, 0x0c, 0x6f, 0xa5, 0x28, 0x44, 0x81, 0x77, 0xea, 0xa2, 0xca, 0x42, 0xf4, 0x18, 0x76, 0xaa,
	0xd7, 0xb4, 0xff, 0xa6, 0xe7, 0x0f, 0x65, 0xc4, 0x2b, 0x9e, 0x18, 0x2a, 0x93, 0xc7, 0xfe, 0x30,
	0xd4, 0x36, 0xf6, 0x87, 0xf8, 0x19, 0xec, 0xa6, 0x62, 0x99, 0x87, 0x3e, 0x82, 0xf5, 0xbe, 0xeb,
	0x70, 0xea, 0x70, 0x43, 0x5d, 0xf3, 0x8c, 0x54, 0xb5, 0xa6, 0xa4, 0x0d, 0x29, 0xc4, 0x3f, 0x80,
	0xf5, 0x73, 0xf7, 0x9d, 0xd3, 0x70, 0x4d, 0x6b, 0x9e, 0x16, 0x54, 0x82, 0x15, 0x61, 0xab, 0x3b,
	0xe6, 0x61, 0x5c, 0xd4, 0x14, 0x3f, 0x82, 0x07, 0x89, 0xdd, 0x41, 0x48, 0x46, 0x6c, 0x10, 0x6e,
	0x1f, 0xb1, 0x01, 0x7e, 0x06, 0x28, 0x06, 0x3a, 0xbb, 0xe5, 0xf4, 0xeb, 0xaa, 0xf9, 0x04, 0x36,
	0x67, 0x4e, 0x48, 0xaa, 0x5a, 0x0d, 0x54, 0xe5, 0x20, 0x5b, 0x73, 0x5d, 0x4c, 0x61, 0xbd, 0xed,
	0x32, 0xde, 0xf3, 0x87, 0xe7, 0x26, 0x37, 0xd3, 0xb5, 0x7d, 0x08, 0xeb, 0xd7, 0xd4, 0xb4, 0x62,
	0x45, 0x56, 0x14, 0xa2, 0x55, 0xb2, 0x2a, 0xa4, 0x93, 0x92, 0xba, 0x0f, 0x05, 0xcb, 0xe4, 0xa6,
	0x44, 0xc9, 0x5c, 0xad, 0x92, 0xbc, 0x10, 0x08, 0x00, 0xfe, 0x14, 0x1e, 0x24, 0xd4, 0x30, 0x4f,
	0xdc, 0x21, 0x9f, 0x32, 0xcf, 0x88, 0xec, 0x5a, 0x11, 0xf3, 0x4b, 0x36, 0xc0, 0xff, 0xcd, 0xc0,
	0x8e, 0xb8, 0x97, 0x57, 0xae, 0xdd, 0xbf, 0xd7, 0x4b, 0xbd, 0x4f, 0x2d, 0x18, 0x4f, 0x1e, 0x88,
	0xec, 0x40, 0x87, 0x50, 0x74, 0x4c, 0x6e, 0xbb, 0x4e, 0xb0, 0x25, 0x68, 0xd3, 0x10, 0x88, 0xe4,
	0x96, 0x03, 0x80, 0x1b, 0xa1, 0x3f, 0xa0, 0x0c, 0x41, 0xb1, 0x2a, 0x48, 0x49, 0x0a, 0x69, 0x58,
	0x99, 0x26, 0x0d, 0xdf, 0xa4, 0x31, 0xe3, 0xbd, 0xa0, 0x6e, 0xcf, 0xb8, 0xcd, 0x3c, 0xfc, 0x2b,
	0x15, 0x92, 0xce, 0x88, 0xfd, 0xc8, 0xe6, 0xd7, 0x6d, 0xdf, 0xbd, 0xb1, 0x2d, 0xea, 0x8b, 0x90,
	0x94, 0x21, 0xef, 0xa9, 0xa9, 0x8a, 0xca, 0x64, 0x2e, 0x2a, 0x4e, 0xac, 0x77, 0x14, 0x88, 0x9a,
	0x25, 0xd8, 0x50, 0xf6, 0x2e, 0x36, 0x34, 0xdd, 0x86, 0x93, 0x74, 0x24, 0x97, 0xa4, 0x23, 0xa1,
	0x03, 0x33, 0x46, 0x32, 0x0f, 0xff, 0x14, 0x76, 0x09, 0xed, 0xbb, 0xbe, 0x15, 0x39, 0xd6, 0x36,
	0x99, 0x2c, 0x75, 0x29, 0xac, 0x29, 0x93, 0xc6, 0x9a, 0xf6, 0xa1, 0xa0, 0x70, 0x66, 0x78, 0xef,
	0xf3, 0x81, 0xa0, 0xc2, 0x71, 0x19, 0x4a, 0xe9, 0xe7, 0x33, 0x0f, 0xff, 0x32, 0x03, 0x9b, 0xc2,
	0xae, 0x4b, 0x69, 0xa7, 0xed, 0x0c, 0x54, 0x8d, 0x8d, 0xa2, 0x93, 0x49, 0x44, 0xa7, 0x0c, 0xf9,
	0x11, 0x65, 0xa2, 0xda, 0x87, 0x71, 0x9b, 0xcc, 0xbf, 0x51, 0xb5, 0x15, 0x9b, 0x4c, 0xcf, 0x0b,
	0x23, 0x56, 0x20, 0x39, 0xd3, 0xf3, 0xea, 0x16, 0x7e, 0x0e, 0xfb, 0x09, 0xb3, 0x64, 0xff, 0xd6,
	0x7d, 0x11, 0x30, 0xd1, 0xe1, 0xee, 0xcf, 0x2a, 0x6e, 0x61, 0x6b, 0xd6, 0x3f, 0xe6, 0x29, 0xa6,
	0x67, 0x24, 0x9a, 0x8e, 0x60, 0x7a, 0x4a, 0xc1, 0x19, 0x00, 0xf5, 0xa7, 0xd8, 0xc5, 0xa3, 0x90,
	0x5d, 0xdc, 0x61, 0x19, 0x29, 0x50, 0x3f, 0x24, 0x1b, 0x06, 0x40, 0x65, 0xcc, 0xaf, 0x55, 0x44,
	0x23, 0x47, 0x33, 0x31, 0x47, 0x85, 0x1d, 0x42, 0xcc, 0x68, 0xdf, 0xa7, 0x61, 0xf3, 0x2a, 0x98,
	0x9e, 0xd7, 0x91, 0x02, 0x91, 0xd8, 0xfe, 0xd0, 0x16, 0xf5, 0xd7, 0xf6, 0x54, 0x43, 0xcd, 0x07,
	0x82, 0xba, 0x87, 0x7f, 0x9e, 0x81, 0xe2, 0x44, 0xc3, 0x1d, 0x7c, 0x20, 0x4a, 0xcc, 0x62, 0x2c,
	0x31, 0xf8, 0x0c, 0x96, 0x95, 0xb7, 0x45, 0x58, 0xe9, 0xf4, 0xaa, 0x55, 0xbd, 0xd3, 0xd1, 0x16,
	0xd0, 0x16, 0x68, 0xf5, 0xe6, 0x55, 0xa5, 0x51, 0x3f, 0x37, 0x2a, 0xed, 0xb6, 0x51, 0x6f, 0xd6,
	0x5a, 0x5a, 0x06, 0x6d, 0xc3, 0x46, 0x28, 0xad, 0x36, 0xea, 0x7a, 0xb3, 0x6b, 0xd4, 0xdb, 0xda,
	0x22, 0xfe, 0x14, 0xb6, 0x9e, 0x53, 0x2e, 0x1d, 0x0e, 0xc8, 0xdb, 0xd8, 0xe1, 0x73, 0x8b, 0x11,
	0x7e, 0x02, 0xdb, 0x29, 0x68, 0xe6, 0x09, 0x78, 0x5f, 0x4c, 0x24, 0x3c, 0x4b, 0x82, 0x09, 0x3e,
	0x81, 0x9d, 0xea, 0x90, 0x9a, 0xfe, 0x7d, 0x8f, 0xdf, 0x83, 0xdd, 0x54, 0x3c, 0xf3, 0x1e, 0xff,
	0x66, 0x09, 0x56, 0x74, 0x42, 0x8c, 0xce, 0x65, 0x07, 0xad, 0x03, 0xa8, 0xa1, 0xd1, 0x7a, 0xa1,
	0x2d, 0xa0, 0x27, 0x70, 0x14, 0xce, 0xaf, 0x74, 0x52, 0xab, 0xbf, 0xac, 0xb6, 0xce, 0x75, 0x43,
	0xfa, 0x5a, 0xe9, 0xea, 0x46, 0xad, 0x52, 0x6f, 0x68, 0xff, 0xfa, 0x9f, 0xfa, 0xcb, 0xa0, 0x47,
	0xb0, 0x13, 0xc2, 0xbb, 0xad, 0x96, 0x71, 0x59, 0x69, 0xbe, 0x34, 0xda, 0x17, 0xad, 0xa6, 0xae,
	0xfd, 0x33, 0x02, 0x61, 0xd8, 0x0e, 0x41, 0x1d, 0xbd, 0x79, 0x2e, 0x07, 0x35, 0xa2, 0xff, 0x50,
	0xfb, 0x47, 0x84, 0xf9, 0x16, 0x6c, 0x4d, 0x0e, 0x7a, 0xd9, 0xd6, 0x0d, 0x15, 0x5f, 0xed, 0xef,
	0x11, 0xe4, 0x23, 0x28, 0x85, 0x90, 0x66, 0xcb, 0xd0, 0x9b, 0xad, 0xde, 0xf3, 0x0b, 0xa3, 0x5d,
	0x21, 0x95, 0xcb, 0x8e, 0xf6, 0xb7, 0x08, 0x76, 0x0a, 0x78, 0x46, 0x5b, 0xf7, 0x82, 0xe8, 0x9d,
	0x8b, 0x8b, 0x56, 0xe3, 0xdc, 0xd0, 0xbf, 0xa8, 0xea, 0xfa, 0xb9, 0xf6, 0xd7, 0x54, 0xf3, 0xc2,
	0xac, 0x06, 0x2e, 0xfc, 0x25, 0xc2, 0x7c, 0x02, 0xe5, 0x69, 0xcc, 0x95, 0x4e, 0xea, 0x35, 0x19,
	0x1e, 0xed, 0xcf, 0x11, 0xf0, 0x43, 0xd8, 0x8d, 0x8c, 0xec, 0x1a, 0x9d, 0x5e, 0xbb, 0xdd, 0x22,
	0x5d, 0xa3, 0xde, 0x6c, 0x6a, 0x7f, 0x8a, 0x50, 0x47, 0xb0, 0x19, 0xa2, 0x88, 0x7e, 0x5e, 0xef,
	0x18, 0x3a, 0x21, 0x2d, 0xa2, 0xfd, 0x31, 0x42, 0x1c, 0xc3, 0x7e, 0x88, 0x68, 0x57, 0x48, 0x47,
	0x37, 0x6a, 0xa4, 0x75, 0x69, 0xb4, 0xcf, 0x14, 0xf2, 0x0f, 0x11, 0xf2, 0x3b, 0xf0, 0x50, 0x20,
	0xc3, 0x4b, 0xfa, 0xc2, 0xe8, 0x91, 0x86, 0x51, 0xad, 0x34, 0x85, 0x7a, 0xa2, 0x57, 0xaa, 0x17,
	0xda, 0xef, 0x23, 0xf0, 0xc7, 0xb0, 0x37, 0x0b, 0x16, 0xc8, 0x4a, 0xfb, 0x85, 0xf6, 0xbb, 0xd4,
	0xbc, 0x86, 0xfe, 0x8a, 0x60, 0xea, 0x44, 0xfb, 0xed, 0x04, 0xf4, 0xf4, 0x3f, 0x05, 0xc8, 0x76,
	0x46, 0x0c, 0xd5, 0x60, 0x2d, 0xf1, 0xb1, 0x82, 0x4a, 0xf1, 0xe2, 0x10, 0xff, 0x10, 0x2b, 0xef,
	0xcd, 0x59, 0x61, 0x1e, 0x5e, 0x40, 0x3f, 0x09, 0xca, 0xd3, 0x34, 0xbd, 0x47, 0x87, 0xf1, 0x4d,
	0x29, 0x5f, 0x2b, 0xe5, 0xa3, 0xbb, 0x01, 0xf2, 0xf0, 0x1e, 0xa0, 0x59, 0xba, 0x8d, 0x0e, 0xc2,
	0x9d, 0xa9, 0x84, 0xbf, 0xfc, 0xf0, 0xae, 0x65, 0x79, 0xec, 0xf7, 0x61, 0x45, 0xb5, 0x32, 0x84,
	0xe2, 0x56, 0x28, 0x7f, 0x37, 0x67, 0x64, 0x72, 0xd7, 0x17, 0x41, 0xa3, 0x99, 0x6a, 0x80, 0xe8,
	0xe1, 0x14, 0x7a, 0xaa, 0x85, 0x97, 0x0f, 0xef, 0x5c, 0x0f, 0x4f, 0x4e, 0xe1, 0xaf, 0xd1, 0xc9,
	0xe9, 0x44, 0x38, 0x3a, 0x79, 0x0e, 0xf9, 0xc5, 0x0b, 0xe8, 0x19, 0x14, 0x63, 0x94, 0x11, 0xed,
	0x84, 0x3b, 0x92, 0x64, 0xb7, 0xbc, 0x9b, 0x2a, 0x97, 0x27, 0xd4, 0x60, 0x2d, 0xf1, 0x31, 0x11,
	0xdd, 0x93, 0xe9, 0xcf, 0x9a, 0xe8, 0x9e, 0xcc, 0x7e, 0x7d, 0x48, 0x4b, 0x62, 0x2c, 0x31, 0xb2,
	0x24, 0xc9, 0x50, 0x23, 0x4b, 0xa6, 0x28, 0x25, 0x5e, 0x40, 0x8d, 0x04, 0xcb, 0x16, 0xf4, 0x17,
	0x95, 0x53, 0xec, 0x56, 0xcc, 0xba, 0xbc, 0x3f, 0x77, 0x2d, 0x9e, 0xcd, 0x29, 0x3e, 0x96, 0xcc,
	0xe6, 0x2c, 0x47, 0x4d, 0x66, 0x33, 0x8d, 0xcc, 0xc9, 0x17, 0x91, 0xc6, 0x56, 0xa2, 0x17, 0x31,
	0x87, 0x2b, 0x45, 0x2f, 0x62, 0x2e, 0xd9, 0x59, 0x40, 0xad, 0xe0, 0x87, 0x92, 0x38, 0x1b, 0x40,
	0xfb, 0xa9, 0x6d, 0x5d, 0x25, 0xe5, 0x83, 0xf9, 0x8b, 0xe1, 0x5b, 0x50, 0x1d, 0x38, 0x7a, 0x0b,
	0x51, 0xd3, 0x8f, 0xde, 0x42, 0xac, 0x4d, 0xe3, 0x05, 0x44, 0x60, 0x63, 0xa6, 0x0f, 0xa2, 0x89,
	0xaa, 0xb4, 0x86, 0x5a, 0x3e, 0xb8, 0x63, 0x75, 0xf2, 0x0a, 0x66, 0x9b, 0x5f, 0xec, 0x15, 0xa4,
	0x76, 0xd2, 0xd8, 0x2b, 0x48, 0xef, 0x9c, 0x78, 0xe1, 0xec, 0xf1, 0x8f, 0x8f, 0x07, 0xee, 0xd0,
	0x74, 0x06, 0x27, 0x9f, 0x3d, 0xe5, 0xfc, 0xa4, 0xef, 0x8e, 0x4e, 0xe5, 0xaf, 0x8c, 0x7d, 0x77,
	0x78, 0xca, 0xa8, 0x7f, 0x63, 0xf7, 0x69, 0xf8, 0xf3, 0xe3, 0xab, 0x65, 0xb9, 0xf2, 0xbd, 0xff,
	0x07, 0x00, 0x00, 0xff, 0xff, 0x7a, 0xc4, 0xc0, 0xf8, 0xa2, 0x14, 0x00, 0x00,
}
