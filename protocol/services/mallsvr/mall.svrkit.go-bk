package Mall

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: services/mall/mall.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for Mall service
const MallMagic = uint16(15190)

// Client API for Mall service

type MallClientInterface interface {
	GetCategories(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*CategoryList, error)
	CreateProduct(ctx context.Context, uin uint32, in *CreateProductReq, opts ...svrkit.CallOption) (*CreateProductResp, error)
	UpdateProduct(ctx context.Context, uin uint32, in *UpdateProductReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DeleteProducts(ctx context.Context, uin uint32, in *ProductIdList, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetProductsByPage(ctx context.Context, uin uint32, in *GetProductReg, opts ...svrkit.CallOption) (*ProductList, error)
	GetProductsByCategory(ctx context.Context, uin uint32, in *GetProductsByCategoryReq, opts ...svrkit.CallOption) (*ProductList, error)
	AddProductItem_AccountPassword(ctx context.Context, uin uint32, in *AddProductItemReq_AccountPassword, opts ...svrkit.CallOption) (*AddProductItemResp_AccountPassword, error)
	GetProductsRemain(ctx context.Context, uin uint32, in *GetProductsRemainReq, opts ...svrkit.CallOption) (*GetProductsRemainResp, error)
	GetProductsByIdList(ctx context.Context, uin uint32, in *ProductIdList, opts ...svrkit.CallOption) (*ProductList, error)
	GetProductRemainsByCategory(ctx context.Context, uin uint32, in *GetProductRemainsByCategoryReq, opts ...svrkit.CallOption) (*GetProductRemainsByCategoryResp, error)
	GetProductItemList(ctx context.Context, uin uint32, in *GetProductItemReq, opts ...svrkit.CallOption) (*GetProductItemResp, error)
	DeleteProductItems(ctx context.Context, uin uint32, in *DeleteProductItemReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	AddProductItems(ctx context.Context, uin uint32, in *AddProductItemsReq, opts ...svrkit.CallOption) (*AddProductItemsResp, error)
	GetProductSeedItem(ctx context.Context, uin uint32, in *GetProductItemReq, opts ...svrkit.CallOption) (*ProductItem, error)
	GetProductTotalPurchaseTimes(ctx context.Context, uin uint32, in *GetProductTotalPurchaseTimesReq, opts ...svrkit.CallOption) (*GetProductTotalPurchaseTimesResp, error)
	// 100 - 199, Activity related
	CreateActivity(ctx context.Context, uin uint32, in *CreateActivityReq, opts ...svrkit.CallOption) (*CreateActivityResp, error)
	UpdateActivity(ctx context.Context, uin uint32, in *UpdateActivityReq, opts ...svrkit.CallOption) (*UpdateActivityResp, error)
	DeleteActivity(ctx context.Context, uin uint32, in *DeleteActivityReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetActivityList(ctx context.Context, uin uint32, in *GetActivityListReq, opts ...svrkit.CallOption) (*GetActivityListResp, error)
	// Deprecated, use GetCurrentActivityDetailByCategory instead
	GetCurrentActivityDetail(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*CurrentActivityDetail, error)
	PurchaseActivityProductItem(ctx context.Context, uin uint32, in *PurchaseActivityProductItemReq, opts ...svrkit.CallOption) (*PurchaseActivityProductItemResp, error)
	SetCurrentActivity(ctx context.Context, uin uint32, in *SetCurrentActivityReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// Deprecated
	GetCurrentActivity(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*Activity, error)
	CloseActivity(ctx context.Context, uin uint32, in *CloseActivityReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetCurrentActivityDetailByCategory(ctx context.Context, uin uint32, in *ActivityCategoryReq, opts ...svrkit.CallOption) (*CurrentActivityDetail, error)
	// 201 - 299, User's storage related
	GetUserProducts(ctx context.Context, uin uint32, in *GetUserProductsReq, opts ...svrkit.CallOption) (*GetUserProductsResp, error)
	FeedbackDiamond(ctx context.Context, uin uint32, in *FeedbackDiamondReq, opts ...svrkit.CallOption) (*FeedbackDiamondResp, error)
	GetUserProductItemByOrder(ctx context.Context, uin uint32, in *GetUserProductItemByOrderReq, opts ...svrkit.CallOption) (*UserProductItem, error)
	GetActivityWinningDetail(ctx context.Context, uin uint32, in *GetActivityWinningReq, opts ...svrkit.CallOption) (*GetActivityWinningDetailResp, error)
	GetActivityWinningCount(ctx context.Context, uin uint32, in *GetActivityWinningReq, opts ...svrkit.CallOption) (*GetActivityWinningCountResp, error)
}

type MallClient struct {
	cc *svrkit.ClientConn
}

func NewMallClient(cc *svrkit.ClientConn) MallClientInterface {
	return &MallClient{cc}
}

const (
	commandMallGetSelfSvnInfo                     = 9995
	commandMallEcho                               = 9999
	commandMallGetCategories                      = 1
	commandMallCreateProduct                      = 2
	commandMallUpdateProduct                      = 3
	commandMallDeleteProducts                     = 4
	commandMallGetProductsByPage                  = 5
	commandMallGetProductsByCategory              = 6
	commandMallAddProductItem_AccountPassword     = 7
	commandMallGetProductsRemain                  = 8
	commandMallGetProductsByIdList                = 9
	commandMallGetProductRemainsByCategory        = 10
	commandMallGetProductItemList                 = 11
	commandMallDeleteProductItems                 = 12
	commandMallAddProductItems                    = 13
	commandMallGetProductSeedItem                 = 14
	commandMallGetProductTotalPurchaseTimes       = 15
	commandMallCreateActivity                     = 100
	commandMallUpdateActivity                     = 101
	commandMallDeleteActivity                     = 102
	commandMallGetActivityList                    = 103
	commandMallGetCurrentActivityDetail           = 104
	commandMallPurchaseActivityProductItem        = 105
	commandMallSetCurrentActivity                 = 106
	commandMallGetCurrentActivity                 = 107
	commandMallCloseActivity                      = 108
	commandMallGetCurrentActivityDetailByCategory = 109
	commandMallGetUserProducts                    = 201
	commandMallFeedbackDiamond                    = 202
	commandMallGetUserProductItemByOrder          = 203
	commandMallGetActivityWinningDetail           = 204
	commandMallGetActivityWinningCount            = 205
)

func (c *MallClient) GetCategories(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*CategoryList, error) {
	out := new(CategoryList)
	err := c.cc.Invoke(ctx, uin, commandMallGetCategories, "/Mall.Mall/GetCategories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) CreateProduct(ctx context.Context, uin uint32, in *CreateProductReq, opts ...svrkit.CallOption) (*CreateProductResp, error) {
	out := new(CreateProductResp)
	err := c.cc.Invoke(ctx, uin, commandMallCreateProduct, "/Mall.Mall/CreateProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) UpdateProduct(ctx context.Context, uin uint32, in *UpdateProductReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandMallUpdateProduct, "/Mall.Mall/UpdateProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) DeleteProducts(ctx context.Context, uin uint32, in *ProductIdList, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandMallDeleteProducts, "/Mall.Mall/DeleteProducts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) GetProductsByPage(ctx context.Context, uin uint32, in *GetProductReg, opts ...svrkit.CallOption) (*ProductList, error) {
	out := new(ProductList)
	err := c.cc.Invoke(ctx, uin, commandMallGetProductsByPage, "/Mall.Mall/GetProductsByPage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) GetProductsByCategory(ctx context.Context, uin uint32, in *GetProductsByCategoryReq, opts ...svrkit.CallOption) (*ProductList, error) {
	out := new(ProductList)
	err := c.cc.Invoke(ctx, uin, commandMallGetProductsByCategory, "/Mall.Mall/GetProductsByCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) AddProductItem_AccountPassword(ctx context.Context, uin uint32, in *AddProductItemReq_AccountPassword, opts ...svrkit.CallOption) (*AddProductItemResp_AccountPassword, error) {
	out := new(AddProductItemResp_AccountPassword)
	err := c.cc.Invoke(ctx, uin, commandMallAddProductItem_AccountPassword, "/Mall.Mall/AddProductItem_AccountPassword", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) GetProductsRemain(ctx context.Context, uin uint32, in *GetProductsRemainReq, opts ...svrkit.CallOption) (*GetProductsRemainResp, error) {
	out := new(GetProductsRemainResp)
	err := c.cc.Invoke(ctx, uin, commandMallGetProductsRemain, "/Mall.Mall/GetProductsRemain", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) GetProductsByIdList(ctx context.Context, uin uint32, in *ProductIdList, opts ...svrkit.CallOption) (*ProductList, error) {
	out := new(ProductList)
	err := c.cc.Invoke(ctx, uin, commandMallGetProductsByIdList, "/Mall.Mall/GetProductsByIdList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) GetProductRemainsByCategory(ctx context.Context, uin uint32, in *GetProductRemainsByCategoryReq, opts ...svrkit.CallOption) (*GetProductRemainsByCategoryResp, error) {
	out := new(GetProductRemainsByCategoryResp)
	err := c.cc.Invoke(ctx, uin, commandMallGetProductRemainsByCategory, "/Mall.Mall/GetProductRemainsByCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) GetProductItemList(ctx context.Context, uin uint32, in *GetProductItemReq, opts ...svrkit.CallOption) (*GetProductItemResp, error) {
	out := new(GetProductItemResp)
	err := c.cc.Invoke(ctx, uin, commandMallGetProductItemList, "/Mall.Mall/GetProductItemList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) DeleteProductItems(ctx context.Context, uin uint32, in *DeleteProductItemReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandMallDeleteProductItems, "/Mall.Mall/DeleteProductItems", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) AddProductItems(ctx context.Context, uin uint32, in *AddProductItemsReq, opts ...svrkit.CallOption) (*AddProductItemsResp, error) {
	out := new(AddProductItemsResp)
	err := c.cc.Invoke(ctx, uin, commandMallAddProductItems, "/Mall.Mall/AddProductItems", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) GetProductSeedItem(ctx context.Context, uin uint32, in *GetProductItemReq, opts ...svrkit.CallOption) (*ProductItem, error) {
	out := new(ProductItem)
	err := c.cc.Invoke(ctx, uin, commandMallGetProductSeedItem, "/Mall.Mall/GetProductSeedItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) GetProductTotalPurchaseTimes(ctx context.Context, uin uint32, in *GetProductTotalPurchaseTimesReq, opts ...svrkit.CallOption) (*GetProductTotalPurchaseTimesResp, error) {
	out := new(GetProductTotalPurchaseTimesResp)
	err := c.cc.Invoke(ctx, uin, commandMallGetProductTotalPurchaseTimes, "/Mall.Mall/GetProductTotalPurchaseTimes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) CreateActivity(ctx context.Context, uin uint32, in *CreateActivityReq, opts ...svrkit.CallOption) (*CreateActivityResp, error) {
	out := new(CreateActivityResp)
	err := c.cc.Invoke(ctx, uin, commandMallCreateActivity, "/Mall.Mall/CreateActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) UpdateActivity(ctx context.Context, uin uint32, in *UpdateActivityReq, opts ...svrkit.CallOption) (*UpdateActivityResp, error) {
	out := new(UpdateActivityResp)
	err := c.cc.Invoke(ctx, uin, commandMallUpdateActivity, "/Mall.Mall/UpdateActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) DeleteActivity(ctx context.Context, uin uint32, in *DeleteActivityReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandMallDeleteActivity, "/Mall.Mall/DeleteActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) GetActivityList(ctx context.Context, uin uint32, in *GetActivityListReq, opts ...svrkit.CallOption) (*GetActivityListResp, error) {
	out := new(GetActivityListResp)
	err := c.cc.Invoke(ctx, uin, commandMallGetActivityList, "/Mall.Mall/GetActivityList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) GetCurrentActivityDetail(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*CurrentActivityDetail, error) {
	out := new(CurrentActivityDetail)
	err := c.cc.Invoke(ctx, uin, commandMallGetCurrentActivityDetail, "/Mall.Mall/GetCurrentActivityDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) PurchaseActivityProductItem(ctx context.Context, uin uint32, in *PurchaseActivityProductItemReq, opts ...svrkit.CallOption) (*PurchaseActivityProductItemResp, error) {
	out := new(PurchaseActivityProductItemResp)
	err := c.cc.Invoke(ctx, uin, commandMallPurchaseActivityProductItem, "/Mall.Mall/PurchaseActivityProductItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) SetCurrentActivity(ctx context.Context, uin uint32, in *SetCurrentActivityReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandMallSetCurrentActivity, "/Mall.Mall/SetCurrentActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) GetCurrentActivity(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*Activity, error) {
	out := new(Activity)
	err := c.cc.Invoke(ctx, uin, commandMallGetCurrentActivity, "/Mall.Mall/GetCurrentActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) CloseActivity(ctx context.Context, uin uint32, in *CloseActivityReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandMallCloseActivity, "/Mall.Mall/CloseActivity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) GetCurrentActivityDetailByCategory(ctx context.Context, uin uint32, in *ActivityCategoryReq, opts ...svrkit.CallOption) (*CurrentActivityDetail, error) {
	out := new(CurrentActivityDetail)
	err := c.cc.Invoke(ctx, uin, commandMallGetCurrentActivityDetailByCategory, "/Mall.Mall/GetCurrentActivityDetailByCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) GetUserProducts(ctx context.Context, uin uint32, in *GetUserProductsReq, opts ...svrkit.CallOption) (*GetUserProductsResp, error) {
	out := new(GetUserProductsResp)
	err := c.cc.Invoke(ctx, uin, commandMallGetUserProducts, "/Mall.Mall/GetUserProducts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) FeedbackDiamond(ctx context.Context, uin uint32, in *FeedbackDiamondReq, opts ...svrkit.CallOption) (*FeedbackDiamondResp, error) {
	out := new(FeedbackDiamondResp)
	err := c.cc.Invoke(ctx, uin, commandMallFeedbackDiamond, "/Mall.Mall/FeedbackDiamond", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) GetUserProductItemByOrder(ctx context.Context, uin uint32, in *GetUserProductItemByOrderReq, opts ...svrkit.CallOption) (*UserProductItem, error) {
	out := new(UserProductItem)
	err := c.cc.Invoke(ctx, uin, commandMallGetUserProductItemByOrder, "/Mall.Mall/GetUserProductItemByOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) GetActivityWinningDetail(ctx context.Context, uin uint32, in *GetActivityWinningReq, opts ...svrkit.CallOption) (*GetActivityWinningDetailResp, error) {
	out := new(GetActivityWinningDetailResp)
	err := c.cc.Invoke(ctx, uin, commandMallGetActivityWinningDetail, "/Mall.Mall/GetActivityWinningDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MallClient) GetActivityWinningCount(ctx context.Context, uin uint32, in *GetActivityWinningReq, opts ...svrkit.CallOption) (*GetActivityWinningCountResp, error) {
	out := new(GetActivityWinningCountResp)
	err := c.cc.Invoke(ctx, uin, commandMallGetActivityWinningCount, "/Mall.Mall/GetActivityWinningCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}
