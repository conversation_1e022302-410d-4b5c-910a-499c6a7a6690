// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/interaction/fellow-svr.proto

package fellow_svr // import "golang.52tt.com/protocol/services/fellow-svr"

/*
buf:lint:ignore DIRECTORY_SAME_PACKAGE
*/

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
import cb "golang.52tt.com/protocol/services/unified_pay/cb"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type FellowType int32

const (
	FellowType_ENUM_FELLOW_TYPE_UNKNOWN  FellowType = 0
	FellowType_ENUM_FELLOW_TYPE_BRO      FellowType = 1
	FellowType_ENUM_FELLOW_TYPE_LADYBRO  FellowType = 2
	FellowType_ENUM_FELLOW_TYPE_INTIMATE FellowType = 3
	FellowType_ENUM_FELLOW_TYPE_PARTNER  FellowType = 4
)

var FellowType_name = map[int32]string{
	0: "ENUM_FELLOW_TYPE_UNKNOWN",
	1: "ENUM_FELLOW_TYPE_BRO",
	2: "ENUM_FELLOW_TYPE_LADYBRO",
	3: "ENUM_FELLOW_TYPE_INTIMATE",
	4: "ENUM_FELLOW_TYPE_PARTNER",
}
var FellowType_value = map[string]int32{
	"ENUM_FELLOW_TYPE_UNKNOWN":  0,
	"ENUM_FELLOW_TYPE_BRO":      1,
	"ENUM_FELLOW_TYPE_LADYBRO":  2,
	"ENUM_FELLOW_TYPE_INTIMATE": 3,
	"ENUM_FELLOW_TYPE_PARTNER":  4,
}

func (x FellowType) String() string {
	return proto.EnumName(FellowType_name, int32(x))
}
func (FellowType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{0}
}

type FellowBindType int32

const (
	FellowBindType_ENUM_FELLOW_BIND_TYPE_UNKNOWN FellowBindType = 0
	FellowBindType_ENUM_FELLOW_BIND_TYPE_UNIQUE  FellowBindType = 1
	FellowBindType_ENUM_FELLOW_BIND_TYPE_MULTI   FellowBindType = 2
)

var FellowBindType_name = map[int32]string{
	0: "ENUM_FELLOW_BIND_TYPE_UNKNOWN",
	1: "ENUM_FELLOW_BIND_TYPE_UNIQUE",
	2: "ENUM_FELLOW_BIND_TYPE_MULTI",
}
var FellowBindType_value = map[string]int32{
	"ENUM_FELLOW_BIND_TYPE_UNKNOWN": 0,
	"ENUM_FELLOW_BIND_TYPE_UNIQUE":  1,
	"ENUM_FELLOW_BIND_TYPE_MULTI":   2,
}

func (x FellowBindType) String() string {
	return proto.EnumName(FellowBindType_name, int32(x))
}
func (FellowBindType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{1}
}

type FellowBindStatus int32

const (
	FellowBindStatus_ENUM_FELLOW_BIND_STATUS_NORMAL FellowBindStatus = 0
	FellowBindStatus_ENUM_FELLOW_BIND_STATUS_APART  FellowBindStatus = 1
)

var FellowBindStatus_name = map[int32]string{
	0: "ENUM_FELLOW_BIND_STATUS_NORMAL",
	1: "ENUM_FELLOW_BIND_STATUS_APART",
}
var FellowBindStatus_value = map[string]int32{
	"ENUM_FELLOW_BIND_STATUS_NORMAL": 0,
	"ENUM_FELLOW_BIND_STATUS_APART":  1,
}

func (x FellowBindStatus) String() string {
	return proto.EnumName(FellowBindStatus_name, int32(x))
}
func (FellowBindStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{2}
}

type BanType int32

const (
	BanType_BAN_TYPE_NORMAL    BanType = 0
	BanType_BAN_TYPE_TMP       BanType = 1
	BanType_BAN_TYPE_PERMANENT BanType = 2
)

var BanType_name = map[int32]string{
	0: "BAN_TYPE_NORMAL",
	1: "BAN_TYPE_TMP",
	2: "BAN_TYPE_PERMANENT",
}
var BanType_value = map[string]int32{
	"BAN_TYPE_NORMAL":    0,
	"BAN_TYPE_TMP":       1,
	"BAN_TYPE_PERMANENT": 2,
}

func (x BanType) String() string {
	return proto.EnumName(BanType_name, int32(x))
}
func (BanType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{3}
}

type InviteStatus int32

const (
	InviteStatus_ENUM_INVITE_STATUS_NO_INVITE InviteStatus = 0
	InviteStatus_ENUM_INVITE_STATUS_INVITED   InviteStatus = 1
	InviteStatus_ENUM_INVITE_STATUS_SUCCESS   InviteStatus = 2
	InviteStatus_ENUM_INVITE_STATUS_FAILED    InviteStatus = 3
	InviteStatus_ENUM_INVITE_STATUS_CANCELED  InviteStatus = 4
	InviteStatus_ENUM_INVITE_STATUS_TIMEOUT   InviteStatus = 5
)

var InviteStatus_name = map[int32]string{
	0: "ENUM_INVITE_STATUS_NO_INVITE",
	1: "ENUM_INVITE_STATUS_INVITED",
	2: "ENUM_INVITE_STATUS_SUCCESS",
	3: "ENUM_INVITE_STATUS_FAILED",
	4: "ENUM_INVITE_STATUS_CANCELED",
	5: "ENUM_INVITE_STATUS_TIMEOUT",
}
var InviteStatus_value = map[string]int32{
	"ENUM_INVITE_STATUS_NO_INVITE": 0,
	"ENUM_INVITE_STATUS_INVITED":   1,
	"ENUM_INVITE_STATUS_SUCCESS":   2,
	"ENUM_INVITE_STATUS_FAILED":    3,
	"ENUM_INVITE_STATUS_CANCELED":  4,
	"ENUM_INVITE_STATUS_TIMEOUT":   5,
}

func (x InviteStatus) String() string {
	return proto.EnumName(InviteStatus_name, int32(x))
}
func (InviteStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{4}
}

type UnboundStatus int32

const (
	UnboundStatus_ENUM_UNBOUND_STATUS_NO_INVITE UnboundStatus = 0
	UnboundStatus_ENUM_UNBOUND_STATUS_PENDING   UnboundStatus = 1
	UnboundStatus_ENUM_UNBOUND_STATUS_FINISHED  UnboundStatus = 2
	UnboundStatus_ENUM_UNBOUND_STATUS_CANCELED  UnboundStatus = 3
)

var UnboundStatus_name = map[int32]string{
	0: "ENUM_UNBOUND_STATUS_NO_INVITE",
	1: "ENUM_UNBOUND_STATUS_PENDING",
	2: "ENUM_UNBOUND_STATUS_FINISHED",
	3: "ENUM_UNBOUND_STATUS_CANCELED",
}
var UnboundStatus_value = map[string]int32{
	"ENUM_UNBOUND_STATUS_NO_INVITE": 0,
	"ENUM_UNBOUND_STATUS_PENDING":   1,
	"ENUM_UNBOUND_STATUS_FINISHED":  2,
	"ENUM_UNBOUND_STATUS_CANCELED":  3,
}

func (x UnboundStatus) String() string {
	return proto.EnumName(UnboundStatus_name, int32(x))
}
func (UnboundStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{5}
}

// 资源类型
type ResourceType int32

const (
	ResourceType_RESOURCE_TYPE_UNKNOWN ResourceType = 0
	ResourceType_RESOURCE_TYPE_IMG     ResourceType = 1
	ResourceType_RESOURCE_TYPE_VIP     ResourceType = 2
	ResourceType_RESOURCE_TYPE_ZIP     ResourceType = 3
)

var ResourceType_name = map[int32]string{
	0: "RESOURCE_TYPE_UNKNOWN",
	1: "RESOURCE_TYPE_IMG",
	2: "RESOURCE_TYPE_VIP",
	3: "RESOURCE_TYPE_ZIP",
}
var ResourceType_value = map[string]int32{
	"RESOURCE_TYPE_UNKNOWN": 0,
	"RESOURCE_TYPE_IMG":     1,
	"RESOURCE_TYPE_VIP":     2,
	"RESOURCE_TYPE_ZIP":     3,
}

func (x ResourceType) String() string {
	return proto.EnumName(ResourceType_name, int32(x))
}
func (ResourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{6}
}

// 关系类型
type RelationType int32

const (
	RelationType_RELATION_TYPE_UNKNOWN RelationType = 0
	RelationType_RELATION_TYPE_RARE    RelationType = 1
)

var RelationType_name = map[int32]string{
	0: "RELATION_TYPE_UNKNOWN",
	1: "RELATION_TYPE_RARE",
}
var RelationType_value = map[string]int32{
	"RELATION_TYPE_UNKNOWN": 0,
	"RELATION_TYPE_RARE":    1,
}

func (x RelationType) String() string {
	return proto.EnumName(RelationType_name, int32(x))
}
func (RelationType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{7}
}

// 铭牌信息
type PlateUrl struct {
	CpUnique             string   `protobuf:"bytes,1,opt,name=cp_unique,json=cpUnique,proto3" json:"cp_unique,omitempty"`
	DateUnique           string   `protobuf:"bytes,2,opt,name=date_unique,json=dateUnique,proto3" json:"date_unique,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlateUrl) Reset()         { *m = PlateUrl{} }
func (m *PlateUrl) String() string { return proto.CompactTextString(m) }
func (*PlateUrl) ProtoMessage()    {}
func (*PlateUrl) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{0}
}
func (m *PlateUrl) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlateUrl.Unmarshal(m, b)
}
func (m *PlateUrl) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlateUrl.Marshal(b, m, deterministic)
}
func (dst *PlateUrl) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlateUrl.Merge(dst, src)
}
func (m *PlateUrl) XXX_Size() int {
	return xxx_messageInfo_PlateUrl.Size(m)
}
func (m *PlateUrl) XXX_DiscardUnknown() {
	xxx_messageInfo_PlateUrl.DiscardUnknown(m)
}

var xxx_messageInfo_PlateUrl proto.InternalMessageInfo

func (m *PlateUrl) GetCpUnique() string {
	if m != nil {
		return m.CpUnique
	}
	return ""
}

func (m *PlateUrl) GetDateUnique() string {
	if m != nil {
		return m.DateUnique
	}
	return ""
}

type FellowBackground struct {
	BackgroundUrl        string   `protobuf:"bytes,1,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	SourceType           uint32   `protobuf:"varint,2,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	Md5                  string   `protobuf:"bytes,3,opt,name=md5,proto3" json:"md5,omitempty"`
	BackgroundImg        string   `protobuf:"bytes,4,opt,name=background_img,json=backgroundImg,proto3" json:"background_img,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowBackground) Reset()         { *m = FellowBackground{} }
func (m *FellowBackground) String() string { return proto.CompactTextString(m) }
func (*FellowBackground) ProtoMessage()    {}
func (*FellowBackground) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{1}
}
func (m *FellowBackground) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowBackground.Unmarshal(m, b)
}
func (m *FellowBackground) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowBackground.Marshal(b, m, deterministic)
}
func (dst *FellowBackground) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowBackground.Merge(dst, src)
}
func (m *FellowBackground) XXX_Size() int {
	return xxx_messageInfo_FellowBackground.Size(m)
}
func (m *FellowBackground) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowBackground.DiscardUnknown(m)
}

var xxx_messageInfo_FellowBackground proto.InternalMessageInfo

func (m *FellowBackground) GetBackgroundUrl() string {
	if m != nil {
		return m.BackgroundUrl
	}
	return ""
}

func (m *FellowBackground) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *FellowBackground) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *FellowBackground) GetBackgroundImg() string {
	if m != nil {
		return m.BackgroundImg
	}
	return ""
}

type RareInfo struct {
	RareId               uint32   `protobuf:"varint,1,opt,name=rare_id,json=rareId,proto3" json:"rare_id,omitempty"`
	SubRareId            uint32   `protobuf:"varint,2,opt,name=sub_rare_id,json=subRareId,proto3" json:"sub_rare_id,omitempty"`
	DayCount             uint32   `protobuf:"varint,3,opt,name=day_count,json=dayCount,proto3" json:"day_count,omitempty"`
	RemainCount          uint32   `protobuf:"varint,4,opt,name=remain_count,json=remainCount,proto3" json:"remain_count,omitempty"`
	BindStatus           bool     `protobuf:"varint,5,opt,name=bind_status,json=bindStatus,proto3" json:"bind_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RareInfo) Reset()         { *m = RareInfo{} }
func (m *RareInfo) String() string { return proto.CompactTextString(m) }
func (*RareInfo) ProtoMessage()    {}
func (*RareInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{2}
}
func (m *RareInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RareInfo.Unmarshal(m, b)
}
func (m *RareInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RareInfo.Marshal(b, m, deterministic)
}
func (dst *RareInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RareInfo.Merge(dst, src)
}
func (m *RareInfo) XXX_Size() int {
	return xxx_messageInfo_RareInfo.Size(m)
}
func (m *RareInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RareInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RareInfo proto.InternalMessageInfo

func (m *RareInfo) GetRareId() uint32 {
	if m != nil {
		return m.RareId
	}
	return 0
}

func (m *RareInfo) GetSubRareId() uint32 {
	if m != nil {
		return m.SubRareId
	}
	return 0
}

func (m *RareInfo) GetDayCount() uint32 {
	if m != nil {
		return m.DayCount
	}
	return 0
}

func (m *RareInfo) GetRemainCount() uint32 {
	if m != nil {
		return m.RemainCount
	}
	return 0
}

func (m *RareInfo) GetBindStatus() bool {
	if m != nil {
		return m.BindStatus
	}
	return false
}

// 稀缺信息
type RareTabInfo struct {
	ToUid                uint32            `protobuf:"varint,1,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ToAccount            string            `protobuf:"bytes,2,opt,name=to_account,json=toAccount,proto3" json:"to_account,omitempty"`
	ToNickName           string            `protobuf:"bytes,3,opt,name=to_nick_name,json=toNickName,proto3" json:"to_nick_name,omitempty"`
	BindType             uint32            `protobuf:"varint,4,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	FellowType           uint32            `protobuf:"varint,5,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	FellowName           string            `protobuf:"bytes,6,opt,name=fellow_name,json=fellowName,proto3" json:"fellow_name,omitempty"`
	OutRare              *RareInfo         `protobuf:"bytes,7,opt,name=out_rare,json=outRare,proto3" json:"out_rare,omitempty"`
	PresentBg            *FellowBackground `protobuf:"bytes,8,opt,name=present_bg,json=presentBg,proto3" json:"present_bg,omitempty"`
	RareCount            uint32            `protobuf:"varint,9,opt,name=rare_count,json=rareCount,proto3" json:"rare_count,omitempty"`
	ToSex                uint32            `protobuf:"varint,10,opt,name=to_sex,json=toSex,proto3" json:"to_sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *RareTabInfo) Reset()         { *m = RareTabInfo{} }
func (m *RareTabInfo) String() string { return proto.CompactTextString(m) }
func (*RareTabInfo) ProtoMessage()    {}
func (*RareTabInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{3}
}
func (m *RareTabInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RareTabInfo.Unmarshal(m, b)
}
func (m *RareTabInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RareTabInfo.Marshal(b, m, deterministic)
}
func (dst *RareTabInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RareTabInfo.Merge(dst, src)
}
func (m *RareTabInfo) XXX_Size() int {
	return xxx_messageInfo_RareTabInfo.Size(m)
}
func (m *RareTabInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RareTabInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RareTabInfo proto.InternalMessageInfo

func (m *RareTabInfo) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *RareTabInfo) GetToAccount() string {
	if m != nil {
		return m.ToAccount
	}
	return ""
}

func (m *RareTabInfo) GetToNickName() string {
	if m != nil {
		return m.ToNickName
	}
	return ""
}

func (m *RareTabInfo) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *RareTabInfo) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *RareTabInfo) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

func (m *RareTabInfo) GetOutRare() *RareInfo {
	if m != nil {
		return m.OutRare
	}
	return nil
}

func (m *RareTabInfo) GetPresentBg() *FellowBackground {
	if m != nil {
		return m.PresentBg
	}
	return nil
}

func (m *RareTabInfo) GetRareCount() uint32 {
	if m != nil {
		return m.RareCount
	}
	return 0
}

func (m *RareTabInfo) GetToSex() uint32 {
	if m != nil {
		return m.ToSex
	}
	return 0
}

// 挚友信息
type FellowInfo struct {
	Uid                  int64             `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string            `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	NickName             string            `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	FellowLevel          uint32            `protobuf:"varint,4,opt,name=fellow_level,json=fellowLevel,proto3" json:"fellow_level,omitempty"`
	FellowType           uint32            `protobuf:"varint,5,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	DayCnt               uint32            `protobuf:"varint,6,opt,name=day_cnt,json=dayCnt,proto3" json:"day_cnt,omitempty"`
	FellowName           string            `protobuf:"bytes,7,opt,name=fellow_name,json=fellowName,proto3" json:"fellow_name,omitempty"`
	BindType             uint32            `protobuf:"varint,8,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	FellowPoint          uint32            `protobuf:"varint,9,opt,name=fellow_point,json=fellowPoint,proto3" json:"fellow_point,omitempty"`
	Background           *FellowBackground `protobuf:"bytes,10,opt,name=background,proto3" json:"background,omitempty"`
	CpPresentIcon        string            `protobuf:"bytes,17,opt,name=cp_present_icon,json=cpPresentIcon,proto3" json:"cp_present_icon,omitempty"`
	PlateUrl             *PlateUrl         `protobuf:"bytes,18,opt,name=plate_url,json=plateUrl,proto3" json:"plate_url,omitempty"`
	BindStatus           uint32            `protobuf:"varint,19,opt,name=bind_status,json=bindStatus,proto3" json:"bind_status,omitempty"`
	BanType              BanType           `protobuf:"varint,20,opt,name=ban_type,json=banType,proto3,enum=fellow_svr.BanType" json:"ban_type,omitempty"`
	BindRare             *RareInfo         `protobuf:"bytes,21,opt,name=bind_rare,json=bindRare,proto3" json:"bind_rare,omitempty"`
	FellowIcon           string            `protobuf:"bytes,22,opt,name=fellow_icon,json=fellowIcon,proto3" json:"fellow_icon,omitempty"`
	CardColour           string            `protobuf:"bytes,23,opt,name=card_colour,json=cardColour,proto3" json:"card_colour,omitempty"`
	RoomMsgColour        []string          `protobuf:"bytes,24,rep,name=room_msg_colour,json=roomMsgColour,proto3" json:"room_msg_colour,omitempty"`
	LigatureUrl          string            `protobuf:"bytes,25,opt,name=ligature_url,json=ligatureUrl,proto3" json:"ligature_url,omitempty"`
	GradingInfo          *GradingInfo      `protobuf:"bytes,26,opt,name=grading_info,json=gradingInfo,proto3" json:"grading_info,omitempty"`
	Sex                  uint32            `protobuf:"varint,27,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *FellowInfo) Reset()         { *m = FellowInfo{} }
func (m *FellowInfo) String() string { return proto.CompactTextString(m) }
func (*FellowInfo) ProtoMessage()    {}
func (*FellowInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{4}
}
func (m *FellowInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowInfo.Unmarshal(m, b)
}
func (m *FellowInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowInfo.Marshal(b, m, deterministic)
}
func (dst *FellowInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowInfo.Merge(dst, src)
}
func (m *FellowInfo) XXX_Size() int {
	return xxx_messageInfo_FellowInfo.Size(m)
}
func (m *FellowInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FellowInfo proto.InternalMessageInfo

func (m *FellowInfo) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FellowInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *FellowInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *FellowInfo) GetFellowLevel() uint32 {
	if m != nil {
		return m.FellowLevel
	}
	return 0
}

func (m *FellowInfo) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *FellowInfo) GetDayCnt() uint32 {
	if m != nil {
		return m.DayCnt
	}
	return 0
}

func (m *FellowInfo) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

func (m *FellowInfo) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *FellowInfo) GetFellowPoint() uint32 {
	if m != nil {
		return m.FellowPoint
	}
	return 0
}

func (m *FellowInfo) GetBackground() *FellowBackground {
	if m != nil {
		return m.Background
	}
	return nil
}

func (m *FellowInfo) GetCpPresentIcon() string {
	if m != nil {
		return m.CpPresentIcon
	}
	return ""
}

func (m *FellowInfo) GetPlateUrl() *PlateUrl {
	if m != nil {
		return m.PlateUrl
	}
	return nil
}

func (m *FellowInfo) GetBindStatus() uint32 {
	if m != nil {
		return m.BindStatus
	}
	return 0
}

func (m *FellowInfo) GetBanType() BanType {
	if m != nil {
		return m.BanType
	}
	return BanType_BAN_TYPE_NORMAL
}

func (m *FellowInfo) GetBindRare() *RareInfo {
	if m != nil {
		return m.BindRare
	}
	return nil
}

func (m *FellowInfo) GetFellowIcon() string {
	if m != nil {
		return m.FellowIcon
	}
	return ""
}

func (m *FellowInfo) GetCardColour() string {
	if m != nil {
		return m.CardColour
	}
	return ""
}

func (m *FellowInfo) GetRoomMsgColour() []string {
	if m != nil {
		return m.RoomMsgColour
	}
	return nil
}

func (m *FellowInfo) GetLigatureUrl() string {
	if m != nil {
		return m.LigatureUrl
	}
	return ""
}

func (m *FellowInfo) GetGradingInfo() *GradingInfo {
	if m != nil {
		return m.GradingInfo
	}
	return nil
}

func (m *FellowInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

// 邀请函信息
type FellowTypeInfo struct {
	MultiFellowType      uint32   `protobuf:"varint,1,opt,name=multi_fellow_type,json=multiFellowType,proto3" json:"multi_fellow_type,omitempty"`
	MultiFellowName      string   `protobuf:"bytes,2,opt,name=multi_fellow_name,json=multiFellowName,proto3" json:"multi_fellow_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowTypeInfo) Reset()         { *m = FellowTypeInfo{} }
func (m *FellowTypeInfo) String() string { return proto.CompactTextString(m) }
func (*FellowTypeInfo) ProtoMessage()    {}
func (*FellowTypeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{5}
}
func (m *FellowTypeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowTypeInfo.Unmarshal(m, b)
}
func (m *FellowTypeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowTypeInfo.Marshal(b, m, deterministic)
}
func (dst *FellowTypeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowTypeInfo.Merge(dst, src)
}
func (m *FellowTypeInfo) XXX_Size() int {
	return xxx_messageInfo_FellowTypeInfo.Size(m)
}
func (m *FellowTypeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowTypeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FellowTypeInfo proto.InternalMessageInfo

func (m *FellowTypeInfo) GetMultiFellowType() uint32 {
	if m != nil {
		return m.MultiFellowType
	}
	return 0
}

func (m *FellowTypeInfo) GetMultiFellowName() string {
	if m != nil {
		return m.MultiFellowName
	}
	return ""
}

// 申请挚友页面提供的用户信息
type FellowInviteUser struct {
	Uid                  int64    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	NickName             string   `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	FellowPoint          uint32   `protobuf:"varint,4,opt,name=fellow_point,json=fellowPoint,proto3" json:"fellow_point,omitempty"`
	InviteStatus         uint32   `protobuf:"varint,5,opt,name=invite_status,json=inviteStatus,proto3" json:"invite_status,omitempty"`
	Sex                  uint32   `protobuf:"varint,6,opt,name=sex,proto3" json:"sex,omitempty"`
	InviteId             string   `protobuf:"bytes,7,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowInviteUser) Reset()         { *m = FellowInviteUser{} }
func (m *FellowInviteUser) String() string { return proto.CompactTextString(m) }
func (*FellowInviteUser) ProtoMessage()    {}
func (*FellowInviteUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{6}
}
func (m *FellowInviteUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowInviteUser.Unmarshal(m, b)
}
func (m *FellowInviteUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowInviteUser.Marshal(b, m, deterministic)
}
func (dst *FellowInviteUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowInviteUser.Merge(dst, src)
}
func (m *FellowInviteUser) XXX_Size() int {
	return xxx_messageInfo_FellowInviteUser.Size(m)
}
func (m *FellowInviteUser) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowInviteUser.DiscardUnknown(m)
}

var xxx_messageInfo_FellowInviteUser proto.InternalMessageInfo

func (m *FellowInviteUser) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FellowInviteUser) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *FellowInviteUser) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *FellowInviteUser) GetFellowPoint() uint32 {
	if m != nil {
		return m.FellowPoint
	}
	return 0
}

func (m *FellowInviteUser) GetInviteStatus() uint32 {
	if m != nil {
		return m.InviteStatus
	}
	return 0
}

func (m *FellowInviteUser) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *FellowInviteUser) GetInviteId() string {
	if m != nil {
		return m.InviteId
	}
	return ""
}

// 信物信息
type FellowPresentInfo struct {
	ItemId               uint32            `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Name                 string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Value                uint32            `protobuf:"varint,3,opt,name=value,proto3" json:"value,omitempty"`
	Icon                 string            `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	PriceType            uint32            `protobuf:"varint,5,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	UniqueBackground     *FellowBackground `protobuf:"bytes,6,opt,name=unique_background,json=uniqueBackground,proto3" json:"unique_background,omitempty"`
	MultiBackground      *FellowBackground `protobuf:"bytes,7,opt,name=multi_background,json=multiBackground,proto3" json:"multi_background,omitempty"`
	AlreadyOwn           uint32            `protobuf:"varint,8,opt,name=already_own,json=alreadyOwn,proto3" json:"already_own,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *FellowPresentInfo) Reset()         { *m = FellowPresentInfo{} }
func (m *FellowPresentInfo) String() string { return proto.CompactTextString(m) }
func (*FellowPresentInfo) ProtoMessage()    {}
func (*FellowPresentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{7}
}
func (m *FellowPresentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowPresentInfo.Unmarshal(m, b)
}
func (m *FellowPresentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowPresentInfo.Marshal(b, m, deterministic)
}
func (dst *FellowPresentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowPresentInfo.Merge(dst, src)
}
func (m *FellowPresentInfo) XXX_Size() int {
	return xxx_messageInfo_FellowPresentInfo.Size(m)
}
func (m *FellowPresentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowPresentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FellowPresentInfo proto.InternalMessageInfo

func (m *FellowPresentInfo) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *FellowPresentInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *FellowPresentInfo) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *FellowPresentInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *FellowPresentInfo) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *FellowPresentInfo) GetUniqueBackground() *FellowBackground {
	if m != nil {
		return m.UniqueBackground
	}
	return nil
}

func (m *FellowPresentInfo) GetMultiBackground() *FellowBackground {
	if m != nil {
		return m.MultiBackground
	}
	return nil
}

func (m *FellowPresentInfo) GetAlreadyOwn() uint32 {
	if m != nil {
		return m.AlreadyOwn
	}
	return 0
}

// 邀请函信息
type FellowInviteInfo struct {
	InviteId             string             `protobuf:"bytes,1,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	FromUid              int64              `protobuf:"varint,2,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	FromAccount          string             `protobuf:"bytes,3,opt,name=from_account,json=fromAccount,proto3" json:"from_account,omitempty"`
	FromNickname         string             `protobuf:"bytes,4,opt,name=from_nickname,json=fromNickname,proto3" json:"from_nickname,omitempty"`
	BindType             uint32             `protobuf:"varint,5,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	FellowType           uint32             `protobuf:"varint,6,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	FellowPoint          uint32             `protobuf:"varint,7,opt,name=fellow_point,json=fellowPoint,proto3" json:"fellow_point,omitempty"`
	CreateTime           uint32             `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	FellowName           string             `protobuf:"bytes,9,opt,name=fellow_name,json=fellowName,proto3" json:"fellow_name,omitempty"`
	Status               uint32             `protobuf:"varint,10,opt,name=status,proto3" json:"status,omitempty"`
	ToUid                int64              `protobuf:"varint,11,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ToAccount            string             `protobuf:"bytes,12,opt,name=to_account,json=toAccount,proto3" json:"to_account,omitempty"`
	ToNickname           string             `protobuf:"bytes,13,opt,name=to_nickname,json=toNickname,proto3" json:"to_nickname,omitempty"`
	WithUnlock           bool               `protobuf:"varint,14,opt,name=with_unlock,json=withUnlock,proto3" json:"with_unlock,omitempty"`
	FromSex              uint32             `protobuf:"varint,15,opt,name=from_sex,json=fromSex,proto3" json:"from_sex,omitempty"`
	ToSex                uint32             `protobuf:"varint,16,opt,name=to_sex,json=toSex,proto3" json:"to_sex,omitempty"`
	PresentInfo          *FellowPresentInfo `protobuf:"bytes,17,opt,name=present_info,json=presentInfo,proto3" json:"present_info,omitempty"`
	UnlockPrice          uint32             `protobuf:"varint,18,opt,name=unlock_price,json=unlockPrice,proto3" json:"unlock_price,omitempty"`
	ChannelId            uint32             `protobuf:"varint,19,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	EndTime              uint32             `protobuf:"varint,20,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	FromFellowName       string             `protobuf:"bytes,21,opt,name=from_fellow_name,json=fromFellowName,proto3" json:"from_fellow_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *FellowInviteInfo) Reset()         { *m = FellowInviteInfo{} }
func (m *FellowInviteInfo) String() string { return proto.CompactTextString(m) }
func (*FellowInviteInfo) ProtoMessage()    {}
func (*FellowInviteInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{8}
}
func (m *FellowInviteInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowInviteInfo.Unmarshal(m, b)
}
func (m *FellowInviteInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowInviteInfo.Marshal(b, m, deterministic)
}
func (dst *FellowInviteInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowInviteInfo.Merge(dst, src)
}
func (m *FellowInviteInfo) XXX_Size() int {
	return xxx_messageInfo_FellowInviteInfo.Size(m)
}
func (m *FellowInviteInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowInviteInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FellowInviteInfo proto.InternalMessageInfo

func (m *FellowInviteInfo) GetInviteId() string {
	if m != nil {
		return m.InviteId
	}
	return ""
}

func (m *FellowInviteInfo) GetFromUid() int64 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *FellowInviteInfo) GetFromAccount() string {
	if m != nil {
		return m.FromAccount
	}
	return ""
}

func (m *FellowInviteInfo) GetFromNickname() string {
	if m != nil {
		return m.FromNickname
	}
	return ""
}

func (m *FellowInviteInfo) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *FellowInviteInfo) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *FellowInviteInfo) GetFellowPoint() uint32 {
	if m != nil {
		return m.FellowPoint
	}
	return 0
}

func (m *FellowInviteInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *FellowInviteInfo) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

func (m *FellowInviteInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *FellowInviteInfo) GetToUid() int64 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *FellowInviteInfo) GetToAccount() string {
	if m != nil {
		return m.ToAccount
	}
	return ""
}

func (m *FellowInviteInfo) GetToNickname() string {
	if m != nil {
		return m.ToNickname
	}
	return ""
}

func (m *FellowInviteInfo) GetWithUnlock() bool {
	if m != nil {
		return m.WithUnlock
	}
	return false
}

func (m *FellowInviteInfo) GetFromSex() uint32 {
	if m != nil {
		return m.FromSex
	}
	return 0
}

func (m *FellowInviteInfo) GetToSex() uint32 {
	if m != nil {
		return m.ToSex
	}
	return 0
}

func (m *FellowInviteInfo) GetPresentInfo() *FellowPresentInfo {
	if m != nil {
		return m.PresentInfo
	}
	return nil
}

func (m *FellowInviteInfo) GetUnlockPrice() uint32 {
	if m != nil {
		return m.UnlockPrice
	}
	return 0
}

func (m *FellowInviteInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *FellowInviteInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *FellowInviteInfo) GetFromFellowName() string {
	if m != nil {
		return m.FromFellowName
	}
	return ""
}

type GetFellowListReq struct {
	Uid                  int64    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFellowListReq) Reset()         { *m = GetFellowListReq{} }
func (m *GetFellowListReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowListReq) ProtoMessage()    {}
func (*GetFellowListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{9}
}
func (m *GetFellowListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowListReq.Unmarshal(m, b)
}
func (m *GetFellowListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowListReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowListReq.Merge(dst, src)
}
func (m *GetFellowListReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowListReq.Size(m)
}
func (m *GetFellowListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowListReq proto.InternalMessageInfo

func (m *GetFellowListReq) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetFellowListResp struct {
	Uid                  int64          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UniqueFellow         *FellowInfo    `protobuf:"bytes,2,opt,name=unique_fellow,json=uniqueFellow,proto3" json:"unique_fellow,omitempty"`
	MultiFellow          []*FellowInfo  `protobuf:"bytes,3,rep,name=multi_fellow,json=multiFellow,proto3" json:"multi_fellow,omitempty"`
	FellowPositionCnt    uint32         `protobuf:"varint,4,opt,name=fellow_position_cnt,json=fellowPositionCnt,proto3" json:"fellow_position_cnt,omitempty"`
	PendingInviteCnt     uint32         `protobuf:"varint,5,opt,name=pending_invite_cnt,json=pendingInviteCnt,proto3" json:"pending_invite_cnt,omitempty"`
	UnlockInfo           *UnlockInfo    `protobuf:"bytes,7,opt,name=unlock_info,json=unlockInfo,proto3" json:"unlock_info,omitempty"`
	SendInviteCount      uint32         `protobuf:"varint,8,opt,name=send_invite_count,json=sendInviteCount,proto3" json:"send_invite_count,omitempty"`
	RareTabList          []*RareTabInfo `protobuf:"bytes,9,rep,name=rare_tab_list,json=rareTabList,proto3" json:"rare_tab_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetFellowListResp) Reset()         { *m = GetFellowListResp{} }
func (m *GetFellowListResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowListResp) ProtoMessage()    {}
func (*GetFellowListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{10}
}
func (m *GetFellowListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowListResp.Unmarshal(m, b)
}
func (m *GetFellowListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowListResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowListResp.Merge(dst, src)
}
func (m *GetFellowListResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowListResp.Size(m)
}
func (m *GetFellowListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowListResp proto.InternalMessageInfo

func (m *GetFellowListResp) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFellowListResp) GetUniqueFellow() *FellowInfo {
	if m != nil {
		return m.UniqueFellow
	}
	return nil
}

func (m *GetFellowListResp) GetMultiFellow() []*FellowInfo {
	if m != nil {
		return m.MultiFellow
	}
	return nil
}

func (m *GetFellowListResp) GetFellowPositionCnt() uint32 {
	if m != nil {
		return m.FellowPositionCnt
	}
	return 0
}

func (m *GetFellowListResp) GetPendingInviteCnt() uint32 {
	if m != nil {
		return m.PendingInviteCnt
	}
	return 0
}

func (m *GetFellowListResp) GetUnlockInfo() *UnlockInfo {
	if m != nil {
		return m.UnlockInfo
	}
	return nil
}

func (m *GetFellowListResp) GetSendInviteCount() uint32 {
	if m != nil {
		return m.SendInviteCount
	}
	return 0
}

func (m *GetFellowListResp) GetRareTabList() []*RareTabInfo {
	if m != nil {
		return m.RareTabList
	}
	return nil
}

type GetFellowSimpleInfoReq struct {
	Uid                  int64    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFellowSimpleInfoReq) Reset()         { *m = GetFellowSimpleInfoReq{} }
func (m *GetFellowSimpleInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowSimpleInfoReq) ProtoMessage()    {}
func (*GetFellowSimpleInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{11}
}
func (m *GetFellowSimpleInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowSimpleInfoReq.Unmarshal(m, b)
}
func (m *GetFellowSimpleInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowSimpleInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowSimpleInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowSimpleInfoReq.Merge(dst, src)
}
func (m *GetFellowSimpleInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowSimpleInfoReq.Size(m)
}
func (m *GetFellowSimpleInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowSimpleInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowSimpleInfoReq proto.InternalMessageInfo

func (m *GetFellowSimpleInfoReq) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetFellowSimpleInfoResp struct {
	Uid                  int64         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FellowList           []*FellowInfo `protobuf:"bytes,2,rep,name=fellow_list,json=fellowList,proto3" json:"fellow_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetFellowSimpleInfoResp) Reset()         { *m = GetFellowSimpleInfoResp{} }
func (m *GetFellowSimpleInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowSimpleInfoResp) ProtoMessage()    {}
func (*GetFellowSimpleInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{12}
}
func (m *GetFellowSimpleInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowSimpleInfoResp.Unmarshal(m, b)
}
func (m *GetFellowSimpleInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowSimpleInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowSimpleInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowSimpleInfoResp.Merge(dst, src)
}
func (m *GetFellowSimpleInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowSimpleInfoResp.Size(m)
}
func (m *GetFellowSimpleInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowSimpleInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowSimpleInfoResp proto.InternalMessageInfo

func (m *GetFellowSimpleInfoResp) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFellowSimpleInfoResp) GetFellowList() []*FellowInfo {
	if m != nil {
		return m.FellowList
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type FellowPair struct {
	UidA                 int64    `protobuf:"varint,1,opt,name=uidA,proto3" json:"uidA,omitempty"`
	UidB                 int64    `protobuf:"varint,2,opt,name=uidB,proto3" json:"uidB,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowPair) Reset()         { *m = FellowPair{} }
func (m *FellowPair) String() string { return proto.CompactTextString(m) }
func (*FellowPair) ProtoMessage()    {}
func (*FellowPair) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{13}
}
func (m *FellowPair) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowPair.Unmarshal(m, b)
}
func (m *FellowPair) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowPair.Marshal(b, m, deterministic)
}
func (dst *FellowPair) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowPair.Merge(dst, src)
}
func (m *FellowPair) XXX_Size() int {
	return xxx_messageInfo_FellowPair.Size(m)
}
func (m *FellowPair) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowPair.DiscardUnknown(m)
}

var xxx_messageInfo_FellowPair proto.InternalMessageInfo

func (m *FellowPair) GetUidA() int64 {
	if m != nil {
		return m.UidA
	}
	return 0
}

func (m *FellowPair) GetUidB() int64 {
	if m != nil {
		return m.UidB
	}
	return 0
}

type BatchGetFellowSimpleInfoByPairListReq struct {
	PairList             []*FellowPair `protobuf:"bytes,1,rep,name=pair_list,json=pairList,proto3" json:"pair_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BatchGetFellowSimpleInfoByPairListReq) Reset()         { *m = BatchGetFellowSimpleInfoByPairListReq{} }
func (m *BatchGetFellowSimpleInfoByPairListReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetFellowSimpleInfoByPairListReq) ProtoMessage()    {}
func (*BatchGetFellowSimpleInfoByPairListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{14}
}
func (m *BatchGetFellowSimpleInfoByPairListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetFellowSimpleInfoByPairListReq.Unmarshal(m, b)
}
func (m *BatchGetFellowSimpleInfoByPairListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetFellowSimpleInfoByPairListReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetFellowSimpleInfoByPairListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetFellowSimpleInfoByPairListReq.Merge(dst, src)
}
func (m *BatchGetFellowSimpleInfoByPairListReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetFellowSimpleInfoByPairListReq.Size(m)
}
func (m *BatchGetFellowSimpleInfoByPairListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetFellowSimpleInfoByPairListReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetFellowSimpleInfoByPairListReq proto.InternalMessageInfo

func (m *BatchGetFellowSimpleInfoByPairListReq) GetPairList() []*FellowPair {
	if m != nil {
		return m.PairList
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type SimpleFellowInfo struct {
	UidA                 uint32   `protobuf:"varint,1,opt,name=uidA,proto3" json:"uidA,omitempty"`
	UidB                 uint32   `protobuf:"varint,2,opt,name=uidB,proto3" json:"uidB,omitempty"`
	Level                uint32   `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	FellowName           string   `protobuf:"bytes,4,opt,name=fellow_name,json=fellowName,proto3" json:"fellow_name,omitempty"`
	Point                uint32   `protobuf:"varint,5,opt,name=point,proto3" json:"point,omitempty"`
	DayCnt               uint32   `protobuf:"varint,6,opt,name=day_cnt,json=dayCnt,proto3" json:"day_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SimpleFellowInfo) Reset()         { *m = SimpleFellowInfo{} }
func (m *SimpleFellowInfo) String() string { return proto.CompactTextString(m) }
func (*SimpleFellowInfo) ProtoMessage()    {}
func (*SimpleFellowInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{15}
}
func (m *SimpleFellowInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleFellowInfo.Unmarshal(m, b)
}
func (m *SimpleFellowInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleFellowInfo.Marshal(b, m, deterministic)
}
func (dst *SimpleFellowInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleFellowInfo.Merge(dst, src)
}
func (m *SimpleFellowInfo) XXX_Size() int {
	return xxx_messageInfo_SimpleFellowInfo.Size(m)
}
func (m *SimpleFellowInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleFellowInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleFellowInfo proto.InternalMessageInfo

func (m *SimpleFellowInfo) GetUidA() uint32 {
	if m != nil {
		return m.UidA
	}
	return 0
}

func (m *SimpleFellowInfo) GetUidB() uint32 {
	if m != nil {
		return m.UidB
	}
	return 0
}

func (m *SimpleFellowInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *SimpleFellowInfo) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

func (m *SimpleFellowInfo) GetPoint() uint32 {
	if m != nil {
		return m.Point
	}
	return 0
}

func (m *SimpleFellowInfo) GetDayCnt() uint32 {
	if m != nil {
		return m.DayCnt
	}
	return 0
}

type BatchGetFellowSimpleInfoByPairListResp struct {
	FellowList           []*SimpleFellowInfo `protobuf:"bytes,1,rep,name=fellow_list,json=fellowList,proto3" json:"fellow_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatchGetFellowSimpleInfoByPairListResp) Reset() {
	*m = BatchGetFellowSimpleInfoByPairListResp{}
}
func (m *BatchGetFellowSimpleInfoByPairListResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetFellowSimpleInfoByPairListResp) ProtoMessage()    {}
func (*BatchGetFellowSimpleInfoByPairListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{16}
}
func (m *BatchGetFellowSimpleInfoByPairListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetFellowSimpleInfoByPairListResp.Unmarshal(m, b)
}
func (m *BatchGetFellowSimpleInfoByPairListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetFellowSimpleInfoByPairListResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetFellowSimpleInfoByPairListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetFellowSimpleInfoByPairListResp.Merge(dst, src)
}
func (m *BatchGetFellowSimpleInfoByPairListResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetFellowSimpleInfoByPairListResp.Size(m)
}
func (m *BatchGetFellowSimpleInfoByPairListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetFellowSimpleInfoByPairListResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetFellowSimpleInfoByPairListResp proto.InternalMessageInfo

func (m *BatchGetFellowSimpleInfoByPairListResp) GetFellowList() []*SimpleFellowInfo {
	if m != nil {
		return m.FellowList
	}
	return nil
}

type UnlockInfo struct {
	UnlockPrice          uint32   `protobuf:"varint,1,opt,name=unlock_price,json=unlockPrice,proto3" json:"unlock_price,omitempty"`
	UnlockLevel          uint32   `protobuf:"varint,2,opt,name=unlock_level,json=unlockLevel,proto3" json:"unlock_level,omitempty"`
	UnlockSite           uint32   `protobuf:"varint,3,opt,name=unlock_site,json=unlockSite,proto3" json:"unlock_site,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnlockInfo) Reset()         { *m = UnlockInfo{} }
func (m *UnlockInfo) String() string { return proto.CompactTextString(m) }
func (*UnlockInfo) ProtoMessage()    {}
func (*UnlockInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{17}
}
func (m *UnlockInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnlockInfo.Unmarshal(m, b)
}
func (m *UnlockInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnlockInfo.Marshal(b, m, deterministic)
}
func (dst *UnlockInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnlockInfo.Merge(dst, src)
}
func (m *UnlockInfo) XXX_Size() int {
	return xxx_messageInfo_UnlockInfo.Size(m)
}
func (m *UnlockInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UnlockInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UnlockInfo proto.InternalMessageInfo

func (m *UnlockInfo) GetUnlockPrice() uint32 {
	if m != nil {
		return m.UnlockPrice
	}
	return 0
}

func (m *UnlockInfo) GetUnlockLevel() uint32 {
	if m != nil {
		return m.UnlockLevel
	}
	return 0
}

func (m *UnlockInfo) GetUnlockSite() uint32 {
	if m != nil {
		return m.UnlockSite
	}
	return 0
}

type GetFellowCandidateListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BindType             uint32   `protobuf:"varint,2,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFellowCandidateListReq) Reset()         { *m = GetFellowCandidateListReq{} }
func (m *GetFellowCandidateListReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowCandidateListReq) ProtoMessage()    {}
func (*GetFellowCandidateListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{18}
}
func (m *GetFellowCandidateListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowCandidateListReq.Unmarshal(m, b)
}
func (m *GetFellowCandidateListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowCandidateListReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowCandidateListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowCandidateListReq.Merge(dst, src)
}
func (m *GetFellowCandidateListReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowCandidateListReq.Size(m)
}
func (m *GetFellowCandidateListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowCandidateListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowCandidateListReq proto.InternalMessageInfo

func (m *GetFellowCandidateListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFellowCandidateListReq) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *GetFellowCandidateListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetFellowCandidateListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetFellowCandidateListResp struct {
	FellowList           []*FellowInviteUser `protobuf:"bytes,1,rep,name=fellow_list,json=fellowList,proto3" json:"fellow_list,omitempty"`
	IsReachEnd           bool                `protobuf:"varint,2,opt,name=is_reach_end,json=isReachEnd,proto3" json:"is_reach_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetFellowCandidateListResp) Reset()         { *m = GetFellowCandidateListResp{} }
func (m *GetFellowCandidateListResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowCandidateListResp) ProtoMessage()    {}
func (*GetFellowCandidateListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{19}
}
func (m *GetFellowCandidateListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowCandidateListResp.Unmarshal(m, b)
}
func (m *GetFellowCandidateListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowCandidateListResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowCandidateListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowCandidateListResp.Merge(dst, src)
}
func (m *GetFellowCandidateListResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowCandidateListResp.Size(m)
}
func (m *GetFellowCandidateListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowCandidateListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowCandidateListResp proto.InternalMessageInfo

func (m *GetFellowCandidateListResp) GetFellowList() []*FellowInviteUser {
	if m != nil {
		return m.FellowList
	}
	return nil
}

func (m *GetFellowCandidateListResp) GetIsReachEnd() bool {
	if m != nil {
		return m.IsReachEnd
	}
	return false
}

type GetFellowCandidateInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFellowCandidateInfoReq) Reset()         { *m = GetFellowCandidateInfoReq{} }
func (m *GetFellowCandidateInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowCandidateInfoReq) ProtoMessage()    {}
func (*GetFellowCandidateInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{20}
}
func (m *GetFellowCandidateInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowCandidateInfoReq.Unmarshal(m, b)
}
func (m *GetFellowCandidateInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowCandidateInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowCandidateInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowCandidateInfoReq.Merge(dst, src)
}
func (m *GetFellowCandidateInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowCandidateInfoReq.Size(m)
}
func (m *GetFellowCandidateInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowCandidateInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowCandidateInfoReq proto.InternalMessageInfo

func (m *GetFellowCandidateInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFellowCandidateInfoReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetFellowCandidateInfoResp struct {
	PresentInfo           []*FellowPresentInfo `protobuf:"bytes,1,rep,name=present_info,json=presentInfo,proto3" json:"present_info,omitempty"`
	UserInfo              *FellowInviteUser    `protobuf:"bytes,2,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	MultiFellowOptionList []*FellowTypeInfo    `protobuf:"bytes,3,rep,name=multi_fellow_option_list,json=multiFellowOptionList,proto3" json:"multi_fellow_option_list,omitempty"`
	HasMultiFellowField   bool                 `protobuf:"varint,4,opt,name=has_multi_fellow_field,json=hasMultiFellowField,proto3" json:"has_multi_fellow_field,omitempty"`
	UnlockPrice           uint32               `protobuf:"varint,5,opt,name=unlock_price,json=unlockPrice,proto3" json:"unlock_price,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}             `json:"-"`
	XXX_unrecognized      []byte               `json:"-"`
	XXX_sizecache         int32                `json:"-"`
}

func (m *GetFellowCandidateInfoResp) Reset()         { *m = GetFellowCandidateInfoResp{} }
func (m *GetFellowCandidateInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowCandidateInfoResp) ProtoMessage()    {}
func (*GetFellowCandidateInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{21}
}
func (m *GetFellowCandidateInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowCandidateInfoResp.Unmarshal(m, b)
}
func (m *GetFellowCandidateInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowCandidateInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowCandidateInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowCandidateInfoResp.Merge(dst, src)
}
func (m *GetFellowCandidateInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowCandidateInfoResp.Size(m)
}
func (m *GetFellowCandidateInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowCandidateInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowCandidateInfoResp proto.InternalMessageInfo

func (m *GetFellowCandidateInfoResp) GetPresentInfo() []*FellowPresentInfo {
	if m != nil {
		return m.PresentInfo
	}
	return nil
}

func (m *GetFellowCandidateInfoResp) GetUserInfo() *FellowInviteUser {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *GetFellowCandidateInfoResp) GetMultiFellowOptionList() []*FellowTypeInfo {
	if m != nil {
		return m.MultiFellowOptionList
	}
	return nil
}

func (m *GetFellowCandidateInfoResp) GetHasMultiFellowField() bool {
	if m != nil {
		return m.HasMultiFellowField
	}
	return false
}

func (m *GetFellowCandidateInfoResp) GetUnlockPrice() uint32 {
	if m != nil {
		return m.UnlockPrice
	}
	return 0
}

type SendFellowInviteReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	BindType             uint32   `protobuf:"varint,3,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	PresentId            uint32   `protobuf:"varint,4,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	FellowType           uint32   `protobuf:"varint,5,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	WithUnlock           bool     `protobuf:"varint,6,opt,name=with_unlock,json=withUnlock,proto3" json:"with_unlock,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendFellowInviteReq) Reset()         { *m = SendFellowInviteReq{} }
func (m *SendFellowInviteReq) String() string { return proto.CompactTextString(m) }
func (*SendFellowInviteReq) ProtoMessage()    {}
func (*SendFellowInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{22}
}
func (m *SendFellowInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendFellowInviteReq.Unmarshal(m, b)
}
func (m *SendFellowInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendFellowInviteReq.Marshal(b, m, deterministic)
}
func (dst *SendFellowInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendFellowInviteReq.Merge(dst, src)
}
func (m *SendFellowInviteReq) XXX_Size() int {
	return xxx_messageInfo_SendFellowInviteReq.Size(m)
}
func (m *SendFellowInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendFellowInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendFellowInviteReq proto.InternalMessageInfo

func (m *SendFellowInviteReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendFellowInviteReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SendFellowInviteReq) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *SendFellowInviteReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *SendFellowInviteReq) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *SendFellowInviteReq) GetWithUnlock() bool {
	if m != nil {
		return m.WithUnlock
	}
	return false
}

type SendFellowInviteResp struct {
	RemainCurrency       int64    `protobuf:"varint,1,opt,name=remain_currency,json=remainCurrency,proto3" json:"remain_currency,omitempty"`
	RemainTbean          int64    `protobuf:"varint,2,opt,name=remain_tbean,json=remainTbean,proto3" json:"remain_tbean,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendFellowInviteResp) Reset()         { *m = SendFellowInviteResp{} }
func (m *SendFellowInviteResp) String() string { return proto.CompactTextString(m) }
func (*SendFellowInviteResp) ProtoMessage()    {}
func (*SendFellowInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{23}
}
func (m *SendFellowInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendFellowInviteResp.Unmarshal(m, b)
}
func (m *SendFellowInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendFellowInviteResp.Marshal(b, m, deterministic)
}
func (dst *SendFellowInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendFellowInviteResp.Merge(dst, src)
}
func (m *SendFellowInviteResp) XXX_Size() int {
	return xxx_messageInfo_SendFellowInviteResp.Size(m)
}
func (m *SendFellowInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendFellowInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendFellowInviteResp proto.InternalMessageInfo

func (m *SendFellowInviteResp) GetRemainCurrency() int64 {
	if m != nil {
		return m.RemainCurrency
	}
	return 0
}

func (m *SendFellowInviteResp) GetRemainTbean() int64 {
	if m != nil {
		return m.RemainTbean
	}
	return 0
}

type GetFellowInviteListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFellowInviteListReq) Reset()         { *m = GetFellowInviteListReq{} }
func (m *GetFellowInviteListReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowInviteListReq) ProtoMessage()    {}
func (*GetFellowInviteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{24}
}
func (m *GetFellowInviteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowInviteListReq.Unmarshal(m, b)
}
func (m *GetFellowInviteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowInviteListReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowInviteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowInviteListReq.Merge(dst, src)
}
func (m *GetFellowInviteListReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowInviteListReq.Size(m)
}
func (m *GetFellowInviteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowInviteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowInviteListReq proto.InternalMessageInfo

func (m *GetFellowInviteListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetFellowInviteListResp struct {
	InviteList           []*FellowInviteInfo `protobuf:"bytes,1,rep,name=invite_list,json=inviteList,proto3" json:"invite_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetFellowInviteListResp) Reset()         { *m = GetFellowInviteListResp{} }
func (m *GetFellowInviteListResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowInviteListResp) ProtoMessage()    {}
func (*GetFellowInviteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{25}
}
func (m *GetFellowInviteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowInviteListResp.Unmarshal(m, b)
}
func (m *GetFellowInviteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowInviteListResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowInviteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowInviteListResp.Merge(dst, src)
}
func (m *GetFellowInviteListResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowInviteListResp.Size(m)
}
func (m *GetFellowInviteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowInviteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowInviteListResp proto.InternalMessageInfo

func (m *GetFellowInviteListResp) GetInviteList() []*FellowInviteInfo {
	if m != nil {
		return m.InviteList
	}
	return nil
}

type GetFellowInviteInfoByIdReq struct {
	InviteId             string   `protobuf:"bytes,1,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFellowInviteInfoByIdReq) Reset()         { *m = GetFellowInviteInfoByIdReq{} }
func (m *GetFellowInviteInfoByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowInviteInfoByIdReq) ProtoMessage()    {}
func (*GetFellowInviteInfoByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{26}
}
func (m *GetFellowInviteInfoByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowInviteInfoByIdReq.Unmarshal(m, b)
}
func (m *GetFellowInviteInfoByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowInviteInfoByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowInviteInfoByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowInviteInfoByIdReq.Merge(dst, src)
}
func (m *GetFellowInviteInfoByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowInviteInfoByIdReq.Size(m)
}
func (m *GetFellowInviteInfoByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowInviteInfoByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowInviteInfoByIdReq proto.InternalMessageInfo

func (m *GetFellowInviteInfoByIdReq) GetInviteId() string {
	if m != nil {
		return m.InviteId
	}
	return ""
}

type GetFellowInviteInfoByIdResp struct {
	InviteList           *FellowInviteInfo `protobuf:"bytes,1,opt,name=invite_list,json=inviteList,proto3" json:"invite_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetFellowInviteInfoByIdResp) Reset()         { *m = GetFellowInviteInfoByIdResp{} }
func (m *GetFellowInviteInfoByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowInviteInfoByIdResp) ProtoMessage()    {}
func (*GetFellowInviteInfoByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{27}
}
func (m *GetFellowInviteInfoByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowInviteInfoByIdResp.Unmarshal(m, b)
}
func (m *GetFellowInviteInfoByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowInviteInfoByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowInviteInfoByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowInviteInfoByIdResp.Merge(dst, src)
}
func (m *GetFellowInviteInfoByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowInviteInfoByIdResp.Size(m)
}
func (m *GetFellowInviteInfoByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowInviteInfoByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowInviteInfoByIdResp proto.InternalMessageInfo

func (m *GetFellowInviteInfoByIdResp) GetInviteList() *FellowInviteInfo {
	if m != nil {
		return m.InviteList
	}
	return nil
}

type ServiceCtrlInfo struct {
	ClientIp             string   `protobuf:"bytes,1,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	ClientPort           uint32   `protobuf:"varint,2,opt,name=client_port,json=clientPort,proto3" json:"client_port,omitempty"`
	DeviceId             []byte   `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,4,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	TerminalType         uint32   `protobuf:"varint,5,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	ClientId             uint32   `protobuf:"varint,6,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,7,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ServiceCtrlInfo) Reset()         { *m = ServiceCtrlInfo{} }
func (m *ServiceCtrlInfo) String() string { return proto.CompactTextString(m) }
func (*ServiceCtrlInfo) ProtoMessage()    {}
func (*ServiceCtrlInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{28}
}
func (m *ServiceCtrlInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServiceCtrlInfo.Unmarshal(m, b)
}
func (m *ServiceCtrlInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServiceCtrlInfo.Marshal(b, m, deterministic)
}
func (dst *ServiceCtrlInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServiceCtrlInfo.Merge(dst, src)
}
func (m *ServiceCtrlInfo) XXX_Size() int {
	return xxx_messageInfo_ServiceCtrlInfo.Size(m)
}
func (m *ServiceCtrlInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ServiceCtrlInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ServiceCtrlInfo proto.InternalMessageInfo

func (m *ServiceCtrlInfo) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *ServiceCtrlInfo) GetClientPort() uint32 {
	if m != nil {
		return m.ClientPort
	}
	return 0
}

func (m *ServiceCtrlInfo) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *ServiceCtrlInfo) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *ServiceCtrlInfo) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *ServiceCtrlInfo) GetClientId() uint32 {
	if m != nil {
		return m.ClientId
	}
	return 0
}

func (m *ServiceCtrlInfo) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

type HandleFellowInviteReq struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	InviteId             string           `protobuf:"bytes,2,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	IsAcceptInvite       bool             `protobuf:"varint,3,opt,name=is_accept_invite,json=isAcceptInvite,proto3" json:"is_accept_invite,omitempty"`
	MarketId             uint32           `protobuf:"varint,4,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	AppId                uint32           `protobuf:"varint,5,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	ServiceInfo          *ServiceCtrlInfo `protobuf:"bytes,6,opt,name=service_info,json=serviceInfo,proto3" json:"service_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *HandleFellowInviteReq) Reset()         { *m = HandleFellowInviteReq{} }
func (m *HandleFellowInviteReq) String() string { return proto.CompactTextString(m) }
func (*HandleFellowInviteReq) ProtoMessage()    {}
func (*HandleFellowInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{29}
}
func (m *HandleFellowInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleFellowInviteReq.Unmarshal(m, b)
}
func (m *HandleFellowInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleFellowInviteReq.Marshal(b, m, deterministic)
}
func (dst *HandleFellowInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleFellowInviteReq.Merge(dst, src)
}
func (m *HandleFellowInviteReq) XXX_Size() int {
	return xxx_messageInfo_HandleFellowInviteReq.Size(m)
}
func (m *HandleFellowInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleFellowInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_HandleFellowInviteReq proto.InternalMessageInfo

func (m *HandleFellowInviteReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HandleFellowInviteReq) GetInviteId() string {
	if m != nil {
		return m.InviteId
	}
	return ""
}

func (m *HandleFellowInviteReq) GetIsAcceptInvite() bool {
	if m != nil {
		return m.IsAcceptInvite
	}
	return false
}

func (m *HandleFellowInviteReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *HandleFellowInviteReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *HandleFellowInviteReq) GetServiceInfo() *ServiceCtrlInfo {
	if m != nil {
		return m.ServiceInfo
	}
	return nil
}

type HandleFellowInviteResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleFellowInviteResp) Reset()         { *m = HandleFellowInviteResp{} }
func (m *HandleFellowInviteResp) String() string { return proto.CompactTextString(m) }
func (*HandleFellowInviteResp) ProtoMessage()    {}
func (*HandleFellowInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{30}
}
func (m *HandleFellowInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleFellowInviteResp.Unmarshal(m, b)
}
func (m *HandleFellowInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleFellowInviteResp.Marshal(b, m, deterministic)
}
func (dst *HandleFellowInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleFellowInviteResp.Merge(dst, src)
}
func (m *HandleFellowInviteResp) XXX_Size() int {
	return xxx_messageInfo_HandleFellowInviteResp.Size(m)
}
func (m *HandleFellowInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleFellowInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_HandleFellowInviteResp proto.InternalMessageInfo

// web接口
type GetWebFellowListReq struct {
	Uid                  int64    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FellowUid            int64    `protobuf:"varint,2,opt,name=fellow_uid,json=fellowUid,proto3" json:"fellow_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWebFellowListReq) Reset()         { *m = GetWebFellowListReq{} }
func (m *GetWebFellowListReq) String() string { return proto.CompactTextString(m) }
func (*GetWebFellowListReq) ProtoMessage()    {}
func (*GetWebFellowListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{31}
}
func (m *GetWebFellowListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWebFellowListReq.Unmarshal(m, b)
}
func (m *GetWebFellowListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWebFellowListReq.Marshal(b, m, deterministic)
}
func (dst *GetWebFellowListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWebFellowListReq.Merge(dst, src)
}
func (m *GetWebFellowListReq) XXX_Size() int {
	return xxx_messageInfo_GetWebFellowListReq.Size(m)
}
func (m *GetWebFellowListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWebFellowListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWebFellowListReq proto.InternalMessageInfo

func (m *GetWebFellowListReq) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetWebFellowListReq) GetFellowUid() int64 {
	if m != nil {
		return m.FellowUid
	}
	return 0
}

type FellowTask struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	AddPoint             float32  `protobuf:"fixed32,2,opt,name=add_point,json=addPoint,proto3" json:"add_point,omitempty"`
	MaxPoint             uint32   `protobuf:"varint,3,opt,name=max_point,json=maxPoint,proto3" json:"max_point,omitempty"`
	MaxCnt               uint32   `protobuf:"varint,4,opt,name=max_cnt,json=maxCnt,proto3" json:"max_cnt,omitempty"`
	CompleteCnt          uint32   `protobuf:"varint,5,opt,name=complete_cnt,json=completeCnt,proto3" json:"complete_cnt,omitempty"`
	Title                string   `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	JumpUrl              string   `protobuf:"bytes,7,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	Icon                 string   `protobuf:"bytes,8,opt,name=icon,proto3" json:"icon,omitempty"`
	Desc                 string   `protobuf:"bytes,9,opt,name=desc,proto3" json:"desc,omitempty"`
	ButtonText           string   `protobuf:"bytes,10,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowTask) Reset()         { *m = FellowTask{} }
func (m *FellowTask) String() string { return proto.CompactTextString(m) }
func (*FellowTask) ProtoMessage()    {}
func (*FellowTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{32}
}
func (m *FellowTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowTask.Unmarshal(m, b)
}
func (m *FellowTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowTask.Marshal(b, m, deterministic)
}
func (dst *FellowTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowTask.Merge(dst, src)
}
func (m *FellowTask) XXX_Size() int {
	return xxx_messageInfo_FellowTask.Size(m)
}
func (m *FellowTask) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowTask.DiscardUnknown(m)
}

var xxx_messageInfo_FellowTask proto.InternalMessageInfo

func (m *FellowTask) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *FellowTask) GetAddPoint() float32 {
	if m != nil {
		return m.AddPoint
	}
	return 0
}

func (m *FellowTask) GetMaxPoint() uint32 {
	if m != nil {
		return m.MaxPoint
	}
	return 0
}

func (m *FellowTask) GetMaxCnt() uint32 {
	if m != nil {
		return m.MaxCnt
	}
	return 0
}

func (m *FellowTask) GetCompleteCnt() uint32 {
	if m != nil {
		return m.CompleteCnt
	}
	return 0
}

func (m *FellowTask) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *FellowTask) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *FellowTask) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *FellowTask) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *FellowTask) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

type FellowLevelConfig struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	Point                uint32   `protobuf:"varint,2,opt,name=point,proto3" json:"point,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Award                string   `protobuf:"bytes,4,opt,name=award,proto3" json:"award,omitempty"`
	Icon                 string   `protobuf:"bytes,5,opt,name=icon,proto3" json:"icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowLevelConfig) Reset()         { *m = FellowLevelConfig{} }
func (m *FellowLevelConfig) String() string { return proto.CompactTextString(m) }
func (*FellowLevelConfig) ProtoMessage()    {}
func (*FellowLevelConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{33}
}
func (m *FellowLevelConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowLevelConfig.Unmarshal(m, b)
}
func (m *FellowLevelConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowLevelConfig.Marshal(b, m, deterministic)
}
func (dst *FellowLevelConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowLevelConfig.Merge(dst, src)
}
func (m *FellowLevelConfig) XXX_Size() int {
	return xxx_messageInfo_FellowLevelConfig.Size(m)
}
func (m *FellowLevelConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowLevelConfig.DiscardUnknown(m)
}

var xxx_messageInfo_FellowLevelConfig proto.InternalMessageInfo

func (m *FellowLevelConfig) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *FellowLevelConfig) GetPoint() uint32 {
	if m != nil {
		return m.Point
	}
	return 0
}

func (m *FellowLevelConfig) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *FellowLevelConfig) GetAward() string {
	if m != nil {
		return m.Award
	}
	return ""
}

func (m *FellowLevelConfig) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

type GetWebFellowListResp struct {
	Uid                  int64                `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FellowUid            int64                `protobuf:"varint,2,opt,name=fellow_uid,json=fellowUid,proto3" json:"fellow_uid,omitempty"`
	FellowPoint          uint32               `protobuf:"varint,3,opt,name=fellow_point,json=fellowPoint,proto3" json:"fellow_point,omitempty"`
	BindType             uint32               `protobuf:"varint,4,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	FellowType           uint32               `protobuf:"varint,5,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	Task                 []*FellowTask        `protobuf:"bytes,6,rep,name=task,proto3" json:"task,omitempty"`
	LevelConfig          []*FellowLevelConfig `protobuf:"bytes,7,rep,name=level_config,json=levelConfig,proto3" json:"level_config,omitempty"`
	BindDay              uint32               `protobuf:"varint,8,opt,name=bind_day,json=bindDay,proto3" json:"bind_day,omitempty"`
	UnboundTime          uint32               `protobuf:"varint,9,opt,name=unbound_time,json=unboundTime,proto3" json:"unbound_time,omitempty"`
	PresentInfo          *FellowPresentInfo   `protobuf:"bytes,10,opt,name=present_info,json=presentInfo,proto3" json:"present_info,omitempty"`
	GradingInfo          *GradingInfo         `protobuf:"bytes,11,opt,name=grading_info,json=gradingInfo,proto3" json:"grading_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetWebFellowListResp) Reset()         { *m = GetWebFellowListResp{} }
func (m *GetWebFellowListResp) String() string { return proto.CompactTextString(m) }
func (*GetWebFellowListResp) ProtoMessage()    {}
func (*GetWebFellowListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{34}
}
func (m *GetWebFellowListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWebFellowListResp.Unmarshal(m, b)
}
func (m *GetWebFellowListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWebFellowListResp.Marshal(b, m, deterministic)
}
func (dst *GetWebFellowListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWebFellowListResp.Merge(dst, src)
}
func (m *GetWebFellowListResp) XXX_Size() int {
	return xxx_messageInfo_GetWebFellowListResp.Size(m)
}
func (m *GetWebFellowListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWebFellowListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWebFellowListResp proto.InternalMessageInfo

func (m *GetWebFellowListResp) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetWebFellowListResp) GetFellowUid() int64 {
	if m != nil {
		return m.FellowUid
	}
	return 0
}

func (m *GetWebFellowListResp) GetFellowPoint() uint32 {
	if m != nil {
		return m.FellowPoint
	}
	return 0
}

func (m *GetWebFellowListResp) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *GetWebFellowListResp) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *GetWebFellowListResp) GetTask() []*FellowTask {
	if m != nil {
		return m.Task
	}
	return nil
}

func (m *GetWebFellowListResp) GetLevelConfig() []*FellowLevelConfig {
	if m != nil {
		return m.LevelConfig
	}
	return nil
}

func (m *GetWebFellowListResp) GetBindDay() uint32 {
	if m != nil {
		return m.BindDay
	}
	return 0
}

func (m *GetWebFellowListResp) GetUnboundTime() uint32 {
	if m != nil {
		return m.UnboundTime
	}
	return 0
}

func (m *GetWebFellowListResp) GetPresentInfo() *FellowPresentInfo {
	if m != nil {
		return m.PresentInfo
	}
	return nil
}

func (m *GetWebFellowListResp) GetGradingInfo() *GradingInfo {
	if m != nil {
		return m.GradingInfo
	}
	return nil
}

type CheckFellowInviteReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BindType             uint32   `protobuf:"varint,2,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckFellowInviteReq) Reset()         { *m = CheckFellowInviteReq{} }
func (m *CheckFellowInviteReq) String() string { return proto.CompactTextString(m) }
func (*CheckFellowInviteReq) ProtoMessage()    {}
func (*CheckFellowInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{35}
}
func (m *CheckFellowInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckFellowInviteReq.Unmarshal(m, b)
}
func (m *CheckFellowInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckFellowInviteReq.Marshal(b, m, deterministic)
}
func (dst *CheckFellowInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckFellowInviteReq.Merge(dst, src)
}
func (m *CheckFellowInviteReq) XXX_Size() int {
	return xxx_messageInfo_CheckFellowInviteReq.Size(m)
}
func (m *CheckFellowInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckFellowInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckFellowInviteReq proto.InternalMessageInfo

func (m *CheckFellowInviteReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckFellowInviteReq) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

type CheckFellowInviteResp struct {
	ReachLimit           bool        `protobuf:"varint,1,opt,name=reach_limit,json=reachLimit,proto3" json:"reach_limit,omitempty"`
	CpName               string      `protobuf:"bytes,2,opt,name=cp_name,json=cpName,proto3" json:"cp_name,omitempty"`
	CpSex                uint32      `protobuf:"varint,3,opt,name=cp_sex,json=cpSex,proto3" json:"cp_sex,omitempty"`
	UnlockInfo           *UnlockInfo `protobuf:"bytes,4,opt,name=unlock_info,json=unlockInfo,proto3" json:"unlock_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *CheckFellowInviteResp) Reset()         { *m = CheckFellowInviteResp{} }
func (m *CheckFellowInviteResp) String() string { return proto.CompactTextString(m) }
func (*CheckFellowInviteResp) ProtoMessage()    {}
func (*CheckFellowInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{36}
}
func (m *CheckFellowInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckFellowInviteResp.Unmarshal(m, b)
}
func (m *CheckFellowInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckFellowInviteResp.Marshal(b, m, deterministic)
}
func (dst *CheckFellowInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckFellowInviteResp.Merge(dst, src)
}
func (m *CheckFellowInviteResp) XXX_Size() int {
	return xxx_messageInfo_CheckFellowInviteResp.Size(m)
}
func (m *CheckFellowInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckFellowInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckFellowInviteResp proto.InternalMessageInfo

func (m *CheckFellowInviteResp) GetReachLimit() bool {
	if m != nil {
		return m.ReachLimit
	}
	return false
}

func (m *CheckFellowInviteResp) GetCpName() string {
	if m != nil {
		return m.CpName
	}
	return ""
}

func (m *CheckFellowInviteResp) GetCpSex() uint32 {
	if m != nil {
		return m.CpSex
	}
	return 0
}

func (m *CheckFellowInviteResp) GetUnlockInfo() *UnlockInfo {
	if m != nil {
		return m.UnlockInfo
	}
	return nil
}

type GetFellowPointReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FellowUid            uint32   `protobuf:"varint,2,opt,name=fellow_uid,json=fellowUid,proto3" json:"fellow_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFellowPointReq) Reset()         { *m = GetFellowPointReq{} }
func (m *GetFellowPointReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowPointReq) ProtoMessage()    {}
func (*GetFellowPointReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{37}
}
func (m *GetFellowPointReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowPointReq.Unmarshal(m, b)
}
func (m *GetFellowPointReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowPointReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowPointReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowPointReq.Merge(dst, src)
}
func (m *GetFellowPointReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowPointReq.Size(m)
}
func (m *GetFellowPointReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowPointReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowPointReq proto.InternalMessageInfo

func (m *GetFellowPointReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFellowPointReq) GetFellowUid() uint32 {
	if m != nil {
		return m.FellowUid
	}
	return 0
}

type GetFellowPointResp struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FellowUid            uint32   `protobuf:"varint,2,opt,name=fellow_uid,json=fellowUid,proto3" json:"fellow_uid,omitempty"`
	FellowPoint          uint32   `protobuf:"varint,3,opt,name=fellow_point,json=fellowPoint,proto3" json:"fellow_point,omitempty"`
	FellowLevel          uint32   `protobuf:"varint,4,opt,name=fellow_level,json=fellowLevel,proto3" json:"fellow_level,omitempty"`
	CurrentLevelPoint    uint32   `protobuf:"varint,5,opt,name=current_level_point,json=currentLevelPoint,proto3" json:"current_level_point,omitempty"`
	NextLevelPoint       uint32   `protobuf:"varint,6,opt,name=next_level_point,json=nextLevelPoint,proto3" json:"next_level_point,omitempty"`
	BindType             uint32   `protobuf:"varint,7,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFellowPointResp) Reset()         { *m = GetFellowPointResp{} }
func (m *GetFellowPointResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowPointResp) ProtoMessage()    {}
func (*GetFellowPointResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{38}
}
func (m *GetFellowPointResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowPointResp.Unmarshal(m, b)
}
func (m *GetFellowPointResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowPointResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowPointResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowPointResp.Merge(dst, src)
}
func (m *GetFellowPointResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowPointResp.Size(m)
}
func (m *GetFellowPointResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowPointResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowPointResp proto.InternalMessageInfo

func (m *GetFellowPointResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFellowPointResp) GetFellowUid() uint32 {
	if m != nil {
		return m.FellowUid
	}
	return 0
}

func (m *GetFellowPointResp) GetFellowPoint() uint32 {
	if m != nil {
		return m.FellowPoint
	}
	return 0
}

func (m *GetFellowPointResp) GetFellowLevel() uint32 {
	if m != nil {
		return m.FellowLevel
	}
	return 0
}

func (m *GetFellowPointResp) GetCurrentLevelPoint() uint32 {
	if m != nil {
		return m.CurrentLevelPoint
	}
	return 0
}

func (m *GetFellowPointResp) GetNextLevelPoint() uint32 {
	if m != nil {
		return m.NextLevelPoint
	}
	return 0
}

func (m *GetFellowPointResp) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

// 段位信息（一个等级区间称为一个段位）
type GradingInfo struct {
	Icon                 string   `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	Color                string   `protobuf:"bytes,2,opt,name=color,proto3" json:"color,omitempty"`
	GradingName          string   `protobuf:"bytes,3,opt,name=grading_name,json=gradingName,proto3" json:"grading_name,omitempty"`
	Level                uint32   `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GradingInfo) Reset()         { *m = GradingInfo{} }
func (m *GradingInfo) String() string { return proto.CompactTextString(m) }
func (*GradingInfo) ProtoMessage()    {}
func (*GradingInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{39}
}
func (m *GradingInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GradingInfo.Unmarshal(m, b)
}
func (m *GradingInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GradingInfo.Marshal(b, m, deterministic)
}
func (dst *GradingInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GradingInfo.Merge(dst, src)
}
func (m *GradingInfo) XXX_Size() int {
	return xxx_messageInfo_GradingInfo.Size(m)
}
func (m *GradingInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GradingInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GradingInfo proto.InternalMessageInfo

func (m *GradingInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *GradingInfo) GetColor() string {
	if m != nil {
		return m.Color
	}
	return ""
}

func (m *GradingInfo) GetGradingName() string {
	if m != nil {
		return m.GradingName
	}
	return ""
}

func (m *GradingInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type CancelFellowInviteReq struct {
	OpUid                uint32   `protobuf:"varint,1,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	InviteId             string   `protobuf:"bytes,2,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelFellowInviteReq) Reset()         { *m = CancelFellowInviteReq{} }
func (m *CancelFellowInviteReq) String() string { return proto.CompactTextString(m) }
func (*CancelFellowInviteReq) ProtoMessage()    {}
func (*CancelFellowInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{40}
}
func (m *CancelFellowInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelFellowInviteReq.Unmarshal(m, b)
}
func (m *CancelFellowInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelFellowInviteReq.Marshal(b, m, deterministic)
}
func (dst *CancelFellowInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelFellowInviteReq.Merge(dst, src)
}
func (m *CancelFellowInviteReq) XXX_Size() int {
	return xxx_messageInfo_CancelFellowInviteReq.Size(m)
}
func (m *CancelFellowInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelFellowInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelFellowInviteReq proto.InternalMessageInfo

func (m *CancelFellowInviteReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *CancelFellowInviteReq) GetInviteId() string {
	if m != nil {
		return m.InviteId
	}
	return ""
}

type CancelFellowInviteResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelFellowInviteResp) Reset()         { *m = CancelFellowInviteResp{} }
func (m *CancelFellowInviteResp) String() string { return proto.CompactTextString(m) }
func (*CancelFellowInviteResp) ProtoMessage()    {}
func (*CancelFellowInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{41}
}
func (m *CancelFellowInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelFellowInviteResp.Unmarshal(m, b)
}
func (m *CancelFellowInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelFellowInviteResp.Marshal(b, m, deterministic)
}
func (dst *CancelFellowInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelFellowInviteResp.Merge(dst, src)
}
func (m *CancelFellowInviteResp) XXX_Size() int {
	return xxx_messageInfo_CancelFellowInviteResp.Size(m)
}
func (m *CancelFellowInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelFellowInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelFellowInviteResp proto.InternalMessageInfo

type FellowPresentConfig struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	UniqueBackgroundUrl  string   `protobuf:"bytes,2,opt,name=unique_background_url,json=uniqueBackgroundUrl,proto3" json:"unique_background_url,omitempty"`
	UniqueSourceType     uint32   `protobuf:"varint,3,opt,name=unique_source_type,json=uniqueSourceType,proto3" json:"unique_source_type,omitempty"`
	UniqueMd5            string   `protobuf:"bytes,4,opt,name=unique_md5,json=uniqueMd5,proto3" json:"unique_md5,omitempty"`
	MultiBackgroundUrl   string   `protobuf:"bytes,5,opt,name=multi_background_url,json=multiBackgroundUrl,proto3" json:"multi_background_url,omitempty"`
	MultiSourceType      uint32   `protobuf:"varint,6,opt,name=multi_source_type,json=multiSourceType,proto3" json:"multi_source_type,omitempty"`
	MultiMd5             string   `protobuf:"bytes,7,opt,name=multi_md5,json=multiMd5,proto3" json:"multi_md5,omitempty"`
	UniqueBackgroundImg  string   `protobuf:"bytes,8,opt,name=unique_background_img,json=uniqueBackgroundImg,proto3" json:"unique_background_img,omitempty"`
	MultiBackgroundImg   string   `protobuf:"bytes,9,opt,name=multi_background_img,json=multiBackgroundImg,proto3" json:"multi_background_img,omitempty"`
	SourceUrl            string   `protobuf:"bytes,10,opt,name=source_url,json=sourceUrl,proto3" json:"source_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowPresentConfig) Reset()         { *m = FellowPresentConfig{} }
func (m *FellowPresentConfig) String() string { return proto.CompactTextString(m) }
func (*FellowPresentConfig) ProtoMessage()    {}
func (*FellowPresentConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{42}
}
func (m *FellowPresentConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowPresentConfig.Unmarshal(m, b)
}
func (m *FellowPresentConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowPresentConfig.Marshal(b, m, deterministic)
}
func (dst *FellowPresentConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowPresentConfig.Merge(dst, src)
}
func (m *FellowPresentConfig) XXX_Size() int {
	return xxx_messageInfo_FellowPresentConfig.Size(m)
}
func (m *FellowPresentConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowPresentConfig.DiscardUnknown(m)
}

var xxx_messageInfo_FellowPresentConfig proto.InternalMessageInfo

func (m *FellowPresentConfig) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *FellowPresentConfig) GetUniqueBackgroundUrl() string {
	if m != nil {
		return m.UniqueBackgroundUrl
	}
	return ""
}

func (m *FellowPresentConfig) GetUniqueSourceType() uint32 {
	if m != nil {
		return m.UniqueSourceType
	}
	return 0
}

func (m *FellowPresentConfig) GetUniqueMd5() string {
	if m != nil {
		return m.UniqueMd5
	}
	return ""
}

func (m *FellowPresentConfig) GetMultiBackgroundUrl() string {
	if m != nil {
		return m.MultiBackgroundUrl
	}
	return ""
}

func (m *FellowPresentConfig) GetMultiSourceType() uint32 {
	if m != nil {
		return m.MultiSourceType
	}
	return 0
}

func (m *FellowPresentConfig) GetMultiMd5() string {
	if m != nil {
		return m.MultiMd5
	}
	return ""
}

func (m *FellowPresentConfig) GetUniqueBackgroundImg() string {
	if m != nil {
		return m.UniqueBackgroundImg
	}
	return ""
}

func (m *FellowPresentConfig) GetMultiBackgroundImg() string {
	if m != nil {
		return m.MultiBackgroundImg
	}
	return ""
}

func (m *FellowPresentConfig) GetSourceUrl() string {
	if m != nil {
		return m.SourceUrl
	}
	return ""
}

type AddFellowPresentConfigReq struct {
	Config               *FellowPresentConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *AddFellowPresentConfigReq) Reset()         { *m = AddFellowPresentConfigReq{} }
func (m *AddFellowPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddFellowPresentConfigReq) ProtoMessage()    {}
func (*AddFellowPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{43}
}
func (m *AddFellowPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFellowPresentConfigReq.Unmarshal(m, b)
}
func (m *AddFellowPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFellowPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddFellowPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFellowPresentConfigReq.Merge(dst, src)
}
func (m *AddFellowPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddFellowPresentConfigReq.Size(m)
}
func (m *AddFellowPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFellowPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddFellowPresentConfigReq proto.InternalMessageInfo

func (m *AddFellowPresentConfigReq) GetConfig() *FellowPresentConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type AddFellowPresentConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddFellowPresentConfigResp) Reset()         { *m = AddFellowPresentConfigResp{} }
func (m *AddFellowPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddFellowPresentConfigResp) ProtoMessage()    {}
func (*AddFellowPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{44}
}
func (m *AddFellowPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFellowPresentConfigResp.Unmarshal(m, b)
}
func (m *AddFellowPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFellowPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddFellowPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFellowPresentConfigResp.Merge(dst, src)
}
func (m *AddFellowPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddFellowPresentConfigResp.Size(m)
}
func (m *AddFellowPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFellowPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddFellowPresentConfigResp proto.InternalMessageInfo

type UpdateFellowPresentConfigReq struct {
	Config               *FellowPresentConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UpdateFellowPresentConfigReq) Reset()         { *m = UpdateFellowPresentConfigReq{} }
func (m *UpdateFellowPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdateFellowPresentConfigReq) ProtoMessage()    {}
func (*UpdateFellowPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{45}
}
func (m *UpdateFellowPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFellowPresentConfigReq.Unmarshal(m, b)
}
func (m *UpdateFellowPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFellowPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdateFellowPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFellowPresentConfigReq.Merge(dst, src)
}
func (m *UpdateFellowPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdateFellowPresentConfigReq.Size(m)
}
func (m *UpdateFellowPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFellowPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFellowPresentConfigReq proto.InternalMessageInfo

func (m *UpdateFellowPresentConfigReq) GetConfig() *FellowPresentConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type UpdateFellowPresentConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateFellowPresentConfigResp) Reset()         { *m = UpdateFellowPresentConfigResp{} }
func (m *UpdateFellowPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdateFellowPresentConfigResp) ProtoMessage()    {}
func (*UpdateFellowPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{46}
}
func (m *UpdateFellowPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFellowPresentConfigResp.Unmarshal(m, b)
}
func (m *UpdateFellowPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFellowPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdateFellowPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFellowPresentConfigResp.Merge(dst, src)
}
func (m *UpdateFellowPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdateFellowPresentConfigResp.Size(m)
}
func (m *UpdateFellowPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFellowPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFellowPresentConfigResp proto.InternalMessageInfo

type DelFellowPresentConfigReq struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelFellowPresentConfigReq) Reset()         { *m = DelFellowPresentConfigReq{} }
func (m *DelFellowPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelFellowPresentConfigReq) ProtoMessage()    {}
func (*DelFellowPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{47}
}
func (m *DelFellowPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelFellowPresentConfigReq.Unmarshal(m, b)
}
func (m *DelFellowPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelFellowPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelFellowPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelFellowPresentConfigReq.Merge(dst, src)
}
func (m *DelFellowPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelFellowPresentConfigReq.Size(m)
}
func (m *DelFellowPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelFellowPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelFellowPresentConfigReq proto.InternalMessageInfo

func (m *DelFellowPresentConfigReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type DelFellowPresentConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelFellowPresentConfigResp) Reset()         { *m = DelFellowPresentConfigResp{} }
func (m *DelFellowPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelFellowPresentConfigResp) ProtoMessage()    {}
func (*DelFellowPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{48}
}
func (m *DelFellowPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelFellowPresentConfigResp.Unmarshal(m, b)
}
func (m *DelFellowPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelFellowPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelFellowPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelFellowPresentConfigResp.Merge(dst, src)
}
func (m *DelFellowPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelFellowPresentConfigResp.Size(m)
}
func (m *DelFellowPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelFellowPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelFellowPresentConfigResp proto.InternalMessageInfo

type GetFellowPresentConfigByIdReq struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFellowPresentConfigByIdReq) Reset()         { *m = GetFellowPresentConfigByIdReq{} }
func (m *GetFellowPresentConfigByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowPresentConfigByIdReq) ProtoMessage()    {}
func (*GetFellowPresentConfigByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{49}
}
func (m *GetFellowPresentConfigByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowPresentConfigByIdReq.Unmarshal(m, b)
}
func (m *GetFellowPresentConfigByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowPresentConfigByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowPresentConfigByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowPresentConfigByIdReq.Merge(dst, src)
}
func (m *GetFellowPresentConfigByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowPresentConfigByIdReq.Size(m)
}
func (m *GetFellowPresentConfigByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowPresentConfigByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowPresentConfigByIdReq proto.InternalMessageInfo

func (m *GetFellowPresentConfigByIdReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type GetFellowPresentConfigByIdResp struct {
	Config               *FellowPresentConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetFellowPresentConfigByIdResp) Reset()         { *m = GetFellowPresentConfigByIdResp{} }
func (m *GetFellowPresentConfigByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowPresentConfigByIdResp) ProtoMessage()    {}
func (*GetFellowPresentConfigByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{50}
}
func (m *GetFellowPresentConfigByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowPresentConfigByIdResp.Unmarshal(m, b)
}
func (m *GetFellowPresentConfigByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowPresentConfigByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowPresentConfigByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowPresentConfigByIdResp.Merge(dst, src)
}
func (m *GetFellowPresentConfigByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowPresentConfigByIdResp.Size(m)
}
func (m *GetFellowPresentConfigByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowPresentConfigByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowPresentConfigByIdResp proto.InternalMessageInfo

func (m *GetFellowPresentConfigByIdResp) GetConfig() *FellowPresentConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type GetAllFellowPresentConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllFellowPresentConfigReq) Reset()         { *m = GetAllFellowPresentConfigReq{} }
func (m *GetAllFellowPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetAllFellowPresentConfigReq) ProtoMessage()    {}
func (*GetAllFellowPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{51}
}
func (m *GetAllFellowPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllFellowPresentConfigReq.Unmarshal(m, b)
}
func (m *GetAllFellowPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllFellowPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetAllFellowPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllFellowPresentConfigReq.Merge(dst, src)
}
func (m *GetAllFellowPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetAllFellowPresentConfigReq.Size(m)
}
func (m *GetAllFellowPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllFellowPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllFellowPresentConfigReq proto.InternalMessageInfo

type GetAllFellowPresentConfigResp struct {
	Config               []*FellowPresentConfig `protobuf:"bytes,1,rep,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetAllFellowPresentConfigResp) Reset()         { *m = GetAllFellowPresentConfigResp{} }
func (m *GetAllFellowPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetAllFellowPresentConfigResp) ProtoMessage()    {}
func (*GetAllFellowPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{52}
}
func (m *GetAllFellowPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllFellowPresentConfigResp.Unmarshal(m, b)
}
func (m *GetAllFellowPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllFellowPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetAllFellowPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllFellowPresentConfigResp.Merge(dst, src)
}
func (m *GetAllFellowPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetAllFellowPresentConfigResp.Size(m)
}
func (m *GetAllFellowPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllFellowPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllFellowPresentConfigResp proto.InternalMessageInfo

func (m *GetAllFellowPresentConfigResp) GetConfig() []*FellowPresentConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type UnlockFellowSiteReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnlockFellowSiteReq) Reset()         { *m = UnlockFellowSiteReq{} }
func (m *UnlockFellowSiteReq) String() string { return proto.CompactTextString(m) }
func (*UnlockFellowSiteReq) ProtoMessage()    {}
func (*UnlockFellowSiteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{53}
}
func (m *UnlockFellowSiteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnlockFellowSiteReq.Unmarshal(m, b)
}
func (m *UnlockFellowSiteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnlockFellowSiteReq.Marshal(b, m, deterministic)
}
func (dst *UnlockFellowSiteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnlockFellowSiteReq.Merge(dst, src)
}
func (m *UnlockFellowSiteReq) XXX_Size() int {
	return xxx_messageInfo_UnlockFellowSiteReq.Size(m)
}
func (m *UnlockFellowSiteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnlockFellowSiteReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnlockFellowSiteReq proto.InternalMessageInfo

func (m *UnlockFellowSiteReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type UnlockFellowSiteResp struct {
	Remain               int64    `protobuf:"varint,1,opt,name=remain,proto3" json:"remain,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnlockFellowSiteResp) Reset()         { *m = UnlockFellowSiteResp{} }
func (m *UnlockFellowSiteResp) String() string { return proto.CompactTextString(m) }
func (*UnlockFellowSiteResp) ProtoMessage()    {}
func (*UnlockFellowSiteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{54}
}
func (m *UnlockFellowSiteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnlockFellowSiteResp.Unmarshal(m, b)
}
func (m *UnlockFellowSiteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnlockFellowSiteResp.Marshal(b, m, deterministic)
}
func (dst *UnlockFellowSiteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnlockFellowSiteResp.Merge(dst, src)
}
func (m *UnlockFellowSiteResp) XXX_Size() int {
	return xxx_messageInfo_UnlockFellowSiteResp.Size(m)
}
func (m *UnlockFellowSiteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnlockFellowSiteResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnlockFellowSiteResp proto.InternalMessageInfo

func (m *UnlockFellowSiteResp) GetRemain() int64 {
	if m != nil {
		return m.Remain
	}
	return 0
}

type UnboundFellowReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnboundFellowReq) Reset()         { *m = UnboundFellowReq{} }
func (m *UnboundFellowReq) String() string { return proto.CompactTextString(m) }
func (*UnboundFellowReq) ProtoMessage()    {}
func (*UnboundFellowReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{55}
}
func (m *UnboundFellowReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnboundFellowReq.Unmarshal(m, b)
}
func (m *UnboundFellowReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnboundFellowReq.Marshal(b, m, deterministic)
}
func (dst *UnboundFellowReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnboundFellowReq.Merge(dst, src)
}
func (m *UnboundFellowReq) XXX_Size() int {
	return xxx_messageInfo_UnboundFellowReq.Size(m)
}
func (m *UnboundFellowReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnboundFellowReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnboundFellowReq proto.InternalMessageInfo

func (m *UnboundFellowReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UnboundFellowReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type UnboundFellowResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnboundFellowResp) Reset()         { *m = UnboundFellowResp{} }
func (m *UnboundFellowResp) String() string { return proto.CompactTextString(m) }
func (*UnboundFellowResp) ProtoMessage()    {}
func (*UnboundFellowResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{56}
}
func (m *UnboundFellowResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnboundFellowResp.Unmarshal(m, b)
}
func (m *UnboundFellowResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnboundFellowResp.Marshal(b, m, deterministic)
}
func (dst *UnboundFellowResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnboundFellowResp.Merge(dst, src)
}
func (m *UnboundFellowResp) XXX_Size() int {
	return xxx_messageInfo_UnboundFellowResp.Size(m)
}
func (m *UnboundFellowResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnboundFellowResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnboundFellowResp proto.InternalMessageInfo

type CancelUnboundFellowReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelUnboundFellowReq) Reset()         { *m = CancelUnboundFellowReq{} }
func (m *CancelUnboundFellowReq) String() string { return proto.CompactTextString(m) }
func (*CancelUnboundFellowReq) ProtoMessage()    {}
func (*CancelUnboundFellowReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{57}
}
func (m *CancelUnboundFellowReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelUnboundFellowReq.Unmarshal(m, b)
}
func (m *CancelUnboundFellowReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelUnboundFellowReq.Marshal(b, m, deterministic)
}
func (dst *CancelUnboundFellowReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelUnboundFellowReq.Merge(dst, src)
}
func (m *CancelUnboundFellowReq) XXX_Size() int {
	return xxx_messageInfo_CancelUnboundFellowReq.Size(m)
}
func (m *CancelUnboundFellowReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelUnboundFellowReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelUnboundFellowReq proto.InternalMessageInfo

func (m *CancelUnboundFellowReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CancelUnboundFellowReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type CancelUnboundFellowResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelUnboundFellowResp) Reset()         { *m = CancelUnboundFellowResp{} }
func (m *CancelUnboundFellowResp) String() string { return proto.CompactTextString(m) }
func (*CancelUnboundFellowResp) ProtoMessage()    {}
func (*CancelUnboundFellowResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{58}
}
func (m *CancelUnboundFellowResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelUnboundFellowResp.Unmarshal(m, b)
}
func (m *CancelUnboundFellowResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelUnboundFellowResp.Marshal(b, m, deterministic)
}
func (dst *CancelUnboundFellowResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelUnboundFellowResp.Merge(dst, src)
}
func (m *CancelUnboundFellowResp) XXX_Size() int {
	return xxx_messageInfo_CancelUnboundFellowResp.Size(m)
}
func (m *CancelUnboundFellowResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelUnboundFellowResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelUnboundFellowResp proto.InternalMessageInfo

type ChangeFellowBindTypeReq struct {
	OpUid                uint32   `protobuf:"varint,1,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	FromBindType         uint32   `protobuf:"varint,3,opt,name=from_bind_type,json=fromBindType,proto3" json:"from_bind_type,omitempty"`
	FromFellowType       uint32   `protobuf:"varint,4,opt,name=from_fellow_type,json=fromFellowType,proto3" json:"from_fellow_type,omitempty"`
	ToBindType           uint32   `protobuf:"varint,5,opt,name=to_bind_type,json=toBindType,proto3" json:"to_bind_type,omitempty"`
	ToFellowType         uint32   `protobuf:"varint,6,opt,name=to_fellow_type,json=toFellowType,proto3" json:"to_fellow_type,omitempty"`
	PresentId            uint32   `protobuf:"varint,7,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	WithUnlock           bool     `protobuf:"varint,8,opt,name=with_unlock,json=withUnlock,proto3" json:"with_unlock,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeFellowBindTypeReq) Reset()         { *m = ChangeFellowBindTypeReq{} }
func (m *ChangeFellowBindTypeReq) String() string { return proto.CompactTextString(m) }
func (*ChangeFellowBindTypeReq) ProtoMessage()    {}
func (*ChangeFellowBindTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{59}
}
func (m *ChangeFellowBindTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeFellowBindTypeReq.Unmarshal(m, b)
}
func (m *ChangeFellowBindTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeFellowBindTypeReq.Marshal(b, m, deterministic)
}
func (dst *ChangeFellowBindTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeFellowBindTypeReq.Merge(dst, src)
}
func (m *ChangeFellowBindTypeReq) XXX_Size() int {
	return xxx_messageInfo_ChangeFellowBindTypeReq.Size(m)
}
func (m *ChangeFellowBindTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeFellowBindTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeFellowBindTypeReq proto.InternalMessageInfo

func (m *ChangeFellowBindTypeReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *ChangeFellowBindTypeReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *ChangeFellowBindTypeReq) GetFromBindType() uint32 {
	if m != nil {
		return m.FromBindType
	}
	return 0
}

func (m *ChangeFellowBindTypeReq) GetFromFellowType() uint32 {
	if m != nil {
		return m.FromFellowType
	}
	return 0
}

func (m *ChangeFellowBindTypeReq) GetToBindType() uint32 {
	if m != nil {
		return m.ToBindType
	}
	return 0
}

func (m *ChangeFellowBindTypeReq) GetToFellowType() uint32 {
	if m != nil {
		return m.ToFellowType
	}
	return 0
}

func (m *ChangeFellowBindTypeReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *ChangeFellowBindTypeReq) GetWithUnlock() bool {
	if m != nil {
		return m.WithUnlock
	}
	return false
}

type ChangeFellowBindTypeResp struct {
	RemainTbean          int64    `protobuf:"varint,1,opt,name=remain_tbean,json=remainTbean,proto3" json:"remain_tbean,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeFellowBindTypeResp) Reset()         { *m = ChangeFellowBindTypeResp{} }
func (m *ChangeFellowBindTypeResp) String() string { return proto.CompactTextString(m) }
func (*ChangeFellowBindTypeResp) ProtoMessage()    {}
func (*ChangeFellowBindTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{60}
}
func (m *ChangeFellowBindTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeFellowBindTypeResp.Unmarshal(m, b)
}
func (m *ChangeFellowBindTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeFellowBindTypeResp.Marshal(b, m, deterministic)
}
func (dst *ChangeFellowBindTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeFellowBindTypeResp.Merge(dst, src)
}
func (m *ChangeFellowBindTypeResp) XXX_Size() int {
	return xxx_messageInfo_ChangeFellowBindTypeResp.Size(m)
}
func (m *ChangeFellowBindTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeFellowBindTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeFellowBindTypeResp proto.InternalMessageInfo

func (m *ChangeFellowBindTypeResp) GetRemainTbean() int64 {
	if m != nil {
		return m.RemainTbean
	}
	return 0
}

type SendFellowPresentReq struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32           `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	GiftId               uint32           `protobuf:"varint,3,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	MarketId             uint32           `protobuf:"varint,4,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	AppId                uint32           `protobuf:"varint,5,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	ServiceInfo          *ServiceCtrlInfo `protobuf:"bytes,6,opt,name=service_info,json=serviceInfo,proto3" json:"service_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SendFellowPresentReq) Reset()         { *m = SendFellowPresentReq{} }
func (m *SendFellowPresentReq) String() string { return proto.CompactTextString(m) }
func (*SendFellowPresentReq) ProtoMessage()    {}
func (*SendFellowPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{61}
}
func (m *SendFellowPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendFellowPresentReq.Unmarshal(m, b)
}
func (m *SendFellowPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendFellowPresentReq.Marshal(b, m, deterministic)
}
func (dst *SendFellowPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendFellowPresentReq.Merge(dst, src)
}
func (m *SendFellowPresentReq) XXX_Size() int {
	return xxx_messageInfo_SendFellowPresentReq.Size(m)
}
func (m *SendFellowPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendFellowPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendFellowPresentReq proto.InternalMessageInfo

func (m *SendFellowPresentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendFellowPresentReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SendFellowPresentReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *SendFellowPresentReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *SendFellowPresentReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *SendFellowPresentReq) GetServiceInfo() *ServiceCtrlInfo {
	if m != nil {
		return m.ServiceInfo
	}
	return nil
}

type SendFellowPresentResp struct {
	RemainCurrency       int64                      `protobuf:"varint,1,opt,name=remain_currency,json=remainCurrency,proto3" json:"remain_currency,omitempty"`
	RemainTbean          int64                      `protobuf:"varint,2,opt,name=remain_tbean,json=remainTbean,proto3" json:"remain_tbean,omitempty"`
	PresentInfo          *PresentSendItemSimpleInfo `protobuf:"bytes,3,opt,name=present_info,json=presentInfo,proto3" json:"present_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *SendFellowPresentResp) Reset()         { *m = SendFellowPresentResp{} }
func (m *SendFellowPresentResp) String() string { return proto.CompactTextString(m) }
func (*SendFellowPresentResp) ProtoMessage()    {}
func (*SendFellowPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{62}
}
func (m *SendFellowPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendFellowPresentResp.Unmarshal(m, b)
}
func (m *SendFellowPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendFellowPresentResp.Marshal(b, m, deterministic)
}
func (dst *SendFellowPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendFellowPresentResp.Merge(dst, src)
}
func (m *SendFellowPresentResp) XXX_Size() int {
	return xxx_messageInfo_SendFellowPresentResp.Size(m)
}
func (m *SendFellowPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendFellowPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendFellowPresentResp proto.InternalMessageInfo

func (m *SendFellowPresentResp) GetRemainCurrency() int64 {
	if m != nil {
		return m.RemainCurrency
	}
	return 0
}

func (m *SendFellowPresentResp) GetRemainTbean() int64 {
	if m != nil {
		return m.RemainTbean
	}
	return 0
}

func (m *SendFellowPresentResp) GetPresentInfo() *PresentSendItemSimpleInfo {
	if m != nil {
		return m.PresentInfo
	}
	return nil
}

type PresentSendItemSimpleInfo struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	ShowEffect           uint32   `protobuf:"varint,3,opt,name=show_effect,json=showEffect,proto3" json:"show_effect,omitempty"`
	ShowEffectV2         uint32   `protobuf:"varint,4,opt,name=show_effect_v2,json=showEffectV2,proto3" json:"show_effect_v2,omitempty"`
	PresentIcon          string   `protobuf:"bytes,5,opt,name=present_icon,json=presentIcon,proto3" json:"present_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentSendItemSimpleInfo) Reset()         { *m = PresentSendItemSimpleInfo{} }
func (m *PresentSendItemSimpleInfo) String() string { return proto.CompactTextString(m) }
func (*PresentSendItemSimpleInfo) ProtoMessage()    {}
func (*PresentSendItemSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{63}
}
func (m *PresentSendItemSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentSendItemSimpleInfo.Unmarshal(m, b)
}
func (m *PresentSendItemSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentSendItemSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *PresentSendItemSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentSendItemSimpleInfo.Merge(dst, src)
}
func (m *PresentSendItemSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_PresentSendItemSimpleInfo.Size(m)
}
func (m *PresentSendItemSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentSendItemSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentSendItemSimpleInfo proto.InternalMessageInfo

func (m *PresentSendItemSimpleInfo) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentSendItemSimpleInfo) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *PresentSendItemSimpleInfo) GetShowEffect() uint32 {
	if m != nil {
		return m.ShowEffect
	}
	return 0
}

func (m *PresentSendItemSimpleInfo) GetShowEffectV2() uint32 {
	if m != nil {
		return m.ShowEffectV2
	}
	return 0
}

func (m *PresentSendItemSimpleInfo) GetPresentIcon() string {
	if m != nil {
		return m.PresentIcon
	}
	return ""
}

type GetFellowPresentDetailReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFellowPresentDetailReq) Reset()         { *m = GetFellowPresentDetailReq{} }
func (m *GetFellowPresentDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowPresentDetailReq) ProtoMessage()    {}
func (*GetFellowPresentDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{64}
}
func (m *GetFellowPresentDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowPresentDetailReq.Unmarshal(m, b)
}
func (m *GetFellowPresentDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowPresentDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowPresentDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowPresentDetailReq.Merge(dst, src)
}
func (m *GetFellowPresentDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowPresentDetailReq.Size(m)
}
func (m *GetFellowPresentDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowPresentDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowPresentDetailReq proto.InternalMessageInfo

func (m *GetFellowPresentDetailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFellowPresentDetailReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetFellowPresentDetailResp struct {
	PresentList          []*FellowPresentInfo `protobuf:"bytes,1,rep,name=present_list,json=presentList,proto3" json:"present_list,omitempty"`
	FellowPresent        *FellowPresentInfo   `protobuf:"bytes,2,opt,name=fellow_present,json=fellowPresent,proto3" json:"fellow_present,omitempty"`
	FellowLevel          uint32               `protobuf:"varint,3,opt,name=fellow_level,json=fellowLevel,proto3" json:"fellow_level,omitempty"`
	FellowName           string               `protobuf:"bytes,4,opt,name=fellow_name,json=fellowName,proto3" json:"fellow_name,omitempty"`
	DayCnt               uint32               `protobuf:"varint,5,opt,name=day_cnt,json=dayCnt,proto3" json:"day_cnt,omitempty"`
	BindType             uint32               `protobuf:"varint,6,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetFellowPresentDetailResp) Reset()         { *m = GetFellowPresentDetailResp{} }
func (m *GetFellowPresentDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowPresentDetailResp) ProtoMessage()    {}
func (*GetFellowPresentDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{65}
}
func (m *GetFellowPresentDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowPresentDetailResp.Unmarshal(m, b)
}
func (m *GetFellowPresentDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowPresentDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowPresentDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowPresentDetailResp.Merge(dst, src)
}
func (m *GetFellowPresentDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowPresentDetailResp.Size(m)
}
func (m *GetFellowPresentDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowPresentDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowPresentDetailResp proto.InternalMessageInfo

func (m *GetFellowPresentDetailResp) GetPresentList() []*FellowPresentInfo {
	if m != nil {
		return m.PresentList
	}
	return nil
}

func (m *GetFellowPresentDetailResp) GetFellowPresent() *FellowPresentInfo {
	if m != nil {
		return m.FellowPresent
	}
	return nil
}

func (m *GetFellowPresentDetailResp) GetFellowLevel() uint32 {
	if m != nil {
		return m.FellowLevel
	}
	return 0
}

func (m *GetFellowPresentDetailResp) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

func (m *GetFellowPresentDetailResp) GetDayCnt() uint32 {
	if m != nil {
		return m.DayCnt
	}
	return 0
}

func (m *GetFellowPresentDetailResp) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

type GetFellowTypeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFellowTypeReq) Reset()         { *m = GetFellowTypeReq{} }
func (m *GetFellowTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowTypeReq) ProtoMessage()    {}
func (*GetFellowTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{66}
}
func (m *GetFellowTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowTypeReq.Unmarshal(m, b)
}
func (m *GetFellowTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowTypeReq.Merge(dst, src)
}
func (m *GetFellowTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowTypeReq.Size(m)
}
func (m *GetFellowTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowTypeReq proto.InternalMessageInfo

func (m *GetFellowTypeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFellowTypeReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetFellowTypeResp struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	FellowType           uint32   `protobuf:"varint,3,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	FellowTypeStr        string   `protobuf:"bytes,4,opt,name=fellow_type_str,json=fellowTypeStr,proto3" json:"fellow_type_str,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFellowTypeResp) Reset()         { *m = GetFellowTypeResp{} }
func (m *GetFellowTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowTypeResp) ProtoMessage()    {}
func (*GetFellowTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{67}
}
func (m *GetFellowTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowTypeResp.Unmarshal(m, b)
}
func (m *GetFellowTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowTypeResp.Merge(dst, src)
}
func (m *GetFellowTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowTypeResp.Size(m)
}
func (m *GetFellowTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowTypeResp proto.InternalMessageInfo

func (m *GetFellowTypeResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFellowTypeResp) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetFellowTypeResp) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *GetFellowTypeResp) GetFellowTypeStr() string {
	if m != nil {
		return m.FellowTypeStr
	}
	return ""
}

type GetHistoryFellowTypeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	UpdateTime           string   `protobuf:"bytes,3,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHistoryFellowTypeReq) Reset()         { *m = GetHistoryFellowTypeReq{} }
func (m *GetHistoryFellowTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetHistoryFellowTypeReq) ProtoMessage()    {}
func (*GetHistoryFellowTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{68}
}
func (m *GetHistoryFellowTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHistoryFellowTypeReq.Unmarshal(m, b)
}
func (m *GetHistoryFellowTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHistoryFellowTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetHistoryFellowTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHistoryFellowTypeReq.Merge(dst, src)
}
func (m *GetHistoryFellowTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetHistoryFellowTypeReq.Size(m)
}
func (m *GetHistoryFellowTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHistoryFellowTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHistoryFellowTypeReq proto.InternalMessageInfo

func (m *GetHistoryFellowTypeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetHistoryFellowTypeReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetHistoryFellowTypeReq) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

type GetHistoryFellowTypeResp struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	FellowType           uint32   `protobuf:"varint,3,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	FellowTypeStr        string   `protobuf:"bytes,4,opt,name=fellow_type_str,json=fellowTypeStr,proto3" json:"fellow_type_str,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHistoryFellowTypeResp) Reset()         { *m = GetHistoryFellowTypeResp{} }
func (m *GetHistoryFellowTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetHistoryFellowTypeResp) ProtoMessage()    {}
func (*GetHistoryFellowTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{69}
}
func (m *GetHistoryFellowTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHistoryFellowTypeResp.Unmarshal(m, b)
}
func (m *GetHistoryFellowTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHistoryFellowTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetHistoryFellowTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHistoryFellowTypeResp.Merge(dst, src)
}
func (m *GetHistoryFellowTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetHistoryFellowTypeResp.Size(m)
}
func (m *GetHistoryFellowTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHistoryFellowTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHistoryFellowTypeResp proto.InternalMessageInfo

func (m *GetHistoryFellowTypeResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetHistoryFellowTypeResp) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetHistoryFellowTypeResp) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *GetHistoryFellowTypeResp) GetFellowTypeStr() string {
	if m != nil {
		return m.FellowTypeStr
	}
	return ""
}

type GetFellowInfoByUidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFellowInfoByUidReq) Reset()         { *m = GetFellowInfoByUidReq{} }
func (m *GetFellowInfoByUidReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowInfoByUidReq) ProtoMessage()    {}
func (*GetFellowInfoByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{70}
}
func (m *GetFellowInfoByUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowInfoByUidReq.Unmarshal(m, b)
}
func (m *GetFellowInfoByUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowInfoByUidReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowInfoByUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowInfoByUidReq.Merge(dst, src)
}
func (m *GetFellowInfoByUidReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowInfoByUidReq.Size(m)
}
func (m *GetFellowInfoByUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowInfoByUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowInfoByUidReq proto.InternalMessageInfo

func (m *GetFellowInfoByUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFellowInfoByUidReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetFellowInfoByUidResp struct {
	FellowInfo           *FellowInfo `protobuf:"bytes,1,opt,name=fellow_info,json=fellowInfo,proto3" json:"fellow_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetFellowInfoByUidResp) Reset()         { *m = GetFellowInfoByUidResp{} }
func (m *GetFellowInfoByUidResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowInfoByUidResp) ProtoMessage()    {}
func (*GetFellowInfoByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{71}
}
func (m *GetFellowInfoByUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowInfoByUidResp.Unmarshal(m, b)
}
func (m *GetFellowInfoByUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowInfoByUidResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowInfoByUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowInfoByUidResp.Merge(dst, src)
}
func (m *GetFellowInfoByUidResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowInfoByUidResp.Size(m)
}
func (m *GetFellowInfoByUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowInfoByUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowInfoByUidResp proto.InternalMessageInfo

func (m *GetFellowInfoByUidResp) GetFellowInfo() *FellowInfo {
	if m != nil {
		return m.FellowInfo
	}
	return nil
}

type ChannelSendFellowPresentReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	PresentId            uint32   `protobuf:"varint,3,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelSendFellowPresentReq) Reset()         { *m = ChannelSendFellowPresentReq{} }
func (m *ChannelSendFellowPresentReq) String() string { return proto.CompactTextString(m) }
func (*ChannelSendFellowPresentReq) ProtoMessage()    {}
func (*ChannelSendFellowPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{72}
}
func (m *ChannelSendFellowPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSendFellowPresentReq.Unmarshal(m, b)
}
func (m *ChannelSendFellowPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSendFellowPresentReq.Marshal(b, m, deterministic)
}
func (dst *ChannelSendFellowPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSendFellowPresentReq.Merge(dst, src)
}
func (m *ChannelSendFellowPresentReq) XXX_Size() int {
	return xxx_messageInfo_ChannelSendFellowPresentReq.Size(m)
}
func (m *ChannelSendFellowPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSendFellowPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSendFellowPresentReq proto.InternalMessageInfo

func (m *ChannelSendFellowPresentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelSendFellowPresentReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *ChannelSendFellowPresentReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *ChannelSendFellowPresentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ChannelSendFellowPresentResp struct {
	RemainCurrency       int64    `protobuf:"varint,1,opt,name=remain_currency,json=remainCurrency,proto3" json:"remain_currency,omitempty"`
	RemainTbean          int64    `protobuf:"varint,2,opt,name=remain_tbean,json=remainTbean,proto3" json:"remain_tbean,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelSendFellowPresentResp) Reset()         { *m = ChannelSendFellowPresentResp{} }
func (m *ChannelSendFellowPresentResp) String() string { return proto.CompactTextString(m) }
func (*ChannelSendFellowPresentResp) ProtoMessage()    {}
func (*ChannelSendFellowPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{73}
}
func (m *ChannelSendFellowPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSendFellowPresentResp.Unmarshal(m, b)
}
func (m *ChannelSendFellowPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSendFellowPresentResp.Marshal(b, m, deterministic)
}
func (dst *ChannelSendFellowPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSendFellowPresentResp.Merge(dst, src)
}
func (m *ChannelSendFellowPresentResp) XXX_Size() int {
	return xxx_messageInfo_ChannelSendFellowPresentResp.Size(m)
}
func (m *ChannelSendFellowPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSendFellowPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSendFellowPresentResp proto.InternalMessageInfo

func (m *ChannelSendFellowPresentResp) GetRemainCurrency() int64 {
	if m != nil {
		return m.RemainCurrency
	}
	return 0
}

func (m *ChannelSendFellowPresentResp) GetRemainTbean() int64 {
	if m != nil {
		return m.RemainTbean
	}
	return 0
}

type SendChannelFellowInviteReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	BindType             uint32   `protobuf:"varint,3,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	PresentId            uint32   `protobuf:"varint,4,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	FellowType           uint32   `protobuf:"varint,5,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	WithUnlock           bool     `protobuf:"varint,6,opt,name=with_unlock,json=withUnlock,proto3" json:"with_unlock,omitempty"`
	ChannelId            uint32   `protobuf:"varint,7,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendChannelFellowInviteReq) Reset()         { *m = SendChannelFellowInviteReq{} }
func (m *SendChannelFellowInviteReq) String() string { return proto.CompactTextString(m) }
func (*SendChannelFellowInviteReq) ProtoMessage()    {}
func (*SendChannelFellowInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{74}
}
func (m *SendChannelFellowInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendChannelFellowInviteReq.Unmarshal(m, b)
}
func (m *SendChannelFellowInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendChannelFellowInviteReq.Marshal(b, m, deterministic)
}
func (dst *SendChannelFellowInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendChannelFellowInviteReq.Merge(dst, src)
}
func (m *SendChannelFellowInviteReq) XXX_Size() int {
	return xxx_messageInfo_SendChannelFellowInviteReq.Size(m)
}
func (m *SendChannelFellowInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendChannelFellowInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendChannelFellowInviteReq proto.InternalMessageInfo

func (m *SendChannelFellowInviteReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendChannelFellowInviteReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SendChannelFellowInviteReq) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *SendChannelFellowInviteReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *SendChannelFellowInviteReq) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *SendChannelFellowInviteReq) GetWithUnlock() bool {
	if m != nil {
		return m.WithUnlock
	}
	return false
}

func (m *SendChannelFellowInviteReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type SendChannelFellowInviteResp struct {
	RemainCurrency       int64    `protobuf:"varint,1,opt,name=remain_currency,json=remainCurrency,proto3" json:"remain_currency,omitempty"`
	RemainTbean          int64    `protobuf:"varint,2,opt,name=remain_tbean,json=remainTbean,proto3" json:"remain_tbean,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendChannelFellowInviteResp) Reset()         { *m = SendChannelFellowInviteResp{} }
func (m *SendChannelFellowInviteResp) String() string { return proto.CompactTextString(m) }
func (*SendChannelFellowInviteResp) ProtoMessage()    {}
func (*SendChannelFellowInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{75}
}
func (m *SendChannelFellowInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendChannelFellowInviteResp.Unmarshal(m, b)
}
func (m *SendChannelFellowInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendChannelFellowInviteResp.Marshal(b, m, deterministic)
}
func (dst *SendChannelFellowInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendChannelFellowInviteResp.Merge(dst, src)
}
func (m *SendChannelFellowInviteResp) XXX_Size() int {
	return xxx_messageInfo_SendChannelFellowInviteResp.Size(m)
}
func (m *SendChannelFellowInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendChannelFellowInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendChannelFellowInviteResp proto.InternalMessageInfo

func (m *SendChannelFellowInviteResp) GetRemainCurrency() int64 {
	if m != nil {
		return m.RemainCurrency
	}
	return 0
}

func (m *SendChannelFellowInviteResp) GetRemainTbean() int64 {
	if m != nil {
		return m.RemainTbean
	}
	return 0
}

type HandleChannelFellowInviteReq struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	InviteId             string           `protobuf:"bytes,2,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	IsAcceptInvite       bool             `protobuf:"varint,3,opt,name=is_accept_invite,json=isAcceptInvite,proto3" json:"is_accept_invite,omitempty"`
	ChannelId            uint32           `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MarketId             uint32           `protobuf:"varint,5,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	AppId                uint32           `protobuf:"varint,6,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	ServiceInfo          *ServiceCtrlInfo `protobuf:"bytes,7,opt,name=service_info,json=serviceInfo,proto3" json:"service_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *HandleChannelFellowInviteReq) Reset()         { *m = HandleChannelFellowInviteReq{} }
func (m *HandleChannelFellowInviteReq) String() string { return proto.CompactTextString(m) }
func (*HandleChannelFellowInviteReq) ProtoMessage()    {}
func (*HandleChannelFellowInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{76}
}
func (m *HandleChannelFellowInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleChannelFellowInviteReq.Unmarshal(m, b)
}
func (m *HandleChannelFellowInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleChannelFellowInviteReq.Marshal(b, m, deterministic)
}
func (dst *HandleChannelFellowInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleChannelFellowInviteReq.Merge(dst, src)
}
func (m *HandleChannelFellowInviteReq) XXX_Size() int {
	return xxx_messageInfo_HandleChannelFellowInviteReq.Size(m)
}
func (m *HandleChannelFellowInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleChannelFellowInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_HandleChannelFellowInviteReq proto.InternalMessageInfo

func (m *HandleChannelFellowInviteReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HandleChannelFellowInviteReq) GetInviteId() string {
	if m != nil {
		return m.InviteId
	}
	return ""
}

func (m *HandleChannelFellowInviteReq) GetIsAcceptInvite() bool {
	if m != nil {
		return m.IsAcceptInvite
	}
	return false
}

func (m *HandleChannelFellowInviteReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *HandleChannelFellowInviteReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *HandleChannelFellowInviteReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *HandleChannelFellowInviteReq) GetServiceInfo() *ServiceCtrlInfo {
	if m != nil {
		return m.ServiceInfo
	}
	return nil
}

type HandleChannelFellowInviteResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleChannelFellowInviteResp) Reset()         { *m = HandleChannelFellowInviteResp{} }
func (m *HandleChannelFellowInviteResp) String() string { return proto.CompactTextString(m) }
func (*HandleChannelFellowInviteResp) ProtoMessage()    {}
func (*HandleChannelFellowInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{77}
}
func (m *HandleChannelFellowInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleChannelFellowInviteResp.Unmarshal(m, b)
}
func (m *HandleChannelFellowInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleChannelFellowInviteResp.Marshal(b, m, deterministic)
}
func (dst *HandleChannelFellowInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleChannelFellowInviteResp.Merge(dst, src)
}
func (m *HandleChannelFellowInviteResp) XXX_Size() int {
	return xxx_messageInfo_HandleChannelFellowInviteResp.Size(m)
}
func (m *HandleChannelFellowInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleChannelFellowInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_HandleChannelFellowInviteResp proto.InternalMessageInfo

// 邀请函信息
type ChannelFellowMsg struct {
	FromUid              int64              `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	FromAccount          string             `protobuf:"bytes,2,opt,name=from_account,json=fromAccount,proto3" json:"from_account,omitempty"`
	FromNickname         string             `protobuf:"bytes,3,opt,name=from_nickname,json=fromNickname,proto3" json:"from_nickname,omitempty"`
	BindType             uint32             `protobuf:"varint,4,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	FellowType           uint32             `protobuf:"varint,5,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	FellowName           string             `protobuf:"bytes,6,opt,name=fellow_name,json=fellowName,proto3" json:"fellow_name,omitempty"`
	ToUid                int64              `protobuf:"varint,7,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ToAccount            string             `protobuf:"bytes,8,opt,name=to_account,json=toAccount,proto3" json:"to_account,omitempty"`
	ToNickname           string             `protobuf:"bytes,9,opt,name=to_nickname,json=toNickname,proto3" json:"to_nickname,omitempty"`
	FromSex              uint32             `protobuf:"varint,10,opt,name=from_sex,json=fromSex,proto3" json:"from_sex,omitempty"`
	ToSex                uint32             `protobuf:"varint,11,opt,name=to_sex,json=toSex,proto3" json:"to_sex,omitempty"`
	PresentInfo          *FellowPresentInfo `protobuf:"bytes,12,opt,name=present_info,json=presentInfo,proto3" json:"present_info,omitempty"`
	Status               uint32             `protobuf:"varint,13,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ChannelFellowMsg) Reset()         { *m = ChannelFellowMsg{} }
func (m *ChannelFellowMsg) String() string { return proto.CompactTextString(m) }
func (*ChannelFellowMsg) ProtoMessage()    {}
func (*ChannelFellowMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{78}
}
func (m *ChannelFellowMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelFellowMsg.Unmarshal(m, b)
}
func (m *ChannelFellowMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelFellowMsg.Marshal(b, m, deterministic)
}
func (dst *ChannelFellowMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelFellowMsg.Merge(dst, src)
}
func (m *ChannelFellowMsg) XXX_Size() int {
	return xxx_messageInfo_ChannelFellowMsg.Size(m)
}
func (m *ChannelFellowMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelFellowMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelFellowMsg proto.InternalMessageInfo

func (m *ChannelFellowMsg) GetFromUid() int64 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *ChannelFellowMsg) GetFromAccount() string {
	if m != nil {
		return m.FromAccount
	}
	return ""
}

func (m *ChannelFellowMsg) GetFromNickname() string {
	if m != nil {
		return m.FromNickname
	}
	return ""
}

func (m *ChannelFellowMsg) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *ChannelFellowMsg) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *ChannelFellowMsg) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

func (m *ChannelFellowMsg) GetToUid() int64 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *ChannelFellowMsg) GetToAccount() string {
	if m != nil {
		return m.ToAccount
	}
	return ""
}

func (m *ChannelFellowMsg) GetToNickname() string {
	if m != nil {
		return m.ToNickname
	}
	return ""
}

func (m *ChannelFellowMsg) GetFromSex() uint32 {
	if m != nil {
		return m.FromSex
	}
	return 0
}

func (m *ChannelFellowMsg) GetToSex() uint32 {
	if m != nil {
		return m.ToSex
	}
	return 0
}

func (m *ChannelFellowMsg) GetPresentInfo() *FellowPresentInfo {
	if m != nil {
		return m.PresentInfo
	}
	return nil
}

func (m *ChannelFellowMsg) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type GetRoomFellowListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	NextUid              []uint32 `protobuf:"varint,2,rep,packed,name=next_uid,json=nextUid,proto3" json:"next_uid,omitempty"`
	OnMicUid             []uint32 `protobuf:"varint,3,rep,packed,name=on_mic_uid,json=onMicUid,proto3" json:"on_mic_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRoomFellowListReq) Reset()         { *m = GetRoomFellowListReq{} }
func (m *GetRoomFellowListReq) String() string { return proto.CompactTextString(m) }
func (*GetRoomFellowListReq) ProtoMessage()    {}
func (*GetRoomFellowListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{79}
}
func (m *GetRoomFellowListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoomFellowListReq.Unmarshal(m, b)
}
func (m *GetRoomFellowListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoomFellowListReq.Marshal(b, m, deterministic)
}
func (dst *GetRoomFellowListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoomFellowListReq.Merge(dst, src)
}
func (m *GetRoomFellowListReq) XXX_Size() int {
	return xxx_messageInfo_GetRoomFellowListReq.Size(m)
}
func (m *GetRoomFellowListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoomFellowListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoomFellowListReq proto.InternalMessageInfo

func (m *GetRoomFellowListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRoomFellowListReq) GetNextUid() []uint32 {
	if m != nil {
		return m.NextUid
	}
	return nil
}

func (m *GetRoomFellowListReq) GetOnMicUid() []uint32 {
	if m != nil {
		return m.OnMicUid
	}
	return nil
}

type GetRoomFellowListResp struct {
	Uid                  uint32        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FellowList           []*FellowInfo `protobuf:"bytes,2,rep,name=fellow_list,json=fellowList,proto3" json:"fellow_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetRoomFellowListResp) Reset()         { *m = GetRoomFellowListResp{} }
func (m *GetRoomFellowListResp) String() string { return proto.CompactTextString(m) }
func (*GetRoomFellowListResp) ProtoMessage()    {}
func (*GetRoomFellowListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{80}
}
func (m *GetRoomFellowListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoomFellowListResp.Unmarshal(m, b)
}
func (m *GetRoomFellowListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoomFellowListResp.Marshal(b, m, deterministic)
}
func (dst *GetRoomFellowListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoomFellowListResp.Merge(dst, src)
}
func (m *GetRoomFellowListResp) XXX_Size() int {
	return xxx_messageInfo_GetRoomFellowListResp.Size(m)
}
func (m *GetRoomFellowListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoomFellowListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoomFellowListResp proto.InternalMessageInfo

func (m *GetRoomFellowListResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRoomFellowListResp) GetFellowList() []*FellowInfo {
	if m != nil {
		return m.FellowList
	}
	return nil
}

type GetAllChannelFellowInviteReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllChannelFellowInviteReq) Reset()         { *m = GetAllChannelFellowInviteReq{} }
func (m *GetAllChannelFellowInviteReq) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelFellowInviteReq) ProtoMessage()    {}
func (*GetAllChannelFellowInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{81}
}
func (m *GetAllChannelFellowInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllChannelFellowInviteReq.Unmarshal(m, b)
}
func (m *GetAllChannelFellowInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllChannelFellowInviteReq.Marshal(b, m, deterministic)
}
func (dst *GetAllChannelFellowInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllChannelFellowInviteReq.Merge(dst, src)
}
func (m *GetAllChannelFellowInviteReq) XXX_Size() int {
	return xxx_messageInfo_GetAllChannelFellowInviteReq.Size(m)
}
func (m *GetAllChannelFellowInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllChannelFellowInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllChannelFellowInviteReq proto.InternalMessageInfo

func (m *GetAllChannelFellowInviteReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAllChannelFellowInviteReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetAllChannelFellowInviteResp struct {
	SendInviteList       []*FellowInviteInfo `protobuf:"bytes,1,rep,name=send_invite_list,json=sendInviteList,proto3" json:"send_invite_list,omitempty"`
	ReceivedInviteList   []*FellowInviteInfo `protobuf:"bytes,2,rep,name=received_invite_list,json=receivedInviteList,proto3" json:"received_invite_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetAllChannelFellowInviteResp) Reset()         { *m = GetAllChannelFellowInviteResp{} }
func (m *GetAllChannelFellowInviteResp) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelFellowInviteResp) ProtoMessage()    {}
func (*GetAllChannelFellowInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{82}
}
func (m *GetAllChannelFellowInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllChannelFellowInviteResp.Unmarshal(m, b)
}
func (m *GetAllChannelFellowInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllChannelFellowInviteResp.Marshal(b, m, deterministic)
}
func (dst *GetAllChannelFellowInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllChannelFellowInviteResp.Merge(dst, src)
}
func (m *GetAllChannelFellowInviteResp) XXX_Size() int {
	return xxx_messageInfo_GetAllChannelFellowInviteResp.Size(m)
}
func (m *GetAllChannelFellowInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllChannelFellowInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllChannelFellowInviteResp proto.InternalMessageInfo

func (m *GetAllChannelFellowInviteResp) GetSendInviteList() []*FellowInviteInfo {
	if m != nil {
		return m.SendInviteList
	}
	return nil
}

func (m *GetAllChannelFellowInviteResp) GetReceivedInviteList() []*FellowInviteInfo {
	if m != nil {
		return m.ReceivedInviteList
	}
	return nil
}

type DirectUnboundFellowReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DirectUnboundFellowReq) Reset()         { *m = DirectUnboundFellowReq{} }
func (m *DirectUnboundFellowReq) String() string { return proto.CompactTextString(m) }
func (*DirectUnboundFellowReq) ProtoMessage()    {}
func (*DirectUnboundFellowReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{83}
}
func (m *DirectUnboundFellowReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DirectUnboundFellowReq.Unmarshal(m, b)
}
func (m *DirectUnboundFellowReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DirectUnboundFellowReq.Marshal(b, m, deterministic)
}
func (dst *DirectUnboundFellowReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DirectUnboundFellowReq.Merge(dst, src)
}
func (m *DirectUnboundFellowReq) XXX_Size() int {
	return xxx_messageInfo_DirectUnboundFellowReq.Size(m)
}
func (m *DirectUnboundFellowReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DirectUnboundFellowReq.DiscardUnknown(m)
}

var xxx_messageInfo_DirectUnboundFellowReq proto.InternalMessageInfo

func (m *DirectUnboundFellowReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DirectUnboundFellowReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type DirectUnboundFellowResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DirectUnboundFellowResp) Reset()         { *m = DirectUnboundFellowResp{} }
func (m *DirectUnboundFellowResp) String() string { return proto.CompactTextString(m) }
func (*DirectUnboundFellowResp) ProtoMessage()    {}
func (*DirectUnboundFellowResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{84}
}
func (m *DirectUnboundFellowResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DirectUnboundFellowResp.Unmarshal(m, b)
}
func (m *DirectUnboundFellowResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DirectUnboundFellowResp.Marshal(b, m, deterministic)
}
func (dst *DirectUnboundFellowResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DirectUnboundFellowResp.Merge(dst, src)
}
func (m *DirectUnboundFellowResp) XXX_Size() int {
	return xxx_messageInfo_DirectUnboundFellowResp.Size(m)
}
func (m *DirectUnboundFellowResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DirectUnboundFellowResp.DiscardUnknown(m)
}

var xxx_messageInfo_DirectUnboundFellowResp proto.InternalMessageInfo

type GetOnMicFellowListReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOnMicFellowListReq) Reset()         { *m = GetOnMicFellowListReq{} }
func (m *GetOnMicFellowListReq) String() string { return proto.CompactTextString(m) }
func (*GetOnMicFellowListReq) ProtoMessage()    {}
func (*GetOnMicFellowListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{85}
}
func (m *GetOnMicFellowListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOnMicFellowListReq.Unmarshal(m, b)
}
func (m *GetOnMicFellowListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOnMicFellowListReq.Marshal(b, m, deterministic)
}
func (dst *GetOnMicFellowListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOnMicFellowListReq.Merge(dst, src)
}
func (m *GetOnMicFellowListReq) XXX_Size() int {
	return xxx_messageInfo_GetOnMicFellowListReq.Size(m)
}
func (m *GetOnMicFellowListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOnMicFellowListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOnMicFellowListReq proto.InternalMessageInfo

func (m *GetOnMicFellowListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 麦位挚友信息
type MicFellowInfo struct {
	Uid                  uint32    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FellowUid            uint32    `protobuf:"varint,2,opt,name=fellow_uid,json=fellowUid,proto3" json:"fellow_uid,omitempty"`
	FellowLevel          uint32    `protobuf:"varint,3,opt,name=fellow_level,json=fellowLevel,proto3" json:"fellow_level,omitempty"`
	FellowType           uint32    `protobuf:"varint,4,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	BindType             uint32    `protobuf:"varint,5,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	CurrentRare          *RareInfo `protobuf:"bytes,6,opt,name=current_rare,json=currentRare,proto3" json:"current_rare,omitempty"`
	LigatureUrl          string    `protobuf:"bytes,7,opt,name=ligature_url,json=ligatureUrl,proto3" json:"ligature_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *MicFellowInfo) Reset()         { *m = MicFellowInfo{} }
func (m *MicFellowInfo) String() string { return proto.CompactTextString(m) }
func (*MicFellowInfo) ProtoMessage()    {}
func (*MicFellowInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{86}
}
func (m *MicFellowInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicFellowInfo.Unmarshal(m, b)
}
func (m *MicFellowInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicFellowInfo.Marshal(b, m, deterministic)
}
func (dst *MicFellowInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicFellowInfo.Merge(dst, src)
}
func (m *MicFellowInfo) XXX_Size() int {
	return xxx_messageInfo_MicFellowInfo.Size(m)
}
func (m *MicFellowInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MicFellowInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MicFellowInfo proto.InternalMessageInfo

func (m *MicFellowInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MicFellowInfo) GetFellowUid() uint32 {
	if m != nil {
		return m.FellowUid
	}
	return 0
}

func (m *MicFellowInfo) GetFellowLevel() uint32 {
	if m != nil {
		return m.FellowLevel
	}
	return 0
}

func (m *MicFellowInfo) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *MicFellowInfo) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *MicFellowInfo) GetCurrentRare() *RareInfo {
	if m != nil {
		return m.CurrentRare
	}
	return nil
}

func (m *MicFellowInfo) GetLigatureUrl() string {
	if m != nil {
		return m.LigatureUrl
	}
	return ""
}

type MicFellowInfoChangeInfo struct {
	MicFellow            []*MicFellowInfo `protobuf:"bytes,1,rep,name=mic_fellow,json=micFellow,proto3" json:"mic_fellow,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *MicFellowInfoChangeInfo) Reset()         { *m = MicFellowInfoChangeInfo{} }
func (m *MicFellowInfoChangeInfo) String() string { return proto.CompactTextString(m) }
func (*MicFellowInfoChangeInfo) ProtoMessage()    {}
func (*MicFellowInfoChangeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{87}
}
func (m *MicFellowInfoChangeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicFellowInfoChangeInfo.Unmarshal(m, b)
}
func (m *MicFellowInfoChangeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicFellowInfoChangeInfo.Marshal(b, m, deterministic)
}
func (dst *MicFellowInfoChangeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicFellowInfoChangeInfo.Merge(dst, src)
}
func (m *MicFellowInfoChangeInfo) XXX_Size() int {
	return xxx_messageInfo_MicFellowInfoChangeInfo.Size(m)
}
func (m *MicFellowInfoChangeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MicFellowInfoChangeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MicFellowInfoChangeInfo proto.InternalMessageInfo

func (m *MicFellowInfoChangeInfo) GetMicFellow() []*MicFellowInfo {
	if m != nil {
		return m.MicFellow
	}
	return nil
}

type GetOnMicFellowListResp struct {
	ChannelId            uint32           `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MicFellow            []*MicFellowInfo `protobuf:"bytes,2,rep,name=mic_fellow,json=micFellow,proto3" json:"mic_fellow,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetOnMicFellowListResp) Reset()         { *m = GetOnMicFellowListResp{} }
func (m *GetOnMicFellowListResp) String() string { return proto.CompactTextString(m) }
func (*GetOnMicFellowListResp) ProtoMessage()    {}
func (*GetOnMicFellowListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{88}
}
func (m *GetOnMicFellowListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOnMicFellowListResp.Unmarshal(m, b)
}
func (m *GetOnMicFellowListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOnMicFellowListResp.Marshal(b, m, deterministic)
}
func (dst *GetOnMicFellowListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOnMicFellowListResp.Merge(dst, src)
}
func (m *GetOnMicFellowListResp) XXX_Size() int {
	return xxx_messageInfo_GetOnMicFellowListResp.Size(m)
}
func (m *GetOnMicFellowListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOnMicFellowListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOnMicFellowListResp proto.InternalMessageInfo

func (m *GetOnMicFellowListResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetOnMicFellowListResp) GetMicFellow() []*MicFellowInfo {
	if m != nil {
		return m.MicFellow
	}
	return nil
}

type GetChannelFellowCandidateInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ItemId               uint32   `protobuf:"varint,3,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelFellowCandidateInfoReq) Reset()         { *m = GetChannelFellowCandidateInfoReq{} }
func (m *GetChannelFellowCandidateInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelFellowCandidateInfoReq) ProtoMessage()    {}
func (*GetChannelFellowCandidateInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{89}
}
func (m *GetChannelFellowCandidateInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelFellowCandidateInfoReq.Unmarshal(m, b)
}
func (m *GetChannelFellowCandidateInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelFellowCandidateInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelFellowCandidateInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelFellowCandidateInfoReq.Merge(dst, src)
}
func (m *GetChannelFellowCandidateInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelFellowCandidateInfoReq.Size(m)
}
func (m *GetChannelFellowCandidateInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelFellowCandidateInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelFellowCandidateInfoReq proto.InternalMessageInfo

func (m *GetChannelFellowCandidateInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelFellowCandidateInfoReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetChannelFellowCandidateInfoReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

// 可选的关系信息
type FellowOptionInfo struct {
	BindType             uint32   `protobuf:"varint,1,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	FellowType           uint32   `protobuf:"varint,2,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	FellowName           string   `protobuf:"bytes,3,opt,name=fellow_name,json=fellowName,proto3" json:"fellow_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowOptionInfo) Reset()         { *m = FellowOptionInfo{} }
func (m *FellowOptionInfo) String() string { return proto.CompactTextString(m) }
func (*FellowOptionInfo) ProtoMessage()    {}
func (*FellowOptionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{90}
}
func (m *FellowOptionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowOptionInfo.Unmarshal(m, b)
}
func (m *FellowOptionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowOptionInfo.Marshal(b, m, deterministic)
}
func (dst *FellowOptionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowOptionInfo.Merge(dst, src)
}
func (m *FellowOptionInfo) XXX_Size() int {
	return xxx_messageInfo_FellowOptionInfo.Size(m)
}
func (m *FellowOptionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowOptionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FellowOptionInfo proto.InternalMessageInfo

func (m *FellowOptionInfo) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *FellowOptionInfo) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *FellowOptionInfo) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

type GetChannelFellowCandidateInfoResp struct {
	PresentInfo          *FellowPresentInfo  `protobuf:"bytes,1,opt,name=present_info,json=presentInfo,proto3" json:"present_info,omitempty"`
	UserInfo             *FellowInviteUser   `protobuf:"bytes,2,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	FellowOptionList     []*FellowOptionInfo `protobuf:"bytes,3,rep,name=fellow_option_list,json=fellowOptionList,proto3" json:"fellow_option_list,omitempty"`
	HasMultiFellowField  bool                `protobuf:"varint,4,opt,name=has_multi_fellow_field,json=hasMultiFellowField,proto3" json:"has_multi_fellow_field,omitempty"`
	UnlockPrice          uint32              `protobuf:"varint,5,opt,name=unlock_price,json=unlockPrice,proto3" json:"unlock_price,omitempty"`
	HasCpField           bool                `protobuf:"varint,6,opt,name=has_cp_field,json=hasCpField,proto3" json:"has_cp_field,omitempty"`
	SelfUnlockInfo       *UnlockInfo         `protobuf:"bytes,7,opt,name=self_unlock_info,json=selfUnlockInfo,proto3" json:"self_unlock_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetChannelFellowCandidateInfoResp) Reset()         { *m = GetChannelFellowCandidateInfoResp{} }
func (m *GetChannelFellowCandidateInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelFellowCandidateInfoResp) ProtoMessage()    {}
func (*GetChannelFellowCandidateInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{91}
}
func (m *GetChannelFellowCandidateInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelFellowCandidateInfoResp.Unmarshal(m, b)
}
func (m *GetChannelFellowCandidateInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelFellowCandidateInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelFellowCandidateInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelFellowCandidateInfoResp.Merge(dst, src)
}
func (m *GetChannelFellowCandidateInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelFellowCandidateInfoResp.Size(m)
}
func (m *GetChannelFellowCandidateInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelFellowCandidateInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelFellowCandidateInfoResp proto.InternalMessageInfo

func (m *GetChannelFellowCandidateInfoResp) GetPresentInfo() *FellowPresentInfo {
	if m != nil {
		return m.PresentInfo
	}
	return nil
}

func (m *GetChannelFellowCandidateInfoResp) GetUserInfo() *FellowInviteUser {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *GetChannelFellowCandidateInfoResp) GetFellowOptionList() []*FellowOptionInfo {
	if m != nil {
		return m.FellowOptionList
	}
	return nil
}

func (m *GetChannelFellowCandidateInfoResp) GetHasMultiFellowField() bool {
	if m != nil {
		return m.HasMultiFellowField
	}
	return false
}

func (m *GetChannelFellowCandidateInfoResp) GetUnlockPrice() uint32 {
	if m != nil {
		return m.UnlockPrice
	}
	return 0
}

func (m *GetChannelFellowCandidateInfoResp) GetHasCpField() bool {
	if m != nil {
		return m.HasCpField
	}
	return false
}

func (m *GetChannelFellowCandidateInfoResp) GetSelfUnlockInfo() *UnlockInfo {
	if m != nil {
		return m.SelfUnlockInfo
	}
	return nil
}

// 我发出的挚友邀请列表
type GetSendInviteListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSendInviteListReq) Reset()         { *m = GetSendInviteListReq{} }
func (m *GetSendInviteListReq) String() string { return proto.CompactTextString(m) }
func (*GetSendInviteListReq) ProtoMessage()    {}
func (*GetSendInviteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{92}
}
func (m *GetSendInviteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSendInviteListReq.Unmarshal(m, b)
}
func (m *GetSendInviteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSendInviteListReq.Marshal(b, m, deterministic)
}
func (dst *GetSendInviteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSendInviteListReq.Merge(dst, src)
}
func (m *GetSendInviteListReq) XXX_Size() int {
	return xxx_messageInfo_GetSendInviteListReq.Size(m)
}
func (m *GetSendInviteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSendInviteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSendInviteListReq proto.InternalMessageInfo

type GetSendInviteListResp struct {
	InviteList           []*FellowInviteInfo `protobuf:"bytes,1,rep,name=invite_list,json=inviteList,proto3" json:"invite_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetSendInviteListResp) Reset()         { *m = GetSendInviteListResp{} }
func (m *GetSendInviteListResp) String() string { return proto.CompactTextString(m) }
func (*GetSendInviteListResp) ProtoMessage()    {}
func (*GetSendInviteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{93}
}
func (m *GetSendInviteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSendInviteListResp.Unmarshal(m, b)
}
func (m *GetSendInviteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSendInviteListResp.Marshal(b, m, deterministic)
}
func (dst *GetSendInviteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSendInviteListResp.Merge(dst, src)
}
func (m *GetSendInviteListResp) XXX_Size() int {
	return xxx_messageInfo_GetSendInviteListResp.Size(m)
}
func (m *GetSendInviteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSendInviteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSendInviteListResp proto.InternalMessageInfo

func (m *GetSendInviteListResp) GetInviteList() []*FellowInviteInfo {
	if m != nil {
		return m.InviteList
	}
	return nil
}

// 资源
type Resource struct {
	Url                  string       `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Type                 ResourceType `protobuf:"varint,2,opt,name=type,proto3,enum=fellow_svr.ResourceType" json:"type,omitempty"`
	BackgroundImgUrl     string       `protobuf:"bytes,3,opt,name=background_img_url,json=backgroundImgUrl,proto3" json:"background_img_url,omitempty"`
	Md5                  string       `protobuf:"bytes,4,opt,name=md5,proto3" json:"md5,omitempty"`
	Name                 string       `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *Resource) Reset()         { *m = Resource{} }
func (m *Resource) String() string { return proto.CompactTextString(m) }
func (*Resource) ProtoMessage()    {}
func (*Resource) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{94}
}
func (m *Resource) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Resource.Unmarshal(m, b)
}
func (m *Resource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Resource.Marshal(b, m, deterministic)
}
func (dst *Resource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Resource.Merge(dst, src)
}
func (m *Resource) XXX_Size() int {
	return xxx_messageInfo_Resource.Size(m)
}
func (m *Resource) XXX_DiscardUnknown() {
	xxx_messageInfo_Resource.DiscardUnknown(m)
}

var xxx_messageInfo_Resource proto.InternalMessageInfo

func (m *Resource) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *Resource) GetType() ResourceType {
	if m != nil {
		return m.Type
	}
	return ResourceType_RESOURCE_TYPE_UNKNOWN
}

func (m *Resource) GetBackgroundImgUrl() string {
	if m != nil {
		return m.BackgroundImgUrl
	}
	return ""
}

func (m *Resource) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *Resource) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// 颜色资源
type GradientColor struct {
	StartHex             string   `protobuf:"bytes,1,opt,name=start_hex,json=startHex,proto3" json:"start_hex,omitempty"`
	EndHex               string   `protobuf:"bytes,2,opt,name=end_hex,json=endHex,proto3" json:"end_hex,omitempty"`
	ShadowHex            string   `protobuf:"bytes,3,opt,name=shadow_hex,json=shadowHex,proto3" json:"shadow_hex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GradientColor) Reset()         { *m = GradientColor{} }
func (m *GradientColor) String() string { return proto.CompactTextString(m) }
func (*GradientColor) ProtoMessage()    {}
func (*GradientColor) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{95}
}
func (m *GradientColor) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GradientColor.Unmarshal(m, b)
}
func (m *GradientColor) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GradientColor.Marshal(b, m, deterministic)
}
func (dst *GradientColor) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GradientColor.Merge(dst, src)
}
func (m *GradientColor) XXX_Size() int {
	return xxx_messageInfo_GradientColor.Size(m)
}
func (m *GradientColor) XXX_DiscardUnknown() {
	xxx_messageInfo_GradientColor.DiscardUnknown(m)
}

var xxx_messageInfo_GradientColor proto.InternalMessageInfo

func (m *GradientColor) GetStartHex() string {
	if m != nil {
		return m.StartHex
	}
	return ""
}

func (m *GradientColor) GetEndHex() string {
	if m != nil {
		return m.EndHex
	}
	return ""
}

func (m *GradientColor) GetShadowHex() string {
	if m != nil {
		return m.ShadowHex
	}
	return ""
}

// 关系盒子
type RelationshipBox struct {
	Background           *Resource `protobuf:"bytes,1,opt,name=background,proto3" json:"background,omitempty"`
	BigBackground        *Resource `protobuf:"bytes,2,opt,name=big_background,json=bigBackground,proto3" json:"big_background,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *RelationshipBox) Reset()         { *m = RelationshipBox{} }
func (m *RelationshipBox) String() string { return proto.CompactTextString(m) }
func (*RelationshipBox) ProtoMessage()    {}
func (*RelationshipBox) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{96}
}
func (m *RelationshipBox) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelationshipBox.Unmarshal(m, b)
}
func (m *RelationshipBox) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelationshipBox.Marshal(b, m, deterministic)
}
func (dst *RelationshipBox) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelationshipBox.Merge(dst, src)
}
func (m *RelationshipBox) XXX_Size() int {
	return xxx_messageInfo_RelationshipBox.Size(m)
}
func (m *RelationshipBox) XXX_DiscardUnknown() {
	xxx_messageInfo_RelationshipBox.DiscardUnknown(m)
}

var xxx_messageInfo_RelationshipBox proto.InternalMessageInfo

func (m *RelationshipBox) GetBackground() *Resource {
	if m != nil {
		return m.Background
	}
	return nil
}

func (m *RelationshipBox) GetBigBackground() *Resource {
	if m != nil {
		return m.BigBackground
	}
	return nil
}

// 消息通知图片配置
type MsgNotifyImg struct {
	Origin               string   `protobuf:"bytes,1,opt,name=origin,proto3" json:"origin,omitempty"`
	Thumbnail            string   `protobuf:"bytes,2,opt,name=thumbnail,proto3" json:"thumbnail,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MsgNotifyImg) Reset()         { *m = MsgNotifyImg{} }
func (m *MsgNotifyImg) String() string { return proto.CompactTextString(m) }
func (*MsgNotifyImg) ProtoMessage()    {}
func (*MsgNotifyImg) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{97}
}
func (m *MsgNotifyImg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MsgNotifyImg.Unmarshal(m, b)
}
func (m *MsgNotifyImg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MsgNotifyImg.Marshal(b, m, deterministic)
}
func (dst *MsgNotifyImg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MsgNotifyImg.Merge(dst, src)
}
func (m *MsgNotifyImg) XXX_Size() int {
	return xxx_messageInfo_MsgNotifyImg.Size(m)
}
func (m *MsgNotifyImg) XXX_DiscardUnknown() {
	xxx_messageInfo_MsgNotifyImg.DiscardUnknown(m)
}

var xxx_messageInfo_MsgNotifyImg proto.InternalMessageInfo

func (m *MsgNotifyImg) GetOrigin() string {
	if m != nil {
		return m.Origin
	}
	return ""
}

func (m *MsgNotifyImg) GetThumbnail() string {
	if m != nil {
		return m.Thumbnail
	}
	return ""
}

// 麦上连线样式
type ConnectedStringForMic struct {
	Left                 string   `protobuf:"bytes,1,opt,name=left,proto3" json:"left,omitempty"`
	Right                string   `protobuf:"bytes,2,opt,name=right,proto3" json:"right,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConnectedStringForMic) Reset()         { *m = ConnectedStringForMic{} }
func (m *ConnectedStringForMic) String() string { return proto.CompactTextString(m) }
func (*ConnectedStringForMic) ProtoMessage()    {}
func (*ConnectedStringForMic) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{98}
}
func (m *ConnectedStringForMic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConnectedStringForMic.Unmarshal(m, b)
}
func (m *ConnectedStringForMic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConnectedStringForMic.Marshal(b, m, deterministic)
}
func (dst *ConnectedStringForMic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConnectedStringForMic.Merge(dst, src)
}
func (m *ConnectedStringForMic) XXX_Size() int {
	return xxx_messageInfo_ConnectedStringForMic.Size(m)
}
func (m *ConnectedStringForMic) XXX_DiscardUnknown() {
	xxx_messageInfo_ConnectedStringForMic.DiscardUnknown(m)
}

var xxx_messageInfo_ConnectedStringForMic proto.InternalMessageInfo

func (m *ConnectedStringForMic) GetLeft() string {
	if m != nil {
		return m.Left
	}
	return ""
}

func (m *ConnectedStringForMic) GetRight() string {
	if m != nil {
		return m.Right
	}
	return ""
}

// 关系
type Relationship struct {
	Id                    uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreateTime            int64                  `protobuf:"varint,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime            int64                  `protobuf:"varint,3,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Name                  string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Type                  RelationType           `protobuf:"varint,5,opt,name=type,proto3,enum=fellow_svr.RelationType" json:"type,omitempty"`
	AnimationOfSettlement *Resource              `protobuf:"bytes,6,opt,name=animation_of_settlement,json=animationOfSettlement,proto3" json:"animation_of_settlement,omitempty"`
	RelationshipBox       *RelationshipBox       `protobuf:"bytes,7,opt,name=relationship_box,json=relationshipBox,proto3" json:"relationship_box,omitempty"`
	ConnectedStringForMic *ConnectedStringForMic `protobuf:"bytes,8,opt,name=connected_string_for_mic,json=connectedStringForMic,proto3" json:"connected_string_for_mic,omitempty"`
	SpaceFlagUrl          string                 `protobuf:"bytes,9,opt,name=space_flag_url,json=spaceFlagUrl,proto3" json:"space_flag_url,omitempty"`
	CardFlagColor         *GradientColor         `protobuf:"bytes,10,opt,name=card_flag_color,json=cardFlagColor,proto3" json:"card_flag_color,omitempty"`
	ImFlagUrl             string                 `protobuf:"bytes,11,opt,name=im_flag_url,json=imFlagUrl,proto3" json:"im_flag_url,omitempty"`
	FriendSpaceBackground string                 `protobuf:"bytes,12,opt,name=friend_space_background,json=friendSpaceBackground,proto3" json:"friend_space_background,omitempty"`
	MsgNotifyImg          *MsgNotifyImg          `protobuf:"bytes,13,opt,name=msg_notify_img,json=msgNotifyImg,proto3" json:"msg_notify_img,omitempty"`
	BizType               int32                  `protobuf:"varint,14,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	SubRelation           []*Relationship        `protobuf:"bytes,15,rep,name=sub_relation,json=subRelation,proto3" json:"sub_relation,omitempty"`
	SortIndex             int32                  `protobuf:"varint,16,opt,name=sort_index,json=sortIndex,proto3" json:"sort_index,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}               `json:"-"`
	XXX_unrecognized      []byte                 `json:"-"`
	XXX_sizecache         int32                  `json:"-"`
}

func (m *Relationship) Reset()         { *m = Relationship{} }
func (m *Relationship) String() string { return proto.CompactTextString(m) }
func (*Relationship) ProtoMessage()    {}
func (*Relationship) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{99}
}
func (m *Relationship) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Relationship.Unmarshal(m, b)
}
func (m *Relationship) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Relationship.Marshal(b, m, deterministic)
}
func (dst *Relationship) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Relationship.Merge(dst, src)
}
func (m *Relationship) XXX_Size() int {
	return xxx_messageInfo_Relationship.Size(m)
}
func (m *Relationship) XXX_DiscardUnknown() {
	xxx_messageInfo_Relationship.DiscardUnknown(m)
}

var xxx_messageInfo_Relationship proto.InternalMessageInfo

func (m *Relationship) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Relationship) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *Relationship) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *Relationship) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Relationship) GetType() RelationType {
	if m != nil {
		return m.Type
	}
	return RelationType_RELATION_TYPE_UNKNOWN
}

func (m *Relationship) GetAnimationOfSettlement() *Resource {
	if m != nil {
		return m.AnimationOfSettlement
	}
	return nil
}

func (m *Relationship) GetRelationshipBox() *RelationshipBox {
	if m != nil {
		return m.RelationshipBox
	}
	return nil
}

func (m *Relationship) GetConnectedStringForMic() *ConnectedStringForMic {
	if m != nil {
		return m.ConnectedStringForMic
	}
	return nil
}

func (m *Relationship) GetSpaceFlagUrl() string {
	if m != nil {
		return m.SpaceFlagUrl
	}
	return ""
}

func (m *Relationship) GetCardFlagColor() *GradientColor {
	if m != nil {
		return m.CardFlagColor
	}
	return nil
}

func (m *Relationship) GetImFlagUrl() string {
	if m != nil {
		return m.ImFlagUrl
	}
	return ""
}

func (m *Relationship) GetFriendSpaceBackground() string {
	if m != nil {
		return m.FriendSpaceBackground
	}
	return ""
}

func (m *Relationship) GetMsgNotifyImg() *MsgNotifyImg {
	if m != nil {
		return m.MsgNotifyImg
	}
	return nil
}

func (m *Relationship) GetBizType() int32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

func (m *Relationship) GetSubRelation() []*Relationship {
	if m != nil {
		return m.SubRelation
	}
	return nil
}

func (m *Relationship) GetSortIndex() int32 {
	if m != nil {
		return m.SortIndex
	}
	return 0
}

// 房间绑定的稀有关系
type ChannelRelationshipBinding struct {
	Id                   int64                                                `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreateTime           int64                                                `protobuf:"varint,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           int64                                                `protobuf:"varint,3,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	ChannelId            uint32                                               `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RelationshipIdList   []*ChannelRelationshipBinding_RelationshipSimpleInfo `protobuf:"bytes,5,rep,name=relationship_id_list,json=relationshipIdList,proto3" json:"relationship_id_list,omitempty"`
	StartTime            int64                                                `protobuf:"varint,6,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64                                                `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	IntroUrl             *ChannelRelationshipBinding_Introduction             `protobuf:"bytes,8,opt,name=intro_url,json=introUrl,proto3" json:"intro_url,omitempty"`
	EntranceUrl          string                                               `protobuf:"bytes,9,opt,name=entrance_url,json=entranceUrl,proto3" json:"entrance_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                             `json:"-"`
	XXX_unrecognized     []byte                                               `json:"-"`
	XXX_sizecache        int32                                                `json:"-"`
}

func (m *ChannelRelationshipBinding) Reset()         { *m = ChannelRelationshipBinding{} }
func (m *ChannelRelationshipBinding) String() string { return proto.CompactTextString(m) }
func (*ChannelRelationshipBinding) ProtoMessage()    {}
func (*ChannelRelationshipBinding) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{100}
}
func (m *ChannelRelationshipBinding) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRelationshipBinding.Unmarshal(m, b)
}
func (m *ChannelRelationshipBinding) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRelationshipBinding.Marshal(b, m, deterministic)
}
func (dst *ChannelRelationshipBinding) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRelationshipBinding.Merge(dst, src)
}
func (m *ChannelRelationshipBinding) XXX_Size() int {
	return xxx_messageInfo_ChannelRelationshipBinding.Size(m)
}
func (m *ChannelRelationshipBinding) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRelationshipBinding.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRelationshipBinding proto.InternalMessageInfo

func (m *ChannelRelationshipBinding) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ChannelRelationshipBinding) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *ChannelRelationshipBinding) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *ChannelRelationshipBinding) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelRelationshipBinding) GetRelationshipIdList() []*ChannelRelationshipBinding_RelationshipSimpleInfo {
	if m != nil {
		return m.RelationshipIdList
	}
	return nil
}

func (m *ChannelRelationshipBinding) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *ChannelRelationshipBinding) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ChannelRelationshipBinding) GetIntroUrl() *ChannelRelationshipBinding_Introduction {
	if m != nil {
		return m.IntroUrl
	}
	return nil
}

func (m *ChannelRelationshipBinding) GetEntranceUrl() string {
	if m != nil {
		return m.EntranceUrl
	}
	return ""
}

type ChannelRelationshipBinding_RelationshipSimpleInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelRelationshipBinding_RelationshipSimpleInfo) Reset() {
	*m = ChannelRelationshipBinding_RelationshipSimpleInfo{}
}
func (m *ChannelRelationshipBinding_RelationshipSimpleInfo) String() string {
	return proto.CompactTextString(m)
}
func (*ChannelRelationshipBinding_RelationshipSimpleInfo) ProtoMessage() {}
func (*ChannelRelationshipBinding_RelationshipSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{100, 0}
}
func (m *ChannelRelationshipBinding_RelationshipSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRelationshipBinding_RelationshipSimpleInfo.Unmarshal(m, b)
}
func (m *ChannelRelationshipBinding_RelationshipSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRelationshipBinding_RelationshipSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelRelationshipBinding_RelationshipSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRelationshipBinding_RelationshipSimpleInfo.Merge(dst, src)
}
func (m *ChannelRelationshipBinding_RelationshipSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelRelationshipBinding_RelationshipSimpleInfo.Size(m)
}
func (m *ChannelRelationshipBinding_RelationshipSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRelationshipBinding_RelationshipSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRelationshipBinding_RelationshipSimpleInfo proto.InternalMessageInfo

func (m *ChannelRelationshipBinding_RelationshipSimpleInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ChannelRelationshipBinding_RelationshipSimpleInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type ChannelRelationshipBinding_Introduction struct {
	TtUrl                string   `protobuf:"bytes,1,opt,name=tt_url,json=ttUrl,proto3" json:"tt_url,omitempty"`
	HappyGameIos         string   `protobuf:"bytes,2,opt,name=happy_game_ios,json=happyGameIos,proto3" json:"happy_game_ios,omitempty"`
	HappyGameAndroid     string   `protobuf:"bytes,3,opt,name=happy_game_android,json=happyGameAndroid,proto3" json:"happy_game_android,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelRelationshipBinding_Introduction) Reset() {
	*m = ChannelRelationshipBinding_Introduction{}
}
func (m *ChannelRelationshipBinding_Introduction) String() string { return proto.CompactTextString(m) }
func (*ChannelRelationshipBinding_Introduction) ProtoMessage()    {}
func (*ChannelRelationshipBinding_Introduction) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{100, 1}
}
func (m *ChannelRelationshipBinding_Introduction) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRelationshipBinding_Introduction.Unmarshal(m, b)
}
func (m *ChannelRelationshipBinding_Introduction) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRelationshipBinding_Introduction.Marshal(b, m, deterministic)
}
func (dst *ChannelRelationshipBinding_Introduction) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRelationshipBinding_Introduction.Merge(dst, src)
}
func (m *ChannelRelationshipBinding_Introduction) XXX_Size() int {
	return xxx_messageInfo_ChannelRelationshipBinding_Introduction.Size(m)
}
func (m *ChannelRelationshipBinding_Introduction) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRelationshipBinding_Introduction.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRelationshipBinding_Introduction proto.InternalMessageInfo

func (m *ChannelRelationshipBinding_Introduction) GetTtUrl() string {
	if m != nil {
		return m.TtUrl
	}
	return ""
}

func (m *ChannelRelationshipBinding_Introduction) GetHappyGameIos() string {
	if m != nil {
		return m.HappyGameIos
	}
	return ""
}

func (m *ChannelRelationshipBinding_Introduction) GetHappyGameAndroid() string {
	if m != nil {
		return m.HappyGameAndroid
	}
	return ""
}

// 分页查询关系列表请求
type RelationshipListReq struct {
	PageIndex            int32    `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
	PageSize             int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	ConditionId          uint32   `protobuf:"varint,3,opt,name=condition_id,json=conditionId,proto3" json:"condition_id,omitempty"`
	ConditionName        string   `protobuf:"bytes,4,opt,name=condition_name,json=conditionName,proto3" json:"condition_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RelationshipListReq) Reset()         { *m = RelationshipListReq{} }
func (m *RelationshipListReq) String() string { return proto.CompactTextString(m) }
func (*RelationshipListReq) ProtoMessage()    {}
func (*RelationshipListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{101}
}
func (m *RelationshipListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelationshipListReq.Unmarshal(m, b)
}
func (m *RelationshipListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelationshipListReq.Marshal(b, m, deterministic)
}
func (dst *RelationshipListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelationshipListReq.Merge(dst, src)
}
func (m *RelationshipListReq) XXX_Size() int {
	return xxx_messageInfo_RelationshipListReq.Size(m)
}
func (m *RelationshipListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RelationshipListReq.DiscardUnknown(m)
}

var xxx_messageInfo_RelationshipListReq proto.InternalMessageInfo

func (m *RelationshipListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *RelationshipListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *RelationshipListReq) GetConditionId() uint32 {
	if m != nil {
		return m.ConditionId
	}
	return 0
}

func (m *RelationshipListReq) GetConditionName() string {
	if m != nil {
		return m.ConditionName
	}
	return ""
}

// 分页查询关系列表响应
type RelationshipListResp struct {
	List                 []*Relationship `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	TotalCount           int32           `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	TotalPage            int32           `protobuf:"varint,3,opt,name=total_page,json=totalPage,proto3" json:"total_page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *RelationshipListResp) Reset()         { *m = RelationshipListResp{} }
func (m *RelationshipListResp) String() string { return proto.CompactTextString(m) }
func (*RelationshipListResp) ProtoMessage()    {}
func (*RelationshipListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{102}
}
func (m *RelationshipListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelationshipListResp.Unmarshal(m, b)
}
func (m *RelationshipListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelationshipListResp.Marshal(b, m, deterministic)
}
func (dst *RelationshipListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelationshipListResp.Merge(dst, src)
}
func (m *RelationshipListResp) XXX_Size() int {
	return xxx_messageInfo_RelationshipListResp.Size(m)
}
func (m *RelationshipListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RelationshipListResp.DiscardUnknown(m)
}

var xxx_messageInfo_RelationshipListResp proto.InternalMessageInfo

func (m *RelationshipListResp) GetList() []*Relationship {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *RelationshipListResp) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *RelationshipListResp) GetTotalPage() int32 {
	if m != nil {
		return m.TotalPage
	}
	return 0
}

// 根据条件查询关系请求
type RelationshipGetReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RelationshipGetReq) Reset()         { *m = RelationshipGetReq{} }
func (m *RelationshipGetReq) String() string { return proto.CompactTextString(m) }
func (*RelationshipGetReq) ProtoMessage()    {}
func (*RelationshipGetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{103}
}
func (m *RelationshipGetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelationshipGetReq.Unmarshal(m, b)
}
func (m *RelationshipGetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelationshipGetReq.Marshal(b, m, deterministic)
}
func (dst *RelationshipGetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelationshipGetReq.Merge(dst, src)
}
func (m *RelationshipGetReq) XXX_Size() int {
	return xxx_messageInfo_RelationshipGetReq.Size(m)
}
func (m *RelationshipGetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RelationshipGetReq.DiscardUnknown(m)
}

var xxx_messageInfo_RelationshipGetReq proto.InternalMessageInfo

func (m *RelationshipGetReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 根据条件查询关系响应
type RelationshipGetResp struct {
	Relationship         *Relationship `protobuf:"bytes,1,opt,name=relationship,proto3" json:"relationship,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *RelationshipGetResp) Reset()         { *m = RelationshipGetResp{} }
func (m *RelationshipGetResp) String() string { return proto.CompactTextString(m) }
func (*RelationshipGetResp) ProtoMessage()    {}
func (*RelationshipGetResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{104}
}
func (m *RelationshipGetResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelationshipGetResp.Unmarshal(m, b)
}
func (m *RelationshipGetResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelationshipGetResp.Marshal(b, m, deterministic)
}
func (dst *RelationshipGetResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelationshipGetResp.Merge(dst, src)
}
func (m *RelationshipGetResp) XXX_Size() int {
	return xxx_messageInfo_RelationshipGetResp.Size(m)
}
func (m *RelationshipGetResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RelationshipGetResp.DiscardUnknown(m)
}

var xxx_messageInfo_RelationshipGetResp proto.InternalMessageInfo

func (m *RelationshipGetResp) GetRelationship() *Relationship {
	if m != nil {
		return m.Relationship
	}
	return nil
}

// 添加关系请求
type RelationshipAddReq struct {
	Relationship         *Relationship `protobuf:"bytes,1,opt,name=relationship,proto3" json:"relationship,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *RelationshipAddReq) Reset()         { *m = RelationshipAddReq{} }
func (m *RelationshipAddReq) String() string { return proto.CompactTextString(m) }
func (*RelationshipAddReq) ProtoMessage()    {}
func (*RelationshipAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{105}
}
func (m *RelationshipAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelationshipAddReq.Unmarshal(m, b)
}
func (m *RelationshipAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelationshipAddReq.Marshal(b, m, deterministic)
}
func (dst *RelationshipAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelationshipAddReq.Merge(dst, src)
}
func (m *RelationshipAddReq) XXX_Size() int {
	return xxx_messageInfo_RelationshipAddReq.Size(m)
}
func (m *RelationshipAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RelationshipAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_RelationshipAddReq proto.InternalMessageInfo

func (m *RelationshipAddReq) GetRelationship() *Relationship {
	if m != nil {
		return m.Relationship
	}
	return nil
}

// 添加稀缺关系响应
type RelationshipAddResp struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RelationshipAddResp) Reset()         { *m = RelationshipAddResp{} }
func (m *RelationshipAddResp) String() string { return proto.CompactTextString(m) }
func (*RelationshipAddResp) ProtoMessage()    {}
func (*RelationshipAddResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{106}
}
func (m *RelationshipAddResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelationshipAddResp.Unmarshal(m, b)
}
func (m *RelationshipAddResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelationshipAddResp.Marshal(b, m, deterministic)
}
func (dst *RelationshipAddResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelationshipAddResp.Merge(dst, src)
}
func (m *RelationshipAddResp) XXX_Size() int {
	return xxx_messageInfo_RelationshipAddResp.Size(m)
}
func (m *RelationshipAddResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RelationshipAddResp.DiscardUnknown(m)
}

var xxx_messageInfo_RelationshipAddResp proto.InternalMessageInfo

func (m *RelationshipAddResp) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 编辑关系请求
type RelationshipUpdateReq struct {
	Relationship         *Relationship `protobuf:"bytes,1,opt,name=relationship,proto3" json:"relationship,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *RelationshipUpdateReq) Reset()         { *m = RelationshipUpdateReq{} }
func (m *RelationshipUpdateReq) String() string { return proto.CompactTextString(m) }
func (*RelationshipUpdateReq) ProtoMessage()    {}
func (*RelationshipUpdateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{107}
}
func (m *RelationshipUpdateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelationshipUpdateReq.Unmarshal(m, b)
}
func (m *RelationshipUpdateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelationshipUpdateReq.Marshal(b, m, deterministic)
}
func (dst *RelationshipUpdateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelationshipUpdateReq.Merge(dst, src)
}
func (m *RelationshipUpdateReq) XXX_Size() int {
	return xxx_messageInfo_RelationshipUpdateReq.Size(m)
}
func (m *RelationshipUpdateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RelationshipUpdateReq.DiscardUnknown(m)
}

var xxx_messageInfo_RelationshipUpdateReq proto.InternalMessageInfo

func (m *RelationshipUpdateReq) GetRelationship() *Relationship {
	if m != nil {
		return m.Relationship
	}
	return nil
}

// 编辑关系响应
type RelationshipUpdateResp struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RelationshipUpdateResp) Reset()         { *m = RelationshipUpdateResp{} }
func (m *RelationshipUpdateResp) String() string { return proto.CompactTextString(m) }
func (*RelationshipUpdateResp) ProtoMessage()    {}
func (*RelationshipUpdateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{108}
}
func (m *RelationshipUpdateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelationshipUpdateResp.Unmarshal(m, b)
}
func (m *RelationshipUpdateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelationshipUpdateResp.Marshal(b, m, deterministic)
}
func (dst *RelationshipUpdateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelationshipUpdateResp.Merge(dst, src)
}
func (m *RelationshipUpdateResp) XXX_Size() int {
	return xxx_messageInfo_RelationshipUpdateResp.Size(m)
}
func (m *RelationshipUpdateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RelationshipUpdateResp.DiscardUnknown(m)
}

var xxx_messageInfo_RelationshipUpdateResp proto.InternalMessageInfo

func (m *RelationshipUpdateResp) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 删除关系请求
type RelationshipDeleteReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RelationshipDeleteReq) Reset()         { *m = RelationshipDeleteReq{} }
func (m *RelationshipDeleteReq) String() string { return proto.CompactTextString(m) }
func (*RelationshipDeleteReq) ProtoMessage()    {}
func (*RelationshipDeleteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{109}
}
func (m *RelationshipDeleteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelationshipDeleteReq.Unmarshal(m, b)
}
func (m *RelationshipDeleteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelationshipDeleteReq.Marshal(b, m, deterministic)
}
func (dst *RelationshipDeleteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelationshipDeleteReq.Merge(dst, src)
}
func (m *RelationshipDeleteReq) XXX_Size() int {
	return xxx_messageInfo_RelationshipDeleteReq.Size(m)
}
func (m *RelationshipDeleteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RelationshipDeleteReq.DiscardUnknown(m)
}

var xxx_messageInfo_RelationshipDeleteReq proto.InternalMessageInfo

func (m *RelationshipDeleteReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 删除关系响应
type RelationshipDeleteResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RelationshipDeleteResp) Reset()         { *m = RelationshipDeleteResp{} }
func (m *RelationshipDeleteResp) String() string { return proto.CompactTextString(m) }
func (*RelationshipDeleteResp) ProtoMessage()    {}
func (*RelationshipDeleteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{110}
}
func (m *RelationshipDeleteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelationshipDeleteResp.Unmarshal(m, b)
}
func (m *RelationshipDeleteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelationshipDeleteResp.Marshal(b, m, deterministic)
}
func (dst *RelationshipDeleteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelationshipDeleteResp.Merge(dst, src)
}
func (m *RelationshipDeleteResp) XXX_Size() int {
	return xxx_messageInfo_RelationshipDeleteResp.Size(m)
}
func (m *RelationshipDeleteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RelationshipDeleteResp.DiscardUnknown(m)
}

var xxx_messageInfo_RelationshipDeleteResp proto.InternalMessageInfo

// 分页查询关系下发列表请求
type ChannelRelationshipBindingListReq struct {
	PageIndex            int32    `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
	PageSize             int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelRelationshipBindingListReq) Reset()         { *m = ChannelRelationshipBindingListReq{} }
func (m *ChannelRelationshipBindingListReq) String() string { return proto.CompactTextString(m) }
func (*ChannelRelationshipBindingListReq) ProtoMessage()    {}
func (*ChannelRelationshipBindingListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{111}
}
func (m *ChannelRelationshipBindingListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRelationshipBindingListReq.Unmarshal(m, b)
}
func (m *ChannelRelationshipBindingListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRelationshipBindingListReq.Marshal(b, m, deterministic)
}
func (dst *ChannelRelationshipBindingListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRelationshipBindingListReq.Merge(dst, src)
}
func (m *ChannelRelationshipBindingListReq) XXX_Size() int {
	return xxx_messageInfo_ChannelRelationshipBindingListReq.Size(m)
}
func (m *ChannelRelationshipBindingListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRelationshipBindingListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRelationshipBindingListReq proto.InternalMessageInfo

func (m *ChannelRelationshipBindingListReq) GetPageIndex() int32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *ChannelRelationshipBindingListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *ChannelRelationshipBindingListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 分页查询关系下发列表响应
type ChannelRelationshipBindingListResp struct {
	List                 []*ChannelRelationshipBinding `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	TotalCount           int32                         `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	TotalPage            int32                         `protobuf:"varint,3,opt,name=total_page,json=totalPage,proto3" json:"total_page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *ChannelRelationshipBindingListResp) Reset()         { *m = ChannelRelationshipBindingListResp{} }
func (m *ChannelRelationshipBindingListResp) String() string { return proto.CompactTextString(m) }
func (*ChannelRelationshipBindingListResp) ProtoMessage()    {}
func (*ChannelRelationshipBindingListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{112}
}
func (m *ChannelRelationshipBindingListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRelationshipBindingListResp.Unmarshal(m, b)
}
func (m *ChannelRelationshipBindingListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRelationshipBindingListResp.Marshal(b, m, deterministic)
}
func (dst *ChannelRelationshipBindingListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRelationshipBindingListResp.Merge(dst, src)
}
func (m *ChannelRelationshipBindingListResp) XXX_Size() int {
	return xxx_messageInfo_ChannelRelationshipBindingListResp.Size(m)
}
func (m *ChannelRelationshipBindingListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRelationshipBindingListResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRelationshipBindingListResp proto.InternalMessageInfo

func (m *ChannelRelationshipBindingListResp) GetList() []*ChannelRelationshipBinding {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *ChannelRelationshipBindingListResp) GetTotalCount() int32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *ChannelRelationshipBindingListResp) GetTotalPage() int32 {
	if m != nil {
		return m.TotalPage
	}
	return 0
}

// 下发关系请求
type ChannelRelationshipBindingAddReq struct {
	ChannelRelationshipBinding *ChannelRelationshipBinding `protobuf:"bytes,1,opt,name=channel_relationship_binding,json=channelRelationshipBinding,proto3" json:"channel_relationship_binding,omitempty"`
	XXX_NoUnkeyedLiteral       struct{}                    `json:"-"`
	XXX_unrecognized           []byte                      `json:"-"`
	XXX_sizecache              int32                       `json:"-"`
}

func (m *ChannelRelationshipBindingAddReq) Reset()         { *m = ChannelRelationshipBindingAddReq{} }
func (m *ChannelRelationshipBindingAddReq) String() string { return proto.CompactTextString(m) }
func (*ChannelRelationshipBindingAddReq) ProtoMessage()    {}
func (*ChannelRelationshipBindingAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{113}
}
func (m *ChannelRelationshipBindingAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRelationshipBindingAddReq.Unmarshal(m, b)
}
func (m *ChannelRelationshipBindingAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRelationshipBindingAddReq.Marshal(b, m, deterministic)
}
func (dst *ChannelRelationshipBindingAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRelationshipBindingAddReq.Merge(dst, src)
}
func (m *ChannelRelationshipBindingAddReq) XXX_Size() int {
	return xxx_messageInfo_ChannelRelationshipBindingAddReq.Size(m)
}
func (m *ChannelRelationshipBindingAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRelationshipBindingAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRelationshipBindingAddReq proto.InternalMessageInfo

func (m *ChannelRelationshipBindingAddReq) GetChannelRelationshipBinding() *ChannelRelationshipBinding {
	if m != nil {
		return m.ChannelRelationshipBinding
	}
	return nil
}

// 下发关系响应
type ChannelRelationshipBindingAddResp struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelRelationshipBindingAddResp) Reset()         { *m = ChannelRelationshipBindingAddResp{} }
func (m *ChannelRelationshipBindingAddResp) String() string { return proto.CompactTextString(m) }
func (*ChannelRelationshipBindingAddResp) ProtoMessage()    {}
func (*ChannelRelationshipBindingAddResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{114}
}
func (m *ChannelRelationshipBindingAddResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRelationshipBindingAddResp.Unmarshal(m, b)
}
func (m *ChannelRelationshipBindingAddResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRelationshipBindingAddResp.Marshal(b, m, deterministic)
}
func (dst *ChannelRelationshipBindingAddResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRelationshipBindingAddResp.Merge(dst, src)
}
func (m *ChannelRelationshipBindingAddResp) XXX_Size() int {
	return xxx_messageInfo_ChannelRelationshipBindingAddResp.Size(m)
}
func (m *ChannelRelationshipBindingAddResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRelationshipBindingAddResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRelationshipBindingAddResp proto.InternalMessageInfo

func (m *ChannelRelationshipBindingAddResp) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 编辑下发关系信息请求
type ChannelRelationshipBindingUpdateReq struct {
	ChannelRelationshipBinding *ChannelRelationshipBinding `protobuf:"bytes,1,opt,name=channel_relationship_binding,json=channelRelationshipBinding,proto3" json:"channel_relationship_binding,omitempty"`
	XXX_NoUnkeyedLiteral       struct{}                    `json:"-"`
	XXX_unrecognized           []byte                      `json:"-"`
	XXX_sizecache              int32                       `json:"-"`
}

func (m *ChannelRelationshipBindingUpdateReq) Reset()         { *m = ChannelRelationshipBindingUpdateReq{} }
func (m *ChannelRelationshipBindingUpdateReq) String() string { return proto.CompactTextString(m) }
func (*ChannelRelationshipBindingUpdateReq) ProtoMessage()    {}
func (*ChannelRelationshipBindingUpdateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{115}
}
func (m *ChannelRelationshipBindingUpdateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRelationshipBindingUpdateReq.Unmarshal(m, b)
}
func (m *ChannelRelationshipBindingUpdateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRelationshipBindingUpdateReq.Marshal(b, m, deterministic)
}
func (dst *ChannelRelationshipBindingUpdateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRelationshipBindingUpdateReq.Merge(dst, src)
}
func (m *ChannelRelationshipBindingUpdateReq) XXX_Size() int {
	return xxx_messageInfo_ChannelRelationshipBindingUpdateReq.Size(m)
}
func (m *ChannelRelationshipBindingUpdateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRelationshipBindingUpdateReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRelationshipBindingUpdateReq proto.InternalMessageInfo

func (m *ChannelRelationshipBindingUpdateReq) GetChannelRelationshipBinding() *ChannelRelationshipBinding {
	if m != nil {
		return m.ChannelRelationshipBinding
	}
	return nil
}

// 编辑下发关系信息响应
type ChannelRelationshipBindingUpdateResp struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelRelationshipBindingUpdateResp) Reset()         { *m = ChannelRelationshipBindingUpdateResp{} }
func (m *ChannelRelationshipBindingUpdateResp) String() string { return proto.CompactTextString(m) }
func (*ChannelRelationshipBindingUpdateResp) ProtoMessage()    {}
func (*ChannelRelationshipBindingUpdateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{116}
}
func (m *ChannelRelationshipBindingUpdateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRelationshipBindingUpdateResp.Unmarshal(m, b)
}
func (m *ChannelRelationshipBindingUpdateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRelationshipBindingUpdateResp.Marshal(b, m, deterministic)
}
func (dst *ChannelRelationshipBindingUpdateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRelationshipBindingUpdateResp.Merge(dst, src)
}
func (m *ChannelRelationshipBindingUpdateResp) XXX_Size() int {
	return xxx_messageInfo_ChannelRelationshipBindingUpdateResp.Size(m)
}
func (m *ChannelRelationshipBindingUpdateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRelationshipBindingUpdateResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRelationshipBindingUpdateResp proto.InternalMessageInfo

func (m *ChannelRelationshipBindingUpdateResp) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 删除下发关系信息请求
type ChannelRelationshipBindingDeleteReq struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelRelationshipBindingDeleteReq) Reset()         { *m = ChannelRelationshipBindingDeleteReq{} }
func (m *ChannelRelationshipBindingDeleteReq) String() string { return proto.CompactTextString(m) }
func (*ChannelRelationshipBindingDeleteReq) ProtoMessage()    {}
func (*ChannelRelationshipBindingDeleteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{117}
}
func (m *ChannelRelationshipBindingDeleteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRelationshipBindingDeleteReq.Unmarshal(m, b)
}
func (m *ChannelRelationshipBindingDeleteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRelationshipBindingDeleteReq.Marshal(b, m, deterministic)
}
func (dst *ChannelRelationshipBindingDeleteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRelationshipBindingDeleteReq.Merge(dst, src)
}
func (m *ChannelRelationshipBindingDeleteReq) XXX_Size() int {
	return xxx_messageInfo_ChannelRelationshipBindingDeleteReq.Size(m)
}
func (m *ChannelRelationshipBindingDeleteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRelationshipBindingDeleteReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRelationshipBindingDeleteReq proto.InternalMessageInfo

func (m *ChannelRelationshipBindingDeleteReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 删除下发关系信息响应
type ChannelRelationshipBindingDeleteResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelRelationshipBindingDeleteResp) Reset()         { *m = ChannelRelationshipBindingDeleteResp{} }
func (m *ChannelRelationshipBindingDeleteResp) String() string { return proto.CompactTextString(m) }
func (*ChannelRelationshipBindingDeleteResp) ProtoMessage()    {}
func (*ChannelRelationshipBindingDeleteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{118}
}
func (m *ChannelRelationshipBindingDeleteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelRelationshipBindingDeleteResp.Unmarshal(m, b)
}
func (m *ChannelRelationshipBindingDeleteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelRelationshipBindingDeleteResp.Marshal(b, m, deterministic)
}
func (dst *ChannelRelationshipBindingDeleteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelRelationshipBindingDeleteResp.Merge(dst, src)
}
func (m *ChannelRelationshipBindingDeleteResp) XXX_Size() int {
	return xxx_messageInfo_ChannelRelationshipBindingDeleteResp.Size(m)
}
func (m *ChannelRelationshipBindingDeleteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelRelationshipBindingDeleteResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelRelationshipBindingDeleteResp proto.InternalMessageInfo

// 获取稀缺关系配置
type GetRareConfigReq struct {
	DayConfig            uint32   `protobuf:"varint,1,opt,name=day_config,json=dayConfig,proto3" json:"day_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRareConfigReq) Reset()         { *m = GetRareConfigReq{} }
func (m *GetRareConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetRareConfigReq) ProtoMessage()    {}
func (*GetRareConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{119}
}
func (m *GetRareConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRareConfigReq.Unmarshal(m, b)
}
func (m *GetRareConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRareConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetRareConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRareConfigReq.Merge(dst, src)
}
func (m *GetRareConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetRareConfigReq.Size(m)
}
func (m *GetRareConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRareConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRareConfigReq proto.InternalMessageInfo

func (m *GetRareConfigReq) GetDayConfig() uint32 {
	if m != nil {
		return m.DayConfig
	}
	return 0
}

type AnimationConfig struct {
	ResourceUrl          string   `protobuf:"bytes,1,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	Md5                  string   `protobuf:"bytes,2,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnimationConfig) Reset()         { *m = AnimationConfig{} }
func (m *AnimationConfig) String() string { return proto.CompactTextString(m) }
func (*AnimationConfig) ProtoMessage()    {}
func (*AnimationConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{120}
}
func (m *AnimationConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnimationConfig.Unmarshal(m, b)
}
func (m *AnimationConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnimationConfig.Marshal(b, m, deterministic)
}
func (dst *AnimationConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnimationConfig.Merge(dst, src)
}
func (m *AnimationConfig) XXX_Size() int {
	return xxx_messageInfo_AnimationConfig.Size(m)
}
func (m *AnimationConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_AnimationConfig.DiscardUnknown(m)
}

var xxx_messageInfo_AnimationConfig proto.InternalMessageInfo

func (m *AnimationConfig) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *AnimationConfig) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

// 稀缺关系CP值对应天数和动画
type RareDayConfig struct {
	Day                  uint32           `protobuf:"varint,1,opt,name=day,proto3" json:"day,omitempty"`
	CpValue              uint32           `protobuf:"varint,2,opt,name=cp_value,json=cpValue,proto3" json:"cp_value,omitempty"`
	Animation            *AnimationConfig `protobuf:"bytes,3,opt,name=animation,proto3" json:"animation,omitempty"`
	BackgroundUrl        string           `protobuf:"bytes,4,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *RareDayConfig) Reset()         { *m = RareDayConfig{} }
func (m *RareDayConfig) String() string { return proto.CompactTextString(m) }
func (*RareDayConfig) ProtoMessage()    {}
func (*RareDayConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{121}
}
func (m *RareDayConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RareDayConfig.Unmarshal(m, b)
}
func (m *RareDayConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RareDayConfig.Marshal(b, m, deterministic)
}
func (dst *RareDayConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RareDayConfig.Merge(dst, src)
}
func (m *RareDayConfig) XXX_Size() int {
	return xxx_messageInfo_RareDayConfig.Size(m)
}
func (m *RareDayConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_RareDayConfig.DiscardUnknown(m)
}

var xxx_messageInfo_RareDayConfig proto.InternalMessageInfo

func (m *RareDayConfig) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

func (m *RareDayConfig) GetCpValue() uint32 {
	if m != nil {
		return m.CpValue
	}
	return 0
}

func (m *RareDayConfig) GetAnimation() *AnimationConfig {
	if m != nil {
		return m.Animation
	}
	return nil
}

func (m *RareDayConfig) GetBackgroundUrl() string {
	if m != nil {
		return m.BackgroundUrl
	}
	return ""
}

type SubRareConfig struct {
	SubRareId            uint32   `protobuf:"varint,1,opt,name=sub_rare_id,json=subRareId,proto3" json:"sub_rare_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	RareFlag             string   `protobuf:"bytes,3,opt,name=rare_flag,json=rareFlag,proto3" json:"rare_flag,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubRareConfig) Reset()         { *m = SubRareConfig{} }
func (m *SubRareConfig) String() string { return proto.CompactTextString(m) }
func (*SubRareConfig) ProtoMessage()    {}
func (*SubRareConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{122}
}
func (m *SubRareConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubRareConfig.Unmarshal(m, b)
}
func (m *SubRareConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubRareConfig.Marshal(b, m, deterministic)
}
func (dst *SubRareConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubRareConfig.Merge(dst, src)
}
func (m *SubRareConfig) XXX_Size() int {
	return xxx_messageInfo_SubRareConfig.Size(m)
}
func (m *SubRareConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_SubRareConfig.DiscardUnknown(m)
}

var xxx_messageInfo_SubRareConfig proto.InternalMessageInfo

func (m *SubRareConfig) GetSubRareId() uint32 {
	if m != nil {
		return m.SubRareId
	}
	return 0
}

func (m *SubRareConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SubRareConfig) GetRareFlag() string {
	if m != nil {
		return m.RareFlag
	}
	return ""
}

type ConnectedForMic struct {
	Left                 string   `protobuf:"bytes,1,opt,name=left,proto3" json:"left,omitempty"`
	Right                string   `protobuf:"bytes,2,opt,name=right,proto3" json:"right,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConnectedForMic) Reset()         { *m = ConnectedForMic{} }
func (m *ConnectedForMic) String() string { return proto.CompactTextString(m) }
func (*ConnectedForMic) ProtoMessage()    {}
func (*ConnectedForMic) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{123}
}
func (m *ConnectedForMic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConnectedForMic.Unmarshal(m, b)
}
func (m *ConnectedForMic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConnectedForMic.Marshal(b, m, deterministic)
}
func (dst *ConnectedForMic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConnectedForMic.Merge(dst, src)
}
func (m *ConnectedForMic) XXX_Size() int {
	return xxx_messageInfo_ConnectedForMic.Size(m)
}
func (m *ConnectedForMic) XXX_DiscardUnknown() {
	xxx_messageInfo_ConnectedForMic.DiscardUnknown(m)
}

var xxx_messageInfo_ConnectedForMic proto.InternalMessageInfo

func (m *ConnectedForMic) GetLeft() string {
	if m != nil {
		return m.Left
	}
	return ""
}

func (m *ConnectedForMic) GetRight() string {
	if m != nil {
		return m.Right
	}
	return ""
}

// MsgNotifyPictures 消息通知图片配置
type MsgNotifyPictures struct {
	Origin               string   `protobuf:"bytes,1,opt,name=origin,proto3" json:"origin,omitempty"`
	Thumbnail            string   `protobuf:"bytes,2,opt,name=thumbnail,proto3" json:"thumbnail,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MsgNotifyPictures) Reset()         { *m = MsgNotifyPictures{} }
func (m *MsgNotifyPictures) String() string { return proto.CompactTextString(m) }
func (*MsgNotifyPictures) ProtoMessage()    {}
func (*MsgNotifyPictures) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{124}
}
func (m *MsgNotifyPictures) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MsgNotifyPictures.Unmarshal(m, b)
}
func (m *MsgNotifyPictures) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MsgNotifyPictures.Marshal(b, m, deterministic)
}
func (dst *MsgNotifyPictures) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MsgNotifyPictures.Merge(dst, src)
}
func (m *MsgNotifyPictures) XXX_Size() int {
	return xxx_messageInfo_MsgNotifyPictures.Size(m)
}
func (m *MsgNotifyPictures) XXX_DiscardUnknown() {
	xxx_messageInfo_MsgNotifyPictures.DiscardUnknown(m)
}

var xxx_messageInfo_MsgNotifyPictures proto.InternalMessageInfo

func (m *MsgNotifyPictures) GetOrigin() string {
	if m != nil {
		return m.Origin
	}
	return ""
}

func (m *MsgNotifyPictures) GetThumbnail() string {
	if m != nil {
		return m.Thumbnail
	}
	return ""
}

type RareConfig struct {
	RareId                uint32             `protobuf:"varint,1,opt,name=rare_id,json=rareId,proto3" json:"rare_id,omitempty"`
	Name                  string             `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	SubRareCfg            []*SubRareConfig   `protobuf:"bytes,3,rep,name=sub_rare_cfg,json=subRareCfg,proto3" json:"sub_rare_cfg,omitempty"`
	CpAnimation           *AnimationConfig   `protobuf:"bytes,4,opt,name=cp_animation,json=cpAnimation,proto3" json:"cp_animation,omitempty"`
	MicConnected          *ConnectedForMic   `protobuf:"bytes,5,opt,name=mic_connected,json=micConnected,proto3" json:"mic_connected,omitempty"`
	RareFlag              string             `protobuf:"bytes,6,opt,name=rare_flag,json=rareFlag,proto3" json:"rare_flag,omitempty"`
	CardColor             string             `protobuf:"bytes,7,opt,name=card_color,json=cardColor,proto3" json:"card_color,omitempty"`
	CpBg                  *FellowBackground  `protobuf:"bytes,8,opt,name=cp_bg,json=cpBg,proto3" json:"cp_bg,omitempty"`
	MidBg                 *FellowBackground  `protobuf:"bytes,9,opt,name=mid_bg,json=midBg,proto3" json:"mid_bg,omitempty"`
	MsgPictrures          *MsgNotifyPictures `protobuf:"bytes,10,opt,name=msg_pictrures,json=msgPictrures,proto3" json:"msg_pictrures,omitempty"`
	FriendSpaceBackground string             `protobuf:"bytes,11,opt,name=friend_space_background,json=friendSpaceBackground,proto3" json:"friend_space_background,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}           `json:"-"`
	XXX_unrecognized      []byte             `json:"-"`
	XXX_sizecache         int32              `json:"-"`
}

func (m *RareConfig) Reset()         { *m = RareConfig{} }
func (m *RareConfig) String() string { return proto.CompactTextString(m) }
func (*RareConfig) ProtoMessage()    {}
func (*RareConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{125}
}
func (m *RareConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RareConfig.Unmarshal(m, b)
}
func (m *RareConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RareConfig.Marshal(b, m, deterministic)
}
func (dst *RareConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RareConfig.Merge(dst, src)
}
func (m *RareConfig) XXX_Size() int {
	return xxx_messageInfo_RareConfig.Size(m)
}
func (m *RareConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_RareConfig.DiscardUnknown(m)
}

var xxx_messageInfo_RareConfig proto.InternalMessageInfo

func (m *RareConfig) GetRareId() uint32 {
	if m != nil {
		return m.RareId
	}
	return 0
}

func (m *RareConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RareConfig) GetSubRareCfg() []*SubRareConfig {
	if m != nil {
		return m.SubRareCfg
	}
	return nil
}

func (m *RareConfig) GetCpAnimation() *AnimationConfig {
	if m != nil {
		return m.CpAnimation
	}
	return nil
}

func (m *RareConfig) GetMicConnected() *ConnectedForMic {
	if m != nil {
		return m.MicConnected
	}
	return nil
}

func (m *RareConfig) GetRareFlag() string {
	if m != nil {
		return m.RareFlag
	}
	return ""
}

func (m *RareConfig) GetCardColor() string {
	if m != nil {
		return m.CardColor
	}
	return ""
}

func (m *RareConfig) GetCpBg() *FellowBackground {
	if m != nil {
		return m.CpBg
	}
	return nil
}

func (m *RareConfig) GetMidBg() *FellowBackground {
	if m != nil {
		return m.MidBg
	}
	return nil
}

func (m *RareConfig) GetMsgPictrures() *MsgNotifyPictures {
	if m != nil {
		return m.MsgPictrures
	}
	return nil
}

func (m *RareConfig) GetFriendSpaceBackground() string {
	if m != nil {
		return m.FriendSpaceBackground
	}
	return ""
}

type GetRareConfigResp struct {
	RareList             []*RareConfig    `protobuf:"bytes,1,rep,name=rare_list,json=rareList,proto3" json:"rare_list,omitempty"`
	DayConfig            []*RareDayConfig `protobuf:"bytes,2,rep,name=day_config,json=dayConfig,proto3" json:"day_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetRareConfigResp) Reset()         { *m = GetRareConfigResp{} }
func (m *GetRareConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetRareConfigResp) ProtoMessage()    {}
func (*GetRareConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{126}
}
func (m *GetRareConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRareConfigResp.Unmarshal(m, b)
}
func (m *GetRareConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRareConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetRareConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRareConfigResp.Merge(dst, src)
}
func (m *GetRareConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetRareConfigResp.Size(m)
}
func (m *GetRareConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRareConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRareConfigResp proto.InternalMessageInfo

func (m *GetRareConfigResp) GetRareList() []*RareConfig {
	if m != nil {
		return m.RareList
	}
	return nil
}

func (m *GetRareConfigResp) GetDayConfig() []*RareDayConfig {
	if m != nil {
		return m.DayConfig
	}
	return nil
}

// 切换绑定的稀缺关系
type SetBindRelationReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	RareId               uint32   `protobuf:"varint,3,opt,name=rare_id,json=rareId,proto3" json:"rare_id,omitempty"`
	SubRareId            uint32   `protobuf:"varint,4,opt,name=sub_rare_id,json=subRareId,proto3" json:"sub_rare_id,omitempty"`
	Bind                 bool     `protobuf:"varint,5,opt,name=bind,proto3" json:"bind,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetBindRelationReq) Reset()         { *m = SetBindRelationReq{} }
func (m *SetBindRelationReq) String() string { return proto.CompactTextString(m) }
func (*SetBindRelationReq) ProtoMessage()    {}
func (*SetBindRelationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{127}
}
func (m *SetBindRelationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBindRelationReq.Unmarshal(m, b)
}
func (m *SetBindRelationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBindRelationReq.Marshal(b, m, deterministic)
}
func (dst *SetBindRelationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBindRelationReq.Merge(dst, src)
}
func (m *SetBindRelationReq) XXX_Size() int {
	return xxx_messageInfo_SetBindRelationReq.Size(m)
}
func (m *SetBindRelationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBindRelationReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetBindRelationReq proto.InternalMessageInfo

func (m *SetBindRelationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetBindRelationReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *SetBindRelationReq) GetRareId() uint32 {
	if m != nil {
		return m.RareId
	}
	return 0
}

func (m *SetBindRelationReq) GetSubRareId() uint32 {
	if m != nil {
		return m.SubRareId
	}
	return 0
}

func (m *SetBindRelationReq) GetBind() bool {
	if m != nil {
		return m.Bind
	}
	return false
}

// 切换绑定的稀缺关系
type SetBindRelationResp struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	RareId               uint32   `protobuf:"varint,3,opt,name=rare_id,json=rareId,proto3" json:"rare_id,omitempty"`
	SubRareId            uint32   `protobuf:"varint,4,opt,name=sub_rare_id,json=subRareId,proto3" json:"sub_rare_id,omitempty"`
	Bind                 bool     `protobuf:"varint,5,opt,name=bind,proto3" json:"bind,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetBindRelationResp) Reset()         { *m = SetBindRelationResp{} }
func (m *SetBindRelationResp) String() string { return proto.CompactTextString(m) }
func (*SetBindRelationResp) ProtoMessage()    {}
func (*SetBindRelationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{128}
}
func (m *SetBindRelationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBindRelationResp.Unmarshal(m, b)
}
func (m *SetBindRelationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBindRelationResp.Marshal(b, m, deterministic)
}
func (dst *SetBindRelationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBindRelationResp.Merge(dst, src)
}
func (m *SetBindRelationResp) XXX_Size() int {
	return xxx_messageInfo_SetBindRelationResp.Size(m)
}
func (m *SetBindRelationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBindRelationResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetBindRelationResp proto.InternalMessageInfo

func (m *SetBindRelationResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetBindRelationResp) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *SetBindRelationResp) GetRareId() uint32 {
	if m != nil {
		return m.RareId
	}
	return 0
}

func (m *SetBindRelationResp) GetSubRareId() uint32 {
	if m != nil {
		return m.SubRareId
	}
	return 0
}

func (m *SetBindRelationResp) GetBind() bool {
	if m != nil {
		return m.Bind
	}
	return false
}

type ChoseRare struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SubRareId            uint32   `protobuf:"varint,2,opt,name=sub_rare_id,json=subRareId,proto3" json:"sub_rare_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChoseRare) Reset()         { *m = ChoseRare{} }
func (m *ChoseRare) String() string { return proto.CompactTextString(m) }
func (*ChoseRare) ProtoMessage()    {}
func (*ChoseRare) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{129}
}
func (m *ChoseRare) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChoseRare.Unmarshal(m, b)
}
func (m *ChoseRare) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChoseRare.Marshal(b, m, deterministic)
}
func (dst *ChoseRare) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChoseRare.Merge(dst, src)
}
func (m *ChoseRare) XXX_Size() int {
	return xxx_messageInfo_ChoseRare.Size(m)
}
func (m *ChoseRare) XXX_DiscardUnknown() {
	xxx_messageInfo_ChoseRare.DiscardUnknown(m)
}

var xxx_messageInfo_ChoseRare proto.InternalMessageInfo

func (m *ChoseRare) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChoseRare) GetSubRareId() uint32 {
	if m != nil {
		return m.SubRareId
	}
	return 0
}

// 选择稀缺关系
type AddRareReq struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ToUid                uint32       `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	RareId               uint32       `protobuf:"varint,3,opt,name=rare_id,json=rareId,proto3" json:"rare_id,omitempty"`
	AddTime              uint32       `protobuf:"varint,4,opt,name=add_time,json=addTime,proto3" json:"add_time,omitempty"`
	GameId               uint32       `protobuf:"varint,5,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	ChannelId            uint32       `protobuf:"varint,6,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UserRare             []*ChoseRare `protobuf:"bytes,7,rep,name=user_rare,json=userRare,proto3" json:"user_rare,omitempty"`
	IsRecorver           bool         `protobuf:"varint,8,opt,name=is_recorver,json=isRecorver,proto3" json:"is_recorver,omitempty"`
	CreateTime           uint32       `protobuf:"varint,9,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AddRareReq) Reset()         { *m = AddRareReq{} }
func (m *AddRareReq) String() string { return proto.CompactTextString(m) }
func (*AddRareReq) ProtoMessage()    {}
func (*AddRareReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{130}
}
func (m *AddRareReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRareReq.Unmarshal(m, b)
}
func (m *AddRareReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRareReq.Marshal(b, m, deterministic)
}
func (dst *AddRareReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRareReq.Merge(dst, src)
}
func (m *AddRareReq) XXX_Size() int {
	return xxx_messageInfo_AddRareReq.Size(m)
}
func (m *AddRareReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRareReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddRareReq proto.InternalMessageInfo

func (m *AddRareReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddRareReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *AddRareReq) GetRareId() uint32 {
	if m != nil {
		return m.RareId
	}
	return 0
}

func (m *AddRareReq) GetAddTime() uint32 {
	if m != nil {
		return m.AddTime
	}
	return 0
}

func (m *AddRareReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *AddRareReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddRareReq) GetUserRare() []*ChoseRare {
	if m != nil {
		return m.UserRare
	}
	return nil
}

func (m *AddRareReq) GetIsRecorver() bool {
	if m != nil {
		return m.IsRecorver
	}
	return false
}

func (m *AddRareReq) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

type AddRareResp struct {
	RareName             string   `protobuf:"bytes,1,opt,name=rare_name,json=rareName,proto3" json:"rare_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddRareResp) Reset()         { *m = AddRareResp{} }
func (m *AddRareResp) String() string { return proto.CompactTextString(m) }
func (*AddRareResp) ProtoMessage()    {}
func (*AddRareResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{131}
}
func (m *AddRareResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRareResp.Unmarshal(m, b)
}
func (m *AddRareResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRareResp.Marshal(b, m, deterministic)
}
func (dst *AddRareResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRareResp.Merge(dst, src)
}
func (m *AddRareResp) XXX_Size() int {
	return xxx_messageInfo_AddRareResp.Size(m)
}
func (m *AddRareResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRareResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddRareResp proto.InternalMessageInfo

func (m *AddRareResp) GetRareName() string {
	if m != nil {
		return m.RareName
	}
	return ""
}

// 获取房间可用稀缺关系配置
type GetChannelRareConfigReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelRareConfigReq) Reset()         { *m = GetChannelRareConfigReq{} }
func (m *GetChannelRareConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelRareConfigReq) ProtoMessage()    {}
func (*GetChannelRareConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{132}
}
func (m *GetChannelRareConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelRareConfigReq.Unmarshal(m, b)
}
func (m *GetChannelRareConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelRareConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelRareConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelRareConfigReq.Merge(dst, src)
}
func (m *GetChannelRareConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelRareConfigReq.Size(m)
}
func (m *GetChannelRareConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelRareConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelRareConfigReq proto.InternalMessageInfo

func (m *GetChannelRareConfigReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelRareConfigResp struct {
	ChannelId            uint32        `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	IntroUrl             string        `protobuf:"bytes,2,opt,name=intro_url,json=introUrl,proto3" json:"intro_url,omitempty"`
	RareList             []*RareConfig `protobuf:"bytes,3,rep,name=rare_list,json=rareList,proto3" json:"rare_list,omitempty"`
	EntranceUrl          string        `protobuf:"bytes,4,opt,name=entrance_url,json=entranceUrl,proto3" json:"entrance_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelRareConfigResp) Reset()         { *m = GetChannelRareConfigResp{} }
func (m *GetChannelRareConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelRareConfigResp) ProtoMessage()    {}
func (*GetChannelRareConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{133}
}
func (m *GetChannelRareConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelRareConfigResp.Unmarshal(m, b)
}
func (m *GetChannelRareConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelRareConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelRareConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelRareConfigResp.Merge(dst, src)
}
func (m *GetChannelRareConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelRareConfigResp.Size(m)
}
func (m *GetChannelRareConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelRareConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelRareConfigResp proto.InternalMessageInfo

func (m *GetChannelRareConfigResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelRareConfigResp) GetIntroUrl() string {
	if m != nil {
		return m.IntroUrl
	}
	return ""
}

func (m *GetChannelRareConfigResp) GetRareList() []*RareConfig {
	if m != nil {
		return m.RareList
	}
	return nil
}

func (m *GetChannelRareConfigResp) GetEntranceUrl() string {
	if m != nil {
		return m.EntranceUrl
	}
	return ""
}

type GetFromAllRelationshipByIdsReq struct {
	Ids                  []uint32 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFromAllRelationshipByIdsReq) Reset()         { *m = GetFromAllRelationshipByIdsReq{} }
func (m *GetFromAllRelationshipByIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetFromAllRelationshipByIdsReq) ProtoMessage()    {}
func (*GetFromAllRelationshipByIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{134}
}
func (m *GetFromAllRelationshipByIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFromAllRelationshipByIdsReq.Unmarshal(m, b)
}
func (m *GetFromAllRelationshipByIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFromAllRelationshipByIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetFromAllRelationshipByIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFromAllRelationshipByIdsReq.Merge(dst, src)
}
func (m *GetFromAllRelationshipByIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetFromAllRelationshipByIdsReq.Size(m)
}
func (m *GetFromAllRelationshipByIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFromAllRelationshipByIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFromAllRelationshipByIdsReq proto.InternalMessageInfo

func (m *GetFromAllRelationshipByIdsReq) GetIds() []uint32 {
	if m != nil {
		return m.Ids
	}
	return nil
}

type GetFromAllRelationshipByIdsResp struct {
	RelationList         []*Relationship `protobuf:"bytes,1,rep,name=relation_list,json=relationList,proto3" json:"relation_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetFromAllRelationshipByIdsResp) Reset()         { *m = GetFromAllRelationshipByIdsResp{} }
func (m *GetFromAllRelationshipByIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetFromAllRelationshipByIdsResp) ProtoMessage()    {}
func (*GetFromAllRelationshipByIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{135}
}
func (m *GetFromAllRelationshipByIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFromAllRelationshipByIdsResp.Unmarshal(m, b)
}
func (m *GetFromAllRelationshipByIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFromAllRelationshipByIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetFromAllRelationshipByIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFromAllRelationshipByIdsResp.Merge(dst, src)
}
func (m *GetFromAllRelationshipByIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetFromAllRelationshipByIdsResp.Size(m)
}
func (m *GetFromAllRelationshipByIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFromAllRelationshipByIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFromAllRelationshipByIdsResp proto.InternalMessageInfo

func (m *GetFromAllRelationshipByIdsResp) GetRelationList() []*Relationship {
	if m != nil {
		return m.RelationList
	}
	return nil
}

type GetRareTagsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRareTagsReq) Reset()         { *m = GetRareTagsReq{} }
func (m *GetRareTagsReq) String() string { return proto.CompactTextString(m) }
func (*GetRareTagsReq) ProtoMessage()    {}
func (*GetRareTagsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{136}
}
func (m *GetRareTagsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRareTagsReq.Unmarshal(m, b)
}
func (m *GetRareTagsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRareTagsReq.Marshal(b, m, deterministic)
}
func (dst *GetRareTagsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRareTagsReq.Merge(dst, src)
}
func (m *GetRareTagsReq) XXX_Size() int {
	return xxx_messageInfo_GetRareTagsReq.Size(m)
}
func (m *GetRareTagsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRareTagsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRareTagsReq proto.InternalMessageInfo

func (m *GetRareTagsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRareTagsReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

type GetRareTagsResp struct {
	Tags                 []string `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRareTagsResp) Reset()         { *m = GetRareTagsResp{} }
func (m *GetRareTagsResp) String() string { return proto.CompactTextString(m) }
func (*GetRareTagsResp) ProtoMessage()    {}
func (*GetRareTagsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{137}
}
func (m *GetRareTagsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRareTagsResp.Unmarshal(m, b)
}
func (m *GetRareTagsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRareTagsResp.Marshal(b, m, deterministic)
}
func (dst *GetRareTagsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRareTagsResp.Merge(dst, src)
}
func (m *GetRareTagsResp) XXX_Size() int {
	return xxx_messageInfo_GetRareTagsResp.Size(m)
}
func (m *GetRareTagsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRareTagsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRareTagsResp proto.InternalMessageInfo

func (m *GetRareTagsResp) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

// 获取两个UID的所有稀缺关系列表
type GetRareListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRareListReq) Reset()         { *m = GetRareListReq{} }
func (m *GetRareListReq) String() string { return proto.CompactTextString(m) }
func (*GetRareListReq) ProtoMessage()    {}
func (*GetRareListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{138}
}
func (m *GetRareListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRareListReq.Unmarshal(m, b)
}
func (m *GetRareListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRareListReq.Marshal(b, m, deterministic)
}
func (dst *GetRareListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRareListReq.Merge(dst, src)
}
func (m *GetRareListReq) XXX_Size() int {
	return xxx_messageInfo_GetRareListReq.Size(m)
}
func (m *GetRareListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRareListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRareListReq proto.InternalMessageInfo

func (m *GetRareListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRareListReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

type GetRareListResp struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ToUid                uint32            `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ToAccount            string            `protobuf:"bytes,3,opt,name=to_account,json=toAccount,proto3" json:"to_account,omitempty"`
	ToNickName           string            `protobuf:"bytes,4,opt,name=to_nick_name,json=toNickName,proto3" json:"to_nick_name,omitempty"`
	BindType             uint32            `protobuf:"varint,5,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	FellowType           uint32            `protobuf:"varint,6,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	FellowName           string            `protobuf:"bytes,7,opt,name=fellow_name,json=fellowName,proto3" json:"fellow_name,omitempty"`
	RareList             []*RareInfo       `protobuf:"bytes,8,rep,name=rare_list,json=rareList,proto3" json:"rare_list,omitempty"`
	PresentBg            *FellowBackground `protobuf:"bytes,9,opt,name=present_bg,json=presentBg,proto3" json:"present_bg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetRareListResp) Reset()         { *m = GetRareListResp{} }
func (m *GetRareListResp) String() string { return proto.CompactTextString(m) }
func (*GetRareListResp) ProtoMessage()    {}
func (*GetRareListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{139}
}
func (m *GetRareListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRareListResp.Unmarshal(m, b)
}
func (m *GetRareListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRareListResp.Marshal(b, m, deterministic)
}
func (dst *GetRareListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRareListResp.Merge(dst, src)
}
func (m *GetRareListResp) XXX_Size() int {
	return xxx_messageInfo_GetRareListResp.Size(m)
}
func (m *GetRareListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRareListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRareListResp proto.InternalMessageInfo

func (m *GetRareListResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRareListResp) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *GetRareListResp) GetToAccount() string {
	if m != nil {
		return m.ToAccount
	}
	return ""
}

func (m *GetRareListResp) GetToNickName() string {
	if m != nil {
		return m.ToNickName
	}
	return ""
}

func (m *GetRareListResp) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *GetRareListResp) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *GetRareListResp) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

func (m *GetRareListResp) GetRareList() []*RareInfo {
	if m != nil {
		return m.RareList
	}
	return nil
}

func (m *GetRareListResp) GetPresentBg() *FellowBackground {
	if m != nil {
		return m.PresentBg
	}
	return nil
}

type DelRareReq struct {
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,3,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	RareId               uint32   `protobuf:"varint,4,opt,name=rare_id,json=rareId,proto3" json:"rare_id,omitempty"`
	SubRareId            uint32   `protobuf:"varint,5,opt,name=sub_rare_id,json=subRareId,proto3" json:"sub_rare_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelRareReq) Reset()         { *m = DelRareReq{} }
func (m *DelRareReq) String() string { return proto.CompactTextString(m) }
func (*DelRareReq) ProtoMessage()    {}
func (*DelRareReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{140}
}
func (m *DelRareReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelRareReq.Unmarshal(m, b)
}
func (m *DelRareReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelRareReq.Marshal(b, m, deterministic)
}
func (dst *DelRareReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelRareReq.Merge(dst, src)
}
func (m *DelRareReq) XXX_Size() int {
	return xxx_messageInfo_DelRareReq.Size(m)
}
func (m *DelRareReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelRareReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelRareReq proto.InternalMessageInfo

func (m *DelRareReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DelRareReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *DelRareReq) GetRareId() uint32 {
	if m != nil {
		return m.RareId
	}
	return 0
}

func (m *DelRareReq) GetSubRareId() uint32 {
	if m != nil {
		return m.SubRareId
	}
	return 0
}

type DelRareResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelRareResp) Reset()         { *m = DelRareResp{} }
func (m *DelRareResp) String() string { return proto.CompactTextString(m) }
func (*DelRareResp) ProtoMessage()    {}
func (*DelRareResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{141}
}
func (m *DelRareResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelRareResp.Unmarshal(m, b)
}
func (m *DelRareResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelRareResp.Marshal(b, m, deterministic)
}
func (dst *DelRareResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelRareResp.Merge(dst, src)
}
func (m *DelRareResp) XXX_Size() int {
	return xxx_messageInfo_DelRareResp.Size(m)
}
func (m *DelRareResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelRareResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelRareResp proto.InternalMessageInfo

type FellowPointDelayItem struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	Point                float32  `protobuf:"fixed32,3,opt,name=point,proto3" json:"point,omitempty"`
	Reason               string   `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	AddType              uint32   `protobuf:"varint,5,opt,name=add_type,json=addType,proto3" json:"add_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowPointDelayItem) Reset()         { *m = FellowPointDelayItem{} }
func (m *FellowPointDelayItem) String() string { return proto.CompactTextString(m) }
func (*FellowPointDelayItem) ProtoMessage()    {}
func (*FellowPointDelayItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{142}
}
func (m *FellowPointDelayItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowPointDelayItem.Unmarshal(m, b)
}
func (m *FellowPointDelayItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowPointDelayItem.Marshal(b, m, deterministic)
}
func (dst *FellowPointDelayItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowPointDelayItem.Merge(dst, src)
}
func (m *FellowPointDelayItem) XXX_Size() int {
	return xxx_messageInfo_FellowPointDelayItem.Size(m)
}
func (m *FellowPointDelayItem) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowPointDelayItem.DiscardUnknown(m)
}

var xxx_messageInfo_FellowPointDelayItem proto.InternalMessageInfo

func (m *FellowPointDelayItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FellowPointDelayItem) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *FellowPointDelayItem) GetPoint() float32 {
	if m != nil {
		return m.Point
	}
	return 0
}

func (m *FellowPointDelayItem) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *FellowPointDelayItem) GetAddType() uint32 {
	if m != nil {
		return m.AddType
	}
	return 0
}

type AddFellowPointDelayReq struct {
	DelayItems           []*FellowPointDelayItem `protobuf:"bytes,1,rep,name=delay_items,json=delayItems,proto3" json:"delay_items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AddFellowPointDelayReq) Reset()         { *m = AddFellowPointDelayReq{} }
func (m *AddFellowPointDelayReq) String() string { return proto.CompactTextString(m) }
func (*AddFellowPointDelayReq) ProtoMessage()    {}
func (*AddFellowPointDelayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{143}
}
func (m *AddFellowPointDelayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFellowPointDelayReq.Unmarshal(m, b)
}
func (m *AddFellowPointDelayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFellowPointDelayReq.Marshal(b, m, deterministic)
}
func (dst *AddFellowPointDelayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFellowPointDelayReq.Merge(dst, src)
}
func (m *AddFellowPointDelayReq) XXX_Size() int {
	return xxx_messageInfo_AddFellowPointDelayReq.Size(m)
}
func (m *AddFellowPointDelayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFellowPointDelayReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddFellowPointDelayReq proto.InternalMessageInfo

func (m *AddFellowPointDelayReq) GetDelayItems() []*FellowPointDelayItem {
	if m != nil {
		return m.DelayItems
	}
	return nil
}

type AddFellowPointDelayResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddFellowPointDelayResp) Reset()         { *m = AddFellowPointDelayResp{} }
func (m *AddFellowPointDelayResp) String() string { return proto.CompactTextString(m) }
func (*AddFellowPointDelayResp) ProtoMessage()    {}
func (*AddFellowPointDelayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{144}
}
func (m *AddFellowPointDelayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFellowPointDelayResp.Unmarshal(m, b)
}
func (m *AddFellowPointDelayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFellowPointDelayResp.Marshal(b, m, deterministic)
}
func (dst *AddFellowPointDelayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFellowPointDelayResp.Merge(dst, src)
}
func (m *AddFellowPointDelayResp) XXX_Size() int {
	return xxx_messageInfo_AddFellowPointDelayResp.Size(m)
}
func (m *AddFellowPointDelayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFellowPointDelayResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddFellowPointDelayResp proto.InternalMessageInfo

type TestUpgradeImMsgReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	Level                uint32   `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestUpgradeImMsgReq) Reset()         { *m = TestUpgradeImMsgReq{} }
func (m *TestUpgradeImMsgReq) String() string { return proto.CompactTextString(m) }
func (*TestUpgradeImMsgReq) ProtoMessage()    {}
func (*TestUpgradeImMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{145}
}
func (m *TestUpgradeImMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestUpgradeImMsgReq.Unmarshal(m, b)
}
func (m *TestUpgradeImMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestUpgradeImMsgReq.Marshal(b, m, deterministic)
}
func (dst *TestUpgradeImMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestUpgradeImMsgReq.Merge(dst, src)
}
func (m *TestUpgradeImMsgReq) XXX_Size() int {
	return xxx_messageInfo_TestUpgradeImMsgReq.Size(m)
}
func (m *TestUpgradeImMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestUpgradeImMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestUpgradeImMsgReq proto.InternalMessageInfo

func (m *TestUpgradeImMsgReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TestUpgradeImMsgReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *TestUpgradeImMsgReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *TestUpgradeImMsgReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type TestUpgradeImMsgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestUpgradeImMsgResp) Reset()         { *m = TestUpgradeImMsgResp{} }
func (m *TestUpgradeImMsgResp) String() string { return proto.CompactTextString(m) }
func (*TestUpgradeImMsgResp) ProtoMessage()    {}
func (*TestUpgradeImMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{146}
}
func (m *TestUpgradeImMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestUpgradeImMsgResp.Unmarshal(m, b)
}
func (m *TestUpgradeImMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestUpgradeImMsgResp.Marshal(b, m, deterministic)
}
func (dst *TestUpgradeImMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestUpgradeImMsgResp.Merge(dst, src)
}
func (m *TestUpgradeImMsgResp) XXX_Size() int {
	return xxx_messageInfo_TestUpgradeImMsgResp.Size(m)
}
func (m *TestUpgradeImMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestUpgradeImMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestUpgradeImMsgResp proto.InternalMessageInfo

type TestSetFellowLevelReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	Level                uint32   `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestSetFellowLevelReq) Reset()         { *m = TestSetFellowLevelReq{} }
func (m *TestSetFellowLevelReq) String() string { return proto.CompactTextString(m) }
func (*TestSetFellowLevelReq) ProtoMessage()    {}
func (*TestSetFellowLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{147}
}
func (m *TestSetFellowLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestSetFellowLevelReq.Unmarshal(m, b)
}
func (m *TestSetFellowLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestSetFellowLevelReq.Marshal(b, m, deterministic)
}
func (dst *TestSetFellowLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestSetFellowLevelReq.Merge(dst, src)
}
func (m *TestSetFellowLevelReq) XXX_Size() int {
	return xxx_messageInfo_TestSetFellowLevelReq.Size(m)
}
func (m *TestSetFellowLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestSetFellowLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestSetFellowLevelReq proto.InternalMessageInfo

func (m *TestSetFellowLevelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TestSetFellowLevelReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *TestSetFellowLevelReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type TestSetFellowLevelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestSetFellowLevelResp) Reset()         { *m = TestSetFellowLevelResp{} }
func (m *TestSetFellowLevelResp) String() string { return proto.CompactTextString(m) }
func (*TestSetFellowLevelResp) ProtoMessage()    {}
func (*TestSetFellowLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{148}
}
func (m *TestSetFellowLevelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestSetFellowLevelResp.Unmarshal(m, b)
}
func (m *TestSetFellowLevelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestSetFellowLevelResp.Marshal(b, m, deterministic)
}
func (dst *TestSetFellowLevelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestSetFellowLevelResp.Merge(dst, src)
}
func (m *TestSetFellowLevelResp) XXX_Size() int {
	return xxx_messageInfo_TestSetFellowLevelResp.Size(m)
}
func (m *TestSetFellowLevelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestSetFellowLevelResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestSetFellowLevelResp proto.InternalMessageInfo

type GetTopNFellowReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopNFellowReq) Reset()         { *m = GetTopNFellowReq{} }
func (m *GetTopNFellowReq) String() string { return proto.CompactTextString(m) }
func (*GetTopNFellowReq) ProtoMessage()    {}
func (*GetTopNFellowReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{149}
}
func (m *GetTopNFellowReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopNFellowReq.Unmarshal(m, b)
}
func (m *GetTopNFellowReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopNFellowReq.Marshal(b, m, deterministic)
}
func (dst *GetTopNFellowReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopNFellowReq.Merge(dst, src)
}
func (m *GetTopNFellowReq) XXX_Size() int {
	return xxx_messageInfo_GetTopNFellowReq.Size(m)
}
func (m *GetTopNFellowReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopNFellowReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopNFellowReq proto.InternalMessageInfo

func (m *GetTopNFellowReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetTopNFellowResp struct {
	TopNFellowList       []*TopNFellowInfo `protobuf:"bytes,1,rep,name=top_n_fellow_list,json=topNFellowList,proto3" json:"top_n_fellow_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetTopNFellowResp) Reset()         { *m = GetTopNFellowResp{} }
func (m *GetTopNFellowResp) String() string { return proto.CompactTextString(m) }
func (*GetTopNFellowResp) ProtoMessage()    {}
func (*GetTopNFellowResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{150}
}
func (m *GetTopNFellowResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopNFellowResp.Unmarshal(m, b)
}
func (m *GetTopNFellowResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopNFellowResp.Marshal(b, m, deterministic)
}
func (dst *GetTopNFellowResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopNFellowResp.Merge(dst, src)
}
func (m *GetTopNFellowResp) XXX_Size() int {
	return xxx_messageInfo_GetTopNFellowResp.Size(m)
}
func (m *GetTopNFellowResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopNFellowResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopNFellowResp proto.InternalMessageInfo

func (m *GetTopNFellowResp) GetTopNFellowList() []*TopNFellowInfo {
	if m != nil {
		return m.TopNFellowList
	}
	return nil
}

type TopNFellowInfo struct {
	FellowUid            uint32   `protobuf:"varint,1,opt,name=fellow_uid,json=fellowUid,proto3" json:"fellow_uid,omitempty"`
	FellowVal            uint32   `protobuf:"varint,2,opt,name=fellow_val,json=fellowVal,proto3" json:"fellow_val,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopNFellowInfo) Reset()         { *m = TopNFellowInfo{} }
func (m *TopNFellowInfo) String() string { return proto.CompactTextString(m) }
func (*TopNFellowInfo) ProtoMessage()    {}
func (*TopNFellowInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_svr_077a1558472899a7, []int{151}
}
func (m *TopNFellowInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopNFellowInfo.Unmarshal(m, b)
}
func (m *TopNFellowInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopNFellowInfo.Marshal(b, m, deterministic)
}
func (dst *TopNFellowInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopNFellowInfo.Merge(dst, src)
}
func (m *TopNFellowInfo) XXX_Size() int {
	return xxx_messageInfo_TopNFellowInfo.Size(m)
}
func (m *TopNFellowInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TopNFellowInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TopNFellowInfo proto.InternalMessageInfo

func (m *TopNFellowInfo) GetFellowUid() uint32 {
	if m != nil {
		return m.FellowUid
	}
	return 0
}

func (m *TopNFellowInfo) GetFellowVal() uint32 {
	if m != nil {
		return m.FellowVal
	}
	return 0
}

func init() {
	proto.RegisterType((*PlateUrl)(nil), "fellow_svr.PlateUrl")
	proto.RegisterType((*FellowBackground)(nil), "fellow_svr.FellowBackground")
	proto.RegisterType((*RareInfo)(nil), "fellow_svr.RareInfo")
	proto.RegisterType((*RareTabInfo)(nil), "fellow_svr.RareTabInfo")
	proto.RegisterType((*FellowInfo)(nil), "fellow_svr.FellowInfo")
	proto.RegisterType((*FellowTypeInfo)(nil), "fellow_svr.FellowTypeInfo")
	proto.RegisterType((*FellowInviteUser)(nil), "fellow_svr.FellowInviteUser")
	proto.RegisterType((*FellowPresentInfo)(nil), "fellow_svr.FellowPresentInfo")
	proto.RegisterType((*FellowInviteInfo)(nil), "fellow_svr.FellowInviteInfo")
	proto.RegisterType((*GetFellowListReq)(nil), "fellow_svr.GetFellowListReq")
	proto.RegisterType((*GetFellowListResp)(nil), "fellow_svr.GetFellowListResp")
	proto.RegisterType((*GetFellowSimpleInfoReq)(nil), "fellow_svr.GetFellowSimpleInfoReq")
	proto.RegisterType((*GetFellowSimpleInfoResp)(nil), "fellow_svr.GetFellowSimpleInfoResp")
	proto.RegisterType((*FellowPair)(nil), "fellow_svr.FellowPair")
	proto.RegisterType((*BatchGetFellowSimpleInfoByPairListReq)(nil), "fellow_svr.BatchGetFellowSimpleInfoByPairListReq")
	proto.RegisterType((*SimpleFellowInfo)(nil), "fellow_svr.SimpleFellowInfo")
	proto.RegisterType((*BatchGetFellowSimpleInfoByPairListResp)(nil), "fellow_svr.BatchGetFellowSimpleInfoByPairListResp")
	proto.RegisterType((*UnlockInfo)(nil), "fellow_svr.UnlockInfo")
	proto.RegisterType((*GetFellowCandidateListReq)(nil), "fellow_svr.GetFellowCandidateListReq")
	proto.RegisterType((*GetFellowCandidateListResp)(nil), "fellow_svr.GetFellowCandidateListResp")
	proto.RegisterType((*GetFellowCandidateInfoReq)(nil), "fellow_svr.GetFellowCandidateInfoReq")
	proto.RegisterType((*GetFellowCandidateInfoResp)(nil), "fellow_svr.GetFellowCandidateInfoResp")
	proto.RegisterType((*SendFellowInviteReq)(nil), "fellow_svr.SendFellowInviteReq")
	proto.RegisterType((*SendFellowInviteResp)(nil), "fellow_svr.SendFellowInviteResp")
	proto.RegisterType((*GetFellowInviteListReq)(nil), "fellow_svr.GetFellowInviteListReq")
	proto.RegisterType((*GetFellowInviteListResp)(nil), "fellow_svr.GetFellowInviteListResp")
	proto.RegisterType((*GetFellowInviteInfoByIdReq)(nil), "fellow_svr.GetFellowInviteInfoByIdReq")
	proto.RegisterType((*GetFellowInviteInfoByIdResp)(nil), "fellow_svr.GetFellowInviteInfoByIdResp")
	proto.RegisterType((*ServiceCtrlInfo)(nil), "fellow_svr.ServiceCtrlInfo")
	proto.RegisterType((*HandleFellowInviteReq)(nil), "fellow_svr.HandleFellowInviteReq")
	proto.RegisterType((*HandleFellowInviteResp)(nil), "fellow_svr.HandleFellowInviteResp")
	proto.RegisterType((*GetWebFellowListReq)(nil), "fellow_svr.GetWebFellowListReq")
	proto.RegisterType((*FellowTask)(nil), "fellow_svr.FellowTask")
	proto.RegisterType((*FellowLevelConfig)(nil), "fellow_svr.FellowLevelConfig")
	proto.RegisterType((*GetWebFellowListResp)(nil), "fellow_svr.GetWebFellowListResp")
	proto.RegisterType((*CheckFellowInviteReq)(nil), "fellow_svr.CheckFellowInviteReq")
	proto.RegisterType((*CheckFellowInviteResp)(nil), "fellow_svr.CheckFellowInviteResp")
	proto.RegisterType((*GetFellowPointReq)(nil), "fellow_svr.GetFellowPointReq")
	proto.RegisterType((*GetFellowPointResp)(nil), "fellow_svr.GetFellowPointResp")
	proto.RegisterType((*GradingInfo)(nil), "fellow_svr.GradingInfo")
	proto.RegisterType((*CancelFellowInviteReq)(nil), "fellow_svr.CancelFellowInviteReq")
	proto.RegisterType((*CancelFellowInviteResp)(nil), "fellow_svr.CancelFellowInviteResp")
	proto.RegisterType((*FellowPresentConfig)(nil), "fellow_svr.FellowPresentConfig")
	proto.RegisterType((*AddFellowPresentConfigReq)(nil), "fellow_svr.AddFellowPresentConfigReq")
	proto.RegisterType((*AddFellowPresentConfigResp)(nil), "fellow_svr.AddFellowPresentConfigResp")
	proto.RegisterType((*UpdateFellowPresentConfigReq)(nil), "fellow_svr.UpdateFellowPresentConfigReq")
	proto.RegisterType((*UpdateFellowPresentConfigResp)(nil), "fellow_svr.UpdateFellowPresentConfigResp")
	proto.RegisterType((*DelFellowPresentConfigReq)(nil), "fellow_svr.DelFellowPresentConfigReq")
	proto.RegisterType((*DelFellowPresentConfigResp)(nil), "fellow_svr.DelFellowPresentConfigResp")
	proto.RegisterType((*GetFellowPresentConfigByIdReq)(nil), "fellow_svr.GetFellowPresentConfigByIdReq")
	proto.RegisterType((*GetFellowPresentConfigByIdResp)(nil), "fellow_svr.GetFellowPresentConfigByIdResp")
	proto.RegisterType((*GetAllFellowPresentConfigReq)(nil), "fellow_svr.GetAllFellowPresentConfigReq")
	proto.RegisterType((*GetAllFellowPresentConfigResp)(nil), "fellow_svr.GetAllFellowPresentConfigResp")
	proto.RegisterType((*UnlockFellowSiteReq)(nil), "fellow_svr.UnlockFellowSiteReq")
	proto.RegisterType((*UnlockFellowSiteResp)(nil), "fellow_svr.UnlockFellowSiteResp")
	proto.RegisterType((*UnboundFellowReq)(nil), "fellow_svr.UnboundFellowReq")
	proto.RegisterType((*UnboundFellowResp)(nil), "fellow_svr.UnboundFellowResp")
	proto.RegisterType((*CancelUnboundFellowReq)(nil), "fellow_svr.CancelUnboundFellowReq")
	proto.RegisterType((*CancelUnboundFellowResp)(nil), "fellow_svr.CancelUnboundFellowResp")
	proto.RegisterType((*ChangeFellowBindTypeReq)(nil), "fellow_svr.ChangeFellowBindTypeReq")
	proto.RegisterType((*ChangeFellowBindTypeResp)(nil), "fellow_svr.ChangeFellowBindTypeResp")
	proto.RegisterType((*SendFellowPresentReq)(nil), "fellow_svr.SendFellowPresentReq")
	proto.RegisterType((*SendFellowPresentResp)(nil), "fellow_svr.SendFellowPresentResp")
	proto.RegisterType((*PresentSendItemSimpleInfo)(nil), "fellow_svr.PresentSendItemSimpleInfo")
	proto.RegisterType((*GetFellowPresentDetailReq)(nil), "fellow_svr.GetFellowPresentDetailReq")
	proto.RegisterType((*GetFellowPresentDetailResp)(nil), "fellow_svr.GetFellowPresentDetailResp")
	proto.RegisterType((*GetFellowTypeReq)(nil), "fellow_svr.GetFellowTypeReq")
	proto.RegisterType((*GetFellowTypeResp)(nil), "fellow_svr.GetFellowTypeResp")
	proto.RegisterType((*GetHistoryFellowTypeReq)(nil), "fellow_svr.GetHistoryFellowTypeReq")
	proto.RegisterType((*GetHistoryFellowTypeResp)(nil), "fellow_svr.GetHistoryFellowTypeResp")
	proto.RegisterType((*GetFellowInfoByUidReq)(nil), "fellow_svr.GetFellowInfoByUidReq")
	proto.RegisterType((*GetFellowInfoByUidResp)(nil), "fellow_svr.GetFellowInfoByUidResp")
	proto.RegisterType((*ChannelSendFellowPresentReq)(nil), "fellow_svr.ChannelSendFellowPresentReq")
	proto.RegisterType((*ChannelSendFellowPresentResp)(nil), "fellow_svr.ChannelSendFellowPresentResp")
	proto.RegisterType((*SendChannelFellowInviteReq)(nil), "fellow_svr.SendChannelFellowInviteReq")
	proto.RegisterType((*SendChannelFellowInviteResp)(nil), "fellow_svr.SendChannelFellowInviteResp")
	proto.RegisterType((*HandleChannelFellowInviteReq)(nil), "fellow_svr.HandleChannelFellowInviteReq")
	proto.RegisterType((*HandleChannelFellowInviteResp)(nil), "fellow_svr.HandleChannelFellowInviteResp")
	proto.RegisterType((*ChannelFellowMsg)(nil), "fellow_svr.ChannelFellowMsg")
	proto.RegisterType((*GetRoomFellowListReq)(nil), "fellow_svr.GetRoomFellowListReq")
	proto.RegisterType((*GetRoomFellowListResp)(nil), "fellow_svr.GetRoomFellowListResp")
	proto.RegisterType((*GetAllChannelFellowInviteReq)(nil), "fellow_svr.GetAllChannelFellowInviteReq")
	proto.RegisterType((*GetAllChannelFellowInviteResp)(nil), "fellow_svr.GetAllChannelFellowInviteResp")
	proto.RegisterType((*DirectUnboundFellowReq)(nil), "fellow_svr.DirectUnboundFellowReq")
	proto.RegisterType((*DirectUnboundFellowResp)(nil), "fellow_svr.DirectUnboundFellowResp")
	proto.RegisterType((*GetOnMicFellowListReq)(nil), "fellow_svr.GetOnMicFellowListReq")
	proto.RegisterType((*MicFellowInfo)(nil), "fellow_svr.MicFellowInfo")
	proto.RegisterType((*MicFellowInfoChangeInfo)(nil), "fellow_svr.MicFellowInfoChangeInfo")
	proto.RegisterType((*GetOnMicFellowListResp)(nil), "fellow_svr.GetOnMicFellowListResp")
	proto.RegisterType((*GetChannelFellowCandidateInfoReq)(nil), "fellow_svr.GetChannelFellowCandidateInfoReq")
	proto.RegisterType((*FellowOptionInfo)(nil), "fellow_svr.FellowOptionInfo")
	proto.RegisterType((*GetChannelFellowCandidateInfoResp)(nil), "fellow_svr.GetChannelFellowCandidateInfoResp")
	proto.RegisterType((*GetSendInviteListReq)(nil), "fellow_svr.GetSendInviteListReq")
	proto.RegisterType((*GetSendInviteListResp)(nil), "fellow_svr.GetSendInviteListResp")
	proto.RegisterType((*Resource)(nil), "fellow_svr.Resource")
	proto.RegisterType((*GradientColor)(nil), "fellow_svr.GradientColor")
	proto.RegisterType((*RelationshipBox)(nil), "fellow_svr.RelationshipBox")
	proto.RegisterType((*MsgNotifyImg)(nil), "fellow_svr.MsgNotifyImg")
	proto.RegisterType((*ConnectedStringForMic)(nil), "fellow_svr.ConnectedStringForMic")
	proto.RegisterType((*Relationship)(nil), "fellow_svr.Relationship")
	proto.RegisterType((*ChannelRelationshipBinding)(nil), "fellow_svr.ChannelRelationshipBinding")
	proto.RegisterType((*ChannelRelationshipBinding_RelationshipSimpleInfo)(nil), "fellow_svr.ChannelRelationshipBinding.RelationshipSimpleInfo")
	proto.RegisterType((*ChannelRelationshipBinding_Introduction)(nil), "fellow_svr.ChannelRelationshipBinding.Introduction")
	proto.RegisterType((*RelationshipListReq)(nil), "fellow_svr.RelationshipListReq")
	proto.RegisterType((*RelationshipListResp)(nil), "fellow_svr.RelationshipListResp")
	proto.RegisterType((*RelationshipGetReq)(nil), "fellow_svr.RelationshipGetReq")
	proto.RegisterType((*RelationshipGetResp)(nil), "fellow_svr.RelationshipGetResp")
	proto.RegisterType((*RelationshipAddReq)(nil), "fellow_svr.RelationshipAddReq")
	proto.RegisterType((*RelationshipAddResp)(nil), "fellow_svr.RelationshipAddResp")
	proto.RegisterType((*RelationshipUpdateReq)(nil), "fellow_svr.RelationshipUpdateReq")
	proto.RegisterType((*RelationshipUpdateResp)(nil), "fellow_svr.RelationshipUpdateResp")
	proto.RegisterType((*RelationshipDeleteReq)(nil), "fellow_svr.RelationshipDeleteReq")
	proto.RegisterType((*RelationshipDeleteResp)(nil), "fellow_svr.RelationshipDeleteResp")
	proto.RegisterType((*ChannelRelationshipBindingListReq)(nil), "fellow_svr.ChannelRelationshipBindingListReq")
	proto.RegisterType((*ChannelRelationshipBindingListResp)(nil), "fellow_svr.ChannelRelationshipBindingListResp")
	proto.RegisterType((*ChannelRelationshipBindingAddReq)(nil), "fellow_svr.ChannelRelationshipBindingAddReq")
	proto.RegisterType((*ChannelRelationshipBindingAddResp)(nil), "fellow_svr.ChannelRelationshipBindingAddResp")
	proto.RegisterType((*ChannelRelationshipBindingUpdateReq)(nil), "fellow_svr.ChannelRelationshipBindingUpdateReq")
	proto.RegisterType((*ChannelRelationshipBindingUpdateResp)(nil), "fellow_svr.ChannelRelationshipBindingUpdateResp")
	proto.RegisterType((*ChannelRelationshipBindingDeleteReq)(nil), "fellow_svr.ChannelRelationshipBindingDeleteReq")
	proto.RegisterType((*ChannelRelationshipBindingDeleteResp)(nil), "fellow_svr.ChannelRelationshipBindingDeleteResp")
	proto.RegisterType((*GetRareConfigReq)(nil), "fellow_svr.GetRareConfigReq")
	proto.RegisterType((*AnimationConfig)(nil), "fellow_svr.AnimationConfig")
	proto.RegisterType((*RareDayConfig)(nil), "fellow_svr.RareDayConfig")
	proto.RegisterType((*SubRareConfig)(nil), "fellow_svr.SubRareConfig")
	proto.RegisterType((*ConnectedForMic)(nil), "fellow_svr.ConnectedForMic")
	proto.RegisterType((*MsgNotifyPictures)(nil), "fellow_svr.MsgNotifyPictures")
	proto.RegisterType((*RareConfig)(nil), "fellow_svr.RareConfig")
	proto.RegisterType((*GetRareConfigResp)(nil), "fellow_svr.GetRareConfigResp")
	proto.RegisterType((*SetBindRelationReq)(nil), "fellow_svr.SetBindRelationReq")
	proto.RegisterType((*SetBindRelationResp)(nil), "fellow_svr.SetBindRelationResp")
	proto.RegisterType((*ChoseRare)(nil), "fellow_svr.ChoseRare")
	proto.RegisterType((*AddRareReq)(nil), "fellow_svr.AddRareReq")
	proto.RegisterType((*AddRareResp)(nil), "fellow_svr.AddRareResp")
	proto.RegisterType((*GetChannelRareConfigReq)(nil), "fellow_svr.GetChannelRareConfigReq")
	proto.RegisterType((*GetChannelRareConfigResp)(nil), "fellow_svr.GetChannelRareConfigResp")
	proto.RegisterType((*GetFromAllRelationshipByIdsReq)(nil), "fellow_svr.GetFromAllRelationshipByIdsReq")
	proto.RegisterType((*GetFromAllRelationshipByIdsResp)(nil), "fellow_svr.GetFromAllRelationshipByIdsResp")
	proto.RegisterType((*GetRareTagsReq)(nil), "fellow_svr.GetRareTagsReq")
	proto.RegisterType((*GetRareTagsResp)(nil), "fellow_svr.GetRareTagsResp")
	proto.RegisterType((*GetRareListReq)(nil), "fellow_svr.GetRareListReq")
	proto.RegisterType((*GetRareListResp)(nil), "fellow_svr.GetRareListResp")
	proto.RegisterType((*DelRareReq)(nil), "fellow_svr.DelRareReq")
	proto.RegisterType((*DelRareResp)(nil), "fellow_svr.DelRareResp")
	proto.RegisterType((*FellowPointDelayItem)(nil), "fellow_svr.FellowPointDelayItem")
	proto.RegisterType((*AddFellowPointDelayReq)(nil), "fellow_svr.AddFellowPointDelayReq")
	proto.RegisterType((*AddFellowPointDelayResp)(nil), "fellow_svr.AddFellowPointDelayResp")
	proto.RegisterType((*TestUpgradeImMsgReq)(nil), "fellow_svr.TestUpgradeImMsgReq")
	proto.RegisterType((*TestUpgradeImMsgResp)(nil), "fellow_svr.TestUpgradeImMsgResp")
	proto.RegisterType((*TestSetFellowLevelReq)(nil), "fellow_svr.TestSetFellowLevelReq")
	proto.RegisterType((*TestSetFellowLevelResp)(nil), "fellow_svr.TestSetFellowLevelResp")
	proto.RegisterType((*GetTopNFellowReq)(nil), "fellow_svr.GetTopNFellowReq")
	proto.RegisterType((*GetTopNFellowResp)(nil), "fellow_svr.GetTopNFellowResp")
	proto.RegisterType((*TopNFellowInfo)(nil), "fellow_svr.TopNFellowInfo")
	proto.RegisterEnum("fellow_svr.FellowType", FellowType_name, FellowType_value)
	proto.RegisterEnum("fellow_svr.FellowBindType", FellowBindType_name, FellowBindType_value)
	proto.RegisterEnum("fellow_svr.FellowBindStatus", FellowBindStatus_name, FellowBindStatus_value)
	proto.RegisterEnum("fellow_svr.BanType", BanType_name, BanType_value)
	proto.RegisterEnum("fellow_svr.InviteStatus", InviteStatus_name, InviteStatus_value)
	proto.RegisterEnum("fellow_svr.UnboundStatus", UnboundStatus_name, UnboundStatus_value)
	proto.RegisterEnum("fellow_svr.ResourceType", ResourceType_name, ResourceType_value)
	proto.RegisterEnum("fellow_svr.RelationType", RelationType_name, RelationType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// FellowSvrClient is the client API for FellowSvr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type FellowSvrClient interface {
	// 获取挚友名单
	GetFellowList(ctx context.Context, in *GetFellowListReq, opts ...grpc.CallOption) (*GetFellowListResp, error)
	// 获取可以申请挚友关系的用户名单
	GetFellowCandidateList(ctx context.Context, in *GetFellowCandidateListReq, opts ...grpc.CallOption) (*GetFellowCandidateListResp, error)
	// 获取对应用户的挚友申请相关信息
	GetFellowCandidateInfo(ctx context.Context, in *GetFellowCandidateInfoReq, opts ...grpc.CallOption) (*GetFellowCandidateInfoResp, error)
	// 发送挚友申请（邀请函）
	SendFellowInvite(ctx context.Context, in *SendFellowInviteReq, opts ...grpc.CallOption) (*SendFellowInviteResp, error)
	// 获取对应id的邀请函信息
	GetFellowInviteInfoById(ctx context.Context, in *GetFellowInviteInfoByIdReq, opts ...grpc.CallOption) (*GetFellowInviteInfoByIdResp, error)
	// 获取待处理的邀请函列表
	GetFellowInviteList(ctx context.Context, in *GetFellowInviteListReq, opts ...grpc.CallOption) (*GetFellowInviteListResp, error)
	// 处理挚友申请
	HandleFellowInvite(ctx context.Context, in *HandleFellowInviteReq, opts ...grpc.CallOption) (*HandleFellowInviteResp, error)
	// 获取web挚友信息
	GetWebFellowInfo(ctx context.Context, in *GetWebFellowListReq, opts ...grpc.CallOption) (*GetWebFellowListResp, error)
	// 检查邀请坑位
	CheckFellowInvite(ctx context.Context, in *CheckFellowInviteReq, opts ...grpc.CallOption) (*CheckFellowInviteResp, error)
	// 获取挚友值
	GetFellowPoint(ctx context.Context, in *GetFellowPointReq, opts ...grpc.CallOption) (*GetFellowPointResp, error)
	// 撤销挚友邀请
	CancelFellowInvite(ctx context.Context, in *CancelFellowInviteReq, opts ...grpc.CallOption) (*CancelFellowInviteResp, error)
	// 增加信物配置
	AddFellowPresentConfig(ctx context.Context, in *AddFellowPresentConfigReq, opts ...grpc.CallOption) (*AddFellowPresentConfigResp, error)
	// 删除信物配置
	DelFellowPresentConfig(ctx context.Context, in *DelFellowPresentConfigReq, opts ...grpc.CallOption) (*DelFellowPresentConfigResp, error)
	// 更新信物配置
	UpdateFellowPresentConfig(ctx context.Context, in *UpdateFellowPresentConfigReq, opts ...grpc.CallOption) (*UpdateFellowPresentConfigResp, error)
	// 获取信物配置
	GetAllFellowPresentConfig(ctx context.Context, in *GetAllFellowPresentConfigReq, opts ...grpc.CallOption) (*GetAllFellowPresentConfigResp, error)
	// 获取所有信物配置
	GetFellowPresentConfigById(ctx context.Context, in *GetFellowPresentConfigByIdReq, opts ...grpc.CallOption) (*GetFellowPresentConfigByIdResp, error)
	// 解锁挚友位
	UnlockFellowSite(ctx context.Context, in *UnlockFellowSiteReq, opts ...grpc.CallOption) (*UnlockFellowSiteResp, error)
	// 解绑关系申请
	UnboundFellow(ctx context.Context, in *UnboundFellowReq, opts ...grpc.CallOption) (*UnboundFellowResp, error)
	// 取消解绑关系
	CancelUnboundFellow(ctx context.Context, in *CancelUnboundFellowReq, opts ...grpc.CallOption) (*CancelUnboundFellowResp, error)
	// 赠送挚友信物
	SendFellowPresent(ctx context.Context, in *SendFellowPresentReq, opts ...grpc.CallOption) (*SendFellowPresentResp, error)
	// 获取挚友信物赠送页信息
	GetFellowPresentDetail(ctx context.Context, in *GetFellowPresentDetailReq, opts ...grpc.CallOption) (*GetFellowPresentDetailResp, error)
	Notify(ctx context.Context, in *cb.PayNotify, opts ...grpc.CallOption) (*cb.PayNotifyResponse, error)
	// 获取挚友绑定类型
	GetFellowType(ctx context.Context, in *GetFellowTypeReq, opts ...grpc.CallOption) (*GetFellowTypeResp, error)
	// 　根据对方uid 获取挚友信息
	GetFellowInfoByUid(ctx context.Context, in *GetFellowInfoByUidReq, opts ...grpc.CallOption) (*GetFellowInfoByUidResp, error)
	// 房间内直接赠送信物
	ChannelSendFellowPresent(ctx context.Context, in *ChannelSendFellowPresentReq, opts ...grpc.CallOption) (*ChannelSendFellowPresentResp, error)
	// 在房间内发起挚友邀请
	SendChannelFellowInvite(ctx context.Context, in *SendChannelFellowInviteReq, opts ...grpc.CallOption) (*SendChannelFellowInviteResp, error)
	// 处理房间内的挚友邀请
	HandleChannelFellowInvite(ctx context.Context, in *HandleChannelFellowInviteReq, opts ...grpc.CallOption) (*HandleChannelFellowInviteResp, error)
	GetRoomFellowList(ctx context.Context, in *GetRoomFellowListReq, opts ...grpc.CallOption) (*GetRoomFellowListResp, error)
	// 获取在该房间的所有挚友邀请信息
	GetAllChannelFellowInvite(ctx context.Context, in *GetAllChannelFellowInviteReq, opts ...grpc.CallOption) (*GetAllChannelFellowInviteResp, error)
	// 立即解绑
	DirectUnboundFellow(ctx context.Context, in *DirectUnboundFellowReq, opts ...grpc.CallOption) (*DirectUnboundFellowResp, error)
	// 获取房间申请的相关信息
	GetChannelFellowCandidateInfo(ctx context.Context, in *GetChannelFellowCandidateInfoReq, opts ...grpc.CallOption) (*GetChannelFellowCandidateInfoResp, error)
	// 获取麦上挚友信息
	GetOnMicFellowList(ctx context.Context, in *GetOnMicFellowListReq, opts ...grpc.CallOption) (*GetOnMicFellowListResp, error)
	// 获取麦上挚友信息
	GetSendInviteList(ctx context.Context, in *GetSendInviteListReq, opts ...grpc.CallOption) (*GetSendInviteListResp, error)
	ChangeFellowBindType(ctx context.Context, in *ChangeFellowBindTypeReq, opts ...grpc.CallOption) (*ChangeFellowBindTypeResp, error)
	// 获取挚友列表
	GetFellowSimpleInfo(ctx context.Context, in *GetFellowSimpleInfoReq, opts ...grpc.CallOption) (*GetFellowSimpleInfoResp, error)
	// 批量获取挚友对 信息
	BatchGetFellowSimpleInfoByPairList(ctx context.Context, in *BatchGetFellowSimpleInfoByPairListReq, opts ...grpc.CallOption) (*BatchGetFellowSimpleInfoByPairListResp, error)
	// 添加关系
	AddRelationship(ctx context.Context, in *RelationshipAddReq, opts ...grpc.CallOption) (*RelationshipAddResp, error)
	// 编辑关系
	UpdateRelationship(ctx context.Context, in *RelationshipUpdateReq, opts ...grpc.CallOption) (*RelationshipUpdateResp, error)
	// 删除关系
	DelRelationship(ctx context.Context, in *RelationshipDeleteReq, opts ...grpc.CallOption) (*RelationshipDeleteResp, error)
	// 分页查询关系列表
	GetRelationshipList(ctx context.Context, in *RelationshipListReq, opts ...grpc.CallOption) (*RelationshipListResp, error)
	// 根据条件查询关系
	GetRelationship(ctx context.Context, in *RelationshipGetReq, opts ...grpc.CallOption) (*RelationshipGetResp, error)
	// 分页查询关系下发列表
	GetChannelRelationshipBindingList(ctx context.Context, in *ChannelRelationshipBindingListReq, opts ...grpc.CallOption) (*ChannelRelationshipBindingListResp, error)
	// 下发关系
	AddChannelRelationshipBinding(ctx context.Context, in *ChannelRelationshipBindingAddReq, opts ...grpc.CallOption) (*ChannelRelationshipBindingAddResp, error)
	// 编辑下发关系信息
	UpdateChannelRelationshipBinding(ctx context.Context, in *ChannelRelationshipBindingUpdateReq, opts ...grpc.CallOption) (*ChannelRelationshipBindingUpdateResp, error)
	// 删除下发关系信息
	DelChannelRelationshipBinding(ctx context.Context, in *ChannelRelationshipBindingDeleteReq, opts ...grpc.CallOption) (*ChannelRelationshipBindingDeleteResp, error)
	// 获取挚友值topN好友
	GetTopNFellow(ctx context.Context, in *GetTopNFellowReq, opts ...grpc.CallOption) (*GetTopNFellowResp, error)
	// 获取所有稀缺关系配置
	GetRareConfig(ctx context.Context, in *GetRareConfigReq, opts ...grpc.CallOption) (*GetRareConfigResp, error)
	// 根据ids获取稀缺关系配置,包括过期,软删
	GetFromAllRelationshipByIds(ctx context.Context, in *GetFromAllRelationshipByIdsReq, opts ...grpc.CallOption) (*GetFromAllRelationshipByIdsResp, error)
	// 添加稀缺关系
	AddRare(ctx context.Context, in *AddRareReq, opts ...grpc.CallOption) (*AddRareResp, error)
	// 切换绑定的稀缺关系
	SetBindRelation(ctx context.Context, in *SetBindRelationReq, opts ...grpc.CallOption) (*SetBindRelationResp, error)
	// 获取房间对应的稀缺关系配置
	GetChannelRareConfig(ctx context.Context, in *GetChannelRareConfigReq, opts ...grpc.CallOption) (*GetChannelRareConfigResp, error)
	// 获取挚友空间稀缺关系标签
	GetRareTags(ctx context.Context, in *GetRareTagsReq, opts ...grpc.CallOption) (*GetRareTagsResp, error)
	// 获取自己和某个UID的所有稀缺关系列表
	GetRareList(ctx context.Context, in *GetRareListReq, opts ...grpc.CallOption) (*GetRareListResp, error)
	// 获取历史挚友绑定类型
	GetHistoryFellowType(ctx context.Context, in *GetHistoryFellowTypeReq, opts ...grpc.CallOption) (*GetHistoryFellowTypeResp, error)
	// 补单
	FixFellowOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	// 获取礼物kafka消费单数
	CntPresentEvLogTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	// 获取礼物kafka消费订单
	GetPresentEvLogOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 礼物Kafka消费补单
	FixPresentEvLogOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	DelRare(ctx context.Context, in *DelRareReq, opts ...grpc.CallOption) (*DelRareResp, error)
	// 神秘人延迟加分
	AddFellowPointDelay(ctx context.Context, in *AddFellowPointDelayReq, opts ...grpc.CallOption) (*AddFellowPointDelayResp, error)
	// 测试升级消息
	TestUpgradeImMsg(ctx context.Context, in *TestUpgradeImMsgReq, opts ...grpc.CallOption) (*TestUpgradeImMsgResp, error)
	// 测试设置等级
	TestSetFellowLevel(ctx context.Context, in *TestSetFellowLevelReq, opts ...grpc.CallOption) (*TestSetFellowLevelResp, error)
}

type fellowSvrClient struct {
	cc *grpc.ClientConn
}

func NewFellowSvrClient(cc *grpc.ClientConn) FellowSvrClient {
	return &fellowSvrClient{cc}
}

func (c *fellowSvrClient) GetFellowList(ctx context.Context, in *GetFellowListReq, opts ...grpc.CallOption) (*GetFellowListResp, error) {
	out := new(GetFellowListResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetFellowList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetFellowCandidateList(ctx context.Context, in *GetFellowCandidateListReq, opts ...grpc.CallOption) (*GetFellowCandidateListResp, error) {
	out := new(GetFellowCandidateListResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetFellowCandidateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetFellowCandidateInfo(ctx context.Context, in *GetFellowCandidateInfoReq, opts ...grpc.CallOption) (*GetFellowCandidateInfoResp, error) {
	out := new(GetFellowCandidateInfoResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetFellowCandidateInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) SendFellowInvite(ctx context.Context, in *SendFellowInviteReq, opts ...grpc.CallOption) (*SendFellowInviteResp, error) {
	out := new(SendFellowInviteResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/SendFellowInvite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetFellowInviteInfoById(ctx context.Context, in *GetFellowInviteInfoByIdReq, opts ...grpc.CallOption) (*GetFellowInviteInfoByIdResp, error) {
	out := new(GetFellowInviteInfoByIdResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetFellowInviteInfoById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetFellowInviteList(ctx context.Context, in *GetFellowInviteListReq, opts ...grpc.CallOption) (*GetFellowInviteListResp, error) {
	out := new(GetFellowInviteListResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetFellowInviteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) HandleFellowInvite(ctx context.Context, in *HandleFellowInviteReq, opts ...grpc.CallOption) (*HandleFellowInviteResp, error) {
	out := new(HandleFellowInviteResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/HandleFellowInvite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetWebFellowInfo(ctx context.Context, in *GetWebFellowListReq, opts ...grpc.CallOption) (*GetWebFellowListResp, error) {
	out := new(GetWebFellowListResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetWebFellowInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) CheckFellowInvite(ctx context.Context, in *CheckFellowInviteReq, opts ...grpc.CallOption) (*CheckFellowInviteResp, error) {
	out := new(CheckFellowInviteResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/CheckFellowInvite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetFellowPoint(ctx context.Context, in *GetFellowPointReq, opts ...grpc.CallOption) (*GetFellowPointResp, error) {
	out := new(GetFellowPointResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetFellowPoint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) CancelFellowInvite(ctx context.Context, in *CancelFellowInviteReq, opts ...grpc.CallOption) (*CancelFellowInviteResp, error) {
	out := new(CancelFellowInviteResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/CancelFellowInvite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) AddFellowPresentConfig(ctx context.Context, in *AddFellowPresentConfigReq, opts ...grpc.CallOption) (*AddFellowPresentConfigResp, error) {
	out := new(AddFellowPresentConfigResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/AddFellowPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) DelFellowPresentConfig(ctx context.Context, in *DelFellowPresentConfigReq, opts ...grpc.CallOption) (*DelFellowPresentConfigResp, error) {
	out := new(DelFellowPresentConfigResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/DelFellowPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) UpdateFellowPresentConfig(ctx context.Context, in *UpdateFellowPresentConfigReq, opts ...grpc.CallOption) (*UpdateFellowPresentConfigResp, error) {
	out := new(UpdateFellowPresentConfigResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/UpdateFellowPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetAllFellowPresentConfig(ctx context.Context, in *GetAllFellowPresentConfigReq, opts ...grpc.CallOption) (*GetAllFellowPresentConfigResp, error) {
	out := new(GetAllFellowPresentConfigResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetAllFellowPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetFellowPresentConfigById(ctx context.Context, in *GetFellowPresentConfigByIdReq, opts ...grpc.CallOption) (*GetFellowPresentConfigByIdResp, error) {
	out := new(GetFellowPresentConfigByIdResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetFellowPresentConfigById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) UnlockFellowSite(ctx context.Context, in *UnlockFellowSiteReq, opts ...grpc.CallOption) (*UnlockFellowSiteResp, error) {
	out := new(UnlockFellowSiteResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/UnlockFellowSite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) UnboundFellow(ctx context.Context, in *UnboundFellowReq, opts ...grpc.CallOption) (*UnboundFellowResp, error) {
	out := new(UnboundFellowResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/UnboundFellow", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) CancelUnboundFellow(ctx context.Context, in *CancelUnboundFellowReq, opts ...grpc.CallOption) (*CancelUnboundFellowResp, error) {
	out := new(CancelUnboundFellowResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/CancelUnboundFellow", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) SendFellowPresent(ctx context.Context, in *SendFellowPresentReq, opts ...grpc.CallOption) (*SendFellowPresentResp, error) {
	out := new(SendFellowPresentResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/SendFellowPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetFellowPresentDetail(ctx context.Context, in *GetFellowPresentDetailReq, opts ...grpc.CallOption) (*GetFellowPresentDetailResp, error) {
	out := new(GetFellowPresentDetailResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetFellowPresentDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) Notify(ctx context.Context, in *cb.PayNotify, opts ...grpc.CallOption) (*cb.PayNotifyResponse, error) {
	out := new(cb.PayNotifyResponse)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/Notify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetFellowType(ctx context.Context, in *GetFellowTypeReq, opts ...grpc.CallOption) (*GetFellowTypeResp, error) {
	out := new(GetFellowTypeResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetFellowType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetFellowInfoByUid(ctx context.Context, in *GetFellowInfoByUidReq, opts ...grpc.CallOption) (*GetFellowInfoByUidResp, error) {
	out := new(GetFellowInfoByUidResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetFellowInfoByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) ChannelSendFellowPresent(ctx context.Context, in *ChannelSendFellowPresentReq, opts ...grpc.CallOption) (*ChannelSendFellowPresentResp, error) {
	out := new(ChannelSendFellowPresentResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/ChannelSendFellowPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) SendChannelFellowInvite(ctx context.Context, in *SendChannelFellowInviteReq, opts ...grpc.CallOption) (*SendChannelFellowInviteResp, error) {
	out := new(SendChannelFellowInviteResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/SendChannelFellowInvite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) HandleChannelFellowInvite(ctx context.Context, in *HandleChannelFellowInviteReq, opts ...grpc.CallOption) (*HandleChannelFellowInviteResp, error) {
	out := new(HandleChannelFellowInviteResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/HandleChannelFellowInvite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetRoomFellowList(ctx context.Context, in *GetRoomFellowListReq, opts ...grpc.CallOption) (*GetRoomFellowListResp, error) {
	out := new(GetRoomFellowListResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetRoomFellowList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetAllChannelFellowInvite(ctx context.Context, in *GetAllChannelFellowInviteReq, opts ...grpc.CallOption) (*GetAllChannelFellowInviteResp, error) {
	out := new(GetAllChannelFellowInviteResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetAllChannelFellowInvite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) DirectUnboundFellow(ctx context.Context, in *DirectUnboundFellowReq, opts ...grpc.CallOption) (*DirectUnboundFellowResp, error) {
	out := new(DirectUnboundFellowResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/DirectUnboundFellow", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetChannelFellowCandidateInfo(ctx context.Context, in *GetChannelFellowCandidateInfoReq, opts ...grpc.CallOption) (*GetChannelFellowCandidateInfoResp, error) {
	out := new(GetChannelFellowCandidateInfoResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetChannelFellowCandidateInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetOnMicFellowList(ctx context.Context, in *GetOnMicFellowListReq, opts ...grpc.CallOption) (*GetOnMicFellowListResp, error) {
	out := new(GetOnMicFellowListResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetOnMicFellowList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetSendInviteList(ctx context.Context, in *GetSendInviteListReq, opts ...grpc.CallOption) (*GetSendInviteListResp, error) {
	out := new(GetSendInviteListResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetSendInviteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) ChangeFellowBindType(ctx context.Context, in *ChangeFellowBindTypeReq, opts ...grpc.CallOption) (*ChangeFellowBindTypeResp, error) {
	out := new(ChangeFellowBindTypeResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/ChangeFellowBindType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetFellowSimpleInfo(ctx context.Context, in *GetFellowSimpleInfoReq, opts ...grpc.CallOption) (*GetFellowSimpleInfoResp, error) {
	out := new(GetFellowSimpleInfoResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetFellowSimpleInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) BatchGetFellowSimpleInfoByPairList(ctx context.Context, in *BatchGetFellowSimpleInfoByPairListReq, opts ...grpc.CallOption) (*BatchGetFellowSimpleInfoByPairListResp, error) {
	out := new(BatchGetFellowSimpleInfoByPairListResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/BatchGetFellowSimpleInfoByPairList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) AddRelationship(ctx context.Context, in *RelationshipAddReq, opts ...grpc.CallOption) (*RelationshipAddResp, error) {
	out := new(RelationshipAddResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/AddRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) UpdateRelationship(ctx context.Context, in *RelationshipUpdateReq, opts ...grpc.CallOption) (*RelationshipUpdateResp, error) {
	out := new(RelationshipUpdateResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/UpdateRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) DelRelationship(ctx context.Context, in *RelationshipDeleteReq, opts ...grpc.CallOption) (*RelationshipDeleteResp, error) {
	out := new(RelationshipDeleteResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/DelRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetRelationshipList(ctx context.Context, in *RelationshipListReq, opts ...grpc.CallOption) (*RelationshipListResp, error) {
	out := new(RelationshipListResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetRelationshipList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetRelationship(ctx context.Context, in *RelationshipGetReq, opts ...grpc.CallOption) (*RelationshipGetResp, error) {
	out := new(RelationshipGetResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetChannelRelationshipBindingList(ctx context.Context, in *ChannelRelationshipBindingListReq, opts ...grpc.CallOption) (*ChannelRelationshipBindingListResp, error) {
	out := new(ChannelRelationshipBindingListResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetChannelRelationshipBindingList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) AddChannelRelationshipBinding(ctx context.Context, in *ChannelRelationshipBindingAddReq, opts ...grpc.CallOption) (*ChannelRelationshipBindingAddResp, error) {
	out := new(ChannelRelationshipBindingAddResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/AddChannelRelationshipBinding", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) UpdateChannelRelationshipBinding(ctx context.Context, in *ChannelRelationshipBindingUpdateReq, opts ...grpc.CallOption) (*ChannelRelationshipBindingUpdateResp, error) {
	out := new(ChannelRelationshipBindingUpdateResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/UpdateChannelRelationshipBinding", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) DelChannelRelationshipBinding(ctx context.Context, in *ChannelRelationshipBindingDeleteReq, opts ...grpc.CallOption) (*ChannelRelationshipBindingDeleteResp, error) {
	out := new(ChannelRelationshipBindingDeleteResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/DelChannelRelationshipBinding", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetTopNFellow(ctx context.Context, in *GetTopNFellowReq, opts ...grpc.CallOption) (*GetTopNFellowResp, error) {
	out := new(GetTopNFellowResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetTopNFellow", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetRareConfig(ctx context.Context, in *GetRareConfigReq, opts ...grpc.CallOption) (*GetRareConfigResp, error) {
	out := new(GetRareConfigResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetRareConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetFromAllRelationshipByIds(ctx context.Context, in *GetFromAllRelationshipByIdsReq, opts ...grpc.CallOption) (*GetFromAllRelationshipByIdsResp, error) {
	out := new(GetFromAllRelationshipByIdsResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetFromAllRelationshipByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) AddRare(ctx context.Context, in *AddRareReq, opts ...grpc.CallOption) (*AddRareResp, error) {
	out := new(AddRareResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/AddRare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) SetBindRelation(ctx context.Context, in *SetBindRelationReq, opts ...grpc.CallOption) (*SetBindRelationResp, error) {
	out := new(SetBindRelationResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/SetBindRelation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetChannelRareConfig(ctx context.Context, in *GetChannelRareConfigReq, opts ...grpc.CallOption) (*GetChannelRareConfigResp, error) {
	out := new(GetChannelRareConfigResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetChannelRareConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetRareTags(ctx context.Context, in *GetRareTagsReq, opts ...grpc.CallOption) (*GetRareTagsResp, error) {
	out := new(GetRareTagsResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetRareTags", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetRareList(ctx context.Context, in *GetRareListReq, opts ...grpc.CallOption) (*GetRareListResp, error) {
	out := new(GetRareListResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetRareList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetHistoryFellowType(ctx context.Context, in *GetHistoryFellowTypeReq, opts ...grpc.CallOption) (*GetHistoryFellowTypeResp, error) {
	out := new(GetHistoryFellowTypeResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetHistoryFellowType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) FixFellowOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/FixFellowOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) CntPresentEvLogTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/CntPresentEvLogTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) GetPresentEvLogOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/GetPresentEvLogOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) FixPresentEvLogOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/FixPresentEvLogOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) DelRare(ctx context.Context, in *DelRareReq, opts ...grpc.CallOption) (*DelRareResp, error) {
	out := new(DelRareResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/DelRare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) AddFellowPointDelay(ctx context.Context, in *AddFellowPointDelayReq, opts ...grpc.CallOption) (*AddFellowPointDelayResp, error) {
	out := new(AddFellowPointDelayResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/AddFellowPointDelay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) TestUpgradeImMsg(ctx context.Context, in *TestUpgradeImMsgReq, opts ...grpc.CallOption) (*TestUpgradeImMsgResp, error) {
	out := new(TestUpgradeImMsgResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/TestUpgradeImMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fellowSvrClient) TestSetFellowLevel(ctx context.Context, in *TestSetFellowLevelReq, opts ...grpc.CallOption) (*TestSetFellowLevelResp, error) {
	out := new(TestSetFellowLevelResp)
	err := c.cc.Invoke(ctx, "/fellow_svr.FellowSvr/TestSetFellowLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FellowSvrServer is the server API for FellowSvr service.
type FellowSvrServer interface {
	// 获取挚友名单
	GetFellowList(context.Context, *GetFellowListReq) (*GetFellowListResp, error)
	// 获取可以申请挚友关系的用户名单
	GetFellowCandidateList(context.Context, *GetFellowCandidateListReq) (*GetFellowCandidateListResp, error)
	// 获取对应用户的挚友申请相关信息
	GetFellowCandidateInfo(context.Context, *GetFellowCandidateInfoReq) (*GetFellowCandidateInfoResp, error)
	// 发送挚友申请（邀请函）
	SendFellowInvite(context.Context, *SendFellowInviteReq) (*SendFellowInviteResp, error)
	// 获取对应id的邀请函信息
	GetFellowInviteInfoById(context.Context, *GetFellowInviteInfoByIdReq) (*GetFellowInviteInfoByIdResp, error)
	// 获取待处理的邀请函列表
	GetFellowInviteList(context.Context, *GetFellowInviteListReq) (*GetFellowInviteListResp, error)
	// 处理挚友申请
	HandleFellowInvite(context.Context, *HandleFellowInviteReq) (*HandleFellowInviteResp, error)
	// 获取web挚友信息
	GetWebFellowInfo(context.Context, *GetWebFellowListReq) (*GetWebFellowListResp, error)
	// 检查邀请坑位
	CheckFellowInvite(context.Context, *CheckFellowInviteReq) (*CheckFellowInviteResp, error)
	// 获取挚友值
	GetFellowPoint(context.Context, *GetFellowPointReq) (*GetFellowPointResp, error)
	// 撤销挚友邀请
	CancelFellowInvite(context.Context, *CancelFellowInviteReq) (*CancelFellowInviteResp, error)
	// 增加信物配置
	AddFellowPresentConfig(context.Context, *AddFellowPresentConfigReq) (*AddFellowPresentConfigResp, error)
	// 删除信物配置
	DelFellowPresentConfig(context.Context, *DelFellowPresentConfigReq) (*DelFellowPresentConfigResp, error)
	// 更新信物配置
	UpdateFellowPresentConfig(context.Context, *UpdateFellowPresentConfigReq) (*UpdateFellowPresentConfigResp, error)
	// 获取信物配置
	GetAllFellowPresentConfig(context.Context, *GetAllFellowPresentConfigReq) (*GetAllFellowPresentConfigResp, error)
	// 获取所有信物配置
	GetFellowPresentConfigById(context.Context, *GetFellowPresentConfigByIdReq) (*GetFellowPresentConfigByIdResp, error)
	// 解锁挚友位
	UnlockFellowSite(context.Context, *UnlockFellowSiteReq) (*UnlockFellowSiteResp, error)
	// 解绑关系申请
	UnboundFellow(context.Context, *UnboundFellowReq) (*UnboundFellowResp, error)
	// 取消解绑关系
	CancelUnboundFellow(context.Context, *CancelUnboundFellowReq) (*CancelUnboundFellowResp, error)
	// 赠送挚友信物
	SendFellowPresent(context.Context, *SendFellowPresentReq) (*SendFellowPresentResp, error)
	// 获取挚友信物赠送页信息
	GetFellowPresentDetail(context.Context, *GetFellowPresentDetailReq) (*GetFellowPresentDetailResp, error)
	Notify(context.Context, *cb.PayNotify) (*cb.PayNotifyResponse, error)
	// 获取挚友绑定类型
	GetFellowType(context.Context, *GetFellowTypeReq) (*GetFellowTypeResp, error)
	// 　根据对方uid 获取挚友信息
	GetFellowInfoByUid(context.Context, *GetFellowInfoByUidReq) (*GetFellowInfoByUidResp, error)
	// 房间内直接赠送信物
	ChannelSendFellowPresent(context.Context, *ChannelSendFellowPresentReq) (*ChannelSendFellowPresentResp, error)
	// 在房间内发起挚友邀请
	SendChannelFellowInvite(context.Context, *SendChannelFellowInviteReq) (*SendChannelFellowInviteResp, error)
	// 处理房间内的挚友邀请
	HandleChannelFellowInvite(context.Context, *HandleChannelFellowInviteReq) (*HandleChannelFellowInviteResp, error)
	GetRoomFellowList(context.Context, *GetRoomFellowListReq) (*GetRoomFellowListResp, error)
	// 获取在该房间的所有挚友邀请信息
	GetAllChannelFellowInvite(context.Context, *GetAllChannelFellowInviteReq) (*GetAllChannelFellowInviteResp, error)
	// 立即解绑
	DirectUnboundFellow(context.Context, *DirectUnboundFellowReq) (*DirectUnboundFellowResp, error)
	// 获取房间申请的相关信息
	GetChannelFellowCandidateInfo(context.Context, *GetChannelFellowCandidateInfoReq) (*GetChannelFellowCandidateInfoResp, error)
	// 获取麦上挚友信息
	GetOnMicFellowList(context.Context, *GetOnMicFellowListReq) (*GetOnMicFellowListResp, error)
	// 获取麦上挚友信息
	GetSendInviteList(context.Context, *GetSendInviteListReq) (*GetSendInviteListResp, error)
	ChangeFellowBindType(context.Context, *ChangeFellowBindTypeReq) (*ChangeFellowBindTypeResp, error)
	// 获取挚友列表
	GetFellowSimpleInfo(context.Context, *GetFellowSimpleInfoReq) (*GetFellowSimpleInfoResp, error)
	// 批量获取挚友对 信息
	BatchGetFellowSimpleInfoByPairList(context.Context, *BatchGetFellowSimpleInfoByPairListReq) (*BatchGetFellowSimpleInfoByPairListResp, error)
	// 添加关系
	AddRelationship(context.Context, *RelationshipAddReq) (*RelationshipAddResp, error)
	// 编辑关系
	UpdateRelationship(context.Context, *RelationshipUpdateReq) (*RelationshipUpdateResp, error)
	// 删除关系
	DelRelationship(context.Context, *RelationshipDeleteReq) (*RelationshipDeleteResp, error)
	// 分页查询关系列表
	GetRelationshipList(context.Context, *RelationshipListReq) (*RelationshipListResp, error)
	// 根据条件查询关系
	GetRelationship(context.Context, *RelationshipGetReq) (*RelationshipGetResp, error)
	// 分页查询关系下发列表
	GetChannelRelationshipBindingList(context.Context, *ChannelRelationshipBindingListReq) (*ChannelRelationshipBindingListResp, error)
	// 下发关系
	AddChannelRelationshipBinding(context.Context, *ChannelRelationshipBindingAddReq) (*ChannelRelationshipBindingAddResp, error)
	// 编辑下发关系信息
	UpdateChannelRelationshipBinding(context.Context, *ChannelRelationshipBindingUpdateReq) (*ChannelRelationshipBindingUpdateResp, error)
	// 删除下发关系信息
	DelChannelRelationshipBinding(context.Context, *ChannelRelationshipBindingDeleteReq) (*ChannelRelationshipBindingDeleteResp, error)
	// 获取挚友值topN好友
	GetTopNFellow(context.Context, *GetTopNFellowReq) (*GetTopNFellowResp, error)
	// 获取所有稀缺关系配置
	GetRareConfig(context.Context, *GetRareConfigReq) (*GetRareConfigResp, error)
	// 根据ids获取稀缺关系配置,包括过期,软删
	GetFromAllRelationshipByIds(context.Context, *GetFromAllRelationshipByIdsReq) (*GetFromAllRelationshipByIdsResp, error)
	// 添加稀缺关系
	AddRare(context.Context, *AddRareReq) (*AddRareResp, error)
	// 切换绑定的稀缺关系
	SetBindRelation(context.Context, *SetBindRelationReq) (*SetBindRelationResp, error)
	// 获取房间对应的稀缺关系配置
	GetChannelRareConfig(context.Context, *GetChannelRareConfigReq) (*GetChannelRareConfigResp, error)
	// 获取挚友空间稀缺关系标签
	GetRareTags(context.Context, *GetRareTagsReq) (*GetRareTagsResp, error)
	// 获取自己和某个UID的所有稀缺关系列表
	GetRareList(context.Context, *GetRareListReq) (*GetRareListResp, error)
	// 获取历史挚友绑定类型
	GetHistoryFellowType(context.Context, *GetHistoryFellowTypeReq) (*GetHistoryFellowTypeResp, error)
	// 补单
	FixFellowOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	// 获取礼物kafka消费单数
	CntPresentEvLogTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	// 获取礼物kafka消费订单
	GetPresentEvLogOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 礼物Kafka消费补单
	FixPresentEvLogOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	DelRare(context.Context, *DelRareReq) (*DelRareResp, error)
	// 神秘人延迟加分
	AddFellowPointDelay(context.Context, *AddFellowPointDelayReq) (*AddFellowPointDelayResp, error)
	// 测试升级消息
	TestUpgradeImMsg(context.Context, *TestUpgradeImMsgReq) (*TestUpgradeImMsgResp, error)
	// 测试设置等级
	TestSetFellowLevel(context.Context, *TestSetFellowLevelReq) (*TestSetFellowLevelResp, error)
}

func RegisterFellowSvrServer(s *grpc.Server, srv FellowSvrServer) {
	s.RegisterService(&_FellowSvr_serviceDesc, srv)
}

func _FellowSvr_GetFellowList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFellowListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetFellowList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetFellowList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetFellowList(ctx, req.(*GetFellowListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetFellowCandidateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFellowCandidateListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetFellowCandidateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetFellowCandidateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetFellowCandidateList(ctx, req.(*GetFellowCandidateListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetFellowCandidateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFellowCandidateInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetFellowCandidateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetFellowCandidateInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetFellowCandidateInfo(ctx, req.(*GetFellowCandidateInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_SendFellowInvite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendFellowInviteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).SendFellowInvite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/SendFellowInvite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).SendFellowInvite(ctx, req.(*SendFellowInviteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetFellowInviteInfoById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFellowInviteInfoByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetFellowInviteInfoById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetFellowInviteInfoById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetFellowInviteInfoById(ctx, req.(*GetFellowInviteInfoByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetFellowInviteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFellowInviteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetFellowInviteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetFellowInviteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetFellowInviteList(ctx, req.(*GetFellowInviteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_HandleFellowInvite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleFellowInviteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).HandleFellowInvite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/HandleFellowInvite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).HandleFellowInvite(ctx, req.(*HandleFellowInviteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetWebFellowInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWebFellowListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetWebFellowInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetWebFellowInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetWebFellowInfo(ctx, req.(*GetWebFellowListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_CheckFellowInvite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckFellowInviteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).CheckFellowInvite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/CheckFellowInvite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).CheckFellowInvite(ctx, req.(*CheckFellowInviteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetFellowPoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFellowPointReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetFellowPoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetFellowPoint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetFellowPoint(ctx, req.(*GetFellowPointReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_CancelFellowInvite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelFellowInviteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).CancelFellowInvite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/CancelFellowInvite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).CancelFellowInvite(ctx, req.(*CancelFellowInviteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_AddFellowPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFellowPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).AddFellowPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/AddFellowPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).AddFellowPresentConfig(ctx, req.(*AddFellowPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_DelFellowPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelFellowPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).DelFellowPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/DelFellowPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).DelFellowPresentConfig(ctx, req.(*DelFellowPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_UpdateFellowPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFellowPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).UpdateFellowPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/UpdateFellowPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).UpdateFellowPresentConfig(ctx, req.(*UpdateFellowPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetAllFellowPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllFellowPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetAllFellowPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetAllFellowPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetAllFellowPresentConfig(ctx, req.(*GetAllFellowPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetFellowPresentConfigById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFellowPresentConfigByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetFellowPresentConfigById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetFellowPresentConfigById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetFellowPresentConfigById(ctx, req.(*GetFellowPresentConfigByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_UnlockFellowSite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnlockFellowSiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).UnlockFellowSite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/UnlockFellowSite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).UnlockFellowSite(ctx, req.(*UnlockFellowSiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_UnboundFellow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnboundFellowReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).UnboundFellow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/UnboundFellow",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).UnboundFellow(ctx, req.(*UnboundFellowReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_CancelUnboundFellow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelUnboundFellowReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).CancelUnboundFellow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/CancelUnboundFellow",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).CancelUnboundFellow(ctx, req.(*CancelUnboundFellowReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_SendFellowPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendFellowPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).SendFellowPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/SendFellowPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).SendFellowPresent(ctx, req.(*SendFellowPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetFellowPresentDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFellowPresentDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetFellowPresentDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetFellowPresentDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetFellowPresentDetail(ctx, req.(*GetFellowPresentDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_Notify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cb.PayNotify)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).Notify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/Notify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).Notify(ctx, req.(*cb.PayNotify))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetFellowType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFellowTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetFellowType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetFellowType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetFellowType(ctx, req.(*GetFellowTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetFellowInfoByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFellowInfoByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetFellowInfoByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetFellowInfoByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetFellowInfoByUid(ctx, req.(*GetFellowInfoByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_ChannelSendFellowPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelSendFellowPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).ChannelSendFellowPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/ChannelSendFellowPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).ChannelSendFellowPresent(ctx, req.(*ChannelSendFellowPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_SendChannelFellowInvite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendChannelFellowInviteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).SendChannelFellowInvite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/SendChannelFellowInvite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).SendChannelFellowInvite(ctx, req.(*SendChannelFellowInviteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_HandleChannelFellowInvite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleChannelFellowInviteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).HandleChannelFellowInvite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/HandleChannelFellowInvite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).HandleChannelFellowInvite(ctx, req.(*HandleChannelFellowInviteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetRoomFellowList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRoomFellowListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetRoomFellowList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetRoomFellowList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetRoomFellowList(ctx, req.(*GetRoomFellowListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetAllChannelFellowInvite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllChannelFellowInviteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetAllChannelFellowInvite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetAllChannelFellowInvite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetAllChannelFellowInvite(ctx, req.(*GetAllChannelFellowInviteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_DirectUnboundFellow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DirectUnboundFellowReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).DirectUnboundFellow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/DirectUnboundFellow",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).DirectUnboundFellow(ctx, req.(*DirectUnboundFellowReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetChannelFellowCandidateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelFellowCandidateInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetChannelFellowCandidateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetChannelFellowCandidateInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetChannelFellowCandidateInfo(ctx, req.(*GetChannelFellowCandidateInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetOnMicFellowList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnMicFellowListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetOnMicFellowList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetOnMicFellowList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetOnMicFellowList(ctx, req.(*GetOnMicFellowListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetSendInviteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSendInviteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetSendInviteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetSendInviteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetSendInviteList(ctx, req.(*GetSendInviteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_ChangeFellowBindType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeFellowBindTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).ChangeFellowBindType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/ChangeFellowBindType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).ChangeFellowBindType(ctx, req.(*ChangeFellowBindTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetFellowSimpleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFellowSimpleInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetFellowSimpleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetFellowSimpleInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetFellowSimpleInfo(ctx, req.(*GetFellowSimpleInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_BatchGetFellowSimpleInfoByPairList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetFellowSimpleInfoByPairListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).BatchGetFellowSimpleInfoByPairList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/BatchGetFellowSimpleInfoByPairList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).BatchGetFellowSimpleInfoByPairList(ctx, req.(*BatchGetFellowSimpleInfoByPairListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_AddRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RelationshipAddReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).AddRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/AddRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).AddRelationship(ctx, req.(*RelationshipAddReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_UpdateRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RelationshipUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).UpdateRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/UpdateRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).UpdateRelationship(ctx, req.(*RelationshipUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_DelRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RelationshipDeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).DelRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/DelRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).DelRelationship(ctx, req.(*RelationshipDeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetRelationshipList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RelationshipListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetRelationshipList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetRelationshipList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetRelationshipList(ctx, req.(*RelationshipListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RelationshipGetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetRelationship(ctx, req.(*RelationshipGetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetChannelRelationshipBindingList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelRelationshipBindingListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetChannelRelationshipBindingList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetChannelRelationshipBindingList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetChannelRelationshipBindingList(ctx, req.(*ChannelRelationshipBindingListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_AddChannelRelationshipBinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelRelationshipBindingAddReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).AddChannelRelationshipBinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/AddChannelRelationshipBinding",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).AddChannelRelationshipBinding(ctx, req.(*ChannelRelationshipBindingAddReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_UpdateChannelRelationshipBinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelRelationshipBindingUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).UpdateChannelRelationshipBinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/UpdateChannelRelationshipBinding",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).UpdateChannelRelationshipBinding(ctx, req.(*ChannelRelationshipBindingUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_DelChannelRelationshipBinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelRelationshipBindingDeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).DelChannelRelationshipBinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/DelChannelRelationshipBinding",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).DelChannelRelationshipBinding(ctx, req.(*ChannelRelationshipBindingDeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetTopNFellow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopNFellowReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetTopNFellow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetTopNFellow",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetTopNFellow(ctx, req.(*GetTopNFellowReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetRareConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRareConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetRareConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetRareConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetRareConfig(ctx, req.(*GetRareConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetFromAllRelationshipByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFromAllRelationshipByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetFromAllRelationshipByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetFromAllRelationshipByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetFromAllRelationshipByIds(ctx, req.(*GetFromAllRelationshipByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_AddRare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRareReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).AddRare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/AddRare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).AddRare(ctx, req.(*AddRareReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_SetBindRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetBindRelationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).SetBindRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/SetBindRelation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).SetBindRelation(ctx, req.(*SetBindRelationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetChannelRareConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelRareConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetChannelRareConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetChannelRareConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetChannelRareConfig(ctx, req.(*GetChannelRareConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetRareTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRareTagsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetRareTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetRareTags",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetRareTags(ctx, req.(*GetRareTagsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetRareList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRareListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetRareList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetRareList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetRareList(ctx, req.(*GetRareListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetHistoryFellowType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHistoryFellowTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetHistoryFellowType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetHistoryFellowType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetHistoryFellowType(ctx, req.(*GetHistoryFellowTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_FixFellowOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).FixFellowOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/FixFellowOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).FixFellowOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_CntPresentEvLogTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).CntPresentEvLogTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/CntPresentEvLogTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).CntPresentEvLogTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_GetPresentEvLogOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).GetPresentEvLogOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/GetPresentEvLogOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).GetPresentEvLogOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_FixPresentEvLogOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).FixPresentEvLogOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/FixPresentEvLogOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).FixPresentEvLogOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_DelRare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelRareReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).DelRare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/DelRare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).DelRare(ctx, req.(*DelRareReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_AddFellowPointDelay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFellowPointDelayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).AddFellowPointDelay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/AddFellowPointDelay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).AddFellowPointDelay(ctx, req.(*AddFellowPointDelayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_TestUpgradeImMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestUpgradeImMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).TestUpgradeImMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/TestUpgradeImMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).TestUpgradeImMsg(ctx, req.(*TestUpgradeImMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FellowSvr_TestSetFellowLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestSetFellowLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FellowSvrServer).TestSetFellowLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/fellow_svr.FellowSvr/TestSetFellowLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FellowSvrServer).TestSetFellowLevel(ctx, req.(*TestSetFellowLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _FellowSvr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "fellow_svr.FellowSvr",
	HandlerType: (*FellowSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetFellowList",
			Handler:    _FellowSvr_GetFellowList_Handler,
		},
		{
			MethodName: "GetFellowCandidateList",
			Handler:    _FellowSvr_GetFellowCandidateList_Handler,
		},
		{
			MethodName: "GetFellowCandidateInfo",
			Handler:    _FellowSvr_GetFellowCandidateInfo_Handler,
		},
		{
			MethodName: "SendFellowInvite",
			Handler:    _FellowSvr_SendFellowInvite_Handler,
		},
		{
			MethodName: "GetFellowInviteInfoById",
			Handler:    _FellowSvr_GetFellowInviteInfoById_Handler,
		},
		{
			MethodName: "GetFellowInviteList",
			Handler:    _FellowSvr_GetFellowInviteList_Handler,
		},
		{
			MethodName: "HandleFellowInvite",
			Handler:    _FellowSvr_HandleFellowInvite_Handler,
		},
		{
			MethodName: "GetWebFellowInfo",
			Handler:    _FellowSvr_GetWebFellowInfo_Handler,
		},
		{
			MethodName: "CheckFellowInvite",
			Handler:    _FellowSvr_CheckFellowInvite_Handler,
		},
		{
			MethodName: "GetFellowPoint",
			Handler:    _FellowSvr_GetFellowPoint_Handler,
		},
		{
			MethodName: "CancelFellowInvite",
			Handler:    _FellowSvr_CancelFellowInvite_Handler,
		},
		{
			MethodName: "AddFellowPresentConfig",
			Handler:    _FellowSvr_AddFellowPresentConfig_Handler,
		},
		{
			MethodName: "DelFellowPresentConfig",
			Handler:    _FellowSvr_DelFellowPresentConfig_Handler,
		},
		{
			MethodName: "UpdateFellowPresentConfig",
			Handler:    _FellowSvr_UpdateFellowPresentConfig_Handler,
		},
		{
			MethodName: "GetAllFellowPresentConfig",
			Handler:    _FellowSvr_GetAllFellowPresentConfig_Handler,
		},
		{
			MethodName: "GetFellowPresentConfigById",
			Handler:    _FellowSvr_GetFellowPresentConfigById_Handler,
		},
		{
			MethodName: "UnlockFellowSite",
			Handler:    _FellowSvr_UnlockFellowSite_Handler,
		},
		{
			MethodName: "UnboundFellow",
			Handler:    _FellowSvr_UnboundFellow_Handler,
		},
		{
			MethodName: "CancelUnboundFellow",
			Handler:    _FellowSvr_CancelUnboundFellow_Handler,
		},
		{
			MethodName: "SendFellowPresent",
			Handler:    _FellowSvr_SendFellowPresent_Handler,
		},
		{
			MethodName: "GetFellowPresentDetail",
			Handler:    _FellowSvr_GetFellowPresentDetail_Handler,
		},
		{
			MethodName: "Notify",
			Handler:    _FellowSvr_Notify_Handler,
		},
		{
			MethodName: "GetFellowType",
			Handler:    _FellowSvr_GetFellowType_Handler,
		},
		{
			MethodName: "GetFellowInfoByUid",
			Handler:    _FellowSvr_GetFellowInfoByUid_Handler,
		},
		{
			MethodName: "ChannelSendFellowPresent",
			Handler:    _FellowSvr_ChannelSendFellowPresent_Handler,
		},
		{
			MethodName: "SendChannelFellowInvite",
			Handler:    _FellowSvr_SendChannelFellowInvite_Handler,
		},
		{
			MethodName: "HandleChannelFellowInvite",
			Handler:    _FellowSvr_HandleChannelFellowInvite_Handler,
		},
		{
			MethodName: "GetRoomFellowList",
			Handler:    _FellowSvr_GetRoomFellowList_Handler,
		},
		{
			MethodName: "GetAllChannelFellowInvite",
			Handler:    _FellowSvr_GetAllChannelFellowInvite_Handler,
		},
		{
			MethodName: "DirectUnboundFellow",
			Handler:    _FellowSvr_DirectUnboundFellow_Handler,
		},
		{
			MethodName: "GetChannelFellowCandidateInfo",
			Handler:    _FellowSvr_GetChannelFellowCandidateInfo_Handler,
		},
		{
			MethodName: "GetOnMicFellowList",
			Handler:    _FellowSvr_GetOnMicFellowList_Handler,
		},
		{
			MethodName: "GetSendInviteList",
			Handler:    _FellowSvr_GetSendInviteList_Handler,
		},
		{
			MethodName: "ChangeFellowBindType",
			Handler:    _FellowSvr_ChangeFellowBindType_Handler,
		},
		{
			MethodName: "GetFellowSimpleInfo",
			Handler:    _FellowSvr_GetFellowSimpleInfo_Handler,
		},
		{
			MethodName: "BatchGetFellowSimpleInfoByPairList",
			Handler:    _FellowSvr_BatchGetFellowSimpleInfoByPairList_Handler,
		},
		{
			MethodName: "AddRelationship",
			Handler:    _FellowSvr_AddRelationship_Handler,
		},
		{
			MethodName: "UpdateRelationship",
			Handler:    _FellowSvr_UpdateRelationship_Handler,
		},
		{
			MethodName: "DelRelationship",
			Handler:    _FellowSvr_DelRelationship_Handler,
		},
		{
			MethodName: "GetRelationshipList",
			Handler:    _FellowSvr_GetRelationshipList_Handler,
		},
		{
			MethodName: "GetRelationship",
			Handler:    _FellowSvr_GetRelationship_Handler,
		},
		{
			MethodName: "GetChannelRelationshipBindingList",
			Handler:    _FellowSvr_GetChannelRelationshipBindingList_Handler,
		},
		{
			MethodName: "AddChannelRelationshipBinding",
			Handler:    _FellowSvr_AddChannelRelationshipBinding_Handler,
		},
		{
			MethodName: "UpdateChannelRelationshipBinding",
			Handler:    _FellowSvr_UpdateChannelRelationshipBinding_Handler,
		},
		{
			MethodName: "DelChannelRelationshipBinding",
			Handler:    _FellowSvr_DelChannelRelationshipBinding_Handler,
		},
		{
			MethodName: "GetTopNFellow",
			Handler:    _FellowSvr_GetTopNFellow_Handler,
		},
		{
			MethodName: "GetRareConfig",
			Handler:    _FellowSvr_GetRareConfig_Handler,
		},
		{
			MethodName: "GetFromAllRelationshipByIds",
			Handler:    _FellowSvr_GetFromAllRelationshipByIds_Handler,
		},
		{
			MethodName: "AddRare",
			Handler:    _FellowSvr_AddRare_Handler,
		},
		{
			MethodName: "SetBindRelation",
			Handler:    _FellowSvr_SetBindRelation_Handler,
		},
		{
			MethodName: "GetChannelRareConfig",
			Handler:    _FellowSvr_GetChannelRareConfig_Handler,
		},
		{
			MethodName: "GetRareTags",
			Handler:    _FellowSvr_GetRareTags_Handler,
		},
		{
			MethodName: "GetRareList",
			Handler:    _FellowSvr_GetRareList_Handler,
		},
		{
			MethodName: "GetHistoryFellowType",
			Handler:    _FellowSvr_GetHistoryFellowType_Handler,
		},
		{
			MethodName: "FixFellowOrder",
			Handler:    _FellowSvr_FixFellowOrder_Handler,
		},
		{
			MethodName: "CntPresentEvLogTotalCount",
			Handler:    _FellowSvr_CntPresentEvLogTotalCount_Handler,
		},
		{
			MethodName: "GetPresentEvLogOrderIds",
			Handler:    _FellowSvr_GetPresentEvLogOrderIds_Handler,
		},
		{
			MethodName: "FixPresentEvLogOrder",
			Handler:    _FellowSvr_FixPresentEvLogOrder_Handler,
		},
		{
			MethodName: "DelRare",
			Handler:    _FellowSvr_DelRare_Handler,
		},
		{
			MethodName: "AddFellowPointDelay",
			Handler:    _FellowSvr_AddFellowPointDelay_Handler,
		},
		{
			MethodName: "TestUpgradeImMsg",
			Handler:    _FellowSvr_TestUpgradeImMsg_Handler,
		},
		{
			MethodName: "TestSetFellowLevel",
			Handler:    _FellowSvr_TestSetFellowLevel_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/interaction/fellow-svr.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/interaction/fellow-svr.proto", fileDescriptor_fellow_svr_077a1558472899a7)
}

var fileDescriptor_fellow_svr_077a1558472899a7 = []byte{
	// 7317 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x7d, 0x5d, 0x8f, 0x24, 0xc7,
	0x71, 0xe0, 0x54, 0xf7, 0x7c, 0x74, 0x47, 0x77, 0xcf, 0xf4, 0xd6, 0x7c, 0xf7, 0x7e, 0xcc, 0x6c,
	0xed, 0x92, 0x5c, 0xad, 0xc8, 0x59, 0x72, 0x29, 0x8a, 0xa4, 0x48, 0x0a, 0x9a, 0xaf, 0xdd, 0xed,
	0xd3, 0xce, 0xec, 0xa8, 0x66, 0x66, 0x29, 0xf2, 0x74, 0x28, 0xd6, 0x54, 0xd5, 0xf4, 0x94, 0xb6,
	0xbb, 0xaa, 0x58, 0x55, 0x33, 0x9c, 0x21, 0x70, 0x7a, 0x10, 0x04, 0xdc, 0x41, 0xa2, 0x70, 0x0f,
	0x77, 0x80, 0x0e, 0x38, 0x9c, 0x2c, 0x03, 0x06, 0xec, 0x17, 0xdb, 0x2f, 0x36, 0xf4, 0x66, 0xf8,
	0xc1, 0xf0, 0x83, 0x9f, 0x0c, 0xc1, 0x3f, 0xc1, 0x30, 0x0c, 0xff, 0x00, 0xbf, 0x19, 0x30, 0x32,
	0x32, 0xb3, 0x3a, 0xb3, 0x3e, 0xba, 0x7b, 0x76, 0xd7, 0x14, 0xfc, 0xd6, 0x15, 0x19, 0xf9, 0x15,
	0x19, 0x19, 0x11, 0x19, 0x19, 0x91, 0x0d, 0xdf, 0x8c, 0xe3, 0x7b, 0x9f, 0x9f, 0xba, 0xd6, 0xb3,
	0xc8, 0xed, 0x9e, 0x39, 0xe1, 0x3d, 0xd7, 0x8b, 0x9d, 0xd0, 0xb4, 0x62, 0xd7, 0xf7, 0xee, 0x1d,
	0x3b, 0xdd, 0xae, 0xff, 0xc5, 0x1b, 0xd1, 0x59, 0xb8, 0x16, 0x84, 0x7e, 0xec, 0xab, 0x40, 0x21,
	0x46, 0x74, 0x16, 0xb6, 0xde, 0x4c, 0x55, 0x3c, 0xf5, 0xdc, 0x63, 0xd7, 0xb1, 0x8d, 0xc0, 0xbc,
	0xb8, 0x67, 0x1d, 0xdd, 0x0b, 0xcc, 0x0b, 0xc3, 0x32, 0xbb, 0xdd, 0x23, 0xd3, 0x7a, 0x46, 0x6b,
	0xb7, 0xd6, 0x52, 0x35, 0x42, 0xc7, 0xf2, 0x3d, 0xcb, 0xed, 0x3a, 0x6f, 0x9c, 0xdd, 0x97, 0x3e,
	0x28, 0xbe, 0xf6, 0x08, 0x2a, 0x7b, 0x5d, 0x33, 0x76, 0x0e, 0xc3, 0xae, 0x7a, 0x15, 0xaa, 0x56,
	0x60, 0x9c, 0x7a, 0xee, 0xe7, 0xa7, 0xce, 0x92, 0xb2, 0xaa, 0xdc, 0xa9, 0xea, 0x15, 0x2b, 0x38,
	0xc4, 0x6f, 0x75, 0x05, 0x6a, 0xb6, 0x19, 0x3b, 0xbc, 0xb8, 0x84, 0xc5, 0x40, 0x40, 0x14, 0x41,
	0xfb, 0xdf, 0x0a, 0x34, 0x1f, 0xe0, 0xd0, 0x37, 0x4c, 0xeb, 0x59, 0x27, 0xf4, 0x4f, 0x3d, 0x5b,
	0x7d, 0x05, 0xa6, 0x8f, 0x92, 0x2f, 0xe3, 0x34, 0xec, 0xb2, 0x76, 0x1b, 0x7d, 0x28, 0xe9, 0x79,
	0x05, 0x6a, 0x91, 0x7f, 0x1a, 0x5a, 0x8e, 0x11, 0x5f, 0x04, 0xb4, 0xf1, 0x86, 0x0e, 0x14, 0x74,
	0x70, 0x11, 0x38, 0x6a, 0x13, 0xca, 0x3d, 0xfb, 0x9d, 0xa5, 0x32, 0x56, 0x26, 0x3f, 0x53, 0x2d,
	0xbb, 0xbd, 0xce, 0xd2, 0x78, 0xba, 0xe5, 0x76, 0xaf, 0xa3, 0xfd, 0x91, 0x02, 0x15, 0xdd, 0x0c,
	0x9d, 0xb6, 0x77, 0xec, 0xab, 0x8b, 0x30, 0x15, 0x9a, 0xa1, 0x63, 0xb8, 0x36, 0x0e, 0xa3, 0xa1,
	0x4f, 0x92, 0xcf, 0xb6, 0xad, 0xde, 0x80, 0x5a, 0x74, 0x7a, 0x64, 0xf0, 0x42, 0xda, 0x7f, 0x35,
	0x3a, 0x3d, 0xd2, 0x69, 0xf9, 0x55, 0xa8, 0xda, 0x84, 0xd6, 0xfe, 0xa9, 0x17, 0xe3, 0x20, 0x1a,
	0x7a, 0xc5, 0x36, 0x2f, 0x36, 0xc9, 0xb7, 0x7a, 0x13, 0xea, 0xa1, 0xd3, 0x33, 0x5d, 0x8f, 0x95,
	0x8f, 0x63, 0x79, 0x8d, 0xc2, 0x28, 0xca, 0x0a, 0xd4, 0x8e, 0x5c, 0xcf, 0x36, 0xa2, 0xd8, 0x8c,
	0x4f, 0xa3, 0xa5, 0x89, 0x55, 0xe5, 0x4e, 0x45, 0x07, 0x02, 0xda, 0x47, 0x88, 0xf6, 0xcf, 0x25,
	0xa8, 0x91, 0xbe, 0x0e, 0xcc, 0x23, 0x1c, 0xe9, 0x3c, 0x4c, 0xc6, 0xbe, 0x71, 0x9a, 0x0c, 0x74,
	0x22, 0xf6, 0x0f, 0x5d, 0x5b, 0xbd, 0x0e, 0x10, 0xfb, 0x86, 0x69, 0xd1, 0x8e, 0xe8, 0x1a, 0x54,
	0x63, 0x7f, 0x9d, 0x02, 0xd4, 0x55, 0xa8, 0xc7, 0xbe, 0xe1, 0xb9, 0xd6, 0x33, 0xc3, 0x33, 0x7b,
	0x0e, 0x23, 0x17, 0xc4, 0xfe, 0xae, 0x6b, 0x3d, 0xdb, 0x35, 0x7b, 0x0e, 0x99, 0x08, 0x0e, 0x04,
	0xc9, 0x4c, 0x07, 0x5a, 0x21, 0x00, 0x24, 0xf2, 0x0a, 0xd4, 0x18, 0xef, 0x61, 0xf1, 0x04, 0x5d,
	0x05, 0x0a, 0x4a, 0x21, 0x60, 0xf3, 0x93, 0xb4, 0x79, 0x0a, 0xc2, 0xe6, 0xef, 0x41, 0xc5, 0x3f,
	0x8d, 0x91, 0x8e, 0x4b, 0x53, 0xab, 0xca, 0x9d, 0xda, 0xfd, 0xb9, 0xb5, 0x3e, 0x3b, 0xaf, 0xf1,
	0x85, 0xd0, 0xa7, 0xfc, 0xd3, 0x98, 0x7c, 0xa8, 0x1f, 0x00, 0x04, 0xa1, 0x13, 0x39, 0x5e, 0x6c,
	0x1c, 0x75, 0x96, 0x2a, 0x58, 0xe5, 0x9a, 0x58, 0x25, 0xcd, 0x51, 0x7a, 0x95, 0xe1, 0x6f, 0x74,
	0x08, 0x35, 0x70, 0xc5, 0x28, 0x35, 0xaa, 0x74, 0xd1, 0x08, 0x84, 0x12, 0x9d, 0xd2, 0x30, 0x72,
	0xce, 0x97, 0x80, 0xd3, 0x70, 0xdf, 0x39, 0xd7, 0x7e, 0x31, 0x09, 0x40, 0x5b, 0x45, 0x4a, 0x37,
	0xa1, 0xcc, 0xc9, 0x5c, 0xd6, 0xc9, 0x4f, 0x75, 0x09, 0xa6, 0x64, 0x0a, 0xf3, 0x4f, 0x42, 0xbd,
	0x34, 0x71, 0x2b, 0x1e, 0x27, 0xed, 0x4d, 0xa8, 0xb3, 0x71, 0x77, 0x9d, 0x33, 0xa7, 0xcb, 0xd9,
	0x80, 0xc2, 0x1e, 0x13, 0xd0, 0x70, 0x02, 0x2f, 0xc2, 0x14, 0xf2, 0x99, 0x17, 0x23, 0x71, 0x1b,
	0xfa, 0x24, 0xe1, 0x32, 0xca, 0x40, 0x22, 0xe5, 0xa7, 0x32, 0x94, 0x97, 0x16, 0xb6, 0x92, 0x5a,
	0xd8, 0xfe, 0xd0, 0x02, 0xdf, 0x4d, 0x48, 0xc5, 0x5a, 0xdc, 0x23, 0x20, 0xf5, 0x43, 0x80, 0xfe,
	0xc6, 0x41, 0x82, 0x0d, 0x5b, 0x08, 0x01, 0x5f, 0x7d, 0x15, 0x66, 0xac, 0xc0, 0xe0, 0x2b, 0xe9,
	0x5a, 0xbe, 0xb7, 0x74, 0x85, 0xee, 0x46, 0x2b, 0xd8, 0xa3, 0xd0, 0xb6, 0xe5, 0x7b, 0xea, 0x5b,
	0x50, 0x0d, 0xba, 0x28, 0x45, 0xc2, 0xee, 0x92, 0x9a, 0x65, 0x10, 0x2e, 0x8a, 0xf4, 0x4a, 0xc0,
	0x85, 0x52, 0x6a, 0xeb, 0xcc, 0x52, 0x9a, 0xf5, 0xb7, 0x8e, 0xba, 0x06, 0x95, 0x23, 0xd3, 0xa3,
	0x13, 0x9f, 0x5b, 0x55, 0xee, 0x4c, 0xdf, 0x9f, 0x15, 0x9b, 0xdc, 0x30, 0x3d, 0x42, 0x03, 0x7d,
	0xea, 0x88, 0xfe, 0x20, 0x63, 0xc0, 0x06, 0x91, 0x49, 0xe7, 0x07, 0x30, 0x29, 0xd2, 0x0f, 0xb9,
	0xb4, 0x4f, 0x7d, 0x9c, 0xda, 0x82, 0x48, 0x7d, 0x9c, 0xd7, 0x0a, 0xd4, 0x2c, 0x33, 0xb4, 0x0d,
	0xcb, 0xef, 0xfa, 0xa7, 0xe1, 0xd2, 0x22, 0x45, 0x20, 0xa0, 0x4d, 0x84, 0x10, 0x02, 0x85, 0xbe,
	0xdf, 0x33, 0x7a, 0x51, 0x87, 0x23, 0x2d, 0xad, 0x96, 0x09, 0x81, 0x08, 0x78, 0x27, 0xea, 0x30,
	0xbc, 0x9b, 0x50, 0xef, 0xba, 0x1d, 0x33, 0x3e, 0x0d, 0x29, 0x8d, 0x96, 0xb1, 0xa5, 0x1a, 0x87,
	0x11, 0x82, 0x7c, 0x07, 0xea, 0x9d, 0xd0, 0xb4, 0x5d, 0xaf, 0x63, 0xb8, 0xde, 0xb1, 0xbf, 0xd4,
	0xc2, 0x29, 0x2c, 0x8a, 0x53, 0x78, 0x48, 0xcb, 0x71, 0x16, 0xb5, 0x4e, 0xff, 0x83, 0x30, 0x3b,
	0xd9, 0x0f, 0x57, 0x91, 0x88, 0xe4, 0xa7, 0x76, 0x02, 0xd3, 0x0f, 0x12, 0xfe, 0x43, 0x9c, 0xbb,
	0x70, 0xa5, 0x77, 0xda, 0x8d, 0x5d, 0x43, 0x64, 0x55, 0x2a, 0x85, 0x66, 0xb0, 0xa0, 0x8f, 0x9f,
	0xc1, 0x45, 0xe6, 0xa4, 0x9b, 0x46, 0xc4, 0x25, 0x1c, 0xaa, 0xfd, 0x2e, 0xd1, 0x0f, 0x6d, 0xef,
	0xcc, 0x8d, 0x9d, 0xc3, 0xc8, 0x09, 0xff, 0x23, 0x76, 0x1f, 0x65, 0xf1, 0xf1, 0x2c, 0x8b, 0xdf,
	0x82, 0x86, 0x8b, 0x3d, 0x8b, 0x62, 0xb8, 0xa1, 0xd7, 0x29, 0x90, 0x71, 0x13, 0xa3, 0xd0, 0x64,
	0x42, 0x21, 0xd2, 0x2d, 0xab, 0xe6, 0xda, 0x6c, 0xe3, 0x55, 0x28, 0xa0, 0x6d, 0x6b, 0x7f, 0x55,
	0x82, 0x2b, 0x74, 0x52, 0x9c, 0xcd, 0x99, 0x9e, 0x71, 0x63, 0xa7, 0x27, 0xe8, 0x19, 0xf2, 0xd9,
	0xb6, 0x55, 0x15, 0xc6, 0x05, 0x12, 0xe1, 0x6f, 0x75, 0x0e, 0x26, 0xce, 0xcc, 0xee, 0xa9, 0xc3,
	0xf4, 0x0a, 0xfd, 0x20, 0x98, 0xc8, 0x6b, 0x54, 0xa9, 0xe1, 0x6f, 0x22, 0xef, 0x82, 0xd0, 0xe5,
	0x4a, 0x92, 0x8e, 0xbe, 0x8a, 0x10, 0x5c, 0x8c, 0x36, 0x5c, 0xa1, 0xca, 0xd9, 0x10, 0x76, 0xf2,
	0xe4, 0x08, 0x3b, 0xb9, 0x49, 0xab, 0x09, 0x6a, 0xfb, 0x21, 0x34, 0xe9, 0xba, 0x0a, 0x2d, 0x4d,
	0x8d, 0xd0, 0x12, 0x5d, 0x74, 0xa1, 0xa1, 0x15, 0xa8, 0x99, 0xdd, 0xd0, 0x31, 0xed, 0x0b, 0xc3,
	0xff, 0xc2, 0x63, 0x82, 0x09, 0x18, 0xe8, 0xc9, 0x17, 0x9e, 0xf6, 0xd7, 0x13, 0x32, 0x57, 0x20,
	0xfd, 0x24, 0x92, 0x2b, 0x32, 0xc9, 0xd5, 0x65, 0xa8, 0x1c, 0x87, 0x7e, 0x0f, 0x95, 0x63, 0x09,
	0xf9, 0x66, 0x8a, 0x7c, 0x13, 0xf5, 0x48, 0x98, 0x80, 0x14, 0x71, 0x06, 0xa2, 0x4c, 0x52, 0x23,
	0x30, 0xae, 0x22, 0x6f, 0x41, 0x03, 0x51, 0x08, 0xe3, 0xe0, 0x52, 0x50, 0x02, 0x63, 0xbd, 0x5d,
	0x06, 0x93, 0x85, 0xe9, 0xc4, 0x60, 0x2d, 0x39, 0x99, 0x11, 0xe2, 0x69, 0x56, 0x9c, 0xca, 0xb2,
	0x22, 0x91, 0x17, 0xa1, 0x43, 0x04, 0x61, 0xec, 0xf6, 0xb8, 0xbc, 0x06, 0x0a, 0x3a, 0x70, 0x7b,
	0x19, 0x4d, 0x5b, 0xcd, 0xc8, 0xfb, 0x05, 0x98, 0x64, 0x5c, 0x4c, 0x95, 0x1b, 0xfb, 0x12, 0x0c,
	0x87, 0x1a, 0xd2, 0x26, 0xd7, 0x70, 0xa8, 0xa7, 0x0d, 0x87, 0x15, 0xa8, 0x31, 0xc3, 0x01, 0xbb,
	0x6b, 0x88, 0x76, 0x03, 0x52, 0x64, 0x05, 0x6a, 0x5f, 0xb8, 0xf1, 0x89, 0x71, 0xea, 0x75, 0x7d,
	0xeb, 0xd9, 0xd2, 0x34, 0x35, 0x60, 0x08, 0xe8, 0x10, 0x21, 0xc9, 0xaa, 0x90, 0xcd, 0x33, 0x83,
	0x23, 0xc2, 0x55, 0xd9, 0x77, 0xce, 0x05, 0x3d, 0xdc, 0x14, 0xf4, 0xb0, 0xfa, 0x3d, 0xa8, 0x27,
	0x0a, 0x83, 0xc8, 0xb1, 0x2b, 0xc8, 0x5f, 0xd7, 0xb3, 0xfc, 0x25, 0xec, 0x2c, 0xbd, 0x16, 0x08,
	0xdb, 0xec, 0x26, 0xd4, 0xe9, 0x78, 0x0c, 0xdc, 0x04, 0xa8, 0x50, 0x1a, 0x7a, 0x8d, 0xc2, 0xf6,
	0x08, 0x88, 0xcc, 0xdb, 0x3a, 0x31, 0x3d, 0xcf, 0xe9, 0x12, 0x56, 0xa2, 0xca, 0xa3, 0xca, 0x20,
	0x94, 0x97, 0x1c, 0xb2, 0xce, 0x64, 0x11, 0xe6, 0xe8, 0xa8, 0x1d, 0xcf, 0xc6, 0x15, 0xb8, 0x03,
	0x4d, 0x9c, 0x90, 0xb8, 0x0c, 0xf3, 0x48, 0x97, 0x69, 0x02, 0x17, 0x04, 0xdb, 0x6d, 0x68, 0x3e,
	0x74, 0x62, 0x0a, 0x78, 0xec, 0x46, 0xb1, 0xee, 0x7c, 0x9e, 0x95, 0x6b, 0xda, 0xff, 0x2f, 0xc3,
	0x95, 0x14, 0x5a, 0x14, 0xe4, 0xc8, 0xbf, 0x0f, 0xa0, 0xc1, 0x76, 0x31, 0xed, 0x19, 0x79, 0xbc,
	0x76, 0x7f, 0x21, 0x4b, 0x17, 0x24, 0x48, 0x9d, 0x22, 0x53, 0x88, 0xfa, 0x3e, 0xd4, 0x45, 0x79,
	0xbc, 0x54, 0x5e, 0x2d, 0x0f, 0xa8, 0x5b, 0x13, 0x44, 0xb4, 0xba, 0x06, 0xb3, 0x09, 0xd7, 0x46,
	0x2e, 0x39, 0x9a, 0xa0, 0x19, 0x42, 0xe5, 0xe8, 0x15, 0xce, 0xbc, 0xb4, 0x84, 0x58, 0x24, 0xaf,
	0x83, 0x1a, 0x38, 0x1e, 0x53, 0x43, 0xb8, 0x57, 0x09, 0x3a, 0xdd, 0x2c, 0x4d, 0x56, 0x42, 0xb7,
	0x34, 0xc1, 0x7e, 0x17, 0xd8, 0xb2, 0xd0, 0xb5, 0x9e, 0xca, 0xce, 0x89, 0xf2, 0x11, 0x8e, 0x0b,
	0x4e, 0x93, 0xdf, 0x44, 0xc3, 0x44, 0x64, 0x89, 0x78, 0x1f, 0xc8, 0xbf, 0x74, 0xbf, 0xcc, 0x90,
	0x02, 0xd6, 0x05, 0x72, 0xf1, 0x07, 0xd0, 0x40, 0x7b, 0x30, 0x36, 0x8f, 0x8c, 0xae, 0x1b, 0x11,
	0x3b, 0xa7, 0x9c, 0x56, 0x8d, 0x82, 0x91, 0xad, 0xd7, 0x42, 0xfa, 0x41, 0x56, 0x43, 0xbb, 0x0b,
	0x0b, 0xc9, 0xf2, 0xec, 0xbb, 0xbd, 0xa0, 0x4b, 0x8d, 0x80, 0xdc, 0xb5, 0xb4, 0x61, 0x31, 0x17,
	0x37, 0x77, 0x41, 0xdf, 0x4d, 0xb6, 0x32, 0x8e, 0xa9, 0x34, 0x70, 0x49, 0xd8, 0x16, 0xc7, 0x11,
	0x7d, 0x8b, 0xdb, 0xa9, 0x7b, 0xa6, 0x1b, 0x12, 0x85, 0x70, 0xea, 0xda, 0xeb, 0xac, 0x65, 0xfc,
	0xcd, 0x60, 0x1b, 0x4c, 0x0c, 0xe2, 0x6f, 0xed, 0x47, 0xf0, 0xca, 0x86, 0x19, 0x5b, 0x27, 0x39,
	0x03, 0xdc, 0xb8, 0x20, 0x2d, 0x71, 0x16, 0x7d, 0x1b, 0xaa, 0x81, 0xe9, 0x86, 0x74, 0x54, 0x4a,
	0xd1, 0xa8, 0x48, 0x0d, 0xbd, 0x12, 0xb0, 0x7a, 0xda, 0x6f, 0x14, 0x68, 0xd2, 0x06, 0x05, 0x13,
	0x5a, 0x1c, 0x5a, 0x23, 0x67, 0x68, 0x14, 0xb6, 0x41, 0x34, 0x1d, 0x35, 0x8d, 0x99, 0xa6, 0xeb,
	0xa6, 0x8c, 0x62, 0x41, 0x1e, 0x8b, 0xa2, 0x6e, 0x0e, 0x26, 0xa8, 0x20, 0xa5, 0xcc, 0x45, 0x3f,
	0x0a, 0x4d, 0x65, 0xad, 0x03, 0xaf, 0x8e, 0x42, 0x80, 0x28, 0x50, 0x3f, 0x92, 0x57, 0x86, 0xd2,
	0x40, 0x52, 0x70, 0xe9, 0xa9, 0x4a, 0xeb, 0x13, 0x01, 0xf4, 0x99, 0x36, 0x23, 0x8c, 0x94, 0xac,
	0x30, 0xea, 0xa3, 0x50, 0x32, 0x94, 0x44, 0x94, 0xe4, 0x84, 0xc0, 0x50, 0x22, 0x37, 0xe6, 0x26,
	0x01, 0xdb, 0x0f, 0xfb, 0x6e, 0xec, 0x68, 0x31, 0x2c, 0x27, 0x13, 0xdb, 0x34, 0x3d, 0xdb, 0x25,
	0x27, 0xf0, 0x1c, 0xa9, 0xd3, 0xa0, 0xcc, 0x27, 0x69, 0xb2, 0x52, 0x4a, 0x93, 0xa9, 0x30, 0x1e,
	0x98, 0x1d, 0xde, 0x0b, 0xfe, 0x26, 0xc4, 0x16, 0x4f, 0xb1, 0xf4, 0x43, 0xfb, 0xef, 0xd0, 0x2a,
	0xea, 0x75, 0x24, 0x3a, 0xa6, 0xed, 0x3e, 0x91, 0x8e, 0xe4, 0xd4, 0xea, 0x46, 0x46, 0xe8, 0x98,
	0xd6, 0x89, 0xe1, 0x78, 0x54, 0xa9, 0x57, 0x74, 0x70, 0x23, 0x9d, 0x80, 0xb6, 0x3d, 0x5b, 0x7b,
	0x9c, 0x37, 0xe9, 0x9c, 0xed, 0xc9, 0x26, 0x4d, 0x94, 0x9d, 0x19, 0x76, 0x9c, 0x38, 0xb1, 0x11,
	0x1a, 0x7a, 0x95, 0x42, 0x0e, 0x5d, 0x5b, 0xfb, 0xdb, 0x52, 0xde, 0x6c, 0x92, 0x1d, 0x9c, 0xd6,
	0x4b, 0x74, 0x3a, 0x97, 0xd1, 0x4b, 0xef, 0x43, 0xf5, 0x34, 0x72, 0x42, 0x5a, 0xbd, 0x54, 0x64,
	0x36, 0x09, 0xd4, 0xa8, 0x10, 0x74, 0xac, 0xba, 0x0f, 0x4b, 0x92, 0x41, 0xed, 0x07, 0x28, 0x89,
	0x91, 0xae, 0x54, 0x98, 0xb7, 0xb2, 0x2d, 0x71, 0xd3, 0x5d, 0x9f, 0x17, 0x04, 0xfa, 0x13, 0xac,
	0x89, 0x04, 0x7e, 0x1b, 0x16, 0x4e, 0xcc, 0xc8, 0x90, 0x1a, 0x3e, 0x76, 0x9d, 0xae, 0x8d, 0x8b,
	0x5c, 0xd1, 0x67, 0x4f, 0xcc, 0x68, 0xa7, 0x5f, 0xf3, 0x01, 0x29, 0xca, 0xf0, 0xf3, 0x44, 0x86,
	0x9f, 0xb5, 0xbf, 0x51, 0x60, 0x76, 0xdf, 0xf1, 0x6c, 0x71, 0x3e, 0xcf, 0xb3, 0x22, 0x32, 0x97,
	0x96, 0x53, 0x5c, 0x7a, 0xbd, 0xef, 0x22, 0x70, 0x6d, 0xc6, 0x96, 0xdc, 0x09, 0xd0, 0xb6, 0x47,
	0x72, 0x5a, 0x88, 0xa6, 0xcb, 0x64, 0xda, 0x74, 0xd1, 0x8e, 0x60, 0x2e, 0x3b, 0x8b, 0x28, 0x50,
	0x5f, 0x83, 0x19, 0xee, 0xd7, 0x39, 0x0d, 0x43, 0xc7, 0xb3, 0x2e, 0x98, 0xf0, 0x9d, 0x66, 0xae,
	0x1d, 0x06, 0x15, 0x1c, 0x40, 0xf1, 0x91, 0x63, 0x7a, 0x4c, 0x1c, 0x33, 0x07, 0xd0, 0x01, 0x01,
	0x49, 0xda, 0x85, 0x76, 0x51, 0xb8, 0x67, 0xb5, 0x1f, 0x0a, 0xda, 0x45, 0xc4, 0xa5, 0x3b, 0x8d,
	0x29, 0xc2, 0x51, 0x76, 0x1a, 0x95, 0x58, 0x6e, 0xd2, 0x84, 0xf6, 0xbe, 0xc0, 0xf8, 0x7d, 0x94,
	0x8d, 0x8b, 0xb6, 0x4d, 0x46, 0x32, 0xc8, 0xea, 0xd6, 0x7e, 0x04, 0x57, 0x0b, 0xab, 0xe6, 0x0d,
	0x4c, 0xb9, 0xd4, 0xc0, 0xfe, 0x55, 0x81, 0x99, 0x7d, 0x27, 0x3c, 0x73, 0x2d, 0x67, 0x33, 0x0e,
	0xbb, 0xfc, 0x10, 0x60, 0x75, 0x5d, 0x5c, 0xf6, 0x20, 0xf1, 0x46, 0x22, 0xa0, 0x1d, 0xa0, 0x01,
	0x4d, 0x0b, 0x03, 0x3f, 0x8c, 0xb9, 0xc3, 0x90, 0x82, 0xf6, 0xfc, 0x10, 0x0f, 0x8b, 0xb6, 0x43,
	0xda, 0x23, 0x93, 0x21, 0x2c, 0x55, 0xd7, 0x2b, 0x14, 0x40, 0x79, 0x86, 0xd5, 0x16, 0xfc, 0x60,
	0xac, 0x36, 0xf2, 0xcc, 0x2d, 0x68, 0xc4, 0x4e, 0xd8, 0x73, 0x3d, 0xb3, 0x2b, 0xb2, 0x55, 0x9d,
	0x03, 0x11, 0x49, 0x18, 0xa0, 0xcd, 0x74, 0x10, 0x1f, 0x20, 0x3a, 0x3e, 0x59, 0xe1, 0x99, 0x13,
	0x46, 0xae, 0xef, 0xb1, 0x63, 0x40, 0x83, 0x42, 0x9f, 0x52, 0xa0, 0xf6, 0x8f, 0x0a, 0xcc, 0x3f,
	0x32, 0x3d, 0xbb, 0xaf, 0x64, 0x8a, 0x37, 0x91, 0xb4, 0x3e, 0xa5, 0xd4, 0xa9, 0xe8, 0x0e, 0x34,
	0xdd, 0x88, 0x18, 0xf8, 0x4e, 0x10, 0x33, 0x63, 0x09, 0xa7, 0x5d, 0xd1, 0xa7, 0xdd, 0x68, 0x1d,
	0xc1, 0xb4, 0x6d, 0xd2, 0x4c, 0xcf, 0x0c, 0x9f, 0x39, 0xc2, 0x76, 0xaa, 0x50, 0x40, 0xdb, 0x26,
	0xb6, 0xba, 0x19, 0x04, 0xa4, 0x84, 0x29, 0x5b, 0x33, 0x08, 0xda, 0xb6, 0xfa, 0x5d, 0xa8, 0x47,
	0x74, 0x79, 0xa8, 0x50, 0xa3, 0xa7, 0xca, 0xab, 0x92, 0xaa, 0x94, 0x97, 0x4f, 0xaf, 0xb1, 0x0a,
	0xe4, 0x43, 0x5b, 0x82, 0x85, 0xbc, 0x59, 0x46, 0x81, 0xf6, 0x00, 0x66, 0x1f, 0x3a, 0xf1, 0xc7,
	0xce, 0xd1, 0x10, 0xfb, 0x99, 0x88, 0x01, 0xd6, 0x5b, 0xff, 0xe0, 0x57, 0xa5, 0x10, 0x22, 0xd4,
	0xbf, 0x2a, 0x71, 0x6b, 0xe9, 0xc0, 0x8c, 0x9e, 0xa9, 0xd3, 0x50, 0x4a, 0x88, 0x57, 0xa2, 0xb4,
	0x33, 0x6d, 0x9b, 0x1d, 0xc8, 0x48, 0xe5, 0x92, 0x5e, 0x31, 0x6d, 0x9b, 0x9e, 0xc6, 0x90, 0x22,
	0xe7, 0xac, 0xb0, 0xcc, 0x29, 0x72, 0xbe, 0xc7, 0xed, 0x0c, 0x52, 0xd8, 0xb7, 0x85, 0x27, 0x7b,
	0xe6, 0xf9, 0x26, 0x75, 0xfb, 0x5a, 0x3e, 0x31, 0x0f, 0x24, 0xd3, 0xb7, 0xc6, 0x61, 0x04, 0x65,
	0x0e, 0x26, 0x62, 0x37, 0xee, 0x72, 0x4f, 0x29, 0xfd, 0x20, 0x87, 0x8e, 0x1f, 0x9f, 0xf6, 0x02,
	0xf4, 0xef, 0x50, 0x7f, 0xc2, 0x14, 0xf9, 0x3e, 0x0c, 0xbb, 0xc9, 0xa9, 0xbf, 0x22, 0x9c, 0xfa,
	0x55, 0x18, 0xb7, 0x9d, 0xc8, 0x62, 0x67, 0x40, 0xfc, 0x8d, 0x4e, 0xb1, 0xd3, 0x38, 0xf6, 0x3d,
	0x23, 0x76, 0xce, 0x63, 0x3c, 0x02, 0x56, 0x75, 0xa0, 0xa0, 0x03, 0xe7, 0x9c, 0x28, 0x6c, 0xe6,
	0x96, 0x40, 0xb3, 0x62, 0xd3, 0xf7, 0x8e, 0xdd, 0x4e, 0xdf, 0xfe, 0x52, 0x44, 0xfb, 0x2b, 0x31,
	0xaf, 0x4a, 0xa2, 0x79, 0xc5, 0x7b, 0x2d, 0x0b, 0xbd, 0xce, 0xc1, 0x84, 0xf9, 0x85, 0x19, 0xda,
	0xcc, 0x46, 0xa3, 0x1f, 0xc9, 0x98, 0x27, 0xfa, 0x63, 0xd6, 0x7e, 0x57, 0x86, 0xb9, 0xec, 0xb2,
	0xe6, 0x9a, 0xc7, 0x83, 0xd7, 0x35, 0x73, 0x98, 0x2e, 0x67, 0x0f, 0xd3, 0x2f, 0xe6, 0xd3, 0xbe,
	0x0b, 0xe3, 0xb1, 0x19, 0x11, 0xbd, 0x50, 0x60, 0x01, 0x13, 0x7e, 0xd2, 0x11, 0x87, 0x98, 0x06,
	0x48, 0x33, 0xc3, 0x42, 0x82, 0x2e, 0x4d, 0x15, 0x99, 0x06, 0x02, 0xd5, 0xf5, 0x5a, 0x57, 0x58,
	0x82, 0x65, 0xc0, 0xa1, 0x19, 0xb6, 0x79, 0xc1, 0x4e, 0x31, 0x53, 0xe4, 0x7b, 0xcb, 0xbc, 0xa0,
	0x0a, 0xf7, 0x08, 0x6f, 0x33, 0xf0, 0x3c, 0x5a, 0xe5, 0x0a, 0x17, 0x61, 0x78, 0x26, 0x4d, 0x9b,
	0x26, 0x70, 0xe9, 0x23, 0x73, 0xda, 0x79, 0x58, 0x1b, 0xdd, 0x79, 0xa8, 0x6d, 0xc3, 0xdc, 0xe6,
	0x89, 0x63, 0x3d, 0x1b, 0x49, 0x52, 0x15, 0x5a, 0x9d, 0xda, 0x1f, 0x2a, 0x30, 0x9f, 0xd3, 0x4e,
	0x84, 0x42, 0x9d, 0x5a, 0x81, 0x5d, 0xb7, 0xe7, 0x52, 0x25, 0x52, 0xd1, 0x01, 0x41, 0x8f, 0x09,
	0x84, 0xec, 0x45, 0x2b, 0x10, 0x9d, 0x8c, 0x93, 0x56, 0x80, 0x47, 0x84, 0x79, 0x98, 0xb4, 0x02,
	0x74, 0x31, 0xb0, 0xa3, 0x85, 0x15, 0xec, 0x3b, 0xe7, 0xe9, 0x53, 0xe7, 0xf8, 0xa8, 0xa7, 0x4e,
	0x6d, 0x4b, 0x38, 0xab, 0x23, 0x93, 0x15, 0x9a, 0x35, 0x29, 0xde, 0x6d, 0x88, 0x32, 0xe9, 0xa7,
	0x25, 0x50, 0xd3, 0xcd, 0xc8, 0x7b, 0x60, 0xa4, 0x76, 0x46, 0xd9, 0x03, 0x23, 0x5c, 0x3e, 0xac,
	0xc1, 0x2c, 0xb5, 0x63, 0x62, 0x8a, 0x63, 0x88, 0x87, 0xaa, 0x2b, 0xac, 0x08, 0x51, 0x69, 0x93,
	0x77, 0xa0, 0xe9, 0x39, 0xe7, 0x32, 0x32, 0xd5, 0x72, 0xd3, 0x04, 0x2e, 0x60, 0x4a, 0xcb, 0x3d,
	0x95, 0x5a, 0xee, 0x00, 0x6a, 0x02, 0x47, 0x25, 0xd2, 0x42, 0x11, 0x24, 0x1c, 0x9e, 0x39, 0xba,
	0x7e, 0xc8, 0x16, 0x95, 0x7e, 0x90, 0x29, 0x71, 0x56, 0x15, 0x3c, 0xbe, 0x9c, 0x23, 0xf9, 0xc9,
	0x50, 0x9c, 0x2e, 0xfd, 0xd0, 0xbe, 0x0f, 0xf3, 0x9b, 0xa6, 0x67, 0x39, 0xdd, 0x34, 0xa3, 0xce,
	0xc3, 0xa4, 0x1f, 0x88, 0x97, 0x6a, 0x7e, 0x70, 0x38, 0x44, 0xaf, 0x12, 0xcd, 0x95, 0xd7, 0x58,
	0x14, 0x68, 0x7f, 0x5e, 0x86, 0x59, 0x69, 0xb7, 0xb1, 0x2d, 0xbe, 0x08, 0x53, 0x1d, 0xf7, 0x38,
	0x16, 0x9c, 0xbf, 0xe4, 0xb3, 0x6d, 0xab, 0xf7, 0x61, 0x3e, 0xe3, 0x9f, 0x45, 0x25, 0x40, 0xfb,
	0x9c, 0x4d, 0x7b, 0x61, 0x89, 0x42, 0x78, 0x1d, 0x54, 0x56, 0x47, 0xbc, 0x1f, 0xa5, 0x0c, 0xc0,
	0xdc, 0xb6, 0xfb, 0xfd, 0x5b, 0xd2, 0xeb, 0x00, 0x0c, 0xbb, 0x67, 0xbf, 0xc3, 0xa4, 0x74, 0x95,
	0x42, 0x76, 0xec, 0x77, 0xd4, 0x37, 0x61, 0x2e, 0xed, 0xd5, 0xc5, 0xfe, 0xa9, 0xe4, 0x56, 0x53,
	0xbe, 0x5b, 0xd2, 0x7d, 0xe2, 0xdf, 0x17, 0x7b, 0x9f, 0x14, 0xee, 0x02, 0x84, 0xce, 0x89, 0x16,
	0x45, 0x5c, 0xd2, 0x37, 0xf3, 0x93, 0x23, 0x80, 0x74, 0x9d, 0x3b, 0x77, 0xb7, 0xd7, 0x61, 0x9a,
	0x2e, 0x33, 0xf7, 0x76, 0xaf, 0x93, 0x3b, 0x5c, 0x52, 0xa5, 0x9a, 0x3b, 0x5c, 0x52, 0xe3, 0x3a,
	0xb0, 0x3b, 0x63, 0x9c, 0x16, 0xd5, 0x8a, 0x55, 0x0a, 0x39, 0x0c, 0xbb, 0xda, 0x01, 0x2c, 0xaf,
	0xdb, 0x76, 0xce, 0x9a, 0x11, 0xe6, 0x78, 0x17, 0x26, 0x99, 0x54, 0xa7, 0xc6, 0xeb, 0x4a, 0xa1,
	0x54, 0x65, 0x75, 0x18, 0xba, 0x76, 0x0d, 0x5a, 0x45, 0xad, 0x46, 0x81, 0xf6, 0x31, 0x5c, 0x3b,
	0x0c, 0xc8, 0xf9, 0xf2, 0x65, 0x77, 0xbb, 0x02, 0xd7, 0x07, 0x34, 0x1c, 0x05, 0xda, 0xb7, 0x60,
	0x79, 0x8b, 0xb3, 0x6d, 0xa6, 0xdb, 0x22, 0x26, 0x25, 0xb3, 0x29, 0xaa, 0x15, 0x05, 0xda, 0x7b,
	0x70, 0xbd, 0x2f, 0xd0, 0xc4, 0x52, 0x7e, 0x86, 0x28, 0x6c, 0xf7, 0x13, 0xb8, 0x31, 0xa8, 0x66,
	0x14, 0x3c, 0x3f, 0x25, 0x6e, 0xc0, 0xb5, 0x87, 0x4e, 0xbc, 0xde, 0x2d, 0x98, 0xab, 0xf6, 0x43,
	0x1c, 0x74, 0x51, 0x79, 0xaa, 0xe7, 0xf2, 0x65, 0x7a, 0x7e, 0x0d, 0x66, 0xa9, 0x02, 0xe1, 0x8e,
	0xa6, 0x02, 0x85, 0xa8, 0xad, 0xc1, 0x5c, 0x16, 0x31, 0x0a, 0xd4, 0x05, 0x98, 0xa4, 0xa7, 0x44,
	0x66, 0x11, 0xb1, 0x2f, 0x6d, 0x13, 0x9a, 0x87, 0x54, 0xef, 0xd3, 0x0a, 0xcf, 0xe5, 0xe7, 0x98,
	0x85, 0x2b, 0xa9, 0x46, 0xa2, 0x40, 0x6b, 0x73, 0x79, 0xf6, 0xe2, 0xed, 0x2f, 0xc3, 0x62, 0x6e,
	0x53, 0x51, 0xa0, 0xfd, 0x71, 0x09, 0x16, 0x37, 0x4f, 0x4c, 0xaf, 0xc3, 0xb8, 0x73, 0x83, 0x69,
	0x83, 0x01, 0x52, 0x78, 0x88, 0x8b, 0xe0, 0x36, 0xa0, 0xdb, 0xdd, 0x48, 0xfb, 0x09, 0xf0, 0xe2,
	0x86, 0x37, 0x9f, 0x76, 0xda, 0x0b, 0x16, 0xa1, 0xe0, 0xb4, 0x47, 0x4c, 0x1a, 0x2a, 0x91, 0xbe,
	0xe5, 0x81, 0xd8, 0x4f, 0xda, 0xba, 0x0d, 0xd3, 0xb1, 0x6f, 0x64, 0xaf, 0x7a, 0xea, 0xb1, 0x2f,
	0xb4, 0x23, 0x7b, 0x27, 0xa6, 0x72, 0xbc, 0x13, 0xa2, 0xf3, 0xa1, 0x92, 0x71, 0x3e, 0x7c, 0x04,
	0x4b, 0xf9, 0x84, 0x8a, 0x82, 0x8c, 0x5f, 0x41, 0xc9, 0xfa, 0x15, 0xfe, 0x41, 0x11, 0x9d, 0x17,
	0x8c, 0x4b, 0x9f, 0xcb, 0x07, 0x23, 0xec, 0xdc, 0xb2, 0xa4, 0xb6, 0x7e, 0x1f, 0xe7, 0xc5, 0x3f,
	0x55, 0x60, 0x3e, 0x67, 0x5a, 0x2f, 0xd7, 0x29, 0xa3, 0x3e, 0x4a, 0x99, 0xd3, 0x65, 0x1c, 0xe5,
	0x2b, 0x52, 0x40, 0x02, 0x2d, 0x27, 0x63, 0x69, 0xc7, 0x4e, 0x4f, 0x70, 0xf6, 0x8b, 0x66, 0xb5,
	0xf6, 0x17, 0x0a, 0x2c, 0x17, 0xa2, 0x16, 0x5f, 0x07, 0x27, 0xce, 0xd6, 0x92, 0xe0, 0x6c, 0xc5,
	0x60, 0xa8, 0x13, 0xff, 0x0b, 0xc3, 0x39, 0x3e, 0x76, 0x2c, 0x6e, 0xed, 0x01, 0x01, 0x6d, 0x23,
	0x84, 0x70, 0xa6, 0x80, 0x60, 0x9c, 0xdd, 0x67, 0xcb, 0x52, 0xef, 0xe3, 0x3c, 0xbd, 0x4f, 0x08,
	0x20, 0x05, 0x64, 0x50, 0x2d, 0x9f, 0x0c, 0x9b, 0x1c, 0xd3, 0x44, 0xbf, 0x2a, 0x1b, 0xfe, 0x96,
	0x13, 0x9b, 0x6e, 0xf7, 0xb9, 0xe4, 0xc1, 0xaf, 0x45, 0xbf, 0x6a, 0xaa, 0x39, 0xd9, 0xaf, 0x2a,
	0x38, 0xaf, 0x46, 0x3c, 0xbc, 0xa0, 0x1f, 0x73, 0x0b, 0xa6, 0xb9, 0x1d, 0x4c, 0xa1, 0xcc, 0xb9,
	0x3a, 0xa4, 0x8d, 0xc6, 0xb1, 0x08, 0xca, 0x98, 0xca, 0xe5, 0x41, 0x71, 0x3a, 0x05, 0x57, 0x12,
	0xc2, 0xe5, 0xc3, 0x84, 0x14, 0xa7, 0x23, 0x99, 0xc2, 0x93, 0x29, 0x53, 0x78, 0x53, 0xb8, 0x28,
	0xe4, 0xd2, 0xf0, 0xd2, 0x54, 0xfe, 0xa5, 0x22, 0x9c, 0x4d, 0x12, 0x51, 0x71, 0xe9, 0xed, 0x9e,
	0x3a, 0x17, 0x97, 0x33, 0xe7, 0xe2, 0x57, 0x61, 0x46, 0x40, 0x30, 0xa2, 0x38, 0xe4, 0x01, 0x76,
	0x7d, 0xa4, 0xfd, 0x38, 0xd4, 0x9e, 0xa1, 0xb7, 0xf2, 0x91, 0x1b, 0xc5, 0x7e, 0x78, 0xf1, 0x62,
	0x73, 0xc3, 0xdb, 0x0f, 0xb4, 0x69, 0xe8, 0x09, 0x98, 0x85, 0xaf, 0x51, 0x10, 0x39, 0x00, 0x6b,
	0xff, 0x47, 0x81, 0xa5, 0xfc, 0xde, 0x7e, 0xaf, 0x34, 0x78, 0x04, 0xf3, 0x82, 0x73, 0xf4, 0xd8,
	0xdf, 0xb8, 0x38, 0x74, 0xed, 0xe7, 0x5a, 0xdd, 0x1f, 0x48, 0x7e, 0xe2, 0xa4, 0x25, 0x34, 0x52,
	0x92, 0x18, 0x24, 0x7a, 0x2b, 0xa1, 0x0c, 0xbf, 0x46, 0x44, 0xd9, 0xf4, 0x95, 0x02, 0x57, 0x37,
	0xe9, 0x8d, 0xf7, 0xcb, 0xd1, 0x14, 0xb2, 0xca, 0x2b, 0xa7, 0x55, 0x9e, 0x7c, 0xe5, 0x3e, 0x9e,
	0xba, 0x72, 0xd7, 0x7e, 0x0c, 0xd7, 0x8a, 0x47, 0xf3, 0x92, 0xbd, 0xee, 0xff, 0xa2, 0x40, 0x8b,
	0xf4, 0xc2, 0x3a, 0xfc, 0xcf, 0x7d, 0x4f, 0x91, 0x22, 0xec, 0x54, 0x9a, 0xb0, 0x2e, 0x5c, 0x2d,
	0x9c, 0xeb, 0x4b, 0xa6, 0xeb, 0xcf, 0x4b, 0x70, 0x8d, 0xfa, 0x73, 0x47, 0xa6, 0xec, 0x4b, 0x72,
	0x5e, 0x0f, 0x66, 0x2e, 0xd9, 0x56, 0x99, 0x28, 0xb4, 0x55, 0x26, 0x07, 0xd9, 0x2a, 0x53, 0x97,
	0xb4, 0x55, 0x56, 0xe0, 0xfa, 0x00, 0x5a, 0x44, 0x81, 0xf6, 0xdb, 0x32, 0x34, 0xa5, 0xb2, 0x9d,
	0xa8, 0x23, 0x45, 0x31, 0x29, 0x83, 0xa3, 0x98, 0x4a, 0x23, 0x44, 0x31, 0x95, 0x87, 0x45, 0x31,
	0xbd, 0xfc, 0x58, 0xdf, 0x7e, 0xa4, 0xd1, 0x54, 0x71, 0xa4, 0x51, 0x65, 0x48, 0xa4, 0x51, 0x35,
	0x13, 0x69, 0x24, 0x06, 0x12, 0x41, 0x51, 0x20, 0x51, 0x6d, 0x50, 0x20, 0x51, 0xfd, 0xd2, 0x5e,
	0xd1, 0x7e, 0x30, 0x55, 0x43, 0x0c, 0xa6, 0xd2, 0x4c, 0xf4, 0x62, 0xeb, 0x3e, 0x3f, 0x39, 0x14,
	0xdf, 0xb3, 0x2f, 0x43, 0x05, 0x9d, 0x65, 0x54, 0x6c, 0x94, 0xc9, 0xa8, 0xc9, 0x37, 0x21, 0xc8,
	0x35, 0x00, 0xdf, 0x33, 0x7a, 0xae, 0x85, 0x85, 0x65, 0x2c, 0xac, 0xf8, 0xde, 0x8e, 0x6b, 0x11,
	0x81, 0x7f, 0x84, 0xaa, 0x23, 0xdd, 0x45, 0xae, 0x36, 0x7b, 0xee, 0x40, 0x92, 0x27, 0xfc, 0x80,
	0x7c, 0x19, 0x39, 0x28, 0x6c, 0xb3, 0x52, 0x5a, 0xd4, 0xfc, 0x56, 0xe1, 0x47, 0xea, 0x22, 0x69,
	0xf3, 0x00, 0x9a, 0x62, 0xd8, 0xce, 0xc8, 0xb7, 0x95, 0xd3, 0xfd, 0x98, 0x1e, 0x34, 0xf9, 0x76,
	0x61, 0x2e, 0x74, 0x2c, 0xc7, 0x3d, 0x73, 0xe4, 0xb6, 0x4a, 0x23, 0xb4, 0xa5, 0xf2, 0x9a, 0xfd,
	0xf6, 0xc8, 0xf1, 0x77, 0xcb, 0x0d, 0x1d, 0x2b, 0x7e, 0x29, 0xc7, 0xdf, 0xdc, 0xa6, 0xa2, 0x40,
	0xfb, 0x36, 0x2e, 0xea, 0x13, 0xb2, 0xc6, 0x32, 0xe3, 0xc8, 0x74, 0x55, 0xd2, 0x74, 0xfd, 0x37,
	0x05, 0x1a, 0x49, 0x9d, 0x74, 0x74, 0xfa, 0x65, 0x7d, 0xc5, 0x23, 0x58, 0xb7, 0xe2, 0xed, 0xa7,
	0xb0, 0xf5, 0x07, 0x86, 0x3f, 0xbe, 0x0b, 0x75, 0xee, 0x46, 0xc6, 0x08, 0xea, 0xc9, 0x01, 0x11,
	0xd4, 0x35, 0x86, 0x89, 0x41, 0xd4, 0xe9, 0xd0, 0xe6, 0xa9, 0x4c, 0x68, 0xb3, 0xb6, 0x0f, 0x8b,
	0xd2, 0xf4, 0xe9, 0xc9, 0x18, 0x09, 0xf1, 0x1e, 0x00, 0xd9, 0x42, 0x2c, 0xae, 0x8d, 0xb2, 0xd2,
	0xb2, 0xd8, 0xa9, 0x54, 0x51, 0xaf, 0xf6, 0xf8, 0xa7, 0xf6, 0x39, 0x9a, 0x54, 0x99, 0xc5, 0x88,
	0x82, 0x21, 0xab, 0x91, 0xea, 0xb2, 0x74, 0x89, 0x2e, 0xbb, 0xb0, 0xfa, 0xd0, 0x89, 0xa5, 0xbd,
	0xf1, 0xc2, 0x61, 0x2b, 0xe2, 0x29, 0xb2, 0x2c, 0x9e, 0x22, 0xb5, 0xcf, 0x79, 0x04, 0x2d, 0x0d,
	0xf9, 0xe0, 0x97, 0xe7, 0xfd, 0x25, 0x54, 0x06, 0xcb, 0xfe, 0xd2, 0x30, 0xd9, 0x5f, 0x4e, 0xcb,
	0x7e, 0xed, 0x4f, 0xca, 0x70, 0x73, 0xc8, 0x0c, 0x73, 0x23, 0x69, 0x94, 0xaf, 0x2f, 0x92, 0xe6,
	0xbf, 0x80, 0x5a, 0x18, 0x43, 0x93, 0xd3, 0x46, 0x9f, 0x76, 0x7a, 0xf3, 0xf8, 0x6b, 0x0a, 0xa0,
	0x51, 0x57, 0xa1, 0x4e, 0xda, 0xb5, 0x02, 0xd6, 0x1a, 0xb3, 0xf9, 0x4e, 0xcc, 0x68, 0x33, 0xa0,
	0x8d, 0x7c, 0x8f, 0xc8, 0xd1, 0xee, 0xb1, 0x31, 0x7a, 0xf0, 0xe4, 0x34, 0xc1, 0xef, 0x7f, 0x6b,
	0x0b, 0xa8, 0xc3, 0xf6, 0x25, 0xb1, 0xaa, 0x3b, 0x9f, 0x6b, 0x4f, 0x51, 0x46, 0xa5, 0xe1, 0x2f,
	0x1e, 0x63, 0xf2, 0x6b, 0x05, 0x2a, 0xba, 0x43, 0x9d, 0xee, 0xc8, 0xe4, 0x49, 0xce, 0x17, 0xf9,
	0xa9, 0xbe, 0x0e, 0xe3, 0x09, 0xd3, 0x4d, 0xdf, 0x5f, 0x92, 0xc4, 0x86, 0xd3, 0x4f, 0xf8, 0xd2,
	0x11, 0x4b, 0x7d, 0x1d, 0x54, 0xd9, 0xf9, 0x8f, 0x92, 0x83, 0xf2, 0x63, 0x53, 0x4a, 0xf4, 0x3a,
	0x0c, 0xbb, 0x3c, 0x49, 0x6c, 0xbc, 0x9f, 0x24, 0xc6, 0xe3, 0xed, 0x27, 0xfa, 0xf1, 0xf6, 0x9a,
	0x0d, 0x0d, 0xbc, 0x90, 0x42, 0x77, 0x6e, 0xd7, 0x0f, 0xc9, 0x5e, 0x89, 0x62, 0x33, 0x8c, 0x8d,
	0x13, 0xe7, 0x9c, 0x07, 0x9a, 0x20, 0xe0, 0x91, 0x73, 0x4e, 0x76, 0x1d, 0xd1, 0x63, 0xa4, 0x88,
	0x5d, 0x39, 0x3a, 0x9e, 0x4d, 0x0a, 0xae, 0x03, 0x44, 0x27, 0xa6, 0xed, 0x7f, 0x81, 0x65, 0x65,
	0x76, 0xd7, 0x80, 0x90, 0x47, 0xce, 0xb9, 0xf6, 0x33, 0x05, 0x66, 0x74, 0xa7, 0x6b, 0x12, 0x1e,
	0x8a, 0x4e, 0xdc, 0x60, 0xc3, 0x3f, 0x57, 0xbf, 0x25, 0xe5, 0xd8, 0x28, 0x39, 0x82, 0x93, 0x51,
	0x40, 0xca, 0xad, 0xf9, 0x00, 0xa6, 0x8f, 0xdc, 0x8e, 0x18, 0x89, 0x5f, 0x1a, 0x50, 0xb3, 0x71,
	0xe4, 0x76, 0xfa, 0x97, 0x22, 0xda, 0x16, 0xd4, 0x77, 0xa2, 0xce, 0xae, 0x1f, 0xbb, 0xc7, 0x17,
	0xed, 0x5e, 0x87, 0x58, 0x3a, 0x7e, 0xe8, 0x76, 0x5c, 0x7e, 0x01, 0xc7, 0xbe, 0xd4, 0x6b, 0x50,
	0x8d, 0x4f, 0x4e, 0x7b, 0x47, 0x9e, 0xe9, 0x76, 0x93, 0xbc, 0x32, 0x0e, 0xd0, 0xd6, 0x61, 0x7e,
	0xd3, 0xf7, 0x3c, 0xc7, 0x8a, 0x1d, 0x7b, 0x3f, 0x0e, 0x5d, 0xaf, 0xf3, 0xc0, 0x0f, 0x77, 0x5c,
	0x8b, 0xd0, 0xb7, 0xeb, 0x1c, 0xc7, 0xfc, 0x36, 0x8f, 0xfc, 0x56, 0xe7, 0x60, 0x22, 0x74, 0x3b,
	0x27, 0xdc, 0x6e, 0xa5, 0x1f, 0xda, 0x57, 0x93, 0x50, 0x17, 0xe9, 0x91, 0x89, 0xd0, 0x48, 0x85,
	0xc4, 0xd3, 0x53, 0x47, 0x2a, 0x24, 0x3e, 0xed, 0x1c, 0x28, 0x8b, 0xce, 0x81, 0x64, 0xb1, 0xc7,
	0x85, 0xe4, 0x0a, 0xce, 0x6e, 0x13, 0x79, 0xec, 0x46, 0x47, 0x23, 0xb0, 0xdb, 0x63, 0x58, 0x34,
	0x3d, 0xb7, 0x87, 0x60, 0xc3, 0x3f, 0x36, 0x22, 0x27, 0x8e, 0xbb, 0x4e, 0xcf, 0x61, 0x37, 0x9f,
	0x45, 0x34, 0x9f, 0x4f, 0x2a, 0x3d, 0x39, 0xde, 0x4f, 0xaa, 0x10, 0x1b, 0x28, 0x14, 0x66, 0x6c,
	0x1c, 0xf9, 0xe7, 0x79, 0x87, 0x8b, 0x14, 0x97, 0xe8, 0x33, 0x61, 0x8a, 0x6d, 0x3e, 0x85, 0x25,
	0x8b, 0x53, 0xdf, 0x88, 0x90, 0xfc, 0xc6, 0xb1, 0x1f, 0x12, 0x93, 0x92, 0x65, 0xcc, 0xdd, 0x14,
	0xdb, 0xcb, 0x5d, 0x29, 0x7d, 0xde, 0xca, 0x5d, 0xc0, 0xdb, 0x30, 0x1d, 0x05, 0xa6, 0xe5, 0x18,
	0xc7, 0x5d, 0x93, 0x6e, 0x2e, 0x6a, 0x91, 0xd7, 0x11, 0xfa, 0xa0, 0x6b, 0xe2, 0xc6, 0x5a, 0x87,
	0x19, 0x4c, 0x6f, 0x42, 0x24, 0x7a, 0x55, 0x4b, 0x43, 0x0f, 0x96, 0x33, 0x81, 0x03, 0x7c, 0x57,
	0xe9, 0x0d, 0x52, 0x83, 0x34, 0x40, 0x37, 0xd9, 0x0d, 0xa8, 0xb9, 0xbd, 0x7e, 0x2f, 0x35, 0xca,
	0x62, 0x6e, 0x8f, 0x77, 0xf1, 0x6d, 0x58, 0x3c, 0x0e, 0x5d, 0xb2, 0xd5, 0xe8, 0x78, 0x04, 0x76,
	0xa7, 0xd9, 0x0a, 0xf3, 0xb4, 0x78, 0x9f, 0x94, 0x0a, 0x09, 0x26, 0xdf, 0x85, 0xe9, 0x5e, 0xd4,
	0x31, 0x3c, 0xe4, 0x70, 0xbc, 0x1e, 0x6c, 0xe0, 0xc8, 0xa4, 0xa5, 0x16, 0xb7, 0x80, 0x5e, 0xef,
	0x89, 0x1b, 0x02, 0x03, 0x32, 0xbe, 0xa4, 0x8a, 0x70, 0x7a, 0x55, 0xb9, 0x33, 0xa1, 0x4f, 0x1d,
	0xb9, 0x5f, 0xa2, 0x16, 0xfc, 0x00, 0xea, 0x98, 0x14, 0xca, 0x96, 0x63, 0x69, 0x06, 0x25, 0xe1,
	0x52, 0xd1, 0xda, 0xe9, 0xb5, 0xe8, 0xf4, 0x88, 0x03, 0xe8, 0x55, 0x64, 0x48, 0x14, 0x9f, 0xcd,
	0x12, 0x1f, 0x26, 0xf4, 0x2a, 0x81, 0xb4, 0x09, 0x40, 0xfb, 0xa7, 0x71, 0x68, 0x31, 0xed, 0x29,
	0xad, 0xbf, 0x8b, 0x71, 0xf3, 0xc2, 0xe6, 0x28, 0xbf, 0xa4, 0xcd, 0x31, 0xe4, 0xe0, 0xec, 0x13,
	0x3b, 0x5b, 0xe0, 0x55, 0xd7, 0xa6, 0xd2, 0x7f, 0x02, 0xe7, 0xfc, 0x91, 0xc4, 0x5f, 0x85, 0xc3,
	0x96, 0xc8, 0x21, 0xb8, 0xca, 0x55, 0xb1, 0xe9, 0xb6, 0x8d, 0x2a, 0x95, 0xd0, 0x07, 0x85, 0x2e,
	0x8e, 0x77, 0x92, 0x86, 0xfd, 0x20, 0x04, 0x87, 0x2b, 0x26, 0x66, 0xd0, 0xe3, 0x65, 0x92, 0x98,
	0xb1, 0x07, 0x55, 0xd7, 0x8b, 0x43, 0x1f, 0xf9, 0x88, 0xf2, 0xff, 0xdb, 0x23, 0x8e, 0xaf, 0x4d,
	0xea, 0xd9, 0xa7, 0x98, 0x78, 0xad, 0x57, 0xb0, 0x15, 0xc2, 0x7b, 0x37, 0xa1, 0xee, 0x78, 0x71,
	0x68, 0x7a, 0xec, 0xe2, 0x98, 0x6e, 0x81, 0x1a, 0x87, 0x1d, 0x86, 0xdd, 0xd6, 0x87, 0xb0, 0x90,
	0x3f, 0xb9, 0x8c, 0x1c, 0xcb, 0x49, 0xf1, 0x6a, 0x5d, 0x40, 0x5d, 0xec, 0x1a, 0x0f, 0xb2, 0xb1,
	0x90, 0x0d, 0x3d, 0x11, 0xc7, 0x64, 0x1c, 0xb7, 0x61, 0xfa, 0xc4, 0x0c, 0x82, 0x0b, 0xa3, 0x63,
	0xf6, 0x1c, 0xc3, 0xf5, 0x23, 0xd6, 0x48, 0x1d, 0xa1, 0x0f, 0xcd, 0x9e, 0xd3, 0xf6, 0x23, 0xa2,
	0x13, 0x05, 0x2c, 0xd3, 0xb3, 0x43, 0x9f, 0x99, 0x84, 0x55, 0xbd, 0x99, 0x60, 0xae, 0x53, 0xb8,
	0xf6, 0xff, 0x14, 0x98, 0x15, 0x47, 0x2e, 0x9c, 0x44, 0x02, 0xb3, 0xe3, 0x30, 0xfe, 0x54, 0x28,
	0x7f, 0x12, 0x08, 0xf2, 0x27, 0xd1, 0x89, 0x58, 0x1c, 0xb9, 0x5f, 0xd2, 0xa9, 0x4c, 0xe8, 0x15,
	0x02, 0xd8, 0x77, 0xbf, 0x74, 0x68, 0xe4, 0x9b, 0x67, 0xd3, 0x24, 0x91, 0xc4, 0x1c, 0xad, 0x25,
	0x30, 0x16, 0xfe, 0x98, 0xa0, 0x08, 0x52, 0xb9, 0x91, 0x40, 0xd1, 0x8e, 0xfc, 0x99, 0x02, 0x73,
	0xd9, 0xd1, 0x45, 0x01, 0x91, 0xdb, 0x82, 0xf5, 0x51, 0xbc, 0xe7, 0x10, 0x8b, 0x3a, 0x15, 0x62,
	0xb3, 0x6b, 0xf4, 0x1d, 0x26, 0x13, 0x3a, 0x20, 0x88, 0x66, 0x86, 0xa0, 0x53, 0x82, 0x20, 0x24,
	0xf1, 0xee, 0x13, 0x7a, 0x15, 0x21, 0x7b, 0x66, 0xc7, 0xd1, 0x6e, 0x83, 0x2a, 0xb6, 0x4a, 0x0e,
	0xe4, 0xce, 0xe7, 0xe9, 0x95, 0xd5, 0xf6, 0x65, 0x4a, 0x22, 0x56, 0x14, 0xa8, 0x1f, 0x42, 0x5d,
	0xe4, 0x6f, 0xa6, 0xd7, 0x8b, 0x87, 0x2c, 0x61, 0x6b, 0xba, 0xdc, 0xf5, 0xba, 0x8d, 0x7e, 0xe3,
	0x17, 0x6b, 0xf3, 0x15, 0x79, 0xa0, 0xd8, 0x66, 0x94, 0xd1, 0xb8, 0xda, 0x21, 0xcc, 0x8b, 0x68,
	0x34, 0x9a, 0xe0, 0xc5, 0x7b, 0xbf, 0x23, 0x6f, 0x15, 0xde, 0x6c, 0xce, 0x00, 0x5e, 0x93, 0x07,
	0xb0, 0xe5, 0x74, 0x1d, 0x3a, 0x80, 0x34, 0xe2, 0x92, 0xdc, 0x24, 0x47, 0x8c, 0x02, 0xed, 0x27,
	0x70, 0xb3, 0x78, 0xbf, 0xbf, 0x0c, 0x5e, 0x97, 0xe5, 0x66, 0x39, 0x7d, 0x62, 0xff, 0x8d, 0x02,
	0xda, 0xb0, 0x01, 0x44, 0x81, 0xfa, 0x1d, 0x89, 0x9d, 0x5f, 0x1d, 0x4d, 0x5c, 0xbd, 0x24, 0xe6,
	0xfe, 0x4a, 0x81, 0xd5, 0xe2, 0x4e, 0x18, 0xc3, 0x9d, 0xc0, 0x35, 0x3e, 0x4d, 0xd9, 0x66, 0xa1,
	0x58, 0x8c, 0x05, 0x46, 0x1d, 0x78, 0xcb, 0x2a, 0x2c, 0xd3, 0xde, 0x1e, 0xb4, 0x62, 0x59, 0x56,
	0x45, 0xfd, 0xa7, 0xfd, 0x2f, 0x05, 0x6e, 0x15, 0xd7, 0xea, 0x73, 0xee, 0xd7, 0x37, 0x8d, 0x6f,
	0xc3, 0xed, 0xe1, 0x03, 0xca, 0x99, 0xc9, 0x3b, 0x83, 0x26, 0x92, 0xb7, 0x03, 0x68, 0xb5, 0x57,
	0x07, 0x75, 0x27, 0xec, 0x87, 0xb7, 0xf0, 0x8a, 0x51, 0xc7, 0x37, 0x10, 0x78, 0xac, 0xcf, 0x75,
	0x00, 0xfa, 0x78, 0x45, 0x12, 0x5c, 0xd3, 0xd0, 0xab, 0xf8, 0x7a, 0x05, 0x06, 0xb1, 0x3c, 0x80,
	0x99, 0x75, 0x6e, 0xbf, 0xb2, 0x10, 0x36, 0xbc, 0x02, 0x10, 0x22, 0xa9, 0xa8, 0x96, 0xaa, 0x71,
	0x98, 0x70, 0xd6, 0x2a, 0x25, 0x67, 0x2d, 0x72, 0xf0, 0x6b, 0x90, 0x8e, 0xb7, 0x78, 0xcb, 0x04,
	0xc7, 0x36, 0x2f, 0xb8, 0x8b, 0xc3, 0x36, 0x2f, 0x88, 0x5a, 0xb7, 0x02, 0x83, 0xa6, 0x3b, 0x53,
	0xb7, 0xc3, 0x94, 0x15, 0x3c, 0xc5, 0x84, 0xe7, 0xf7, 0xa1, 0x9a, 0x98, 0xd1, 0xec, 0x26, 0x5e,
	0x32, 0x93, 0x53, 0x63, 0xd4, 0xfb, 0xd8, 0x39, 0x8f, 0x8c, 0x8c, 0xe7, 0x3c, 0x32, 0xa2, 0x7d,
	0x06, 0x8d, 0x7d, 0xfa, 0xa2, 0x07, 0x1b, 0x5f, 0xea, 0xd5, 0x0f, 0x25, 0xfd, 0xea, 0x47, 0x5e,
	0xb6, 0xf6, 0x55, 0xc0, 0x17, 0x26, 0xd0, 0x92, 0xe5, 0x49, 0xe8, 0x04, 0x40, 0xec, 0x58, 0xed,
	0x03, 0x98, 0x49, 0xac, 0xef, 0x4b, 0x9f, 0x90, 0xda, 0x70, 0x25, 0xb1, 0x53, 0xf7, 0x5c, 0x2b,
	0x3e, 0x0d, 0x9d, 0xe8, 0x39, 0xcf, 0x6b, 0xff, 0x77, 0x1c, 0x40, 0x98, 0x67, 0xe1, 0xb3, 0x27,
	0x79, 0x13, 0xe4, 0x56, 0x2f, 0x3e, 0xac, 0x71, 0xdc, 0x61, 0x1e, 0x13, 0xc9, 0xd0, 0x97, 0xa8,
	0xa8, 0x03, 0x23, 0xd8, 0xe6, 0x71, 0x47, 0xfd, 0x2e, 0xd4, 0xad, 0xc0, 0xe8, 0xaf, 0xe3, 0xf8,
	0xf0, 0x75, 0xac, 0x59, 0x41, 0x02, 0x52, 0xbf, 0x07, 0x8d, 0x9e, 0x6b, 0x19, 0xc9, 0x59, 0x05,
	0xcf, 0x6d, 0xa9, 0x06, 0x52, 0x14, 0xd6, 0xeb, 0x3d, 0xd7, 0x4a, 0x60, 0xf2, 0xfa, 0x4c, 0xca,
	0xeb, 0x83, 0xc2, 0x9c, 0x3f, 0xd3, 0x10, 0x32, 0x07, 0x64, 0x95, 0xbf, 0xd2, 0x10, 0xaa, 0x6f,
	0xc1, 0x84, 0x15, 0x8c, 0xfa, 0x0e, 0xc9, 0xb8, 0x15, 0x6c, 0x74, 0xd4, 0xb7, 0x61, 0xb2, 0xe7,
	0xda, 0xa4, 0x4e, 0x75, 0x84, 0x3a, 0x13, 0x3d, 0xd7, 0xde, 0xe8, 0xa8, 0x1b, 0xd0, 0x20, 0x67,
	0x96, 0xc0, 0xb5, 0xe2, 0x90, 0xac, 0x72, 0x5e, 0x1c, 0x77, 0x86, 0x15, 0xf0, 0xdc, 0xb2, 0xc7,
	0xab, 0x0c, 0x3a, 0x2f, 0xd5, 0x06, 0x9c, 0x97, 0xb4, 0x9f, 0xd2, 0xf0, 0x01, 0x51, 0x42, 0x44,
	0x81, 0xfa, 0x36, 0xa3, 0x5a, 0x51, 0x2e, 0xa8, 0x80, 0x8e, 0xd4, 0x44, 0x13, 0xfe, 0x3d, 0x49,
	0xae, 0xe4, 0xf8, 0x47, 0x25, 0x69, 0x20, 0x8a, 0x9c, 0x9f, 0x2b, 0xa0, 0xee, 0x3b, 0x31, 0x11,
	0x5f, 0x5c, 0x9c, 0xe5, 0xbb, 0x44, 0xfb, 0x77, 0x4c, 0x25, 0xf1, 0x19, 0x1c, 0x81, 0xa1, 0xcb,
	0x83, 0xde, 0xf1, 0x19, 0xcf, 0xd9, 0xd1, 0x44, 0x15, 0xb0, 0x07, 0x78, 0xf0, 0xb7, 0xf6, 0x0b,
	0xcc, 0x62, 0x4b, 0x0d, 0x26, 0xf7, 0x02, 0xe6, 0xeb, 0x18, 0xcd, 0x47, 0x50, 0xdd, 0x3c, 0xf1,
	0x23, 0x07, 0x5d, 0xe6, 0xd9, 0x21, 0x0c, 0x79, 0xa8, 0x48, 0xfb, 0x55, 0x09, 0x80, 0x28, 0x51,
	0x33, 0x74, 0x5e, 0x0e, 0x45, 0x97, 0xa1, 0x62, 0xda, 0xec, 0x20, 0x46, 0x27, 0x30, 0x65, 0xda,
	0xf4, 0x20, 0xb6, 0x08, 0x53, 0xf4, 0xa0, 0xc2, 0xaf, 0x5a, 0x27, 0xc9, 0x67, 0x26, 0x02, 0x60,
	0x32, 0x7d, 0xd6, 0xbc, 0xcf, 0x9c, 0xba, 0xec, 0x95, 0x20, 0xc2, 0x36, 0xf3, 0xb2, 0x46, 0x66,
	0xf3, 0xa7, 0xde, 0x5c, 0xfe, 0x02, 0x0b, 0xe6, 0x88, 0x5a, 0x7e, 0x78, 0xe6, 0x84, 0x3c, 0x8e,
	0xce, 0x8d, 0x74, 0x06, 0x49, 0x9f, 0x90, 0xab, 0xe9, 0x17, 0x15, 0xb4, 0xbb, 0x50, 0x4b, 0x08,
	0x13, 0x05, 0x89, 0x9c, 0x40, 0xf9, 0xa7, 0xf4, 0xe5, 0x04, 0x1e, 0x4b, 0xde, 0xc3, 0x98, 0x16,
	0xae, 0x70, 0xd3, 0xca, 0x74, 0xd0, 0x0d, 0xce, 0x9f, 0xd1, 0x00, 0x95, 0x9c, 0xaa, 0xc3, 0xef,
	0x1b, 0xae, 0x8a, 0x07, 0xdb, 0xe4, 0x8a, 0x9c, 0x9d, 0x51, 0xa5, 0x1d, 0x5a, 0x1e, 0x71, 0x87,
	0xa6, 0x0f, 0xb6, 0xe3, 0x99, 0x83, 0xad, 0x76, 0x9f, 0xc6, 0xe5, 0x86, 0x7e, 0x6f, 0xbd, 0x2b,
	0xdb, 0x16, 0x17, 0x6d, 0x3b, 0x62, 0x3c, 0xe4, 0xda, 0x11, 0x4a, 0x85, 0x86, 0x4e, 0x7e, 0x6a,
	0x9f, 0xc1, 0xca, 0xc0, 0x3a, 0xe8, 0x44, 0x6e, 0x70, 0x03, 0xcc, 0x18, 0xe9, 0x20, 0x97, 0x9c,
	0x21, 0x58, 0xa2, 0xe2, 0x34, 0x13, 0x52, 0x07, 0x66, 0x27, 0xba, 0x0c, 0x27, 0x6b, 0xaf, 0xc0,
	0x8c, 0x54, 0x35, 0x0a, 0xc8, 0x3e, 0x8b, 0xcd, 0x0e, 0x9d, 0x42, 0x55, 0xc7, 0xdf, 0x42, 0x0f,
	0xc5, 0x97, 0xba, 0x05, 0x3d, 0xfc, 0x5d, 0x29, 0xe9, 0x62, 0xc0, 0x6d, 0x6d, 0xc1, 0x46, 0x93,
	0xaf, 0xc7, 0xcb, 0xc3, 0x5e, 0xf0, 0x1a, 0x1f, 0xfc, 0x82, 0xd7, 0xa5, 0xdf, 0x26, 0x19, 0xfa,
	0x8e, 0xd4, 0x5b, 0x22, 0x9f, 0x55, 0x70, 0xd1, 0x0a, 0x5e, 0x47, 0x4a, 0xb8, 0x4c, 0x7e, 0xc3,
	0xab, 0x7a, 0xa9, 0x37, 0xbc, 0x34, 0x0f, 0x60, 0x8b, 0x6e, 0x14, 0x61, 0x0d, 0x4a, 0x79, 0x64,
	0x2c, 0x17, 0xc8, 0xab, 0xf1, 0x41, 0x32, 0x77, 0x22, 0x2d, 0x20, 0x1b, 0x50, 0x4b, 0xfa, 0x8b,
	0x02, 0xed, 0x7f, 0x2a, 0x30, 0x27, 0xe4, 0xe7, 0x6c, 0x39, 0x5d, 0xf3, 0xa2, 0x1d, 0x3b, 0xbd,
	0xd1, 0x17, 0x34, 0x49, 0x9f, 0x2b, 0x63, 0x56, 0x21, 0x4b, 0x9f, 0xc3, 0xc0, 0x6e, 0x33, 0x4a,
	0x1e, 0xf0, 0x61, 0x5f, 0x89, 0x38, 0xed, 0x2f, 0x1e, 0x8a, 0xd3, 0x8b, 0xc0, 0xd1, 0xfe, 0x2b,
	0x2c, 0xf4, 0xf3, 0x08, 0x92, 0xc1, 0x10, 0xaa, 0xac, 0x43, 0xcd, 0x26, 0xbf, 0x0d, 0x37, 0x76,
	0x7a, 0x11, 0xdb, 0x4a, 0xab, 0x39, 0xd7, 0x68, 0xd2, 0x14, 0x74, 0xb0, 0xf9, 0xcf, 0x48, 0x5b,
	0x86, 0xc5, 0xdc, 0xc6, 0xa3, 0x40, 0x8b, 0x60, 0xf6, 0xc0, 0x89, 0xe2, 0xc3, 0xa0, 0x13, 0x9a,
	0xb6, 0xd3, 0xee, 0xed, 0x44, 0x9d, 0x4b, 0xa9, 0x8e, 0xfc, 0x57, 0x1d, 0x86, 0x44, 0x81, 0x2d,
	0xc0, 0x5c, 0xb6, 0xd3, 0x28, 0xd0, 0x9e, 0xc2, 0x3c, 0x81, 0xef, 0x27, 0x0f, 0xa5, 0x90, 0xc6,
	0x5e, 0x7c, 0x38, 0xda, 0x12, 0x2c, 0xe4, 0xb5, 0x1b, 0x05, 0xec, 0xf5, 0x96, 0x03, 0x3f, 0xd8,
	0x1d, 0x10, 0x0b, 0xa0, 0x7d, 0x8a, 0x56, 0x93, 0x88, 0x15, 0x05, 0xea, 0x36, 0x5c, 0x89, 0xfd,
	0xc0, 0xf0, 0x8c, 0xec, 0xeb, 0x07, 0x52, 0x96, 0x7e, 0xbf, 0x1a, 0xbd, 0xa1, 0x8b, 0x93, 0x6f,
	0x14, 0x76, 0xbb, 0x30, 0x2d, 0x63, 0xa4, 0xee, 0xf8, 0x95, 0xf4, 0x1d, 0x7f, 0xbf, 0xf8, 0xcc,
	0xec, 0xca, 0x21, 0x00, 0x4f, 0xcd, 0xee, 0xdd, 0x3f, 0x50, 0x92, 0x54, 0x58, 0xb2, 0xe5, 0xaf,
	0xc1, 0xd2, 0xf6, 0xee, 0xe1, 0x8e, 0xf1, 0x60, 0xfb, 0xf1, 0xe3, 0x27, 0x1f, 0x1b, 0x07, 0x9f,
	0xec, 0x6d, 0x1b, 0x87, 0xbb, 0xdf, 0xdf, 0x7d, 0xf2, 0xf1, 0x6e, 0x73, 0x4c, 0x5d, 0x82, 0xb9,
	0x4c, 0xe9, 0x86, 0xfe, 0xa4, 0xa9, 0xe4, 0xd6, 0x7b, 0xbc, 0xbe, 0xf5, 0x09, 0x29, 0x2d, 0xa9,
	0xd7, 0x61, 0x39, 0x53, 0xda, 0xde, 0x3d, 0x68, 0xef, 0xac, 0x1f, 0x6c, 0x37, 0xcb, 0xb9, 0x95,
	0xf7, 0xd6, 0xf5, 0x83, 0xdd, 0x6d, 0xbd, 0x39, 0x7e, 0xf7, 0x8c, 0x3f, 0x3a, 0xb6, 0xd1, 0x7f,
	0xa1, 0xee, 0xba, 0x88, 0xbf, 0xd1, 0xde, 0xdd, 0x4a, 0x8f, 0x74, 0x15, 0xae, 0x15, 0xa1, 0xb4,
	0x7f, 0x70, 0xb8, 0xdd, 0x54, 0xd4, 0x15, 0xb8, 0x9a, 0x8f, 0xb1, 0x73, 0xf8, 0xf8, 0xa0, 0xdd,
	0x2c, 0xdd, 0xfd, 0x24, 0x79, 0xa1, 0xb2, 0xff, 0x7c, 0x9c, 0x06, 0x37, 0x32, 0x95, 0xf6, 0x0f,
	0xd6, 0x0f, 0x0e, 0xf7, 0x8d, 0xdd, 0x27, 0xfa, 0xce, 0xfa, 0xe3, 0xe6, 0x58, 0xee, 0xe8, 0x18,
	0xce, 0x3a, 0x99, 0x55, 0x53, 0xb9, 0xfb, 0x08, 0xa6, 0xd8, 0x4b, 0x73, 0xea, 0x2c, 0xcc, 0x6c,
	0xac, 0xef, 0xd2, 0x9e, 0x93, 0x26, 0x9a, 0x50, 0x4f, 0x80, 0x07, 0x3b, 0x7b, 0x4d, 0x45, 0x5d,
	0x00, 0x35, 0x81, 0xec, 0x6d, 0xeb, 0x3b, 0xeb, 0xbb, 0xdb, 0xbb, 0x07, 0xcd, 0xd2, 0xdd, 0xbf,
	0x57, 0xa0, 0xde, 0x16, 0x9f, 0x24, 0xe3, 0x13, 0x6f, 0xef, 0x3e, 0x6d, 0x1f, 0x6c, 0xf7, 0x07,
	0xc7, 0x00, 0xcd, 0x31, 0xf5, 0x06, 0xb4, 0x72, 0x30, 0xe8, 0xd7, 0x56, 0x53, 0x29, 0x28, 0xdf,
	0x3f, 0xdc, 0xdc, 0xdc, 0xde, 0xdf, 0x17, 0x16, 0x53, 0x2e, 0x7f, 0xb0, 0xde, 0x7e, 0xbc, 0xbd,
	0xd5, 0x2c, 0x27, 0x74, 0x95, 0x8b, 0x37, 0xd7, 0x77, 0x37, 0xb7, 0x09, 0xc2, 0x78, 0x41, 0xfb,
	0x07, 0xed, 0x9d, 0xed, 0x27, 0x87, 0x07, 0xcd, 0x89, 0xbb, 0xbf, 0x52, 0xa0, 0xc1, 0xa2, 0x64,
	0xd8, 0x9c, 0x38, 0x45, 0x0f, 0x77, 0x37, 0x9e, 0x1c, 0x8a, 0x14, 0xef, 0x4f, 0x8a, 0xf7, 0x9a,
	0x42, 0xd9, 0xdb, 0xde, 0xdd, 0x6a, 0xef, 0x3e, 0x6c, 0x2a, 0x09, 0x5d, 0x52, 0x08, 0x0f, 0xda,
	0xbb, 0xed, 0xfd, 0x47, 0xdb, 0x5b, 0xcd, 0x52, 0x11, 0x46, 0x32, 0xf2, 0xf2, 0x5d, 0x0f, 0xea,
	0xe2, 0xb5, 0xb3, 0xba, 0x0c, 0xf3, 0xfa, 0xf6, 0xfe, 0x93, 0x43, 0x7d, 0x73, 0x3b, 0xcd, 0x7f,
	0xf3, 0x70, 0x45, 0x2e, 0x6a, 0xef, 0x90, 0x51, 0x64, 0xc0, 0x4f, 0xdb, 0x7b, 0xcd, 0x52, 0x16,
	0xfc, 0x69, 0x7b, 0xaf, 0x59, 0xbe, 0xbb, 0xde, 0xbf, 0x05, 0xed, 0xf7, 0xf7, 0x78, 0xfd, 0xa0,
	0xfd, 0x64, 0x37, 0xdd, 0xdf, 0x02, 0xa8, 0x72, 0x91, 0xbe, 0xae, 0x6f, 0x37, 0x95, 0xfb, 0x7f,
	0x79, 0x0f, 0xaa, 0x2c, 0x8d, 0xe8, 0x2c, 0x54, 0x77, 0xa1, 0x21, 0xbd, 0x2a, 0xa5, 0x4a, 0x9a,
	0x37, 0xfd, 0x2e, 0x55, 0xeb, 0xfa, 0x80, 0xd2, 0x28, 0xd0, 0xc6, 0x54, 0x57, 0x08, 0x40, 0x96,
	0x5e, 0x7a, 0x51, 0x5f, 0xc9, 0xad, 0x9a, 0x7e, 0x83, 0xa6, 0xf5, 0xea, 0x28, 0x68, 0xc5, 0x5d,
	0xa1, 0xfc, 0x1b, 0xd2, 0x15, 0x0b, 0xa1, 0x19, 0xd6, 0x15, 0x8f, 0x43, 0xd1, 0xc6, 0xd4, 0x8f,
	0xa1, 0x99, 0x7e, 0xe2, 0x43, 0x5d, 0x91, 0x23, 0x3c, 0x33, 0xcf, 0x98, 0xb4, 0x56, 0x07, 0x23,
	0x60, 0xc3, 0xdd, 0xcc, 0x5b, 0x1d, 0xfc, 0x59, 0x0c, 0x35, 0x7f, 0x74, 0x99, 0x67, 0x37, 0x5a,
	0xaf, 0x8d, 0x84, 0x87, 0xbd, 0x7d, 0x86, 0x8f, 0x25, 0xa4, 0x5f, 0x06, 0x51, 0xb5, 0x01, 0x2d,
	0xf0, 0x65, 0xb9, 0x35, 0x14, 0x07, 0x7b, 0xf8, 0x6f, 0xa0, 0x66, 0x1f, 0x6a, 0x50, 0xa5, 0xfb,
	0xe5, 0xdc, 0xe7, 0x2a, 0x5a, 0xda, 0x30, 0x14, 0xbe, 0x0e, 0xe2, 0xb3, 0x00, 0xb8, 0xd8, 0x2b,
	0xa9, 0x91, 0xa5, 0xdf, 0x82, 0x90, 0xd7, 0x21, 0xef, 0x55, 0x01, 0x6d, 0x4c, 0xfd, 0x14, 0xae,
	0x64, 0x72, 0xca, 0xd5, 0x55, 0xf9, 0x54, 0x99, 0x4d, 0x5d, 0x6f, 0xdd, 0x1c, 0x82, 0x81, 0x6d,
	0xff, 0x00, 0x8f, 0x0a, 0x82, 0xed, 0xa4, 0xe6, 0xef, 0x22, 0x9e, 0x28, 0xde, 0xba, 0x31, 0xa8,
	0x98, 0x93, 0x39, 0x9b, 0x55, 0x2c, 0x93, 0x39, 0x37, 0x85, 0x59, 0x26, 0x73, 0x41, 0x62, 0x32,
	0xee, 0xac, 0xfc, 0x94, 0x54, 0x79, 0x67, 0x15, 0x26, 0xc3, 0xca, 0x3b, 0x6b, 0x40, 0x76, 0x2b,
	0x76, 0x95, 0x9f, 0x2f, 0x2a, 0x77, 0x55, 0x98, 0x89, 0x2a, 0x77, 0x35, 0x20, 0xf5, 0x74, 0x4c,
	0x0d, 0x61, 0xb9, 0x30, 0xe3, 0x55, 0xbd, 0x23, 0x85, 0x43, 0x0d, 0xc8, 0xb8, 0x6d, 0x7d, 0x63,
	0x44, 0x4c, 0xde, 0x67, 0x61, 0xee, 0xa8, 0xdc, 0xe7, 0xa0, 0x14, 0x54, 0xb9, 0xcf, 0x81, 0xc9,
	0xa8, 0xda, 0x98, 0x7a, 0x9a, 0x4d, 0xa3, 0xea, 0xa7, 0xca, 0xaa, 0xdf, 0xc8, 0x67, 0xae, 0x9c,
	0x64, 0xdc, 0xd6, 0xdd, 0x51, 0x51, 0xf9, 0xde, 0x4c, 0xe7, 0xa8, 0xca, 0x7b, 0x33, 0x27, 0xd5,
	0x55, 0xde, 0x9b, 0x79, 0x29, 0xae, 0xda, 0x18, 0x51, 0x51, 0x52, 0x88, 0xac, 0xac, 0xa2, 0xd2,
	0x81, 0xb8, 0xb2, 0x8a, 0xca, 0xc6, 0xd6, 0xa2, 0x14, 0xcc, 0xc9, 0x3b, 0x55, 0x73, 0xb6, 0x46,
	0xa6, 0xed, 0x5b, 0x43, 0x71, 0xb8, 0x34, 0xc9, 0x24, 0xa7, 0xa8, 0x05, 0xea, 0xa0, 0x9f, 0x49,
	0x23, 0x4b, 0x93, 0xdc, 0xec, 0x96, 0x94, 0xd6, 0x93, 0x92, 0xe4, 0x0a, 0xb4, 0x5e, 0x3a, 0x2f,
	0xaf, 0x40, 0xeb, 0x65, 0xf2, 0xed, 0xb4, 0x31, 0xf5, 0x11, 0x4c, 0x52, 0x1f, 0xb2, 0xba, 0xbc,
	0x26, 0xbc, 0x1c, 0xbf, 0x66, 0x1d, 0xad, 0xed, 0x99, 0x17, 0xb4, 0xa8, 0x75, 0xb3, 0xb0, 0x88,
	0xb4, 0xe2, 0x7b, 0x91, 0x43, 0x97, 0x50, 0xca, 0x39, 0x2b, 0xb0, 0x32, 0x58, 0xe2, 0x57, 0x81,
	0x95, 0xc1, 0x13, 0xb5, 0xa8, 0xfc, 0xcb, 0xa6, 0x39, 0xc9, 0xf2, 0x2f, 0x37, 0xa1, 0xaa, 0xa5,
	0x0d, 0x43, 0xc1, 0xe6, 0x7d, 0x9a, 0x54, 0x9b, 0x97, 0x63, 0xa4, 0xbe, 0x96, 0x73, 0xf9, 0x97,
	0xbb, 0x9a, 0x77, 0x46, 0x43, 0xe4, 0x66, 0x40, 0x41, 0xee, 0x8d, 0x6c, 0x06, 0x14, 0x27, 0x23,
	0xc9, 0x66, 0xc0, 0x80, 0x44, 0x1e, 0x2a, 0x94, 0x0a, 0x33, 0x4e, 0x64, 0xa1, 0x34, 0x28, 0x49,
	0x47, 0x16, 0x4a, 0x83, 0x53, 0x58, 0x70, 0x4b, 0x64, 0xf2, 0x14, 0xd4, 0xb4, 0x66, 0xce, 0x64,
	0x4a, 0xb4, 0x6e, 0x0e, 0xc1, 0x90, 0x85, 0xec, 0xd0, 0xf9, 0x0c, 0x4a, 0x63, 0xc8, 0x13, 0xb2,
	0xc5, 0xf3, 0xf9, 0x0c, 0x66, 0x73, 0xa2, 0xf7, 0x65, 0x21, 0x92, 0x9f, 0x29, 0x20, 0x0b, 0x91,
	0xa2, 0x14, 0x80, 0x31, 0xf5, 0x27, 0x98, 0x23, 0x51, 0x1c, 0x22, 0xad, 0xbe, 0x9e, 0x1a, 0xef,
	0xc0, 0x78, 0xf1, 0xd6, 0x1b, 0x97, 0xc0, 0x16, 0xf6, 0x58, 0x2a, 0xee, 0x3d, 0xb3, 0xc7, 0xb2,
	0x49, 0x0a, 0x99, 0x3d, 0x96, 0x13, 0x3a, 0x9f, 0x30, 0x84, 0x1c, 0x3f, 0x9c, 0x61, 0x88, 0x4c,
	0xd8, 0x71, 0x86, 0x21, 0xb2, 0x01, 0xc8, 0xda, 0x98, 0x6a, 0xc1, 0x5c, 0x5e, 0x52, 0xbc, 0x7a,
	0x2b, 0xbd, 0x25, 0x73, 0xde, 0x17, 0x68, 0xdd, 0x1e, 0x8e, 0x94, 0x31, 0xa6, 0x85, 0x78, 0xae,
	0x7c, 0x09, 0x23, 0xbd, 0x08, 0x5b, 0x60, 0x4c, 0xcb, 0x2f, 0xc1, 0x6a, 0x63, 0xea, 0x2f, 0x15,
	0xd0, 0x86, 0x3f, 0x45, 0xaa, 0xbe, 0x25, 0x3f, 0x57, 0x3f, 0xc2, 0xdb, 0xad, 0xad, 0xfb, 0x97,
	0xad, 0x82, 0xe3, 0x39, 0x80, 0x19, 0x0c, 0xb0, 0x10, 0xa2, 0x70, 0x6f, 0x14, 0x39, 0xe4, 0x69,
	0x5c, 0x48, 0x6b, 0x65, 0x60, 0x39, 0xe7, 0x33, 0x1e, 0xef, 0x20, 0x34, 0x7c, 0xb3, 0xa8, 0x62,
	0x12, 0xac, 0x21, 0xf3, 0x59, 0x7e, 0xc8, 0x10, 0xf2, 0xd9, 0xcc, 0x96, 0x1c, 0xf5, 0x50, 0xdc,
	0x76, 0x12, 0x3f, 0x51, 0xdc, 0xb6, 0x10, 0x2b, 0x41, 0xda, 0x9e, 0xc5, 0x28, 0x2e, 0x39, 0x00,
	0x4d, 0x2d, 0x9c, 0x74, 0xee, 0x89, 0x24, 0x2f, 0x7e, 0x8d, 0x12, 0x3b, 0xd5, 0x76, 0x31, 0xb1,
	0x69, 0xc0, 0x59, 0x31, 0xb1, 0x59, 0xa8, 0x99, 0x36, 0xa6, 0xfe, 0x4c, 0x11, 0x13, 0x2f, 0x0a,
	0x42, 0x8e, 0xd4, 0x37, 0x46, 0x0b, 0x70, 0xe1, 0xd3, 0x59, 0xbb, 0x0c, 0x3a, 0x97, 0x6d, 0xeb,
	0xb6, 0x3d, 0x20, 0x80, 0xf5, 0xf5, 0xd1, 0x9a, 0x64, 0x5c, 0xf6, 0xc6, 0x25, 0xb0, 0xb1, 0xff,
	0xff, 0xa1, 0xc0, 0x2a, 0xe5, 0x92, 0x01, 0x63, 0xb8, 0x37, 0x5a, 0xab, 0x7d, 0x86, 0x7c, 0xf3,
	0x72, 0x15, 0xf8, 0x82, 0x5c, 0xdf, 0x72, 0xba, 0x2f, 0x3e, 0x8c, 0x3e, 0xef, 0xbe, 0x79, 0xb9,
	0x0a, 0xdc, 0xc6, 0x96, 0xfc, 0xd3, 0x19, 0x03, 0x4d, 0x72, 0x70, 0x67, 0x0c, 0x34, 0xd9, 0xb1,
	0x9d, 0xb4, 0x27, 0xc4, 0x90, 0xa4, 0xdb, 0x93, 0x6e, 0x45, 0x33, 0xed, 0xc9, 0x17, 0x9f, 0xda,
	0x98, 0x7a, 0x4e, 0x9f, 0x0f, 0x2d, 0xb8, 0x32, 0x54, 0x33, 0x27, 0x95, 0xe2, 0xfb, 0xc8, 0xd6,
	0x37, 0x47, 0xc6, 0xc5, 0x9e, 0x3f, 0x84, 0x29, 0x76, 0xef, 0xab, 0x2e, 0xa4, 0x4e, 0xb5, 0xec,
	0xd6, 0xa9, 0xb5, 0x98, 0x0b, 0xe7, 0xbb, 0x38, 0x15, 0x1b, 0x20, 0xef, 0xe2, 0x6c, 0x14, 0x43,
	0x6b, 0x65, 0x60, 0x39, 0xd7, 0x6f, 0x79, 0x97, 0xc4, 0xea, 0xad, 0x7c, 0x1d, 0x2f, 0xd3, 0xfa,
	0xf6, 0x70, 0x24, 0x66, 0xfd, 0xd7, 0x84, 0x8b, 0x50, 0xb5, 0x95, 0xb3, 0x44, 0xec, 0x72, 0xb5,
	0x75, 0xb5, 0xb0, 0x2c, 0xd5, 0x12, 0x4a, 0x97, 0xbc, 0x96, 0xb8, 0x28, 0xb9, 0x5a, 0x58, 0x26,
	0x4c, 0x3c, 0xf3, 0x7c, 0x43, 0x66, 0xe2, 0x79, 0xcf, 0x49, 0x64, 0x26, 0x9e, 0xfb, 0x0a, 0x04,
	0x0e, 0x77, 0xfa, 0x81, 0x7b, 0xce, 0xd2, 0xba, 0x42, 0xdb, 0x09, 0xd5, 0x6b, 0x6b, 0x3a, 0xff,
	0xe7, 0xab, 0xa7, 0xf7, 0xd7, 0x74, 0x27, 0xe8, 0x9a, 0x96, 0x83, 0x45, 0xa4, 0xdd, 0x05, 0xa9,
	0x74, 0xbb, 0x17, 0xc4, 0x17, 0xac, 0xa5, 0x3d, 0x58, 0xde, 0xf4, 0x62, 0x66, 0xea, 0x6f, 0x9f,
	0x3d, 0xf6, 0x3b, 0x07, 0xfd, 0xb0, 0xcb, 0x65, 0xa9, 0xda, 0x81, 0xdb, 0x73, 0x74, 0x62, 0x6b,
	0x64, 0x5b, 0x44, 0xf4, 0xc4, 0x97, 0xb4, 0xf8, 0xd0, 0x91, 0x5a, 0xc4, 0x61, 0x90, 0x3d, 0x30,
	0xa0, 0x3d, 0xb9, 0x88, 0xd7, 0x48, 0xb6, 0xea, 0xdc, 0x03, 0xf7, 0x3c, 0xd3, 0xe4, 0x73, 0x4f,
	0xfa, 0x43, 0x98, 0x62, 0x37, 0xa4, 0xf2, 0x86, 0xe9, 0x5f, 0xd3, 0xca, 0x1b, 0x46, 0xbc, 0x4e,
	0x45, 0xab, 0x2a, 0xe7, 0xa2, 0x51, 0xb6, 0xaa, 0xf2, 0xaf, 0x39, 0x65, 0xab, 0xaa, 0xe8, 0xb6,
	0x12, 0xfd, 0x14, 0xe9, 0xab, 0x43, 0x59, 0x63, 0xe7, 0xdc, 0x66, 0xca, 0x1a, 0x3b, 0xf7, 0xe6,
	0x11, 0x0d, 0x99, 0xec, 0x1d, 0xa1, 0x6c, 0x6c, 0xe4, 0xde, 0x4d, 0xca, 0xc6, 0x46, 0xc1, 0x35,
	0xe3, 0xd8, 0xc6, 0xda, 0xa7, 0xaf, 0x77, 0xfc, 0xae, 0xe9, 0x75, 0xd6, 0xde, 0xb9, 0x1f, 0xc7,
	0x6b, 0x96, 0xdf, 0xbb, 0x87, 0x7f, 0xc1, 0x66, 0xf9, 0xdd, 0x7b, 0xec, 0x3d, 0x81, 0x48, 0xf8,
	0x37, 0xb8, 0xa3, 0x49, 0x2c, 0x7d, 0xfb, 0xdf, 0x03, 0x00, 0x00, 0xff, 0xff, 0x89, 0x6f, 0x3a,
	0x5f, 0x3d, 0x6e, 0x00, 0x00,
}
