// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/esport-skill/esport-skill.proto

package esport_skill

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockEsportSkillClient is a mock of EsportSkillClient interface.
type MockEsportSkillClient struct {
	ctrl     *gomock.Controller
	recorder *MockEsportSkillClientMockRecorder
}

// MockEsportSkillClientMockRecorder is the mock recorder for MockEsportSkillClient.
type MockEsportSkillClientMockRecorder struct {
	mock *MockEsportSkillClient
}

// NewMockEsportSkillClient creates a new mock instance.
func NewMockEsportSkillClient(ctrl *gomock.Controller) *MockEsportSkillClient {
	mock := &MockEsportSkillClient{ctrl: ctrl}
	mock.recorder = &MockEsportSkillClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEsportSkillClient) EXPECT() *MockEsportSkillClientMockRecorder {
	return m.recorder
}

// AddEsportGameConfig mocks base method.
func (m *MockEsportSkillClient) AddEsportGameConfig(ctx context.Context, in *AddEsportGameConfigRequest, opts ...grpc.CallOption) (*AddEsportGameConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddEsportGameConfig", varargs...)
	ret0, _ := ret[0].(*AddEsportGameConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddEsportGameConfig indicates an expected call of AddEsportGameConfig.
func (mr *MockEsportSkillClientMockRecorder) AddEsportGameConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddEsportGameConfig", reflect.TypeOf((*MockEsportSkillClient)(nil).AddEsportGameConfig), varargs...)
}

// AddRenownedPlayer mocks base method.
func (m *MockEsportSkillClient) AddRenownedPlayer(ctx context.Context, in *AddRenownedPlayerRequest, opts ...grpc.CallOption) (*AddRenownedPlayerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddRenownedPlayer", varargs...)
	ret0, _ := ret[0].(*AddRenownedPlayerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddRenownedPlayer indicates an expected call of AddRenownedPlayer.
func (mr *MockEsportSkillClientMockRecorder) AddRenownedPlayer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRenownedPlayer", reflect.TypeOf((*MockEsportSkillClient)(nil).AddRenownedPlayer), varargs...)
}

// AddUserAuditSkill mocks base method.
func (m *MockEsportSkillClient) AddUserAuditSkill(ctx context.Context, in *AddUserAuditSkillRequest, opts ...grpc.CallOption) (*AddUserAuditSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddUserAuditSkill", varargs...)
	ret0, _ := ret[0].(*AddUserAuditSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserAuditSkill indicates an expected call of AddUserAuditSkill.
func (mr *MockEsportSkillClientMockRecorder) AddUserAuditSkill(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserAuditSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).AddUserAuditSkill), varargs...)
}

// BatGetUserSkillFreezeStatus mocks base method.
func (m *MockEsportSkillClient) BatGetUserSkillFreezeStatus(ctx context.Context, in *BatGetUserSkillFreezeStatusRequest, opts ...grpc.CallOption) (*BatGetUserSkillFreezeStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatGetUserSkillFreezeStatus", varargs...)
	ret0, _ := ret[0].(*BatGetUserSkillFreezeStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetUserSkillFreezeStatus indicates an expected call of BatGetUserSkillFreezeStatus.
func (mr *MockEsportSkillClientMockRecorder) BatGetUserSkillFreezeStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetUserSkillFreezeStatus", reflect.TypeOf((*MockEsportSkillClient)(nil).BatGetUserSkillFreezeStatus), varargs...)
}

// BatchCheckCoachHasGame mocks base method.
func (m *MockEsportSkillClient) BatchCheckCoachHasGame(ctx context.Context, in *BatchCheckCoachHasGameRequest, opts ...grpc.CallOption) (*BatchCheckCoachHasGameResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchCheckCoachHasGame", varargs...)
	ret0, _ := ret[0].(*BatchCheckCoachHasGameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckCoachHasGame indicates an expected call of BatchCheckCoachHasGame.
func (mr *MockEsportSkillClientMockRecorder) BatchCheckCoachHasGame(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckCoachHasGame", reflect.TypeOf((*MockEsportSkillClient)(nil).BatchCheckCoachHasGame), varargs...)
}

// BatchGetAuditSkill mocks base method.
func (m *MockEsportSkillClient) BatchGetAuditSkill(ctx context.Context, in *BatchGetAuditSkillRequest, opts ...grpc.CallOption) (*BatchGetAuditSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetAuditSkill", varargs...)
	ret0, _ := ret[0].(*BatchGetAuditSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAuditSkill indicates an expected call of BatchGetAuditSkill.
func (mr *MockEsportSkillClientMockRecorder) BatchGetAuditSkill(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAuditSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).BatchGetAuditSkill), varargs...)
}

// BatchGetCoachLabelsForGame mocks base method.
func (m *MockEsportSkillClient) BatchGetCoachLabelsForGame(ctx context.Context, in *BatchGetCoachLabelsForGameRequest, opts ...grpc.CallOption) (*BatchGetCoachLabelsForGameResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetCoachLabelsForGame", varargs...)
	ret0, _ := ret[0].(*BatchGetCoachLabelsForGameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetCoachLabelsForGame indicates an expected call of BatchGetCoachLabelsForGame.
func (mr *MockEsportSkillClientMockRecorder) BatchGetCoachLabelsForGame(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetCoachLabelsForGame", reflect.TypeOf((*MockEsportSkillClient)(nil).BatchGetCoachLabelsForGame), varargs...)
}

// BatchGetGameInfoByNames mocks base method.
func (m *MockEsportSkillClient) BatchGetGameInfoByNames(ctx context.Context, in *BatchGetGameInfoByNamesRequest, opts ...grpc.CallOption) (*BatchGetGameInfoByNamesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetGameInfoByNames", varargs...)
	ret0, _ := ret[0].(*BatchGetGameInfoByNamesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGameInfoByNames indicates an expected call of BatchGetGameInfoByNames.
func (mr *MockEsportSkillClientMockRecorder) BatchGetGameInfoByNames(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGameInfoByNames", reflect.TypeOf((*MockEsportSkillClient)(nil).BatchGetGameInfoByNames), varargs...)
}

// BatchGetUserCurrentSkill mocks base method.
func (m *MockEsportSkillClient) BatchGetUserCurrentSkill(ctx context.Context, in *BatchGetUserCurrentSkillRequest, opts ...grpc.CallOption) (*BatchGetUserCurrentSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetUserCurrentSkill", varargs...)
	ret0, _ := ret[0].(*BatchGetUserCurrentSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserCurrentSkill indicates an expected call of BatchGetUserCurrentSkill.
func (mr *MockEsportSkillClientMockRecorder) BatchGetUserCurrentSkill(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserCurrentSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).BatchGetUserCurrentSkill), varargs...)
}

// BatchGetUserGameSpecialLabel mocks base method.
func (m *MockEsportSkillClient) BatchGetUserGameSpecialLabel(ctx context.Context, in *BatchGetUserGameSpecialLabelRequest, opts ...grpc.CallOption) (*BatchGetUserGameSpecialLabelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetUserGameSpecialLabel", varargs...)
	ret0, _ := ret[0].(*BatchGetUserGameSpecialLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserGameSpecialLabel indicates an expected call of BatchGetUserGameSpecialLabel.
func (mr *MockEsportSkillClientMockRecorder) BatchGetUserGameSpecialLabel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserGameSpecialLabel", reflect.TypeOf((*MockEsportSkillClient)(nil).BatchGetUserGameSpecialLabel), varargs...)
}

// BatchGetUserRenownedInfo mocks base method.
func (m *MockEsportSkillClient) BatchGetUserRenownedInfo(ctx context.Context, in *BatchGetUserRenownedInfoRequest, opts ...grpc.CallOption) (*BatchGetUserRenownedInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetUserRenownedInfo", varargs...)
	ret0, _ := ret[0].(*BatchGetUserRenownedInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserRenownedInfo indicates an expected call of BatchGetUserRenownedInfo.
func (mr *MockEsportSkillClientMockRecorder) BatchGetUserRenownedInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserRenownedInfo", reflect.TypeOf((*MockEsportSkillClient)(nil).BatchGetUserRenownedInfo), varargs...)
}

// BatchGetUserSpecialLabel mocks base method.
func (m *MockEsportSkillClient) BatchGetUserSpecialLabel(ctx context.Context, in *BatchGetUserSpecialLabelRequest, opts ...grpc.CallOption) (*BatchGetUserSpecialLabelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetUserSpecialLabel", varargs...)
	ret0, _ := ret[0].(*BatchGetUserSpecialLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserSpecialLabel indicates an expected call of BatchGetUserSpecialLabel.
func (mr *MockEsportSkillClientMockRecorder) BatchGetUserSpecialLabel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserSpecialLabel", reflect.TypeOf((*MockEsportSkillClient)(nil).BatchGetUserSpecialLabel), varargs...)
}

// BatchRemoveRenownedPlayers mocks base method.
func (m *MockEsportSkillClient) BatchRemoveRenownedPlayers(ctx context.Context, in *BatchRemoveRenownedPlayersRequest, opts ...grpc.CallOption) (*BatchRemoveRenownedPlayersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchRemoveRenownedPlayers", varargs...)
	ret0, _ := ret[0].(*BatchRemoveRenownedPlayersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchRemoveRenownedPlayers indicates an expected call of BatchRemoveRenownedPlayers.
func (mr *MockEsportSkillClientMockRecorder) BatchRemoveRenownedPlayers(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchRemoveRenownedPlayers", reflect.TypeOf((*MockEsportSkillClient)(nil).BatchRemoveRenownedPlayers), varargs...)
}

// CalculatePrice mocks base method.
func (m *MockEsportSkillClient) CalculatePrice(ctx context.Context, in *CalculatePriceRequest, opts ...grpc.CallOption) (*CalculatePriceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CalculatePrice", varargs...)
	ret0, _ := ret[0].(*CalculatePriceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculatePrice indicates an expected call of CalculatePrice.
func (mr *MockEsportSkillClientMockRecorder) CalculatePrice(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculatePrice", reflect.TypeOf((*MockEsportSkillClient)(nil).CalculatePrice), varargs...)
}

// CalculatePriceByGames mocks base method.
func (m *MockEsportSkillClient) CalculatePriceByGames(ctx context.Context, in *CalculatePriceByGamesRequest, opts ...grpc.CallOption) (*CalculatePriceByGamesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CalculatePriceByGames", varargs...)
	ret0, _ := ret[0].(*CalculatePriceByGamesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculatePriceByGames indicates an expected call of CalculatePriceByGames.
func (mr *MockEsportSkillClientMockRecorder) CalculatePriceByGames(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculatePriceByGames", reflect.TypeOf((*MockEsportSkillClient)(nil).CalculatePriceByGames), varargs...)
}

// CheckCoachCouldOpenSwitch mocks base method.
func (m *MockEsportSkillClient) CheckCoachCouldOpenSwitch(ctx context.Context, in *CheckCoachCouldOpenSwitchRequest, opts ...grpc.CallOption) (*CheckCoachCouldOpenSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckCoachCouldOpenSwitch", varargs...)
	ret0, _ := ret[0].(*CheckCoachCouldOpenSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckCoachCouldOpenSwitch indicates an expected call of CheckCoachCouldOpenSwitch.
func (mr *MockEsportSkillClientMockRecorder) CheckCoachCouldOpenSwitch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCoachCouldOpenSwitch", reflect.TypeOf((*MockEsportSkillClient)(nil).CheckCoachCouldOpenSwitch), varargs...)
}

// CheckCoachIdentityAndSkill mocks base method.
func (m *MockEsportSkillClient) CheckCoachIdentityAndSkill(ctx context.Context, in *CheckCoachIdentityAndSkillRequest, opts ...grpc.CallOption) (*CheckCoachIdentityAndSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckCoachIdentityAndSkill", varargs...)
	ret0, _ := ret[0].(*CheckCoachIdentityAndSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckCoachIdentityAndSkill indicates an expected call of CheckCoachIdentityAndSkill.
func (mr *MockEsportSkillClientMockRecorder) CheckCoachIdentityAndSkill(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCoachIdentityAndSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).CheckCoachIdentityAndSkill), varargs...)
}

// CheckLabelOrder mocks base method.
func (m *MockEsportSkillClient) CheckLabelOrder(ctx context.Context, in *CheckLabelOrderRequest, opts ...grpc.CallOption) (*CheckLabelOrderResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckLabelOrder", varargs...)
	ret0, _ := ret[0].(*CheckLabelOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckLabelOrder indicates an expected call of CheckLabelOrder.
func (mr *MockEsportSkillClientMockRecorder) CheckLabelOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckLabelOrder", reflect.TypeOf((*MockEsportSkillClient)(nil).CheckLabelOrder), varargs...)
}

// CreateLabel mocks base method.
func (m *MockEsportSkillClient) CreateLabel(ctx context.Context, in *CreateLabelRequest, opts ...grpc.CallOption) (*CreateLabelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateLabel", varargs...)
	ret0, _ := ret[0].(*CreateLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLabel indicates an expected call of CreateLabel.
func (mr *MockEsportSkillClientMockRecorder) CreateLabel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLabel", reflect.TypeOf((*MockEsportSkillClient)(nil).CreateLabel), varargs...)
}

// DebugCheckGuaranteeWinPermission mocks base method.
func (m *MockEsportSkillClient) DebugCheckGuaranteeWinPermission(ctx context.Context, in *DebugCheckGuaranteeWinPermissionRequest, opts ...grpc.CallOption) (*DebugCheckGuaranteeWinPermissionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DebugCheckGuaranteeWinPermission", varargs...)
	ret0, _ := ret[0].(*DebugCheckGuaranteeWinPermissionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DebugCheckGuaranteeWinPermission indicates an expected call of DebugCheckGuaranteeWinPermission.
func (mr *MockEsportSkillClientMockRecorder) DebugCheckGuaranteeWinPermission(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DebugCheckGuaranteeWinPermission", reflect.TypeOf((*MockEsportSkillClient)(nil).DebugCheckGuaranteeWinPermission), varargs...)
}

// DelUserSkill mocks base method.
func (m *MockEsportSkillClient) DelUserSkill(ctx context.Context, in *DelUserSkillRequest, opts ...grpc.CallOption) (*DelUserSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelUserSkill", varargs...)
	ret0, _ := ret[0].(*DelUserSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelUserSkill indicates an expected call of DelUserSkill.
func (mr *MockEsportSkillClientMockRecorder) DelUserSkill(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).DelUserSkill), varargs...)
}

// DeleteEsportGameConfig mocks base method.
func (m *MockEsportSkillClient) DeleteEsportGameConfig(ctx context.Context, in *DeleteEsportGameConfigRequest, opts ...grpc.CallOption) (*DeleteEsportGameConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteEsportGameConfig", varargs...)
	ret0, _ := ret[0].(*DeleteEsportGameConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteEsportGameConfig indicates an expected call of DeleteEsportGameConfig.
func (mr *MockEsportSkillClientMockRecorder) DeleteEsportGameConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEsportGameConfig", reflect.TypeOf((*MockEsportSkillClient)(nil).DeleteEsportGameConfig), varargs...)
}

// DeleteLabel mocks base method.
func (m *MockEsportSkillClient) DeleteLabel(ctx context.Context, in *DeleteLabelRequest, opts ...grpc.CallOption) (*DeleteLabelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteLabel", varargs...)
	ret0, _ := ret[0].(*DeleteLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteLabel indicates an expected call of DeleteLabel.
func (mr *MockEsportSkillClientMockRecorder) DeleteLabel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLabel", reflect.TypeOf((*MockEsportSkillClient)(nil).DeleteLabel), varargs...)
}

// EditLabel mocks base method.
func (m *MockEsportSkillClient) EditLabel(ctx context.Context, in *EditLabelRequest, opts ...grpc.CallOption) (*EditLabelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EditLabel", varargs...)
	ret0, _ := ret[0].(*EditLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EditLabel indicates an expected call of EditLabel.
func (mr *MockEsportSkillClientMockRecorder) EditLabel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EditLabel", reflect.TypeOf((*MockEsportSkillClient)(nil).EditLabel), varargs...)
}

// FreezeCoachSkill mocks base method.
func (m *MockEsportSkillClient) FreezeCoachSkill(ctx context.Context, in *FreezeCoachSkillRequest, opts ...grpc.CallOption) (*FreezeCoachSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FreezeCoachSkill", varargs...)
	ret0, _ := ret[0].(*FreezeCoachSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FreezeCoachSkill indicates an expected call of FreezeCoachSkill.
func (mr *MockEsportSkillClientMockRecorder) FreezeCoachSkill(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreezeCoachSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).FreezeCoachSkill), varargs...)
}

// GetAllGameCardConfig mocks base method.
func (m *MockEsportSkillClient) GetAllGameCardConfig(ctx context.Context, in *GetAllGameCardConfigRequest, opts ...grpc.CallOption) (*GetAllGameCardConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllGameCardConfig", varargs...)
	ret0, _ := ret[0].(*GetAllGameCardConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllGameCardConfig indicates an expected call of GetAllGameCardConfig.
func (mr *MockEsportSkillClientMockRecorder) GetAllGameCardConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllGameCardConfig", reflect.TypeOf((*MockEsportSkillClient)(nil).GetAllGameCardConfig), varargs...)
}

// GetAllGameSimpleInfo mocks base method.
func (m *MockEsportSkillClient) GetAllGameSimpleInfo(ctx context.Context, in *GetAllGameSimpleInfoRequest, opts ...grpc.CallOption) (*GetAllGameSimpleInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllGameSimpleInfo", varargs...)
	ret0, _ := ret[0].(*GetAllGameSimpleInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllGameSimpleInfo indicates an expected call of GetAllGameSimpleInfo.
func (mr *MockEsportSkillClientMockRecorder) GetAllGameSimpleInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllGameSimpleInfo", reflect.TypeOf((*MockEsportSkillClient)(nil).GetAllGameSimpleInfo), varargs...)
}

// GetBasePriceSetting mocks base method.
func (m *MockEsportSkillClient) GetBasePriceSetting(ctx context.Context, in *GetBasePriceSettingRequest, opts ...grpc.CallOption) (*GetBasePriceSettingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBasePriceSetting", varargs...)
	ret0, _ := ret[0].(*GetBasePriceSettingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBasePriceSetting indicates an expected call of GetBasePriceSetting.
func (mr *MockEsportSkillClientMockRecorder) GetBasePriceSetting(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBasePriceSetting", reflect.TypeOf((*MockEsportSkillClient)(nil).GetBasePriceSetting), varargs...)
}

// GetCoachApplicableLabels mocks base method.
func (m *MockEsportSkillClient) GetCoachApplicableLabels(ctx context.Context, in *GetCoachApplicableLabelsRequest, opts ...grpc.CallOption) (*GetCoachApplicableLabelsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCoachApplicableLabels", varargs...)
	ret0, _ := ret[0].(*GetCoachApplicableLabelsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachApplicableLabels indicates an expected call of GetCoachApplicableLabels.
func (mr *MockEsportSkillClientMockRecorder) GetCoachApplicableLabels(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachApplicableLabels", reflect.TypeOf((*MockEsportSkillClient)(nil).GetCoachApplicableLabels), varargs...)
}

// GetEsportGameConfigListByPage mocks base method.
func (m *MockEsportSkillClient) GetEsportGameConfigListByPage(ctx context.Context, in *GetEsportGameConfigListByPageRequest, opts ...grpc.CallOption) (*GetEsportGameConfigListByPageResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEsportGameConfigListByPage", varargs...)
	ret0, _ := ret[0].(*GetEsportGameConfigListByPageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEsportGameConfigListByPage indicates an expected call of GetEsportGameConfigListByPage.
func (mr *MockEsportSkillClientMockRecorder) GetEsportGameConfigListByPage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEsportGameConfigListByPage", reflect.TypeOf((*MockEsportSkillClient)(nil).GetEsportGameConfigListByPage), varargs...)
}

// GetGameDetailById mocks base method.
func (m *MockEsportSkillClient) GetGameDetailById(ctx context.Context, in *GetGameDetailByIdRequest, opts ...grpc.CallOption) (*GetGameDetailByIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameDetailById", varargs...)
	ret0, _ := ret[0].(*GetGameDetailByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameDetailById indicates an expected call of GetGameDetailById.
func (mr *MockEsportSkillClientMockRecorder) GetGameDetailById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameDetailById", reflect.TypeOf((*MockEsportSkillClient)(nil).GetGameDetailById), varargs...)
}

// GetGameDetailByIds mocks base method.
func (m *MockEsportSkillClient) GetGameDetailByIds(ctx context.Context, in *GetGameDetailByIdsRequest, opts ...grpc.CallOption) (*GetGameDetailByIdsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameDetailByIds", varargs...)
	ret0, _ := ret[0].(*GetGameDetailByIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameDetailByIds indicates an expected call of GetGameDetailByIds.
func (mr *MockEsportSkillClientMockRecorder) GetGameDetailByIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameDetailByIds", reflect.TypeOf((*MockEsportSkillClient)(nil).GetGameDetailByIds), varargs...)
}

// GetGameList mocks base method.
func (m *MockEsportSkillClient) GetGameList(ctx context.Context, in *GetGameListRequest, opts ...grpc.CallOption) (*GetGameListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameList", varargs...)
	ret0, _ := ret[0].(*GetGameListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameList indicates an expected call of GetGameList.
func (mr *MockEsportSkillClientMockRecorder) GetGameList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameList", reflect.TypeOf((*MockEsportSkillClient)(nil).GetGameList), varargs...)
}

// GetLabelIssueRecord mocks base method.
func (m *MockEsportSkillClient) GetLabelIssueRecord(ctx context.Context, in *GetLabelIssuanceRecordRequest, opts ...grpc.CallOption) (*GetLabelIssuanceRecordResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLabelIssueRecord", varargs...)
	ret0, _ := ret[0].(*GetLabelIssuanceRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLabelIssueRecord indicates an expected call of GetLabelIssueRecord.
func (mr *MockEsportSkillClientMockRecorder) GetLabelIssueRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLabelIssueRecord", reflect.TypeOf((*MockEsportSkillClient)(nil).GetLabelIssueRecord), varargs...)
}

// GetMinimumPrice mocks base method.
func (m *MockEsportSkillClient) GetMinimumPrice(ctx context.Context, in *GetMinimumPriceRequest, opts ...grpc.CallOption) (*GetMinimumPriceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMinimumPrice", varargs...)
	ret0, _ := ret[0].(*GetMinimumPriceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMinimumPrice indicates an expected call of GetMinimumPrice.
func (mr *MockEsportSkillClientMockRecorder) GetMinimumPrice(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinimumPrice", reflect.TypeOf((*MockEsportSkillClient)(nil).GetMinimumPrice), varargs...)
}

// GetSkillFreezeOperationList mocks base method.
func (m *MockEsportSkillClient) GetSkillFreezeOperationList(ctx context.Context, in *GetSkillFreezeOperationListRequest, opts ...grpc.CallOption) (*GetSkillFreezeOperationListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSkillFreezeOperationList", varargs...)
	ret0, _ := ret[0].(*GetSkillFreezeOperationListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSkillFreezeOperationList indicates an expected call of GetSkillFreezeOperationList.
func (mr *MockEsportSkillClientMockRecorder) GetSkillFreezeOperationList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSkillFreezeOperationList", reflect.TypeOf((*MockEsportSkillClient)(nil).GetSkillFreezeOperationList), varargs...)
}

// GetSpecialIssueRecord mocks base method.
func (m *MockEsportSkillClient) GetSpecialIssueRecord(ctx context.Context, in *GetSpecialLabelIssuanceRecordRequest, opts ...grpc.CallOption) (*GetSpecialLabelIssuanceRecordResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSpecialIssueRecord", varargs...)
	ret0, _ := ret[0].(*GetSpecialLabelIssuanceRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSpecialIssueRecord indicates an expected call of GetSpecialIssueRecord.
func (mr *MockEsportSkillClientMockRecorder) GetSpecialIssueRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSpecialIssueRecord", reflect.TypeOf((*MockEsportSkillClient)(nil).GetSpecialIssueRecord), varargs...)
}

// GetSpecialLabelList mocks base method.
func (m *MockEsportSkillClient) GetSpecialLabelList(ctx context.Context, in *GetSpecialLabelListRequest, opts ...grpc.CallOption) (*GetSpecialLabelListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSpecialLabelList", varargs...)
	ret0, _ := ret[0].(*GetSpecialLabelListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSpecialLabelList indicates an expected call of GetSpecialLabelList.
func (mr *MockEsportSkillClientMockRecorder) GetSpecialLabelList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSpecialLabelList", reflect.TypeOf((*MockEsportSkillClient)(nil).GetSpecialLabelList), varargs...)
}

// GetSwitch mocks base method.
func (m *MockEsportSkillClient) GetSwitch(ctx context.Context, in *GetSwitchRequest, opts ...grpc.CallOption) (*GetSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSwitch", varargs...)
	ret0, _ := ret[0].(*GetSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSwitch indicates an expected call of GetSwitch.
func (mr *MockEsportSkillClientMockRecorder) GetSwitch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSwitch", reflect.TypeOf((*MockEsportSkillClient)(nil).GetSwitch), varargs...)
}

// GetTopGameList mocks base method.
func (m *MockEsportSkillClient) GetTopGameList(ctx context.Context, in *GetTopGameListRequest, opts ...grpc.CallOption) (*GetTopGameListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTopGameList", varargs...)
	ret0, _ := ret[0].(*GetTopGameListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTopGameList indicates an expected call of GetTopGameList.
func (mr *MockEsportSkillClientMockRecorder) GetTopGameList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTopGameList", reflect.TypeOf((*MockEsportSkillClient)(nil).GetTopGameList), varargs...)
}

// GetUserAuditSkill mocks base method.
func (m *MockEsportSkillClient) GetUserAuditSkill(ctx context.Context, in *GetUserAuditSkillRequest, opts ...grpc.CallOption) (*GetUserAuditSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserAuditSkill", varargs...)
	ret0, _ := ret[0].(*GetUserAuditSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAuditSkill indicates an expected call of GetUserAuditSkill.
func (mr *MockEsportSkillClientMockRecorder) GetUserAuditSkill(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAuditSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).GetUserAuditSkill), varargs...)
}

// GetUserCurrentSkill mocks base method.
func (m *MockEsportSkillClient) GetUserCurrentSkill(ctx context.Context, in *GetUserCurrentSkillRequest, opts ...grpc.CallOption) (*GetUserCurrentSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserCurrentSkill", varargs...)
	ret0, _ := ret[0].(*GetUserCurrentSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserCurrentSkill indicates an expected call of GetUserCurrentSkill.
func (mr *MockEsportSkillClientMockRecorder) GetUserCurrentSkill(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserCurrentSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).GetUserCurrentSkill), varargs...)
}

// GetUserSkillByGameId mocks base method.
func (m *MockEsportSkillClient) GetUserSkillByGameId(ctx context.Context, in *GetUserSkillByGameIdRequest, opts ...grpc.CallOption) (*GetUserSkillByGameIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserSkillByGameId", varargs...)
	ret0, _ := ret[0].(*GetUserSkillByGameIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSkillByGameId indicates an expected call of GetUserSkillByGameId.
func (mr *MockEsportSkillClientMockRecorder) GetUserSkillByGameId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSkillByGameId", reflect.TypeOf((*MockEsportSkillClient)(nil).GetUserSkillByGameId), varargs...)
}

// GetUserSkillFreezeStatus mocks base method.
func (m *MockEsportSkillClient) GetUserSkillFreezeStatus(ctx context.Context, in *GetUserSkillFreezeStatusRequest, opts ...grpc.CallOption) (*GetUserSkillFreezeStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserSkillFreezeStatus", varargs...)
	ret0, _ := ret[0].(*GetUserSkillFreezeStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSkillFreezeStatus indicates an expected call of GetUserSkillFreezeStatus.
func (mr *MockEsportSkillClientMockRecorder) GetUserSkillFreezeStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSkillFreezeStatus", reflect.TypeOf((*MockEsportSkillClient)(nil).GetUserSkillFreezeStatus), varargs...)
}

// GetUserSkillStatus mocks base method.
func (m *MockEsportSkillClient) GetUserSkillStatus(ctx context.Context, in *GetUserSkillStatusRequest, opts ...grpc.CallOption) (*GetUserSkillStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserSkillStatus", varargs...)
	ret0, _ := ret[0].(*GetUserSkillStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSkillStatus indicates an expected call of GetUserSkillStatus.
func (mr *MockEsportSkillClientMockRecorder) GetUserSkillStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSkillStatus", reflect.TypeOf((*MockEsportSkillClient)(nil).GetUserSkillStatus), varargs...)
}

// HandleGameUpdate mocks base method.
func (m *MockEsportSkillClient) HandleGameUpdate(ctx context.Context, in *HandleGameUpdateRequest, opts ...grpc.CallOption) (*HandleGameUpdateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandleGameUpdate", varargs...)
	ret0, _ := ret[0].(*HandleGameUpdateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleGameUpdate indicates an expected call of HandleGameUpdate.
func (mr *MockEsportSkillClientMockRecorder) HandleGameUpdate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleGameUpdate", reflect.TypeOf((*MockEsportSkillClient)(nil).HandleGameUpdate), varargs...)
}

// IssueLabel mocks base method.
func (m *MockEsportSkillClient) IssueLabel(ctx context.Context, in *IssueLabelRequest, opts ...grpc.CallOption) (*IssueLabelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IssueLabel", varargs...)
	ret0, _ := ret[0].(*IssueLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IssueLabel indicates an expected call of IssueLabel.
func (mr *MockEsportSkillClientMockRecorder) IssueLabel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IssueLabel", reflect.TypeOf((*MockEsportSkillClient)(nil).IssueLabel), varargs...)
}

// ListLabels mocks base method.
func (m *MockEsportSkillClient) ListLabels(ctx context.Context, in *ListLabelsRequest, opts ...grpc.CallOption) (*ListLabelsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListLabels", varargs...)
	ret0, _ := ret[0].(*ListLabelsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListLabels indicates an expected call of ListLabels.
func (mr *MockEsportSkillClientMockRecorder) ListLabels(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListLabels", reflect.TypeOf((*MockEsportSkillClient)(nil).ListLabels), varargs...)
}

// ListRenownedPlayers mocks base method.
func (m *MockEsportSkillClient) ListRenownedPlayers(ctx context.Context, in *ListRenownedPlayersRequest, opts ...grpc.CallOption) (*ListRenownedPlayersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListRenownedPlayers", varargs...)
	ret0, _ := ret[0].(*ListRenownedPlayersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRenownedPlayers indicates an expected call of ListRenownedPlayers.
func (mr *MockEsportSkillClientMockRecorder) ListRenownedPlayers(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRenownedPlayers", reflect.TypeOf((*MockEsportSkillClient)(nil).ListRenownedPlayers), varargs...)
}

// ModifyUserSkill mocks base method.
func (m *MockEsportSkillClient) ModifyUserSkill(ctx context.Context, in *ModifyUserSkillRequest, opts ...grpc.CallOption) (*ModifyUserSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ModifyUserSkill", varargs...)
	ret0, _ := ret[0].(*ModifyUserSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyUserSkill indicates an expected call of ModifyUserSkill.
func (mr *MockEsportSkillClientMockRecorder) ModifyUserSkill(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyUserSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).ModifyUserSkill), varargs...)
}

// QueryIssuanceRecords mocks base method.
func (m *MockEsportSkillClient) QueryIssuanceRecords(ctx context.Context, in *QueryIssuanceRecordsRequest, opts ...grpc.CallOption) (*QueryIssuanceRecordsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryIssuanceRecords", varargs...)
	ret0, _ := ret[0].(*QueryIssuanceRecordsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryIssuanceRecords indicates an expected call of QueryIssuanceRecords.
func (mr *MockEsportSkillClientMockRecorder) QueryIssuanceRecords(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryIssuanceRecords", reflect.TypeOf((*MockEsportSkillClient)(nil).QueryIssuanceRecords), varargs...)
}

// RevokeLabel mocks base method.
func (m *MockEsportSkillClient) RevokeLabel(ctx context.Context, in *RevokeLabelRequest, opts ...grpc.CallOption) (*RevokeLabelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RevokeLabel", varargs...)
	ret0, _ := ret[0].(*RevokeLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RevokeLabel indicates an expected call of RevokeLabel.
func (mr *MockEsportSkillClientMockRecorder) RevokeLabel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RevokeLabel", reflect.TypeOf((*MockEsportSkillClient)(nil).RevokeLabel), varargs...)
}

// SetBasePriceSetting mocks base method.
func (m *MockEsportSkillClient) SetBasePriceSetting(ctx context.Context, in *SetBasePriceSettingRequest, opts ...grpc.CallOption) (*SetBasePriceSettingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetBasePriceSetting", varargs...)
	ret0, _ := ret[0].(*SetBasePriceSettingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetBasePriceSetting indicates an expected call of SetBasePriceSetting.
func (mr *MockEsportSkillClientMockRecorder) SetBasePriceSetting(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBasePriceSetting", reflect.TypeOf((*MockEsportSkillClient)(nil).SetBasePriceSetting), varargs...)
}

// SetGameGuaranteeStatus mocks base method.
func (m *MockEsportSkillClient) SetGameGuaranteeStatus(ctx context.Context, in *SetGameGuaranteeStatusRequest, opts ...grpc.CallOption) (*SetGameGuaranteeStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetGameGuaranteeStatus", varargs...)
	ret0, _ := ret[0].(*SetGameGuaranteeStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetGameGuaranteeStatus indicates an expected call of SetGameGuaranteeStatus.
func (mr *MockEsportSkillClientMockRecorder) SetGameGuaranteeStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGameGuaranteeStatus", reflect.TypeOf((*MockEsportSkillClient)(nil).SetGameGuaranteeStatus), varargs...)
}

// SetLabelPriceSwitch mocks base method.
func (m *MockEsportSkillClient) SetLabelPriceSwitch(ctx context.Context, in *SetLabelPriceSwitchRequest, opts ...grpc.CallOption) (*SetLabelPriceSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetLabelPriceSwitch", varargs...)
	ret0, _ := ret[0].(*SetLabelPriceSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetLabelPriceSwitch indicates an expected call of SetLabelPriceSwitch.
func (mr *MockEsportSkillClientMockRecorder) SetLabelPriceSwitch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLabelPriceSwitch", reflect.TypeOf((*MockEsportSkillClient)(nil).SetLabelPriceSwitch), varargs...)
}

// SetSwitch mocks base method.
func (m *MockEsportSkillClient) SetSwitch(ctx context.Context, in *SetSwitchRequest, opts ...grpc.CallOption) (*SetSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetSwitch", varargs...)
	ret0, _ := ret[0].(*SetSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetSwitch indicates an expected call of SetSwitch.
func (mr *MockEsportSkillClientMockRecorder) SetSwitch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSwitch", reflect.TypeOf((*MockEsportSkillClient)(nil).SetSwitch), varargs...)
}

// SetUserSkillAuditType mocks base method.
func (m *MockEsportSkillClient) SetUserSkillAuditType(ctx context.Context, in *SetUserSkillAuditTypeRequest, opts ...grpc.CallOption) (*SetUserSkillAuditTypeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserSkillAuditType", varargs...)
	ret0, _ := ret[0].(*SetUserSkillAuditTypeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserSkillAuditType indicates an expected call of SetUserSkillAuditType.
func (mr *MockEsportSkillClientMockRecorder) SetUserSkillAuditType(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSkillAuditType", reflect.TypeOf((*MockEsportSkillClient)(nil).SetUserSkillAuditType), varargs...)
}

// SetUserSkillRiskAuditType mocks base method.
func (m *MockEsportSkillClient) SetUserSkillRiskAuditType(ctx context.Context, in *SetUserSkillRiskAuditTypeRequest, opts ...grpc.CallOption) (*SetUserSkillRiskAuditTypeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserSkillRiskAuditType", varargs...)
	ret0, _ := ret[0].(*SetUserSkillRiskAuditTypeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserSkillRiskAuditType indicates an expected call of SetUserSkillRiskAuditType.
func (mr *MockEsportSkillClientMockRecorder) SetUserSkillRiskAuditType(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSkillRiskAuditType", reflect.TypeOf((*MockEsportSkillClient)(nil).SetUserSkillRiskAuditType), varargs...)
}

// TestAddUserSkill mocks base method.
func (m *MockEsportSkillClient) TestAddUserSkill(ctx context.Context, in *TestAddUserSkillRequest, opts ...grpc.CallOption) (*TestAddUserSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestAddUserSkill", varargs...)
	ret0, _ := ret[0].(*TestAddUserSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestAddUserSkill indicates an expected call of TestAddUserSkill.
func (mr *MockEsportSkillClientMockRecorder) TestAddUserSkill(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestAddUserSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).TestAddUserSkill), varargs...)
}

// UnfreezeCoachSkill mocks base method.
func (m *MockEsportSkillClient) UnfreezeCoachSkill(ctx context.Context, in *UnfreezeCoachSkillRequest, opts ...grpc.CallOption) (*UnfreezeCoachSkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UnfreezeCoachSkill", varargs...)
	ret0, _ := ret[0].(*UnfreezeCoachSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnfreezeCoachSkill indicates an expected call of UnfreezeCoachSkill.
func (mr *MockEsportSkillClientMockRecorder) UnfreezeCoachSkill(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnfreezeCoachSkill", reflect.TypeOf((*MockEsportSkillClient)(nil).UnfreezeCoachSkill), varargs...)
}

// UpdateEsportGameConfig mocks base method.
func (m *MockEsportSkillClient) UpdateEsportGameConfig(ctx context.Context, in *UpdateEsportGameConfigRequest, opts ...grpc.CallOption) (*UpdateEsportGameConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateEsportGameConfig", varargs...)
	ret0, _ := ret[0].(*UpdateEsportGameConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateEsportGameConfig indicates an expected call of UpdateEsportGameConfig.
func (mr *MockEsportSkillClientMockRecorder) UpdateEsportGameConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEsportGameConfig", reflect.TypeOf((*MockEsportSkillClient)(nil).UpdateEsportGameConfig), varargs...)
}

// UpdateRenownedPlayer mocks base method.
func (m *MockEsportSkillClient) UpdateRenownedPlayer(ctx context.Context, in *UpdateRenownedPlayerRequest, opts ...grpc.CallOption) (*UpdateRenownedPlayerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateRenownedPlayer", varargs...)
	ret0, _ := ret[0].(*UpdateRenownedPlayerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRenownedPlayer indicates an expected call of UpdateRenownedPlayer.
func (mr *MockEsportSkillClientMockRecorder) UpdateRenownedPlayer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRenownedPlayer", reflect.TypeOf((*MockEsportSkillClient)(nil).UpdateRenownedPlayer), varargs...)
}

// UpdateUserSpecialLabel mocks base method.
func (m *MockEsportSkillClient) UpdateUserSpecialLabel(ctx context.Context, in *UpdateUserSpecialLabelRequest, opts ...grpc.CallOption) (*UpdateUserSpecialLabelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateUserSpecialLabel", varargs...)
	ret0, _ := ret[0].(*UpdateUserSpecialLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserSpecialLabel indicates an expected call of UpdateUserSpecialLabel.
func (mr *MockEsportSkillClientMockRecorder) UpdateUserSpecialLabel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserSpecialLabel", reflect.TypeOf((*MockEsportSkillClient)(nil).UpdateUserSpecialLabel), varargs...)
}

// MockEsportSkillServer is a mock of EsportSkillServer interface.
type MockEsportSkillServer struct {
	ctrl     *gomock.Controller
	recorder *MockEsportSkillServerMockRecorder
}

// MockEsportSkillServerMockRecorder is the mock recorder for MockEsportSkillServer.
type MockEsportSkillServerMockRecorder struct {
	mock *MockEsportSkillServer
}

// NewMockEsportSkillServer creates a new mock instance.
func NewMockEsportSkillServer(ctrl *gomock.Controller) *MockEsportSkillServer {
	mock := &MockEsportSkillServer{ctrl: ctrl}
	mock.recorder = &MockEsportSkillServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEsportSkillServer) EXPECT() *MockEsportSkillServerMockRecorder {
	return m.recorder
}

// AddEsportGameConfig mocks base method.
func (m *MockEsportSkillServer) AddEsportGameConfig(ctx context.Context, in *AddEsportGameConfigRequest) (*AddEsportGameConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddEsportGameConfig", ctx, in)
	ret0, _ := ret[0].(*AddEsportGameConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddEsportGameConfig indicates an expected call of AddEsportGameConfig.
func (mr *MockEsportSkillServerMockRecorder) AddEsportGameConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddEsportGameConfig", reflect.TypeOf((*MockEsportSkillServer)(nil).AddEsportGameConfig), ctx, in)
}

// AddRenownedPlayer mocks base method.
func (m *MockEsportSkillServer) AddRenownedPlayer(ctx context.Context, in *AddRenownedPlayerRequest) (*AddRenownedPlayerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRenownedPlayer", ctx, in)
	ret0, _ := ret[0].(*AddRenownedPlayerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddRenownedPlayer indicates an expected call of AddRenownedPlayer.
func (mr *MockEsportSkillServerMockRecorder) AddRenownedPlayer(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRenownedPlayer", reflect.TypeOf((*MockEsportSkillServer)(nil).AddRenownedPlayer), ctx, in)
}

// AddUserAuditSkill mocks base method.
func (m *MockEsportSkillServer) AddUserAuditSkill(ctx context.Context, in *AddUserAuditSkillRequest) (*AddUserAuditSkillResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserAuditSkill", ctx, in)
	ret0, _ := ret[0].(*AddUserAuditSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddUserAuditSkill indicates an expected call of AddUserAuditSkill.
func (mr *MockEsportSkillServerMockRecorder) AddUserAuditSkill(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserAuditSkill", reflect.TypeOf((*MockEsportSkillServer)(nil).AddUserAuditSkill), ctx, in)
}

// BatGetUserSkillFreezeStatus mocks base method.
func (m *MockEsportSkillServer) BatGetUserSkillFreezeStatus(ctx context.Context, in *BatGetUserSkillFreezeStatusRequest) (*BatGetUserSkillFreezeStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetUserSkillFreezeStatus", ctx, in)
	ret0, _ := ret[0].(*BatGetUserSkillFreezeStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetUserSkillFreezeStatus indicates an expected call of BatGetUserSkillFreezeStatus.
func (mr *MockEsportSkillServerMockRecorder) BatGetUserSkillFreezeStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetUserSkillFreezeStatus", reflect.TypeOf((*MockEsportSkillServer)(nil).BatGetUserSkillFreezeStatus), ctx, in)
}

// BatchCheckCoachHasGame mocks base method.
func (m *MockEsportSkillServer) BatchCheckCoachHasGame(ctx context.Context, in *BatchCheckCoachHasGameRequest) (*BatchCheckCoachHasGameResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCheckCoachHasGame", ctx, in)
	ret0, _ := ret[0].(*BatchCheckCoachHasGameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckCoachHasGame indicates an expected call of BatchCheckCoachHasGame.
func (mr *MockEsportSkillServerMockRecorder) BatchCheckCoachHasGame(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckCoachHasGame", reflect.TypeOf((*MockEsportSkillServer)(nil).BatchCheckCoachHasGame), ctx, in)
}

// BatchGetAuditSkill mocks base method.
func (m *MockEsportSkillServer) BatchGetAuditSkill(ctx context.Context, in *BatchGetAuditSkillRequest) (*BatchGetAuditSkillResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAuditSkill", ctx, in)
	ret0, _ := ret[0].(*BatchGetAuditSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAuditSkill indicates an expected call of BatchGetAuditSkill.
func (mr *MockEsportSkillServerMockRecorder) BatchGetAuditSkill(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAuditSkill", reflect.TypeOf((*MockEsportSkillServer)(nil).BatchGetAuditSkill), ctx, in)
}

// BatchGetCoachLabelsForGame mocks base method.
func (m *MockEsportSkillServer) BatchGetCoachLabelsForGame(ctx context.Context, in *BatchGetCoachLabelsForGameRequest) (*BatchGetCoachLabelsForGameResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetCoachLabelsForGame", ctx, in)
	ret0, _ := ret[0].(*BatchGetCoachLabelsForGameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetCoachLabelsForGame indicates an expected call of BatchGetCoachLabelsForGame.
func (mr *MockEsportSkillServerMockRecorder) BatchGetCoachLabelsForGame(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetCoachLabelsForGame", reflect.TypeOf((*MockEsportSkillServer)(nil).BatchGetCoachLabelsForGame), ctx, in)
}

// BatchGetGameInfoByNames mocks base method.
func (m *MockEsportSkillServer) BatchGetGameInfoByNames(ctx context.Context, in *BatchGetGameInfoByNamesRequest) (*BatchGetGameInfoByNamesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetGameInfoByNames", ctx, in)
	ret0, _ := ret[0].(*BatchGetGameInfoByNamesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGameInfoByNames indicates an expected call of BatchGetGameInfoByNames.
func (mr *MockEsportSkillServerMockRecorder) BatchGetGameInfoByNames(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGameInfoByNames", reflect.TypeOf((*MockEsportSkillServer)(nil).BatchGetGameInfoByNames), ctx, in)
}

// BatchGetUserCurrentSkill mocks base method.
func (m *MockEsportSkillServer) BatchGetUserCurrentSkill(ctx context.Context, in *BatchGetUserCurrentSkillRequest) (*BatchGetUserCurrentSkillResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserCurrentSkill", ctx, in)
	ret0, _ := ret[0].(*BatchGetUserCurrentSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserCurrentSkill indicates an expected call of BatchGetUserCurrentSkill.
func (mr *MockEsportSkillServerMockRecorder) BatchGetUserCurrentSkill(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserCurrentSkill", reflect.TypeOf((*MockEsportSkillServer)(nil).BatchGetUserCurrentSkill), ctx, in)
}

// BatchGetUserGameSpecialLabel mocks base method.
func (m *MockEsportSkillServer) BatchGetUserGameSpecialLabel(ctx context.Context, in *BatchGetUserGameSpecialLabelRequest) (*BatchGetUserGameSpecialLabelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserGameSpecialLabel", ctx, in)
	ret0, _ := ret[0].(*BatchGetUserGameSpecialLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserGameSpecialLabel indicates an expected call of BatchGetUserGameSpecialLabel.
func (mr *MockEsportSkillServerMockRecorder) BatchGetUserGameSpecialLabel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserGameSpecialLabel", reflect.TypeOf((*MockEsportSkillServer)(nil).BatchGetUserGameSpecialLabel), ctx, in)
}

// BatchGetUserRenownedInfo mocks base method.
func (m *MockEsportSkillServer) BatchGetUserRenownedInfo(ctx context.Context, in *BatchGetUserRenownedInfoRequest) (*BatchGetUserRenownedInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserRenownedInfo", ctx, in)
	ret0, _ := ret[0].(*BatchGetUserRenownedInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserRenownedInfo indicates an expected call of BatchGetUserRenownedInfo.
func (mr *MockEsportSkillServerMockRecorder) BatchGetUserRenownedInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserRenownedInfo", reflect.TypeOf((*MockEsportSkillServer)(nil).BatchGetUserRenownedInfo), ctx, in)
}

// BatchGetUserSpecialLabel mocks base method.
func (m *MockEsportSkillServer) BatchGetUserSpecialLabel(ctx context.Context, in *BatchGetUserSpecialLabelRequest) (*BatchGetUserSpecialLabelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserSpecialLabel", ctx, in)
	ret0, _ := ret[0].(*BatchGetUserSpecialLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserSpecialLabel indicates an expected call of BatchGetUserSpecialLabel.
func (mr *MockEsportSkillServerMockRecorder) BatchGetUserSpecialLabel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserSpecialLabel", reflect.TypeOf((*MockEsportSkillServer)(nil).BatchGetUserSpecialLabel), ctx, in)
}

// BatchRemoveRenownedPlayers mocks base method.
func (m *MockEsportSkillServer) BatchRemoveRenownedPlayers(ctx context.Context, in *BatchRemoveRenownedPlayersRequest) (*BatchRemoveRenownedPlayersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchRemoveRenownedPlayers", ctx, in)
	ret0, _ := ret[0].(*BatchRemoveRenownedPlayersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchRemoveRenownedPlayers indicates an expected call of BatchRemoveRenownedPlayers.
func (mr *MockEsportSkillServerMockRecorder) BatchRemoveRenownedPlayers(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchRemoveRenownedPlayers", reflect.TypeOf((*MockEsportSkillServer)(nil).BatchRemoveRenownedPlayers), ctx, in)
}

// CalculatePrice mocks base method.
func (m *MockEsportSkillServer) CalculatePrice(ctx context.Context, in *CalculatePriceRequest) (*CalculatePriceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculatePrice", ctx, in)
	ret0, _ := ret[0].(*CalculatePriceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculatePrice indicates an expected call of CalculatePrice.
func (mr *MockEsportSkillServerMockRecorder) CalculatePrice(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculatePrice", reflect.TypeOf((*MockEsportSkillServer)(nil).CalculatePrice), ctx, in)
}

// CalculatePriceByGames mocks base method.
func (m *MockEsportSkillServer) CalculatePriceByGames(ctx context.Context, in *CalculatePriceByGamesRequest) (*CalculatePriceByGamesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculatePriceByGames", ctx, in)
	ret0, _ := ret[0].(*CalculatePriceByGamesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculatePriceByGames indicates an expected call of CalculatePriceByGames.
func (mr *MockEsportSkillServerMockRecorder) CalculatePriceByGames(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculatePriceByGames", reflect.TypeOf((*MockEsportSkillServer)(nil).CalculatePriceByGames), ctx, in)
}

// CheckCoachCouldOpenSwitch mocks base method.
func (m *MockEsportSkillServer) CheckCoachCouldOpenSwitch(ctx context.Context, in *CheckCoachCouldOpenSwitchRequest) (*CheckCoachCouldOpenSwitchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckCoachCouldOpenSwitch", ctx, in)
	ret0, _ := ret[0].(*CheckCoachCouldOpenSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckCoachCouldOpenSwitch indicates an expected call of CheckCoachCouldOpenSwitch.
func (mr *MockEsportSkillServerMockRecorder) CheckCoachCouldOpenSwitch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCoachCouldOpenSwitch", reflect.TypeOf((*MockEsportSkillServer)(nil).CheckCoachCouldOpenSwitch), ctx, in)
}

// CheckCoachIdentityAndSkill mocks base method.
func (m *MockEsportSkillServer) CheckCoachIdentityAndSkill(ctx context.Context, in *CheckCoachIdentityAndSkillRequest) (*CheckCoachIdentityAndSkillResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckCoachIdentityAndSkill", ctx, in)
	ret0, _ := ret[0].(*CheckCoachIdentityAndSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckCoachIdentityAndSkill indicates an expected call of CheckCoachIdentityAndSkill.
func (mr *MockEsportSkillServerMockRecorder) CheckCoachIdentityAndSkill(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCoachIdentityAndSkill", reflect.TypeOf((*MockEsportSkillServer)(nil).CheckCoachIdentityAndSkill), ctx, in)
}

// CheckLabelOrder mocks base method.
func (m *MockEsportSkillServer) CheckLabelOrder(ctx context.Context, in *CheckLabelOrderRequest) (*CheckLabelOrderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckLabelOrder", ctx, in)
	ret0, _ := ret[0].(*CheckLabelOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckLabelOrder indicates an expected call of CheckLabelOrder.
func (mr *MockEsportSkillServerMockRecorder) CheckLabelOrder(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckLabelOrder", reflect.TypeOf((*MockEsportSkillServer)(nil).CheckLabelOrder), ctx, in)
}

// CreateLabel mocks base method.
func (m *MockEsportSkillServer) CreateLabel(ctx context.Context, in *CreateLabelRequest) (*CreateLabelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLabel", ctx, in)
	ret0, _ := ret[0].(*CreateLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLabel indicates an expected call of CreateLabel.
func (mr *MockEsportSkillServerMockRecorder) CreateLabel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLabel", reflect.TypeOf((*MockEsportSkillServer)(nil).CreateLabel), ctx, in)
}

// DebugCheckGuaranteeWinPermission mocks base method.
func (m *MockEsportSkillServer) DebugCheckGuaranteeWinPermission(ctx context.Context, in *DebugCheckGuaranteeWinPermissionRequest) (*DebugCheckGuaranteeWinPermissionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DebugCheckGuaranteeWinPermission", ctx, in)
	ret0, _ := ret[0].(*DebugCheckGuaranteeWinPermissionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DebugCheckGuaranteeWinPermission indicates an expected call of DebugCheckGuaranteeWinPermission.
func (mr *MockEsportSkillServerMockRecorder) DebugCheckGuaranteeWinPermission(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DebugCheckGuaranteeWinPermission", reflect.TypeOf((*MockEsportSkillServer)(nil).DebugCheckGuaranteeWinPermission), ctx, in)
}

// DelUserSkill mocks base method.
func (m *MockEsportSkillServer) DelUserSkill(ctx context.Context, in *DelUserSkillRequest) (*DelUserSkillResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserSkill", ctx, in)
	ret0, _ := ret[0].(*DelUserSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelUserSkill indicates an expected call of DelUserSkill.
func (mr *MockEsportSkillServerMockRecorder) DelUserSkill(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserSkill", reflect.TypeOf((*MockEsportSkillServer)(nil).DelUserSkill), ctx, in)
}

// DeleteEsportGameConfig mocks base method.
func (m *MockEsportSkillServer) DeleteEsportGameConfig(ctx context.Context, in *DeleteEsportGameConfigRequest) (*DeleteEsportGameConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteEsportGameConfig", ctx, in)
	ret0, _ := ret[0].(*DeleteEsportGameConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteEsportGameConfig indicates an expected call of DeleteEsportGameConfig.
func (mr *MockEsportSkillServerMockRecorder) DeleteEsportGameConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEsportGameConfig", reflect.TypeOf((*MockEsportSkillServer)(nil).DeleteEsportGameConfig), ctx, in)
}

// DeleteLabel mocks base method.
func (m *MockEsportSkillServer) DeleteLabel(ctx context.Context, in *DeleteLabelRequest) (*DeleteLabelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteLabel", ctx, in)
	ret0, _ := ret[0].(*DeleteLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteLabel indicates an expected call of DeleteLabel.
func (mr *MockEsportSkillServerMockRecorder) DeleteLabel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLabel", reflect.TypeOf((*MockEsportSkillServer)(nil).DeleteLabel), ctx, in)
}

// EditLabel mocks base method.
func (m *MockEsportSkillServer) EditLabel(ctx context.Context, in *EditLabelRequest) (*EditLabelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EditLabel", ctx, in)
	ret0, _ := ret[0].(*EditLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EditLabel indicates an expected call of EditLabel.
func (mr *MockEsportSkillServerMockRecorder) EditLabel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EditLabel", reflect.TypeOf((*MockEsportSkillServer)(nil).EditLabel), ctx, in)
}

// FreezeCoachSkill mocks base method.
func (m *MockEsportSkillServer) FreezeCoachSkill(ctx context.Context, in *FreezeCoachSkillRequest) (*FreezeCoachSkillResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FreezeCoachSkill", ctx, in)
	ret0, _ := ret[0].(*FreezeCoachSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FreezeCoachSkill indicates an expected call of FreezeCoachSkill.
func (mr *MockEsportSkillServerMockRecorder) FreezeCoachSkill(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreezeCoachSkill", reflect.TypeOf((*MockEsportSkillServer)(nil).FreezeCoachSkill), ctx, in)
}

// GetAllGameCardConfig mocks base method.
func (m *MockEsportSkillServer) GetAllGameCardConfig(ctx context.Context, in *GetAllGameCardConfigRequest) (*GetAllGameCardConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllGameCardConfig", ctx, in)
	ret0, _ := ret[0].(*GetAllGameCardConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllGameCardConfig indicates an expected call of GetAllGameCardConfig.
func (mr *MockEsportSkillServerMockRecorder) GetAllGameCardConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllGameCardConfig", reflect.TypeOf((*MockEsportSkillServer)(nil).GetAllGameCardConfig), ctx, in)
}

// GetAllGameSimpleInfo mocks base method.
func (m *MockEsportSkillServer) GetAllGameSimpleInfo(ctx context.Context, in *GetAllGameSimpleInfoRequest) (*GetAllGameSimpleInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllGameSimpleInfo", ctx, in)
	ret0, _ := ret[0].(*GetAllGameSimpleInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllGameSimpleInfo indicates an expected call of GetAllGameSimpleInfo.
func (mr *MockEsportSkillServerMockRecorder) GetAllGameSimpleInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllGameSimpleInfo", reflect.TypeOf((*MockEsportSkillServer)(nil).GetAllGameSimpleInfo), ctx, in)
}

// GetBasePriceSetting mocks base method.
func (m *MockEsportSkillServer) GetBasePriceSetting(ctx context.Context, in *GetBasePriceSettingRequest) (*GetBasePriceSettingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBasePriceSetting", ctx, in)
	ret0, _ := ret[0].(*GetBasePriceSettingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBasePriceSetting indicates an expected call of GetBasePriceSetting.
func (mr *MockEsportSkillServerMockRecorder) GetBasePriceSetting(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBasePriceSetting", reflect.TypeOf((*MockEsportSkillServer)(nil).GetBasePriceSetting), ctx, in)
}

// GetCoachApplicableLabels mocks base method.
func (m *MockEsportSkillServer) GetCoachApplicableLabels(ctx context.Context, in *GetCoachApplicableLabelsRequest) (*GetCoachApplicableLabelsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCoachApplicableLabels", ctx, in)
	ret0, _ := ret[0].(*GetCoachApplicableLabelsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachApplicableLabels indicates an expected call of GetCoachApplicableLabels.
func (mr *MockEsportSkillServerMockRecorder) GetCoachApplicableLabels(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachApplicableLabels", reflect.TypeOf((*MockEsportSkillServer)(nil).GetCoachApplicableLabels), ctx, in)
}

// GetEsportGameConfigListByPage mocks base method.
func (m *MockEsportSkillServer) GetEsportGameConfigListByPage(ctx context.Context, in *GetEsportGameConfigListByPageRequest) (*GetEsportGameConfigListByPageResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEsportGameConfigListByPage", ctx, in)
	ret0, _ := ret[0].(*GetEsportGameConfigListByPageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEsportGameConfigListByPage indicates an expected call of GetEsportGameConfigListByPage.
func (mr *MockEsportSkillServerMockRecorder) GetEsportGameConfigListByPage(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEsportGameConfigListByPage", reflect.TypeOf((*MockEsportSkillServer)(nil).GetEsportGameConfigListByPage), ctx, in)
}

// GetGameDetailById mocks base method.
func (m *MockEsportSkillServer) GetGameDetailById(ctx context.Context, in *GetGameDetailByIdRequest) (*GetGameDetailByIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameDetailById", ctx, in)
	ret0, _ := ret[0].(*GetGameDetailByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameDetailById indicates an expected call of GetGameDetailById.
func (mr *MockEsportSkillServerMockRecorder) GetGameDetailById(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameDetailById", reflect.TypeOf((*MockEsportSkillServer)(nil).GetGameDetailById), ctx, in)
}

// GetGameDetailByIds mocks base method.
func (m *MockEsportSkillServer) GetGameDetailByIds(ctx context.Context, in *GetGameDetailByIdsRequest) (*GetGameDetailByIdsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameDetailByIds", ctx, in)
	ret0, _ := ret[0].(*GetGameDetailByIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameDetailByIds indicates an expected call of GetGameDetailByIds.
func (mr *MockEsportSkillServerMockRecorder) GetGameDetailByIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameDetailByIds", reflect.TypeOf((*MockEsportSkillServer)(nil).GetGameDetailByIds), ctx, in)
}

// GetGameList mocks base method.
func (m *MockEsportSkillServer) GetGameList(ctx context.Context, in *GetGameListRequest) (*GetGameListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGameList", ctx, in)
	ret0, _ := ret[0].(*GetGameListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameList indicates an expected call of GetGameList.
func (mr *MockEsportSkillServerMockRecorder) GetGameList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameList", reflect.TypeOf((*MockEsportSkillServer)(nil).GetGameList), ctx, in)
}

// GetLabelIssueRecord mocks base method.
func (m *MockEsportSkillServer) GetLabelIssueRecord(ctx context.Context, in *GetLabelIssuanceRecordRequest) (*GetLabelIssuanceRecordResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLabelIssueRecord", ctx, in)
	ret0, _ := ret[0].(*GetLabelIssuanceRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLabelIssueRecord indicates an expected call of GetLabelIssueRecord.
func (mr *MockEsportSkillServerMockRecorder) GetLabelIssueRecord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLabelIssueRecord", reflect.TypeOf((*MockEsportSkillServer)(nil).GetLabelIssueRecord), ctx, in)
}

// GetMinimumPrice mocks base method.
func (m *MockEsportSkillServer) GetMinimumPrice(ctx context.Context, in *GetMinimumPriceRequest) (*GetMinimumPriceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMinimumPrice", ctx, in)
	ret0, _ := ret[0].(*GetMinimumPriceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMinimumPrice indicates an expected call of GetMinimumPrice.
func (mr *MockEsportSkillServerMockRecorder) GetMinimumPrice(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinimumPrice", reflect.TypeOf((*MockEsportSkillServer)(nil).GetMinimumPrice), ctx, in)
}

// GetSkillFreezeOperationList mocks base method.
func (m *MockEsportSkillServer) GetSkillFreezeOperationList(ctx context.Context, in *GetSkillFreezeOperationListRequest) (*GetSkillFreezeOperationListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSkillFreezeOperationList", ctx, in)
	ret0, _ := ret[0].(*GetSkillFreezeOperationListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSkillFreezeOperationList indicates an expected call of GetSkillFreezeOperationList.
func (mr *MockEsportSkillServerMockRecorder) GetSkillFreezeOperationList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSkillFreezeOperationList", reflect.TypeOf((*MockEsportSkillServer)(nil).GetSkillFreezeOperationList), ctx, in)
}

// GetSpecialIssueRecord mocks base method.
func (m *MockEsportSkillServer) GetSpecialIssueRecord(ctx context.Context, in *GetSpecialLabelIssuanceRecordRequest) (*GetSpecialLabelIssuanceRecordResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSpecialIssueRecord", ctx, in)
	ret0, _ := ret[0].(*GetSpecialLabelIssuanceRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSpecialIssueRecord indicates an expected call of GetSpecialIssueRecord.
func (mr *MockEsportSkillServerMockRecorder) GetSpecialIssueRecord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSpecialIssueRecord", reflect.TypeOf((*MockEsportSkillServer)(nil).GetSpecialIssueRecord), ctx, in)
}

// GetSpecialLabelList mocks base method.
func (m *MockEsportSkillServer) GetSpecialLabelList(ctx context.Context, in *GetSpecialLabelListRequest) (*GetSpecialLabelListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSpecialLabelList", ctx, in)
	ret0, _ := ret[0].(*GetSpecialLabelListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSpecialLabelList indicates an expected call of GetSpecialLabelList.
func (mr *MockEsportSkillServerMockRecorder) GetSpecialLabelList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSpecialLabelList", reflect.TypeOf((*MockEsportSkillServer)(nil).GetSpecialLabelList), ctx, in)
}

// GetSwitch mocks base method.
func (m *MockEsportSkillServer) GetSwitch(ctx context.Context, in *GetSwitchRequest) (*GetSwitchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSwitch", ctx, in)
	ret0, _ := ret[0].(*GetSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSwitch indicates an expected call of GetSwitch.
func (mr *MockEsportSkillServerMockRecorder) GetSwitch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSwitch", reflect.TypeOf((*MockEsportSkillServer)(nil).GetSwitch), ctx, in)
}

// GetTopGameList mocks base method.
func (m *MockEsportSkillServer) GetTopGameList(ctx context.Context, in *GetTopGameListRequest) (*GetTopGameListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTopGameList", ctx, in)
	ret0, _ := ret[0].(*GetTopGameListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTopGameList indicates an expected call of GetTopGameList.
func (mr *MockEsportSkillServerMockRecorder) GetTopGameList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTopGameList", reflect.TypeOf((*MockEsportSkillServer)(nil).GetTopGameList), ctx, in)
}

// GetUserAuditSkill mocks base method.
func (m *MockEsportSkillServer) GetUserAuditSkill(ctx context.Context, in *GetUserAuditSkillRequest) (*GetUserAuditSkillResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAuditSkill", ctx, in)
	ret0, _ := ret[0].(*GetUserAuditSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAuditSkill indicates an expected call of GetUserAuditSkill.
func (mr *MockEsportSkillServerMockRecorder) GetUserAuditSkill(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAuditSkill", reflect.TypeOf((*MockEsportSkillServer)(nil).GetUserAuditSkill), ctx, in)
}

// GetUserCurrentSkill mocks base method.
func (m *MockEsportSkillServer) GetUserCurrentSkill(ctx context.Context, in *GetUserCurrentSkillRequest) (*GetUserCurrentSkillResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserCurrentSkill", ctx, in)
	ret0, _ := ret[0].(*GetUserCurrentSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserCurrentSkill indicates an expected call of GetUserCurrentSkill.
func (mr *MockEsportSkillServerMockRecorder) GetUserCurrentSkill(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserCurrentSkill", reflect.TypeOf((*MockEsportSkillServer)(nil).GetUserCurrentSkill), ctx, in)
}

// GetUserSkillByGameId mocks base method.
func (m *MockEsportSkillServer) GetUserSkillByGameId(ctx context.Context, in *GetUserSkillByGameIdRequest) (*GetUserSkillByGameIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSkillByGameId", ctx, in)
	ret0, _ := ret[0].(*GetUserSkillByGameIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSkillByGameId indicates an expected call of GetUserSkillByGameId.
func (mr *MockEsportSkillServerMockRecorder) GetUserSkillByGameId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSkillByGameId", reflect.TypeOf((*MockEsportSkillServer)(nil).GetUserSkillByGameId), ctx, in)
}

// GetUserSkillFreezeStatus mocks base method.
func (m *MockEsportSkillServer) GetUserSkillFreezeStatus(ctx context.Context, in *GetUserSkillFreezeStatusRequest) (*GetUserSkillFreezeStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSkillFreezeStatus", ctx, in)
	ret0, _ := ret[0].(*GetUserSkillFreezeStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSkillFreezeStatus indicates an expected call of GetUserSkillFreezeStatus.
func (mr *MockEsportSkillServerMockRecorder) GetUserSkillFreezeStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSkillFreezeStatus", reflect.TypeOf((*MockEsportSkillServer)(nil).GetUserSkillFreezeStatus), ctx, in)
}

// GetUserSkillStatus mocks base method.
func (m *MockEsportSkillServer) GetUserSkillStatus(ctx context.Context, in *GetUserSkillStatusRequest) (*GetUserSkillStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSkillStatus", ctx, in)
	ret0, _ := ret[0].(*GetUserSkillStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSkillStatus indicates an expected call of GetUserSkillStatus.
func (mr *MockEsportSkillServerMockRecorder) GetUserSkillStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSkillStatus", reflect.TypeOf((*MockEsportSkillServer)(nil).GetUserSkillStatus), ctx, in)
}

// HandleGameUpdate mocks base method.
func (m *MockEsportSkillServer) HandleGameUpdate(ctx context.Context, in *HandleGameUpdateRequest) (*HandleGameUpdateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleGameUpdate", ctx, in)
	ret0, _ := ret[0].(*HandleGameUpdateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleGameUpdate indicates an expected call of HandleGameUpdate.
func (mr *MockEsportSkillServerMockRecorder) HandleGameUpdate(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleGameUpdate", reflect.TypeOf((*MockEsportSkillServer)(nil).HandleGameUpdate), ctx, in)
}

// IssueLabel mocks base method.
func (m *MockEsportSkillServer) IssueLabel(ctx context.Context, in *IssueLabelRequest) (*IssueLabelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IssueLabel", ctx, in)
	ret0, _ := ret[0].(*IssueLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IssueLabel indicates an expected call of IssueLabel.
func (mr *MockEsportSkillServerMockRecorder) IssueLabel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IssueLabel", reflect.TypeOf((*MockEsportSkillServer)(nil).IssueLabel), ctx, in)
}

// ListLabels mocks base method.
func (m *MockEsportSkillServer) ListLabels(ctx context.Context, in *ListLabelsRequest) (*ListLabelsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListLabels", ctx, in)
	ret0, _ := ret[0].(*ListLabelsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListLabels indicates an expected call of ListLabels.
func (mr *MockEsportSkillServerMockRecorder) ListLabels(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListLabels", reflect.TypeOf((*MockEsportSkillServer)(nil).ListLabels), ctx, in)
}

// ListRenownedPlayers mocks base method.
func (m *MockEsportSkillServer) ListRenownedPlayers(ctx context.Context, in *ListRenownedPlayersRequest) (*ListRenownedPlayersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRenownedPlayers", ctx, in)
	ret0, _ := ret[0].(*ListRenownedPlayersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRenownedPlayers indicates an expected call of ListRenownedPlayers.
func (mr *MockEsportSkillServerMockRecorder) ListRenownedPlayers(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRenownedPlayers", reflect.TypeOf((*MockEsportSkillServer)(nil).ListRenownedPlayers), ctx, in)
}

// ModifyUserSkill mocks base method.
func (m *MockEsportSkillServer) ModifyUserSkill(ctx context.Context, in *ModifyUserSkillRequest) (*ModifyUserSkillResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyUserSkill", ctx, in)
	ret0, _ := ret[0].(*ModifyUserSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyUserSkill indicates an expected call of ModifyUserSkill.
func (mr *MockEsportSkillServerMockRecorder) ModifyUserSkill(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyUserSkill", reflect.TypeOf((*MockEsportSkillServer)(nil).ModifyUserSkill), ctx, in)
}

// QueryIssuanceRecords mocks base method.
func (m *MockEsportSkillServer) QueryIssuanceRecords(ctx context.Context, in *QueryIssuanceRecordsRequest) (*QueryIssuanceRecordsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryIssuanceRecords", ctx, in)
	ret0, _ := ret[0].(*QueryIssuanceRecordsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryIssuanceRecords indicates an expected call of QueryIssuanceRecords.
func (mr *MockEsportSkillServerMockRecorder) QueryIssuanceRecords(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryIssuanceRecords", reflect.TypeOf((*MockEsportSkillServer)(nil).QueryIssuanceRecords), ctx, in)
}

// RevokeLabel mocks base method.
func (m *MockEsportSkillServer) RevokeLabel(ctx context.Context, in *RevokeLabelRequest) (*RevokeLabelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RevokeLabel", ctx, in)
	ret0, _ := ret[0].(*RevokeLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RevokeLabel indicates an expected call of RevokeLabel.
func (mr *MockEsportSkillServerMockRecorder) RevokeLabel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RevokeLabel", reflect.TypeOf((*MockEsportSkillServer)(nil).RevokeLabel), ctx, in)
}

// SetBasePriceSetting mocks base method.
func (m *MockEsportSkillServer) SetBasePriceSetting(ctx context.Context, in *SetBasePriceSettingRequest) (*SetBasePriceSettingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetBasePriceSetting", ctx, in)
	ret0, _ := ret[0].(*SetBasePriceSettingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetBasePriceSetting indicates an expected call of SetBasePriceSetting.
func (mr *MockEsportSkillServerMockRecorder) SetBasePriceSetting(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBasePriceSetting", reflect.TypeOf((*MockEsportSkillServer)(nil).SetBasePriceSetting), ctx, in)
}

// SetGameGuaranteeStatus mocks base method.
func (m *MockEsportSkillServer) SetGameGuaranteeStatus(ctx context.Context, in *SetGameGuaranteeStatusRequest) (*SetGameGuaranteeStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGameGuaranteeStatus", ctx, in)
	ret0, _ := ret[0].(*SetGameGuaranteeStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetGameGuaranteeStatus indicates an expected call of SetGameGuaranteeStatus.
func (mr *MockEsportSkillServerMockRecorder) SetGameGuaranteeStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGameGuaranteeStatus", reflect.TypeOf((*MockEsportSkillServer)(nil).SetGameGuaranteeStatus), ctx, in)
}

// SetLabelPriceSwitch mocks base method.
func (m *MockEsportSkillServer) SetLabelPriceSwitch(ctx context.Context, in *SetLabelPriceSwitchRequest) (*SetLabelPriceSwitchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetLabelPriceSwitch", ctx, in)
	ret0, _ := ret[0].(*SetLabelPriceSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetLabelPriceSwitch indicates an expected call of SetLabelPriceSwitch.
func (mr *MockEsportSkillServerMockRecorder) SetLabelPriceSwitch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLabelPriceSwitch", reflect.TypeOf((*MockEsportSkillServer)(nil).SetLabelPriceSwitch), ctx, in)
}

// SetSwitch mocks base method.
func (m *MockEsportSkillServer) SetSwitch(ctx context.Context, in *SetSwitchRequest) (*SetSwitchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetSwitch", ctx, in)
	ret0, _ := ret[0].(*SetSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetSwitch indicates an expected call of SetSwitch.
func (mr *MockEsportSkillServerMockRecorder) SetSwitch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSwitch", reflect.TypeOf((*MockEsportSkillServer)(nil).SetSwitch), ctx, in)
}

// SetUserSkillAuditType mocks base method.
func (m *MockEsportSkillServer) SetUserSkillAuditType(ctx context.Context, in *SetUserSkillAuditTypeRequest) (*SetUserSkillAuditTypeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserSkillAuditType", ctx, in)
	ret0, _ := ret[0].(*SetUserSkillAuditTypeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserSkillAuditType indicates an expected call of SetUserSkillAuditType.
func (mr *MockEsportSkillServerMockRecorder) SetUserSkillAuditType(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSkillAuditType", reflect.TypeOf((*MockEsportSkillServer)(nil).SetUserSkillAuditType), ctx, in)
}

// SetUserSkillRiskAuditType mocks base method.
func (m *MockEsportSkillServer) SetUserSkillRiskAuditType(ctx context.Context, in *SetUserSkillRiskAuditTypeRequest) (*SetUserSkillRiskAuditTypeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserSkillRiskAuditType", ctx, in)
	ret0, _ := ret[0].(*SetUserSkillRiskAuditTypeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserSkillRiskAuditType indicates an expected call of SetUserSkillRiskAuditType.
func (mr *MockEsportSkillServerMockRecorder) SetUserSkillRiskAuditType(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSkillRiskAuditType", reflect.TypeOf((*MockEsportSkillServer)(nil).SetUserSkillRiskAuditType), ctx, in)
}

// TestAddUserSkill mocks base method.
func (m *MockEsportSkillServer) TestAddUserSkill(ctx context.Context, in *TestAddUserSkillRequest) (*TestAddUserSkillResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestAddUserSkill", ctx, in)
	ret0, _ := ret[0].(*TestAddUserSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestAddUserSkill indicates an expected call of TestAddUserSkill.
func (mr *MockEsportSkillServerMockRecorder) TestAddUserSkill(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestAddUserSkill", reflect.TypeOf((*MockEsportSkillServer)(nil).TestAddUserSkill), ctx, in)
}

// UnfreezeCoachSkill mocks base method.
func (m *MockEsportSkillServer) UnfreezeCoachSkill(ctx context.Context, in *UnfreezeCoachSkillRequest) (*UnfreezeCoachSkillResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnfreezeCoachSkill", ctx, in)
	ret0, _ := ret[0].(*UnfreezeCoachSkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnfreezeCoachSkill indicates an expected call of UnfreezeCoachSkill.
func (mr *MockEsportSkillServerMockRecorder) UnfreezeCoachSkill(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnfreezeCoachSkill", reflect.TypeOf((*MockEsportSkillServer)(nil).UnfreezeCoachSkill), ctx, in)
}

// UpdateEsportGameConfig mocks base method.
func (m *MockEsportSkillServer) UpdateEsportGameConfig(ctx context.Context, in *UpdateEsportGameConfigRequest) (*UpdateEsportGameConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEsportGameConfig", ctx, in)
	ret0, _ := ret[0].(*UpdateEsportGameConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateEsportGameConfig indicates an expected call of UpdateEsportGameConfig.
func (mr *MockEsportSkillServerMockRecorder) UpdateEsportGameConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEsportGameConfig", reflect.TypeOf((*MockEsportSkillServer)(nil).UpdateEsportGameConfig), ctx, in)
}

// UpdateRenownedPlayer mocks base method.
func (m *MockEsportSkillServer) UpdateRenownedPlayer(ctx context.Context, in *UpdateRenownedPlayerRequest) (*UpdateRenownedPlayerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRenownedPlayer", ctx, in)
	ret0, _ := ret[0].(*UpdateRenownedPlayerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRenownedPlayer indicates an expected call of UpdateRenownedPlayer.
func (mr *MockEsportSkillServerMockRecorder) UpdateRenownedPlayer(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRenownedPlayer", reflect.TypeOf((*MockEsportSkillServer)(nil).UpdateRenownedPlayer), ctx, in)
}

// UpdateUserSpecialLabel mocks base method.
func (m *MockEsportSkillServer) UpdateUserSpecialLabel(ctx context.Context, in *UpdateUserSpecialLabelRequest) (*UpdateUserSpecialLabelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserSpecialLabel", ctx, in)
	ret0, _ := ret[0].(*UpdateUserSpecialLabelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserSpecialLabel indicates an expected call of UpdateUserSpecialLabel.
func (mr *MockEsportSkillServerMockRecorder) UpdateUserSpecialLabel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserSpecialLabel", reflect.TypeOf((*MockEsportSkillServer)(nil).UpdateUserSpecialLabel), ctx, in)
}
