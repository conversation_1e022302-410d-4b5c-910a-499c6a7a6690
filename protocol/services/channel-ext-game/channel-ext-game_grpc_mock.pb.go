// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/channel-ext-game/channel-ext-game.proto

package channel_ext_game

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	grpc "google.golang.org/grpc"
)

// MockChannelExtGameClient is a mock of ChannelExtGameClient interface.
type MockChannelExtGameClient struct {
	ctrl     *gomock.Controller
	recorder *MockChannelExtGameClientMockRecorder
}

// MockChannelExtGameClientMockRecorder is the mock recorder for MockChannelExtGameClient.
type MockChannelExtGameClientMockRecorder struct {
	mock *MockChannelExtGameClient
}

// NewMockChannelExtGameClient creates a new mock instance.
func NewMockChannelExtGameClient(ctrl *gomock.Controller) *MockChannelExtGameClient {
	mock := &MockChannelExtGameClient{ctrl: ctrl}
	mock.recorder = &MockChannelExtGameClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelExtGameClient) EXPECT() *MockChannelExtGameClientMockRecorder {
	return m.recorder
}

// AddWhiteList mocks base method.
func (m *MockChannelExtGameClient) AddWhiteList(ctx context.Context, in *AddWhiteListReq, opts ...grpc.CallOption) (*AddWhiteListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddWhiteList", varargs...)
	ret0, _ := ret[0].(*AddWhiteListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddWhiteList indicates an expected call of AddWhiteList.
func (mr *MockChannelExtGameClientMockRecorder) AddWhiteList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddWhiteList", reflect.TypeOf((*MockChannelExtGameClient)(nil).AddWhiteList), varargs...)
}

// BatchGetUidByOpenIds mocks base method.
func (m *MockChannelExtGameClient) BatchGetUidByOpenIds(ctx context.Context, in *BatchGetUidByOpenIdsReq, opts ...grpc.CallOption) (*BatchGetUidByOpenIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetUidByOpenIds", varargs...)
	ret0, _ := ret[0].(*BatchGetUidByOpenIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUidByOpenIds indicates an expected call of BatchGetUidByOpenIds.
func (mr *MockChannelExtGameClientMockRecorder) BatchGetUidByOpenIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUidByOpenIds", reflect.TypeOf((*MockChannelExtGameClient)(nil).BatchGetUidByOpenIds), varargs...)
}

// CancelUserExtGameJsCode mocks base method.
func (m *MockChannelExtGameClient) CancelUserExtGameJsCode(ctx context.Context, in *CancelUserExtGameJsCodeReq, opts ...grpc.CallOption) (*CancelUserExtGameJsCodeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CancelUserExtGameJsCode", varargs...)
	ret0, _ := ret[0].(*CancelUserExtGameJsCodeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelUserExtGameJsCode indicates an expected call of CancelUserExtGameJsCode.
func (mr *MockChannelExtGameClientMockRecorder) CancelUserExtGameJsCode(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelUserExtGameJsCode", reflect.TypeOf((*MockChannelExtGameClient)(nil).CancelUserExtGameJsCode), varargs...)
}

// CheckChannelBlackList mocks base method.
func (m *MockChannelExtGameClient) CheckChannelBlackList(ctx context.Context, in *CheckChannelBlackListReq, opts ...grpc.CallOption) (*CheckChannelBlackListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckChannelBlackList", varargs...)
	ret0, _ := ret[0].(*CheckChannelBlackListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckChannelBlackList indicates an expected call of CheckChannelBlackList.
func (mr *MockChannelExtGameClientMockRecorder) CheckChannelBlackList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckChannelBlackList", reflect.TypeOf((*MockChannelExtGameClient)(nil).CheckChannelBlackList), varargs...)
}

// CheckUserGameAccess mocks base method.
func (m *MockChannelExtGameClient) CheckUserGameAccess(ctx context.Context, in *CheckUserGameAccessReq, opts ...grpc.CallOption) (*CheckUserGameAccessResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckUserGameAccess", varargs...)
	ret0, _ := ret[0].(*CheckUserGameAccessResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserGameAccess indicates an expected call of CheckUserGameAccess.
func (mr *MockChannelExtGameClientMockRecorder) CheckUserGameAccess(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserGameAccess", reflect.TypeOf((*MockChannelExtGameClient)(nil).CheckUserGameAccess), varargs...)
}

// CheckWhiteList mocks base method.
func (m *MockChannelExtGameClient) CheckWhiteList(ctx context.Context, in *CheckWhiteListReq, opts ...grpc.CallOption) (*CheckWhiteListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckWhiteList", varargs...)
	ret0, _ := ret[0].(*CheckWhiteListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckWhiteList indicates an expected call of CheckWhiteList.
func (mr *MockChannelExtGameClientMockRecorder) CheckWhiteList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckWhiteList", reflect.TypeOf((*MockChannelExtGameClient)(nil).CheckWhiteList), varargs...)
}

// DailyConsumeTotalCntPush mocks base method.
func (m *MockChannelExtGameClient) DailyConsumeTotalCntPush(ctx context.Context, in *DailyConsumeTotalCntPushReq, opts ...grpc.CallOption) (*DailyConsumeTotalCntPushResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DailyConsumeTotalCntPush", varargs...)
	ret0, _ := ret[0].(*DailyConsumeTotalCntPushResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DailyConsumeTotalCntPush indicates an expected call of DailyConsumeTotalCntPush.
func (mr *MockChannelExtGameClientMockRecorder) DailyConsumeTotalCntPush(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DailyConsumeTotalCntPush", reflect.TypeOf((*MockChannelExtGameClient)(nil).DailyConsumeTotalCntPush), varargs...)
}

// ExtGameAward mocks base method.
func (m *MockChannelExtGameClient) ExtGameAward(ctx context.Context, in *ExtGameAwardReq, opts ...grpc.CallOption) (*ExtGameAwardResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ExtGameAward", varargs...)
	ret0, _ := ret[0].(*ExtGameAwardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExtGameAward indicates an expected call of ExtGameAward.
func (mr *MockChannelExtGameClientMockRecorder) ExtGameAward(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtGameAward", reflect.TypeOf((*MockChannelExtGameClient)(nil).ExtGameAward), varargs...)
}

// ExtGameConsume mocks base method.
func (m *MockChannelExtGameClient) ExtGameConsume(ctx context.Context, in *ExtGameConsumeReq, opts ...grpc.CallOption) (*ExtGameConsumeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ExtGameConsume", varargs...)
	ret0, _ := ret[0].(*ExtGameConsumeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExtGameConsume indicates an expected call of ExtGameConsume.
func (mr *MockChannelExtGameClientMockRecorder) ExtGameConsume(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtGameConsume", reflect.TypeOf((*MockChannelExtGameClient)(nil).ExtGameConsume), varargs...)
}

// GetAuthInfoByJsCode mocks base method.
func (m *MockChannelExtGameClient) GetAuthInfoByJsCode(ctx context.Context, in *GetAuthInfoByJsCodeReq, opts ...grpc.CallOption) (*GetAuthInfoByJsCodeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAuthInfoByJsCode", varargs...)
	ret0, _ := ret[0].(*GetAuthInfoByJsCodeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuthInfoByJsCode indicates an expected call of GetAuthInfoByJsCode.
func (mr *MockChannelExtGameClientMockRecorder) GetAuthInfoByJsCode(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthInfoByJsCode", reflect.TypeOf((*MockChannelExtGameClient)(nil).GetAuthInfoByJsCode), varargs...)
}

// GetAwardOrderIds mocks base method.
func (m *MockChannelExtGameClient) GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAwardOrderIds", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardOrderIds indicates an expected call of GetAwardOrderIds.
func (mr *MockChannelExtGameClientMockRecorder) GetAwardOrderIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardOrderIds", reflect.TypeOf((*MockChannelExtGameClient)(nil).GetAwardOrderIds), varargs...)
}

// GetAwardTotalCount mocks base method.
func (m *MockChannelExtGameClient) GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAwardTotalCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardTotalCount indicates an expected call of GetAwardTotalCount.
func (mr *MockChannelExtGameClientMockRecorder) GetAwardTotalCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardTotalCount", reflect.TypeOf((*MockChannelExtGameClient)(nil).GetAwardTotalCount), varargs...)
}

// GetConsumeOrderIds mocks base method.
func (m *MockChannelExtGameClient) GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetConsumeOrderIds", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeOrderIds indicates an expected call of GetConsumeOrderIds.
func (mr *MockChannelExtGameClientMockRecorder) GetConsumeOrderIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeOrderIds", reflect.TypeOf((*MockChannelExtGameClient)(nil).GetConsumeOrderIds), varargs...)
}

// GetConsumeTotalCount mocks base method.
func (m *MockChannelExtGameClient) GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetConsumeTotalCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeTotalCount indicates an expected call of GetConsumeTotalCount.
func (mr *MockChannelExtGameClientMockRecorder) GetConsumeTotalCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeTotalCount", reflect.TypeOf((*MockChannelExtGameClient)(nil).GetConsumeTotalCount), varargs...)
}

// GetExtGameInfoList mocks base method.
func (m *MockChannelExtGameClient) GetExtGameInfoList(ctx context.Context, in *GetExtGameInfoListReq, opts ...grpc.CallOption) (*GetExtGameInfoListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExtGameInfoList", varargs...)
	ret0, _ := ret[0].(*GetExtGameInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExtGameInfoList indicates an expected call of GetExtGameInfoList.
func (mr *MockChannelExtGameClientMockRecorder) GetExtGameInfoList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExtGameInfoList", reflect.TypeOf((*MockChannelExtGameClient)(nil).GetExtGameInfoList), varargs...)
}

// GetUidByOpenid mocks base method.
func (m *MockChannelExtGameClient) GetUidByOpenid(ctx context.Context, in *GetUidByOpenidReq, opts ...grpc.CallOption) (*GetUidByOpenidResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUidByOpenid", varargs...)
	ret0, _ := ret[0].(*GetUidByOpenidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUidByOpenid indicates an expected call of GetUidByOpenid.
func (mr *MockChannelExtGameClientMockRecorder) GetUidByOpenid(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUidByOpenid", reflect.TypeOf((*MockChannelExtGameClient)(nil).GetUidByOpenid), varargs...)
}

// GetUserExtGameJsCode mocks base method.
func (m *MockChannelExtGameClient) GetUserExtGameJsCode(ctx context.Context, in *GetUserExtGameJsCodeReq, opts ...grpc.CallOption) (*GetUserExtGameJsCodeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserExtGameJsCode", varargs...)
	ret0, _ := ret[0].(*GetUserExtGameJsCodeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserExtGameJsCode indicates an expected call of GetUserExtGameJsCode.
func (mr *MockChannelExtGameClientMockRecorder) GetUserExtGameJsCode(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExtGameJsCode", reflect.TypeOf((*MockChannelExtGameClient)(nil).GetUserExtGameJsCode), varargs...)
}

// GetUserExtGameOpenid mocks base method.
func (m *MockChannelExtGameClient) GetUserExtGameOpenid(ctx context.Context, in *GetUserExtGameOpenidReq, opts ...grpc.CallOption) (*GetUserExtGameOpenidResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserExtGameOpenid", varargs...)
	ret0, _ := ret[0].(*GetUserExtGameOpenidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserExtGameOpenid indicates an expected call of GetUserExtGameOpenid.
func (mr *MockChannelExtGameClientMockRecorder) GetUserExtGameOpenid(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExtGameOpenid", reflect.TypeOf((*MockChannelExtGameClient)(nil).GetUserExtGameOpenid), varargs...)
}

// GetWhiteChannelRandomly mocks base method.
func (m *MockChannelExtGameClient) GetWhiteChannelRandomly(ctx context.Context, in *GetWhiteChannelRandomlyReq, opts ...grpc.CallOption) (*GetWhiteChannelRandomlyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWhiteChannelRandomly", varargs...)
	ret0, _ := ret[0].(*GetWhiteChannelRandomlyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWhiteChannelRandomly indicates an expected call of GetWhiteChannelRandomly.
func (mr *MockChannelExtGameClientMockRecorder) GetWhiteChannelRandomly(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWhiteChannelRandomly", reflect.TypeOf((*MockChannelExtGameClient)(nil).GetWhiteChannelRandomly), varargs...)
}

// RemoveWhiteList mocks base method.
func (m *MockChannelExtGameClient) RemoveWhiteList(ctx context.Context, in *RemoveWhiteListReq, opts ...grpc.CallOption) (*RemoveWhiteListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RemoveWhiteList", varargs...)
	ret0, _ := ret[0].(*RemoveWhiteListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveWhiteList indicates an expected call of RemoveWhiteList.
func (mr *MockChannelExtGameClientMockRecorder) RemoveWhiteList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveWhiteList", reflect.TypeOf((*MockChannelExtGameClient)(nil).RemoveWhiteList), varargs...)
}

// SendPlatformMsg mocks base method.
func (m *MockChannelExtGameClient) SendPlatformMsg(ctx context.Context, in *SendPlatformMsgReq, opts ...grpc.CallOption) (*SendPlatformMsgResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendPlatformMsg", varargs...)
	ret0, _ := ret[0].(*SendPlatformMsgResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendPlatformMsg indicates an expected call of SendPlatformMsg.
func (mr *MockChannelExtGameClientMockRecorder) SendPlatformMsg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPlatformMsg", reflect.TypeOf((*MockChannelExtGameClient)(nil).SendPlatformMsg), varargs...)
}

// SimpleQueryOpenid mocks base method.
func (m *MockChannelExtGameClient) SimpleQueryOpenid(ctx context.Context, in *SimpleQueryOpenidReq, opts ...grpc.CallOption) (*SimpleQueryOpenidResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SimpleQueryOpenid", varargs...)
	ret0, _ := ret[0].(*SimpleQueryOpenidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SimpleQueryOpenid indicates an expected call of SimpleQueryOpenid.
func (mr *MockChannelExtGameClientMockRecorder) SimpleQueryOpenid(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimpleQueryOpenid", reflect.TypeOf((*MockChannelExtGameClient)(nil).SimpleQueryOpenid), varargs...)
}

// TestChannelCommonHighLightIm mocks base method.
func (m *MockChannelExtGameClient) TestChannelCommonHighLightIm(ctx context.Context, in *TestChannelCommonHighLightImReq, opts ...grpc.CallOption) (*TestChannelCommonHighLightImResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestChannelCommonHighLightIm", varargs...)
	ret0, _ := ret[0].(*TestChannelCommonHighLightImResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestChannelCommonHighLightIm indicates an expected call of TestChannelCommonHighLightIm.
func (mr *MockChannelExtGameClientMockRecorder) TestChannelCommonHighLightIm(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestChannelCommonHighLightIm", reflect.TypeOf((*MockChannelExtGameClient)(nil).TestChannelCommonHighLightIm), varargs...)
}

// MockChannelExtGameServer is a mock of ChannelExtGameServer interface.
type MockChannelExtGameServer struct {
	ctrl     *gomock.Controller
	recorder *MockChannelExtGameServerMockRecorder
}

// MockChannelExtGameServerMockRecorder is the mock recorder for MockChannelExtGameServer.
type MockChannelExtGameServerMockRecorder struct {
	mock *MockChannelExtGameServer
}

// NewMockChannelExtGameServer creates a new mock instance.
func NewMockChannelExtGameServer(ctrl *gomock.Controller) *MockChannelExtGameServer {
	mock := &MockChannelExtGameServer{ctrl: ctrl}
	mock.recorder = &MockChannelExtGameServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelExtGameServer) EXPECT() *MockChannelExtGameServerMockRecorder {
	return m.recorder
}

// AddWhiteList mocks base method.
func (m *MockChannelExtGameServer) AddWhiteList(ctx context.Context, in *AddWhiteListReq) (*AddWhiteListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddWhiteList", ctx, in)
	ret0, _ := ret[0].(*AddWhiteListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddWhiteList indicates an expected call of AddWhiteList.
func (mr *MockChannelExtGameServerMockRecorder) AddWhiteList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddWhiteList", reflect.TypeOf((*MockChannelExtGameServer)(nil).AddWhiteList), ctx, in)
}

// BatchGetUidByOpenIds mocks base method.
func (m *MockChannelExtGameServer) BatchGetUidByOpenIds(ctx context.Context, in *BatchGetUidByOpenIdsReq) (*BatchGetUidByOpenIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUidByOpenIds", ctx, in)
	ret0, _ := ret[0].(*BatchGetUidByOpenIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUidByOpenIds indicates an expected call of BatchGetUidByOpenIds.
func (mr *MockChannelExtGameServerMockRecorder) BatchGetUidByOpenIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUidByOpenIds", reflect.TypeOf((*MockChannelExtGameServer)(nil).BatchGetUidByOpenIds), ctx, in)
}

// CancelUserExtGameJsCode mocks base method.
func (m *MockChannelExtGameServer) CancelUserExtGameJsCode(ctx context.Context, in *CancelUserExtGameJsCodeReq) (*CancelUserExtGameJsCodeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelUserExtGameJsCode", ctx, in)
	ret0, _ := ret[0].(*CancelUserExtGameJsCodeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelUserExtGameJsCode indicates an expected call of CancelUserExtGameJsCode.
func (mr *MockChannelExtGameServerMockRecorder) CancelUserExtGameJsCode(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelUserExtGameJsCode", reflect.TypeOf((*MockChannelExtGameServer)(nil).CancelUserExtGameJsCode), ctx, in)
}

// CheckChannelBlackList mocks base method.
func (m *MockChannelExtGameServer) CheckChannelBlackList(ctx context.Context, in *CheckChannelBlackListReq) (*CheckChannelBlackListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckChannelBlackList", ctx, in)
	ret0, _ := ret[0].(*CheckChannelBlackListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckChannelBlackList indicates an expected call of CheckChannelBlackList.
func (mr *MockChannelExtGameServerMockRecorder) CheckChannelBlackList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckChannelBlackList", reflect.TypeOf((*MockChannelExtGameServer)(nil).CheckChannelBlackList), ctx, in)
}

// CheckUserGameAccess mocks base method.
func (m *MockChannelExtGameServer) CheckUserGameAccess(ctx context.Context, in *CheckUserGameAccessReq) (*CheckUserGameAccessResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserGameAccess", ctx, in)
	ret0, _ := ret[0].(*CheckUserGameAccessResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserGameAccess indicates an expected call of CheckUserGameAccess.
func (mr *MockChannelExtGameServerMockRecorder) CheckUserGameAccess(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserGameAccess", reflect.TypeOf((*MockChannelExtGameServer)(nil).CheckUserGameAccess), ctx, in)
}

// CheckWhiteList mocks base method.
func (m *MockChannelExtGameServer) CheckWhiteList(ctx context.Context, in *CheckWhiteListReq) (*CheckWhiteListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckWhiteList", ctx, in)
	ret0, _ := ret[0].(*CheckWhiteListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckWhiteList indicates an expected call of CheckWhiteList.
func (mr *MockChannelExtGameServerMockRecorder) CheckWhiteList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckWhiteList", reflect.TypeOf((*MockChannelExtGameServer)(nil).CheckWhiteList), ctx, in)
}

// DailyConsumeTotalCntPush mocks base method.
func (m *MockChannelExtGameServer) DailyConsumeTotalCntPush(ctx context.Context, in *DailyConsumeTotalCntPushReq) (*DailyConsumeTotalCntPushResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DailyConsumeTotalCntPush", ctx, in)
	ret0, _ := ret[0].(*DailyConsumeTotalCntPushResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DailyConsumeTotalCntPush indicates an expected call of DailyConsumeTotalCntPush.
func (mr *MockChannelExtGameServerMockRecorder) DailyConsumeTotalCntPush(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DailyConsumeTotalCntPush", reflect.TypeOf((*MockChannelExtGameServer)(nil).DailyConsumeTotalCntPush), ctx, in)
}

// ExtGameAward mocks base method.
func (m *MockChannelExtGameServer) ExtGameAward(ctx context.Context, in *ExtGameAwardReq) (*ExtGameAwardResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExtGameAward", ctx, in)
	ret0, _ := ret[0].(*ExtGameAwardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExtGameAward indicates an expected call of ExtGameAward.
func (mr *MockChannelExtGameServerMockRecorder) ExtGameAward(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtGameAward", reflect.TypeOf((*MockChannelExtGameServer)(nil).ExtGameAward), ctx, in)
}

// ExtGameConsume mocks base method.
func (m *MockChannelExtGameServer) ExtGameConsume(ctx context.Context, in *ExtGameConsumeReq) (*ExtGameConsumeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExtGameConsume", ctx, in)
	ret0, _ := ret[0].(*ExtGameConsumeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExtGameConsume indicates an expected call of ExtGameConsume.
func (mr *MockChannelExtGameServerMockRecorder) ExtGameConsume(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtGameConsume", reflect.TypeOf((*MockChannelExtGameServer)(nil).ExtGameConsume), ctx, in)
}

// GetAuthInfoByJsCode mocks base method.
func (m *MockChannelExtGameServer) GetAuthInfoByJsCode(ctx context.Context, in *GetAuthInfoByJsCodeReq) (*GetAuthInfoByJsCodeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuthInfoByJsCode", ctx, in)
	ret0, _ := ret[0].(*GetAuthInfoByJsCodeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuthInfoByJsCode indicates an expected call of GetAuthInfoByJsCode.
func (mr *MockChannelExtGameServerMockRecorder) GetAuthInfoByJsCode(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthInfoByJsCode", reflect.TypeOf((*MockChannelExtGameServer)(nil).GetAuthInfoByJsCode), ctx, in)
}

// GetAwardOrderIds mocks base method.
func (m *MockChannelExtGameServer) GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardOrderIds", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardOrderIds indicates an expected call of GetAwardOrderIds.
func (mr *MockChannelExtGameServerMockRecorder) GetAwardOrderIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardOrderIds", reflect.TypeOf((*MockChannelExtGameServer)(nil).GetAwardOrderIds), ctx, in)
}

// GetAwardTotalCount mocks base method.
func (m *MockChannelExtGameServer) GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardTotalCount", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardTotalCount indicates an expected call of GetAwardTotalCount.
func (mr *MockChannelExtGameServerMockRecorder) GetAwardTotalCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardTotalCount", reflect.TypeOf((*MockChannelExtGameServer)(nil).GetAwardTotalCount), ctx, in)
}

// GetConsumeOrderIds mocks base method.
func (m *MockChannelExtGameServer) GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsumeOrderIds", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeOrderIds indicates an expected call of GetConsumeOrderIds.
func (mr *MockChannelExtGameServerMockRecorder) GetConsumeOrderIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeOrderIds", reflect.TypeOf((*MockChannelExtGameServer)(nil).GetConsumeOrderIds), ctx, in)
}

// GetConsumeTotalCount mocks base method.
func (m *MockChannelExtGameServer) GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsumeTotalCount", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeTotalCount indicates an expected call of GetConsumeTotalCount.
func (mr *MockChannelExtGameServerMockRecorder) GetConsumeTotalCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeTotalCount", reflect.TypeOf((*MockChannelExtGameServer)(nil).GetConsumeTotalCount), ctx, in)
}

// GetExtGameInfoList mocks base method.
func (m *MockChannelExtGameServer) GetExtGameInfoList(ctx context.Context, in *GetExtGameInfoListReq) (*GetExtGameInfoListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExtGameInfoList", ctx, in)
	ret0, _ := ret[0].(*GetExtGameInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExtGameInfoList indicates an expected call of GetExtGameInfoList.
func (mr *MockChannelExtGameServerMockRecorder) GetExtGameInfoList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExtGameInfoList", reflect.TypeOf((*MockChannelExtGameServer)(nil).GetExtGameInfoList), ctx, in)
}

// GetUidByOpenid mocks base method.
func (m *MockChannelExtGameServer) GetUidByOpenid(ctx context.Context, in *GetUidByOpenidReq) (*GetUidByOpenidResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUidByOpenid", ctx, in)
	ret0, _ := ret[0].(*GetUidByOpenidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUidByOpenid indicates an expected call of GetUidByOpenid.
func (mr *MockChannelExtGameServerMockRecorder) GetUidByOpenid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUidByOpenid", reflect.TypeOf((*MockChannelExtGameServer)(nil).GetUidByOpenid), ctx, in)
}

// GetUserExtGameJsCode mocks base method.
func (m *MockChannelExtGameServer) GetUserExtGameJsCode(ctx context.Context, in *GetUserExtGameJsCodeReq) (*GetUserExtGameJsCodeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserExtGameJsCode", ctx, in)
	ret0, _ := ret[0].(*GetUserExtGameJsCodeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserExtGameJsCode indicates an expected call of GetUserExtGameJsCode.
func (mr *MockChannelExtGameServerMockRecorder) GetUserExtGameJsCode(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExtGameJsCode", reflect.TypeOf((*MockChannelExtGameServer)(nil).GetUserExtGameJsCode), ctx, in)
}

// GetUserExtGameOpenid mocks base method.
func (m *MockChannelExtGameServer) GetUserExtGameOpenid(ctx context.Context, in *GetUserExtGameOpenidReq) (*GetUserExtGameOpenidResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserExtGameOpenid", ctx, in)
	ret0, _ := ret[0].(*GetUserExtGameOpenidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserExtGameOpenid indicates an expected call of GetUserExtGameOpenid.
func (mr *MockChannelExtGameServerMockRecorder) GetUserExtGameOpenid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserExtGameOpenid", reflect.TypeOf((*MockChannelExtGameServer)(nil).GetUserExtGameOpenid), ctx, in)
}

// GetWhiteChannelRandomly mocks base method.
func (m *MockChannelExtGameServer) GetWhiteChannelRandomly(ctx context.Context, in *GetWhiteChannelRandomlyReq) (*GetWhiteChannelRandomlyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWhiteChannelRandomly", ctx, in)
	ret0, _ := ret[0].(*GetWhiteChannelRandomlyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWhiteChannelRandomly indicates an expected call of GetWhiteChannelRandomly.
func (mr *MockChannelExtGameServerMockRecorder) GetWhiteChannelRandomly(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWhiteChannelRandomly", reflect.TypeOf((*MockChannelExtGameServer)(nil).GetWhiteChannelRandomly), ctx, in)
}

// RemoveWhiteList mocks base method.
func (m *MockChannelExtGameServer) RemoveWhiteList(ctx context.Context, in *RemoveWhiteListReq) (*RemoveWhiteListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveWhiteList", ctx, in)
	ret0, _ := ret[0].(*RemoveWhiteListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveWhiteList indicates an expected call of RemoveWhiteList.
func (mr *MockChannelExtGameServerMockRecorder) RemoveWhiteList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveWhiteList", reflect.TypeOf((*MockChannelExtGameServer)(nil).RemoveWhiteList), ctx, in)
}

// SendPlatformMsg mocks base method.
func (m *MockChannelExtGameServer) SendPlatformMsg(ctx context.Context, in *SendPlatformMsgReq) (*SendPlatformMsgResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendPlatformMsg", ctx, in)
	ret0, _ := ret[0].(*SendPlatformMsgResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendPlatformMsg indicates an expected call of SendPlatformMsg.
func (mr *MockChannelExtGameServerMockRecorder) SendPlatformMsg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPlatformMsg", reflect.TypeOf((*MockChannelExtGameServer)(nil).SendPlatformMsg), ctx, in)
}

// SimpleQueryOpenid mocks base method.
func (m *MockChannelExtGameServer) SimpleQueryOpenid(ctx context.Context, in *SimpleQueryOpenidReq) (*SimpleQueryOpenidResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SimpleQueryOpenid", ctx, in)
	ret0, _ := ret[0].(*SimpleQueryOpenidResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SimpleQueryOpenid indicates an expected call of SimpleQueryOpenid.
func (mr *MockChannelExtGameServerMockRecorder) SimpleQueryOpenid(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimpleQueryOpenid", reflect.TypeOf((*MockChannelExtGameServer)(nil).SimpleQueryOpenid), ctx, in)
}

// TestChannelCommonHighLightIm mocks base method.
func (m *MockChannelExtGameServer) TestChannelCommonHighLightIm(ctx context.Context, in *TestChannelCommonHighLightImReq) (*TestChannelCommonHighLightImResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestChannelCommonHighLightIm", ctx, in)
	ret0, _ := ret[0].(*TestChannelCommonHighLightImResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestChannelCommonHighLightIm indicates an expected call of TestChannelCommonHighLightIm.
func (mr *MockChannelExtGameServerMockRecorder) TestChannelCommonHighLightIm(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestChannelCommonHighLightIm", reflect.TypeOf((*MockChannelExtGameServer)(nil).TestChannelCommonHighLightIm), ctx, in)
}
