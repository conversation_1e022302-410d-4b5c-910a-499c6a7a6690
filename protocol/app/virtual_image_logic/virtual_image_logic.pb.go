// Code generated by protoc-gen-go. DO NOT EDIT.
// source: virtual_image_logic/virtual_image_logic.proto

package virtual_image_logic // import "golang.52tt.com/protocol/app/virtual_image_logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 推荐是否也放品类
type VirtualImageResourceCategoryType int32

const (
	VirtualImageResourceCategoryType_VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_UNSPECIFIED VirtualImageResourceCategoryType = 0
	VirtualImageResourceCategoryType_VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_RECOMMEND   VirtualImageResourceCategoryType = 1
	VirtualImageResourceCategoryType_VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_SUIT        VirtualImageResourceCategoryType = 2
	VirtualImageResourceCategoryType_VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_FACE        VirtualImageResourceCategoryType = 3
	VirtualImageResourceCategoryType_VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_OTHER       VirtualImageResourceCategoryType = 100
)

var VirtualImageResourceCategoryType_name = map[int32]string{
	0:   "VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_UNSPECIFIED",
	1:   "VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_RECOMMEND",
	2:   "VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_SUIT",
	3:   "VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_FACE",
	100: "VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_OTHER",
}
var VirtualImageResourceCategoryType_value = map[string]int32{
	"VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_UNSPECIFIED": 0,
	"VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_RECOMMEND":   1,
	"VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_SUIT":        2,
	"VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_FACE":        3,
	"VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_OTHER":       100,
}

func (x VirtualImageResourceCategoryType) String() string {
	return proto.EnumName(VirtualImageResourceCategoryType_name, int32(x))
}
func (VirtualImageResourceCategoryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{0}
}

type VirtualImageResourceSubCategoryType int32

const (
	VirtualImageResourceSubCategoryType_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_UNSPECIFIED VirtualImageResourceSubCategoryType = 0
	VirtualImageResourceSubCategoryType_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_WRAP        VirtualImageResourceSubCategoryType = 1
	VirtualImageResourceSubCategoryType_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_SIT         VirtualImageResourceSubCategoryType = 2
	VirtualImageResourceSubCategoryType_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_SEAT        VirtualImageResourceSubCategoryType = 3
	VirtualImageResourceSubCategoryType_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_BACK        VirtualImageResourceSubCategoryType = 4
	VirtualImageResourceSubCategoryType_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_EFFECTS     VirtualImageResourceSubCategoryType = 5
	VirtualImageResourceSubCategoryType_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_OTHER       VirtualImageResourceSubCategoryType = 100
)

var VirtualImageResourceSubCategoryType_name = map[int32]string{
	0:   "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_UNSPECIFIED",
	1:   "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_WRAP",
	2:   "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_SIT",
	3:   "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_SEAT",
	4:   "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_BACK",
	5:   "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_EFFECTS",
	100: "VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_OTHER",
}
var VirtualImageResourceSubCategoryType_value = map[string]int32{
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_UNSPECIFIED": 0,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_WRAP":        1,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_SIT":         2,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_SEAT":        3,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_BACK":        4,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_EFFECTS":     5,
	"VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_TYPE_OTHER":       100,
}

func (x VirtualImageResourceSubCategoryType) String() string {
	return proto.EnumName(VirtualImageResourceSubCategoryType_name, int32(x))
}
func (VirtualImageResourceSubCategoryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{1}
}

type CommodityGainPath int32

const (
	CommodityGainPath_COMMODITY_GAIN_PATH_UNSPECIFIED          CommodityGainPath = 0
	CommodityGainPath_COMMODITY_GAIN_PATH_PURCHASE             CommodityGainPath = 1
	CommodityGainPath_COMMODITY_GAIN_PATH_GIFT                 CommodityGainPath = 2
	CommodityGainPath_COMMODITY_GAIN_PATH_ACTIVITY             CommodityGainPath = 3
	CommodityGainPath_COMMODITY_GAIN_PATH_REWARD               CommodityGainPath = 4
	CommodityGainPath_COMMODITY_GAIN_PATH_EXCHANGE             CommodityGainPath = 5
	CommodityGainPath_COMMODITY_GAIN_PATH_OTHER                CommodityGainPath = 6
	CommodityGainPath_COMMODITY_GAIN_PATH_INFINITE_CHANGE_CARD CommodityGainPath = 7
)

var CommodityGainPath_name = map[int32]string{
	0: "COMMODITY_GAIN_PATH_UNSPECIFIED",
	1: "COMMODITY_GAIN_PATH_PURCHASE",
	2: "COMMODITY_GAIN_PATH_GIFT",
	3: "COMMODITY_GAIN_PATH_ACTIVITY",
	4: "COMMODITY_GAIN_PATH_REWARD",
	5: "COMMODITY_GAIN_PATH_EXCHANGE",
	6: "COMMODITY_GAIN_PATH_OTHER",
	7: "COMMODITY_GAIN_PATH_INFINITE_CHANGE_CARD",
}
var CommodityGainPath_value = map[string]int32{
	"COMMODITY_GAIN_PATH_UNSPECIFIED":          0,
	"COMMODITY_GAIN_PATH_PURCHASE":             1,
	"COMMODITY_GAIN_PATH_GIFT":                 2,
	"COMMODITY_GAIN_PATH_ACTIVITY":             3,
	"COMMODITY_GAIN_PATH_REWARD":               4,
	"COMMODITY_GAIN_PATH_EXCHANGE":             5,
	"COMMODITY_GAIN_PATH_OTHER":                6,
	"COMMODITY_GAIN_PATH_INFINITE_CHANGE_CARD": 7,
}

func (x CommodityGainPath) String() string {
	return proto.EnumName(CommodityGainPath_name, int32(x))
}
func (CommodityGainPath) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{2}
}

type CommodityType int32

const (
	CommodityType_COMMODITY_TYPE_UNSPECIFIED CommodityType = 0
	CommodityType_COMMODITY_TYPE_SINGLE      CommodityType = 1
	CommodityType_COMMODITY_TYPE_SUIT        CommodityType = 2
)

var CommodityType_name = map[int32]string{
	0: "COMMODITY_TYPE_UNSPECIFIED",
	1: "COMMODITY_TYPE_SINGLE",
	2: "COMMODITY_TYPE_SUIT",
}
var CommodityType_value = map[string]int32{
	"COMMODITY_TYPE_UNSPECIFIED": 0,
	"COMMODITY_TYPE_SINGLE":      1,
	"COMMODITY_TYPE_SUIT":        2,
}

func (x CommodityType) String() string {
	return proto.EnumName(CommodityType_name, int32(x))
}
func (CommodityType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{3}
}

type VirtualImageResourceType int32

const (
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_UNSPECIFIED            VirtualImageResourceType = 0
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_SKELETON               VirtualImageResourceType = 1
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_SKIN                   VirtualImageResourceType = 2
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_BACKGROUND             VirtualImageResourceType = 3
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_FOLLOW                 VirtualImageResourceType = 4
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_SHOW_VAP               VirtualImageResourceType = 5
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_DEFAULT_BACKGROUND_PNG VirtualImageResourceType = 6
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_ANIMATION              VirtualImageResourceType = 7
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_SKIN_FULL_OUTFIT       VirtualImageResourceType = 8
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_INFORMATION_CARD_VAP   VirtualImageResourceType = 9
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_CP_SKELETON            VirtualImageResourceType = 10
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_SIDE_SKELETON          VirtualImageResourceType = 11
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_CP_SKIN                VirtualImageResourceType = 12
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_SKELETON          VirtualImageResourceType = 13
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_SIDE_SKELETON     VirtualImageResourceType = 14
	VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_CP_SKELETON       VirtualImageResourceType = 15
)

var VirtualImageResourceType_name = map[int32]string{
	0:  "VIRTUAL_IMAGE_RESOURCE_TYPE_UNSPECIFIED",
	1:  "VIRTUAL_IMAGE_RESOURCE_TYPE_SKELETON",
	2:  "VIRTUAL_IMAGE_RESOURCE_TYPE_SKIN",
	3:  "VIRTUAL_IMAGE_RESOURCE_TYPE_BACKGROUND",
	4:  "VIRTUAL_IMAGE_RESOURCE_TYPE_FOLLOW",
	5:  "VIRTUAL_IMAGE_RESOURCE_TYPE_SHOW_VAP",
	6:  "VIRTUAL_IMAGE_RESOURCE_TYPE_DEFAULT_BACKGROUND_PNG",
	7:  "VIRTUAL_IMAGE_RESOURCE_TYPE_ANIMATION",
	8:  "VIRTUAL_IMAGE_RESOURCE_TYPE_SKIN_FULL_OUTFIT",
	9:  "VIRTUAL_IMAGE_RESOURCE_TYPE_INFORMATION_CARD_VAP",
	10: "VIRTUAL_IMAGE_RESOURCE_TYPE_CP_SKELETON",
	11: "VIRTUAL_IMAGE_RESOURCE_TYPE_SIDE_SKELETON",
	12: "VIRTUAL_IMAGE_RESOURCE_TYPE_CP_SKIN",
	13: "VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_SKELETON",
	14: "VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_SIDE_SKELETON",
	15: "VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_CP_SKELETON",
}
var VirtualImageResourceType_value = map[string]int32{
	"VIRTUAL_IMAGE_RESOURCE_TYPE_UNSPECIFIED":            0,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_SKELETON":               1,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_SKIN":                   2,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_BACKGROUND":             3,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_FOLLOW":                 4,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_SHOW_VAP":               5,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_DEFAULT_BACKGROUND_PNG": 6,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_ANIMATION":              7,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_SKIN_FULL_OUTFIT":       8,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_INFORMATION_CARD_VAP":   9,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_CP_SKELETON":            10,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_SIDE_SKELETON":          11,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_CP_SKIN":                12,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_SKELETON":          13,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_SIDE_SKELETON":     14,
	"VIRTUAL_IMAGE_RESOURCE_TYPE_BASE_CP_SKELETON":       15,
}

func (x VirtualImageResourceType) String() string {
	return proto.EnumName(VirtualImageResourceType_name, int32(x))
}
func (VirtualImageResourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{4}
}

type RedDotAlertType int32

const (
	RedDotAlertType_RED_DOT_ALERT_TYPE_UNSPECIFIED          RedDotAlertType = 0
	RedDotAlertType_RED_DOT_ALERT_TYPE_COMMODITY_TAB_PARENT RedDotAlertType = 1 // Deprecated: Do not use.
	RedDotAlertType_RED_DOT_ALERT_TYPE_COMMODITY_TAB_SUB    RedDotAlertType = 2 // Deprecated: Do not use.
	RedDotAlertType_RED_DOT_ALERT_TYPE_PERSONAL_IMAGE       RedDotAlertType = 3
	RedDotAlertType_RED_DOT_ALERT_TYPE_VIRTUAL_IMAGE        RedDotAlertType = 4 // Deprecated: Do not use.
	RedDotAlertType_RED_DOT_ALERT_TYPE_COMMODITY            RedDotAlertType = 5 // Deprecated: Do not use.
	RedDotAlertType_RED_DOT_ALERT_TYPE_COMMODITY_TAB        RedDotAlertType = 6
	RedDotAlertType_RED_DOT_ALERT_TYPE_ROOM_VIRTUAL_IMAGE   RedDotAlertType = 7
)

var RedDotAlertType_name = map[int32]string{
	0: "RED_DOT_ALERT_TYPE_UNSPECIFIED",
	1: "RED_DOT_ALERT_TYPE_COMMODITY_TAB_PARENT",
	2: "RED_DOT_ALERT_TYPE_COMMODITY_TAB_SUB",
	3: "RED_DOT_ALERT_TYPE_PERSONAL_IMAGE",
	4: "RED_DOT_ALERT_TYPE_VIRTUAL_IMAGE",
	5: "RED_DOT_ALERT_TYPE_COMMODITY",
	6: "RED_DOT_ALERT_TYPE_COMMODITY_TAB",
	7: "RED_DOT_ALERT_TYPE_ROOM_VIRTUAL_IMAGE",
}
var RedDotAlertType_value = map[string]int32{
	"RED_DOT_ALERT_TYPE_UNSPECIFIED":          0,
	"RED_DOT_ALERT_TYPE_COMMODITY_TAB_PARENT": 1,
	"RED_DOT_ALERT_TYPE_COMMODITY_TAB_SUB":    2,
	"RED_DOT_ALERT_TYPE_PERSONAL_IMAGE":       3,
	"RED_DOT_ALERT_TYPE_VIRTUAL_IMAGE":        4,
	"RED_DOT_ALERT_TYPE_COMMODITY":            5,
	"RED_DOT_ALERT_TYPE_COMMODITY_TAB":        6,
	"RED_DOT_ALERT_TYPE_ROOM_VIRTUAL_IMAGE":   7,
}

func (x RedDotAlertType) String() string {
	return proto.EnumName(RedDotAlertType_name, int32(x))
}
func (RedDotAlertType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{5}
}

type VirtualImagePoseType int32

const (
	VirtualImagePoseType_VIRTUAL_IMAGE_POSE_TYPE_UNSPECIFIED VirtualImagePoseType = 0
	VirtualImagePoseType_VIRTUAL_IMAGE_POSE_TYPE_STAND       VirtualImagePoseType = 1
	VirtualImagePoseType_VIRTUAL_IMAGE_POSE_TYPE_SIT         VirtualImagePoseType = 2
)

var VirtualImagePoseType_name = map[int32]string{
	0: "VIRTUAL_IMAGE_POSE_TYPE_UNSPECIFIED",
	1: "VIRTUAL_IMAGE_POSE_TYPE_STAND",
	2: "VIRTUAL_IMAGE_POSE_TYPE_SIT",
}
var VirtualImagePoseType_value = map[string]int32{
	"VIRTUAL_IMAGE_POSE_TYPE_UNSPECIFIED": 0,
	"VIRTUAL_IMAGE_POSE_TYPE_STAND":       1,
	"VIRTUAL_IMAGE_POSE_TYPE_SIT":         2,
}

func (x VirtualImagePoseType) String() string {
	return proto.EnumName(VirtualImagePoseType_name, int32(x))
}
func (VirtualImagePoseType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{6}
}

// 虚拟形象权益
type VirtualImageRightsType int32

const (
	VirtualImageRightsType_VIRTUAL_IMAGE_RIGHTS_TYPE_UNSPECIFIED          VirtualImageRightsType = 0
	VirtualImageRightsType_VIRTUAL_IMAGE_RIGHTS_TYPE_INFINITE_CHANGE_CARD VirtualImageRightsType = 1
)

var VirtualImageRightsType_name = map[int32]string{
	0: "VIRTUAL_IMAGE_RIGHTS_TYPE_UNSPECIFIED",
	1: "VIRTUAL_IMAGE_RIGHTS_TYPE_INFINITE_CHANGE_CARD",
}
var VirtualImageRightsType_value = map[string]int32{
	"VIRTUAL_IMAGE_RIGHTS_TYPE_UNSPECIFIED":          0,
	"VIRTUAL_IMAGE_RIGHTS_TYPE_INFINITE_CHANGE_CARD": 1,
}

func (x VirtualImageRightsType) String() string {
	return proto.EnumName(VirtualImageRightsType_name, int32(x))
}
func (VirtualImageRightsType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{7}
}

type VirtualImageOrientation int32

const (
	VirtualImageOrientation_VIRTUAL_IMAGE_ORIENTATION_UNSPECIFIED VirtualImageOrientation = 0
	VirtualImageOrientation_VIRTUAL_IMAGE_ORIENTATION_RIGHT       VirtualImageOrientation = 1
	VirtualImageOrientation_VIRTUAL_IMAGE_ORIENTATION_LEFT        VirtualImageOrientation = 2
)

var VirtualImageOrientation_name = map[int32]string{
	0: "VIRTUAL_IMAGE_ORIENTATION_UNSPECIFIED",
	1: "VIRTUAL_IMAGE_ORIENTATION_RIGHT",
	2: "VIRTUAL_IMAGE_ORIENTATION_LEFT",
}
var VirtualImageOrientation_value = map[string]int32{
	"VIRTUAL_IMAGE_ORIENTATION_UNSPECIFIED": 0,
	"VIRTUAL_IMAGE_ORIENTATION_RIGHT":       1,
	"VIRTUAL_IMAGE_ORIENTATION_LEFT":        2,
}

func (x VirtualImageOrientation) String() string {
	return proto.EnumName(VirtualImageOrientation_name, int32(x))
}
func (VirtualImageOrientation) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{8}
}

// 外显场景开关类型
type VirtualImageDisplaySwitch int32

const (
	VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_UNSPECIFIED       VirtualImageDisplaySwitch = 0
	VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_MAIN              VirtualImageDisplaySwitch = 1
	VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_MIC               VirtualImageDisplaySwitch = 2
	VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_PERSONAL_PAGE     VirtualImageDisplaySwitch = 3
	VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_PROFILE_CARD      VirtualImageDisplaySwitch = 4
	VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_ROOM_ENTER_EFFECT VirtualImageDisplaySwitch = 5
	VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_FELLOW_SPACE      VirtualImageDisplaySwitch = 6
)

var VirtualImageDisplaySwitch_name = map[int32]string{
	0: "VIRTUAL_IMAGE_DISPLAY_SWITCH_UNSPECIFIED",
	1: "VIRTUAL_IMAGE_DISPLAY_SWITCH_MAIN",
	2: "VIRTUAL_IMAGE_DISPLAY_SWITCH_MIC",
	3: "VIRTUAL_IMAGE_DISPLAY_SWITCH_PERSONAL_PAGE",
	4: "VIRTUAL_IMAGE_DISPLAY_SWITCH_PROFILE_CARD",
	5: "VIRTUAL_IMAGE_DISPLAY_SWITCH_ROOM_ENTER_EFFECT",
	6: "VIRTUAL_IMAGE_DISPLAY_SWITCH_FELLOW_SPACE",
}
var VirtualImageDisplaySwitch_value = map[string]int32{
	"VIRTUAL_IMAGE_DISPLAY_SWITCH_UNSPECIFIED":       0,
	"VIRTUAL_IMAGE_DISPLAY_SWITCH_MAIN":              1,
	"VIRTUAL_IMAGE_DISPLAY_SWITCH_MIC":               2,
	"VIRTUAL_IMAGE_DISPLAY_SWITCH_PERSONAL_PAGE":     3,
	"VIRTUAL_IMAGE_DISPLAY_SWITCH_PROFILE_CARD":      4,
	"VIRTUAL_IMAGE_DISPLAY_SWITCH_ROOM_ENTER_EFFECT": 5,
	"VIRTUAL_IMAGE_DISPLAY_SWITCH_FELLOW_SPACE":      6,
}

func (x VirtualImageDisplaySwitch) String() string {
	return proto.EnumName(VirtualImageDisplaySwitch_name, int32(x))
}
func (VirtualImageDisplaySwitch) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{9}
}

type InviteAction int32

const (
	InviteAction_INVITE_ACTION_UNSPECIFIED InviteAction = 0
	InviteAction_INVITE_ACTION_SEND        InviteAction = 1
	InviteAction_INVITE_ACTION_CANCEL      InviteAction = 2
)

var InviteAction_name = map[int32]string{
	0: "INVITE_ACTION_UNSPECIFIED",
	1: "INVITE_ACTION_SEND",
	2: "INVITE_ACTION_CANCEL",
}
var InviteAction_value = map[string]int32{
	"INVITE_ACTION_UNSPECIFIED": 0,
	"INVITE_ACTION_SEND":        1,
	"INVITE_ACTION_CANCEL":      2,
}

func (x InviteAction) String() string {
	return proto.EnumName(InviteAction_name, int32(x))
}
func (InviteAction) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{10}
}

// 3. 确认/拒绝绑定
type BindConfirmAction int32

const (
	BindConfirmAction_BIND_CONFIRM_ACTION_UNSPECIFIED BindConfirmAction = 0
	BindConfirmAction_BIND_CONFIRM_ACTION_CONFIRM     BindConfirmAction = 1
	BindConfirmAction_BIND_CONFIRM_ACTION_REJECT      BindConfirmAction = 2
)

var BindConfirmAction_name = map[int32]string{
	0: "BIND_CONFIRM_ACTION_UNSPECIFIED",
	1: "BIND_CONFIRM_ACTION_CONFIRM",
	2: "BIND_CONFIRM_ACTION_REJECT",
}
var BindConfirmAction_value = map[string]int32{
	"BIND_CONFIRM_ACTION_UNSPECIFIED": 0,
	"BIND_CONFIRM_ACTION_CONFIRM":     1,
	"BIND_CONFIRM_ACTION_REJECT":      2,
}

func (x BindConfirmAction) String() string {
	return proto.EnumName(BindConfirmAction_name, int32(x))
}
func (BindConfirmAction) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{11}
}

type BindInviteStatus int32

const (
	BindInviteStatus_BIND_INVITE_STATUS_UNSPECIFIED BindInviteStatus = 0
	BindInviteStatus_BIND_INVITE_STATUS_UNHANDLED   BindInviteStatus = 1
	BindInviteStatus_BIND_INVITE_STATUS_ACCEPTED    BindInviteStatus = 2
	BindInviteStatus_BIND_INVITE_STATUS_REJECTED    BindInviteStatus = 3
	BindInviteStatus_BIND_INVITE_STATUS_EXPIRED     BindInviteStatus = 4
)

var BindInviteStatus_name = map[int32]string{
	0: "BIND_INVITE_STATUS_UNSPECIFIED",
	1: "BIND_INVITE_STATUS_UNHANDLED",
	2: "BIND_INVITE_STATUS_ACCEPTED",
	3: "BIND_INVITE_STATUS_REJECTED",
	4: "BIND_INVITE_STATUS_EXPIRED",
}
var BindInviteStatus_value = map[string]int32{
	"BIND_INVITE_STATUS_UNSPECIFIED": 0,
	"BIND_INVITE_STATUS_UNHANDLED":   1,
	"BIND_INVITE_STATUS_ACCEPTED":    2,
	"BIND_INVITE_STATUS_REJECTED":    3,
	"BIND_INVITE_STATUS_EXPIRED":     4,
}

func (x BindInviteStatus) String() string {
	return proto.EnumName(BindInviteStatus_name, int32(x))
}
func (BindInviteStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{12}
}

type VirtualImageCardPayResultExt_ResultType int32

const (
	VirtualImageCardPayResultExt_RESULT_TYPE_UNSPECIFIED      VirtualImageCardPayResultExt_ResultType = 0
	VirtualImageCardPayResultExt_RESULT_TYPE_ALREADY_CONTRACT VirtualImageCardPayResultExt_ResultType = 1
	VirtualImageCardPayResultExt_RESULT_TYPE_PAY_FOR_OTHER    VirtualImageCardPayResultExt_ResultType = 2
)

var VirtualImageCardPayResultExt_ResultType_name = map[int32]string{
	0: "RESULT_TYPE_UNSPECIFIED",
	1: "RESULT_TYPE_ALREADY_CONTRACT",
	2: "RESULT_TYPE_PAY_FOR_OTHER",
}
var VirtualImageCardPayResultExt_ResultType_value = map[string]int32{
	"RESULT_TYPE_UNSPECIFIED":      0,
	"RESULT_TYPE_ALREADY_CONTRACT": 1,
	"RESULT_TYPE_PAY_FOR_OTHER":    2,
}

func (x VirtualImageCardPayResultExt_ResultType) String() string {
	return proto.EnumName(VirtualImageCardPayResultExt_ResultType_name, int32(x))
}
func (VirtualImageCardPayResultExt_ResultType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{93, 0}
}

// 一级品类信息
type VirtualImageParentCategoryInfo struct {
	Category             uint32   `protobuf:"varint,1,opt,name=category,proto3" json:"category,omitempty"`
	CategoryName         string   `protobuf:"bytes,2,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"`
	CategoryType         uint32   `protobuf:"varint,3,opt,name=category_type,json=categoryType,proto3" json:"category_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VirtualImageParentCategoryInfo) Reset()         { *m = VirtualImageParentCategoryInfo{} }
func (m *VirtualImageParentCategoryInfo) String() string { return proto.CompactTextString(m) }
func (*VirtualImageParentCategoryInfo) ProtoMessage()    {}
func (*VirtualImageParentCategoryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{0}
}
func (m *VirtualImageParentCategoryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImageParentCategoryInfo.Unmarshal(m, b)
}
func (m *VirtualImageParentCategoryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImageParentCategoryInfo.Marshal(b, m, deterministic)
}
func (dst *VirtualImageParentCategoryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImageParentCategoryInfo.Merge(dst, src)
}
func (m *VirtualImageParentCategoryInfo) XXX_Size() int {
	return xxx_messageInfo_VirtualImageParentCategoryInfo.Size(m)
}
func (m *VirtualImageParentCategoryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImageParentCategoryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImageParentCategoryInfo proto.InternalMessageInfo

func (m *VirtualImageParentCategoryInfo) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *VirtualImageParentCategoryInfo) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

func (m *VirtualImageParentCategoryInfo) GetCategoryType() uint32 {
	if m != nil {
		return m.CategoryType
	}
	return 0
}

// 二级品类信息
type VirtualImageSubCategoryInfo struct {
	SubCategory              uint32   `protobuf:"varint,1,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	SubCategoryName          string   `protobuf:"bytes,2,opt,name=sub_category_name,json=subCategoryName,proto3" json:"sub_category_name,omitempty"`
	SubCategoryImgUrl        string   `protobuf:"bytes,3,opt,name=sub_category_img_url,json=subCategoryImgUrl,proto3" json:"sub_category_img_url,omitempty"`
	CategoryType             uint32   `protobuf:"varint,4,opt,name=category_type,json=categoryType,proto3" json:"category_type,omitempty"`
	SubCategoryImgPreviewUrl string   `protobuf:"bytes,5,opt,name=sub_category_img_preview_url,json=subCategoryImgPreviewUrl,proto3" json:"sub_category_img_preview_url,omitempty"`
	XXX_NoUnkeyedLiteral     struct{} `json:"-"`
	XXX_unrecognized         []byte   `json:"-"`
	XXX_sizecache            int32    `json:"-"`
}

func (m *VirtualImageSubCategoryInfo) Reset()         { *m = VirtualImageSubCategoryInfo{} }
func (m *VirtualImageSubCategoryInfo) String() string { return proto.CompactTextString(m) }
func (*VirtualImageSubCategoryInfo) ProtoMessage()    {}
func (*VirtualImageSubCategoryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{1}
}
func (m *VirtualImageSubCategoryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImageSubCategoryInfo.Unmarshal(m, b)
}
func (m *VirtualImageSubCategoryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImageSubCategoryInfo.Marshal(b, m, deterministic)
}
func (dst *VirtualImageSubCategoryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImageSubCategoryInfo.Merge(dst, src)
}
func (m *VirtualImageSubCategoryInfo) XXX_Size() int {
	return xxx_messageInfo_VirtualImageSubCategoryInfo.Size(m)
}
func (m *VirtualImageSubCategoryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImageSubCategoryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImageSubCategoryInfo proto.InternalMessageInfo

func (m *VirtualImageSubCategoryInfo) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

func (m *VirtualImageSubCategoryInfo) GetSubCategoryName() string {
	if m != nil {
		return m.SubCategoryName
	}
	return ""
}

func (m *VirtualImageSubCategoryInfo) GetSubCategoryImgUrl() string {
	if m != nil {
		return m.SubCategoryImgUrl
	}
	return ""
}

func (m *VirtualImageSubCategoryInfo) GetCategoryType() uint32 {
	if m != nil {
		return m.CategoryType
	}
	return 0
}

func (m *VirtualImageSubCategoryInfo) GetSubCategoryImgPreviewUrl() string {
	if m != nil {
		return m.SubCategoryImgPreviewUrl
	}
	return ""
}

// 资源品类信息
type VirtualImageResourceCategoryInfo struct {
	ParentCategoryInfo   *VirtualImageParentCategoryInfo `protobuf:"bytes,1,opt,name=parent_category_info,json=parentCategoryInfo,proto3" json:"parent_category_info,omitempty"`
	SubCategoryInfoList  []*VirtualImageSubCategoryInfo  `protobuf:"bytes,2,rep,name=sub_category_info_list,json=subCategoryInfoList,proto3" json:"sub_category_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *VirtualImageResourceCategoryInfo) Reset()         { *m = VirtualImageResourceCategoryInfo{} }
func (m *VirtualImageResourceCategoryInfo) String() string { return proto.CompactTextString(m) }
func (*VirtualImageResourceCategoryInfo) ProtoMessage()    {}
func (*VirtualImageResourceCategoryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{2}
}
func (m *VirtualImageResourceCategoryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImageResourceCategoryInfo.Unmarshal(m, b)
}
func (m *VirtualImageResourceCategoryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImageResourceCategoryInfo.Marshal(b, m, deterministic)
}
func (dst *VirtualImageResourceCategoryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImageResourceCategoryInfo.Merge(dst, src)
}
func (m *VirtualImageResourceCategoryInfo) XXX_Size() int {
	return xxx_messageInfo_VirtualImageResourceCategoryInfo.Size(m)
}
func (m *VirtualImageResourceCategoryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImageResourceCategoryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImageResourceCategoryInfo proto.InternalMessageInfo

func (m *VirtualImageResourceCategoryInfo) GetParentCategoryInfo() *VirtualImageParentCategoryInfo {
	if m != nil {
		return m.ParentCategoryInfo
	}
	return nil
}

func (m *VirtualImageResourceCategoryInfo) GetSubCategoryInfoList() []*VirtualImageSubCategoryInfo {
	if m != nil {
		return m.SubCategoryInfoList
	}
	return nil
}

// 获取默认形象资源列表
type GetDefaultResourceListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetDefaultResourceListRequest) Reset()         { *m = GetDefaultResourceListRequest{} }
func (m *GetDefaultResourceListRequest) String() string { return proto.CompactTextString(m) }
func (*GetDefaultResourceListRequest) ProtoMessage()    {}
func (*GetDefaultResourceListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{3}
}
func (m *GetDefaultResourceListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDefaultResourceListRequest.Unmarshal(m, b)
}
func (m *GetDefaultResourceListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDefaultResourceListRequest.Marshal(b, m, deterministic)
}
func (dst *GetDefaultResourceListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDefaultResourceListRequest.Merge(dst, src)
}
func (m *GetDefaultResourceListRequest) XXX_Size() int {
	return xxx_messageInfo_GetDefaultResourceListRequest.Size(m)
}
func (m *GetDefaultResourceListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDefaultResourceListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetDefaultResourceListRequest proto.InternalMessageInfo

func (m *GetDefaultResourceListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type MutualExclusion struct {
	Category             []uint32 `protobuf:"varint,1,rep,packed,name=category,proto3" json:"category,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MutualExclusion) Reset()         { *m = MutualExclusion{} }
func (m *MutualExclusion) String() string { return proto.CompactTextString(m) }
func (*MutualExclusion) ProtoMessage()    {}
func (*MutualExclusion) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{4}
}
func (m *MutualExclusion) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MutualExclusion.Unmarshal(m, b)
}
func (m *MutualExclusion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MutualExclusion.Marshal(b, m, deterministic)
}
func (dst *MutualExclusion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MutualExclusion.Merge(dst, src)
}
func (m *MutualExclusion) XXX_Size() int {
	return xxx_messageInfo_MutualExclusion.Size(m)
}
func (m *MutualExclusion) XXX_DiscardUnknown() {
	xxx_messageInfo_MutualExclusion.DiscardUnknown(m)
}

var xxx_messageInfo_MutualExclusion proto.InternalMessageInfo

func (m *MutualExclusion) GetCategory() []uint32 {
	if m != nil {
		return m.Category
	}
	return nil
}

type GetDefaultResourceListResponse struct {
	BaseResp             *app.BaseResp               `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MaleResources        []uint32                    `protobuf:"varint,2,rep,packed,name=male_resources,json=maleResources,proto3" json:"male_resources,omitempty"`
	FemaleResources      []uint32                    `protobuf:"varint,3,rep,packed,name=female_resources,json=femaleResources,proto3" json:"female_resources,omitempty"`
	OutFit               []uint32                    `protobuf:"varint,4,rep,packed,name=out_fit,json=outFit,proto3" json:"out_fit,omitempty"`
	MaleAnimationMap     map[uint32]uint32           `protobuf:"bytes,5,rep,name=male_animation_map,json=maleAnimationMap,proto3" json:"male_animation_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	FemaleAnimationMap   map[uint32]uint32           `protobuf:"bytes,6,rep,name=female_animation_map,json=femaleAnimationMap,proto3" json:"female_animation_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	DatingHatMap         map[uint32]uint32           `protobuf:"bytes,7,rep,name=dating_hat_map,json=datingHatMap,proto3" json:"dating_hat_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	UnShowList           []uint32                    `protobuf:"varint,8,rep,packed,name=un_show_list,json=unShowList,proto3" json:"un_show_list,omitempty"`
	MapMutualExclusion   map[uint32]*MutualExclusion `protobuf:"bytes,9,rep,name=map_mutual_exclusion,json=mapMutualExclusion,proto3" json:"map_mutual_exclusion,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	GuideUrl             string                      `protobuf:"bytes,10,opt,name=guide_url,json=guideUrl,proto3" json:"guide_url,omitempty"`
	GuideUrlMd5          string                      `protobuf:"bytes,11,opt,name=guide_url_md5,json=guideUrlMd5,proto3" json:"guide_url_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetDefaultResourceListResponse) Reset()         { *m = GetDefaultResourceListResponse{} }
func (m *GetDefaultResourceListResponse) String() string { return proto.CompactTextString(m) }
func (*GetDefaultResourceListResponse) ProtoMessage()    {}
func (*GetDefaultResourceListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{5}
}
func (m *GetDefaultResourceListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDefaultResourceListResponse.Unmarshal(m, b)
}
func (m *GetDefaultResourceListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDefaultResourceListResponse.Marshal(b, m, deterministic)
}
func (dst *GetDefaultResourceListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDefaultResourceListResponse.Merge(dst, src)
}
func (m *GetDefaultResourceListResponse) XXX_Size() int {
	return xxx_messageInfo_GetDefaultResourceListResponse.Size(m)
}
func (m *GetDefaultResourceListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDefaultResourceListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetDefaultResourceListResponse proto.InternalMessageInfo

func (m *GetDefaultResourceListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetDefaultResourceListResponse) GetMaleResources() []uint32 {
	if m != nil {
		return m.MaleResources
	}
	return nil
}

func (m *GetDefaultResourceListResponse) GetFemaleResources() []uint32 {
	if m != nil {
		return m.FemaleResources
	}
	return nil
}

func (m *GetDefaultResourceListResponse) GetOutFit() []uint32 {
	if m != nil {
		return m.OutFit
	}
	return nil
}

func (m *GetDefaultResourceListResponse) GetMaleAnimationMap() map[uint32]uint32 {
	if m != nil {
		return m.MaleAnimationMap
	}
	return nil
}

func (m *GetDefaultResourceListResponse) GetFemaleAnimationMap() map[uint32]uint32 {
	if m != nil {
		return m.FemaleAnimationMap
	}
	return nil
}

func (m *GetDefaultResourceListResponse) GetDatingHatMap() map[uint32]uint32 {
	if m != nil {
		return m.DatingHatMap
	}
	return nil
}

func (m *GetDefaultResourceListResponse) GetUnShowList() []uint32 {
	if m != nil {
		return m.UnShowList
	}
	return nil
}

func (m *GetDefaultResourceListResponse) GetMapMutualExclusion() map[uint32]*MutualExclusion {
	if m != nil {
		return m.MapMutualExclusion
	}
	return nil
}

func (m *GetDefaultResourceListResponse) GetGuideUrl() string {
	if m != nil {
		return m.GuideUrl
	}
	return ""
}

func (m *GetDefaultResourceListResponse) GetGuideUrlMd5() string {
	if m != nil {
		return m.GuideUrlMd5
	}
	return ""
}

// 获取资源品类列表
type GetVirtualImageResourceCategoryRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetVirtualImageResourceCategoryRequest) Reset() {
	*m = GetVirtualImageResourceCategoryRequest{}
}
func (m *GetVirtualImageResourceCategoryRequest) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageResourceCategoryRequest) ProtoMessage()    {}
func (*GetVirtualImageResourceCategoryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{6}
}
func (m *GetVirtualImageResourceCategoryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageResourceCategoryRequest.Unmarshal(m, b)
}
func (m *GetVirtualImageResourceCategoryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageResourceCategoryRequest.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageResourceCategoryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageResourceCategoryRequest.Merge(dst, src)
}
func (m *GetVirtualImageResourceCategoryRequest) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageResourceCategoryRequest.Size(m)
}
func (m *GetVirtualImageResourceCategoryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageResourceCategoryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageResourceCategoryRequest proto.InternalMessageInfo

func (m *GetVirtualImageResourceCategoryRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetVirtualImageResourceCategoryResponse struct {
	BaseResp                 *app.BaseResp                       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ResourceCategoryInfoList []*VirtualImageResourceCategoryInfo `protobuf:"bytes,2,rep,name=resource_category_info_list,json=resourceCategoryInfoList,proto3" json:"resource_category_info_list,omitempty"`
	IsNew                    bool                                `protobuf:"varint,3,opt,name=is_new,json=isNew,proto3" json:"is_new,omitempty"`
	XXX_NoUnkeyedLiteral     struct{}                            `json:"-"`
	XXX_unrecognized         []byte                              `json:"-"`
	XXX_sizecache            int32                               `json:"-"`
}

func (m *GetVirtualImageResourceCategoryResponse) Reset() {
	*m = GetVirtualImageResourceCategoryResponse{}
}
func (m *GetVirtualImageResourceCategoryResponse) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageResourceCategoryResponse) ProtoMessage()    {}
func (*GetVirtualImageResourceCategoryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{7}
}
func (m *GetVirtualImageResourceCategoryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageResourceCategoryResponse.Unmarshal(m, b)
}
func (m *GetVirtualImageResourceCategoryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageResourceCategoryResponse.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageResourceCategoryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageResourceCategoryResponse.Merge(dst, src)
}
func (m *GetVirtualImageResourceCategoryResponse) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageResourceCategoryResponse.Size(m)
}
func (m *GetVirtualImageResourceCategoryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageResourceCategoryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageResourceCategoryResponse proto.InternalMessageInfo

func (m *GetVirtualImageResourceCategoryResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetVirtualImageResourceCategoryResponse) GetResourceCategoryInfoList() []*VirtualImageResourceCategoryInfo {
	if m != nil {
		return m.ResourceCategoryInfoList
	}
	return nil
}

func (m *GetVirtualImageResourceCategoryResponse) GetIsNew() bool {
	if m != nil {
		return m.IsNew
	}
	return false
}

type CommodityDataPackage struct {
	PackageId            uint32   `protobuf:"varint,1,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	Price                uint32   `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`
	DiscountPrice        uint32   `protobuf:"varint,3,opt,name=discount_price,json=discountPrice,proto3" json:"discount_price,omitempty"`
	DiscountRate         uint32   `protobuf:"varint,4,opt,name=discount_rate,json=discountRate,proto3" json:"discount_rate,omitempty"`
	EffectiveDay         uint32   `protobuf:"varint,5,opt,name=effective_day,json=effectiveDay,proto3" json:"effective_day,omitempty"`
	Description          string   `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,7,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	IsPerpetual          bool     `protobuf:"varint,8,opt,name=is_perpetual,json=isPerpetual,proto3" json:"is_perpetual,omitempty"`
	ShowExpireTime       bool     `protobuf:"varint,9,opt,name=show_expire_time,json=showExpireTime,proto3" json:"show_expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommodityDataPackage) Reset()         { *m = CommodityDataPackage{} }
func (m *CommodityDataPackage) String() string { return proto.CompactTextString(m) }
func (*CommodityDataPackage) ProtoMessage()    {}
func (*CommodityDataPackage) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{8}
}
func (m *CommodityDataPackage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommodityDataPackage.Unmarshal(m, b)
}
func (m *CommodityDataPackage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommodityDataPackage.Marshal(b, m, deterministic)
}
func (dst *CommodityDataPackage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommodityDataPackage.Merge(dst, src)
}
func (m *CommodityDataPackage) XXX_Size() int {
	return xxx_messageInfo_CommodityDataPackage.Size(m)
}
func (m *CommodityDataPackage) XXX_DiscardUnknown() {
	xxx_messageInfo_CommodityDataPackage.DiscardUnknown(m)
}

var xxx_messageInfo_CommodityDataPackage proto.InternalMessageInfo

func (m *CommodityDataPackage) GetPackageId() uint32 {
	if m != nil {
		return m.PackageId
	}
	return 0
}

func (m *CommodityDataPackage) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *CommodityDataPackage) GetDiscountPrice() uint32 {
	if m != nil {
		return m.DiscountPrice
	}
	return 0
}

func (m *CommodityDataPackage) GetDiscountRate() uint32 {
	if m != nil {
		return m.DiscountRate
	}
	return 0
}

func (m *CommodityDataPackage) GetEffectiveDay() uint32 {
	if m != nil {
		return m.EffectiveDay
	}
	return 0
}

func (m *CommodityDataPackage) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *CommodityDataPackage) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *CommodityDataPackage) GetIsPerpetual() bool {
	if m != nil {
		return m.IsPerpetual
	}
	return false
}

func (m *CommodityDataPackage) GetShowExpireTime() bool {
	if m != nil {
		return m.ShowExpireTime
	}
	return false
}

// 活动信息
type VirtualImageActivityInfo struct {
	Desc                 string   `protobuf:"bytes,1,opt,name=desc,proto3" json:"desc,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	DescColor            string   `protobuf:"bytes,3,opt,name=desc_color,json=descColor,proto3" json:"desc_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VirtualImageActivityInfo) Reset()         { *m = VirtualImageActivityInfo{} }
func (m *VirtualImageActivityInfo) String() string { return proto.CompactTextString(m) }
func (*VirtualImageActivityInfo) ProtoMessage()    {}
func (*VirtualImageActivityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{9}
}
func (m *VirtualImageActivityInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImageActivityInfo.Unmarshal(m, b)
}
func (m *VirtualImageActivityInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImageActivityInfo.Marshal(b, m, deterministic)
}
func (dst *VirtualImageActivityInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImageActivityInfo.Merge(dst, src)
}
func (m *VirtualImageActivityInfo) XXX_Size() int {
	return xxx_messageInfo_VirtualImageActivityInfo.Size(m)
}
func (m *VirtualImageActivityInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImageActivityInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImageActivityInfo proto.InternalMessageInfo

func (m *VirtualImageActivityInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *VirtualImageActivityInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *VirtualImageActivityInfo) GetDescColor() string {
	if m != nil {
		return m.DescColor
	}
	return ""
}

// 运营自定义标识
type CustomizeLogotype struct {
	Logotype             string   `protobuf:"bytes,1,opt,name=logotype,proto3" json:"logotype,omitempty"`
	ShelfTime            uint32   `protobuf:"varint,2,opt,name=shelf_time,json=shelfTime,proto3" json:"shelf_time,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,3,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	IsPerpetual          bool     `protobuf:"varint,4,opt,name=is_perpetual,json=isPerpetual,proto3" json:"is_perpetual,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomizeLogotype) Reset()         { *m = CustomizeLogotype{} }
func (m *CustomizeLogotype) String() string { return proto.CompactTextString(m) }
func (*CustomizeLogotype) ProtoMessage()    {}
func (*CustomizeLogotype) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{10}
}
func (m *CustomizeLogotype) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomizeLogotype.Unmarshal(m, b)
}
func (m *CustomizeLogotype) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomizeLogotype.Marshal(b, m, deterministic)
}
func (dst *CustomizeLogotype) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomizeLogotype.Merge(dst, src)
}
func (m *CustomizeLogotype) XXX_Size() int {
	return xxx_messageInfo_CustomizeLogotype.Size(m)
}
func (m *CustomizeLogotype) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomizeLogotype.DiscardUnknown(m)
}

var xxx_messageInfo_CustomizeLogotype proto.InternalMessageInfo

func (m *CustomizeLogotype) GetLogotype() string {
	if m != nil {
		return m.Logotype
	}
	return ""
}

func (m *CustomizeLogotype) GetShelfTime() uint32 {
	if m != nil {
		return m.ShelfTime
	}
	return 0
}

func (m *CustomizeLogotype) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *CustomizeLogotype) GetIsPerpetual() bool {
	if m != nil {
		return m.IsPerpetual
	}
	return false
}

type CommodityData struct {
	CommodityId               uint32                      `protobuf:"varint,1,opt,name=commodity_id,json=commodityId,proto3" json:"commodity_id,omitempty"`
	LevelIcon                 string                      `protobuf:"bytes,2,opt,name=level_icon,json=levelIcon,proto3" json:"level_icon,omitempty"`
	Level                     uint32                      `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	Logotype                  string                      `protobuf:"bytes,4,opt,name=logotype,proto3" json:"logotype,omitempty"`
	CommodityIcon             string                      `protobuf:"bytes,5,opt,name=commodity_icon,json=commodityIcon,proto3" json:"commodity_icon,omitempty"`
	CommodityAnimation        string                      `protobuf:"bytes,6,opt,name=commodity_animation,json=commodityAnimation,proto3" json:"commodity_animation,omitempty"`
	CommodityName             string                      `protobuf:"bytes,7,opt,name=commodity_name,json=commodityName,proto3" json:"commodity_name,omitempty"`
	GainPath                  uint32                      `protobuf:"varint,8,opt,name=gain_path,json=gainPath,proto3" json:"gain_path,omitempty"`
	ResourceList              []*VirtualImageResourceInfo `protobuf:"bytes,9,rep,name=resource_list,json=resourceList,proto3" json:"resource_list,omitempty"`
	Category                  uint32                      `protobuf:"varint,10,opt,name=category,proto3" json:"category,omitempty"`
	SubCategory               uint32                      `protobuf:"varint,11,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	CommodityType             uint32                      `protobuf:"varint,12,opt,name=commodity_type,json=commodityType,proto3" json:"commodity_type,omitempty"`
	Rank                      uint32                      `protobuf:"varint,13,opt,name=rank,proto3" json:"rank,omitempty"`
	ResourceSex               uint32                      `protobuf:"varint,16,opt,name=resource_sex,json=resourceSex,proto3" json:"resource_sex,omitempty"`
	PricePackageList          []*CommodityDataPackage     `protobuf:"bytes,17,rep,name=price_package_list,json=pricePackageList,proto3" json:"price_package_list,omitempty"`
	ActInfo                   *VirtualImageActivityInfo   `protobuf:"bytes,18,opt,name=act_info,json=actInfo,proto3" json:"act_info,omitempty"`
	ExpireTime                uint32                      `protobuf:"varint,19,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	SpineAnimation            string                      `protobuf:"bytes,20,opt,name=spine_animation,json=spineAnimation,proto3" json:"spine_animation,omitempty"`
	IsGet                     bool                        `protobuf:"varint,21,opt,name=is_get,json=isGet,proto3" json:"is_get,omitempty"`
	CommodityAnimationTop     string                      `protobuf:"bytes,22,opt,name=commodity_animation_top,json=commodityAnimationTop,proto3" json:"commodity_animation_top,omitempty"`
	CommodityAnimationTopMd5  string                      `protobuf:"bytes,23,opt,name=commodity_animation_top_md5,json=commodityAnimationTopMd5,proto3" json:"commodity_animation_top_md5,omitempty"`
	CommodityAnimationBack    string                      `protobuf:"bytes,24,opt,name=commodity_animation_back,json=commodityAnimationBack,proto3" json:"commodity_animation_back,omitempty"`
	CommodityAnimationBackMd5 string                      `protobuf:"bytes,25,opt,name=commodity_animation_back_md5,json=commodityAnimationBackMd5,proto3" json:"commodity_animation_back_md5,omitempty"`
	UserExpireTime            uint32                      `protobuf:"varint,26,opt,name=user_expire_time,json=userExpireTime,proto3" json:"user_expire_time,omitempty"`
	UserIsPerpetual           bool                        `protobuf:"varint,27,opt,name=user_is_perpetual,json=userIsPerpetual,proto3" json:"user_is_perpetual,omitempty"`
	UserBuyTime               uint32                      `protobuf:"varint,28,opt,name=user_buy_time,json=userBuyTime,proto3" json:"user_buy_time,omitempty"`
	ShelfTime                 uint32                      `protobuf:"varint,29,opt,name=shelf_time,json=shelfTime,proto3" json:"shelf_time,omitempty"`
	ShowExpireTime            bool                        `protobuf:"varint,30,opt,name=show_expire_time,json=showExpireTime,proto3" json:"show_expire_time,omitempty"` // Deprecated: Do not use.
	RedDotVersion             uint32                      `protobuf:"varint,31,opt,name=red_dot_version,json=redDotVersion,proto3" json:"red_dot_version,omitempty"`
	PromotionalVideoId        uint32                      `protobuf:"varint,32,opt,name=promotional_video_id,json=promotionalVideoId,proto3" json:"promotional_video_id,omitempty"`
	LevelWebp                 string                      `protobuf:"bytes,33,opt,name=level_webp,json=levelWebp,proto3" json:"level_webp,omitempty"`
	IsRecommend               bool                        `protobuf:"varint,34,opt,name=is_recommend,json=isRecommend,proto3" json:"is_recommend,omitempty"`
	SuitUniqueId              string                      `protobuf:"bytes,35,opt,name=suit_unique_id,json=suitUniqueId,proto3" json:"suit_unique_id,omitempty"`
	XXX_NoUnkeyedLiteral      struct{}                    `json:"-"`
	XXX_unrecognized          []byte                      `json:"-"`
	XXX_sizecache             int32                       `json:"-"`
}

func (m *CommodityData) Reset()         { *m = CommodityData{} }
func (m *CommodityData) String() string { return proto.CompactTextString(m) }
func (*CommodityData) ProtoMessage()    {}
func (*CommodityData) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{11}
}
func (m *CommodityData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommodityData.Unmarshal(m, b)
}
func (m *CommodityData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommodityData.Marshal(b, m, deterministic)
}
func (dst *CommodityData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommodityData.Merge(dst, src)
}
func (m *CommodityData) XXX_Size() int {
	return xxx_messageInfo_CommodityData.Size(m)
}
func (m *CommodityData) XXX_DiscardUnknown() {
	xxx_messageInfo_CommodityData.DiscardUnknown(m)
}

var xxx_messageInfo_CommodityData proto.InternalMessageInfo

func (m *CommodityData) GetCommodityId() uint32 {
	if m != nil {
		return m.CommodityId
	}
	return 0
}

func (m *CommodityData) GetLevelIcon() string {
	if m != nil {
		return m.LevelIcon
	}
	return ""
}

func (m *CommodityData) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *CommodityData) GetLogotype() string {
	if m != nil {
		return m.Logotype
	}
	return ""
}

func (m *CommodityData) GetCommodityIcon() string {
	if m != nil {
		return m.CommodityIcon
	}
	return ""
}

func (m *CommodityData) GetCommodityAnimation() string {
	if m != nil {
		return m.CommodityAnimation
	}
	return ""
}

func (m *CommodityData) GetCommodityName() string {
	if m != nil {
		return m.CommodityName
	}
	return ""
}

func (m *CommodityData) GetGainPath() uint32 {
	if m != nil {
		return m.GainPath
	}
	return 0
}

func (m *CommodityData) GetResourceList() []*VirtualImageResourceInfo {
	if m != nil {
		return m.ResourceList
	}
	return nil
}

func (m *CommodityData) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *CommodityData) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

func (m *CommodityData) GetCommodityType() uint32 {
	if m != nil {
		return m.CommodityType
	}
	return 0
}

func (m *CommodityData) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *CommodityData) GetResourceSex() uint32 {
	if m != nil {
		return m.ResourceSex
	}
	return 0
}

func (m *CommodityData) GetPricePackageList() []*CommodityDataPackage {
	if m != nil {
		return m.PricePackageList
	}
	return nil
}

func (m *CommodityData) GetActInfo() *VirtualImageActivityInfo {
	if m != nil {
		return m.ActInfo
	}
	return nil
}

func (m *CommodityData) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *CommodityData) GetSpineAnimation() string {
	if m != nil {
		return m.SpineAnimation
	}
	return ""
}

func (m *CommodityData) GetIsGet() bool {
	if m != nil {
		return m.IsGet
	}
	return false
}

func (m *CommodityData) GetCommodityAnimationTop() string {
	if m != nil {
		return m.CommodityAnimationTop
	}
	return ""
}

func (m *CommodityData) GetCommodityAnimationTopMd5() string {
	if m != nil {
		return m.CommodityAnimationTopMd5
	}
	return ""
}

func (m *CommodityData) GetCommodityAnimationBack() string {
	if m != nil {
		return m.CommodityAnimationBack
	}
	return ""
}

func (m *CommodityData) GetCommodityAnimationBackMd5() string {
	if m != nil {
		return m.CommodityAnimationBackMd5
	}
	return ""
}

func (m *CommodityData) GetUserExpireTime() uint32 {
	if m != nil {
		return m.UserExpireTime
	}
	return 0
}

func (m *CommodityData) GetUserIsPerpetual() bool {
	if m != nil {
		return m.UserIsPerpetual
	}
	return false
}

func (m *CommodityData) GetUserBuyTime() uint32 {
	if m != nil {
		return m.UserBuyTime
	}
	return 0
}

func (m *CommodityData) GetShelfTime() uint32 {
	if m != nil {
		return m.ShelfTime
	}
	return 0
}

// Deprecated: Do not use.
func (m *CommodityData) GetShowExpireTime() bool {
	if m != nil {
		return m.ShowExpireTime
	}
	return false
}

func (m *CommodityData) GetRedDotVersion() uint32 {
	if m != nil {
		return m.RedDotVersion
	}
	return 0
}

func (m *CommodityData) GetPromotionalVideoId() uint32 {
	if m != nil {
		return m.PromotionalVideoId
	}
	return 0
}

func (m *CommodityData) GetLevelWebp() string {
	if m != nil {
		return m.LevelWebp
	}
	return ""
}

func (m *CommodityData) GetIsRecommend() bool {
	if m != nil {
		return m.IsRecommend
	}
	return false
}

func (m *CommodityData) GetSuitUniqueId() string {
	if m != nil {
		return m.SuitUniqueId
	}
	return ""
}

// 获取数量变化时的商品总价
type ComputeCommodityPriceRequest struct {
	BaseReq              *app.BaseReq         `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	CommodityItemList    []*ShoppingItemBasic `protobuf:"bytes,2,rep,name=commodity_item_list,json=commodityItemList,proto3" json:"commodity_item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ComputeCommodityPriceRequest) Reset()         { *m = ComputeCommodityPriceRequest{} }
func (m *ComputeCommodityPriceRequest) String() string { return proto.CompactTextString(m) }
func (*ComputeCommodityPriceRequest) ProtoMessage()    {}
func (*ComputeCommodityPriceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{12}
}
func (m *ComputeCommodityPriceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ComputeCommodityPriceRequest.Unmarshal(m, b)
}
func (m *ComputeCommodityPriceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ComputeCommodityPriceRequest.Marshal(b, m, deterministic)
}
func (dst *ComputeCommodityPriceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ComputeCommodityPriceRequest.Merge(dst, src)
}
func (m *ComputeCommodityPriceRequest) XXX_Size() int {
	return xxx_messageInfo_ComputeCommodityPriceRequest.Size(m)
}
func (m *ComputeCommodityPriceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ComputeCommodityPriceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ComputeCommodityPriceRequest proto.InternalMessageInfo

func (m *ComputeCommodityPriceRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ComputeCommodityPriceRequest) GetCommodityItemList() []*ShoppingItemBasic {
	if m != nil {
		return m.CommodityItemList
	}
	return nil
}

type ComputeCommodityPriceResponse struct {
	BaseResp                *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CommodityItemList       []*ShoppingItemBasic `protobuf:"bytes,2,rep,name=commodity_item_list,json=commodityItemList,proto3" json:"commodity_item_list,omitempty"`
	TotalPrice              uint32               `protobuf:"varint,3,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	ExpireCommodityItemList []*ShoppingItemBasic `protobuf:"bytes,4,rep,name=expire_commodity_item_list,json=expireCommodityItemList,proto3" json:"expire_commodity_item_list,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}             `json:"-"`
	XXX_unrecognized        []byte               `json:"-"`
	XXX_sizecache           int32                `json:"-"`
}

func (m *ComputeCommodityPriceResponse) Reset()         { *m = ComputeCommodityPriceResponse{} }
func (m *ComputeCommodityPriceResponse) String() string { return proto.CompactTextString(m) }
func (*ComputeCommodityPriceResponse) ProtoMessage()    {}
func (*ComputeCommodityPriceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{13}
}
func (m *ComputeCommodityPriceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ComputeCommodityPriceResponse.Unmarshal(m, b)
}
func (m *ComputeCommodityPriceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ComputeCommodityPriceResponse.Marshal(b, m, deterministic)
}
func (dst *ComputeCommodityPriceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ComputeCommodityPriceResponse.Merge(dst, src)
}
func (m *ComputeCommodityPriceResponse) XXX_Size() int {
	return xxx_messageInfo_ComputeCommodityPriceResponse.Size(m)
}
func (m *ComputeCommodityPriceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ComputeCommodityPriceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ComputeCommodityPriceResponse proto.InternalMessageInfo

func (m *ComputeCommodityPriceResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ComputeCommodityPriceResponse) GetCommodityItemList() []*ShoppingItemBasic {
	if m != nil {
		return m.CommodityItemList
	}
	return nil
}

func (m *ComputeCommodityPriceResponse) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *ComputeCommodityPriceResponse) GetExpireCommodityItemList() []*ShoppingItemBasic {
	if m != nil {
		return m.ExpireCommodityItemList
	}
	return nil
}

// 获取商品列表 category和sub_category 都填0时 获取全量商品列表
type GetCommodityDataListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Category             uint32       `protobuf:"varint,2,opt,name=category,proto3" json:"category,omitempty"`
	SubCategory          uint32       `protobuf:"varint,3,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	ResourceIdList       []uint32     `protobuf:"varint,4,rep,packed,name=resource_id_list,json=resourceIdList,proto3" json:"resource_id_list,omitempty"`
	CommodityType        uint32       `protobuf:"varint,5,opt,name=commodity_type,json=commodityType,proto3" json:"commodity_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCommodityDataListRequest) Reset()         { *m = GetCommodityDataListRequest{} }
func (m *GetCommodityDataListRequest) String() string { return proto.CompactTextString(m) }
func (*GetCommodityDataListRequest) ProtoMessage()    {}
func (*GetCommodityDataListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{14}
}
func (m *GetCommodityDataListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommodityDataListRequest.Unmarshal(m, b)
}
func (m *GetCommodityDataListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommodityDataListRequest.Marshal(b, m, deterministic)
}
func (dst *GetCommodityDataListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommodityDataListRequest.Merge(dst, src)
}
func (m *GetCommodityDataListRequest) XXX_Size() int {
	return xxx_messageInfo_GetCommodityDataListRequest.Size(m)
}
func (m *GetCommodityDataListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommodityDataListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommodityDataListRequest proto.InternalMessageInfo

func (m *GetCommodityDataListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetCommodityDataListRequest) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *GetCommodityDataListRequest) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

func (m *GetCommodityDataListRequest) GetResourceIdList() []uint32 {
	if m != nil {
		return m.ResourceIdList
	}
	return nil
}

func (m *GetCommodityDataListRequest) GetCommodityType() uint32 {
	if m != nil {
		return m.CommodityType
	}
	return 0
}

type GetCommodityDataListResponse struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CommodityDataList    []*CommodityData `protobuf:"bytes,2,rep,name=commodity_data_list,json=commodityDataList,proto3" json:"commodity_data_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetCommodityDataListResponse) Reset()         { *m = GetCommodityDataListResponse{} }
func (m *GetCommodityDataListResponse) String() string { return proto.CompactTextString(m) }
func (*GetCommodityDataListResponse) ProtoMessage()    {}
func (*GetCommodityDataListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{15}
}
func (m *GetCommodityDataListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommodityDataListResponse.Unmarshal(m, b)
}
func (m *GetCommodityDataListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommodityDataListResponse.Marshal(b, m, deterministic)
}
func (dst *GetCommodityDataListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommodityDataListResponse.Merge(dst, src)
}
func (m *GetCommodityDataListResponse) XXX_Size() int {
	return xxx_messageInfo_GetCommodityDataListResponse.Size(m)
}
func (m *GetCommodityDataListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommodityDataListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommodityDataListResponse proto.InternalMessageInfo

func (m *GetCommodityDataListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetCommodityDataListResponse) GetCommodityDataList() []*CommodityData {
	if m != nil {
		return m.CommodityDataList
	}
	return nil
}

// 获取推荐商品列表
type GetRecommendCommodityDataListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRecommendCommodityDataListRequest) Reset()         { *m = GetRecommendCommodityDataListRequest{} }
func (m *GetRecommendCommodityDataListRequest) String() string { return proto.CompactTextString(m) }
func (*GetRecommendCommodityDataListRequest) ProtoMessage()    {}
func (*GetRecommendCommodityDataListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{16}
}
func (m *GetRecommendCommodityDataListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendCommodityDataListRequest.Unmarshal(m, b)
}
func (m *GetRecommendCommodityDataListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendCommodityDataListRequest.Marshal(b, m, deterministic)
}
func (dst *GetRecommendCommodityDataListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendCommodityDataListRequest.Merge(dst, src)
}
func (m *GetRecommendCommodityDataListRequest) XXX_Size() int {
	return xxx_messageInfo_GetRecommendCommodityDataListRequest.Size(m)
}
func (m *GetRecommendCommodityDataListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendCommodityDataListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendCommodityDataListRequest proto.InternalMessageInfo

func (m *GetRecommendCommodityDataListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetRecommendCommodityDataListResponse struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CommodityDataList    []*CommodityData `protobuf:"bytes,2,rep,name=commodity_data_list,json=commodityDataList,proto3" json:"commodity_data_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetRecommendCommodityDataListResponse) Reset()         { *m = GetRecommendCommodityDataListResponse{} }
func (m *GetRecommendCommodityDataListResponse) String() string { return proto.CompactTextString(m) }
func (*GetRecommendCommodityDataListResponse) ProtoMessage()    {}
func (*GetRecommendCommodityDataListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{17}
}
func (m *GetRecommendCommodityDataListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendCommodityDataListResponse.Unmarshal(m, b)
}
func (m *GetRecommendCommodityDataListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendCommodityDataListResponse.Marshal(b, m, deterministic)
}
func (dst *GetRecommendCommodityDataListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendCommodityDataListResponse.Merge(dst, src)
}
func (m *GetRecommendCommodityDataListResponse) XXX_Size() int {
	return xxx_messageInfo_GetRecommendCommodityDataListResponse.Size(m)
}
func (m *GetRecommendCommodityDataListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendCommodityDataListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendCommodityDataListResponse proto.InternalMessageInfo

func (m *GetRecommendCommodityDataListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRecommendCommodityDataListResponse) GetCommodityDataList() []*CommodityData {
	if m != nil {
		return m.CommodityDataList
	}
	return nil
}

// 购物车商品基础数据
type ShoppingItemBasic struct {
	// uint32 shopping_item_id = 1;        // 购物车商品ID
	CommodityId          uint32   `protobuf:"varint,2,opt,name=commodity_id,json=commodityId,proto3" json:"commodity_id,omitempty"`
	PackageId            uint32   `protobuf:"varint,3,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	TotalPrice           uint32   `protobuf:"varint,5,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	AvgPrice             uint32   `protobuf:"varint,6,opt,name=avg_price,json=avgPrice,proto3" json:"avg_price,omitempty"`
	EffectiveDay         uint32   `protobuf:"varint,7,opt,name=effective_day,json=effectiveDay,proto3" json:"effective_day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShoppingItemBasic) Reset()         { *m = ShoppingItemBasic{} }
func (m *ShoppingItemBasic) String() string { return proto.CompactTextString(m) }
func (*ShoppingItemBasic) ProtoMessage()    {}
func (*ShoppingItemBasic) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{18}
}
func (m *ShoppingItemBasic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShoppingItemBasic.Unmarshal(m, b)
}
func (m *ShoppingItemBasic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShoppingItemBasic.Marshal(b, m, deterministic)
}
func (dst *ShoppingItemBasic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShoppingItemBasic.Merge(dst, src)
}
func (m *ShoppingItemBasic) XXX_Size() int {
	return xxx_messageInfo_ShoppingItemBasic.Size(m)
}
func (m *ShoppingItemBasic) XXX_DiscardUnknown() {
	xxx_messageInfo_ShoppingItemBasic.DiscardUnknown(m)
}

var xxx_messageInfo_ShoppingItemBasic proto.InternalMessageInfo

func (m *ShoppingItemBasic) GetCommodityId() uint32 {
	if m != nil {
		return m.CommodityId
	}
	return 0
}

func (m *ShoppingItemBasic) GetPackageId() uint32 {
	if m != nil {
		return m.PackageId
	}
	return 0
}

func (m *ShoppingItemBasic) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ShoppingItemBasic) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *ShoppingItemBasic) GetAvgPrice() uint32 {
	if m != nil {
		return m.AvgPrice
	}
	return 0
}

func (m *ShoppingItemBasic) GetEffectiveDay() uint32 {
	if m != nil {
		return m.EffectiveDay
	}
	return 0
}

// 购买商品
type BuyCommodityDataRequest struct {
	BaseReq              *app.BaseReq         `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	CommodityItemList    []*ShoppingItemBasic `protobuf:"bytes,2,rep,name=commodity_item_list,json=commodityItemList,proto3" json:"commodity_item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BuyCommodityDataRequest) Reset()         { *m = BuyCommodityDataRequest{} }
func (m *BuyCommodityDataRequest) String() string { return proto.CompactTextString(m) }
func (*BuyCommodityDataRequest) ProtoMessage()    {}
func (*BuyCommodityDataRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{19}
}
func (m *BuyCommodityDataRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyCommodityDataRequest.Unmarshal(m, b)
}
func (m *BuyCommodityDataRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyCommodityDataRequest.Marshal(b, m, deterministic)
}
func (dst *BuyCommodityDataRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyCommodityDataRequest.Merge(dst, src)
}
func (m *BuyCommodityDataRequest) XXX_Size() int {
	return xxx_messageInfo_BuyCommodityDataRequest.Size(m)
}
func (m *BuyCommodityDataRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyCommodityDataRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BuyCommodityDataRequest proto.InternalMessageInfo

func (m *BuyCommodityDataRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BuyCommodityDataRequest) GetCommodityItemList() []*ShoppingItemBasic {
	if m != nil {
		return m.CommodityItemList
	}
	return nil
}

type BuyCommodityDataResponse struct {
	BaseResp                *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ExpireCommodityItemList []*ShoppingItemBasic `protobuf:"bytes,4,rep,name=expire_commodity_item_list,json=expireCommodityItemList,proto3" json:"expire_commodity_item_list,omitempty"` // Deprecated: Do not use.
	OrderId                 string               `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}             `json:"-"`
	XXX_unrecognized        []byte               `json:"-"`
	XXX_sizecache           int32                `json:"-"`
}

func (m *BuyCommodityDataResponse) Reset()         { *m = BuyCommodityDataResponse{} }
func (m *BuyCommodityDataResponse) String() string { return proto.CompactTextString(m) }
func (*BuyCommodityDataResponse) ProtoMessage()    {}
func (*BuyCommodityDataResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{20}
}
func (m *BuyCommodityDataResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyCommodityDataResponse.Unmarshal(m, b)
}
func (m *BuyCommodityDataResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyCommodityDataResponse.Marshal(b, m, deterministic)
}
func (dst *BuyCommodityDataResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyCommodityDataResponse.Merge(dst, src)
}
func (m *BuyCommodityDataResponse) XXX_Size() int {
	return xxx_messageInfo_BuyCommodityDataResponse.Size(m)
}
func (m *BuyCommodityDataResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyCommodityDataResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BuyCommodityDataResponse proto.InternalMessageInfo

func (m *BuyCommodityDataResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// Deprecated: Do not use.
func (m *BuyCommodityDataResponse) GetExpireCommodityItemList() []*ShoppingItemBasic {
	if m != nil {
		return m.ExpireCommodityItemList
	}
	return nil
}

func (m *BuyCommodityDataResponse) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

// 根据id获取商品详情
type GetCommodityDataListByIdRequest struct {
	BaseReq              *app.BaseReq         `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	CommodityItemList    []*ShoppingItemBasic `protobuf:"bytes,2,rep,name=commodity_item_list,json=commodityItemList,proto3" json:"commodity_item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetCommodityDataListByIdRequest) Reset()         { *m = GetCommodityDataListByIdRequest{} }
func (m *GetCommodityDataListByIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetCommodityDataListByIdRequest) ProtoMessage()    {}
func (*GetCommodityDataListByIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{21}
}
func (m *GetCommodityDataListByIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommodityDataListByIdRequest.Unmarshal(m, b)
}
func (m *GetCommodityDataListByIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommodityDataListByIdRequest.Marshal(b, m, deterministic)
}
func (dst *GetCommodityDataListByIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommodityDataListByIdRequest.Merge(dst, src)
}
func (m *GetCommodityDataListByIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetCommodityDataListByIdRequest.Size(m)
}
func (m *GetCommodityDataListByIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommodityDataListByIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommodityDataListByIdRequest proto.InternalMessageInfo

func (m *GetCommodityDataListByIdRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetCommodityDataListByIdRequest) GetCommodityItemList() []*ShoppingItemBasic {
	if m != nil {
		return m.CommodityItemList
	}
	return nil
}

type GetCommodityDataListByIdResponse struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CommodityDataList    []*CommodityData `protobuf:"bytes,2,rep,name=commodity_data_list,json=commodityDataList,proto3" json:"commodity_data_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetCommodityDataListByIdResponse) Reset()         { *m = GetCommodityDataListByIdResponse{} }
func (m *GetCommodityDataListByIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetCommodityDataListByIdResponse) ProtoMessage()    {}
func (*GetCommodityDataListByIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{22}
}
func (m *GetCommodityDataListByIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommodityDataListByIdResponse.Unmarshal(m, b)
}
func (m *GetCommodityDataListByIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommodityDataListByIdResponse.Marshal(b, m, deterministic)
}
func (dst *GetCommodityDataListByIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommodityDataListByIdResponse.Merge(dst, src)
}
func (m *GetCommodityDataListByIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetCommodityDataListByIdResponse.Size(m)
}
func (m *GetCommodityDataListByIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommodityDataListByIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommodityDataListByIdResponse proto.InternalMessageInfo

func (m *GetCommodityDataListByIdResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetCommodityDataListByIdResponse) GetCommodityDataList() []*CommodityData {
	if m != nil {
		return m.CommodityDataList
	}
	return nil
}

// FreeCommodityGainNotify 免费获得商品弹窗
type FreeCommodityGainNotify struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cnt                  uint32           `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	CommodityDataList    []*CommodityData `protobuf:"bytes,3,rep,name=commodity_data_list,json=commodityDataList,proto3" json:"commodity_data_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *FreeCommodityGainNotify) Reset()         { *m = FreeCommodityGainNotify{} }
func (m *FreeCommodityGainNotify) String() string { return proto.CompactTextString(m) }
func (*FreeCommodityGainNotify) ProtoMessage()    {}
func (*FreeCommodityGainNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{23}
}
func (m *FreeCommodityGainNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FreeCommodityGainNotify.Unmarshal(m, b)
}
func (m *FreeCommodityGainNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FreeCommodityGainNotify.Marshal(b, m, deterministic)
}
func (dst *FreeCommodityGainNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FreeCommodityGainNotify.Merge(dst, src)
}
func (m *FreeCommodityGainNotify) XXX_Size() int {
	return xxx_messageInfo_FreeCommodityGainNotify.Size(m)
}
func (m *FreeCommodityGainNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_FreeCommodityGainNotify.DiscardUnknown(m)
}

var xxx_messageInfo_FreeCommodityGainNotify proto.InternalMessageInfo

func (m *FreeCommodityGainNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FreeCommodityGainNotify) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

func (m *FreeCommodityGainNotify) GetCommodityDataList() []*CommodityData {
	if m != nil {
		return m.CommodityDataList
	}
	return nil
}

// 获取资源列表
type GetResourceListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Offset               uint32       `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32       `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	LatestVersion        uint32       `protobuf:"varint,4,opt,name=latest_version,json=latestVersion,proto3" json:"latest_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetResourceListRequest) Reset()         { *m = GetResourceListRequest{} }
func (m *GetResourceListRequest) String() string { return proto.CompactTextString(m) }
func (*GetResourceListRequest) ProtoMessage()    {}
func (*GetResourceListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{24}
}
func (m *GetResourceListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetResourceListRequest.Unmarshal(m, b)
}
func (m *GetResourceListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetResourceListRequest.Marshal(b, m, deterministic)
}
func (dst *GetResourceListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetResourceListRequest.Merge(dst, src)
}
func (m *GetResourceListRequest) XXX_Size() int {
	return xxx_messageInfo_GetResourceListRequest.Size(m)
}
func (m *GetResourceListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetResourceListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetResourceListRequest proto.InternalMessageInfo

func (m *GetResourceListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetResourceListRequest) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetResourceListRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetResourceListRequest) GetLatestVersion() uint32 {
	if m != nil {
		return m.LatestVersion
	}
	return 0
}

type VirtualImageResourceInfo struct {
	Id               uint32            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SkinName         string            `protobuf:"bytes,2,opt,name=skin_name,json=skinName,proto3" json:"skin_name,omitempty"`
	ResourceUrl      string            `protobuf:"bytes,3,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	Version          uint32            `protobuf:"varint,4,opt,name=version,proto3" json:"version,omitempty"`
	Essential        bool              `protobuf:"varint,5,opt,name=essential,proto3" json:"essential,omitempty"`
	ShelfTime        uint32            `protobuf:"varint,6,opt,name=shelf_time,json=shelfTime,proto3" json:"shelf_time,omitempty"`
	ExpireTime       uint32            `protobuf:"varint,7,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	Md5              string            `protobuf:"bytes,8,opt,name=md5,proto3" json:"md5,omitempty"`
	EncryptKey       string            `protobuf:"bytes,9,opt,name=encrypt_key,json=encryptKey,proto3" json:"encrypt_key,omitempty"`
	ResourceName     string            `protobuf:"bytes,10,opt,name=resource_name,json=resourceName,proto3" json:"resource_name,omitempty"`
	ResourceType     uint32            `protobuf:"varint,11,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	DisplayName      string            `protobuf:"bytes,12,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	IconUrl          string            `protobuf:"bytes,13,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	Category         uint32            `protobuf:"varint,14,opt,name=category,proto3" json:"category,omitempty"`
	SubCategory      uint32            `protobuf:"varint,15,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	LevelIcon        string            `protobuf:"bytes,16,opt,name=level_icon,json=levelIcon,proto3" json:"level_icon,omitempty"`
	Level            uint32            `protobuf:"varint,17,opt,name=level,proto3" json:"level,omitempty"`
	Sex              uint32            `protobuf:"varint,18,opt,name=sex,proto3" json:"sex,omitempty"`
	LevelWebp        string            `protobuf:"bytes,19,opt,name=level_webp,json=levelWebp,proto3" json:"level_webp,omitempty"`
	ScaleAble        bool              `protobuf:"varint,20,opt,name=scale_able,json=scaleAble,proto3" json:"scale_able,omitempty"`
	DefaultAnimation string            `protobuf:"bytes,21,opt,name=default_animation,json=defaultAnimation,proto3" json:"default_animation,omitempty"`
	SkinFacing       uint32            `protobuf:"varint,22,opt,name=skin_facing,json=skinFacing,proto3" json:"skin_facing,omitempty"`
	ResourcePrefix   string            `protobuf:"bytes,23,opt,name=resource_prefix,json=resourcePrefix,proto3" json:"resource_prefix,omitempty"`
	CustomMap        map[string]string `protobuf:"bytes,24,rep,name=custom_map,json=customMap,proto3" json:"custom_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 按照骨骼名称作为key区分皮肤资源信息
	// 例如:
	// 1. 正身皮肤资源信息, key: base_boy.zip, base_girl.zip
	// 2. 侧身皮肤资源信息, key: base_cboy.zip, base_cgirl.zip
	// 3. 双人正正皮肤资源信息, key: base_zboy_zgirl.zip
	// 4. 双人正侧皮肤资源信息, key: base_zboy_cgirl.zip
	// 5. 双人侧侧皮肤资源信息, key: base_cboy_cgirl.zip
	SkinMap              map[string]*SkinInfo `protobuf:"bytes,26,rep,name=skin_map,json=skinMap,proto3" json:"skin_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	IosSkinMap           map[string]*SkinInfo `protobuf:"bytes,27,rep,name=ios_skin_map,json=iosSkinMap,proto3" json:"ios_skin_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	IosResourceUrl       string               `protobuf:"bytes,28,opt,name=ios_resource_url,json=iosResourceUrl,proto3" json:"ios_resource_url,omitempty"`
	IosVersion           uint32               `protobuf:"varint,29,opt,name=ios_version,json=iosVersion,proto3" json:"ios_version,omitempty"`
	IsNewResource        bool                 `protobuf:"varint,30,opt,name=is_new_resource,json=isNewResource,proto3" json:"is_new_resource,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *VirtualImageResourceInfo) Reset()         { *m = VirtualImageResourceInfo{} }
func (m *VirtualImageResourceInfo) String() string { return proto.CompactTextString(m) }
func (*VirtualImageResourceInfo) ProtoMessage()    {}
func (*VirtualImageResourceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{25}
}
func (m *VirtualImageResourceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImageResourceInfo.Unmarshal(m, b)
}
func (m *VirtualImageResourceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImageResourceInfo.Marshal(b, m, deterministic)
}
func (dst *VirtualImageResourceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImageResourceInfo.Merge(dst, src)
}
func (m *VirtualImageResourceInfo) XXX_Size() int {
	return xxx_messageInfo_VirtualImageResourceInfo.Size(m)
}
func (m *VirtualImageResourceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImageResourceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImageResourceInfo proto.InternalMessageInfo

func (m *VirtualImageResourceInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetSkinName() string {
	if m != nil {
		return m.SkinName
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetEssential() bool {
	if m != nil {
		return m.Essential
	}
	return false
}

func (m *VirtualImageResourceInfo) GetShelfTime() uint32 {
	if m != nil {
		return m.ShelfTime
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetEncryptKey() string {
	if m != nil {
		return m.EncryptKey
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetResourceName() string {
	if m != nil {
		return m.ResourceName
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetResourceType() uint32 {
	if m != nil {
		return m.ResourceType
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetDisplayName() string {
	if m != nil {
		return m.DisplayName
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetLevelIcon() string {
	if m != nil {
		return m.LevelIcon
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetLevelWebp() string {
	if m != nil {
		return m.LevelWebp
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetScaleAble() bool {
	if m != nil {
		return m.ScaleAble
	}
	return false
}

func (m *VirtualImageResourceInfo) GetDefaultAnimation() string {
	if m != nil {
		return m.DefaultAnimation
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetSkinFacing() uint32 {
	if m != nil {
		return m.SkinFacing
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetResourcePrefix() string {
	if m != nil {
		return m.ResourcePrefix
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetCustomMap() map[string]string {
	if m != nil {
		return m.CustomMap
	}
	return nil
}

func (m *VirtualImageResourceInfo) GetSkinMap() map[string]*SkinInfo {
	if m != nil {
		return m.SkinMap
	}
	return nil
}

func (m *VirtualImageResourceInfo) GetIosSkinMap() map[string]*SkinInfo {
	if m != nil {
		return m.IosSkinMap
	}
	return nil
}

func (m *VirtualImageResourceInfo) GetIosResourceUrl() string {
	if m != nil {
		return m.IosResourceUrl
	}
	return ""
}

func (m *VirtualImageResourceInfo) GetIosVersion() uint32 {
	if m != nil {
		return m.IosVersion
	}
	return 0
}

func (m *VirtualImageResourceInfo) GetIsNewResource() bool {
	if m != nil {
		return m.IsNewResource
	}
	return false
}

type SkinInfo struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	MinBonesVersion      uint32   `protobuf:"varint,2,opt,name=min_bones_version,json=minBonesVersion,proto3" json:"min_bones_version,omitempty"`
	Md5                  string   `protobuf:"bytes,3,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SkinInfo) Reset()         { *m = SkinInfo{} }
func (m *SkinInfo) String() string { return proto.CompactTextString(m) }
func (*SkinInfo) ProtoMessage()    {}
func (*SkinInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{26}
}
func (m *SkinInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkinInfo.Unmarshal(m, b)
}
func (m *SkinInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkinInfo.Marshal(b, m, deterministic)
}
func (dst *SkinInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkinInfo.Merge(dst, src)
}
func (m *SkinInfo) XXX_Size() int {
	return xxx_messageInfo_SkinInfo.Size(m)
}
func (m *SkinInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SkinInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SkinInfo proto.InternalMessageInfo

func (m *SkinInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *SkinInfo) GetMinBonesVersion() uint32 {
	if m != nil {
		return m.MinBonesVersion
	}
	return 0
}

func (m *SkinInfo) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

type GetResourceListResponse struct {
	BaseResp             *app.BaseResp               `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Offset               uint32                      `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32                      `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Resources            []*VirtualImageResourceInfo `protobuf:"bytes,4,rep,name=resources,proto3" json:"resources,omitempty"`
	LatestVersion        uint32                      `protobuf:"varint,5,opt,name=latest_version,json=latestVersion,proto3" json:"latest_version,omitempty"`
	IsEnd                bool                        `protobuf:"varint,6,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
	DownloadUrl          string                      `protobuf:"bytes,7,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
	DownloadMd5          string                      `protobuf:"bytes,8,opt,name=download_md5,json=downloadMd5,proto3" json:"download_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetResourceListResponse) Reset()         { *m = GetResourceListResponse{} }
func (m *GetResourceListResponse) String() string { return proto.CompactTextString(m) }
func (*GetResourceListResponse) ProtoMessage()    {}
func (*GetResourceListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{27}
}
func (m *GetResourceListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetResourceListResponse.Unmarshal(m, b)
}
func (m *GetResourceListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetResourceListResponse.Marshal(b, m, deterministic)
}
func (dst *GetResourceListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetResourceListResponse.Merge(dst, src)
}
func (m *GetResourceListResponse) XXX_Size() int {
	return xxx_messageInfo_GetResourceListResponse.Size(m)
}
func (m *GetResourceListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetResourceListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetResourceListResponse proto.InternalMessageInfo

func (m *GetResourceListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetResourceListResponse) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetResourceListResponse) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetResourceListResponse) GetResources() []*VirtualImageResourceInfo {
	if m != nil {
		return m.Resources
	}
	return nil
}

func (m *GetResourceListResponse) GetLatestVersion() uint32 {
	if m != nil {
		return m.LatestVersion
	}
	return 0
}

func (m *GetResourceListResponse) GetIsEnd() bool {
	if m != nil {
		return m.IsEnd
	}
	return false
}

func (m *GetResourceListResponse) GetDownloadUrl() string {
	if m != nil {
		return m.DownloadUrl
	}
	return ""
}

func (m *GetResourceListResponse) GetDownloadMd5() string {
	if m != nil {
		return m.DownloadMd5
	}
	return ""
}

// 获取红点状态
type GetRedDotAlertStatusRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	RedDotAlerType       uint32       `protobuf:"varint,2,opt,name=red_dot_aler_type,json=redDotAlerType,proto3" json:"red_dot_aler_type,omitempty"`
	Category             uint32       `protobuf:"varint,3,opt,name=category,proto3" json:"category,omitempty"`                          // Deprecated: Do not use.
	SubCategory          uint32       `protobuf:"varint,4,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"` // Deprecated: Do not use.
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRedDotAlertStatusRequest) Reset()         { *m = GetRedDotAlertStatusRequest{} }
func (m *GetRedDotAlertStatusRequest) String() string { return proto.CompactTextString(m) }
func (*GetRedDotAlertStatusRequest) ProtoMessage()    {}
func (*GetRedDotAlertStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{28}
}
func (m *GetRedDotAlertStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRedDotAlertStatusRequest.Unmarshal(m, b)
}
func (m *GetRedDotAlertStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRedDotAlertStatusRequest.Marshal(b, m, deterministic)
}
func (dst *GetRedDotAlertStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRedDotAlertStatusRequest.Merge(dst, src)
}
func (m *GetRedDotAlertStatusRequest) XXX_Size() int {
	return xxx_messageInfo_GetRedDotAlertStatusRequest.Size(m)
}
func (m *GetRedDotAlertStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRedDotAlertStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRedDotAlertStatusRequest proto.InternalMessageInfo

func (m *GetRedDotAlertStatusRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetRedDotAlertStatusRequest) GetRedDotAlerType() uint32 {
	if m != nil {
		return m.RedDotAlerType
	}
	return 0
}

// Deprecated: Do not use.
func (m *GetRedDotAlertStatusRequest) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

// Deprecated: Do not use.
func (m *GetRedDotAlertStatusRequest) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

// 商品tab红点信息
type CommodityTabRedDotInfo struct {
	Category             uint32   `protobuf:"varint,1,opt,name=category,proto3" json:"category,omitempty"`
	SubCategory          uint32   `protobuf:"varint,2,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	HasRedDot            bool     `protobuf:"varint,3,opt,name=has_red_dot,json=hasRedDot,proto3" json:"has_red_dot,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommodityTabRedDotInfo) Reset()         { *m = CommodityTabRedDotInfo{} }
func (m *CommodityTabRedDotInfo) String() string { return proto.CompactTextString(m) }
func (*CommodityTabRedDotInfo) ProtoMessage()    {}
func (*CommodityTabRedDotInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{29}
}
func (m *CommodityTabRedDotInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommodityTabRedDotInfo.Unmarshal(m, b)
}
func (m *CommodityTabRedDotInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommodityTabRedDotInfo.Marshal(b, m, deterministic)
}
func (dst *CommodityTabRedDotInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommodityTabRedDotInfo.Merge(dst, src)
}
func (m *CommodityTabRedDotInfo) XXX_Size() int {
	return xxx_messageInfo_CommodityTabRedDotInfo.Size(m)
}
func (m *CommodityTabRedDotInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CommodityTabRedDotInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CommodityTabRedDotInfo proto.InternalMessageInfo

func (m *CommodityTabRedDotInfo) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *CommodityTabRedDotInfo) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

func (m *CommodityTabRedDotInfo) GetHasRedDot() bool {
	if m != nil {
		return m.HasRedDot
	}
	return false
}

// 获取虚拟形象红点状态
type GetRedDotAlertStatusResponse struct {
	BaseResp               *app.BaseResp             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RedDotAlerType         uint32                    `protobuf:"varint,2,opt,name=red_dot_aler_type,json=redDotAlerType,proto3" json:"red_dot_aler_type,omitempty"`
	HasRedDot              bool                      `protobuf:"varint,3,opt,name=has_red_dot,json=hasRedDot,proto3" json:"has_red_dot,omitempty"`
	CommodityTabRedDotList []*CommodityTabRedDotInfo `protobuf:"bytes,4,rep,name=commodity_tab_red_dot_list,json=commodityTabRedDotList,proto3" json:"commodity_tab_red_dot_list,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                  `json:"-"`
	XXX_unrecognized       []byte                    `json:"-"`
	XXX_sizecache          int32                     `json:"-"`
}

func (m *GetRedDotAlertStatusResponse) Reset()         { *m = GetRedDotAlertStatusResponse{} }
func (m *GetRedDotAlertStatusResponse) String() string { return proto.CompactTextString(m) }
func (*GetRedDotAlertStatusResponse) ProtoMessage()    {}
func (*GetRedDotAlertStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{30}
}
func (m *GetRedDotAlertStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRedDotAlertStatusResponse.Unmarshal(m, b)
}
func (m *GetRedDotAlertStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRedDotAlertStatusResponse.Marshal(b, m, deterministic)
}
func (dst *GetRedDotAlertStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRedDotAlertStatusResponse.Merge(dst, src)
}
func (m *GetRedDotAlertStatusResponse) XXX_Size() int {
	return xxx_messageInfo_GetRedDotAlertStatusResponse.Size(m)
}
func (m *GetRedDotAlertStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRedDotAlertStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRedDotAlertStatusResponse proto.InternalMessageInfo

func (m *GetRedDotAlertStatusResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRedDotAlertStatusResponse) GetRedDotAlerType() uint32 {
	if m != nil {
		return m.RedDotAlerType
	}
	return 0
}

func (m *GetRedDotAlertStatusResponse) GetHasRedDot() bool {
	if m != nil {
		return m.HasRedDot
	}
	return false
}

func (m *GetRedDotAlertStatusResponse) GetCommodityTabRedDotList() []*CommodityTabRedDotInfo {
	if m != nil {
		return m.CommodityTabRedDotList
	}
	return nil
}

// 红点已读
type RedDotAlertReadedRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	RedDotAlerType       uint32       `protobuf:"varint,2,opt,name=red_dot_aler_type,json=redDotAlerType,proto3" json:"red_dot_aler_type,omitempty"`
	Category             uint32       `protobuf:"varint,3,opt,name=category,proto3" json:"category,omitempty"`
	SubCategory          uint32       `protobuf:"varint,4,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RedDotAlertReadedRequest) Reset()         { *m = RedDotAlertReadedRequest{} }
func (m *RedDotAlertReadedRequest) String() string { return proto.CompactTextString(m) }
func (*RedDotAlertReadedRequest) ProtoMessage()    {}
func (*RedDotAlertReadedRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{31}
}
func (m *RedDotAlertReadedRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RedDotAlertReadedRequest.Unmarshal(m, b)
}
func (m *RedDotAlertReadedRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RedDotAlertReadedRequest.Marshal(b, m, deterministic)
}
func (dst *RedDotAlertReadedRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RedDotAlertReadedRequest.Merge(dst, src)
}
func (m *RedDotAlertReadedRequest) XXX_Size() int {
	return xxx_messageInfo_RedDotAlertReadedRequest.Size(m)
}
func (m *RedDotAlertReadedRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RedDotAlertReadedRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RedDotAlertReadedRequest proto.InternalMessageInfo

func (m *RedDotAlertReadedRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *RedDotAlertReadedRequest) GetRedDotAlerType() uint32 {
	if m != nil {
		return m.RedDotAlerType
	}
	return 0
}

func (m *RedDotAlertReadedRequest) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *RedDotAlertReadedRequest) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

type RedDotAlertReadedResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *RedDotAlertReadedResponse) Reset()         { *m = RedDotAlertReadedResponse{} }
func (m *RedDotAlertReadedResponse) String() string { return proto.CompactTextString(m) }
func (*RedDotAlertReadedResponse) ProtoMessage()    {}
func (*RedDotAlertReadedResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{32}
}
func (m *RedDotAlertReadedResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RedDotAlertReadedResponse.Unmarshal(m, b)
}
func (m *RedDotAlertReadedResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RedDotAlertReadedResponse.Marshal(b, m, deterministic)
}
func (dst *RedDotAlertReadedResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RedDotAlertReadedResponse.Merge(dst, src)
}
func (m *RedDotAlertReadedResponse) XXX_Size() int {
	return xxx_messageInfo_RedDotAlertReadedResponse.Size(m)
}
func (m *RedDotAlertReadedResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RedDotAlertReadedResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RedDotAlertReadedResponse proto.InternalMessageInfo

func (m *RedDotAlertReadedResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type UserVirtualImageItem struct {
	ResourceId           uint32   `protobuf:"varint,1,opt,name=resource_id,json=resourceId,proto3" json:"resource_id,omitempty"`
	ExpireTs             int64    `protobuf:"varint,2,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"`
	Inuse                bool     `protobuf:"varint,3,opt,name=inuse,proto3" json:"inuse,omitempty"`
	UpdateTs             int64    `protobuf:"varint,4,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	UseRightsType        uint32   `protobuf:"varint,5,opt,name=use_rights_type,json=useRightsType,proto3" json:"use_rights_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserVirtualImageItem) Reset()         { *m = UserVirtualImageItem{} }
func (m *UserVirtualImageItem) String() string { return proto.CompactTextString(m) }
func (*UserVirtualImageItem) ProtoMessage()    {}
func (*UserVirtualImageItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{33}
}
func (m *UserVirtualImageItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserVirtualImageItem.Unmarshal(m, b)
}
func (m *UserVirtualImageItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserVirtualImageItem.Marshal(b, m, deterministic)
}
func (dst *UserVirtualImageItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserVirtualImageItem.Merge(dst, src)
}
func (m *UserVirtualImageItem) XXX_Size() int {
	return xxx_messageInfo_UserVirtualImageItem.Size(m)
}
func (m *UserVirtualImageItem) XXX_DiscardUnknown() {
	xxx_messageInfo_UserVirtualImageItem.DiscardUnknown(m)
}

var xxx_messageInfo_UserVirtualImageItem proto.InternalMessageInfo

func (m *UserVirtualImageItem) GetResourceId() uint32 {
	if m != nil {
		return m.ResourceId
	}
	return 0
}

func (m *UserVirtualImageItem) GetExpireTs() int64 {
	if m != nil {
		return m.ExpireTs
	}
	return 0
}

func (m *UserVirtualImageItem) GetInuse() bool {
	if m != nil {
		return m.Inuse
	}
	return false
}

func (m *UserVirtualImageItem) GetUpdateTs() int64 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *UserVirtualImageItem) GetUseRightsType() uint32 {
	if m != nil {
		return m.UseRightsType
	}
	return 0
}

type VirtualImageItemTab struct {
	TabName              string                  `protobuf:"bytes,1,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	TabIcon              string                  `protobuf:"bytes,2,opt,name=tab_icon,json=tabIcon,proto3" json:"tab_icon,omitempty"`
	TabSelectIcon        string                  `protobuf:"bytes,3,opt,name=tab_select_icon,json=tabSelectIcon,proto3" json:"tab_select_icon,omitempty"`
	Items                []*UserVirtualImageItem `protobuf:"bytes,4,rep,name=items,proto3" json:"items,omitempty"`
	CategoryType         uint32                  `protobuf:"varint,5,opt,name=category_type,json=categoryType,proto3" json:"category_type,omitempty"`
	SubCategoryType      uint32                  `protobuf:"varint,6,opt,name=sub_category_type,json=subCategoryType,proto3" json:"sub_category_type,omitempty"`
	Category             uint32                  `protobuf:"varint,7,opt,name=category,proto3" json:"category,omitempty"`
	SubCategory          uint32                  `protobuf:"varint,8,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *VirtualImageItemTab) Reset()         { *m = VirtualImageItemTab{} }
func (m *VirtualImageItemTab) String() string { return proto.CompactTextString(m) }
func (*VirtualImageItemTab) ProtoMessage()    {}
func (*VirtualImageItemTab) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{34}
}
func (m *VirtualImageItemTab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImageItemTab.Unmarshal(m, b)
}
func (m *VirtualImageItemTab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImageItemTab.Marshal(b, m, deterministic)
}
func (dst *VirtualImageItemTab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImageItemTab.Merge(dst, src)
}
func (m *VirtualImageItemTab) XXX_Size() int {
	return xxx_messageInfo_VirtualImageItemTab.Size(m)
}
func (m *VirtualImageItemTab) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImageItemTab.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImageItemTab proto.InternalMessageInfo

func (m *VirtualImageItemTab) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *VirtualImageItemTab) GetTabIcon() string {
	if m != nil {
		return m.TabIcon
	}
	return ""
}

func (m *VirtualImageItemTab) GetTabSelectIcon() string {
	if m != nil {
		return m.TabSelectIcon
	}
	return ""
}

func (m *VirtualImageItemTab) GetItems() []*UserVirtualImageItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *VirtualImageItemTab) GetCategoryType() uint32 {
	if m != nil {
		return m.CategoryType
	}
	return 0
}

func (m *VirtualImageItemTab) GetSubCategoryType() uint32 {
	if m != nil {
		return m.SubCategoryType
	}
	return 0
}

func (m *VirtualImageItemTab) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *VirtualImageItemTab) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

// 用户的组件套装
type UserVirtualImageSuit struct {
	Name                 string                  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string                  `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	Items                []*UserVirtualImageItem `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	ExpireTs             int64                   `protobuf:"varint,4,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"`
	Inuse                bool                    `protobuf:"varint,5,opt,name=inuse,proto3" json:"inuse,omitempty"`
	UpdateTs             int64                   `protobuf:"varint,6,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	LevelIcon            string                  `protobuf:"bytes,7,opt,name=level_icon,json=levelIcon,proto3" json:"level_icon,omitempty"`
	SuitId               uint32                  `protobuf:"varint,8,opt,name=suit_id,json=suitId,proto3" json:"suit_id,omitempty"`
	PromotionResourceId  uint32                  `protobuf:"varint,9,opt,name=promotion_resource_id,json=promotionResourceId,proto3" json:"promotion_resource_id,omitempty"`
	SuitUniqueId         string                  `protobuf:"bytes,10,opt,name=suit_unique_id,json=suitUniqueId,proto3" json:"suit_unique_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *UserVirtualImageSuit) Reset()         { *m = UserVirtualImageSuit{} }
func (m *UserVirtualImageSuit) String() string { return proto.CompactTextString(m) }
func (*UserVirtualImageSuit) ProtoMessage()    {}
func (*UserVirtualImageSuit) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{35}
}
func (m *UserVirtualImageSuit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserVirtualImageSuit.Unmarshal(m, b)
}
func (m *UserVirtualImageSuit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserVirtualImageSuit.Marshal(b, m, deterministic)
}
func (dst *UserVirtualImageSuit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserVirtualImageSuit.Merge(dst, src)
}
func (m *UserVirtualImageSuit) XXX_Size() int {
	return xxx_messageInfo_UserVirtualImageSuit.Size(m)
}
func (m *UserVirtualImageSuit) XXX_DiscardUnknown() {
	xxx_messageInfo_UserVirtualImageSuit.DiscardUnknown(m)
}

var xxx_messageInfo_UserVirtualImageSuit proto.InternalMessageInfo

func (m *UserVirtualImageSuit) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UserVirtualImageSuit) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *UserVirtualImageSuit) GetItems() []*UserVirtualImageItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *UserVirtualImageSuit) GetExpireTs() int64 {
	if m != nil {
		return m.ExpireTs
	}
	return 0
}

func (m *UserVirtualImageSuit) GetInuse() bool {
	if m != nil {
		return m.Inuse
	}
	return false
}

func (m *UserVirtualImageSuit) GetUpdateTs() int64 {
	if m != nil {
		return m.UpdateTs
	}
	return 0
}

func (m *UserVirtualImageSuit) GetLevelIcon() string {
	if m != nil {
		return m.LevelIcon
	}
	return ""
}

func (m *UserVirtualImageSuit) GetSuitId() uint32 {
	if m != nil {
		return m.SuitId
	}
	return 0
}

func (m *UserVirtualImageSuit) GetPromotionResourceId() uint32 {
	if m != nil {
		return m.PromotionResourceId
	}
	return 0
}

func (m *UserVirtualImageSuit) GetSuitUniqueId() string {
	if m != nil {
		return m.SuitUniqueId
	}
	return ""
}

type VirtualImageSuitTab struct {
	TabName              string                  `protobuf:"bytes,1,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	TabIcon              string                  `protobuf:"bytes,2,opt,name=tab_icon,json=tabIcon,proto3" json:"tab_icon,omitempty"`
	TabSelectIcon        string                  `protobuf:"bytes,3,opt,name=tab_select_icon,json=tabSelectIcon,proto3" json:"tab_select_icon,omitempty"`
	Suits                []*UserVirtualImageSuit `protobuf:"bytes,4,rep,name=suits,proto3" json:"suits,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *VirtualImageSuitTab) Reset()         { *m = VirtualImageSuitTab{} }
func (m *VirtualImageSuitTab) String() string { return proto.CompactTextString(m) }
func (*VirtualImageSuitTab) ProtoMessage()    {}
func (*VirtualImageSuitTab) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{36}
}
func (m *VirtualImageSuitTab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImageSuitTab.Unmarshal(m, b)
}
func (m *VirtualImageSuitTab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImageSuitTab.Marshal(b, m, deterministic)
}
func (dst *VirtualImageSuitTab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImageSuitTab.Merge(dst, src)
}
func (m *VirtualImageSuitTab) XXX_Size() int {
	return xxx_messageInfo_VirtualImageSuitTab.Size(m)
}
func (m *VirtualImageSuitTab) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImageSuitTab.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImageSuitTab proto.InternalMessageInfo

func (m *VirtualImageSuitTab) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *VirtualImageSuitTab) GetTabIcon() string {
	if m != nil {
		return m.TabIcon
	}
	return ""
}

func (m *VirtualImageSuitTab) GetTabSelectIcon() string {
	if m != nil {
		return m.TabSelectIcon
	}
	return ""
}

func (m *VirtualImageSuitTab) GetSuits() []*UserVirtualImageSuit {
	if m != nil {
		return m.Suits
	}
	return nil
}

type VirtualImageAllTabCfg struct {
	TabName              string   `protobuf:"bytes,1,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	TabIcon              string   `protobuf:"bytes,2,opt,name=tab_icon,json=tabIcon,proto3" json:"tab_icon,omitempty"`
	TabSelectIcon        string   `protobuf:"bytes,3,opt,name=tab_select_icon,json=tabSelectIcon,proto3" json:"tab_select_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VirtualImageAllTabCfg) Reset()         { *m = VirtualImageAllTabCfg{} }
func (m *VirtualImageAllTabCfg) String() string { return proto.CompactTextString(m) }
func (*VirtualImageAllTabCfg) ProtoMessage()    {}
func (*VirtualImageAllTabCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{37}
}
func (m *VirtualImageAllTabCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImageAllTabCfg.Unmarshal(m, b)
}
func (m *VirtualImageAllTabCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImageAllTabCfg.Marshal(b, m, deterministic)
}
func (dst *VirtualImageAllTabCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImageAllTabCfg.Merge(dst, src)
}
func (m *VirtualImageAllTabCfg) XXX_Size() int {
	return xxx_messageInfo_VirtualImageAllTabCfg.Size(m)
}
func (m *VirtualImageAllTabCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImageAllTabCfg.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImageAllTabCfg proto.InternalMessageInfo

func (m *VirtualImageAllTabCfg) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *VirtualImageAllTabCfg) GetTabIcon() string {
	if m != nil {
		return m.TabIcon
	}
	return ""
}

func (m *VirtualImageAllTabCfg) GetTabSelectIcon() string {
	if m != nil {
		return m.TabSelectIcon
	}
	return ""
}

// 获取用户拥有的虚拟形象
type GetUserVirtualImageRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserVirtualImageRequest) Reset()         { *m = GetUserVirtualImageRequest{} }
func (m *GetUserVirtualImageRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserVirtualImageRequest) ProtoMessage()    {}
func (*GetUserVirtualImageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{38}
}
func (m *GetUserVirtualImageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserVirtualImageRequest.Unmarshal(m, b)
}
func (m *GetUserVirtualImageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserVirtualImageRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserVirtualImageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserVirtualImageRequest.Merge(dst, src)
}
func (m *GetUserVirtualImageRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserVirtualImageRequest.Size(m)
}
func (m *GetUserVirtualImageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserVirtualImageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserVirtualImageRequest proto.InternalMessageInfo

func (m *GetUserVirtualImageRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetUserVirtualImageResponse struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ItemTabs             []*VirtualImageItemTab  `protobuf:"bytes,2,rep,name=item_tabs,json=itemTabs,proto3" json:"item_tabs,omitempty"`
	SuitTab              *VirtualImageSuitTab    `protobuf:"bytes,3,opt,name=suit_tab,json=suitTab,proto3" json:"suit_tab,omitempty"`
	LastUpdateTs         int64                   `protobuf:"varint,4,opt,name=last_update_ts,json=lastUpdateTs,proto3" json:"last_update_ts,omitempty"`
	AllTabCfg            *VirtualImageAllTabCfg  `protobuf:"bytes,5,opt,name=all_tab_cfg,json=allTabCfg,proto3" json:"all_tab_cfg,omitempty"`
	InuseRightsItems     []*UserVirtualImageItem `protobuf:"bytes,6,rep,name=inuse_rights_items,json=inuseRightsItems,proto3" json:"inuse_rights_items,omitempty"`
	InuseResourceIds     []uint32                `protobuf:"varint,7,rep,packed,name=inuse_resource_ids,json=inuseResourceIds,proto3" json:"inuse_resource_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetUserVirtualImageResponse) Reset()         { *m = GetUserVirtualImageResponse{} }
func (m *GetUserVirtualImageResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserVirtualImageResponse) ProtoMessage()    {}
func (*GetUserVirtualImageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{39}
}
func (m *GetUserVirtualImageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserVirtualImageResponse.Unmarshal(m, b)
}
func (m *GetUserVirtualImageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserVirtualImageResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserVirtualImageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserVirtualImageResponse.Merge(dst, src)
}
func (m *GetUserVirtualImageResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserVirtualImageResponse.Size(m)
}
func (m *GetUserVirtualImageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserVirtualImageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserVirtualImageResponse proto.InternalMessageInfo

func (m *GetUserVirtualImageResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserVirtualImageResponse) GetItemTabs() []*VirtualImageItemTab {
	if m != nil {
		return m.ItemTabs
	}
	return nil
}

func (m *GetUserVirtualImageResponse) GetSuitTab() *VirtualImageSuitTab {
	if m != nil {
		return m.SuitTab
	}
	return nil
}

func (m *GetUserVirtualImageResponse) GetLastUpdateTs() int64 {
	if m != nil {
		return m.LastUpdateTs
	}
	return 0
}

func (m *GetUserVirtualImageResponse) GetAllTabCfg() *VirtualImageAllTabCfg {
	if m != nil {
		return m.AllTabCfg
	}
	return nil
}

func (m *GetUserVirtualImageResponse) GetInuseRightsItems() []*UserVirtualImageItem {
	if m != nil {
		return m.InuseRightsItems
	}
	return nil
}

func (m *GetUserVirtualImageResponse) GetInuseResourceIds() []uint32 {
	if m != nil {
		return m.InuseResourceIds
	}
	return nil
}

// 获取用户的虚拟形象姿势组件
type GetUserVirtualImagePoseRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserVirtualImagePoseRequest) Reset()         { *m = GetUserVirtualImagePoseRequest{} }
func (m *GetUserVirtualImagePoseRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserVirtualImagePoseRequest) ProtoMessage()    {}
func (*GetUserVirtualImagePoseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{40}
}
func (m *GetUserVirtualImagePoseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserVirtualImagePoseRequest.Unmarshal(m, b)
}
func (m *GetUserVirtualImagePoseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserVirtualImagePoseRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserVirtualImagePoseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserVirtualImagePoseRequest.Merge(dst, src)
}
func (m *GetUserVirtualImagePoseRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserVirtualImagePoseRequest.Size(m)
}
func (m *GetUserVirtualImagePoseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserVirtualImagePoseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserVirtualImagePoseRequest proto.InternalMessageInfo

func (m *GetUserVirtualImagePoseRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetUserVirtualImagePoseResponse struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Items                []*UserVirtualImageItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	Orientation          uint32                  `protobuf:"varint,3,opt,name=orientation,proto3" json:"orientation,omitempty"`
	JumpUrl              string                  `protobuf:"bytes,4,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	SubCategory          uint32                  `protobuf:"varint,5,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetUserVirtualImagePoseResponse) Reset()         { *m = GetUserVirtualImagePoseResponse{} }
func (m *GetUserVirtualImagePoseResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserVirtualImagePoseResponse) ProtoMessage()    {}
func (*GetUserVirtualImagePoseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{41}
}
func (m *GetUserVirtualImagePoseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserVirtualImagePoseResponse.Unmarshal(m, b)
}
func (m *GetUserVirtualImagePoseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserVirtualImagePoseResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserVirtualImagePoseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserVirtualImagePoseResponse.Merge(dst, src)
}
func (m *GetUserVirtualImagePoseResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserVirtualImagePoseResponse.Size(m)
}
func (m *GetUserVirtualImagePoseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserVirtualImagePoseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserVirtualImagePoseResponse proto.InternalMessageInfo

func (m *GetUserVirtualImagePoseResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserVirtualImagePoseResponse) GetItems() []*UserVirtualImageItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *GetUserVirtualImagePoseResponse) GetOrientation() uint32 {
	if m != nil {
		return m.Orientation
	}
	return 0
}

func (m *GetUserVirtualImagePoseResponse) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *GetUserVirtualImagePoseResponse) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

type UserVirtualImageInuse struct {
	Uid                  uint32                  `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Items                []*UserVirtualImageItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	Orientation          uint32                  `protobuf:"varint,3,opt,name=orientation,proto3" json:"orientation,omitempty"`
	PoseType             uint32                  `protobuf:"varint,4,opt,name=pose_type,json=poseType,proto3" json:"pose_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *UserVirtualImageInuse) Reset()         { *m = UserVirtualImageInuse{} }
func (m *UserVirtualImageInuse) String() string { return proto.CompactTextString(m) }
func (*UserVirtualImageInuse) ProtoMessage()    {}
func (*UserVirtualImageInuse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{42}
}
func (m *UserVirtualImageInuse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserVirtualImageInuse.Unmarshal(m, b)
}
func (m *UserVirtualImageInuse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserVirtualImageInuse.Marshal(b, m, deterministic)
}
func (dst *UserVirtualImageInuse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserVirtualImageInuse.Merge(dst, src)
}
func (m *UserVirtualImageInuse) XXX_Size() int {
	return xxx_messageInfo_UserVirtualImageInuse.Size(m)
}
func (m *UserVirtualImageInuse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserVirtualImageInuse.DiscardUnknown(m)
}

var xxx_messageInfo_UserVirtualImageInuse proto.InternalMessageInfo

func (m *UserVirtualImageInuse) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserVirtualImageInuse) GetItems() []*UserVirtualImageItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *UserVirtualImageInuse) GetOrientation() uint32 {
	if m != nil {
		return m.Orientation
	}
	return 0
}

func (m *UserVirtualImageInuse) GetPoseType() uint32 {
	if m != nil {
		return m.PoseType
	}
	return 0
}

// 用户的虚拟形象变更通知
type UserVirtualImageChangeOpt struct {
	Uid                  uint32                  `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32                  `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	Items                []*UserVirtualImageItem `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	Orientation          uint32                  `protobuf:"varint,4,opt,name=orientation,proto3" json:"orientation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *UserVirtualImageChangeOpt) Reset()         { *m = UserVirtualImageChangeOpt{} }
func (m *UserVirtualImageChangeOpt) String() string { return proto.CompactTextString(m) }
func (*UserVirtualImageChangeOpt) ProtoMessage()    {}
func (*UserVirtualImageChangeOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{43}
}
func (m *UserVirtualImageChangeOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserVirtualImageChangeOpt.Unmarshal(m, b)
}
func (m *UserVirtualImageChangeOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserVirtualImageChangeOpt.Marshal(b, m, deterministic)
}
func (dst *UserVirtualImageChangeOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserVirtualImageChangeOpt.Merge(dst, src)
}
func (m *UserVirtualImageChangeOpt) XXX_Size() int {
	return xxx_messageInfo_UserVirtualImageChangeOpt.Size(m)
}
func (m *UserVirtualImageChangeOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_UserVirtualImageChangeOpt.DiscardUnknown(m)
}

var xxx_messageInfo_UserVirtualImageChangeOpt proto.InternalMessageInfo

func (m *UserVirtualImageChangeOpt) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserVirtualImageChangeOpt) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *UserVirtualImageChangeOpt) GetItems() []*UserVirtualImageItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *UserVirtualImageChangeOpt) GetOrientation() uint32 {
	if m != nil {
		return m.Orientation
	}
	return 0
}

// 批量获取用户正在使用的虚拟形象
type BatchGetUserVirtualImageInuseRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	UidList              []uint32     `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Scope                uint32       `protobuf:"varint,3,opt,name=scope,proto3" json:"scope,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatchGetUserVirtualImageInuseRequest) Reset()         { *m = BatchGetUserVirtualImageInuseRequest{} }
func (m *BatchGetUserVirtualImageInuseRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserVirtualImageInuseRequest) ProtoMessage()    {}
func (*BatchGetUserVirtualImageInuseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{44}
}
func (m *BatchGetUserVirtualImageInuseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserVirtualImageInuseRequest.Unmarshal(m, b)
}
func (m *BatchGetUserVirtualImageInuseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserVirtualImageInuseRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserVirtualImageInuseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserVirtualImageInuseRequest.Merge(dst, src)
}
func (m *BatchGetUserVirtualImageInuseRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserVirtualImageInuseRequest.Size(m)
}
func (m *BatchGetUserVirtualImageInuseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserVirtualImageInuseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserVirtualImageInuseRequest proto.InternalMessageInfo

func (m *BatchGetUserVirtualImageInuseRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BatchGetUserVirtualImageInuseRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetUserVirtualImageInuseRequest) GetScope() uint32 {
	if m != nil {
		return m.Scope
	}
	return 0
}

type BatchGetUserVirtualImageInuseResponse struct {
	BaseResp             *app.BaseResp            `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UseList              []*UserVirtualImageInuse `protobuf:"bytes,2,rep,name=use_list,json=useList,proto3" json:"use_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *BatchGetUserVirtualImageInuseResponse) Reset()         { *m = BatchGetUserVirtualImageInuseResponse{} }
func (m *BatchGetUserVirtualImageInuseResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserVirtualImageInuseResponse) ProtoMessage()    {}
func (*BatchGetUserVirtualImageInuseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{45}
}
func (m *BatchGetUserVirtualImageInuseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserVirtualImageInuseResponse.Unmarshal(m, b)
}
func (m *BatchGetUserVirtualImageInuseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserVirtualImageInuseResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserVirtualImageInuseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserVirtualImageInuseResponse.Merge(dst, src)
}
func (m *BatchGetUserVirtualImageInuseResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserVirtualImageInuseResponse.Size(m)
}
func (m *BatchGetUserVirtualImageInuseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserVirtualImageInuseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserVirtualImageInuseResponse proto.InternalMessageInfo

func (m *BatchGetUserVirtualImageInuseResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *BatchGetUserVirtualImageInuseResponse) GetUseList() []*UserVirtualImageInuse {
	if m != nil {
		return m.UseList
	}
	return nil
}

// 获取用户正在使用的虚拟形象
type GetUserVirtualImageDisplayRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	Scope                uint32       `protobuf:"varint,3,opt,name=scope,proto3" json:"scope,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserVirtualImageDisplayRequest) Reset()         { *m = GetUserVirtualImageDisplayRequest{} }
func (m *GetUserVirtualImageDisplayRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserVirtualImageDisplayRequest) ProtoMessage()    {}
func (*GetUserVirtualImageDisplayRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{46}
}
func (m *GetUserVirtualImageDisplayRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserVirtualImageDisplayRequest.Unmarshal(m, b)
}
func (m *GetUserVirtualImageDisplayRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserVirtualImageDisplayRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserVirtualImageDisplayRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserVirtualImageDisplayRequest.Merge(dst, src)
}
func (m *GetUserVirtualImageDisplayRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserVirtualImageDisplayRequest.Size(m)
}
func (m *GetUserVirtualImageDisplayRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserVirtualImageDisplayRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserVirtualImageDisplayRequest proto.InternalMessageInfo

func (m *GetUserVirtualImageDisplayRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserVirtualImageDisplayRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetUserVirtualImageDisplayRequest) GetScope() uint32 {
	if m != nil {
		return m.Scope
	}
	return 0
}

type GetUserVirtualImageDisplayResponse struct {
	BaseResp             *app.BaseResp                    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	InuseInfo            *UserVirtualImageInuse           `protobuf:"bytes,2,opt,name=inuse_info,json=inuseInfo,proto3" json:"inuse_info,omitempty"`
	UserProfile          *app.UserProfile                 `protobuf:"bytes,3,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	SwitchInfo           []*VirtualImageDisplaySwitchInfo `protobuf:"bytes,4,rep,name=switch_info,json=switchInfo,proto3" json:"switch_info,omitempty"`
	RelationInuseInfo    *UserVirtualImageInuse           `protobuf:"bytes,5,opt,name=relation_inuse_info,json=relationInuseInfo,proto3" json:"relation_inuse_info,omitempty"`
	RelationUserProfile  *app.UserProfile                 `protobuf:"bytes,6,opt,name=relation_user_profile,json=relationUserProfile,proto3" json:"relation_user_profile,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *GetUserVirtualImageDisplayResponse) Reset()         { *m = GetUserVirtualImageDisplayResponse{} }
func (m *GetUserVirtualImageDisplayResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserVirtualImageDisplayResponse) ProtoMessage()    {}
func (*GetUserVirtualImageDisplayResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{47}
}
func (m *GetUserVirtualImageDisplayResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserVirtualImageDisplayResponse.Unmarshal(m, b)
}
func (m *GetUserVirtualImageDisplayResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserVirtualImageDisplayResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserVirtualImageDisplayResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserVirtualImageDisplayResponse.Merge(dst, src)
}
func (m *GetUserVirtualImageDisplayResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserVirtualImageDisplayResponse.Size(m)
}
func (m *GetUserVirtualImageDisplayResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserVirtualImageDisplayResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserVirtualImageDisplayResponse proto.InternalMessageInfo

func (m *GetUserVirtualImageDisplayResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserVirtualImageDisplayResponse) GetInuseInfo() *UserVirtualImageInuse {
	if m != nil {
		return m.InuseInfo
	}
	return nil
}

func (m *GetUserVirtualImageDisplayResponse) GetUserProfile() *app.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *GetUserVirtualImageDisplayResponse) GetSwitchInfo() []*VirtualImageDisplaySwitchInfo {
	if m != nil {
		return m.SwitchInfo
	}
	return nil
}

func (m *GetUserVirtualImageDisplayResponse) GetRelationInuseInfo() *UserVirtualImageInuse {
	if m != nil {
		return m.RelationInuseInfo
	}
	return nil
}

func (m *GetUserVirtualImageDisplayResponse) GetRelationUserProfile() *app.UserProfile {
	if m != nil {
		return m.RelationUserProfile
	}
	return nil
}

type SetUseItem struct {
	SubCategory          uint32   `protobuf:"varint,1,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	ResourceId           uint32   `protobuf:"varint,2,opt,name=resource_id,json=resourceId,proto3" json:"resource_id,omitempty"`
	RightsType           uint32   `protobuf:"varint,3,opt,name=rights_type,json=rightsType,proto3" json:"rights_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUseItem) Reset()         { *m = SetUseItem{} }
func (m *SetUseItem) String() string { return proto.CompactTextString(m) }
func (*SetUseItem) ProtoMessage()    {}
func (*SetUseItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{48}
}
func (m *SetUseItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUseItem.Unmarshal(m, b)
}
func (m *SetUseItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUseItem.Marshal(b, m, deterministic)
}
func (dst *SetUseItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUseItem.Merge(dst, src)
}
func (m *SetUseItem) XXX_Size() int {
	return xxx_messageInfo_SetUseItem.Size(m)
}
func (m *SetUseItem) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUseItem.DiscardUnknown(m)
}

var xxx_messageInfo_SetUseItem proto.InternalMessageInfo

func (m *SetUseItem) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

func (m *SetUseItem) GetResourceId() uint32 {
	if m != nil {
		return m.ResourceId
	}
	return 0
}

func (m *SetUseItem) GetRightsType() uint32 {
	if m != nil {
		return m.RightsType
	}
	return 0
}

// 设置用户使用的虚拟形象
type SetUserVirtualImageInuseRequest struct {
	BaseReq              *app.BaseReq  `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	UseItems             []*SetUseItem `protobuf:"bytes,2,rep,name=use_items,json=useItems,proto3" json:"use_items,omitempty"`
	IncrementalUpdate    bool          `protobuf:"varint,3,opt,name=incremental_update,json=incrementalUpdate,proto3" json:"incremental_update,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetUserVirtualImageInuseRequest) Reset()         { *m = SetUserVirtualImageInuseRequest{} }
func (m *SetUserVirtualImageInuseRequest) String() string { return proto.CompactTextString(m) }
func (*SetUserVirtualImageInuseRequest) ProtoMessage()    {}
func (*SetUserVirtualImageInuseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{49}
}
func (m *SetUserVirtualImageInuseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserVirtualImageInuseRequest.Unmarshal(m, b)
}
func (m *SetUserVirtualImageInuseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserVirtualImageInuseRequest.Marshal(b, m, deterministic)
}
func (dst *SetUserVirtualImageInuseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserVirtualImageInuseRequest.Merge(dst, src)
}
func (m *SetUserVirtualImageInuseRequest) XXX_Size() int {
	return xxx_messageInfo_SetUserVirtualImageInuseRequest.Size(m)
}
func (m *SetUserVirtualImageInuseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserVirtualImageInuseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserVirtualImageInuseRequest proto.InternalMessageInfo

func (m *SetUserVirtualImageInuseRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetUserVirtualImageInuseRequest) GetUseItems() []*SetUseItem {
	if m != nil {
		return m.UseItems
	}
	return nil
}

func (m *SetUserVirtualImageInuseRequest) GetIncrementalUpdate() bool {
	if m != nil {
		return m.IncrementalUpdate
	}
	return false
}

type SetUserVirtualImageInuseResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetUserVirtualImageInuseResponse) Reset()         { *m = SetUserVirtualImageInuseResponse{} }
func (m *SetUserVirtualImageInuseResponse) String() string { return proto.CompactTextString(m) }
func (*SetUserVirtualImageInuseResponse) ProtoMessage()    {}
func (*SetUserVirtualImageInuseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{50}
}
func (m *SetUserVirtualImageInuseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserVirtualImageInuseResponse.Unmarshal(m, b)
}
func (m *SetUserVirtualImageInuseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserVirtualImageInuseResponse.Marshal(b, m, deterministic)
}
func (dst *SetUserVirtualImageInuseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserVirtualImageInuseResponse.Merge(dst, src)
}
func (m *SetUserVirtualImageInuseResponse) XXX_Size() int {
	return xxx_messageInfo_SetUserVirtualImageInuseResponse.Size(m)
}
func (m *SetUserVirtualImageInuseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserVirtualImageInuseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserVirtualImageInuseResponse proto.InternalMessageInfo

func (m *SetUserVirtualImageInuseResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 设置用户虚拟形象朝向
type SetUserVirtualImageOrientationRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Orientation          uint32       `protobuf:"varint,2,opt,name=orientation,proto3" json:"orientation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetUserVirtualImageOrientationRequest) Reset()         { *m = SetUserVirtualImageOrientationRequest{} }
func (m *SetUserVirtualImageOrientationRequest) String() string { return proto.CompactTextString(m) }
func (*SetUserVirtualImageOrientationRequest) ProtoMessage()    {}
func (*SetUserVirtualImageOrientationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{51}
}
func (m *SetUserVirtualImageOrientationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserVirtualImageOrientationRequest.Unmarshal(m, b)
}
func (m *SetUserVirtualImageOrientationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserVirtualImageOrientationRequest.Marshal(b, m, deterministic)
}
func (dst *SetUserVirtualImageOrientationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserVirtualImageOrientationRequest.Merge(dst, src)
}
func (m *SetUserVirtualImageOrientationRequest) XXX_Size() int {
	return xxx_messageInfo_SetUserVirtualImageOrientationRequest.Size(m)
}
func (m *SetUserVirtualImageOrientationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserVirtualImageOrientationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserVirtualImageOrientationRequest proto.InternalMessageInfo

func (m *SetUserVirtualImageOrientationRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetUserVirtualImageOrientationRequest) GetOrientation() uint32 {
	if m != nil {
		return m.Orientation
	}
	return 0
}

type SetUserVirtualImageOrientationResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetUserVirtualImageOrientationResponse) Reset() {
	*m = SetUserVirtualImageOrientationResponse{}
}
func (m *SetUserVirtualImageOrientationResponse) String() string { return proto.CompactTextString(m) }
func (*SetUserVirtualImageOrientationResponse) ProtoMessage()    {}
func (*SetUserVirtualImageOrientationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{52}
}
func (m *SetUserVirtualImageOrientationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserVirtualImageOrientationResponse.Unmarshal(m, b)
}
func (m *SetUserVirtualImageOrientationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserVirtualImageOrientationResponse.Marshal(b, m, deterministic)
}
func (dst *SetUserVirtualImageOrientationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserVirtualImageOrientationResponse.Merge(dst, src)
}
func (m *SetUserVirtualImageOrientationResponse) XXX_Size() int {
	return xxx_messageInfo_SetUserVirtualImageOrientationResponse.Size(m)
}
func (m *SetUserVirtualImageOrientationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserVirtualImageOrientationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserVirtualImageOrientationResponse proto.InternalMessageInfo

func (m *SetUserVirtualImageOrientationResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type VirtualImageDisplaySwitchInfo struct {
	SwitchType           uint32   `protobuf:"varint,1,opt,name=switch_type,json=switchType,proto3" json:"switch_type,omitempty"`
	SwitchStatus         bool     `protobuf:"varint,2,opt,name=switch_status,json=switchStatus,proto3" json:"switch_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VirtualImageDisplaySwitchInfo) Reset()         { *m = VirtualImageDisplaySwitchInfo{} }
func (m *VirtualImageDisplaySwitchInfo) String() string { return proto.CompactTextString(m) }
func (*VirtualImageDisplaySwitchInfo) ProtoMessage()    {}
func (*VirtualImageDisplaySwitchInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{53}
}
func (m *VirtualImageDisplaySwitchInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImageDisplaySwitchInfo.Unmarshal(m, b)
}
func (m *VirtualImageDisplaySwitchInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImageDisplaySwitchInfo.Marshal(b, m, deterministic)
}
func (dst *VirtualImageDisplaySwitchInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImageDisplaySwitchInfo.Merge(dst, src)
}
func (m *VirtualImageDisplaySwitchInfo) XXX_Size() int {
	return xxx_messageInfo_VirtualImageDisplaySwitchInfo.Size(m)
}
func (m *VirtualImageDisplaySwitchInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImageDisplaySwitchInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImageDisplaySwitchInfo proto.InternalMessageInfo

func (m *VirtualImageDisplaySwitchInfo) GetSwitchType() uint32 {
	if m != nil {
		return m.SwitchType
	}
	return 0
}

func (m *VirtualImageDisplaySwitchInfo) GetSwitchStatus() bool {
	if m != nil {
		return m.SwitchStatus
	}
	return false
}

// 设置外显开关状态
type SetVirtualImageDisplaySwitchRequest struct {
	BaseReq              *app.BaseReq                   `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SwitchInfo           *VirtualImageDisplaySwitchInfo `protobuf:"bytes,2,opt,name=switch_info,json=switchInfo,proto3" json:"switch_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *SetVirtualImageDisplaySwitchRequest) Reset()         { *m = SetVirtualImageDisplaySwitchRequest{} }
func (m *SetVirtualImageDisplaySwitchRequest) String() string { return proto.CompactTextString(m) }
func (*SetVirtualImageDisplaySwitchRequest) ProtoMessage()    {}
func (*SetVirtualImageDisplaySwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{54}
}
func (m *SetVirtualImageDisplaySwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVirtualImageDisplaySwitchRequest.Unmarshal(m, b)
}
func (m *SetVirtualImageDisplaySwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVirtualImageDisplaySwitchRequest.Marshal(b, m, deterministic)
}
func (dst *SetVirtualImageDisplaySwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVirtualImageDisplaySwitchRequest.Merge(dst, src)
}
func (m *SetVirtualImageDisplaySwitchRequest) XXX_Size() int {
	return xxx_messageInfo_SetVirtualImageDisplaySwitchRequest.Size(m)
}
func (m *SetVirtualImageDisplaySwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVirtualImageDisplaySwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetVirtualImageDisplaySwitchRequest proto.InternalMessageInfo

func (m *SetVirtualImageDisplaySwitchRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetVirtualImageDisplaySwitchRequest) GetSwitchInfo() *VirtualImageDisplaySwitchInfo {
	if m != nil {
		return m.SwitchInfo
	}
	return nil
}

type SetVirtualImageDisplaySwitchResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetVirtualImageDisplaySwitchResponse) Reset()         { *m = SetVirtualImageDisplaySwitchResponse{} }
func (m *SetVirtualImageDisplaySwitchResponse) String() string { return proto.CompactTextString(m) }
func (*SetVirtualImageDisplaySwitchResponse) ProtoMessage()    {}
func (*SetVirtualImageDisplaySwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{55}
}
func (m *SetVirtualImageDisplaySwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVirtualImageDisplaySwitchResponse.Unmarshal(m, b)
}
func (m *SetVirtualImageDisplaySwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVirtualImageDisplaySwitchResponse.Marshal(b, m, deterministic)
}
func (dst *SetVirtualImageDisplaySwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVirtualImageDisplaySwitchResponse.Merge(dst, src)
}
func (m *SetVirtualImageDisplaySwitchResponse) XXX_Size() int {
	return xxx_messageInfo_SetVirtualImageDisplaySwitchResponse.Size(m)
}
func (m *SetVirtualImageDisplaySwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVirtualImageDisplaySwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetVirtualImageDisplaySwitchResponse proto.InternalMessageInfo

func (m *SetVirtualImageDisplaySwitchResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type UserRelationInfo struct {
	RelationUidInUse     uint32             `protobuf:"varint,1,opt,name=relation_uid_in_use,json=relationUidInUse,proto3" json:"relation_uid_in_use,omitempty"`
	BindList             []*app.UserProfile `protobuf:"bytes,2,rep,name=bind_list,json=bindList,proto3" json:"bind_list,omitempty"`
	InvitingList         []*app.UserProfile `protobuf:"bytes,3,rep,name=inviting_list,json=invitingList,proto3" json:"inviting_list,omitempty"`
	MaxBindCount         uint32             `protobuf:"varint,4,opt,name=max_bind_count,json=maxBindCount,proto3" json:"max_bind_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UserRelationInfo) Reset()         { *m = UserRelationInfo{} }
func (m *UserRelationInfo) String() string { return proto.CompactTextString(m) }
func (*UserRelationInfo) ProtoMessage()    {}
func (*UserRelationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{56}
}
func (m *UserRelationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRelationInfo.Unmarshal(m, b)
}
func (m *UserRelationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRelationInfo.Marshal(b, m, deterministic)
}
func (dst *UserRelationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRelationInfo.Merge(dst, src)
}
func (m *UserRelationInfo) XXX_Size() int {
	return xxx_messageInfo_UserRelationInfo.Size(m)
}
func (m *UserRelationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRelationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserRelationInfo proto.InternalMessageInfo

func (m *UserRelationInfo) GetRelationUidInUse() uint32 {
	if m != nil {
		return m.RelationUidInUse
	}
	return 0
}

func (m *UserRelationInfo) GetBindList() []*app.UserProfile {
	if m != nil {
		return m.BindList
	}
	return nil
}

func (m *UserRelationInfo) GetInvitingList() []*app.UserProfile {
	if m != nil {
		return m.InvitingList
	}
	return nil
}

func (m *UserRelationInfo) GetMaxBindCount() uint32 {
	if m != nil {
		return m.MaxBindCount
	}
	return 0
}

// 获取用户的外显设置
type GetVirtualImageDisplayCfgRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetVirtualImageDisplayCfgRequest) Reset()         { *m = GetVirtualImageDisplayCfgRequest{} }
func (m *GetVirtualImageDisplayCfgRequest) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageDisplayCfgRequest) ProtoMessage()    {}
func (*GetVirtualImageDisplayCfgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{57}
}
func (m *GetVirtualImageDisplayCfgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageDisplayCfgRequest.Unmarshal(m, b)
}
func (m *GetVirtualImageDisplayCfgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageDisplayCfgRequest.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageDisplayCfgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageDisplayCfgRequest.Merge(dst, src)
}
func (m *GetVirtualImageDisplayCfgRequest) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageDisplayCfgRequest.Size(m)
}
func (m *GetVirtualImageDisplayCfgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageDisplayCfgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageDisplayCfgRequest proto.InternalMessageInfo

func (m *GetVirtualImageDisplayCfgRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetVirtualImageDisplayCfgResponse struct {
	BaseResp               *app.BaseResp                    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SwitchInfo             []*VirtualImageDisplaySwitchInfo `protobuf:"bytes,2,rep,name=switch_info,json=switchInfo,proto3" json:"switch_info,omitempty"`
	UserRelationInfo       *UserRelationInfo                `protobuf:"bytes,3,opt,name=user_relation_info,json=userRelationInfo,proto3" json:"user_relation_info,omitempty"`
	AutoPlaySec            uint32                           `protobuf:"varint,4,opt,name=auto_play_sec,json=autoPlaySec,proto3" json:"auto_play_sec,omitempty"`
	MyExpireRemindText     string                           `protobuf:"bytes,5,opt,name=my_expire_remind_text,json=myExpireRemindText,proto3" json:"my_expire_remind_text,omitempty"`
	TargetExpireRemindText string                           `protobuf:"bytes,6,opt,name=target_expire_remind_text,json=targetExpireRemindText,proto3" json:"target_expire_remind_text,omitempty"`
	PoseTypeInfo           []*VirtualImagePoseTypeInfo      `protobuf:"bytes,7,rep,name=pose_type_info,json=poseTypeInfo,proto3" json:"pose_type_info,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                         `json:"-"`
	XXX_unrecognized       []byte                           `json:"-"`
	XXX_sizecache          int32                            `json:"-"`
}

func (m *GetVirtualImageDisplayCfgResponse) Reset()         { *m = GetVirtualImageDisplayCfgResponse{} }
func (m *GetVirtualImageDisplayCfgResponse) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageDisplayCfgResponse) ProtoMessage()    {}
func (*GetVirtualImageDisplayCfgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{58}
}
func (m *GetVirtualImageDisplayCfgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageDisplayCfgResponse.Unmarshal(m, b)
}
func (m *GetVirtualImageDisplayCfgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageDisplayCfgResponse.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageDisplayCfgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageDisplayCfgResponse.Merge(dst, src)
}
func (m *GetVirtualImageDisplayCfgResponse) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageDisplayCfgResponse.Size(m)
}
func (m *GetVirtualImageDisplayCfgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageDisplayCfgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageDisplayCfgResponse proto.InternalMessageInfo

func (m *GetVirtualImageDisplayCfgResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetVirtualImageDisplayCfgResponse) GetSwitchInfo() []*VirtualImageDisplaySwitchInfo {
	if m != nil {
		return m.SwitchInfo
	}
	return nil
}

func (m *GetVirtualImageDisplayCfgResponse) GetUserRelationInfo() *UserRelationInfo {
	if m != nil {
		return m.UserRelationInfo
	}
	return nil
}

func (m *GetVirtualImageDisplayCfgResponse) GetAutoPlaySec() uint32 {
	if m != nil {
		return m.AutoPlaySec
	}
	return 0
}

func (m *GetVirtualImageDisplayCfgResponse) GetMyExpireRemindText() string {
	if m != nil {
		return m.MyExpireRemindText
	}
	return ""
}

func (m *GetVirtualImageDisplayCfgResponse) GetTargetExpireRemindText() string {
	if m != nil {
		return m.TargetExpireRemindText
	}
	return ""
}

func (m *GetVirtualImageDisplayCfgResponse) GetPoseTypeInfo() []*VirtualImagePoseTypeInfo {
	if m != nil {
		return m.PoseTypeInfo
	}
	return nil
}

type VirtualImagePoseTypeInfo struct {
	Scene                uint32   `protobuf:"varint,1,opt,name=scene,proto3" json:"scene,omitempty"`
	PoseType             uint32   `protobuf:"varint,2,opt,name=pose_type,json=poseType,proto3" json:"pose_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VirtualImagePoseTypeInfo) Reset()         { *m = VirtualImagePoseTypeInfo{} }
func (m *VirtualImagePoseTypeInfo) String() string { return proto.CompactTextString(m) }
func (*VirtualImagePoseTypeInfo) ProtoMessage()    {}
func (*VirtualImagePoseTypeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{59}
}
func (m *VirtualImagePoseTypeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImagePoseTypeInfo.Unmarshal(m, b)
}
func (m *VirtualImagePoseTypeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImagePoseTypeInfo.Marshal(b, m, deterministic)
}
func (dst *VirtualImagePoseTypeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImagePoseTypeInfo.Merge(dst, src)
}
func (m *VirtualImagePoseTypeInfo) XXX_Size() int {
	return xxx_messageInfo_VirtualImagePoseTypeInfo.Size(m)
}
func (m *VirtualImagePoseTypeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImagePoseTypeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImagePoseTypeInfo proto.InternalMessageInfo

func (m *VirtualImagePoseTypeInfo) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *VirtualImagePoseTypeInfo) GetPoseType() uint32 {
	if m != nil {
		return m.PoseType
	}
	return 0
}

// 外显状态提醒弹窗
type VirtualImageDisplayStatusPop struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	HighLightContent     string   `protobuf:"bytes,3,opt,name=high_light_content,json=highLightContent,proto3" json:"high_light_content,omitempty"`
	JumpUrl              string   `protobuf:"bytes,4,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VirtualImageDisplayStatusPop) Reset()         { *m = VirtualImageDisplayStatusPop{} }
func (m *VirtualImageDisplayStatusPop) String() string { return proto.CompactTextString(m) }
func (*VirtualImageDisplayStatusPop) ProtoMessage()    {}
func (*VirtualImageDisplayStatusPop) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{60}
}
func (m *VirtualImageDisplayStatusPop) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImageDisplayStatusPop.Unmarshal(m, b)
}
func (m *VirtualImageDisplayStatusPop) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImageDisplayStatusPop.Marshal(b, m, deterministic)
}
func (dst *VirtualImageDisplayStatusPop) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImageDisplayStatusPop.Merge(dst, src)
}
func (m *VirtualImageDisplayStatusPop) XXX_Size() int {
	return xxx_messageInfo_VirtualImageDisplayStatusPop.Size(m)
}
func (m *VirtualImageDisplayStatusPop) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImageDisplayStatusPop.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImageDisplayStatusPop proto.InternalMessageInfo

func (m *VirtualImageDisplayStatusPop) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *VirtualImageDisplayStatusPop) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *VirtualImageDisplayStatusPop) GetHighLightContent() string {
	if m != nil {
		return m.HighLightContent
	}
	return ""
}

func (m *VirtualImageDisplayStatusPop) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

// 外显状态提醒房间公屏
type VirtualImageDisplayStatusChannelNotify struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	Content              string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	HighLightContent     string   `protobuf:"bytes,4,opt,name=high_light_content,json=highLightContent,proto3" json:"high_light_content,omitempty"`
	JumpUrl              string   `protobuf:"bytes,5,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VirtualImageDisplayStatusChannelNotify) Reset() {
	*m = VirtualImageDisplayStatusChannelNotify{}
}
func (m *VirtualImageDisplayStatusChannelNotify) String() string { return proto.CompactTextString(m) }
func (*VirtualImageDisplayStatusChannelNotify) ProtoMessage()    {}
func (*VirtualImageDisplayStatusChannelNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{61}
}
func (m *VirtualImageDisplayStatusChannelNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImageDisplayStatusChannelNotify.Unmarshal(m, b)
}
func (m *VirtualImageDisplayStatusChannelNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImageDisplayStatusChannelNotify.Marshal(b, m, deterministic)
}
func (dst *VirtualImageDisplayStatusChannelNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImageDisplayStatusChannelNotify.Merge(dst, src)
}
func (m *VirtualImageDisplayStatusChannelNotify) XXX_Size() int {
	return xxx_messageInfo_VirtualImageDisplayStatusChannelNotify.Size(m)
}
func (m *VirtualImageDisplayStatusChannelNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImageDisplayStatusChannelNotify.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImageDisplayStatusChannelNotify proto.InternalMessageInfo

func (m *VirtualImageDisplayStatusChannelNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *VirtualImageDisplayStatusChannelNotify) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *VirtualImageDisplayStatusChannelNotify) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *VirtualImageDisplayStatusChannelNotify) GetHighLightContent() string {
	if m != nil {
		return m.HighLightContent
	}
	return ""
}

func (m *VirtualImageDisplayStatusChannelNotify) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

// 1. 获取可邀请列表
type GetBindInvitableListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Offset               string       `protobuf:"bytes,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32       `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBindInvitableListRequest) Reset()         { *m = GetBindInvitableListRequest{} }
func (m *GetBindInvitableListRequest) String() string { return proto.CompactTextString(m) }
func (*GetBindInvitableListRequest) ProtoMessage()    {}
func (*GetBindInvitableListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{62}
}
func (m *GetBindInvitableListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindInvitableListRequest.Unmarshal(m, b)
}
func (m *GetBindInvitableListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindInvitableListRequest.Marshal(b, m, deterministic)
}
func (dst *GetBindInvitableListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindInvitableListRequest.Merge(dst, src)
}
func (m *GetBindInvitableListRequest) XXX_Size() int {
	return xxx_messageInfo_GetBindInvitableListRequest.Size(m)
}
func (m *GetBindInvitableListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindInvitableListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindInvitableListRequest proto.InternalMessageInfo

func (m *GetBindInvitableListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetBindInvitableListRequest) GetOffset() string {
	if m != nil {
		return m.Offset
	}
	return ""
}

func (m *GetBindInvitableListRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type InvitableUser struct {
	Info                 *app.UserProfile `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	IsOnline             bool             `protobuf:"varint,2,opt,name=is_online,json=isOnline,proto3" json:"is_online,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *InvitableUser) Reset()         { *m = InvitableUser{} }
func (m *InvitableUser) String() string { return proto.CompactTextString(m) }
func (*InvitableUser) ProtoMessage()    {}
func (*InvitableUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{63}
}
func (m *InvitableUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InvitableUser.Unmarshal(m, b)
}
func (m *InvitableUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InvitableUser.Marshal(b, m, deterministic)
}
func (dst *InvitableUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InvitableUser.Merge(dst, src)
}
func (m *InvitableUser) XXX_Size() int {
	return xxx_messageInfo_InvitableUser.Size(m)
}
func (m *InvitableUser) XXX_DiscardUnknown() {
	xxx_messageInfo_InvitableUser.DiscardUnknown(m)
}

var xxx_messageInfo_InvitableUser proto.InternalMessageInfo

func (m *InvitableUser) GetInfo() *app.UserProfile {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *InvitableUser) GetIsOnline() bool {
	if m != nil {
		return m.IsOnline
	}
	return false
}

type GetBindInvitableListResponse struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UserList             []*InvitableUser `protobuf:"bytes,2,rep,name=user_list,json=userList,proto3" json:"user_list,omitempty"`
	NextOffset           string           `protobuf:"bytes,3,opt,name=next_offset,json=nextOffset,proto3" json:"next_offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetBindInvitableListResponse) Reset()         { *m = GetBindInvitableListResponse{} }
func (m *GetBindInvitableListResponse) String() string { return proto.CompactTextString(m) }
func (*GetBindInvitableListResponse) ProtoMessage()    {}
func (*GetBindInvitableListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{64}
}
func (m *GetBindInvitableListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindInvitableListResponse.Unmarshal(m, b)
}
func (m *GetBindInvitableListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindInvitableListResponse.Marshal(b, m, deterministic)
}
func (dst *GetBindInvitableListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindInvitableListResponse.Merge(dst, src)
}
func (m *GetBindInvitableListResponse) XXX_Size() int {
	return xxx_messageInfo_GetBindInvitableListResponse.Size(m)
}
func (m *GetBindInvitableListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindInvitableListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindInvitableListResponse proto.InternalMessageInfo

func (m *GetBindInvitableListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetBindInvitableListResponse) GetUserList() []*InvitableUser {
	if m != nil {
		return m.UserList
	}
	return nil
}

func (m *GetBindInvitableListResponse) GetNextOffset() string {
	if m != nil {
		return m.NextOffset
	}
	return ""
}

// 2. 发起/取消 关系绑定邀请
type SetBindInviteRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	Action               uint32       `protobuf:"varint,3,opt,name=action,proto3" json:"action,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetBindInviteRequest) Reset()         { *m = SetBindInviteRequest{} }
func (m *SetBindInviteRequest) String() string { return proto.CompactTextString(m) }
func (*SetBindInviteRequest) ProtoMessage()    {}
func (*SetBindInviteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{65}
}
func (m *SetBindInviteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBindInviteRequest.Unmarshal(m, b)
}
func (m *SetBindInviteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBindInviteRequest.Marshal(b, m, deterministic)
}
func (dst *SetBindInviteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBindInviteRequest.Merge(dst, src)
}
func (m *SetBindInviteRequest) XXX_Size() int {
	return xxx_messageInfo_SetBindInviteRequest.Size(m)
}
func (m *SetBindInviteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBindInviteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetBindInviteRequest proto.InternalMessageInfo

func (m *SetBindInviteRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetBindInviteRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SetBindInviteRequest) GetAction() uint32 {
	if m != nil {
		return m.Action
	}
	return 0
}

type SetBindInviteResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetBindInviteResponse) Reset()         { *m = SetBindInviteResponse{} }
func (m *SetBindInviteResponse) String() string { return proto.CompactTextString(m) }
func (*SetBindInviteResponse) ProtoMessage()    {}
func (*SetBindInviteResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{66}
}
func (m *SetBindInviteResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBindInviteResponse.Unmarshal(m, b)
}
func (m *SetBindInviteResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBindInviteResponse.Marshal(b, m, deterministic)
}
func (dst *SetBindInviteResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBindInviteResponse.Merge(dst, src)
}
func (m *SetBindInviteResponse) XXX_Size() int {
	return xxx_messageInfo_SetBindInviteResponse.Size(m)
}
func (m *SetBindInviteResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBindInviteResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetBindInviteResponse proto.InternalMessageInfo

func (m *SetBindInviteResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type BindConfirmActionRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	InviteId             string       `protobuf:"bytes,2,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	Action               uint32       `protobuf:"varint,3,opt,name=action,proto3" json:"action,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BindConfirmActionRequest) Reset()         { *m = BindConfirmActionRequest{} }
func (m *BindConfirmActionRequest) String() string { return proto.CompactTextString(m) }
func (*BindConfirmActionRequest) ProtoMessage()    {}
func (*BindConfirmActionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{67}
}
func (m *BindConfirmActionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindConfirmActionRequest.Unmarshal(m, b)
}
func (m *BindConfirmActionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindConfirmActionRequest.Marshal(b, m, deterministic)
}
func (dst *BindConfirmActionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindConfirmActionRequest.Merge(dst, src)
}
func (m *BindConfirmActionRequest) XXX_Size() int {
	return xxx_messageInfo_BindConfirmActionRequest.Size(m)
}
func (m *BindConfirmActionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BindConfirmActionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BindConfirmActionRequest proto.InternalMessageInfo

func (m *BindConfirmActionRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BindConfirmActionRequest) GetInviteId() string {
	if m != nil {
		return m.InviteId
	}
	return ""
}

func (m *BindConfirmActionRequest) GetAction() uint32 {
	if m != nil {
		return m.Action
	}
	return 0
}

type BindConfirmActionResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BindConfirmActionResponse) Reset()         { *m = BindConfirmActionResponse{} }
func (m *BindConfirmActionResponse) String() string { return proto.CompactTextString(m) }
func (*BindConfirmActionResponse) ProtoMessage()    {}
func (*BindConfirmActionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{68}
}
func (m *BindConfirmActionResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindConfirmActionResponse.Unmarshal(m, b)
}
func (m *BindConfirmActionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindConfirmActionResponse.Marshal(b, m, deterministic)
}
func (dst *BindConfirmActionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindConfirmActionResponse.Merge(dst, src)
}
func (m *BindConfirmActionResponse) XXX_Size() int {
	return xxx_messageInfo_BindConfirmActionResponse.Size(m)
}
func (m *BindConfirmActionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BindConfirmActionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BindConfirmActionResponse proto.InternalMessageInfo

func (m *BindConfirmActionResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 4. 获取关系绑定请求状态
type GetBindInviteStatusRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	InviteId             string       `protobuf:"bytes,2,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBindInviteStatusRequest) Reset()         { *m = GetBindInviteStatusRequest{} }
func (m *GetBindInviteStatusRequest) String() string { return proto.CompactTextString(m) }
func (*GetBindInviteStatusRequest) ProtoMessage()    {}
func (*GetBindInviteStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{69}
}
func (m *GetBindInviteStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindInviteStatusRequest.Unmarshal(m, b)
}
func (m *GetBindInviteStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindInviteStatusRequest.Marshal(b, m, deterministic)
}
func (dst *GetBindInviteStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindInviteStatusRequest.Merge(dst, src)
}
func (m *GetBindInviteStatusRequest) XXX_Size() int {
	return xxx_messageInfo_GetBindInviteStatusRequest.Size(m)
}
func (m *GetBindInviteStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindInviteStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindInviteStatusRequest proto.InternalMessageInfo

func (m *GetBindInviteStatusRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetBindInviteStatusRequest) GetInviteId() string {
	if m != nil {
		return m.InviteId
	}
	return ""
}

type GetBindInviteStatusResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	InviteInfo           *InviteInfo   `protobuf:"bytes,2,opt,name=invite_info,json=inviteInfo,proto3" json:"invite_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetBindInviteStatusResponse) Reset()         { *m = GetBindInviteStatusResponse{} }
func (m *GetBindInviteStatusResponse) String() string { return proto.CompactTextString(m) }
func (*GetBindInviteStatusResponse) ProtoMessage()    {}
func (*GetBindInviteStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{70}
}
func (m *GetBindInviteStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindInviteStatusResponse.Unmarshal(m, b)
}
func (m *GetBindInviteStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindInviteStatusResponse.Marshal(b, m, deterministic)
}
func (dst *GetBindInviteStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindInviteStatusResponse.Merge(dst, src)
}
func (m *GetBindInviteStatusResponse) XXX_Size() int {
	return xxx_messageInfo_GetBindInviteStatusResponse.Size(m)
}
func (m *GetBindInviteStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindInviteStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindInviteStatusResponse proto.InternalMessageInfo

func (m *GetBindInviteStatusResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetBindInviteStatusResponse) GetInviteInfo() *InviteInfo {
	if m != nil {
		return m.InviteInfo
	}
	return nil
}

// 5. 解除绑定
type UnbindVirtualImageRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UnbindVirtualImageRequest) Reset()         { *m = UnbindVirtualImageRequest{} }
func (m *UnbindVirtualImageRequest) String() string { return proto.CompactTextString(m) }
func (*UnbindVirtualImageRequest) ProtoMessage()    {}
func (*UnbindVirtualImageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{71}
}
func (m *UnbindVirtualImageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnbindVirtualImageRequest.Unmarshal(m, b)
}
func (m *UnbindVirtualImageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnbindVirtualImageRequest.Marshal(b, m, deterministic)
}
func (dst *UnbindVirtualImageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnbindVirtualImageRequest.Merge(dst, src)
}
func (m *UnbindVirtualImageRequest) XXX_Size() int {
	return xxx_messageInfo_UnbindVirtualImageRequest.Size(m)
}
func (m *UnbindVirtualImageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UnbindVirtualImageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UnbindVirtualImageRequest proto.InternalMessageInfo

func (m *UnbindVirtualImageRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UnbindVirtualImageRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type UnbindVirtualImageResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UnbindVirtualImageResponse) Reset()         { *m = UnbindVirtualImageResponse{} }
func (m *UnbindVirtualImageResponse) String() string { return proto.CompactTextString(m) }
func (*UnbindVirtualImageResponse) ProtoMessage()    {}
func (*UnbindVirtualImageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{72}
}
func (m *UnbindVirtualImageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnbindVirtualImageResponse.Unmarshal(m, b)
}
func (m *UnbindVirtualImageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnbindVirtualImageResponse.Marshal(b, m, deterministic)
}
func (dst *UnbindVirtualImageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnbindVirtualImageResponse.Merge(dst, src)
}
func (m *UnbindVirtualImageResponse) XXX_Size() int {
	return xxx_messageInfo_UnbindVirtualImageResponse.Size(m)
}
func (m *UnbindVirtualImageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UnbindVirtualImageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UnbindVirtualImageResponse proto.InternalMessageInfo

func (m *UnbindVirtualImageResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 6. 指定使用双人关系
type SetVirtualBindInUseRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetVirtualBindInUseRequest) Reset()         { *m = SetVirtualBindInUseRequest{} }
func (m *SetVirtualBindInUseRequest) String() string { return proto.CompactTextString(m) }
func (*SetVirtualBindInUseRequest) ProtoMessage()    {}
func (*SetVirtualBindInUseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{73}
}
func (m *SetVirtualBindInUseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVirtualBindInUseRequest.Unmarshal(m, b)
}
func (m *SetVirtualBindInUseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVirtualBindInUseRequest.Marshal(b, m, deterministic)
}
func (dst *SetVirtualBindInUseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVirtualBindInUseRequest.Merge(dst, src)
}
func (m *SetVirtualBindInUseRequest) XXX_Size() int {
	return xxx_messageInfo_SetVirtualBindInUseRequest.Size(m)
}
func (m *SetVirtualBindInUseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVirtualBindInUseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetVirtualBindInUseRequest proto.InternalMessageInfo

func (m *SetVirtualBindInUseRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetVirtualBindInUseRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type SetVirtualBindInUseResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetVirtualBindInUseResponse) Reset()         { *m = SetVirtualBindInUseResponse{} }
func (m *SetVirtualBindInUseResponse) String() string { return proto.CompactTextString(m) }
func (*SetVirtualBindInUseResponse) ProtoMessage()    {}
func (*SetVirtualBindInUseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{74}
}
func (m *SetVirtualBindInUseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVirtualBindInUseResponse.Unmarshal(m, b)
}
func (m *SetVirtualBindInUseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVirtualBindInUseResponse.Marshal(b, m, deterministic)
}
func (dst *SetVirtualBindInUseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVirtualBindInUseResponse.Merge(dst, src)
}
func (m *SetVirtualBindInUseResponse) XXX_Size() int {
	return xxx_messageInfo_SetVirtualBindInUseResponse.Size(m)
}
func (m *SetVirtualBindInUseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVirtualBindInUseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetVirtualBindInUseResponse proto.InternalMessageInfo

func (m *SetVirtualBindInUseResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type InviteInfo struct {
	InviteId             string           `protobuf:"bytes,1,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	Inviter              *app.UserProfile `protobuf:"bytes,2,opt,name=inviter,proto3" json:"inviter,omitempty"`
	Status               uint32           `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *InviteInfo) Reset()         { *m = InviteInfo{} }
func (m *InviteInfo) String() string { return proto.CompactTextString(m) }
func (*InviteInfo) ProtoMessage()    {}
func (*InviteInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{75}
}
func (m *InviteInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InviteInfo.Unmarshal(m, b)
}
func (m *InviteInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InviteInfo.Marshal(b, m, deterministic)
}
func (dst *InviteInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InviteInfo.Merge(dst, src)
}
func (m *InviteInfo) XXX_Size() int {
	return xxx_messageInfo_InviteInfo.Size(m)
}
func (m *InviteInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_InviteInfo.DiscardUnknown(m)
}

var xxx_messageInfo_InviteInfo proto.InternalMessageInfo

func (m *InviteInfo) GetInviteId() string {
	if m != nil {
		return m.InviteId
	}
	return ""
}

func (m *InviteInfo) GetInviter() *app.UserProfile {
	if m != nil {
		return m.Inviter
	}
	return nil
}

func (m *InviteInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

// 获取用户待处理的关系绑定邀请列表
type GetBindBeInvitedListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBindBeInvitedListRequest) Reset()         { *m = GetBindBeInvitedListRequest{} }
func (m *GetBindBeInvitedListRequest) String() string { return proto.CompactTextString(m) }
func (*GetBindBeInvitedListRequest) ProtoMessage()    {}
func (*GetBindBeInvitedListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{76}
}
func (m *GetBindBeInvitedListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindBeInvitedListRequest.Unmarshal(m, b)
}
func (m *GetBindBeInvitedListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindBeInvitedListRequest.Marshal(b, m, deterministic)
}
func (dst *GetBindBeInvitedListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindBeInvitedListRequest.Merge(dst, src)
}
func (m *GetBindBeInvitedListRequest) XXX_Size() int {
	return xxx_messageInfo_GetBindBeInvitedListRequest.Size(m)
}
func (m *GetBindBeInvitedListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindBeInvitedListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindBeInvitedListRequest proto.InternalMessageInfo

func (m *GetBindBeInvitedListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetBindBeInvitedListResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	InviteList           []*InviteInfo `protobuf:"bytes,2,rep,name=invite_list,json=inviteList,proto3" json:"invite_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetBindBeInvitedListResponse) Reset()         { *m = GetBindBeInvitedListResponse{} }
func (m *GetBindBeInvitedListResponse) String() string { return proto.CompactTextString(m) }
func (*GetBindBeInvitedListResponse) ProtoMessage()    {}
func (*GetBindBeInvitedListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{77}
}
func (m *GetBindBeInvitedListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindBeInvitedListResponse.Unmarshal(m, b)
}
func (m *GetBindBeInvitedListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindBeInvitedListResponse.Marshal(b, m, deterministic)
}
func (dst *GetBindBeInvitedListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindBeInvitedListResponse.Merge(dst, src)
}
func (m *GetBindBeInvitedListResponse) XXX_Size() int {
	return xxx_messageInfo_GetBindBeInvitedListResponse.Size(m)
}
func (m *GetBindBeInvitedListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindBeInvitedListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindBeInvitedListResponse proto.InternalMessageInfo

func (m *GetBindBeInvitedListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetBindBeInvitedListResponse) GetInviteList() []*InviteInfo {
	if m != nil {
		return m.InviteList
	}
	return nil
}

// VirtualImageBindInviteMsg, 废弃
type VirtualImageBindInviteMsg struct {
	Inviter              *app.UserProfile `protobuf:"bytes,1,opt,name=inviter,proto3" json:"inviter,omitempty"`
	InviteId             string           `protobuf:"bytes,2,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	PictureUrl           string           `protobuf:"bytes,3,opt,name=picture_url,json=pictureUrl,proto3" json:"picture_url,omitempty"`
	Text                 string           `protobuf:"bytes,4,opt,name=text,proto3" json:"text,omitempty"`
	HighlightText        string           `protobuf:"bytes,5,opt,name=highlight_text,json=highlightText,proto3" json:"highlight_text,omitempty"`
	TextColor            string           `protobuf:"bytes,6,opt,name=text_color,json=textColor,proto3" json:"text_color,omitempty"`
	JumpUrl              string           `protobuf:"bytes,7,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *VirtualImageBindInviteMsg) Reset()         { *m = VirtualImageBindInviteMsg{} }
func (m *VirtualImageBindInviteMsg) String() string { return proto.CompactTextString(m) }
func (*VirtualImageBindInviteMsg) ProtoMessage()    {}
func (*VirtualImageBindInviteMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{78}
}
func (m *VirtualImageBindInviteMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImageBindInviteMsg.Unmarshal(m, b)
}
func (m *VirtualImageBindInviteMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImageBindInviteMsg.Marshal(b, m, deterministic)
}
func (dst *VirtualImageBindInviteMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImageBindInviteMsg.Merge(dst, src)
}
func (m *VirtualImageBindInviteMsg) XXX_Size() int {
	return xxx_messageInfo_VirtualImageBindInviteMsg.Size(m)
}
func (m *VirtualImageBindInviteMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImageBindInviteMsg.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImageBindInviteMsg proto.InternalMessageInfo

func (m *VirtualImageBindInviteMsg) GetInviter() *app.UserProfile {
	if m != nil {
		return m.Inviter
	}
	return nil
}

func (m *VirtualImageBindInviteMsg) GetInviteId() string {
	if m != nil {
		return m.InviteId
	}
	return ""
}

func (m *VirtualImageBindInviteMsg) GetPictureUrl() string {
	if m != nil {
		return m.PictureUrl
	}
	return ""
}

func (m *VirtualImageBindInviteMsg) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *VirtualImageBindInviteMsg) GetHighlightText() string {
	if m != nil {
		return m.HighlightText
	}
	return ""
}

func (m *VirtualImageBindInviteMsg) GetTextColor() string {
	if m != nil {
		return m.TextColor
	}
	return ""
}

func (m *VirtualImageBindInviteMsg) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

type ChannelNoticeCfg struct {
	BeginTime            int64    `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	PublicContent        string   `protobuf:"bytes,3,opt,name=public_content,json=publicContent,proto3" json:"public_content,omitempty"`
	PublicContentColor   string   `protobuf:"bytes,4,opt,name=public_content_color,json=publicContentColor,proto3" json:"public_content_color,omitempty"`
	PublicContentJumpUrl string   `protobuf:"bytes,5,opt,name=public_content_jump_url,json=publicContentJumpUrl,proto3" json:"public_content_jump_url,omitempty"`
	FloatContent         string   `protobuf:"bytes,6,opt,name=float_content,json=floatContent,proto3" json:"float_content,omitempty"`
	FloatContentDuration uint32   `protobuf:"varint,7,opt,name=float_content_duration,json=floatContentDuration,proto3" json:"float_content_duration,omitempty"`
	HasRedDot            bool     `protobuf:"varint,8,opt,name=has_red_dot,json=hasRedDot,proto3" json:"has_red_dot,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelNoticeCfg) Reset()         { *m = ChannelNoticeCfg{} }
func (m *ChannelNoticeCfg) String() string { return proto.CompactTextString(m) }
func (*ChannelNoticeCfg) ProtoMessage()    {}
func (*ChannelNoticeCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{79}
}
func (m *ChannelNoticeCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelNoticeCfg.Unmarshal(m, b)
}
func (m *ChannelNoticeCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelNoticeCfg.Marshal(b, m, deterministic)
}
func (dst *ChannelNoticeCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelNoticeCfg.Merge(dst, src)
}
func (m *ChannelNoticeCfg) XXX_Size() int {
	return xxx_messageInfo_ChannelNoticeCfg.Size(m)
}
func (m *ChannelNoticeCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelNoticeCfg.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelNoticeCfg proto.InternalMessageInfo

func (m *ChannelNoticeCfg) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ChannelNoticeCfg) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ChannelNoticeCfg) GetPublicContent() string {
	if m != nil {
		return m.PublicContent
	}
	return ""
}

func (m *ChannelNoticeCfg) GetPublicContentColor() string {
	if m != nil {
		return m.PublicContentColor
	}
	return ""
}

func (m *ChannelNoticeCfg) GetPublicContentJumpUrl() string {
	if m != nil {
		return m.PublicContentJumpUrl
	}
	return ""
}

func (m *ChannelNoticeCfg) GetFloatContent() string {
	if m != nil {
		return m.FloatContent
	}
	return ""
}

func (m *ChannelNoticeCfg) GetFloatContentDuration() uint32 {
	if m != nil {
		return m.FloatContentDuration
	}
	return 0
}

func (m *ChannelNoticeCfg) GetHasRedDot() bool {
	if m != nil {
		return m.HasRedDot
	}
	return false
}

// 虚拟形象入口权限
type CheckUserVirtualImageEntranceRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CheckUserVirtualImageEntranceRequest) Reset()         { *m = CheckUserVirtualImageEntranceRequest{} }
func (m *CheckUserVirtualImageEntranceRequest) String() string { return proto.CompactTextString(m) }
func (*CheckUserVirtualImageEntranceRequest) ProtoMessage()    {}
func (*CheckUserVirtualImageEntranceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{80}
}
func (m *CheckUserVirtualImageEntranceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserVirtualImageEntranceRequest.Unmarshal(m, b)
}
func (m *CheckUserVirtualImageEntranceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserVirtualImageEntranceRequest.Marshal(b, m, deterministic)
}
func (dst *CheckUserVirtualImageEntranceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserVirtualImageEntranceRequest.Merge(dst, src)
}
func (m *CheckUserVirtualImageEntranceRequest) XXX_Size() int {
	return xxx_messageInfo_CheckUserVirtualImageEntranceRequest.Size(m)
}
func (m *CheckUserVirtualImageEntranceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserVirtualImageEntranceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserVirtualImageEntranceRequest proto.InternalMessageInfo

func (m *CheckUserVirtualImageEntranceRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckUserVirtualImageEntranceRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CheckUserVirtualImageEntranceResponse struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	HasEntrance          bool              `protobuf:"varint,2,opt,name=has_entrance,json=hasEntrance,proto3" json:"has_entrance,omitempty"`
	GameEnable           bool              `protobuf:"varint,3,opt,name=game_enable,json=gameEnable,proto3" json:"game_enable,omitempty"`
	ChannelNoticeCfg     *ChannelNoticeCfg `protobuf:"bytes,4,opt,name=channel_notice_cfg,json=channelNoticeCfg,proto3" json:"channel_notice_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CheckUserVirtualImageEntranceResponse) Reset()         { *m = CheckUserVirtualImageEntranceResponse{} }
func (m *CheckUserVirtualImageEntranceResponse) String() string { return proto.CompactTextString(m) }
func (*CheckUserVirtualImageEntranceResponse) ProtoMessage()    {}
func (*CheckUserVirtualImageEntranceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{81}
}
func (m *CheckUserVirtualImageEntranceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserVirtualImageEntranceResponse.Unmarshal(m, b)
}
func (m *CheckUserVirtualImageEntranceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserVirtualImageEntranceResponse.Marshal(b, m, deterministic)
}
func (dst *CheckUserVirtualImageEntranceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserVirtualImageEntranceResponse.Merge(dst, src)
}
func (m *CheckUserVirtualImageEntranceResponse) XXX_Size() int {
	return xxx_messageInfo_CheckUserVirtualImageEntranceResponse.Size(m)
}
func (m *CheckUserVirtualImageEntranceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserVirtualImageEntranceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserVirtualImageEntranceResponse proto.InternalMessageInfo

func (m *CheckUserVirtualImageEntranceResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckUserVirtualImageEntranceResponse) GetHasEntrance() bool {
	if m != nil {
		return m.HasEntrance
	}
	return false
}

func (m *CheckUserVirtualImageEntranceResponse) GetGameEnable() bool {
	if m != nil {
		return m.GameEnable
	}
	return false
}

func (m *CheckUserVirtualImageEntranceResponse) GetChannelNoticeCfg() *ChannelNoticeCfg {
	if m != nil {
		return m.ChannelNoticeCfg
	}
	return nil
}

type GetVirtualImageCommodityRedDotRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetVirtualImageCommodityRedDotRequest) Reset()         { *m = GetVirtualImageCommodityRedDotRequest{} }
func (m *GetVirtualImageCommodityRedDotRequest) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageCommodityRedDotRequest) ProtoMessage()    {}
func (*GetVirtualImageCommodityRedDotRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{82}
}
func (m *GetVirtualImageCommodityRedDotRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageCommodityRedDotRequest.Unmarshal(m, b)
}
func (m *GetVirtualImageCommodityRedDotRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageCommodityRedDotRequest.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageCommodityRedDotRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageCommodityRedDotRequest.Merge(dst, src)
}
func (m *GetVirtualImageCommodityRedDotRequest) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageCommodityRedDotRequest.Size(m)
}
func (m *GetVirtualImageCommodityRedDotRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageCommodityRedDotRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageCommodityRedDotRequest proto.InternalMessageInfo

func (m *GetVirtualImageCommodityRedDotRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetVirtualImageCommodityRedDotResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	GlobalVersion        uint32        `protobuf:"varint,2,opt,name=global_version,json=globalVersion,proto3" json:"global_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetVirtualImageCommodityRedDotResponse) Reset() {
	*m = GetVirtualImageCommodityRedDotResponse{}
}
func (m *GetVirtualImageCommodityRedDotResponse) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageCommodityRedDotResponse) ProtoMessage()    {}
func (*GetVirtualImageCommodityRedDotResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{83}
}
func (m *GetVirtualImageCommodityRedDotResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageCommodityRedDotResponse.Unmarshal(m, b)
}
func (m *GetVirtualImageCommodityRedDotResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageCommodityRedDotResponse.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageCommodityRedDotResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageCommodityRedDotResponse.Merge(dst, src)
}
func (m *GetVirtualImageCommodityRedDotResponse) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageCommodityRedDotResponse.Size(m)
}
func (m *GetVirtualImageCommodityRedDotResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageCommodityRedDotResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageCommodityRedDotResponse proto.InternalMessageInfo

func (m *GetVirtualImageCommodityRedDotResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetVirtualImageCommodityRedDotResponse) GetGlobalVersion() uint32 {
	if m != nil {
		return m.GlobalVersion
	}
	return 0
}

type CommodityRedDotInfo struct {
	CommodityId          uint32   `protobuf:"varint,1,opt,name=commodity_id,json=commodityId,proto3" json:"commodity_id,omitempty"`
	RedDotVersion        uint32   `protobuf:"varint,2,opt,name=red_dot_version,json=redDotVersion,proto3" json:"red_dot_version,omitempty"`
	Category             uint32   `protobuf:"varint,3,opt,name=category,proto3" json:"category,omitempty"`
	SubCategory          uint32   `protobuf:"varint,4,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	CommodityName        string   `protobuf:"bytes,5,opt,name=commodity_name,json=commodityName,proto3" json:"commodity_name,omitempty"`
	IsRecommend          bool     `protobuf:"varint,6,opt,name=is_recommend,json=isRecommend,proto3" json:"is_recommend,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommodityRedDotInfo) Reset()         { *m = CommodityRedDotInfo{} }
func (m *CommodityRedDotInfo) String() string { return proto.CompactTextString(m) }
func (*CommodityRedDotInfo) ProtoMessage()    {}
func (*CommodityRedDotInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{84}
}
func (m *CommodityRedDotInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommodityRedDotInfo.Unmarshal(m, b)
}
func (m *CommodityRedDotInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommodityRedDotInfo.Marshal(b, m, deterministic)
}
func (dst *CommodityRedDotInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommodityRedDotInfo.Merge(dst, src)
}
func (m *CommodityRedDotInfo) XXX_Size() int {
	return xxx_messageInfo_CommodityRedDotInfo.Size(m)
}
func (m *CommodityRedDotInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CommodityRedDotInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CommodityRedDotInfo proto.InternalMessageInfo

func (m *CommodityRedDotInfo) GetCommodityId() uint32 {
	if m != nil {
		return m.CommodityId
	}
	return 0
}

func (m *CommodityRedDotInfo) GetRedDotVersion() uint32 {
	if m != nil {
		return m.RedDotVersion
	}
	return 0
}

func (m *CommodityRedDotInfo) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *CommodityRedDotInfo) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

func (m *CommodityRedDotInfo) GetCommodityName() string {
	if m != nil {
		return m.CommodityName
	}
	return ""
}

func (m *CommodityRedDotInfo) GetIsRecommend() bool {
	if m != nil {
		return m.IsRecommend
	}
	return false
}

type GetVirtualImageCommodityRedDotDetailRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	MaxVersion           uint32       `protobuf:"varint,2,opt,name=max_version,json=maxVersion,proto3" json:"max_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetVirtualImageCommodityRedDotDetailRequest) Reset() {
	*m = GetVirtualImageCommodityRedDotDetailRequest{}
}
func (m *GetVirtualImageCommodityRedDotDetailRequest) String() string {
	return proto.CompactTextString(m)
}
func (*GetVirtualImageCommodityRedDotDetailRequest) ProtoMessage() {}
func (*GetVirtualImageCommodityRedDotDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{85}
}
func (m *GetVirtualImageCommodityRedDotDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageCommodityRedDotDetailRequest.Unmarshal(m, b)
}
func (m *GetVirtualImageCommodityRedDotDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageCommodityRedDotDetailRequest.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageCommodityRedDotDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageCommodityRedDotDetailRequest.Merge(dst, src)
}
func (m *GetVirtualImageCommodityRedDotDetailRequest) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageCommodityRedDotDetailRequest.Size(m)
}
func (m *GetVirtualImageCommodityRedDotDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageCommodityRedDotDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageCommodityRedDotDetailRequest proto.InternalMessageInfo

func (m *GetVirtualImageCommodityRedDotDetailRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetVirtualImageCommodityRedDotDetailRequest) GetMaxVersion() uint32 {
	if m != nil {
		return m.MaxVersion
	}
	return 0
}

type GetVirtualImageCommodityRedDotDetailResponse struct {
	BaseResp             *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CommodityRedDotInfos []*CommodityRedDotInfo `protobuf:"bytes,2,rep,name=commodity_red_dot_infos,json=commodityRedDotInfos,proto3" json:"commodity_red_dot_infos,omitempty"`
	MaxVersion           uint32                 `protobuf:"varint,3,opt,name=max_version,json=maxVersion,proto3" json:"max_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetVirtualImageCommodityRedDotDetailResponse) Reset() {
	*m = GetVirtualImageCommodityRedDotDetailResponse{}
}
func (m *GetVirtualImageCommodityRedDotDetailResponse) String() string {
	return proto.CompactTextString(m)
}
func (*GetVirtualImageCommodityRedDotDetailResponse) ProtoMessage() {}
func (*GetVirtualImageCommodityRedDotDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{86}
}
func (m *GetVirtualImageCommodityRedDotDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageCommodityRedDotDetailResponse.Unmarshal(m, b)
}
func (m *GetVirtualImageCommodityRedDotDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageCommodityRedDotDetailResponse.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageCommodityRedDotDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageCommodityRedDotDetailResponse.Merge(dst, src)
}
func (m *GetVirtualImageCommodityRedDotDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageCommodityRedDotDetailResponse.Size(m)
}
func (m *GetVirtualImageCommodityRedDotDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageCommodityRedDotDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageCommodityRedDotDetailResponse proto.InternalMessageInfo

func (m *GetVirtualImageCommodityRedDotDetailResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetVirtualImageCommodityRedDotDetailResponse) GetCommodityRedDotInfos() []*CommodityRedDotInfo {
	if m != nil {
		return m.CommodityRedDotInfos
	}
	return nil
}

func (m *GetVirtualImageCommodityRedDotDetailResponse) GetMaxVersion() uint32 {
	if m != nil {
		return m.MaxVersion
	}
	return 0
}

// 获取虚拟形象卡片通用配置 cfg_version 变大时调用
type GetVirtualImageCardCommonCfgRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetVirtualImageCardCommonCfgRequest) Reset()         { *m = GetVirtualImageCardCommonCfgRequest{} }
func (m *GetVirtualImageCardCommonCfgRequest) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageCardCommonCfgRequest) ProtoMessage()    {}
func (*GetVirtualImageCardCommonCfgRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{87}
}
func (m *GetVirtualImageCardCommonCfgRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageCardCommonCfgRequest.Unmarshal(m, b)
}
func (m *GetVirtualImageCardCommonCfgRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageCardCommonCfgRequest.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageCardCommonCfgRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageCardCommonCfgRequest.Merge(dst, src)
}
func (m *GetVirtualImageCardCommonCfgRequest) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageCardCommonCfgRequest.Size(m)
}
func (m *GetVirtualImageCardCommonCfgRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageCardCommonCfgRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageCardCommonCfgRequest proto.InternalMessageInfo

func (m *GetVirtualImageCardCommonCfgRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type AboutToExpireCfg struct {
	Icon                 string   `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	ExpireAlertTime      uint32   `protobuf:"varint,15,opt,name=expire_alert_time,json=expireAlertTime,proto3" json:"expire_alert_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AboutToExpireCfg) Reset()         { *m = AboutToExpireCfg{} }
func (m *AboutToExpireCfg) String() string { return proto.CompactTextString(m) }
func (*AboutToExpireCfg) ProtoMessage()    {}
func (*AboutToExpireCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{88}
}
func (m *AboutToExpireCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AboutToExpireCfg.Unmarshal(m, b)
}
func (m *AboutToExpireCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AboutToExpireCfg.Marshal(b, m, deterministic)
}
func (dst *AboutToExpireCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AboutToExpireCfg.Merge(dst, src)
}
func (m *AboutToExpireCfg) XXX_Size() int {
	return xxx_messageInfo_AboutToExpireCfg.Size(m)
}
func (m *AboutToExpireCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_AboutToExpireCfg.DiscardUnknown(m)
}

var xxx_messageInfo_AboutToExpireCfg proto.InternalMessageInfo

func (m *AboutToExpireCfg) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *AboutToExpireCfg) GetExpireAlertTime() uint32 {
	if m != nil {
		return m.ExpireAlertTime
	}
	return 0
}

type GetVirtualImageCardCommonCfgResponse struct {
	BaseResp               *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	WaitToBuyIcon          string              `protobuf:"bytes,2,opt,name=wait_to_buy_icon,json=waitToBuyIcon,proto3" json:"wait_to_buy_icon,omitempty"`
	AlreadyBuyIcon         string              `protobuf:"bytes,3,opt,name=already_buy_icon,json=alreadyBuyIcon,proto3" json:"already_buy_icon,omitempty"`
	AboutToExpireCfgList   []*AboutToExpireCfg `protobuf:"bytes,4,rep,name=about_to_expire_cfg_list,json=aboutToExpireCfgList,proto3" json:"about_to_expire_cfg_list,omitempty"`
	FirstEnterCardStoreUrl string              `protobuf:"bytes,5,opt,name=first_enter_card_store_url,json=firstEnterCardStoreUrl,proto3" json:"first_enter_card_store_url,omitempty"`
	AdText                 string              `protobuf:"bytes,6,opt,name=ad_text,json=adText,proto3" json:"ad_text,omitempty"`
	NDayShowOnce           uint32              `protobuf:"varint,7,opt,name=n_day_show_once,json=nDayShowOnce,proto3" json:"n_day_show_once,omitempty"`
	CfgVersion             uint32              `protobuf:"varint,8,opt,name=cfg_version,json=cfgVersion,proto3" json:"cfg_version,omitempty"`
	WaitToBuyBg            string              `protobuf:"bytes,9,opt,name=wait_to_buy_bg,json=waitToBuyBg,proto3" json:"wait_to_buy_bg,omitempty"`
	AlreadyBuyBg           string              `protobuf:"bytes,10,opt,name=already_buy_bg,json=alreadyBuyBg,proto3" json:"already_buy_bg,omitempty"`
	AboutToExpireBg        string              `protobuf:"bytes,11,opt,name=about_to_expire_bg,json=aboutToExpireBg,proto3" json:"about_to_expire_bg,omitempty"`
	StoreResidentEntryIcon string              `protobuf:"bytes,12,opt,name=store_resident_entry_icon,json=storeResidentEntryIcon,proto3" json:"store_resident_entry_icon,omitempty"`
	StoreTabIconSelected   string              `protobuf:"bytes,13,opt,name=store_tab_icon_selected,json=storeTabIconSelected,proto3" json:"store_tab_icon_selected,omitempty"`
	StoreTabIconUnselected string              `protobuf:"bytes,14,opt,name=store_tab_icon_unselected,json=storeTabIconUnselected,proto3" json:"store_tab_icon_unselected,omitempty"`
	ExpireAlertTime        uint32              `protobuf:"varint,15,opt,name=expire_alert_time,json=expireAlertTime,proto3" json:"expire_alert_time,omitempty"`
	FirstEnterCardStoreMd5 string              `protobuf:"bytes,16,opt,name=first_enter_card_store_md5,json=firstEnterCardStoreMd5,proto3" json:"first_enter_card_store_md5,omitempty"`
	PcWaitToBuyBg          string              `protobuf:"bytes,17,opt,name=pc_wait_to_buy_bg,json=pcWaitToBuyBg,proto3" json:"pc_wait_to_buy_bg,omitempty"`
	PcAlreadyBuyBg         string              `protobuf:"bytes,18,opt,name=pc_already_buy_bg,json=pcAlreadyBuyBg,proto3" json:"pc_already_buy_bg,omitempty"`
	PcAboutToExpireBg      string              `protobuf:"bytes,19,opt,name=pc_about_to_expire_bg,json=pcAboutToExpireBg,proto3" json:"pc_about_to_expire_bg,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}            `json:"-"`
	XXX_unrecognized       []byte              `json:"-"`
	XXX_sizecache          int32               `json:"-"`
}

func (m *GetVirtualImageCardCommonCfgResponse) Reset()         { *m = GetVirtualImageCardCommonCfgResponse{} }
func (m *GetVirtualImageCardCommonCfgResponse) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageCardCommonCfgResponse) ProtoMessage()    {}
func (*GetVirtualImageCardCommonCfgResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{89}
}
func (m *GetVirtualImageCardCommonCfgResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageCardCommonCfgResponse.Unmarshal(m, b)
}
func (m *GetVirtualImageCardCommonCfgResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageCardCommonCfgResponse.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageCardCommonCfgResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageCardCommonCfgResponse.Merge(dst, src)
}
func (m *GetVirtualImageCardCommonCfgResponse) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageCardCommonCfgResponse.Size(m)
}
func (m *GetVirtualImageCardCommonCfgResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageCardCommonCfgResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageCardCommonCfgResponse proto.InternalMessageInfo

func (m *GetVirtualImageCardCommonCfgResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetVirtualImageCardCommonCfgResponse) GetWaitToBuyIcon() string {
	if m != nil {
		return m.WaitToBuyIcon
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetAlreadyBuyIcon() string {
	if m != nil {
		return m.AlreadyBuyIcon
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetAboutToExpireCfgList() []*AboutToExpireCfg {
	if m != nil {
		return m.AboutToExpireCfgList
	}
	return nil
}

func (m *GetVirtualImageCardCommonCfgResponse) GetFirstEnterCardStoreUrl() string {
	if m != nil {
		return m.FirstEnterCardStoreUrl
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetAdText() string {
	if m != nil {
		return m.AdText
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetNDayShowOnce() uint32 {
	if m != nil {
		return m.NDayShowOnce
	}
	return 0
}

func (m *GetVirtualImageCardCommonCfgResponse) GetCfgVersion() uint32 {
	if m != nil {
		return m.CfgVersion
	}
	return 0
}

func (m *GetVirtualImageCardCommonCfgResponse) GetWaitToBuyBg() string {
	if m != nil {
		return m.WaitToBuyBg
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetAlreadyBuyBg() string {
	if m != nil {
		return m.AlreadyBuyBg
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetAboutToExpireBg() string {
	if m != nil {
		return m.AboutToExpireBg
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetStoreResidentEntryIcon() string {
	if m != nil {
		return m.StoreResidentEntryIcon
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetStoreTabIconSelected() string {
	if m != nil {
		return m.StoreTabIconSelected
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetStoreTabIconUnselected() string {
	if m != nil {
		return m.StoreTabIconUnselected
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetExpireAlertTime() uint32 {
	if m != nil {
		return m.ExpireAlertTime
	}
	return 0
}

func (m *GetVirtualImageCardCommonCfgResponse) GetFirstEnterCardStoreMd5() string {
	if m != nil {
		return m.FirstEnterCardStoreMd5
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetPcWaitToBuyBg() string {
	if m != nil {
		return m.PcWaitToBuyBg
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetPcAlreadyBuyBg() string {
	if m != nil {
		return m.PcAlreadyBuyBg
	}
	return ""
}

func (m *GetVirtualImageCardCommonCfgResponse) GetPcAboutToExpireBg() string {
	if m != nil {
		return m.PcAboutToExpireBg
	}
	return ""
}

// 获取虚拟形象卡片入口状态
type GetVirtualImageCardEntryStatusRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetVirtualImageCardEntryStatusRequest) Reset()         { *m = GetVirtualImageCardEntryStatusRequest{} }
func (m *GetVirtualImageCardEntryStatusRequest) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageCardEntryStatusRequest) ProtoMessage()    {}
func (*GetVirtualImageCardEntryStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{90}
}
func (m *GetVirtualImageCardEntryStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageCardEntryStatusRequest.Unmarshal(m, b)
}
func (m *GetVirtualImageCardEntryStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageCardEntryStatusRequest.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageCardEntryStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageCardEntryStatusRequest.Merge(dst, src)
}
func (m *GetVirtualImageCardEntryStatusRequest) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageCardEntryStatusRequest.Size(m)
}
func (m *GetVirtualImageCardEntryStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageCardEntryStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageCardEntryStatusRequest proto.InternalMessageInfo

func (m *GetVirtualImageCardEntryStatusRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

// 虚拟形象卡片入口状态
type GetVirtualImageCardEntryStatusResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ExpireTime           uint32        `protobuf:"varint,2,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	LowPriceText         string        `protobuf:"bytes,3,opt,name=low_price_text,json=lowPriceText,proto3" json:"low_price_text,omitempty"`
	AdIdx                uint32        `protobuf:"varint,4,opt,name=ad_idx,json=adIdx,proto3" json:"ad_idx,omitempty"`
	CfgVersion           uint32        `protobuf:"varint,5,opt,name=cfg_version,json=cfgVersion,proto3" json:"cfg_version,omitempty"`
	Switch               bool          `protobuf:"varint,6,opt,name=switch,proto3" json:"switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetVirtualImageCardEntryStatusResponse) Reset() {
	*m = GetVirtualImageCardEntryStatusResponse{}
}
func (m *GetVirtualImageCardEntryStatusResponse) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageCardEntryStatusResponse) ProtoMessage()    {}
func (*GetVirtualImageCardEntryStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{91}
}
func (m *GetVirtualImageCardEntryStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageCardEntryStatusResponse.Unmarshal(m, b)
}
func (m *GetVirtualImageCardEntryStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageCardEntryStatusResponse.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageCardEntryStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageCardEntryStatusResponse.Merge(dst, src)
}
func (m *GetVirtualImageCardEntryStatusResponse) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageCardEntryStatusResponse.Size(m)
}
func (m *GetVirtualImageCardEntryStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageCardEntryStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageCardEntryStatusResponse proto.InternalMessageInfo

func (m *GetVirtualImageCardEntryStatusResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetVirtualImageCardEntryStatusResponse) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *GetVirtualImageCardEntryStatusResponse) GetLowPriceText() string {
	if m != nil {
		return m.LowPriceText
	}
	return ""
}

func (m *GetVirtualImageCardEntryStatusResponse) GetAdIdx() uint32 {
	if m != nil {
		return m.AdIdx
	}
	return 0
}

func (m *GetVirtualImageCardEntryStatusResponse) GetCfgVersion() uint32 {
	if m != nil {
		return m.CfgVersion
	}
	return 0
}

func (m *GetVirtualImageCardEntryStatusResponse) GetSwitch() bool {
	if m != nil {
		return m.Switch
	}
	return false
}

// 无限换装卡变更推送，开通/自动续费/退款都会推，用于客户端更新卡的状态
type VirtualImageCardStatusChangeNotify struct {
	ExpireTime           uint32   `protobuf:"varint,1,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VirtualImageCardStatusChangeNotify) Reset()         { *m = VirtualImageCardStatusChangeNotify{} }
func (m *VirtualImageCardStatusChangeNotify) String() string { return proto.CompactTextString(m) }
func (*VirtualImageCardStatusChangeNotify) ProtoMessage()    {}
func (*VirtualImageCardStatusChangeNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{92}
}
func (m *VirtualImageCardStatusChangeNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImageCardStatusChangeNotify.Unmarshal(m, b)
}
func (m *VirtualImageCardStatusChangeNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImageCardStatusChangeNotify.Marshal(b, m, deterministic)
}
func (dst *VirtualImageCardStatusChangeNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImageCardStatusChangeNotify.Merge(dst, src)
}
func (m *VirtualImageCardStatusChangeNotify) XXX_Size() int {
	return xxx_messageInfo_VirtualImageCardStatusChangeNotify.Size(m)
}
func (m *VirtualImageCardStatusChangeNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImageCardStatusChangeNotify.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImageCardStatusChangeNotify proto.InternalMessageInfo

func (m *VirtualImageCardStatusChangeNotify) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

// 无限换装卡支付结果扩展信息
type VirtualImageCardPayResultExt struct {
	ResultType           VirtualImageCardPayResultExt_ResultType `protobuf:"varint,1,opt,name=result_type,json=resultType,proto3,enum=ga.virtual_image_logic.VirtualImageCardPayResultExt_ResultType" json:"result_type,omitempty"`
	PopupInfo            string                                  `protobuf:"bytes,2,opt,name=popup_info,json=popupInfo,proto3" json:"popup_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                `json:"-"`
	XXX_unrecognized     []byte                                  `json:"-"`
	XXX_sizecache        int32                                   `json:"-"`
}

func (m *VirtualImageCardPayResultExt) Reset()         { *m = VirtualImageCardPayResultExt{} }
func (m *VirtualImageCardPayResultExt) String() string { return proto.CompactTextString(m) }
func (*VirtualImageCardPayResultExt) ProtoMessage()    {}
func (*VirtualImageCardPayResultExt) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{93}
}
func (m *VirtualImageCardPayResultExt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualImageCardPayResultExt.Unmarshal(m, b)
}
func (m *VirtualImageCardPayResultExt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualImageCardPayResultExt.Marshal(b, m, deterministic)
}
func (dst *VirtualImageCardPayResultExt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualImageCardPayResultExt.Merge(dst, src)
}
func (m *VirtualImageCardPayResultExt) XXX_Size() int {
	return xxx_messageInfo_VirtualImageCardPayResultExt.Size(m)
}
func (m *VirtualImageCardPayResultExt) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualImageCardPayResultExt.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualImageCardPayResultExt proto.InternalMessageInfo

func (m *VirtualImageCardPayResultExt) GetResultType() VirtualImageCardPayResultExt_ResultType {
	if m != nil {
		return m.ResultType
	}
	return VirtualImageCardPayResultExt_RESULT_TYPE_UNSPECIFIED
}

func (m *VirtualImageCardPayResultExt) GetPopupInfo() string {
	if m != nil {
		return m.PopupInfo
	}
	return ""
}

type SetVirtualImagePoseTypeRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PoseType             uint32       `protobuf:"varint,2,opt,name=pose_type,json=poseType,proto3" json:"pose_type,omitempty"`
	Scene                uint32       `protobuf:"varint,3,opt,name=scene,proto3" json:"scene,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetVirtualImagePoseTypeRequest) Reset()         { *m = SetVirtualImagePoseTypeRequest{} }
func (m *SetVirtualImagePoseTypeRequest) String() string { return proto.CompactTextString(m) }
func (*SetVirtualImagePoseTypeRequest) ProtoMessage()    {}
func (*SetVirtualImagePoseTypeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{94}
}
func (m *SetVirtualImagePoseTypeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVirtualImagePoseTypeRequest.Unmarshal(m, b)
}
func (m *SetVirtualImagePoseTypeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVirtualImagePoseTypeRequest.Marshal(b, m, deterministic)
}
func (dst *SetVirtualImagePoseTypeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVirtualImagePoseTypeRequest.Merge(dst, src)
}
func (m *SetVirtualImagePoseTypeRequest) XXX_Size() int {
	return xxx_messageInfo_SetVirtualImagePoseTypeRequest.Size(m)
}
func (m *SetVirtualImagePoseTypeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVirtualImagePoseTypeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetVirtualImagePoseTypeRequest proto.InternalMessageInfo

func (m *SetVirtualImagePoseTypeRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetVirtualImagePoseTypeRequest) GetPoseType() uint32 {
	if m != nil {
		return m.PoseType
	}
	return 0
}

func (m *SetVirtualImagePoseTypeRequest) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

type SetVirtualImagePoseTypeResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetVirtualImagePoseTypeResponse) Reset()         { *m = SetVirtualImagePoseTypeResponse{} }
func (m *SetVirtualImagePoseTypeResponse) String() string { return proto.CompactTextString(m) }
func (*SetVirtualImagePoseTypeResponse) ProtoMessage()    {}
func (*SetVirtualImagePoseTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{95}
}
func (m *SetVirtualImagePoseTypeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVirtualImagePoseTypeResponse.Unmarshal(m, b)
}
func (m *SetVirtualImagePoseTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVirtualImagePoseTypeResponse.Marshal(b, m, deterministic)
}
func (dst *SetVirtualImagePoseTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVirtualImagePoseTypeResponse.Merge(dst, src)
}
func (m *SetVirtualImagePoseTypeResponse) XXX_Size() int {
	return xxx_messageInfo_SetVirtualImagePoseTypeResponse.Size(m)
}
func (m *SetVirtualImagePoseTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVirtualImagePoseTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetVirtualImagePoseTypeResponse proto.InternalMessageInfo

func (m *SetVirtualImagePoseTypeResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetVirtualImageBeginnerGuideRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetVirtualImageBeginnerGuideRequest) Reset()         { *m = GetVirtualImageBeginnerGuideRequest{} }
func (m *GetVirtualImageBeginnerGuideRequest) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageBeginnerGuideRequest) ProtoMessage()    {}
func (*GetVirtualImageBeginnerGuideRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{96}
}
func (m *GetVirtualImageBeginnerGuideRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageBeginnerGuideRequest.Unmarshal(m, b)
}
func (m *GetVirtualImageBeginnerGuideRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageBeginnerGuideRequest.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageBeginnerGuideRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageBeginnerGuideRequest.Merge(dst, src)
}
func (m *GetVirtualImageBeginnerGuideRequest) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageBeginnerGuideRequest.Size(m)
}
func (m *GetVirtualImageBeginnerGuideRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageBeginnerGuideRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageBeginnerGuideRequest proto.InternalMessageInfo

func (m *GetVirtualImageBeginnerGuideRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetVirtualImageBeginnerGuideResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	HasGuide             bool          `protobuf:"varint,2,opt,name=has_guide,json=hasGuide,proto3" json:"has_guide,omitempty"`
	WaitSourceTime       uint32        `protobuf:"varint,3,opt,name=wait_source_time,json=waitSourceTime,proto3" json:"wait_source_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetVirtualImageBeginnerGuideResponse) Reset()         { *m = GetVirtualImageBeginnerGuideResponse{} }
func (m *GetVirtualImageBeginnerGuideResponse) String() string { return proto.CompactTextString(m) }
func (*GetVirtualImageBeginnerGuideResponse) ProtoMessage()    {}
func (*GetVirtualImageBeginnerGuideResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{97}
}
func (m *GetVirtualImageBeginnerGuideResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualImageBeginnerGuideResponse.Unmarshal(m, b)
}
func (m *GetVirtualImageBeginnerGuideResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualImageBeginnerGuideResponse.Marshal(b, m, deterministic)
}
func (dst *GetVirtualImageBeginnerGuideResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualImageBeginnerGuideResponse.Merge(dst, src)
}
func (m *GetVirtualImageBeginnerGuideResponse) XXX_Size() int {
	return xxx_messageInfo_GetVirtualImageBeginnerGuideResponse.Size(m)
}
func (m *GetVirtualImageBeginnerGuideResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualImageBeginnerGuideResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualImageBeginnerGuideResponse proto.InternalMessageInfo

func (m *GetVirtualImageBeginnerGuideResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetVirtualImageBeginnerGuideResponse) GetHasGuide() bool {
	if m != nil {
		return m.HasGuide
	}
	return false
}

func (m *GetVirtualImageBeginnerGuideResponse) GetWaitSourceTime() uint32 {
	if m != nil {
		return m.WaitSourceTime
	}
	return 0
}

type MarkVirtualImageBeginnerGuideDoneRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MarkVirtualImageBeginnerGuideDoneRequest) Reset() {
	*m = MarkVirtualImageBeginnerGuideDoneRequest{}
}
func (m *MarkVirtualImageBeginnerGuideDoneRequest) String() string { return proto.CompactTextString(m) }
func (*MarkVirtualImageBeginnerGuideDoneRequest) ProtoMessage()    {}
func (*MarkVirtualImageBeginnerGuideDoneRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{98}
}
func (m *MarkVirtualImageBeginnerGuideDoneRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkVirtualImageBeginnerGuideDoneRequest.Unmarshal(m, b)
}
func (m *MarkVirtualImageBeginnerGuideDoneRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkVirtualImageBeginnerGuideDoneRequest.Marshal(b, m, deterministic)
}
func (dst *MarkVirtualImageBeginnerGuideDoneRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkVirtualImageBeginnerGuideDoneRequest.Merge(dst, src)
}
func (m *MarkVirtualImageBeginnerGuideDoneRequest) XXX_Size() int {
	return xxx_messageInfo_MarkVirtualImageBeginnerGuideDoneRequest.Size(m)
}
func (m *MarkVirtualImageBeginnerGuideDoneRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkVirtualImageBeginnerGuideDoneRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MarkVirtualImageBeginnerGuideDoneRequest proto.InternalMessageInfo

func (m *MarkVirtualImageBeginnerGuideDoneRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type MarkVirtualImageBeginnerGuideDoneResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MarkVirtualImageBeginnerGuideDoneResponse) Reset() {
	*m = MarkVirtualImageBeginnerGuideDoneResponse{}
}
func (m *MarkVirtualImageBeginnerGuideDoneResponse) String() string { return proto.CompactTextString(m) }
func (*MarkVirtualImageBeginnerGuideDoneResponse) ProtoMessage()    {}
func (*MarkVirtualImageBeginnerGuideDoneResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_logic_87f31f0198812316, []int{99}
}
func (m *MarkVirtualImageBeginnerGuideDoneResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkVirtualImageBeginnerGuideDoneResponse.Unmarshal(m, b)
}
func (m *MarkVirtualImageBeginnerGuideDoneResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkVirtualImageBeginnerGuideDoneResponse.Marshal(b, m, deterministic)
}
func (dst *MarkVirtualImageBeginnerGuideDoneResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkVirtualImageBeginnerGuideDoneResponse.Merge(dst, src)
}
func (m *MarkVirtualImageBeginnerGuideDoneResponse) XXX_Size() int {
	return xxx_messageInfo_MarkVirtualImageBeginnerGuideDoneResponse.Size(m)
}
func (m *MarkVirtualImageBeginnerGuideDoneResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkVirtualImageBeginnerGuideDoneResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MarkVirtualImageBeginnerGuideDoneResponse proto.InternalMessageInfo

func (m *MarkVirtualImageBeginnerGuideDoneResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func init() {
	proto.RegisterType((*VirtualImageParentCategoryInfo)(nil), "ga.virtual_image_logic.VirtualImageParentCategoryInfo")
	proto.RegisterType((*VirtualImageSubCategoryInfo)(nil), "ga.virtual_image_logic.VirtualImageSubCategoryInfo")
	proto.RegisterType((*VirtualImageResourceCategoryInfo)(nil), "ga.virtual_image_logic.VirtualImageResourceCategoryInfo")
	proto.RegisterType((*GetDefaultResourceListRequest)(nil), "ga.virtual_image_logic.GetDefaultResourceListRequest")
	proto.RegisterType((*MutualExclusion)(nil), "ga.virtual_image_logic.MutualExclusion")
	proto.RegisterType((*GetDefaultResourceListResponse)(nil), "ga.virtual_image_logic.GetDefaultResourceListResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "ga.virtual_image_logic.GetDefaultResourceListResponse.DatingHatMapEntry")
	proto.RegisterMapType((map[uint32]uint32)(nil), "ga.virtual_image_logic.GetDefaultResourceListResponse.FemaleAnimationMapEntry")
	proto.RegisterMapType((map[uint32]uint32)(nil), "ga.virtual_image_logic.GetDefaultResourceListResponse.MaleAnimationMapEntry")
	proto.RegisterMapType((map[uint32]*MutualExclusion)(nil), "ga.virtual_image_logic.GetDefaultResourceListResponse.MapMutualExclusionEntry")
	proto.RegisterType((*GetVirtualImageResourceCategoryRequest)(nil), "ga.virtual_image_logic.GetVirtualImageResourceCategoryRequest")
	proto.RegisterType((*GetVirtualImageResourceCategoryResponse)(nil), "ga.virtual_image_logic.GetVirtualImageResourceCategoryResponse")
	proto.RegisterType((*CommodityDataPackage)(nil), "ga.virtual_image_logic.CommodityDataPackage")
	proto.RegisterType((*VirtualImageActivityInfo)(nil), "ga.virtual_image_logic.VirtualImageActivityInfo")
	proto.RegisterType((*CustomizeLogotype)(nil), "ga.virtual_image_logic.CustomizeLogotype")
	proto.RegisterType((*CommodityData)(nil), "ga.virtual_image_logic.CommodityData")
	proto.RegisterType((*ComputeCommodityPriceRequest)(nil), "ga.virtual_image_logic.ComputeCommodityPriceRequest")
	proto.RegisterType((*ComputeCommodityPriceResponse)(nil), "ga.virtual_image_logic.ComputeCommodityPriceResponse")
	proto.RegisterType((*GetCommodityDataListRequest)(nil), "ga.virtual_image_logic.GetCommodityDataListRequest")
	proto.RegisterType((*GetCommodityDataListResponse)(nil), "ga.virtual_image_logic.GetCommodityDataListResponse")
	proto.RegisterType((*GetRecommendCommodityDataListRequest)(nil), "ga.virtual_image_logic.GetRecommendCommodityDataListRequest")
	proto.RegisterType((*GetRecommendCommodityDataListResponse)(nil), "ga.virtual_image_logic.GetRecommendCommodityDataListResponse")
	proto.RegisterType((*ShoppingItemBasic)(nil), "ga.virtual_image_logic.ShoppingItemBasic")
	proto.RegisterType((*BuyCommodityDataRequest)(nil), "ga.virtual_image_logic.BuyCommodityDataRequest")
	proto.RegisterType((*BuyCommodityDataResponse)(nil), "ga.virtual_image_logic.BuyCommodityDataResponse")
	proto.RegisterType((*GetCommodityDataListByIdRequest)(nil), "ga.virtual_image_logic.GetCommodityDataListByIdRequest")
	proto.RegisterType((*GetCommodityDataListByIdResponse)(nil), "ga.virtual_image_logic.GetCommodityDataListByIdResponse")
	proto.RegisterType((*FreeCommodityGainNotify)(nil), "ga.virtual_image_logic.FreeCommodityGainNotify")
	proto.RegisterType((*GetResourceListRequest)(nil), "ga.virtual_image_logic.GetResourceListRequest")
	proto.RegisterType((*VirtualImageResourceInfo)(nil), "ga.virtual_image_logic.VirtualImageResourceInfo")
	proto.RegisterMapType((map[string]string)(nil), "ga.virtual_image_logic.VirtualImageResourceInfo.CustomMapEntry")
	proto.RegisterMapType((map[string]*SkinInfo)(nil), "ga.virtual_image_logic.VirtualImageResourceInfo.IosSkinMapEntry")
	proto.RegisterMapType((map[string]*SkinInfo)(nil), "ga.virtual_image_logic.VirtualImageResourceInfo.SkinMapEntry")
	proto.RegisterType((*SkinInfo)(nil), "ga.virtual_image_logic.SkinInfo")
	proto.RegisterType((*GetResourceListResponse)(nil), "ga.virtual_image_logic.GetResourceListResponse")
	proto.RegisterType((*GetRedDotAlertStatusRequest)(nil), "ga.virtual_image_logic.GetRedDotAlertStatusRequest")
	proto.RegisterType((*CommodityTabRedDotInfo)(nil), "ga.virtual_image_logic.CommodityTabRedDotInfo")
	proto.RegisterType((*GetRedDotAlertStatusResponse)(nil), "ga.virtual_image_logic.GetRedDotAlertStatusResponse")
	proto.RegisterType((*RedDotAlertReadedRequest)(nil), "ga.virtual_image_logic.RedDotAlertReadedRequest")
	proto.RegisterType((*RedDotAlertReadedResponse)(nil), "ga.virtual_image_logic.RedDotAlertReadedResponse")
	proto.RegisterType((*UserVirtualImageItem)(nil), "ga.virtual_image_logic.UserVirtualImageItem")
	proto.RegisterType((*VirtualImageItemTab)(nil), "ga.virtual_image_logic.VirtualImageItemTab")
	proto.RegisterType((*UserVirtualImageSuit)(nil), "ga.virtual_image_logic.UserVirtualImageSuit")
	proto.RegisterType((*VirtualImageSuitTab)(nil), "ga.virtual_image_logic.VirtualImageSuitTab")
	proto.RegisterType((*VirtualImageAllTabCfg)(nil), "ga.virtual_image_logic.VirtualImageAllTabCfg")
	proto.RegisterType((*GetUserVirtualImageRequest)(nil), "ga.virtual_image_logic.GetUserVirtualImageRequest")
	proto.RegisterType((*GetUserVirtualImageResponse)(nil), "ga.virtual_image_logic.GetUserVirtualImageResponse")
	proto.RegisterType((*GetUserVirtualImagePoseRequest)(nil), "ga.virtual_image_logic.GetUserVirtualImagePoseRequest")
	proto.RegisterType((*GetUserVirtualImagePoseResponse)(nil), "ga.virtual_image_logic.GetUserVirtualImagePoseResponse")
	proto.RegisterType((*UserVirtualImageInuse)(nil), "ga.virtual_image_logic.UserVirtualImageInuse")
	proto.RegisterType((*UserVirtualImageChangeOpt)(nil), "ga.virtual_image_logic.UserVirtualImageChangeOpt")
	proto.RegisterType((*BatchGetUserVirtualImageInuseRequest)(nil), "ga.virtual_image_logic.BatchGetUserVirtualImageInuseRequest")
	proto.RegisterType((*BatchGetUserVirtualImageInuseResponse)(nil), "ga.virtual_image_logic.BatchGetUserVirtualImageInuseResponse")
	proto.RegisterType((*GetUserVirtualImageDisplayRequest)(nil), "ga.virtual_image_logic.GetUserVirtualImageDisplayRequest")
	proto.RegisterType((*GetUserVirtualImageDisplayResponse)(nil), "ga.virtual_image_logic.GetUserVirtualImageDisplayResponse")
	proto.RegisterType((*SetUseItem)(nil), "ga.virtual_image_logic.SetUseItem")
	proto.RegisterType((*SetUserVirtualImageInuseRequest)(nil), "ga.virtual_image_logic.SetUserVirtualImageInuseRequest")
	proto.RegisterType((*SetUserVirtualImageInuseResponse)(nil), "ga.virtual_image_logic.SetUserVirtualImageInuseResponse")
	proto.RegisterType((*SetUserVirtualImageOrientationRequest)(nil), "ga.virtual_image_logic.SetUserVirtualImageOrientationRequest")
	proto.RegisterType((*SetUserVirtualImageOrientationResponse)(nil), "ga.virtual_image_logic.SetUserVirtualImageOrientationResponse")
	proto.RegisterType((*VirtualImageDisplaySwitchInfo)(nil), "ga.virtual_image_logic.VirtualImageDisplaySwitchInfo")
	proto.RegisterType((*SetVirtualImageDisplaySwitchRequest)(nil), "ga.virtual_image_logic.SetVirtualImageDisplaySwitchRequest")
	proto.RegisterType((*SetVirtualImageDisplaySwitchResponse)(nil), "ga.virtual_image_logic.SetVirtualImageDisplaySwitchResponse")
	proto.RegisterType((*UserRelationInfo)(nil), "ga.virtual_image_logic.UserRelationInfo")
	proto.RegisterType((*GetVirtualImageDisplayCfgRequest)(nil), "ga.virtual_image_logic.GetVirtualImageDisplayCfgRequest")
	proto.RegisterType((*GetVirtualImageDisplayCfgResponse)(nil), "ga.virtual_image_logic.GetVirtualImageDisplayCfgResponse")
	proto.RegisterType((*VirtualImagePoseTypeInfo)(nil), "ga.virtual_image_logic.VirtualImagePoseTypeInfo")
	proto.RegisterType((*VirtualImageDisplayStatusPop)(nil), "ga.virtual_image_logic.VirtualImageDisplayStatusPop")
	proto.RegisterType((*VirtualImageDisplayStatusChannelNotify)(nil), "ga.virtual_image_logic.VirtualImageDisplayStatusChannelNotify")
	proto.RegisterType((*GetBindInvitableListRequest)(nil), "ga.virtual_image_logic.GetBindInvitableListRequest")
	proto.RegisterType((*InvitableUser)(nil), "ga.virtual_image_logic.InvitableUser")
	proto.RegisterType((*GetBindInvitableListResponse)(nil), "ga.virtual_image_logic.GetBindInvitableListResponse")
	proto.RegisterType((*SetBindInviteRequest)(nil), "ga.virtual_image_logic.SetBindInviteRequest")
	proto.RegisterType((*SetBindInviteResponse)(nil), "ga.virtual_image_logic.SetBindInviteResponse")
	proto.RegisterType((*BindConfirmActionRequest)(nil), "ga.virtual_image_logic.BindConfirmActionRequest")
	proto.RegisterType((*BindConfirmActionResponse)(nil), "ga.virtual_image_logic.BindConfirmActionResponse")
	proto.RegisterType((*GetBindInviteStatusRequest)(nil), "ga.virtual_image_logic.GetBindInviteStatusRequest")
	proto.RegisterType((*GetBindInviteStatusResponse)(nil), "ga.virtual_image_logic.GetBindInviteStatusResponse")
	proto.RegisterType((*UnbindVirtualImageRequest)(nil), "ga.virtual_image_logic.UnbindVirtualImageRequest")
	proto.RegisterType((*UnbindVirtualImageResponse)(nil), "ga.virtual_image_logic.UnbindVirtualImageResponse")
	proto.RegisterType((*SetVirtualBindInUseRequest)(nil), "ga.virtual_image_logic.SetVirtualBindInUseRequest")
	proto.RegisterType((*SetVirtualBindInUseResponse)(nil), "ga.virtual_image_logic.SetVirtualBindInUseResponse")
	proto.RegisterType((*InviteInfo)(nil), "ga.virtual_image_logic.InviteInfo")
	proto.RegisterType((*GetBindBeInvitedListRequest)(nil), "ga.virtual_image_logic.GetBindBeInvitedListRequest")
	proto.RegisterType((*GetBindBeInvitedListResponse)(nil), "ga.virtual_image_logic.GetBindBeInvitedListResponse")
	proto.RegisterType((*VirtualImageBindInviteMsg)(nil), "ga.virtual_image_logic.VirtualImageBindInviteMsg")
	proto.RegisterType((*ChannelNoticeCfg)(nil), "ga.virtual_image_logic.ChannelNoticeCfg")
	proto.RegisterType((*CheckUserVirtualImageEntranceRequest)(nil), "ga.virtual_image_logic.CheckUserVirtualImageEntranceRequest")
	proto.RegisterType((*CheckUserVirtualImageEntranceResponse)(nil), "ga.virtual_image_logic.CheckUserVirtualImageEntranceResponse")
	proto.RegisterType((*GetVirtualImageCommodityRedDotRequest)(nil), "ga.virtual_image_logic.GetVirtualImageCommodityRedDotRequest")
	proto.RegisterType((*GetVirtualImageCommodityRedDotResponse)(nil), "ga.virtual_image_logic.GetVirtualImageCommodityRedDotResponse")
	proto.RegisterType((*CommodityRedDotInfo)(nil), "ga.virtual_image_logic.CommodityRedDotInfo")
	proto.RegisterType((*GetVirtualImageCommodityRedDotDetailRequest)(nil), "ga.virtual_image_logic.GetVirtualImageCommodityRedDotDetailRequest")
	proto.RegisterType((*GetVirtualImageCommodityRedDotDetailResponse)(nil), "ga.virtual_image_logic.GetVirtualImageCommodityRedDotDetailResponse")
	proto.RegisterType((*GetVirtualImageCardCommonCfgRequest)(nil), "ga.virtual_image_logic.GetVirtualImageCardCommonCfgRequest")
	proto.RegisterType((*AboutToExpireCfg)(nil), "ga.virtual_image_logic.AboutToExpireCfg")
	proto.RegisterType((*GetVirtualImageCardCommonCfgResponse)(nil), "ga.virtual_image_logic.GetVirtualImageCardCommonCfgResponse")
	proto.RegisterType((*GetVirtualImageCardEntryStatusRequest)(nil), "ga.virtual_image_logic.GetVirtualImageCardEntryStatusRequest")
	proto.RegisterType((*GetVirtualImageCardEntryStatusResponse)(nil), "ga.virtual_image_logic.GetVirtualImageCardEntryStatusResponse")
	proto.RegisterType((*VirtualImageCardStatusChangeNotify)(nil), "ga.virtual_image_logic.VirtualImageCardStatusChangeNotify")
	proto.RegisterType((*VirtualImageCardPayResultExt)(nil), "ga.virtual_image_logic.VirtualImageCardPayResultExt")
	proto.RegisterType((*SetVirtualImagePoseTypeRequest)(nil), "ga.virtual_image_logic.SetVirtualImagePoseTypeRequest")
	proto.RegisterType((*SetVirtualImagePoseTypeResponse)(nil), "ga.virtual_image_logic.SetVirtualImagePoseTypeResponse")
	proto.RegisterType((*GetVirtualImageBeginnerGuideRequest)(nil), "ga.virtual_image_logic.GetVirtualImageBeginnerGuideRequest")
	proto.RegisterType((*GetVirtualImageBeginnerGuideResponse)(nil), "ga.virtual_image_logic.GetVirtualImageBeginnerGuideResponse")
	proto.RegisterType((*MarkVirtualImageBeginnerGuideDoneRequest)(nil), "ga.virtual_image_logic.MarkVirtualImageBeginnerGuideDoneRequest")
	proto.RegisterType((*MarkVirtualImageBeginnerGuideDoneResponse)(nil), "ga.virtual_image_logic.MarkVirtualImageBeginnerGuideDoneResponse")
	proto.RegisterEnum("ga.virtual_image_logic.VirtualImageResourceCategoryType", VirtualImageResourceCategoryType_name, VirtualImageResourceCategoryType_value)
	proto.RegisterEnum("ga.virtual_image_logic.VirtualImageResourceSubCategoryType", VirtualImageResourceSubCategoryType_name, VirtualImageResourceSubCategoryType_value)
	proto.RegisterEnum("ga.virtual_image_logic.CommodityGainPath", CommodityGainPath_name, CommodityGainPath_value)
	proto.RegisterEnum("ga.virtual_image_logic.CommodityType", CommodityType_name, CommodityType_value)
	proto.RegisterEnum("ga.virtual_image_logic.VirtualImageResourceType", VirtualImageResourceType_name, VirtualImageResourceType_value)
	proto.RegisterEnum("ga.virtual_image_logic.RedDotAlertType", RedDotAlertType_name, RedDotAlertType_value)
	proto.RegisterEnum("ga.virtual_image_logic.VirtualImagePoseType", VirtualImagePoseType_name, VirtualImagePoseType_value)
	proto.RegisterEnum("ga.virtual_image_logic.VirtualImageRightsType", VirtualImageRightsType_name, VirtualImageRightsType_value)
	proto.RegisterEnum("ga.virtual_image_logic.VirtualImageOrientation", VirtualImageOrientation_name, VirtualImageOrientation_value)
	proto.RegisterEnum("ga.virtual_image_logic.VirtualImageDisplaySwitch", VirtualImageDisplaySwitch_name, VirtualImageDisplaySwitch_value)
	proto.RegisterEnum("ga.virtual_image_logic.InviteAction", InviteAction_name, InviteAction_value)
	proto.RegisterEnum("ga.virtual_image_logic.BindConfirmAction", BindConfirmAction_name, BindConfirmAction_value)
	proto.RegisterEnum("ga.virtual_image_logic.BindInviteStatus", BindInviteStatus_name, BindInviteStatus_value)
	proto.RegisterEnum("ga.virtual_image_logic.VirtualImageCardPayResultExt_ResultType", VirtualImageCardPayResultExt_ResultType_name, VirtualImageCardPayResultExt_ResultType_value)
}

func init() {
	proto.RegisterFile("virtual_image_logic/virtual_image_logic.proto", fileDescriptor_virtual_image_logic_87f31f0198812316)
}

var fileDescriptor_virtual_image_logic_87f31f0198812316 = []byte{
	// 6329 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x7c, 0xcd, 0x6f, 0x23, 0xc9,
	0x75, 0xf8, 0xaf, 0x49, 0x49, 0x24, 0x9f, 0x44, 0xa9, 0xd5, 0xa3, 0x0f, 0x4a, 0x9a, 0x0f, 0x4d,
	0xcf, 0x68, 0x76, 0x46, 0x3b, 0x33, 0xbb, 0x1e, 0xef, 0x2c, 0xd6, 0xc6, 0xcf, 0xd9, 0x50, 0x24,
	0xa5, 0xa1, 0x57, 0x22, 0xe9, 0x26, 0x35, 0xe3, 0x59, 0x24, 0x69, 0x37, 0xd9, 0x45, 0xaa, 0x3d,
	0xcd, 0x6e, 0x6e, 0x77, 0x73, 0x24, 0x39, 0x87, 0x18, 0x09, 0x90, 0x4b, 0xe0, 0x1c, 0x12, 0x07,
	0x30, 0x02, 0x1f, 0x12, 0xc0, 0x39, 0x18, 0x30, 0xec, 0xa3, 0x91, 0xfc, 0x01, 0x49, 0x80, 0x38,
	0xb9, 0x05, 0x49, 0x80, 0x20, 0x27, 0x23, 0x48, 0x72, 0x08, 0x72, 0x0a, 0x90, 0x9c, 0x82, 0x7a,
	0xd5, 0xdd, 0xec, 0x2f, 0x52, 0xa2, 0x66, 0x6c, 0xec, 0x8d, 0xfd, 0xea, 0xd5, 0xab, 0xaa, 0x57,
	0xef, 0xbb, 0xaa, 0x08, 0x8f, 0x5e, 0x6b, 0x96, 0x33, 0x54, 0x74, 0x59, 0xeb, 0x2b, 0x3d, 0x22,
	0xeb, 0x66, 0x4f, 0xeb, 0xbc, 0x97, 0x00, 0x7b, 0x3c, 0xb0, 0x4c, 0xc7, 0x14, 0xd6, 0x7a, 0xca,
	0xe3, 0x84, 0xd6, 0xcd, 0x7c, 0x4f, 0x91, 0xdb, 0x8a, 0x4d, 0x18, 0x9a, 0xf8, 0xbb, 0x1c, 0xdc,
	0x7c, 0xce, 0xd0, 0xaa, 0x14, 0xab, 0xa1, 0x58, 0xc4, 0x70, 0x4a, 0x8a, 0x43, 0x7a, 0xa6, 0x75,
	0x5e, 0x35, 0xba, 0xa6, 0xb0, 0x09, 0xd9, 0x8e, 0xfb, 0x5d, 0xe0, 0xb6, 0xb9, 0xfb, 0x79, 0xc9,
	0xff, 0x16, 0xee, 0x40, 0xde, 0xfb, 0x2d, 0x1b, 0x4a, 0x9f, 0x14, 0x52, 0xdb, 0xdc, 0xfd, 0x9c,
	0xb4, 0xe0, 0x01, 0x6b, 0x4a, 0x9f, 0x84, 0x90, 0x9c, 0xf3, 0x01, 0x29, 0xa4, 0x91, 0x8a, 0x8f,
	0xd4, 0x3a, 0x1f, 0x10, 0xf1, 0xb7, 0x53, 0xb0, 0x15, 0x9c, 0x48, 0x73, 0xd8, 0x0e, 0xcd, 0xe2,
	0x36, 0x2c, 0xd8, 0xc3, 0xb6, 0x1c, 0x99, 0xc9, 0xbc, 0x3d, 0x42, 0x13, 0x76, 0x61, 0x39, 0x88,
	0x12, 0x9c, 0xd0, 0x52, 0x00, 0x0f, 0xe7, 0xf4, 0x1e, 0xac, 0x84, 0x70, 0xb5, 0x7e, 0x4f, 0x1e,
	0x5a, 0x3a, 0x4e, 0x2d, 0x27, 0x2d, 0x07, 0xd0, 0xab, 0xfd, 0xde, 0xb1, 0xa5, 0xc7, 0x17, 0x31,
	0x13, 0x5f, 0x84, 0xf0, 0x2b, 0x70, 0x3d, 0x46, 0x75, 0x60, 0x91, 0xd7, 0x1a, 0x39, 0x45, 0xea,
	0xb3, 0x48, 0xbd, 0x10, 0xa6, 0xde, 0x60, 0x08, 0xc7, 0x96, 0x2e, 0xfe, 0x37, 0x07, 0xdb, 0x41,
	0x26, 0x48, 0xc4, 0x36, 0x87, 0x56, 0x87, 0x84, 0x38, 0x71, 0x02, 0x2b, 0x03, 0xdc, 0xa5, 0xc0,
	0x38, 0x46, 0xd7, 0x44, 0x8e, 0xcc, 0x3f, 0xf9, 0xf0, 0x71, 0xf2, 0xc6, 0x3f, 0x9e, 0xbc, 0xcb,
	0x92, 0x30, 0x88, 0xef, 0xfc, 0x09, 0xac, 0x85, 0x97, 0x63, 0x74, 0x4d, 0x59, 0xd7, 0x6c, 0xa7,
	0x90, 0xda, 0x4e, 0xdf, 0x9f, 0x7f, 0xf2, 0xc5, 0xcb, 0x8c, 0x15, 0xd9, 0x48, 0xe9, 0x9a, 0x1d,
	0x06, 0x1c, 0x6a, 0xb6, 0x23, 0x1e, 0xc0, 0x8d, 0x03, 0xe2, 0x94, 0x49, 0x57, 0x19, 0xea, 0x8e,
	0xb7, 0x6a, 0xda, 0x22, 0x91, 0xcf, 0x86, 0xc4, 0x76, 0x84, 0x7b, 0x90, 0xa5, 0x52, 0x2b, 0x5b,
	0xe4, 0x33, 0x77, 0xa1, 0xf3, 0x74, 0xf0, 0x3d, 0xc5, 0x26, 0x12, 0xf9, 0x4c, 0xca, 0xb4, 0xd9,
	0x0f, 0xf1, 0x11, 0x2c, 0x1d, 0x0d, 0xe9, 0xd8, 0x95, 0xb3, 0x8e, 0x3e, 0xb4, 0x35, 0xd3, 0x88,
	0xc8, 0x6f, 0x3a, 0x28, 0xbf, 0xe2, 0x5f, 0x67, 0xe1, 0xe6, 0xb8, 0x81, 0xed, 0x81, 0x69, 0xd8,
	0x44, 0x78, 0x00, 0x39, 0x77, 0x64, 0x7b, 0xe0, 0x0e, 0xbd, 0x30, 0x1a, 0xda, 0x1e, 0x48, 0xd9,
	0xb6, 0xfb, 0x4b, 0xd8, 0x81, 0xc5, 0xbe, 0xa2, 0x23, 0x2a, 0xd2, 0xb1, 0x91, 0x4f, 0x79, 0x29,
	0x4f, 0xa1, 0x1e, 0x71, 0x5b, 0x78, 0x00, 0x7c, 0x97, 0x44, 0x10, 0xd3, 0x88, 0xb8, 0xc4, 0xe0,
	0x23, 0xd4, 0x75, 0xc8, 0x98, 0x43, 0x47, 0xee, 0x6a, 0x4e, 0x61, 0x06, 0x31, 0xe6, 0xcc, 0xa1,
	0xb3, 0xaf, 0x39, 0xc2, 0xb7, 0x40, 0x40, 0x0a, 0x8a, 0xa1, 0xf5, 0x15, 0x47, 0x33, 0x0d, 0xb9,
	0xaf, 0x0c, 0x0a, 0xb3, 0xb8, 0x2d, 0x87, 0xe3, 0xb6, 0x65, 0xf2, 0x4a, 0x1f, 0x1f, 0x29, 0x3a,
	0x29, 0x7a, 0xf4, 0x8e, 0x94, 0x41, 0xc5, 0x70, 0xac, 0x73, 0x89, 0xef, 0x47, 0xc0, 0xc2, 0xb7,
	0x39, 0x58, 0x71, 0x17, 0x10, 0x1e, 0x7e, 0x0e, 0x87, 0xaf, 0x5d, 0x71, 0xf8, 0x7d, 0xd2, 0x4f,
	0x9c, 0x80, 0xd0, 0x8d, 0x35, 0x08, 0x06, 0x2c, 0xaa, 0x8a, 0xa3, 0x19, 0x3d, 0xf9, 0x44, 0x71,
	0x70, 0xec, 0x0c, 0x8e, 0xfd, 0xec, 0x8a, 0x63, 0x97, 0x91, 0xd8, 0x33, 0xc5, 0xf1, 0x47, 0x5d,
	0x50, 0x03, 0x20, 0x61, 0x1b, 0x16, 0x86, 0x86, 0x6c, 0x9f, 0x98, 0xa7, 0x4c, 0xfe, 0xb3, 0xb8,
	0x19, 0x30, 0x34, 0x9a, 0x27, 0xe6, 0x29, 0xa5, 0x84, 0x4c, 0xe9, 0x2b, 0x03, 0xb9, 0x8f, 0xd2,
	0x27, 0x13, 0x4f, 0xfc, 0x0a, 0xb9, 0x37, 0x62, 0xca, 0x91, 0x32, 0x88, 0xc8, 0xb3, 0xcb, 0x94,
	0x7e, 0xac, 0x41, 0xd8, 0x82, 0x5c, 0x6f, 0xa8, 0xa9, 0x04, 0x4d, 0x0d, 0xa0, 0xa9, 0xc9, 0x22,
	0x80, 0xda, 0x2f, 0x11, 0xf2, 0x7e, 0xa3, 0xdc, 0x57, 0x9f, 0x16, 0xe6, 0x11, 0x61, 0xde, 0x43,
	0x38, 0x52, 0x9f, 0x6e, 0x96, 0x60, 0x35, 0x51, 0x06, 0x04, 0x1e, 0xd2, 0xaf, 0x88, 0x67, 0x73,
	0xe9, 0x4f, 0x61, 0x05, 0x66, 0x5f, 0x2b, 0xfa, 0x90, 0xd9, 0xd7, 0xbc, 0xc4, 0x3e, 0xbe, 0x9c,
	0xfa, 0x88, 0xdb, 0xac, 0xc0, 0xfa, 0x98, 0x9d, 0x9c, 0x8a, 0xcc, 0xc7, 0xb0, 0x1c, 0xdb, 0x94,
	0xa9, 0x08, 0x18, 0xb0, 0x3e, 0x86, 0x79, 0x09, 0x64, 0xbe, 0x12, 0x24, 0x33, 0xff, 0xe4, 0x9d,
	0x71, 0xbb, 0x15, 0x21, 0x17, 0x18, 0x4f, 0x6c, 0xc0, 0xbd, 0x03, 0xe2, 0x4c, 0xb2, 0xde, 0xd3,
	0xda, 0xb2, 0x9f, 0x73, 0xf0, 0xce, 0x85, 0x24, 0xa7, 0xb7, 0x52, 0xa7, 0xb0, 0xe5, 0xd9, 0x9d,
	0xf1, 0xa6, 0xfd, 0xa3, 0xcb, 0x98, 0xf6, 0x24, 0xf7, 0x24, 0x15, 0xac, 0x04, 0x28, 0xaa, 0xc8,
	0x2a, 0xcc, 0x69, 0xb6, 0x6c, 0x90, 0x53, 0xf4, 0xb2, 0x59, 0x69, 0x56, 0xb3, 0x6b, 0xe4, 0x54,
	0xfc, 0x59, 0x0a, 0x56, 0x4a, 0x66, 0xbf, 0x6f, 0xaa, 0x9a, 0x73, 0x5e, 0x56, 0x1c, 0xa5, 0xa1,
	0x74, 0x5e, 0x29, 0x3d, 0x22, 0xdc, 0x00, 0x18, 0xb0, 0x9f, 0xb2, 0xa6, 0xba, 0xbb, 0x95, 0x73,
	0x21, 0x55, 0x95, 0x6e, 0xfd, 0xc0, 0xd2, 0x3a, 0xfe, 0xd6, 0xe3, 0x07, 0xb5, 0xc1, 0xaa, 0x66,
	0x77, 0xcc, 0xa1, 0xe1, 0xc8, 0xac, 0x99, 0x45, 0x1b, 0x79, 0x0f, 0xda, 0x40, 0xb4, 0x3b, 0xe0,
	0x03, 0x64, 0x4b, 0x71, 0x7c, 0x77, 0xee, 0x01, 0x25, 0xc5, 0x41, 0x24, 0xd2, 0xed, 0x92, 0x8e,
	0xa3, 0xbd, 0x26, 0xb2, 0xaa, 0x9c, 0xa3, 0xff, 0xce, 0x4b, 0x0b, 0x3e, 0xb0, 0xac, 0x9c, 0x0b,
	0xdb, 0x30, 0xaf, 0x12, 0xbb, 0x63, 0x69, 0x03, 0x2a, 0xeb, 0x85, 0x39, 0xa6, 0x56, 0x01, 0x90,
	0x70, 0x0b, 0xe6, 0xc9, 0xd9, 0x40, 0xb3, 0x88, 0xec, 0x68, 0x7d, 0x52, 0xc8, 0x20, 0x11, 0x60,
	0xa0, 0x96, 0xd6, 0x27, 0x34, 0xb6, 0xd1, 0x6c, 0x79, 0x40, 0xac, 0x01, 0xa1, 0xbc, 0x2d, 0x64,
	0x91, 0x3d, 0xf3, 0x9a, 0xdd, 0xf0, 0x40, 0xc2, 0x7d, 0xe0, 0xd1, 0xfa, 0x04, 0x09, 0xe5, 0x10,
	0x6d, 0x91, 0xc2, 0x2b, 0x3e, 0x31, 0x51, 0x86, 0x42, 0x70, 0x8f, 0x8a, 0x74, 0xa2, 0x9a, 0xc3,
	0x1c, 0xba, 0x00, 0x33, 0x74, 0x62, 0xc8, 0xcb, 0x9c, 0x84, 0xbf, 0xa9, 0x32, 0x50, 0x7b, 0xc1,
	0xe2, 0x24, 0xfa, 0x93, 0xf2, 0x9d, 0xb6, 0xc8, 0x1d, 0x53, 0x37, 0x2d, 0x37, 0x22, 0xca, 0x51,
	0x48, 0x89, 0x02, 0xc4, 0x3f, 0xe0, 0x60, 0xb9, 0x34, 0xb4, 0x1d, 0xb3, 0xaf, 0x7d, 0x8b, 0x1c,
	0x9a, 0x3d, 0x93, 0x86, 0x43, 0xd4, 0xcb, 0xea, 0xee, 0x6f, 0x97, 0xbc, 0xff, 0x4d, 0x09, 0xda,
	0x27, 0x44, 0xef, 0xb2, 0x69, 0xb3, 0xed, 0xca, 0x21, 0x04, 0x97, 0x1f, 0xe1, 0x4f, 0xfa, 0x42,
	0xfe, 0xcc, 0xc4, 0xf8, 0x23, 0x7e, 0x6f, 0x1e, 0xf2, 0x21, 0x21, 0xa2, 0x9d, 0x3a, 0x1e, 0x60,
	0x24, 0x3f, 0xf3, 0x3e, 0xac, 0xaa, 0xd2, 0x79, 0xe9, 0xe4, 0x35, 0xd1, 0x65, 0xad, 0x63, 0x1a,
	0x2e, 0x07, 0x72, 0x08, 0xa9, 0x76, 0x4c, 0x83, 0x0a, 0x18, 0x7e, 0xb8, 0x33, 0x62, 0x1f, 0xa1,
	0x85, 0xce, 0x44, 0x16, 0xba, 0x03, 0x8b, 0x81, 0x31, 0x29, 0x51, 0x16, 0xf1, 0xe5, 0x47, 0xa3,
	0x52, 0xc2, 0xef, 0xc1, 0xb5, 0x11, 0x9a, 0xef, 0x42, 0x5d, 0xd1, 0x11, 0xfc, 0x26, 0xdf, 0x80,
	0x86, 0xe9, 0x62, 0x58, 0x9b, 0x89, 0xd0, 0xc5, 0xa0, 0x96, 0x3a, 0x00, 0x45, 0x33, 0xe4, 0x81,
	0xe2, 0x9c, 0xa0, 0x10, 0xe5, 0xa5, 0x2c, 0x05, 0x34, 0x14, 0xe7, 0x44, 0x38, 0x86, 0xbc, 0xaf,
	0xf6, 0xa8, 0xe8, 0xcc, 0x31, 0xbd, 0x3f, 0x8d, 0xa2, 0xa3, 0x82, 0x2f, 0x58, 0x01, 0x47, 0x15,
	0x8a, 0xae, 0x20, 0x92, 0x1d, 0x44, 0x63, 0xf6, 0xf9, 0x78, 0xcc, 0x1e, 0x5a, 0x19, 0xf2, 0x74,
	0x81, 0xa9, 0xab, 0x0f, 0xc5, 0xc0, 0x5a, 0x80, 0x19, 0x4b, 0x31, 0x5e, 0x15, 0xf2, 0xd8, 0x88,
	0xbf, 0x29, 0x75, 0x7f, 0x41, 0x36, 0x39, 0x2b, 0xf0, 0x8c, 0xba, 0x07, 0x6b, 0x92, 0x33, 0xe1,
	0x53, 0x10, 0xd0, 0x06, 0xc8, 0x9e, 0x1d, 0xc1, 0x85, 0x2f, 0xe3, 0xc2, 0x1f, 0x8e, 0x5b, 0x78,
	0x92, 0x2d, 0x92, 0x78, 0xa4, 0xe3, 0x7e, 0xe1, 0xc2, 0x3f, 0x81, 0xac, 0xd2, 0x71, 0x58, 0xe8,
	0x2d, 0xa0, 0xc1, 0xbd, 0x14, 0x2b, 0x83, 0xfa, 0x28, 0x65, 0x94, 0x8e, 0x83, 0x8a, 0x19, 0x51,
	0x81, 0x6b, 0x31, 0x15, 0x78, 0x07, 0x96, 0xec, 0x81, 0x66, 0x04, 0x22, 0xae, 0xc2, 0x0a, 0x8a,
	0xc0, 0x22, 0x82, 0x47, 0xa2, 0xc2, 0x8c, 0x6c, 0x8f, 0x38, 0x85, 0x55, 0xcf, 0xc8, 0x1e, 0x10,
	0x47, 0xf8, 0x10, 0xd6, 0x13, 0x44, 0x4e, 0x76, 0xcc, 0x41, 0x61, 0x0d, 0xe9, 0xac, 0xc6, 0xc5,
	0xae, 0x65, 0x0e, 0x84, 0xaf, 0xc0, 0xd6, 0x98, 0x7e, 0x18, 0x44, 0xac, 0xb3, 0x84, 0x26, 0xb1,
	0xef, 0x91, 0xfa, 0x54, 0xf8, 0x08, 0x0a, 0x49, 0xdd, 0xdb, 0x4a, 0xe7, 0x55, 0xa1, 0x80, 0x7d,
	0xd7, 0xe2, 0x7d, 0xf7, 0x94, 0xce, 0x2b, 0xe1, 0x63, 0xb8, 0x3e, 0xae, 0x27, 0x8e, 0xbc, 0x81,
	0xbd, 0x37, 0x92, 0x7b, 0xd3, 0xa1, 0xef, 0x03, 0x3f, 0xb4, 0x89, 0x15, 0xb2, 0x98, 0x9b, 0xc8,
	0xd7, 0x45, 0x0a, 0x1f, 0x59, 0x4c, 0x9a, 0x37, 0x22, 0x66, 0xc8, 0xc6, 0x6c, 0x21, 0xf7, 0x96,
	0x68, 0x43, 0x35, 0x60, 0x87, 0x45, 0xc8, 0x23, 0x6e, 0x7b, 0x78, 0xce, 0x48, 0x5e, 0x67, 0x52,
	0x47, 0x81, 0x7b, 0xc3, 0x73, 0xa4, 0x17, 0x36, 0x77, 0x37, 0xa2, 0xe6, 0xee, 0x61, 0x82, 0x29,
	0xbf, 0x49, 0x47, 0xdb, 0x4b, 0x15, 0xb8, 0xa8, 0x39, 0x17, 0xee, 0xc1, 0x92, 0x45, 0x54, 0x59,
	0x35, 0x1d, 0xf9, 0x35, 0xb1, 0x30, 0xa2, 0xbc, 0xc5, 0x34, 0xc4, 0x22, 0x6a, 0xd9, 0x74, 0x9e,
	0x33, 0xa0, 0xf0, 0x3e, 0xac, 0x0c, 0x2c, 0xb3, 0x6f, 0x52, 0x16, 0x28, 0xba, 0xfc, 0x5a, 0x53,
	0x89, 0x49, 0xcd, 0xde, 0x36, 0x22, 0x0b, 0x81, 0xb6, 0xe7, 0xb4, 0x29, 0x68, 0xfd, 0x4e, 0x49,
	0x7b, 0x50, 0xb8, 0x1d, 0xb0, 0x7e, 0x2f, 0x48, 0x7b, 0xe0, 0x1a, 0x5d, 0x8b, 0x50, 0x0e, 0x13,
	0x43, 0x2d, 0x88, 0x9e, 0xd1, 0x95, 0x3c, 0x90, 0x70, 0x17, 0x16, 0xed, 0xa1, 0xe6, 0xc8, 0x43,
	0x43, 0xfb, 0x6c, 0x88, 0x4e, 0xfa, 0x0e, 0x4b, 0xff, 0x29, 0xf4, 0x18, 0x81, 0x55, 0x55, 0xfc,
	0x53, 0x0e, 0xae, 0x97, 0xcc, 0xfe, 0x60, 0xe8, 0x10, 0x5f, 0xb5, 0xd0, 0x09, 0x4f, 0x19, 0x0f,
	0x09, 0x2f, 0x83, 0x66, 0x53, 0x73, 0x48, 0x3f, 0x18, 0xb0, 0x3c, 0x18, 0xa7, 0x7c, 0xcd, 0x13,
	0x73, 0x30, 0xd0, 0x8c, 0x5e, 0xd5, 0x21, 0xfd, 0x3d, 0xc5, 0xd6, 0x3a, 0xd2, 0xf2, 0xc8, 0x1a,
	0x3b, 0xa4, 0x8f, 0xf9, 0xe7, 0x8f, 0x53, 0x70, 0x63, 0xcc, 0x1c, 0xa7, 0x0f, 0xb0, 0x7e, 0x71,
	0xf3, 0xa4, 0x76, 0xc2, 0x31, 0x1d, 0x45, 0x0f, 0x85, 0x36, 0x80, 0x20, 0x16, 0xd7, 0x74, 0x61,
	0xd3, 0x95, 0xab, 0xa4, 0x29, 0xcc, 0x4c, 0x3b, 0x85, 0x75, 0x46, 0xac, 0x14, 0x63, 0xd8, 0xdf,
	0x73, 0xb0, 0x75, 0x40, 0x9c, 0x90, 0xad, 0xbc, 0x42, 0xbe, 0x1e, 0x72, 0x1f, 0xa9, 0x0b, 0xdc,
	0x47, 0x3a, 0xee, 0x3e, 0xee, 0x03, 0xef, 0xfb, 0x00, 0x4d, 0x1d, 0x2d, 0x32, 0x2f, 0x2d, 0x7a,
	0xf0, 0xaa, 0x8a, 0x9c, 0x8b, 0x3b, 0x9a, 0xd9, 0x04, 0x47, 0x23, 0xfe, 0x09, 0x07, 0xd7, 0x93,
	0xd7, 0x35, 0xbd, 0x1c, 0x1c, 0x07, 0xe5, 0x40, 0x55, 0x1c, 0x25, 0x28, 0x07, 0x3b, 0x97, 0x72,
	0x3f, 0x01, 0x19, 0xf0, 0x66, 0x22, 0xd6, 0xe0, 0xee, 0x01, 0x71, 0x7c, 0x2d, 0x7c, 0xd3, 0x2d,
	0x10, 0x7f, 0xc8, 0xc1, 0xce, 0x05, 0x04, 0x3f, 0x37, 0x6b, 0xff, 0x3b, 0x0e, 0x96, 0x63, 0x52,
	0x1a, 0x0b, 0xf5, 0x52, 0x89, 0xa1, 0x5e, 0x20, 0x97, 0x48, 0x27, 0xe4, 0x12, 0x18, 0xf6, 0xbb,
	0x69, 0x00, 0xfb, 0x88, 0x6a, 0xdb, 0x6c, 0x4c, 0xdb, 0xb6, 0x20, 0xa7, 0xbc, 0xee, 0xb9, 0xcd,
	0x73, 0x4c, 0x7c, 0x95, 0xd7, 0x3d, 0x3f, 0xc5, 0x08, 0x67, 0x0f, 0x99, 0x78, 0xf6, 0x20, 0x7e,
	0x9f, 0x83, 0xf5, 0xbd, 0xe1, 0x79, 0x78, 0xe1, 0x9f, 0x1f, 0xbb, 0xf8, 0x33, 0x0e, 0x0a, 0xf1,
	0xe9, 0x4d, 0x2f, 0x0e, 0xdf, 0x7c, 0xab, 0x66, 0x09, 0x1d, 0xe5, 0x38, 0xd3, 0x24, 0x6c, 0x40,
	0xd6, 0xb4, 0x54, 0xea, 0xcf, 0x55, 0x37, 0xa6, 0xcf, 0xe0, 0x77, 0x55, 0x15, 0x7f, 0xc0, 0xc1,
	0xad, 0x24, 0xed, 0xde, 0x3b, 0xaf, 0xaa, 0x9f, 0x23, 0xae, 0xff, 0x80, 0x83, 0xed, 0xf1, 0xd3,
	0xfc, 0xdc, 0x28, 0xe3, 0x77, 0x39, 0x58, 0xdf, 0xb7, 0xc8, 0x68, 0x0b, 0x0e, 0x14, 0xcd, 0xa8,
	0x99, 0x8e, 0xd6, 0xc5, 0x12, 0xcb, 0xd0, 0x4f, 0xba, 0xe8, 0x4f, 0x0a, 0xe9, 0x18, 0x8e, 0xab,
	0x9b, 0xf4, 0xe7, 0xb8, 0x69, 0xa5, 0xdf, 0x70, 0x5a, 0x7f, 0xc4, 0xc1, 0x1a, 0xda, 0xb3, 0x2b,
	0x57, 0x91, 0x85, 0x35, 0x98, 0x33, 0xbb, 0x5d, 0x9b, 0x78, 0xd3, 0x75, 0xbf, 0x30, 0x23, 0xd4,
	0xfa, 0x9a, 0xe3, 0x67, 0x84, 0xf4, 0x83, 0xba, 0x16, 0x5d, 0x71, 0x88, 0x3d, 0x8a, 0xd0, 0x98,
	0x15, 0xc9, 0x33, 0xa8, 0x1b, 0xa1, 0x89, 0xff, 0x0a, 0xe1, 0xcc, 0x3c, 0x98, 0x54, 0x09, 0x8b,
	0x90, 0xf2, 0xd9, 0x95, 0xd2, 0x54, 0x6a, 0x59, 0xec, 0x57, 0x9a, 0x11, 0x3c, 0xc3, 0xc8, 0x52,
	0x00, 0xe6, 0x79, 0xc1, 0xcc, 0x67, 0x74, 0x68, 0xe1, 0x67, 0x3e, 0xc7, 0x96, 0x2e, 0x14, 0x20,
	0x13, 0x9e, 0x8c, 0xf7, 0x29, 0x5c, 0x87, 0x1c, 0xb1, 0x6d, 0x62, 0x38, 0x9a, 0xc2, 0x0e, 0x24,
	0xb2, 0xd2, 0x08, 0x10, 0x89, 0x5d, 0xe7, 0x2e, 0x48, 0xd5, 0xe3, 0xa5, 0x0c, 0x1e, 0xd2, 0x34,
	0x3a, 0xcf, 0xb2, 0x6a, 0x42, 0x5f, 0x7d, 0x8a, 0x5d, 0x8c, 0x8e, 0x75, 0x3e, 0x70, 0xe4, 0x57,
	0xe4, 0x1c, 0x8b, 0x16, 0x39, 0x09, 0x5c, 0xd0, 0x27, 0x04, 0xcf, 0x90, 0xfc, 0xd5, 0xe0, 0x72,
	0x59, 0xe9, 0xd2, 0x5f, 0xa2, 0x77, 0x86, 0xe4, 0x23, 0xa1, 0xf7, 0x66, 0xb9, 0xa4, 0x8f, 0x84,
	0x59, 0xe2, 0x6d, 0x58, 0x50, 0x35, 0x7b, 0xa0, 0x2b, 0x6e, 0x92, 0xbc, 0xe0, 0xd6, 0x62, 0x18,
	0x0c, 0xe9, 0x6c, 0x40, 0x96, 0xe6, 0xe5, 0xc8, 0xb6, 0x3c, 0x33, 0x0e, 0xf4, 0x9b, 0xb2, 0x2c,
	0x18, 0x8a, 0x2c, 0x5e, 0x10, 0x8a, 0x2c, 0xc5, 0x43, 0x91, 0x70, 0x31, 0x81, 0x1f, 0x5b, 0x4c,
	0x58, 0x0e, 0x16, 0x13, 0x78, 0x48, 0xd3, 0xd4, 0x55, 0x60, 0x4a, 0x61, 0x93, 0xb3, 0x48, 0x54,
	0x7e, 0x2d, 0x1a, 0x95, 0xd3, 0xfd, 0xe9, 0x60, 0xe5, 0xbd, 0xad, 0x13, 0x4c, 0x01, 0xb3, 0x52,
	0x0e, 0x21, 0xc5, 0xb6, 0x4e, 0x84, 0x77, 0x61, 0x59, 0x65, 0xd5, 0xe4, 0x40, 0xa2, 0xb8, 0x8a,
	0x44, 0x78, 0xb7, 0x61, 0x94, 0x2a, 0xde, 0x82, 0x79, 0x94, 0xb1, 0xae, 0xd2, 0xd1, 0x8c, 0x1e,
	0xe6, 0x81, 0x79, 0x09, 0x28, 0x68, 0x1f, 0x21, 0x34, 0xe9, 0xf4, 0x99, 0x3e, 0xb0, 0x48, 0x57,
	0x3b, 0x73, 0x13, 0x3e, 0x3f, 0xb8, 0x6a, 0x20, 0x54, 0xf8, 0x0d, 0x80, 0x0e, 0x56, 0x84, 0xb0,
	0x14, 0x5f, 0x40, 0x05, 0xfe, 0x78, 0xda, 0xc2, 0xc2, 0x63, 0x56, 0x54, 0xf2, 0x2b, 0xf0, 0xb9,
	0x8e, 0xf7, 0x2d, 0x7c, 0x1d, 0x50, 0xf8, 0x91, 0xfa, 0x26, 0x52, 0xff, 0xca, 0xd4, 0xd4, 0x9b,
	0xaf, 0xb4, 0xd1, 0x99, 0x42, 0xc6, 0x66, 0x5f, 0x42, 0x1b, 0x16, 0x34, 0xd3, 0x96, 0x7d, 0xea,
	0x5b, 0x48, 0xfd, 0x57, 0xa7, 0xa6, 0x5e, 0x35, 0xed, 0xd0, 0x00, 0xa0, 0xf9, 0x00, 0x1a, 0xa4,
	0xd2, 0x31, 0x42, 0x2a, 0x7b, 0x9d, 0xf1, 0x51, 0x33, 0x6d, 0x29, 0xa0, 0xb5, 0xb7, 0x60, 0x9e,
	0x62, 0x7a, 0x9a, 0xcb, 0x52, 0x47, 0x4a, 0xca, 0xcb, 0xf2, 0xee, 0xc1, 0x12, 0x2b, 0xa1, 0xfa,
	0xd4, 0x58, 0xea, 0x28, 0xe5, 0xb1, 0x96, 0xea, 0xd1, 0xda, 0xfc, 0xff, 0xb0, 0x18, 0xe6, 0x66,
	0xb0, 0xe6, 0x9d, 0x4b, 0x28, 0x9d, 0xe7, 0x82, 0xa5, 0xf3, 0x5f, 0x83, 0x85, 0xe0, 0x62, 0x12,
	0xfa, 0x7e, 0x18, 0xae, 0x97, 0x6f, 0x8f, 0x75, 0x77, 0xaf, 0x34, 0x03, 0x2b, 0x1d, 0x01, 0xea,
	0x32, 0x2c, 0x45, 0xb8, 0xf5, 0x76, 0x07, 0x10, 0x3f, 0x85, 0xac, 0x07, 0xf6, 0xaa, 0x9b, 0xdc,
	0xa8, 0xba, 0xb9, 0x0b, 0xcb, 0x7d, 0xcd, 0x90, 0xdb, 0xa6, 0x41, 0x46, 0x9c, 0x66, 0x66, 0x7e,
	0xa9, 0xaf, 0x19, 0x7b, 0x14, 0xee, 0xb1, 0xdb, 0xb5, 0x66, 0x69, 0xdf, 0x9a, 0x89, 0x7f, 0x99,
	0x82, 0xf5, 0x98, 0x73, 0x99, 0xde, 0x23, 0x4f, 0xe7, 0x60, 0x6a, 0x90, 0x1b, 0x9d, 0x14, 0xce,
	0x5c, 0xb1, 0x6c, 0x37, 0x22, 0x91, 0xe0, 0xb0, 0x66, 0x13, 0x1c, 0x96, 0x5b, 0x4a, 0xa2, 0xb9,
	0xff, 0x9c, 0x57, 0x4a, 0xaa, 0x18, 0x2a, 0x5a, 0x59, 0xf3, 0xd4, 0xd0, 0x4d, 0x45, 0x45, 0x51,
	0xce, 0xb8, 0x56, 0xd6, 0x85, 0x51, 0x39, 0x0e, 0xa2, 0x8c, 0xdc, 0x81, 0x8f, 0x72, 0xa4, 0x3e,
	0x15, 0x7f, 0xca, 0x12, 0x48, 0x09, 0x8b, 0x18, 0x45, 0x9d, 0x58, 0x4e, 0xd3, 0x51, 0x9c, 0xa1,
	0x3d, 0xad, 0xab, 0x7e, 0x00, 0xcb, 0x5e, 0x7d, 0x44, 0xd1, 0x89, 0xc5, 0x9c, 0x03, 0x63, 0xea,
	0xa2, 0xe5, 0x13, 0x47, 0xf7, 0x70, 0x33, 0x60, 0xe0, 0x91, 0xbf, 0x18, 0x47, 0x8e, 0x8c, 0xfc,
	0x4e, 0xc4, 0xc8, 0xcf, 0xf8, 0x38, 0x41, 0x43, 0x2f, 0x9e, 0xc2, 0x9a, 0x1f, 0x83, 0xb4, 0x94,
	0x36, 0x5b, 0xc1, 0x85, 0x37, 0x25, 0xa2, 0x1e, 0x24, 0x15, 0xf7, 0x20, 0x37, 0x61, 0xfe, 0x44,
	0xa1, 0x76, 0x02, 0x97, 0xe3, 0x1e, 0x92, 0xe4, 0x4e, 0x14, 0x9b, 0x0d, 0x21, 0x7e, 0x3b, 0x85,
	0xb9, 0x69, 0x02, 0xcb, 0xa6, 0x17, 0xc0, 0xa9, 0xd8, 0x36, 0x71, 0x5a, 0x34, 0xb6, 0x0f, 0x64,
	0xd6, 0x4a, 0xdb, 0xc3, 0x0c, 0xc6, 0xf6, 0x8f, 0x2f, 0x8c, 0xe6, 0x42, 0x9c, 0x0c, 0x54, 0x05,
	0x7d, 0x38, 0xc6, 0x76, 0x3f, 0xe2, 0xa0, 0x10, 0x58, 0xbf, 0x44, 0x14, 0x95, 0xa8, 0xbf, 0x40,
	0x91, 0xd9, 0x8c, 0x8a, 0xcc, 0x84, 0x1d, 0x9d, 0x89, 0xed, 0xa8, 0xb8, 0x0f, 0x1b, 0x09, 0xb3,
	0x9d, 0x7a, 0xb7, 0xc4, 0x9f, 0x70, 0xb0, 0x72, 0x6c, 0x13, 0x2b, 0xa8, 0xdc, 0x34, 0x5b, 0xa0,
	0x0e, 0x23, 0x50, 0xff, 0x70, 0x85, 0x0e, 0x46, 0xa5, 0x0f, 0x1a, 0x47, 0x7a, 0x01, 0x9b, 0x8d,
	0x6b, 0x4c, 0x4b, 0x59, 0x37, 0x5c, 0xb3, 0xa9, 0xb5, 0xd1, 0x8c, 0xa1, 0x4d, 0xfc, 0xf3, 0x38,
	0xfa, 0x41, 0xbb, 0x0c, 0x07, 0xaa, 0xe2, 0x60, 0x97, 0x19, 0xd6, 0x85, 0x01, 0x5a, 0x36, 0x75,
	0x40, 0x43, 0x3a, 0x67, 0xad, 0x77, 0xe2, 0xd8, 0xa1, 0x3a, 0xca, 0xd0, 0x26, 0x12, 0x42, 0xb1,
	0x8e, 0xf2, 0x57, 0x29, 0xb8, 0x16, 0x9d, 0x6d, 0x4b, 0x69, 0xd3, 0xf8, 0x8b, 0x8a, 0x08, 0x86,
	0x67, 0xcc, 0x28, 0x67, 0x1c, 0xa5, 0xed, 0x85, 0x66, 0xb4, 0x29, 0x70, 0x16, 0x43, 0x9b, 0x30,
	0x78, 0xba, 0x07, 0x4b, 0xb4, 0xc9, 0x26, 0x3a, 0xe9, 0x38, 0x0c, 0x83, 0xd9, 0xe4, 0xbc, 0xa3,
	0xb4, 0x9b, 0x08, 0x45, 0xbc, 0x3d, 0x98, 0xa5, 0x99, 0x98, 0x67, 0x24, 0xc7, 0x96, 0xf8, 0x93,
	0x78, 0x29, 0xb1, 0xae, 0xf1, 0x8b, 0x3e, 0xb3, 0x09, 0x17, 0x7d, 0xa2, 0x57, 0x8d, 0x10, 0x91,
	0x45, 0xcb, 0xc1, 0xab, 0x46, 0x31, 0x19, 0xca, 0x5c, 0x20, 0x43, 0xd9, 0xb8, 0x0c, 0xfd, 0x3c,
	0x15, 0xdf, 0xfb, 0xe6, 0x50, 0x73, 0x04, 0x01, 0x66, 0x02, 0x6c, 0xc4, 0xdf, 0x14, 0x16, 0xe0,
	0x1f, 0xfe, 0x1e, 0x31, 0x25, 0x7d, 0x75, 0xa6, 0x84, 0xc4, 0x68, 0x66, 0x9c, 0x18, 0xcd, 0x8e,
	0x15, 0xa3, 0xb9, 0x88, 0x18, 0x85, 0x83, 0xe5, 0x4c, 0x34, 0x58, 0x5e, 0x87, 0x0c, 0x16, 0x96,
	0x35, 0xd5, 0xe5, 0xc8, 0x1c, 0xfd, 0xac, 0xaa, 0xc2, 0x13, 0x58, 0xf5, 0x2b, 0xd9, 0x72, 0x50,
	0xf2, 0x73, 0x88, 0x76, 0xcd, 0x6f, 0x94, 0x46, 0x2a, 0x10, 0xaf, 0x52, 0x43, 0x42, 0x95, 0xfa,
	0xa7, 0x5c, 0x58, 0x60, 0x29, 0x8b, 0x7f, 0x29, 0x02, 0x4b, 0x67, 0x31, 0xb5, 0xc0, 0xd2, 0xd9,
	0x49, 0xac, 0xab, 0x38, 0x84, 0xd5, 0xd0, 0x01, 0x93, 0xae, 0xb7, 0x94, 0x76, 0xa9, 0xdb, 0xfb,
	0xc5, 0x4e, 0x5d, 0x2c, 0xc3, 0xe6, 0x01, 0x71, 0xa2, 0x13, 0x9b, 0xb6, 0xf8, 0xf8, 0x4f, 0x69,
	0x0c, 0x03, 0xe2, 0x64, 0xa6, 0x77, 0x69, 0xcf, 0x20, 0x87, 0x65, 0x18, 0x47, 0x69, 0xdb, 0x6e,
	0x6d, 0xe3, 0xdd, 0xcb, 0x44, 0x49, 0xae, 0x69, 0x92, 0xb2, 0x1a, 0xfb, 0x61, 0x0b, 0xfb, 0x90,
	0x45, 0x89, 0x71, 0x94, 0x36, 0xae, 0xfd, 0x92, 0x84, 0x5c, 0x91, 0x91, 0x50, 0x76, 0xa9, 0xec,
	0xdc, 0xa5, 0x71, 0x96, 0xed, 0xc8, 0x51, 0x73, 0xba, 0x40, 0xa1, 0xc7, 0x9e, 0x2e, 0x1c, 0xc1,
	0xbc, 0xa2, 0xeb, 0xe8, 0x39, 0x3b, 0xdd, 0x1e, 0x2a, 0xd1, 0xfc, 0x93, 0x47, 0x97, 0x3a, 0x4b,
	0xf4, 0xb6, 0x5a, 0xca, 0x29, 0xfe, 0xae, 0x7f, 0x0a, 0x02, 0x2a, 0xa0, 0x67, 0xa3, 0x99, 0xee,
	0xcf, 0x5d, 0x41, 0xf7, 0x79, 0xa4, 0xc3, 0x8c, 0x7a, 0x15, 0xcd, 0xc0, 0x43, 0x9f, 0xf6, 0x48,
	0xf5, 0x6c, 0xbc, 0x7a, 0x95, 0xf7, 0xb0, 0x7d, 0xbd, 0xb3, 0xc5, 0x67, 0x78, 0xb7, 0x2e, 0x4a,
	0xba, 0x61, 0xda, 0x53, 0x4b, 0xc9, 0x7f, 0xb1, 0xba, 0x5d, 0x32, 0xa9, 0xe9, 0x25, 0xc5, 0xb7,
	0x88, 0xa9, 0xab, 0x5b, 0xc4, 0x6d, 0x98, 0x37, 0x2d, 0x8d, 0x18, 0x0e, 0xcb, 0xb1, 0xdd, 0xb3,
	0x89, 0x00, 0x88, 0xea, 0xd8, 0x37, 0x87, 0xfd, 0x01, 0xc6, 0xc8, 0xec, 0xa2, 0x40, 0x86, 0x7e,
	0xbb, 0xf1, 0x71, 0xc8, 0xec, 0xcf, 0xc6, 0xcd, 0xfe, 0x8f, 0x38, 0x58, 0x8d, 0x8d, 0x8f, 0x86,
	0x35, 0x5e, 0x5a, 0xfb, 0xe5, 0xac, 0x67, 0x0b, 0x72, 0x03, 0xd3, 0x26, 0xc1, 0xdb, 0xaf, 0x59,
	0x0a, 0x40, 0x7f, 0xff, 0x67, 0x1c, 0x6c, 0x44, 0xc9, 0x97, 0x4e, 0x14, 0xa3, 0x47, 0xea, 0x03,
	0x67, 0x4c, 0x35, 0xd0, 0xaf, 0xd4, 0xd3, 0x9f, 0x6f, 0xc5, 0x4d, 0x45, 0x16, 0x31, 0x13, 0x5b,
	0x84, 0xf8, 0x5b, 0x70, 0x77, 0x4f, 0x71, 0x3a, 0x27, 0x09, 0xd2, 0x54, 0x65, 0xf2, 0x3b, 0x5d,
	0x2c, 0xb9, 0x01, 0xd9, 0xa1, 0x77, 0xf0, 0xc4, 0x2e, 0x7b, 0x66, 0x86, 0x1a, 0x3b, 0x71, 0x5a,
	0x81, 0x59, 0xbb, 0x63, 0xfa, 0xd7, 0x9d, 0xd9, 0x87, 0xf8, 0x7d, 0x0e, 0x76, 0x2e, 0x98, 0xc1,
	0x55, 0x4c, 0x5f, 0x96, 0x6a, 0x65, 0xa0, 0xaa, 0xfb, 0xe8, 0xd2, 0xec, 0xc3, 0x31, 0x33, 0x43,
	0x1b, 0x73, 0x59, 0xf1, 0xdb, 0x1c, 0xdc, 0x4e, 0x98, 0x59, 0x99, 0x95, 0xd0, 0xa6, 0xe5, 0xce,
	0x0d, 0x00, 0x47, 0xb1, 0x7a, 0xc4, 0x91, 0x87, 0xfe, 0x66, 0xe7, 0x18, 0xe4, 0x58, 0x53, 0xc7,
	0x70, 0xe8, 0x6f, 0xd3, 0x20, 0x4e, 0x9a, 0xc2, 0xf4, 0xec, 0x39, 0x04, 0x60, 0x66, 0x0b, 0x2f,
	0x6b, 0xa4, 0x26, 0x1b, 0xd8, 0x64, 0x06, 0xe5, 0x90, 0x00, 0x66, 0x79, 0x4f, 0x60, 0x01, 0xaf,
	0x00, 0x0c, 0x2c, 0xb3, 0xab, 0xe9, 0xc4, 0xf5, 0x10, 0x4b, 0x94, 0x1e, 0xed, 0xdb, 0x60, 0x60,
	0x76, 0x25, 0xc0, 0xfd, 0x10, 0x9e, 0xc3, 0xbc, 0x7d, 0xaa, 0x39, 0x9d, 0x13, 0x36, 0x05, 0xe6,
	0xed, 0x9f, 0x5e, 0xc6, 0xc6, 0xbb, 0xcb, 0x6e, 0x62, 0x6f, 0xcc, 0x8d, 0xc0, 0xf6, 0x7f, 0x0b,
	0xbf, 0x0e, 0xd7, 0x2c, 0xa2, 0xb3, 0xab, 0x11, 0x81, 0x25, 0xce, 0x5e, 0x65, 0x89, 0xcb, 0x1e,
	0xa5, 0xaa, 0xbf, 0xd4, 0x12, 0xac, 0xfa, 0xe4, 0x43, 0x6b, 0x9e, 0x4b, 0x5e, 0xb3, 0x3f, 0x99,
	0x00, 0x50, 0xfc, 0x0c, 0xa0, 0x89, 0xdb, 0x89, 0x19, 0xcb, 0x25, 0xee, 0xf1, 0x47, 0x92, 0x9a,
	0x54, 0x2c, 0xa9, 0xa1, 0x08, 0x81, 0x04, 0xc4, 0x3d, 0x05, 0xb7, 0x46, 0xd9, 0xc7, 0x9f, 0x73,
	0x70, 0xab, 0xf9, 0x96, 0x34, 0xfc, 0x63, 0xc8, 0x21, 0x5f, 0x03, 0x06, 0x56, 0x1c, 0x5b, 0x8d,
	0xf2, 0xd7, 0x29, 0x51, 0x85, 0x64, 0x4e, 0xf3, 0x11, 0x75, 0x9a, 0x1d, 0x8b, 0xf4, 0xa9, 0x11,
	0xd2, 0xdd, 0x60, 0xc0, 0x4d, 0xb9, 0x96, 0x03, 0x2d, 0x2c, 0x20, 0x10, 0x8f, 0x60, 0xbb, 0xf9,
	0xf6, 0x4c, 0x83, 0xf8, 0x19, 0xec, 0x24, 0x90, 0xab, 0x8f, 0x4c, 0xe2, 0xb4, 0xfc, 0x88, 0xd8,
	0xd8, 0x54, 0xdc, 0xc6, 0x36, 0xe1, 0xde, 0x45, 0x43, 0x4e, 0xbf, 0x0e, 0x02, 0x37, 0x26, 0xaa,
	0x05, 0x56, 0xb3, 0x99, 0x8a, 0xf9, 0x77, 0x10, 0xf3, 0x9e, 0xae, 0x60, 0x1e, 0x76, 0x07, 0xf2,
	0x2e, 0x82, 0x8d, 0x65, 0x13, 0x9c, 0x7a, 0x56, 0x5a, 0x60, 0x40, 0x56, 0x4a, 0xa1, 0x7e, 0xec,
	0x4e, 0x33, 0x7c, 0xe7, 0x36, 0x34, 0xd4, 0xb4, 0xdc, 0x8a, 0x28, 0x3e, 0xb3, 0x3d, 0x6f, 0xae,
	0xf8, 0xe2, 0xd7, 0xe0, 0xee, 0xe4, 0x69, 0x4e, 0xcf, 0xe1, 0xbf, 0xe1, 0x80, 0xa7, 0x9b, 0x26,
	0xf9, 0x66, 0xa0, 0x6b, 0x0a, 0x8f, 0x02, 0x06, 0x86, 0x3a, 0x3a, 0x0d, 0x0d, 0x81, 0xcb, 0x5d,
	0xde, 0x57, 0x77, 0x4d, 0xad, 0x52, 0x9d, 0x17, 0x1e, 0x42, 0xae, 0xad, 0x19, 0x6a, 0xd0, 0x13,
	0xc5, 0x8c, 0x44, 0x96, 0x62, 0xa0, 0x87, 0xfc, 0x00, 0xf2, 0x9a, 0xf1, 0x5a, 0xc3, 0x7b, 0xfc,
	0x81, 0xa3, 0xbf, 0x58, 0x8f, 0x05, 0x0f, 0x0b, 0x7b, 0xdd, 0x85, 0xc5, 0xbe, 0x72, 0x26, 0xe3,
	0x38, 0xc1, 0x43, 0xfb, 0x85, 0xbe, 0x72, 0xb6, 0xa7, 0x19, 0x6a, 0x89, 0xc2, 0xc4, 0xaf, 0xe2,
	0x11, 0x6a, 0x02, 0x83, 0x68, 0xb8, 0x3c, 0x65, 0xf8, 0xf9, 0x6f, 0x69, 0x74, 0x8a, 0xe3, 0x88,
	0x4d, 0xef, 0x90, 0x62, 0x52, 0xf1, 0x96, 0xdc, 0xc1, 0x73, 0x10, 0xd0, 0x4c, 0x07, 0x7c, 0x42,
	0xd7, 0x74, 0x1d, 0xd4, 0xfd, 0x49, 0xde, 0x20, 0xb8, 0xe7, 0x12, 0xde, 0x9b, 0x0b, 0x49, 0x81,
	0x08, 0x79, 0x65, 0xe8, 0x98, 0x32, 0x1e, 0xad, 0xd9, 0xa4, 0xe3, 0x45, 0x56, 0x14, 0xd8, 0xa0,
	0xd3, 0x21, 0x1d, 0xe1, 0x0b, 0xb0, 0xda, 0x3f, 0xf7, 0x2e, 0xb5, 0x59, 0xa4, 0x4f, 0xb7, 0xc7,
	0x21, 0x67, 0x8e, 0x7b, 0x05, 0x56, 0xe8, 0x9f, 0xb3, 0x5b, 0x6d, 0x12, 0x36, 0xb5, 0xc8, 0x99,
	0x23, 0x7c, 0x09, 0x36, 0xdc, 0xf0, 0x20, 0xa1, 0x1b, 0xbb, 0x0d, 0xbb, 0xc6, 0x10, 0x62, 0x5d,
	0x9f, 0xc3, 0xa2, 0x1f, 0x8c, 0xb2, 0x55, 0x66, 0x2e, 0x5f, 0x17, 0x6f, 0xb8, 0x51, 0x2b, 0xbb,
	0xce, 0x3a, 0x08, 0x7c, 0x89, 0x47, 0xe1, 0x33, 0xda, 0x20, 0x26, 0x0b, 0x57, 0x88, 0xe1, 0x49,
	0x3f, 0xfb, 0x08, 0x87, 0xc5, 0xa9, 0x48, 0x58, 0xfc, 0x87, 0x1c, 0x5c, 0x4f, 0xda, 0x3e, 0x34,
	0x36, 0x0d, 0x73, 0x90, 0x10, 0x19, 0x17, 0x20, 0xd3, 0x31, 0x0d, 0x87, 0xb8, 0x67, 0xe5, 0x39,
	0xc9, 0xfb, 0xa4, 0xd9, 0xd7, 0x89, 0xd6, 0x3b, 0x91, 0x75, 0xea, 0xe8, 0x64, 0x0f, 0x89, 0x25,
	0xe7, 0x3c, 0x6d, 0x39, 0xa4, 0x0d, 0x25, 0x17, 0x7b, 0x7c, 0xfa, 0x21, 0xfe, 0x84, 0x83, 0x7b,
	0x63, 0x67, 0x45, 0xa3, 0x76, 0x83, 0xe8, 0x13, 0xcf, 0xf1, 0xc3, 0x91, 0x7b, 0x60, 0xc6, 0xe9,
	0xcb, 0xcc, 0x78, 0xe6, 0x12, 0x33, 0x9e, 0x0d, 0xcf, 0xd8, 0xc6, 0x2a, 0x01, 0xd5, 0xee, 0x2a,
	0x35, 0x05, 0x4a, 0x5b, 0x7f, 0x0b, 0xe7, 0xfa, 0xb9, 0xc9, 0xc7, 0x2e, 0xe2, 0xd7, 0x20, 0xef,
	0x8f, 0x46, 0x95, 0x44, 0xb8, 0x03, 0x33, 0x81, 0x97, 0x76, 0x31, 0x33, 0x85, 0x8d, 0x54, 0x1e,
	0x34, 0x5b, 0x36, 0x0d, 0x5d, 0x33, 0x88, 0xeb, 0x62, 0xb2, 0x9a, 0x5d, 0xc7, 0x6f, 0xf1, 0xc7,
	0xec, 0x7a, 0x59, 0xc2, 0x42, 0xae, 0x92, 0xc5, 0xe6, 0x50, 0xd9, 0x2f, 0x73, 0x97, 0x23, 0xb4,
	0x0e, 0x8c, 0x4d, 0x2c, 0xef, 0x3e, 0xa1, 0x41, 0xce, 0x1c, 0xd9, 0xe5, 0x0a, 0xdb, 0x3e, 0xa0,
	0xa0, 0x3a, 0x42, 0xc4, 0x21, 0xac, 0x34, 0x03, 0xf3, 0x25, 0x6f, 0x39, 0x03, 0x58, 0x83, 0x39,
	0xa5, 0x13, 0x48, 0x38, 0xdd, 0x2f, 0x71, 0x0f, 0x56, 0x23, 0xc3, 0x4e, 0xef, 0xcf, 0x4e, 0xa1,
	0xc0, 0xdc, 0x81, 0xd1, 0xd5, 0xac, 0x7e, 0xb1, 0x73, 0x95, 0x60, 0x87, 0x6e, 0x26, 0x4e, 0x60,
	0x74, 0x99, 0x28, 0xcb, 0x00, 0xd5, 0xf1, 0x93, 0xdf, 0x87, 0x8d, 0x84, 0x81, 0xa7, 0x5f, 0x80,
	0x82, 0x15, 0xb6, 0x11, 0x13, 0xae, 0x76, 0x40, 0x36, 0x69, 0x09, 0xe2, 0x77, 0xb8, 0xb0, 0x62,
	0x91, 0xab, 0x9f, 0x28, 0x95, 0x60, 0xde, 0x1b, 0x67, 0x14, 0xe9, 0x88, 0x13, 0x05, 0x92, 0x19,
	0x60, 0xd0, 0xfc, 0xdf, 0x62, 0x1b, 0x36, 0x8e, 0x0d, 0xea, 0xd9, 0xdf, 0xa0, 0xa6, 0x78, 0x81,
	0xcc, 0x89, 0x07, 0xb0, 0x99, 0x34, 0xc6, 0xf4, 0xfb, 0xd3, 0x81, 0xcd, 0x51, 0x0c, 0xc6, 0x58,
	0x78, 0x6c, 0xbf, 0xed, 0xd9, 0x3e, 0x83, 0xad, 0xc4, 0x41, 0xa6, 0x9f, 0xae, 0x0e, 0x30, 0xe2,
	0x7a, 0x58, 0x2c, 0xb8, 0x88, 0x64, 0x3f, 0x80, 0x0c, 0xfb, 0x6d, 0xb9, 0xfb, 0x18, 0xb3, 0x75,
	0x5e, 0x3b, 0x55, 0x02, 0x37, 0x9c, 0x76, 0x95, 0x80, 0x7d, 0x89, 0x15, 0x5f, 0xb0, 0xf6, 0x08,
	0x1b, 0x56, 0xbd, 0xca, 0xe5, 0xd4, 0xdf, 0x1f, 0x19, 0xcc, 0x08, 0x9d, 0x37, 0x91, 0xd0, 0x80,
	0xc9, 0x9c, 0x42, 0x42, 0xb1, 0x40, 0xf2, 0x3f, 0x1c, 0x6c, 0x04, 0x05, 0x67, 0xa4, 0x3a, 0x47,
	0x76, 0x2f, 0xc8, 0x38, 0xee, 0x02, 0xc6, 0x4d, 0x34, 0x2d, 0xb7, 0x60, 0x7e, 0xa0, 0x75, 0x9c,
	0xa1, 0x15, 0xbc, 0xe0, 0x05, 0x2e, 0xe8, 0xd8, 0xd2, 0x05, 0x01, 0x66, 0x30, 0x4a, 0x62, 0xbe,
	0x14, 0x7f, 0x0b, 0x3b, 0xb0, 0x48, 0x7d, 0x2a, 0x73, 0xb6, 0x81, 0xd0, 0x2b, 0xef, 0x43, 0x31,
	0x74, 0xa2, 0x02, 0x47, 0x6d, 0x3e, 0x7b, 0xde, 0xc5, 0xc2, 0xac, 0x1c, 0x85, 0xe0, 0xf3, 0xae,
	0x90, 0x17, 0xce, 0x84, 0xbd, 0xf0, 0x3f, 0xa4, 0x80, 0x0f, 0x84, 0x07, 0x1d, 0x52, 0xea, 0xf6,
	0x28, 0xb9, 0x36, 0xe9, 0x69, 0x06, 0xbb, 0x11, 0xc6, 0x61, 0x81, 0x3b, 0x87, 0x10, 0xbc, 0x10,
	0xb6, 0x01, 0x59, 0x42, 0x43, 0x3a, 0xef, 0xe5, 0x57, 0x5a, 0xca, 0x10, 0x43, 0xc5, 0xa6, 0x1d,
	0x58, 0x1c, 0x0c, 0xdb, 0xba, 0xd6, 0x89, 0xc4, 0x32, 0x79, 0x06, 0xf5, 0xc2, 0x82, 0xf7, 0x61,
	0x25, 0x8c, 0xe6, 0xce, 0x9c, 0x2d, 0x5d, 0x08, 0x21, 0xb3, 0x25, 0x3c, 0x85, 0xf5, 0x48, 0x8f,
	0x48, 0x5c, 0xb1, 0x12, 0xea, 0xf4, 0x55, 0xb7, 0x2a, 0x7b, 0x07, 0xf2, 0x5d, 0xdd, 0x54, 0x46,
	0x81, 0x0a, 0xe3, 0xcd, 0x02, 0x02, 0xbd, 0xd9, 0x7c, 0x00, 0x6b, 0x21, 0x24, 0x59, 0x1d, 0x5a,
	0x2c, 0x13, 0x66, 0x67, 0x7b, 0x2b, 0x41, 0xec, 0xb2, 0xdb, 0x16, 0x3d, 0x43, 0xcf, 0x46, 0x8f,
	0xf6, 0xfb, 0x70, 0xb7, 0x74, 0x42, 0x3a, 0xaf, 0xa2, 0x49, 0x73, 0xc5, 0x70, 0x2c, 0xc5, 0xe8,
	0x5c, 0xc5, 0xa8, 0x74, 0xd8, 0x46, 0x8d, 0x2a, 0x28, 0x39, 0x17, 0x52, 0x55, 0xc5, 0xff, 0xe4,
	0x60, 0xe7, 0x82, 0xf1, 0xa6, 0x57, 0xaf, 0xdb, 0xb0, 0x40, 0xd7, 0x48, 0x5c, 0x12, 0x6e, 0xec,
	0x43, 0xd7, 0xed, 0x51, 0xa5, 0x62, 0xdd, 0x53, 0xfa, 0x44, 0x26, 0x06, 0x5e, 0x5f, 0x63, 0x35,
	0x10, 0xa0, 0xa0, 0x0a, 0x42, 0x68, 0x02, 0xe3, 0xcd, 0xdb, 0x40, 0x09, 0xc3, 0x23, 0x91, 0x99,
	0xc9, 0x09, 0x4c, 0x54, 0x24, 0x25, 0xbe, 0x13, 0x81, 0x88, 0x75, 0xbc, 0xe2, 0x1e, 0x2a, 0x4e,
	0x7b, 0x17, 0x0c, 0xd8, 0x0e, 0x4c, 0x6b, 0x97, 0xbe, 0x15, 0x7b, 0xed, 0x1b, 0x23, 0x78, 0xa5,
	0xff, 0x0f, 0xe8, 0xe9, 0x66, 0x5b, 0xd1, 0x23, 0xf7, 0x92, 0xf2, 0x0c, 0xea, 0x5d, 0x24, 0xfd,
	0x77, 0x0e, 0xae, 0x45, 0x46, 0xf3, 0xfe, 0x22, 0xe3, 0xa2, 0x17, 0x8f, 0x09, 0xaf, 0x89, 0x52,
	0x49, 0xaf, 0x89, 0xde, 0xec, 0xde, 0x43, 0xc2, 0x7b, 0xc5, 0xd9, 0xa4, 0xf7, 0x8a, 0xd1, 0x27,
	0x46, 0x73, 0xb1, 0x27, 0x46, 0xe2, 0x6b, 0x78, 0x77, 0x32, 0x9f, 0xcb, 0xc4, 0x51, 0x34, 0x7d,
	0x5a, 0xfd, 0xb8, 0x05, 0xf3, 0x7d, 0xe5, 0x2c, 0xc2, 0x03, 0xe8, 0x2b, 0x67, 0x1e, 0x8f, 0xff,
	0x85, 0x83, 0x87, 0x97, 0x1b, 0x78, 0xfa, 0x6d, 0x6e, 0x07, 0xdf, 0xe2, 0x79, 0xdb, 0x41, 0x83,
	0xa6, 0x0b, 0x8f, 0x2d, 0x13, 0x76, 0x5d, 0x5a, 0xe9, 0xc4, 0x81, 0x76, 0x74, 0x81, 0xe9, 0xd8,
	0x02, 0x8f, 0xe0, 0x4e, 0x74, 0x7d, 0x8a, 0xc5, 0xde, 0x7e, 0x18, 0x57, 0x28, 0x91, 0x48, 0xc0,
	0x17, 0xdb, 0xe6, 0xd0, 0x69, 0x99, 0x2c, 0x55, 0xa7, 0x9e, 0xc1, 0xbb, 0x8c, 0xc0, 0x05, 0x2e,
	0x23, 0xec, 0xc2, 0xb2, 0x9b, 0xeb, 0x2b, 0x3a, 0xb1, 0x1c, 0xe6, 0x17, 0xd8, 0x6d, 0xda, 0x25,
	0xd6, 0x80, 0x57, 0x65, 0xf0, 0x25, 0xf3, 0x4f, 0x33, 0xf8, 0xd2, 0x65, 0xc2, 0x1c, 0xa7, 0xe7,
	0xfd, 0x3b, 0xc0, 0x9f, 0x2a, 0x9a, 0x23, 0x3b, 0x26, 0x3e, 0xe1, 0x0b, 0x1c, 0x80, 0xe7, 0x29,
	0xbc, 0x65, 0xee, 0x0d, 0xd9, 0x1b, 0xdd, 0xfb, 0xc0, 0x2b, 0xba, 0x45, 0x14, 0xf5, 0x7c, 0x84,
	0xc8, 0xdc, 0xd3, 0xa2, 0x0b, 0xf7, 0x30, 0xbf, 0x01, 0x05, 0x85, 0x2e, 0x9d, 0xd2, 0xf4, 0x1e,
	0x39, 0x74, 0x7b, 0xc1, 0xdb, 0x4f, 0x63, 0x2d, 0x57, 0x94, 0x65, 0xd2, 0x8a, 0x12, 0x81, 0x60,
	0x96, 0xf6, 0x65, 0xd8, 0xec, 0x6a, 0x96, 0xed, 0x50, 0xdb, 0x4a, 0x2c, 0xb9, 0xa3, 0x58, 0xaa,
	0x6c, 0x3b, 0xa6, 0x1b, 0x1c, 0x30, 0xd5, 0x5a, 0x43, 0x8c, 0x0a, 0x45, 0xa0, 0x4c, 0x6a, 0xd2,
	0x66, 0xea, 0xd4, 0xd6, 0x21, 0xa3, 0x84, 0x2a, 0x2a, 0x73, 0x0a, 0xab, 0xa0, 0xec, 0xc0, 0x92,
	0x21, 0xab, 0xca, 0x39, 0xfb, 0x57, 0x0b, 0x93, 0x5a, 0x6c, 0xf7, 0x81, 0x8a, 0x51, 0x56, 0xce,
	0x9b, 0x27, 0xe6, 0x69, 0xdd, 0x35, 0xd9, 0x74, 0x35, 0x9e, 0x20, 0xb1, 0xeb, 0x18, 0xd0, 0xe9,
	0xf6, 0x3c, 0x53, 0x71, 0x07, 0x16, 0x83, 0x1c, 0x6d, 0xf7, 0xdc, 0x2b, 0xde, 0xf3, 0x3e, 0x3f,
	0xf7, 0x7a, 0xc2, 0x5d, 0x58, 0x0c, 0x72, 0xb3, 0xdd, 0xf3, 0xee, 0x60, 0x8c, 0x78, 0xb9, 0xd7,
	0x13, 0xde, 0x05, 0x21, 0xca, 0xc9, 0x76, 0xcf, 0xfd, 0xa3, 0x8a, 0xa5, 0x10, 0x67, 0xf6, 0x7a,
	0xc2, 0x97, 0x60, 0x83, 0xf1, 0xc0, 0x22, 0xb6, 0xa6, 0x52, 0x4f, 0x4c, 0x3d, 0x8f, 0xbb, 0x53,
	0xec, 0xe6, 0xf7, 0x1a, 0x22, 0x48, 0x6e, 0x3b, 0x5e, 0x35, 0xc5, 0x1d, 0x7b, 0x0a, 0xeb, 0xac,
	0xab, 0x77, 0x07, 0xc2, 0xbd, 0xed, 0x40, 0x54, 0xf7, 0x4e, 0xf8, 0x0a, 0x36, 0xb7, 0xd8, 0x8d,
	0x88, 0xa6, 0xdb, 0x36, 0x1a, 0xd1, 0xef, 0x36, 0x34, 0xfc, 0x8e, 0x8b, 0x81, 0x11, 0xdd, 0x8e,
	0xc7, 0x7e, 0xeb, 0x34, 0x62, 0x3f, 0x61, 0xb7, 0xfb, 0xea, 0x53, 0xf7, 0x62, 0x79, 0xd2, 0x6e,
	0xb3, 0x47, 0xaf, 0xcb, 0x83, 0x8e, 0x1c, 0xd9, 0x8f, 0x65, 0x37, 0xaa, 0xea, 0xbc, 0x08, 0xec,
	0xc8, 0x03, 0xc4, 0x8c, 0x6c, 0x8a, 0xc0, 0x04, 0x7c, 0xd0, 0x29, 0x06, 0xb7, 0xe5, 0x7d, 0x58,
	0xa5, 0xa8, 0xf1, 0x9d, 0x61, 0xb7, 0xd3, 0x97, 0x07, 0x9d, 0x62, 0x78, 0x6f, 0x92, 0xdc, 0xad,
	0x62, 0xa9, 0xb8, 0x01, 0x57, 0x4a, 0x62, 0xc5, 0xff, 0xe0, 0xe2, 0xfe, 0x36, 0x4a, 0x71, 0x7a,
	0x63, 0x10, 0x79, 0xcd, 0x90, 0x8a, 0xbd, 0x66, 0xb8, 0x0b, 0x8b, 0xba, 0x79, 0xca, 0xde, 0x77,
	0x31, 0x1d, 0x62, 0x26, 0x60, 0x41, 0x37, 0x4f, 0xf1, 0x91, 0x17, 0x6a, 0xd2, 0x2a, 0xcc, 0x29,
	0xaa, 0xac, 0xa9, 0x67, 0xde, 0xeb, 0x31, 0x45, 0xad, 0xaa, 0x67, 0x51, 0xcd, 0x99, 0x8d, 0x69,
	0x0e, 0x4d, 0x9d, 0xb0, 0x76, 0xeb, 0x3a, 0x3e, 0xf7, 0x4b, 0xac, 0x80, 0x18, 0x5d, 0xe8, 0xa8,
	0x34, 0xd7, 0x23, 0x6e, 0x65, 0x2e, 0x32, 0x79, 0x2e, 0x3a, 0x79, 0xf1, 0xf7, 0x52, 0xe1, 0xda,
	0x23, 0xa5, 0xd3, 0xc0, 0x03, 0xd4, 0xa1, 0xee, 0x54, 0xce, 0x1c, 0xe1, 0x1b, 0x78, 0xce, 0x36,
	0xd4, 0x9d, 0xd1, 0x89, 0xc9, 0xe2, 0xe5, 0xae, 0xed, 0x47, 0x49, 0x3d, 0x66, 0xbf, 0x5a, 0xe7,
	0x03, 0x82, 0x07, 0x75, 0xee, 0x6f, 0x7c, 0x75, 0x67, 0x0e, 0x86, 0x83, 0x51, 0x49, 0x20, 0x27,
	0xe5, 0x10, 0x82, 0xd9, 0xfe, 0x37, 0x01, 0x46, 0x1d, 0x85, 0x2d, 0x58, 0x97, 0x2a, 0xcd, 0xe3,
	0xc3, 0x96, 0xdc, 0x7a, 0xd9, 0xa8, 0xc8, 0xc7, 0xb5, 0x66, 0xa3, 0x52, 0xaa, 0xee, 0x57, 0x2b,
	0x65, 0xfe, 0xff, 0x09, 0xdb, 0x70, 0x3d, 0xd8, 0x58, 0x3c, 0x94, 0x2a, 0xc5, 0xf2, 0x4b, 0xb9,
	0x54, 0xaf, 0xb5, 0xa4, 0x62, 0xa9, 0xc5, 0x73, 0xc2, 0x0d, 0xd8, 0x08, 0x62, 0x34, 0x8a, 0x2f,
	0xe5, 0xfd, 0xba, 0x24, 0xd7, 0x5b, 0xcf, 0x2a, 0x12, 0x9f, 0x12, 0x7f, 0x13, 0x6e, 0x46, 0x0e,
	0x4c, 0xbc, 0xda, 0xee, 0x15, 0x0a, 0x2a, 0x63, 0x0b, 0xbe, 0xa3, 0x1a, 0x71, 0x3a, 0x50, 0x23,
	0x16, 0x0f, 0xf1, 0x38, 0x32, 0x79, 0xf0, 0xe9, 0x13, 0xf9, 0xb8, 0xeb, 0xde, 0xa3, 0xe9, 0x96,
	0x41, 0xac, 0x83, 0xa1, 0xa6, 0x4e, 0x7d, 0xb9, 0xe6, 0x7b, 0x5c, 0xcc, 0xcd, 0x46, 0xe8, 0x4d,
	0xaf, 0x59, 0x5b, 0x40, 0x93, 0x1b, 0x19, 0xff, 0x5c, 0xc8, 0x2b, 0x82, 0x9e, 0x28, 0x36, 0xd2,
	0xa3, 0xae, 0x15, 0x2d, 0x94, 0xf7, 0x9c, 0x67, 0xf4, 0xa7, 0x1f, 0xe8, 0x49, 0x9a, 0xec, 0x41,
	0x0f, 0x15, 0x61, 0x09, 0xee, 0x1f, 0x29, 0xd6, 0xab, 0xb1, 0x53, 0x2b, 0x9b, 0xc6, 0xd4, 0xcb,
	0x7d, 0x0e, 0x0f, 0x2e, 0x41, 0x73, 0xea, 0x25, 0xef, 0xfe, 0x71, 0x6a, 0xf2, 0x7f, 0xb7, 0xa1,
	0x78, 0x7c, 0x00, 0xef, 0x3f, 0xaf, 0x4a, 0xad, 0xe3, 0xe2, 0xa1, 0x5c, 0x3d, 0x2a, 0x1e, 0x54,
	0x64, 0xa9, 0xd2, 0xac, 0x1f, 0x4b, 0xa5, 0x8a, 0x5c, 0x2a, 0xb6, 0x2a, 0x07, 0x75, 0xe9, 0x65,
	0x92, 0xf0, 0x3f, 0x81, 0xc7, 0x97, 0xea, 0x25, 0x55, 0x4a, 0xf5, 0xa3, 0xa3, 0x4a, 0xad, 0xcc,
	0x73, 0xc2, 0x23, 0x78, 0x70, 0xa9, 0x3e, 0xcd, 0xe3, 0x6a, 0x8b, 0x4f, 0x5d, 0x1a, 0x7d, 0xbf,
	0x58, 0xaa, 0xf0, 0x69, 0xe1, 0x31, 0xec, 0x5e, 0x0a, 0x9d, 0x69, 0x9f, 0xba, 0xfb, 0xdd, 0x34,
	0xdc, 0x49, 0x62, 0x4e, 0x33, 0x72, 0x57, 0xf6, 0x23, 0xf8, 0x60, 0x0c, 0xdd, 0xe6, 0xf1, 0xde,
	0x44, 0x1e, 0x7d, 0x01, 0x1e, 0x5d, 0xba, 0xe7, 0x0b, 0xa9, 0xd8, 0xe0, 0x39, 0xe1, 0x7d, 0x78,
	0x78, 0xe9, 0x2e, 0x4d, 0xe4, 0xd2, 0x34, 0x83, 0x34, 0x2b, 0xc5, 0x16, 0x9f, 0x9e, 0xaa, 0xcb,
	0x5e, 0xb1, 0xf4, 0x09, 0x3f, 0x33, 0x41, 0x48, 0xe2, 0x5d, 0x2a, 0xfb, 0xfb, 0x95, 0x52, 0xab,
	0xc9, 0xcf, 0x4e, 0x10, 0x92, 0x78, 0x2f, 0x6f, 0x5b, 0x7e, 0x98, 0x82, 0xe5, 0xd0, 0xeb, 0x4d,
	0xfc, 0xa7, 0x98, 0x3b, 0x70, 0x8b, 0xca, 0x51, 0xbd, 0x5c, 0x6d, 0xbd, 0x94, 0x0f, 0x8a, 0xd5,
	0x9a, 0xdc, 0x28, 0xb6, 0x9e, 0xc5, 0x0d, 0x72, 0x12, 0x52, 0xe3, 0x58, 0x2a, 0x3d, 0x2b, 0x36,
	0x2b, 0x3c, 0x27, 0x5c, 0x87, 0x42, 0x12, 0xc6, 0x41, 0x75, 0x9f, 0xb2, 0x72, 0x4c, 0xff, 0x62,
	0xa9, 0x55, 0x7d, 0x5e, 0x6d, 0xbd, 0xe4, 0xd3, 0xc2, 0x4d, 0xd8, 0x4c, 0xc2, 0x90, 0x2a, 0x2f,
	0x8a, 0x52, 0x99, 0x9f, 0x19, 0x47, 0xa1, 0xf2, 0xf5, 0xd2, 0xb3, 0x62, 0xed, 0xa0, 0xc2, 0xcf,
	0x52, 0x97, 0x90, 0x84, 0xc1, 0x56, 0x3f, 0x27, 0x3c, 0x84, 0xfb, 0x49, 0xcd, 0xd5, 0xda, 0x7e,
	0xb5, 0x56, 0x6d, 0x55, 0x64, 0x46, 0x47, 0x2e, 0xd1, 0xe1, 0x32, 0xbb, 0x9d, 0xc0, 0x1f, 0x0c,
	0xb9, 0xef, 0x22, 0x02, 0xf3, 0x4b, 0x90, 0xc8, 0x0d, 0x58, 0x8d, 0xb4, 0x37, 0xab, 0xb5, 0x83,
	0x43, 0xca, 0x9a, 0x75, 0xb8, 0x16, 0x6d, 0x42, 0x35, 0xdc, 0xfd, 0xdf, 0xd9, 0xe4, 0x37, 0xa2,
	0x38, 0xe0, 0xbb, 0xf0, 0xce, 0x98, 0x1d, 0x4e, 0x18, 0xfd, 0x3e, 0xdc, 0x9d, 0x84, 0xdc, 0xfc,
	0xa4, 0x72, 0x58, 0x69, 0xd5, 0x6b, 0x3c, 0x27, 0xdc, 0x85, 0xed, 0xc9, 0x98, 0xd5, 0x1a, 0x9f,
	0x12, 0x76, 0xe1, 0xde, 0x24, 0x2c, 0x2a, 0xba, 0x07, 0x52, 0xfd, 0xb8, 0x56, 0xe6, 0xd3, 0xc2,
	0x3d, 0x10, 0x27, 0xe1, 0xee, 0xd7, 0x0f, 0x0f, 0xeb, 0x2f, 0xf8, 0x99, 0x0b, 0xe7, 0xf8, 0xac,
	0xfe, 0x42, 0x7e, 0x5e, 0x6c, 0xf0, 0xb3, 0xc2, 0x87, 0xf0, 0x64, 0x12, 0x66, 0xb9, 0xb2, 0x5f,
	0xa4, 0x9e, 0x7f, 0x34, 0x0b, 0xb9, 0x51, 0x3b, 0xe0, 0xe7, 0x84, 0x07, 0xb0, 0x33, 0xa9, 0x5f,
	0xb1, 0x56, 0x3d, 0x2a, 0xb6, 0xaa, 0xf5, 0x1a, 0x9f, 0x99, 0x60, 0x0d, 0x7c, 0x36, 0xc8, 0xfb,
	0xc7, 0x87, 0x87, 0x72, 0xfd, 0xb8, 0xb5, 0x5f, 0x6d, 0xf1, 0xd9, 0x09, 0x7a, 0x8a, 0x3d, 0xaa,
	0xb5, 0xfd, 0xba, 0xc4, 0xc8, 0xa3, 0x0c, 0xe1, 0x52, 0x72, 0x17, 0xed, 0x62, 0xa9, 0x31, 0xda,
	0x1b, 0x98, 0x60, 0x96, 0x5d, 0x81, 0x2a, 0x07, 0xb6, 0x72, 0x5e, 0x78, 0x07, 0xee, 0x5c, 0x48,
	0xbb, 0x5a, 0xe3, 0x17, 0x2e, 0xa2, 0xbb, 0x57, 0x6c, 0x06, 0xe8, 0xe6, 0x27, 0xd8, 0x96, 0x00,
	0x7a, 0x68, 0x2e, 0x8b, 0x17, 0xf1, 0x13, 0xfb, 0x04, 0x17, 0xbb, 0xb4, 0xfb, 0xcf, 0x29, 0x58,
	0x0a, 0x3c, 0x97, 0x41, 0x99, 0x17, 0xe1, 0xa6, 0x54, 0x29, 0xcb, 0xe5, 0x7a, 0x4b, 0x2e, 0x1e,
	0x56, 0xa4, 0xc4, 0xd8, 0xf0, 0x3d, 0x78, 0x27, 0x01, 0x27, 0xa0, 0x60, 0xc5, 0x3d, 0xb9, 0x51,
	0x94, 0x2a, 0xb5, 0x16, 0xcf, 0x6d, 0xa6, 0xb2, 0x9c, 0xf0, 0x10, 0xee, 0x5e, 0xd8, 0xa1, 0x79,
	0xbc, 0xc7, 0xa7, 0x10, 0x7b, 0x07, 0x6e, 0x27, 0x60, 0x37, 0x2a, 0x52, 0xb3, 0x5e, 0xf3, 0x16,
	0xc7, 0xa7, 0x85, 0xfb, 0xb0, 0x9d, 0x80, 0x16, 0x62, 0x01, 0x3f, 0x83, 0x04, 0xef, 0xd2, 0x58,
	0x76, 0xfc, 0xf0, 0xfc, 0xac, 0x8b, 0xb5, 0x7d, 0xd1, 0x24, 0x99, 0x80, 0x27, 0x60, 0x49, 0xf5,
	0xfa, 0x51, 0x64, 0xe8, 0xcc, 0xee, 0xef, 0x70, 0xb0, 0x92, 0x14, 0x82, 0xc6, 0xa5, 0xa6, 0x51,
	0x6f, 0x26, 0xda, 0x94, 0xdb, 0x70, 0x63, 0x1c, 0x62, 0xb3, 0x55, 0xc4, 0xb0, 0xe3, 0x16, 0x6c,
	0x8d, 0x45, 0x41, 0x0b, 0x77, 0x0a, 0x6b, 0x21, 0x03, 0xe7, 0x5f, 0xda, 0x4b, 0xd0, 0xd5, 0xea,
	0xc1, 0xb3, 0x56, 0xf3, 0x72, 0x01, 0x51, 0x00, 0x35, 0xd1, 0x7e, 0x73, 0xbb, 0xdf, 0xe1, 0x60,
	0x7d, 0xcc, 0x7d, 0xb4, 0xf8, 0xd0, 0x75, 0xa9, 0x5a, 0xa9, 0xb5, 0x98, 0xee, 0x86, 0x87, 0xbe,
	0x03, 0xb7, 0xc6, 0xa3, 0xe2, 0x34, 0x78, 0x8e, 0x4a, 0xed, 0x78, 0xa4, 0xc3, 0x0a, 0x75, 0x80,
	0xbb, 0xff, 0x98, 0x0a, 0x1f, 0x24, 0x85, 0x6e, 0xf6, 0x50, 0xdf, 0x14, 0xa6, 0x50, 0xae, 0x36,
	0x1b, 0x87, 0xc5, 0x97, 0x72, 0xf3, 0x45, 0xb5, 0x55, 0x8a, 0x3a, 0xe3, 0x1d, 0xb8, 0x3d, 0x11,
	0xfb, 0xa8, 0x58, 0x4d, 0xb4, 0xf4, 0x51, 0xb4, 0x6a, 0x89, 0x4f, 0xc5, 0x63, 0xbb, 0x08, 0x96,
	0x2f, 0xf9, 0x0d, 0x26, 0xf8, 0x31, 0x5b, 0x12, 0xc5, 0x97, 0xea, 0xfb, 0xd5, 0x43, 0x77, 0x1f,
	0x66, 0xe2, 0x7b, 0x17, 0x41, 0x47, 0xd9, 0xad, 0xd4, 0x5a, 0x15, 0xc9, 0x0d, 0x6e, 0xf8, 0xd9,
	0x0b, 0x87, 0xd8, 0xaf, 0x50, 0x8f, 0x22, 0x37, 0x1b, 0x34, 0x3a, 0x9d, 0xdb, 0x95, 0x61, 0x81,
	0x1d, 0xc9, 0xb1, 0xb3, 0x77, 0x1a, 0x07, 0x54, 0x6b, 0xcf, 0xa9, 0x48, 0xd0, 0xf0, 0x22, 0xb6,
	0xa5, 0x6b, 0x20, 0x84, 0x9b, 0x9b, 0x2c, 0x84, 0x2e, 0xc0, 0x4a, 0x18, 0x5e, 0x2a, 0xd6, 0x4a,
	0x95, 0x43, 0x3e, 0xb5, 0x7b, 0x0e, 0xcb, 0xb1, 0x13, 0x7e, 0x2a, 0x19, 0x7b, 0xd5, 0x5a, 0x99,
	0xe6, 0xa4, 0xfb, 0x55, 0xe9, 0x28, 0x79, 0xac, 0x5b, 0xb0, 0x95, 0x84, 0xe4, 0x7e, 0xf2, 0x1c,
	0x8d, 0x2a, 0x92, 0x10, 0xa4, 0xca, 0x57, 0x29, 0x2b, 0x52, 0xbb, 0x7f, 0xc1, 0x01, 0x1f, 0x3d,
	0xae, 0xa7, 0xf2, 0x86, 0x9d, 0xdc, 0xe9, 0x36, 0x5b, 0xc5, 0xd6, 0x71, 0x33, 0x1e, 0xb0, 0x25,
	0xe2, 0x3c, 0x2b, 0xd6, 0xca, 0x87, 0x15, 0x57, 0x77, 0x13, 0x30, 0x8a, 0xa5, 0x52, 0xa5, 0xd1,
	0xaa, 0x94, 0xf9, 0xd4, 0x18, 0x04, 0x36, 0xb5, 0x4a, 0x99, 0x85, 0x6c, 0x09, 0x08, 0x95, 0xaf,
	0x37, 0xaa, 0x52, 0xa5, 0xcc, 0xcf, 0xec, 0x1d, 0xe2, 0xdf, 0xc1, 0x3d, 0x3e, 0xd7, 0xce, 0xcd,
	0x21, 0x4d, 0xa3, 0xfa, 0xa6, 0x4a, 0x74, 0xf6, 0x4f, 0xe4, 0x9f, 0xbe, 0xdf, 0x33, 0x75, 0xc5,
	0xe8, 0x3d, 0x7e, 0xfa, 0xc4, 0x71, 0x1e, 0x77, 0xcc, 0xfe, 0x7b, 0x08, 0xee, 0x98, 0xfa, 0x7b,
	0xca, 0x60, 0x90, 0xf4, 0x47, 0xe7, 0xed, 0x39, 0xc4, 0xf8, 0xe2, 0xff, 0x05, 0x00, 0x00, 0xff,
	0xff, 0x2d, 0x10, 0x6c, 0x8f, 0x1a, 0x5d, 0x00, 0x00,
}
