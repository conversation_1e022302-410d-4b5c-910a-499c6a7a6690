// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/numeric/grpc_numeric.proto

package numeric // import "golang.52tt.com/protocol/app/api/numeric"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import numeric_logic "golang.52tt.com/protocol/app/numeric-logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// NumericLogicClient is the client API for NumericLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type NumericLogicClient interface {
	GetUserRichSwitch(ctx context.Context, in *numeric_logic.GetUserRichSwitchReq, opts ...grpc.CallOption) (*numeric_logic.GetUserRichSwitchResp, error)
	SetUserRichSwitch(ctx context.Context, in *numeric_logic.SetUserRichSwitchReq, opts ...grpc.CallOption) (*numeric_logic.SetUserRichSwitchResp, error)
	GetUserNumericLock(ctx context.Context, in *numeric_logic.GetUserNumericLockReq, opts ...grpc.CallOption) (*numeric_logic.GetUserNumericLockResp, error)
	SetUserNumericLock(ctx context.Context, in *numeric_logic.SetUserNumericLockReq, opts ...grpc.CallOption) (*numeric_logic.SetUserNumericLockResp, error)
	GetUserGloryRank(ctx context.Context, in *numeric_logic.GetUserGloryRankReq, opts ...grpc.CallOption) (*numeric_logic.GetUserGloryRankResp, error)
	GetUserVipGiftPackageInfo(ctx context.Context, in *numeric_logic.GetUserVipGiftPackageInfoReq, opts ...grpc.CallOption) (*numeric_logic.GetUserVipGiftPackageInfoResp, error)
}

type numericLogicClient struct {
	cc *grpc.ClientConn
}

func NewNumericLogicClient(cc *grpc.ClientConn) NumericLogicClient {
	return &numericLogicClient{cc}
}

func (c *numericLogicClient) GetUserRichSwitch(ctx context.Context, in *numeric_logic.GetUserRichSwitchReq, opts ...grpc.CallOption) (*numeric_logic.GetUserRichSwitchResp, error) {
	out := new(numeric_logic.GetUserRichSwitchResp)
	err := c.cc.Invoke(ctx, "/ga.api.numeric.NumericLogic/GetUserRichSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericLogicClient) SetUserRichSwitch(ctx context.Context, in *numeric_logic.SetUserRichSwitchReq, opts ...grpc.CallOption) (*numeric_logic.SetUserRichSwitchResp, error) {
	out := new(numeric_logic.SetUserRichSwitchResp)
	err := c.cc.Invoke(ctx, "/ga.api.numeric.NumericLogic/SetUserRichSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericLogicClient) GetUserNumericLock(ctx context.Context, in *numeric_logic.GetUserNumericLockReq, opts ...grpc.CallOption) (*numeric_logic.GetUserNumericLockResp, error) {
	out := new(numeric_logic.GetUserNumericLockResp)
	err := c.cc.Invoke(ctx, "/ga.api.numeric.NumericLogic/GetUserNumericLock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericLogicClient) SetUserNumericLock(ctx context.Context, in *numeric_logic.SetUserNumericLockReq, opts ...grpc.CallOption) (*numeric_logic.SetUserNumericLockResp, error) {
	out := new(numeric_logic.SetUserNumericLockResp)
	err := c.cc.Invoke(ctx, "/ga.api.numeric.NumericLogic/SetUserNumericLock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericLogicClient) GetUserGloryRank(ctx context.Context, in *numeric_logic.GetUserGloryRankReq, opts ...grpc.CallOption) (*numeric_logic.GetUserGloryRankResp, error) {
	out := new(numeric_logic.GetUserGloryRankResp)
	err := c.cc.Invoke(ctx, "/ga.api.numeric.NumericLogic/GetUserGloryRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *numericLogicClient) GetUserVipGiftPackageInfo(ctx context.Context, in *numeric_logic.GetUserVipGiftPackageInfoReq, opts ...grpc.CallOption) (*numeric_logic.GetUserVipGiftPackageInfoResp, error) {
	out := new(numeric_logic.GetUserVipGiftPackageInfoResp)
	err := c.cc.Invoke(ctx, "/ga.api.numeric.NumericLogic/GetUserVipGiftPackageInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NumericLogicServer is the server API for NumericLogic service.
type NumericLogicServer interface {
	GetUserRichSwitch(context.Context, *numeric_logic.GetUserRichSwitchReq) (*numeric_logic.GetUserRichSwitchResp, error)
	SetUserRichSwitch(context.Context, *numeric_logic.SetUserRichSwitchReq) (*numeric_logic.SetUserRichSwitchResp, error)
	GetUserNumericLock(context.Context, *numeric_logic.GetUserNumericLockReq) (*numeric_logic.GetUserNumericLockResp, error)
	SetUserNumericLock(context.Context, *numeric_logic.SetUserNumericLockReq) (*numeric_logic.SetUserNumericLockResp, error)
	GetUserGloryRank(context.Context, *numeric_logic.GetUserGloryRankReq) (*numeric_logic.GetUserGloryRankResp, error)
	GetUserVipGiftPackageInfo(context.Context, *numeric_logic.GetUserVipGiftPackageInfoReq) (*numeric_logic.GetUserVipGiftPackageInfoResp, error)
}

func RegisterNumericLogicServer(s *grpc.Server, srv NumericLogicServer) {
	s.RegisterService(&_NumericLogic_serviceDesc, srv)
}

func _NumericLogic_GetUserRichSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(numeric_logic.GetUserRichSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericLogicServer).GetUserRichSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.numeric.NumericLogic/GetUserRichSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericLogicServer).GetUserRichSwitch(ctx, req.(*numeric_logic.GetUserRichSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericLogic_SetUserRichSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(numeric_logic.SetUserRichSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericLogicServer).SetUserRichSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.numeric.NumericLogic/SetUserRichSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericLogicServer).SetUserRichSwitch(ctx, req.(*numeric_logic.SetUserRichSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericLogic_GetUserNumericLock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(numeric_logic.GetUserNumericLockReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericLogicServer).GetUserNumericLock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.numeric.NumericLogic/GetUserNumericLock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericLogicServer).GetUserNumericLock(ctx, req.(*numeric_logic.GetUserNumericLockReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericLogic_SetUserNumericLock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(numeric_logic.SetUserNumericLockReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericLogicServer).SetUserNumericLock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.numeric.NumericLogic/SetUserNumericLock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericLogicServer).SetUserNumericLock(ctx, req.(*numeric_logic.SetUserNumericLockReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericLogic_GetUserGloryRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(numeric_logic.GetUserGloryRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericLogicServer).GetUserGloryRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.numeric.NumericLogic/GetUserGloryRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericLogicServer).GetUserGloryRank(ctx, req.(*numeric_logic.GetUserGloryRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NumericLogic_GetUserVipGiftPackageInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(numeric_logic.GetUserVipGiftPackageInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NumericLogicServer).GetUserVipGiftPackageInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.numeric.NumericLogic/GetUserVipGiftPackageInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NumericLogicServer).GetUserVipGiftPackageInfo(ctx, req.(*numeric_logic.GetUserVipGiftPackageInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _NumericLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.numeric.NumericLogic",
	HandlerType: (*NumericLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserRichSwitch",
			Handler:    _NumericLogic_GetUserRichSwitch_Handler,
		},
		{
			MethodName: "SetUserRichSwitch",
			Handler:    _NumericLogic_SetUserRichSwitch_Handler,
		},
		{
			MethodName: "GetUserNumericLock",
			Handler:    _NumericLogic_GetUserNumericLock_Handler,
		},
		{
			MethodName: "SetUserNumericLock",
			Handler:    _NumericLogic_SetUserNumericLock_Handler,
		},
		{
			MethodName: "GetUserGloryRank",
			Handler:    _NumericLogic_GetUserGloryRank_Handler,
		},
		{
			MethodName: "GetUserVipGiftPackageInfo",
			Handler:    _NumericLogic_GetUserVipGiftPackageInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/numeric/grpc_numeric.proto",
}

func init() {
	proto.RegisterFile("api/numeric/grpc_numeric.proto", fileDescriptor_grpc_numeric_f6c5b1965bc185d1)
}

var fileDescriptor_grpc_numeric_f6c5b1965bc185d1 = []byte{
	// 377 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x93, 0xbf, 0x4b, 0xc3, 0x40,
	0x14, 0xc7, 0x49, 0xfd, 0x41, 0x09, 0x2a, 0x7a, 0x88, 0x60, 0x40, 0x09, 0x05, 0xab, 0x8b, 0x77,
	0x52, 0x71, 0x72, 0xd3, 0xa1, 0x08, 0x22, 0x25, 0x41, 0x07, 0x97, 0x72, 0x1e, 0xe9, 0xf5, 0x48,
	0x7a, 0x77, 0x4d, 0xaf, 0x54, 0xb7, 0xd0, 0xc1, 0xc1, 0xd1, 0x3f, 0xc1, 0xc9, 0x1f, 0x9b, 0x8b,
	0x7f, 0x59, 0x26, 0x17, 0x49, 0x73, 0xc6, 0x96, 0xd2, 0x72, 0x4e, 0xb9, 0xcb, 0xfb, 0xbc, 0xf7,
	0xf9, 0x06, 0xf2, 0xec, 0x5d, 0x2c, 0x19, 0xe2, 0xfd, 0x4e, 0x10, 0x33, 0x82, 0x68, 0x2c, 0x49,
	0x53, 0x5f, 0xa0, 0x8c, 0x85, 0x12, 0x60, 0x8d, 0x62, 0x88, 0x25, 0x83, 0xfa, 0xad, 0x53, 0xd1,
	0x87, 0x66, 0x24, 0x28, 0x23, 0xbf, 0x9d, 0x87, 0xa3, 0x5b, 0x33, 0xef, 0x71, 0x76, 0xb2, 0x99,
	0xc1, 0xbd, 0x0a, 0x78, 0x8f, 0x09, 0xfe, 0x77, 0xca, 0xcb, 0xb5, 0xef, 0x25, 0x7b, 0xe5, 0x2a,
	0xef, 0xbb, 0xcc, 0xda, 0x00, 0xb7, 0x37, 0xea, 0x81, 0xba, 0xee, 0x05, 0xb1, 0xc7, 0x48, 0xdb,
	0x1f, 0x30, 0x45, 0xda, 0xa0, 0x0a, 0x29, 0x86, 0x13, 0x32, 0x38, 0x05, 0x79, 0x41, 0xd7, 0xd9,
	0x37, 0xe2, 0x7a, 0xb2, 0x52, 0x1e, 0x26, 0xee, 0x62, 0xf9, 0x35, 0xb5, 0x32, 0x9f, 0x6f, 0xe2,
	0xf3, 0x0d, 0x7d, 0xfe, 0x1c, 0xdf, 0x5b, 0x6a, 0x81, 0xae, 0x0d, 0x74, 0xa4, 0xe2, 0xb3, 0x49,
	0x08, 0x66, 0x07, 0x1f, 0xa3, 0x32, 0xe3, 0x81, 0x19, 0x58, 0x28, 0xdf, 0x73, 0xa5, 0x6f, 0xa4,
	0xf4, 0x4d, 0x95, 0xfe, 0x3c, 0xe5, 0x47, 0x6a, 0x81, 0xd0, 0x5e, 0xd7, 0xb1, 0xea, 0x91, 0x88,
	0x1f, 0x3c, 0xcc, 0x43, 0xb0, 0x37, 0x33, 0x7a, 0xc1, 0x64, 0xba, 0xaa, 0x09, 0x56, 0xc8, 0x3e,
	0x53, 0x0b, 0x3c, 0x5a, 0xf6, 0xb6, 0x46, 0x6e, 0x98, 0xac, 0xb3, 0x96, 0x6a, 0x60, 0x12, 0x62,
	0x1a, 0x5c, 0xf0, 0x96, 0x00, 0x70, 0xe6, 0xbc, 0x69, 0x38, 0xf3, 0xa3, 0x7f, 0xf1, 0x45, 0x90,
	0xaf, 0xd4, 0x72, 0xd0, 0x30, 0x71, 0x57, 0x27, 0xb6, 0xe0, 0x29, 0x71, 0x4b, 0x54, 0x3c, 0x27,
	0xee, 0x26, 0xca, 0x47, 0x8d, 0xff, 0xeb, 0xe8, 0xac, 0x61, 0x6f, 0x11, 0xd1, 0x81, 0xdd, 0xfe,
	0x00, 0x73, 0xa8, 0x54, 0xbe, 0x13, 0xd9, 0x8a, 0xdd, 0x1e, 0x51, 0x11, 0x61, 0x4e, 0xe1, 0x49,
	0x4d, 0x29, 0x48, 0x44, 0x07, 0x8d, 0x4a, 0x44, 0x44, 0x08, 0x4b, 0x89, 0xc6, 0xf6, 0xf4, 0x54,
	0x3f, 0x5f, 0x4a, 0x0b, 0x5e, 0xe3, 0xfc, 0x6e, 0x79, 0xc4, 0x1d, 0xff, 0x04, 0x00, 0x00, 0xff,
	0xff, 0x28, 0x2c, 0x78, 0x9f, 0xcb, 0x03, 0x00, 0x00,
}
