// Code generated by protoc-gen-go. DO NOT EDIT.
// source: numeric_logic/numeric-logic_.proto

package numeric_logic // import "golang.52tt.com/protocol/app/numeric-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 财富魅力值锁状态
type LockStatus int32

const (
	LockStatus_LOCK_STATUS_UNSPECIFIED LockStatus = 0
	LockStatus_LOCK_STATUS_ENABLE      LockStatus = 1
	LockStatus_LOCK_STATUS_DISABLE     LockStatus = 2
)

var LockStatus_name = map[int32]string{
	0: "LOCK_STATUS_UNSPECIFIED",
	1: "LOCK_STATUS_ENABLE",
	2: "LOCK_STATUS_DISABLE",
}
var LockStatus_value = map[string]int32{
	"LOCK_STATUS_UNSPECIFIED": 0,
	"LOCK_STATUS_ENABLE":      1,
	"LOCK_STATUS_DISABLE":     2,
}

func (x LockStatus) String() string {
	return proto.EnumName(LockStatus_name, int32(x))
}
func (LockStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{0}
}

type ChangeType int32

const (
	ChangeType_NoChange ChangeType = 0
	ChangeType_Rich     ChangeType = 1
	ChangeType_Charm    ChangeType = 2
)

var ChangeType_name = map[int32]string{
	0: "NoChange",
	1: "Rich",
	2: "Charm",
}
var ChangeType_value = map[string]int32{
	"NoChange": 0,
	"Rich":     1,
	"Charm":    2,
}

func (x ChangeType) String() string {
	return proto.EnumName(ChangeType_name, int32(x))
}
func (ChangeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{1}
}

type FromType int32

const (
	FromType_InvalidChangeType    FromType = 0
	FromType_PresentChange        FromType = 1
	FromType_ManualChange         FromType = 2
	FromType_KnightChange         FromType = 3
	FromType_YouKnownWhoChange    FromType = 4
	FromType_WolfChange           FromType = 5
	FromType_RichCardChange       FromType = 6
	FromType_BuyTreasurePrivilege FromType = 7
	FromType_BuyVirtualImage      FromType = 8
)

var FromType_name = map[int32]string{
	0: "InvalidChangeType",
	1: "PresentChange",
	2: "ManualChange",
	3: "KnightChange",
	4: "YouKnownWhoChange",
	5: "WolfChange",
	6: "RichCardChange",
	7: "BuyTreasurePrivilege",
	8: "BuyVirtualImage",
}
var FromType_value = map[string]int32{
	"InvalidChangeType":    0,
	"PresentChange":        1,
	"ManualChange":         2,
	"KnightChange":         3,
	"YouKnownWhoChange":    4,
	"WolfChange":           5,
	"RichCardChange":       6,
	"BuyTreasurePrivilege": 7,
	"BuyVirtualImage":      8,
}

func (x FromType) String() string {
	return proto.EnumName(FromType_name, int32(x))
}
func (FromType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{2}
}

type GloryRankType int32

const (
	GloryRankType_InvalidRankType     GloryRankType = 0
	GloryRankType_RichRank            GloryRankType = 1
	GloryRankType_CharmRank           GloryRankType = 2
	GloryRankType_ActivityRank        GloryRankType = 3
	GloryRankType_CelebrityRank       GloryRankType = 4
	GloryRankType_InteractGameRank    GloryRankType = 5
	GloryRankType_FellowAccompanyRank GloryRankType = 6
)

var GloryRankType_name = map[int32]string{
	0: "InvalidRankType",
	1: "RichRank",
	2: "CharmRank",
	3: "ActivityRank",
	4: "CelebrityRank",
	5: "InteractGameRank",
	6: "FellowAccompanyRank",
}
var GloryRankType_value = map[string]int32{
	"InvalidRankType":     0,
	"RichRank":            1,
	"CharmRank":           2,
	"ActivityRank":        3,
	"CelebrityRank":       4,
	"InteractGameRank":    5,
	"FellowAccompanyRank": 6,
}

func (x GloryRankType) String() string {
	return proto.EnumName(GloryRankType_name, int32(x))
}
func (GloryRankType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{3}
}

// VIP礼包推送类型
type VipPackUpdatePushType int32

const (
	VipPackUpdatePushType_VIP_PACK_ENTRANCE_PUSH_TYPE_UNSPECIFIED VipPackUpdatePushType = 0
	VipPackUpdatePushType_VIP_PACK_ENTRANCE_PUSH_TYPE_NEW         VipPackUpdatePushType = 1
	VipPackUpdatePushType_VIP_PACK_ENTRANCE_PUSH_TYPE_READ        VipPackUpdatePushType = 2
	VipPackUpdatePushType_VIP_PACK_ENTRANCE_PUSH_TYPE_RECEIVE     VipPackUpdatePushType = 3
)

var VipPackUpdatePushType_name = map[int32]string{
	0: "VIP_PACK_ENTRANCE_PUSH_TYPE_UNSPECIFIED",
	1: "VIP_PACK_ENTRANCE_PUSH_TYPE_NEW",
	2: "VIP_PACK_ENTRANCE_PUSH_TYPE_READ",
	3: "VIP_PACK_ENTRANCE_PUSH_TYPE_RECEIVE",
}
var VipPackUpdatePushType_value = map[string]int32{
	"VIP_PACK_ENTRANCE_PUSH_TYPE_UNSPECIFIED": 0,
	"VIP_PACK_ENTRANCE_PUSH_TYPE_NEW":         1,
	"VIP_PACK_ENTRANCE_PUSH_TYPE_READ":        2,
	"VIP_PACK_ENTRANCE_PUSH_TYPE_RECEIVE":     3,
}

func (x VipPackUpdatePushType) String() string {
	return proto.EnumName(VipPackUpdatePushType_name, int32(x))
}
func (VipPackUpdatePushType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{4}
}

// 新财富魅力值锁
type UserNumericLock struct {
	BeanRichLock         LockStatus `protobuf:"varint,1,opt,name=bean_rich_lock,json=beanRichLock,proto3,enum=ga.numeric_logic.LockStatus" json:"bean_rich_lock,omitempty"`
	DiamondRichLock      LockStatus `protobuf:"varint,2,opt,name=diamond_rich_lock,json=diamondRichLock,proto3,enum=ga.numeric_logic.LockStatus" json:"diamond_rich_lock,omitempty"`
	BeanCharmLock        LockStatus `protobuf:"varint,3,opt,name=bean_charm_lock,json=beanCharmLock,proto3,enum=ga.numeric_logic.LockStatus" json:"bean_charm_lock,omitempty"`
	DiamondCharmLock     LockStatus `protobuf:"varint,4,opt,name=diamond_charm_lock,json=diamondCharmLock,proto3,enum=ga.numeric_logic.LockStatus" json:"diamond_charm_lock,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UserNumericLock) Reset()         { *m = UserNumericLock{} }
func (m *UserNumericLock) String() string { return proto.CompactTextString(m) }
func (*UserNumericLock) ProtoMessage()    {}
func (*UserNumericLock) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{0}
}
func (m *UserNumericLock) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserNumericLock.Unmarshal(m, b)
}
func (m *UserNumericLock) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserNumericLock.Marshal(b, m, deterministic)
}
func (dst *UserNumericLock) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserNumericLock.Merge(dst, src)
}
func (m *UserNumericLock) XXX_Size() int {
	return xxx_messageInfo_UserNumericLock.Size(m)
}
func (m *UserNumericLock) XXX_DiscardUnknown() {
	xxx_messageInfo_UserNumericLock.DiscardUnknown(m)
}

var xxx_messageInfo_UserNumericLock proto.InternalMessageInfo

func (m *UserNumericLock) GetBeanRichLock() LockStatus {
	if m != nil {
		return m.BeanRichLock
	}
	return LockStatus_LOCK_STATUS_UNSPECIFIED
}

func (m *UserNumericLock) GetDiamondRichLock() LockStatus {
	if m != nil {
		return m.DiamondRichLock
	}
	return LockStatus_LOCK_STATUS_UNSPECIFIED
}

func (m *UserNumericLock) GetBeanCharmLock() LockStatus {
	if m != nil {
		return m.BeanCharmLock
	}
	return LockStatus_LOCK_STATUS_UNSPECIFIED
}

func (m *UserNumericLock) GetDiamondCharmLock() LockStatus {
	if m != nil {
		return m.DiamondCharmLock
	}
	return LockStatus_LOCK_STATUS_UNSPECIFIED
}

// 获取用户财富魅力值锁状态
type GetUserNumericLockReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserNumericLockReq) Reset()         { *m = GetUserNumericLockReq{} }
func (m *GetUserNumericLockReq) String() string { return proto.CompactTextString(m) }
func (*GetUserNumericLockReq) ProtoMessage()    {}
func (*GetUserNumericLockReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{1}
}
func (m *GetUserNumericLockReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserNumericLockReq.Unmarshal(m, b)
}
func (m *GetUserNumericLockReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserNumericLockReq.Marshal(b, m, deterministic)
}
func (dst *GetUserNumericLockReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserNumericLockReq.Merge(dst, src)
}
func (m *GetUserNumericLockReq) XXX_Size() int {
	return xxx_messageInfo_GetUserNumericLockReq.Size(m)
}
func (m *GetUserNumericLockReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserNumericLockReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserNumericLockReq proto.InternalMessageInfo

func (m *GetUserNumericLockReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetUserNumericLockResp struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Locks                *UserNumericLock `protobuf:"bytes,2,opt,name=locks,proto3" json:"locks,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserNumericLockResp) Reset()         { *m = GetUserNumericLockResp{} }
func (m *GetUserNumericLockResp) String() string { return proto.CompactTextString(m) }
func (*GetUserNumericLockResp) ProtoMessage()    {}
func (*GetUserNumericLockResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{2}
}
func (m *GetUserNumericLockResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserNumericLockResp.Unmarshal(m, b)
}
func (m *GetUserNumericLockResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserNumericLockResp.Marshal(b, m, deterministic)
}
func (dst *GetUserNumericLockResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserNumericLockResp.Merge(dst, src)
}
func (m *GetUserNumericLockResp) XXX_Size() int {
	return xxx_messageInfo_GetUserNumericLockResp.Size(m)
}
func (m *GetUserNumericLockResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserNumericLockResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserNumericLockResp proto.InternalMessageInfo

func (m *GetUserNumericLockResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserNumericLockResp) GetLocks() *UserNumericLock {
	if m != nil {
		return m.Locks
	}
	return nil
}

// 设置用户财富魅力值锁状态
type SetUserNumericLockReq struct {
	BaseReq              *app.BaseReq     `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Locks                *UserNumericLock `protobuf:"bytes,3,opt,name=locks,proto3" json:"locks,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SetUserNumericLockReq) Reset()         { *m = SetUserNumericLockReq{} }
func (m *SetUserNumericLockReq) String() string { return proto.CompactTextString(m) }
func (*SetUserNumericLockReq) ProtoMessage()    {}
func (*SetUserNumericLockReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{3}
}
func (m *SetUserNumericLockReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserNumericLockReq.Unmarshal(m, b)
}
func (m *SetUserNumericLockReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserNumericLockReq.Marshal(b, m, deterministic)
}
func (dst *SetUserNumericLockReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserNumericLockReq.Merge(dst, src)
}
func (m *SetUserNumericLockReq) XXX_Size() int {
	return xxx_messageInfo_SetUserNumericLockReq.Size(m)
}
func (m *SetUserNumericLockReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserNumericLockReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserNumericLockReq proto.InternalMessageInfo

func (m *SetUserNumericLockReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetUserNumericLockReq) GetLocks() *UserNumericLock {
	if m != nil {
		return m.Locks
	}
	return nil
}

type SetUserNumericLockResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetUserNumericLockResp) Reset()         { *m = SetUserNumericLockResp{} }
func (m *SetUserNumericLockResp) String() string { return proto.CompactTextString(m) }
func (*SetUserNumericLockResp) ProtoMessage()    {}
func (*SetUserNumericLockResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{4}
}
func (m *SetUserNumericLockResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserNumericLockResp.Unmarshal(m, b)
}
func (m *SetUserNumericLockResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserNumericLockResp.Marshal(b, m, deterministic)
}
func (dst *SetUserNumericLockResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserNumericLockResp.Merge(dst, src)
}
func (m *SetUserNumericLockResp) XXX_Size() int {
	return xxx_messageInfo_SetUserNumericLockResp.Size(m)
}
func (m *SetUserNumericLockResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserNumericLockResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserNumericLockResp proto.InternalMessageInfo

func (m *SetUserNumericLockResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 废弃
type GetUserRichSwitchReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserRichSwitchReq) Reset()         { *m = GetUserRichSwitchReq{} }
func (m *GetUserRichSwitchReq) String() string { return proto.CompactTextString(m) }
func (*GetUserRichSwitchReq) ProtoMessage()    {}
func (*GetUserRichSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{5}
}
func (m *GetUserRichSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRichSwitchReq.Unmarshal(m, b)
}
func (m *GetUserRichSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRichSwitchReq.Marshal(b, m, deterministic)
}
func (dst *GetUserRichSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRichSwitchReq.Merge(dst, src)
}
func (m *GetUserRichSwitchReq) XXX_Size() int {
	return xxx_messageInfo_GetUserRichSwitchReq.Size(m)
}
func (m *GetUserRichSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRichSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRichSwitchReq proto.InternalMessageInfo

func (m *GetUserRichSwitchReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserRichSwitchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserRichSwitchResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Enable               bool          `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserRichSwitchResp) Reset()         { *m = GetUserRichSwitchResp{} }
func (m *GetUserRichSwitchResp) String() string { return proto.CompactTextString(m) }
func (*GetUserRichSwitchResp) ProtoMessage()    {}
func (*GetUserRichSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{6}
}
func (m *GetUserRichSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRichSwitchResp.Unmarshal(m, b)
}
func (m *GetUserRichSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRichSwitchResp.Marshal(b, m, deterministic)
}
func (dst *GetUserRichSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRichSwitchResp.Merge(dst, src)
}
func (m *GetUserRichSwitchResp) XXX_Size() int {
	return xxx_messageInfo_GetUserRichSwitchResp.Size(m)
}
func (m *GetUserRichSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRichSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRichSwitchResp proto.InternalMessageInfo

func (m *GetUserRichSwitchResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserRichSwitchResp) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

// 废弃
type SetUserRichSwitchReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Enable               bool         `protobuf:"varint,3,opt,name=enable,proto3" json:"enable,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetUserRichSwitchReq) Reset()         { *m = SetUserRichSwitchReq{} }
func (m *SetUserRichSwitchReq) String() string { return proto.CompactTextString(m) }
func (*SetUserRichSwitchReq) ProtoMessage()    {}
func (*SetUserRichSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{7}
}
func (m *SetUserRichSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserRichSwitchReq.Unmarshal(m, b)
}
func (m *SetUserRichSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserRichSwitchReq.Marshal(b, m, deterministic)
}
func (dst *SetUserRichSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserRichSwitchReq.Merge(dst, src)
}
func (m *SetUserRichSwitchReq) XXX_Size() int {
	return xxx_messageInfo_SetUserRichSwitchReq.Size(m)
}
func (m *SetUserRichSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserRichSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserRichSwitchReq proto.InternalMessageInfo

func (m *SetUserRichSwitchReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetUserRichSwitchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserRichSwitchReq) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

type SetUserRichSwitchResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetUserRichSwitchResp) Reset()         { *m = SetUserRichSwitchResp{} }
func (m *SetUserRichSwitchResp) String() string { return proto.CompactTextString(m) }
func (*SetUserRichSwitchResp) ProtoMessage()    {}
func (*SetUserRichSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{8}
}
func (m *SetUserRichSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserRichSwitchResp.Unmarshal(m, b)
}
func (m *SetUserRichSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserRichSwitchResp.Marshal(b, m, deterministic)
}
func (dst *SetUserRichSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserRichSwitchResp.Merge(dst, src)
}
func (m *SetUserRichSwitchResp) XXX_Size() int {
	return xxx_messageInfo_SetUserRichSwitchResp.Size(m)
}
func (m *SetUserRichSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserRichSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserRichSwitchResp proto.InternalMessageInfo

func (m *SetUserRichSwitchResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type RichCharmChangeMsg struct {
	FromType              FromType   `protobuf:"varint,1,opt,name=from_type,json=fromType,proto3,enum=ga.numeric_logic.FromType" json:"from_type,omitempty"`
	ChangeType            ChangeType `protobuf:"varint,2,opt,name=change_type,json=changeType,proto3,enum=ga.numeric_logic.ChangeType" json:"change_type,omitempty"`
	Value                 uint32     `protobuf:"varint,3,opt,name=value,proto3" json:"value,omitempty"`
	Uid                   uint32     `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	HasAcceleratorCard    bool       `protobuf:"varint,5,opt,name=has_accelerator_card,json=hasAcceleratorCard,proto3" json:"has_accelerator_card,omitempty"`
	AcceleratorRate       float32    `protobuf:"fixed32,6,opt,name=accelerator_rate,json=acceleratorRate,proto3" json:"accelerator_rate,omitempty"`
	AcceleratorExtraValue uint32     `protobuf:"varint,7,opt,name=accelerator_extra_value,json=acceleratorExtraValue,proto3" json:"accelerator_extra_value,omitempty"`
	OriginValue           uint32     `protobuf:"varint,8,opt,name=origin_value,json=originValue,proto3" json:"origin_value,omitempty"`
	FromDesc              string     `protobuf:"bytes,9,opt,name=from_desc,json=fromDesc,proto3" json:"from_desc,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}   `json:"-"`
	XXX_unrecognized      []byte     `json:"-"`
	XXX_sizecache         int32      `json:"-"`
}

func (m *RichCharmChangeMsg) Reset()         { *m = RichCharmChangeMsg{} }
func (m *RichCharmChangeMsg) String() string { return proto.CompactTextString(m) }
func (*RichCharmChangeMsg) ProtoMessage()    {}
func (*RichCharmChangeMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{9}
}
func (m *RichCharmChangeMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RichCharmChangeMsg.Unmarshal(m, b)
}
func (m *RichCharmChangeMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RichCharmChangeMsg.Marshal(b, m, deterministic)
}
func (dst *RichCharmChangeMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RichCharmChangeMsg.Merge(dst, src)
}
func (m *RichCharmChangeMsg) XXX_Size() int {
	return xxx_messageInfo_RichCharmChangeMsg.Size(m)
}
func (m *RichCharmChangeMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_RichCharmChangeMsg.DiscardUnknown(m)
}

var xxx_messageInfo_RichCharmChangeMsg proto.InternalMessageInfo

func (m *RichCharmChangeMsg) GetFromType() FromType {
	if m != nil {
		return m.FromType
	}
	return FromType_InvalidChangeType
}

func (m *RichCharmChangeMsg) GetChangeType() ChangeType {
	if m != nil {
		return m.ChangeType
	}
	return ChangeType_NoChange
}

func (m *RichCharmChangeMsg) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *RichCharmChangeMsg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RichCharmChangeMsg) GetHasAcceleratorCard() bool {
	if m != nil {
		return m.HasAcceleratorCard
	}
	return false
}

func (m *RichCharmChangeMsg) GetAcceleratorRate() float32 {
	if m != nil {
		return m.AcceleratorRate
	}
	return 0
}

func (m *RichCharmChangeMsg) GetAcceleratorExtraValue() uint32 {
	if m != nil {
		return m.AcceleratorExtraValue
	}
	return 0
}

func (m *RichCharmChangeMsg) GetOriginValue() uint32 {
	if m != nil {
		return m.OriginValue
	}
	return 0
}

func (m *RichCharmChangeMsg) GetFromDesc() string {
	if m != nil {
		return m.FromDesc
	}
	return ""
}

type GloryRank struct {
	RankType GloryRankType `protobuf:"varint,1,opt,name=rank_type,json=rankType,proto3,enum=ga.numeric_logic.GloryRankType" json:"rank_type,omitempty"`
	Uid      uint32        `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// 废弃 2023.09
	DayRank   uint32 `protobuf:"varint,5,opt,name=day_rank,json=dayRank,proto3" json:"day_rank,omitempty"`
	WeekRank  uint32 `protobuf:"varint,6,opt,name=week_rank,json=weekRank,proto3" json:"week_rank,omitempty"`
	MonthRank uint32 `protobuf:"varint,7,opt,name=month_rank,json=monthRank,proto3" json:"month_rank,omitempty"`
	// 通用榜单（财富、魅力、活动、游戏 等）
	// eg: activity_name + rank_name + activity_rank
	RankSign             string   `protobuf:"bytes,3,opt,name=rank_sign,json=rankSign,proto3" json:"rank_sign,omitempty"`
	RankName             string   `protobuf:"bytes,4,opt,name=rank_name,json=rankName,proto3" json:"rank_name,omitempty"`
	ActivitySign         string   `protobuf:"bytes,9,opt,name=activity_sign,json=activitySign,proto3" json:"activity_sign,omitempty"`
	ActivityName         string   `protobuf:"bytes,10,opt,name=activity_name,json=activityName,proto3" json:"activity_name,omitempty"`
	ActivityRank         uint32   `protobuf:"varint,11,opt,name=activity_rank,json=activityRank,proto3" json:"activity_rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GloryRank) Reset()         { *m = GloryRank{} }
func (m *GloryRank) String() string { return proto.CompactTextString(m) }
func (*GloryRank) ProtoMessage()    {}
func (*GloryRank) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{10}
}
func (m *GloryRank) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GloryRank.Unmarshal(m, b)
}
func (m *GloryRank) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GloryRank.Marshal(b, m, deterministic)
}
func (dst *GloryRank) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GloryRank.Merge(dst, src)
}
func (m *GloryRank) XXX_Size() int {
	return xxx_messageInfo_GloryRank.Size(m)
}
func (m *GloryRank) XXX_DiscardUnknown() {
	xxx_messageInfo_GloryRank.DiscardUnknown(m)
}

var xxx_messageInfo_GloryRank proto.InternalMessageInfo

func (m *GloryRank) GetRankType() GloryRankType {
	if m != nil {
		return m.RankType
	}
	return GloryRankType_InvalidRankType
}

func (m *GloryRank) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GloryRank) GetDayRank() uint32 {
	if m != nil {
		return m.DayRank
	}
	return 0
}

func (m *GloryRank) GetWeekRank() uint32 {
	if m != nil {
		return m.WeekRank
	}
	return 0
}

func (m *GloryRank) GetMonthRank() uint32 {
	if m != nil {
		return m.MonthRank
	}
	return 0
}

func (m *GloryRank) GetRankSign() string {
	if m != nil {
		return m.RankSign
	}
	return ""
}

func (m *GloryRank) GetRankName() string {
	if m != nil {
		return m.RankName
	}
	return ""
}

func (m *GloryRank) GetActivitySign() string {
	if m != nil {
		return m.ActivitySign
	}
	return ""
}

func (m *GloryRank) GetActivityName() string {
	if m != nil {
		return m.ActivityName
	}
	return ""
}

func (m *GloryRank) GetActivityRank() uint32 {
	if m != nil {
		return m.ActivityRank
	}
	return 0
}

// 获取用户荣誉排名
type GetUserGloryRankReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserGloryRankReq) Reset()         { *m = GetUserGloryRankReq{} }
func (m *GetUserGloryRankReq) String() string { return proto.CompactTextString(m) }
func (*GetUserGloryRankReq) ProtoMessage()    {}
func (*GetUserGloryRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{11}
}
func (m *GetUserGloryRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGloryRankReq.Unmarshal(m, b)
}
func (m *GetUserGloryRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGloryRankReq.Marshal(b, m, deterministic)
}
func (dst *GetUserGloryRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGloryRankReq.Merge(dst, src)
}
func (m *GetUserGloryRankReq) XXX_Size() int {
	return xxx_messageInfo_GetUserGloryRankReq.Size(m)
}
func (m *GetUserGloryRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGloryRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGloryRankReq proto.InternalMessageInfo

func (m *GetUserGloryRankReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserGloryRankReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserGloryRankResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UserRankList         []*GloryRank  `protobuf:"bytes,2,rep,name=user_rank_list,json=userRankList,proto3" json:"user_rank_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserGloryRankResp) Reset()         { *m = GetUserGloryRankResp{} }
func (m *GetUserGloryRankResp) String() string { return proto.CompactTextString(m) }
func (*GetUserGloryRankResp) ProtoMessage()    {}
func (*GetUserGloryRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{12}
}
func (m *GetUserGloryRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGloryRankResp.Unmarshal(m, b)
}
func (m *GetUserGloryRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGloryRankResp.Marshal(b, m, deterministic)
}
func (dst *GetUserGloryRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGloryRankResp.Merge(dst, src)
}
func (m *GetUserGloryRankResp) XXX_Size() int {
	return xxx_messageInfo_GetUserGloryRankResp.Size(m)
}
func (m *GetUserGloryRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGloryRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGloryRankResp proto.InternalMessageInfo

func (m *GetUserGloryRankResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserGloryRankResp) GetUserRankList() []*GloryRank {
	if m != nil {
		return m.UserRankList
	}
	return nil
}

// 进房推送
type GloryRankChannelPushMsg struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UserRankList         []*GloryRank `protobuf:"bytes,2,rep,name=user_rank_list,json=userRankList,proto3" json:"user_rank_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GloryRankChannelPushMsg) Reset()         { *m = GloryRankChannelPushMsg{} }
func (m *GloryRankChannelPushMsg) String() string { return proto.CompactTextString(m) }
func (*GloryRankChannelPushMsg) ProtoMessage()    {}
func (*GloryRankChannelPushMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{13}
}
func (m *GloryRankChannelPushMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GloryRankChannelPushMsg.Unmarshal(m, b)
}
func (m *GloryRankChannelPushMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GloryRankChannelPushMsg.Marshal(b, m, deterministic)
}
func (dst *GloryRankChannelPushMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GloryRankChannelPushMsg.Merge(dst, src)
}
func (m *GloryRankChannelPushMsg) XXX_Size() int {
	return xxx_messageInfo_GloryRankChannelPushMsg.Size(m)
}
func (m *GloryRankChannelPushMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_GloryRankChannelPushMsg.DiscardUnknown(m)
}

var xxx_messageInfo_GloryRankChannelPushMsg proto.InternalMessageInfo

func (m *GloryRankChannelPushMsg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GloryRankChannelPushMsg) GetUserRankList() []*GloryRank {
	if m != nil {
		return m.UserRankList
	}
	return nil
}

// 获取用户VIP礼包信息
type GetUserVipGiftPackageInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserVipGiftPackageInfoReq) Reset()         { *m = GetUserVipGiftPackageInfoReq{} }
func (m *GetUserVipGiftPackageInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserVipGiftPackageInfoReq) ProtoMessage()    {}
func (*GetUserVipGiftPackageInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{14}
}
func (m *GetUserVipGiftPackageInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserVipGiftPackageInfoReq.Unmarshal(m, b)
}
func (m *GetUserVipGiftPackageInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserVipGiftPackageInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserVipGiftPackageInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserVipGiftPackageInfoReq.Merge(dst, src)
}
func (m *GetUserVipGiftPackageInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserVipGiftPackageInfoReq.Size(m)
}
func (m *GetUserVipGiftPackageInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserVipGiftPackageInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserVipGiftPackageInfoReq proto.InternalMessageInfo

func (m *GetUserVipGiftPackageInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetUserVipGiftPackageInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	HasVipGiftPackage    bool          `protobuf:"varint,2,opt,name=has_vip_gift_package,json=hasVipGiftPackage,proto3" json:"has_vip_gift_package,omitempty"`
	ShowRedDot           bool          `protobuf:"varint,3,opt,name=show_red_dot,json=showRedDot,proto3" json:"show_red_dot,omitempty"`
	EntranceIconUrl      string        `protobuf:"bytes,4,opt,name=entrance_icon_url,json=entranceIconUrl,proto3" json:"entrance_icon_url,omitempty"`
	EntranceIconMd5      string        `protobuf:"bytes,5,opt,name=entrance_icon_md5,json=entranceIconMd5,proto3" json:"entrance_icon_md5,omitempty"`
	EntranceJump         string        `protobuf:"bytes,6,opt,name=entrance_jump,json=entranceJump,proto3" json:"entrance_jump,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserVipGiftPackageInfoResp) Reset()         { *m = GetUserVipGiftPackageInfoResp{} }
func (m *GetUserVipGiftPackageInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserVipGiftPackageInfoResp) ProtoMessage()    {}
func (*GetUserVipGiftPackageInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{15}
}
func (m *GetUserVipGiftPackageInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserVipGiftPackageInfoResp.Unmarshal(m, b)
}
func (m *GetUserVipGiftPackageInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserVipGiftPackageInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserVipGiftPackageInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserVipGiftPackageInfoResp.Merge(dst, src)
}
func (m *GetUserVipGiftPackageInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserVipGiftPackageInfoResp.Size(m)
}
func (m *GetUserVipGiftPackageInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserVipGiftPackageInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserVipGiftPackageInfoResp proto.InternalMessageInfo

func (m *GetUserVipGiftPackageInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserVipGiftPackageInfoResp) GetHasVipGiftPackage() bool {
	if m != nil {
		return m.HasVipGiftPackage
	}
	return false
}

func (m *GetUserVipGiftPackageInfoResp) GetShowRedDot() bool {
	if m != nil {
		return m.ShowRedDot
	}
	return false
}

func (m *GetUserVipGiftPackageInfoResp) GetEntranceIconUrl() string {
	if m != nil {
		return m.EntranceIconUrl
	}
	return ""
}

func (m *GetUserVipGiftPackageInfoResp) GetEntranceIconMd5() string {
	if m != nil {
		return m.EntranceIconMd5
	}
	return ""
}

func (m *GetUserVipGiftPackageInfoResp) GetEntranceJump() string {
	if m != nil {
		return m.EntranceJump
	}
	return ""
}

// VIP礼包推送消息
type VipPackUpdatePushMsg struct {
	PushType             VipPackUpdatePushType `protobuf:"varint,1,opt,name=push_type,json=pushType,proto3,enum=ga.numeric_logic.VipPackUpdatePushType" json:"push_type,omitempty"`
	Uid                  uint32                `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	RichVal              uint64                `protobuf:"varint,3,opt,name=rich_val,json=richVal,proto3" json:"rich_val,omitempty"`
	VipLevel             uint32                `protobuf:"varint,4,opt,name=vip_level,json=vipLevel,proto3" json:"vip_level,omitempty"`
	HasVipGiftPackage    bool                  `protobuf:"varint,5,opt,name=has_vip_gift_package,json=hasVipGiftPackage,proto3" json:"has_vip_gift_package,omitempty"`
	ShowRedDot           bool                  `protobuf:"varint,6,opt,name=show_red_dot,json=showRedDot,proto3" json:"show_red_dot,omitempty"`
	EntranceIconUrl      string                `protobuf:"bytes,7,opt,name=entrance_icon_url,json=entranceIconUrl,proto3" json:"entrance_icon_url,omitempty"`
	EntranceIconMd5      string                `protobuf:"bytes,8,opt,name=entrance_icon_md5,json=entranceIconMd5,proto3" json:"entrance_icon_md5,omitempty"`
	EntranceJump         string                `protobuf:"bytes,9,opt,name=entrance_jump,json=entranceJump,proto3" json:"entrance_jump,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *VipPackUpdatePushMsg) Reset()         { *m = VipPackUpdatePushMsg{} }
func (m *VipPackUpdatePushMsg) String() string { return proto.CompactTextString(m) }
func (*VipPackUpdatePushMsg) ProtoMessage()    {}
func (*VipPackUpdatePushMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_numeric_logic__51896e626d801c63, []int{16}
}
func (m *VipPackUpdatePushMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VipPackUpdatePushMsg.Unmarshal(m, b)
}
func (m *VipPackUpdatePushMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VipPackUpdatePushMsg.Marshal(b, m, deterministic)
}
func (dst *VipPackUpdatePushMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VipPackUpdatePushMsg.Merge(dst, src)
}
func (m *VipPackUpdatePushMsg) XXX_Size() int {
	return xxx_messageInfo_VipPackUpdatePushMsg.Size(m)
}
func (m *VipPackUpdatePushMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_VipPackUpdatePushMsg.DiscardUnknown(m)
}

var xxx_messageInfo_VipPackUpdatePushMsg proto.InternalMessageInfo

func (m *VipPackUpdatePushMsg) GetPushType() VipPackUpdatePushType {
	if m != nil {
		return m.PushType
	}
	return VipPackUpdatePushType_VIP_PACK_ENTRANCE_PUSH_TYPE_UNSPECIFIED
}

func (m *VipPackUpdatePushMsg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *VipPackUpdatePushMsg) GetRichVal() uint64 {
	if m != nil {
		return m.RichVal
	}
	return 0
}

func (m *VipPackUpdatePushMsg) GetVipLevel() uint32 {
	if m != nil {
		return m.VipLevel
	}
	return 0
}

func (m *VipPackUpdatePushMsg) GetHasVipGiftPackage() bool {
	if m != nil {
		return m.HasVipGiftPackage
	}
	return false
}

func (m *VipPackUpdatePushMsg) GetShowRedDot() bool {
	if m != nil {
		return m.ShowRedDot
	}
	return false
}

func (m *VipPackUpdatePushMsg) GetEntranceIconUrl() string {
	if m != nil {
		return m.EntranceIconUrl
	}
	return ""
}

func (m *VipPackUpdatePushMsg) GetEntranceIconMd5() string {
	if m != nil {
		return m.EntranceIconMd5
	}
	return ""
}

func (m *VipPackUpdatePushMsg) GetEntranceJump() string {
	if m != nil {
		return m.EntranceJump
	}
	return ""
}

func init() {
	proto.RegisterType((*UserNumericLock)(nil), "ga.numeric_logic.UserNumericLock")
	proto.RegisterType((*GetUserNumericLockReq)(nil), "ga.numeric_logic.GetUserNumericLockReq")
	proto.RegisterType((*GetUserNumericLockResp)(nil), "ga.numeric_logic.GetUserNumericLockResp")
	proto.RegisterType((*SetUserNumericLockReq)(nil), "ga.numeric_logic.SetUserNumericLockReq")
	proto.RegisterType((*SetUserNumericLockResp)(nil), "ga.numeric_logic.SetUserNumericLockResp")
	proto.RegisterType((*GetUserRichSwitchReq)(nil), "ga.numeric_logic.GetUserRichSwitchReq")
	proto.RegisterType((*GetUserRichSwitchResp)(nil), "ga.numeric_logic.GetUserRichSwitchResp")
	proto.RegisterType((*SetUserRichSwitchReq)(nil), "ga.numeric_logic.SetUserRichSwitchReq")
	proto.RegisterType((*SetUserRichSwitchResp)(nil), "ga.numeric_logic.SetUserRichSwitchResp")
	proto.RegisterType((*RichCharmChangeMsg)(nil), "ga.numeric_logic.RichCharmChangeMsg")
	proto.RegisterType((*GloryRank)(nil), "ga.numeric_logic.GloryRank")
	proto.RegisterType((*GetUserGloryRankReq)(nil), "ga.numeric_logic.GetUserGloryRankReq")
	proto.RegisterType((*GetUserGloryRankResp)(nil), "ga.numeric_logic.GetUserGloryRankResp")
	proto.RegisterType((*GloryRankChannelPushMsg)(nil), "ga.numeric_logic.GloryRankChannelPushMsg")
	proto.RegisterType((*GetUserVipGiftPackageInfoReq)(nil), "ga.numeric_logic.GetUserVipGiftPackageInfoReq")
	proto.RegisterType((*GetUserVipGiftPackageInfoResp)(nil), "ga.numeric_logic.GetUserVipGiftPackageInfoResp")
	proto.RegisterType((*VipPackUpdatePushMsg)(nil), "ga.numeric_logic.VipPackUpdatePushMsg")
	proto.RegisterEnum("ga.numeric_logic.LockStatus", LockStatus_name, LockStatus_value)
	proto.RegisterEnum("ga.numeric_logic.ChangeType", ChangeType_name, ChangeType_value)
	proto.RegisterEnum("ga.numeric_logic.FromType", FromType_name, FromType_value)
	proto.RegisterEnum("ga.numeric_logic.GloryRankType", GloryRankType_name, GloryRankType_value)
	proto.RegisterEnum("ga.numeric_logic.VipPackUpdatePushType", VipPackUpdatePushType_name, VipPackUpdatePushType_value)
}

func init() {
	proto.RegisterFile("numeric_logic/numeric-logic_.proto", fileDescriptor_numeric_logic__51896e626d801c63)
}

var fileDescriptor_numeric_logic__51896e626d801c63 = []byte{
	// 1385 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x57, 0x5d, 0x53, 0xdb, 0x46,
	0x17, 0x46, 0x36, 0x1f, 0xd6, 0xc1, 0x06, 0xb1, 0xe1, 0xeb, 0x0d, 0xc9, 0x84, 0x38, 0xef, 0xbc,
	0x21, 0xbc, 0x53, 0xe8, 0xd0, 0x49, 0x73, 0xd3, 0x4e, 0xc7, 0x18, 0x43, 0x1c, 0x88, 0xe3, 0x91,
	0x81, 0x4c, 0x72, 0xa3, 0x59, 0xa4, 0x45, 0xda, 0x22, 0xed, 0x2a, 0x2b, 0xc9, 0xc4, 0x33, 0xbd,
	0xec, 0x4d, 0x7f, 0x40, 0x6f, 0xfa, 0x2f, 0x7a, 0xd7, 0xeb, 0xfe, 0x8d, 0xfe, 0x8f, 0x5e, 0x77,
	0x76, 0x25, 0xdb, 0x02, 0x13, 0x06, 0x37, 0xb9, 0xdb, 0x3d, 0xe7, 0x39, 0xcf, 0x39, 0xfb, 0x9c,
	0xdd, 0x23, 0x1b, 0xaa, 0x2c, 0x09, 0x88, 0xa0, 0xb6, 0xe5, 0x73, 0x97, 0xda, 0xdb, 0xd9, 0xee,
	0x2b, 0xb5, 0xb3, 0xb6, 0x42, 0xc1, 0x63, 0x8e, 0x0c, 0x17, 0x6f, 0x5d, 0x81, 0xdd, 0xaf, 0xb8,
	0xd8, 0x3a, 0xc3, 0x11, 0x49, 0x01, 0xd5, 0xdf, 0x0b, 0x30, 0x7f, 0x12, 0x11, 0xd1, 0x4a, 0x41,
	0x47, 0xdc, 0xbe, 0x40, 0xbb, 0x30, 0x77, 0x46, 0x30, 0xb3, 0x04, 0xb5, 0x3d, 0xcb, 0xe7, 0xf6,
	0xc5, 0xaa, 0xb6, 0xae, 0x6d, 0xcc, 0xed, 0x3c, 0xd8, 0xba, 0xce, 0xb6, 0x25, 0xf1, 0x9d, 0x18,
	0xc7, 0x49, 0x64, 0x96, 0x65, 0x8c, 0x49, 0x6d, 0x4f, 0x71, 0xbc, 0x84, 0x05, 0x87, 0xe2, 0x80,
	0x33, 0x27, 0x47, 0x53, 0xb8, 0x03, 0xcd, 0x7c, 0x16, 0x36, 0x60, 0xda, 0x83, 0x79, 0x55, 0x8d,
	0xed, 0x61, 0x11, 0xa4, 0x3c, 0xc5, 0x3b, 0xf0, 0x54, 0x64, 0x50, 0x5d, 0xc6, 0x28, 0x96, 0x57,
	0x80, 0xfa, 0xf5, 0xe4, 0x88, 0x26, 0xef, 0x40, 0x64, 0x64, 0x71, 0x03, 0xae, 0xea, 0x0f, 0xb0,
	0x74, 0x40, 0xe2, 0x6b, 0xaa, 0x99, 0xe4, 0x03, 0xfa, 0x1f, 0x94, 0xa4, 0xb4, 0x96, 0x20, 0x1f,
	0x94, 0x64, 0xb3, 0x3b, 0xb3, 0x92, 0x7a, 0x17, 0x47, 0xc4, 0x24, 0x1f, 0xcc, 0x99, 0xb3, 0x74,
	0x51, 0xfd, 0x09, 0x96, 0x6f, 0x22, 0x88, 0x42, 0xf4, 0x0c, 0xf4, 0x8c, 0x21, 0x0a, 0x33, 0x8a,
	0xf2, 0x90, 0x22, 0x0a, 0xcd, 0xd2, 0x59, 0xb6, 0x42, 0x2f, 0x60, 0x4a, 0x9e, 0x21, 0x52, 0xaa,
	0xce, 0xee, 0x3c, 0x1e, 0x3d, 0xc4, 0xf5, 0x04, 0x29, 0xbe, 0xfa, 0x11, 0x96, 0x3a, 0x9f, 0x53,
	0xfe, 0x30, 0x73, 0x71, 0xcc, 0xcc, 0x75, 0x58, 0xee, 0x7c, 0xee, 0xb9, 0xab, 0x6d, 0x58, 0xcc,
	0xc4, 0x93, 0x57, 0xa4, 0x73, 0x49, 0x63, 0xdb, 0x1b, 0xa7, 0x7a, 0x03, 0x8a, 0x09, 0x75, 0x94,
	0x6a, 0x15, 0x53, 0x2e, 0xab, 0xef, 0x07, 0xfd, 0xcc, 0x33, 0x8e, 0xd7, 0x8d, 0x65, 0x98, 0x26,
	0x0c, 0x9f, 0xf9, 0x44, 0x11, 0x97, 0xcc, 0x6c, 0x57, 0xf5, 0x60, 0xb1, 0xf3, 0x45, 0xab, 0xcd,
	0x65, 0x2a, 0x5e, 0xc9, 0xb4, 0x3b, 0x68, 0xeb, 0xbf, 0x3e, 0x45, 0xf5, 0x97, 0x22, 0x20, 0x19,
	0xad, 0xee, 0x7a, 0xdd, 0xc3, 0xcc, 0x25, 0xaf, 0x23, 0x17, 0xbd, 0x00, 0xfd, 0x5c, 0xf0, 0xc0,
	0x8a, 0x7b, 0x21, 0xc9, 0x66, 0xc1, 0xfd, 0xd1, 0xa6, 0xef, 0x0b, 0x1e, 0x1c, 0xf7, 0x42, 0x62,
	0x96, 0xce, 0xb3, 0x15, 0xfa, 0x1e, 0x66, 0x6d, 0xc5, 0x92, 0x86, 0x7e, 0xf2, 0xfd, 0xa7, 0xa9,
	0x54, 0x30, 0xd8, 0x83, 0x35, 0x5a, 0x84, 0xa9, 0x2e, 0xf6, 0x93, 0xf4, 0xa4, 0x15, 0x33, 0xdd,
	0xf4, 0x25, 0x99, 0x1c, 0x4a, 0xf2, 0x35, 0x2c, 0x7a, 0x38, 0xb2, 0xb0, 0x6d, 0x13, 0x9f, 0x08,
	0x1c, 0x73, 0x61, 0xd9, 0x58, 0x38, 0xab, 0x53, 0x4a, 0x20, 0xe4, 0xe1, 0xa8, 0x36, 0x74, 0xd5,
	0xb1, 0x70, 0xd0, 0x33, 0x30, 0xf2, 0x68, 0x81, 0x63, 0xb2, 0x3a, 0xbd, 0xae, 0x6d, 0x14, 0xcc,
	0xf9, 0x9c, 0xdd, 0xc4, 0x31, 0x41, 0xdf, 0xc2, 0x4a, 0x1e, 0x4a, 0x3e, 0xc6, 0x02, 0x5b, 0x69,
	0x59, 0x33, 0xaa, 0x84, 0xa5, 0x9c, 0xbb, 0x21, 0xbd, 0xa7, 0xaa, 0xcc, 0xc7, 0x50, 0xe6, 0x82,
	0xba, 0x94, 0x65, 0xe0, 0x92, 0x02, 0xcf, 0xa6, 0xb6, 0x14, 0xb2, 0x96, 0xe9, 0xea, 0x90, 0xc8,
	0x5e, 0xd5, 0xd7, 0xb5, 0x0d, 0x3d, 0xd5, 0x6e, 0x8f, 0x44, 0x76, 0xf5, 0xaf, 0x02, 0xe8, 0x07,
	0x3e, 0x17, 0x3d, 0x13, 0xb3, 0x0b, 0xf4, 0x1d, 0xe8, 0x02, 0xb3, 0x8b, 0x7c, 0x0b, 0x1e, 0x8d,
	0xea, 0x38, 0xc0, 0xa7, 0x7d, 0x10, 0xd9, 0xea, 0x86, 0x5b, 0xf4, 0x1f, 0x28, 0x39, 0xb8, 0x67,
	0x49, 0x84, 0x92, 0xa9, 0x62, 0xce, 0x38, 0x38, 0x4d, 0xb5, 0x06, 0xfa, 0x25, 0x21, 0x17, 0xa9,
	0x6f, 0x5a, 0xf9, 0x4a, 0xd2, 0xa0, 0x9c, 0x0f, 0x01, 0x02, 0xce, 0x62, 0x2f, 0xf5, 0xa6, 0x02,
	0xe8, 0xca, 0xd2, 0x8f, 0x55, 0x65, 0x46, 0xd4, 0x65, 0xaa, 0x6b, 0x7a, 0x5a, 0x45, 0x87, 0xba,
	0x6c, 0xe0, 0x64, 0x38, 0x20, 0xaa, 0x7d, 0x99, 0xb3, 0x85, 0x03, 0x82, 0x9e, 0x40, 0x05, 0xdb,
	0x31, 0xed, 0xd2, 0xb8, 0x97, 0x46, 0xa7, 0x7a, 0x94, 0xfb, 0x46, 0xc5, 0x90, 0x07, 0x29, 0x16,
	0xb8, 0x0a, 0x1a, 0x61, 0x52, 0x55, 0xce, 0xaa, 0x2a, 0x07, 0x20, 0x59, 0x68, 0xf5, 0x0d, 0xdc,
	0xcb, 0xde, 0xfc, 0x40, 0xb3, 0xcf, 0x1b, 0x22, 0x3f, 0x6b, 0x83, 0xb9, 0x94, 0x63, 0x1c, 0x6f,
	0x88, 0xd4, 0x60, 0x2e, 0x89, 0x88, 0x50, 0x55, 0x5b, 0x3e, 0x8d, 0xe2, 0xd5, 0xc2, 0x7a, 0x71,
	0x63, 0x76, 0x67, 0xed, 0x96, 0x4e, 0x9b, 0x65, 0x19, 0x22, 0x57, 0x47, 0x34, 0x8a, 0xab, 0x0c,
	0x56, 0x06, 0x2e, 0xf9, 0xaa, 0x18, 0xf1, 0xdb, 0x49, 0xe4, 0xc9, 0x57, 0x9c, 0xd5, 0xac, 0x0d,
	0x2f, 0xc1, 0x17, 0xc8, 0xb7, 0x0f, 0x0f, 0xb2, 0x53, 0x9f, 0xd2, 0xf0, 0x80, 0x9e, 0xc7, 0x6d,
	0x6c, 0x5f, 0x60, 0x97, 0x34, 0xd9, 0x39, 0x1f, 0xe7, 0x93, 0xf8, 0x5b, 0x01, 0x1e, 0xde, 0x42,
	0x34, 0x9e, 0x8e, 0xdb, 0xe9, 0x3c, 0xe8, 0xd2, 0xd0, 0x72, 0xe9, 0x79, 0x6c, 0x85, 0x29, 0x55,
	0x36, 0x9a, 0x17, 0x3c, 0x1c, 0x5d, 0xcd, 0x81, 0xd6, 0xa1, 0x1c, 0x79, 0xfc, 0xd2, 0x12, 0xc4,
	0xb1, 0x1c, 0x1e, 0x67, 0x93, 0x15, 0xa4, 0xcd, 0x24, 0xce, 0x1e, 0x8f, 0xd1, 0x26, 0x2c, 0x10,
	0x16, 0x0b, 0xcc, 0x6c, 0x62, 0x51, 0x9b, 0x33, 0x2b, 0x11, 0x7e, 0x76, 0x87, 0xe7, 0xfb, 0x8e,
	0xa6, 0xcd, 0xd9, 0x89, 0xf0, 0x47, 0xb1, 0x81, 0xf3, 0x5c, 0x3d, 0xb2, 0x6b, 0xd8, 0xd7, 0xce,
	0x73, 0x79, 0x59, 0x07, 0xd8, 0x1f, 0x93, 0x20, 0x54, 0x0f, 0x4e, 0x37, 0xcb, 0x7d, 0xe3, 0xab,
	0x24, 0x08, 0xab, 0x7f, 0x17, 0x60, 0xf1, 0x94, 0x86, 0xb2, 0xda, 0x93, 0xd0, 0xc1, 0x31, 0xe9,
	0xb7, 0x74, 0x0f, 0xf4, 0x30, 0x89, 0xbc, 0xfc, 0x54, 0x78, 0x3a, 0xda, 0xbb, 0x91, 0xd0, 0x74,
	0x3a, 0x84, 0xd9, 0xea, 0xe6, 0xe9, 0xa0, 0x7e, 0xb5, 0x75, 0xb1, 0xaf, 0xb4, 0x98, 0x34, 0x67,
	0xe4, 0xfe, 0x14, 0xfb, 0xf2, 0x11, 0x4b, 0x5d, 0x7d, 0xd2, 0x25, 0x7e, 0x36, 0x83, 0x4b, 0x5d,
	0x1a, 0x1e, 0xc9, 0xfd, 0x27, 0x85, 0x9f, 0xba, 0xab, 0xf0, 0xd3, 0x77, 0x13, 0x7e, 0x66, 0x0c,
	0xe1, 0x4b, 0x77, 0x14, 0x5e, 0x1f, 0x15, 0x7e, 0xf3, 0x3d, 0xc0, 0xf0, 0x97, 0x20, 0x5a, 0x83,
	0x95, 0xa3, 0x37, 0xf5, 0x43, 0xab, 0x73, 0x5c, 0x3b, 0x3e, 0xe9, 0x58, 0x27, 0xad, 0x4e, 0xbb,
	0x51, 0x6f, 0xee, 0x37, 0x1b, 0x7b, 0xc6, 0x04, 0x5a, 0x06, 0x94, 0x77, 0x36, 0x5a, 0xb5, 0xdd,
	0xa3, 0x86, 0xa1, 0xa1, 0x15, 0xb8, 0x97, 0xb7, 0xef, 0x35, 0x3b, 0xca, 0x51, 0xd8, 0xdc, 0x06,
	0x18, 0x7e, 0xf6, 0x50, 0x19, 0x4a, 0x2d, 0x9e, 0xee, 0x8d, 0x09, 0x54, 0x82, 0x49, 0xf9, 0x19,
	0x36, 0x34, 0xa4, 0xc3, 0x94, 0xfa, 0x18, 0x1b, 0x85, 0xcd, 0x3f, 0x35, 0x28, 0xf5, 0xbf, 0xb1,
	0x68, 0x09, 0x16, 0x9a, 0xac, 0x8b, 0x7d, 0xea, 0x0c, 0x49, 0x8c, 0x09, 0xb4, 0x00, 0x95, 0xb6,
	0x20, 0x11, 0x61, 0x71, 0xc6, 0xa5, 0x21, 0x03, 0xca, 0xaf, 0x31, 0x4b, 0xb0, 0x9f, 0x59, 0x0a,
	0xd2, 0x72, 0xc8, 0xa8, 0xeb, 0xf5, 0x31, 0x45, 0xc9, 0xf6, 0x8e, 0x27, 0x87, 0x8c, 0x5f, 0xb2,
	0xb7, 0x5e, 0xbf, 0x8c, 0x49, 0x34, 0x07, 0xf0, 0x96, 0xfb, 0xe7, 0xd9, 0x7e, 0x0a, 0x21, 0x98,
	0x53, 0xbf, 0x0e, 0xb0, 0xc8, 0xb2, 0x1a, 0xd3, 0x68, 0x15, 0x16, 0x77, 0x93, 0xde, 0xb1, 0x20,
	0x38, 0x4a, 0x04, 0x69, 0x0b, 0xda, 0xa5, 0x3e, 0x71, 0x89, 0x31, 0x83, 0xee, 0xc1, 0xfc, 0x6e,
	0xd2, 0x3b, 0xa5, 0x22, 0x4e, 0xb0, 0xdf, 0x0c, 0xb0, 0x4b, 0x8c, 0xd2, 0xe6, 0xaf, 0x1a, 0x54,
	0xae, 0x7c, 0xa5, 0x24, 0x2c, 0x3b, 0x49, 0xdf, 0x64, 0x4c, 0x48, 0x39, 0x64, 0x26, 0x69, 0x31,
	0x34, 0x54, 0x01, 0x5d, 0x89, 0xa0, 0xb6, 0xaa, 0xfe, 0x5a, 0x6e, 0x96, 0x1b, 0x45, 0x79, 0xec,
	0x3a, 0xf1, 0xc9, 0x99, 0xe8, 0x9b, 0x26, 0xd1, 0x22, 0x18, 0x4d, 0x16, 0x13, 0x81, 0xed, 0xf8,
	0x00, 0x07, 0x44, 0x59, 0xa7, 0x64, 0x37, 0xf6, 0x89, 0xef, 0xf3, 0xcb, 0x9a, 0x6d, 0xf3, 0x20,
	0xc4, 0x2c, 0x85, 0x4f, 0x6f, 0xfe, 0xa1, 0xc1, 0xd2, 0x8d, 0xef, 0x04, 0xfd, 0x1f, 0x9e, 0x9e,
	0x36, 0xdb, 0x56, 0xbb, 0x56, 0x3f, 0xb4, 0x1a, 0xad, 0x63, 0xb3, 0xd6, 0xaa, 0x37, 0xac, 0xf6,
	0x49, 0xe7, 0xa5, 0x75, 0xfc, 0xae, 0xdd, 0xb8, 0x76, 0x0b, 0x9e, 0xc0, 0xa3, 0xdb, 0xc0, 0xad,
	0xc6, 0x5b, 0x43, 0x43, 0xff, 0x85, 0xf5, 0xdb, 0x40, 0x66, 0xa3, 0xb6, 0x67, 0x14, 0xd0, 0x53,
	0x78, 0x72, 0x3b, 0xaa, 0xde, 0x68, 0x9e, 0x36, 0x8c, 0xe2, 0xee, 0x3e, 0xac, 0xda, 0x3c, 0xd8,
	0xea, 0xd1, 0x1e, 0x4f, 0xe4, 0xe3, 0x0f, 0xb8, 0x43, 0xfc, 0xf4, 0xef, 0xdd, 0xfb, 0x4d, 0x97,
	0xfb, 0x98, 0xb9, 0x5b, 0xcf, 0x77, 0xe2, 0x78, 0xcb, 0xe6, 0xc1, 0xb6, 0x32, 0xdb, 0xdc, 0xdf,
	0xc6, 0x61, 0x78, 0xf5, 0x2f, 0xe3, 0xd9, 0xb4, 0xf2, 0x7d, 0xf3, 0x4f, 0x00, 0x00, 0x00, 0xff,
	0xff, 0xd5, 0x74, 0x82, 0xd5, 0x58, 0x0e, 0x00, 0x00,
}
