syntax = "proto3";

package ga.contract;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/contract-logic";

enum SceneType {
   SCENE_TYPE_SCENE_INVALID_UNSPECIFIED = 0;  // 无效
   SCENE_TYPE_SCENE_USERCARD_LIVE = 1;  // 直播房用户资料卡
   SCENE_TYPE_SCENE_USERCARD_PGC = 2;  // 公会公开房资料卡
   SCENE_TYPE_SCENE_IM_PAGE = 3;    // im聊天框
}

// 检查用户是否有互动消息查看入口
message CheckUserInteractEntryRequest{
   ga.BaseReq base_req = 1;
   uint32 scene_type = 2;  // see SceneType
   uint32 interact_uid = 3;  // 互动对方uid
   uint32 channel_id = 4;  // 房间id
   uint64 channel_live_id = 5; //直播场次id, 直播房内需要
}
message CheckUserInteractEntryResponse {
   ga.BaseResp base_resp = 1;
   bool is_has_entry = 2;  // 是否有入口
   string entry_text = 3;  // 入口文案  
}


//互动消息
message InteractInfo {
   string interact_type = 1;
   string interact_value = 2; 
}

enum InteractUserType {
   INTERACT_USER_TYPE_INTERACTUSER_INVALID_UNSPECIFIED = 0;   //无效
   INTERACT_USER_TYPE_INTERACTUSER_COMMON = 1;  // 普通互动用户
   INTERACT_USER_TYPE_INTERACTUSER_EXPIRE_FANS = 2;  // 粉团铭牌已变灰用户
   INTERACT_USER_TYPE_INTERACTUSER_LIVE_SEVEN_NEW = 3; // 7天内第一次来本直播间
   INTERACT_USER_TYPE_INTERACTUSER_PGC_SEVEN_NEW = 4; // 7天内第一次来本多人互动房间(pgc)
}

// 互动标识 
message InteractCertInfo {
  ga.ChannelLiveFansInfo fans_info = 1; //语音直播房粉丝信息 已变灰用户需要展示
  string cert_msg = 2;  // 标识内容信息
}

// 获取用户的互动消息
message GetUserInteractInfoRequest {
   ga.BaseReq base_req = 1;
   uint32 scene_type = 2;  // see SceneType
   uint32 interact_uid = 3;  // 互动对方uid
   uint32 channel_id = 4;  // 房间id
   uint64 channel_live_id = 5; //直播场次id, 直播房内需要
}
message GetUserInteractInfoResponse {
   ga.BaseResp base_resp = 1;
   uint32 user_type = 2; // see InteractUserType
   repeated InteractInfo info_list = 3;
   InteractCertInfo cert_info = 4; 
   string account = 5; // 本人account
   string interact_account = 6; // 互动对方account
   string interact_nickname = 7;  // 互动对方昵称
}

// 获取用户互动资料隐私查看权限
message GetUserInteractViewPerRequest {
   ga.BaseReq base_req = 1;
}
message GetUserInteractViewPerResponse {
   ga.BaseResp base_resp = 1;
   bool is_open = 2;  // 是否打开
}

// 设置互动资料隐私查看权限
message SetUserInteractViewPerRequest {
   ga.BaseReq base_req = 1;
   bool is_open = 2;  // 是否打开
}
message SetUserInteractViewPerResponse {
   ga.BaseResp base_resp = 1;
} 


// 获取多人互动成员大厅任务入口
message GetMultiPlayerHallTaskEntryRequest {
   ga.BaseReq base_req = 1;
   uint32 channel_id = 2;
}
message GetMultiPlayerHallTaskEntryResponse{
   ga.BaseResp base_resp = 1;
   string jump_url = 2; // 半屏页跳转url 此字段为空 则不可见
   repeated MultiPlayerHallTask list = 3; // 任务列表
   bool is_done=4; // 是否已完成
}
message MultiPlayerHallTask{
   string task_name = 1; // 任务名称
   string task_progress = 2; // 任务进度  例如 1h20min/2h
   float rate = 3; // 进度比率
}
message MultiPlayerHallTaskListMsg {
   uint32 uid=1;
   string jump_url = 2;
   repeated MultiPlayerHallTask list = 3; // 任务列表
   bool is_done=4; // 是否已完成
}

message ContractClaimObsTokenRequest {
    ga.BaseReq base_req = 1;
    int32 expiration = 2; // optional, seconds,
}

message ContractClaimObsTokenResponse {
    ga.BaseResp base_resp = 1;
    string token = 2;
    int64 expire_at = 3; // token 过期时间戳
}

message GetNewbieAnchorGuideInfoRequest {
    ga.BaseReq base_req = 1;
}

message GetNewbieAnchorGuideInfoResponse {
    ga.BaseResp base_resp = 1;
    bool is_newbie = 2; // 是否新手
    repeated uint32 anchor_identity_list = 3; // 新手身份列表 see SIGN_ANCHOR_IDENTITY
    string pop_txt = 4; // 引导泡泡文本
    uint32 pop_type = 5; // 引导泡泡类型
}