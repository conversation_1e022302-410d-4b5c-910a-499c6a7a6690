syntax = "proto3";

package ga.tt_coin_logic;

import "ga_base.proto";

option go_package = "golang.52tt.com/protocol/app/tt_coin_logic";
option java_package = "com.yiyou.ga.model.proto";

message GetBalanceRequest {
  ga.BaseReq base_req = 1;
}

message GetBalanceResponse {
  ga.BaseResp base_resp = 1;
  // T豆数量
  int64 balance = 2;
}

message CreateInAppOrderRequest {
  ga.BaseReq base_req = 1;
  // 订单金额
  int64 amount = 2;
  // 随机数, 防止重复提交
  int64 rn = 3;
  // 外部订单号
  string out_order_no = 4;
  //同意充值协议?
  bool agreement_checked = 10;
}

message CreateInAppOrderResponse {
  ga.BaseResp base_resp = 1;
  // 针对安卓的返回，需要加密中台返回的字段,按照<老的规则>(有一定保密性质)加密后的json, 解密后可以反序列化成InAppOrder
  string encrypted_proto = 2;
}

message InAppOrder {
  // 订单号
  string order_no = 1;
  // 订单金额
  int64 amount = 2;
  // 支付token
  string token = 3;
  // 支付渠道显示顺序,按照list中参数先后顺序显示. 内容为('ALIPAY', 'WECHAT' ...)
  repeated string channel_sort = 4;
  // actives DEPRECATED: 这个字段已经废弃, USE GetPayActivity接口单独获取
  repeated ActiveConfig actives = 5[deprecated =true];
  //最后支付成功的支付渠道
  string last_pay_success_channel = 6;
}

message GetInAppGoodsListRequest {
  ga.BaseReq base_req = 1;
}

message GetInAppGoodsListResponse {
  ga.BaseResp base_resp = 1;
  // 商品列表
  repeated Goods goods_list = 2;
}

message Goods {
  // id
  int64 id = 1;
  // t豆数量
  string num = 2;
  // 商品金额
  int64 amount = 3;
  // 活动信息
  repeated ActiveConfig actives = 4;
}

message AppGoods {
  // 商品ID
  string product_id = 1;
  // 商品价格
  string price = 2;
  // 商品数量
  int64 num = 3;
  // 活动标识
  repeated ActiveConfig actives = 4;
}

message ActiveConfig {
  // 支付渠道
  string pay_channel = 1;
  // 展示文案
  string paperwork = 2;
  // 活动代码
  string active_code = 3;
  // 角标位置
  string corner_position = 4;
  // 颜色
  string color = 5;
  //扩展参数 需要传递到支付接口
  map<string, string> extra_params = 6;
}

message GetUserAccumulateRechargeRequest {
  ga.BaseReq base_req = 1;
}

message GetUserAccumulateRechargeResponse {
  ga.BaseResp base_resp = 1;
  // 累计充值总金额, 元.
  // 注意: 在原Java代码里面返回的是Integer, 这里用int64的话, protojson序列化会得到string类型, 要留意客户端/Web的兼容性.
  // 如果改为gRPC的话, 则没有这个问题
  int64 total_recharge = 2;
}

message GetAppstoreProductRequest {
  ga.BaseReq base_req = 1;
  // 商品名称前缀
  string bundle_id = 2;
}

message GetAppstoreProductResponse {
  ga.BaseResp base_resp = 1;
  // 商品列表
  repeated AppGoods goods_list = 2;
}

message GetAppAccountTokenRequest {
  ga.BaseReq base_req = 1;
  // coin_code
  string coin_code = 2;
}

message GetAppAccountTokenResponse {
  ga.BaseResp base_resp = 1;
  // 苹果账号token
  string app_account_token = 2;
  // coin_code
  string coin_code = 3;
  // 加密结果
  string encrypt_result = 4;
}

message GetRechargeRecordRequest {
  ga.BaseReq base_req = 1;
}

message GetRechargeRecordResponse {
  ga.BaseResp base_resp = 1;
  // 重定向地址
  string redirect_url = 2;
}

message FaceResultRequest {
  ga.BaseReq base_req = 1;
  // 供应商CODE
  string provider_code = 2;
  // 人脸认证的certifyId
  string certify_id = 3;
  // 人脸认证场景ID 例如：充值场景=1-1
  string scene = 4;
  // 人脸认证报告数据
  FaceAuthReportData report_data = 5;
  // 特定供应商校验人脸需要上传sdk数据。例如：旷世人脸认证
  string provider_result_data = 6;
}

message FaceAuthReportData {
  // 客户端类型
  int32 client_type = 1;
  // 客户端版本
  int32 client_version = 2;
  // 设备ID
  string device_id = 3;
}

message FaceResultResponse {
  ga.BaseResp base_resp = 1;
  // 人脸认证结果。true=通过，false=不通过
  bool is_pass = 2;
  // 人脸认证的certifyId
  string certify_id = 3;
  // 请求ID
  string request_id = 4;
  // 供应商响应结果
  string provider_response_result = 5;
  // 供应商响应描述
  string provider_response_description = 6;
}

message CreateInIosAppOrderRequest {
  ga.BaseReq base_req = 1;
  // 订单金额
  int64 amount = 2;
  // 随机数, 防止重复提交
  int64 rn = 3;
  // AppStore产品id(ios)
  string product_id = 4;
  // AppStore交易凭证（ios）
  string recipt_data = 5;
  // 地区
  string locale = 6;
  //同意充值协议?
  bool agreement_checked = 10;
}

message CreateInIosAppOrderResponse {
  ga.BaseResp base_resp = 1;
  // 针对充值的返回，需要加密中台返回的字段,按照<老的规则>(有一定保密性质)加密后的json, 解密后可以反序列化成InAppOrder
  string encrypted_proto = 2;
}

message GetIOSVoldemortedVersionRequest {
  ga.BaseReq base_req = 1;
  // app_name 非必传
  string app_name = 2;
}

message GetIOSVoldemortedVersionResponse {
  ga.BaseResp base_resp = 1;

  repeated VoldemortedVersion voldemorted_version = 2;
}

message VoldemortedVersion {
  string app_name = 1;

  string version = 2;
}

message GetFirstRechargeRequest {
  ga.BaseReq base_req = 1;
  // 最低金额
  string first_min_price = 2;
}

message GetFirstRechargeResponse {
  ga.BaseResp base_resp = 1;

  bool first_recharge = 2;
}

message ProveBlackUserRequest {
  ga.BaseReq base_req = 1;
  string type = 2;
}

message ProveBlackUserResponse {
  ga.BaseResp base_resp = 1;

  bool is_black_user = 2;
}

message QueryBlackUserRequest {
  ga.BaseReq base_req = 1;

  repeated int32 type_list = 2;
}

message QueryBlackUserResponse {
  ga.BaseResp base_resp = 1;

  map<uint32, bool> scene_map = 2;
}

message ConsumePrepareRequest {
  ga.BaseReq base_req = 1;

  string app_id = 2;
  //买家名称
  string buyer_name = 3;
  //商品id
  string commodity_id = 4;
  //商品名称
  string commodity_name = 5;
  //num
  uint32 num = 6;
  //单价，单位T豆
  uint32 unit_price = 7;
  //价格
  uint32 price = 8;
  //platform
  string platform = 9;
  string out_trade_no = 10;
  string notes = 11;
  //外部系统上报的订单时间, 格式:yyyy-MM-dd HH:mm:ss
  string out_order_time = 12;
  //礼物系统 的 礼物id
  string item_id = 13;
}

message ConsumePrepareResponse {
  ga.BaseResp base_resp = 1;
  string app_id = 2;
  string out_trade_no = 3;
  string trade_no = 4;
  int32 uid = 5;
  string buyer_name = 6;
  string commodity_id = 7;
  string commodity_name = 8;
  uint32 num = 9;
  uint32 unit_price = 10;
  uint32 price = 11;
  string create_time = 12;
  string update_time = 13;
  uint32 balance = 14;
  int32 platform = 15;
  string deal_token = 16;
}

message ConsumeTBeanRequest {
  ga.BaseReq base_req = 1;

  string app_id = 2;
  //买家名称
  string buyer_name = 3;
  //商品id
  string commodity_id = 4;
  //商品名称
  string commodity_name = 5;
  //num
  uint32 num = 6;
  //单价，单位T豆
  uint32 unit_price = 7;
  //价格
  uint32 price = 8;
  //platform
  string platform = 9;
  string out_trade_no = 10;
  string notes = 11;
  //外部系统上报的订单时间, 格式:yyyy-MM-dd HH:mm:ss
  string out_order_time = 12;
  //礼物系统 的 礼物id
  string item_id = 13;
}

message ConsumeTBeanResponse {
  ga.BaseResp base_resp = 1;
  string app_id = 2;
  string out_trade_no = 3;
  string trade_no = 4;
  int32 uid = 5;
  string buyer_name = 6;
  string commodity_id = 7;
  string commodity_name = 8;
  uint32 num = 9;
  uint32 unit_price = 10;
  uint32 price = 11;
  string create_time = 12;
  string update_time = 13;
  uint32 balance = 14;
  int32 platform = 15;
  string deal_token = 16;
}

message ConsumeNotifyRequest {
  ga.BaseReq base_req = 1;

  string app_id = 2;
  string out_trade_no = 3;
  string trade_no = 4;
  string command = 5;
}

message ConsumeNotifyResponse {
  ga.BaseResp base_resp = 1;
  string app_id = 2;
  string out_trade_no = 3;
  string trade_no = 4;
  string status = 5;
  string update_time = 6;
}

message GetTransferBalanceRequest {
  ga.BaseReq base_req = 1;
  string app_id = 2;
  string out_trade_no = 3;
}

message GetTransferBalanceResponse {
  ga.BaseResp base_resp = 1;
    //收入账户UID
    int32 cr_uid = 2;
    //收入金额
    int32 cr_amount = 3;
    //收入账户余额
    int32 cr_balance = 4;
    //发出账户的UID
    int32 dr_uid = 5;
}

message GetLastChargeChannelRequest{
  ga.BaseReq base_req = 1;
  //uid
  uint64 uid = 2;
}

message GetLastChargeChannelResponse{
  ga.BaseResp base_resp = 1;
  //uid
  uint64 uid = 2;
  //支付渠道
  string pay_channel = 3;
  // 订单号
  string order_no = 4;
  // 订单金额
  int64 amount = 5;
}

message AndroidHalfOrderPayRequest {
  ga.BaseReq base_req = 1;
  // required 订单号+token的密文
  string token = 2;
  // required 支付渠道
  string pay_channel = 3;
  //扩展参数 来源于GetPayActivity返回的
  map<string, string> extra_params = 4;
}

message AndroidHalfOrderPayResponse {
  ga.BaseResp base_resp = 1;
  // 支付渠道
  string method = 2;
  // 支付参数
  PayParam pay_param = 3;

  message PayParam {
    // 签名
    string signature = 1;
    // 支付参数
    string content = 2;
  }
}

message GetPayActivityRequest{
  //base_req
  ga.BaseReq base_req = 1;
  //amount 支付金额
  string amount = 2;
}

message GetPayActivityResponse{
  ga.BaseResp base_resp = 1;
  // 营销活动列表
  repeated ActiveConfig activity_list = 2;
}