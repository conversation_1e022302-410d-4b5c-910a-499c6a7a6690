package datahouse

import (
	"time"

	"golang.52tt.com/pkg/foundation/utils"
)

// DeviceLastLoginInfo 设备历史登陆用户信息
type DeviceLastLoginInfo struct {
	DeviceId      string
	Uid           uint32
	IP            string
	Platform      string
	Province      string
	City          string
	Version       string
	LastLoginDate time.Time
}

// BanLog 设备相关封禁日志
type BanLog struct {
	DeviceId   string
	OpType     uint32
	At         time.Time
	BanDays    string
	RecoveryAt time.Time
	BanUID     uint32
	UnbanUID   uint32
}

const (
	OptionUnknown = 0 // 未知操作
	OptionBan     = 1 // 封禁
	OptionUnban   = 2 // 解封
)

var banOpName2OpStatus = map[string]uint32{
	"封禁": OptionBan,
	"解封": OptionUnban,
}

// ChangeEnvToDHEnv 转换环境参数为数仓参数
func ChangeEnvToDHEnv(env string) string {
	switch env {
	case "test":
		return "test"
	case "testing":
		return "test"
	case "prod":
		return "prod"
	case "dev":
		return "dev"
	default:
		return "test"
	}
}

// =====================================
// 2. 主播中心/会长服务号/娱乐厅从业者中心
type GuildMemberInfo struct {
	DataDate      string  `json:"data_date"`  // 20221123
	DataMonth     string  `json:"data_month"` // 复用 202211
	GuildId       uint32  `json:"guild_id"`
	UserId        uint32  `json:"user_id"`
	LoginDays     uint32  `json:"login_days"`
	LoginDuration uint32  `json:"login_duration"` // 秒
	AddFollowCnt  uint32  `json:"add_follow_cnt"`
	LostFollowCnt uint32  `json:"lost_follow_cnt"`
	AddFriendCnt  uint32  `json:"add_friend_cnt"`
	LostFriendCnt uint32  `json:"lost_friend_cnt"`
	FollowAccum   uint32  `json:"follow_accum"`        // 月累计关注
	PkgGiftRatio  float64 `json:"backpack_gift_ratio"` //背包流水占比
	AnchorIncome  uint64  `json:"anchor_income"`       //当月收礼值 T豆
	IsNewSign     string  `json:"is_new_sign"`         //本月新增从业者
	IsVaild       string  `json:"is_vaild"`            //有效从业者
	IsPro         string  `json:"is_pro"`              //专业从业者
	EffectMicDays string  `json:"effect_mic_days"`     // 有效接档天数
	MicDuration   string  `json:"mic_duration"`        // 接档时长
	AbilityDim    string  `json:"ability_dim"`         // 能力维度
	AnchorFee     string  `json:"anchor_fee"`          // 收礼金额
}

// 按签约主播，签约成员汇总
type MemberDailyInfo struct {
	Date          string `json:"data_date"` //yyyyMMdd
	UserId        uint32 `json:"user_id"`
	LoginDays     uint32 `json:"login_days"`
	LoginDuration uint32 `json:"login_duration"` // 秒
	AddFollowCnt  uint32 `json:"add_follow_cnt"`
	LostFollowCnt uint32 `json:"lost_follow_cnt"`
	AddFriendCnt  uint32 `json:"add_friend_cnt"`
	LostFriendCnt uint32 `json:"lost_friend_cnt"`
}

type GuildMemberChainInfo struct {
	DataDate      string `json:"data_date"` // 20221101
	UserId        uint32 `json:"user_id"`
	ChainCnt      uint32 `json:"chain_cnt"`       // 新增关系链数量
	VaildChainCnt uint32 `json:"vaild_chain_cnt"` // 新增有效关系链数量
}

// 成员、主播关系链日维度数据汇总 - 按签约主播，签约成员维度汇总
type MemberChainDailyInfo struct {
	Date          string `json:"data_date"`
	UserId        uint32 `json:"user_id"`
	ChainCnt      uint32 `json:"chain_cnt"`       // 新增关系链数量
	VaildChainCnt uint32 `json:"vaild_chain_cnt"` // 新增有效关系链数量
}

// 成员、主播关系链月维度数据汇总 - 公会+签约主播，签约成员维度汇总
type GuildMemberChainMonthInfo struct {
	Date          string `json:"data_month"`
	GuildId       uint32 `json:"guild_id"`
	UserId        uint32 `json:"user_id"`
	ChainCnt      uint32 `json:"chain_cnt"`  // 新增关系链数量
	VaildChainCnt uint32 `json:"chain_pair"` // 新增有效关系链数量
}

func (t *GuildMemberChainMonthInfo) GetChainCnt() uint32 {
	if t == nil {
		return 0
	}
	return t.ChainCnt
}

func (t *GuildMemberChainMonthInfo) GetVaildChainCnt() uint32 {
	if t == nil {
		return 0
	}
	return t.VaildChainCnt
}

func (t *GuildMemberInfo) String() string {
	return utils.ToJson(t)
}

func (t *GuildMemberChainInfo) String() string {
	return utils.ToJson(t)
}

func (t *GuildMemberInfo) GetLoginDays() uint32 {
	if t == nil {
		return 0
	}
	return t.LoginDays
}

func (t *GuildMemberInfo) GetFollowAccum() uint32 {
	if t == nil {
		return 0
	}
	return t.FollowAccum
}

func (t *GuildMemberInfo) GetAddFollowCnt() uint32 {
	if t == nil {
		return 0
	}
	return t.AddFollowCnt
}

func (t *GuildMemberInfo) GetLostFollowCnt() uint32 {
	if t == nil {
		return 0
	}
	return t.LostFollowCnt
}

func (t *GuildMemberInfo) GetLostFriendCnt() uint32 {
	if t == nil {
		return 0
	}
	return t.LostFriendCnt
}

func (t *GuildMemberChainInfo) GetChainCnt() uint32 {
	if t == nil {
		return 0
	}
	return t.ChainCnt
}

func (t *GuildMemberChainInfo) GetVaildChainCnt() uint32 {
	if t == nil {
		return 0
	}
	return t.VaildChainCnt
}

type GuildViolationRecord struct {
	Uid                uint32 `json:"user_id"`
	OptTime            string `json:"opt_time"` // 处罚时间
	GuildId            uint32 `json:"guild_id"`
	SanctionActionDesc string `json:"sanction_action_desc"` // 处罚方式
	SourceType         string `json:"source_type"`          // 违规内容形式
	ViolationReason    string `json:"violation_reason"`     // 违规原因
	PubViolationReason string `json:"pub_violation_reason"` // 违规原因
	DataDate           string `json:"data_date"`
	LabelLevelKey      string `json:"label_level_key"` //违规等级
	LabelId            string `json:"label_id"`        //违规标签
}

type Pagination struct {
	Total     uint32 `json:"total"`
	TotalPage uint32 `json:"totalPage"`
	PageSize  uint32 `json:"pageSize"`
	Page      uint32 `json:"page"`
}

func (r GuildViolationRecord) String() string {
	return utils.ToJson(r)
}

type GuildChannelInfo struct {
	GuildId                             uint32 `json:"guild_id"`
	RoomId                              uint32 `json:"room_id"`
	DataMonth                           string `json:"data_month"`
	ProAnchor                           uint32 `json:"pro_anchor"`
	RoomFlow                            string `json:"room_flow"`                                // 房间流水
	EffectMicMemberCnt                  string `json:"effect_mic_member_cnt"`                    // 有效接档成员数
	EffectSignedMemberCount             string `json:"effect_signed_member_count"`               // 有效签约成员数
	ActiveMemberCount                   string `json:"active_member_count"`                      // 活跃从业者数
	HighQualityMemberCount              string `json:"high_quality_member_count"`                // 优质从业者数
	ActiveMemberIncome                  string `json:"active_member_income"`                     // 活跃从业者贡献流水
	HighQualityMemberIncome             string `json:"high_quality_member_income"`               // 优质从业者贡献流水
	HighQualityMemberCountRatio         string `json:"high_quality_member_count_ratio"`          // 优质从业者数量占比
	HighQualityMemberIncomeRatio        string `json:"high_quality_member_income_ratio"`         // 优质从业者贡献流水占比
	RoomHighQualityMemberConversionRate string `json:"room_high_quality_member_conversion_rate"` // 房间优质从业者转换率
	RoomBackpackFlowRatio               string `json:"room_backpack_flow_ratio"`                 // 房间背包流水占比
}

type GuildMonthInfo struct {
	DataMonth               string  `json:"data_month"`
	GuildId                 uint32  `json:"guild_id"`
	ProAnchor               uint32  `json:"pro_anchor"`
	ProAnchorRemainCnt      uint32  `json:"pro_anchor_remain_cnt"`
	ProAnchorRemainRatio    float64 `json:"pro_anchor_remain_ratio"`
	NewAnchorCnt            uint32  `json:"new_anchor_cnt"`
	NewProAnchorCnt         uint32  `json:"new_pro_anchor_cnt"`
	NewProAnchorRemainCnt   uint32  `json:"new_pro_anchor_remain_cnt"`
	NewProAnchorRemainRatio float64 `json:"new_pro_anchor_remain_ratio"`
	GuildMonthlyFlow        string  `json:"guild_monthly_flow"`        // 公会月流水
	SignedMemberCount       string  `json:"signed_member_count"`       // 签约成员数
	ActivePractCount        string  `json:"active_pract_count"`        // 活跃从业者数
	HighQualityPractCount   string  `json:"high_quality_pract_count"`  // 优质从业者数量
	ValidSignedMemberCount  string  `json:"valid_signed_member_count"` // 有效签约成员
	NewValidSignedCount     string  `json:"new_valid_signed_count"`    // 新增有效签约成员
	ActivePractFlow         string  `json:"active_pract_flow"`         // 活跃从业者贡献流水
	HighQualityPractFlow    string  `json:"high_quality_pract_flow"`   // 优质从业者贡献流水
	HighQualityPractRatio   string  `json:"high_quality_pract_ratio"`  // 优质从业者数量占比
	HighQualityFlowRatio    string  `json:"high_quality_flow_ratio"`   // 优质从业者贡献流水占比
	BackpackFlowRatio       string  `json:"backpack_flow_ratio"`       // 背包流水占比
}

// MultiMemberMicStatReq 多人互动 接档数据
type MultiMemberMicStatReq struct {
	Offset        uint32   `json:"offset"`
	Limit         uint32   `json:"limit"`
	StartTime     string   `json:"start_time"`
	EndTime       string   `json:"end_time"`
	HourList      []string `json:"hour_list"`
	ChannelIdList []uint32 `json:"channel_id_list"`
	UidList       []uint32 `json:"uid_list"`
	TimeDim       string   `json:"time_dim"`

	// conv to real param
	//Page      uint32 `json:"page"`
	//PageSize  uint32 `json:"pageSize"`
	//StartDate string `json:"start_date"` // 数据日期起，yyyy-MM-dd
	//EndDate   string `json:"end_date"`   // 数据日期止，yyyy-MM-dd
	//DataHour  string `json:"data_hour"`  // 所属时间段，格式HH
	//RoomId    string `json:"room_id"`    // 房间id，查多个房间用逗号分隔
	//UserId    string `json:"user_id"`    // 查多个uid用逗号分隔 如需查所有用户，则传入'all'
}

// MultiMemberMicStatData 多人互动 接档数据
type MultiMemberMicStatData struct {
	RoomID            uint32 `json:"room_id"`             // 房间ID
	MicDurationOther  string `json:"mic_duration_other"`  // 非0麦接档时长（分钟）
	UserID            uint32 `json:"user_id"`             // 用户ID
	AnchorIncome100Up string `json:"anchor_income_100up"` // 收到百元礼物总值（元）
	DataHour          string `json:"data_hour"`           // 数据所属小时，格式HH
	GuildID           uint32 `json:"guild_id"`            // 公会ID
	MicDuration0      string `json:"mic_duration_0"`      // 0麦接档时长（分钟）
	DataDate          string `json:"data_date"`           // 数据日期
	AnchorIncome      string `json:"anchor_income"`       // 接档期间收礼（元）
	UpdateTime        string `json:"update_time"`         // 更新时间
	IsEmployment      string `json:"is_employment"`       // 是否为任职成员
}

type MultiGuildMemberInfoReq struct {
	StartDate     time.Time // 数据日期起，yyyyMMdd
	EndDate       time.Time // 数据日期止，yyyyMMdd
	UidList       []uint32
	ChannelIdList []uint32
	Offset        uint32
	Limit         uint32
}

// MultiGuildMemberDayInfo 多人互动从业者房间维度日累计数据
type MultiGuildMemberDayInfo struct {
	DataDate     string `json:"data_date"` // 20221123
	UserId       uint32 `json:"user_id"`
	RoomId       string `json:"room_id"`
	AnchorIncome uint64 `json:"anchor_income"` //当月收礼值 T豆
	IsNewSign    string `json:"is_new_sign"`   //本月新增从业者
	IsVaild      string `json:"is_vaild"`      //有效从业者
	IsPro        string `json:"is_pro"`        //专业从业者
	MicDuration  string `json:"mic_duration"`  // 房间接档时长
	WolfIncome   string `json:"wolf_income"`   //房间狼人杀金额 T豆
}

// MultiGuildMemberMonthInfo 多人互动从业者房间维度月累计数据
type MultiGuildMemberMonthInfo struct {
	DataMonth     string `json:"data_month"` // 202211
	Uid           uint32 `json:"uid"`
	RoomId        string `json:"room_id"`
	LoginDays5h   uint32 `json:"login_days_5h"`   // 5h活跃天数
	LoginDays7h   uint32 `json:"login_days_7h"`   // 7h活跃天数
	AnchorIncome  uint64 `json:"anchor_income"`   //当月收礼值 T豆
	IsNewSign     string `json:"is_new_sign"`     //本月新增从业者
	IsVaild       string `json:"is_vaild"`        //有效从业者
	IsPro         string `json:"is_pro"`          //专业从业者
	MicDuration   string `json:"mic_duration"`    // 房间接档时长
	EffectMicDays string `json:"effect_mic_days"` // 房间有效接档天数
	WolfIncome    string `json:"wolf_income"`     //房间狼人杀金额 T豆
}

type FaceAuthFailedDailyReq struct {
	StartDate    time.Time // 数据日期起，yyyy-MM-dd
	EndDate      time.Time // 数据日期止，yyyy-MM-dd
	Page         uint32    // 当前页
	PageSize     uint32    // 每页数量
	GuildIdList  []uint32  // 公会ID
	UidList      []uint32  // 用户ID
	IdentityType uint32    // 用户签约身份,听听:1/多人:0
}

// FaceAuthFailedDailyResp 主播人脸认证日明细数据返回结构
type FaceAuthFailedDailyResp struct {
	Pagination Pagination             `json:"pagination"` // 分页参数
	Data       []*FaceAuthFailedDaily `json:"data"`       // 响应数据，如果没有则为空数组
}

// FaceAuthFailedDaily 主播人脸认证日明细数据
type FaceAuthFailedDaily struct {
	RoomID            int    `json:"room_id"`             // 房间ID
	UserID            int    `json:"user_id"`             // 用户ID
	RoomGiftAmt       string `json:"room_gift_amt"`       // 用户在房间收礼金额/元
	GuildGiftAmt      string `json:"guild_gift_amt"`      // 公会收礼金额,属于同个公会的所有房间的收礼金额之和/元
	GuildID           int    `json:"guild_id"`            // 公会ID
	LessCommissionAmt string `json:"less_commission_amt"` // 预计扣除佣金礼物金额/元
	DataDate          string `json:"data_date"`           // 数据日期，yyyy-MM-dd
	RuleType          string `json:"rule_type"`           // 从业者标记,新签约,其他
	IdentityType      string `json:"identity_type"`       // 用户签约身份,听听/多人
}

type FaceAuthFailedWeeklyReq struct {
	StartDate    time.Time // 数据日期起，yyyy-MM-dd
	EndDate      time.Time // 数据日期止，yyyy-MM-dd
	Page         uint32    // 当前页
	PageSize     uint32    // 每页数量
	GuildIdList  []uint32  // 公会ID
	UidList      []uint32  // 用户ID
	IdentityType uint32    // 用户签约身份,听听:1/多人:0
}

// FaceAuthFailedWeeklyResp 主播人脸认证周明细数据返回结构
type FaceAuthFailedWeeklyResp struct {
	Pagination Pagination              `json:"pagination"` // 分页参数
	Data       []*FaceAuthFailedWeekly `json:"data"`       // 响应数据，如果没有则为空数组
}

// FaceAuthFailedWeekly 主播人脸认证周明细数据
type FaceAuthFailedWeekly struct {
	RoomID            int    `json:"room_id"`             // 房间ID
	UserID            int    `json:"user_id"`             // 用户ID
	RoomGiftAmt       string `json:"room_gift_amt"`       // 用户在房间收礼金额/元
	GuildGiftAmt      string `json:"guild_gift_amt"`      // 公会收礼金额,属于同个公会的所有房间的收礼金额之和/元
	GuildID           int    `json:"guild_id"`            // 公会ID
	LessCommissionAmt string `json:"less_commission_amt"` // 预计扣除佣金礼物金额/元
	DataDate          string `json:"data_date"`           // 数据日期，yyyy-MM-dd
	RuleType          string `json:"rule_type"`           // 从业者标记,新签约,其他
	IdentityType      string `json:"identity_type"`       // 用户签约身份,听听/多人
}

type GuildFaceAuthDailySumReq struct {
	StartDate    time.Time // 数据日期起，yyyy-MM-dd
	EndDate      time.Time // 数据日期止，yyyy-MM-dd
	Page         uint32    // 当前页
	PageSize     uint32    // 每页数量
	GuildIdList  []uint32  // 公会ID
	IdentityType uint32    // 用户签约身份,听听:1/多人:0
}

// GuildFaceAuthDailySumResp 公会人脸认证日汇总明细数据返回结构
type GuildFaceAuthDailySumResp struct {
	Pagination Pagination               `json:"pagination"` // 分页参数
	Data       []*GuildFaceAuthDailySum `json:"data"`       // 响应数据，如果没有则为空数组
}

// GuildFaceAuthDailySum 公会人脸认证日汇总明细数据
type GuildFaceAuthDailySum struct {
	FaceUserCnt            string `json:"face_user_cnt"`
	FaceRatio              string `json:"face_ratio"`
	RuleType               string `json:"rule_type"`
	HimselfRatio           string `json:"himself_ratio"`
	FaceHimselfRatio       string `json:"face_himself_ratio"`
	GuildID                uint32 `json:"guild_id"`
	ActionUserCnt          string `json:"action_user_cnt"`
	LessCommissionAmt      string `json:"less_commission_amt"`
	DataDate               string `json:"data_date"`
	IdentityType           string `json:"identity_type"`
	NotHimselfGuildGiftAmt string `json:"not_himself_guild_gift_amt"`
	NotHimselfUserCnt      string `json:"not_himself_user_cnt"`
}

type GuildFaceAuthWeeklySumReq struct {
	StartDate    time.Time // 数据日期起，yyyy-MM-dd
	EndDate      time.Time // 数据日期止，yyyy-MM-dd
	Page         uint32    // 当前页
	PageSize     uint32    // 每页数量
	GuildIdList  []uint32  // 公会ID
	IdentityType uint32    // 用户签约身份,听听:1/多人:0
}

// GuildFaceAuthWeeklySumResp 公会人脸认证周汇总明细数据返回结构
type GuildFaceAuthWeeklySumResp struct {
	Pagination Pagination                `json:"pagination"` // 分页参数
	Data       []*GuildFaceAuthWeeklySum `json:"data"`       // 响应数据，如果没有则为空数组
}

// GuildFaceAuthWeeklySum 公会人脸认证周汇总明细数据
type GuildFaceAuthWeeklySum struct {
	FaceUserCnt            string `json:"face_user_cnt"`
	FaceRatio              string `json:"face_ratio"`
	RuleType               string `json:"rule_type"`
	HimselfRatio           string `json:"himself_ratio"`
	FaceHimselfRatio       string `json:"face_himself_ratio"`
	GuildID                int    `json:"guild_id"`
	ActionUserCnt          string `json:"action_user_cnt"`
	LessCommissionAmt      string `json:"less_commission_amt"`
	DataDate               string `json:"data_date"`
	IdentityType           string `json:"identity_type"`
	NotHimselfGuildGiftAmt string `json:"not_himself_guild_gift_amt"`
	NotHimselfUserCnt      string `json:"not_himself_user_cnt"`
}

type GuildFaceAuthDailyPushReq struct {
	StartDate   time.Time // 数据日期起，yyyy-MM-dd
	EndDate     time.Time // 数据日期止，yyyy-MM-dd
	Page        uint32    // 当前页
	PageSize    uint32    // 每页数量
	GuildIdList []uint32  // 公会ID
}

// GuildFaceAuthDailyPushResp 公会人脸认证日推送明细数据返回结构
type GuildFaceAuthDailyPushResp struct {
	Pagination Pagination                `json:"pagination"` // 分页参数
	Data       []*GuildFaceAuthDailyPush `json:"data"`       // 响应数据，如果没有则为空数组
}

// GuildFaceAuthDailyPush 公会人脸认证日推送明细数据
type GuildFaceAuthDailyPush struct {
	GuildID                uint32 `json:"guild_id"`
	LessCommissionAmt      string `json:"less_commission_amt"`
	DataDate               string `json:"data_date"`
	NotHimselfGuildGiftAmt string `json:"not_himself_guild_gift_amt"`
	NotHimselfUserCnt      string `json:"not_himself_user_cnt"`
}

type GuildFaceAuthWeeklyPushReq struct {
	StartDate   time.Time // 数据日期起，yyyy-MM-dd
	EndDate     time.Time // 数据日期止，yyyy-MM-dd
	Page        uint32    // 当前页
	PageSize    uint32    // 每页数量
	GuildIdList []uint32  // 公会ID
}

// GuildFaceAuthWeeklyPushResp 公会人脸认证周推送明细数据返回结构
type GuildFaceAuthWeeklyPushResp struct {
	Pagination Pagination                 `json:"pagination"` // 分页参数
	Data       []*GuildFaceAuthWeeklyPush `json:"data"`       // 响应数据，如果没有则为空数组
}

// GuildFaceAuthWeeklyPush 公会人脸认证周推送明细数据
type GuildFaceAuthWeeklyPush struct {
	GuildID                uint32 `json:"guild_id"`
	LessCommissionAmt      string `json:"less_commission_amt"`
	DataDate               string `json:"data_date"`
	NotHimselfGuildGiftAmt string `json:"not_himself_guild_gift_amt"`
	NotHimselfUserCnt      string `json:"not_himself_user_cnt"`
}

// queryOpdataLiveGuildStats 经营者数据平台-语音直播-公会月数据
type OpdataLiveGuildStats struct {
	DataDate               string `json:"data_date"`
	GuildId                uint32 `json:"guild_id"`
	PureNewAnchorIncome    string `json:"pure_new_anchor_income"`     //纯新达人流水（T豆）
	PureNewAnchorCnt       string `json:"pure_new_anchor_cnt"`        //纯新达人数（人）
	PureNewActiveAnchorCnt string `json:"pure_new_active_anchor_cnt"` //纯新活跃达人数（人）
	PureNewProAnchorCnt    string `json:"pure_new_pro_anchor_cnt"`    //纯新专业从业者数（人）
}
type OpdataLiveGuildStatsReq struct {
	StartDate time.Time // 数据日期起，yyyy-MM-dd
	EndDate   time.Time // 数据日期止，yyyy-MM-dd
	GuildId   uint32    // 公会ID
	Page      uint32    // 当前页
	PageSize  uint32    // 每页数量
}
type OpdataLiveGuildStatsResp struct {
	Pagination Pagination              `json:"pagination"` // 分页参数
	Data       []*OpdataLiveGuildStats `json:"data"`       // 响应数据，如果没有则为空数组
}

// queryOpdataLiveAnchorStats 经营者数据平台-语音直播-主播月数据
type OpdataLiveAnchorStats struct {
	DataDate              string `json:"data_date"`
	GuildId               uint32 `json:"guild_id"`
	UserId                uint32 `json:"user_id"`
	IsPureNewAnchor       string `json:"is_pure_new_anchor"`        //是否纯新达人
	IsPureNewActiveAnchor string `json:"is_pure_new_active_anchor"` //是否纯新活跃达人
	IsPureNewProAnchor    string `json:"is_pure_new_pro_anchor"`    //是否纯新专业从业者
	IsMatureAnchor        string `json:"is_mature_anchor"`          //是否成熟达人
	IsPotActiveAnchor     string `json:"is_pot_active_anchor"`      //是否潜力活跃达人
}
type OpdataLiveAnchorStatsReq struct {
	StartDate   time.Time // 数据日期起，yyyy-MM-dd
	EndDate     time.Time // 数据日期止，yyyy-MM-dd
	GuildIdList []uint32  // 公会ID
	UserIdList  []uint32  // 用户ID
	Page        uint32    // 当前页
	PageSize    uint32    // 每页数量
}
type OpdataLiveAnchorStatsResp struct {
	Pagination Pagination               `json:"pagination"` // 分页参数
	Data       []*OpdataLiveAnchorStats `json:"data"`       // 响应数据，如果没有则为空数组
}

// queryOpdataEsportCoachStats 电竞大神数据
type OpdataEsportCoachStats struct {
	DataDate                 string `json:"data_date"`
	GuildId                  uint32 `json:"guild_id"`
	UserId                   uint32 `json:"user_id"`
	TimeDim                  string `json:"time_dim"`
	CurrentLevel             string `json:"current_level"`               //当前等级
	OnlineDurationMin        string `json:"online_duration_min"`         //在线时长(分钟)
	QuickAcceptDurationMin   string `json:"quick_accept_duration_min"`   //秒接单时长（分钟）
	TotalOrderAmount         string `json:"total_order_amount"`          //完成订单总额(元)
	TotalOrderGames          string `json:"total_order_games"`           //完成订单局数
	TotalOrderUsers          string `json:"total_order_users"`           //完成订单用户数
	OrderShareAmount         string `json:"order_share_amount"`          //当前分成奖励
	ExpertVisitorCnt         string `json:"expert_visitor_cnt"`          //大神访客数
	VisitorInteractionCnt    string `json:"visitor_interaction_cnt"`     //访客互动数
	VisitorInteractionRate   string `json:"visitor_interaction_rate"`    //访客互动率
	AvgResponseTimeSec       string `json:"avg_response_time_sec"`       //平均回复时长（秒）
	ValidOrderDays           string `json:"valid_order_days"`            //有效接单天数
	NewUserVisitorCnt        string `json:"new_user_visitor_cnt"`        //新用户访客数
	NewUserInteractionCnt    string `json:"new_user_interaction_cnt"`    //新用户访客互动数
	NewUserInteractionRate   string `json:"new_user_interaction_rate"`   //新用户访客互动率
	NewUserOrderUsers        string `json:"new_user_order_users"`        //完单新用户数
	NewUserRoomEntryCnt      string `json:"new_user_room_entry_cnt"`     //新用户进房数
	NewUserConversionCnt     string `json:"new_user_conversion_cnt"`     //新用户进房打赏数
	NewUserConversionRevenue string `json:"new_user_conversion_revenue"` //新用户进房打赏流水（元）
	ExpoVisitorCnt           string `json:"expo_visitor_cnt"`            //曝光数
	AllUserEnterCount        string `json:"all_user_enter_count"`        //完单进房用户数
	AllUserRewardCount       string `json:"all_user_reward_count"`       //完单进房打赏用户数
	AllUserRewardAmount      string `json:"all_user_reward_amount"`      //完单用户进房打赏流水（元）
}
type OpdataEsportCoachStatsReq struct {
	StartDate   time.Time // 数据日期起，yyyy-MM-dd
	EndDate     time.Time // 数据日期止，yyyy-MM-dd
	TimeDim     string    // 时间维度
	GuildIdList []uint32  // 公会ID
	UserIdList  []uint32  // 用户ID
	Page        uint32    // 当前页
	PageSize    uint32    // 每页数量
}
type OpdataEsportCoachStatsResp struct {
	Pagination Pagination                `json:"pagination"` // 分页参数
	Data       []*OpdataEsportCoachStats `json:"data"`       // 响应数据，如果没有则为空数组
}

// queryOpdataEsportGuildStats 电竞公会整体经营数据
type OpdataEsportGuildStats struct {
	DataDate                   string `json:"data_date"`
	GuildId                    uint32 `json:"guild_id"`
	TimeDim                    string `json:"time_dim"`
	ActiveExpertCount          string `json:"active_expert_count"`            //有效活跃大神数
	NewExpertCount             string `json:"new_expert_count"`               //新增大神数
	CompletedExpertCount       string `json:"completed_expert_count"`         //完单大神数
	TotalOrderAmount           string `json:"total_order_amount"`             //完成订单总额(元)
	TotalRewardAmount          string `json:"total_reward_amount"`            //打赏总流水（元）
	TotalOrderGames            string `json:"total_order_games"`              //完成订单局数
	TotalOrderUsers            string `json:"total_order_users"`              //完成订单用户数
	NewUserRoomEntryCount      string `json:"new_user_room_entry_count"`      //完单进房用户数
	NewUserRoomPayCount        string `json:"new_user_room_pay_count"`        //完单进房打赏用户数
	UserRewardAmount           string `json:"user_reward_amount"`             //完单用户打赏总流水（元）
	NewUserConversionCount     string `json:"new_user_conversion_count"`      //完单重点用户数
	NewUserPureConversionCount string `json:"new_user_pure_conversion_count"` //新用户进房数
	PureUserConversionCount    string `json:"pure_user_conversion_count"`     //新用户进房打赏数
	PureUserConversionRevenue  string `json:"pure_user_conversion_revenue"`   //新用户打赏流水（元）
	UserRewardAmountNextMonth  string `json:"user_reward_amount_next_month"`  // 新用户后续打赏流水（元）
}
type OpdataEsportGuildStatsReq struct {
	StartDate   time.Time // 数据日期起，yyyy-MM-dd
	EndDate     time.Time // 数据日期止，yyyy-MM-dd
	TimeDim     string    // 时间维度
	GuildIdList []uint32  // 公会ID
	Page        uint32    // 当前页
	PageSize    uint32    // 每页数量
}
type OpdataEsportGuildStatsResp struct {
	Pagination Pagination                `json:"pagination"` // 分页参数
	Data       []*OpdataEsportGuildStats `json:"data"`       // 响应数据，如果没有则为空数组
}

// queryOpdataEsportRoomStats 电竞公会厅数据
type OpdataEsportRoomStats struct {
	DataDate                      string `json:"data_date"`
	GuildId                       uint32 `json:"guild_id"`
	RoomId                        uint32 `json:"room_id"`
	TimeDim                       string `json:"time_dim"`
	EnterRoomCount                string `json:"enter_room_count"`                  //进房人数
	RewardUserCount               string `json:"reward_user_count"`                 //进房打赏人数
	RewardAmount                  string `json:"reward_amount"`                     //打赏金额 （元）
	CompletedOrderRoomCount       string `json:"completed_order_room_count"`        //完单进房人数
	CompletedOrderRewardUserCount string `json:"completed_order_reward_user_count"` //完单进房打赏人数
	CompletedOrderRewardAmount    string `json:"completed_order_reward_amount"`     //完单进房打赏金额 （元）
	NewUserCompletedOrderCount    string `json:"new_user_completed_order_count"`    //新用户进房数
	NewUserConversionCount        string `json:"new_user_conversion_count"`         //新用户进房打赏数
	NewUserConversionRevenue      string `json:"new_user_conversion_revenue"`       //新用户打赏流水（元）
	CoachRewardAmount             string `json:"coach_reward_amount"`               //大神打赏金额
	CoachRewardRatio              string `json:"coach_reward_ratio"`                //大神打赏占比
}
type OpdataEsportRoomStatsReq struct {
	StartDate   time.Time // 数据日期起，yyyy-MM-dd
	EndDate     time.Time // 数据日期止，yyyy-MM-dd
	TimeDim     string    // 时间维度
	GuildIdList []uint32  // 公会ID
	RoomIdList  []uint32  // 房间ID
	Page        uint32    // 当前页
	PageSize    uint32    // 每页数量
}
type OpdataEsportRoomStatsResp struct {
	Pagination Pagination               `json:"pagination"` // 分页参数
	Data       []*OpdataEsportRoomStats `json:"data"`       // 响应数据，如果没有则为空数组
}

// 数据接口-经营分析
type OpdataMultiOperate struct {
	DataDate                           string `json:"data_date"`                              // 数据日期
	TimeDim                            string `json:"time_dim"`                               // 时间维度
	GuildId                            uint32 `json:"guild_id"`                               // 公会ID
	TotalFlowLevel                     string `json:"total_flow_level"`                       // 公会月流水层级
	TotalFlow                          string `json:"total_flow"`                             // 公会总流水
	TotalFlowRank                      string `json:"total_flow_rank"`                        // 新增从业者数同层级排名x%
	ContentFlow                        string `json:"content_flow"`                           // 内容品类流水
	InteractiveFlow                    string `json:"interactive_flow"`                       // 互动品类流水
	SpecialFlow                        string `json:"special_flow"`                           // 特殊品类流水
	PractitionerFlow                   string `json:"practitioner_flow"`                      // 从业者流水
	MonthTop3Rooms                     string `json:"month_top3_rooms"`                       // 本月流水TOP3房间
	MonthlyEstimatedFlow               string `json:"monthly_estimated_flow"`                 // 本月预估流水
	NewPractitioners                   string `json:"new_practitioners"`                      // 新增从业者数
	NewPractitionersRank               string `json:"new_practitioners_rank"`                 // 新增从业者数同层级排名x%
	NewPractitionerIncome              string `json:"new_practitioner_income"`                // 新增从业者收入
	NewPractitionerIncomeRank          string `json:"new_practitioner_income_rank"`           // 新增从业者收入同层级排名x%
	NewValidPractitioners              string `json:"new_valid_practitioners"`                // 新增有效从业者数
	NewValidPractitionersRank          string `json:"new_valid_practitioners_rank"`           // 新增有效从业者数同层级排名x%
	NewValidPractitionerIncome         string `json:"new_valid_practitioner_income"`          // 新增有效从业者收入
	NewValidPractitionerIncomeRank     string `json:"new_valid_practitioner_income_rank"`     // 新增有效从业者收入同层级排名x%
	NewProPractitioners                string `json:"new_pro_practitioners"`                  // 新增专业从业者数
	NewProPractitionersRank            string `json:"new_pro_practitioners_rank"`             // 新增专业从业者数同层级排名x%
	NewProPractitionerIncome           string `json:"new_pro_practitioner_income"`            // 新增专业从业者收入
	NewProPractitionerIncomeRank       string `json:"new_pro_practitioner_income_rank"`       // 新增专业从业者收入同层级排名x%
	SignedPractitioners                string `json:"signed_practitioners"`                   // 签约从业者数
	SignedPractitionersRank            string `json:"signed_practitioners_rank"`              // 签约从业者数同层级排名x%
	SignedIncome                       string `json:"signed_income"`                          // 签约从业者收入
	SignedIncomeRank                   string `json:"signed_income_rank"`                     // 签约从业者收入同层级排名x%
	SignedRetention                    string `json:"signed_retention"`                       // 签约从业者上月留存数
	ValidPractitioners                 string `json:"valid_practitioners"`                    // 有效从业者数
	ValidPractitionerIncome            string `json:"valid_practitioner_income"`              // 有效从业者收入
	ValidRetention                     string `json:"valid_retention"`                        // 有效从业者数上月留存数
	ProPractitioners                   string `json:"pro_practitioners"`                      // 专业从业者数
	ProPractitionersRank               string `json:"pro_practitioners_rank"`                 // 专业从业者数同层级排名x%
	ProPractitionerIncome              string `json:"pro_practitioner_income"`                // 专业从业者收入
	ProPractitionerIncomeRank          string `json:"pro_practitioner_income_rank"`           // 专业从业者收入同层级排名x%
	ProRetention                       string `json:"pro_retention"`                          // 专业从业者上月留存数
	DailyViolationRate                 string `json:"daily_violation_rate"`                   // 日均违规率
	ViolationPractitioners             string `json:"violation_practitioners"`                // 违规从业者数
	FaceVerificationRate               string `json:"face_verification_rate"`                 // 人脸验证通过率
	FaceVerificationRateNewOld         string `json:"face_verification_rate_new_old"`         // 人脸验证通过率_分新老
	EcoScore                           string `json:"eco_score"`                              // 生态得分
	ViolationDetail                    string `json:"violation_detail"`                       // 违规详细占比
	OperatingRooms                     string `json:"operating_rooms"`                        // 经营房间数
	AvgOperatingFlow                   string `json:"avg_operating_flow"`                     // 经营单厅平均流水
	ContentRooms                       string `json:"content_rooms"`                          // 内容房间数
	AvgContentFlow                     string `json:"avg_content_flow"`                       // 内容单厅平均流水
	InteractiveRooms                   string `json:"interactive_rooms"`                      // 互动房间数
	AvgInteractiveFlow                 string `json:"avg_interactive_flow"`                   // 互动单厅平均流水
	SpecialRooms                       string `json:"special_rooms"`                          // 特殊品类房间数
	AvgSpecialFlow                     string `json:"avg_special_flow"`                       // 特殊品类单厅平均流水
	AvgNotspecialFlow                  string `json:"avg_notspecial_flow"`                    // 单厅平均流水（除特殊品类）
	LuckyGiftFlow                      string `json:"lucky_gift_flow"`                        // 幸运礼物流水
	LuckyGiftRatio                     string `json:"lucky_gift_ratio"`                       // 幸运礼物流水占比
	BackpackGiftFlow                   string `json:"backpack_gift_flow"`                     // 背包礼物流水
	BackpackGiftRatio                  string `json:"backpack_gift_ratio"`                    // 背包礼物流水占比
	TbeanGiftFlow                      string `json:"tbean_gift_flow"`                        // 纯豆礼物流水
	TbeanGiftRatio                     string `json:"tbean_gift_ratio"`                       // 纯豆礼物流水占比
	OtherGiftRatio                     string `json:"other_gift_ratio"`                       // 其他礼物流水占比
	Last3mAvgFlow                      string `json:"last3m_avg_flow"`                        // 近三月月均流水（不包含本月）
	Last3mAvgNewPractitioners          string `json:"last3m_avg_new_practitioners"`           // 近三月新从均值（不包含本月）
	Last3mAvgProPractitioners          string `json:"last3m_avg_pro_practitioners"`           // 近三月专从均值（不包含本月）
	NewPractitionersRetention          string `json:"new_practitioners_retention"`            // 上月新增从业者数留存数
	NewPractitionersRetentionRank      string `json:"new_practitioners_retention_rank"`       // 上月新增从业者数留存数同层级排名x%
	NewPractitionerRetentionIncome     string `json:"new_practitioner_retention_income"`      // 上月新增留存从业者收礼值
	NewPractitionerRetentionIncomeRank string `json:"new_practitioner_retention_income_rank"` // 上月新增留存从业者收礼值同层级排名x%
}

// queryOpdataMultiOperate 公会经营分析数据
type OpdataMultiOperateReq struct {
	StartDate   time.Time // 数据日期起，yyyy-MM-dd
	EndDate     time.Time // 数据日期止，yyyy-MM-dd
	TimeDim     string    // 时间维度
	GuildIdList []uint32  // 公会ID
	Page        uint32    // 当前页
	PageSize    uint32    // 每页数量
}
type OpdataMultiOperateResp struct {
	Pagination Pagination            `json:"pagination"` // 分页参数
	Data       []*OpdataMultiOperate `json:"data"`       // 响应数据，如果没有则为空数组
}

// 数据接口-管理后台首页
type OpdataMultiFrontPage struct {
	DataDate               string `json:"data_date"`                // 数据日期
	GuildId                uint32 `json:"guild_id"`                 // 公会ID
	DailyFlow              string `json:"daily_flow"`               // 日流水
	OperatingRooms         string `json:"operating_rooms"`          // 在营房间数
	DailyNewSignedMembers  string `json:"daily_new_signed_members"` // 日新增签约成员
	DailyTerminatedMembers string `json:"daily_terminated_members"` // 日解约成员
	DailyTop3Rooms         string `json:"daily_top3_rooms"`         // 今日流水TOP3房间
}

// queryOpdataMultiFrontPage 公会多人互动重点数据
type OpdataMultiFrontPageReq struct {
	StartDate   time.Time // 数据日期起，yyyy-MM-dd
	EndDate     time.Time // 数据日期止，yyyy-MM-dd
	GuildIdList []uint32  // 公会ID
	Page        uint32    // 当前页
	PageSize    uint32    // 每页数量
}
type OpdataMultiFrontPageResp struct {
	Pagination Pagination              `json:"pagination"` // 分页参数
	Data       []*OpdataMultiFrontPage `json:"data"`       // 响应数据，如果没有则为空数组
}

// 数据接口-房间数据
type OpdataMultiRoomStats struct {
	DataDate                       string `json:"data_date"`                         // 数据日期
	GuildId                        uint32 `json:"guild_id"`                          // 公会长号id
	RoomId                         uint32 `json:"room_id"`                           // 房间cid
	RoomRevenue                    string `json:"room_revenue"`                      // 房间流水
	SignedMemberGiftIncome         string `json:"signed_member_gift_income"`         // 签约成员收礼流水
	SignedMemberGiftRatio          string `json:"signed_member_gift_ratio"`          // 签约成员收礼占比
	EffectiveOpeningDays           string `json:"effective_opening_days"`            // 有效开厅天数
	BaseOpeningDays                string `json:"base_opening_days"`                 // 基础开厅天数
	NewPractitionersCount          string `json:"new_practitioners_count"`           // 新增从业者数
	EffectiveNewPractitionersCount string `json:"effective_new_practitioners_count"` // 有效新增从业者数
	ProfessionalPractitionersCount string `json:"professional_practitioners_count"`  // 专业从业者数
	NewPractitionersRemain         string `json:"new_practitioners_remain"`          // 新增从业者数留存
}

// queryOpdataMultiRoomStats 公会房间经营数据
type OpdataMultiRoomStatsReq struct {
	StartDate   time.Time // 数据日期起，yyyy-MM-dd
	EndDate     time.Time // 数据日期止，yyyy-MM-dd
	TimeDim     string    // 时间维度
	GuildIdList []uint32  // 公会ID
	RoomIdList  []uint32  // 房间ID
	Page        uint32    // 当前页
	PageSize    uint32    // 每页数量
}
type OpdataMultiRoomStatsResp struct {
	Pagination Pagination              `json:"pagination"` // 分页参数
	Data       []*OpdataMultiRoomStats `json:"data"`       // 响应数据，如果没有则为空数组
}

// GuildPromotion 语音直播经营数据
type GuildPromotion struct {
	DataDate string `json:"data_date"` // 数据日期
	GuildId  uint32 `json:"guild_id"`  // 公会ID

	//营收分析_达人营收贡献
	Revenue      string `json:"revenue"`        // 公会营收
	NewCount     string `json:"new_count"`      // 新达人人数
	OldCount     string `json:"old_count"`      // 老达人人数
	NewLive      string `json:"new_live"`       // 新达人直播间流水
	OldLive      string `json:"old_live"`       // 老达人直播间流水
	NewLiveRatio string `json:"new_live_ratio"` // 新达人直播间流水占比
	OldLiveRatio string `json:"old_live_ratio"` // 老达人直播间流水占比

	//营收分析_达人流水层级
	Lvl1Count     string `json:"lvl1_count"`      // 层级1人数
	Lvl2Count     string `json:"lvl2_count"`      // 层级2人数
	Lvl3Count     string `json:"lvl3_count"`      // 层级3人数
	Lvl4Count     string `json:"lvl4_count"`      // 层级4人数
	Lvl5Count     string `json:"lvl5_count"`      // 层级5人数
	Lvl6Count     string `json:"lvl6_count"`      // 层级6人数
	Lvl1LiveTotal string `json:"lvl1_live_total"` // 层级1直播间总流水
	Lvl2LiveTotal string `json:"lvl2_live_total"` // 层级2直播间总流水
	Lvl3LiveTotal string `json:"lvl3_live_total"` // 层级3直播间总流水
	Lvl4LiveTotal string `json:"lvl4_live_total"` // 层级4直播间总流水
	Lvl5LiveTotal string `json:"lvl5_live_total"` // 层级5直播间总流水
	Lvl6LiveTotal string `json:"lvl6_live_total"` // 层级6直播间总流水
	Lvl1LiveRatio string `json:"lvl1_live_ratio"` // 层级1直播间总流水占比
	Lvl2LiveRatio string `json:"lvl2_live_ratio"` // 层级2直播间总流水占比
	Lvl3LiveRatio string `json:"lvl3_live_ratio"` // 层级3直播间总流水占比
	Lvl4LiveRatio string `json:"lvl4_live_ratio"` // 层级4直播间总流水占比
	Lvl5LiveRatio string `json:"lvl5_live_ratio"` // 层级5直播间总流水占比
	Lvl6LiveRatio string `json:"lvl6_live_ratio"` // 层级6直播间总流水占比

	//拉新分析_经营诊断
	NewSignCount          string `json:"new_sign_count"`           // 新签达人人数
	NewSignCountLevelAvg  string `json:"new_sign_count_level_avg"` //层级公会新签达人平均水平
	NewSignRevenue        string `json:"new_sign_revenue"`         // 新签达人收入
	NewSignListenCount    string `json:"new_sign_listen_count"`    // 新签达人开启听听人数
	NewSign2wCount        string `json:"new_sign_2w_count"`        // 周流水≥2w豆新签达人数
	NewSignProCount       string `json:"new_sign_pro_count"`       //新签专业从业者
	NewSignActive2Count   string `json:"new_sign_active2_count"`   //新签达人听听活跃天≥2天人数
	NewSign10wCount       string `json:"new_sign_10w_count"`       //周流水≥10w豆新签达人数
	ActiveNewSignCount    string `json:"active_new_sign_count"`    //纯新活跃达人数
	NewSignMatureCount    string `json:"new_sign_mature_count"`    //新签成熟达人数
	NewSignPotentialCount string `json:"new_sign_potential_count"` //新签潜力活跃达人数

	// 拉新分析_达人营收贡献
	PureNewCount        string `json:"pure_new_count"`          //纯新达人人数
	NonPureNewCount     string `json:"non_pure_new_count"`      //非纯新达人人数
	PureNewLive         string `json:"pure_new_live"`           //纯新达人直播间流水
	NonPureNewLive      string `json:"non_pure_new_live"`       //非纯新达人直播间流水
	PureNewLiveRatio    string `json:"pure_new_live_ratio"`     //纯新达人直播间流水占比
	NonPureNewLiveRatio string `json:"non_pure_new_live_ratio"` //非纯新达人直播间流水占比

	// 拉新分析_达人收礼层级
	NewLvl1Count     string `json:"new_lvl1_count"`      //新签达人层级1人数
	NewLvl2Count     string `json:"new_lvl2_count"`      //新签达人层级2人数
	NewLvl3Count     string `json:"new_lvl3_count"`      //新签达人层级3人数
	NewLvl4Count     string `json:"new_lvl4_count"`      //新签达人层级4人数
	NewLvl5Count     string `json:"new_lvl5_count"`      //新签达人层级5人数
	NewLvl6Count     string `json:"new_lvl6_count"`      //新签达人层级6人数
	NewLvl1LiveTotal string `json:"new_lvl1_live_total"` //新签达人层级1直播间总流水
	NewLvl2LiveTotal string `json:"new_lvl2_live_total"` //新签达人层级2直播间总流水
	NewLvl3LiveTotal string `json:"new_lvl3_live_total"` //新签达人层级3直播间总流水
	NewLvl4LiveTotal string `json:"new_lvl4_live_total"` //新签达人层级4直播间总流水
	NewLvl5LiveTotal string `json:"new_lvl5_live_total"` //新签达人层级5直播间总流水
	NewLvl6LiveTotal string `json:"new_lvl6_live_total"` //新签达人层级6直播间总流水
	NewLvl1LiveRatio string `json:"new_lvl1_live_ratio"` //新签达人层级1直播间总流水占比
	NewLvl2LiveRatio string `json:"new_lvl2_live_ratio"` //新签达人层级2直播间总流水占比
	NewLvl3LiveRatio string `json:"new_lvl3_live_ratio"` //新签达人层级3直播间总流水占比
	NewLvl4LiveRatio string `json:"new_lvl4_live_ratio"` //新签达人层级4直播间总流水占比
	NewLvl5LiveRatio string `json:"new_lvl5_live_ratio"` //新签达人层级5直播间总流水占比
	NewLvl6LiveRatio string `json:"new_lvl6_live_ratio"` //新签达人层级6直播间总流水占比
}

// queryGuildPromotion 语音直播经营诊断接口
type GuildPromotionReq struct {
	StartDate   time.Time // 数据日期起，yyyy-MM-dd
	EndDate     time.Time // 数据日期止，yyyy-MM-dd
	TimeDim     string    // 时间维度
	GuildIdList []uint32  // 公会ID
	Page        uint32    // 当前页
	PageSize    uint32    // 每页数量
}
type GuildPromotionResp struct {
	Pagination Pagination        `json:"pagination"` // 分页参数
	Data       []*GuildPromotion `json:"data"`       // 响应数据，如果没有则为空数组
}

// 公会房间日数据
type GuildRoomDay struct {
	DataDate               string `json:"data_date"`                // 数据日期
	GuildId                uint32 `json:"guild_id"`                 // 公会ID
	RoomId                 uint32 `json:"room_id"`                  // 房间ID
	PractitionerGiftIncome string `json:"practitioner_gift_income"` // 从业者收礼流水
	BackpackGiftIncome     string `json:"backpack_gift_income"`     // 背包礼物流水
	TbeanGiftIncome        string `json:"tbean_gift_income"`        // T豆礼物流水
	RoomGiftUserCnt        string `json:"room_gift_user_cnt"`       // 房间送礼人数
	Mic1hPractCnt          string `json:"mic_1h_pract_cnt"`         // 接档1小时从业者数
	Mic2hPractCnt          string `json:"mic_2h_pract_cnt"`         // 接档2小时从业者数
	Mic4hPractCnt          string `json:"mic_4h_pract_cnt"`         // 接档4小时从业者数
	RoomFlow               string `json:"room_flow"`                // 房间流水
}

// queryGuildRoomDayIndex 公会房间日数据
type GuildRoomDayReq struct {
	StartDate  time.Time // 数据日期起，yyyy-MM-dd
	EndDate    time.Time // 数据日期止，yyyy-MM-dd
	GuildId    uint32    // 公会ID
	RoomIdList []uint32  // 房间ID列表
	Page       uint32    // 当前页
	PageSize   uint32    // 每页数量
}
type GuildRoomDayResp struct {
	Pagination Pagination      `json:"pagination"` // 分页参数
	Data       []*GuildRoomDay `json:"data"`       // 响应数据，如果没有则为空数组
}

// 签约用户管理
type SignManage struct {
	GuildId                  uint32 `json:"guild_id"`                 // 公会ID
	Uid                      uint32 `json:"uid"`                      // 用户ID
	RecentlyActive           string `json:"recently_active"`          // 最近活跃时间
	ThirtyDLiveMicDuration   string `json:"30d_live_mic_duration"`    // 30日听听时长
	ThirtyDLiveEffectMicDays string `json:"30d_live_effect_mic_days"` // 30日有效听听天数
	ThirtyDMultiMicDays      string `json:"30d_multi_mic_days"`       // 30日有效接档天数
}

// querySignManage 签约用户管理
type SignManageReq struct {
	GuildIdList []uint32 // 公会ID列表
	UidList     []uint32 // 用户ID列表
	Page        uint32   // 当前页
	PageSize    uint32   // 每页数量
}
type SignManageResp struct {
	Pagination Pagination    `json:"pagination"` // 分页参数
	Data       []*SignManage `json:"data"`       // 响应数据，如果没有则为空数组
}
