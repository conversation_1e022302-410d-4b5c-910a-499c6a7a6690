syntax = "proto3";

package rcmd.rcmd_ai_partner_kafka;

option go_package = "golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner_kafka";

message MP_BreakSilenceGreeting {
  uint32 uid = 1;
  uint32 partner_id = 2;
  enum Source {
    Source_EnterChatting = 0; // 进入聊天页面
    Source_BotSelfTrigger = 1; // 机器人主动触发
  }
  Source source = 3;
}

message MP_Greeting { // 桌宠打招呼
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;// 角色ID
    enum GreetingType {
        GreetingType_Unknown = 0; // 未知
        GreetingType_EnterPetHome = 1; // 进入桌宠首页
    }
    GreetingType greeting_type = 4; // 打招呼类型
    string source = 5;//触发来源，用于区分不同的触发来源
}

message MP_LoginGreeting {
  uint32 uid = 1;
  uint32 market_id =2;
  uint32 cli_version = 3; // 客户端版本
  enum Type {
      LoginEventType_Login = 0; // 登录
      LoginEventType_Register = 1; // 注册
      LoginEventType_VisitHome = 2; // 首页曝光
  }
  Type type = 4; // 事件类型
  uint32 client_type = 5; // 客户端类型
  string device_id_hex = 6; // 设备ID
}

enum UserSource {
  UserSource_Unknown = 0;
  UserSource_ChatIn3Day = 1;
  UserSource_GettingUser = 2;
  UserSource_SelectTime = 3;
}

message MP_ScheduleGreeting {
  uint32 uid = 1;
  uint32 partner_id = 2;
  enum Type {
    Type_Invalid = 0;
    Type_FirstGreeting = 1;
    Type_SecondGreeting = 2;
    Type_WeekendGreeting = 3;
    Type_Common = 4;
    Type_Role = 5;//多角色
  }
  Type type = 3;
  int64 expected_send_at = 4; // 期望的发送时间，用于确定文案
  string schedule_conf_id = 5; // 调度任务配置id，用于确定文案
  uint32 role_id = 6;// 角色ID
  string role_style = 7;// 角色风格
  string session_id = 8;// 回话ID
  uint32 day_push_num = 9;// 当天第几次推送
  int32 user_source = 10;// 用户来源 UserSource
  repeated string greet_content = 11; // 打招呼文案
  string bus_id = 12;  // 业务ID
  bool is_test = 13; // 是否是测试
  uint32 reply_type = 14;
}

// 用户回复角色消息
message MP_UserReply {
  uint32 uid = 1;
  uint32 partner_id = 2;

  MsgType actual_msg_type = 3;
  string msg_content = 4;
  bytes ext = 5;
  uint32 tt_msg_type = 6;
  string msg_id = 7;
  int64 sent_at = 8;
  string trace_id = 9;
  uint32 ai_role_type = 10; // 0树洞 1游戏角色 2桌宠
  uint32 im_busi_type = 11; //业务类型 ImBusiType, 定义见 chat-bot.proto.ImBusiType
  uint32 im_content_type = 12; //内容类型 ImMsgContentType, 定义见 chat-bot.proto.ImMsgContentType
  uint32 im_cmd_type = 13; //命令类型 ImCmdType, 定义见 chat-bot.proto.ImCmdType
  map<string,string> extra_map = 14; // 扩展字段
  bool is_exactly_reach_limit = 15; // 是否刚好到达句数上限
}

// 用户回复群组消息
message MP_GroupUserReply {
    message UserInfo {
        string name = 1; // 用户昵称或扮演角色名
        uint32 play_role_id = 2; // 扮演角色ID (若无置 0 )
    }
    uint32 uid = 1;
    uint32 group_template_id = 2;
    uint32 group_instance_id = 3;

    MsgType actual_msg_type = 4;
    string msg_content = 5;
    bytes ext = 6;
    uint32 tt_msg_type = 7;
    uint32 seq_id = 8;  // 消息序号，拉历史消息的时候用到
    int64 sent_at = 9;
    string trace_id = 10;
    uint32 ai_group_type = 11; // 0默认
    repeated uint32 target_role_ids = 12; // 群组 @ 角色 ID 列表
    repeated uint32 target_uids = 13; // 群组 @ 用户 ID 列表
    map<uint32, uint32> uid_to_play_role_id = 14; // uid -> 扮演角色ID 映射
    map<uint32, UserInfo> uid_to_info = 15;  // 所有用户 ID 到用户信息的映射
    map<string,string> extra_map = 16; // 扩展字段
}

// 触发 AI 主动向群内发送消息
message MP_AISendToGroupTrigger {
    message UserInfo {
        string name = 1; // 用户昵称或扮演角色名
        uint32 play_role_id = 2; // 扮演角色ID (若无置 0 )
    }
    uint32 group_template_id = 1;  // 群模板 ID
    uint32 group_instance_id = 2;  // 群实例 ID
    map<uint32, UserInfo> uid_to_info = 3;  // 所有用户 ID 到用户信息的映射
    repeated uint32 target_uids = 4;  // 被 @ 的用户 ID 列表
    string trace_id = 5; // 追踪链路ID
}

message MP_SilenceNotify {
  uint32 uid = 1;
  uint32 partner_id = 2;
  string version = 3;// 用于区分新旧版本
}

message MP_ResetElizaCtx {
  uint32 uid = 1;
  uint32 partner_id = 2;
  uint32 eliza_ctx_id = 3;
}

message MP_Tick{
    int64 ts = 1;
    string name = 2;
}

message MP_DelayMsgEvent {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 session_id = 3;
    uint32 eliza_id = 4;
    uint32 role_id = 5;
    string role_style = 6;

    SendMsg user_msg = 7; // 用户发送的消息
    int64 receive_ts = 8; // 接受到用户消息的时间

    string send_msg_id = 9;
    uint32 send_msg_order = 10;
    repeated SendMsg send_msgs = 11;

    uint32 reply_type = 12;
    uint32 scene_type = 13;
    uint32 reply_msg_plan_b = 14; // 回复的兜底类型
    string excepted_send_msg = 15; // 期望发送的消息
    string model_used = 16; // 使用的模型
    string algo_detected_info = 17; // 算法检测信息
    bool retry = 18; // 是否在发送失败时重试
    bool save_reply = 19; // 是否保存回复的内容作为下次回复的上下文

    string user_format_msg = 20; //修正后的用户回复文本，用于替换用户的原始回复文本来进行GPT文本生成
    uint32 meet_days = 21; // 相遇天数，数分需要数据上报
    string placeholder = 22; // 替换符信息,json
    string session_id_str = 23;//字符串回话ID
    StoryInfo story_info = 24;// 消息类型为故事相关时存在
    string original_excepted_msg = 25; // GPT首次生成的文本
    repeated string user_text_labels = 26; // 用户输入文本的情感标签识别结果

    string no_parse_gpt_text = 27; // 未经过处理的GPT原始文本
    string chatting_id = 28; // 应用中心的ID
    string prompt_id = 29; //
    string prompt_version = 30;
    string retry_report = 31;// 重试上报的数据

    string come_from = 32; // 来源
    // 从db.user_game_character获取来源，如果为true，就不再db里面拉取。
    // 防止db的数据为空时，在进行子消息发送时，出现重复读DB的无效情况
    bool disable_get_come_from = 33;
    string user_text_labels_model_info = 34; // 用户输入文本的情感标签识别结果使用的模型
    CtxInfo ctx_info = 35;// 上下文信息
    string game_type = 36; // 玩法类型，用于上报
    string voice_chat_ctx_id = 37; // 语音聊天的上下文ID
    repeated MP_Tick ticks = 38;//追踪链路时间
    string trace_id = 39;//追踪链路ID
    uint32 ai_role_type = 40; // 0树洞 1游戏角色 2桌宠
    bool is_use_extra_chat = 41; // 是否使用额外的聊天次数

    int64 custom_push_content_interval = 42; // 自定义的消息发送间隔

    uint32 user_msg_count = 43; // 用户发送的消息数(用于记录消息轮次)
    uint32 original_reply_word_count = 44;  // gpt 返回截断前的消息字数
    uint32 segment_count = 45;  // 截断分段数

    uint32 trigger_msg_type = 46;  // 触发消息类型
    uint32 sentence_type = 47; // 消耗句数类型
    map<string,string> extra_map = 48; // 扩展字段
}

message StoryInfo {
    uint32 story_id = 1; // 故事ID
    string scene_id = 2; // 场景ID
    string dialog_id = 3; // 对话ID 或者是 node_id
    string content_id = 4;

    string old_scene_id = 5; // 上一次的场景ID, 如果没有就传空
    string old_dialog_id = 6; // 上一次的对话ID或者是node_id, 如果没有就传空
    string old_content_id = 7; // 内容ID

    string story_id_str = 8; // 故事id
    string chapter_id = 9; // 章节ID
    string npc_voice_id = 10; // 角色音色Id
    string narrator_voice_id = 11; // 旁白音色Id
}

message SendMsg {
    string content = 1;
    uint32 content_type = 2;

    MsgType msg_type = 3;
    bytes ext = 4; // 扩展消息
    string offline_push_content = 5; // 离线推送消息显示
    // 消息拆分后，每个单独句子进行算法检测，需要上报对应数据,这些信息在发送下一句时回填到MP_DelayMsgEvent
    bool is_split = 6; //是否为消息拆分的子句，否则忽略下面的数据
    uint32 reply_type = 7;
    uint32 reply_msg_plan_b = 8; // 回复的兜底类型
    string excepted_send_msg = 9; // 期望发送的消息
    string algo_detected_info = 10; // 算法检测信息


    bool save_as_history = 11; // 是否保存回复的内容作为下次回复的上下文
    bool tts = 12;// 是否需要文字转语音
    int32 spk_id = 13;// 文字转语音的发音人，废弃
    uint32 tts_role_id = 14;// 文字转语音的角色ID
    string tts_model = 15;// 文字转语音的模型
    string tts_voice_id = 16;// 音色ID
    int64 delay_seconds = 17; // 延迟发送时间，单位秒，0表示没有提前计算，忽略此字段
    string custom_voice_json = 18; // 自定义音色

    bool is_no_audit = 19; // 是否不需要送审
    string audit_text = 20; // 送审文本内容
}

enum MsgType {
    Invalid = 0;
    Text = 1; // 文本
    Meme = 3; // 表情
    Silence = 4; // 免打扰
    AirTick = 5; // 房间推送
    Common = 6; // 通用消息类型, 用来表示 TT 的通用消息类型， 1~5跟TT的消息类型保持一致, 大于 6的 类型都是UniformMsg.type的消息类型
    // 1~6类型是TT的规定的消息类型，如果想扩展新消息类型，使用 Common类型，并且用UniformMsg消息填充到TT消息协议的ext里面

    AnimatedMeme = 10; // 互动表情
    RichText = 11; // 富文本
    RelationshipLetter = 12; // 关系信件
    NewPhotograph = 13; // 获取新立绘
    ListenSong = 14; // 一起听歌

    SetName = 15; //修改树洞昵称
    SetUserNickname = 16; // 修改用户昵称
    SetRoleTip = 17; // 修改用户形象的tip
    UserAutoReply = 18; // 用户被动触发的事件
    UserGuide = 19; // 引导词

    StoryEntrance = 20; // 故事入口, ext=Ext_StoryEntrance
    StoryNarration = 21; // 故事旁白, ext=Ext_StoryNarration
    StoryText = 22; // 故事文本消息, ext=Ext_StoryCommon
    StoryImage = 23; // 故事图片消息, ext=Ext_StoryCommon
    StoryFinish = 24; // 故事结束, ext=Ext_StoryFinish
    StoryOptForUserReply = 25; // 用户通过故事提示选择的回复文本, ext= Ext_StoryOptForUserReply
    StoryRewardsNotify = 26;// 故事完成奖励
    NewAtmosphere = 27; // 获得新的氛围 Ext_NewAtmosphere
    GameCharacterTips = 28; // 游戏形象任务 固定提示内容
    TTSMsg = 29; // 文字转语音消息, ext=Ext_TTSMsg
    UserGuideV2 = 30; // 引导词v2
    StoryAnimatedMeme = 31; // 故事轻互动, ext=Ext_StoryAnimateMeme
    SetPartnerInfo = 32; // 设置树洞形象、昵称等, 即把SetUserNickname、SetRoleTip和SetRoleTip整合成一个消息
    QuickReply = 33; // 快捷回复，功能与Text=1一致，但是不需要用户点击发送

    StoryBookEntrance = 34; // 故事书入口
    StoryTTSMsg = 35; // 故事TTS
    VoiceChatReply = 36; // 语音通话回复

    PetReply = 37; // 宠物的回复
    AIRcmdReply = 38; // AI推荐的回复
    ContinueChat = 39; // 继续说
    ReachChatLimitMsg = 40; // 达到聊天上限提示消息
    PlayTarotTip = 41; // 玩塔罗牌提示消息
    TargetCompleteMsg = 42; // 目标完成提示消息
    TargetTriggerMsg = 43; // 目标触发提示消息
}

message Ext_StoryBookEntrance {
    string story_book_id = 1; // 故事书ID
    string title = 2;
    string subtitle = 3;
    string cover_image = 4;
}

message CtxInfo{
    string ctx_id = 1;//下文ID
    string game_id = 2;//互动玩法ID
    uint32 ctx_scene = 3; // 上下文场景
}

// 上下文场景
enum CtxScene {
    CtxScene_Unknown = 0;

    CtxScene_PetSleepTalk = 3001; // 桌宠哄睡
}

message UniformMsg {

    MsgType type = 1; // 消息类型
    string content = 2;
    bytes ext = 3; // 扩展消息
    CtxInfo  ctx_info = 4;// 上下文信息
}

message Ext_AnimateMeme {
    bool is_auto_show = 1; // 是否自动展示动画
    bool is_first_unlock = 2; // 是否是第一次解锁
}

message RichTextFormat {
    string text = 1;
    string style = 2;
}

message Ext_RichText {
    repeated RichTextFormat texts = 1;
}

message Ext_RelationshipLetter {
    string to = 1; // 写给谁
    string from = 2; // 谁写
    string content = 3; // 信件的内容
}
message Ext_TTSMsg {
    string url = 1; // 语音url
    uint32 seconds = 2; // 语音长度，单位秒
    string text = 3; // 语音文本内容
    string display_msg = 4; // 显示的包含@用户消息内容
}

message Ext_NewPhotograph {
    uint32 id = 1;// 立绘ID
    string avatar = 2; // 头像资源
    string image = 3; // 立绘
    uint32 type = 4; // 立绘类型
    string image_for_msg = 5; //
}

message Ext_UserAutoReply {
    repeated RichTextFormat texts = 1;
    uint32 trigger_msg_type = 2; // 由哪个消息类型触发的
    MsgType trigger_uniform_msg_type = 3; // 当trigger_msg_type=6时，需要填写 ext里面的msg_type，即uniform.msg_type
}
message Ext_VoiceChatReply {
    // "voice_chat_ctx_id":"",//string，语音聊天的上下文ID,调用StartVoiceChat接口获得
    // "input_from":0,//int32,0:语音识别,1:打字输入
    string voice_chat_ctx_id = 1;
    int32 input_from = 2;

}
message IMEvent {
    uint32 uid = 1;
    uint32 partner_id = 2;
    enum RoleType {
        RoleType_User = 0;
        RoleType_Partner = 1;
    }
    RoleType sender = 3; // 发送者
    int64 send_ts = 4; // 发送时间
    string msg_content = 5; // 消息内容
    uint32 msg_type = 6; // 消息类型
    bytes ext = 7; // 扩展内容
}

message NoReplyEvent {
    uint32 uid  = 1;
    uint32 partner_id = 2;
    uint32 last_user_msg_count = 3; //发送消息时，用户发送的消息数，用于判断用户是否已经回复消息了，大于这个数值，说明用户产生回复行为
    enum Type {
        Type_Invalid = 0;
        Type_RoleTip = 1; // 设置用户形象
        Type_SetName = 2; // 设置树洞昵称
        Type_SetNickname = 3; // 设置用户昵称
        Type_SetPartnerInfo = 4; // 设置树洞信息
    }
    Type type = 4;
    string version = 5;// 用于区分新旧版本
}

message Ext_StoryEntrance {
    uint32 story_id = 1; // 故事ID
    repeated RichTextFormat rich_text = 2; // 故事入口消息内容
    int64 old_trigger_ts = 3;//最近一次的触发时间
}

message Ext_StoryCommon {
    string dialog_id = 1; // 对白ID
    uint32 story_id = 2; // 故事ID
    bool use_need_reply = 3; // 是否轮到用户对话

    // 故事书玩法新增字段
    string chapter_id = 4; // 章节ID
    string story_id_str = 5; // 故事ID
    string version = 6; //
    string name = 7; // 昵称
    string avatar = 8; // 头像

    string url = 9; // 语音url
    uint32 seconds = 10; // 语音长度，单位秒
    string text = 11; // 语音文本内容
}

message Ext_StoryNarration {
    repeated RichTextFormat texts = 1;
    string dialog_id = 2; // 对白ID
    uint32 story_id = 3; // 故事ID
    bool use_need_reply = 4; // 是否轮到用户对话

    // 故事书玩法新增字段
    string chapter_id = 5; // 章节ID
    string story_id_str = 6; // 故事书ID
    string version = 7; //
    string name = 8; // 昵称
    string avatar = 9; // 头像
    string url = 10; // 语音 url
    uint32 seconds = 11; // 语音长度，单位秒
    string text = 12; // 语音文本内容
}

message Ext_StoryFinish {
    uint32 story_id = 1; // 故事ID
    string text = 2; // 结束文本
    uint32 story_classify_id = 3; // 故事类型ID，用于统计

    // 故事书玩法新增字段
    string chapter_id = 4; // 章节ID
    string story_id_str = 5; // 故事书ID
    string version = 6; //;
    string name = 7; // 昵称
    string avatar = 8; // 头像
}

message Ext_StoryOptForUserReply {
    string dialog_id = 1; // 对白ID
    uint32 story_id = 2; // 故事ID
    string content_id = 3; // 选项ID

    // 故事书玩法新增字段
    string chapter_id = 4; // 章节ID
    string story_id_str = 5; // 故事书ID
    string version = 6; //

    string name = 7; // 昵称
    string avatar = 8; // 头像
}

message Ext_StoryRewardsNotify {
    uint32 story_id = 1;
    // 故事入口消息内容, rich_text需要在里面的style定一个点击类型
    repeated RichTextFormat rich_text = 2;
}

message Ext_NewAtmosphere {
    string id = 1; // 氛围ID
    string resource_url = 2; // 资源的url
    string resource_type = 3; // 资源类型, 如video
    string description = 4;
    string audio_url = 5; // 音频
    string small_url = 6; // 小图
    string image_url = 7; // 正常图片
}

message Ext_StoryAnimateMeme {
    string dialog_id = 1; // 对白ID
    uint32 story_id = 2; // 故事ID
    bool use_need_reply = 3; // 是否轮到用户对话
    bool is_auto_show = 4; // 是否自动展示动画
}

message StoryNotify {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 story_id = 3;

    enum NotifyType {
        NotifyType_Invalid = 0;
        NotifyType_Start = 1; // 开始故事
        NotifyType_Restart = 2; // 重置开始故事
        NotifyType_Next = 3; // 触发下一个对话
        NotifyType_Continue = 4; // 继续故事
    }
    NotifyType notify_type = 4;
}

message PushSchedule {
    uint32 uid = 1;
    uint32 order = 2; // 触发顺序
    enum Type {
        Type_Default = 0; // 正常触发
        Type_AfterPartnerCreated = 1; // 在partner 创建之后触发
    }
    Type type = 3;
    uint32 role_id = 4; // type =3 时一定存在
    uint32 partner_id = 5; // type =3 时一定存在
}

message UserReadMsgNotify {
    uint32 uid = 1;
    uint32 partner_id = 2;
    string msg_id = 3;
    int64 received_ts = 4; // 收到通知的时间
}

message UserReadNoReplyEvent {
    uint32 uid = 1;
    uint32 partner_id = 2;
    string read_msg_id = 3;
    int64 received_ts = 4; // 收到通知的时间
    int64 read_msg_sent_ts = 5; // 已读消息的发送时间
    enum TriggerType {
        Push_Role_Trigger = 0; // 主动触发角色已读未回
        All_Role_Trigger = 1; // 所有角色已读未回
    }
    TriggerType trigger_type = 6; //已读未回类型
}

message PartnerSharedUserEvent {
    uint32 uid = 1; // 被分享的用户
    uint32 inviterUid = 2; // 邀请者UID
    uint32 ts = 3; // 事件发生时间戳（激活时间）

    string device_id_hex = 4; // 服务端设备Id
    uint32 client_type = 5; // 客户端类型， 0 android， 1 ios
    uint32 market_id = 6; // 0:tt, 2: 欢游, 具体可以查询ga_base.proto MarketId
    string cli_device_id = 7; // 客户端设备ID
}

message RoleCreatorNotifyEvent {
    uint32 uid = 1; // 角色创建者
    uint32 role_id = 2;
    uint32 chatted_user_count = 3; // 被用户聊过的次数
}

message WhiteListUserEvent {
    uint32 uid = 1; // 白名单用户
    string device_id_hex = 2; // 服务端设备ID, Source=0时存在
    uint32 client_type = 3; // 客户端类型， 0 android， 1 ios
    string device_status = 4; // 流量类型, Source=0时存在
    uint32 market_id = 5; // 0:tt, 2: 欢游, 具体可以查询ga_base.proto MarketId
    string cli_device_id = 6; // 客户端设备ID

    enum Source {
        Source_COST = 0; // 买量
        Source_SEARCH = 1; // 搜索
    }
    Source source = 7; // 0 买量, 1 搜索
}

message StoryBookNotify {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string story_id = 4;
    string chapter_id = 5;

    enum Type {
        Type_Invalid = 0;
        Type_Start = 1; // 开始故事章节
        Type_Continue = 2; // 继续故事
    }
    Type type = 6;
}

message Ext_StoryTTSMsg {
    string url = 1; // 语音url
    uint32 seconds = 2; // 语音长度，单位秒
    string text = 3; // 语音文本内容

    string dialog_id = 4; // 对白ID
    string story_id_str = 5; // 故事ID
    bool use_need_reply = 6; // 是否轮到用户对话

    // 故事书玩法新增字段
    string chapter_id = 7; // 章节ID
    string version = 8; //
    string name = 9; // 昵称
    string avatar = 10; // 头像
}

message PetTipReport {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    Action action = 4;
    enum Action {
        Action_Invalid = 0; //
        Action_DefaultClick = 1; // 默认状态下点击
        Action_Drag = 2; // 拖拽触发
        Action_PeekClick = 3; // 贴边点击
        Action_PeekAFK = 4; // 贴边挂机触发
        Action_DefaultAFK = 5; // 默认状态挂机触发
    }
    string tip = 5;
    string msg_id = 6; // 消息ID
    int64 sent_at = 7; // 发送时间（毫秒级）
}

message Ext_PetReply {
    string url = 1; // 语音url
    uint32 seconds = 2; // 语音长度，单位秒
    string text = 3; // 文本回复内容
    string user_text = 4;//用户输入文本
    string content_type = 5; // 内容类型, text:纯文本,tts：文字转语音
    string bubble_msg = 6; // 桌宠气泡消息
    string display_msg = 7; // 桌宠展示消息
    string target_id = 8; // 目标ID
    string target_title = 9; // 目标标题
    string target_subtitle = 10; // 目标副标题
    string target_icon = 11; // 目标图标
    bool has_reward = 12; // 是否有奖励
}

message GroupReportExtra {
    string  role_name = 1;
    uint32  order = 2;
    bool    is_use_extra_chat = 3;
    string  chat_context = 4;
    string  model_used = 5;
    string  seed = 6;  // 模型排查问题用
    string  user_name = 7; // 用户昵称或扮演角色名
    string  response_role_name = 8; // 回复角色名
    string  role_info_list = 9; // 角色信息列表
    uint32  reply_msg_plan_b = 10; // 回复的兜底类型
    uint32  scene_type = 11; // 场景类型
    uint32  group_type = 12; // 群组类型: 1-单用户 2-多用户 3-多用户剧本
}

message GroupMsgIndex {
    uint32 total_ai_count = 1; // 回复该消息的AI伴侣数量
    uint32 cur_ai_index = 2; // 当前回复的AI伴侣的编号（从 1 开始）
    uint32 cur_ai_msg_count = 3; // 当前AI伴侣回复的消息数量
    uint32 cur_ai_msg_index = 4; // 当前AI伴侣回复的消息编号（从 1 开始）
}
message MP_GroupDelayMsgEvent {
    MP_GroupUserReply   user_reply = 1;
    repeated SendMsg    send_msgs = 2;
    uint32              role_id = 3;
    int64               delay_time = 4;
    repeated string     audit_contents = 5;
    GroupReportExtra    report_extra = 6;
    repeated uint32     group_role_ids = 7;
    map<uint32,string>  role_id_to_name = 8;
    uint32              reply_msg_plan_b = 9;
    uint32              send_order = 10;
    int64               push_delay_time = 11;
    uint32              scene = 12;
    repeated uint32     at_target_uids = 13; // 目标 @ 的用户 ID 列表
    bytes               ext = 14; // 扩展消息
    uint32              req_seq_id = 15;  // 消息序号，透传返回给 tt 做回复率统计
    map<string, string> extra_map = 16; // 扩展字段
    GroupMsgIndex group_msg_index = 17; // 群组消息索引
}
