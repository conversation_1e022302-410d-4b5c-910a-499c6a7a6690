syntax = "proto3";

package rcmd.game_character;

option go_package = "golang.52tt.com/protocol/services/rcmd/game_character";

import "rcmd-ai-partner/partner_common.proto";
service RCMDGameCharacter {
  // 设置角色信息
  rpc SetCharacterInfo(SetCharacterInfoReq) returns (SetCharacterInfoResp);
  // 接收用户发送的消息
  rpc ReceiveMsgFromUser(ReceiveMsgFromUserReq) returns (ReceiveMsgFromUserResp);
  // 用户进入聊天页通知
  rpc UserEnterChattingNotify(UserEnterChattingNotifyReq) returns (UserEnterChattingNotifyResp);

  rpc Test(TestReq) returns (TestResp);

  rpc DeleteCharacter(DeleteCharacterReq) returns (DeleteCharacterResp);
  rpc ReplyTextFormat(ReplyTextFormatReq) returns (ReplyTextFormatResp);

  rpc BatchGetCharacters(BatchGetCharactersReq) returns (BatchGetCharactersResp);
  // 获取用户信息
  rpc GetUserInfo(GetUserInfoReq) returns(GetUserInfoResp);
  // Get GPT Req info
  rpc GetGPTReqInfo(GetGPTReqInfoReq) returns(GetGPTReqInfoResp);

  rpc FormatGptAnswer(FormatGptAnswerReq) returns(FormatGptAnswerResp);

  // 开始互动玩法
  rpc StartGame(StartGameReq) returns(StartGameResp);

  // 获取故事书列表
  rpc GetStoryBookList(GetStoryBookListReq) returns(GetStoryBookListResp);
  rpc GetStoryBook(GetStoryBookReq) returns(GetStoryBookResp);
  // 常驻入口红点
  rpc GetStoryBookNotifySeq(GetStoryBookNotifySeqReq) returns(GetStoryBookNotifySeqResp);
  // 常驻红点已读
  rpc MarkStoryBookNotifySeq(MarkStoryBookNotifySeqReq) returns(MarkStoryBookNotifySeqResp);
  // 开始故事章节
  rpc StartStoryBook(StartStoryBookReq) returns(StartStoryBookResp);
  // 获取故事进度
  rpc GetStoryBookProgress(GetStoryBookProgressReq) returns(GetStoryBookProgressResp);
  // 获取故事用户回复
  rpc GetStoryBookReply(GetStoryBookReplyReq) returns(GetStoryBookReplyResp);
  // 获取故事历史记录
  rpc GetStoryBookHistory(GetStoryBookHistoryReq) returns(GetStoryBookHistoryResp);

  // 推荐回复生成
  rpc GenRecommendReply(GenRecommendReplyReq) returns(GenRecommendReplyResp);

  // 生成语音打招呼
  rpc GenVoiceChatGreeting(GenVoiceChatGreetingReq) returns(GenVoiceChatGreetingResp);

  // 是否显示免打扰
  rpc ShowPartnerSilent(ShowPartnerSilentReq) returns(ShowPartnerSilentRsp);

  // 是否显示继续说
  rpc ShowContinueChat(ShowContinueChatReq) returns(ShowContinueChatRsp);

  // 继续说
  rpc ContinueChat(ContinueChatReq) returns(ContinueChatRsp);

  // 给自己发消息1
  rpc TestRoleSendMsg(TestRoleSendMsgReq) returns(TestRoleSendMsgRsp);

  // 获取塔罗配置
  rpc GetTarotConfig(GetTarotConfigReq) returns(GetTarotConfigRsp);

  // 翻开塔罗牌
  rpc OpenTarotCard(OpenTarotCardReq) returns(OpenTarotCardRsp);

  // 获取塔罗结果
  rpc GetTarotResult(GetTarotResultReq) returns(GetTarotResultRsp);

  // 获取宠物陪伴天数
  rpc GetPetOwnDay(GetPetOwnDayReq) returns(GetPetOwnDayRsp);

  // 让桌宠发消息
  rpc TestPetSendMsg(TestPetSendMsgReq) returns(TestPetSendMsgRsp);

  // 获取听故事次数
  rpc GetPetStoryNum(GetPetStoryNumReq) returns(GetPetStoryNumRsp);

  // 开始桌宠故事
  rpc StartPetStory(StartPetStoryReq) returns(StartPetStoryRsp);

  // 结束桌宠故事
  rpc StopPetStory(StopPetStoryReq) returns(StopPetStoryRsp);

  // 进入桌宠哄睡场景
  rpc EnterPetSleepTalk(EnterPetSleepTalkReq) returns(EnterPetSleepTalkRsp);

  // AI房间可见性
  rpc CheckAIRoomVisibility(CheckAIRoomVisibilityReq) returns(CheckAIRoomVisibilityResp);

  // 获取氛围配置
  rpc GetVibesConfig(GetVibesConfigReq) returns(GetVibesConfigRsp);

  // 增加陪伴时间
  rpc AddAccompanyTime(AddAccompanyTimeReq) returns(AddAccompanyTimeRsp);

  // 获取陪伴时间
  rpc GetAccompanyTime(GetAccompanyTimeReq) returns(GetAccompanyTimeRsp);

  // 获取目标列表
  rpc GetTargetList(GetTargetListReq) returns(GetTargetListRsp);

  // 领取目标奖励
  rpc GetTargetReward(GetTargetRewardReq) returns(GetTargetRewardRsp);

  // 领取聊天次数
  rpc GetChatNum(GetChatNumReq) returns(GetChatNumRsp);

  // 用户自定义场景相关
  rpc SetUserChatScene(SetUserChatSceneReq)returns(SetUserChatSceneResp); // 设置自定义聊天场景
  rpc CheckChatSceneVisibility(CheckChatSceneVisibilityReq) returns(CheckChatSceneVisibilityResp); // 判断已配置的角色是否关闭自定义场景，以及自定义场景ab实验

  // 获取外显玩法
  rpc GetOutGames(GetOutGamesReq) returns(GetOutGamesRsp);

  // 获取主题列表
  rpc GetGameTopics(GetGameTopicsReq) returns(GetGameTopicsRsp);

  // 获取某主题玩法列表
  rpc GetTopicGames(GetTopicGamesReq) returns(GetTopicGamesRsp);

  // 获取玩法信息
  rpc GetRoleGameInfo(GetRoleGameInfoReq) returns(GetRoleGameInfoRsp);

  // 用户是否需要触发聊天引导
  rpc CheckIfTriggerChat(CheckIfTriggerChatReq) returns(CheckIfTriggerChatResp);

  // 获取未与用户聊过天的角色
  rpc GetNotOpenedRoles(GetNotOpenedRolesReq) returns (GetNotOpenedRolesResp);

  // 聊天记录追加（续聊）
  rpc ChatHistoryAppend(ChatHistoryAppendReq) returns (ChatHistoryAppendResp);

  // 读心
  rpc ReadPartnerHeart(ReadPartnerHeartReq) returns (ReadPartnerHeartResp);  // 角色读心
}

message GetRoleGameInfoReq {
    uint32 uid = 1;  //用户ID
    uint32 partner_id = 2;  // 机器人ID
    uint32 role_id = 3;   // 角色ID
    string req_dev_id = 4;  // 请求设备ID
    string game_id = 5;   // 玩法ID
}

message GetRoleGameInfoRsp {
    GameInfo game = 1;  // 玩法信息
}

message GetTopicGamesReq {
    uint32 uid = 1;  //用户ID
    uint32 partner_id = 2;  // 机器人ID
    uint32 role_id = 3;   // 角色ID
    string req_dev_id = 4;  // 请求设备ID
    uint32 topic_id = 5;   // 主题ID 传0表示不限主题 即热门
}

message GetTopicGamesRsp {
    repeated GameInfo games = 1;  // 玩法列表
}

message GetGameTopicsReq {
    uint32 uid = 1;  //用户ID
    uint32 role_id = 2;   // 角色ID
}

message GetGameTopicsRsp {
    repeated GameTopic topics = 1; // 主题列表
}

enum GameInfoOperate {
    GAME_INFO_OPERATE_UNKNOWN = 0;
    GAME_INFO_OPERATE_UPSERT = 1;  // 新增或更新
    GAME_INFO_OPERATE_DELETE = 2;  // 删除
}

message GameInfoNotify {
    uint32 uid = 1;  //用户ID
    GameInfoOperate operate = 2;  // 操作类型
    repeated GameInfo games = 3;
}

enum GameCreator {
    GAME_CREATOR_UNKNOWN = 0;
    GAME_CREATOR_SYSTEM = 1;    // 官方
    GAME_CREATOR_USER = 2;      // 用户
}

message GameTopic {
    uint32 id = 1;    // 主题ID
    string name = 2;  // 主题名
}

enum GamePermission {
    GAME_PERMISSION_UNKNOWN = 0;
    GAME_PERMISSION_PUBLIC = 1;
    GAME_PERMISSION_PRIVATE = 2;
}

message GameInfo {
    string id = 1;   // 玩法ID
    string name = 2; // 玩法名
    string icon = 3;  // 图标
    string bgm = 4;  // 背景音乐URL
    string bg = 5;  // 背景图
    int32 creator = 6;  // 创建者 GameCreator
    uint32 topic_id = 7; //主题ID
    string scene_desc = 8; //场景描述
    string prompt = 9; // 提示词 用户配置的提示词=场景描述
    uint32 role_id = 10; // 角色ID
    int32 user_public = 11; // 用户设置的权限 GamePermission
    int32 sys_public = 12; // 系统设置的权限 GamePermission
    int64 create_time = 13; // 创建时间戳
    uint32 user_num = 14; // 使用人数
    string greeting = 15; // 开场白
    string subtitle = 16; // 副标题
    uint32 creator_uid = 17; // 创建者ID
}

message GetOutGamesReq {
    uint32 uid = 1;  //用户ID
    uint32 partner_id = 2;  // 机器人ID
    uint32 role_id = 3;   // 角色ID
    string req_dev_id = 4;
}

message OutGames {
    uint32 topic_id = 1; //主题ID
    repeated GameInfo games = 2;  // 玩法列表
}

message GetOutGamesRsp {
    repeated GameTopic topics = 1;
    repeated OutGames games = 2;
}

message GenVoiceChatGreetingReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string voice_id = 4;
    string voice_json = 5;
    string text = 6;
    string session_id = 7;
}

message GenVoiceChatGreetingResp {
    string url = 1;
    int64 seconds = 2; // 消息类型
    string content = 3; // 内容
}

message GetStoryBookHistoryReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string story_id = 4;
    string chapter_id = 5;
    int64 max_sent_at = 6; //返回比max_sent_at小的数据，不传默认拉取最新的
    uint32 size = 7; //拉取的大小
}

message StoryMsg {
    uint32 type = 1; // 消息类型
    string content = 2; // 内容
    bytes ext = 3; // 扩展内容
    int64 sent_at = 4; // 发送时间
    string msg_id = 5; // 消息ID
    string sender = 6; //发送者
}
message GetStoryBookHistoryResp {
    repeated StoryMsg msg_list = 1;
    uint32 size = 2;
}

message GetStoryBookReplyReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string story_id = 4;
    string chapter_id = 5;

}

message GetStoryBookReplyResp {
    string chapter_id = 1;
    string scene_id = 2;
    string node_id = 3;
    string avatar = 4;
    string name = 5;

    message ReplyOption {
        string id = 1;
        string content = 2;
    }
    repeated ReplyOption reply_options = 6;
    bool enable_custom_reply = 7; // 是否启用自定义回复
    string custom_content_id = 8; // 自定义回复的content id 字段
    bool is_send_directly = 9; // 是否直接发送
}

message GetStoryBookProgressReq {
    uint32 uid = 1;
    uint32 role_id = 3;
    string story_id = 4;
    string chapter_id = 5;
}

message GetStoryBookProgressResp {
    string id = 1;
    string title = 2;
    uint32 status = 3;
    string chapter_id = 4; // 当前进行的章节Id
    string chapter_name = 5; // 当前进行的章节名称
    uint32 chapter_status = 6; // 当前进行的章节状态
    string scene_id = 7; // 当前进行的场景Id
    string scene = 8; // 当前进行的场景
    string background_url = 9; // 当前进行的背景图
    string background_music_url = 10; // 当前进行的背景音乐
    uint32 style = 11; // 故事书样式
}

message StartStoryBookReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string story_id = 4;
    string chapter_id = 5;
}

message StartStoryBookResp {
    bool ok = 1;
}

message MarkStoryBookNotifySeqReq {
    uint32 uid = 1;
    uint32 role_id = 3;
    uint32 mark_seq = 4;
}

message MarkStoryBookNotifySeqResp {
    bool ok = 1;
}

message GetStoryBookNotifySeqReq {
    uint32 uid = 1;
    uint32 role_id = 3;
}

message GetStoryBookNotifySeqResp {
    uint32 last_seq = 1;
    uint32 show_type = 2; // 1 新故事， 2 解锁新章节，3 解锁新故事
    uint32 last_read_seq = 3;
}

message GetStoryBookReq {
    uint32 uid = 1;
    uint32 role_id = 3;
    string story_id = 4;
}

message GetStoryBookResp {
    StoryBook story = 1;
}

message GetStoryBookListReq {
    uint32 uid = 1;
    uint32 role_id = 3;
}

message GetStoryBookListResp {
    repeated StoryBook stories = 1;
    uint32 total = 2;
}

message StoryBook {
    string id = 1;
    string cover_image = 2; // 故事封面
    string title = 3; // 故事标题
    string subtitle = 4; // 故事副标题
    uint32 status = 5; //故事状态 0:未解锁, 1:已解锁, 2:运行中, 3:已完成
    repeated StoryUnlockCond unlock_condition = 6;// 解锁条件
    repeated StoryChapter chapters = 7;
    int64 update_ts = 8;
    uint32 style = 9; // 故事书样式
}

message StoryChapter {
    string id = 1; // 章节id
    string name = 2; // 章节名称
    uint32 status = 3; // 故事章节状态 0:未解锁, 1:已解锁, 2:运行中, 3:已完成
    repeated StoryUnlockCond unlock_condition = 4;
}

message StoryUnlockCond {
    int64  val = 1;
    uint32 type = 2;
}

message StartGameReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string game_id = 4;
}

message StartGameResp {
    string ctx_id = 1;
}

message FormatGptAnswerReq {
    string gpt_a = 1;
    uint32 role_id = 2;
}

message FormatGptAnswerResp {
   repeated rcmd.partner_common.FormatMsg msg_list = 1;
}

message GetGPTReqInfoReq {
    uint32 uid = 1; // 预埋uid，方便以后做prompt的AB
    uint32 role_id = 2;
}

message GetGPTReqInfoResp {
    string placeholder_str = 1; // 占位符, map<string, string> 的json字符串
    string prompt_id = 2;
    string prompt_version = 3;
}

message BatchGetCharactersReq {
    uint32 uid = 1;
    repeated uint32 ids = 2;
}

message GameCharacter {
    uint32 uid = 1;
    uint32 partner_id = 2;
    int64 create_time = 3;
    uint32 user_msg_count = 4;//用户发送消息数
    uint32 role_id = 5;
    bytes role_extra = 6;
    repeated CustomVoice custom_voices = 7; // 自定义语音
}

message BatchGetCharactersResp {
    repeated GameCharacter game_characters = 1;
}

message GetUserInfoReq{
  uint32 uid = 1;
}
message GetUserInfoResp{
  uint32 user_today_msg_count = 1;//当天用户发送消息数
  uint32 user_total_msg_count = 2;//  用户发送消息总数
}
message ReplyTextFormatReq {
    string text = 1;
    FormatType format_type = 2;
    enum FormatType {
        FormatType_Default = 0;
        FormatType_TextProcDefault = 1;
        FormatType_RemoveBracket = 2;
        FormatType_TextProcV1 = 3;
        FormatType_LongReplyDetect = 4;
    }
}

message ReplyTextFormatResp {
    repeated string texts = 1;
    repeated int64 delay_times = 2;
    string data = 3;
}

message DeleteCharacterReq {
  uint32 uid = 1; // 请求用户id
  uint32 ai_partner_id = 2; // 对方id
}

message DeleteCharacterResp {}

message CustomVoice {
    string id = 1;
    float weight = 2;// 权重，0.0-1.0
}

message SetCharacterInfoReq {
  uint32 uid = 1; // 请求用户id
  uint32 ai_partner_id = 2; // 对方id
  AIRole role = 3; // 角色特征
  uint32 source = 4; // 创建来源 参考business_ai_partner.proto PartnerSource
  string name = 5; // AI玩伴名称
  string call_name = 6; // AI玩伴对用户称呼
  uint32 role_type = 7; // 角色类型 参考business_ai_partner.proto RoleType
  repeated CustomVoice custom_voices = 8; // 自定义语音
}

message AIRole {
    uint32 id = 1;
    // AI风格
    string style = 2;
    // AI性别 0:女 1:男
    int32 sex = 3;
}

message SetCharacterInfoResp {
}

message ReceiveMsgFromUserReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    enum MsgType {
        MsgType_Unknown = 0;
        // 文本
        MsgType_Text = 1;
        // 表情
        MsgType_Emoticon = 3;
        MsgType_Ext = 6; // 扩展消息
    }

    // 消息类型
    MsgType msg_type = 3;
    // 消息内容
    string content = 4;
    bytes ext = 5;
    string msg_id = 6;// 消息ID
}

message ReceiveMsgFromUserResp {
}

message UserEnterChattingNotifyReq {
    uint32 uid = 1;
    uint32 ai_partner_id = 2;
}

message UserEnterChattingNotifyResp {}

message TestReq {
    uint32 uid = 1;
    uint32 ai_partner_id = 2;
    enum Action {
      Action_Unknown = 0;
      Action_Timer = 1;//触发定时任务
      Action_Greeting = 2;//指定uid推送
      Action_RoleCreatorNotify = 3; // 触发角色创建者触达逻辑
      Action_IncRoleChattedCount = 4; // 被聊角色 被聊次数 递增
      Action_RoleLikeNotifyPush = 5; // 触发角色点赞引导推送, 必填 uid, ai_partner_id, role_id
      Action_PetMsg = 6; // 触发桌宠消息
      Action_SelfPushPredict = 7; // 主动推送分数预测
    }
    Action action = 3;
    int64 role_creator_push_day_ts = 4; // action=3时存在， 指定 角色创建者触达检测任务触发的时间
    uint32 role_creator_min_chatted_count = 5; // action=3时存在，指定 角色创建者的角色被聊次数

    uint32 role_id = 6; // action=4时存在，被聊角色
    uint32 role_creator_uid = 7;  //action=4时存在， 被聊角色的创建者uid
    uint32 chatted_uid = 8; // action=4时存在，给角色发送消息的用户uid
    int64 chat_ts = 9;// action=4时存在，发送消息的时间
    string asr_text = 10; // action=6时存在，用户发送的ASR文本
}

message TestResp {
    string data = 1;
}

message GenRecommendReplyReq{
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;

}
message GenRecommendReplyResp{
    repeated string reply_list = 1;
}

message ShowPartnerSilentReq{
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;

}
message ShowPartnerSilentRsp{
    bool show = 1;
}

message ShowContinueChatReq{
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string req_dev_id = 4;
}
message ShowContinueChatRsp{
    bool show = 1;
    uint32 chat_left_num = 2;  // 聊天剩余句数
    uint32 continue_round_left_num = 3; // 继续说一轮剩余次数
    uint32 continue_total_left_num = 4; // 继续说总剩余次数
}

message ContinueChatReq{
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;

}
message ContinueChatRsp{
}

message TestRoleSendMsgReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string msg = 4;
}

message TestRoleSendMsgRsp {
}

message GetTarotConfigReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string req_dev_id = 4;
}

message TarotCard {
    uint32 id = 1;
    string name = 2;
    string picture = 3;
    string text = 4;
    string calendar_icon = 5;
}

message GetTarotConfigRsp {
    repeated TarotCard card_list = 1;
    uint64 server_ts = 2;
}

message OpenTarotCardReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string req_dev_id = 4;
    string date = 5;
}

message OpenTarotCardRsp {
    TarotResult result = 1;
    bool first_open = 2;
}

message GetTarotResultReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string req_dev_id = 4;
    string month = 5;
    string day = 6;
}

message TarotResult {
    string date = 1;
    uint32 id = 2;
    string open_time = 3;
}

message GetTarotResultRsp {
    repeated TarotResult result_list = 1;
}

message GetPetOwnDayReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string req_dev_id = 4;
}

message GetPetOwnDayRsp {
    uint32 own_day = 1;
}

message TestPetSendMsgReq {
    uint32 uid = 1;
    uint32 msgtype = 2;
    string bubble_msg = 3;
    string display_msg = 4;
    string title = 5;
}

message TestPetSendMsgRsp {

}

message GetPetStoryNumReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string req_dev_id = 4;
}

message GetPetStoryNumRsp {
    uint32 total = 1;
    uint32 left = 2;
}

message PetStory {
    string story_id = 1;
    repeated StoryPart story_parts = 2;
}

message StoryPart {
    uint32 part_num = 1;
    string text = 2;
    string audio = 3;
    uint32 seconds = 4;
}

message PushPetStoryPart {
    string story_id = 1; // 故事ID
    repeated StoryPart story_parts = 2; // 段落列表
    bool is_last_one = 3;   // 是否最后一个
}

message StartPetStoryReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string req_dev_id = 4;
    string story_id = 5;
}

message StartPetStoryRsp {
    string story_id = 1;
}

message StopPetStoryReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string req_dev_id = 4;
    string story_id = 5;
}

message StopPetStoryRsp {
}

message EnterPetSleepTalkReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string req_dev_id = 4;
}

message EnterPetSleepTalkRsp {

}

message GetVibesConfigReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string req_dev_id = 4;
    uint32 vibes_type = 5;
}

message VibesConfig {
    uint32 id = 1;
    string description = 2;
    string small_url = 3;
    string image_url = 4;
    string resource_type = 5;
    string resource_url = 6;
    string audio_url = 7;
}

message GetVibesConfigRsp {
    repeated VibesConfig vibes_list = 1;
}

message AddAccompanyTimeReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string req_dev_id = 4;
    uint32 seconds = 5;
}

message AddAccompanyTimeRsp {
    uint32 total_seconds = 1;
}

message GetAccompanyTimeReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string req_dev_id = 4;
    uint32 seconds = 5;
}

message GetAccompanyTimeRsp {
    uint32 total_seconds = 1;
}

enum TargetRewardType {
    TARGET_REWARD_TYPE_NONE = 0;
    TARGET_REWARD_TYPE_CHAT = 1;  // 聊天次数
}

enum TargetToDo {
    TARGET_TODO_NONE = 0;
    TARGET_TODO_CHAt = 1;  // 聊天
}

enum ComputeMethod {
    COMPUTE_METHOD_NONE = 0;
    COMPUTE_METHOD_NUM_ADD = 1;  // 次数累计
    COMPUTE_METHOD_DAY_ADD = 2;  // 天数累计
    COMPUTE_METHOD_DAY_CONTINUE = 3;  // 连续天数
    COMPUTE_METHOD_RECV_MATCH = 4;  // 收到匹配
    COMPUTE_METHOD_RECV_CONTAIN = 5;  // 收到包含
    COMPUTE_METHOD_SEND_MATCH = 6;  // 发送匹配
    COMPUTE_METHOD_SEND_CONTAIN = 7;  // 发送包含
}

enum TargetTrigger {
    TARGET_TRIGGER_NONE = 0;
    TARGET_TRIGGER_IN_CHAT = 1;  // 进入聊天
    TARGET_TRIGGER_CHAT_ONE = 2;  // 聊天1句
}

message TargetInfo {
    Target target = 1;  // 目标
    TargetComplete complete = 2;  // 目标完成情况
}

message Target {
    string id = 1;   // 目标ID
    int32 type = 2;   // 目标类型 TargetType
    uint32 sub_id = 3;   // 目标子ID
    uint32 order = 4;   // 目标顺序
    string title = 5;   // 目标标题
    string subtitle = 6; // 目标副标题
    int32 reward_type = 7;  // 目标奖励类型 TargetRewardType
    uint32 reward_num = 8;  // 目标奖励数量
    string description = 9;   // 目标描述 例子：完成10句聊天
    int32 todo = 10;   // 目标行动 TargetToDo
    int32 compute_method = 11;   // 统计方式 ComputeMethod
    uint32 role_id = 12;   // 角色ID
    uint32 complete_do_value = 13;   // 目标完成对应的值
    string param = 14;   // 参数 根据统计方式来填 TargetCompleteParam的json字符串
    string start_time = 15;   // 目标开始时间
    string end_time = 16;   // 目标结束时间
    string icon = 17;   // 目标图标
    int32 trigger = 18;   // 目标触发 TargetTrigger
}

message TargetCompleteParam {
    repeated string match_any = 1;   // 匹配任意字符串
    repeated string contain_any = 2;   // 包含任意字符串
}

enum TargetType {
    TARGET_TYPE_NONE = 0;
    TARGET_TYPE_SYSTEM = 1;  // 系统目标
    TARGET_TYPE_CHAT = 2;  // 角色聊天目标
}

message GetTargetListReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    int32 target_type = 4; // 目标类型 TargetType
    string req_dev_id = 5;
}

message TargetComplete {
    string target_id = 1;   // 目标ID
    uint32 do_value = 2;   // 完成值
    bool is_complete = 3;   // 是否完成
    int64 complete_time = 4;   // 完成时间
    bool is_get_reward = 5;   // 是否领取奖励
    int64 get_reward_time = 6;   // 领取奖励时间
}

message GetTargetListRsp {
    repeated TargetInfo target_infos = 1;   // 目标情况列表
}

message GetTargetRewardReq {
    uint32 uid = 1;
    string target_id = 2;   // 目标ID
}

message GetTargetRewardRsp {

}

message GetChatNumReq {
    uint32 uid = 1;
}

message GetChatNumRsp {
    uint32 conf_use_num = 1;     // 配置次数使用数
    uint32 conf_total_num = 2;   // 配置次数
    uint32 extra_remaining_num = 3;  // 额外剩余次数
}

message CheckAIRoomVisibilityReq {
    uint32 uid = 1;
    uint32 role_id = 2;
}

message SetUserChatSceneReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    string chat_scene = 3;
}

message SetUserChatSceneResp {
    bool scene_effect = 1;
}

message CheckChatSceneVisibilityReq {
    uint32 uid = 1;
    uint32 role_id = 2;
}

message CheckAIRoomVisibilityResp {
    bool can_display = 1;  // 该角色是否展示AI房间入口
}

message CheckChatSceneVisibilityResp {
    bool can_display = 1;  // 是否可以展示该角色
}

message CheckIfTriggerChatReq {
  uint32 uid = 1;
};
message CheckIfTriggerChatResp {
  bool is_trigger = 1;  // false: 无需触发，true：需触发
}

message GetNotOpenedRolesReq {
  uint32 uid = 1;
  repeated uint32 role_list = 2;
};
message GetNotOpenedRolesResp {
  repeated uint32 role_list = 1; // 角色 id
};

message ChatRecord {
  string content = 1;
  string chat_role = 2;  // "assistant" or "user"
}
message ChatHistoryAppendReq {
  uint32 uid = 1;
  uint32 partner_id= 2;
  repeated ChatRecord chat_records = 3;
}
message ChatHistoryAppendResp {
  bool res = 1;
  string message = 2;
}

message ReadPartnerHeartReq {
  uint32 uid = 1;  // 用户 id
  uint32 partner_id = 2;  // 角色实例 id
  uint32 role_id = 3;  // 角色模板 id
  string content = 4;  // 需要读心的内容
  string msg_id = 5;  // 消息 id
}

message ReadPartnerHeartResp {
  string msg_id = 1;  // 消息 id
  string text = 2;  // 读心结果
}