syntax = "proto3";

import "persona/options.proto";
import "common/common.proto";

package rcmd.persona.tt.channel;

option go_package = "golang.52tt.com/protocol/services/rcmd/persona/tt/channel";

option java_package = "com.tt.protocol.services.rcmd.persona.tt.channel";
option java_outer_classname = "ChannelProto";

message BlockOption {
    uint32 block_id = 1;           //块id
    uint32 elem_id = 2;            //块里面的元素id
    string block_title = 3; // 块名称
    string element_title = 4;    // 选项值
    BlockMode mode = 5; // // 0: 单选, 1: 多选, 2：输入框
}

enum BlockMode {
    single = 0;
    multi  = 1;
    user_input = 2;
}

//房间基本信息
message ChannelBasic {
    option (persona.profileId) = 10100;
    option (persona.version) = 1;
    option (persona.app) = "channel_basic";
    option (rcmd.persona.process) = "hard_view";
    option (rcmd.persona.process) = "index_es|*********,channel_index_es_view";
    option (rcmd.persona.process) = "index_es|150201200,mini_game_channel_index_es_view";

    //为了兼容开黑和音乐，额外开的音乐房类型字段
    enum MusicChannelType{
        UnknownMusicChannelType = 0;//一般是开黑的房间
        UnknownButNotKaiheiChannel = 1;//不明确房间类型，但不是开黑。用于临时兼容两个版本 ，一般指音乐
        LiveChannel = 2; // 直播房
    }

    // 密室逃脱类型
    enum EscapeType{
        UnknownType = 0;    //未知类型
        QingGuan = 1;       //情棺
        Other = 20;         //其他，预留19个类型，如果需要枚举，在other前面定义即可
    }

    uint32 uid                         = 1;
    uint32 channel_id                  = 2;  //房间id
    uint32 is_alive                    = 3;  //房间是否开播
    uint32 tag_id                      = 4;  //房间标签id
    string tag_name                    = 5;  //房间标签名

    string tag_4_opt1                  = 6;  //标签-王者/和平区服
    string tag_4_opt2                  = 7;  //标签-王者/和平段位
    string tag_4_opt3                  = 8;  //标签-王者/和平模式
    string tag_4_opt4                  = 9;  //标签-王者/和平排位
    string channel_name                = 10; //房间口号

    uint32 push_time                   = 11; //推荐房间发布时间
    repeated BlockOption block_options = 12[(rcmd.persona.type)="list"]; //游戏选项
    bool want_fresh                    = 13; // 萌新标识
    rcmd.common.LocationInfo loc       = 14;
    uint32 channel_type                = 15; // 召回池类型， 小游戏方/开黑房/扩列房

    uint32 channel_game_status         = 16; // 游戏开局状态, 0: 未开局, 1: 开局

    uint32 companion_type              = 17; // 陪玩类型, 0: 正常房间, 1: lol手游陪玩房间
	  uint32 sex						   = 18; // 房主性别 0: 女 1：男

    uint32 music_channel_type = 19;     //use MusicChannelType

	  uint32 ucus_tag_id = 20;	//你行你唱专区tagID
	  string ucus_tag_name = 21;	//你行你唱专区tagName
	  repeated string blockId_elemId = 22; //blockId_elemId
	  uint32 category_id = 23; // 房间所属类目id
    repeated uint32 unlimited_block_ids      = 24; // 不限或者全选的block id
    uint32 playmate_intention  = 25; // 默认陪玩意图：0 正常， 1 陪玩（规则识别结果）
    double music_title_feature  = 26; //音乐房间标题热门特征值
    uint32 model_playmate_intention = 27; // todo 废弃, 陪玩意图识别：0 正常， 1 陪玩（规则+模型识别结果）
    bool ignore_game_status = 28; //是否忽略游戏开局状态
    repeated uint32 game_player_limits = 30; //限制人数, 即达到可以开局的人数

    message PlaymateResult {
        string ab_val = 1; // ab对应字段上的值
        bool is_playmate =2; // 是否是陪玩
    }
    repeated PlaymateResult playmate_results = 32[(rcmd.persona.type)="list"]; // 所有实验的陪玩识别结果列表
	  bool is_new_user = 33; //房主是否为当天注册的新用户
	  uint32 tab_type = 34; // 房间类型: 0 普通分类, 1 游戏分类, 2 小游戏分类 3 音乐
    enum RefuseSexType {
        RefuseSexTypeUnlimited = 0;
        RefuseSexTypeMale = 1;
        RefuseSexTypeFemale = 2;
        RefuseSexTypeBoth = 3;
    }
    uint32 refuse_sex = 35; // 房主不想进房的用户性别 use RefuseSexType
    uint32 escape_type = 36; // 密室逃脱类型：0非密逃类型，1情棺，20其他
    repeated uint32 only_unlimited_block_ids = 37; // 不限block_id，不包含全选
    bool is_theme_channel = 38; // 是否是主题房
    map<string, int32> device_negative_num = 39; // 房主的设备号被X个用户反馈   - 反馈类型：贩卖色情  - 反馈类型：故意辱骂
    enum ChannelPlayMode {
        VoiceChannel = 0; // 语音房
        WordChannel = 1; // 文字房
    }
    uint32 channel_play_mode = 40; // 房间模式，use ChannelPlayMode
    uint32 buss_publish_time = 41; // 业务的发布时间
    map<uint32, string> elem_id_map = 42; // elem_id->发布条件映射
    repeated uint32 unselected_block_ids = 43; // 未选的blockIds

    message ChannelBasicUpdate {
        option(rcmd.persona.profileId) = 1010001;
        //option (rcmd.persona.process) = "index_es|*********,channel_index_es_view";
        uint32 uid                         = 1;
        uint32 channel_id                  = 2;  //房间id
        uint32 is_alive                    = 3;  //房间是否开播
        uint32 tag_id                      = 4;  //房间标签id
        string tag_name                    = 5;  //房间标签名

        string tag_4_opt1                  = 6;  //标签-王者/和平区服
        string tag_4_opt2                  = 7;  //标签-王者/和平段位
        string tag_4_opt3                  = 8;  //标签-王者/和平模式
        string tag_4_opt4                  = 9;  //标签-王者/和平排位
        string channel_name                = 10; //房间标题

        uint32 push_time                   = 11; //房间发布时间
        repeated BlockOption block_options = 12; //游戏选项
        bool want_fresh                    = 13; // 萌新标识
        rcmd.common.LocationInfo loc       = 14;
        uint32 channel_type                = 15; // 房间类型， 小游戏方/开黑房/扩列房
		    uint32 sex						   = 16; // 房主性别 0: 女 1：男
	      uint32 category_id = 17; // 房间所属类目id
        repeated uint32 unlimited_block_ids      = 18; // 不限或者全选的block id
        uint32 playmate_intention  = 19; // 默认陪玩意图：0 正常， 1 陪玩（规则识别结果）

        repeated string blockId_elemId = 20; //blockId_elemId
        uint32 model_playmate_intention = 21; // todo 废弃字段，陪玩意图识别：0 正常， 1 陪玩（规则+模型识别结果）
        bool ignore_game_status = 22;
        repeated PlaymateResult playmate_results = 24; // 所有实验的陪玩识别结果列表
	      uint32 tab_type = 25; // 房间类型: 0 普通分类, 1 游戏分类, 2 小游戏分类 3 音乐
        uint32 refuse_sex = 26; // 房主不想进房的用户性别  use RefuseSexType
        uint32 escape_type = 27; // 密室逃脱类型：0非密逃类型，1情棺，20其他
        repeated uint32 only_unlimited_block_ids = 28; // 不限block_id，不包含全选
        bool is_theme_channel = 29; // 是否是主题房
        map<string, int32> device_negative_num = 30; // 房主的设备号被X个用户反馈   - 反馈类型：贩卖色情  - 反馈类型：故意辱骂
        uint32 channel_play_mode = 31; // 房间模式，use ChannelPlayMode
        uint32 buss_publish_time = 32; // 业务的发布时间
        map<uint32, string> elem_id_map = 33; // elem_id->发布条件映射
        repeated uint32 unselected_block_ids = 34; // 未选的blockIds
    }

    message ChannelGameStatusUpdate {
        option(rcmd.persona.profileId) = 1010002;
        uint32 channel_game_status = 1;
        repeated uint32 game_player_limits = 2; //限制人数, 即达到可以开局的人数
    }

    message ChannelCompanionTypeUpdate {
        option(rcmd.persona.profileId) = 1010003;
        uint32 companion_type = 1; // 陪玩类型, 0: 正常房间, 1: lol手游陪玩房间
    }

    message ChannelMusicChannelUpdate{
        option(rcmd.persona.profileId) = 1010004;
        option (rcmd.persona.process) = "index_es|*********,channel_index_es_view";
        uint32 uid                         = 1;
        uint32 channel_id                  = 2;  //房间id
        uint32 tag_id                      = 4;  //房间标签id
        uint32 push_time                   = 5; //房间发布时间
        repeated BlockOption block_options = 6; //游戏选项
        rcmd.common.LocationInfo loc       = 7;
		    uint32 sex						   = 8; // 房主性别 0: 女 1：男
        uint32 music_channel_type  = 9;
		    repeated string blockId_elemId = 10; //blockId_elemId
        double music_title_feature  = 11; //音乐房间标题热门特征值
		bool is_new_user = 12; //房主是否为当天注册的新用户
    }

	message UcusTagUpdate{
        option(rcmd.persona.profileId) = 1010005;
        option (rcmd.persona.process) = "index_es|*********,channel_index_es_view";
		uint32 ucus_tag_id = 1;	//你行你唱专区tagID
		string ucus_tag_name = 2;	//你行你唱专区tagName
    }

    message SimpleBasic {
        option(rcmd.persona.profileId) = 1010006;
        uint32 tag_id                      = 1;  //房间标签id
        uint32 uid                         = 2;
        repeated BlockOption block_options = 3; //游戏选项
    }

    message UpdateChannelName {
        option(rcmd.persona.profileId) = 1010007;
        string channel_name = 1; //房间标题
    }

    message UpdatePlaymateIntention {
        option(rcmd.persona.profileId) = 1010008;
        uint32 playmate_intention  = 1;  // 默认陪玩意图：0 正常， 1 陪玩（规则识别结果）
        uint32 model_playmate_intention  = 2; // todo 废弃字段, 陪玩意图识别：0 正常， 1 陪玩（规则+模型识别结果）
        repeated PlaymateResult playmate_results = 3; // 陪玩识别结果列表
    }

}

//房间动态数据
message ChannelDynamic {
    option (persona.profileId) = 10101;
    option (persona.version) = 1;
    option (persona.app) = "channel_dynamic";
    option (rcmd.persona.process) = "hard_view";
    option (rcmd.persona.process) = "index_es|*********,channel_index_es_view";
    option (rcmd.persona.process) = "index_es|150201200,mini_game_channel_index_es_view";

    uint32 user_num = 1;         //房间人数
    uint32 enter_female_num = 2; //房间女性人数
    uint32 mic_num = 3;          //麦上人数
    uint32 mic_female_num = 4;   //在麦女性人数

    /*
    repeated uint32 enter_uid_list = 5;    //在线用户uid列表
    repeated uint32 enter_female_list = 6; //在线女性uid列表
    repeated uint32 mic_female_list = 8;   //麦上女性uid列表
    */

    uint32 last_fix_time = 9;   //非业务用，本特征修正时间

    uint32 last_enter_time = 10;       //最后一个用户进房时间（不包含房主）

    uint32 mic_age_sum = 11;      //在麦人数年龄和(未知年龄用户当0处理)
    uint32 mic_no_age_user_num = 12; //在麦未知年龄房间人数
	repeated string song_list = 13; //房间播放列表


    uint32 mic_male_num = 14;//在麦男性人数

    bool high_quality = 15; //优质房
    uint32 hq_set_time = 16; //优质房设置时间

	int32 ktv_excellent_producer_num =  17; //ktv优质生产者
	int32 ktv_social_king_num = 19;	//社交牛b症
	int32 ktv_content_cosumer_num = 20; //内容消费者
	int32 ktv_imprecise_user_num = 21; //非精准用户
	int32 ucus_excellent_producer_num =  22;  //你行你唱
	int32 ucus_social_king_num = 23;
	int32 ucus_content_cosumer_num = 24;
	int32 ucus_imprecise_user_num = 25;

    uint32 minor_user_num = 26;//房间未成年人数

    repeated PlayingItem playing_list = 27[(rcmd.persona.type)="list"]; //房间播放列表
    bool is_climax = 28; //当前演唱歌曲是否为高潮片段
    uint32 concert_song_status = 29; //麦可乐队状态

    float enter_hot = 30; //进房停留时长热度系数
    float mic_hot = 31; //麦上时长热度系数

	repeated uint32 mic_uid_list = 32[(persona.type)="set"]; //麦上用户uid列表


	message SongListUpdate {
        option(rcmd.persona.profileId) = 1010101;
        repeated string song_list = 1; // 房间播放列表更新
        repeated PlayingItem playing_list = 2; // 房间播放列表更新
    }

	message HighQualityUpdate {
        option(rcmd.persona.profileId) = 1010102;
        bool high_quality = 1; // //优质房更新
        uint32 hq_set_time = 2; //优质房设置时间更新
        bool is_climax = 3; //当前演唱歌曲是否为高潮片段
    }

    message UserEnterAppend {
        option(rcmd.persona.profileId) = 1010103;
        uint32 user_num = 1;         //房间人数
        uint32 enter_female_num = 2; //房间女性人数
        uint32 minor_user_num = 3;//房间未成年人数
        uint32 last_enter_time = 4;
    }

    message OnMicAppend {
        option(rcmd.persona.profileId) = 1010104;

        uint32 mic_num = 1;          //麦上人数
        uint32 mic_female_num = 2;   //在麦女性人数
        uint32 mic_age_sum = 3;      //在麦人数年龄和(未知年龄用户当0处理)
        uint32 mic_no_age_user_num = 4; //在麦未知年龄房间人数

        int32 ktv_excellent_producer_num =  5; //ktv优质生产者
        int32 ktv_social_king_num = 6;	//社交牛b症
        int32 ktv_content_cosumer_num = 7; //内容消费者
        int32 ktv_imprecise_user_num = 8; //非精准用户
        int32 ucus_excellent_producer_num =  9;  //你行你唱
        int32 ucus_social_king_num = 10;
        int32 ucus_content_cosumer_num = 11;
        int32 ucus_imprecise_user_num = 12;
    }

	message ConcertSongStatusUpdate {
        option(rcmd.persona.profileId) = 1010105;
        uint32 concert_song_status = 1; //麦可乐队状态更新
    }
}

message PlayingItem {
    string song_id = 1;
    bool is_climax = 2; //KTV是否为高潮片段
}

// 查询房间小队情况
message ChannelTeam {
    option (persona.profileId) = 10105;
    option (persona.version) = 1;

    uint32 owner_uid = 1;
    uint32 tab_id = 2;

    map<uint32,uint32> location2uid = 3;  //分路->uid映射
    map<uint32,uint32> uid2location = 4;  //uid->分路的映射
    uint32   lack_num = 5; //空缺位置数
}


message ChannelExpDetail {
    uint32 browse_count     = 1; //曝光次数
    uint32 click_count      = 2; //点击次数
    double click_rate       = 3; //点击率
}

message ChannelEnterDetail {
    uint32 enter_count     = 1; //进房次数
    double avg_duration    = 2; //平均时长
    double team_rate       = 3; //开黑率
    double out_rate        = 4; //退出率
    double mic_rate        = 5; //上麦率
    double sum_duration    = 6; //总时长
}

//离线统计数据
message ChannelOffline {
    option (persona.profileId) = 10102;
    option (persona.version) = 1;
    option (rcmd.persona.process) = "hard_view";

    uint32 t_enter_count      = 1;  //进房次数
    uint32 t_enter_user_count = 2;  //进房用户数
    double t_total_duration   = 3;  //总停留时长
    double t_avg_duration     = 4;  //平均停留时长
    double t_team_rate        = 5;  //开黑率
    double t_mic_rate         = 6;  //上麦率
    double t_out_rate         = 7;  //秒退率
    double t_follow_rate      = 8;  //关注率
    double t_u_avg_duration   = 9;  //平均停留时长UV
    double t_u_team_rate      = 10; //开黑率UV
    double t_u_mic_rate       = 11; //上麦率UV
    double t_u_out_rate       = 12; //秒退率UV
    double t_u_follow_rate    = 13; //关注率UV

    uint32 t_browse_count     = 14; //曝光次数
    uint32 t_click_count      = 15; //点击次数
    double t_click_rate       = 16; //点击率

    map<uint32,ChannelExpDetail> t_exp_d_sex      = 17; // 对不同性别用户的曝光点击数据
    map<uint32,ChannelExpDetail> t_exp_d_tag_id   = 18; // 对不同游戏的曝光点击数据

    map<uint32,ChannelEnterDetail> t_enter_d_sex      = 19; // 对不同性别用户的进房转化数据
    map<uint32,ChannelEnterDetail> t_enter_d_tag_id   = 20; // 对不同游戏的进房转化数据
}

message MusicChannelOffline{
    option (persona.profileId) = 110200800;
    option (persona.version) = 1;
    option (rcmd.persona.process) = "index_es|*********,channel_index_es_view";

    uint32 iu_room_music_enter_cnt_1w=1;        //'近1周音乐列表房间的用户进房数'
    uint32 i_room_music_active_cnt_1w=2;        //'近1周音乐列表房间的活跃天数'
    uint32 iu_room_music_enter_user_cnt_1w=3;   //'近1周音乐列表房间的进房用户数'
    uint32 iu_room_music_duration_1w=4;         //'近1周音乐列表房间的用户进房停留总时长'
    uint32 iu_room_music_duration_avg_1w=5;     //'近1周音乐列表房间的用户平均进房停留时长'
    uint32 iu_room_music_kaihei_cnt_1w=6;       //'近1周音乐列表房间的用户开黑次数'
    uint32 iu_room_music_kaihei_user_cnt_1w=7;  //'近1周音乐列表房间的用户开黑用户数'
    uint32 iu_room_music_expo_cnt_1w=8;         //'近1周音乐列表房间的曝光用户数'
    double iu_room_music_ctr_1w=9;              //'近1周音乐列表房间的用户进房率'
    double iu_room_music_cvr_1w=10;             //'近1周音乐列表房间的用户开黑率'

    uint32 iu_room_music_enter_cnt_3w=11;       //'近3周音乐列表房间的用户进房数'
    uint32 i_room_music_active_cnt_3w=12;       //'近3周音乐列表房间的活跃天数'
    uint32 iu_room_music_enter_user_cnt_3w=13;  //'近3周音乐列表房间的进房用户数'
    uint32 iu_room_music_duration_3w=14;        //'近3周音乐列表房间的用户进房停留总时长'
    uint32 iu_room_music_duration_avg_3w=15;    //'近3周音乐列表房间的用户平均进房停留时长'
    uint32 iu_room_music_kaihei_cnt_3w=16;      //'近3周音乐列表房间的用户开黑次数'
    uint32 iu_room_music_kaihei_user_cnt_3w=17; //'近3周音乐列表房间的用户开黑用户数'
    uint32 iu_room_music_expo_cnt_3w=18;        //'近3周音乐列表房间的用户曝光次数'
    double iu_room_music_ctr_3w=19;             //'近3周音乐列表房间的用户进房率'
    double iu_room_music_cvr_3w=20;             //'近3周音乐列表房间的用户开黑率'

    map<uint32,uint32> iu_room_music_gender_enter_user_cnt_1w=21;   //'近1周音乐列表房间不同性别的进房用户数'
    map<uint32,uint32> iu_room_music_gender_expo_cnt_1w=22;         //'近1周音乐列表房间不同性别的曝光次数'
    map<uint32,uint32> iu_room_music_gender_enter_cnt_1w=23;        //'近1周音乐列表房间不同性别的进房次数'
    map<uint32,uint32> iu_room_music_gender_kaihei_cnt_1w=24;       //'近1周音乐列表房间不同性别的开黑次数(有效停留时长)'
    map<uint32,double> iu_room_music_gender_ctr_1w=25;              //'近1周音乐列表房间不同性别的进房率'
    map<uint32,double> iu_room_music_gender_cvr_1w=26;              //'近1周音乐列表房间不同性别的开黑率'
    map<uint32,uint32> iu_room_music_gender_enter_user_cnt_3w=27;   //'近3周音乐列表房间不同性别的进房用户数'
    map<uint32,uint32> iu_room_music_gender_expo_cnt_3w=28;         //'近3周音乐列表房间不同性别的曝光次数'
    map<uint32,uint32> iu_room_music_gender_enter_cnt_3w=29;        //'近3周音乐列表房间不同性别的进房次数'
    map<uint32,uint32> iu_room_music_gender_kaihei_cnt_3w=30;       //'近3周音乐列表房间不同性别的开黑次数(有效停留时长)'
    map<uint32,double> iu_room_music_gender_ctr_3w=31;              //'近3周音乐列表房间不同性别的进房率'
    map<uint32,double> iu_room_music_gender_cvr_3w=32;              //'近3周音乐列表房间不同性别的开黑率'

    map<uint32,uint32> iu_room_music_isnew_enter_user_cnt_1w=33;    //'近1周音乐列表房间新旧用户的进房用户数'
    map<uint32,uint32> iu_room_music_isnew_expo_cnt_1w=34;          //'近1周音乐列表房间新旧用户的曝光次数'
    map<uint32,uint32> iu_room_music_isnew_enter_cnt_1w=35;         //'近1周音乐列表房间新旧用户的进房次数'
    map<uint32,uint32> iu_room_music_isnew_kaihei_cnt_1w=36;        //'近1周音乐列表房间新旧用户的开黑次数(有效停留时长)'
    map<uint32,double> iu_room_music_isnew_ctr_1w=37;               //'近1周音乐列表房间新旧用户的进房率'
    map<uint32,double> iu_room_music_isnew_cvr_1w=38;               //'近1周音乐列表房间新旧用户的开黑率'
    map<uint32,uint32> iu_room_music_isnew_enter_user_cnt_3w=39;    //'近3周音乐列表房间新旧用户的进房用户数'
    map<uint32,uint32> iu_room_music_isnew_expo_cnt_3w=40;          //'近3周音乐列表房间新旧用户的曝光次数'
    map<uint32,uint32> iu_room_music_isnew_enter_cnt_3w=41;         //'近3周音乐列表房间新旧用户的进房次数'
    map<uint32,uint32> iu_room_music_isnew_kaihei_cnt_3w=42;        //'近3周音乐列表房间新旧用户的开黑次数(有效停留时长)'
    map<uint32,double> iu_room_music_isnew_ctr_3w=43;               //'近3周音乐列表房间新旧用户的进房率'
    map<uint32,double> iu_room_music_isnew_cvr_3w=44;               //'近3周音乐列表房间新旧用户的开黑率'

    map<uint32,uint32> iu_room_music_age_group_enter_user_cnt_1w=45;    //'近1周音乐列表房间不同年龄组的进房用户数'
    map<uint32,uint32> iu_room_music_age_group_expo_cnt_1w=46;          //'近1周音乐列表房间不同年龄组的曝光次数'
    map<uint32,uint32> iu_room_music_age_group_kaihei_cnt_1w=47;        //'近1周音乐列表房间不同年龄组的进房次数'
    map<uint32,uint32> iu_room_music_age_group_enter_cnt_1w=48;         //'近1周音乐列表房间不同年龄组的开黑次数(有效停留时长)
    map<uint32,double> iu_room_music_age_group_ctr_1w=49;               //'近1周音乐列表房间不同年龄组的进房率'
    map<uint32,double> iu_room_music_age_group_cvr_1w=50;               //'近1周音乐列表房间不同年龄组的开黑率'
    map<uint32,uint32> iu_room_music_age_group_enter_user_cnt_3w=51;    //'近3周音乐列表房间不同年龄组的进房用户数'
    map<uint32,uint32> iu_room_music_age_group_expo_cnt_3w=52;          //'近3周音乐列表房间不同年龄组的曝光次数'
    map<uint32,uint32> iu_room_music_age_group_kaihei_cnt_3w=53;        //'近3周音乐列表房间不同年龄组的进房次数'
    map<uint32,uint32> iu_room_music_age_group_enter_cnt_3w=54;         //'近3周音乐列表房间不同年龄组的开黑次数(有效停留时长)'
    map<uint32,double> iu_room_music_age_group_ctr_3w=55;               //'近3周音乐列表房间不同年龄组的进房率'
    map<uint32,double> iu_room_music_age_group_cvr_3w=56;               //'近3周音乐列表房间不同年龄组的开黑率'

    double iu_room_music_duration_rate=57;               // T+7房间次均停留时长数据高到低排序 当前房间排序位置[0,1]
}

message MusicChannelInnerOffline{
    option (persona.profileId) = 110203000;
    option (persona.version) = 1;
    option (persona.app) = "music_channel_inner_offline";
    // option (rcmd.persona.process) = "hard_view";  // 迁移到features-collect
    option (rcmd.persona.process) = "index_es|*********,channel_index_es_view";

    float i_enter_cnt_28d = 1;
    float i_mic_cnt_28d = 2;
    float i_fastquit_cnt_28d = 3;
    float i_gangup_cnt_28d = 4;
    float i_mic_gangup_cnt_28d = 5;
    float i_send_msg_cnt_28d = 6;
    float i_follow_cnt_28d = 7;
    float i_collect_cnt_28d = 8;
    float i_gift_cnt_28d = 9;
    int32 i_fastquit_level_28d = 10; // 1~5表示房间的速退率,越小越好
}

//房间战歌播放偏好
message ChannelWarsongOffline{
    option (persona.profileId) = 110201900;
    option (persona.version) = 1;
    // option (rcmd.persona.process) = "hard_view";  // 迁移到features-collect

    double i_warsong_play_duration_1w = 1; //近1周房间播放战歌时长（s）
    double i_warsong_play_duration_3w = 2; //近3周房间播放战歌时长（s）
    double i_warsong_play_song_cnt_1w = 3; //近1周房间播放战歌歌曲数
    double i_warsong_play_song_cnt_3w = 4; //近3周房间播放战歌歌曲数
}

message Nothing{
   uint32 nothing = 1;
}

message ChannelTabIdDetail {
    double click_rate       = 1; //点击率
    double team_rate       = 2; // 开黑率
	uint32 exp_cnt         = 3; //曝光次数
}

message GameSimilar {
    map<string,double> game_similar =1; // 与其他游戏的相似度, key为游戏id
}

message ChannelTabId {
    option (persona.profileId) = 10104;
    option (persona.version) = 1;

    map<uint32,ChannelTabIdDetail> tag_id_trans   = 1; // 不同游戏id的点击率、开黑率转化
    map<string,GameSimilar> game_similar_score   = 2; // 不同游戏与其他游戏的相似性, key为游戏id
}


//message ChannelRealTrans1 {
//    option (persona.profileId) = 10105;
//    option (persona.version) = 1;
//
//    map<uint32,ChannelEnterDetail> last_1h      = 1; // 近1小时的房间转化数据
//    map<uint32,ChannelEnterDetail> last_12h      = 2; // 近12小时的房间转化数据
//}

// -- 实时特征 ----
// 近3分钟的房间转化数据
message ChannelEnterRealTransMinute3{
    option (persona.profileId) = 10106;
    option (persona.version) = 1;
    ChannelEnterRealTrans trans_detail = 1;
}

// 近10分钟的房间转化数据
message ChannelEnterRealTransMinute10{
    option (persona.profileId) = 10107;
    option (persona.version) = 1;
    ChannelEnterRealTrans trans_detail = 1;
}

// 房间特征
message ChannelEnterRealTrans{
    uint32 enter_count = 1; //进房次数
    uint32 total_duration = 2; // 总停留时长
    uint32 team_count = 3; // 开黑次数
    uint32 out_lt20s_count = 4; // 20s内退出次数(不含20s), lt=less than
}

// 直播房基本信息（开播不会重置，永久不过期）
message ChannelLive {
    option (persona.profileId) = 11035;
    option (persona.version) = 1;
    option (persona.app) = "channel_live";

    uint32 uid                         = 1; // 房间主播id
    uint32 channel_id                  = 2;  //房间id
    string tag_id                      = 3;  //房间标签id
    string tag_name                    = 4;  //房间标签名
    string level = 5;   // 房间等级
    string level_type = 6;   // 房间等级类型，1普通，2快速
    string level_change_type = 7;   // 房间录入类型，1运营后台录入，2系统自动
    string bind_type = 8;   // 房间绑定模式
    uint32 create_time = 9;   // 房间创建时间
    uint32 broadcast_status = 10;   // 官频转播状态
    uint32 channel_display_id = 11;   // 官频转播展示ID
    uint32 broadcast_start_time = 12;   // 官频转播开始时间
    uint32 broadcast_end_time = 13;   // 官频转播结束时间
    message ChannelLiveTag{
        option(rcmd.persona.profileId) = 1103501;
        string tag_id                      = 1;  //房间标签id
        string tag_name                    = 2;  //房间标签名
    }

    message ChannelLiveLevel{
        option(rcmd.persona.profileId) = 1103502;
        string level = 1;   // 房间等级
        string level_type = 2;   // 房间等级类型，1普通，2快速
        string level_change_type = 3;   // 房间录入类型，1运营后台录入，2系统自动
        string bind_type = 4;   // 房间绑定模式
    }
    // 事件在官频转播开始时上报，并带上计划的结束时间
    message ChannelLiveBroadcast{
        option(rcmd.persona.profileId) = 1103503;
        uint32 channel_display_id = 1;   // 官频转播展示ID
        uint32 broadcast_start_time = 2;   // 官频转播开始时间
        uint32 broadcast_end_time= 3;   // 官频转播结束时间
    }
}
// 直播房动态信息(每次开播会重置)
message ChannelLiveDynamic {
    option (persona.profileId) = 11042;
    option (persona.version) = 1;
    option (persona.app) = "channel_live_dynamic";

    uint32 live_start_time = 1;   // 开播时间，房间处于未关播状态时才有效
    uint32 live_status  = 2;  //开播状态
    uint32 live_pk_status  = 3;  //PK状态
    int32 enter_male_num = 4;         // 进房男性人数
    int32 enter_female_num = 5; // 进房女性人数
    int32 mic_male_num = 6;          // 麦上男性人数
    int32 mic_female_num = 7;   // 在麦女性人数


    message ChannelLiveStartTime{
        option(rcmd.persona.profileId) = 1104201;
        uint32 live_start_time = 1;   // 开播时间，房间处于未关播状态时才有效
    }
    // 局部更新不能使用枚举
    message ChannelLiveStatus{
        option(rcmd.persona.profileId) = 1104202;
        uint32 live_status  = 1;  //开播状态
    }
    message ChannelLivePKStatus{
        option(rcmd.persona.profileId) = 1104203;
        uint32 live_pk_status  = 1;  //PK状态
    }
    message ChannelLiveEnterMale{
        option(rcmd.persona.profileId) = 1104204;
        int32 enter_male_num = 1 [(rcmd.persona.oper) = "incrby"]; // 进房男性人数
    }
    message ChannelLiveEnterFemale{
        option(rcmd.persona.profileId) = 1104205;
        int32 enter_female_num = 1 [(rcmd.persona.oper) = "incrby"]; // 进房女性人数
    }
    message ChannelLiveMicMale{
        option(rcmd.persona.profileId) = 1104206;
        int32 mic_male_num = 1 [(rcmd.persona.oper) = "incrby"];          // 麦上男性人数
    }
    message ChannelLiveMicFemale{
        option(rcmd.persona.profileId) = 1104207;
        int32 mic_female_num = 1 [(rcmd.persona.oper) = "incrby"];   // 在麦女性人数
    }

}
// 直播房房间侧离线画像
message ChannelLiveRoomOffline{
    option (persona.profileId) = 11038;
    option (persona.version) = 1;
    option (persona.app) = "channel_live_room_offline";

    uint32 i_p_live_login_cnt_14d = 1;
    uint32 i_p_live_enter_cnt_14d = 2;
    uint32 i_p_live_enter_user_cnt_14d = 3;
    uint32 i_p_live_total_duration_14d = 4;
    uint32 i_p_live_follow_user_cnt_14d = 5;
    uint32 i_p_live_fans_user_cnt_14d = 6;
    uint32 i_p_live_gift_user_cnt_14d = 7;
    uint32 i_p_live_gift_cnt_14d = 8;
    double i_p_live_gift_amt_14d = 9;
    double i_p_live_gift_max_amt_14d = 10;
    uint32 i_p_live_enter_cnt2_14d = 11;
    uint32 i_p_live_enter_user_cnt2_14d = 12;
    uint32 i_p_live_total_duration2_14d = 13;
    uint32 i_p_live_follow_user_cnt2_14d= 14;
    uint32 i_p_live_fans_user_cnt2_14d = 15;
    uint32 i_p_live_gift_user_cnt2_14d = 16;
    uint32 i_p_live_gift_cnt2_14d = 17;
    double i_p_live_gift_amt2_14d = 18;
    double i_p_live_gift_max_amt2_14d = 19;
    map<uint32, double> i_p_live_gender_pref_14d = 20;
    map<uint32, double> i_p_live_age_group_pref_14d = 21;
    map<uint32, string> i_p_live_trans_gender_pref_14d = 22;
    map<uint32, string> i_p_live_trans_age_group_pref_14d = 23;
    map<uint32, string> i_p_live_trans_new_user_pref_14d = 24;
}

message RoomEmbeddingVec {
    option (persona.profileId) = 10201;
    option (persona.version) = 1;
    option (persona.app) = "room_embedding";
    repeated float embs = 1;
}

message TokenizeResultV1 {
    option (persona.profileId) = 10202;
    option (persona.version) = 1;
    option (persona.app) = "room_tokenizev1";
    option (rcmd.persona.process) = "hard_view";

    uint32 code              = 1;
    repeated string tag      = 2;
    repeated string cut_word = 3;
}

message TokenizeResultV2 {
    option (persona.profileId) = 10203;
    option (persona.version) = 1;
    option (persona.app) = "room_tokenizev2";
    option (rcmd.persona.process) = "hard_view";

    uint32 code              = 1;
    repeated string tag      = 2;
    repeated string cut_word = 3;
}


// Deprecated
message TokenizeResultV3 {
    option (persona.profileId) = 110200500;
    option (persona.version) = 1;
    option (persona.app) = "room_tokenizev3";
    option (rcmd.persona.process) = "hard_view";

    repeated string tag      = 1;
}


message KeywordResult {
    option (persona.profileId) = 110200600;
    option (persona.version) = 1;
    option (persona.app) = "room_keyword";
    option (rcmd.persona.process) = "hard_view";

    uint32 channelId = 1;
	string channelName = 2;
    repeated string keyword = 3;
}

message TokenizeResultV4 {
    option (persona.profileId) = 110201000;
    option (persona.version) = 1;
    option (persona.app) = "room_tokenizev4";
    option (rcmd.persona.process) = "hard_view";

    repeated string tags      = 1;
}


message ChannelTitleTokenize {
    option (persona.profileId) = 110201200;
    option (persona.version) = 1;
    option (persona.app) = "channel_title_tokenize";

    repeated string original_tags      = 1; // 原始房间类型tags
    repeated string tags      = 2; // 房间类型的tags
    repeated string original_common_tags = 3; // 原始通用标签
    repeated string common_tags = 4; // 通用标签
    repeated string original_all_tags = 5; // 原始所有标签
    repeated string all_tags = 6; // 房间类型tags

}
// Deprecated: use ChannelTokenizeInOne replace
message ChannelPublishTokenize {
    option (persona.profileId) = 110202400;
    option (persona.version) = 1;
    option (persona.app) = "channel_publish_tokenize";

    repeated string original_tags = 1; // 原始房间类型tags
}

message ChannelTokenizeInOne {
    option (persona.profileId) = 110202500;
    option (persona.version) = 1;
    option (persona.app) = "channel_tokenize_in_one";

    string original_title_tags      = 1; // 原始房间标题tags,","做分隔符
    string original_publish_tags    = 2; // 原始房间发布条件tags,","做分隔符
    string game_level = 3;  // 游戏段位
    string service_area = 4; // 区服
    string game_branch = 5; // 分路
    repeated string common_title_tags = 6; // 通用标题标签
    string game_mode = 7; // 模式
    string game_player_count = 8; //游戏人数
    string game_topic = 9; // 主题
    string game_map = 10; // 地图
    string other = 11; // 其它


    message TitleUpdate {
        option(rcmd.persona.profileId) = 110202501;
        string original_title_tags = 1;
        repeated string common_title_tags = 2;
    }

    message PublishUpdate {
        option(rcmd.persona.profileId) = 110202502;
        string original_publish_tags = 1;
    }

    message SinglePublishUpdate {
        option(rcmd.persona.profileId) = 110202503;
        string game_level = 1;  // 游戏段位
        string service_area = 2; // 区服
        string game_branch = 3; // 分路
        string game_mode = 4; // 模式
        string game_player_count = 5; //游戏人数
        string game_topic = 6; // 主题
        string game_map = 7; // 地图
        string other = 8; // 其它
    }
}

// 接唱房基本画像
message ChannelSingBasic{
    option(persona.app) = "channel_sing_basic";
    option (persona.profileId) = 110200100;
    option (persona.version) = 1;
    uint32 channel_id = 1;
    uint32 tag_id = 2;
    enum Status {
        StatusNormal = 0;
        StatusDismiss = 1; // 房间已注销
    }
    uint32 status = 3;
    enum GameStatus {
        GameStatusNoStart = 0; // 游戏未开局
        GameStatusStart = 1; // 游戏已开局
    }
    uint32 game_status = 4;
    message SingGameStatusUpdate {
        option(rcmd.persona.profileId) = 110200101;
        uint32 game_status = 1;
    }
    uint32 num_limit = 5; // 房间满员人数
    repeated uint32 occupy_uid_list = 6; // 房间占位用户数
    message ChannelOccupyUpdate {
        option(rcmd.persona.profileId) = 110200102;
        repeated uint32 occupy_uid_list = 1; // 房间占位用户数
    }
    int32 room_age = 7; // 房间年龄(取空房时首次进房用户年龄)
    message SingBasicRoomAgeUpdate {
    option(rcmd.persona.profileId) = 110200103;
          int32 room_age = 1;
    }
}

// 直播房预估请求数，算法组定期写入，例如写入将来7天的，每天一个画像(key为yymmdd格式的日期，要补0，例如20220105)，
// 每个画像包含这天每小时的预估请求数
message ChannelEnterCountPrediction{
  option (persona.profileId) = 120200200;
  option (persona.version) = 1;
  option (persona.app) = "channel_enter_count_prediction";
  repeated uint32 hour_count=1; //hour_count是数组，长度为24，下标0的值代表0时的预估请求数，以此类推。
  double decr_factor=2;// 当前房间进房速率>预估进房速率 分数衰减因子
  double incr_factor=3;//当前房间进房速率<预估进房速率 分数提升因子
}

// 记录最近5次进房推荐的时间t1-t5
message ChannelRecommendItem{
  uint32 uid = 1;
  uint32 enter_time=2;
}
message ChannelRecommendList{
  option (persona.profileId) = 120200300;
  option (persona.version) = 1;
  option (persona.app) = "channel_recommend_list";
  repeated ChannelRecommendItem items = 1[(persona.decay_len)=5, (persona.type)="list"];
  message Append{
    option (rcmd.persona.profileId) = 120200301;
    repeated ChannelRecommendItem items = 1[(rcmd.persona.oper)="add"];
  }
}

message TopicChannelPublishItem{
    uint32  publish_sum = 1; // 房间发布条件的md5
    int64  push_time = 2; // 房间发布时间
}

//音乐房外显数据
//每类房间一个update类型
message ChannelMusicDisplay{
	option(persona.app) = "channel_music_display";
    option (persona.profileId) = *********;
    option (persona.version) = 1;

	uint32 channel_id = 1;
    string channel_name = 2;
    string channel_owner_account = 3; //房主名
    int32 channel_owner_sex = 4;
    uint32 channel_member_count = 5;
    string publish_label = 6;    // 房间标签
    string publish_region = 7;   // 分类专区

	enum HobbyChannelViewType {
	  HobbyChannelView_Default = 0; // 默认
	  HobbyChannelView_Sing_A_Song = 1; // 你行你唱
	  HobbyChannelView_KTV = 2; // 一起K歌
	  HobbyChannelView_Leisure = 3; // 挂房听歌
	  HobbyChannelView_Rap = 4; //  rap
	  HobbyChannelView_Chat = 5; //  聊天通用模板，如边唱边聊，扩列聊天
      HobbyChannelView_Band = 6;//麦克乐队
	}
    uint32 view_type = 8; // 客户端基于这个展示ui
    string icon = 9;//左上角图标
    string publish_desc = 10; //后台直接拼接分类简称与分区
    string song_title = 11; //当前歌曲
    bool high_quality = 12;//是否是优质房
    uint32 tab_id = 13;//玩法id
    uint64 region_id = 14;//blockId与elemId的拼接或者你行你唱的tagId,客户端埋点需要


	uint32 singer_count = 15; // 数量大于1代表合唱
	repeated string order_song_titles = 16; // 已点歌曲名，第一首为当前播放
	uint32 order_song_count = 17; // 已点歌曲数量
	repeated string channel_member_accounts = 18; // 麦上成员
	string glory_name = 19; // 称号名称
	string glory_img = 20; // 头标
	uint32 glory_rank = 21; // 排行
	enum GloryLevel{
		City = 0;//市级称号
		Province = 1;//省级称号
		Country = 2;//国服称号
	}
	uint32 glory_level = 22;
	string glory_singer_id = 23;//歌手id

	//播放列表
	repeated string playList_song_id = 24;
	repeated string playList_song_title = 25;
	repeated string playList_singer_ids = 26;  //合唱歌曲, 用冒号隔开
	repeated string playList_singer_names = 27;  //合唱歌曲

	string status_desc = 28; // 接歌准备中/接歌进行中+第X/Y首（X为当前歌曲的次序，Y为总歌曲数）
	uint32 mic_empty_count = 29; //空麦位数
	string private_region = 30; // 专区信息
	uint32 tagId = 31;//专区Id

	string leisure_icon = 32; // 分类图标
	string leisure_desc = 33; // 分类描述

	string personal_cert_icon = 34;
	string personal_cert_text = 35;
	repeated string personal_cert_color = 36;
	string personal_cert_text_shadow_color = 37;

	//挂房听歌花花总数
	uint32 flower_count = 38;

    string singer = 39; // 歌手名

	string mic_band_role_desc = 40;//想找-主唱...
	string status = 41; // 状态演奏中
	//角色人数，0表示缺少
	uint32 main_singer_num = 42; //主唱
	uint32 guitar_num = 43; //吉他
	uint32 keyboard_num = 44; //键盘手
	uint32 bassist_num = 45; //贝斯手
	uint32 drummer_num = 46; //鼓手

	//一起K歌
	message ChannelMusicDisplayKtv {
		option(rcmd.persona.profileId) = *********;

		uint32 channel_id = 1;
		string channel_name = 2;
		string channel_owner_account = 3; //房主名
		int32 channel_owner_sex = 4;
		uint32 channel_member_count = 5;
		string publish_label = 6;    // 房间标签
		string publish_region = 7;   // 分类专区
		uint32 view_type = 8; // 客户端基于这个展示ui
		string icon = 9;//左上角图标
		string publish_desc = 10; //后台直接拼接分类简称与分区
		string song_title = 11; //当前歌曲
		bool high_quality = 12;//是否是优质房
		uint32 tab_id = 13;//玩法id
		uint64 region_id = 14;//blockId与elemId的拼接或者你行你唱的tagId,客户端埋点需要
		uint32 singer_count = 15; // 数量大于1代表合唱
		repeated string order_song_titles = 16; // 已点歌曲名，第一首为当前播放
		uint32 order_song_count = 17; // 已点歌曲数量
		repeated string channel_member_accounts = 18; // 麦上成员
		string glory_name = 19; // 称号名称
		string glory_img = 20; // 头标
		uint32 glory_rank = 21; // 排行
		uint32 glory_level = 22;
		string glory_singer_id = 23;//歌手id
		repeated string playList_song_id = 24;
		repeated string playList_song_title = 25;
		repeated string playList_singer_ids = 26;  //合唱歌曲, 用冒号隔开
		repeated string playList_singer_names = 27;  //合唱歌曲
        string singer = 28; // 歌手名
	}

	//你行你唱
	message ChannelMusicDisplayUcus {
		option(rcmd.persona.profileId) = *********;

		uint32 channel_id = 1;
		string channel_name = 2;
		string channel_owner_account = 3; //房主名
		int32 channel_owner_sex = 4;
		uint32 channel_member_count = 5;
		string publish_label = 6;    // 房间标签
		string publish_region = 7;   // 分类专区
		uint32 view_type = 8; // 客户端基于这个展示ui
		string icon = 9;//左上角图标
		string publish_desc = 10; //后台直接拼接分类简称与分区
		string song_title = 11; //当前歌曲
		bool high_quality = 12;//是否是优质房
		uint32 tab_id = 13;//玩法id
		uint64 region_id = 14;//blockId与elemId的拼接或者你行你唱的tagId,客户端埋点需要
		string status_desc = 15; // 接歌准备中/接歌进行中+第X/Y首（X为当前歌曲的次序，Y为总歌曲数）
		uint32 mic_empty_count = 16; //空麦位数
		string private_region = 17; // 专区信息
		repeated string order_song_titles = 18; // 已点歌曲名，第一首为当前播放
		uint32 tagId = 19;//专区Id
		repeated string playList_song_id = 20;
		repeated string playList_song_title = 21;
		repeated string playList_singer_ids = 22;  //合唱歌曲, 用冒号隔开
		repeated string playList_singer_names = 23;  //合唱歌曲
        string singer = 24; // 歌手名
	}

	//挂房听歌/看书/摸鱼
	message ChannelMusicDisplayLeisure {
		option(rcmd.persona.profileId) = *********;

		uint32 channel_id = 1;
		string channel_name = 2;
		string channel_owner_account = 3; //房主名
		int32 channel_owner_sex = 4;
		uint32 channel_member_count = 5;
		string publish_label = 6;    // 房间标签
		string publish_region = 7;   // 分类专区
		uint32 view_type = 8; // 客户端基于这个展示ui
		string icon = 9;//左上角图标
		string publish_desc = 10; //后台直接拼接分类简称与分区
		string song_title = 11; //当前歌曲
		bool high_quality = 12;//是否是优质房
		uint32 tab_id = 13;//玩法id
		uint64 region_id = 14;//blockId与elemId的拼接或者你行你唱的tagId,客户端埋点需要
		string leisure_icon = 15; // 分类图标
		string leisure_desc = 16; // 分类描述
		repeated string order_song_titles = 17; // 正在播放的歌曲+未来2首歌曲（不足三首时都展示，0首时显示“正在挑选歌单”）
		repeated string playList_song_id = 18;
		repeated string playList_song_title = 19;
		repeated string playList_singer_ids = 20;  //合唱歌曲, 用冒号隔开
		repeated string playList_singer_names = 21;  //合唱歌曲
        string singer = 22; // 歌手名
	}

	message ChannelMusicDisplayRap {
		option(rcmd.persona.profileId) = *********;

		uint32 channel_id = 1;
		string channel_name = 2;
		string channel_owner_account = 3; //房主名
		int32 channel_owner_sex = 4;
		uint32 channel_member_count = 5;
		string publish_label = 6;    // 房间标签
		string publish_region = 7;   // 分类专区
		uint32 view_type = 8; // 客户端基于这个展示ui
		string icon = 9;//左上角图标
		string publish_desc = 10; //后台直接拼接分类简称与分区
		string song_title = 11; //当前歌曲
		bool high_quality = 12;//是否是优质房
		uint32 tab_id = 13;//玩法id
		uint64 region_id = 14;//blockId与elemId的拼接或者你行你唱的tagId,客户端埋点需要
		repeated string order_song_titles = 15; // 已点歌曲名，第一首为当前播放
		repeated string channel_member_accounts = 16; // 麦上成员
		string personal_cert_icon = 17;
		string personal_cert_text = 18;
		repeated string personal_cert_color = 19;
		string personal_cert_text_shadow_color = 20;
		repeated string playList_song_id = 21;
		repeated string playList_song_title = 22;
		repeated string playList_singer_ids = 23;  //合唱歌曲, 用冒号隔开
		repeated string playList_singer_names = 24;  //合唱歌曲
        string singer = 25; // 歌手名
	}

	//边唱边聊，默认模式
	message ChannelMusicDisplayChat {
		option(rcmd.persona.profileId) = *********;

		uint32 channel_id = 1;
		string channel_name = 2;
		string channel_owner_account = 3; //房主名
		int32 channel_owner_sex = 4;
		uint32 channel_member_count = 5;
		string publish_label = 6;    // 房间标签
		string publish_region = 7;   // 分类专区
		uint32 view_type = 8; // 客户端基于这个展示ui
		string icon = 9;//左上角图标
		string publish_desc = 10; //后台直接拼接分类简称与分区
		string song_title = 11; //当前歌曲
		bool high_quality = 12;//是否是优质房
		uint32 tab_id = 13;//玩法id
		uint64 region_id = 14;//blockId与elemId的拼接或者你行你唱的tagId,客户端埋点需要
		repeated string order_song_titles = 15; // 已点歌曲名，第一首为当前播放
		repeated string playList_song_id = 16;
		repeated string playList_song_title = 17;
		repeated string playList_singer_ids = 18;  //合唱歌曲, 用冒号隔开
		repeated string playList_singer_names = 19;  //合唱歌曲
        string singer = 20; // 歌手名
	}

	//T+1写挂房听歌花花总数
	message ChannelMusicFlowerUpdate {
		option(rcmd.persona.profileId) = *********;

		uint32 flower_count = 1;
	}

	//麦可乐队
	message ChannelMusicDisplayMicBand {
		option(rcmd.persona.profileId) = *********;

		uint32 channel_id = 1;
		string channel_name = 2;
		string channel_owner_account = 3; //房主名
		int32 channel_owner_sex = 4;
		uint32 channel_member_count = 5;
		string publish_label = 6;    // 房间标签
		string publish_region = 7;   // 分类专区
		uint32 view_type = 8; // 客户端基于这个展示ui
		string icon = 9;//左上角图标
		string publish_desc = 10; //后台直接拼接分类简称与分区
		string song_title = 11; //当前歌曲
		bool high_quality = 12;//是否是优质房
		uint32 tab_id = 13;//玩法id
		uint64 region_id = 14;//blockId与elemId的拼接或者你行你唱的tagId,客户端埋点需要
		repeated string order_song_titles = 15; // 已点歌曲名，第一首为当前播放
		repeated string playList_song_id = 16;
		repeated string playList_song_title = 17;
		repeated string playList_singer_ids = 18;  //合唱歌曲, 用冒号隔开
		repeated string playList_singer_names = 19;  //合唱歌曲
        string singer = 20; // 歌手名

		string mic_band_role_desc = 21;//想找-主唱...
		string status = 22; // 状态演奏中
		repeated string channel_member_accounts = 23; // 麦上成员
		//角色人数，0表示缺少
		uint32 main_singer_num = 24; //主唱
		uint32 guitar_num = 25; //吉他
		uint32 keyboard_num = 26; //键盘手
		uint32 bassist_num = 27; //贝斯手
		uint32 drummer_num = 28; //鼓手
	}
}
// 音乐房外显数据
// 因为view(存在redis)无法引用codis的ChannelMusicDisplay数据
// 需要单独存一份给view引用的画像
message ChannelMusicDisplayView{
	option(persona.app) = "channel_music_display_view";
    option (persona.profileId) = *********;
    option (persona.version) = 1;
    option (rcmd.persona.process) = "index_es|*********,channel_index_es_view";

    string singer = 1; // 歌手名
}

message TopicChannelPublish{
    option (persona.profileId) = *********;
    option (persona.version) = 1;
    option (persona.app) = "topic_channel_publish";

    repeated TopicChannelPublishItem publish_list  = 1[(persona.decay_len)=10, (persona.type)="list",(rcmd.persona.oper)="add"];
}


message ChannelKTVEnterStat {
    option(rcmd.persona.profileId) = 110201300;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "channel_ktv_enter_stat";

    uint32 iu_ktv_enter_users_1d=1;    // 1d进房用户数
    uint32 iu_ktv_enter_users_3d=2;    // 3d进房用户数
    uint32 iu_ktv_enter_users_1w=3;    // 3d进房用户数
    uint32 iu_ktv_enter_dur_1d=4;    // 1d进房时长
    uint32 iu_ktv_enter_dur_3d=5;    // 3d进房时长
    uint32 iu_ktv_enter_dur_1w=6;    // 1w进房时长
    float iu_ktv_enter_dur_day_1d=7;    // 1d人均进房时长
    float iu_ktv_enter_dur_day_3d=8;    // 3d人均进房时长
    float iu_ktv_enter_dur_day_1w=9;    // 1w人均进房时长

}

message ChannelLeisureEnterStat {
    option(rcmd.persona.profileId) = 110201600;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "channel_leisure_enter_stat";
    // option (rcmd.persona.process) = "hard_view";  // 迁移到features-collect

    uint32 iu_leisure_enter_users_1d=1;                      // '1d进房用户数'
    uint32 iu_leisure_enter_users_3d=2;                      // '3d进房用户数'
    uint32 iu_leisure_enter_users_1w=3;                      // '1w进房用户数'
    uint32 iu_leisure_enter_dur_1d=4;                      // '1d进房时长'
    uint32 iu_leisure_enter_dur_3d=5;                      // '3d进房时长'
    uint32 iu_leisure_enter_dur_1w=6;                      // '1w进房时长'
    float iu_leisure_enter_dur_user_1d=7;                      // '1d人均进房时长'
    float iu_leisure_enter_dur_user_3d=8;                      // '3d人均进房时长'
    float iu_leisure_enter_dur_user_1w=9;                      // '1w人均进房时长'

    map<string,uint32> iu_leisure_is_new_enter_users_1d=10;           // <is_new,enter_days>'1d新老用户的进房天数'
    map<string,uint32> iu_leisure_is_new_enter_users_3d=11;           // <is_new,enter_days>'3d新老用户的进房天数'
    map<string,uint32> iu_leisure_is_new_enter_users_1w=12;           // <is_new,enter_days>'1w新老用户的进房天数'
    map<string,uint32> iu_leisure_is_new_enter_dur_1d=13;            // <is_new,enter_dur>'1d新老用户的进房时长'
    map<string,uint32> iu_leisure_is_new_enter_dur_3d=14;            // <is_new,enter_dur>'3d新老用户的进房时长'
    map<string,uint32> iu_leisure_is_new_enter_dur_1w=15;            // <is_new,enter_dur>'1w新老用户的进房时长'
    map<string,float> iu_leisure_is_new_enter_dur_user_1d=16;        // <is_new,enter_dur_day>'1d新老用户的日均进房时长'
    map<string,float> iu_leisure_is_new_enter_dur_user_3d=17;        // <is_new,enter_dur_day>'3d新老用户的日均进房时长'
    map<string,float> iu_leisure_is_new_enter_dur_user_1w=18;        // <is_new,enter_dur_day>'1w新老用户的日均进房时长'

    map<string,uint32> iu_leisure_platform_enter_users_1d=19;          // <platform,enter_days>'1d平台的进房天数'
    map<string,uint32> iu_leisure_platform_enter_users_3d=20;          // <platform,enter_days>'3d平台的进房天数'
    map<string,uint32> iu_leisure_platform_enter_users_1w=21;          // <platform,enter_days>'1w平台的进房天数'
    map<string,uint32> iu_leisure_platform_enter_dur_1d=22;           // <platform,enter_dur>'1d平台的进房时长'
    map<string,uint32> iu_leisure_platform_enter_dur_3d=23;           // <platform,enter_dur>'3d平台的进房时长'
    map<string,uint32> iu_leisure_platform_enter_dur_1w=24;           // <platform,enter_dur>'1w平台的进房时长'
    map<string,float> iu_leisure_platform_enter_dur_user_1d=25;       // <platform,enter_dur_day>'1d平台的日均进房时长'
    map<string,float> iu_leisure_platform_enter_dur_user_3d=26;       // <platform,enter_dur_day>'3d平台的日均进房时长'
    map<string,float> iu_leisure_platform_enter_dur_user_1w=27;       // <platform,enter_dur_day>'1w平台的日均进房时长'
}

// room delivery counter
message ChannelDelivery{
    option(rcmd.persona.profileId) = 110201400;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "channel_delivery";
    option (rcmd.persona.process) = "index_es|*********,channel_index_es_view";


    int32 delivery_count=1; // 当前下发次数
    message ChannelDeliveryIncr {
        option(rcmd.persona.profileId) = 110201401;
        int32 delivery_count = 1[(rcmd.persona.oper)="incrby"];
    }
}
message ChannelAlgoDelivery{
    option(rcmd.persona.profileId) = 110202100;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "channel_algo_delivery";
    option (rcmd.persona.process) = "index_es|*********,channel_index_es_view";

    int32 algo_delivery_count = 1;// 算法版下发次数

    message ChannelAlgoDeliveryIncr{
        option(rcmd.persona.profileId) = 110202101;
        int32 algo_delivery_count = 1[(rcmd.persona.oper)="incrby"];// 算法版下发次数
    }
}
message ChannelViolation{
    option(rcmd.persona.profileId) = 110201700;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "channel_violation";
	option (rcmd.persona.process) = "index_es|*********,channel_index_es_view";


    uint32 level = 2; // 违规等级
	uint32 rap_level = 3; // 说唱房是否带广告
	map<string,bool> hit_key_word_type = 4; //房间命中的关键词类型

    enum LevelType {
        LevelType_Invalid = 0;
        LevelType_Legal = 1; // 合法
        LevelType_Violated = 2; // 违规
    }
    message SetLevel {
        option(rcmd.persona.profileId) = 110201702;
        uint32 level = 2; // 违规等级
    }
    message SetRapLevel {
        option(rcmd.persona.profileId) = 110201703;
        uint32 rap_level = 1; // 说唱房是否带广告
    }
	message UpdateKeyWordType {
		option(rcmd.persona.profileId) = 110201704;
        map<string,bool> hit_key_word_type = 1; //房间命中的关键词类型
	}
}


// 房间侧画像TML重构
message ChannelOfflineTML {
    option (rcmd.persona.profileId) = 110201500;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "channel_offline_profile_tml";
    option (rcmd.persona.process) = "hard_view";

    float i_room_kaihei_enter_cnt_7d = 1;                          //房间近7天进房次数
    float i_room_kaihei_duration_sum_7d = 2;                     //房间近7天进房用户总停留时长
    float i_room_kaihei_gangup_cnt_7d = 3;                         //房间近7天进房开黑次数(duration>=600)
    float i_room_kaihei_out_cnt_7d = 4;                            //房间近7天进房无效停留次数(duration<20)
    float i_room_kaihei_mic_cnt_7d = 5;                            //房间近7天进房上麦次数
    map<string, string> i_room_kaihei_gender_click_cnt_7d=6;        //房间近7天进房用户不同性别对应的进房次数
    map<string, string> i_room_kaihei_gender_duration_sum_7d=7;   //房间近7天进房用户不同性别对应的总停留时长
    map<string, string> i_room_kaihei_gender_gangup_cnt_7d=8;       //房间近7天进房用户不同性别对应的开黑次数
    map<string, string> i_room_kaihei_gender_out_cnt_7d=9;          //房间近7天进房用户不同性别对应的闪退（不足20s）次数
    map<string, string> i_room_kaihei_gender_mic_cnt_7d=10;         //间近7天进房用户不同性别对应的上麦次数


    map<string, string> i_room_kaihei_game_id_click_cnt_7d=11;       //房间近7天进房用户不同game_id对应的进房次数
    map<string, string> i_room_kaihei_game_id_duration_sum_7d=12;  //房间近7天进房用户不同game_id对应的总停留时长
    map<string, string> i_room_kaihei_game_id_gangup_cnt_7d=13;      //房间近7天进房用户不同game_id对应的开黑次数
    map<string, string> i_room_kaihei_game_id_out_cnt_7d=14;         //房间近7天进房用户不同game_id对应的闪退（不足20s）次数
    map<string, string> i_room_kaihei_game_id_mic_cnt_7d=15;         //房间近7天进房用户不同game_id对应的上麦次数

    map<string, string> i_room_kaihei_is_new_click_cnt_7d=16;       //房间近7天进房用户不同is_new对应的进房次数
    map<string, string> i_room_kaihei_is_new_duration_sum_7d=17;  //房间近7天进房用户不同is_new对应的总停留时长
    map<string, string> i_room_kaihei_is_new_gangup_cnt_7d=18;      //房间近7天进房用户不同is_new对应的开黑次数
    map<string, string> i_room_kaihei_is_new_out_cnt_7d=19;         //房间近7天进房用户不同is_new对应的闪退（不足20s）次数
    map<string, string> i_room_kaihei_is_new_mic_cnt_7d=20;         //房间近7天进房用户不同is_new对应的上麦次数

    float i_room_kaihei_browse_cnt_3d = 21;                        //房间近3天被曝光次数
    float i_room_kaihei_click_cnt_3d = 22;                         //房间近3天被点击次数

    map<string, string> i_room_kaihei_gender_browse_cnt_3d=23;      //房间近3天进房用户不同gender对应的曝光次数
    map<string, string> i_room_kaihei_gender_click_cnt_3d=24;       //房间近3天进房用户不同gender对应的点击次数
    map<string, string> i_room_kaihei_game_id_browse_cnt_3d=25;      //房间近3天进房用户不同game_id对应的曝光次数
    map<string, string> i_room_kaihei_game_id_click_cnt_3d=26;       //房间近3天进房用户不同game_id对应的点击次数
    map<string, string> i_room_kaihei_is_new_browse_cnt_3d=27;      //房间近3天进房用户不同is_new对应的曝光次数
    map<string, string> i_room_kaihei_is_new_click_cnt_3d=28;       //房间近3天进房用户不同is_new对应的点击次数
}

// 陪玩意图
message PlaymateIntention {
    option (rcmd.persona.profileId) = 110202200;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "playmate_intention";

    int64 intention_limit_time = 1;
}

// easy-feature 房间侧离线画像
message ChannelOfflineTmlV2 {
    option (rcmd.persona.profileId) = 110201800;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "channel_offline_profile_tml_v2";

    float i_room_kaihei_enter_cnt_7d = 1;                          //房间近7天进房次数
    float i_room_kaihei_duration_sum_7d = 2;                     //房间近7天进房用户总停留时长
    float i_room_kaihei_gangup_cnt_7d = 3;                         //房间近7天进房开黑次数(duration>=600)
    float i_room_kaihei_out_cnt_7d = 4;                            //房间近7天进房无效停留次数(duration<20)
    float i_room_kaihei_mic_cnt_7d = 5;                            //房间近7天进房上麦次数
    map<string, float> i_room_kaihei_gender_click_cnt_7d=6;        //房间近7天进房用户不同性别对应的进房次数
    map<string, float> i_room_kaihei_gender_duration_sum_7d=7;   //房间近7天进房用户不同性别对应的总停留时长
    map<string, float> i_room_kaihei_gender_gangup_cnt_7d=8;       //房间近7天进房用户不同性别对应的开黑次数
    map<string, float> i_room_kaihei_gender_out_cnt_7d=9;          //房间近7天进房用户不同性别对应的闪退（不足20s）次数
    map<string, float> i_room_kaihei_gender_mic_cnt_7d=10;         //间近7天进房用户不同性别对应的上麦次数


    map<string, float> i_room_kaihei_game_id_click_cnt_7d=11;       //房间近7天进房用户不同game_id对应的进房次数
    map<string, float> i_room_kaihei_game_id_duration_sum_7d=12;  //房间近7天进房用户不同game_id对应的总停留时长
    map<string, float> i_room_kaihei_game_id_gangup_cnt_7d=13;      //房间近7天进房用户不同game_id对应的开黑次数
    map<string, float> i_room_kaihei_game_id_out_cnt_7d=14;         //房间近7天进房用户不同game_id对应的闪退（不足20s）次数
    map<string, float> i_room_kaihei_game_id_mic_cnt_7d=15;         //房间近7天进房用户不同game_id对应的上麦次数

    map<string, float> i_room_kaihei_is_new_click_cnt_7d=16;       //房间近7天进房用户不同is_new对应的进房次数
    map<string, float> i_room_kaihei_is_new_duration_sum_7d=17;  //房间近7天进房用户不同is_new对应的总停留时长
    map<string, float> i_room_kaihei_is_new_gangup_cnt_7d=18;      //房间近7天进房用户不同is_new对应的开黑次数
    map<string, float> i_room_kaihei_is_new_out_cnt_7d=19;         //房间近7天进房用户不同is_new对应的闪退（不足20s）次数
    map<string, float> i_room_kaihei_is_new_mic_cnt_7d=20;         //房间近7天进房用户不同is_new对应的上麦次数

    float i_room_kaihei_browse_cnt_3d = 21;                        //房间近3天被曝光次数
    float i_room_kaihei_click_cnt_3d = 22;                         //房间近3天被点击次数

    map<string, float> i_room_kaihei_gender_browse_cnt_3d=23;      //房间近3天进房用户不同gender对应的曝光次数
    map<string, float> i_room_kaihei_gender_click_cnt_3d=24;       //房间近3天进房用户不同gender对应的点击次数
    map<string, float> i_room_kaihei_game_id_browse_cnt_3d=25;      //房间近3天进房用户不同game_id对应的曝光次数
    map<string, float> i_room_kaihei_game_id_click_cnt_3d=26;       //房间近3天进房用户不同game_id对应的点击次数
    map<string, float> i_room_kaihei_is_new_browse_cnt_3d=27;      //房间近3天进房用户不同is_new对应的曝光次数
    map<string, float> i_room_kaihei_is_new_click_cnt_3d=28;       //房间近3天进房用户不同is_new对应的点击次数
}

// 房间用户详情
message ChannelUserDetail {
    option (rcmd.persona.profileId) = 110203200;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "channel_user_detail";
    repeated uint32 user_id_list = 1[(persona.type)="set"];
    map<uint32,uint32> age_map = 2;
    map<uint32,uint32> reg_time_map = 3;
    map<uint32,uint32> province_code_map = 4;
    map<uint32,uint32> pref_tag_map = 5;
    repeated uint32 uids_from_tab_list = 6[(persona.type)="set"]; // 从列表进房用户，进房来源channel_enter_source=13
    repeated uint32 uids_not_from_list = 7[(persona.type)="set"]; // 其它进房来源用户
}

// 房间动态风险数据
message ChannelRisk {
    option (persona.profileId) = 110203300;
    option (persona.version) = 1;
    option (persona.app) = "channel_risk";
    option (rcmd.persona.process) = "hard_view";

    uint32 risk_tag_id = 1;    // 房间标签id
    uint32 publish_state = 2;  // 房间发布状态 0,1
    uint32 channel_source = 3; // 房间来源
    uint32 room_label_id = 4;
    uint32 room_type = 5;
    uint32 bind_type = 6;
    uint32 mic_mode = 7;

    enum ChannelRiskSourceType{
        UnknownRiskSourceType = 0;
        UgcRiskSourceType = 1;
        PgcRiskSourceType = 2;
    }

    message RiskTagIdUpdate {
        option(rcmd.persona.profileId) = 110203301;
        uint32 risk_tag_id = 1;
    }

    message PublishStateUpdate {
        option(rcmd.persona.profileId) = 110203302;
        uint32 publish_state = 2;
    }

    message RoomDataUpdate {
        option(rcmd.persona.profileId) = 110203303;
        uint32 room_label_id = 1;
        uint32 room_type = 2;
    }

    message MicUpdate {
        option(rcmd.persona.profileId) = 110203304;
        uint32 bind_type = 1;
        uint32 mic_mode = 2;
    }
}

// 音乐房历史互动率
message ChannelInteractOffline {
    option (rcmd.persona.profileId) = 110203400;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "channel_interact_offline";
	option (rcmd.persona.process) = "index_es|*********,channel_index_es_view";

    float i_room_enter_user_cnt_7d = 1;  // 近7天该音乐房进房人数
    float i_room_follow_user_cnt_7d = 2;  // 近7天该音乐房发生关注的用户数
    float i_room_music_follow_rate_7d = 3;  // 近7天该音乐房发起的UV关注率
    float i_room_mic_user_cnt_7d = 4;  // 近7天该音乐房发生上麦的用户数
    float i_room_music_mic_rate_7d = 5;  // 近7天该音乐房发起的UV上麦率
    float i_room_mic10s_user_cnt_7d = 6;  // 近7天该音乐房发生上麦10s的用户数
    float i_room_music_mic10s_rate_7d = 7; // 近7天该音乐房发起的UV上麦10s率

    float i_room_mean_follow_rate_7d = 8;  // 近7天所有音乐房的平均关注率
    float i_room_mean_mic_rate_7d=9;       // 近7天所有音乐房的平均上麦率
    float i_room_mean_mic10s_rate_7d=10;   // 近7天所有音乐房的平均上麦10s率
}


// 房间用户动态数据统计
message ChannelDynamicStat {
    option (rcmd.persona.profileId) = 110203500;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "channel_dynamic_stat";
    int32 user_num = 1;         //房间人数
    int32 enter_female_num = 2; //房间女性人数
    int32 minor_user_num = 3;//房间未成年人数

    int32 mic_num = 4;          //麦上人数
    int32 mic_female_num = 5;   //在麦女性人数
    int32 mic_age_sum = 6;      //在麦人数年龄和(未知年龄用户当0处理)
    int32 mic_no_age_user_num = 7; //在麦未知年龄房间人数

    uint32 last_enter_time = 8;

    int32 new_user_num = 9; // 新用户进房数量
}

message ChannelMicInfo {
    option (rcmd.persona.profileId) = 110203700;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "channel_mic_info";

    int32 mute_mic_num = 1; // 闭麦数
    int32 mic_locked_num = 2; // 锁麦数
    int32 disable_mic_num = 3; //不可正常使用的麦数，等于 闭麦数 + 锁麦数
    uint32 mic_size = 4; // 房间麦位数量
}

message ChannelPublishStat {
    option (rcmd.persona.profileId) = 110203800;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "channel_publish_stat";

    int32 daily_publish_times = 1; // 每天发布的次数
}

message ChannelOfflineWzInfo {
    option (rcmd.persona.profileId) = 110203900;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "channel_offline_wz_info";

    string i_wz_service = 1;
    string i_wz_level_nd = 2;
    string i_wz_level = 3;
    repeated string i_wz_positions = 4;
    string i_wz_mode = 5;
    string i_wz_type = 6;
}

// 房间标题识别结果
message ChannelTitleRecognitionResult {
    option (rcmd.persona.profileId) = 110204000;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "channel_title_recognition_result";

    map<string,float> porn_label = 1; // 房间标题涉黄识别结果
    map<string,float> abuse_label = 2; // 房间标题辱骂识别结果
    float default_abuse_label = 3; // 默认辱骂策略识别结果
    float default_porn_label = 4; // 默认涉黄策略识别结果
}

message ChannelGameInfo {
    option (rcmd.persona.profileId) = 110208000;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "channel_game_info";

    repeated string settings = 1[(rcmd.persona.type)="list"];
}

message RoomTagSimilar {
	option (rcmd.persona.profileId) = 110281000;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "room_tag_similar";

	map<string,float> ui_newuser_room_tag_sim = 1;
}


message UndertakeChannelInfo {
    option (rcmd.persona.profileId) = 110209000;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "undertake_channel_info";

    string undertake_channel_label = 1;
}

//服务端热度值系数相关协议
message ChannelHot {
    uint32 channel_id = 1;
    float hot = 2;          //整体热度
    float mic_hot = 3;      //麦上热度
    float enter_hot = 4;    //进房热度
    int64 update_time = 5;  //更新时间
}

// 个人房陪玩意图识别
message ChannelPlayModelIntention {
    option (rcmd.persona.profileId) = 110210000;
    option (rcmd.persona.version) = 1;
    option (rcmd.persona.app) = "channel_play_model_intention";

    bool enable_play_model_filter = 1; // 是否启用陪玩模型过滤
    bool enable_play_model_degrade = 2; // 是否启用陪玩模型降权
}