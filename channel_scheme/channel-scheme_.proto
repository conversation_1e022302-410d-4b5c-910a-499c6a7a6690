syntax = "proto3";

package ga.channel_scheme;

import "ga_base.proto";
import "channel/channel_opt_.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel-scheme";

//对应之前topic-channel的TabType,枚举值和之前的保持一致
enum SchemeType {
  SCHEME_TYPE_COMMON = 0;       //通用分类，例如扩列聊天不属于小游戏，也不是游戏类型和音乐类型
  SCHEME_TYPE_GAME = 1;         //游戏开黑
  SCHEME_TYPE_MINI_GAME = 2;    //小游戏
  SCHEME_TYPE_MUSIC = 3;        //音乐
}

//业务分类
enum SchemeBusinessCategory {
  SCHEME_BUSINESS_CATEGORY_UGC_UNKNOW = 0;
  SCHEME_BUSINESS_CATEGORY_UGC_GAME = 1;  //开黑
  SCHEME_BUSINESS_CATEGORY_UGC_MUSIC = 2;  //音乐
  SCHEME_BUSINESS_CATEGORY_PGC = 3;  //pgc
}

//玩法详细类型，用于区分不同玩法类型，例如是你行你唱还是pia戏等
enum SchemeDetailType {
  SCHEME_DETAIL_TYPE_UNKNOWN = 0 ;
  SCHEME_DETAIL_TYPE_FUN = 1;    //娱乐玩法
  SCHEME_DETAIL_TYPE_GAME = 2;   //游戏开黑玩法(游戏开黑语音房玩法)
  SCHEME_DETAIL_TYPE_LIVE = 3;    // 直播
  SCHEME_DETAIL_TYPE_DATING = 4;          // 相亲
  SCHEME_DETAIL_TYPE_MINI_GAME = 5;        // 小游戏
  SCHEME_DETAIL_TYPE_MASKED_DATING = 6;    // 1V1语音聊天（蒙面聊天）
  SCHEME_DETAIL_TYPE_CP = 7;               // 活动大房cp玩法
  SCHEME_DETAIL_TYPE_IDOL = 8;             // 活动大房偶像玩法
  SCHEME_DETAIL_TYPE_CP_BATTLE_GAME = 9;   // cp战玩法
  SCHEME_DETAIL_TYPE_SING_A_ROUND = 10;    // 接歌抢唱玩法
  SCHEME_DETAIL_TYPE_KTV = 11;             //ktv玩法
  SCHEME_DETAIL_TYPE_ROLE_PLAY = 12;     //角色扮演玩法
  SCHEME_DETAIL_TYPE_LISTENING = 13;  //挂房听歌玩法
  SCHEME_DETAIL_TYPE_MULTI_BATTLE = 14; //团战玩法
  SCHEME_DETAIL_TYPE_MUSIC_NEST = 15;   //乐窝玩法
  SCHEME_DETAIL_TYPE_RAP = 16;         //rap玩法
  SCHEME_DETAIL_TYPE_PGC_WEREWOLVES = 17;  //PGC狼人杀玩法
  SCHEME_DETAIL_TYPE_PIA_XI = 18;       //pia戏玩法
  SCHEME_DETAIL_TYPE_WEREWOLVES_GAME = 19;  //9麦狼人杀玩法
  SCHEME_DETAIL_TYPE_MULTI_MIC_MINI_GAME = 20; //小游戏21麦玩法
  SCHEME_DETAIL_TYPE_MUSIC_CONCERT = 21; // 乐队玩法
  SCHEME_DETAIL_TYPE_PIA_V2 = 22; //pia戏 v2版本玩法
  SCHEME_DETAIL_TYPE_ESCAPE_GAME = 23;  //迷境玩法
  SCHEME_DETAIL_TYPE_GAME_RACE = 24;    //赛事玩法
  SCHEME_DETAIL_TYPE_OFFICIAL_CHANNEL = 25 ;   //官频布局模式
  //SCHEME_SVR_DETAIL_TYPE_PERFECT_COUPLE_GAME = 26; //天配玩法26，已废弃，这里保留占位(6.21.0客户端带上了未完成的玩法, 后续也不能使用26这个数值)
  SCHEME_DETAIL_TYPE_PERFECT_COUPLE_GAME = 27; //天配玩法27 (6.26.0天配上线，使用该枚举数值)
  SCHEME_DETAIL_TYPE_OFFER_CHANNEL = 28;   //拍卖房玩法(公会公开房里的玩法)
  SCHEME_DETAIL_TYPE_COMMUNITY_CHAT = 29; // 社群聊天室玩法
  SCHEME_DETAIL_TYPE_COMMUNITY_EXPAND_SOCIAL_CHAT = 30; // 社群扩列聊天玩法
  SCHEME_DETAIL_TYPE_COMMUNITY_SHOW_RAP = 31; //社群说唱
  SCHEME_DETAIL_TYPE_COMMUNITY_SHOW_KTV = 32; //社群K歌
  SCHEME_DETAIL_TYPE_GAME_WORD = 33;          //游戏开黑文字房模式
  SCHEME_DETAIL_TYPE_WEDDING = 34;    //婚礼房玩法
}

enum SchemeLayoutType {
  SCHEME_LAYOUT_TYPE_UNKNOWN = 0 ;
  SCHEME_LAYOUT_DEFAULT_FUN = 1;  //娱乐房默认布局模式(3排9麦布局)
  SCHEME_LAYOUT_DEFAULT_GAME = 2;  //游戏开黑默认布局模式
  SCHEME_LAYOUT_LIVE = 3;            // 直播布局模式
  SCHEME_LAYOUT_DATING = 4;          // 相亲房布局模式
  SCHEME_LAYOUT_MINI_GAME = 5;        // 小游戏布局模式
  SCHEME_LAYOUT_MASKED_DATING = 6;    // 1V1语音聊天（蒙面聊天）
  SCHEME_LAYOUT_CP = 7;               // cp布局模式，用于活动大房
  SCHEME_LAYOUT_IDOL = 8;             // 偶像布局模式，用于活动大房
  SCHEME_LAYOUT_CP_BATTLE_GAME = 9;   // cp战布局模式
  SCHEME_LAYOUT_SING_A_ROUND = 10;    // 接歌抢唱麦位模式
  SCHEME_LAYOUT_KTV = 11;             //ktv布局模式
  SCHEME_LAYOUT_ROLE_PLAY = 12;     //角色扮演
  SCHEME_LAYOUT_LISTENING = 13;  //挂房听歌
  SCHEME_LAYOUT_MULTI_BATTLE = 14; //团战
  SCHEME_LAYOUT_MUSIC_NEST = 15;   //乐窝
  SCHEME_LAYOUT_RAP = 16;         //rap布局模式
  SCHEME_LAYOUT_PGC_WEREWOLVES = 17;  //PGC狼人杀布局模式
  SCHEME_LAYOUT_PIA_XI = 18;       //pia戏
  SCHEME_LAYOUT_WEREWOLVES_GAME = 19;  //对应原来的9麦狼人杀模式
  SCHEME_LAYOUT_MULTI_MIC_MINI_GAME = 20; //对应原来的小游戏21麦模式
  SCHEME_LAYOUT_MUSIC_CONCERT = 21; // 乐队布局
  SCHEME_LAYOUT_PIA_V2 = 22; //pia戏 v2
  SCHEME_LAYOUT_ESCAPE_GAME = 23;  //密室逃脱
  SCHEME_LAYOUT_GAME_RACE = 24;    //赛事玩法
  SCHEME_LAYOUT_OFFICIAL_CHANNEL = 25 ;   //官频布局模式
  //SCHEME_LAYOUT_PERFECT_COUPLE_GAME = 26; //天配玩法26，已废弃，这里保留占位(6.21.0客户端带上了未完成的玩法, 后续也不能使用26这个数值)
  SCHEME_LAYOUT_PERFECT_COUPLE_GAME = 27; //天配玩法27 (6.26.0天配上线，使用该枚举数值)
  SCHEME_LAYOUT_OFFER_CHANNEL = 28;   //拍卖房玩法(公会公开房里的玩法)
  SCHEME_LAYOUT_COMMUNITY_CHAT = 29; // 社群聊天室玩法
  SCHEME_LAYOUT_COMMUNITY_EXPAND_SOCIAL_CHAT = 30; // 社群扩列聊天玩法
  SCHEME_LAYOUT_COMMUNITY_SHOW_RAP = 31; //社群说唱
  SCHEME_LAYOUT_COMMUNITY_SHOW_KTV = 32; //社群K歌
  SCHEME_LAYOUT_GAME_WORD = 33;          //游戏开黑文字房模式
  SCHEME_LAYOUT_WEDDING = 34;    //婚礼房玩法
}

enum SchemeMicAudioType {
  SCHEME_MIC_AUDIO_TYPE_UNKNOWN = 0;
  SCHEME_MIC_AUDIO_TYPE_FUN   = 1;   //娱乐房音质
  SCHEME_MIC_AUDIO_TYPE_GAME  = 2;   //高音质开黑音质
  SCHEME_MIC_AUDIO_TYPE_KTV = 3;     //KTV音质
  SCHEME_MIC_AUDIO_TYPE_CONCERT = 4; //乐队音质
}

message SchemeLayout{
  uint32 layout_type = 1;            //枚举,布局类型
  uint32 default_mic_size = 2;          //默认麦位数,****在不认识layout_type时，客户端会使用该值作为麦位数****
  repeated uint32 mvp_mic_id_list = 3;  // mvp麦位列表，如果为空表示没有，目前使用第一个元素
  uint32 fallback_layout_type = 4;   //如果旧版客户端不认识layout_type，则用该值做兜底逻辑
  uint32 cur_mic_size = 5;           //当前的麦位数
  repeated ga.channel.SimpleMicrSpace all_mic_list = 6; //当前的全量麦位列表，切换玩法时下发
  int64 mic_op_time_ms = 7;                  //麦位的服务器时间戳
}

message SchemeMicAudio {
  uint32 mic_audio_type = 1;   //枚举，高音质，低音质等
  //6.59.5版本动态配置音频参数需求新增字段
  map<uint32, string> mic_audio_sdk_info = 2; //key为客户端类型，see ga.TT_CLIENT_TYPE  value为sdk配置信息，json格式,客户端自己解析
  uint64  mic_audio_sdk_info_update_ms = 3; //sdk配置信息更新时间,毫秒时间戳
  uint32 high_bit_rate = 4; // 优先高码率，单位kbps，非零时优先使用该码率
}

//废弃结构
message PgcLevelInfo {
  uint32 scheme_id = 1;
  uint32 level     = 2;
}

message ChannelSchemeInfo {
  uint32 scheme_id = 1;      //对应之前的tabid
  string scheme_name = 2;
  uint32 scheme_type = 3;    //enum SchemeType,对应之前的tab_type,游戏，音乐，小游戏等
  SchemeLayout layout_info = 4;
  SchemeMicAudio mic_audio_info = 5;
  uint64 ts = 6;             //服务端时间戳,毫秒
  string scheme_icon = 7;    //玩法icon

  repeated PgcLevelInfo pgc_level_info = 8 ;         //废弃字段

  uint32 level = 9; //公会公开房的特殊信息，兼容相亲房，cp战，这两个玩法的等级信息

  SchemeBusinessCategory business_category = 10 ;   //业务分类

  SchemeDetailType scheme_detail_type = 11;//玩法详细类型，用于区分不同玩法类型，例如是你行你唱,密室逃托,游戏开黑，还是pia戏等
}

//进房后拉取当前房间玩法信息
message GetChannelSchemeInfoReq {
  ga.BaseReq base_req = 1;
  uint32 cid = 2;
}

message GetChannelSchemeInfoResp {
  ga.BaseResp base_resp = 1;
  uint32 cid = 2;
  ChannelSchemeInfo scheme_info = 3;
  //兼容旧命令的一些字段(客户端需要哪些字段后面再补上去)
}

//切换玩法后，检查是否需要主动退房
message CheckExitAfterSwitchSchemeReq {
  ga.BaseReq base_req = 1;
  uint32 cid = 2;
  uint32 scheme_id = 3;
}

message CheckExitAfterSwitchSchemeResp {
  ga.BaseResp base_resp = 1;
  bool need_exit_channel =2;
  string exit_channel_text = 3;
}

//pgc玩法列表
message GetPgcChannelSchemeListReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
}
message PgcChannelSchemeConfInfo  {
  uint32 scheme_id = 1;
  string scheme_name = 2;
  uint32 layout_type = 3;    	// 枚举 see channel-scheme_.proto SchemeLayoutType
  string icon_select = 4;      // 选中状态ICON  选中状态ICON(需要区分移动和pc?)
  string icon_un_select = 5;   // 非选中状态ICON
  uint32 level = 6;            // 废弃，在ChannelSchemeInfo里已返回相亲房和cp战的等级
  SchemeDetailType scheme_detail_type = 11;//玩法详细类型，用于区分不同玩法类型，例如是你行你唱,密室逃托,游戏开黑，还是pia戏等
}
message GetPgcChannelSchemeListResp {
  ga.BaseResp base_resp = 1;
  uint32 channel_id = 2;
  repeated PgcChannelSchemeConfInfo scheme_list = 3;
  uint32  cur_scheme_id = 4;                           //该房间当前正在玩的玩法id
}

//pgc切换玩法
message SwitchChannelSchemeReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  uint32 scheme_id = 3;
  uint32 layout_type = 4;  //公会内部房使用这个字段进行玩法切换
}
message SwitchChannelSchemeResp {
  ga.BaseResp base_resp = 1;
}