// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.content;

import "ugc/ugc_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/content;content";

service ContentLogic {
    option (ga.api.extension.logic_service_name) = "ugc-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.ugc.ContentLogic/";
    rpc SubscribeTopic(ga.ugc.SubscribeTopicReq) returns (ga.ugc.SubscribeTopicResp) {
        option (ga.api.extension.command) = {
             id: 2550;
        };
    }
    rpc UnsubscribeTopic(ga.ugc.UnsubscribeTopicReq) returns (ga.ugc.UnsubscribeTopicResp) {
        option (ga.api.extension.command) = {
             id: 2551;
        };
    }
    rpc GetTopicList(ga.ugc.GetTopicListReq) returns (ga.ugc.GetTopicListResp) {
        option (ga.api.extension.command) = {
             id: 2552;
        };
    }
    rpc GetTopicInfo(ga.ugc.GetTopicInfoReq) returns (ga.ugc.GetTopicInfoResp) {
        option (ga.api.extension.command) = {
             id: 2553;
        };
    }
    rpc GetSubscriberTopicList(ga.ugc.GetSubscribeTopicListReq) returns (ga.ugc.GetSubscribeTopicListResp) {
        option (ga.api.extension.command) = {
             id: 2554;
        };
    }
    rpc GetUnSubscribeTopicList(ga.ugc.GetUnSubscribeTopicListReq) returns (ga.ugc.GetUnSubscribeTopicListResp) {
        option (ga.api.extension.command) = {
             id: 2555;
        };
    }
    rpc CheckTopicsIsSubscribe(ga.ugc.CheckTopicsIsSubscribeReq) returns (ga.ugc.CheckTopicsIsSubscribeResp) {
        option (ga.api.extension.command) = {
             id: 2556;
        };
    }
    rpc SearchTopic(ga.ugc.SearchTopicReq) returns (ga.ugc.SearchTopicResp) {
        option (ga.api.extension.command) = {
             id: 2557;
        };
    }
    rpc UpdateAttachmentPrivacy(ga.ugc.UpdateAttachmentDownloadPrivacyReq) returns (ga.ugc.UpdateAttachmentDownloadPrivacyResp) {
        option (ga.api.extension.command) = {
             id: 2587;
        };
    }
    rpc MarkContentSticky(ga.ugc.MarkContentStickyReq) returns (ga.ugc.MarkContentStickyResp) {
        option (ga.api.extension.command) = {
             id: 2588;
        };
    }
    rpc PostPost(ga.ugc.PostPostReq) returns (ga.ugc.PostPostResp) {
        option (ga.api.extension.command) = {
             id: 2590;
        };
    }
    rpc MarkPostAttachmentUploaded(ga.ugc.MarkPostAttachmentUploadedReq) returns (ga.ugc.MarkPostAttachmentUploadedResp) {
        option (ga.api.extension.command) = {
             id: 2591;
        };
    }
    rpc DeletePost(ga.ugc.DeletePostReq) returns (ga.ugc.DeletePostResp) {
        option (ga.api.extension.command) = {
             id: 2592;
        };
    }
    rpc GetPost(ga.ugc.GetPostReq) returns (ga.ugc.GetPostResp) {
        option (ga.api.extension.command) = {
             id: 2593;
        };
    }
    rpc PostComment(ga.ugc.PostCommentReq) returns (ga.ugc.PostCommentResp) {
        option (ga.api.extension.command) = {
             id: 2594;
        };
    }
    rpc DeleteComment(ga.ugc.DeleteCommentReq) returns (ga.ugc.DeleteCommentResp) {
        option (ga.api.extension.command) = {
             id: 2595;
        };
    }
    rpc GetCommentList(ga.ugc.GetCommentListReq) returns (ga.ugc.GetCommentListResp) {
        option (ga.api.extension.command) = {
             id: 2596;
        };
    }
    rpc ReportPostView(ga.ugc.ReportPostViewReq) returns (ga.ugc.ReportPostViewResp) {
        option (ga.api.extension.command) = {
             id: 2597;
        };
    }
    rpc GetAttitudeUserList(ga.ugc.GetAttitudeUserListReq) returns (ga.ugc.GetAttitudeUserListResp) {
        option (ga.api.extension.command) = {
             id: 2598;
        };
    }
    rpc ReportPostShare(ga.ugc.ReportPostShareReq) returns (ga.ugc.ReportPostShareResp) {
        option (ga.api.extension.command) = {
             id: 2599;
        };
    }
    rpc ExpressAttitude(ga.ugc.ExpressAttitudeReq) returns (ga.ugc.ExpressAttitudeResp) {
        option (ga.api.extension.command) = {
             id: 2600;
        };
    }
    rpc GetNewsFeeds(ga.ugc.GetNewsFeedsReq) returns (ga.ugc.GetNewsFeedsResp) {
        option (ga.api.extension.command) = {
             id: 2570;
        };
    }
    rpc RemoveFeeds(ga.ugc.RemoveFeedsReq) returns (ga.ugc.RemoveFeedsResp) {
        option (ga.api.extension.command) = {
             id: 2571;
        };
    }
    rpc ReportVisitRecord(ga.ugc.ReportVisitRecordReq) returns (ga.ugc.ReportVisitRecordResp) {
        option (ga.api.extension.command) = {
             id: 2572;
        };
    }
    rpc AddFavourite(ga.ugc.AddFavouriteReq) returns (ga.ugc.AddFavouriteResp) {
        option (ga.api.extension.command) = {
             id: 2573;
        };
    }
    rpc ReportPostMultimediaView(ga.ugc.ReportPostMultimediaViewReq) returns (ga.ugc.ReportPostMultimediaViewResp) {
        option (ga.api.extension.command) = {
             id: 2589;
        };
    }
    rpc AudioPostScriptTabs(ga.ugc.AudioPostScriptTabsReq) returns (ga.ugc.AudioPostScriptTabsResp) {
        option (ga.api.extension.command) = {
             id: 2610;
        };
    }
    rpc AudioPostScripts(ga.ugc.AudioPostScriptsReq) returns (ga.ugc.AudioPostScriptsResp) {
        option (ga.api.extension.command) = {
             id: 2611;
        };
    }
    rpc RandomAudioPostScripts(ga.ugc.RandomAudioPostScriptsReq) returns (ga.ugc.RandomAudioPostScriptsResp) {
        option (ga.api.extension.command) = {
             id: 2612;
        };
    }
    rpc RandomAudioPostImages(ga.ugc.RandomAudioPostImagesReq) returns (ga.ugc.RandomAudioPostImagesResp) {
        option (ga.api.extension.command) = {
             id: 2613;
        };
    }
    rpc AudioPostImages(ga.ugc.AudioPostImagesReq) returns (ga.ugc.AudioPostImagesResp) {
        option (ga.api.extension.command) = {
             id: 2614;
        };
    }
    rpc RandomAudioPostMusics(ga.ugc.RandomAudioPostMusicsReq) returns (ga.ugc.RandomAudioPostMusicsResp) {
        option (ga.api.extension.command) = {
             id: 2615;
        };
    }
    rpc AudioPostMusics(ga.ugc.AudioPostMusicsReq) returns (ga.ugc.AudioPostMusicsResp) {
        option (ga.api.extension.command) = {
             id: 2616;
        };
    }
    rpc AudioPostMusicTabs(ga.ugc.AudioPostMusicTabsReq) returns (ga.ugc.AudioPostMusicTabsResp) {
        option (ga.api.extension.command) = {
             id: 2617;
        };
    }
    rpc XunfeiSignature(ga.ugc.XunfeiSignatureReq) returns (ga.ugc.XunfeiSignatureResp) {
        option (ga.api.extension.command) = {
             id: 2618;
        };
    }
    rpc AudioPostScriptsForMobile(ga.ugc.AudioPostScriptsForMobileReq) returns (ga.ugc.AudioPostScriptsForMobileResp) {
        option (ga.api.extension.command) = {
             id: 2619;
        };
    }
    rpc DelRandList(ga.ugc.DelRandListReq) returns (ga.ugc.DelRandListResp) {
        option (ga.api.extension.command) = {
             id: 2620;
        };
    }
    rpc AudioPostScriptTabsV2(ga.ugc.AudioPostScriptTabsV2Req) returns (ga.ugc.AudioPostScriptTabsV2Resp) {
        option (ga.api.extension.command) = {
             id: 2621;
        };
    }
    rpc AudioPostScriptsV2(ga.ugc.AudioPostScriptsV2Req) returns (ga.ugc.AudioPostScriptsV2Resp) {
        option (ga.api.extension.command) = {
             id: 2622;
        };
    }
    rpc RandomAudioPostScriptsV2(ga.ugc.RandomAudioPostScriptsV2Req) returns (ga.ugc.RandomAudioPostScriptsV2Resp) {
        option (ga.api.extension.command) = {
             id: 2623;
        };
    }
    rpc RandomAudioPostImagesV2(ga.ugc.RandomAudioPostImagesV2Req) returns (ga.ugc.RandomAudioPostImagesV2Resp) {
        option (ga.api.extension.command) = {
             id: 2624;
        };
    }
    rpc AudioPostImagesV2(ga.ugc.AudioPostImagesV2Req) returns (ga.ugc.AudioPostImagesV2Resp) {
        option (ga.api.extension.command) = {
             id: 2625;
        };
    }
    rpc RandomAudioPostMusicsV2(ga.ugc.RandomAudioPostMusicsV2Req) returns (ga.ugc.RandomAudioPostMusicsV2Resp) {
        option (ga.api.extension.command) = {
             id: 2626;
        };
    }
    rpc AudioPostMusicsV2(ga.ugc.AudioPostMusicsV2Req) returns (ga.ugc.AudioPostMusicsV2Resp) {
        option (ga.api.extension.command) = {
             id: 2627;
        };
    }
    rpc AudioPostMusicTabsV2(ga.ugc.AudioPostMusicTabsV2Req) returns (ga.ugc.AudioPostMusicTabsV2Resp) {
        option (ga.api.extension.command) = {
             id: 2628;
        };
    }
    rpc XunfeiSignatureV2(ga.ugc.XunfeiSignatureV2Req) returns (ga.ugc.XunfeiSignatureV2Resp) {
        option (ga.api.extension.command) = {
             id: 2629;
        };
    }
    rpc AudioPostScriptsForMobileV2(ga.ugc.AudioPostScriptsForMobileV2Req) returns (ga.ugc.AudioPostScriptsForMobileV2Resp) {
        option (ga.api.extension.command) = {
             id: 2630;
        };
    }
    rpc DelRandListV2(ga.ugc.DelRandListV2Req) returns (ga.ugc.DelRandListV2Resp) {
        option (ga.api.extension.command) = {
             id: 2631;
        };
    }
    rpc GetAttentionPeople(ga.ugc.AttentionPeopleReq) returns (ga.ugc.AttentionPeopleRsp) {
        option (ga.api.extension.command) = {
             id: 2900;
        };
    }
    rpc IsKolExist(ga.ugc.IsKolExistReq) returns (ga.ugc.IsKolExistRsp) {
        option (ga.api.extension.command) = {
             id: 2901;
        };
    }
    rpc HideHighContent(ga.ugc.HideHighContentReq) returns (ga.ugc.HideHighContentRsp) {
        option (ga.api.extension.command) = {
             id: 2902;
        };
    }
    rpc GetPublisherTopicByUid(ga.ugc.GetPublisherTopicByUidReq) returns (ga.ugc.GetPublisherTopicByUidResp) {
        option (ga.api.extension.command) = {
             id: 30145;
        };
    }
    rpc UpdatePostPrivacyPolicy(ga.ugc.UpdatePostPrivacyPolicyReq) returns (ga.ugc.UpdatePostPrivacyPolicyResp) {
        option (ga.api.extension.command) = {
             id: 2903;
        };
    }
    rpc GetNewestPosts(ga.ugc.GetNewestPostsReq) returns (ga.ugc.GetNewestPostsResp) {
        option (ga.api.extension.command) = {
             id: 30147;
        };
    }
    rpc GetFeedsByPostIDs(ga.ugc.GetFeedsByPostIDsReq) returns (ga.ugc.GetNewsFeedsResp) {
        option (ga.api.extension.command) = {
             id: 30148;
        };
    }
    rpc TopicInRecommendFeed(ga.ugc.TopicInRecommendFeedReq) returns (ga.ugc.TopicInRecommendFeedResp) {
        option (ga.api.extension.command) = {
             id: 30149;
        };
    }
    rpc GetTopicAds(ga.ugc.GetTopicAdsReq) returns (ga.ugc.GetTopicAdsResp) {
        option (ga.api.extension.command) = {
             id: 2604;
        };
    }
    rpc GetMoonDetail(ga.ugc.GetMoonDetailReq) returns (ga.ugc.GetMoonDetailRsp) {
        option (ga.api.extension.command) = {
             id: 2606;
        };
    }
    rpc GetMoodConfig(ga.ugc.GetMoodConfigReq) returns (ga.ugc.GetMoodConfigRsp) {
        option (ga.api.extension.command) = {
             id: 2607;
        };
    }
    rpc GetTabConfig(ga.ugc.GetTabConfigReq) returns (ga.ugc.GetTabConfigRsp) {
        option (ga.api.extension.command) = {
             id: 2608;
        };
    }
    rpc ReportUnrelatedTopic(ga.ugc.ReportUnrelatedTopicReq) returns (ga.ugc.ReportUnrelatedTopicRsp) {
        option (ga.api.extension.command) = {
             id: 2609;
        };
    }
    rpc VotePost(ga.ugc.VotePostReq) returns (ga.ugc.VotePostResp) {
        option (ga.api.extension.command) = {
             id: 2574;
        };
    }
    rpc GetGameEntrance(ga.ugc.GetGameEntranceReq) returns (ga.ugc.GetGameEntranceResp) {
        option (ga.api.extension.command) = {
             id: 2575;
        };
    }
    rpc GetPostButtonGuide(ga.ugc.GetPostButtonGuideReq) returns (ga.ugc.GetPostButtonGuideResp) {
        option (ga.api.extension.command) = {
             id: 37001;
        };
    }
    rpc GetUgcContentFeedbackOptions(ga.ugc.GetUgcContentFeedbackOptionsReq) returns (ga.ugc.GetUgcContentFeedbackOptionsResp) {
        option (ga.api.extension.command) = {
            id: 37002;
        };
    }
    rpc ReportUgcContentFeedback(ga.ugc.ReportUgcContentFeedbackReq) returns (ga.ugc.ReportUgcContentFeedbackResp) {
        option (ga.api.extension.command) = {
            id: 37003;
        };
    }
}


