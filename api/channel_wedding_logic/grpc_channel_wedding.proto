syntax = "proto3";
package ga.api.channel_wedding_logic;

import "channel_wedding_logic/channel_wedding_logic.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/channel_wedding_logic;channel_wedding_logic";

service ChannelWeddingLogic {
  option (ga.api.extension.logic_service_name) = "channel-wedding-logic";
  option (ga.api.extension.logic_service_language) = "go";
  option (ga.api.extension.logic_service_uri_rewrite) = "/logic.ChannelWeddingLogic/";

    // 获取婚礼房阶段信息
    rpc GetGetWeddingInfo (ga.channel_wedding_logic.GetChannelWeddingInfoRequest) returns (ga.channel_wedding_logic.GetChannelWeddingInfoResponse) {
        option (ga.api.extension.command) = {
            id: 51311;
       };
    }

    // 切换婚礼房阶段
    rpc SwitchWeddingStage (ga.channel_wedding_logic.SwitchWeddingStageRequest) returns (ga.channel_wedding_logic.SwitchWeddingStageResponse) {
        option (ga.api.extension.command) = {
            id: 51312;
       };
    }

    // 拍婚礼合照
    rpc TakeWeddingGroupPhoto (ga.channel_wedding_logic.TakeWeddingGroupPhotoRequest) returns (ga.channel_wedding_logic.TakeWeddingGroupPhotoResponse) {
        option (ga.api.extension.command) = {
            id: 51313;
       };
    }

    // 申请参加/取消申请
    rpc ApplyToJoinChairGame (ga.channel_wedding_logic.ApplyToJoinChairGameRequest) returns (ga.channel_wedding_logic.ApplyToJoinChairGameResponse) {
        option (ga.api.extension.command) = {
            id: 51314;
       };
    }

    // 获取报名列表
    rpc GetChairGameApplyList (ga.channel_wedding_logic.GetChairGameApplyListRequest) returns (ga.channel_wedding_logic.GetChairGameApplyListResponse) {
        option (ga.api.extension.command) = {
            id: 51315;
       };
    }

    // 获取游戏信息（进程、奖励、参与人员）
    rpc GetChairGameInfo (ga.channel_wedding_logic.GetChairGameInfoRequest) returns (ga.channel_wedding_logic.GetChairGameInfoResponse) {
        option (ga.api.extension.command) = {
            id: 51316;
       };
    }

    // 玩家抢椅子
    rpc GrabChair (ga.channel_wedding_logic.GrabChairRequest) returns (ga.channel_wedding_logic.GrabChairResponse) {
        option (ga.api.extension.command) = {
            id: 51317;
       };
    }

    // 开启新一局抢椅子游戏
    rpc StartChairGame (ga.channel_wedding_logic.StartChairGameRequest) returns (ga.channel_wedding_logic.StartChairGameResponse) {
        option (ga.api.extension.command) = {
            id: 51318;
       };
    }

    // 进入下一轮
    rpc SetChairGameToNextRound (ga.channel_wedding_logic.SetChairGameToNextRoundRequest) returns (ga.channel_wedding_logic.SetChairGameToNextRoundResponse) {
        option (ga.api.extension.command) = {
            id: 51319;
       };
    }

    // 本轮开抢
    rpc StartGrabChair (ga.channel_wedding_logic.StartGrabChairRequest) returns (ga.channel_wedding_logic.StartGrabChairResponse) {
        option (ga.api.extension.command) = {
            id: 51320;
       };
    }

    // 获取用户的婚礼姿势组件
    rpc GetUserWeddingPose (ga.channel_wedding_logic.GetUserWeddingPoseRequest) returns (ga.channel_wedding_logic.GetUserWeddingPoseResponse) {
        option (ga.api.extension.command) = {
            id: 51321;
       };
    }

    // 设置用户使用的婚礼姿势组件
    rpc SetUserInuseWeddingPose (ga.channel_wedding_logic.SetUserInuseWeddingPoseRequest) returns (ga.channel_wedding_logic.SetUserInuseWeddingPoseResponse) {
        option (ga.api.extension.command) = {
            id: 51322;
       };
    }

    // 批量获取用户使用中的婚礼姿势组件
    rpc BatchGetUserInuseWeddingPose (ga.channel_wedding_logic.BatchGetUserInuseWeddingPoseRequest) returns (ga.channel_wedding_logic.BatchGetUserInuseWeddingPoseResponse) {
        option (ga.api.extension.command) = {
            id: 51323;
       };
    }

    // 获取房间合照麦位位置映射请求
    rpc GetWeddingGroupPhotoSeatMap (ga.channel_wedding_logic.GetWeddingGroupPhotoSeatMapRequest) returns (ga.channel_wedding_logic.GetWeddingGroupPhotoSeatMapResponse) {
        option (ga.api.extension.command) = {
            id: 51324;
       };
    }

    // 设置用户合照位置
    rpc SetUserWeddingGroupPhotoSeat (ga.channel_wedding_logic.SetUserWeddingGroupPhotoSeatRequest) returns (ga.channel_wedding_logic.SetUserWeddingGroupPhotoSeatResponse) {
        option (ga.api.extension.command) = {
            id: 51325;
       };
    }

    // 新人设置抢椅子游戏奖励
    rpc SetChairGameReward (ga.channel_wedding_logic.SetChairGameRewardRequest) returns (ga.channel_wedding_logic.SetChairGameRewardResponse) {
        option (ga.api.extension.command) = {
            id: 51326;
       };
    }

    // 批量获取用户服装信息请求
    rpc BatchGetUserWeddingClothes (ga.channel_wedding_logic.BatchGetUserWeddingClothesRequest) returns (ga.channel_wedding_logic.BatchGetUserWeddingClothesResponse) {
      option (ga.api.extension.command) = {
        id: 51327;
      };
    }

    // 获取抢椅子游戏奖励设置
    rpc GetChairGameRewardSetting (ga.channel_wedding_logic.GetChairGameRewardSettingRequest) returns (ga.channel_wedding_logic.GetChairGameRewardSettingResponse) {
      option (ga.api.extension.command) = {
        id: 51328;
      };
    }

    // 设置用户使用的婚礼形象朝向
    rpc SetUserWeddingOrientation (ga.channel_wedding_logic.SetUserWeddingOrientationRequest) returns (ga.channel_wedding_logic.SetUserWeddingOrientationResponse) {
      option (ga.api.extension.command) = {
        id: 51329;
      };
    }

    // 获取婚礼页信息
    rpc GetWeddingSchedulePageInfo (ga.channel_wedding_logic.GetWeddingSchedulePageInfoRequest) returns (ga.channel_wedding_logic.GetWeddingSchedulePageInfoResponse) {
      option (ga.api.extension.command) = {
        id: 51330;
      };
    }
    // 购买婚礼
    rpc BuyWedding (ga.channel_wedding_logic.BuyWeddingRequest) returns (ga.channel_wedding_logic.BuyWeddingResponse) {
      option (ga.api.extension.command) = {
        id: 51331;
      };
    }
    // 取消婚礼
    rpc CancelWedding (ga.channel_wedding_logic.CancelWeddingRequest) returns (ga.channel_wedding_logic.CancelWeddingResponse) {
      option (ga.api.extension.command) = {
        id: 51332;
      };
    }

  // 获取仪式大厅列表
  rpc GetWeddingHallList (ga.channel_wedding_logic.GetWeddingHallListRequest) returns (ga.channel_wedding_logic.GetWeddingHallListResponse) {
    option (ga.api.extension.command) = {
      id: 51333;
    };
  }

  // 订阅婚礼（跟预约婚礼用不同叫法）
  rpc SubscribeWedding (ga.channel_wedding_logic.SubscribeWeddingRequest) returns (ga.channel_wedding_logic.SubscribeWeddingResponse) {
    option (ga.api.extension.command) = {
      id: 51334;
    };
  }

  // 获取婚礼房入口开关
  rpc GetWeddingEntrySwitch (ga.channel_wedding_logic.GetWeddingEntrySwitchRequest) returns (ga.channel_wedding_logic.GetWeddingEntrySwitchResponse) {
    option (ga.api.extension.command) = {
      id: 51335;
    };
  }

  // 获取婚礼大屏
  rpc GetWeddingBigScreen (ga.channel_wedding_logic.GetWeddingBigScreenRequest) returns (ga.channel_wedding_logic.GetWeddingBigScreenResponse) {
    option (ga.api.extension.command) = {
      id: 51336;
    };
  }

  // 保存婚礼大屏
  rpc SaveWeddingBigScreen (ga.channel_wedding_logic.SaveWeddingBigScreenRequest) returns (ga.channel_wedding_logic.SaveWeddingBigScreenResponse) {
    option (ga.api.extension.command) = {
      id: 51337;
    };
  }

  // GetWeddingInviteInfo 获取婚礼邀请信息
  rpc GetWeddingInviteInfo (ga.channel_wedding_logic.GetWeddingInviteInfoRequest) returns (ga.channel_wedding_logic.GetWeddingInviteInfoResponse) {
    option (ga.api.extension.command) = {
      id: 51338;
    };
  }

  // HandleWeddingInvite 处理婚礼邀请
  rpc HandleWeddingInvite (ga.channel_wedding_logic.HandleWeddingInviteRequest) returns (ga.channel_wedding_logic.HandleWeddingInviteResponse) {
    option (ga.api.extension.command) = {
      id: 51339;
    };
  }

  // ApplyEndWeddingRelationship 申请结束婚礼关系
  rpc ApplyEndWeddingRelationship (ga.channel_wedding_logic.ApplyEndWeddingRelationshipRequest) returns (ga.channel_wedding_logic.ApplyEndWeddingRelationshipResponse) {
    option (ga.api.extension.command) = {
      id: 51340;
    };
  }

  // CancelEndWeddingRelationship 取消申请结束婚礼关系
  rpc CancelEndWeddingRelationship (ga.channel_wedding_logic.CancelEndWeddingRelationshipRequest) returns (ga.channel_wedding_logic.CancelEndWeddingRelationshipResponse) {
      option (ga.api.extension.command) = {
        id: 51341;
      };
  }

  // deprecated DirectEndWeddingRelationship 直接结束婚礼关系
  rpc DirectEndWeddingRelationship (ga.channel_wedding_logic.DirectEndWeddingRelationshipRequest) returns (ga.channel_wedding_logic.DirectEndWeddingRelationshipResponse) {
    option (ga.api.extension.command) = {
      id: 51342;
    };
  }

  // GetProposeList 获取求婚用户列表
  rpc GetProposeList (ga.channel_wedding_logic.GetProposeListRequest) returns (ga.channel_wedding_logic.GetProposeListResponse) {
    option (ga.api.extension.command) = {
      id: 51343;
    };
  }


  // SendPropose 发送求婚请求
  rpc SendPropose (ga.channel_wedding_logic.SendProposeRequest) returns (ga.channel_wedding_logic.SendProposeResponse) {
    option (ga.api.extension.command) = {
      id: 51344;
    };
  }

  // HandlePropose 处理求婚请求
  rpc HandlePropose (ga.channel_wedding_logic.HandleProposeRequest) returns (ga.channel_wedding_logic.HandleProposeResponse) {
    option (ga.api.extension.command) = {
      id: 51345;
    };
  }

  rpc GetProposeById (ga.channel_wedding_logic.GetProposeByIdRequest) returns (ga.channel_wedding_logic.GetProposeByIdResponse) {
    option (ga.api.extension.command) = {
      id: 51346;
    };
  }

  rpc GetSendPropose (ga.channel_wedding_logic.GetSendProposeRequest) returns (ga.channel_wedding_logic.GetSendProposeResponse) {
    option (ga.api.extension.command) = {
      id: 51347;
    };
  }

  // 获取用户婚礼沉淀信息
  rpc GetUserWeddingPrecipitation (ga.channel_wedding_logic.GetUserWeddingPrecipitationRequest) returns (ga.channel_wedding_logic.GetUserWeddingPrecipitationResponse) {
    option (ga.api.extension.command) = {
      id: 51348;
    };
  }

  // 上报婚礼场景片段图片
  rpc ReportWeddingScenePic (ga.channel_wedding_logic.ReportWeddingScenePicRequest) returns (ga.channel_wedding_logic.ReportWeddingScenePicResponse) {
    option (ga.api.extension.command) = {
      id: 51349;
    };
  }

  // 手动隐藏婚礼关系
  rpc HideWeddingRelation (ga.channel_wedding_logic.HideWeddingRelationRequest) returns (ga.channel_wedding_logic.HideWeddingRelationResponse) {
    option (ga.api.extension.command) = {
      id: 51350;
    };
  }

  // 获取婚礼预览资源
  rpc GetWeddingPreviewResource (ga.channel_wedding_logic.GetWeddingPreviewResourceRequest) returns (ga.channel_wedding_logic.GetWeddingPreviewResourceResponse) {
    option (ga.api.extension.command) = {
      id: 51460;
    };
  }


  // 获取婚礼高光时刻礼物
  rpc GetWeddingHighLightPresent (ga.channel_wedding_logic.GetWeddingHighLightPresentRequest) returns (ga.channel_wedding_logic.GetWeddingHighLightPresentResponse) {
    option (ga.api.extension.command) = {
      id: 51461;
    };
  }

  // 获取房间爱侣榜入口
  rpc GetWeddingRankEntry (ga.channel_wedding_logic.GetWeddingRankEntryRequest) returns (ga.channel_wedding_logic.GetWeddingRankEntryResponse) {
    option (ga.api.extension.command) = {
      id: 51462;
    };
  }

  // 发送付费预约礼物
  rpc SendWeddingReservePresent (ga.channel_wedding_logic.SendWeddingReservePresentRequest) returns (ga.channel_wedding_logic.SendWeddingReservePresentResponse) {
    option (ga.api.extension.command) = {
      id: 51463;
    };
  }

  // 获取婚礼主题列表请求
  rpc GetWeddingThemeCfgList (ga.channel_wedding_logic.GetWeddingThemeCfgListRequest) returns (ga.channel_wedding_logic.GetWeddingThemeCfgListResponse) {
    option (ga.api.extension.command) = {
      id: 51464;
    };
  }

  // 撤回求婚
  rpc RevokePropose (ga.channel_wedding_logic.RevokeProposeRequest) returns (ga.channel_wedding_logic.RevokeProposeResponse) {
    option (ga.api.extension.command) = {
      id: 51465;
    };
  }

  // 批量查询用户的在房状态
  rpc GetUserInRoomStatus (ga.channel_wedding_logic.GetUserInRoomStatusRequest) returns (ga.channel_wedding_logic.GetUserInRoomStatusResponse) {
    option (ga.api.extension.command) = {
      id: 51466;
    };
  }

  // 获取进行中的婚礼
  rpc GetGoingWeddingEntry (ga.channel_wedding_logic.GetGoingWeddingEntryRequest) returns (ga.channel_wedding_logic.GetGoingWeddingEntryResponse) {
    option (ga.api.extension.command) = {
      id: 51467;
    };
  }

  // 提醒用户进房
  rpc RemindUserJoinWeddingRoom (ga.channel_wedding_logic.RemindUserJoinWeddingRoomRequest) returns (ga.channel_wedding_logic.RemindUserJoinWeddingRoomResponse) {
    option (ga.api.extension.command) = {
      id: 51468;
    };
  }

  // 婚礼房上mvp麦
//  rpc ChannelWeddingHoldMvpMic (ga.channel_wedding_logic.ChannelWeddingHoldMvpMicRequest) returns (ga.channel_wedding_logic.ChannelWeddingHoldMvpMicResponse) {
//    option (ga.api.extension.command) = {
//      id: 51469;
//    };
//  }

  // 获取婚礼礼物
  rpc GetWeddingPresent (ga.channel_wedding_logic.GetWeddingPresentRequest) returns (ga.channel_wedding_logic.GetWeddingPresentResponse) {
    option (ga.api.extension.command) = {
      id: 51470;
    };
  }

  // 获取婚礼进度信息
  rpc GetWeddingPreProgressInfo (ga.channel_wedding_logic.GetWeddingPreProgressInfoRequest) returns (ga.channel_wedding_logic.GetWeddingPreProgressInfoResponse) {
    option (ga.api.extension.command) = {
      id: 51471;
    };
  }

  // 开始婚礼
  rpc StartWedding (ga.channel_wedding_logic.StartWeddingRequest) returns (ga.channel_wedding_logic.StartWeddingResponse) {
    option (ga.api.extension.command) = {
      id: 51472;
    };
  }

  // 取消准备婚礼
  rpc CancelPreparedWedding (ga.channel_wedding_logic.CancelPreparedWeddingRequest) returns (ga.channel_wedding_logic.CancelPreparedWeddingResponse) {
    option (ga.api.extension.command) = {
      id: 51473;
    };
  }

  // 获取婚礼准备信息
  rpc GetWeddingPrepareInfo (ga.channel_wedding_logic.GetWeddingPrepareInfoRequest) returns (ga.channel_wedding_logic.GetWeddingPrepareInfoResponse) {
    option (ga.api.extension.command) = {
      id: 51474;
    };
  }
}