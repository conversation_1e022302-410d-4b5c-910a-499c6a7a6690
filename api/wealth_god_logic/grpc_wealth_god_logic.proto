syntax = "proto3";
package ga.api.wealth_god_logic;

import "wealth_god_logic/wealth_god_logic.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/wealth_god_logic;wealth_god_logic";

service WealthGodLogic {
  option (ga.api.extension.logic_service_name) = "wealth-god-logic";
  option (ga.api.extension.logic_service_language) = "go";
  option (ga.api.extension.logic_service_uri_rewrite) = "/logic.WealthGodLogic/";

  // 获取财神入口信息
  rpc GetWealthGodEntry(ga.wealth_god_logic.GetWealthGodEntryRequest) returns (ga.wealth_god_logic.GetWealthGodEntryResponse) {
    option (ga.api.extension.command) = {
      id: 51501;
    };
  }

  // 获取一个财神降临房间
  rpc GetOneWealthGodChannel(ga.wealth_god_logic.GetOneWealthGodChannelReq) returns (ga.wealth_god_logic.GetOneWealthGodChannelResp) {
    option (ga.api.extension.command) = {
      id: 51502;
    };
  }

  // 获取财神活动介绍
  rpc GetWealthGodActivityInfo(ga.wealth_god_logic.GetWealthGodActivityInfoRequest) returns (ga.wealth_god_logic.GetWealthGodActivityInfoResponse) {
    option (ga.api.extension.command) = {
      id: 51503;
    };
  }

  // 获取财神详情
  rpc GetWealthGodDetail(ga.wealth_god_logic.GetWealthGodDetailRequest) returns (ga.wealth_god_logic.GetWealthGodDetailResponse) {
    option (ga.api.extension.command) = {
      id: 51504;
    };
  }

  // 开启财神宝箱奖励
  rpc OpenWealthGodBoxReward(ga.wealth_god_logic.OpenWealthGodBoxRewardRequest) returns (ga.wealth_god_logic.OpenWealthGodBoxRewardResponse) {
    option (ga.api.extension.command) = {
      id: 51505;
    };
  };

  // 上报财神降临房间任务完成
  rpc ReportStayRoomMissionFinish(ga.wealth_god_logic.ReportStayRoomMissionFinishRequest) returns (ga.wealth_god_logic.ReportStayRoomMissionFinishResponse) {
    option (ga.api.extension.command) = {
      id: 51506;
    };
  }

  // 获取财神通用配置
  rpc GetWealthGodCommonCfg(ga.wealth_god_logic.GetWealthGodCommonCfgRequest) returns (ga.wealth_god_logic.GetWealthGodCommonCfgResponse) {
    option (ga.api.extension.command) = {
      id: 51500;
    };
  }
}
