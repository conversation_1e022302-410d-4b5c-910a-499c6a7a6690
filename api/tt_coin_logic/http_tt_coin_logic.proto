syntax = "proto3";

package ga.api.tt_coin_logic;

import "api/extension/extension.proto";
import "google/api/annotations.proto";
import "google/protobuf/struct.proto";
import "tt_coin_logic/tt_coin_logic.proto";

option go_package = "golang.52tt.com/protocol/app/api/tt_coin_logic;tt_coin_logic";
option java_package = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";

service HTTPTBeanLogic {
  option (ga.api.extension.logic_service_language) = "go";
  option (ga.api.extension.logic_service_name) = "tbean-coin-logic";

  // /portal/rest/api/getBalance.shtml
  // 获取用户T豆余额
  rpc GetBalanceByWeb(GetBalanceByWebRequest) returns (GetBalanceByWebResponse) {
    option (google.api.http) = {
      post: "/coin/api/GetBalance"
      body: "*"
    };
  }

  // /portal/recharge/record/getCcoList.shtml
  //获取用户消费明细
  rpc GetConsumeHistory(GetConsumeHistoryRequest) returns (GetConsumeHistoryResponse) {
    option (google.api.http) = {
      post: "/coin/api/GetConsumeHistory"
      body: "*"
    };
  }

  // /portal/recharge/getAd.shtml
  //获取充值广告（商品页中加载）
  rpc GetRechargeAd(GetRechargeAdRequest) returns (GetRechargeAdResponse) {
    option (google.api.http) = {
      post: "/coin/api/GetRechargeAd"
      body: "*"
    };
  }

  // /portal/recharge/record/getList.shtml
  //获取用户充值明细
  rpc GetRechargeRecordList(GetRechargeRecordListRequest) returns (GetRechargeRecordListResponse) {
    option (google.api.http) = {
      post: "/coin/api/GetRechargeRecordList"
      body: "*"
    };
  }

  // /portal/api/record/getFreezeList.shtml
  //查询T豆冻结明细
  rpc GetFreezeHistory(GetFreezeHistoryRequest) returns (GetFreezeHistoryResponse) {
    option (google.api.http) = {
      post: "/coin/api/GetFreezeHistory"
      body: "*"
    };
  }

  // /portal/goods/charge/info.shtml
  //获取充值信息
  rpc GetRechargeInfo(GetRechargeInfoRequest) returns (GetRechargeInfoResponse) {
    option (google.api.http) = {
      post: "/coin/api/GetRechargeInfo"
      body: "*"
    };
  }

  // /portal/record/firstRechargeCheck.shtml
  // 判断用户是否有首充资格
  rpc CheckFirstRecharge(CheckFirstRechargeRequest) returns (CheckFirstRechargeResponse) {
    option (google.api.http) = {
      post: "/coin/api/CheckFirstRecharge"
      body: "*"
    };
  }

  // /portal/record/account/info.shtml
  // 获取交易记录页用户信息
  rpc GetAccountInfo(GetAccountInfoRequest) returns (GetAccountInfoResponse) {
    option (google.api.http) = {
      post: "/coin/api/GetAccountInfo"
      body: "*"
    };
  }

  // /portal/api/app/web/getWebGoodsList.shtml
  // 安卓全屏获取商品列表
  // +
  // /portal/alipayWapRecharge/getWareList.shtml
  // 端外充值链接-获取商品清单
  rpc GetWebGoodsList(GetWebGoodsListRequest) returns (GetWebGoodsListResponse) {
    option (google.api.http) = {
      post: "/coin/api/GetWebGoodsList"
      body: "*"
    };
  }

  // /portal/refund/
  // 由i2c发生的退还T豆记录
  rpc GetRefundList(GetRefundListRequest) returns (GetRefundListResponse) {
    option (google.api.http) = {
      post: "/coin/api/GetRefundList"
      body: "*"
    };
  }

  // /portal/apply/identify/faceResult.shtml
  // 人脸认证结果上报
  rpc FaceIdentifyResult(FaceIdentifyResultRequest) returns (FaceIdentifyResultResponse) {
    option (google.api.http) = {
      post: "/coin/api/FaceIdentifyResult"
      body: "*"
    };
  }

  // /portal/recharge/api/r2/save.shtml
  // 全屏充值商品下单接口（新版）
  rpc CreateAppWebOrder(CreateAppWebOrderRequest) returns (CreateAppWebOrderResponse) {
    option (google.api.http) = {
      post: "/coin/api/CreateAppWebOrder"
      body: "*"
    };
  }

  // /portal/alipayWap/rest.shtml
  // 支付宝端外下单
  rpc CreateAlipayWapOrder(CreateAlipayWapOrderRequest) returns (CreateAlipayWapOrderResponse) {
    option (google.api.http) = {
      post: "/coin/api/CreateAlipayWapOrder"
      body: "*"
    };
  }

  // 获取用户最近一笔的支付渠道
  rpc GetLastChargeChannelForWap(GetLastChargeChannelForWapRequest) returns (GetLastChargeChannelForWapResponse) {
    option (google.api.http) = {
      post: "/coin/api/GetLastChargeChannelForWap"
      body: "*"
    };
  }

  //  获取支付营销活动信息
  rpc GetPayActivityForWeb(GetPayActivityForWebRequest) returns (GetPayActivityForWebResponse) {
    option (google.api.http) = {
      post: "/coin/api/GetPayActivity"
      body: "*"
    };
  }
}

message GetLastChargeChannelForWapRequest {
  //uid
  uint32 uid = 1;
}

message GetLastChargeChannelForWapResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  //data
  LastChargeChannel data = 5;

  message LastChargeChannel {
    //uid
    uint64 uid = 1;
    //支付渠道
    string pay_channel = 2;
    // 订单号
    string order_no = 3;
    // 订单金额
    int64 amount = 4;
  }
}

message GetBalanceByWebRequest {
  //uid
  uint32 uid = 1;
}

message GetBalanceByWebResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  //data
  Balance data = 4;

  message Balance {
    //balance
    int64 balance = 1;
  }
}

message GetConsumeHistoryRequest {
  //page
  uint32 page_index = 1;
  //page_count
  uint32 page_count = 2;
}
message GetConsumeHistoryResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  //data
  ConsumeRecordList data = 4;

  message ConsumeRecordList {
    //list
    repeated ConsumeRecord list = 1;
  }

  //消费记录
  message ConsumeRecord {
    //title
    string title = 1;
    //price
    string price = 2;
    //spend_time
    string spend_time = 3;
  }
}

message GetRechargeAdRequest {}
message GetRechargeAdResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  //data
  Banner data = 3;

  message Banner {
    //Banner
    string banner = 1;
    //target
    string target = 2;
  }
}

message GetRechargeRecordListRequest {
  //page
  uint32 page_index = 1;
  //page_count
  uint32 page_count = 2;
}

message GetRechargeRecordListResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  //data
  RechargeRecordList data = 4;

  message RechargeRecordList {
    //list
    repeated RechargeRecord list = 1;
  }

  message RechargeRecord {
    //num
    uint32 num = 1;
    //price
    string price = 2;
    //status name
    string status_name = 3;
    //recharge time
    string recharge_time = 4;
    //ctime
    string ctime = 5;
  }
}

message GetFreezeHistoryRequest {
  //pageIndex
  uint32 page_index = 1;
  //pageCount
  uint32 page_count = 2;
}

message GetFreezeHistoryResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  //data
  FreezeRecordList data = 4;

  message FreezeRecordList {
    //list
    repeated FreezeRecord list = 1;
  }

  message FreezeRecord {
    //id
    string id = 1;
    //title
    string title = 2;
    //freezeBalanceStr
    string freeze_balance = 3;
    //freezeTime
    string deal_time = 4;
  }
}

message GetRechargeInfoRequest {
  //uid
  uint32 uid = 1;
}

message GetRechargeInfoResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  //data
  ChargeInfo data = 4;

  message ChargeInfo {
    //balance
    int64 balance = 1;
    //isAdult
    uint32 is_adult = 2;
    //rn
    string rn = 3;
    //token
    string token = 4;
    //account
    string uaccount = 5;
    //NickName
    string nick_name = 6;
  }
}

message CheckFirstRechargeRequest {
  // 最低金额
  string first_min_price = 1;
}

message CheckFirstRechargeResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  //data
  Data data = 4;

  message Data {
    // 是否有首充资格
    bool first_recharge_eligibility = 1;
  }
}

message GetAccountInfoRequest {}

message GetAccountInfoResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  //data
  AccountInfo data = 4;

  message AccountInfo {
    //uid
    uint32 uid = 1;
    //balance
    int64 balance = 2;
    //account
    string uaccount = 3;
    //NickName
    string nick_name = 4;
  }
}

message GetWebGoodsListRequest {
  //caller
  string caller = 1;
  // pay channel，不填默认拉安卓全屏的
  string pay_channel = 2;
  // scene code，不填默认拉BUY的
  string scene_code = 3;
}

message GetWebGoodsListResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  //data
  GoodsList data = 3;

  message GoodsList {
    //goodsList
    repeated Goods goods_list = 1;
  }

  message Goods {
    //id
    uint32 id = 1;
    //num
    uint32 num = 2;
    //amount
    string amount = 3;
    //actives
    repeated Active actives = 4;
  }

  message Active {
    //paperwork
    string paperwork = 1;
    //pay channel
    string pay_channel = 2;
    //cornerPosition
    string corner_position = 3;
    //color
    string color = 4;
    //activeCode
    string active_code = 5;
  }
}

message GetRefundListRequest {
  //page
  uint32 page_index = 1;
  //page_count
  uint32 page_count = 2;
}

message GetRefundListResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  //data
  RefundList data = 3;

  message RefundList {
    //list
    repeated Refund list = 1;
  }
}

message Refund {
  //转入金额
  string amount = 1;
  //创建时间
  string ctime = 2;
  //备注说明
  string notes = 3;
}

message FaceIdentifyResultRequest {
  // 供应商CODE
  string provider_code = 1;
  // 人脸认证的certifyId
  string certify_id = 2;
  // 人脸认证场景ID 例如：充值场景=1-1
  string scene = 3;
  //from
  string from = 4;
  //request_id
  string request_id = 5;
}

message FaceIdentifyResultResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  //data
  IdentifyResult data = 3;

  message IdentifyResult {
    //是否成功
    bool is_pass = 1;
    //certify_id
    string certify_id = 2;
    //request_id
    string request_id = 3;
    //provider_response_result
    string provider_response_result = 4;
    //provider_response_description
    string provider_response_description = 5;
    //
  }
}

message HttpBaseResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  //data
  google.protobuf.Struct data = 3;
}

message CreateAppWebOrderRequest {
  // 订单金额
  int64 amount = 1;
  // 随机数, 防止重复提交
  int64 rn = 2;
  // GetRechargeInfo-token 防止重复提交
  string recharge_token = 3;
  // 设备号
  string device_id = 5;
  // 应用标识(tt=TT语音，mj=谜境，mk=麦可，hy=欢游)
  string caller = 6;
  // APP版本
  string app_version = 7;
}

message CreateAppWebOrderResponse {
  // code
  int32 code = 1;
  // msg
  string msg = 2;
  // data
  CreateAppWebOrderData data = 3;
  // data定义
  message CreateAppWebOrderData {
    // 订单号
    string order_no = 1;
    // token
    string token = 2;
  }
  // err_data
  google.protobuf.Struct err_data = 4;
}

message CreateAlipayWapOrderRequest {
  // 订单金额
  int64 amount = 1;
  // 设备号
  string device_id = 3;
  // token--重复提交校验用
  string ali_token = 4;
  // rn--重复提交校验用
  int64 rn = 5;
  // caller 来源
  string caller = 6;
  // 业务参数
  BizParam biz_param = 7;
  message BizParam {
    // 系统版本 16.6
    string sys_version = 1;
    // 手机系统 ios/android
    string mobile_operating_platform = 2;
    // 设备类型 iphone
    string platform_type = 3;
  }
}

message CreateAlipayWapOrderResponse {
  // code
  int32 code = 1;
  // msg
  string msg = 2;
  // data-支付宝页面参数
  google.protobuf.Struct data = 3;
  // err_data
  google.protobuf.Struct err_data = 4;
}

message GetPayActivityForWebRequest {
  //caller
  string caller = 1;
  //amount 支付金额
  string amount = 2;
}

message GetPayActivityForWebResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  //data
  ActiveConfigList data = 3;

  message ActiveConfigList {
    //活动
    repeated ga.tt_coin_logic.ActiveConfig activity_list = 1;
  }

}
