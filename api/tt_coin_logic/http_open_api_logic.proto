syntax = "proto3";

package ga.api.tt_coin_logic;

import "api/extension/extension.proto";
import "api/tt_coin_logic/http_tt_coin_logic.proto";
import "google/api/annotations.proto";
import "google/protobuf/struct.proto";

option go_package = "golang.52tt.com/protocol/app/api/tt_coin_logic;tt_coin_logic";
option java_package = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";

//web端外接口 使用新的认证方式
service HTTPOpenApiLogic {
  option (ga.api.extension.logic_service_language) = "go";
  option (ga.api.extension.logic_service_name) = "open-api-coin-logic";

  //原 /portal/user/getUserByAccount.shtml
  //根据alias获取用户信息
  //同时会返回一个跟alias绑定的token 目前超时设置成了1小时
  //这个协议文件的接口除了GetUserByAlias都需要带上这个token 放到header中   --header 'authorization: bearer <token>'
  rpc GetUserByAlias(GetUserByAliasRequest) returns (GetUserByAliasResponse) {
    option (google.api.http) = {
      post: "/api/GetUserByAlias"
      body: "*"
    };
  }

  // /portal/alipayMiniRecharge/getWareList.shtml
  // 支付宝小程序-获取商品列表
  rpc GetAliPayMiniRechargeWareList(GetAliPayMiniRechargeWareListRequest) returns (GetAliPayMiniRechargeWareListResponse) {
    option (google.api.http) = {
      post: "/api/GetAliPayMiniRechargeWareList"
      body: "*"
    };
  }

  // /api/coin-inform/alipayMiniMessage/confirm
  // 支付宝消息订阅确认
  rpc AliPayMiniMessageConfirm(AliPayMiniMessageConfirmRequest) returns (AliPayMiniMessageConfirmResponse) {
    option (google.api.http) = {
      post: "/api/AliPayMiniMessageConfirm"
      body: "*"
    };
  }

  // /portal/alipayMiniRecharge/rest.shtml
  // 支付宝小程序下单
  rpc CreateAlipayMiniOrder(CreateAlipayMiniOrderRequest) returns (CreateAlipayMiniOrderResponse) {
    option (google.api.http) = {
      post: "/api/CreateAlipayMiniOrder"
      body: "*"
    };
  }

  // /portal/wcRecharge/rest.shtml
  // 微信公众号下单
  rpc CreateWCPublicOrder(CreateWCPublicOrderRequest) returns (CreateWCPublicOrderResponse) {
    option (google.api.http) = {
      post: "/api/CreateWCPublicOrder"
      body: "*"
    };
  }

  // /portal/wcRecharge/err/log.shtml
  //微信公众号下单结果上报
  rpc ReportRechargeResult(ReportRechargeResultRequest) returns (ReportRechargeResultResponse) {
    option (google.api.http) = {
      post: "/api/ReportRechargeResult"
      body: "*"
    };
  }
}

message GetUserByAliasRequest {
  //caller
  string caller = 1;
  //用户账号
  string alias = 2;
}

message GetUserByAliasResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  //data
  Data data = 4;

  message Data {
    //uid
    uint32 uid = 1;
    //nickname
    string nickname = 2;
    //username
    string username = 3;
    //token 用于其他接口鉴权
    string token = 4;
  }
}

message GetAliPayMiniRechargeWareListRequest {
  //caller
  string caller = 1;
}

message GetAliPayMiniRechargeWareListResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  //data
  GetWebGoodsListResponse.GoodsList data = 3;
}

message AliPayMiniMessageConfirmRequest {
  // caller
  string caller = 1;
  // pay channel
  string pay_channel = 2;
  // scene code
  string scene_code = 3;
  // 模板id集合 template ids like '1, 2'
  string template_ids = 4;
  // 支付宝小程序授权码 code
  string code = 5;
  // 跳转页面 page
  string page = 6;
}

message AliPayMiniMessageConfirmResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  // data
  ConfirmData data = 3;

  message ConfirmData {
    // 解密后的 uid
    string uid = 1;
    // 模板id集合 template ids
    string template_ids = 2;
  }
}

message CreateWCPublicOrderRequest {
  // 订单金额
  int64 amount = 1;
  // 微信公众号code
  string wc_code = 2;
  // 应用标识(tt=TT语音，mj=谜境，mk=麦可，hy=欢游)
  string caller = 6;
}

message CreateWCPublicOrderResponse {
  // code
  int32 code = 1;
  // msg
  string msg = 2;
  // data-微信的支付参数
  google.protobuf.Struct data = 3;
  // err_data
  google.protobuf.Struct err_data = 4;
}

message CreateAlipayMiniOrderRequest {
  // 订单金额
  int64 amount = 1;
  // 随机数, 防止重复提交
  int64 rn = 2;
  // token-支付宝重复检验
  string ali_token = 3;
  // ip
  string ip = 4;
  // code-支付宝小程序code
  string ali_code = 5;
  // 应用标识(tt=TT语音，mj=谜境，mk=麦可，hy=欢游)
  string caller = 6;
}

message CreateAlipayMiniOrderResponse {
  // code
  int32 code = 1;
  // msg
  string msg = 2;
  // data-支付宝页面参数
  google.protobuf.Struct data = 3;
  // err_data
  google.protobuf.Struct err_data = 4;
}

message ReportRechargeResultRequest {
  //上报数据
  google.protobuf.Struct report_data = 1;
}

message ReportRechargeResultResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
}
