syntax = "proto3";

package ga.api.tt_coin_logic;

import "api/extension/extension.proto";
import "tt_coin_logic/tt_coin_logic.proto";

option go_package = "golang.52tt.com/protocol/app/api/tt_coin_logic;tt_coin_logic";
option java_package = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";

service TTCoinLogic {
  option (ga.api.extension.logic_service_language) = "go";
  option (ga.api.extension.logic_service_name) = "tt-coin-logic";

  // 获取用户T豆余额
  rpc GetBalance(ga.tt_coin_logic.GetBalanceRequest) returns (ga.tt_coin_logic.GetBalanceResponse) {
    option (ga.api.extension.command) = {id: 51351};
  }

  // 安卓半屏充值
  rpc CreateInAppOrder(ga.tt_coin_logic.CreateInAppOrderRequest) returns (ga.tt_coin_logic.CreateInAppOrderResponse) {
    option (ga.api.extension.command) = {id: 51352};
  }

  // 半屏充值获取商品(明文)
  rpc GetInAppGoodsList(ga.tt_coin_logic.GetInAppGoodsListRequest) returns (ga.tt_coin_logic.GetInAppGoodsListResponse) {
    option (ga.api.extension.command) = {id: 51353};
  }

  // 查询用户累计充值总金额
  rpc GetUserAccumulateRecharge(ga.tt_coin_logic.GetUserAccumulateRechargeRequest) returns (ga.tt_coin_logic.GetUserAccumulateRechargeResponse) {
    option (ga.api.extension.command) = {id: 51354};
  }

  // 获取苹果商品信息
  rpc GetAppstoreProduct(ga.tt_coin_logic.GetAppstoreProductRequest) returns (ga.tt_coin_logic.GetAppstoreProductResponse) {
    option (ga.api.extension.command) = {id: 51355};
  }

  // 获取苹果token
  rpc GetAppAccountToken(ga.tt_coin_logic.GetAppAccountTokenRequest) returns (ga.tt_coin_logic.GetAppAccountTokenResponse) {
    option (ga.api.extension.command) = {id: 51356};
  }

  // 交易明细页
  rpc GetRechargeRecord(ga.tt_coin_logic.GetRechargeRecordRequest) returns (ga.tt_coin_logic.GetRechargeRecordResponse) {
    option (ga.api.extension.command) = {id: 51357};
  }

  // 人脸认证结果上报
  rpc FaceResult(ga.tt_coin_logic.FaceResultRequest) returns (ga.tt_coin_logic.FaceResultResponse) {
    option (ga.api.extension.command) = {id: 51358};
  }

  // ios半屏充值
  rpc CreateInIosAppOrder(ga.tt_coin_logic.CreateInIosAppOrderRequest) returns (ga.tt_coin_logic.CreateInIosAppOrderResponse) {
    option (ga.api.extension.command) = {id: 51359};
  }

  //获取各APP IOS提审版本
  rpc GetIOSVoldemortedVersion(ga.tt_coin_logic.GetIOSVoldemortedVersionRequest) returns (ga.tt_coin_logic.GetIOSVoldemortedVersionResponse){
    option (ga.api.extension.command) = {id: 51360};
  }

  // 用户最后一次充值渠道
  rpc GetLastChargeChannel (ga.tt_coin_logic.GetLastChargeChannelRequest) returns (ga.tt_coin_logic.GetLastChargeChannelResponse) {
    option (ga.api.extension.command) = {id: 51375};
  }

  // 安卓半屏支付
  rpc AndroidHalfOrderPay(ga.tt_coin_logic.AndroidHalfOrderPayRequest) returns (ga.tt_coin_logic.AndroidHalfOrderPayResponse) {
    option (ga.api.extension.command) = {id: 51368};
  }

  // 获取支付营销活动信息
  rpc GetPayActivity(ga.tt_coin_logic.GetPayActivityRequest) returns (ga.tt_coin_logic.GetPayActivityResponse) {
    option (ga.api.extension.command) = {id: 51369};
  }
}
