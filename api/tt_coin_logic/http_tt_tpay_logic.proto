syntax = "proto3";

package ga.api.tt_coin_logic;

import "api/extension/extension.proto";
import "google/api/annotations.proto";
import "google/protobuf/struct.proto";

option go_package = "golang.52tt.com/protocol/app/api/tt_coin_logic;tt_coin_logic";
option java_package = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";

service HTTPTPayLogic {
  option (ga.api.extension.logic_service_language) = "go";
  option (ga.api.extension.logic_service_name) = "tpay-coin-logic";
  // /payV2/api/v2/orderInfo.shtm
  // src/main/java/com/qw/coin/ttyuyin/tpay/adapter/core/api/PayV2Controller.java:findOrder
  // 订单信息查询
  // DEPRECATED
  rpc GetPayOrderInfo(GetPayOrderInfoRequest) returns (GetPayOrderInfoResponse) {
    option (google.api.http) = {
      post: "/coin/api/GetPayOrderInfo"
      body: "*"
    };
  }

  // /payV2/api/v2/payForward.shtm
  // src/main/java/com/qw/coin/ttyuyin/tpay/adapter/core/api/PayController.java:payForward2
  // 支付结果跳转页(新)
  // DEPRECATED
  rpc PayForward(PayForwardRequest) returns (PayForwardResponse) {
    option (google.api.http) = {
      post: "/coin/api/PayForward"
      body: "*"
    };
  }

  // /api/v2/checkOrderIsPaid.shtm
  // src/main/java/com/qw/coin/ttyuyin/tpay/adapter/core/api/PayController.java:checkOrderIsPaid
  // H5充值页, 支付结果查询
  // DEPRECATED
  rpc CheckOrderIsPaid(CheckOrderIsPaidRequest) returns (CheckOrderIsPaidResponse) {
    option (google.api.http) = {
      post: "/coin/api/CheckOrderIsPaid"
      body: "*"
    };
  }

  // /tpay/api/v2/index.shtm
  // coin-ttyuyin-adapter/coin-tpay-adapter/src/main/java/com/qw/coin/ttyuyin/tpay/adapter/core/api/PayV2Controller.java:indexV2
  // 支付选择页
  // DEPRECATED
  rpc PayIndex(PayIndexRequest) returns (PayIndexResponse) {
    option (google.api.http) = {
      post: "/coin/api/PayIndex"
      body: "*"
    };
  }

  // /tpay/api/v2/restPay.shtm
  // src/main/java/com/qw/coin/ttyuyin/tpay/adapter/core/api/PayV2Controller.java:restPay
  // 确认调起支付
  rpc RestPay(RestPayRequest) returns (RestPayResponse) {
    option (google.api.http) = {
      post: "/coin/api/RestPay"
      body: "*"
    };
  }

  // 渠道支付选择页面信息
  // PayChannelSelection 原 PayIndex和GetPayOrderInfo的整合
  rpc PayChannelSelection(PayChannelSelectionRequest) returns (PayChannelSelectionResponse) {
    option (google.api.http) = {
      post: "/coin/api/PayChannelSelection"
      body: "*"
    };
  }

  // 支付结果
  // payResult 原CheckOrderIsPaid和PayForward的整合
  rpc PayResult(PayResultRequest) returns (PayResultResponse) {
    option (google.api.http) = {
      post: "/coin/api/PayResult"
      body: "*"
    };
  }
}

message GetPayOrderInfoRequest {
  // 订单信息缓存key ——已废弃、新版使用order_no
  string orderNoKey = 1 [deprecated = true];
  // 订单号
  string order_no = 2;
}
message GetPayOrderInfoResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  // 订单信息(json)
  google.protobuf.Struct data = 4;
}

message PayForwardRequest {
  // 加密后的订单号+token-本期废弃
  string tsk = 1;
  // order_no
  string order_no = 2;
  // order_token
  string order_token = 3;
}
message PayForwardResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  // data
  PayForwardData data = 4;
}
message PayForwardData {
  //redirect url
  string redirect_url = 1;
  // to-控制跳的页面，若为error则跳后端返回的redirect_url，若为success则跳前端配置的成功页面
  string to = 2;
}

message CheckOrderIsPaidRequest {
  // 加密后的订单号+token-本期废弃
  string tsk = 1;
  // order_no
  string order_no = 2;
  // order_token
  string order_token = 3;
}
message CheckOrderIsPaidResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  // data
  CheckOrderIsPaidData data = 4;
}
message CheckOrderIsPaidData {
  // 是否支付成功
  bool is_paid = 1;
}

message PayIndexRequest {
  // 订单号
  string order_no = 1;
  // order_token
  string order_token = 2;
  // caller 来源 tt/hy
  string caller = 3;
}
message PayIndexResponse {
  //code
  int32 code = 1;
  //msg
  string msg = 2;
  // data
  PayIndexData data = 4;

  message PayIndexData {
    // 订单号
    string order_no = 1;
  }
}

message RestPayRequest {
  // 订单号
  string order_no = 1;
  // 支付渠道
  string pay_channel = 3;
  // 来源
  string caller = 4;
  //扩展参数 来源于GetPayActivity返回的
  map<string, string> extra_params = 5;
}

message RestPayResponse {
  // Status message
  string msg = 1;
  // Status code
  int32 code = 2;
  // Data
  Data data = 3;
  message Data {
    // Payment method
    string method = 1;
    // Context details
    Context context = 2;
  }
  // Nested message for context
  message Context {
    // Signature
    string signature = 1;
    // Payment parameters
    string pay_params = 2 [json_name = "payParams"];
  }
}

message PayChannelSelectionRequest {
  // 订单号
  string order_no = 1;
  // recharge_token
  string recharge_token = 2;
  // caller 来源 tt/hy
  string caller = 3;
}

message PayChannelSelectionResponse {
  // code
  int32 code = 1;
  // msg
  string msg = 2;
  // data
  Data data = 3;
  message Data {
    // order info
    string order_info = 1; // 实际上里面放的是一个json字符串
  }
}

message PayResultRequest {
  // order_no
  string order_no = 1;
  // recharge_token
  string recharge_token = 2;
}

message PayResultResponse {
  // code
  int32 code = 1;
  // msg
  string msg = 2;
  // data
  Data data = 3;
  message Data {
    //redirect url
    string redirect_url = 1;
    // to-控制跳的页面，若为error则跳后端返回的redirect_url，若为success则跳前端配置的成功页面
    string to = 2;
  }
}
