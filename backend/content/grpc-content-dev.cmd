# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

2550:
api-route-configurator --etcd-endpoints *************:2379 create --id 2550 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/SubscribeTopic
2551:
api-route-configurator --etcd-endpoints *************:2379 create --id 2551 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/UnsubscribeTopic
2552:
api-route-configurator --etcd-endpoints *************:2379 create --id 2552 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/GetTopicList
2553:
api-route-configurator --etcd-endpoints *************:2379 create --id 2553 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/GetTopicInfo
2554:
api-route-configurator --etcd-endpoints *************:2379 create --id 2554 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/GetSubscriberTopicList
2555:
api-route-configurator --etcd-endpoints *************:2379 create --id 2555 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/GetUnSubscribeTopicList
2556:
api-route-configurator --etcd-endpoints *************:2379 create --id 2556 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/CheckTopicsIsSubscribe
2557:
api-route-configurator --etcd-endpoints *************:2379 create --id 2557 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/SearchTopic
2570:
api-route-configurator --etcd-endpoints *************:2379 create --id 2570 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/GetNewsFeeds
2571:
api-route-configurator --etcd-endpoints *************:2379 create --id 2571 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/RemoveFeeds
2572:
api-route-configurator --etcd-endpoints *************:2379 create --id 2572 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/ReportVisitRecord
2573:
api-route-configurator --etcd-endpoints *************:2379 create --id 2573 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/AddFavourite
2574:
api-route-configurator --etcd-endpoints *************:2379 create --id 2574 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/VotePost
2575:
api-route-configurator --etcd-endpoints *************:2379 create --id 2575 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/GetGameEntrance
2587:
api-route-configurator --etcd-endpoints *************:2379 create --id 2587 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/UpdateAttachmentPrivacy
2588:
api-route-configurator --etcd-endpoints *************:2379 create --id 2588 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/MarkContentSticky
2589:
api-route-configurator --etcd-endpoints *************:2379 create --id 2589 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/ReportPostMultimediaView
2590:
api-route-configurator --etcd-endpoints *************:2379 create --id 2590 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/PostPost
2591:
api-route-configurator --etcd-endpoints *************:2379 create --id 2591 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/MarkPostAttachmentUploaded
2592:
api-route-configurator --etcd-endpoints *************:2379 create --id 2592 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/DeletePost
2593:
api-route-configurator --etcd-endpoints *************:2379 create --id 2593 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/GetPost
2594:
api-route-configurator --etcd-endpoints *************:2379 create --id 2594 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/PostComment
2595:
api-route-configurator --etcd-endpoints *************:2379 create --id 2595 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/DeleteComment
2596:
api-route-configurator --etcd-endpoints *************:2379 create --id 2596 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/GetCommentList
2597:
api-route-configurator --etcd-endpoints *************:2379 create --id 2597 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/ReportPostView
2598:
api-route-configurator --etcd-endpoints *************:2379 create --id 2598 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/GetAttitudeUserList
2599:
api-route-configurator --etcd-endpoints *************:2379 create --id 2599 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/ReportPostShare
2600:
api-route-configurator --etcd-endpoints *************:2379 create --id 2600 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/ExpressAttitude
2604:
api-route-configurator --etcd-endpoints *************:2379 create --id 2604 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/GetTopicAds
2606:
api-route-configurator --etcd-endpoints *************:2379 create --id 2606 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/GetMoonDetail
2607:
api-route-configurator --etcd-endpoints *************:2379 create --id 2607 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/GetMoodConfig
2608:
api-route-configurator --etcd-endpoints *************:2379 create --id 2608 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/GetTabConfig
2609:
api-route-configurator --etcd-endpoints *************:2379 create --id 2609 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/ReportUnrelatedTopic
2610:
api-route-configurator --etcd-endpoints *************:2379 create --id 2610 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/AudioPostScriptTabs
2611:
api-route-configurator --etcd-endpoints *************:2379 create --id 2611 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/AudioPostScripts
2612:
api-route-configurator --etcd-endpoints *************:2379 create --id 2612 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/RandomAudioPostScripts
2613:
api-route-configurator --etcd-endpoints *************:2379 create --id 2613 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/RandomAudioPostImages
2614:
api-route-configurator --etcd-endpoints *************:2379 create --id 2614 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/AudioPostImages
2615:
api-route-configurator --etcd-endpoints *************:2379 create --id 2615 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/RandomAudioPostMusics
2616:
api-route-configurator --etcd-endpoints *************:2379 create --id 2616 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/AudioPostMusics
2617:
api-route-configurator --etcd-endpoints *************:2379 create --id 2617 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/AudioPostMusicTabs
2618:
api-route-configurator --etcd-endpoints *************:2379 create --id 2618 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/XunfeiSignature
2619:
api-route-configurator --etcd-endpoints *************:2379 create --id 2619 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/AudioPostScriptsForMobile
2620:
api-route-configurator --etcd-endpoints *************:2379 create --id 2620 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/DelRandList
2621:
api-route-configurator --etcd-endpoints *************:2379 create --id 2621 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/AudioPostScriptTabsV2
2622:
api-route-configurator --etcd-endpoints *************:2379 create --id 2622 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/AudioPostScriptsV2
2623:
api-route-configurator --etcd-endpoints *************:2379 create --id 2623 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/RandomAudioPostScriptsV2
2624:
api-route-configurator --etcd-endpoints *************:2379 create --id 2624 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/RandomAudioPostImagesV2
2625:
api-route-configurator --etcd-endpoints *************:2379 create --id 2625 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/AudioPostImagesV2
2626:
api-route-configurator --etcd-endpoints *************:2379 create --id 2626 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/RandomAudioPostMusicsV2
2627:
api-route-configurator --etcd-endpoints *************:2379 create --id 2627 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/AudioPostMusicsV2
2628:
api-route-configurator --etcd-endpoints *************:2379 create --id 2628 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/AudioPostMusicTabsV2
2629:
api-route-configurator --etcd-endpoints *************:2379 create --id 2629 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/XunfeiSignatureV2
2630:
api-route-configurator --etcd-endpoints *************:2379 create --id 2630 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/AudioPostScriptsForMobileV2
2631:
api-route-configurator --etcd-endpoints *************:2379 create --id 2631 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/DelRandListV2
2900:
api-route-configurator --etcd-endpoints *************:2379 create --id 2900 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/GetAttentionPeople
2901:
api-route-configurator --etcd-endpoints *************:2379 create --id 2901 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/IsKolExist
2902:
api-route-configurator --etcd-endpoints *************:2379 create --id 2902 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/HideHighContent
2903:
api-route-configurator --etcd-endpoints *************:2379 create --id 2903 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/UpdatePostPrivacyPolicy
30145:
api-route-configurator --etcd-endpoints *************:2379 create --id 30145 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/GetPublisherTopicByUid
30147:
api-route-configurator --etcd-endpoints *************:2379 create --id 30147 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/GetNewestPosts
30148:
api-route-configurator --etcd-endpoints *************:2379 create --id 30148 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/GetFeedsByPostIDs
30149:
api-route-configurator --etcd-endpoints *************:2379 create --id 30149 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/TopicInRecommendFeed
37001:
api-route-configurator --etcd-endpoints *************:2379 create --id 37001 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/GetPostButtonGuide
37002:
api-route-configurator --etcd-endpoints *************:2379 create --id 37002 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/GetUgcContentFeedbackOptions
37003:
api-route-configurator --etcd-endpoints *************:2379 create --id 37003 --source api/content/grpc_content.proto --lang go --method /ga.api.content.ContentLogic/ReportUgcContentFeedback
