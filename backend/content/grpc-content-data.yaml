cmdInfoList:
  - source: api/content/grpc_content.proto
    cmd: 2550
    lang: go
    method: /ga.api.content.ContentLogic/SubscribeTopic
  - source: api/content/grpc_content.proto
    cmd: 2551
    lang: go
    method: /ga.api.content.ContentLogic/UnsubscribeTopic
  - source: api/content/grpc_content.proto
    cmd: 2552
    lang: go
    method: /ga.api.content.ContentLogic/GetTopicList
  - source: api/content/grpc_content.proto
    cmd: 2553
    lang: go
    method: /ga.api.content.ContentLogic/GetTopicInfo
  - source: api/content/grpc_content.proto
    cmd: 2554
    lang: go
    method: /ga.api.content.ContentLogic/GetSubscriberTopicList
  - source: api/content/grpc_content.proto
    cmd: 2555
    lang: go
    method: /ga.api.content.ContentLogic/GetUnSubscribeTopicList
  - source: api/content/grpc_content.proto
    cmd: 2556
    lang: go
    method: /ga.api.content.ContentLogic/CheckTopicsIsSubscribe
  - source: api/content/grpc_content.proto
    cmd: 2557
    lang: go
    method: /ga.api.content.ContentLogic/SearchTopic
  - source: api/content/grpc_content.proto
    cmd: 2570
    lang: go
    method: /ga.api.content.ContentLogic/GetNewsFeeds
  - source: api/content/grpc_content.proto
    cmd: 2571
    lang: go
    method: /ga.api.content.ContentLogic/RemoveFeeds
  - source: api/content/grpc_content.proto
    cmd: 2572
    lang: go
    method: /ga.api.content.ContentLogic/ReportVisitRecord
  - source: api/content/grpc_content.proto
    cmd: 2573
    lang: go
    method: /ga.api.content.ContentLogic/AddFavourite
  - source: api/content/grpc_content.proto
    cmd: 2574
    lang: go
    method: /ga.api.content.ContentLogic/VotePost
  - source: api/content/grpc_content.proto
    cmd: 2575
    lang: go
    method: /ga.api.content.ContentLogic/GetGameEntrance
  - source: api/content/grpc_content.proto
    cmd: 2587
    lang: go
    method: /ga.api.content.ContentLogic/UpdateAttachmentPrivacy
  - source: api/content/grpc_content.proto
    cmd: 2588
    lang: go
    method: /ga.api.content.ContentLogic/MarkContentSticky
  - source: api/content/grpc_content.proto
    cmd: 2589
    lang: go
    method: /ga.api.content.ContentLogic/ReportPostMultimediaView
  - source: api/content/grpc_content.proto
    cmd: 2590
    lang: go
    method: /ga.api.content.ContentLogic/PostPost
  - source: api/content/grpc_content.proto
    cmd: 2591
    lang: go
    method: /ga.api.content.ContentLogic/MarkPostAttachmentUploaded
  - source: api/content/grpc_content.proto
    cmd: 2592
    lang: go
    method: /ga.api.content.ContentLogic/DeletePost
  - source: api/content/grpc_content.proto
    cmd: 2593
    lang: go
    method: /ga.api.content.ContentLogic/GetPost
  - source: api/content/grpc_content.proto
    cmd: 2594
    lang: go
    method: /ga.api.content.ContentLogic/PostComment
  - source: api/content/grpc_content.proto
    cmd: 2595
    lang: go
    method: /ga.api.content.ContentLogic/DeleteComment
  - source: api/content/grpc_content.proto
    cmd: 2596
    lang: go
    method: /ga.api.content.ContentLogic/GetCommentList
  - source: api/content/grpc_content.proto
    cmd: 2597
    lang: go
    method: /ga.api.content.ContentLogic/ReportPostView
  - source: api/content/grpc_content.proto
    cmd: 2598
    lang: go
    method: /ga.api.content.ContentLogic/GetAttitudeUserList
  - source: api/content/grpc_content.proto
    cmd: 2599
    lang: go
    method: /ga.api.content.ContentLogic/ReportPostShare
  - source: api/content/grpc_content.proto
    cmd: 2600
    lang: go
    method: /ga.api.content.ContentLogic/ExpressAttitude
  - source: api/content/grpc_content.proto
    cmd: 2604
    lang: go
    method: /ga.api.content.ContentLogic/GetTopicAds
  - source: api/content/grpc_content.proto
    cmd: 2606
    lang: go
    method: /ga.api.content.ContentLogic/GetMoonDetail
  - source: api/content/grpc_content.proto
    cmd: 2607
    lang: go
    method: /ga.api.content.ContentLogic/GetMoodConfig
  - source: api/content/grpc_content.proto
    cmd: 2608
    lang: go
    method: /ga.api.content.ContentLogic/GetTabConfig
  - source: api/content/grpc_content.proto
    cmd: 2609
    lang: go
    method: /ga.api.content.ContentLogic/ReportUnrelatedTopic
  - source: api/content/grpc_content.proto
    cmd: 2610
    lang: go
    method: /ga.api.content.ContentLogic/AudioPostScriptTabs
  - source: api/content/grpc_content.proto
    cmd: 2611
    lang: go
    method: /ga.api.content.ContentLogic/AudioPostScripts
  - source: api/content/grpc_content.proto
    cmd: 2612
    lang: go
    method: /ga.api.content.ContentLogic/RandomAudioPostScripts
  - source: api/content/grpc_content.proto
    cmd: 2613
    lang: go
    method: /ga.api.content.ContentLogic/RandomAudioPostImages
  - source: api/content/grpc_content.proto
    cmd: 2614
    lang: go
    method: /ga.api.content.ContentLogic/AudioPostImages
  - source: api/content/grpc_content.proto
    cmd: 2615
    lang: go
    method: /ga.api.content.ContentLogic/RandomAudioPostMusics
  - source: api/content/grpc_content.proto
    cmd: 2616
    lang: go
    method: /ga.api.content.ContentLogic/AudioPostMusics
  - source: api/content/grpc_content.proto
    cmd: 2617
    lang: go
    method: /ga.api.content.ContentLogic/AudioPostMusicTabs
  - source: api/content/grpc_content.proto
    cmd: 2618
    lang: go
    method: /ga.api.content.ContentLogic/XunfeiSignature
  - source: api/content/grpc_content.proto
    cmd: 2619
    lang: go
    method: /ga.api.content.ContentLogic/AudioPostScriptsForMobile
  - source: api/content/grpc_content.proto
    cmd: 2620
    lang: go
    method: /ga.api.content.ContentLogic/DelRandList
  - source: api/content/grpc_content.proto
    cmd: 2621
    lang: go
    method: /ga.api.content.ContentLogic/AudioPostScriptTabsV2
  - source: api/content/grpc_content.proto
    cmd: 2622
    lang: go
    method: /ga.api.content.ContentLogic/AudioPostScriptsV2
  - source: api/content/grpc_content.proto
    cmd: 2623
    lang: go
    method: /ga.api.content.ContentLogic/RandomAudioPostScriptsV2
  - source: api/content/grpc_content.proto
    cmd: 2624
    lang: go
    method: /ga.api.content.ContentLogic/RandomAudioPostImagesV2
  - source: api/content/grpc_content.proto
    cmd: 2625
    lang: go
    method: /ga.api.content.ContentLogic/AudioPostImagesV2
  - source: api/content/grpc_content.proto
    cmd: 2626
    lang: go
    method: /ga.api.content.ContentLogic/RandomAudioPostMusicsV2
  - source: api/content/grpc_content.proto
    cmd: 2627
    lang: go
    method: /ga.api.content.ContentLogic/AudioPostMusicsV2
  - source: api/content/grpc_content.proto
    cmd: 2628
    lang: go
    method: /ga.api.content.ContentLogic/AudioPostMusicTabsV2
  - source: api/content/grpc_content.proto
    cmd: 2629
    lang: go
    method: /ga.api.content.ContentLogic/XunfeiSignatureV2
  - source: api/content/grpc_content.proto
    cmd: 2630
    lang: go
    method: /ga.api.content.ContentLogic/AudioPostScriptsForMobileV2
  - source: api/content/grpc_content.proto
    cmd: 2631
    lang: go
    method: /ga.api.content.ContentLogic/DelRandListV2
  - source: api/content/grpc_content.proto
    cmd: 2900
    lang: go
    method: /ga.api.content.ContentLogic/GetAttentionPeople
  - source: api/content/grpc_content.proto
    cmd: 2901
    lang: go
    method: /ga.api.content.ContentLogic/IsKolExist
  - source: api/content/grpc_content.proto
    cmd: 2902
    lang: go
    method: /ga.api.content.ContentLogic/HideHighContent
  - source: api/content/grpc_content.proto
    cmd: 2903
    lang: go
    method: /ga.api.content.ContentLogic/UpdatePostPrivacyPolicy
  - source: api/content/grpc_content.proto
    cmd: 30145
    lang: go
    method: /ga.api.content.ContentLogic/GetPublisherTopicByUid
  - source: api/content/grpc_content.proto
    cmd: 30147
    lang: go
    method: /ga.api.content.ContentLogic/GetNewestPosts
  - source: api/content/grpc_content.proto
    cmd: 30148
    lang: go
    method: /ga.api.content.ContentLogic/GetFeedsByPostIDs
  - source: api/content/grpc_content.proto
    cmd: 30149
    lang: go
    method: /ga.api.content.ContentLogic/TopicInRecommendFeed
  - source: api/content/grpc_content.proto
    cmd: 37001
    lang: go
    method: /ga.api.content.ContentLogic/GetPostButtonGuide
  - source: api/content/grpc_content.proto
    cmd: 37002
    lang: go
    method: /ga.api.content.ContentLogic/GetUgcContentFeedbackOptions
  - source: api/content/grpc_content.proto
    cmd: 37003
    lang: go
    method: /ga.api.content.ContentLogic/ReportUgcContentFeedback

