# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

51351:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51351 --source api/tt_coin_logic/grpc_tt_coin_logic.proto --lang go --method /ga.api.tt_coin_logic.TTCoinLogic/GetBalance
51352:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51352 --source api/tt_coin_logic/grpc_tt_coin_logic.proto --lang go --method /ga.api.tt_coin_logic.TTCoinLogic/CreateInAppOrder
51353:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51353 --source api/tt_coin_logic/grpc_tt_coin_logic.proto --lang go --method /ga.api.tt_coin_logic.TTCoinLogic/GetInAppGoodsList
51354:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51354 --source api/tt_coin_logic/grpc_tt_coin_logic.proto --lang go --method /ga.api.tt_coin_logic.TTCoinLogic/GetUserAccumulateRecharge
51355:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51355 --source api/tt_coin_logic/grpc_tt_coin_logic.proto --lang go --method /ga.api.tt_coin_logic.TTCoinLogic/GetAppstoreProduct
51356:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51356 --source api/tt_coin_logic/grpc_tt_coin_logic.proto --lang go --method /ga.api.tt_coin_logic.TTCoinLogic/GetAppAccountToken
51357:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51357 --source api/tt_coin_logic/grpc_tt_coin_logic.proto --lang go --method /ga.api.tt_coin_logic.TTCoinLogic/GetRechargeRecord
51358:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51358 --source api/tt_coin_logic/grpc_tt_coin_logic.proto --lang go --method /ga.api.tt_coin_logic.TTCoinLogic/FaceResult
51359:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51359 --source api/tt_coin_logic/grpc_tt_coin_logic.proto --lang go --method /ga.api.tt_coin_logic.TTCoinLogic/CreateInIosAppOrder
51360:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51360 --source api/tt_coin_logic/grpc_tt_coin_logic.proto --lang go --method /ga.api.tt_coin_logic.TTCoinLogic/GetIOSVoldemortedVersion
51368:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51368 --source api/tt_coin_logic/grpc_tt_coin_logic.proto --lang go --method /ga.api.tt_coin_logic.TTCoinLogic/AndroidHalfOrderPay
51369:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51369 --source api/tt_coin_logic/grpc_tt_coin_logic.proto --lang go --method /ga.api.tt_coin_logic.TTCoinLogic/GetPayActivity
51375:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51375 --source api/tt_coin_logic/grpc_tt_coin_logic.proto --lang go --method /ga.api.tt_coin_logic.TTCoinLogic/GetLastChargeChannel
