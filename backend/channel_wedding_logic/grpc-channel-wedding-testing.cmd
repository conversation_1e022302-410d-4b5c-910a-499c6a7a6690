# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

51311:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51311 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetGetWeddingInfo
51312:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51312 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/SwitchWeddingStage
51313:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51313 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/TakeWeddingGroupPhoto
51314:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51314 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/ApplyToJoinChairGame
51315:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51315 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetChairGameApplyList
51316:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51316 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetChairGameInfo
51317:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51317 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GrabChair
51318:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51318 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/StartChairGame
51319:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51319 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/SetChairGameToNextRound
51320:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51320 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/StartGrabChair
51321:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51321 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetUserWeddingPose
51322:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51322 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/SetUserInuseWeddingPose
51323:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51323 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/BatchGetUserInuseWeddingPose
51324:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51324 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingGroupPhotoSeatMap
51325:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51325 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/SetUserWeddingGroupPhotoSeat
51326:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51326 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/SetChairGameReward
51327:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51327 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/BatchGetUserWeddingClothes
51328:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51328 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetChairGameRewardSetting
51329:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51329 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/SetUserWeddingOrientation
51330:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51330 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingSchedulePageInfo
51331:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51331 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/BuyWedding
51332:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51332 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/CancelWedding
51333:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51333 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingHallList
51334:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51334 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/SubscribeWedding
51335:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51335 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingEntrySwitch
51336:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51336 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingBigScreen
51337:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51337 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/SaveWeddingBigScreen
51338:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51338 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingInviteInfo
51339:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51339 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/HandleWeddingInvite
51340:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51340 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/ApplyEndWeddingRelationship
51341:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51341 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/CancelEndWeddingRelationship
51342:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51342 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/DirectEndWeddingRelationship
51343:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51343 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetProposeList
51344:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51344 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/SendPropose
51345:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51345 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/HandlePropose
51346:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51346 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetProposeById
51347:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51347 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetSendPropose
51348:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51348 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetUserWeddingPrecipitation
51349:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51349 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/ReportWeddingScenePic
51350:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51350 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/HideWeddingRelation
51460:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51460 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingPreviewResource
51461:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51461 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingHighLightPresent
51462:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51462 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingRankEntry
51463:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51463 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/SendWeddingReservePresent
51464:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51464 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingThemeCfgList
51465:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51465 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/RevokePropose
51466:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51466 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetUserInRoomStatus
51467:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51467 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetGoingWeddingEntry
51468:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51468 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/RemindUserJoinWeddingRoom
51470:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51470 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingPresent
51471:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51471 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingPreProgressInfo
51472:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51472 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/StartWedding
51473:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51473 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/CancelPreparedWedding
51474:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 51474 --source api/channel_wedding_logic/grpc_channel_wedding.proto --lang go --method /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingPrepareInfo
