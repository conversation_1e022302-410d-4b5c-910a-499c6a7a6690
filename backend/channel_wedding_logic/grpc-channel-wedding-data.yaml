cmdInfoList:
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51311
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetGetWeddingInfo
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51312
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/SwitchWeddingStage
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51313
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/TakeWeddingGroupPhoto
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51314
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/ApplyToJoinChairGame
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51315
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetChairGameApplyList
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51316
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetChairGameInfo
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51317
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GrabChair
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51318
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/StartChairGame
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51319
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/SetChairGameToNextRound
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51320
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/StartGrabChair
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51321
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetUserWeddingPose
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51322
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/SetUserInuseWeddingPose
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51323
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/BatchGetUserInuseWeddingPose
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51324
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingGroupPhotoSeatMap
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51325
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/SetUserWeddingGroupPhotoSeat
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51326
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/SetChairGameReward
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51327
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/BatchGetUserWeddingClothes
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51328
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetChairGameRewardSetting
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51329
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/SetUserWeddingOrientation
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51330
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingSchedulePageInfo
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51331
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/BuyWedding
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51332
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/CancelWedding
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51333
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingHallList
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51334
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/SubscribeWedding
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51335
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingEntrySwitch
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51336
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingBigScreen
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51337
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/SaveWeddingBigScreen
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51338
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingInviteInfo
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51339
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/HandleWeddingInvite
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51340
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/ApplyEndWeddingRelationship
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51341
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/CancelEndWeddingRelationship
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51342
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/DirectEndWeddingRelationship
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51343
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetProposeList
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51344
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/SendPropose
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51345
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/HandlePropose
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51346
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetProposeById
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51347
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetSendPropose
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51348
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetUserWeddingPrecipitation
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51349
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/ReportWeddingScenePic
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51350
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/HideWeddingRelation
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51460
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingPreviewResource
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51461
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingHighLightPresent
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51462
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingRankEntry
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51463
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/SendWeddingReservePresent
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51464
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingThemeCfgList
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51465
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/RevokePropose
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51466
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetUserInRoomStatus
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51467
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetGoingWeddingEntry
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51468
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/RemindUserJoinWeddingRoom
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51470
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingPresent
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51471
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingPreProgressInfo
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51472
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/StartWedding
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51473
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/CancelPreparedWedding
  - source: api/channel_wedding_logic/grpc_channel_wedding.proto
    cmd: 51474
    lang: go
    method: /ga.api.channel_wedding_logic.ChannelWeddingLogic/GetWeddingPrepareInfo

