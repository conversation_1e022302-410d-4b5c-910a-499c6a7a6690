# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

51500:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51500 --source api/wealth_god_logic/grpc_wealth_god_logic.proto --lang go --method /ga.api.wealth_god_logic.WealthGodLogic/GetWealthGodCommonCfg
51501:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51501 --source api/wealth_god_logic/grpc_wealth_god_logic.proto --lang go --method /ga.api.wealth_god_logic.WealthGodLogic/GetWealthGodEntry
51502:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51502 --source api/wealth_god_logic/grpc_wealth_god_logic.proto --lang go --method /ga.api.wealth_god_logic.WealthGodLogic/GetOneWealthGodChannel
51503:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51503 --source api/wealth_god_logic/grpc_wealth_god_logic.proto --lang go --method /ga.api.wealth_god_logic.WealthGodLogic/GetWealthGodActivityInfo
51504:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51504 --source api/wealth_god_logic/grpc_wealth_god_logic.proto --lang go --method /ga.api.wealth_god_logic.WealthGodLogic/GetWealthGodDetail
51505:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51505 --source api/wealth_god_logic/grpc_wealth_god_logic.proto --lang go --method /ga.api.wealth_god_logic.WealthGodLogic/OpenWealthGodBoxReward
51506:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 51506 --source api/wealth_god_logic/grpc_wealth_god_logic.proto --lang go --method /ga.api.wealth_god_logic.WealthGodLogic/ReportStayRoomMissionFinish
