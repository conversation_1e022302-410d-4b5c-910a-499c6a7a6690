apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-wealth-god-logic-logic
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.wealth_god_logic.WealthGodLogic/
    rewrite:
      uri: /logic.WealthGodLogic/
    delegate:
       name: wealth-god-logic-delegator-80
       namespace: quicksilver


