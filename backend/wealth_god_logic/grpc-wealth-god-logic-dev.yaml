apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-wealth-god-logic-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.wealth_god_logic.WealthGodLogic/
    rewrite:
      uri: /logic.WealthGodLogic/
    delegate:
       name: wealth-god-logic-delegator-80
       namespace: quicksilver


