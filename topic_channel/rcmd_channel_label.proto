syntax = "proto3";

package topic_channel.rcmd_channel_label;

option go_package = "golang.52tt.com/protocol/services/topic_channel/rcmd_channel_label";

service RCMDChannelLabel {
  // 房间标签搜索
  rpc LabelSearch(LabelSearchReq) returns (LabelSearchResp);
  // 房间玩法列表获取
  rpc GetGameLabels(GetGameLabelsReq) returns (GetGameLabelsResp);
  // 结巴切词
  rpc CutWord(CutWordReq) returns (CutWordResp);
  // 刷新玩法标签
  // DEPRECATED: 切词库和标签倒排索引已抽离作为公共基础库，不再通过rpc刷新标签
  rpc LabelRefresh(LabelRefreshReq) returns (LabelRefreshResp);

  // 获取搜索框hint
  rpc GetSearchHint(GetSearchHintReq) returns (GetSearchHintResp);
  // 玩法标签映射
  // 建议以后使用CutAndMatchLabel替代该方法
  rpc ConvertGameLabels(ConvertGameLabelsReq) returns (ConvertGameLabelsResp);
  // 批量获取热门玩法
  rpc BatchHotGameLabels(BatchHotGameLabelsReq) returns (BatchHotGameLabelsResp);
  // 获取游戏发布条件玩法
  // DEPRECATED: 之前提供给服务端缓存至本地，在用户请求的时候，将发布条件转玩法。先发布条件转玩法已迁至推荐
  rpc GetPublishGameLabels(GetPublishGameLabelsReq) returns (GetPublishGameLabelsResp);
  // 全局搜索标签，todo: 迁移至上游调用方rcmd-search
  rpc SearchGlobalLabel(SearchGlobalLabelReq) returns (SearchGlobalLabelResp);
  // 游戏名称搜索
  rpc SearchGameName(SearchGameNameReq) returns (SearchGameNameResp);

  rpc InternalGetAllLabel(InternalGetAllLabelReq) returns (InternalGetAllLabelResp);
  // 获取房间名称匹配的关联发布条件的标签
  rpc GetRelatedPublishLabels(GetRelatedPublishLabelsReq) returns (GetRelatedPublishLabelsResp);

  // 切词并匹配标签
  rpc CutAndMatchLabel(CutAndMatchLabelReq) returns (CutAndMatchLabelResp);

  // 获取游戏卡信息
  rpc MatchGameCardInfo(MatchGameCardInfoReq) returns (MatchGameCardInfoResp);

  // 获取业务关联关系
  rpc GetBusinessLabelRelation(GetBusinessLabelRelationReq) returns (GetBusinessLabelRelationResp);

  // 刷新GameLabel: 用于清除客户端本地gameLabel缓存，服务端房间列表筛选面板版本控制
  rpc RefreshGameLabel(RefreshGameLabelReq) returns (RefreshGameLabelResp);
  // 返回用户所在实验域不可转玩法的BlockOption
  rpc GetFilterBlockOptionList(GetFilterBlockOptionListReq) returns (GetFilterBlockOptionListResp);
}

message GetFilterBlockOptionListReq {
  uint32 uid = 1;
  repeated uint32 tab_ids = 2;
}

message GetFilterBlockOptionListResp {
  repeated GameBlockOption block_option_list = 1;
}

message RefreshGameLabelReq {
  uint32 uid = 1;
  uint32 tab_id = 2;
  repeated GameLabel game_labels = 3;
  enum Source {
    SourceInvalid = 0; // 无效
    SourceRefreshClientCache = 1; // 刷新客户端缓存
    SourceRefreshServerLabels = 2; // 刷新服务端标签
  }
  Source refresh_source = 4; // 来源
  repeated uint32 tab_ids = 5; // tab id列表
}

message RefreshGameLabelResp {
  repeated GameLabel game_labels = 1;
}

enum BusinessType {
  BusinessTypeDefault = 0; // 默认
  BusinessTypeLabelGameCard = 1; // 标签游戏卡映射
  BusinessTypeBlockOptionToGameCard = 2; // 发布条件到游戏卡的映射：发布条件->玩法->游戏卡选项
}

message GetBusinessLabelRelationReq {
  repeated BusinessType business_type = 1; // 默认为0，全部返回
}

message BusinessLabelRelation {
  BusinessType business_type = 1;
  //根据BusinessType解析：
  // BusinessTypeLabelGameCard->LabelGameCardRelation,
  // BusinessTypeBlockOptionToGameCard->BlockOptionGameCardRelation
  bytes data = 2;
}

message GameCardOpt {
  uint32 game_card_id = 1;
  uint32 opt_id = 2;
  string conf_value = 3;
}

message GameCardLabel{
  string label = 1;
  repeated GameCardOpt opt_conf_value_list = 2;
}

message BlockOptionGameCard{
  uint32 tab_id = 1;
  BlockOption block_opt = 2;
  repeated GameCardOpt opt_conf_value_list = 3;
}

message LabelGameCardRelation{
  repeated GameCardLabel game_card_label = 1;
}

message BlockOptionGameCardRelation{
  repeated BlockOptionGameCard block_option_game_card_list = 1;
}

message GetBusinessLabelRelationResp{
  repeated BusinessLabelRelation business_label_relation = 1;
}

message MatchGameCardInfoReq{
  uint32 uid = 1;
  uint32 tab_id = 2;
  // Deprecated,replace by text_pair_list
  repeated string texts = 3;
  repeated TextPair text_pair_list = 4;
}

message TextPair{
  repeated string value_text = 1;
  string title_text = 2;
  enum TextType {
    TextTypeDefault = 0; // 默认
    TextTypeMatchTeam = 1; // 组队信息问题
    TextTypeOfficeQuestion = 2; // 官方弹窗问题文本
    TextTypeCustomQuestion = 3; // 用户自己输入的问题文本
    TextTypeAgeQuestion = 4; // 用户年龄问题文本
  }
  TextType text_type = 3;
}

message GameCardInfo{
  uint32 opt_id = 1;
  repeated string opt_value_list = 2;
}

message MatchGameCardInfoResp{
  uint32 game_card_id = 1;
  repeated GameCardInfo game_card_info = 2;
}

message CutAndMatchLabelReq{
  string text = 1;
  enum CutType {
    Jieba = 0; // 结巴切词
    Contain = 1; // text包含词库标签和同义词
  }
  CutType cut_type = 2; // 切词方式
  bool is_not_return_label = 3; // 是否不返回CutAndMatchLabelResp.labels
}

message CutAndMatchLabelResp{
  repeated GameLabel labels = 1; // 匹配的标签
  bool is_hit_label = 2; // 是否匹配标签
}

message InternalGetAllLabelReq {

}

message InternalGetAllLabelResp {
  string label_text = 1;
}

/** ----------GetRelatedPublishLabels---------- **/
message GetRelatedPublishLabelsReq{
  uint32 uid = 1;
  uint32 tab_id = 2;
  string text = 3; // 房间标题
}

message RelatedPublishLabel {
  BlockOption block_options = 1; // 发布标签id
  string publish_name = 2; // 发布标签名称
  repeated GameLabel label = 3; // 匹配的三级标签
}

// 推荐选择区域
message RecommendationOption {
  enum Type{
    Default = 0; // 默认
    Publish = 1; // 发布条件
    ThirdLabel = 2; // 三级标签
  }
  uint32 option_type = 1; // 选项类型
  GameLabel label = 2; // 三级标签取这个里面的display_name字段（需要用作垂直列表筛选和搜索匹配，发布房间事件TopicChannelEvent需要将整个GameLabel带上来）
  BlockOption block_options = 3; // 发布条件取这个字段-blockid、elemId
}

// 推荐填写
message RecommendationInput {
  BlockOption block_options = 1;
}

// 661
message GetRelatedPublishLabelsResp{
  repeated RelatedPublishLabel publish_label = 1; // 660及以下版本-返回发布条件
  repeated RecommendationOption recommendation_options = 2; // 661及以上版本-返回推荐选择标签
  repeated RecommendationInput recommendation_inputs = 3; // 661及以上版本-返回推荐数值
}

/** ----------GetRelatedPublishLabels---------- **/

/** ----------SearchGameName---------- **/
message SearchGameNameReq {
  uint32 uid = 1;
  string text = 2;
  enum SourceType {
    SearchGuideSource = 0; // 搜索引导场景
    RCMDGameListSource = 1; // 中台获取游戏标签
    TopicChannelSource = 2; // 开黑房间列表场景
    SearchPostSource = 3; // 帖子搜素场景
  }
  SourceType source_type = 3; // 来源

}

message GameInfo {
  uint32 tab_id = 1;
  string game_name = 2;
  repeated string synonyms = 3;
}

message SearchGameNameResp{
  repeated GameLabel game_label = 1;
  repeated GameInfo game_info_list = 2;
}
/** ----------SearchGameName---------- **/


/** ----------SearchGlobalLabel---------- **/
message SearchGlobalLabelReq {
  uint32 uid = 1;
  string text = 2;
}

message SearchLabel {
  string game_name = 1;
  repeated GameLabel label = 2;
  repeated LabelDetail label_detail = 3;
}

message SearchGlobalLabelResp{
  repeated SearchLabel search_label_list = 1;
}
/** ----------SearchGlobalLabel---------- **/

message BlockOption {
  uint32 block_id = 1;           //块id
  uint32 elem_id = 2;            //块里面的元素id
}
/** ----------GetPublishGameLabels---------- **/
message GameBlockOption {
  uint32 tab_id = 1;
  repeated BlockOption block_options = 2;
}

message GetPublishGameLabelsReq {
  repeated GameBlockOption game_block_option = 1;
}

message BlockOptionLabel {
  BlockOption block_options = 1;
  repeated GameLabel label = 2;
}

message BlockOptionLabelList{
  uint32 tab_id = 1;
  repeated BlockOptionLabel block_option_label = 2;
}

message GetPublishGameLabelsResp {
  repeated BlockOptionLabelList block_option_label_list = 1;
}
/** ----------GetPublishGameLabels---------- **/




/** ----------ConvertGameLabels---------- **/
message ConvertGameLabel {
  GameLabel labels = 1;
  string origin_text = 2; // gameLabel对应的标签
}

message ConvertGameLabelList{
  repeated ConvertGameLabel labels = 1;
  uint32 tab_id = 2;
}

message ConvertGameLabelsResp {
  repeated ConvertGameLabelList labels = 1;
}

message ConvertGameInfo{
  uint32 tab_id = 1; // 游戏tab id
  repeated string texts = 2;
}
message ConvertGameLabelsReq {
  uint32 uid = 1;
  repeated ConvertGameInfo game_infos = 2;
  enum SourceType  {
    DEFAULT = 0; // 默认
    GameChannelList = 1; // 房间列表，会走标签版本控制
    QuestionPopWin = 2; // 问题弹窗
  }
  SourceType source = 3;
}
/** ----------ConvertGameLabels---------- **/

message GetSearchHintReq {
  uint32 uid = 1;
  uint32 tab_id = 2; // 游戏tab id
}

message GetSearchHintResp {
  string hint = 1;
  enum HintType {
    DEFAULT = 0; // 默认兜底文案
    RCMD = 1; // 推荐文案, 用于点击直接填充文案
  }
  HintType type = 2;
}

message LabelSearchReq {
  uint32 uid = 1;
  uint32 tab_id = 2; // 游戏tab id
  string text = 3; // 搜索文本
  repeated uint32 tab_ids = 4; // tab id列表
}

message LabelSearchResp {
  repeated GameLabel labels = 1;
}


enum GameLabelType {
  Default = 0; // 搜索结果的标签
  HotLabel = 1; // 热门标签（支持标题玩法标签），val由多个标签值组成，用英文逗号分割
  LabelOfPublish = 2; // 发布条件标签
  LabelOfTabName = 3; // 游戏类型标签
  LabelOfGlobal = 4; // 通用类型标签
  LabelOfClassify = 5; // 分类标签
}

// 外显label
message GameLabel {
  string val = 1; // 标签val 需要传回给推荐
  string display_name = 2; // 标签的外显
  // Deprecated: replace by label_type
  GameLabelType type = 3; // 标签类型
  uint32 label_type = 4; // 标签类型，use GameLabelType
}

message ClassifyLabelList {
  string classify_name = 1; // 分类名称
  repeated GameLabel classify_labels = 2; // 分类下的标签列表
}


/** ----------GetGameLabels---------- **/
// 面板tab信息，玩法id或者混合filterId
message TabItem{
  uint32 tab_id = 1;
  string comb_id = 2;
  enum TabType{
    TabTypeSingleTab = 0; // 单个垂类房间类型
    TabTypeMtCombTab = 1; // MT混合tab
    TabTypeGangUpCombTab = 2; // 开黑混合tab
  }
  uint32 tab_type = 3; // tab类型,use TabType
}

message GetGameLabelsReq {
  uint32 uid = 1;
  // Deprecated: replace by req_tab
  uint32 tab_id = 2; // 游戏tab id
  repeated GameLabel browse_labels = 3; // 曝光的标签
  TabItem req_tab = 4; // tab信息
}

message GetGameLabelsResp {
  repeated GameLabel labels = 1; // 热门玩法标签
  bool enable = 2; // true 表示 玩法功能开启， false：玩法功能关闭
  repeated ClassifyLabelList classify_labels = 3; // 分类标签
}

/** ----------GetGameLabels---------- **/

/** ----------BatchHotGameLabels---------- **/
message BrowseLabel {
  repeated GameLabel browse_labels = 1;
}
message BatchHotGameLabelsReq {
  uint32 uid = 1;
  // Deprecated: replace by tab_list
  repeated uint32 tab_ids = 2;
  map<uint32, BrowseLabel> browse_labels_map = 3; // tab_id -> BrowseLabel
  repeated TabItem tab_list = 4; // tab信息
  map<string, BrowseLabel> comb_tab_browse_labels_map = 5; // 混合tab的浏览标签,filterId -> BrowseLabel
}

message HotGameLabel {
  repeated GameLabel labels = 1;
  bool enable = 2; // true 表示 玩法功能开启， false：玩法功能关闭
  repeated ClassifyLabelList classify_labels = 3; // 分类标签
}
message BatchHotGameLabelsResp {
  map<uint32, HotGameLabel> hot_game_labels_map = 1; // tab_id -> HotGameLabel
  map<string, HotGameLabel> comb_tab_game_labels_map = 2; // 混合tab的玩法标签
}
/** ----------BatchHotGameLabels---------- **/


message CutWordParam {
  bool is_use_stop = 1;
  bool is_use_hmm = 2;
  enum SegType {
    SegTypeDefalt = 0; // s1,t1,业务词表
    SegTypeNormal = 1; // s1,t1,业务词表
    SegTypeLabel = 2; // s1,t1,业务词表，动态加载标签
    SegTypeBusinessSeg = 3; // 业务词表，动态加载标签
  }
  SegType seg_type = 3;
}

message CutWordReq {
  uint32 uid = 1;
  string text = 2;
  uint32 limit = 3;
  bool is_not_stop = 4; // 是否使用停用词，弃用
  CutWordParam cut_word_param = 5;
}

message CutWordResp {
  repeated string labels = 1;
}


message LabelRefreshReq {
  uint32  uid = 1;
  uint32  tab_id = 2;
  repeated GameLabel labels = 3;
}

message LabelDetail {
  string name = 1;
  string parent_name = 2;
  enum LocType {
    LocType_NOTHING = 0;
    LocType_SAME_LOC_MATCH = 1; // 同城同省匹配
    LocType_LOC = 2; // 属于 城市地域 二级标签的子标签（不包括 同城匹配 标签）
  }
  LocType loc_type = 3;
  enum GeoType {
    GeoType_INVALID = 0; // 找不到匹配的省份或城市
    GeoType_PROVINCE = 1; // 省份标签
    GeoType_CITY = 2; // 城市标签
  }
  GeoType geo_type = 4;
  string geo_info = 5; // 标准的位置信息, 省份或城市
  string other_tab_name = 6; // 其他游戏-主题分类-3级标签下的游戏名称
}

message LabelRefreshResp {
  repeated GameLabel labels = 1;
  repeated LabelDetail detail = 2;
  bool enable_second_label = 3;
  bool enable_keyword_weight = 4;
}
