syntax = "proto2";

package ga.topic_channel;

import "ga_base.proto";
import "channel/channel_.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/topic-channel";

message TopicChannelInfo {
  required uint32 channel_id = 1;  // 频道id
  required uint32 display_id = 2;  // 显示Id
  required uint32 app_id = 3;  // 频道对应的APPID
  required bool   has_pwd = 4;  // 是否有密码
  required uint32 channel_type = 5;  // 频道类型 see ga::ChannelType, 开黑房那些
  required uint32 bind_id = 6;
  required uint32 switch_flag = 7;  // 各种房间开关的标记
  required uint32 creator_uid = 8;
  required string icon_md5 = 10; // 房间图标
  required string topic_title = 11; // 房间话题描述（标题）
  required string name = 13; // 名字
  enum TopicChannelType {
    NORMAL = 0;
    RECOMMEND_CHANNEL = 1;
  }
  required TopicChannelType topic_channel_type = 14; // 房间是否为推荐房
  required uint32 online_count = 15; // 房间在线人数
  required uint32 male_count = 16; // 在线人数男人数
  required uint32 female_count = 17; // 在线人数女人数
}

message Player {
  optional uint32 uid = 1;
  optional uint32 sex = 2; //性别0：不限，1：男，2：女
  optional string account = 3;
}

message ChannelOwnerInfo {
  required uint32 uid = 1;     //
  required uint32 sex = 2;     //性别0：不限，1：男，2：女
  required string account = 3; //
  repeated string tags = 4;    //用户填写的标签
  optional string nickname = 5;//昵称
}

message TopicChannelTab {
  required uint32 id = 1;
  required string name = 2;
  optional string list_background_uri = 3;            //列表背景图的uri
  optional string card_background_uri = 4;            //弹窗背景图的uri
  optional string card_main_color = 5;                //弹窗主色 #666666
  optional string card_font_color = 6;                //弹窗文案字体颜色 #666666
  optional string card_font_background_uri = 7;       //弹窗文案背景图的uri
  optional string room_label = 8;                     //房间卡标签
  optional string radar_distribution_image = 9;       //雷达房间下发底图
  optional string room_distribution_image = 10;       //房间转移底图
  optional string bottom_text_color = 11;             //按钮文字颜色
  optional string welcome_text_color = 12;            //招募文字颜色
  optional string follow_label_img = 13;              // 跟随好友头像标头jpg图（ 欢游要的）
  optional string mask_layer = 14;                    // tab图片遮罩颜色（ 欢游要的）
}

// 用于推荐系统与数据中心跟踪统计用户行为，没具体业务意义
message RecommendationTraceInfo {
  optional uint32 recall_flag = 1;
}


message RecommendItem {
  required TopicChannelInfo channel_info = 1; //房间信息
  required TopicChannelTab  tab_info = 2;     //房间标签信息
  required ChannelOwnerInfo user_info = 3;    //房间创建者的信息
}

//直接按照返回的传回即可，不需对此另外赋值
message ListRecommendTopicChannelLoadMore {
  message All {
    optional uint32 num = 1;
    repeated uint32 recommendation = 2;
  }

  message ByTabLoadMoreItem {
    required uint64 cursor = 1;
    required uint32 last_value = 2;
    required uint32 last_index = 3;
    required int64  last_count = 4;
    required bool   the_end = 5;
  }

  message ByTabLoadMore {
    optional ByTabLoadMoreItem newborn = 1;
    optional ByTabLoadMoreItem sink = 2;
    optional ByTabLoadMoreItem big = 3;
  }

  message RecommendMore {
    required bool bottom_reach = 1;
    optional uint32 num = 2;
  }

  optional All all = 1;
  optional ByTabLoadMore by_tab = 2;
  optional RecommendMore recommend_more = 3;
}

//获取【约玩】推荐房间列表
message ListRecommendTopicChannelReq {
  required ga.BaseReq base_req = 1;
  required uint32 tab_id = 2;                                     //指定类型获取推荐列表
  optional uint32 count = 3;                                      //获取几个, 默认10
  optional ListRecommendTopicChannelLoadMore load_more = 10;      //首次获取/下拉刷新置为null，下次直接传回response返回的load_more即可
}


message ListRecommendTopicChannelResp {
  required ga.BaseResp base_resp = 1;

  repeated RecommendItem items = 2;

  optional ListRecommendTopicChannelLoadMore load_more = 10; //返回的load_more为null表示已加载完没有下一页了
  optional bool load_finish = 11;                            //返回true表示没有下一页了
}

//创建主题房
message CreateTopicChannelReq {
  required ga.BaseReq base_req = 1;
  optional string name = 2;       // 主题房名
  required uint32 tab_id = 3;     // 主题房所选分类id
  required uint32 channel_id = 4; // 房间id，如有就传过来

  message TagOption {
    required uint32 option_id = 2;      //对应选择那一块tag的id
    required uint32 item_id = 3;        //对应哪个标签的id
  }
  message RoomName {
    required uint32 content_id = 1;     //对应的文案id
    repeated TagOption option = 2;      //用户选中的标签数据
  }
  optional RoomName room_name = 5;        //拼接出来的主题房名，新版本游戏标签下用
  optional uint32 room_name_version = 6;  //指定标签下房间名配置版本号
}

message CreateTopicChannelResp {
  required ga.BaseResp base_resp = 1;
  required uint32 channel_id = 2;             // 房间id
  required ga.channel.EChannelMicMode old_mic_mode = 3;  // 房间是什么类型
  required ga.channel.EChannelMicMode new_mic_mode = 4;  // 房间需要切成什么类型的房间
  optional uint32 room_name_version = 5;      // 指定标签下房间名配置版本号
}

message GetTopicChannelInfoReq {
  required ga.BaseReq base_req = 1;
  required uint32 channel_id = 2;
  enum ChannelInfoType {
    NORMAL = 0;
    PLAY_TYPE = 1;  // 指定返回切换玩法类型房间数据
  }
  optional ChannelInfoType channel_info_type = 3; // 获取房间信息类型， V-5.0.2之前没有这个字段
  optional ga.channel.ChannelType channel_type = 4; // 房间类型,see enum ChannelType
}

enum CategoryType {
  Invalid_type = 0;
  Gangup_type = 1;  // 一起开黑类型
  CHAT_TYPE = 2;  // 旧版扩列聊天类型
  FUN_GAME_TYPE = 3; // 趣味玩法
  CASUAL_INTERACTION_TYPE = 4; // 休闲互动
  MUSIC_TYPE = 5; // 听歌唱歌
  MELEE_TYPE = 6; // 团战
  ESCAPE_ROOM_TYPE = 7; // 密室逃脱
  GROUP_CHAT_TYPE = 8; // 群聊派对
  NEW_CHAT_TYPE = 9; //新扩列聊天分类
}

// click
message GetTopicChannelInfoResp {
  required ga.BaseResp base_resp = 1;
  required uint32 channel_id = 2;
  required uint32 tab_id = 3;                     // 主题房所选分类id
  required string tab_name = 4;                   // 主题房的标签名
  required bool is_in_ground = 5;                 //
  optional bool is_private = 6;                   // 主题房是否私密房间
  optional string head_desc = 7;                  // 5vs5 | 匹配模式 | 安卓QQ 那行字
  repeated PlayingOption playing_option = 8;      // 找陌生人玩&找好友玩
  optional SwitchPlayInfo switch_play_info = 9;   // 切换玩法需要的玩法和玩法对应房间模式
  enum TabType {
    NORMAL = 0; //普通分类
    GAME = 1; //游戏分类
    MINI_GAME = 2; // 小游戏
  }
  optional TabType tab_type = 10;                 // 主题房标签类型
  optional string welcome_text = 11;              // 欢迎语
  // 从房间内跳到指定外部第三方游戏相关配置(即下载游戏)
  optional ThirdPartyGame third_party_game = 12;
  optional uint32 shift_room_duration = 13;      //房间转移时长测试
  optional uint32 freeze_duration = 14;           //冻结时长
  optional uint32 tag_id = 15;                    //游戏id
  optional string team_desc = 16;                 //小队被移除但房间的小队信息要在房间内保留（[区服][模式]-[人数]，正在找：[想找分路1]、[想找分路2]，地图：[地图1]、[地图2]）
  optional bool show_team_desc = 17;              //是否该有小队信息，没有小队服务了只能自己判断了

  optional bool show_publish_button = 18;    //是否展示发布按钮

  optional CategoryType category_type = 19; //标识当前tab所属的分类类别，1 一起开黑

}

message SwitchPlayInfo {
  required ga.channel.EChannelMicMode room_model = 3;
}

//取消主题房在大厅显示
message HideTopicChannelReq {
  required ga.BaseReq base_req = 1;
  required uint32 channel_id = 2;
  enum PushType {// 用于区分不同推送消息，兼容版本字段
    NORMAL = 0; //普通分类,兼容老版本
    FINDFRIEND = 1;   //取消找人玩。
  }
  optional PushType push_type = 3;
}

message HideTopicChannelResp {
  required ga.BaseResp base_resp = 1;
}

//20秒调用一次，房主与主题房保持心跳，超时会停止展示在大厅
message KeepAliveTopicChannelReq {
  required ga.BaseReq base_req = 1;
  required uint32 channel_id = 2;
}

message KeepAliveTopicChannelResp {
  required ga.BaseResp base_resp = 1;
  required bool the_end = 2;  //若为true，不需要再发心跳
}

// Tab 是主题房间分类管理中的一种类型。
message Tab {
  required uint32 id = 1; // id 是类型的唯一标识。
  required string name = 2; // name 代表主题。
  //bool   is_game          = 3; // is_game 代表是否为游戏分类。
  required string image_uri = 4; // image_uri 代表背景用图的URI。
  required uint32 version = 5; // version 是房间示例名称的版本。

  enum TabType {
    NORMAL = 0; //普通分类。
    GAME = 1;   //游戏分类。
    MINI_GAME = 2; // 小游戏
  }
  optional TabType tab_type = 6;
  enum RoomNameType {
    DEFAULT = 0;    //默认推荐文案
    SPLICE = 1;     //需要拼接
  }
  optional uint32 tag_id = 7; // 对应游戏标签的id
  optional RoomNameType room_name_type = 8;
  optional uint32 room_name_version = 9; // 主题房对应类型取名配置的版本号

  enum TabProperty {
    INVALID = 0;     //默认
    RECOMMEND = 1;  //特殊推荐类型
    INFINITE = 2;  //不限类型
  }
  optional TabProperty tab_property = 10;

  optional string follow_label_img = 19;  // 跟随好友头像标头jpg图
  optional string follow_label_text = 20; // 跟随好友头像标头文案
  optional uint32 category_id = 21;  // 分类id
  optional uint32 mini_game_num = 22; // 小游戏上限人数

  optional LabelType tab_label = 23; // 游戏卡片标签
  optional string mask_layer = 24; //卡片图片遮罩颜色
  optional string cards_image_url = 25; // 其实就是玩法卡图片，新增加字段因为图片尺寸和之前不一致，不确定能否兼容

  optional string room_label = 26; // 房间卡片标签
  // 小游戏配置相关字段
  optional MiniGameConfig mini_game_config = 27;
  optional int64 playing_number = 28; // 房间在玩人数
  optional uint32 category_sort = 29; //特殊类别分类排序，目前只有王者1，吃鸡2，这俩tab在5.5.0创建房间时优先于其他category
  optional bool display_elem = 30;   //特殊分类，直接展示elem
  optional string small_card_url = 31;                //v5.5.0之后首页六格专用
  optional string new_tab_category_url = 32;         //v5.5.0之后创建房间时

  optional bool has_child_list = 33; //是否有子列表，目前用于判断是否为其他游戏
  optional uint32 category_type = 34; //见CategoryType
  optional uint32 game_card_id = 35; //游戏卡片id
}


// TabTopicChannelReq 是用于获取【约玩】主题房间分类列表的请求。
message TabTopicChannelReq {
  required ga.BaseReq base_req = 1;
  optional uint32     page = 2; // 第几页，从0开始。
  optional uint32     count = 3; // 每页多少个。

  enum ReturnedMode {
    NORMAL = 0;    // 普通分类。
    RECOMMEND = 1; // 普通分类+推荐分类。
    INFINITE = 2;  // 普通分类+不限分类。
  }
  optional ReturnedMode returned_mode = 4; // returned_mode用于指定返回模式，即返回中包含哪些分类。
}

// TabTopicChannelResp 是用于获取【约玩】主题房间分类列表的响应。
message TabTopicChannelResp {
  required ga.BaseResp  base_resp = 1;
  repeated Tab tabs = 2;
  required uint32 total = 3;
  optional uint32 index = 4; //默认下标
}

// GetTopicChannelRoomNameReq 是用于获取最新房间示例名称的请求。
message GetTopicChannelRoomNameReq {
  required ga.BaseReq  base_req = 1;
  repeated uint32 tab_id = 2;
}

// ChannelNameInfo 主题房标签对应的示例名称信息。
message ChannelNameInfo {
  required uint32 tab_id = 1;
  repeated string room_name = 2;
  required uint32 version = 3;
}

// GetTopicChannelRoomNameResp 是用于获取最新房间示例名称的响应。
message GetTopicChannelRoomNameResp {
  required ga.BaseResp base_resp = 1;
  repeated ChannelNameInfo info = 2;
}

// 获取房间文明公约提示
message GetRoomProxyTipReq {
  required ga.BaseReq base_req = 1;
}

message GetRoomProxyTipResp {
  required ga.BaseResp base_resp = 1;
  required string url = 2;            //跳转url
  required string title = 3;          //标题
  enum ShowType {
    SHOW_ALWAYS = 0;                //每次弹
    CAN_HIDE = 1;                   //之前同意过就不用弹了
  }
  required ShowType show_type = 4;
}

message GetChannelRoomNameConfigReq {
  required ga.BaseReq  base_req = 1;
  required uint32 tab_id = 2;
}

message GetChannelRoomNameConfigResp {
  required ga.BaseResp base_resp = 1;
  optional RoomNameConfigure room_name_configure = 2; //channel room name配置信息
}

message TagOption {
  message TagItem {
    required string src_string = 1;      //客户端的显示的标签字符串
    optional string splice_string = 2;   //选中后拼接用的标签字符串
    optional uint32 item_id = 3;
  }

  required uint32 option_id = 1;           //模板id
  optional string source_title = 2;        //对应原来游戏标签的标题
  required string client_title = 3;        //客户端显示的标题
  repeated TagItem tag_items = 4;          //标签的对应关系
}

message TagContent {
  message SequenceItem {
    enum ItemType {//拼接类型
      SELF = 0;       //使用自身文案(text)拼接
      TAG = 1;        //使用标签的名称拼接
      TEXT = 2;       //使用指定文案作分隔符拼接
    }
    optional uint32 option_id = 1;           //对应的标签模板id
    optional string split_text = 2;          //对应的分隔文案
    required ItemType type = 3;              //类型
  }
  required string text = 1;                //文案
  repeated SequenceItem sequence = 2;     //拼接规则
  required uint32 content_id = 3;
}

// 用于自动生成房间名的配置
message RoomNameConfigure {
  optional uint32 tab_id = 1;                  //主题房的tab id
  repeated TagOption tag_option = 2;          //标签选项数据
  repeated TagContent tag_content = 3;        //文案数据
  optional uint32 version = 4;               //主题房对应类型取名配置的版本号
}


//----------------------------------V2--------------------------------------
message TopicChannelInfoV2 {
  required uint32 channel_id = 1;                           // 频道id
  required uint32 display_id = 2;                           // 显示Id
  required uint32 app_id = 3;                               // 频道对应的APPID
  required bool   has_pwd = 4;                              // 是否有密码
  required uint32 channel_type = 5;                         // 频道类型 see ga::ChannelType, 开黑房那些
  required string topic_title = 6;                          // 房间话题描述（标题）
  required string name = 7;                                 // 房间名字
  required uint32 on_mic_count = 8;                         // 麦上用户数
  optional string head_desc = 9;                            // 5vs5 | 娱乐匹配 | 安卓QQ ，第一行描述，直接显示字符串，不需要拼接，无返回就不显示 (房间名往下第一个描述)（5.9.0列表改版后开始废弃）
  optional string second_desc = 10;                         // 钻石-星耀 ，第二仲行描述，直接显示字符串，不需要拼接，无返回就不显示  (房间名往下第二个描述)（5.9.0列表改版后开始废弃）
  optional string music_desc = 11;                          // 房间正在播放的音乐, eg: "快回家看看-汪明荃"
  optional string icon_md5 = 12;                            // 房间头像
  optional uint32 bind_id = 13;                             //
  required TopicChannelInfoV2Type topic_channel_type = 14;  //哪种类型的房间
  optional string third_desc = 15;                          // 如微信上分局，QQ娱乐局 右上角描述，直接显示字符串，不需要拼接，无返回就不显示 （产品说是希望用户一眼能被吸引的信息，如国际服·排位上分）（5.9.0列表改版后开始废弃）
  repeated string fourth_desc = 16;                         // 所缺位置无返回就不显示
  optional string fourth_desc_title = 17;                   // fourth_desc的前缀
  optional string movie_desc = 18;                          // 房间正在播放的电影, eg: "正在看：武林外传第8集"
}

enum TopicChannelInfoV2Type {
  UGC = 0;                //个人房
  PGC = 1;                //娱乐房
  TMP_CHANNEL = 2;        //临时房
}

message BlockOption {
  required uint32 block_id = 1;           //块id
  required uint32 elem_id = 2;            //块里面的元素id
  optional string elem_val = 3;               // 发布用户填的值
}

enum PublishSource {
  INVALID = 0;                //非法值
  MAIN_PAGE = 1;              //首页发布的
  IN_CHANNEL = 2;             //房间内发布的
  OLD_VERSION_MAIN_PAGE = 3;  //旧版首页发布的, 调用CreateTopicChannel默认就是这个了，只有旧版本用
}
enum PublishType {
  NORMAL = 0;   // v5.0.2之前发布类型
  PLAY_PUBLISH = 1; // 玩法发布
  MINI_GAME_PUBLISH = 2; // 小游戏发布
}

enum PlayingOption {
  AT_MAIN_PAGE = 0;       //默认大厅找人玩
  FRIEND = 1;             //找好友玩
}

//发布主题房 V2
message CreateTopicChannelV2Req {
  required ga.BaseReq base_req = 1;
  optional string name = 2;                       // 主题房名
  required uint32 tab_id = 3;                     // 主题房所选分类id
  repeated BlockOption block_option = 4;          // 用户所选的标签信息, eg: [<block_id:1, elem_id:100>,<block_id:1, elem_id:101>,<block_id:2, elem_id:200>]
  optional bool is_private = 5;                   // 传true表示私密房间
  optional PublishSource publish_source = 6;      // 在哪里发布房间
  repeated PlayingOption playing_option = 7;      // 找陌生人玩&找好友玩
  optional PublishType publish_type = 8;          // 发布类型，用于区分不同发布方式逻辑
  optional bool want_fresh = 9;                    // 是否优先匹配萌新
}

message CreateTopicChannelV2Resp {
  required ga.BaseResp base_resp = 1;
  required uint32 channel_id = 2;             // 房间id
  required ga.channel.EChannelMicMode old_mic_mode = 3;  // 房间是什么类型
  required ga.channel.EChannelMicMode new_mic_mode = 4;  // 房间需要切成什么类型的房间

}

//快速组队
message QuickFormTeamReq {
  required ga.BaseReq base_req = 1;
  optional uint32 tab_id = 2;                     // 用户指定的tab id
  repeated BlockOption block_option = 4;          // 用户所选的标签信息, eg: [<block_id:1, elem_id:100>,<block_id:1, elem_id:101>,<block_id:2, elem_id:200>]
  optional uint32 request_uid = 5;                // web端用的而已，客户端忽略
  optional string channel_package_id = 11;        // 渠道包id
}

message QuickFormTeamResp {
  required ga.BaseResp base_resp = 1;
  optional uint32 channel_id = 2;             // 房间id
}

message TopicChannelItemV2 {
  required TopicChannelInfoV2 channel_info = 1;
  required ChannelOwnerInfo owner_user_info = 2;
  required TopicChannelTab tab_info = 3;
  optional RecommendationTraceInfo trace_info = 4; // 用于推荐系统与数据中心跟踪统计用户行为，没具体业务意义(透穿)
}

//获取开黑房间列表
message ListTopicChannelV2Req {
  required ga.BaseReq base_req = 1;
  optional uint32 tab_id = 2;                                         //指定类型获取推荐列表
  optional uint32 count = 3;                                          //获取几个, 默认10
  repeated BlockOption block_option = 4;                              //用户所选筛选信息, eg: [<block_id:1, elem_id:100>,<block_id:1, elem_id:101>,<block_id:2, elem_id:200>]
  optional Sex sex = 5;                                               //性别0：不限，1：男，2：女
  optional ListRecommendTopicChannelLoadMore load_more = 10;          //首次获取/下拉刷新置为null，下次直接传回response返回的load_more即可
  optional string channel_package_id = 11;                            //渠道包id
}

message ListTopicChannelV2Resp {
  required ga.BaseResp base_resp = 1;
  repeated TopicChannelItemV2 items = 2;

  optional ListRecommendTopicChannelLoadMore load_more = 10;    //返回的load_more为null表示已加载完没有下一页了
  optional bool load_finish = 11;                        //返回true表示没有下一页了
}

// 聊天列表萌新房推荐
message ListFreshmanRecommendedChannelReq {
  required ga.BaseReq base_req = 1;
  optional string channel_package_id = 2; //渠道包id
}

message ListFreshmanRecommendedChannelResp {
  required ga.BaseResp base_resp = 1;
  repeated FreshmanRecommendedChannelItem items = 2;
}

// 找玩伴返回空白列表时房间推荐
message ListPlaymateRecommendedChannelReq {
  required ga.BaseReq base_req = 1;
  optional string channel_package_id = 2; //渠道包id
}

message ListPlaymateRecommendedChannelResp {
  required ga.BaseResp base_resp = 1;
  repeated PlaymateRecommendedChannelItem items = 2;
}

message PlaymateRecommendedChannelItem{
  required TopicChannelInfoV2 channel_info = 1;
  required ChannelOwnerInfo owner_user_info = 2;
  required string find_playing_text = 3;
  required string find_playing_img = 4;
}

message FreshmanRecommendedChannelItem{
  required TopicChannelInfoV2 channel_info = 1;
  required ChannelOwnerInfo owner_user_info = 2;
  required string find_playing_text = 3;
  required string find_playing_img = 4;
}

//获取进房弹窗
message GetChannelDialogReq {
  enum GetMode {
    DEFAULT = 0;        //新用户进房弹窗
    USER_REQUEST = 1;   //客户端经过间隔时间后，主动获取场景
  }
  required ga.BaseReq base_req = 1;
  optional GetMode get_mode = 2;
  optional string channel_package_id = 11;        //渠道包id
}

message GetChannelDialogResp {
  required ga.BaseResp base_resp = 1;
  optional TopicChannelItemV2 channel_item = 2;           //若返回空则不需要弹出弹窗
  optional string text = 3;                               //显示的文案
  optional string friend_info_text = 4;                   //下面有好友在房间需要显示的文案
}

message GetDialogV2Req {
  required ga.BaseReq base_req = 1;
  optional uint32 get_count = 2;                  //第几次获取了，从1开始
  optional bool never_into_channel = 3;           //本地没进过房给个true
  optional string channel_package_id = 11;        //渠道包id
}

enum DialogStyle {
  AVATAR_CENTER = 0;                              //头像在中心显示的样式，方便之后扩展，后面增加枚举值不识别都用这个作为默认样式
  LOGO_CENTER = 1;                                //游戏logo在中心显示的样式
}

message DialogConfig {
  optional string logo_url = 1;                        //中心logo url
  optional string full_background_url = 2;             //全屏背景url
  optional string horizontal_background_url = 3;       //横屏背景url
  optional string button_color = 4;                    //按钮颜色, #589644
  optional string loading_button_color = 5;            //loading中按钮颜色 #580044
  optional string button_text = 6;                     //按钮文案
  optional string invite_text = 7;                     //邀请文案，返回值 "梁非凡，一起开黑吗？在线等队友"
  optional DialogStyle dialog_style = 8;               //弹窗包含的样式类型
}

message GetDialogV2Resp {
  required ga.BaseResp base_resp = 1;
  required uint32 tab_id = 2;                         //先判断tab id，为0表示没有可推荐的房间就不需要弹窗了
  optional uint32 channel_id = 3;                     //临时房没有这个值，个人房有这个值
  optional TopicChannelInfoV2Type type = 4;           //用于判断是否临时房
  optional DialogConfig config = 5;                   //弹窗的样式配置
  repeated Player player_list = 6;                    //玩家列表，头像中心显示的拿第0个
  optional string desc = 7;                           //房间区服的那些描述，返回值 "安Q | 娱乐匹配 | 五排"
  optional string tab_name = 8;                       //eg:王者荣耀，和平精英
}

message GetGuideConfigReq {
  required ga.BaseReq base_req = 1;
  optional string channel_package_id = 2;         //渠道包id
}

message GetGuideConfigResp {
  required ga.BaseResp base_resp = 1;
  required string guide_image_url = 2;        //浮层url
}

//快速组队显示信息
message GetFormTeamInfoReq {
  required ga.BaseReq base_req = 1;
  optional bool check_game_card_status = 2;       //是否需要检测当前用户是否已完善游戏卡
  optional string channel_package_id = 3;         //渠道包id
}

message GetFormTeamInfoResp {
  required ga.BaseResp base_resp = 1;
  required uint32 total_online_count = 2;     //在数人数
  message ExampleUser {
    required uint32 uid = 1;
    required string account = 2;
  }
  repeated ExampleUser user_list = 3;         //轮换示例用户
  required bool fill_game_card = 4;           //是否需要完善游戏卡
  optional string background_url = 5;         //背景图url
  optional string guide_image_url = 6;        //浮层url
  optional string package_tab_info_json = 7;  //渠道包对应的tab id以及tab name, eg: {"tabId":1, "tabName":"王者"}
  optional string bubble_image_url = 8;       //气泡url

  enum UserType {
    NORMAL = 0;             //普通用户
    CHANNEL_PACKAGE = 1;    //渠道包用户
    TABLE_GAMING = 2;       //选了桌游的用户
  }
  optional UserType user_type = 9;
  optional uint32 default_tab_id = 10;        //默认选中的tab id
  optional string default_tab_name = 11;      //默认选中的tab name
}


// Relation 联系，某个元素与其他栏目的联系
message Relation {
  required uint32 block_id = 1;
  required uint32 before = 2;
  required uint32 after = 3;
}

// Elem 元素，多个元素构成一个栏目
message Elem {
  required uint32 id = 1;
  required string title = 2;
  optional Relation relations = 3;
  // Mode 是 Elem 的模式
  enum Mode {
    NORMAL = 0;    // 普通
    RECOMMEND = 1; // 推荐
    INFINITE = 2;  // 不限
  }
  required Mode mode = 4;
  optional string mini_game_model = 5; // 小游戏模式字段
  optional uint32 team_size = 6;    //小队大小
  optional uint32 priority = 7; //当前block内elem的排序优先级
  optional uint32 min_num = 8;  // block mode = INPUT = 2; 可填写最小值
  optional uint32 max_num = 9;  // block mode = INPUT = 2; 可填写最大值
  optional string flag_url = 10;
}

// Block 栏目，由多个元素构成
message Block {
  required uint32 id = 1;
  required string title = 2;
  // Mode 是 Block 的选择模式
  enum Mode {
    SINGLE = 0;     //单选
    MULTI = 1;      //多选
    USER_INPUT = 2;    // 用户可输入类型
  }
  required Mode mode = 3;
  repeated Elem elems = 4;
  optional bool control_team_size = 5;
  required uint32 most_select_num = 6;
  optional string sub_title = 7;

  message ButtonOpt {
    optional bool is_show = 1;
    // see enum BlockButtonType
    optional uint32 type = 2;
  }
  optional ButtonOpt button_opt = 8; // 发布选项后续的按钮操作选项
}

enum BlockButtonType {
  BUTTON_TYPE_UNSPECIFIED = 0;
  // 将发布选项文案设为麦位名称
  BUTTON_TYPE_SET_MIC_NAME = 1;
}

//性别0：不限，1：男，2：女
enum Sex{
  All = 0;
  Male = 1;
  Female = 2;
}

message ListTabBlocksReq {
  required ga.BaseReq base_req = 1;
  required uint32 tab_id = 2;
  // Mode 是筛选模式的类型
  enum Mode {
    PUBCHANNEL = 0;             // 发布房间
    MATCHTEAMMATE = 1;          // 速配队友
    FILTER = 2;                 // 筛选
    ZAIYA_MATCH_TEAMMATE = 3;   // 在呀  速配队友
    FILER_IN_RADAR = 4;         // V5.5.0开黑列表雷达处
  }
  required Mode mode = 3; // 根据筛选模式返回 tab
  optional uint32 tag_id = 4;
}

message ListTabBlocksResp {
  required ga.BaseResp base_resp = 1;
  repeated Block blocks = 2; // 某个 tab 的所有 block
  optional uint32 tab_id = 3;
  enum TabType {
    NORMAL = 0; //普通分类。
    GAME = 1;   //游戏分类。
    MINI_GAME = 2; // 小游戏
  }
  optional TabType tab_type = 4;
  enum MatchType {
    QUICK = 0; // 快速匹配，即旧匹配。
    TEMPORARYHOUSE = 1; // 临时房匹配
  }
  optional MatchType match_type = 5;
  optional uint32 channel_source = 6;
  optional uint32 tab_version = 7; //版本号
  optional bool fresh_option = 8; //优先匹配萌新显示开关，待推荐接入后开启
  optional Sex sex = 10 ;//性别0：不限，1：男，2：女

  // 获取小队位置配置
  message LocationConfig {
    repeated Location location_list = 1;
  }
  message Location {
    required  string name = 1;
    required string icon = 2;
  }
  repeated LocationConfig location_config_list = 11;

  optional bool is_hidden_geo_option = 12;  //是否隐藏地理位置选项
}

//--------------------------------------------切换模式相关接口--------------------------------------------------------------------
message SwitchGamePlayReq {
  required ga.BaseReq base_req = 1;
  required uint32 tab_id = 2;
  required string tab_name = 3;
  required uint32 channel_id = 4;
  optional PublishType publish_type = 5;          // 发布类型，用于区分不同发布方式逻辑
  optional string channel_name = 6;               //v5.5.0后切换模式时候允许修改房间名
}
message SwitchGamePlayResp {
  required ga.BaseResp base_resp = 1;
}

//---------------------------------------------小游戏相关接口-----------------------------------------------------
message GetTabListReq {
  required ga.BaseReq base_req = 1;
  optional uint32     page = 2; // 第几页，从0开始。
  optional uint32     count = 3; // 每页多少个
  enum ReturnedMode {
    NORMAL = 0;    // 普通分类。
    RECOMMEND = 1; // 普通分类+推荐分类。
    INFINITE = 2;  // 普通分类+不限分类。
    MORECARDS = 3; // 更多卡片
    FASTPC = 4; // 极速版PC来源
  }
  optional ReturnedMode returned_mode = 4; //returned_mode用于指定返回模式，即返回中包含哪些分类。
  repeated uint32 self_game_list = 5; //手机内有装游戏的列表
  optional string channel_pkg = 6; // 用户渠道号
  optional bool is_need_minority_game = 7; // 是否需要返回小众游戏外显
}

message GetTabListResp {
  required ga.BaseResp base_resp = 1;
  enum TabType {
    NORMAL = 0; //普通分类。
    GAME = 1;   //ugc分类。
    MINI_GAME = 2; // 小游戏
  }
  message TabCategory {
    required uint32 category_id = 1; // 分类id
    required string category_name = 2; // 分类名称
    repeated Tab tab_detail = 3; // 具体玩法内容
    optional TabType tab_type = 4; //游戏类型
    //    optional bool can_select_all = 5; //能否全选
    optional uint32 can_select_num = 5; //可选数量 0为可全选
    optional uint32 special_category_mapping = 6; //特殊分类标识，值见CategoryType
  }
  repeated TabCategory tab_category = 2;

}
//-------------------------------------------------首页卡片位相关----------------------------------------------------------------
enum LabelType {
  DEFAULT = 0; //默认没有标签
  NEW = 1; //新发布标签
  HOT = 2; // 热门标签
  ALPHATEST = 3;//内测标签
}

message MiniGameConfig {
  optional uint32 cp_id = 1;
  optional uint32 game_id = 2;
  optional string game_version = 3;

  optional string game_name = 4;
  optional string game_package = 5;
  optional string game_url = 6;

  optional uint32 engine_ver = 7;

  optional uint32 game_member_cnt_limit = 8;          // 进入游戏的人数限制

  optional uint32 engine_type = 9;

  repeated uint32 game_app_limit = 10;
  repeated uint32 game_platform_limit = 11;

  repeated MiniGameEngine mini_game_engine = 12;

  optional string game_digest = 13;
  optional string game_res_url = 14;
  optional string game_res_digest = 15;

  optional string main_package_url = 16;
}

message MiniGameEngine {
  optional uint32 engine_id = 1;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  optional string gameUrl = 2;
  optional string name = 3;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  optional string resUrl = 4;
}

enum CardType {
  ALLCARD = 0;
  MINIGAMECARD = 1;
  UGCCARD = 2;
  ADCARD = 3;
  PGCCARD = 4;
}

// 卡片基本信息
message PlayCards {
  optional uint32 card_id = 1; // 卡片id
  optional string card_name = 2; //卡片名称，即主题房名称
  optional string card_url = 3; // 卡片图片
  optional string mask_layer = 4; //背景色
  // 玩法卡图片、标签等相关外观属性字段
  optional LabelType label_type = 5;
  optional CardType card_type = 6;
  optional uint32 tab_id = 7; //tabid
  optional int64  playing_number = 8; // 在房间人数
  // 小游戏配置相关字段
  optional MiniGameConfig mini_game_config = 9;
  // 广告跳转
  optional string skip_url = 10; // 外链

  // 从房间内跳到指定外部第三方游戏相关配置(即下载游戏)
  optional ThirdPartyGame third_party_game = 11;

  optional string small_card_url = 12; // v5.5.0之后的卡片图片

}

message GetHomePageCardListReq {
  required ga.BaseReq base_req = 1;
  optional uint32     page = 2; // 第几页，从0开始。
  optional uint32     count = 3; // 每页多少个
  optional string     distributor = 4; //渠道
  optional uint32     active_tab_id = 5; // 常玩卡片，客户端告诉后台

}

message GetHomePageCardListResp {
  required ga.BaseResp base_resp = 1;
  repeated PlayCards play_cards = 2;
}

message QuickFormTeamV2Req{
  required ga.BaseReq base_req = 1;
  optional uint32 tab_id = 2;                     // 用户指定的tab id
  repeated BlockOption block_option = 3;          // 用户所选的标签信息, eg: [<block_id:1, elem_id:100>,<block_id:1, elem_id:101>,<block_id:2, elem_id:200>]

}

message QuickFormTeamV2Resp {
  required ga.BaseResp base_resp = 1;
  optional uint32 channel_id = 2;
}

message ThirdPartyGame {
  optional string label_url = 1; // 图标
  optional string public_url = 2; // 公屏图片

  message GameBaseInfo {
    optional string platform = 1;
    optional string jump_url = 2; // 跳转地址
    optional string download_url = 3; // 下载地址
    optional string package_name = 4; // ','号分割
  }
  repeated GameBaseInfo game_base_info = 3; // 第三方游戏基础信息
}

//发布房间 V3
//创建并发布
message CreateAndReleaseTopicChannelReq {
  required ga.BaseReq base_req = 1;
  optional string name = 2;                       // 主题房名
  required uint32 tab_id = 3;                     // 主题房所选分类id
  repeated BlockOption block_option = 4;          // 用户所选的标签信息, eg: [<block_id:1, elem_id:100>,<block_id:1, elem_id:101>,<block_id:2, elem_id:200>]
  optional bool is_private = 5;                   // 传true表示私密房间
  optional PublishSource publish_source = 6;      // 在哪里发布房间
  repeated PlayingOption playing_option = 7;      // 找陌生人玩&找好友玩
  optional PublishType publish_type = 8;          // 发布类型，用于区分不同发布方式逻辑
  optional bool want_fresh = 9;                    // 是否优先匹配萌新

  repeated string location_list = 10;
  optional bool show_geo_info = 11;                   //是否允许同城匹配

  optional string diy_game_name = 12;    //自定义游戏名称，小众游戏上报

  repeated uint32 all_selected_bids = 13;    //已全选的blockId
}

message CreateAndReleaseTopicChannelResp {
  required ga.BaseResp base_resp = 1;
  required uint32 channel_id = 2;             // 房间id
  required ga.channel.EChannelMicMode old_mic_mode = 3;  // 房间是什么类型
  required ga.channel.EChannelMicMode new_mic_mode = 4;  // 房间需要切成什么类型的房间
  required uint32 freeze_duration = 5;         //冻结时长
  optional uint32 shift_room_duration = 6;      //房间转移时长
  optional uint32 change_cool_down = 7;         //房间发布信息修改cd
  optional bool is_change = 8;                  //是否修改
  optional uint32 auto_dismiss_duration = 9;    //自动取消发布时长
}

//取消发布房间
message DismissTopicChannelReq {
  required ga.BaseReq base_req = 1;
  required uint32 channel_id = 2;             // 房间id
}

message DismissTopicChannelResp {
  required uint32 freeze_duration = 1;         //冻结时长
}

message DistributionTopicChannelReq {
  required ga.BaseReq base_req = 1;
  optional uint32 tab_id = 2;                                         //指定类型获取推荐列表
  optional uint32 count = 3;                                          //获取几个, 默认1
  repeated BlockOption block_option = 4;                              //用户所选筛选信息, eg: [<block_id:1, elem_id:100>,<block_id:1, elem_id:101>,<block_id:2, elem_id:200>]
  optional Sex sex = 5;                                               //性别0：不限，1：男，2：女
  enum DistributeSource{
    FromRoom = 0;
    FromRadar = 1;
  }
  optional DistributeSource distribute_source = 6;                    //请求来源，0：房间转移，1：雷达房间下发
  optional string channel_package_id = 11;                            //渠道包id
}

message DistributionTopicChannelResp {
  required ga.BaseResp base_resp = 1;
  repeated TopicChannelItemV3 items = 2;
  optional string footprint = 3; // 客户端上报到数仓
}

//包含小队信息 v5.5.0开启
message TopicChannelItemV3 {
  required TopicChannelInfoV2 channel_info = 1;
  required ChannelOwnerInfo owner_user_info = 2;
  required TopicChannelTab tab_info = 3;
  optional RecommendationTraceInfo trace_info = 4; // 用于推荐系统与数据中心跟踪统计用户行为，没具体业务意义(透穿)
  optional string welcome_tittle = 5;         //邀请标题

  // 查询房间小队情况 已废弃 ----------begin
  message ChannelTeamInfo {
    // 房主uid
    optional uint32 owner_uid = 1;
    // 房间成员信息
    repeated MemberInfo member_list = 2;
    // 房间小队创建时间
    optional int64 create_time = 3;
    // 房间小队ui类型
    enum UiType {
      UNKOWN = 0;         //无
      GLORYOFKINGS = 1;           //王者荣耀
      GAMEOFPEACE = 2;           //和平精英
    }

    optional UiType ui_type = 4;
    optional string team_name = 5;
    optional string game_name = 6;
  }
  message MemberInfo {
    optional uint32 uid = 1;
    optional string account = 2;
    optional string nickname = 3;
    optional int32 sex = 4;//性别0：不限，1：男，2：女
    optional string dan = 5;
    optional string dan_url = 6;
    optional string game_nickname = 7;
    optional string location_name = 8;
    optional string location_icon = 9;
    optional bool disabled = 10;
  }
  optional ChannelTeamInfo channel_team_info = 13;  //小队信息
  // 查询房间小队情况 已废弃 ---------end

  optional uint32 level_id = 14;      //房间等级id
  optional string geo_info = 15;      //地理信息

  optional RCMDLabel rcmd_label = 16; //推荐行为标签
  optional string head_desc = 17;                           // 在房间名之上的描述，如国际服·排位上分（5.9.0改版后可见）
  optional string first_desc = 18;                          // 如微信上分局，QQ娱乐局 右上角描述，直接显示字符串，不需要拼接，无返回就不显示 (房间名往下第一个描述)（5.9.0改版后可见）
  optional DescIcon second_desc_icon = 19;                  //房间名往下第二个描述的icon图标 (主要是音乐信息)（5.9.0改版后可见）
  optional string second_desc = 20;                         // 钻石-星耀 ，第二仲行描述，直接显示字符串，不需要拼接，无返回就不显示  (房间名往下第二个描述)（5.9.0改版后可见）
  optional MemberInfo member = 21; // 房内关系链用户（无论是否在麦）＞麦上异性＞房主
  optional string member_tag = 22; // 推荐用户标签
  optional string welcome_content = 23; // 邀请内容
  optional TopicChannelItemType type = 24;
}

enum TopicChannelItemType{
  TopicChannelItemTypeNormal = 0;
  TopicChannelItemTypeMusic = 1;
}

enum DescIcon{
  DescIcon_None = 0;
  DescIcon_MUSIC = 1;
  DescIcon_KTV = 2;
}

enum RCMDLabel {
  RCMDLabel_None = 0;
  RCMDLabel_GangUpWithHomeOwner = 1;
  RCMDLabel_ChatWithHomeOwner = 2;
}

message GetTabListWhenCreateReq {
  required ga.BaseReq base_req = 1;
}

message GetTabListWhenCreateResp {
  required ga.BaseResp base_resp = 1;
  enum TabType {
    NORMAL = 0; //普通分类。
    GAME = 1;   //ugc分类。
    MINI_GAME = 2; // 小游戏
  }

  //特殊分类法，王者和吃鸡单独当作一个分类
  message SpecialCategory {
    required uint32 category_id = 1; // 分类id
    required string category_name = 2; // 分类名称
    repeated Tab tab_detail = 3; // 具体玩法内容
    optional TabType tab_type = 4; //游戏类型
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    optional uint32 SpecialSort = 5; // 特殊排序，数字小的在前面 目前王者在1，吃鸡在2）
    optional string image_uri = 6; // image_uri 代表背景用图的URI。
  }

  repeated SpecialCategory special_category = 2;
}

message TopicChannelRcmdBrowseInfo {
  repeated uint32 no_browse_list = 1; //上次请求未曝光id列表
}

// 询问用户游戏偏好
message PrefGame {
  // 游戏标签
  message Label {
    // 标签类型
    enum Type {
      TypeSystem = 0; // 系统标签
      TypeCustom = 1; // 用户自定义标签
    }

    optional uint32 id = 1;
    optional string val_value = 2;
    optional Type type = 3;
  }

  required uint32 tab_id = 1;
  optional string tab_name = 2;
  optional string tab_img = 3;

  repeated Label labels = 4;
}

message ListTopicChannelV3Req {
  required ga.BaseReq base_req = 1;
  optional uint32 tab_id = 2;                                         //指定类型获取推荐列表
  optional uint32 count = 3;                                          //获取几个, 默认10
  repeated BlockOption block_option = 4;                              //用户所选筛选信息, eg: [<block_id:1, elem_id:100>,<block_id:1, elem_id:101>,<block_id:2, elem_id:200>]
  optional Sex sex = 5;                                                 //性别0：不限，1：男，2：女
  //  optional uint32 tag_id = 6;                                         //雷达游戏tag_id
  optional ListRecommendTopicChannelLoadMore load_more = 10;          //首次获取/下拉刷新置为null，下次直接传回response返回的load_more即可
  optional string channel_package_id = 11;                            //渠道包id
  optional TopicChannelRcmdBrowseInfo browse_list = 12; //请求列表曝光信息
  repeated uint32 category_ids = 13; //分类id，用于全选小游戏 为了兼容以后万一有多选分类的，就设置成数组
  repeated uint32 tab_ids = 14;  //用于多选小游戏

  // 用户询问, 喜好游戏及标签
  repeated PrefGame pref_games = 15;

  repeated uint32 all_selected_bids = 16;    //已全选的blockId

  //玩法标签数组
  repeated GameLabel labels = 26; // 标签筛选
}

// 预约开黑相关配置
message GangConf {
  // 游戏群组
  message GameGroup {
    // 游戏群组成员
    message Member {
      // 群成员 account, 用来拼接群头像链接
      required string account = 1;
    }

    // 群id
    required uint32 id = 1;
    // 群名称
    required string name = 2;
    // 群 account
    required string account = 3;
    // 群人数
    optional uint32 member_num = 4;
    // 用户是否已加入了群组
    optional bool user_joined = 5;
    // 加入群组是否需要验证(0:不需要验证 1:需要验证 2:任何人无法加入)
    optional uint32 need_verify = 7;

    // 群成员列表
    repeated Member member_list = 6;
  }

  enum BookingState {
    // 用户没有任何开黑预约
    BookingStateNull = 0;
    // 用户预约开黑了但未填写游戏卡信息
    BookingStateNoFilled = 1;
    // 用户预约开黑了并且填写了游戏卡信息
    BookingStateFilled = 2;
  }

  // 游戏群组
  repeated GameGroup group_list = 1;

  // 是否配置开启预约开黑
  optional bool enabled_booking = 2;
  // 开黑预约时长选项
  repeated int64 booking_durations = 3;
  // 用户预约开黑状态
  optional BookingState booking_state = 4;
  // 用户预约开黑后的倒计时
  optional uint32 booking_countdown = 5;

  // 用户所在游戏群组account
  repeated string my_group_acc_list = 7;

}

message ListTopicChannelV3Resp {
  required ga.BaseResp base_resp = 1;
  repeated TopicChannelItemV3 items = 2;
  optional ListRecommendTopicChannelLoadMore load_more = 10;    //返回的load_more为null表示已加载完没有下一页了
  optional bool load_finish = 11;                        //返回true表示没有下一页了

  // 预约开黑相关配置
  optional GangConf gang_conf = 12;
  // 用户询问, 喜好游戏及标签
  repeated PrefGame pref_games = 13;
  // 用户喜好游戏询问插入下标
  optional uint32 pref_game_pos = 14;
  optional string footprint = 15; //推荐trace id
}

//5.5.17加入，用于替代GetTabListWhenCreate
message ShowTopicChannelTabListReq {
  required ga.BaseReq base_req = 1;
  repeated uint32 self_game_ids = 2;
  optional string channel_pkg = 3; // 用户渠道号
  optional bool is_need_minority_game = 4; // 是否需要返回小众游戏外显
  optional uint32 req_source = 5; //请求来源
}

enum ReqSource{
  DEFAULT_SOURCE = 0;
  PC_HOME_PAGE_SOURCE = 1;
  FAST_PC_REQ_SOURCE = 2; // 极速版PC请求
}

message ShowTopicChannelTabListResp {
  required ga.BaseResp base_resp = 1;
  repeated ShowTopicChannelTabPrimaryItem primary_item = 2; //一级
  repeated FastPcCategoryInfo fast_pc_category_info = 3;
}

message FastPcCategoryInfo {
  required uint32 category_id = 1; // 分类id
  required string category_name = 2; // 分类名称
  repeated Tab tab_detail = 3; // 具体玩法内容
}

message ShowTopicChannelTabPrimaryItem {
  repeated ShowTopicChannelTabSecondaryItem secondary_item = 1;
  required string item_text = 2; // 分类名称
  required string mask_layer = 3;
  optional uint32 category_id = 4;
  required string item_icon = 5;
  optional uint32 special_category_mapping = 6; //特殊分类标识，值见CategoryType
}

message ShowTopicChannelTabSecondaryItem{
  required string item_text = 1; // 分类名称
  required string item_icon = 2; //icon
  enum TabType {
    NORMAL = 0; //普通分类。
    GAME = 1;   //游戏分类。
    MINI_GAME = 2; // 小游戏,
    MUSIC = 3; // 音乐
  }
  optional TabType tab_type = 3;
  optional uint32 tab_id = 4;
  optional string mask_layer = 5; //卡片图片遮罩颜色
  optional uint32 category_id = 6;
  optional bool has_list = 7;  //小众游戏
  optional string small_card = 8; //用于替换常玩菜单图标
  optional SpecialTip special_tip = 9;

  enum SpecialTip{
    None = 0;  //无。
    New = 1;   //New。
  }
}

message HomeEntryCardListReq{
  required ga.BaseReq base_req = 1;
}

message HomeEntryCardListResp{
  required ga.BaseResp base_resp = 1;
  repeated HomeEntryCard home_entry_card_list = 2;
}

message HomeEntryCard{
  required string title = 2;
  repeated string secondary_text = 3;
  enum EntryType{
    QUICK_MATCH = 0;  //快速匹配
    CHAT_CARD = 1;    //扩列卡
    SLIP_NOTE = 2;    //小纸条
  }
  required EntryType entry_type = 4;
}

message GetSubTabListReq{
  required ga.BaseReq base_req = 1;
  optional uint32 parent_tab_id = 2;
  repeated uint32 self_game_ids = 3;
  enum ReturnedMode {
    NORMAL = 0;    // 普通分类
    MORECARDS = 3; // 更多卡片
  }
  optional ReturnedMode return_mode = 4;
}

message GetSubTabListResp{
  required ga.BaseResp base_resp = 1;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  repeated ShowTopicChannelTabSecondaryItem ShowTopicChannelTabSecondaryItem = 2;
}

message GetQuickMatchTabListReq{
  required ga.BaseReq base_req = 1;
  enum EntryType{
    HOME_PAGE = 0;  //首页拉取
    HOME_ENTRY = 1; //首页入口进入
  }
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  optional EntryType entryType = 2;
  repeated uint32 self_game_ids = 3;
  repeated uint32 active_ids = 4;
  optional uint32 link_game_id = 5;   //deeplink匹配的ugameid
  optional string channel_pkg = 6; // 用户渠道号
  optional uint32 home_page_filter_tab_id = 7; // 新版首页快速匹配玩法首页筛选器对应的tabId，resp返回需要带上这个玩法的信息
}

message GetQuickMatchTabListResp{
  required ga.BaseResp base_resp = 1;
  repeated QuickMatchItem tab_items = 2;
  optional string big_image = 3;
  optional string secondary_text = 4;
  optional bool is_ab_test = 5; // 用户是否ab测试实验组，false-默认是对照组，true-实验组
}

message QuickMatchItem {
  required string item_text = 1; // 显示文字
  required string item_icon = 2;
  required string mask_layer = 3;
  optional uint32 tab_id = 4;
  optional uint32 category_id = 5;
  optional bool has_list = 6;  //小众游戏
  optional string secondary_text = 7;// 小标题：多少人在玩
  optional CardType card_type = 8;
  optional MiniGameConfig mini_game_config = 9;// 小游戏配置相关字段
  optional string image_url = 10; //大图
}

//负反馈上报选项
message GetNegativeFeedBackOptionReq{
  required ga.BaseReq base_req = 1;
  optional uint32 channel_id = 2;
}
message GetNegativeFeedBackOptionResp{
  required ga.BaseResp base_resp = 1;
  repeated NegativeFeedBackOption feedback_options = 2;    //房间选择的开房标签信息
}

message NegativeFeedBackOption{
  optional uint32 block_id = 1;
  optional string title = 2;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  required NegativeFeedbackType negative_Feedback_type = 3;
  repeated string reasons = 4; // 选择选项的原因
}

message NegativeFeedBackReq{
  required ga.BaseReq base_req = 1;
  optional uint32 channel_id = 2;
  optional uint32 tab_id = 3;
  optional uint32 uid = 4;
  repeated NegativeFeedBackOption feedback_options = 5;    //反馈选项
  optional string name = 6;
}
enum NegativeFeedbackType {
  FeedbackTypeInvalid = 0; // 无效值
  FeedbackTypeChannelOwner = 1; // 房主
  FeedbackTypeChannelTab = 2; // 游戏类型
  FeedbackTypePublishCond = 3; // 发布条件
  FeedbackTypeChannelName = 4; // 房间名称
}
enum FeedbackKeywordType {
  FEEDBACK_KEYWORD_TYPE_UNSPECIFIED = 0;
  // 年龄关键词
  FEEDBACK_KEYWORD_TYPE_AGE = 1;
  // 性别关键词
  FEEDBACK_KEYWORD_TYPE_GENDER = 2;
}
message NegativeFeedBackResp{
  required ga.BaseResp base_resp = 1;
  // 匹配到的关键词类型 see enum FeedbackKeywordType
  repeated uint32 matched_keywords = 2;
}

message GetLiveTogetherConfigReq{
  required ga.BaseReq base_req = 1;
  optional uint32 channel_id = 2;
}
message GetLiveTogetherConfigResp{
  required ga.BaseResp base_resp = 1;
  optional AdsConfig ads_config = 2;
  optional LiveConfig live_config = 3;
  optional BubbleConfig bubble_config = 4;
  optional bool has_owner = 5;
  optional uint32 refresh_time = 6;
}

message SetLiveTogetherStatusReq{
  required ga.BaseReq base_req = 1;
  optional uint32 channel_id = 2;
  optional UGCLiveTogetherStatus status = 3;
}
message SetLiveTogetherStatusResp{
  required ga.BaseResp base_resp = 1;
}

message AdsConfigChangeMsg{
  optional AdsConfig ads_config = 1;
}

message LiveConfigChangeMsg{
  optional LiveConfig live_config = 1;
}

message BubbleConfigChangeMsg{
  optional BubbleConfig bubble_config = 1;
}

// 插播广告配置
message AdsConfig {
  optional uint32 id = 1;
  // 广告链接
  optional string link = 2;

  optional uint32 updated_at = 3;
  optional uint32 live_id = 4;
}

enum UGCLiveTogetherStatus{
  NONE = 0;  //没变更
  CLOSE = 1; //关闭
  OPEN = 2;  //开启
}

// 直播配置
message LiveConfig {
  enum PlayFormat {
    PlayFormatOther = 0;
    PlayFormatRtmp = 1;
  }

  optional uint32 id = 1;
  optional uint32 config_updated_at = 2;

  optional UGCLiveTogetherStatus live_status = 3; //直播开启状态
  optional uint32 status_change_at = 4;//状态变更时间

  // 生效开始时间
  optional uint32 begin_at = 5;
  // 生效结束时间
  optional uint32 end_at = 6;

  // 房间主题id
  optional uint32 tab_id = 7;
  //马甲包id
  repeated uint32 market_id = 8;

  // 直播链接
  optional  string live_link = 10;
  // 直播入口文案
  optional  string entrance_text = 11;
  // 直播入口配图
  optional string entrance_pic = 12;
  // 直播悬浮球配图
  optional string float_pic = 13;
  // 视频标题
  optional string video_title = 14;
  // 视频比例：长度
  optional uint32 video_height = 15;
  // 视频比例：宽度
  optional uint32 video_width = 16;
  // 视频播放格式
  optional PlayFormat play_format = 17;

  //是否有强引导
  optional bool has_strong_guide = 18;
  optional string guide_gif = 19;//强引导图片
  optional string guide_title = 20;//强引导标题
  optional uint32 limit_close_second = 21;//N秒后关闭按钮
}

// 气泡配置
message BubbleConfig {
  optional  uint32 id = 1;
  optional uint32 updated_at = 2;

  // 房间主题id
  optional uint32 tab_id = 5;

  // 生效开始时间
  optional uint32 begin_at = 6;
  // 生效结束时间
  optional uint32 end_at = 7;

  //马甲包id
  repeated uint32 market_id = 8;

  // 气泡文案
  optional string text = 10;
}

//获取热门玩法时，写死type为HotLabel，其他业务场景都不处理这个字段
enum GameLabelType {
  Default = 0;
  HotLabel = 1; // 热门标签（支持标题玩法标签），val由多个标签值组成，用英文逗号分割
  LabelOfPublish = 2; // 发布条件标签
  LabelOfTabName = 3; // 游戏类型标签
  LabelOfGlobal = 4; // 通用类型标签
  LabelOfClassify = 5; // 分类标签
}

message GameLabel {
  required string val_value = 1; // 标签val_value 需要传回给 推荐
  required string display_name = 2; // 标签的外显 value
  required GameLabelType type = 3[deprecated = true]; // 标签类型
  optional string parent_name = 4; // 二级标签
  optional string root_name = 5; // 一级标签
  optional uint32 label_type = 6; // 使用uint32透传推荐的labelType，see GameLabelType
}

//玩法标签搜索
message LabelSearchReq {
  required ga.BaseReq base_req = 1;
  optional uint32 tab_id = 2; // 游戏tab id
  optional string text = 3; // 搜索文本
  optional string filter_id = 4; // 多品类玩法标签筛选
}

message LabelSearchResp {
  required ga.BaseResp base_resp = 1;
  repeated GameLabel labels = 2;
}

message BrowseLabel {
  repeated GameLabel browse_labels = 1;
}

//获取热门玩法
message GetGameLabelsReq {
  required ga.BaseReq base_req = 1;
  optional uint32 tab_id = 2; // 游戏tab id
  optional BrowseLabel browse_labels = 3;
}

message GetGameLabelsResp {
  required ga.BaseResp base_resp = 1;
  repeated GameLabel labels = 2;
  required bool enable = 3; // true 表示 玩法功能开启， false：玩法功能关闭
  required bool hide_filter = 4; // 隐藏筛选 true:隐藏
}

message GetLabelSearchGuideReq{
  required ga.BaseReq base_req = 1;
  optional uint32 tab_id = 2; // 游戏tab id

}

enum HintType {
  DEFAULT_HINT = 0; // 默认兜底文案
  RCMD_HINT = 1; // 推荐文案, 用于点击直接填充文案
}

message GetLabelSearchGuideResp {
  required ga.BaseResp base_resp = 1;
  required string hint = 2;
  required HintType hint_type = 3;
}

message GetTCCacheReq {
  required ga.BaseReq base_req = 1;
}

message TCCacheData {
  repeated uint32 ids = 1;
}

message TCCacheBlockData {
  optional uint32 block_id = 1;
  repeated uint32 elem_ids = 2;
}

message TCCacheTabData {
  optional uint32 tab_id = 1;
  repeated TCCacheBlockData block_datas = 2;
}

message GetTCCacheResp {
  required ga.BaseResp base_resp = 1;
  map<uint32, TCCacheData> category_tabs_map = 2;
  repeated uint32 tab_ids = 3;
  repeated uint32 min_game_tab_ids = 4;
  repeated TCCacheTabData tab_display_block_ids = 5;
  repeated TCCacheTabData tab_base_block_ids = 6;
}
