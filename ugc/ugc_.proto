syntax = "proto3";

package ga.ugc;

import "ga_base.proto";
import "rcmd/rcmd_.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/ugc";

// ugc中的用户信息
message UserUGCInfo {
    uint32 uid = 1; // id
    string account = 2; // 账号
    string alias = 3; // 数字账号
    string nickname = 4; // 昵称
    uint32 gender = 5; // 性别
    string signature = 6; // 个性签名
    uint32 is_official = 7; // 是否官方人员

    uint32 liked_count = 19; // 被赞数
    string face_md5 = 20; // 头像id, 用于拼接头像下载链接
    uint32 follower_count = 21; // 粉丝数
    uint32 following_count = 22; // 关注数
    uint32 post_count = 23; // 动态数
    bool following = 24; // 是否关注中
    bool follow_me = 25; // 是否关注了“我”
    repeated Attachment recent_attachments = 26; // 最近发的附件

    message RoomInfo {
        uint32 room_id = 1;
        uint32 room_type = 2; // channel_.proto: enum ChannelType
        bool room_is_pwd = 3;
        uint32 bind_id = 4; // 房间绑定的id
        string find_playing_text = 10;//找人玩
        string find_playing_img = 11;//找人玩

        // 哪个 app 的房间 see friendol_.proto ChannelAppType
        uint32 channel_app_type = 12;

    }
    RoomInfo current_room = 27; // 所在的房间信息
    GrowInfo grow_info = 28; // 成长信息

    //官方认证信息
    string certify_title = 30; //官方认证
    string certify_intro = 31; //认证介绍
    string certify_style = 32; //认证样式, official官方认证，ent娱乐房认证，ugc优质内容生产者

    string recommend_letter = 38; //大V的推荐语

    string game_svr_name = 40; //游戏区服
    string game_svr_rank = 41; //游戏段位


    string card_bg_url = 50; //卡片背景图

    //贵族相关字段
    message Nobles {
        uint64 value = 1; // 贵族对应积分值
        uint64 keep_value = 2; // 保持贵族身份需要积分值
        uint32 level = 3; // 贵族等级
        uint32 cycle_ts = 4; //
    }
    Nobles noble_info = 60;
    bool show_follow = 61; // 是否显示关注粉丝数
    uint32 vip_level = 62; // vip等级
    bool is_director_certify = 63;  //乐窝代理人

    message SpInfo {
        uint32 sp_level = 1; //超级会员等级
        bool is_year_member = 2;//是否是年费会员
        ga.SuperPlayerLevel super_player_level=3; //超级会员等级信息等
    }
    SpInfo sp_info = 64; //超级会员信息

    bool is_anchor_feed = 65; //是否主播流的标识

    string certify_special_effect_icon = 66;  // 大v图标动效

    /* 个人认证标识 */
    uint32 cert_type = 67; /* personal-certification.proto CertType */
    string icon = 68;
    string text = 69;
    repeated string color = 70;
    string text_shadow_color = 71; /* 文字阴影颜色 */
    /* 个人认证标识 */

    //如果item_name为空，就是没有考核认证
    message ExamineCertMsg {
        string item_name = 1;
        string base_imgurl = 2;     // 标识底图
        string shadow_color = 3;    // 配置文字投影颜色
    }
    //考核认证相关标识
    ExamineCertMsg radio_live_cert = 72; // 语音直播认证标识
    ExamineCertMsg multi_play_cert = 73; // 多人互动认证标识

    enum NameplateType {
        TYPE_ERROR = 0; // 错误类型
        TYPE_LOTTER = 1;  // 资源类型lotter
    }
    message NameplateDetailInfo {
        uint32 id = 1; // 铭牌id
        string name = 2; // 铭牌名称
        string base_url = 3; // 铭牌基础资源url
        string dynamic_url = 4; // 铭牌动态资源的url
        NameplateType type = 5; // 资源类型
        uint32 start_time = 6; // 开始时间
        uint32 finish_time = 7; // 到期时间
    }
    repeated NameplateDetailInfo nameplates = 74; //营收铭牌

    // ip 归属地
    string ip_location = 75;

    /* 个人认证标识 */
    UserPersonalCertOptInfo cert_opt_info = 76;
    /*个人认证标对应的社群信息*/
    ga.PersonalCertificationMuseSocialCommunity social_community_info = 77;
    /*用户是否在线*/
    bool   user_online=78;
    string cert_id=79;  //认证标id
}



/* 个人认证标识 */
message UserPersonalCertOptInfo{
    string introduce = 1; /* 介绍 */
    string jump_url = 2; /* 跳转链接 */
    string cert_type_name = 3; /* 类型名称 */
}


// 附件
enum AttachmentType {
    ATTACHMENT_TYPE_NONE = 0;
    IMAGE = 1;
    GIF = 2;
    VIDEO = 3;
    CMS = 4;
    AUDIO = 5;
}

message Attachment {
    AttachmentType attachment_type = 1; // 附件类型
    string attachment_content = 2; // 附件内容
    string extra_info = 3; //给客户端自定义玩，虽然以后加也可以
    uint64 create_at = 4; // 创建时间
    string key = 5;
    string vm_content= 6; // 带水印的
    // 只有type = VIDEO 才有的
    string param = 10;
    //原视频封面
    string origin_video_cover = 11;

}

message UploadAttachmentInfo {
    AttachmentType attachment_type = 1;
    string attachment_key = 2;
    string extra_info = 3; //给客户端自定义玩，虽然以后加也可以
}

enum AttachmentDownloadPrivacy {
    PRIVACY_DEFAULT = 0;
    PRIVACY_PRIVATE = 1;
    PRIVACY_PUBLIC = 2;
}

enum PostType {
    POST_TYPE_NONE = 0;
    POST_TYPE_TEXT = 1;
    POST_TYPE_IMAGE = 2;
    POST_TYPE_VIDEO = 3;
    POST_TYPE_CMS = 4;
    POST_TYPE_AUDIO = 5;
}

enum AbnormalStatus {
    ABNORMAL_NONE = 0;
    BANNED = 1; // 被屏蔽
    UNDER_REVIEW = 2; // 审核中
}

enum RichTextType {
    INVALID = 0;
    AT = 1; //@
    AT_SOCIAL_COMMUNITY=2; //社群@
}

message RichText {
    RichTextType type = 1;

    message AtData {
        uint32 uid = 1; // id
        string account = 2; // 账号
        string nickname = 3; // 昵称
    }

    AtData at_data = 11; //@人的时候有值
}

enum StickyStatus {
    STICKY_NONE = 0;
    STICKY = 1;
}

enum PostPrivacyPolicy {
    POST_PRIVACY_POLICY_DEFAULT = 0; // 默认是公开的
    POST_PRIVACY_POLICY_PRIVATE = 1; // 仅自己可见
}

//解码方式
enum UnmarshalType {
    UNMARSHAL_TYPE_DEFAULT = 0;
    UNMARSHAL_TYPE_PROTOBUF = 1; //protobuf
    UNMARSHAL_TYPE_JSON = 2; //json
}


//内容来源
enum ContentOrigin {
    ORIGIN_DEFAULT = 0;
    ORIGIN_MK_AI = 1;   //ORIGIN_GAME_DISTRICT = 1; //游戏专区 ORIGIN_SOCIAL_COMMUNITY_DISTRICT = 2; //社群专区
}


//通用内容 因为客户端不支持枚举，废弃
message GeneralContent {
    UnmarshalType unmarshal_type = 1;
    ContentOrigin content_origin = 2;
    string content = 3;
}

//通用内容
message UGCGeneralContent {
    uint32 unmarshal_type = 1; //UnmarshalType
    uint32 content_origin = 2;  //ContentOrigin
    string content = 3;
    bytes byte_content = 4;
}


// 查看帖子详情
message PostInfo {
    enum PostSource {
        SOURCE_DEFAULT = 0;
        SOURCE_RECOMMENDATION = 1;      // 推荐流
        SOURCE_GEO_NEWEST = 2;          // 城市最新流
        SOURCE_GEO_RECOMMENDATION = 3;  // 城市推荐流
        SOURCE_TOPIC_NEWEST = 4;        // 话题最新
        SOURCE_TOPIC_RECOMMENDATION = 5;    // 话题推荐
        SOURCE_OPER_RECOMMENDATION = 6;     // 精选推荐
        SOURCE_PERSONAL_RECOMMENDATION = 7; // 个人主页推荐
        SOURCE_FOLLOW  = 8;                 // 关注流
        SOURCE_ANCHOR = 9;                  // 主播流
        SOURCE_TAB_TOPICS = 10;                  // TAb话题流
    }
    string post_id = 1;
    UserUGCInfo post_owner = 2;
    PostType post_type = 3;
    string content = 4;
    repeated Attachment attachments = 5;
    TopicInfo belong_to_topic = 6;
    uint64 post_time = 7; // 发帖时间, unix second
    uint32 top_level_comment_count = 8;
    uint32 comment_count = 9;
    uint32 attitude_count = 10;
    uint32 view_count = 11; // 浏览量
    Attitude my_attitude = 12;
    // status
    bool is_deleted = 13; // 是否被删除
    uint32 share_count = 14; // 分享数
    string label = 15; // 运营配置的特殊标签
    bool had_favoured = 16; //是否收藏
    StickyStatus sticky_status = 17; // 置顶

    AbnormalStatus abnormal_state = 20; // 异常状态判断

    //add by v3.4.2
    TopicInfo belong_to_sub_topic = 21;
    ContentType contentType = 22; //内容类型，发帖的时候不用传过来，后台识别

    uint32 post_source = 23; // 主要用于新版推荐流 区分帖子来源  见PostSource, 不确定旧版是否兼容, 还是用是uint32吧
    repeated uint32 tags = 24;

    string tgl_cover_url = 25;
    string tgl_cover_size = 26;

    // TT5.4.0
    GeoInfo geoInfo = 27;
    AttachmentDownloadPrivacy privacy = 28;
    uint32 page_source = 29;     // 吉总说，给客户端占坑用，表示服务端不写页面来源

    // TT5.4.2
    PostPrivacyPolicy policy = 30; // 隐私策略

    //TT5.4.3 发帖来源
    PostOrigin origin = 31; // 发帖来源

    repeated TopicInfo diy_topic_ids = 32;  //个人话题列表

    enum PostStatus {
        PostSuc = 0;          // 审核通过
        PostSuspicious = 1;   // 审核中
        PostFail = 2;         // 审核失败
    }
    PostStatus post_status = 33;  //用于个人流针对用户本人发的帖子增加标注
    PostMachineStatus post_machine_status = 34;  //用于个人流针对用户本人发的帖子机器审核未过时表明发送失败
    PartyGoInfo partygo_info = 35;
    MoodInfo mood_info = 36;
    string rcmd_meta_id = 37;
    string label_remind = 38;
    bool is_obs = 39;

    // 帖子关联的投票信息
    VoteInfo vote = 40;

    // ip 归属地
    string ip_location = 41;

    repeated GeneralContent general_contents = 42;

    string tag_img = 43; // 标签图标，可用于精品内容图标显示
    string title = 44; // 标题
    repeated UGCGeneralContent ugc_general_contents = 45; // 通过内容能力

    enum ZonePolicy {
        DEFAULT = 0; // 默认是公开的
        ONLY_ZONE = 1; // 仅专区可见
    }
    ZonePolicy zone_policy = 46; // 专区策略
}

// 投票信息
message VoteInfo {
    // 投票过期策略
    enum ExpiryT {
        ExpiryNever = 0; // 从不
        ExpiryCustom = 1; // 自定义
    }

    // 投票选项内容类型
    enum OptionT {
        OptionTNone = 0;
        OptionTText = 1; // 文本
    }

    // 投票整体内容类型
    enum VoteT {
        VoteTNone = 0;
        VoteTText = 1; // 文本
    }

    message Option {
        // 投票选项id
        string id = 1;
        // 投票选项内容类型
        OptionT optionT = 2;
        // 投票选项内容
        string content = 3;
        // 选项投票人数
        uint32 votes = 4;
    }

    // 帖子所属用户uid
    uint32 uid = 1;
    // 投票标题
    string title = 2;

    // 投票过期策略
    ExpiryT expiryT = 3;
    // 投票过期时间戳(秒)
    uint32 expired_at = 4;

    // 用户投票选择的选项id, 为空表示没有投过
    string option_id = 5;
    // 投票选项列表
    repeated Option options = 6;

    // 总投票人数
    uint32 votes = 7;

    // 投票整体内容类型
    VoteT vote_t = 8;
}

message MoodInfo {
    string mood_id = 1;
    string mood_bind_topic_id = 2; /* 心情绑定的topicid */
    string mood_name = 3; //要填
}

message PartyGoInfo {
    enum FeedType {
        RecommnendType = 0;
        TopicType = 1;
        NormalType = 2;
    }
    string text = 1;
    string url = 2;
    repeated string topic_ids = 3;
    repeated FeedType feed_types = 4;
}

enum PostMachineStatus{
    MachineDefault = 0; /*默认*/
    MachineSuspicious = 1; /*疑似*/
    MachineNormal = 2; /*同意*/
    MachineReject = 3; /*拒绝*/
}

enum ContentType {
    PRESET_CONTENT = 0;
    FORMATTED = 1; //部分格式化后的content格式，eg: &^****************************************************************************************************&^
    //格式化文本前后使用 '&^' 包裹，包裹内容元数据({"at_data":{"uid":1234567,"account":"********","nickname":"Tom"},"type":1})经过base64编码，
}

enum StepType {
    StepNone = 0; // 无
    MusicStep = 1; // 音乐版本的踩
}
// 查看评论列表/查看评论的评论列表
message CommentInfo {
    string post_id = 1;
    string conversation_id = 2;
    string comment_id = 3;
    UserUGCInfo from_user = 4;
    string content = 5;
    repeated Attachment attachments = 6;
    Attitude my_attitude = 7;
    UserUGCInfo to_user = 8;
    uint32 total_sub_comment_count = 9;
    repeated CommentInfo sub_comments = 10;
    uint32 attitude_count = 11;
    uint64 create_at = 12; // 发表评论时间
    bool is_deleted = 13; // 是否被删除
    bool is_obs = 14;
    StepOn my_step = 15;
    StepType step_type = 16;
    uint32 step_count = 17;

    AbnormalStatus abnormal_status = 20; // 异常状态判断
    ContentType contentType = 21; //内容类型，发评论的时候不用传过来，后台识别

    // ip 归属地
    string ip_location = 22;
}

// 表态
enum Attitude {
    NO_ATTITUDE = 0; // 未表态
    LIKE = 1; // 点赞
    ATTITUDE_NONE = 2; //未操作
}

enum StepOn {
    NO_STEP = 0; // 未表态
    STEP = 1; // 踩
    STEP_NONE = 2; //未操作
}

message CardTagInfo {
    uint32 tag = 1;
    string tag_desc = 2;
}

message CardUserInfo {
    uint32 uid = 1;
    string account = 2;
}

// Feed
message Feed {
    // 帖子
    message Post {
        PostInfo post = 1;
        uint64 timestamp = 2;
    }
    // 推荐主题
    message RecommendTopics {
        repeated TopicInfo topics = 1;
        repeated OperationResource resources = 2;

    }

    //推荐话题
    message RecommendSubTopics {
        repeated TopicInfo sub_topics = 1;
    }

    // 推荐好友
    message RecommendUsers {
        repeated UserUGCInfo users = 1;
    }

    // 推荐好友
    message RecommendUsersV2 {
        repeated UserUGCInfo users = 1;
    }

    message AnchorInfo {
        Post post_info = 1;
        uint32 uid = 2;
        string user_name = 3;
        string follow_text = 4;
        string follow_icon = 5;
        uint32 channel_id = 6;
    }

    message HighContentRecommendCard {
        string account = 1;
        string certify_title = 2;
        string certify_style = 3; //认证样式, official官方认证，ent娱乐房认证，ugc优质内容生产者
        string nickname = 4;
        bool attention = 5;
        bool new_post = 6;
        uint32 uid = 7;
        string certify_special_effect_icon = 8;  // 大v图标动效
    }

    message HighContentRecommendMutliCard {
        repeated CardTagInfo tag_infos = 1;
        repeated CardUserInfo user_infos = 2;
    }

    oneof feed_data {
        Post post = 1;
        RecommendTopics recommend_topics = 2;
        RecommendUsers recommend_users = 3;
        RecommendSubTopics recommend_topics_sub = 4;
        RecommendUsersV2 recommend_users_v2 = 5;
        HighContentRecommendCard highcontent_recommend_card = 6;
        HighContentRecommendMutliCard highcontent_recommend_multi_card = 7;
        AnchorInfo anchor_user = 8;
    }

    string feed_id = 10;

}

// UserIdentifier用于对目标用户查询或者操作
message UserIdentifier {
    oneof key_type {
        uint32 uid = 1;
        string account = 2;
        string alias = 3;
    }
}

// ---------------------主题---------------------
//订阅
enum TopicType {
    TypeTopic = 0; //主题
    TypeSubTopic = 1; //话题
    TypeGeo  = 2; //城市 地理位置
    TypeDIY  = 3; // 自定义话题
    TypeMood = 4; //心情
    TypeTalk = 5; //讨论话题
}


message SubscribeTopicReq {
    ga.BaseReq base_req = 1;
    string topic_id = 2; //主题id
    TopicType  topic_type = 3; //指定类型
}

message SubscribeTopicResp {
    ga.BaseResp base_resp = 1;
}

//取消订阅
message UnsubscribeTopicReq {
    ga.BaseReq base_req = 1;
    string topic_id = 2; //主题id
}

message UnsubscribeTopicResp {
    ga.BaseResp base_resp = 1;
}

message TopicInfo {
    string topic_id = 1;
    string name = 2;
    string desc = 3;
    string icon_url = 4;
    uint32 member_count = 5; // 成员数
    uint32 post_count = 6; // 动态数量
    uint32 update_at = 7; // 最后一个帖子发布的时间
    uint32 create_at = 8; // 主题创建时间
    bool is_subscribe = 9; // 是否订阅此主题 此字段仅在获取主题详细资料命令有效

    repeated OperationResource stick_resources = 10; //每个主题下的置顶资源位

    //add by v3.4.2
    repeated TopicInfo parent_topic_infos = 11; //如果为话题的话 此字段有内容
    repeated TopicInfo sub_topic_infos = 12; //如果为主题的话 此字段有内容 表示该主题下的话题

    //观看数量
    string view_count_text = 13;
    //类型
    TopicType topic_type = 14; //指定类型
    repeated string multi_pic_url_list = 15; // 多图
}

message GeoInfo {
    string id = 1;
    string city_code = 2; // baidu city code
    string city_name = 3;
    uint32 post_count = 4;
    int64  update_at = 5; // 最后一个帖子发布的时间
    string lng_lat = 6; // 发帖时候传的经纬度
    string full_name = 29; // 发帖时候传的完整地理位置名称
}

//运营资源位
message OperationResource {
    string label = 1; //资源位的tag eg 左上角
    string icon_url = 2; //资源位icon
    string content = 3; //资源位的文本内容
    string jump_url = 4; //资源位的跳转url
}

message TopicLoadMore {
    uint32 page = 1;
}




//获取主题列表
message GetTopicListReq {

    //add by v3.4.2

    ga.BaseReq base_req = 1;
    uint32 page = 3; //分页拉取 页数从1开始
    uint32 count = 4; //分页拉取 拉取数量

    //add by v3.4.2
    TopicType topic_type = 5;
    string parent_topic_id = 6; //如果指定主题 则返回该主题下的话题

    //add by v5.4.2
    uint32 uid =7 ;
}

message GetTopicListResp {
    ga.BaseResp base_resp = 1;
    repeated TopicInfo topics = 2; //简短主题列表
}

//返回指定主题详细资料
message GetTopicInfoReq {
    ga.BaseReq base_req = 1;
    string topic_id = 2; //主题id
    //add by v3.4.2
    string name = 3; //根据名字查找
    //传入uid
    uint32 uid = 4;
}


message GetTopicInfoResp {
    ga.BaseResp base_resp = 1;
    TopicInfo topic = 2; //主题详细内容
    bool show_recommend = 3;//是否显示推荐tab
}

//获取订阅的主题列表
message GetSubscribeTopicListReq {
    ga.BaseReq base_req = 1;
    uint32 count = 2; //分页拉取 拉取数量
    TopicLoadMore load_more = 3;
}

message GetSubscribeTopicListResp {
    ga.BaseResp base_resp = 1;
    repeated TopicInfo topics = 2;
    bool isSubscribeAll = 3; //是否订阅所有
    TopicLoadMore load_more = 4;
}

//获取未订阅的主题列表
message GetUnSubscribeTopicListReq {
    ga.BaseReq base_req = 1;
    uint32 count = 2; //分页拉取 拉取数量 返回的实际的数量有可能跟此值不一样
    TopicLoadMore load_more = 3; // 下一次加载更多时, 将load_more原封不动地填入请求的load_more中; 如果不包含此字段, 表示已经拉完了

}

message GetUnSubscribeTopicListResp {
    ga.BaseResp base_resp = 1;
    repeated TopicInfo topics = 2;
    TopicLoadMore load_more = 3; // 下一次加载更多时, 将load_more原封不动地填入请求的load_more中; 如果不包含此字段, 表示已经拉完了
}

//判断主题是否已经订阅
message CheckTopicsIsSubscribeReq {
    ga.BaseReq base_req = 1;
    repeated string topic_ids = 2; //主题id 可以批量查询
}

message CheckTopicsIsSubscribeResp {
    ga.BaseResp base_resp = 1;
    map<string, bool> info = 2; // true ：表示已经订阅
}


message SearchTopicReq {
    ga.BaseReq base_req = 1;
    string name = 2;//模糊查询
    int32 last_count = 3;//最后分页item Count 初始为0
    string last_id = 4;//最后分页 页id 初始为空字符串
}

message SearchTopicResp {
    message TopicInfo {
        string name = 1;
        string topic_id = 2; //如果创建话题
        int32 count = 3;//数量
        bool is_new = 4;//是否是近x天的话题
        string text_count = 5;//数量的文字
        TopicType topic_type = 6; //指定类型
    }
    ga.BaseResp base_resp = 1;
    repeated TopicInfo  topic_list = 2;//模糊查询返回名单
    int32 last_count = 3;//最后分页item Count 初始为0
    string last_id = 4;//最后分页 页id 初始为空字符串 返回空 即为到底了
    bool need_create = 5;//第一次请求会判断 精确匹配没有就返回true 后面都为false
}



enum PostOrigin {
    POST_ORIG_NORMAL = 0;
    POST_ORIN_ICE_BREAK = 1; // 破冰
    POST_ORIN_ACT = 2; // 活动页
    POST_REGISTER = 3; //进驻动态
    POST_ORIG_KOL = 4;  //服务端识别
    POST_ORIG_TGL = 5;  //服务端识别
    POST_ORIG_TGL_COVER = 6;  //服务端识别
    POST_ORIG_AI_RAPER = 7;   //ai raper
    POST_ORIG_AI_FOREIGN_LANGUAGE = 8;   //ai 外语
    POST_ORIG_MIJING_COMMENT = 9; // 谜境剧本评价
    POST_ORIG_GAME_ACTIVITY = 10; // 开黑活动发帖

    POST_ORIG_ANCIENT = 1000; // 旧版本发的认为无效
}

//    -------- 帖子 --------
// 发帖
message PostPostReq {
    ga.BaseReq base_req = 1;
    string belong_to_topic_id = 2; // 主题id
    string content = 3; // 帖子内容
    PostType type = 4; // 帖子类型
    uint32 attachment_image_count = 5; // 图片附件数量
    uint32 attachment_video_count = 6; // 视频附件数量
    repeated string client_attachment_identifiers = 7; // 附件在客户端的唯一表示（如文件路径、文件etag等均可）
    //add by v3.4.2
    string belong_to_sub_topic_id = 8; // 话题id
    PostOrigin origin = 9; // 发帖来源
    uint32 attachment_audio_count = 10; // 音频附件数量

    repeated Attachment predefined_attachments = 11;

    // TT5.4.0
    string city_code = 12; //
    string lng_lat = 13; //
    string full_geo_name = 14; // for display

    AttachmentDownloadPrivacy privacy = 15;

   //extra 额外信息
   Extra extra = 30;
   repeated DiyTopicInfo diy_topic_ids = 31;
   TTGamePostInfo game_info = 32;
   MoodInfo mood_info = 33; /*心情信息*/
   bool is_obs = 34; //发贴走obs
   bool is_anchor_feed = 35;

   // 投票信息
   VoteInfo vote = 36;
   repeated string page_tag_id_list = 37 [deprecated = true] ; // 广场推荐页用户兴趣内容选择的标签ID
}

message TTGamePostInfo {
    uint32 gameid = 1;   //gameid:1 partygo； 2:ai-rapper ai说唱短链 3:ai-rapper ai外语短链
    string game_text= 2;
    string game_url = 3;
}

message DiyTopicInfo {
   string diy_topic_id = 1;
   string diy_topic_name = 2;
}

message Extra {
  uint32 width = 1;
  uint32 heigth = 2;
}

message PostPostResp {
    ga.BaseResp base_resp = 1;
    string post_id = 2; // 帖子id
    string image_token = 3; // upload image token
    string video_token = 4; // upload video token
    repeated string image_keys = 5; // 上传用的key
    repeated string video_keys = 6; // 上传用的key
    string audio_token = 7; // upload audio token
    repeated string audio_keys = 8; // 上传用的key
    repeated DiyTopicInfo diy_topic_ids = 9;//返回的话题信息
    repeated MoodInfo mood_infos = 10;
}

message MarkPostAttachmentUploadedReq {
    ga.BaseReq base_req = 1;
    string post_id = 2;
    repeated UploadAttachmentInfo attachment_list = 3; // 上传完的附件key，注意顺序
    // V3.4.2 - 支持图片评论，如果是上传评论的附件，则带上评论的id
    string comment_id = 4;
    bool is_obs = 5;
}

message MarkPostAttachmentUploadedResp {
    ga.BaseResp base_resp = 1;
}

message UpdateAttachmentDownloadPrivacyReq {
    ga.BaseReq base_req = 1;
    string post_id = 2;
    string comment_id = 3;
    AttachmentDownloadPrivacy privacy = 4;
}

message UpdateAttachmentDownloadPrivacyResp {
    ga.BaseResp base_resp = 1;
}

// 删贴
message DeletePostReq {
    ga.BaseReq base_req = 1;
    string post_id = 2;
}

message DeletePostResp {
    ga.BaseResp base_resp = 1;
}

message GetPostReq {
    ga.BaseReq base_req = 1;
    string post_id = 2;
    ContentType content_type = 3; //默认返回富文本转义后的纯文本格式(旧版)，新版加了@ 传FORMATTED的值，用枚举方便以后扩展
    FeedOrigin feed_origin = 4;
    string topic = 5;  //如果是话题流时带这个字段
}

message GetPostResp {
    ga.BaseResp base_resp = 1;
    PostInfo post_info = 2;
}

enum FeedOrigin {
    NONE = 0;
    FOLLOW = 1;         // 关注流
    RECOMMENDATION = 2; // 推荐流
    PERSON = 3;         // 个人流
    TOPIC = 4;          // 圈子流(现在这个变成话题流)
    SUB_TOPIC = 5;      // 话题流
    CITY = 6;           // 城市流
}

// 帖子上报阅读
message PostView {
    string post_id = 1;          // post_id
    uint64 duration = 2;         // 阅读时长, 毫秒
    FeedOrigin feed_origin = 3;  // 哪条流
    string origin_id = 4;        // 来源id, 当"个人/圈子/话题 流"时,分别是对应的uid, topic_id, sub_topic_id
}

// 上报已读; 上报阅读时长
message ReportPostViewReq {
    ga.BaseReq base_req = 1;
    repeated string post_id_list = 2;     // deprecated since Android 3.4.5
    repeated PostView post_view_list = 3; // from Android 3.4.5
}

message ReportPostViewResp {
    ga.BaseResp base_resp = 1;
}

message PostMultimediaView {
    ga.BaseReq base_req = 1;
    string post_id = 2;

    enum MultimediaType {
        NONE = 0;
        IMAGE = 1;
        VIDEO = 2;
    }
    enum ViewLocation {
        INVALID = 0;
        ZOOM_IN = 1;     // 点击多媒体放大
        AUTO_PLAY = 2;   // 在流列表自动播放
        POST_DETAIL = 3; // 在帖子详情页面
    }
    MultimediaType type = 3; // 多媒体类型
    uint64 duration = 4;     // 阅读时长, 毫秒
    FeedOrigin feed_origin = 5;  // 在哪条流阅读的视频/图片, 和上报帖子是一个道理
    string origin_id = 6;        // 来源id, 当"个人/圈子/话题 流"时,分别是对应的uid, topic_id, sub_topic_id
    ViewLocation view_location = 7;      // 播放多媒体的位置
}

message ReportPostMultimediaViewReq {
    ga.BaseReq base_req = 1;
    repeated PostMultimediaView multimedia_view_list = 2;
}

message ReportPostMultimediaViewResp {
    ga.BaseResp base_resp = 1;
}

//    -------- 评论 --------

// 发评论/回复评论
message PostCommentReq {
    ga.BaseReq base_req = 1;
    string post_id = 2; // 帖子id
    string comment_id = 3; // 评论id：传0即评论帖子，非0则评论“某个评论“
    string conversation_id = 4;
    string content = 5; // 评论内容
    repeated Attachment attachments = 6; // 附件类型  DEPRECATED
    repeated string client_attachment_identifiers = 7; // 附件在客户端的唯一表示（如文件路径、文件etag等均可）

    // V3.4.2 - 支持图片评论（协议预留支持多图，目前只支持单图）
    uint32 attachment_image_count = 8; // 图片附件数量
    ga.rcmd.RecommendSourceType source = 9; // 点赞/评论 一级页面来源
    bool no_content_check = 10; /* 无需进行文本审核 */
    bool is_obs = 11; //发评论走obs
}

message PostCommentResp {
    ga.BaseResp base_resp = 1;
    string post_id = 2;
    string comment_id = 3;
    CommentInfo my_comment = 4;

    // V3.4.2 - 上传图片用的token及key（协议预留支持多图，目前只支持单图）
    string image_token = 5; // upload image token
    repeated string image_keys = 6; // 上传用的key
}

// 删评论
message DeleteCommentReq {
    ga.BaseReq base_req = 1;
    string post_id = 2; // 帖子id
    string comment_id = 3;
}

message DeleteCommentResp {
    ga.BaseResp base_resp = 1;
}

message GetCommentListReq {
    enum OrderBy {
        // 兼容旧版
        UNSPECIFIC = 0;
        // 自动，即由服务器决定排序顺序
        AUTOMATIC = 1;
        // 按时间升序
        CREATE_TIME_ASC = 2;
        // 按时间降序
        CREATE_TIME_DESC = 3;
    }

    message LoadMore {
        string  next_comment_id = 1;
        OrderBy order_by = 2;               // 第一页拉取时决定
        OrderBy sub_comments_order_by = 3;  // 第一页拉取时决定
    }

    ga.BaseReq base_req = 1;
    string post_id = 2;

    // 如果传了，表示拉子评论
    string comment_id = 3;
    string load_more = 4;
    uint32 count = 5;

    // 排序规则，仅请求第一页（load_more为空）时有效，分页加载更多时，服务器会以load_more中的排序规则为准
    OrderBy order_by = 6;
    // 在一级评论页面中，露出的二级评论的排序规则。请求二级评论列表时不填
    OrderBy sub_comments_order_by = 7;

    bool query_replying_comment = 8; // 拉取子评论时，同时拉取当前评论的信息
    ContentType content_type = 9; //默认返回富文本转义后的纯文本格式(旧版)，新版加了@ 传FORMATTED的值，用枚举方便以后扩展
}


message GetCommentListResp {
    ga.BaseResp base_resp = 1;
    string post_id = 2;
    repeated CommentInfo comments = 3;
    string load_more = 4;

    // 返回实际的OrderBy（请求是AUTOMATIC时，服务器会填充实际的排序规则；否则跟客户端传的一致)，分页加载时无需填入下一次请求中
    uint32 order_by = 5;
    // 返回实际的OrderBy（请求是AUTOMATIC时，服务器会填充实际的排序规则；否则跟客户端传的一致)，展开列表进入二级评论页时可以带上该参数
    uint32 sub_comments_order_by = 6;

    CommentInfo replying_comment = 7; // 被评论的评论信息
}

enum InteractiveType {
    PRESET = 0;        //默认(获取'喜欢'+'评论')
    NEW_ATTITUDE = 1;   //获取'喜欢'
    NEW_COMMENT = 2;    //获取'评论'
    NEW_AT_MSG = 4;     //获取'被@'
    NEW_CONCERN = 8; // 关心啊, 催更啊


    AT_PRESET = 15;
}

//获取互动消息
message GetInteractiveMsgReq {
    ga.BaseReq base_req = 1;
    InteractiveType type = 2; //使用位或运算传，方便后面加类型 [NEW_ATTITUDE | NEW_COMMENT | NEW_AT_MSG]，【旧版本用】
    uint32 limit = 3; //列表获取多少项
    string last_id = 4; //从last_id开始获取信息,直接传response里面的last_id
    ContentType content_type = 5; //默认返回富文本转义后的纯文本格式(旧版)，新版加了@ 传FORMATTED的值，用枚举方便以后扩展
    repeated InteractiveType types = 12; //选择获取哪个类型的消息
}

message GetInteractiveMsgResp {
    ga.BaseResp base_resp = 1;
    int32 attitude_count = 2;
    int32 comment_count = 3;
    int32 at_count = 7;
    int32 concern_count = 8;
    repeated InteractiveInfo info = 4;
    string history_mark_id = 5;         //当info.id == 此值，表示此行有'历史消息'标志
    string last_id = 6;                 //此值为空，表示全部已拿完
}

message InteractiveInfo {
    InteractiveType type = 1; // DEPRECATED: 直接使用interactive_detail, 1‘新粉丝’，2‘喜欢’，3‘评论’
    int64 time = 2; //通知时间t

    // 下列字段废弃，暂时保留以防现有版本出错，用from_user代替
    uint32 from_user_id = 3; //用户id           // DEPRECATED
    string from_user_nickname = 4; //账号       // DEPRECATED
    string from_user_account = 5; //nickname   // DEPRECATED

    string id = 6;

    UserUGCInfo from_user = 10;

    message NewAttitudeMsg {
        Attitude attitude = 1; // 表态类型
        PostInfo post_object = 2; // 表态所关联的帖子信息
        CommentInfo comment_object = 3; // 若该字段有值，则表示被表态的是一个评论
    }

    message NewCommentMsg {
        CommentInfo comment_info = 1; // 被评论的时候返回，评论的信息
        PostInfo post_object = 2; // 该评论相关的帖子信息
        CommentInfo comment_object = 3; // 被评论的评论的信息
    }

    message NewAtMsg {
        CommentInfo comment_object = 1; // 若该字段有值，则表示在评论中被@了，否则在帖子中被@了
        PostInfo post_object = 2;       // 被@所关联的帖子信息
    }

    message NewConcernMsg { // 关心啊, 催更

    }

    oneof interactive_detail {
        NewCommentMsg comment_msg = 7;         //被评论的时候返回
        NewAttitudeMsg attitude_msg = 8;       //被赞的时候返回
        NewAtMsg at_msg = 9;                   //被@的时候返回
        NewConcernMsg concern_msg = 20; // 催更
    }

}

//进入消息列表页，标记已读互动消息
message MarkReadReq {
    enum MarkType {
        DEFAULT = 0;  //默认，标记评论+赞通知
        FOLLOW = 1;   //标记新粉丝
        COMMENT = 2;  //标记评论
        ATTITUDE = 3; //标记点赞
        AT = 4;       //标记@
        CONCERN = 5;  //标记关心
    }
    ga.BaseReq base_req = 1;
    string first_id = 2; //传获取到列表的第一个id，表示往后信息已读；如标记新粉丝红点无获取互动消息的，不用传
    MarkType type = 3; //标记哪种已读, 旧版本用，没分类

    repeated MarkType multi_type = 4; //标记哪些类型已读，eg:传2,4表示标记评论和@已读
}

message MarkReadResp {
    ga.BaseResp base_resp = 1;
}

// 上报分享成功
message ReportPostShareReq {
    ga.BaseReq base_req = 1;
    string post_id = 2;
    // 分享去哪里, 就不记录了吧
}

message ReportPostShareResp {
    ga.BaseResp base_resp = 1;
}

//删除读互动消息
message DelInteractiveMsgReq {
    ga.BaseReq base_req = 1;
    repeated string ids = 2; //可指定删除多个动态消息
}

message DelInteractiveMsgResp {
    ga.BaseResp base_resp = 1;
}

enum InteractiveSource {
    INTERACTIVE_UNKNOWN = 0;
    INTERACTIVE_RECOMMENDATION = 1;
}

//    -------- 表态 --------
// 表态
message ExpressAttitudeReq {
    ga.BaseReq base_req = 1;
    string post_id = 2; //点赞(或者取消)的帖子id，对象是帖子传这个
    string comment_id = 3; //点赞(或者取消)的评论id，对象是评论传这个
    Attitude attitude = 4; //传Attitude::NO_ATTITUDE表示取消之前的Attitude
    bool is_robot = 5;
    string page_id = 6; // 上报用的, 来源
    ga.rcmd.RecommendSourceType source = 7; // 点赞/评论 一级页面来源
    StepOn step_on = 8;
}
message ExpressAttitudeResp {
    ga.BaseResp base_resp = 1;
}

// 帖子的表态列表
message GetAttitudeUserListReq {
    ga.BaseReq base_req = 1;
    string post_id = 2;
}

message AttitudeUser {
    UserUGCInfo user = 1;
    uint64 create_at = 2; //点赞时间
    Attitude attitude = 3; //
}

message GetAttitudeUserListResp {
    ga.BaseResp base_resp = 1;
    repeated AttitudeUser user_list = 2;
}

//    -------- 关注/粉丝 --------
/*
FriendshipOperationReq/Resp适用于下列命令:
const unsigned int CMD_FollowUser = 2562; 				// 关注用户
const unsigned int CMD_UnfollowUser = 2563;				// 取消关注用户
const unsigned int CMD_RemoveFollower = 2564;			// 移除粉丝
*/
message FriendshipOperationReq {
    ga.BaseReq base_req = 1;
    UserIdentifier user_identifier = 2;

    // 操作来源
    enum Source {
        DEFAULT = 0; // 兼容旧版

        // 固定功能点
        USER_DETAIL = 1;            // 用户详情页
        FEEDS = 2;                  // 各种信息流
        POST_DETAIL = 3;            // 帖子详情页
        ROOM = 4;                   // 房间（进房消息、卡片、麦位按钮）
        GUILD_MEMBER_INFO = 5;      // 公会中的用户信息
        USER_FOLLOWER_LIST = 6;     // 用户粉丝列表
        USER_RECOMMENDATION = 7;    // 好友推荐
        IM_CONVERSATION = 8;        // IM会话页
        USER_TAG_MATCH = 9;         // 用户标签匹配
        USER_TAG_RECOMMEND = 10;    // 用户标签推荐

        RECOMMEND_POST_IN_FEEDS = 11;       // feeds流里的帖子推荐区
        RECOMMEND_USER_IN_FEEDS = 12;       // feeds流里的用户推荐区
        RECOMMEND_USER_IN_USER_HOME = 13;   // 个人主页的用户推荐区
        USER_HOME_FROM_RECOMMEND = 14;      // 个人主页(从动态流的推荐动态处、动态流的用户推荐区卡片、个人主页的用户推荐区卡片位置进入)
        USER_HOME_FROM_ROOM_USER_CARD = 15; // 个人主页(从房间卡片进入)
        USER_HOME_FROM_RANK = 16;           // 个人主页(从排行榜进入)
        USER_HOME_FROM_SEARCH_RESULT = 17;  // 个人主页(从搜索结果进入)
        KOL_CARD = 18;                      // KOL卡片页(不知道是什么鸡毛，问宝爷)
        CHANNEL_CONVERSATION_CARD = 19;     // 公屏卡片页
        MINI_GAME_SETTLE = 20;              // 小游戏结算页
        IM_CONVERSATION_FROM_ROOM_USER_CARD = 21;   // IM会话页(从房间卡片进入)
        CHANNEL_CONVERSATION_CARD_FOLLOW_BACK = 22; // 公屏卡片页回粉
        EXIT_ROOM_POPUP_WINDOW = 23;        // 退房弹窗
        CHANNEL_CONVERSATION_ONE_KEY = 24;  // 公屏一键关注
        INAPP_NOTIFICATION_FOLLOW_BACK = 25;    // 应用内通知回粉
        RECOMMEND_KOL = 26; // 通过广场通过推荐单个KOL卡片关注
        CHOSEN_TALENT = 27; // 通过精选达人页点击关注
        CHOSEN_SUB_PAGE = 28; // 广场_精选二级页（精选单列瀑布流）关注
        LIVE_END_PAGE = 29;  // 直播结束页
        LIVE_ROOM = 30; // 直播间
        FOLLOW_GUIDE_POPUP_WINDOW = 31; // 关注引导弹窗
        JOIN_FAN_PAGE = 32; // 送礼加入粉团页？
        EXIT_LIVE_ROOM = 33; // 退出直播间
        FIND_PARTNER_LIST = 34; // 找人玩推荐列表
        ASSIGN_PARTNER = 35; // 分配玩伴
        IM_CONVERSATION_FOLLOW_PROMPT = 36; // IM页提示关注

        // 根本没法翻译，预留至100寻求解脱
        SOURCE_37 = 37; // 公开身份后语音连麦页的关注
        SOURCE_38 = 38; // 公开身份后进入个人中心页产生的关注（点击头像或公屏上点击查看Ta主页）
        SOURCE_39 = 39; // 聊天结束页的关注
        SOURCE_40 = 40; //年度盛典节目半屏页关注（所有嘉宾和关注按钮的两个入口）
        SOURCE_41 = 41; //雷达约玩卡片新增关注
        SOURCE_42 = 42; //接歌抢唱房结算页关注
        SOURCE_43 = 43; //接歌抢唱房牛啊和我笑了引导关注
        SOURCE_44 = 44; //接歌抢唱房碰拳引导关注
        SOURCE_45 = 45; //接歌抢唱房帮唱引导关注
        SOURCE_46 = 46; //亲密关系绑定关系后自动关注
        SOURCE_47 = 47; //礼物红包倒计时弹窗关注金主
        SOURCE_48 = 48; //礼物红包结算弹窗关注金主
        SOURCE_49 = 49; //乐窝详情页关注主理人
        SOURCE_50 = 50; //从群组里点击进入个人页关注
        SOURCE_51 = 51; //ta喜欢我的歌列表或公屏引导关注
        SOURCE_52 = 52; //乐窝Live节目单浮层
        SOURCE_53 = 53; //乐窝房关注按钮
        SOURCE_54 = 54; //挂房听歌花花榜关注
        SOURCE_55 = 55; //爆灯击掌互动关注
        SOURCE_56 = 56; //语音直播房优质歌手榜关注
        SOURCE_57 = 57; //投票器自动关注
        SOURCE_58 = 58;
        SOURCE_59 = 59; //推荐流子tab头像和按钮关注
        SOURCE_60 = 60; //打卡后公屏引导关注
        SOURCE_61 = 61; //挂房听歌打卡榜关注
        SOURCE_62 = 62; //厂牌详情页关注
        SOURCE_63 = 63; //麦可乐队结算页【一键关注】
        SOURCE_64 = 64; //说唱专区帖子引导关注
        SOURCE_65 = 65; //重逢房间公屏引导关注
        SOURCE_66 = 66; //挂房听歌打气公屏的引导关注
        SOURCE_67 = 67; //合唱结束面板引导合唱者关注
        SOURCE_68 = 68; //打call公屏引导关注来源
        SOURCE_69 = 69; //麦可乐队结算单个用户关注
        SOURCE_70 = 70; //通过团战SDK自动关注房主
        SOURCE_71 = 71; //谜镜邀请好友自动关注
        SOURCE_72 = 72; //房间抽奖一键关注
        SOURCE_73 = 73; //谜镜分享房间自动关注
        SOURCE_74 = 74; //计数器榜单关注
        SOURCE_75 = 75; //chatGpt仿真账号和用户聊天过程关注
        SOURCE_76 = 76; //谜境通关结果弹窗关注
        SOURCE_77 = 77; //你行你唱情侣专区对局结束互关
        SOURCE_78 = 78; //音乐房间公屏引导关注演唱者
        SOURCE_79 = 79; //音乐房间公屏引导关注房主
        SOURCE_80 = 80; //音乐房间公屏回关消息回关

        SOURCE_81 = 81; //谜境首页-玩家社区帖子关注用户
        SOURCE_82 = 82; //谜境剧本详情页-玩家评价帖子关注用户
        SOURCE_83 = 83; //谜境添加好友搜索结果页-关注好友
        SOURCE_84 = 84; //游戏专区动态tab
        SOURCE_85 = 85; //广场推荐流-全部tab(第一个)关注
        SOURCE_86 = 86; //谜镜找搭子自动关注
        SOURCE_87 = 87; //谜境房间-剧本玩伴评价-关注NPC
        SOURCE_88 = 88; //搭子卡去个人中心页的关注
        SOURCE_89 = 89; //帖子搜索结果页关注
        SOURCE_90 = 90; //玩伴搜索结果页关注
        SOURCE_91 = 91; //搜索帖子点击头像进入用户个人中心页关注
        SOURCE_92 = 92; //公屏引导关注麦上用户消息-点击头像关注按钮
        SOURCE_93 = 93; //公屏引导关注麦上用户消息-点击一键关注
        SOURCE_94 = 94; //房间左上角点击房主头像关注点击
        SOURCE_95 = 95; //麦上用户关注按钮点击
        SOURCE_96 = 96; //新退房引导关注弹窗用户头像、和一键关注按钮关注
        SOURCE_97 = 97; //上麦引导关注弹窗关注按钮点击
        SOURCE_98 = 98; //进房破冰-引导互动弹窗关注
        SOURCE_99 = 99; //公屏播报关注消息“我也关注按钮”点击
        SOURCE_100 = 100; //设置在房提醒
        SOURCE_101 = 101; //进房破冰弹窗-左上角用户头像关注点击
        SOURCE_102 = 102; //IM聊天_ai红娘引导关注
        SOURCE_103 = 103; //闪光点客态详情页关注
        SOURCE_104 = 104; //社团讨论帖用户右侧关注
        SOURCE_105 = 105; //社团讨论帖-发帖人头像-弹窗-个人主页-关注
        SOURCE_106 = 106; //社团讨论帖-发帖人头像-弹窗-头像-个人主页-关注
        SOURCE_107 = 107; //社团讨论帖-评论用户头像-弹窗-个人主页-关注
        SOURCE_108 = 108; //社团讨论帖-评论用户头像-弹窗-头像-个人主页-关注
        SOURCE_109 = 109; //小游戏-游戏生涯卡片-关注
        SOURCE_110 = 110; //aigc玩法通讯聊天-关注好友
        SOURCE_111 = 111; //开黑pc-房间个人卡片页
        SOURCE_112 = 112; //开黑pc-房间在房列表
        SOURCE_113 = 113;
        SOURCE_114 = 114;
        SOURCE_115 = 115;
        SOURCE_116 = 116;
        SOURCE_117 = 117;
        SOURCE_118 = 118;
        SOURCE_119 = 119;
        SOURCE_120 = 120;
        SOURCE_121 = 121;
        SOURCE_122 = 122;
        SOURCE_123 = 123;
        SOURCE_124 = 124;
        SOURCE_125 = 125;
        SOURCE_126 = 126;
        SOURCE_127 = 127;
        SOURCE_128 = 128;
        SOURCE_129 = 129;
        SOURCE_130 = 130;
        SOURCE_131 = 131;
        SOURCE_132 = 132;
        SOURCE_133 = 133;
        SOURCE_134 = 134;
        SOURCE_135 = 135;
        SOURCE_136 = 136;
        SOURCE_137 = 137;
        SOURCE_138 = 138;
        SOURCE_139 = 139;
        SOURCE_140 = 140;
        SOURCE_141 = 141;
        SOURCE_142 = 142;
        SOURCE_143 = 143;
        SOURCE_144 = 144;
        SOURCE_145 = 145;
        SOURCE_146 = 146;
        SOURCE_147 = 147;
        SOURCE_148 = 148;
        SOURCE_149 = 149;
        SOURCE_150 = 150;
        SOURCE_151 = 151;
        SOURCE_152 = 152;
        SOURCE_153 = 153;
        SOURCE_154 = 154;
        SOURCE_155 = 155;
        SOURCE_156 = 156;
        SOURCE_157 = 157;
        SOURCE_158 = 158;
        SOURCE_159 = 159;
        SOURCE_160 = 160;
        SOURCE_161 = 161;
        SOURCE_162 = 162;
        SOURCE_163 = 163;
        SOURCE_164 = 164;
        SOURCE_165 = 165;
        SOURCE_166 = 166;
        SOURCE_167 = 167;
        SOURCE_168 = 168;
        SOURCE_169 = 169;
        SOURCE_170 = 170;
        SOURCE_171 = 171;
        SOURCE_172 = 172;
        SOURCE_173 = 173;
        SOURCE_174 = 174;
        SOURCE_175 = 175;
        SOURCE_176 = 176;
        SOURCE_177 = 177;
        SOURCE_178 = 178;
        SOURCE_179 = 179;
        SOURCE_180 = 180;
        SOURCE_181 = 181;
        SOURCE_182 = 182;
        SOURCE_183 = 183;
        SOURCE_184 = 184;
        SOURCE_185 = 185;
        SOURCE_186 = 186;
        SOURCE_187 = 187;
        SOURCE_188 = 188;
        SOURCE_189 = 189;
        SOURCE_190 = 190;
        SOURCE_191 = 191;
        SOURCE_192 = 192;
        SOURCE_193 = 193;
        SOURCE_194 = 194;
        SOURCE_195 = 195;
        SOURCE_196 = 196;
        SOURCE_197 = 197;
        SOURCE_198 = 198;
        SOURCE_199 = 199;
        SOURCE_200 = 200;
        SOURCE_201 = 201;
        SOURCE_202 = 202;
        SOURCE_203 = 203;
        SOURCE_204 = 204;
        SOURCE_205 = 205;
        SOURCE_206 = 206;
        SOURCE_207 = 207;
        SOURCE_208 = 208;
        SOURCE_209 = 209;
        SOURCE_210 = 210;
        SOURCE_211 = 211;
        SOURCE_212 = 212;
        SOURCE_213 = 213;
        SOURCE_214 = 214;
        SOURCE_215 = 215;
        SOURCE_216 = 216;
        SOURCE_217 = 217;
        SOURCE_218 = 218;
        SOURCE_219 = 219;
        SOURCE_220 = 220;
        SOURCE_221 = 221;
        SOURCE_222 = 222;
        SOURCE_223 = 223;
        SOURCE_224 = 224;
        SOURCE_225 = 225;
        SOURCE_226 = 226;
        SOURCE_227 = 227;
        SOURCE_228 = 228;
        SOURCE_229 = 229;
        SOURCE_230 = 230;
        SOURCE_231 = 231;
        SOURCE_232 = 232;
        SOURCE_233 = 233;
        SOURCE_234 = 234;
        SOURCE_235 = 235;
        SOURCE_236 = 236;
        SOURCE_237 = 237;
        SOURCE_238 = 238;
        SOURCE_239 = 239;
        SOURCE_240 = 240;
        SOURCE_241 = 241;
        SOURCE_242 = 242;
        SOURCE_243 = 243;
        SOURCE_244 = 244;
        SOURCE_245 = 245;
        SOURCE_246 = 246;
        SOURCE_247 = 247;
        SOURCE_248 = 248;
        SOURCE_249 = 249;
        SOURCE_250 = 250;
        SOURCE_251 = 251;
        SOURCE_252 = 252;
        SOURCE_253 = 253;
        SOURCE_254 = 254;

        // 用于一些活动功能等临时性功能点，前后端协商使用custom_source字段
        CUSTOM = 255;
    }

    Source source = 3;
    string custom_source = 4;
    bool is_robot = 5;
    map<string, string> datacenter_context_info = 6;       // 数据中心关心的上下文信息
    uint32 channel_id = 7;
    uint32 channel_type = 8;   //房间类型

}

message FriendshipOperationResp {
    ga.BaseResp base_resp = 1;
    uint32 uid = 2;
}

message FriendshipsLoadMore {
    uint32 last_user_id = 1; // deprecated
    uint64 last_friendship_create_at = 2; // deprecated
    string last_id = 3;
}

message GetUserFriendshipsReq {
    enum ListType {
        FOLLOWING = 0; // 关注列表
        FOLLOWER = 1; // 粉丝列表
    }

    ga.BaseReq base_req = 1;
    ListType list_type = 2;
    UserIdentifier user_identifier = 3;
    bool request_total_count = 4; // 如果该值为true, 会返回对应列表的总数
    FriendshipsLoadMore load_more = 10; // 首次拉取不传, 加载更多时原封不动地填入上一次GetNewsFeedsResp中的load_more字段
    uint32 count = 11;
}

message GetUserFriendshipsResp {
    ga.BaseResp base_resp = 1;
    repeated UserUGCInfo user_list = 2; // 用户列表
    uint32 total_count = 3; // 总数
    FriendshipsLoadMore load_more = 10; // 下一次加载更多时, 将load_more原封不动地填入请求的load_more中; 如果不包含此字段, 表示已经拉完了
}

message QuickCheckFriendshipReq {
    ga.BaseReq base_req = 1;
    UserIdentifier user_identifier = 2;
}

message QuickCheckFriendshipResp {
    ga.BaseResp base_resp = 1;
    bool following = 2; // 是否关注中
    bool follow_me = 3; // 是否关注了“我”
}

//    -------- Feeds --------

message NewFeedsLoadMore {
    string feeds_name = 1;

    message LastFeedInfo {
        uint64 time = 1;
        string id = 2;//userid与postid的结合类型
        double score = 3;
    }

    bytes last_feed_info = 2;
    uint32 last_page = 3;
    uint32 last_count = 4;

    bool following_feed_empty = 10; //关注流是否未空
    bool request_recommend_feed = 11;
}


message APPReportExposurePost{
    repeated string post_list = 2;
    string page_id = 3; /* @昭昭 提供 */
}

message GetNewsFeedsReq {
    // enum Type {
    //     UNSPECIFIC = 0;

    //     USER_TIMELINE = 1;          // 用户发布
    //     USER_FOLLOWING = 2;         // 用户关注
    //     USER_RECOMMENDATION = 3;    // 用户推荐

    //     TOPIC_TIMELINE = 11;        // 主题最新
    //     TOPIC_POP = 12;             // 主题热门
    // }

    message UserTimelineReq {
        UserIdentifier user_identifier = 1;
        bool zoom_in = 2;//是否是点击进入帖子后的查看
    }
    message UserFollowingReq {
    }
    message UserRecommendationReq {
    }
    message UserAttitudeReq {
    }
    message UserVisitReq {
    }
    message TopicTimelineReq {
        string topic_id = 1;
    }
    message TopicPopularReq {
        string topic_id = 1;
        repeated PostType post_type_filters = 2; // 只获取某些类型的帖子
        string post_id = 3; //  推荐组需要"点击来源的post id"做相似度推荐
    }
    message TabTopicsReq {
        string tab_name = 1;
        repeated string topic_ids = 2;
        uint32 tab_type = 3;
        bool is_user_location_auth_open = 4; // 用户定位授权是否开启
    }
    message UserFavouriteReq {
    }
    message UserHighContentFirstLevelRecommendReq {
        TabIDType tab_id = 1;
    }
    message UserHighContentSecondLevelRecommendReq {
        repeated uint32 tags = 1;
        string current_postid = 2;
        TabIDType tab_id = 3;
    }
    message UserRecommendationV2Req {

    }
    message UserAnchorReq {
    }
    message GeoTopicTimelineReq {
        string city_code = 1;
    }
    message GeoTopicPopularReq {
        string city_code = 1;
        string post_id = 2; //  推荐组需要"点击来源的post id"做相似度推荐
    }

    ga.BaseReq base_req = 1;
    oneof request_type {
        UserTimelineReq user_timeline_req = 2; //用户个人
        UserFollowingReq user_following_req = 3; //用户关注
        UserRecommendationReq user_recommendation_req = 4; //用户推荐
        TopicTimelineReq topic_timeline_req = 5; //主题
        TopicPopularReq topic_popular_req = 6; //主题热门
        UserAttitudeReq user_attitude_req = 7; //用户点赞记录
        UserVisitReq user_visit_req = 8; //用户浏览记录
        UserFavouriteReq user_favourite_req = 9; //用户收藏

        UserHighContentFirstLevelRecommendReq firstlevel_recommend_req = 10;
        UserHighContentSecondLevelRecommendReq secondlevel_recommend_req = 11;
        UserRecommendationV2Req user_recommendation_v2_req = 12; // 新推荐流
        GeoTopicTimelineReq geo_topic_timeline_req = 13; // 按地级市区分的流
        GeoTopicPopularReq geo_topic_popular_req = 14; // 按地级市区分的热门/推荐流
        UserAnchorReq user_anchor_req = 15; // 主播流
        TabTopicsReq tab_topics_req = 16; // tab话题流
    }

    NewFeedsLoadMore load_more = 20; // 首次拉取不传, 加载更多时原封不动地填入上一次GetNewsFeedsResp中的load_more字段
    uint32 count = 21; // 拉取数量

    ContentType content_type = 22; //默认返回富文本转义后的纯文本格式(旧版)，新版加了@ 传FORMATTED的值，用枚举方便以后扩展

    bool show_topic_stream_switch = 23;//显示主题流 仅测试用
    APPReportExposurePost exposure_post_list = 24; /* 客户端上报帖子实时曝光数据给推荐 */
    repeated string page_tag_id_list = 25 ; // 广场推荐页用户兴趣内容选择的标签ID

}

message GetNewsFeedsResp {
    ga.BaseResp base_resp = 1;
    repeated Feed feeds = 2; // feed列表, 数量可能会超过请求指定的count
    NewFeedsLoadMore load_more = 3; // 下一次加载更多时, 将load_more原封不动地填入请求的load_more中; 如果不包含此字段, 表示已经拉完了
    bool reset_to_first_page = 4; // 表示流可能已经失效或其他异常, 重置为第一页数据
    string group = 5; //feed 类型，后台用于区分feed类型，对应请求的request_type
    PostInfo top_post_for_topic_timeline = 6; // 讨论话题详情页置顶帖子
}

message SimpleMore {
    uint32 last_count = 1;
    uint32 limit = 2;
}

message AttentionPeople {
    uint32 tag = 1;
    uint32 uid = 2;
    string nickname = 3;
    bool attention = 4;
    uint32 gender = 5;
    string certify_intro = 6;
    string certify_style = 7;
    string account = 8;
    uint32 vip_level = 9; // vip等级
    bool is_year_member = 10; //是否年费会员
    string certify_special_effect_icon = 11;  // 大v图标动效
}

message AttentionPeopleReq {
    ga.BaseReq base_req = 1;
    uint32 tag = 2;
    uint32 limit = 3;
    SimpleMore simple_more = 4;
}

message AttentionPeopleRsp {
    ga.BaseResp base_resp = 1;
    repeated AttentionPeople attention_peoples = 2;
    SimpleMore simple_more = 3;
}


message RemoveFeedsReq {
    enum Group {
        INVALID = 0;
        USER_VISIT = 1; //浏览历史
        USER_FAVOURITE = 2; //用户收藏
    }
    ga.BaseReq base_req = 1;
    Group group = 2;                    //feed 类型
    repeated string id_list = 3;        //指定feed id删除
    bool clear_all = 4;                 //true清空列表

    repeated string post_id_list = 5;   //指定帖子id删除，跟'id_list'二选一
}

message RemoveFeedsResp {
    ga.BaseResp base_resp = 1;
}

//上报浏览记录
message ReportVisitRecordReq {
    ga.BaseReq base_req = 1;
    string post_id = 2;
}

message ReportVisitRecordResp {
    ga.BaseResp base_resp = 1;
}

//添加收藏
message AddFavouriteReq {
    ga.BaseReq base_req = 1;
    //收藏帖子，post_id,feed_id二选一
    string post_id = 2;
    string feed_id = 3;
}

message AddFavouriteResp {
    ga.BaseResp base_resp = 1;
}

//    -------- User Info --------

message GetUserUGCInfoReq {
    ga.BaseReq base_req = 1;
    UserIdentifier user_identifier = 2;
}

message GetUserUGCInfoResp {
    ga.BaseResp base_resp = 1;
    UserUGCInfo user_info = 2;
}

//    -------- Sync Messages --------

message InteractiveInfoUpdate {
    uint32 unread_new_comment_count = 1;  // 未读的评论数
    uint32 unread_new_follower_count = 2; // 未读的粉丝数
    uint32 unread_new_attitude_count = 3; // 未读的表态数
    uint64 following_feeds_update_at = 4; // 关注流的更新时间(unix timestamp)
    uint32 unread_new_at_me_count = 5;    // 未读的@我数
    uint32 unread_new_concern_count = 6;  // 未读的关心数
}

message FollowingListUpdate {
    enum UpdateType {
        INCREMENTAL = 0; // 增量更新, 客户端需要合并本地列表
        REPLACE = 1; // 全量替换, 客户端直接替换掉本地列表
    }

    UpdateType update_type = 1; // 更新类型
    repeated uint32 following_uid_list = 2; // DEPRECATED, use following_uid_to_account_map instead
    repeated uint32 unfollowed_uid_list = 3; // DEPRECATED, use unfollowed_uid_to_account_map instead
    map<uint32, string> following_uid_to_account_map = 4; // 关注中的uid->account
    map<uint32, string> unfollowed_uid_to_account_map = 5; // 取消关注的uid->account, 仅在update_type为INCREMENTAL时可能有值
}

message FollowBatchUserReq {
    ga.BaseReq base_req = 1;
    repeated uint32 uid = 2;
    enum Origin {
        DEFAULT = 0;        // 兼容旧版
    }
    Origin origin = 3;  // deprecated
    uint32 source = 4;  // 取值：FriendshipOperationReq.Source
    string custom_source = 5;
    map<string, string> datacenter_context_info = 6;    // 数据中心关心的上下文信息
}

message FollowBatchUserResp {
    ga.BaseResp base_resp = 1;
    repeated uint32 uid = 2;
}

message BatchUnFollowUserRequest {
    ga.BaseReq base_req = 1;
    // 被取关的用户 uid 列表
    repeated uint32 unfollow_uid_list = 3;
}

message BatchUnFollowUserResponse {
    ga.BaseResp base_resp = 1;
    // 成功取关的用户 uid 列表
    repeated uint32 unfollowed_uid_list = 2;
}

message RecommendUserOption {
    uint32 user_id = 1;
}
message FilterOption {
    repeated uint32 filter_uid = 1;
}

message RecommendAttentionUserReq {
    ga.BaseReq base_req = 1;
    RecommendUserOption condition = 2; // 推荐用户条件
    FilterOption filter = 3; // 过滤器
}

message RecommendAttentionUserResp {
    ga.BaseResp base_resp = 1;
    repeated UserUGCInfo users = 2;
}

// ----------------- audio post -----------------
message AudioPostScriptTab {
    int32 id = 1;
    string title = 2;
}

message AudioPostScriptTabsReq {
    ga.BaseReq base_req = 1;
}

message AudioPostScriptTabsResp {
    repeated AudioPostScriptTab tabs = 1;
    ga.BaseResp base_resp = 2;
}

message AudioPostScript {
    int32 id = 1;
    repeated string sections = 2;
    int32 tab_id = 3;
    string tab_title = 4;
    string title = 5;
}

message Rule {
    int32 offset = 1;
    int32 limit = 2;
}

message AudioPostScriptsReq {
    repeated int32 tab_ids = 1;
    // int32 offset = 2;
    // int32 limit = 3;
    ga.BaseReq base_req = 4;
    Rule rule = 6;
}

message AudioPostScriptsResp {
    repeated AudioPostScript scripts = 1;
    ga.BaseResp base_resp = 2;
    bool more = 3;
    Rule rule = 4;
}

message RandomAudioPostScriptsReq {
    int32 limit = 1;
    ga.BaseReq base_req = 2;
}

message RandomAudioPostScriptsResp {
    repeated AudioPostScript scripts = 1;
    ga.BaseResp base_resp = 2;
}

message AudioPostImage {
    string url = 1;
    int32 id = 2;
    string thn_url = 3;
}

message RandomAudioPostImagesReq {
    int32 limit = 1;
    ga.BaseReq base_req = 2;
}

message RandomAudioPostImagesResp {
    repeated AudioPostImage images = 1;
    ga.BaseResp base_resp = 2;
}

message AudioPostImagesReq {
    ga.BaseReq base_req = 1;
    int32 offset = 2;
    int32 limit = 3;
    Rule rule = 4;
}

message AudioPostImagesResp {
    repeated AudioPostImage images = 1;
    ga.BaseResp base_resp = 2;
    bool more = 3;
    Rule rule = 4;
}

message AudioPostMusic {
    string url = 1;
    string name = 2;
    string singer = 3;
    int32 id = 4;
    string abstract_value = 5;
}

message RandomAudioPostMusicsReq {
    int32 limit = 1;
    ga.BaseReq base_req = 2;
}

message RandomAudioPostMusicsResp {
    repeated AudioPostMusic musics = 1;
    ga.BaseResp base_resp = 2;
}

message AudioPostMusicsReq {
    repeated int32 tab_ids = 1;
    int32 offset = 2;
    int32 limit = 3;
    ga.BaseReq base_req = 4;
    Rule rule = 5;
}

message AudioPostMusicsResp {
    repeated AudioPostMusic musics = 1;
    ga.BaseResp base_resp = 2;
    bool more = 3;
    Rule rule = 4;
}

message AudioPostMusicTab {
    int32 id = 1;
    string title = 2;
}

message AudioPostMusicTabsReq {
    ga.BaseReq base_req = 1;
}

message AudioPostMusicTabsResp {
    repeated AudioPostMusicTab tabs = 1;
    ga.BaseResp base_resp = 2;
}

message XunfeiSignatureReq {
    ga.BaseReq base_req = 1;
}

message XunfeiSignatureResp {
    string auth_string = 1;
    ga.BaseResp base_resp = 2;
}

message AudioPostScriptsForMobileReq {
    repeated int32 tab_ids = 1;
    ga.BaseReq base_req = 2;
    Rule rule = 3;
    bool sliding = 4;
}

message AudioPostScriptsForMobileResp {
    repeated AudioPostScript scripts = 1;
    ga.BaseResp base_resp = 2;
    bool more = 3;
    Rule rule = 4;
}

message DelRandListReq {
    ga.BaseReq base_req = 1;
}

message DelRandListResp {
    ga.BaseResp base_resp = 1;
}

message AudioPostScriptTabsV2Req {
    ga.BaseReq base_req = 1;
}

message AudioPostScriptTabsV2Resp {
    ga.BaseResp base_resp = 1;
    repeated AudioPostScriptTab tabs = 2;
}

message AudioPostScriptsV2Req {
    ga.BaseReq base_req = 1;
    Rule rule = 2;
    repeated int32 tab_ids = 3;
}

message AudioPostScriptsV2Resp {
    ga.BaseResp base_resp = 1;
    Rule rule = 2;
    repeated AudioPostScript scripts = 3;
    bool more = 4;
}

message RandomAudioPostScriptsV2Req {
    ga.BaseReq base_req = 1;
    int32 limit = 2;
}

message RandomAudioPostScriptsV2Resp {
    ga.BaseResp base_resp = 1;
    repeated AudioPostScript scripts = 2;
}

message RandomAudioPostImagesV2Req {
    ga.BaseReq base_req = 1;
    int32 limit = 2;
}

message RandomAudioPostImagesV2Resp {
    ga.BaseResp base_resp = 1;
    repeated AudioPostImage images = 2;
}

message AudioPostImagesV2Req {
    ga.BaseReq base_req = 1;
    int32 offset = 2;
    int32 limit = 3;
    Rule rule = 4;
}

message AudioPostImagesV2Resp {
    ga.BaseResp base_resp = 1;
    repeated AudioPostImage images = 2;
    bool more = 3;
    Rule rule = 4;
}

message RandomAudioPostMusicsV2Req {
    ga.BaseReq base_req = 1;
    int32 limit = 2;
}

message RandomAudioPostMusicsV2Resp {
    ga.BaseResp base_resp = 1;
    repeated AudioPostMusic musics = 2;
}

message AudioPostMusicsV2Req {
    ga.BaseReq base_req = 1;
    repeated int32 tab_ids = 2;
    int32 offset = 3;
    int32 limit = 4;
    Rule rule = 5;
}

message AudioPostMusicsV2Resp {
    ga.BaseResp base_resp = 1;
    repeated AudioPostMusic musics = 2;
    bool more = 3;
    Rule rule = 4;
}

message AudioPostMusicTabsV2Req {
    ga.BaseReq base_req = 1;
}

message AudioPostMusicTabsV2Resp {
    ga.BaseResp base_resp = 1;
    repeated AudioPostMusicTab tabs = 2;
}

message XunfeiSignatureV2Req {
    ga.BaseReq base_req = 1;
}

message XunfeiSignatureV2Resp {
    ga.BaseResp base_resp = 1;
    string auth_string = 2;
}

message AudioPostScriptsForMobileV2Req {
    ga.BaseReq base_req = 1;
    repeated int32 tab_ids = 2;
    Rule rule = 3;
    bool sliding = 4;
}

message AudioPostScriptsForMobileV2Resp {
    ga.BaseResp base_resp = 1;
    repeated AudioPostScript scripts = 2;
    bool more = 3;
    Rule rule = 4;
}

message DelRandListV2Req {
    ga.BaseReq base_req = 1;
}

message DelRandListV2Resp {
    ga.BaseResp base_resp = 1;
}

message NewFollowerMessage {
    uint32 uid = 1;
    string account = 2;
    string nickname = 3;
    bool   bi_followed = 4;     // 当前是否达成双向关注
    uint32 source = 5;          // FriendshipOperationReq.Source
    uint32 gender = 6;
}

message IsKolExistReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
}

message IsKolExistRsp {
    ga.BaseResp base_resp = 1;
    bool exist = 2;
}

enum TabIDType{
    KolId = 0;
    NormalFoundId = 1;
    ExtendId = 2;
}

message HideHighContentReq {
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
}
message TabExtendInfo{
    string tab_jump_url = 1; /* 跳转url */
    string bg_pic_url = 2; /* 背景图 */
    enum TabExtendThemeType{
        Black = 0;
        White = 1;
    }
    uint32 theme = 3; /* 通知图标 主题 TabExtendThemeType */
}

message HideHighContentRsp {
    ga.BaseResp base_resp = 1;
    bool hide = 2;
    TabIDType tab_id = 3;
    string tab_desc = 4;
    TabExtendInfo tab_extend = 5; /*   ExtendId = 2; */
}

message MarkContentStickyReq {
    ga.BaseReq base_req = 1;
    string post_id = 2;
    string comment_id = 3;
    StickyStatus new_status = 4;
}

message MarkContentStickyResp {
    ga.BaseResp base_resp = 1;
}

message UpdatePostPrivacyPolicyReq {
    ga.BaseReq base_req = 1;
    string post_id = 2;
    PostPrivacyPolicy policy = 3;
}

message UpdatePostPrivacyPolicyResp {
    ga.BaseResp base_resp = 1;
    PostInfo post = 2;
}

message GetShowUserFollowReq{
    ga.BaseReq base_req = 1;
}
message GetShowUserFollowResp{
    ga.BaseResp base_resp = 1;
    bool show = 2;
}

message SetShowUserFollowReq{
    ga.BaseReq base_req = 1;
    bool show = 2;
}
message SetShowUserFollowResp{
    ga.BaseResp base_resp = 1;
}

// ---- 催更啊, 关心你啊, 催个🐔 ----
message ConcernReq {
    ga.BaseReq base_req = 1;
    uint32 target_uid = 2;
}

message ConcernResp {
    ga.BaseResp base_resp = 1;
}
//


message GetPublisherTopicByUidReq{
    ga.BaseReq base_req = 1;
    uint32 uid = 2;
}

message GetPublisherTopicByUidResp{
    ga.BaseResp base_resp = 1;
    TopicInfo topic_info = 2;
}

// IM页获取最新帖子
message GetNewestPostsReq{
    message LastPost{
        string account = 1;
        string post_id = 2; // 客户端保存的最新帖子id
    }
    ga.BaseReq base_req = 1;
    repeated LastPost last_posts = 2;
}

message GetNewestPostsResp{
    message UserPost{
        string account = 1;
        PostInfo post_info = 2;
    }
    ga.BaseResp base_resp = 1;
    repeated UserPost user_posts = 2;
}

//目前是内部接口 还没有给客户端使用 通过帖子id获取流对象
message GetFeedsByPostIDsReq {
   ga.BaseReq base_req = 1;
   repeated string  post_ids = 2;
   ContentType contentType =3;
   uint32 uid = 4;
   bool needExactInfo = 5;
}


//通过account获取地理信息接口
message GetUserGeoInfoReq {
    ga.BaseReq base_req = 1;
    string account = 2;
}

//通过account获取地理信息接口
message GetUserGeoInfoRsp {
    //
    ga.BaseResp base_resp = 1;
    string city_name = 2;
}

/*推荐流插入话题*/
enum TopicCatalog{ /*分类话题*/
    NormalCatalog = 0; /*旧版 中间 5个话题*/
    HotCatalog = 1; /*热门话题*/
    MultiCatalog = 2; /*包含热门话题、其他分类*/
}
message TopicInRecommendFeedReq{
    ga.BaseReq base_req = 1;
    uint32 topic_catalog = 2;  /*TopicCatalog*/
}

message RecommendTopics{
    string topic_name = 1;
    repeated string avatar_list = 2;
    uint32 view_cnt = 3;
    string bg_pic_url = 4;
    string subscript_url = 5; /*角标*/
    TopicInfo topic_info = 6;
}
message CatalogNameAndTopics{
    string catalog_name = 1; /*分类名称*/
   repeated RecommendTopics topics = 2;
}
message TopicInRecommendFeedResp{
    ga.BaseResp base_resp = 1;
    repeated RecommendTopics topics = 2; /*热门话题*/
    repeated CatalogNameAndTopics catalog_topics = 3; /*分类下的话题*/
}

message GetUserFollowPostReq {
    ga.BaseReq base_req = 1;
}

message GetUserFollowPostRsp {
    ga.BaseResp base_resp = 1;
    bool is_follow_update = 2;
    string post_id = 3;
    int64 post_time = 4;
    string account = 5; // 账号
    string alias = 6; // 数字账号
}

message ReportUserFollowPostReq {
    ga.BaseReq base_req = 1;
    int64 post_time = 2;
    string post_id = 3;
}

message ReportUserFollowPostRsp {
    ga.BaseResp base_resp = 1;
}

/* 话题详情页广告位 */
message GetTopicAdsReq{
    ga.BaseReq base_req = 1;
    string topic_id = 2;
}
message TopicAdInfo{
    string pic_url = 1; /* 入口图 */
    string link = 2; /* 活动链接 */
}
message GetTopicAdsResp{
    ga.BaseResp base_resp = 1;
    repeated TopicAdInfo infos = 2;
}

message MoodConfig{
    string mood_id = 1;
    string name = 2;
    MoodType mood_type = 3;
    string icon = 4;
    uint32 index = 5;   //排序，空时表示下架
    repeated string post_toasts = 6; //发贴后的返回语
    string topic_id = 7;
    repeated string text_me = 8;
}

message GetMoodConfigReq{
    ga.BaseReq base_req = 1;
}

message GetMoodConfigRsp{
    ga.BaseResp base_resp = 1;
    repeated MoodConfig mood_configs = 2;
}

enum MoodType {
    AllMoodType = 0; //全部心情
    Positive = 1; //积极类心情
    Negative = 2; //消极类心情
}

message GetMoonDetailReq{
    ga.BaseReq base_req = 1;
    string mood_id = 2;
}

message GetMoonDetailRsp{
    ga.BaseResp base_resp = 1;
    string mood_name = 2;
    MoodType mood_type = 3;
    string mood_desc = 4;
    uint32 post_cnt = 5;
    uint32 view_cnt = 6;
    string background_icon = 7;
    string topic_id = 8;
    string negative_text = 9;
    repeated string negative_im_text = 10;
    string small_url = 11; /*缩略图*/
}

message GetTabConfigReq{
    ga.BaseReq base_req = 1;
}

message TabInfo {
    string tab_name = 1;
    repeated string topic_ids = 2;
    repeated SimpleTopicInfo SimpleTopicInfoList = 3;
    uint32 tab_type = 4;// tab类型，0默认类型，1同城类型
    string city_name = 5;// 城市
}

message SimpleTopicInfo {
    string id = 1;
    string name = 2;
}

message GetTabConfigRsp{
    ga.BaseResp base_resp = 1;
    bool close_anchor_tab = 2; //监管开关，以前是true时不显示所有推荐子tab，现在改成true时仅不显示声控tab
    repeated TabInfo tab_info = 3; //推荐子tab列表
    bool close_kol_tab = 4; //监管开关，true时不显示整个精选流
    bool close_rcmd_tab = 5; //监管开关，true时不显示整个推荐流
    bool close_rcmd_sub_tab = 6; //监管开关，true时不显示推荐流子tab，只显示推荐流
    bool close_mood = 7;    //监管开关，true时发帖不显示心情
    bool close_topic = 8;   //监管开关，true时发帖不显示话题
    //推荐流开关优先级：close_rcmd_tab > close_rcmd_sub_tab > close_anchor_tab
    bool close_rcmd_all_tab = 9; //监管开关，true时不显示推荐流下名为“全部”的子tab
    bool close_interest_communicate_tab = 10; //兴趣交流TAB开关，true时不显示兴趣交流tab
}

message ReportUnrelatedTopicReq {
    ga.BaseReq base_req = 1;
    string post_id = 2;
    string topic_id = 3;
}

message ReportUnrelatedTopicRsp {
    enum ReportResult {
        Success = 0; //成功上报
        Duplicate = 1; //重复上报
    }
    ga.BaseResp base_resp = 1;
    ReportResult report_result = 2;
}

message VotePostReq {
    ga.BaseReq base_req = 1;

    // 帖子id
    string post_id = 2;
    // 投票选项id
    string option_id = 3;
}

message VotePostResp {
    ga.BaseResp base_resp = 1;
    VoteInfo vote = 2;
}

message GetGameEntranceReq {
    //uid、product在context中获取
    ga.BaseReq base_req = 1;
    uint32 page_owner_uid = 2; //当前页面的uid
}

message GetGameEntranceResp {
    enum PlayStatus {
        UNKNOWN = 0;
        PLAYED = 1;
        NOT_PLAYED = 2;
    }
    enum OnlineStatus {
        UNDEFINE = 0;
        ONLINE = 1;
        OFFLINE = 2;
    }
    message GameEntrance {
        uint32 game_id = 1;
        string game_name = 2;
        string icon_img_url = 3;
        string hint_img_url = 4;
        string jump_url = 5;
        //以下字段在客态才用到
        PlayStatus page_owner_play_status = 6; //是否玩过游戏
        OnlineStatus page_owner_online_status = 7; //是否在线
        string background_img_url = 8;
        string im_img_url = 9;
    }
    ga.BaseResp base_resp = 1;
    GameEntrance game_entrance = 2;
}


//发布按钮引导

enum InformationSources{
    Information_Sources_None = 0;
    Information_Sources_Personal_Page = 1;    //个人页
    Information_Sources_Recommend_Page = 2;   //推荐页
    Information_Sources_Follow_Page = 3;      //关注页
}

message GetPostButtonGuideReq{
    ga.BaseReq base_req = 1;
    uint32 information_source=2;  //弃用
    InformationSources info_source=3;
}
message GetPostButtonGuideResp{
    ga.BaseResp base_resp = 1;
    string picture=2;      //引导图片
    string topic_text=3;    //话题文案
    string reward_text=4;   //奖励文案
    string topic_id=5;     //话题ID
    string topic_name=6;   //话题名
}


message UgcPostAttitudeNotify{
  UserUGCInfo user_ugc_info = 1;   //用户信息
  string content=2;     //
}

// 帖子内容反馈
message GetUgcContentFeedbackOptionsReq {
    ga.BaseReq base_req = 1;
}

message GetUgcContentFeedbackOptionsResp {
    ga.BaseResp base_resp = 1;
    repeated string options = 2;
}

enum UgcContentFeedbackType {
    UGC_CONTENT_FEEDBACK_TYPE_UNSPECIFIED = 0;
    // 屏蔽帖子
    UGC_CONTENT_FEEDBACK_TYPE_SHIELD_POST = 1;
    // 屏蔽用户
    UGC_CONTENT_FEEDBACK_TYPE_SHIELD_USER = 2;
    // 减少推荐内容反馈
    UGC_CONTENT_FEEDBACK_TYPE_REDUCE_RCMD_CONTENT = 3;
}

message ReportUgcContentFeedbackReq {
    ga.BaseReq base_req = 1;
    uint32 feedback_type = 2; // see enum UgcContentFeedbackType
    string post_id = 3; // 屏蔽帖子id,feedback_type=1生效
    uint32 uid = 4; // 屏蔽用户id,feedback_type=2生效
    string content_feedback = 5; // 内容反馈,feedback_type=3生效
}

message ReportUgcContentFeedbackResp {
    ga.BaseResp base_resp = 1;
}