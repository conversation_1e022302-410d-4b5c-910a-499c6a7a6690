package mgr

import (
	"context"
	"errors"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"golang.52tt.com/clients/mocks/appconfig"

	appconfigPb "golang.52tt.com/protocol/services/appconfig"
)

func TestBreakingNewsMgr_RefreshHuntMonsterConf(t *testing.T) {
	controller := gomock.NewController(t)
	defer controller.Finish()
	appconfigCli := appconfig.NewMockIClient(controller)

	m := NewBreakingNewsMgr(nil, nil, nil, nil, nil, appconfigCli, nil, nil, nil, nil, nil, nil)
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name     string
		m        *BreakingNewsMgr
		args     args
		initFunc func()
	}{
		{
			name: "正常",
			m:    m,
			args: args{
				ctx: context.Background(),
			},
			initFunc: func() {
				ts := time.Now().Unix()
				appconfigCli.EXPECT().GetHuntMonsterConfig(context.Background(), uint32(0)).Return(
					[]appconfigPb.FloatLayerEntry{
						appconfigPb.FloatLayerEntry{
							Type:      uint32(appconfigPb.FloatLayerType_CHANNEL_HUNT_MONSTER_V2),
							BeginTime: uint64(ts - 100),
							EndTime:   uint64(ts + 100),
							RushInfo: &appconfigPb.RushInfo{
								RushWaitTs: 30,
							},
						},
					}, nil,
				)
			},
		},
		{
			name: "请求下游失败",
			m:    m,
			args: args{
				ctx: context.Background(),
			},
			initFunc: func() {
				appconfigCli.EXPECT().GetHuntMonsterConfig(context.Background(), uint32(0)).Return([]appconfigPb.FloatLayerEntry{}, errors.New("123"))
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.initFunc != nil {
				tt.initFunc()
			}
			tt.m.RefreshHuntMonsterConf(tt.args.ctx)
		})
	}
}

func TestBreakingNewsMgr_GetHuntMonsterConf(t *testing.T) {
	huntMonsterConf = &HuntMonsterConf{}
	m := NewBreakingNewsMgr(nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	tests := []struct {
		name string
		m    *BreakingNewsMgr
		want *HuntMonsterConf
	}{
		{
			name: "正常",
			m:    m,
			want: huntMonsterConf,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.m.GetHuntMonsterConf(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BreakingNewsMgr.GetHuntMonsterConf() = %v, want %v", got, tt.want)
			}
		})
	}
}
