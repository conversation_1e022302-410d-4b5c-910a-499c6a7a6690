##

mock: mock-cache mock-store mock-conf
	echo "mock gen done"

mock-cache:
	echo "mock gen cache"
	cd ./cache && quicksilver-cli test interface && del cache_api.go && ren _api.go cache_api.go && mockgen -destination=../mock/mock_cache.go -package=mock . ICache

mock-store:
	echo "mock gen store"
	cd ./store && quicksilver-cli test interface && del store_api.go && ren _api.go store_api.go && mockgen -destination=../mock/mock_db.go -package=mock . IStore

mock-cache:
	echo "mock gen cache"
	cd ./cache && quicksilver-cli test interface && del cache_api.go && ren _api.go cache_api.go && mockgen -destination=../mock/mock_cache.go -package=mock . ICache

mock-input:
	echo "mock gen input"
	cd ./modules/parallel_check && quicksilver-cli test interface && del parallel_check_api.go && ren _api.go parallel_check_api.go && mockgen -destination=../../mock/mock_parallel_check.go -package=mock . IParallelChecker
