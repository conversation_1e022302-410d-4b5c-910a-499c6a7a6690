/*rename table smash_egg.smash_egg_consume_record_00 to smash_egg.smash_egg_consume_record_old_00 ;
rename table smash_egg.smash_egg_consume_record_01 to smash_egg.smash_egg_consume_record_old_01 ;
rename table smash_egg.smash_egg_consume_record_02 to smash_egg.smash_egg_consume_record_old_02 ;
rename table smash_egg.smash_egg_consume_record_03 to smash_egg.smash_egg_consume_record_old_03 ;
rename table smash_egg.smash_egg_consume_record_04 to smash_egg.smash_egg_consume_record_old_04 ;
rename table smash_egg.smash_egg_consume_record_05 to smash_egg.smash_egg_consume_record_old_05 ;
rename table smash_egg.smash_egg_consume_record_06 to smash_egg.smash_egg_consume_record_old_06 ;
rename table smash_egg.smash_egg_consume_record_07 to smash_egg.smash_egg_consume_record_old_07 ;
rename table smash_egg.smash_egg_consume_record_08 to smash_egg.smash_egg_consume_record_old_08 ;
rename table smash_egg.smash_egg_consume_record_09 to smash_egg.smash_egg_consume_record_old_09 ;
rename table smash_egg.smash_egg_consume_record_10 to smash_egg.smash_egg_consume_record_old_10 ;
rename table smash_egg.smash_egg_consume_record_11 to smash_egg.smash_egg_consume_record_old_11 ;
rename table smash_egg.smash_egg_consume_record_12 to smash_egg.smash_egg_consume_record_old_12 ;
rename table smash_egg.smash_egg_consume_record_13 to smash_egg.smash_egg_consume_record_old_13 ;
rename table smash_egg.smash_egg_consume_record_14 to smash_egg.smash_egg_consume_record_old_14 ;
rename table smash_egg.smash_egg_consume_record_15 to smash_egg.smash_egg_consume_record_old_15 ;
rename table smash_egg.smash_egg_consume_record_16 to smash_egg.smash_egg_consume_record_old_16 ;
rename table smash_egg.smash_egg_consume_record_17 to smash_egg.smash_egg_consume_record_old_17 ;
rename table smash_egg.smash_egg_consume_record_18 to smash_egg.smash_egg_consume_record_old_18 ;
rename table smash_egg.smash_egg_consume_record_19 to smash_egg.smash_egg_consume_record_old_19 ;
rename table smash_egg.smash_egg_consume_record_20 to smash_egg.smash_egg_consume_record_old_20 ;
rename table smash_egg.smash_egg_consume_record_21 to smash_egg.smash_egg_consume_record_old_21 ;
rename table smash_egg.smash_egg_consume_record_22 to smash_egg.smash_egg_consume_record_old_22 ;
rename table smash_egg.smash_egg_consume_record_23 to smash_egg.smash_egg_consume_record_old_23 ;
rename table smash_egg.smash_egg_consume_record_24 to smash_egg.smash_egg_consume_record_old_24 ;
rename table smash_egg.smash_egg_consume_record_25 to smash_egg.smash_egg_consume_record_old_25 ;
rename table smash_egg.smash_egg_consume_record_26 to smash_egg.smash_egg_consume_record_old_26 ;
rename table smash_egg.smash_egg_consume_record_27 to smash_egg.smash_egg_consume_record_old_27 ;
rename table smash_egg.smash_egg_consume_record_28 to smash_egg.smash_egg_consume_record_old_28 ;
rename table smash_egg.smash_egg_consume_record_29 to smash_egg.smash_egg_consume_record_old_29 ;
rename table smash_egg.smash_egg_consume_record_30 to smash_egg.smash_egg_consume_record_old_30 ;
rename table smash_egg.smash_egg_consume_record_31 to smash_egg.smash_egg_consume_record_old_31 ;
rename table smash_egg.smash_egg_consume_record_32 to smash_egg.smash_egg_consume_record_old_32 ;
rename table smash_egg.smash_egg_consume_record_33 to smash_egg.smash_egg_consume_record_old_33 ;
rename table smash_egg.smash_egg_consume_record_34 to smash_egg.smash_egg_consume_record_old_34 ;
rename table smash_egg.smash_egg_consume_record_35 to smash_egg.smash_egg_consume_record_old_35 ;
rename table smash_egg.smash_egg_consume_record_36 to smash_egg.smash_egg_consume_record_old_36 ;
rename table smash_egg.smash_egg_consume_record_37 to smash_egg.smash_egg_consume_record_old_37 ;
rename table smash_egg.smash_egg_consume_record_38 to smash_egg.smash_egg_consume_record_old_38 ;
rename table smash_egg.smash_egg_consume_record_39 to smash_egg.smash_egg_consume_record_old_39 ;
rename table smash_egg.smash_egg_consume_record_40 to smash_egg.smash_egg_consume_record_old_40 ;
rename table smash_egg.smash_egg_consume_record_41 to smash_egg.smash_egg_consume_record_old_41 ;
rename table smash_egg.smash_egg_consume_record_42 to smash_egg.smash_egg_consume_record_old_42 ;
rename table smash_egg.smash_egg_consume_record_43 to smash_egg.smash_egg_consume_record_old_43 ;
rename table smash_egg.smash_egg_consume_record_44 to smash_egg.smash_egg_consume_record_old_44 ;
rename table smash_egg.smash_egg_consume_record_45 to smash_egg.smash_egg_consume_record_old_45 ;
rename table smash_egg.smash_egg_consume_record_46 to smash_egg.smash_egg_consume_record_old_46 ;
rename table smash_egg.smash_egg_consume_record_47 to smash_egg.smash_egg_consume_record_old_47 ;
rename table smash_egg.smash_egg_consume_record_48 to smash_egg.smash_egg_consume_record_old_48 ;
rename table smash_egg.smash_egg_consume_record_49 to smash_egg.smash_egg_consume_record_old_49 ;
rename table smash_egg.smash_egg_consume_record_50 to smash_egg.smash_egg_consume_record_old_50 ;
rename table smash_egg.smash_egg_consume_record_51 to smash_egg.smash_egg_consume_record_old_51 ;
rename table smash_egg.smash_egg_consume_record_52 to smash_egg.smash_egg_consume_record_old_52 ;
rename table smash_egg.smash_egg_consume_record_53 to smash_egg.smash_egg_consume_record_old_53 ;
rename table smash_egg.smash_egg_consume_record_54 to smash_egg.smash_egg_consume_record_old_54 ;
rename table smash_egg.smash_egg_consume_record_55 to smash_egg.smash_egg_consume_record_old_55 ;
rename table smash_egg.smash_egg_consume_record_56 to smash_egg.smash_egg_consume_record_old_56 ;
rename table smash_egg.smash_egg_consume_record_57 to smash_egg.smash_egg_consume_record_old_57 ;
rename table smash_egg.smash_egg_consume_record_58 to smash_egg.smash_egg_consume_record_old_58 ;
rename table smash_egg.smash_egg_consume_record_59 to smash_egg.smash_egg_consume_record_old_59 ;
rename table smash_egg.smash_egg_consume_record_60 to smash_egg.smash_egg_consume_record_old_60 ;
rename table smash_egg.smash_egg_consume_record_61 to smash_egg.smash_egg_consume_record_old_61 ;
rename table smash_egg.smash_egg_consume_record_62 to smash_egg.smash_egg_consume_record_old_62 ;
rename table smash_egg.smash_egg_consume_record_63 to smash_egg.smash_egg_consume_record_old_63 ;
rename table smash_egg.smash_egg_consume_record_64 to smash_egg.smash_egg_consume_record_old_64 ;
rename table smash_egg.smash_egg_consume_record_65 to smash_egg.smash_egg_consume_record_old_65 ;
rename table smash_egg.smash_egg_consume_record_66 to smash_egg.smash_egg_consume_record_old_66 ;
rename table smash_egg.smash_egg_consume_record_67 to smash_egg.smash_egg_consume_record_old_67 ;
rename table smash_egg.smash_egg_consume_record_68 to smash_egg.smash_egg_consume_record_old_68 ;
rename table smash_egg.smash_egg_consume_record_69 to smash_egg.smash_egg_consume_record_old_69 ;
rename table smash_egg.smash_egg_consume_record_70 to smash_egg.smash_egg_consume_record_old_70 ;
rename table smash_egg.smash_egg_consume_record_71 to smash_egg.smash_egg_consume_record_old_71 ;
rename table smash_egg.smash_egg_consume_record_72 to smash_egg.smash_egg_consume_record_old_72 ;
rename table smash_egg.smash_egg_consume_record_73 to smash_egg.smash_egg_consume_record_old_73 ;
rename table smash_egg.smash_egg_consume_record_74 to smash_egg.smash_egg_consume_record_old_74 ;
rename table smash_egg.smash_egg_consume_record_75 to smash_egg.smash_egg_consume_record_old_75 ;
rename table smash_egg.smash_egg_consume_record_76 to smash_egg.smash_egg_consume_record_old_76 ;
rename table smash_egg.smash_egg_consume_record_77 to smash_egg.smash_egg_consume_record_old_77 ;
rename table smash_egg.smash_egg_consume_record_78 to smash_egg.smash_egg_consume_record_old_78 ;
rename table smash_egg.smash_egg_consume_record_79 to smash_egg.smash_egg_consume_record_old_79 ;
rename table smash_egg.smash_egg_consume_record_80 to smash_egg.smash_egg_consume_record_old_80 ;
rename table smash_egg.smash_egg_consume_record_81 to smash_egg.smash_egg_consume_record_old_81 ;
rename table smash_egg.smash_egg_consume_record_82 to smash_egg.smash_egg_consume_record_old_82 ;
rename table smash_egg.smash_egg_consume_record_83 to smash_egg.smash_egg_consume_record_old_83 ;
rename table smash_egg.smash_egg_consume_record_84 to smash_egg.smash_egg_consume_record_old_84 ;
rename table smash_egg.smash_egg_consume_record_85 to smash_egg.smash_egg_consume_record_old_85 ;
rename table smash_egg.smash_egg_consume_record_86 to smash_egg.smash_egg_consume_record_old_86 ;
rename table smash_egg.smash_egg_consume_record_87 to smash_egg.smash_egg_consume_record_old_87 ;
rename table smash_egg.smash_egg_consume_record_88 to smash_egg.smash_egg_consume_record_old_88 ;
rename table smash_egg.smash_egg_consume_record_89 to smash_egg.smash_egg_consume_record_old_89 ;
rename table smash_egg.smash_egg_consume_record_90 to smash_egg.smash_egg_consume_record_old_90 ;
rename table smash_egg.smash_egg_consume_record_91 to smash_egg.smash_egg_consume_record_old_91 ;
rename table smash_egg.smash_egg_consume_record_92 to smash_egg.smash_egg_consume_record_old_92 ;
rename table smash_egg.smash_egg_consume_record_93 to smash_egg.smash_egg_consume_record_old_93 ;
rename table smash_egg.smash_egg_consume_record_94 to smash_egg.smash_egg_consume_record_old_94 ;
rename table smash_egg.smash_egg_consume_record_95 to smash_egg.smash_egg_consume_record_old_95 ;
rename table smash_egg.smash_egg_consume_record_96 to smash_egg.smash_egg_consume_record_old_96 ;
rename table smash_egg.smash_egg_consume_record_97 to smash_egg.smash_egg_consume_record_old_97 ;
rename table smash_egg.smash_egg_consume_record_98 to smash_egg.smash_egg_consume_record_old_98 ;
rename table smash_egg.smash_egg_consume_record_99 to smash_egg.smash_egg_consume_record_old_99 ;*/
CREATE TABLE `smash_egg_consume_record_01` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uid` int(10) unsigned NOT NULL COMMENT 'uid',
  `amount` int(10) unsigned NOT NULL,
  `fee` int(10) unsigned NOT NULL,
  `order_id` varchar(100) NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `operate_id` varchar(64) DEFAULT NULL,
  `remain_chance` int(10) unsigned NOT NULL,
  `status` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `operate_id` (`operate_id`),
  KEY `consume_user` (`uid`),
  KEY `consume_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=654285 DEFAULT CHARSET=utf8;
INSERT INTO smash_egg_consume_record_01 (uid,amount,fee,order_id,create_time,operate_id,remain_chance,status) SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_00 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_01 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_02 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_03 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_04 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_05 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_06 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_07 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_08 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_09 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_10 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_11 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_12 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_13 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_14 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_15 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_16 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_17 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_18 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_19 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_20 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_21 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_22 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_23 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_24 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_25 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_26 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_27 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_28 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_29 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_30 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_31 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_32 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_33 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_34 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_35 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_36 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_37 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_38 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_39 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_40 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_41 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_42 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_43 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_44 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_45 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_46 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_47 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_48 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_49 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_50 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_51 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_52 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_53 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_54 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_55 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_56 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_57 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_58 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_59 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_60 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_61 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_62 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_63 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_64 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_65 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_66 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_67 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_68 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_69 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_70 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_71 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_72 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_73 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_74 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_75 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_76 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_77 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_78 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_79 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_80 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_81 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_82 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_83 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_84 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_85 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_86 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_87 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_88 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_89 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_90 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_91 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_92 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_93 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_94 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_95 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_96 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_97 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_98 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_99 WHERE create_time between '2020-01-01 00:00:00' AND '2020-01-31 00:00:00' ORDER BY create_time ;

CREATE TABLE `smash_egg_consume_record_02` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uid` int(10) unsigned NOT NULL COMMENT 'uid',
  `amount` int(10) unsigned NOT NULL,
  `fee` int(10) unsigned NOT NULL,
  `order_id` varchar(100) NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `operate_id` varchar(64) DEFAULT NULL,
  `remain_chance` int(10) unsigned NOT NULL,
  `status` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `operate_id` (`operate_id`),
  KEY `consume_user` (`uid`),
  KEY `consume_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=654285 DEFAULT CHARSET=utf8;
INSERT INTO smash_egg_consume_record_02 (uid,amount,fee,order_id,create_time,operate_id,remain_chance,status) SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_00 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_01 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_02 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_03 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_04 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_05 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_06 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_07 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_08 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_09 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_10 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_11 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_12 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_13 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_14 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_15 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_16 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_17 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_18 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_19 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_20 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_21 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_22 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_23 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_24 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_25 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_26 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_27 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_28 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_29 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_30 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_31 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_32 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_33 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_34 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_35 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_36 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_37 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_38 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_39 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_40 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_41 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_42 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_43 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_44 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_45 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_46 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_47 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_48 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_49 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_50 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_51 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_52 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_53 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_54 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_55 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_56 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_57 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_58 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_59 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_60 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_61 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_62 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_63 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_64 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_65 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_66 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_67 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_68 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_69 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_70 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_71 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_72 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_73 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_74 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_75 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_76 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_77 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_78 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_79 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_80 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_81 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_82 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_83 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_84 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_85 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_86 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_87 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_88 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_89 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_90 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_91 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_92 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_93 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_94 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_95 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_96 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_97 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_98 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_99 WHERE create_time between '2020-02-01 00:00:00' AND '2020-02-29 00:00:00' ORDER BY create_time ;

CREATE TABLE `smash_egg_consume_record_03` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uid` int(10) unsigned NOT NULL COMMENT 'uid',
  `amount` int(10) unsigned NOT NULL,
  `fee` int(10) unsigned NOT NULL,
  `order_id` varchar(100) NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `operate_id` varchar(64) DEFAULT NULL,
  `remain_chance` int(10) unsigned NOT NULL,
  `status` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `operate_id` (`operate_id`),
  KEY `consume_user` (`uid`),
  KEY `consume_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=654285 DEFAULT CHARSET=utf8;
INSERT INTO smash_egg_consume_record_03 (uid,amount,fee,order_id,create_time,operate_id,remain_chance,status) SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_00 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_01 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_02 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_03 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_04 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_05 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_06 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_07 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_08 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_09 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_10 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_11 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_12 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_13 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_14 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_15 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_16 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_17 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_18 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_19 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_20 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_21 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_22 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_23 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_24 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_25 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_26 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_27 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_28 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_29 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_30 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_31 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_32 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_33 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_34 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_35 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_36 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_37 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_38 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_39 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_40 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_41 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_42 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_43 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_44 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_45 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_46 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_47 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_48 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_49 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_50 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_51 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_52 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_53 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_54 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_55 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_56 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_57 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_58 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_59 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_60 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_61 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_62 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_63 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_64 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_65 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_66 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_67 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_68 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_69 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_70 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_71 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_72 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_73 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_74 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_75 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_76 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_77 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_78 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_79 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_80 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_81 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_82 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_83 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_84 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_85 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_86 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_87 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_88 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_89 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_90 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_91 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_92 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_93 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_94 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_95 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_96 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_97 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_98 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_99 WHERE create_time between '2020-03-01 00:00:00' AND '2020-03-31 00:00:00' ORDER BY create_time ;

CREATE TABLE `smash_egg_consume_record_04` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uid` int(10) unsigned NOT NULL COMMENT 'uid',
  `amount` int(10) unsigned NOT NULL,
  `fee` int(10) unsigned NOT NULL,
  `order_id` varchar(100) NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `operate_id` varchar(64) DEFAULT NULL,
  `remain_chance` int(10) unsigned NOT NULL,
  `status` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `operate_id` (`operate_id`),
  KEY `consume_user` (`uid`),
  KEY `consume_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=654285 DEFAULT CHARSET=utf8;
INSERT INTO smash_egg_consume_record_04 (uid,amount,fee,order_id,create_time,operate_id,remain_chance,status) SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_00 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_01 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_02 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_03 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_04 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_05 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_06 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_07 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_08 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_09 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_10 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_11 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_12 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_13 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_14 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_15 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_16 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_17 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_18 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_19 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_20 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_21 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_22 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_23 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_24 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_25 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_26 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_27 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_28 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_29 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_30 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_31 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_32 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_33 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_34 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_35 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_36 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_37 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_38 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_39 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_40 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_41 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_42 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_43 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_44 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_45 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_46 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_47 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_48 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_49 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_50 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_51 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_52 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_53 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_54 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_55 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_56 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_57 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_58 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_59 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_60 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_61 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_62 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_63 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_64 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_65 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_66 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_67 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_68 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_69 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_70 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_71 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_72 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_73 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_74 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_75 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_76 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_77 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_78 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_79 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_80 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_81 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_82 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_83 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_84 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_85 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_86 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_87 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_88 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_89 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_90 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_91 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_92 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_93 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_94 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_95 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_96 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_97 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_98 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_99 WHERE create_time between '2020-04-01 00:00:00' AND '2020-04-30 00:00:00' ORDER BY create_time ;

CREATE TABLE `smash_egg_consume_record_05` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uid` int(10) unsigned NOT NULL COMMENT 'uid',
  `amount` int(10) unsigned NOT NULL,
  `fee` int(10) unsigned NOT NULL,
  `order_id` varchar(100) NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `operate_id` varchar(64) DEFAULT NULL,
  `remain_chance` int(10) unsigned NOT NULL,
  `status` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `operate_id` (`operate_id`),
  KEY `consume_user` (`uid`),
  KEY `consume_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=654285 DEFAULT CHARSET=utf8;
INSERT INTO smash_egg_consume_record_05 (uid,amount,fee,order_id,create_time,operate_id,remain_chance,status) SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_00 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_01 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_02 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_03 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_04 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_05 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_06 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_07 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_08 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_09 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_10 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_11 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_12 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_13 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_14 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_15 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_16 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_17 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_18 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_19 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_20 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_21 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_22 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_23 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_24 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_25 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_26 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_27 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_28 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_29 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_30 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_31 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_32 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_33 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_34 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_35 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_36 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_37 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_38 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_39 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_40 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_41 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_42 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_43 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_44 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_45 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_46 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_47 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_48 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_49 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_50 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_51 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_52 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_53 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_54 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_55 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_56 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_57 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_58 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_59 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_60 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_61 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_62 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_63 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_64 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_65 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_66 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_67 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_68 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_69 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_70 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_71 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_72 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_73 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_74 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_75 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_76 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_77 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_78 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_79 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_80 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_81 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_82 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_83 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_84 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_85 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_86 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_87 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_88 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_89 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_90 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_91 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_92 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_93 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_94 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_95 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_96 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_97 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_98 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_99 WHERE create_time between '2020-05-01 00:00:00' AND '2020-05-31 00:00:00' ORDER BY create_time ;

CREATE TABLE `smash_egg_consume_record_06` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uid` int(10) unsigned NOT NULL COMMENT 'uid',
  `amount` int(10) unsigned NOT NULL,
  `fee` int(10) unsigned NOT NULL,
  `order_id` varchar(100) NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `operate_id` varchar(64) DEFAULT NULL,
  `remain_chance` int(10) unsigned NOT NULL,
  `status` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `operate_id` (`operate_id`),
  KEY `consume_user` (`uid`),
  KEY `consume_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=654285 DEFAULT CHARSET=utf8;
INSERT INTO smash_egg_consume_record_06 (uid,amount,fee,order_id,create_time,operate_id,remain_chance,status) SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_00 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_01 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_02 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_03 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_04 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_05 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_06 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_07 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_08 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_09 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_10 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_11 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_12 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_13 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_14 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_15 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_16 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_17 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_18 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_19 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_20 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_21 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_22 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_23 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_24 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_25 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_26 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_27 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_28 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_29 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_30 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_31 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_32 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_33 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_34 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_35 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_36 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_37 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_38 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_39 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_40 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_41 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_42 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_43 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_44 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_45 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_46 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_47 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_48 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_49 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_50 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_51 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_52 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_53 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_54 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_55 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_56 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_57 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_58 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_59 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_60 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_61 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_62 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_63 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_64 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_65 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_66 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_67 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_68 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_69 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_70 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_71 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_72 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_73 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_74 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_75 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_76 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_77 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_78 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_79 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_80 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_81 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_82 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_83 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_84 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_85 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_86 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_87 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_88 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_89 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_90 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_91 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_92 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_93 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_94 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_95 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_96 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_97 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_98 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_99 WHERE create_time between '2020-06-01 00:00:00' AND '2020-06-30 00:00:00' ORDER BY create_time ;

CREATE TABLE `smash_egg_consume_record_07` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uid` int(10) unsigned NOT NULL COMMENT 'uid',
  `amount` int(10) unsigned NOT NULL,
  `fee` int(10) unsigned NOT NULL,
  `order_id` varchar(100) NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `operate_id` varchar(64) DEFAULT NULL,
  `remain_chance` int(10) unsigned NOT NULL,
  `status` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `operate_id` (`operate_id`),
  KEY `consume_user` (`uid`),
  KEY `consume_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=654285 DEFAULT CHARSET=utf8;
INSERT INTO smash_egg_consume_record_07 (uid,amount,fee,order_id,create_time,operate_id,remain_chance,status) SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_00 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_01 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_02 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_03 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_04 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_05 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_06 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_07 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_08 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_09 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_10 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_11 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_12 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_13 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_14 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_15 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_16 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_17 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_18 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_19 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_20 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_21 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_22 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_23 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_24 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_25 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_26 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_27 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_28 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_29 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_30 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_31 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_32 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_33 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_34 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_35 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_36 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_37 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_38 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_39 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_40 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_41 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_42 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_43 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_44 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_45 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_46 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_47 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_48 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_49 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_50 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_51 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_52 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_53 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_54 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_55 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_56 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_57 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_58 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_59 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_60 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_61 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_62 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_63 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_64 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_65 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_66 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_67 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_68 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_69 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_70 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_71 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_72 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_73 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_74 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_75 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_76 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_77 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_78 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_79 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_80 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_81 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_82 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_83 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_84 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_85 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_86 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_87 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_88 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_89 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_90 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_91 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_92 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_93 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_94 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_95 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_96 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_97 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_98 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_99 WHERE create_time between '2020-07-01 00:00:00' AND '2020-07-31 00:00:00' ORDER BY create_time ;

CREATE TABLE `smash_egg_consume_record_08` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uid` int(10) unsigned NOT NULL COMMENT 'uid',
  `amount` int(10) unsigned NOT NULL,
  `fee` int(10) unsigned NOT NULL,
  `order_id` varchar(100) NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `operate_id` varchar(64) DEFAULT NULL,
  `remain_chance` int(10) unsigned NOT NULL,
  `status` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `operate_id` (`operate_id`),
  KEY `consume_user` (`uid`),
  KEY `consume_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=654285 DEFAULT CHARSET=utf8;
INSERT INTO smash_egg_consume_record_08 (uid,amount,fee,order_id,create_time,operate_id,remain_chance,status) SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_00 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_01 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_02 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_03 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_04 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_05 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_06 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_07 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_08 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_09 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_10 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_11 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_12 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_13 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_14 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_15 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_16 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_17 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_18 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_19 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_20 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_21 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_22 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_23 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_24 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_25 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_26 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_27 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_28 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_29 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_30 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_31 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_32 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_33 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_34 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_35 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_36 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_37 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_38 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_39 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_40 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_41 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_42 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_43 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_44 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_45 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_46 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_47 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_48 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_49 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_50 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_51 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_52 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_53 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_54 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_55 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_56 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_57 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_58 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_59 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_60 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_61 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_62 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_63 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_64 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_65 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_66 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_67 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_68 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_69 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_70 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_71 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_72 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_73 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_74 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_75 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_76 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_77 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_78 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_79 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_80 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_81 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_82 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_83 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_84 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_85 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_86 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_87 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_88 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_89 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_90 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_91 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_92 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_93 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_94 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_95 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_96 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_97 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_98 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_99 WHERE create_time between '2020-08-01 00:00:00' AND '2020-08-31 00:00:00' ORDER BY create_time ;

CREATE TABLE `smash_egg_consume_record_09` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uid` int(10) unsigned NOT NULL COMMENT 'uid',
  `amount` int(10) unsigned NOT NULL,
  `fee` int(10) unsigned NOT NULL,
  `order_id` varchar(100) NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `operate_id` varchar(64) DEFAULT NULL,
  `remain_chance` int(10) unsigned NOT NULL,
  `status` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `operate_id` (`operate_id`),
  KEY `consume_user` (`uid`),
  KEY `consume_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=654285 DEFAULT CHARSET=utf8;
INSERT INTO smash_egg_consume_record_09 (uid,amount,fee,order_id,create_time,operate_id,remain_chance,status) SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_00 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_01 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_02 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_03 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_04 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_05 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_06 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_07 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_08 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_09 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_10 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_11 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_12 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_13 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_14 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_15 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_16 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_17 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_18 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_19 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_20 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_21 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_22 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_23 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_24 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_25 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_26 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_27 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_28 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_29 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_30 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_31 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_32 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_33 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_34 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_35 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_36 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_37 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_38 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_39 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_40 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_41 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_42 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_43 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_44 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_45 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_46 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_47 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_48 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_49 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_50 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_51 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_52 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_53 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_54 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_55 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_56 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_57 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_58 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_59 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_60 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_61 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_62 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_63 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_64 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_65 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_66 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_67 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_68 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_69 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_70 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_71 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_72 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_73 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_74 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_75 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_76 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_77 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_78 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_79 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_80 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_81 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_82 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_83 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_84 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_85 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_86 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_87 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_88 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_89 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_90 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_91 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_92 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_93 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_94 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_95 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_96 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_97 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_98 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_99 WHERE create_time between '2020-09-01 00:00:00' AND '2020-09-30 00:00:00' ORDER BY create_time ;

CREATE TABLE `smash_egg_consume_record_10` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uid` int(10) unsigned NOT NULL COMMENT 'uid',
  `amount` int(10) unsigned NOT NULL,
  `fee` int(10) unsigned NOT NULL,
  `order_id` varchar(100) NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `operate_id` varchar(64) DEFAULT NULL,
  `remain_chance` int(10) unsigned NOT NULL,
  `status` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `operate_id` (`operate_id`),
  KEY `consume_user` (`uid`),
  KEY `consume_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=654285 DEFAULT CHARSET=utf8;
INSERT INTO smash_egg_consume_record_10 (uid,amount,fee,order_id,create_time,operate_id,remain_chance,status) SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_00 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_01 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_02 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_03 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_04 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_05 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_06 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_07 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_08 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_09 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_10 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_11 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_12 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_13 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_14 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_15 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_16 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_17 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_18 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_19 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_20 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_21 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_22 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_23 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_24 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_25 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_26 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_27 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_28 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_29 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_30 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_31 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_32 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_33 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_34 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_35 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_36 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_37 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_38 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_39 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_40 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_41 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_42 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_43 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_44 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_45 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_46 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_47 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_48 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_49 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_50 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_51 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_52 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_53 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_54 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_55 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_56 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_57 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_58 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_59 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_60 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_61 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_62 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_63 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_64 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_65 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_66 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_67 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_68 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_69 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_70 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_71 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_72 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_73 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_74 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_75 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_76 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_77 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_78 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_79 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_80 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_81 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_82 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_83 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_84 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_85 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_86 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_87 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_88 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_89 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_90 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_91 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_92 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_93 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_94 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_95 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_96 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_97 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_98 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_99 WHERE create_time between '2020-10-01 00:00:00' AND '2020-10-31 00:00:00' ORDER BY create_time ;

CREATE TABLE `smash_egg_consume_record_11` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uid` int(10) unsigned NOT NULL COMMENT 'uid',
  `amount` int(10) unsigned NOT NULL,
  `fee` int(10) unsigned NOT NULL,
  `order_id` varchar(100) NOT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `operate_id` varchar(64) DEFAULT NULL,
  `remain_chance` int(10) unsigned NOT NULL,
  `status` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `operate_id` (`operate_id`),
  KEY `consume_user` (`uid`),
  KEY `consume_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=654285 DEFAULT CHARSET=utf8;
INSERT INTO smash_egg_consume_record_11 (uid,amount,fee,order_id,create_time,operate_id,remain_chance,status) SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_00 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_01 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_02 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_03 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_04 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_05 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_06 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_07 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_08 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_09 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_10 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_11 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_12 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_13 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_14 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_15 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_16 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_17 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_18 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_19 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_20 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_21 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_22 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_23 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_24 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_25 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_26 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_27 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_28 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_29 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_30 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_31 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_32 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_33 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_34 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_35 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_36 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_37 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_38 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_39 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_40 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_41 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_42 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_43 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_44 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_45 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_46 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_47 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_48 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_49 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_50 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_51 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_52 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_53 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_54 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_55 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_56 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_57 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_58 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_59 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_60 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_61 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_62 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_63 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_64 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_65 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_66 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_67 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_68 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_69 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_70 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_71 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_72 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_73 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_74 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_75 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_76 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_77 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_78 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_79 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_80 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_81 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_82 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_83 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_84 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_85 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_86 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_87 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_88 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_89 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_90 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_91 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_92 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_93 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_94 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_95 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_96 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_97 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_98 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' UNION ALL SELECT uid,amount,fee,order_id,create_time,operate_id,remain_chance,status from smash_egg_consume_record_old_99 WHERE create_time between '2020-11-01 00:00:00' AND '2020-11-30 00:00:00' ORDER BY create_time ;

