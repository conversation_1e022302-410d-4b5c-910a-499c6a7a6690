package internal

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	userPresent "golang.52tt.com/clients/userpresent"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app/channel_wedding_logic"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/channel-wedding-conf"
	"golang.52tt.com/protocol/services/demo/echo"
	userPresentpb "golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/channel-wedding-conf/internal/conf"
	"google.golang.org/grpc/codes"
	"math"
)

type Server struct {
	bc             conf.IBusinessConfManager
	userPresentCli userPresent.IClient
}

func NewServer(ctx context.Context, cfg *conf.StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	bc, err := conf.NewBusinessConfManager()
	if err != nil {
		return nil, err
	}

	userPresentCli := userPresent.NewClient()

	s := &Server{
		bc:             bc,
		userPresentCli: userPresentCli,
	}

	return s, nil
}

func (s *Server) ShutDown() {}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) GetThemeCfg(ctx context.Context, req *pb.GetThemeCfgReq) (*pb.GetThemeCfgResp, error) {
	resp := &pb.GetThemeCfgResp{}

	themeCfg := s.bc.GetWeddingThemeCfg(req.GetThemeId())
	if themeCfg == nil {
		log.ErrorWithCtx(ctx, "GetThemeCfg failed to GetWeddingThemeCfg. themeId:%d", req.GetThemeId())
		return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "主题配置不存在")
	}

	var err error
	resp.ThemeCfg, err = s.fillPbThemeCfg(ctx, themeCfg)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetThemeCfg failed to fillPbThemeCfg. themeId:%d, err: %v", req.GetThemeId(), err)
		return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "主题配置有误")
	}

	return resp, nil
}

func (s *Server) GetThemeCfgList(ctx context.Context, req *pb.GetThemeCfgListReq) (*pb.GetThemeCfgListResp, error) {
	resp := &pb.GetThemeCfgListResp{
		ThemeCfgList: make([]*pb.ThemeCfg, 0),
	}

	themeCfgList := s.bc.GetWeddingThemeCfgList()
	for _, v := range themeCfgList {
		// 支持配置下架
		if v.IsDelete == true {
			continue
		}
		respThemeCfg, err := s.fillPbThemeCfg(ctx, v)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetThemeCfgList failed to fillPbThemeCfg. themeId:%d, err: %v", v.ThemeId, err)
			return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "主题配置有误")
		}
		resp.ThemeCfgList = append(resp.ThemeCfgList, respThemeCfg)
	}
	resp.ThemeTitleBgIcon = s.bc.GetThemeTitleBgIcon()
	resp.ThemeTitleSelectedIcon = s.bc.GetThemeTitleSelectedIcon()
	return resp, nil
}

func (s *Server) fillPbThemeCfg(ctx context.Context, cfg *conf.WeddingThemeCfg) (*pb.ThemeCfg, error) {
	if cfg == nil {
		return nil, fmt.Errorf("themeCfg is nil")
	}

	priceInfo, err := s.fillPbPriceInfo(ctx, cfg.PriceInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "fillPbPriceInfo failed, err: %v", err)
		return nil, err
	}

	out := &pb.ThemeCfg{
		ThemeId:                     cfg.ThemeId,
		ThemeName:                   cfg.Name,
		PriceInfo:                   priceInfo,
		ThemeRoomResource:           fillPbResourceInfo(cfg.RoomResourceCfg),
		SceneCfgList:                make([]*pb.WeddingSceneCfg, 0),
		ThemeLevelCfgList:           make([]*pb.ThemeLevelCfg, 0),
		WeddingPreviewCfg:           fillPbWeddingPreview(cfg.PreviewResource),
		MemorialVideoResource:       fillPbResourceInfo(cfg.MemorialVideo),
		ChairResCfg:                 fillPbChairResCfg(cfg.ChairResConf),
		SelectedThemeTitleIcon:      cfg.SelectedThemeTitleIcon,
		UnselectedThemeTitleIcon:    cfg.UnselectedThemeTitleIcon,
		ExamplePhoto:                cfg.ExamplePhoto,
		ThemeBackground:             cfg.ThemeBackground,
		ThemePreviewText:            cfg.ThemePreviewText,
		RewardInfoDesc:              cfg.RewardInfoDesc,
		RewardInfoList:              make([]*pb.FinishWeddingAward, 0),
		WeddingHallComingBackground: cfg.WeddingHallComingBackground,
		MailLadyLeftBgIcon:          cfg.MailLadyLeftBgIcon,
		MailLadyRightBgIcon:         cfg.MailLadyRightBgIcon,
		ThemeIcon:                   cfg.Icon,
		IsDeleted:                   cfg.IsDelete,
		WeddingGiftIds:              cfg.WeddingGiftIds,
		PreProgressPanelResourceUrl: cfg.PreProgressPanelResourceUrl,
		PreProgressPanelResourceMd5: cfg.PreProgressPanelResourceMd5,
		MvpSettlementResource:       fillPbResourceInfo(cfg.MvpSettlementResource),
		PreProgressPresentTargetVal: cfg.PreProgressPresentTargetVal,
	}

	for _, v := range cfg.SceneCfgList {
		out.SceneCfgList = append(out.SceneCfgList, fillPbSceneCfg(v))
	}

	for _, v := range cfg.LevelCfgList {
		out.ThemeLevelCfgList = append(out.ThemeLevelCfgList, fillPbThemeLevelCfg(v))
	}

	for _, v := range cfg.RewardInfoList {
		out.RewardInfoList = append(out.RewardInfoList, fillPbRewardInfo(v))
	}

	return out, nil
}

func fillPbRewardInfo(cfg *conf.RewardPreview) *pb.FinishWeddingAward {
	if cfg == nil {
		return nil
	}

	out := &pb.FinishWeddingAward{
		AwardAnimation: &pb.ResourceCfg{
			ResourceType: uint32(channel_wedding_logic.WeddingAnimationType_WEDDING_ANIMATION_TYPE_STATIC),
			ResourcePng:  cfg.Png,
		},
		BottomText: cfg.BottomText,
		TopText:    cfg.TopText,
	}

	return out
}

func fillPbThemeLevelCfg(cfg *conf.WeddingLevelCfg) *pb.ThemeLevelCfg {
	if cfg == nil {
		return nil
	}

	var resourceUrl, resourceMd5, resourcePng, smallIcon string
	if cfg.ScenePreviewCfg != nil {
		resourcePng = cfg.ScenePreviewCfg.ScenePng
		smallIcon = cfg.ScenePreviewCfg.SmallIcon
		if cfg.ScenePreviewCfg.ZoomAnimation != nil {
			resourceUrl = cfg.ScenePreviewCfg.ZoomAnimation.Url
			resourceMd5 = cfg.ScenePreviewCfg.ZoomAnimation.Md5
		}
	}
	out := &pb.ThemeLevelCfg{
		Level:                 cfg.Level,
		RoomBackgroundPicture: cfg.RoomBackgroundPic,
		RoomBackgroundMp4Url:  cfg.RoomBackgroundMp4,
		GuestDressCfgMap:      make(map[uint32]*pb.GuestDressCfg),
		WeddingPreview: &pb.WeddingScenePreview{
			SceneAnimation: &pb.ResourceCfg{
				ResourceType: uint32(channel_wedding_logic.WeddingAnimationType_WEDDING_ANIMATION_TYPE_STATIC),
				ResourcePng:  resourcePng,
			},
			SmallIcon: smallIcon,
			ZoomAnimation: &pb.ResourceCfg{
				ResourceType: uint32(channel_wedding_logic.WeddingAnimationType_WEDDING_ANIMATION_TYPE_VAP),
				ResourceUrl:  resourceUrl,
				ResourceMd5:  resourceMd5,
			},
		},
		WeddingHallGoingBackground:        cfg.WeddingHallGoingBackground,
		CertificatePic:                    cfg.CertificatePic,
		PresentId:                         cfg.PresentId,
		PresentDay:                        cfg.PresentDay,
		SpecialBackgroundPicture:          cfg.SpecialBackgroundPicture,
		SpecialBackgroundMp4Url:           cfg.SpecialBackgroundMp4Url,
		FellowSpaceWeddingElement:         cfg.FellowSpaceWeddingElement,
		FellowSpaceWeddingBackground:      cfg.FellowSpaceWeddingBackground,
		FellowHouseSpaceWeddingBackground: cfg.FellowHouseSpaceWeddingBackground,
		FellowSpaceWeddingColor:           cfg.FellowSpaceWeddingColor,
		FellowSpaceWeddingCertificate:     cfg.FellowSpaceWeddingCertificate,
		BuyWeddingPresentId:               cfg.BuyWeddingPresentId,
	}

	for _, v := range cfg.GuestDressCfgList {
		out.GuestDressCfgMap[v.GuestType] = fillPbGuestDressCfg(v)
	}

	return out
}

func fillPbGuestDressCfg(cfg *conf.GuestDressCfg) *pb.GuestDressCfg {
	if cfg == nil {
		return nil
	}

	out := &pb.GuestDressCfg{
		DressText: cfg.DressText,
		SuitCfg:   fillPbWeddingSuitCfg(cfg.SuitCfg),
		WeddingDress: &pb.ResourceCfg{
			ResourceType: uint32(channel_wedding_logic.WeddingAnimationType_WEDDING_ANIMATION_TYPE_STATIC),
			ResourcePng:  cfg.DressPreview,
		},
		SmallIcon:              cfg.DressPreviewSmallIcon,
		ClothesUpgradePopupPng: cfg.ClothesUpgradePopupPng,
	}

	return out
}

func fillPbWeddingSuitCfg(cfg *conf.WeddingSuitCfg) *pb.GuestSuitCfg {
	if cfg == nil {
		return nil
	}

	out := &pb.GuestSuitCfg{
		Name:        cfg.Name,
		Icon:        cfg.Icon,
		ItemIds:     cfg.ClothesIds,
		DurationSec: cfg.Duration,
	}

	return out
}

func fillPbSceneCfg(cfg *conf.WeddingSceneCfg) *pb.WeddingSceneCfg {
	if cfg == nil {
		return nil
	}

	out := &pb.WeddingSceneCfg{
		Scene:          cfg.SceneId,
		SceneIcon:      cfg.Icon,
		ClipImSmallPic: cfg.ClipImSmallPic,
		ClipDefaultPic: cfg.ClipDefaultPic,
		Resource:       fillPbResourceInfo(cfg.ResourceCfg),
		BoneCfgList:    make([]*pb.WeddingSceneBoneCfg, 0),
	}

	for _, v := range cfg.BoneCfgList {
		out.BoneCfgList = append(out.BoneCfgList, fillPbBoneCfg(v))
	}

	return out
}

func fillPbBoneCfg(cfg *conf.SceneBoneCfg) *pb.WeddingSceneBoneCfg {
	if cfg == nil {
		return nil
	}

	out := &pb.WeddingSceneBoneCfg{
		Level:         cfg.Level,
		SeqIndex:      cfg.SeqIndex,
		BoneId:        cfg.BoneId,
		AnimationName: cfg.AnimationName,
		BaseBoneId:    cfg.BaseBoneId,
	}

	return out
}

func fillPbChairResCfg(cfg *conf.ChairGameResConf) *pb.ChairGameResourceCfg {
	if cfg == nil {
		return nil
	}

	out := &pb.ChairGameResourceCfg{
		ChairPic:            cfg.ChairPic,
		SittingPoseFemaleId: cfg.SittingPoseFemaleId,
		SittingPoseMaleId:   cfg.SittingPoseMaleId,
		StandbyFemaleId:     cfg.StandbyFemaleId,
		StandbyMaleId:       cfg.StandbyMaleId,
		FailFemaleIds:       cfg.FailFemaleIds,
		FailMaleIds:         cfg.FailMaleIds,
	}

	return out
}

func fillPbWeddingPreview(cfg *conf.ResourceCfg) *pb.WeddingPreviewCfg {
	if cfg == nil {
		return nil
	}

	out := &pb.WeddingPreviewCfg{
		Resource:     fillPbResourceInfo(cfg),
		CpBoneId:     cfg.BoneId,
		ItemIds:      cfg.Items,
		BaseCpBoneId: cfg.BaseBoneId,
	}

	return out
}

// fillPbPriceInfo 购买价格跟获得的礼物价格一致,
func (s *Server) fillPbPriceInfo(ctx context.Context, cfg *conf.PriceInfo) (*pb.WeddingPriceInfo, error) {
	if cfg == nil {
		return nil, fmt.Errorf("price info cfg is nil")
	}
	if cfg.Type == uint32(userPresentpb.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) {
		return &pb.WeddingPriceInfo{
			Price:     cfg.Price,
			PriceType: cfg.Type,
		}, nil
	}

	giftIdList := make([]uint32, 0, len(cfg.HotTimePrice)+len(cfg.NormalTimePrice))
	for _, item := range cfg.NormalTimePrice {
		giftIdList = append(giftIdList, item.GiftId)
	}
	for _, item := range cfg.HotTimePrice {
		giftIdList = append(giftIdList, item.GiftId)
	}
	giftResp, err := s.userPresentCli.GetPresentConfigByIdList(ctx, 0, giftIdList, uint32(userPresentpb.ConfigListTypeBitMap_CONFIG_NOT_DELETED))
	if err != nil {
		log.ErrorWithCtx(ctx, "fillPbPriceInfo, err: %v, giftIdList: %+v", err, giftIdList)
		return nil, err
	}
	giftMap := make(map[uint32]*userPresentpb.StPresentItemConfig)
	for _, item := range giftResp.GetItemList() {
		giftMap[item.ItemId] = item
	}

	out := &pb.WeddingPriceInfo{
		PriceType: cfg.Type,
	}

	minPrice := uint32(math.MaxUint32)
	for _, item := range cfg.NormalTimePrice {
		if giftMap[item.GiftId] == nil {
			continue
			//return nil, fmt.Errorf("giftId %d not found in giftMap", item.GiftId)
		}
		if giftMap[item.GiftId].GetPrice() < minPrice {
			minPrice = giftMap[item.GiftId].GetPrice()
		}
		out.NormalTimePrice = append(out.NormalTimePrice, &pb.WeddingPriceItem{
			Price:  giftMap[item.GiftId].GetPrice(),
			GiftId: item.GiftId,
		})
	}

	for _, item := range cfg.HotTimePrice {
		if giftMap[item.GiftId] == nil {
			continue
			//return nil, fmt.Errorf("giftId %d not found in giftMap", item.GiftId)
		}
		out.HotTimePrice = append(out.HotTimePrice, &pb.WeddingPriceItem{
			Price:  giftMap[item.GiftId].GetPrice(),
			GiftId: item.GiftId,
		})
	}
	out.Price = minPrice

	if len(out.NormalTimePrice) == 0 && len(out.HotTimePrice) == 0 {
		return nil, fmt.Errorf("price info cfg is empty")
	}
	return out, nil
}

func fillPbResourceInfo(cfg *conf.ResourceCfg) *pb.ResourceCfg {
	if cfg == nil {
		return nil
	}
	var resourceType uint32
	if cfg.Pic != "" {
		resourceType = uint32(channel_wedding_logic.WeddingAnimationType_WEDDING_ANIMATION_TYPE_STATIC)
	} else {
		resourceType = uint32(channel_wedding_logic.WeddingAnimationType_WEDDING_ANIMATION_TYPE_VAP)
	}

	out := &pb.ResourceCfg{
		ResourceType: resourceType,
		ResourceUrl:  cfg.Url,
		ResourceMd5:  cfg.Md5,
		ResourcePng:  cfg.Pic,
	}

	return out
}

func (s *Server) GetAllWeddingRankBackground(c context.Context, req *pb.GetAllWeddingRankBackgroundReq) (*pb.GetAllWeddingRankBackgroundResp, error) {
	out := &pb.GetAllWeddingRankBackgroundResp{}

	rankBgMap := s.bc.GetWeddingRankGgMap()
	if rankBgMap == nil {
		return out, nil
	}

	bgMap := make(map[uint32]*pb.WeddingRankBgList)
	for themeId, rankGgList := range rankBgMap {
		bgList := make([]*pb.WeddingRankBg, 0)
		for _, rankGg := range rankGgList {
			bgList = append(bgList, &pb.WeddingRankBg{
				BgUrl: rankGg.BgIcon,
				Lv:    rankGg.Level,
			})
		}
		bgMap[themeId] = &pb.WeddingRankBgList{
			WeddingRankBgList: bgList,
		}
	}

	out.WeddingRankBgMap = bgMap
	return out, nil
}

func (s *Server) GetThemeFinishedAwardCfg(c context.Context, req *pb.GetThemeFinishedAwardCfgReq) (*pb.GetThemeFinishedAwardCfgResp, error) {
	out := &pb.GetThemeFinishedAwardCfgResp{}

	themeAwardMap := make(map[uint32]*pb.FinishWeddingAwardList)
	for themeId, awardList := range s.bc.GetAllWeddingFinishedAwardCfg() {
		list := make([]*pb.FinishWeddingAward, 0)
		for _, award := range awardList {
			if len(award.RewardList) == 0 {
				continue
			}
			lvAwardList := make([]*pb.AwardInfo, 0)
			for _, reward := range award.RewardList {
				lvAwardList = append(lvAwardList, &pb.AwardInfo{
					AwardType:     reward.RewardType,
					AwardId:       reward.RewardId,
					AwardIdFemale: reward.RewardIdFemale,
					Amount:        reward.Amount,
					Level:         reward.Level,
					LvName:        reward.LvName,
				})
			}
			list = append(list, &pb.FinishWeddingAward{
				//AwardAnimation: nil,
				TopText:    award.TopText,
				BottomText: award.BottomText,
				AwardList:  lvAwardList,
			})
		}
		if len(list) > 0 {
			themeAwardMap[themeId] = &pb.FinishWeddingAwardList{
				ThemeId:   themeId,
				AwardList: list,
			}
		}
	}

	out.ThemeFinishedAwardMap = themeAwardMap
	return out, nil
}
