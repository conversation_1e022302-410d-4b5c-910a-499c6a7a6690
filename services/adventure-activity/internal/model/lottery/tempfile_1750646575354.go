package lottery

import (
	"context"
	"crypto/rand"
	"math/big"
	"sort"
	"sync"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/services/adventure-activity/internal/conf"
	activity_conf "golang.52tt.com/services/adventure-activity/internal/model/activity-conf"
	"golang.52tt.com/services/adventure-activity/internal/model/comm"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"time"
)

const (
	BingoNone       = uint32(iota)
	BingoRandom     // 随机通关
	BingoGuaranteed // 保底通关
)

type Lottery struct {
	bc         conf.IBusinessConfManager
	ActConfMgr activity_conf.IActivityConf

	poolList        []comm.Pool
	activityId      uint32
	cfgVersion      int64
	prizeVersion    int64
	historyPrizeMap map[uint32]*comm.Prize
	rwMute          sync.RWMutex
}

func NewLottery(bc conf.IBusinessConfManager, actConfMgr activity_conf.IActivityConf) (*Lottery, error) {
	l := &Lottery{
		bc:              bc,
		ActConfMgr:      actConfMgr,
		poolList:        make([]comm.Pool, 0),
		historyPrizeMap: make(map[uint32]*comm.Prize),
	}
	return l, nil
}

// CheckLotteryUpdate 检查配置更新
func (l *Lottery) CheckLotteryUpdate() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	now := time.Now()
	prizeVersion, cfgVersion, changed := l.checkLotteryVersion(ctx)
	if !changed {
		return nil
	}

	err := l.updatePools(ctx, prizeVersion, cfgVersion)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckLotteryUpdate fail to updatePools. now:%v, err:%v", now, err)
		return err
	}

	log.InfoWithCtx(ctx, "CheckLotteryUpdate success. now:%+v", now)
	return nil
}

// GetLevelCfgList 获取所有关卡配置
func (l *Lottery) GetLevelCfgList() []*comm.LevelConf {
	l.rwMute.RLock()
	defer l.rwMute.RUnlock()

	list := make([]*comm.LevelConf, 0, len(l.poolList))
	for _, pool := range l.poolList {
		if pool.TotalWeight == 0 || pool.Cfg.LevelId == 0 {
			continue
		}
		cfg := pool.Cfg
		list = append(list, &cfg)
	}
	return list
}

// GetPrizePool 获取指定关卡奖池
func (l *Lottery) GetPrizePool(id uint32) (*comm.Pool, bool) {
	return l.GetNextPrizePool(id, 0)
}

// GetNextPrizePool 获取下一关卡奖池（支持循环）
func (l *Lottery) GetNextPrizePool(id, offset uint32) (*comm.Pool, bool) {
	l.rwMute.RLock()
	defer l.rwMute.RUnlock()

	currentIndex := -1
	for i, pool := range l.poolList {
		if pool.Cfg.LevelId == id {
			currentIndex = i
			break
		}
	}

	if currentIndex == -1 {
		return &comm.Pool{}, false
	}

	targetIndex := (currentIndex + int(offset)) % len(l.poolList)
	return &l.poolList[targetIndex], true
}

// setPrizePoolList 设置奖池列表并排序
func (l *Lottery) setPrizePoolList(poolList []comm.Pool) bool {
	l.rwMute.Lock()
	defer l.rwMute.Unlock()

	sort.Slice(poolList, func(i, j int) bool {
		return poolList[i].Cfg.LevelId < poolList[j].Cfg.LevelId
	})

	l.poolList = poolList
	return true
}

// checkLotteryVersion 检查配置版本是否需要更新
func (l *Lottery) checkLotteryVersion(ctx context.Context) (prizeVersion, cfgVersion int64, change bool) {
	var err error

	cfgVersion, err = l.ActConfMgr.GetLevelInfoUpdateVersion(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkLotteryVersion fail to GetLevelInfoUpdateVersion. err:%v", err)
		return
	}

	prizeVersion, err = l.ActConfMgr.GetPondUpdateVersion(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkLotteryVersion fail to GetPrizeUpdateVersion. err:%v", err)
		return
	}

	if l.cfgVersion >= cfgVersion || l.prizeVersion >= prizeVersion {
		return
	}

	change = true
	return
}

// GenPoolListV2 生成奖池列表
func GenPoolListV2(poolList []*comm.Pool) []comm.Pool {
	newPoolList := make([]comm.Pool, 0)
	for _, pool := range poolList {
		if len(pool.Cfg.CompletedPrize) == 0 || len(pool.PrizeList) == 0 {
			log.Warnf("GenPoolListV2 pool.cfg.completed_prize or pool.prize_list is empty. pool:%+v", pool)
			continue
		}
		newPool := InitPrizePool(pool.Cfg, pool.PrizeList)
		newPoolList = append(newPoolList, *newPool)
	}
	return newPoolList
}

// updatePools 更新奖池
func (l *Lottery) updatePools(ctx context.Context, prizeVersion, cfgVersion int64) error {
	levelWithPool, err := l.ActConfMgr.GetLevelConfWithPool(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "updatePools fail to GetLevelConfWithPool. err:%v", err)
		return err
	}

	poolList := GenPoolListV2(levelWithPool)
	_ = l.setPrizePoolList(poolList)
	l.cfgVersion = cfgVersion
	l.prizeVersion = prizeVersion
	return nil
}

// InitPrizePool 初始化单个奖池
func InitPrizePool(cfg comm.LevelConf, prizeList []*comm.Prize) *comm.Pool {
	sort.SliceStable(prizeList, func(r, l int) bool {
		return prizeList[r].Weight > prizeList[l].Weight
	})

	totalWeight := uint64(0)
	targetPrizeIdList := make([]string, 0, len(prizeList))

	for _, info := range prizeList {
		totalWeight += uint64(info.Weight)
		targetPrizeIdList = append(targetPrizeIdList, info.PrizeId)
	}

	return &comm.Pool{
		Cfg:               cfg,
		PrizeList:         prizeList,
		TargetPrizeIdList: targetPrizeIdList,
		TotalWeight:       totalWeight,
	}
}

// prizeDraw 抽奖逻辑
func (l *Lottery) prizeDraw(p *comm.Pool) (*comm.Prize, error) {
	if p.TotalWeight == 0 || len(p.PrizeList) == 0 {
		return nil, protocol.NewExactServerError(nil, status.ErrSys)
	}

	val, err := rand.Int(rand.Reader, big.NewInt(int64(p.TotalWeight)))
	if err != nil {
		return nil, err
	}

	r := val.Int64()
	curr := uint64(0)

	for _, info := range p.PrizeList {
		curr += uint64(info.Weight)
		if curr > uint64(r) {
			return info, nil
		}
	}

	return nil, protocol.NewExactServerError(nil, status.ErrSys)
}

// PrizeDrawOnce 单次抽奖
func (l *Lottery) PrizeDrawOnce(ctx context.Context, p *comm.Pool, req *PrizeDrawReq) (*comm.Prize, error) {
	if p == nil {
		err := protocol.NewExactServerError(nil, status.ErrSys)
		log.ErrorWithCtx(ctx, "PrizeDrawOnce fail. %+v, err:%v", req, err)
		return nil, err
	}

	prize, err := l.prizeDraw(p)
	if err != nil {
		log.ErrorWithCtx(ctx, "PrizeDrawOnce fail to prizeDraw. %+v, err:%v", req, err)
		return nil, err
	}

	if prize == nil {
		err := protocol.NewExactServerError(nil, status.ErrSys)
		log.ErrorWithCtx(ctx, "PrizeDrawOnce fail. pool not found %+v, err:%v", req, err)
		return nil, err
	}

	return prize, nil
}

// PrizeDrawBatch 批量抽奖
func (l *Lottery) PrizeDrawBatch(ctx context.Context, levelId uint32, req PrizeDrawReq, amount uint32) (PrizeDrawResp, error) {
	drawResp := PrizeDrawResp{
		AwardList: make([]*comm.Prize, 0, amount),
	}

	p, ok := l.GetPrizePool(levelId)
	if !ok {
		log.ErrorWithCtx(ctx, "PrizeDrawBatch fail to GetPrizePool. levelId:%d, pool not found", levelId)
		return drawResp, protocol.NewExactServerError(nil, status.ErrSys)
	}

	currProgress := initUserProgress(req.CurrPlayFile, levelId)
	targetPool := p

	for i := uint32(0); i < amount; i++ {
		prize, err := l.PrizeDrawOnce(ctx, targetPool, &req)
		if err != nil {
			return drawResp, err
		}

		drawResp.AwardList = append(drawResp.AwardList, getAwardInfo([]*comm.Prize{prize}, BingoNone, comm.AwardTypeLighted)...)
		l.updateUserProgress(currProgress, 1, prize.PrizeId)

		isCompleted, bingoType := l.checkCompletion(currProgress, targetPool)
		if isCompleted {
			completedReward := getAwardInfo(targetPool.Cfg.CompletedPrize, bingoType, comm.AwardTypeCompleted)
			drawResp.AwardList = append(drawResp.AwardList, completedReward...)

			nextPool, ok := l.GetNextPrizePool(levelId, 1)
			if !ok {
				log.ErrorWithCtx(ctx, "PrizeDrawBatch fail to GetNextPrizePool. levelId:%d, pool not found", levelId)
				return drawResp, protocol.NewExactServerError(nil, status.ErrSys)
			}

			oldLevelId := currProgress.LevelId
			playCount := currProgress.PlayCount
			if oldLevelId > nextPool.Cfg.LevelId {
				playCount++
			}

			// 重置进度
			currProgress = &comm.UserPlayFile{
				LevelId:   nextPool.Cfg.LevelId,
				PlayCount: playCount,
				UserN:     0,
				PrizeMap:  make(map[string]uint32),
			}
			targetPool = nextPool
		}
	}

	drawResp.CurrPlayFile = currProgress
	return drawResp, nil
}

// initUserProgress 初始化用户进度
func initUserProgress(file *comm.UserPlayFile, levelId uint32) *comm.UserPlayFile {
	if file == nil {
		return &comm.UserPlayFile{
			LevelId:  levelId,
			PrizeMap: make(map[string]uint32),
			UserN:    0,
		}
	}

	return &comm.UserPlayFile{
		LevelId:  file.LevelId,
		PrizeMap: copyMap(file.PrizeMap),
		UserN:    file.UserN,
		PlayCount: file.PlayCount,
	}
}

// copyMap 复制 map
func copyMap(m map[string]uint32) map[string]uint32 {
	res := make(map[string]uint32)
	for k, v := range m {
		res[k] = v
	}
	return res
}

// checkCompletion 检查是否通关
func (l *Lottery) checkCompletion(progress *comm.UserPlayFile, p *comm.Pool) (bool, uint32) {
	if progress.UserN >= p.Cfg.ConstantN {
		return true, BingoGuaranteed
	}
	if l.checkIfCompleted(progress, p) {
		return true, BingoRandom
	}
	return false, BingoNone
}

// checkIfCompleted 判断是否集齐所有卡牌
func (l *Lottery) checkIfCompleted(progress *comm.UserPlayFile, p *comm.Pool) bool {
	if len(progress.PrizeMap) < len(p.TargetPrizeIdList) {
		return false
	}

	for _, id := range p.TargetPrizeIdList {
		if _, ok := progress.PrizeMap[id]; !ok {
			return false
		}
	}

	return true
}

// updateUserProgress 更新用户进度
func (l *Lottery) updateUserProgress(progress *comm.UserPlayFile, step uint32, prizeId string) {
	if progress.PrizeMap == nil {
		progress.PrizeMap = make(map[string]uint32)
	}
	progress.PrizeMap[prizeId] += step
	progress.UserN += step
}

// getAwardInfo 构造奖励信息
func getAwardInfo(prizeList []*comm.Prize, bingoType, source uint32) []*comm.Prize {
	if len(prizeList) == 0 {
		return nil
	}

	list := make([]*comm.Prize, 0, len(prizeList))
	for _, v := range prizeList {
		dressInfo := copyDressInfo(v.DressInfo)
		packItems := copyPackItems(v.PackItem)

		list = append(list, &comm.Prize{
			LevelId:      v.LevelId,
			PlayCount:    v.PlayCount,
			ResultType:   v.ResultType,
			PrizeId:      v.PrizeId,
			PrizeType:    v.PrizeType,
			DressSubType: v.DressSubType,
			Amount:       v.Amount,
			Weight:       v.Weight,
			BingoType:    bingoType,
			Source:       source,
			DressInfo:    dressInfo,
			PackItem:     packItems,
		})
	}

	return list
}

// copyDressInfo 拷贝 DressInfo
func copyDressInfo(di *comm.DressInfo) *comm.DressInfo {
	if di == nil {
		return nil
	}
	return &comm.DressInfo{
		DressId:   di.DressId,
		DressIcon: di.DressIcon,
		DressName: di.DressName,
	}
}

// copyPackItems 拷贝 PackItem
func copyPackItems(items []*comm.PackItem) []*comm.PackItem {
	if len(items) == 0 {
		return nil
	}
	res := make([]*comm.PackItem, len(items))
	for i, item := range items {
		res[i] = &comm.PackItem{
			GiftId:    item.GiftId,
			GiftName:  item.GiftName,
			GiftIcon:  item.GiftIcon,
			GiftPrice: item.GiftPrice,
			GiftDesc:  item.GiftDesc,
			Amount:    item.Amount,
			PriceType: item.PriceType,
		}
	}
	return res
}
