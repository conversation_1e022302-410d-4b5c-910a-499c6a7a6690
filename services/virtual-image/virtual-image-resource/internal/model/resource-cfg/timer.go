package resource_cfg

import (
	"archive/zip"
	"bytes"
	"context"
	"crypto/md5"
	"errors"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	obsObjectGateway "golang.52tt.com/clients/obsgateway"
	"golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/virtual_image_logic"
	virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
	"golang.52tt.com/services/virtual-image/virtual-image-resource/internal/model/resource-cfg/store"
	"golang.52tt.com/services/virtual-image/virtual-image-resource/internal/utils"
	"io"
	"os"
	"reflect"
	"runtime"
	"runtime/debug"
	"sort"
	"strings"
	"sync"
	"time"
)

const (
	UpLoadPrefix = "https://obs-cdn.52tt.com/tt/virtual-role/"
	bufSize      = 1024 * 1024 * 10
	appId        = "tt"
	scope        = "virtual-role"
)

func (m *Mgr) StartTimer() {
	m.reloadLevelConfig()
	m.refreshResourceCDNFile()
	m.getResourceCNDRecord()
	// 定期全量更新配置
	go m.TimerHandle(time.Minute*5, m.checkResourceUpdate)

	go m.TimerHandle(time.Minute*5, m.reloadLevelConfig)
	go m.TimerHandle(time.Minute*5, m.getResourceCNDRecord)

	// 云测定期更新
	currentCluster := os.Getenv("MY_CLUSTER")
	if currentCluster == "testing" {
		go m.TimerHandle(time.Minute, m.refreshResourceCDNFile)
	}
}

func (m *Mgr) TimerHandle(d time.Duration, handle func() error) {
	m.wg.Add(1)
	defer m.wg.Done()

	delay := time.NewTicker(d)
	for {
		select {
		case <-m.shutDown:
			return
		case <-delay.C:
			m.handleWithPanicCatch(handle)
		}
	}
}

func (m *Mgr) handleWithPanicCatch(handle func() error) {
	defer func() {
		if err := recover(); err != nil {
			var stack string
			var buf bytes.Buffer
			buf.Write(debug.Stack())
			stack = buf.String()

			nowTime := time.Now().Format("2006-01-02 15:04:05")
			fmt.Printf("%s %v %s %s", nowTime, err, "\n", stack)

			funcName := runtime.FuncForPC(reflect.ValueOf(handle).Pointer()).Name()
			log.Errorf("handleWithPanicCatch panic func:%s, err:%v", funcName, err)
		}
	}()

	_ = handle()
}

func (m *Mgr) checkResourceUpdate() error {
	// 1. 获取数据库最大update_time
	// 2. 比较内存中的updateVersion和数据库中的update_time
	// 3. 如果内存中的updateVersion小于数据库中的update_time，则更新内存中的配置

	start := time.Now()
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	lastTime, err := m.store.GetMaxUpdateTime(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReFreshLocalResourceCache failed. err:%v", err)
		return err
	}
	if SafeInt64ToUint32(lastTime) == (m.latestVersionTs) {
		return nil
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()
	lastCacheList := m.cacheList
	m.cacheList = make([]*store.VirtualImageResourceInfo, 0)
	m.cacheVersionList = make([]*store.VirtualImageResourceInfo, 0)

	totalCount, err := m.store.GetVirtualImageResourceInfoCount(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReFreshLocalResourceCache failed. err:%v", err)
		return err
	}
	log.InfoWithCtx(ctx, "GetVirtualImageResourceInfoCount:%d", totalCount)
	transCount := SafeInt64ToUint32(totalCount)
	var offset uint32
	limit := uint32(1000)
	mapItemId := make(map[uint32]uint32)
	for offset = 0; offset < transCount; offset += limit {
		list, err := m.store.SearchVirtualImageResource(ctx, &virtual_image_resource.SearchVirtualImageResourceRequest{
			Offset: offset,
			Limit:  limit,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "ReFreshLocalResourceCache failed. err:%v", err)
			return err
		}

		for _, item := range list {
			if item.IsDeleted {
				continue
			}
			CloneItem := item
			m.cacheList = append(m.cacheList, &CloneItem)
			m.cacheVersionList = append(m.cacheVersionList, &CloneItem)
			if id, ok := mapItemId[CloneItem.ID]; ok {
				m.cacheList = lastCacheList
				m.cacheMapResource = sync.Map{}
				log.ErrorWithCtx(ctx, "ReFreshLocalResourceCache failed. err:%v", fmt.Errorf("item.ID:%d is duplicate offset:%d cache size:%d", id, offset, len(m.cacheList)))
				return errors.New("item.ID is duplicate")
			}
			mapItemId[CloneItem.ID] = CloneItem.ID
			m.cacheMapResource.Store(CloneItem.ID, CloneItem)
		}
	}

	poseList := make([]*store.VirtualImageResourceInfo, 0)
	mapItemId = make(map[uint32]uint32)

	for _, v := range m.cacheList {
		if id, ok := mapItemId[v.ID]; ok {
			log.ErrorWithCtx(ctx, "ReFreshLocalResourceCache failed. err:%v", fmt.Errorf("item.ID:%d is duplicate", id))
		}
		mapItemId[v.ID] = v.ID
		if v.SubCategory == uint32(virtual_image_resource.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SITTING_POSE) {
			poseList = append(poseList, v)
		}
	}

	sort.SliceStable(m.cacheList, func(i, j int) bool {
		return m.cacheList[i].UpdateTime.Unix() > m.cacheList[j].UpdateTime.Unix()
	})

	sort.SliceStable(m.cacheVersionList, func(i, j int) bool {
		return m.cacheVersionList[i].Version > m.cacheList[j].Version
	})

	// 大小头资源映射 _sc是小头 只展示在商城
	for _, v := range poseList {
		if strings.HasSuffix(v.ResourceName, "_sc") {
			for _, pose := range poseList {
				if pose.ResourceName == strings.TrimSuffix(v.ResourceName, "_sc") {
					m.ActionResourceMap[v.ID] = pose.ID
					break
				}
			}
		}
	}
	m.latestVersionTs = SafeInt64ToUint32(lastTime)
	log.InfoWithCtx(ctx, "checkResourceUpdate success,latestVersionTs:%d,m.cacheList size:%d, mapItemId:%d, cost:%v", m.latestVersionTs, len(m.cacheList), len(mapItemId), time.Since(start))
	return nil
}

func (m *Mgr) reloadLevelConfig() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	m.mutex.RLock()
	defer m.mutex.RUnlock()

	levelConfig, err := m.store.GetLevelConfig(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "reloadLevelConfig failed. err:%v", err)
		return err
	}

	for _, v := range levelConfig {
		m.levelConfigMap[v.Level] = v
	}

	log.InfoWithCtx(ctx, "reloadLevelConfig success,levelConfig size:%d", len(m.levelConfigMap))
	return nil
}

func (m *Mgr) refreshResourceCDNFile() error {
	ctx := context.Background()

	err := m.checkResourceUpdate()
	if err != nil {
		log.ErrorWithCtx(ctx, "refreshResourceCDNFile failed. err:%v", err)
		return err
	}

	lastTime, err := m.store.GetMaxUpdateTime(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReFreshLocalResourceCache failed. err:%v", err)
		return err
	}

	lastRecord, err := m.store.GetCDNFileRecord(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReFreshLocalResourceCache failed. err:%v", err)
		return err
	}

	if lastRecord.Version == lastTime {
		log.DebugWithCtx(ctx, "ReFreshLocalResourceCache not refresh, lastRecord.Version:%d, lastTime:%d", lastRecord.Version, lastTime)
		return nil
	}

	log.InfoWithCtx(ctx, "refreshResourceCDNFile success,latestVersionTs:%d, lastTime:%d,  ", m.latestVersionTs, lastTime)

	fileName, err := m.genResourceZipFile(ctx, SafeInt64ToUint32(lastTime))
	if err != nil {
		log.ErrorWithCtx(ctx, "genResourceZipFile failed. err:%v", err)
		return err
	}

	err = m.uploadResource(ctx, fileName, SafeInt64ToUint32(lastTime))
	if err != nil {
		log.ErrorWithCtx(ctx, "uploadResource failed. err:%v", err)
		return err
	}
	return nil
}

func (m *Mgr) genResourceZipFile(ctx context.Context, version uint32) (string, error) {
	resourceLogic := &virtual_image_logic.GetResourceListResponse{
		BaseResp: &app.BaseResp{
			ErrMsg: "成功",
		},
		LatestVersion: version,
	}

	m.mutex.RLock()
	defer m.mutex.RUnlock()
	for _, data := range m.cacheList {
		iosResourceUrl := data.IosResourceURL
		if iosResourceUrl == "" {
			iosResourceUrl = data.ResourceURL
		}
		resourceLogic.Resources = append(resourceLogic.Resources, &virtual_image_logic.VirtualImageResourceInfo{
			Id:               data.ID,
			SkinName:         data.SkinName,
			ResourceType:     data.ResourceType,
			ResourceName:     data.ResourceName,
			ResourceUrl:      data.ResourceURL,
			Version:          data.Version,
			Essential:        data.Essential,
			EncryptKey:       data.EncryptKey,
			DisplayName:      data.DisplayName,
			IconUrl:          data.IconURL,
			Category:         data.Category,
			SubCategory:      data.SubCategory,
			Md5:              data.MD5,
			Level:            data.Level,
			LevelIcon:        data.LevelIcon,
			Sex:              data.Sex,
			LevelWebp:        data.LevelIcon,
			ScaleAble:        data.ScaleAble,
			DefaultAnimation: data.DefaultAnimation,
			ResourcePrefix:   data.ResourcePrefix,
			CustomMap:        utils.StringToCustomMap(data.CustomMap),
			SkinMap:          transLogicSkinMap(utils.StringToSkinMap(data.SkinMap)),
			IosSkinMap:       transLogicSkinMap(utils.StringToSkinMap(data.IosSkinMap)),
			IosResourceUrl:   iosResourceUrl,
			IosVersion:       data.IosVersion,
			IsNewResource:    data.IsNew,
			TextureUrlMap:    utils.StringToCustomMap(data.TextureUrlMap),
		})
	}

	// 写入ZIP文件
	zipFileName := fmt.Sprintf("resource_list_%d.zip", time.Now().Unix())
	zipFile, err := os.Create(zipFileName)
	if err != nil {
		fmt.Println("Error creating zip file:", err)
		return "", err
	}
	defer zipFile.Close()

	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()

	// 创建一个新的文件条目在ZIP文件中
	fileInZip, err := zipWriter.Create("resource_list")
	if err != nil {
		fmt.Println("Error creating file in zip:", err)
		return "", err
	}

	// 将资源列表的二进制数据写入ZIP文件
	resourceBytes, err := proto.Marshal(resourceLogic)
	if err != nil {
		fmt.Println("Error marshaling resource list:", err)
		return "", err
	}

	_, err = fileInZip.Write(resourceBytes)
	if err != nil {
		log.ErrorWithCtx(ctx, "Error writing to zip file:%v", err)
		return "", err
	}
	md5sum, _ := calculateFileMD5(zipFileName)
	log.InfoWithCtx(ctx, "uploadResource success, write zip writeSize:%d, version:%d, md5sum:%v", len(m.cacheList), version, md5sum)
	return zipFileName, nil
}

func (m *Mgr) uploadResource(ctx context.Context, fileName string, lastTime uint32) error {
	// 读取ZIP文件以验证写入
	reader, err := zip.OpenReader(fileName)
	if err != nil {
		log.ErrorWithCtx(ctx, "uploadResource err:%v", err)
		return err
	}

	defer reader.Close()

	// 上传资源
	resource, err := os.ReadFile(fileName)
	if err != nil {
		log.ErrorWithCtx(ctx, "uploadResource err:%v", err)
		return err
	}

	retKey, _, err := m.obsGatewayCli.Upload(ctx, appId, scope, resource, obsObjectGateway.WithKey(fileName), obsObjectGateway.WithContentType("application/x-zip-compressed"))
	if err != nil {
		log.ErrorWithCtx(ctx, "uploadResource err:%v", err)
		return err
	}
	// 上传成功，更新数据库
	md5sum, _ := calculateFileMD5(fileName)
	resourceUrl := fmt.Sprintf("%s%s", UpLoadPrefix, retKey)
	err = m.store.AddCDNFileRecord(ctx, resourceUrl, md5sum, int64(lastTime))
	if err != nil {
		log.Errorf("AddCDNFileRecord fail .  err:%v", err)
		return err
	}

	log.Infof("uploadImage success, resourceUrl: %s, md5sum:%s", resourceUrl, md5sum)
	_ = os.Remove(fileName)
	return nil

}

func (m *Mgr) getResourceCNDRecord() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	record, err := m.store.GetCDNFileRecord(ctx)
	if err != nil {
		log.Errorf("GetCDNFileRecord fail .  err:%v", err)
		return err
	}
	if record.ID == 0 {
		log.Errorf("GetCDNFileRecord no record")
		return nil
	}
	m.cdnResourceRecord = record
	log.Infof("GetCDNFileRecord success, resourceUrl: %s, md5sum:%s", record.Url, record.Md5)
	return nil
}

// 计算文件的MD5值
func calculateFileMD5(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	return fmt.Sprintf("%x", hash.Sum(nil)), nil
}

func transLogicSkinMap(skinMap map[string]*virtual_image_resource.SkinInfo) map[string]*virtual_image_logic.SkinInfo {
	out := make(map[string]*virtual_image_logic.SkinInfo)
	for k, v := range skinMap {
		out[k] = &virtual_image_logic.SkinInfo{
			Url:             v.GetUrl(),
			MinBonesVersion: v.GetMinBonesVersion(),
			Md5:             v.GetMd5(),
		}
	}
	return out
}
