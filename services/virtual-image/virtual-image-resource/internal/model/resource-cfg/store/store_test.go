package store

import (
	"context"
	"testing"
	
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
	
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupTestBaseStore(t *testing.T) (*Store, func()) {
	mysqlConfig := &mysqlConnect.MysqlConfig{
		Host:     "*************", // 根据你的测试环境修改
		Port:     3306,
		Database: "appsvr",
		Charset:  "utf8",
		UserName: "godman",
		Password: "thegodofman",
	}

	dbCli, err := mysqlConnect.NewClient(context.Background(), mysqlConfig)
	if err != nil {
		panic(err)
	}

	store := &Store{db: dbCli}

	// 清空相关表
	tables := []string{
		"virtual_image_resource_info",
		"virtual_image_resource_id",
	}
	for _, table := range tables {
		_, err = dbCli.Exec("DELETE FROM " + table)
		require.NoError(t, err)
	}

	return store, func() {
		dbCli.Close()
	}
}

func TestUpdateIconByID(t *testing.T) {
	store, teardown := setupTestBaseStore(t)
	defer teardown()

	// 插入初始数据
	_, err := store.db.Exec(`
		INSERT INTO virtual_image_resource_info 
		(id, skin_name, resource_name, icon_url, display_name) 
		VALUES (1, 'skin1', 'res1', 'old_icon.png', NULL)
	`)
	require.NoError(t, err)

	// 构造请求
	req := &virtual_image_resource.VirtualImageResourceInfo{
		Id:          1,
		IconUrl:     "new_icon.png",
		DisplayName: "New Name",
	}

	// 执行更新
	err = store.UpdateIconByID(context.Background(), req)
	assert.NoError(t, err)

	// 查询验证
	var iconURL, displayName string
	err = store.db.QueryRow("SELECT icon_url, display_name FROM virtual_image_resource_info WHERE id = 1").Scan(&iconURL, &displayName)
	require.NoError(t, err)

	assert.Equal(t, "new_icon.png", iconURL)
	assert.Equal(t, "New Name", displayName)
}

func TestSetVirtualImageResourceForSale(t *testing.T) {
	store, teardown := setupTestBaseStore(t)
	defer teardown()

	// 插入初始数据
	_, err := store.db.Exec(`
		INSERT INTO virtual_image_resource_info 
		(id, skin_name, resource_name) 
		VALUES (1, 'skin1', 'res1'), (2, 'skin2', 'res2')
	`)
	require.NoError(t, err)

	// 设置资源为商品
	idList := []uint32{1, 2}
	err = store.SetVirtualImageResourceForSale(context.Background(), idList)
	assert.NoError(t, err)

	// 查询验证
	var isCommodity bool
	err = store.db.QueryRow("SELECT is_commodity FROM virtual_image_resource_info WHERE id = 1").Scan(&isCommodity)
	require.NoError(t, err)
	assert.True(t, isCommodity)
}

func TestGetVirtualImageResourceIdByResourceName(t *testing.T) {
	store, teardown := setupTestBaseStore(t)
	defer teardown()

	// 插入资源名称记录
	res, err := store.db.Exec("INSERT INTO virtual_image_resource_id (resource_name) VALUES ('res1')")
	require.NoError(t, err)

	lastId, _ := res.LastInsertId()
	// 查询资源ID
	id, err := store.GetVirtualImageResourceIdByResourceName(context.Background(), "res1")
	assert.NoError(t, err)
	assert.Equal(t, uint32(lastId), id)

}

func TestInsertResourceIdRecord(t *testing.T) {
	store, teardown := setupTestBaseStore(t)
	defer teardown()

	// 插入资源名称记录
	id, err := store.InsertResourceIdRecord(context.Background(), 1, "res1")
	assert.NoError(t, err)
	assert.Equal(t, uint32(1), id)

	// 查询验证
	var name string
	err = store.db.QueryRow("SELECT resource_name FROM virtual_image_resource_id WHERE id = 1").Scan(&name)
	require.NoError(t, err)
	assert.Equal(t, "res1", name)
}

func TestUpdateVirtualImageResourceStatus(t *testing.T) {
	store, teardown := setupTestBaseStore(t)
	defer teardown()

	// 插入初始数据
	_, err := store.db.Exec(`
		INSERT INTO virtual_image_resource_info 
		(id, skin_name, resource_name) 
		VALUES (1, 'skin1', 'res1'), (2, 'skin2', 'res2')
	`)
	require.NoError(t, err)

	// 更新状态
	idList := []uint32{1, 2}
	err = store.UpdateVirtualImageResourceStatus(context.Background(), idList, 3)
	assert.NoError(t, err)

	// 查询验证
	var status uint32
	err = store.db.QueryRow("SELECT status FROM virtual_image_resource_info WHERE id = 1").Scan(&status)
	require.NoError(t, err)
	assert.Equal(t, uint32(3), status)
}

func TestAddAndroidVirtualImageResourceInfo(t *testing.T) {
	store, teardown := setupTestBaseStore(t)
	defer teardown()

	resource := &VirtualImageResourceInfo{
		ID:               1,
		SkinName:         "skin1",
		ResourceName:     "res1",
		Essential:        true,
		MD5:              "md5_1",
		ResourceType:     1,
		Category:         10,
		SubCategory:      100,
		DisplayName:      "name1",
		IconURL:          "icon1.png",
		Sex:              1,
		LevelIcon:        "level_icon1",
		Level:            1,
		Status:           1,
		DefaultSuit:      "suit1",
		Operator:         "admin",
		ScaleAble:        true,
		DefaultAnimation: "anim1",
		IsNew:            true,
		IosVersion:       1,
		TextureUrlMap:    `{"aa":"bb"}`,
	}

	err := store.AddAndroidVirtualImageResourceInfo(context.Background(), []*VirtualImageResourceInfo{resource})
	assert.NoError(t, err)
	
	
	var count int
	err = store.db.Get(&count, "SELECT COUNT(*) FROM virtual_image_resource_info")
	require.NoError(t, err)
	assert.Equal(t, 1, count)
}

func TestCloneVirtualImageResource(t *testing.T) {
	store, teardown := setupTestBaseStore(t)
	defer teardown()

	resource := &VirtualImageResourceInfo{
		ID:               1,
		SkinName:         "skin1",
		ResourceName:     "res1",
		ResourceURL:      "url1",
		Essential:        true,
		MD5:              "md5_1",
		Status:           1,
		ResourceType:     1,
		Category:         10,
		SubCategory:      100,
		SplineVersion:    "v1",
		DefaultSuit:      "suit1",
		DisplayName:      "name1",
		IconURL:          "icon1.png",
		Sex:              1,
		LevelIcon:        "level_icon1",
		Level:            1,
		ScaleAble:        true,
		DefaultAnimation: "anim1",
		ResourcePrefix:   "prefix1",
		CustomMap:        "{}",
		SkinMap:          "{}",
		IosSkinMap:       "{}",
		IosVersion:       1,
		IsNew:            true,
	}

	err := store.CloneVirtualImageResource(context.Background(), []*VirtualImageResourceInfo{resource})
	assert.NoError(t, err)

	var count int
	err = store.db.Get(&count, "SELECT COUNT(*) FROM virtual_image_resource_info WHERE id = 1")
	require.NoError(t, err)
	assert.Equal(t, 1, count)
}

func TestUpdateVirtualImageResource(t *testing.T) {
	store, teardown := setupTestBaseStore(t)
	defer teardown()

	// 插入初始数据
	_, err := store.db.Exec(`
		INSERT INTO virtual_image_resource_info 
		(id, skin_name, resource_name) VALUES (1, 'skin1', 'res1')
	`)
	require.NoError(t, err)

	req := &virtual_image_resource.VirtualImageResourceInfo{
		Id:          1,
		DisplayName: "Updated Name",
		ResourceUrl: "new_url",
		IconUrl:     "new_icon.png",
		Sex:         2,
		LevelIcon:   "new_level_icon",
		Level:       99,
		Status:      2,
		DefaultSuit: "new_suit",
		Version:     2,
		IosVersion:  2,
	}

	err = store.UpdateVirtualImageResource(context.Background(), req)
	assert.NoError(t, err)

	var name, icon string
	err = store.db.QueryRow("SELECT display_name, icon_url FROM virtual_image_resource_info WHERE id = 1").Scan(&name, &icon)
	require.NoError(t, err)

	assert.Equal(t, "Updated Name", name)
	assert.Equal(t, "new_icon.png", icon)
}

func TestDeleteVirtualImageResource(t *testing.T) {
	store, teardown := setupTestBaseStore(t)
	defer teardown()

	_, err := store.db.Exec("INSERT INTO virtual_image_resource_info (id, skin_name) VALUES (1, 'skin1')")
	require.NoError(t, err)

	err = store.DeleteVirtualImageResource(context.Background(), 1)
	assert.NoError(t, err)

	var count int
	err = store.db.Get(&count, "SELECT COUNT(*) FROM virtual_image_resource_info WHERE id = 1")
	require.NoError(t, err)
	assert.Equal(t, 0, count)
}

func TestUpdateVirtualImageResourceUrl(t *testing.T) {
	store, teardown := setupTestBaseStore(t)
	defer teardown()

	// 插入初始数据
	_, err := store.db.Exec(`
		INSERT INTO virtual_image_resource_info 
		(id, skin_name, resource_name, resource_url) 
		VALUES (1, 'skin1', 'res1', 'old_url')`)
	require.NoError(t, err)

	req := &virtual_image_resource.UpdateVirtualImageResourceUrlRequest{
		Resources: []*virtual_image_resource.VirtualImageResourceInfo{
			{
				ResourceName:     "res1",
				ResourceUrl:      "new_url",
				EncryptKey:       "key_new",
				Md5:              "new_md5",
				IosMd5:           "ios_new_md5",
				IosResourceUrl:   "ios_new_url",
				Version:          2,
				DisplayName:      "updated_display",
				Category:         20,
				SubCategory:      200,
				DefaultSuit:      "default_suit",
				Sex:              2,
				IconUrl:          "new_icon.png",
				Essential:        true,
				ScaleAble:        true,
				LevelIcon:        "new_level_icon",
				Level:            99,
				DefaultAnimation: "anim2",
				ResourcePrefix:   "prefix2",
				CustomMap:        map[string]string{"k1": "v1"},
				IosVersion:       2,
				IsNewResource:    true,
			},
		},
	}

	err = store.UpdateVirtualImageResourceUrl(context.Background(), req)
	assert.NoError(t, err)

	var url string
	err = store.db.QueryRow("SELECT resource_url FROM virtual_image_resource_info WHERE resource_name = 'res1'").Scan(&url)
	require.NoError(t, err)
	assert.Equal(t, "new_url", url)
}

func TestUpdateVirtualImageResourceCustomMap(t *testing.T) {
	store, teardown := setupTestBaseStore(t)
	defer teardown()

	// 插入初始数据
	_, err := store.db.Exec(`
		INSERT INTO virtual_image_resource_info 
		(id, skin_name, resource_name) VALUES (1, 'skin1', 'res1')`)
	require.NoError(t, err)

	info := &VirtualImageResourceInfo{
		ID:             1,
		CustomMap:      "custom_map_v2",
		IosMD5:         "ios_md5_v2",
		IosResourceURL: "ios_url_v2",
		IosSkinMap:     "ios_skin_map_v2",
		IosVersion:     2,
		ResourceName:   "res1",
	}

	err = store.UpdateVirtualImageResourceCustomMap(context.Background(), info)
	assert.NoError(t, err)

	var customMap string
	err = store.db.QueryRow("SELECT custom_map FROM virtual_image_resource_info WHERE id = 1").Scan(&customMap)
	require.NoError(t, err)
	assert.Equal(t, "custom_map_v2", customMap)
}
