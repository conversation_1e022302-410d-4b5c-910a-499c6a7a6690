package store

import (
	"context"
	"errors"
	"fmt"
	"github.com/jmoiron/sqlx"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"golang.52tt.com/pkg/log"
	virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
	interUtils "golang.52tt.com/services/virtual-image/virtual-image-resource/internal/utils"

	"math"
	"strings"
	"time"
)

const (
	tblParentCategoryInfo = "tbl_parent_category_info" //父级分类表
	tblSubCategoryInfo    = "tbl_sub_category_info"    //子级分类表
)

//go:generate quicksilver-cli test interface ../store
//go:generate mockgen -destination=../mocks/store.go -package=mocks golang.52tt.com/services/virtual-image/virtual-image-resource/internal/model/resource-cfg/store IStore

type Store struct {
	db mysql.DBx
}

func NewStore(db mysql.DBx) *Store {
	s := &Store{
		db: db,
	}

	s.createTable(context.Background())
	return s
}

func (s *Store) Close() error {
	return s.db.Close()
}

// VirtualImageResourceInfoID 表示虚拟形像资源信息
type VirtualImageResourceInfoID struct {
	ID           uint32 `db:"id"`            // 资源ID[自增ID]
	ResourceName string `db:"resource_name"` // 资源名称
}

// ALTER TABLE virtual_image_resource_info ADD is_new tinyint NOT NULL DEFAULT 0 COMMENT '是否新资源';
// ALTER TABLE virtual_image_resource_info ADD ios_version int(10) unsigned NOT NULL DEFAULT 1 COMMENT 'ios版本号';
// VirtualImageResourceInfo 表示虚拟形像资源信息
type VirtualImageResourceInfo struct {
	ID               uint32    `db:"id"`                // 资源ID[自增ID]
	SkinName         string    `db:"skin_name"`         // 皮肤名称[资源唯一标识]
	ResourceName     string    `db:"resource_name"`     // 资源名称
	ResourceURL      string    `db:"resource_url"`      // 资源地址 OBS
	Essential        bool      `db:"essential"`         // 是否必须的
	EncryptKey       string    `db:"encrypt_key"`       // 加密key
	MD5              string    `db:"md5"`               // md5
	ResourceType     uint32    `db:"resource_type"`     // 资源类型 see VirtualImageResourceType
	Category         uint32    `db:"category"`          // 资源品类 see VirtualImageResourceCategory
	SubCategory      uint32    `db:"sub_category"`      // 资源子分类
	SplineVersion    string    `db:"spline_version"`    // spline版本号
	DisplayName      string    `db:"display_name"`      // 显示名称 [运营录入的名称]
	IconURL          string    `db:"icon_url"`          // 图标地址
	Sex              uint32    `db:"sex"`               // 性别  1: 男  2: 女 3: 通用
	LevelIcon        string    `db:"level_icon"`        // 等级图标
	Level            uint32    `db:"level"`             // 等级
	Status           uint32    `db:"status"`            // 状态 see VirtualImageResourceStatus
	Version          uint32    `db:"version"`           // 版本号 有更新+1
	DefaultSuit      string    `db:"default_suit"`      // 默认套装编码
	Operator         string    `db:"operator"`          // 操作人
	CreateTime       time.Time `db:"create_time"`       // 创建时间
	UpdateTime       time.Time `db:"update_time"`       // 更新时间[资源版本号]
	IsCommodity      bool      `db:"is_commodity"`      // 是否已配置过商品
	IsDeleted        bool      `db:"is_deleted"`        // 是否已删除
	IosMD5           string    `db:"ios_md5"`           // md5
	IosResourceURL   string    `db:"ios_resource_url"`  // 资源地址 OBS
	ScaleAble        bool      `db:"scale_able"`        // 是否支持缩放
	DefaultAnimation string    `db:"default_animation"` // 默认动作
	ResourcePrefix   string    `db:"resource_prefix"`   // 资源前缀
	Statement        string    `db:"statement"`         // 资源描述  已弃用
	CustomMap        string    `db:"custom_map"`        // ios自定义字段
	SkinMap          string    `db:"skin_map"`          // 按照骨骼名称作为key区分皮肤资源信息
	IosSkinMap       string    `db:"ios_skin_map"`      // 按照骨骼名称作为key区分皮肤资源信息
	IsNew            bool      `db:"is_new"`            // 是否支持缩放
	IosVersion       uint32    `db:"ios_version"`       // ios版本号
	TextureUrlMap    string    `db:"texture_url_map"`   // 按照骨骼名称作为key区分贴图资源信息
}

func (s *Store) createTable(ctx context.Context) {
	createTableSQL := `
	CREATE TABLE IF NOT EXISTS virtual_image_resource_info (
		id INT AUTO_INCREMENT PRIMARY KEY,             -- 资源ID[自增ID]
		skin_name VARCHAR(255) NOT NULL,               -- 资源路径[资源唯一标识]
		resource_name VARCHAR(255) NOT NULL,           -- 资源名称
		resource_url VARCHAR(255),                     -- 资源地址 OBS
		ios_resource_url VARCHAR(255),                 -- ios资源地址 OBS
		essential BOOLEAN,                             -- 是否必须的
		encrypt_key VARCHAR(1024),                      -- 加密key
		md5 VARCHAR(32),                               -- md5
		ios_md5 VARCHAR(32),                               -- md5
		resource_type INT,                             -- 资源类型 see VirtualImageResourceType
		category INT,                                  -- 资源品类 see VirtualImageResourceCategory
		sub_category INT,                              -- 资源子分类
		spline_version VARCHAR(50),                    -- spline版本号
		display_name VARCHAR(255),                     -- 显示名称 [运营录入的名称]
		icon_url VARCHAR(255),                         -- 图标地址
		sex INT DEFAULT 0,                             -- 性别 0: 通用 1: 男  2: 女 
		level_icon VARCHAR(255),                       -- 等级图标
		level INT DEFAULT 1,                           -- 等级
		status INT,                                    -- 状态 see VirtualImageResourceStatus
		version INT DEFAULT 1,                         -- 版本号 有更新+1
		default_suit VARCHAR(255),                     -- 默认套装编码
		operator VARCHAR(255),                         -- 操作人
		create_time DATETIME DEFAULT CURRENT_TIMESTAMP,-- 创建时间
		update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,                               -- 更新时间[资源版本号]
		is_commodity BOOLEAN,                           -- 是否已配置过商品
		scale_able BOOLEAN,                             -- 是否支持缩放
		UNIQUE KEY uniq_skin_name (skin_name),
		INDEX idx_category (category, sub_category),
		INDEX idx_update_time (update_time)
	);`

	_, err := s.db.Exec(createTableSQL)
	if err != nil {
		fmt.Println("Error creating table:", err)
		return
	}

	createTableSQL = `
	CREATE TABLE IF NOT EXISTS virtual_image_resource_id (
		id INT AUTO_INCREMENT PRIMARY KEY,             -- 资源ID[自增ID]
		resource_name VARCHAR(255) NOT NULL,           -- 资源名称
		UNIQUE KEY resource_name (resource_name)
	);`
	_, err = s.db.Exec(createTableSQL)
	if err != nil {
		fmt.Println("Error creating table:", err)
		return
	}
}

// AddAndroidVirtualImageResourceInfo 批量安卓资源添加虚拟形像资源信息
func (s *Store) AddAndroidVirtualImageResourceInfo(ctx context.Context, list []*VirtualImageResourceInfo) error {
	if len(list) == 0 {
		return nil
	}

	valueStrings := make([]string, 0)
	valueArgs := make([]interface{}, 0)
	for _, u := range list {
		if u.ID == 0 {
			log.ErrorWithCtx(ctx, "AddVirtualImageResourceInfo id is not null, list:%+v", list)
			return errors.New("id is not null")
		}

		valueStrings = append(valueStrings, "(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)")
		valueArgs = append(valueArgs, u.ID)
		valueArgs = append(valueArgs, u.SkinName)
		valueArgs = append(valueArgs, u.ResourceName)
		valueArgs = append(valueArgs, u.Essential)
		valueArgs = append(valueArgs, u.EncryptKey)
		valueArgs = append(valueArgs, u.ResourceType)
		valueArgs = append(valueArgs, u.Category)
		valueArgs = append(valueArgs, u.SubCategory)
		valueArgs = append(valueArgs, u.SplineVersion)
		valueArgs = append(valueArgs, u.Version)
		valueArgs = append(valueArgs, u.DefaultSuit)
		valueArgs = append(valueArgs, u.Sex)
		valueArgs = append(valueArgs, u.IconURL)
		valueArgs = append(valueArgs, u.MD5)
		valueArgs = append(valueArgs, u.ResourceURL)
		valueArgs = append(valueArgs, u.IosMD5)
		valueArgs = append(valueArgs, u.IosResourceURL)
		valueArgs = append(valueArgs, u.ScaleAble)
		valueArgs = append(valueArgs, u.DefaultAnimation)
		valueArgs = append(valueArgs, u.Level)
		valueArgs = append(valueArgs, u.IsDeleted)
		valueArgs = append(valueArgs, u.ResourcePrefix)
		valueArgs = append(valueArgs, u.CustomMap)
		valueArgs = append(valueArgs, u.SkinMap)
		valueArgs = append(valueArgs, u.IsNew)
		valueArgs = append(valueArgs, u.Status)
		valueArgs = append(valueArgs, u.TextureUrlMap)
	}

	// 自行拼接要执行的具体语句
	query := fmt.Sprintf("INSERT INTO virtual_image_resource_info"+
		"(id, skin_name, resource_name, essential, encrypt_key, resource_type, category, sub_category, "+
		"spline_version, version, default_suit, sex, icon_url,md5, resource_url,ios_md5,ios_resource_url,"+
		" scale_able, default_animation, level, is_deleted, resource_prefix,custom_map, skin_map, is_new, status, texture_url_map) VALUES %s "+
		"ON DUPLICATE KEY UPDATE resource_name=VALUES(resource_name),"+
		"essential=VALUES(essential), encrypt_key=VALUES(encrypt_key), "+
		"resource_type=VALUES(resource_type), category=VALUES(category), sub_category=VALUES(sub_category), "+
		"spline_version=VALUES(spline_version),version = VALUES(version), default_suit=VALUES(default_suit),sex=VALUES(sex),"+
		"icon_url=CASE WHEN VALUES(icon_url) != '' THEN VALUES(icon_url) ELSE icon_url END,"+
		"md5=CASE WHEN VALUES(md5) != '' THEN VALUES(md5) ELSE md5 END,"+
		"resource_url=CASE WHEN VALUES(resource_url) != '' THEN VALUES(resource_url) ELSE resource_url END,"+
		"ios_md5=CASE WHEN VALUES(ios_md5) != '' THEN VALUES(ios_md5) ELSE ios_md5 END,"+
		"ios_resource_url=CASE WHEN VALUES(ios_resource_url) != '' THEN VALUES(ios_resource_url) ELSE ios_resource_url END,"+
		"scale_able=VALUES(scale_able),default_animation=VALUES(default_animation),"+
		"level=CASE WHEN VALUES(level) != 1 THEN VALUES(level) ELSE level END, is_deleted=VALUES(is_deleted), resource_prefix=VALUES(resource_prefix),"+
		"custom_map=CASE WHEN VALUES(custom_map) != '' THEN VALUES(custom_map) ELSE custom_map END, skin_map=VALUES(skin_map), is_new=VALUES(is_new),"+
		"status=CASE WHEN VALUES(status) != 0 THEN VALUES(status) ELSE status END, texture_url_map=VALUES(texture_url_map)",
		strings.Join(valueStrings, ","))
	_, err := s.db.ExecContext(ctx, query, valueArgs...)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddVirtualImageResourceInfo error: %v", err)
	}
	return err
}

// CloneVirtualImageResource 批量添加虚拟形像资源信息
func (s *Store) CloneVirtualImageResource(ctx context.Context, list []*VirtualImageResourceInfo) error {
	if len(list) == 0 {
		return nil
	}

	valueStrings := make([]string, 0, len(list))
	valueArgs := make([]interface{}, 0, len(list)*2)
	for _, u := range list {
		if u.ID == 0 {
			log.ErrorWithCtx(ctx, "CloneVirtualImageResource id is not null, list:%+v", list)
			return errors.New("id is not null")
		}

		valueStrings = append(valueStrings, "(?, ?, ?,  ?, ?, ?, ?,  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")
		valueArgs = append(valueArgs, u.ID)
		valueArgs = append(valueArgs, u.SkinName)
		valueArgs = append(valueArgs, u.ResourceName)
		valueArgs = append(valueArgs, u.ResourceURL)
		valueArgs = append(valueArgs, u.Essential)
		valueArgs = append(valueArgs, u.EncryptKey)
		valueArgs = append(valueArgs, u.MD5)
		valueArgs = append(valueArgs, u.Status)
		valueArgs = append(valueArgs, u.ResourceType)
		valueArgs = append(valueArgs, u.Category)
		valueArgs = append(valueArgs, u.SubCategory)
		valueArgs = append(valueArgs, u.SplineVersion)
		valueArgs = append(valueArgs, u.DefaultSuit)
		valueArgs = append(valueArgs, u.Version)
		valueArgs = append(valueArgs, u.DisplayName)
		valueArgs = append(valueArgs, u.IconURL)
		valueArgs = append(valueArgs, u.Sex)
		valueArgs = append(valueArgs, u.Level)
		valueArgs = append(valueArgs, u.LevelIcon)
		valueArgs = append(valueArgs, u.IosMD5)
		valueArgs = append(valueArgs, u.IosResourceURL)
		valueArgs = append(valueArgs, u.ScaleAble)
		valueArgs = append(valueArgs, u.DefaultAnimation)
		valueArgs = append(valueArgs, u.ResourcePrefix)
		valueArgs = append(valueArgs, u.CustomMap)
		valueArgs = append(valueArgs, u.SkinMap)
		valueArgs = append(valueArgs, u.IosSkinMap)
		valueArgs = append(valueArgs, u.IosVersion)
		valueArgs = append(valueArgs, u.IsNew)
		valueArgs = append(valueArgs, u.TextureUrlMap)
	}

	// 自行拼接要执行的具体语句
	query := fmt.Sprintf("INSERT INTO virtual_image_resource_info "+
		"(id, skin_name, resource_name, resource_url, essential, encrypt_key, md5, status, resource_type, category, sub_category, "+
		"spline_version, default_suit, version, display_name, icon_url, sex, level, level_icon, ios_md5,"+
		" ios_resource_url, scale_able, default_animation, resource_prefix,custom_map, skin_map, ios_skin_map, ios_version, is_new, texture_url_map) VALUES %s",
		strings.Join(valueStrings, ","))
	_, err := s.db.ExecContext(ctx, query, valueArgs...)
	if err != nil {
		log.ErrorWithCtx(ctx, "CloneVirtualImageResource error: %v", err)
	}
	return err
}

// SearchVirtualImageResource 查询虚拟形像资源信息条数
func (s *Store) SearchVirtualImageResourceCount(ctx context.Context, request *virtual_image_resource.SearchVirtualImageResourceRequest) (uint32, error) {
	// 构建查询语句
	var query strings.Builder
	selectAllSQL := `SELECT  count(1) FROM virtual_image_resource_info WHERE 1=1`
	query.WriteString(selectAllSQL)

	// 添加筛选条件
	params := []interface{}{}
	if len(request.GetId()) > 0 {
		query.WriteString(" AND (")
		for i, id := range request.GetId() {
			if i > 0 {
				query.WriteString(" OR ")
			}
			query.WriteString("id LIKE ?")
			params = append(params, fmt.Sprintf("%%%d%%", id))
		}
		query.WriteString(")")
	}

	if len(request.GetName()) > 0 {
		query.WriteString(" AND (")
		for i, name := range request.GetName() {
			if i > 0 {
				query.WriteString(" OR ")
			}
			query.WriteString("display_name LIKE ?")
			params = append(params, fmt.Sprintf("%%%s%%", name))
		}
		query.WriteString(")")
	}

	if request.GetCategory() > 0 {
		query.WriteString(" AND category LIKE ?")
		params = append(params, fmt.Sprintf("%%%d%%", request.GetCategory()))
	}

	if request.GetSubCategory() > 0 {
		query.WriteString(" AND sub_category LIKE ?")
		params = append(params, fmt.Sprintf("%%%d%%", request.GetSubCategory()))
	}

	if request.GetSex() > 0 {
		query.WriteString(" AND sex = ?")
		params = append(params, request.GetSex())
	}

	if request.GetLevel() > 0 {
		query.WriteString(" AND level = ?")
		params = append(params, request.GetLevel())
	}

	if len(request.GetSuit()) > 0 {
		query.WriteString(" AND default_suit LIKE ? ")
		params = append(params, fmt.Sprintf("%%%s%%", request.GetSuit()))
	}

	if request.GetCommodityFilter() == uint32(virtual_image_resource.VirtualImageResourceCommodityFilter_VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_COMMODITY) {
		query.WriteString(" AND is_commodity IS NOT NULL")
	} else if request.GetCommodityFilter() == uint32(virtual_image_resource.VirtualImageResourceCommodityFilter_VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_NOT_COMMODITY) {
		query.WriteString(" AND is_commodity IS NULL")
	} else {
		log.DebugWithCtx(ctx, "SearchVirtualImageResource: commodityFilter:%d", request.GetCommodityFilter())
	}

	if len(request.GetSkinName()) > 0 {
		query.WriteString(" AND skin_name LIKE ?")
		params = append(params, fmt.Sprintf("%%%s%%", request.GetSkinName()))
	}

	if request.GetIsEssential() == 1 {
		query.WriteString(" AND essential = 1")
	} else if request.GetIsEssential() == 2 {
		query.WriteString(" AND essential = 0")
	} else {
		log.DebugWithCtx(ctx, "SearchVirtualImageResource: isEssential:%d", request.GetIsEssential())
	}

	if len(request.GetResourceType()) > 0 {
		query.WriteString(" AND (")
		for i, id := range request.GetResourceType() {
			if i > 0 {
				query.WriteString(" OR ")
			}
			query.WriteString("resource_type = ?")
			params = append(params, id)
		}
		query.WriteString(")")
	}

	if len(request.GetSkinNameList()) > 0 {
		query.WriteString(" AND skin_name IN (")
		for i, skinName := range request.GetSkinNameList() {
			if i > 0 {
				query.WriteString(",")
			}
			query.WriteString(" ? ")
			params = append(params, skinName)
		}
		query.WriteString(")")
	}

	if len(request.GetResourceNameList()) > 0 {
		query.WriteString(" AND resource_name IN (")
		for i, skinName := range request.GetResourceNameList() {
			if i > 0 {
				query.WriteString(",")
			}
			query.WriteString(" ? ")
			params = append(params, skinName)
		}
		query.WriteString(")")
	}
	log.DebugWithCtx(ctx, "SearchVirtualImageResource: %s", query.String())
	query.WriteString(" AND is_deleted = 0")

	// 执行查询
	var count uint32
	err := s.db.GetContext(ctx, &count, query.String(), params...)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchVirtualImageResource error: %v", err)
		return count, err
	}
	return count, nil

}

// SearchVirtualImageResource 查询虚拟形像资源信息 接口没有做分页，需要注意，内存中分页
func (s *Store) SearchVirtualImageResource(ctx context.Context, request *virtual_image_resource.SearchVirtualImageResourceRequest) ([]VirtualImageResourceInfo, error) {
	// 构建查询语句
	var query strings.Builder
	selectAllSQL := `
	SELECT 
		id,
		skin_name,
		resource_name,
		resource_url,
		essential,
		encrypt_key,
		md5,
		resource_type,
		category,
		sub_category,
		spline_version,
		IFNULL(display_name,''),
		IFNULL(icon_url,''),
		IFNULL(sex, 0),
		IFNULL(level_icon,''),
		IFNULL(level,0),
		status,
		IFNULL(default_suit,''),
		IFNULL(operator,''),
		create_time,
		update_time,
		IFNULL(is_commodity, FALSE),
		IFNULL(scale_able, FALSE),
		IFNULL(default_animation,''),
		version,
		IFNULL(ios_md5,''),
		IFNULL(ios_resource_url,''),
		IFNULL(is_deleted, FALSE),
		IFNULL(resource_prefix,''),
		IFNULL(custom_map,''),
		IFNULL(skin_map,''),
		IFNULL(ios_skin_map,''),
		ios_version,
		IFNULL(is_new, FALSE),
		IFNULL(texture_url_map,'')
		
	FROM 
		virtual_image_resource_info WHERE 1=1
`
	query.WriteString(selectAllSQL)

	// 添加筛选条件
	params := []interface{}{}
	if len(request.GetId()) > 0 {
		query.WriteString(" AND (")
		for i, id := range request.GetId() {
			if i > 0 {
				query.WriteString(" OR ")
			}
			query.WriteString("id LIKE ?")
			params = append(params, fmt.Sprintf("%%%d%%", id))
		}
		query.WriteString(")")
	}

	if len(request.GetName()) > 0 {
		query.WriteString(" AND (")
		for i, name := range request.GetName() {
			if i > 0 {
				query.WriteString(" OR ")
			}
			query.WriteString("display_name LIKE ?")
			params = append(params, fmt.Sprintf("%%%s%%", name))
		}
		query.WriteString(")")
	}

	if request.GetCategory() > 0 {
		query.WriteString(" AND category LIKE ?")
		params = append(params, fmt.Sprintf("%%%d%%", request.GetCategory()))
	}

	if request.GetSubCategory() > 0 {
		query.WriteString(" AND sub_category LIKE ?")
		params = append(params, fmt.Sprintf("%%%d%%", request.GetSubCategory()))
	}

	if request.GetSex() > 0 {
		query.WriteString(" AND sex = ?")
		params = append(params, request.GetSex())
	}

	if request.GetLevel() > 0 {
		query.WriteString(" AND level = ?")
		params = append(params, request.GetLevel())
	}

	if len(request.GetSuit()) > 0 {
		query.WriteString(" AND default_suit LIKE ? ")
		params = append(params, fmt.Sprintf("%%%s%%", request.GetSuit()))
	}

	if request.GetCommodityFilter() == uint32(virtual_image_resource.VirtualImageResourceCommodityFilter_VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_COMMODITY) {
		query.WriteString(" AND is_commodity IS NOT NULL")
	} else if request.GetCommodityFilter() == uint32(virtual_image_resource.VirtualImageResourceCommodityFilter_VIRTUAL_IMAGE_RESOURCE_COMMODITY_FILTER_NOT_COMMODITY) {
		query.WriteString(" AND is_commodity IS NULL")
	} else {
		log.DebugWithCtx(ctx, "SearchVirtualImageResource: commodityFilter:%d", request.GetCommodityFilter())
	}

	if len(request.GetSkinName()) > 0 {
		query.WriteString(" AND skin_name LIKE ?")
		params = append(params, fmt.Sprintf("%%%s%%", request.GetSkinName()))
	}

	if request.GetIsEssential() == 1 {
		query.WriteString(" AND essential = 1")
	} else if request.GetIsEssential() == 2 {
		query.WriteString(" AND essential = 0")
	} else {
		log.DebugWithCtx(ctx, "SearchVirtualImageResource: isEssential:%d", request.GetIsEssential())
	}

	if len(request.GetResourceType()) > 0 {
		query.WriteString(" AND (")
		for i, id := range request.GetResourceType() {
			if i > 0 {
				query.WriteString(" OR ")
			}
			query.WriteString("resource_type = ?")
			params = append(params, id)
		}
		query.WriteString(")")
	}

	if len(request.GetSkinNameList()) > 0 {
		query.WriteString(" AND skin_name IN (")
		for i, skinName := range request.GetSkinNameList() {
			if i > 0 {
				query.WriteString(",")
			}
			query.WriteString(" ? ")
			params = append(params, skinName)
		}
		query.WriteString(")")
	}

	if len(request.GetResourceNameList()) > 0 {
		query.WriteString(" AND resource_name IN (")
		for i, skinName := range request.GetResourceNameList() {
			if i > 0 {
				query.WriteString(",")
			}
			query.WriteString(" ? ")
			params = append(params, skinName)
		}
		query.WriteString(")")
	}

	query.WriteString(" AND is_deleted = 0")

	query.WriteString(" ORDER BY update_time DESC, id DESC ")

	// 添加分页条件
	if request.GetLimit() > 0 {
		query.WriteString(" LIMIT ?, ?")
		params = append(params, request.GetOffset(), request.GetLimit())
	}

	// 执行查询
	rows, err := s.db.Query(query.String(), params...)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchVirtualImageResource error: %v", err)
		return nil, err
	}
	defer rows.Close()

	// 解析结果
	var resources []VirtualImageResourceInfo
	for rows.Next() {
		var resource VirtualImageResourceInfo
		if err := rows.Scan(
			&resource.ID,
			&resource.SkinName,
			&resource.ResourceName,
			&resource.ResourceURL,
			&resource.Essential,
			&resource.EncryptKey,
			&resource.MD5,
			&resource.ResourceType,
			&resource.Category,
			&resource.SubCategory,
			&resource.SplineVersion,
			&resource.DisplayName,
			&resource.IconURL,
			&resource.Sex,
			&resource.LevelIcon,
			&resource.Level,
			&resource.Status,
			&resource.DefaultSuit,
			&resource.Operator,
			&resource.CreateTime,
			&resource.UpdateTime,
			&resource.IsCommodity,
			&resource.ScaleAble,
			&resource.DefaultAnimation,
			&resource.Version,
			&resource.IosMD5,
			&resource.IosResourceURL,
			&resource.IsDeleted,
			&resource.ResourcePrefix,
			&resource.CustomMap,
			&resource.SkinMap,
			&resource.IosSkinMap,
			&resource.IosVersion,
			&resource.IsNew,
			&resource.TextureUrlMap,
		); err != nil {
			return nil, err
		}
		resources = append(resources, resource)
	}

	return resources, nil
}

func (s *Store) UpdateVirtualImageResource(ctx context.Context, request *virtual_image_resource.VirtualImageResourceInfo) error {
	// 构建更新SQL语句
	query := `
		UPDATE virtual_image_resource_info
		SET
			display_name = ?,
			resource_url = ?,
			icon_url = ?,
			sex = ?,
			level_icon = ?,
			level = ?,
			status = ?,
			default_suit = ?,
			version = ?,
			ios_version = ?
		WHERE id = ?
	`

	// 执行更新操作
	_, err := s.db.ExecContext(ctx, query,
		request.GetDisplayName(),
		request.GetResourceUrl(),
		request.GetIconUrl(),
		request.GetSex(),
		request.GetLevelIcon(),
		request.GetLevel(),
		request.GetStatus(),
		request.GetDefaultSuit(),
		request.GetVersion()+1,
		request.GetIosVersion()+1,
		request.GetId(),
	)

	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateVirtualImageResource: %w", err)
		return err
	}
	return nil
}

func (s *Store) GetMaxUpdateTime(ctx context.Context) (int64, error) {
	query := `SELECT MAX(update_time) FROM virtual_image_resource_info`
	var maxUpdateTime time.Time
	err := s.db.QueryRow(query).Scan(&maxUpdateTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMaxUpdateTime: %w", err)
		return 0, err
	}
	return maxUpdateTime.Unix(), nil
}

func (s *Store) GetMaxVersion(ctx context.Context) (uint32, error) {
	query := `SELECT MAX(version) FROM virtual_image_resource_info`
	var maxVersion uint32
	err := s.db.QueryRow(query).Scan(&maxVersion)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMaxUpdateTime: %w", err)
		return 0, err
	}
	return maxVersion, nil
}

// DeleteVirtualImageResource 根据ID删除虚拟形象资源
func (s *Store) DeleteVirtualImageResource(ctx context.Context, id uint32) error {
	if id == 0 {
		log.ErrorWithCtx(ctx, "DeleteVirtualImageResource: id is 0")
		return nil
	}
	query := `DELETE FROM virtual_image_resource_info WHERE id = ?`
	_, err := s.db.ExecContext(ctx, query, id)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteVirtualImageResource: %w", err)
		return err
	}
	log.InfoWithCtx(ctx, "DeleteVirtualImageResource: id = %d", id)
	return nil
}

func (s *Store) UpdateVirtualImageResourceUrl(ctx context.Context, request *virtual_image_resource.UpdateVirtualImageResourceUrlRequest) error {

	log.InfoWithCtx(ctx, "UpdateVirtualImageResourceUrl :%v, ", request)

	for _, resource := range request.GetResources() {
		query := `
		UPDATE virtual_image_resource_info
		SET
			resource_url = ?,
			encrypt_key = ?,
			md5 = ?,
			ios_md5 = ?,
			ios_resource_url = ?,
		    version = ?,
		    display_name = ?,
		    category = ?,
		    sub_category = ?,
		    default_suit = ?,
		    sex = ?,
		    icon_url = ?,
		    essential = ?,
		    scale_able = ?,
		    level_icon = ?,
			level = ?,
			default_animation = ?,
			resource_prefix = ?,
			custom_map = ?,
			skin_map = ?,
			ios_skin_map = ?,
		    ios_version = ?,
		    is_new = ?,
		    texture_url_map = ?
		WHERE resource_name = ? 
	`
		result, err := s.db.Exec(query,
			resource.GetResourceUrl(),
			resource.GetEncryptKey(),
			resource.GetMd5(),
			resource.GetIosMd5(),
			resource.GetIosResourceUrl(),
			resource.GetVersion(),
			resource.GetDisplayName(),
			resource.GetCategory(),
			resource.GetSubCategory(),
			resource.GetDefaultSuit(),
			resource.GetSex(),
			resource.GetIconUrl(),
			resource.GetEssential(),
			resource.GetScaleAble(),
			resource.GetLevelIcon(),
			resource.GetLevel(),
			resource.GetDefaultAnimation(),
			resource.GetResourcePrefix(),
			interUtils.CustomMapToString(resource.GetCustomMap()),
			interUtils.SkinMapToString(resource.GetSkinMap()),
			interUtils.SkinMapToString(resource.GetIosSkinMap()),
			resource.GetIosVersion(),
			resource.GetIsNewResource(),
			interUtils.CustomMapToString(resource.GetTextureUrlMap()),

			resource.GetResourceName(),
		)

		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateVirtualImageResourceUrl: %w, query :%s", err, query)
			return err
		}

		// 检查更新行数
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to get rows affected: %w", err)
			return fmt.Errorf("failed to get rows affected: %w", err)
		}

		if rowsAffected == 0 {
			log.WarnWithCtx(ctx, "No rows updated for skin name %v", request)
			return fmt.Errorf("no rows updated for skin name %v", request)
		}
	}
	return nil
}

func (s *Store) SetVirtualImageResourceForSale(ctx context.Context, idList []uint32) error {
	log.InfoWithCtx(ctx, "SetVirtualImageResourceForSale :%v", idList)
	if len(idList) == 0 {
		log.WarnWithCtx(ctx, "SetVirtualImageResourceForSale idList is empty")
		return nil
	}

	query, args, err := sqlx.In(`UPDATE virtual_image_resource_info SET is_commodity = true WHERE id IN (?)`, idList)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetVirtualImageResourceForSale: %w", err)
		return err
	}

	result, err := s.db.ExecContext(ctx, query, args...)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetVirtualImageResourceForSale: %w", err)
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.ErrorWithCtx(ctx, "SetVirtualImageResourceForSale Failed to get rows affected: %w", err)
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	log.InfoWithCtx(ctx, "SetVirtualImageResourceForSale rows affected: %d", rowsAffected)
	return nil
}

// 根据  id  更新 icon_url， 如果原来记录display_name为空则更新，否则不更新
func (s *Store) UpdateIconByID(ctx context.Context, request *virtual_image_resource.VirtualImageResourceInfo) error {
	// Prepare the SQL statement
	sql := `
        UPDATE virtual_image_resource_info 
        SET icon_url = ?, 
            display_name = CASE 
                WHEN display_name IS NULL THEN ? 
                ELSE display_name 
            END
        WHERE id = ?
    `

	// Execute the SQL statement
	_, err := s.db.ExecContext(ctx, sql, request.IconUrl, request.DisplayName, request.Id)
	if err != nil {
		log.ErrorWithCtx(ctx, "Error executing update: %v", err)
		return err
	}
	log.InfoWithCtx(ctx, "UpdateIconByID: %v", request)
	return nil
}

// GetVirtualImageResourceInfoCount 获取虚拟形象资源总数
func (s *Store) GetVirtualImageResourceInfoCount(ctx context.Context) (int64, error) {
	var count int64
	query := `SELECT COUNT(*) FROM virtual_image_resource_info`
	err := s.db.QueryRow(query).Scan(&count)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetVirtualImageResourceInfoCount: %w", err)
		return 0, err
	}
	return count, nil
}

func (s *Store) UpdateVirtualImageResourceCustomMap(ctx context.Context, info *VirtualImageResourceInfo) error {
	// 构建更新SQL语句
	query := `
		UPDATE virtual_image_resource_info
		SET
		   	custom_map = ?,
		   	ios_md5 = ?,
		   	ios_resource_url = ?,
		   	ios_skin_map = ?,
		   	ios_version = ?,
		   	texture_url_map = ?
		
		WHERE resource_name = ?
	`

	// 执行更新操作
	_, err := s.db.ExecContext(ctx, query,
		info.CustomMap,
		info.IosMD5,
		info.IosResourceURL,
		info.IosSkinMap,
		info.IosVersion,
		info.TextureUrlMap,
		info.ResourceName,
	)

	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateVirtualImageResourceCustomMap: %w", err)
		return err
	}
	return nil
}

// GetVirtualImageResourceIdByResourceName 根据资源名称获取资源ID
func (s *Store) GetVirtualImageResourceIdByResourceName(ctx context.Context, name string) (uint32, error) {
	query := `SELECT id FROM virtual_image_resource_id WHERE resource_name = ?`
	var id uint32
	err := s.db.GetContext(ctx, &id, query, name)
	if err != nil {
		if mysql.IsNoRowsError(err) {
			log.WarnWithCtx(ctx, "GetVirtualImageResourceIdByResourceName: no rows found name:%s", name)
			return 0, nil
		}
		log.ErrorWithCtx(ctx, "GetVirtualImageResourceIdByResourceName name:%s,  err:%w", name, err)
		return 0, err
	}
	return id, nil
}

func (s *Store) InsertResourceIdRecord(ctx context.Context, id uint32, name string) (uint32, error) {
	var query string
	if id > 0 {
		query = `INSERT INTO virtual_image_resource_id (id, resource_name) VALUES (?, ?)`
		_, err := s.db.ExecContext(ctx, query, id, name)
		if err != nil {
			log.ErrorWithCtx(ctx, "InsertResourceIdRecord: %w", err)
			return 0, err
		}
		return id, nil
	} else {
		query = `INSERT INTO virtual_image_resource_id (resource_name) VALUES (?)`
		res, err := s.db.ExecContext(ctx, query, name)
		if err != nil {
			log.ErrorWithCtx(ctx, "InsertResourceIdRecord: %w", err)
			return 0, err
		}

		recordId, err := res.LastInsertId()
		if err != nil {
			log.ErrorWithCtx(ctx, "InsertResourceIdRecord: %w", err)
			return 0, err
		}
		return SafeInt64ToUint32(recordId), nil
	}
}

func SafeInt64ToUint32(val int64) uint32 {
	if val < 0 || val > math.MaxUint32 {
		log.ErrorWithCtx(context.Background(), "SafeInt64ToUint32: %d", val)
		return 0
	}
	return uint32(val)
}

func (s *Store) UpdateVirtualImageResourceStatus(ctx context.Context, idList []uint32, status uint32) error {
	if len(idList) == 0 {
		log.WarnWithCtx(ctx, "UpdateVirtualImageResourceStatus idList is empty")
		return nil
	}

	query, args, err := sqlx.In(`UPDATE virtual_image_resource_info SET status = ? WHERE id IN (?)`, status, idList)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateVirtualImageResourceStatus: %w", err)
		return err
	}

	result, err := s.db.ExecContext(ctx, query, args...)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateVirtualImageResourceStatus: %w", err)
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateVirtualImageResourceStatus Failed to get rows affected: %w", err)
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		log.WarnWithCtx(ctx, "No rows updated for idList %v", idList)
	}

	log.InfoWithCtx(ctx, "UpdateVirtualImageResourceStatus rows affected:%d, idList:%v, status:%s", rowsAffected, idList, status)
	return nil
}
