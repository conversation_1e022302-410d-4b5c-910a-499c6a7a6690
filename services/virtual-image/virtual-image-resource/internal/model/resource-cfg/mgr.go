package resource_cfg

//go:generate quicksilver-cli test interface ../resource-cfg
//go:generate mockgen -destination=./mocks/resource_cfg.go -package=mocks golang.52tt.com/services/virtual-image-resource/internal/model/resource-cfg IMgr

import (
	"context"
	"fmt"
	"github.com/buger/jsonparser"
	"github.com/coocood/freecache"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"gitlab.ttyuyin.com/tyr/x/compatible/proto"
	"golang.52tt.com/clients/obsgateway"
	"golang.52tt.com/pkg/log"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/pkg/timer"
	"golang.52tt.com/pkg/ttversion"
	virtual_image_mall "golang.52tt.com/protocol/services/virtual-image-mall"
	virtualImageResourcePb "golang.52tt.com/protocol/services/virtual-image-resource"
	"golang.52tt.com/services/virtual-image/virtual-image-resource/internal/conf"
	"golang.52tt.com/services/virtual-image/virtual-image-resource/internal/model/resource-cfg/store"
	"golang.52tt.com/services/virtual-image/virtual-image-resource/internal/utils"
	"math"
	"sort"
	"strings"
	"sync"
	"time"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"google.golang.org/grpc/codes"
)

const (
	MAX_PAGE_SIZE       = 99
	MINI_CLIENT_VERSION = "ios-6.62.3,pc-2.5.5,android-6.63.5,car-6.63.5,mini-6.63.5"
	ONCE_ADD_COUNT      = 50
)

// Mgr 资源配置
type Mgr struct {
	store store.IStore
	bc    conf.IBusinessConfManager

	wg       sync.WaitGroup
	shutDown chan struct{}

	mutex sync.RWMutex
	// 缓存版本
	latestVersionTs uint32
	// 缓存数据
	cacheList         []*store.VirtualImageResourceInfo
	cacheVersionList  []*store.VirtualImageResourceInfo
	levelConfigMap    map[uint32]*store.VirtualImageLevelConfig
	ActionResourceMap map[uint32]uint32

	mallClient virtual_image_mall.VirtualImageMallClient

	cacheMapResource sync.Map

	obsGatewayCli *obsgateway.Client
	timerD        *timer.Timer

	//CDN 资源地址
	cdnResourceRecord store.VirtualImageResourceInfoCDN
	localCache        freecache.Cache
}

// NewMgr 活动配置模块
func NewMgr(ctx context.Context, s mysql.DBx, bc conf.IBusinessConfManager) (*Mgr, error) {
	mysqlStore := store.NewStore(s)
	mallCli, _ := virtual_image_mall.NewClient(ctx)
	obsGatewayCli, _ := obsgateway.NewClient()

	// 定时建后续日期的表

	timerD, err := timer.NewTimerD(ctx, "virtual-image-resource", timer.WithTTL(10*time.Second))
	if nil != err {
		log.ErrorWithCtx(ctx, "init NewTimerD fail, err: %v", err)
		//return nil, err
	}

	timerD.Start()

	m := &Mgr{
		store:             mysqlStore,
		bc:                bc,
		shutDown:          make(chan struct{}),
		mallClient:        mallCli,
		levelConfigMap:    make(map[uint32]*store.VirtualImageLevelConfig),
		ActionResourceMap: make(map[uint32]uint32),
		obsGatewayCli:     obsGatewayCli,
		timerD:            timerD,
	}

	m.localCache = *freecache.NewCache(1024 * 1024 * 50)

	err = timerD.AddTask("@every 5m", "refreshResourceCDNFile", timer.BuildFromLambda(func(ctx context.Context) {
		m.refreshResourceCDNFile()
	}))
	if nil != err {
		log.ErrorWithCtx(ctx, "init ClearTimeOutContractHandle fail, err: %v", err)
		return m, nil
	}
	m.StartTimer()

	return m, nil
}

func (m *Mgr) Stop() {
	_ = m.store.Close()
}

func (m *Mgr) getResourceId(ctx context.Context, resourceName string) (uint32, error) {
	lastId, err := m.store.GetVirtualImageResourceIdByResourceName(ctx, resourceName)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddVirtualImageResource  getResourceId failed. err:%v", err)
		return 0, err
	}

	var resourceId uint32
	if lastId > 0 {
		return lastId, nil
	}

	resourceId, err = m.store.InsertResourceIdRecord(ctx, 0, resourceName)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddVirtualImageResource failed. err:%v", err)
		return 0, err
	}
	log.InfoWithCtx(ctx, "AddVirtualImageResource insert resourceId success. id:%d, %s", resourceId, resourceName)
	return resourceId, nil
}

func (m *Mgr) AddVirtualImageResource(ctx context.Context, request *virtualImageResourcePb.AddVirtualImageResourceRequest) (*virtualImageResourcePb.AddVirtualImageResourceResponse, error) {
	out := &virtualImageResourcePb.AddVirtualImageResourceResponse{}
	log.DebugWithCtx(ctx, "AddVirtualImageResource req size:%+v", len(request.GetResources()))
	storeList := make([]*store.VirtualImageResourceInfo, 0, len(request.GetResources()))
	for idx, v := range request.GetResources() {
		log.DebugWithCtx(ctx, "AddVirtualImageResource req：%+v", v)

		pathList := strings.Split(v.GetSkinName(), "/")

		var category, subCategory uint32
		if len(pathList) >= 2 {
			category, subCategory = m.bc.GetMapPathToCategory(pathList[0], pathList[1])
		}

		// 默认套装
		defaultSuit := v.GetDefaultSuit()
		if len(pathList) >= 3 && (strings.Contains(v.GetSkinName(), "suit") ||
			strings.Contains(v.GetSkinName(), "single") ||
			strings.Contains(v.GetSkinName(), "base_boy") ||
			strings.Contains(v.GetSkinName(), "base_girl")) {
			suitNameList := strings.Split(pathList[2], "_")
			if len(suitNameList) >= 3 {
				defaultSuit = fmt.Sprintf("%s_%s", suitNameList[1], suitNameList[2])
			}
		}

		//全身装 特殊的皮肤，需要特殊处理
		resourceType := v.GetResourceType()
		if subCategory == uint32(virtualImageResourcePb.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FULL_OUTFIT) {
			resourceType = uint32(virtualImageResourcePb.VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_SKIN_FULL_OUTFIT)
		}

		// IosResourceUrl为IOS特有字段， AddVirtualImageResource 先上传安卓资源
		if len(v.IosResourceUrl) == 0 {
			resourceId, err := m.getResourceId(ctx, v.GetResourceName())
			if resourceId == 0 {
				log.ErrorWithCtx(ctx, "AddVirtualImageResource failed. resourceId is 0 %s, err: %v", v.GetResourceName(), err)
				return out, nil
			}
			
			record, ok := m.cacheMapResource.Load(resourceId)
			if ok {
				lastRecord := record.(store.VirtualImageResourceInfo)
				log.DebugWithCtx(ctx, "AddVirtualImageResource anzhuo cacheMapResource:%+v", lastRecord.TextureUrlMap)
				if len(lastRecord.TextureUrlMap) > 0 {
					lastMap := utils.StringToCustomMap(lastRecord.TextureUrlMap)
					for key, value := range lastMap {
						if _, ok := v.TextureUrlMap[key]; !ok {
							v.TextureUrlMap[key] = value
						}
					}
				}
			}

			storeList = append(storeList, &store.VirtualImageResourceInfo{
				ID:               resourceId,
				SkinName:         v.GetSkinName(),
				ResourceURL:      v.GetResourceUrl(),
				ResourceName:     v.GetResourceName(),
				Essential:        v.GetEssential(),
				EncryptKey:       v.GetEncryptKey(),
				MD5:              v.GetMd5(),
				Status:           uint32(virtualImageResourcePb.VirtualImageResourceStatus_VIRTUAL_IMAGE_RESOURCE_STATUS_NORMAL),
				ResourceType:     resourceType,
				Category:         category,
				SubCategory:      subCategory,
				SplineVersion:    v.SplineVersion,
				DefaultSuit:      defaultSuit,
				LevelIcon:        v.GetLevelIcon(),
				Level:            1,
				Sex:              v.GetSex(),
				DisplayName:      v.GetDisplayName(),
				IconURL:          v.GetIconUrl(),
				IosMD5:           v.GetIosMd5(),
				IosResourceURL:   v.GetIosResourceUrl(),
				ScaleAble:        v.GetScaleAble(),
				DefaultAnimation: v.GetDefaultAnimation(),
				IsDeleted:        v.GetIsDeleted(),
				ResourcePrefix:   v.GetResourcePrefix(),
				Version:          v.GetVersion(),
				SkinMap:          utils.SkinMapToString(v.GetSkinMap()),
				TextureUrlMap:    utils.CustomMapToString(v.GetTextureUrlMap()),
			})

			// 每次添加 ONCE_ADD_COUNT 条数据 避免数据量过多
			if len(storeList) >= ONCE_ADD_COUNT || idx == len(request.GetResources())-1 {
				err := m.store.AddAndroidVirtualImageResourceInfo(ctx, storeList)
				if err != nil {
					log.ErrorWithCtx(ctx, "AddVirtualImageResource failed. err:%v", err)
					return out, err
				}
				storeList = make([]*store.VirtualImageResourceInfo, 0)
			}
		} else {
			// IOS资源 分开打包，相关参数单独上传 update对应物品
			record, ok := m.cacheMapResource.Load(resourceId)
			if ok {
				lastRecord := record.(store.VirtualImageResourceInfo)
				if len(lastRecord.CustomMap) > 0 {
					lastMap := utils.StringToCustomMap(lastRecord.CustomMap)
					for key, value := range lastMap {
						if _, ok := v.CustomMap[key]; !ok {
							v.CustomMap[key] = value
						}
					}
				}
				
				if len(lastRecord.TextureUrlMap) > 0 {
					lastMap := utils.StringToCustomMap(lastRecord.TextureUrlMap)
					log.DebugWithCtx(ctx, "AddVirtualImageResource ios cacheMapResource:%+v", lastRecord.TextureUrlMap)
					for key, value := range lastMap {
						if _, ok := v.TextureUrlMap[key]; !ok {
							v.TextureUrlMap[key] = value
						}
					}
				}
			}

			err := m.store.UpdateVirtualImageResourceCustomMap(ctx, &store.VirtualImageResourceInfo{
				ResourceName:   v.GetResourceName(),
				CustomMap:      utils.CustomMapToString(v.GetCustomMap()),
				IosResourceURL: v.GetIosResourceUrl(),
				IosMD5:         v.GetIosMd5(),
				IosSkinMap:     utils.SkinMapToString(v.GetIosSkinMap()),
				IosVersion:     v.GetIosVersion(),
				TextureUrlMap:  utils.CustomMapToString(v.GetTextureUrlMap()),
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "AddVirtualImageResource failed. err:%v", err)
				return out, err
			}
			log.DebugWithCtx(ctx, "AddVirtualImageResource update customMap success. id:%d", v.GetId())
		}
	}

	m.checkResourceUpdate()
	return out, nil
}

func (m *Mgr) EditVirtualImageResource(ctx context.Context, request *virtualImageResourcePb.EditVirtualImageResourceRequest) (*virtualImageResourcePb.EditVirtualImageResourceResponse, error) {
	out := &virtualImageResourcePb.EditVirtualImageResourceResponse{}

	resources, err := m.GetVirtualImageResourcesByIds(ctx, &virtualImageResourcePb.GetVirtualImageResourcesByIdsRequest{Ids: []uint32{request.GetResource().Id}})
	if err != nil {
		log.ErrorWithCtx(ctx, "EditVirtualImageResource failed. err:%v", err)
		return out, err
	}
	if len(resources.GetResources()) == 0 {
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "资源配置未找到")
	}

	item := resources.GetResources()[0]

	if item.GetIsCommodity() {
		if item.GetLevel() != request.GetResource().GetLevel() {
			return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "物品已配置过商品，无法修改商品等级")
		}

		if item.GetSex() != request.GetResource().GetSex() {
			if request.GetResource().GetSex() != uint32(virtualImageResourcePb.VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_UNKNOWN) {
				return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "该物品已配置过商品，无法修改性别")
			}
		}

		for i := 0; i <= 3; i++ {
			_, err := m.mallClient.UpdateCommodityResource(ctx, &virtual_image_mall.UpdateCommodityResourceReq{
				Id:   request.GetResource().GetId(),
				Name: request.GetResource().GetDisplayName(),
				Icon: request.GetResource().GetIconUrl(),
				Sex:  request.GetResource().GetSex(),
			})

			if err != nil {
				log.ErrorWithCtx(ctx, "EditVirtualImageResource failed. err:%v", err)
				continue
			} else {
				break
			}
		}
	}

	err = m.store.UpdateVirtualImageResource(ctx, request.GetResource())
	if err != nil {
		log.ErrorWithCtx(ctx, "EditVirtualImageResource failed. err:%v", err)
		return out, err
	}

	return out, nil
}

func (m *Mgr) SearchVirtualImageResource(ctx context.Context, request *virtualImageResourcePb.SearchVirtualImageResourceRequest) (*virtualImageResourcePb.SearchVirtualImageResourceResponse, error) {
	out := &virtualImageResourcePb.SearchVirtualImageResourceResponse{}
	list, err := m.store.SearchVirtualImageResource(ctx, request)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchVirtualImageResource failed. err:%v", err)
		return out, err
	}

	totalCount, err := m.store.SearchVirtualImageResourceCount(ctx, request)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchVirtualImageResource failed. err:%v", err)
		return out, nil
	}

	out.Offset = request.GetOffset()
	out.Limit = request.GetLimit()
	out.Total = totalCount
	out.Resources = make([]*virtualImageResourcePb.VirtualImageResourceInfo, 0, len(list))
	log.DebugWithCtx(ctx, "SearchVirtualImageResource req:%+v list:%d", request, len(list))

	for _, v := range list {
		if v.IsDeleted {
			log.WarnWithCtx(ctx, "SearchVirtualImageResource: id:%d is deleted", v.ID)
			continue
		}
		outTemp := v
		out.Resources = append(out.Resources, m.transStoreToPB(ctx, &outTemp))
	}
	log.DebugWithCtx(ctx, "SearchVirtualImageResource req:%+v out:%+v", request, out)
	return out, nil
}

func (m *Mgr) GetVirtualImageResourceCategory(ctx context.Context, req *virtualImageResourcePb.GetVirtualImageResourceCategoryRequest) (*virtualImageResourcePb.GetVirtualImageResourceCategoryResponse, error) {
	log.DebugWithCtx(ctx, "GetVirtualImageResourceCategory: %v", req)
	out := &virtualImageResourcePb.GetVirtualImageResourceCategoryResponse{}
	key := jsonparser.StringToBytes("GetVirtualImageResourceCategory")
	cacheData, err := m.localCache.Get(key)
	if err == nil {
		err = proto.Unmarshal(cacheData, out)
		if err == nil {
			return out, nil
		}
	}

	parentCategoryInfoList, err := m.store.GetVirtualImageParentCategory(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetVirtualImageParentCategory err:%v", err)
		return out, err
	}
	parentCategoryIdList := make([]uint32, 0)
	//parentCategoryMap := make(map[uint32]*store.ParentCategoryInfo)
	for _, data := range parentCategoryInfoList {
		parentCategoryIdList = append(parentCategoryIdList, data.CategoryId)
		//parentCategoryMap[data.CategoryId] = data
	}
	subCategoryInfoList, err := m.store.GetVirtualImageSubCategory(ctx, parentCategoryIdList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetVirtualImageSubCategory err:%v", err)
		return out, err
	}

	subCategoryInfoMap := make(map[uint32][]*store.SubCategoryInfo)
	for _, data := range subCategoryInfoList {
		subCategoryInfoMap[data.ParentId] = append(subCategoryInfoMap[data.ParentId], data)
	}

	sort.SliceStable(parentCategoryInfoList, func(i, j int) bool {
		return parentCategoryInfoList[i].Rank < parentCategoryInfoList[j].Rank
	})
	for _, data := range parentCategoryInfoList {
		tmpData := &virtualImageResourcePb.VirtualImageResourceCategoryInfo{
			ParentCategoryInfo: &virtualImageResourcePb.VirtualImageParentCategoryInfo{
				Category:     data.CategoryId,
				CategoryName: data.CategoryName,
				CategoryType: data.CategoryType,
			},
		}
		if valList, ok := subCategoryInfoMap[data.CategoryId]; ok {
			sort.SliceStable(valList, func(i, j int) bool {
				return valList[i].Rank < valList[j].Rank
			})
			for _, subData := range valList {
				tmpData.SubCategoryInfoList = append(tmpData.GetSubCategoryInfoList(), &virtualImageResourcePb.VirtualImageSubCategoryInfo{
					SubCategory:                  subData.CategoryId,
					SubCategoryName:              subData.CategoryName,
					SubCategoryImgUrl:            subData.CategoryImgUrl,
					SubCategoryType:              subData.CategoryType,
					SubCategoryImgUrlSelected:    subData.CategoryImgPreviewUrl,
					WebSubCategoryImgUrl:         subData.WebCategoryImgUrl,
					WebSubCategoryImgUrlSelected: subData.WebCategoryImgPreviewUrl,
				})

			}
		}
		out.ResourceCategoryInfoList = append(out.GetResourceCategoryInfoList(), tmpData)
	}
	log.DebugWithCtx(ctx, "GetVirtualImageResourceCategory: req:%v out:%v", req, out)
	// 缓存数据
	outByte, _ := proto.Marshal(out)
	err = m.localCache.Set(key, outByte, 60)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetVirtualImageResourceCategory SetCache failed req:%v err:%v", req, err)
	}
	return out, nil

}

// BinarySearch 查找第一个 update_time >= last_time 的元素的索引
func BinarySearch(cacheList []*store.VirtualImageResourceInfo, lastTime time.Time) int {
	left, right := 0, len(cacheList)-1
	for left <= right {
		mid := left + (right-left)/2
		if cacheList[mid].UpdateTime.After(lastTime) {
			left = mid + 1
		} else {
			right = mid - 1
		}
	}

	return right + 1
}

// BinarySearch 查找第一个 update_time >= last_time 的元素的索引
func BinarySearchByVersion(cacheVersionList []*store.VirtualImageResourceInfo, version uint32) int {
	sort.SliceStable(cacheVersionList, func(i, j int) bool {
		return cacheVersionList[i].Version > cacheVersionList[j].Version
	})
	left, right := 0, len(cacheVersionList)-1
	for left <= right {
		mid := left + (right-left)/2
		if cacheVersionList[mid].Version > version {
			left = mid + 1
		} else {
			right = mid - 1
		}
	}

	return right + 1
}

func (m *Mgr) isNewClientVersion(ctx context.Context) bool {
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return false
	}

	minVersion := ttversion.Parse("", strings.Split(MINI_CLIENT_VERSION, ",")...)
	return minVersion.Atleast(serviceInfo.ClientType, serviceInfo.ClientVersion)
}

func (m *Mgr) GetClientListByPage(ctx context.Context, request *virtualImageResourcePb.GetClientListByPageRequest) (*virtualImageResourcePb.GetClientListByPageResponse, error) {
	out := &virtualImageResourcePb.GetClientListByPageResponse{}
	log.DebugWithCtx(ctx, "GetClientListByPage req:%v", request)

	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)

	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if request.GetLatestId() == m.latestVersionTs {
		out.IsEnd = true
		return out, nil
	}

	out.LatestId = m.latestVersionTs
	out.Offset = request.GetOffset()
	out.Limit = request.GetLimit()

	if m.isNewClientVersion(ctx) {
		if serviceInfo.ClientType == protocol.ClientTypeANDROID {
			if request.GetLatestId() > m.bc.GetAndroidMinVersion() {
				findIndex := BinarySearchByVersion(m.cacheVersionList, request.GetLatestId())
				log.DebugWithCtx(ctx, "GetClientListByPage BinarySearchByVersion %d, findIndex:%d, uid:%d", request.GetLatestId(), findIndex, serviceInfo.UserID)
				if findIndex <= MAX_PAGE_SIZE {
					for i := 0; i < findIndex; i++ {
						out.Resources = append(out.Resources, m.transStoreToPB(ctx, m.cacheVersionList[i]))
					}
					out.IsEnd = true
					log.DebugWithCtx(ctx, "GetClientListByPage BinarySearchByVersion:%d out:%v, uid:%d ", len(out.Resources), out.IsEnd, serviceInfo.UserID)
					return out, nil
				}
			}
		} else {
			findIndex := BinarySearch(m.cacheList, time.Unix(int64(request.GetLatestId()), 0))
			log.DebugWithCtx(ctx, "GetClientListByPage GetLatestId %d, findIndex:%d, uid:%d", request.GetLatestId(), findIndex, serviceInfo.UserID)
			if findIndex <= MAX_PAGE_SIZE {
				for i := 0; i < findIndex; i++ {
					out.Resources = append(out.Resources, m.transStoreToPB(ctx, m.cacheList[i]))
					log.DebugWithCtx(ctx, "GetClientListByPage update id:%d, uid:%d", m.cacheList[i].ID, serviceInfo.UserID)
				}
				out.IsEnd = true
				log.DebugWithCtx(ctx, "GetClientListByPage size:%d out:%v, uid:%d ", len(out.Resources), out.IsEnd, serviceInfo.UserID)
				return out, nil
			}
		}
	}

	if request.GetOffset() == 0 {
		if m.cdnResourceRecord.Url != "" {
			out.DownloadUrl = m.cdnResourceRecord.Url
			out.DownloadMd5 = m.cdnResourceRecord.Md5
		}
	}

	list := make([]*store.VirtualImageResourceInfo, 0)
	beginIdx := int(request.GetOffset())
	endIdx := beginIdx + int(request.GetLimit())
	for i, v := range m.cacheList {
		if beginIdx > 0 && i < beginIdx {
			continue
		}
		if i >= endIdx {
			break
		}

		list = append(list, v)
	}

	for _, v := range list {
		out.Resources = append(out.Resources, m.transStoreToPB(ctx, v))
	}

	if len(out.Resources) < int(request.GetLimit()) {
		out.IsEnd = true
	}
	log.DebugWithCtx(ctx, "GetClientListByPage: req:%v out size:%d, uid:%d", request, len(out.Resources), serviceInfo.UserID)
	return out, nil
}

func (m *Mgr) DeleteVirtualImageResource(ctx context.Context, request *virtualImageResourcePb.DeleteVirtualImageResourceRequest) error {
	log.InfoWithCtx(ctx, "DeleteVirtualImageResource: %v", request)
	m.mutex.Lock()
	defer m.mutex.Unlock()

	for _, v := range m.cacheList {
		if v.ID == request.GetId() {
			if v.IsCommodity {
				return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "该物品已配置过商品，无法删除")
			}
		}
		log.InfoWithCtx(ctx, "DeleteVirtualImageResource: id = %d", v.ID)
	}
	return m.store.DeleteVirtualImageResource(ctx, request.GetId())
}

func (m *Mgr) GetVirtualImageResourcesByIds(ctx context.Context, request *virtualImageResourcePb.GetVirtualImageResourcesByIdsRequest) (*virtualImageResourcePb.GetVirtualImageResourcesByIdsResponse, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	out := &virtualImageResourcePb.GetVirtualImageResourcesByIdsResponse{}
	for _, id := range request.GetIds() {
		for _, v := range m.cacheList {
			if v.ID == id {
				out.Resources = append(out.Resources, m.transStoreToPB(ctx, v))
			}
		}
	}

	return out, nil
}

func (m *Mgr) UpdateVirtualImageResourceUrl(ctx context.Context, request *virtualImageResourcePb.UpdateVirtualImageResourceUrlRequest) (*virtualImageResourcePb.UpdateVirtualImageResourceUrlResponse, error) {
	out := &virtualImageResourcePb.UpdateVirtualImageResourceUrlResponse{}
	return out, m.store.UpdateVirtualImageResourceUrl(ctx, request)
}

func (m *Mgr) SetVirtualImageResourceForSale(ctx context.Context, idList []uint32) error {
	return m.store.SetVirtualImageResourceForSale(ctx, idList)
}

func (m *Mgr) CloneVirtualImageResource(ctx context.Context, request *virtualImageResourcePb.CloneVirtualImageResourceRequest) (*virtualImageResourcePb.CloneVirtualImageResourceResponse, error) {
	out := &virtualImageResourcePb.CloneVirtualImageResourceResponse{}
	storeList := make([]*store.VirtualImageResourceInfo, 0, len(request.GetResources()))
	skeletonList := make([]*virtualImageResourcePb.VirtualImageResourceInfo, 0)
	for _, v := range request.GetResources() {
		if v.ResourceType == uint32(virtualImageResourcePb.VirtualImageResourceType_VIRTUAL_IMAGE_RESOURCE_TYPE_SKELETON) {
			skeletonList = append(skeletonList, v)
			continue
		}
		storeList = append(storeList, &store.VirtualImageResourceInfo{
			ID:               v.GetId(),
			SkinName:         v.GetSkinName(),
			ResourceURL:      v.GetResourceUrl(),
			ResourceName:     v.GetResourceName(),
			Essential:        v.GetEssential(),
			EncryptKey:       v.GetEncryptKey(),
			MD5:              v.GetMd5(),
			Status:           uint32(virtualImageResourcePb.VirtualImageResourceStatus_VIRTUAL_IMAGE_RESOURCE_STATUS_NORMAL),
			ResourceType:     v.GetResourceType(),
			Category:         v.GetCategory(),
			SubCategory:      v.GetSubCategory(),
			SplineVersion:    v.SplineVersion,
			DefaultSuit:      v.GetDefaultSuit(),
			Level:            v.GetLevel(),
			LevelIcon:        v.GetLevelIcon(),
			Sex:              v.GetSex(),
			DisplayName:      v.GetDisplayName(),
			IconURL:          v.GetIconUrl(),
			IosMD5:           v.GetIosMd5(),
			IosResourceURL:   v.GetIosResourceUrl(),
			ScaleAble:        v.GetScaleAble(),
			DefaultAnimation: v.GetDefaultAnimation(),
			ResourcePrefix:   v.GetResourcePrefix(),
			CustomMap:        utils.CustomMapToString(v.GetCustomMap()),
			Version:          v.GetVersion(),
			SkinMap:          utils.SkinMapToString(v.GetSkinMap()),
			IosSkinMap:       utils.SkinMapToString(v.GetIosSkinMap()),
			IosVersion:       v.GetIosVersion(),
			IsNew:            v.GetIsNewResource(),
			TextureUrlMap:    utils.CustomMapToString(v.GetTextureUrlMap()),
		})
	}
	err := m.store.CloneVirtualImageResource(ctx, storeList)
	if err != nil {
		log.ErrorWithCtx(ctx, "CloneVirtualImageResource: %w", err)
		// 如果是重复错误，由于有自增主键，说明排序值重复，返回一个特定的错误
		if err != nil && strings.Contains(err.Error(), "Error 1062: Duplicate entry") {
			return out, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, "该物品已上传过正式，无法重复上传")
		}
		return out, err
	}

	if len(skeletonList) > 0 {
		log.InfoWithCtx(ctx, "CloneVirtualImageResource: skeletonList:%+v", skeletonList)
		m.UpdateVirtualImageResourceUrl(ctx, &virtualImageResourcePb.UpdateVirtualImageResourceUrlRequest{Resources: request.GetResources()})
	}

	return out, nil
}

func (m *Mgr) transStoreToPB(ctx context.Context, v *store.VirtualImageResourceInfo) *virtualImageResourcePb.VirtualImageResourceInfo {
	sv, ok := protogrpc.ServiceInfoFromContext(ctx)
	md5 := v.MD5
	resourceUrl := v.ResourceURL
	if ok {
		if sv.ClientType == protocol.ClientTypeIOS && v.IosResourceURL != "" && v.IosMD5 != "" {
			md5 = v.IosMD5
			resourceUrl = v.IosResourceURL
		}
	}

	var levelWebp string
	var levelIcon string
	if levelCfg, ok := m.levelConfigMap[v.Level]; ok {
		levelWebp = levelCfg.LevelWebp
		levelIcon = levelCfg.LevelIcon
	}

	return &virtualImageResourcePb.VirtualImageResourceInfo{
		Id:               v.ID,
		ResourceUrl:      resourceUrl,
		SkinName:         v.SkinName,
		Essential:        v.Essential,
		EncryptKey:       v.EncryptKey,
		Md5:              md5,
		ResourceType:     v.ResourceType,
		Category:         v.Category,
		SubCategory:      v.SubCategory,
		SplineVersion:    v.SplineVersion,
		DefaultSuit:      v.DefaultSuit,
		CreateTime:       SafeInt64ToUint32(v.CreateTime.Unix()),
		UpdateTime:       SafeInt64ToUint32(v.UpdateTime.Unix()),
		Status:           v.Status,
		IsCommodity:      v.IsCommodity,
		Sex:              v.Sex,
		LevelIcon:        levelIcon,
		Level:            v.Level,
		DisplayName:      v.DisplayName,
		IconUrl:          v.IconURL,
		Version:          v.Version,
		ResourceName:     v.ResourceName,
		LevelWebp:        levelWebp,
		ScaleAble:        v.ScaleAble,
		DefaultAnimation: v.DefaultAnimation,
		IosMd5:           v.IosMD5,
		IosResourceUrl:   v.IosResourceURL,
		ResourcePrefix:   v.ResourcePrefix,
		CustomMap:        utils.StringToCustomMap(v.CustomMap),
		SkinMap:          utils.StringToSkinMap(v.SkinMap),
		IosSkinMap:       utils.StringToSkinMap(v.IosSkinMap),
		IosVersion:       v.IosVersion,
		IsNewResource:    v.IsNew,
		TextureUrlMap:    utils.StringToCustomMap(v.TextureUrlMap),
	}
}

func (m *Mgr) GetVirtualImageResourceBySuit(ctx context.Context) (*virtualImageResourcePb.GetVirtualImageResourceBySuitResponse, error) {
	out := &virtualImageResourcePb.GetVirtualImageResourceBySuitResponse{}
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	mapSuitList := make(map[string][]*store.VirtualImageResourceInfo)
	for _, v := range m.cacheList {
		if len(v.DefaultSuit) > 0 && strings.Contains(v.DefaultSuit, "suit") {
			mapSuitList[v.DefaultSuit] = append(mapSuitList[v.DefaultSuit], v)
		}

		if len(mapSuitList) >= 20 {
			break
		}
	}

	for _, v := range mapSuitList {
		suitInfo := &virtualImageResourcePb.VirtualImageResourceSuitInfo{
			Suit: v[0].DefaultSuit,
		}

		for _, v1 := range v {
			suitInfo.Resources = append(suitInfo.Resources, m.transStoreToPB(ctx, v1))
		}
		out.Resources = append(out.Resources, suitInfo)
	}
	return out, nil
}

func (m *Mgr) BatchUpdateIcon(ctx context.Context, request *virtualImageResourcePb.BatchUpdateIconRequest) error {
	for _, v := range request.GetList() {
		err := m.store.UpdateIconByID(ctx, v)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchUpdateIcon: %w", err)
			return err
		}

		// 更新商城商品icon信息
		if v.GetIsCommodity() {
			for i := 0; i <= 3; i++ {
				_, err := m.mallClient.UpdateCommodityResource(ctx, &virtual_image_mall.UpdateCommodityResourceReq{
					Id:   v.GetId(),
					Name: v.GetDisplayName(),
					Icon: v.GetIconUrl(),
					Sex:  v.GetSex(),
				})

				if err != nil {
					log.ErrorWithCtx(ctx, "EditVirtualImageResource failed. err:%v", err)
					continue
				} else {
					break
				}
			}
		}
	}
	return nil
}

func (m *Mgr) GetDefaultResourceList(ctx context.Context) (*virtualImageResourcePb.GetDefaultResourceListResponse, error) {
	out := &virtualImageResourcePb.GetDefaultResourceListResponse{
		MaleAnimationMap:   make(map[uint32]uint32),
		FemaleAnimationMap: make(map[uint32]uint32),
	}
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	for _, v := range m.cacheList {
		if v.Essential {
			// 默认男女素体资源 &动作
			if v.Sex == uint32(virtualImageResourcePb.VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_MALE) {
				out.MaleResources = append(out.MaleResources, v.ID)

				//动作小头是默认资源 座椅、坐姿、背景时，虚拟形象预览为坐姿。 其余情况虚拟形象预览为站姿
				if strings.Contains(v.ResourceName, "sit") {
					out.MaleAnimationMap[uint32(virtualImageResourcePb.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SITTING_POSE)] = v.ID
					out.MaleAnimationMap[uint32(virtualImageResourcePb.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_CHAIR)] = v.ID
					out.MaleAnimationMap[uint32(virtualImageResourcePb.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_BACKGROUND)] = v.ID
				} else if strings.Contains(v.ResourceName, "stand") {
					out.MaleAnimationMap[uint32(virtualImageResourcePb.VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_FACE_SUIT)] = v.ID
					out.MaleAnimationMap[uint32(virtualImageResourcePb.VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_FACE_SHAPING)] = v.ID
				} else {
					log.DebugWithCtx(ctx, "GetDefaultResourceList :%s", v.ResourceName)
				}
			} else if v.Sex == uint32(virtualImageResourcePb.VirtualImageResourceSex_VIRTUAL_IMAGE_RESOURCE_SEX_FEMALE) {
				out.FemaleResources = append(out.FemaleResources, v.ID)
				if strings.Contains(v.ResourceName, "sit") {
					out.FemaleAnimationMap[uint32(virtualImageResourcePb.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SITTING_POSE)] = v.ID
					out.FemaleAnimationMap[uint32(virtualImageResourcePb.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_CHAIR)] = v.ID
					out.FemaleAnimationMap[uint32(virtualImageResourcePb.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_BACKGROUND)] = v.ID
				} else if strings.Contains(v.ResourceName, "stand") {
					out.FemaleAnimationMap[uint32(virtualImageResourcePb.VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_FACE_SUIT)] = v.ID
					out.FemaleAnimationMap[uint32(virtualImageResourcePb.VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_FACE_SHAPING)] = v.ID
				} else {
					log.DebugWithCtx(ctx, "GetDefaultResourceList :%s", v.ResourceName)
				}
			} else {
				out.MaleResources = append(out.MaleResources, v.ID)
				out.FemaleResources = append(out.FemaleResources, v.ID)
			}

			//全身装只跟素体基础上下装互斥，跟非素体基础上下装不互斥
			if v.SubCategory == uint32(virtualImageResourcePb.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_UPPER_OUTFIT) ||
				v.SubCategory == uint32(virtualImageResourcePb.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_LOWER_OUTFIT) {
				out.OutFit = append(out.OutFit, v.ID)
			}

			if v.SubCategory > 0 {
				if (v.SubCategory >= uint32(virtualImageResourcePb.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_FACE_SHAPE) && v.SubCategory <= uint32(virtualImageResourcePb.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_EYES)) ||
					(v.SubCategory >= uint32(virtualImageResourcePb.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SITTING_POSE) && v.SubCategory <= uint32(virtualImageResourcePb.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_ROOM_ENTRANCE_EFFECTS)) ||
					(v.SubCategory == uint32(virtualImageResourcePb.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_UPPER_OUTFIT) || v.SubCategory == uint32(virtualImageResourcePb.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_LOWER_OUTFIT)) {
					continue
				} else {
					out.UnShowList = append(out.UnShowList, v.ID)
				}
			}
		}
	}

	log.DebugWithCtx(ctx, "GetDefaultResourceList: out:%+v", out)
	return out, nil
}

func (m *Mgr) GetLevelConfig(ctx context.Context) (*virtualImageResourcePb.GetLevelConfigResponse, error) {
	out := &virtualImageResourcePb.GetLevelConfigResponse{}
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	for _, v := range m.levelConfigMap {
		out.List = append(out.List, &virtualImageResourcePb.LevelConfig{
			Level:      v.Level,
			LevelIcon:  v.LevelIcon,
			LevelWebp:  v.LevelWebp,
			UpdateTime: SafeInt64ToUint32(v.UpdateTime.Unix()),
		})
	}

	sort.SliceStable(out.List, func(i, j int) bool {
		return out.List[i].Level < out.List[j].Level
	})
	return out, nil
}

func (m *Mgr) UpdateLevelConfig(ctx context.Context, request *virtualImageResourcePb.LevelConfig) error {
	err := m.store.UpdateLevelConfig(ctx, request)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateLevelConfig: %w", err)
		return err
	}

	// 更新物品版本号，避免客户端无法获取到最新数据
	m.mutex.Lock()
	defer m.mutex.Unlock()
	for _, v := range m.cacheList {
		if v.Level == request.Level {
			err := m.store.UpdateVirtualImageResource(ctx, &virtualImageResourcePb.VirtualImageResourceInfo{
				Id:          v.ID,
				DisplayName: v.DisplayName,
				ResourceUrl: v.ResourceURL,
				IconUrl:     v.IconURL,
				Sex:         v.Sex,
				LevelIcon:   request.LevelIcon,
				Level:       v.Level,
				Status:      v.Status,
				DefaultSuit: v.DefaultSuit,
				Version:     v.Version,
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateVirtualImageResource: %w", err)
			}
		}
	}

	return nil
}

func (m *Mgr) AddLevelConfig(ctx context.Context, request *virtualImageResourcePb.LevelConfig) error {
	err := m.store.AddLevelConfig(ctx, request)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddLevelConfig: %w", err)
		if err != nil && strings.Contains(err.Error(), "Error 1062: Duplicate entry") {
			return protocol.NewExactServerError(nil, status.ErrRevenueSvrErr, "已有相同等级的配置，无法提交成功")
		}
		return err
	}
	return nil
}

func (m *Mgr) GetActionResourceMap(ctx context.Context) (*virtualImageResourcePb.GetActionResourceMapResponse, error) {
	out := &virtualImageResourcePb.GetActionResourceMapResponse{
		ActionResourceMap: make(map[uint32]uint32),
	}

	m.mutex.RLock()
	defer m.mutex.RUnlock()
	out.ActionResourceMap = m.ActionResourceMap
	return out, nil
}

func SafeInt64ToUint32(val int64) uint32 {
	if val < 0 || val > math.MaxUint32 {
		log.Errorf("SafeInt64ToUint32: %d", val)
		return 0
	}
	return uint32(val)
}

func (m *Mgr) UpdateVirtualImageResourceStatus(ctx context.Context, idList []uint32, status uint32) error {
	err := m.store.UpdateVirtualImageResourceStatus(ctx, idList, status)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateVirtualImageResourceStatus failed. err:%v", err)
	}
	return err
}
