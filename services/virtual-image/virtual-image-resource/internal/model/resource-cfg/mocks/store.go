// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/virtual-image/virtual-image-resource/internal/model/resource-cfg/store (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
	store "golang.52tt.com/services/virtual-image/virtual-image-resource/internal/model/resource-cfg/store"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// AddAndroidVirtualImageResourceInfo mocks base method.
func (m *MockIStore) AddAndroidVirtualImageResourceInfo(arg0 context.Context, arg1 []*store.VirtualImageResourceInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAndroidVirtualImageResourceInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddAndroidVirtualImageResourceInfo indicates an expected call of AddAndroidVirtualImageResourceInfo.
func (mr *MockIStoreMockRecorder) AddAndroidVirtualImageResourceInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAndroidVirtualImageResourceInfo", reflect.TypeOf((*MockIStore)(nil).AddAndroidVirtualImageResourceInfo), arg0, arg1)
}

// AddCDNFileRecord mocks base method.
func (m *MockIStore) AddCDNFileRecord(arg0 context.Context, arg1, arg2 string, arg3 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCDNFileRecord", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddCDNFileRecord indicates an expected call of AddCDNFileRecord.
func (mr *MockIStoreMockRecorder) AddCDNFileRecord(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCDNFileRecord", reflect.TypeOf((*MockIStore)(nil).AddCDNFileRecord), arg0, arg1, arg2, arg3)
}

// AddLevelConfig mocks base method.
func (m *MockIStore) AddLevelConfig(arg0 context.Context, arg1 *virtual_image_resource.LevelConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddLevelConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddLevelConfig indicates an expected call of AddLevelConfig.
func (mr *MockIStoreMockRecorder) AddLevelConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddLevelConfig", reflect.TypeOf((*MockIStore)(nil).AddLevelConfig), arg0, arg1)
}

// CloneVirtualImageResource mocks base method.
func (m *MockIStore) CloneVirtualImageResource(arg0 context.Context, arg1 []*store.VirtualImageResourceInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloneVirtualImageResource", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CloneVirtualImageResource indicates an expected call of CloneVirtualImageResource.
func (mr *MockIStoreMockRecorder) CloneVirtualImageResource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloneVirtualImageResource", reflect.TypeOf((*MockIStore)(nil).CloneVirtualImageResource), arg0, arg1)
}

// Close mocks base method.
func (m *MockIStore) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockIStoreMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIStore)(nil).Close))
}

// DelCDNFileRecord mocks base method.
func (m *MockIStore) DelCDNFileRecord(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelCDNFileRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelCDNFileRecord indicates an expected call of DelCDNFileRecord.
func (mr *MockIStoreMockRecorder) DelCDNFileRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelCDNFileRecord", reflect.TypeOf((*MockIStore)(nil).DelCDNFileRecord), arg0, arg1)
}

// DeleteVirtualImageResource mocks base method.
func (m *MockIStore) DeleteVirtualImageResource(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteVirtualImageResource", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteVirtualImageResource indicates an expected call of DeleteVirtualImageResource.
func (mr *MockIStoreMockRecorder) DeleteVirtualImageResource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteVirtualImageResource", reflect.TypeOf((*MockIStore)(nil).DeleteVirtualImageResource), arg0, arg1)
}

// GetCDNFileRecord mocks base method.
func (m *MockIStore) GetCDNFileRecord(arg0 context.Context) (store.VirtualImageResourceInfoCDN, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCDNFileRecord", arg0)
	ret0, _ := ret[0].(store.VirtualImageResourceInfoCDN)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCDNFileRecord indicates an expected call of GetCDNFileRecord.
func (mr *MockIStoreMockRecorder) GetCDNFileRecord(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCDNFileRecord", reflect.TypeOf((*MockIStore)(nil).GetCDNFileRecord), arg0)
}

// GetLevelConfig mocks base method.
func (m *MockIStore) GetLevelConfig(arg0 context.Context) ([]*store.VirtualImageLevelConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelConfig", arg0)
	ret0, _ := ret[0].([]*store.VirtualImageLevelConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLevelConfig indicates an expected call of GetLevelConfig.
func (mr *MockIStoreMockRecorder) GetLevelConfig(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelConfig", reflect.TypeOf((*MockIStore)(nil).GetLevelConfig), arg0)
}

// GetMaxUpdateTime mocks base method.
func (m *MockIStore) GetMaxUpdateTime(arg0 context.Context) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxUpdateTime", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxUpdateTime indicates an expected call of GetMaxUpdateTime.
func (mr *MockIStoreMockRecorder) GetMaxUpdateTime(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxUpdateTime", reflect.TypeOf((*MockIStore)(nil).GetMaxUpdateTime), arg0)
}

// GetMaxVersion mocks base method.
func (m *MockIStore) GetMaxVersion(arg0 context.Context) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxVersion", arg0)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxVersion indicates an expected call of GetMaxVersion.
func (mr *MockIStoreMockRecorder) GetMaxVersion(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxVersion", reflect.TypeOf((*MockIStore)(nil).GetMaxVersion), arg0)
}

// GetVirtualImageParentCategory mocks base method.
func (m *MockIStore) GetVirtualImageParentCategory(arg0 context.Context) ([]*store.ParentCategoryInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualImageParentCategory", arg0)
	ret0, _ := ret[0].([]*store.ParentCategoryInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageParentCategory indicates an expected call of GetVirtualImageParentCategory.
func (mr *MockIStoreMockRecorder) GetVirtualImageParentCategory(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageParentCategory", reflect.TypeOf((*MockIStore)(nil).GetVirtualImageParentCategory), arg0)
}

// GetVirtualImageResourceIdByResourceName mocks base method.
func (m *MockIStore) GetVirtualImageResourceIdByResourceName(arg0 context.Context, arg1 string) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualImageResourceIdByResourceName", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageResourceIdByResourceName indicates an expected call of GetVirtualImageResourceIdByResourceName.
func (mr *MockIStoreMockRecorder) GetVirtualImageResourceIdByResourceName(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageResourceIdByResourceName", reflect.TypeOf((*MockIStore)(nil).GetVirtualImageResourceIdByResourceName), arg0, arg1)
}

// GetVirtualImageResourceInfoCount mocks base method.
func (m *MockIStore) GetVirtualImageResourceInfoCount(arg0 context.Context) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualImageResourceInfoCount", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageResourceInfoCount indicates an expected call of GetVirtualImageResourceInfoCount.
func (mr *MockIStoreMockRecorder) GetVirtualImageResourceInfoCount(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageResourceInfoCount", reflect.TypeOf((*MockIStore)(nil).GetVirtualImageResourceInfoCount), arg0)
}

// GetVirtualImageSubCategory mocks base method.
func (m *MockIStore) GetVirtualImageSubCategory(arg0 context.Context, arg1 []uint32) ([]*store.SubCategoryInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualImageSubCategory", arg0, arg1)
	ret0, _ := ret[0].([]*store.SubCategoryInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageSubCategory indicates an expected call of GetVirtualImageSubCategory.
func (mr *MockIStoreMockRecorder) GetVirtualImageSubCategory(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageSubCategory", reflect.TypeOf((*MockIStore)(nil).GetVirtualImageSubCategory), arg0, arg1)
}

// InsertResourceIdRecord mocks base method.
func (m *MockIStore) InsertResourceIdRecord(arg0 context.Context, arg1 uint32, arg2 string) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertResourceIdRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertResourceIdRecord indicates an expected call of InsertResourceIdRecord.
func (mr *MockIStoreMockRecorder) InsertResourceIdRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertResourceIdRecord", reflect.TypeOf((*MockIStore)(nil).InsertResourceIdRecord), arg0, arg1, arg2)
}

// SearchVirtualImageResource mocks base method.
func (m *MockIStore) SearchVirtualImageResource(arg0 context.Context, arg1 *virtual_image_resource.SearchVirtualImageResourceRequest) ([]store.VirtualImageResourceInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchVirtualImageResource", arg0, arg1)
	ret0, _ := ret[0].([]store.VirtualImageResourceInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchVirtualImageResource indicates an expected call of SearchVirtualImageResource.
func (mr *MockIStoreMockRecorder) SearchVirtualImageResource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchVirtualImageResource", reflect.TypeOf((*MockIStore)(nil).SearchVirtualImageResource), arg0, arg1)
}

// SearchVirtualImageResourceCount mocks base method.
func (m *MockIStore) SearchVirtualImageResourceCount(arg0 context.Context, arg1 *virtual_image_resource.SearchVirtualImageResourceRequest) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchVirtualImageResourceCount", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchVirtualImageResourceCount indicates an expected call of SearchVirtualImageResourceCount.
func (mr *MockIStoreMockRecorder) SearchVirtualImageResourceCount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchVirtualImageResourceCount", reflect.TypeOf((*MockIStore)(nil).SearchVirtualImageResourceCount), arg0, arg1)
}

// SetVirtualImageResourceForSale mocks base method.
func (m *MockIStore) SetVirtualImageResourceForSale(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetVirtualImageResourceForSale", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetVirtualImageResourceForSale indicates an expected call of SetVirtualImageResourceForSale.
func (mr *MockIStoreMockRecorder) SetVirtualImageResourceForSale(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetVirtualImageResourceForSale", reflect.TypeOf((*MockIStore)(nil).SetVirtualImageResourceForSale), arg0, arg1)
}

// UpdateIconByID mocks base method.
func (m *MockIStore) UpdateIconByID(arg0 context.Context, arg1 *virtual_image_resource.VirtualImageResourceInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateIconByID", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateIconByID indicates an expected call of UpdateIconByID.
func (mr *MockIStoreMockRecorder) UpdateIconByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateIconByID", reflect.TypeOf((*MockIStore)(nil).UpdateIconByID), arg0, arg1)
}

// UpdateLevelConfig mocks base method.
func (m *MockIStore) UpdateLevelConfig(arg0 context.Context, arg1 *virtual_image_resource.LevelConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLevelConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateLevelConfig indicates an expected call of UpdateLevelConfig.
func (mr *MockIStoreMockRecorder) UpdateLevelConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLevelConfig", reflect.TypeOf((*MockIStore)(nil).UpdateLevelConfig), arg0, arg1)
}

// UpdateVirtualImageResource mocks base method.
func (m *MockIStore) UpdateVirtualImageResource(arg0 context.Context, arg1 *virtual_image_resource.VirtualImageResourceInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateVirtualImageResource", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateVirtualImageResource indicates an expected call of UpdateVirtualImageResource.
func (mr *MockIStoreMockRecorder) UpdateVirtualImageResource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateVirtualImageResource", reflect.TypeOf((*MockIStore)(nil).UpdateVirtualImageResource), arg0, arg1)
}

// UpdateVirtualImageResourceCustomMap mocks base method.
func (m *MockIStore) UpdateVirtualImageResourceCustomMap(arg0 context.Context, arg1 *store.VirtualImageResourceInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateVirtualImageResourceCustomMap", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateVirtualImageResourceCustomMap indicates an expected call of UpdateVirtualImageResourceCustomMap.
func (mr *MockIStoreMockRecorder) UpdateVirtualImageResourceCustomMap(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateVirtualImageResourceCustomMap", reflect.TypeOf((*MockIStore)(nil).UpdateVirtualImageResourceCustomMap), arg0, arg1)
}

// UpdateVirtualImageResourceStatus mocks base method.
func (m *MockIStore) UpdateVirtualImageResourceStatus(arg0 context.Context, arg1 []uint32, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateVirtualImageResourceStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateVirtualImageResourceStatus indicates an expected call of UpdateVirtualImageResourceStatus.
func (mr *MockIStoreMockRecorder) UpdateVirtualImageResourceStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateVirtualImageResourceStatus", reflect.TypeOf((*MockIStore)(nil).UpdateVirtualImageResourceStatus), arg0, arg1, arg2)
}

// UpdateVirtualImageResourceUrl mocks base method.
func (m *MockIStore) UpdateVirtualImageResourceUrl(arg0 context.Context, arg1 *virtual_image_resource.UpdateVirtualImageResourceUrlRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateVirtualImageResourceUrl", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateVirtualImageResourceUrl indicates an expected call of UpdateVirtualImageResourceUrl.
func (mr *MockIStoreMockRecorder) UpdateVirtualImageResourceUrl(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateVirtualImageResourceUrl", reflect.TypeOf((*MockIStore)(nil).UpdateVirtualImageResourceUrl), arg0, arg1)
}
