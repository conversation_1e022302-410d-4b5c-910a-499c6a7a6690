package conf

import (
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
)

type StartConfig struct {
	// from config file
	MysqlConfig *mysqlConnect.MysqlConfig `json:"mysql"`
	AppID       string                    `json:"app_id"`
	AppSecret   string                    `json:"app_secret"`

	AppToken string `json:"app_token"` // 表格令牌
	TableId  string `json:"table_id"`  // Lark table ID, used for Lark API requests
	ViewId   string `json:"view_id"`   // Lark view ID, used for Lark API requests
}
