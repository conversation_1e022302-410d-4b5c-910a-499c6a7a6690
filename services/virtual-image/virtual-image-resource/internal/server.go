package internal

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	"golang.52tt.com/protocol/services/demo/echo"
	"golang.52tt.com/protocol/services/virtual-image-resource"
	"golang.52tt.com/services/virtual-image/virtual-image-resource/internal/conf"
	lark_mgr "golang.52tt.com/services/virtual-image/virtual-image-resource/internal/model/lark-mgr"
	notice_cfg "golang.52tt.com/services/virtual-image/virtual-image-resource/internal/model/notice-cfg"
	"golang.52tt.com/services/virtual-image/virtual-image-resource/internal/model/resource-cfg"
	context0 "golang.org/x/net/context"
	"time"
)

func NewServer(ctx context.Context, cfg *conf.StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	mysqlDBCli, err := mysqlConnect.NewClient(ctx, cfg.MysqlConfig)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer fail to mysqlConnect.NewClient, %+v, err:%v", cfg.MysqlConfig, err)
		return nil, err
	}

	bc, err := conf.NewBusinessConfManager()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer fail to NewBusinessConfManager err:%v", err)
		return nil, err
	}

	cfgMgr, err := resource_cfg.NewMgr(ctx, mysqlDBCli, bc)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer fail to resource_cfg.NewMgr, %+v, err:%v", cfg.MysqlConfig, err)
		return nil, err
	}

	noticeMgr, err := notice_cfg.NewMgr(mysqlDBCli)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer fail to notice_cfg.NewMgr, %+v, err:%v", cfg.MysqlConfig, err)
		return nil, err
	}

	larkMgr := lark_mgr.NewLarkMgr(cfg)
	if larkMgr == nil {
		log.ErrorWithCtx(ctx, "NewServer fail to lark_mgr.NewLarkMgr, %+v, err:%v", cfg, err)
		return nil, err
	}

	s := &Server{
		bc:        bc,
		cfgMgr:    cfgMgr,
		noticeMgr: noticeMgr,
		larkMgr:   larkMgr,
	}

	return s, nil
}

type Server struct {
	bc        conf.IBusinessConfManager
	cfgMgr    resource_cfg.IMgr
	noticeMgr notice_cfg.IMgr
	larkMgr   lark_mgr.IMgr
}

func (s *Server) SetResourceToTest(ctx context.Context, request *virtual_image_resource.SetResourceToTestRequest) (*virtual_image_resource.SetResourceToTestResponse, error) {
	out := &virtual_image_resource.SetResourceToTestResponse{}

	go func() {
		goCtx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
		defer cancel()
		mapIDList := make(map[uint32]uint32, 0)
		for _, item := range request.GetChangeList() {
			//先找单品
			log.DebugWithCtx(goCtx, "SetResourceToTest: item:%+v", item)
			records, err := s.larkMgr.SearchRecordByFieldNameAndValue(goCtx, "物品ID", fmt.Sprintf("%d", item.GetResourceId()))
			if err != nil {
				log.ErrorWithCtx(goCtx, "SetResourceToTest fail to larkMgr.SearchRecordByFieldNameAndValue, err:%v", err)
				continue
			}

			//找不到单品，再找套装
			if len(records) == 0 {
				resourceList, err := s.cfgMgr.GetVirtualImageResourcesByIds(goCtx, &virtual_image_resource.GetVirtualImageResourcesByIdsRequest{Ids: []uint32{item.GetResourceId()}})
				if err != nil {
					log.ErrorWithCtx(goCtx, "SetResourceToTest fail to cfgMgr.GetVirtualImageResourcesByIds, err:%v", err)
					return
				}

				if len(resourceList.GetResources()) == 0 {
					continue
				}

				resource := resourceList.GetResources()[0]
				suitName := resource.GetDefaultSuit()
				if resource.GetResourcePrefix() == "c" {
					suitName = "c" + suitName
				}
				records, err = s.larkMgr.SearchRecordByFieldNameAndValue(goCtx, "套装ID/物品码", suitName)
				if err != nil {
					log.ErrorWithCtx(goCtx, "SetResourceToTest fail to larkMgr.SearchRecordByFieldNameAndValue, err:%v", err)
					continue
				}
			}

			for _, record := range records {
				if record.Fields.Progress != "测试完成" {
					continue
				}

				mapIDList[item.GetResourceId()] = item.GetResourceId()
				err = s.larkMgr.UpdateSheetByRecordID(goCtx, record.RecordId, "进度", "待测试")
				if err != nil {
					log.ErrorWithCtx(goCtx, "SetResourceToTest fail to larkMgr.UpdateSheetByRecordID, err:%v", err)
					continue
				}

				err = s.larkMgr.UpdateSheetByRecordID(goCtx, record.RecordId, "修改原因", item.GetModifyReason())
				if err != nil {
					log.ErrorWithCtx(ctx, "SetResourceToTest fail to larkMgr.UpdateSheetByRecordID, err:%v", err)
					continue
				}

				err = s.larkMgr.UpdateSheetByRecordID(goCtx, record.RecordId, "修改人", item.GetOperator())
				if err != nil {
					log.ErrorWithCtx(ctx, "SetResourceToTest fail to larkMgr.UpdateSheetByRecordID, err:%v", err)
					continue
				}
			}
		}

		idList := make([]uint32, 0, len(mapIDList))
		for k := range mapIDList {
			idList = append(idList, k)
		}

		if len(idList) == 0 {
			return
		}

		err := s.cfgMgr.UpdateVirtualImageResourceStatus(goCtx, idList, uint32(virtual_image_resource.VirtualImageResourceStatus_VIRTUAL_IMAGE_RESOURCE_STATUS_UPLOADED_PENDING_TEST))
		if err != nil {
			log.ErrorWithCtx(goCtx, "SetResourceToTest fail to cfgMgr.UpdateResourceStatus, err:%v", err)
		}
	}()

	return out, nil
}

func (s *Server) GetActionResourceMap(ctx context0.Context, request *virtual_image_resource.GetActionResourceMapRequest) (*virtual_image_resource.GetActionResourceMapResponse, error) {
	return s.cfgMgr.GetActionResourceMap(ctx)
}

func (s *Server) GetLevelConfig(ctx context.Context, request *virtual_image_resource.GetLevelConfigRequest) (*virtual_image_resource.GetLevelConfigResponse, error) {
	return s.cfgMgr.GetLevelConfig(ctx)
}

func (s *Server) UpdateLevelConfig(ctx context.Context, request *virtual_image_resource.UpdateLevelConfigRequest) (*virtual_image_resource.UpdateLevelConfigResponse, error) {
	out := &virtual_image_resource.UpdateLevelConfigResponse{}
	return out, s.cfgMgr.UpdateLevelConfig(ctx, request.GetCfg())
}

func (s *Server) AddLevelConfig(ctx context.Context, request *virtual_image_resource.AddLevelConfigRequest) (*virtual_image_resource.AddLevelConfigResponse, error) {
	out := &virtual_image_resource.AddLevelConfigResponse{}
	return out, s.cfgMgr.AddLevelConfig(ctx, request.GetCfg())
}

func (s *Server) GetDefaultResourceList(ctx context0.Context, request *virtual_image_resource.GetDefaultResourceListRequest) (*virtual_image_resource.GetDefaultResourceListResponse, error) {
	return s.cfgMgr.GetDefaultResourceList(ctx)
}

func (s *Server) BatchUpdateIcon(ctx context.Context, request *virtual_image_resource.BatchUpdateIconRequest) (*virtual_image_resource.BatchUpdateIconResponse, error) {
	out := &virtual_image_resource.BatchUpdateIconResponse{}
	return out, s.cfgMgr.BatchUpdateIcon(ctx, request)
}

func (s *Server) GetVirtualImageResourceBySuit(ctx context.Context, request *virtual_image_resource.GetVirtualImageResourceBySuitRequest) (*virtual_image_resource.GetVirtualImageResourceBySuitResponse, error) {
	return s.cfgMgr.GetVirtualImageResourceBySuit(ctx)
}

func (s *Server) CloneVirtualImageResource(ctx context.Context, request *virtual_image_resource.CloneVirtualImageResourceRequest) (*virtual_image_resource.CloneVirtualImageResourceResponse, error) {
	return s.cfgMgr.CloneVirtualImageResource(ctx, request)
}

func (s *Server) SetVirtualImageResourceForSale(ctx context.Context, request *virtual_image_resource.SetVirtualImageResourceForSaleRequest) (*virtual_image_resource.SetVirtualImageResourceForSaleResponse, error) {
	out := &virtual_image_resource.SetVirtualImageResourceForSaleResponse{}
	return out, s.cfgMgr.SetVirtualImageResourceForSale(ctx, request.GetId())
}

func (s *Server) UpdateVirtualImageResourceUrl(ctx context.Context, request *virtual_image_resource.UpdateVirtualImageResourceUrlRequest) (*virtual_image_resource.UpdateVirtualImageResourceUrlResponse, error) {
	return s.cfgMgr.UpdateVirtualImageResourceUrl(ctx, request)
}

func (s *Server) SearchVirtualImageResource(ctx context.Context, request *virtual_image_resource.SearchVirtualImageResourceRequest) (*virtual_image_resource.SearchVirtualImageResourceResponse, error) {
	return s.cfgMgr.SearchVirtualImageResource(ctx, request)
}

func (s *Server) DeleteVirtualImageResource(ctx context.Context, request *virtual_image_resource.DeleteVirtualImageResourceRequest) (*virtual_image_resource.DeleteVirtualImageResourceResponse, error) {
	out := &virtual_image_resource.DeleteVirtualImageResourceResponse{}
	return out, s.cfgMgr.DeleteVirtualImageResource(ctx, request)
}

func (s *Server) GetVirtualImageResourcesByIds(ctx context.Context, request *virtual_image_resource.GetVirtualImageResourcesByIdsRequest) (*virtual_image_resource.GetVirtualImageResourcesByIdsResponse, error) {
	return s.cfgMgr.GetVirtualImageResourcesByIds(ctx, request)
}

func (s *Server) GetVirtualImageResourceCategory(ctx context.Context, req *virtual_image_resource.GetVirtualImageResourceCategoryRequest) (*virtual_image_resource.GetVirtualImageResourceCategoryResponse, error) {
	log.DebugWithCtx(ctx, "GetVirtualImageResourceCategory: %v", req)
	out, err := s.cfgMgr.GetVirtualImageResourceCategory(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetVirtualImageResourceCategory err:%v", err)
		return nil, err
	}

	log.DebugWithCtx(ctx, "GetVirtualImageResourceCategory: req:%v, out:%v", req, out)
	return out, nil
}

func (s *Server) AddVirtualImageResource(ctx context.Context, request *virtual_image_resource.AddVirtualImageResourceRequest) (*virtual_image_resource.AddVirtualImageResourceResponse, error) {
	return s.cfgMgr.AddVirtualImageResource(ctx, request)
}

func (s *Server) EditVirtualImageResource(ctx context.Context, request *virtual_image_resource.EditVirtualImageResourceRequest) (*virtual_image_resource.EditVirtualImageResourceResponse, error) {
	return s.cfgMgr.EditVirtualImageResource(ctx, request)
}

func (s *Server) GetClientListByPage(ctx context.Context, request *virtual_image_resource.GetClientListByPageRequest) (*virtual_image_resource.GetClientListByPageResponse, error) {
	return s.cfgMgr.GetClientListByPage(ctx, request)
}

func (s *Server) ShutDown() {

}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (s *Server) GetNoticeCfgCache(ctx context.Context, request *virtual_image_resource.GetNoticeCfgCacheRequest) (*virtual_image_resource.GetNoticeCfgCacheResponse, error) {
	out := &virtual_image_resource.GetNoticeCfgCacheResponse{}
	cfg := s.noticeMgr.GetNoticeCfgCache(ctx)

	now := time.Now()
	if now.Unix() <= cfg.EndTime {
		// 还未过期
		out.Cfg = cfg
	}

	return out, nil
}

func (s *Server) GetNoticeCfg(ctx context.Context, request *virtual_image_resource.GetNoticeCfgRequest) (*virtual_image_resource.GetNoticeCfgResponse, error) {
	out := &virtual_image_resource.GetNoticeCfgResponse{}
	var err error

	out.Cfg, err = s.noticeMgr.GetNoticeCfg(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNoticeCfg fail to GetNoticeCfg. err:%v", err)
		return out, err
	}

	return out, nil
}

func (s *Server) UpdateNoticeCfg(ctx context.Context, request *virtual_image_resource.UpdateNoticeCfgRequest) (*virtual_image_resource.UpdateNoticeCfgResponse, error) {
	out := &virtual_image_resource.UpdateNoticeCfgResponse{}
	err := s.noticeMgr.UpdateNoticeCfg(ctx, request.GetCfg())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateNoticeCfg fail to UpdateNoticeCfg. err:%v", err)
		return out, err
	}

	return out, nil
}
