package manager

//go:generate quicksilver-cli test interface ../manager
//go:generate mockgen -destination=./mocks/manager.go -package=mocks golang.52tt.com/services/virtual-image/virtual-image-logic/manager IVirtualImageLogicMgr

import (
	"context"
	"fmt"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	channelIm "golang.52tt.com/clients/channelim"
	"golang.52tt.com/clients/channelol"
	im_api "golang.52tt.com/clients/im-api"
	numeric_go "golang.52tt.com/clients/numeric-go"
	push "golang.52tt.com/clients/push-notification/v2"
	risk_mng_api "golang.52tt.com/clients/risk-mng-api"
	usual_device "golang.52tt.com/clients/usual-device"
	channelMsg "golang.52tt.com/pkg/channel-msg"
    "golang.52tt.com/pkg/ttversion"
    channel_go "golang.52tt.com/protocol/services/channel-go"
	channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
	virtual_image_card "golang.52tt.com/protocol/services/virtual-image-card"
	anchorCheckConf "golang.52tt.com/services/anchor-check/conf"
	"golang.52tt.com/services/virtual-image/virtual-image-logic/util"
	"google.golang.org/grpc/codes"
	"net/http"
	"time"

	"golang.52tt.com/clients/account"
	"golang.52tt.com/pkg/tbean"

	presenceV2 "golang.52tt.com/clients/presence/v2"
	superplayerprivilege "golang.52tt.com/clients/super-player-privilege"
	friendShip "golang.52tt.com/clients/ugc/friendship"
	unifiedPay "golang.52tt.com/clients/unified_pay"
	userBlackList "golang.52tt.com/clients/user-black-list"
	userProfile "golang.52tt.com/clients/user-profile-api"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/pkg/reporter"
	sentinel_interceptor "golang.52tt.com/pkg/sentinel/interceptor"
	pb "golang.52tt.com/protocol/app/virtual_image_logic"
	"golang.52tt.com/protocol/common/status"
	mallPb "golang.52tt.com/protocol/services/virtual-image-mall"
	virtualimageresource "golang.52tt.com/protocol/services/virtual-image-resource"
	userPb "golang.52tt.com/protocol/services/virtual-image-user"
	virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
	cache "golang.52tt.com/services/virtual-image/virtual-image-logic/cache"
	"golang.52tt.com/services/virtual-image/virtual-image-logic/conf"
	"google.golang.org/grpc"
)

type VirtualImageLogicMgr struct {
	dyconfig            conf.ISDyConfigHandler
	bc                  conf.IBusinessConfManager
	reporter            *reporter.FeishuReporter
	mallClient          mallPb.VirtualImageMallClient
	vUserClient         virtual_image_user.VirtualImageUserClient
	unifiedPayCli       unifiedPay.IClient
	cfg                 *conf.StartConfig
	resourceCli         virtualimageresource.VirtualImageResourceClient
	tbeanClient         tbean.Client
	//userCli             userPb.VirtualImageUserClient
	accountCli          account.IClient
	userProfileCli      userProfile.IClient
	friendCli           friendShip.IClient
	presenceV2Cli       presenceV2.IClient
	anchorCheckDyconfig *anchorCheckConf.SDyConfigHandler
	apiCenterClient     apicenter.IClient
	userBlackCli        userBlackList.IClient
	localCache          *cache.LocalCache
	superPlayerCli      superplayerprivilege.IClient
	RiskMngCli          risk_mng_api.IClient
	channelCli          channel_go.ChannelGoClient
	usualDeviceClient   usual_device.IClient
	channelImCli        channelIm.IClient
	channelOLCli        channelol.IClient
	channelMsgSender    channelMsg.ISender
	ImApiCli            im_api.IClient
	weddingCli          channel_wedding.ChannelWeddingClient
	pushCli             push.IClient
	httpClient          *http.Client
	NumericGoCli        numeric_go.IClient
	cardCli             virtual_image_card.VirtualImageCardClient
}

func NewVirtualImageLogicMgr(cfg *conf.StartConfig, reporter *reporter.FeishuReporter) (*VirtualImageLogicMgr, error) {

	log.Infof("NewVirtualImageLogicMgr conf:%v", cfg)

	opts := []grpc.DialOption{
		grpc.WithBlock(),
		grpc.WithChainUnaryInterceptor(
			//tests.UnaryClientInterceptor(),                // 测试拦截器
			sentinel_interceptor.UnaryClientInterceptor(), // 熔断拦截器
		),
	}

	dyconfig := conf.NewConfigHandler(conf.DyconfigPath)
	if err := dyconfig.Start(); err != nil {
		log.Errorf("dyconfig.Start fail %v", err)
		return nil, err
	}
	bsConfig_, err := conf.NewBusinessConfManager()
	if err != nil {
		log.Errorf("NewBusinessConfManager err: %v", err)
		return nil, err

	}

	mallClient := mallPb.MustNewClient(context.Background())
	vUserClient := virtual_image_user.MustNewClient(context.Background())

	unifiedPayCli, _ := unifiedPay.NewClient(opts...)
	resourceCli, _ := virtualimageresource.NewClient(context.Background())
	//userCli_, _ := userPb.NewClient(context.Background())
	accountCli_, _ := account.NewClient(opts...)
	userProfileCli := userProfile.NewIClient()
	friendCli := friendShip.NewIClient()
	presenceV2Cli, err := presenceV2.NewClient()
	riskMngCli := risk_mng_api.NewIClient()
	if err != nil {
		log.Errorf("NewEsportHallMgr presenceV2.NewClient err: %v", err)
		return nil, err
	}

	anchorCheckDyconfig := anchorCheckConf.NewConfigHandler(anchorCheckConf.DyconfigPath)
	if err := anchorCheckDyconfig.Start(); err != nil {
		log.Errorf("anchorCheckConf dyconfig.Start() fail %v", err)
		return nil, err
	}
	apiCenterClient := apicenter.NewIClient()

	userBlackCli := userBlackList.NewIClient()

	localCache_ := cache.NewLocalCache()

	superPlayPriCli_ := superplayerprivilege.NewIClient()

	channelCli := channel_go.MustNewClient(context.Background())
	usualDeviceClient := usual_device.NewIClient()
	channelIMCli := channelIm.NewClient()
	channelOLCli := channelol.NewClient()
	channelMsgSender := channelMsg.NewISender()
	imApiCli, _ := im_api.NewClient()

	weddingCli := channel_wedding.MustNewClient(context.Background())

	pushCli := push.NewIClient()
	NumericGoCli := numeric_go.NewIClient()
	cardCli := virtual_image_card.MustNewClient(context.Background())

	mgr := &VirtualImageLogicMgr{
		dyconfig:            dyconfig,
		mallClient:          mallClient,
		vUserClient:         vUserClient,
		reporter:            reporter,
		unifiedPayCli:       unifiedPayCli,
		cfg:                 cfg,
		resourceCli:         resourceCli,
		tbeanClient:         tbean.NewClient(cfg.TbeanContextPath),
		//userCli:             userCli_,
		accountCli:          accountCli_,
		userProfileCli:      userProfileCli,
		friendCli:           friendCli,
		presenceV2Cli:       presenceV2Cli,
		anchorCheckDyconfig: anchorCheckDyconfig,
		apiCenterClient:     apiCenterClient,
		userBlackCli:        userBlackCli,
		localCache:          localCache_,
		superPlayerCli:      superPlayPriCli_,
		bc:                  bsConfig_,
		RiskMngCli:          riskMngCli,
		channelCli:          channelCli,
		usualDeviceClient:   usualDeviceClient,
		channelImCli:        channelIMCli,
		channelOLCli:        channelOLCli,
		channelMsgSender:    channelMsgSender,
		ImApiCli:            imApiCli,
		weddingCli:          weddingCli,
		pushCli:             pushCli,
		httpClient:          &http.Client{Timeout: time.Second * 2},
		NumericGoCli:        NumericGoCli,
		cardCli:             cardCli,
	}
	mgr.ResourceCategoryLoad()

	return mgr, nil
}

func (m *VirtualImageLogicMgr) ShutDown() {
}

func (m *VirtualImageLogicMgr) transSkinMap(skinMap map[string]*virtualimageresource.SkinInfo) map[string]*pb.SkinInfo {
	out := make(map[string]*pb.SkinInfo)
	for k, v := range skinMap {
		out[k] = &pb.SkinInfo{
			Url:             v.GetUrl(),
			MinBonesVersion: v.GetMinBonesVersion(),
			Md5:             v.GetMd5(),
		}
	}
	return out
}

func (m *VirtualImageLogicMgr) GetResourceList(ctx context.Context, in *pb.GetResourceListRequest) (*pb.GetResourceListResponse, error) {
	log.InfoWithCtx(ctx, "GetResourceList in:%+v", in)
	out := &pb.GetResourceListResponse{}
	list, err := m.resourceCli.GetClientListByPage(ctx, &virtualimageresource.GetClientListByPageRequest{
		Limit:    in.GetLimit(),
		Offset:   in.GetOffset(),
		LatestId: in.GetLatestVersion(),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "GetClientListByPage err:%v", err)
		return out, err
	}

	out.Limit = in.GetLimit()
	out.Offset = in.GetOffset()
	out.LatestVersion = list.GetLatestId()
	out.IsEnd = list.GetIsEnd()
	out.DownloadUrl = list.GetDownloadUrl()
	out.DownloadMd5 = list.GetDownloadMd5()
	for _, data := range list.GetResources() {
		out.Resources = append(out.Resources, &pb.VirtualImageResourceInfo{
			Id:               data.GetId(),
			SkinName:         data.GetSkinName(),
			ResourceType:     data.GetResourceType(),
			ResourceName:     data.GetResourceName(),
			ResourceUrl:      data.GetResourceUrl(),
			Version:          data.GetVersion(),
			Essential:        data.GetEssential(),
			ShelfTime:        data.GetShelfTime(),
			ExpireTime:       data.GetExpireTime(),
			EncryptKey:       data.GetEncryptKey(),
			DisplayName:      data.GetDisplayName(),
			IconUrl:          data.GetIconUrl(),
			Category:         data.GetCategory(),
			SubCategory:      data.GetSubCategory(),
			Md5:              data.GetMd5(),
			Level:            data.GetLevel(),
			LevelIcon:        data.GetLevelIcon(),
			Sex:              data.GetSex(),
			LevelWebp:        data.GetLevelWebp(),
			ScaleAble:        data.GetScaleAble(),
			DefaultAnimation: data.GetDefaultAnimation(),
			ResourcePrefix:   data.GetResourcePrefix(),
			CustomMap:        data.GetCustomMap(),
			SkinMap:          m.transSkinMap(data.GetSkinMap()),
			IosSkinMap:       m.transSkinMap(data.GetIosSkinMap()),
			IosResourceUrl:   data.GetResourceUrl(),
			IosVersion:       data.GetIosVersion(),
			IsNewResource:    data.GetIsNewResource(),
			TextureUrlMap:    data.GetTextureUrlMap(),
		})
	}
	return out, nil
}

func (m *VirtualImageLogicMgr) checkUsualDevice(ctx context.Context) error {
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}

	uid := serviceInfo.UserID
	res, sErr := m.usualDeviceClient.CheckUsualDevice(ctx, string(serviceInfo.DeviceID), uid, 1, uint32(serviceInfo.ClientType))
	if sErr != nil {
		log.ErrorWithCtx(ctx, "checkUsualDevice Fail to CheckUsualDevice err(%v)", sErr)
		return sErr
	}
	if !res.GetResult() {
		err := m.usualDeviceClient.GetDeviceAuthError(ctx, uint64(uid), serviceInfo.ClientType, serviceInfo.ClientVersion)
		if err != nil {
			return err
		}
	}
	return nil
}

func (m *VirtualImageLogicMgr) CheckFrozenOrderList(ctx context.Context) {
	orderResp, err := m.mallClient.GetCommodityDataOrdersPaying(ctx, &mallPb.GetCommodityDataOrdersPayingRequest{})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommodityDataOrdersPaying err:%v", err)
		return
	}
	log.InfoWithCtx(ctx, "CheckPayingOrderList", orderResp.GetOrders())
	for _, order := range orderResp.GetOrders() {
		m.ShippedCommodityData(ctx, order.GetUid(), []*mallPb.CommodityDataOrders{order})
	}
}

func (m *VirtualImageLogicMgr) RefreshVirtualImageRedDotAlert(ctx context.Context) {
	log.DebugWithCtx(ctx, "RefreshVirtualImageRedDotAlert")

	packageDataList, err := m.mallClient.GetUnRefreshedRedDotPackageData(ctx, &mallPb.GetUnRefreshedRedDotPackageDataReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUnRefreshedRedDotPackageData err:%v", err)
		return
	}
	packageDataMap := make(map[uint32]*mallPb.CommodityDataPackage)
	commodityIdList := make([]uint32, 0)
	for _, packageData := range packageDataList.GetPackageList() {
		commodityIdList = append(commodityIdList, packageData.GetCommodityId())
		packageDataMap[packageData.GetPackageId()] = packageData
	}
	log.DebugWithCtx(ctx, "RefreshVirtualImageRedDotAlert packageDataMap:%v", packageDataMap)

	dataResp, err := m.mallClient.GetCommodityDataList(ctx, &mallPb.GetCommodityDataListRequest{
		CommodityIdList: commodityIdList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCommodityDataList commodityIdList:%v err:%v", commodityIdList, err)
		return
	}
	commodityDataMap := make(map[uint32]*mallPb.CommodityData)
	log.DebugWithCtx(ctx, "RefreshVirtualImageRedDotAlert GetCommodityDataList:%v", len(dataResp.GetCommodityDataList()))
	for _, commodityData := range dataResp.GetCommodityDataList() {
		for _, tmpPackageData := range commodityData.GetPricePackageList() {
			log.DebugWithCtx(ctx, "RefreshVirtualImageRedDotAlert GetPackageId:%v", tmpPackageData.GetPackageId())
			if _, ok := packageDataMap[tmpPackageData.GetPackageId()]; ok {
				continue
			}
			log.DebugWithCtx(ctx, "RefreshVirtualImageRedDotAlert GetPackageId:%v", tmpPackageData.GetPackageId())
			if val, ok := commodityDataMap[tmpPackageData.GetCommodityId()]; ok {
				if val.GetShelfTime() > tmpPackageData.GetShelfTime() {
					val.ShelfTime = tmpPackageData.GetShelfTime()
				}
				if val.GetExpireTime() < tmpPackageData.GetExpireTime() {
					val.ExpireTime = tmpPackageData.GetExpireTime()
				}
			} else {
				commodityDataMap[tmpPackageData.GetCommodityId()] = &mallPb.CommodityData{
					CommodityId: tmpPackageData.GetCommodityId(),
					ShelfTime:   tmpPackageData.GetShelfTime(),
					ExpireTime:  tmpPackageData.GetExpireTime(),
				}
			}
		}
	}

	for _, commodityData := range dataResp.GetCommodityDataList() {
		category := commodityData.GetCategory()
		if commodityData.GetCommodityType() == uint32(mallPb.CommodityType_COMMODITY_TYPE_SUIT) {
			category = uint32(virtualimageresource.VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_FACE_SUIT)
		}
		log.DebugWithCtx(ctx, "RefreshVirtualImageRedDotAlert commodityData:%v", commodityData.GetCommodityId())
		var btime, etime uint32
		isContinue := false
		btime = uint32(time.Now().Unix())
		etime = uint32(time.Now().Unix())
		if tmpCommodityData, ok := commodityDataMap[commodityData.GetCommodityId()]; ok {
			log.DebugWithCtx(ctx, "RefreshVirtualImageRedDotAlert GetPricePackageList:%v", commodityData.GetPricePackageList())
			for _, tmpPackageData := range commodityData.GetPricePackageList() {
				log.DebugWithCtx(ctx, "RefreshVirtualImageRedDotAlert id:%v", tmpPackageData.GetPackageId())
				if val, ok := packageDataMap[tmpPackageData.GetPackageId()]; ok {
					if val.GetShelfTime() < tmpCommodityData.GetExpireTime() && val.GetExpireTime() > tmpCommodityData.GetShelfTime() {
						isContinue = true
						btime = tmpCommodityData.GetShelfTime()
						etime = tmpCommodityData.GetExpireTime()
					}
					log.DebugWithCtx(ctx, "RefreshVirtualImageRedDotAlert  packageData:%v ", tmpPackageData)
					break
				}
			}
		}
		for _, tmpPackageData := range commodityData.GetPricePackageList() {
			if tmpPackageData.GetIsRefreshedRedDot() == false {
				if btime > tmpPackageData.GetShelfTime() {
					btime = tmpPackageData.GetShelfTime()
				}
				if etime < tmpPackageData.GetExpireTime() {
					etime = tmpPackageData.GetExpireTime()
				}
				log.DebugWithCtx(ctx, "RefreshVirtualImageRedDotAlert UpdateCommodityPackageRedDot PackageId:%v category:%v subCategory:%v commodityId:%v isContinue:%v btime:%v etime:%v",
					tmpPackageData.GetPackageId(), category, commodityData.GetSubCategory(), commodityData.GetCommodityId(), isContinue, btime, etime)
				_, err := m.mallClient.UpdateCommodityPackageRedDot(ctx, &mallPb.UpdateCommodityPackageRedDotReq{
					PackageId:         tmpPackageData.GetPackageId(),
					IsRefreshedRedDot: true,
					Category:          category,
					SubCategory:       commodityData.GetSubCategory(),
					CommodityId:       commodityData.GetCommodityId(),
					IsContinue:        isContinue,
					ShelfTime:         btime + 3611,
					ExpireTime:        etime + 3611,
				})
				if err != nil {
					log.ErrorWithCtx(ctx, "UpdateCommodityPackageRedDot PackageId:%v err:%v", tmpPackageData.GetPackageId(), err)
				}

				if isContinue {
					log.InfoWithCtx(ctx, "RefreshVirtualImageRedDotAlert>>>  PackageId:%v category:%v subCategory:%v commodityId:%v isContinue:%v btime:%v etime:%v",
						tmpPackageData.GetPackageId(), category, commodityData.GetSubCategory(), commodityData.GetCommodityId(), isContinue, btime, etime)
				}
			}
		}
	}
}

func (m *VirtualImageLogicMgr) GetVirtualImageCommodityRedDot(ctx context.Context, in *pb.GetVirtualImageCommodityRedDotRequest) (*pb.GetVirtualImageCommodityRedDotResponse, error) {
	log.DebugWithCtx(ctx, "GetVirtualImageCommodityRedDot in:%+v", in)
	out := &pb.GetVirtualImageCommodityRedDotResponse{}
	resp, err := m.mallClient.BatchGetCommodityRedDot(ctx, &mallPb.BatchGetCommodityRedDotReq{
		IsGetDetailedData: false,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetVirtualImageCommodityRedDot err:%v", err)
		return out, err
	}
	out.GlobalVersion = resp.GetGlobalVersion()
	log.DebugWithCtx(ctx, "GetVirtualImageCommodityRedDot in:%+v out:%v", in, out)
	return out, nil

}

func (m *VirtualImageLogicMgr) GetVirtualImageResourceCategory(ctx context.Context, in *pb.GetVirtualImageResourceCategoryRequest) (*pb.GetVirtualImageResourceCategoryResponse, error) {
	log.DebugWithCtx(ctx, "GetVirtualImageResourceCategory in:%+v", in)
	out := &pb.GetVirtualImageResourceCategoryResponse{}

	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	clientType := serviceInfo.ClientType
	categoryInfoList := m.localCache.GetAllResourceCategoryInfos()
	if len(categoryInfoList) == 0 {
		m.ResourceCategoryLoad()
		categoryInfoList = m.localCache.GetAllResourceCategoryInfos()
	}

	// 新版本隐藏推荐tab开关
    cardRightsCommodityMinVer := ttversion.Parse("无限换装卡类商品最低支持版本", m.dyconfig.GetCardMinCliVersion()...)
	isHideRecommendTab := m.dyconfig.GetHideRecommendTab() && cardRightsCommodityMinVer.Atleast(serviceInfo.ClientType, serviceInfo.ClientVersion)

	for _, data := range categoryInfoList {
		if isHideRecommendTab &&
			data.GetParentCategoryInfo().GetCategoryType() == uint32(pb.VirtualImageResourceCategoryType_VIRTUAL_IMAGE_RESOURCE_CATEGORY_TYPE_RECOMMEND) {
			// 隐藏推荐tab
			continue
		}

		tmpData := &pb.VirtualImageResourceCategoryInfo{
			ParentCategoryInfo: &pb.VirtualImageParentCategoryInfo{
				Category:     data.GetParentCategoryInfo().GetCategory(),
				CategoryName: data.GetParentCategoryInfo().GetCategoryName(),
				CategoryType: data.GetParentCategoryInfo().GetCategoryType(),
			},
		}
		for _, subData := range data.GetSubCategoryInfoList() {
			tmpSubData := &pb.VirtualImageSubCategoryInfo{
				SubCategory:              subData.GetSubCategory(),
				SubCategoryName:          subData.GetSubCategoryName(),
				SubCategoryImgUrl:        subData.GetSubCategoryImgUrl(),
				SubCategoryImgPreviewUrl: subData.GetSubCategoryImgUrlSelected(),
				CategoryType:             subData.GetSubCategoryType(),
			}
			if clientType == protocol.ClientTypePcTT {
				tmpSubData.SubCategoryImgUrl = subData.GetWebSubCategoryImgUrl()
				tmpSubData.SubCategoryImgPreviewUrl = subData.GetWebSubCategoryImgUrlSelected()
			}
			tmpData.SubCategoryInfoList = append(tmpData.GetSubCategoryInfoList(), tmpSubData)
		}
		out.ResourceCategoryInfoList = append(out.GetResourceCategoryInfoList(), tmpData)
	}

	var eventData UserEventData
	checkNewUrl := fmt.Sprintf("%s&uid=%d", m.dyconfig.GetCheckNewUserUrl(), serviceInfo.UserID)
	err := util.HttpGet(ctx, m.httpClient, checkNewUrl, &eventData)
	if err == nil {
		log.DebugWithCtx(ctx, "GetVirtualImageResourceCategory checkNewUrl:%s, eventData:%v", checkNewUrl, eventData)
		if eventData.Data == nil {
			out.IsNew = true
		}
	} else {
		log.ErrorWithCtx(ctx, "GetVirtualImageResourceCategory checkNewUrl:%s, HttpGet err:%v", checkNewUrl, err)
	}

	log.InfoWithCtx(ctx, "GetVirtualImageResourceCategory in:%+v out:%v", in, out)
	return out, nil
}

func (m *VirtualImageLogicMgr) GetDefaultResourceList(ctx context.Context) (*pb.GetDefaultResourceListResponse, error) {
	out := &pb.GetDefaultResourceListResponse{
		MaleAnimationMap:   make(map[uint32]uint32),
		FemaleAnimationMap: make(map[uint32]uint32),
		DatingHatMap:       make(map[uint32]uint32),
		MapMutualExclusion: make(map[uint32]*pb.MutualExclusion),
	}
	resp, err := m.resourceCli.GetDefaultResourceList(ctx, &virtualimageresource.GetDefaultResourceListRequest{})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDefaultResourceList err:%v", err)
		return out, err
	}

	out.MaleResources = resp.GetMaleResources()
	out.FemaleResources = resp.GetFemaleResources()
	out.OutFit = resp.GetOutFit()
	out.MaleAnimationMap = resp.GetMaleAnimationMap()
	out.FemaleAnimationMap = resp.GetFemaleAnimationMap()
	out.DatingHatMap = m.dyconfig.GetDatingHatMap()
	out.UnShowList = resp.GetUnShowList()

	for k, v := range m.dyconfig.GetMapMutualExclusion() {
		out.MapMutualExclusion[k] = &pb.MutualExclusion{
			Category: v,
		}
	}

	out.GuideUrl = m.dyconfig.GetBeginnerGuidanceSourceUrl()
	out.GuideUrlMd5 = m.dyconfig.GetBeginnerGuidanceSourceUrlMd5()
	return out, nil
}

func (m *VirtualImageLogicMgr) RedDotAlertReaded(ctx context.Context, redDotAlerType, category, subCategory uint32) error {

	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID
	log.DebugWithCtx(ctx, "RedDotAlertReaded uid:%v redDotAlerType:%+v category:%v, subCategory:%v", uid, redDotAlerType, category, subCategory)

	_, err := m.mallClient.CommodityUserReadRedDot(ctx, &mallPb.CommodityUserReadRedDotReq{
		Uid:            uid,
		RedDotAlerType: redDotAlerType,
		Category:       category,
		SubCategory:    subCategory,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CommodityUserReadRedDot err:%v", err)
		return err
	}
	return nil

}

func (m *VirtualImageLogicMgr) GetRedDotAlertStatus(ctx context.Context, redDotAlerType uint32) (*pb.GetRedDotAlertStatusResponse, error) {
	out := &pb.GetRedDotAlertStatusResponse{}
	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID
	log.DebugWithCtx(ctx, "GetRedDotAlertStatus uid:%v redDotAlerType:%+v", uid, redDotAlerType)

	categoryList := make([]uint32, 0)
	categoryList = append(categoryList, uint32(virtualimageresource.VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_FACE_SHAPING))
	categoryList = append(categoryList, uint32(virtualimageresource.VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_DRESS))
	categoryList = append(categoryList, uint32(virtualimageresource.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SUIT))
	categoryList = append(categoryList, uint32(virtualimageresource.VirtualImageResourceCategory_VIRTUAL_IMAGE_RESOURCE_CATEGORY_ATMOSPHERE))
	out.RedDotAlerType = redDotAlerType
	unResp, err := m.mallClient.GetUserUnReadCommodityRedDot(ctx, &mallPb.GetUserUnReadCommodityRedDotReq{
		Uid:            uid,
		RedDotAlerType: redDotAlerType,
		CategoryList:   categoryList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserUnReadCommodityRedDot uid:%v err:%v", uid, err)
		return out, err
	}
	out.HasRedDot = unResp.GetHasRedDot()
	out.RedDotAlerType = redDotAlerType
	if redDotAlerType == uint32(pb.RedDotAlertType_RED_DOT_ALERT_TYPE_COMMODITY_TAB) {
		for _, categoryInfo := range unResp.GetCategoryList() {
			for _, subCategoryInfo := range categoryInfo.GetSubCategoryInfoList() {
				tmpData := &pb.CommodityTabRedDotInfo{
					Category:    categoryInfo.GetParentCategoryInfo().GetCategory(),
					SubCategory: subCategoryInfo.GetSubCategory(),
					HasRedDot:   true,
				}
				out.CommodityTabRedDotList = append(out.GetCommodityTabRedDotList(), tmpData)
			}
		}
	}
	log.DebugWithCtx(ctx, "GetRedDotAlertStatus uid:%v redDotAlerType:%+v out:%v", uid, redDotAlerType, out)

	return out, nil
}

func (m *VirtualImageLogicMgr) ResourceCategoryLoad() {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	resp, err := m.resourceCli.GetVirtualImageResourceCategory(ctx, &virtualimageresource.GetVirtualImageResourceCategoryRequest{})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetVirtualImageResourceCategory err:%v", err)
		return
	}
	m.localCache.UpdateAllResourceCategoryInfos(resp.GetResourceCategoryInfoList())
	log.DebugWithCtx(context.Background(), "ResourceCategoryLoad resp:%v", resp)
	return
}

func (m *VirtualImageLogicMgr) ShippedCommodityData(ctx context.Context, uid uint32, orderList []*mallPb.CommodityDataOrders) {
	bigOrderMap := make(map[string]uint32)
	for _, order := range orderList {
		isCommit := false
		log.DebugWithCtx(ctx, "uid:%v ShippedCommodityData order:%v", uid, order)
		items := make([]*userPb.ItemInfo, 0)
		if order.GetCommodityType() == uint32(pb.CommodityType_COMMODITY_TYPE_SUIT) {
			rResp, err := m.resourceCli.GetVirtualImageResourcesByIds(ctx, &virtualimageresource.GetVirtualImageResourcesByIdsRequest{
				Ids: order.GetResourceIdList(),
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "uid:%v GetVirtualImageResourcesByIds order:%v err:%v", uid, order.GetDataOrderId(), err)
				continue
			}
			for _, resource := range rResp.GetResources() {
				items = append(items, &userPb.ItemInfo{
					SubCategory: resource.GetSubCategory(),
					CfgId:       resource.GetId(),
				})
			}
		} else {
			for _, cfgId := range order.GetResourceIdList() {
				items = append(items, &userPb.ItemInfo{
					SubCategory: order.GetSubCategory(),
					CfgId:       cfgId,
				})
			}
		}

		giveReq := &userPb.GiveVirtualImageToUserReq{
			Uid:         uid,
			OutsideTime: int64(order.GetCreateTime()),
			Source:      uint32(userPb.VirtualImageGainSource_VIRTUAL_IMAGE_GAIN_MALL),
		}
		if order.GetCommodityType() == uint32(pb.CommodityType_COMMODITY_TYPE_SINGLE) {
			if len(items) > 0 {
				giveItems := make([]*userPb.ItemGiveInfo, 0)
				giveItems = append(giveItems, &userPb.ItemGiveInfo{
					OrderId:     order.GetDataOrderId(),
					DurationSec: int32(order.GetEffectiveDay() * 24 * 3600 * order.GetCount()),
					Item:        items[0],
					ItemPrice:   order.GetTotalPrice(),
				})
				giveReq.Items = giveItems
			}
		}
		if order.GetCommodityType() == uint32(pb.CommodityType_COMMODITY_TYPE_SUIT) {
			suitItems := make([]*userPb.SuitGiveInfo, 0)
			suitItems = append(suitItems, &userPb.SuitGiveInfo{
				OrderId:             order.GetDataOrderId(),
				DurationSec:         int32(order.GetEffectiveDay() * 24 * 3600 * order.GetCount()),
				Items:               items,
				SuitName:            order.GetCommodityName(),
				SuitIcon:            order.GetCommodityIcon(),
				LevelIcon:           order.GetLevelIcon(),
				SuitPrice:           order.GetTotalPrice(),
				PromotionResourceId: order.GetPromotionalVideoId(),
			})
			giveReq.Suits = suitItems
		}
		_, err := m.vUserClient.GiveVirtualImageToUser(ctx, giveReq)
		if err != nil && protocol.ToServerError(err).Code() != status.ErrVirtualAvatarOrderidExist {
			log.ErrorWithCtx(ctx, "uid:%v GiveVirtualImageToUser order:%v err:%v", uid, order.GetDataOrderId(), err)
			continue
		}

		if _, ok := bigOrderMap[order.GetBigTradeNo()]; !ok {
			bigOrderMap[order.GetBigTradeNo()] = order.GetTotalPrice()
			isCommit = true
		}
		log.DebugWithCtx(ctx, "uid:%v ShippedCommodityData order:%v isCommit:%v", uid, order.GetBigTradeNo(), isCommit)
		m.mallClient.UpdateCommodityDataOrdersStatus(ctx, &mallPb.UpdateCommodityDataOrdersStatusRequest{
			DataOrderId: order.GetDataOrderId(),
			BigTradeNo:  order.GetBigTradeNo(),
			PayStatus:   uint32(mallPb.CommodityPayStatus_COMMODITY_PAY_STATUS_SHIPPED),
			IsCommit:    isCommit,
			Uid:         order.GetUid(),
			CreateTime:  order.GetCreateTime(),
		})
	}
}

func (m *VirtualImageLogicMgr) GetLevelIconConfig(ctx context.Context) map[uint32]*virtualimageresource.LevelConfig {
	out := make(map[uint32]*virtualimageresource.LevelConfig)
	levelConf, lErr := m.resourceCli.GetLevelConfig(ctx, &virtualimageresource.GetLevelConfigRequest{})
	if lErr != nil {
		log.ErrorWithCtx(ctx, "GetLevelConfig err:%v", lErr)
		return out
	}

	for _, tmpConf := range levelConf.GetList() {
		out[tmpConf.Level] = tmpConf
	}
	return out
}

func (m *VirtualImageLogicMgr) GetVirtualImageBeginnerGuide(ctx context.Context, uid uint32) (bool, uint32, error) {
	hasGuide, err := m.vUserClient.GetVirtualImageBeginnerGuide(ctx, &virtual_image_user.GetVirtualImageBeginnerGuideRequest{
		Uid: uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetVirtualImageBeginnerGuide, uid: %d, err:%v", uid, err)
	}
	return hasGuide.HasGuide, m.dyconfig.GetBeginnerGuideSourceWaitTime(), err
}

func (m *VirtualImageLogicMgr) MarkVirtualImageBeginnerGuideDone(ctx context.Context, uid uint32) error {
	_, err := m.vUserClient.MarkVirtualImageBeginnerGuideDone(ctx, &virtual_image_user.MarkVirtualImageBeginnerGuideDoneRequest{
		Uid: uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "MarkVirtualImageBeginnerGuideDone, uid: %d, err:%v", uid, err)
	}
	return err
}

func (m *VirtualImageLogicMgr) GetVirtualImageCardEntry(ctx context.Context, uid uint32) (bool, error) {
	es, err := m.cardCli.GetVirtualImageCardEntryStatus(ctx, &virtual_image_card.GetVirtualImageCardEntryStatusRequest{
		Uid: uid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetVirtualImageCardEntry failed to call GetVirtualImageCardEntryStatus, uid: %d, error: %v", uid, err)
		return false, err
	}

	return es.GetSwitch(), nil
}