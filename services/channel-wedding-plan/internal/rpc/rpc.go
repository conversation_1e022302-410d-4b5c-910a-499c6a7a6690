package rpc

import (
	"context"
	"fmt"
	"github.com/golang/protobuf/proto"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic"
	micMiddle "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic_middle"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_scheme"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channelol_go"
	"golang.52tt.com/clients/account"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	"golang.52tt.com/clients/channel"
	channel_msg_api "golang.52tt.com/clients/channel-msg-api"
	"golang.52tt.com/clients/currency"
	"golang.52tt.com/clients/gnobility"
	"golang.52tt.com/clients/guild"
	headImage "golang.52tt.com/clients/headimage"
	imApi "golang.52tt.com/clients/im-api"
	push "golang.52tt.com/clients/push-notification/v2"
	riskMngApi "golang.52tt.com/clients/risk-mng-api"
	"golang.52tt.com/clients/seqgen/v2"
	timeline "golang.52tt.com/clients/timeline"
	tmp_channel_alloc "golang.52tt.com/clients/tmp-channel-alloc"
	friendshipClient "golang.52tt.com/clients/ugc/friendship"
	unifiedPay "golang.52tt.com/clients/unified_pay"
	user_online "golang.52tt.com/clients/user-online"
	userProfileApi "golang.52tt.com/clients/user-profile-api"
	userPresent "golang.52tt.com/clients/userpresent"
	youknowwho "golang.52tt.com/clients/you-know-who"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app"
	pbLogic "golang.52tt.com/protocol/app/channel_wedding_logic"
	imPB "golang.52tt.com/protocol/app/im"
	pushPb "golang.52tt.com/protocol/app/push"
	channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
	channel_wedding_conf "golang.52tt.com/protocol/services/channel-wedding-conf"
	pbIMApi "golang.52tt.com/protocol/services/im-api"
	"golang.52tt.com/protocol/services/imstrangergo"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
	channel_mode_mgr "golang.52tt.com/protocol/services/tt-rev-channel-mode-mgr"
	"golang.52tt.com/services/channel-wedding-plan/internal/store"
	"time"
)

type Rpc struct {
	ChannelModeMgrCli    channel_mode_mgr.ChannelModeMgrClient
	UnifiedPayCli        unifiedPay.IClient
	UserprofileCli       userProfileApi.IClient
	AccountCli           account.IClient
	Currency             currency.IClient
	ApiClient            apicenter.IClient
	ImApiCli             imApi.IClient
	SeqgenCli            seqgen.IClient
	PushCli              push.IClient
	UserOnlineCli        user_online.IClient
	ChannelWeddingSvrCli channel_wedding.ChannelWeddingClient
	TmpChannelAllocCli   tmp_channel_alloc.IClient
	WeddingConfCli       channel_wedding_conf.ChannelWeddingConfClient
	ChannelCli           channel.IClient
	GuildCli             guild.IClient
	TimelineCli          timeline.IClient
	HeadImageCli         headImage.IClient
	RiskMngApiCli        riskMngApi.IClient
	ChannelOlGoCli       channelol_go.ChannelolGoClient
	PresentCli           userPresent.IClient
	GnobilityCli         gnobility.IClient
	FriendshipClient     friendshipClient.IClient
	ImStrangeGoCli       imstrangergo.ImstrangerGoClient
	ChannelMicCli        channel_mic.ChannelMicClient
	ChannelSchemeCli     channel_scheme.ChannelSchemeClient
	ChannelMsgApiCli     channel_msg_api.IClient
	MicMiddleCli         micMiddle.ChannelMicMiddleClient
	YourknowknwhoCLi     youknowwho.IClient
}

func NewRpc() *Rpc {
	ctx := context.Background()
	channelModeMgrCli, err := channel_mode_mgr.NewClient(context.Background())
	if err != nil {
		log.ErrorWithCtx(ctx, "NewRpc fail to new channelModeMgrCli. err:%v", err)
		return nil
	}
	unifiedPayCli := unifiedPay.NewIClient()

	userProfileApiCli, err := userProfileApi.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewRpc fail to new userProfileApiCli. err:%v", err)
		return nil
	}
	accountCli := account.NewIClient()
	currencyCli := currency.NewIClient()
	apiCenterCli := apicenter.NewClient()
	imApiCli, err := imApi.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewRpc fail to new imApiCli. err:%v", err)
		return nil
	}
	seqgenCli := seqgen.NewIClient()
	pushCli := push.NewIClient()
	userOnlineCli := user_online.NewIClient()
	channelWeddingSvrCli, err := channel_wedding.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "NewRpc fail to new channelWed")
		return nil
	}
	tmpChannelAllocCli, err := tmp_channel_alloc.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewRpc fail to new tmpChannelAllocCli. err:%v", err)
		return nil
	}
	timelineCli := timeline.NewClient()

	weddingConfCli, _ := channel_wedding_conf.NewClient(ctx)
	channelCli := channel.NewClient()
	guildCli := guild.NewClient()
	headImageCli := headImage.NewClient()
	riskMngApiCli, _ := riskMngApi.NewClient()
	channelOlGoCli, _ := channelol_go.NewClient(ctx)
	presentCli := userPresent.NewClient()
	gnobilityCli, _ := gnobility.NewClient()
	friendshipClient, _ := friendshipClient.NewClient()
	imStrangerGoCli, _ := imstrangergo.NewClient(ctx)
	channelMicCli, _ := channel_mic.NewClient(ctx)
	channelSchemeCli := channel_scheme.MustNewClient(context.Background())
	channelMsgApiCli, _ := channel_msg_api.NewIClient()
	micMiddleCli := micMiddle.MustNewClient(ctx)
	youknowwhoCli := youknowwho.NewIClient()
	return &Rpc{
		ChannelModeMgrCli:    channelModeMgrCli,
		UnifiedPayCli:        unifiedPayCli,
		UserprofileCli:       userProfileApiCli,
		AccountCli:           accountCli,
		Currency:             currencyCli,
		ApiClient:            apiCenterCli,
		ImApiCli:             imApiCli,
		TimelineCli:          timelineCli,
		SeqgenCli:            seqgenCli,
		PushCli:              pushCli,
		UserOnlineCli:        userOnlineCli,
		ChannelWeddingSvrCli: channelWeddingSvrCli,
		TmpChannelAllocCli:   tmpChannelAllocCli,
		WeddingConfCli:       weddingConfCli,
		ChannelCli:           channelCli,
		GuildCli:             guildCli,
		HeadImageCli:         headImageCli,
		RiskMngApiCli:        riskMngApiCli,
		ChannelOlGoCli:       channelOlGoCli,
		PresentCli:           presentCli,
		GnobilityCli:         gnobilityCli,
		FriendshipClient:     friendshipClient,
		ImStrangeGoCli:       imStrangerGoCli,
		ChannelMicCli:        channelMicCli,
		ChannelSchemeCli:     channelSchemeCli,
		ChannelMsgApiCli:     channelMsgApiCli,
		MicMiddleCli:         micMiddleCli,
		YourknowknwhoCLi:     youknowwhoCli,
	}
}

func (r *Rpc) GenerateSequence(ctx context.Context, uid uint32) (uint64, error) {
	return r.SeqgenCli.GenerateSequence(ctx, uid, seqgen.NamespaceUser, seqgen.KeySvrMsgId, 1)
}

const (
	IMXMLImgTemplate = "<ttbg display=flex width=200 height=150 url=%s event-link=%s>\n</ttbg>"
)

func (r *Rpc) SendIMCommonXmlMsg(ctx context.Context, fromUid, targetUid uint32, fromServerMsgId, targetServerMsgId uint64, xmlContent, outsideText string) error {
	extData := &imPB.IMCommonXmlMsg{
		DisplayType: 0,
		XmlContent:  xmlContent,
	}

	ext, _ := proto.Marshal(extData)
	_, err := r.ImApiCli.SendCommonMsg(ctx, &pbIMApi.SendCommonMsgReq{
		From: &pbIMApi.Entity{
			Type: pbIMApi.Entity_USER,
			Id:   fromUid,
		},
		To: &pbIMApi.Entity{
			Type: pbIMApi.Entity_USER,
			Id:   targetUid,
		},
		Msg: &pbIMApi.CommonMsg{
			MsgType: uint32(imPB.IM_MSG_TYPE_XML_MSG_NORMAL),
			Ext:     ext,
			FromSeq: &pbIMApi.SeqInfo{
				SvrMsgId: fromServerMsgId,
			},
			ToSeq: &pbIMApi.SeqInfo{
				SvrMsgId: targetServerMsgId,
			},
			Content: outsideText,
		},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GenerateSequenceBatch fail to send message. err:%v", err)
	}
	return err
}

func (r *Rpc) SendProposeMsg(ctx context.Context, propose *store.ProposeInfo, msgFromUid, msgToUid uint32, msgFromServerMsgId, msgToServerMsgId uint64, url, outContent string) error {
	xmlContent := fmt.Sprintf("<ttbg display=flex width=200 height=150 url=%s event-link=%s>\n</ttbg>", url, fmt.Sprintf("tt://m.52tt.com/proposal_info_page?proposeId=%s", propose.ID.Hex()))
	err := r.SendIMCommonXmlMsg(ctx, msgFromUid, msgToUid, msgFromServerMsgId, msgToServerMsgId, xmlContent, outContent)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendIMCommonXmlMsg fail to send message. err:%v", err)
		return err
	}

	//发送push通知
	userMap, err := r.UserprofileCli.BatchGetUserProfile(ctx, []uint32{propose.FromUid, propose.TargetUid})
	proposePb := &pbLogic.WeddingProposeInfo{
		ProposeId:  propose.ID.Hex(),
		FromUser:   userMap[propose.FromUid],
		TargetUser: userMap[propose.TargetUid],
		Status:     propose.Status,
		CreateTime: uint32(propose.CreateTime.Unix()),
		EndTime:    uint32(propose.EndTime.Unix()),
		ExpireDay:  propose.ExpiredDays,
		Tips:       propose.Tips,
	}

	content, _ := proto.Marshal(proposePb)

	pushMessage := pushPb.PushMessage{Cmd: uint32(pushPb.PushMessage_WEDDING_PROPOSE_PUSH), Content: content}
	targetUidList := []uint32{propose.TargetUid, propose.FromUid}
	pushMessageContent, _ := proto.Marshal(&pushMessage)
	notification := &pushPB.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()),
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		AppId:              uint32(protocol.TT),
		TerminalTypePolicy: push.DefaultPolicy,
		ProxyNotification: &pushPB.ProxyNotification{
			Type:       uint32(pushPB.ProxyNotification_PUSH),
			Payload:    pushMessageContent,
			Policy:     pushPB.ProxyNotification_DEFAULT,
			ExpireTime: 86400,
		},
	}

	err = r.PushCli.PushToUsers(ctx, targetUidList, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendProposeMsg fail to push message. err:%v", err)
	}
	log.DebugWithCtx(ctx, "SendProposeMsg success. propose:%v", propose)
	return err
}

func (r *Rpc) BatchGetUserProfile(ctx context.Context, uidList []uint32) (map[uint32]*app.UserProfile, error) {
	userprofile, err := r.UserprofileCli.BatchGetUserProfile(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserProfile fail to get user profile. err:%v", err)
		return nil, err
	}

	usernameList := make([]string, 0, len(uidList))
	for _, item := range userprofile {
		usernameList = append(usernameList, item.GetAccount())
	}

	headReso, err := r.HeadImageCli.BatchGetHeadImageMd5(ctx, 0, usernameList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserProfile fail to get head image. err:%v", err)
		return nil, err
	}

	for _, item := range userprofile {
		item.HeadImgMd5 = headReso[item.GetAccount()]
	}

	return userprofile, nil
}

// SendIMCommonXmlMsgWithMsgType 发送IM通用XML消息
func (r *Rpc) SendIMCommonXmlMsgWithMsgType(ctx context.Context, fromUid uint32, toUidList []uint32, commStr, xmlContent string, msgType uint32) error {
	extData := &imPB.IMCommonXmlMsg{
		DisplayType: 0,
		XmlContent:  xmlContent,
	}

	ext, _ := proto.Marshal(extData)
	msgList := make([]*pbIMApi.BatchMsg, 0, len(toUidList))
	for _, toUid := range toUidList {
		msgList = append(msgList, &pbIMApi.BatchMsg{
			From: &pbIMApi.Entity{
				Type: pbIMApi.Entity_USER,
				Id:   fromUid,
			},
			To: &pbIMApi.Entity{
				Type: pbIMApi.Entity_USER,
				Id:   toUid,
			},
			Msg: &pbIMApi.CommonMsg{
				MsgType: msgType,
				Ext:     ext,
				Content: commStr,
			},
		})
	}

	taskList, err := r.ImApiCli.BatchSendCommonMsg(ctx, &pbIMApi.BatchSendCommonMsgReq{
		BatchMsg: msgList,
		Opt: &pbIMApi.SendOption{
			HasMsgRedpoint: pbIMApi.SendOption_BOOL_TRUE,
			HasMsgExposure: pbIMApi.SendOption_BOOL_TRUE,
		},
		Namespace: "婚礼提醒",
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SendIMCommonXmlMsg fail to BatchSendCommonMsg. err:%v", err)
		return err
	}
	log.InfoWithCtx(ctx, "SendIMCommonXmlMsg success. fromUid:%v, toUidList:%v,  msgType:%d, taskList:%+v", fromUid, toUidList, msgType, taskList)
	return nil
}
