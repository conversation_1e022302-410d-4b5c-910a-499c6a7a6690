package internal

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	grpcProtocol "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app/channel_wedding_logic"
	"golang.52tt.com/protocol/common/status"
	channel_wedding_conf "golang.52tt.com/protocol/services/channel-wedding-conf"
	pb "golang.52tt.com/protocol/services/channel-wedding-plan"
	"golang.52tt.com/services/channel-wedding-plan/internal/store"
	comctx "golang.52tt.com/services/tt-rev/common/ctx"
	"google.golang.org/grpc/codes"
	"time"
)

func (s *Server) GetMyWeddingReserveInfo(ctx context.Context, request *pb.GetMyWeddingReserveInfoRequest) (*pb.GetMyWeddingReserveInfoResponse, error) {
	resp := &pb.GetMyWeddingReserveInfoResponse{}
	resp.ChangeLimitTime = s.bc.GetConfig().ReserveConf.LimitChangeAheadTime
	serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetMyWeddingReserveInfo fail to get service info")
		return resp, ErrParamInValid
	}

	weddingPlan, err := s.mgr.GetWeddingPlanById(ctx, request.WeddingPlanId, serviceInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMyWeddingReserveInfo fail to GetWeddingPlanById. err:%v", err)
	}
	if weddingPlan == nil || weddingPlan.ReserveInfo == nil {
		return resp, nil
	}
	reserveInfo := weddingPlan.ReserveInfo

	resp.ChannelId = reserveInfo.ChannelId
	reserveStartTime := time.Unix(int64(reserveInfo.StartTime), 0)
	reserveDateTime := time.Date(reserveStartTime.Year(), reserveStartTime.Month(), reserveStartTime.Day(), 0, 0, 0, 0, reserveStartTime.Location())
	resp.ReserveDate = uint32(reserveDateTime.Unix())
	if s.bc.GetConfig().ReserveConf.MaxChangeTimes <= reserveInfo.ChangeTimes {
		resp.RemainChangeTimes = 0
	} else {
		resp.RemainChangeTimes = s.bc.GetConfig().ReserveConf.MaxChangeTimes - reserveInfo.ChangeTimes
	}
	resp.InChangeTime = time.Now().Add(time.Duration(s.bc.GetConfig().ReserveConf.LimitChangeAheadTime)*time.Hour).Unix() < int64(reserveInfo.StartTime)
	if resp.ChangeLimitTime == 0 {
		resp.InChangeTime = true // 如果没有限制时间，就一直可以修改
	}
	resp.ReserveStartTime = reserveInfo.StartTime
	resp.ReserveEndTime = reserveInfo.EndTime
	st := reserveInfo.StartTime
	et := reserveInfo.EndTime
	stTime := time.Unix(int64(st), 0)
	timeSectionDuration := s.bc.GetReserveTimeSectionDurationConf(stTime)
	if weddingPlan.ThemeType == store.ThemeTypeFree {
		timeSectionDuration = s.bc.GetConfig().ReserveConf.FreeWeddingReserveTimeSectionDuration
	}
	for st < et {
		partEt := st + timeSectionDuration*60
		if partEt >= et {
			partEt = et
		}
		stTime := time.Unix(int64(st), 0)
		partEtTime := time.Unix(int64(partEt), 0)
		resp.ReserveTimeSection = append(resp.ReserveTimeSection, stTime.Format("15:04")+"-"+partEtTime.Format("15:04"))
		st = partEt
	}
	return resp, nil
}

func (s *Server) GetWeddingReserveInfo(ctx context.Context, request *pb.GetWeddingReserveInfoRequest) (*pb.GetWeddingReserveInfoResponse, error) {
	if request.GetThemeType() == 1 {
		resp, err := s.mgr.GetFreeWeddingReserveInfo(ctx, request)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetWeddingReserveInfo fail to GetFreeWeddingReserveInfo. req:  %+v, err:%v", request, err)
		}
		return resp, err
	}
	resp, err := s.mgr.GetWeddingReserveInfo(ctx, request)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWeddingReserveInfo fail to GetWeddingReserveInfo. req:  %+v, err:%v", request, err)
	}

	return resp, err
}

func (s *Server) SaveWeddingReserveInfo(ctx context.Context, req *pb.SaveWeddingReserveRequest) (*pb.SaveWeddingReserveInfoResponse, error) {
	resp := &pb.SaveWeddingReserveInfoResponse{}
	serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "SaveWeddingReserveInfo fail to get service info")
		return resp, nil
	}

	weddingPlan, err := s.mgr.GetWeddingPlanById(ctx, req.WeddingPlanId, serviceInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "SaveWeddingReserveInfo fail to GetWeddingPlanById. err:%v", err)
		return resp, err
	}

	if weddingPlan != nil && weddingPlan.ReserveInfo != nil { // 已经预约过了
		log.WarnWithCtx(ctx, "SaveWeddingReserveInfo fail, already reserved")
		return resp, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "抱歉~对方已经选定了婚礼场地，重新看看吧")
	}

	reserveSt, reserveEt, err := s.mgr.TranReserveTimeSection2Time(req.GetReserveDate(), req.GetReserveTime())
	if err != nil {
		log.ErrorWithCtx(ctx, "SaveWeddingReserveInfo fail to TranReserveTimeSection2Time. err:%v", err)
		return resp, err
	}
	if s.mgr.IsHotTimeSection(ctx, req.GetChannelId(), time.Unix(int64(reserveSt), 0)) {
		log.WarnWithCtx(ctx, "SaveWeddingReserveInfo fail, hot wedding")
		return resp, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "无法直接预约热门时段哦~")
	}

	if weddingPlan.ThemeType == store.ThemeTypePay {
		canDo, err := s.mgr.CheckWeddingPlanByChannelIdAndTimeRange(ctx, req.GetChannelId(), reserveSt, reserveEt)
		if err != nil {
			log.ErrorWithCtx(ctx, "SaveWeddingReserveInfo fail to CheckWeddingPlanByChannelIdAndTimeRange. err:%v", err)
			return resp, err
		}
		if !canDo {
			log.WarnWithCtx(ctx, "SaveWeddingReserveInfo fail, channel is not available")
			return resp, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "本时段已约满请重新选择~")
		}
	}

	err = s.mgr.UpdateWeddingReserve(ctx, weddingPlan.ThemeType, req.GetWeddingPlanId(), req.GetChannelId(), reserveSt, reserveEt, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "SaveWeddingReserveInfo fail to UpdateWeddingReserve. err:%v", err)
		return resp, err
	}
	weddingPlan.ReserveInfo = &store.ReserveInfo{
		ChannelId: req.GetChannelId(),
		StartTime: reserveSt,
		EndTime:   reserveEt,
	}

	// 通知房管
	//asyncCtx, _ := comctx.WithTimeout(time.Second * 5)
	go func() {
		s.mgr.ReserveChangeNotifyAdmin(context.TODO(), serviceInfo.UserID, nil, weddingPlan, false)
	}()

	return resp, err
}

func (s *Server) ChangeWeddingReserveInfo(ctx context.Context, req *pb.ChangeWeddingReserveRequest) (*pb.ChangeWeddingReserveInfoResponse, error) {
	resp := &pb.ChangeWeddingReserveInfoResponse{}
	serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "ChangeWeddingReserveInfo fail to get service info")
		return resp, nil
	}
	weddingPlan, err := s.mgr.GetWeddingPlanById(ctx, req.WeddingPlanId, serviceInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChangeWeddingReserveInfo fail to GetWeddingPlanById. err:%v", err)
		return resp, err
	}

	reserveInfo := weddingPlan.ReserveInfo

	if weddingPlan.ReserveInfo == nil || reserveInfo.StartTime == 0 { // 没有预约
		log.WarnWithCtx(ctx, "ChangeWeddingReserveInfo fail, not reserved")
		return resp, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "未预约")
	}

	// 热门时段无法修改
	if weddingPlan.IsHot {
		log.WarnWithCtx(ctx, "ChangeWeddingReserveInfo fail, hot wedding")
		return resp, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "暂不支持直接修改为其他时段哦~")
	}

	if reserveInfo.ChangeTimes >= s.bc.GetConfig().ReserveConf.MaxChangeTimes {
		log.WarnWithCtx(ctx, "ChangeWeddingReserveInfo fail, change times reach limit")
		return resp, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "你已经修改多次时间，不再支持修改时间了哦")
	}

	if int64(reserveInfo.StartTime)-int64(s.bc.GetConfig().ReserveConf.LimitChangeAheadTime)*3600 < time.Now().Unix() {
		log.WarnWithCtx(ctx, "ChangeWeddingReserveInfo fail, in change time")
		return resp, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "当前已经不支持再次修改预约时间了哦")
	}

	reserveSt, reserveEt, err := s.mgr.TranReserveTimeSection2Time(req.GetReserveDate(), req.GetReserveTime())
	if err != nil {
		log.ErrorWithCtx(ctx, "ChangeWeddingReserveInfo fail to TranReserveTimeSection2Time. err:%v", err)
		return resp, err
	}
	if s.mgr.IsHotTimeSection(ctx, req.GetChannelId(), time.Unix(int64(reserveSt), 0)) {
		log.WarnWithCtx(ctx, "ChangeWeddingReserveInfo fail, hot wedding")
		return resp, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "无法直接修改为热门时段哦~")
	}

	if weddingPlan.ThemeType == store.ThemeTypePay {
		canDo, err := s.mgr.CheckWeddingPlanByChannelIdAndTimeRange(ctx, req.GetChannelId(), reserveSt, reserveEt)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChangeWeddingReserveInfo fail to CheckWeddingPlanByChannelIdAndTimeRange. err:%v", err)
			return resp, err
		}
		if !canDo {
			log.WarnWithCtx(ctx, "ChangeWeddingReserveInfo fail, channel is not available")
			return resp, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "本时段已约满请重新选择~")
		}
	}

	err = s.mgr.UpdateWeddingReserve(ctx, weddingPlan.ThemeType, req.GetWeddingPlanId(), req.GetChannelId(), reserveSt, reserveEt, reserveInfo.ChangeTimes+1)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChangeWeddingReserveInfo fail to UpdateWeddingReserve. err:%v", err)
		return resp, err
	}

	// 修改了房间需要删除主持人
	if weddingPlan.ReserveInfo.ChannelId != req.GetChannelId() {
		err := s.mgr.UpdateWeddingHost(ctx, req.GetWeddingPlanId(), 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChangeWeddingReserveInfo fail to UpdateWeddingHost. req: %+v, err:%v", req, err)
		}
	}

	// 更新预约信息
	oldWeddingPlan := *weddingPlan // 复制一份旧的
	weddingPlan.ReserveInfo = &store.ReserveInfo{
		ChannelId: req.GetChannelId(),
		StartTime: reserveSt,
		EndTime:   reserveEt,
	}

	// 通知房管和好友
	asyncCtx, _ := comctx.WithTimeout(time.Second * 5)
	go func() {
		s.mgr.ReserveChangeNotifyAdmin(context.TODO(), serviceInfo.UserID, &oldWeddingPlan, weddingPlan, true)
		if oldWeddingPlan.ReserveInfo.StartTime != weddingPlan.ReserveInfo.StartTime {
			s.mgr.ReserveChangeNotifyFriends(asyncCtx, serviceInfo.UserID, weddingPlan)
		}
	}()

	return resp, nil
}

func (s *Server) GetReservedWedding(ctx context.Context, request *pb.GetReservedWeddingRequest) (*pb.GetReservedWeddingResponse, error) {
	resp := &pb.GetReservedWeddingResponse{}
	if len(request.GetChannelId()) == 0 {
		return resp, nil
	}
	sortType := 0
	if request.GetDataType() == uint32(pb.WeddingReserveDataType_WEDDING_RESERVE_DATA_TYPE_NOT_START) {
		validStartTime := uint32(time.Now().Unix())
		if request.GetStartTime() < validStartTime {
			request.StartTime = validStartTime
		}
		if request.EndTime == 0 {
			request.EndTime = uint32(time.Now().Add(time.Hour * 24 * 365).Unix())
		}
	}
	if request.GetDataType() == uint32(pb.WeddingReserveDataType_WEDDING_RESERVE_DATA_TYPE_END) {
		validEndTime := uint32(time.Now().Unix())
		if request.GetEndTime() == 0 || request.GetEndTime() > validEndTime {
			request.EndTime = validEndTime
		}
		sortType = 1
	}
	if request.GetPageSize() == 0 {
		request.PageSize = 10
	}

	total, data, err := s.mgr.PageWeddingPlanByChannelId(ctx, request.GetChannelId(), int(request.GetPageNum()), int(request.GetPageSize()),
		sortType, int64(request.GetStartTime()), int64(request.GetEndTime()))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReservedWedding fail to PageWeddingPlanByChannelIdReserveTime. err:%v", err)
		return resp, err
	}
	resp.Total = total

	themeCfgList, err := s.rpc.WeddingConfCli.GetThemeCfgList(ctx, &channel_wedding_conf.GetThemeCfgListReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReservedWedding fail to GetAllThemeCfg. err:%v", err)
		return resp, err
	}
	allThemeMap := make(map[uint32]*channel_wedding_conf.ThemeCfg)
	for _, item := range themeCfgList.GetThemeCfgList() {
		allThemeMap[item.GetThemeId()] = item
	}

	planIdList := make([]uint32, 0, len(data))
	for _, item := range data {
		planIdList = append(planIdList, item.ID)
	}

	// 获取订单/客服信息
	orderList, err := s.mgr.BatchGetWeddingOrderByPlanIdList(ctx, planIdList )
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReservedWedding fail to BatchGetWeddingOrderByPlanIdList. planIdList: %+v, err:%v", planIdList, err)
		return resp, err
	}
	sourceMsgIdList := make([]uint32, 0, len(orderList))
	for _, item := range orderList {
		if item.SourceMsgId == 0 {
			continue
		}
		sourceMsgIdList = append(sourceMsgIdList, item.SourceMsgId)
	}
	reserveImList, err := s.mgr.BatchGetWeddingReserveIMByIdList(ctx, sourceMsgIdList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReservedWedding fail to BatchGetWeddingReserveIMByIdList. sourceMsgIdList: %+v, err:%v", sourceMsgIdList, err)
		return resp, err
	}
	sourceMsgIdReserveImMap := make(map[uint32]*store.WeddingReserveIM)
	for _, item := range reserveImList {
		sourceMsgIdReserveImMap[item.ID] = item
	}
	orderMap, reserveImMap := make(map[uint32]*store.WeddingOrder), make(map[uint32]*store.WeddingReserveIM)
	for _, item := range orderList {
		orderMap[item.Id] = item
		reserveImMap[item.Id] = sourceMsgIdReserveImMap[item.SourceMsgId]
	}


	for _, item := range data {
		buyPrice := uint32(0)
		channelManagerUid := uint32(0)
		if target, ok := orderMap[item.ID]; ok && target != nil {
			buyPrice = target.WeddingPrice
		}
		if target, ok := reserveImMap[item.ID]; ok && target != nil {
			channelManagerUid = target.FromUid
		}

		resp.ReservedWeddingList = append(resp.ReservedWeddingList, &pb.ReservedWeddingInfo{
			WeddingPlanId:     item.ID,
			CreateTime:        item.CreateTime,
			ChannelId:         item.ReserveInfo.ChannelId,
			ReserveStartTime:  item.ReserveInfo.StartTime,
			ReserveEndTime:    item.ReserveInfo.EndTime,
			GroomUid:          item.GroomUid,
			BrideUid:          item.BrideUid,
			HostUid:           item.HostUid,
			ThemeName:         allThemeMap[item.ThemeId].GetThemeName(),
			BuyerUid:          item.BuyerUid,
			ChannelManagerUid: channelManagerUid,
			BuyPrice:          buyPrice,
			IsHot:             item.IsHot,
			IsAdminCancel:     item.IsAdminCancel,
		})
	}

	return resp, nil
}

func (s *Server) SetWeddingHost(ctx context.Context, req *pb.SetWeddingHostRequest) (*pb.SetWeddingHostResponse, error) {
	resp := &pb.SetWeddingHostResponse{}
	weddingPlan, err := s.mgr.GetWeddingPlanByIdNoCheck(ctx, req.GetWeddingPlanId(), false)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateWeddingPlanHost fail to GetWeddingPlanById. err:%v", err)
		return resp, err
	}

	if weddingPlan.Status == store.WeddingPlanStatusCancel || weddingPlan.Status >= store.WeddingPlanStatusCanceled {
		log.ErrorWithCtx(ctx, "UpdateWeddingPlanHost weddingPlan is cancel, req: %+v", req)
		return resp, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "无法安排主持，该场婚礼已取消")
	}

	// 婚礼已经结束
	if weddingPlan.Status >= store.WeddingPlanStatusFinish {
		log.ErrorWithCtx(ctx, "UpdateWeddingPlanHost weddingPlan is cancel, req: %+v", req)
		return resp, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "本婚礼已不存在，请刷新页面")
	}

	// 设置主持时候用户已经修改了房间
	if weddingPlan.ReserveInfo != nil && weddingPlan.ReserveInfo.ChannelId != req.GetChannelId() {
		log.ErrorWithCtx(ctx, "UpdateWeddingPlanHost weddingPlan is change, req: %+v", req)
		return resp, protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingPlanCommonError, "本婚礼已不存在，请刷新页面")
	}

	err = s.mgr.UpdateWeddingHost(ctx, req.GetWeddingPlanId(), req.GetHostUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetWeddingHost fail to UpdateWeddingHost. err:%v", err)
		return resp, err
	}

	return resp, nil
}

func (s *Server) GetSimpleWeddingPlanInfo(ctx context.Context, req *pb.GetSimpleWeddingPlanInfoRequest) (*pb.GetSimpleWeddingPlanInfoResponse, error) {
	resp := &pb.GetSimpleWeddingPlanInfoResponse{}
	st := time.Now()
	weddingPlan, err := s.mgr.GetWeddingPlanByIdNoCheck(ctx, req.WeddingPlanId, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSimpleWeddingPlanInfo fail to GetWeddingPlanById. err:%v", err)
	}
	if weddingPlan == nil {
		return resp, nil
	}

	log.InfoWithCtx(ctx, "GetSimpleWeddingPlanInfo. found plan cost:%vms", time.Since(st).Milliseconds())

	resp.GroomUid = weddingPlan.GroomUid
	resp.BrideUid = weddingPlan.BrideUid

	groomsman := make([]uint32, 0)
	for _, item := range weddingPlan.Groomsman {
		groomsman = append(groomsman, item.Uid)
	}
	bridesmaid := make([]uint32, 0)
	for _, item := range weddingPlan.Bridesmaid {
		bridesmaid = append(bridesmaid, item.Uid)
	}
	bigScreenList := make([]string, 0)
	for _, item := range weddingPlan.BigScreenList {
		if item.ReviewStatus != uint32(pb.ReviewStatus_REVIEW_STATUS_PASS) {
			continue
		}
		bigScreenList = append(bigScreenList, item.ImgUrl)
		if len(bigScreenList) >= 10 {
			break
		}
	}

	resp.GroomsmanList = groomsman
	resp.BridesmaidList = bridesmaid
	resp.BigScreenList = bigScreenList
	if weddingPlan.ReserveInfo != nil {
		resp.ReserveStartTime = weddingPlan.ReserveInfo.StartTime
		resp.ReserveEndTime = weddingPlan.ReserveInfo.EndTime
	}
	resp.BuyerUid = weddingPlan.BuyerUid
	resp.IsHot = weddingPlan.IsHot
	resp.ThemeId = weddingPlan.ThemeId

	// 获取礼物id
	log.InfoWithCtx(ctx, "GetSimpleWeddingPlanInfo. ", weddingPlan.ReserveInfo)
	weddingOrder, err := s.mgr.GetWeddingOrderById(ctx, req.GetWeddingPlanId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSimpleWeddingPlanInfo fail to GetWeddingOrderById. err:%v", err)
		return resp, err
	}
	resp.GiftId = weddingOrder.WeddingGiftId

	return resp, nil
}

func (s *Server) GetWeddingInviteInfo(ctx context.Context, req *pb.GetWeddingInviteInfoRequest) (*pb.GetWeddingInviteInfoResponse, error) {
	resp := &pb.GetWeddingInviteInfoResponse{
		InviteCard: &pb.InviteCard{},
	}

	// 查看有没有邀请
	if req.GetInviteId() > 0 {
		invite, err := s.mgr.GetWeddingGuestInviteById(ctx, req.GetInviteId())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetWeddingInviteInfo fail to GetWeddingGuestInviteByWeddingPlanIdGuestTypeUidStatus. err:%v", err)
			return resp, err
		}
		if invite != nil {
			resp.InviteCard.InviteId = req.GetInviteId()
			resp.InviteStatus = inviteStatusMap(invite.InviteStatus)
			req.WeddingPlanId = invite.WeddingPlanId
			req.WeddingInviteType = invite.GuestType
		}
	}

	weddingPlan, err := s.mgr.GetWeddingPlanByIdNoCheck(ctx, req.WeddingPlanId, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWeddingInviteInfo fail to GetWeddingPlanById. err:%v", err)
		return resp, err
	}

	themeCfg, err := s.rpc.WeddingConfCli.GetThemeCfg(ctx, &channel_wedding_conf.GetThemeCfgReq{
		ThemeId: weddingPlan.ThemeId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWeddingInviteInfo fail to GetThemeCfg, themeId: %d, err: %v", weddingPlan.ThemeId, err)
		return resp, err
	}

	inviteCardBg := themeCfg.GetThemeCfg().GetMailLadyRightBgIcon()
	if req.GetUid() == weddingPlan.BrideUid { // 如果主态是新娘, 要用新娘在左侧的背景图
		inviteCardBg = themeCfg.GetThemeCfg().GetMailLadyLeftBgIcon()
	}

	resp.InviteCard.WeddingPlanId = weddingPlan.ID
	resp.InviteCard.ThemeId = weddingPlan.ThemeId
	resp.InviteCard.GroomUid = weddingPlan.GroomUid
	resp.InviteCard.BrideUid = weddingPlan.BrideUid
	resp.InviteCard.InviteCardBg = inviteCardBg
	resp.InviteCard.WeddingInviteType = req.GetWeddingInviteType()

	if req.GetWeddingInviteType() != store.GuestTypeFriend && len(themeCfg.GetThemeCfg().GetThemeLevelCfgList()) > 0 {
		target := themeCfg.GetThemeCfg().GetThemeLevelCfgList()[0].GetGuestDressCfgMap()[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDES)]
		if req.GetWeddingInviteType() == store.GuestTypeBridesmaid {
			target = themeCfg.GetThemeCfg().GetThemeLevelCfgList()[0].GetGuestDressCfgMap()[uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDESMAID)]
		}

		gift := target.GetWeddingDress().GetResourcePng()
		giftDesc := target.GetDressText()
		resp.InviteCard.GiftList = append(resp.InviteCard.GiftList, &pb.WeddingGuestGift{
			GiftUrl:  gift,
			GiftDesc: giftDesc,
		})
	}

	if weddingPlan.ReserveInfo != nil {
		resp.InviteCard.WeddingDatetime = uint32(time.Unix(int64(weddingPlan.ReserveInfo.StartTime), 0).Unix())
		resp.InviteCard.WeddingChannelId = weddingPlan.ReserveInfo.ChannelId
	}

	return resp, nil
}

func inviteStatusMap(inviteStatus uint32) uint32 {
	switch inviteStatus {
	case store.InviteStatusWaiting:
		return uint32(channel_wedding_logic.WeddingInviteStatus_WEDDING_INVITE_STATUS_WAITING)
	case store.InviteStatusAccepted:
		return uint32(channel_wedding_logic.WeddingInviteStatus_WEDDING_INVITE_STATUS_ACCEPTED)
	case store.InviteStatusRejected:
		return uint32(channel_wedding_logic.WeddingInviteStatus_WEDDING_INVITE_STATUS_REFUSED)
	default:
		return uint32(channel_wedding_logic.WeddingInviteStatus_WEDDING_INVITE_STATUS_CANCELED)
	}
	return uint32(channel_wedding_logic.WeddingInviteStatus_WEDDING_INVITE_STATUS_CANCELED)
}

func (s *Server) GetWeddingPlanBaseInfo(ctx context.Context, request *pb.GetWeddingPlanBaseInfoRequest) (*pb.GetWeddingPlanBaseInfoResponse, error) {
	resp := &pb.GetWeddingPlanBaseInfoResponse{}
	serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "SetDressInUse ctx:%+v", ctx)
		return resp, ErrParamInValid
	}
	marriageRelation, err := s.dao.GetMarriageRelationByUid(ctx, serviceInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetSendPropose failed to get propose info")
		return resp, err
	}

	plan, err := s.dao.GetGoingPlan(ctx, serviceInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGoingWeddingOrder failed to get plan info")
		return resp, err
	}

	return &pb.GetWeddingPlanBaseInfoResponse{
		HasMate:   marriageRelation != nil,
		IsPay:     plan != nil,
		IsReserve: plan != nil && plan.ReserveInfo != nil,
	}, nil

}

func (s *Server) UpdateWeddingPlanStatus(c context.Context, request *pb.UpdateWeddingPlanStatusRequest) (*pb.UpdateWeddingPlanStatusResponse, error) {
	resp := &pb.UpdateWeddingPlanStatusResponse{}
	err := s.mgr.UpdateWeddingPlanStatusFinish(c, request.GetWeddingPlanId(), request.GetIsForce())
	if err != nil {
		log.ErrorWithCtx(c, "UpdateWeddingPlanStatus fail to UpdateWeddingPlanStatusFinish. err:%v", err)
		return resp, err
	}

	return resp, nil
}

func (s *Server) ConsultWeddingReserve(ctx context.Context, request *pb.ConsultWeddingReserveRequest) (*pb.ConsultWeddingReserveResponse, error) {
	resp,err := s.mgr.ConsultWeddingReserve(ctx, request)
	if err != nil {
		log.ErrorWithCtx(ctx, "ConsultWeddingReserve fail to ConsultWeddingReserve. err:%v", err)
		return resp, err
	}

	return resp, nil
}

func (s *Server) ArrangeWeddingReserve(ctx context.Context, request *pb.ArrangeWeddingReserveRequest) (*pb.ArrangeWeddingReserveResponse, error) {
	resp, err := s.mgr.ArrangeWeddingReserve(ctx, request)
	if err != nil {
		log.ErrorWithCtx(ctx, "ArrangeWeddingReserve fail to ArrangeWeddingReserve. req: %+v, err:%v", request, err)
		return resp, err
	}
	return resp, nil
}

