package mgr

import (
	"context"
	"github.com/golang/protobuf/proto"
	micMiddle "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic_middle"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/app/channel_wedding_logic"
	"golang.52tt.com/protocol/common/status"
	channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
	channel_wedding_conf "golang.52tt.com/protocol/services/channel-wedding-conf"
	pb "golang.52tt.com/protocol/services/channel-wedding-plan"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkasimplemic"
	presentPb "golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/channel-wedding-common/constant"
	"golang.52tt.com/services/channel-wedding-plan/internal/store"
	"time"
)

func (m *WeddingPlanMgr) GetPreProgressInfo(ctx context.Context, channelId  uint32) (*pb.GetWeddingPreProgressInfoResponse, error) {
	resp := &pb.GetWeddingPreProgressInfoResponse{}

	stage, err := m.cache.GetPreProgressStage(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPreProgressStage, channelId: %d, err: %v", channelId, err)
		return resp, err
	}

	weddingPlanId, err := m.cache.GetPreProgressWeddingId(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPreProgressWeddingId, channelId: %d, err: %v", channelId, err)
		return resp, err
	}

	uidA, uidB, err := m.getNewcomers(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPreProgressCurOnMicUser, channelId: %d, err: %v", channelId, err)
		return resp, err
	}

	giftValA, giftValB, err := m.cache.GetPreProgressGiftValue(ctx, channelId, uidA, uidB)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPreProgressGiftValue, channelId: %d, err: %v", channelId, err)
		return resp, err
	}
	resp.PreProgressInfo = &pb.WeddingPreProgressInfo{
		Stage: stage,
		WeddingPlanId: weddingPlanId,
		WeddingGiftValueList: []*pb.WeddingGiftValue{
			{
				Uid:   uidA,
				Value: giftValA,
			},
			{
				Uid:   uidB,
				Value: giftValB,
			},
		},
	}

	return resp, nil
}

func (m *WeddingPlanMgr) getNewcomers(ctx context.Context, channelId uint32) (uint32, uint32, error) {
	 uidA, err := m.cache.GetNewcomersMic(ctx, channelId, uint32(constant.GroomMicId))
	 if err != nil {
		 log.ErrorWithCtx(ctx, "GetNewcomersMic, channelId: %d, err: %v", channelId, err)
		 return 0, 0, err
	 }
	 uidB, err := m.cache.GetNewcomersMic(ctx, channelId, uint32(constant.BrideMicId))
	 if err != nil {
		 log.ErrorWithCtx(ctx, "GetNewcomersMic, channelId: %d, err: %v", channelId, err)
		 return 0, 0, err
	 }
	 return uidA, uidB, nil
}

func (m *WeddingPlanMgr) HandleNewcomerMic(ctx context.Context, channelId, sourceMicId, targetMicId, uid, opMicType uint32) error {
	// 是不是在婚礼中
	isChannelWedding, err := m.IsChannelWedding(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleNewcomerMic IsChannelWedding, channelId: %d, err:%v", channelId, err)
		return err
	}
	if isChannelWedding {
		log.WarnWithCtx(ctx, "HandleNewcomerMic, channelId: %d, isChannelWedding: %v", channelId, isChannelWedding)
		return nil // 当前房间正在举行婚礼, 忽略上下麦事件
	}

	log.InfoWithCtx(ctx, "HandleNewcomerMic, channelId: %d, targetMicId: %d, uid: %d", channelId, targetMicId, uid)

	// 换麦转化成上/下麦
	if opMicType == uint32(kafkasimplemic.ESIMPLE_MIC_EVENT_TYPE_ENUM_SIMPLE_MIC_EV_CHANGE) {
		if sourceMicId == constant.GroomMicId || sourceMicId == constant.BrideMicId { // 如果从新人麦换走, 转化成下麦
			opMicType = uint32(kafkasimplemic.ESIMPLE_MIC_EVENT_TYPE_ENUM_SIMPLE_MIC_EV_RELEASE)
		}
		if targetMicId == constant.GroomMicId || targetMicId == constant.BrideMicId { // 如果换到新人麦, 转化成上麦
			opMicType = uint32(kafkasimplemic.ESIMPLE_MIC_EVENT_TYPE_ENUM_SIMPLE_MIC_EV_HOLD)
		}
	}

	// 记录麦上新人
	if opMicType == uint32(kafkasimplemic.ESIMPLE_MIC_EVENT_TYPE_ENUM_SIMPLE_MIC_EV_HOLD) {
		if err := m.cache.SetNewcomersMic(ctx, channelId, targetMicId, uid); err != nil {
			log.ErrorWithCtx(ctx, "SetNewcomersMic, channelId: %d, uid: %d, err: %v", channelId, uid, err)
			return err
		}
	}

	// 移除新人
	if opMicType == uint32(kafkasimplemic.ESIMPLE_MIC_EVENT_TYPE_ENUM_SIMPLE_MIC_EV_RELEASE) {
		if err := m.cache.DelNewcomersMic(ctx, channelId, targetMicId); err != nil {
			log.ErrorWithCtx(ctx, "RemoveNewcomersMic, channelId: %d, uid: %d, err: %v", channelId, uid, err)
			return err
		}
	}


	otherNewcomerMicId :=  uint32(constant.GroomMicId)
	if targetMicId == constant.GroomMicId {
		otherNewcomerMicId = constant.BrideMicId
	}
	otherNewcomerUid, err := m.cache.GetNewcomersMic(ctx, channelId, otherNewcomerMicId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNewcomersMic, channelId: %d, targetMicId: %d, err: %v", channelId, otherNewcomerMicId, err)
		return err
	}
	log.DebugWithCtx(ctx, "HandleNewcomerMic, channelId: %d, uid: %d, otherNewcomerUid: %d", channelId, uid, otherNewcomerUid)

	// 上麦, 看另一个麦上是否有新人
	needUpdateStage := true // 是否需要更新stage
	stage := uint32(channel_wedding_logic.WeddingPreProgressStage_WEDDING_PRE_PROGRESS_STAGE_ON_MIC)
	if opMicType  == uint32(kafkasimplemic.ESIMPLE_MIC_EVENT_TYPE_ENUM_SIMPLE_MIC_EV_HOLD) {
		if otherNewcomerUid != 0 { // 如果两人都上麦了
			stage = uint32(channel_wedding_logic.WeddingPreProgressStage_WEDDING_PRE_PROGRESS_STAGE_SEND_GIFT)
			// 如果两人同时上麦,尝试记录两人首次上麦时间, 开始爱意值计数, 如果之前已经及路过, 不会再更新
			if err := m.cache.SetFirstOnMic(ctx, channelId, uid); err != nil {
				log.ErrorWithCtx(ctx, "SetFirstOnMic, channelId: %d, uid: %d, err: %v", channelId, uid, err)
				return err
			}
			// 记录另一个
			if err := m.cache.SetFirstOnMic(ctx, channelId, otherNewcomerUid); err != nil {
				log.ErrorWithCtx(ctx, "SetFirstOnMic, channelId: %d, uid: %d, err: %v", channelId, otherNewcomerUid, err)
				return err
			}
			// 判断当前买上两人礼物值是达到获得婚礼目标值
			giftValA, giftValB, err := m.cache.GetPreProgressGiftValue(ctx, channelId, uid, otherNewcomerUid)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetPreProgressGiftValue, channelId: %d, uid: %d, otherNewcomerUid: %d, err: %v", channelId, uid, otherNewcomerUid, err)
				return err
			}
			buyerUid := uid
			maxVal := giftValA
			if giftValB > giftValA {
				maxVal = giftValB
				buyerUid = otherNewcomerUid
			}
			planInit, err := m.checkGiftValReachWeddingPlanInit(ctx, channelId, uid, otherNewcomerUid, buyerUid, maxVal)
			if err != nil {
				log.ErrorWithCtx(ctx, "checkGiftValReachWeddingPlanInit, channelId: %d, uid: %d, otherNewcomerUid: %d, err: %v", channelId, uid, otherNewcomerUid, err)
				return err
			}
			if planInit {
				log.InfoWithCtx(ctx, "HandleNewcomerMic, giftValA: %d, giftValB: %d, buyerUid: %d, maxVal: %d, planInit: %v", giftValA, giftValB, buyerUid, maxVal, planInit)
				needUpdateStage = false // 婚礼初始化成功/已有婚礼, 阶段更新已经在那边处理了, 这里就不需要更新
			}
		}
	}

	if opMicType == uint32(kafkasimplemic.ESIMPLE_MIC_EVENT_TYPE_ENUM_SIMPLE_MIC_EV_RELEASE) {
		log.DebugWithCtx(ctx, "HandleNewcomerMic, release, channelId: %d, targetMicId: %d, uid: %d", channelId, targetMicId, uid)
		if  otherNewcomerUid == 0 { // 新人麦没人更新为0
			stage = uint32(channel_wedding_logic.WeddingPreProgressStage_WEDDING_PRE_PROGRESS_STAGE_UNSPECIFIED)
		}

		weddingPlanId, err := m.cache.GetPreProgressWeddingId(ctx, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPreProgressWeddingId, channelId: %d, err: %v", channelId, err)
			return err
		}

		// 有婚礼且所有人下麦, 取消婚礼
		if weddingPlanId != 0 {
			if  otherNewcomerUid == 0 { // 新人麦所有人下麦, 婚礼取消
				log.DebugWithCtx(ctx, "HandleNewcomerMic, cancel wedding plan, channelId: %d, planId: %v, uidA: %d, uidB: %d", channelId, weddingPlanId, uid, otherNewcomerUid)
				// 取消婚礼
				planInfo, err := m.GetWeddingPlanByIdNoCheck(ctx, weddingPlanId, false)
				if err != nil {
					log.ErrorWithCtx(ctx, "GetWeddingPlanByIdNoCheck, channelId: %d, planId: %d, err: %v", channelId, weddingPlanId, err)
					return err
				}
				err = m.CancelChannelBuyWedding(ctx, channelId, planInfo)
				if err != nil {
					log.ErrorWithCtx(ctx, "CancelChannelBuyWedding, channelId: %d, planId: %d, err: %v", channelId, weddingPlanId, err)
					return err
				}
			} else {
				needUpdateStage = false // 有婚礼, 只要不取消就不用再更新
			}
		}
	}

	if needUpdateStage {
		err = m.cache.SetPreProgressStage(ctx, channelId, stage)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetPreProgressStage, channelId: %d, stage: %d, err: %v", channelId, stage, err)
			return err
		}
		log.DebugWithCtx(ctx, "HandleNewcomerMic, channelId: %d, stage: %d", channelId, stage)
	}

	// 推送更新
	err = m.preProgressInfoUpdatePush(ctx, channelId, uid)
	 if err != nil {
		 log.ErrorWithCtx(ctx, "preProgressInfoUpdatePush, channelId: %d, uid: %d, err: %v", channelId, uid, err)
		 return err
	 }

	return nil
}

func (m *WeddingPlanMgr) preProgressInfoUpdatePush(ctx context.Context, channelId, uid uint32) error {
	log.DebugWithCtx(ctx, "preProgressInfoUpdatePush, channelId: %d, uid: %d", channelId, uid)
	// 推送
	preProgressInfo, err := m.GetPreProgressInfo(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPreProgressInfo, channelId: %d, err: %v", channelId, err)
		return err
	}

	logicWeddingPreProgressInfo, err := m.genLogicWeddingPreProgressInfo(ctx, preProgressInfo.GetPreProgressInfo())
	if err != nil {
		log.ErrorWithCtx(ctx, "genLogicWeddingPreProgressInfo, channelId: %d, err: %v", channelId, err)
		return err
	}

	msg := &channel_wedding_logic.WeddingPreProgressInfoChangeOpt{
		PreProgressInfo: logicWeddingPreProgressInfo,
		ServerTs: uint32(time.Now().Unix()),
	}
	log.DebugWithCtx(ctx, "PushToChannel, uid: %d, channelId: %d, msg: %+v", uid, channelId, msg)
	data, err := proto.Marshal(msg)
	if err != nil {
		log.ErrorWithCtx(ctx, "proto.Marshal, err: %v", err)
		return err
	}

	requestId, err := m.rpc.ChannelMsgApiCli.SimplePushToChannel(ctx, uid, channelId, uint32(channel.ChannelMsgType_WEDDING_PRE_PROGRESS_UPDATE_PUSH), "婚礼房前置流程信息更新", data)
	if err != nil {
		log.ErrorWithCtx(ctx, "SimplePushToChannel, channelId: %d, requestId: %s, err: %v", channelId, requestId, err)
		return err
	}

	log.InfoWithCtx(ctx, "preProgressInfoUpdatePush success. channelId: %d, requestId: %s", channelId, requestId)

	return nil
}

func (m *WeddingPlanMgr) genLogicWeddingPreProgressInfo(ctx context.Context, preProgressInfo *pb.WeddingPreProgressInfo) (*channel_wedding_logic.WeddingPreProgressInfo, error) {
	uidList := make([]uint32, 0, len(preProgressInfo.GetWeddingGiftValueList()))
	for _, item := range preProgressInfo.GetWeddingGiftValueList() {
		uidList = append(uidList, item.GetUid())
	}

	userprofileMap, uErr := m.rpc.UserprofileCli.BatchGetUserProfileV2(ctx, uidList, false)
	if uErr != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserProfileV2, uidList: %+v, err: %v", uidList, uErr)
		return nil, uErr

	}

	newcomerList := make([]*channel_wedding_logic.WeddingNewcomer, 0)
	for _, item := range preProgressInfo.GetWeddingGiftValueList() {
		 newcomerList = append(newcomerList, &channel_wedding_logic.WeddingNewcomer{
			 User:  userprofileMap[item.GetUid()],
			 Value: item.GetValue(),
		 })
	}

	themeCfg, err := m.getDefaultPayTheme(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "getDefaultPayTheme, err: %v", err)
		return nil, err
	}

	return &channel_wedding_logic.WeddingPreProgressInfo{
		Stage:            preProgressInfo.GetStage(),
		WeddingPlanId:    preProgressInfo.GetWeddingPlanId(),
		NewcomerList:     newcomerList,
		PanelResourceUrl: themeCfg.GetPreProgressPanelResourceUrl(),
		PanelResourceMd5: themeCfg.GetPreProgressPanelResourceMd5(),
	}, err
}

func (m *WeddingPlanMgr) HandlePreProgressPresent(ctx context.Context, channelId, receiveUid, presentVal uint32) error {
	// 是不是在婚礼中
	isChannelWedding, err := m.IsChannelWedding(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleNewcomerMic IsChannelWedding, channelId: %d, err:%v", channelId, err)
		return err
	}
	if isChannelWedding {
		log.WarnWithCtx(ctx, "HandleNewcomerMic, channelId: %d, isChannelWedding: %v", channelId, isChannelWedding)
		return nil // 当前房间正在举行婚礼, 忽略收礼事件
	}

	// 判断是否是新人收礼
	uidA, uidB, err := m.getNewcomers(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "getNewcomers, channelId: %d, err: %v", channelId, err)
		return err
	}
	isNewcomers := false
	if receiveUid == uidA || receiveUid == uidB {
		isNewcomers = true
	}

	log.InfoWithCtx(ctx, "HandlePreProgressPresent, channelId: %d, receiveUid: %d, presentVal: %d", channelId, receiveUid, presentVal)

	// 增加礼物值
	nowVal, err := m.cache.AddPreProgressGiftValue(ctx, channelId , receiveUid, presentVal);
	if err != nil {
		log.ErrorWithCtx(ctx, "AddPreProgressGiftValue, channelId: %d, receiveUid: %d, presentVal: %d, err: %v", channelId, receiveUid, presentVal, err)
	}

	if !isNewcomers {
		log.InfoWithCtx(ctx, "HandlePreProgressPresent, channelId: %d, receiveUid: %d, presentVal: %d, nowVal: %d", channelId, receiveUid, presentVal, nowVal)
		return nil // 不是新人送礼, 记录一下他收礼值就完了
	}

	if uidA != 0 && uidB != 0 { // 新人送礼, 且目前麦上2人, 要判断一下当前收礼值是否达标
		_, err = m.checkGiftValReachWeddingPlanInit(ctx, channelId, uidA, uidB, receiveUid, nowVal)
		if err != nil {
			log.ErrorWithCtx(ctx, "checkGiftValReachWeddingPlanInit, channelId: %d, uidA: %d, uidB: %d, receiveUid: %d, nowVal: %d, err: %v", channelId, uidA, uidB, receiveUid, nowVal, err)
			return err
		}
	}

	// 推送
	err = m.preProgressInfoUpdatePush(ctx, channelId, receiveUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "preProgressInfoUpdatePush, channelId: %d, receiveUid: %d, err: %v", channelId, receiveUid, err)
		return err
	}

	return nil
}

func (m *WeddingPlanMgr) checkGiftValReachWeddingPlanInit(ctx context.Context, channelId, uidA, uidB, buyerUid, nowVal uint32) (bool, error) {
	// 取付费主题
	targetTheme, err := m.getDefaultPayTheme(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "getDefaultPayTheme, err: %v", err)
		return false, err
	}

	if nowVal >= targetTheme.GetPreProgressPresentTargetVal() { // 如果某一方的礼物值达标, 则达成婚礼
		// 达标了但是目前有婚礼, 忽略
		channelWeddingPlanId, err := m.cache.GetPreProgressWeddingId(ctx, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetPreProgressWeddingId, channelId: %d, err: %v", channelId, err)
			return false, err
		}
		if channelWeddingPlanId != 0 {
			log.InfoWithCtx(ctx, "checkGiftValReachWeddingPlanInit, channelId: %d, uidA: %d, uidB: %d, buyerUid: %d, nowVal: %d, channelWeddingPlanId: %d", channelId, uidA, uidB, buyerUid, nowVal, channelWeddingPlanId)
			return true, nil
		}

		goingPlan, err := m.st.GetGoingPlan(ctx, buyerUid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetGoingPlan, buyerUid: %d, err: %v", buyerUid, err)
			return false, err
		}
		if goingPlan != nil {
			log.WarnWithCtx(ctx, "checkGiftValReachWeddingPlanInit, channelId: %d, uidA: %d, uidB: %d, buyerUid: %d, nowVal: %d, goingPlan: %+v", channelId, uidA, uidB, buyerUid, nowVal, goingPlan)
			return true, nil // 如果当前有进行中的婚礼, 先忽略新流程生成的, 免得影响现有婚礼流程
		}

		// 初始化婚礼计划
		userprofileMap, err := m.rpc.UserprofileCli.BatchGetUserProfileV2(ctx, []uint32{uidA, uidB}, false)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchGetUserProfileV2, uidList: %+v, err: %v", []uint32{uidA, uidB}, err)
			return false, err
		}
		groomUid := uidA
		brideUid := uidB
		if userprofileMap[uidA].GetSex() == 0 {
			groomUid = uidB
			brideUid = uidA
		}
		planId := m.st.GenWeddingOrderId(ctx)
		newPlan := &store.WeddingPlan{
			ID:         planId,
			GroomUid:   groomUid,
			BrideUid:   brideUid,
			ThemeId:    targetTheme.GetThemeId(),
			ThemeType:  store.ThemeTypePay,
			BuyerUid:   buyerUid,
			CreateTime: uint32(time.Now().Unix()),
			ReserveInfo: &store.ReserveInfo{
				ChannelId:   channelId,
			},
			Status:     store.WeddingStatusInit,
		}

		sErr := m.st.InsertWeddingPlan(ctx, newPlan)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "InsertWeddingPlan, newPlan: %+v, err: %v", newPlan, sErr)
			return false, sErr
		}

		log.DebugWithCtx(ctx, "init plan success, uid: %d, plan: %+v", buyerUid, newPlan)

		// 记录婚礼id缓存
		cErr := m.cache.SetPreProgressWeddingId(ctx, channelId, planId)
		if cErr  != nil {
			log.ErrorWithCtx(ctx, "SetPreProgressWeddingId, channelId: %d, planId: %d, err: %v", channelId, planId, cErr)
			return false, cErr
		}

		// 更新婚礼阶段
		cErr = m.cache.SetPreProgressStage(ctx, channelId, uint32(channel_wedding_logic.WeddingPreProgressStage_WEDDING_PRE_PROGRESS_STAGE_WEDDING_PREPARE))
		if cErr != nil {
			log.ErrorWithCtx(ctx, "SetPreProgressStage, channelId: %d, err: %v", channelId, cErr)
			return false, cErr
		}

		log.InfoWithCtx(ctx, "update pre progress stage success. channelId: %d, planId: %d", channelId, planId)
		return true, nil
	}

	return false, nil
}

func (m *WeddingPlanMgr) getDefaultPayTheme(ctx context.Context) (*channel_wedding_conf.ThemeCfg, error) {
	var targetTheme *channel_wedding_conf.ThemeCfg
	// 取付费主题
	themeCfgList, err := m.rpc.WeddingConfCli.GetThemeCfgList(ctx, &channel_wedding_conf.GetThemeCfgListReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetReservedWedding fail to GetAllThemeCfg. err:%v", err)
		return targetTheme, err
	}
	for _, item := range themeCfgList.GetThemeCfgList() {
		if item.GetPriceInfo().GetPriceType() == uint32(presentPb.PresentPriceType_PRESENT_PRICE_TBEAN) {
			targetTheme = item
			break
		}
	}
	return targetTheme, nil
}

func (m *WeddingPlanMgr) StartWedding(ctx context.Context, request *pb.StartWeddingRequest) (*pb.StartWeddingResponse, error) {
	resp := &pb.StartWeddingResponse{}
	planInfo, err := m.GetWeddingPlanByIdNoCheck(ctx, request.WeddingPlanId, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartWedding fail to GetWeddingPlanByIdNoCheck. req: %+v, err:%v", request, err)
		return resp, nil
	}
	// 数据校验
	if planInfo.Status != store.WeddingStatusInit {
		log.ErrorWithCtx(ctx, "StartWedding fail to GetWeddingPlanByIdNoCheck. req: %+v, err:%v", request, err)
		return resp, nil
	}

	if planInfo.ReserveInfo == nil {
		log.ErrorWithCtx(ctx, "StartWedding fail to GetWeddingPlanByIdNoCheck. req: %+v, err:%v", request, err)
		return resp, nil
	}

	if planInfo.ReserveInfo.ChannelId != request.GetChannelId() {
		log.ErrorWithCtx(ctx, "StartWedding fail to GetWeddingPlanByIdNoCheck. req: %+v, err:%v", request, err)
		return resp, nil
	}

	// 获取服装增加时长
	giftValA, giftValB, err := m.cache.GetPreProgressGiftValue(ctx, request.GetChannelId(), planInfo.GroomUid, planInfo.BrideUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartWedding fail to GetPreProgressGiftValue. req: %+v, err:%v", request, err)
		return resp, err
	}
	themeCfgResp, err := m.rpc.WeddingConfCli.GetThemeCfg(ctx, &channel_wedding_conf.GetThemeCfgReq{
		ThemeId: planInfo.ThemeId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "StartWedding fail to GetThemeCfg. req: %+v, err:%v", request, err)
		return resp, err
	}
	buyPrice := giftValA
	if planInfo.BuyerUid == planInfo.BrideUid {
		buyPrice = giftValB
	}
	addDuraion := uint32(0)
	for k, v := range themeCfgResp.GetThemeCfg().GetPreProgressClothesExtraDurationCfg() {
		if buyPrice >= k {
			addDuraion = v
		}
	}
	gabUidList := make([]uint32, 0)
	for _, item := range planInfo.Groomsman {
		gabUidList = append(gabUidList, item.Uid)
	}
	for _, item := range planInfo.Bridesmaid {
		gabUidList = append(gabUidList, item.Uid)
	}

	// 通知房间玩法初始化
	reserveReq := &channel_wedding.ReserveWeddingReq{
		Uid:                planInfo.BuyerUid,
		Cid:                planInfo.ReserveInfo.ChannelId,
		StartTime:          time.Now().Add(1*time.Second).Unix(),
		BrideUid:           planInfo.BrideUid,
		GroomUid:           planInfo.GroomUid,
		WeddingThemeId:     planInfo.ThemeId,
		EndTime:            time.Now().Add(45 * time.Minute).Unix(),
		PlanId:             planInfo.ID,
		ThemeType:          planInfo.ThemeType,
		BridesmaidUidList:  gabUidList,
		SuitExtraSendSec:   addDuraion,
		InitHappinessValue: giftValA+giftValB,
	}
	_, err = m.rpc.ChannelWeddingSvrCli.ReserveWedding(ctx, reserveReq)
	if err != nil && !protocol.IsErrorOf(err, status.ErrChannelWeddingReserveDuplicate) {
		log.ErrorWithCtx(ctx, "ReserveChannel fail to ReserveWedding. weddingPlanId: %d, err:%v", planInfo.ID, err)
		return resp, err
	}

	// 更新预约状态,清理缓存
	err = m.st.BatchUpdateWeddingPlanStatus(ctx, []uint32{planInfo.ID}, store.WeddingStatusReserved)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReserveChannel fail to BatchUpdateWeddingPlanStatus. weddingPlanId: %d, err:%v", planInfo.ID, err)
		return resp, err
	}
	err = m.cache.BatchDelPreProgressCacheWithUid(ctx, request.GetChannelId(), planInfo.GroomUid, planInfo.BuyerUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReserveChannel fail to BatchDelPreProgressCacheWithUid. weddingPlanId: %d, err:%v", planInfo.ID, err)
		return resp, err
	}
	// 推送充值婚礼准备面板
	err = m.preProgressInfoUpdatePush(ctx, request.GetChannelId(), planInfo.BuyerUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReserveChannel fail to preProgressInfoUpdatePush. weddingPlanId: %d, err:%v", planInfo.ID, err)
		return resp, err
	}


	log.DebugWithCtx(ctx, "ReserveChannel success. planInfo: %+v", planInfo)
	return resp, nil
}

func (m *WeddingPlanMgr) CancelPreparedWedding(ctx context.Context, request *pb.CancelPreparedWeddingRequest) (*pb.CancelPreparedWeddingResponse, error) {
	resp := &pb.CancelPreparedWeddingResponse{}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "CancelPreparedWedding ServiceInfoFromContext fail. req:%+v", request)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	uidA, uidB, err := m.getNewcomers(ctx, request.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelPreparedWedding fail to getNewcomers. req:%+v", request)
		return resp, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}
	planInfo := &store.WeddingPlan{ // 未达成婚礼也能取消
		GroomUid: uidA,
		BrideUid: uidB,
	}

	if request.GetWeddingPlanId() != 0 {
		var err error
		planInfo, err = m.GetWeddingPlanByIdNoCheck(ctx, request.WeddingPlanId, false)
		if err != nil {
			log.ErrorWithCtx(ctx, "CancelPreparedWedding fail to GetWeddingPlanByIdNoCheck. req: %+v, err:%v", request, err)
			return resp, err
		}

		// 数据校验
		if planInfo != nil && planInfo.Status != store.WeddingStatusInit {
			log.ErrorWithCtx(ctx, "CancelPreparedWedding fail to GetWeddingPlanByIdNoCheck. req: %+v, err:%v", request, err)
			return resp, err
		}
		if planInfo != nil &&  planInfo.ReserveInfo == nil {
			log.ErrorWithCtx(ctx, "CancelPreparedWedding fail to GetWeddingPlanByIdNoCheck. req: %+v, err:%v", request, err)
			return resp, err
		}
		if planInfo != nil &&  planInfo.ReserveInfo.ChannelId != request.GetChannelId() {
			log.ErrorWithCtx(ctx, "CancelPreparedWedding fail to GetWeddingPlanByIdNoCheck. req: %+v, err:%v", request, err)
			return resp, err
		}
	}

	// 取消婚礼
	err = m.CancelChannelBuyWedding(ctx, request.GetChannelId(), planInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelPreparedWedding fail to CancelChannelBuyWedding. planInfo: %+v, err:%v", planInfo, err)
		return resp, err
	}

	// 推送
	err = m.preProgressInfoUpdatePush(ctx, request.GetChannelId(), serviceInfo.UserID)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelPreparedWedding fail to preProgressInfoUpdatePush. planInfo: %+v, err:%v", planInfo, err)
		return resp, err
	}

	return resp, nil
}

func (m *WeddingPlanMgr) CancelChannelBuyWedding(ctx context.Context, channelId uint32, planInfo *store.WeddingPlan) error {
	if planInfo.ID != 0 { // 有真实weddingPlan
		// 更新状态
		err := m.st.BatchUpdateWeddingPlanStatus(ctx, []uint32{planInfo.ID}, store.WeddingPlanStatusCanceled)
		if err != nil {
			log.ErrorWithCtx(ctx, "CancelChannelBuyWedding fail to BatchUpdateWeddingPlanStatus. planInfo: %+v, err:%v", planInfo, err)
			return err
		}

		// 后处理
		err = m.weddingPlanCancelPostprocess(ctx, planInfo)
		if err != nil {
			log.ErrorWithCtx(ctx, "CancelChannelBuyWedding fail to weddingPlanCancelPostprocess. planInfo: %+v, err:%v", planInfo, err)
			return err
		}
	}


	// 清理缓存
	err := m.cache.BatchDelPreProgressCacheWithUid(ctx, channelId, planInfo.GroomUid, planInfo.BuyerUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelPreparedWedding fail to BatchDelPreProgressCacheWithUid. planInfo: %+v, err:%v", planInfo, err)
		return err
	}

	// 踢人下麦
	_, err = m.rpc.MicMiddleCli.KickOutMic(ctx,  &micMiddle.KickOutMicReq{
		Source:        "channel-wedding",
		OpUid:         0,
		Cid:           channelId,
		TargetUidList: []uint32{planInfo.GroomUid, planInfo.BrideUid},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelChannelBuyWedding fail to KickOutMic. planInfo: %+v, err:%v", planInfo, err)
		return err
	}

	return nil
}

func (m *WeddingPlanMgr) GetPrepareWedding(ctx context.Context, channelId uint32) (bool, uint32, uint32, error) {
	weddingPlanId, err := m.cache.GetPreProgressWeddingId(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPrepareWedding fail to GetWeddingPlanById. channelId: %d, err:%v", channelId, err)
		return false, 0, 0, err
	}
	if weddingPlanId == 0 {
		return false, 0, 0, nil
	}

	planInfo, err := m.GetWeddingPlanByIdNoCheck(ctx, weddingPlanId, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPrepareWedding fail to GetWeddingPlanByIdNoCheck. channelId: %d, err:%v", channelId, err)
		return false, 0, 0, err
	}
	return true, planInfo.BuyerUid, planInfo.GroomUid, nil
}

func (m *WeddingPlanMgr) IsChannelWedding(ctx context.Context, channelId uint32) (bool, error) {
	weddingInfoResp, err := m.rpc.ChannelWeddingSvrCli.GetChannelWeddingInfo(ctx, &channel_wedding.GetChannelWeddingInfoReq{
		Uid: 0,
		Cid: channelId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "IsChannelWedding fail to GetChannelWeddingInfo. channelId: %d, err:%v", channelId, err)
		return false, err
	}
	if weddingInfoResp.GetWeddingInfo().GetStageInfo().GetCurrStage() != uint32(channel_wedding.WeddingStage_WEDDING_STAGE_UNSPECIFIED) {
		return true, nil
	}

	return false, nil
}

