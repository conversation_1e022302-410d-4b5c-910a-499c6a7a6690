package conf

import (
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"gitlab.ttyuyin.com/bizFund/bizFund/pkg/config"
	pkgConfig "golang.52tt.com/pkg/config"
)

type StartConfig struct {
	// from config file
	RedisConfig           *redisConnect.RedisConfig `json:"redis"`
	MongoConfig           *pkgConfig.MongoConfig    `json:"mongo"`
	UserOnlineKfk         *config.KafkaConfig       `json:"user_online_kfk"`
	UserInfoKfk           *config.KafkaConfig       `json:"user_info_kfk"`
	FellowKfk             *config.KafkaConfig       `json:"fellow_kfk"`
	ChannelMicKafkaConfig *config.KafkaConfig       `json:"channel_mic_kfk"`
	PresentKafkaConfig    *config.KafkaConfig       `json:"present_kafka"`
}
