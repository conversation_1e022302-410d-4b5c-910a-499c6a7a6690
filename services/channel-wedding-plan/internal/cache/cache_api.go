package cache

import (
	context "context"
	redis "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	store "golang.52tt.com/services/channel-wedding-plan/internal/store"
	time "time"
)

type ICache interface {
	AddDivideTimeout(ctx context.Context, uid uint32) (bool,error)
	AddPreProgressGiftValue(ctx context.Context, channelId, uid uint32, value uint32) (uint32,error)
	BatSetWeddingAnniversaryPopupInfo(ctx context.Context, anniversaryDay int, dayTime time.Time, cpList []*AnniversaryCp) error
	BatchDelPreProgressCacheWithUid(ctx context.Context, channelId, uidA, uidB uint32) error
	BatchDelPreProgressGiftValue(ctx context.Context, channelId uint32, uids ...uint32) error
	BatchGetUserDivorceTimeout(ctx context.Context, uidList []uint32) (map[uint32]uint64,error)
	CheckNextIfAnniversaryPopupInfoExist(ctx context.Context, anniversaryDay int, t time.Time) (bool,error)
	Close() error
	DelAnniversaryPopupInfoByUid(ctx context.Context, uid uint32, anniversaryDay int, t time.Time) error
	DelNewcomersMic(ctx context.Context, channelId, newcomerType uint32) error
	DelPreProgressStage(ctx context.Context, channelId uint32) error
	DelRelationHideSwitch(ctx context.Context, uid uint32) error
	DelUserConsultReserveTimeSection(ctx context.Context, uid, cid uint32, reserveSt, reserveEt uint32) error
	DelWeddingAnniversaryPopupFlag(ctx context.Context, uid uint32, t time.Time) error
	DelWeddingPlan(ctx context.Context, weddingPlanId uint32) error
	DeletePreProgressWeddingId(ctx context.Context, channelId uint32) error
	GetAllAnniversaryPopupInfo(ctx context.Context, anniversaryDay int, t time.Time) (cpMap map[uint32]uint32,err error)
	GetAnniversaryPopupByUid(ctx context.Context, uid uint32, anniversaryDay int, t time.Time) (cpUid uint32,err error)
	GetDivideTimeout(ctx context.Context, timeOut int64) ([]uint32,error)
	GetMyDivideTimeout(ctx context.Context, uid uint32) (uint64,error)
	GetNewcomersMic(ctx context.Context, channelId, newcomerType uint32) (uint32,error)
	GetPreProgressGiftValue(ctx context.Context, channelId, uidA, uidB uint32) (uint32,uint32,error)
	GetPreProgressStage(ctx context.Context, channelId uint32) (uint32,error)
	GetPreProgressWeddingId(ctx context.Context, channelId uint32) (uint32,error)
	GetRedisClient() redis.Cmdable
	GetRelationHideSwitch(ctx context.Context, uid uint32) (bool,uint32,error)
	GetUserConsultReserveManagerUid(ctx context.Context, uid, cid uint32) (uint32,error)
	GetUserConsultReserveTimeSection(ctx context.Context, uid, cid uint32, reserveSt, reserveEt uint32) (uint32,error)
	GetWeddingPlan(ctx context.Context, weddingPlanId uint32) (*store.WeddingPlan,error)
	RemoveDivideTimeout(ctx context.Context, uid uint32) (bool,error)
	SetFirstOnMic(ctx context.Context, channelId, uid uint32) error
	SetNewcomersMic(ctx context.Context, channelId, newcomerType uint32, uid uint32) error
	SetPreProgressStage(ctx context.Context, channelId uint32, stage uint32) error
	SetPreProgressWeddingId(ctx context.Context, channelId, weddingId uint32) error
	SetRelationHideSwitch(ctx context.Context, uid uint32, status uint32) error
	SetUserConsultReserveManagerUid(ctx context.Context, uid, cid, managerUid uint32, expire time.Duration) error
	SetUserConsultReserveTimeSection(ctx context.Context, uid, cid uint32, reserveSt, reserveEt, managerUid uint32, expire time.Duration) error
	SetWeddingAnniversaryPopupFlag(ctx context.Context, uid uint32, t time.Time) (bool,error)
	SetWeddingPlan(ctx context.Context, weddingPlanId uint32, weddingPlan *store.WeddingPlan) error
	TryLock(ctx context.Context, key, val string, timeOutList ...time.Duration) error
	TtlFirstOnMic(ctx context.Context, channelId, uid uint32) (time.Duration,error)
	Unlock(ctx context.Context, key, val string) error
}

