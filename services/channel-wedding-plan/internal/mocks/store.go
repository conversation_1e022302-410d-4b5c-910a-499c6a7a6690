// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-wedding-plan/internal/store (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	primitive "go.mongodb.org/mongo-driver/bson/primitive"
	channel_wedding_plan "golang.52tt.com/protocol/services/channel-wedding-plan"
	store "golang.52tt.com/services/channel-wedding-plan/internal/store"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// AddProposeInfo mocks base method.
func (m *MockIStore) AddProposeInfo(arg0 context.Context, arg1 *store.ProposeInfo) (primitive.ObjectID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddProposeInfo", arg0, arg1)
	ret0, _ := ret[0].(primitive.ObjectID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddProposeInfo indicates an expected call of AddProposeInfo.
func (mr *MockIStoreMockRecorder) AddProposeInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddProposeInfo", reflect.TypeOf((*MockIStore)(nil).AddProposeInfo), arg0, arg1)
}

// AddWeddingBigScreen mocks base method.
func (m *MockIStore) AddWeddingBigScreen(arg0 context.Context, arg1 uint32, arg2 *store.BigScreenItem) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddWeddingBigScreen", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddWeddingBigScreen indicates an expected call of AddWeddingBigScreen.
func (mr *MockIStoreMockRecorder) AddWeddingBigScreen(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddWeddingBigScreen", reflect.TypeOf((*MockIStore)(nil).AddWeddingBigScreen), arg0, arg1, arg2)
}

// BatGetWeddingPlan mocks base method.
func (m *MockIStore) BatGetWeddingPlan(arg0 context.Context, arg1 []uint32) ([]*store.WeddingPlan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetWeddingPlan", arg0, arg1)
	ret0, _ := ret[0].([]*store.WeddingPlan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetWeddingPlan indicates an expected call of BatGetWeddingPlan.
func (mr *MockIStoreMockRecorder) BatGetWeddingPlan(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetWeddingPlan", reflect.TypeOf((*MockIStore)(nil).BatGetWeddingPlan), arg0, arg1)
}

// BatGetWeddingSubscribeStatus mocks base method.
func (m *MockIStore) BatGetWeddingSubscribeStatus(arg0 context.Context, arg1 uint32, arg2 []uint32) (map[uint32]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetWeddingSubscribeStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetWeddingSubscribeStatus indicates an expected call of BatGetWeddingSubscribeStatus.
func (mr *MockIStoreMockRecorder) BatGetWeddingSubscribeStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetWeddingSubscribeStatus", reflect.TypeOf((*MockIStore)(nil).BatGetWeddingSubscribeStatus), arg0, arg1, arg2)
}

// BatchClearReserveInfo mocks base method.
func (m *MockIStore) BatchClearReserveInfo(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchClearReserveInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchClearReserveInfo indicates an expected call of BatchClearReserveInfo.
func (mr *MockIStoreMockRecorder) BatchClearReserveInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchClearReserveInfo", reflect.TypeOf((*MockIStore)(nil).BatchClearReserveInfo), arg0, arg1)
}

// BatchGetMarriageRelationByUidList mocks base method.
func (m *MockIStore) BatchGetMarriageRelationByUidList(arg0 context.Context, arg1 []uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetMarriageRelationByUidList", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetMarriageRelationByUidList indicates an expected call of BatchGetMarriageRelationByUidList.
func (mr *MockIStoreMockRecorder) BatchGetMarriageRelationByUidList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetMarriageRelationByUidList", reflect.TypeOf((*MockIStore)(nil).BatchGetMarriageRelationByUidList), arg0, arg1)
}

// BatchGetWeddingOrderByPlanIdList mocks base method.
func (m *MockIStore) BatchGetWeddingOrderByPlanIdList(arg0 context.Context, arg1 []uint32) ([]*store.WeddingOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetWeddingOrderByPlanIdList", arg0, arg1)
	ret0, _ := ret[0].([]*store.WeddingOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetWeddingOrderByPlanIdList indicates an expected call of BatchGetWeddingOrderByPlanIdList.
func (mr *MockIStoreMockRecorder) BatchGetWeddingOrderByPlanIdList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetWeddingOrderByPlanIdList", reflect.TypeOf((*MockIStore)(nil).BatchGetWeddingOrderByPlanIdList), arg0, arg1)
}

// BatchGetWeddingReserveIMByIdList mocks base method.
func (m *MockIStore) BatchGetWeddingReserveIMByIdList(arg0 context.Context, arg1 []uint32) ([]*store.WeddingReserveIM, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetWeddingReserveIMByIdList", arg0, arg1)
	ret0, _ := ret[0].([]*store.WeddingReserveIM)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetWeddingReserveIMByIdList indicates an expected call of BatchGetWeddingReserveIMByIdList.
func (mr *MockIStoreMockRecorder) BatchGetWeddingReserveIMByIdList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetWeddingReserveIMByIdList", reflect.TypeOf((*MockIStore)(nil).BatchGetWeddingReserveIMByIdList), arg0, arg1)
}

// BatchUpdateWeddingPlanStatus mocks base method.
func (m *MockIStore) BatchUpdateWeddingPlanStatus(arg0 context.Context, arg1 []uint32, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateWeddingPlanStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdateWeddingPlanStatus indicates an expected call of BatchUpdateWeddingPlanStatus.
func (mr *MockIStoreMockRecorder) BatchUpdateWeddingPlanStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateWeddingPlanStatus", reflect.TypeOf((*MockIStore)(nil).BatchUpdateWeddingPlanStatus), arg0, arg1, arg2)
}

// ChangeConsumeRecordPayInfo mocks base method.
func (m *MockIStore) ChangeConsumeRecordPayInfo(arg0 context.Context, arg1 time.Time, arg2 uint32, arg3 []uint32, arg4 uint32, arg5, arg6, arg7 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChangeConsumeRecordPayInfo", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChangeConsumeRecordPayInfo indicates an expected call of ChangeConsumeRecordPayInfo.
func (mr *MockIStoreMockRecorder) ChangeConsumeRecordPayInfo(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeConsumeRecordPayInfo", reflect.TypeOf((*MockIStore)(nil).ChangeConsumeRecordPayInfo), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
}

// Close mocks base method.
func (m *MockIStore) Close(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockIStoreMockRecorder) Close(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIStore)(nil).Close), arg0)
}

// CountWeddingPlanByChannelIdReserveTime mocks base method.
func (m *MockIStore) CountWeddingPlanByChannelIdReserveTime(arg0 context.Context, arg1 []uint32, arg2, arg3 int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountWeddingPlanByChannelIdReserveTime", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountWeddingPlanByChannelIdReserveTime indicates an expected call of CountWeddingPlanByChannelIdReserveTime.
func (mr *MockIStoreMockRecorder) CountWeddingPlanByChannelIdReserveTime(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountWeddingPlanByChannelIdReserveTime", reflect.TypeOf((*MockIStore)(nil).CountWeddingPlanByChannelIdReserveTime), arg0, arg1, arg2, arg3)
}

// CreateThemeCfg mocks base method.
func (m *MockIStore) CreateThemeCfg(arg0 context.Context, arg1 *channel_wedding_plan.AddThemeCfgRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateThemeCfg", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateThemeCfg indicates an expected call of CreateThemeCfg.
func (mr *MockIStoreMockRecorder) CreateThemeCfg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateThemeCfg", reflect.TypeOf((*MockIStore)(nil).CreateThemeCfg), arg0, arg1)
}

// DelChannelBanTime mocks base method.
func (m *MockIStore) DelChannelBanTime(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelChannelBanTime", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelChannelBanTime indicates an expected call of DelChannelBanTime.
func (mr *MockIStoreMockRecorder) DelChannelBanTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChannelBanTime", reflect.TypeOf((*MockIStore)(nil).DelChannelBanTime), arg0, arg1)
}

// DelWeddingBigScreen mocks base method.
func (m *MockIStore) DelWeddingBigScreen(arg0 context.Context, arg1 uint32, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelWeddingBigScreen", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelWeddingBigScreen indicates an expected call of DelWeddingBigScreen.
func (mr *MockIStoreMockRecorder) DelWeddingBigScreen(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelWeddingBigScreen", reflect.TypeOf((*MockIStore)(nil).DelWeddingBigScreen), arg0, arg1, arg2)
}

// DeleteCloseChannelReserveTimeSectionConf mocks base method.
func (m *MockIStore) DeleteCloseChannelReserveTimeSectionConf(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCloseChannelReserveTimeSectionConf", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteCloseChannelReserveTimeSectionConf indicates an expected call of DeleteCloseChannelReserveTimeSectionConf.
func (mr *MockIStoreMockRecorder) DeleteCloseChannelReserveTimeSectionConf(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCloseChannelReserveTimeSectionConf", reflect.TypeOf((*MockIStore)(nil).DeleteCloseChannelReserveTimeSectionConf), arg0, arg1, arg2, arg3)
}

// DeleteMarriageRelation mocks base method.
func (m *MockIStore) DeleteMarriageRelation(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteMarriageRelation", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteMarriageRelation indicates an expected call of DeleteMarriageRelation.
func (mr *MockIStoreMockRecorder) DeleteMarriageRelation(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteMarriageRelation", reflect.TypeOf((*MockIStore)(nil).DeleteMarriageRelation), arg0, arg1, arg2)
}

// DeleteThemeCfg mocks base method.
func (m *MockIStore) DeleteThemeCfg(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteThemeCfg", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteThemeCfg indicates an expected call of DeleteThemeCfg.
func (mr *MockIStoreMockRecorder) DeleteThemeCfg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteThemeCfg", reflect.TypeOf((*MockIStore)(nil).DeleteThemeCfg), arg0, arg1)
}

// Divorce mocks base method.
func (m *MockIStore) Divorce(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Divorce", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Divorce indicates an expected call of Divorce.
func (mr *MockIStoreMockRecorder) Divorce(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Divorce", reflect.TypeOf((*MockIStore)(nil).Divorce), arg0, arg1, arg2)
}

// GenWeddingOrderId mocks base method.
func (m *MockIStore) GenWeddingOrderId(arg0 context.Context) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenWeddingOrderId", arg0)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GenWeddingOrderId indicates an expected call of GenWeddingOrderId.
func (mr *MockIStoreMockRecorder) GenWeddingOrderId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenWeddingOrderId", reflect.TypeOf((*MockIStore)(nil).GenWeddingOrderId), arg0)
}

// GenWeddingPlanId mocks base method.
func (m *MockIStore) GenWeddingPlanId(arg0 context.Context) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenWeddingPlanId", arg0)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GenWeddingPlanId indicates an expected call of GenWeddingPlanId.
func (mr *MockIStoreMockRecorder) GenWeddingPlanId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenWeddingPlanId", reflect.TypeOf((*MockIStore)(nil).GenWeddingPlanId), arg0)
}

// GenWeddingReserveIMId mocks base method.
func (m *MockIStore) GenWeddingReserveIMId(arg0 context.Context) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenWeddingReserveIMId", arg0)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GenWeddingReserveIMId indicates an expected call of GenWeddingReserveIMId.
func (mr *MockIStoreMockRecorder) GenWeddingReserveIMId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenWeddingReserveIMId", reflect.TypeOf((*MockIStore)(nil).GenWeddingReserveIMId), arg0)
}

// GetAllStartupWeddingPlan mocks base method.
func (m *MockIStore) GetAllStartupWeddingPlan(arg0 context.Context, arg1 int64) ([]*store.WeddingPlan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllStartupWeddingPlan", arg0, arg1)
	ret0, _ := ret[0].([]*store.WeddingPlan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllStartupWeddingPlan indicates an expected call of GetAllStartupWeddingPlan.
func (mr *MockIStoreMockRecorder) GetAllStartupWeddingPlan(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllStartupWeddingPlan", reflect.TypeOf((*MockIStore)(nil).GetAllStartupWeddingPlan), arg0, arg1)
}

// GetChannelBanTimeByDateTime mocks base method.
func (m *MockIStore) GetChannelBanTimeByDateTime(arg0 context.Context, arg1, arg2 uint32) ([]*store.ChannelBanTime, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelBanTimeByDateTime", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.ChannelBanTime)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelBanTimeByDateTime indicates an expected call of GetChannelBanTimeByDateTime.
func (mr *MockIStoreMockRecorder) GetChannelBanTimeByDateTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelBanTimeByDateTime", reflect.TypeOf((*MockIStore)(nil).GetChannelBanTimeByDateTime), arg0, arg1, arg2)
}

// GetConsumeOrderIds mocks base method.
func (m *MockIStore) GetConsumeOrderIds(arg0 context.Context, arg1, arg2 time.Time) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsumeOrderIds", arg0, arg1, arg2)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeOrderIds indicates an expected call of GetConsumeOrderIds.
func (mr *MockIStoreMockRecorder) GetConsumeOrderIds(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeOrderIds", reflect.TypeOf((*MockIStore)(nil).GetConsumeOrderIds), arg0, arg1, arg2)
}

// GetConsumeTotalCountInfo mocks base method.
func (m *MockIStore) GetConsumeTotalCountInfo(arg0 context.Context, arg1, arg2 time.Time) (*store.StCount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsumeTotalCountInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.StCount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeTotalCountInfo indicates an expected call of GetConsumeTotalCountInfo.
func (mr *MockIStoreMockRecorder) GetConsumeTotalCountInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeTotalCountInfo", reflect.TypeOf((*MockIStore)(nil).GetConsumeTotalCountInfo), arg0, arg1, arg2)
}

// GetGoingPlan mocks base method.
func (m *MockIStore) GetGoingPlan(arg0 context.Context, arg1 uint32) (*store.WeddingPlan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGoingPlan", arg0, arg1)
	ret0, _ := ret[0].(*store.WeddingPlan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGoingPlan indicates an expected call of GetGoingPlan.
func (mr *MockIStoreMockRecorder) GetGoingPlan(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGoingPlan", reflect.TypeOf((*MockIStore)(nil).GetGoingPlan), arg0, arg1)
}

// GetGoingWeddingBridesmaidInvite mocks base method.
func (m *MockIStore) GetGoingWeddingBridesmaidInvite(arg0 context.Context, arg1, arg2 uint32) ([]*store.WeddingGuestInvite, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGoingWeddingBridesmaidInvite", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.WeddingGuestInvite)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGoingWeddingBridesmaidInvite indicates an expected call of GetGoingWeddingBridesmaidInvite.
func (mr *MockIStoreMockRecorder) GetGoingWeddingBridesmaidInvite(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGoingWeddingBridesmaidInvite", reflect.TypeOf((*MockIStore)(nil).GetGoingWeddingBridesmaidInvite), arg0, arg1, arg2)
}

// GetMarriageRelation mocks base method.
func (m *MockIStore) GetMarriageRelation(arg0 context.Context, arg1, arg2 uint32) (*store.MarriageRelation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMarriageRelation", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.MarriageRelation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMarriageRelation indicates an expected call of GetMarriageRelation.
func (mr *MockIStoreMockRecorder) GetMarriageRelation(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMarriageRelation", reflect.TypeOf((*MockIStore)(nil).GetMarriageRelation), arg0, arg1, arg2)
}

// GetMarriageRelationByIdList mocks base method.
func (m *MockIStore) GetMarriageRelationByIdList(arg0 context.Context, arg1 []string) ([]*store.MarriageRelation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMarriageRelationByIdList", arg0, arg1)
	ret0, _ := ret[0].([]*store.MarriageRelation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMarriageRelationByIdList indicates an expected call of GetMarriageRelationByIdList.
func (mr *MockIStoreMockRecorder) GetMarriageRelationByIdList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMarriageRelationByIdList", reflect.TypeOf((*MockIStore)(nil).GetMarriageRelationByIdList), arg0, arg1)
}

// GetMarriageRelationByUid mocks base method.
func (m *MockIStore) GetMarriageRelationByUid(arg0 context.Context, arg1 uint32) (*store.MarriageRelation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMarriageRelationByUid", arg0, arg1)
	ret0, _ := ret[0].(*store.MarriageRelation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMarriageRelationByUid indicates an expected call of GetMarriageRelationByUid.
func (mr *MockIStoreMockRecorder) GetMarriageRelationByUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMarriageRelationByUid", reflect.TypeOf((*MockIStore)(nil).GetMarriageRelationByUid), arg0, arg1)
}

// GetMyProposeInfo mocks base method.
func (m *MockIStore) GetMyProposeInfo(arg0 context.Context, arg1, arg2 uint32) (*store.ProposeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyProposeInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.ProposeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyProposeInfo indicates an expected call of GetMyProposeInfo.
func (mr *MockIStoreMockRecorder) GetMyProposeInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyProposeInfo", reflect.TypeOf((*MockIStore)(nil).GetMyProposeInfo), arg0, arg1, arg2)
}

// GetNoChannelFreeWeddingPlanByTimeRange mocks base method.
func (m *MockIStore) GetNoChannelFreeWeddingPlanByTimeRange(arg0 context.Context, arg1, arg2 uint32, arg3 int64) ([]*store.WeddingPlan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNoChannelFreeWeddingPlanByTimeRange", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*store.WeddingPlan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNoChannelFreeWeddingPlanByTimeRange indicates an expected call of GetNoChannelFreeWeddingPlanByTimeRange.
func (mr *MockIStoreMockRecorder) GetNoChannelFreeWeddingPlanByTimeRange(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNoChannelFreeWeddingPlanByTimeRange", reflect.TypeOf((*MockIStore)(nil).GetNoChannelFreeWeddingPlanByTimeRange), arg0, arg1, arg2, arg3)
}

// GetOneDayChannelReserveTimeSectionConf mocks base method.
func (m *MockIStore) GetOneDayChannelReserveTimeSectionConf(arg0 context.Context, arg1, arg2 uint32) ([]*store.ChannelReserveTimeSectionConf, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOneDayChannelReserveTimeSectionConf", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.ChannelReserveTimeSectionConf)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOneDayChannelReserveTimeSectionConf indicates an expected call of GetOneDayChannelReserveTimeSectionConf.
func (mr *MockIStoreMockRecorder) GetOneDayChannelReserveTimeSectionConf(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOneDayChannelReserveTimeSectionConf", reflect.TypeOf((*MockIStore)(nil).GetOneDayChannelReserveTimeSectionConf), arg0, arg1, arg2)
}

// GetOrderList mocks base method.
func (m *MockIStore) GetOrderList(arg0 context.Context, arg1 []uint32, arg2 int64) ([]*store.WeddingOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.WeddingOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderList indicates an expected call of GetOrderList.
func (mr *MockIStoreMockRecorder) GetOrderList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderList", reflect.TypeOf((*MockIStore)(nil).GetOrderList), arg0, arg1, arg2)
}

// GetProposeInfoByID mocks base method.
func (m *MockIStore) GetProposeInfoByID(arg0 context.Context, arg1 string) (*store.ProposeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProposeInfoByID", arg0, arg1)
	ret0, _ := ret[0].(*store.ProposeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProposeInfoByID indicates an expected call of GetProposeInfoByID.
func (mr *MockIStoreMockRecorder) GetProposeInfoByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProposeInfoByID", reflect.TypeOf((*MockIStore)(nil).GetProposeInfoByID), arg0, arg1)
}

// GetProposeInfoByUidAndStatus mocks base method.
func (m *MockIStore) GetProposeInfoByUidAndStatus(arg0 context.Context, arg1, arg2 uint32) ([]*store.ProposeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProposeInfoByUidAndStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.ProposeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProposeInfoByUidAndStatus indicates an expected call of GetProposeInfoByUidAndStatus.
func (mr *MockIStoreMockRecorder) GetProposeInfoByUidAndStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProposeInfoByUidAndStatus", reflect.TypeOf((*MockIStore)(nil).GetProposeInfoByUidAndStatus), arg0, arg1, arg2)
}

// GetRelationHideSwitch mocks base method.
func (m *MockIStore) GetRelationHideSwitch(arg0 context.Context, arg1 uint32) (bool, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRelationHideSwitch", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetRelationHideSwitch indicates an expected call of GetRelationHideSwitch.
func (mr *MockIStoreMockRecorder) GetRelationHideSwitch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationHideSwitch", reflect.TypeOf((*MockIStore)(nil).GetRelationHideSwitch), arg0, arg1)
}

// GetSendProposeInfoByUid mocks base method.
func (m *MockIStore) GetSendProposeInfoByUid(arg0 context.Context, arg1 uint32) (*store.ProposeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSendProposeInfoByUid", arg0, arg1)
	ret0, _ := ret[0].(*store.ProposeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSendProposeInfoByUid indicates an expected call of GetSendProposeInfoByUid.
func (mr *MockIStoreMockRecorder) GetSendProposeInfoByUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSendProposeInfoByUid", reflect.TypeOf((*MockIStore)(nil).GetSendProposeInfoByUid), arg0, arg1)
}

// GetTimoutProposeList mocks base method.
func (m *MockIStore) GetTimoutProposeList(arg0 context.Context) ([]*store.ProposeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTimoutProposeList", arg0)
	ret0, _ := ret[0].([]*store.ProposeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTimoutProposeList indicates an expected call of GetTimoutProposeList.
func (mr *MockIStoreMockRecorder) GetTimoutProposeList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTimoutProposeList", reflect.TypeOf((*MockIStore)(nil).GetTimoutProposeList), arg0)
}

// GetTodayAllComingWeddingList mocks base method.
func (m *MockIStore) GetTodayAllComingWeddingList(arg0 context.Context) ([]*store.WeddingPlan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTodayAllComingWeddingList", arg0)
	ret0, _ := ret[0].([]*store.WeddingPlan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTodayAllComingWeddingList indicates an expected call of GetTodayAllComingWeddingList.
func (mr *MockIStoreMockRecorder) GetTodayAllComingWeddingList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTodayAllComingWeddingList", reflect.TypeOf((*MockIStore)(nil).GetTodayAllComingWeddingList), arg0)
}

// GetValidWeddingOrderCnt mocks base method.
func (m *MockIStore) GetValidWeddingOrderCnt(arg0 context.Context, arg1 uint32, arg2 int64, arg3 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetValidWeddingOrderCnt", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetValidWeddingOrderCnt indicates an expected call of GetValidWeddingOrderCnt.
func (mr *MockIStoreMockRecorder) GetValidWeddingOrderCnt(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetValidWeddingOrderCnt", reflect.TypeOf((*MockIStore)(nil).GetValidWeddingOrderCnt), arg0, arg1, arg2, arg3)
}

// GetWeddingGuestInviteById mocks base method.
func (m *MockIStore) GetWeddingGuestInviteById(arg0 context.Context, arg1 uint32) (*store.WeddingGuestInvite, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingGuestInviteById", arg0, arg1)
	ret0, _ := ret[0].(*store.WeddingGuestInvite)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingGuestInviteById indicates an expected call of GetWeddingGuestInviteById.
func (mr *MockIStoreMockRecorder) GetWeddingGuestInviteById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingGuestInviteById", reflect.TypeOf((*MockIStore)(nil).GetWeddingGuestInviteById), arg0, arg1)
}

// GetWeddingGuestInviteByWeddingPlanId mocks base method.
func (m *MockIStore) GetWeddingGuestInviteByWeddingPlanId(arg0 context.Context, arg1 uint32) ([]*store.WeddingGuestInvite, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingGuestInviteByWeddingPlanId", arg0, arg1)
	ret0, _ := ret[0].([]*store.WeddingGuestInvite)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingGuestInviteByWeddingPlanId indicates an expected call of GetWeddingGuestInviteByWeddingPlanId.
func (mr *MockIStoreMockRecorder) GetWeddingGuestInviteByWeddingPlanId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingGuestInviteByWeddingPlanId", reflect.TypeOf((*MockIStore)(nil).GetWeddingGuestInviteByWeddingPlanId), arg0, arg1)
}

// GetWeddingGuestInviteByWeddingPlanIdGuestType mocks base method.
func (m *MockIStore) GetWeddingGuestInviteByWeddingPlanIdGuestType(arg0 context.Context, arg1 uint32, arg2 []uint32) ([]*store.WeddingGuestInvite, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingGuestInviteByWeddingPlanIdGuestType", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.WeddingGuestInvite)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingGuestInviteByWeddingPlanIdGuestType indicates an expected call of GetWeddingGuestInviteByWeddingPlanIdGuestType.
func (mr *MockIStoreMockRecorder) GetWeddingGuestInviteByWeddingPlanIdGuestType(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingGuestInviteByWeddingPlanIdGuestType", reflect.TypeOf((*MockIStore)(nil).GetWeddingGuestInviteByWeddingPlanIdGuestType), arg0, arg1, arg2)
}

// GetWeddingGuestInviteByWeddingPlanIdGuestTypeUidStatus mocks base method.
func (m *MockIStore) GetWeddingGuestInviteByWeddingPlanIdGuestTypeUidStatus(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 []uint32) (*store.WeddingGuestInvite, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingGuestInviteByWeddingPlanIdGuestTypeUidStatus", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*store.WeddingGuestInvite)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingGuestInviteByWeddingPlanIdGuestTypeUidStatus indicates an expected call of GetWeddingGuestInviteByWeddingPlanIdGuestTypeUidStatus.
func (mr *MockIStoreMockRecorder) GetWeddingGuestInviteByWeddingPlanIdGuestTypeUidStatus(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingGuestInviteByWeddingPlanIdGuestTypeUidStatus", reflect.TypeOf((*MockIStore)(nil).GetWeddingGuestInviteByWeddingPlanIdGuestTypeUidStatus), arg0, arg1, arg2, arg3, arg4)
}

// GetWeddingGuestInviteWaitingByInviteIdUid mocks base method.
func (m *MockIStore) GetWeddingGuestInviteWaitingByInviteIdUid(arg0 context.Context, arg1, arg2 uint32) (*store.WeddingGuestInvite, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingGuestInviteWaitingByInviteIdUid", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.WeddingGuestInvite)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingGuestInviteWaitingByInviteIdUid indicates an expected call of GetWeddingGuestInviteWaitingByInviteIdUid.
func (mr *MockIStoreMockRecorder) GetWeddingGuestInviteWaitingByInviteIdUid(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingGuestInviteWaitingByInviteIdUid", reflect.TypeOf((*MockIStore)(nil).GetWeddingGuestInviteWaitingByInviteIdUid), arg0, arg1, arg2)
}

// GetWeddingGuestInviteWaitingByWeddingPlanIdUid mocks base method.
func (m *MockIStore) GetWeddingGuestInviteWaitingByWeddingPlanIdUid(arg0 context.Context, arg1, arg2 uint32) ([]*store.WeddingGuestInvite, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingGuestInviteWaitingByWeddingPlanIdUid", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.WeddingGuestInvite)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingGuestInviteWaitingByWeddingPlanIdUid indicates an expected call of GetWeddingGuestInviteWaitingByWeddingPlanIdUid.
func (mr *MockIStoreMockRecorder) GetWeddingGuestInviteWaitingByWeddingPlanIdUid(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingGuestInviteWaitingByWeddingPlanIdUid", reflect.TypeOf((*MockIStore)(nil).GetWeddingGuestInviteWaitingByWeddingPlanIdUid), arg0, arg1, arg2)
}

// GetWeddingOrderById mocks base method.
func (m *MockIStore) GetWeddingOrderById(arg0 context.Context, arg1 string) (*store.WeddingOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingOrderById", arg0, arg1)
	ret0, _ := ret[0].(*store.WeddingOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingOrderById indicates an expected call of GetWeddingOrderById.
func (mr *MockIStoreMockRecorder) GetWeddingOrderById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingOrderById", reflect.TypeOf((*MockIStore)(nil).GetWeddingOrderById), arg0, arg1)
}

// GetWeddingOrderByPlanId mocks base method.
func (m *MockIStore) GetWeddingOrderByPlanId(arg0 context.Context, arg1 uint32) (*store.WeddingOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingOrderByPlanId", arg0, arg1)
	ret0, _ := ret[0].(*store.WeddingOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingOrderByPlanId indicates an expected call of GetWeddingOrderByPlanId.
func (mr *MockIStoreMockRecorder) GetWeddingOrderByPlanId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingOrderByPlanId", reflect.TypeOf((*MockIStore)(nil).GetWeddingOrderByPlanId), arg0, arg1)
}

// GetWeddingPlan mocks base method.
func (m *MockIStore) GetWeddingPlan(arg0 context.Context, arg1 uint32) (*store.WeddingPlan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingPlan", arg0, arg1)
	ret0, _ := ret[0].(*store.WeddingPlan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingPlan indicates an expected call of GetWeddingPlan.
func (mr *MockIStoreMockRecorder) GetWeddingPlan(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingPlan", reflect.TypeOf((*MockIStore)(nil).GetWeddingPlan), arg0, arg1)
}

// GetWeddingPlanByChannelIdTimeRange mocks base method.
func (m *MockIStore) GetWeddingPlanByChannelIdTimeRange(arg0 context.Context, arg1, arg2, arg3 uint32) ([]*store.WeddingPlan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingPlanByChannelIdTimeRange", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*store.WeddingPlan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingPlanByChannelIdTimeRange indicates an expected call of GetWeddingPlanByChannelIdTimeRange.
func (mr *MockIStoreMockRecorder) GetWeddingPlanByChannelIdTimeRange(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingPlanByChannelIdTimeRange", reflect.TypeOf((*MockIStore)(nil).GetWeddingPlanByChannelIdTimeRange), arg0, arg1, arg2, arg3)
}

// GetWeddingPlanByEndTimeTimeRangeAndStatus mocks base method.
func (m *MockIStore) GetWeddingPlanByEndTimeTimeRangeAndStatus(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 []uint32) ([]*store.WeddingPlan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingPlanByEndTimeTimeRangeAndStatus", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*store.WeddingPlan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingPlanByEndTimeTimeRangeAndStatus indicates an expected call of GetWeddingPlanByEndTimeTimeRangeAndStatus.
func (mr *MockIStoreMockRecorder) GetWeddingPlanByEndTimeTimeRangeAndStatus(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingPlanByEndTimeTimeRangeAndStatus", reflect.TypeOf((*MockIStore)(nil).GetWeddingPlanByEndTimeTimeRangeAndStatus), arg0, arg1, arg2, arg3, arg4)
}

// GetWeddingPlanById mocks base method.
func (m *MockIStore) GetWeddingPlanById(arg0 context.Context, arg1 uint32) (*store.WeddingPlan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingPlanById", arg0, arg1)
	ret0, _ := ret[0].(*store.WeddingPlan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingPlanById indicates an expected call of GetWeddingPlanById.
func (mr *MockIStoreMockRecorder) GetWeddingPlanById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingPlanById", reflect.TypeOf((*MockIStore)(nil).GetWeddingPlanById), arg0, arg1)
}

// GetWeddingPlanByThemeTypeTimeRange mocks base method.
func (m *MockIStore) GetWeddingPlanByThemeTypeTimeRange(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) ([]*store.WeddingPlan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingPlanByThemeTypeTimeRange", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*store.WeddingPlan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingPlanByThemeTypeTimeRange indicates an expected call of GetWeddingPlanByThemeTypeTimeRange.
func (mr *MockIStoreMockRecorder) GetWeddingPlanByThemeTypeTimeRange(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingPlanByThemeTypeTimeRange", reflect.TypeOf((*MockIStore)(nil).GetWeddingPlanByThemeTypeTimeRange), arg0, arg1, arg2, arg3, arg4)
}

// GetWeddingPlanByTimeRangeStatus mocks base method.
func (m *MockIStore) GetWeddingPlanByTimeRangeStatus(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 []uint32) ([]*store.WeddingPlan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingPlanByTimeRangeStatus", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*store.WeddingPlan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingPlanByTimeRangeStatus indicates an expected call of GetWeddingPlanByTimeRangeStatus.
func (mr *MockIStoreMockRecorder) GetWeddingPlanByTimeRangeStatus(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingPlanByTimeRangeStatus", reflect.TypeOf((*MockIStore)(nil).GetWeddingPlanByTimeRangeStatus), arg0, arg1, arg2, arg3, arg4)
}

// GetWeddingReserveIM mocks base method.
func (m *MockIStore) GetWeddingReserveIM(arg0 context.Context, arg1 uint32) (*store.WeddingReserveIM, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingReserveIM", arg0, arg1)
	ret0, _ := ret[0].(*store.WeddingReserveIM)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingReserveIM indicates an expected call of GetWeddingReserveIM.
func (mr *MockIStoreMockRecorder) GetWeddingReserveIM(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingReserveIM", reflect.TypeOf((*MockIStore)(nil).GetWeddingReserveIM), arg0, arg1)
}

// GetWeddingReserveIMByCreateTimeType mocks base method.
func (m *MockIStore) GetWeddingReserveIMByCreateTimeType(arg0 context.Context, arg1, arg2 uint32) ([]*store.WeddingReserveIM, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingReserveIMByCreateTimeType", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.WeddingReserveIM)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingReserveIMByCreateTimeType indicates an expected call of GetWeddingReserveIMByCreateTimeType.
func (mr *MockIStoreMockRecorder) GetWeddingReserveIMByCreateTimeType(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingReserveIMByCreateTimeType", reflect.TypeOf((*MockIStore)(nil).GetWeddingReserveIMByCreateTimeType), arg0, arg1, arg2)
}

// GetWeddingReserveIMByReserveTimeType mocks base method.
func (m *MockIStore) GetWeddingReserveIMByReserveTimeType(arg0 context.Context, arg1, arg2 uint32) ([]*store.WeddingReserveIM, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingReserveIMByReserveTimeType", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.WeddingReserveIM)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingReserveIMByReserveTimeType indicates an expected call of GetWeddingReserveIMByReserveTimeType.
func (mr *MockIStoreMockRecorder) GetWeddingReserveIMByReserveTimeType(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingReserveIMByReserveTimeType", reflect.TypeOf((*MockIStore)(nil).GetWeddingReserveIMByReserveTimeType), arg0, arg1, arg2)
}

// GetWeddingSubscribeList mocks base method.
func (m *MockIStore) GetWeddingSubscribeList(arg0 context.Context, arg1 uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingSubscribeList", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingSubscribeList indicates an expected call of GetWeddingSubscribeList.
func (mr *MockIStoreMockRecorder) GetWeddingSubscribeList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingSubscribeList", reflect.TypeOf((*MockIStore)(nil).GetWeddingSubscribeList), arg0, arg1)
}

// IncCounter mocks base method.
func (m *MockIStore) IncCounter(arg0 context.Context, arg1 string) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncCounter", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// IncCounter indicates an expected call of IncCounter.
func (mr *MockIStoreMockRecorder) IncCounter(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncCounter", reflect.TypeOf((*MockIStore)(nil).IncCounter), arg0, arg1)
}

// InsertChannelBanTime mocks base method.
func (m *MockIStore) InsertChannelBanTime(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertChannelBanTime", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertChannelBanTime indicates an expected call of InsertChannelBanTime.
func (mr *MockIStoreMockRecorder) InsertChannelBanTime(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertChannelBanTime", reflect.TypeOf((*MockIStore)(nil).InsertChannelBanTime), arg0, arg1, arg2, arg3, arg4)
}

// InsertCloseChannelReserveTimeSectionConf mocks base method.
func (m *MockIStore) InsertCloseChannelReserveTimeSectionConf(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertCloseChannelReserveTimeSectionConf", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertCloseChannelReserveTimeSectionConf indicates an expected call of InsertCloseChannelReserveTimeSectionConf.
func (mr *MockIStoreMockRecorder) InsertCloseChannelReserveTimeSectionConf(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertCloseChannelReserveTimeSectionConf", reflect.TypeOf((*MockIStore)(nil).InsertCloseChannelReserveTimeSectionConf), arg0, arg1, arg2, arg3)
}

// InsertMarriageRelation mocks base method.
func (m *MockIStore) InsertMarriageRelation(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertMarriageRelation", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertMarriageRelation indicates an expected call of InsertMarriageRelation.
func (mr *MockIStoreMockRecorder) InsertMarriageRelation(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertMarriageRelation", reflect.TypeOf((*MockIStore)(nil).InsertMarriageRelation), arg0, arg1, arg2)
}

// InsertWeddingGuestInvite mocks base method.
func (m *MockIStore) InsertWeddingGuestInvite(arg0 context.Context, arg1 *store.WeddingGuestInvite) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertWeddingGuestInvite", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertWeddingGuestInvite indicates an expected call of InsertWeddingGuestInvite.
func (mr *MockIStoreMockRecorder) InsertWeddingGuestInvite(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertWeddingGuestInvite", reflect.TypeOf((*MockIStore)(nil).InsertWeddingGuestInvite), arg0, arg1)
}

// InsertWeddingOrder mocks base method.
func (m *MockIStore) InsertWeddingOrder(arg0 context.Context, arg1 *store.WeddingOrder) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertWeddingOrder", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertWeddingOrder indicates an expected call of InsertWeddingOrder.
func (mr *MockIStoreMockRecorder) InsertWeddingOrder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertWeddingOrder", reflect.TypeOf((*MockIStore)(nil).InsertWeddingOrder), arg0, arg1)
}

// InsertWeddingPlan mocks base method.
func (m *MockIStore) InsertWeddingPlan(arg0 context.Context, arg1 *store.WeddingPlan) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertWeddingPlan", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertWeddingPlan indicates an expected call of InsertWeddingPlan.
func (mr *MockIStoreMockRecorder) InsertWeddingPlan(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertWeddingPlan", reflect.TypeOf((*MockIStore)(nil).InsertWeddingPlan), arg0, arg1)
}

// InsertWeddingReserveIM mocks base method.
func (m *MockIStore) InsertWeddingReserveIM(arg0 context.Context, arg1 *store.WeddingReserveIM) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertWeddingReserveIM", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertWeddingReserveIM indicates an expected call of InsertWeddingReserveIM.
func (mr *MockIStoreMockRecorder) InsertWeddingReserveIM(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertWeddingReserveIM", reflect.TypeOf((*MockIStore)(nil).InsertWeddingReserveIM), arg0, arg1)
}

// InsertWeddingSubscribeRecord mocks base method.
func (m *MockIStore) InsertWeddingSubscribeRecord(arg0 context.Context, arg1 *store.WeddingSubscribeRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertWeddingSubscribeRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertWeddingSubscribeRecord indicates an expected call of InsertWeddingSubscribeRecord.
func (mr *MockIStoreMockRecorder) InsertWeddingSubscribeRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertWeddingSubscribeRecord", reflect.TypeOf((*MockIStore)(nil).InsertWeddingSubscribeRecord), arg0, arg1)
}

// MarkAdminCancel mocks base method.
func (m *MockIStore) MarkAdminCancel(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkAdminCancel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// MarkAdminCancel indicates an expected call of MarkAdminCancel.
func (mr *MockIStoreMockRecorder) MarkAdminCancel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkAdminCancel", reflect.TypeOf((*MockIStore)(nil).MarkAdminCancel), arg0, arg1)
}

// PageGetWeddingList mocks base method.
func (m *MockIStore) PageGetWeddingList(arg0 context.Context, arg1, arg2, arg3 int64) ([]*store.WeddingPlan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PageGetWeddingList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*store.WeddingPlan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PageGetWeddingList indicates an expected call of PageGetWeddingList.
func (mr *MockIStoreMockRecorder) PageGetWeddingList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PageGetWeddingList", reflect.TypeOf((*MockIStore)(nil).PageGetWeddingList), arg0, arg1, arg2, arg3)
}

// PageWeddingPlanByChannelIdReserveTime mocks base method.
func (m *MockIStore) PageWeddingPlanByChannelIdReserveTime(arg0 context.Context, arg1 []uint32, arg2, arg3, arg4 int, arg5, arg6 int64) ([]*store.WeddingPlan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PageWeddingPlanByChannelIdReserveTime", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].([]*store.WeddingPlan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PageWeddingPlanByChannelIdReserveTime indicates an expected call of PageWeddingPlanByChannelIdReserveTime.
func (mr *MockIStoreMockRecorder) PageWeddingPlanByChannelIdReserveTime(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PageWeddingPlanByChannelIdReserveTime", reflect.TypeOf((*MockIStore)(nil).PageWeddingPlanByChannelIdReserveTime), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// PushWeddingBridesmaid mocks base method.
func (m *MockIStore) PushWeddingBridesmaid(arg0 context.Context, arg1 uint32, arg2 *store.WeddingGuest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushWeddingBridesmaid", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushWeddingBridesmaid indicates an expected call of PushWeddingBridesmaid.
func (mr *MockIStoreMockRecorder) PushWeddingBridesmaid(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushWeddingBridesmaid", reflect.TypeOf((*MockIStore)(nil).PushWeddingBridesmaid), arg0, arg1, arg2)
}

// PushWeddingFriends mocks base method.
func (m *MockIStore) PushWeddingFriends(arg0 context.Context, arg1 uint32, arg2 *store.WeddingGuest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushWeddingFriends", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushWeddingFriends indicates an expected call of PushWeddingFriends.
func (mr *MockIStoreMockRecorder) PushWeddingFriends(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushWeddingFriends", reflect.TypeOf((*MockIStore)(nil).PushWeddingFriends), arg0, arg1, arg2)
}

// PushWeddingGroomsman mocks base method.
func (m *MockIStore) PushWeddingGroomsman(arg0 context.Context, arg1 uint32, arg2 *store.WeddingGuest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushWeddingGroomsman", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushWeddingGroomsman indicates an expected call of PushWeddingGroomsman.
func (mr *MockIStoreMockRecorder) PushWeddingGroomsman(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushWeddingGroomsman", reflect.TypeOf((*MockIStore)(nil).PushWeddingGroomsman), arg0, arg1, arg2)
}

// SetWeddingGuestInvitePlanFinish mocks base method.
func (m *MockIStore) SetWeddingGuestInvitePlanFinish(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWeddingGuestInvitePlanFinish", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWeddingGuestInvitePlanFinish indicates an expected call of SetWeddingGuestInvitePlanFinish.
func (mr *MockIStoreMockRecorder) SetWeddingGuestInvitePlanFinish(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWeddingGuestInvitePlanFinish", reflect.TypeOf((*MockIStore)(nil).SetWeddingGuestInvitePlanFinish), arg0, arg1)
}

// UpdateHolePlanInviteStatus mocks base method.
func (m *MockIStore) UpdateHolePlanInviteStatus(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateHolePlanInviteStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateHolePlanInviteStatus indicates an expected call of UpdateHolePlanInviteStatus.
func (mr *MockIStoreMockRecorder) UpdateHolePlanInviteStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateHolePlanInviteStatus", reflect.TypeOf((*MockIStore)(nil).UpdateHolePlanInviteStatus), arg0, arg1, arg2)
}

// UpdateOrder2FreezeAndInitPlan mocks base method.
func (m *MockIStore) UpdateOrder2FreezeAndInitPlan(arg0 context.Context, arg1 time.Time, arg2 *store.WeddingOrder, arg3 *store.ReserveInfo, arg4 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOrder2FreezeAndInitPlan", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOrder2FreezeAndInitPlan indicates an expected call of UpdateOrder2FreezeAndInitPlan.
func (mr *MockIStoreMockRecorder) UpdateOrder2FreezeAndInitPlan(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrder2FreezeAndInitPlan", reflect.TypeOf((*MockIStore)(nil).UpdateOrder2FreezeAndInitPlan), arg0, arg1, arg2, arg3, arg4)
}

// UpdateOrderStatus mocks base method.
func (m *MockIStore) UpdateOrderStatus(arg0 context.Context, arg1 uint32, arg2 []uint32, arg3 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOrderStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateOrderStatus indicates an expected call of UpdateOrderStatus.
func (mr *MockIStoreMockRecorder) UpdateOrderStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrderStatus", reflect.TypeOf((*MockIStore)(nil).UpdateOrderStatus), arg0, arg1, arg2, arg3)
}

// UpdateProposeInfoStatusByID mocks base method.
func (m *MockIStore) UpdateProposeInfoStatusByID(arg0 context.Context, arg1 string, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateProposeInfoStatusByID", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateProposeInfoStatusByID indicates an expected call of UpdateProposeInfoStatusByID.
func (mr *MockIStoreMockRecorder) UpdateProposeInfoStatusByID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateProposeInfoStatusByID", reflect.TypeOf((*MockIStore)(nil).UpdateProposeInfoStatusByID), arg0, arg1, arg2)
}

// UpdateThemeCfg mocks base method.
func (m *MockIStore) UpdateThemeCfg(arg0 context.Context, arg1 *channel_wedding_plan.ThemeCfg) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateThemeCfg", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateThemeCfg indicates an expected call of UpdateThemeCfg.
func (mr *MockIStoreMockRecorder) UpdateThemeCfg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateThemeCfg", reflect.TypeOf((*MockIStore)(nil).UpdateThemeCfg), arg0, arg1)
}

// UpdateWeddingBigScreenReviewStatus mocks base method.
func (m *MockIStore) UpdateWeddingBigScreenReviewStatus(arg0 context.Context, arg1, arg2 uint32, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWeddingBigScreenReviewStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWeddingBigScreenReviewStatus indicates an expected call of UpdateWeddingBigScreenReviewStatus.
func (mr *MockIStoreMockRecorder) UpdateWeddingBigScreenReviewStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWeddingBigScreenReviewStatus", reflect.TypeOf((*MockIStore)(nil).UpdateWeddingBigScreenReviewStatus), arg0, arg1, arg2, arg3)
}

// UpdateWeddingBridesmaid mocks base method.
func (m *MockIStore) UpdateWeddingBridesmaid(arg0 context.Context, arg1 uint32, arg2 []*store.WeddingGuest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWeddingBridesmaid", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWeddingBridesmaid indicates an expected call of UpdateWeddingBridesmaid.
func (mr *MockIStoreMockRecorder) UpdateWeddingBridesmaid(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWeddingBridesmaid", reflect.TypeOf((*MockIStore)(nil).UpdateWeddingBridesmaid), arg0, arg1, arg2)
}

// UpdateWeddingFriends mocks base method.
func (m *MockIStore) UpdateWeddingFriends(arg0 context.Context, arg1 uint32, arg2 []*store.WeddingGuest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWeddingFriends", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWeddingFriends indicates an expected call of UpdateWeddingFriends.
func (mr *MockIStoreMockRecorder) UpdateWeddingFriends(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWeddingFriends", reflect.TypeOf((*MockIStore)(nil).UpdateWeddingFriends), arg0, arg1, arg2)
}

// UpdateWeddingGroomsman mocks base method.
func (m *MockIStore) UpdateWeddingGroomsman(arg0 context.Context, arg1 uint32, arg2 []*store.WeddingGuest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWeddingGroomsman", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWeddingGroomsman indicates an expected call of UpdateWeddingGroomsman.
func (mr *MockIStoreMockRecorder) UpdateWeddingGroomsman(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWeddingGroomsman", reflect.TypeOf((*MockIStore)(nil).UpdateWeddingGroomsman), arg0, arg1, arg2)
}

// UpdateWeddingGuestInviteStatus mocks base method.
func (m *MockIStore) UpdateWeddingGuestInviteStatus(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWeddingGuestInviteStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWeddingGuestInviteStatus indicates an expected call of UpdateWeddingGuestInviteStatus.
func (mr *MockIStoreMockRecorder) UpdateWeddingGuestInviteStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWeddingGuestInviteStatus", reflect.TypeOf((*MockIStore)(nil).UpdateWeddingGuestInviteStatus), arg0, arg1, arg2, arg3)
}

// UpdateWeddingGuestInviteStatusByWeddingPlanIdGuestTypeUid mocks base method.
func (m *MockIStore) UpdateWeddingGuestInviteStatusByWeddingPlanIdGuestTypeUid(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWeddingGuestInviteStatusByWeddingPlanIdGuestTypeUid", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWeddingGuestInviteStatusByWeddingPlanIdGuestTypeUid indicates an expected call of UpdateWeddingGuestInviteStatusByWeddingPlanIdGuestTypeUid.
func (mr *MockIStoreMockRecorder) UpdateWeddingGuestInviteStatusByWeddingPlanIdGuestTypeUid(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWeddingGuestInviteStatusByWeddingPlanIdGuestTypeUid", reflect.TypeOf((*MockIStore)(nil).UpdateWeddingGuestInviteStatusByWeddingPlanIdGuestTypeUid), arg0, arg1, arg2, arg3, arg4)
}

// UpdateWeddingPlanHost mocks base method.
func (m *MockIStore) UpdateWeddingPlanHost(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWeddingPlanHost", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWeddingPlanHost indicates an expected call of UpdateWeddingPlanHost.
func (mr *MockIStoreMockRecorder) UpdateWeddingPlanHost(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWeddingPlanHost", reflect.TypeOf((*MockIStore)(nil).UpdateWeddingPlanHost), arg0, arg1, arg2)
}

// UpdateWeddingPlanReserveInfo mocks base method.
func (m *MockIStore) UpdateWeddingPlanReserveInfo(arg0 context.Context, arg1 uint32, arg2 *store.ReserveInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWeddingPlanReserveInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWeddingPlanReserveInfo indicates an expected call of UpdateWeddingPlanReserveInfo.
func (mr *MockIStoreMockRecorder) UpdateWeddingPlanReserveInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWeddingPlanReserveInfo", reflect.TypeOf((*MockIStore)(nil).UpdateWeddingPlanReserveInfo), arg0, arg1, arg2)
}

// UpdateWeddingPlanStatusStrict mocks base method.
func (m *MockIStore) UpdateWeddingPlanStatusStrict(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWeddingPlanStatusStrict", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWeddingPlanStatusStrict indicates an expected call of UpdateWeddingPlanStatusStrict.
func (mr *MockIStoreMockRecorder) UpdateWeddingPlanStatusStrict(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWeddingPlanStatusStrict", reflect.TypeOf((*MockIStore)(nil).UpdateWeddingPlanStatusStrict), arg0, arg1, arg2, arg3)
}

// UpdateWeddingReserveIMStatus mocks base method.
func (m *MockIStore) UpdateWeddingReserveIMStatus(arg0 context.Context, arg1 []uint32, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWeddingReserveIMStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWeddingReserveIMStatus indicates an expected call of UpdateWeddingReserveIMStatus.
func (mr *MockIStoreMockRecorder) UpdateWeddingReserveIMStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWeddingReserveIMStatus", reflect.TypeOf((*MockIStore)(nil).UpdateWeddingReserveIMStatus), arg0, arg1, arg2)
}

// UpdateWeddingReserveInfo mocks base method.
func (m *MockIStore) UpdateWeddingReserveInfo(arg0 context.Context, arg1 uint32, arg2 *store.ReserveInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWeddingReserveInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWeddingReserveInfo indicates an expected call of UpdateWeddingReserveInfo.
func (mr *MockIStoreMockRecorder) UpdateWeddingReserveInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWeddingReserveInfo", reflect.TypeOf((*MockIStore)(nil).UpdateWeddingReserveInfo), arg0, arg1, arg2)
}

// UpsertRelationHideSwitch mocks base method.
func (m *MockIStore) UpsertRelationHideSwitch(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertRelationHideSwitch", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertRelationHideSwitch indicates an expected call of UpsertRelationHideSwitch.
func (mr *MockIStoreMockRecorder) UpsertRelationHideSwitch(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertRelationHideSwitch", reflect.TypeOf((*MockIStore)(nil).UpsertRelationHideSwitch), arg0, arg1, arg2)
}

// WeddingPlanEnsureIndex mocks base method.
func (m *MockIStore) WeddingPlanEnsureIndex(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WeddingPlanEnsureIndex", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// WeddingPlanEnsureIndex indicates an expected call of WeddingPlanEnsureIndex.
func (mr *MockIStoreMockRecorder) WeddingPlanEnsureIndex(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WeddingPlanEnsureIndex", reflect.TypeOf((*MockIStore)(nil).WeddingPlanEnsureIndex), arg0)
}
