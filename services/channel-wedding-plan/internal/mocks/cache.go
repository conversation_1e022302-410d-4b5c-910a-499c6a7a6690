// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-wedding-plan/internal/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	redis "github.com/go-redis/redis/v8"
	gomock "github.com/golang/mock/gomock"
	cache "golang.52tt.com/services/channel-wedding-plan/internal/cache"
	store "golang.52tt.com/services/channel-wedding-plan/internal/store"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// AddDivideTimeout mocks base method.
func (m *MockICache) AddDivideTimeout(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddDivideTimeout", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddDivideTimeout indicates an expected call of AddDivideTimeout.
func (mr *MockICacheMockRecorder) AddDivideTimeout(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddDivideTimeout", reflect.TypeOf((*MockICache)(nil).AddDivideTimeout), arg0, arg1)
}

// AddPreProgressGiftValue mocks base method.
func (m *MockICache) AddPreProgressGiftValue(arg0 context.Context, arg1, arg2, arg3 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPreProgressGiftValue", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPreProgressGiftValue indicates an expected call of AddPreProgressGiftValue.
func (mr *MockICacheMockRecorder) AddPreProgressGiftValue(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPreProgressGiftValue", reflect.TypeOf((*MockICache)(nil).AddPreProgressGiftValue), arg0, arg1, arg2, arg3)
}

// BatSetWeddingAnniversaryPopupInfo mocks base method.
func (m *MockICache) BatSetWeddingAnniversaryPopupInfo(arg0 context.Context, arg1 int, arg2 time.Time, arg3 []*cache.AnniversaryCp) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatSetWeddingAnniversaryPopupInfo", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatSetWeddingAnniversaryPopupInfo indicates an expected call of BatSetWeddingAnniversaryPopupInfo.
func (mr *MockICacheMockRecorder) BatSetWeddingAnniversaryPopupInfo(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatSetWeddingAnniversaryPopupInfo", reflect.TypeOf((*MockICache)(nil).BatSetWeddingAnniversaryPopupInfo), arg0, arg1, arg2, arg3)
}

// BatchDelPreProgressCacheWithUid mocks base method.
func (m *MockICache) BatchDelPreProgressCacheWithUid(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDelPreProgressCacheWithUid", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDelPreProgressCacheWithUid indicates an expected call of BatchDelPreProgressCacheWithUid.
func (mr *MockICacheMockRecorder) BatchDelPreProgressCacheWithUid(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelPreProgressCacheWithUid", reflect.TypeOf((*MockICache)(nil).BatchDelPreProgressCacheWithUid), arg0, arg1, arg2, arg3)
}

// BatchDelPreProgressGiftValue mocks base method.
func (m *MockICache) BatchDelPreProgressGiftValue(arg0 context.Context, arg1 uint32, arg2 ...uint32) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchDelPreProgressGiftValue", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDelPreProgressGiftValue indicates an expected call of BatchDelPreProgressGiftValue.
func (mr *MockICacheMockRecorder) BatchDelPreProgressGiftValue(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelPreProgressGiftValue", reflect.TypeOf((*MockICache)(nil).BatchDelPreProgressGiftValue), varargs...)
}

// BatchGetUserDivorceTimeout mocks base method.
func (m *MockICache) BatchGetUserDivorceTimeout(arg0 context.Context, arg1 []uint32) (map[uint32]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserDivorceTimeout", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserDivorceTimeout indicates an expected call of BatchGetUserDivorceTimeout.
func (mr *MockICacheMockRecorder) BatchGetUserDivorceTimeout(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserDivorceTimeout", reflect.TypeOf((*MockICache)(nil).BatchGetUserDivorceTimeout), arg0, arg1)
}

// CheckNextIfAnniversaryPopupInfoExist mocks base method.
func (m *MockICache) CheckNextIfAnniversaryPopupInfoExist(arg0 context.Context, arg1 int, arg2 time.Time) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckNextIfAnniversaryPopupInfoExist", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckNextIfAnniversaryPopupInfoExist indicates an expected call of CheckNextIfAnniversaryPopupInfoExist.
func (mr *MockICacheMockRecorder) CheckNextIfAnniversaryPopupInfoExist(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckNextIfAnniversaryPopupInfoExist", reflect.TypeOf((*MockICache)(nil).CheckNextIfAnniversaryPopupInfoExist), arg0, arg1, arg2)
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// DelAnniversaryPopupInfoByUid mocks base method.
func (m *MockICache) DelAnniversaryPopupInfoByUid(arg0 context.Context, arg1 uint32, arg2 int, arg3 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelAnniversaryPopupInfoByUid", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelAnniversaryPopupInfoByUid indicates an expected call of DelAnniversaryPopupInfoByUid.
func (mr *MockICacheMockRecorder) DelAnniversaryPopupInfoByUid(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelAnniversaryPopupInfoByUid", reflect.TypeOf((*MockICache)(nil).DelAnniversaryPopupInfoByUid), arg0, arg1, arg2, arg3)
}

// DelNewcomersMic mocks base method.
func (m *MockICache) DelNewcomersMic(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelNewcomersMic", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelNewcomersMic indicates an expected call of DelNewcomersMic.
func (mr *MockICacheMockRecorder) DelNewcomersMic(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelNewcomersMic", reflect.TypeOf((*MockICache)(nil).DelNewcomersMic), arg0, arg1, arg2)
}

// DelPreProgressStage mocks base method.
func (m *MockICache) DelPreProgressStage(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPreProgressStage", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelPreProgressStage indicates an expected call of DelPreProgressStage.
func (mr *MockICacheMockRecorder) DelPreProgressStage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPreProgressStage", reflect.TypeOf((*MockICache)(nil).DelPreProgressStage), arg0, arg1)
}

// DelRelationHideSwitch mocks base method.
func (m *MockICache) DelRelationHideSwitch(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelRelationHideSwitch", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelRelationHideSwitch indicates an expected call of DelRelationHideSwitch.
func (mr *MockICacheMockRecorder) DelRelationHideSwitch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelRelationHideSwitch", reflect.TypeOf((*MockICache)(nil).DelRelationHideSwitch), arg0, arg1)
}

// DelUserConsultReserveTimeSection mocks base method.
func (m *MockICache) DelUserConsultReserveTimeSection(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserConsultReserveTimeSection", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserConsultReserveTimeSection indicates an expected call of DelUserConsultReserveTimeSection.
func (mr *MockICacheMockRecorder) DelUserConsultReserveTimeSection(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserConsultReserveTimeSection", reflect.TypeOf((*MockICache)(nil).DelUserConsultReserveTimeSection), arg0, arg1, arg2, arg3, arg4)
}

// DelWeddingAnniversaryPopupFlag mocks base method.
func (m *MockICache) DelWeddingAnniversaryPopupFlag(arg0 context.Context, arg1 uint32, arg2 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelWeddingAnniversaryPopupFlag", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelWeddingAnniversaryPopupFlag indicates an expected call of DelWeddingAnniversaryPopupFlag.
func (mr *MockICacheMockRecorder) DelWeddingAnniversaryPopupFlag(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelWeddingAnniversaryPopupFlag", reflect.TypeOf((*MockICache)(nil).DelWeddingAnniversaryPopupFlag), arg0, arg1, arg2)
}

// DelWeddingPlan mocks base method.
func (m *MockICache) DelWeddingPlan(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelWeddingPlan", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelWeddingPlan indicates an expected call of DelWeddingPlan.
func (mr *MockICacheMockRecorder) DelWeddingPlan(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelWeddingPlan", reflect.TypeOf((*MockICache)(nil).DelWeddingPlan), arg0, arg1)
}

// DeletePreProgressWeddingId mocks base method.
func (m *MockICache) DeletePreProgressWeddingId(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePreProgressWeddingId", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePreProgressWeddingId indicates an expected call of DeletePreProgressWeddingId.
func (mr *MockICacheMockRecorder) DeletePreProgressWeddingId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePreProgressWeddingId", reflect.TypeOf((*MockICache)(nil).DeletePreProgressWeddingId), arg0, arg1)
}

// GetAllAnniversaryPopupInfo mocks base method.
func (m *MockICache) GetAllAnniversaryPopupInfo(arg0 context.Context, arg1 int, arg2 time.Time) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllAnniversaryPopupInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllAnniversaryPopupInfo indicates an expected call of GetAllAnniversaryPopupInfo.
func (mr *MockICacheMockRecorder) GetAllAnniversaryPopupInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllAnniversaryPopupInfo", reflect.TypeOf((*MockICache)(nil).GetAllAnniversaryPopupInfo), arg0, arg1, arg2)
}

// GetAnniversaryPopupByUid mocks base method.
func (m *MockICache) GetAnniversaryPopupByUid(arg0 context.Context, arg1 uint32, arg2 int, arg3 time.Time) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnniversaryPopupByUid", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnniversaryPopupByUid indicates an expected call of GetAnniversaryPopupByUid.
func (mr *MockICacheMockRecorder) GetAnniversaryPopupByUid(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnniversaryPopupByUid", reflect.TypeOf((*MockICache)(nil).GetAnniversaryPopupByUid), arg0, arg1, arg2, arg3)
}

// GetDivideTimeout mocks base method.
func (m *MockICache) GetDivideTimeout(arg0 context.Context, arg1 int64) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDivideTimeout", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDivideTimeout indicates an expected call of GetDivideTimeout.
func (mr *MockICacheMockRecorder) GetDivideTimeout(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDivideTimeout", reflect.TypeOf((*MockICache)(nil).GetDivideTimeout), arg0, arg1)
}

// GetMyDivideTimeout mocks base method.
func (m *MockICache) GetMyDivideTimeout(arg0 context.Context, arg1 uint32) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyDivideTimeout", arg0, arg1)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyDivideTimeout indicates an expected call of GetMyDivideTimeout.
func (mr *MockICacheMockRecorder) GetMyDivideTimeout(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyDivideTimeout", reflect.TypeOf((*MockICache)(nil).GetMyDivideTimeout), arg0, arg1)
}

// GetNewcomersMic mocks base method.
func (m *MockICache) GetNewcomersMic(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewcomersMic", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewcomersMic indicates an expected call of GetNewcomersMic.
func (mr *MockICacheMockRecorder) GetNewcomersMic(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewcomersMic", reflect.TypeOf((*MockICache)(nil).GetNewcomersMic), arg0, arg1, arg2)
}

// GetPreProgressGiftValue mocks base method.
func (m *MockICache) GetPreProgressGiftValue(arg0 context.Context, arg1, arg2, arg3 uint32) (uint32, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPreProgressGiftValue", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetPreProgressGiftValue indicates an expected call of GetPreProgressGiftValue.
func (mr *MockICacheMockRecorder) GetPreProgressGiftValue(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPreProgressGiftValue", reflect.TypeOf((*MockICache)(nil).GetPreProgressGiftValue), arg0, arg1, arg2, arg3)
}

// GetPreProgressStage mocks base method.
func (m *MockICache) GetPreProgressStage(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPreProgressStage", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPreProgressStage indicates an expected call of GetPreProgressStage.
func (mr *MockICacheMockRecorder) GetPreProgressStage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPreProgressStage", reflect.TypeOf((*MockICache)(nil).GetPreProgressStage), arg0, arg1)
}

// GetPreProgressWeddingId mocks base method.
func (m *MockICache) GetPreProgressWeddingId(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPreProgressWeddingId", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPreProgressWeddingId indicates an expected call of GetPreProgressWeddingId.
func (mr *MockICacheMockRecorder) GetPreProgressWeddingId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPreProgressWeddingId", reflect.TypeOf((*MockICache)(nil).GetPreProgressWeddingId), arg0, arg1)
}

// GetRedisClient mocks base method.
func (m *MockICache) GetRedisClient() redis.Cmdable {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisClient")
	ret0, _ := ret[0].(redis.Cmdable)
	return ret0
}

// GetRedisClient indicates an expected call of GetRedisClient.
func (mr *MockICacheMockRecorder) GetRedisClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisClient", reflect.TypeOf((*MockICache)(nil).GetRedisClient))
}

// GetRelationHideSwitch mocks base method.
func (m *MockICache) GetRelationHideSwitch(arg0 context.Context, arg1 uint32) (bool, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRelationHideSwitch", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetRelationHideSwitch indicates an expected call of GetRelationHideSwitch.
func (mr *MockICacheMockRecorder) GetRelationHideSwitch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationHideSwitch", reflect.TypeOf((*MockICache)(nil).GetRelationHideSwitch), arg0, arg1)
}

// GetUserConsultReserveManagerUid mocks base method.
func (m *MockICache) GetUserConsultReserveManagerUid(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserConsultReserveManagerUid", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserConsultReserveManagerUid indicates an expected call of GetUserConsultReserveManagerUid.
func (mr *MockICacheMockRecorder) GetUserConsultReserveManagerUid(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserConsultReserveManagerUid", reflect.TypeOf((*MockICache)(nil).GetUserConsultReserveManagerUid), arg0, arg1, arg2)
}

// GetUserConsultReserveTimeSection mocks base method.
func (m *MockICache) GetUserConsultReserveTimeSection(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserConsultReserveTimeSection", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserConsultReserveTimeSection indicates an expected call of GetUserConsultReserveTimeSection.
func (mr *MockICacheMockRecorder) GetUserConsultReserveTimeSection(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserConsultReserveTimeSection", reflect.TypeOf((*MockICache)(nil).GetUserConsultReserveTimeSection), arg0, arg1, arg2, arg3, arg4)
}

// GetWeddingPlan mocks base method.
func (m *MockICache) GetWeddingPlan(arg0 context.Context, arg1 uint32) (*store.WeddingPlan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingPlan", arg0, arg1)
	ret0, _ := ret[0].(*store.WeddingPlan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingPlan indicates an expected call of GetWeddingPlan.
func (mr *MockICacheMockRecorder) GetWeddingPlan(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingPlan", reflect.TypeOf((*MockICache)(nil).GetWeddingPlan), arg0, arg1)
}

// RemoveDivideTimeout mocks base method.
func (m *MockICache) RemoveDivideTimeout(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveDivideTimeout", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveDivideTimeout indicates an expected call of RemoveDivideTimeout.
func (mr *MockICacheMockRecorder) RemoveDivideTimeout(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveDivideTimeout", reflect.TypeOf((*MockICache)(nil).RemoveDivideTimeout), arg0, arg1)
}

// SetFirstOnMic mocks base method.
func (m *MockICache) SetFirstOnMic(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetFirstOnMic", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetFirstOnMic indicates an expected call of SetFirstOnMic.
func (mr *MockICacheMockRecorder) SetFirstOnMic(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetFirstOnMic", reflect.TypeOf((*MockICache)(nil).SetFirstOnMic), arg0, arg1, arg2)
}

// SetNewcomersMic mocks base method.
func (m *MockICache) SetNewcomersMic(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetNewcomersMic", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetNewcomersMic indicates an expected call of SetNewcomersMic.
func (mr *MockICacheMockRecorder) SetNewcomersMic(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNewcomersMic", reflect.TypeOf((*MockICache)(nil).SetNewcomersMic), arg0, arg1, arg2, arg3)
}

// SetPreProgressStage mocks base method.
func (m *MockICache) SetPreProgressStage(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPreProgressStage", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPreProgressStage indicates an expected call of SetPreProgressStage.
func (mr *MockICacheMockRecorder) SetPreProgressStage(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPreProgressStage", reflect.TypeOf((*MockICache)(nil).SetPreProgressStage), arg0, arg1, arg2)
}

// SetPreProgressWeddingId mocks base method.
func (m *MockICache) SetPreProgressWeddingId(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPreProgressWeddingId", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPreProgressWeddingId indicates an expected call of SetPreProgressWeddingId.
func (mr *MockICacheMockRecorder) SetPreProgressWeddingId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPreProgressWeddingId", reflect.TypeOf((*MockICache)(nil).SetPreProgressWeddingId), arg0, arg1, arg2)
}

// SetRelationHideSwitch mocks base method.
func (m *MockICache) SetRelationHideSwitch(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRelationHideSwitch", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetRelationHideSwitch indicates an expected call of SetRelationHideSwitch.
func (mr *MockICacheMockRecorder) SetRelationHideSwitch(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRelationHideSwitch", reflect.TypeOf((*MockICache)(nil).SetRelationHideSwitch), arg0, arg1, arg2)
}

// SetUserConsultReserveManagerUid mocks base method.
func (m *MockICache) SetUserConsultReserveManagerUid(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserConsultReserveManagerUid", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserConsultReserveManagerUid indicates an expected call of SetUserConsultReserveManagerUid.
func (mr *MockICacheMockRecorder) SetUserConsultReserveManagerUid(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserConsultReserveManagerUid", reflect.TypeOf((*MockICache)(nil).SetUserConsultReserveManagerUid), arg0, arg1, arg2, arg3, arg4)
}

// SetUserConsultReserveTimeSection mocks base method.
func (m *MockICache) SetUserConsultReserveTimeSection(arg0 context.Context, arg1, arg2, arg3, arg4, arg5 uint32, arg6 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserConsultReserveTimeSection", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserConsultReserveTimeSection indicates an expected call of SetUserConsultReserveTimeSection.
func (mr *MockICacheMockRecorder) SetUserConsultReserveTimeSection(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserConsultReserveTimeSection", reflect.TypeOf((*MockICache)(nil).SetUserConsultReserveTimeSection), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// SetWeddingAnniversaryPopupFlag mocks base method.
func (m *MockICache) SetWeddingAnniversaryPopupFlag(arg0 context.Context, arg1 uint32, arg2 time.Time) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWeddingAnniversaryPopupFlag", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetWeddingAnniversaryPopupFlag indicates an expected call of SetWeddingAnniversaryPopupFlag.
func (mr *MockICacheMockRecorder) SetWeddingAnniversaryPopupFlag(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWeddingAnniversaryPopupFlag", reflect.TypeOf((*MockICache)(nil).SetWeddingAnniversaryPopupFlag), arg0, arg1, arg2)
}

// SetWeddingPlan mocks base method.
func (m *MockICache) SetWeddingPlan(arg0 context.Context, arg1 uint32, arg2 *store.WeddingPlan) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWeddingPlan", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWeddingPlan indicates an expected call of SetWeddingPlan.
func (mr *MockICacheMockRecorder) SetWeddingPlan(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWeddingPlan", reflect.TypeOf((*MockICache)(nil).SetWeddingPlan), arg0, arg1, arg2)
}

// TryLock mocks base method.
func (m *MockICache) TryLock(arg0 context.Context, arg1, arg2 string, arg3 ...time.Duration) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TryLock", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// TryLock indicates an expected call of TryLock.
func (mr *MockICacheMockRecorder) TryLock(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TryLock", reflect.TypeOf((*MockICache)(nil).TryLock), varargs...)
}

// TtlFirstOnMic mocks base method.
func (m *MockICache) TtlFirstOnMic(arg0 context.Context, arg1, arg2 uint32) (time.Duration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TtlFirstOnMic", arg0, arg1, arg2)
	ret0, _ := ret[0].(time.Duration)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TtlFirstOnMic indicates an expected call of TtlFirstOnMic.
func (mr *MockICacheMockRecorder) TtlFirstOnMic(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TtlFirstOnMic", reflect.TypeOf((*MockICache)(nil).TtlFirstOnMic), arg0, arg1, arg2)
}

// Unlock mocks base method.
func (m *MockICache) Unlock(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Unlock", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Unlock indicates an expected call of Unlock.
func (mr *MockICacheMockRecorder) Unlock(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Unlock", reflect.TypeOf((*MockICache)(nil).Unlock), arg0, arg1, arg2)
}
