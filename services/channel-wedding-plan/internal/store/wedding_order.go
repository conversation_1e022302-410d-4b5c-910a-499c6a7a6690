package store

import (
    "context"
    "errors"
    tyrMongo "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mongo"
    "go.mongodb.org/mongo-driver/bson"
    "go.mongodb.org/mongo-driver/mongo"
    "go.mongodb.org/mongo-driver/mongo/options"
    "go.mongodb.org/mongo-driver/mongo/readconcern"
    "go.mongodb.org/mongo-driver/mongo/writeconcern"
    "golang.52tt.com/pkg/log"
    "time"
)

const (
    WeddingStatusInit     = iota
    WeddingStatusFreezing // wedding 已冻结
    WeddingStatusCancel   // wedding 提前取消（退款）
    WeddingStatusReserved // wedding 已预约（如果超时未举办，不退款）
    WeddingStatusConfirm  // 已确认
    WeddingStatusRollback // 已回滚
)

type WeddingOrder struct {
    Id               uint32 `bson:"_id"`                // wedding id
    OrderId          string `bson:"order_id"`           // 订单号
    GroomUid         uint32 `bson:"groom_uid"`          //  新郎 uid
    BrideUid         uint32 `bson:"bride_uid"`          // 新娘 uid
    ThemeId          uint32 `bson:"theme_id"`           // 主题 id
    BuyerUid         uint32 `bson:"buyer_uid"`          // 买家 uid
    WeddingStatus    uint32 `bson:"wedding_status"`     // 订单 状态
    WeddingPrice     uint32 `bson:"wedding_price"`      // 订单 价格
    WeddingPriceType uint32 `bson:"wedding_price_type"` // 价格类型, 1: 红钻 2: T豆
    WeddingGiftId    uint32 `bson:"wedding_gift_id"`    // 婚礼礼物id
    SourceMsgId      uint32 `bson:"source_msg_id"`      // IM来的单
    TBeanSysTime     int64  `bson:"t_bean_sys_time"`    // t豆系统时间 t_bean_sys_time
    CreateTs         int64  `bson:"create_ts"`          // 创建 wedding 的时间戳
    UpdateTs         int64  `bson:"update_ts"`          // 更新 wedding 的时间戳
    DealToken        string `bson:"deal_token"`         // deal_token
}

func (s *Store) ensureIndex4WeddingOrder(ctx context.Context) {
    err := s.weddingOrderCollection.CreateIndexes(ctx, []mongo.IndexModel{
        {
            Keys: bson.D{
                {Key: "order_id", Value: 1},
                {Key: "t_bean_sys_time", Value: 1},
            },
            Options: options.Index().SetUnique(true),
        },
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "ensureIndex4WeddingSubscribeRecord error: %v", err)
    }
    err = s.weddingOrderCollection.CreateIndexes(ctx, []mongo.IndexModel{
        {
            Keys: bson.D{
                {Key: "t_bean_sys_time", Value: 1},
            },
        },
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "ensureIndex4WeddingSubscribeRecord error: %v", err)
    }
}

func (s *Store) GenWeddingOrderId(ctx context.Context) uint32 {
    id := s.IncCounter(ctx, bizNameWeddingOrder)
    if id == 0 {
        log.ErrorWithCtx(ctx, "GenWeddingOrderId failed to IncCounter")
        return 0
    }
    return id
}

func (s *Store) InsertWeddingOrder(ctx context.Context, record *WeddingOrder) error {
    id := s.IncCounter(ctx, bizNameWeddingOrder)
    if id == 0 {
        log.ErrorWithCtx(ctx, "InsertWeddingRecord failed to IncCounter, record:%+v", record)
        return errors.New("insert wedding record error")
    }
    record.Id = id

    _, err := s.weddingOrderCollection.InsertOne(ctx, record)
    if err != nil {
        log.ErrorWithCtx(ctx, "InsertWeddingOrder error: %v, record:%+v", err, record)
        return err
    }
    log.InfoWithCtx(ctx, "InsertWeddingOrder success, record: %+v", record)
    return nil
}

func (s *Store) UpdateOrder2FreezeAndInitPlan(ctx context.Context, outsideTime time.Time, record *WeddingOrder, reserveInfo *ReserveInfo, isHot bool) error {
    // 创建会话
    session, err := s.client.StartSession()
    if err != nil {
        log.ErrorWithCtx(ctx, "StartSession error: %v", err)
        return err
    }
    defer session.EndSession(ctx)

    // 执行事务操作
    _, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
        _, err := s.ChangeConsumeRecordPayInfo(sessCtx, outsideTime, record.BuyerUid, []uint32{WeddingStatusInit}, WeddingStatusFreezing, record.OrderId, "", "")
        if err != nil {
            log.ErrorWithCtx(sessCtx, "UpdateOrder2FreezeAndInitPlan ChangeConsumeRecordPayInfo error: %v, record:%+v", err, record)
            return nil, err
        }

        log.InfoWithCtx(sessCtx, "UpdateOrder2FreezeAndInitPlan ChangeConsumeRecordPayInfo success, record: %+v, outsideTime:%v", record, outsideTime)

        now := time.Now().Unix()
        err = s.InsertWeddingPlan(sessCtx, &WeddingPlan{
            ID:            record.Id,
            GroomUid:      record.GroomUid,
            BrideUid:      record.BrideUid,
            ThemeId:       record.ThemeId,
            ThemeType:     record.WeddingPriceType,
            ReserveInfo:   reserveInfo,
            CreateTime:    uint32(now),
            UpdateTime:    uint32(now),
            Status:        WeddingPlanStatusInit,
            BigScreenList: make([]*BigScreenItem, 0),
            BuyerUid:      record.BuyerUid,
            IsHot:         isHot,
        })
        if err != nil {
            log.ErrorWithCtx(sessCtx, "UpdateOrder2FreezeAndInitPlan InsertWeddingPlan error: %v, record:%+v", err, record)
            return nil, err
        }
        log.InfoWithCtx(sessCtx, "UpdateOrder2FreezeAndInitPlan InsertWeddingPlan success, record: %+v, outsideTime:%v", record, outsideTime)
        return nil, nil
    }, options.Transaction().
        SetReadConcern(readconcern.Majority()).
        SetWriteConcern(writeconcern.New(writeconcern.WMajority())))

    if err != nil {
        log.ErrorWithCtx(ctx, "Transaction error: %v", err)
        return err
    }

    log.InfoWithCtx(ctx, "Transaction committed successfully, record: %+v", record)
    return nil
}

func (s *Store) ChangeConsumeRecordPayInfo(ctx context.Context, outsideTime time.Time, uid uint32, oldStatus []uint32, newStatus uint32, orderId, dealToken, tBeanTimeStr string) (bool, error) {
    filter := bson.D{
        {Key: "wedding_status", Value: bson.D{{Key: "$in", Value: oldStatus}}},
        {Key: "order_id", Value: orderId},
    }

    value := bson.D{
        {Key: "wedding_status", Value: newStatus},
        {Key: "update_ts", Value: outsideTime.Unix()},
    }
    if dealToken != "" {
        value = append(value, bson.E{Key: "deal_token", Value: dealToken})
    }
    if tBeanTimeStr != "" {
        tBeanTime, _ := time.ParseInLocation("2006-01-02 15:04:05", tBeanTimeStr, time.Local)
        value = append(value, bson.E{Key: "t_bean_sys_time", Value: tBeanTime.Unix()})
    }
    update := bson.D{
        {Key: "$set", Value: value},
    }
    _, err := s.weddingOrderCollection.UpdateMany(ctx, filter, update)
    if err != nil {
        if errors.Is(err, tyrMongo.NoDocuments) {
            log.WarnWithCtx(ctx, "ChangeConsumeRecordPayInfo error: %v, filter:%+v, update:%+v, uid:%d", err, filter, update, uid)
            return false, nil
        }
        log.ErrorWithCtx(ctx, "ChangeConsumeRecordPayInfo error: %v, filter:%+v, update:%+v, uid:%d", err, filter, update, uid)
        return false, err
    }
    log.InfoWithCtx(ctx, "ChangeConsumeRecordPayInfo success, filter:%+v, update:%+v, uid:%d", filter, update, uid)
    return true, nil
}

// UpdateOrderStatus （needCheckBuyer 购买方才能取消婚礼）
func (s *Store) UpdateOrderStatus(ctx context.Context, planId uint32, oldStatus []uint32, newStatus uint32) (match bool, err error) {
    // 保证状态匹配
    filter := bson.D{
        {Key: "_id", Value: planId},
        {Key: "wedding_status", Value: bson.M{"$in": oldStatus}},
    }

    value := bson.D{
        {Key: "wedding_status", Value: newStatus},
        {Key: "update_ts", Value: time.Now().Unix()},
    }
    update := bson.D{
        {Key: "$set", Value: value},
    }
    _, err = s.weddingOrderCollection.UpdateOne(ctx, filter, update)
    if err != nil {
        if errors.Is(err, tyrMongo.NoDocuments) {
            log.DebugWithCtx(ctx, "UpdateOrderStatus error: %v, filter:%+v, update:%+v", err, filter, update)
            return false, nil
        }

        log.ErrorWithCtx(ctx, "UpdateOrderStatus error: %v, filter:%+v, update:%+v", err, filter, update)
        return false, err
    }
    log.InfoWithCtx(ctx, "UpdateOrderStatus success, filter:%+v, update:%+v", filter, update)
    return true, nil
}

func (s *Store) GetWeddingOrderById(ctx context.Context, orderId string) (*WeddingOrder, error) {
    filter := bson.D{
        {Key: "order_id", Value: orderId},
    }
    order := &WeddingOrder{}
    err := s.weddingOrderCollection.Collection.FindOne(ctx, filter).Decode(order)
    if err != nil {
        if errors.Is(err, tyrMongo.NoDocuments) {
            log.DebugWithCtx(ctx, "GetWeddingOrderById error: %v, filter:%+v", err, filter)
            return nil, nil
        }
    }
    log.DebugWithCtx(ctx, "GetWeddingOrderById success, filter:%+v, order:%+v", filter, order)
    return order, nil
}

func (s *Store) GetWeddingOrderByPlanId(ctx context.Context, planId uint32) (*WeddingOrder, error) {
    filter := bson.D{
        {Key: "_id", Value: planId},
    }
    order := &WeddingOrder{}
    err := s.weddingOrderCollection.Collection.FindOne(ctx, filter).Decode(order)
    if err != nil {
        if errors.Is(err, tyrMongo.NoDocuments) {
            log.DebugWithCtx(ctx, "GetWeddingOrderByPlanId error: %v, filter:%+v", err, filter)
            return nil, nil
        }
    }
    log.DebugWithCtx(ctx, "GetWeddingOrderByPlanId success, filter:%+v, order:%+v", filter, order)
    return order, nil
}

func (s *Store) BatchGetWeddingOrderByPlanIdList(ctx context.Context, planIdList []uint32) ([]*WeddingOrder, error) {
    filter := bson.M{"_id": bson.M{"$in": planIdList}}
    cur, err := s.weddingOrderCollection.Collection.Find(ctx, filter)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetWeddingOrderByPlanIdList error: %v, filter:%+v", err, filter)
        return nil, err
    }

    var rs []*WeddingOrder
    for cur.Next(ctx) {
        var order WeddingOrder
        if err := cur.Decode(&order); err != nil {
            log.ErrorWithCtx(ctx, "BatchGetWeddingOrderByPlanIdList Decode error: %v", err)
            return nil, err
        }
        rs = append(rs, &order)
    }

    log.DebugWithCtx(ctx, "BatchGetWeddingOrderByPlanIdList success, filter:%+v, orderList:%+v", filter, rs)
    return rs, nil
}

func (s *Store) GetOrderList(ctx context.Context, orderStatus []uint32, limit int64) ([]*WeddingOrder, error) {
    filter := bson.D{
        {Key: "wedding_status", Value: bson.M{"$in": orderStatus}},
    }
    cur, err := s.weddingOrderCollection.Collection.Find(ctx, filter, options.Find().SetLimit(limit))
    if err != nil {
        log.ErrorWithCtx(ctx, "GetOrderList error: %v, filter:%+v", err, filter)
        return nil, err
    }
    defer cur.Close(ctx)
    var rs []*WeddingOrder
    for cur.Next(ctx) {
        var order WeddingOrder
        if err := cur.Decode(&order); err != nil {
            log.ErrorWithCtx(ctx, "GetOrderList Decode error: %v", err)
            return nil, err
        }
        rs = append(rs, &order)
    }
    log.InfoWithCtx(ctx, "GetOrderList success, filter:%+v, orderList len:%d", filter, len(rs))
    return rs, nil
}

// GetWeddingOrderCnt 获取婚礼订单数量
func (s *Store) GetValidWeddingOrderCnt(ctx context.Context, priceType uint32, startTs int64, uid uint32) (uint32, error) {
    filter := bson.M{
        "wedding_price_type": priceType,
        "create_ts":          bson.M{"$gte": startTs},
        "wedding_status":     bson.M{"$gt": WeddingStatusInit},
        "$or": []bson.M{
            {"groom_uid": uid},
            {"bride_uid": uid},
        },
    }
    cnt, err := s.weddingOrderCollection.Collection.CountDocuments(ctx, filter)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetValidWeddingOrderCnt error: %v, filter:%+v", err, filter)
        return 0, err
    }
    log.DebugWithCtx(ctx, "GetValidWeddingOrderCnt success, filter:%+v, cnt:%d", filter, cnt)
    return uint32(cnt), nil
}

type StCount struct {
    Count int64 `db:"count"`
    Worth int64 `db:"worth"`
}

func (s *Store) GetConsumeTotalCountInfo(ctx context.Context, beginTime, endTime time.Time) (*StCount, error) {
    filter := bson.D{
        {Key: "wedding_status", Value: WeddingStatusConfirm},
        {Key: "t_bean_sys_time", Value: bson.M{"$gte": beginTime.Unix(), "$lt": endTime.Unix()}},
    }
    pipeline := []bson.M{
        {"$match": filter},
        {"$group": bson.M{
            "_id":   nil,
            "count": bson.M{"$sum": 1},
            "worth": bson.M{"$sum": "$wedding_price"},
        }},
    }
    cur, err := s.weddingOrderCollection.Collection.Aggregate(ctx, pipeline)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetConsumeTotalCountInfo error: %v, filter:%+v", err, filter)
        return nil, err
    }
    defer cur.Close(ctx)

    var rs StCount
    if cur.Next(ctx) {
        if err := cur.Decode(&rs); err != nil {
            log.ErrorWithCtx(ctx, "GetConsumeTotalCountInfo Decode error: %v", err)
            return nil, err
        }
    }
    log.DebugWithCtx(ctx, "GetConsumeTotalCountInfo success, filter:%+v, rs:%+v", filter, rs)
    return &rs, nil
}

func (s *Store) GetConsumeOrderIds(ctx context.Context, beginTime, endTime time.Time) ([]string, error) {
    filter := bson.D{
        {Key: "wedding_status", Value: WeddingStatusConfirm},
        {Key: "t_bean_sys_time", Value: bson.M{"$gte": beginTime.Unix(), "$lt": endTime.Unix()}},
    }
    cur, err := s.weddingOrderCollection.Collection.Find(ctx, filter)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetConsumeOrderIds error: %v, filter:%+v", err, filter)
        return nil, err
    }
    defer cur.Close(ctx)

    var rs []string
    for cur.Next(ctx) {
        var order WeddingOrder
        if err := cur.Decode(&order); err != nil {
            log.ErrorWithCtx(ctx, "GetConsumeOrderIds Decode error: %v", err)
            return nil, err
        }
        rs = append(rs, order.OrderId)
    }
    log.DebugWithCtx(ctx, "GetConsumeOrderIds success, filter:%+v, orderList len:%d", filter, len(rs))
    return rs, nil
}
