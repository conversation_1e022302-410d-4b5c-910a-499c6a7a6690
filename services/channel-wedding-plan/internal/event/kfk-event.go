package event

import (
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/services/channel-wedding-plan/internal/conf"
	"golang.52tt.com/services/channel-wedding-plan/internal/mgr"
	"golang.52tt.com/services/channel-wedding-plan/internal/rpc"
)

type KafkaEvent struct {
	sc                 *conf.StartConfig
	userOnlineSub      subscriber.Subscriber
	userInfoSub        subscriber.Subscriber
	fellowSub          subscriber.Subscriber
	channelMicEventSub subscriber.Subscriber
	presentSub         subscriber.Subscriber
	mgr                *mgr.WeddingPlanMgr
	rpc                *rpc.Rpc
}

func NewKafkaEvent(sc *conf.StartConfig, mgr *mgr.WeddingPlanMgr, rpc *rpc.Rpc) (*KafkaEvent, error) {
	if sc == nil || sc.UserOnlineKfk == nil {
		return nil, fmt.Errorf("sc or sc.UserOnlineKfk is nil")
	}

	userInfoKfkCfg := sc.UserOnlineKfk

	kfkCfg := kafka.DefaultConfig()
	kfkCfg.ClientID = userInfoKfkCfg.ClientID
	kfkCfg.Consumer.Offsets.Initial = kafka.OffsetNewest
	kfkCfg.Consumer.Return.Errors = true

	userOnlineSub, err := kafka.NewSub(sc.UserOnlineKfk.BrokerList(), kfkCfg,
		subscriber.InitOptions(subscriber.WithMaxRetryTimes(3)))
	if err != nil {
		log.Errorf("NewKafkaEvent NewSub userOnlineSub failed, err:%v", err)
		return nil, err
	}

	userInfoSub, err := kafka.NewSub(sc.UserInfoKfk.BrokerList(), kfkCfg,
		subscriber.InitOptions(subscriber.WithMaxRetryTimes(3)))
	if err != nil {
		log.Errorf("NewKafkaEvent NewSub userInfoSub failed, err:%v", err)
		return nil, err
	}

	fellowSub, err := kafka.NewSub(sc.FellowKfk.BrokerList(), kfkCfg,
		subscriber.InitOptions(subscriber.WithMaxRetryTimes(3)))
	if err != nil {
		log.Errorf("NewKafkaEvent NewSub userInfoSub failed, err:%v", err)
		return nil, err
	}

	channelMicEventSub, err := kafka.NewSub(sc.ChannelMicKafkaConfig.BrokerList(), kfkCfg,
		subscriber.InitOptions(subscriber.WithMaxRetryTimes(3)))
	if err != nil {
		log.Errorf("NewKafkaEvent NewSub channelMicEventSub failed, err: %v", err)
		return nil, err
	}

	presentSub, err := kafka.NewSub(sc.PresentKafkaConfig.BrokerList(), kfkCfg,
		subscriber.InitOptions(subscriber.WithMaxRetryTimes(3)))
	if err != nil {
		log.Errorf("NewKafkaEvent NewSub presentSub failed, err: %v", err)
		return nil, err
	}

	ev := &KafkaEvent{
		sc:                 sc,
		userOnlineSub:      userOnlineSub,
		userInfoSub:        userInfoSub,
		fellowSub:          fellowSub,
		channelMicEventSub: channelMicEventSub,
		presentSub:         presentSub,
		mgr:                mgr,
		rpc:                rpc,
	}

	err = ev.userOnlineSub.SubscribeContext(sc.UserOnlineKfk.GroupID, sc.UserOnlineKfk.TopicList(),
		subscriber.ProcessorContextFunc(ev.HandleUserOnlineEvent))
	if err != nil {
		log.Errorf("NewKafkaEvent SubscribeContext failed, err:%v", err)
		return nil, err
	}

	err = ev.userInfoSub.SubscribeContext(sc.UserInfoKfk.GroupID, sc.UserInfoKfk.TopicList(),
		subscriber.ProcessorContextFunc(ev.HandleUserInfoChange))
	if err != nil {
		log.Errorf("NewKafkaEvent SubscribeContext failed, err:%v", err)
		return nil, err
	}

	err = ev.fellowSub.SubscribeContext(sc.FellowKfk.GroupID, sc.FellowKfk.TopicList(),
		subscriber.ProcessorContextFunc(ev.HandleFellowUpdateEvent))
	if err != nil {
		log.Errorf("NewKafkaEvent SubscribeContext failed, err:%v", err)
		return nil, err
	}

	err = ev.channelMicEventSub.SubscribeContext(sc.ChannelMicKafkaConfig.GroupID, sc.ChannelMicKafkaConfig.TopicList(),
		subscriber.ProcessorContextFunc(ev.HandleChannelMicEvent))
	if err != nil {
		log.Errorf("NewKafkaEvent SubscribeContext failed, err:%v", err)
		return nil, err
	}

	err = ev.presentSub.SubscribeContext(sc.PresentKafkaConfig.GroupID, sc.PresentKafkaConfig.TopicList(),
		subscriber.ProcessorContextFunc(ev.handlerPresentEvent))
	if err != nil {
		log.Errorf("NewKafkaEvent SubscribeContext failed, err:%v", err)
		return nil, err
	}

	return ev, nil
}

func (k *KafkaEvent) Close() {
	_ = k.userOnlineSub.Stop()
}
