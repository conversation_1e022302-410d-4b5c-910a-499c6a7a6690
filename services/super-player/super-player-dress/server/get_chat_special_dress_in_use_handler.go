package server

import (
	"context"

	"golang.52tt.com/services/super-player/super-player-dress/store"

	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/superplayerdress"
)

func (svr *SuperPlayerDress) GetUserCurrChatBgSpecialDressId(ctx context.Context, in *pb.GetUserCurrChatBgSpecialDressIdReq) (*pb.GetUserCurrChatBgSpecialDressIdResp, error) {
	var out pb.GetUserCurrChatBgSpecialDressIdResp
	var outErr error

	uid := in.GetUid()
	for i := 0; i < 1; i++ {
		if in.GetLevel() <= 0 {
			break
		}

		dressId, err := svr.GetStore().GetChatBgSpecialInDress(ctx, uid, in.GetAccount())
		if err != nil {
			if err.Error() == "record not found" {
				out.DressId = 0
				outErr = nil
				break
			} else {
				outErr = err
				break
			}
		}
		out.DressId = dressId
	}

	if out.DressId > 0 {
		dressCfg, err := svr.GetStore().GetDressByIDAndType(ctx, out.DressId, uint32(pb.DressType_DRESS_TYPE_CHAT_BACKGROUND))
		if err == nil {
			if dressCfg.Level > in.Level {
				// 判断用户是否有体验资格
				dressExperienceMap := svr.mgr.GetUserHasDressExperienceMap(ctx, uid, uint32(pb.DressType_DRESS_TYPE_CHAT_BACKGROUND), []*store.DressConfig{dressCfg})
				if !dressExperienceMap[out.DressId] {
					log.ErrorWithCtx(ctx, "GetDressInUse dress config is bigger uid:%d", uid)
					out.DressId = 0
				}
			}
		}
	}
	log.DebugfWithCtx(ctx, "GetDressInUse in:%+v, out:%+v, err:%v", in, out, outErr)
	return &out, outErr
}
