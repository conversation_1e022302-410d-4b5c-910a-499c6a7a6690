package main

import (
	"context"
	"fmt"
	"time"

	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
)

var contractList = []string{
	"sp_278885632_APPSTORE_20220525180008",
	"sp_278885632_APPSTORE_20220525180008",
	"sp_278885632_APPSTORE_20220525180008",
	"sp_278885632_APPSTORE_20220525180008",
	"sp_301377204_APPSTORE_20221224165647",
	"sp_301377204_APPSTORE_20221224165647",
	"sp_283999207_APPSTORE_20230928214426",
	"sp_310270428_APPSTORE_20231106022412",
	"sp_283999207_APPSTORE_20230928214426",
	"sp_310270428_APPSTORE_20231106022412",
	"sp_283999207_APPSTORE_20230928214426",
	"sp_310270428_APPSTORE_20231106022412",
	"sp_283999207_APPSTORE_20230928214426",
	"sp_249860660_APPSTORE_20220517235107",
	"sp_310270428_APPSTORE_20231106022412",
	"sp_281200638_APPSTORE_20231021073351",
	"sp_281200638_APPSTORE_20231021073351",
	"sp_275714629_APPSTORE_20230818174807",
	"sp_110225657_APPSTORE_20230130202706",
	"sp_281200638_APPSTORE_20231021073351",
	"sp_275714629_APPSTORE_20230818174807",
	"sp_249860660_APPSTORE_20220517235107",
	"sp_110225657_APPSTORE_20230130202706",
	"sp_281200638_APPSTORE_20231021073351",
	"sp_275714629_APPSTORE_20230818174807",
	"sp_110225657_APPSTORE_20230130202706",
	"sp_275714629_APPSTORE_20230818174807",
	"sp_281200638_APPSTORE_20231021073351",
	"sp_281200638_APPSTORE_20231021073351",
	"sp_281200638_APPSTORE_20231021073351",
	"sp_281200638_APPSTORE_20231021073351",
	"sp_281200638_APPSTORE_20231021073351",
	"sp_281200638_APPSTORE_20231021073351",
	"sp_281200638_APPSTORE_20231021073351",
	"sp_281200638_APPSTORE_20231021073351",
	"sp_249860660_APPSTORE_20220517235107",
	"sp_281200638_APPSTORE_20231021073351",
	"sp_281200638_APPSTORE_20231021073351",
	"sp_249860660_APPSTORE_20220517235107",
	"sp_172400041_APPSTORE_20220628131612",
	"sp_250991027_APPSTORE_20220611023024",
	"sp_249860660_APPSTORE_20220517235107",
	"sp_172400041_APPSTORE_20220628131612",
	"sp_250991027_APPSTORE_20220611023024",
	"sp_33331359_APPSTORE_20221231222630",
	"sp_250991027_APPSTORE_20220611023024",
	"sp_172254730_APPSTORE_20210902140544",
	"sp_308946682_APPSTORE_20230318231017",
	"sp_319025537_APPSTORE_20230616023148",
	"sp_130210524_APPSTORE_20220718232250",
	"sp_223222070_APPSTORE_20210810043159",
	"sp_292179419_APPSTORE_20221116130201",
	"sp_273850562_APPSTORE_20221219011945",
	"sp_226081883_APPSTORE_20210716000552",
	"sp_306382255_APPSTORE_20230126174351",
	"sp_170062004_APPSTORE_20210825045120",
	"sp_33331359_APPSTORE_20221231222630",
	"sp_161040742_APPSTORE_20220719013428",
	"sp_250991027_APPSTORE_20220611023024",
	"sp_280615924_APPSTORE_20231111194651",
	"sp_118134239_APPSTORE_20221013004532",
	"sp_222828420_APPSTORE_20220919021056",
	"sp_184445108_APPSTORE_20231020203739",
	"sp_278042933_APPSTORE_20220519023015",
	"sp_25264225_APPSTORE_20220307054642",
	"sp_128890996_APPSTORE_20230819023137",
	"sp_53238538_APPSTORE_20221019033328",
	"sp_284798014_APPSTORE_20220805063259",
	"sp_164495_APPSTORE_20220925221100",
	"sp_182276182_APPSTORE_20230519055507",
	"sp_172254730_APPSTORE_20210902140544",
	"sp_263339078_APPSTORE_20220818101426",
	"sp_141719253_APPSTORE_20220616221309",
	"sp_268706831_APPSTORE_20230418090937",
	"sp_110423_APPSTORE_20230719075521",
	"sp_214170965_APPSTORE_20221115144452",
	"sp_172400041_APPSTORE_20220628131612",
	"sp_273807972_APPSTORE_20231019091637",
	"sp_278640768_APPSTORE_20220919163600",
	"sp_308946682_APPSTORE_20230318231017",
	"sp_319025537_APPSTORE_20230616023148",
	"sp_130210524_APPSTORE_20220718232250",
	"sp_208121457_APPSTORE_20220507174139",
	"sp_292179419_APPSTORE_20221116130201",
	"sp_223222070_APPSTORE_20210810043159",
	"sp_273850562_APPSTORE_20221219011945",
	"sp_226081883_APPSTORE_20210716000552",
	"sp_306382255_APPSTORE_20230126174351",
	"sp_170062004_APPSTORE_20210825045120",
	"sp_231900580_APPSTORE_20220619104114",
	"sp_282273903_APPSTORE_20220614173121",
	"sp_152378105_APPSTORE_20210716085052",
	"sp_33331359_APPSTORE_20221231222630",
	"sp_161040742_APPSTORE_20220719013428",
	"sp_197857639_APPSTORE_20231019122430",
	"sp_118134239_APPSTORE_20221013004532",
	"sp_222828420_APPSTORE_20220919021056",
	"sp_184445108_APPSTORE_20231020203739",
	"sp_278042933_APPSTORE_20220519023015",
	"sp_280615924_APPSTORE_20231111194651",
	"sp_239481084_APPSTORE_20231013225040",
	"sp_25264225_APPSTORE_20220307054642",
	"sp_278416976_APPSTORE_20220511212005",
	"sp_128890996_APPSTORE_20230819023137",
	"sp_53238538_APPSTORE_20221019033328",
	"sp_275235274_APPSTORE_20221219143253",
	"sp_284798014_APPSTORE_20220805063259",
	"sp_263363044_APPSTORE_20230819054113",
	"sp_164495_APPSTORE_20220925221100",
	"sp_33547719_APPSTORE_20220805011433",
	"sp_87969890_APPSTORE_20210804130605",
	"sp_172254730_APPSTORE_20210902140544",
	"sp_263339078_APPSTORE_20220818101426",
	"sp_182276182_APPSTORE_20230519055507",
	"sp_153312971_APPSTORE_20220817075357",
	"sp_91283293_APPSTORE_20221219171021",
	"sp_141719253_APPSTORE_20220616221309",
	"sp_314889904_APPSTORE_20230830063211",
	"sp_277840594_APPSTORE_20220821030634",
	"sp_145154834_APPSTORE_20220819172229",
	"sp_23773291_APPSTORE_20230218231212",
	"sp_268706831_APPSTORE_20230418090937",
	"sp_275377927_APPSTORE_20221109183650",
	"sp_279428848_APPSTORE_20220516084157",
	"sp_110423_APPSTORE_20230719075521",
	"sp_123364715_APPSTORE_20220619132505",
	"sp_289725804_APPSTORE_20220919182354",
	"sp_214170965_APPSTORE_20221115144452",
	"sp_273130183_APPSTORE_20221019080505",
	"sp_327326671_APPSTORE_20230817184538",
	"sp_280406350_APPSTORE_20220519192200",
	"sp_273807972_APPSTORE_20231019091637",
	"sp_278640768_APPSTORE_20220919163600",
	"sp_308946682_APPSTORE_20230318231017",
	"sp_319025537_APPSTORE_20230616023148",
	"sp_130210524_APPSTORE_20220718232250",
	"sp_208121457_APPSTORE_20220507174139",
	"sp_201287582_APPSTORE_20220819195322",
	"sp_292179419_APPSTORE_20221116130201",
	"sp_223222070_APPSTORE_20210810043159",
	"sp_273850562_APPSTORE_20221219011945",
	"sp_226081883_APPSTORE_20210716000552",
	"sp_306382255_APPSTORE_20230126174351",
	"sp_170062004_APPSTORE_20210825045120",
	"sp_231900580_APPSTORE_20220619104114",
	"sp_261865991_APPSTORE_20230215141608",
	"sp_282273903_APPSTORE_20220614173121",
	"sp_214525462_APPSTORE_20231029201009",
	"sp_185120781_APPSTORE_20210725153211",
	"sp_33331359_APPSTORE_20221231222630",
	"sp_247354349_APPSTORE_20240219235740",
	"sp_161040742_APPSTORE_20220719013428",
	"sp_312623713_APPSTORE_20231007185252",
	"sp_152378105_APPSTORE_20210716085052",
	"sp_197857639_APPSTORE_20231019122430",
	"sp_118134239_APPSTORE_20221013004532",
	"sp_222828420_APPSTORE_20220919021056",
	"sp_184445108_APPSTORE_20231020203739",
	"sp_278042933_APPSTORE_20220519023015",
	"sp_280615924_APPSTORE_20231111194651",
	"sp_180445762_APPSTORE_20220810222858",
	"sp_239481084_APPSTORE_20231013225040",
	"sp_33516469_APPSTORE_20221126180623",
	"sp_25264225_APPSTORE_20220307054642",
	"sp_278416976_APPSTORE_20220511212005",
	"sp_283810531_APPSTORE_20220716053307",
	"sp_128890996_APPSTORE_20230819023137",
	"sp_53238538_APPSTORE_20221019033328",
	"sp_275235274_APPSTORE_20221219143253",
	"sp_284798014_APPSTORE_20220805063259",
	"sp_263363044_APPSTORE_20230819054113",
	"sp_102530368_APPSTORE_20211007045804",
	"sp_157957561_APPSTORE_20231120021240",
	"sp_182497645_APPSTORE_20230719234720",
	"sp_288453411_APPSTORE_20230511051655",
	"sp_164495_APPSTORE_20220925221100",
	"sp_33547719_APPSTORE_20220805011433",
	"sp_87969890_APPSTORE_20210804130605",
	"sp_172254730_APPSTORE_20210902140544",
	"sp_263339078_APPSTORE_20220818101426",
	"sp_182276182_APPSTORE_20230519055507",
	"sp_280213266_APPSTORE_20220522121706",
	"sp_153312971_APPSTORE_20220817075357",
	"sp_91283293_APPSTORE_20221219171021",
	"sp_227174859_APPSTORE_20220611202208",
	"sp_228004057_APPSTORE_20230301130444",
	"sp_31582575_APPSTORE_20230220021718",
	"sp_242993816_APPSTORE_20221020013137",
	"sp_310270428_APPSTORE_20231106022412",
	"sp_141719253_APPSTORE_20220616221309",
	"sp_238536092_APPSTORE_20220620014037",
	"sp_120863931_APPSTORE_20230920000551",
	"sp_290769633_APPSTORE_20220913042635",
	"sp_144506770_APPSTORE_20221118232434",
	"sp_277840594_APPSTORE_20220821030634",
	"sp_314889904_APPSTORE_20230830063211",
	"sp_145154834_APPSTORE_20220819172229",
	"sp_223403454_APPSTORE_20221220030710",
	"sp_210503567_APPSTORE_20220619144646",
	"sp_101204401_APPSTORE_20221120032839",
	"sp_324650250_APPSTORE_20240120033820",
	"sp_107493961_APPSTORE_20230618090844",
	"sp_306728549_APPSTORE_20231111135856",
	"sp_268706831_APPSTORE_20230418090937",
	"sp_275377927_APPSTORE_20221109183650",
	"sp_279428848_APPSTORE_20220516084157",
	"sp_23773291_APPSTORE_20230218231212",
	"sp_213926899_APPSTORE_20220720025240",
	"sp_260026115_APPSTORE_20230120035503",
	"sp_315013333_APPSTORE_20231117011006",
	"sp_110423_APPSTORE_20230719075521",
	"sp_123364715_APPSTORE_20220619132505",
	"sp_289725804_APPSTORE_20220919182354",
	"sp_338434914_APPSTORE_20240120041003",
	"sp_230028234_APPSTORE_20230403083918",
	"sp_214170965_APPSTORE_20221115144452",
	"sp_273130183_APPSTORE_20221019080505",
	"sp_327326671_APPSTORE_20230817184538",
	"sp_280406350_APPSTORE_20220519192200",
	"sp_82279449_APPSTORE_20220619230008",
	"sp_150149502_APPSTORE_20230720043557",
	"sp_273807972_APPSTORE_20231019091637",
	"sp_278640768_APPSTORE_20220919163600",
	"sp_308946682_APPSTORE_20230318231017",
	"sp_319025537_APPSTORE_20230616023148",
	"sp_130210524_APPSTORE_20220718232250",
	"sp_208121457_APPSTORE_20220507174139",
	"sp_201287582_APPSTORE_20220819195322",
	"sp_117605848_APPSTORE_20230120061317",
	"sp_156983875_APPSTORE_20220508193659",
	"sp_292179419_APPSTORE_20221116130201",
	"sp_223222070_APPSTORE_20210810043159",
	"sp_111102662_APPSTORE_20220509070257",
	"sp_273850562_APPSTORE_20221219011945",
	"sp_226081883_APPSTORE_20210716000552",
	"sp_306382255_APPSTORE_20230126174351",
	"sp_170062004_APPSTORE_20210825045120",
	"sp_231900580_APPSTORE_20220619104114",
	"sp_261865991_APPSTORE_20230215141608",
}

func NewMysql() (mysql.DBx, error) {
	mysqlConf := &mysqlConnect.MysqlConfig{
		Host:     "***********",
		Port:     3306,
		Database: "superplayer",
		Charset:  "utf8",
		UserName: "godman",
		Password: "thegodofman",
	}
	//mysqlConf := &mysqlConnect.MysqlConfig{
	//	//Host:     "***********", // 线上
	//	Host:     "************", // 云测
	//	Port:     3306,
	//	Database: "appsvr",
	//	Charset:  "utf8mb4",
	//	UserName: "godman",
	//	Password: "thegodofman",
	//}

	ctx := context.Background()
	db, err := mysqlConnect.NewClient(ctx, mysqlConf)
	if err != nil {
		panic(err)
	}
	return db, err
}

func main() {
	// 数据库连接字符串
	dBx, err := NewMysql()
	if err != nil {
		panic(err)
	}
	now := time.Now()

	// 遍历CSV记录并更新数据库
	for _, contract := range contractList { // 不跳过任何行
		// 执行SQL更新
		sql := "update tbl_superplayer_contract_record_v2 set `next_time` = ? where `contract_id` = ?"
		_, err = dBx.Exec(sql, now, contract)
		if err != nil {
			fmt.Printf("Failed to update contract %s: %s\n", contract, err)
		}
		fmt.Printf("Updated contract %s\n", contract)
	}
}
