package mongo

/*
import (
	"fmt"
	"github.com/globalsign/mgo"
	"github.com/orlangure/gnomock"
	gnomock_mongo "github.com/orlangure/gnomock/preset/mongo"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"reflect"
	"testing"
	"time"
)

var mDao = &MongoDao{}

var anchorUid uint32 = 2212797
var fansUid uint32 = 2185921
var groupId uint32 = 100001
var groupName = "团名测试"
var nowTm = time.Unix(1660122403, 0)
var addGroupTm = time.Unix(1661153000, 0)
var loveValue = 1000
var plateId uint32 = 1

var grantId uint32 = 1
var beginTs int64 = 1661739040
var endTs int64 = 1756433440
var updateTs int64 = 1693275040
var summitTs int64 = 1661739040

var verifyRecord = GroupNameVerifyRecord{
	AnchorUid:     anchorUid,
	GroupName:     groupName,
	SummitTs:      nowTm.Unix(),
	VerifyResult:  0,
	NotPassReason: "dd",
	Operator:      "dd",
	OperateTs:     nowTm.Unix(),
	AnchorTTid:    "dd",
	AnchorNick:    "dd",
}

func init() {
	p := gnomock_mongo.Preset()
	c, err := gnomock.Start(p, gnomock.WithUseLocalImagesFirst())
	if err != nil {
		log.Errorf("gnomock.Start(p) failed err:%v", err)
	}
	if c == nil {
		fmt.Printf("init err: %v\n", err)
	}

	mongoConfig := &config.MongoConfig{
		Addrs:       c.DefaultAddress(),
		Database:    "channellivefans",
		MaxPoolSize: 10,
	}

	mDao, err = NewMongoDao(mongoConfig)
	if err != nil {
		log.Errorf("NewMongoDao failed err:%v", err)
	}
}

func TestMongoDao_GroupIdIncr(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
	}
	tests := []struct {
		name    string
		fields  fields
		want    uint32
		wantErr bool
	}{
		{
			name:    "GroupIdIncr",
			fields:  fields{infoSession: mDao.infoSession},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GroupIdIncr()
			if (err != nil) != tt.wantErr {
				t.Errorf("GroupIdIncr() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GroupIdIncr() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_SaveFansGroupInfo(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
	}
	type args struct {
		groupId uint32
		uid     uint32
		nowTime time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "SaveFansGroupInfo",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				groupId: groupId,
				uid:     anchorUid,
				nowTime: nowTm,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			if err := m.SaveFansGroupInfo(tt.args.groupId, tt.args.uid, tt.args.nowTime); (err != nil) != tt.wantErr {
				t.Errorf("SaveFansGroupInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMongoDao_CheckGroupNameIsExists(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
	}
	type args struct {
		groupName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name:    "CheckGroupNameIsExists",
			fields:  fields{infoSession: mDao.infoSession},
			args:    args{groupName: groupName},
			want:    false,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.CheckGroupNameIsExists(tt.args.groupName)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckGroupNameIsExists() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("CheckGroupNameIsExists() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_UpdateGroupName(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
	}
	type args struct {
		anchorUid uint32
		groupName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "UpdateGroupName",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				anchorUid: anchorUid,
				groupName: groupName,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			if err := m.UpdateGroupName(tt.args.anchorUid, tt.args.groupName); (err != nil) != tt.wantErr {
				t.Errorf("UpdateGroupName() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMongoDao_GetGroupInfoByUid(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
	}
	type args struct {
		anchorUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    FansGroupInfo
		wantErr bool
	}{
		{
			name:   "GetGroupInfoByUid",
			fields: fields{infoSession: mDao.infoSession},
			args:   args{anchorUid: anchorUid},
			want: FansGroupInfo{
				AnchorUid:  anchorUid,
				GroupId:    groupId,
				GroupName:  groupName,
				CreateTime: nowTm.UTC(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetGroupInfoByUid(tt.args.anchorUid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGroupInfoByUid() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetGroupInfoByUid() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_BatchGetGroupInfo(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
	}
	type args struct {
		anchorUids []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []FansGroupInfo
		wantErr bool
	}{
		{
			name:   "BatchGetGroupInfo",
			fields: fields{infoSession: mDao.infoSession},
			args:   args{anchorUids: []uint32{anchorUid}},
			want: []FansGroupInfo{{
				AnchorUid:  anchorUid,
				GroupId:    groupId,
				CreateTime: nowTm.UTC(),
				GroupName:  groupName,
			}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.BatchGetGroupInfo(tt.args.anchorUids)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetGroupInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchGetGroupInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetGroupIdByAnChor(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name:    "GetGroupIdByAnChor",
			fields:  fields{infoSession: mDao.infoSession},
			args:    args{uid: anchorUid},
			want:    groupId,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetGroupIdByAnChor(tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGroupIdByAnChor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetGroupIdByAnChor() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_CheckIsCreateGroup(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name:    "CheckIsCreateGroup",
			fields:  fields{infoSession: mDao.infoSession},
			args:    args{uid: anchorUid},
			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.CheckIsCreateGroup(tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckIsCreateGroup() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("CheckIsCreateGroup() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_SaveFansGroupMemberInfo(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		ownerUid uint32
		fansUid  uint32
		groupId  uint32
		addTime  time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "SaveFansGroupMemberInfo",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				ownerUid: anchorUid,
				fansUid:  fansUid,
				groupId:  groupId,
				addTime:  addGroupTm,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			if err := m.SaveFansGroupMemberInfo(tt.args.ownerUid, tt.args.fansUid, tt.args.groupId, tt.args.addTime); (err != nil) != tt.wantErr {
				t.Errorf("SaveFansGroupMemberInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMongoDao_AddFansLoveValue(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
		fansUid   uint32
		loveValue uint32
		nowTime   time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name:   "AddFansLoveValue",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				anchorUid: anchorUid,
				fansUid:   fansUid,
				loveValue: uint32(loveValue),
				nowTime:   nowTm,
			},
			want:    uint32(loveValue),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.AddFansLoveValue(tt.args.anchorUid, tt.args.fansUid, tt.args.loveValue, tt.args.nowTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddFansLoveValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("AddFansLoveValue() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetAllFansByUid(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []FansGroupMemberInfo
		wantErr bool
	}{
		{
			name:   "GetAllFansByUid",
			fields: fields{infoSession: mDao.infoSession},
			args:   args{anchorUid: anchorUid},
			want: []FansGroupMemberInfo{
				{
					GroupId:         groupId,
					AnchorUid:       anchorUid,
					FansUid:         fansUid,
					LoveValue:       uint32(loveValue),
					ValueUpdateTime: nowTm.UTC(),
					AddGroupTime:    addGroupTm.UTC(),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetAllFansByUid(tt.args.anchorUid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllFansByUid() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllFansByUid() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetFansCount(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name:    "GetFansCount",
			fields:  fields{infoSession: mDao.infoSession},
			args:    args{anchorUid: anchorUid},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetFansCount(tt.args.anchorUid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFansCount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetFansCount() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetFansCntByTs(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
		tm        time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name:   "GetFansCntByTs",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				anchorUid: anchorUid,
				tm:        time.Now(),
			},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetFansCntByTs(tt.args.anchorUid, tt.args.tm)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFansCntByTs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetFansCntByTs() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetActiveFans(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
		limit     int
		offset    int
		ts        time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []FansGroupMemberInfo
		wantErr bool
	}{
		{
			name:   "GetActiveFans",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				anchorUid: anchorUid,
				limit:     1,
				offset:    0,
				ts:        nowTm,
			},
			want: []FansGroupMemberInfo{
				{
					GroupId:         groupId,
					AnchorUid:       anchorUid,
					FansUid:         fansUid,
					LoveValue:       uint32(loveValue),
					ValueUpdateTime: nowTm.UTC(),
					AddGroupTime:    addGroupTm.UTC(),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetActiveFans(tt.args.anchorUid, tt.args.limit, tt.args.offset, tt.args.ts)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetActiveFans() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetActiveFans() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetActiveFansCount(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
		ts        time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name:   "GetActiveFansCount",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				anchorUid: anchorUid,
				ts:        nowTm,
			},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetActiveFansCount(tt.args.anchorUid, tt.args.ts)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetActiveFansCount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetActiveFansCount() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetInActiveFans(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
		limit     int
		offset    int
		ts        time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []FansGroupMemberInfo
		wantErr bool
	}{
		{
			name:   "GetInActiveFans",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				anchorUid: anchorUid,
				limit:     1,
				offset:    0,
				ts:        time.Now(),
			},
			want: []FansGroupMemberInfo{
				{
					GroupId:         groupId,
					AnchorUid:       anchorUid,
					FansUid:         fansUid,
					LoveValue:       uint32(loveValue),
					ValueUpdateTime: nowTm.UTC(),
					AddGroupTime:    addGroupTm.UTC(),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetInActiveFans(tt.args.anchorUid, tt.args.limit, tt.args.offset, tt.args.ts)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetInActiveFans() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetInActiveFans() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetInActiveFansCount(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
		ts        time.Time
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name:   "GetInActiveFansCount",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				anchorUid: anchorUid,
				ts:        nowTm,
			},
			want:    0,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetInActiveFansCount(tt.args.anchorUid, tt.args.ts)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetInActiveFansCount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetInActiveFansCount() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetFansInfo(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		uid       uint32
		anchorUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    FansGroupMemberInfo
		wantErr bool
	}{
		{
			name:   "GetFansInfo",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				uid:       fansUid,
				anchorUid: anchorUid,
			},
			want: FansGroupMemberInfo{
				GroupId:         groupId,
				AnchorUid:       anchorUid,
				FansUid:         fansUid,
				LoveValue:       uint32(loveValue),
				ValueUpdateTime: nowTm.UTC(),
				AddGroupTime:    addGroupTm.UTC(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetFansInfo(tt.args.uid, tt.args.anchorUid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFansInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFansInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_BatchGetFansInfo(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
		fansUids  []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []FansGroupMemberInfo
		wantErr bool
	}{
		{
			name:   "BatchGetFansInfo",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				anchorUid: anchorUid,
				fansUids:  []uint32{fansUid},
			},
			want: []FansGroupMemberInfo{
				{
					GroupId:         groupId,
					AnchorUid:       anchorUid,
					FansUid:         fansUid,
					LoveValue:       uint32(loveValue),
					ValueUpdateTime: nowTm.UTC(),
					AddGroupTime:    addGroupTm.UTC(),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.BatchGetFansInfo(tt.args.anchorUid, tt.args.fansUids)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetFansInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchGetFansInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_BatchGetFansInfoByAnchorUids(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		fansUid    uint32
		anchorUids []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []FansGroupMemberInfo
		wantErr bool
	}{
		{
			name:   "BatchGetFansInfoByAnchorUids",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				fansUid:    fansUid,
				anchorUids: []uint32{anchorUid},
			},
			want: []FansGroupMemberInfo{
				{
					GroupId:         groupId,
					AnchorUid:       anchorUid,
					FansUid:         fansUid,
					LoveValue:       uint32(loveValue),
					ValueUpdateTime: nowTm.UTC(),
					AddGroupTime:    addGroupTm.UTC(),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.BatchGetFansInfoByAnchorUids(tt.args.fansUid, tt.args.anchorUids)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetFansInfoByAnchorUids() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchGetFansInfoByAnchorUids() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetFansCntByPeriod(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
		beginTs   uint32
		endTs     uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name:   "GetFansCntByPeriod",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				anchorUid: anchorUid,
				beginTs:   uint32(addGroupTm.Unix()),
				endTs:     uint32(addGroupTm.Unix()),
			},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetFansCntByPeriod(tt.args.anchorUid, tt.args.beginTs, tt.args.endTs)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFansCntByPeriod() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetFansCntByPeriod() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetFansAllAnchorsInfo(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		fansUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []FansGroupMemberInfo
		wantErr bool
	}{
		{
			name:   "GetFansAllAnchorsInfo",
			fields: fields{infoSession: mDao.infoSession},
			args:   args{fansUid: fansUid},
			want: []FansGroupMemberInfo{
				{
					GroupId:         groupId,
					AnchorUid:       anchorUid,
					FansUid:         fansUid,
					LoveValue:       uint32(loveValue),
					ValueUpdateTime: nowTm.UTC(),
					AddGroupTime:    addGroupTm.UTC(),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetFansAllAnchorsInfo(tt.args.fansUid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFansAllAnchorsInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFansAllAnchorsInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_PlateIdIncr(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	tests := []struct {
		name    string
		fields  fields
		want    uint32
		wantErr bool
	}{
		{
			name:    "PlateIdIncr",
			fields:  fields{infoSession: mDao.infoSession},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.PlateIdIncr()
			if (err != nil) != tt.wantErr {
				t.Errorf("PlateIdIncr() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("PlateIdIncr() got = %v, want %v", got, tt.want)
			}
		})
	}
}

var DefaultNamePlateInfo = PlateConfigInfos{
	PlateName:     "默认铭牌1",
	NameFontColor: "#FFFFFF",
	PlateConfigList: []PlateConfig{
		{
			LevelFontColor: "#FFFFFF",
			MinLevel:       1,
			MaxLevel:       4,
			PlateImgList: []PlateImgConfig{
				{
					ImgUrl:         "https://ga-album-cdnqn.52tt.com/fansgroup/first_level1_1.png",
					NameFontCntMin: 1,
					NameFontCntMax: 3,
				},
				{
					ImgUrl:         "https://ga-album-cdnqn.52tt.com/fansgroup/first_level1_2.png",
					NameFontCntMin: 4,
					NameFontCntMax: 6,
				},
				{
					ImgUrl:         "https://ga-album-cdnqn.52tt.com/fansgroup/first_level1_3.png",
					NameFontCntMin: 7,
					NameFontCntMax: 0,
				},
			},
		},
		{
			LevelFontColor: "#FFFFFF",
			MinLevel:       5,
			MaxLevel:       9,
			PlateImgList: []PlateImgConfig{
				{
					ImgUrl:         "https://ga-album-cdnqn.52tt.com/fansgroup/second_level1_1.png",
					NameFontCntMin: 1,
					NameFontCntMax: 3,
				},
				{
					ImgUrl:         "https://ga-album-cdnqn.52tt.com/fansgroup/second_level1_2.png",
					NameFontCntMin: 4,
					NameFontCntMax: 6,
				},
				{
					ImgUrl:         "https://ga-album-cdnqn.52tt.com/fansgroup/second_level1_3.png",
					NameFontCntMin: 7,
					NameFontCntMax: 0,
				},
			},
		},
		{
			LevelFontColor: "#E36402",
			MinLevel:       10,
			MaxLevel:       15,
			PlateImgList: []PlateImgConfig{
				{
					ImgUrl:         "https://ga-album-cdnqn.52tt.com/fansgroup/third_level1_1.png",
					NameFontCntMin: 1,
					NameFontCntMax: 3,
				},
				{
					ImgUrl:         "https://ga-album-cdnqn.52tt.com/fansgroup/third_level1_2.png",
					NameFontCntMin: 4,
					NameFontCntMax: 6,
				},
				{
					ImgUrl:         "https://ga-album-cdnqn.52tt.com/fansgroup/third_level1_3.png",
					NameFontCntMin: 7,
					NameFontCntMax: 0,
				},
			},
		},
		{
			LevelFontColor: "#E36402",
			MinLevel:       16,
			MaxLevel:       0,
			PlateImgList: []PlateImgConfig{
				{
					ImgUrl:         "https://ga-album-cdnqn.52tt.com/fansgroup/forth_level1_1.png",
					NameFontCntMin: 1,
					NameFontCntMax: 3,
				},
				{
					ImgUrl:         "https://ga-album-cdnqn.52tt.com/fansgroup/forth_level1_2.png",
					NameFontCntMin: 4,
					NameFontCntMax: 6,
				},
				{
					ImgUrl:         "https://ga-album-cdnqn.52tt.com/fansgroup/forth_level1_3.png",
					NameFontCntMin: 7,
					NameFontCntMax: 0,
				},
			},
		},
	},
	PlateDecorationList: []PlateDecoration{
		{
			ImgUrl:         "https://ga-album-cdnqn.52tt.com/fansgroup/decoration3_1.png",
			NameFontCntMin: 1,
			NameFontCntMax: 3,
		},
		{
			ImgUrl:         "https://ga-album-cdnqn.52tt.com/fansgroup/decoration3_2.png",
			NameFontCntMin: 4,
			NameFontCntMax: 6,
		},
		{
			ImgUrl:         "https://ga-album-cdnqn.52tt.com/fansgroup/decoration3_3.png",
			NameFontCntMin: 7,
			NameFontCntMax: 0,
		},
	},
}

func TestMongoDao_AddPlateConfig(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		plateId             uint32
		plateName           string
		nameFontColor       string
		plateConfigList     []PlateConfig
		plateDecorationList []PlateDecoration
		updateTs            int64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "AddPlateConfig",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				plateId:             plateId,
				plateName:           DefaultNamePlateInfo.PlateName,
				nameFontColor:       DefaultNamePlateInfo.NameFontColor,
				plateConfigList:     DefaultNamePlateInfo.PlateConfigList,
				plateDecorationList: DefaultNamePlateInfo.PlateDecorationList,
				updateTs:            nowTm.Unix(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			if err := m.AddPlateConfig(tt.args.plateId, tt.args.plateName, tt.args.nameFontColor, tt.args.plateConfigList, tt.args.plateDecorationList, tt.args.updateTs); (err != nil) != tt.wantErr {
				t.Errorf("AddPlateConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMongoDao_UpdatePlateConfig(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		plateId             uint32
		plateName           string
		nameFontColor       string
		plateConfigList     []PlateConfig
		plateDecorationList []PlateDecoration
		updateTs            int64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "UpdatePlateConfig",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				plateId:             plateId,
				plateName:           DefaultNamePlateInfo.PlateName,
				nameFontColor:       DefaultNamePlateInfo.NameFontColor,
				plateConfigList:     DefaultNamePlateInfo.PlateConfigList,
				plateDecorationList: DefaultNamePlateInfo.PlateDecorationList,
				updateTs:            nowTm.Unix(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			if err := m.UpdatePlateConfig(tt.args.plateId, tt.args.plateName, tt.args.nameFontColor, tt.args.plateConfigList, tt.args.plateDecorationList, tt.args.updateTs); (err != nil) != tt.wantErr {
				t.Errorf("UpdatePlateConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMongoDao_GetPlateConfigCount(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		plateId   uint32
		plateName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name:   "GetPlateConfigCount",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				plateId:   plateId,
				plateName: "",
			},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetPlateConfigCount(tt.args.plateId, tt.args.plateName)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPlateConfigCount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetPlateConfigCount() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetPlateConfigList(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		plateId   uint32
		plateName string
		limit     int
		offset    int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []PlateConfigInfos
		wantErr bool
	}{
		{
			name:   "GetPlateConfigList",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				plateId:   plateId,
				plateName: "",
				limit:     1,
				offset:    0,
			},
			want: []PlateConfigInfos{
				{
					PlateId:             plateId,
					PlateName:           DefaultNamePlateInfo.PlateName,
					NameFontColor:       DefaultNamePlateInfo.NameFontColor,
					PlateConfigList:     DefaultNamePlateInfo.PlateConfigList,
					PlateDecorationList: DefaultNamePlateInfo.PlateDecorationList,
					UpdateTs:            nowTm.Unix(),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetPlateConfigList(tt.args.plateId, tt.args.plateName, tt.args.limit, tt.args.offset)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPlateConfigList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPlateConfigList() got = %v, want %v", got, tt.want)
			}
		})
	}

}

func TestMongoDao_GetPlateConfigById(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		plateId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    PlateConfigInfos
		wantErr bool
	}{
		{
			name:   "GetPlateConfigById",
			fields: fields{infoSession: mDao.infoSession},
			args:   args{plateId: plateId},
			want: PlateConfigInfos{
				PlateId:             plateId,
				PlateName:           DefaultNamePlateInfo.PlateName,
				NameFontColor:       DefaultNamePlateInfo.NameFontColor,
				PlateConfigList:     DefaultNamePlateInfo.PlateConfigList,
				PlateDecorationList: DefaultNamePlateInfo.PlateDecorationList,
				UpdateTs:            nowTm.Unix(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetPlateConfigById(tt.args.plateId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPlateConfigById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPlateConfigById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetPlateConfigByIds(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		plateIds []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []PlateConfigInfos
		wantErr bool
	}{
		{
			name:   "GetPlateConfigByIds",
			fields: fields{infoSession: mDao.infoSession},
			args:   args{plateIds: []uint32{plateId}},
			want: []PlateConfigInfos{
				{
					PlateId:             plateId,
					PlateName:           DefaultNamePlateInfo.PlateName,
					NameFontColor:       DefaultNamePlateInfo.NameFontColor,
					PlateConfigList:     DefaultNamePlateInfo.PlateConfigList,
					PlateDecorationList: DefaultNamePlateInfo.PlateDecorationList,
					UpdateTs:            nowTm.Unix(),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetPlateConfigByIds(tt.args.plateIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPlateConfigByIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPlateConfigByIds() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_DelPlateConfig(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		plateId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "DelPlateConfig",
			fields:  fields{infoSession: mDao.infoSession},
			args:    args{plateId: plateId},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			if err := m.DelPlateConfig(tt.args.plateId); (err != nil) != tt.wantErr {
				t.Errorf("DelPlateConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMongoDao_GrantedIdIncr(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	tests := []struct {
		name    string
		fields  fields
		want    uint32
		wantErr bool
	}{
		{
			name:    "GrantedIdIncr",
			fields:  fields{infoSession: mDao.infoSession},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GrantedIdIncr()
			if (err != nil) != tt.wantErr {
				t.Errorf("GrantedIdIncr() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GrantedIdIncr() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_AddAnchorPlate(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		grantedId uint32
		plateId   uint32
		anchorUid uint32
		beginTs   int64
		endTs     int64
		updateTs  int64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "AddAnchorPlate",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				grantedId: grantId,
				plateId:   plateId,
				anchorUid: anchorUid,
				beginTs:   beginTs,
				endTs:     endTs,
				updateTs:  updateTs,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			if err := m.AddAnchorPlate(tt.args.grantedId, tt.args.plateId, tt.args.anchorUid, tt.args.beginTs, tt.args.endTs, tt.args.updateTs); (err != nil) != tt.wantErr {
				t.Errorf("AddAnchorPlate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMongoDao_UpdateAnchorPlate(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		grantedId uint32
		plateId   uint32
		anchorUid uint32
		beginTs   int64
		endTs     int64
		updateTs  int64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "UpdateAnchorPlate",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				grantedId: grantId,
				plateId:   plateId,
				anchorUid: anchorUid,
				beginTs:   beginTs,
				endTs:     endTs,
				updateTs:  updateTs,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			if err := m.UpdateAnchorPlate(tt.args.grantedId, tt.args.plateId, tt.args.anchorUid, tt.args.beginTs, tt.args.endTs, tt.args.updateTs); (err != nil) != tt.wantErr {
				t.Errorf("UpdateAnchorPlate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMongoDao_GetAnchorPlateByGrantId(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		grantId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    AnchorPlateInfo
		wantErr bool
	}{
		{
			name:   "GetAnchorPlateByGrantId",
			fields: fields{infoSession: mDao.infoSession},
			args:   args{grantId: grantId},
			want: AnchorPlateInfo{
				GrantedId: grantId,
				PlateId:   plateId,
				AnchorUid: anchorUid,
				BeginTs:   beginTs,
				EndTs:     endTs,
				UpdateTs:  updateTs,
				IsWear:    false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetAnchorPlateByGrantId(tt.args.grantId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAnchorPlateByGrantId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAnchorPlateByGrantId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetGrantedPlateList(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
		plateId   uint32
		limit     int
		offset    int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []AnchorPlateInfo
		wantErr bool
	}{
		{
			name:   "GetGrantedPlateList",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				anchorUid: anchorUid,
				plateId:   0,
				limit:     1,
				offset:    0,
			},
			want: []AnchorPlateInfo{
				{
					GrantedId: grantId,
					PlateId:   plateId,
					AnchorUid: anchorUid,
					BeginTs:   beginTs,
					EndTs:     endTs,
					UpdateTs:  updateTs,
					IsWear:    false,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetGrantedPlateList(tt.args.anchorUid, tt.args.plateId, tt.args.limit, tt.args.offset)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGrantedPlateList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetGrantedPlateList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetGrantedPlateCount(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
		plateId   uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name:   "GetGrantedPlateCount",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				anchorUid: anchorUid,
				plateId:   0,
			},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetGrantedPlateCount(tt.args.anchorUid, tt.args.plateId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGrantedPlateCount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetGrantedPlateCount() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetAnchorValidPlateConfig(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    AnchorPlateInfo
		wantErr bool
	}{
		{
			name:   "GetAnchorValidPlateConfig",
			fields: fields{infoSession: mDao.infoSession},
			args:   args{anchorUid: anchorUid},
			want: AnchorPlateInfo{
				GrantedId: grantId,
				PlateId:   plateId,
				AnchorUid: anchorUid,
				BeginTs:   beginTs,
				EndTs:     endTs,
				UpdateTs:  updateTs,
				IsWear:    false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetAnchorValidPlateConfig(tt.args.anchorUid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAnchorValidPlateConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAnchorValidPlateConfig() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetAnchorAllValidPlate(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []AnchorPlateInfo
		wantErr bool
	}{
		{
			name:   "GetAnchorAllValidPlate",
			fields: fields{infoSession: mDao.infoSession},
			args:   args{anchorUid: anchorUid},
			want: []AnchorPlateInfo{
				{
					GrantedId: grantId,
					PlateId:   plateId,
					AnchorUid: anchorUid,
					BeginTs:   beginTs,
					EndTs:     endTs,
					UpdateTs:  updateTs,
					IsWear:    false,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetAnchorAllValidPlate(tt.args.anchorUid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAnchorAllValidPlate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAnchorAllValidPlate() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_BatGetAnchorAllValidPlate(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorList []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []AnchorPlateInfo
		wantErr bool
	}{
		{
			name:   "BatGetAnchorAllValidPlate",
			fields: fields{infoSession: mDao.infoSession},
			args:   args{anchorList: []uint32{anchorUid}},
			want: []AnchorPlateInfo{
				{
					GrantedId: grantId,
					PlateId:   plateId,
					AnchorUid: anchorUid,
					BeginTs:   beginTs,
					EndTs:     endTs,
					UpdateTs:  updateTs,
					IsWear:    false,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.BatGetAnchorAllValidPlate(tt.args.anchorList)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatGetAnchorAllValidPlate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatGetAnchorAllValidPlate() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_WearAnchorPlate(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
		grantedId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "WearAnchorPlate",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				anchorUid: anchorUid,
				grantedId: grantId,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			if err := m.WearAnchorPlate(tt.args.anchorUid, tt.args.grantedId); (err != nil) != tt.wantErr {
				t.Errorf("WearAnchorPlate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMongoDao_DeleteAnchorWearPlate(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "DeleteAnchorWearPlate",
			fields:  fields{infoSession: mDao.infoSession},
			args:    args{anchorUid: anchorUid},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			if err := m.DeleteAnchorWearPlate(tt.args.anchorUid); (err != nil) != tt.wantErr {
				t.Errorf("DeleteAnchorWearPlate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMongoDao_GetAnchorValidPlateConfigById(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
		plateId   uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []AnchorPlateInfo
		wantErr bool
	}{
		{
			name:   "GetAnchorValidPlateConfigById",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				anchorUid: anchorUid,
				plateId:   plateId,
			},
			want: []AnchorPlateInfo{
				{
					GrantedId: grantId,
					PlateId:   plateId,
					AnchorUid: anchorUid,
					BeginTs:   beginTs,
					EndTs:     endTs,
					UpdateTs:  updateTs,
					IsWear:    false,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetAnchorValidPlateConfigById(tt.args.anchorUid, tt.args.plateId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAnchorValidPlateConfigById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAnchorValidPlateConfigById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_DelAnchorPlate(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		grantedId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "DelAnchorPlate",
			fields:  fields{infoSession: mDao.infoSession},
			args:    args{grantedId: grantId},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			if err := m.DelAnchorPlate(tt.args.grantedId); (err != nil) != tt.wantErr {
				t.Errorf("DelAnchorPlate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMongoDao_DelAnchorPlateByPlateId(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		plateId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "DelAnchorPlateByPlateId",
			fields:  fields{infoSession: mDao.infoSession},
			args:    args{plateId: plateId},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			if err := m.DelAnchorPlateByPlateId(tt.args.plateId); (err != nil) != tt.wantErr {
				t.Errorf("DelAnchorPlateByPlateId() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMongoDao_AddGroupNameVerifyInfo(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
		groupName string
		summitTs  int64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "AddGroupNameVerifyInfo",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				anchorUid: anchorUid,
				groupName: groupName,
				summitTs:  summitTs,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			if err := m.AddGroupNameVerifyInfo(tt.args.anchorUid, tt.args.groupName, tt.args.summitTs); (err != nil) != tt.wantErr {
				t.Errorf("AddGroupNameVerifyInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMongoDao_GetNeedVerifyGroupNameList(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		limit  int
		offset int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []GroupNameVerifyInfo
		wantErr bool
	}{
		{
			name:   "GetNeedVerifyGroupNameList",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				limit:  1,
				offset: 0,
			},
			want: []GroupNameVerifyInfo{
				GroupNameVerifyInfo{
					AnchorUid: anchorUid,
					GroupName: groupName,
					SummitTs:  summitTs,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetNeedVerifyGroupNameList(tt.args.limit, tt.args.offset)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNeedVerifyGroupNameList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetNeedVerifyGroupNameList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetNeedVerifyGroupNameByUid(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    GroupNameVerifyInfo
		wantErr bool
	}{
		{
			name:   "GetNeedVerifyGroupNameByUid",
			fields: fields{infoSession: mDao.infoSession},
			args:   args{uid: anchorUid},
			want: GroupNameVerifyInfo{
				AnchorUid: anchorUid,
				GroupName: groupName,
				SummitTs:  summitTs,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetNeedVerifyGroupNameByUid(tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNeedVerifyGroupNameByUid() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetNeedVerifyGroupNameByUid() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetNeedVerifyGroupNameCount(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	tests := []struct {
		name    string
		fields  fields
		want    uint32
		wantErr bool
	}{
		{
			name:    "GetNeedVerifyGroupNameCount",
			fields:  fields{infoSession: mDao.infoSession},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetNeedVerifyGroupNameCount()
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNeedVerifyGroupNameCount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetNeedVerifyGroupNameCount() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_DelNeedVerifyGroupName(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
		groupName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "DelNeedVerifyGroupName",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				anchorUid: anchorUid,
				groupName: groupName,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			if err := m.DelNeedVerifyGroupName(tt.args.anchorUid, tt.args.groupName); (err != nil) != tt.wantErr {
				t.Errorf("DelNeedVerifyGroupName() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMongoDao_CheckAnchorGroupNameIsVerify(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name:    "CheckAnchorGroupNameIsVerify",
			fields:  fields{infoSession: mDao.infoSession},
			args:    args{uid: anchorUid},
			want:    false,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.CheckAnchorGroupNameIsVerify(tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckAnchorGroupNameIsVerify() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("CheckAnchorGroupNameIsVerify() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_CheckGroupNameIsVerify(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		groupName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name:    "CheckGroupNameIsVerify",
			fields:  fields{infoSession: mDao.infoSession},
			args:    args{groupName: groupName},
			want:    false,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.CheckGroupNameIsVerify(tt.args.groupName)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckGroupNameIsVerify() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("CheckGroupNameIsVerify() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_AddGroupNameVerifyRecord(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		verifyRecord *GroupNameVerifyRecord
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "AddGroupNameVerifyRecord",
			fields:  fields{infoSession: mDao.infoSession},
			args:    args{verifyRecord: &verifyRecord},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			if err := m.AddGroupNameVerifyRecord(tt.args.verifyRecord); (err != nil) != tt.wantErr {
				t.Errorf("AddGroupNameVerifyRecord() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMongoDao_GetGroupNameVerifyRecordList(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		limit     int
		offset    int
		anchorUid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []GroupNameVerifyRecord
		wantErr bool
	}{
		{
			name:   "GetGroupNameVerifyRecordList",
			fields: fields{infoSession: mDao.infoSession},
			args: args{
				limit:  1,
				offset: 0,
			},
			want: []GroupNameVerifyRecord{
				verifyRecord,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetGroupNameVerifyRecordList(tt.args.anchorUid, tt.args.limit, tt.args.offset)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGroupNameVerifyRecordList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetGroupNameVerifyRecordList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_GetGroupNameVerifyRecordCount(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorUid uint32
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "GetGroupNameVerifyRecordCount",
			fields: fields{
				infoSession: mDao.infoSession,
			},
			args:    args{anchorUid: 0},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			got, err := m.GetGroupNameVerifyRecordCount(tt.args.anchorUid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGroupNameVerifyRecordCount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetGroupNameVerifyRecordCount() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMongoDao_BatDelNeedVerifyGroupName(t *testing.T) {
	type fields struct {
		infoSession *mgo.Session
		dbName      string
	}
	type args struct {
		anchorList []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "BatDelNeedVerifyGroupName",
			fields:  fields{infoSession: mDao.infoSession},
			args:    args{anchorList: []uint32{anchorUid}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MongoDao{
				infoSession: tt.fields.infoSession,
			}
			if err := m.BatDelNeedVerifyGroupName(tt.args.anchorList); (err != nil) != tt.wantErr {
				t.Errorf("BatDelNeedVerifyGroupName() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
*/
