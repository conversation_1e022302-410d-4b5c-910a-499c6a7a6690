package mgr

import (
    "context"
    "crypto/md5" //#nosec
    "encoding/hex"
    "fmt"
    sms_go "golang.52tt.com/protocol/services/sms-go"
    "io"
    "sort"
    "strconv"
    "strings"
    "time"

    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    "golang.52tt.com/pkg/foundation/utils"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/metrics"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    imPB "golang.52tt.com/protocol/app/im"
    logicPB "golang.52tt.com/protocol/app/user_recall"
    "golang.52tt.com/protocol/common/status"
    imApiPB "golang.52tt.com/protocol/services/im-api"
    smsPB "golang.52tt.com/protocol/services/smssvr"
    pb "golang.52tt.com/protocol/services/user-recall"
    user_recall_award "golang.52tt.com/protocol/services/user-recall-award"
    "golang.52tt.com/services/user-recall/internal/model"
    "golang.52tt.com/services/user-recall/util"
)

// 获取待召回好友列表
func (mgr *UserRecallManager) GetUserRecallList(ctx context.Context, req *pb.GetUserRecallListReq) (*pb.GetUserRecallListResp, error) {
    out := &pb.GetUserRecallListResp{}
    si, _ := protogrpc.ServiceInfoFromContext(ctx)
    opUid := req.Uid

    defer func() {
        log.InfoWithCtx(ctx, "GetUserRecallList si=%s uid=%d out.size=%d, list=%s", si.String(), opUid, len(out.List), out.String())
    }()

    out.GiftVal = mgr.dyconfig.GetMaxGiftVal()
    out.JumpUrl = mgr.dyconfig.GetInviterWebUrl()

    lossUidList, err := mgr.getValidRecallList(ctx, opUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserRecallList getValidRecallList fail %v, uid=%d", err, opUid)
        return out, err
    }
    if len(lossUidList) == 0 {
        return out, nil
    }

    defer metrics.DISCOVERY_FUNC_TRACK(protocol.NewServerError(0, "GetUserRecallList.has")).End()

    uid2awardMap, err := mgr.getInviteAwardInfo(ctx, opUid, lossUidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserRecallList getInviteAwardInfo fail %v, uid=%d", err, opUid)
        return out, err
    }

    uids := make([]uint32, 0, len(lossUidList)+1)
    lastLoginTime := make([]uint32, 0, len(lossUidList)+1)
    for _, info := range lossUidList {
        uids = append(uids, info.Uid)
        lastLoginTime = append(lastLoginTime, info.LstLoginDate)
    }
    uids = append(uids, opUid)
    userMap, err := mgr.AccountCli.GetUsersMap(ctx, uids)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserRecallList GetUsersMap fail %v, opuid=%d", err, opUid)
        return out, err
    }
    log.InfoWithCtx(ctx, "GetUserRecallList uids.size=%d map.size=%d", len(uids), len(userMap))

    recallStatusMap, err := mgr.userRecallAwardClient.BatchCheckRecallStatusWithLastLoginTime(ctx,
        &user_recall_award.BatchCheckRecallStatusWithLastLoginTimeReq{
        RecalledUidList: uids,
        LastLoginTime: lastLoginTime,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserRecallList userRecallAwardClient.BatchCheckRecallStatus err=%v", err)
        return out, err
    }

    log.InfoWithCtx(ctx, "GetUserRecallList uids.size=%d BatchCheckRecallStatus map.size=%d", len(uids), len(recallStatusMap.GetRecallStatus()))

    opUser := userMap[opUid]
    for _, info := range lossUidList {
        lossUid := info.Uid
        lossUserInfo := userMap[lossUid]

        //recallStats, err := mgr.userRecallAwardClient.CheckRecallStatus(ctx, &user_recall_award.CheckRecallStatusReq{RecalledUid: lossUid})
        //if err != nil {
        //    log.ErrorWithCtx(ctx, "GetUserRecallList CheckRecallStatus fail %v, opUid=%d", err, opUid)
        //    return out, err
        //}

        recallStatus := recallStatusMap.GetRecallStatus()[lossUid]
        log.InfoWithCtx(ctx, "GetUserRecallList lossUid=%d, recallStatus=%d", lossUid, recallStatus)

        bind := info.Status == uint32(logicPB.UserRecallStatus_USER_RECALL_STATUS_CALLED)
        log.InfoWithCtx(ctx, "GetUserRecallList lossUid=%d recallStats=%d uid2bind=%v opuid=%d", lossUid, recallStatus, bind, opUid)

        // 绑定前自己回归了
        if recallStatus != uint32(user_recall_award.RecallStatus_RECALL_STATUS_UNKNOWN) && !bind {
            log.InfoWithCtx(ctx, "GetUserRecallList ignore self login. opUid=%d lossUid=%d recallStats=%d", opUid, lossUid, recallStatus)
            continue
        }

        // 数据部接口数据不更新
        // 过滤掉已经回归并领奖的
        // select * from user_recall_bind where uid=? and last_login_time=? and status=1;
        binded, err := mgr.getUserBindedInfo(ctx, lossUid, info.LstLoginDate)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetUserRecallList getUserBindedInfo fail %v, opUid=%d lossUid=%d", err, opUid, lossUid)
            return out, err
        }
        if binded {
            log.InfoWithCtx(ctx, "GetUserRecallList ignore binded. opUid=%d lossUid=%d LstLoginDate=%d", opUid, lossUid, info.LstLoginDate)
            continue
        }

        awardInfo := uid2awardMap[lossUid]

        sign := util.AESEncrypt([]byte(fmt.Sprintf("%d_%d", opUser.GetUid(), lossUserInfo.GetUid())))
        inviteUrl := fmt.Sprintf("%s?sign=%s&market_id=%d", mgr.dyconfig.GetInviteUrl(), sign, si.MarketID)
        linkResp, err := mgr.GetShortLink(ctx, &pb.GetShortLinkReq{Url: inviteUrl})
        if err != nil {
            log.ErrorWithCtx(ctx, "GetUserRecallList GetShortLink fail %v, opUid=%d, lossUid=%d", err, opUid, lossUid)
            return out, err
        }
        log.DebugWithCtx(ctx, "GetUserRecallList GetShortLink %d %d %q->%q", opUser.GetUid(), lossUserInfo.GetUid(), inviteUrl, linkResp.GetShortLink())

        recallInfo := &pb.RecallUserInfo{
            UserInfo: &pb.UserProfile{
                Uid:          lossUid,
                Account:      lossUserInfo.GetUsername(),
                Nickname:     lossUserInfo.GetNickname(),
                AccountAlias: lossUserInfo.GetAlias(),
                Sex:          uint32(lossUserInfo.GetSex()),
            },
            Status:        uint32(logicPB.UserRecallStatus_USER_RECALL_STATUS_WAIT),
            GiftName:      awardInfo.GetGiftName(),
            GiftIconUrl:   awardInfo.GetGiftIcon(),
            GiftCnt:       awardInfo.GetGiftCnt(),
            GiftVal:       awardInfo.GetGiftVal(),
            LastLoginTs:   info.LstLoginDate,
            CreateTime:    info.CreateTime,
            LastLoginTime: info.LstLoginDate,
            AccConsume:    uint32(info.ConsumeTbeanAmtCnt),
            InviteUrl:     fmt.Sprintf("https://%s/b/%s", mgr.dyconfig.GetShortLinkHost(), linkResp.GetShortLink()), // https://r-test.52tt.com/b/R7Brmu
        }
        if bind {
            recallInfo.Status = uint32(logicPB.UserRecallStatus_USER_RECALL_STATUS_CALLED)
        }

        if recallStatus == uint32(user_recall_award.RecallStatus_RECALL_STATUS_UNKNOWN) {
            recallInfo.Status = uint32(logicPB.UserRecallStatus_USER_RECALL_STATUS_WAIT)
        }

        out.List = append(out.List, recallInfo)
    }

    if len(out.GetList()) == 0 {
        return out, nil
    }

    sort.Slice(out.List, func(i, j int) bool {
        if out.List[i].Status == out.List[j].Status {
            return out.List[i].CreateTime > out.List[j].CreateTime
        }
        return out.List[i].Status > out.List[j].Status
    })

    InviteTitle, InviteContent, InviteImgUrl := mgr.dyconfig.GetInviteConf()
    out.InviteTitle = fmt.Sprintf(InviteTitle, opUser.GetNickname())
    out.InviteContent = fmt.Sprintf(InviteContent, mgr.dyconfig.GetMarktId2TbeanName(si.MarketID))
    out.InviteImgUrl = InviteImgUrl

    go func() {
        if len(out.GetList()) == 0 {
            return
        }

        if si.ClientVersion == 0 || si.TerminalType == 0 {
            log.InfoWithCtx(ctx, "GetUserRecallList push_im ignore. no app. si=%s opuid=%d", si.String(), opUid)
            return
        }

        // 每次只给邀请方返回3个可召回用户，剩余的可召回用户在后续的每5次切换消息tab请求陆续返回，每次返回数量<=3个。切换次数按天重置。
        ctx := context.Background()
        cnt := uint32(0)

        feq, err := mgr.rc.IncrImPushRecord(opUid)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetUserRecallList IncrImPushRecord fail %v opUid=%d si=%s", err, opUid, si.String())
            return
        }
        log.InfoWithCtx(ctx, "GetUserRecallList IncrImPushRecord=%d opUid=%d %s", feq, opUid, si.String())
        if !(feq == 1 || feq%5 == 0) {
            //log.InfoWithCtx(ctx, "GetUserRecallList IncrImPushRecord=%d ignore. opUid=%d %s", feq, si.String())
            return
        }

        log.InfoWithCtx(ctx, "GetUserRecallList push_im opUid=%d total=%d %s", opUid, len(out.List), si.String())
        for _, info := range out.List {
            uid := info.GetUserInfo().GetUid()
            row, err := mgr.store.InsertUserRecallImRecord(ctx, opUid, uid, int64(info.LastLoginTs))
            if err != nil {
                log.Errorf("GetUserRecallList InsertUserRecallImRecord fail %v,opUid=%d uid=%d", err, opUid, uid)
                continue
            }
            if row == 1 && cnt < 3 { // 每次请求推im上限3个
                mgr.PushImMsg(ctx, opUid, uid)
                log.InfoWithCtx(ctx, "GetUserRecallList push_im PushImMsg uid=%d opUid=%d cnt=%d %s", uid, opUid, cnt, si.String())
                cnt++
            }
            if cnt == 3 {
                break
            }
        }
    }()
    return out, nil
}

func (mgr *UserRecallManager) getValidRecallList(ctx context.Context, opUid uint32) ([]*pb.LossUserInfo, error) {

    // 所有可召回流失用户
    lossUidList, err := mgr.getLossUidListFromCache(ctx, opUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserRecallList GetLossUidList fail %v, uid=%d", err, opUid)
        return nil, err
    }
    log.InfoWithCtx(ctx, "GetUserRecallList opUid=%d cache.GetLossUidList.size=%d list=%+v", opUid, len(lossUidList), lossUidList)

    // select uid, last_login_time, invite_uid, status, create_time from user_recall_bind where invite_uid=? and status=0 order by create_time des
    // 已经绑定关系，邀请方还未领奖的流失用户
    bindUnfinishedList, err := mgr.getBindUnfinishedList(ctx, opUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserRecallList getBindUnfinishedList fail %v, uid=%d", err, opUid)
        return nil, err
    }

    lossUidList = mgr.mergeRecallList(bindUnfinishedList, lossUidList)

    if len(lossUidList) == 0 {
        return nil, nil
    }

    newLossUserList := []*pb.LossUserInfo{}
    for _, lossUser := range lossUidList {
        lossUid := lossUser.Uid
        cheating, err := mgr.CheckIsRecallOnly(ctx, lossUid)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetUserRecallList antiCheating err=%v, opuid=%d, lossUid=%d", err, opUid, lossUid)
            return nil, err
        }
        log.DebugWithCtx(ctx, "GetUserRecallList opUid=%d lossUid=%d CheckIsRecallOnly=%+v lossUser=%+v", opUid, lossUid, cheating, lossUser)

        if cheating.GetLastLoginTime() <= 0 {
            log.ErrorWithCtx(ctx, "GetUserRecallList ignore. CheckIsRecallOnly.antiCheating hit, opuid=%d, lossUid=%d cheating=%+v",
                opUid, lossUid, cheating)
            continue
        }

        if cheating.GetCreateTime() > 0 && lossUser.Status != uint32(uint32(logicPB.UserRecallStatus_USER_RECALL_STATUS_CALLED)) {
            log.ErrorWithCtx(ctx, "GetUserRecallList ignore. no bind and self login, opuid=%d, lossUid=%d cheating=%+v",
                opUid, lossUid, cheating)
            continue
        }

        if mgr.dyconfig.CheckInConsumeRange(uint32(lossUser.ConsumeTbeanAmtCnt)) {
            log.InfoWithCtx(ctx, "GetUserRecallList CheckInConsumeRange inviteUid=%d lossUid=%d", opUid, lossUid)
            continue
        }

        newLossUserList = append(newLossUserList, lossUser)
    }
    return newLossUserList, nil
}

// GetInviteAwardInfo
func (mgr *UserRecallManager) getInviteAwardInfo(ctx context.Context, opUid uint32, lossUserList []*pb.LossUserInfo) (map[uint32]*user_recall_award.AwardInfo, error) {
    awardReq := &user_recall_award.BatchGetInviteAwardInfoReq{}
    for _, info := range lossUserList {
        awardReq.RecalledUser = append(awardReq.RecalledUser, &user_recall_award.RecalledUserInfo{
            RecalledUid: info.Uid,
            ConsumeVal:  uint32(info.ConsumeTbeanAmtCnt),
        })
    }
    awardResp, err := mgr.userRecallAwardClient.BatchGetInviteAwardInfo(ctx, awardReq)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserRecallList BatchGetInviteAwardInfo fail %v, uid=%d, awardReq=%s", err, opUid, awardReq.String())
        return nil, err
    }
    uid2awardMap := map[uint32]*user_recall_award.AwardInfo{}
    for _, info := range awardResp.GetAwardInfos() {
        uid2awardMap[info.RecalledUid] = info.AwardInfo
    }
    if len(uid2awardMap) != len(awardReq.GetRecalledUser()) {
        err := protocol.NewServerError(status.ErrUserRecallParams, "奖励配置错误")
        log.ErrorWithCtx(ctx, "GetUserRecallList BatchGetInviteAwardInfo fail no eq. %v, uid=%d, awardReq=%s awardResp=%s", err, opUid, awardReq.String(), awardResp)
        //return out, err
    }
    log.InfoWithCtx(ctx, "GetUserRecallList BatchGetInviteAwardInfo opUid=%d  awardReq=%s uid2awardMap=%+v", opUid, awardReq.String(), uid2awardMap)
    return uid2awardMap, nil
}

func (mgr *UserRecallManager) getBindUnfinishedList(ctx context.Context, opUid uint32) ([]*pb.LossUserInfo, error) {
    exist, data, err := mgr.rc.GetBindUnfinished(opUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "getBindUnfinished fail %v, uid=%d", err, opUid)
        return nil, err
    }
    if exist {
        info := &pb.LossUserInfoList{}
        err = proto.Unmarshal(data, info)
        if err != nil {
            log.ErrorWithCtx(ctx, "getBindUnfinished Unmarshal fail %v, uid=%d", err, opUid)
            return nil, err
        }
        log.DebugWithCtx(ctx, "getBindUnfinishedList cache opUid=%d list=%+v", opUid, info.List)
        return info.List, nil
    }

    bindList, err := mgr.store.GetUserRecallBindUnfinishedList(ctx, opUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "getBindUnfinished GetUserRecallBindUnfinishedList fail %v, uid=%d", err, opUid)
        return nil, err
    }
    log.InfoWithCtx(ctx, "getBindUnfinished opUid=%d GetUserRecallBindUnfinishedList=%s", opUid, utils.ToJson(bindList))

    list := []*pb.LossUserInfo{}
    for _, info := range bindList {
        lossUid := info.Uid

        accConsume := uint32(0)
        lossInfo, err := mgr.store.GetUserRecalledWhiteListData(ctx, lossUid)
        if err != nil {
            log.ErrorWithCtx(ctx, "getBindUnfinished GetUserRecalledWhiteListData fail %v, lossUid=%d", err, lossUid)
            return nil, err
        }
        accConsume = uint32(lossInfo.AccConsume)
        if accConsume == 0 {
            lossInfo, err := mgr.store.GetUserRecallCoupleInfo(ctx, opUid, lossUid, uint32(info.LastLoginTime))
            if err != nil {
                log.ErrorWithCtx(ctx, "getBindUnfinished GetUserRecallCoupleInfo fail %v, uid=%d", err, opUid)
                return nil, err
            }
            accConsume = uint32(lossInfo.ConsumeTbeanAmtCnt)
        }

        if accConsume == 0 {
            log.ErrorWithCtx(ctx, "getBindUnfinished accConsume=0 lossUid=%d opUid=%d", lossUid, opUid)
            continue
        }

        list = append(list, &pb.LossUserInfo{
            Uid:                lossUid,
            ConsumeTbeanAmtCnt: uint64(accConsume),
            LstLoginDate:       uint32(info.LastLoginTime),
            Status:             uint32(logicPB.UserRecallStatus_USER_RECALL_STATUS_CALLED),
            CreateTime:         uint32(time.Now().Unix()),
        })
    }

    info := &pb.LossUserInfoList{List: list}
    bin, _ := proto.Marshal(info)
    err = mgr.rc.SetBindUnfinished(opUid, bin)
    if err != nil {
        log.ErrorWithCtx(ctx, "getBindUnfinished SetBindUnfinished fail %v, uid=%d", err, opUid)
    }

    log.DebugWithCtx(ctx, "getBindUnfinishedList db opUid=%d list=%+v", opUid, info.List)
    return list, nil
}

// 发起召回
func (mgr *UserRecallManager) SendRecall(ctx context.Context, req *pb.SendRecallReq) (*pb.SendRecallResp, error) {
    out := &pb.SendRecallResp{}
    si, _ := protogrpc.ServiceInfoFromContext(ctx)
    uid, targetUid := req.Uid, req.TargetUid
    recallType := req.RecallType
    log.InfoWithCtx(ctx, "SendRecall begin si=%s marketId=%d req=%+v", si.String(), si.MarketID, req)

    if uid == 0 || targetUid == 0 {
        return out, protocol.NewServerError(status.ErrUserRecallParams)
    }
    if uid == targetUid {
        err := protocol.NewServerError(status.ErrUserRecallParams, "不能邀请自己")
        log.ErrorWithCtx(ctx, "SendRecall invalid param uid=%d req=%+v err=%+v", uid, req, err)
        return out, err
    }
    if uid == 0 || targetUid == 0 ||
        !(recallType >= uint32(logicPB.RecallType_RECALL_TYPE_SMS) && recallType <= uint32(logicPB.RecallType_RECALL_TYPE_QQ)) {
        log.ErrorWithCtx(ctx, "SendRecall invalid param uid=%d req=%+v", uid, req)
        return out, protocol.NewServerError(status.ErrUserRecallParams)
    }

    lastLoginTime := uint32(0)
    lossUidList, err := mgr.getLossUidListFromCache(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendRecall GetLossUidList fail %v, uid=%d", err, uid)
        return out, err
    }
    log.InfoWithCtx(ctx, "SendRecall opUid=%d GetLossUidList.size=%d list=%+v", uid, len(lossUidList), lossUidList)
    for _, info := range lossUidList {
        if info.Uid == targetUid {
            lastLoginTime = info.LstLoginDate
            log.InfoWithCtx(ctx, "SendRecall opUid=%d lossInfo=%+v", uid, info)
            break
        }
    }
    if lastLoginTime == 0 {
        err := protocol.NewServerError(status.ErrUserRecallParams, "不可邀请该用户")
        log.ErrorWithCtx(ctx, "SendRecall fail %v. no find lossUid. uid=%d targetUid=%d si=%s", err, uid, targetUid, si.String())
        return out, err
    }

    // 流失用户防作弊检查
    cheating, err := mgr.CheckIsRecallOnly(ctx, targetUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendRecall antiCheating err=%v, uid=%d, targetUid=%d", err, uid, targetUid)
        return out, err
    }
    if cheating.GetLastLoginTime() <= 0 {
        err := protocol.NewServerError(status.ErrUserRecallParams, "邀请失败")
        log.ErrorWithCtx(ctx, "SendRecall fail %v. antiCheating hit, uid=%d, targetUid=%d cheating=%+v", err, uid, targetUid, cheating)
        return out, err
    }
    if cheating.GetCreateTime() > 0 {
        err := protocol.NewServerError(status.ErrUserRecallNoAllow) // 该用户已回归，不可再召回喔
        log.ErrorWithCtx(ctx, "SendRecall fail. cheating.GetCreateTime() > 0. uid=%d, targetUid=%d cheating=%+v", uid, targetUid, cheating)
        return out, err
    }

    recallStats, err := mgr.userRecallAwardClient.CheckRecallStatus(ctx, &user_recall_award.CheckRecallStatusReq{RecalledUid: targetUid})
    if err != nil {
        log.ErrorWithCtx(ctx, "SendRecall CheckRecallStatus fail %v, uid=%d targetUid=%d", err, uid, targetUid)
        return out, err
    }
    if recallStats.RecallStatus != uint32(user_recall_award.RecallStatus_RECALL_STATUS_UNKNOWN) {
        err := protocol.NewServerError(status.ErrUserRecallNoAllow) // 该用户已回归，不可再召回喔
        log.ErrorWithCtx(ctx, "SendRecall fail. targetUid already login. uid=%d, targetUid=%d recallStats=%+v", uid, targetUid, recallStats)
        return out, err
    }

    if req.RecallType == uint32(logicPB.RecallType_RECALL_TYPE_SMS) {

        err = mgr.sendSms(ctx, uid, targetUid, lastLoginTime)
        if err != nil {
            log.ErrorWithCtx(ctx, "SendRecall sendSms fail %v req %+v", err, req)
            return out, err
        }
    }

    err = mgr.store.InsertUserRecallInviteRecord(ctx, uid, targetUid, lastLoginTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendRecall InsertUserRecallInviteRecord fail %v req %+v", err, req)
        return out, err
    }
    return out, nil
}

func (mgr *UserRecallManager) GetRecallPrize(ctx context.Context, req *pb.GetRecallPrizeReq) (*pb.GetRecallPrizeResp, error) {
    out := &pb.GetRecallPrizeResp{}
    uid := req.Uid
    targetUid := req.TargetUid

    if uid == 0 || targetUid == 0 {
        return out, protocol.NewServerError(status.ErrUserRecallParams)
    }
    if uid == targetUid {
        return out, protocol.NewServerError(status.ErrUserRecallParams)
    }

    bindList, err := mgr.store.GetUserRecallBindUnfinishedList(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetRecallPrize GetUserRecallBindUnfinishedList fail %v, uid=%d", err, uid)
        return out, err
    }
    log.InfoWithCtx(ctx, "GetRecallPrize opUid=%d GetUserRecallBindUnfinishedList=%+v", uid, utils.ToJson(bindList))
    var hasPrize = false
    var lastLoginAt int64
    for _, info := range bindList {
        if info.Uid == targetUid {
            hasPrize = true
            lastLoginAt = info.LastLoginTime
            log.InfoWithCtx(ctx, "GetRecallPrize opUid=%d targetUid=%d bind_info=%+v", uid, targetUid, info)
            break
        }
    }
    if !hasPrize {
        log.ErrorWithCtx(ctx, "GetRecallPrize fail. no bind. opUid=%d targetUid=%d", uid, targetUid)
        return out, protocol.NewServerError(status.ErrUserRecallParams, "没有绑定关系")
    }

    // 邀请方领奖
    _, err = mgr.userRecallAwardClient.GetInviteAward(ctx, &user_recall_award.GetInviteAwardReq{
        InviteUid:     uid,
        RecalledUid:   targetUid,
        LastLoginTime: lastLoginAt,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetRecallPrize GetInviteAward fail %v, uid=%d targetUid=%d lastLoginAt=%d", err, uid, targetUid, lastLoginAt)
        return out, err
    }

    // 更新状态
    err = mgr.store.FinishUserRecallBindStatus(ctx, targetUid, lastLoginAt)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetRecallPrize FinishUserRecallBindStatus fail %v, uid=%d targetUid=%d lastLoginAt=%d", err, uid, targetUid, lastLoginAt)
        return out, err
    }

    err = mgr.rc.DelUserBindedInfo(targetUid, uint32(lastLoginAt))
    if err != nil {
        log.ErrorWithCtx(ctx, "GetRecallPrize DelUserBindedInfo fail %v, uid=%d targetUid=%d lastLoginAt=%d", err, uid, targetUid, lastLoginAt)
    }

    err = mgr.rc.DelBindUnfinished(uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetRecallPrize DelBindUnfinished fail %v, uid=%d targetUid=%d lastLoginAt=%d", err, uid, targetUid, lastLoginAt)
    }

    log.InfoWithCtx(ctx, "GetRecallPrize end %+v", req)
    return out, nil
}

// ==================================================================================================================================
// 发im
func (mgr *UserRecallManager) PushImMsg(ctx context.Context, inviteUid, uid uint32) error {

    // 被召回方 给 邀请方 发消息
    sendReq := &imApiPB.Send1V1ExtMsgReq{
        From: &imApiPB.User{
            Uid: uid,
            //Username: fromUser.GetAccount(),
            //Nickname: fromUser.GetNickname(),
        },
        To: &imApiPB.User{
            Uid: inviteUid,
            //Username: toUser.GetAccount(),
            //Nickname: toUser.GetNickname(),
        },
        Msg: &imApiPB.ExtMsg{
            MsgType: uint32(imPB.IM_MSG_TYPE_USER_RECALL_NOTIFY_MSG),
            //Content: content,
            //Ext:     data,
        },
        Namespace: "USER_RECALL",
    }

    //log.InfoWithCtx(ctx, "PushImMsg sendReq=%+v", sendReq)
    resp, err := mgr.imApiClient.Send1V1ExtMsg(ctx, sendReq)
    log.InfoWithCtx(ctx, "PushImMsg sendReq=%+v resp=%+v", sendReq, resp)
    if err != nil {
        log.ErrorWithCtx(ctx, "PushImMsg Send1V1ExtMsg fail %v, inviteUid=%d, uid=%d", err, inviteUid, uid)
        return err
    }
    return nil
}

// 发送召回短信
func (mgr *UserRecallManager) sendSms(ctx context.Context, uid, targetUid, lastLoginTime uint32) error {
    si, _ := protogrpc.ServiceInfoFromContext(ctx)
    marketId := si.MarketID
    log.Infof("sendSms begin uid=%d targetUid=%d marketId=%d si=%s", uid, targetUid, marketId, si.String())

    userMap, serr := mgr.AccountCli.GetUsersMap(ctx, []uint32{uid, targetUid})
    if serr != nil {
        log.ErrorWithCtx(ctx, "sendSms GetUserByUid fail %v, uid=%d targetUid=%d", serr, uid, targetUid)
        return serr
    }
    if len(userMap) != 2 {
        log.ErrorWithCtx(ctx, "sendSms fail. invalid uid. uid=%d targetUid=%d", uid, targetUid)
        return protocol.NewServerError(status.ErrRequestParamInvalid, "用户不存在")
    }
    phone := userMap[targetUid].GetPhone()
    if phone == "" {
        err := protocol.NewServerError(status.ErrRequestParamInvalid, "对方未绑定手机号,无法发送短信")
        log.ErrorWithCtx(ctx, "sendSms fail. targetUid no bind phone. uid=%d targetUid=%d", uid, targetUid)
        return err
    }

    isNeedSend, err := mgr.rc.CheckIsSendSms(uid, targetUid, lastLoginTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendRecall CheckIsSendSms fail %v uid=%d, targetUid=%d, lastLoginTime=%d", err, uid, targetUid, lastLoginTime)
        return err
    }
    if !isNeedSend {
        log.ErrorWithCtx(ctx, "SendRecall fail. already send sms. uid=%d, targetUid=%d lastLoginTime=%d", uid, targetUid, lastLoginTime)
        return protocol.NewServerError(status.ErrUserRecallSmsSend) // 已经发过短信啦，试试其他方式吧~
    }

    // 查好友备注,
    // friendInfo, serr := mgr.UgcfriendshipCli.GetOneFriendInfo(ctx, targetUid, uid) // 查对方给我的备注
    // if serr != nil {
    // 	log.ErrorWithCtx(ctx, "sendSms GetOneFriendInfo fail %v, uid=%d targetUid=%d", serr, uid, targetUid)
    // 	return serr
    // }
    // log.InfoWithCtx(ctx, "sendSms uid=%d targetUid=%d GetOneFriendInfo.remark=%s", uid, targetUid, friendInfo.GetRemark())
    nickname := userMap[uid].GetNickname()
    // if friendInfo.GetRemark() != "" {
    // 	nickname = friendInfo.GetRemark()
    // }

    opUser := userMap[uid]
    user := userMap[targetUid]
    sign := util.AESEncrypt([]byte(fmt.Sprintf("%d_%d", opUser.GetUid(), user.GetUid())))
    jumpUrl := fmt.Sprintf("%s?&sign=%s&market_id=%d", mgr.dyconfig.GetInviteUrl(), sign, marketId)
    linkResp, err := mgr.GetShortLink(ctx, &pb.GetShortLinkReq{Url: jumpUrl})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserRecallList GetShortLink fail %v, opUid=%d, lossUid=%d", err, uid, targetUid)
        return err
    }
    log.DebugWithCtx(ctx, "GetUserRecallList GetShortLink %q->%q", jumpUrl, linkResp.GetShortLink())
    shortLinkUrl := fmt.Sprintf("https://%s/b/%s ", mgr.dyconfig.GetShortLinkHost(), linkResp.GetShortLink())

    // 你的玩伴<{#}>发来消息：想念和你在一起的欢乐时光，已为你备好回归大礼，内含绝版珍稀{#}礼物，盲盒价值最高{#}元！快点击链接领取吧>>>{#} 拒收请回复R
    BlindboxVal := mgr.dyconfig.GetBlindboxVal()
    smsType := mgr.dyconfig.GetSmsType(marketId) //mgr.dyconfig.GetUserRecallSmsType()
    //jumpUrl := mgr.dyconfig.GetUserRecallSmsJumpUrl()
    //recallCode := userMap[uid].GetAlias()
    tbeanName := mgr.dyconfig.GetMarktId2TbeanName(marketId)
    params := []string{nickname, tbeanName, fmt.Sprintf("%d", BlindboxVal), shortLinkUrl}
    smsReq := &sms_go.SendSmsReq{
        Phone:           phone,
        SmsType:         smsType,
        ParamList:       params,
        WithoutCooldown: false,
        VerifyCodeKey:   "",
        VerifyCodeUsage: "",
        MarketId:        marketId,
    }
    log.InfoWithCtx(ctx, "sendSms uid=%d targetUid=%d smsReq=%s", uid, targetUid, smsReq.String())

    serr = mgr.SmsCli.SendSmsV2(ctx, smsReq)
    if serr != nil {
        log.ErrorWithCtx(ctx, "sendSms SendSmsV2 fail %v, uid=%d targetUid=%d phone=%s", serr, uid, targetUid, phone)

        ok, err := mgr.rc.DelSendSmsLock(uid, targetUid, lastLoginTime)
        log.InfoWithCtx(ctx, "sendSms DelSendSmsLock %d %d %d %d %v", uid, targetUid, lastLoginTime, ok, err)

        if serr.Underlying() != nil && strings.Contains(serr.Underlying().Error(), "no healthy upstream") {
            serr = mgr.SmsCli.SendSmsV2(ctx, smsReq)
            if serr != nil {
                log.ErrorWithCtx(ctx, "sendSms SendSmsV2 fail retry. %v, uid=%d targetUid=%d phone=%s", serr, uid, targetUid, phone)
            } else {
                mgr.rc.CheckIsSendSms(uid, targetUid, lastLoginTime)
                log.InfoWithCtx(ctx, "sendSms uid=%d targetUid=%d retry ok.", uid, targetUid)
                return nil
            }
        }

        if serr.Code() == int(smsPB.ERR_SMS_ERR_SMS_SEND_SMS_THRESHHOLD_EXCEED) {
            return protocol.NewServerError(status.ErrAccountSmsThreshholdExceed, "今日验证码发送次数已达上限")
        } else if serr.Code() == int(smsPB.ERR_SMS_ERR_SMS_SEND_SMS_FREQ) {
            return protocol.NewServerError(status.ErrAccountVerifyTooFreq, "请稍后再试")
        }
        // 其他错误
        return serr
    }
    return nil
}

func (mgr *UserRecallManager) TestSms(ctx context.Context, req *pb.TestSmsReq) (*pb.SendRecallResp, error) {
    out := &pb.SendRecallResp{}
    if !req.Certain {
        return out, nil
    }
    err := mgr.sendSms(ctx, req.Uid, req.TargetUid, 0)
    return out, err
}

// 直接查数据接口
func (mgr *UserRecallManager) GetLossUidList(ctx context.Context, req *pb.GetLossUidListReq) (*pb.GetLossUidListResp, error) {
    out := &pb.GetLossUidListResp{}
    if req.InviteUid > 0 {
        _, list, err := mgr.getLossUidList(ctx, req.InviteUid)
        if err != nil {
            return out, err
        }
        out.List = list
        return out, nil
    }
    for _, uid := range req.LossUids {
        res, err := mgr.getLossUserInfo(ctx, uid)
        if err != nil {
            return out, err
        }
        if len(res.Data.UserInfos) == 0 {
            continue
        }

        info := res.Data.UserInfos[0]

        lossuid, _ := strconv.Atoi(info.Uid)
        tbeanVal, _ := strconv.ParseFloat(info.ConsumeTbeanAmtAcc, 32)
        receiveVal, _ := strconv.ParseFloat(info.ReceiveGiftOrdAmt, 32)
        logindate, _ := time.ParseInLocation("2006-01-02", info.LstLoginDate, time.Local)
        if lossuid > 0 && tbeanVal > 0 && logindate.Unix() > 0 {
            out.List = append(out.List, &pb.LossUserInfo{
                Uid:                uint32(lossuid),
                ConsumeTbeanAmtCnt: uint64(tbeanVal * 100),
                LstLoginDate:       uint32(logindate.Unix()),
                ReceiveGiftOrdAmt:  uint64(receiveVal * 100),
            })
        } else {
            log.Errorf("getLossUidList invalid data. %+v, opuid=%d", info, uid)
        }
    }
    return out, nil
}

func (mgr *UserRecallManager) CleanInviteData(ctx context.Context, req *pb.CleanInviteDataReq) (*pb.CleanInviteDataResp, error) {
    uid := req.InviteUid
    out := &pb.CleanInviteDataResp{}

    /*
    	删除邀请记录
    	delete from user_recall_invite_record where invite_uid=2421669 and uid in(2435401,2534775);
    	删除邀请im推送记录
    	delete from user_recall_im_record where invite_uid=2421669 and uid in(2435401,2534775);
    	删除邀请方可召回好友缓存
    	redis-cli -h 10.64.236.69  del user_recall:loss_uids_2421669_20231103
    */

    recordList, err := mgr.store.GetInviterAllRecord(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "store.GetInviterAllRecord err=%v", err)
        return out, err
    }
    for _, elem := range recordList {
        err = mgr.rc.DelTryTimes(elem.InviteUid, elem.Uid, elem.LastLoginTime)
        if err != nil {
            log.ErrorWithCtx(ctx, "rc.DelTryTimes err=%v", err)
            return out, err
        }
    }

    mgr.store.DeleteUserRecallInviteRecord(ctx, uid)
    mgr.store.DeleteUserRecallImRecord(ctx, uid)
    mgr.rc.DelLossUidList(uid)
    log.InfoWithCtx(ctx, "CleanInviteData %d", uid)
    return out, nil
}

// ====================================================================

func (mgr *UserRecallManager) getLossUidListFromCache(ctx context.Context, uid uint32) ([]*pb.LossUserInfo, error) {
    exist, data, err := mgr.rc.GetLossUidList(uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLossUidList fail %v, uid=%d", err, uid)
        return nil, err
    }
    if exist {
        info := &pb.LossUserInfoList{}
        err = proto.Unmarshal(data, info)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetLossUidList Unmarshal fail %v, uid=%d", err, uid)
            return nil, err
        }
        return info.GetList(), nil
    }

    subCtx, cancel := context.WithTimeout(ctx, time.Second)
    defer cancel()
    _, list, err := mgr.getLossUidList(subCtx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLossUidList from data fail %v, uid=%d", err, uid)
        //return nil, err
    }

    if len(list) > 0 {
        err = mgr.store.InsertUserRecallCouple(ctx, uid, list)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetLossUidList InsertUserRecallCouple fail %v, uid=%d", err, uid)
            return nil, err
        }

        recordList, err := mgr.store.GetUserRecallCouple(ctx, uid, list)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetLossUidList GetUserRecallCouple fail %v, uid=%d", err, uid)
            return nil, err
        }
        list = []*pb.LossUserInfo{}
        for _, record := range recordList {
            list = append(list, &pb.LossUserInfo{
                Uid:                record.Uid,
                ConsumeTbeanAmtCnt: uint64(record.ConsumeTbeanAmtCnt),
                LstLoginDate:       uint32(record.LastLoginTime),
                ReceiveGiftOrdAmt:  uint64(record.ReceiveGiftOrdAmt),
                CreateTime:         uint32(record.CreateTime),
            })
        }
        log.InfoWithCtx(ctx, "GetLossUidList opUid=%d reload list=%s", uid, utils.ToJson(list))
    }

    whiteList, err := mgr.GetRecallWhiteList(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLossUidList GetRecallWhiteList fail %v, uid=%d", err, uid)
        return nil, err
    }

    newlist := mgr.mergeRecallList(whiteList, list)

    log.DebugWithCtx(ctx, "GetLossUidList uid=%d GetRecallWhiteList=%+v, dataList=%+v, newlist=%+v",
        uid, whiteList, list, newlist)

    info := &pb.LossUserInfoList{List: newlist}
    bin, _ := proto.Marshal(info)
    err = mgr.rc.SetLossUidList(uid, bin)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetLossUidList SetLossUidList fail %v, uid=%d", err, uid)
        return nil, err
    }
    return newlist, nil
}

func (mgr *UserRecallManager) GetShortLink(ctx context.Context, req *pb.GetShortLinkReq) (*pb.GetShortLinkResp, error) {
    out := &pb.GetShortLinkResp{}

    if req.Url != "" {
        link, err := mgr.rc.GetShortLinkByUrl(req.Url)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetShortLink GetShortLinkByUrl fail %v req=%+v", err, req)
            return out, err
        }
        log.DebugWithCtx(ctx, "GetShortLink cache.GetShortLinkByUrl=%s req=%+v", link, req)
        if link != "" {
            out.ShortLink = link
            out.Url = req.Url
            return out, nil
        }

        links := shortUrl(req.Url)
        newlink := ""
        for _, link := range links {
            url, err := mgr.rc.GetUrlByShortLink(link)
            if err != nil {
                log.ErrorWithCtx(ctx, "GetShortLink GetUrlByShortLink fail %v req=%v", err, req)
                return out, err
            }
            if url == "" {
                newlink = link
                break
            }
        }
        if newlink == "" {
            log.ErrorWithCtx(ctx, "短链生成错误 req.Url=%s, links=%v", req.Url, links)
            err := protocol.NewServerError(status.ErrUserRecallParams, "短链生成错误")
            return out, err
        }

        //newlink = fmt.Sprintf("https://%s/b/%s", mgr.dyconfig.GetShortLinkHost(), newlink)
        err = mgr.rc.SetShortLink(req.Url, newlink)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetShortLink SetShortLink fail %v req=%v", err, req)
            return out, err
        }
        out.ShortLink = newlink
        out.Url = req.Url
        log.InfoWithCtx(ctx, "GetShortLink SetShortLink %s %s", req.Url, newlink)
        return out, nil
    }

    if req.ShortLink != "" {
        url, err := mgr.rc.GetUrlByShortLink(req.ShortLink)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetShortLink GetUrlByShortLink fail %v req=%+v", err, req)
            return out, err
        }
        log.DebugWithCtx(ctx, "GetShortLink cache.GetUrlByShortLink=%s req=%+v", url, req)

        if url != "" {
            out.ShortLink = req.ShortLink
            out.Url = url
            return out, nil
        }

        //out.ShortLink = req.ShortLink
        //out.Url = url
    }
    return out, nil
}

func getMd5(url string) string {
    h := md5.New() // #nosec
    io.WriteString(h, url)
    return hex.EncodeToString(h.Sum(nil))
}

func shortUrl(url string) []string {
    // 要使用生成 URL 的字符
    chars := []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p",
        "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A",
        "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V",
        "W", "X", "Y", "Z"}
    // 对传入网址进行 MD5 加密，获取32位字符串
    hex := getMd5(url)
    log.Debugf("shortUrl len=%d,%v\n", len(hex), hex)
    resUrl := make([]string, 4)
    for i := 0; i < 4; i++ {
        // 把加密字符按照 8 位一组 16 进制与 0x3FFFFFFF 进行位与运算
        sTempSubString := hex[i*8 : i*8+8]
        // 这里需要使用 long 型来转换，因为 Inteper .parseInt() 只能处理 31 位 , 首位为符号位 , 如果不用
        // long ，则会越界

        value, _ := strconv.ParseInt(sTempSubString, 16, 64)
        log.Debugf("shortUrl sTempSubString=%v Val=%d\n", sTempSubString, value)

        lHexLong := 0x3FFFFFFF & uint64(value)
        outChars := ""
        for j := 0; j < 6; j++ {
            // 把得到的值与 0x0000003D 进行位与运算，取得字符数组 chars 索引
            index := 0x0000003D & lHexLong
            // 把取得的字符相加
            outChars += chars[index]
            // 每次循环按位右移 5 位
            lHexLong = lHexLong >> 5
        }
        // 把字符串存入对应索引的输出数组
        resUrl[i] = outChars

        log.Debugf("shortUrl chars=%s\n", outChars)
    }
    return resUrl
}

func (mgr *UserRecallManager) SetRecallWhite(ctx context.Context, inviteUid uint32, uid uint32) error {
    log.InfoWithCtx(ctx, "SetRecallWhite inviteUid=%d uid=%d", inviteUid, uid)
    if inviteUid == 0 || uid == 0 {
        return nil
    }
    err := mgr.store.SaveUserRecallWhiteList(ctx, inviteUid, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetRecallWhite SaveUserRecallWhiteList fail %v, inviteUid=%d uid=%d", err, inviteUid, uid)
        return err
    }

    err = mgr.rc.DelLossUidList(inviteUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetRecallWhite DelLossUidList fail %v, inviteUid=%d uid=%d", err, inviteUid, uid)
        return err
    }

    return nil
}

func (mgr *UserRecallManager) DelRecallWhite(ctx context.Context, inviteUid uint32, uid uint32) error {
    log.InfoWithCtx(ctx, "DelRecallWhite inviteUid=%d uid=%d", inviteUid, uid)
    if inviteUid == 0 || uid == 0 {
        return nil
    }

    err := mgr.store.DelUserRecallWhiteList(ctx, inviteUid, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "DelRecallWhite DelUserRecallWhiteList fail %v, inviteUid=%d uid=%d", err, inviteUid, uid)
        return err
    }

    err = mgr.rc.DelLossUidList(inviteUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "DelRecallWhite DelLossUidList fail %v, inviteUid=%d uid=%d", err, inviteUid, uid)
        return err
    }

    return nil
}

func (mgr *UserRecallManager) GetRecallWhiteList(ctx context.Context, inviteUid uint32) ([]*pb.LossUserInfo, error) {
    list, err := mgr.store.GetUserRecallWhiteListDataList(ctx, inviteUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetRecallWhiteList GetUserRecallWhiteListDataList fail %v, inviteUid=%d", err, inviteUid)
        return nil, err
    }
    lossList := []*pb.LossUserInfo{}
    for _, info := range list {
        lossUid := info.Uid
        lossInfo, err := mgr.store.GetUserRecalledWhiteListData(ctx, lossUid)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetRecallWhiteList GetUserRecalledWhiteListData fail %v, inviteUid=%d", err, inviteUid)
            return nil, err
        }
        log.InfoWithCtx(ctx, "GetRecallWhiteList inviteUid=%d lossUid=%d lossInfo=%+v", inviteUid, lossUid, lossInfo)
        lossList = append(lossList, &pb.LossUserInfo{
            Uid:                lossUid,
            ConsumeTbeanAmtCnt: lossInfo.AccConsume,
            LstLoginDate:       uint32(lossInfo.LastLoginTime),
            CreateTime:         uint32(time.Now().Unix()),
        })
    }
    log.InfoWithCtx(ctx, "GetRecallWhiteList inviteUid=%d list=%+v", inviteUid, lossList)
    return lossList, nil
}

func (mgr *UserRecallManager) mergeRecallList(list1, list2 []*pb.LossUserInfo) []*pb.LossUserInfo {
    if len(list1) == 0 {
        return list2
    }

    uid2map := map[uint32]bool{}
    for _, info := range list1 {
        uid2map[info.Uid] = true
    }
    for _, info := range list2 {
        if !uid2map[info.Uid] {
            list1 = append(list1, info)
        }
    }
    return list1
}

func (mgr *UserRecallManager) GetBindStatus(ctx context.Context, req *pb.GetBindStatusReq) (*pb.GetBindStatusResp, error) {
    out := &pb.GetBindStatusResp{}
    defer func() {
        log.DebugWithCtx(ctx, "GetBindStatus req=%+v, out.Status=%d", req, out.Status)
    }()

    lossUidList, err := mgr.getLossUidListFromCache(ctx, req.InviteUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetBindStatus GetLossUidList fail %v, uid=%d", err, req.InviteUid)
        return nil, err
    }
    if len(lossUidList) == 0 {
        return out, nil
    }

    cheating, err := mgr.CheckIsRecallOnly(ctx, req.LossUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetBindStatus antiCheating err=%v, req=%+v", err, req)
        return nil, err
    }
    if cheating.LastLoginTime <= 0 {
        log.ErrorWithCtx(ctx, "GetBindStatus CheckIsRecallOnly null. lossUid=%d", req.LossUid)
        return out, nil
    }

    if mgr.dyconfig.CheckInConsumeRange(uint32(cheating.AccConsume)) {
        log.InfoWithCtx(ctx, "GetBindStatus CheckInConsumeRange inviteUid=%d lossUid=%d", req.InviteUid, req.LossUid)
        return out, nil
    }

    bind, err := mgr.store.GetUserBindInfo(ctx, req.LossUid, cheating.LastLoginTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetBindStatus antiCheating err=%v, req=%d", err, req)
        return nil, err
    }

    log.DebugWithCtx(ctx, "GetBindStatus GetBindInfo=%d, req=%+v", bind, req)
    bindUid := bind.InviteUid
    if bindUid > 0 {
        if bindUid == req.InviteUid {
            out.Status = uint32(pb.UserRecallBindStatus_UserRecallBindStatus_Bind)
        } else {
            out.Status = uint32(pb.UserRecallBindStatus_UserRecallBindStatus_BindOther)
        }
        return out, nil
    }

    lossUserList, err := mgr.getLossUidListFromCache(ctx, req.InviteUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetBindStatus getLossUidListFromCache fail %v, req=%+v", err, req)
        return out, err
    }
    log.DebugWithCtx(ctx, "GetBindStatus getLossUidListFromCache=%+v, req=%+v", lossUserList, req)
    for _, user := range lossUserList {
        if user.Uid == req.LossUid {
            out.Status = uint32(pb.UserRecallBindStatus_UserRecallBindStatus_NoBind)
            return out, nil
        }
    }
    return out, nil
}

func (mgr *UserRecallManager) getUserBindedInfo(ctx context.Context, uid, lastLoginTime uint32) (binded bool, err error) {

    exist, bined, err := mgr.rc.GetUserBindedInfo(uid, lastLoginTime)
    if err != nil {
        log.Errorf("getUserBindedInfo GetUserBindedInfo fail %v, uid=%d,lastLoginTime=%d", err, uid, lastLoginTime)
        return false, err
    }
    if exist {
        log.DebugWithCtx(ctx, "getUserBindedInfo uid=%d,lastLoginTime=%d cache.GetUserBindedInfo bined=%v", uid, lastLoginTime, bined)
        return bined, nil
    }

    log.InfoWithCtx(ctx, "before store.GetUserBindInfo uid=%d, lastLoginTime=%d", uid, lastLoginTime)

    info, err := mgr.store.GetUserBindInfo(ctx, uid, int64(lastLoginTime))
    if err != nil {
        log.Errorf("getUserBindedInfo GetUserBindInfo fail %v, uid=%d,lastLoginTime=%d", err, uid, lastLoginTime)
        return false, err
    }

    log.InfoWithCtx(ctx, "after store.GetUserBindInfo uid=%d, lastLoginTime=%d", uid, lastLoginTime)

    binded = info.Uid > 0 && info.Status == model.StatusDone
    err = mgr.rc.SetUserBindedInfo(uid, lastLoginTime, binded)
    if err != nil {
        log.Errorf("getUserBindedInfo SetUserBindedInfo fail %v, uid=%d,lastLoginTime=%d", err, uid, lastLoginTime)
        return false, err
    }
    log.InfoWithCtx(ctx, "getUserBindedInfo uid=%d,lastLoginTime=%d info=%+v SetUserBindedInfo binded=%v", uid, lastLoginTime, info, binded)
    return binded, nil
}
