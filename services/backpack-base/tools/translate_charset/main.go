package main

import (
	"flag"
	"fmt"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/backpack-base/store"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type Store struct {
	utf8Db  *gorm.DB
	latinDb *gorm.DB
}

func NewStore(sc *config.ServerConfig) (*Store, error) {
	wdb, err := store.NewMysqlCli(sc, "mysql_utf8", false)
	if err != nil {
		log.Errorf("mysql_utf8 fail , err:%v", err)
		return nil, err
	}
	latiDb, err := store.NewMysqlCli(sc, "mysql_latin", false)
	if err != nil {
		log.Errorf("mysql_latin  fail , err:%v", err)
		return nil, err
	}
	return &Store{
		utf8Db:  wdb,
		latinDb: latiDb,
	}, nil
}

type TranslateRow struct {
	ID        uint32 `gorm:"column:id" db:"id" json:"id"`                         //id
	OldColumn string `gorm:"column:old_column" db:"old_column" json:"old_column"` //描述
	NewColumn string `gorm:"column:new_column" db:"new_column" json:"new_column"` //描述V2
}

func (s *Store) TranslateCharset(tb string, idCol, oldCol, newCol string) error {
	limit := 200
	offset := uint32(0)
	for {
		sql := fmt.Sprintf("select %s as id, %s as old_column, %s as new_column from %s where %s > ? order by %s limit ?", idCol, oldCol, newCol, tb, idCol, idCol)
		fmt.Printf(sql)
		rows := []*TranslateRow{}
		err := s.latinDb.Raw(sql, offset, limit).Scan(&rows).Error
		if err != nil {
			log.Errorf("sql:%s, offset:%d, %d err:%v", sql, offset, limit, err)
			return err
		}
		for _, row := range rows {
			if row.ID > offset {
				offset = row.ID
			}
			if len(row.OldColumn) == 0 {
				continue
			}
			if len(row.NewColumn) > 0 {
				continue
			}
			log.Infof("%v", row)
			err = s.utf8Db.Table(tb).Where(fmt.Sprintf("%s=?", idCol), row.ID).Update(newCol, row.OldColumn).Error
			if err != nil {
				log.Errorf("row:%v, err:%v", row, err)
				return err
			}
		}

		if len(rows) < limit {
			break
		}
	}
	return nil
}
func (s *Store) TranslateWeight() {
	mList := []*store.PackageItemConfig{}
	err := s.utf8Db.Select("item_type", "source_id", "weight").Where("is_del=0 and weight>0").Find(&mList).Error
	if err != nil {
		log.Errorf("GetPackageItemCfg fail , err:%v", err)
		return
	}
	weightList := []*store.ShowBackpackItemWeight{}

	itemOkMaps := map[string]uint32{}
	for _, m := range mList {
		key := fmt.Sprintf("%d-%d", m.ItemType, m.SourceId)
		if _, ok := itemOkMaps[key]; !ok {
			weightList = append(weightList, &store.ShowBackpackItemWeight{
				ItemType: m.ItemType,
				ItemId:   m.SourceId,
				Weight:   m.Weight,
			})
			itemOkMaps[key] = 1
		}
	}
	err = s.utf8Db.Clauses(clause.OnConflict{
		DoUpdates: clause.AssignmentColumns([]string{"item_type", "item_type", "weight", "deleted"}),
	}).CreateInBatches(weightList, len(weightList)).Error
	if err != nil {
		log.Errorf("TranslateWeight fail , err:%v", err)
		return
	}
	log.Infof("TranslateWeight ok...")
}

func main() {
	translateType := flag.Int("type", 1, "1: charset, 2: show weight")
	configFile := flag.String("config", "./translate_charset.json", "config")
	flag.Parse()
	sc := config.EmptyServerConfig()
	err := sc.InitWithPath("json", *configFile)
	if err != nil {
		log.Errorf("Init config err %v", err)
		return
	}

	s, err := NewStore(sc)
	if err != nil {
		log.Errorf("NewStore err %v", err)
		return
	}

	switch *translateType {
	case 1:
		_ = s.TranslateCharset("package_config", "bg_id", "bg_name", "bg_name_v2")
		_ = s.TranslateCharset("package_config", "bg_id", "bg_desc", "bg_desc_v2")
		_ = s.TranslateCharset("tbl_fragment_cfg", "fragment_id", "fragment_name", "fragment_name_v2")
		_ = s.TranslateCharset("tbl_fragment_cfg", "fragment_id", "fragment_desc", "fragment_desc_v2")
	case 2:
		s.TranslateWeight()
	default:
	}

}
