package comm

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/clients/account"
	anchorcontract2 "golang.52tt.com/clients/anchorcontract"
	anchorcontract_go "golang.52tt.com/clients/anchorcontract-go"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	channellivemgr "golang.52tt.com/clients/channel-live-mgr"
	channellivestats "golang.52tt.com/clients/channel-live-stats"
	"golang.52tt.com/clients/guild"
	guild_management_svr "golang.52tt.com/clients/guild-management-svr"
	im_api "golang.52tt.com/clients/im-api"
	iopClient "golang.52tt.com/clients/iop-proxy"
	knightgroupscore "golang.52tt.com/clients/knight-group-score"
	masked_pk_score "golang.52tt.com/clients/masked-pk-score"
	"golang.52tt.com/clients/public"
	"golang.52tt.com/clients/realnameauth"
	settlement_bill "golang.52tt.com/clients/settlement-bill"
	user_risk_api "golang.52tt.com/clients/user-risk-api"
	userscore_go "golang.52tt.com/clients/userscore-go"
	"golang.52tt.com/pkg/commission"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/oa"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tbean"
	"golang.52tt.com/protocol/common/status"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	anchorcontract_go2 "golang.52tt.com/protocol/services/anchorcontract-go"
	anchorcontract "golang.52tt.com/protocol/services/anchorcontractsvr"
	channel_live_stats "golang.52tt.com/protocol/services/channel-live-stats"
	"golang.52tt.com/protocol/services/esport_score"
	Exchange "golang.52tt.com/protocol/services/exchange"
	Guild "golang.52tt.com/protocol/services/guildsvr"
	imApiPb "golang.52tt.com/protocol/services/im-api"
	publicPB "golang.52tt.com/protocol/services/publicsvr"
	pb "golang.52tt.com/protocol/services/realNameAuthSvr"
	settlement_bill2 "golang.52tt.com/protocol/services/settlement-bill"
	"golang.52tt.com/services/exchange/conf"
	"google.golang.org/grpc"
	"io/ioutil"
	"os"
	"strconv"
	"time"
)

const (
	CommissionTtknightAppid            = 22
	CommissionExchangeguildknightAppid = 23
	CommissionEsportAppid              = 35
	CommissionEsportGuildAppid         = 36
)

type mockDataType struct {
	AuthPhone          string   `json:"auth_phone"`
	Name               string   `json:"name"`
	IdentityNum        string   `json:"identity_num"`
	GuildId            uint32   `json:"guild_id"`
	AnchorIdentityList []uint32 `json:"anchor_identity_list"`
	ContractExpireTime uint32   `json:"contract_expire_time"`
	MonthScore         uint32   `json:"month_score"`
	GuildShortId       uint32   `json:"guild_short_id"`
	GuildName          string   `json:"guild_name"`
}

var (
	accountClient          *account.Client
	realnameauthClient     *realnameauth.Client
	guildClient            *guild.Client
	anchorcontractGoClient *anchorcontract_go.Client
	anchorcontractClient   *anchorcontract2.Client
	//commissionCAClient            commission.Client
	commissionPkClient            commission.Client
	commissionPresentClient       commission.Client
	commissionAnchorClient        commission.Client
	commissionKnightClient        commission.Client
	commissionPrivateKnightClient commission.Client
	commissionEsportClient        commission.Client
	commissionPrivateEsportlient  commission.Client
	apiClient                     *apicenter.Client
	publicClient                  *public.Client
	mock                          bool
	mockData                      mockDataType
	channelLiveStatsClient        *channellivestats.Client
	oaClient                      *oa.Client
	settleBillClient              *settlement_bill.Client
	userRiskClient                *user_risk_api.Client
	tbeanClient                   tbean.Client
	//esportRoleClient              *esport_role.Client
	ImApiCli *im_api.Client
	IopClient iopClient.IClient
	guildMangerClient guild_management_svr.IClient
)

func InitClients(configer config.Configer) {
	var err error
	accountClient, err = account.NewClient(grpc.WithInsecure(), grpc.WithBlock())
	if err != nil {
		panic(err)
	}
	realnameauthClient = realnameauth.NewClient(grpc.WithInsecure(), grpc.WithBlock())
	guildClient = guild.NewClient(grpc.WithInsecure(), grpc.WithBlock())
	anchorcontractGoClient, err = anchorcontract_go.NewClient(grpc.WithInsecure(), grpc.WithBlock())
	if err != nil {
		panic(err)
	}
	anchorcontractClient = anchorcontract2.NewClient(grpc.WithInsecure(), grpc.WithBlock())

	userscoreGoClient = userscore_go.NewClient()
	channellivemgrClient, err = channellivemgr.NewClient(grpc.WithInsecure(), grpc.WithBlock())
	if err != nil {
		panic(err)
	}
	maskedPkScoreClient, err = masked_pk_score.NewClient(grpc.WithInsecure(), grpc.WithBlock())
	if err != nil {
		panic(err)
	}

	knightScoreClient, err = knightgroupscore.NewClient(grpc.WithInsecure(), grpc.WithBlock())
	if err != nil {
		panic(err)
	}

	esportScoreClient, err = esport_score.NewClient(context.Background())
	if err != nil {
		panic(err)
	}

	publicClient = public.NewClient()

	apiClient = apicenter.NewClient()

	channelLiveStatsClient, err = channellivestats.NewClient(grpc.WithInsecure(), grpc.WithBlock())
	if err != nil {
		panic(err)
	}

	userRiskClient, err = user_risk_api.NewClient(grpc.WithInsecure(), grpc.WithBlock())
	if err != nil {
		panic(err)
	}

	IopClient = iopClient.NewIClient()

	guildMangerClient = guild_management_svr.NewIClient()

	//commissionCAClient = commission.NewClient(
	//	configer.String("commission_ca_api::url_prefix"),
	//	configer.String("commission_ca_api::app_id"),
	//	configer.String("commission_ca_api::sign_key"))

	commissionPkClient = commission.NewClient(
		configer.String("commission_pk_api::url_prefix"),
		configer.String("commission_pk_api::app_id"),
		configer.String("commission_pk_api::sign_key"))

	commissionPresentClient = commission.NewClient(
		configer.String("commission_present_api::url_prefix"),
		configer.String("commission_present_api::app_id"),
		configer.String("commission_present_api::sign_key"))

	commissionAnchorClient = commission.NewClient(
		configer.String("commission_anchor_api::url_prefix"),
		configer.String("commission_anchor_api::app_id"),
		configer.String("commission_anchor_api::sign_key"))

	commissionKnightClient = commission.NewClient(
		configer.String("commission_knight_api::url_prefix"),
		configer.String("commission_knight_api::app_id"),
		configer.String("commission_knight_api::sign_key"))

	commissionPrivateKnightClient = commission.NewClient(
		configer.String("commission_api::url_prefix"),
		configer.String("commission_api::knight_app_id"),
		configer.String("commission_api::knight_sign_key"))

	commissionEsportClient = commission.NewClient(
		configer.String("commission_esport_api::url_prefix"),
		configer.String("commission_esport_api::app_id"),
		configer.String("commission_esport_api::sign_key"))

	commissionPrivateEsportlient = commission.NewClient(
		configer.String("commission_api::url_prefix"),
		configer.String("commission_api::esport_app_id"),
		configer.String("commission_api::esport_sign_key"))

	oaClient = oa.NewOAClient(
		configer.String("oa_config::host"),
		configer.String("oa_config::id"),
		configer.String("oa_config::secret"),
		configer.String("oa_config::account"))

	settleBillClient = settlement_bill.NewClient(grpc.WithInsecure(), grpc.WithBlock())

	tbeanClient = tbean.NewClient(configer.String("tbean_api::context_path"))

	ImApiCli, err = im_api.NewClient()
	if err != nil {
		panic(err)
	}

	//esportRoleClient, err = esport_role.NewClient()
	//if err != nil {
	//	panic(err)
	//}

	mock = false
	mockPath := os.Getenv("MOCK_PATH")
	if len(mockPath) > 0 {
		log.Infoln("MOCK_PATH=", mockPath)
		mock = true
		data, err := ioutil.ReadFile(mockPath)
		if err != nil {
			panic(err)
		}

		err = json.Unmarshal(data, &mockData)
		if err != nil {
			panic(err)
		}
	}
}

//分页获取电竞指导信息
//func GetESportErInfoListWithUidList(ctx context.Context, uidList []uint32) (*esport_role2.GetESportErInfoListResp, error) {
//	return esportRoleClient.GetESportErInfoList(ctx, &esport_role2.GetESportErInfoListReq{
//		QueryType:  uint32(esport_role2.QueryType_QUERY_BY_UID),
//		EsportType: 0,
//		UidList:    uidList,
//		GuildId:    0,
//		Page:       0,
//		PageSize:   50,
//	})
//}

func SettlementPresent(ctx context.Context, masterUid uint32, score uint64, date string) (*commission.SettlementDataResponse, error) {
	if conf.XGuildConfig.Test {
		date = fmt.Sprintf("%d", time.Now().Unix())
	}
	return commissionPresentClient.Settlement(ctx, masterUid, score, date, "")
}

func SettlementAnchor(ctx context.Context, masterUid uint32, score uint64, date string) (*commission.SettlementDataResponse, error) {
	if conf.XGuildConfig.Test {
		date = fmt.Sprintf("%d", time.Now().Unix())
	}
	return commissionAnchorClient.Settlement(ctx, masterUid, score, date, "")
}

func SettlementPk(ctx context.Context, masterUid uint32, score uint64, date string) (*commission.SettlementDataResponse, error) {
	if conf.XGuildConfig.Test {
		date = fmt.Sprintf("%d", time.Now().Unix())
	}
	return commissionPkClient.Settlement(ctx, masterUid, score, date, "")
}

func SettlementKnight(ctx context.Context, masterUid uint32, score uint64, date string) (*commission.SettlementDataResponse, error) {
	if conf.XGuildConfig.Test {
		date = fmt.Sprintf("%d", time.Now().Unix())
	}
	return commissionKnightClient.Settlement(ctx, masterUid, score, date, "")
}

func SettlementEsport(ctx context.Context, masterUid uint32, score uint64, date string) (*commission.SettlementDataResponse, error) {
	if conf.XGuildConfig.Test {
		date = fmt.Sprintf("%d", time.Now().Unix())
	}
	return commissionEsportClient.Settlement(ctx, masterUid, score, date, "")
}

func GetSettlementBalance(ctx context.Context, masterUid uint32, xType Exchange.ExchangeType) (balance uint64, err error) {
	var clt commission.Client
	switch xType {
	case Exchange.ExchangeType_PRESENT:
		clt = commissionPresentClient
	case Exchange.ExchangeType_ANCHOR:
		clt = commissionAnchorClient
	case Exchange.ExchangeType_PK:
		clt = commissionPkClient
	case Exchange.ExchangeType_KNIGHT:
		clt = commissionKnightClient
	case Exchange.ExchangeType_ESPORT:
		clt = commissionEsportClient
	default:
		err = protocol.NewServerError(status.ErrExchangeTypeNotSupport)
		return
	}
	balance, _, err = clt.GetBalance(ctx, masterUid)
	return
}

//用这个写部分接口，太不优雅了，虽然效率比较高
func BatchGetAccountData(uidlist []uint32) (map[uint32]*accountPB.UserResp, error) {
	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()
	return accountClient.BatGetUserByUid(ctx, uidlist...)
}

func GetAccountData(uid uint32) (*accountPB.UserResp, error) {
	if mock {
		resp := &accountPB.UserResp{Uid: uid}
		resp.Alias = "ttid" + strconv.Itoa(int(uid))
		resp.Nickname = "nick" + strconv.Itoa(int(uid))
		return resp, nil
	}

	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()
	return accountClient.GetUserByUid(ctx, uid)
}

func GetUidByTTid(ttid string) (uint32, error) {
	//if mock {
	//	return 123, nil
	//}

	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()
	uid, _, err := accountClient.GetUidByName(ctx, ttid)
	if err != nil {
		return 0, err
	} else {
		return uid, nil
	}
}

func GetRealData(uid uint32) (*pb.GetUserRealNameAuthInfoV2Resp, protocol.ServerError) {
	if mock {
		resp := &pb.GetUserRealNameAuthInfoV2Resp{}
		resp.AuthPhone = mockData.AuthPhone
		resp.IdcardInfo = &pb.AuthIdCardInfo{Name: mockData.Name, IdentityNum: mockData.IdentityNum}
		return resp, nil
	}
	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()
	return realnameauthClient.GetUserRealNameAuthInfoV2(ctx, uid, true, true)
}

func GetGuildData(guildId uint32) (*Guild.GuildResp, protocol.ServerError) {
	if mock {
		resp := &Guild.GuildResp{GuildId: guildId, ShortId: mockData.GuildShortId, Name: mockData.GuildName}
		return resp, nil
	}
	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()
	return guildClient.GetGuild(ctx, guildId)
}

func GetGuildByDataShortId(guildShortId uint32) (*Guild.GuildResp, protocol.ServerError) {
	if mock {
		resp := &Guild.GuildResp{GuildId: mockData.GuildId, ShortId: guildShortId}
		return resp, nil
	}
	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()
	return guildClient.GetGuildByShortId(ctx, guildShortId)
}

func GetAnchorContractGuildInfo(uid uint32) (*anchorcontract_go2.ContractCacheInfo, protocol.ServerError) {
	if mock {
		resp := &anchorcontract_go2.ContractCacheInfo{AnchorIdentityList: mockData.AnchorIdentityList}
		resp.Contract = &anchorcontract_go2.ContractInfo{}
		resp.Contract.GuildId = mockData.GuildId
		resp.Contract.ExpireTime = mockData.ContractExpireTime
		return resp, nil
	}
	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()
	return anchorcontractGoClient.GetUserContractCacheInfo(ctx, uid, uid)
}

func GetAnchorMonthScore(uid uint32, guildId uint32, timestamp uint32) (*anchorcontract.GetUserInGuildMonthScoreResp, protocol.ServerError) {
	if mock {
		resp := &anchorcontract.GetUserInGuildMonthScoreResp{Score: mockData.MonthScore}
		return resp, nil
	}
	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()
	return anchorcontractClient.GetUserInGuildMonthScore(ctx, uid, guildId, timestamp)
}

func GetAnchorMonthLiveScore(uid uint32, guildId uint32, timestamp uint32) (*channel_live_stats.GetAnchorMonthlyStatsResp, protocol.ServerError) {
	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()
	return channelLiveStatsClient.GetAnchorMonthlyStats(ctx, uid, uid, guildId, timestamp)
}

func GetGuildServerFromUid(ctx context.Context) (fromUID uint32) {
	publicReq := &publicPB.GetPublicAccountsByBindedIdListReq{
		Type: uint32(publicPB.PublicAccountType_SYSTEM),
	}
	publicRsp, err := publicClient.GetPublicAccountsByBindedIdList(context.Background(), 0, publicReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPublicAccountsByBindedIdList err:%s\n", err.Error())
		return
	}
	for _, accountElem := range publicRsp.GetPublicAccountList() {
		if accountElem.GetName() == "会长服务号" {
			fromUID = accountElem.GetPublicId()
			return
		}
	}
	if fromUID == 0 {
		log.ErrorWithCtx(ctx, "GetPublicAccountsByBindedIdList no guild server")
	}
	return
}

func SendTTMsg(ctx context.Context, uid uint32, content, hlight, url string, guildServer bool) error {
	/*if mock {
		return nil
	}

	msg := new(apiPB.ImMsg)
	msg.ImType = &apiPB.ImType{
		SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
		ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
	}

	if guildServer {
		msg.FromUid = GetGuildServerFromUid(ctx)
		if msg.FromUid == 0 {
			return protocol.NewServerError(status.ErrExchangeGetGuildServer)
		}
		msg.ImType.SenderType = uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_PUBLIC_ACCOUNT)
	} else {
		msg.FromUid = 10000 // TT语音助手
	}

	msg.ToIdList = []uint32{uid}
	msg.ImContent = &apiPB.ImContent{}
	msg.ImContent.TextHlUrl = &apiPB.ImTextWithHighlightUrl{
		Content:    content,
		Hightlight: hlight,
		Url:        url,
	}
	msg.ImType.ContentType = uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL)
	msg.Platform = apiPB.Platform_UNSPECIFIED
	msg.AppPlatform = "all"

	err := apiClient.SendImMsg(ctx, uid, protocol.TT, []*apiPB.ImMsg{msg}, true)


	if err != nil {
		log.ErrorWithCtx(ctx, "SendTTMsg get fail uid:%d err:%v content:%s", uid, err, content)
	}

	totalPre, ok := notify.NotifySync(uid, notify.ImMsgV2)*/

	if guildServer {
		sendReq := &imApiPb.SendPublicAccountTextReq{
			Namespace: "EXCHANGE",
			PublicAccount: &imApiPb.PublicAccount{
				PublicType: imApiPb.PublicAccount_SYSTEM,
				BindedId:   90002, // 会长服务号绑定id
			},
			ToUid: uid,
			Text: &imApiPb.Text{
				Content: content,
			},
		}
		_, err := ImApiCli.SendPublicAccountText(ctx, sendReq)
		if err != nil {
			log.ErrorWithCtx(ctx, "ImApiCli.SendPublicAccountText err=%v", err)
			return err
		}
		log.InfoWithCtx(ctx, "ImApiCli.SendPublicAccountText uid:%d content:%s", uid, content)
	} else {
		_, err := ImApiCli.SimpleSendTTAssistantText(ctx, uid, content, hlight, url)
		if err != nil {
			log.ErrorWithCtx(ctx, "ImApiCli.SimpleSendTTAssistantText err=%v", err)
			return err
		}
		log.InfoWithCtx(ctx, "ImApiCli.SimpleSendTTAssistantText uid:%d content:%s", uid, content)
	}

	return nil
}

func GetOaAccountInfo(ctx context.Context, companyName string, accountId string) ([]oa.AccountInfo, error) {
	return oaClient.GetAccountInfo(ctx, companyName, accountId)
}

func SettlementOver(masterUid uint32, billType settlement_bill2.SettlementBillType, lastBalance uint64) (string, protocol.ServerError) {
	return settleBillClient.CreateSettlementBill(context.Background(), &settlement_bill2.CreateSettlementBillReq{
		BillType:    billType,
		Uid:         masterUid,
		SettleEnd:   uint32(time.Now().Unix()),
		LastBalance: lastBalance,
	})
}

func QueryEncashment(ctx context.Context, t Exchange.QueryEncashmentType, uid uint32, page uint32, rows uint32,
	start string, end string) (*Exchange.QueryEncashmentListResp, error) {
	resp := &Exchange.QueryEncashmentListResp{}
	intAppid := 0
	var clt commission.Client
	switch t {
	case Exchange.QueryEncashmentType_KnightScore:
		clt = commissionPrivateKnightClient
		intAppid = CommissionTtknightAppid
	case Exchange.QueryEncashmentType_KnightGuildScore:
		clt = commissionKnightClient
		intAppid = CommissionExchangeguildknightAppid
	case Exchange.QueryEncashmentType_EsportScore:
		clt = commissionPrivateEsportlient
		intAppid = CommissionEsportAppid
	case Exchange.QueryEncashmentType_EsportGuildScore:
		clt = commissionEsportClient
		intAppid = CommissionEsportGuildAppid
	default:
		return resp, nil
	}
	encashment, err := clt.QueryEncashment(ctx, uint32(intAppid), uid, page, rows, start, end)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryEncashment err=%+v", err)
		return resp, err
	}

	accountData, err := GetAccountData(uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAccountData err=%+v", err)
		return resp, err
	}

	resp.Total = encashment.Total
	for _, elem := range encashment.Rows {
		var data Exchange.QueryEncashmentData
		data.OrderId = elem.OrderId
		data.Uid = uid
		data.Nick = accountData.Nickname
		data.RealName = elem.RealName
		data.IdentityCard = elem.IdentityCard
		data.Amount = elem.Amount
		data.BankCard = elem.BankCard
		data.BankName = elem.BankName
		data.BankcardProvince = elem.BankcardProvince
		data.BankcardCity = elem.BankcardCity
		data.CreateTime = elem.CreateTime

		resp.List = append(resp.List, &data)
	}
	return resp, nil
}

func CheckTBeanProvider(ctx context.Context, uid uint64, scene string, amount int64) (bool, error) {
	return userRiskClient.CheckTBeanProvider(ctx, 0, uid, scene, amount)
}

func CheckTBeanBlackUser(ctx context.Context, uid uint32, blackType string) (bool, error) {
	return tbeanClient.CheckBlackUser(ctx, fmt.Sprintf("%d", uid), blackType)
}

func StartCorpApplyProcess(ctx context.Context, req *oa.ContractApplyReq) (string, error) {
	return oaClient.StartCorpApplyProcess(ctx, req)
}

func BatchGetUploadFileUrl(ctx context.Context, fileKeyList []string) (map[string]string, protocol.ServerError) {
	return guildMangerClient.BatchGetUploadFileUrl(ctx, fileKeyList)
}

func SetTaxRate(ctx context.Context, uid uint32, taxRate uint32, startTime uint32) (*settlement_bill2.SetTaxRateResp, error) {
	return settleBillClient.SetTaxRate(ctx, settlement_bill2.SetTaxRateReq{
		Uid:       uid,
		TaxRate:   taxRate,
		StartTime: startTime,
		Operator:  "",
	})
}

func AppendCorpApplicationArchive(ctx context.Context, instId, archiveUrl, fileName string) error {
	return oaClient.AppendCorpApplicationArchive(ctx, instId, archiveUrl, fileName)
}
