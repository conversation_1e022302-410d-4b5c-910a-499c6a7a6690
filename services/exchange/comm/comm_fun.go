package comm

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-redis/redis"
	"github.com/jinzhu/gorm"
	channellivemgr "golang.52tt.com/clients/channel-live-mgr"
	knightgroupscore "golang.52tt.com/clients/knight-group-score"
	masked_pk_score "golang.52tt.com/clients/masked-pk-score"
	user_risk_api "golang.52tt.com/clients/user-risk-api"
	userscore_go "golang.52tt.com/clients/userscore-go"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tbean"
	"golang.52tt.com/protocol/common/status"
	channellivemgr2 "golang.52tt.com/protocol/services/channellivemgr"
	"golang.52tt.com/protocol/services/esport_score"
	Exchange "golang.52tt.com/protocol/services/exchange"
	knightgroupscore2 "golang.52tt.com/protocol/services/knightgroupscore"
	masked_pk_score2 "golang.52tt.com/protocol/services/masked-pk-score"
	userscore2 "golang.52tt.com/protocol/services/userscore"
	"golang.52tt.com/services/exchange/cache"
	"golang.52tt.com/services/exchange/conf"
	"golang.52tt.com/services/exchange/model"
	"net/http"
	"time"
)

var (
	userscoreGoClient *userscore_go.Client
	channellivemgrClient *channellivemgr.Client
	maskedPkScoreClient  *masked_pk_score.Client
	knightScoreClient    *knightgroupscore.Client
	esportScoreClient    *esport_score.Client
)

//为了更新score时锁住，防止其他业务并行查询扣除，读到脏数据
//这个接口都是用于对公的，所以都是可以提现的
func GetScore(uid uint32, scoreType uint32) (score uint32, err error) {
	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()
	score, err = userscoreGoClient.GetUserScore(ctx, uid, scoreType)
	return
}

func AddScore(uid uint32, amount int32, orderId string, desc string, reason uint8, outsideTime uint32, scoreType uint32) (finalScore uint32, err error) {
	scoreReason := userscore2.ScoreChangeReason_REASON_GUILD_CHANGE_PRIVATE
	switch reason {
	case model.X_GUILD_X_REASON_TO_PRIVATE:
		scoreReason = userscore2.ScoreChangeReason_REASON_GUILD_CHANGE_PRIVATE
	case model.X_GUILD_X_REASON_CANCEL_CONTRACT:
		scoreReason = userscore2.ScoreChangeReason_REASON_GUILD_QUIT
	case model.X_GUILD_X_REASON_OFFICIAL_RECOVERY:
		scoreReason = userscore2.ScoreChangeReason_REASON_GUILD_OFFICIAL_RECYCLE
	case model.X_GUILD_X_REASON_AUTO_EXCHANGE:
		scoreReason = userscore2.ScoreChangeReason_REASON_GUILD_EXCHANGE
	case model.X_REASON_OFFICIAL_RECYCLE:
		scoreReason = userscore2.ScoreChangeReason_REASON_OFFICIAL_RECYCLE
	default:
	}

	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()
	finalScore, err = userscoreGoClient.AddUserScore(ctx, uid, uid, amount,
		uint32(scoreReason), orderId, desc, "", nil, outsideTime, scoreType)
	return
}

//为了减积分获取主播积分锁
func GetAnchorScore(uid uint32) (score uint32, err error) {
	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()
	var resp *channellivemgr2.GetChannelLiveAnchorScoreResp
	resp, err = channellivemgrClient.GetChannelLiveAnchorScore(ctx, uid)
	if err != nil {
		return
	}
	score = resp.Score
	return
}

func AddAnchorScore(uid uint32, amount int32, orderId string, reason uint8, outsideTime uint32) (finalScore uint32, err error) {
	anchorScoreReason := channellivemgr2.AddChannelLiveAnchorScoreReq_GuildChangePrivate
	switch reason {
	case model.X_GUILD_X_REASON_TO_PRIVATE:
		anchorScoreReason = channellivemgr2.AddChannelLiveAnchorScoreReq_GuildChangePrivate
	case model.X_GUILD_X_REASON_CANCEL_CONTRACT:
		anchorScoreReason = channellivemgr2.AddChannelLiveAnchorScoreReq_GuildQuit
	case model.X_GUILD_X_REASON_OFFICIAL_RECOVERY:
		anchorScoreReason = channellivemgr2.AddChannelLiveAnchorScoreReq_GuildOfficalRecycle
	case model.X_GUILD_X_REASON_AUTO_EXCHANGE:
		anchorScoreReason = channellivemgr2.AddChannelLiveAnchorScoreReq_GuildExchange
	case model.X_REASON_OFFICIAL_RECYCLE:
		anchorScoreReason = channellivemgr2.AddChannelLiveAnchorScoreReq_ReclaimScore
	default:

	}

	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()

	req := channellivemgr2.AddChannelLiveAnchorScoreReq{
		Uid:         uid,
		Score:       amount,
		OrderId:     orderId,
		SourceType:  uint32(anchorScoreReason), //来源不确定，如果要新增，需要改httpgo-logic里的channel-live-center，是个主播服务号
		OutsideTime: outsideTime,
	}
	resp, serverErr := channellivemgrClient.AddChannelLiveAnchorScore(ctx, req)
	if serverErr != nil {
		if serverErr.Code() != status.ErrUserScoreOrderExist {
			err = serverErr
			return
		} else {
			log.WarnWithCtx(ctx, "AddAnchorScore %d, %d, %s, %d, %s", uid, amount, orderId, reason, serverErr.Message())
			return
		}
	}
	finalScore = resp.FinalScore
	return
}

//为了减积分获取pk积分锁
func GetPkScore(uid uint32) (score uint32, err error) {
	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()
	score, err = maskedPkScoreClient.GetUserMaskPkScore(ctx, uid)
	return
}

func AddPkScore(uid uint32, amount int32, orderId string, reason uint8, outsideTime uint32) (finalScore uint32, err error) {
	pkScoreReason := masked_pk_score2.SourceType_GuildChangePrivate
	switch reason {
	case model.X_GUILD_X_REASON_TO_PRIVATE:
		pkScoreReason = masked_pk_score2.SourceType_GuildChangePrivate
	case model.X_GUILD_X_REASON_CANCEL_CONTRACT:
		pkScoreReason = masked_pk_score2.SourceType_GuildQuit
	case model.X_GUILD_X_REASON_OFFICIAL_RECOVERY:
		pkScoreReason = masked_pk_score2.SourceType_GuildOfficalRecycle
	case model.X_GUILD_X_REASON_AUTO_EXCHANGE:
		pkScoreReason = masked_pk_score2.SourceType_GuildExchange
	case model.X_REASON_OFFICIAL_RECYCLE:
		pkScoreReason = masked_pk_score2.SourceType_OfficeReclaimScore
	default:

	}

	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()
	var serverErr protocol.ServerError
	finalScore, serverErr = maskedPkScoreClient.AddUserMaskPkScore(ctx, orderId, uid, uint32(pkScoreReason),
		outsideTime, amount, "")
	if serverErr != nil {
		if serverErr.Code() != status.ErrUserScoreOrderExist {
			err = serverErr
			return
		} else {
			log.WarnWithCtx(ctx, "AddPkScore %d, %d, %s, %d, %s", uid, amount, orderId, reason, serverErr.Message())
			return
		}
	}
	return
}


func GetKnightScore(uid uint32) (score uint32, err error) {
	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()
	req := &knightgroupscore2.GetKnightScoreReq{
		OwnerId:              uid,
	}
	knightScore, serverError := knightScoreClient.GetKnightScore(ctx, req)
	if serverError != nil {
		return 0, serverError
	}
	score = uint32(knightScore.GetScore())
	return
}

func AddKnightScore(uid uint32, amount int32, orderId string, reason uint8, outsideTime uint32) (finalScore uint32, err error) {
	scoreReasonDetail := ""
	scoreReason := knightgroupscore2.ReasonType_REASON_GUILD_CHANGE_PRIVATE
	switch reason {
	case model.X_GUILD_X_REASON_TO_PRIVATE:
		scoreReason = knightgroupscore2.ReasonType_REASON_GUILD_CHANGE_PRIVATE
		scoreReasonDetail = "更改结算方式，扣除对公积分"
	case model.X_GUILD_X_REASON_CANCEL_CONTRACT:
		scoreReason = knightgroupscore2.ReasonType_REASON_GUILD_QUIT
		scoreReasonDetail = "个人对公合同失效，扣除对公积分"
	case model.X_GUILD_X_REASON_OFFICIAL_RECOVERY:
		scoreReason = knightgroupscore2.ReasonType_REASON_GUILD_OFFICIAL_RECYCLE
		scoreReasonDetail = "个人对公合同失效，扣除对公积分"
	case model.X_GUILD_X_REASON_AUTO_EXCHANGE:
		scoreReason = knightgroupscore2.ReasonType_REASON_GUILD_EXCHANGE
		scoreReasonDetail = "每日个人对公积分归入会长账户"
	case model.X_REASON_OFFICIAL_RECYCLE:
		scoreReason = knightgroupscore2.ReasonType_REASON_OFFICIAL_RECYCLE
		scoreReasonDetail = "冻结扣除"
	default:

	}

	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()
	req := &knightgroupscore2.AddKnightScoreReq{
		OrderId:      orderId,
		ReasonType:   scoreReason,
		ReasonDetail: scoreReasonDetail,
		OwnerId:      uid,
		Score:        amount,
		ServerTime:   outsideTime,
	}
	scoreResp, serverError := knightScoreClient.AddKnightScore(ctx, req)
	if serverError != nil {
		return 0, serverError
	}
	finalScore = uint32(scoreResp.FinalScore)
	return
}

func GetEsportScore(uid uint32) (score uint32, err error) {
	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()
	req := &esport_score.GetEsportScoreReq{
		Uid: uid,
	}
	esportScore, serverError := esportScoreClient.GetEsportScore(ctx, req)
	if serverError != nil {
		return 0, serverError
	}
	score = uint32(esportScore.GetScore())
	return
}

func AddEsportScore(uid uint32, amount int32, orderId string, reason uint8, outsideTime uint32) (finalScore uint32, err error) {
	scoreReasonDetail := ""
	scoreReason := esport_score.ReasonType_REASON_GUILD_CHANGE_PRIVATE
	switch reason {
	case model.X_GUILD_X_REASON_TO_PRIVATE:
		scoreReason = esport_score.ReasonType_REASON_GUILD_CHANGE_PRIVATE
		scoreReasonDetail = "更改结算方式，扣除对公积分"
	case model.X_GUILD_X_REASON_CANCEL_CONTRACT:
		scoreReason = esport_score.ReasonType_REASON_GUILD_QUIT
		scoreReasonDetail = "个人对公合同失效，扣除对公积分"
	case model.X_GUILD_X_REASON_OFFICIAL_RECOVERY:
		scoreReason = esport_score.ReasonType_REASON_GUILD_OFFICIAL_RECYCLE
		scoreReasonDetail = "官方回收个人对公权扣除积分"
	case model.X_GUILD_X_REASON_AUTO_EXCHANGE:
		scoreReason = esport_score.ReasonType_REASON_GUILD_EXCHANGE
		scoreReasonDetail = "每日个人对公积分归入会长账户"
	case model.X_REASON_OFFICIAL_RECYCLE:
		scoreReason = esport_score.ReasonType_REASON_OFFICIAL_RECYCLE
		scoreReasonDetail = "官方运营回收积分"
	default:

	}

	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Second*3)
	defer cancelFunc()
	req := &esport_score.AddEsportScoreReq{
		OrderId:      orderId,
		ServerTime:   int64(outsideTime),
		ReasonDetail: scoreReasonDetail,
		ReasonType:   scoreReason,
		Uid:          uid,
		Score:        int64(amount),
	}
	scoreResp, serverError := esportScoreClient.AddEsportScore(ctx, req)
	if serverError != nil {
		return 0, serverError
	}
	finalScore = uint32(scoreResp.GetFinalScore())
	return
}

func FlushScoreSumForMaster(db *gorm.DB, rc *redis.Client, rb *redis.Client, masterUid uint32) {
	xdb := model.ExchangeGuildDB{Db: db}

	locker := cache.NewLocker(rc, rb)
	lockV := locker.GenerateValue()
	if !locker.LockFreezeDump(masterUid, lockV) {
		log.Errorf("locker.LockFreezeDump")
		return
	}
	defer locker.UnlockFreezeDump(masterUid, lockV)

	var err error
	log.Infoln("FlushScoreSumForMaster ", masterUid)

	undoAmountDb := model.ExchangeGuildUndoAmountDB{Db: db}
	err = undoAmountDb.Clean(masterUid)
	if err != nil {
		log.Errorf("undoAmountDb.Clean err=%v", err)
		return
	}

	list := []model.ExchangeGuild{}
	err = xdb.GetListWithMasterUid(masterUid, &list)
	if err != nil {
		log.Errorln(err)
		return
	}


	for _, elem := range list {
		uid := elem.UserId

		//判断是否是货币组提现黑名单
		isBlack, err := CheckTBeanBlackUser(context.Background(), uid, tbean.BLACK_TYPE_RECHARGE)
		if err != nil {
			//接口挂了，则放过
			log.Errorf("comm.CheckTBeanBlackUser err=%v", err)
		} else {
			if isBlack {
				log.Infof("comm.CheckTBeanBlackUser uid=%d is black", uid)
				continue
			}
		}


		var score, anchorScore, pkScore, knightScore, esportScore uint32
		xPart := &model.ExchangeScorePartFreezeDB{Db: db}
		score, err = GetScore(uid, uint32(userscore2.ScoreType_Origin))
		if err != nil {
			log.Errorf("GetScore err=%v", err)
			continue
		}
		undoAmountDb = model.ExchangeGuildUndoAmountDB{Db: db}
		undoAmountDb.MasterUid = masterUid
		undoAmountDb.UserId = uid
		undoAmountDb.GuildId = elem.GuildId
		undoAmountDb.CreateAt = uint32(time.Now().Unix())
		undoAmountDb.ScoreType = uint32(Exchange.ExchangeType_PRESENT)
		undoAmountDb.Score = score
		err = undoAmountDb.Save()
		if err != nil {
			log.Errorf("undoAmountDb.Save() err=%v", err)
			continue
		}

		//判断是否冻结
		xsf := model.ExchangeScoreFreezeDB{Db: db}
		err = xsf.GetUidFreeze(uid)
		if err != nil {
			log.Errorf("xsf.GetUidFreeze err=%v", err)
			//数据库报错就跳过，让用户正常走流程
			//return err
		}
		if xsf.FreezeTime > 0 && xsf.UnfreezeTime == 0 {
			log.Infof("uid=%d is frozen", uid)
			undoAmountDb = model.ExchangeGuildUndoAmountDB{Db: db}
			undoAmountDb.MasterUid = masterUid
			undoAmountDb.UserId = uid
			undoAmountDb.ScoreType = uint32(Exchange.ExchangeType_PRESENT)
			undoAmountDb.GuildId = elem.GuildId
			undoAmountDb.Score = score
			undoAmountDb.CreateAt = uint32(time.Now().Unix())
			undoAmountDb.FreezeStatus = model.FreezeStatusAll
			undoAmountDb.FreezeTime = xsf.FreezeTime

			err = undoAmountDb.Save()
			if err != nil {
				log.Errorf("undoAmountDb.Save() err=%v", err)
				continue
			}
		} else { //没有全冻结，才有部分冻结
			partFreezeList, err := xPart.GetFreezeList(uid, model.ScoreTypeUserScore)
			if err != nil {
				log.Errorf("xPart.GetFreezeSumScore err=%v", err)
				continue
			}
			for _, pe := range partFreezeList {
				undoAmountDb = model.ExchangeGuildUndoAmountDB{Db: db}
				undoAmountDb.MasterUid = masterUid
				undoAmountDb.UserId = uid
				undoAmountDb.ScoreType = uint32(Exchange.ExchangeType_PRESENT)
				undoAmountDb.GuildId = elem.GuildId
				undoAmountDb.CreateAt = uint32(time.Now().Unix())
				undoAmountDb.Score = pe.Score
				undoAmountDb.FreezeStatus = model.FreezeStatusPart
				undoAmountDb.FreezeTime = pe.FreezeTime
				undoAmountDb.UnfreezeTime = pe.UnfreezeTime
				err = undoAmountDb.Save()
				if err != nil {
					log.Errorf("undoAmountDb.Save() err=%v", err)
					continue
				}
			}
		}



		anchorScore, err = GetAnchorScore(uid)
		if err != nil {
			log.Errorf("GetAnchorScore err=%v", err)
			continue
		}

		undoAmountDb = model.ExchangeGuildUndoAmountDB{Db: db}
		undoAmountDb.MasterUid = masterUid
		undoAmountDb.UserId = uid
		undoAmountDb.GuildId = elem.GuildId
		undoAmountDb.CreateAt = uint32(time.Now().Unix())
		undoAmountDb.ScoreType = uint32(Exchange.ExchangeType_ANCHOR)
		undoAmountDb.Score = anchorScore
		err = undoAmountDb.Save()
		if err != nil {
			log.Errorf("undoAmountDb.Save() err=%v", err)
			continue
		}

		undoAmountDb = model.ExchangeGuildUndoAmountDB{Db: db}
		undoAmountDb.MasterUid = masterUid
		undoAmountDb.UserId = uid
		undoAmountDb.GuildId = elem.GuildId
		undoAmountDb.CreateAt = uint32(time.Now().Unix())
		pkScore, err = GetPkScore(uid)
		if err != nil {
			log.Errorf("GetPkScore err=%v", err)
			continue
		}
		undoAmountDb.ScoreType = uint32(Exchange.ExchangeType_PK)
		undoAmountDb.Score = pkScore
		err = undoAmountDb.Save()
		if err != nil {
			log.Errorf("undoAmountDb.Save() err=%v", err)
			continue
		}

		undoAmountDb = model.ExchangeGuildUndoAmountDB{Db: db}
		undoAmountDb.MasterUid = masterUid
		undoAmountDb.UserId = uid
		undoAmountDb.GuildId = elem.GuildId
		undoAmountDb.CreateAt = uint32(time.Now().Unix())
		knightScore, err = GetKnightScore(uid)
		if err != nil {
			log.Errorf("GetKnightScore err=%v", err)
			continue
		}
		undoAmountDb.ScoreType = uint32(Exchange.ExchangeType_KNIGHT)
		undoAmountDb.Score = knightScore
		err = undoAmountDb.Save()
		if err != nil {
			log.Errorf("undoAmountDb.Save() err=%v", err)
			continue
		}

		undoAmountDb = model.ExchangeGuildUndoAmountDB{Db: db}
		undoAmountDb.MasterUid = masterUid
		undoAmountDb.UserId = uid
		undoAmountDb.GuildId = elem.GuildId
		undoAmountDb.CreateAt = uint32(time.Now().Unix())
		esportScore, err = GetEsportScore(uid)
		if err != nil {
			log.Errorf("GetEsportScore err=%v", err)
			continue
		}

		ef := model.ExchangeESportScoreFreezeDB{Db: db}
		err = ef.GetUidFreeze(uid)
		if err != nil {
			log.Errorf("ef.GetUidFreeze err=%v", err)
			return
		}
		if ef.Uid == 0 { //如果电竞积分没有被冻结才记录
			log.Infof("ESportScoreFreeze uid=%d", uid)
			undoAmountDb.ScoreType = uint32(Exchange.ExchangeType_ESPORT)
			undoAmountDb.Score = esportScore
			err = undoAmountDb.Save()
			if err != nil {
				log.Errorf("undoAmountDb.Save() err=%v", err)
				continue
			}
		}
	}

}



//处理自动清算积分到暂存表，包括：0公对私切换，1解约，2官方回收，4自动转移到仓库
//产品说跟运营同步，让他们注意一下用户的情况
func HandleAutoCleanScore(db *gorm.DB, redisClient *redis.Client, redisClientBack *redis.Client, uid uint32,
	guildId uint32, autoTime uint32, reason uint8, quit bool) error {
	log.Infoln("HandleAutoCleanScore ", uid, guildId, reason, quit)

	//判断是否冻结
	xsf := model.ExchangeScoreFreezeDB{Db: db}
	err := xsf.GetUidFreeze(uid)
	if err != nil {
		log.Errorf("xsf.GetUidFreeze err=%v", err)
		//数据库报错就跳过，让用户正常走流程
		//return err
	}
	if xsf.FreezeTime > 0 {
		log.Infof("uid=%d is frozen", uid)
		return nil
	}

	xdb := model.ExchangeGuildDB{Db: db}
	xdb.GetUserGuildData(uid)
	if xdb.MasterUid == 0 {
		//没有对公的跳过
		return nil
	}
	if xdb.GuildId != guildId {
		//与签约时的公会不匹配，跳过
		return nil
	}

	//lock，防止多种情况同时触发时，主播被扣除多次
	locker := cache.NewLocker(redisClient, redisClientBack)
	lockV := locker.GenerateValue()
	if !locker.LockCleanScore(uid, lockV) {
		log.Warnln("HandleAutoCleanScore LockCleanScore locked")
		return protocol.NewServerError(status.ErrExchangeLock)
	}
	defer locker.UnlockCleanScore(uid, lockV)

	//确保没有未扣完的积分
	xTransDB := model.ExchangeGuildTransactionsDB{Db: db}
	undoneCount := xTransDB.GetUndoneCount(uid)
	if undoneCount > 0 {
		log.Warnln("HandleAutoCleanScore have UndoneCount ", uid)
		return protocol.NewServerError(status.ErrExchangeUndoneTransaction)
	}

	xGuildTSDB := model.ExchangeGuildTSDB{}
	xGuildTSDB.MasterUid = xdb.MasterUid
	xGuildTSDB.GuildId = guildId
	xGuildTSDB.UserId = uid

	orderId := fmt.Sprintf("ACS_%d_%d_%d", autoTime, guildId, uid)

	if !locker.LockScore(uid, orderId) {
		return protocol.NewServerError(status.ErrExchangeLock)
	}
	defer locker.UnlockScore(uid, orderId)
	score, err := GetScore(uid, uint32(userscore2.ScoreType_Origin))
	if err != nil {
		return err
	}

	//减去部分冻结的积分
	xPart := &model.ExchangeScorePartFreezeDB{Db: db}
	freezeScore, err := xPart.GetFreezeSumScore(uid, model.ScoreTypeUserScore)
	if err != nil {
		log.Errorf("xPart.GetFreezeSumScore err=%v", err)
		return err
	}

	if freezeScore > 0 {
		log.Infof("uid=%d freezeScore=%d score=%d", uid, freezeScore, score)
	}

	if score > freezeScore {
		score -= freezeScore
	} else {
		score = 0
	}

	xGuildTSDB.Score = uint64(score)

	if !locker.LockAnchorScore(uid, orderId) {
		return protocol.NewServerError(status.ErrExchangeLock)
	}
	defer locker.UnlockAnchorScore(uid, orderId)
	anchorScore, err := GetAnchorScore(uid)
	if err != nil {
		return err
	}
	xGuildTSDB.AnchorScore = uint64(anchorScore)

	if !locker.LockPkScore(uid, orderId) {
		return protocol.NewServerError(status.ErrExchangeLock)
	}
	defer locker.UnlockPkScore(uid, orderId)
	pkScore, err := GetPkScore(uid)
	if err != nil {
		return err
	}
	xGuildTSDB.MaskedPkScore = uint64(pkScore)

	if !locker.LockKnightScore(uid, orderId) {
		return protocol.NewServerError(status.ErrExchangeLock)
	}
	defer locker.UnlockKnightScore(uid, orderId)
	knightScore, err := GetKnightScore(uid)
	if err != nil {
		return err
	}
	xGuildTSDB.KnightScore = uint64(knightScore)

	if !locker.LockEsportScore(uid, orderId) {
		return protocol.NewServerError(status.ErrExchangeLock)
	}
	defer locker.UnlockEsportScore(uid, orderId)
	esportScore, err := GetEsportScore(uid)
	if err != nil {
		return err
	}

	//判断电竞积分冻结
	ef := model.ExchangeESportScoreFreezeDB{Db: db}
	err = ef.GetUidFreeze(uid)
	if err != nil {
		log.Errorf("ef.GetUidFreeze err=%v", err)
		return err
	}
	if ef.Uid > 0 { //如果电竞积分被冻结，则不转给会长
		log.Infof("ESportScoreFreeze uid=%d", uid)
		esportScore = 0
	}

	xGuildTSDB.EsportScore = uint64(esportScore)

	tNow := time.Now()
	sumScore := xGuildTSDB.Score + xGuildTSDB.AnchorScore + xGuildTSDB.MaskedPkScore + xGuildTSDB.KnightScore + xGuildTSDB.EsportScore

	//判断是否是货币组提现黑名单
	isBlack, err := CheckTBeanBlackUser(context.Background(), uid, tbean.BLACK_TYPE_RECHARGE)
	if err != nil {
		//接口挂了，则放过
		log.Errorf("comm.CheckTBeanBlackUser err=%v", err)
	} else {
		if isBlack {
			log.Infof("comm.CheckTBeanBlackUser uid=%d is black", uid)
			return protocol.NewServerError(status.ErrExchangeBlackUser, "货币组提现黑名单")
		}
	}

	//判断是否疑似豆商
	var isProvider bool
	isProvider, err = CheckTBeanProvider(context.Background(), uint64(uid), user_risk_api.TBeanScene_PublicContractAutoTransfer, int64(sumScore))
	if err != nil {
		//接口挂了，则放过
		log.Errorf("comm.CheckTBeanProvider err=%v", err)
	} else {
		if isProvider {
			log.Infof("comm.CheckTBeanProvider uid=%d is provider", uid)
			return protocol.NewServerError(status.ErrExchangeStatus, "豆商")
		}
	}

	tx := db.Begin()
	defer tx.Rollback()

	if sumScore > 0 {
		//exchange_guild_ts与exchange_guild_transactions同时入库，exchange_guild删除用户
		xGuildTSDB.Db = tx
		err = xGuildTSDB.Add()
		if err != nil {
			log.Errorln(err)
			err = protocol.NewServerError(status.ErrExchangeDbErr)
			return err
		}

		xGuildTransactionsDB := model.ExchangeGuildTransactionsDB{Db: tx}
		xGuildTransactionsDB.GuildOrderId = orderId
		xGuildTransactionsDB.GuildId = guildId
		xGuildTransactionsDB.UserId = uid
		xGuildTransactionsDB.Score = xGuildTSDB.Score
		xGuildTransactionsDB.AnchorScore = xGuildTSDB.AnchorScore
		xGuildTransactionsDB.MaskedPkScore = xGuildTSDB.MaskedPkScore
		xGuildTransactionsDB.KnightScore = xGuildTSDB.KnightScore
		xGuildTransactionsDB.Reason = reason
		xGuildTransactionsDB.MasterUid = xdb.MasterUid
		xGuildTransactionsDB.Status = model.X_GUILD_X_STATUS_INIT
		xGuildTransactionsDB.Source = model.X_SOURCE_TEMPSAVE
		xGuildTransactionsDB.CreateAt = uint32(tNow.Unix())
		xGuildTransactionsDB.EsportScore = xGuildTSDB.EsportScore
		err = xGuildTransactionsDB.Insert()
		if err != nil {
			log.Errorln(err)
			err = protocol.NewServerError(status.ErrExchangeDbErr)
			return err
		}
	}

	if quit && freezeScore == 0 {
		xg := cache.NewExchangeGuild(tx, redisClient)
		err = xg.DeleteUserGuildExchangeData(uid)
		if err != nil {
			log.Errorln(err)
			err = protocol.NewServerError(status.ErrExchangeDbErr)
			return err
		}

		applyDb := model.ExchangeGuildApplyDB{Db: tx}
		err = applyDb.UpdateAllContractToValid(uid)
		if err != nil {
			log.Errorln(err)
			err = protocol.NewServerError(status.ErrExchangeDbErr)
			return err
		}
	}

	log.Infof("HandleAutoCleanScore orderId=%s Add Master:%d score:%d, anchorScore:%d, pkScore:%d, knightScore:%d, esportScore:%d from %d",
		orderId, xdb.MasterUid, score, anchorScore, pkScore, knightScore, esportScore, uid)

	tx.Commit()

	if sumScore > 0 {
		//扣除各个积分
		if score > 0 {
			_, err = AddScore(uid, -1*int32(score), orderId, "", reason, uint32(tNow.Unix()), uint32(userscore2.ScoreType_Origin))
			if err != nil {
				log.Errorln(err)
				return err
			}
		}

		if anchorScore > 0 {
			_, err = AddAnchorScore(uid, -1*int32(anchorScore), orderId, reason, uint32(tNow.Unix()))
			if err != nil {
				log.Errorln(err)
				return err
			}
		}

		if pkScore > 0 {
			_, err = AddPkScore(uid, -1*int32(pkScore), orderId, reason, uint32(tNow.Unix()))
			if err != nil {
				log.Errorln(err)
				return err
			}
		}

		if knightScore > 0 {
			_, err = AddKnightScore(uid, -1*int32(knightScore), orderId, reason, uint32(tNow.Unix()))
			if err != nil {
				log.Errorln(err)
				return err
			}
		}

		if esportScore > 0 {
			_, err = AddEsportScore(uid, -1*int32(esportScore), orderId, reason, uint32(tNow.Unix()))
			if err != nil {
				log.Errorln(err)
				return err
			}
		}

		xGuildTransactionsDB2 := model.ExchangeGuildTransactionsDB{Db: db}
		err = xGuildTransactionsDB2.UpdateStatus(orderId, xdb.MasterUid, uid, model.X_SOURCE_TEMPSAVE, model.X_GUILD_X_STATUS_DONE)
		if err != nil {
			log.Errorln(err)
			err = protocol.NewServerError(status.ErrExchangeDbErr)
			return err
		}
	}

	return nil
}

type FeiShuMsg struct {
	MsgType string      `json:"msg_type"`
	Content interface{} `json:"content"`
}

type TextContent struct {
	Text string `json:"text"`
}

func sendFeishu(text string) error {
	text = "<" + conf.XGuildConfig.Env + ">" + text

	client := http.Client{
		Timeout: 500 * time.Millisecond,
	}

	msg := FeiShuMsg{MsgType: "text", Content: TextContent{Text: text}}

	b, _ := json.Marshal(msg)

	resp, err := client.Post(conf.XGuildConfig.AlarmUrl, "application/json", bytes.NewReader(b))
	if err != nil {
		log.Errorf("SendAlarmMsgToFeiShu Post fail url : %s err :%v", conf.XGuildConfig.AlarmUrl, err)
		return err
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Errorf("SendAlarmMsgToFeiShu Post response not ok : %s", resp.Status)
		return errors.New(resp.Status)
	}

	return nil
}

func SendInfo(text string) {
	go func() {
		text = "[Info] " + text
		err := sendFeishu(text)
		if err != nil {
			log.Errorln(err)
		}
	}()
}

func SendError(text string) {
	go func() {
		text = "<at user_id=\"all\"></at> [Error] " + text
		err := sendFeishu(text)
		if err != nil {
			log.Errorln(err)
		}
	}()
}

func SendWarning(text string) {
	go func() {
		text = "[Warning] " + text
		err := sendFeishu(text)
		if err != nil {
			log.Errorln(err)
		}
	}()
}

func SendGloryFeishu(text string) error {
	log.Debugf("SendGloryFeishu %s", text)
	client := http.Client{
		Timeout: 500 * time.Millisecond,
	}

	msg := FeiShuMsg{MsgType: "text", Content: TextContent{Text: text}}

	b, _ := json.Marshal(msg)

	resp, err := client.Post(conf.GloryConfig.PushUrl, "application/json", bytes.NewReader(b))
	if err != nil {
		log.Errorf("SendGloryFeishu Post fail url : %s err :%v", conf.XGuildConfig.AlarmUrl, err)
		return err
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Errorf("SendGloryFeishu Post response not ok : %s", resp.Status)
		return errors.New(resp.Status)
	}

	return nil
}
