package server

import (
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	"golang.52tt.com/clients/currency"
	userscore_go "golang.52tt.com/clients/userscore-go"
	"golang.52tt.com/clients/verifycode"
)

var (
	userScoreGoClient userscore_go.IClient
	verifyCodeClient verifycode.IClient
	currencyClient   currency.IClient
	apiCenterClient  apicenter.IClient
)

func Init() {
	userScoreGoClient = userscore_go.NewIClient()
	verifyCodeClient = verifycode.NewIClient()
	currencyClient = currency.NewIClient()
	apiCenterClient = apicenter.NewIClient()
}
