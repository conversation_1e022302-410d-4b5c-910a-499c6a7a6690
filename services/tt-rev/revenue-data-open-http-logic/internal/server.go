package internal

import (
    "context"
    "encoding/json"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/core/service/http"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/clients/account"
    "golang.52tt.com/clients/channel"
    revenue_ext_game "golang.52tt.com/clients/revenue-ext-game"
    youknowwhoCli "golang.52tt.com/clients/you-know-who"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    "golang.52tt.com/protocol/services/demo/echo"
    revenueExtGamePb "golang.52tt.com/protocol/services/revenue-ext-game"
    config "golang.52tt.com/services/tt-rev/revenue-data-open-http-logic/internal/config/ttconfig/revenue_ext_game"
    "os"
    "sort"
    "strconv"
    "strings"
)

const (
	badRequestDesc = "bad request"
	badAppidDesc   = "bad appid"
)

type StartConfig struct {
}

type Server struct {
	DyCfg             config.IDyConfig
	RevenueExtGameCli revenue_ext_game.IClient
	ChannelCli        channel.IClient
	AccountCli        account.IClient
	YKWCli            youknowwhoCli.IClient
}

func NewServer(ctx context.Context, _ *StartConfig) (*Server, error) {
	revenueExtGameCli, _ := revenue_ext_game.NewClient()
	channelCli := channel.NewClient()
	accountCli, _ := account.NewClient()
	YKWCli, _ := youknowwhoCli.NewClient()
	dyCfg, err := config.NewDyConfig()
	if err != nil {
		log.ErrorWithCtx(ctx, "NewServer fail to NewDyConfig, err:%v", err)
		return nil, err
	}

	svr := &Server{
		DyCfg:             dyCfg,
		RevenueExtGameCli: revenueExtGameCli,
		ChannelCli:        channelCli,
		AccountCli:        accountCli,
		YKWCli:            YKWCli,
	}

	return svr, nil
}

func (s *Server) Echo(_ context.Context, w http.ResponseWriter, r *http.Request) {
	// try http.GetUrlParams for url.Values
	msg := &echo.StringMessage{}
	if err := http.ReadJSON(r, msg); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		return
	}

	_ = http.WriteJSON(w, http.StatusOK, msg)
}

func (s *Server) getGameTyAndCid(ctx context.Context, appid, roomId string) (uint32, uint32, error) {
	cfg, err := s.DyCfg.GetConfig().GetGameAppConfByAppid(appid)
	if err != nil {
		log.ErrorWithCtx(ctx, "getGameTyAndCid failed to GetGameAppConfByAppid. appid:%s, roomId:%s, err %+v", appid, roomId, err)
		return 0, 0, err
	}

	gameType := cfg.GameType

	gameResp, err := s.RevenueExtGameCli.GetChannelBySerial(ctx, &revenueExtGamePb.GetChannelBySerialReq{
		GameType: revenueExtGamePb.ExtGameType(gameType), Serial: roomId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getGameTyAndCid failed to GetChannelBySerial, appid:%s, roomId:%s, err %+v", appid, roomId, err)
		return 0, 0, err
	}

	return gameType, gameResp.GetChannelId(), nil
}

func (s *Server) GetChannelInfo(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	req := &GetChannelInfoReq{}
	err := http.ReadJSON(r, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelInfo failed to ReadJSON. %s, err %v", r.RequestURI, err)
		ServeAPIJsonWithError(w, ErrBadRequest, badRequestDesc)
		return
	}

	appid, serial := req.Appid, req.UserCode

	gameType, cid, err := s.getGameTyAndCid(ctx, appid, req.UserCode)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelInfo failed to getGameTyAndCid, req:%+v, gameType:%d, serial:%s, err %+v", req, gameType, serial, err)
		ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
		return
	}

	if cid == 0 {
		ServeAPIJsonWithError(w, ErrRoomNotFound, "room not exist")
		return
	}

	channelResp, err := s.ChannelCli.GetChannelSimpleInfo(ctx, 0, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelInfo failed to GetChannelSimpleInfo, req:%+v, gameType:%d, serial:%s, cid:%d, err %+v", req, gameType, serial, cid, err)
		ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
		return
	}

	userInfo, err := s.AccountCli.GetUser(ctx, channelResp.GetCreaterUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelInfo failed to GetUser, req:%+v, gameType:%d, serial:%s, uid:%d, err %+v",
			req, gameType, serial, channelResp.GetCreaterUid(), err)
		ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
		return
	}

	resp := &GetChannelInfoResp{
		RoomId:    req.UserCode,
		OpenId:    userInfo.GetUsername(),
		AvatarUrl: s.GenUserAvatarUrl(userInfo.GetUsername()),
		UserName:  userInfo.GetNickname(),
	}

	log.InfoWithCtx(ctx, "GetChannelInfo req:%+v, resp:%+v", req, resp)
	ServePB2JSON(w, resp)
}

func (s *Server) GenUserAvatarUrl(userName string) string {
	if userName == "" {
		return ""
	}
	return fmt.Sprintf(s.DyCfg.GetConfig().GetAvatarUrlFormat(), userName)
}

func (s *Server) GetDataReportTaskStatus(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	req := &GetDataReportTaskReq{}
	err := http.ReadJSON(r, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDataReportTaskStatus failed to ReadJSON. body [%s], err %v", r.RequestURI, err)
		ServeAPIJsonWithError(w, ErrBadRequest, badRequestDesc)
		return
	}

	gameType, cid, err := s.getGameTyAndCid(ctx, req.Appid, req.RoomId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDataReportTaskStatus failed to getGameTyAndCid, req:%+v, gameType:%d, err %+v", req, gameType, err)
		ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
		return
	}
	if cid == 0 {
		ServeAPIJsonWithError(w, ErrRoomNotFound, "房间不存在")
		return
	}

	svrResp, err := s.RevenueExtGameCli.GetDataReportTaskStatus(ctx, &revenueExtGamePb.GetDataReportTaskStatusReq{
		GameType:  revenueExtGamePb.ExtGameType(gameType),
		ChannelId: cid,
		TaskType:  req.MsgType,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDataReportTaskStatus failed to GetGameAppConfByAppid req:%+v, err %+v", req, err)
		ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
		return
	}

	ServePB2JSON(w, &GetDataReportTaskResp{
		Status: int32(svrResp.GetStatus()),
	})
}

func (s *Server) GameStartReportCampCfg(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	req := &ReportCampCfgReq{}
	err := http.ReadJSON(r, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GameStartReportCampCfg failed to ReadJSON. body [%s], err %v", r.RequestURI, err)
		ServeAPIJsonWithError(w, ErrBadRequest, badRequestDesc)
		return
	}

	gameType, cid, err := s.getGameTyAndCid(ctx, req.Appid, req.RoomId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GameStartReportCampCfg failed to getGameTyAndCid, req:%+v, gameType:%d, err %+v", req, gameType, err)
		ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
		return
	}
	if cid == 0 {
		ServeAPIJsonWithError(w, ErrRoomNotFound, "房间不存在")
		return
	}

	in := &revenueExtGamePb.SetExtGameOpCfgReq{
		GameType:  revenueExtGamePb.ExtGameType(gameType),
		ChannelId: cid,
		RoundId:   req.RoundId,
	}

	campCfg := &CampCfg{}
    if req.CampCfg != "" {
        err = json.Unmarshal([]byte(req.CampCfg), campCfg)
        if err != nil {
            log.ErrorWithCtx(ctx, "GameStartReportCampCfg failed to json.Unmarshal, req:%+v, gameType:%d, err %+v", req, gameType, err)
            ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
            return
        }
    }

	in.OpCfg = &revenueExtGamePb.ExtGameOpCfg{
		QuickGiftEnable: !campCfg.CloseQuickInfoBySendGift,
		CampButtonList:  fillCampButtonPbList(campCfg.QuickInfos),
	}

	_, err = s.RevenueExtGameCli.SetExtGameOpCfg(ctx, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "GameStartReportCampCfg failed to SetExtGameOpCfg req:%+v, err %+v", req, err)
		ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
		return
	}

	log.InfoWithCtx(ctx, "GameStartReportCampCfg req:%+v", req)
	ServePB2JSON(w, nil)
}

func fillCampButtonPbList(list []*CampButtonCfg) []*revenueExtGamePb.CampButtonCfg {
	out := make([]*revenueExtGamePb.CampButtonCfg, 0, len(list))
	for _, info := range list {
		out = append(out, &revenueExtGamePb.CampButtonCfg{
			ButtonColor: info.ButtonColor,
			ButtonText:  info.ButtonText,
			JoinText:    info.CommentText,
		})
	}

	return out
}

func (s *Server) GameEndReport(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	req := &ReportGameEndReq{}
	err := http.ReadJSON(r, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GameEndReport failed to ReadJSON. body [%s], err %v", r.RequestURI, err)
		ServeAPIJsonWithError(w, ErrBadRequest, badRequestDesc)
		return
	}

	gameType, cid, err := s.getGameTyAndCid(ctx, req.Appid, req.RoomId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GameEndReport failed to getGameTyAndCid, req:%+v, gameType:%d, err %+v", req, gameType, err)
		ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
		return
	}
	if cid == 0 {
		ServeAPIJsonWithError(w, ErrRoomNotFound, "房间不存在")
		return
	}

	in := &revenueExtGamePb.ReportGameEndReq{
		GameType:  revenueExtGamePb.ExtGameType(gameType),
		ChannelId: cid,
		RoundId:   req.RoundId,
	}

	rankList := make([]*RankInfo, 0)
    err = json.Unmarshal([]byte(req.Data), &rankList)
    if err != nil {
        log.ErrorWithCtx(ctx, "GameEndReport failed to json.Unmarshal, req:%+v, gameType:%d, err %+v", req, gameType, err)
    }

	sort.SliceStable(rankList, func(i, j int) bool {
		return rankList[i].Rank < rankList[j].Rank
	})

	in.InfoList, err = s.fillGameSettleRankList(ctx, rankList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GameEndReport failed to fillGameSettleRankList, req:%+v, gameType:%d, err %+v", req, gameType, err)
		ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
		return
	}

	_, err = s.RevenueExtGameCli.ReportGameEnd(ctx, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "GameEndReport failed to ReportGameEnd req:%+v, err %+v", req, err)
		ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
		return
	}

	log.InfoWithCtx(ctx, "GameEndReport req:%+v", req)
	ServePB2JSON(w, nil)
}

func (s *Server) fillGameSettleRankList(ctx context.Context, list []*RankInfo) ([]*revenueExtGamePb.ReportGameEndReq_RankInfo, error) {
	out := make([]*revenueExtGamePb.ReportGameEndReq_RankInfo, 0, len(list))
	if len(list) == 0 {
		return out, nil
	}

	// 1.遍历榜单信息，归类普通用户、ukw用户
	acc2UidMap, fakeUid2RecordUidMap, err := s.classifyAccountAndFakeUid(ctx, list)
	if err != nil {
		log.ErrorWithCtx(ctx, "fillGameSettleRankList failed to classifyAccountAndFakeUid, list:%+v, err %+v", list, err)
		return out, err
	}

	for _, info := range list {
		if info.Score == 0 {
			continue
		}

		rankInfo := &revenueExtGamePb.ReportGameEndReq_RankInfo{
			Score: uint64(info.Score),
		}

		if _, ok := acc2UidMap[info.OpenId]; ok {
			rankInfo.Uid = acc2UidMap[info.OpenId]

		} else if _, ok := fakeUid2RecordUidMap[info.OpenId]; ok {
			// 神秘人recordId特殊处理
			fakeUidStr := strings.ReplaceAll(info.OpenId, "ukw", "")
			fakeUid, _ := strconv.ParseUint(fakeUidStr, 10, 32)
			rankInfo.Uid = fakeUid2RecordUidMap[info.OpenId]
			rankInfo.FakeUid = uint32(fakeUid)

		} else {
			log.WarnWithCtx(ctx, "fillGameSettleRankList wrong user, info:%+v", info)
			continue
		}
		out = append(out, rankInfo)
	}

	return out, nil
}

func (s *Server) ReportUserCamp(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	req := &ReportUserCampReq{}
	err := http.ReadJSON(r, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportUserCamp failed to ReadJSON. body [%s], err %v", r.RequestURI, err)
		ServeAPIJsonWithError(w, ErrBadRequest, badRequestDesc)
		return
	}

	if req.Openid == "" {
		ServeAPIJsonWithError(w, ErrRoomNotFound, "用户不存在")
		return
	}

	gameType, cid, err := s.getGameTyAndCid(ctx, req.Appid, req.RoomId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportUserCamp failed to getGameTyAndCid, req:%+v, gameType:%d, err %+v", req, gameType, err)
		ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
		return
	}
	if cid == 0 {
		ServeAPIJsonWithError(w, ErrRoomNotFound, "房间不存在")
		return
	}

	in := &revenueExtGamePb.SetUserCampReq{
		GameType:  revenueExtGamePb.ExtGameType(gameType),
		ChannelId: cid,
		RoundId:   req.RoundId,
		Camp:      req.Camp,
	}

	in.Uid, in.RecodeUid, err = s.getUserId(ctx, req.Openid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportUserCamp failed to getUserId req:%+v, err %+v", req, err)
		ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
		return
	}

	_, err = s.RevenueExtGameCli.SetUserCamp(ctx, in)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportUserCamp failed to SetUserCamp req:%+v, err %+v", req, err)
		ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
		return
	}

	log.InfoWithCtx(ctx, "ReportUserCamp req:%+v", req)
	ServePB2JSON(w, nil)
}

func (s *Server) getUserId(ctx context.Context, openid string) (uid, recordUid uint32, err error) {
	var serr protocol.ServerError
	uid, _, serr = s.AccountCli.GetUidByName(ctx, openid)
	if serr == nil {
		recordUid = uid
		return

	} else if serr.Code() != status.ErrAccountNotExist {
		log.ErrorWithCtx(ctx, "account.GetUidByName failed, openid:%s, err:%v", openid, serr)
		err = serr
		return

	} else {
		uid, recordUid, err = s.YKWCli.ChangeUKWAccountToUid(ctx, openid)
		if err != nil {
			log.ErrorWithCtx(ctx, "you-know-who.ChangeUKWAccountToUid failed, openid:%s, err:%v", openid, err)
			return
		}
		if uid == 0 {
			log.ErrorWithCtx(ctx, "you-know-who ChangeUKWAccountToUid, empty uid, openid:%s", openid)
			err = serr
			return
		}
	}

	return
}

func (s *Server) StartDataReportTask(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	req := &StartDataReportTaskReq{}
	err := http.ReadJSON(r, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartDataReportTask failed to ReadJSON. body [%s], err %v", r.RequestURI, err)
		ServeAPIJsonWithError(w, ErrBadRequest, badRequestDesc)
		return
	}

	if req.MsgType == "" {
		ServeAPIJsonWithError(w, ErrBadRequest, badRequestDesc)
		return
	}

	gameType, cid, err := s.getGameTyAndCid(ctx, req.Appid, req.RoomId)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartDataReportTask failed to getGameTyAndCid, req:%+v, gameType:%d, err %+v", req, gameType, err)
		ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
		return
	}
	if cid == 0 {
		ServeAPIJsonWithError(w, ErrRoomNotFound, "房间不存在")
		return
	}

	_, err = s.RevenueExtGameCli.StartDataReport(ctx, &revenueExtGamePb.StartDataReportReq{
		GameType:     revenueExtGamePb.ExtGameType(gameType),
		ChannelId:    cid,
		TaskTypeList: []string{req.MsgType},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "StartDataReportTask failed to StartDataReport req:%+v, err %+v", req, err)
		ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
        return
	}

	log.InfoWithCtx(ctx, "StartDataReportTask req:%+v", req)
	ServePB2JSON(w, &StartDataReportTaskResp{
		TaskId: req.RoomId + req.MsgType,
	})
}

func (s *Server) StopDataReportTask(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	req := &StopDataReportTaskReq{}
	err := http.ReadJSON(r, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "StopDataReportTask failed to ReadJSON. body [%s], err %v", r.RequestURI, err)
		ServeAPIJsonWithError(w, ErrBadRequest, badRequestDesc)
		return
	}

	if req.MsgType == "" {
		ServeAPIJsonWithError(w, ErrBadRequest, badRequestDesc)
		return
	}

	gameType, cid, err := s.getGameTyAndCid(ctx, req.Appid, req.RoomId)
	if err != nil {
		log.ErrorWithCtx(ctx, "StopDataReportTask failed to getGameTyAndCid, req:%+v, gameType:%d, err %+v", req, gameType, err)
		ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
		return
	}
	if cid == 0 {
		ServeAPIJsonWithError(w, ErrRoomNotFound, "房间不存在")
		return
	}

	_, err = s.RevenueExtGameCli.StopDataReport(ctx, &revenueExtGamePb.StopDataReportReq{
		GameType:     revenueExtGamePb.ExtGameType(gameType),
		ChannelId:    cid,
		TaskTypeList: []string{req.MsgType},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "StopDataReportTask failed to StartDataReport req:%+v, err %+v", req, err)
		ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
		return
	}

	log.InfoWithCtx(ctx, "StopDataReportTask req:%+v", req)
	ServePB2JSON(w, nil)
}

func (s *Server) ReportScoreRank(ctx context.Context, w http.ResponseWriter, r *http.Request) {
	req := &ReportScoreRankReq{}

	err := http.RepeatableReadJSON(r, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportScoreRank failed to ReadJSON. body [%s], err %v", r.RequestURI, err)
		ServeAPIJsonWithError(w, ErrBadRequest, badRequestDesc)
		return
	}

	body, err := http.ReadBodyBytes(r)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportScoreRank failed to http.ReadBodyBytes. body [%s], err %v", r.RequestURI, err)
		ServeAPIJsonWithError(w, ErrBadRequest, badRequestDesc)
		return
	}

	// 签名校验
	if !s.ValidateSignature(ctx, r, req.Appid, body) {
		log.ErrorWithCtx(ctx, "ReportScoreRank failed to ValidateSignature. body [%s], err %v", string(body), err)
		ServeAPIJsonWithError(w, ErrBadRequest, "signature error")
		return
	}

	if len(req.RankList) == 0 {
		log.InfoWithCtx(ctx, "ReportScoreRank req:%+v", req)
		ServePB2JSON(w, nil)
		return
	}

	gameType, err := s.batGetGameType(ctx, req.Appid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportScoreRank failed to batGetGameType, req:%+v, err %+v", req, err)
		ServeAPIJsonWithError(w, ErrBadAppid, "appid Invalid")
		return
	}

	// 1.遍历榜单信息，归类普通用户、ukw用户
	acc2UidMap, fakeUid2RecordUidMap, err := s.classifyAccountAndFakeUid(ctx, req.RankList)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportScoreRank failed to classifyAccountAndFakeUid, req:%+v, err %+v", req, err)
		ServeAPIJsonWithError(w, ErrInternalSysErr, "sys error")
		return
	}

	// 3.组装SetGameRankReq
	setReq := &revenueExtGamePb.SetGameScoresRankReq{
		GameType: revenueExtGamePb.ExtGameType(gameType),
		RankType: uint32(req.RankType),
		RankName: req.RankName,
	}

	for _, info := range req.RankList {
		rankInfo := &revenueExtGamePb.SetGameScoresRankReq_RankInfo{
			Scores: uint64(info.Score),
		}

		if _, ok := acc2UidMap[info.OpenId]; ok {
			rankInfo.Uid = acc2UidMap[info.OpenId]
			rankInfo.RecordUid = acc2UidMap[info.OpenId]

		} else if _, ok := fakeUid2RecordUidMap[info.OpenId]; ok {
			// 神秘人recordId特殊处理
			fakeUidStr := strings.ReplaceAll(info.OpenId, "ukw", "")
			fakeUid, _ := strconv.ParseUint(fakeUidStr, 10, 32)
			rankInfo.Uid = fakeUid2RecordUidMap[info.OpenId]
			rankInfo.RecordUid = uint32(fakeUid)

		} else {
			log.InfoWithCtx(ctx, "ReportScoreRank info:%+v", info)
			continue
		}
		setReq.InfoList = append(setReq.InfoList, rankInfo)
	}

	// 4.调用SetGameRank方法全量更新游戏榜单
	_, err = s.RevenueExtGameCli.SetGameScoresRank(ctx, setReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportScoreRank failed to SetGameScoresRank req:%+v, err %+v", req, err)
		ServeAPIJsonWithError(w, ErrInternalSysErr, "sys error")
		return
	}

	var rankInfo string
	for _, info := range req.RankList {
		rankInfo += fmt.Sprintf("rank_info:%+v", info)
	}

	log.InfoWithCtx(ctx, "ReportScoreRank req:%+v,rankInfo:%s", req, rankInfo)
	ServePB2JSON(w, nil)
}

func (s *Server) batGetGameType(ctx context.Context, appid string) (uint32, error) {
	cfg, err := s.DyCfg.GetConfig().GetGameAppConfByAppid(appid)
	if err != nil {
		log.ErrorWithCtx(ctx, "getGameTyAndCid failed to GetGameAppConfByAppid. appid:%s, err %+v", appid, err)
		return 0, err
	}

	return cfg.GameType, nil
}

func (s *Server) classifyAccountAndFakeUid(ctx context.Context, rankList []*RankInfo) (map[string]uint32, map[string]uint32, error) {
	accountList := make([]string, 0)
	fakeUidList := make([]string, 0)

	for _, info := range rankList {
		if strings.Contains(info.OpenId, "ukw") {
			fakeUidList = append(fakeUidList, info.OpenId)

		} else {
			accountList = append(accountList, info.OpenId)
		}
	}

	// 2.反向查用户uid、神秘人recordUid
	acc2UidMap, err := s.batGetUserIdByName(ctx, accountList)
	if err != nil {
		log.ErrorWithCtx(ctx, "classifyAccountAndFakeUid failed to batGetUserIdByName, rankList:%+v, err %+v", rankList, err)
		return acc2UidMap, make(map[string]uint32), err
	}

	if len(fakeUidList) == 0 {
		return acc2UidMap, make(map[string]uint32), nil
	}

	fakeUid2RecordUidMap, err := s.batGetUidByFakeAccount(ctx, fakeUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "classifyAccountAndFakeUid failed to batGetUidByFakeUid, rankList:%+v, err %+v", rankList, err)
		return acc2UidMap, fakeUid2RecordUidMap, err
	}

	return acc2UidMap, fakeUid2RecordUidMap, err
}

func (s *Server) batGetUserIdByName(ctx context.Context, accountList []string) (map[string]uint32, error) {
	acc2UidMap := make(map[string]uint32)
	accountUidPair, err := s.AccountCli.BatchQueryUidListByAccount(ctx, accountList)
	if err != nil {
		log.ErrorWithCtx(ctx, "batGetUserIdByName failed to BatchQueryUidListByAccount, accountList:%+v, err %+v", accountList, err)
		return nil, err
	}

	for _, v := range accountUidPair {
		acc2UidMap[v.GetKey()] = v.GetUid()
	}

	return acc2UidMap, nil
}

func (s *Server) batGetUidByFakeAccount(ctx context.Context, fakeAccountList []string) (map[string]uint32, error) {
	fAcc2UidMap := make(map[string]uint32)

	fakeUidList := make([]uint32, 0)
	for _, v := range fakeAccountList {
		fakeUidStr := strings.ReplaceAll(v, "ukw", "")
		fakeUid, _ := strconv.ParseUint(fakeUidStr, 10, 32)
		fakeUidList = append(fakeUidList, uint32(fakeUid))
	}

	fid2UidPair, err := s.YKWCli.BatchGetTrueUidByFake(ctx, fakeUidList)

	if err != nil {
		log.ErrorWithCtx(ctx, "batGetUidByFakeUid failed to BatchQueryUidListByAccount, fakeUidList:%+v, err %+v", fakeUidList, err)
		return nil, err
	}

	for _, v := range fid2UidPair {
		fAcc2UidMap[fmt.Sprintf("ukw%d", v.GetReqUid())] = v.GetUid()
	}

	return fAcc2UidMap, nil
}

// MockTaskDataReport 模拟场景数据（送礼/评论）上报
func (s *Server) MockTaskDataReport(ctx context.Context, w http.ResponseWriter, r *http.Request) {
    if os.Getenv("MY_CLUSTER") != "testing" {
        log.ErrorWithCtx(ctx, "MockTaskDataReport is not allowed in env:%s", os.Getenv("MY_CLUSTER"))
        ServeAPIJsonWithError(w, ErrInternalSysErr, "not allowed")
        return
    }

    req := &MockDataReportReq{}
    err := http.ReadJSON(r, req)
    if err != nil {
        log.ErrorWithCtx(ctx, "MockTaskDataReport failed to ReadJSON. body [%s], err %v", r.RequestURI, err)
        ServeAPIJsonWithError(w, ErrBadRequest, badRequestDesc)
        return
    }

    if req.MsgType == "" {
        ServeAPIJsonWithError(w, ErrBadRequest, badRequestDesc)
        return
    }

    gameType, cid, err := s.getGameTyAndCid(ctx, req.Appid, req.RoomId)
    if err != nil {
        log.ErrorWithCtx(ctx, "MockTaskDataReport failed to getGameTyAndCid, req:%+v, gameType:%d, err %+v", req, gameType, err)
        ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
        return
    }
    if cid == 0 {
        ServeAPIJsonWithError(w, ErrRoomNotFound, "房间不存在")
        return
    }

    in := &revenueExtGamePb.ManualDataReportDemoReq{
        ChannelId:  cid,
        MsgType:    req.MsgType,
        PublicText: req.CommentText,
        GiftInfo: &revenueExtGamePb.GiftInfo {
            GiftId:     req.GiftId,
            GiftPrice:  req.GiftPrice,
            GiftAmount: req.GiftAmount,
        },
    }

    _, err = s.RevenueExtGameCli.ManualDataReportDemo(ctx, in)
    if err != nil {
        log.ErrorWithCtx(ctx, "MockTaskDataReport failed to MockDataReport req:%+v, err %+v", req, err)
        ServeAPIJsonWithError(w, ErrInternalSysErr, err.Error())
        return
    }

    log.InfoWithCtx(ctx, "MockTaskDataReport req:%+v", req)
    ServePB2JSON(w, nil)
}
