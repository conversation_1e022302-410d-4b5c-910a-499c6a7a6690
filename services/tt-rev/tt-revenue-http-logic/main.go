package main

import (
    "context"
    "golang.52tt.com/clients/gnobility"
    "golang.52tt.com/clients/nobility"
    userprofileapi "golang.52tt.com/clients/user-profile-api"
    channel_wedding_cli "golang.52tt.com/protocol/services/channel-wedding"
    channel_wedding_plan "golang.52tt.com/protocol/services/channel-wedding-plan"
    "golang.52tt.com/protocol/services/esport_godlevel"
    "golang.52tt.com/protocol/services/esport_hall"
    "golang.52tt.com/protocol/services/esport_score"
    fellow_svr "golang.52tt.com/protocol/services/fellow-svr"
    channel_wedding "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/channel-wedding"
    richer_birthday "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/richer-birthday"
    virtual_image "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/virtual-image"

    headdynamicimage "golang.52tt.com/clients/head-dynamic-image-logic"
    "golang.52tt.com/clients/ugc/friendship"
    ext_push "golang.52tt.com/services/tt-rev/esport/common/push"

    netHttp "net/http"
    _ "net/http/pprof"
    "time"

    censoring_proxy "golang.52tt.com/clients/censoring-proxy"
    ttcProxy "golang.52tt.com/clients/ttc-proxy"
    esport_statistics "golang.52tt.com/protocol/services/esport-statistics"
    esport_trade "golang.52tt.com/protocol/services/esport-trade"

    esport_role "golang.52tt.com/clients/esport-role"
    riskMngApi "golang.52tt.com/clients/risk-mng-api"
    esport_skill "golang.52tt.com/protocol/services/esport-skill"
    esport_trade_appeal "golang.52tt.com/protocol/services/esport-trade-appeal"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/esport"

    "golang.52tt.com/clients/darkserver"

    "gitlab.ttyuyin.com/avengers/tyr/core/service/http"
    startup "gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/http"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channelol_go"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/clients/account"
    anchorcontract_go "golang.52tt.com/clients/anchorcontract-go"
    "golang.52tt.com/clients/appconfig"
    "golang.52tt.com/clients/backpack"
    backpackBaseClient "golang.52tt.com/clients/backpack-base"
    "golang.52tt.com/clients/channel"
    channelcpgame "golang.52tt.com/clients/channel-cp-game"
    "golang.52tt.com/clients/channelol"
    "golang.52tt.com/clients/conversion"
    darkGiftBonusCli "golang.52tt.com/clients/dark-gift-bonus"
    fellowsvr "golang.52tt.com/clients/fellow-svr"
    "golang.52tt.com/clients/guild"
    headwarClient "golang.52tt.com/clients/headwear-go"
    huntmonster "golang.52tt.com/clients/hunt-monster"
    huntmonstermission "golang.52tt.com/clients/hunt-monster-mission"
    iop_proxy "golang.52tt.com/clients/iop-proxy"
    maskedPKCli "golang.52tt.com/clients/masked-pk-score"
    piaCli "golang.52tt.com/clients/pia"
    present_week_card_cli "golang.52tt.com/clients/present-week-card"
    pushNotification "golang.52tt.com/clients/push-notification/v2"
    superplayersvr "golang.52tt.com/clients/super-player-svr"
    ttPeripheralMall "golang.52tt.com/clients/tt-peripheral-mall"
    userPresent "golang.52tt.com/clients/userpresent"
    youknowwho "golang.52tt.com/clients/you-know-who"
    "golang.52tt.com/pkg/web"
    glorylotterypb "golang.52tt.com/protocol/services/glory-lottery"
    gloryMagicPB "golang.52tt.com/protocol/services/glory-magic"
    _ "golang.52tt.com/services/recommend-dialog/tools/grpc_proxy/enable"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/conf"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/channel-cp-game"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/compose-gift"
    conversion_mgr "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/conversion"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/dark-compose-gift"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/dark-gift-bonus"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/fellow"
    businessConf "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/fellow/conf"
    glorylottery "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/glory-lottery"
    glory_magic "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/glory-magic"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/hunt-monster-mission"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/masked-pk"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/pia"
    present_week_card "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/present-week-card"
    peripheralMall "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/tt-peripheral-mall"
    user_recall_award "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/user-recall-award"
    wechatCtrl "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/wechat"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/rpc"

    _ "golang.52tt.com/pkg/hub/tyr/compatible/http" // 兼容tyr公共库
    channel_wedding_conf "golang.52tt.com/protocol/services/channel-wedding-conf"
    new_recharge_act "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/new-recharge-act"
    star_train "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/control/star-train"
)

var (
    cfg       = &conf.ServiceConfig{}
    closeFunc func(ctx context.Context)
)

func main() {

    initializeFunc := func(ctx context.Context, r *http.Router) error {

        // client 初始化
        piaClient, _ := piaCli.NewClient()
        accountClient := account.NewIClient()
        youKnowWhoClient := youknowwho.NewIClient()
        huntMonsterMissionCli := huntmonstermission.NewIClient()
        huntmonsterCli := huntmonster.NewIClient()
        conversionCli, err := conversion.NewClient()
        if err != nil {
            log.Errorf("fail to new conversion client")
            return err
        }
        backpackCli := backpack.NewIClient()
        presentCli := userPresent.NewIClient()
        channelCpGameCli := channelcpgame.NewIClient()
        darkGiftBonusClient := darkGiftBonusCli.NewIClient()
        channelCli := channel.NewIClient()
        guildClient := guild.NewIClient()
        fellowClient := fellowsvr.NewIClient()
        originFellowClient, _ := fellow_svr.NewClient(context.Background())
        superPlayerCli := superplayersvr.NewIClient()
        maskedPkScoreCli := maskedPKCli.NewIClient()
        anchorContractGoClient := anchorcontract_go.NewIClient()
        channelolClient := channelol.NewIClient()
        channelOlGoClient, _ := channelol_go.NewClient(context.Background())
        pushCli := pushNotification.NewIClient()
        headwarCli := headwarClient.NewIClient()
        backpackBaseCli := backpackBaseClient.NewIClient()
        peripheralMallCli := ttPeripheralMall.NewIClient()
        iopCli, _ := iop_proxy.NewClient()
        appCfgCli := appconfig.NewClient()
        presentWeekCardCli, _ := present_week_card_cli.NewClient()
        darkCli, _ := darkserver.NewClient()
        refundService, err := esport_trade_appeal.NewClient(ctx)
        ttcProxyClient, _ := ttcProxy.NewClient()
        nobilityCli, _ := nobility.NewClient()
        gNobilityCli, _ := gnobility.NewClient()
        if err != nil {
            log.Errorf("fail to new esport trade appeal client")
            return err
        }
        esportRoleCli := esport_role.NewIClient()
        riskMngApiCli, _ := riskMngApi.NewClient()
        esportSkillCli, _ := esport_skill.NewClient(ctx)
        esportCli, _ := esport_hall.NewClient(ctx)
        esportScoreCli, _ := esport_score.NewClient(ctx)
        esportTrakeClient, err := esport_trade.NewClient(ctx)
        if err != nil {
            log.Errorf("fail to new esport trade client")
            return err
        }
        tshellCli := censoring_proxy.NewIClient()

        headDynamicImageCli := headdynamicimage.NewIClient()
        friendShipCli := friendship.NewIClient()

        godLvCli, err := esport_godlevel.NewClient(ctx)
        if err != nil {
            log.Errorf("New ESportGodLevelMgr failed, err:%v", err)
            return nil
        }

        userprofileCli := userprofileapi.NewIClient()
        fellowCliNew, err := fellow_svr.NewClient(ctx)
        if err != nil {
            log.Errorf("New fellowCli failed, err:%v", err)
            return nil
        }
        // 创建动态配置
        businessConfManager, businessCleanup, err := conf.NewBusinessConfManager(cfg)
        if err != nil {
            log.Errorf("fail to new business conf manager")
            return err
        }

        extPushCli := ext_push.NewPushCli()

        p := r.Child("/tt-revenue-http-logic")

        /*** 礼物合成 ***/
        giftComposeMgr := compose_gift.NewGiftComposeMgr(conversionCli, backpackCli, presentCli)
        giftCompose := p.Child("/compose-gift")
        giftCompose.POST("/mix", authHandleInterceptor(giftComposeMgr.ComposeHandler))
        giftCompose.POST("/getMyGifts", authHandleInterceptor(giftComposeMgr.GetUserMaterialListHandle))
        giftCompose.POST("/getSyntheticLogs", authHandleInterceptor(giftComposeMgr.GetUserComposeLogsHandler))
        giftCompose.POST("/getSyntheticConfigs", authHandleInterceptor(giftComposeMgr.GetComposeGiftConfHandle))

        /** 黑暗系礼物合成 **/
        darkGiftMgr := dark_compose_gift.NewDarkComposeGiftMgr(conversionCli, backpackCli, presentCli)
        darkGiftCompose := p.Child("/dark-compose-gift")
        darkGiftCompose.POST("/compose", authHandleInterceptor(darkGiftMgr.DarkComposeHandle))
        darkGiftCompose.POST("/getComposeHomePage", authHandleInterceptor(darkGiftMgr.GetComposeHomePageHandle))
        darkGiftCompose.POST("/getGift2Materials", authHandleInterceptor(darkGiftMgr.GetDarkGift2MaterialsHandle))
        darkGiftCompose.POST("/getMyDarkMaterials", authHandleInterceptor(darkGiftMgr.GetAllUserDarkMaterialsHandle))
        darkGiftCompose.POST("/getMyComposeLog", authHandleInterceptor(darkGiftMgr.GetDarkComposeLogsHandle))
        darkGiftCompose.POST("/checkComposeEntry", authHandleInterceptor(darkGiftMgr.CheckComposeEntryHandle))

        /** 碎片兑换-from C++重构 **/
        conversionMgr, err := conversion_mgr.NewConversionMgr(conversionCli, backpackBaseCli, presentCli, headwarCli, friendShipCli, accountClient)
        if err != nil {
            log.Errorf("fail to new conversion mgr")
            return err
        }
        conversionSvc := p.Child("/conversion")
        conversionSvc.POST("/getconfig", noAuthHandleInterceptor(conversionMgr.GetAllConversionConfig))
        conversionSvc.POST("/conversion", authHandleInterceptor(conversionMgr.ConversionHandler))
        conversionSvc.POST("/getlogs", noAuthHandleInterceptor(conversionMgr.GetLogsHandler))
        conversionSvc.POST("/activite_user_decoration", authHandleInterceptor(conversionMgr.ActiveiteUserDecorationHandler))
        conversionSvc.POST("/get_magic_fragment", authHandleInterceptor(conversionMgr.GetSmashBingoPrizeHandler))
        conversionSvc.POST("/get_fragment_conf", authHandleInterceptor(conversionMgr.BatGetFragmentConfHandler))

        /** 黑暗礼物奖励 **/
        darkGitfBonusMgr := dark_gift_bonus.NewDrakGiftBonusMgr(darkGiftBonusClient)
        darkGiftBonus := p.Child("/dark-gift-bonus")
        darkGiftBonus.POST("/getUserBonusLog", authHandleInterceptor(darkGitfBonusMgr.GetUserDarkGiftBonusLogs))

        // 打龙任务详情页接口
        huntMonsterMgr := hunt_monster_mission.NewHuntMonsterMgr(huntMonsterMissionCli, huntmonsterCli, accountClient)
        huntMonsterMission := p.Child("/hunt-monster-mission")
        huntMonsterMission.POST("/getUserHuntMonsterMission", authHandleInterceptor(huntMonsterMgr.GetHuntMonsterPropsInfo))

        /*** 蒙面pk ***/
        maskedPk := p.Child("/masked-pk")
        maskedPkMgr := masked_pk.NewMaskPKMgr(maskedPkScoreCli, anchorContractGoClient)
        maskedPk.POST("/getScore", authHandleInterceptor(maskedPkMgr.GetMaskedPkScoreHandler))
        maskedPk.POST("/getScoreLog", authHandleInterceptor(maskedPkMgr.GetMaskedPkScoreLogHandler))

        /*** 挚友 ***/
        fellowSvrHandler := p.Child("/fellow-svr")
        bc, err := businessConf.NewBusinessConfManager()
        if err != nil {
            panic(err)
        }
        httpClient := &netHttp.Client{Timeout: time.Second * 5}
        fellowMgr := fellow.NewFellowMgr(&rpc.ClientSet{
            ChannelCli:         channelCli,
            GuildClient:        guildClient,
            OriginFellowClient: originFellowClient,
            FellowClient:       fellowClient,
            ChannelCpGameCli:   channelCpGameCli,
            AccountCli:         accountClient,
            SuperPlayerCli:     superPlayerCli,
            YouKnowWhoClient:   youKnowWhoClient,
            ChannelOlCli:       channelolClient,
        }, cfg, httpClient, bc)
        fellowSvrHandler.POST("/getFellowInfo", authHandleInterceptor(fellowMgr.GetFellowInfoHandler))
        fellowSvrHandler.POST("/getPiecesInfo", authHandleInterceptor(fellowMgr.GetPiecesInfo))
        fellowSvrHandler.POST("/setNameplateInUse", authHandleInterceptor(fellowMgr.SetNameplateInUse))
        fellowSvrHandler.POST("/getCPStrengthHistory", authHandleInterceptor(fellowMgr.GetCPStrengthHistory))
        fellowSvrHandler.POST("/unboundFellow", authHandleInterceptor(fellowMgr.UnboundFellow))
        fellowSvrHandler.POST("/cancelUnboundFellow", authHandleInterceptor(fellowMgr.CancelUnboundFellow))
        fellowSvrHandler.POST("/directUnboundFellow", authHandleInterceptor(fellowMgr.DirectUnboundFellow))
        fellowSvrHandler.POST("/getAccompanyRankList", authHandleInterceptor(fellowMgr.GetAccompanyRankList))
        fellowSvrHandler.POST("/expandMyAccompanyRankList", authHandleInterceptor(fellowMgr.ExpandMyAccompanyRankList))
        fellowSvrHandler.POST("/getUserLevelAwardList", authHandleInterceptor(fellowMgr.GetUserLevelAwardList))
        fellowSvrHandler.POST("/receiveLevelAward", authHandleInterceptor(fellowMgr.ReceiveLevelAward))
        fellowSvrHandler.POST("/getFellowAccountInfo", authHandleInterceptor(fellowMgr.GetFellowAccountInfo))

        /*** 挚友小屋相关 ***/
        fellowHouseHandler := p.Child("/fellow-house")
        fellowHouseHandler.POST("/get_user_house_list", authHandleInterceptor(fellowMgr.GetUserFellowHouseList))
        fellowHouseHandler.POST("/get_user_history_house_list", authHandleInterceptor(fellowMgr.GetUserHistoryFellowHouseList))
        fellowHouseHandler.POST("/set_user_house_inuse", authHandleInterceptor(fellowMgr.SetUserFellowHouseInuse))
        fellowHouseHandler.POST("/get_house_list", authHandleInterceptor(fellowMgr.GetFellowHouseProductList))
        fellowHouseHandler.POST("/buy_fellow_house", authHandleInterceptor(fellowMgr.BuyFellowHouse))
        fellowHouseHandler.POST("/get_user_fellow_list", authHandleInterceptor(fellowMgr.GetUserFellowList))
        fellowHouseHandler.POST("/get_bean_balance", authHandleInterceptor(fellowMgr.GetUserTBeanBalance))

        /***cp战相关***/
        channelCpGameMgr := channel_cp_game.NewChannelCpGameMgr(channelCpGameCli, accountClient)
        channelCpGameRouter := p.Child("/channel-cp-game")
        channelCpGameRouter.POST("/getGodRankList", authHandleInterceptor(channelCpGameMgr.GetChannelCpGameGodRank))
        channelCpGameRouter.POST("/getNameplateInfo", authHandleInterceptor(channelCpGameMgr.GetCpGameNameplateInfo))

        // pia戏服务
        piaMgr := pia.NewPiaMgr(piaClient, accountClient, youKnowWhoClient, pushCli, channelCli)
        piaSvrRouter := p.Child("/pia")
        piaSvrRouter.POST("/getDramaDetail", authHandleInterceptor(piaMgr.GetDramaDetail))
        piaSvrRouter.POST("/getDramaExpansion", authHandleInterceptor(piaMgr.GetDramaExpansion))
        piaSvrRouter.POST("/dramaReport/sendMessage", noAuthHandleInterceptor(piaMgr.HandleDramaReportResult))
        piaSvrRouter.POST("/addDramaFeedBack", authHandleInterceptor(piaMgr.PiaAddDramaFeedBack))
        piaSvrRouter.POST("/GetAuthorWorksList", authHandleInterceptor(piaMgr.GetAuthorWorksList))
        piaSvrRouter.POST("/GetAuthorGenInfo", authHandleInterceptor(piaMgr.GetAuthorGenInfo))
        piaSvrRouter.POST("/SelectDramaV2", authHandleInterceptor(piaMgr.SelectDramaV2))
        piaSvrRouter.POST("/OrderDrama", authHandleInterceptor(piaMgr.OrderDrama))
        piaSvrRouter.POST("/GetOrderDramaList", authHandleInterceptor(piaMgr.GetOrderDramaList))
        piaSvrRouter.POST("/SimpleGetDramaStatus", authHandleInterceptor(piaMgr.SimpleGetDramaStatus))

        // 荣耀世界抽奖
        gloryLotteryCli, err := glorylotterypb.NewClient(context.Background())
        if err != nil {
            log.Errorf("fail to new glory lottery cli")
            return err
        }
        gloryLotteryMgr := glorylottery.NewGloryLotteryMgr(gloryLotteryCli)
        gloryLotteryRouter := p.Child("/glory-lottery")
        gloryLotteryRouter.POST("/GetWinningCarousel", authHandleInterceptor(gloryLotteryMgr.GetWinningCarousel))
        gloryLotteryRouter.POST("/GetWelfareWall", authHandleInterceptor(gloryLotteryMgr.GetWelfareWall))
        gloryLotteryRouter.POST("/GetMyLotteryRecordAwardType", authHandleInterceptor(gloryLotteryMgr.GetMyLotteryRecordAwardType))
        gloryLotteryRouter.POST("/GetMyLotteryRecord", authHandleInterceptor(gloryLotteryMgr.GetMyLotteryRecord))
        gloryLotteryRouter.POST("/Lottery", authHandleInterceptor(gloryLotteryMgr.Lottery))

        // 荣耀世界魔法祈愿抽奖
        gloryMagicCli, err := gloryMagicPB.NewClient(context.Background())
        if err != nil {
            log.Errorf("fail to new glory maigc cli")
            return err
        }
        gloryMagicMgr := glory_magic.NewGloryMagicMgr(gloryMagicCli)
        gloryMagicRouter := p.Child("/glory-magic")
        gloryMagicRouter.POST("/CheckEntry", authHandleInterceptor(gloryMagicMgr.CheckEntry))
        gloryMagicRouter.POST("/GetPreInfo", authHandleInterceptor(gloryMagicMgr.GetPreInfo))
        gloryMagicRouter.POST("/GetAwardPreview", authHandleInterceptor(gloryMagicMgr.GetAwardPreview))
        gloryMagicRouter.POST("/GetWinningCarousel", authHandleInterceptor(gloryMagicMgr.GetWinningCarousel))
        gloryMagicRouter.POST("/Lottery", authHandleInterceptor(gloryMagicMgr.Lottery))
        gloryMagicRouter.POST("/GetMyLotteryRecord", authHandleInterceptor(gloryMagicMgr.GetMyLotteryRecord))
        gloryMagicRouter.POST("/GetMyLotteryRecordType", authHandleInterceptor(gloryMagicMgr.GetMyLotteryRecordType))

        // TT周边商城
        peripheralMallMgr := peripheralMall.NewPeripheralMallMgr(peripheralMallCli, iopCli, appCfgCli)
        peripheralMallRouter := p.Child("/tt_peripheral")
        peripheralMallRouter.POST("/product_list", authHandleInterceptor(peripheralMallMgr.GetPeripheralBriefInfo))
        peripheralMallRouter.POST("/product_detail", authHandleInterceptor(peripheralMallMgr.GetPeripheralProductDetail))
        peripheralMallRouter.POST("/order", authHandleInterceptor(peripheralMallMgr.OrderPeripheralProduct))
        peripheralMallRouter.POST("/get_history_orders", authHandleInterceptor(peripheralMallMgr.GetPeripheralProductOrders))
        peripheralMallRouter.POST("/order_addr/get", authHandleInterceptor(peripheralMallMgr.GetUserPeripheralOrderAddr))
        peripheralMallRouter.POST("/order_addr/set", authHandleInterceptor(peripheralMallMgr.SetUserPeripheralOrderAddr))

        // 用户召回
        userRecallAwardMgr, err := user_recall_award.NewUserRecallAwardMgr(ctx)
        if err != nil {
            log.Errorf("fail to new user recall award mgr")
            return err
        }
        userRecallAwardRouter := p.Child("/user-recall-award")
        userRecallAwardRouter.POST("/GetReturnAwardInfo", authHandleInterceptor(userRecallAwardMgr.GetReturnAwardInfo))
        userRecallAwardRouter.POST("/GetReturnAward", authHandleInterceptor(userRecallAwardMgr.GetReturnAward))

        // 礼物周卡
        weekCardMgr := present_week_card.NewPresentWeekCardMgr(presentWeekCardCli, darkCli)
        if err != nil {
            log.Errorf("fail to NewPresentWeekCardMgr")
            return err
        }
        WeekCardRouter := p.Child("/present-week-card")
        WeekCardRouter.POST("/entry", authHandleInterceptor(weekCardMgr.GetPresentWeekCardEntry))

        esportRefundMgr := esport.NewRefundMgr(refundService, accountClient, tshellCli, businessConfManager, extPushCli, esportTrakeClient)
        esportRefundMgr.Register(cfg, p)

        esportRoleMgr := esport.NewApplyRoleMgr(esportRoleCli, riskMngApiCli, anchorContractGoClient, esportSkillCli,
            ttcProxyClient, cfg, businessConfManager)
        esportRoleMgr.Register(cfg, p)

        statisticsCli, _ := esport_statistics.NewClient(ctx)
        statisticMgr := esport.NewStatisticsMgr(statisticsCli, godLvCli, esportCli, esportScoreCli, esportRoleCli, esportSkillCli)
        statisticMgr.Register(cfg, p)

        esportHallMgr := esport.NewEsportHallMgr(ctx, businessConfManager)
        esportHallMgr.Register(cfg, p)

        esportCouponMgr := esport.NewEsportCouponMgr(ctx, businessConfManager)
        esportCouponMgr.Register(cfg, p)

        esportGrabMgr := esport.NewEsportGrabMgr(ctx, businessConfManager)
        esportGrabMgr.Register(cfg, p)

        esportSkillMgr := esport.NewEsportSkillMgr(ctx, businessConfManager)
        esportSkillMgr.Register(cfg, p)

        esportOrderMgr := esport.NewOrderMgr(esportTrakeClient, refundService, accountClient, headDynamicImageCli, friendShipCli, businessConfManager)
        esportOrderMgr.Register(cfg, p)

        esportReportMgr := esport.NewReportMgr(esportRoleCli, esportSkillCli, accountClient)
        esportReportMgr.Register(cfg, p)

        //wechat server
        wechatMgr := wechatCtrl.NewWeChatMgr(ctx, businessConfManager)
        if wechatMgr != nil {
            wechatMgr.Register(cfg, p)
        }

        esportGodLevelMgr := esport.NewESportGodLevelMgr(ctx, godLvCli)
        if esportGodLevelMgr != nil {
            esportGodLevelMgr.Register(cfg, p)
        }

        // esport visit mgr
        esportVisitMgr := esport.NewVisitMgr(ctx, businessConfManager)
        if esportVisitMgr != nil {
            esportVisitMgr.Register(cfg, p)
        }

        // esport task mgr
        esportIncentiveTaskMgr := esport.NewIncentiveTaskMgr(ctx, businessConfManager)
        if esportIncentiveTaskMgr != nil {
            esportIncentiveTaskMgr.Register(cfg, p)
        }

        // 摘星列车记录接口
        starTrainMgr := star_train.NewStarTrainMgr(ctx)
        if starTrainMgr != nil {
            starTrainMgr.Register(cfg, p)
        }

        // 大R生日
        birthdayMgr := richer_birthday.NewRicherBirthdayMgr(ctx, businessConfManager)
        if birthdayMgr != nil {
            birthdayMgr.Register(cfg, p)
        }

        // 新首充活动
        newRechargeActMgr := new_recharge_act.NewRechargeActMgrMgr(presentWeekCardCli, darkCli)
        if newRechargeActMgr != nil {
            newRechargeActMgr.Register(cfg, p)
        }
        closeFunc = func(ctx context.Context) {
            // do something when server terminating
            businessCleanup()
        }

        virtualImageMgr := virtual_image.NewVirtualImageMgr(ctx, businessConfManager)
        if virtualImageMgr != nil {
            virtualImageMgr.Register(cfg, p)
        }

        virtualImageCardMgr := virtual_image.NewVirtualImageCardMgr(ctx, businessConfManager)
        if virtualImageCardMgr != nil {
            virtualImageCardMgr.Register(cfg, p)
        }

        channelWeddingPlanSvr, err := channel_wedding_plan.NewClient(ctx)
        if err != nil {
            log.Errorf("fail to new channel wedding plan client")
            return err
        }

        channelWeddingCli, err := channel_wedding_cli.NewClient(ctx)
        if err != nil {
            log.Errorf("fail to new channel wedding client")
            return err
        }

        channelWeddingConfCli, err := channel_wedding_conf.NewClient(ctx)
        if err != nil {
            log.Errorf("fail to new channel wedding conf client")
            return err
        }

        channelWeddingMgr := channel_wedding.NewChannelWeddingMgr(channelWeddingPlanSvr, channelCli, userprofileCli, fellowCliNew, youKnowWhoClient, accountClient,
            channelWeddingCli, channelWeddingConfCli, presentCli, guildClient, channelOlGoClient, gNobilityCli, nobilityCli)
        if channelWeddingMgr != nil {
            channelWeddingMgr.Register(cfg, p)
        }

        return nil
    }

    if err := startup.New("tt-revenue-http-logic", cfg).
        AddHttpServer(
            http.NewBuildOption().WithInitializeFunc(initializeFunc),
        ).
        WithCloseFunc(closeFunc).
        Start(); err != nil {
        log.Errorf("server start fail, err: %v", err)
    }

}

func authHandleInterceptor(handleFunc func(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request)) func(ctx context.Context, w http.ResponseWriter, r *http.Request) {
    auth := web.NewAuth(&web.UidAuthVerify{}, cfg.ValidateToken)
    authHandler := web.NewHandler(auth, cfg.Cors, true)
    handler := authHandler.SetHandler(handleFunc)

    return func(ctx context.Context, w http.ResponseWriter, r *http.Request) {
        handler.ServeHTTP(w, r)
    }
}

func noAuthHandleInterceptor(handleFunc func(w http.ResponseWriter, r *http.Request)) func(ctx context.Context, w http.ResponseWriter, r *http.Request) {
    noAuthHandler := web.NewHandler(nil, cfg.Cors, true)
    handler := noAuthHandler.SetNoAuthHandlerToken(handleFunc, false)

    return func(ctx context.Context, w http.ResponseWriter, r *http.Request) {
        handler.ServeHTTP(w, r)
    }
}
