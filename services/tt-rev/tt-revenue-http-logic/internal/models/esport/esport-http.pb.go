// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/esport-http/esport-http.proto

package esport_http

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import esport_hall "golang.52tt.com/protocol/services/esport_hall"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 游戏类型
// buf:lint:ignore ENUM_PASCAL_CASE
type GAME_TYPE int32

const (
	GAME_TYPE_GAME_TYPE_INVALID GAME_TYPE = 0
	GAME_TYPE_GAME_TYPE_MOBILE  GAME_TYPE = 1
	GAME_TYPE_GAME_TYPE_PC      GAME_TYPE = 2
)

var GAME_TYPE_name = map[int32]string{
	0: "GAME_TYPE_INVALID",
	1: "GAME_TYPE_MOBILE",
	2: "GAME_TYPE_PC",
}
var GAME_TYPE_value = map[string]int32{
	"GAME_TYPE_INVALID": 0,
	"GAME_TYPE_MOBILE":  1,
	"GAME_TYPE_PC":      2,
}

func (x GAME_TYPE) String() string {
	return proto.EnumName(GAME_TYPE_name, int32(x))
}
func (GAME_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{0}
}

type SelectType int32

const (
	SelectType_SELECT_TYPE_UNSPECIFIED SelectType = 0
	SelectType_SELECT_TYPE_SINGLE      SelectType = 1
	SelectType_SELECT_TYPE_MULTI       SelectType = 2
)

var SelectType_name = map[int32]string{
	0: "SELECT_TYPE_UNSPECIFIED",
	1: "SELECT_TYPE_SINGLE",
	2: "SELECT_TYPE_MULTI",
}
var SelectType_value = map[string]int32{
	"SELECT_TYPE_UNSPECIFIED": 0,
	"SELECT_TYPE_SINGLE":      1,
	"SELECT_TYPE_MULTI":       2,
}

func (x SelectType) String() string {
	return proto.EnumName(SelectType_name, int32(x))
}
func (SelectType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{1}
}

// 电竞指导身份类型
type ESportErType int32

const (
	ESportErType_ESPORT_TYPE_UNSPECIFIED ESportErType = 0
	ESportErType_ESPORT_TYPE_PERSONAL    ESportErType = 1
	ESportErType_ESPORT_TYPE_GUILD       ESportErType = 2
)

var ESportErType_name = map[int32]string{
	0: "ESPORT_TYPE_UNSPECIFIED",
	1: "ESPORT_TYPE_PERSONAL",
	2: "ESPORT_TYPE_GUILD",
}
var ESportErType_value = map[string]int32{
	"ESPORT_TYPE_UNSPECIFIED": 0,
	"ESPORT_TYPE_PERSONAL":    1,
	"ESPORT_TYPE_GUILD":       2,
}

func (x ESportErType) String() string {
	return proto.EnumName(ESportErType_name, int32(x))
}
func (ESportErType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{2}
}

// 电竞指导身份类型
type ESportStatType int32

const (
	ESportStatType_ESPORT_STAT_TYPE_UNSPECIFIED ESportStatType = 0
	ESportStatType_ESPORT_STAT_TYPE_WEEKLY      ESportStatType = 1
	ESportStatType_ESPORT_STAT_TYPE_MONTHLY     ESportStatType = 2
	ESportStatType_ESPORT_STAT_TYPE_ALL         ESportStatType = 3
)

var ESportStatType_name = map[int32]string{
	0: "ESPORT_STAT_TYPE_UNSPECIFIED",
	1: "ESPORT_STAT_TYPE_WEEKLY",
	2: "ESPORT_STAT_TYPE_MONTHLY",
	3: "ESPORT_STAT_TYPE_ALL",
}
var ESportStatType_value = map[string]int32{
	"ESPORT_STAT_TYPE_UNSPECIFIED": 0,
	"ESPORT_STAT_TYPE_WEEKLY":      1,
	"ESPORT_STAT_TYPE_MONTHLY":     2,
	"ESPORT_STAT_TYPE_ALL":         3,
}

func (x ESportStatType) String() string {
	return proto.EnumName(ESportStatType_name, int32(x))
}
func (ESportStatType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{3}
}

// 资源类型
type LabelSourceType int32

const (
	LabelSourceType_LABEL_SOURCE_TYPE_UNSPECIFIED LabelSourceType = 0
	LabelSourceType_LABEL_SOURCE_TYPE_PNG         LabelSourceType = 1
	LabelSourceType_LABEL_SOURCE_TYPE_WEBP        LabelSourceType = 2
)

var LabelSourceType_name = map[int32]string{
	0: "LABEL_SOURCE_TYPE_UNSPECIFIED",
	1: "LABEL_SOURCE_TYPE_PNG",
	2: "LABEL_SOURCE_TYPE_WEBP",
}
var LabelSourceType_value = map[string]int32{
	"LABEL_SOURCE_TYPE_UNSPECIFIED": 0,
	"LABEL_SOURCE_TYPE_PNG":         1,
	"LABEL_SOURCE_TYPE_WEBP":        2,
}

func (x LabelSourceType) String() string {
	return proto.EnumName(LabelSourceType_name, int32(x))
}
func (LabelSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{4}
}

// 搜索类型
type AuditSearchType int32

const (
	AuditSearchType_AUDIT_SEARCH_TYPE_UNSPECIFIED AuditSearchType = 0
	AuditSearchType_AUDIT_SEARCH_TYPE_BY_USER     AuditSearchType = 1
	AuditSearchType_AUDIT_SEARCH_TYPE_BY_GUILD    AuditSearchType = 2
	AuditSearchType_AUDIT_SEARCH_TYPE_ALL         AuditSearchType = 3
)

var AuditSearchType_name = map[int32]string{
	0: "AUDIT_SEARCH_TYPE_UNSPECIFIED",
	1: "AUDIT_SEARCH_TYPE_BY_USER",
	2: "AUDIT_SEARCH_TYPE_BY_GUILD",
	3: "AUDIT_SEARCH_TYPE_ALL",
}
var AuditSearchType_value = map[string]int32{
	"AUDIT_SEARCH_TYPE_UNSPECIFIED": 0,
	"AUDIT_SEARCH_TYPE_BY_USER":     1,
	"AUDIT_SEARCH_TYPE_BY_GUILD":    2,
	"AUDIT_SEARCH_TYPE_ALL":         3,
}

func (x AuditSearchType) String() string {
	return proto.EnumName(AuditSearchType_name, int32(x))
}
func (AuditSearchType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{5}
}

type ESportApplyType int32

const (
	ESportApplyType_ESPORT_APPLY_TYPE_INVALID           ESportApplyType = 0
	ESportApplyType_ESPORT_APPLY_TYPE_PERSONAL          ESportApplyType = 1
	ESportApplyType_ESPORT_APPLY_TYPE_GUILD             ESportApplyType = 2
	ESportApplyType_ESPORT_APPLY_TYPE_PERSONAL_TO_GUILD ESportApplyType = 3
)

var ESportApplyType_name = map[int32]string{
	0: "ESPORT_APPLY_TYPE_INVALID",
	1: "ESPORT_APPLY_TYPE_PERSONAL",
	2: "ESPORT_APPLY_TYPE_GUILD",
	3: "ESPORT_APPLY_TYPE_PERSONAL_TO_GUILD",
}
var ESportApplyType_value = map[string]int32{
	"ESPORT_APPLY_TYPE_INVALID":           0,
	"ESPORT_APPLY_TYPE_PERSONAL":          1,
	"ESPORT_APPLY_TYPE_GUILD":             2,
	"ESPORT_APPLY_TYPE_PERSONAL_TO_GUILD": 3,
}

func (x ESportApplyType) String() string {
	return proto.EnumName(ESportApplyType_name, int32(x))
}
func (ESportApplyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{6}
}

// 退款类型枚举
type RefundType int32

const (
	RefundType_REFUND_TYPE_FULL    RefundType = 0
	RefundType_REFUND_TYPE_PARTIAL RefundType = 1
)

var RefundType_name = map[int32]string{
	0: "REFUND_TYPE_FULL",
	1: "REFUND_TYPE_PARTIAL",
}
var RefundType_value = map[string]int32{
	"REFUND_TYPE_FULL":    0,
	"REFUND_TYPE_PARTIAL": 1,
}

func (x RefundType) String() string {
	return proto.EnumName(RefundType_name, int32(x))
}
func (RefundType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{7}
}

type FreezeType int32

const (
	FreezeType_FREEZE_TYPE_UNFREEZE FreezeType = 0
	FreezeType_FREEZE_TYPE_FOREVER  FreezeType = 1
	FreezeType_FREEZE_TYPE_TO_TIME  FreezeType = 2
)

var FreezeType_name = map[int32]string{
	0: "FREEZE_TYPE_UNFREEZE",
	1: "FREEZE_TYPE_FOREVER",
	2: "FREEZE_TYPE_TO_TIME",
}
var FreezeType_value = map[string]int32{
	"FREEZE_TYPE_UNFREEZE": 0,
	"FREEZE_TYPE_FOREVER":  1,
	"FREEZE_TYPE_TO_TIME":  2,
}

func (x FreezeType) String() string {
	return proto.EnumName(FreezeType_name, int32(x))
}
func (FreezeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{8}
}

// 优惠类型
type DiscountType int32

const (
	DiscountType_DISCOUNT_TYPE_UNSPECIFIED  DiscountType = 0
	DiscountType_DISCOUNT_TYPE_FIRST_ROUND  DiscountType = 1
	DiscountType_DISCOUNT_TYPE_COUPON       DiscountType = 2
	DiscountType_DISCOUNT_TYPE_NEW_CUSTOMER DiscountType = 3
)

var DiscountType_name = map[int32]string{
	0: "DISCOUNT_TYPE_UNSPECIFIED",
	1: "DISCOUNT_TYPE_FIRST_ROUND",
	2: "DISCOUNT_TYPE_COUPON",
	3: "DISCOUNT_TYPE_NEW_CUSTOMER",
}
var DiscountType_value = map[string]int32{
	"DISCOUNT_TYPE_UNSPECIFIED":  0,
	"DISCOUNT_TYPE_FIRST_ROUND":  1,
	"DISCOUNT_TYPE_COUPON":       2,
	"DISCOUNT_TYPE_NEW_CUSTOMER": 3,
}

func (x DiscountType) String() string {
	return proto.EnumName(DiscountType_name, int32(x))
}
func (DiscountType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{9}
}

// LabelType 标识类型
type LabelType int32

const (
	LabelType_LABEL_TYPE_UNSPECIFIED LabelType = 0
	LabelType_LABEL_TYPE_COACH       LabelType = 1
	LabelType_LABEL_TYPE_SKILL       LabelType = 2
)

var LabelType_name = map[int32]string{
	0: "LABEL_TYPE_UNSPECIFIED",
	1: "LABEL_TYPE_COACH",
	2: "LABEL_TYPE_SKILL",
}
var LabelType_value = map[string]int32{
	"LABEL_TYPE_UNSPECIFIED": 0,
	"LABEL_TYPE_COACH":       1,
	"LABEL_TYPE_SKILL":       2,
}

func (x LabelType) String() string {
	return proto.EnumName(LabelType_name, int32(x))
}
func (LabelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{10}
}

// 客服托管开关操作类型
type HostingOperation int32

const (
	HostingOperation_HOSTING_OPERATION_UNSPECIFIED HostingOperation = 0
	HostingOperation_HOSTING_OPERATION_ENABLE      HostingOperation = 1
	HostingOperation_HOSTING_OPERATION_DISABLE     HostingOperation = 2
)

var HostingOperation_name = map[int32]string{
	0: "HOSTING_OPERATION_UNSPECIFIED",
	1: "HOSTING_OPERATION_ENABLE",
	2: "HOSTING_OPERATION_DISABLE",
}
var HostingOperation_value = map[string]int32{
	"HOSTING_OPERATION_UNSPECIFIED": 0,
	"HOSTING_OPERATION_ENABLE":      1,
	"HOSTING_OPERATION_DISABLE":     2,
}

func (x HostingOperation) String() string {
	return proto.EnumName(HostingOperation_name, int32(x))
}
func (HostingOperation) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{11}
}

type TabType int32

const (
	TabType_TAB_TYPE_UNSPECIFIED  TabType = 0
	TabType_TAB_TYPE_PAYED        TabType = 1
	TabType_TAB_TYPE_RECEIVED     TabType = 2
	TabType_TAB_TYPE_IN_REFUNDING TabType = 3
	TabType_TAB_TYPE_IN_APPEALING TabType = 4
	TabType_TAB_TYPE_CANCELED     TabType = 5
	TabType_TAB_TYPE_FINISHED     TabType = 6
)

var TabType_name = map[int32]string{
	0: "TAB_TYPE_UNSPECIFIED",
	1: "TAB_TYPE_PAYED",
	2: "TAB_TYPE_RECEIVED",
	3: "TAB_TYPE_IN_REFUNDING",
	4: "TAB_TYPE_IN_APPEALING",
	5: "TAB_TYPE_CANCELED",
	6: "TAB_TYPE_FINISHED",
}
var TabType_value = map[string]int32{
	"TAB_TYPE_UNSPECIFIED":  0,
	"TAB_TYPE_PAYED":        1,
	"TAB_TYPE_RECEIVED":     2,
	"TAB_TYPE_IN_REFUNDING": 3,
	"TAB_TYPE_IN_APPEALING": 4,
	"TAB_TYPE_CANCELED":     5,
	"TAB_TYPE_FINISHED":     6,
}

func (x TabType) String() string {
	return proto.EnumName(TabType_name, int32(x))
}
func (TabType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{12}
}

// 订单售后状态
type RefundStatus int32

const (
	RefundStatus_REFUND_STATUS_UNSPECIFIED       RefundStatus = 0
	RefundStatus_REFUND_STATUS_REFUNDING         RefundStatus = 1
	RefundStatus_REFUND_STATUS_REFUND_ACCEPT     RefundStatus = 2
	RefundStatus_REFUND_STATUS_REFUND_REJECT     RefundStatus = 3
	RefundStatus_REFUND_STATUS_APPEALING         RefundStatus = 4
	RefundStatus_REFUND_STATUS_APPEALING_ACCEPT  RefundStatus = 5
	RefundStatus_REFUND_STATUS_APPEALING_REJECT  RefundStatus = 6
	RefundStatus_REFUND_STATUS_APPEALING_APPLIED RefundStatus = 7
)

var RefundStatus_name = map[int32]string{
	0: "REFUND_STATUS_UNSPECIFIED",
	1: "REFUND_STATUS_REFUNDING",
	2: "REFUND_STATUS_REFUND_ACCEPT",
	3: "REFUND_STATUS_REFUND_REJECT",
	4: "REFUND_STATUS_APPEALING",
	5: "REFUND_STATUS_APPEALING_ACCEPT",
	6: "REFUND_STATUS_APPEALING_REJECT",
	7: "REFUND_STATUS_APPEALING_APPLIED",
}
var RefundStatus_value = map[string]int32{
	"REFUND_STATUS_UNSPECIFIED":       0,
	"REFUND_STATUS_REFUNDING":         1,
	"REFUND_STATUS_REFUND_ACCEPT":     2,
	"REFUND_STATUS_REFUND_REJECT":     3,
	"REFUND_STATUS_APPEALING":         4,
	"REFUND_STATUS_APPEALING_ACCEPT":  5,
	"REFUND_STATUS_APPEALING_REJECT":  6,
	"REFUND_STATUS_APPEALING_APPLIED": 7,
}

func (x RefundStatus) String() string {
	return proto.EnumName(RefundStatus_name, int32(x))
}
func (RefundStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{13}
}

// 订单已取消子状态
type CanceledOrderSubStatus int32

const (
	CanceledOrderSubStatus_CANCELED_ORDER_SUB_STATUS_UNSPECIFIED   CanceledOrderSubStatus = 0
	CanceledOrderSubStatus_CANCELED_ORDER_SUB_STATUS_PLAYER_CANCEL CanceledOrderSubStatus = 1
	CanceledOrderSubStatus_CANCELED_ORDER_SUB_STATUS_COACH_REFUSE  CanceledOrderSubStatus = 2
	CanceledOrderSubStatus_CANCELED_ORDER_SUB_STATUS_COACH_TIMEOUT CanceledOrderSubStatus = 3
)

var CanceledOrderSubStatus_name = map[int32]string{
	0: "CANCELED_ORDER_SUB_STATUS_UNSPECIFIED",
	1: "CANCELED_ORDER_SUB_STATUS_PLAYER_CANCEL",
	2: "CANCELED_ORDER_SUB_STATUS_COACH_REFUSE",
	3: "CANCELED_ORDER_SUB_STATUS_COACH_TIMEOUT",
}
var CanceledOrderSubStatus_value = map[string]int32{
	"CANCELED_ORDER_SUB_STATUS_UNSPECIFIED":   0,
	"CANCELED_ORDER_SUB_STATUS_PLAYER_CANCEL": 1,
	"CANCELED_ORDER_SUB_STATUS_COACH_REFUSE":  2,
	"CANCELED_ORDER_SUB_STATUS_COACH_TIMEOUT": 3,
}

func (x CanceledOrderSubStatus) String() string {
	return proto.EnumName(CanceledOrderSubStatus_name, int32(x))
}
func (CanceledOrderSubStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{14}
}

// 订单状态
type OrderStatus int32

const (
	OrderStatus_ORDER_STATUS_UNSPECIFIED  OrderStatus = 0
	OrderStatus_ORDER_STATUS_PAYED        OrderStatus = 1
	OrderStatus_ORDER_STATUS_RECEIVED     OrderStatus = 2
	OrderStatus_ORDER_STATUS_IN_REFUNDING OrderStatus = 3
	OrderStatus_ORDER_STATUS_FINISHED     OrderStatus = 4
	OrderStatus_ORDER_STATUS_CANCELED     OrderStatus = 5
	OrderStatus_ORDER_STATUS_REFUNDED     OrderStatus = 6
)

var OrderStatus_name = map[int32]string{
	0: "ORDER_STATUS_UNSPECIFIED",
	1: "ORDER_STATUS_PAYED",
	2: "ORDER_STATUS_RECEIVED",
	3: "ORDER_STATUS_IN_REFUNDING",
	4: "ORDER_STATUS_FINISHED",
	5: "ORDER_STATUS_CANCELED",
	6: "ORDER_STATUS_REFUNDED",
}
var OrderStatus_value = map[string]int32{
	"ORDER_STATUS_UNSPECIFIED":  0,
	"ORDER_STATUS_PAYED":        1,
	"ORDER_STATUS_RECEIVED":     2,
	"ORDER_STATUS_IN_REFUNDING": 3,
	"ORDER_STATUS_FINISHED":     4,
	"ORDER_STATUS_CANCELED":     5,
	"ORDER_STATUS_REFUNDED":     6,
}

func (x OrderStatus) String() string {
	return proto.EnumName(OrderStatus_name, int32(x))
}
func (OrderStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{15}
}

type MasterSwitchType int32

const (
	MasterSwitchType_MASTER_SWITCH_TYPE_UNSPECIFIED           MasterSwitchType = 0
	MasterSwitchType_MASTER_SWITCH_TYPE_QUICK_RECEIVE         MasterSwitchType = 1
	MasterSwitchType_MASTER_SWITCH_TYPE_FIRST_ROUND_DISCOUNT  MasterSwitchType = 2
	MasterSwitchType_MASTER_SWITCH_TYPE_NEW_CUSTOMER_DISCOUNT MasterSwitchType = 3
)

var MasterSwitchType_name = map[int32]string{
	0: "MASTER_SWITCH_TYPE_UNSPECIFIED",
	1: "MASTER_SWITCH_TYPE_QUICK_RECEIVE",
	2: "MASTER_SWITCH_TYPE_FIRST_ROUND_DISCOUNT",
	3: "MASTER_SWITCH_TYPE_NEW_CUSTOMER_DISCOUNT",
}
var MasterSwitchType_value = map[string]int32{
	"MASTER_SWITCH_TYPE_UNSPECIFIED":           0,
	"MASTER_SWITCH_TYPE_QUICK_RECEIVE":         1,
	"MASTER_SWITCH_TYPE_FIRST_ROUND_DISCOUNT":  2,
	"MASTER_SWITCH_TYPE_NEW_CUSTOMER_DISCOUNT": 3,
}

func (x MasterSwitchType) String() string {
	return proto.EnumName(MasterSwitchType_name, int32(x))
}
func (MasterSwitchType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{16}
}

type GrabType int32

const (
	GrabType_GRAB_TYPE_UNSPECIFIED GrabType = 0
	GrabType_GRAB_TYPE_TEXT        GrabType = 1
	GrabType_GRAB_TYPE_AUDIO       GrabType = 2
)

var GrabType_name = map[int32]string{
	0: "GRAB_TYPE_UNSPECIFIED",
	1: "GRAB_TYPE_TEXT",
	2: "GRAB_TYPE_AUDIO",
}
var GrabType_value = map[string]int32{
	"GRAB_TYPE_UNSPECIFIED": 0,
	"GRAB_TYPE_TEXT":        1,
	"GRAB_TYPE_AUDIO":       2,
}

func (x GrabType) String() string {
	return proto.EnumName(GrabType_name, int32(x))
}
func (GrabType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{17}
}

type GrabStatus int32

const (
	GrabStatus_GRAB_STATUS_UNSPECIFIED GrabStatus = 0
	GrabStatus_GRAB_STATUS_PENDING     GrabStatus = 1
	GrabStatus_GRAB_STATUS_END         GrabStatus = 2
	GrabStatus_GRAB_STATUS_CHOSEN      GrabStatus = 3
)

var GrabStatus_name = map[int32]string{
	0: "GRAB_STATUS_UNSPECIFIED",
	1: "GRAB_STATUS_PENDING",
	2: "GRAB_STATUS_END",
	3: "GRAB_STATUS_CHOSEN",
}
var GrabStatus_value = map[string]int32{
	"GRAB_STATUS_UNSPECIFIED": 0,
	"GRAB_STATUS_PENDING":     1,
	"GRAB_STATUS_END":         2,
	"GRAB_STATUS_CHOSEN":      3,
}

func (x GrabStatus) String() string {
	return proto.EnumName(GrabStatus_name, int32(x))
}
func (GrabStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{18}
}

// 游戏资料类型
// buf:lint:ignore ENUM_PASCAL_CASE
type GameInformation_GAME_INFORMATION_TYPE int32

const (
	GameInformation_GAME_INFORMATION_TYPE_INVALID GameInformation_GAME_INFORMATION_TYPE = 0
	GameInformation_GAME_INFORMATION_TYPE_RANK    GameInformation_GAME_INFORMATION_TYPE = 1
	GameInformation_GAME_INFORMATION_TYPE_DIY     GameInformation_GAME_INFORMATION_TYPE = 2
)

var GameInformation_GAME_INFORMATION_TYPE_name = map[int32]string{
	0: "GAME_INFORMATION_TYPE_INVALID",
	1: "GAME_INFORMATION_TYPE_RANK",
	2: "GAME_INFORMATION_TYPE_DIY",
}
var GameInformation_GAME_INFORMATION_TYPE_value = map[string]int32{
	"GAME_INFORMATION_TYPE_INVALID": 0,
	"GAME_INFORMATION_TYPE_RANK":    1,
	"GAME_INFORMATION_TYPE_DIY":     2,
}

func (x GameInformation_GAME_INFORMATION_TYPE) String() string {
	return proto.EnumName(GameInformation_GAME_INFORMATION_TYPE_name, int32(x))
}
func (GameInformation_GAME_INFORMATION_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{31, 0}
}

// buf:lint:ignore ENUM_PASCAL_CASE
type GameInformation_GAME_INFORMATION_SELECT_TYPE int32

const (
	GameInformation_GAME_INFORMATION_SELECT_TYPE_INVALID GameInformation_GAME_INFORMATION_SELECT_TYPE = 0
	GameInformation_GAME_INFORMATION_SELECT_TYPE_SINGLE  GameInformation_GAME_INFORMATION_SELECT_TYPE = 1
	GameInformation_GAME_INFORMATION_SELECT_TYPE_MULTI   GameInformation_GAME_INFORMATION_SELECT_TYPE = 2
)

var GameInformation_GAME_INFORMATION_SELECT_TYPE_name = map[int32]string{
	0: "GAME_INFORMATION_SELECT_TYPE_INVALID",
	1: "GAME_INFORMATION_SELECT_TYPE_SINGLE",
	2: "GAME_INFORMATION_SELECT_TYPE_MULTI",
}
var GameInformation_GAME_INFORMATION_SELECT_TYPE_value = map[string]int32{
	"GAME_INFORMATION_SELECT_TYPE_INVALID": 0,
	"GAME_INFORMATION_SELECT_TYPE_SINGLE":  1,
	"GAME_INFORMATION_SELECT_TYPE_MULTI":   2,
}

func (x GameInformation_GAME_INFORMATION_SELECT_TYPE) String() string {
	return proto.EnumName(GameInformation_GAME_INFORMATION_SELECT_TYPE_name, int32(x))
}
func (GameInformation_GAME_INFORMATION_SELECT_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{31, 1}
}

type GameProperty_PropertyType int32

const (
	GameProperty_PROPERTY_TYPE_UNSPECIFIED GameProperty_PropertyType = 0
	GameProperty_PROPERTY_TYPE_GENDER      GameProperty_PropertyType = 1
	GameProperty_PROPERTY_TYPE_PRICE       GameProperty_PropertyType = 2
	GameProperty_PROPERTY_TYPE_CUSTOM      GameProperty_PropertyType = 3
)

var GameProperty_PropertyType_name = map[int32]string{
	0: "PROPERTY_TYPE_UNSPECIFIED",
	1: "PROPERTY_TYPE_GENDER",
	2: "PROPERTY_TYPE_PRICE",
	3: "PROPERTY_TYPE_CUSTOM",
}
var GameProperty_PropertyType_value = map[string]int32{
	"PROPERTY_TYPE_UNSPECIFIED": 0,
	"PROPERTY_TYPE_GENDER":      1,
	"PROPERTY_TYPE_PRICE":       2,
	"PROPERTY_TYPE_CUSTOM":      3,
}

func (x GameProperty_PropertyType) String() string {
	return proto.EnumName(GameProperty_PropertyType_name, int32(x))
}
func (GameProperty_PropertyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{65, 0}
}

type GameProperty_SelectType int32

const (
	GameProperty_SELECT_TYPE_UNSPECIFIED GameProperty_SelectType = 0
	GameProperty_SELECT_TYPE_SINGLE      GameProperty_SelectType = 1
	GameProperty_SELECT_TYPE_MULTI       GameProperty_SelectType = 2
)

var GameProperty_SelectType_name = map[int32]string{
	0: "SELECT_TYPE_UNSPECIFIED",
	1: "SELECT_TYPE_SINGLE",
	2: "SELECT_TYPE_MULTI",
}
var GameProperty_SelectType_value = map[string]int32{
	"SELECT_TYPE_UNSPECIFIED": 0,
	"SELECT_TYPE_SINGLE":      1,
	"SELECT_TYPE_MULTI":       2,
}

func (x GameProperty_SelectType) String() string {
	return proto.EnumName(GameProperty_SelectType_name, int32(x))
}
func (GameProperty_SelectType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{65, 1}
}

type ReportExposeCoachRequest_ExposeCoachType int32

const (
	ReportExposeCoachRequest_EXPOSE_COACH_TYPE_UNSPECIFIED            ReportExposeCoachRequest_ExposeCoachType = 0
	ReportExposeCoachRequest_EXPOSE_COACH_TYPE_ESPORT_AREA            ReportExposeCoachRequest_ExposeCoachType = 1
	ReportExposeCoachRequest_EXPOSE_COACH_TYPE_ESPORT_KING_TAB        ReportExposeCoachRequest_ExposeCoachType = 2
	ReportExposeCoachRequest_EXPOSE_COACH_TYPE_ESPORT_UGC_CHANNEL     ReportExposeCoachRequest_ExposeCoachType = 3
	ReportExposeCoachRequest_EXPOSE_COACH_TYPE_ESPORT_HELPER_ACTIVITY ReportExposeCoachRequest_ExposeCoachType = 4
)

var ReportExposeCoachRequest_ExposeCoachType_name = map[int32]string{
	0: "EXPOSE_COACH_TYPE_UNSPECIFIED",
	1: "EXPOSE_COACH_TYPE_ESPORT_AREA",
	2: "EXPOSE_COACH_TYPE_ESPORT_KING_TAB",
	3: "EXPOSE_COACH_TYPE_ESPORT_UGC_CHANNEL",
	4: "EXPOSE_COACH_TYPE_ESPORT_HELPER_ACTIVITY",
}
var ReportExposeCoachRequest_ExposeCoachType_value = map[string]int32{
	"EXPOSE_COACH_TYPE_UNSPECIFIED":            0,
	"EXPOSE_COACH_TYPE_ESPORT_AREA":            1,
	"EXPOSE_COACH_TYPE_ESPORT_KING_TAB":        2,
	"EXPOSE_COACH_TYPE_ESPORT_UGC_CHANNEL":     3,
	"EXPOSE_COACH_TYPE_ESPORT_HELPER_ACTIVITY": 4,
}

func (x ReportExposeCoachRequest_ExposeCoachType) String() string {
	return proto.EnumName(ReportExposeCoachRequest_ExposeCoachType_name, int32(x))
}
func (ReportExposeCoachRequest_ExposeCoachType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{163, 0}
}

type QueryCheckAudioStatusResponse_CheckStatus int32

const (
	QueryCheckAudioStatusResponse_CHECK_STATUS_UNSPECIFIED QueryCheckAudioStatusResponse_CheckStatus = 0
	QueryCheckAudioStatusResponse_CHECK_STATUS_CHECKING    QueryCheckAudioStatusResponse_CheckStatus = 1
	QueryCheckAudioStatusResponse_CHECK_STATUS_VALID       QueryCheckAudioStatusResponse_CheckStatus = 2
	QueryCheckAudioStatusResponse_CHECK_STATUS_INVALID     QueryCheckAudioStatusResponse_CheckStatus = 3
)

var QueryCheckAudioStatusResponse_CheckStatus_name = map[int32]string{
	0: "CHECK_STATUS_UNSPECIFIED",
	1: "CHECK_STATUS_CHECKING",
	2: "CHECK_STATUS_VALID",
	3: "CHECK_STATUS_INVALID",
}
var QueryCheckAudioStatusResponse_CheckStatus_value = map[string]int32{
	"CHECK_STATUS_UNSPECIFIED": 0,
	"CHECK_STATUS_CHECKING":    1,
	"CHECK_STATUS_VALID":       2,
	"CHECK_STATUS_INVALID":     3,
}

func (x QueryCheckAudioStatusResponse_CheckStatus) String() string {
	return proto.EnumName(QueryCheckAudioStatusResponse_CheckStatus_name, int32(x))
}
func (QueryCheckAudioStatusResponse_CheckStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{183, 0}
}

// 段位等信息
type SectionInfo struct {
	SectionName          string   `protobuf:"bytes,1,opt,name=section_name,json=sectionName,proto3" json:"section_name"`
	ItemList             []string `protobuf:"bytes,2,rep,name=item_list,json=itemList,proto3" json:"item_list"`
	SectionId            uint32   `protobuf:"varint,3,opt,name=section_id,json=sectionId,proto3" json:"section_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SectionInfo) Reset()         { *m = SectionInfo{} }
func (m *SectionInfo) String() string { return proto.CompactTextString(m) }
func (*SectionInfo) ProtoMessage()    {}
func (*SectionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{0}
}
func (m *SectionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SectionInfo.Unmarshal(m, b)
}
func (m *SectionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SectionInfo.Marshal(b, m, deterministic)
}
func (dst *SectionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SectionInfo.Merge(dst, src)
}
func (m *SectionInfo) XXX_Size() int {
	return xxx_messageInfo_SectionInfo.Size(m)
}
func (m *SectionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SectionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SectionInfo proto.InternalMessageInfo

func (m *SectionInfo) GetSectionName() string {
	if m != nil {
		return m.SectionName
	}
	return ""
}

func (m *SectionInfo) GetItemList() []string {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *SectionInfo) GetSectionId() uint32 {
	if m != nil {
		return m.SectionId
	}
	return 0
}

// 用户游戏资料信息
type UserSkillInfo struct {
	GameId               uint32         `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	GameName             string         `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name"`
	SkillEvidence        string         `protobuf:"bytes,3,opt,name=skill_evidence,json=skillEvidence,proto3" json:"skill_evidence"`
	SkillDesc            string         `protobuf:"bytes,4,opt,name=skill_desc,json=skillDesc,proto3" json:"skill_desc"`
	Audio                string         `protobuf:"bytes,5,opt,name=audio,proto3" json:"audio"`
	AudioDuration        uint32         `protobuf:"varint,6,opt,name=audio_duration,json=audioDuration,proto3" json:"audio_duration"`
	SectionList          []*SectionInfo `protobuf:"bytes,7,rep,name=section_list,json=sectionList,proto3" json:"section_list"`
	TextDesc             string         `protobuf:"bytes,8,opt,name=text_desc,json=textDesc,proto3" json:"text_desc"`
	GameRank             uint32         `protobuf:"varint,9,opt,name=game_rank,json=gameRank,proto3" json:"game_rank"`
	IsGuaranteeWin       bool           `protobuf:"varint,10,opt,name=is_guarantee_win,json=isGuaranteeWin,proto3" json:"is_guarantee_win"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserSkillInfo) Reset()         { *m = UserSkillInfo{} }
func (m *UserSkillInfo) String() string { return proto.CompactTextString(m) }
func (*UserSkillInfo) ProtoMessage()    {}
func (*UserSkillInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{1}
}
func (m *UserSkillInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSkillInfo.Unmarshal(m, b)
}
func (m *UserSkillInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSkillInfo.Marshal(b, m, deterministic)
}
func (dst *UserSkillInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSkillInfo.Merge(dst, src)
}
func (m *UserSkillInfo) XXX_Size() int {
	return xxx_messageInfo_UserSkillInfo.Size(m)
}
func (m *UserSkillInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSkillInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserSkillInfo proto.InternalMessageInfo

func (m *UserSkillInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UserSkillInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *UserSkillInfo) GetSkillEvidence() string {
	if m != nil {
		return m.SkillEvidence
	}
	return ""
}

func (m *UserSkillInfo) GetSkillDesc() string {
	if m != nil {
		return m.SkillDesc
	}
	return ""
}

func (m *UserSkillInfo) GetAudio() string {
	if m != nil {
		return m.Audio
	}
	return ""
}

func (m *UserSkillInfo) GetAudioDuration() uint32 {
	if m != nil {
		return m.AudioDuration
	}
	return 0
}

func (m *UserSkillInfo) GetSectionList() []*SectionInfo {
	if m != nil {
		return m.SectionList
	}
	return nil
}

func (m *UserSkillInfo) GetTextDesc() string {
	if m != nil {
		return m.TextDesc
	}
	return ""
}

func (m *UserSkillInfo) GetGameRank() uint32 {
	if m != nil {
		return m.GameRank
	}
	return 0
}

func (m *UserSkillInfo) GetIsGuaranteeWin() bool {
	if m != nil {
		return m.IsGuaranteeWin
	}
	return false
}

// uri: /add_user_skill
// 前端接口：新技能审核信息请求
type AddUserSkillRequest struct {
	Skill                *UserSkillInfo `protobuf:"bytes,1,opt,name=skill,proto3" json:"skill"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *AddUserSkillRequest) Reset()         { *m = AddUserSkillRequest{} }
func (m *AddUserSkillRequest) String() string { return proto.CompactTextString(m) }
func (*AddUserSkillRequest) ProtoMessage()    {}
func (*AddUserSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{2}
}
func (m *AddUserSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserSkillRequest.Unmarshal(m, b)
}
func (m *AddUserSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserSkillRequest.Marshal(b, m, deterministic)
}
func (dst *AddUserSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserSkillRequest.Merge(dst, src)
}
func (m *AddUserSkillRequest) XXX_Size() int {
	return xxx_messageInfo_AddUserSkillRequest.Size(m)
}
func (m *AddUserSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserSkillRequest proto.InternalMessageInfo

func (m *AddUserSkillRequest) GetSkill() *UserSkillInfo {
	if m != nil {
		return m.Skill
	}
	return nil
}

// 增加新技能响应
type AddUserSkillResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserSkillResponse) Reset()         { *m = AddUserSkillResponse{} }
func (m *AddUserSkillResponse) String() string { return proto.CompactTextString(m) }
func (*AddUserSkillResponse) ProtoMessage()    {}
func (*AddUserSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{3}
}
func (m *AddUserSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserSkillResponse.Unmarshal(m, b)
}
func (m *AddUserSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserSkillResponse.Marshal(b, m, deterministic)
}
func (dst *AddUserSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserSkillResponse.Merge(dst, src)
}
func (m *AddUserSkillResponse) XXX_Size() int {
	return xxx_messageInfo_AddUserSkillResponse.Size(m)
}
func (m *AddUserSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserSkillResponse proto.InternalMessageInfo

// uri: /get_user_current_skill
// 前端接口：获取用户当前技能信息
type GetUserCurrentSkillRequest struct {
	TargetUid            uint32   `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCurrentSkillRequest) Reset()         { *m = GetUserCurrentSkillRequest{} }
func (m *GetUserCurrentSkillRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserCurrentSkillRequest) ProtoMessage()    {}
func (*GetUserCurrentSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{4}
}
func (m *GetUserCurrentSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCurrentSkillRequest.Unmarshal(m, b)
}
func (m *GetUserCurrentSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCurrentSkillRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserCurrentSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCurrentSkillRequest.Merge(dst, src)
}
func (m *GetUserCurrentSkillRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserCurrentSkillRequest.Size(m)
}
func (m *GetUserCurrentSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCurrentSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCurrentSkillRequest proto.InternalMessageInfo

func (m *GetUserCurrentSkillRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetUserCurrentSkillResponse struct {
	TargetUid            uint32           `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid"`
	Skill                []*UserSkillInfo `protobuf:"bytes,2,rep,name=skill,proto3" json:"skill"`
	AuditType            uint32           `protobuf:"varint,3,opt,name=audit_type,json=auditType,proto3" json:"audit_type"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserCurrentSkillResponse) Reset()         { *m = GetUserCurrentSkillResponse{} }
func (m *GetUserCurrentSkillResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserCurrentSkillResponse) ProtoMessage()    {}
func (*GetUserCurrentSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{5}
}
func (m *GetUserCurrentSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCurrentSkillResponse.Unmarshal(m, b)
}
func (m *GetUserCurrentSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCurrentSkillResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserCurrentSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCurrentSkillResponse.Merge(dst, src)
}
func (m *GetUserCurrentSkillResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserCurrentSkillResponse.Size(m)
}
func (m *GetUserCurrentSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCurrentSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCurrentSkillResponse proto.InternalMessageInfo

func (m *GetUserCurrentSkillResponse) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetUserCurrentSkillResponse) GetSkill() []*UserSkillInfo {
	if m != nil {
		return m.Skill
	}
	return nil
}

func (m *GetUserCurrentSkillResponse) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

// uri: /get_user_skill_by_game_id
// 前端接口：获取用户当前技能信息
type GetUserSkillByGameIdRequest struct {
	TargetUid            uint32   `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSkillByGameIdRequest) Reset()         { *m = GetUserSkillByGameIdRequest{} }
func (m *GetUserSkillByGameIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserSkillByGameIdRequest) ProtoMessage()    {}
func (*GetUserSkillByGameIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{6}
}
func (m *GetUserSkillByGameIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSkillByGameIdRequest.Unmarshal(m, b)
}
func (m *GetUserSkillByGameIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSkillByGameIdRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserSkillByGameIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSkillByGameIdRequest.Merge(dst, src)
}
func (m *GetUserSkillByGameIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserSkillByGameIdRequest.Size(m)
}
func (m *GetUserSkillByGameIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSkillByGameIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSkillByGameIdRequest proto.InternalMessageInfo

func (m *GetUserSkillByGameIdRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetUserSkillByGameIdRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetUserSkillByGameIdResponse struct {
	TargetUid                 uint32           `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid"`
	GameId                    uint32           `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	AuditSkill                []*UserSkillInfo `protobuf:"bytes,3,rep,name=audit_skill,json=auditSkill,proto3" json:"audit_skill"`
	CurrentSkill              *UserSkillInfo   `protobuf:"bytes,4,opt,name=current_skill,json=currentSkill,proto3" json:"current_skill"`
	AuditType                 uint32           `protobuf:"varint,5,opt,name=audit_type,json=auditType,proto3" json:"audit_type"`
	HasGuaranteeWinPermission bool             `protobuf:"varint,6,opt,name=has_guarantee_win_permission,json=hasGuaranteeWinPermission,proto3" json:"has_guarantee_win_permission"`
	XXX_NoUnkeyedLiteral      struct{}         `json:"-"`
	XXX_unrecognized          []byte           `json:"-"`
	XXX_sizecache             int32            `json:"-"`
}

func (m *GetUserSkillByGameIdResponse) Reset()         { *m = GetUserSkillByGameIdResponse{} }
func (m *GetUserSkillByGameIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserSkillByGameIdResponse) ProtoMessage()    {}
func (*GetUserSkillByGameIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{7}
}
func (m *GetUserSkillByGameIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSkillByGameIdResponse.Unmarshal(m, b)
}
func (m *GetUserSkillByGameIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSkillByGameIdResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserSkillByGameIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSkillByGameIdResponse.Merge(dst, src)
}
func (m *GetUserSkillByGameIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserSkillByGameIdResponse.Size(m)
}
func (m *GetUserSkillByGameIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSkillByGameIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSkillByGameIdResponse proto.InternalMessageInfo

func (m *GetUserSkillByGameIdResponse) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetUserSkillByGameIdResponse) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetUserSkillByGameIdResponse) GetAuditSkill() []*UserSkillInfo {
	if m != nil {
		return m.AuditSkill
	}
	return nil
}

func (m *GetUserSkillByGameIdResponse) GetCurrentSkill() *UserSkillInfo {
	if m != nil {
		return m.CurrentSkill
	}
	return nil
}

func (m *GetUserSkillByGameIdResponse) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

func (m *GetUserSkillByGameIdResponse) GetHasGuaranteeWinPermission() bool {
	if m != nil {
		return m.HasGuaranteeWinPermission
	}
	return false
}

// uri: /modify_user_skill
// 前端接口： 修改用户技能
type ModifyUserSkillRequest struct {
	Skill                *UserSkillInfo `protobuf:"bytes,1,opt,name=skill,proto3" json:"skill"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ModifyUserSkillRequest) Reset()         { *m = ModifyUserSkillRequest{} }
func (m *ModifyUserSkillRequest) String() string { return proto.CompactTextString(m) }
func (*ModifyUserSkillRequest) ProtoMessage()    {}
func (*ModifyUserSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{8}
}
func (m *ModifyUserSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyUserSkillRequest.Unmarshal(m, b)
}
func (m *ModifyUserSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyUserSkillRequest.Marshal(b, m, deterministic)
}
func (dst *ModifyUserSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyUserSkillRequest.Merge(dst, src)
}
func (m *ModifyUserSkillRequest) XXX_Size() int {
	return xxx_messageInfo_ModifyUserSkillRequest.Size(m)
}
func (m *ModifyUserSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyUserSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyUserSkillRequest proto.InternalMessageInfo

func (m *ModifyUserSkillRequest) GetSkill() *UserSkillInfo {
	if m != nil {
		return m.Skill
	}
	return nil
}

type ModifyUserSkillResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyUserSkillResponse) Reset()         { *m = ModifyUserSkillResponse{} }
func (m *ModifyUserSkillResponse) String() string { return proto.CompactTextString(m) }
func (*ModifyUserSkillResponse) ProtoMessage()    {}
func (*ModifyUserSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{9}
}
func (m *ModifyUserSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyUserSkillResponse.Unmarshal(m, b)
}
func (m *ModifyUserSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyUserSkillResponse.Marshal(b, m, deterministic)
}
func (dst *ModifyUserSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyUserSkillResponse.Merge(dst, src)
}
func (m *ModifyUserSkillResponse) XXX_Size() int {
	return xxx_messageInfo_ModifyUserSkillResponse.Size(m)
}
func (m *ModifyUserSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyUserSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyUserSkillResponse proto.InternalMessageInfo

// uri: /del_user_skill
// 前端接口：删除技能
type DelUserSkillRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserSkillRequest) Reset()         { *m = DelUserSkillRequest{} }
func (m *DelUserSkillRequest) String() string { return proto.CompactTextString(m) }
func (*DelUserSkillRequest) ProtoMessage()    {}
func (*DelUserSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{10}
}
func (m *DelUserSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserSkillRequest.Unmarshal(m, b)
}
func (m *DelUserSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserSkillRequest.Marshal(b, m, deterministic)
}
func (dst *DelUserSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserSkillRequest.Merge(dst, src)
}
func (m *DelUserSkillRequest) XXX_Size() int {
	return xxx_messageInfo_DelUserSkillRequest.Size(m)
}
func (m *DelUserSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserSkillRequest proto.InternalMessageInfo

func (m *DelUserSkillRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type DelUserSkillResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserSkillResponse) Reset()         { *m = DelUserSkillResponse{} }
func (m *DelUserSkillResponse) String() string { return proto.CompactTextString(m) }
func (*DelUserSkillResponse) ProtoMessage()    {}
func (*DelUserSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{11}
}
func (m *DelUserSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserSkillResponse.Unmarshal(m, b)
}
func (m *DelUserSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserSkillResponse.Marshal(b, m, deterministic)
}
func (dst *DelUserSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserSkillResponse.Merge(dst, src)
}
func (m *DelUserSkillResponse) XXX_Size() int {
	return xxx_messageInfo_DelUserSkillResponse.Size(m)
}
func (m *DelUserSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserSkillResponse proto.InternalMessageInfo

// uri: /get_esport_master_info
// 前端接口：获取用户大神页信息
type GetESportMasterInfoRequest struct {
	TargetUid            uint32   `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetESportMasterInfoRequest) Reset()         { *m = GetESportMasterInfoRequest{} }
func (m *GetESportMasterInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetESportMasterInfoRequest) ProtoMessage()    {}
func (*GetESportMasterInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{12}
}
func (m *GetESportMasterInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportMasterInfoRequest.Unmarshal(m, b)
}
func (m *GetESportMasterInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportMasterInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetESportMasterInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportMasterInfoRequest.Merge(dst, src)
}
func (m *GetESportMasterInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetESportMasterInfoRequest.Size(m)
}
func (m *GetESportMasterInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportMasterInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportMasterInfoRequest proto.InternalMessageInfo

func (m *GetESportMasterInfoRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetESportMasterInfoResponse struct {
	TargetUid            uint32          `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid"`
	Account              string          `protobuf:"bytes,2,opt,name=account,proto3" json:"account"`
	NickName             string          `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name"`
	Ttid                 string          `protobuf:"bytes,4,opt,name=ttid,proto3" json:"ttid"`
	EsportRole           uint32          `protobuf:"varint,5,opt,name=esport_role,json=esportRole,proto3" json:"esport_role"`
	GuildId              uint32          `protobuf:"varint,6,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	GuildName            string          `protobuf:"bytes,7,opt,name=guild_name,json=guildName,proto3" json:"guild_name"`
	Divide               uint32          `protobuf:"varint,8,opt,name=divide,proto3" json:"divide"`
	StatList             []*TradeStat    `protobuf:"bytes,9,rep,name=stat_list,json=statList,proto3" json:"stat_list"`
	SkillList            []*GodPageSkill `protobuf:"bytes,10,rep,name=skill_list,json=skillList,proto3" json:"skill_list"`
	HeadDyMd5            string          `protobuf:"bytes,11,opt,name=head_dy_md5,json=headDyMd5,proto3" json:"head_dy_md5"`
	Label                *CoachLabel     `protobuf:"bytes,12,opt,name=label,proto3" json:"label"`
	CoachLabelList       []string        `protobuf:"bytes,13,rep,name=coach_label_list,json=coachLabelList,proto3" json:"coach_label_list"`
	GuaranteeDesc        string          `protobuf:"bytes,14,opt,name=guarantee_desc,json=guaranteeDesc,proto3" json:"guarantee_desc"`
	ShowGrabOrderCenter  bool            `protobuf:"varint,15,opt,name=show_grab_order_center,json=showGrabOrderCenter,proto3" json:"show_grab_order_center"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetESportMasterInfoResponse) Reset()         { *m = GetESportMasterInfoResponse{} }
func (m *GetESportMasterInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetESportMasterInfoResponse) ProtoMessage()    {}
func (*GetESportMasterInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{13}
}
func (m *GetESportMasterInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportMasterInfoResponse.Unmarshal(m, b)
}
func (m *GetESportMasterInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportMasterInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetESportMasterInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportMasterInfoResponse.Merge(dst, src)
}
func (m *GetESportMasterInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetESportMasterInfoResponse.Size(m)
}
func (m *GetESportMasterInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportMasterInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportMasterInfoResponse proto.InternalMessageInfo

func (m *GetESportMasterInfoResponse) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetESportMasterInfoResponse) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GetESportMasterInfoResponse) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *GetESportMasterInfoResponse) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *GetESportMasterInfoResponse) GetEsportRole() uint32 {
	if m != nil {
		return m.EsportRole
	}
	return 0
}

func (m *GetESportMasterInfoResponse) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetESportMasterInfoResponse) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *GetESportMasterInfoResponse) GetDivide() uint32 {
	if m != nil {
		return m.Divide
	}
	return 0
}

func (m *GetESportMasterInfoResponse) GetStatList() []*TradeStat {
	if m != nil {
		return m.StatList
	}
	return nil
}

func (m *GetESportMasterInfoResponse) GetSkillList() []*GodPageSkill {
	if m != nil {
		return m.SkillList
	}
	return nil
}

func (m *GetESportMasterInfoResponse) GetHeadDyMd5() string {
	if m != nil {
		return m.HeadDyMd5
	}
	return ""
}

func (m *GetESportMasterInfoResponse) GetLabel() *CoachLabel {
	if m != nil {
		return m.Label
	}
	return nil
}

func (m *GetESportMasterInfoResponse) GetCoachLabelList() []string {
	if m != nil {
		return m.CoachLabelList
	}
	return nil
}

func (m *GetESportMasterInfoResponse) GetGuaranteeDesc() string {
	if m != nil {
		return m.GuaranteeDesc
	}
	return ""
}

func (m *GetESportMasterInfoResponse) GetShowGrabOrderCenter() bool {
	if m != nil {
		return m.ShowGrabOrderCenter
	}
	return false
}

type TradeData struct {
	Income               uint32   `protobuf:"varint,1,opt,name=income,proto3" json:"income"`
	ServiceUser          uint32   `protobuf:"varint,2,opt,name=service_user,json=serviceUser,proto3" json:"service_user"`
	RetradeUser          uint32   `protobuf:"varint,3,opt,name=retrade_user,json=retradeUser,proto3" json:"retrade_user"`
	FreshUser            uint32   `protobuf:"varint,4,opt,name=fresh_user,json=freshUser,proto3" json:"fresh_user"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TradeData) Reset()         { *m = TradeData{} }
func (m *TradeData) String() string { return proto.CompactTextString(m) }
func (*TradeData) ProtoMessage()    {}
func (*TradeData) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{14}
}
func (m *TradeData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TradeData.Unmarshal(m, b)
}
func (m *TradeData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TradeData.Marshal(b, m, deterministic)
}
func (dst *TradeData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TradeData.Merge(dst, src)
}
func (m *TradeData) XXX_Size() int {
	return xxx_messageInfo_TradeData.Size(m)
}
func (m *TradeData) XXX_DiscardUnknown() {
	xxx_messageInfo_TradeData.DiscardUnknown(m)
}

var xxx_messageInfo_TradeData proto.InternalMessageInfo

func (m *TradeData) GetIncome() uint32 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *TradeData) GetServiceUser() uint32 {
	if m != nil {
		return m.ServiceUser
	}
	return 0
}

func (m *TradeData) GetRetradeUser() uint32 {
	if m != nil {
		return m.RetradeUser
	}
	return 0
}

func (m *TradeData) GetFreshUser() uint32 {
	if m != nil {
		return m.FreshUser
	}
	return 0
}

type TradeStat struct {
	StatType             uint32     `protobuf:"varint,1,opt,name=stat_type,json=statType,proto3" json:"stat_type"`
	Trade                *TradeData `protobuf:"bytes,2,opt,name=trade,proto3" json:"trade"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *TradeStat) Reset()         { *m = TradeStat{} }
func (m *TradeStat) String() string { return proto.CompactTextString(m) }
func (*TradeStat) ProtoMessage()    {}
func (*TradeStat) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{15}
}
func (m *TradeStat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TradeStat.Unmarshal(m, b)
}
func (m *TradeStat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TradeStat.Marshal(b, m, deterministic)
}
func (dst *TradeStat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TradeStat.Merge(dst, src)
}
func (m *TradeStat) XXX_Size() int {
	return xxx_messageInfo_TradeStat.Size(m)
}
func (m *TradeStat) XXX_DiscardUnknown() {
	xxx_messageInfo_TradeStat.DiscardUnknown(m)
}

var xxx_messageInfo_TradeStat proto.InternalMessageInfo

func (m *TradeStat) GetStatType() uint32 {
	if m != nil {
		return m.StatType
	}
	return 0
}

func (m *TradeStat) GetTrade() *TradeData {
	if m != nil {
		return m.Trade
	}
	return nil
}

type CoachLabel struct {
	Type                 LabelSourceType `protobuf:"varint,1,opt,name=type,proto3,enum=esport_http.LabelSourceType" json:"type"`
	SourceUrl            string          `protobuf:"bytes,2,opt,name=source_url,json=sourceUrl,proto3" json:"source_url"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CoachLabel) Reset()         { *m = CoachLabel{} }
func (m *CoachLabel) String() string { return proto.CompactTextString(m) }
func (*CoachLabel) ProtoMessage()    {}
func (*CoachLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{16}
}
func (m *CoachLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoachLabel.Unmarshal(m, b)
}
func (m *CoachLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoachLabel.Marshal(b, m, deterministic)
}
func (dst *CoachLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoachLabel.Merge(dst, src)
}
func (m *CoachLabel) XXX_Size() int {
	return xxx_messageInfo_CoachLabel.Size(m)
}
func (m *CoachLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_CoachLabel.DiscardUnknown(m)
}

var xxx_messageInfo_CoachLabel proto.InternalMessageInfo

func (m *CoachLabel) GetType() LabelSourceType {
	if m != nil {
		return m.Type
	}
	return LabelSourceType_LABEL_SOURCE_TYPE_UNSPECIFIED
}

func (m *CoachLabel) GetSourceUrl() string {
	if m != nil {
		return m.SourceUrl
	}
	return ""
}

// uri: /batch_get_audit_skill
// 公会后台 获取新增技能审核信息请求
type BatchGetAuditSkillRequest struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	Uids                 []uint32 `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids"`
	AuditType            []uint32 `protobuf:"varint,3,rep,packed,name=audit_type,json=auditType,proto3" json:"audit_type"`
	OffSet               uint32   `protobuf:"varint,4,opt,name=off_set,json=offSet,proto3" json:"off_set"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit"`
	NeedTotal            uint32   `protobuf:"varint,6,opt,name=need_total,json=needTotal,proto3" json:"need_total"`
	SearchType           uint32   `protobuf:"varint,7,opt,name=search_type,json=searchType,proto3" json:"search_type"`
	AuditSource          []uint32 `protobuf:"varint,8,rep,packed,name=audit_source,json=auditSource,proto3" json:"audit_source"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetAuditSkillRequest) Reset()         { *m = BatchGetAuditSkillRequest{} }
func (m *BatchGetAuditSkillRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetAuditSkillRequest) ProtoMessage()    {}
func (*BatchGetAuditSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{17}
}
func (m *BatchGetAuditSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAuditSkillRequest.Unmarshal(m, b)
}
func (m *BatchGetAuditSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAuditSkillRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetAuditSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAuditSkillRequest.Merge(dst, src)
}
func (m *BatchGetAuditSkillRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetAuditSkillRequest.Size(m)
}
func (m *BatchGetAuditSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAuditSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAuditSkillRequest proto.InternalMessageInfo

func (m *BatchGetAuditSkillRequest) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *BatchGetAuditSkillRequest) GetAuditType() []uint32 {
	if m != nil {
		return m.AuditType
	}
	return nil
}

func (m *BatchGetAuditSkillRequest) GetOffSet() uint32 {
	if m != nil {
		return m.OffSet
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetNeedTotal() uint32 {
	if m != nil {
		return m.NeedTotal
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetSearchType() uint32 {
	if m != nil {
		return m.SearchType
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetAuditSource() []uint32 {
	if m != nil {
		return m.AuditSource
	}
	return nil
}

type AuditSkillRecord struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Account              string           `protobuf:"bytes,2,opt,name=account,proto3" json:"account"`
	NickName             string           `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name"`
	Ttid                 string           `protobuf:"bytes,4,opt,name=ttid,proto3" json:"ttid"`
	AuditToken           string           `protobuf:"bytes,5,opt,name=audit_token,json=auditToken,proto3" json:"audit_token"`
	AuditType            uint32           `protobuf:"varint,6,opt,name=audit_type,json=auditType,proto3" json:"audit_type"`
	ApplyTime            uint32           `protobuf:"varint,7,opt,name=apply_time,json=applyTime,proto3" json:"apply_time"`
	Skill                []*UserSkillInfo `protobuf:"bytes,8,rep,name=skill,proto3" json:"skill"`
	AuditSource          uint32           `protobuf:"varint,9,opt,name=audit_source,json=auditSource,proto3" json:"audit_source"`
	Reason               string           `protobuf:"bytes,10,opt,name=reason,proto3" json:"reason"`
	GuildId              uint32           `protobuf:"varint,11,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	SignDuration         string           `protobuf:"bytes,12,opt,name=sign_duration,json=signDuration,proto3" json:"sign_duration"`
	Operator             string           `protobuf:"bytes,13,opt,name=operator,proto3" json:"operator"`
	UpdateTime           uint32           `protobuf:"varint,14,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *AuditSkillRecord) Reset()         { *m = AuditSkillRecord{} }
func (m *AuditSkillRecord) String() string { return proto.CompactTextString(m) }
func (*AuditSkillRecord) ProtoMessage()    {}
func (*AuditSkillRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{18}
}
func (m *AuditSkillRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuditSkillRecord.Unmarshal(m, b)
}
func (m *AuditSkillRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuditSkillRecord.Marshal(b, m, deterministic)
}
func (dst *AuditSkillRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuditSkillRecord.Merge(dst, src)
}
func (m *AuditSkillRecord) XXX_Size() int {
	return xxx_messageInfo_AuditSkillRecord.Size(m)
}
func (m *AuditSkillRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_AuditSkillRecord.DiscardUnknown(m)
}

var xxx_messageInfo_AuditSkillRecord proto.InternalMessageInfo

func (m *AuditSkillRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AuditSkillRecord) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *AuditSkillRecord) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *AuditSkillRecord) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *AuditSkillRecord) GetAuditToken() string {
	if m != nil {
		return m.AuditToken
	}
	return ""
}

func (m *AuditSkillRecord) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

func (m *AuditSkillRecord) GetApplyTime() uint32 {
	if m != nil {
		return m.ApplyTime
	}
	return 0
}

func (m *AuditSkillRecord) GetSkill() []*UserSkillInfo {
	if m != nil {
		return m.Skill
	}
	return nil
}

func (m *AuditSkillRecord) GetAuditSource() uint32 {
	if m != nil {
		return m.AuditSource
	}
	return 0
}

func (m *AuditSkillRecord) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *AuditSkillRecord) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AuditSkillRecord) GetSignDuration() string {
	if m != nil {
		return m.SignDuration
	}
	return ""
}

func (m *AuditSkillRecord) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *AuditSkillRecord) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

// 获取新增技能审核信息请求
type BatchGetAuditSkillResponse struct {
	GuildId              uint32              `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	Uids                 []uint32            `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids"`
	AuditType            []uint32            `protobuf:"varint,3,rep,packed,name=audit_type,json=auditType,proto3" json:"audit_type"`
	OffSet               uint32              `protobuf:"varint,4,opt,name=off_set,json=offSet,proto3" json:"off_set"`
	Limit                uint32              `protobuf:"varint,5,opt,name=limit,proto3" json:"limit"`
	List                 []*AuditSkillRecord `protobuf:"bytes,6,rep,name=list,proto3" json:"list"`
	TotalCount           uint32              `protobuf:"varint,7,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	SearchType           uint32              `protobuf:"varint,8,opt,name=search_type,json=searchType,proto3" json:"search_type"`
	AuditSource          []uint32            `protobuf:"varint,9,rep,packed,name=audit_source,json=auditSource,proto3" json:"audit_source"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatchGetAuditSkillResponse) Reset()         { *m = BatchGetAuditSkillResponse{} }
func (m *BatchGetAuditSkillResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetAuditSkillResponse) ProtoMessage()    {}
func (*BatchGetAuditSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{19}
}
func (m *BatchGetAuditSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAuditSkillResponse.Unmarshal(m, b)
}
func (m *BatchGetAuditSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAuditSkillResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetAuditSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAuditSkillResponse.Merge(dst, src)
}
func (m *BatchGetAuditSkillResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetAuditSkillResponse.Size(m)
}
func (m *BatchGetAuditSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAuditSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAuditSkillResponse proto.InternalMessageInfo

func (m *BatchGetAuditSkillResponse) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetAuditSkillResponse) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *BatchGetAuditSkillResponse) GetAuditType() []uint32 {
	if m != nil {
		return m.AuditType
	}
	return nil
}

func (m *BatchGetAuditSkillResponse) GetOffSet() uint32 {
	if m != nil {
		return m.OffSet
	}
	return 0
}

func (m *BatchGetAuditSkillResponse) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *BatchGetAuditSkillResponse) GetList() []*AuditSkillRecord {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *BatchGetAuditSkillResponse) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *BatchGetAuditSkillResponse) GetSearchType() uint32 {
	if m != nil {
		return m.SearchType
	}
	return 0
}

func (m *BatchGetAuditSkillResponse) GetAuditSource() []uint32 {
	if m != nil {
		return m.AuditSource
	}
	return nil
}

// uri: /set_user_skill_audit_type
// 公会后台:设置用户技能审核结果
type SetUserSkillAuditTypeRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	AuditToken           string   `protobuf:"bytes,2,opt,name=audit_token,json=auditToken,proto3" json:"audit_token"`
	AuditType            uint32   `protobuf:"varint,3,opt,name=audit_type,json=auditType,proto3" json:"audit_type"`
	Reason               string   `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserSkillAuditTypeRequest) Reset()         { *m = SetUserSkillAuditTypeRequest{} }
func (m *SetUserSkillAuditTypeRequest) String() string { return proto.CompactTextString(m) }
func (*SetUserSkillAuditTypeRequest) ProtoMessage()    {}
func (*SetUserSkillAuditTypeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{20}
}
func (m *SetUserSkillAuditTypeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserSkillAuditTypeRequest.Unmarshal(m, b)
}
func (m *SetUserSkillAuditTypeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserSkillAuditTypeRequest.Marshal(b, m, deterministic)
}
func (dst *SetUserSkillAuditTypeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserSkillAuditTypeRequest.Merge(dst, src)
}
func (m *SetUserSkillAuditTypeRequest) XXX_Size() int {
	return xxx_messageInfo_SetUserSkillAuditTypeRequest.Size(m)
}
func (m *SetUserSkillAuditTypeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserSkillAuditTypeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserSkillAuditTypeRequest proto.InternalMessageInfo

func (m *SetUserSkillAuditTypeRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserSkillAuditTypeRequest) GetAuditToken() string {
	if m != nil {
		return m.AuditToken
	}
	return ""
}

func (m *SetUserSkillAuditTypeRequest) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

func (m *SetUserSkillAuditTypeRequest) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type SetUserSkillAuditTypeResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserSkillAuditTypeResponse) Reset()         { *m = SetUserSkillAuditTypeResponse{} }
func (m *SetUserSkillAuditTypeResponse) String() string { return proto.CompactTextString(m) }
func (*SetUserSkillAuditTypeResponse) ProtoMessage()    {}
func (*SetUserSkillAuditTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{21}
}
func (m *SetUserSkillAuditTypeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserSkillAuditTypeResponse.Unmarshal(m, b)
}
func (m *SetUserSkillAuditTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserSkillAuditTypeResponse.Marshal(b, m, deterministic)
}
func (dst *SetUserSkillAuditTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserSkillAuditTypeResponse.Merge(dst, src)
}
func (m *SetUserSkillAuditTypeResponse) XXX_Size() int {
	return xxx_messageInfo_SetUserSkillAuditTypeResponse.Size(m)
}
func (m *SetUserSkillAuditTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserSkillAuditTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserSkillAuditTypeResponse proto.InternalMessageInfo

type ESportGameSimpleInfo struct {
	GameId               uint32     `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	Name                 string     `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Icon                 string     `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon"`
	GameType             uint32     `protobuf:"varint,4,opt,name=game_type,json=gameType,proto3" json:"game_type"`
	Rank                 uint32     `protobuf:"varint,5,opt,name=rank,proto3" json:"rank"`
	AuditType            uint32     `protobuf:"varint,6,opt,name=audit_type,json=auditType,proto3" json:"audit_type"`
	FreezeType           FreezeType `protobuf:"varint,7,opt,name=freeze_type,json=freezeType,proto3,enum=esport_http.FreezeType" json:"freeze_type"`
	FreezeStopTs         int64      `protobuf:"varint,8,opt,name=freeze_stop_ts,json=freezeStopTs,proto3" json:"freeze_stop_ts"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ESportGameSimpleInfo) Reset()         { *m = ESportGameSimpleInfo{} }
func (m *ESportGameSimpleInfo) String() string { return proto.CompactTextString(m) }
func (*ESportGameSimpleInfo) ProtoMessage()    {}
func (*ESportGameSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{22}
}
func (m *ESportGameSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ESportGameSimpleInfo.Unmarshal(m, b)
}
func (m *ESportGameSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ESportGameSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *ESportGameSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ESportGameSimpleInfo.Merge(dst, src)
}
func (m *ESportGameSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_ESportGameSimpleInfo.Size(m)
}
func (m *ESportGameSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ESportGameSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ESportGameSimpleInfo proto.InternalMessageInfo

func (m *ESportGameSimpleInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ESportGameSimpleInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ESportGameSimpleInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *ESportGameSimpleInfo) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *ESportGameSimpleInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *ESportGameSimpleInfo) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

func (m *ESportGameSimpleInfo) GetFreezeType() FreezeType {
	if m != nil {
		return m.FreezeType
	}
	return FreezeType_FREEZE_TYPE_UNFREEZE
}

func (m *ESportGameSimpleInfo) GetFreezeStopTs() int64 {
	if m != nil {
		return m.FreezeStopTs
	}
	return 0
}

type ESportGameTypeInfo struct {
	GameType             uint32   `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3" json:"game_type"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Rank                 uint32   `protobuf:"varint,3,opt,name=rank,proto3" json:"rank"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ESportGameTypeInfo) Reset()         { *m = ESportGameTypeInfo{} }
func (m *ESportGameTypeInfo) String() string { return proto.CompactTextString(m) }
func (*ESportGameTypeInfo) ProtoMessage()    {}
func (*ESportGameTypeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{23}
}
func (m *ESportGameTypeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ESportGameTypeInfo.Unmarshal(m, b)
}
func (m *ESportGameTypeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ESportGameTypeInfo.Marshal(b, m, deterministic)
}
func (dst *ESportGameTypeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ESportGameTypeInfo.Merge(dst, src)
}
func (m *ESportGameTypeInfo) XXX_Size() int {
	return xxx_messageInfo_ESportGameTypeInfo.Size(m)
}
func (m *ESportGameTypeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ESportGameTypeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ESportGameTypeInfo proto.InternalMessageInfo

func (m *ESportGameTypeInfo) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *ESportGameTypeInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ESportGameTypeInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

// url: /get_game_list
// 根据当前用户身份，如果已是陪陪，则返回用户已有技能游戏列表；如果是普通用户身份，则返回游戏列表
type GetESportGameListRequest struct {
	// uint32 uid = 1;
	Source               uint32   `protobuf:"varint,1,opt,name=source,proto3" json:"source"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetESportGameListRequest) Reset()         { *m = GetESportGameListRequest{} }
func (m *GetESportGameListRequest) String() string { return proto.CompactTextString(m) }
func (*GetESportGameListRequest) ProtoMessage()    {}
func (*GetESportGameListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{24}
}
func (m *GetESportGameListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportGameListRequest.Unmarshal(m, b)
}
func (m *GetESportGameListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportGameListRequest.Marshal(b, m, deterministic)
}
func (dst *GetESportGameListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportGameListRequest.Merge(dst, src)
}
func (m *GetESportGameListRequest) XXX_Size() int {
	return xxx_messageInfo_GetESportGameListRequest.Size(m)
}
func (m *GetESportGameListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportGameListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportGameListRequest proto.InternalMessageInfo

func (m *GetESportGameListRequest) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

type GetESportGameListResponse struct {
	UserCurRole  uint32                  `protobuf:"varint,1,opt,name=user_cur_role,json=userCurRole,proto3" json:"user_cur_role"`
	GameList     []*ESportGameSimpleInfo `protobuf:"bytes,2,rep,name=game_list,json=gameList,proto3" json:"game_list"`
	GameTypeList []*ESportGameTypeInfo   `protobuf:"bytes,3,rep,name=game_type_list,json=gameTypeList,proto3" json:"game_type_list"`
	// 当前审核状态
	ApplyStatus          uint32   `protobuf:"varint,4,opt,name=apply_status,json=applyStatus,proto3" json:"apply_status"`
	ApplyType            uint32   `protobuf:"varint,5,opt,name=apply_type,json=applyType,proto3" json:"apply_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetESportGameListResponse) Reset()         { *m = GetESportGameListResponse{} }
func (m *GetESportGameListResponse) String() string { return proto.CompactTextString(m) }
func (*GetESportGameListResponse) ProtoMessage()    {}
func (*GetESportGameListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{25}
}
func (m *GetESportGameListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportGameListResponse.Unmarshal(m, b)
}
func (m *GetESportGameListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportGameListResponse.Marshal(b, m, deterministic)
}
func (dst *GetESportGameListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportGameListResponse.Merge(dst, src)
}
func (m *GetESportGameListResponse) XXX_Size() int {
	return xxx_messageInfo_GetESportGameListResponse.Size(m)
}
func (m *GetESportGameListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportGameListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportGameListResponse proto.InternalMessageInfo

func (m *GetESportGameListResponse) GetUserCurRole() uint32 {
	if m != nil {
		return m.UserCurRole
	}
	return 0
}

func (m *GetESportGameListResponse) GetGameList() []*ESportGameSimpleInfo {
	if m != nil {
		return m.GameList
	}
	return nil
}

func (m *GetESportGameListResponse) GetGameTypeList() []*ESportGameTypeInfo {
	if m != nil {
		return m.GameTypeList
	}
	return nil
}

func (m *GetESportGameListResponse) GetApplyStatus() uint32 {
	if m != nil {
		return m.ApplyStatus
	}
	return 0
}

func (m *GetESportGameListResponse) GetApplyType() uint32 {
	if m != nil {
		return m.ApplyType
	}
	return 0
}

// url: /apply/apply_risk_check
// 申请风控检查，可能命中人脸风控检查，前端需要根据对应的错误码做相应处理
type ApplyRiskCheckRequest struct {
	DeviceId             string   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
	Ip                   string   `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip"`
	ClientType           uint32   `protobuf:"varint,3,opt,name=client_type,json=clientType,proto3" json:"client_type"`
	AppVersion           uint32   `protobuf:"varint,4,opt,name=app_version,json=appVersion,proto3" json:"app_version"`
	FaceAuthResultToken  string   `protobuf:"bytes,5,opt,name=face_auth_result_token,json=faceAuthResultToken,proto3" json:"face_auth_result_token"`
	Source               uint32   `protobuf:"varint,6,opt,name=source,proto3" json:"source"`
	GuildId              uint32   `protobuf:"varint,7,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyRiskCheckRequest) Reset()         { *m = ApplyRiskCheckRequest{} }
func (m *ApplyRiskCheckRequest) String() string { return proto.CompactTextString(m) }
func (*ApplyRiskCheckRequest) ProtoMessage()    {}
func (*ApplyRiskCheckRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{26}
}
func (m *ApplyRiskCheckRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyRiskCheckRequest.Unmarshal(m, b)
}
func (m *ApplyRiskCheckRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyRiskCheckRequest.Marshal(b, m, deterministic)
}
func (dst *ApplyRiskCheckRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyRiskCheckRequest.Merge(dst, src)
}
func (m *ApplyRiskCheckRequest) XXX_Size() int {
	return xxx_messageInfo_ApplyRiskCheckRequest.Size(m)
}
func (m *ApplyRiskCheckRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyRiskCheckRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyRiskCheckRequest proto.InternalMessageInfo

func (m *ApplyRiskCheckRequest) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *ApplyRiskCheckRequest) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *ApplyRiskCheckRequest) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *ApplyRiskCheckRequest) GetAppVersion() uint32 {
	if m != nil {
		return m.AppVersion
	}
	return 0
}

func (m *ApplyRiskCheckRequest) GetFaceAuthResultToken() string {
	if m != nil {
		return m.FaceAuthResultToken
	}
	return ""
}

func (m *ApplyRiskCheckRequest) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *ApplyRiskCheckRequest) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type ApplyRiskCheckResponse struct {
	UserCurRole uint32 `protobuf:"varint,1,opt,name=user_cur_role,json=userCurRole,proto3" json:"user_cur_role"`
	// 当前审核状态
	ApplyStatus uint32 `protobuf:"varint,2,opt,name=apply_status,json=applyStatus,proto3" json:"apply_status"`
	ApplyType   uint32 `protobuf:"varint,3,opt,name=apply_type,json=applyType,proto3" json:"apply_type"`
	// 人脸context
	FaceAuthContextJson  string   `protobuf:"bytes,4,opt,name=face_auth_context_json,json=faceAuthContextJson,proto3" json:"face_auth_context_json"`
	GuildId              uint32   `protobuf:"varint,5,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyRiskCheckResponse) Reset()         { *m = ApplyRiskCheckResponse{} }
func (m *ApplyRiskCheckResponse) String() string { return proto.CompactTextString(m) }
func (*ApplyRiskCheckResponse) ProtoMessage()    {}
func (*ApplyRiskCheckResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{27}
}
func (m *ApplyRiskCheckResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyRiskCheckResponse.Unmarshal(m, b)
}
func (m *ApplyRiskCheckResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyRiskCheckResponse.Marshal(b, m, deterministic)
}
func (dst *ApplyRiskCheckResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyRiskCheckResponse.Merge(dst, src)
}
func (m *ApplyRiskCheckResponse) XXX_Size() int {
	return xxx_messageInfo_ApplyRiskCheckResponse.Size(m)
}
func (m *ApplyRiskCheckResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyRiskCheckResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyRiskCheckResponse proto.InternalMessageInfo

func (m *ApplyRiskCheckResponse) GetUserCurRole() uint32 {
	if m != nil {
		return m.UserCurRole
	}
	return 0
}

func (m *ApplyRiskCheckResponse) GetApplyStatus() uint32 {
	if m != nil {
		return m.ApplyStatus
	}
	return 0
}

func (m *ApplyRiskCheckResponse) GetApplyType() uint32 {
	if m != nil {
		return m.ApplyType
	}
	return 0
}

func (m *ApplyRiskCheckResponse) GetFaceAuthContextJson() string {
	if m != nil {
		return m.FaceAuthContextJson
	}
	return ""
}

func (m *ApplyRiskCheckResponse) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetEsportGameListWithAuditRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEsportGameListWithAuditRequest) Reset()         { *m = GetEsportGameListWithAuditRequest{} }
func (m *GetEsportGameListWithAuditRequest) String() string { return proto.CompactTextString(m) }
func (*GetEsportGameListWithAuditRequest) ProtoMessage()    {}
func (*GetEsportGameListWithAuditRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{28}
}
func (m *GetEsportGameListWithAuditRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportGameListWithAuditRequest.Unmarshal(m, b)
}
func (m *GetEsportGameListWithAuditRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportGameListWithAuditRequest.Marshal(b, m, deterministic)
}
func (dst *GetEsportGameListWithAuditRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportGameListWithAuditRequest.Merge(dst, src)
}
func (m *GetEsportGameListWithAuditRequest) XXX_Size() int {
	return xxx_messageInfo_GetEsportGameListWithAuditRequest.Size(m)
}
func (m *GetEsportGameListWithAuditRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportGameListWithAuditRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportGameListWithAuditRequest proto.InternalMessageInfo

type GetEsportGameListWithAuditResponse struct {
	GameList             []*ESportGameSimpleInfo `protobuf:"bytes,1,rep,name=game_list,json=gameList,proto3" json:"game_list"`
	GameTypeList         []*ESportGameTypeInfo   `protobuf:"bytes,2,rep,name=game_type_list,json=gameTypeList,proto3" json:"game_type_list"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetEsportGameListWithAuditResponse) Reset()         { *m = GetEsportGameListWithAuditResponse{} }
func (m *GetEsportGameListWithAuditResponse) String() string { return proto.CompactTextString(m) }
func (*GetEsportGameListWithAuditResponse) ProtoMessage()    {}
func (*GetEsportGameListWithAuditResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{29}
}
func (m *GetEsportGameListWithAuditResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportGameListWithAuditResponse.Unmarshal(m, b)
}
func (m *GetEsportGameListWithAuditResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportGameListWithAuditResponse.Marshal(b, m, deterministic)
}
func (dst *GetEsportGameListWithAuditResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportGameListWithAuditResponse.Merge(dst, src)
}
func (m *GetEsportGameListWithAuditResponse) XXX_Size() int {
	return xxx_messageInfo_GetEsportGameListWithAuditResponse.Size(m)
}
func (m *GetEsportGameListWithAuditResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportGameListWithAuditResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportGameListWithAuditResponse proto.InternalMessageInfo

func (m *GetEsportGameListWithAuditResponse) GetGameList() []*ESportGameSimpleInfo {
	if m != nil {
		return m.GameList
	}
	return nil
}

func (m *GetEsportGameListWithAuditResponse) GetGameTypeList() []*ESportGameTypeInfo {
	if m != nil {
		return m.GameTypeList
	}
	return nil
}

type SelfIntroConf struct {
	TextMaxCnt           uint32   `protobuf:"varint,1,opt,name=text_max_cnt,json=textMaxCnt,proto3" json:"text_max_cnt"`
	AudioMaxSec          uint32   `protobuf:"varint,2,opt,name=audio_max_sec,json=audioMaxSec,proto3" json:"audio_max_sec"`
	AudioReq             bool     `protobuf:"varint,3,opt,name=audio_req,json=audioReq,proto3" json:"audio_req"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SelfIntroConf) Reset()         { *m = SelfIntroConf{} }
func (m *SelfIntroConf) String() string { return proto.CompactTextString(m) }
func (*SelfIntroConf) ProtoMessage()    {}
func (*SelfIntroConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{30}
}
func (m *SelfIntroConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelfIntroConf.Unmarshal(m, b)
}
func (m *SelfIntroConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelfIntroConf.Marshal(b, m, deterministic)
}
func (dst *SelfIntroConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelfIntroConf.Merge(dst, src)
}
func (m *SelfIntroConf) XXX_Size() int {
	return xxx_messageInfo_SelfIntroConf.Size(m)
}
func (m *SelfIntroConf) XXX_DiscardUnknown() {
	xxx_messageInfo_SelfIntroConf.DiscardUnknown(m)
}

var xxx_messageInfo_SelfIntroConf proto.InternalMessageInfo

func (m *SelfIntroConf) GetTextMaxCnt() uint32 {
	if m != nil {
		return m.TextMaxCnt
	}
	return 0
}

func (m *SelfIntroConf) GetAudioMaxSec() uint32 {
	if m != nil {
		return m.AudioMaxSec
	}
	return 0
}

func (m *SelfIntroConf) GetAudioReq() bool {
	if m != nil {
		return m.AudioReq
	}
	return false
}

// 游戏资料
type GameInformation struct {
	InformationType      uint32   `protobuf:"varint,1,opt,name=information_type,json=informationType,proto3" json:"information_type"`
	SectionName          string   `protobuf:"bytes,2,opt,name=section_name,json=sectionName,proto3" json:"section_name"`
	ItemList             []string `protobuf:"bytes,3,rep,name=item_list,json=itemList,proto3" json:"item_list"`
	SelectType           uint32   `protobuf:"varint,4,opt,name=select_type,json=selectType,proto3" json:"select_type"`
	SectionId            uint32   `protobuf:"varint,5,opt,name=section_id,json=sectionId,proto3" json:"section_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameInformation) Reset()         { *m = GameInformation{} }
func (m *GameInformation) String() string { return proto.CompactTextString(m) }
func (*GameInformation) ProtoMessage()    {}
func (*GameInformation) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{31}
}
func (m *GameInformation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameInformation.Unmarshal(m, b)
}
func (m *GameInformation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameInformation.Marshal(b, m, deterministic)
}
func (dst *GameInformation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameInformation.Merge(dst, src)
}
func (m *GameInformation) XXX_Size() int {
	return xxx_messageInfo_GameInformation.Size(m)
}
func (m *GameInformation) XXX_DiscardUnknown() {
	xxx_messageInfo_GameInformation.DiscardUnknown(m)
}

var xxx_messageInfo_GameInformation proto.InternalMessageInfo

func (m *GameInformation) GetInformationType() uint32 {
	if m != nil {
		return m.InformationType
	}
	return 0
}

func (m *GameInformation) GetSectionName() string {
	if m != nil {
		return m.SectionName
	}
	return ""
}

func (m *GameInformation) GetItemList() []string {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *GameInformation) GetSelectType() uint32 {
	if m != nil {
		return m.SelectType
	}
	return 0
}

func (m *GameInformation) GetSectionId() uint32 {
	if m != nil {
		return m.SectionId
	}
	return 0
}

type UserGameInfo struct {
	SkillUrl             string             `protobuf:"bytes,1,opt,name=skill_url,json=skillUrl,proto3" json:"skill_url"`
	IntroText            string             `protobuf:"bytes,2,opt,name=intro_text,json=introText,proto3" json:"intro_text"`
	IntroAudio           string             `protobuf:"bytes,3,opt,name=intro_audio,json=introAudio,proto3" json:"intro_audio"`
	InfoList             []*GameInformation `protobuf:"bytes,4,rep,name=info_list,json=infoList,proto3" json:"info_list"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UserGameInfo) Reset()         { *m = UserGameInfo{} }
func (m *UserGameInfo) String() string { return proto.CompactTextString(m) }
func (*UserGameInfo) ProtoMessage()    {}
func (*UserGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{32}
}
func (m *UserGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserGameInfo.Unmarshal(m, b)
}
func (m *UserGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserGameInfo.Marshal(b, m, deterministic)
}
func (dst *UserGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserGameInfo.Merge(dst, src)
}
func (m *UserGameInfo) XXX_Size() int {
	return xxx_messageInfo_UserGameInfo.Size(m)
}
func (m *UserGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserGameInfo proto.InternalMessageInfo

func (m *UserGameInfo) GetSkillUrl() string {
	if m != nil {
		return m.SkillUrl
	}
	return ""
}

func (m *UserGameInfo) GetIntroText() string {
	if m != nil {
		return m.IntroText
	}
	return ""
}

func (m *UserGameInfo) GetIntroAudio() string {
	if m != nil {
		return m.IntroAudio
	}
	return ""
}

func (m *UserGameInfo) GetInfoList() []*GameInformation {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type GameSkillConf struct {
	GameId               uint32             `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	Name                 string             `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	GameType             uint32             `protobuf:"varint,3,opt,name=game_type,json=gameType,proto3" json:"game_type"`
	GameIcon             string             `protobuf:"bytes,4,opt,name=game_icon,json=gameIcon,proto3" json:"game_icon"`
	GameBackground       string             `protobuf:"bytes,5,opt,name=game_background,json=gameBackground,proto3" json:"game_background"`
	GameColor            string             `protobuf:"bytes,6,opt,name=game_color,json=gameColor,proto3" json:"game_color"`
	SkillEvidence        string             `protobuf:"bytes,7,opt,name=skill_evidence,json=skillEvidence,proto3" json:"skill_evidence"`
	SkillDesc            string             `protobuf:"bytes,8,opt,name=skill_desc,json=skillDesc,proto3" json:"skill_desc"`
	GameInfoList         []*GameInformation `protobuf:"bytes,9,rep,name=game_info_list,json=gameInfoList,proto3" json:"game_info_list"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GameSkillConf) Reset()         { *m = GameSkillConf{} }
func (m *GameSkillConf) String() string { return proto.CompactTextString(m) }
func (*GameSkillConf) ProtoMessage()    {}
func (*GameSkillConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{33}
}
func (m *GameSkillConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameSkillConf.Unmarshal(m, b)
}
func (m *GameSkillConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameSkillConf.Marshal(b, m, deterministic)
}
func (dst *GameSkillConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameSkillConf.Merge(dst, src)
}
func (m *GameSkillConf) XXX_Size() int {
	return xxx_messageInfo_GameSkillConf.Size(m)
}
func (m *GameSkillConf) XXX_DiscardUnknown() {
	xxx_messageInfo_GameSkillConf.DiscardUnknown(m)
}

var xxx_messageInfo_GameSkillConf proto.InternalMessageInfo

func (m *GameSkillConf) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GameSkillConf) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GameSkillConf) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *GameSkillConf) GetGameIcon() string {
	if m != nil {
		return m.GameIcon
	}
	return ""
}

func (m *GameSkillConf) GetGameBackground() string {
	if m != nil {
		return m.GameBackground
	}
	return ""
}

func (m *GameSkillConf) GetGameColor() string {
	if m != nil {
		return m.GameColor
	}
	return ""
}

func (m *GameSkillConf) GetSkillEvidence() string {
	if m != nil {
		return m.SkillEvidence
	}
	return ""
}

func (m *GameSkillConf) GetSkillDesc() string {
	if m != nil {
		return m.SkillDesc
	}
	return ""
}

func (m *GameSkillConf) GetGameInfoList() []*GameInformation {
	if m != nil {
		return m.GameInfoList
	}
	return nil
}

// url: /apply/get_skill_conf
// 获取指定游戏技能配置
type GetESportGameSkillConfRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetESportGameSkillConfRequest) Reset()         { *m = GetESportGameSkillConfRequest{} }
func (m *GetESportGameSkillConfRequest) String() string { return proto.CompactTextString(m) }
func (*GetESportGameSkillConfRequest) ProtoMessage()    {}
func (*GetESportGameSkillConfRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{34}
}
func (m *GetESportGameSkillConfRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportGameSkillConfRequest.Unmarshal(m, b)
}
func (m *GetESportGameSkillConfRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportGameSkillConfRequest.Marshal(b, m, deterministic)
}
func (dst *GetESportGameSkillConfRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportGameSkillConfRequest.Merge(dst, src)
}
func (m *GetESportGameSkillConfRequest) XXX_Size() int {
	return xxx_messageInfo_GetESportGameSkillConfRequest.Size(m)
}
func (m *GetESportGameSkillConfRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportGameSkillConfRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportGameSkillConfRequest proto.InternalMessageInfo

func (m *GetESportGameSkillConfRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetESportGameSkillConfResponse struct {
	GameId               uint32         `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	SkillConf            *GameSkillConf `protobuf:"bytes,2,opt,name=skill_conf,json=skillConf,proto3" json:"skill_conf"`
	IntroConf            *SelfIntroConf `protobuf:"bytes,3,opt,name=intro_conf,json=introConf,proto3" json:"intro_conf"`
	GuaranteeDesc        string         `protobuf:"bytes,4,opt,name=guarantee_desc,json=guaranteeDesc,proto3" json:"guarantee_desc"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetESportGameSkillConfResponse) Reset()         { *m = GetESportGameSkillConfResponse{} }
func (m *GetESportGameSkillConfResponse) String() string { return proto.CompactTextString(m) }
func (*GetESportGameSkillConfResponse) ProtoMessage()    {}
func (*GetESportGameSkillConfResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{35}
}
func (m *GetESportGameSkillConfResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportGameSkillConfResponse.Unmarshal(m, b)
}
func (m *GetESportGameSkillConfResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportGameSkillConfResponse.Marshal(b, m, deterministic)
}
func (dst *GetESportGameSkillConfResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportGameSkillConfResponse.Merge(dst, src)
}
func (m *GetESportGameSkillConfResponse) XXX_Size() int {
	return xxx_messageInfo_GetESportGameSkillConfResponse.Size(m)
}
func (m *GetESportGameSkillConfResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportGameSkillConfResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportGameSkillConfResponse proto.InternalMessageInfo

func (m *GetESportGameSkillConfResponse) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetESportGameSkillConfResponse) GetSkillConf() *GameSkillConf {
	if m != nil {
		return m.SkillConf
	}
	return nil
}

func (m *GetESportGameSkillConfResponse) GetIntroConf() *SelfIntroConf {
	if m != nil {
		return m.IntroConf
	}
	return nil
}

func (m *GetESportGameSkillConfResponse) GetGuaranteeDesc() string {
	if m != nil {
		return m.GuaranteeDesc
	}
	return ""
}

// url: /apply/apply_role
// 提交审核申请
type ApplyESportRoleReq struct {
	ApplyType uint32 `protobuf:"varint,1,opt,name=apply_type,json=applyType,proto3" json:"apply_type"`
	// 签约工会需要填写相关信息
	GuildId      uint32         `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	SignDuration uint32         `protobuf:"varint,3,opt,name=sign_duration,json=signDuration,proto3" json:"sign_duration"`
	UserSkill    *UserSkillInfo `protobuf:"bytes,4,opt,name=user_skill,json=userSkill,proto3" json:"user_skill"`
	// 开关判断需要
	DeviceId             string   `protobuf:"bytes,5,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
	ClientType           uint32   `protobuf:"varint,6,opt,name=client_type,json=clientType,proto3" json:"client_type"`
	AppVersion           uint32   `protobuf:"varint,7,opt,name=app_version,json=appVersion,proto3" json:"app_version"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyESportRoleReq) Reset()         { *m = ApplyESportRoleReq{} }
func (m *ApplyESportRoleReq) String() string { return proto.CompactTextString(m) }
func (*ApplyESportRoleReq) ProtoMessage()    {}
func (*ApplyESportRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{36}
}
func (m *ApplyESportRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyESportRoleReq.Unmarshal(m, b)
}
func (m *ApplyESportRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyESportRoleReq.Marshal(b, m, deterministic)
}
func (dst *ApplyESportRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyESportRoleReq.Merge(dst, src)
}
func (m *ApplyESportRoleReq) XXX_Size() int {
	return xxx_messageInfo_ApplyESportRoleReq.Size(m)
}
func (m *ApplyESportRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyESportRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyESportRoleReq proto.InternalMessageInfo

func (m *ApplyESportRoleReq) GetApplyType() uint32 {
	if m != nil {
		return m.ApplyType
	}
	return 0
}

func (m *ApplyESportRoleReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ApplyESportRoleReq) GetSignDuration() uint32 {
	if m != nil {
		return m.SignDuration
	}
	return 0
}

func (m *ApplyESportRoleReq) GetUserSkill() *UserSkillInfo {
	if m != nil {
		return m.UserSkill
	}
	return nil
}

func (m *ApplyESportRoleReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *ApplyESportRoleReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *ApplyESportRoleReq) GetAppVersion() uint32 {
	if m != nil {
		return m.AppVersion
	}
	return 0
}

type ApplyESportRoleResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyESportRoleResp) Reset()         { *m = ApplyESportRoleResp{} }
func (m *ApplyESportRoleResp) String() string { return proto.CompactTextString(m) }
func (*ApplyESportRoleResp) ProtoMessage()    {}
func (*ApplyESportRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{37}
}
func (m *ApplyESportRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyESportRoleResp.Unmarshal(m, b)
}
func (m *ApplyESportRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyESportRoleResp.Marshal(b, m, deterministic)
}
func (dst *ApplyESportRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyESportRoleResp.Merge(dst, src)
}
func (m *ApplyESportRoleResp) XXX_Size() int {
	return xxx_messageInfo_ApplyESportRoleResp.Size(m)
}
func (m *ApplyESportRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyESportRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyESportRoleResp proto.InternalMessageInfo

// 用户创建退款请求的消息
type CreateRefundRequest struct {
	OrderId              string     `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	Quantity             uint32     `protobuf:"varint,2,opt,name=quantity,proto3" json:"quantity"`
	Reason               string     `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason"`
	Description          string     `protobuf:"bytes,4,opt,name=description,proto3" json:"description"`
	Type                 RefundType `protobuf:"varint,5,opt,name=type,proto3,enum=esport_http.RefundType" json:"type"`
	DeviceId             string     `protobuf:"bytes,6,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CreateRefundRequest) Reset()         { *m = CreateRefundRequest{} }
func (m *CreateRefundRequest) String() string { return proto.CompactTextString(m) }
func (*CreateRefundRequest) ProtoMessage()    {}
func (*CreateRefundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{38}
}
func (m *CreateRefundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateRefundRequest.Unmarshal(m, b)
}
func (m *CreateRefundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateRefundRequest.Marshal(b, m, deterministic)
}
func (dst *CreateRefundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateRefundRequest.Merge(dst, src)
}
func (m *CreateRefundRequest) XXX_Size() int {
	return xxx_messageInfo_CreateRefundRequest.Size(m)
}
func (m *CreateRefundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateRefundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateRefundRequest proto.InternalMessageInfo

func (m *CreateRefundRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *CreateRefundRequest) GetQuantity() uint32 {
	if m != nil {
		return m.Quantity
	}
	return 0
}

func (m *CreateRefundRequest) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *CreateRefundRequest) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *CreateRefundRequest) GetType() RefundType {
	if m != nil {
		return m.Type
	}
	return RefundType_REFUND_TYPE_FULL
}

func (m *CreateRefundRequest) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

// 电竞指导者拒绝退款请求的消息
type RejectRefundRequest struct {
	RefundRequestId      string   `protobuf:"bytes,1,opt,name=refund_request_id,json=refundRequestId,proto3" json:"refund_request_id"`
	Reason               string   `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason"`
	Description          string   `protobuf:"bytes,3,opt,name=description,proto3" json:"description"`
	DeviceId             string   `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RejectRefundRequest) Reset()         { *m = RejectRefundRequest{} }
func (m *RejectRefundRequest) String() string { return proto.CompactTextString(m) }
func (*RejectRefundRequest) ProtoMessage()    {}
func (*RejectRefundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{39}
}
func (m *RejectRefundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RejectRefundRequest.Unmarshal(m, b)
}
func (m *RejectRefundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RejectRefundRequest.Marshal(b, m, deterministic)
}
func (dst *RejectRefundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RejectRefundRequest.Merge(dst, src)
}
func (m *RejectRefundRequest) XXX_Size() int {
	return xxx_messageInfo_RejectRefundRequest.Size(m)
}
func (m *RejectRefundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RejectRefundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RejectRefundRequest proto.InternalMessageInfo

func (m *RejectRefundRequest) GetRefundRequestId() string {
	if m != nil {
		return m.RefundRequestId
	}
	return ""
}

func (m *RejectRefundRequest) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *RejectRefundRequest) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *RejectRefundRequest) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

// 退款操作的通用响应消息
type RefundResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefundResponse) Reset()         { *m = RefundResponse{} }
func (m *RefundResponse) String() string { return proto.CompactTextString(m) }
func (*RefundResponse) ProtoMessage()    {}
func (*RefundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{40}
}
func (m *RefundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefundResponse.Unmarshal(m, b)
}
func (m *RefundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefundResponse.Marshal(b, m, deterministic)
}
func (dst *RefundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefundResponse.Merge(dst, src)
}
func (m *RefundResponse) XXX_Size() int {
	return xxx_messageInfo_RefundResponse.Size(m)
}
func (m *RefundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RefundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RefundResponse proto.InternalMessageInfo

// 获取退款原因的请求消息
type GetRefundReasonsRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRefundReasonsRequest) Reset()         { *m = GetRefundReasonsRequest{} }
func (m *GetRefundReasonsRequest) String() string { return proto.CompactTextString(m) }
func (*GetRefundReasonsRequest) ProtoMessage()    {}
func (*GetRefundReasonsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{41}
}
func (m *GetRefundReasonsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRefundReasonsRequest.Unmarshal(m, b)
}
func (m *GetRefundReasonsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRefundReasonsRequest.Marshal(b, m, deterministic)
}
func (dst *GetRefundReasonsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRefundReasonsRequest.Merge(dst, src)
}
func (m *GetRefundReasonsRequest) XXX_Size() int {
	return xxx_messageInfo_GetRefundReasonsRequest.Size(m)
}
func (m *GetRefundReasonsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRefundReasonsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRefundReasonsRequest proto.InternalMessageInfo

// 获取退款原因的响应消息
type GetRefundReasonsResponse struct {
	Reasons               []string                                         `protobuf:"bytes,1,rep,name=reasons,proto3" json:"reasons"`
	ReasonsWithFastRefund []*GetRefundReasonsResponse_ReasonWithFastRefund `protobuf:"bytes,2,rep,name=reasons_with_fast_refund,json=reasonsWithFastRefund,proto3" json:"reasons_with_fast_refund"`
	XXX_NoUnkeyedLiteral  struct{}                                         `json:"-"`
	XXX_unrecognized      []byte                                           `json:"-"`
	XXX_sizecache         int32                                            `json:"-"`
}

func (m *GetRefundReasonsResponse) Reset()         { *m = GetRefundReasonsResponse{} }
func (m *GetRefundReasonsResponse) String() string { return proto.CompactTextString(m) }
func (*GetRefundReasonsResponse) ProtoMessage()    {}
func (*GetRefundReasonsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{42}
}
func (m *GetRefundReasonsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRefundReasonsResponse.Unmarshal(m, b)
}
func (m *GetRefundReasonsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRefundReasonsResponse.Marshal(b, m, deterministic)
}
func (dst *GetRefundReasonsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRefundReasonsResponse.Merge(dst, src)
}
func (m *GetRefundReasonsResponse) XXX_Size() int {
	return xxx_messageInfo_GetRefundReasonsResponse.Size(m)
}
func (m *GetRefundReasonsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRefundReasonsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRefundReasonsResponse proto.InternalMessageInfo

func (m *GetRefundReasonsResponse) GetReasons() []string {
	if m != nil {
		return m.Reasons
	}
	return nil
}

func (m *GetRefundReasonsResponse) GetReasonsWithFastRefund() []*GetRefundReasonsResponse_ReasonWithFastRefund {
	if m != nil {
		return m.ReasonsWithFastRefund
	}
	return nil
}

// 带急速退款的退款原因
type GetRefundReasonsResponse_ReasonWithFastRefund struct {
	Reason               string   `protobuf:"bytes,1,opt,name=reason,proto3" json:"reason"`
	CouldFastRefund      bool     `protobuf:"varint,2,opt,name=could_fast_refund,json=couldFastRefund,proto3" json:"could_fast_refund"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRefundReasonsResponse_ReasonWithFastRefund) Reset() {
	*m = GetRefundReasonsResponse_ReasonWithFastRefund{}
}
func (m *GetRefundReasonsResponse_ReasonWithFastRefund) String() string {
	return proto.CompactTextString(m)
}
func (*GetRefundReasonsResponse_ReasonWithFastRefund) ProtoMessage() {}
func (*GetRefundReasonsResponse_ReasonWithFastRefund) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{42, 0}
}
func (m *GetRefundReasonsResponse_ReasonWithFastRefund) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRefundReasonsResponse_ReasonWithFastRefund.Unmarshal(m, b)
}
func (m *GetRefundReasonsResponse_ReasonWithFastRefund) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRefundReasonsResponse_ReasonWithFastRefund.Marshal(b, m, deterministic)
}
func (dst *GetRefundReasonsResponse_ReasonWithFastRefund) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRefundReasonsResponse_ReasonWithFastRefund.Merge(dst, src)
}
func (m *GetRefundReasonsResponse_ReasonWithFastRefund) XXX_Size() int {
	return xxx_messageInfo_GetRefundReasonsResponse_ReasonWithFastRefund.Size(m)
}
func (m *GetRefundReasonsResponse_ReasonWithFastRefund) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRefundReasonsResponse_ReasonWithFastRefund.DiscardUnknown(m)
}

var xxx_messageInfo_GetRefundReasonsResponse_ReasonWithFastRefund proto.InternalMessageInfo

func (m *GetRefundReasonsResponse_ReasonWithFastRefund) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *GetRefundReasonsResponse_ReasonWithFastRefund) GetCouldFastRefund() bool {
	if m != nil {
		return m.CouldFastRefund
	}
	return false
}

// 获取拒绝退款的原因请求消息
type GetRejectReasonsRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRejectReasonsRequest) Reset()         { *m = GetRejectReasonsRequest{} }
func (m *GetRejectReasonsRequest) String() string { return proto.CompactTextString(m) }
func (*GetRejectReasonsRequest) ProtoMessage()    {}
func (*GetRejectReasonsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{43}
}
func (m *GetRejectReasonsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRejectReasonsRequest.Unmarshal(m, b)
}
func (m *GetRejectReasonsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRejectReasonsRequest.Marshal(b, m, deterministic)
}
func (dst *GetRejectReasonsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRejectReasonsRequest.Merge(dst, src)
}
func (m *GetRejectReasonsRequest) XXX_Size() int {
	return xxx_messageInfo_GetRejectReasonsRequest.Size(m)
}
func (m *GetRejectReasonsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRejectReasonsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRejectReasonsRequest proto.InternalMessageInfo

// 获取拒绝退款的原因响应消息
type GetRejectReasonsResponse struct {
	Reasons              []string `protobuf:"bytes,1,rep,name=reasons,proto3" json:"reasons"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRejectReasonsResponse) Reset()         { *m = GetRejectReasonsResponse{} }
func (m *GetRejectReasonsResponse) String() string { return proto.CompactTextString(m) }
func (*GetRejectReasonsResponse) ProtoMessage()    {}
func (*GetRejectReasonsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{44}
}
func (m *GetRejectReasonsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRejectReasonsResponse.Unmarshal(m, b)
}
func (m *GetRejectReasonsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRejectReasonsResponse.Marshal(b, m, deterministic)
}
func (dst *GetRejectReasonsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRejectReasonsResponse.Merge(dst, src)
}
func (m *GetRejectReasonsResponse) XXX_Size() int {
	return xxx_messageInfo_GetRejectReasonsResponse.Size(m)
}
func (m *GetRejectReasonsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRejectReasonsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRejectReasonsResponse proto.InternalMessageInfo

func (m *GetRejectReasonsResponse) GetReasons() []string {
	if m != nil {
		return m.Reasons
	}
	return nil
}

// 用户发起退款申诉请求
type InitiateAppealRequest struct {
	RefundId             string   `protobuf:"bytes,1,opt,name=refund_id,json=refundId,proto3" json:"refund_id"`
	Description          string   `protobuf:"bytes,2,opt,name=description,proto3" json:"description"`
	ProofImages          []string `protobuf:"bytes,3,rep,name=proof_images,json=proofImages,proto3" json:"proof_images"`
	DeviceId             string   `protobuf:"bytes,6,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InitiateAppealRequest) Reset()         { *m = InitiateAppealRequest{} }
func (m *InitiateAppealRequest) String() string { return proto.CompactTextString(m) }
func (*InitiateAppealRequest) ProtoMessage()    {}
func (*InitiateAppealRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{45}
}
func (m *InitiateAppealRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InitiateAppealRequest.Unmarshal(m, b)
}
func (m *InitiateAppealRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InitiateAppealRequest.Marshal(b, m, deterministic)
}
func (dst *InitiateAppealRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InitiateAppealRequest.Merge(dst, src)
}
func (m *InitiateAppealRequest) XXX_Size() int {
	return xxx_messageInfo_InitiateAppealRequest.Size(m)
}
func (m *InitiateAppealRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InitiateAppealRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InitiateAppealRequest proto.InternalMessageInfo

func (m *InitiateAppealRequest) GetRefundId() string {
	if m != nil {
		return m.RefundId
	}
	return ""
}

func (m *InitiateAppealRequest) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *InitiateAppealRequest) GetProofImages() []string {
	if m != nil {
		return m.ProofImages
	}
	return nil
}

func (m *InitiateAppealRequest) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

// 用户发起退款申诉响应
type InitiateAppealResponse struct {
	AppealId             string   `protobuf:"bytes,1,opt,name=appeal_id,json=appealId,proto3" json:"appeal_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InitiateAppealResponse) Reset()         { *m = InitiateAppealResponse{} }
func (m *InitiateAppealResponse) String() string { return proto.CompactTextString(m) }
func (*InitiateAppealResponse) ProtoMessage()    {}
func (*InitiateAppealResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{46}
}
func (m *InitiateAppealResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InitiateAppealResponse.Unmarshal(m, b)
}
func (m *InitiateAppealResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InitiateAppealResponse.Marshal(b, m, deterministic)
}
func (dst *InitiateAppealResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InitiateAppealResponse.Merge(dst, src)
}
func (m *InitiateAppealResponse) XXX_Size() int {
	return xxx_messageInfo_InitiateAppealResponse.Size(m)
}
func (m *InitiateAppealResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_InitiateAppealResponse.DiscardUnknown(m)
}

var xxx_messageInfo_InitiateAppealResponse proto.InternalMessageInfo

func (m *InitiateAppealResponse) GetAppealId() string {
	if m != nil {
		return m.AppealId
	}
	return ""
}

// 电竞指导提交自己的申诉信息请求
type SubmitGuideAppealInfoRequest struct {
	AppealId               string   `protobuf:"bytes,1,opt,name=appeal_id,json=appealId,proto3" json:"appeal_id"`
	CoachAppealDescription string   `protobuf:"bytes,2,opt,name=coach_appeal_description,json=coachAppealDescription,proto3" json:"coach_appeal_description"`
	CoachAppealImages      []string `protobuf:"bytes,3,rep,name=coach_appeal_images,json=coachAppealImages,proto3" json:"coach_appeal_images"`
	DeviceId               string   `protobuf:"bytes,6,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *SubmitGuideAppealInfoRequest) Reset()         { *m = SubmitGuideAppealInfoRequest{} }
func (m *SubmitGuideAppealInfoRequest) String() string { return proto.CompactTextString(m) }
func (*SubmitGuideAppealInfoRequest) ProtoMessage()    {}
func (*SubmitGuideAppealInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{47}
}
func (m *SubmitGuideAppealInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitGuideAppealInfoRequest.Unmarshal(m, b)
}
func (m *SubmitGuideAppealInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitGuideAppealInfoRequest.Marshal(b, m, deterministic)
}
func (dst *SubmitGuideAppealInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitGuideAppealInfoRequest.Merge(dst, src)
}
func (m *SubmitGuideAppealInfoRequest) XXX_Size() int {
	return xxx_messageInfo_SubmitGuideAppealInfoRequest.Size(m)
}
func (m *SubmitGuideAppealInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitGuideAppealInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitGuideAppealInfoRequest proto.InternalMessageInfo

func (m *SubmitGuideAppealInfoRequest) GetAppealId() string {
	if m != nil {
		return m.AppealId
	}
	return ""
}

func (m *SubmitGuideAppealInfoRequest) GetCoachAppealDescription() string {
	if m != nil {
		return m.CoachAppealDescription
	}
	return ""
}

func (m *SubmitGuideAppealInfoRequest) GetCoachAppealImages() []string {
	if m != nil {
		return m.CoachAppealImages
	}
	return nil
}

func (m *SubmitGuideAppealInfoRequest) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

// 电竞指导提交自己的申诉信息响应
type SubmitGuideAppealInfoResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitGuideAppealInfoResponse) Reset()         { *m = SubmitGuideAppealInfoResponse{} }
func (m *SubmitGuideAppealInfoResponse) String() string { return proto.CompactTextString(m) }
func (*SubmitGuideAppealInfoResponse) ProtoMessage()    {}
func (*SubmitGuideAppealInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{48}
}
func (m *SubmitGuideAppealInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitGuideAppealInfoResponse.Unmarshal(m, b)
}
func (m *SubmitGuideAppealInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitGuideAppealInfoResponse.Marshal(b, m, deterministic)
}
func (dst *SubmitGuideAppealInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitGuideAppealInfoResponse.Merge(dst, src)
}
func (m *SubmitGuideAppealInfoResponse) XXX_Size() int {
	return xxx_messageInfo_SubmitGuideAppealInfoResponse.Size(m)
}
func (m *SubmitGuideAppealInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitGuideAppealInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitGuideAppealInfoResponse proto.InternalMessageInfo

// =============================== 接单时间, 价格修改 ===============================
// 获取接单时间
type GetReceiveTimeFrameRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReceiveTimeFrameRequest) Reset()         { *m = GetReceiveTimeFrameRequest{} }
func (m *GetReceiveTimeFrameRequest) String() string { return proto.CompactTextString(m) }
func (*GetReceiveTimeFrameRequest) ProtoMessage()    {}
func (*GetReceiveTimeFrameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{49}
}
func (m *GetReceiveTimeFrameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReceiveTimeFrameRequest.Unmarshal(m, b)
}
func (m *GetReceiveTimeFrameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReceiveTimeFrameRequest.Marshal(b, m, deterministic)
}
func (dst *GetReceiveTimeFrameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReceiveTimeFrameRequest.Merge(dst, src)
}
func (m *GetReceiveTimeFrameRequest) XXX_Size() int {
	return xxx_messageInfo_GetReceiveTimeFrameRequest.Size(m)
}
func (m *GetReceiveTimeFrameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReceiveTimeFrameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetReceiveTimeFrameRequest proto.InternalMessageInfo

type GetReceiveTimeFrameResponse struct {
	StartTime            uint32   `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	DayOfWeek            []bool   `protobuf:"varint,4,rep,packed,name=day_of_week,json=dayOfWeek,proto3" json:"day_of_week"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReceiveTimeFrameResponse) Reset()         { *m = GetReceiveTimeFrameResponse{} }
func (m *GetReceiveTimeFrameResponse) String() string { return proto.CompactTextString(m) }
func (*GetReceiveTimeFrameResponse) ProtoMessage()    {}
func (*GetReceiveTimeFrameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{50}
}
func (m *GetReceiveTimeFrameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReceiveTimeFrameResponse.Unmarshal(m, b)
}
func (m *GetReceiveTimeFrameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReceiveTimeFrameResponse.Marshal(b, m, deterministic)
}
func (dst *GetReceiveTimeFrameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReceiveTimeFrameResponse.Merge(dst, src)
}
func (m *GetReceiveTimeFrameResponse) XXX_Size() int {
	return xxx_messageInfo_GetReceiveTimeFrameResponse.Size(m)
}
func (m *GetReceiveTimeFrameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReceiveTimeFrameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetReceiveTimeFrameResponse proto.InternalMessageInfo

func (m *GetReceiveTimeFrameResponse) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetReceiveTimeFrameResponse) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetReceiveTimeFrameResponse) GetDayOfWeek() []bool {
	if m != nil {
		return m.DayOfWeek
	}
	return nil
}

// 设置接单时间
type SetReceiveTimeFrameRequest struct {
	StartTime            uint32   `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	DayOfWeek            []bool   `protobuf:"varint,4,rep,packed,name=day_of_week,json=dayOfWeek,proto3" json:"day_of_week"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetReceiveTimeFrameRequest) Reset()         { *m = SetReceiveTimeFrameRequest{} }
func (m *SetReceiveTimeFrameRequest) String() string { return proto.CompactTextString(m) }
func (*SetReceiveTimeFrameRequest) ProtoMessage()    {}
func (*SetReceiveTimeFrameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{51}
}
func (m *SetReceiveTimeFrameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetReceiveTimeFrameRequest.Unmarshal(m, b)
}
func (m *SetReceiveTimeFrameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetReceiveTimeFrameRequest.Marshal(b, m, deterministic)
}
func (dst *SetReceiveTimeFrameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetReceiveTimeFrameRequest.Merge(dst, src)
}
func (m *SetReceiveTimeFrameRequest) XXX_Size() int {
	return xxx_messageInfo_SetReceiveTimeFrameRequest.Size(m)
}
func (m *SetReceiveTimeFrameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetReceiveTimeFrameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetReceiveTimeFrameRequest proto.InternalMessageInfo

func (m *SetReceiveTimeFrameRequest) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *SetReceiveTimeFrameRequest) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *SetReceiveTimeFrameRequest) GetDayOfWeek() []bool {
	if m != nil {
		return m.DayOfWeek
	}
	return nil
}

type SetReceiveTimeFrameResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetReceiveTimeFrameResponse) Reset()         { *m = SetReceiveTimeFrameResponse{} }
func (m *SetReceiveTimeFrameResponse) String() string { return proto.CompactTextString(m) }
func (*SetReceiveTimeFrameResponse) ProtoMessage()    {}
func (*SetReceiveTimeFrameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{52}
}
func (m *SetReceiveTimeFrameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetReceiveTimeFrameResponse.Unmarshal(m, b)
}
func (m *SetReceiveTimeFrameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetReceiveTimeFrameResponse.Marshal(b, m, deterministic)
}
func (dst *SetReceiveTimeFrameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetReceiveTimeFrameResponse.Merge(dst, src)
}
func (m *SetReceiveTimeFrameResponse) XXX_Size() int {
	return xxx_messageInfo_SetReceiveTimeFrameResponse.Size(m)
}
func (m *SetReceiveTimeFrameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetReceiveTimeFrameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetReceiveTimeFrameResponse proto.InternalMessageInfo

// 开启/关闭接单
type SetSkillReceiveSwitchRequest struct {
	SkillId              uint32   `protobuf:"varint,1,opt,name=skill_id,json=skillId,proto3" json:"skill_id"`
	Switch               bool     `protobuf:"varint,2,opt,name=switch,proto3" json:"switch"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSkillReceiveSwitchRequest) Reset()         { *m = SetSkillReceiveSwitchRequest{} }
func (m *SetSkillReceiveSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*SetSkillReceiveSwitchRequest) ProtoMessage()    {}
func (*SetSkillReceiveSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{53}
}
func (m *SetSkillReceiveSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSkillReceiveSwitchRequest.Unmarshal(m, b)
}
func (m *SetSkillReceiveSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSkillReceiveSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *SetSkillReceiveSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSkillReceiveSwitchRequest.Merge(dst, src)
}
func (m *SetSkillReceiveSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_SetSkillReceiveSwitchRequest.Size(m)
}
func (m *SetSkillReceiveSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSkillReceiveSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetSkillReceiveSwitchRequest proto.InternalMessageInfo

func (m *SetSkillReceiveSwitchRequest) GetSkillId() uint32 {
	if m != nil {
		return m.SkillId
	}
	return 0
}

func (m *SetSkillReceiveSwitchRequest) GetSwitch() bool {
	if m != nil {
		return m.Switch
	}
	return false
}

type SetSkillReceiveSwitchResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSkillReceiveSwitchResponse) Reset()         { *m = SetSkillReceiveSwitchResponse{} }
func (m *SetSkillReceiveSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*SetSkillReceiveSwitchResponse) ProtoMessage()    {}
func (*SetSkillReceiveSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{54}
}
func (m *SetSkillReceiveSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSkillReceiveSwitchResponse.Unmarshal(m, b)
}
func (m *SetSkillReceiveSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSkillReceiveSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *SetSkillReceiveSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSkillReceiveSwitchResponse.Merge(dst, src)
}
func (m *SetSkillReceiveSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_SetSkillReceiveSwitchResponse.Size(m)
}
func (m *SetSkillReceiveSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSkillReceiveSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetSkillReceiveSwitchResponse proto.InternalMessageInfo

// checkIfCouldOpenSwitch
type CheckIfCouldOpenSwitchRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIfCouldOpenSwitchRequest) Reset()         { *m = CheckIfCouldOpenSwitchRequest{} }
func (m *CheckIfCouldOpenSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*CheckIfCouldOpenSwitchRequest) ProtoMessage()    {}
func (*CheckIfCouldOpenSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{55}
}
func (m *CheckIfCouldOpenSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIfCouldOpenSwitchRequest.Unmarshal(m, b)
}
func (m *CheckIfCouldOpenSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIfCouldOpenSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *CheckIfCouldOpenSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIfCouldOpenSwitchRequest.Merge(dst, src)
}
func (m *CheckIfCouldOpenSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_CheckIfCouldOpenSwitchRequest.Size(m)
}
func (m *CheckIfCouldOpenSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIfCouldOpenSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIfCouldOpenSwitchRequest proto.InternalMessageInfo

type CheckIfCouldOpenSwitchResponse struct {
	CouldOpen            bool     `protobuf:"varint,1,opt,name=could_open,json=couldOpen,proto3" json:"could_open"`
	Reason               string   `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIfCouldOpenSwitchResponse) Reset()         { *m = CheckIfCouldOpenSwitchResponse{} }
func (m *CheckIfCouldOpenSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*CheckIfCouldOpenSwitchResponse) ProtoMessage()    {}
func (*CheckIfCouldOpenSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{56}
}
func (m *CheckIfCouldOpenSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIfCouldOpenSwitchResponse.Unmarshal(m, b)
}
func (m *CheckIfCouldOpenSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIfCouldOpenSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *CheckIfCouldOpenSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIfCouldOpenSwitchResponse.Merge(dst, src)
}
func (m *CheckIfCouldOpenSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_CheckIfCouldOpenSwitchResponse.Size(m)
}
func (m *CheckIfCouldOpenSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIfCouldOpenSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIfCouldOpenSwitchResponse proto.InternalMessageInfo

func (m *CheckIfCouldOpenSwitchResponse) GetCouldOpen() bool {
	if m != nil {
		return m.CouldOpen
	}
	return false
}

func (m *CheckIfCouldOpenSwitchResponse) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

// 设置技能价格
type SetSkillPriceRequest struct {
	SkillId              uint32   `protobuf:"varint,1,opt,name=skill_id,json=skillId,proto3" json:"skill_id"`
	PriceVal             uint32   `protobuf:"varint,2,opt,name=price_val,json=priceVal,proto3" json:"price_val"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSkillPriceRequest) Reset()         { *m = SetSkillPriceRequest{} }
func (m *SetSkillPriceRequest) String() string { return proto.CompactTextString(m) }
func (*SetSkillPriceRequest) ProtoMessage()    {}
func (*SetSkillPriceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{57}
}
func (m *SetSkillPriceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSkillPriceRequest.Unmarshal(m, b)
}
func (m *SetSkillPriceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSkillPriceRequest.Marshal(b, m, deterministic)
}
func (dst *SetSkillPriceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSkillPriceRequest.Merge(dst, src)
}
func (m *SetSkillPriceRequest) XXX_Size() int {
	return xxx_messageInfo_SetSkillPriceRequest.Size(m)
}
func (m *SetSkillPriceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSkillPriceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetSkillPriceRequest proto.InternalMessageInfo

func (m *SetSkillPriceRequest) GetSkillId() uint32 {
	if m != nil {
		return m.SkillId
	}
	return 0
}

func (m *SetSkillPriceRequest) GetPriceVal() uint32 {
	if m != nil {
		return m.PriceVal
	}
	return 0
}

type SetSkillPriceResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSkillPriceResponse) Reset()         { *m = SetSkillPriceResponse{} }
func (m *SetSkillPriceResponse) String() string { return proto.CompactTextString(m) }
func (*SetSkillPriceResponse) ProtoMessage()    {}
func (*SetSkillPriceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{58}
}
func (m *SetSkillPriceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSkillPriceResponse.Unmarshal(m, b)
}
func (m *SetSkillPriceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSkillPriceResponse.Marshal(b, m, deterministic)
}
func (dst *SetSkillPriceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSkillPriceResponse.Merge(dst, src)
}
func (m *SetSkillPriceResponse) XXX_Size() int {
	return xxx_messageInfo_SetSkillPriceResponse.Size(m)
}
func (m *SetSkillPriceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSkillPriceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetSkillPriceResponse proto.InternalMessageInfo

type GodPageSkill struct {
	Id                   uint32                      `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name                 string                      `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Icon                 string                      `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon"`
	Price                *PriceInfo                  `protobuf:"bytes,4,opt,name=price,proto3" json:"price"`
	ReceiveSwitch        bool                        `protobuf:"varint,5,opt,name=receive_switch,json=receiveSwitch,proto3" json:"receive_switch"`
	GameId               uint32                      `protobuf:"varint,6,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	GuaranteeInfo        *GodPageSkill_GuaranteeInfo `protobuf:"bytes,7,opt,name=guarantee_info,json=guaranteeInfo,proto3" json:"guarantee_info"`
	FreezeType           FreezeType                  `protobuf:"varint,8,opt,name=freeze_type,json=freezeType,proto3,enum=esport_http.FreezeType" json:"freeze_type"`
	FreezeStopTs         int64                       `protobuf:"varint,9,opt,name=freeze_stop_ts,json=freezeStopTs,proto3" json:"freeze_stop_ts"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GodPageSkill) Reset()         { *m = GodPageSkill{} }
func (m *GodPageSkill) String() string { return proto.CompactTextString(m) }
func (*GodPageSkill) ProtoMessage()    {}
func (*GodPageSkill) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{59}
}
func (m *GodPageSkill) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GodPageSkill.Unmarshal(m, b)
}
func (m *GodPageSkill) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GodPageSkill.Marshal(b, m, deterministic)
}
func (dst *GodPageSkill) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GodPageSkill.Merge(dst, src)
}
func (m *GodPageSkill) XXX_Size() int {
	return xxx_messageInfo_GodPageSkill.Size(m)
}
func (m *GodPageSkill) XXX_DiscardUnknown() {
	xxx_messageInfo_GodPageSkill.DiscardUnknown(m)
}

var xxx_messageInfo_GodPageSkill proto.InternalMessageInfo

func (m *GodPageSkill) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GodPageSkill) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GodPageSkill) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *GodPageSkill) GetPrice() *PriceInfo {
	if m != nil {
		return m.Price
	}
	return nil
}

func (m *GodPageSkill) GetReceiveSwitch() bool {
	if m != nil {
		return m.ReceiveSwitch
	}
	return false
}

func (m *GodPageSkill) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GodPageSkill) GetGuaranteeInfo() *GodPageSkill_GuaranteeInfo {
	if m != nil {
		return m.GuaranteeInfo
	}
	return nil
}

func (m *GodPageSkill) GetFreezeType() FreezeType {
	if m != nil {
		return m.FreezeType
	}
	return FreezeType_FREEZE_TYPE_UNFREEZE
}

func (m *GodPageSkill) GetFreezeStopTs() int64 {
	if m != nil {
		return m.FreezeStopTs
	}
	return 0
}

// 包赢信息
type GodPageSkill_GuaranteeInfo struct {
	GuaranteeRank             []string   `protobuf:"bytes,1,rep,name=guarantee_rank,json=guaranteeRank,proto3" json:"guarantee_rank"`
	CurrentGuaranteeIndexList []uint32   `protobuf:"varint,2,rep,packed,name=current_guarantee_index_list,json=currentGuaranteeIndexList,proto3" json:"current_guarantee_index_list"`
	SectionId                 uint32     `protobuf:"varint,3,opt,name=section_id,json=sectionId,proto3" json:"section_id"`
	SelectType                SelectType `protobuf:"varint,4,opt,name=select_type,json=selectType,proto3,enum=esport_http.SelectType" json:"select_type"`
	XXX_NoUnkeyedLiteral      struct{}   `json:"-"`
	XXX_unrecognized          []byte     `json:"-"`
	XXX_sizecache             int32      `json:"-"`
}

func (m *GodPageSkill_GuaranteeInfo) Reset()         { *m = GodPageSkill_GuaranteeInfo{} }
func (m *GodPageSkill_GuaranteeInfo) String() string { return proto.CompactTextString(m) }
func (*GodPageSkill_GuaranteeInfo) ProtoMessage()    {}
func (*GodPageSkill_GuaranteeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{59, 0}
}
func (m *GodPageSkill_GuaranteeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GodPageSkill_GuaranteeInfo.Unmarshal(m, b)
}
func (m *GodPageSkill_GuaranteeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GodPageSkill_GuaranteeInfo.Marshal(b, m, deterministic)
}
func (dst *GodPageSkill_GuaranteeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GodPageSkill_GuaranteeInfo.Merge(dst, src)
}
func (m *GodPageSkill_GuaranteeInfo) XXX_Size() int {
	return xxx_messageInfo_GodPageSkill_GuaranteeInfo.Size(m)
}
func (m *GodPageSkill_GuaranteeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GodPageSkill_GuaranteeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GodPageSkill_GuaranteeInfo proto.InternalMessageInfo

func (m *GodPageSkill_GuaranteeInfo) GetGuaranteeRank() []string {
	if m != nil {
		return m.GuaranteeRank
	}
	return nil
}

func (m *GodPageSkill_GuaranteeInfo) GetCurrentGuaranteeIndexList() []uint32 {
	if m != nil {
		return m.CurrentGuaranteeIndexList
	}
	return nil
}

func (m *GodPageSkill_GuaranteeInfo) GetSectionId() uint32 {
	if m != nil {
		return m.SectionId
	}
	return 0
}

func (m *GodPageSkill_GuaranteeInfo) GetSelectType() SelectType {
	if m != nil {
		return m.SelectType
	}
	return SelectType_SELECT_TYPE_UNSPECIFIED
}

type PriceInfo struct {
	Price                uint32   `protobuf:"varint,1,opt,name=price,proto3" json:"price"`
	PriceUnit            string   `protobuf:"bytes,2,opt,name=price_unit,json=priceUnit,proto3" json:"price_unit"`
	MinPrice             uint32   `protobuf:"varint,3,opt,name=min_price,json=minPrice,proto3" json:"min_price"`
	BasePrice            uint32   `protobuf:"varint,4,opt,name=base_price,json=basePrice,proto3" json:"base_price"`
	MaxPrice             uint32   `protobuf:"varint,5,opt,name=max_price,json=maxPrice,proto3" json:"max_price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PriceInfo) Reset()         { *m = PriceInfo{} }
func (m *PriceInfo) String() string { return proto.CompactTextString(m) }
func (*PriceInfo) ProtoMessage()    {}
func (*PriceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{60}
}
func (m *PriceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PriceInfo.Unmarshal(m, b)
}
func (m *PriceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PriceInfo.Marshal(b, m, deterministic)
}
func (dst *PriceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PriceInfo.Merge(dst, src)
}
func (m *PriceInfo) XXX_Size() int {
	return xxx_messageInfo_PriceInfo.Size(m)
}
func (m *PriceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PriceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PriceInfo proto.InternalMessageInfo

func (m *PriceInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *PriceInfo) GetPriceUnit() string {
	if m != nil {
		return m.PriceUnit
	}
	return ""
}

func (m *PriceInfo) GetMinPrice() uint32 {
	if m != nil {
		return m.MinPrice
	}
	return 0
}

func (m *PriceInfo) GetBasePrice() uint32 {
	if m != nil {
		return m.BasePrice
	}
	return 0
}

func (m *PriceInfo) GetMaxPrice() uint32 {
	if m != nil {
		return m.MaxPrice
	}
	return 0
}

// 电竞专区大神详情
type GetCoachDetailRequest struct {
	CoachUid             uint32   `protobuf:"varint,1,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachDetailRequest) Reset()         { *m = GetCoachDetailRequest{} }
func (m *GetCoachDetailRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachDetailRequest) ProtoMessage()    {}
func (*GetCoachDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{61}
}
func (m *GetCoachDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachDetailRequest.Unmarshal(m, b)
}
func (m *GetCoachDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachDetailRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachDetailRequest.Merge(dst, src)
}
func (m *GetCoachDetailRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachDetailRequest.Size(m)
}
func (m *GetCoachDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachDetailRequest proto.InternalMessageInfo

func (m *GetCoachDetailRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *GetCoachDetailRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetCoachDetailResponse struct {
	CoachDetail          *CoachDetail `protobuf:"bytes,1,opt,name=coach_detail,json=coachDetail,proto3" json:"coach_detail"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCoachDetailResponse) Reset()         { *m = GetCoachDetailResponse{} }
func (m *GetCoachDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachDetailResponse) ProtoMessage()    {}
func (*GetCoachDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{62}
}
func (m *GetCoachDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachDetailResponse.Unmarshal(m, b)
}
func (m *GetCoachDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachDetailResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachDetailResponse.Merge(dst, src)
}
func (m *GetCoachDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachDetailResponse.Size(m)
}
func (m *GetCoachDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachDetailResponse proto.InternalMessageInfo

func (m *GetCoachDetailResponse) GetCoachDetail() *CoachDetail {
	if m != nil {
		return m.CoachDetail
	}
	return nil
}

type CoachDetail struct {
	UserInfo              *UserInfo         `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info"`
	TextDesc              string            `protobuf:"bytes,2,opt,name=text_desc,json=textDesc,proto3" json:"text_desc"`
	VoiceDesc             string            `protobuf:"bytes,3,opt,name=voice_desc,json=voiceDesc,proto3" json:"voice_desc"`
	GameName              string            `protobuf:"bytes,4,opt,name=game_name,json=gameName,proto3" json:"game_name"`
	Tag                   string            `protobuf:"bytes,5,opt,name=tag,proto3" json:"tag"`
	Score                 float32           `protobuf:"fixed32,6,opt,name=score,proto3" json:"score"`
	ServiceCnt            uint32            `protobuf:"varint,7,opt,name=service_cnt,json=serviceCnt,proto3" json:"service_cnt"`
	GamePropertyList      []*GameProperty   `protobuf:"bytes,8,rep,name=game_property_list,json=gamePropertyList,proto3" json:"game_property_list"`
	GameImg               []string          `protobuf:"bytes,9,rep,name=game_img,json=gameImg,proto3" json:"game_img"`
	CoachLabel            *CoachLabel       `protobuf:"bytes,10,opt,name=coach_label,json=coachLabel,proto3" json:"coach_label"`
	OnlineType            uint32            `protobuf:"varint,11,opt,name=online_type,json=onlineType,proto3" json:"online_type"`
	VideoDuration         uint32            `protobuf:"varint,12,opt,name=video_duration,json=videoDuration,proto3" json:"video_duration"`
	Price                 uint32            `protobuf:"varint,13,opt,name=price,proto3" json:"price"`
	PriceUnit             string            `protobuf:"bytes,14,opt,name=price_unit,json=priceUnit,proto3" json:"price_unit"`
	GameBg                string            `protobuf:"bytes,15,opt,name=game_bg,json=gameBg,proto3" json:"game_bg"`
	GameBgColor           string            `protobuf:"bytes,16,opt,name=game_bg_color,json=gameBgColor,proto3" json:"game_bg_color"`
	IsFollow              bool              `protobuf:"varint,17,opt,name=is_follow,json=isFollow,proto3" json:"is_follow"`
	IsFamousPlayer        bool              `protobuf:"varint,18,opt,name=is_famous_player,json=isFamousPlayer,proto3" json:"is_famous_player"`
	CoachLabelList        []string          `protobuf:"bytes,19,rep,name=coach_label_list,json=coachLabelList,proto3" json:"coach_label_list"`
	SkillLabelList        []string          `protobuf:"bytes,20,rep,name=skill_label_list,json=skillLabelList,proto3" json:"skill_label_list"`
	GranteeWinText        string            `protobuf:"bytes,21,opt,name=grantee_win_text,json=granteeWinText,proto3" json:"grantee_win_text"`
	SkillLabelUrlList     []string          `protobuf:"bytes,22,rep,name=skill_label_url_list,json=skillLabelUrlList,proto3" json:"skill_label_url_list"`
	HasFirstRoundDiscount bool              `protobuf:"varint,23,opt,name=has_first_round_discount,json=hasFirstRoundDiscount,proto3" json:"has_first_round_discount"`
	FirstRoundPrice       uint32            `protobuf:"varint,24,opt,name=first_round_price,json=firstRoundPrice,proto3" json:"first_round_price"`
	IsCustomerHosting     bool              `protobuf:"varint,25,opt,name=is_customer_hosting,json=isCustomerHosting,proto3" json:"is_customer_hosting"`
	HasDiscount           bool              `protobuf:"varint,26,opt,name=has_discount,json=hasDiscount,proto3" json:"has_discount"`
	DiscountPrice         uint32            `protobuf:"varint,27,opt,name=discount_price,json=discountPrice,proto3" json:"discount_price"`
	DiscountType          uint32            `protobuf:"varint,28,opt,name=discount_type,json=discountType,proto3" json:"discount_type"`
	DiscountDesc          string            `protobuf:"bytes,29,opt,name=discount_desc,json=discountDesc,proto3" json:"discount_desc"`
	OrderButtonHintText   string            `protobuf:"bytes,30,opt,name=order_button_hint_text,json=orderButtonHintText,proto3" json:"order_button_hint_text"`
	CoachChannelInfo      *CoachChannelInfo `protobuf:"bytes,31,opt,name=coach_channel_info,json=coachChannelInfo,proto3" json:"coach_channel_info"`
	XXX_NoUnkeyedLiteral  struct{}          `json:"-"`
	XXX_unrecognized      []byte            `json:"-"`
	XXX_sizecache         int32             `json:"-"`
}

func (m *CoachDetail) Reset()         { *m = CoachDetail{} }
func (m *CoachDetail) String() string { return proto.CompactTextString(m) }
func (*CoachDetail) ProtoMessage()    {}
func (*CoachDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{63}
}
func (m *CoachDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoachDetail.Unmarshal(m, b)
}
func (m *CoachDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoachDetail.Marshal(b, m, deterministic)
}
func (dst *CoachDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoachDetail.Merge(dst, src)
}
func (m *CoachDetail) XXX_Size() int {
	return xxx_messageInfo_CoachDetail.Size(m)
}
func (m *CoachDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_CoachDetail.DiscardUnknown(m)
}

var xxx_messageInfo_CoachDetail proto.InternalMessageInfo

func (m *CoachDetail) GetUserInfo() *UserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *CoachDetail) GetTextDesc() string {
	if m != nil {
		return m.TextDesc
	}
	return ""
}

func (m *CoachDetail) GetVoiceDesc() string {
	if m != nil {
		return m.VoiceDesc
	}
	return ""
}

func (m *CoachDetail) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *CoachDetail) GetTag() string {
	if m != nil {
		return m.Tag
	}
	return ""
}

func (m *CoachDetail) GetScore() float32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *CoachDetail) GetServiceCnt() uint32 {
	if m != nil {
		return m.ServiceCnt
	}
	return 0
}

func (m *CoachDetail) GetGamePropertyList() []*GameProperty {
	if m != nil {
		return m.GamePropertyList
	}
	return nil
}

func (m *CoachDetail) GetGameImg() []string {
	if m != nil {
		return m.GameImg
	}
	return nil
}

func (m *CoachDetail) GetCoachLabel() *CoachLabel {
	if m != nil {
		return m.CoachLabel
	}
	return nil
}

func (m *CoachDetail) GetOnlineType() uint32 {
	if m != nil {
		return m.OnlineType
	}
	return 0
}

func (m *CoachDetail) GetVideoDuration() uint32 {
	if m != nil {
		return m.VideoDuration
	}
	return 0
}

func (m *CoachDetail) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *CoachDetail) GetPriceUnit() string {
	if m != nil {
		return m.PriceUnit
	}
	return ""
}

func (m *CoachDetail) GetGameBg() string {
	if m != nil {
		return m.GameBg
	}
	return ""
}

func (m *CoachDetail) GetGameBgColor() string {
	if m != nil {
		return m.GameBgColor
	}
	return ""
}

func (m *CoachDetail) GetIsFollow() bool {
	if m != nil {
		return m.IsFollow
	}
	return false
}

func (m *CoachDetail) GetIsFamousPlayer() bool {
	if m != nil {
		return m.IsFamousPlayer
	}
	return false
}

func (m *CoachDetail) GetCoachLabelList() []string {
	if m != nil {
		return m.CoachLabelList
	}
	return nil
}

func (m *CoachDetail) GetSkillLabelList() []string {
	if m != nil {
		return m.SkillLabelList
	}
	return nil
}

func (m *CoachDetail) GetGranteeWinText() string {
	if m != nil {
		return m.GranteeWinText
	}
	return ""
}

func (m *CoachDetail) GetSkillLabelUrlList() []string {
	if m != nil {
		return m.SkillLabelUrlList
	}
	return nil
}

func (m *CoachDetail) GetHasFirstRoundDiscount() bool {
	if m != nil {
		return m.HasFirstRoundDiscount
	}
	return false
}

func (m *CoachDetail) GetFirstRoundPrice() uint32 {
	if m != nil {
		return m.FirstRoundPrice
	}
	return 0
}

func (m *CoachDetail) GetIsCustomerHosting() bool {
	if m != nil {
		return m.IsCustomerHosting
	}
	return false
}

func (m *CoachDetail) GetHasDiscount() bool {
	if m != nil {
		return m.HasDiscount
	}
	return false
}

func (m *CoachDetail) GetDiscountPrice() uint32 {
	if m != nil {
		return m.DiscountPrice
	}
	return 0
}

func (m *CoachDetail) GetDiscountType() uint32 {
	if m != nil {
		return m.DiscountType
	}
	return 0
}

func (m *CoachDetail) GetDiscountDesc() string {
	if m != nil {
		return m.DiscountDesc
	}
	return ""
}

func (m *CoachDetail) GetOrderButtonHintText() string {
	if m != nil {
		return m.OrderButtonHintText
	}
	return ""
}

func (m *CoachDetail) GetCoachChannelInfo() *CoachChannelInfo {
	if m != nil {
		return m.CoachChannelInfo
	}
	return nil
}

type CoachChannelInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	ChannelType          uint32   `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3" json:"channel_type"`
	HasPwd               bool     `protobuf:"varint,3,opt,name=has_pwd,json=hasPwd,proto3" json:"has_pwd"`
	CreatorUid           uint32   `protobuf:"varint,4,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CoachChannelInfo) Reset()         { *m = CoachChannelInfo{} }
func (m *CoachChannelInfo) String() string { return proto.CompactTextString(m) }
func (*CoachChannelInfo) ProtoMessage()    {}
func (*CoachChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{64}
}
func (m *CoachChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoachChannelInfo.Unmarshal(m, b)
}
func (m *CoachChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoachChannelInfo.Marshal(b, m, deterministic)
}
func (dst *CoachChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoachChannelInfo.Merge(dst, src)
}
func (m *CoachChannelInfo) XXX_Size() int {
	return xxx_messageInfo_CoachChannelInfo.Size(m)
}
func (m *CoachChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CoachChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CoachChannelInfo proto.InternalMessageInfo

func (m *CoachChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CoachChannelInfo) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *CoachChannelInfo) GetHasPwd() bool {
	if m != nil {
		return m.HasPwd
	}
	return false
}

func (m *CoachChannelInfo) GetCreatorUid() uint32 {
	if m != nil {
		return m.CreatorUid
	}
	return 0
}

// 游戏属性
type GameProperty struct {
	Id                   uint32             `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name                 string             `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	ValList              []*GamePropertyVal `protobuf:"bytes,3,rep,name=val_list,json=valList,proto3" json:"val_list"`
	PropertyType         uint32             `protobuf:"varint,4,opt,name=property_type,json=propertyType,proto3" json:"property_type"`
	SelectType           uint32             `protobuf:"varint,5,opt,name=select_type,json=selectType,proto3" json:"select_type"`
	Expose               bool               `protobuf:"varint,6,opt,name=expose,proto3" json:"expose"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GameProperty) Reset()         { *m = GameProperty{} }
func (m *GameProperty) String() string { return proto.CompactTextString(m) }
func (*GameProperty) ProtoMessage()    {}
func (*GameProperty) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{65}
}
func (m *GameProperty) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameProperty.Unmarshal(m, b)
}
func (m *GameProperty) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameProperty.Marshal(b, m, deterministic)
}
func (dst *GameProperty) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameProperty.Merge(dst, src)
}
func (m *GameProperty) XXX_Size() int {
	return xxx_messageInfo_GameProperty.Size(m)
}
func (m *GameProperty) XXX_DiscardUnknown() {
	xxx_messageInfo_GameProperty.DiscardUnknown(m)
}

var xxx_messageInfo_GameProperty proto.InternalMessageInfo

func (m *GameProperty) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GameProperty) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GameProperty) GetValList() []*GamePropertyVal {
	if m != nil {
		return m.ValList
	}
	return nil
}

func (m *GameProperty) GetPropertyType() uint32 {
	if m != nil {
		return m.PropertyType
	}
	return 0
}

func (m *GameProperty) GetSelectType() uint32 {
	if m != nil {
		return m.SelectType
	}
	return 0
}

func (m *GameProperty) GetExpose() bool {
	if m != nil {
		return m.Expose
	}
	return false
}

// 游戏属性值
type GamePropertyVal struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GamePropertyVal) Reset()         { *m = GamePropertyVal{} }
func (m *GamePropertyVal) String() string { return proto.CompactTextString(m) }
func (*GamePropertyVal) ProtoMessage()    {}
func (*GamePropertyVal) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{66}
}
func (m *GamePropertyVal) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GamePropertyVal.Unmarshal(m, b)
}
func (m *GamePropertyVal) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GamePropertyVal.Marshal(b, m, deterministic)
}
func (dst *GamePropertyVal) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GamePropertyVal.Merge(dst, src)
}
func (m *GamePropertyVal) XXX_Size() int {
	return xxx_messageInfo_GamePropertyVal.Size(m)
}
func (m *GamePropertyVal) XXX_DiscardUnknown() {
	xxx_messageInfo_GamePropertyVal.DiscardUnknown(m)
}

var xxx_messageInfo_GamePropertyVal proto.InternalMessageInfo

func (m *GamePropertyVal) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GamePropertyVal) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// 获取订单详情
type GetOrderDetailRequest struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderDetailRequest) Reset()         { *m = GetOrderDetailRequest{} }
func (m *GetOrderDetailRequest) String() string { return proto.CompactTextString(m) }
func (*GetOrderDetailRequest) ProtoMessage()    {}
func (*GetOrderDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{67}
}
func (m *GetOrderDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderDetailRequest.Unmarshal(m, b)
}
func (m *GetOrderDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderDetailRequest.Marshal(b, m, deterministic)
}
func (dst *GetOrderDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderDetailRequest.Merge(dst, src)
}
func (m *GetOrderDetailRequest) XXX_Size() int {
	return xxx_messageInfo_GetOrderDetailRequest.Size(m)
}
func (m *GetOrderDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderDetailRequest proto.InternalMessageInfo

func (m *GetOrderDetailRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetOrderDetailResponse struct {
	Order                *SkillProductOrderDetail `protobuf:"bytes,1,opt,name=order,proto3" json:"order"`
	Refund               *OrderRefund             `protobuf:"bytes,2,opt,name=refund,proto3" json:"refund"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetOrderDetailResponse) Reset()         { *m = GetOrderDetailResponse{} }
func (m *GetOrderDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GetOrderDetailResponse) ProtoMessage()    {}
func (*GetOrderDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{68}
}
func (m *GetOrderDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderDetailResponse.Unmarshal(m, b)
}
func (m *GetOrderDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderDetailResponse.Marshal(b, m, deterministic)
}
func (dst *GetOrderDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderDetailResponse.Merge(dst, src)
}
func (m *GetOrderDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetOrderDetailResponse.Size(m)
}
func (m *GetOrderDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderDetailResponse proto.InternalMessageInfo

func (m *GetOrderDetailResponse) GetOrder() *SkillProductOrderDetail {
	if m != nil {
		return m.Order
	}
	return nil
}

func (m *GetOrderDetailResponse) GetRefund() *OrderRefund {
	if m != nil {
		return m.Refund
	}
	return nil
}

// 商品订单详情
type SkillProductOrderDetail struct {
	// UserProfile player = 1;   // 玩家个人信息
	ProductOrder         *ProductOrder `protobuf:"bytes,2,opt,name=product_order,json=productOrder,proto3" json:"product_order"`
	OrderId              string        `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	Status               uint32        `protobuf:"varint,4,opt,name=status,proto3" json:"status"`
	SubStatus            uint32        `protobuf:"varint,5,opt,name=sub_status,json=subStatus,proto3" json:"sub_status"`
	PayTime              int64         `protobuf:"varint,6,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	ReceiveTime          int64         `protobuf:"varint,7,opt,name=receive_time,json=receiveTime,proto3" json:"receive_time"`
	FinishTime           int64         `protobuf:"varint,8,opt,name=finish_time,json=finishTime,proto3" json:"finish_time"`
	CancelTime           int64         `protobuf:"varint,9,opt,name=cancel_time,json=cancelTime,proto3" json:"cancel_time"`
	OrderEndTime         int64         `protobuf:"varint,10,opt,name=order_end_time,json=orderEndTime,proto3" json:"order_end_time"`
	StatusDesc           string        `protobuf:"bytes,11,opt,name=status_desc,json=statusDesc,proto3" json:"status_desc"`
	OrderNumber          string        `protobuf:"bytes,12,opt,name=order_number,json=orderNumber,proto3" json:"order_number"`
	OrderRemark          string        `protobuf:"bytes,13,opt,name=order_remark,json=orderRemark,proto3" json:"order_remark"`
	IsNotifyFinish       bool          `protobuf:"varint,14,opt,name=is_notify_finish,json=isNotifyFinish,proto3" json:"is_notify_finish"`
	CoachUid             uint32        `protobuf:"varint,15,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SkillProductOrderDetail) Reset()         { *m = SkillProductOrderDetail{} }
func (m *SkillProductOrderDetail) String() string { return proto.CompactTextString(m) }
func (*SkillProductOrderDetail) ProtoMessage()    {}
func (*SkillProductOrderDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{69}
}
func (m *SkillProductOrderDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SkillProductOrderDetail.Unmarshal(m, b)
}
func (m *SkillProductOrderDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SkillProductOrderDetail.Marshal(b, m, deterministic)
}
func (dst *SkillProductOrderDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SkillProductOrderDetail.Merge(dst, src)
}
func (m *SkillProductOrderDetail) XXX_Size() int {
	return xxx_messageInfo_SkillProductOrderDetail.Size(m)
}
func (m *SkillProductOrderDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_SkillProductOrderDetail.DiscardUnknown(m)
}

var xxx_messageInfo_SkillProductOrderDetail proto.InternalMessageInfo

func (m *SkillProductOrderDetail) GetProductOrder() *ProductOrder {
	if m != nil {
		return m.ProductOrder
	}
	return nil
}

func (m *SkillProductOrderDetail) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *SkillProductOrderDetail) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *SkillProductOrderDetail) GetSubStatus() uint32 {
	if m != nil {
		return m.SubStatus
	}
	return 0
}

func (m *SkillProductOrderDetail) GetPayTime() int64 {
	if m != nil {
		return m.PayTime
	}
	return 0
}

func (m *SkillProductOrderDetail) GetReceiveTime() int64 {
	if m != nil {
		return m.ReceiveTime
	}
	return 0
}

func (m *SkillProductOrderDetail) GetFinishTime() int64 {
	if m != nil {
		return m.FinishTime
	}
	return 0
}

func (m *SkillProductOrderDetail) GetCancelTime() int64 {
	if m != nil {
		return m.CancelTime
	}
	return 0
}

func (m *SkillProductOrderDetail) GetOrderEndTime() int64 {
	if m != nil {
		return m.OrderEndTime
	}
	return 0
}

func (m *SkillProductOrderDetail) GetStatusDesc() string {
	if m != nil {
		return m.StatusDesc
	}
	return ""
}

func (m *SkillProductOrderDetail) GetOrderNumber() string {
	if m != nil {
		return m.OrderNumber
	}
	return ""
}

func (m *SkillProductOrderDetail) GetOrderRemark() string {
	if m != nil {
		return m.OrderRemark
	}
	return ""
}

func (m *SkillProductOrderDetail) GetIsNotifyFinish() bool {
	if m != nil {
		return m.IsNotifyFinish
	}
	return false
}

func (m *SkillProductOrderDetail) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

type ProductOrderPriceInfo struct {
	Price                 uint32   `protobuf:"varint,1,opt,name=price,proto3" json:"price"`
	PriceUnit             string   `protobuf:"bytes,2,opt,name=price_unit,json=priceUnit,proto3" json:"price_unit"`
	MeasureCnt            uint32   `protobuf:"varint,3,opt,name=measure_cnt,json=measureCnt,proto3" json:"measure_cnt"`
	MeasureUnit           string   `protobuf:"bytes,4,opt,name=measure_unit,json=measureUnit,proto3" json:"measure_unit"`
	HasFirstRoundDiscount bool     `protobuf:"varint,6,opt,name=has_first_round_discount,json=hasFirstRoundDiscount,proto3" json:"has_first_round_discount"`
	FirstRoundPrice       uint32   `protobuf:"varint,7,opt,name=first_round_price,json=firstRoundPrice,proto3" json:"first_round_price"`
	HasDiscount           bool     `protobuf:"varint,8,opt,name=has_discount,json=hasDiscount,proto3" json:"has_discount"`
	DiscountPrice         uint32   `protobuf:"varint,9,opt,name=discount_price,json=discountPrice,proto3" json:"discount_price"`
	DiscountType          uint32   `protobuf:"varint,10,opt,name=discount_type,json=discountType,proto3" json:"discount_type"`
	DiscountDesc          string   `protobuf:"bytes,11,opt,name=discount_desc,json=discountDesc,proto3" json:"discount_desc"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *ProductOrderPriceInfo) Reset()         { *m = ProductOrderPriceInfo{} }
func (m *ProductOrderPriceInfo) String() string { return proto.CompactTextString(m) }
func (*ProductOrderPriceInfo) ProtoMessage()    {}
func (*ProductOrderPriceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{70}
}
func (m *ProductOrderPriceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductOrderPriceInfo.Unmarshal(m, b)
}
func (m *ProductOrderPriceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductOrderPriceInfo.Marshal(b, m, deterministic)
}
func (dst *ProductOrderPriceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductOrderPriceInfo.Merge(dst, src)
}
func (m *ProductOrderPriceInfo) XXX_Size() int {
	return xxx_messageInfo_ProductOrderPriceInfo.Size(m)
}
func (m *ProductOrderPriceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductOrderPriceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ProductOrderPriceInfo proto.InternalMessageInfo

func (m *ProductOrderPriceInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *ProductOrderPriceInfo) GetPriceUnit() string {
	if m != nil {
		return m.PriceUnit
	}
	return ""
}

func (m *ProductOrderPriceInfo) GetMeasureCnt() uint32 {
	if m != nil {
		return m.MeasureCnt
	}
	return 0
}

func (m *ProductOrderPriceInfo) GetMeasureUnit() string {
	if m != nil {
		return m.MeasureUnit
	}
	return ""
}

func (m *ProductOrderPriceInfo) GetHasFirstRoundDiscount() bool {
	if m != nil {
		return m.HasFirstRoundDiscount
	}
	return false
}

func (m *ProductOrderPriceInfo) GetFirstRoundPrice() uint32 {
	if m != nil {
		return m.FirstRoundPrice
	}
	return 0
}

func (m *ProductOrderPriceInfo) GetHasDiscount() bool {
	if m != nil {
		return m.HasDiscount
	}
	return false
}

func (m *ProductOrderPriceInfo) GetDiscountPrice() uint32 {
	if m != nil {
		return m.DiscountPrice
	}
	return 0
}

func (m *ProductOrderPriceInfo) GetDiscountType() uint32 {
	if m != nil {
		return m.DiscountType
	}
	return 0
}

func (m *ProductOrderPriceInfo) GetDiscountDesc() string {
	if m != nil {
		return m.DiscountDesc
	}
	return ""
}

// 商品订单
type ProductOrder struct {
	Coach                *UserProfile           `protobuf:"bytes,1,opt,name=coach,proto3" json:"coach"`
	ProductId            uint64                 `protobuf:"varint,2,opt,name=product_id,json=productId,proto3" json:"product_id"`
	Name                 string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	Icon                 string                 `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon"`
	PriceInfo            *ProductOrderPriceInfo `protobuf:"bytes,5,opt,name=price_info,json=priceInfo,proto3" json:"price_info"`
	Count                uint32                 `protobuf:"varint,6,opt,name=count,proto3" json:"count"`
	TotalPrice           uint32                 `protobuf:"varint,7,opt,name=total_price,json=totalPrice,proto3" json:"total_price"`
	GuaranteeWinText     string                 `protobuf:"bytes,8,opt,name=guarantee_win_text,json=guaranteeWinText,proto3" json:"guarantee_win_text"`
	GameId               uint32                 `protobuf:"varint,9,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	CanCallCustomer      bool                   `protobuf:"varint,10,opt,name=can_call_customer,json=canCallCustomer,proto3" json:"can_call_customer"`
	CoachTotalPrice      uint32                 `protobuf:"varint,11,opt,name=coach_total_price,json=coachTotalPrice,proto3" json:"coach_total_price"`
	CouponUseDetail      *CouponUseDetail       `protobuf:"bytes,12,opt,name=coupon_use_detail,json=couponUseDetail,proto3" json:"coupon_use_detail"`
	NewCustomerUseDetail *NewCustomerUseDetail  `protobuf:"bytes,13,opt,name=new_customer_use_detail,json=newCustomerUseDetail,proto3" json:"new_customer_use_detail"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ProductOrder) Reset()         { *m = ProductOrder{} }
func (m *ProductOrder) String() string { return proto.CompactTextString(m) }
func (*ProductOrder) ProtoMessage()    {}
func (*ProductOrder) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{71}
}
func (m *ProductOrder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProductOrder.Unmarshal(m, b)
}
func (m *ProductOrder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProductOrder.Marshal(b, m, deterministic)
}
func (dst *ProductOrder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProductOrder.Merge(dst, src)
}
func (m *ProductOrder) XXX_Size() int {
	return xxx_messageInfo_ProductOrder.Size(m)
}
func (m *ProductOrder) XXX_DiscardUnknown() {
	xxx_messageInfo_ProductOrder.DiscardUnknown(m)
}

var xxx_messageInfo_ProductOrder proto.InternalMessageInfo

func (m *ProductOrder) GetCoach() *UserProfile {
	if m != nil {
		return m.Coach
	}
	return nil
}

func (m *ProductOrder) GetProductId() uint64 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *ProductOrder) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ProductOrder) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *ProductOrder) GetPriceInfo() *ProductOrderPriceInfo {
	if m != nil {
		return m.PriceInfo
	}
	return nil
}

func (m *ProductOrder) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ProductOrder) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *ProductOrder) GetGuaranteeWinText() string {
	if m != nil {
		return m.GuaranteeWinText
	}
	return ""
}

func (m *ProductOrder) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ProductOrder) GetCanCallCustomer() bool {
	if m != nil {
		return m.CanCallCustomer
	}
	return false
}

func (m *ProductOrder) GetCoachTotalPrice() uint32 {
	if m != nil {
		return m.CoachTotalPrice
	}
	return 0
}

func (m *ProductOrder) GetCouponUseDetail() *CouponUseDetail {
	if m != nil {
		return m.CouponUseDetail
	}
	return nil
}

func (m *ProductOrder) GetNewCustomerUseDetail() *NewCustomerUseDetail {
	if m != nil {
		return m.NewCustomerUseDetail
	}
	return nil
}

// 优惠券使用详情
type CouponUseDetail struct {
	UseCoupon            bool     `protobuf:"varint,1,opt,name=use_coupon,json=useCoupon,proto3" json:"use_coupon"`
	CouponMoney          uint32   `protobuf:"varint,2,opt,name=coupon_money,json=couponMoney,proto3" json:"coupon_money"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponUseDetail) Reset()         { *m = CouponUseDetail{} }
func (m *CouponUseDetail) String() string { return proto.CompactTextString(m) }
func (*CouponUseDetail) ProtoMessage()    {}
func (*CouponUseDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{72}
}
func (m *CouponUseDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponUseDetail.Unmarshal(m, b)
}
func (m *CouponUseDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponUseDetail.Marshal(b, m, deterministic)
}
func (dst *CouponUseDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponUseDetail.Merge(dst, src)
}
func (m *CouponUseDetail) XXX_Size() int {
	return xxx_messageInfo_CouponUseDetail.Size(m)
}
func (m *CouponUseDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponUseDetail.DiscardUnknown(m)
}

var xxx_messageInfo_CouponUseDetail proto.InternalMessageInfo

func (m *CouponUseDetail) GetUseCoupon() bool {
	if m != nil {
		return m.UseCoupon
	}
	return false
}

func (m *CouponUseDetail) GetCouponMoney() uint32 {
	if m != nil {
		return m.CouponMoney
	}
	return 0
}

// 新客优惠使用详情
type NewCustomerUseDetail struct {
	UseNewCustomerDiscount bool     `protobuf:"varint,1,opt,name=use_new_customer_discount,json=useNewCustomerDiscount,proto3" json:"use_new_customer_discount"`
	NewCustomerPrice       uint32   `protobuf:"varint,2,opt,name=new_customer_price,json=newCustomerPrice,proto3" json:"new_customer_price"`
	PlatBonusFee           uint32   `protobuf:"varint,3,opt,name=plat_bonus_fee,json=platBonusFee,proto3" json:"plat_bonus_fee"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *NewCustomerUseDetail) Reset()         { *m = NewCustomerUseDetail{} }
func (m *NewCustomerUseDetail) String() string { return proto.CompactTextString(m) }
func (*NewCustomerUseDetail) ProtoMessage()    {}
func (*NewCustomerUseDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{73}
}
func (m *NewCustomerUseDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewCustomerUseDetail.Unmarshal(m, b)
}
func (m *NewCustomerUseDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewCustomerUseDetail.Marshal(b, m, deterministic)
}
func (dst *NewCustomerUseDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewCustomerUseDetail.Merge(dst, src)
}
func (m *NewCustomerUseDetail) XXX_Size() int {
	return xxx_messageInfo_NewCustomerUseDetail.Size(m)
}
func (m *NewCustomerUseDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_NewCustomerUseDetail.DiscardUnknown(m)
}

var xxx_messageInfo_NewCustomerUseDetail proto.InternalMessageInfo

func (m *NewCustomerUseDetail) GetUseNewCustomerDiscount() bool {
	if m != nil {
		return m.UseNewCustomerDiscount
	}
	return false
}

func (m *NewCustomerUseDetail) GetNewCustomerPrice() uint32 {
	if m != nil {
		return m.NewCustomerPrice
	}
	return 0
}

func (m *NewCustomerUseDetail) GetPlatBonusFee() uint32 {
	if m != nil {
		return m.PlatBonusFee
	}
	return 0
}

// 退款信息
type OrderRefund struct {
	RefundId             string   `protobuf:"bytes,1,opt,name=refund_id,json=refundId,proto3" json:"refund_id"`
	AppealId             string   `protobuf:"bytes,2,opt,name=appeal_id,json=appealId,proto3" json:"appeal_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderRefund) Reset()         { *m = OrderRefund{} }
func (m *OrderRefund) String() string { return proto.CompactTextString(m) }
func (*OrderRefund) ProtoMessage()    {}
func (*OrderRefund) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{74}
}
func (m *OrderRefund) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderRefund.Unmarshal(m, b)
}
func (m *OrderRefund) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderRefund.Marshal(b, m, deterministic)
}
func (dst *OrderRefund) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderRefund.Merge(dst, src)
}
func (m *OrderRefund) XXX_Size() int {
	return xxx_messageInfo_OrderRefund.Size(m)
}
func (m *OrderRefund) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderRefund.DiscardUnknown(m)
}

var xxx_messageInfo_OrderRefund proto.InternalMessageInfo

func (m *OrderRefund) GetRefundId() string {
	if m != nil {
		return m.RefundId
	}
	return ""
}

func (m *OrderRefund) GetAppealId() string {
	if m != nil {
		return m.AppealId
	}
	return ""
}

// =================== 临时接口 ==========
type ManualAddESportCoachRoleRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManualAddESportCoachRoleRequest) Reset()         { *m = ManualAddESportCoachRoleRequest{} }
func (m *ManualAddESportCoachRoleRequest) String() string { return proto.CompactTextString(m) }
func (*ManualAddESportCoachRoleRequest) ProtoMessage()    {}
func (*ManualAddESportCoachRoleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{75}
}
func (m *ManualAddESportCoachRoleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualAddESportCoachRoleRequest.Unmarshal(m, b)
}
func (m *ManualAddESportCoachRoleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualAddESportCoachRoleRequest.Marshal(b, m, deterministic)
}
func (dst *ManualAddESportCoachRoleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualAddESportCoachRoleRequest.Merge(dst, src)
}
func (m *ManualAddESportCoachRoleRequest) XXX_Size() int {
	return xxx_messageInfo_ManualAddESportCoachRoleRequest.Size(m)
}
func (m *ManualAddESportCoachRoleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualAddESportCoachRoleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ManualAddESportCoachRoleRequest proto.InternalMessageInfo

type ManualAddESportCoachRoleResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManualAddESportCoachRoleResponse) Reset()         { *m = ManualAddESportCoachRoleResponse{} }
func (m *ManualAddESportCoachRoleResponse) String() string { return proto.CompactTextString(m) }
func (*ManualAddESportCoachRoleResponse) ProtoMessage()    {}
func (*ManualAddESportCoachRoleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{76}
}
func (m *ManualAddESportCoachRoleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualAddESportCoachRoleResponse.Unmarshal(m, b)
}
func (m *ManualAddESportCoachRoleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualAddESportCoachRoleResponse.Marshal(b, m, deterministic)
}
func (dst *ManualAddESportCoachRoleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualAddESportCoachRoleResponse.Merge(dst, src)
}
func (m *ManualAddESportCoachRoleResponse) XXX_Size() int {
	return xxx_messageInfo_ManualAddESportCoachRoleResponse.Size(m)
}
func (m *ManualAddESportCoachRoleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualAddESportCoachRoleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ManualAddESportCoachRoleResponse proto.InternalMessageInfo

type GetCoachStatisticsRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachStatisticsRequest) Reset()         { *m = GetCoachStatisticsRequest{} }
func (m *GetCoachStatisticsRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachStatisticsRequest) ProtoMessage()    {}
func (*GetCoachStatisticsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{77}
}
func (m *GetCoachStatisticsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachStatisticsRequest.Unmarshal(m, b)
}
func (m *GetCoachStatisticsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachStatisticsRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachStatisticsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachStatisticsRequest.Merge(dst, src)
}
func (m *GetCoachStatisticsRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachStatisticsRequest.Size(m)
}
func (m *GetCoachStatisticsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachStatisticsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachStatisticsRequest proto.InternalMessageInfo

func (m *GetCoachStatisticsRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetCoachStatisticsRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetCoachStatisticsResponse struct {
	OrderNum             uint32   `protobuf:"varint,1,opt,name=order_num,json=orderNum,proto3" json:"order_num"`
	CustomerNum          uint32   `protobuf:"varint,2,opt,name=customer_num,json=customerNum,proto3" json:"customer_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachStatisticsResponse) Reset()         { *m = GetCoachStatisticsResponse{} }
func (m *GetCoachStatisticsResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachStatisticsResponse) ProtoMessage()    {}
func (*GetCoachStatisticsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{78}
}
func (m *GetCoachStatisticsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachStatisticsResponse.Unmarshal(m, b)
}
func (m *GetCoachStatisticsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachStatisticsResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachStatisticsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachStatisticsResponse.Merge(dst, src)
}
func (m *GetCoachStatisticsResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachStatisticsResponse.Size(m)
}
func (m *GetCoachStatisticsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachStatisticsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachStatisticsResponse proto.InternalMessageInfo

func (m *GetCoachStatisticsResponse) GetOrderNum() uint32 {
	if m != nil {
		return m.OrderNum
	}
	return 0
}

func (m *GetCoachStatisticsResponse) GetCustomerNum() uint32 {
	if m != nil {
		return m.CustomerNum
	}
	return 0
}

type EvaluateWordCnt struct {
	Word                 string   `protobuf:"bytes,1,opt,name=word,proto3" json:"word"`
	Cnt                  uint32   `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EvaluateWordCnt) Reset()         { *m = EvaluateWordCnt{} }
func (m *EvaluateWordCnt) String() string { return proto.CompactTextString(m) }
func (*EvaluateWordCnt) ProtoMessage()    {}
func (*EvaluateWordCnt) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{79}
}
func (m *EvaluateWordCnt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EvaluateWordCnt.Unmarshal(m, b)
}
func (m *EvaluateWordCnt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EvaluateWordCnt.Marshal(b, m, deterministic)
}
func (dst *EvaluateWordCnt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EvaluateWordCnt.Merge(dst, src)
}
func (m *EvaluateWordCnt) XXX_Size() int {
	return xxx_messageInfo_EvaluateWordCnt.Size(m)
}
func (m *EvaluateWordCnt) XXX_DiscardUnknown() {
	xxx_messageInfo_EvaluateWordCnt.DiscardUnknown(m)
}

var xxx_messageInfo_EvaluateWordCnt proto.InternalMessageInfo

func (m *EvaluateWordCnt) GetWord() string {
	if m != nil {
		return m.Word
	}
	return ""
}

func (m *EvaluateWordCnt) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type EvaluateScore struct {
	AvgScore             float32  `protobuf:"fixed32,1,opt,name=avg_score,json=avgScore,proto3" json:"avg_score"`
	ServiceScore         float32  `protobuf:"fixed32,2,opt,name=service_score,json=serviceScore,proto3" json:"service_score"`
	SkillScore           float32  `protobuf:"fixed32,3,opt,name=skill_score,json=skillScore,proto3" json:"skill_score"`
	VoiceScore           float32  `protobuf:"fixed32,4,opt,name=voice_score,json=voiceScore,proto3" json:"voice_score"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EvaluateScore) Reset()         { *m = EvaluateScore{} }
func (m *EvaluateScore) String() string { return proto.CompactTextString(m) }
func (*EvaluateScore) ProtoMessage()    {}
func (*EvaluateScore) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{80}
}
func (m *EvaluateScore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EvaluateScore.Unmarshal(m, b)
}
func (m *EvaluateScore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EvaluateScore.Marshal(b, m, deterministic)
}
func (dst *EvaluateScore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EvaluateScore.Merge(dst, src)
}
func (m *EvaluateScore) XXX_Size() int {
	return xxx_messageInfo_EvaluateScore.Size(m)
}
func (m *EvaluateScore) XXX_DiscardUnknown() {
	xxx_messageInfo_EvaluateScore.DiscardUnknown(m)
}

var xxx_messageInfo_EvaluateScore proto.InternalMessageInfo

func (m *EvaluateScore) GetAvgScore() float32 {
	if m != nil {
		return m.AvgScore
	}
	return 0
}

func (m *EvaluateScore) GetServiceScore() float32 {
	if m != nil {
		return m.ServiceScore
	}
	return 0
}

func (m *EvaluateScore) GetSkillScore() float32 {
	if m != nil {
		return m.SkillScore
	}
	return 0
}

func (m *EvaluateScore) GetVoiceScore() float32 {
	if m != nil {
		return m.VoiceScore
	}
	return 0
}

// 评价汇总
type EvaluateSummary struct {
	TotalCnt             uint32             `protobuf:"varint,1,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt"`
	ScoreInfo            *EvaluateScore     `protobuf:"bytes,2,opt,name=score_info,json=scoreInfo,proto3" json:"score_info"`
	WordCntList          []*EvaluateWordCnt `protobuf:"bytes,3,rep,name=word_cnt_list,json=wordCntList,proto3" json:"word_cnt_list"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *EvaluateSummary) Reset()         { *m = EvaluateSummary{} }
func (m *EvaluateSummary) String() string { return proto.CompactTextString(m) }
func (*EvaluateSummary) ProtoMessage()    {}
func (*EvaluateSummary) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{81}
}
func (m *EvaluateSummary) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EvaluateSummary.Unmarshal(m, b)
}
func (m *EvaluateSummary) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EvaluateSummary.Marshal(b, m, deterministic)
}
func (dst *EvaluateSummary) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EvaluateSummary.Merge(dst, src)
}
func (m *EvaluateSummary) XXX_Size() int {
	return xxx_messageInfo_EvaluateSummary.Size(m)
}
func (m *EvaluateSummary) XXX_DiscardUnknown() {
	xxx_messageInfo_EvaluateSummary.DiscardUnknown(m)
}

var xxx_messageInfo_EvaluateSummary proto.InternalMessageInfo

func (m *EvaluateSummary) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

func (m *EvaluateSummary) GetScoreInfo() *EvaluateScore {
	if m != nil {
		return m.ScoreInfo
	}
	return nil
}

func (m *EvaluateSummary) GetWordCntList() []*EvaluateWordCnt {
	if m != nil {
		return m.WordCntList
	}
	return nil
}

type UserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account"`
	Sex                  uint32   `protobuf:"varint,4,opt,name=sex,proto3" json:"sex"`
	HeadDyMd5            string   `protobuf:"bytes,5,opt,name=head_dy_md5,json=headDyMd5,proto3" json:"head_dy_md5"`
	Remark               string   `protobuf:"bytes,6,opt,name=remark,proto3" json:"remark"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfo) Reset()         { *m = UserInfo{} }
func (m *UserInfo) String() string { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()    {}
func (*UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{82}
}
func (m *UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo.Unmarshal(m, b)
}
func (m *UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo.Marshal(b, m, deterministic)
}
func (dst *UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo.Merge(dst, src)
}
func (m *UserInfo) XXX_Size() int {
	return xxx_messageInfo_UserInfo.Size(m)
}
func (m *UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo proto.InternalMessageInfo

func (m *UserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserInfo) GetHeadDyMd5() string {
	if m != nil {
		return m.HeadDyMd5
	}
	return ""
}

func (m *UserInfo) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

// 订单评价信息
type EvaluateInfo struct {
	IsAnonymous          bool           `protobuf:"varint,1,opt,name=is_anonymous,json=isAnonymous,proto3" json:"is_anonymous"`
	EvaluateTime         int64          `protobuf:"varint,2,opt,name=evaluate_time,json=evaluateTime,proto3" json:"evaluate_time"`
	WordList             []string       `protobuf:"bytes,3,rep,name=word_list,json=wordList,proto3" json:"word_list"`
	Content              string         `protobuf:"bytes,4,opt,name=content,proto3" json:"content"`
	ScoreInfo            *EvaluateScore `protobuf:"bytes,5,opt,name=score_info,json=scoreInfo,proto3" json:"score_info"`
	OrderId              string         `protobuf:"bytes,6,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	Player               *UserInfo      `protobuf:"bytes,7,opt,name=player,proto3" json:"player"`
	IsQueryUser          bool           `protobuf:"varint,8,opt,name=is_query_user,json=isQueryUser,proto3" json:"is_query_user"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *EvaluateInfo) Reset()         { *m = EvaluateInfo{} }
func (m *EvaluateInfo) String() string { return proto.CompactTextString(m) }
func (*EvaluateInfo) ProtoMessage()    {}
func (*EvaluateInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{83}
}
func (m *EvaluateInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EvaluateInfo.Unmarshal(m, b)
}
func (m *EvaluateInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EvaluateInfo.Marshal(b, m, deterministic)
}
func (dst *EvaluateInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EvaluateInfo.Merge(dst, src)
}
func (m *EvaluateInfo) XXX_Size() int {
	return xxx_messageInfo_EvaluateInfo.Size(m)
}
func (m *EvaluateInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EvaluateInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EvaluateInfo proto.InternalMessageInfo

func (m *EvaluateInfo) GetIsAnonymous() bool {
	if m != nil {
		return m.IsAnonymous
	}
	return false
}

func (m *EvaluateInfo) GetEvaluateTime() int64 {
	if m != nil {
		return m.EvaluateTime
	}
	return 0
}

func (m *EvaluateInfo) GetWordList() []string {
	if m != nil {
		return m.WordList
	}
	return nil
}

func (m *EvaluateInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *EvaluateInfo) GetScoreInfo() *EvaluateScore {
	if m != nil {
		return m.ScoreInfo
	}
	return nil
}

func (m *EvaluateInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *EvaluateInfo) GetPlayer() *UserInfo {
	if m != nil {
		return m.Player
	}
	return nil
}

func (m *EvaluateInfo) GetIsQueryUser() bool {
	if m != nil {
		return m.IsQueryUser
	}
	return false
}

// 分页获取用户评价列表
type GetEvaluateListRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Word                 string   `protobuf:"bytes,2,opt,name=word,proto3" json:"word"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit"`
	GameId               uint32   `protobuf:"varint,5,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEvaluateListRequest) Reset()         { *m = GetEvaluateListRequest{} }
func (m *GetEvaluateListRequest) String() string { return proto.CompactTextString(m) }
func (*GetEvaluateListRequest) ProtoMessage()    {}
func (*GetEvaluateListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{84}
}
func (m *GetEvaluateListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEvaluateListRequest.Unmarshal(m, b)
}
func (m *GetEvaluateListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEvaluateListRequest.Marshal(b, m, deterministic)
}
func (dst *GetEvaluateListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEvaluateListRequest.Merge(dst, src)
}
func (m *GetEvaluateListRequest) XXX_Size() int {
	return xxx_messageInfo_GetEvaluateListRequest.Size(m)
}
func (m *GetEvaluateListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEvaluateListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetEvaluateListRequest proto.InternalMessageInfo

func (m *GetEvaluateListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetEvaluateListRequest) GetWord() string {
	if m != nil {
		return m.Word
	}
	return ""
}

func (m *GetEvaluateListRequest) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetEvaluateListRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetEvaluateListRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetEvaluateListResponse struct {
	Summary              *EvaluateSummary `protobuf:"bytes,1,opt,name=summary,proto3" json:"summary"`
	List                 []*EvaluateInfo  `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetEvaluateListResponse) Reset()         { *m = GetEvaluateListResponse{} }
func (m *GetEvaluateListResponse) String() string { return proto.CompactTextString(m) }
func (*GetEvaluateListResponse) ProtoMessage()    {}
func (*GetEvaluateListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{85}
}
func (m *GetEvaluateListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEvaluateListResponse.Unmarshal(m, b)
}
func (m *GetEvaluateListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEvaluateListResponse.Marshal(b, m, deterministic)
}
func (dst *GetEvaluateListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEvaluateListResponse.Merge(dst, src)
}
func (m *GetEvaluateListResponse) XXX_Size() int {
	return xxx_messageInfo_GetEvaluateListResponse.Size(m)
}
func (m *GetEvaluateListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEvaluateListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetEvaluateListResponse proto.InternalMessageInfo

func (m *GetEvaluateListResponse) GetSummary() *EvaluateSummary {
	if m != nil {
		return m.Summary
	}
	return nil
}

func (m *GetEvaluateListResponse) GetList() []*EvaluateInfo {
	if m != nil {
		return m.List
	}
	return nil
}

// 检查是否可以举报该电竞陪玩
type CheckCanReportCoachRequest struct {
	TargetUid            uint32   `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid"`
	TargetAccount        string   `protobuf:"bytes,2,opt,name=target_account,json=targetAccount,proto3" json:"target_account"`
	Version              string   `protobuf:"bytes,3,opt,name=version,proto3" json:"version"`
	OsType               string   `protobuf:"bytes,4,opt,name=os_type,json=osType,proto3" json:"os_type"`
	Platform             string   `protobuf:"bytes,5,opt,name=platform,proto3" json:"platform"`
	MarketId             string   `protobuf:"bytes,6,opt,name=market_id,json=marketId,proto3" json:"market_id"`
	Ip                   string   `protobuf:"bytes,7,opt,name=ip,proto3" json:"ip"`
	DeviceId             string   `protobuf:"bytes,8,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
	App                  string   `protobuf:"bytes,9,opt,name=app,proto3" json:"app"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckCanReportCoachRequest) Reset()         { *m = CheckCanReportCoachRequest{} }
func (m *CheckCanReportCoachRequest) String() string { return proto.CompactTextString(m) }
func (*CheckCanReportCoachRequest) ProtoMessage()    {}
func (*CheckCanReportCoachRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{86}
}
func (m *CheckCanReportCoachRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCanReportCoachRequest.Unmarshal(m, b)
}
func (m *CheckCanReportCoachRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCanReportCoachRequest.Marshal(b, m, deterministic)
}
func (dst *CheckCanReportCoachRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCanReportCoachRequest.Merge(dst, src)
}
func (m *CheckCanReportCoachRequest) XXX_Size() int {
	return xxx_messageInfo_CheckCanReportCoachRequest.Size(m)
}
func (m *CheckCanReportCoachRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCanReportCoachRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCanReportCoachRequest proto.InternalMessageInfo

func (m *CheckCanReportCoachRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *CheckCanReportCoachRequest) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *CheckCanReportCoachRequest) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *CheckCanReportCoachRequest) GetOsType() string {
	if m != nil {
		return m.OsType
	}
	return ""
}

func (m *CheckCanReportCoachRequest) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

func (m *CheckCanReportCoachRequest) GetMarketId() string {
	if m != nil {
		return m.MarketId
	}
	return ""
}

func (m *CheckCanReportCoachRequest) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *CheckCanReportCoachRequest) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *CheckCanReportCoachRequest) GetApp() string {
	if m != nil {
		return m.App
	}
	return ""
}

type CheckCanReportCoachResponse struct {
	CanReport            bool     `protobuf:"varint,1,opt,name=can_report,json=canReport,proto3" json:"can_report"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckCanReportCoachResponse) Reset()         { *m = CheckCanReportCoachResponse{} }
func (m *CheckCanReportCoachResponse) String() string { return proto.CompactTextString(m) }
func (*CheckCanReportCoachResponse) ProtoMessage()    {}
func (*CheckCanReportCoachResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{87}
}
func (m *CheckCanReportCoachResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCanReportCoachResponse.Unmarshal(m, b)
}
func (m *CheckCanReportCoachResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCanReportCoachResponse.Marshal(b, m, deterministic)
}
func (dst *CheckCanReportCoachResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCanReportCoachResponse.Merge(dst, src)
}
func (m *CheckCanReportCoachResponse) XXX_Size() int {
	return xxx_messageInfo_CheckCanReportCoachResponse.Size(m)
}
func (m *CheckCanReportCoachResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCanReportCoachResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCanReportCoachResponse proto.InternalMessageInfo

func (m *CheckCanReportCoachResponse) GetCanReport() bool {
	if m != nil {
		return m.CanReport
	}
	return false
}

// 设置秒接单开关
type SetQuickReceiveSwitchRequest struct {
	Switch               bool     `protobuf:"varint,1,opt,name=switch,proto3" json:"switch"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetQuickReceiveSwitchRequest) Reset()         { *m = SetQuickReceiveSwitchRequest{} }
func (m *SetQuickReceiveSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*SetQuickReceiveSwitchRequest) ProtoMessage()    {}
func (*SetQuickReceiveSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{88}
}
func (m *SetQuickReceiveSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetQuickReceiveSwitchRequest.Unmarshal(m, b)
}
func (m *SetQuickReceiveSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetQuickReceiveSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *SetQuickReceiveSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetQuickReceiveSwitchRequest.Merge(dst, src)
}
func (m *SetQuickReceiveSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_SetQuickReceiveSwitchRequest.Size(m)
}
func (m *SetQuickReceiveSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetQuickReceiveSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetQuickReceiveSwitchRequest proto.InternalMessageInfo

func (m *SetQuickReceiveSwitchRequest) GetSwitch() bool {
	if m != nil {
		return m.Switch
	}
	return false
}

type SetQuickReceiveSwitchResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetQuickReceiveSwitchResponse) Reset()         { *m = SetQuickReceiveSwitchResponse{} }
func (m *SetQuickReceiveSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*SetQuickReceiveSwitchResponse) ProtoMessage()    {}
func (*SetQuickReceiveSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{89}
}
func (m *SetQuickReceiveSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetQuickReceiveSwitchResponse.Unmarshal(m, b)
}
func (m *SetQuickReceiveSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetQuickReceiveSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *SetQuickReceiveSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetQuickReceiveSwitchResponse.Merge(dst, src)
}
func (m *SetQuickReceiveSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_SetQuickReceiveSwitchResponse.Size(m)
}
func (m *SetQuickReceiveSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetQuickReceiveSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetQuickReceiveSwitchResponse proto.InternalMessageInfo

// 获取秒接单开关状态
type GetQuickReceiveSwitchRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuickReceiveSwitchRequest) Reset()         { *m = GetQuickReceiveSwitchRequest{} }
func (m *GetQuickReceiveSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*GetQuickReceiveSwitchRequest) ProtoMessage()    {}
func (*GetQuickReceiveSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{90}
}
func (m *GetQuickReceiveSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickReceiveSwitchRequest.Unmarshal(m, b)
}
func (m *GetQuickReceiveSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickReceiveSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *GetQuickReceiveSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickReceiveSwitchRequest.Merge(dst, src)
}
func (m *GetQuickReceiveSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_GetQuickReceiveSwitchRequest.Size(m)
}
func (m *GetQuickReceiveSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickReceiveSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickReceiveSwitchRequest proto.InternalMessageInfo

type GetQuickReceiveSwitchResponse struct {
	Switch               bool     `protobuf:"varint,1,opt,name=switch,proto3" json:"switch"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuickReceiveSwitchResponse) Reset()         { *m = GetQuickReceiveSwitchResponse{} }
func (m *GetQuickReceiveSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*GetQuickReceiveSwitchResponse) ProtoMessage()    {}
func (*GetQuickReceiveSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{91}
}
func (m *GetQuickReceiveSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickReceiveSwitchResponse.Unmarshal(m, b)
}
func (m *GetQuickReceiveSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickReceiveSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *GetQuickReceiveSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickReceiveSwitchResponse.Merge(dst, src)
}
func (m *GetQuickReceiveSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_GetQuickReceiveSwitchResponse.Size(m)
}
func (m *GetQuickReceiveSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickReceiveSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickReceiveSwitchResponse proto.InternalMessageInfo

func (m *GetQuickReceiveSwitchResponse) GetSwitch() bool {
	if m != nil {
		return m.Switch
	}
	return false
}

// 获取当前用户的定价等级信息
// uri: /get_cur_pricing_info
type GetCurPricingInfoRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCurPricingInfoRequest) Reset()         { *m = GetCurPricingInfoRequest{} }
func (m *GetCurPricingInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetCurPricingInfoRequest) ProtoMessage()    {}
func (*GetCurPricingInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{92}
}
func (m *GetCurPricingInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurPricingInfoRequest.Unmarshal(m, b)
}
func (m *GetCurPricingInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurPricingInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetCurPricingInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurPricingInfoRequest.Merge(dst, src)
}
func (m *GetCurPricingInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetCurPricingInfoRequest.Size(m)
}
func (m *GetCurPricingInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurPricingInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurPricingInfoRequest proto.InternalMessageInfo

func (m *GetCurPricingInfoRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

// 获取当前用户的定价等级信息响应
type GetCurPricingInfoResponse struct {
	PricingLevels        []*PricingLevel `protobuf:"bytes,1,rep,name=pricing_levels,json=pricingLevels,proto3" json:"pricing_levels"`
	PriceUnit            string          `protobuf:"bytes,2,opt,name=price_unit,json=priceUnit,proto3" json:"price_unit"`
	UserLevel            uint32          `protobuf:"varint,3,opt,name=user_level,json=userLevel,proto3" json:"user_level"`
	SettingPriceLevel    uint32          `protobuf:"varint,4,opt,name=setting_price_level,json=settingPriceLevel,proto3" json:"setting_price_level"`
	CurrentPrice         uint32          `protobuf:"varint,5,opt,name=current_price,json=currentPrice,proto3" json:"current_price"`
	IsRenownedPlayer     bool            `protobuf:"varint,6,opt,name=is_renowned_player,json=isRenownedPlayer,proto3" json:"is_renowned_player"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetCurPricingInfoResponse) Reset()         { *m = GetCurPricingInfoResponse{} }
func (m *GetCurPricingInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetCurPricingInfoResponse) ProtoMessage()    {}
func (*GetCurPricingInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{93}
}
func (m *GetCurPricingInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurPricingInfoResponse.Unmarshal(m, b)
}
func (m *GetCurPricingInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurPricingInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetCurPricingInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurPricingInfoResponse.Merge(dst, src)
}
func (m *GetCurPricingInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetCurPricingInfoResponse.Size(m)
}
func (m *GetCurPricingInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurPricingInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurPricingInfoResponse proto.InternalMessageInfo

func (m *GetCurPricingInfoResponse) GetPricingLevels() []*PricingLevel {
	if m != nil {
		return m.PricingLevels
	}
	return nil
}

func (m *GetCurPricingInfoResponse) GetPriceUnit() string {
	if m != nil {
		return m.PriceUnit
	}
	return ""
}

func (m *GetCurPricingInfoResponse) GetUserLevel() uint32 {
	if m != nil {
		return m.UserLevel
	}
	return 0
}

func (m *GetCurPricingInfoResponse) GetSettingPriceLevel() uint32 {
	if m != nil {
		return m.SettingPriceLevel
	}
	return 0
}

func (m *GetCurPricingInfoResponse) GetCurrentPrice() uint32 {
	if m != nil {
		return m.CurrentPrice
	}
	return 0
}

func (m *GetCurPricingInfoResponse) GetIsRenownedPlayer() bool {
	if m != nil {
		return m.IsRenownedPlayer
	}
	return false
}

// 设置定价等级
// uri: /set_pricing_level
type SetPricingLevelRequest struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPricingLevelRequest) Reset()         { *m = SetPricingLevelRequest{} }
func (m *SetPricingLevelRequest) String() string { return proto.CompactTextString(m) }
func (*SetPricingLevelRequest) ProtoMessage()    {}
func (*SetPricingLevelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{94}
}
func (m *SetPricingLevelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPricingLevelRequest.Unmarshal(m, b)
}
func (m *SetPricingLevelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPricingLevelRequest.Marshal(b, m, deterministic)
}
func (dst *SetPricingLevelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPricingLevelRequest.Merge(dst, src)
}
func (m *SetPricingLevelRequest) XXX_Size() int {
	return xxx_messageInfo_SetPricingLevelRequest.Size(m)
}
func (m *SetPricingLevelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPricingLevelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetPricingLevelRequest proto.InternalMessageInfo

func (m *SetPricingLevelRequest) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *SetPricingLevelRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

// 设置定价等级响应
type SetPricingLevelResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPricingLevelResponse) Reset()         { *m = SetPricingLevelResponse{} }
func (m *SetPricingLevelResponse) String() string { return proto.CompactTextString(m) }
func (*SetPricingLevelResponse) ProtoMessage()    {}
func (*SetPricingLevelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{95}
}
func (m *SetPricingLevelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPricingLevelResponse.Unmarshal(m, b)
}
func (m *SetPricingLevelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPricingLevelResponse.Marshal(b, m, deterministic)
}
func (dst *SetPricingLevelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPricingLevelResponse.Merge(dst, src)
}
func (m *SetPricingLevelResponse) XXX_Size() int {
	return xxx_messageInfo_SetPricingLevelResponse.Size(m)
}
func (m *SetPricingLevelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPricingLevelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetPricingLevelResponse proto.InternalMessageInfo

// 获取可申请的标识列表
// uri: /get_applicable_labels
type GetApplicableLabelsRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetApplicableLabelsRequest) Reset()         { *m = GetApplicableLabelsRequest{} }
func (m *GetApplicableLabelsRequest) String() string { return proto.CompactTextString(m) }
func (*GetApplicableLabelsRequest) ProtoMessage()    {}
func (*GetApplicableLabelsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{96}
}
func (m *GetApplicableLabelsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplicableLabelsRequest.Unmarshal(m, b)
}
func (m *GetApplicableLabelsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplicableLabelsRequest.Marshal(b, m, deterministic)
}
func (dst *GetApplicableLabelsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplicableLabelsRequest.Merge(dst, src)
}
func (m *GetApplicableLabelsRequest) XXX_Size() int {
	return xxx_messageInfo_GetApplicableLabelsRequest.Size(m)
}
func (m *GetApplicableLabelsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplicableLabelsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplicableLabelsRequest proto.InternalMessageInfo

func (m *GetApplicableLabelsRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

// 获取可申请的标识列表响应
type GetApplicableLabelsResponse struct {
	AppliedLabels        []*Label `protobuf:"bytes,1,rep,name=applied_labels,json=appliedLabels,proto3" json:"applied_labels"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetApplicableLabelsResponse) Reset()         { *m = GetApplicableLabelsResponse{} }
func (m *GetApplicableLabelsResponse) String() string { return proto.CompactTextString(m) }
func (*GetApplicableLabelsResponse) ProtoMessage()    {}
func (*GetApplicableLabelsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{97}
}
func (m *GetApplicableLabelsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplicableLabelsResponse.Unmarshal(m, b)
}
func (m *GetApplicableLabelsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplicableLabelsResponse.Marshal(b, m, deterministic)
}
func (dst *GetApplicableLabelsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplicableLabelsResponse.Merge(dst, src)
}
func (m *GetApplicableLabelsResponse) XXX_Size() int {
	return xxx_messageInfo_GetApplicableLabelsResponse.Size(m)
}
func (m *GetApplicableLabelsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplicableLabelsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplicableLabelsResponse proto.InternalMessageInfo

func (m *GetApplicableLabelsResponse) GetAppliedLabels() []*Label {
	if m != nil {
		return m.AppliedLabels
	}
	return nil
}

type PricingLevel struct {
	Level                int32    `protobuf:"varint,1,opt,name=level,proto3" json:"level"`
	LevelName            string   `protobuf:"bytes,2,opt,name=level_name,json=levelName,proto3" json:"level_name"`
	Price                int32    `protobuf:"varint,3,opt,name=price,proto3" json:"price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PricingLevel) Reset()         { *m = PricingLevel{} }
func (m *PricingLevel) String() string { return proto.CompactTextString(m) }
func (*PricingLevel) ProtoMessage()    {}
func (*PricingLevel) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{98}
}
func (m *PricingLevel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PricingLevel.Unmarshal(m, b)
}
func (m *PricingLevel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PricingLevel.Marshal(b, m, deterministic)
}
func (dst *PricingLevel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PricingLevel.Merge(dst, src)
}
func (m *PricingLevel) XXX_Size() int {
	return xxx_messageInfo_PricingLevel.Size(m)
}
func (m *PricingLevel) XXX_DiscardUnknown() {
	xxx_messageInfo_PricingLevel.DiscardUnknown(m)
}

var xxx_messageInfo_PricingLevel proto.InternalMessageInfo

func (m *PricingLevel) GetLevel() int32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *PricingLevel) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *PricingLevel) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

type Label struct {
	LabelId               uint32    `protobuf:"varint,1,opt,name=label_id,json=labelId,proto3" json:"label_id"`
	LabelName             string    `protobuf:"bytes,2,opt,name=label_name,json=labelName,proto3" json:"label_name"`
	LabelIcon             string    `protobuf:"bytes,3,opt,name=label_icon,json=labelIcon,proto3" json:"label_icon"`
	AdditionalPrice       int32     `protobuf:"varint,4,opt,name=additional_price,json=additionalPrice,proto3" json:"additional_price"`
	LabelDescription      string    `protobuf:"bytes,5,opt,name=label_description,json=labelDescription,proto3" json:"label_description"`
	LabelType             LabelType `protobuf:"varint,6,opt,name=label_type,json=labelType,proto3,enum=esport_http.LabelType" json:"label_type"`
	ApplyLevel            int32     `protobuf:"varint,7,opt,name=apply_level,json=applyLevel,proto3" json:"apply_level"`
	IsApplied             bool      `protobuf:"varint,8,opt,name=is_applied,json=isApplied,proto3" json:"is_applied"`
	ApplyEntry            string    `protobuf:"bytes,9,opt,name=apply_entry,json=applyEntry,proto3" json:"apply_entry"`
	IsMetCondition        bool      `protobuf:"varint,10,opt,name=is_met_condition,json=isMetCondition,proto3" json:"is_met_condition"`
	GameName              string    `protobuf:"bytes,11,opt,name=game_name,json=gameName,proto3" json:"game_name"`
	Requirement           string    `protobuf:"bytes,12,opt,name=requirement,proto3" json:"requirement"`
	PriceAdditionalSwitch bool      `protobuf:"varint,13,opt,name=price_additional_switch,json=priceAdditionalSwitch,proto3" json:"price_additional_switch"`
	XXX_NoUnkeyedLiteral  struct{}  `json:"-"`
	XXX_unrecognized      []byte    `json:"-"`
	XXX_sizecache         int32     `json:"-"`
}

func (m *Label) Reset()         { *m = Label{} }
func (m *Label) String() string { return proto.CompactTextString(m) }
func (*Label) ProtoMessage()    {}
func (*Label) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{99}
}
func (m *Label) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Label.Unmarshal(m, b)
}
func (m *Label) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Label.Marshal(b, m, deterministic)
}
func (dst *Label) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Label.Merge(dst, src)
}
func (m *Label) XXX_Size() int {
	return xxx_messageInfo_Label.Size(m)
}
func (m *Label) XXX_DiscardUnknown() {
	xxx_messageInfo_Label.DiscardUnknown(m)
}

var xxx_messageInfo_Label proto.InternalMessageInfo

func (m *Label) GetLabelId() uint32 {
	if m != nil {
		return m.LabelId
	}
	return 0
}

func (m *Label) GetLabelName() string {
	if m != nil {
		return m.LabelName
	}
	return ""
}

func (m *Label) GetLabelIcon() string {
	if m != nil {
		return m.LabelIcon
	}
	return ""
}

func (m *Label) GetAdditionalPrice() int32 {
	if m != nil {
		return m.AdditionalPrice
	}
	return 0
}

func (m *Label) GetLabelDescription() string {
	if m != nil {
		return m.LabelDescription
	}
	return ""
}

func (m *Label) GetLabelType() LabelType {
	if m != nil {
		return m.LabelType
	}
	return LabelType_LABEL_TYPE_UNSPECIFIED
}

func (m *Label) GetApplyLevel() int32 {
	if m != nil {
		return m.ApplyLevel
	}
	return 0
}

func (m *Label) GetIsApplied() bool {
	if m != nil {
		return m.IsApplied
	}
	return false
}

func (m *Label) GetApplyEntry() string {
	if m != nil {
		return m.ApplyEntry
	}
	return ""
}

func (m *Label) GetIsMetCondition() bool {
	if m != nil {
		return m.IsMetCondition
	}
	return false
}

func (m *Label) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *Label) GetRequirement() string {
	if m != nil {
		return m.Requirement
	}
	return ""
}

func (m *Label) GetPriceAdditionalSwitch() bool {
	if m != nil {
		return m.PriceAdditionalSwitch
	}
	return false
}

// 设置标识加成价格开关
// uri: /set_label_price_switch
type SetLabelPriceSwitchRequest struct {
	LabelId              uint32   `protobuf:"varint,1,opt,name=label_id,json=labelId,proto3" json:"label_id"`
	TargetState          bool     `protobuf:"varint,2,opt,name=target_state,json=targetState,proto3" json:"target_state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetLabelPriceSwitchRequest) Reset()         { *m = SetLabelPriceSwitchRequest{} }
func (m *SetLabelPriceSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*SetLabelPriceSwitchRequest) ProtoMessage()    {}
func (*SetLabelPriceSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{100}
}
func (m *SetLabelPriceSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetLabelPriceSwitchRequest.Unmarshal(m, b)
}
func (m *SetLabelPriceSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetLabelPriceSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *SetLabelPriceSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetLabelPriceSwitchRequest.Merge(dst, src)
}
func (m *SetLabelPriceSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_SetLabelPriceSwitchRequest.Size(m)
}
func (m *SetLabelPriceSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetLabelPriceSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetLabelPriceSwitchRequest proto.InternalMessageInfo

func (m *SetLabelPriceSwitchRequest) GetLabelId() uint32 {
	if m != nil {
		return m.LabelId
	}
	return 0
}

func (m *SetLabelPriceSwitchRequest) GetTargetState() bool {
	if m != nil {
		return m.TargetState
	}
	return false
}

type SetLabelPriceSwitchResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetLabelPriceSwitchResponse) Reset()         { *m = SetLabelPriceSwitchResponse{} }
func (m *SetLabelPriceSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*SetLabelPriceSwitchResponse) ProtoMessage()    {}
func (*SetLabelPriceSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{101}
}
func (m *SetLabelPriceSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetLabelPriceSwitchResponse.Unmarshal(m, b)
}
func (m *SetLabelPriceSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetLabelPriceSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *SetLabelPriceSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetLabelPriceSwitchResponse.Merge(dst, src)
}
func (m *SetLabelPriceSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_SetLabelPriceSwitchResponse.Size(m)
}
func (m *SetLabelPriceSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetLabelPriceSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetLabelPriceSwitchResponse proto.InternalMessageInfo

// 权益
type GodPrivilege struct {
	PrivilegeId          uint32   `protobuf:"varint,1,opt,name=privilege_id,json=privilegeId,proto3" json:"privilege_id"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc"`
	Url                  string   `protobuf:"bytes,4,opt,name=url,proto3" json:"url"`
	Locked               bool     `protobuf:"varint,5,opt,name=locked,proto3" json:"locked"`
	UnlockLv             uint32   `protobuf:"varint,6,opt,name=unlock_lv,json=unlockLv,proto3" json:"unlock_lv"`
	BigUrl               string   `protobuf:"bytes,7,opt,name=big_url,json=bigUrl,proto3" json:"big_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GodPrivilege) Reset()         { *m = GodPrivilege{} }
func (m *GodPrivilege) String() string { return proto.CompactTextString(m) }
func (*GodPrivilege) ProtoMessage()    {}
func (*GodPrivilege) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{102}
}
func (m *GodPrivilege) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GodPrivilege.Unmarshal(m, b)
}
func (m *GodPrivilege) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GodPrivilege.Marshal(b, m, deterministic)
}
func (dst *GodPrivilege) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GodPrivilege.Merge(dst, src)
}
func (m *GodPrivilege) XXX_Size() int {
	return xxx_messageInfo_GodPrivilege.Size(m)
}
func (m *GodPrivilege) XXX_DiscardUnknown() {
	xxx_messageInfo_GodPrivilege.DiscardUnknown(m)
}

var xxx_messageInfo_GodPrivilege proto.InternalMessageInfo

func (m *GodPrivilege) GetPrivilegeId() uint32 {
	if m != nil {
		return m.PrivilegeId
	}
	return 0
}

func (m *GodPrivilege) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GodPrivilege) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *GodPrivilege) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *GodPrivilege) GetLocked() bool {
	if m != nil {
		return m.Locked
	}
	return false
}

func (m *GodPrivilege) GetUnlockLv() uint32 {
	if m != nil {
		return m.UnlockLv
	}
	return 0
}

func (m *GodPrivilege) GetBigUrl() string {
	if m != nil {
		return m.BigUrl
	}
	return ""
}

// 当前指标
type GodLevelKpi struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	Score                string   `protobuf:"bytes,2,opt,name=score,proto3" json:"score"`
	UpgradeDesc          string   `protobuf:"bytes,3,opt,name=upgrade_desc,json=upgradeDesc,proto3" json:"upgrade_desc"`
	Progress             string   `protobuf:"bytes,4,opt,name=progress,proto3" json:"progress"`
	Finished             bool     `protobuf:"varint,5,opt,name=finished,proto3" json:"finished"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GodLevelKpi) Reset()         { *m = GodLevelKpi{} }
func (m *GodLevelKpi) String() string { return proto.CompactTextString(m) }
func (*GodLevelKpi) ProtoMessage()    {}
func (*GodLevelKpi) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{103}
}
func (m *GodLevelKpi) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GodLevelKpi.Unmarshal(m, b)
}
func (m *GodLevelKpi) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GodLevelKpi.Marshal(b, m, deterministic)
}
func (dst *GodLevelKpi) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GodLevelKpi.Merge(dst, src)
}
func (m *GodLevelKpi) XXX_Size() int {
	return xxx_messageInfo_GodLevelKpi.Size(m)
}
func (m *GodLevelKpi) XXX_DiscardUnknown() {
	xxx_messageInfo_GodLevelKpi.DiscardUnknown(m)
}

var xxx_messageInfo_GodLevelKpi proto.InternalMessageInfo

func (m *GodLevelKpi) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GodLevelKpi) GetScore() string {
	if m != nil {
		return m.Score
	}
	return ""
}

func (m *GodLevelKpi) GetUpgradeDesc() string {
	if m != nil {
		return m.UpgradeDesc
	}
	return ""
}

func (m *GodLevelKpi) GetProgress() string {
	if m != nil {
		return m.Progress
	}
	return ""
}

func (m *GodLevelKpi) GetFinished() bool {
	if m != nil {
		return m.Finished
	}
	return false
}

// 等级
type GodLevel struct {
	Level                uint32          `protobuf:"varint,1,opt,name=level,proto3" json:"level"`
	Name                 string          `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Url                  string          `protobuf:"bytes,3,opt,name=url,proto3" json:"url"`
	PrivilegeList        []*GodPrivilege `protobuf:"bytes,4,rep,name=privilege_list,json=privilegeList,proto3" json:"privilege_list"`
	KpiList              []*GodLevelKpi  `protobuf:"bytes,5,rep,name=kpi_list,json=kpiList,proto3" json:"kpi_list"`
	LockedLvIcon         string          `protobuf:"bytes,6,opt,name=locked_lv_icon,json=lockedLvIcon,proto3" json:"locked_lv_icon"`
	CurLvIcon            string          `protobuf:"bytes,7,opt,name=cur_lv_icon,json=curLvIcon,proto3" json:"cur_lv_icon"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GodLevel) Reset()         { *m = GodLevel{} }
func (m *GodLevel) String() string { return proto.CompactTextString(m) }
func (*GodLevel) ProtoMessage()    {}
func (*GodLevel) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{104}
}
func (m *GodLevel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GodLevel.Unmarshal(m, b)
}
func (m *GodLevel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GodLevel.Marshal(b, m, deterministic)
}
func (dst *GodLevel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GodLevel.Merge(dst, src)
}
func (m *GodLevel) XXX_Size() int {
	return xxx_messageInfo_GodLevel.Size(m)
}
func (m *GodLevel) XXX_DiscardUnknown() {
	xxx_messageInfo_GodLevel.DiscardUnknown(m)
}

var xxx_messageInfo_GodLevel proto.InternalMessageInfo

func (m *GodLevel) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GodLevel) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GodLevel) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *GodLevel) GetPrivilegeList() []*GodPrivilege {
	if m != nil {
		return m.PrivilegeList
	}
	return nil
}

func (m *GodLevel) GetKpiList() []*GodLevelKpi {
	if m != nil {
		return m.KpiList
	}
	return nil
}

func (m *GodLevel) GetLockedLvIcon() string {
	if m != nil {
		return m.LockedLvIcon
	}
	return ""
}

func (m *GodLevel) GetCurLvIcon() string {
	if m != nil {
		return m.CurLvIcon
	}
	return ""
}

// 获取用户大神等级
type GetGodLevelRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGodLevelRequest) Reset()         { *m = GetGodLevelRequest{} }
func (m *GetGodLevelRequest) String() string { return proto.CompactTextString(m) }
func (*GetGodLevelRequest) ProtoMessage()    {}
func (*GetGodLevelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{105}
}
func (m *GetGodLevelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGodLevelRequest.Unmarshal(m, b)
}
func (m *GetGodLevelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGodLevelRequest.Marshal(b, m, deterministic)
}
func (dst *GetGodLevelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGodLevelRequest.Merge(dst, src)
}
func (m *GetGodLevelRequest) XXX_Size() int {
	return xxx_messageInfo_GetGodLevelRequest.Size(m)
}
func (m *GetGodLevelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGodLevelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGodLevelRequest proto.InternalMessageInfo

type GetGodLevelResponse struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level"`
	IconUrl              string   `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url"`
	NoShow               bool     `protobuf:"varint,3,opt,name=no_show,json=noShow,proto3" json:"no_show"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGodLevelResponse) Reset()         { *m = GetGodLevelResponse{} }
func (m *GetGodLevelResponse) String() string { return proto.CompactTextString(m) }
func (*GetGodLevelResponse) ProtoMessage()    {}
func (*GetGodLevelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{106}
}
func (m *GetGodLevelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGodLevelResponse.Unmarshal(m, b)
}
func (m *GetGodLevelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGodLevelResponse.Marshal(b, m, deterministic)
}
func (dst *GetGodLevelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGodLevelResponse.Merge(dst, src)
}
func (m *GetGodLevelResponse) XXX_Size() int {
	return xxx_messageInfo_GetGodLevelResponse.Size(m)
}
func (m *GetGodLevelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGodLevelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGodLevelResponse proto.InternalMessageInfo

func (m *GetGodLevelResponse) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GetGodLevelResponse) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *GetGodLevelResponse) GetNoShow() bool {
	if m != nil {
		return m.NoShow
	}
	return false
}

// 获取用户大神等级详情
type GetGodLevelDetailRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGodLevelDetailRequest) Reset()         { *m = GetGodLevelDetailRequest{} }
func (m *GetGodLevelDetailRequest) String() string { return proto.CompactTextString(m) }
func (*GetGodLevelDetailRequest) ProtoMessage()    {}
func (*GetGodLevelDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{107}
}
func (m *GetGodLevelDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGodLevelDetailRequest.Unmarshal(m, b)
}
func (m *GetGodLevelDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGodLevelDetailRequest.Marshal(b, m, deterministic)
}
func (dst *GetGodLevelDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGodLevelDetailRequest.Merge(dst, src)
}
func (m *GetGodLevelDetailRequest) XXX_Size() int {
	return xxx_messageInfo_GetGodLevelDetailRequest.Size(m)
}
func (m *GetGodLevelDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGodLevelDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGodLevelDetailRequest proto.InternalMessageInfo

type GetGodLevelDetailResponse struct {
	AllLevelInfo         []*GodLevel `protobuf:"bytes,1,rep,name=all_level_info,json=allLevelInfo,proto3" json:"all_level_info"`
	Level                uint32      `protobuf:"varint,2,opt,name=level,proto3" json:"level"`
	UpgradeDesc          string      `protobuf:"bytes,3,opt,name=upgrade_desc,json=upgradeDesc,proto3" json:"upgrade_desc"`
	KeepDesc             string      `protobuf:"bytes,4,opt,name=keep_desc,json=keepDesc,proto3" json:"keep_desc"`
	UpgradeDescV2        string      `protobuf:"bytes,5,opt,name=upgrade_desc_v2,json=upgradeDescV2,proto3" json:"upgrade_desc_v2"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetGodLevelDetailResponse) Reset()         { *m = GetGodLevelDetailResponse{} }
func (m *GetGodLevelDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GetGodLevelDetailResponse) ProtoMessage()    {}
func (*GetGodLevelDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{108}
}
func (m *GetGodLevelDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGodLevelDetailResponse.Unmarshal(m, b)
}
func (m *GetGodLevelDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGodLevelDetailResponse.Marshal(b, m, deterministic)
}
func (dst *GetGodLevelDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGodLevelDetailResponse.Merge(dst, src)
}
func (m *GetGodLevelDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetGodLevelDetailResponse.Size(m)
}
func (m *GetGodLevelDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGodLevelDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGodLevelDetailResponse proto.InternalMessageInfo

func (m *GetGodLevelDetailResponse) GetAllLevelInfo() []*GodLevel {
	if m != nil {
		return m.AllLevelInfo
	}
	return nil
}

func (m *GetGodLevelDetailResponse) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GetGodLevelDetailResponse) GetUpgradeDesc() string {
	if m != nil {
		return m.UpgradeDesc
	}
	return ""
}

func (m *GetGodLevelDetailResponse) GetKeepDesc() string {
	if m != nil {
		return m.KeepDesc
	}
	return ""
}

func (m *GetGodLevelDetailResponse) GetUpgradeDescV2() string {
	if m != nil {
		return m.UpgradeDescV2
	}
	return ""
}

// 检查是否有急速退款的权限
type CheckFastRefundPermissionRequest struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckFastRefundPermissionRequest) Reset()         { *m = CheckFastRefundPermissionRequest{} }
func (m *CheckFastRefundPermissionRequest) String() string { return proto.CompactTextString(m) }
func (*CheckFastRefundPermissionRequest) ProtoMessage()    {}
func (*CheckFastRefundPermissionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{109}
}
func (m *CheckFastRefundPermissionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckFastRefundPermissionRequest.Unmarshal(m, b)
}
func (m *CheckFastRefundPermissionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckFastRefundPermissionRequest.Marshal(b, m, deterministic)
}
func (dst *CheckFastRefundPermissionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckFastRefundPermissionRequest.Merge(dst, src)
}
func (m *CheckFastRefundPermissionRequest) XXX_Size() int {
	return xxx_messageInfo_CheckFastRefundPermissionRequest.Size(m)
}
func (m *CheckFastRefundPermissionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckFastRefundPermissionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckFastRefundPermissionRequest proto.InternalMessageInfo

func (m *CheckFastRefundPermissionRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type CheckFastRefundPermissionResponse struct {
	Has                  bool     `protobuf:"varint,1,opt,name=has,proto3" json:"has"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckFastRefundPermissionResponse) Reset()         { *m = CheckFastRefundPermissionResponse{} }
func (m *CheckFastRefundPermissionResponse) String() string { return proto.CompactTextString(m) }
func (*CheckFastRefundPermissionResponse) ProtoMessage()    {}
func (*CheckFastRefundPermissionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{110}
}
func (m *CheckFastRefundPermissionResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckFastRefundPermissionResponse.Unmarshal(m, b)
}
func (m *CheckFastRefundPermissionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckFastRefundPermissionResponse.Marshal(b, m, deterministic)
}
func (dst *CheckFastRefundPermissionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckFastRefundPermissionResponse.Merge(dst, src)
}
func (m *CheckFastRefundPermissionResponse) XXX_Size() int {
	return xxx_messageInfo_CheckFastRefundPermissionResponse.Size(m)
}
func (m *CheckFastRefundPermissionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckFastRefundPermissionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckFastRefundPermissionResponse proto.InternalMessageInfo

func (m *CheckFastRefundPermissionResponse) GetHas() bool {
	if m != nil {
		return m.Has
	}
	return false
}

// 用户创建退款请求的消息
type CreateFastRefundRequest struct {
	OrderId              string     `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	Quantity             uint32     `protobuf:"varint,2,opt,name=quantity,proto3" json:"quantity"`
	Reason               string     `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason"`
	Description          string     `protobuf:"bytes,4,opt,name=description,proto3" json:"description"`
	Type                 RefundType `protobuf:"varint,5,opt,name=type,proto3,enum=esport_http.RefundType" json:"type"`
	DeviceId             string     `protobuf:"bytes,6,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CreateFastRefundRequest) Reset()         { *m = CreateFastRefundRequest{} }
func (m *CreateFastRefundRequest) String() string { return proto.CompactTextString(m) }
func (*CreateFastRefundRequest) ProtoMessage()    {}
func (*CreateFastRefundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{111}
}
func (m *CreateFastRefundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateFastRefundRequest.Unmarshal(m, b)
}
func (m *CreateFastRefundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateFastRefundRequest.Marshal(b, m, deterministic)
}
func (dst *CreateFastRefundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateFastRefundRequest.Merge(dst, src)
}
func (m *CreateFastRefundRequest) XXX_Size() int {
	return xxx_messageInfo_CreateFastRefundRequest.Size(m)
}
func (m *CreateFastRefundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateFastRefundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateFastRefundRequest proto.InternalMessageInfo

func (m *CreateFastRefundRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *CreateFastRefundRequest) GetQuantity() uint32 {
	if m != nil {
		return m.Quantity
	}
	return 0
}

func (m *CreateFastRefundRequest) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *CreateFastRefundRequest) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *CreateFastRefundRequest) GetType() RefundType {
	if m != nil {
		return m.Type
	}
	return RefundType_REFUND_TYPE_FULL
}

func (m *CreateFastRefundRequest) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

// 设置游戏包赢开关
type SetGameGuaranteeStatusRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	IsGuaranteeWin       bool     `protobuf:"varint,2,opt,name=is_guarantee_win,json=isGuaranteeWin,proto3" json:"is_guarantee_win"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGameGuaranteeStatusRequest) Reset()         { *m = SetGameGuaranteeStatusRequest{} }
func (m *SetGameGuaranteeStatusRequest) String() string { return proto.CompactTextString(m) }
func (*SetGameGuaranteeStatusRequest) ProtoMessage()    {}
func (*SetGameGuaranteeStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{112}
}
func (m *SetGameGuaranteeStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameGuaranteeStatusRequest.Unmarshal(m, b)
}
func (m *SetGameGuaranteeStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameGuaranteeStatusRequest.Marshal(b, m, deterministic)
}
func (dst *SetGameGuaranteeStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameGuaranteeStatusRequest.Merge(dst, src)
}
func (m *SetGameGuaranteeStatusRequest) XXX_Size() int {
	return xxx_messageInfo_SetGameGuaranteeStatusRequest.Size(m)
}
func (m *SetGameGuaranteeStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameGuaranteeStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameGuaranteeStatusRequest proto.InternalMessageInfo

func (m *SetGameGuaranteeStatusRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetGameGuaranteeStatusRequest) GetIsGuaranteeWin() bool {
	if m != nil {
		return m.IsGuaranteeWin
	}
	return false
}

type SetGameGuaranteeStatusResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetGameGuaranteeStatusResponse) Reset()         { *m = SetGameGuaranteeStatusResponse{} }
func (m *SetGameGuaranteeStatusResponse) String() string { return proto.CompactTextString(m) }
func (*SetGameGuaranteeStatusResponse) ProtoMessage()    {}
func (*SetGameGuaranteeStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{113}
}
func (m *SetGameGuaranteeStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameGuaranteeStatusResponse.Unmarshal(m, b)
}
func (m *SetGameGuaranteeStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameGuaranteeStatusResponse.Marshal(b, m, deterministic)
}
func (dst *SetGameGuaranteeStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameGuaranteeStatusResponse.Merge(dst, src)
}
func (m *SetGameGuaranteeStatusResponse) XXX_Size() int {
	return xxx_messageInfo_SetGameGuaranteeStatusResponse.Size(m)
}
func (m *SetGameGuaranteeStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameGuaranteeStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameGuaranteeStatusResponse proto.InternalMessageInfo

// ************ 大神侧 谁看过我功能begin  ***********
// uri: /visit/report_visit
// 用户访问上报
type ReportGameCardVisitRequest struct {
	CoachId              uint32   `protobuf:"varint,1,opt,name=coach_id,json=coachId,proto3" json:"coach_id"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportGameCardVisitRequest) Reset()         { *m = ReportGameCardVisitRequest{} }
func (m *ReportGameCardVisitRequest) String() string { return proto.CompactTextString(m) }
func (*ReportGameCardVisitRequest) ProtoMessage()    {}
func (*ReportGameCardVisitRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{114}
}
func (m *ReportGameCardVisitRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportGameCardVisitRequest.Unmarshal(m, b)
}
func (m *ReportGameCardVisitRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportGameCardVisitRequest.Marshal(b, m, deterministic)
}
func (dst *ReportGameCardVisitRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportGameCardVisitRequest.Merge(dst, src)
}
func (m *ReportGameCardVisitRequest) XXX_Size() int {
	return xxx_messageInfo_ReportGameCardVisitRequest.Size(m)
}
func (m *ReportGameCardVisitRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportGameCardVisitRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReportGameCardVisitRequest proto.InternalMessageInfo

func (m *ReportGameCardVisitRequest) GetCoachId() uint32 {
	if m != nil {
		return m.CoachId
	}
	return 0
}

func (m *ReportGameCardVisitRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type ReportGameCardVisitResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportGameCardVisitResponse) Reset()         { *m = ReportGameCardVisitResponse{} }
func (m *ReportGameCardVisitResponse) String() string { return proto.CompactTextString(m) }
func (*ReportGameCardVisitResponse) ProtoMessage()    {}
func (*ReportGameCardVisitResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{115}
}
func (m *ReportGameCardVisitResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportGameCardVisitResponse.Unmarshal(m, b)
}
func (m *ReportGameCardVisitResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportGameCardVisitResponse.Marshal(b, m, deterministic)
}
func (dst *ReportGameCardVisitResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportGameCardVisitResponse.Merge(dst, src)
}
func (m *ReportGameCardVisitResponse) XXX_Size() int {
	return xxx_messageInfo_ReportGameCardVisitResponse.Size(m)
}
func (m *ReportGameCardVisitResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportGameCardVisitResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReportGameCardVisitResponse proto.InternalMessageInfo

// uri: /visit/get_visitor_cnt
// 大神获取被访问记录数
type GetBeVisitorRecordCountReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBeVisitorRecordCountReq) Reset()         { *m = GetBeVisitorRecordCountReq{} }
func (m *GetBeVisitorRecordCountReq) String() string { return proto.CompactTextString(m) }
func (*GetBeVisitorRecordCountReq) ProtoMessage()    {}
func (*GetBeVisitorRecordCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{116}
}
func (m *GetBeVisitorRecordCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBeVisitorRecordCountReq.Unmarshal(m, b)
}
func (m *GetBeVisitorRecordCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBeVisitorRecordCountReq.Marshal(b, m, deterministic)
}
func (dst *GetBeVisitorRecordCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBeVisitorRecordCountReq.Merge(dst, src)
}
func (m *GetBeVisitorRecordCountReq) XXX_Size() int {
	return xxx_messageInfo_GetBeVisitorRecordCountReq.Size(m)
}
func (m *GetBeVisitorRecordCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBeVisitorRecordCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBeVisitorRecordCountReq proto.InternalMessageInfo

type GetBeVisitorRecordCountResp struct {
	TotalUserCount       uint32   `protobuf:"varint,1,opt,name=total_user_count,json=totalUserCount,proto3" json:"total_user_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBeVisitorRecordCountResp) Reset()         { *m = GetBeVisitorRecordCountResp{} }
func (m *GetBeVisitorRecordCountResp) String() string { return proto.CompactTextString(m) }
func (*GetBeVisitorRecordCountResp) ProtoMessage()    {}
func (*GetBeVisitorRecordCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{117}
}
func (m *GetBeVisitorRecordCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBeVisitorRecordCountResp.Unmarshal(m, b)
}
func (m *GetBeVisitorRecordCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBeVisitorRecordCountResp.Marshal(b, m, deterministic)
}
func (dst *GetBeVisitorRecordCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBeVisitorRecordCountResp.Merge(dst, src)
}
func (m *GetBeVisitorRecordCountResp) XXX_Size() int {
	return xxx_messageInfo_GetBeVisitorRecordCountResp.Size(m)
}
func (m *GetBeVisitorRecordCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBeVisitorRecordCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBeVisitorRecordCountResp proto.InternalMessageInfo

func (m *GetBeVisitorRecordCountResp) GetTotalUserCount() uint32 {
	if m != nil {
		return m.TotalUserCount
	}
	return 0
}

// VisitRecord
type VisitRecord struct {
	UserInfo             *UserInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info"`
	EsportRole           uint32    `protobuf:"varint,2,opt,name=esport_role,json=esportRole,proto3" json:"esport_role"`
	IsPlaymate           bool      `protobuf:"varint,3,opt,name=is_playmate,json=isPlaymate,proto3" json:"is_playmate"`
	GameName             string    `protobuf:"bytes,4,opt,name=game_name,json=gameName,proto3" json:"game_name"`
	UpdateTime           int64     `protobuf:"varint,5,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	LastPlaceOrderTs     int64     `protobuf:"varint,6,opt,name=last_place_order_ts,json=lastPlaceOrderTs,proto3" json:"last_place_order_ts"`
	Cnt                  uint32    `protobuf:"varint,7,opt,name=cnt,proto3" json:"cnt"`
	IsOnline             bool      `protobuf:"varint,8,opt,name=is_online,json=isOnline,proto3" json:"is_online"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *VisitRecord) Reset()         { *m = VisitRecord{} }
func (m *VisitRecord) String() string { return proto.CompactTextString(m) }
func (*VisitRecord) ProtoMessage()    {}
func (*VisitRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{118}
}
func (m *VisitRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VisitRecord.Unmarshal(m, b)
}
func (m *VisitRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VisitRecord.Marshal(b, m, deterministic)
}
func (dst *VisitRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VisitRecord.Merge(dst, src)
}
func (m *VisitRecord) XXX_Size() int {
	return xxx_messageInfo_VisitRecord.Size(m)
}
func (m *VisitRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_VisitRecord.DiscardUnknown(m)
}

var xxx_messageInfo_VisitRecord proto.InternalMessageInfo

func (m *VisitRecord) GetUserInfo() *UserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *VisitRecord) GetEsportRole() uint32 {
	if m != nil {
		return m.EsportRole
	}
	return 0
}

func (m *VisitRecord) GetIsPlaymate() bool {
	if m != nil {
		return m.IsPlaymate
	}
	return false
}

func (m *VisitRecord) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *VisitRecord) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *VisitRecord) GetLastPlaceOrderTs() int64 {
	if m != nil {
		return m.LastPlaceOrderTs
	}
	return 0
}

func (m *VisitRecord) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

func (m *VisitRecord) GetIsOnline() bool {
	if m != nil {
		return m.IsOnline
	}
	return false
}

// uri: /visit/get_visitor_record
// 大神获取被访问记录
type GetBeVisitorRecordListReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBeVisitorRecordListReq) Reset()         { *m = GetBeVisitorRecordListReq{} }
func (m *GetBeVisitorRecordListReq) String() string { return proto.CompactTextString(m) }
func (*GetBeVisitorRecordListReq) ProtoMessage()    {}
func (*GetBeVisitorRecordListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{119}
}
func (m *GetBeVisitorRecordListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBeVisitorRecordListReq.Unmarshal(m, b)
}
func (m *GetBeVisitorRecordListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBeVisitorRecordListReq.Marshal(b, m, deterministic)
}
func (dst *GetBeVisitorRecordListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBeVisitorRecordListReq.Merge(dst, src)
}
func (m *GetBeVisitorRecordListReq) XXX_Size() int {
	return xxx_messageInfo_GetBeVisitorRecordListReq.Size(m)
}
func (m *GetBeVisitorRecordListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBeVisitorRecordListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBeVisitorRecordListReq proto.InternalMessageInfo

func (m *GetBeVisitorRecordListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetBeVisitorRecordListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetBeVisitorRecordListResp struct {
	Records              []*VisitRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records"`
	NextOffset           uint32         `protobuf:"varint,2,opt,name=next_offset,json=nextOffset,proto3" json:"next_offset"`
	IsEnd                bool           `protobuf:"varint,3,opt,name=is_end,json=isEnd,proto3" json:"is_end"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetBeVisitorRecordListResp) Reset()         { *m = GetBeVisitorRecordListResp{} }
func (m *GetBeVisitorRecordListResp) String() string { return proto.CompactTextString(m) }
func (*GetBeVisitorRecordListResp) ProtoMessage()    {}
func (*GetBeVisitorRecordListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{120}
}
func (m *GetBeVisitorRecordListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBeVisitorRecordListResp.Unmarshal(m, b)
}
func (m *GetBeVisitorRecordListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBeVisitorRecordListResp.Marshal(b, m, deterministic)
}
func (dst *GetBeVisitorRecordListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBeVisitorRecordListResp.Merge(dst, src)
}
func (m *GetBeVisitorRecordListResp) XXX_Size() int {
	return xxx_messageInfo_GetBeVisitorRecordListResp.Size(m)
}
func (m *GetBeVisitorRecordListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBeVisitorRecordListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBeVisitorRecordListResp proto.InternalMessageInfo

func (m *GetBeVisitorRecordListResp) GetRecords() []*VisitRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

func (m *GetBeVisitorRecordListResp) GetNextOffset() uint32 {
	if m != nil {
		return m.NextOffset
	}
	return 0
}

func (m *GetBeVisitorRecordListResp) GetIsEnd() bool {
	if m != nil {
		return m.IsEnd
	}
	return false
}

// uri:/visit/report_enter_im
// 上报用户进入大神IM私聊页
type ReportEnterIMPageRequest struct {
	CoachId              uint32   `protobuf:"varint,1,opt,name=coach_id,json=coachId,proto3" json:"coach_id"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportEnterIMPageRequest) Reset()         { *m = ReportEnterIMPageRequest{} }
func (m *ReportEnterIMPageRequest) String() string { return proto.CompactTextString(m) }
func (*ReportEnterIMPageRequest) ProtoMessage()    {}
func (*ReportEnterIMPageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{121}
}
func (m *ReportEnterIMPageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportEnterIMPageRequest.Unmarshal(m, b)
}
func (m *ReportEnterIMPageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportEnterIMPageRequest.Marshal(b, m, deterministic)
}
func (dst *ReportEnterIMPageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportEnterIMPageRequest.Merge(dst, src)
}
func (m *ReportEnterIMPageRequest) XXX_Size() int {
	return xxx_messageInfo_ReportEnterIMPageRequest.Size(m)
}
func (m *ReportEnterIMPageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportEnterIMPageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReportEnterIMPageRequest proto.InternalMessageInfo

func (m *ReportEnterIMPageRequest) GetCoachId() uint32 {
	if m != nil {
		return m.CoachId
	}
	return 0
}

func (m *ReportEnterIMPageRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type ReportEnterIMPageResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportEnterIMPageResponse) Reset()         { *m = ReportEnterIMPageResponse{} }
func (m *ReportEnterIMPageResponse) String() string { return proto.CompactTextString(m) }
func (*ReportEnterIMPageResponse) ProtoMessage()    {}
func (*ReportEnterIMPageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{122}
}
func (m *ReportEnterIMPageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportEnterIMPageResponse.Unmarshal(m, b)
}
func (m *ReportEnterIMPageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportEnterIMPageResponse.Marshal(b, m, deterministic)
}
func (dst *ReportEnterIMPageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportEnterIMPageResponse.Merge(dst, src)
}
func (m *ReportEnterIMPageResponse) XXX_Size() int {
	return xxx_messageInfo_ReportEnterIMPageResponse.Size(m)
}
func (m *ReportEnterIMPageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportEnterIMPageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReportEnterIMPageResponse proto.InternalMessageInfo

// 获取电竞助手推荐给用户的活动大神列表
// /tt-revenue-http-logic/esport/hall/getRecommandCoachList
type GetRecommandCoachListRequest struct {
	GameId               uint32                      `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	SeriesId             string                      `protobuf:"bytes,2,opt,name=series_id,json=seriesId,proto3" json:"series_id"`
	SceneType            uint32                      `protobuf:"varint,3,opt,name=scene_type,json=sceneType,proto3" json:"scene_type"`
	FilterOption         []*esport_hall.GameProperty `protobuf:"bytes,4,rep,name=filter_option,json=filterOption,proto3" json:"filter_option"`
	PageNum              uint32                      `protobuf:"varint,5,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	PageSize             uint32                      `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetRecommandCoachListRequest) Reset()         { *m = GetRecommandCoachListRequest{} }
func (m *GetRecommandCoachListRequest) String() string { return proto.CompactTextString(m) }
func (*GetRecommandCoachListRequest) ProtoMessage()    {}
func (*GetRecommandCoachListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{123}
}
func (m *GetRecommandCoachListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommandCoachListRequest.Unmarshal(m, b)
}
func (m *GetRecommandCoachListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommandCoachListRequest.Marshal(b, m, deterministic)
}
func (dst *GetRecommandCoachListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommandCoachListRequest.Merge(dst, src)
}
func (m *GetRecommandCoachListRequest) XXX_Size() int {
	return xxx_messageInfo_GetRecommandCoachListRequest.Size(m)
}
func (m *GetRecommandCoachListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommandCoachListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommandCoachListRequest proto.InternalMessageInfo

func (m *GetRecommandCoachListRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetRecommandCoachListRequest) GetSeriesId() string {
	if m != nil {
		return m.SeriesId
	}
	return ""
}

func (m *GetRecommandCoachListRequest) GetSceneType() uint32 {
	if m != nil {
		return m.SceneType
	}
	return 0
}

func (m *GetRecommandCoachListRequest) GetFilterOption() []*esport_hall.GameProperty {
	if m != nil {
		return m.FilterOption
	}
	return nil
}

func (m *GetRecommandCoachListRequest) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *GetRecommandCoachListRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetRecommandCoachListResponse struct {
	CoachList            []*EsportAreaCoachInfo `protobuf:"bytes,1,rep,name=coach_list,json=coachList,proto3" json:"coach_list"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetRecommandCoachListResponse) Reset()         { *m = GetRecommandCoachListResponse{} }
func (m *GetRecommandCoachListResponse) String() string { return proto.CompactTextString(m) }
func (*GetRecommandCoachListResponse) ProtoMessage()    {}
func (*GetRecommandCoachListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{124}
}
func (m *GetRecommandCoachListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommandCoachListResponse.Unmarshal(m, b)
}
func (m *GetRecommandCoachListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommandCoachListResponse.Marshal(b, m, deterministic)
}
func (dst *GetRecommandCoachListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommandCoachListResponse.Merge(dst, src)
}
func (m *GetRecommandCoachListResponse) XXX_Size() int {
	return xxx_messageInfo_GetRecommandCoachListResponse.Size(m)
}
func (m *GetRecommandCoachListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommandCoachListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommandCoachListResponse proto.InternalMessageInfo

func (m *GetRecommandCoachListResponse) GetCoachList() []*EsportAreaCoachInfo {
	if m != nil {
		return m.CoachList
	}
	return nil
}

type EsportAreaCoachInfo struct {
	UserProfile          *UserProfile           `protobuf:"bytes,1,opt,name=user_profile,json=userProfile,proto3" json:"user_profile"`
	TextDesc             string                 `protobuf:"bytes,2,opt,name=text_desc,json=textDesc,proto3" json:"text_desc"`
	GuaranteeWinText     string                 `protobuf:"bytes,3,opt,name=guarantee_win_text,json=guaranteeWinText,proto3" json:"guarantee_win_text"`
	IsFollowed           bool                   `protobuf:"varint,4,opt,name=is_followed,json=isFollowed,proto3" json:"is_followed"`
	Price                *ProductOrderPriceInfo `protobuf:"bytes,5,opt,name=price,proto3" json:"price"`
	IsFamousPlayer       bool                   `protobuf:"varint,6,opt,name=is_famous_player,json=isFamousPlayer,proto3" json:"is_famous_player"`
	VoiceDesc            string                 `protobuf:"bytes,7,opt,name=voice_desc,json=voiceDesc,proto3" json:"voice_desc"`
	Tone                 string                 `protobuf:"bytes,8,opt,name=tone,proto3" json:"tone"`
	StrategyId           uint32                 `protobuf:"varint,9,opt,name=strategy_id,json=strategyId,proto3" json:"strategy_id"`
	RecallSourceId       uint32                 `protobuf:"varint,10,opt,name=recall_source_id,json=recallSourceId,proto3" json:"recall_source_id"`
	FeatureLabelList     []string               `protobuf:"bytes,14,rep,name=feature_label_list,json=featureLabelList,proto3" json:"feature_label_list"`
	FeatureLabelImgList  []string               `protobuf:"bytes,17,rep,name=feature_label_img_list,json=featureLabelImgList,proto3" json:"feature_label_img_list"`
	SkillLabelList       []string               `protobuf:"bytes,15,rep,name=skill_label_list,json=skillLabelList,proto3" json:"skill_label_list"`
	Rank                 string                 `protobuf:"bytes,16,opt,name=rank,proto3" json:"rank"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *EsportAreaCoachInfo) Reset()         { *m = EsportAreaCoachInfo{} }
func (m *EsportAreaCoachInfo) String() string { return proto.CompactTextString(m) }
func (*EsportAreaCoachInfo) ProtoMessage()    {}
func (*EsportAreaCoachInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{125}
}
func (m *EsportAreaCoachInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportAreaCoachInfo.Unmarshal(m, b)
}
func (m *EsportAreaCoachInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportAreaCoachInfo.Marshal(b, m, deterministic)
}
func (dst *EsportAreaCoachInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportAreaCoachInfo.Merge(dst, src)
}
func (m *EsportAreaCoachInfo) XXX_Size() int {
	return xxx_messageInfo_EsportAreaCoachInfo.Size(m)
}
func (m *EsportAreaCoachInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportAreaCoachInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EsportAreaCoachInfo proto.InternalMessageInfo

func (m *EsportAreaCoachInfo) GetUserProfile() *UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *EsportAreaCoachInfo) GetTextDesc() string {
	if m != nil {
		return m.TextDesc
	}
	return ""
}

func (m *EsportAreaCoachInfo) GetGuaranteeWinText() string {
	if m != nil {
		return m.GuaranteeWinText
	}
	return ""
}

func (m *EsportAreaCoachInfo) GetIsFollowed() bool {
	if m != nil {
		return m.IsFollowed
	}
	return false
}

func (m *EsportAreaCoachInfo) GetPrice() *ProductOrderPriceInfo {
	if m != nil {
		return m.Price
	}
	return nil
}

func (m *EsportAreaCoachInfo) GetIsFamousPlayer() bool {
	if m != nil {
		return m.IsFamousPlayer
	}
	return false
}

func (m *EsportAreaCoachInfo) GetVoiceDesc() string {
	if m != nil {
		return m.VoiceDesc
	}
	return ""
}

func (m *EsportAreaCoachInfo) GetTone() string {
	if m != nil {
		return m.Tone
	}
	return ""
}

func (m *EsportAreaCoachInfo) GetStrategyId() uint32 {
	if m != nil {
		return m.StrategyId
	}
	return 0
}

func (m *EsportAreaCoachInfo) GetRecallSourceId() uint32 {
	if m != nil {
		return m.RecallSourceId
	}
	return 0
}

func (m *EsportAreaCoachInfo) GetFeatureLabelList() []string {
	if m != nil {
		return m.FeatureLabelList
	}
	return nil
}

func (m *EsportAreaCoachInfo) GetFeatureLabelImgList() []string {
	if m != nil {
		return m.FeatureLabelImgList
	}
	return nil
}

func (m *EsportAreaCoachInfo) GetSkillLabelList() []string {
	if m != nil {
		return m.SkillLabelList
	}
	return nil
}

func (m *EsportAreaCoachInfo) GetRank() string {
	if m != nil {
		return m.Rank
	}
	return ""
}

type UserProfile struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname"`
	AccountAlias         string   `protobuf:"bytes,4,opt,name=account_alias,json=accountAlias,proto3" json:"account_alias"`
	Sex                  uint32   `protobuf:"varint,5,opt,name=sex,proto3" json:"sex"`
	HeadImgMd5           string   `protobuf:"bytes,6,opt,name=head_img_md5,json=headImgMd5,proto3" json:"head_img_md5"`
	HeadDyImgMd5         string   `protobuf:"bytes,7,opt,name=head_dy_img_md5,json=headDyImgMd5,proto3" json:"head_dy_img_md5"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserProfile) Reset()         { *m = UserProfile{} }
func (m *UserProfile) String() string { return proto.CompactTextString(m) }
func (*UserProfile) ProtoMessage()    {}
func (*UserProfile) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{126}
}
func (m *UserProfile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserProfile.Unmarshal(m, b)
}
func (m *UserProfile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserProfile.Marshal(b, m, deterministic)
}
func (dst *UserProfile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserProfile.Merge(dst, src)
}
func (m *UserProfile) XXX_Size() int {
	return xxx_messageInfo_UserProfile.Size(m)
}
func (m *UserProfile) XXX_DiscardUnknown() {
	xxx_messageInfo_UserProfile.DiscardUnknown(m)
}

var xxx_messageInfo_UserProfile proto.InternalMessageInfo

func (m *UserProfile) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserProfile) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserProfile) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserProfile) GetAccountAlias() string {
	if m != nil {
		return m.AccountAlias
	}
	return ""
}

func (m *UserProfile) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserProfile) GetHeadImgMd5() string {
	if m != nil {
		return m.HeadImgMd5
	}
	return ""
}

func (m *UserProfile) GetHeadDyImgMd5() string {
	if m != nil {
		return m.HeadDyImgMd5
	}
	return ""
}

// uri: /get_first_round_discount_info
// 获取首局优惠信息
type GetFirstRoundDiscountInfoRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFirstRoundDiscountInfoRequest) Reset()         { *m = GetFirstRoundDiscountInfoRequest{} }
func (m *GetFirstRoundDiscountInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetFirstRoundDiscountInfoRequest) ProtoMessage()    {}
func (*GetFirstRoundDiscountInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{127}
}
func (m *GetFirstRoundDiscountInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFirstRoundDiscountInfoRequest.Unmarshal(m, b)
}
func (m *GetFirstRoundDiscountInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFirstRoundDiscountInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetFirstRoundDiscountInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFirstRoundDiscountInfoRequest.Merge(dst, src)
}
func (m *GetFirstRoundDiscountInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetFirstRoundDiscountInfoRequest.Size(m)
}
func (m *GetFirstRoundDiscountInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFirstRoundDiscountInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetFirstRoundDiscountInfoRequest proto.InternalMessageInfo

func (m *GetFirstRoundDiscountInfoRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetFirstRoundDiscountInfoResponse struct {
	ShowSwitch           bool     `protobuf:"varint,1,opt,name=show_switch,json=showSwitch,proto3" json:"show_switch"`
	FirstRoundPrice      uint32   `protobuf:"varint,2,opt,name=first_round_price,json=firstRoundPrice,proto3" json:"first_round_price"`
	IsOpen               bool     `protobuf:"varint,3,opt,name=is_open,json=isOpen,proto3" json:"is_open"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFirstRoundDiscountInfoResponse) Reset()         { *m = GetFirstRoundDiscountInfoResponse{} }
func (m *GetFirstRoundDiscountInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetFirstRoundDiscountInfoResponse) ProtoMessage()    {}
func (*GetFirstRoundDiscountInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{128}
}
func (m *GetFirstRoundDiscountInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFirstRoundDiscountInfoResponse.Unmarshal(m, b)
}
func (m *GetFirstRoundDiscountInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFirstRoundDiscountInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetFirstRoundDiscountInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFirstRoundDiscountInfoResponse.Merge(dst, src)
}
func (m *GetFirstRoundDiscountInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetFirstRoundDiscountInfoResponse.Size(m)
}
func (m *GetFirstRoundDiscountInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFirstRoundDiscountInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetFirstRoundDiscountInfoResponse proto.InternalMessageInfo

func (m *GetFirstRoundDiscountInfoResponse) GetShowSwitch() bool {
	if m != nil {
		return m.ShowSwitch
	}
	return false
}

func (m *GetFirstRoundDiscountInfoResponse) GetFirstRoundPrice() uint32 {
	if m != nil {
		return m.FirstRoundPrice
	}
	return 0
}

func (m *GetFirstRoundDiscountInfoResponse) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

// uri: /set_first_round_switch
// 设置首局优惠
type SetFirstRoundSwitchRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	IsOpen               bool     `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetFirstRoundSwitchRequest) Reset()         { *m = SetFirstRoundSwitchRequest{} }
func (m *SetFirstRoundSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*SetFirstRoundSwitchRequest) ProtoMessage()    {}
func (*SetFirstRoundSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{129}
}
func (m *SetFirstRoundSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetFirstRoundSwitchRequest.Unmarshal(m, b)
}
func (m *SetFirstRoundSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetFirstRoundSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *SetFirstRoundSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetFirstRoundSwitchRequest.Merge(dst, src)
}
func (m *SetFirstRoundSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_SetFirstRoundSwitchRequest.Size(m)
}
func (m *SetFirstRoundSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetFirstRoundSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetFirstRoundSwitchRequest proto.InternalMessageInfo

func (m *SetFirstRoundSwitchRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetFirstRoundSwitchRequest) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

type SetFirstRoundSwitchResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetFirstRoundSwitchResponse) Reset()         { *m = SetFirstRoundSwitchResponse{} }
func (m *SetFirstRoundSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*SetFirstRoundSwitchResponse) ProtoMessage()    {}
func (*SetFirstRoundSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{130}
}
func (m *SetFirstRoundSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetFirstRoundSwitchResponse.Unmarshal(m, b)
}
func (m *SetFirstRoundSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetFirstRoundSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *SetFirstRoundSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetFirstRoundSwitchResponse.Merge(dst, src)
}
func (m *SetFirstRoundSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_SetFirstRoundSwitchResponse.Size(m)
}
func (m *SetFirstRoundSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetFirstRoundSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetFirstRoundSwitchResponse proto.InternalMessageInfo

// uri: /get_first_round_discount_game_list
// 获取所有首局优惠游戏
type GetFirstRoundDiscountGameListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFirstRoundDiscountGameListRequest) Reset()         { *m = GetFirstRoundDiscountGameListRequest{} }
func (m *GetFirstRoundDiscountGameListRequest) String() string { return proto.CompactTextString(m) }
func (*GetFirstRoundDiscountGameListRequest) ProtoMessage()    {}
func (*GetFirstRoundDiscountGameListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{131}
}
func (m *GetFirstRoundDiscountGameListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFirstRoundDiscountGameListRequest.Unmarshal(m, b)
}
func (m *GetFirstRoundDiscountGameListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFirstRoundDiscountGameListRequest.Marshal(b, m, deterministic)
}
func (dst *GetFirstRoundDiscountGameListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFirstRoundDiscountGameListRequest.Merge(dst, src)
}
func (m *GetFirstRoundDiscountGameListRequest) XXX_Size() int {
	return xxx_messageInfo_GetFirstRoundDiscountGameListRequest.Size(m)
}
func (m *GetFirstRoundDiscountGameListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFirstRoundDiscountGameListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetFirstRoundDiscountGameListRequest proto.InternalMessageInfo

type FirstRoundDiscountGameInfo struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	GameName             string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name"`
	FirstRoundPrice      uint32   `protobuf:"varint,3,opt,name=first_round_price,json=firstRoundPrice,proto3" json:"first_round_price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FirstRoundDiscountGameInfo) Reset()         { *m = FirstRoundDiscountGameInfo{} }
func (m *FirstRoundDiscountGameInfo) String() string { return proto.CompactTextString(m) }
func (*FirstRoundDiscountGameInfo) ProtoMessage()    {}
func (*FirstRoundDiscountGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{132}
}
func (m *FirstRoundDiscountGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FirstRoundDiscountGameInfo.Unmarshal(m, b)
}
func (m *FirstRoundDiscountGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FirstRoundDiscountGameInfo.Marshal(b, m, deterministic)
}
func (dst *FirstRoundDiscountGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FirstRoundDiscountGameInfo.Merge(dst, src)
}
func (m *FirstRoundDiscountGameInfo) XXX_Size() int {
	return xxx_messageInfo_FirstRoundDiscountGameInfo.Size(m)
}
func (m *FirstRoundDiscountGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FirstRoundDiscountGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FirstRoundDiscountGameInfo proto.InternalMessageInfo

func (m *FirstRoundDiscountGameInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *FirstRoundDiscountGameInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *FirstRoundDiscountGameInfo) GetFirstRoundPrice() uint32 {
	if m != nil {
		return m.FirstRoundPrice
	}
	return 0
}

type GetFirstRoundDiscountGameListResponse struct {
	GameList             []*FirstRoundDiscountGameInfo `protobuf:"bytes,1,rep,name=game_list,json=gameList,proto3" json:"game_list"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetFirstRoundDiscountGameListResponse) Reset()         { *m = GetFirstRoundDiscountGameListResponse{} }
func (m *GetFirstRoundDiscountGameListResponse) String() string { return proto.CompactTextString(m) }
func (*GetFirstRoundDiscountGameListResponse) ProtoMessage()    {}
func (*GetFirstRoundDiscountGameListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{133}
}
func (m *GetFirstRoundDiscountGameListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFirstRoundDiscountGameListResponse.Unmarshal(m, b)
}
func (m *GetFirstRoundDiscountGameListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFirstRoundDiscountGameListResponse.Marshal(b, m, deterministic)
}
func (dst *GetFirstRoundDiscountGameListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFirstRoundDiscountGameListResponse.Merge(dst, src)
}
func (m *GetFirstRoundDiscountGameListResponse) XXX_Size() int {
	return xxx_messageInfo_GetFirstRoundDiscountGameListResponse.Size(m)
}
func (m *GetFirstRoundDiscountGameListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFirstRoundDiscountGameListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetFirstRoundDiscountGameListResponse proto.InternalMessageInfo

func (m *GetFirstRoundDiscountGameListResponse) GetGameList() []*FirstRoundDiscountGameInfo {
	if m != nil {
		return m.GameList
	}
	return nil
}

// uri: /refresh_discount_info
// 刷新优惠信息
type RefreshDiscountInfoRequest struct {
	CoachUid             uint32   `protobuf:"varint,1,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefreshDiscountInfoRequest) Reset()         { *m = RefreshDiscountInfoRequest{} }
func (m *RefreshDiscountInfoRequest) String() string { return proto.CompactTextString(m) }
func (*RefreshDiscountInfoRequest) ProtoMessage()    {}
func (*RefreshDiscountInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{134}
}
func (m *RefreshDiscountInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefreshDiscountInfoRequest.Unmarshal(m, b)
}
func (m *RefreshDiscountInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefreshDiscountInfoRequest.Marshal(b, m, deterministic)
}
func (dst *RefreshDiscountInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefreshDiscountInfoRequest.Merge(dst, src)
}
func (m *RefreshDiscountInfoRequest) XXX_Size() int {
	return xxx_messageInfo_RefreshDiscountInfoRequest.Size(m)
}
func (m *RefreshDiscountInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RefreshDiscountInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RefreshDiscountInfoRequest proto.InternalMessageInfo

func (m *RefreshDiscountInfoRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *RefreshDiscountInfoRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type RefreshDiscountInfoResponse struct {
	HasFirstRoundDiscount bool     `protobuf:"varint,1,opt,name=has_first_round_discount,json=hasFirstRoundDiscount,proto3" json:"has_first_round_discount"`
	FirstRoundPrice       uint32   `protobuf:"varint,2,opt,name=first_round_price,json=firstRoundPrice,proto3" json:"first_round_price"`
	HasDiscount           bool     `protobuf:"varint,3,opt,name=has_discount,json=hasDiscount,proto3" json:"has_discount"`
	DiscountPrice         uint32   `protobuf:"varint,4,opt,name=discount_price,json=discountPrice,proto3" json:"discount_price"`
	DiscountType          uint32   `protobuf:"varint,5,opt,name=discount_type,json=discountType,proto3" json:"discount_type"`
	DiscountDesc          string   `protobuf:"bytes,6,opt,name=discount_desc,json=discountDesc,proto3" json:"discount_desc"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *RefreshDiscountInfoResponse) Reset()         { *m = RefreshDiscountInfoResponse{} }
func (m *RefreshDiscountInfoResponse) String() string { return proto.CompactTextString(m) }
func (*RefreshDiscountInfoResponse) ProtoMessage()    {}
func (*RefreshDiscountInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{135}
}
func (m *RefreshDiscountInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefreshDiscountInfoResponse.Unmarshal(m, b)
}
func (m *RefreshDiscountInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefreshDiscountInfoResponse.Marshal(b, m, deterministic)
}
func (dst *RefreshDiscountInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefreshDiscountInfoResponse.Merge(dst, src)
}
func (m *RefreshDiscountInfoResponse) XXX_Size() int {
	return xxx_messageInfo_RefreshDiscountInfoResponse.Size(m)
}
func (m *RefreshDiscountInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RefreshDiscountInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RefreshDiscountInfoResponse proto.InternalMessageInfo

func (m *RefreshDiscountInfoResponse) GetHasFirstRoundDiscount() bool {
	if m != nil {
		return m.HasFirstRoundDiscount
	}
	return false
}

func (m *RefreshDiscountInfoResponse) GetFirstRoundPrice() uint32 {
	if m != nil {
		return m.FirstRoundPrice
	}
	return 0
}

func (m *RefreshDiscountInfoResponse) GetHasDiscount() bool {
	if m != nil {
		return m.HasDiscount
	}
	return false
}

func (m *RefreshDiscountInfoResponse) GetDiscountPrice() uint32 {
	if m != nil {
		return m.DiscountPrice
	}
	return 0
}

func (m *RefreshDiscountInfoResponse) GetDiscountType() uint32 {
	if m != nil {
		return m.DiscountType
	}
	return 0
}

func (m *RefreshDiscountInfoResponse) GetDiscountDesc() string {
	if m != nil {
		return m.DiscountDesc
	}
	return ""
}

// uri: /get_new_customer_discount_info
// 获取新客优惠信息
type GetNewCustomerDiscountInfoRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewCustomerDiscountInfoRequest) Reset()         { *m = GetNewCustomerDiscountInfoRequest{} }
func (m *GetNewCustomerDiscountInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetNewCustomerDiscountInfoRequest) ProtoMessage()    {}
func (*GetNewCustomerDiscountInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{136}
}
func (m *GetNewCustomerDiscountInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewCustomerDiscountInfoRequest.Unmarshal(m, b)
}
func (m *GetNewCustomerDiscountInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewCustomerDiscountInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetNewCustomerDiscountInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewCustomerDiscountInfoRequest.Merge(dst, src)
}
func (m *GetNewCustomerDiscountInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetNewCustomerDiscountInfoRequest.Size(m)
}
func (m *GetNewCustomerDiscountInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewCustomerDiscountInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewCustomerDiscountInfoRequest proto.InternalMessageInfo

func (m *GetNewCustomerDiscountInfoRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetNewCustomerDiscountInfoResponse struct {
	ShowSwitch           bool     `protobuf:"varint,1,opt,name=show_switch,json=showSwitch,proto3" json:"show_switch"`
	NewCustomerPrice     uint32   `protobuf:"varint,2,opt,name=new_customer_price,json=newCustomerPrice,proto3" json:"new_customer_price"`
	PlatBonusFee         uint32   `protobuf:"varint,3,opt,name=plat_bonus_fee,json=platBonusFee,proto3" json:"plat_bonus_fee"`
	IsOpen               bool     `protobuf:"varint,4,opt,name=is_open,json=isOpen,proto3" json:"is_open"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewCustomerDiscountInfoResponse) Reset()         { *m = GetNewCustomerDiscountInfoResponse{} }
func (m *GetNewCustomerDiscountInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetNewCustomerDiscountInfoResponse) ProtoMessage()    {}
func (*GetNewCustomerDiscountInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{137}
}
func (m *GetNewCustomerDiscountInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewCustomerDiscountInfoResponse.Unmarshal(m, b)
}
func (m *GetNewCustomerDiscountInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewCustomerDiscountInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetNewCustomerDiscountInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewCustomerDiscountInfoResponse.Merge(dst, src)
}
func (m *GetNewCustomerDiscountInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetNewCustomerDiscountInfoResponse.Size(m)
}
func (m *GetNewCustomerDiscountInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewCustomerDiscountInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewCustomerDiscountInfoResponse proto.InternalMessageInfo

func (m *GetNewCustomerDiscountInfoResponse) GetShowSwitch() bool {
	if m != nil {
		return m.ShowSwitch
	}
	return false
}

func (m *GetNewCustomerDiscountInfoResponse) GetNewCustomerPrice() uint32 {
	if m != nil {
		return m.NewCustomerPrice
	}
	return 0
}

func (m *GetNewCustomerDiscountInfoResponse) GetPlatBonusFee() uint32 {
	if m != nil {
		return m.PlatBonusFee
	}
	return 0
}

func (m *GetNewCustomerDiscountInfoResponse) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

// uri: /set_new_customer_switch
// 设置新客价
type SetNewCustomerSwitchRequest struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	IsOpen               bool     `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetNewCustomerSwitchRequest) Reset()         { *m = SetNewCustomerSwitchRequest{} }
func (m *SetNewCustomerSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*SetNewCustomerSwitchRequest) ProtoMessage()    {}
func (*SetNewCustomerSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{138}
}
func (m *SetNewCustomerSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetNewCustomerSwitchRequest.Unmarshal(m, b)
}
func (m *SetNewCustomerSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetNewCustomerSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *SetNewCustomerSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetNewCustomerSwitchRequest.Merge(dst, src)
}
func (m *SetNewCustomerSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_SetNewCustomerSwitchRequest.Size(m)
}
func (m *SetNewCustomerSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetNewCustomerSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetNewCustomerSwitchRequest proto.InternalMessageInfo

func (m *SetNewCustomerSwitchRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetNewCustomerSwitchRequest) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

type SetNewCustomerSwitchResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetNewCustomerSwitchResponse) Reset()         { *m = SetNewCustomerSwitchResponse{} }
func (m *SetNewCustomerSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*SetNewCustomerSwitchResponse) ProtoMessage()    {}
func (*SetNewCustomerSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{139}
}
func (m *SetNewCustomerSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetNewCustomerSwitchResponse.Unmarshal(m, b)
}
func (m *SetNewCustomerSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetNewCustomerSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *SetNewCustomerSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetNewCustomerSwitchResponse.Merge(dst, src)
}
func (m *SetNewCustomerSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_SetNewCustomerSwitchResponse.Size(m)
}
func (m *SetNewCustomerSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetNewCustomerSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetNewCustomerSwitchResponse proto.InternalMessageInfo

type GetCoachIncentiveTaskInfoRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachIncentiveTaskInfoRequest) Reset()         { *m = GetCoachIncentiveTaskInfoRequest{} }
func (m *GetCoachIncentiveTaskInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachIncentiveTaskInfoRequest) ProtoMessage()    {}
func (*GetCoachIncentiveTaskInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{140}
}
func (m *GetCoachIncentiveTaskInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachIncentiveTaskInfoRequest.Unmarshal(m, b)
}
func (m *GetCoachIncentiveTaskInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachIncentiveTaskInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachIncentiveTaskInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachIncentiveTaskInfoRequest.Merge(dst, src)
}
func (m *GetCoachIncentiveTaskInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachIncentiveTaskInfoRequest.Size(m)
}
func (m *GetCoachIncentiveTaskInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachIncentiveTaskInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachIncentiveTaskInfoRequest proto.InternalMessageInfo

type GetCoachIncentiveTaskInfoResponse struct {
	SumScore             uint32                       `protobuf:"varint,1,opt,name=sum_score,json=sumScore,proto3" json:"sum_score"`
	NewScore             uint32                       `protobuf:"varint,2,opt,name=new_score,json=newScore,proto3" json:"new_score"`
	NewOrderCnt          uint32                       `protobuf:"varint,3,opt,name=new_order_cnt,json=newOrderCnt,proto3" json:"new_order_cnt"`
	FirstOrderScore      uint32                       `protobuf:"varint,4,opt,name=first_order_score,json=firstOrderScore,proto3" json:"first_order_score"`
	IsQuickReceive       bool                         `protobuf:"varint,5,opt,name=is_quick_receive,json=isQuickReceive,proto3" json:"is_quick_receive"`
	TaskList             []*esport_hall.IncentiveTask `protobuf:"bytes,6,rep,name=task_list,json=taskList,proto3" json:"task_list"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetCoachIncentiveTaskInfoResponse) Reset()         { *m = GetCoachIncentiveTaskInfoResponse{} }
func (m *GetCoachIncentiveTaskInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachIncentiveTaskInfoResponse) ProtoMessage()    {}
func (*GetCoachIncentiveTaskInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{141}
}
func (m *GetCoachIncentiveTaskInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachIncentiveTaskInfoResponse.Unmarshal(m, b)
}
func (m *GetCoachIncentiveTaskInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachIncentiveTaskInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachIncentiveTaskInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachIncentiveTaskInfoResponse.Merge(dst, src)
}
func (m *GetCoachIncentiveTaskInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachIncentiveTaskInfoResponse.Size(m)
}
func (m *GetCoachIncentiveTaskInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachIncentiveTaskInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachIncentiveTaskInfoResponse proto.InternalMessageInfo

func (m *GetCoachIncentiveTaskInfoResponse) GetSumScore() uint32 {
	if m != nil {
		return m.SumScore
	}
	return 0
}

func (m *GetCoachIncentiveTaskInfoResponse) GetNewScore() uint32 {
	if m != nil {
		return m.NewScore
	}
	return 0
}

func (m *GetCoachIncentiveTaskInfoResponse) GetNewOrderCnt() uint32 {
	if m != nil {
		return m.NewOrderCnt
	}
	return 0
}

func (m *GetCoachIncentiveTaskInfoResponse) GetFirstOrderScore() uint32 {
	if m != nil {
		return m.FirstOrderScore
	}
	return 0
}

func (m *GetCoachIncentiveTaskInfoResponse) GetIsQuickReceive() bool {
	if m != nil {
		return m.IsQuickReceive
	}
	return false
}

func (m *GetCoachIncentiveTaskInfoResponse) GetTaskList() []*esport_hall.IncentiveTask {
	if m != nil {
		return m.TaskList
	}
	return nil
}

// 请求客服托管开关操作
// /esport/hall/customer_hosting_operation
type HostingOperationRequest struct {
	Operation            HostingOperation `protobuf:"varint,1,opt,name=operation,proto3,enum=esport_http.HostingOperation" json:"operation"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *HostingOperationRequest) Reset()         { *m = HostingOperationRequest{} }
func (m *HostingOperationRequest) String() string { return proto.CompactTextString(m) }
func (*HostingOperationRequest) ProtoMessage()    {}
func (*HostingOperationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{142}
}
func (m *HostingOperationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HostingOperationRequest.Unmarshal(m, b)
}
func (m *HostingOperationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HostingOperationRequest.Marshal(b, m, deterministic)
}
func (dst *HostingOperationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HostingOperationRequest.Merge(dst, src)
}
func (m *HostingOperationRequest) XXX_Size() int {
	return xxx_messageInfo_HostingOperationRequest.Size(m)
}
func (m *HostingOperationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HostingOperationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HostingOperationRequest proto.InternalMessageInfo

func (m *HostingOperationRequest) GetOperation() HostingOperation {
	if m != nil {
		return m.Operation
	}
	return HostingOperation_HOSTING_OPERATION_UNSPECIFIED
}

// 客服托管开关操作响应
type HostingOperationResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HostingOperationResponse) Reset()         { *m = HostingOperationResponse{} }
func (m *HostingOperationResponse) String() string { return proto.CompactTextString(m) }
func (*HostingOperationResponse) ProtoMessage()    {}
func (*HostingOperationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{143}
}
func (m *HostingOperationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HostingOperationResponse.Unmarshal(m, b)
}
func (m *HostingOperationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HostingOperationResponse.Marshal(b, m, deterministic)
}
func (dst *HostingOperationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HostingOperationResponse.Merge(dst, src)
}
func (m *HostingOperationResponse) XXX_Size() int {
	return xxx_messageInfo_HostingOperationResponse.Size(m)
}
func (m *HostingOperationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HostingOperationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HostingOperationResponse proto.InternalMessageInfo

// 获取客服托管状态
// /esport/hall/get_hosting_status
type GetHostingStatusRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHostingStatusRequest) Reset()         { *m = GetHostingStatusRequest{} }
func (m *GetHostingStatusRequest) String() string { return proto.CompactTextString(m) }
func (*GetHostingStatusRequest) ProtoMessage()    {}
func (*GetHostingStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{144}
}
func (m *GetHostingStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHostingStatusRequest.Unmarshal(m, b)
}
func (m *GetHostingStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHostingStatusRequest.Marshal(b, m, deterministic)
}
func (dst *GetHostingStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHostingStatusRequest.Merge(dst, src)
}
func (m *GetHostingStatusRequest) XXX_Size() int {
	return xxx_messageInfo_GetHostingStatusRequest.Size(m)
}
func (m *GetHostingStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHostingStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetHostingStatusRequest proto.InternalMessageInfo

type GetHostingStatusResponse struct {
	IsOpen               bool         `protobuf:"varint,1,opt,name=is_open,json=isOpen,proto3" json:"is_open"`
	CustomerInfo         *UserProfile `protobuf:"bytes,2,opt,name=customer_info,json=customerInfo,proto3" json:"customer_info"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetHostingStatusResponse) Reset()         { *m = GetHostingStatusResponse{} }
func (m *GetHostingStatusResponse) String() string { return proto.CompactTextString(m) }
func (*GetHostingStatusResponse) ProtoMessage()    {}
func (*GetHostingStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{145}
}
func (m *GetHostingStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHostingStatusResponse.Unmarshal(m, b)
}
func (m *GetHostingStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHostingStatusResponse.Marshal(b, m, deterministic)
}
func (dst *GetHostingStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHostingStatusResponse.Merge(dst, src)
}
func (m *GetHostingStatusResponse) XXX_Size() int {
	return xxx_messageInfo_GetHostingStatusResponse.Size(m)
}
func (m *GetHostingStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHostingStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetHostingStatusResponse proto.InternalMessageInfo

func (m *GetHostingStatusResponse) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

func (m *GetHostingStatusResponse) GetCustomerInfo() *UserProfile {
	if m != nil {
		return m.CustomerInfo
	}
	return nil
}

// 联系客服
// /esport/hall/contact_customer_service
type ContactCustomerServiceRequest struct {
	CoachId              uint32   `protobuf:"varint,1,opt,name=coach_id,json=coachId,proto3" json:"coach_id"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ContactCustomerServiceRequest) Reset()         { *m = ContactCustomerServiceRequest{} }
func (m *ContactCustomerServiceRequest) String() string { return proto.CompactTextString(m) }
func (*ContactCustomerServiceRequest) ProtoMessage()    {}
func (*ContactCustomerServiceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{146}
}
func (m *ContactCustomerServiceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContactCustomerServiceRequest.Unmarshal(m, b)
}
func (m *ContactCustomerServiceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContactCustomerServiceRequest.Marshal(b, m, deterministic)
}
func (dst *ContactCustomerServiceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContactCustomerServiceRequest.Merge(dst, src)
}
func (m *ContactCustomerServiceRequest) XXX_Size() int {
	return xxx_messageInfo_ContactCustomerServiceRequest.Size(m)
}
func (m *ContactCustomerServiceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ContactCustomerServiceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ContactCustomerServiceRequest proto.InternalMessageInfo

func (m *ContactCustomerServiceRequest) GetCoachId() uint32 {
	if m != nil {
		return m.CoachId
	}
	return 0
}

func (m *ContactCustomerServiceRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

// 联系客服响应
type ContactCustomerServiceResponse struct {
	CustomerInfo         *UserProfile `protobuf:"bytes,1,opt,name=customer_info,json=customerInfo,proto3" json:"customer_info"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ContactCustomerServiceResponse) Reset()         { *m = ContactCustomerServiceResponse{} }
func (m *ContactCustomerServiceResponse) String() string { return proto.CompactTextString(m) }
func (*ContactCustomerServiceResponse) ProtoMessage()    {}
func (*ContactCustomerServiceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{147}
}
func (m *ContactCustomerServiceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContactCustomerServiceResponse.Unmarshal(m, b)
}
func (m *ContactCustomerServiceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContactCustomerServiceResponse.Marshal(b, m, deterministic)
}
func (dst *ContactCustomerServiceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContactCustomerServiceResponse.Merge(dst, src)
}
func (m *ContactCustomerServiceResponse) XXX_Size() int {
	return xxx_messageInfo_ContactCustomerServiceResponse.Size(m)
}
func (m *ContactCustomerServiceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ContactCustomerServiceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ContactCustomerServiceResponse proto.InternalMessageInfo

func (m *ContactCustomerServiceResponse) GetCustomerInfo() *UserProfile {
	if m != nil {
		return m.CustomerInfo
	}
	return nil
}

// 请求获取订单列表
// /esport/order/get_guild_order_list_for_customer
type GetGuildOrderListForCustomerRequest struct {
	TabType              uint32   `protobuf:"varint,1,opt,name=tab_type,json=tabType,proto3" json:"tab_type"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildOrderListForCustomerRequest) Reset()         { *m = GetGuildOrderListForCustomerRequest{} }
func (m *GetGuildOrderListForCustomerRequest) String() string { return proto.CompactTextString(m) }
func (*GetGuildOrderListForCustomerRequest) ProtoMessage()    {}
func (*GetGuildOrderListForCustomerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{148}
}
func (m *GetGuildOrderListForCustomerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildOrderListForCustomerRequest.Unmarshal(m, b)
}
func (m *GetGuildOrderListForCustomerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildOrderListForCustomerRequest.Marshal(b, m, deterministic)
}
func (dst *GetGuildOrderListForCustomerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildOrderListForCustomerRequest.Merge(dst, src)
}
func (m *GetGuildOrderListForCustomerRequest) XXX_Size() int {
	return xxx_messageInfo_GetGuildOrderListForCustomerRequest.Size(m)
}
func (m *GetGuildOrderListForCustomerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildOrderListForCustomerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildOrderListForCustomerRequest proto.InternalMessageInfo

func (m *GetGuildOrderListForCustomerRequest) GetTabType() uint32 {
	if m != nil {
		return m.TabType
	}
	return 0
}

func (m *GetGuildOrderListForCustomerRequest) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGuildOrderListForCustomerRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

// 订单简略信息
type OrderSimpleInfo struct {
	Player                    *UserProfile  `protobuf:"bytes,1,opt,name=player,proto3" json:"player"`
	ProductOrder              *ProductOrder `protobuf:"bytes,2,opt,name=product_order,json=productOrder,proto3" json:"product_order"`
	OrderId                   string        `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	Status                    uint32        `protobuf:"varint,4,opt,name=status,proto3" json:"status"`
	SubStatus                 uint32        `protobuf:"varint,5,opt,name=sub_status,json=subStatus,proto3" json:"sub_status"`
	PayTime                   int64         `protobuf:"varint,6,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	OrderEndTime              int64         `protobuf:"varint,7,opt,name=order_end_time,json=orderEndTime,proto3" json:"order_end_time"`
	OffsetId                  string        `protobuf:"bytes,8,opt,name=offset_id,json=offsetId,proto3" json:"offset_id"`
	StatusDesc                string        `protobuf:"bytes,9,opt,name=status_desc,json=statusDesc,proto3" json:"status_desc"`
	IsNotifyFinish            bool          `protobuf:"varint,10,opt,name=is_notify_finish,json=isNotifyFinish,proto3" json:"is_notify_finish"`
	CanCoachUploadAppeal      bool          `protobuf:"varint,11,opt,name=can_coach_upload_appeal,json=canCoachUploadAppeal,proto3" json:"can_coach_upload_appeal"`
	RefundType                RefundType    `protobuf:"varint,12,opt,name=refund_type,json=refundType,proto3,enum=esport_http.RefundType" json:"refund_type"`
	UpdateTime                int64         `protobuf:"varint,13,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	EvaluateEntry             bool          `protobuf:"varint,14,opt,name=evaluate_entry,json=evaluateEntry,proto3" json:"evaluate_entry"`
	CanAppeal                 bool          `protobuf:"varint,15,opt,name=can_appeal,json=canAppeal,proto3" json:"can_appeal"`
	AppealId                  string        `protobuf:"bytes,16,opt,name=appeal_id,json=appealId,proto3" json:"appeal_id"`
	RefundId                  string        `protobuf:"bytes,17,opt,name=refund_id,json=refundId,proto3" json:"refund_id"`
	CoachUploadAppealDeadline int64         `protobuf:"varint,18,opt,name=coach_upload_appeal_deadline,json=coachUploadAppealDeadline,proto3" json:"coach_upload_appeal_deadline"`
	StatueUpdateTime          int64         `protobuf:"varint,19,opt,name=statue_update_time,json=statueUpdateTime,proto3" json:"statue_update_time"`
	XXX_NoUnkeyedLiteral      struct{}      `json:"-"`
	XXX_unrecognized          []byte        `json:"-"`
	XXX_sizecache             int32         `json:"-"`
}

func (m *OrderSimpleInfo) Reset()         { *m = OrderSimpleInfo{} }
func (m *OrderSimpleInfo) String() string { return proto.CompactTextString(m) }
func (*OrderSimpleInfo) ProtoMessage()    {}
func (*OrderSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{149}
}
func (m *OrderSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderSimpleInfo.Unmarshal(m, b)
}
func (m *OrderSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *OrderSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderSimpleInfo.Merge(dst, src)
}
func (m *OrderSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_OrderSimpleInfo.Size(m)
}
func (m *OrderSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OrderSimpleInfo proto.InternalMessageInfo

func (m *OrderSimpleInfo) GetPlayer() *UserProfile {
	if m != nil {
		return m.Player
	}
	return nil
}

func (m *OrderSimpleInfo) GetProductOrder() *ProductOrder {
	if m != nil {
		return m.ProductOrder
	}
	return nil
}

func (m *OrderSimpleInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *OrderSimpleInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *OrderSimpleInfo) GetSubStatus() uint32 {
	if m != nil {
		return m.SubStatus
	}
	return 0
}

func (m *OrderSimpleInfo) GetPayTime() int64 {
	if m != nil {
		return m.PayTime
	}
	return 0
}

func (m *OrderSimpleInfo) GetOrderEndTime() int64 {
	if m != nil {
		return m.OrderEndTime
	}
	return 0
}

func (m *OrderSimpleInfo) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

func (m *OrderSimpleInfo) GetStatusDesc() string {
	if m != nil {
		return m.StatusDesc
	}
	return ""
}

func (m *OrderSimpleInfo) GetIsNotifyFinish() bool {
	if m != nil {
		return m.IsNotifyFinish
	}
	return false
}

func (m *OrderSimpleInfo) GetCanCoachUploadAppeal() bool {
	if m != nil {
		return m.CanCoachUploadAppeal
	}
	return false
}

func (m *OrderSimpleInfo) GetRefundType() RefundType {
	if m != nil {
		return m.RefundType
	}
	return RefundType_REFUND_TYPE_FULL
}

func (m *OrderSimpleInfo) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *OrderSimpleInfo) GetEvaluateEntry() bool {
	if m != nil {
		return m.EvaluateEntry
	}
	return false
}

func (m *OrderSimpleInfo) GetCanAppeal() bool {
	if m != nil {
		return m.CanAppeal
	}
	return false
}

func (m *OrderSimpleInfo) GetAppealId() string {
	if m != nil {
		return m.AppealId
	}
	return ""
}

func (m *OrderSimpleInfo) GetRefundId() string {
	if m != nil {
		return m.RefundId
	}
	return ""
}

func (m *OrderSimpleInfo) GetCoachUploadAppealDeadline() int64 {
	if m != nil {
		return m.CoachUploadAppealDeadline
	}
	return 0
}

func (m *OrderSimpleInfo) GetStatueUpdateTime() int64 {
	if m != nil {
		return m.StatueUpdateTime
	}
	return 0
}

// 订单列表响应
type GetGuildOrderListForCustomerResponse struct {
	OrderList            []*OrderSimpleInfo `protobuf:"bytes,1,rep,name=order_list,json=orderList,proto3" json:"order_list"`
	NextOffset           uint32             `protobuf:"varint,2,opt,name=next_offset,json=nextOffset,proto3" json:"next_offset"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetGuildOrderListForCustomerResponse) Reset()         { *m = GetGuildOrderListForCustomerResponse{} }
func (m *GetGuildOrderListForCustomerResponse) String() string { return proto.CompactTextString(m) }
func (*GetGuildOrderListForCustomerResponse) ProtoMessage()    {}
func (*GetGuildOrderListForCustomerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{150}
}
func (m *GetGuildOrderListForCustomerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildOrderListForCustomerResponse.Unmarshal(m, b)
}
func (m *GetGuildOrderListForCustomerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildOrderListForCustomerResponse.Marshal(b, m, deterministic)
}
func (dst *GetGuildOrderListForCustomerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildOrderListForCustomerResponse.Merge(dst, src)
}
func (m *GetGuildOrderListForCustomerResponse) XXX_Size() int {
	return xxx_messageInfo_GetGuildOrderListForCustomerResponse.Size(m)
}
func (m *GetGuildOrderListForCustomerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildOrderListForCustomerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildOrderListForCustomerResponse proto.InternalMessageInfo

func (m *GetGuildOrderListForCustomerResponse) GetOrderList() []*OrderSimpleInfo {
	if m != nil {
		return m.OrderList
	}
	return nil
}

func (m *GetGuildOrderListForCustomerResponse) GetNextOffset() uint32 {
	if m != nil {
		return m.NextOffset
	}
	return 0
}

// 获取顶部游戏列表
// /esport/skill/get_top_game_list
type GetTopGameListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopGameListRequest) Reset()         { *m = GetTopGameListRequest{} }
func (m *GetTopGameListRequest) String() string { return proto.CompactTextString(m) }
func (*GetTopGameListRequest) ProtoMessage()    {}
func (*GetTopGameListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{151}
}
func (m *GetTopGameListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopGameListRequest.Unmarshal(m, b)
}
func (m *GetTopGameListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopGameListRequest.Marshal(b, m, deterministic)
}
func (dst *GetTopGameListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopGameListRequest.Merge(dst, src)
}
func (m *GetTopGameListRequest) XXX_Size() int {
	return xxx_messageInfo_GetTopGameListRequest.Size(m)
}
func (m *GetTopGameListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopGameListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopGameListRequest proto.InternalMessageInfo

type GameItem struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	GameName             string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name"`
	GameIcon             string   `protobuf:"bytes,3,opt,name=game_icon,json=gameIcon,proto3" json:"game_icon"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameItem) Reset()         { *m = GameItem{} }
func (m *GameItem) String() string { return proto.CompactTextString(m) }
func (*GameItem) ProtoMessage()    {}
func (*GameItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{152}
}
func (m *GameItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameItem.Unmarshal(m, b)
}
func (m *GameItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameItem.Marshal(b, m, deterministic)
}
func (dst *GameItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameItem.Merge(dst, src)
}
func (m *GameItem) XXX_Size() int {
	return xxx_messageInfo_GameItem.Size(m)
}
func (m *GameItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GameItem.DiscardUnknown(m)
}

var xxx_messageInfo_GameItem proto.InternalMessageInfo

func (m *GameItem) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GameItem) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GameItem) GetGameIcon() string {
	if m != nil {
		return m.GameIcon
	}
	return ""
}

type GetTopGameListResponse struct {
	ItemList             []*GameItem `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetTopGameListResponse) Reset()         { *m = GetTopGameListResponse{} }
func (m *GetTopGameListResponse) String() string { return proto.CompactTextString(m) }
func (*GetTopGameListResponse) ProtoMessage()    {}
func (*GetTopGameListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{153}
}
func (m *GetTopGameListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopGameListResponse.Unmarshal(m, b)
}
func (m *GetTopGameListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopGameListResponse.Marshal(b, m, deterministic)
}
func (dst *GetTopGameListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopGameListResponse.Merge(dst, src)
}
func (m *GetTopGameListResponse) XXX_Size() int {
	return xxx_messageInfo_GetTopGameListResponse.Size(m)
}
func (m *GetTopGameListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopGameListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopGameListResponse proto.InternalMessageInfo

func (m *GetTopGameListResponse) GetItemList() []*GameItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

// 客服获取公会旗下的大神列表（分页）
// esport/hall/get_coach_list_for_customer
type GetCoachListForCustomerRequest struct {
	PageOffset           uint32   `protobuf:"varint,1,opt,name=page_offset,json=pageOffset,proto3" json:"page_offset"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	Condition            string   `protobuf:"bytes,3,opt,name=condition,proto3" json:"condition"`
	GameId               uint32   `protobuf:"varint,4,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	ViewedUid            []uint32 `protobuf:"varint,5,rep,packed,name=viewed_uid,json=viewedUid,proto3" json:"viewed_uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachListForCustomerRequest) Reset()         { *m = GetCoachListForCustomerRequest{} }
func (m *GetCoachListForCustomerRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachListForCustomerRequest) ProtoMessage()    {}
func (*GetCoachListForCustomerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{154}
}
func (m *GetCoachListForCustomerRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachListForCustomerRequest.Unmarshal(m, b)
}
func (m *GetCoachListForCustomerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachListForCustomerRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachListForCustomerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachListForCustomerRequest.Merge(dst, src)
}
func (m *GetCoachListForCustomerRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachListForCustomerRequest.Size(m)
}
func (m *GetCoachListForCustomerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachListForCustomerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachListForCustomerRequest proto.InternalMessageInfo

func (m *GetCoachListForCustomerRequest) GetPageOffset() uint32 {
	if m != nil {
		return m.PageOffset
	}
	return 0
}

func (m *GetCoachListForCustomerRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetCoachListForCustomerRequest) GetCondition() string {
	if m != nil {
		return m.Condition
	}
	return ""
}

func (m *GetCoachListForCustomerRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetCoachListForCustomerRequest) GetViewedUid() []uint32 {
	if m != nil {
		return m.ViewedUid
	}
	return nil
}

// 客服获取公会旗下的大神列表（分页）响应
type GetCoachListForCustomerResponse struct {
	CoachList            []*EsportAreaCoachInfo `protobuf:"bytes,1,rep,name=coach_list,json=coachList,proto3" json:"coach_list"`
	NextPageOffset       uint32                 `protobuf:"varint,2,opt,name=next_page_offset,json=nextPageOffset,proto3" json:"next_page_offset"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetCoachListForCustomerResponse) Reset()         { *m = GetCoachListForCustomerResponse{} }
func (m *GetCoachListForCustomerResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachListForCustomerResponse) ProtoMessage()    {}
func (*GetCoachListForCustomerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{155}
}
func (m *GetCoachListForCustomerResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachListForCustomerResponse.Unmarshal(m, b)
}
func (m *GetCoachListForCustomerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachListForCustomerResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachListForCustomerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachListForCustomerResponse.Merge(dst, src)
}
func (m *GetCoachListForCustomerResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachListForCustomerResponse.Size(m)
}
func (m *GetCoachListForCustomerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachListForCustomerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachListForCustomerResponse proto.InternalMessageInfo

func (m *GetCoachListForCustomerResponse) GetCoachList() []*EsportAreaCoachInfo {
	if m != nil {
		return m.CoachList
	}
	return nil
}

func (m *GetCoachListForCustomerResponse) GetNextPageOffset() uint32 {
	if m != nil {
		return m.NextPageOffset
	}
	return 0
}

// 请求发送大神名片
// /esport/hall/send_coach_skill_card
type SendCoachSkillCardRequest struct {
	CoachId              uint32   `protobuf:"varint,1,opt,name=coach_id,json=coachId,proto3" json:"coach_id"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	UserId               uint32   `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendCoachSkillCardRequest) Reset()         { *m = SendCoachSkillCardRequest{} }
func (m *SendCoachSkillCardRequest) String() string { return proto.CompactTextString(m) }
func (*SendCoachSkillCardRequest) ProtoMessage()    {}
func (*SendCoachSkillCardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{156}
}
func (m *SendCoachSkillCardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendCoachSkillCardRequest.Unmarshal(m, b)
}
func (m *SendCoachSkillCardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendCoachSkillCardRequest.Marshal(b, m, deterministic)
}
func (dst *SendCoachSkillCardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendCoachSkillCardRequest.Merge(dst, src)
}
func (m *SendCoachSkillCardRequest) XXX_Size() int {
	return xxx_messageInfo_SendCoachSkillCardRequest.Size(m)
}
func (m *SendCoachSkillCardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendCoachSkillCardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendCoachSkillCardRequest proto.InternalMessageInfo

func (m *SendCoachSkillCardRequest) GetCoachId() uint32 {
	if m != nil {
		return m.CoachId
	}
	return 0
}

func (m *SendCoachSkillCardRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SendCoachSkillCardRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

// 发送大神名片响应
type SendCoachSkillCardResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendCoachSkillCardResponse) Reset()         { *m = SendCoachSkillCardResponse{} }
func (m *SendCoachSkillCardResponse) String() string { return proto.CompactTextString(m) }
func (*SendCoachSkillCardResponse) ProtoMessage()    {}
func (*SendCoachSkillCardResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{157}
}
func (m *SendCoachSkillCardResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendCoachSkillCardResponse.Unmarshal(m, b)
}
func (m *SendCoachSkillCardResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendCoachSkillCardResponse.Marshal(b, m, deterministic)
}
func (dst *SendCoachSkillCardResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendCoachSkillCardResponse.Merge(dst, src)
}
func (m *SendCoachSkillCardResponse) XXX_Size() int {
	return xxx_messageInfo_SendCoachSkillCardResponse.Size(m)
}
func (m *SendCoachSkillCardResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SendCoachSkillCardResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SendCoachSkillCardResponse proto.InternalMessageInfo

// 获取优惠券背包
// uri: /esport/coupon/get_coupon_package
type GetCouponPackageRequest struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	RequestType          uint32   `protobuf:"varint,3,opt,name=request_type,json=requestType,proto3" json:"request_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCouponPackageRequest) Reset()         { *m = GetCouponPackageRequest{} }
func (m *GetCouponPackageRequest) String() string { return proto.CompactTextString(m) }
func (*GetCouponPackageRequest) ProtoMessage()    {}
func (*GetCouponPackageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{158}
}
func (m *GetCouponPackageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCouponPackageRequest.Unmarshal(m, b)
}
func (m *GetCouponPackageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCouponPackageRequest.Marshal(b, m, deterministic)
}
func (dst *GetCouponPackageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCouponPackageRequest.Merge(dst, src)
}
func (m *GetCouponPackageRequest) XXX_Size() int {
	return xxx_messageInfo_GetCouponPackageRequest.Size(m)
}
func (m *GetCouponPackageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCouponPackageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCouponPackageRequest proto.InternalMessageInfo

func (m *GetCouponPackageRequest) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetCouponPackageRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetCouponPackageRequest) GetRequestType() uint32 {
	if m != nil {
		return m.RequestType
	}
	return 0
}

type GetCouponPackageResponse struct {
	Total                uint32          `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	HasMore              bool            `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more"`
	Coupons              []*CouponDetail `protobuf:"bytes,3,rep,name=coupons,proto3" json:"coupons"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetCouponPackageResponse) Reset()         { *m = GetCouponPackageResponse{} }
func (m *GetCouponPackageResponse) String() string { return proto.CompactTextString(m) }
func (*GetCouponPackageResponse) ProtoMessage()    {}
func (*GetCouponPackageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{159}
}
func (m *GetCouponPackageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCouponPackageResponse.Unmarshal(m, b)
}
func (m *GetCouponPackageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCouponPackageResponse.Marshal(b, m, deterministic)
}
func (dst *GetCouponPackageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCouponPackageResponse.Merge(dst, src)
}
func (m *GetCouponPackageResponse) XXX_Size() int {
	return xxx_messageInfo_GetCouponPackageResponse.Size(m)
}
func (m *GetCouponPackageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCouponPackageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCouponPackageResponse proto.InternalMessageInfo

func (m *GetCouponPackageResponse) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetCouponPackageResponse) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

func (m *GetCouponPackageResponse) GetCoupons() []*CouponDetail {
	if m != nil {
		return m.Coupons
	}
	return nil
}

// 获取可用优惠券
// uri: /esport/coupon/get_available_coupon
type GetAvailableCouponRequest struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	RequestType          uint32   `protobuf:"varint,3,opt,name=request_type,json=requestType,proto3" json:"request_type"`
	ProductId            uint32   `protobuf:"varint,4,opt,name=product_id,json=productId,proto3" json:"product_id"`
	ProductCount         uint32   `protobuf:"varint,5,opt,name=product_count,json=productCount,proto3" json:"product_count"`
	ChosenCoupons        []string `protobuf:"bytes,6,rep,name=chosen_coupons,json=chosenCoupons,proto3" json:"chosen_coupons"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAvailableCouponRequest) Reset()         { *m = GetAvailableCouponRequest{} }
func (m *GetAvailableCouponRequest) String() string { return proto.CompactTextString(m) }
func (*GetAvailableCouponRequest) ProtoMessage()    {}
func (*GetAvailableCouponRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{160}
}
func (m *GetAvailableCouponRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAvailableCouponRequest.Unmarshal(m, b)
}
func (m *GetAvailableCouponRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAvailableCouponRequest.Marshal(b, m, deterministic)
}
func (dst *GetAvailableCouponRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAvailableCouponRequest.Merge(dst, src)
}
func (m *GetAvailableCouponRequest) XXX_Size() int {
	return xxx_messageInfo_GetAvailableCouponRequest.Size(m)
}
func (m *GetAvailableCouponRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAvailableCouponRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAvailableCouponRequest proto.InternalMessageInfo

func (m *GetAvailableCouponRequest) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetAvailableCouponRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetAvailableCouponRequest) GetRequestType() uint32 {
	if m != nil {
		return m.RequestType
	}
	return 0
}

func (m *GetAvailableCouponRequest) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *GetAvailableCouponRequest) GetProductCount() uint32 {
	if m != nil {
		return m.ProductCount
	}
	return 0
}

func (m *GetAvailableCouponRequest) GetChosenCoupons() []string {
	if m != nil {
		return m.ChosenCoupons
	}
	return nil
}

type GetAvailableCouponResponse struct {
	Total                uint32          `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	HasMore              bool            `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more"`
	Coupons              []*CouponDetail `protobuf:"bytes,3,rep,name=coupons,proto3" json:"coupons"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetAvailableCouponResponse) Reset()         { *m = GetAvailableCouponResponse{} }
func (m *GetAvailableCouponResponse) String() string { return proto.CompactTextString(m) }
func (*GetAvailableCouponResponse) ProtoMessage()    {}
func (*GetAvailableCouponResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{161}
}
func (m *GetAvailableCouponResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAvailableCouponResponse.Unmarshal(m, b)
}
func (m *GetAvailableCouponResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAvailableCouponResponse.Marshal(b, m, deterministic)
}
func (dst *GetAvailableCouponResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAvailableCouponResponse.Merge(dst, src)
}
func (m *GetAvailableCouponResponse) XXX_Size() int {
	return xxx_messageInfo_GetAvailableCouponResponse.Size(m)
}
func (m *GetAvailableCouponResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAvailableCouponResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAvailableCouponResponse proto.InternalMessageInfo

func (m *GetAvailableCouponResponse) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetAvailableCouponResponse) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

func (m *GetAvailableCouponResponse) GetCoupons() []*CouponDetail {
	if m != nil {
		return m.Coupons
	}
	return nil
}

type CouponDetail struct {
	CouponId             string   `protobuf:"bytes,1,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id"`
	CouponName           string   `protobuf:"bytes,2,opt,name=coupon_name,json=couponName,proto3" json:"coupon_name"`
	UsageLimitText       string   `protobuf:"bytes,3,opt,name=usage_limit_text,json=usageLimitText,proto3" json:"usage_limit_text"`
	ValidDateText        string   `protobuf:"bytes,4,opt,name=valid_date_text,json=validDateText,proto3" json:"valid_date_text"`
	CornerText           string   `protobuf:"bytes,5,opt,name=corner_text,json=cornerText,proto3" json:"corner_text"`
	MainDescReducePrice  uint32   `protobuf:"varint,6,opt,name=main_desc_reduce_price,json=mainDescReducePrice,proto3" json:"main_desc_reduce_price"`
	SubDescText          string   `protobuf:"bytes,7,opt,name=sub_desc_text,json=subDescText,proto3" json:"sub_desc_text"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CouponDetail) Reset()         { *m = CouponDetail{} }
func (m *CouponDetail) String() string { return proto.CompactTextString(m) }
func (*CouponDetail) ProtoMessage()    {}
func (*CouponDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{162}
}
func (m *CouponDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CouponDetail.Unmarshal(m, b)
}
func (m *CouponDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CouponDetail.Marshal(b, m, deterministic)
}
func (dst *CouponDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CouponDetail.Merge(dst, src)
}
func (m *CouponDetail) XXX_Size() int {
	return xxx_messageInfo_CouponDetail.Size(m)
}
func (m *CouponDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_CouponDetail.DiscardUnknown(m)
}

var xxx_messageInfo_CouponDetail proto.InternalMessageInfo

func (m *CouponDetail) GetCouponId() string {
	if m != nil {
		return m.CouponId
	}
	return ""
}

func (m *CouponDetail) GetCouponName() string {
	if m != nil {
		return m.CouponName
	}
	return ""
}

func (m *CouponDetail) GetUsageLimitText() string {
	if m != nil {
		return m.UsageLimitText
	}
	return ""
}

func (m *CouponDetail) GetValidDateText() string {
	if m != nil {
		return m.ValidDateText
	}
	return ""
}

func (m *CouponDetail) GetCornerText() string {
	if m != nil {
		return m.CornerText
	}
	return ""
}

func (m *CouponDetail) GetMainDescReducePrice() uint32 {
	if m != nil {
		return m.MainDescReducePrice
	}
	return 0
}

func (m *CouponDetail) GetSubDescText() string {
	if m != nil {
		return m.SubDescText
	}
	return ""
}

// 上报已曝光大神列表
type ReportExposeCoachRequest struct {
	ExposeCoachList      []uint32 `protobuf:"varint,1,rep,packed,name=expose_coach_list,json=exposeCoachList,proto3" json:"expose_coach_list"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	ExposeType           uint32   `protobuf:"varint,3,opt,name=expose_type,json=exposeType,proto3" json:"expose_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportExposeCoachRequest) Reset()         { *m = ReportExposeCoachRequest{} }
func (m *ReportExposeCoachRequest) String() string { return proto.CompactTextString(m) }
func (*ReportExposeCoachRequest) ProtoMessage()    {}
func (*ReportExposeCoachRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{163}
}
func (m *ReportExposeCoachRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportExposeCoachRequest.Unmarshal(m, b)
}
func (m *ReportExposeCoachRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportExposeCoachRequest.Marshal(b, m, deterministic)
}
func (dst *ReportExposeCoachRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportExposeCoachRequest.Merge(dst, src)
}
func (m *ReportExposeCoachRequest) XXX_Size() int {
	return xxx_messageInfo_ReportExposeCoachRequest.Size(m)
}
func (m *ReportExposeCoachRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportExposeCoachRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReportExposeCoachRequest proto.InternalMessageInfo

func (m *ReportExposeCoachRequest) GetExposeCoachList() []uint32 {
	if m != nil {
		return m.ExposeCoachList
	}
	return nil
}

func (m *ReportExposeCoachRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ReportExposeCoachRequest) GetExposeType() uint32 {
	if m != nil {
		return m.ExposeType
	}
	return 0
}

type ReportExposeCoachResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportExposeCoachResponse) Reset()         { *m = ReportExposeCoachResponse{} }
func (m *ReportExposeCoachResponse) String() string { return proto.CompactTextString(m) }
func (*ReportExposeCoachResponse) ProtoMessage()    {}
func (*ReportExposeCoachResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{164}
}
func (m *ReportExposeCoachResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportExposeCoachResponse.Unmarshal(m, b)
}
func (m *ReportExposeCoachResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportExposeCoachResponse.Marshal(b, m, deterministic)
}
func (dst *ReportExposeCoachResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportExposeCoachResponse.Merge(dst, src)
}
func (m *ReportExposeCoachResponse) XXX_Size() int {
	return xxx_messageInfo_ReportExposeCoachResponse.Size(m)
}
func (m *ReportExposeCoachResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportExposeCoachResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReportExposeCoachResponse proto.InternalMessageInfo

// 获取游戏筛选项
type GetActivityGamePropertyRequest struct {
	SceneType            uint32   `protobuf:"varint,1,opt,name=scene_type,json=sceneType,proto3" json:"scene_type"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetActivityGamePropertyRequest) Reset()         { *m = GetActivityGamePropertyRequest{} }
func (m *GetActivityGamePropertyRequest) String() string { return proto.CompactTextString(m) }
func (*GetActivityGamePropertyRequest) ProtoMessage()    {}
func (*GetActivityGamePropertyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{165}
}
func (m *GetActivityGamePropertyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivityGamePropertyRequest.Unmarshal(m, b)
}
func (m *GetActivityGamePropertyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivityGamePropertyRequest.Marshal(b, m, deterministic)
}
func (dst *GetActivityGamePropertyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivityGamePropertyRequest.Merge(dst, src)
}
func (m *GetActivityGamePropertyRequest) XXX_Size() int {
	return xxx_messageInfo_GetActivityGamePropertyRequest.Size(m)
}
func (m *GetActivityGamePropertyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivityGamePropertyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivityGamePropertyRequest proto.InternalMessageInfo

func (m *GetActivityGamePropertyRequest) GetSceneType() uint32 {
	if m != nil {
		return m.SceneType
	}
	return 0
}

func (m *GetActivityGamePropertyRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetActivityGamePropertyResponse struct {
	PropertyList         []*esport_hall.GameProperty `protobuf:"bytes,1,rep,name=property_list,json=propertyList,proto3" json:"property_list"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetActivityGamePropertyResponse) Reset()         { *m = GetActivityGamePropertyResponse{} }
func (m *GetActivityGamePropertyResponse) String() string { return proto.CompactTextString(m) }
func (*GetActivityGamePropertyResponse) ProtoMessage()    {}
func (*GetActivityGamePropertyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{166}
}
func (m *GetActivityGamePropertyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivityGamePropertyResponse.Unmarshal(m, b)
}
func (m *GetActivityGamePropertyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivityGamePropertyResponse.Marshal(b, m, deterministic)
}
func (dst *GetActivityGamePropertyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivityGamePropertyResponse.Merge(dst, src)
}
func (m *GetActivityGamePropertyResponse) XXX_Size() int {
	return xxx_messageInfo_GetActivityGamePropertyResponse.Size(m)
}
func (m *GetActivityGamePropertyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivityGamePropertyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivityGamePropertyResponse proto.InternalMessageInfo

func (m *GetActivityGamePropertyResponse) GetPropertyList() []*esport_hall.GameProperty {
	if m != nil {
		return m.PropertyList
	}
	return nil
}

type GetCoachMissionInfoPageRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachMissionInfoPageRequest) Reset()         { *m = GetCoachMissionInfoPageRequest{} }
func (m *GetCoachMissionInfoPageRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachMissionInfoPageRequest) ProtoMessage()    {}
func (*GetCoachMissionInfoPageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{167}
}
func (m *GetCoachMissionInfoPageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachMissionInfoPageRequest.Unmarshal(m, b)
}
func (m *GetCoachMissionInfoPageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachMissionInfoPageRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachMissionInfoPageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachMissionInfoPageRequest.Merge(dst, src)
}
func (m *GetCoachMissionInfoPageRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachMissionInfoPageRequest.Size(m)
}
func (m *GetCoachMissionInfoPageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachMissionInfoPageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachMissionInfoPageRequest proto.InternalMessageInfo

type MasterSwitch struct {
	IsOpen               bool     `protobuf:"varint,1,opt,name=is_open,json=isOpen,proto3" json:"is_open"`
	MasterSwitchType     uint32   `protobuf:"varint,2,opt,name=master_switch_type,json=masterSwitchType,proto3" json:"master_switch_type"`
	CouldOpen            bool     `protobuf:"varint,3,opt,name=could_open,json=couldOpen,proto3" json:"could_open"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MasterSwitch) Reset()         { *m = MasterSwitch{} }
func (m *MasterSwitch) String() string { return proto.CompactTextString(m) }
func (*MasterSwitch) ProtoMessage()    {}
func (*MasterSwitch) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{168}
}
func (m *MasterSwitch) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MasterSwitch.Unmarshal(m, b)
}
func (m *MasterSwitch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MasterSwitch.Marshal(b, m, deterministic)
}
func (dst *MasterSwitch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterSwitch.Merge(dst, src)
}
func (m *MasterSwitch) XXX_Size() int {
	return xxx_messageInfo_MasterSwitch.Size(m)
}
func (m *MasterSwitch) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterSwitch.DiscardUnknown(m)
}

var xxx_messageInfo_MasterSwitch proto.InternalMessageInfo

func (m *MasterSwitch) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

func (m *MasterSwitch) GetMasterSwitchType() uint32 {
	if m != nil {
		return m.MasterSwitchType
	}
	return 0
}

func (m *MasterSwitch) GetCouldOpen() bool {
	if m != nil {
		return m.CouldOpen
	}
	return false
}

type GetCoachMissionInfoPageResponse struct {
	GodLevel              uint32          `protobuf:"varint,1,opt,name=god_level,json=godLevel,proto3" json:"god_level"`
	TodayExposure         uint32          `protobuf:"varint,2,opt,name=today_exposure,json=todayExposure,proto3" json:"today_exposure"`
	TotalViewCnt          uint32          `protobuf:"varint,3,opt,name=total_view_cnt,json=totalViewCnt,proto3" json:"total_view_cnt"`
	RecentAdditionViewCnt uint32          `protobuf:"varint,4,opt,name=recent_addition_view_cnt,json=recentAdditionViewCnt,proto3" json:"recent_addition_view_cnt"`
	Proportion            uint32          `protobuf:"varint,5,opt,name=proportion,proto3" json:"proportion"`
	ProjectedIncome       uint32          `protobuf:"varint,6,opt,name=projected_income,json=projectedIncome,proto3" json:"projected_income"`
	MasterSwitchList      []*MasterSwitch `protobuf:"bytes,7,rep,name=master_switch_list,json=masterSwitchList,proto3" json:"master_switch_list"`
	XXX_NoUnkeyedLiteral  struct{}        `json:"-"`
	XXX_unrecognized      []byte          `json:"-"`
	XXX_sizecache         int32           `json:"-"`
}

func (m *GetCoachMissionInfoPageResponse) Reset()         { *m = GetCoachMissionInfoPageResponse{} }
func (m *GetCoachMissionInfoPageResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachMissionInfoPageResponse) ProtoMessage()    {}
func (*GetCoachMissionInfoPageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{169}
}
func (m *GetCoachMissionInfoPageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachMissionInfoPageResponse.Unmarshal(m, b)
}
func (m *GetCoachMissionInfoPageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachMissionInfoPageResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachMissionInfoPageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachMissionInfoPageResponse.Merge(dst, src)
}
func (m *GetCoachMissionInfoPageResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachMissionInfoPageResponse.Size(m)
}
func (m *GetCoachMissionInfoPageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachMissionInfoPageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachMissionInfoPageResponse proto.InternalMessageInfo

func (m *GetCoachMissionInfoPageResponse) GetGodLevel() uint32 {
	if m != nil {
		return m.GodLevel
	}
	return 0
}

func (m *GetCoachMissionInfoPageResponse) GetTodayExposure() uint32 {
	if m != nil {
		return m.TodayExposure
	}
	return 0
}

func (m *GetCoachMissionInfoPageResponse) GetTotalViewCnt() uint32 {
	if m != nil {
		return m.TotalViewCnt
	}
	return 0
}

func (m *GetCoachMissionInfoPageResponse) GetRecentAdditionViewCnt() uint32 {
	if m != nil {
		return m.RecentAdditionViewCnt
	}
	return 0
}

func (m *GetCoachMissionInfoPageResponse) GetProportion() uint32 {
	if m != nil {
		return m.Proportion
	}
	return 0
}

func (m *GetCoachMissionInfoPageResponse) GetProjectedIncome() uint32 {
	if m != nil {
		return m.ProjectedIncome
	}
	return 0
}

func (m *GetCoachMissionInfoPageResponse) GetMasterSwitchList() []*MasterSwitch {
	if m != nil {
		return m.MasterSwitchList
	}
	return nil
}

type GetIncentiveTaskEntrySwitchRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetIncentiveTaskEntrySwitchRequest) Reset()         { *m = GetIncentiveTaskEntrySwitchRequest{} }
func (m *GetIncentiveTaskEntrySwitchRequest) String() string { return proto.CompactTextString(m) }
func (*GetIncentiveTaskEntrySwitchRequest) ProtoMessage()    {}
func (*GetIncentiveTaskEntrySwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{170}
}
func (m *GetIncentiveTaskEntrySwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIncentiveTaskEntrySwitchRequest.Unmarshal(m, b)
}
func (m *GetIncentiveTaskEntrySwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIncentiveTaskEntrySwitchRequest.Marshal(b, m, deterministic)
}
func (dst *GetIncentiveTaskEntrySwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIncentiveTaskEntrySwitchRequest.Merge(dst, src)
}
func (m *GetIncentiveTaskEntrySwitchRequest) XXX_Size() int {
	return xxx_messageInfo_GetIncentiveTaskEntrySwitchRequest.Size(m)
}
func (m *GetIncentiveTaskEntrySwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIncentiveTaskEntrySwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetIncentiveTaskEntrySwitchRequest proto.InternalMessageInfo

type GetIncentiveTaskEntrySwitchResponse struct {
	ShowSwitch           bool     `protobuf:"varint,1,opt,name=show_switch,json=showSwitch,proto3" json:"show_switch"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetIncentiveTaskEntrySwitchResponse) Reset()         { *m = GetIncentiveTaskEntrySwitchResponse{} }
func (m *GetIncentiveTaskEntrySwitchResponse) String() string { return proto.CompactTextString(m) }
func (*GetIncentiveTaskEntrySwitchResponse) ProtoMessage()    {}
func (*GetIncentiveTaskEntrySwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{171}
}
func (m *GetIncentiveTaskEntrySwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIncentiveTaskEntrySwitchResponse.Unmarshal(m, b)
}
func (m *GetIncentiveTaskEntrySwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIncentiveTaskEntrySwitchResponse.Marshal(b, m, deterministic)
}
func (dst *GetIncentiveTaskEntrySwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIncentiveTaskEntrySwitchResponse.Merge(dst, src)
}
func (m *GetIncentiveTaskEntrySwitchResponse) XXX_Size() int {
	return xxx_messageInfo_GetIncentiveTaskEntrySwitchResponse.Size(m)
}
func (m *GetIncentiveTaskEntrySwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIncentiveTaskEntrySwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetIncentiveTaskEntrySwitchResponse proto.InternalMessageInfo

func (m *GetIncentiveTaskEntrySwitchResponse) GetShowSwitch() bool {
	if m != nil {
		return m.ShowSwitch
	}
	return false
}

// 获取抢单中心概要
// uri: /esport/grab/get_grab_center_overview
type GetGrabCenterOverviewRequest struct {
	LastRefreshTs        int64    `protobuf:"varint,1,opt,name=last_refresh_ts,json=lastRefreshTs,proto3" json:"last_refresh_ts"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGrabCenterOverviewRequest) Reset()         { *m = GetGrabCenterOverviewRequest{} }
func (m *GetGrabCenterOverviewRequest) String() string { return proto.CompactTextString(m) }
func (*GetGrabCenterOverviewRequest) ProtoMessage()    {}
func (*GetGrabCenterOverviewRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{172}
}
func (m *GetGrabCenterOverviewRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGrabCenterOverviewRequest.Unmarshal(m, b)
}
func (m *GetGrabCenterOverviewRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGrabCenterOverviewRequest.Marshal(b, m, deterministic)
}
func (dst *GetGrabCenterOverviewRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGrabCenterOverviewRequest.Merge(dst, src)
}
func (m *GetGrabCenterOverviewRequest) XXX_Size() int {
	return xxx_messageInfo_GetGrabCenterOverviewRequest.Size(m)
}
func (m *GetGrabCenterOverviewRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGrabCenterOverviewRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGrabCenterOverviewRequest proto.InternalMessageInfo

func (m *GetGrabCenterOverviewRequest) GetLastRefreshTs() int64 {
	if m != nil {
		return m.LastRefreshTs
	}
	return 0
}

type GetGrabCenterOverviewResponse struct {
	UpdateOrderCount     uint32   `protobuf:"varint,1,opt,name=update_order_count,json=updateOrderCount,proto3" json:"update_order_count"`
	GoingOrderCount      uint32   `protobuf:"varint,2,opt,name=going_order_count,json=goingOrderCount,proto3" json:"going_order_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGrabCenterOverviewResponse) Reset()         { *m = GetGrabCenterOverviewResponse{} }
func (m *GetGrabCenterOverviewResponse) String() string { return proto.CompactTextString(m) }
func (*GetGrabCenterOverviewResponse) ProtoMessage()    {}
func (*GetGrabCenterOverviewResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{173}
}
func (m *GetGrabCenterOverviewResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGrabCenterOverviewResponse.Unmarshal(m, b)
}
func (m *GetGrabCenterOverviewResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGrabCenterOverviewResponse.Marshal(b, m, deterministic)
}
func (dst *GetGrabCenterOverviewResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGrabCenterOverviewResponse.Merge(dst, src)
}
func (m *GetGrabCenterOverviewResponse) XXX_Size() int {
	return xxx_messageInfo_GetGrabCenterOverviewResponse.Size(m)
}
func (m *GetGrabCenterOverviewResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGrabCenterOverviewResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGrabCenterOverviewResponse proto.InternalMessageInfo

func (m *GetGrabCenterOverviewResponse) GetUpdateOrderCount() uint32 {
	if m != nil {
		return m.UpdateOrderCount
	}
	return 0
}

func (m *GetGrabCenterOverviewResponse) GetGoingOrderCount() uint32 {
	if m != nil {
		return m.GoingOrderCount
	}
	return 0
}

// 获取待抢订单列表
// uri: /esport/grab/get_pending_grab_order_list
type GetPendingGrabOrderListRequest struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	Size                 uint32   `protobuf:"varint,2,opt,name=size,proto3" json:"size"`
	RefreshTs            int64    `protobuf:"varint,3,opt,name=refresh_ts,json=refreshTs,proto3" json:"refresh_ts"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPendingGrabOrderListRequest) Reset()         { *m = GetPendingGrabOrderListRequest{} }
func (m *GetPendingGrabOrderListRequest) String() string { return proto.CompactTextString(m) }
func (*GetPendingGrabOrderListRequest) ProtoMessage()    {}
func (*GetPendingGrabOrderListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{174}
}
func (m *GetPendingGrabOrderListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPendingGrabOrderListRequest.Unmarshal(m, b)
}
func (m *GetPendingGrabOrderListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPendingGrabOrderListRequest.Marshal(b, m, deterministic)
}
func (dst *GetPendingGrabOrderListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPendingGrabOrderListRequest.Merge(dst, src)
}
func (m *GetPendingGrabOrderListRequest) XXX_Size() int {
	return xxx_messageInfo_GetPendingGrabOrderListRequest.Size(m)
}
func (m *GetPendingGrabOrderListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPendingGrabOrderListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPendingGrabOrderListRequest proto.InternalMessageInfo

func (m *GetPendingGrabOrderListRequest) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetPendingGrabOrderListRequest) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetPendingGrabOrderListRequest) GetRefreshTs() int64 {
	if m != nil {
		return m.RefreshTs
	}
	return 0
}

type GetPendingGrabOrderListResponse struct {
	OrderList            []*OneKeyFindCoachDemand `protobuf:"bytes,1,rep,name=order_list,json=orderList,proto3" json:"order_list"`
	HasMore              bool                     `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more"`
	RefreshTs            int64                    `protobuf:"varint,3,opt,name=refresh_ts,json=refreshTs,proto3" json:"refresh_ts"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetPendingGrabOrderListResponse) Reset()         { *m = GetPendingGrabOrderListResponse{} }
func (m *GetPendingGrabOrderListResponse) String() string { return proto.CompactTextString(m) }
func (*GetPendingGrabOrderListResponse) ProtoMessage()    {}
func (*GetPendingGrabOrderListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{175}
}
func (m *GetPendingGrabOrderListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPendingGrabOrderListResponse.Unmarshal(m, b)
}
func (m *GetPendingGrabOrderListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPendingGrabOrderListResponse.Marshal(b, m, deterministic)
}
func (dst *GetPendingGrabOrderListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPendingGrabOrderListResponse.Merge(dst, src)
}
func (m *GetPendingGrabOrderListResponse) XXX_Size() int {
	return xxx_messageInfo_GetPendingGrabOrderListResponse.Size(m)
}
func (m *GetPendingGrabOrderListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPendingGrabOrderListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPendingGrabOrderListResponse proto.InternalMessageInfo

func (m *GetPendingGrabOrderListResponse) GetOrderList() []*OneKeyFindCoachDemand {
	if m != nil {
		return m.OrderList
	}
	return nil
}

func (m *GetPendingGrabOrderListResponse) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

func (m *GetPendingGrabOrderListResponse) GetRefreshTs() int64 {
	if m != nil {
		return m.RefreshTs
	}
	return 0
}

// 获取已抢订单列表
// uri: /esport/grab/get_grabbed_order_list
type GetGrabbedOrderListRequest struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	Size                 uint32   `protobuf:"varint,2,opt,name=size,proto3" json:"size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGrabbedOrderListRequest) Reset()         { *m = GetGrabbedOrderListRequest{} }
func (m *GetGrabbedOrderListRequest) String() string { return proto.CompactTextString(m) }
func (*GetGrabbedOrderListRequest) ProtoMessage()    {}
func (*GetGrabbedOrderListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{176}
}
func (m *GetGrabbedOrderListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGrabbedOrderListRequest.Unmarshal(m, b)
}
func (m *GetGrabbedOrderListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGrabbedOrderListRequest.Marshal(b, m, deterministic)
}
func (dst *GetGrabbedOrderListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGrabbedOrderListRequest.Merge(dst, src)
}
func (m *GetGrabbedOrderListRequest) XXX_Size() int {
	return xxx_messageInfo_GetGrabbedOrderListRequest.Size(m)
}
func (m *GetGrabbedOrderListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGrabbedOrderListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGrabbedOrderListRequest proto.InternalMessageInfo

func (m *GetGrabbedOrderListRequest) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetGrabbedOrderListRequest) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

type GetGrabbedOrderListResponse struct {
	OrderList            []*GrabOrderInfo `protobuf:"bytes,1,rep,name=order_list,json=orderList,proto3" json:"order_list"`
	HasMore              bool             `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetGrabbedOrderListResponse) Reset()         { *m = GetGrabbedOrderListResponse{} }
func (m *GetGrabbedOrderListResponse) String() string { return proto.CompactTextString(m) }
func (*GetGrabbedOrderListResponse) ProtoMessage()    {}
func (*GetGrabbedOrderListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{177}
}
func (m *GetGrabbedOrderListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGrabbedOrderListResponse.Unmarshal(m, b)
}
func (m *GetGrabbedOrderListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGrabbedOrderListResponse.Marshal(b, m, deterministic)
}
func (dst *GetGrabbedOrderListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGrabbedOrderListResponse.Merge(dst, src)
}
func (m *GetGrabbedOrderListResponse) XXX_Size() int {
	return xxx_messageInfo_GetGrabbedOrderListResponse.Size(m)
}
func (m *GetGrabbedOrderListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGrabbedOrderListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGrabbedOrderListResponse proto.InternalMessageInfo

func (m *GetGrabbedOrderListResponse) GetOrderList() []*GrabOrderInfo {
	if m != nil {
		return m.OrderList
	}
	return nil
}

func (m *GetGrabbedOrderListResponse) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

// 发起抢单
// uri: /esport/grab/grab_order
type GrabOrderRequest struct {
	PublishId            string   `protobuf:"bytes,1,opt,name=publish_id,json=publishId,proto3" json:"publish_id"`
	GrabType             uint32   `protobuf:"varint,2,opt,name=grab_type,json=grabType,proto3" json:"grab_type"`
	GrabText             string   `protobuf:"bytes,3,opt,name=grab_text,json=grabText,proto3" json:"grab_text"`
	GrabAudio            string   `protobuf:"bytes,4,opt,name=grab_audio,json=grabAudio,proto3" json:"grab_audio"`
	GrabAudioDuration    uint32   `protobuf:"varint,5,opt,name=grab_audio_duration,json=grabAudioDuration,proto3" json:"grab_audio_duration"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GrabOrderRequest) Reset()         { *m = GrabOrderRequest{} }
func (m *GrabOrderRequest) String() string { return proto.CompactTextString(m) }
func (*GrabOrderRequest) ProtoMessage()    {}
func (*GrabOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{178}
}
func (m *GrabOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrabOrderRequest.Unmarshal(m, b)
}
func (m *GrabOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrabOrderRequest.Marshal(b, m, deterministic)
}
func (dst *GrabOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrabOrderRequest.Merge(dst, src)
}
func (m *GrabOrderRequest) XXX_Size() int {
	return xxx_messageInfo_GrabOrderRequest.Size(m)
}
func (m *GrabOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GrabOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GrabOrderRequest proto.InternalMessageInfo

func (m *GrabOrderRequest) GetPublishId() string {
	if m != nil {
		return m.PublishId
	}
	return ""
}

func (m *GrabOrderRequest) GetGrabType() uint32 {
	if m != nil {
		return m.GrabType
	}
	return 0
}

func (m *GrabOrderRequest) GetGrabText() string {
	if m != nil {
		return m.GrabText
	}
	return ""
}

func (m *GrabOrderRequest) GetGrabAudio() string {
	if m != nil {
		return m.GrabAudio
	}
	return ""
}

func (m *GrabOrderRequest) GetGrabAudioDuration() uint32 {
	if m != nil {
		return m.GrabAudioDuration
	}
	return 0
}

type GrabOrderResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GrabOrderResponse) Reset()         { *m = GrabOrderResponse{} }
func (m *GrabOrderResponse) String() string { return proto.CompactTextString(m) }
func (*GrabOrderResponse) ProtoMessage()    {}
func (*GrabOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{179}
}
func (m *GrabOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrabOrderResponse.Unmarshal(m, b)
}
func (m *GrabOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrabOrderResponse.Marshal(b, m, deterministic)
}
func (dst *GrabOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrabOrderResponse.Merge(dst, src)
}
func (m *GrabOrderResponse) XXX_Size() int {
	return xxx_messageInfo_GrabOrderResponse.Size(m)
}
func (m *GrabOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GrabOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GrabOrderResponse proto.InternalMessageInfo

// 异步检测语音
// uri: /esport/grab/async_check_audio
type AsyncCheckAudioRequest struct {
	GrabAudio            string   `protobuf:"bytes,1,opt,name=grab_audio,json=grabAudio,proto3" json:"grab_audio"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AsyncCheckAudioRequest) Reset()         { *m = AsyncCheckAudioRequest{} }
func (m *AsyncCheckAudioRequest) String() string { return proto.CompactTextString(m) }
func (*AsyncCheckAudioRequest) ProtoMessage()    {}
func (*AsyncCheckAudioRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{180}
}
func (m *AsyncCheckAudioRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AsyncCheckAudioRequest.Unmarshal(m, b)
}
func (m *AsyncCheckAudioRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AsyncCheckAudioRequest.Marshal(b, m, deterministic)
}
func (dst *AsyncCheckAudioRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AsyncCheckAudioRequest.Merge(dst, src)
}
func (m *AsyncCheckAudioRequest) XXX_Size() int {
	return xxx_messageInfo_AsyncCheckAudioRequest.Size(m)
}
func (m *AsyncCheckAudioRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AsyncCheckAudioRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AsyncCheckAudioRequest proto.InternalMessageInfo

func (m *AsyncCheckAudioRequest) GetGrabAudio() string {
	if m != nil {
		return m.GrabAudio
	}
	return ""
}

type AsyncCheckAudioResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AsyncCheckAudioResponse) Reset()         { *m = AsyncCheckAudioResponse{} }
func (m *AsyncCheckAudioResponse) String() string { return proto.CompactTextString(m) }
func (*AsyncCheckAudioResponse) ProtoMessage()    {}
func (*AsyncCheckAudioResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{181}
}
func (m *AsyncCheckAudioResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AsyncCheckAudioResponse.Unmarshal(m, b)
}
func (m *AsyncCheckAudioResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AsyncCheckAudioResponse.Marshal(b, m, deterministic)
}
func (dst *AsyncCheckAudioResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AsyncCheckAudioResponse.Merge(dst, src)
}
func (m *AsyncCheckAudioResponse) XXX_Size() int {
	return xxx_messageInfo_AsyncCheckAudioResponse.Size(m)
}
func (m *AsyncCheckAudioResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AsyncCheckAudioResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AsyncCheckAudioResponse proto.InternalMessageInfo

// 查询检测语音结果
// 1. 如果检测结果为正在检测，则每隔1s轮询；直到检测结果为有效或无效，或者10s超时，再停止轮询
// 2. 如果检测结果为无效，则提示用户，结束流程
// 3. 如果检测结果为有效，则继续调用抢单接口
// uri: /esport/grab/query_check_audio_status
type QueryCheckAudioStatusRequest struct {
	GrabAudio            string   `protobuf:"bytes,1,opt,name=grab_audio,json=grabAudio,proto3" json:"grab_audio"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryCheckAudioStatusRequest) Reset()         { *m = QueryCheckAudioStatusRequest{} }
func (m *QueryCheckAudioStatusRequest) String() string { return proto.CompactTextString(m) }
func (*QueryCheckAudioStatusRequest) ProtoMessage()    {}
func (*QueryCheckAudioStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{182}
}
func (m *QueryCheckAudioStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryCheckAudioStatusRequest.Unmarshal(m, b)
}
func (m *QueryCheckAudioStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryCheckAudioStatusRequest.Marshal(b, m, deterministic)
}
func (dst *QueryCheckAudioStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryCheckAudioStatusRequest.Merge(dst, src)
}
func (m *QueryCheckAudioStatusRequest) XXX_Size() int {
	return xxx_messageInfo_QueryCheckAudioStatusRequest.Size(m)
}
func (m *QueryCheckAudioStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryCheckAudioStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryCheckAudioStatusRequest proto.InternalMessageInfo

func (m *QueryCheckAudioStatusRequest) GetGrabAudio() string {
	if m != nil {
		return m.GrabAudio
	}
	return ""
}

type QueryCheckAudioStatusResponse struct {
	CheckStatus          uint32   `protobuf:"varint,1,opt,name=check_status,json=checkStatus,proto3" json:"check_status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryCheckAudioStatusResponse) Reset()         { *m = QueryCheckAudioStatusResponse{} }
func (m *QueryCheckAudioStatusResponse) String() string { return proto.CompactTextString(m) }
func (*QueryCheckAudioStatusResponse) ProtoMessage()    {}
func (*QueryCheckAudioStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{183}
}
func (m *QueryCheckAudioStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryCheckAudioStatusResponse.Unmarshal(m, b)
}
func (m *QueryCheckAudioStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryCheckAudioStatusResponse.Marshal(b, m, deterministic)
}
func (dst *QueryCheckAudioStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryCheckAudioStatusResponse.Merge(dst, src)
}
func (m *QueryCheckAudioStatusResponse) XXX_Size() int {
	return xxx_messageInfo_QueryCheckAudioStatusResponse.Size(m)
}
func (m *QueryCheckAudioStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryCheckAudioStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryCheckAudioStatusResponse proto.InternalMessageInfo

func (m *QueryCheckAudioStatusResponse) GetCheckStatus() uint32 {
	if m != nil {
		return m.CheckStatus
	}
	return 0
}

type OneKeyFindCoachDemand struct {
	PublishId               string   `protobuf:"bytes,1,opt,name=publish_id,json=publishId,proto3" json:"publish_id"`
	GameId                  uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	GameName                string   `protobuf:"bytes,3,opt,name=game_name,json=gameName,proto3" json:"game_name"`
	GameIcon                string   `protobuf:"bytes,4,opt,name=game_icon,json=gameIcon,proto3" json:"game_icon"`
	PlayerUid               uint32   `protobuf:"varint,5,opt,name=player_uid,json=playerUid,proto3" json:"player_uid"`
	PlayerGender            string   `protobuf:"bytes,6,opt,name=player_gender,json=playerGender,proto3" json:"player_gender"`
	PlayerTagList           []string `protobuf:"bytes,7,rep,name=player_tag_list,json=playerTagList,proto3" json:"player_tag_list"`
	TargetTagList           []string `protobuf:"bytes,8,rep,name=target_tag_list,json=targetTagList,proto3" json:"target_tag_list"`
	TargetCustomRequirement string   `protobuf:"bytes,9,opt,name=target_custom_requirement,json=targetCustomRequirement,proto3" json:"target_custom_requirement"`
	GrabbedCnt              uint32   `protobuf:"varint,10,opt,name=grabbed_cnt,json=grabbedCnt,proto3" json:"grabbed_cnt"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *OneKeyFindCoachDemand) Reset()         { *m = OneKeyFindCoachDemand{} }
func (m *OneKeyFindCoachDemand) String() string { return proto.CompactTextString(m) }
func (*OneKeyFindCoachDemand) ProtoMessage()    {}
func (*OneKeyFindCoachDemand) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{184}
}
func (m *OneKeyFindCoachDemand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneKeyFindCoachDemand.Unmarshal(m, b)
}
func (m *OneKeyFindCoachDemand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneKeyFindCoachDemand.Marshal(b, m, deterministic)
}
func (dst *OneKeyFindCoachDemand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneKeyFindCoachDemand.Merge(dst, src)
}
func (m *OneKeyFindCoachDemand) XXX_Size() int {
	return xxx_messageInfo_OneKeyFindCoachDemand.Size(m)
}
func (m *OneKeyFindCoachDemand) XXX_DiscardUnknown() {
	xxx_messageInfo_OneKeyFindCoachDemand.DiscardUnknown(m)
}

var xxx_messageInfo_OneKeyFindCoachDemand proto.InternalMessageInfo

func (m *OneKeyFindCoachDemand) GetPublishId() string {
	if m != nil {
		return m.PublishId
	}
	return ""
}

func (m *OneKeyFindCoachDemand) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *OneKeyFindCoachDemand) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *OneKeyFindCoachDemand) GetGameIcon() string {
	if m != nil {
		return m.GameIcon
	}
	return ""
}

func (m *OneKeyFindCoachDemand) GetPlayerUid() uint32 {
	if m != nil {
		return m.PlayerUid
	}
	return 0
}

func (m *OneKeyFindCoachDemand) GetPlayerGender() string {
	if m != nil {
		return m.PlayerGender
	}
	return ""
}

func (m *OneKeyFindCoachDemand) GetPlayerTagList() []string {
	if m != nil {
		return m.PlayerTagList
	}
	return nil
}

func (m *OneKeyFindCoachDemand) GetTargetTagList() []string {
	if m != nil {
		return m.TargetTagList
	}
	return nil
}

func (m *OneKeyFindCoachDemand) GetTargetCustomRequirement() string {
	if m != nil {
		return m.TargetCustomRequirement
	}
	return ""
}

func (m *OneKeyFindCoachDemand) GetGrabbedCnt() uint32 {
	if m != nil {
		return m.GrabbedCnt
	}
	return 0
}

type GrabOrderInfo struct {
	Demand               *OneKeyFindCoachDemand `protobuf:"bytes,1,opt,name=demand,proto3" json:"demand"`
	GrabType             uint32                 `protobuf:"varint,2,opt,name=grab_type,json=grabType,proto3" json:"grab_type"`
	GrabText             string                 `protobuf:"bytes,3,opt,name=grab_text,json=grabText,proto3" json:"grab_text"`
	GrabAudio            string                 `protobuf:"bytes,4,opt,name=grab_audio,json=grabAudio,proto3" json:"grab_audio"`
	GrabAudioDuration    uint32                 `protobuf:"varint,5,opt,name=grab_audio_duration,json=grabAudioDuration,proto3" json:"grab_audio_duration"`
	GrabStatus           uint32                 `protobuf:"varint,6,opt,name=grab_status,json=grabStatus,proto3" json:"grab_status"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GrabOrderInfo) Reset()         { *m = GrabOrderInfo{} }
func (m *GrabOrderInfo) String() string { return proto.CompactTextString(m) }
func (*GrabOrderInfo) ProtoMessage()    {}
func (*GrabOrderInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_http_f1b378bd6d2327e7, []int{185}
}
func (m *GrabOrderInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrabOrderInfo.Unmarshal(m, b)
}
func (m *GrabOrderInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrabOrderInfo.Marshal(b, m, deterministic)
}
func (dst *GrabOrderInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrabOrderInfo.Merge(dst, src)
}
func (m *GrabOrderInfo) XXX_Size() int {
	return xxx_messageInfo_GrabOrderInfo.Size(m)
}
func (m *GrabOrderInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GrabOrderInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GrabOrderInfo proto.InternalMessageInfo

func (m *GrabOrderInfo) GetDemand() *OneKeyFindCoachDemand {
	if m != nil {
		return m.Demand
	}
	return nil
}

func (m *GrabOrderInfo) GetGrabType() uint32 {
	if m != nil {
		return m.GrabType
	}
	return 0
}

func (m *GrabOrderInfo) GetGrabText() string {
	if m != nil {
		return m.GrabText
	}
	return ""
}

func (m *GrabOrderInfo) GetGrabAudio() string {
	if m != nil {
		return m.GrabAudio
	}
	return ""
}

func (m *GrabOrderInfo) GetGrabAudioDuration() uint32 {
	if m != nil {
		return m.GrabAudioDuration
	}
	return 0
}

func (m *GrabOrderInfo) GetGrabStatus() uint32 {
	if m != nil {
		return m.GrabStatus
	}
	return 0
}

func init() {
	proto.RegisterType((*SectionInfo)(nil), "esport_http.SectionInfo")
	proto.RegisterType((*UserSkillInfo)(nil), "esport_http.UserSkillInfo")
	proto.RegisterType((*AddUserSkillRequest)(nil), "esport_http.AddUserSkillRequest")
	proto.RegisterType((*AddUserSkillResponse)(nil), "esport_http.AddUserSkillResponse")
	proto.RegisterType((*GetUserCurrentSkillRequest)(nil), "esport_http.GetUserCurrentSkillRequest")
	proto.RegisterType((*GetUserCurrentSkillResponse)(nil), "esport_http.GetUserCurrentSkillResponse")
	proto.RegisterType((*GetUserSkillByGameIdRequest)(nil), "esport_http.GetUserSkillByGameIdRequest")
	proto.RegisterType((*GetUserSkillByGameIdResponse)(nil), "esport_http.GetUserSkillByGameIdResponse")
	proto.RegisterType((*ModifyUserSkillRequest)(nil), "esport_http.ModifyUserSkillRequest")
	proto.RegisterType((*ModifyUserSkillResponse)(nil), "esport_http.ModifyUserSkillResponse")
	proto.RegisterType((*DelUserSkillRequest)(nil), "esport_http.DelUserSkillRequest")
	proto.RegisterType((*DelUserSkillResponse)(nil), "esport_http.DelUserSkillResponse")
	proto.RegisterType((*GetESportMasterInfoRequest)(nil), "esport_http.GetESportMasterInfoRequest")
	proto.RegisterType((*GetESportMasterInfoResponse)(nil), "esport_http.GetESportMasterInfoResponse")
	proto.RegisterType((*TradeData)(nil), "esport_http.TradeData")
	proto.RegisterType((*TradeStat)(nil), "esport_http.TradeStat")
	proto.RegisterType((*CoachLabel)(nil), "esport_http.CoachLabel")
	proto.RegisterType((*BatchGetAuditSkillRequest)(nil), "esport_http.BatchGetAuditSkillRequest")
	proto.RegisterType((*AuditSkillRecord)(nil), "esport_http.AuditSkillRecord")
	proto.RegisterType((*BatchGetAuditSkillResponse)(nil), "esport_http.BatchGetAuditSkillResponse")
	proto.RegisterType((*SetUserSkillAuditTypeRequest)(nil), "esport_http.SetUserSkillAuditTypeRequest")
	proto.RegisterType((*SetUserSkillAuditTypeResponse)(nil), "esport_http.SetUserSkillAuditTypeResponse")
	proto.RegisterType((*ESportGameSimpleInfo)(nil), "esport_http.ESportGameSimpleInfo")
	proto.RegisterType((*ESportGameTypeInfo)(nil), "esport_http.ESportGameTypeInfo")
	proto.RegisterType((*GetESportGameListRequest)(nil), "esport_http.GetESportGameListRequest")
	proto.RegisterType((*GetESportGameListResponse)(nil), "esport_http.GetESportGameListResponse")
	proto.RegisterType((*ApplyRiskCheckRequest)(nil), "esport_http.ApplyRiskCheckRequest")
	proto.RegisterType((*ApplyRiskCheckResponse)(nil), "esport_http.ApplyRiskCheckResponse")
	proto.RegisterType((*GetEsportGameListWithAuditRequest)(nil), "esport_http.GetEsportGameListWithAuditRequest")
	proto.RegisterType((*GetEsportGameListWithAuditResponse)(nil), "esport_http.GetEsportGameListWithAuditResponse")
	proto.RegisterType((*SelfIntroConf)(nil), "esport_http.SelfIntroConf")
	proto.RegisterType((*GameInformation)(nil), "esport_http.GameInformation")
	proto.RegisterType((*UserGameInfo)(nil), "esport_http.UserGameInfo")
	proto.RegisterType((*GameSkillConf)(nil), "esport_http.GameSkillConf")
	proto.RegisterType((*GetESportGameSkillConfRequest)(nil), "esport_http.GetESportGameSkillConfRequest")
	proto.RegisterType((*GetESportGameSkillConfResponse)(nil), "esport_http.GetESportGameSkillConfResponse")
	proto.RegisterType((*ApplyESportRoleReq)(nil), "esport_http.ApplyESportRoleReq")
	proto.RegisterType((*ApplyESportRoleResp)(nil), "esport_http.ApplyESportRoleResp")
	proto.RegisterType((*CreateRefundRequest)(nil), "esport_http.CreateRefundRequest")
	proto.RegisterType((*RejectRefundRequest)(nil), "esport_http.RejectRefundRequest")
	proto.RegisterType((*RefundResponse)(nil), "esport_http.RefundResponse")
	proto.RegisterType((*GetRefundReasonsRequest)(nil), "esport_http.GetRefundReasonsRequest")
	proto.RegisterType((*GetRefundReasonsResponse)(nil), "esport_http.GetRefundReasonsResponse")
	proto.RegisterType((*GetRefundReasonsResponse_ReasonWithFastRefund)(nil), "esport_http.GetRefundReasonsResponse.ReasonWithFastRefund")
	proto.RegisterType((*GetRejectReasonsRequest)(nil), "esport_http.GetRejectReasonsRequest")
	proto.RegisterType((*GetRejectReasonsResponse)(nil), "esport_http.GetRejectReasonsResponse")
	proto.RegisterType((*InitiateAppealRequest)(nil), "esport_http.InitiateAppealRequest")
	proto.RegisterType((*InitiateAppealResponse)(nil), "esport_http.InitiateAppealResponse")
	proto.RegisterType((*SubmitGuideAppealInfoRequest)(nil), "esport_http.SubmitGuideAppealInfoRequest")
	proto.RegisterType((*SubmitGuideAppealInfoResponse)(nil), "esport_http.SubmitGuideAppealInfoResponse")
	proto.RegisterType((*GetReceiveTimeFrameRequest)(nil), "esport_http.GetReceiveTimeFrameRequest")
	proto.RegisterType((*GetReceiveTimeFrameResponse)(nil), "esport_http.GetReceiveTimeFrameResponse")
	proto.RegisterType((*SetReceiveTimeFrameRequest)(nil), "esport_http.SetReceiveTimeFrameRequest")
	proto.RegisterType((*SetReceiveTimeFrameResponse)(nil), "esport_http.SetReceiveTimeFrameResponse")
	proto.RegisterType((*SetSkillReceiveSwitchRequest)(nil), "esport_http.SetSkillReceiveSwitchRequest")
	proto.RegisterType((*SetSkillReceiveSwitchResponse)(nil), "esport_http.SetSkillReceiveSwitchResponse")
	proto.RegisterType((*CheckIfCouldOpenSwitchRequest)(nil), "esport_http.CheckIfCouldOpenSwitchRequest")
	proto.RegisterType((*CheckIfCouldOpenSwitchResponse)(nil), "esport_http.CheckIfCouldOpenSwitchResponse")
	proto.RegisterType((*SetSkillPriceRequest)(nil), "esport_http.SetSkillPriceRequest")
	proto.RegisterType((*SetSkillPriceResponse)(nil), "esport_http.SetSkillPriceResponse")
	proto.RegisterType((*GodPageSkill)(nil), "esport_http.GodPageSkill")
	proto.RegisterType((*GodPageSkill_GuaranteeInfo)(nil), "esport_http.GodPageSkill.GuaranteeInfo")
	proto.RegisterType((*PriceInfo)(nil), "esport_http.PriceInfo")
	proto.RegisterType((*GetCoachDetailRequest)(nil), "esport_http.GetCoachDetailRequest")
	proto.RegisterType((*GetCoachDetailResponse)(nil), "esport_http.GetCoachDetailResponse")
	proto.RegisterType((*CoachDetail)(nil), "esport_http.CoachDetail")
	proto.RegisterType((*CoachChannelInfo)(nil), "esport_http.CoachChannelInfo")
	proto.RegisterType((*GameProperty)(nil), "esport_http.GameProperty")
	proto.RegisterType((*GamePropertyVal)(nil), "esport_http.GamePropertyVal")
	proto.RegisterType((*GetOrderDetailRequest)(nil), "esport_http.GetOrderDetailRequest")
	proto.RegisterType((*GetOrderDetailResponse)(nil), "esport_http.GetOrderDetailResponse")
	proto.RegisterType((*SkillProductOrderDetail)(nil), "esport_http.SkillProductOrderDetail")
	proto.RegisterType((*ProductOrderPriceInfo)(nil), "esport_http.ProductOrderPriceInfo")
	proto.RegisterType((*ProductOrder)(nil), "esport_http.ProductOrder")
	proto.RegisterType((*CouponUseDetail)(nil), "esport_http.CouponUseDetail")
	proto.RegisterType((*NewCustomerUseDetail)(nil), "esport_http.NewCustomerUseDetail")
	proto.RegisterType((*OrderRefund)(nil), "esport_http.OrderRefund")
	proto.RegisterType((*ManualAddESportCoachRoleRequest)(nil), "esport_http.ManualAddESportCoachRoleRequest")
	proto.RegisterType((*ManualAddESportCoachRoleResponse)(nil), "esport_http.ManualAddESportCoachRoleResponse")
	proto.RegisterType((*GetCoachStatisticsRequest)(nil), "esport_http.GetCoachStatisticsRequest")
	proto.RegisterType((*GetCoachStatisticsResponse)(nil), "esport_http.GetCoachStatisticsResponse")
	proto.RegisterType((*EvaluateWordCnt)(nil), "esport_http.EvaluateWordCnt")
	proto.RegisterType((*EvaluateScore)(nil), "esport_http.EvaluateScore")
	proto.RegisterType((*EvaluateSummary)(nil), "esport_http.EvaluateSummary")
	proto.RegisterType((*UserInfo)(nil), "esport_http.UserInfo")
	proto.RegisterType((*EvaluateInfo)(nil), "esport_http.EvaluateInfo")
	proto.RegisterType((*GetEvaluateListRequest)(nil), "esport_http.GetEvaluateListRequest")
	proto.RegisterType((*GetEvaluateListResponse)(nil), "esport_http.GetEvaluateListResponse")
	proto.RegisterType((*CheckCanReportCoachRequest)(nil), "esport_http.CheckCanReportCoachRequest")
	proto.RegisterType((*CheckCanReportCoachResponse)(nil), "esport_http.CheckCanReportCoachResponse")
	proto.RegisterType((*SetQuickReceiveSwitchRequest)(nil), "esport_http.SetQuickReceiveSwitchRequest")
	proto.RegisterType((*SetQuickReceiveSwitchResponse)(nil), "esport_http.SetQuickReceiveSwitchResponse")
	proto.RegisterType((*GetQuickReceiveSwitchRequest)(nil), "esport_http.GetQuickReceiveSwitchRequest")
	proto.RegisterType((*GetQuickReceiveSwitchResponse)(nil), "esport_http.GetQuickReceiveSwitchResponse")
	proto.RegisterType((*GetCurPricingInfoRequest)(nil), "esport_http.GetCurPricingInfoRequest")
	proto.RegisterType((*GetCurPricingInfoResponse)(nil), "esport_http.GetCurPricingInfoResponse")
	proto.RegisterType((*SetPricingLevelRequest)(nil), "esport_http.SetPricingLevelRequest")
	proto.RegisterType((*SetPricingLevelResponse)(nil), "esport_http.SetPricingLevelResponse")
	proto.RegisterType((*GetApplicableLabelsRequest)(nil), "esport_http.GetApplicableLabelsRequest")
	proto.RegisterType((*GetApplicableLabelsResponse)(nil), "esport_http.GetApplicableLabelsResponse")
	proto.RegisterType((*PricingLevel)(nil), "esport_http.PricingLevel")
	proto.RegisterType((*Label)(nil), "esport_http.Label")
	proto.RegisterType((*SetLabelPriceSwitchRequest)(nil), "esport_http.SetLabelPriceSwitchRequest")
	proto.RegisterType((*SetLabelPriceSwitchResponse)(nil), "esport_http.SetLabelPriceSwitchResponse")
	proto.RegisterType((*GodPrivilege)(nil), "esport_http.GodPrivilege")
	proto.RegisterType((*GodLevelKpi)(nil), "esport_http.GodLevelKpi")
	proto.RegisterType((*GodLevel)(nil), "esport_http.GodLevel")
	proto.RegisterType((*GetGodLevelRequest)(nil), "esport_http.GetGodLevelRequest")
	proto.RegisterType((*GetGodLevelResponse)(nil), "esport_http.GetGodLevelResponse")
	proto.RegisterType((*GetGodLevelDetailRequest)(nil), "esport_http.GetGodLevelDetailRequest")
	proto.RegisterType((*GetGodLevelDetailResponse)(nil), "esport_http.GetGodLevelDetailResponse")
	proto.RegisterType((*CheckFastRefundPermissionRequest)(nil), "esport_http.CheckFastRefundPermissionRequest")
	proto.RegisterType((*CheckFastRefundPermissionResponse)(nil), "esport_http.CheckFastRefundPermissionResponse")
	proto.RegisterType((*CreateFastRefundRequest)(nil), "esport_http.CreateFastRefundRequest")
	proto.RegisterType((*SetGameGuaranteeStatusRequest)(nil), "esport_http.SetGameGuaranteeStatusRequest")
	proto.RegisterType((*SetGameGuaranteeStatusResponse)(nil), "esport_http.SetGameGuaranteeStatusResponse")
	proto.RegisterType((*ReportGameCardVisitRequest)(nil), "esport_http.ReportGameCardVisitRequest")
	proto.RegisterType((*ReportGameCardVisitResponse)(nil), "esport_http.ReportGameCardVisitResponse")
	proto.RegisterType((*GetBeVisitorRecordCountReq)(nil), "esport_http.GetBeVisitorRecordCountReq")
	proto.RegisterType((*GetBeVisitorRecordCountResp)(nil), "esport_http.GetBeVisitorRecordCountResp")
	proto.RegisterType((*VisitRecord)(nil), "esport_http.VisitRecord")
	proto.RegisterType((*GetBeVisitorRecordListReq)(nil), "esport_http.GetBeVisitorRecordListReq")
	proto.RegisterType((*GetBeVisitorRecordListResp)(nil), "esport_http.GetBeVisitorRecordListResp")
	proto.RegisterType((*ReportEnterIMPageRequest)(nil), "esport_http.ReportEnterIMPageRequest")
	proto.RegisterType((*ReportEnterIMPageResponse)(nil), "esport_http.ReportEnterIMPageResponse")
	proto.RegisterType((*GetRecommandCoachListRequest)(nil), "esport_http.GetRecommandCoachListRequest")
	proto.RegisterType((*GetRecommandCoachListResponse)(nil), "esport_http.GetRecommandCoachListResponse")
	proto.RegisterType((*EsportAreaCoachInfo)(nil), "esport_http.EsportAreaCoachInfo")
	proto.RegisterType((*UserProfile)(nil), "esport_http.UserProfile")
	proto.RegisterType((*GetFirstRoundDiscountInfoRequest)(nil), "esport_http.GetFirstRoundDiscountInfoRequest")
	proto.RegisterType((*GetFirstRoundDiscountInfoResponse)(nil), "esport_http.GetFirstRoundDiscountInfoResponse")
	proto.RegisterType((*SetFirstRoundSwitchRequest)(nil), "esport_http.SetFirstRoundSwitchRequest")
	proto.RegisterType((*SetFirstRoundSwitchResponse)(nil), "esport_http.SetFirstRoundSwitchResponse")
	proto.RegisterType((*GetFirstRoundDiscountGameListRequest)(nil), "esport_http.GetFirstRoundDiscountGameListRequest")
	proto.RegisterType((*FirstRoundDiscountGameInfo)(nil), "esport_http.FirstRoundDiscountGameInfo")
	proto.RegisterType((*GetFirstRoundDiscountGameListResponse)(nil), "esport_http.GetFirstRoundDiscountGameListResponse")
	proto.RegisterType((*RefreshDiscountInfoRequest)(nil), "esport_http.RefreshDiscountInfoRequest")
	proto.RegisterType((*RefreshDiscountInfoResponse)(nil), "esport_http.RefreshDiscountInfoResponse")
	proto.RegisterType((*GetNewCustomerDiscountInfoRequest)(nil), "esport_http.GetNewCustomerDiscountInfoRequest")
	proto.RegisterType((*GetNewCustomerDiscountInfoResponse)(nil), "esport_http.GetNewCustomerDiscountInfoResponse")
	proto.RegisterType((*SetNewCustomerSwitchRequest)(nil), "esport_http.SetNewCustomerSwitchRequest")
	proto.RegisterType((*SetNewCustomerSwitchResponse)(nil), "esport_http.SetNewCustomerSwitchResponse")
	proto.RegisterType((*GetCoachIncentiveTaskInfoRequest)(nil), "esport_http.GetCoachIncentiveTaskInfoRequest")
	proto.RegisterType((*GetCoachIncentiveTaskInfoResponse)(nil), "esport_http.GetCoachIncentiveTaskInfoResponse")
	proto.RegisterType((*HostingOperationRequest)(nil), "esport_http.HostingOperationRequest")
	proto.RegisterType((*HostingOperationResponse)(nil), "esport_http.HostingOperationResponse")
	proto.RegisterType((*GetHostingStatusRequest)(nil), "esport_http.GetHostingStatusRequest")
	proto.RegisterType((*GetHostingStatusResponse)(nil), "esport_http.GetHostingStatusResponse")
	proto.RegisterType((*ContactCustomerServiceRequest)(nil), "esport_http.ContactCustomerServiceRequest")
	proto.RegisterType((*ContactCustomerServiceResponse)(nil), "esport_http.ContactCustomerServiceResponse")
	proto.RegisterType((*GetGuildOrderListForCustomerRequest)(nil), "esport_http.GetGuildOrderListForCustomerRequest")
	proto.RegisterType((*OrderSimpleInfo)(nil), "esport_http.OrderSimpleInfo")
	proto.RegisterType((*GetGuildOrderListForCustomerResponse)(nil), "esport_http.GetGuildOrderListForCustomerResponse")
	proto.RegisterType((*GetTopGameListRequest)(nil), "esport_http.GetTopGameListRequest")
	proto.RegisterType((*GameItem)(nil), "esport_http.GameItem")
	proto.RegisterType((*GetTopGameListResponse)(nil), "esport_http.GetTopGameListResponse")
	proto.RegisterType((*GetCoachListForCustomerRequest)(nil), "esport_http.GetCoachListForCustomerRequest")
	proto.RegisterType((*GetCoachListForCustomerResponse)(nil), "esport_http.GetCoachListForCustomerResponse")
	proto.RegisterType((*SendCoachSkillCardRequest)(nil), "esport_http.SendCoachSkillCardRequest")
	proto.RegisterType((*SendCoachSkillCardResponse)(nil), "esport_http.SendCoachSkillCardResponse")
	proto.RegisterType((*GetCouponPackageRequest)(nil), "esport_http.GetCouponPackageRequest")
	proto.RegisterType((*GetCouponPackageResponse)(nil), "esport_http.GetCouponPackageResponse")
	proto.RegisterType((*GetAvailableCouponRequest)(nil), "esport_http.GetAvailableCouponRequest")
	proto.RegisterType((*GetAvailableCouponResponse)(nil), "esport_http.GetAvailableCouponResponse")
	proto.RegisterType((*CouponDetail)(nil), "esport_http.CouponDetail")
	proto.RegisterType((*ReportExposeCoachRequest)(nil), "esport_http.ReportExposeCoachRequest")
	proto.RegisterType((*ReportExposeCoachResponse)(nil), "esport_http.ReportExposeCoachResponse")
	proto.RegisterType((*GetActivityGamePropertyRequest)(nil), "esport_http.GetActivityGamePropertyRequest")
	proto.RegisterType((*GetActivityGamePropertyResponse)(nil), "esport_http.GetActivityGamePropertyResponse")
	proto.RegisterType((*GetCoachMissionInfoPageRequest)(nil), "esport_http.GetCoachMissionInfoPageRequest")
	proto.RegisterType((*MasterSwitch)(nil), "esport_http.MasterSwitch")
	proto.RegisterType((*GetCoachMissionInfoPageResponse)(nil), "esport_http.GetCoachMissionInfoPageResponse")
	proto.RegisterType((*GetIncentiveTaskEntrySwitchRequest)(nil), "esport_http.GetIncentiveTaskEntrySwitchRequest")
	proto.RegisterType((*GetIncentiveTaskEntrySwitchResponse)(nil), "esport_http.GetIncentiveTaskEntrySwitchResponse")
	proto.RegisterType((*GetGrabCenterOverviewRequest)(nil), "esport_http.GetGrabCenterOverviewRequest")
	proto.RegisterType((*GetGrabCenterOverviewResponse)(nil), "esport_http.GetGrabCenterOverviewResponse")
	proto.RegisterType((*GetPendingGrabOrderListRequest)(nil), "esport_http.GetPendingGrabOrderListRequest")
	proto.RegisterType((*GetPendingGrabOrderListResponse)(nil), "esport_http.GetPendingGrabOrderListResponse")
	proto.RegisterType((*GetGrabbedOrderListRequest)(nil), "esport_http.GetGrabbedOrderListRequest")
	proto.RegisterType((*GetGrabbedOrderListResponse)(nil), "esport_http.GetGrabbedOrderListResponse")
	proto.RegisterType((*GrabOrderRequest)(nil), "esport_http.GrabOrderRequest")
	proto.RegisterType((*GrabOrderResponse)(nil), "esport_http.GrabOrderResponse")
	proto.RegisterType((*AsyncCheckAudioRequest)(nil), "esport_http.AsyncCheckAudioRequest")
	proto.RegisterType((*AsyncCheckAudioResponse)(nil), "esport_http.AsyncCheckAudioResponse")
	proto.RegisterType((*QueryCheckAudioStatusRequest)(nil), "esport_http.QueryCheckAudioStatusRequest")
	proto.RegisterType((*QueryCheckAudioStatusResponse)(nil), "esport_http.QueryCheckAudioStatusResponse")
	proto.RegisterType((*OneKeyFindCoachDemand)(nil), "esport_http.OneKeyFindCoachDemand")
	proto.RegisterType((*GrabOrderInfo)(nil), "esport_http.GrabOrderInfo")
	proto.RegisterEnum("esport_http.GAME_TYPE", GAME_TYPE_name, GAME_TYPE_value)
	proto.RegisterEnum("esport_http.SelectType", SelectType_name, SelectType_value)
	proto.RegisterEnum("esport_http.ESportErType", ESportErType_name, ESportErType_value)
	proto.RegisterEnum("esport_http.ESportStatType", ESportStatType_name, ESportStatType_value)
	proto.RegisterEnum("esport_http.LabelSourceType", LabelSourceType_name, LabelSourceType_value)
	proto.RegisterEnum("esport_http.AuditSearchType", AuditSearchType_name, AuditSearchType_value)
	proto.RegisterEnum("esport_http.ESportApplyType", ESportApplyType_name, ESportApplyType_value)
	proto.RegisterEnum("esport_http.RefundType", RefundType_name, RefundType_value)
	proto.RegisterEnum("esport_http.FreezeType", FreezeType_name, FreezeType_value)
	proto.RegisterEnum("esport_http.DiscountType", DiscountType_name, DiscountType_value)
	proto.RegisterEnum("esport_http.LabelType", LabelType_name, LabelType_value)
	proto.RegisterEnum("esport_http.HostingOperation", HostingOperation_name, HostingOperation_value)
	proto.RegisterEnum("esport_http.TabType", TabType_name, TabType_value)
	proto.RegisterEnum("esport_http.RefundStatus", RefundStatus_name, RefundStatus_value)
	proto.RegisterEnum("esport_http.CanceledOrderSubStatus", CanceledOrderSubStatus_name, CanceledOrderSubStatus_value)
	proto.RegisterEnum("esport_http.OrderStatus", OrderStatus_name, OrderStatus_value)
	proto.RegisterEnum("esport_http.MasterSwitchType", MasterSwitchType_name, MasterSwitchType_value)
	proto.RegisterEnum("esport_http.GrabType", GrabType_name, GrabType_value)
	proto.RegisterEnum("esport_http.GrabStatus", GrabStatus_name, GrabStatus_value)
	proto.RegisterEnum("esport_http.GameInformation_GAME_INFORMATION_TYPE", GameInformation_GAME_INFORMATION_TYPE_name, GameInformation_GAME_INFORMATION_TYPE_value)
	proto.RegisterEnum("esport_http.GameInformation_GAME_INFORMATION_SELECT_TYPE", GameInformation_GAME_INFORMATION_SELECT_TYPE_name, GameInformation_GAME_INFORMATION_SELECT_TYPE_value)
	proto.RegisterEnum("esport_http.GameProperty_PropertyType", GameProperty_PropertyType_name, GameProperty_PropertyType_value)
	proto.RegisterEnum("esport_http.GameProperty_SelectType", GameProperty_SelectType_name, GameProperty_SelectType_value)
	proto.RegisterEnum("esport_http.ReportExposeCoachRequest_ExposeCoachType", ReportExposeCoachRequest_ExposeCoachType_name, ReportExposeCoachRequest_ExposeCoachType_value)
	proto.RegisterEnum("esport_http.QueryCheckAudioStatusResponse_CheckStatus", QueryCheckAudioStatusResponse_CheckStatus_name, QueryCheckAudioStatusResponse_CheckStatus_value)
}

func init() {
	proto.RegisterFile("tt/quicksilver/esport-http/esport-http.proto", fileDescriptor_esport_http_f1b378bd6d2327e7)
}

var fileDescriptor_esport_http_f1b378bd6d2327e7 = []byte{
	// 9421 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x7d, 0x5d, 0x6f, 0x23, 0x59,
	0x76, 0xd8, 0x92, 0xd4, 0x07, 0x79, 0x28, 0x4a, 0xec, 0x52, 0xeb, 0xb3, 0xa5, 0xfe, 0xa8, 0xee,
	0x9e, 0xe9, 0xd5, 0xce, 0xf4, 0x6e, 0x7a, 0xd2, 0x3b, 0x33, 0x3b, 0xde, 0x1d, 0xb3, 0x29, 0x4a,
	0xcd, 0x1d, 0x7d, 0x70, 0x8b, 0x54, 0xcf, 0xf4, 0xc2, 0x46, 0xa5, 0xc4, 0xba, 0xa2, 0x6a, 0x45,
	0x56, 0x71, 0xaa, 0x8a, 0x52, 0x6b, 0x1f, 0x02, 0x23, 0x89, 0xed, 0x35, 0x90, 0x35, 0x82, 0x04,
	0x58, 0x38, 0x40, 0x60, 0x3f, 0x25, 0x2f, 0x7e, 0x8a, 0x61, 0xc4, 0x49, 0x10, 0x04, 0x88, 0x9f,
	0x92, 0x17, 0xc3, 0x40, 0x92, 0x45, 0x12, 0xe4, 0x0f, 0x04, 0x01, 0x02, 0xe4, 0x21, 0x2f, 0xc9,
	0x83, 0x83, 0x7b, 0xce, 0xbd, 0x55, 0xb7, 0x8a, 0x45, 0x49, 0x33, 0xb3, 0x9b, 0xd8, 0x6f, 0xac,
	0x73, 0xce, 0xfd, 0x3e, 0xf7, 0xdc, 0xf3, 0x75, 0x2f, 0xe1, 0x9d, 0x30, 0xfc, 0xe6, 0xe7, 0x23,
	0xa7, 0x7b, 0x16, 0x38, 0xfd, 0x73, 0xe6, 0x7f, 0x93, 0x05, 0x43, 0xcf, 0x0f, 0xdf, 0x3d, 0x0d,
	0xc3, 0xa1, 0xfa, 0xfb, 0xe9, 0xd0, 0xf7, 0x42, 0x4f, 0x2b, 0x13, 0xc8, 0xe4, 0xa0, 0xf5, 0x49,
	0x45, 0xad, 0x7e, 0x5f, 0xfd, 0x4d, 0x45, 0xf5, 0x3e, 0x94, 0xdb, 0xac, 0x1b, 0x3a, 0x9e, 0xdb,
	0x74, 0x4f, 0x3c, 0xed, 0x01, 0xcc, 0x05, 0xf4, 0x69, 0xba, 0xd6, 0x80, 0xad, 0xe6, 0xee, 0xe7,
	0x9e, 0x94, 0x8c, 0xb2, 0x80, 0x1d, 0x58, 0x03, 0xa6, 0xdd, 0x81, 0x92, 0x13, 0xb2, 0x81, 0xd9,
	0x77, 0x82, 0x70, 0x35, 0x7f, 0xbf, 0xf0, 0xa4, 0x64, 0x14, 0x39, 0x60, 0xcf, 0x09, 0x42, 0x6d,
	0x13, 0x40, 0x96, 0x77, 0xec, 0xd5, 0xc2, 0xfd, 0xdc, 0x93, 0x8a, 0x51, 0x12, 0x90, 0xa6, 0xad,
	0xff, 0xb7, 0x3c, 0x54, 0x8e, 0x02, 0xe6, 0xb7, 0xcf, 0x9c, 0x7e, 0x1f, 0x1b, 0x5c, 0x81, 0xd9,
	0x9e, 0x35, 0x60, 0x9c, 0x3a, 0x87, 0xd4, 0x33, 0xfc, 0xb3, 0x69, 0xf3, 0x66, 0x10, 0x81, 0xdd,
	0xc8, 0x63, 0x37, 0x8a, 0x1c, 0x80, 0x7d, 0x78, 0x0c, 0xf3, 0x01, 0xaf, 0xc2, 0x64, 0xe7, 0x8e,
	0xcd, 0xdc, 0x2e, 0xc3, 0xa6, 0x4a, 0x46, 0x05, 0xa1, 0x0d, 0x01, 0xc4, 0xde, 0x20, 0x99, 0xcd,
	0x82, 0xee, 0xea, 0x14, 0x92, 0x94, 0x10, 0xb2, 0xcd, 0x82, 0xae, 0x76, 0x1b, 0xa6, 0xad, 0x91,
	0xed, 0x78, 0xab, 0xd3, 0x88, 0xa1, 0x0f, 0x5e, 0x37, 0xfe, 0x30, 0xed, 0x91, 0x6f, 0xf1, 0x7e,
	0xaf, 0xce, 0x60, 0xc7, 0x2a, 0x08, 0xdd, 0x16, 0x40, 0xed, 0xa3, 0x78, 0xa6, 0x70, 0x26, 0x66,
	0xef, 0x17, 0x9e, 0x94, 0x9f, 0xad, 0x3e, 0x55, 0x96, 0xe2, 0xa9, 0x32, 0xb3, 0xd1, 0x1c, 0xe2,
	0x34, 0xdd, 0x81, 0x52, 0xc8, 0xde, 0x84, 0xd4, 0xaf, 0x22, 0x0d, 0x8e, 0x03, 0xb0, 0x5b, 0x72,
	0xe4, 0xbe, 0xe5, 0x9e, 0xad, 0x96, 0xb0, 0x6d, 0x1c, 0xb9, 0x61, 0xb9, 0x67, 0xda, 0x13, 0xa8,
	0x3a, 0x81, 0xd9, 0x1b, 0x59, 0xbe, 0xe5, 0x86, 0x8c, 0x99, 0x17, 0x8e, 0xbb, 0x0a, 0xf7, 0x73,
	0x4f, 0x8a, 0xc6, 0xbc, 0x13, 0xec, 0x4a, 0xf0, 0xa7, 0x8e, 0xab, 0xef, 0xc2, 0x62, 0xcd, 0xb6,
	0xa3, 0xd9, 0x36, 0xd8, 0xe7, 0x23, 0x16, 0x84, 0xda, 0xb7, 0x60, 0x1a, 0x67, 0x00, 0xa7, 0xbb,
	0xfc, 0x6c, 0x3d, 0xd1, 0xe1, 0xc4, 0xda, 0x18, 0x44, 0xa8, 0x2f, 0xc3, 0xed, 0x64, 0x45, 0xc1,
	0xd0, 0x73, 0x03, 0xa6, 0x7f, 0x04, 0xeb, 0xbb, 0x2c, 0xe4, 0xf0, 0xfa, 0xc8, 0xf7, 0x99, 0x1b,
	0x26, 0xda, 0xd9, 0x04, 0x08, 0x2d, 0xbf, 0xc7, 0x42, 0x73, 0x14, 0xad, 0x6d, 0x89, 0x20, 0x47,
	0x8e, 0xad, 0xff, 0x34, 0x07, 0x77, 0x32, 0x4b, 0x53, 0xe5, 0xd7, 0x14, 0x8f, 0x47, 0x91, 0xc7,
	0x69, 0xbf, 0x7e, 0x14, 0xbc, 0x42, 0xbe, 0x80, 0xa1, 0x19, 0x5e, 0x0e, 0x99, 0xe4, 0x4c, 0x84,
	0x74, 0x2e, 0x87, 0x4c, 0x3f, 0x8a, 0xba, 0x83, 0x25, 0x5f, 0x5c, 0xee, 0x22, 0x1b, 0xde, 0x6c,
	0x34, 0x2a, 0x17, 0xe7, 0x55, 0x2e, 0xd6, 0xff, 0x38, 0x0f, 0x1b, 0xd9, 0xf5, 0xde, 0x6c, 0x9c,
	0x93, 0x2a, 0xd6, 0x3e, 0x82, 0x32, 0x0d, 0x87, 0xa6, 0xa1, 0x70, 0xed, 0x34, 0xd0, 0xe8, 0xf1,
	0x5b, 0xfb, 0x18, 0x2a, 0x5d, 0x9a, 0x74, 0x51, 0x7c, 0xea, 0x5a, 0x5e, 0x98, 0xeb, 0x2a, 0xab,
	0x94, 0x9a, 0xcc, 0xe9, 0xd4, 0x64, 0x6a, 0x1f, 0xc3, 0xc6, 0xa9, 0x95, 0xe2, 0x52, 0x73, 0xc8,
	0xfc, 0x81, 0x13, 0x04, 0x72, 0x43, 0x15, 0x8d, 0xb5, 0x53, 0x2b, 0xc1, 0xb1, 0xad, 0x88, 0x40,
	0xff, 0x3e, 0x2c, 0xef, 0x7b, 0xb6, 0x73, 0x72, 0xf9, 0x0b, 0x60, 0xdf, 0x35, 0x58, 0x19, 0xab,
	0x4b, 0x70, 0xf0, 0x53, 0x58, 0xdc, 0x66, 0xfd, 0xb1, 0x36, 0x26, 0xc9, 0x24, 0xbe, 0x13, 0x92,
	0xf4, 0x89, 0x9d, 0xd0, 0x68, 0xf3, 0x9e, 0xec, 0x5b, 0x41, 0xc8, 0x7c, 0xec, 0xc0, 0xcd, 0x76,
	0xc2, 0xbf, 0x9e, 0x42, 0xd6, 0x1b, 0x2f, 0x7d, 0x33, 0x0e, 0x59, 0x85, 0x59, 0xab, 0xdb, 0xf5,
	0x46, 0x6e, 0x28, 0xa4, 0xa4, 0xfc, 0xe4, 0x72, 0xc4, 0x75, 0xba, 0x67, 0x24, 0x41, 0x49, 0x3e,
	0x16, 0x39, 0x00, 0x25, 0xa8, 0x06, 0x53, 0x61, 0xe8, 0xd8, 0x42, 0x28, 0xe2, 0x6f, 0xed, 0x1e,
	0xc8, 0x83, 0xc4, 0xf7, 0xfa, 0x72, 0x59, 0x81, 0x40, 0x86, 0xd7, 0x67, 0xda, 0x1a, 0x14, 0x7b,
	0x23, 0xa7, 0x6f, 0xf3, 0x99, 0x21, 0xa1, 0x38, 0x8b, 0xdf, 0x4d, 0x9b, 0xf7, 0x92, 0x50, 0xd8,
	0xda, 0x2c, 0x89, 0x5a, 0x84, 0x60, 0x73, 0xcb, 0x30, 0x63, 0x3b, 0x5c, 0x2c, 0xa3, 0xb4, 0xab,
	0x18, 0xe2, 0x4b, 0x7b, 0x0f, 0x4a, 0x41, 0x68, 0x85, 0x24, 0x42, 0x4b, 0xc8, 0xc4, 0xcb, 0x89,
	0x25, 0xed, 0xf8, 0x96, 0xcd, 0xda, 0xa1, 0x15, 0x1a, 0x45, 0x4e, 0x88, 0xd2, 0xf3, 0x03, 0x29,
	0xd6, 0xb1, 0x14, 0x60, 0xa9, 0xb5, 0x44, 0xa9, 0x5d, 0xcf, 0x6e, 0x59, 0x3d, 0x46, 0xab, 0x44,
	0x12, 0x1f, 0x4b, 0xde, 0x85, 0xf2, 0x29, 0xb3, 0x6c, 0xd3, 0xbe, 0x34, 0x07, 0xf6, 0xf3, 0xd5,
	0x32, 0x75, 0x93, 0x83, 0xb6, 0x2f, 0xf7, 0xed, 0xe7, 0xda, 0xbb, 0x30, 0xdd, 0xb7, 0x8e, 0x59,
	0x7f, 0x75, 0x0e, 0xb9, 0x6b, 0x25, 0x51, 0x69, 0xdd, 0xb3, 0xba, 0xa7, 0x7b, 0x1c, 0x6d, 0x10,
	0x15, 0x17, 0xc6, 0x5d, 0x0e, 0x34, 0xf1, 0x93, 0xba, 0x53, 0xc1, 0x13, 0x71, 0xbe, 0x1b, 0x11,
	0x63, 0xc3, 0x8f, 0x61, 0x3e, 0xde, 0x0d, 0x28, 0xf5, 0xe7, 0xe9, 0xc0, 0x8a, 0xa0, 0x28, 0xfa,
	0xdf, 0x83, 0xe5, 0xe0, 0xd4, 0xbb, 0x30, 0x7b, 0xbe, 0x75, 0x6c, 0x7a, 0xbe, 0xcd, 0x7c, 0xb3,
	0xcb, 0xdc, 0x90, 0xf9, 0xab, 0x0b, 0xb8, 0x65, 0x16, 0x39, 0x76, 0xd7, 0xb7, 0x8e, 0x0f, 0x39,
	0xae, 0x8e, 0x28, 0xfd, 0x27, 0x39, 0x28, 0xe1, 0x34, 0x6d, 0x5b, 0xa1, 0xc5, 0x67, 0xda, 0x71,
	0xbb, 0x9e, 0x38, 0xbb, 0x2b, 0x86, 0xf8, 0xa2, 0x93, 0xdd, 0x3f, 0x77, 0xba, 0xcc, 0x1c, 0x05,
	0xcc, 0x17, 0xe2, 0xa4, 0x2c, 0x60, 0x9c, 0xa7, 0x39, 0x89, 0xcf, 0x42, 0x5e, 0x13, 0x91, 0x90,
	0x90, 0x2c, 0x0b, 0x18, 0x92, 0x6c, 0x02, 0x9c, 0xf8, 0x2c, 0x38, 0x25, 0x82, 0x29, 0x62, 0x46,
	0x84, 0x70, 0xb4, 0xfe, 0x4a, 0xf4, 0x84, 0x2f, 0x18, 0xe7, 0x3f, 0x5c, 0x5b, 0x94, 0x11, 0xd4,
	0x19, 0x5c, 0x43, 0x14, 0x11, 0xef, 0xc0, 0x34, 0xd6, 0x8a, 0xfd, 0xc8, 0x5c, 0x74, 0x3e, 0x1a,
	0x83, 0x88, 0xf4, 0x5f, 0x07, 0x88, 0x67, 0x5f, 0xfb, 0x16, 0x4c, 0x45, 0x75, 0xce, 0x3f, 0xdb,
	0x48, 0x14, 0x45, 0x8a, 0xb6, 0x37, 0xf2, 0xbb, 0x8c, 0xb7, 0x63, 0x20, 0x25, 0x2a, 0x02, 0x08,
	0x33, 0x47, 0x7e, 0x5f, 0xec, 0x93, 0x12, 0x41, 0x8e, 0xfc, 0xbe, 0xfe, 0x7f, 0x72, 0xb0, 0xf6,
	0xc2, 0x0a, 0xbb, 0xa7, 0xbb, 0x2c, 0xac, 0x45, 0x62, 0x52, 0xee, 0x5f, 0x95, 0xeb, 0x73, 0x49,
	0xae, 0xd7, 0x60, 0x6a, 0xe4, 0xd8, 0x01, 0x9e, 0x42, 0x15, 0x03, 0x7f, 0x8f, 0x1d, 0x34, 0x85,
	0xa4, 0x6c, 0x5c, 0x81, 0x59, 0xef, 0xe4, 0xc4, 0x0c, 0x58, 0x28, 0xa6, 0x6f, 0xc6, 0x3b, 0x39,
	0x69, 0xb3, 0x90, 0x6b, 0x23, 0x7d, 0x67, 0xe0, 0x84, 0x62, 0xdf, 0xd1, 0x07, 0xaf, 0xcd, 0x65,
	0xcc, 0x36, 0x43, 0x2f, 0xb4, 0xfa, 0x62, 0xd3, 0x95, 0x38, 0xa4, 0xc3, 0x01, 0x7c, 0xcb, 0x06,
	0xcc, 0xf2, 0xbb, 0xa7, 0xd4, 0xda, 0x2c, 0x6d, 0x59, 0x02, 0x61, 0x73, 0x0f, 0x60, 0x4e, 0x9c,
	0x13, 0x38, 0xda, 0xd5, 0x22, 0xf6, 0x87, 0xce, 0x0e, 0x9a, 0x26, 0xfd, 0x9f, 0x17, 0xa0, 0xaa,
	0x8e, 0xba, 0xeb, 0xf9, 0xb6, 0x56, 0x85, 0x42, 0x2c, 0x6e, 0xf8, 0xcf, 0x5f, 0xb0, 0xa0, 0x11,
	0x53, 0xe4, 0x9d, 0x31, 0x57, 0xa8, 0x5f, 0x34, 0x6b, 0x1d, 0x0e, 0x49, 0xcd, 0xe1, 0x4c, 0xfa,
	0x7c, 0xe1, 0xe8, 0xe1, 0xb0, 0x7f, 0x69, 0x86, 0xce, 0x40, 0x0e, 0xba, 0x84, 0x90, 0x8e, 0x33,
	0x60, 0xf1, 0x19, 0x51, 0xbc, 0xa9, 0x72, 0x90, 0x9e, 0x25, 0xd2, 0xba, 0xd4, 0x59, 0xe2, 0xfb,
	0xca, 0x67, 0x56, 0xe0, 0x91, 0xba, 0x55, 0x32, 0xc4, 0x57, 0x82, 0x3b, 0xca, 0x49, 0xee, 0x78,
	0x08, 0x95, 0xc0, 0xe9, 0xb9, 0xb1, 0x22, 0x39, 0x87, 0x25, 0xe7, 0x38, 0x30, 0xd2, 0x23, 0xd7,
	0xa1, 0xe8, 0x0d, 0x99, 0x6f, 0x85, 0x9e, 0xbf, 0x5a, 0xa1, 0xb9, 0x93, 0xdf, 0x7c, 0x9e, 0x46,
	0x43, 0xdb, 0x0a, 0x19, 0x0d, 0x74, 0x9e, 0x56, 0x97, 0x40, 0x7c, 0xa4, 0xfa, 0x3f, 0xcd, 0xc3,
	0x7a, 0x16, 0xe3, 0x8a, 0xa3, 0xe3, 0xff, 0x2b, 0xe7, 0xfe, 0x35, 0x98, 0x42, 0x81, 0x38, 0x83,
	0x8b, 0xb0, 0x99, 0x58, 0x84, 0x34, 0xbb, 0x19, 0x48, 0xca, 0xc7, 0x8b, 0x7c, 0x6e, 0x12, 0x9b,
	0x09, 0x6e, 0x46, 0x50, 0x1d, 0x39, 0x2d, 0xc5, 0xee, 0xc5, 0x6b, 0xd9, 0xbd, 0x34, 0xce, 0xee,
	0x3f, 0xc9, 0xc1, 0x46, 0x5b, 0x51, 0xc9, 0x6a, 0x72, 0x80, 0x72, 0xbf, 0x8f, 0xb3, 0x7e, 0x8a,
	0x5f, 0xf3, 0xd7, 0xf0, 0x6b, 0x5a, 0xb9, 0x54, 0x78, 0x67, 0x4a, 0xe5, 0x1d, 0xfd, 0x1e, 0x6c,
	0x4e, 0xe8, 0x89, 0x50, 0x2c, 0x7e, 0x3b, 0x0f, 0xb7, 0x49, 0x31, 0xe0, 0x6a, 0x63, 0xdb, 0x19,
	0x0c, 0xfb, 0xec, 0x6a, 0xb3, 0x49, 0x83, 0x29, 0xc5, 0x62, 0xc2, 0xdf, 0x1c, 0xe6, 0x74, 0x3d,
	0x57, 0x6c, 0x4d, 0xfc, 0x1d, 0x19, 0x19, 0xd8, 0xe1, 0xa9, 0xd8, 0xc8, 0xc0, 0xfe, 0x6a, 0x30,
	0x85, 0xc6, 0x07, 0xad, 0x27, 0xfe, 0xbe, 0x6e, 0x4b, 0x7e, 0x00, 0xe5, 0x13, 0x9f, 0xb1, 0x1f,
	0xb3, 0x58, 0x10, 0xcd, 0xa7, 0xce, 0xcf, 0x1d, 0xc4, 0xe3, 0xf8, 0xe0, 0x24, 0xfa, 0xad, 0x3d,
	0x82, 0x79, 0x51, 0x32, 0x08, 0xbd, 0xa1, 0x19, 0x06, 0xb8, 0xac, 0x05, 0x63, 0x8e, 0xa0, 0xed,
	0xd0, 0x1b, 0x76, 0x02, 0xfd, 0x35, 0x68, 0xf1, 0x44, 0xf0, 0x72, 0x38, 0x0d, 0x89, 0x51, 0xe4,
	0xc6, 0x47, 0x91, 0x35, 0x15, 0x38, 0xb2, 0x42, 0x3c, 0x32, 0xfd, 0x19, 0xac, 0x46, 0xfa, 0x17,
	0xaf, 0x9d, 0x1f, 0xd8, 0x92, 0x17, 0x96, 0x61, 0x46, 0x70, 0x92, 0x98, 0xe6, 0x40, 0x30, 0x51,
	0x1e, 0xd6, 0x32, 0x0a, 0x89, 0x7d, 0xa7, 0x43, 0x85, 0x9f, 0x8f, 0x66, 0x77, 0xe4, 0x93, 0x2a,
	0x45, 0x85, 0xcb, 0x23, 0xb2, 0x76, 0x50, 0x97, 0xfa, 0x9e, 0xe8, 0x7a, 0x64, 0x46, 0x97, 0x9f,
	0x3d, 0x48, 0x4c, 0x57, 0xd6, 0xba, 0xd3, 0xe8, 0x50, 0xa3, 0x68, 0xc0, 0x7c, 0x34, 0x74, 0xaa,
	0x84, 0x6c, 0x80, 0x7b, 0x13, 0x2a, 0x91, 0x73, 0x66, 0xcc, 0xc9, 0x09, 0xc2, 0x6a, 0xf8, 0x86,
	0x41, 0x51, 0xca, 0x4f, 0xe6, 0x51, 0x20, 0x58, 0xa1, 0x8c, 0xb0, 0x36, 0x82, 0x14, 0x69, 0xab,
	0x2a, 0xfb, 0x28, 0x6d, 0xb9, 0xe5, 0xf4, 0x3f, 0x72, 0xb0, 0x54, 0xe3, 0x5f, 0x86, 0x13, 0x9c,
	0xd5, 0x4f, 0x59, 0xf7, 0x4c, 0x4e, 0xde, 0x1d, 0x28, 0xd9, 0x0c, 0x35, 0x0e, 0xc1, 0xa6, 0x25,
	0xa3, 0x48, 0x80, 0xa6, 0xad, 0xcd, 0x43, 0xde, 0x19, 0x8a, 0xb5, 0xc9, 0x3b, 0x43, 0xbe, 0xc7,
	0xba, 0x7d, 0x87, 0x9b, 0x24, 0xca, 0x1e, 0x02, 0x02, 0xe1, 0x72, 0xf2, 0x4d, 0x38, 0x1c, 0x9a,
	0xe7, 0xcc, 0x47, 0x1b, 0x82, 0x3a, 0xca, 0x7b, 0xf6, 0x8a, 0x20, 0x5c, 0x79, 0x3a, 0xb1, 0xba,
	0xcc, 0xb4, 0x46, 0xe1, 0xa9, 0xe9, 0xb3, 0x60, 0xd4, 0x4f, 0x1e, 0x30, 0x8b, 0x1c, 0x5b, 0x1b,
	0x85, 0xa7, 0x06, 0xe2, 0x68, 0xe7, 0xc6, 0x0b, 0x3c, 0xa3, 0x2e, 0x70, 0x42, 0x74, 0xce, 0x26,
	0x44, 0xa7, 0xfe, 0x67, 0x39, 0x58, 0x4e, 0x0f, 0xf8, 0x0b, 0x2c, 0x7c, 0x7a, 0xc6, 0xf3, 0xd7,
	0xcd, 0x78, 0x21, 0x35, 0xe3, 0xc9, 0x81, 0x76, 0x3d, 0x17, 0x5d, 0x09, 0x3f, 0x8a, 0xc5, 0x4b,
	0x34, 0xd0, 0x3a, 0xe1, 0xbe, 0x9f, 0x3e, 0xa7, 0xa6, 0x93, 0x03, 0x7a, 0x08, 0x0f, 0x38, 0x2f,
	0x07, 0x2a, 0x2f, 0x7f, 0xea, 0x84, 0xa7, 0x28, 0x8f, 0xc4, 0x62, 0xea, 0x7f, 0x98, 0x03, 0xfd,
	0x2a, 0x2a, 0x31, 0x03, 0x09, 0xb6, 0xce, 0xfd, 0x22, 0xd8, 0x3a, 0xff, 0x25, 0xd8, 0x5a, 0xf7,
	0xa1, 0xd2, 0x66, 0xfd, 0x93, 0xa6, 0x1b, 0xfa, 0x5e, 0xdd, 0x73, 0x4f, 0xb4, 0xfb, 0x30, 0x87,
	0xd3, 0x34, 0xb0, 0xde, 0x98, 0x5d, 0x37, 0x14, 0x0b, 0x03, 0x1c, 0xb6, 0x6f, 0xbd, 0xa9, 0xbb,
	0x21, 0x5f, 0x3b, 0xf2, 0xfb, 0x70, 0x92, 0x80, 0x75, 0xa3, 0x85, 0xe1, 0xc0, 0x7d, 0xeb, 0x4d,
	0x9b, 0xa1, 0x6b, 0x86, 0x68, 0x7c, 0xf6, 0x39, 0xae, 0x4b, 0xd1, 0x28, 0x22, 0xc0, 0x60, 0x9f,
	0xeb, 0xff, 0xbe, 0x00, 0x0b, 0x68, 0xdd, 0xbb, 0x27, 0x9e, 0x3f, 0xa0, 0xd3, 0xfd, 0xeb, 0x50,
	0x75, 0xe2, 0x4f, 0x55, 0x4e, 0x2d, 0x28, 0x70, 0x79, 0x74, 0x25, 0x5c, 0x6f, 0xf9, 0x6b, 0x5c,
	0x6f, 0x85, 0x94, 0xeb, 0x0d, 0xcf, 0xc6, 0x3e, 0xeb, 0x86, 0xaa, 0x4c, 0x07, 0x02, 0x49, 0xad,
	0x49, 0xf1, 0xcd, 0x4d, 0xa7, 0x7d, 0x73, 0x97, 0xb0, 0xb4, 0x5b, 0xdb, 0x6f, 0x98, 0xcd, 0x83,
	0x9d, 0x43, 0x63, 0xbf, 0xd6, 0x69, 0x1e, 0x1e, 0x98, 0x9d, 0xd7, 0xad, 0x86, 0xf6, 0x00, 0x36,
	0x33, 0x11, 0x66, 0xf3, 0xe0, 0x55, 0x6d, 0xaf, 0xb9, 0x5d, 0xfd, 0x9a, 0x76, 0x17, 0xd6, 0xb3,
	0x49, 0x8c, 0xda, 0xc1, 0x27, 0xd5, 0x9c, 0xb6, 0x09, 0x6b, 0xd9, 0xf8, 0xed, 0xe6, 0xeb, 0x6a,
	0x5e, 0xff, 0x87, 0x39, 0xd8, 0x18, 0xc3, 0xb7, 0x1b, 0x7b, 0x8d, 0x7a, 0x87, 0xba, 0xf0, 0x04,
	0x1e, 0x5d, 0x85, 0x57, 0x7a, 0xf2, 0x36, 0x3c, 0xbc, 0x92, 0xb2, 0xdd, 0x3c, 0xd8, 0xdd, 0x6b,
	0x54, 0x73, 0xda, 0x5b, 0xa0, 0x5f, 0x49, 0xb8, 0x7f, 0xb4, 0xd7, 0x69, 0x56, 0xf3, 0xfa, 0x3f,
	0xc9, 0xc1, 0x1c, 0x3f, 0xa1, 0xe5, 0xca, 0xa2, 0x59, 0x83, 0xd6, 0x27, 0x37, 0x25, 0x84, 0x54,
	0x43, 0xc0, 0x91, 0x8f, 0x8e, 0x11, 0x87, 0xf3, 0x9c, 0xc9, 0x19, 0x4b, 0x1a, 0x1a, 0x08, 0xe9,
	0xb0, 0x37, 0xb8, 0x46, 0x84, 0x26, 0xbf, 0x23, 0x1d, 0xc8, 0x54, 0xa2, 0x86, 0xce, 0xc7, 0x0f,
	0xa1, 0xc4, 0xf9, 0x82, 0x56, 0x78, 0x0a, 0x39, 0x3f, 0x69, 0xdf, 0xa4, 0x18, 0xcc, 0x28, 0x72,
	0x72, 0x64, 0xf9, 0x7f, 0x9b, 0x87, 0x0a, 0x6e, 0x2b, 0xde, 0x17, 0xe4, 0xf9, 0x2f, 0xa4, 0x24,
	0x24, 0x8e, 0xd2, 0x42, 0xea, 0x28, 0x95, 0x48, 0x54, 0x23, 0xa6, 0x62, 0x67, 0x6c, 0x93, 0xab,
	0x12, 0x6f, 0xc3, 0x02, 0x22, 0x8f, 0xad, 0xee, 0x59, 0xcf, 0xf7, 0x46, 0xae, 0x2d, 0x04, 0x2e,
	0xee, 0xe4, 0x17, 0x11, 0x14, 0x7d, 0x04, 0x9c, 0xb0, 0xeb, 0xf5, 0x3d, 0x1f, 0xe5, 0x6d, 0xc9,
	0xc0, 0x7a, 0xeb, 0x1c, 0x90, 0xe1, 0xd4, 0x9d, 0xbd, 0xde, 0xa9, 0x5b, 0x4c, 0x3b, 0x75, 0x5f,
	0x08, 0x01, 0x12, 0x4f, 0x63, 0xe9, 0x06, 0xd3, 0x88, 0xd2, 0xa3, 0x29, 0xa7, 0xf2, 0x03, 0xd8,
	0x4c, 0x1c, 0xee, 0xd1, 0x94, 0x5e, 0xeb, 0x21, 0xfa, 0x79, 0x0e, 0xee, 0x4e, 0x2a, 0x2a, 0x24,
	0xe4, 0xc4, 0x55, 0xf9, 0x50, 0x0e, 0xac, 0xeb, 0xb9, 0x27, 0xc2, 0x2e, 0x5e, 0x1f, 0xeb, 0x75,
	0x5c, 0x21, 0x0d, 0x1a, 0x57, 0xfa, 0x43, 0xc9, 0x76, 0x58, 0xb4, 0x90, 0x51, 0x34, 0x21, 0x0d,
	0x05, 0x4b, 0x62, 0xd1, 0x71, 0xcf, 0xc4, 0x54, 0x86, 0x67, 0x42, 0xff, 0x69, 0x1e, 0x34, 0x3c,
	0xf4, 0x68, 0x68, 0xfc, 0x24, 0x33, 0xd8, 0xe7, 0xa9, 0x93, 0x2a, 0x97, 0x3e, 0xa9, 0xd4, 0x43,
	0x27, 0x7f, 0x8d, 0x71, 0x44, 0x3c, 0x97, 0x34, 0x8e, 0x3e, 0x04, 0xc0, 0xf3, 0xf4, 0xa6, 0x5e,
	0xca, 0xd2, 0x48, 0x7e, 0x26, 0x95, 0x8f, 0xe9, 0x94, 0xf2, 0x91, 0x52, 0x36, 0x66, 0xae, 0x53,
	0x36, 0x66, 0xd3, 0xca, 0x86, 0xbe, 0x04, 0x8b, 0x63, 0xd3, 0x11, 0x0c, 0xf5, 0x3f, 0xcf, 0xc1,
	0x62, 0xdd, 0x67, 0x56, 0xc8, 0x0c, 0x76, 0x32, 0x72, 0x6d, 0xc5, 0x87, 0x40, 0xee, 0x9c, 0x48,
	0x13, 0x9a, 0xc5, 0xef, 0xa6, 0xcd, 0x0d, 0xc0, 0xcf, 0x47, 0x96, 0x1b, 0x3a, 0xe1, 0xa5, 0x98,
	0xa3, 0xe8, 0x5b, 0x31, 0x1c, 0x0a, 0x09, 0xa3, 0xf3, 0x3e, 0x94, 0xf9, 0x52, 0xf9, 0xce, 0x30,
	0x74, 0xa2, 0x1d, 0xa9, 0x82, 0xb4, 0x6f, 0x08, 0x1f, 0xc9, 0x74, 0x86, 0x22, 0x4e, 0x5d, 0x53,
	0xdc, 0x23, 0x89, 0xb9, 0x9a, 0x49, 0xce, 0x95, 0xfe, 0x7b, 0x39, 0x58, 0x34, 0xd8, 0x8f, 0x58,
	0x37, 0x4c, 0x0e, 0x69, 0x0b, 0x6e, 0xf9, 0x08, 0xe0, 0x87, 0x21, 0x87, 0xc4, 0x63, 0x5b, 0xf0,
	0x55, 0xca, 0xa6, 0xad, 0x8c, 0x23, 0x7f, 0xd5, 0x38, 0x0a, 0xe3, 0xe3, 0x48, 0x74, 0x6d, 0x2a,
	0xd5, 0xb5, 0x2a, 0xcc, 0xcb, 0x3e, 0x09, 0x83, 0x69, 0x0d, 0x56, 0x76, 0x59, 0xd4, 0x51, 0xde,
	0x46, 0x20, 0x15, 0x98, 0xdf, 0xcc, 0xa3, 0x9e, 0x9f, 0xc2, 0x89, 0x4d, 0xb9, 0x0a, 0xb3, 0xd4,
	0xa5, 0x00, 0x95, 0x96, 0x92, 0x21, 0x3f, 0xb5, 0x00, 0x56, 0xc5, 0x4f, 0xf3, 0xc2, 0x09, 0x4f,
	0xcd, 0x13, 0x2b, 0x08, 0x4d, 0x1a, 0x9f, 0x50, 0x4d, 0xbe, 0x93, 0xdc, 0xa3, 0x13, 0x9a, 0x78,
	0x4a, 0xdf, 0x5c, 0x63, 0xda, 0xb1, 0x02, 0x49, 0xb3, 0x24, 0xea, 0x4e, 0x82, 0xd7, 0x7f, 0x08,
	0xb7, 0xb3, 0xc8, 0x95, 0x79, 0xcc, 0x25, 0xe6, 0x71, 0x0b, 0x6e, 0x75, 0xbd, 0x51, 0xdf, 0x4e,
	0xf5, 0x8e, 0xeb, 0x27, 0x0b, 0x88, 0x88, 0xeb, 0x88, 0xa6, 0x88, 0x56, 0x34, 0x31, 0x45, 0x7f,
	0x5d, 0xcc, 0x50, 0x02, 0x75, 0xdd, 0x0c, 0xe9, 0x3f, 0xcb, 0xc1, 0x52, 0xd3, 0x75, 0x42, 0xc7,
	0x0a, 0x59, 0x6d, 0x38, 0x64, 0x56, 0x5f, 0x31, 0x00, 0x04, 0x8b, 0xc4, 0x06, 0x00, 0x01, 0x9a,
	0x76, 0x7a, 0xed, 0xf3, 0xe3, 0x6b, 0xff, 0x00, 0xe6, 0x86, 0xbe, 0xe7, 0x9d, 0x98, 0xce, 0xc0,
	0xea, 0xb1, 0x40, 0x68, 0x3c, 0x65, 0x84, 0x35, 0x11, 0x74, 0x35, 0xe7, 0x3e, 0x87, 0xe5, 0x74,
	0xbf, 0xc4, 0x60, 0xb8, 0x1e, 0x87, 0x10, 0xa5, 0x63, 0x04, 0x68, 0xda, 0xfa, 0x9f, 0xe6, 0x60,
	0xa3, 0x3d, 0x3a, 0x1e, 0x38, 0xe1, 0xee, 0xc8, 0xb1, 0x45, 0x51, 0xd5, 0xa1, 0x7f, 0x55, 0x69,
	0xed, 0x03, 0x58, 0x25, 0x9f, 0xb0, 0x20, 0x19, 0x1f, 0xe3, 0x32, 0xe2, 0xa9, 0xda, 0x6d, 0x65,
	0xb8, 0x4f, 0x61, 0x31, 0x51, 0x32, 0x31, 0xea, 0x5b, 0x4a, 0xa1, 0x9b, 0x8c, 0xfd, 0x1e, 0x6c,
	0x4e, 0x18, 0x83, 0xd8, 0x29, 0x1b, 0x18, 0xb3, 0x30, 0x58, 0x97, 0x39, 0xe7, 0xe8, 0x4c, 0xda,
	0xf1, 0xad, 0x81, 0xf4, 0x81, 0xe8, 0x17, 0x18, 0x93, 0x18, 0xc7, 0xc6, 0x31, 0x89, 0x20, 0xb4,
	0xfc, 0x90, 0xfc, 0x52, 0x79, 0xa1, 0x4a, 0x72, 0x08, 0x3a, 0xe0, 0xd6, 0xa0, 0xc8, 0x5c, 0x9b,
	0x90, 0x24, 0xd6, 0x67, 0x99, 0x6b, 0x23, 0xea, 0x2e, 0x94, 0x6d, 0xeb, 0xd2, 0xf4, 0x4e, 0xcc,
	0x0b, 0xc6, 0xce, 0x50, 0xc5, 0x29, 0x1a, 0x25, 0xdb, 0xba, 0x3c, 0x3c, 0xf9, 0x94, 0xb1, 0x33,
	0xfd, 0x1c, 0xd6, 0xdb, 0x13, 0xbb, 0xf5, 0x4b, 0x6c, 0x77, 0x13, 0xee, 0xb4, 0x27, 0x0f, 0x58,
	0xff, 0x01, 0xfa, 0x8c, 0xa4, 0xc7, 0x8a, 0xd3, 0xb4, 0x2f, 0x9c, 0xb0, 0x7b, 0xaa, 0xc8, 0x77,
	0x3a, 0xbb, 0x63, 0x4f, 0x1b, 0x7e, 0x93, 0xec, 0x0b, 0x90, 0x56, 0x6c, 0x48, 0xf1, 0x25, 0x9c,
	0x3f, 0x59, 0x55, 0x8a, 0x36, 0xef, 0xc1, 0x26, 0x5a, 0x97, 0xcd, 0x93, 0x3a, 0xdf, 0xc2, 0x87,
	0x43, 0xe6, 0x26, 0x1a, 0xd5, 0x3f, 0x85, 0xbb, 0x93, 0x08, 0xe2, 0x75, 0x22, 0xb9, 0xe0, 0x0d,
	0x19, 0xc9, 0x8c, 0xa2, 0x51, 0xea, 0x4a, 0xe2, 0x49, 0x62, 0x59, 0x3f, 0x80, 0xdb, 0xb2, 0x6b,
	0x2d, 0xdf, 0xe9, 0xb2, 0x1b, 0x8c, 0xf2, 0x0e, 0x94, 0x86, 0x9c, 0xd4, 0x3c, 0xb7, 0xfa, 0xf2,
	0x18, 0x43, 0xc0, 0x2b, 0xab, 0xaf, 0xaf, 0xc0, 0x52, 0xaa, 0x3e, 0x31, 0xc4, 0x3f, 0x99, 0x82,
	0x39, 0x35, 0x56, 0x83, 0x5e, 0x01, 0x59, 0x77, 0xde, 0xb9, 0xb9, 0x3b, 0xeb, 0x1d, 0x98, 0xc6,
	0xd6, 0x84, 0x8e, 0x90, 0x0c, 0x27, 0x60, 0x9b, 0xe4, 0xee, 0x45, 0x22, 0xae, 0xf3, 0xf8, 0x34,
	0xe5, 0xa6, 0x58, 0x9a, 0x69, 0x9c, 0x9a, 0x8a, 0xaf, 0x2e, 0x84, 0xaa, 0xa9, 0xcd, 0x24, 0x34,
	0xb5, 0x03, 0x55, 0x67, 0xe2, 0x8a, 0x26, 0x2a, 0x08, 0xe5, 0x67, 0x6f, 0x4f, 0x0c, 0x42, 0x3d,
	0x8d, 0x02, 0x9d, 0xd8, 0x8f, 0x58, 0xb9, 0x42, 0x93, 0x22, 0xe5, 0x3c, 0x2b, 0x7e, 0x15, 0xe7,
	0x59, 0x69, 0xdc, 0x79, 0xb6, 0xfe, 0xe7, 0x39, 0xa8, 0x24, 0x3a, 0x90, 0xd4, 0xfa, 0xd0, 0x23,
	0x46, 0x42, 0x3d, 0xee, 0x18, 0x66, 0x1b, 0x7c, 0x0c, 0x1b, 0x32, 0x50, 0xac, 0x0e, 0xd8, 0x66,
	0x6f, 0x62, 0xdb, 0xbc, 0x62, 0xac, 0x09, 0x1a, 0xa5, 0x09, 0x9b, 0xbd, 0xb9, 0x41, 0x3e, 0x08,
	0x1f, 0x78, 0xda, 0x66, 0x4d, 0x0f, 0xbc, 0x1d, 0x19, 0xb0, 0xaa, 0x31, 0xcb, 0x4d, 0xc6, 0x52,
	0xb4, 0xae, 0xda, 0x6d, 0xb9, 0xfc, 0xc4, 0x39, 0x62, 0x99, 0x37, 0x01, 0x88, 0x27, 0x47, 0xae,
	0x13, 0x19, 0x63, 0x08, 0x39, 0x72, 0x1d, 0x14, 0xe3, 0x03, 0xc7, 0x35, 0xa9, 0xa0, 0xb0, 0x78,
	0x06, 0x8e, 0xdb, 0x92, 0x65, 0x8f, 0xad, 0x80, 0x99, 0x31, 0x57, 0x55, 0x8c, 0x12, 0x87, 0x10,
	0x9a, 0x97, 0xb5, 0xde, 0x08, 0xec, 0xb4, 0x28, 0x6b, 0xbd, 0x41, 0xa4, 0xbe, 0x0f, 0x4b, 0xbb,
	0x2c, 0xc4, 0x80, 0xd5, 0x36, 0x0b, 0x2d, 0x47, 0x3d, 0x0f, 0x49, 0xc2, 0xc7, 0xfe, 0xe5, 0x22,
	0x02, 0xae, 0xcc, 0x21, 0x38, 0x82, 0xe5, 0x74, 0x75, 0x62, 0x7b, 0x7f, 0x04, 0x73, 0x54, 0x9f,
	0x8d, 0x70, 0x11, 0x13, 0x5f, 0x1d, 0x8f, 0x5a, 0x8a, 0x72, 0xe5, 0x6e, 0xfc, 0xa1, 0xff, 0xd7,
	0x12, 0x94, 0x15, 0xa4, 0xf6, 0x0c, 0x50, 0x7b, 0x26, 0x7e, 0xa6, 0x9a, 0x96, 0xc6, 0x54, 0x6d,
	0xf2, 0xd6, 0x8c, 0xc4, 0xaf, 0x64, 0x1e, 0x4b, 0x3e, 0x95, 0xc7, 0xb2, 0x09, 0x70, 0xee, 0xf1,
	0xe9, 0x47, 0x2c, 0xed, 0xd6, 0x12, 0x42, 0x12, 0x69, 0x2e, 0xb8, 0xbf, 0xa7, 0x52, 0x09, 0x3e,
	0x55, 0x28, 0x84, 0x56, 0x4f, 0xe8, 0xed, 0xfc, 0x27, 0x5f, 0xe2, 0xa0, 0xeb, 0xf9, 0xa4, 0xac,
	0xe7, 0x0d, 0xfa, 0x20, 0xa7, 0x07, 0x45, 0x35, 0xbb, 0x71, 0xc4, 0x40, 0x80, 0xea, 0x6e, 0xa8,
	0xed, 0x82, 0x86, 0xad, 0x0c, 0x7d, 0x6f, 0xc8, 0xfc, 0xf0, 0x92, 0xf8, 0xb6, 0x98, 0x15, 0x33,
	0xb6, 0x06, 0xac, 0x25, 0xa8, 0x8c, 0x6a, 0x4f, 0xf9, 0x42, 0x4e, 0xe6, 0xa6, 0x0c, 0x2e, 0xcf,
	0xa0, 0x87, 0x16, 0x65, 0xc9, 0xc0, 0xe5, 0x6a, 0x0e, 0x7a, 0x9c, 0x8b, 0x95, 0x30, 0x30, 0xc6,
	0x87, 0xae, 0x88, 0x1d, 0x43, 0x1c, 0x1a, 0xe6, 0xdd, 0xf7, 0xdc, 0xbe, 0xe3, 0x8a, 0x8d, 0x4f,
	0xf1, 0x23, 0x20, 0x10, 0xee, 0xef, 0xc7, 0x30, 0xcf, 0xed, 0x5e, 0x2f, 0x19, 0x43, 0xaa, 0x18,
	0x15, 0x84, 0x46, 0x76, 0x52, 0xc4, 0xff, 0x95, 0xc9, 0xfc, 0x3f, 0x9f, 0xe6, 0x7f, 0xc9, 0x70,
	0xc7, 0x3d, 0x8c, 0x2e, 0x97, 0x88, 0xe1, 0x5e, 0xf4, 0x34, 0x1d, 0x2a, 0x02, 0x21, 0x4c, 0xf5,
	0x2a, 0xe9, 0x66, 0x84, 0x26, 0x63, 0xfd, 0x0e, 0x94, 0x9c, 0xc0, 0x3c, 0xf1, 0xfa, 0x7d, 0xef,
	0x62, 0xf5, 0x16, 0x79, 0xc2, 0x9c, 0x60, 0x07, 0xbf, 0x45, 0x92, 0xd2, 0x89, 0x35, 0xf0, 0x46,
	0x81, 0x39, 0xec, 0x5b, 0x97, 0xcc, 0x5f, 0xd5, 0x64, 0x92, 0xd2, 0x0e, 0x82, 0x5b, 0x08, 0xcd,
	0x8c, 0xa0, 0x2f, 0x66, 0x46, 0xd0, 0x9f, 0x40, 0x55, 0x04, 0xfd, 0x63, 0xca, 0xdb, 0x44, 0x49,
	0xf1, 0x7d, 0x95, 0xb2, 0xa7, 0xe4, 0x9d, 0xa0, 0x27, 0x66, 0x49, 0x38, 0x24, 0xa2, 0x64, 0x13,
	0x74, 0xc7, 0x7c, 0x13, 0x6e, 0xab, 0x75, 0x8e, 0x7c, 0x51, 0xef, 0x32, 0xa9, 0x5c, 0x71, 0xbd,
	0x47, 0x3e, 0x55, 0xfd, 0x3e, 0xac, 0x9e, 0x5a, 0x81, 0x79, 0xe2, 0xf8, 0x5c, 0xcd, 0xf6, 0xb8,
	0x66, 0x6b, 0x3b, 0x01, 0x45, 0xab, 0x56, 0x70, 0x80, 0x4b, 0xa7, 0x56, 0xb0, 0xc3, 0xd1, 0x06,
	0xc7, 0x6e, 0x0b, 0x24, 0x57, 0xd0, 0xd5, 0x42, 0xb4, 0x58, 0xab, 0xe4, 0x08, 0x3c, 0x89, 0xc8,
	0x49, 0xb6, 0x3c, 0x85, 0x45, 0x27, 0x30, 0xbb, 0xa3, 0x20, 0xf4, 0x06, 0xcc, 0x37, 0x4f, 0xbd,
	0x20, 0x74, 0xdc, 0xde, 0xea, 0x1a, 0xd6, 0x7f, 0xcb, 0x09, 0xea, 0x02, 0xf3, 0x92, 0x10, 0x5c,
	0x4d, 0xe6, 0x9d, 0x8a, 0x3a, 0xb2, 0x8e, 0x84, 0xe5, 0x53, 0x2b, 0x88, 0x9a, 0x7f, 0x0c, 0xf3,
	0x12, 0x2d, 0xda, 0xbe, 0x43, 0x6c, 0x24, 0xa1, 0xd4, 0xf2, 0x43, 0x88, 0x00, 0xc4, 0x90, 0x1b,
	0x64, 0x93, 0x4b, 0x20, 0xb2, 0xa4, 0x4a, 0x84, 0x3b, 0x7b, 0x93, 0xa2, 0x9a, 0x12, 0x28, 0x13,
	0x19, 0xc8, 0xde, 0x3d, 0x1e, 0x85, 0xa1, 0xe7, 0x9a, 0xa7, 0x0e, 0xaf, 0x92, 0xaf, 0xc4, 0x5d,
	0x72, 0x51, 0x23, 0xf6, 0x05, 0x22, 0x5f, 0x3a, 0x6e, 0x88, 0xcb, 0xf1, 0x09, 0x68, 0xc4, 0x0c,
	0xdd, 0x53, 0xcb, 0x75, 0x59, 0x9f, 0x44, 0xd1, 0x3d, 0xdc, 0x4e, 0x9b, 0xe3, 0xdb, 0xa9, 0x4e,
	0x54, 0x28, 0x92, 0x88, 0x8b, 0x14, 0x88, 0xfe, 0xbb, 0x39, 0xa8, 0xa6, 0xc9, 0x50, 0x1f, 0x92,
	0x75, 0x47, 0xb9, 0x34, 0x02, 0xd2, 0xb4, 0xf9, 0x4c, 0x4a, 0x34, 0x0e, 0x5f, 0x78, 0x80, 0x05,
	0x4c, 0x06, 0x41, 0xf9, 0x64, 0x0f, 0x2f, 0x6c, 0xe1, 0xff, 0x9d, 0x39, 0xb5, 0x82, 0xd6, 0x05,
	0xb9, 0x14, 0xb8, 0xe1, 0xef, 0xf9, 0x28, 0xdd, 0x85, 0xfb, 0x55, 0x80, 0x8e, 0x1c, 0x5b, 0xff,
	0x83, 0x02, 0xcc, 0xa9, 0x32, 0xe6, 0x46, 0xba, 0xce, 0xfb, 0x50, 0x3c, 0xb7, 0xfa, 0x6a, 0x7c,
	0x67, 0x63, 0xa2, 0xd0, 0x7a, 0x65, 0xf5, 0x8d, 0xd9, 0x73, 0x8b, 0x38, 0xf5, 0x21, 0x54, 0x22,
	0x91, 0xa7, 0xf8, 0x83, 0xe7, 0x24, 0x50, 0x7a, 0x39, 0xd4, 0xe3, 0x77, 0x7a, 0xcc, 0x65, 0xbc,
	0x0c, 0x33, 0xec, 0xcd, 0xd0, 0x0b, 0x98, 0x48, 0xd9, 0x12, 0x5f, 0xfa, 0x8f, 0x61, 0xae, 0xa5,
	0x56, 0xb4, 0x09, 0x6b, 0x2d, 0xe3, 0xb0, 0xd5, 0x30, 0x3a, 0xaf, 0xc9, 0x7b, 0x7a, 0x74, 0xd0,
	0x6e, 0x35, 0xea, 0xcd, 0x9d, 0x66, 0x63, 0xbb, 0xfa, 0x35, 0x6d, 0x15, 0x6e, 0x27, 0xd1, 0xbb,
	0x8d, 0x83, 0xed, 0x86, 0x51, 0xcd, 0x69, 0x2b, 0xb0, 0x98, 0xc4, 0xb4, 0x8c, 0x66, 0xbd, 0x51,
	0xcd, 0x8f, 0x17, 0xa9, 0x1f, 0xb5, 0x3b, 0x87, 0xfb, 0xd5, 0x82, 0xfe, 0x19, 0x40, 0xac, 0x13,
	0x68, 0x77, 0x60, 0x45, 0xf5, 0xda, 0x26, 0xdb, 0x5d, 0x06, 0x2d, 0xd3, 0xf7, 0xbb, 0x04, 0xb7,
	0xb2, 0x5c, 0xbd, 0xcf, 0xc9, 0x7f, 0xaf, 0xcc, 0xe7, 0x4d, 0xd6, 0x48, 0x7f, 0x86, 0xc7, 0x3d,
	0x66, 0xe4, 0x24, 0x8f, 0xfb, 0xc9, 0x4e, 0x1f, 0xfd, 0xb7, 0x72, 0x78, 0xa8, 0x27, 0x0a, 0x89,
	0x43, 0xfd, 0x3b, 0x30, 0x8d, 0x54, 0xe2, 0x0c, 0x7e, 0x94, 0xd4, 0x86, 0x48, 0x87, 0xf6, 0xec,
	0x51, 0x37, 0x51, 0x98, 0x8a, 0x68, 0xdf, 0xe2, 0x0a, 0x7d, 0x64, 0xfc, 0xa7, 0x55, 0x01, 0x2c,
	0x20, 0x1c, 0x0f, 0x82, 0x4e, 0xff, 0x8b, 0x02, 0xac, 0x4c, 0xa8, 0x54, 0xfb, 0x1e, 0xf2, 0x10,
	0x87, 0x52, 0x2e, 0x92, 0xa8, 0x74, 0x2d, 0xa5, 0x5c, 0xc7, 0xe5, 0x90, 0xbd, 0xa2, 0xaf, 0xc4,
	0xf8, 0x0b, 0x49, 0xa7, 0x17, 0x37, 0x8a, 0xd4, 0x80, 0xa3, 0xf8, 0x42, 0x7d, 0x71, 0x74, 0x2c,
	0x43, 0x63, 0x32, 0x46, 0x31, 0x3a, 0x16, 0x81, 0xb1, 0x35, 0x28, 0x0e, 0x2d, 0x91, 0xf6, 0x31,
	0x83, 0x8a, 0xee, 0xec, 0xd0, 0xa2, 0xa4, 0x0f, 0x4c, 0x5e, 0x22, 0x9d, 0x3e, 0xca, 0x0a, 0x29,
	0x18, 0x65, 0x3f, 0xb6, 0xe8, 0x38, 0xbb, 0x9f, 0x38, 0xae, 0x13, 0x9c, 0x12, 0x05, 0x85, 0x99,
	0x81, 0x40, 0x92, 0xa0, 0x6b, 0xb9, 0x5d, 0xbe, 0xfd, 0x39, 0x01, 0xa9, 0xd2, 0x40, 0x20, 0x24,
	0x78, 0x04, 0xf3, 0x34, 0xa2, 0xc8, 0xcc, 0x04, 0x52, 0xb7, 0x11, 0xda, 0x10, 0xb6, 0x26, 0xdf,
	0x56, 0xd8, 0x5f, 0x92, 0x8f, 0x94, 0x65, 0x06, 0x04, 0x42, 0xe9, 0xf8, 0x00, 0xa8, 0x80, 0xe9,
	0x8e, 0x06, 0xc7, 0xcc, 0x17, 0x79, 0x21, 0x65, 0x84, 0x1d, 0x20, 0x28, 0x26, 0xf1, 0xd9, 0xc0,
	0xf2, 0xcf, 0x44, 0x6a, 0x08, 0x91, 0x18, 0x08, 0x12, 0xa7, 0xac, 0xeb, 0x85, 0xce, 0xc9, 0xa5,
	0x49, 0xa3, 0xc0, 0x43, 0x1e, 0x4f, 0xd9, 0x03, 0x04, 0xef, 0x20, 0x34, 0xa9, 0x77, 0x2e, 0x24,
	0xf5, 0x4e, 0xfd, 0x77, 0x0a, 0xb0, 0xa4, 0x2e, 0xe2, 0x57, 0xd4, 0xaa, 0xef, 0x41, 0x79, 0xc0,
	0xac, 0x60, 0xe4, 0x93, 0x46, 0x26, 0xe2, 0xb8, 0x02, 0xc4, 0x35, 0xb2, 0x07, 0x30, 0x27, 0x09,
	0xb0, 0x06, 0xe1, 0xbc, 0x14, 0x30, 0xac, 0xe3, 0xaa, 0x63, 0x76, 0xe6, 0x0b, 0x1f, 0xb3, 0xb3,
	0xd9, 0xc7, 0x6c, 0xfa, 0xd8, 0x2c, 0xde, 0xe4, 0xd8, 0x2c, 0xdd, 0xe8, 0xd8, 0x84, 0x9b, 0x1c,
	0x9b, 0xe5, 0xf1, 0x63, 0x53, 0xff, 0xd3, 0x29, 0x14, 0xac, 0xf1, 0x16, 0x7a, 0x0a, 0xd3, 0xb8,
	0x50, 0x99, 0xaa, 0x3d, 0x57, 0xc8, 0x5b, 0xbe, 0x77, 0xe2, 0xf4, 0x99, 0x41, 0x64, 0xb4, 0x38,
	0xb4, 0x65, 0x85, 0x1d, 0x31, 0xc5, 0x17, 0x07, 0x21, 0x4a, 0xe0, 0xa7, 0x90, 0x61, 0x4e, 0x4f,
	0x29, 0xe6, 0x74, 0x4d, 0xae, 0x31, 0x9e, 0xc0, 0xd3, 0xd8, 0xb6, 0x3e, 0x71, 0xdb, 0xc7, 0xf6,
	0x35, 0xf1, 0x81, 0x64, 0x9e, 0x78, 0xc1, 0x2a, 0x06, 0x7d, 0xc4, 0x19, 0x3e, 0xea, 0xd2, 0x50,
	0x86, 0x0f, 0xcd, 0xe5, 0x3b, 0xa0, 0x25, 0xd3, 0x86, 0x51, 0x69, 0xa0, 0x28, 0x4f, 0xb5, 0xa7,
	0x64, 0x0b, 0xa3, 0xc6, 0xa0, 0xd8, 0x4c, 0xa5, 0x84, 0x85, 0xbe, 0x05, 0xb7, 0xba, 0x96, 0x6b,
	0x76, 0xad, 0x7e, 0x3f, 0xd2, 0xa4, 0x44, 0x9e, 0xfc, 0x42, 0xd7, 0x72, 0xeb, 0x56, 0xbf, 0x2f,
	0xd5, 0x28, 0x72, 0x9e, 0xf2, 0xdd, 0xa1, 0xf6, 0x8c, 0x54, 0xf1, 0x05, 0x44, 0x74, 0xe2, 0xee,
	0xbd, 0x44, 0x47, 0xeb, 0xd0, 0x73, 0xcd, 0x51, 0xc0, 0xa4, 0xd9, 0x45, 0xc9, 0xa2, 0x1b, 0x29,
	0x0d, 0x85, 0x53, 0x1d, 0x05, 0x4c, 0x08, 0xe8, 0x85, 0x6e, 0x12, 0xa0, 0x7d, 0x06, 0x2b, 0x2e,
	0xbb, 0x88, 0xd5, 0x3c, 0xa5, 0xbe, 0x0a, 0xd6, 0x97, 0x0c, 0x9b, 0x1f, 0xb0, 0x0b, 0xd9, 0xe1,
	0xb8, 0xd2, 0xdb, 0x6e, 0x06, 0x54, 0x6f, 0xc3, 0x42, 0xaa, 0x75, 0xce, 0x16, 0xbc, 0x7e, 0xea,
	0x83, 0xf4, 0x03, 0x8d, 0x02, 0x46, 0x74, 0xa8, 0xf7, 0xd0, 0xa8, 0x06, 0x9e, 0xcb, 0x2e, 0x23,
	0xbd, 0x07, 0x61, 0xfb, 0x1c, 0xa4, 0xff, 0xe3, 0x1c, 0xdc, 0xce, 0xea, 0x83, 0xf6, 0x21, 0xac,
	0xf1, 0xaa, 0x13, 0x63, 0x89, 0xf6, 0x14, 0xb5, 0xb4, 0x3c, 0x0a, 0x98, 0x52, 0x36, 0xda, 0x5e,
	0xef, 0x80, 0x96, 0x28, 0x46, 0x33, 0x4f, 0x8d, 0x57, 0x95, 0xa1, 0xd1, 0xd4, 0x3f, 0x82, 0xf9,
	0x61, 0xdf, 0x0a, 0xcd, 0x63, 0xcf, 0x1d, 0x05, 0xe6, 0x09, 0x93, 0x36, 0xfb, 0x1c, 0x87, 0xbe,
	0xe0, 0xc0, 0x1d, 0xc6, 0xf4, 0x5d, 0x28, 0x2b, 0xc7, 0xdc, 0xd5, 0x1e, 0xe8, 0x84, 0x1f, 0x37,
	0x9f, 0xf2, 0x02, 0x3f, 0x80, 0x7b, 0xfb, 0x96, 0x3b, 0xb2, 0xfa, 0x35, 0xdb, 0xa6, 0x20, 0x0f,
	0x6a, 0x93, 0x22, 0xf0, 0x85, 0xfe, 0x37, 0x1d, 0xee, 0x4f, 0x26, 0x11, 0x1e, 0xae, 0x1d, 0xcc,
	0x13, 0x42, 0x38, 0x3f, 0xc3, 0x9c, 0x20, 0x74, 0xba, 0xc1, 0xe4, 0x4c, 0xb3, 0x89, 0x4e, 0x80,
	0x5f, 0x43, 0x77, 0xed, 0x58, 0x3d, 0xb1, 0x3f, 0x3b, 0x3a, 0x50, 0xa4, 0x63, 0x41, 0x9e, 0x26,
	0xb8, 0xba, 0x72, 0x8a, 0x39, 0x5e, 0xae, 0xae, 0x80, 0x1d, 0x8c, 0x06, 0xfa, 0xfb, 0xb0, 0xd0,
	0x38, 0xb7, 0xfa, 0x23, 0x2b, 0x64, 0x9f, 0x7a, 0xbe, 0xcd, 0xc5, 0xb4, 0x06, 0x53, 0x17, 0x9e,
	0x2f, 0x27, 0x0d, 0x7f, 0xf3, 0xfe, 0x76, 0x45, 0xfa, 0x67, 0xc5, 0xe0, 0x3f, 0xf5, 0x7f, 0x90,
	0x83, 0x8a, 0x2c, 0xd9, 0x46, 0x8b, 0x9c, 0x4f, 0xea, 0x79, 0xcf, 0x24, 0x5b, 0x3d, 0x87, 0xb6,
	0x7a, 0xd1, 0x3a, 0xef, 0x11, 0xf2, 0x21, 0x54, 0xa4, 0xb9, 0x4e, 0x04, 0x79, 0x24, 0x90, 0x99,
	0xc9, 0xed, 0xc8, 0xa6, 0x47, 0xab, 0x8c, 0x48, 0x0a, 0x48, 0x42, 0xa1, 0xd1, 0x88, 0x80, 0x1c,
	0x0b, 0x44, 0x30, 0x45, 0x04, 0x08, 0x42, 0x02, 0xfd, 0x0f, 0x73, 0xf1, 0x78, 0xda, 0xa3, 0xc1,
	0xc0, 0xf2, 0x2f, 0xd1, 0x55, 0x41, 0xb9, 0x85, 0x51, 0xf6, 0x47, 0x91, 0x32, 0x0b, 0xdd, 0x10,
	0x43, 0xaf, 0xbc, 0x24, 0xc9, 0xbb, 0xac, 0xd0, 0x6b, 0x62, 0x90, 0x46, 0x09, 0xa9, 0x51, 0xce,
	0xfd, 0x2a, 0x54, 0xf8, 0xdc, 0xf0, 0x6a, 0x27, 0xab, 0xe9, 0xa9, 0xc9, 0x35, 0xca, 0x17, 0xf4,
	0x03, 0xa3, 0xcd, 0xff, 0x28, 0x07, 0x45, 0xe9, 0x5b, 0xc9, 0x60, 0x89, 0x75, 0xc0, 0x64, 0x5a,
	0xf5, 0x1e, 0x94, 0xfc, 0x56, 0x73, 0x72, 0x0b, 0xc9, 0x9c, 0xdc, 0x2a, 0x14, 0x02, 0xf6, 0x46,
	0x68, 0x57, 0xfc, 0x67, 0x3a, 0xf7, 0x7d, 0x3a, 0x9d, 0xfb, 0x8e, 0xce, 0x60, 0xd4, 0x35, 0x66,
	0xa4, 0x33, 0x98, 0x7f, 0xe9, 0xff, 0x2c, 0x0f, 0x73, 0xb2, 0xff, 0xf2, 0x8e, 0x98, 0x13, 0x98,
	0x96, 0xeb, 0xb9, 0x97, 0xdc, 0x92, 0x17, 0x9b, 0xbc, 0xec, 0x04, 0x35, 0x09, 0xe2, 0xeb, 0xcc,
	0x44, 0x91, 0xd8, 0x55, 0x5f, 0x30, 0xe6, 0x24, 0x10, 0xd5, 0xa4, 0x3b, 0x50, 0xc2, 0x99, 0x53,
	0xb3, 0x59, 0x38, 0x00, 0xed, 0x97, 0x55, 0x98, 0xc5, 0xcc, 0x26, 0x57, 0x2a, 0x08, 0xf2, 0x33,
	0xb5, 0x56, 0xd3, 0x5f, 0x64, 0xad, 0x54, 0x85, 0x74, 0x26, 0xa9, 0x90, 0xbe, 0x0b, 0x33, 0xc2,
	0x51, 0x31, 0x7b, 0x95, 0xeb, 0x4b, 0x10, 0x69, 0x3a, 0x54, 0x9c, 0xc0, 0xfc, 0x7c, 0xc4, 0xfc,
	0x4b, 0x4a, 0x85, 0x2f, 0xca, 0x49, 0xf8, 0x01, 0x87, 0x61, 0x32, 0xfc, 0x6f, 0x92, 0x8e, 0x2f,
	0x7b, 0xa3, 0xa6, 0x15, 0x8e, 0xaf, 0xb2, 0xdc, 0x6e, 0x79, 0x65, 0xbb, 0x2d, 0xc3, 0x8c, 0x77,
	0x72, 0x12, 0x30, 0xa9, 0x45, 0x89, 0xaf, 0x38, 0xdf, 0x76, 0x4a, 0xcd, 0xb7, 0x55, 0x44, 0xc7,
	0x74, 0x42, 0x74, 0xfc, 0x46, 0x0e, 0x23, 0x7e, 0xc9, 0x7e, 0x08, 0xc1, 0xf1, 0x6d, 0x98, 0x0d,
	0x68, 0x83, 0x08, 0x0d, 0x23, 0x9b, 0x6f, 0xc5, 0x26, 0x32, 0x24, 0xb1, 0xf6, 0xae, 0x48, 0xee,
	0xcd, 0x67, 0x38, 0xd2, 0x54, 0x66, 0xa1, 0xc4, 0x5e, 0xfd, 0xa7, 0x79, 0x58, 0xc7, 0x50, 0x45,
	0xdd, 0x72, 0x0d, 0x16, 0x4b, 0xca, 0x9b, 0xdd, 0xae, 0x7a, 0x0c, 0xf3, 0x02, 0x9d, 0x4c, 0x40,
	0xaf, 0x10, 0xb4, 0x26, 0x58, 0x7e, 0x15, 0x66, 0x65, 0xbc, 0x5e, 0x6c, 0x06, 0xf1, 0x89, 0x99,
	0xcb, 0x41, 0x6c, 0x06, 0x97, 0x8c, 0x19, 0x2f, 0x40, 0xa5, 0x6c, 0x1d, 0x8a, 0xfc, 0xf4, 0x38,
	0xf1, 0xfc, 0x81, 0xcc, 0x11, 0x90, 0xdf, 0xe4, 0xe2, 0xf5, 0xcf, 0x58, 0xa8, 0x84, 0xd7, 0x08,
	0x10, 0x65, 0x2f, 0xce, 0x46, 0xd9, 0x8b, 0x89, 0x58, 0x5c, 0x31, 0x95, 0x6d, 0x50, 0x85, 0x82,
	0x35, 0x1c, 0xa2, 0x86, 0x52, 0x32, 0xf8, 0x4f, 0xfd, 0x57, 0xe0, 0x4e, 0xe6, 0x74, 0x28, 0x61,
	0x1b, 0xcb, 0x35, 0x7d, 0x44, 0x45, 0x61, 0x1b, 0x49, 0xab, 0x7f, 0x1b, 0x83, 0x51, 0x3f, 0x18,
	0x39, 0xdd, 0xb3, 0xcc, 0x60, 0x54, 0x1c, 0x71, 0xca, 0x65, 0x44, 0x9c, 0xb2, 0xca, 0x89, 0xc3,
	0xea, 0x2e, 0x5e, 0x56, 0x9b, 0x58, 0xb1, 0xfe, 0x3e, 0xe6, 0xc5, 0x4c, 0xae, 0x60, 0x62, 0xcb,
	0xef, 0x61, 0x60, 0xb9, 0x3e, 0xc2, 0xa3, 0xdc, 0x71, 0x7b, 0x6a, 0x34, 0x75, 0x62, 0x2e, 0xcd,
	0xef, 0x53, 0x8e, 0x6d, 0xba, 0x94, 0x68, 0xea, 0x57, 0x61, 0x7e, 0x48, 0x60, 0xb3, 0xcf, 0xce,
	0x59, 0x3f, 0x10, 0xd9, 0x86, 0x6b, 0x63, 0xa1, 0x1f, 0xc7, 0xed, 0xed, 0x71, 0x0a, 0xa3, 0x32,
	0x54, 0xbe, 0x82, 0xeb, 0x0c, 0x99, 0x4d, 0x91, 0x7b, 0x82, 0xb5, 0xcb, 0xd0, 0x05, 0x87, 0x60,
	0x71, 0xed, 0x29, 0x2c, 0x06, 0x2c, 0x0c, 0x79, 0xfb, 0x54, 0x0b, 0xd1, 0xd1, 0x96, 0xbc, 0x25,
	0x50, 0xa8, 0xb9, 0x10, 0xfd, 0xc3, 0xf8, 0xce, 0x9d, 0x1a, 0x35, 0x90, 0xf7, 0xea, 0x22, 0xed,
	0xd7, 0x09, 0x4c, 0x9f, 0xb9, 0xde, 0x85, 0xcb, 0x6c, 0xe9, 0x3a, 0x25, 0x93, 0xa7, 0xea, 0x04,
	0x86, 0x40, 0x90, 0xf3, 0x54, 0xdf, 0x85, 0xe5, 0x36, 0x0b, 0x13, 0x43, 0x14, 0x73, 0xca, 0x25,
	0x04, 0x76, 0x47, 0x58, 0x6e, 0xf8, 0x31, 0x59, 0xb9, 0x58, 0x83, 0x95, 0xb1, 0x8a, 0x04, 0x4b,
	0x3c, 0x47, 0xbd, 0xa3, 0x36, 0x1c, 0xf6, 0x9d, 0xae, 0x75, 0xdc, 0x67, 0xe8, 0x0e, 0x0d, 0xae,
	0x5d, 0xbb, 0xcf, 0x30, 0x7e, 0x3c, 0x5e, 0x4c, 0x2c, 0xde, 0x87, 0x30, 0x6f, 0x71, 0x1c, 0xb3,
	0xc9, 0xf5, 0x2a, 0x17, 0x4f, 0x1b, 0xbf, 0xcb, 0x63, 0x54, 0x04, 0x25, 0x55, 0xa1, 0xbf, 0xe6,
	0x16, 0x52, 0xdc, 0xd1, 0xe4, 0x50, 0xa7, 0xe5, 0x50, 0x37, 0x01, 0xf0, 0x87, 0x9a, 0x4a, 0x59,
	0x42, 0x08, 0x86, 0x17, 0x22, 0xcb, 0xb6, 0x40, 0x85, 0xf0, 0x43, 0xff, 0x9f, 0x05, 0x98, 0x26,
	0xbf, 0xfc, 0x1a, 0x14, 0xc9, 0x25, 0x1c, 0x07, 0x3a, 0xf1, 0x9b, 0x2e, 0xba, 0x11, 0x2a, 0x51,
	0x33, 0x87, 0x60, 0xcd, 0x11, 0x5a, 0x09, 0x51, 0x12, 0x1a, 0x73, 0xe5, 0xbe, 0x0e, 0x55, 0xcb,
	0xb6, 0x9d, 0xd0, 0xf1, 0xdc, 0xc8, 0xd4, 0x98, 0xc2, 0x3e, 0x2c, 0xc4, 0x70, 0xe2, 0x85, 0x6f,
	0xc0, 0x2d, 0xaa, 0x49, 0xcd, 0x20, 0x20, 0x21, 0x55, 0x45, 0x84, 0x9a, 0x3b, 0xf0, 0x5c, 0x36,
	0x1b, 0xe5, 0x33, 0xcd, 0xa7, 0x82, 0xa0, 0x38, 0x30, 0x0c, 0xa3, 0x51, 0x77, 0x94, 0x34, 0xa7,
	0xfe, 0xa5, 0x60, 0xde, 0x59, 0xec, 0x09, 0x65, 0x74, 0xed, 0xc9, 0x79, 0xe4, 0x67, 0x3d, 0xad,
	0x80, 0x38, 0xe4, 0x4a, 0x4e, 0x50, 0x23, 0x40, 0x5c, 0x9e, 0xb9, 0xa1, 0x7f, 0x29, 0x24, 0x1c,
	0x95, 0x6f, 0x70, 0x88, 0xf0, 0x51, 0x0c, 0x58, 0x68, 0x76, 0x3d, 0x97, 0xc6, 0x17, 0x5f, 0x57,
	0xde, 0xe7, 0x0a, 0xad, 0x80, 0x26, 0xc3, 0x41, 0xe5, 0x54, 0x38, 0xe8, 0x3e, 0x94, 0x7d, 0xf6,
	0xf9, 0xc8, 0xf1, 0xd9, 0x80, 0x6b, 0x04, 0xc2, 0x5f, 0xa2, 0x80, 0xb4, 0x6f, 0xc3, 0x0a, 0x6d,
	0x43, 0x65, 0x7a, 0x85, 0x28, 0xaa, 0x90, 0xc7, 0x00, 0xd1, 0xb5, 0x08, 0x4b, 0x92, 0x4b, 0xff,
	0x21, 0xe6, 0x1b, 0xe0, 0xe4, 0xe0, 0xb4, 0x8f, 0x85, 0xf5, 0x27, 0xf1, 0xc1, 0x03, 0x98, 0x13,
	0x87, 0x52, 0x10, 0x5a, 0x21, 0x13, 0xc1, 0xfd, 0x32, 0xc1, 0xb8, 0x8a, 0xce, 0x44, 0x4e, 0xc1,
	0x78, 0xdd, 0x62, 0x6b, 0xfd, 0x8b, 0x1c, 0x05, 0xbf, 0x7d, 0xe7, 0xdc, 0xe9, 0xb3, 0x1e, 0xa3,
	0x7c, 0x17, 0xf1, 0x11, 0xb7, 0x58, 0x8e, 0x60, 0x93, 0xaf, 0x77, 0x28, 0x11, 0x36, 0xfc, 0x8d,
	0x0a, 0x86, 0xdf, 0x17, 0xa7, 0x1d, 0xff, 0xc9, 0x45, 0x73, 0xdf, 0xeb, 0x9e, 0x31, 0x5b, 0xc4,
	0xba, 0xc5, 0x17, 0x9f, 0xf7, 0x91, 0xcb, 0x7f, 0x9b, 0xfd, 0x73, 0x61, 0xab, 0x17, 0x09, 0xb0,
	0x77, 0xce, 0xf7, 0xf7, 0xb1, 0xd3, 0xc3, 0x4c, 0x57, 0x3a, 0xeb, 0x66, 0x8e, 0x9d, 0xde, 0x91,
	0xdf, 0xd7, 0xff, 0x7e, 0x0e, 0xca, 0xbb, 0x9e, 0x8d, 0x4c, 0xf2, 0xc9, 0xd0, 0x89, 0xfa, 0x95,
	0x53, 0xfa, 0x15, 0x45, 0xec, 0xa8, 0xb3, 0x22, 0x62, 0xf7, 0x00, 0xe6, 0x46, 0xc3, 0x1e, 0x5e,
	0x32, 0x54, 0x7a, 0x5d, 0x16, 0x30, 0x74, 0x8f, 0xf1, 0x53, 0xd9, 0xf7, 0x7a, 0x3e, 0x0b, 0x02,
	0x19, 0x18, 0x94, 0xdf, 0x1c, 0x47, 0xae, 0xae, 0x68, 0x20, 0xd1, 0xb7, 0xfe, 0xb7, 0xf3, 0x50,
	0x94, 0x9d, 0x9a, 0x20, 0x02, 0xb3, 0xe6, 0x4f, 0xcc, 0x55, 0x21, 0x9e, 0x2b, 0x3a, 0x5b, 0xc4,
	0x42, 0x28, 0xa9, 0xb8, 0xe3, 0x97, 0x4c, 0x25, 0x15, 0x9e, 0x2d, 0xf4, 0x13, 0xd5, 0xd7, 0xf7,
	0xa0, 0x78, 0x36, 0x74, 0xa8, 0xec, 0x74, 0xc6, 0xcb, 0x00, 0xca, 0xdc, 0x19, 0xb3, 0x67, 0x43,
	0x07, 0x0b, 0x3d, 0x82, 0x79, 0x5a, 0x14, 0xb3, 0x7f, 0x4e, 0xf2, 0x83, 0xd4, 0x8e, 0x39, 0x82,
	0xee, 0x9d, 0xa3, 0x08, 0xb9, 0x0b, 0xe5, 0xee, 0xc8, 0x8f, 0x48, 0xc4, 0x55, 0xdb, 0xee, 0xc8,
	0x27, 0xbc, 0x7e, 0x1b, 0xb4, 0x5d, 0x16, 0xca, 0x06, 0xe4, 0xd1, 0x6d, 0xc2, 0x62, 0x02, 0x2a,
	0x04, 0x71, 0xf6, 0x2c, 0xad, 0x41, 0x91, 0xd7, 0xad, 0x5c, 0x96, 0x9c, 0xe5, 0xdf, 0x47, 0x3e,
	0x9e, 0x21, 0xae, 0x67, 0x06, 0xa7, 0xde, 0x85, 0x8c, 0x7f, 0xb8, 0x5e, 0xfb, 0xd4, 0xbb, 0xd0,
	0xd7, 0xf1, 0x88, 0x97, 0x0d, 0x24, 0x1c, 0xe1, 0xfa, 0xcf, 0x73, 0x78, 0x92, 0xa7, 0x91, 0x51,
	0x14, 0x7b, 0xde, 0xea, 0xf7, 0x49, 0x04, 0xc9, 0xe8, 0x73, 0x61, 0x4c, 0x05, 0x8f, 0xba, 0x3e,
	0x67, 0xf5, 0xfb, 0xf8, 0x4b, 0xba, 0x99, 0x68, 0x00, 0x79, 0x75, 0x00, 0x37, 0x60, 0xb2, 0x3b,
	0x50, 0x3a, 0x63, 0x6c, 0xa8, 0xa6, 0xbc, 0x16, 0x39, 0x00, 0x91, 0x6f, 0xc1, 0x82, 0x5a, 0xde,
	0x3c, 0x7f, 0x26, 0x24, 0x6f, 0x45, 0xa9, 0xe2, 0xd5, 0x33, 0xfd, 0xbb, 0x70, 0x1f, 0xf5, 0xb8,
	0x38, 0xbd, 0x2e, 0xbe, 0xc4, 0x7e, 0x83, 0x28, 0xc0, 0x73, 0x78, 0x70, 0x45, 0x71, 0x31, 0x3d,
	0x55, 0x28, 0x9c, 0x5a, 0xd2, 0xca, 0xe2, 0x3f, 0xf5, 0xff, 0x90, 0x83, 0x15, 0x4a, 0x32, 0x55,
	0x32, 0x09, 0xff, 0xea, 0x27, 0x9a, 0x1e, 0xa3, 0x7a, 0xba, 0x6b, 0x0d, 0x58, 0x94, 0x48, 0x42,
	0x6e, 0xff, 0xeb, 0xb4, 0x8d, 0xcc, 0x47, 0x31, 0xf2, 0x99, 0x8f, 0x62, 0xdc, 0x87, 0xbb, 0x93,
	0xda, 0x10, 0x52, 0xb9, 0x05, 0xeb, 0xa4, 0x66, 0x73, 0xa2, 0xba, 0xe5, 0xdb, 0xaf, 0x9c, 0x20,
	0xba, 0x05, 0xc3, 0xa7, 0x97, 0x7c, 0x85, 0xf1, 0x81, 0x80, 0xdf, 0xcd, 0x2b, 0x5c, 0x37, 0x9b,
	0x70, 0x27, 0xb3, 0xc6, 0x44, 0x22, 0xde, 0x0b, 0x86, 0x50, 0xcf, 0xa7, 0x0b, 0x91, 0x78, 0xdd,
	0xd1, 0x60, 0x9f, 0xeb, 0xbb, 0xa8, 0x48, 0x65, 0x63, 0x83, 0x21, 0x1f, 0x39, 0x79, 0x35, 0xe8,
	0xda, 0x51, 0xe4, 0x74, 0xab, 0x18, 0xf3, 0x08, 0xc7, 0xf7, 0x35, 0x38, 0x54, 0xff, 0x83, 0x3c,
	0x94, 0x45, 0xc3, 0x78, 0xc1, 0xf7, 0xcb, 0xa4, 0x7b, 0xa4, 0x1e, 0x08, 0xc8, 0x8f, 0x3d, 0x10,
	0x70, 0x0f, 0xca, 0x0e, 0x45, 0xfc, 0x07, 0xfc, 0x4c, 0x24, 0x09, 0x01, 0x0e, 0x46, 0xfb, 0x39,
	0xe4, 0xea, 0xa4, 0x8f, 0xd4, 0x75, 0xd7, 0x69, 0x0a, 0xbf, 0xc4, 0xd7, 0x5d, 0xb5, 0x77, 0x61,
	0xb1, 0x6f, 0x05, 0x21, 0x6f, 0xa0, 0xcb, 0xc4, 0xfd, 0xf8, 0x30, 0x10, 0x91, 0xa0, 0x2a, 0x47,
	0xb5, 0x38, 0x06, 0x1d, 0x80, 0x9d, 0x40, 0xba, 0xab, 0x66, 0x23, 0x77, 0x95, 0xc8, 0x5a, 0xa0,
	0xfc, 0x0a, 0xa1, 0xcd, 0x14, 0x9d, 0xe0, 0x10, 0xbf, 0xf5, 0x26, 0x0a, 0xa9, 0xd4, 0x54, 0x0b,
	0xab, 0x5d, 0xb1, 0xc5, 0x73, 0xd9, 0xb6, 0x78, 0x5e, 0xb1, 0xc5, 0xf5, 0x9f, 0xe4, 0xb2, 0x16,
	0x55, 0x5a, 0xde, 0xda, 0x33, 0x98, 0xf5, 0x11, 0x22, 0xf5, 0xde, 0xe4, 0xe1, 0xa0, 0x2c, 0x93,
	0x21, 0x09, 0xf9, 0xe4, 0xb8, 0xec, 0x4d, 0x68, 0x8a, 0x5e, 0x88, 0xb9, 0xe7, 0xa0, 0x43, 0xea,
	0xc9, 0x12, 0xcc, 0x38, 0x81, 0xc9, 0x5c, 0x19, 0x98, 0x9e, 0x76, 0x82, 0x86, 0x6b, 0xeb, 0x07,
	0xb0, 0x4a, 0xdc, 0xd7, 0x70, 0x43, 0xe6, 0x37, 0xf7, 0x5b, 0x56, 0x8f, 0x7d, 0x15, 0x6e, 0xbe,
	0x03, 0x6b, 0x19, 0xf5, 0x09, 0x5e, 0xfe, 0xef, 0x39, 0xb4, 0x20, 0x79, 0xdf, 0x07, 0x03, 0xcb,
	0xb5, 0x29, 0xed, 0x45, 0x71, 0x7c, 0x5c, 0xf5, 0xdc, 0x4f, 0xc0, 0x7c, 0x87, 0x05, 0x8a, 0x2f,
	0x96, 0x00, 0xa4, 0x73, 0x07, 0x5d, 0xe6, 0x26, 0x2e, 0xa7, 0x94, 0x10, 0x82, 0x5a, 0xec, 0xf7,
	0xa0, 0x72, 0xe2, 0xf4, 0x43, 0xe6, 0x9b, 0x9e, 0x14, 0x53, 0xc9, 0xd3, 0xda, 0xea, 0xf7, 0x93,
	0xe9, 0x3d, 0x73, 0x44, 0x7f, 0x48, 0x22, 0x0c, 0xa3, 0x8a, 0x3d, 0x86, 0xce, 0x51, 0x71, 0x35,
	0x8e, 0x7f, 0x1f, 0x8c, 0xd0, 0x09, 0x80, 0xa8, 0xc0, 0xf9, 0xb1, 0xbc, 0x26, 0x80, 0xb4, 0x6d,
	0xe7, 0xc7, 0x4c, 0xff, 0x1b, 0x68, 0x0e, 0x67, 0x0d, 0x56, 0x88, 0xee, 0x8f, 0x01, 0x44, 0x76,
	0x4b, 0x7c, 0x1b, 0xee, 0x7e, 0xd2, 0x57, 0x82, 0xbf, 0x6b, 0x3e, 0xb3, 0xb0, 0x34, 0x05, 0x51,
	0xba, 0xb2, 0x22, 0xfd, 0xdf, 0x4c, 0xc1, 0x62, 0x06, 0x89, 0xf6, 0x11, 0xcc, 0xe1, 0xe6, 0x1d,
	0x52, 0xf4, 0xe7, 0xda, 0xe8, 0x10, 0x5e, 0x40, 0x14, 0x1f, 0x57, 0x27, 0x6d, 0x65, 0xc7, 0x5f,
	0x0a, 0x13, 0xe2, 0x2f, 0xb4, 0xdf, 0x29, 0x0b, 0x88, 0x51, 0xd2, 0x03, 0xee, 0xf7, 0x1d, 0x01,
	0xd1, 0x3e, 0x90, 0x86, 0xd6, 0xcd, 0x63, 0x48, 0x22, 0xcc, 0x98, 0x95, 0x43, 0x34, 0x93, 0x99,
	0x43, 0x94, 0xcc, 0x33, 0x9b, 0x4d, 0xe7, 0x99, 0x69, 0x30, 0x15, 0x7a, 0x62, 0xbb, 0x97, 0x0c,
	0xfc, 0x4d, 0x11, 0x5a, 0xdf, 0x0a, 0x59, 0xef, 0x32, 0x8e, 0x1d, 0x81, 0x04, 0xd1, 0x89, 0xe2,
	0x33, 0x8c, 0x1e, 0x89, 0x77, 0x23, 0x1c, 0x5b, 0x44, 0xf5, 0xe6, 0x09, 0x4e, 0x97, 0xc9, 0x9b,
	0x36, 0x9f, 0xb0, 0x13, 0x66, 0x85, 0x23, 0x9f, 0xa9, 0x99, 0x49, 0xf3, 0xe8, 0xce, 0xac, 0x0a,
	0x4c, 0x9c, 0x9b, 0xf4, 0x1e, 0x2c, 0x27, 0xa9, 0x9d, 0x41, 0x8f, 0x4a, 0xdc, 0xc2, 0x12, 0x8b,
	0x6a, 0x89, 0xe6, 0xa0, 0x37, 0x31, 0xf5, 0x69, 0x21, 0x33, 0xf5, 0x49, 0x5e, 0x6f, 0xa6, 0x84,
	0x2d, 0xba, 0xde, 0xfc, 0x1f, 0x73, 0x50, 0x56, 0x78, 0xe1, 0x0b, 0xbd, 0xec, 0xa0, 0xfa, 0x9e,
	0x0b, 0x29, 0xdf, 0xf3, 0x43, 0xa8, 0x08, 0x32, 0xd3, 0xea, 0x3b, 0x96, 0x54, 0xd5, 0xe7, 0x04,
	0xb0, 0xc6, 0x61, 0xd2, 0x0d, 0x3d, 0x1d, 0xbb, 0xa1, 0xef, 0xc3, 0x1c, 0xba, 0xa1, 0xf9, 0xc0,
	0x07, 0xf6, 0x73, 0xa1, 0x05, 0x00, 0x87, 0x35, 0x07, 0xbd, 0x7d, 0xfb, 0xb9, 0xf6, 0x18, 0x16,
	0xa4, 0xa3, 0x5a, 0x12, 0xd1, 0xa2, 0xce, 0x91, 0xb3, 0x9a, 0xc8, 0xf4, 0x8f, 0xe0, 0xfe, 0x2e,
	0x0b, 0xc7, 0x83, 0xc0, 0x37, 0xf2, 0x2d, 0xfd, 0x4e, 0x0e, 0xef, 0xbc, 0x4e, 0x2a, 0x2d, 0xf6,
	0x2f, 0x67, 0x93, 0x53, 0xef, 0xc2, 0x4c, 0xf8, 0xb4, 0x80, 0x83, 0x44, 0x86, 0x70, 0x66, 0xbc,
	0x39, 0x9f, 0x1d, 0x6f, 0x5e, 0x81, 0x59, 0x7e, 0xf6, 0x0c, 0x99, 0x2b, 0x35, 0x67, 0x27, 0x38,
	0x1c, 0x32, 0x57, 0x3f, 0x40, 0x13, 0x34, 0xee, 0x4a, 0xd2, 0x04, 0x9d, 0x28, 0x31, 0x95, 0xfa,
	0xf2, 0x89, 0xfa, 0xc8, 0xec, 0x1c, 0xaf, 0x4f, 0xc8, 0xe8, 0xb7, 0xe0, 0x51, 0xe6, 0xc8, 0x53,
	0x57, 0xdf, 0xf5, 0xbf, 0x09, 0xeb, 0xd9, 0x44, 0x5f, 0xe1, 0xdd, 0xb6, 0xcc, 0xf9, 0x2a, 0x64,
	0xce, 0x97, 0x3e, 0x80, 0xc7, 0xd7, 0xf4, 0x53, 0xac, 0xd2, 0xf6, 0xf8, 0x95, 0xe3, 0x64, 0x22,
	0xf6, 0xe4, 0x61, 0xc4, 0x17, 0x8f, 0x75, 0x83, 0xeb, 0x7d, 0xf8, 0x92, 0x4d, 0x16, 0x23, 0x7d,
	0xb9, 0xcc, 0xdd, 0x9f, 0xe5, 0xb9, 0xea, 0x97, 0x51, 0xa9, 0xe8, 0xf9, 0x55, 0x79, 0x0e, 0xb9,
	0x2f, 0x9c, 0xe7, 0x90, 0xbf, 0x59, 0x9e, 0x43, 0xe1, 0x26, 0x79, 0x0e, 0x53, 0x37, 0xca, 0x73,
	0x98, 0xbe, 0x49, 0x9e, 0xc3, 0x4c, 0x46, 0x9e, 0xc3, 0xaf, 0xe0, 0xee, 0xcb, 0x88, 0x09, 0xdf,
	0x68, 0xf3, 0xfe, 0x11, 0x5d, 0x45, 0x9f, 0x58, 0xfc, 0xa6, 0xbb, 0xf7, 0x97, 0x10, 0x7f, 0x56,
	0x77, 0xe5, 0x54, 0x62, 0x57, 0x1e, 0xe2, 0xae, 0x54, 0xfa, 0xfc, 0x55, 0xb7, 0xf9, 0x5d, 0x8c,
	0x02, 0x64, 0x54, 0x28, 0xf6, 0xb9, 0x8e, 0xf2, 0x51, 0xe8, 0x0c, 0x5d, 0xe6, 0x86, 0xce, 0x39,
	0xeb, 0x58, 0xc1, 0x99, 0x32, 0xc5, 0xfa, 0xdf, 0xcb, 0xe3, 0x42, 0x4c, 0x22, 0x8a, 0xa3, 0xcb,
	0xc1, 0x68, 0xa0, 0x84, 0x74, 0x2b, 0x46, 0x31, 0x18, 0x0d, 0xa2, 0x78, 0x2f, 0x9f, 0xc4, 0xd8,
	0xd3, 0x53, 0x31, 0x8a, 0x2e, 0xbb, 0x20, 0xa4, 0x0e, 0x15, 0x8e, 0x14, 0x2f, 0x59, 0x45, 0xe9,
	0x40, 0x65, 0x97, 0x5d, 0xd0, 0x0b, 0x56, 0x2a, 0x2f, 0x13, 0x55, 0x1c, 0xd3, 0x95, 0xbc, 0x8c,
	0x94, 0x54, 0x1f, 0x29, 0x05, 0xf8, 0xba, 0xa5, 0x29, 0x32, 0xbb, 0x84, 0x17, 0x68, 0xde, 0x09,
	0xd4, 0xb8, 0x84, 0xf6, 0x3e, 0x94, 0x42, 0x2b, 0x38, 0x33, 0x95, 0x27, 0x68, 0xd6, 0x13, 0xfa,
	0x60, 0x62, 0xb8, 0x46, 0x91, 0x13, 0xa3, 0x1c, 0x78, 0x05, 0x2b, 0x22, 0xb1, 0xf6, 0x10, 0x9f,
	0xe1, 0x51, 0x2c, 0xf9, 0x8f, 0xa0, 0xe4, 0x49, 0x98, 0x78, 0x7c, 0x2a, 0x99, 0x96, 0x3a, 0x56,
	0x30, 0xa6, 0xd7, 0xd7, 0x61, 0x75, 0xbc, 0xde, 0xc4, 0xad, 0x45, 0x81, 0x4e, 0xd8, 0xbc, 0xba,
	0x8f, 0x6e, 0x95, 0x14, 0x2a, 0xbe, 0x49, 0x2c, 0x59, 0x23, 0xa7, 0xb2, 0x86, 0xf6, 0x5d, 0xa8,
	0x44, 0x4c, 0xad, 0x44, 0xb4, 0x27, 0xeb, 0x87, 0x51, 0x82, 0x00, 0xa6, 0xce, 0xb6, 0x61, 0xb3,
	0xee, 0xb9, 0xa1, 0xd5, 0x0d, 0x23, 0xd6, 0xa2, 0xf8, 0xfc, 0x57, 0xb1, 0x1b, 0x4c, 0xb8, 0x3b,
	0xa9, 0x52, 0x31, 0x9c, 0xb1, 0x5e, 0xe7, 0xbe, 0x50, 0xaf, 0x5d, 0x78, 0xb8, 0xcb, 0xc2, 0xdd,
	0x91, 0xd3, 0xb7, 0x91, 0x63, 0xf8, 0x6a, 0xee, 0x78, 0xbe, 0x6c, 0x4d, 0xe9, 0x7b, 0x68, 0x1d,
	0xab, 0xf7, 0x95, 0x67, 0x43, 0xeb, 0x58, 0x66, 0xbb, 0x26, 0xac, 0xab, 0x31, 0x1b, 0xaf, 0xa0,
	0xda, 0x78, 0x7f, 0x31, 0x0d, 0x0b, 0xc4, 0x9a, 0xf1, 0xb3, 0x3c, 0xdf, 0x8a, 0xa2, 0xc8, 0xd7,
	0xf5, 0x5d, 0x06, 0x92, 0xff, 0x4a, 0xe5, 0x58, 0x8e, 0xa7, 0x3f, 0xce, 0x66, 0xa4, 0x3f, 0xde,
	0x81, 0x12, 0x4d, 0x9c, 0x12, 0x0b, 0x25, 0x00, 0xdd, 0xbc, 0x56, 0x73, 0x23, 0x4b, 0x63, 0xb9,
	0x91, 0x59, 0x59, 0x8d, 0x90, 0x99, 0xd5, 0xf8, 0x1c, 0x56, 0x30, 0xc7, 0x8b, 0xce, 0xe5, 0x61,
	0xdf, 0xb3, 0x6c, 0x71, 0x75, 0x12, 0xe3, 0x07, 0x45, 0xe3, 0x76, 0xd7, 0x72, 0x51, 0xb4, 0x1d,
	0x21, 0x92, 0x2e, 0x41, 0x6a, 0x1f, 0x40, 0x59, 0xa4, 0x04, 0x21, 0x0f, 0xcc, 0x5d, 0xed, 0xb7,
	0x02, 0x3f, 0xfa, 0x9d, 0xf6, 0x4f, 0x54, 0xc6, 0xfc, 0x13, 0x8f, 0x61, 0x3e, 0x4a, 0x7b, 0xa0,
	0x88, 0x08, 0xe5, 0x63, 0x46, 0xc9, 0x10, 0x14, 0x14, 0x11, 0xe1, 0x5d, 0xd1, 0xd7, 0x85, 0x28,
	0xbc, 0x2b, 0x3a, 0x98, 0x48, 0x4b, 0xaa, 0xa6, 0xae, 0x97, 0x26, 0x12, 0x9a, 0x6e, 0xa5, 0x12,
	0x9a, 0x3e, 0x86, 0x8d, 0x8c, 0xd9, 0x30, 0x6d, 0x66, 0xd9, 0xe8, 0xf1, 0xd0, 0xb0, 0xc7, 0x6b,
	0xdd, 0xf4, 0x9c, 0x6c, 0x0b, 0x02, 0x7e, 0x22, 0xe2, 0x52, 0x30, 0x53, 0x1d, 0xe8, 0x22, 0xf9,
	0x57, 0x08, 0x73, 0x14, 0xbf, 0x3e, 0xf6, 0x77, 0x72, 0xa8, 0x4a, 0x5e, 0xb1, 0xe5, 0x22, 0x0f,
	0x2f, 0x10, 0xdf, 0x28, 0x2a, 0xda, 0xc6, 0x78, 0x6a, 0xb2, 0xf2, 0x20, 0x0c, 0xa5, 0x33, 0xc9,
	0x77, 0x4d, 0xae, 0x74, 0x7c, 0xe8, 0x2b, 0x98, 0x7f, 0xdd, 0xf1, 0x86, 0x69, 0x0d, 0xf6, 0xd7,
	0xa1, 0x88, 0x8a, 0x5e, 0xc8, 0x06, 0x5f, 0x52, 0x5f, 0x4d, 0xbc, 0x7b, 0x51, 0x48, 0xbe, 0x7b,
	0xa1, 0xef, 0x61, 0x7a, 0x47, 0xa2, 0x5d, 0x31, 0xde, 0x67, 0xea, 0x3b, 0x2d, 0x99, 0xce, 0x6c,
	0xd1, 0xad, 0xf8, 0xf9, 0x16, 0xfd, 0x8f, 0xe9, 0xe5, 0x88, 0xc8, 0x89, 0x90, 0x21, 0xba, 0xee,
	0x41, 0x19, 0x9d, 0x11, 0x09, 0x47, 0x14, 0x70, 0x90, 0x70, 0x01, 0x25, 0xbc, 0x15, 0xf9, 0xa4,
	0xb7, 0x42, 0xdb, 0xe0, 0x2a, 0xac, 0x8c, 0xc1, 0x89, 0xc0, 0x64, 0x04, 0x50, 0xe7, 0x67, 0x2a,
	0x31, 0x3f, 0xdc, 0xba, 0x76, 0xd8, 0x05, 0xb3, 0x51, 0xf5, 0x9d, 0xa6, 0x47, 0xe1, 0x08, 0x72,
	0xe4, 0xd8, 0xfa, 0xdf, 0xcd, 0xc1, 0xbd, 0x89, 0xdd, 0xfe, 0x05, 0xb9, 0x41, 0xb8, 0x4c, 0x40,
	0x16, 0x50, 0x47, 0x4f, 0xc3, 0x9b, 0xe7, 0xf0, 0x56, 0x34, 0x03, 0xfa, 0x29, 0xac, 0xb5, 0x99,
	0x70, 0xc5, 0xd0, 0x4b, 0x19, 0x96, 0x6f, 0x7f, 0x85, 0x63, 0x8b, 0x23, 0xc8, 0x4d, 0x2a, 0x6f,
	0x2f, 0xce, 0xa0, 0x37, 0xd4, 0xd6, 0x37, 0xb8, 0xd5, 0x36, 0xde, 0x92, 0x38, 0xd1, 0x07, 0x78,
	0xa2, 0x53, 0x7a, 0x65, 0xcb, 0xea, 0x9e, 0x29, 0x4e, 0x37, 0x0d, 0xa6, 0xf8, 0x38, 0x44, 0x0f,
	0xf0, 0xf7, 0xd5, 0x0b, 0x87, 0x99, 0xed, 0xf4, 0xc2, 0x82, 0xe2, 0xff, 0x2a, 0x0b, 0x18, 0xde,
	0x86, 0xfc, 0x8d, 0x1c, 0x25, 0x58, 0x24, 0xdb, 0x8b, 0x63, 0x3c, 0xf4, 0x7a, 0xa4, 0x88, 0xf1,
	0xe0, 0x07, 0x9f, 0x0c, 0x6e, 0x16, 0x0c, 0xa4, 0xda, 0x56, 0x34, 0x66, 0x4f, 0xad, 0x60, 0x9f,
	0x6b, 0x59, 0xef, 0xc1, 0x2c, 0xa5, 0x7e, 0x06, 0x22, 0x99, 0x6d, 0x2d, 0x23, 0xb5, 0x55, 0x04,
	0x71, 0x24, 0xa5, 0xfe, 0x5f, 0x28, 0xc6, 0x53, 0x3b, 0xb7, 0x9c, 0xbe, 0x75, 0xdc, 0x17, 0xa9,
	0xa5, 0xbf, 0xc4, 0x41, 0xa7, 0x72, 0x9d, 0xc5, 0x15, 0xcd, 0x38, 0xd7, 0xf9, 0x61, 0x7c, 0xb2,
	0x92, 0xe1, 0x33, 0x1d, 0xdd, 0x80, 0xe1, 0xc0, 0xba, 0xb4, 0x7c, 0xba, 0xa7, 0x5e, 0xc0, 0x5c,
	0x53, 0x8e, 0x78, 0x86, 0xee, 0xc1, 0x12, 0xb4, 0x2e, 0x06, 0xf7, 0xb7, 0xc8, 0x9f, 0x3b, 0x36,
	0xb8, 0xff, 0xa7, 0x33, 0xfc, 0x7b, 0x79, 0x98, 0x53, 0x31, 0x64, 0x94, 0x62, 0xda, 0x6e, 0x9c,
	0xdc, 0x4a, 0x00, 0xf1, 0xc4, 0x09, 0x21, 0x15, 0xc9, 0x06, 0x04, 0x42, 0xd9, 0xf6, 0x04, 0xaa,
	0xa3, 0xc0, 0xc2, 0x00, 0xe7, 0xc0, 0x09, 0x55, 0x3f, 0xdf, 0x3c, 0xc2, 0xf7, 0x38, 0x18, 0xbd,
	0x7c, 0x6f, 0xc1, 0xc2, 0xb9, 0xd5, 0x77, 0x6c, 0x93, 0x8e, 0x04, 0x4e, 0x28, 0xde, 0x88, 0x41,
	0xf0, 0x36, 0x3f, 0x0f, 0x84, 0x37, 0xb0, 0xeb, 0xf9, 0x2e, 0xf3, 0x89, 0x66, 0x5a, 0x36, 0xc9,
	0x41, 0x48, 0xf0, 0x1e, 0x2c, 0x0f, 0x2c, 0xc7, 0xa5, 0x98, 0x9a, 0xcf, 0xec, 0x51, 0x57, 0x5e,
	0xb0, 0x25, 0xd7, 0xea, 0x22, 0xc7, 0x72, 0x2d, 0xc0, 0x40, 0x1c, 0xd9, 0x5d, 0x3a, 0x54, 0xb8,
	0xba, 0x82, 0x65, 0xb0, 0xde, 0x59, 0xf1, 0x30, 0xd6, 0xe8, 0x98, 0x93, 0xf2, 0x8a, 0xf5, 0xff,
	0x9c, 0x8f, 0xbc, 0xdc, 0x78, 0x41, 0x29, 0x91, 0x5d, 0xb6, 0x05, 0xb7, 0xe8, 0xda, 0x92, 0x99,
	0x92, 0x42, 0x15, 0x63, 0x81, 0xc5, 0xe4, 0x28, 0x69, 0x26, 0xca, 0x81, 0x7b, 0x50, 0x16, 0x95,
	0xa8, 0xcf, 0xd3, 0x11, 0x08, 0xb7, 0xe0, 0xcf, 0x73, 0xb0, 0xa0, 0x34, 0x2e, 0x9e, 0xf4, 0xda,
	0x6c, 0x7c, 0xd6, 0x3a, 0x6c, 0x37, 0xcc, 0xfa, 0x61, 0xad, 0xfe, 0x32, 0xeb, 0x8a, 0x52, 0x26,
	0x49, 0xa3, 0xdd, 0x3a, 0x34, 0x3a, 0x66, 0xcd, 0x68, 0xd4, 0xaa, 0x39, 0xed, 0x31, 0x3c, 0x98,
	0x48, 0xf2, 0x49, 0xf3, 0x60, 0xd7, 0xec, 0xd4, 0x5e, 0x54, 0xf3, 0xda, 0x13, 0x78, 0x34, 0x91,
	0xec, 0x68, 0xb7, 0x6e, 0xd6, 0x5f, 0xd6, 0x0e, 0x0e, 0x1a, 0x7b, 0xd5, 0x82, 0xf6, 0x0e, 0x3c,
	0x99, 0x48, 0xf9, 0xb2, 0xb1, 0xd7, 0x6a, 0x18, 0x66, 0xad, 0xde, 0x69, 0xbe, 0x6a, 0x76, 0x5e,
	0x57, 0xa7, 0x14, 0x87, 0xbf, 0x3a, 0xb5, 0x42, 0xce, 0x7d, 0x86, 0x87, 0x56, 0xad, 0x1b, 0x3a,
	0xe7, 0x4e, 0x78, 0x99, 0xf0, 0xb1, 0x2b, 0x4f, 0x36, 0xc4, 0xbe, 0xfb, 0x5c, 0xda, 0x77, 0x3f,
	0xd1, 0x5e, 0xb0, 0xf0, 0x5c, 0xc9, 0xae, 0x39, 0x7a, 0x6b, 0xae, 0x92, 0xbc, 0xd6, 0x9b, 0xbb,
	0xd6, 0xef, 0x3f, 0x54, 0xae, 0xf4, 0xea, 0xf7, 0xe3, 0x13, 0x77, 0x9f, 0x82, 0xae, 0xfc, 0xe0,
	0x51, 0x02, 0x24, 0x7a, 0x08, 0x73, 0xf4, 0x22, 0x77, 0xfc, 0x22, 0x40, 0xb6, 0xc5, 0xf5, 0x0e,
	0x68, 0x03, 0x24, 0x14, 0xde, 0x06, 0xf5, 0xfe, 0x60, 0x75, 0xa0, 0x54, 0x21, 0x25, 0x97, 0xf2,
	0x2c, 0x43, 0x21, 0xf5, 0x2c, 0x83, 0xfe, 0x9f, 0xf2, 0xf1, 0x99, 0x3a, 0xd6, 0xb1, 0xd8, 0x26,
	0xef, 0x79, 0xb6, 0xa9, 0x06, 0xef, 0x8b, 0x3d, 0x99, 0xfb, 0xf0, 0x18, 0xe6, 0x43, 0xcf, 0xb6,
	0x2e, 0x4d, 0xe4, 0xcf, 0x51, 0x64, 0x98, 0x57, 0x10, 0xda, 0x10, 0x40, 0xae, 0xce, 0x53, 0xf0,
	0x90, 0x1f, 0xe7, 0x8a, 0x79, 0x3e, 0x87, 0xd0, 0x57, 0x0e, 0xbb, 0xe0, 0xf6, 0xf9, 0xfb, 0xb0,
	0xca, 0x4d, 0x6d, 0x37, 0x8c, 0x52, 0x6b, 0x62, 0x7a, 0x12, 0xba, 0x4b, 0x84, 0x97, 0xb9, 0x35,
	0xb2, 0xe0, 0x5d, 0x94, 0xcf, 0x7c, 0x25, 0x64, 0xe6, 0x12, 0xd7, 0x56, 0x22, 0x88, 0xf6, 0x75,
	0xa8, 0x0e, 0x7d, 0xef, 0x47, 0xac, 0x1b, 0x32, 0xdb, 0x14, 0x6f, 0x56, 0x93, 0x1c, 0x58, 0x88,
	0xe0, 0x4d, 0x7a, 0xbc, 0x7a, 0x37, 0x3d, 0xbd, 0xca, 0x5f, 0x2e, 0x24, 0x45, 0xa7, 0xba, 0x5c,
	0xc9, 0x99, 0xc7, 0x25, 0x7f, 0x84, 0x9e, 0xa3, 0x84, 0xed, 0x8f, 0x3a, 0x79, 0x32, 0xcf, 0x71,
	0x07, 0x4d, 0xc9, 0xc9, 0x54, 0x37, 0x74, 0x30, 0xe9, 0x3b, 0x18, 0x0d, 0xdb, 0xf5, 0xad, 0x63,
	0x7a, 0xaa, 0xfb, 0xf0, 0x9c, 0x9b, 0xbc, 0xec, 0x42, 0xee, 0x8d, 0xb7, 0x60, 0xa1, 0x2f, 0x1e,
	0xec, 0xc1, 0x47, 0xb5, 0x43, 0x0a, 0xf3, 0x17, 0x8c, 0x4a, 0x9f, 0x02, 0xfb, 0x1c, 0xda, 0x09,
	0xf4, 0x4b, 0x0c, 0x34, 0x65, 0xd5, 0x23, 0x7a, 0xf2, 0x0e, 0x68, 0x42, 0x61, 0x17, 0xae, 0x16,
	0x25, 0x10, 0x5c, 0x25, 0x0c, 0xf9, 0x5b, 0xa4, 0xf7, 0xb0, 0xe7, 0x39, 0x6e, 0x2f, 0x41, 0x2c,
	0xbc, 0x87, 0x88, 0x88, 0x69, 0xf5, 0x1e, 0xee, 0x91, 0x16, 0x73, 0x6d, 0xc7, 0xed, 0x45, 0x2f,
	0x8f, 0xab, 0x21, 0xbd, 0xac, 0xa3, 0x5d, 0x83, 0x29, 0xe5, 0x54, 0xc7, 0xdf, 0x9c, 0xe9, 0x95,
	0x71, 0x16, 0x70, 0x9c, 0x25, 0x3f, 0x1a, 0xe3, 0xef, 0x93, 0x22, 0x99, 0xdd, 0x92, 0x18, 0x66,
	0x2d, 0xc3, 0x8e, 0x48, 0x86, 0x94, 0x0e, 0x5d, 0xf6, 0x09, 0xe3, 0x06, 0xa2, 0x2d, 0x9e, 0x36,
	0x18, 0x58, 0xae, 0xad, 0x5a, 0x13, 0x57, 0x1c, 0xca, 0xd7, 0x74, 0x70, 0x1b, 0x55, 0x00, 0xde,
	0xb1, 0x63, 0x66, 0x7f, 0xd9, 0x59, 0xd0, 0x03, 0x8c, 0xe7, 0x8f, 0xd7, 0x12, 0x25, 0x46, 0x8e,
	0x8f, 0x30, 0xf5, 0x06, 0x9c, 0x9c, 0x99, 0xb4, 0x9d, 0x34, 0x79, 0x64, 0xfa, 0xbf, 0xcc, 0x41,
	0x35, 0x2a, 0xa7, 0x08, 0xe6, 0xe1, 0xe8, 0xb8, 0xef, 0x04, 0xa7, 0xb1, 0xfa, 0x50, 0x12, 0x10,
	0x61, 0x17, 0xf9, 0xd2, 0x51, 0x22, 0xb4, 0x33, 0x0e, 0x88, 0xde, 0x03, 0x44, 0x64, 0xac, 0x34,
	0x10, 0x92, 0x9f, 0xf2, 0x9b, 0x00, 0x88, 0xa4, 0x37, 0x0e, 0xc5, 0xbf, 0xae, 0x70, 0x08, 0x3d,
	0x71, 0xf8, 0x14, 0x16, 0x63, 0x74, 0xfc, 0xae, 0x01, 0xc9, 0x87, 0x5b, 0x11, 0x9d, 0x7c, 0xdb,
	0x40, 0x5f, 0x84, 0x5b, 0x4a, 0xdf, 0xc5, 0xb9, 0xf3, 0x3e, 0x2c, 0xd7, 0x82, 0x4b, 0xb7, 0x8b,
	0xe9, 0x33, 0x35, 0xf1, 0x02, 0xa7, 0x1c, 0x96, 0xd2, 0x7a, 0x2e, 0xd5, 0xba, 0xbe, 0x06, 0x2b,
	0x63, 0x05, 0x45, 0x9d, 0xdf, 0x85, 0x0d, 0x4c, 0xde, 0x8f, 0x51, 0xc9, 0xf4, 0x93, 0x6b, 0x6a,
	0xfe, 0x57, 0x39, 0xd8, 0x9c, 0x50, 0x5e, 0x2c, 0x2e, 0x5e, 0x2f, 0x67, 0xdd, 0x33, 0xe9, 0x7a,
	0xc9, 0xc9, 0xeb, 0xe5, 0xac, 0x7b, 0x46, 0xa4, 0xfa, 0x1b, 0x28, 0xd7, 0xe3, 0x4f, 0x6d, 0x03,
	0x56, 0xeb, 0x2f, 0x1b, 0xf5, 0x4f, 0xcc, 0x76, 0xa7, 0xd6, 0x39, 0x6a, 0xa7, 0x74, 0x87, 0x35,
	0x58, 0x4a, 0x60, 0xf1, 0xa3, 0x79, 0xb0, 0x5b, 0xcd, 0x69, 0xcb, 0xa0, 0x25, 0x50, 0xf4, 0x3c,
	0x26, 0x5e, 0xab, 0x4e, 0xc0, 0xe5, 0xc3, 0x99, 0x05, 0xfd, 0x7f, 0xe7, 0x61, 0x29, 0x73, 0xf7,
	0x5c, 0xc7, 0x28, 0x13, 0x55, 0xa6, 0x84, 0x65, 0x5d, 0xb8, 0xca, 0xb2, 0x4e, 0xbf, 0x28, 0xc9,
	0x5b, 0x44, 0xf7, 0x98, 0xb0, 0x39, 0x49, 0xb3, 0x47, 0xc8, 0x91, 0x43, 0x9a, 0x3d, 0xa1, 0x7b,
	0xcc, 0xb5, 0x99, 0x7c, 0x4a, 0x72, 0x8e, 0x80, 0xbb, 0x08, 0xe3, 0xb2, 0x55, 0x10, 0x85, 0x56,
	0x2f, 0x3e, 0x2f, 0x4a, 0x86, 0x28, 0xdb, 0xb1, 0x28, 0xb8, 0xfa, 0x16, 0x2c, 0x88, 0x3c, 0xce,
	0x88, 0xae, 0x48, 0x74, 0x04, 0x96, 0x74, 0xdf, 0x81, 0x35, 0x41, 0x47, 0x5e, 0x47, 0x53, 0x4d,
	0x48, 0x25, 0x37, 0xd6, 0x0a, 0x11, 0x90, 0xfd, 0x6b, 0x28, 0xc9, 0xa9, 0xf7, 0xa0, 0xdc, 0xa3,
	0x1d, 0x8f, 0xa7, 0x26, 0x05, 0x92, 0x41, 0x80, 0xea, 0x6e, 0xa8, 0xff, 0xaf, 0x1c, 0x54, 0x12,
	0x1b, 0x5b, 0xfb, 0x0e, 0xcc, 0xd8, 0x38, 0xfd, 0xc2, 0x93, 0x78, 0x13, 0x31, 0x27, 0x4a, 0xfc,
	0xa5, 0xd9, 0xba, 0x72, 0xdc, 0x92, 0xdf, 0x67, 0xe2, 0x71, 0x13, 0x7f, 0x6f, 0x7d, 0x1f, 0x4a,
	0xf8, 0x08, 0x2b, 0x3e, 0xf2, 0xba, 0x04, 0xb7, 0xa2, 0x0f, 0xe5, 0x45, 0xd7, 0xdb, 0x50, 0x8d,
	0xc1, 0xfb, 0x87, 0x2f, 0x9a, 0x78, 0x85, 0xbf, 0x0a, 0x73, 0x31, 0xb4, 0x55, 0xaf, 0xe6, 0xb7,
	0x7e, 0x69, 0xef, 0x02, 0x6c, 0xfd, 0x1a, 0xcc, 0xd1, 0xf5, 0xbe, 0x86, 0x2f, 0xeb, 0x16, 0xda,
	0x72, 0xf6, 0x5b, 0x07, 0x2a, 0xb2, 0xd5, 0x30, 0xda, 0x87, 0x07, 0xb5, 0x3d, 0xaa, 0x5d, 0xc5,
	0xec, 0x1e, 0x35, 0xf7, 0xb6, 0xab, 0xf9, 0xad, 0xdf, 0xce, 0xc1, 0x3c, 0x55, 0xdf, 0x96, 0x7f,
	0x8e, 0x71, 0x1f, 0x36, 0x04, 0x25, 0xdf, 0xa6, 0x59, 0xad, 0xc4, 0x5d, 0x88, 0x29, 0x3e, 0x6d,
	0x34, 0x3e, 0xd9, 0x7b, 0x5d, 0xcd, 0x71, 0xa9, 0x31, 0x86, 0xdc, 0x3f, 0x3c, 0xe8, 0xbc, 0xdc,
	0x7b, 0x4d, 0x22, 0x60, 0x0c, 0x5b, 0xdb, 0xdb, 0xab, 0x16, 0xb6, 0xce, 0x60, 0x21, 0xf5, 0xf7,
	0x19, 0xdc, 0x3c, 0xd9, 0xab, 0xbd, 0x68, 0xec, 0x99, 0xed, 0xc3, 0x23, 0xa3, 0xde, 0xc8, 0xea,
	0xca, 0x1a, 0x2c, 0x8d, 0x93, 0xb4, 0x50, 0x0a, 0xad, 0xc3, 0xf2, 0x38, 0xea, 0xd3, 0xc6, 0x8b,
	0x56, 0x35, 0xbf, 0xf5, 0xbb, 0x39, 0x58, 0xa0, 0xbf, 0x01, 0x50, 0x5f, 0xef, 0xdf, 0xac, 0x1d,
	0x6d, 0x37, 0x3b, 0x66, 0xbb, 0x51, 0x33, 0xb2, 0xed, 0xa5, 0x4d, 0x58, 0x1b, 0x27, 0x79, 0xf1,
	0xda, 0x3c, 0x6a, 0xe3, 0x7b, 0x12, 0x77, 0x61, 0x3d, 0x13, 0x2d, 0x26, 0x9b, 0x77, 0x76, 0x1c,
	0x4f, 0xa3, 0xff, 0x19, 0x37, 0xe0, 0x70, 0x1d, 0x6a, 0xd1, 0xfb, 0xa5, 0x9b, 0xb0, 0x26, 0x6d,
	0xb1, 0x56, 0x6b, 0xef, 0x75, 0xc6, 0xb3, 0xc7, 0xe3, 0x68, 0x65, 0xc5, 0xe3, 0x55, 0x52, 0xf0,
	0xb2, 0x2b, 0x6f, 0xc3, 0xc3, 0xc9, 0x85, 0xcd, 0xce, 0xa1, 0x20, 0x2c, 0x6c, 0x7d, 0x04, 0x10,
	0x3b, 0xa4, 0xf9, 0x76, 0x30, 0x1a, 0x3b, 0x47, 0x07, 0xdb, 0x54, 0x60, 0xe7, 0x68, 0x6f, 0xaf,
	0xfa, 0x35, 0x6d, 0x05, 0x16, 0x55, 0x68, 0xab, 0x66, 0x74, 0x9a, 0xbc, 0x0b, 0x7c, 0x57, 0xc4,
	0x4f, 0x87, 0xf1, 0xb5, 0xdf, 0x31, 0x1a, 0x8d, 0x1f, 0x46, 0x0b, 0x49, 0x5f, 0x54, 0x81, 0x8a,
	0xd9, 0x39, 0x34, 0x1a, 0xaf, 0xe4, 0x0b, 0x1d, 0x2a, 0xa2, 0x73, 0x68, 0x76, 0x9a, 0xfb, 0x8d,
	0x6a, 0x7e, 0xeb, 0xb7, 0x72, 0x30, 0xb7, 0xad, 0x46, 0x7e, 0x37, 0x61, 0x6d, 0xbb, 0xd9, 0xae,
	0x1f, 0x1e, 0x1d, 0x74, 0x26, 0xac, 0x5c, 0x12, 0xbd, 0xd3, 0x34, 0xda, 0x1d, 0xd3, 0x38, 0x3c,
	0x3a, 0xd8, 0xae, 0xe6, 0x78, 0xd7, 0x92, 0xe8, 0xfa, 0xe1, 0x51, 0xeb, 0xf0, 0xa0, 0x9a, 0xe7,
	0xb3, 0x9c, 0xc4, 0x1c, 0x34, 0x3e, 0x15, 0xcf, 0x81, 0x34, 0x8c, 0x6a, 0x61, 0xab, 0x0d, 0xa5,
	0xe8, 0x72, 0x43, 0xcc, 0x72, 0x19, 0x3d, 0xb8, 0x0d, 0x55, 0x05, 0x87, 0xb6, 0x6f, 0x35, 0x97,
	0x82, 0xb6, 0x3f, 0x69, 0xee, 0xed, 0x55, 0xf3, 0x5b, 0x3e, 0x54, 0xd3, 0xe1, 0x3a, 0xce, 0x9e,
	0x2f, 0x0f, 0xdb, 0x1d, 0x6e, 0x72, 0x1f, 0xb6, 0x1a, 0x06, 0xbd, 0x19, 0x9d, 0x6c, 0x62, 0x03,
	0x56, 0xc7, 0x49, 0x1a, 0x07, 0xb5, 0x17, 0x28, 0x5f, 0x36, 0x61, 0x6d, 0x1c, 0xbb, 0xdd, 0x6c,
	0x23, 0x3a, 0xbf, 0xf5, 0x47, 0x39, 0x98, 0xed, 0x08, 0x31, 0xbd, 0x0a, 0xb7, 0x3b, 0xb5, 0x17,
	0x59, 0xa3, 0xd0, 0x60, 0x3e, 0xc2, 0xb4, 0x6a, 0xaf, 0x1b, 0xdb, 0x24, 0x5a, 0x22, 0x98, 0xd1,
	0xa8, 0x37, 0x9a, 0xaf, 0x1a, 0x82, 0xdb, 0x23, 0x70, 0xf3, 0xc0, 0x24, 0x0e, 0xe1, 0x0a, 0x42,
	0x21, 0x8d, 0xaa, 0xb5, 0x5a, 0x8d, 0xda, 0x1e, 0x47, 0x4d, 0x25, 0x2a, 0xab, 0xd7, 0x0e, 0xea,
	0x8d, 0xbd, 0xc6, 0x76, 0x75, 0x3a, 0x01, 0xde, 0x69, 0x1e, 0x34, 0xdb, 0x2f, 0x1b, 0xdb, 0xd5,
	0x99, 0xad, 0x9f, 0xe5, 0x61, 0x8e, 0xd8, 0x33, 0x7a, 0xbc, 0x7e, 0x4d, 0xb0, 0x62, 0xa6, 0xd2,
	0x72, 0x07, 0x56, 0x92, 0xe8, 0xb8, 0x57, 0x39, 0xed, 0x1e, 0xdc, 0xc9, 0x42, 0x9a, 0xb5, 0x7a,
	0xbd, 0xd1, 0xea, 0x54, 0xf3, 0x13, 0x09, 0x8c, 0xc6, 0xf7, 0x1b, 0xf5, 0x4e, 0xb5, 0x30, 0x5e,
	0xbd, 0x3a, 0x32, 0x1d, 0xee, 0x4e, 0x40, 0xca, 0x16, 0xa6, 0xaf, 0xa2, 0x11, 0x8d, 0xcc, 0x68,
	0x0f, 0xe1, 0xde, 0xc4, 0x7a, 0x5a, 0xad, 0x3d, 0x3e, 0xd0, 0xd9, 0xad, 0x7f, 0x97, 0x83, 0xe5,
	0x3a, 0x3e, 0x1d, 0x22, 0x14, 0xfd, 0x76, 0x14, 0x62, 0xfb, 0x3a, 0x3c, 0x96, 0x13, 0x6b, 0x1e,
	0x1a, 0xdb, 0x0d, 0xc3, 0x6c, 0x1f, 0xbd, 0xc8, 0x9e, 0xae, 0x6f, 0xc0, 0xdb, 0x93, 0x49, 0x5b,
	0x7b, 0xb5, 0xd7, 0x0d, 0x43, 0x2c, 0x52, 0x35, 0xa7, 0x6d, 0xc1, 0x5b, 0x93, 0x89, 0xc9, 0xd7,
	0xc3, 0xfb, 0xdd, 0x6e, 0x54, 0xf3, 0x57, 0x57, 0x2c, 0xfc, 0x42, 0xcd, 0xfd, 0xc6, 0xe1, 0x51,
	0xa7, 0x5a, 0xd8, 0xfa, 0xb3, 0x9c, 0xb8, 0x56, 0x1f, 0xeb, 0xa5, 0xa2, 0x4c, 0x56, 0x9f, 0x97,
	0x41, 0x4b, 0x60, 0x25, 0x97, 0xae, 0xc1, 0x52, 0x02, 0xae, 0x70, 0xea, 0x26, 0xac, 0x25, 0x50,
	0xe3, 0xdc, 0x9a, 0x40, 0x47, 0xfc, 0x37, 0x35, 0x86, 0x52, 0x38, 0x76, 0xbc, 0x3d, 0x5e, 0x23,
	0x72, 0xed, 0x9f, 0xe4, 0xa0, 0xba, 0x9f, 0x76, 0xcb, 0xe8, 0x70, 0x77, 0xbf, 0xd6, 0xee, 0xf0,
	0x02, 0x9f, 0x36, 0x3b, 0xd9, 0xe7, 0xcf, 0x23, 0xb8, 0x9f, 0x41, 0xf3, 0x83, 0xa3, 0x66, 0xfd,
	0x13, 0x39, 0x9e, 0x6a, 0x8e, 0x4f, 0x6e, 0x06, 0x95, 0x22, 0xf0, 0x4c, 0x29, 0xd1, 0xaa, 0x79,
	0xed, 0x1d, 0x78, 0x92, 0x41, 0xac, 0x0a, 0xb9, 0x98, 0xba, 0xb0, 0x75, 0x00, 0xc5, 0x5d, 0xa9,
	0xcb, 0xad, 0xc1, 0xd2, 0xae, 0x31, 0x51, 0x4a, 0xc4, 0xa8, 0x4e, 0xe3, 0xb3, 0x4e, 0x35, 0xa7,
	0x2d, 0xc2, 0x42, 0x0c, 0xe3, 0xc7, 0xe0, 0x61, 0x35, 0xbf, 0xe5, 0x01, 0xec, 0x46, 0x0a, 0x19,
	0xdf, 0x3e, 0x48, 0x92, 0xb9, 0xae, 0x2b, 0xb0, 0xa8, 0x22, 0x5b, 0x0d, 0xb9, 0x6d, 0x65, 0xc5,
	0x02, 0xd1, 0x38, 0xe0, 0x4b, 0xba, 0x0c, 0x9a, 0x0a, 0xac, 0xbf, 0x3c, 0x6c, 0x37, 0x0e, 0xaa,
	0x85, 0x17, 0x95, 0x1f, 0x96, 0x95, 0x7f, 0x30, 0x3d, 0x9e, 0xc1, 0xff, 0x21, 0x7d, 0xef, 0xff,
	0x06, 0x00, 0x00, 0xff, 0xff, 0x45, 0x50, 0x67, 0xbf, 0xf2, 0x74, 0x00, 0x00,
}
