package channel_wedding

import (
    "context"
    "fmt"
    tyr_http "gitlab.ttyuyin.com/avengers/tyr/core/service/http"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channelol_go"
    "golang.52tt.com/clients/account"
    "golang.52tt.com/clients/channel"
    "golang.52tt.com/clients/gnobility"
    "golang.52tt.com/clients/guild"
    "golang.52tt.com/clients/nobility"
    userProfileApi "golang.52tt.com/clients/user-profile-api"
    userPresent "golang.52tt.com/clients/userpresent"
    youknowwho "golang.52tt.com/clients/you-know-who"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    grpcProtocol "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app"
    "golang.52tt.com/protocol/common/status"
    channel_wedding_plan "golang.52tt.com/protocol/services/channel-wedding-plan"
    fellow_svr "golang.52tt.com/protocol/services/fellow-svr"
    gNobilityPb "golang.52tt.com/protocol/services/gnobility"
    guildpb "golang.52tt.com/protocol/services/guildsvr"
    userPresentpb "golang.52tt.com/protocol/services/userpresent"
    youknowwhopb "golang.52tt.com/protocol/services/youknowwho"
    "golang.52tt.com/services/channel-wedding-common/constant"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/common"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/conf"
    channel_wedding_http "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/models/channel-wedding"

    "google.golang.org/grpc/codes"

    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    channel_wedding_conf "golang.52tt.com/protocol/services/channel-wedding-conf"
    "time"
)

type ChannelWeddingMgr struct {
    channelWeddingPlanSvr channel_wedding_plan.ChannelWeddingPlanClient
    channelSvr            channel.IClient
    userProfileCli        userProfileApi.IClient
    fellowSvr             fellow_svr.FellowSvrClient
    youKnowWhoClient      youknowwho.IClient
    accountCli            account.IClient

    channelWeddingCli     channel_wedding.ChannelWeddingClient
    channelWeddingConfCli channel_wedding_conf.ChannelWeddingConfClient
    userPresentCli        userPresent.IClient
    guildCli              guild.IClient
    ChannelOlGoCli       channelol_go.ChannelolGoClient
    GNobilityCli          gnobility.IClient
    NobilityCli           nobility.IClient
}

func NewChannelWeddingMgr(
    channelWeddingPlanSvr channel_wedding_plan.ChannelWeddingPlanClient,
    channelSvr channel.IClient,
    userProfileCli userProfileApi.IClient,
    fellowSvr fellow_svr.FellowSvrClient,
    youKnowWhoClient youknowwho.IClient,
    accountCli account.IClient,
    channelWeddingCli channel_wedding.ChannelWeddingClient,
    channelWeddingConfCli channel_wedding_conf.ChannelWeddingConfClient,
    userPresentCli        userPresent.IClient,
    guildCli              guild.IClient,
    channelOlGoCli        channelol_go.ChannelolGoClient,
    gNobilityCli          gnobility.IClient,
    nobilityCli           nobility.IClient,
) *ChannelWeddingMgr {
    return &ChannelWeddingMgr{
        channelWeddingPlanSvr: channelWeddingPlanSvr,
        channelSvr:            channelSvr,
        userProfileCli:        userProfileCli,
        fellowSvr:             fellowSvr,
        youKnowWhoClient:      youKnowWhoClient,
        accountCli:            accountCli,
        channelWeddingCli:     channelWeddingCli,
        channelWeddingConfCli: channelWeddingConfCli,
        userPresentCli:        userPresentCli,
        guildCli:              guildCli,
        ChannelOlGoCli:        channelOlGoCli,
        GNobilityCli:          gNobilityCli,
        NobilityCli:           nobilityCli,
    }
}

func (mgr *ChannelWeddingMgr) Register(cfg *conf.ServiceConfig, router *tyr_http.Router) {
    child := router.Child("/channel_wedding")
    child.POST("/get_my_wedding_reserve_info", common.HandleWrapperG(cfg, mgr.GetMyWeddingReserveInfo))
    child.POST("/get_wedding_reserve_info", common.HandleWrapperG(cfg, mgr.GetWeddingReserveInfo))
    child.POST("/save_wedding_reserve", common.HandleWrapperG(cfg, mgr.SaveWeddingReserve))
    child.POST("/change_wedding_reserve", common.HandleWrapperG(cfg, mgr.ChangeWeddingReserve))
    child.POST("/get_wedding_plan_base_info", common.HandleWrapperG(cfg, mgr.GetWeddingPlanBaseInfo))
    child.POST("/get_inner_channel_wedding_reserved_list", common.HandleWrapperG(cfg, mgr.GetInnerChannelWeddingReservedList))
    child.POST("/get_playmate_list", common.HandleWrapperG(cfg, mgr.GetPlaymateList))
    child.POST("/get_groomsman_and_bridesmaid_info", common.HandleWrapperG(cfg, mgr.GetGroomsmanAndBridesmaidInfo))
    child.POST("/get_wedding_friend_info", common.HandleWrapperG(cfg, mgr.GetWeddingFriendInfo))
    child.POST("/invite_wedding_guest", common.HandleWrapperG(cfg, mgr.InviteWeddingGuest))
    child.POST("/del_wedding_guest", common.HandleWrapperG(cfg, mgr.DelWeddingGuest))
    child.POST("/consult_wedding_reserve", common.HandleWrapperG(cfg, mgr.ConsultWeddingReserve))
    child.POST("/get_channel_reserved_info", common.HandleWrapperG(cfg, mgr.GetChannelReservedInfo))
    child.POST("/get_wedding_rank", common.HandleWrapperG(cfg, mgr.GetChannelWeddingRankInfo))
    child.POST("/arrange_wedding_reserve", common.HandleWrapperG(cfg, mgr.ArrangeWeddingReserveRequest))
    child.POST("/get_wedding_big_screen_info", common.HandleWrapperG(cfg, mgr.GetWeddingBigScreenInfo))
    child.POST("/is_show_info_card", common.HandleWrapperG(cfg, mgr.IsShowInfoCard))
}

func (mgr *ChannelWeddingMgr) GetMyWeddingReserveInfo(ctx context.Context, req *channel_wedding_http.GetMyWeddingReserveInfoRequest) (*channel_wedding_http.GetMyWeddingReserveInfoResponse, error) {
    resp := &channel_wedding_http.GetMyWeddingReserveInfoResponse{}

    myReserveInfoResp, err := mgr.channelWeddingPlanSvr.GetMyWeddingReserveInfo(ctx, &channel_wedding_plan.GetMyWeddingReserveInfoRequest{
        WeddingPlanId: req.GetWeddingPlanId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetMyWeddingReserveInfo fail to GetMyWeddingReserveInfo. req:%v, err:%v", req, err)
        return resp, err
    }

    resp.ReserveDate = myReserveInfoResp.GetReserveDate()
    resp.ChannelId = myReserveInfoResp.GetChannelId()
    resp.ReserveTimeSection = myReserveInfoResp.GetReserveTimeSection()
    resp.RemainChangeTimes = myReserveInfoResp.GetRemainChangeTimes()
    resp.ChangeLimitTime = myReserveInfoResp.GetChangeLimitTime()
    resp.InChangeTime = myReserveInfoResp.GetInChangeTime()

    return resp, nil
}

func (mgr *ChannelWeddingMgr) GetWeddingReserveInfo(ctx context.Context, req *channel_wedding_http.GetWeddingReserveInfoRequest) (*channel_wedding_http.GetWeddingReserveInfoResponse, error) {
    resp := &channel_wedding_http.GetWeddingReserveInfoResponse{}

    reserveableInfoResp, err := mgr.channelWeddingPlanSvr.GetWeddingReserveInfo(ctx, &channel_wedding_plan.GetWeddingReserveInfoRequest{
        ThemeType:   req.GetThemeType(),
        ReserveDate: req.GetReserveDate(),
        ChannelId:   req.GetChannelId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingReserveInfo fail to GetWeddingReserveInfo. req:%v, err:%v", req, err)
        return resp, err
    }

    resp.CurReserveDate = reserveableInfoResp.GetCurReserveDate()
    resp.CurChannelId = reserveableInfoResp.GetCurChannelId()
    resp.MaxReserveNum = 1
    channelIds := make([]uint32, 0)
    for _, item := range reserveableInfoResp.GetChannelInfo() {
        channelIds = append(channelIds, item.ChannelId)
    }
    channelSimpleInfoResp, err := mgr.channelSvr.BatchGetChannelSimpleInfo(ctx, 0, channelIds)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingReserveInfo fail to BatchGetChannelSimpleInfo. channelIds:%v, err:%v", channelIds, err)
        return resp, err
    }
    bindIdList := make([]uint32, 0, len(channelSimpleInfoResp))
    for _, item := range channelSimpleInfoResp {
        bindIdList = append(bindIdList, item.GetBindId())
    }
    guildResp, err := mgr.guildCli.GetGuildBat(ctx, 0, &guildpb.GetGuildBatReq{
        GuildIdList:   bindIdList,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingReserveInfo fail to GetGuildBat. bindIdList:%v, err:%v", bindIdList, err)
        return resp, err
    }
    guildInfoMap := make(map[uint32]*guildpb.GuildResp)
    for _, item := range guildResp.GetGuildList() {
        guildInfoMap[item.GetGuildId()] = item
    }

    uidList := make([]uint32, 0)
    for _, cinfo := range reserveableInfoResp.ChannelInfo {
        uidList = append(uidList, cinfo.ManagerUid)
    }
    for _, item := range reserveableInfoResp.ReserveTimeInfo {
        uidList = append(uidList, item.GetGroomUid(), item.GetBrideUid())
    }
    userprofileResp, err := mgr.userProfileCli.BatchGetUserProfileV2(ctx, uidList, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingReserveInfo fail to BatchGetUserProfileV2. uidList:%v, err:%v", uidList, err)
        return nil, err
    }

    for _, cinfo := range reserveableInfoResp.GetChannelInfo() {
        cid := cinfo.GetChannelId()
        guildId := channelSimpleInfoResp[cid].GetBindId()
        if guildInfoMap[guildId].GetShortId() > 0 {
            guildId = guildInfoMap[guildId].GetShortId()
        }
        resp.ReserveableChannelList = append(resp.ReserveableChannelList, &channel_wedding_http.WeddingChannelInfo{
            ChannelId:   cid,
            ChannelName: channelSimpleInfoResp[cid].GetName(),
            GuildId:     guildId,
            ManagerTtid: userprofileResp[cinfo.ManagerUid].GetAccount(),
            ManagerNickname: userprofileResp[cinfo.ManagerUid].GetNickname(),
        })
    }

    for _, item := range reserveableInfoResp.ReserveTimeInfo {
        resp.ReserveTimeInfoList = append(resp.ReserveTimeInfoList, &channel_wedding_http.ReserveTimeInfo{
            ReserveTimeSection: item.GetReserveTimeSection(),
            IsFullyReserved:    item.GetIsFullyReserved(),
            Groom:              &channel_wedding_http.UserProfile{
                Uid:          item.GetGroomUid(),
                Account:      userprofileResp[item.GetGroomUid()].GetAccount(),
                Nickname:     userprofileResp[item.GetGroomUid()].GetNickname(),
            },
            Bride:              &channel_wedding_http.UserProfile{
                Uid:          item.GetBrideUid(),
                Account:      userprofileResp[item.GetBrideUid()].GetAccount(),
                Nickname:     userprofileResp[item.GetBrideUid()].GetNickname(),
            },
            IsHot:              item.GetIsHot(),
        })
    }

    resp.MinReserveDate = reserveableInfoResp.GetMinReserveDate()
    resp.MaxReserveDate = reserveableInfoResp.GetMaxReserveDate()
    resp.HotLabel = &channel_wedding_http.LabelInfo{
        LabelName: "热门婚礼·标识",
        LabelIcon: "https://obs-cdn.52tt.com/tt/fe-moss/tt-server/20250427133800_30498823.png",
        LabelDesc: "房间排序靠前",
    }

    // 填充礼物信息
    themeConf, err := mgr.channelWeddingConfCli.GetThemeCfg(ctx, &channel_wedding_conf.GetThemeCfgReq{
        ThemeId: req.GetThemeId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingReserveInfo fail to GetThemeCfg. err:%v", err)
        return resp, err
    }
    if themeConf.GetThemeCfg().GetPriceInfo().GetPriceType() == uint32(userPresentpb.PresentPriceType_PRESENT_PRICE_TBEAN) {
        giftIdList := make([]uint32, 0)
        for _, item := range themeConf.GetThemeCfg().GetPriceInfo().GetNormalTimePrice() {
            giftIdList = append(giftIdList, item.GetGiftId())
        }
        for _, item := range themeConf.GetThemeCfg().GetPriceInfo().GetHotTimePrice() {
            giftIdList = append(giftIdList, item.GetGiftId())
        }
        presentResp, err := mgr.userPresentCli.GetPresentConfigByIdList(ctx, 0, giftIdList, uint32(userPresentpb.ConfigListTypeBitMap_CONFIG_NOT_DELETED))
        if err != nil {
            log.ErrorWithCtx(ctx, "GetWeddingReserveInfo fail to GetPresentConfigByIdList. giftIdList:%v, err:%v", giftIdList, err)
            return resp, err
        }
        log.DebugWithCtx(ctx, "GetWeddingReserveInfo GetPresentConfigByIdList giftIdList:%v, presentResp:%+v", giftIdList, presentResp)
        giftMap := make(map[uint32]*userPresentpb.StPresentItemConfig)
        for _, item := range presentResp.GetItemList() {
            giftMap[item.GetItemId()] = item
        }
        log.DebugWithCtx(ctx, "GetWeddingReserveInfo giftMap:%+v", giftMap)
        for _, item := range themeConf.GetThemeCfg().GetPriceInfo().GetNormalTimePrice() {
            resp.NormalTimeGiftList = append(resp.NormalTimeGiftList, &channel_wedding_http.GiftInfo{
                GiftId:   item.GiftId,
                GiftName: giftMap[item.GetGiftId()].GetName(),
                GiftIcon: giftMap[item.GetGiftId()].GetIconUrl(),
                Worth:    giftMap[item.GetGiftId()].GetPrice(),
                BuyPrice: giftMap[item.GetGiftId()].GetPrice(),
            })
        }
        for _, item := range themeConf.GetThemeCfg().GetPriceInfo().GetHotTimePrice() {
            resp.HotTimeGiftList = append(resp.HotTimeGiftList, &channel_wedding_http.GiftInfo{
                GiftId:   item.GiftId,
                GiftName: giftMap[item.GetGiftId()].GetName(),
                GiftIcon: giftMap[item.GetGiftId()].GetIconUrl(),
                Worth:    giftMap[item.GetGiftId()].GetPrice(),
                BuyPrice: giftMap[item.GetGiftId()].GetPrice(),
            })
        }
    } else {
        resp.NormalTimeGiftList = append(resp.NormalTimeGiftList, &channel_wedding_http.GiftInfo{
            BuyPrice: themeConf.GetThemeCfg().GetPriceInfo().GetPrice(),
        })
    }


    return resp, nil
}

func (mgr *ChannelWeddingMgr) SaveWeddingReserve(ctx context.Context, req *channel_wedding_http.SaveWeddingReserveRequest) (*channel_wedding_http.SaveWeddingReserveResponse, error) {
    resp := &channel_wedding_http.SaveWeddingReserveResponse{}

    _, err := mgr.channelWeddingPlanSvr.SaveWeddingReserveInfo(ctx, &channel_wedding_plan.SaveWeddingReserveRequest{
        ChannelId:     req.GetChannelId(),
        ReserveDate:   req.GetReserveDate(),
        ReserveTime:   req.GetReserveTime(),
        WeddingPlanId: req.GetWeddingPlanId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SaveWeddingReserve fail to SaveWeddingReserve. req:%v, err:%v", req, err)
        return resp, err
    }

    return resp, nil
}

func (mgr *ChannelWeddingMgr) ChangeWeddingReserve(ctx context.Context, req *channel_wedding_http.ChangeWeddingReserveRequest) (*channel_wedding_http.ChangeWeddingReserveResponse, error) {
    resp := &channel_wedding_http.ChangeWeddingReserveResponse{}

    _, err := mgr.channelWeddingPlanSvr.ChangeWeddingReserveInfo(ctx, &channel_wedding_plan.ChangeWeddingReserveRequest{
        WeddingPlanId: req.GetWeddingPlanId(),
        ReserveDate:   req.GetReserveDate(),
        ChannelId:     req.GetChannelId(),
        ReserveTime:   req.GetReserveTime(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "ChangeWeddingReserve fail to ChangeWeddingReserve. req:%v, err:%v", req, err)
        return resp, err
    }

    return resp, nil
}

func (mgr *ChannelWeddingMgr) GetWeddingPlanBaseInfo(ctx context.Context, req *channel_wedding_http.GetWeddingPlanBaseInfoRequest) (*channel_wedding_http.GetWeddingPlanBaseInfoResponse, error) {
    resp := &channel_wedding_http.GetWeddingPlanBaseInfoResponse{}
    svrResp, err := mgr.channelWeddingPlanSvr.GetWeddingPlanBaseInfo(ctx, &channel_wedding_plan.GetWeddingPlanBaseInfoRequest{})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingPlanBaseInfo fail, err: %v", err)
        return resp, nil
    }

    resp.HasMate = svrResp.GetHasMate()
    resp.IsPay = svrResp.GetIsPay()
    resp.IsReserve = svrResp.GetIsReserve()

    return resp, nil
}

func (mgr *ChannelWeddingMgr) GetInnerChannelWeddingReservedList(ctx context.Context, req *channel_wedding_http.GetInnerChannelWeddingReservedListRequest) (
    *channel_wedding_http.GetInnerChannelWeddingReservedListResponse, error) {
    resp := &channel_wedding_http.GetInnerChannelWeddingReservedListResponse{}

    innerChannelWeddingReservedListResp, err := mgr.channelWeddingPlanSvr.GetReservedWedding(ctx, &channel_wedding_plan.GetReservedWeddingRequest{
        ChannelId: []uint32{req.GetChannelId()},
        PageNum:   req.GetPageNum(),
        PageSize:  req.GetPageSize(),
        StartTime: uint32(time.Now().Unix()),
        EndTime:   uint32(time.Now().Add(time.Hour * 24 * 365).Unix()),
        DataType:  uint32(channel_wedding_plan.WeddingReserveDataType_WEDDING_RESERVE_DATA_TYPE_NOT_START),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetInnerChannelWeddingReservedList fail to GetReservedWedding. req:%v, err:%v", req, err)
        return resp, err
    }

    uidList := make([]uint32, 0, 2*len(innerChannelWeddingReservedListResp.GetReservedWeddingList()))
    for _, item := range innerChannelWeddingReservedListResp.GetReservedWeddingList() {
        uidList = append(uidList, item.GetGroomUid(), item.GetBrideUid())
    }

    userProfileMap, err := mgr.userProfileCli.BatchGetUserProfile(ctx, uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetInnerChannelWeddingReservedList fail to BatchGetUserProfile. uidList:%v, err:%v", uidList, err)
        return resp, nil
    }

    for _, item := range innerChannelWeddingReservedListResp.GetReservedWeddingList() {
        resp.ReservedList = append(resp.ReservedList, &channel_wedding_http.InnerChannelWeddingReservedInfo{
            Groom: &channel_wedding_http.UserProfile{
                Uid:          userProfileMap[item.GetGroomUid()].GetUid(),
                Account:      userProfileMap[item.GetGroomUid()].GetAccount(),
                Nickname:     userProfileMap[item.GetGroomUid()].GetNickname(),
                AccountAlias: userProfileMap[item.GetGroomUid()].GetAccountAlias(),
                Sex:          userProfileMap[item.GetGroomUid()].GetSex(),
                HeadImgMd5:   userProfileMap[item.GetGroomUid()].GetHeadImgMd5(),
                HeadDyImgMd5: userProfileMap[item.GetGroomUid()].GetHeadDyImgMd5(),
            },
            Bride: &channel_wedding_http.UserProfile{
                Uid:          userProfileMap[item.GetBrideUid()].GetUid(),
                Account:      userProfileMap[item.GetBrideUid()].GetAccount(),
                Nickname:     userProfileMap[item.GetBrideUid()].GetNickname(),
                AccountAlias: userProfileMap[item.GetBrideUid()].GetAccountAlias(),
                Sex:          userProfileMap[item.GetBrideUid()].GetSex(),
                HeadImgMd5:   userProfileMap[item.GetBrideUid()].GetHeadImgMd5(),
                HeadDyImgMd5: userProfileMap[item.GetBrideUid()].GetHeadDyImgMd5(),
            },
            ThemeName:        item.GetThemeName(),
            ReserveStartTime: item.GetReserveStartTime(),
            ReserveEndTime:   item.GetReserveEndTime(),
        })
    }

    return resp, nil
}

func (mgr *ChannelWeddingMgr) GetPlaymateList(ctx context.Context, req *channel_wedding_http.GetPlaymateListRequest) (*channel_wedding_http.GetPlaymateListResponse, error) {
    resp := &channel_wedding_http.GetPlaymateListResponse{}
    st := time.Now()

    if req.GetPageSize() == 0 {
        req.PageSize = 10
    }

    serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
    if !ok {
        return resp, fmt.Errorf("GetPlaymateList fail to get service info")
    }
    log.InfoWithCtx(ctx, "GetPlaymateList, uid: %d, start", serviceInfo.UserID)

    if len(req.GetAccount()) > 0 {
        searchUserId, _, err := mgr.accountCli.GetUidByName(ctx, req.GetAccount())
        if err != nil {
            log.ErrorWithCtx(ctx, "GetProposeList failed to GetUidByName err:%v", err)
            return resp, nil
        }

        if searchUserId == 0 {
            return resp, nil
        }

        targetUser, err := mgr.userProfileCli.GetUserProfile(ctx, searchUserId)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetProposeList failed to GetUserProfile err:%v", err)
            return resp, err
        }

        fellowPointResp, serr := mgr.fellowSvr.GetFellowPoint(ctx, &fellow_svr.GetFellowPointReq{
            Uid:       serviceInfo.UserID,
            FellowUid: searchUserId,
        })
        if serr != nil {
            log.ErrorWithCtx(ctx, "GetProposeList failed to GetFellowPoint err:%v", serr)
            return resp, serr
        }

        resp.PlaymateList = append(resp.PlaymateList, &channel_wedding_http.WeddingPlaymateInfo{
            UserProfile: &channel_wedding_http.UserProfile{
                Uid:          targetUser.GetUid(),
                Account:      targetUser.GetAccount(),
                Nickname:     targetUser.GetNickname(),
                AccountAlias: targetUser.GetAccountAlias(),
                Sex:          targetUser.GetSex(),
                HeadImgMd5:   targetUser.GetHeadImgMd5(),
                HeadDyImgMd5: targetUser.GetHeadDyImgMd5(),
            },
            FellowVal: fellowPointResp.GetFellowPoint(),
        })

        return resp, nil
    }

    topNFellowResp, err := mgr.fellowSvr.GetTopNFellow(ctx, &fellow_svr.GetTopNFellowReq{
        Uid: serviceInfo.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetPlaymateList fail to GetTopNFellow. req:%v, err:%v", req, err)
        return resp, err
    }
    fellowList := topNFellowResp.GetTopNFellowList()
    // 过滤新郎新娘
    excludeMap := make(map[uint32]struct{})
    if req.GetWeddingPlanId() != 0 {
        simpleWeddingPlanInfo, err := mgr.channelWeddingPlanSvr.GetSimpleWeddingPlanInfo(ctx, &channel_wedding_plan.GetSimpleWeddingPlanInfoRequest{
            WeddingPlanId: req.GetWeddingPlanId(),
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "GetPlaymateList fail to GetSimpleWeddingPlanInfo. req:%v, err:%v", req, err)
            return resp, err
        }
        excludeMap[simpleWeddingPlanInfo.GetGroomUid()] = struct{}{}
        excludeMap[simpleWeddingPlanInfo.GetBrideUid()] = struct{}{}
    }
    log.InfoWithCtx(ctx, "GetPlaymateList.GetTopNFellow, uid: %d, cost: %dms", serviceInfo.UserID, time.Since(st).Milliseconds())

    // 过滤求婚对象
    marriageInfo, err := mgr.channelWeddingPlanSvr.GetMarriageStatus(ctx, &channel_wedding_plan.GetMarriageStatusRequest{
        Uid: serviceInfo.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetPlaymateList fail to BatchGetMarriageInfo. req:%v, err:%v", req, err)
        return resp, err
    }
    if marriageInfo.GetRelationInfo().GetPartnerUid() > 0 {
        excludeMap[marriageInfo.GetRelationInfo().GetPartnerUid()] = struct{}{}
    }
    log.InfoWithCtx(ctx, "GetPlaymateList.GetMarriageStatus, uid: %d, cost: %dms", serviceInfo.UserID, time.Since(st).Milliseconds())

    // 过滤神秘人
    uidList := make([]uint32, 0, len(fellowList))
    for _, item := range fellowList {
        uidList = append(uidList, item.FellowUid)
    }
    youknowwhoResp, err := mgr.youKnowWhoClient.BatchGetUKWInfo(ctx, uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetPlaymateList fail to BatchGetUKWInfo. uidList:%v, err:%v", uidList, err)
        return resp, err
    }
    for _, item := range youknowwhoResp {
        if item.GetUkwPermissionInfo().GetSwitch() == youknowwhopb.UKWSwitchType_UKW_SWITCH_ON {
            excludeMap[item.GetUid()] = struct{}{}
        }
    }
    log.InfoWithCtx(ctx, "GetPlaymateList.BatchGetUKWInfo, uid: %d, cost: %dms", serviceInfo.UserID, time.Since(st).Milliseconds())

    tmpFellowList := make([]*fellow_svr.TopNFellowInfo, 0, len(fellowList))
    for _, item := range fellowList {
        if item.FellowVal == 0 {
            continue
        }
        if _, ok := excludeMap[item.FellowUid]; ok {
            continue
        }
        tmpFellowList = append(tmpFellowList, item)
    }
    fellowList = tmpFellowList

    sIdx := int(req.GetPageNum() * req.GetPageSize())
    eIdx := sIdx + int(req.GetPageSize())
    if sIdx >= len(fellowList) {
        return resp, nil
    }
    if eIdx > len(fellowList) {
        eIdx = len(fellowList)
    }

    rsPage := fellowList[sIdx:eIdx]
    rsUid := make([]uint32, 0, len(rsPage))
    for _, item := range rsPage {
        rsUid = append(rsUid, item.FellowUid)
    }

    userProfileMap, err := mgr.userProfileCli.BatchGetUserProfile(ctx, rsUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetPlaymateList fail to BatchGetUserProfile. uidList:%v, err:%v", rsUid, err)
        return resp, nil
    }
    log.InfoWithCtx(ctx, "GetPlaymateList.BatchGetUserProfile, uid: %d, cost: %dms", serviceInfo.UserID, time.Since(st).Milliseconds())

    for _, item := range rsPage {
        resp.PlaymateList = append(resp.PlaymateList, &channel_wedding_http.WeddingPlaymateInfo{
            UserProfile: &channel_wedding_http.UserProfile{
                Uid:          item.FellowUid,
                Account:      userProfileMap[item.FellowUid].GetAccount(),
                Nickname:     userProfileMap[item.FellowUid].GetNickname(),
                AccountAlias: userProfileMap[item.FellowUid].GetAccountAlias(),
                Sex:          userProfileMap[item.FellowUid].GetSex(),
                HeadImgMd5:   userProfileMap[item.FellowUid].GetHeadImgMd5(),
                HeadDyImgMd5: userProfileMap[item.FellowUid].GetHeadDyImgMd5(),
            },
            FellowVal: item.FellowVal,
        })
    }

    return resp, nil
}

func (mgr *ChannelWeddingMgr) GetGroomsmanAndBridesmaidInfo(ctx context.Context, req *channel_wedding_http.GetGroomsmanAndBridesmaidInfoRequest) (
    *channel_wedding_http.GetGroomsmanAndBridesmaidInfoResponse, error) {
    resp := &channel_wedding_http.GetGroomsmanAndBridesmaidInfoResponse{}
    serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
    if !ok {
        return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    // 获取亲友团信息
    gbResp, err := mgr.channelWeddingPlanSvr.GetGroomsmanAndBridesmaidInfo(ctx, &channel_wedding_plan.GetGroomsmanAndBridesmaidInfoRequest{
        WeddingPlanId: req.GetWeddingPlanId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetGroomsmanAndBridesmaidInfo fail to GetGroomsmanAndBridesmaidInfo. req:%v, err:%v", req, err)
        return resp, err
    }

    // 用户信息,挚友值数据准备
    fellowSimpleInfoMap, userprofileMap, err := mgr.WeddingGuestRequirePrepare(ctx, serviceInfo.UserID, [][]*channel_wedding_plan.WeddingGuestInfo{
        gbResp.GetInvitedList(),
        gbResp.GetAgreedList(),
        gbResp.GetRefusedList(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetGroomsmanAndBridesmaidInfo fail to WeddingGuestRequirePrepare. err:%v", err)
        return resp, err
    }

    // resp转换
    for _, item := range gbResp.GetGroomsmanList() {
        resp.GroomsmanList = append(resp.GroomsmanList, &channel_wedding_http.WeddingGuestInfo{
            UserProfile: userprofileConvert(userprofileMap[item.Uid]),
            InviteUid:   item.GetInviteUid(),
            FellowVal:   fellowSimpleInfoMap[getFellowPair(serviceInfo.UserID, item.GetUid())].GetPoint(),
        })
    }
    for _, item := range gbResp.GetBridesmaidList() {
        resp.BridesmaidList = append(resp.BridesmaidList, &channel_wedding_http.WeddingGuestInfo{
            UserProfile: userprofileConvert(userprofileMap[item.Uid]),
            InviteUid:   item.GetInviteUid(),
            FellowVal:   fellowSimpleInfoMap[getFellowPair(serviceInfo.UserID, item.GetUid())].GetPoint(),
        })
    }
    for _, item := range gbResp.GetAgreedList() {
        resp.AgreedList = append(resp.AgreedList, &channel_wedding_http.WeddingGuestInfo{
            UserProfile: userprofileConvert(userprofileMap[item.Uid]),
            InviteUid:   item.GetInviteUid(),
            FellowVal:   fellowSimpleInfoMap[getFellowPair(serviceInfo.UserID, item.GetUid())].GetPoint(),
        })
    }
    for _, item := range gbResp.GetInvitedList() {
        resp.InvitedList = append(resp.InvitedList, &channel_wedding_http.WeddingGuestInfo{
            UserProfile: userprofileConvert(userprofileMap[item.Uid]),
            InviteUid:   item.GetInviteUid(),
            FellowVal:   fellowSimpleInfoMap[getFellowPair(serviceInfo.UserID, item.GetUid())].GetPoint(),
        })
    }
    for _, item := range gbResp.GetRefusedList() {
        resp.RefusedList = append(resp.RefusedList, &channel_wedding_http.WeddingGuestInfo{
            UserProfile: userprofileConvert(userprofileMap[item.Uid]),
            InviteUid:   item.GetInviteUid(),
            FellowVal:   fellowSimpleInfoMap[getFellowPair(serviceInfo.UserID, item.GetUid())].GetPoint(),
        })
    }

    return resp, nil
}

func (mgr *ChannelWeddingMgr) GetWeddingFriendInfo(ctx context.Context, req *channel_wedding_http.GetWeddingFriendInfoRequest) (*channel_wedding_http.GetWeddingFriendInfoResponse, error) {
    resp := &channel_wedding_http.GetWeddingFriendInfoResponse{}
    serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
    if !ok {
        return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    // 获取亲友团及邀请信息
    friendsResp, err := mgr.channelWeddingPlanSvr.GetWeddingFriendInfo(ctx, &channel_wedding_plan.GetWeddingFriendInfoRequest{
        WeddingPlanId: req.GetWeddingPlanId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingFriendInfo fail to GetWeddingFriendInfo. req:%v, err:%v", req, err)
        return resp, err
    }

    // 用户信息,挚友值数据准备
    fellowSimpleInfoMap, userprofileMap, err := mgr.WeddingGuestRequirePrepare(ctx, serviceInfo.UserID, [][]*channel_wedding_plan.WeddingGuestInfo{
        friendsResp.GetInvitedList(),
        friendsResp.GetAgreedList(),
        friendsResp.GetRefusedList(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingFriendInfo fail to WeddingGuestRequirePrepare. err:%v", err)
        return resp, err
    }

    // resp转换
    for _, item := range friendsResp.GetFriendList() {
        resp.FriendList = append(resp.FriendList, &channel_wedding_http.WeddingGuestInfo{
            UserProfile: userprofileConvert(userprofileMap[item.Uid]),
            InviteUid:   item.GetInviteUid(),
            FellowVal:   fellowSimpleInfoMap[getFellowPair(serviceInfo.UserID, item.GetUid())].GetPoint(),
        })
    }
    for _, item := range friendsResp.GetAgreedList() {
        resp.InvitedList = append(resp.InvitedList, &channel_wedding_http.WeddingGuestInfo{
            UserProfile: userprofileConvert(userprofileMap[item.Uid]),
            InviteUid:   item.GetInviteUid(),
            FellowVal:   fellowSimpleInfoMap[getFellowPair(serviceInfo.UserID, item.GetUid())].GetPoint(),
        })
    }
    for _, item := range friendsResp.GetAgreedList() {
        resp.AgreedList = append(resp.AgreedList, &channel_wedding_http.WeddingGuestInfo{
            UserProfile: userprofileConvert(userprofileMap[item.Uid]),
            InviteUid:   item.GetInviteUid(),
            FellowVal:   fellowSimpleInfoMap[getFellowPair(serviceInfo.UserID, item.GetUid())].GetPoint(),
        })
    }
    for _, item := range friendsResp.GetRefusedList() {
        resp.RefusedList = append(resp.RefusedList, &channel_wedding_http.WeddingGuestInfo{
            UserProfile: userprofileConvert(userprofileMap[item.Uid]),
            InviteUid:   item.GetInviteUid(),
            FellowVal:   fellowSimpleInfoMap[getFellowPair(serviceInfo.UserID, item.GetUid())].GetPoint(),
        })
    }

    resp.MaxFriendNum = friendsResp.GetMaxFriendNum()
    return resp, nil
}

func (mgr *ChannelWeddingMgr) WeddingGuestRequirePrepare(ctx context.Context, uid uint32, weddingGuestList [][]*channel_wedding_plan.WeddingGuestInfo) (
    simpleFellowInfoMap map[string]*fellow_svr.SimpleFellowInfo,
    userprofileMap map[uint32]*app.UserProfile,
    err error) {
    uidList := make([]uint32, 0, 32)
    pairList := make([]*fellow_svr.FellowPair, 0, 32)
    for _, part := range weddingGuestList {
        for _, item := range part {
            pairList = append(pairList, &fellow_svr.FellowPair{
                UidA: int64(uid),
                UidB: int64(item.GetUid()),
            })
            uidList = append(uidList, item.GetUid())
        }
    }
    fellowSimpleInfoResp, err := mgr.fellowSvr.BatchGetFellowSimpleInfoByPairList(ctx, &fellow_svr.BatchGetFellowSimpleInfoByPairListReq{
        PairList: pairList,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetGroomsmanAndBridesmaidInfo fail to BatchGetFellowSimpleInfoByPairList. pairList:%v, err:%v", pairList, err)
        return
    }
    simpleFellowInfoMap = make(map[string]*fellow_svr.SimpleFellowInfo)
    for _, item := range fellowSimpleInfoResp.GetFellowList() {
        simpleFellowInfoMap[getFellowPair(item.GetUidA(), item.GetUidB())] = item
    }
    userprofileMap, err = mgr.userProfileCli.BatchGetUserProfileV2(ctx, uidList, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetGroomsmanAndBridesmaidInfo fail to BatchGetUserProfile. uidList:%v, err:%v", uidList, err)
        return
    }
    return
}

func getFellowPair(uidA, uidB uint32) string {
    key := fmt.Sprintf("%d_%d", uidA, uidB)
    if uidB < uidA {
        key = fmt.Sprintf("%d_%d", uidB, uidA)
    }
    return key
}

func (mgr *ChannelWeddingMgr) InviteWeddingGuest(ctx context.Context, req *channel_wedding_http.InviteWeddingGuestRequest) (*channel_wedding_http.InviteWeddingGuestResponse, error) {
    resp := &channel_wedding_http.InviteWeddingGuestResponse{}
    _, err := mgr.channelWeddingPlanSvr.InviteWeddingGuest(ctx, &channel_wedding_plan.InviteWeddingGuestRequest{
        WeddingPlanId:    req.GetWeddingPlanId(),
        WeddingGuestType: req.GetWeddingGuestType(),
        Uid:              req.GetTargetUid(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "InviteWeddingGuest fail to InviteWeddingGuest. req:%v, err:%v", req, err)
        return resp, err
    }
    return resp, nil
}

func userprofileConvert(userProfile *app.UserProfile) *channel_wedding_http.UserProfile {
    return &channel_wedding_http.UserProfile{
        Uid:          userProfile.GetUid(),
        Account:      userProfile.GetAccount(),
        Nickname:     userProfile.GetNickname(),
        AccountAlias: userProfile.GetAccountAlias(),
        Sex:          userProfile.GetSex(),
        HeadImgMd5:   userProfile.GetHeadImgMd5(),
        HeadDyImgMd5: userProfile.GetHeadDyImgMd5(),
    }
}

func (mgr *ChannelWeddingMgr) DelWeddingGuest(ctx context.Context, req *channel_wedding_http.DelWeddingGuestRequest) (*channel_wedding_http.DelWeddingGuestResponse, error) {
    resp := &channel_wedding_http.DelWeddingGuestResponse{}
    _, err := mgr.channelWeddingPlanSvr.DelWeddingGuest(ctx, &channel_wedding_plan.DelWeddingGuestRequest{
        WeddingPlanId:    req.GetWeddingPlanId(),
        WeddingGuestType: req.GetWeddingGuestType(),
        Uid:              req.GetTargetUid(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "DelWeddingGuest fail to DelWeddingGuest. req:%v, err:%v", req, err)
        return resp, err
    }
    return resp, nil
}

func (mgr *ChannelWeddingMgr) ConsultWeddingReserve(ctx context.Context, req *channel_wedding_http.ConsultWeddingReserveRequest) (*channel_wedding_http.ConsultWeddingReserveResponse, error) {
    resp := &channel_wedding_http.ConsultWeddingReserveResponse{}
    uid, _,  err := mgr.accountCli.GetUidByName(ctx, req.GetManagerTtid())
    if err != nil {
        log.ErrorWithCtx(ctx, "ConsultWeddingReserve, req: %+v, err: %v", req, err)
        return resp, err
    }
    _, sErr := mgr.channelWeddingPlanSvr.ConsultWeddingReserve(ctx, &channel_wedding_plan.ConsultWeddingReserveRequest{
        ChannelId:   req.GetChannelId(),
        ReserveDate: req.GetReserveDate(),
        ReserveTime: req.GetReserveTime(),
        ThemeId:     req.GetThemeId(),
        ManagerUid:  uid,

    })
    if sErr != nil {
        log.ErrorWithCtx(ctx, "ConsultWeddingReserve fail to ConsultWeddingReserve. req:%v, err:%v", req, sErr)
        return resp, sErr
    }

    return resp, nil
}

func (mgr *ChannelWeddingMgr) GetChannelReservedInfo(ctx context.Context, req *channel_wedding_http.GetChannelReservedInfoRequest) (*channel_wedding_http.GetChannelReservedInfoResponse, error) {
    resp := &channel_wedding_http.GetChannelReservedInfoResponse{}
    serviceInfo, ok := grpcProtocol.ServiceInfoFromContext(ctx)
    if !ok {
        return resp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }

    channelReservedInfoResp, err := mgr.channelWeddingPlanSvr.GetChannelReservedInfo(ctx, &channel_wedding_plan.GetChannelReservedInfoRequest{
        ReserveDate: req.GetReserveDate(),
        ChannelId:   req.GetChannelId(),
        ThemeType:   req.GetThemeType(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelReservedInfo, req: %+v, err:%v", req, err)
        return resp, err
    }

    // 拉去用户信息
    uidList := make([]uint32, 0, len(channelReservedInfoResp.GetReserveTimeInfoList())*2)
    for _, item := range channelReservedInfoResp.GetReserveTimeInfoList() {
        uidList = append(uidList, item.GetGroomUid(), item.GetBrideUid())
    }
    userprofile, err := mgr.userProfileCli.BatchGetUserProfileV2(ctx, uidList, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelReservedInfo fail. uidList:%v, err:%v", uidList, err)
        return resp, err
    }

    // 房间信息
    cInfoResp, err := mgr.channelSvr.GetChannelSimpleInfo(ctx, 0, req.GetChannelId())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelReservedInfo fail. req:%v, err:%v", req, err)
        return resp, err
    }
    if cInfoResp.GetCreaterUid() == serviceInfo.UserID {
        resp.IsGuild = true
    }
    channelAdminResp, err := mgr.channelSvr.GetChannelAdmin(ctx, 0, req.GetChannelId())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelReservedInfo fail. req:%v, err:%v", req, err)
        return resp, err
    }
    for _, item := range channelAdminResp {
        if item.GetUid() == serviceInfo.UserID {
            resp.IsGuild = true
            break
        }
    }

    // 婚礼主题信息
    allThemeCfg, err := mgr.channelWeddingConfCli.GetThemeCfgList(ctx, &channel_wedding_conf.GetThemeCfgListReq{})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelReservedInfo fail to GetThemeCfgList. err:%v", err)
        return resp, err
    }
    allThemeMap := make(map[uint32]*channel_wedding_conf.ThemeCfg)
    for _, item := range allThemeCfg.GetThemeCfgList() {
            allThemeMap[item.ThemeId] = item
    }

    for _, item := range channelReservedInfoResp.GetReserveTimeInfoList() {
        resp.ReserveTimeInfoList = append(resp.ReserveTimeInfoList, &channel_wedding_http.ChannelReserveTimeInfo{
            ReserveTime: item.GetReserveTime(),
            Groom: &channel_wedding_http.UserProfile{
                Uid:      item.GetGroomUid(),
                Account:  userprofile[item.GetGroomUid()].GetAccount(),
                Nickname: userprofile[item.GetGroomUid()].GetNickname(),
            },
            Bride: &channel_wedding_http.UserProfile{
                Uid:      item.GetBrideUid(),
                Account:  userprofile[item.GetBrideUid()].GetAccount(),
                Nickname: userprofile[item.GetBrideUid()].GetNickname(),
            },
            IsHot:       item.GetIsHot(),
            ThemeName:   allThemeMap[item.ThemeId].GetThemeName(),
        })
    }

    resp.MinReserveDate = channelReservedInfoResp.GetMinReserveDate()
    resp.MaxReserveDate = channelReservedInfoResp.GetMaxReserveDate()

    return resp, nil
}

func (mgr *ChannelWeddingMgr) ArrangeWeddingReserveRequest(ctx context.Context, request *channel_wedding_http.ArrangeWeddingReserveRequest) (*channel_wedding_http.ArrangeWeddingReserveResponse, error) {
    resp := &channel_wedding_http.ArrangeWeddingReserveResponse{}

    _, err := mgr.channelWeddingPlanSvr.ArrangeWeddingReserve(ctx, &channel_wedding_plan.ArrangeWeddingReserveRequest{
        ChannelId:   request.GetChannelId(),
        ReserveDate: request.GetReserveDate(),
        ReserveTime: request.GetReserveTime(),
        IsHot:       request.GetIsHot(),
        GiftId:      request.GetGiftId(),
        SourceMsgId: request.GetSourceMsgId(),
        ThemeId:     request.GetThemeId(),
        TargetUid:   request.GetTargetUid(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "ArrangeWeddingReserveRequest fail to ArrangeWeddingReserve. req:%v, err:%v", request, err)
        return resp, err
    }

    return resp, nil
}

func (mgr *ChannelWeddingMgr) GetWeddingBigScreenInfo(ctx context.Context, req *channel_wedding_http.GetWeddingBigScreenInfoRequest) (*channel_wedding_http.GetWeddingBigScreenInfoResponse, error) {
    resp := &channel_wedding_http.GetWeddingBigScreenInfoResponse{}
    
    bigScreenList, err := mgr.channelWeddingPlanSvr.GetWeddingBigScreen(ctx, &channel_wedding_plan.GetWeddingBigScreenRequest{
        WeddingPlanId: req.GetWeddingPlanId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetWeddingBigScreenInfo fail to GetWeddingBigScreen. req:%v, err:%v", req, err)
        return resp, nil
    }

    for _, item := range bigScreenList.GetBigScreenList() {
        resp.BigScreenList = append(resp.BigScreenList, &channel_wedding_http.BigScreenItem{
            ImgUrl:       item.GetImgUrl(),
            ReviewStatus: uint32(item.GetReviewStatus()),
            UploadByUid:  item.GetUploadByUid(),
        })
    }
    resp.MaxNum = constant.MaxBigScreenNum

    return resp, nil
}

func (mgr *ChannelWeddingMgr) IsShowInfoCard(ctx context.Context, req *channel_wedding_http.IsShowInfoCardRequest) (*channel_wedding_http.IsShowInfoCardResponse, error) {
    resp := &channel_wedding_http.IsShowInfoCardResponse{
        IsShow: true,
    }

    userChannelIdResp, err := mgr.ChannelOlGoCli.GetUserChannelId(ctx, &channelol_go.GetUserChannelIdReq{
        Uid: req.GetTargetUid(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "IsShowInfoCard GetUserChannelId failed req:%v err:%v", req, err)
        return resp, err
    }
    if userChannelIdResp.GetChannelId() == 0 {
        resp.IsShow = false
        return resp, nil
    }
    if userChannelIdResp.GetChannelId() != req.GetChannelId() {
        resp.IsShow = false
        return resp, nil
    }


    //神秘人判断
    ukwInfo, err := mgr.youKnowWhoClient.GetUKWInfo(ctx, req.GetTargetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "IsShowInfoCard GetUKWInfo failed uid:%d err:%v", req.GetTargetUid(), err)
    }

    if ukwInfo.GetUkwPermissionInfo().GetSwitch() == youknowwhopb.UKWSwitchType_UKW_SWITCH_ON {
        log.ErrorWithCtx(ctx, "IsShowInfoCard user is ukw uid:%d info:%v", req.GetTargetUid(), ukwInfo)
        resp.IsShow = false
        return resp, nil
    }

    nobilityResp, err := mgr.NobilityCli.GetNobilityInfo(ctx, req.GetTargetUid(), false)
    if err != nil {
        log.ErrorWithCtx(ctx, "IsShowInfoCard GetNobilityInfo failed uid:%d err:%v", req.GetTargetUid(), err)
    }
    if nobilityResp.GetInvisible() {
        resp.IsShow = false
        //判断是否已现身
        gResp, tmpErr := mgr.GNobilityCli.GetChannelInvisibleFlag(ctx, &gNobilityPb.GetChannelInvisibleFlagReq{
            Uid: req.GetTargetUid(),
            Cid: userChannelIdResp.GetChannelId(),
        })
        if tmpErr != nil {
            log.ErrorWithCtx(ctx, "IsShowInfoCard GetChannelInvisibleFlag failed uid:%d err:%v", req.GetTargetUid(), tmpErr)
        } else if gResp.GetInvisible() {
            log.ErrorWithCtx(ctx, "IsShowInfoCard user is invisible uid:%d info:%v gResp:%v", req.GetTargetUid(), nobilityResp, gResp)
            return resp, nil
        } else if !gResp.GetInvisible() {
            resp.IsShow = true
        }
    }

    return resp, nil
}

