package server

import (
	"context"
	channel_dating_game_record "golang.52tt.com/protocol/services/channel-dating-game-record"
	fellow_level_award "golang.52tt.com/protocol/services/fellow-level-award"
	"golang.52tt.com/services/tt-rev/fellow-svr/internal/utils"
	context0 "golang.org/x/net/context"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	accountClient "golang.52tt.com/clients/account"
	"golang.52tt.com/clients/banuser"
	"golang.52tt.com/clients/channel"
	channelcpgame "golang.52tt.com/clients/channel-cp-game"
	channelMic "golang.52tt.com/clients/channelmic"
	"golang.52tt.com/clients/channelol"
	"golang.52tt.com/clients/currency"
	"golang.52tt.com/clients/exp"
	missionTL "golang.52tt.com/clients/missiontimeline"
	present_middleware "golang.52tt.com/clients/present-middleware"
	publicnotice "golang.52tt.com/clients/public-notice"
	"golang.52tt.com/clients/push-notification/v2"
	reconcilePresent "golang.52tt.com/clients/reconcile-v2-svr/reconcile-present"
	"golang.52tt.com/clients/sendim"
	"golang.52tt.com/clients/seqgen/v2"
	"golang.52tt.com/clients/timeline"
	friendshipClient "golang.52tt.com/clients/ugc/friendship"
	unifiedPay "golang.52tt.com/clients/unified_pay"
	userBlackListService "golang.52tt.com/clients/user-black-list"
	userprofileapi "golang.52tt.com/clients/user-profile-api"
	userPresentClient "golang.52tt.com/clients/userpresent"
	youknowwho "golang.52tt.com/clients/you-know-who"
	yswfukwdelay "golang.52tt.com/clients/yswf-ukw-delay"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/mapreduce"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/services/fellow-svr"
	"golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/tt-rev/fellow-svr/internal/conf"
	"golang.52tt.com/services/tt-rev/fellow-svr/internal/event"
	"golang.52tt.com/services/tt-rev/fellow-svr/internal/manger"
	"golang.52tt.com/services/tt-rev/fellow-svr/internal/store"
)

const MultiBoundDefault = 3
const RoomFellowListCount = 3

type Server struct {
	sc                     *conf.StartConfig
	bc                     *conf.BusinessConfManager
	store                  *store.Store
	accountCli             *accountClient.Client
	friendshipClient       *friendshipClient.Client
	presentCli             *userPresentClient.Client
	sendImCli              *sendim.Client
	seqgenCli              *seqgen.Client
	timelineCli            *Timeline.Client
	payCli                 *unifiedPay.Client
	currencyCli            *currency.Client
	presentMiddleCli       *present_middleware.Client
	pushCli                *PushNotification.Client
	blacklistCli           *userBlackListService.Client
	banuserClient          *banuser.Client
	expCli                 *exp.Client
	missionHelper          *missionHelper
	kafkaSub               *event.KafkaEventSub
	shutDown               chan interface{}
	mgr                    *manger.Manager
	channelCPCli           *channelcpgame.Client
	channelOlCli           *channelol.Client
	fellowPresentConfigMap map[uint32]*userpresent.StPresentItemConfig // 信物礼物属性的本地缓存
	channelMicCli          *channelMic.Client
	channelCli             *channel.Client
	ukwCli                 *youknowwho.Client
	userProfileApiCli      *userprofileapi.DisplayClient
	yswfUkwDelayCli        yswfukwdelay.IClient
	publicNoticeCli        publicnotice.IClient
	reconcilePresentCli    reconcilePresent.IClient
	//dcron                  *dcron.Dcron
	channelDatingGameRecordCli *channel_dating_game_record.Client
	levelAwardCli              *fellow_level_award.Client

	timerD *timer.Timer
}

func (s *Server) BatchGetFellowSimpleInfoByPairList(c context0.Context, req *pb.BatchGetFellowSimpleInfoByPairListReq) (*pb.BatchGetFellowSimpleInfoByPairListResp, error) {
	out := &pb.BatchGetFellowSimpleInfoByPairListResp{}
	defer func() {
		log.DebugWithCtx(c, "BatchGetFellowSimpleInfoByPairList req:%+v, out:%+v", req, out)
	}()
	list, err := s.mgr.BatchGetBindInfoOfPairList(c, req.GetPairList())
	if err != nil {
		return out, err
	}
	for _, item := range list {
		var bindName string
		if item.BindType != 0 {
			bindName = s.bc.GetUnbindFellowMapByType(item.FellowType)
		}
		out.FellowList = append(out.FellowList, &pb.SimpleFellowInfo{
			UidA:       item.Uid,
			UidB:       item.ToUid,
			Level:      s.bc.GetLevelByPoint(item.Point),
			FellowName: bindName,
			Point:      item.Point,
			DayCnt:     utils.GetSubDay(time.Now(), time.Unix(item.CreateTime, 0)),
		})

	}
	return out, nil
}

func (s *Server) TestSetFellowLevel(ctx context.Context, req *pb.TestSetFellowLevelReq) (*pb.TestSetFellowLevelResp, error) {
	point, _ := s.bc.GetCurrentAndNextLevPoint(req.GetLevel())
	s.store.SetFellowPoint(req.GetUid(), req.GetToUid(), point)
	s.store.UpgradeFellow(req.GetUid(), req.GetToUid(), req.GetLevel())
	s.store.DelCache(ctx, req.GetUid(), req.GetToUid())
	s.store.DelFellowBindListCache(ctx, req.GetToUid())
	s.store.DelFellowBindListCache(ctx, req.GetUid())
	log.InfoWithCtx(ctx, "TestSetFellowLevel fellow req:%+v, level:%d, point:%d", req, req.GetLevel(), point)
	return &pb.TestSetFellowLevelResp{}, nil
}

func (s *Server) TestUpgradeImMsg(ctx context.Context, req *pb.TestUpgradeImMsgReq) (*pb.TestUpgradeImMsgResp, error) {
	_ = s.mgr.TestPushIMMgs(ctx, req.GetUid(), req.GetToUid(), req.GetLevel())

	if req.GetChannelId() != 0 {
		s.mgr.PushUpgradeAnimation(ctx, 0, req.GetLevel(), req.GetChannelId(), req.GetUid(), req.GetToUid())
	}
	return &pb.TestUpgradeImMsgResp{}, nil
}

func NewServer(ctx context.Context, sc *conf.StartConfig) (*Server, error) {
	st, err := store.NewStore(ctx, sc)
	if err != nil {
		log.Errorf("init store failed:%v", err)
		return nil, err
	}
	accountCli, _ := accountClient.NewClient()
	friendshipCli, _ := friendshipClient.NewClient()
	presentCli := userPresentClient.NewClient()
	sendImCli := sendim.NewClient()
	seqgenCli, _ := seqgen.NewClient()
	timelineCli := Timeline.NewClient()
	payCli, _ := unifiedPay.NewClient()
	presentMiddleCli, _ := present_middleware.NewClient()
	currencyCli := currency.NewClient()
	pushCli, _ := PushNotification.NewClient()
	blacklistCli, _ := userBlackListService.NewClient()
	banuserClient := banuser.NewClient()
	expCli := exp.NewClient()
	tlCli := missionTL.NewClient()
	channelCPCli, _ := channelcpgame.NewClient()
	channelOlCli := channelol.NewClient()
	channelMicCli := channelMic.NewClient()
	channelCli := channel.NewClient()
	ukwCli, _ := youknowwho.NewClient()
	userProfileApiCli, _ := userprofileapi.NewDisplayClient()
	yswfUkwDelayCli, _ := yswfukwdelay.NewClient()
	publicNoticeCli, _ := publicnotice.NewClient()
	reconcilePresentCli := reconcilePresent.NewIClient()
	levelAwardClient, _ := fellow_level_award.NewClient(ctx)
	missionHelper := NewMissionHelperCli(expCli, currencyCli, tlCli, seqgenCli)

	channelDatingGameRecordCli, _ := channel_dating_game_record.NewClient(ctx)
	if err != nil {
		return nil, err
	}

	bc, err := conf.NewBusinessConfManager()
	if err != nil {
		log.ErrorWithCtx(ctx, "init business conf fail, err: %v", err)
		return nil, err
	}

	mgr := manger.NewManager(sc, bc, st)

	s := &Server{
		sc:               sc,
		bc:               bc,
		store:            st,
		accountCli:       accountCli,
		friendshipClient: friendshipCli,
		presentCli:       presentCli,
		sendImCli:        sendImCli,
		seqgenCli:        seqgenCli,
		timelineCli:      timelineCli,
		payCli:           payCli,
		currencyCli:      currencyCli,
		presentMiddleCli: presentMiddleCli,
		pushCli:          pushCli,
		blacklistCli:     blacklistCli,
		banuserClient:    banuserClient,
		expCli:           expCli,
		missionHelper:    missionHelper,
		//kafkaSub:                   kafkaSub,
		shutDown:                   make(chan interface{}),
		mgr:                        mgr,
		channelCPCli:               channelCPCli,
		channelOlCli:               channelOlCli,
		channelMicCli:              channelMicCli,
		channelCli:                 channelCli,
		ukwCli:                     ukwCli,
		userProfileApiCli:          userProfileApiCli,
		yswfUkwDelayCli:            yswfUkwDelayCli,
		publicNoticeCli:            publicNoticeCli,
		reconcilePresentCli:        reconcilePresentCli,
		channelDatingGameRecordCli: channelDatingGameRecordCli,
		levelAwardCli:              levelAwardClient,
	}

	if sc.Environment != "staging" {
		kafkaSub, err := event.NewKafkaEventSub(sc, bc, st, mgr)
		log.Infof("init kafkaSub suc  err:%v", err)
		s.kafkaSub = kafkaSub
	}

	s.fellowPresentConfigMap = make(map[uint32]*userpresent.StPresentItemConfig, 0)

	err = s.InitTimer()
	if err != nil {
		log.Errorf("init timer fail, err: %v", err)
		return nil, err
	}

	s.freshFellowPresentConfig()
	s.mgr.UpdatePresentList()
	s.mgr.FreshFellowPresentConfig()
	return s, nil
}

func (s *Server) ShutDown() {
	s.kafkaSub.Stop()
	s.store.Close(context.Background())
	close(s.shutDown)
}

func (s *Server) GetStore() *store.Store {
	return s.store
}

func (s *Server) GetMgr() *manger.Manager {
	return s.mgr
}

func (s *Server) checkNewFellowTypeAllowed(ctx context.Context, fellowType uint32) bool {
	if fellowType != uint32(pb.FellowType_ENUM_FELLOW_TYPE_PARTNER) {
		return true
	}

	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return true
	}
	log.DebugWithCtx(ctx, "checkNewFellowTypeAllowed fellowType:%d", fellowType)
	return s.bc.NewFellowAllow(uint16(serviceInfo.MarketID), serviceInfo.ClientType, serviceInfo.ClientVersion)
}

func (s *Server) freshFellowPresentConfig() {
	resp, err := s.store.GetAllPresentConfig(true)
	if err != nil {
		log.Errorf("init FellowPresentConfig fail , err :%v", err)
		return
	}
	presentList := make([]uint32, 0)
	for _, item := range resp {
		presentList = append(presentList, item.GiftId)
	}

	presentConfig, err := s.presentCli.GetPresentConfigByIdList(context.Background(), 0, presentList, 0)
	if err != nil {
		log.Errorf("init FellowPresentConfig GetPresentConfigByIdList fail , err :%v", err)
		return
	}

	for _, item := range presentConfig.GetItemList() {
		s.fellowPresentConfigMap[item.GetItemId()] = item
	}

	log.Debugf("freshFellowPresentConfig done")
}

func timeDiffVal(startTime, endTime time.Time) int64 {
	return (endTime.UnixNano() - startTime.UnixNano()) / 1e6
}

// 并发执行多任务, 全部执行完之后有序判断异常列表
func concurrentExecWithOrderlyErr(fns ...func() error) error {
	errList := make([]error, len(fns))

	// 部分失败不会提前打断的fn列表
	noInterruptFns := make([]func() error, len(fns))
	for i, fn := range fns {
		thisFn := fn
		thisFnIdx := i
		noInterruptFns[i] = func() error {
			errList[thisFnIdx] = thisFn()
			return nil
		}
	}

	mapreduce.Finish(noInterruptFns...)
	for _, err := range errList {
		if err != nil {
			return err
		}
	}

	return nil
}
