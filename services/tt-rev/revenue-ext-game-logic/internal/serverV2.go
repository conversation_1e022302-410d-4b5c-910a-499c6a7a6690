package internal

import (
    "context"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app"
    pb "golang.52tt.com/protocol/app/revenue-ext-game-logic"
    "golang.52tt.com/protocol/common/status"
    channel_ext_game "golang.52tt.com/protocol/services/channel-ext-game"
    riskMngApiPb "golang.52tt.com/protocol/services/risk-mng-api"

    user_online "golang.52tt.com/protocol/services/user-online"
    "google.golang.org/grpc/codes"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_go"
    channelPb "golang.52tt.com/protocol/app/channel"
)

func (svr *Server) GetExtGameInfoList(c context.Context, request *pb.GetExtGameInfoListRequest) (*pb.GetExtGameInfoListResponse, error) {
    out := &pb.GetExtGameInfoListResponse{}
    defer func() {
        log.DebugWithCtx(c, "GetExtGameInfoList req:%+v, resp:%+v", request, out)
    }()
    infoResp, err := svr.channelExtGameCli.GetExtGameInfoList(c, &channel_ext_game.GetExtGameInfoListReq{})
    if err != nil {
        log.ErrorWithCtx(c, "GetExtGameInfoList fail to GetExtGameInfoList. req:%+v, err:%v", request, err)
        return out, err
    }
    for _, info := range infoResp.GetGameInfo() {
        out.GameList = append(out.GameList, &pb.ExtGameInfo{
            Appid:   info.GetAppId(),
            Name:    info.GetName(),
            Content: info.GetContent(),
            Version: info.GetVersion(),
            Build:   info.GetBuild(),
            Zip:     info.GetZip(),
            H5Url:   info.GetH5Url(),
            FullUrl: info.GetFullUrl(),
            Md5:     info.GetMd5(),
            Size:    info.GetSize(),
        })
    }
    return out, nil
}

func (svr *Server) GetExtGameOpenId(c context.Context, request *pb.GetExtGameOpenIdRequest) (*pb.GetExtGameOpenIdResponse, error) {
    out := &pb.GetExtGameOpenIdResponse{}
    defer func() {
        log.DebugWithCtx(c, "GetExtGameOpenId req:%+v, resp:%+v", request, out)
    }()
    info, ok := protogrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "GetExtGameOpenId ServiceInfoFromContext fail. req:%+v", request)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    openIdResp, err := svr.channelExtGameCli.GetUserExtGameOpenid(c, &channel_ext_game.GetUserExtGameOpenidReq{
        Uid: info.UserID,
    })
    if err != nil {
        log.ErrorWithCtx(c, "GetExtGameOpenId fail to GetUserExtGameOpenid. req:%+v, err:%v", request, err)
        return out, err
    }
    out.Openid = openIdResp.GetOpenid()
    return out, nil
}

func (svr *Server) GetExtGameJsCode(c context.Context, request *pb.GetExtGameJsCodeRequest) (*pb.GetExtGameJsCodeResponse, error) {
    out := &pb.GetExtGameJsCodeResponse{}
    defer func() {
        log.InfoWithCtx(c, "GetExtGameJsCode req:%+v, resp:%+v", request, out)
    }()
    info, ok := protogrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "GetExtGameJsCode ServiceInfoFromContext fail. req:%+v", request)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    channelViewIdMap, err := svr.getChannelInfoByViewIdList(c, info.UserID, []string{request.GetChannelViewId()})
    if err != nil {
        log.ErrorWithCtx(c, "GetExtGameJsCode fail to getChannelInfoByViewIdList. req:%+v, err:%v", request, err)
        return out, err
    }

    channelInfo := channelViewIdMap[request.GetChannelViewId()]

    // 新增房间类型检查
    if !svr.bc.CheckChannelTypeLimit(channelInfo.GetChannelType()) {
        log.DebugWithCtx(c, "GetExtGameAccessList channel_type is not support. req:%+v", request)
        return out, nil
    }

    // 检查用户人群包、房间白名单
    svrResp, err := svr.channelExtGameCli.CheckUserGameAccess(c, &channel_ext_game.CheckUserGameAccessReq{
        Uid:       info.UserID,
        ChannelId: channelInfo.GetChannelId(),
        //AppId:     request.GetAppid(),
    })
    if err != nil {
        log.ErrorWithCtx(c, "GetExtGameAccessList fail to CheckUserGameAccess. req:%+v, err:%v", request, err)
        return out, err
    }

    if !svrResp.GetAccess() {
        log.WarnWithCtx(c, "GetExtGameAccessList user not in access list. req:%+v", request)
        return out, nil
    }

    jsCodeResp, err := svr.channelExtGameCli.GetUserExtGameJsCode(c, &channel_ext_game.GetUserExtGameJsCodeReq{
        Uid:           info.UserID,
        ChannelViewId: request.GetChannelViewId(),
        //GameId:        cfg.AccessInfo.GameId,
        AppId: request.GetAppid(),
    })
    if err != nil {
        log.ErrorWithCtx(c, "GetExtGameJsCode fail to GetUserExtGameJsCode. req:%+v, err:%v", request, err)
        return out, err
    }
    out.JsCode = jsCodeResp.GetJsCode()
    out.Openid = jsCodeResp.GetOpenid()
    return out, nil
}

func (svr *Server) GetExtGameAccessList(c context.Context, request *pb.GetExtGameAccessListRequest) (*pb.GetExtGameAccessListResponse, error) {
    out := &pb.GetExtGameAccessListResponse{}
    log.DebugWithCtx(c, "GetExtGameAccessList req:%+v", request)
    info, ok := protogrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "GetExtGameAccessList ServiceInfoFromContext fail. req:%+v", request)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    userId := info.UserID
    if request.GetChannelId() == 0 {
        log.WarnWithCtx(c, "GetExtGameAccessList channel_id is empty. req:%+v", request)
        return out, nil
    }

    list := svr.bc.GetExtGameAccessInfoList()
    if len(list) == 0 {
        return out, nil
    }

    // 新增房间类型检查
    if !svr.bc.CheckChannelTypeLimit(request.GetChannelType()) {
        log.DebugWithCtx(c, "GetExtGameAccessList channel_type is not support. req:%+v", request)
        return out, nil
    }

    // 检查用户人群包、房间白名单
    svrResp, err := svr.channelExtGameCli.CheckUserGameAccess(c, &channel_ext_game.CheckUserGameAccessReq{
        Uid:       userId,
        ChannelId: request.GetChannelId(),
        //AppId:     request.GetAppid(),
    })
    if err != nil {
        log.ErrorWithCtx(c, "GetExtGameAccessList fail to CheckUserGameAccess. req:%+v, err:%v", request, err)
        return out, err
    }

    if !svrResp.GetAccess() {
        log.WarnWithCtx(c, "GetExtGameAccessList user not in access list. req:%+v", request)
        return out, nil
    }

    var usePgcFloat bool
    if request.GetChannelType() == uint32(channelPb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) ||
        request.GetChannelType() == uint32(channelPb.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) ||
        request.GetChannelType() == uint32(channelPb.ChannelType_RADIO_LIVE_CHANNEL_TYPE) ||
        request.GetChannelType() == uint32(channelPb.ChannelType_GUILD_HOME_CHANNEL_TYPE) {
        usePgcFloat = true
    }

    accessList := make([]*pb.ExtGameAccessInfo, 0)
    for _, v := range list {
        floatUrl := v.FloatingUrl
        if usePgcFloat && v.PgcFloatingUrl != "" {
            floatUrl = v.PgcFloatingUrl
        }
        accessList = append(accessList, &pb.ExtGameAccessInfo{
            Appid:               v.Appid,
            Icon:                v.Icon,
            Name:                v.Name,
            FloatingUrl:         floatUrl,
            PopupUrl:            v.PopupUrl,
            HasRed:              v.HasRed,
            ShowInMore:          v.ShowInMore,
            DaysShowAfterPlay:   v.DaysShowAfterPlay,
            DaysShowAfterIgnore: v.DaysShowAfterIgnore,
        })
    }

    ////入口风控检查
    //_, err = svr.entryRiskCheck(c, request.GetChannelId(), request.GetBaseReq())
    //if err != nil {
    //    //out.BaseResp = baseResp
    //    log.ErrorWithCtx(c, "GetExtGameAccessList fail to entryRiskCheck. req:%+v, err:%v", request, err)
    //    return out, nil // 不返回错误信息，不返回入口
    //}

    out.AccessInfo = accessList
    log.DebugWithCtx(c, "GetExtGameAccessList success. req:%+v, resp:%+v", request, out)
    return out, nil
}

func (svr *Server) GetExtGameWhiteChannel(c context.Context, request *pb.GetExtGameWhiteChannelRequest) (*pb.GetExtGameWhiteChannelResponse, error) {
    out := &pb.GetExtGameWhiteChannelResponse{}
    defer func() {
        log.DebugWithCtx(c, "GetExtGameWhiteChannel req:%+v, resp:%+v", request, out)
    }()

    info, ok := protogrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "GetExtGameWhiteChannel ServiceInfoFromContext fail. req:%+v", request)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    uid := info.UserID

    whiteChannelResp, err := svr.channelExtGameCli.GetWhiteChannelRandomly(c, &channel_ext_game.GetWhiteChannelRandomlyReq{})
    if err != nil {
        log.ErrorWithCtx(c, "GetExtGameWhiteChannel fail to GetWhiteChannelRandomly. req:%+v, err:%v", request, err)
        return out, err
    }

    var cid, cnt uint32
    for cid == 0 && cnt < 3 {
        cnt++
        channelResp, err := svr.channelCli.BatchGetChannelSimpleInfo(c, &channel_go.BatchGetChannelSimpleInfoReq{
            OpUid:         uid,
            ChannelIdList: whiteChannelResp.GetCidList(),
        })
        if err != nil {
            log.ErrorWithCtx(c, "GetExtGameWhiteChannel fail to BatchGetChannelSimpleInfo. req:%+v, err:%v", request, err)
            return out, err
        }

        for _, v := range channelResp.GetChannelSimpleList() {
            if v.GetIsDel() {
                continue
            }
            // 检查房间是否可跳转
            err = svr.CheckIfChannelCouldJoin(c, v, uid)
            if err == nil {
                cid = v.GetChannelId()
                break
            }
            log.ErrorWithCtx(c, "GetExtGameWhiteChannel fail to CheckIfChannelCouldJoin. req:%+v, err:%v", request, err)
        }
    }

    out.ChannelId = cid
    log.DebugWithCtx(c, "GetExtGameWhiteChannel success. uid:%d, resp:%+v", uid, out)
    return out, nil
}

func (svr *Server) CheckExtGameChannel(c context.Context, request *pb.CheckExtGameChannelRequest) (*pb.CheckExtGameChannelResponse, error) {
    out := &pb.CheckExtGameChannelResponse{}
    defer func() {
        log.DebugWithCtx(c, "CheckExtGameChannel req:%+v, resp:%+v", request, out)
    }()

    var err error
    info, ok := protogrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "CheckExtGameChannel ServiceInfoFromContext fail. req:%+v", request)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    channelMap, err := svr.getChannelInfoByViewIdList(c, info.UserID, []string{request.GetChannelViewId()})
    if err != nil {
        log.ErrorWithCtx(c, "CheckExtGameChannel fail to getChannelInfoByViewIdList. req:%+v, err:%v", request, err)
        return out, err
    }
    channelId := channelMap[request.GetChannelViewId()].GetChannelId()

    if channelId == 0 || channelMap[request.GetChannelViewId()].GetIsDel() {
        log.ErrorWithCtx(c, "CheckExtGameChannel channel not exist. req:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, status.ErrChannelNotExist, "加入房间失败")
    }

    //resp, err := svr.channelExtGameCli.CheckWhiteList(c, &channel_ext_game.CheckWhiteListReq{
    //    ChannelId: channelId,
    //})
    //if err != nil {
    //    log.ErrorWithCtx(c, "CheckExtGameChannel fail to CheckWhiteChannel. req:%+v, err:%v", request, err)
    //    return out, err
    //}
    //if !resp.GetCidWhite() {
    //    log.ErrorWithCtx(c, "CheckExtGameChannel channel not in white list. req:%+v", request)
    //    return out, protocol.NewExactServerError(codes.OK, status.ErrChannelExtGameJoinRoomFail)
    //}

    channelInfo := channelMap[request.GetChannelViewId()]

    err = svr.CheckIfChannelCouldJoin(c, channelInfo, info.UserID)
    if err != nil {
        log.ErrorWithCtx(c, "CheckExtGameChannel fail to CheckIfChannelCouldJoin. req:%+v, err:%v", request, err)
        return out, err
    }

    out.ChannelId = channelInfo.GetChannelId()
    return out, nil
}

func (svr *Server) getChannelInfoByViewIdList(ctx context.Context, uid uint32, viewIdList []string) (map[string]*channel_go.ChannelSimpleInfo, error) {
    channelResp, err := svr.channelCli.BatchGetChannelSimpleInfo(ctx, &channel_go.BatchGetChannelSimpleInfoReq{
        OpUid:             uid,
        ChannelIdList:     nil,
        ChannelViewIdList: viewIdList,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelByDisplayId failed, err %v", err)
        return nil, err
    }

    channelViewMap := make(map[string]*channel_go.ChannelSimpleInfo)
    for _, v := range channelResp.GetChannelSimpleList() {
        channelViewMap[v.GetChannelViewId()] = v
    }

    return channelViewMap, nil
}

func (svr *Server) ExtGameLoginCheck(c context.Context, request *pb.ExtGameLoginCheckRequest) (*pb.ExtGameLoginCheckResponse, error) {
    out := &pb.ExtGameLoginCheckResponse{}
    defer func() {
        log.DebugWithCtx(c, "ExtGameLoginCheck req:%+v, resp:%+v", request, out)
    }()

    var err error
    info, ok := protogrpc.ServiceInfoFromContext(c)
    if !ok {
        log.ErrorWithCtx(c, "ExtGameLoginCheck ServiceInfoFromContext fail. req:%+v", request)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    uid := info.UserID
    channelId := request.GetChannelId()

    channelResp, err := svr.channelCli.GetChannelSimpleInfo(c, &channel_go.GetChannelSimpleInfoReq{
        ChannelId:     request.GetChannelId(),
        OpUid:         uid,
        ChannelViewId: request.GetChannelViewId(),
    })
    if err != nil {
        log.ErrorWithCtx(c, "GetExtGameWhiteChannel fail to BatchGetChannelSimpleInfo. req:%+v, err:%v", request, err)
        return out, err
    }

    // 新增房间类型检查
    if !svr.bc.CheckChannelTypeLimit(channelResp.GetChannelSimple().GetChannelType()) {
        log.DebugWithCtx(c, "GetExtGameAccessList channel_type is not support. req:%+v", request)
        return out, protocol.NewExactServerError(nil, status.ErrAntispamSuspectReject, "很抱歉，您暂不满足参与条件~")
    }

    // 房间白名单、检查用户人群包
    svrResp, err := svr.channelExtGameCli.CheckUserGameAccess(c, &channel_ext_game.CheckUserGameAccessReq{
        Uid:       uid,
        ChannelId: channelId,
        //AppId:     request.GetAppid(),
    })
    if err != nil {
        log.ErrorWithCtx(c, "ExtGameLoginCheck fail to CheckUserGameAccess. req:%+v, err:%v", request, err)
        return out, err
    }

    if !svrResp.GetAccess() {
        log.WarnWithCtx(c, "ExtGameLoginCheck user not in access list. req:%+v", request)
        return out, protocol.NewExactServerError(nil, status.ErrAntispamSuspectReject, "很抱歉，您暂不满足参与条件~")
    }

    //入口风控检查
    if baseResp, err := svr.entryRiskCheck(c, channelId, request.GetBaseReq()); err != nil {
        out.BaseResp = baseResp
        log.ErrorWithCtx(c, "ExtGameLoginCheck fail to entryRiskCheck. req:%+v, err:%v", request, err)
        return out, err
    }

    return out, nil
}

func (svr *Server) entryRiskCheck(ctx context.Context, cid uint32, baseReq *app.BaseReq) (*app.BaseResp, error) {
    // 返回给客户端的 BaseResp
    baseResp := &app.BaseResp{}
    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        return baseResp, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
    }
    uid := serviceInfo.UserID

    onlineInfo, err := svr.userOnlineCli.GetLatestOnlineInfo(ctx, &user_online.GetLatestOnlineInfoReq{
        Uid: uid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "entryRiskCheck fail to GetLatestOnlineInfo. err:%v", err)
        return baseResp, nil
    }

    checkReq := &riskMngApiPb.CheckReq{
        Scene: "THIRD_GAME_ENTRY_ZDXX",
        SourceEntity: &riskMngApiPb.Entity{
            Uid:       uid,
            ClientIp:  onlineInfo.GetOnlineInfo().GetClientIp(),
            ChannelId: cid,
        },
        //// 根据需求，传递额外的参数
        //CustomParams: map[string]string{
        //    "room_id": fmt.Sprintf("%d", cid),
        //},
    }
    checkResp, err := svr.riskMngApiCli.CheckHelper(ctx, checkReq, baseReq)
    if err != nil {
        // 系统错误，风控非关键路径，可忽略系统错误
        log.ErrorWithCtx(ctx, "entryRiskCheck risk-mng-api.Check failed, err:%v, req:%+v", err, checkReq)
        return baseResp, nil
    }
    // 命中风控拦截
    if checkResp.GetErrCode() < 0 {
        // 建议打个 info 拦截日志，方便排查，风控拦截日志不会很多
        log.InfoWithCtx(ctx, "entryRiskCheck risk-mng-api.Check hit, req:%+v, resp:%+v", checkReq, checkResp)
        // 需要返回 ErrInfo 给客户端
        baseResp.ErrInfo = checkResp.GetErrInfo()
        // 返回错误码给客户端，并设置 gRPC 错误码为 OK
        return baseResp, protocol.NewExactServerError(codes.OK, int(checkResp.GetErrCode()), checkResp.GetErrMsg())
    }
    // 无拦截
    return baseResp, nil
}
