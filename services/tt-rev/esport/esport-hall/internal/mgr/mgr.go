//go:generate quicksilver-cli.exe test interface ../mgr
package mgr

import (
    "context"
    "crypto/rand"
    "fmt"
    "golang.52tt.com/services/tt-rev/esport/common/user_group"
    "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/local_cache"
    "math/big"
    "time"

    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    push "golang.52tt.com/clients/push-notification/v2"
    "golang.52tt.com/pkg/bylink"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/pkg/timer"
    pushPb "golang.52tt.com/protocol/app/push"
    push_notification "golang.52tt.com/protocol/services/push-notification/v2"
    trade_im_notify "golang.52tt.com/services/tt-rev/esport/common/trade-im-notify"
    "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/cache"
    "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/conf"
    "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/rpc"
    "golang.52tt.com/services/tt-rev/esport/esport-hall/internal/store"
)

const (
    guaranteeWin = "包赢承诺"
)

type Mgr struct {
    bc           conf.IBusinessConfManager
    store        store.IStore
    cache        cache.ICache
    rpcCli       *rpc.Client
    esportImCli  trade_im_notify.ITradeImNotify
    timerD       *timer.Timer
    shutdown     chan interface{}
    localCache   *local_cache.LocalCache
    userGroupCli *user_group.UserGroupCli
}

func NewMgr(bc conf.IBusinessConfManager, store store.IStore, cache cache.ICache, rpcCli *rpc.Client) *Mgr {
    esportImCli := trade_im_notify.NewTradeImNotify(rpcCli.AccountCli, rpcCli.SeqCli, rpcCli.TimelineCli, rpcCli.ImApiCli)
    m := &Mgr{
        bc:           bc,
        store:        store,
        cache:        cache,
        rpcCli:       rpcCli,
        esportImCli:  esportImCli,
        userGroupCli: rpcCli.UserGroupCli,
    }
    // 开启本地定时任务
    m.startInternalTimer()
    // 开启业务定时任务
    m.setupTimer()

    // 初始化本地缓存
    localCache, err := local_cache.NewLocalCache(rpcCli.ESportSkillCli, rpcCli.ESportStatCli)
    if err != nil {
        panic(fmt.Errorf("Failed to new local cache: %v", err))
    }
    m.localCache = localCache

    // 百灵数据统计 初始化
    bylinkCollect, err := bylink.NewKfkCollector()
    if err != nil {
        log.Errorf("Failed to new kfk collector: %v", err)
    }
    bylink.InitGlobalCollector(bylinkCollect)

    return m
}

func NewToolMgr(bc conf.IBusinessConfManager, store store.IStore, cache cache.ICache, rpcCli *rpc.Client) *Mgr {
    return &Mgr{
        bc:     bc,
        store:  store,
        cache:  cache,
        rpcCli: rpcCli,
    }
}

func GetUidFromCtx(ctx context.Context) uint32 {
    serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        return 0
    }

    return serviceInfo.UserID
}

func randUint32(max uint32) uint32 {
    r, _ := rand.Int(rand.Reader, big.NewInt(int64(max)))
    return uint32(r.Int64())
}

func (m *Mgr) Shutdown() {
    m.shutdown <- struct{}{}
    m.timerD.Stop()
}

func (m *Mgr) CommonPlainTextToastPush(ctx context.Context, uid uint32, content string) error {
    msgSt := &pushPb.CommonPlainTextToastPush{
        Uid:     uid,
        Content: content,
    }
    msg, _ := proto.Marshal(msgSt)

    pushMessage := &pushPb.PushMessage{
        Cmd:     uint32(pushPb.PushMessage_COMMON_TEXT_TOAST_PUSH),
        Content: msg,
    }
    pushMessageBytes, _ := proto.Marshal(pushMessage)

    notification := &push_notification.CompositiveNotification{
        Sequence:           uint32(time.Now().Unix()),
        TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
        TerminalTypePolicy: push.DefaultPolicy,
        AppId:              0,
        ProxyNotification: &push_notification.ProxyNotification{
            Type:    uint32(push_notification.ProxyNotification_PUSH),
            Payload: pushMessageBytes,
        },
    }
    err := m.rpcCli.PushCli.PushToUsers(ctx, []uint32{uid}, notification)
    if err != nil {
        log.ErrorWithCtx(ctx, "CommonPlainTextToastPush, uid: %d, content: %d, err: %v", uid, content, err)
    }
    return err
}

func (m *Mgr) BatchCloseCoachSwitch(ctx context.Context, uidList []uint32) error {
    err := m.store.BatchCloseCoachNewCustomerSwitch(ctx, uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchCloseCoachSwitch err: %v", err)
        return err
    }
    err = m.store.BatchCloseCoachGuaranteeWinSwitch(ctx, uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchCloseCoachSwitch err: %v", err)
        return err
    }
    err = m.store.BatchCloseFirstRoundSwitch(ctx, uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchCloseCoachSwitch err: %v", err)
        return err
    }
    return nil
}
