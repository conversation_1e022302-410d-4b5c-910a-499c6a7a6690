package store

import(
	context "context"
	primitive "go.mongodb.org/mongo-driver/bson/primitive"
	time "time"
)

type IStore interface {
	AddCoachIncentiveAddition(ctx context.Context, addition *CoachIncentiveAddition) error
	AddCoachRecommend(ctx context.Context, coachRecommend *CoachRecommend) error
	AddFirstRoundOrderRecord(ctx context.Context, record *FirstRoundOrderRecord) error
	AddNewCustomerOrderRecord(ctx context.Context, record *NewCustomerOrderRecord) error
	BatchCloseCoachGuaranteeWinSwitch(ctx context.Context, uidList []uint32) error
	BatchCloseCoachNewCustomerSwitch(ctx context.Context, uidList []uint32) error
	BatchCloseFirstRoundSwitch(ctx context.Context, uidList []uint32) error
	BatchFindSkillProductByProductIds(ctx context.Context, productIds []uint32) (map[uint32]*SkillProduct,error)
	BatchFindSkillProductByUid(ctx context.Context, uids []uint32, gameId uint32) (map[uint32]*SkillProduct,error)
	BatchGetUSerSetting(ctx context.Context, uids []uint32) (map[uint32]*UserSetting,error)
	BatchInsertSkillProduct(ctx context.Context, data []*SkillProduct) error
	BatchUpdateSkillProduct(ctx context.Context, data []*SkillProduct) error
	ClearFirstRoundOrderRecord(ctx context.Context, playerUid, coachUid, gameId uint32) error
	ClearNewCustomerOrderRecord(ctx context.Context, playerUid uint32) error
	Close(ctx context.Context) error
	CloseCoachAllNewCustomerSwitch(ctx context.Context, coachUid uint32) error
	CntCoachRecommend(ctx context.Context, ttid string, gameId, recommendType, startTime, endTime uint32) (int64,error)
	DelCoachRecommend(ctx context.Context, id string) error
	DelSkillProduct(ctx context.Context, uid, skillId []uint32) error
	DistinctSkillId(ctx context.Context) []uint32
	DistinctUidBySkillId(ctx context.Context, skillId uint32) ([]uint32,error)
	DistnctUid(ctx context.Context) []uint32
	EnsureIndex(ctx context.Context) 
	EnsureIndexCoachIncentiveAddition(ctx context.Context) 
	EnsureIndexCoachRecommend(ctx context.Context) 
	EnsureIndexFirstRoundOrderRecord(ctx context.Context) 
	EnsureIndexInviteOrderLog(ctx context.Context) 
	EnsureIndexNewCustomerOrderRecord(ctx context.Context) 
	EnsureIndexQuickReceiveRecord(ctx context.Context) 
	EnsureIndexSkillProduct(ctx context.Context) 
	EnsureIndexUserSettingColl(ctx context.Context) 
	Find15MinAgoInviteOrderLog(ctx context.Context) ([]*InviteOrderLog,error)
	FindBySkillId(ctx context.Context, skillId uint32) ([]*SkillProduct,error)
	FindEnableSkillProductByUid(ctx context.Context, uid uint32) ([]*SkillProduct,error)
	FindFirstRoundOrderRecord(ctx context.Context, playerUid uint32, coachList []uint32, gameId uint32) ([]*FirstRoundOrderRecord,error)
	FindInviteOrderLogByInviteId(ctx context.Context, inviteId uint32) (*InviteOrderLog,error)
	FindQuickReceiveRecord(ctx context.Context, uid uint32, startTime time.Time, endTime time.Time) ([]*QuickReceiveRecord,error)
	FindSkillProduct(ctx context.Context, uidList []uint32, skillId, productId uint32) ([]*SkillProduct,error)
	FindSkillProductByCoachUidsAndGameId(ctx context.Context, gameId uint32, uids []uint32) ([]*SkillProduct,error)
	FindSkillProductByProductId(ctx context.Context, productId uint32) (*SkillProduct,error)
	FindSkillProductBySkillId(ctx context.Context, uid, skillId uint32) (*SkillProduct,error)
	FindSkillProductBySkillIdIgnoreStatus(ctx context.Context, uid, skillId uint32) (*SkillProduct,error)
	FindSkillProductBySkillIdPaged(ctx context.Context, skillId uint32, offset, limit int) ([]*SkillProduct,error)
	FindSkillProductByUid(ctx context.Context, uid uint32) ([]*SkillProduct,error)
	FindSkillProductByUids(ctx context.Context, uids []uint32) ([]*SkillProduct,error)
	GenInviteId(ctx context.Context) uint32
	GenProductId(ctx context.Context) uint32
	GetAllFirstRoundCoachUids(ctx context.Context) ([]*SkillProduct,error)
	GetAllGuaranteeWinSwitch(ctx context.Context) ([]*GuaranteeWinSwitch,error)
	GetAllSkillProductPaged(ctx context.Context, offset, limit uint32) ([]*SkillProduct,error)
	GetCoachNewCustomerOrderCntToday(ctx context.Context, coachUid uint32) (int64,error)
	GetCoachRecommend(ctx context.Context, ttid string, gameId, recommendType, startTime, endTime uint32) ([]*CoachRecommend,error)
	GetFirstRoundOrderCountToday(ctx context.Context, playerUid uint32) (int64,error)
	GetUserSetting(ctx context.Context, uid uint32) (*UserSetting,error)
	HasFirstRoundOrderRecord(ctx context.Context, playerUid, coachUid, gameId uint32) (bool,error)
	HasNewCustomerOrderRecord(ctx context.Context, playerUid uint32) (bool,error)
	HasSkillProductOpenFirstRound(ctx context.Context, uid uint32) (bool,error)
	HasSkillProductOpenNewCustomer(ctx context.Context, uid uint32) (bool,error)
	IncCounter(ctx context.Context, bizName string) uint32
	InsertInviteOrderLog(ctx context.Context, log *InviteOrderLog) error
	InsertQuickReceiveRecord(ctx context.Context, record *QuickReceiveRecord) error
	SearchFirstRoundCoachList(ctx context.Context, pageSize uint32, gameId uint32, excludeUid []uint32) ([]*SkillProduct,error)
	SearchUserFirstRecordByPage(ctx context.Context, offsetId primitive.ObjectID, limit uint32, uids []uint32) ([]*FirstRoundOrderRecord,error)
	SetCoachAllFirstRoundSwitch(ctx context.Context, coachUid uint32, switchStatus bool) error
	SetCoachAllNewCustomerSwitch(ctx context.Context, coachUid uint32, switchStatus bool) error
	SetFirstRoundSwitch(ctx context.Context, uid, skillId uint32, switchStatus bool) error
	SetGuaranteeWinSwitch(ctx context.Context, uid, skillId uint32, switchStatus bool) error
	SetNewCustomerSwitch(ctx context.Context, uid, skillId uint32, switchStatus bool) error
	UpdateCoachRecommend(ctx context.Context, id string, coachRecommend *CoachRecommend) error
	UpdateInviteOrderLogStatus(ctx context.Context, inviteId, status uint32) error
	UpdateReceiveTimeFrame(ctx context.Context, uid uint32, receiveTimeFrame *ReceiveTimeFrame) error
	UpdateRecommendVal(ctx context.Context, productId uint32, recommendVal float64) error
	UpdateSkillProductPrice(ctx context.Context, uid, skillId, price uint32) error
	UpdateSkillProductReceivingTime(ctx context.Context, uid, skillId uint32, startTime, endTime time.Time, dayOfWeek []bool) error
	UpdateSkillProductSwitch(ctx context.Context, uid, skillId uint32, switchStatus bool) error
}


type ISkillProduct interface {
	GetGuaranteeWinTexts() []string
	Marshal() ([]byte,error)
}


type ISkillProductList interface {
	ExtractUid() []uint32
}

