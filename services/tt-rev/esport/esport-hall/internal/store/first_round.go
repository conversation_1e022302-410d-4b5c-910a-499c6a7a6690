package store

import (
    "context"
    "go.mongodb.org/mongo-driver/bson"
    "go.mongodb.org/mongo-driver/bson/primitive"
    "go.mongodb.org/mongo-driver/mongo"
    "go.mongodb.org/mongo-driver/mongo/options"
    "golang.52tt.com/pkg/log"
    "time"
)

type FirstRoundOrderRecord struct {
    ID        primitive.ObjectID `bson:"_id,omitempty"`
    PlayerUid uint32             `bson:"player_uid"`
    CoachUid  uint32             `bson:"coach_uid"`
    GameId    uint32             `bson:"game_id"`
    OrderId   string             `bson:"order_id"`
    CreateAt  time.Time          `bson:"create_at"`
}

func (s *Store) EnsureIndexFirstRoundOrderRecord(ctx context.Context) {
    _, err := s.firstRoundOrderColl.Indexes().CreateMany(context.Background(), []mongo.IndexModel{
        {
            Keys: bson.D{
                {
                    Key:   "player_uid",
                    Value: 1,
                },
                {
                    Key:   "coach_uid",
                    Value: 1,
                },
                {
                    Key:   "game_id",
                    Value: 1,
                },
            },
            Options: options.Index().SetUnique(true),
        },
        {
            Keys: bson.D{
                {
                    Key:   "player_uid",
                    Value: 1,
                },
                {
                    Key:   "create_at",
                    Value: 1,
                },
            },
        },
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "EnsureIndex err: %v", err)
    }
}

func (s *Store) AddFirstRoundOrderRecord(ctx context.Context, record *FirstRoundOrderRecord) error {
    filter := bson.M{"player_uid": record.PlayerUid, "coach_uid": record.CoachUid, "game_id": record.GameId}
    update := bson.M{
        "$set": bson.M{
            "order_id":  record.OrderId,
            "create_at": record.CreateAt,
        },
    }
    opts := options.Update().SetUpsert(true)
    _, err := s.firstRoundOrderColl.UpdateOne(ctx, filter, update, opts)
    return err
}

func (s *Store) HasFirstRoundOrderRecord(ctx context.Context, playerUid, coachUid, gameId uint32) (bool, error) {
    filter := bson.M{"player_uid": playerUid, "coach_uid": coachUid, "game_id": gameId}
    count, err := s.firstRoundOrderColl.CountDocuments(ctx, filter)
    if err != nil {
        return false, err
    }
    return count > 0, nil
}

func (s *Store) ClearFirstRoundOrderRecord(ctx context.Context, playerUid, coachUid, gameId uint32) error {
    filter := bson.M{"player_uid": playerUid, "coach_uid": coachUid, "game_id": gameId}
    _, err := s.firstRoundOrderColl.DeleteOne(ctx, filter)
    return err
}

func (s *Store) GetFirstRoundOrderCountToday(ctx context.Context, playerUid uint32) (int64, error) {
    t := time.Now()
    start := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
    end := start.Add(24 * time.Hour)
    filter := bson.M{
        "player_uid": playerUid,
        "create_at":  bson.M{"$gte": start, "$lt": end},
    }
    return s.firstRoundOrderColl.CountDocuments(ctx, filter)
}

// FindFirstRoundOrderRecord 通用搜索
func (s *Store) FindFirstRoundOrderRecord(ctx context.Context, playerUid uint32, coachList []uint32, gameId uint32) ([]*FirstRoundOrderRecord, error) {
    filter := bson.M{"player_uid": playerUid}
    if len(coachList) > 0 {
        filter["coach_uid"] = bson.M{"$in": coachList}
    }
    if gameId > 0 {
        filter["game_id"] = gameId
    }
    cursor, err := s.firstRoundOrderColl.Find(ctx, filter)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(ctx)
    var records []*FirstRoundOrderRecord
    err = cursor.All(ctx, &records)
    return records, err
}

// SearchFirstRoundCoachList 分页搜索开启了首单优惠的大神
func (s *Store) SearchFirstRoundCoachList(ctx context.Context, pageSize uint32, gameId uint32, excludeUid []uint32) ([]*SkillProduct, error) {
    // Create a filter to find coaches with first round switch on
    filter := bson.M{
        "first_round_switch": true,
    }

    // If excludeUid is not empty, add it to the filter
    if len(excludeUid) > 0 {
        filter["uid"] = bson.M{
            "$nin": excludeUid,
        }
    }
    if gameId != 0 {
        filter["skill_id"] = gameId
    }

    // Create a pipeline with match and sample stages
    pipeline := []bson.M{
        {"$match": filter},
        {"$sample": bson.M{"size": int(pageSize)}},
    }

    // Execute the query
    cursor, err := s.skillProductColl.Aggregate(ctx, pipeline)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(ctx)

    // Decode the results
    var coaches []*SkillProduct
    if err = cursor.All(ctx, &coaches); err != nil {
        return nil, err
    }

    return coaches, nil
}

// GetAllFirstRoundCoachUids 获取所有开启了首单优惠的大神的uid
func (s *Store) GetAllFirstRoundCoachUids(ctx context.Context) ([]*SkillProduct, error) {
    // Create a filter to find coaches with first round switch on
    filter := bson.M{
        "first_round_switch": true,
    }

    // Set projection to return only uid field
    opts := options.Find().SetProjection(bson.M{"uid": 1, "skill_id": 1})

    // Execute the query
    cursor, err := s.skillProductColl.Find(ctx, filter, opts)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(ctx)

    // Decode the results
    var coaches []*SkillProduct
    if err = cursor.All(ctx, &coaches); err != nil {
        return nil, err
    }
    return coaches, nil
}

// SearchUserFirstRecordByPage 分页搜索用户首单记录
func (s *Store) SearchUserFirstRecordByPage(ctx context.Context, offsetId primitive.ObjectID, limit uint32, uids []uint32) ([]*FirstRoundOrderRecord, error) {
    filter := bson.M{}
    if !offsetId.IsZero() {
        filter["_id"] = bson.M{"$gt": offsetId}
    }
    if len(uids) > 0 {
        filter["player_uid"] = bson.M{"$in": uids}
    }

    opts := options.Find()
    opts.SetLimit(int64(limit))

    cursor, err := s.firstRoundOrderColl.Find(ctx, filter, opts)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(ctx)

    var records []*FirstRoundOrderRecord
    if err = cursor.All(ctx, &records); err != nil {
        return nil, err
    }

    return records, nil
}

func (s *Store) BatchCloseFirstRoundSwitch(ctx context.Context, uidList []uint32) error {
    if len(uidList) == 0 {
        return nil
    }
    filter := bson.M{"uid": bson.M{"$in": uidList}}
    update := bson.M{"$set": bson.M{"first_round_switch": false}}
    _, err := s.skillProductColl.UpdateMany(ctx, filter, update)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchCloseFirstRoundSwitch err: %v", err)
        return err
    }
    log.InfoWithCtx(ctx, "BatchCloseFirstRoundSwitch success, uidList: %v", uidList)
    return nil
}
