package user_group

import (
    "bytes"
    "context"
    "encoding/json"
    "fmt"
    "golang.52tt.com/pkg/log"
    "io"
    "net/http"
    "strconv"
    "time"
)

type UserGroupCli struct {
    httpCli *http.Client
    // dsp-lpm-admin
    dspLpmAdminHost string
    // dsp-lpm-offlinegroup
    dspLpmOfflineGroupHost string
    // dsp-lpm-apiserver
    dspLpmApiserverHost string
}

func NewUserGroupCli(dspLpmAdminHost, dspLpmOfflineGroupHost, dspLpmApiserverHost string) *UserGroupCli {
    cli := &UserGroupCli{
        dspLpmAdminHost:        dspLpmAdminHost,
        dspLpmOfflineGroupHost: dspLpmOfflineGroupHost,
        dspLpmApiserverHost:    dspLpmApiserverHost,
    }
    transport := &http.Transport{
        MaxIdleConnsPerHost: 16,               // 每个host最大空闲连接
        MaxConnsPerHost:     64,               // 每个host最大连接数
        IdleConnTimeout:     30 * time.Second, // 空闲连接的超时回收时间
    }

    cli.httpCli = &http.Client{
        Transport: transport,
        Timeout:   time.Second * 5, // 请求超时时间
    }

    return cli
}

// GetGroupInfo 获取人群包信息
func (cli *UserGroupCli) GetGroupInfo(ctx context.Context, groupId string) (*GroupInfoResp, error) {
    req := &GroupInfoReq{
        GroupId: groupId,
    }

    reqBytes, err := json.Marshal(req)
    if err != nil {
        return nil, err
    }

    request, err := http.NewRequest(
        http.MethodPost,
        cli.dspLpmAdminHost+"/lpm-admin/openapi/group/groupInfo",
        bytes.NewReader(reqBytes))
    if err != nil {
        return nil, err
    }
    request = request.WithContext(ctx)
    request.Header.Set("Content-Type", "application/json")
    request.Header.Set("System-Id", "tt-revenue-esport")

    response, err := cli.httpCli.Do(request)
    if err != nil {
        return nil, err
    }
    defer response.Body.Close()

    body, err := io.ReadAll(response.Body)
    if err != nil {
        return nil, err
    }

    resp := &GroupInfoResp{}
    err = json.Unmarshal(body, resp)
    if err != nil {
        return nil, err
    }
    if resp.Code != 200 || !resp.Success {
        return nil, fmt.Errorf("code: %d, msg: %s", resp.Code, resp.Msg)
    }
    return resp, err
}

// GetEntityList 获取人群包实体列表, 注意,仅能拉取离线人群包
func (cli *UserGroupCli) GetEntityList(ctx context.Context, req *EntityListReq) (*EntityListResp, error) {
    if req.PageSize > 100000 {
        return nil, fmt.Errorf("页大小最大值100000")
    }

    // 如果没指定版本, 自动补充人群包版本号
    if req.GroupVersion == "" {
        groupInfoResp, err := cli.GetGroupInfo(ctx, req.GroupId)
        if err != nil {
            return nil, err
        }
        req.GroupVersion = groupInfoResp.Data.LastGroupVersion
    }

    reqBytes, err := json.Marshal(req)
    if err != nil {
        return nil, err
    }

    request, err := http.NewRequest(
        http.MethodPost,
        cli.dspLpmOfflineGroupHost+"/offline-group/entityList/v2",
        bytes.NewReader(reqBytes))
    if err != nil {
        return nil, err
    }
    request = request.WithContext(ctx)
    request.Header.Set("Content-Type", "application/json")
    request.Header.Set("System-Id", "tt-revenue-esport")
    log.DebugWithCtx(ctx, "GetEntityList req: %+v, reqStr:%s", req, string(reqBytes))

    log.DebugWithCtx(ctx, "GetEntityList request: %+v", request)
    response, err := cli.httpCli.Do(request)
    if err != nil {
        return nil, err
    }
    defer response.Body.Close()

    body, err := io.ReadAll(response.Body)
    if err != nil {
        return nil, err
    }

    resp := &EntityListResp{}
    err = json.Unmarshal(body, resp)
    if err != nil {
        return nil, err
    }
    if resp.Code != 200 || !resp.Success {
        return nil, fmt.Errorf("code: %d, msg: %s", resp.Code, resp.Msg)
    }
    if resp.Data == nil {
        return nil, fmt.Errorf("data is nil")
    }

    return resp, nil
}

func (cli *UserGroupCli) GetUserHasGroup(ctx context.Context, uid uint32, groupList []string) ([]string, error) {
    result := make([]string, 0)
    req := &MatchBatchReq{
        Id: time.Now().Unix(),
        Client: &MatchBatchReqClient{
            Caller: "",
            Ex:     "no",
        },
        Data: &MatchBatchReqData{
            AppId:      "ttvoice",
            GroupList:  groupList,
            EntityList: []string{fmt.Sprintf("%d", uid)},
        },
    }

    reqBytes, err := json.Marshal(req)
    if err != nil {
        return nil, err
    }

    request, err := http.NewRequest(
        http.MethodPost,
        cli.dspLpmApiserverHost+"/iop-api/group/matchBatch/v2",
        bytes.NewReader(reqBytes))
    if err != nil {
        return nil, err
    }
    request = request.WithContext(ctx)
    request.Header.Set("Content-Type", "application/json")
    request.Header.Set("System-Id", "tt-revenue-esport")

    response, err := cli.httpCli.Do(request)
    if err != nil {
        return nil, err
    }
    defer response.Body.Close()

    body, err := io.ReadAll(response.Body)
    if err != nil {
        return nil, err
    }

    resp := &MatchBatchResp{}
    err = json.Unmarshal(body, resp)
    if err != nil {
        return nil, err
    }

    if resp.Status != 0 {
        return nil, fmt.Errorf("status: %d, message: %s", resp.Status, resp.Message)
    }

    if resp.Data == nil {
        return nil, fmt.Errorf("data is nil")
    }

    for _, item := range resp.Data.Result {
        for _, groupId := range groupList {
            if v, ok := item[groupId]; ok {
                if vStr, ok := v.(string); ok {
                    if vStr == "1" {
                        result = append(result, groupId)
                    }
                }
            }
        }
    }

    return result, nil
}

func (cli *UserGroupCli) CheckUsersHasGroups(ctx context.Context, uidList []uint32, groupList []string) (map[string][]uint32, error) {
    groupMap := make(map[string][]uint32)

    uidStrList := make([]string, 0, len(uidList))
    for _, uid := range uidList {
        uidStrList = append(uidStrList, fmt.Sprintf("%d", uid))
    }

    req := &MatchBatchReq{
        Id: time.Now().Unix(),
        Client: &MatchBatchReqClient{
            Caller: "",
            Ex:     "no",
        },
        Data: &MatchBatchReqData{
            AppId:      "ttvoice",
            GroupList:  groupList,
            EntityList: uidStrList,
        },
    }

    reqBytes, err := json.Marshal(req)
    if err != nil {
        return nil, err
    }

    request, err := http.NewRequest(
        http.MethodPost,
        cli.dspLpmApiserverHost+"/iop-api/group/matchBatch/v2",
        bytes.NewReader(reqBytes))
    if err != nil {
        return nil, err
    }
    request = request.WithContext(ctx)
    request.Header.Set("Content-Type", "application/json")
    request.Header.Set("System-Id", "tt-revenue-esport")

    response, err := cli.httpCli.Do(request)
    if err != nil {
        return nil, err
    }
    defer response.Body.Close()

    body, err := io.ReadAll(response.Body)
    if err != nil {
        return nil, err
    }

    resp := &MatchBatchResp{}
    err = json.Unmarshal(body, resp)
    if err != nil {
        return nil, err
    }

    if resp.Status != 0 {
        return nil, fmt.Errorf("status: %d, message: %s", resp.Status, resp.Message)
    }

    if resp.Data == nil {
        return nil, fmt.Errorf("data is nil")
    }

    for _, item := range resp.Data.Result {
        entityId, ok := item["entityId"]
        if !ok {
            continue
        }
        uidStr, ok := entityId.(string)
        if !ok {
            continue
        }
        uid, err := strconv.ParseUint(uidStr, 10, 64)
        if err != nil {
            continue
        }

        for _, groupId := range groupList {
            val, ok := item[groupId]
            if !ok {
                continue
            }
            str, ok := val.(string)
            if !ok {
                continue
            }
            if str == "1" {
                groupMap[groupId] = append(groupMap[groupId], uint32(uid))
            }
        }
    }

    return groupMap, nil
}
