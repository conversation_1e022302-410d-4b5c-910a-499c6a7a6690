package rpc

import (
    "context"
    "golang.52tt.com/services/tt-rev/esport/common/user_group"
    "golang.52tt.com/services/tt-rev/esport/esport-skill/internal/conf"
    "time"

    "golang.52tt.com/protocol/services/esport_godlevel"

    accountClient "golang.52tt.com/clients/account"
    censoringProxy "golang.52tt.com/clients/censoring-proxy"
    esport_role "golang.52tt.com/clients/esport-role"
    "golang.52tt.com/clients/guild"
    im_api "golang.52tt.com/clients/im-api"
    pushNotification "golang.52tt.com/clients/push-notification/v2"
    sendIm "golang.52tt.com/clients/sendim"
    "golang.52tt.com/clients/seqgen/v2"
    timeline "golang.52tt.com/clients/timeline"
    "golang.52tt.com/protocol/services/esport_hall"
    trade_im_notify "golang.52tt.com/services/tt-rev/esport/common/trade-im-notify"
)

// go generate ./...
//
//go:generate mockgen -destination=../../../../../../clients/mocks/esport-hall/iclient.go -mock_names IClient=MockEsportHallClient -package=esport_hall golang.52tt.com/protocol/services/esport_hall EsportHallClient
//go:generate mockgen -destination=../../../../../../clients/mocks/esport-role/iclient.go -mock_names IClient=MockIClient -package=esport_role golang.52tt.com/clients/esport-role IClient
//go:generate mockgen -destination=mock_god_level.go -package=rpc golang.52tt.com/protocol/services/esport_godlevel ESportGodLevelServiceClient
//go:generate mockgen -destination=mock_trade_im_notify.go -package=rpc golang.52tt.com/services/tt-rev/esport/common/trade-im-notify ITradeImNotify
//go:generate mockgen -destination=../../../../../../clients/mocks/esport-hall/iclient.go -mock_names IClient=MockEsportHallClient -package=esport_hall golang.52tt.com/clients/esport-hall IClient
type Client struct {
    AccountCli        accountClient.IClient
    SendImCli         sendIm.IClient
    CensoringProxyCli censoringProxy.IClient
    PushCli           pushNotification.IClient
    TradeImService    trade_im_notify.ITradeImNotify
    EsportHallCli     esport_hall.EsportHallClient
    GuildCli          guild.IClient
    EsportRoleCli     esport_role.IClient
    GodLevelCli       esport_godlevel.ESportGodLevelServiceClient
    UserGroupCli      *user_group.UserGroupCli
}

func NewClient(bc conf.IBusinessConfManager) (*Client, error) {
    ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
    defer cancel()
    accountCli, err := accountClient.NewClient()
    if err != nil {
        return nil, err
    }

    sendImCli := sendIm.NewClient()
    censoringProxyCli := censoringProxy.NewIClient()
    pushCli := pushNotification.NewIClient()

    seqgenClient := seqgen.NewIClient()
    timelineClient := timeline.NewIClient()
    imClient := im_api.NewIClient()
    tradeImService := trade_im_notify.NewTradeImNotify(accountCli, seqgenClient, timelineClient, imClient)
    esportMallClient, _ := esport_hall.NewClient(ctx)
    esportRoleCli := esport_role.NewIClient()
    guildCli := guild.NewIClient()

    godlevelCli, err := esport_godlevel.NewClient(ctx)
    if err != nil {
        return nil, err
    }

    userGroupCfg := bc.GetUserGroupServerCfg()
    userGroupCli := user_group.NewUserGroupCli(
        userGroupCfg.DspLpmAdminHost,
        userGroupCfg.DspLpmOfflineGroupHost,
        userGroupCfg.DspLpmApiserverHost)

    return &Client{
        AccountCli:        accountCli,
        SendImCli:         sendImCli,
        CensoringProxyCli: censoringProxyCli,
        PushCli:           pushCli,
        TradeImService:    tradeImService,
        EsportHallCli:     esportMallClient,
        GuildCli:          guildCli,
        EsportRoleCli:     esportRoleCli,
        GodLevelCli:       godlevelCli,
        UserGroupCli:      userGroupCli,
    }, nil
}

func (c *Client) Close() {
    c.SendImCli.Close()
    c.AccountCli.Close()
    c.CensoringProxyCli.Close()
}
