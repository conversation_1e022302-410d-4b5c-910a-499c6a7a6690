package mgr

import (
    "context"
    "fmt"
    "go.opentelemetry.io/otel/codes"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    pb "golang.52tt.com/protocol/services/esport-skill"
    "golang.52tt.com/protocol/services/esport_hall"
    "golang.52tt.com/protocol/services/esport_role"
    imApiPb "golang.52tt.com/protocol/services/im-api"
    "golang.52tt.com/services/tt-rev/esport/esport-skill/internal/store"
    "strings"
    "time"
)

func (m *Manager) GetUserSkillFreezeStatus(ctx context.Context, in *pb.GetUserSkillFreezeStatusRequest) (*pb.GetUserSkillFreezeStatusResponse, error) {
    freezeMap, err := m.store.GetUserSkillFreezeStatus(ctx, in.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserSkillFreezeStatus err: %v", err)
        return nil, err
    }

    out := &pb.GetUserSkillFreezeStatusResponse{
        GameStatusMap: make(map[uint32]*pb.SkillFreezeStatus, len(freezeMap)),
    }
    for _, freeze := range freezeMap {
        out.GameStatusMap[freeze.GameId] = &pb.SkillFreezeStatus{
            GameId:       freeze.GameId,
            FreezeType:   pb.FreezeType(freeze.FreezeType),
            FreezeStopTs: freeze.FreezeStopTs,
        }
    }
    return out, nil
}

func (m *Manager) BatGetUserSkillFreezeStatus(ctx context.Context, in *pb.BatGetUserSkillFreezeStatusRequest) (*pb.BatGetUserSkillFreezeStatusResponse, error) {
    freezeList, err := m.store.BatGetUserSkillFreezeStatus(ctx, in.GetUidList())
    if err != nil {
        log.ErrorWithCtx(ctx, "BatGetUserSkillFreezeStatus err: %v", err)
        return nil, err
    }

    userMap := make(map[uint32]*pb.SkillFreezeStatusMap)
    for _, freeze := range freezeList {
        user := userMap[freeze.Uid]
        if user == nil {
            user = &pb.SkillFreezeStatusMap{GameStatusMap: make(map[uint32]*pb.SkillFreezeStatus)}
        }
        user.GameStatusMap[freeze.GameId] = &pb.SkillFreezeStatus{
            GameId:       freeze.GameId,
            FreezeType:   pb.FreezeType(freeze.FreezeType),
            FreezeStopTs: freeze.FreezeStopTs,
        }
        userMap[freeze.Uid] = user
    }

    return &pb.BatGetUserSkillFreezeStatusResponse{UserMap: userMap}, nil
}

func (m *Manager) GetSkillFreezeOperationList(ctx context.Context, in *pb.GetSkillFreezeOperationListRequest) (*pb.GetSkillFreezeOperationListResponse, error) {
    historyList, err := m.store.SearchUserSkillFreezeHistory(ctx, in.UidList, in.GuildId, in.GameId, in.Page, in.PageSize)
    if err != nil {
        log.ErrorWithCtx(ctx, "SearchUserSkillFreezeHistory err: %v", err)
        return nil, err
    }

    count, err := m.store.SearchUserSkillFreezeHistoryCnt(ctx, in.UidList, in.GuildId, in.GameId)
    if err != nil {
        log.ErrorWithCtx(ctx, "SearchUserSkillFreezeHistoryCnt err: %v", err)
        return nil, err
    }

    operationList := make([]*pb.SkillFreezeOperation, 0, len(historyList))
    for _, history := range historyList {
        operationList = append(operationList, &pb.SkillFreezeOperation{
            Uid:          history.Uid,
            GuildId:      history.GuildId,
            GameIdList:   history.GameIdList,
            OpUser:       history.OpUser,
            OpTs:         history.OpTs,
            FreezeType:   pb.FreezeType(history.FreezeType),
            FreezeStopTs: history.FreezeStopTs,
            FreezeReason: history.FreezeReason,
        })
    }

    return &pb.GetSkillFreezeOperationListResponse{
        OperationList: operationList,
        Total:         int32(count),
    }, nil
}

func (m *Manager) FreezeCoachSkill(ctx context.Context, in *pb.FreezeCoachSkillRequest) error {
    log.InfoWithCtx(ctx, "FreezeCoachSkill in: %+v", in)

    // 如果相关技能被冻结过了，返回错误，避免误操作
    freezeMap, err := m.store.GetUserSkillFreezeStatus(ctx, in.GetUid())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserSkillFreezeStatus err: %v", err)
        return err
    }
    for _, gameId := range in.GameIdList {
        if _, ok := freezeMap[gameId]; ok {
            err = protocol.NewExactServerError(codes.Ok, status.ErrRequestParamInvalid, "技能已被冻结，请勿重复冻结")
            log.ErrorWithCtx(ctx, "FreezeCoachSkill invalid param: %v", err)
            return err
        }
    }

    // 写入冻结状态
    freezeList := make([]*store.UserSkillFreezeStatusEntity, len(in.GameIdList))
    for i, gameId := range in.GameIdList {
        freezeList[i] = &store.UserSkillFreezeStatusEntity{
            Uid:          in.Uid,
            GameId:       gameId,
            FreezeType:   uint32(in.FreezeType),
            FreezeStopTs: in.FreezeStopTs,
        }
    }
    err = m.store.BatUpsertUserSkillFreezeStatus(ctx, freezeList)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatUpsertUserSkillFreezeStatus err: %v", err)
        return err
    }

    // 关掉接单开关
    for _, gameId := range in.GameIdList {
        // 直接暴力循环，不搞批量接口了，没那么多
        _, setErr := m.rpc.EsportHallCli.SetSkillReceiveSwitch(ctx, &esport_hall.SetSkillReceiveSwitchRequest{
            Uid:     in.GetUid(),
            SkillId: gameId,
            Switch:  false,
        })
        // 如果用户没该技能，接口会返回错误，但是不影响业务
        if setErr != nil {
            log.WarnWithCtx(ctx, "SetSkillReceiveSwitch err: %v", setErr)
        }
    }

    // 发大神公众号消息
    err = m.sendFreezeMsg(ctx, in)
    if err != nil {
        return err
    }

    // 拿教练的公会信息写入，只是为了方便后面做检索
    roleRsp, err := m.rpc.EsportRoleCli.GetUserESportRole(ctx, &esport_role.GetUserESportRoleReq{Uid: in.GetUid()})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserESportRole err: %v", err)
        return err
    }
    guildId := roleRsp.GetGuildId()

    // 写入操作记录
    err = m.store.AddUserSkillFreezeHistory(ctx, &store.UserSkillFreezeHistoryEntity{
        Uid:          in.GetUid(),
        GuildId:      guildId,
        GameIdList:   in.GetGameIdList(),
        OpUser:       in.GetOpUser(),
        OpTs:         time.Now().Unix(),
        FreezeType:   uint32(in.GetFreezeType()),
        FreezeStopTs: in.GetFreezeStopTs(),
        FreezeReason: in.GetFreezeReason(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "AddUserSkillFreezeHistory err: %v", err)
        return err
    }

    return nil
}

func (m *Manager) UnfreezeCoachSkill(ctx context.Context, in *pb.UnfreezeCoachSkillRequest) error {
    log.InfoWithCtx(ctx, "UnfreezeCoachSkill in: %+v", in)

    // 删除冻结状态
    err := m.store.DeleteUserSkillFreezeStatus(ctx, in.GetUid(), in.GetGameIdList())
    if err != nil {
        log.ErrorWithCtx(ctx, "DeleteUserSkillFreezeStatus err: %v", err)
        return err
    }

    // 发大神公众号消息
    err = m.sendUnfreezeMsg(ctx, in.GetUid(), in.GetGameIdList())
    if err != nil {
        return err
    }

    // 拿教练的公会信息写入，只是为了方便后面做检索
    roleRsp, err := m.rpc.EsportRoleCli.GetUserESportRole(ctx, &esport_role.GetUserESportRoleReq{Uid: in.GetUid()})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserESportRole err: %v", err)
        return err
    }
    guildId := roleRsp.GetGuildId()

    // 写入操作记录
    err = m.store.AddUserSkillFreezeHistory(ctx, &store.UserSkillFreezeHistoryEntity{
        Uid:        in.GetUid(),
        GuildId:    guildId,
        GameIdList: in.GetGameIdList(),
        OpUser:     in.GetOpUser(),
        OpTs:       time.Now().Unix(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "AddUserSkillFreezeHistory err: %v", err)
        return err
    }

    return nil
}

func (m *Manager) runTaskHandleExpiredFreeze() {
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()

    expiredList, err := m.store.GetExpiredUserSkillFreezeStatus(ctx, time.Now().Unix())
    if err != nil {
        log.ErrorWithCtx(ctx, "GetExpiredUserSkillFreezeStatus err: %v", err)
        return
    }

    for _, expired := range expiredList {
        m.handleExpireFreeze(ctx, expired.Uid)
    }
}

func (m *Manager) handleExpireFreeze(ctx context.Context, uid uint32) {
    // 先查询有哪些技能到期了，因为产品要求一起发通知
    freezeList, err := m.store.GetUserSkillFreezeStatus(ctx, uid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserSkillFreezeStatus err: %v", err)
        return
    }
    expireGameList := make([]uint32, 0, len(freezeList))
    now := time.Now().Unix()
    for _, freeze := range freezeList {
        if freeze.FreezeType == uint32(pb.FreezeType_FREEZE_TYPE_TO_TIME) && freeze.FreezeStopTs < now {
            expireGameList = append(expireGameList, freeze.GameId)
        }
    }
    if len(expireGameList) == 0 {
        return
    }
    log.InfoWithCtx(ctx, "handleExpireFreeze uid: %d, expireGameList: %v", uid, expireGameList)

    // 删除冻结状态
    err = m.store.DeleteUserSkillFreezeStatus(ctx, uid, expireGameList)
    if err != nil {
        log.ErrorWithCtx(ctx, "DeleteUserSkillFreezeStatus err: %v", err)
        return
    }

    // 发大神公众号消息
    err = m.sendUnfreezeMsg(ctx, uid, expireGameList)
    if err != nil {
        return
    }
}

func (m *Manager) sendFreezeMsg(ctx context.Context, in *pb.FreezeCoachSkillRequest) error {
    gameNameList := make([]string, 0, len(in.GameIdList))
    for _, gameId := range in.GameIdList {
        gameNameList = append(gameNameList, m.localCache.GetGameNameById(gameId))
    }

    timeStr := "永久"
    if in.GetFreezeType() == pb.FreezeType_FREEZE_TYPE_TO_TIME {
        timeStr = time.Unix(in.GetFreezeStopTs(), 0).In(time.Local).Format("2006年01月02日-15:04:05")
    }

    msg := fmt.Sprintf("您的%s技能已被冻结，当前不可接单该技能，冻结日期至%s，冻结原因：%s",
        strings.Join(gameNameList, "、"), timeStr, in.GetFreezeReason())
    log.DebugWithCtx(ctx, "sendFreezeMsg msg: %s", msg)

    err := m.tradeImService.SendOfficialAccountMsg(ctx, "esport-skill", in.GetUid(), &imApiPb.Text{Content: msg})
    if err != nil {
        log.ErrorWithCtx(ctx, "SendOfficialAccountMsg err: %v", err)
    }
    return err
}

func (m *Manager) sendUnfreezeMsg(ctx context.Context, uid uint32, gameIdList []uint32) error {
    gameNameList := make([]string, 0, len(gameIdList))
    for _, gameId := range gameIdList {
        gameNameList = append(gameNameList, m.localCache.GetGameNameById(gameId))
    }

    msg := fmt.Sprintf("您的%s技能冻结已解除，需在大神页开启技能接单，才可正常接单哦~去开启>>", strings.Join(gameNameList, "、"))
    log.DebugWithCtx(ctx, "sendUnfreezeMsg msg: %s", msg)

    err := m.tradeImService.SendOfficialAccountMsg(ctx, "esport-skill", uid, &imApiPb.Text{
        Content:   msg,
        Highlight: "去开启>>",
        Url:       m.bc.GetOpenSkillUrl(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SendOfficialAccountMsg err: %v", err)
    }
    return err
}
