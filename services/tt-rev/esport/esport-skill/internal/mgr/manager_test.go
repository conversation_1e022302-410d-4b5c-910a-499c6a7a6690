package mgr

import (
    "github.com/golang/mock/gomock"
    esportHallMock "golang.52tt.com/protocol/services/esport_hall"
    "golang.52tt.com/services/tt-rev/esport/esport-skill/internal/mocks"
    "golang.52tt.com/services/tt-rev/esport/esport-skill/internal/rpc"
    "testing"
)

type mgrHelperForTest struct {
    *Manager
}

func newMgrHelperForTest(t *testing.T) *mgrHelperForTest {
    controller := gomock.NewController(t)

    mocks.NewTestMockMgr(controller)

    return &mgrHelperForTest{
        &Manager{
            store:            mocks.MockStore,
            cache:            mocks.MockCache,
            rpc:              mocks.MockRpcCli,
            bc:               mocks.MockBc,
            tradeImService:   rpc.NewMockITradeImNotify(controller),
            IsImAsynchronous: false,
        },
    }
}

func (receiver *mgrHelperForTest) getStore() *mocks.MockIStore {
    return receiver.store.(*mocks.MockIStore)
}

func (receiver *mgrHelperForTest) getEsportHallCli() *esportHallMock.MockEsportHallClient {
    return receiver.rpc.EsportHallCli.(*esportHallMock.MockEsportHallClient)
}

func (receiver *mgrHelperForTest) getCache() *mocks.MockICache {
    return receiver.cache.(*mocks.MockICache)
}

func (receiver *mgrHelperForTest) getBC() *mocks.MockIBusinessConfManager {
    return receiver.bc.(*mocks.MockIBusinessConfManager)
}

func (receiver *mgrHelperForTest) getTradeImService() *rpc.MockITradeImNotify {
    return receiver.tradeImService.(*rpc.MockITradeImNotify)
}
