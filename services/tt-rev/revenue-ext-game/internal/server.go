package internal

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/demo/echo"
	revenue_ext_game "golang.52tt.com/protocol/services/revenue-ext-game"
	"golang.52tt.com/services/tt-rev/revenue-ext-game/internal/conf"
	"golang.52tt.com/services/tt-rev/revenue-ext-game/internal/entity"
	"golang.52tt.com/services/tt-rev/revenue-ext-game/internal/event"
	"golang.52tt.com/services/tt-rev/revenue-ext-game/internal/kfk_produce"
	"golang.52tt.com/services/tt-rev/revenue-ext-game/internal/mgr"
    "os"
    "time"

	"golang.52tt.com/services/tt-rev/revenue-ext-game/internal/cache"

	"golang.52tt.com/services/tt-rev/revenue-ext-game/internal/store"
)

func NewServer(ctx context.Context, cfg *conf.StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	cache_, err := cache.NewCache(ctx, cfg.RedisConfig)
	if nil != err {
		log.ErrorWithCtx(ctx, "init redis fail, err: %v", err)
		return nil, err
	}

	store_, err := store.NewStore(ctx, cfg.MysqlConfig)
	if nil != err {
		log.ErrorWithCtx(ctx, "init store fail, err: %v", err)
		return nil, err
	}

	bc, err := conf.NewBusinessConfManager()
	if err != nil {
		return nil, err
	}

	kfkMountProduce, err := kfk_produce.NewKafkaProduce(cfg.ExtGameMountKafka.BrokerList(), cfg.ExtGameMountKafka.ClientID, cfg.ExtGameMountKafka.Topics)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to NewKafkaProduce %v", err)
		return nil, err
	}

	manger, err := mgr.NewMgr(ctx, cache_, store_, bc, kfkMountProduce)
	if err != nil {
		log.Errorf("mgr.NewMgr fail,err:%v", err)
		return nil, err
	}

	kfkSub, err := event.NewKafkaEvent(cfg, manger, bc)
	if err != nil {
		return nil, err
	}

	svr := &Server{
		mgr:    manger,
		kfkSub: kfkSub,
	}
	return svr, nil
}

type Server struct {
	kfkSub *event.KafkaEvent
	mgr    mgr.IMgr
}

func (svr *Server) ShutDown() {
	svr.mgr.Close()
	svr.kfkSub.Shutdown()
}

func (svr *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

func (svr *Server) MountExtGame(ctx context.Context, req *revenue_ext_game.MountExtGameReq) (*revenue_ext_game.MountExtGameResp, error) {
	out := &revenue_ext_game.MountExtGameResp{}

	serial, err := svr.mgr.DoChannelMountGameHandle(ctx, uint32(req.GetGameType()), req.GetChannelId(), req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "MountExtGame.req:%+v,err:%v", req, err)
		return out, err
	}

	out.Serial = serial
	log.InfoWithCtx(ctx, "MountExtGame req:%+v,resp:%+v", req, out)
	return out, nil
}

func (svr *Server) GetMountExtGame(ctx context.Context, req *revenue_ext_game.GetMountExtGameReq) (*revenue_ext_game.GetMountExtGameResp, error) {
	resp := &revenue_ext_game.GetMountExtGameResp{}

	gameType, err := svr.mgr.GetChannelMountGame(ctx, req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMountExtGame fail to GetChannelMountGame. req:%+v, err:%v", req, err)
		return resp, err
	}

	resp.GameType = revenue_ext_game.ExtGameType(gameType)
	return resp, nil
}

func (svr *Server) BatchGetMountExtGame(ctx context.Context, req *revenue_ext_game.BatchGetMountExtGameReq) (*revenue_ext_game.BatchGetMountExtGameResp, error) {
	resp := &revenue_ext_game.BatchGetMountExtGameResp{}

	cid2Game, err := svr.mgr.BatchGetChannelMountGame(ctx, req.GetChannelIdList())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetMountExtGame fail to BatchGetChannelMountGame. req:%+v, err:%v", req, err)
		return resp, err
	}

	resp.CidToGame = cid2Game
	return resp, nil
}

func (svr *Server) UnmountExtGame(ctx context.Context, req *revenue_ext_game.UnmountExtGameReq) (*revenue_ext_game.UnmountExtGameResp, error) {
	out := &revenue_ext_game.UnmountExtGameResp{}

	err := svr.mgr.DoChannelUnmountGameHandle(ctx, req.GetChannelId(), req.GetGameType(), req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "UnmountExtGame.req:%+v,err:%v", req, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "UnmountExtGame req:%+v,resp:%+v", req, out)
	return out, nil
}

func (svr *Server) GetChannelBySerial(ctx context.Context, req *revenue_ext_game.GetChannelBySerialReq) (*revenue_ext_game.GetChannelBySerialResp, error) {
	out := &revenue_ext_game.GetChannelBySerialResp{}
	cid, err := svr.mgr.GetChannelBySerial(ctx, uint32(req.GetGameType()), req.GetSerial())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelBySerial fail to GetChannelBySerial. req:%+v, err:%v", req, err)
		return out, err
	}

	out.ChannelId = cid
	return out, nil
}

func (svr *Server) StartDataReport(ctx context.Context, req *revenue_ext_game.StartDataReportReq) (*revenue_ext_game.StartDataReportResp, error) {
	out := &revenue_ext_game.StartDataReportResp{}

	err := svr.mgr.DoStartDataReportTask(ctx, req.GetChannelId(), uint32(req.GetGameType()), req.GetTaskTypeList())
	if err != nil {
		log.ErrorWithCtx(ctx, "StartDataReport.req:%+v,err:%v", req, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "StartDataReport req:%+v,resp:%+v", req, out)
	return out, nil
}

func (svr *Server) StopDataReport(ctx context.Context, req *revenue_ext_game.StopDataReportReq) (*revenue_ext_game.StopDataReportResp, error) {
	out := &revenue_ext_game.StopDataReportResp{}

	err := svr.mgr.DoStopDataReportTask(ctx, req.GetChannelId(), uint32(req.GetGameType()), req.GetTaskTypeList())
	if err != nil {
		log.ErrorWithCtx(ctx, "StopDataReport.req:%+v,err:%v", req, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "StopDataReport req:%+v,resp:%+v", req, out)
	return out, nil
}

func (svr *Server) GetDataReportTaskStatus(ctx context.Context, req *revenue_ext_game.GetDataReportTaskStatusReq) (*revenue_ext_game.GetDataReportTaskStatusResp, error) {
	out := &revenue_ext_game.GetDataReportTaskStatusResp{}

	status, err := svr.mgr.GetDataReportTaskStatus(ctx, req.GetChannelId(), uint32(req.GetGameType()), req.GetTaskType())
	if err != nil {
		log.ErrorWithCtx(ctx, "StopDataReport.req:%+v,err:%v", req, err)
		return out, err
	}
	out.Status = status

	log.Debugf("GetDataReportTaskStatus req:%+v,resp:%+v", req, out)
	return out, nil
}

func (svr *Server) ManualDataReportDemo(ctx context.Context, req *revenue_ext_game.ManualDataReportDemoReq) (*revenue_ext_game.ManualDataReportDemoResp, error) {
	out := &revenue_ext_game.ManualDataReportDemoResp{}

    if os.Getenv("MY_CLUSTER") != "testing" {
        log.ErrorWithCtx(ctx, "ManualDataReportDemo is only allowed in testing environment, MY_CLUSTER: %s", os.Getenv("MY_CLUSTER"))
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "手动数据上报仅限测试环境")
    }

    gameType, err := svr.mgr.GetChannelMountGame(ctx, req.GetChannelId())
    if err != nil {
        log.ErrorWithCtx(ctx, "ManualDataReportDemo fail to GetChannelMountGame. req:%+v, err:%v", req, err)
        return out, err
    }
    if gameType == 0 {
        log.ErrorWithCtx(ctx, "ManualDataReportDemo gameType is 0, req:%+v", req)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "游戏未挂载")
    }

	now := time.Now().UnixNano()
	switch req.GetMsgType() {
	case cache.TaskTypeGift:
		return out, svr.mgr.DataReportGift(ctx, req.GetChannelId(), gameType, []*entity.GiftData{
			{
				MsgId:     fmt.Sprintf("%d", now),
				SecOpenId: "opid001",
				SecGiftId: req.GetGiftInfo().GetGiftId(),
				GiftNum:   req.GetGiftInfo().GetGiftAmount(),
				GiftValue: req.GetGiftInfo().GetGiftPrice(),
				Nickname:  "测试用户",
				Timestamp: time.Now().UnixNano() / 1e6,
			}})
	case cache.TaskTypeComment:
		return out, svr.mgr.DataReportComment(ctx, req.GetChannelId(), gameType, []*entity.Comment{
			{
				MsgId:     fmt.Sprintf("%d", now),
				SecOpenId: "opid001",
				Content:   req.GetPublicText(),
				Nickname:  "测试用户",
				Timestamp: time.Now().UnixNano() / 1e6,
			}})
	default:
		return out, nil
	}
}

func (svr *Server) GetChannelExtGameAccessStatus(ctx context.Context, req *revenue_ext_game.GetChannelExtGameAccessStatusReq) (
	*revenue_ext_game.GetChannelExtGameAccessStatusResp, error) {
	out := &revenue_ext_game.GetChannelExtGameAccessStatusResp{}

	accessStatus, err := svr.mgr.CheckChannelExtGameAccess(ctx, req.GetChannelId())
	out.AccessStatus = accessStatus
	return out, err
}

func (svr *Server) SetExtGameOpCfg(ctx context.Context, req *revenue_ext_game.SetExtGameOpCfgReq) (*revenue_ext_game.SetExtGameOpCfgResp, error) {
	out := &revenue_ext_game.SetExtGameOpCfgResp{}
	return out, svr.mgr.SetExtGameOpCfg(ctx, req)
}

func (svr *Server) ReportGameEnd(ctx context.Context, req *revenue_ext_game.ReportGameEndReq) (*revenue_ext_game.ReportGameEndResp, error) {
	out := &revenue_ext_game.ReportGameEndResp{}
	return out, svr.mgr.ReportGameEnd(ctx, req)
}

func (svr *Server) SetUserCamp(ctx context.Context, req *revenue_ext_game.SetUserCampReq) (*revenue_ext_game.SetUserCampResp, error) {
	out := &revenue_ext_game.SetUserCampResp{}
	return out, svr.mgr.SetUserCamp(ctx, req)
}

func (svr *Server) GetUserExtGameInfo(ctx context.Context, req *revenue_ext_game.GetUserExtGameInfoReq) (*revenue_ext_game.GetUserExtGameInfoResp, error) {
	return svr.mgr.GetUserExtGameInfo(ctx, req.GetChannelId(), req.GetUid())
}

func (svr *Server) GetExtGameCfgList(ctx context.Context, req *revenue_ext_game.GetExtGameCfgListReq) (*revenue_ext_game.GetExtGameCfgListResp, error) {
	out := &revenue_ext_game.GetExtGameCfgListResp{}
	var err error

	out.ConfList, err = svr.mgr.GetExtGameCfgList(ctx, req.GetChannelId(), req.GetUid())
	return out, err
}

func (svr *Server) ReportUserWantPlay(ctx context.Context, req *revenue_ext_game.ReportUserWantPlayReq) (*revenue_ext_game.ReportUserWantPlayResp, error) {
	return &revenue_ext_game.ReportUserWantPlayResp{}, svr.mgr.RecordUserWantPlay(ctx, req)
}

func (svr *Server) BatSetChannelExtGameAccessConf(ctx context.Context, req *revenue_ext_game.BatSetChannelExtGameAccessConfReq) (
	*revenue_ext_game.BatSetChannelExtGameAccessConfResp, error) {
	out := &revenue_ext_game.BatSetChannelExtGameAccessConfResp{}

	failList, err := svr.mgr.BatSetChannelExtGameAccessConf(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatSetChannelExtGameAccessConf fail,req:%+v,err:%+v", req, err)
		return out, err
	}

	out.TimeConflictCidList = failList
	log.InfoWithCtx(ctx, "BatSetChannelExtGameAccessConf success,req:%+v,failList:%+v", req, failList)
	return out, nil
}

func (svr *Server) GetChannelExtGameAccessConf(ctx context.Context, req *revenue_ext_game.GetChannelExtGameAccessConfReq) (
	*revenue_ext_game.GetChannelExtGameAccessConfResp, error) {
	var out *revenue_ext_game.GetChannelExtGameAccessConfResp
	var err error

	if req.GetChannelId() != 0 {
		// 按channelId 检索
		out, err = svr.mgr.SearchChannelAccessByChannelId(ctx, req.GetChannelId())

	} else if req.GetUid() != 0 {
		// 按uid检索
		out, err = svr.mgr.SearchChannelAccessByUid(ctx, req.GetUid())

	} else {
		// 分页查询
		if req.GetPageLimit() == 0 {
			log.ErrorWithCtx(ctx, "GetChannelExtGameAccessConf, req.GetPageLimit() ==0")
			return &revenue_ext_game.GetChannelExtGameAccessConfResp{}, protocol.NewExactServerError(nil, status.ErrParam)
		}
		out, err = svr.mgr.GetChannelExtGameAccessRecords(ctx, req)

	}
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelExtGameAccessConf err, req:%+v,err:%+v")
		return out, err
	}

	return out, nil
}

func (svr *Server) BatDelChannelExtGameAccess(ctx context.Context, req *revenue_ext_game.BatDelChannelExtGameAccessReq) (
	*revenue_ext_game.BatDelChannelExtGameAccessResp, error) {
	out := &revenue_ext_game.BatDelChannelExtGameAccessResp{}

	if len(req.GetConfIdList()) == 0 {
		log.WarnWithCtx(ctx, "BatDelChannelExtGameAccess len(req.GetConfIdList()) == 0")
		return out, nil
	}

	err := svr.mgr.BatDelChannelExtGameAccess(ctx, req.GetConfIdList())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatDelChannelExtGameAccess fail at mgr,req:%+v,err:%v", req, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "BatDelChannelExtGameAccess success,condIdList:%+v", req.GetConfIdList())
	return out, nil
}

func (svr *Server) UpdateChannelExtGameAccessConf(ctx context.Context, req *revenue_ext_game.UpdateChannelExtGameAccessConfReq) (
	*revenue_ext_game.UpdateChannelExtGameAccessConfResp, error) {
	err := svr.mgr.UpdateChannelExtGameAccessConf(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateChannelExtGameAccessConf fail at mgr,req:%+v,err:%v", req, err)
		return &revenue_ext_game.UpdateChannelExtGameAccessConfResp{}, err
	}

	log.InfoWithCtx(ctx, "UpdateChannelExtGameAccessConf success,req:%+v", req)
	return &revenue_ext_game.UpdateChannelExtGameAccessConfResp{}, nil
}

func (svr *Server) GetExtGameRankNameplate(ctx context.Context, req *revenue_ext_game.GetExtGameRankNameplateReq) (*revenue_ext_game.GetExtGameRankNameplateResp, error) {
	return svr.mgr.GetUserRankNameplate(ctx, req)
}

func (svr *Server) GetExtGameScoreRank(ctx context.Context, req *revenue_ext_game.GetExtGameScoreRankReq) (*revenue_ext_game.GetExtGameScoreRankResp, error) {
	return svr.mgr.GetGameRank(ctx, req)
}

func (svr *Server) GetExtGameRankHonorInfo(ctx context.Context, req *revenue_ext_game.GetExtGameRankHonorInfoReq) (*revenue_ext_game.GetExtGameRankHonorInfoResp, error) {
	return svr.mgr.GetGameRankHonorInfo(ctx, req)
}

func (svr *Server) SetGameScoresRank(ctx context.Context, req *revenue_ext_game.SetGameScoresRankReq) (*revenue_ext_game.SetGameScoresRankResp, error) {
	out := &revenue_ext_game.SetGameScoresRankResp{}
	log.InfoWithCtx(ctx, "SetGameScoresRank req:%+v", req)
	return out, svr.mgr.SetGameScoresRank(ctx, req)
}

func (svr *Server) StartChatPk(ctx context.Context, req *revenue_ext_game.StartChatPkReq) (*revenue_ext_game.StartChatPkResp, error) {
	return &revenue_ext_game.StartChatPkResp{}, svr.mgr.StartChatPk(ctx, req)
}

func (svr *Server) StopChatPk(ctx context.Context, req *revenue_ext_game.StopChatPkReq) (*revenue_ext_game.StopChatPkResp, error) {
	return &revenue_ext_game.StopChatPkResp{}, svr.mgr.StopChatPk(ctx, req.GetGameType(), req.GetPkId())
}

func (svr *Server) ChatPkHeartbeat(ctx context.Context, req *revenue_ext_game.ChatPkHeartbeatReq) (*revenue_ext_game.ChatPkHeartbeatResp, error) {
	return &revenue_ext_game.ChatPkHeartbeatResp{}, svr.mgr.ChatPkHeartbeat(ctx, req.GetGameType(), req.GetPkId())
}
