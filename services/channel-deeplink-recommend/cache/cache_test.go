package cache

import (
	"github.com/go-redis/redis"
	tracing "golang.52tt.com/pkg/tracing/jaeger"
	sconf "golang.52tt.com/services/channel-live-mission/conf"
	"testing"

	//"golang.52tt.com/services/channel-live-mission/server"
	//"testing"
)

var (
	sc          *sconf.ServiceConfigT
	cacheClient *ChannelDeeplinkRecommendCache
	redisClient *redis.Client
)

func init() {
	sc = &sconf.ServiceConfigT{}
	err := sc.Parse("../channel-deeplink-recommend.json")
	if err != nil {
		return
	}

	redisClient = redis.NewClient(&redis.Options{
		Network:            sc.GetRedisConfig().Protocol,
		Addr:               sc.GetRedisConfig().Addr(),
		PoolSize:           sc.GetRedisConfig().PoolSize,
		IdleCheckFrequency: sc.GetRedisConfig().IdleCheckFrequency(),
		DB:                 sc.GetRedisConfig().DB,
	})
	redisTracer := tracing.Init("channel-live-mission_redis")
	cacheClient = NewChannelDeeplinkRecommendCache(redisClient, redisTracer)
}

func Test_TTL(t *testing.T) {
	err := redisClient.Set("test", 1, 0).Err()
	if err != nil {
		t.Error(err)
	}

	val := redisClient.TTL("test").Val()
	t.Log(val)
}

func TestChannelDeeplinkRecommendCache_SetLivingChannel(t *testing.T) {
	t.Log(cacheClient.SetLivingChannel(1))
	t.Log(cacheClient.SetLivingChannel(2))
}

func TestChannelDeeplinkRecommendCache_BatchGetLivingChannel(t *testing.T) {
	t.Log(cacheClient.BatchGetLivingChannel([]uint32{1, 2}))
}

func TestChannelDeeplinkRecommendCache_GetChannelDiversion(t *testing.T) {
	t.Log(cacheClient.GetChannelDiversion(4))
}