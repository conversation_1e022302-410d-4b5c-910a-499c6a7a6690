package anti_corruption_layer

//go:generate quicksilver-cli test interface ../anti-corruption-layer
//go:generate mockgen -destination=./mocks/anti_corruption_layer.go -package=mocks golang.52tt.com/services/channel-wedding-minigame/internal/model/anti-corruption-layer IMgr

import (
    "context"
    "errors"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    "gitlab.ttyuyin.com/bizFund/bizFund/pkg/protocol"
    channelMic "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic"
    micMiddle "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic_middle"
    micScheme "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_scheme_middle"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/clients/account"
    apicenter "golang.52tt.com/clients/apicenter/apiserver"
    backpacksender "golang.52tt.com/clients/backpack-sender"
    currencyClient "golang.52tt.com/clients/currency"
    magic_spirit "golang.52tt.com/clients/magic-spirit"
    presend_middleware_client "golang.52tt.com/clients/present-middleware"
    PushNotification "golang.52tt.com/clients/push-notification/v2"
    push "golang.52tt.com/clients/push-notification/v2"
    sendIm "golang.52tt.com/clients/sendim"
    unifiedPay "golang.52tt.com/clients/unified_pay"
    userProfileApi "golang.52tt.com/clients/user-profile-api"
    userPresent "golang.52tt.com/clients/userpresent"
    "golang.52tt.com/pkg/bylink"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app"
    channelPB "golang.52tt.com/protocol/app/channel"
    "golang.52tt.com/protocol/app/channel_wedding_logic"
    pushPb "golang.52tt.com/protocol/app/push"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    pb "golang.52tt.com/protocol/services/channel-wedding-minigame"
    pushPB "golang.52tt.com/protocol/services/push-notification/v2"
    sendim "golang.52tt.com/protocol/services/sendimsvr"
    "golang.52tt.com/services/channel-wedding-minigame/internal/conf"
    "strings"
    "time"
)

const (
    ChairGameMicIdBegin = 12 // 抢椅子游戏麦位起始id
    ChairGameMicIdEnd   = 16 // 抢椅子游戏麦位结束id
)

type Mgr struct {
    shutDown          chan struct{}
    sendImCli         sendIm.IClient
    backpackSenderCli backpacksender.IClient
    micMiddleCli      micMiddle.ChannelMicMiddleClient
    userProfile       userProfileApi.IClient
    micSchemeCli      micScheme.ChannelSchemeMiddleClient
    channelMicCli     channelMic.ChannelMicClient
    pushCli           push.IClient
    unifiedPayCli     unifiedPay.IClient
    userPresentCli    userPresent.IClient
    accountCli        account.IClient
    bc                conf.IBusinessConfManager

    magicSpiritCli    magic_spirit.IClient
    currencyCli       currencyClient.IClient
    apicenterCli      apicenter.IClient
    presentMiddle     presend_middleware_client.IClient
    channelWeddingCli channel_wedding.ChannelWeddingClient
}

// NewMgr 活动配置模块
func NewMgr(bc conf.IBusinessConfManager) (*Mgr, error) {
    sendImCli := sendIm.NewClient()
    pushCli, _ := push.NewClient()
    backpackSenderCli, _ := backpacksender.NewClient()
    micMiddleCli := micMiddle.MustNewClient(context.Background())
    micSchemeCli := micScheme.MustNewClient(context.Background())
    channelMicCli := channelMic.MustNewClient(context.Background())
    userProfile, _ := userProfileApi.NewClient()

    unifiedPayCli, _ := unifiedPay.NewClient()
    userPresentCli := userPresent.NewClient()
    accountCli, _ := account.NewClient()
    magicSpiritCli, _ := magic_spirit.NewClient()
    currencyCli := currencyClient.NewClient()
    apicenterCli := apicenter.NewClient()
    presentMiddle, _ := presend_middleware_client.NewClient()

    channelWeddingCli := channel_wedding.MustNewClient(context.Background())

    //presentCli := presentclient.NewClient()
    //backpackBaseCli, _ := backpackBasePb.NewClient(context.Background())

    m := &Mgr{
        shutDown:          make(chan struct{}),
        sendImCli:         sendImCli,
        backpackSenderCli: backpackSenderCli,
        micMiddleCli:      micMiddleCli,
        userProfile:       userProfile,
        micSchemeCli:      micSchemeCli,
        channelMicCli:     channelMicCli,
        pushCli:           pushCli,
        unifiedPayCli:     unifiedPayCli,
        userPresentCli:    userPresentCli,
        accountCli:        accountCli,
        magicSpiritCli:    magicSpiritCli,
        currencyCli:       currencyCli,
        apicenterCli:      apicenterCli,
        presentMiddle:     presentMiddle,
        channelWeddingCli: channelWeddingCli,
        bc:                bc,
    }

    return m, nil
}

func (m *Mgr) Stop() {
    close(m.shutDown)
}

// GetUsers 批量获取用户信息
func (m *Mgr) GetUsers(ctx context.Context, uidList []uint32) (map[uint32]*app.UserProfile, error) {
    userMap, err := m.userProfile.BatchGetUserProfileV2(ctx, uidList, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUsers err: %s,uidList:%v", err.Error(), uidList)
        return nil, err
    }
    return userMap, nil
}

// GetUser 获取用户信息
func (m *Mgr) GetUser(ctx context.Context, uid uint32) (*app.UserProfile, error) {
    user, err := m.userProfile.GetUserProfileV2(ctx, uid, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUsers err: %s,uid:%d", err.Error(), uid)
        return nil, err
    }
    return user, nil
}

// SendIMMsg 发送TT助手消息
func (m *Mgr) SendIMMsg(ctx context.Context, toUidList []uint32, content string) (err error) {
    msg := &sendim.SendSyncReq{
        Sender: &sendim.Sender{
            Type: int32(sendim.Sender_User),
            Id:   10000,
        },
        Receiver: &sendim.Receiver{
            Type:   int32(sendim.Receiver_User),
            IdList: toUidList,
        },
        Msg: &sendim.ImMsg{
            Content: &sendim.Content{
                Type:       int32(sendim.Content_Text),
                TextNormal: &sendim.ImTextNormal{Content: content},
            },
            AppPlatform: "all",
            AppName:     "",
            ExpiredAt:   uint32(time.Now().Unix()) + 86400,
        },
        WithNotify: true,
    }

    _, err = m.sendImCli.SendSync(ctx, msg)
    if err != nil {
        log.Errorf("SendIMMsg err: %s,toUid:%v, content:%s", err.Error(), toUidList, content)
        return err
    }

    log.InfoWithCtx(ctx, "SendIMMsg success. msg:%+v, uid:%v", msg, toUidList)
    return nil
}

// KickOutMicSpace 游戏结束，踢用户下麦
func (m *Mgr) KickOutMicSpace(ctx context.Context, channelId uint32, uidList []uint32) error {
    // 1.获取房间内当前麦位列表
    micResp, err := m.channelMicCli.GetMicrList(ctx, &channelMic.GetMicrListReq{
        ChannelId: channelId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "KickOutMicSpace fail to GetMicrList. channelId:%d, err:%v", channelId, err)
        return err
    }

    uidMap := make(map[uint32]struct{})
    for _, uid := range uidList {
        uidMap[uid] = struct{}{}
    }

    kickOutList := make([]uint32, 0)
    for _, mic := range micResp.GetAllMicList() {
        if _, ok := uidMap[mic.GetMicUid()]; ok {
            if !CheckInChairGameMicId(mic.GetMicId()) {
                continue
            }
            kickOutList = append(kickOutList, mic.GetMicUid())
        }
    }

    if len(kickOutList) == 0 {
        return nil
    }

    _, err = m.micMiddleCli.KickOutMic(ctx, &micMiddle.KickOutMicReq{
        Source:        "grab-chair-game",
        OpUid:         0,
        Cid:           channelId,
        TargetUidList: kickOutList,
        //BanSecond:     0,
        //Toasts:        "",
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "KickOutMicSpace fail to KickOutMicSpace.kickOutList:%v channelId:%d, err:%v", kickOutList, channelId, err)
        return err
    }

    log.InfoWithCtx(ctx, "KickOutMicSpace success. kickOutList:%v, channelId:%d", kickOutList, channelId)
    return nil
}

// ChairGameApplyMsgNotify 用户报名列表变更通知
func (m *Mgr) ChairGameApplyMsgNotify(ctx context.Context, cid, uid, totalCnt uint32, isCancel bool) error {
    opt := &channel_wedding_logic.ChairGameApplyMsgOpt{
        TotalApplyNum: totalCnt,
    }

    if !isCancel {
        user, err := m.GetUser(ctx, uid)
        if err != nil {
            log.ErrorWithCtx(ctx, "ChairGameApplyMsgNotify fail to GetUser. uid:%d, err:%v", uid, err)
            return err
        }
        var publicText *channel_wedding_logic.ChairGamePublicText
        if user.GetNickname() != "" {
            publicText = &channel_wedding_logic.ChairGamePublicText{
                Uid:        uid,
                Nickname:   user.GetNickname(),
                InviteText: "邀请大家来玩一局抢椅子",
            }
        }
        opt.PublicText = publicText
    }

    data, err := proto.Marshal(opt)
    if err != nil {
        log.ErrorWithCtx(ctx, "ChairGameApplyMsgNotify marshal err: %s, %+v", err.Error(), opt)
        return err
    }

    msg := &channelPB.ChannelBroadcastMsg{
        Time:         uint64(time.Now().Unix()),
        ToChannelId:  cid,
        Type:         uint32(channelPB.ChannelMsgType_CHANNEL_GRAB_CHAIR_GAME_INVITE_PUBLIC_MSG),
        PbOptContent: data,
    }

    channelMsgBin, err := msg.Marshal()
    if err != nil {
        log.ErrorWithCtx(ctx, "ChairGameApplyMsgNotify marshal err: %s, %+v", err.Error(), msg)
        return err
    }

    log.InfoWithCtx(ctx, "ChairGameApplyMsgNotify. cmdType:%d cid:%d %+v", msg.GetType(), cid, opt)
    return m.pushChannelBroMsgToChannels(ctx, []uint32{cid}, channelMsgBin)
}

// ChairGameInfoChannelNotify 房间抢椅子游戏变更通知
func (m *Mgr) ChairGameInfoChannelNotify(ctx context.Context, cid uint32, gameInfo *pb.ChairGameInfo) error {
    if cid == 0 || gameInfo == nil {
        log.WarnWithCtx(ctx, "ChairGameInfoChannelNotify fail to ChairGameInfoChannelNotify. cid:%d, gameInfo:%+v", cid, gameInfo)
        return nil
    }

    opt := &channel_wedding_logic.ChairGameInfo{}

    opt, err := m.transformGameInfoToLogicPb(ctx, gameInfo)
    if err != nil {
        log.ErrorWithCtx(ctx, "ChairGameInfoChannelNotify fail to transformGameInfoToLogicPb. channelId:%d, err:%v", cid, err)
        return err
    }

    data, err := proto.Marshal(opt)
    if err != nil {
        log.ErrorWithCtx(ctx, "ChairGameInfoChannelNotify marshal err: %s, %+v", err.Error(), opt)
        return err
    }

    msg := &channelPB.ChannelBroadcastMsg{
        Time:         uint64(gameInfo.GetGameProgress().GetServerTimeMs() % 1000), // 时间戳，单位秒
        ToChannelId:  cid,
        Type:         uint32(channelPB.ChannelMsgType_CHANNEL_GRAB_CHAIR_GAME_INFO_CHANEG_MSG),
        PbOptContent: data,
    }

    channelMsgBin, err := msg.Marshal()
    if err != nil {
        log.ErrorWithCtx(ctx, "ChairGameInfoChannelNotify marshal err: %s, %+v", err.Error(), msg)
        return err
    }

    log.InfoWithCtx(ctx, "ChairGameInfoChannelNotify. cmdType:%d cid:%d %+v", msg.GetType(), cid, opt)
    return m.pushChannelBroMsgToChannels(ctx, []uint32{cid}, channelMsgBin)
}

// transform gameInfo to logic pb
func (m *Mgr) transformGameInfoToLogicPb(ctx context.Context, gameInfo *pb.ChairGameInfo) (*channel_wedding_logic.ChairGameInfo, error) {
    if gameInfo == nil {
        return nil, errors.New("gameInfo is nil")
    }

    uidList := make([]uint32, 0)
    for _, v := range gameInfo.GetPlayers() {
        uidList = append(uidList, v.GetUid())
    }

    // 获取玩家用户信息
    userMap, err := m.GetUsers(ctx, uidList)
    if err != nil {
        log.ErrorWithCtx(ctx, "transformGameInfoToLogicPb GetUsers err:%v", err)
        return nil, err
    }

    PlayerInfoList := make([]*channel_wedding_logic.ChairGamePlayerInfo, 0)
    for _, v := range gameInfo.GetPlayers() {
        if user, ok := userMap[v.GetUid()]; ok {
            PlayerInfoList = append(PlayerInfoList, &channel_wedding_logic.ChairGamePlayerInfo{
                UserInfo: user,
                MicId:    v.GetMicId(),
            })
        }
    }

    rewardList := make([]*channel_wedding_logic.ChairGameRewardInfo, 0)
    for _, v := range gameInfo.GetRewardList() {
        rewardList = append(rewardList, &channel_wedding_logic.ChairGameRewardInfo{
            Icon:       v.GetIcon(),
            Name:       v.GetName(),
            Value:      v.GetValue(),
            RewardUnit: v.GetRewardUnit(),
            Amount:     v.GetAmount(),
        })
    }

    process := gameInfo.GetGameProgress()
    opt := &channel_wedding_logic.ChairGameInfo{
        GameId:            gameInfo.GetGameId(),
        ShowGameBeginAnim: gameInfo.GetShowGameBeginAnim(),
        GameProgress: &channel_wedding_logic.ChairGameProgress{
            GameId:               process.GetGameId(),
            CurRound:             process.GetCurRound(),
            ChairNum:             process.GetChairNum(),
            RoundStatus:          process.GetRoundStatus(),
            ShowRoundTip:         process.GetShowRoundTip(),
            RoundPalyerUids:      process.GetRoundPalyerUids(),
            RoundWinnerUids:      process.GetRoundWinnerUids(),
            NextRoundChairNum:    process.GetNextRoundChairNum(),
            ServerTimeMs:         process.GetServerTimeMs(),
            HostStartButDuration: process.GetHostStartButDuration(),
            HostButtonEndTs:      process.GetHostButtonEndTs(),
        },
        RewardList: rewardList,
        Players:    PlayerInfoList,
    }

    return opt, nil
}

// 房间广播消息
func (m *Mgr) pushChannelBroMsgToChannels(ctx context.Context, channelIds []uint32, channelMsgBin []byte) error {
    pushMessage := &pushPb.PushMessage{
        Cmd:     uint32(pushPb.PushMessage_CHANNEL_MSG_BRO),
        Content: channelMsgBin,
        SeqId:   uint32(time.Now().Unix()),
    }
    pushMessageBytes, e := pushMessage.Marshal()
    if e != nil {
        log.ErrorWithCtx(ctx, "pushChannelBroMsgToChannels Marshal channelIds:%v, err: %v", channelIds, e)
        return e
    }

    notification := &pushPB.CompositiveNotification{
        Sequence: uint32(time.Now().Unix()),
        TerminalTypeList: []uint32{
            protocol.MobileAndroidTT,
            protocol.MobileIPhoneTT,
        },
        TerminalTypePolicy: PushNotification.DefaultPolicy,
        AppId:              uint32(protocol.TT),
        ProxyNotification: &pushPB.ProxyNotification{
            Type:       uint32(pushPB.ProxyNotification_PUSH),
            Payload:    pushMessageBytes,
            Policy:     pushPB.ProxyNotification_DEFAULT,
            ExpireTime: 60,
        },
    }

    multicastMap := map[uint64]string{}
    for _, channelId := range channelIds {
        if channelId == 0 {
            continue
        }
        multicastMap[uint64(channelId)] = fmt.Sprintf("%d@channel", channelId)
    }

    if len(multicastMap) == 0 {
        return nil
    }

    err := m.pushCli.PushMulticasts(ctx, multicastMap, []uint32{}, notification)
    if err != nil {
        log.ErrorWithCtx(ctx, "pushChannelBroMsgToChannels fail to PushMulticasts channelIds:%v, err: %s", channelIds, err.Error())
        return err
    }

    return nil
}

// GetChannelCurWeddingId 获取当前房间婚礼id
func (m *Mgr) GetChannelCurWeddingId(ctx context.Context, cid uint32) (uint32, error) {
    weddingInfoRsp, err := m.channelWeddingCli.BatchGetChannelWeddingSimpleInfo(ctx, &channel_wedding.BatchGetChannelWeddingSimpleInfoReq{
        OpUid:   0,
        CidList: []uint32{cid},
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelCurWeddingId fail to GetChannelWeddingInfo, cid:%d, err:%v", cid, err)
        return 0, err
    }

    var weddingId uint32
    for _, v := range weddingInfoRsp.GetWeddingInfoList() {
        if v.GetCid() == cid {
            weddingId = uint32(v.GetWeddingId())
        }
    }

    return weddingId, nil
}

// ResetPlayerDefaultPose 将玩家恢复默认姿势
func (m *Mgr) ResetPlayerDefaultPose(ctx context.Context, cid uint32, uidList []uint32) error {
    ctxN, cancel := protogrpc.NewContextWithInfoTimeout(ctx, 2*time.Second)
    defer cancel()
    for _, uid := range uidList {
        _, err := m.channelWeddingCli.SetUserWeddingPose(ctxN, &channel_wedding.SetUserWeddingPoseReq{
            Uid:  uid,
            Cid:  cid,
            Pose: 0, // 默认姿势
        })
        if err != nil {
            log.ErrorWithCtx(ctxN, "ResetPlayerDefaultPose fail to SetUserWeddingPose, cid:%d, uid:%d, err:%v", cid, uid, err)
            return err
        }

        log.InfoWithCtx(ctxN, "ResetPlayerDefaultPose success to SetUserWeddingPose, cid:%d, uid:%d", cid, uid)
    }

    return nil
}

func (m *Mgr) ReportStartChairGame(ctx context.Context, cid, gameId, weddingId uint32, uidList []uint32) {
    //uidList to string,使用英文,进行分隔
    strList := make([]string, 0, len(uidList))
    for _, i := range uidList {
        strList = append(strList, fmt.Sprint(i))
    }

    // 上报开启新一局抢椅子游戏
    data := map[string]interface{}{
        "room_id":    cid,
        "uid_list":   strings.Join(strList, ","),
        "game_id":    gameId,
        "wedding_id": weddingId,
    }
    err := bylink.Track(ctx, 0, "wedding_chair_join_log", data)
    if err != nil {
        log.ErrorWithCtx(ctx, "ReportStartChairGame fail to bylink.Track error: %+v", err)
    }
    log.DebugWithCtx(ctx, "ReportStartChairGame success cid:%d, gameId:%d, weddingId:%d uidList:%v", cid, gameId, weddingId, uidList)
}

func CheckInChairGameMicId(micId uint32) bool {
    return micId >= ChairGameMicIdBegin && micId <= ChairGameMicIdEnd
}