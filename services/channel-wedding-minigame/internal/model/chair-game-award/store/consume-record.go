package store

import (
    "time"
    "fmt"
    "context"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "strings"
    "golang.52tt.com/pkg/protocol"
    "google.golang.org/grpc/codes"

    "golang.52tt.com/services/channel-wedding-minigame/internal/conf"
    "errors"
    "golang.52tt.com/protocol/common/status"
)

var createConsumeTableSql = `
CREATE Table IF NOT EXISTS %s (
    id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id 自增',
    pay_order_id varchar(128) NOT NULL COMMENT '支付订单号',
    
    uid int(10) unsigned NOT NULL COMMENT '购买用户id',
    cid int(10) unsigned NOT NULL COMMENT '房间id',
    wedding_id int(10) unsigned NOT NULL default 0 COMMENT '婚礼唯一id',
    game_id int(10) unsigned NOT NULL default 0 COMMENT '游戏唯一id',
    gift_id varchar(128) NOT NULL COMMENT '礼物id',
    gift_type int(10) unsigned NOT NULL COMMENT '礼物类型',
    gift_icon varchar(256) NOT NULL default '' COMMENT '礼物图片',
    gift_name varchar(256) NOT NULL default '' COMMENT '礼物名称',
    amount int(10) unsigned NOT NULL default 0 COMMENT '购买数量',
    price int(10) unsigned NOT NULL default 0 COMMENT '物品价格',
    price_type int(10) unsigned NOT NULL default 0 COMMENT '价值类型',

    order_status int(10)  unsigned NOT NULL default 0 COMMENT '订单状态',
    deal_token varchar(1024) NOT NULL default '' COMMENT 'deal_token,送礼时使用',
    t_bean_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 't豆系统时间',
    ctime DATETIME NOT NULL default CURRENT_TIMESTAMP COMMENT '创建时间',
    mtime DATETIME NOT NULL default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    
    primary key (id),
    unique key uniq_idx_order_id (pay_order_id),
    index idx_cid(cid,wedding_id),
    key idx_t_bean_time (t_bean_time) -- 需要跟T豆对账
)engine=InnoDB default charset=utf8 COMMENT "购买抢椅子奖励消费记录表";
`

// gen table name
func genConsumeTblName(t time.Time) string {
    return fmt.Sprintf("chair_game_consume_record_%04d%02d", t.Year(), t.Month())
}

// 建表
func (s *Store) createConsumeTable(ctx context.Context, tblTime time.Time) error {
    _, err := s.db.ExecContext(ctx, fmt.Sprintf(createConsumeTableSql, genConsumeTblName(tblTime)))
    if err != nil {
        log.ErrorWithCtx(ctx, "create table failed. err:%v", err)
        return err
    }
    return nil
}

type ConsumeRecord struct {
    Id          uint32    `db:"id"`
    PayOrderId  string    `db:"pay_order_id"`
    Uid         uint32    `db:"uid"`
    Cid         uint32    `db:"cid"`
    WeddingId   uint32    `db:"wedding_id"`
    GameId      uint32    `db:"game_id"`
    GiftId      string    `db:"gift_id"`
    GiftType    uint32    `db:"gift_type"`
    GiftIcon    string    `db:"gift_icon"`
    GiftName    string    `db:"gift_name"`
    Amount      uint32    `db:"amount"`
    Price       uint32    `db:"price"`
    PriceType   uint32    `db:"price_type"`
    OrderStatus uint32    `db:"order_status"`
    DealToken   string    `db:"deal_token"`
    TBeanTime   time.Time `db:"t_bean_time"`
    Ctime       time.Time `db:"ctime"`
    Mtime       time.Time `db:"mtime"`
}

// InsertConsumeRecord 新增购买消费记录
func (s *Store) InsertConsumeRecord(ctx context.Context, record *ConsumeRecord) error {
    query := fmt.Sprintf("insert into %s (pay_order_id, uid,cid, wedding_id, gift_id, gift_type, gift_icon, gift_name, amount, price,price_type,ctime) "+
        "values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", genConsumeTblName(record.Ctime))

    params := make([]interface{}, 0)
    params = append(params, record.PayOrderId, record.Uid, record.Cid, record.WeddingId, record.GiftId, record.GiftType, record.GiftIcon, record.GiftName,
        record.Amount, record.Price, record.PriceType, record.Ctime)

    _, err := s.db.ExecContext(ctx, query, params...)
    if err != nil {
        // 唯一键重复
        if mysql.IsMySQLError(err, 1062) {
            log.ErrorWithCtx(ctx, "InsertConsumeRecord failed. err:%v", err)
            return protocol.NewExactServerError(codes.OK, status.ErrChannelWeddingMinigameCommonErr, "购买失败，请重试")
        }

        // 表不存在
        if mysql.IsMySQLError(err, 1146) {
            // 尝试建表
            err = s.createConsumeTable(ctx, record.Ctime)
            if err != nil {
                log.ErrorWithCtx(ctx, "createConsumeTable failed. err:%v", err)
                return err
            }
            // 重试
            _, err = s.db.ExecContext(ctx, query, params...)
            if err != nil {
                log.ErrorWithCtx(ctx, "InsertConsumeRecord failed. err:%v", err)
                return err
            }
            return nil
        }

        log.ErrorWithCtx(ctx, "InsertConsumeRecord failed. err:%v", err)
        return err
    }

    log.InfoWithCtx(ctx, "InsertConsumeRecord success. record:%+v", record)
    return nil
}

// ChangeConsumeRecordStatus 修改购买消费记录状态
func (s *Store) ChangeConsumeRecordStatus(ctx context.Context, payOrderId string, t time.Time, oldStatusList []uint32, toStatus uint32) (bool, error) {
    query := fmt.Sprintf("update %s set order_status = ? where pay_order_id = ? and order_status in (%s)", genConsumeTblName(t), genParamJoinStr(oldStatusList))

    change, err := s.db.ExecContext(ctx, query, toStatus, payOrderId)
    if err != nil {
        log.ErrorWithCtx(ctx, "ChangeConsumeRecordStatus failed. payOrderId:%s err:%v", payOrderId, err)
        return false, err
    }

    rowsEffected, _ := change.RowsAffected()
    log.InfoWithCtx(ctx, "ChangeConsumeRecordStatus success. payOrderId:%s toStatus:%d rowsEffected:%d", payOrderId, toStatus, rowsEffected)
    return rowsEffected > 0, nil
}

// ChangeRecordStatusWithGameId 修改购买消费记录状态
func (s *Store) ChangeRecordStatusWithGameId(ctx context.Context, tx mysql.Txx, payOrderId string, gameId uint32, t time.Time, oldStatusList []uint32, toStatus uint32) (bool, error) {
    query := fmt.Sprintf("update %s set game_id=?, order_status = ? where pay_order_id = ? and order_status in (%s)", genConsumeTblName(t), genParamJoinStr(oldStatusList))

    if tx == nil {
        return false, errors.New("tx is nil")
    }

    change, err := tx.ExecContext(ctx, query, gameId, toStatus, payOrderId)
    if err != nil {
        log.ErrorWithCtx(ctx, "ChangeRecordStatusWithGameId failed. payOrderId:%s err:%v", payOrderId, err)
        return false, err
    }

    rowsEffected, _ := change.RowsAffected()
    log.InfoWithCtx(ctx, "ChangeRecordStatusWithGameId success. payOrderId:%s toStatus:%d rowsEffected:%d", payOrderId, toStatus, rowsEffected)
    return rowsEffected > 0, nil
}

// GetFreezingRecordByCid 根据房间id、weddingId 判断是否有freezing状态的记录
func (s *Store) GetFreezingRecordByCid(ctx context.Context, t time.Time, cid, weddingId uint32) (*ConsumeRecord, error) {
    query := fmt.Sprintf("select pay_order_id, uid,cid, wedding_id, gift_id, gift_type, gift_icon, gift_name, amount, price,price_type,ctime from %s where cid = ? and wedding_id = ? and order_status = ?", genConsumeTblName(t))

    record := &ConsumeRecord{}
    err := s.db.GetContext(ctx, record, query, cid, weddingId, conf.ConsumeOrderStatusFreezing)
    if err != nil {
        // 记录不存在/表不存在
        if mysql.IsNoRowsError(err) || mysql.IsMySQLError(err, 1146) {
            return nil, nil
        }
        log.ErrorWithCtx(ctx, "IsExistFreezingRecord failed. cid:%d weddingId:%d err:%v", cid, weddingId, err)
        return nil, err
    }

    return record, nil
}

// ChangeConsumeRecordPayInfo 更新记录PayInfo
func (s *Store) ChangeConsumeRecordPayInfo(ctx context.Context, t time.Time, uid uint32, oldStatus []uint32, newStatus uint32, payOrderId, tBeanTimeStr, dealToken string) (bool, error) {
    if len(oldStatus) == 0 {
        return false, nil
    }

    param := make([]interface{}, 0)
    query := fmt.Sprintf(`UPDATE %s SET order_status=? `, genConsumeTblName(t))
    param = append(param, newStatus)

    if tBeanTimeStr != "" {
        tBeanTime, _ := time.ParseInLocation("2006-01-02 15:04:05", tBeanTimeStr, time.Local)
        query += `, t_bean_time = ?`
        param = append(param, tBeanTime)
    }

    if dealToken != "" {
        query += `, deal_token = ?`
        param = append(param, dealToken)
    }

    param = append(param, payOrderId)
    statusStrList := make([]string, 0)
    for _, v := range oldStatus {
        statusStrList = append(statusStrList, "?")
        param = append(param, v)
    }

    query += fmt.Sprintf(` WHERE pay_order_id=? AND order_status in (%s)`, strings.Join(statusStrList, ","))

    r, err := s.db.ExecContext(ctx, query, param...)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateConsumeRecordPayInfo fail.uid:%d, payOrderId:%v, newStatus:%v, err:%v", uid, payOrderId, newStatus, err)
        return false, err
    }

    rowAffect, _ := r.RowsAffected()
    log.InfoWithCtx(ctx, "UpdateConsumeRecordPayInfo success. uid:%d, payOrderId:%v, newStatus:%v,rowAffect:%d", uid, payOrderId, newStatus, rowAffect)
    return rowAffect > 0, nil
}

// GetConsumeRecordByOrderId 根据payOrderId获取购买消费记录
func (s *Store) GetConsumeRecordByOrderId(ctx context.Context, t time.Time, payOrderId string) (*ConsumeRecord, bool, error) {
    queryFmt := "select pay_order_id, uid,cid, wedding_id,game_id, gift_id, gift_type, gift_icon, gift_name, amount, price,price_type,order_status,deal_token, t_bean_time, ctime" +
        " from %s where pay_order_id = ?"
    query := fmt.Sprintf(queryFmt, genConsumeTblName(t))
    log.DebugWithCtx(ctx, "GetConsumeRecordByOrderId.query:%s", query)
    record := &ConsumeRecord{}
    err := s.db.GetContext(ctx, record, query, payOrderId)

    // 表不存在/记录不存在
    if mysql.IsMySQLError(err, 1146) || mysql.IsMySQLError(err, 1062) {
        // 多查上一个月
        t = time.Date(t.Year(), t.Month(), 1, 1, 0, 0, 0, t.Location()).AddDate(0, -1, 0)
        query = fmt.Sprintf(queryFmt, genConsumeTblName(t))
        err = s.db.GetContext(ctx, record, query, payOrderId)
    }

    if err != nil {
        log.ErrorWithCtx(ctx, "GetConsumeRecordByOrderId failed. payOrderId:%s err:%v", payOrderId, err)
        return nil, false, err
    }

    return record, true, nil
}

// GetConsumeRecordByStatus 根据订单状态获取指定时间范围内的购买消费记录
func (s *Store) GetConsumeRecordByStatus(ctx context.Context, status uint32, tblTime, beginTime, endTime time.Time) ([]*ConsumeRecord, error) {
    rList := make([]*ConsumeRecord, 0)

    query := fmt.Sprintf("SELECT pay_order_id, uid, cid, wedding_id, amount, price, price_type,ctime FROM %s WHERE order_status = ? AND ctime >= ? AND ctime <?",
        genConsumeTblName(tblTime))

    err := s.db.SelectContext(ctx, &rList, query, status, beginTime, endTime)
    if err != nil {
        // 表不存在
        if mysql.IsMySQLError(err, 1146) {
            return nil, nil
        }
        log.ErrorWithCtx(ctx, "GetConsumeRecordByStatus fail. err %v", err)
        return nil, err
    }

    return rList, err
}

type StCount struct {
    Count int64 `db:"count"`
    Worth int64 `db:"worth"`
}

func (s *Store) GetConsumeTotalCountInfo(ctx context.Context, beginTime, endTime time.Time) (*StCount, error) {
    out := &StCount{}

    // 提取查询逻辑到单独的函数中
    queryFunc := func(tblName string) (*StCount, error) {
        temp := "SELECT COUNT(1) as count, COALESCE(SUM(price*amount), 0) as worth FROM %s WHERE t_bean_time >= ? AND t_bean_time < ? AND price_type=2 AND order_status IN (?, ?)"
        query := fmt.Sprintf(temp, tblName)
        tmpCnt := &StCount{}
        err := s.readonlyDB.GetContext(ctx, tmpCnt, query, beginTime, endTime, conf.ConsumeOrderStatusAwarded, conf.ConsumeOrderStatusCommit)
        return tmpCnt, handleDBError(err, ctx, beginTime, "GetConsumeTotalCountInfo")
    }

    // 查询主表
    mainTblName := genConsumeTblName(beginTime)
    tmpCnt, err := queryFunc(mainTblName)
    if err != nil {
        return out, err
    }
    out.Count += tmpCnt.Count
    out.Worth += tmpCnt.Worth

    // 判断是否需要查询次表
    monthTime := getMonthBoundaryTime(beginTime)
    if !monthTime.IsZero() {
        subTblName := genConsumeTblName(monthTime)
        tmpCnt2, err := queryFunc(subTblName)
        if err != nil {
            return out, err
        }
        out.Count += tmpCnt2.Count
        out.Worth += tmpCnt2.Worth
    }

    log.InfoWithCtx(ctx, "GetConsumeTotalCountInfo success. beginTime: %v, endTime: %v", beginTime, endTime)
    return out, nil
}

func (s *Store) GetConsumeOrderIds(ctx context.Context, beginTime, endTime time.Time) ([]string, error) {
    var list []string

    // 提取查询逻辑到单独的函数中
    queryFunc := func(tblName string) ([]string, error) {
        temp := "SELECT pay_order_id FROM %s WHERE t_bean_time >= ? AND t_bean_time < ? AND price_type=2 AND order_status IN (?, ?)"
        query := fmt.Sprintf(temp, tblName)
        var ids []string
        err := s.readonlyDB.SelectContext(ctx, &ids, query, beginTime, endTime, conf.ConsumeOrderStatusAwarded, conf.ConsumeOrderStatusCommit)
        return ids, handleDBError(err, ctx, beginTime, "GetConsumeOrderIds")
    }

    // 查询第一个时间段的数据
    tblName := genConsumeTblName(beginTime)
    ids, err := queryFunc(tblName)
    if err != nil {
        return ids, err
    }
    list = append(list, ids...)

    // 判断是否需要查询跨月的数据
    monthTime := getMonthBoundaryTime(beginTime)
    if !monthTime.IsZero() {
        tblName2 := genConsumeTblName(monthTime)
        ids2, err := queryFunc(tblName2)
        if err != nil {
            return ids2, err
        }
        list = append(list, ids2...)
    }

    log.InfoWithCtx(ctx, "GetConsumeOrderIds success. beginTime: %v, endTime: %v", beginTime, endTime)
    return list, nil
}

// 处理数据库错误，统一错误处理逻辑
func handleDBError(err error, ctx context.Context, beginTime time.Time, msg string) error {
    if mysql.IsMySQLError(err, 1146) || mysql.IsMySQLError(err, 1062) {
        return nil
    }
    if err != nil {
        log.ErrorWithCtx(ctx, "%s failed. queryMonthTime:%v, err:%v", msg, beginTime, err)
        return err
    }
    return nil
}

// 获取跨月的时间点
func getMonthBoundaryTime(t time.Time) time.Time {
    if t.Add(time.Hour).Month() != t.Month() {
        return t.Add(time.Hour)
    } else if t.Add(-time.Hour).Month() != t.Month() {
        return t.Add(-time.Hour)
    }
    return time.Time{}
}
