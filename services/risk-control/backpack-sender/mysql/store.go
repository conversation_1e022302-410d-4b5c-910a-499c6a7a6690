package mysql

import (
	"context"
	"encoding/hex"
	"errors"
	"fmt"
	"github.com/go-sql-driver/mysql" // MySQL驱动。
	"github.com/jmoiron/sqlx"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/backpacksender"
	"golang.52tt.com/services/risk-control/backpack-sender/conf"
	"golang.52tt.com/services/risk-control/backpack-sender/utils"
	"time"
)

const (
	CreateBusinessTableSql = `CREATE TABLE risk_control_business
		(business_id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '业务ID',
		source_id int(10) unsigned NOT NULL COMMENT '包裹来源',
		business_name varchar(255) NOT NULL DEFAULT '' COMMENT '业务名',
		oper_user     varchar(255) NOT NULL COMMENT '操作者用户名',
		description varchar(255) NOT NULL DEFAULT '' COMMENT '业务描述',
		secret_key varchar(255) NOT NULL DEFAULT '' COMMENT '密钥',
		callback_url varchar(255) NOT NULL DEFAULT '' COMMENT '回调接口',
		business_is_valid int unsigned NOT NULL DEFAULT 1 COMMENT '业务可用状态 1:可用，0：不可用',
		create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
		update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
		begin_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '业务开始时间',
		end_time   timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '业务结束时间',
		warning_precent   int unsigned NOT NULL DEFAULT 0 COMMENT '告警百分比',
		oper_email  varchar(255) NOT NULL DEFAULT '' COMMENT '操作者邮箱',
		secret_key_type tinyint unsigned NOT NULL DEFAULT 0 COMMENT '密钥类型 0:普通密钥 1:加密密钥',
		UNIQUE KEY business_name(business_name),
		PRIMARY KEY (business_id)) ENGINE=InnoDB DEFAULT CHARSET=utf8;`

	CreateRiskcontrolConfigTableSql = `CREATE TABLE risk_control_backpack_config(
		id int(10) unsigned NOT NULL AUTO_INCREMENT,
		business_id int(10) unsigned NOT NULL COMMENT '业务ID',
		backpack_id int(10) unsigned NOT NULL COMMENT '发放包裹ID', 
		hour_cnt_limit bigint NOT NULL DEFAULT 0 COMMENT '每小时限制数量',
		day_cnt_limit bigint unsigned NOT NULL DEFAULT 0 COMMENT '每天限制数量',
		single_cnt_limit int(10) unsigned NOT NULL DEFAULT 0 COMMENT '单次发放限制数量',
		single_value_limit int(10) unsigned NOT NULL DEFAULT 0 COMMENT '单次发放限制价值',
		hour_tbean_value_limit bigint NOT NULL DEFAULT 0 COMMENT '每小时T豆价值限制',
		day_tbean_value_limit bigint NOT NULL DEFAULT 0 COMMENT '每天T豆价值限制',
		oper_user varchar(255) NOT NULL COMMENT '操作者用户名',
		oper_email  varchar(255) NOT NULL DEFAULT '' COMMENT '操作者邮箱',
		mtime datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新的时间',
		index business_index (business_id),
		UNIQUE KEY business_id(business_id,backpack_id),
		PRIMARY KEY (id)) ENGINE=InnoDB DEFAULT CHARSET=utf8;`

	OrderTableName = "risk_contral_backpack_order_%04v%02v"

	CreateMonthlyOrderTableSql = `CREATE TABLE %v(
		order_id varchar(255) NOT NULL COMMENT '订单号',
		business_id int(10) unsigned NOT NULL COMMENT '业务ID',
		backpack_id int(10) unsigned NOT NULL COMMENT '包裹ID',
		receive_uid int(10) unsigned NOT NULL COMMENT '收包裹用户UID',
		source_id int(10) unsigned NOT NULL DEFAULT 0 COMMENT '来源ID',
		count int(10) unsigned NOT NULL COMMENT '发放包裹数量',
		expire_duration int(10) unsigned NOT NULL DEFAULT 0 COMMENT '自然天过期',
		source_app_id varchar(255) NOT NULL DEFAULT '' COMMENT '包裹详细来源',
		outside_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发放包裹业务的时间',
		create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间风控服务时间',
		update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新的时间',
		order_status tinyint(4) NOT NULL DEFAULT 0 COMMENT '订单状态0:初始化,1:发放成功',
        traffic_mark varchar(64) NOT NULL DEFAULT '' COMMENT '流量标识',
		index outside_time_index(outside_time),
		index create_time_index(create_time),
		index business_index (business_id),
		PRIMARY KEY (order_id)) ENGINE=InnoDB DEFAULT CHARSET=utf8;`

	//业务发放包裹数据统计表
	CreateBusinessDataTableSql = `CREATE TABLE risk_control_business_data_%04v%02v (
		idx int(10) unsigned NOT NULL AUTO_INCREMENT,
		business_id int(10) unsigned NOT NULL COMMENT '业务ID',
		sdate varchar(255) NOT NULL COMMENT '时间',
		backpack_id int(10) unsigned NOT NULL COMMENT '包裹ID,0为总数据',
		backpack_cnt bigint NOT NULL DEFAULT 0 COMMENT '发放包裹数量',
        tbean_value bigint NOT NULL DEFAULT 0 COMMENT 'T豆总价值',
		create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
		PRIMARY KEY (idx),
		UNIQUE KEY business_id (business_id,backpack_id,sdate),
        index business_index(business_id),
		index create_time_index(create_time)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
)

type Store struct {
	DB *sqlx.DB
}

func NewMysql(db *sqlx.DB) *Store {
	return &Store{
		DB: db,
	}
}

// 业务配置
type BusinessConf struct {
	BusinessID     uint32 `db:"business_id"`
	SourceID       uint32 `db:"source_id"`
	Name           string `db:"business_name"`
	OperUser       string `db:"oper_user"`
	Desc           string `db:"description"`
	SecretKey      string `db:"secret_key"`
	CallbackUrl    string `db:"callback_url"`
	IsValid        uint32 `db:"business_is_valid"`
	CreateTime     int64  `db:"create_time"`
	UpdateTime     int64  `db:"update_time"`
	BeginTime      int64  `db:"begin_time"`
	EndTime        int64  `db:"end_time"`
	WarningPrecent uint32 `db:"warning_precent"`
	OperEmail      string `db:"oper_email"`
	SecretKeyType  uint32 `db:"secret_key_type"`
}

// 风险控制配置
type RiskControlConf struct {
	BusinessID              uint32 `db:"business_id"`
	BackpackID              uint32 `db:"backpack_id"`
	HourCntLimit            uint64 `db:"hour_cnt_limit"`
	DayCntLimit             uint64 `db:"day_cnt_limit"`
	SingleCntLimit          uint32 `db:"single_cnt_limit"`
	SingleValueLimit        uint32 `db:"single_value_limit"`
	HourTbeanValueLimit     uint64 `db:"hour_tbean_value_limit"`
	DayTbeanValueLimit      uint64 `db:"day_tbean_value_limit"`
	OperUser                string `db:"oper_user"`
	OperEmail               string `db:"oper_email"`
	Id                      uint32 `db:"id"`
	UserHourCntLimit        uint64 `db:"user_hour_cnt_limit"`
	UserDayCntLimit         uint64 `db:"user_day_cnt_limit"`
	UserHourTbeanValueLimit uint64 `db:"user_hour_tbean_value_limit"`
	UserDayTbeanValueLimit  uint64 `db:"user_day_tbean_value_limit"`
}

// 订单
type OrderInfoStu struct {
	OrderId        string    `db:"order_id"`
	BusinessID     uint32    `db:"business_id"`
	BackpackID     uint32    `db:"backpack_id"`
	ReceiveUid     uint32    `db:"receive_uid"`
	SourceID       uint32    `db:"source_id"`
	Count          uint32    `db:"count"`
	OutsideTime    time.Time `db:"outside_time"`
	CreateTime     time.Time `db:"create_time"`
	ExpireDuration int32     `db:"expire_duration"`
	SourceAppID    string    `db:"source_app_id"`
	TrafficMark    string    `db:"traffic_mark"`
}

// 业务数据统计
type BusinessData struct {
	BusinessID  uint32 `db:"business_id"`
	SDate       string `db:"sdate"`
	BackpackID  uint32 `db:"backpack_id"`
	BackpackCnt uint64 `db:"backpack_cnt"`
	TbeanValue  uint64 `db:"tbean_value"`
}

func (s *Store) CreateTables(ctx context.Context) error {
	//_, err := s.DB.Exec(CreateBusinessTableSql)
	//if nil != err {
	//	log.ErrorWithCtx(ctx, "CreateOrderTable fail err:%v", err)
	//	//return err
	//}
	//_, err = s.DB.Exec(CreateRiskcontrolConfigTableSql)
	//if nil != err {
	//	log.ErrorWithCtx(ctx, "CreateRiskcontrolConfigTable fail err:%v", err)
	//	//return err
	//}
	nowTs := time.Now()
	_, err := s.DB.Exec(fmt.Sprintf(CreateMonthlyOrderTableSql, utils.GenTableName(OrderTableName, nowTs)))
	if nil != err {
		log.ErrorWithCtx(ctx, "CreateMonthlyOrderTable fail err:%v", err)
		//return err
	}

	_, err = s.DB.Exec(fmt.Sprintf(CreateBusinessDataTableSql, nowTs.Year(), int(nowTs.Month())))
	if nil != err {
		log.ErrorWithCtx(ctx, "CreateMonthlyOrderTable fail err:%v", err)
		//return err
	}
	return nil
}

func (s *Store) AddBusiness(ctx context.Context, secretKey string, bsConf *pb.BusinessConf) (uint32, error) {
	secretKeyPass := utils.AESEncrypt([]byte(secretKey), []byte(conf.STORAGE_ENCRYPT_PASSWD))
	secretKeyHex := hex.EncodeToString(secretKeyPass)
	secretKeyType := conf.SECRET_KEY_TYPE_AES
	sql := fmt.Sprintf("insert into risk_control_business (source_id, oper_user, business_name, description, secret_key, callback_url, begin_time, end_time, warning_precent, oper_email, secret_key_type) "+
		"values(%v, '%v', '%v', '%v', '%v','%v', '%v', '%v', %v,'%v', %v)", bsConf.SourceId, bsConf.OperaUser, bsConf.Name, bsConf.Desc,
		secretKeyHex, bsConf.CallbackUrl, bsConf.BeginTime, bsConf.EndTime, bsConf.WarningPrecent, bsConf.OperEmail, secretKeyType)

	r, err := s.DB.ExecContext(ctx, sql)
	if nil != err {
		log.ErrorWithCtx(ctx, "insert risk_control_business fail err:%v", err)
		if driverErr, ok := err.(*mysql.MySQLError); ok && driverErr.Number == 1062 {
			return 0, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "该风控业务名称已被使用")
		}
		return 0, err
	}

	id, err := r.LastInsertId()
	if err != nil {
		log.ErrorWithCtx(ctx, "AddBusiness LastInsertId failed err:%v", err)
		return 0, err
	}

	return uint32(id), nil
}

func (s *Store) UpdateBusinessSecret(ctx context.Context, secretKey string, businessId uint32) error {
	secretKeyPass := utils.AESEncrypt([]byte(secretKey), []byte(conf.STORAGE_ENCRYPT_PASSWD))
	secretKeyHex := hex.EncodeToString(secretKeyPass)
	secretKeyType := conf.SECRET_KEY_TYPE_AES
	sql := fmt.Sprintf("update risk_control_business set secret_key='%s',secret_key_type=%d where business_id=%d", secretKeyHex, secretKeyType, businessId)
	_, err := s.DB.ExecContext(ctx, sql)
	if nil != err {
		log.ErrorWithCtx(ctx, "UpdateBusinessSecret fail sql:%s err:%v", sql, err)
		return err
	}
	return nil
}

func (s *Store) GetBusiness(ctx context.Context, businessID uint32) ([]*BusinessConf, error) {
	sql := fmt.Sprintf("select oper_user, business_id, source_id, business_name, description, secret_key, callback_url," +
		"business_is_valid, UNIX_TIMESTAMP(create_time) as create_time, UNIX_TIMESTAMP(update_time) as update_time, UNIX_TIMESTAMP(begin_time) as begin_time, " +
		"UNIX_TIMESTAMP(end_time) as end_time, warning_precent, oper_email,secret_key_type from risk_control_business")

	if businessID != 0 {
		sql = sql + fmt.Sprintf(" where business_id=%v", businessID)
	}

	bs := make([]*BusinessConf, 0)
	err := s.DB.SelectContext(ctx, &bs, sql)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetAllBusiness err:%v", err)
		return bs, err
	}
	passwd := []byte(conf.STORAGE_ENCRYPT_PASSWD)
	for _, b := range bs {
		if b.SecretKeyType == conf.SECRET_KEY_TYPE_AES {
			decrypt, err := hex.DecodeString(b.SecretKey)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetBusiness DecodeString bs :%+v, err:%v", b, err)
				continue
			}
			b.SecretKey = string(utils.AESDecrypt(decrypt, passwd))
		}
	}
	return bs, nil
}

func (s *Store) AddBusinessRiskControlConf(ctx context.Context, tx *sqlx.Tx, riskConf *pb.BusinessRiskControlConf) error {

	sql := fmt.Sprintf("insert into risk_control_backpack_config (oper_user,business_id, backpack_id, hour_cnt_limit, day_cnt_limit, single_cnt_limit, "+
		"single_value_limit, hour_tbean_value_limit, day_tbean_value_limit, oper_email,"+
		"user_hour_cnt_limit, user_day_cnt_limit, user_hour_tbean_value_limit, user_day_tbean_value_limit) "+
		"values('%v',%v, %v, %v, %v, %v, %v, %v, %v, '%v',%v, %v, %v, %v)", riskConf.OperaUser, riskConf.BusinessId, riskConf.BgId, riskConf.HourCntLimit,
		riskConf.DayCntLimit, riskConf.SingleCntLimit, riskConf.SingleValueLimit, riskConf.HourTbeanValueLimit, riskConf.DayTbeanValueLimit, riskConf.OperEmail,
		riskConf.UserHourCntLimit, riskConf.UserDayCntLimit, riskConf.UserHourTbeanValueLimit, riskConf.UserDayTbeanValueLimit)

	var err error
	if tx == nil {
		_, err = s.DB.ExecContext(ctx, sql)
	} else {
		_, err = tx.ExecContext(ctx, sql)
	}
	if nil != err {
		log.ErrorWithCtx(ctx, "insert risk_control_backpack_config fail err:%v", err)
		if driverErr, ok := err.(*mysql.MySQLError); ok && driverErr.Number == 1062 {
			return errors.New(fmt.Sprintf("该风控的包裹ID(%d)已存在", riskConf.BgId))
		}
		return err
	}

	return nil
}

func (s *Store) ModBusinessRiskControlConf(ctx context.Context, tx *sqlx.Tx, conf *pb.BusinessRiskControlConf) error {
	sql := fmt.Sprintf("update risk_control_backpack_config set oper_user=?, business_id=?, backpack_id=?, hour_cnt_limit=?, day_cnt_limit=?, " +
		"single_cnt_limit=?, single_value_limit=?, hour_tbean_value_limit=?, day_tbean_value_limit=?, oper_email=?," +
		"user_hour_cnt_limit=?, user_day_cnt_limit=?, user_hour_tbean_value_limit=?, user_day_tbean_value_limit=? where id = ?")

	var err error
	if tx == nil {
		_, err = s.DB.ExecContext(ctx, sql, conf.GetOperaUser(), conf.GetBusinessId(), conf.GetBgId(), conf.GetHourCntLimit(), conf.GetDayCntLimit(),
			conf.GetSingleCntLimit(), conf.GetSingleValueLimit(), conf.GetHourTbeanValueLimit(), conf.GetDayTbeanValueLimit(), conf.GetOperEmail(),
			conf.UserHourCntLimit, conf.UserDayCntLimit, conf.UserHourTbeanValueLimit, conf.UserDayTbeanValueLimit, conf.GetId())
	} else {
		_, err = s.DB.ExecContext(ctx, sql, conf.GetOperaUser(), conf.GetBusinessId(), conf.GetBgId(), conf.GetHourCntLimit(), conf.GetDayCntLimit(),
			conf.GetSingleCntLimit(), conf.GetSingleValueLimit(), conf.GetHourTbeanValueLimit(), conf.GetDayTbeanValueLimit(), conf.GetOperEmail(),
			conf.UserHourCntLimit, conf.UserDayCntLimit, conf.UserHourTbeanValueLimit, conf.UserDayTbeanValueLimit, conf.GetId())
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "ModBusinessRiskControlConf fail err:%v", err)
		return err
	}
	return nil
}

func (s *Store) GetBusinessRiskControlConf(ctx context.Context, businessID uint32) ([]*RiskControlConf, error) {
	rcList := make([]*RiskControlConf, 0)
	sql := fmt.Sprintf("select id, business_id, backpack_id, hour_cnt_limit, day_cnt_limit, single_cnt_limit," +
		"single_value_limit, hour_tbean_value_limit, day_tbean_value_limit, oper_user, oper_email," +
		"user_hour_cnt_limit, user_day_cnt_limit, user_hour_tbean_value_limit, user_day_tbean_value_limit from risk_control_backpack_config")

	if businessID != 0 {
		sql = sql + fmt.Sprintf(" where business_id=%v", businessID)
	}
	sql = sql + " order by id desc"

	err := s.DB.SelectContext(ctx, &rcList, sql)
	if nil != err {
		return rcList, err
	}
	return rcList, nil
}

/*func (s *Store) GetBusinessCurrData(ctx context.Context, ts time.Time) ([]*BusinessData, error) {
	hoursAgoTs := time.Unix(ts.Unix()-3600*26, 0)
	tbName := utils.GenTableName("risk_control_business_data_%04v%02v", ts)
	sql := fmt.Sprintf("select business_id,sdate,backpack_id, backpack_cnt, tbean_value from %v where create_time >= '%v' and sdate in ('%v', '%v')",
		tbName, utils.GetTimeStr(hoursAgoTs), utils.GetHourStr(ts), utils.GetDayStr(ts))
	ds := make([]*BusinessData, 0)
	err := s.DB.SelectContext(ctx, &ds, sql)
	if nil != err {
		log.ErrorWithCtx(ctx,"GetBusinessData err:%v", err)
		return ds, err
	}
	return ds, nil
}*/

/*func (s *Store) GetBusinessCurrDataById(ctx context.Context, bId uint32, ts time.Time) ([]*BusinessData, error) {
	hoursAgoTs := time.Unix(ts.Unix()-3600*26, 0)
	tbName := utils.GenTableName("risk_control_business_data_%04v%02v", ts)
	sql := fmt.Sprintf("select business_id,sdate,backpack_id, backpack_cnt, tbean_value from %v where business_id=%v and  create_time >= '%v' and sdate in ('%v', '%v')",
		tbName, bId, utils.GetTimeStr(hoursAgoTs), utils.GetHourStr(ts), utils.GetDayStr(ts))
	ds := make([]*BusinessData, 0)
	err := s.DB.SelectContext(ctx, &ds, sql)
	if nil != err {
		log.ErrorWithCtx(ctx,"GetBusinessCurrDataById err:%v", err)
		return ds, err
	}
	return ds, nil
}*/

/*func ExecSqlV2(tx *sqlx.Tx, ctx context.Context, tbName, filed, sDate string, businessID, bgID, addValue uint32, limitValue uint64) error {

	var value int64
	selectSql := fmt.Sprintf("select %v from %v where business_id=%v and sdate='%v' and backpack_id=%v", filed, tbName, businessID, sDate, bgID)

	serr := tx.GetContext(ctx, &value, selectSql)

	if serr == nil && value > int64(limitValue) {
		return errors.New(fmt.Sprintf("limit hit sData:%v businessID:%v %v %v %v", sDate, businessID, filed, value, limitValue))
	}

	//not found
	if serr == sql.ErrNoRows {
		insertSql := fmt.Sprintf("insert into %v (business_id,sdate,backpack_id,%v) values(%v,'%v',%v,%v)", tbName, filed, businessID, sDate, bgID, 0)
		_, err := tx.ExecContext(ctx, insertSql)

		if nil != err {
			if driverErr, ok := err.(*mysql.MySQLError); ok {
				if driverErr.Number != 1062 {
					log.ErrorWithCtx(ctx,"ExecSqlV2 insertSql:%v err:%v", insertSql, err)
					return err
				}
			} else {
				log.ErrorWithCtx(ctx,"ExecSqlV2 insertSql:%v err:%v", insertSql, err)
				return err
			}
		}
	}

	updateSql := fmt.Sprintf("update %v set %v=%v+%v where business_id=%v and sdate='%v' and backpack_id=%v and %v<=%v",
		tbName, filed, filed, addValue, businessID, sDate, bgID, filed, limitValue)
	res, err := tx.ExecContext(ctx, updateSql)

	if nil != err {
		return err
	}

	if cnt, _ := res.RowsAffected(); cnt == 0 {
		return errors.New(fmt.Sprintf("limit hit sData:%v businessID:%v %v", sDate, businessID, filed))
	}

	return nil
}*/

/*func ExecSql(tx *sqlx.Tx, createSql, tmpSql, filed, sDate string, businessID, bgID, addValue uint32, limitValue uint64) error {
	sql := fmt.Sprintf(tmpSql, filed, businessID, sDate, bgID, addValue, filed, filed, limitValue, filed, addValue, filed)
	//insert into risk_control_business_data_202101(business_id,sdate,backpack_id,backpack_cnt) values(1,'2021-01-19-05',10,10) ON DUPLICATE KEY UPDATE backpack_cnt=if(backpack_cnt<=99999990,backpack_cnt+values(10),backpack_cnt)
	log.DebugfWithCtx(ctx,"ExecSql sql:%v", sql)

	res, err := tx.Exec(sql)
	if nil != err {
		log.ErrorWithCtx(ctx,"ExecSql sql:%v err:%v", sql, err)

		_, err = tx.Exec(createSql)
		if nil != err {
			log.ErrorWithCtx(ctx,"ExecSql createSql:%v err:%v", createSql, err)
			return err
		}
		res, err = tx.Exec(sql)
		if nil != err {
			log.ErrorWithCtx(ctx,"ExecSql sql:%v err:%v", sql, err)
			return err
		}
	}

	if cnt, _ := res.RowsAffected(); cnt == 0 {
		log.ErrorWithCtx(ctx,"ExecSql sql:%v cnt==0", sql, cnt)
		return errors.New(fmt.Sprintf("limit hit sData:%v businessID:%v %v", sDate, businessID, filed))
		//return protocol.NewServerError(-6080, err.Error())
	}
	return nil
}*/

/*func (s *Store) CheckSingleRiskControl(tx *sqlx.Tx, ctx context.Context, ts time.Time, rc *RiskControlConf, orderInfo *pb.SendBackpackOrderInfo, tbeanValue uint32) protocol.ServerError {

	if rc.SingleValueLimit > 0 && rc.SingleValueLimit < tbeanValue {
		log.ErrorWithCtx(ctx,"CheckSingleRiskControl 单次T豆价值限制 orderInfo:%v", orderInfo)
		return protocol.NewServerError(status.ErrRiskControlBackpackSignalTbeanLimit, "单次T豆价值限制")
	}

	if rc.SingleCntLimit > 0 && rc.SingleCntLimit < orderInfo.BackpackCnt {
		log.ErrorWithCtx(ctx,"CheckSingleRiskControl 单次包裹数量限制 BackpackCnt:%v SingleCntLimit:%v", orderInfo.BackpackCnt, rc.SingleCntLimit)
		return protocol.NewServerError(status.ErrRiskControlBackpackSignalCountLimit, "单次包裹数量限制")
	}

	if rc.DayTbeanValueLimit > 0 && rc.DayTbeanValueLimit < uint64(tbeanValue) {
		log.ErrorWithCtx(ctx,"CheckSingleRiskControl T豆天余额不足 orderInfo:%v", orderInfo)
		return protocol.NewServerError(status.ErrRiskControlBackpackTbeanLimit, "T豆天余额不足")
	}

	if rc.DayCntLimit > 0 && rc.DayCntLimit < uint64(orderInfo.BackpackCnt) {
		log.ErrorWithCtx(ctx,"CheckSingleRiskControl 包裹天发放数量限制 orderInfo:%v", orderInfo)
		return protocol.NewServerError(status.ErrRiskControlBackpackCountLimit, "包裹天发放数量限制")
	}
	if rc.HourTbeanValueLimit > 0 && rc.HourTbeanValueLimit < uint64(tbeanValue) {
		log.ErrorWithCtx(ctx,"CheckSingleRiskControl T豆小时余额不足 orderInfo:%v", orderInfo)
		return protocol.NewServerError(status.ErrRiskControlBackpackTbeanLimit, "T豆小时余额不足")
	}
	if rc.HourCntLimit > 0 && rc.HourCntLimit < uint64(orderInfo.BackpackCnt) {
		log.ErrorWithCtx(ctx,"CheckSingleRiskControl 包裹小时发放数量限制 orderInfo:%v", orderInfo)
		return protocol.NewServerError(status.ErrRiskControlBackpackCountLimit, "包裹小时发放数量限制")
	}

	tbName := utils.GenTableName("risk_control_business_data_%04v%02v", ts)
	hourStr := ts.Format("2006-01-02-15")
	dayStr := ts.Format("2006-01-02")

	//小时背包数量
	if rc.HourCntLimit > 0 {
		err := ExecSqlV2(tx, ctx, tbName, "backpack_cnt", hourStr, orderInfo.BusinessId, rc.BackpackID,
			orderInfo.BackpackCnt, rc.HourCntLimit-uint64(orderInfo.BackpackCnt))

		if nil != err {
			log.ErrorWithCtx(ctx,"ExecSql 包裹小时发放数量限制 orderInfo:%v err:%v", orderInfo, err)
			return protocol.NewServerError(status.ErrRiskControlBackpackCountLimit, "包裹小时发放数量限制")
		}
	}

	//天包裹数量
	if rc.DayCntLimit > 0 {
		err := ExecSqlV2(tx, ctx, tbName, "backpack_cnt", dayStr, orderInfo.BusinessId, rc.BackpackID,
			orderInfo.BackpackCnt, rc.DayCntLimit-uint64(orderInfo.BackpackCnt))
		if nil != err {
			log.ErrorWithCtx(ctx,"ExecSql 包裹天发放数量限制 orderInfo:%v err:%v", orderInfo, err)
			return protocol.NewServerError(status.ErrRiskControlBackpackCountLimit, "包裹天发放数量限制")
		}
	}

	//小时T豆
	if rc.HourTbeanValueLimit > 0 && tbeanValue > 0 {
		err := ExecSqlV2(tx, ctx, tbName, "tbean_value", hourStr, orderInfo.BusinessId,
			rc.BackpackID, tbeanValue, rc.HourTbeanValueLimit-uint64(tbeanValue))

		if nil != err {
			log.ErrorWithCtx(ctx,"ExecSql T豆小时余额不足 orderInfo:%v err:%v", orderInfo, err)
			return protocol.NewServerError(status.ErrRiskControlBackpackTbeanLimit, "T豆小时余额不足")
		}
	}

	//天T豆
	if rc.DayTbeanValueLimit > 0 && tbeanValue > 0 {
		err := ExecSqlV2(tx, ctx, tbName, "tbean_value", dayStr, orderInfo.BusinessId,
			rc.BackpackID, tbeanValue, rc.DayTbeanValueLimit-uint64(tbeanValue))

		if nil != err {
			log.ErrorWithCtx(ctx,"ExecSql T豆天余额不足 orderInfo:%v err:%v", orderInfo, err)
			return protocol.NewServerError(status.ErrRiskControlBackpackTbeanLimit, "T豆天余额不足")
		}
	}

	return nil
}*/

/*func (s *Store) CheckRiskControl(ctx context.Context, tx *sqlx.Tx, ts time.Time, orderInfo *pb.SendBackpackOrderInfo, tbeanValue uint32, rcList []*RiskControlConf) protocol.ServerError {

	//逐个检查多个风控配置
	bHasRickConf := false //是否有配置对应的风控配置项，可以是按T豆价值限制，或者是具体包裹ID限制
	var serr protocol.ServerError
	for _, rc := range rcList {
		//backpackID == 0 业务整体的T豆价值和包裹数量限制。 ！= 0 具体的包裹ID数量限制
		if rc.BackpackID != 0 && rc.BackpackID != orderInfo.BackpackId {
			continue
		}
		bHasRickConf = true
		serr = s.CheckSingleRiskControl(tx, ctx, ts, rc, orderInfo, tbeanValue)
		if nil != serr {
			break
		}
	}

	if !bHasRickConf {
		log.ErrorWithCtx(ctx,"CheckRiskControl not risk conf businessID:%v backpackID:%v", orderInfo.BusinessId, orderInfo.BackpackId)
		return protocol.NewServerError(status.ErrRiskControlBackpackBusinessNotFound, "没有配置对应包裹的风控配置")
	}

	if serr != nil {
		log.ErrorWithCtx(ctx,"CheckRiskControl CheckSingleRiskControl serr:%v", serr)
		return serr
	}

	return nil
}*/

// 订单入库
func (s *Store) AddSendBackpackOrder(ctx context.Context, tx *sqlx.Tx, ts time.Time, orderInfo *pb.SendBackpackOrderInfo) protocol.ServerError {

	outsideTime := time.Unix(orderInfo.OutsideTime, 0).Format("2006-01-02 15:04:05")
	serverTime := time.Unix(orderInfo.ServerTime, 0).Format("2006-01-02 15:04:05")

	//如果是1号，查查上个月
	if ts.Day() == 1 {
		var count = 0
		lastMonthTable := utils.GenTableName(OrderTableName, time.Unix(ts.Unix()-3600*24, 0))
		sql := fmt.Sprintf("select count(order_id) from %v where order_id='%v'", lastMonthTable, orderInfo.OrderId)
		err := tx.GetContext(ctx, &count, sql)
		if nil != err {
			log.ErrorWithCtx(ctx, "%v AddSendBackpackOrder GetContext err:%v", utils.SvrName, err)
		}
		if count == 1 {
			return protocol.NewServerError(status.ErrRiskControlBackpackDuplicateOrderid, "重复订单")
		}
	}

	insertSql := fmt.Sprintf("insert into %v (order_id,business_id,backpack_id,receive_uid,source_id,count,expire_duration,source_app_id,outside_time,create_time,traffic_mark)"+
		" values('%v',%v,%v,%v,%v,%v,%v,'%v','%v','%v', '%v')", utils.GenTableName(OrderTableName, ts), orderInfo.OrderId, orderInfo.BusinessId, orderInfo.BackpackId,
		orderInfo.ReceiveUid, orderInfo.SourceId, orderInfo.BackpackCnt, orderInfo.ExpireDuration, orderInfo.SourceAppId, outsideTime, serverTime, orderInfo.TrafficMark)

	_, err := tx.ExecContext(ctx, insertSql)

	if nil == err {
		return nil
	}

	bCreateTable := false
	if driverErr, ok := err.(*mysql.MySQLError); ok {
		if driverErr.Number == 1146 { //table doesn't exist
			createSql := fmt.Sprintf(CreateMonthlyOrderTableSql, utils.GenTableName(OrderTableName, ts))
			_, err = tx.ExecContext(ctx, createSql)
			if nil == err {
				bCreateTable = true
			}
		}
		if driverErr.Number == 1062 { //duplicate order
			return protocol.NewServerError(status.ErrRiskControlBackpackDuplicateOrderid, "重复订单")
		}
	}

	if !bCreateTable {
		return protocol.NewServerError(status.ErrRiskControlBackpackSysFail, err.Error())
	}

	_, err = tx.ExecContext(ctx, insertSql)
	if nil != err {
		return protocol.NewServerError(status.ErrRiskControlBackpackSysFail, err.Error())
	}

	return nil
}

func (s *Store) GetSendBackpackOrderByTimeRange(beginTs, endTs time.Time) ([]*OrderInfoStu, error) {
	//如果跨月需要查两个月
	beginTableName := utils.GenTableName(OrderTableName, beginTs)
	tempSQL := "select order_id, business_id, backpack_id, receive_uid, source_id,count,expire_duration,source_app_id,outside_time, " +
		"create_time, traffic_mark from %v where create_time>='%v' and create_time<'%v' and order_status=0 limit 512;"

	orders := make([]*OrderInfoStu, 0)

	ctx := context.Background()

	sql := fmt.Sprintf(tempSQL, beginTableName, utils.GetTimeStr(beginTs), utils.GetTimeStr(endTs))

	err := s.DB.SelectContext(ctx, &orders, sql)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetSendBackpackOrderByTimeRange SelectContext TableName:%v err:%v", beginTableName, err)
		return orders, err
	}

	if beginTs.Month() != endTs.Month() {
		endTableName := utils.GenTableName(OrderTableName, beginTs)
		sql := fmt.Sprintf(tempSQL, endTableName, utils.GetTimeStr(beginTs), utils.GetTimeStr(endTs))
		tmpOrders := make([]*OrderInfoStu, 0)
		err := s.DB.SelectContext(ctx, &tmpOrders, sql)
		if nil != err {
			log.ErrorWithCtx(ctx, "GetSendBackpackOrderByTimeRange SelectContext TableName:%v err:%v", endTableName, err)
		} else {
			orders = append(orders, tmpOrders...)
		}
	}

	return orders, nil
}

func (d *Store) UpdateOrderStatus(orderInfo *pb.SendBackpackOrderInfo, status int) error {
	createTime := time.Unix(orderInfo.ServerTime, 0)
	nowTime := time.Now()
	tbName := utils.GenTableName(OrderTableName, createTime)
	sql := fmt.Sprintf("update %v set order_status=%v, update_time='%v' where order_id='%v'", tbName, status, utils.GetTimeStr(nowTime), orderInfo.OrderId)
	_, err := d.DB.Exec(sql)
	return err
}

func (d *Store) GetOrderCount(beginTs, endTs time.Time, status int) (int, error) {

	sqltemplate := "select count(order_id) from %v where create_time>='%v' and create_time<'%v' and order_status=%v"

	tbName := utils.GenTableName(OrderTableName, beginTs)
	sql := fmt.Sprintf(sqltemplate, tbName, utils.GetTimeStr(beginTs), utils.GetTimeStr(endTs), status)

	var count = 0
	ctx := context.Background()
	err := d.DB.GetContext(ctx, &count, sql)
	if nil != err {
		log.ErrorWithCtx(ctx, "%v GetOrderCount err:%v", utils.SvrName, err)
	}

	//查这个月数据
	if beginTs.Month() != endTs.Month() {
		var lastMonthCount = 0
		tbName := utils.GenTableName(OrderTableName, endTs)
		sql := fmt.Sprintf(sqltemplate, tbName, utils.GetTimeStr(beginTs), utils.GetTimeStr(endTs), status)
		err := d.DB.GetContext(ctx, &lastMonthCount, sql)
		if nil != err {
			log.ErrorWithCtx(ctx, "%v GetOrderCount err:%v", utils.SvrName, err)
		}
		count = count + lastMonthCount
	}

	return count, err
}

func TransfromOrder(stuOrder *OrderInfoStu) *pb.SendBackpackOrderInfo {
	pbOrder := &pb.SendBackpackOrderInfo{
		BusinessId:  stuOrder.BusinessID,
		BackpackId:  stuOrder.BackpackID,
		ReceiveUid:  stuOrder.ReceiveUid,
		BackpackCnt: stuOrder.Count,
		ServerTime:  stuOrder.CreateTime.Unix(),
		OrderId:     stuOrder.OrderId,
		OutsideTime: stuOrder.OutsideTime.Unix(),
		SourceId:    stuOrder.SourceID,
		TrafficMark: stuOrder.TrafficMark,
	}
	return pbOrder
}
