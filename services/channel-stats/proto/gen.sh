result=`protoc -I=/mnt/hgfs/gopath/src/golang.52tt.com/services/channel-stats/proto  --go_out=plugins=grpc:/mnt/hgfs/gopath/src channel-stats.proto` 
if [ -n "$result" ] 
then  
echo "protoc err:"  
echo $result  
exit  
fi 
result=`cp /mnt/hgfs/gopath/src/golang.52tt.com/services/channel-stats/proto/channel-stats.proto /mnt/hgfs/gopath/src/golang.52tt.com/third-party/tt-protocol/service/quicksilver/channel-stats` 
if [ -n "$result" ] 
then 
echo "cp err:" 
echo $result 
 exit  
fi 
