syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channelstats";
package channelstats;


service ChannelStats {
    //取房间礼物热度值
    rpc GetPresentHotValue (GetPresentHotValueReq) returns (GetPresentHotValueResp) {
    }

    rpc BatchGetPresentHotValue (BatchGetPresentHotValueReq) returns (BatchGetPresentHotValueResp) {
    }


    rpc GetChannelHotValue (GetChannelHotValueReq) returns (GetChannelHotValueResp) {
    }

    rpc BatchGetChannelHotValue (BatchGetChannelHotValueReq) returns (BatchGetChannelHotValueResp) {
    }
}

message HotValueItem {
    string order_id = 1;
    int64 value = 2;
}

message GetPresentHotValueReq {
    uint32 channel_id = 1;
}

message GetPresentHotValueResp {
    int64 present_hot_value = 1;
}

message BatchGetPresentHotValueReq {
    repeated uint32 channel_id_list = 1;
}

message BatchGetPresentHotValueResp {
    repeated int64 present_hot_value_list = 1;
}

message GetChannelHotValueReq {
    uint32 channel_id = 1;
    uint32 channel_type = 2;
}

message GetChannelHotValueResp {
    int64 channel_hot_value = 1;
    int64 member_cnt_factor = 2;
}

message BatchGetChannelHotValueReq {
    repeated uint32 channel_id_list = 1;
}

message BatchGetChannelHotValueResp {
    map<uint32,int64> channel_hot_value_map = 1;
}