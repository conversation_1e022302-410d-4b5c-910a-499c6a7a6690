// Code generated by protoc-gen-go. DO NOT EDIT.
// source: golddiamond-logic.proto

package gold_diamond_logic

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RangeType int32

const (
	RangeType_DAY_RANGE_TYPE   RangeType = 0
	RangeType_MONTH_RANGE_TYPE RangeType = 1
)

var RangeType_name = map[int32]string{
	0: "DAY_RANGE_TYPE",
	1: "MONTH_RANGE_TYPE",
}
var RangeType_value = map[string]int32{
	"DAY_RANGE_TYPE":   0,
	"MONTH_RANGE_TYPE": 1,
}

func (x RangeType) String() string {
	return proto.EnumName(RangeType_name, int32(x))
}
func (RangeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{0}
}

type ErrorCode int32

const (
	ErrorCode_ERROR_CODE_DEFAULT   ErrorCode = 0
	ErrorCode_ERROR_CODE_PARR      ErrorCode = 3001
	ErrorCode_ERROR_CODE_UNMARSHAL ErrorCode = 3002
	ErrorCode_ERROR_CODE_INVALID   ErrorCode = 3003
	ErrorCode_ERROR_CODE_NETWORK   ErrorCode = 3004
)

var ErrorCode_name = map[int32]string{
	0:    "ERROR_CODE_DEFAULT",
	3001: "ERROR_CODE_PARR",
	3002: "ERROR_CODE_UNMARSHAL",
	3003: "ERROR_CODE_INVALID",
	3004: "ERROR_CODE_NETWORK",
}
var ErrorCode_value = map[string]int32{
	"ERROR_CODE_DEFAULT":   0,
	"ERROR_CODE_PARR":      3001,
	"ERROR_CODE_UNMARSHAL": 3002,
	"ERROR_CODE_INVALID":   3003,
	"ERROR_CODE_NETWORK":   3004,
}

func (x ErrorCode) String() string {
	return proto.EnumName(ErrorCode_name, int32(x))
}
func (ErrorCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{1}
}

// 获取考核标签
type ExamTagType int32

const (
	ExamTagType_TAG_DEFAULT   ExamTagType = 0
	ExamTagType_TAG_RECOMMEND ExamTagType = 1
	ExamTagType_TAG_YUYIN     ExamTagType = 2
)

var ExamTagType_name = map[int32]string{
	0: "TAG_DEFAULT",
	1: "TAG_RECOMMEND",
	2: "TAG_YUYIN",
}
var ExamTagType_value = map[string]int32{
	"TAG_DEFAULT":   0,
	"TAG_RECOMMEND": 1,
	"TAG_YUYIN":     2,
}

func (x ExamTagType) String() string {
	return proto.EnumName(ExamTagType_name, int32(x))
}
func (ExamTagType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{2}
}

// 语音直播
type YuyinExamStatus int32

const (
	YuyinExamStatus_YE_DEFAULT   YuyinExamStatus = 0
	YuyinExamStatus_YE_COMMITTED YuyinExamStatus = 1
	YuyinExamStatus_YE_REJECTED  YuyinExamStatus = 2
)

var YuyinExamStatus_name = map[int32]string{
	0: "YE_DEFAULT",
	1: "YE_COMMITTED",
	2: "YE_REJECTED",
}
var YuyinExamStatus_value = map[string]int32{
	"YE_DEFAULT":   0,
	"YE_COMMITTED": 1,
	"YE_REJECTED":  2,
}

func (x YuyinExamStatus) String() string {
	return proto.EnumName(YuyinExamStatus_name, int32(x))
}
func (YuyinExamStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{3}
}

type OperateInfoT struct {
	MonthIncome          string   `protobuf:"bytes,1,opt,name=month_income,json=monthIncome,proto3" json:"month_income"`
	IncomeImgs           []string `protobuf:"bytes,2,rep,name=income_imgs,json=incomeImgs,proto3" json:"income_imgs"`
	Consume              string   `protobuf:"bytes,3,opt,name=consume,proto3" json:"consume"`
	ConsumeImgs          []string `protobuf:"bytes,4,rep,name=consume_imgs,json=consumeImgs,proto3" json:"consume_imgs"`
	Teamsize             uint32   `protobuf:"varint,5,opt,name=teamsize,proto3" json:"teamsize"`
	Accompanys           []string `protobuf:"bytes,6,rep,name=accompanys,proto3" json:"accompanys"`
	EstimateTeams        uint32   `protobuf:"varint,7,opt,name=estimate_teams,json=estimateTeams,proto3" json:"estimate_teams"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OperateInfoT) Reset()         { *m = OperateInfoT{} }
func (m *OperateInfoT) String() string { return proto.CompactTextString(m) }
func (*OperateInfoT) ProtoMessage()    {}
func (*OperateInfoT) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{0}
}
func (m *OperateInfoT) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OperateInfoT.Unmarshal(m, b)
}
func (m *OperateInfoT) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OperateInfoT.Marshal(b, m, deterministic)
}
func (dst *OperateInfoT) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OperateInfoT.Merge(dst, src)
}
func (m *OperateInfoT) XXX_Size() int {
	return xxx_messageInfo_OperateInfoT.Size(m)
}
func (m *OperateInfoT) XXX_DiscardUnknown() {
	xxx_messageInfo_OperateInfoT.DiscardUnknown(m)
}

var xxx_messageInfo_OperateInfoT proto.InternalMessageInfo

func (m *OperateInfoT) GetMonthIncome() string {
	if m != nil {
		return m.MonthIncome
	}
	return ""
}

func (m *OperateInfoT) GetIncomeImgs() []string {
	if m != nil {
		return m.IncomeImgs
	}
	return nil
}

func (m *OperateInfoT) GetConsume() string {
	if m != nil {
		return m.Consume
	}
	return ""
}

func (m *OperateInfoT) GetConsumeImgs() []string {
	if m != nil {
		return m.ConsumeImgs
	}
	return nil
}

func (m *OperateInfoT) GetTeamsize() uint32 {
	if m != nil {
		return m.Teamsize
	}
	return 0
}

func (m *OperateInfoT) GetAccompanys() []string {
	if m != nil {
		return m.Accompanys
	}
	return nil
}

func (m *OperateInfoT) GetEstimateTeams() uint32 {
	if m != nil {
		return m.EstimateTeams
	}
	return 0
}

type ApplicationCooperateReq struct {
	Guildid               uint32        `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid"`
	ApplicationType       uint32        `protobuf:"varint,2,opt,name=application_type,json=applicationType,proto3" json:"application_type"`
	PlatformName          string        `protobuf:"bytes,3,opt,name=platform_name,json=platformName,proto3" json:"platform_name"`
	PlatformContent       string        `protobuf:"bytes,4,opt,name=platform_content,json=platformContent,proto3" json:"platform_content"`
	PlatformId            uint32        `protobuf:"varint,5,opt,name=platform_id,json=platformId,proto3" json:"platform_id"`
	PlatformCertification []string      `protobuf:"bytes,6,rep,name=platform_certification,json=platformCertification,proto3" json:"platform_certification"`
	PlatformIdStr         string        `protobuf:"bytes,7,opt,name=platform_id_str,json=platformIdStr,proto3" json:"platform_id_str"`
	ApplyType             uint32        `protobuf:"varint,8,opt,name=apply_type,json=applyType,proto3" json:"apply_type"`
	Ttguild               uint32        `protobuf:"varint,9,opt,name=ttguild,proto3" json:"ttguild"`
	PlatformGuildType     uint32        `protobuf:"varint,10,opt,name=platform_guild_type,json=platformGuildType,proto3" json:"platform_guild_type"`
	PlatformGuildid       string        `protobuf:"bytes,11,opt,name=platform_guildid,json=platformGuildid,proto3" json:"platform_guildid"`
	PlatformGuildOwnerId  string        `protobuf:"bytes,12,opt,name=platform_guild_ownerId,json=platformGuildOwnerId,proto3" json:"platform_guild_ownerId"`
	PlatformGuildAuthimgs []string      `protobuf:"bytes,13,rep,name=platform_guild_authimgs,json=platformGuildAuthimgs,proto3" json:"platform_guild_authimgs"`
	EstablishTime         string        `protobuf:"bytes,14,opt,name=establish_time,json=establishTime,proto3" json:"establish_time"`
	Operate               *OperateInfoT `protobuf:"bytes,15,opt,name=operate,proto3" json:"operate"`
	Estimates             uint32        `protobuf:"varint,16,opt,name=estimates,proto3" json:"estimates"`
	Contact               string        `protobuf:"bytes,17,opt,name=contact,proto3" json:"contact"`
	XXX_NoUnkeyedLiteral  struct{}      `json:"-"`
	XXX_unrecognized      []byte        `json:"-"`
	XXX_sizecache         int32         `json:"-"`
}

func (m *ApplicationCooperateReq) Reset()         { *m = ApplicationCooperateReq{} }
func (m *ApplicationCooperateReq) String() string { return proto.CompactTextString(m) }
func (*ApplicationCooperateReq) ProtoMessage()    {}
func (*ApplicationCooperateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{1}
}
func (m *ApplicationCooperateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationCooperateReq.Unmarshal(m, b)
}
func (m *ApplicationCooperateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationCooperateReq.Marshal(b, m, deterministic)
}
func (dst *ApplicationCooperateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationCooperateReq.Merge(dst, src)
}
func (m *ApplicationCooperateReq) XXX_Size() int {
	return xxx_messageInfo_ApplicationCooperateReq.Size(m)
}
func (m *ApplicationCooperateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationCooperateReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationCooperateReq proto.InternalMessageInfo

func (m *ApplicationCooperateReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *ApplicationCooperateReq) GetApplicationType() uint32 {
	if m != nil {
		return m.ApplicationType
	}
	return 0
}

func (m *ApplicationCooperateReq) GetPlatformName() string {
	if m != nil {
		return m.PlatformName
	}
	return ""
}

func (m *ApplicationCooperateReq) GetPlatformContent() string {
	if m != nil {
		return m.PlatformContent
	}
	return ""
}

func (m *ApplicationCooperateReq) GetPlatformId() uint32 {
	if m != nil {
		return m.PlatformId
	}
	return 0
}

func (m *ApplicationCooperateReq) GetPlatformCertification() []string {
	if m != nil {
		return m.PlatformCertification
	}
	return nil
}

func (m *ApplicationCooperateReq) GetPlatformIdStr() string {
	if m != nil {
		return m.PlatformIdStr
	}
	return ""
}

func (m *ApplicationCooperateReq) GetApplyType() uint32 {
	if m != nil {
		return m.ApplyType
	}
	return 0
}

func (m *ApplicationCooperateReq) GetTtguild() uint32 {
	if m != nil {
		return m.Ttguild
	}
	return 0
}

func (m *ApplicationCooperateReq) GetPlatformGuildType() uint32 {
	if m != nil {
		return m.PlatformGuildType
	}
	return 0
}

func (m *ApplicationCooperateReq) GetPlatformGuildid() string {
	if m != nil {
		return m.PlatformGuildid
	}
	return ""
}

func (m *ApplicationCooperateReq) GetPlatformGuildOwnerId() string {
	if m != nil {
		return m.PlatformGuildOwnerId
	}
	return ""
}

func (m *ApplicationCooperateReq) GetPlatformGuildAuthimgs() []string {
	if m != nil {
		return m.PlatformGuildAuthimgs
	}
	return nil
}

func (m *ApplicationCooperateReq) GetEstablishTime() string {
	if m != nil {
		return m.EstablishTime
	}
	return ""
}

func (m *ApplicationCooperateReq) GetOperate() *OperateInfoT {
	if m != nil {
		return m.Operate
	}
	return nil
}

func (m *ApplicationCooperateReq) GetEstimates() uint32 {
	if m != nil {
		return m.Estimates
	}
	return 0
}

func (m *ApplicationCooperateReq) GetContact() string {
	if m != nil {
		return m.Contact
	}
	return ""
}

type ApplicationCooperateRsp struct {
	IsApp                bool     `protobuf:"varint,1,opt,name=is_app,json=isApp,proto3" json:"is_app"`
	IsLimit              bool     `protobuf:"varint,2,opt,name=is_limit,json=isLimit,proto3" json:"is_limit"`
	LimitEndTime         int64    `protobuf:"varint,3,opt,name=limit_end_time,json=limitEndTime,proto3" json:"limit_end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplicationCooperateRsp) Reset()         { *m = ApplicationCooperateRsp{} }
func (m *ApplicationCooperateRsp) String() string { return proto.CompactTextString(m) }
func (*ApplicationCooperateRsp) ProtoMessage()    {}
func (*ApplicationCooperateRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{2}
}
func (m *ApplicationCooperateRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationCooperateRsp.Unmarshal(m, b)
}
func (m *ApplicationCooperateRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationCooperateRsp.Marshal(b, m, deterministic)
}
func (dst *ApplicationCooperateRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationCooperateRsp.Merge(dst, src)
}
func (m *ApplicationCooperateRsp) XXX_Size() int {
	return xxx_messageInfo_ApplicationCooperateRsp.Size(m)
}
func (m *ApplicationCooperateRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationCooperateRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationCooperateRsp proto.InternalMessageInfo

func (m *ApplicationCooperateRsp) GetIsApp() bool {
	if m != nil {
		return m.IsApp
	}
	return false
}

func (m *ApplicationCooperateRsp) GetIsLimit() bool {
	if m != nil {
		return m.IsLimit
	}
	return false
}

func (m *ApplicationCooperateRsp) GetLimitEndTime() int64 {
	if m != nil {
		return m.LimitEndTime
	}
	return 0
}

type CooperateReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid"`
	Applied              uint32   `protobuf:"varint,2,opt,name=applied,proto3" json:"applied"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CooperateReq) Reset()         { *m = CooperateReq{} }
func (m *CooperateReq) String() string { return proto.CompactTextString(m) }
func (*CooperateReq) ProtoMessage()    {}
func (*CooperateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{3}
}
func (m *CooperateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CooperateReq.Unmarshal(m, b)
}
func (m *CooperateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CooperateReq.Marshal(b, m, deterministic)
}
func (dst *CooperateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CooperateReq.Merge(dst, src)
}
func (m *CooperateReq) XXX_Size() int {
	return xxx_messageInfo_CooperateReq.Size(m)
}
func (m *CooperateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CooperateReq.DiscardUnknown(m)
}

var xxx_messageInfo_CooperateReq proto.InternalMessageInfo

func (m *CooperateReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *CooperateReq) GetApplied() uint32 {
	if m != nil {
		return m.Applied
	}
	return 0
}

type CooperateRsp struct {
	Applied              uint32   `protobuf:"varint,1,opt,name=applied,proto3" json:"applied"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CooperateRsp) Reset()         { *m = CooperateRsp{} }
func (m *CooperateRsp) String() string { return proto.CompactTextString(m) }
func (*CooperateRsp) ProtoMessage()    {}
func (*CooperateRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{4}
}
func (m *CooperateRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CooperateRsp.Unmarshal(m, b)
}
func (m *CooperateRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CooperateRsp.Marshal(b, m, deterministic)
}
func (dst *CooperateRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CooperateRsp.Merge(dst, src)
}
func (m *CooperateRsp) XXX_Size() int {
	return xxx_messageInfo_CooperateRsp.Size(m)
}
func (m *CooperateRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_CooperateRsp.DiscardUnknown(m)
}

var xxx_messageInfo_CooperateRsp proto.InternalMessageInfo

func (m *CooperateRsp) GetApplied() uint32 {
	if m != nil {
		return m.Applied
	}
	return 0
}

type ChannelListReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelListReq) Reset()         { *m = ChannelListReq{} }
func (m *ChannelListReq) String() string { return proto.CompactTextString(m) }
func (*ChannelListReq) ProtoMessage()    {}
func (*ChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{5}
}
func (m *ChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListReq.Unmarshal(m, b)
}
func (m *ChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListReq.Marshal(b, m, deterministic)
}
func (dst *ChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListReq.Merge(dst, src)
}
func (m *ChannelListReq) XXX_Size() int {
	return xxx_messageInfo_ChannelListReq.Size(m)
}
func (m *ChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListReq proto.InternalMessageInfo

func (m *ChannelListReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type Channelinfot struct {
	Channelid            uint32   `protobuf:"varint,1,opt,name=channelid,proto3" json:"channelid"`
	Displayid            uint32   `protobuf:"varint,2,opt,name=displayid,proto3" json:"displayid"`
	ChannelName          string   `protobuf:"bytes,3,opt,name=channel_name,json=channelName,proto3" json:"channel_name"`
	ChannelViewId        string   `protobuf:"bytes,4,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Channelinfot) Reset()         { *m = Channelinfot{} }
func (m *Channelinfot) String() string { return proto.CompactTextString(m) }
func (*Channelinfot) ProtoMessage()    {}
func (*Channelinfot) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{6}
}
func (m *Channelinfot) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Channelinfot.Unmarshal(m, b)
}
func (m *Channelinfot) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Channelinfot.Marshal(b, m, deterministic)
}
func (dst *Channelinfot) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Channelinfot.Merge(dst, src)
}
func (m *Channelinfot) XXX_Size() int {
	return xxx_messageInfo_Channelinfot.Size(m)
}
func (m *Channelinfot) XXX_DiscardUnknown() {
	xxx_messageInfo_Channelinfot.DiscardUnknown(m)
}

var xxx_messageInfo_Channelinfot proto.InternalMessageInfo

func (m *Channelinfot) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *Channelinfot) GetDisplayid() uint32 {
	if m != nil {
		return m.Displayid
	}
	return 0
}

func (m *Channelinfot) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *Channelinfot) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type ChannelListRsp struct {
	ChannelList          []*Channelinfot `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ChannelListRsp) Reset()         { *m = ChannelListRsp{} }
func (m *ChannelListRsp) String() string { return proto.CompactTextString(m) }
func (*ChannelListRsp) ProtoMessage()    {}
func (*ChannelListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{7}
}
func (m *ChannelListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListRsp.Unmarshal(m, b)
}
func (m *ChannelListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListRsp.Marshal(b, m, deterministic)
}
func (dst *ChannelListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListRsp.Merge(dst, src)
}
func (m *ChannelListRsp) XXX_Size() int {
	return xxx_messageInfo_ChannelListRsp.Size(m)
}
func (m *ChannelListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListRsp proto.InternalMessageInfo

func (m *ChannelListRsp) GetChannelList() []*Channelinfot {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type PendingReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PendingReq) Reset()         { *m = PendingReq{} }
func (m *PendingReq) String() string { return proto.CompactTextString(m) }
func (*PendingReq) ProtoMessage()    {}
func (*PendingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{8}
}
func (m *PendingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PendingReq.Unmarshal(m, b)
}
func (m *PendingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PendingReq.Marshal(b, m, deterministic)
}
func (dst *PendingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PendingReq.Merge(dst, src)
}
func (m *PendingReq) XXX_Size() int {
	return xxx_messageInfo_PendingReq.Size(m)
}
func (m *PendingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PendingReq.DiscardUnknown(m)
}

var xxx_messageInfo_PendingReq proto.InternalMessageInfo

func (m *PendingReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type PendingRsp struct {
	IncomeBalance        int64    `protobuf:"varint,1,opt,name=income_balance,json=incomeBalance,proto3" json:"income_balance"`
	Members              int64    `protobuf:"varint,2,opt,name=members,proto3" json:"members"`
	Consume              int64    `protobuf:"varint,3,opt,name=consume,proto3" json:"consume"`
	StartTime            int64    `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime              int64    `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PendingRsp) Reset()         { *m = PendingRsp{} }
func (m *PendingRsp) String() string { return proto.CompactTextString(m) }
func (*PendingRsp) ProtoMessage()    {}
func (*PendingRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{9}
}
func (m *PendingRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PendingRsp.Unmarshal(m, b)
}
func (m *PendingRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PendingRsp.Marshal(b, m, deterministic)
}
func (dst *PendingRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PendingRsp.Merge(dst, src)
}
func (m *PendingRsp) XXX_Size() int {
	return xxx_messageInfo_PendingRsp.Size(m)
}
func (m *PendingRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_PendingRsp.DiscardUnknown(m)
}

var xxx_messageInfo_PendingRsp proto.InternalMessageInfo

func (m *PendingRsp) GetIncomeBalance() int64 {
	if m != nil {
		return m.IncomeBalance
	}
	return 0
}

func (m *PendingRsp) GetMembers() int64 {
	if m != nil {
		return m.Members
	}
	return 0
}

func (m *PendingRsp) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *PendingRsp) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *PendingRsp) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type PendingDetailReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid"`
	QueryType            uint32   `protobuf:"varint,2,opt,name=query_type,json=queryType,proto3" json:"query_type"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page"`
	PageNum              uint32   `protobuf:"varint,4,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PendingDetailReq) Reset()         { *m = PendingDetailReq{} }
func (m *PendingDetailReq) String() string { return proto.CompactTextString(m) }
func (*PendingDetailReq) ProtoMessage()    {}
func (*PendingDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{10}
}
func (m *PendingDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PendingDetailReq.Unmarshal(m, b)
}
func (m *PendingDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PendingDetailReq.Marshal(b, m, deterministic)
}
func (dst *PendingDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PendingDetailReq.Merge(dst, src)
}
func (m *PendingDetailReq) XXX_Size() int {
	return xxx_messageInfo_PendingDetailReq.Size(m)
}
func (m *PendingDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PendingDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_PendingDetailReq proto.InternalMessageInfo

func (m *PendingDetailReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *PendingDetailReq) GetQueryType() uint32 {
	if m != nil {
		return m.QueryType
	}
	return 0
}

func (m *PendingDetailReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *PendingDetailReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type PendingDetail struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Channelid            uint32   `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	ChannelName          string   `protobuf:"bytes,4,opt,name=channel_name,json=channelName,proto3" json:"channel_name"`
	Consume              int64    `protobuf:"varint,5,opt,name=consume,proto3" json:"consume"`
	Income               int64    `protobuf:"varint,6,opt,name=income,proto3" json:"income"`
	ChannelViewId        string   `protobuf:"bytes,7,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PendingDetail) Reset()         { *m = PendingDetail{} }
func (m *PendingDetail) String() string { return proto.CompactTextString(m) }
func (*PendingDetail) ProtoMessage()    {}
func (*PendingDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{11}
}
func (m *PendingDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PendingDetail.Unmarshal(m, b)
}
func (m *PendingDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PendingDetail.Marshal(b, m, deterministic)
}
func (dst *PendingDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PendingDetail.Merge(dst, src)
}
func (m *PendingDetail) XXX_Size() int {
	return xxx_messageInfo_PendingDetail.Size(m)
}
func (m *PendingDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_PendingDetail.DiscardUnknown(m)
}

var xxx_messageInfo_PendingDetail proto.InternalMessageInfo

func (m *PendingDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PendingDetail) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *PendingDetail) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PendingDetail) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *PendingDetail) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *PendingDetail) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *PendingDetail) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type PendingDetailRsp struct {
	PendingList          []*PendingDetail `protobuf:"bytes,1,rep,name=pending_list,json=pendingList,proto3" json:"pending_list"`
	NextPage             bool             `protobuf:"varint,2,opt,name=next_page,json=nextPage,proto3" json:"next_page"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PendingDetailRsp) Reset()         { *m = PendingDetailRsp{} }
func (m *PendingDetailRsp) String() string { return proto.CompactTextString(m) }
func (*PendingDetailRsp) ProtoMessage()    {}
func (*PendingDetailRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{12}
}
func (m *PendingDetailRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PendingDetailRsp.Unmarshal(m, b)
}
func (m *PendingDetailRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PendingDetailRsp.Marshal(b, m, deterministic)
}
func (dst *PendingDetailRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PendingDetailRsp.Merge(dst, src)
}
func (m *PendingDetailRsp) XXX_Size() int {
	return xxx_messageInfo_PendingDetailRsp.Size(m)
}
func (m *PendingDetailRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_PendingDetailRsp.DiscardUnknown(m)
}

var xxx_messageInfo_PendingDetailRsp proto.InternalMessageInfo

func (m *PendingDetailRsp) GetPendingList() []*PendingDetail {
	if m != nil {
		return m.PendingList
	}
	return nil
}

func (m *PendingDetailRsp) GetNextPage() bool {
	if m != nil {
		return m.NextPage
	}
	return false
}

type YuyinPendingDetail struct {
	PaidUid              uint32   `protobuf:"varint,1,opt,name=paid_uid,json=paidUid,proto3" json:"paid_uid"`
	AnchorId             uint32   `protobuf:"varint,2,opt,name=anchor_id,json=anchorId,proto3" json:"anchor_id"`
	PaidName             string   `protobuf:"bytes,3,opt,name=paid_name,json=paidName,proto3" json:"paid_name"`
	AnchorName           string   `protobuf:"bytes,4,opt,name=anchor_name,json=anchorName,proto3" json:"anchor_name"`
	Consume              int64    `protobuf:"varint,5,opt,name=consume,proto3" json:"consume"`
	Income               int64    `protobuf:"varint,6,opt,name=income,proto3" json:"income"`
	PaidTtid             string   `protobuf:"bytes,7,opt,name=paid_ttid,json=paidTtid,proto3" json:"paid_ttid"`
	AnchorTtid           string   `protobuf:"bytes,8,opt,name=anchor_ttid,json=anchorTtid,proto3" json:"anchor_ttid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinPendingDetail) Reset()         { *m = YuyinPendingDetail{} }
func (m *YuyinPendingDetail) String() string { return proto.CompactTextString(m) }
func (*YuyinPendingDetail) ProtoMessage()    {}
func (*YuyinPendingDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{13}
}
func (m *YuyinPendingDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinPendingDetail.Unmarshal(m, b)
}
func (m *YuyinPendingDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinPendingDetail.Marshal(b, m, deterministic)
}
func (dst *YuyinPendingDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinPendingDetail.Merge(dst, src)
}
func (m *YuyinPendingDetail) XXX_Size() int {
	return xxx_messageInfo_YuyinPendingDetail.Size(m)
}
func (m *YuyinPendingDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinPendingDetail.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinPendingDetail proto.InternalMessageInfo

func (m *YuyinPendingDetail) GetPaidUid() uint32 {
	if m != nil {
		return m.PaidUid
	}
	return 0
}

func (m *YuyinPendingDetail) GetAnchorId() uint32 {
	if m != nil {
		return m.AnchorId
	}
	return 0
}

func (m *YuyinPendingDetail) GetPaidName() string {
	if m != nil {
		return m.PaidName
	}
	return ""
}

func (m *YuyinPendingDetail) GetAnchorName() string {
	if m != nil {
		return m.AnchorName
	}
	return ""
}

func (m *YuyinPendingDetail) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *YuyinPendingDetail) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *YuyinPendingDetail) GetPaidTtid() string {
	if m != nil {
		return m.PaidTtid
	}
	return ""
}

func (m *YuyinPendingDetail) GetAnchorTtid() string {
	if m != nil {
		return m.AnchorTtid
	}
	return ""
}

type YuyinPendingDetailRsp struct {
	PendingList          []*YuyinPendingDetail `protobuf:"bytes,1,rep,name=pending_list,json=pendingList,proto3" json:"pending_list"`
	NextPage             bool                  `protobuf:"varint,2,opt,name=next_page,json=nextPage,proto3" json:"next_page"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *YuyinPendingDetailRsp) Reset()         { *m = YuyinPendingDetailRsp{} }
func (m *YuyinPendingDetailRsp) String() string { return proto.CompactTextString(m) }
func (*YuyinPendingDetailRsp) ProtoMessage()    {}
func (*YuyinPendingDetailRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{14}
}
func (m *YuyinPendingDetailRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinPendingDetailRsp.Unmarshal(m, b)
}
func (m *YuyinPendingDetailRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinPendingDetailRsp.Marshal(b, m, deterministic)
}
func (dst *YuyinPendingDetailRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinPendingDetailRsp.Merge(dst, src)
}
func (m *YuyinPendingDetailRsp) XXX_Size() int {
	return xxx_messageInfo_YuyinPendingDetailRsp.Size(m)
}
func (m *YuyinPendingDetailRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinPendingDetailRsp.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinPendingDetailRsp proto.InternalMessageInfo

func (m *YuyinPendingDetailRsp) GetPendingList() []*YuyinPendingDetail {
	if m != nil {
		return m.PendingList
	}
	return nil
}

func (m *YuyinPendingDetailRsp) GetNextPage() bool {
	if m != nil {
		return m.NextPage
	}
	return false
}

type TodayIncomeReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TodayIncomeReq) Reset()         { *m = TodayIncomeReq{} }
func (m *TodayIncomeReq) String() string { return proto.CompactTextString(m) }
func (*TodayIncomeReq) ProtoMessage()    {}
func (*TodayIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{15}
}
func (m *TodayIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TodayIncomeReq.Unmarshal(m, b)
}
func (m *TodayIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TodayIncomeReq.Marshal(b, m, deterministic)
}
func (dst *TodayIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TodayIncomeReq.Merge(dst, src)
}
func (m *TodayIncomeReq) XXX_Size() int {
	return xxx_messageInfo_TodayIncomeReq.Size(m)
}
func (m *TodayIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TodayIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_TodayIncomeReq proto.InternalMessageInfo

func (m *TodayIncomeReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type TodayIncomeInfo struct {
	Channelid            uint32   `protobuf:"varint,1,opt,name=channelid,proto3" json:"channelid"`
	ChannelName          string   `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name"`
	Members              int64    `protobuf:"varint,3,opt,name=members,proto3" json:"members"`
	Consume              int64    `protobuf:"varint,4,opt,name=consume,proto3" json:"consume"`
	Income               int64    `protobuf:"varint,5,opt,name=income,proto3" json:"income"`
	ChannelViewId        string   `protobuf:"bytes,6,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TodayIncomeInfo) Reset()         { *m = TodayIncomeInfo{} }
func (m *TodayIncomeInfo) String() string { return proto.CompactTextString(m) }
func (*TodayIncomeInfo) ProtoMessage()    {}
func (*TodayIncomeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{16}
}
func (m *TodayIncomeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TodayIncomeInfo.Unmarshal(m, b)
}
func (m *TodayIncomeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TodayIncomeInfo.Marshal(b, m, deterministic)
}
func (dst *TodayIncomeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TodayIncomeInfo.Merge(dst, src)
}
func (m *TodayIncomeInfo) XXX_Size() int {
	return xxx_messageInfo_TodayIncomeInfo.Size(m)
}
func (m *TodayIncomeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TodayIncomeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TodayIncomeInfo proto.InternalMessageInfo

func (m *TodayIncomeInfo) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *TodayIncomeInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *TodayIncomeInfo) GetMembers() int64 {
	if m != nil {
		return m.Members
	}
	return 0
}

func (m *TodayIncomeInfo) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *TodayIncomeInfo) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *TodayIncomeInfo) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type TodayIncomeRsp struct {
	ChannelList          []*TodayIncomeInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *TodayIncomeRsp) Reset()         { *m = TodayIncomeRsp{} }
func (m *TodayIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*TodayIncomeRsp) ProtoMessage()    {}
func (*TodayIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{17}
}
func (m *TodayIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TodayIncomeRsp.Unmarshal(m, b)
}
func (m *TodayIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TodayIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *TodayIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TodayIncomeRsp.Merge(dst, src)
}
func (m *TodayIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_TodayIncomeRsp.Size(m)
}
func (m *TodayIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_TodayIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_TodayIncomeRsp proto.InternalMessageInfo

func (m *TodayIncomeRsp) GetChannelList() []*TodayIncomeInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type YuyinTodayIncomeReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinTodayIncomeReq) Reset()         { *m = YuyinTodayIncomeReq{} }
func (m *YuyinTodayIncomeReq) String() string { return proto.CompactTextString(m) }
func (*YuyinTodayIncomeReq) ProtoMessage()    {}
func (*YuyinTodayIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{18}
}
func (m *YuyinTodayIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinTodayIncomeReq.Unmarshal(m, b)
}
func (m *YuyinTodayIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinTodayIncomeReq.Marshal(b, m, deterministic)
}
func (dst *YuyinTodayIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinTodayIncomeReq.Merge(dst, src)
}
func (m *YuyinTodayIncomeReq) XXX_Size() int {
	return xxx_messageInfo_YuyinTodayIncomeReq.Size(m)
}
func (m *YuyinTodayIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinTodayIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinTodayIncomeReq proto.InternalMessageInfo

func (m *YuyinTodayIncomeReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type YuyinTodayIncomeInfo struct {
	AnchorName           string   `protobuf:"bytes,1,opt,name=anchor_name,json=anchorName,proto3" json:"anchor_name"`
	Members              int64    `protobuf:"varint,3,opt,name=members,proto3" json:"members"`
	Fee                  int64    `protobuf:"varint,4,opt,name=fee,proto3" json:"fee"`
	PresentFee           int64    `protobuf:"varint,5,opt,name=present_fee,json=presentFee,proto3" json:"present_fee"`
	KnightFee            int64    `protobuf:"varint,6,opt,name=knight_fee,json=knightFee,proto3" json:"knight_fee"`
	Income               int64    `protobuf:"varint,7,opt,name=income,proto3" json:"income"`
	Consume              int64    `protobuf:"varint,8,opt,name=consume,proto3" json:"consume"`
	VirtualLiveFee       int64    `protobuf:"varint,9,opt,name=virtual_live_fee,json=virtualLiveFee,proto3" json:"virtual_live_fee"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinTodayIncomeInfo) Reset()         { *m = YuyinTodayIncomeInfo{} }
func (m *YuyinTodayIncomeInfo) String() string { return proto.CompactTextString(m) }
func (*YuyinTodayIncomeInfo) ProtoMessage()    {}
func (*YuyinTodayIncomeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{19}
}
func (m *YuyinTodayIncomeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinTodayIncomeInfo.Unmarshal(m, b)
}
func (m *YuyinTodayIncomeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinTodayIncomeInfo.Marshal(b, m, deterministic)
}
func (dst *YuyinTodayIncomeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinTodayIncomeInfo.Merge(dst, src)
}
func (m *YuyinTodayIncomeInfo) XXX_Size() int {
	return xxx_messageInfo_YuyinTodayIncomeInfo.Size(m)
}
func (m *YuyinTodayIncomeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinTodayIncomeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinTodayIncomeInfo proto.InternalMessageInfo

func (m *YuyinTodayIncomeInfo) GetAnchorName() string {
	if m != nil {
		return m.AnchorName
	}
	return ""
}

func (m *YuyinTodayIncomeInfo) GetMembers() int64 {
	if m != nil {
		return m.Members
	}
	return 0
}

func (m *YuyinTodayIncomeInfo) GetFee() int64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *YuyinTodayIncomeInfo) GetPresentFee() int64 {
	if m != nil {
		return m.PresentFee
	}
	return 0
}

func (m *YuyinTodayIncomeInfo) GetKnightFee() int64 {
	if m != nil {
		return m.KnightFee
	}
	return 0
}

func (m *YuyinTodayIncomeInfo) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *YuyinTodayIncomeInfo) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *YuyinTodayIncomeInfo) GetVirtualLiveFee() int64 {
	if m != nil {
		return m.VirtualLiveFee
	}
	return 0
}

type YuyinTodayIncomeRsp struct {
	List                 []*YuyinTodayIncomeInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *YuyinTodayIncomeRsp) Reset()         { *m = YuyinTodayIncomeRsp{} }
func (m *YuyinTodayIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*YuyinTodayIncomeRsp) ProtoMessage()    {}
func (*YuyinTodayIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{20}
}
func (m *YuyinTodayIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinTodayIncomeRsp.Unmarshal(m, b)
}
func (m *YuyinTodayIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinTodayIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *YuyinTodayIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinTodayIncomeRsp.Merge(dst, src)
}
func (m *YuyinTodayIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_YuyinTodayIncomeRsp.Size(m)
}
func (m *YuyinTodayIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinTodayIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinTodayIncomeRsp proto.InternalMessageInfo

func (m *YuyinTodayIncomeRsp) GetList() []*YuyinTodayIncomeInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type MonthIncomeReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid"`
	MonthTime            int64    `protobuf:"varint,2,opt,name=month_time,json=monthTime,proto3" json:"month_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MonthIncomeReq) Reset()         { *m = MonthIncomeReq{} }
func (m *MonthIncomeReq) String() string { return proto.CompactTextString(m) }
func (*MonthIncomeReq) ProtoMessage()    {}
func (*MonthIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{21}
}
func (m *MonthIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MonthIncomeReq.Unmarshal(m, b)
}
func (m *MonthIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MonthIncomeReq.Marshal(b, m, deterministic)
}
func (dst *MonthIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MonthIncomeReq.Merge(dst, src)
}
func (m *MonthIncomeReq) XXX_Size() int {
	return xxx_messageInfo_MonthIncomeReq.Size(m)
}
func (m *MonthIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MonthIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_MonthIncomeReq proto.InternalMessageInfo

func (m *MonthIncomeReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *MonthIncomeReq) GetMonthTime() int64 {
	if m != nil {
		return m.MonthTime
	}
	return 0
}

type MonthIncomeInfo struct {
	DateTime             int64    `protobuf:"varint,1,opt,name=date_time,json=dateTime,proto3" json:"date_time"`
	Consume              int64    `protobuf:"varint,2,opt,name=consume,proto3" json:"consume"`
	Members              int64    `protobuf:"varint,3,opt,name=members,proto3" json:"members"`
	Income               int64    `protobuf:"varint,4,opt,name=income,proto3" json:"income"`
	Fee                  int64    `protobuf:"varint,5,opt,name=fee,proto3" json:"fee"`
	PresentFee           int64    `protobuf:"varint,6,opt,name=present_fee,json=presentFee,proto3" json:"present_fee"`
	KnightFee            int64    `protobuf:"varint,7,opt,name=knight_fee,json=knightFee,proto3" json:"knight_fee"`
	WerewolfFee          int64    `protobuf:"varint,8,opt,name=werewolf_fee,json=werewolfFee,proto3" json:"werewolf_fee"`
	InteractGameFee      int64    `protobuf:"varint,9,opt,name=interact_game_fee,json=interactGameFee,proto3" json:"interact_game_fee"`
	VirtualLiveFee       int64    `protobuf:"varint,10,opt,name=virtual_live_fee,json=virtualLiveFee,proto3" json:"virtual_live_fee"`
	EsportFee            int64    `protobuf:"varint,11,opt,name=esport_fee,json=esportFee,proto3" json:"esport_fee"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MonthIncomeInfo) Reset()         { *m = MonthIncomeInfo{} }
func (m *MonthIncomeInfo) String() string { return proto.CompactTextString(m) }
func (*MonthIncomeInfo) ProtoMessage()    {}
func (*MonthIncomeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{22}
}
func (m *MonthIncomeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MonthIncomeInfo.Unmarshal(m, b)
}
func (m *MonthIncomeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MonthIncomeInfo.Marshal(b, m, deterministic)
}
func (dst *MonthIncomeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MonthIncomeInfo.Merge(dst, src)
}
func (m *MonthIncomeInfo) XXX_Size() int {
	return xxx_messageInfo_MonthIncomeInfo.Size(m)
}
func (m *MonthIncomeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MonthIncomeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MonthIncomeInfo proto.InternalMessageInfo

func (m *MonthIncomeInfo) GetDateTime() int64 {
	if m != nil {
		return m.DateTime
	}
	return 0
}

func (m *MonthIncomeInfo) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *MonthIncomeInfo) GetMembers() int64 {
	if m != nil {
		return m.Members
	}
	return 0
}

func (m *MonthIncomeInfo) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *MonthIncomeInfo) GetFee() int64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *MonthIncomeInfo) GetPresentFee() int64 {
	if m != nil {
		return m.PresentFee
	}
	return 0
}

func (m *MonthIncomeInfo) GetKnightFee() int64 {
	if m != nil {
		return m.KnightFee
	}
	return 0
}

func (m *MonthIncomeInfo) GetWerewolfFee() int64 {
	if m != nil {
		return m.WerewolfFee
	}
	return 0
}

func (m *MonthIncomeInfo) GetInteractGameFee() int64 {
	if m != nil {
		return m.InteractGameFee
	}
	return 0
}

func (m *MonthIncomeInfo) GetVirtualLiveFee() int64 {
	if m != nil {
		return m.VirtualLiveFee
	}
	return 0
}

func (m *MonthIncomeInfo) GetEsportFee() int64 {
	if m != nil {
		return m.EsportFee
	}
	return 0
}

type MonthIncomeRsp struct {
	MonthIncomeList      []*MonthIncomeInfo `protobuf:"bytes,1,rep,name=month_income_list,json=monthIncomeList,proto3" json:"month_income_list"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MonthIncomeRsp) Reset()         { *m = MonthIncomeRsp{} }
func (m *MonthIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*MonthIncomeRsp) ProtoMessage()    {}
func (*MonthIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{23}
}
func (m *MonthIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MonthIncomeRsp.Unmarshal(m, b)
}
func (m *MonthIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MonthIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *MonthIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MonthIncomeRsp.Merge(dst, src)
}
func (m *MonthIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_MonthIncomeRsp.Size(m)
}
func (m *MonthIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_MonthIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_MonthIncomeRsp proto.InternalMessageInfo

func (m *MonthIncomeRsp) GetMonthIncomeList() []*MonthIncomeInfo {
	if m != nil {
		return m.MonthIncomeList
	}
	return nil
}

type ConsumeSearchReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account"`
	Channelid            uint32   `protobuf:"varint,3,opt,name=channelid,proto3" json:"channelid"`
	StartTime            int64    `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime              int64    `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	Page                 uint32   `protobuf:"varint,6,opt,name=page,proto3" json:"page"`
	PageNum              uint32   `protobuf:"varint,7,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConsumeSearchReq) Reset()         { *m = ConsumeSearchReq{} }
func (m *ConsumeSearchReq) String() string { return proto.CompactTextString(m) }
func (*ConsumeSearchReq) ProtoMessage()    {}
func (*ConsumeSearchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{24}
}
func (m *ConsumeSearchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeSearchReq.Unmarshal(m, b)
}
func (m *ConsumeSearchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeSearchReq.Marshal(b, m, deterministic)
}
func (dst *ConsumeSearchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeSearchReq.Merge(dst, src)
}
func (m *ConsumeSearchReq) XXX_Size() int {
	return xxx_messageInfo_ConsumeSearchReq.Size(m)
}
func (m *ConsumeSearchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeSearchReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeSearchReq proto.InternalMessageInfo

func (m *ConsumeSearchReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *ConsumeSearchReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ConsumeSearchReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *ConsumeSearchReq) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *ConsumeSearchReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ConsumeSearchReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *ConsumeSearchReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type ConsumeDetail struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Consume              int64    `protobuf:"varint,3,opt,name=consume,proto3" json:"consume"`
	ConsumeTime          int64    `protobuf:"varint,4,opt,name=consume_time,json=consumeTime,proto3" json:"consume_time"`
	ChannelName          string   `protobuf:"bytes,5,opt,name=channel_name,json=channelName,proto3" json:"channel_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConsumeDetail) Reset()         { *m = ConsumeDetail{} }
func (m *ConsumeDetail) String() string { return proto.CompactTextString(m) }
func (*ConsumeDetail) ProtoMessage()    {}
func (*ConsumeDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{25}
}
func (m *ConsumeDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeDetail.Unmarshal(m, b)
}
func (m *ConsumeDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeDetail.Marshal(b, m, deterministic)
}
func (dst *ConsumeDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeDetail.Merge(dst, src)
}
func (m *ConsumeDetail) XXX_Size() int {
	return xxx_messageInfo_ConsumeDetail.Size(m)
}
func (m *ConsumeDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeDetail.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeDetail proto.InternalMessageInfo

func (m *ConsumeDetail) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ConsumeDetail) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ConsumeDetail) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *ConsumeDetail) GetConsumeTime() int64 {
	if m != nil {
		return m.ConsumeTime
	}
	return 0
}

func (m *ConsumeDetail) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

type ConsumeSearchRsp struct {
	NextPage             bool             `protobuf:"varint,1,opt,name=next_page,json=nextPage,proto3" json:"next_page"`
	IncomeList           []*ConsumeDetail `protobuf:"bytes,2,rep,name=income_list,json=incomeList,proto3" json:"income_list"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ConsumeSearchRsp) Reset()         { *m = ConsumeSearchRsp{} }
func (m *ConsumeSearchRsp) String() string { return proto.CompactTextString(m) }
func (*ConsumeSearchRsp) ProtoMessage()    {}
func (*ConsumeSearchRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{26}
}
func (m *ConsumeSearchRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeSearchRsp.Unmarshal(m, b)
}
func (m *ConsumeSearchRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeSearchRsp.Marshal(b, m, deterministic)
}
func (dst *ConsumeSearchRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeSearchRsp.Merge(dst, src)
}
func (m *ConsumeSearchRsp) XXX_Size() int {
	return xxx_messageInfo_ConsumeSearchRsp.Size(m)
}
func (m *ConsumeSearchRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeSearchRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeSearchRsp proto.InternalMessageInfo

func (m *ConsumeSearchRsp) GetNextPage() bool {
	if m != nil {
		return m.NextPage
	}
	return false
}

func (m *ConsumeSearchRsp) GetIncomeList() []*ConsumeDetail {
	if m != nil {
		return m.IncomeList
	}
	return nil
}

type YuyinConsumeSearchReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account"`
	AnchorUid            uint32   `protobuf:"varint,3,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid"`
	StartTime            int64    `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime              int64    `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	Page                 uint32   `protobuf:"varint,6,opt,name=page,proto3" json:"page"`
	PageNum              uint32   `protobuf:"varint,7,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinConsumeSearchReq) Reset()         { *m = YuyinConsumeSearchReq{} }
func (m *YuyinConsumeSearchReq) String() string { return proto.CompactTextString(m) }
func (*YuyinConsumeSearchReq) ProtoMessage()    {}
func (*YuyinConsumeSearchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{27}
}
func (m *YuyinConsumeSearchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinConsumeSearchReq.Unmarshal(m, b)
}
func (m *YuyinConsumeSearchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinConsumeSearchReq.Marshal(b, m, deterministic)
}
func (dst *YuyinConsumeSearchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinConsumeSearchReq.Merge(dst, src)
}
func (m *YuyinConsumeSearchReq) XXX_Size() int {
	return xxx_messageInfo_YuyinConsumeSearchReq.Size(m)
}
func (m *YuyinConsumeSearchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinConsumeSearchReq.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinConsumeSearchReq proto.InternalMessageInfo

func (m *YuyinConsumeSearchReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *YuyinConsumeSearchReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *YuyinConsumeSearchReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *YuyinConsumeSearchReq) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *YuyinConsumeSearchReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *YuyinConsumeSearchReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *YuyinConsumeSearchReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type YuyinConsumeDetail struct {
	PaidAccount          string   `protobuf:"bytes,1,opt,name=paid_account,json=paidAccount,proto3" json:"paid_account"`
	PaidName             string   `protobuf:"bytes,2,opt,name=paid_name,json=paidName,proto3" json:"paid_name"`
	Consume              int64    `protobuf:"varint,3,opt,name=consume,proto3" json:"consume"`
	ConsumeTime          int64    `protobuf:"varint,4,opt,name=consume_time,json=consumeTime,proto3" json:"consume_time"`
	AnchorAccount        string   `protobuf:"bytes,5,opt,name=anchor_account,json=anchorAccount,proto3" json:"anchor_account"`
	AnchorName           string   `protobuf:"bytes,6,opt,name=anchor_name,json=anchorName,proto3" json:"anchor_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinConsumeDetail) Reset()         { *m = YuyinConsumeDetail{} }
func (m *YuyinConsumeDetail) String() string { return proto.CompactTextString(m) }
func (*YuyinConsumeDetail) ProtoMessage()    {}
func (*YuyinConsumeDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{28}
}
func (m *YuyinConsumeDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinConsumeDetail.Unmarshal(m, b)
}
func (m *YuyinConsumeDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinConsumeDetail.Marshal(b, m, deterministic)
}
func (dst *YuyinConsumeDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinConsumeDetail.Merge(dst, src)
}
func (m *YuyinConsumeDetail) XXX_Size() int {
	return xxx_messageInfo_YuyinConsumeDetail.Size(m)
}
func (m *YuyinConsumeDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinConsumeDetail.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinConsumeDetail proto.InternalMessageInfo

func (m *YuyinConsumeDetail) GetPaidAccount() string {
	if m != nil {
		return m.PaidAccount
	}
	return ""
}

func (m *YuyinConsumeDetail) GetPaidName() string {
	if m != nil {
		return m.PaidName
	}
	return ""
}

func (m *YuyinConsumeDetail) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *YuyinConsumeDetail) GetConsumeTime() int64 {
	if m != nil {
		return m.ConsumeTime
	}
	return 0
}

func (m *YuyinConsumeDetail) GetAnchorAccount() string {
	if m != nil {
		return m.AnchorAccount
	}
	return ""
}

func (m *YuyinConsumeDetail) GetAnchorName() string {
	if m != nil {
		return m.AnchorName
	}
	return ""
}

type YuyinConsumeSearchRsp struct {
	NextPage             bool                  `protobuf:"varint,1,opt,name=next_page,json=nextPage,proto3" json:"next_page"`
	IncomeList           []*YuyinConsumeDetail `protobuf:"bytes,2,rep,name=income_list,json=incomeList,proto3" json:"income_list"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *YuyinConsumeSearchRsp) Reset()         { *m = YuyinConsumeSearchRsp{} }
func (m *YuyinConsumeSearchRsp) String() string { return proto.CompactTextString(m) }
func (*YuyinConsumeSearchRsp) ProtoMessage()    {}
func (*YuyinConsumeSearchRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{29}
}
func (m *YuyinConsumeSearchRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinConsumeSearchRsp.Unmarshal(m, b)
}
func (m *YuyinConsumeSearchRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinConsumeSearchRsp.Marshal(b, m, deterministic)
}
func (dst *YuyinConsumeSearchRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinConsumeSearchRsp.Merge(dst, src)
}
func (m *YuyinConsumeSearchRsp) XXX_Size() int {
	return xxx_messageInfo_YuyinConsumeSearchRsp.Size(m)
}
func (m *YuyinConsumeSearchRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinConsumeSearchRsp.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinConsumeSearchRsp proto.InternalMessageInfo

func (m *YuyinConsumeSearchRsp) GetNextPage() bool {
	if m != nil {
		return m.NextPage
	}
	return false
}

func (m *YuyinConsumeSearchRsp) GetIncomeList() []*YuyinConsumeDetail {
	if m != nil {
		return m.IncomeList
	}
	return nil
}

type ChannelIncomeReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid"`
	Channelid            uint32   `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid"`
	RangeType            uint32   `protobuf:"varint,3,opt,name=range_type,json=rangeType,proto3" json:"range_type"`
	StartTime            int64    `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime              int64    `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	Page                 uint32   `protobuf:"varint,6,opt,name=page,proto3" json:"page"`
	PageNum              uint32   `protobuf:"varint,7,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelIncomeReq) Reset()         { *m = ChannelIncomeReq{} }
func (m *ChannelIncomeReq) String() string { return proto.CompactTextString(m) }
func (*ChannelIncomeReq) ProtoMessage()    {}
func (*ChannelIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{30}
}
func (m *ChannelIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelIncomeReq.Unmarshal(m, b)
}
func (m *ChannelIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelIncomeReq.Marshal(b, m, deterministic)
}
func (dst *ChannelIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelIncomeReq.Merge(dst, src)
}
func (m *ChannelIncomeReq) XXX_Size() int {
	return xxx_messageInfo_ChannelIncomeReq.Size(m)
}
func (m *ChannelIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelIncomeReq proto.InternalMessageInfo

func (m *ChannelIncomeReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *ChannelIncomeReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *ChannelIncomeReq) GetRangeType() uint32 {
	if m != nil {
		return m.RangeType
	}
	return 0
}

func (m *ChannelIncomeReq) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *ChannelIncomeReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ChannelIncomeReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *ChannelIncomeReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type ChannelIncomeInfo struct {
	ConsumeTime          int64    `protobuf:"varint,1,opt,name=consume_time,json=consumeTime,proto3" json:"consume_time"`
	Members              int64    `protobuf:"varint,2,opt,name=members,proto3" json:"members"`
	Consume              int64    `protobuf:"varint,3,opt,name=consume,proto3" json:"consume"`
	Income               int64    `protobuf:"varint,4,opt,name=income,proto3" json:"income"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelIncomeInfo) Reset()         { *m = ChannelIncomeInfo{} }
func (m *ChannelIncomeInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelIncomeInfo) ProtoMessage()    {}
func (*ChannelIncomeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{31}
}
func (m *ChannelIncomeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelIncomeInfo.Unmarshal(m, b)
}
func (m *ChannelIncomeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelIncomeInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelIncomeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelIncomeInfo.Merge(dst, src)
}
func (m *ChannelIncomeInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelIncomeInfo.Size(m)
}
func (m *ChannelIncomeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelIncomeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelIncomeInfo proto.InternalMessageInfo

func (m *ChannelIncomeInfo) GetConsumeTime() int64 {
	if m != nil {
		return m.ConsumeTime
	}
	return 0
}

func (m *ChannelIncomeInfo) GetMembers() int64 {
	if m != nil {
		return m.Members
	}
	return 0
}

func (m *ChannelIncomeInfo) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *ChannelIncomeInfo) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

type ChannelIncomeRsp struct {
	Consume              int64                `protobuf:"varint,1,opt,name=consume,proto3" json:"consume"`
	Income               int64                `protobuf:"varint,2,opt,name=income,proto3" json:"income"`
	NextPage             bool                 `protobuf:"varint,3,opt,name=next_page,json=nextPage,proto3" json:"next_page"`
	ChannelIncomeList    []*ChannelIncomeInfo `protobuf:"bytes,4,rep,name=channel_income_list,json=channelIncomeList,proto3" json:"channel_income_list"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ChannelIncomeRsp) Reset()         { *m = ChannelIncomeRsp{} }
func (m *ChannelIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*ChannelIncomeRsp) ProtoMessage()    {}
func (*ChannelIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{32}
}
func (m *ChannelIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelIncomeRsp.Unmarshal(m, b)
}
func (m *ChannelIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *ChannelIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelIncomeRsp.Merge(dst, src)
}
func (m *ChannelIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_ChannelIncomeRsp.Size(m)
}
func (m *ChannelIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelIncomeRsp proto.InternalMessageInfo

func (m *ChannelIncomeRsp) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *ChannelIncomeRsp) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *ChannelIncomeRsp) GetNextPage() bool {
	if m != nil {
		return m.NextPage
	}
	return false
}

func (m *ChannelIncomeRsp) GetChannelIncomeList() []*ChannelIncomeInfo {
	if m != nil {
		return m.ChannelIncomeList
	}
	return nil
}

type DayTrendInfo struct {
	Day                  uint32   `protobuf:"varint,1,opt,name=day,proto3" json:"day"`
	Income               int64    `protobuf:"varint,2,opt,name=income,proto3" json:"income"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DayTrendInfo) Reset()         { *m = DayTrendInfo{} }
func (m *DayTrendInfo) String() string { return proto.CompactTextString(m) }
func (*DayTrendInfo) ProtoMessage()    {}
func (*DayTrendInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{33}
}
func (m *DayTrendInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DayTrendInfo.Unmarshal(m, b)
}
func (m *DayTrendInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DayTrendInfo.Marshal(b, m, deterministic)
}
func (dst *DayTrendInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DayTrendInfo.Merge(dst, src)
}
func (m *DayTrendInfo) XXX_Size() int {
	return xxx_messageInfo_DayTrendInfo.Size(m)
}
func (m *DayTrendInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DayTrendInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DayTrendInfo proto.InternalMessageInfo

func (m *DayTrendInfo) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

func (m *DayTrendInfo) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

type GuildChannelInfo struct {
	Channelid            uint32   `protobuf:"varint,1,opt,name=channelid,proto3" json:"channelid"`
	Displayid            uint32   `protobuf:"varint,2,opt,name=displayid,proto3" json:"displayid"`
	ChannelName          string   `protobuf:"bytes,3,opt,name=channel_name,json=channelName,proto3" json:"channel_name"`
	Consume              int64    `protobuf:"varint,4,opt,name=consume,proto3" json:"consume"`
	MonthTonowQoq        float32  `protobuf:"fixed32,5,opt,name=month_tonow_qoq,json=monthTonowQoq,proto3" json:"month_tonow_qoq"`
	ChannelViewId        string   `protobuf:"bytes,6,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildChannelInfo) Reset()         { *m = GuildChannelInfo{} }
func (m *GuildChannelInfo) String() string { return proto.CompactTextString(m) }
func (*GuildChannelInfo) ProtoMessage()    {}
func (*GuildChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{34}
}
func (m *GuildChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildChannelInfo.Unmarshal(m, b)
}
func (m *GuildChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildChannelInfo.Marshal(b, m, deterministic)
}
func (dst *GuildChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildChannelInfo.Merge(dst, src)
}
func (m *GuildChannelInfo) XXX_Size() int {
	return xxx_messageInfo_GuildChannelInfo.Size(m)
}
func (m *GuildChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildChannelInfo proto.InternalMessageInfo

func (m *GuildChannelInfo) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *GuildChannelInfo) GetDisplayid() uint32 {
	if m != nil {
		return m.Displayid
	}
	return 0
}

func (m *GuildChannelInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *GuildChannelInfo) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *GuildChannelInfo) GetMonthTonowQoq() float32 {
	if m != nil {
		return m.MonthTonowQoq
	}
	return 0
}

func (m *GuildChannelInfo) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type GuildInitInfoReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildInitInfoReq) Reset()         { *m = GuildInitInfoReq{} }
func (m *GuildInitInfoReq) String() string { return proto.CompactTextString(m) }
func (*GuildInitInfoReq) ProtoMessage()    {}
func (*GuildInitInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{35}
}
func (m *GuildInitInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildInitInfoReq.Unmarshal(m, b)
}
func (m *GuildInitInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildInitInfoReq.Marshal(b, m, deterministic)
}
func (dst *GuildInitInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildInitInfoReq.Merge(dst, src)
}
func (m *GuildInitInfoReq) XXX_Size() int {
	return xxx_messageInfo_GuildInitInfoReq.Size(m)
}
func (m *GuildInitInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildInitInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildInitInfoReq proto.InternalMessageInfo

func (m *GuildInitInfoReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type GuildInitInfoRsp struct {
	GuildName            string              `protobuf:"bytes,1,opt,name=guild_name,json=guildName,proto3" json:"guild_name"`
	Guildid              uint32              `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid"`
	Account              string              `protobuf:"bytes,3,opt,name=account,proto3" json:"account"`
	GuildType            uint32              `protobuf:"varint,4,opt,name=guild_type,json=guildType,proto3" json:"guild_type"`
	Applied              uint32              `protobuf:"varint,5,opt,name=applied,proto3" json:"applied"`
	ServerTime           int64               `protobuf:"varint,6,opt,name=server_time,json=serverTime,proto3" json:"server_time"`
	TodayIncome          int64               `protobuf:"varint,7,opt,name=today_income,json=todayIncome,proto3" json:"today_income"`
	YesterdayIncome      int64               `protobuf:"varint,8,opt,name=yesterday_income,json=yesterdayIncome,proto3" json:"yesterday_income"`
	ThisMonthIncome      int64               `protobuf:"varint,9,opt,name=this_month_income,json=thisMonthIncome,proto3" json:"this_month_income"`
	LastMonthIncome      int64               `protobuf:"varint,10,opt,name=last_month_income,json=lastMonthIncome,proto3" json:"last_month_income"`
	DayList              []*DayTrendInfo     `protobuf:"bytes,11,rep,name=day_list,json=dayList,proto3" json:"day_list"`
	ChannelList          []*GuildChannelInfo `protobuf:"bytes,12,rep,name=channel_list,json=channelList,proto3" json:"channel_list"`
	ChannelCash          float64             `protobuf:"fixed64,13,opt,name=channel_cash,json=channelCash,proto3" json:"channel_cash"`
	GameCash             float64             `protobuf:"fixed64,14,opt,name=game_cash,json=gameCash,proto3" json:"game_cash"`
	LiveCash             float64             `protobuf:"fixed64,15,opt,name=live_cash,json=liveCash,proto3" json:"live_cash"`
	DayQoq               float32             `protobuf:"fixed32,16,opt,name=day_qoq,json=dayQoq,proto3" json:"day_qoq"`
	MonthQoq             float32             `protobuf:"fixed32,17,opt,name=month_qoq,json=monthQoq,proto3" json:"month_qoq"`
	WealthValue          uint32              `protobuf:"varint,18,opt,name=wealth_value,json=wealthValue,proto3" json:"wealth_value"`
	IsApplication        bool                `protobuf:"varint,19,opt,name=is_application,json=isApplication,proto3" json:"is_application"`
	LastdayQoq           float32             `protobuf:"fixed32,20,opt,name=lastday_qoq,json=lastdayQoq,proto3" json:"lastday_qoq"`
	MonthTonowQoq        float32             `protobuf:"fixed32,21,opt,name=month_tonow_qoq,json=monthTonowQoq,proto3" json:"month_tonow_qoq"`
	LastMonthSamePeriod  int64               `protobuf:"varint,22,opt,name=last_month_same_period,json=lastMonthSamePeriod,proto3" json:"last_month_same_period"`
	LastDayList          []*DayTrendInfo     `protobuf:"bytes,23,rep,name=last_day_list,json=lastDayList,proto3" json:"last_day_list"`
	AudioBasicCash       float64             `protobuf:"fixed64,24,opt,name=audio_basic_cash,json=audioBasicCash,proto3" json:"audio_basic_cash"`
	AudioExtraCash       float64             `protobuf:"fixed64,25,opt,name=audio_extra_cash,json=audioExtraCash,proto3" json:"audio_extra_cash"`
	MonthSixIncome       int64               `protobuf:"varint,26,opt,name=month_six_income,json=monthSixIncome,proto3" json:"month_six_income"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GuildInitInfoRsp) Reset()         { *m = GuildInitInfoRsp{} }
func (m *GuildInitInfoRsp) String() string { return proto.CompactTextString(m) }
func (*GuildInitInfoRsp) ProtoMessage()    {}
func (*GuildInitInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{36}
}
func (m *GuildInitInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildInitInfoRsp.Unmarshal(m, b)
}
func (m *GuildInitInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildInitInfoRsp.Marshal(b, m, deterministic)
}
func (dst *GuildInitInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildInitInfoRsp.Merge(dst, src)
}
func (m *GuildInitInfoRsp) XXX_Size() int {
	return xxx_messageInfo_GuildInitInfoRsp.Size(m)
}
func (m *GuildInitInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildInitInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildInitInfoRsp proto.InternalMessageInfo

func (m *GuildInitInfoRsp) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *GuildInitInfoRsp) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *GuildInitInfoRsp) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GuildInitInfoRsp) GetGuildType() uint32 {
	if m != nil {
		return m.GuildType
	}
	return 0
}

func (m *GuildInitInfoRsp) GetApplied() uint32 {
	if m != nil {
		return m.Applied
	}
	return 0
}

func (m *GuildInitInfoRsp) GetServerTime() int64 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *GuildInitInfoRsp) GetTodayIncome() int64 {
	if m != nil {
		return m.TodayIncome
	}
	return 0
}

func (m *GuildInitInfoRsp) GetYesterdayIncome() int64 {
	if m != nil {
		return m.YesterdayIncome
	}
	return 0
}

func (m *GuildInitInfoRsp) GetThisMonthIncome() int64 {
	if m != nil {
		return m.ThisMonthIncome
	}
	return 0
}

func (m *GuildInitInfoRsp) GetLastMonthIncome() int64 {
	if m != nil {
		return m.LastMonthIncome
	}
	return 0
}

func (m *GuildInitInfoRsp) GetDayList() []*DayTrendInfo {
	if m != nil {
		return m.DayList
	}
	return nil
}

func (m *GuildInitInfoRsp) GetChannelList() []*GuildChannelInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *GuildInitInfoRsp) GetChannelCash() float64 {
	if m != nil {
		return m.ChannelCash
	}
	return 0
}

func (m *GuildInitInfoRsp) GetGameCash() float64 {
	if m != nil {
		return m.GameCash
	}
	return 0
}

func (m *GuildInitInfoRsp) GetLiveCash() float64 {
	if m != nil {
		return m.LiveCash
	}
	return 0
}

func (m *GuildInitInfoRsp) GetDayQoq() float32 {
	if m != nil {
		return m.DayQoq
	}
	return 0
}

func (m *GuildInitInfoRsp) GetMonthQoq() float32 {
	if m != nil {
		return m.MonthQoq
	}
	return 0
}

func (m *GuildInitInfoRsp) GetWealthValue() uint32 {
	if m != nil {
		return m.WealthValue
	}
	return 0
}

func (m *GuildInitInfoRsp) GetIsApplication() bool {
	if m != nil {
		return m.IsApplication
	}
	return false
}

func (m *GuildInitInfoRsp) GetLastdayQoq() float32 {
	if m != nil {
		return m.LastdayQoq
	}
	return 0
}

func (m *GuildInitInfoRsp) GetMonthTonowQoq() float32 {
	if m != nil {
		return m.MonthTonowQoq
	}
	return 0
}

func (m *GuildInitInfoRsp) GetLastMonthSamePeriod() int64 {
	if m != nil {
		return m.LastMonthSamePeriod
	}
	return 0
}

func (m *GuildInitInfoRsp) GetLastDayList() []*DayTrendInfo {
	if m != nil {
		return m.LastDayList
	}
	return nil
}

func (m *GuildInitInfoRsp) GetAudioBasicCash() float64 {
	if m != nil {
		return m.AudioBasicCash
	}
	return 0
}

func (m *GuildInitInfoRsp) GetAudioExtraCash() float64 {
	if m != nil {
		return m.AudioExtraCash
	}
	return 0
}

func (m *GuildInitInfoRsp) GetMonthSixIncome() int64 {
	if m != nil {
		return m.MonthSixIncome
	}
	return 0
}

type GuildCheckReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildCheckReq) Reset()         { *m = GuildCheckReq{} }
func (m *GuildCheckReq) String() string { return proto.CompactTextString(m) }
func (*GuildCheckReq) ProtoMessage()    {}
func (*GuildCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{37}
}
func (m *GuildCheckReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildCheckReq.Unmarshal(m, b)
}
func (m *GuildCheckReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildCheckReq.Marshal(b, m, deterministic)
}
func (dst *GuildCheckReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildCheckReq.Merge(dst, src)
}
func (m *GuildCheckReq) XXX_Size() int {
	return xxx_messageInfo_GuildCheckReq.Size(m)
}
func (m *GuildCheckReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildCheckReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildCheckReq proto.InternalMessageInfo

func (m *GuildCheckReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

// 检查公开房
type GuildCheckRsp struct {
	HasAmusement         bool     `protobuf:"varint,1,opt,name=has_amusement,json=hasAmusement,proto3" json:"has_amusement"`
	MultiGuildType       uint32   `protobuf:"varint,2,opt,name=multi_guild_type,json=multiGuildType,proto3" json:"multi_guild_type"`
	AudioGuildType       uint32   `protobuf:"varint,3,opt,name=audio_guild_type,json=audioGuildType,proto3" json:"audio_guild_type"`
	MultiApplied         uint32   `protobuf:"varint,4,opt,name=multi_applied,json=multiApplied,proto3" json:"multi_applied"`
	AudioApplied         uint32   `protobuf:"varint,5,opt,name=audio_applied,json=audioApplied,proto3" json:"audio_applied"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildCheckRsp) Reset()         { *m = GuildCheckRsp{} }
func (m *GuildCheckRsp) String() string { return proto.CompactTextString(m) }
func (*GuildCheckRsp) ProtoMessage()    {}
func (*GuildCheckRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{38}
}
func (m *GuildCheckRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildCheckRsp.Unmarshal(m, b)
}
func (m *GuildCheckRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildCheckRsp.Marshal(b, m, deterministic)
}
func (dst *GuildCheckRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildCheckRsp.Merge(dst, src)
}
func (m *GuildCheckRsp) XXX_Size() int {
	return xxx_messageInfo_GuildCheckRsp.Size(m)
}
func (m *GuildCheckRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildCheckRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildCheckRsp proto.InternalMessageInfo

func (m *GuildCheckRsp) GetHasAmusement() bool {
	if m != nil {
		return m.HasAmusement
	}
	return false
}

func (m *GuildCheckRsp) GetMultiGuildType() uint32 {
	if m != nil {
		return m.MultiGuildType
	}
	return 0
}

func (m *GuildCheckRsp) GetAudioGuildType() uint32 {
	if m != nil {
		return m.AudioGuildType
	}
	return 0
}

func (m *GuildCheckRsp) GetMultiApplied() uint32 {
	if m != nil {
		return m.MultiApplied
	}
	return 0
}

func (m *GuildCheckRsp) GetAudioApplied() uint32 {
	if m != nil {
		return m.AudioApplied
	}
	return 0
}

// 公会日流水环比(废弃)
type GuildConsumeDayQoqReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid"`
	Day                  int64    `protobuf:"varint,2,opt,name=day,proto3" json:"day"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildConsumeDayQoqReq) Reset()         { *m = GuildConsumeDayQoqReq{} }
func (m *GuildConsumeDayQoqReq) String() string { return proto.CompactTextString(m) }
func (*GuildConsumeDayQoqReq) ProtoMessage()    {}
func (*GuildConsumeDayQoqReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{39}
}
func (m *GuildConsumeDayQoqReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildConsumeDayQoqReq.Unmarshal(m, b)
}
func (m *GuildConsumeDayQoqReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildConsumeDayQoqReq.Marshal(b, m, deterministic)
}
func (dst *GuildConsumeDayQoqReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildConsumeDayQoqReq.Merge(dst, src)
}
func (m *GuildConsumeDayQoqReq) XXX_Size() int {
	return xxx_messageInfo_GuildConsumeDayQoqReq.Size(m)
}
func (m *GuildConsumeDayQoqReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildConsumeDayQoqReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildConsumeDayQoqReq proto.InternalMessageInfo

func (m *GuildConsumeDayQoqReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *GuildConsumeDayQoqReq) GetDay() int64 {
	if m != nil {
		return m.Day
	}
	return 0
}

type GuildConsumeDayQoqRsp struct {
	StatTime             int64    `protobuf:"varint,1,opt,name=stat_time,json=statTime,proto3" json:"stat_time"`
	Consume              int64    `protobuf:"varint,2,opt,name=consume,proto3" json:"consume"`
	CompareStatTime      int64    `protobuf:"varint,3,opt,name=compare_stat_time,json=compareStatTime,proto3" json:"compare_stat_time"`
	CompareConsume       int64    `protobuf:"varint,4,opt,name=compare_consume,json=compareConsume,proto3" json:"compare_consume"`
	Qoq                  float32  `protobuf:"fixed32,5,opt,name=qoq,proto3" json:"qoq"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildConsumeDayQoqRsp) Reset()         { *m = GuildConsumeDayQoqRsp{} }
func (m *GuildConsumeDayQoqRsp) String() string { return proto.CompactTextString(m) }
func (*GuildConsumeDayQoqRsp) ProtoMessage()    {}
func (*GuildConsumeDayQoqRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{40}
}
func (m *GuildConsumeDayQoqRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildConsumeDayQoqRsp.Unmarshal(m, b)
}
func (m *GuildConsumeDayQoqRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildConsumeDayQoqRsp.Marshal(b, m, deterministic)
}
func (dst *GuildConsumeDayQoqRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildConsumeDayQoqRsp.Merge(dst, src)
}
func (m *GuildConsumeDayQoqRsp) XXX_Size() int {
	return xxx_messageInfo_GuildConsumeDayQoqRsp.Size(m)
}
func (m *GuildConsumeDayQoqRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildConsumeDayQoqRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildConsumeDayQoqRsp proto.InternalMessageInfo

func (m *GuildConsumeDayQoqRsp) GetStatTime() int64 {
	if m != nil {
		return m.StatTime
	}
	return 0
}

func (m *GuildConsumeDayQoqRsp) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *GuildConsumeDayQoqRsp) GetCompareStatTime() int64 {
	if m != nil {
		return m.CompareStatTime
	}
	return 0
}

func (m *GuildConsumeDayQoqRsp) GetCompareConsume() int64 {
	if m != nil {
		return m.CompareConsume
	}
	return 0
}

func (m *GuildConsumeDayQoqRsp) GetQoq() float32 {
	if m != nil {
		return m.Qoq
	}
	return 0
}

// 公会日流水环比 新
type GuildDayComparisonReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	Day                  int64    `protobuf:"varint,2,opt,name=day,proto3" json:"day"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildDayComparisonReq) Reset()         { *m = GuildDayComparisonReq{} }
func (m *GuildDayComparisonReq) String() string { return proto.CompactTextString(m) }
func (*GuildDayComparisonReq) ProtoMessage()    {}
func (*GuildDayComparisonReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{41}
}
func (m *GuildDayComparisonReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildDayComparisonReq.Unmarshal(m, b)
}
func (m *GuildDayComparisonReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildDayComparisonReq.Marshal(b, m, deterministic)
}
func (dst *GuildDayComparisonReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildDayComparisonReq.Merge(dst, src)
}
func (m *GuildDayComparisonReq) XXX_Size() int {
	return xxx_messageInfo_GuildDayComparisonReq.Size(m)
}
func (m *GuildDayComparisonReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildDayComparisonReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildDayComparisonReq proto.InternalMessageInfo

func (m *GuildDayComparisonReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildDayComparisonReq) GetDay() int64 {
	if m != nil {
		return m.Day
	}
	return 0
}

type DayComparisonInfo struct {
	StatTime             uint64   `protobuf:"varint,1,opt,name=stat_time,json=statTime,proto3" json:"stat_time"`
	DayIncome            uint64   `protobuf:"varint,2,opt,name=day_income,json=dayIncome,proto3" json:"day_income"`
	CompareIncome        uint64   `protobuf:"varint,3,opt,name=compare_income,json=compareIncome,proto3" json:"compare_income"`
	Qoq                  float32  `protobuf:"fixed32,5,opt,name=qoq,proto3" json:"qoq"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DayComparisonInfo) Reset()         { *m = DayComparisonInfo{} }
func (m *DayComparisonInfo) String() string { return proto.CompactTextString(m) }
func (*DayComparisonInfo) ProtoMessage()    {}
func (*DayComparisonInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{42}
}
func (m *DayComparisonInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DayComparisonInfo.Unmarshal(m, b)
}
func (m *DayComparisonInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DayComparisonInfo.Marshal(b, m, deterministic)
}
func (dst *DayComparisonInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DayComparisonInfo.Merge(dst, src)
}
func (m *DayComparisonInfo) XXX_Size() int {
	return xxx_messageInfo_DayComparisonInfo.Size(m)
}
func (m *DayComparisonInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DayComparisonInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DayComparisonInfo proto.InternalMessageInfo

func (m *DayComparisonInfo) GetStatTime() uint64 {
	if m != nil {
		return m.StatTime
	}
	return 0
}

func (m *DayComparisonInfo) GetDayIncome() uint64 {
	if m != nil {
		return m.DayIncome
	}
	return 0
}

func (m *DayComparisonInfo) GetCompareIncome() uint64 {
	if m != nil {
		return m.CompareIncome
	}
	return 0
}

func (m *DayComparisonInfo) GetQoq() float32 {
	if m != nil {
		return m.Qoq
	}
	return 0
}

type GuildDayComparisonResp struct {
	StatTime    uint64 `protobuf:"varint,1,opt,name=stat_time,json=statTime,proto3" json:"stat_time"`
	CompareTime uint64 `protobuf:"varint,2,opt,name=compare_time,json=compareTime,proto3" json:"compare_time"`
	// 根据合作库类型返回
	AmuseDayComparison   *DayComparisonInfo `protobuf:"bytes,3,opt,name=amuse_day_comparison,json=amuseDayComparison,proto3" json:"amuse_day_comparison"`
	YuyinDayComparison   *DayComparisonInfo `protobuf:"bytes,4,opt,name=yuyin_day_comparison,json=yuyinDayComparison,proto3" json:"yuyin_day_comparison"`
	EsportDayComparison  *DayComparisonInfo `protobuf:"bytes,5,opt,name=esport_day_comparison,json=esportDayComparison,proto3" json:"esport_day_comparison"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GuildDayComparisonResp) Reset()         { *m = GuildDayComparisonResp{} }
func (m *GuildDayComparisonResp) String() string { return proto.CompactTextString(m) }
func (*GuildDayComparisonResp) ProtoMessage()    {}
func (*GuildDayComparisonResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{43}
}
func (m *GuildDayComparisonResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildDayComparisonResp.Unmarshal(m, b)
}
func (m *GuildDayComparisonResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildDayComparisonResp.Marshal(b, m, deterministic)
}
func (dst *GuildDayComparisonResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildDayComparisonResp.Merge(dst, src)
}
func (m *GuildDayComparisonResp) XXX_Size() int {
	return xxx_messageInfo_GuildDayComparisonResp.Size(m)
}
func (m *GuildDayComparisonResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildDayComparisonResp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildDayComparisonResp proto.InternalMessageInfo

func (m *GuildDayComparisonResp) GetStatTime() uint64 {
	if m != nil {
		return m.StatTime
	}
	return 0
}

func (m *GuildDayComparisonResp) GetCompareTime() uint64 {
	if m != nil {
		return m.CompareTime
	}
	return 0
}

func (m *GuildDayComparisonResp) GetAmuseDayComparison() *DayComparisonInfo {
	if m != nil {
		return m.AmuseDayComparison
	}
	return nil
}

func (m *GuildDayComparisonResp) GetYuyinDayComparison() *DayComparisonInfo {
	if m != nil {
		return m.YuyinDayComparison
	}
	return nil
}

func (m *GuildDayComparisonResp) GetEsportDayComparison() *DayComparisonInfo {
	if m != nil {
		return m.EsportDayComparison
	}
	return nil
}

type ConsumeRankItem struct {
	Alias                string   `protobuf:"bytes,1,opt,name=alias,proto3" json:"alias"`
	NickName             string   `protobuf:"bytes,2,opt,name=nick_name,json=nickName,proto3" json:"nick_name"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account"`
	Consume              int64    `protobuf:"varint,4,opt,name=consume,proto3" json:"consume"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConsumeRankItem) Reset()         { *m = ConsumeRankItem{} }
func (m *ConsumeRankItem) String() string { return proto.CompactTextString(m) }
func (*ConsumeRankItem) ProtoMessage()    {}
func (*ConsumeRankItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{44}
}
func (m *ConsumeRankItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeRankItem.Unmarshal(m, b)
}
func (m *ConsumeRankItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeRankItem.Marshal(b, m, deterministic)
}
func (dst *ConsumeRankItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeRankItem.Merge(dst, src)
}
func (m *ConsumeRankItem) XXX_Size() int {
	return xxx_messageInfo_ConsumeRankItem.Size(m)
}
func (m *ConsumeRankItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeRankItem.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeRankItem proto.InternalMessageInfo

func (m *ConsumeRankItem) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *ConsumeRankItem) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *ConsumeRankItem) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ConsumeRankItem) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

// 公会推荐房消费Top10用户列表
type ConsumeRankReq struct {
	ConsumeType          RangeType `protobuf:"varint,1,opt,name=consume_type,json=consumeType,proto3,enum=gold_diamond_logic.RangeType" json:"consume_type"`
	Guildid              uint32    `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ConsumeRankReq) Reset()         { *m = ConsumeRankReq{} }
func (m *ConsumeRankReq) String() string { return proto.CompactTextString(m) }
func (*ConsumeRankReq) ProtoMessage()    {}
func (*ConsumeRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{45}
}
func (m *ConsumeRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeRankReq.Unmarshal(m, b)
}
func (m *ConsumeRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeRankReq.Marshal(b, m, deterministic)
}
func (dst *ConsumeRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeRankReq.Merge(dst, src)
}
func (m *ConsumeRankReq) XXX_Size() int {
	return xxx_messageInfo_ConsumeRankReq.Size(m)
}
func (m *ConsumeRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeRankReq proto.InternalMessageInfo

func (m *ConsumeRankReq) GetConsumeType() RangeType {
	if m != nil {
		return m.ConsumeType
	}
	return RangeType_DAY_RANGE_TYPE
}

func (m *ConsumeRankReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type ConsumeRankRsp struct {
	ConsumeRankList      []*ConsumeRankItem `protobuf:"bytes,1,rep,name=consume_rank_list,json=consumeRankList,proto3" json:"consume_rank_list"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ConsumeRankRsp) Reset()         { *m = ConsumeRankRsp{} }
func (m *ConsumeRankRsp) String() string { return proto.CompactTextString(m) }
func (*ConsumeRankRsp) ProtoMessage()    {}
func (*ConsumeRankRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{46}
}
func (m *ConsumeRankRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeRankRsp.Unmarshal(m, b)
}
func (m *ConsumeRankRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeRankRsp.Marshal(b, m, deterministic)
}
func (dst *ConsumeRankRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeRankRsp.Merge(dst, src)
}
func (m *ConsumeRankRsp) XXX_Size() int {
	return xxx_messageInfo_ConsumeRankRsp.Size(m)
}
func (m *ConsumeRankRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeRankRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeRankRsp proto.InternalMessageInfo

func (m *ConsumeRankRsp) GetConsumeRankList() []*ConsumeRankItem {
	if m != nil {
		return m.ConsumeRankList
	}
	return nil
}

// 公会推荐房日流水消费数据
type RoomConsumeQoqReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid"`
	Channelid            uint32   `protobuf:"varint,2,opt,name=channelid,proto3" json:"channelid"`
	Day                  int64    `protobuf:"varint,3,opt,name=day,proto3" json:"day"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoomConsumeQoqReq) Reset()         { *m = RoomConsumeQoqReq{} }
func (m *RoomConsumeQoqReq) String() string { return proto.CompactTextString(m) }
func (*RoomConsumeQoqReq) ProtoMessage()    {}
func (*RoomConsumeQoqReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{47}
}
func (m *RoomConsumeQoqReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoomConsumeQoqReq.Unmarshal(m, b)
}
func (m *RoomConsumeQoqReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoomConsumeQoqReq.Marshal(b, m, deterministic)
}
func (dst *RoomConsumeQoqReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoomConsumeQoqReq.Merge(dst, src)
}
func (m *RoomConsumeQoqReq) XXX_Size() int {
	return xxx_messageInfo_RoomConsumeQoqReq.Size(m)
}
func (m *RoomConsumeQoqReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RoomConsumeQoqReq.DiscardUnknown(m)
}

var xxx_messageInfo_RoomConsumeQoqReq proto.InternalMessageInfo

func (m *RoomConsumeQoqReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *RoomConsumeQoqReq) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

func (m *RoomConsumeQoqReq) GetDay() int64 {
	if m != nil {
		return m.Day
	}
	return 0
}

type RoomConsumeQoqRsp struct {
	StatTime             int64    `protobuf:"varint,1,opt,name=stat_time,json=statTime,proto3" json:"stat_time"`
	Consume              int64    `protobuf:"varint,2,opt,name=consume,proto3" json:"consume"`
	CompareStatTime      int64    `protobuf:"varint,3,opt,name=compare_stat_time,json=compareStatTime,proto3" json:"compare_stat_time"`
	CompareConsume       int64    `protobuf:"varint,4,opt,name=compare_consume,json=compareConsume,proto3" json:"compare_consume"`
	Qoq                  float32  `protobuf:"fixed32,5,opt,name=qoq,proto3" json:"qoq"`
	Channelid            uint32   `protobuf:"varint,6,opt,name=channelid,proto3" json:"channelid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoomConsumeQoqRsp) Reset()         { *m = RoomConsumeQoqRsp{} }
func (m *RoomConsumeQoqRsp) String() string { return proto.CompactTextString(m) }
func (*RoomConsumeQoqRsp) ProtoMessage()    {}
func (*RoomConsumeQoqRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{48}
}
func (m *RoomConsumeQoqRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoomConsumeQoqRsp.Unmarshal(m, b)
}
func (m *RoomConsumeQoqRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoomConsumeQoqRsp.Marshal(b, m, deterministic)
}
func (dst *RoomConsumeQoqRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoomConsumeQoqRsp.Merge(dst, src)
}
func (m *RoomConsumeQoqRsp) XXX_Size() int {
	return xxx_messageInfo_RoomConsumeQoqRsp.Size(m)
}
func (m *RoomConsumeQoqRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_RoomConsumeQoqRsp.DiscardUnknown(m)
}

var xxx_messageInfo_RoomConsumeQoqRsp proto.InternalMessageInfo

func (m *RoomConsumeQoqRsp) GetStatTime() int64 {
	if m != nil {
		return m.StatTime
	}
	return 0
}

func (m *RoomConsumeQoqRsp) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *RoomConsumeQoqRsp) GetCompareStatTime() int64 {
	if m != nil {
		return m.CompareStatTime
	}
	return 0
}

func (m *RoomConsumeQoqRsp) GetCompareConsume() int64 {
	if m != nil {
		return m.CompareConsume
	}
	return 0
}

func (m *RoomConsumeQoqRsp) GetQoq() float32 {
	if m != nil {
		return m.Qoq
	}
	return 0
}

func (m *RoomConsumeQoqRsp) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

type GetGuildAnchorListReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page"`
	PageNum              uint32   `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildAnchorListReq) Reset()         { *m = GetGuildAnchorListReq{} }
func (m *GetGuildAnchorListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildAnchorListReq) ProtoMessage()    {}
func (*GetGuildAnchorListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{49}
}
func (m *GetGuildAnchorListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildAnchorListReq.Unmarshal(m, b)
}
func (m *GetGuildAnchorListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildAnchorListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildAnchorListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildAnchorListReq.Merge(dst, src)
}
func (m *GetGuildAnchorListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildAnchorListReq.Size(m)
}
func (m *GetGuildAnchorListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildAnchorListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildAnchorListReq proto.InternalMessageInfo

func (m *GetGuildAnchorListReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *GetGuildAnchorListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetGuildAnchorListReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type GuildAnchorSimpleInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Ttid                 string   `protobuf:"bytes,3,opt,name=ttid,proto3" json:"ttid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildAnchorSimpleInfo) Reset()         { *m = GuildAnchorSimpleInfo{} }
func (m *GuildAnchorSimpleInfo) String() string { return proto.CompactTextString(m) }
func (*GuildAnchorSimpleInfo) ProtoMessage()    {}
func (*GuildAnchorSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{50}
}
func (m *GuildAnchorSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildAnchorSimpleInfo.Unmarshal(m, b)
}
func (m *GuildAnchorSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildAnchorSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *GuildAnchorSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildAnchorSimpleInfo.Merge(dst, src)
}
func (m *GuildAnchorSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_GuildAnchorSimpleInfo.Size(m)
}
func (m *GuildAnchorSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildAnchorSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildAnchorSimpleInfo proto.InternalMessageInfo

func (m *GuildAnchorSimpleInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GuildAnchorSimpleInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GuildAnchorSimpleInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

type GetGuildAnchorListRsp struct {
	List                 []*GuildAnchorSimpleInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetGuildAnchorListRsp) Reset()         { *m = GetGuildAnchorListRsp{} }
func (m *GetGuildAnchorListRsp) String() string { return proto.CompactTextString(m) }
func (*GetGuildAnchorListRsp) ProtoMessage()    {}
func (*GetGuildAnchorListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{51}
}
func (m *GetGuildAnchorListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildAnchorListRsp.Unmarshal(m, b)
}
func (m *GetGuildAnchorListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildAnchorListRsp.Marshal(b, m, deterministic)
}
func (dst *GetGuildAnchorListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildAnchorListRsp.Merge(dst, src)
}
func (m *GetGuildAnchorListRsp) XXX_Size() int {
	return xxx_messageInfo_GetGuildAnchorListRsp.Size(m)
}
func (m *GetGuildAnchorListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildAnchorListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildAnchorListRsp proto.InternalMessageInfo

func (m *GetGuildAnchorListRsp) GetList() []*GuildAnchorSimpleInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type GetGuildIdReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildIdReq) Reset()         { *m = GetGuildIdReq{} }
func (m *GetGuildIdReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildIdReq) ProtoMessage()    {}
func (*GetGuildIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{52}
}
func (m *GetGuildIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildIdReq.Unmarshal(m, b)
}
func (m *GetGuildIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildIdReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildIdReq.Merge(dst, src)
}
func (m *GetGuildIdReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildIdReq.Size(m)
}
func (m *GetGuildIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildIdReq proto.InternalMessageInfo

type GetGuildIdRsp struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account"`
	GuildShortId         uint32   `protobuf:"varint,3,opt,name=guild_short_id,json=guildShortId,proto3" json:"guild_short_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildIdRsp) Reset()         { *m = GetGuildIdRsp{} }
func (m *GetGuildIdRsp) String() string { return proto.CompactTextString(m) }
func (*GetGuildIdRsp) ProtoMessage()    {}
func (*GetGuildIdRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{53}
}
func (m *GetGuildIdRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildIdRsp.Unmarshal(m, b)
}
func (m *GetGuildIdRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildIdRsp.Marshal(b, m, deterministic)
}
func (dst *GetGuildIdRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildIdRsp.Merge(dst, src)
}
func (m *GetGuildIdRsp) XXX_Size() int {
	return xxx_messageInfo_GetGuildIdRsp.Size(m)
}
func (m *GetGuildIdRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildIdRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildIdRsp proto.InternalMessageInfo

func (m *GetGuildIdRsp) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *GetGuildIdRsp) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GetGuildIdRsp) GetGuildShortId() uint32 {
	if m != nil {
		return m.GuildShortId
	}
	return 0
}

// assessStatus多人合作库申请推荐房考核状态
type ExamUidGuildIdInfo struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	Guildid              uint32   `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExamUidGuildIdInfo) Reset()         { *m = ExamUidGuildIdInfo{} }
func (m *ExamUidGuildIdInfo) String() string { return proto.CompactTextString(m) }
func (*ExamUidGuildIdInfo) ProtoMessage()    {}
func (*ExamUidGuildIdInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{54}
}
func (m *ExamUidGuildIdInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamUidGuildIdInfo.Unmarshal(m, b)
}
func (m *ExamUidGuildIdInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamUidGuildIdInfo.Marshal(b, m, deterministic)
}
func (dst *ExamUidGuildIdInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamUidGuildIdInfo.Merge(dst, src)
}
func (m *ExamUidGuildIdInfo) XXX_Size() int {
	return xxx_messageInfo_ExamUidGuildIdInfo.Size(m)
}
func (m *ExamUidGuildIdInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamUidGuildIdInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ExamUidGuildIdInfo proto.InternalMessageInfo

func (m *ExamUidGuildIdInfo) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *ExamUidGuildIdInfo) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type RecommendExamLimitCheckResp struct {
	IsLimit              uint32   `protobuf:"varint,1,opt,name=is_limit,json=isLimit,proto3" json:"is_limit"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendExamLimitCheckResp) Reset()         { *m = RecommendExamLimitCheckResp{} }
func (m *RecommendExamLimitCheckResp) String() string { return proto.CompactTextString(m) }
func (*RecommendExamLimitCheckResp) ProtoMessage()    {}
func (*RecommendExamLimitCheckResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{55}
}
func (m *RecommendExamLimitCheckResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendExamLimitCheckResp.Unmarshal(m, b)
}
func (m *RecommendExamLimitCheckResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendExamLimitCheckResp.Marshal(b, m, deterministic)
}
func (dst *RecommendExamLimitCheckResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendExamLimitCheckResp.Merge(dst, src)
}
func (m *RecommendExamLimitCheckResp) XXX_Size() int {
	return xxx_messageInfo_RecommendExamLimitCheckResp.Size(m)
}
func (m *RecommendExamLimitCheckResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendExamLimitCheckResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendExamLimitCheckResp proto.InternalMessageInfo

func (m *RecommendExamLimitCheckResp) GetIsLimit() uint32 {
	if m != nil {
		return m.IsLimit
	}
	return 0
}

// openChannel 公会厅
type OpenChannelReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	Guildid              uint32   `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page"`
	PageSize             uint32   `protobuf:"varint,4,opt,name=pageSize,proto3" json:"pageSize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OpenChannelReq) Reset()         { *m = OpenChannelReq{} }
func (m *OpenChannelReq) String() string { return proto.CompactTextString(m) }
func (*OpenChannelReq) ProtoMessage()    {}
func (*OpenChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{56}
}
func (m *OpenChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenChannelReq.Unmarshal(m, b)
}
func (m *OpenChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenChannelReq.Marshal(b, m, deterministic)
}
func (dst *OpenChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenChannelReq.Merge(dst, src)
}
func (m *OpenChannelReq) XXX_Size() int {
	return xxx_messageInfo_OpenChannelReq.Size(m)
}
func (m *OpenChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_OpenChannelReq proto.InternalMessageInfo

func (m *OpenChannelReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *OpenChannelReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *OpenChannelReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *OpenChannelReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type TagInfo struct {
	Tagbg                string   `protobuf:"bytes,1,opt,name=tagbg,proto3" json:"tagbg"`
	TagText              string   `protobuf:"bytes,2,opt,name=tagText,proto3" json:"tagText"`
	TagId                uint32   `protobuf:"varint,3,opt,name=tagId,proto3" json:"tagId"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channelId,proto3" json:"channelId"`
	ChannelName          string   `protobuf:"bytes,5,opt,name=channelName,proto3" json:"channelName"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TagInfo) Reset()         { *m = TagInfo{} }
func (m *TagInfo) String() string { return proto.CompactTextString(m) }
func (*TagInfo) ProtoMessage()    {}
func (*TagInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{57}
}
func (m *TagInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagInfo.Unmarshal(m, b)
}
func (m *TagInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagInfo.Marshal(b, m, deterministic)
}
func (dst *TagInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagInfo.Merge(dst, src)
}
func (m *TagInfo) XXX_Size() int {
	return xxx_messageInfo_TagInfo.Size(m)
}
func (m *TagInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TagInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TagInfo proto.InternalMessageInfo

func (m *TagInfo) GetTagbg() string {
	if m != nil {
		return m.Tagbg
	}
	return ""
}

func (m *TagInfo) GetTagText() string {
	if m != nil {
		return m.TagText
	}
	return ""
}

func (m *TagInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *TagInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TagInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

type OpenChannelResp struct {
	List                 []*TagInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	Total                uint32     `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *OpenChannelResp) Reset()         { *m = OpenChannelResp{} }
func (m *OpenChannelResp) String() string { return proto.CompactTextString(m) }
func (*OpenChannelResp) ProtoMessage()    {}
func (*OpenChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{58}
}
func (m *OpenChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenChannelResp.Unmarshal(m, b)
}
func (m *OpenChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenChannelResp.Marshal(b, m, deterministic)
}
func (dst *OpenChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenChannelResp.Merge(dst, src)
}
func (m *OpenChannelResp) XXX_Size() int {
	return xxx_messageInfo_OpenChannelResp.Size(m)
}
func (m *OpenChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_OpenChannelResp proto.InternalMessageInfo

func (m *OpenChannelResp) GetList() []*TagInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *OpenChannelResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// multiLaymans 申请多人互动所有人列表
type RecommendUsersReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	Guildid              uint32   `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channelId,proto3" json:"channelId"`
	Page                 uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page"`
	PageSize             uint32   `protobuf:"varint,5,opt,name=pageSize,proto3" json:"pageSize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendUsersReq) Reset()         { *m = RecommendUsersReq{} }
func (m *RecommendUsersReq) String() string { return proto.CompactTextString(m) }
func (*RecommendUsersReq) ProtoMessage()    {}
func (*RecommendUsersReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{59}
}
func (m *RecommendUsersReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendUsersReq.Unmarshal(m, b)
}
func (m *RecommendUsersReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendUsersReq.Marshal(b, m, deterministic)
}
func (dst *RecommendUsersReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendUsersReq.Merge(dst, src)
}
func (m *RecommendUsersReq) XXX_Size() int {
	return xxx_messageInfo_RecommendUsersReq.Size(m)
}
func (m *RecommendUsersReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendUsersReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendUsersReq proto.InternalMessageInfo

func (m *RecommendUsersReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *RecommendUsersReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *RecommendUsersReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RecommendUsersReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *RecommendUsersReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type UserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account"`
	NickName             string   `protobuf:"bytes,3,opt,name=nickName,proto3" json:"nickName"`
	Alias                string   `protobuf:"bytes,4,opt,name=alias,proto3" json:"alias"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfo) Reset()         { *m = UserInfo{} }
func (m *UserInfo) String() string { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()    {}
func (*UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{60}
}
func (m *UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo.Unmarshal(m, b)
}
func (m *UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo.Marshal(b, m, deterministic)
}
func (dst *UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo.Merge(dst, src)
}
func (m *UserInfo) XXX_Size() int {
	return xxx_messageInfo_UserInfo.Size(m)
}
func (m *UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo proto.InternalMessageInfo

func (m *UserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *UserInfo) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

// searchMultiLayman 搜索多人互动公开厅考核所有人
type SearchMultiLayman struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	Guildid              uint32   `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channelId,proto3" json:"channelId"`
	Keyword              string   `protobuf:"bytes,4,opt,name=keyword,proto3" json:"keyword"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchMultiLayman) Reset()         { *m = SearchMultiLayman{} }
func (m *SearchMultiLayman) String() string { return proto.CompactTextString(m) }
func (*SearchMultiLayman) ProtoMessage()    {}
func (*SearchMultiLayman) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{61}
}
func (m *SearchMultiLayman) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchMultiLayman.Unmarshal(m, b)
}
func (m *SearchMultiLayman) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchMultiLayman.Marshal(b, m, deterministic)
}
func (dst *SearchMultiLayman) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchMultiLayman.Merge(dst, src)
}
func (m *SearchMultiLayman) XXX_Size() int {
	return xxx_messageInfo_SearchMultiLayman.Size(m)
}
func (m *SearchMultiLayman) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchMultiLayman.DiscardUnknown(m)
}

var xxx_messageInfo_SearchMultiLayman proto.InternalMessageInfo

func (m *SearchMultiLayman) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *SearchMultiLayman) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *SearchMultiLayman) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SearchMultiLayman) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

type RecommendUsersResp struct {
	List                 []*UserInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	Total                uint32      `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *RecommendUsersResp) Reset()         { *m = RecommendUsersResp{} }
func (m *RecommendUsersResp) String() string { return proto.CompactTextString(m) }
func (*RecommendUsersResp) ProtoMessage()    {}
func (*RecommendUsersResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{62}
}
func (m *RecommendUsersResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendUsersResp.Unmarshal(m, b)
}
func (m *RecommendUsersResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendUsersResp.Marshal(b, m, deterministic)
}
func (dst *RecommendUsersResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendUsersResp.Merge(dst, src)
}
func (m *RecommendUsersResp) XXX_Size() int {
	return xxx_messageInfo_RecommendUsersResp.Size(m)
}
func (m *RecommendUsersResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendUsersResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendUsersResp proto.InternalMessageInfo

func (m *RecommendUsersResp) GetList() []*UserInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *RecommendUsersResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// multiAssess 申请多人互动推荐房考核
type RecommendExamBaseInfo struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	Guildid              uint32   `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid"`
	Purpose              uint32   `protobuf:"varint,3,opt,name=purpose,proto3" json:"purpose"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channelId,proto3" json:"channelId"`
	CurrentTag           uint32   `protobuf:"varint,5,opt,name=currentTag,proto3" json:"currentTag"`
	ExamTag              uint32   `protobuf:"varint,6,opt,name=examTag,proto3" json:"examTag"`
	ExamUids             []uint32 `protobuf:"varint,7,rep,packed,name=examUids,proto3" json:"examUids"`
	Password             string   `protobuf:"bytes,8,opt,name=password,proto3" json:"password"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendExamBaseInfo) Reset()         { *m = RecommendExamBaseInfo{} }
func (m *RecommendExamBaseInfo) String() string { return proto.CompactTextString(m) }
func (*RecommendExamBaseInfo) ProtoMessage()    {}
func (*RecommendExamBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{63}
}
func (m *RecommendExamBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendExamBaseInfo.Unmarshal(m, b)
}
func (m *RecommendExamBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendExamBaseInfo.Marshal(b, m, deterministic)
}
func (dst *RecommendExamBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendExamBaseInfo.Merge(dst, src)
}
func (m *RecommendExamBaseInfo) XXX_Size() int {
	return xxx_messageInfo_RecommendExamBaseInfo.Size(m)
}
func (m *RecommendExamBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendExamBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendExamBaseInfo proto.InternalMessageInfo

func (m *RecommendExamBaseInfo) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *RecommendExamBaseInfo) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *RecommendExamBaseInfo) GetPurpose() uint32 {
	if m != nil {
		return m.Purpose
	}
	return 0
}

func (m *RecommendExamBaseInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RecommendExamBaseInfo) GetCurrentTag() uint32 {
	if m != nil {
		return m.CurrentTag
	}
	return 0
}

func (m *RecommendExamBaseInfo) GetExamTag() uint32 {
	if m != nil {
		return m.ExamTag
	}
	return 0
}

func (m *RecommendExamBaseInfo) GetExamUids() []uint32 {
	if m != nil {
		return m.ExamUids
	}
	return nil
}

func (m *RecommendExamBaseInfo) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

type ExamTagInfo struct {
	ExamId               uint32   `protobuf:"varint,1,opt,name=examId,proto3" json:"examId"`
	Tag                  string   `protobuf:"bytes,2,opt,name=tag,proto3" json:"tag"`
	LimitNumber          uint32   `protobuf:"varint,3,opt,name=limit_number,json=limitNumber,proto3" json:"limit_number"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExamTagInfo) Reset()         { *m = ExamTagInfo{} }
func (m *ExamTagInfo) String() string { return proto.CompactTextString(m) }
func (*ExamTagInfo) ProtoMessage()    {}
func (*ExamTagInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{64}
}
func (m *ExamTagInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamTagInfo.Unmarshal(m, b)
}
func (m *ExamTagInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamTagInfo.Marshal(b, m, deterministic)
}
func (dst *ExamTagInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamTagInfo.Merge(dst, src)
}
func (m *ExamTagInfo) XXX_Size() int {
	return xxx_messageInfo_ExamTagInfo.Size(m)
}
func (m *ExamTagInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamTagInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ExamTagInfo proto.InternalMessageInfo

func (m *ExamTagInfo) GetExamId() uint32 {
	if m != nil {
		return m.ExamId
	}
	return 0
}

func (m *ExamTagInfo) GetTag() string {
	if m != nil {
		return m.Tag
	}
	return ""
}

func (m *ExamTagInfo) GetLimitNumber() uint32 {
	if m != nil {
		return m.LimitNumber
	}
	return 0
}

// mutliRoomExam 多人互动合作库推荐房考核项目
type GetExamTagListResp struct {
	ExamList             []*ExamTagInfo `protobuf:"bytes,1,rep,name=examList,proto3" json:"examList"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetExamTagListResp) Reset()         { *m = GetExamTagListResp{} }
func (m *GetExamTagListResp) String() string { return proto.CompactTextString(m) }
func (*GetExamTagListResp) ProtoMessage()    {}
func (*GetExamTagListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{65}
}
func (m *GetExamTagListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExamTagListResp.Unmarshal(m, b)
}
func (m *GetExamTagListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExamTagListResp.Marshal(b, m, deterministic)
}
func (dst *GetExamTagListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExamTagListResp.Merge(dst, src)
}
func (m *GetExamTagListResp) XXX_Size() int {
	return xxx_messageInfo_GetExamTagListResp.Size(m)
}
func (m *GetExamTagListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExamTagListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExamTagListResp proto.InternalMessageInfo

func (m *GetExamTagListResp) GetExamList() []*ExamTagInfo {
	if m != nil {
		return m.ExamList
	}
	return nil
}

type YuyinExamApplicationInfo struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account"`
	NickName             string   `protobuf:"bytes,3,opt,name=nickName,proto3" json:"nickName"`
	Alias                string   `protobuf:"bytes,4,opt,name=alias,proto3" json:"alias"`
	ExamTag              uint32   `protobuf:"varint,5,opt,name=examTag,proto3" json:"examTag"`
	Link                 string   `protobuf:"bytes,6,opt,name=link,proto3" json:"link"`
	BeginTime            uint32   `protobuf:"varint,7,opt,name=beginTime,proto3" json:"beginTime"`
	EndTime              uint32   `protobuf:"varint,8,opt,name=endTime,proto3" json:"endTime"`
	TagName              string   `protobuf:"bytes,9,opt,name=tagName,proto3" json:"tagName"`
	AssessStatus         uint32   `protobuf:"varint,10,opt,name=assessStatus,proto3" json:"assessStatus"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinExamApplicationInfo) Reset()         { *m = YuyinExamApplicationInfo{} }
func (m *YuyinExamApplicationInfo) String() string { return proto.CompactTextString(m) }
func (*YuyinExamApplicationInfo) ProtoMessage()    {}
func (*YuyinExamApplicationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{66}
}
func (m *YuyinExamApplicationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamApplicationInfo.Unmarshal(m, b)
}
func (m *YuyinExamApplicationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamApplicationInfo.Marshal(b, m, deterministic)
}
func (dst *YuyinExamApplicationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamApplicationInfo.Merge(dst, src)
}
func (m *YuyinExamApplicationInfo) XXX_Size() int {
	return xxx_messageInfo_YuyinExamApplicationInfo.Size(m)
}
func (m *YuyinExamApplicationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamApplicationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamApplicationInfo proto.InternalMessageInfo

func (m *YuyinExamApplicationInfo) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *YuyinExamApplicationInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *YuyinExamApplicationInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *YuyinExamApplicationInfo) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *YuyinExamApplicationInfo) GetExamTag() uint32 {
	if m != nil {
		return m.ExamTag
	}
	return 0
}

func (m *YuyinExamApplicationInfo) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

func (m *YuyinExamApplicationInfo) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *YuyinExamApplicationInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *YuyinExamApplicationInfo) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *YuyinExamApplicationInfo) GetAssessStatus() uint32 {
	if m != nil {
		return m.AssessStatus
	}
	return 0
}

// handleAssess
type YuyinExamGuildOwnerOperationReq struct {
	Uid                  string          `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	AnchorUids           []string        `protobuf:"bytes,2,rep,name=anchorUids,proto3" json:"anchorUids"`
	Guildid              uint32          `protobuf:"varint,3,opt,name=guildid,proto3" json:"guildid"`
	YuyinStatus          YuyinExamStatus `protobuf:"varint,4,opt,name=yuyinStatus,proto3,enum=gold_diamond_logic.YuyinExamStatus" json:"yuyinStatus"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *YuyinExamGuildOwnerOperationReq) Reset()         { *m = YuyinExamGuildOwnerOperationReq{} }
func (m *YuyinExamGuildOwnerOperationReq) String() string { return proto.CompactTextString(m) }
func (*YuyinExamGuildOwnerOperationReq) ProtoMessage()    {}
func (*YuyinExamGuildOwnerOperationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{67}
}
func (m *YuyinExamGuildOwnerOperationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamGuildOwnerOperationReq.Unmarshal(m, b)
}
func (m *YuyinExamGuildOwnerOperationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamGuildOwnerOperationReq.Marshal(b, m, deterministic)
}
func (dst *YuyinExamGuildOwnerOperationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamGuildOwnerOperationReq.Merge(dst, src)
}
func (m *YuyinExamGuildOwnerOperationReq) XXX_Size() int {
	return xxx_messageInfo_YuyinExamGuildOwnerOperationReq.Size(m)
}
func (m *YuyinExamGuildOwnerOperationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamGuildOwnerOperationReq.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamGuildOwnerOperationReq proto.InternalMessageInfo

func (m *YuyinExamGuildOwnerOperationReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *YuyinExamGuildOwnerOperationReq) GetAnchorUids() []string {
	if m != nil {
		return m.AnchorUids
	}
	return nil
}

func (m *YuyinExamGuildOwnerOperationReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *YuyinExamGuildOwnerOperationReq) GetYuyinStatus() YuyinExamStatus {
	if m != nil {
		return m.YuyinStatus
	}
	return YuyinExamStatus_YE_DEFAULT
}

type YuyinExamGuildOwnerOperationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinExamGuildOwnerOperationResp) Reset()         { *m = YuyinExamGuildOwnerOperationResp{} }
func (m *YuyinExamGuildOwnerOperationResp) String() string { return proto.CompactTextString(m) }
func (*YuyinExamGuildOwnerOperationResp) ProtoMessage()    {}
func (*YuyinExamGuildOwnerOperationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{68}
}
func (m *YuyinExamGuildOwnerOperationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamGuildOwnerOperationResp.Unmarshal(m, b)
}
func (m *YuyinExamGuildOwnerOperationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamGuildOwnerOperationResp.Marshal(b, m, deterministic)
}
func (dst *YuyinExamGuildOwnerOperationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamGuildOwnerOperationResp.Merge(dst, src)
}
func (m *YuyinExamGuildOwnerOperationResp) XXX_Size() int {
	return xxx_messageInfo_YuyinExamGuildOwnerOperationResp.Size(m)
}
func (m *YuyinExamGuildOwnerOperationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamGuildOwnerOperationResp.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamGuildOwnerOperationResp proto.InternalMessageInfo

// 主播提交考核
type YuyinExamReq struct {
	Info                 *YuyinExamApplicationInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *YuyinExamReq) Reset()         { *m = YuyinExamReq{} }
func (m *YuyinExamReq) String() string { return proto.CompactTextString(m) }
func (*YuyinExamReq) ProtoMessage()    {}
func (*YuyinExamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{69}
}
func (m *YuyinExamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamReq.Unmarshal(m, b)
}
func (m *YuyinExamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamReq.Marshal(b, m, deterministic)
}
func (dst *YuyinExamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamReq.Merge(dst, src)
}
func (m *YuyinExamReq) XXX_Size() int {
	return xxx_messageInfo_YuyinExamReq.Size(m)
}
func (m *YuyinExamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamReq.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamReq proto.InternalMessageInfo

func (m *YuyinExamReq) GetInfo() *YuyinExamApplicationInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type YuyinExamResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinExamResp) Reset()         { *m = YuyinExamResp{} }
func (m *YuyinExamResp) String() string { return proto.CompactTextString(m) }
func (*YuyinExamResp) ProtoMessage()    {}
func (*YuyinExamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{70}
}
func (m *YuyinExamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamResp.Unmarshal(m, b)
}
func (m *YuyinExamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamResp.Marshal(b, m, deterministic)
}
func (dst *YuyinExamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamResp.Merge(dst, src)
}
func (m *YuyinExamResp) XXX_Size() int {
	return xxx_messageInfo_YuyinExamResp.Size(m)
}
func (m *YuyinExamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamResp.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamResp proto.InternalMessageInfo

// 主播查看提交的考核信息
type GetYuyinExamReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetYuyinExamReq) Reset()         { *m = GetYuyinExamReq{} }
func (m *GetYuyinExamReq) String() string { return proto.CompactTextString(m) }
func (*GetYuyinExamReq) ProtoMessage()    {}
func (*GetYuyinExamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{71}
}
func (m *GetYuyinExamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinExamReq.Unmarshal(m, b)
}
func (m *GetYuyinExamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinExamReq.Marshal(b, m, deterministic)
}
func (dst *GetYuyinExamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinExamReq.Merge(dst, src)
}
func (m *GetYuyinExamReq) XXX_Size() int {
	return xxx_messageInfo_GetYuyinExamReq.Size(m)
}
func (m *GetYuyinExamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinExamReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinExamReq proto.InternalMessageInfo

func (m *GetYuyinExamReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

type GetYuyinExamResp struct {
	IsLimit              bool                      `protobuf:"varint,1,opt,name=isLimit,proto3" json:"isLimit"`
	Info                 *YuyinExamApplicationInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetYuyinExamResp) Reset()         { *m = GetYuyinExamResp{} }
func (m *GetYuyinExamResp) String() string { return proto.CompactTextString(m) }
func (*GetYuyinExamResp) ProtoMessage()    {}
func (*GetYuyinExamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{72}
}
func (m *GetYuyinExamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinExamResp.Unmarshal(m, b)
}
func (m *GetYuyinExamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinExamResp.Marshal(b, m, deterministic)
}
func (dst *GetYuyinExamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinExamResp.Merge(dst, src)
}
func (m *GetYuyinExamResp) XXX_Size() int {
	return xxx_messageInfo_GetYuyinExamResp.Size(m)
}
func (m *GetYuyinExamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinExamResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinExamResp proto.InternalMessageInfo

func (m *GetYuyinExamResp) GetIsLimit() bool {
	if m != nil {
		return m.IsLimit
	}
	return false
}

func (m *GetYuyinExamResp) GetInfo() *YuyinExamApplicationInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// assessList
type GetYuyinExamForGuildResp struct {
	List                 []*YuyinExamApplicationInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	Total                uint32                      `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetYuyinExamForGuildResp) Reset()         { *m = GetYuyinExamForGuildResp{} }
func (m *GetYuyinExamForGuildResp) String() string { return proto.CompactTextString(m) }
func (*GetYuyinExamForGuildResp) ProtoMessage()    {}
func (*GetYuyinExamForGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{73}
}
func (m *GetYuyinExamForGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinExamForGuildResp.Unmarshal(m, b)
}
func (m *GetYuyinExamForGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinExamForGuildResp.Marshal(b, m, deterministic)
}
func (dst *GetYuyinExamForGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinExamForGuildResp.Merge(dst, src)
}
func (m *GetYuyinExamForGuildResp) XXX_Size() int {
	return xxx_messageInfo_GetYuyinExamForGuildResp.Size(m)
}
func (m *GetYuyinExamForGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinExamForGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinExamForGuildResp proto.InternalMessageInfo

func (m *GetYuyinExamForGuildResp) GetList() []*YuyinExamApplicationInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetYuyinExamForGuildResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// appraisalStatus 考核状态
type GetYuyinExamStatusReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetYuyinExamStatusReq) Reset()         { *m = GetYuyinExamStatusReq{} }
func (m *GetYuyinExamStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetYuyinExamStatusReq) ProtoMessage()    {}
func (*GetYuyinExamStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{74}
}
func (m *GetYuyinExamStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinExamStatusReq.Unmarshal(m, b)
}
func (m *GetYuyinExamStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinExamStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetYuyinExamStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinExamStatusReq.Merge(dst, src)
}
func (m *GetYuyinExamStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetYuyinExamStatusReq.Size(m)
}
func (m *GetYuyinExamStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinExamStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinExamStatusReq proto.InternalMessageInfo

func (m *GetYuyinExamStatusReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

type GetYuyinExamStatusResp struct {
	Status               int32    `protobuf:"varint,1,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetYuyinExamStatusResp) Reset()         { *m = GetYuyinExamStatusResp{} }
func (m *GetYuyinExamStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetYuyinExamStatusResp) ProtoMessage()    {}
func (*GetYuyinExamStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{75}
}
func (m *GetYuyinExamStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinExamStatusResp.Unmarshal(m, b)
}
func (m *GetYuyinExamStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinExamStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetYuyinExamStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinExamStatusResp.Merge(dst, src)
}
func (m *GetYuyinExamStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetYuyinExamStatusResp.Size(m)
}
func (m *GetYuyinExamStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinExamStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinExamStatusResp proto.InternalMessageInfo

func (m *GetYuyinExamStatusResp) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type TagsInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TagsInfo) Reset()         { *m = TagsInfo{} }
func (m *TagsInfo) String() string { return proto.CompactTextString(m) }
func (*TagsInfo) ProtoMessage()    {}
func (*TagsInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{76}
}
func (m *TagsInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagsInfo.Unmarshal(m, b)
}
func (m *TagsInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagsInfo.Marshal(b, m, deterministic)
}
func (dst *TagsInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagsInfo.Merge(dst, src)
}
func (m *TagsInfo) XXX_Size() int {
	return xxx_messageInfo_TagsInfo.Size(m)
}
func (m *TagsInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TagsInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TagsInfo proto.InternalMessageInfo

func (m *TagsInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TagsInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type SubmitInfo struct {
	Tag                  uint32   `protobuf:"varint,1,opt,name=tag,proto3" json:"tag"`
	Link                 string   `protobuf:"bytes,2,opt,name=link,proto3" json:"link"`
	Contact              string   `protobuf:"bytes,3,opt,name=contact,proto3" json:"contact"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitInfo) Reset()         { *m = SubmitInfo{} }
func (m *SubmitInfo) String() string { return proto.CompactTextString(m) }
func (*SubmitInfo) ProtoMessage()    {}
func (*SubmitInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{77}
}
func (m *SubmitInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitInfo.Unmarshal(m, b)
}
func (m *SubmitInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitInfo.Marshal(b, m, deterministic)
}
func (dst *SubmitInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitInfo.Merge(dst, src)
}
func (m *SubmitInfo) XXX_Size() int {
	return xxx_messageInfo_SubmitInfo.Size(m)
}
func (m *SubmitInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitInfo proto.InternalMessageInfo

func (m *SubmitInfo) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *SubmitInfo) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

func (m *SubmitInfo) GetContact() string {
	if m != nil {
		return m.Contact
	}
	return ""
}

// appraisalInit 信息提交初始化
type YuyinExamInfo struct {
	Tags                 []*TagsInfo `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags"`
	Submit               *SubmitInfo `protobuf:"bytes,2,opt,name=submit,proto3" json:"submit"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *YuyinExamInfo) Reset()         { *m = YuyinExamInfo{} }
func (m *YuyinExamInfo) String() string { return proto.CompactTextString(m) }
func (*YuyinExamInfo) ProtoMessage()    {}
func (*YuyinExamInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{78}
}
func (m *YuyinExamInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamInfo.Unmarshal(m, b)
}
func (m *YuyinExamInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamInfo.Marshal(b, m, deterministic)
}
func (dst *YuyinExamInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamInfo.Merge(dst, src)
}
func (m *YuyinExamInfo) XXX_Size() int {
	return xxx_messageInfo_YuyinExamInfo.Size(m)
}
func (m *YuyinExamInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamInfo.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamInfo proto.InternalMessageInfo

func (m *YuyinExamInfo) GetTags() []*TagsInfo {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *YuyinExamInfo) GetSubmit() *SubmitInfo {
	if m != nil {
		return m.Submit
	}
	return nil
}

// submitAppraisal 提交考核内容
type YuyinSubmit struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	Tag                  uint32   `protobuf:"varint,2,opt,name=tag,proto3" json:"tag"`
	Link                 string   `protobuf:"bytes,3,opt,name=link,proto3" json:"link"`
	Contact              string   `protobuf:"bytes,4,opt,name=contact,proto3" json:"contact"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinSubmit) Reset()         { *m = YuyinSubmit{} }
func (m *YuyinSubmit) String() string { return proto.CompactTextString(m) }
func (*YuyinSubmit) ProtoMessage()    {}
func (*YuyinSubmit) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{79}
}
func (m *YuyinSubmit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinSubmit.Unmarshal(m, b)
}
func (m *YuyinSubmit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinSubmit.Marshal(b, m, deterministic)
}
func (dst *YuyinSubmit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinSubmit.Merge(dst, src)
}
func (m *YuyinSubmit) XXX_Size() int {
	return xxx_messageInfo_YuyinSubmit.Size(m)
}
func (m *YuyinSubmit) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinSubmit.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinSubmit proto.InternalMessageInfo

func (m *YuyinSubmit) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *YuyinSubmit) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *YuyinSubmit) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

func (m *YuyinSubmit) GetContact() string {
	if m != nil {
		return m.Contact
	}
	return ""
}

// 会长查看申请记录
type ApplicationRecordForGuildOwnerReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	Guildid              uint32   `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid"`
	Keyword              string   `protobuf:"bytes,3,opt,name=keyword,proto3" json:"keyword"`
	Page                 uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page"`
	PageSize             uint32   `protobuf:"varint,5,opt,name=pageSize,proto3" json:"pageSize"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplicationRecordForGuildOwnerReq) Reset()         { *m = ApplicationRecordForGuildOwnerReq{} }
func (m *ApplicationRecordForGuildOwnerReq) String() string { return proto.CompactTextString(m) }
func (*ApplicationRecordForGuildOwnerReq) ProtoMessage()    {}
func (*ApplicationRecordForGuildOwnerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{80}
}
func (m *ApplicationRecordForGuildOwnerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationRecordForGuildOwnerReq.Unmarshal(m, b)
}
func (m *ApplicationRecordForGuildOwnerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationRecordForGuildOwnerReq.Marshal(b, m, deterministic)
}
func (dst *ApplicationRecordForGuildOwnerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationRecordForGuildOwnerReq.Merge(dst, src)
}
func (m *ApplicationRecordForGuildOwnerReq) XXX_Size() int {
	return xxx_messageInfo_ApplicationRecordForGuildOwnerReq.Size(m)
}
func (m *ApplicationRecordForGuildOwnerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationRecordForGuildOwnerReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationRecordForGuildOwnerReq proto.InternalMessageInfo

func (m *ApplicationRecordForGuildOwnerReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *ApplicationRecordForGuildOwnerReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *ApplicationRecordForGuildOwnerReq) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

func (m *ApplicationRecordForGuildOwnerReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *ApplicationRecordForGuildOwnerReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type ApplicationRecordInfo struct {
	SubmitTime           uint32                      `protobuf:"varint,1,opt,name=submitTime,proto3" json:"submitTime"`
	Items                []*YuyinExamApplicationInfo `protobuf:"bytes,2,rep,name=items,proto3" json:"items"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *ApplicationRecordInfo) Reset()         { *m = ApplicationRecordInfo{} }
func (m *ApplicationRecordInfo) String() string { return proto.CompactTextString(m) }
func (*ApplicationRecordInfo) ProtoMessage()    {}
func (*ApplicationRecordInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{81}
}
func (m *ApplicationRecordInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationRecordInfo.Unmarshal(m, b)
}
func (m *ApplicationRecordInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationRecordInfo.Marshal(b, m, deterministic)
}
func (dst *ApplicationRecordInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationRecordInfo.Merge(dst, src)
}
func (m *ApplicationRecordInfo) XXX_Size() int {
	return xxx_messageInfo_ApplicationRecordInfo.Size(m)
}
func (m *ApplicationRecordInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationRecordInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationRecordInfo proto.InternalMessageInfo

func (m *ApplicationRecordInfo) GetSubmitTime() uint32 {
	if m != nil {
		return m.SubmitTime
	}
	return 0
}

func (m *ApplicationRecordInfo) GetItems() []*YuyinExamApplicationInfo {
	if m != nil {
		return m.Items
	}
	return nil
}

type ApplicationRecordForGuildOwnerResp struct {
	List                 []*ApplicationRecordInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	NextPage             uint32                   `protobuf:"varint,2,opt,name=nextPage,proto3" json:"nextPage"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *ApplicationRecordForGuildOwnerResp) Reset()         { *m = ApplicationRecordForGuildOwnerResp{} }
func (m *ApplicationRecordForGuildOwnerResp) String() string { return proto.CompactTextString(m) }
func (*ApplicationRecordForGuildOwnerResp) ProtoMessage()    {}
func (*ApplicationRecordForGuildOwnerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{82}
}
func (m *ApplicationRecordForGuildOwnerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationRecordForGuildOwnerResp.Unmarshal(m, b)
}
func (m *ApplicationRecordForGuildOwnerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationRecordForGuildOwnerResp.Marshal(b, m, deterministic)
}
func (dst *ApplicationRecordForGuildOwnerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationRecordForGuildOwnerResp.Merge(dst, src)
}
func (m *ApplicationRecordForGuildOwnerResp) XXX_Size() int {
	return xxx_messageInfo_ApplicationRecordForGuildOwnerResp.Size(m)
}
func (m *ApplicationRecordForGuildOwnerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationRecordForGuildOwnerResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationRecordForGuildOwnerResp proto.InternalMessageInfo

func (m *ApplicationRecordForGuildOwnerResp) GetList() []*ApplicationRecordInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *ApplicationRecordForGuildOwnerResp) GetNextPage() uint32 {
	if m != nil {
		return m.NextPage
	}
	return 0
}

type CheckUidMainReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUidMainReq) Reset()         { *m = CheckUidMainReq{} }
func (m *CheckUidMainReq) String() string { return proto.CompactTextString(m) }
func (*CheckUidMainReq) ProtoMessage()    {}
func (*CheckUidMainReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{83}
}
func (m *CheckUidMainReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUidMainReq.Unmarshal(m, b)
}
func (m *CheckUidMainReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUidMainReq.Marshal(b, m, deterministic)
}
func (dst *CheckUidMainReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUidMainReq.Merge(dst, src)
}
func (m *CheckUidMainReq) XXX_Size() int {
	return xxx_messageInfo_CheckUidMainReq.Size(m)
}
func (m *CheckUidMainReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUidMainReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUidMainReq proto.InternalMessageInfo

func (m *CheckUidMainReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

type CheckUidMainResp struct {
	IsMain               uint32   `protobuf:"varint,1,opt,name=is_main,json=isMain,proto3" json:"is_main"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUidMainResp) Reset()         { *m = CheckUidMainResp{} }
func (m *CheckUidMainResp) String() string { return proto.CompactTextString(m) }
func (*CheckUidMainResp) ProtoMessage()    {}
func (*CheckUidMainResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{84}
}
func (m *CheckUidMainResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUidMainResp.Unmarshal(m, b)
}
func (m *CheckUidMainResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUidMainResp.Marshal(b, m, deterministic)
}
func (dst *CheckUidMainResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUidMainResp.Merge(dst, src)
}
func (m *CheckUidMainResp) XXX_Size() int {
	return xxx_messageInfo_CheckUidMainResp.Size(m)
}
func (m *CheckUidMainResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUidMainResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUidMainResp proto.InternalMessageInfo

func (m *CheckUidMainResp) GetIsMain() uint32 {
	if m != nil {
		return m.IsMain
	}
	return 0
}

type AmuseExtraIncomeDetailReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	YearMonth            uint32   `protobuf:"varint,3,opt,name=year_month,json=yearMonth,proto3" json:"year_month"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AmuseExtraIncomeDetailReq) Reset()         { *m = AmuseExtraIncomeDetailReq{} }
func (m *AmuseExtraIncomeDetailReq) String() string { return proto.CompactTextString(m) }
func (*AmuseExtraIncomeDetailReq) ProtoMessage()    {}
func (*AmuseExtraIncomeDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{85}
}
func (m *AmuseExtraIncomeDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseExtraIncomeDetailReq.Unmarshal(m, b)
}
func (m *AmuseExtraIncomeDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseExtraIncomeDetailReq.Marshal(b, m, deterministic)
}
func (dst *AmuseExtraIncomeDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseExtraIncomeDetailReq.Merge(dst, src)
}
func (m *AmuseExtraIncomeDetailReq) XXX_Size() int {
	return xxx_messageInfo_AmuseExtraIncomeDetailReq.Size(m)
}
func (m *AmuseExtraIncomeDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseExtraIncomeDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseExtraIncomeDetailReq proto.InternalMessageInfo

func (m *AmuseExtraIncomeDetailReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *AmuseExtraIncomeDetailReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AmuseExtraIncomeDetailReq) GetYearMonth() uint32 {
	if m != nil {
		return m.YearMonth
	}
	return 0
}

type AmuseExtraIncomeDetailResp struct {
	YearMonth            uint32                                    `protobuf:"varint,1,opt,name=year_month,json=yearMonth,proto3" json:"year_month"`
	ThisMonthFee         uint64                                    `protobuf:"varint,2,opt,name=this_month_fee,json=thisMonthFee,proto3" json:"this_month_fee"`
	LastMonthFee         uint64                                    `protobuf:"varint,3,opt,name=last_month_fee,json=lastMonthFee,proto3" json:"last_month_fee"`
	ThisMonthIncome      uint64                                    `protobuf:"varint,4,opt,name=this_month_income,json=thisMonthIncome,proto3" json:"this_month_income"`
	ThisMonthIncomeCny   string                                    `protobuf:"bytes,5,opt,name=this_month_income_cny,json=thisMonthIncomeCny,proto3" json:"this_month_income_cny"`
	PrepaidMoney         uint64                                    `protobuf:"varint,6,opt,name=prepaid_money,json=prepaidMoney,proto3" json:"prepaid_money"`
	PrepaidMoneyCny      string                                    `protobuf:"bytes,7,opt,name=prepaid_money_cny,json=prepaidMoneyCny,proto3" json:"prepaid_money_cny"`
	Remark               string                                    `protobuf:"bytes,8,opt,name=remark,proto3" json:"remark"`
	GrowRate             string                                    `protobuf:"bytes,9,opt,name=grow_rate,json=growRate,proto3" json:"grow_rate"`
	ChannelList          []*AmuseExtraIncomeDetailResp_ChannelItem `protobuf:"bytes,10,rep,name=channel_list,json=channelList,proto3" json:"channel_list"`
	XXX_NoUnkeyedLiteral struct{}                                  `json:"-"`
	XXX_unrecognized     []byte                                    `json:"-"`
	XXX_sizecache        int32                                     `json:"-"`
}

func (m *AmuseExtraIncomeDetailResp) Reset()         { *m = AmuseExtraIncomeDetailResp{} }
func (m *AmuseExtraIncomeDetailResp) String() string { return proto.CompactTextString(m) }
func (*AmuseExtraIncomeDetailResp) ProtoMessage()    {}
func (*AmuseExtraIncomeDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{86}
}
func (m *AmuseExtraIncomeDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseExtraIncomeDetailResp.Unmarshal(m, b)
}
func (m *AmuseExtraIncomeDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseExtraIncomeDetailResp.Marshal(b, m, deterministic)
}
func (dst *AmuseExtraIncomeDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseExtraIncomeDetailResp.Merge(dst, src)
}
func (m *AmuseExtraIncomeDetailResp) XXX_Size() int {
	return xxx_messageInfo_AmuseExtraIncomeDetailResp.Size(m)
}
func (m *AmuseExtraIncomeDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseExtraIncomeDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseExtraIncomeDetailResp proto.InternalMessageInfo

func (m *AmuseExtraIncomeDetailResp) GetYearMonth() uint32 {
	if m != nil {
		return m.YearMonth
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp) GetThisMonthFee() uint64 {
	if m != nil {
		return m.ThisMonthFee
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp) GetLastMonthFee() uint64 {
	if m != nil {
		return m.LastMonthFee
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp) GetThisMonthIncome() uint64 {
	if m != nil {
		return m.ThisMonthIncome
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp) GetThisMonthIncomeCny() string {
	if m != nil {
		return m.ThisMonthIncomeCny
	}
	return ""
}

func (m *AmuseExtraIncomeDetailResp) GetPrepaidMoney() uint64 {
	if m != nil {
		return m.PrepaidMoney
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp) GetPrepaidMoneyCny() string {
	if m != nil {
		return m.PrepaidMoneyCny
	}
	return ""
}

func (m *AmuseExtraIncomeDetailResp) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *AmuseExtraIncomeDetailResp) GetGrowRate() string {
	if m != nil {
		return m.GrowRate
	}
	return ""
}

func (m *AmuseExtraIncomeDetailResp) GetChannelList() []*AmuseExtraIncomeDetailResp_ChannelItem {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type AmuseExtraIncomeDetailResp_ChannelItem struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	ChannelDisplayId     uint32   `protobuf:"varint,2,opt,name=channel_display_id,json=channelDisplayId,proto3" json:"channel_display_id"`
	ChannelName          string   `protobuf:"bytes,3,opt,name=channel_name,json=channelName,proto3" json:"channel_name"`
	ChannelTag           string   `protobuf:"bytes,4,opt,name=channel_tag,json=channelTag,proto3" json:"channel_tag"`
	ThisMonthFee         uint64   `protobuf:"varint,5,opt,name=this_month_fee,json=thisMonthFee,proto3" json:"this_month_fee"`
	SettlementRate       string   `protobuf:"bytes,6,opt,name=settlement_rate,json=settlementRate,proto3" json:"settlement_rate"`
	ThisMonthIncome      uint64   `protobuf:"varint,7,opt,name=this_month_income,json=thisMonthIncome,proto3" json:"this_month_income"`
	GuildId              uint32   `protobuf:"varint,8,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	GuildDisplayId       uint32   `protobuf:"varint,9,opt,name=guild_display_id,json=guildDisplayId,proto3" json:"guild_display_id"`
	LastMonthFee         uint64   `protobuf:"varint,10,opt,name=last_month_fee,json=lastMonthFee,proto3" json:"last_month_fee"`
	GrowRate             string   `protobuf:"bytes,11,opt,name=grow_rate,json=growRate,proto3" json:"grow_rate"`
	ChannelViewId        string   `protobuf:"bytes,12,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) Reset() {
	*m = AmuseExtraIncomeDetailResp_ChannelItem{}
}
func (m *AmuseExtraIncomeDetailResp_ChannelItem) String() string { return proto.CompactTextString(m) }
func (*AmuseExtraIncomeDetailResp_ChannelItem) ProtoMessage()    {}
func (*AmuseExtraIncomeDetailResp_ChannelItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{86, 0}
}
func (m *AmuseExtraIncomeDetailResp_ChannelItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseExtraIncomeDetailResp_ChannelItem.Unmarshal(m, b)
}
func (m *AmuseExtraIncomeDetailResp_ChannelItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseExtraIncomeDetailResp_ChannelItem.Marshal(b, m, deterministic)
}
func (dst *AmuseExtraIncomeDetailResp_ChannelItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseExtraIncomeDetailResp_ChannelItem.Merge(dst, src)
}
func (m *AmuseExtraIncomeDetailResp_ChannelItem) XXX_Size() int {
	return xxx_messageInfo_AmuseExtraIncomeDetailResp_ChannelItem.Size(m)
}
func (m *AmuseExtraIncomeDetailResp_ChannelItem) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseExtraIncomeDetailResp_ChannelItem.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseExtraIncomeDetailResp_ChannelItem proto.InternalMessageInfo

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetChannelTag() string {
	if m != nil {
		return m.ChannelTag
	}
	return ""
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetThisMonthFee() uint64 {
	if m != nil {
		return m.ThisMonthFee
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetSettlementRate() string {
	if m != nil {
		return m.SettlementRate
	}
	return ""
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetThisMonthIncome() uint64 {
	if m != nil {
		return m.ThisMonthIncome
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetGuildDisplayId() uint32 {
	if m != nil {
		return m.GuildDisplayId
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetLastMonthFee() uint64 {
	if m != nil {
		return m.LastMonthFee
	}
	return 0
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetGrowRate() string {
	if m != nil {
		return m.GrowRate
	}
	return ""
}

func (m *AmuseExtraIncomeDetailResp_ChannelItem) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type InteractGameIncomeInfo struct {
	AnchorName           string   `protobuf:"bytes,1,opt,name=anchor_name,json=anchorName,proto3" json:"anchor_name"`
	Members              int64    `protobuf:"varint,3,opt,name=members,proto3" json:"members"`
	Fee                  int64    `protobuf:"varint,4,opt,name=fee,proto3" json:"fee"`
	Income               int64    `protobuf:"varint,7,opt,name=income,proto3" json:"income"`
	GameDuration         int64    `protobuf:"varint,8,opt,name=game_duration,json=gameDuration,proto3" json:"game_duration"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InteractGameIncomeInfo) Reset()         { *m = InteractGameIncomeInfo{} }
func (m *InteractGameIncomeInfo) String() string { return proto.CompactTextString(m) }
func (*InteractGameIncomeInfo) ProtoMessage()    {}
func (*InteractGameIncomeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{87}
}
func (m *InteractGameIncomeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InteractGameIncomeInfo.Unmarshal(m, b)
}
func (m *InteractGameIncomeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InteractGameIncomeInfo.Marshal(b, m, deterministic)
}
func (dst *InteractGameIncomeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractGameIncomeInfo.Merge(dst, src)
}
func (m *InteractGameIncomeInfo) XXX_Size() int {
	return xxx_messageInfo_InteractGameIncomeInfo.Size(m)
}
func (m *InteractGameIncomeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractGameIncomeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_InteractGameIncomeInfo proto.InternalMessageInfo

func (m *InteractGameIncomeInfo) GetAnchorName() string {
	if m != nil {
		return m.AnchorName
	}
	return ""
}

func (m *InteractGameIncomeInfo) GetMembers() int64 {
	if m != nil {
		return m.Members
	}
	return 0
}

func (m *InteractGameIncomeInfo) GetFee() int64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *InteractGameIncomeInfo) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *InteractGameIncomeInfo) GetGameDuration() int64 {
	if m != nil {
		return m.GameDuration
	}
	return 0
}

type InteractGameIncomeRsp struct {
	List                 []*InteractGameIncomeInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *InteractGameIncomeRsp) Reset()         { *m = InteractGameIncomeRsp{} }
func (m *InteractGameIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*InteractGameIncomeRsp) ProtoMessage()    {}
func (*InteractGameIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{88}
}
func (m *InteractGameIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InteractGameIncomeRsp.Unmarshal(m, b)
}
func (m *InteractGameIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InteractGameIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *InteractGameIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractGameIncomeRsp.Merge(dst, src)
}
func (m *InteractGameIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_InteractGameIncomeRsp.Size(m)
}
func (m *InteractGameIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractGameIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_InteractGameIncomeRsp proto.InternalMessageInfo

func (m *InteractGameIncomeRsp) GetList() []*InteractGameIncomeInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type InteractGameExtraIncomeReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	MonthTime            uint32   `protobuf:"varint,2,opt,name=month_time,json=monthTime,proto3" json:"month_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InteractGameExtraIncomeReq) Reset()         { *m = InteractGameExtraIncomeReq{} }
func (m *InteractGameExtraIncomeReq) String() string { return proto.CompactTextString(m) }
func (*InteractGameExtraIncomeReq) ProtoMessage()    {}
func (*InteractGameExtraIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{89}
}
func (m *InteractGameExtraIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InteractGameExtraIncomeReq.Unmarshal(m, b)
}
func (m *InteractGameExtraIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InteractGameExtraIncomeReq.Marshal(b, m, deterministic)
}
func (dst *InteractGameExtraIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractGameExtraIncomeReq.Merge(dst, src)
}
func (m *InteractGameExtraIncomeReq) XXX_Size() int {
	return xxx_messageInfo_InteractGameExtraIncomeReq.Size(m)
}
func (m *InteractGameExtraIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractGameExtraIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_InteractGameExtraIncomeReq proto.InternalMessageInfo

func (m *InteractGameExtraIncomeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *InteractGameExtraIncomeReq) GetMonthTime() uint32 {
	if m != nil {
		return m.MonthTime
	}
	return 0
}

type InteractGameExtraIncomeResp struct {
	GameMonthTotalFee    uint64                           `protobuf:"varint,1,opt,name=game_month_total_fee,json=gameMonthTotalFee,proto3" json:"game_month_total_fee"`
	GameMonthExtraIncome uint64                           `protobuf:"varint,2,opt,name=game_month_extra_income,json=gameMonthExtraIncome,proto3" json:"game_month_extra_income"`
	List                 []*InteractGameExtraIncomeDetail `protobuf:"bytes,3,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *InteractGameExtraIncomeResp) Reset()         { *m = InteractGameExtraIncomeResp{} }
func (m *InteractGameExtraIncomeResp) String() string { return proto.CompactTextString(m) }
func (*InteractGameExtraIncomeResp) ProtoMessage()    {}
func (*InteractGameExtraIncomeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{90}
}
func (m *InteractGameExtraIncomeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InteractGameExtraIncomeResp.Unmarshal(m, b)
}
func (m *InteractGameExtraIncomeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InteractGameExtraIncomeResp.Marshal(b, m, deterministic)
}
func (dst *InteractGameExtraIncomeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractGameExtraIncomeResp.Merge(dst, src)
}
func (m *InteractGameExtraIncomeResp) XXX_Size() int {
	return xxx_messageInfo_InteractGameExtraIncomeResp.Size(m)
}
func (m *InteractGameExtraIncomeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractGameExtraIncomeResp.DiscardUnknown(m)
}

var xxx_messageInfo_InteractGameExtraIncomeResp proto.InternalMessageInfo

func (m *InteractGameExtraIncomeResp) GetGameMonthTotalFee() uint64 {
	if m != nil {
		return m.GameMonthTotalFee
	}
	return 0
}

func (m *InteractGameExtraIncomeResp) GetGameMonthExtraIncome() uint64 {
	if m != nil {
		return m.GameMonthExtraIncome
	}
	return 0
}

func (m *InteractGameExtraIncomeResp) GetList() []*InteractGameExtraIncomeDetail {
	if m != nil {
		return m.List
	}
	return nil
}

type InteractGameExtraIncomeDetail struct {
	AnchorUid            uint32   `protobuf:"varint,2,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid"`
	AnchorTtid           string   `protobuf:"bytes,3,opt,name=anchor_ttid,json=anchorTtid,proto3" json:"anchor_ttid"`
	AnchorAccount        string   `protobuf:"bytes,4,opt,name=anchor_account,json=anchorAccount,proto3" json:"anchor_account"`
	AnchorNickname       string   `protobuf:"bytes,5,opt,name=anchor_nickname,json=anchorNickname,proto3" json:"anchor_nickname"`
	AnchorSex            uint32   `protobuf:"varint,6,opt,name=anchor_sex,json=anchorSex,proto3" json:"anchor_sex"`
	GameMonthFee         uint64   `protobuf:"varint,7,opt,name=game_month_fee,json=gameMonthFee,proto3" json:"game_month_fee"`
	GameIncomeRate       float32  `protobuf:"fixed32,8,opt,name=game_income_rate,json=gameIncomeRate,proto3" json:"game_income_rate"`
	GameIncome           uint64   `protobuf:"varint,9,opt,name=game_income,json=gameIncome,proto3" json:"game_income"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InteractGameExtraIncomeDetail) Reset()         { *m = InteractGameExtraIncomeDetail{} }
func (m *InteractGameExtraIncomeDetail) String() string { return proto.CompactTextString(m) }
func (*InteractGameExtraIncomeDetail) ProtoMessage()    {}
func (*InteractGameExtraIncomeDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{91}
}
func (m *InteractGameExtraIncomeDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InteractGameExtraIncomeDetail.Unmarshal(m, b)
}
func (m *InteractGameExtraIncomeDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InteractGameExtraIncomeDetail.Marshal(b, m, deterministic)
}
func (dst *InteractGameExtraIncomeDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InteractGameExtraIncomeDetail.Merge(dst, src)
}
func (m *InteractGameExtraIncomeDetail) XXX_Size() int {
	return xxx_messageInfo_InteractGameExtraIncomeDetail.Size(m)
}
func (m *InteractGameExtraIncomeDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_InteractGameExtraIncomeDetail.DiscardUnknown(m)
}

var xxx_messageInfo_InteractGameExtraIncomeDetail proto.InternalMessageInfo

func (m *InteractGameExtraIncomeDetail) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *InteractGameExtraIncomeDetail) GetAnchorTtid() string {
	if m != nil {
		return m.AnchorTtid
	}
	return ""
}

func (m *InteractGameExtraIncomeDetail) GetAnchorAccount() string {
	if m != nil {
		return m.AnchorAccount
	}
	return ""
}

func (m *InteractGameExtraIncomeDetail) GetAnchorNickname() string {
	if m != nil {
		return m.AnchorNickname
	}
	return ""
}

func (m *InteractGameExtraIncomeDetail) GetAnchorSex() uint32 {
	if m != nil {
		return m.AnchorSex
	}
	return 0
}

func (m *InteractGameExtraIncomeDetail) GetGameMonthFee() uint64 {
	if m != nil {
		return m.GameMonthFee
	}
	return 0
}

func (m *InteractGameExtraIncomeDetail) GetGameIncomeRate() float32 {
	if m != nil {
		return m.GameIncomeRate
	}
	return 0
}

func (m *InteractGameExtraIncomeDetail) GetGameIncome() uint64 {
	if m != nil {
		return m.GameIncome
	}
	return 0
}

type EsportIncomeInfo struct {
	AnchorName           string   `protobuf:"bytes,1,opt,name=anchor_name,json=anchorName,proto3" json:"anchor_name"`
	Fee                  int64    `protobuf:"varint,4,opt,name=fee,proto3" json:"fee"`
	Income               int64    `protobuf:"varint,7,opt,name=income,proto3" json:"income"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportIncomeInfo) Reset()         { *m = EsportIncomeInfo{} }
func (m *EsportIncomeInfo) String() string { return proto.CompactTextString(m) }
func (*EsportIncomeInfo) ProtoMessage()    {}
func (*EsportIncomeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{92}
}
func (m *EsportIncomeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportIncomeInfo.Unmarshal(m, b)
}
func (m *EsportIncomeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportIncomeInfo.Marshal(b, m, deterministic)
}
func (dst *EsportIncomeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportIncomeInfo.Merge(dst, src)
}
func (m *EsportIncomeInfo) XXX_Size() int {
	return xxx_messageInfo_EsportIncomeInfo.Size(m)
}
func (m *EsportIncomeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportIncomeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EsportIncomeInfo proto.InternalMessageInfo

func (m *EsportIncomeInfo) GetAnchorName() string {
	if m != nil {
		return m.AnchorName
	}
	return ""
}

func (m *EsportIncomeInfo) GetFee() int64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *EsportIncomeInfo) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

type EsportIncomeRsp struct {
	List                 []*EsportIncomeInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *EsportIncomeRsp) Reset()         { *m = EsportIncomeRsp{} }
func (m *EsportIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*EsportIncomeRsp) ProtoMessage()    {}
func (*EsportIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{93}
}
func (m *EsportIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportIncomeRsp.Unmarshal(m, b)
}
func (m *EsportIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *EsportIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportIncomeRsp.Merge(dst, src)
}
func (m *EsportIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_EsportIncomeRsp.Size(m)
}
func (m *EsportIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_EsportIncomeRsp proto.InternalMessageInfo

func (m *EsportIncomeRsp) GetList() []*EsportIncomeInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type EsportPendingReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportPendingReq) Reset()         { *m = EsportPendingReq{} }
func (m *EsportPendingReq) String() string { return proto.CompactTextString(m) }
func (*EsportPendingReq) ProtoMessage()    {}
func (*EsportPendingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{94}
}
func (m *EsportPendingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportPendingReq.Unmarshal(m, b)
}
func (m *EsportPendingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportPendingReq.Marshal(b, m, deterministic)
}
func (dst *EsportPendingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportPendingReq.Merge(dst, src)
}
func (m *EsportPendingReq) XXX_Size() int {
	return xxx_messageInfo_EsportPendingReq.Size(m)
}
func (m *EsportPendingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportPendingReq.DiscardUnknown(m)
}

var xxx_messageInfo_EsportPendingReq proto.InternalMessageInfo

func (m *EsportPendingReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type EsportPendingRsp struct {
	ThisMonthFee         int64    `protobuf:"varint,1,opt,name=this_month_fee,json=thisMonthFee,proto3" json:"this_month_fee"`
	ThisMonthIncome      int64    `protobuf:"varint,2,opt,name=this_month_income,json=thisMonthIncome,proto3" json:"this_month_income"`
	StartTime            int64    `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime              int64    `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportPendingRsp) Reset()         { *m = EsportPendingRsp{} }
func (m *EsportPendingRsp) String() string { return proto.CompactTextString(m) }
func (*EsportPendingRsp) ProtoMessage()    {}
func (*EsportPendingRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{95}
}
func (m *EsportPendingRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportPendingRsp.Unmarshal(m, b)
}
func (m *EsportPendingRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportPendingRsp.Marshal(b, m, deterministic)
}
func (dst *EsportPendingRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportPendingRsp.Merge(dst, src)
}
func (m *EsportPendingRsp) XXX_Size() int {
	return xxx_messageInfo_EsportPendingRsp.Size(m)
}
func (m *EsportPendingRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportPendingRsp.DiscardUnknown(m)
}

var xxx_messageInfo_EsportPendingRsp proto.InternalMessageInfo

func (m *EsportPendingRsp) GetThisMonthFee() int64 {
	if m != nil {
		return m.ThisMonthFee
	}
	return 0
}

func (m *EsportPendingRsp) GetThisMonthIncome() int64 {
	if m != nil {
		return m.ThisMonthIncome
	}
	return 0
}

func (m *EsportPendingRsp) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *EsportPendingRsp) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type EsportCoachesMonthIncomeReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page"`
	PageNum              uint32   `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportCoachesMonthIncomeReq) Reset()         { *m = EsportCoachesMonthIncomeReq{} }
func (m *EsportCoachesMonthIncomeReq) String() string { return proto.CompactTextString(m) }
func (*EsportCoachesMonthIncomeReq) ProtoMessage()    {}
func (*EsportCoachesMonthIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{96}
}
func (m *EsportCoachesMonthIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportCoachesMonthIncomeReq.Unmarshal(m, b)
}
func (m *EsportCoachesMonthIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportCoachesMonthIncomeReq.Marshal(b, m, deterministic)
}
func (dst *EsportCoachesMonthIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportCoachesMonthIncomeReq.Merge(dst, src)
}
func (m *EsportCoachesMonthIncomeReq) XXX_Size() int {
	return xxx_messageInfo_EsportCoachesMonthIncomeReq.Size(m)
}
func (m *EsportCoachesMonthIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportCoachesMonthIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_EsportCoachesMonthIncomeReq proto.InternalMessageInfo

func (m *EsportCoachesMonthIncomeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *EsportCoachesMonthIncomeReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *EsportCoachesMonthIncomeReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type EsportCoachesMonthIncomeInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Ttid                 string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname"`
	Fee                  uint64   `protobuf:"varint,4,opt,name=fee,proto3" json:"fee"`
	Income               uint64   `protobuf:"varint,5,opt,name=income,proto3" json:"income"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportCoachesMonthIncomeInfo) Reset()         { *m = EsportCoachesMonthIncomeInfo{} }
func (m *EsportCoachesMonthIncomeInfo) String() string { return proto.CompactTextString(m) }
func (*EsportCoachesMonthIncomeInfo) ProtoMessage()    {}
func (*EsportCoachesMonthIncomeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{97}
}
func (m *EsportCoachesMonthIncomeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportCoachesMonthIncomeInfo.Unmarshal(m, b)
}
func (m *EsportCoachesMonthIncomeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportCoachesMonthIncomeInfo.Marshal(b, m, deterministic)
}
func (dst *EsportCoachesMonthIncomeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportCoachesMonthIncomeInfo.Merge(dst, src)
}
func (m *EsportCoachesMonthIncomeInfo) XXX_Size() int {
	return xxx_messageInfo_EsportCoachesMonthIncomeInfo.Size(m)
}
func (m *EsportCoachesMonthIncomeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportCoachesMonthIncomeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EsportCoachesMonthIncomeInfo proto.InternalMessageInfo

func (m *EsportCoachesMonthIncomeInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *EsportCoachesMonthIncomeInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *EsportCoachesMonthIncomeInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *EsportCoachesMonthIncomeInfo) GetFee() uint64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *EsportCoachesMonthIncomeInfo) GetIncome() uint64 {
	if m != nil {
		return m.Income
	}
	return 0
}

type EsportCoachesMonthIncomeRsp struct {
	List                 []*EsportCoachesMonthIncomeInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *EsportCoachesMonthIncomeRsp) Reset()         { *m = EsportCoachesMonthIncomeRsp{} }
func (m *EsportCoachesMonthIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*EsportCoachesMonthIncomeRsp) ProtoMessage()    {}
func (*EsportCoachesMonthIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{98}
}
func (m *EsportCoachesMonthIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportCoachesMonthIncomeRsp.Unmarshal(m, b)
}
func (m *EsportCoachesMonthIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportCoachesMonthIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *EsportCoachesMonthIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportCoachesMonthIncomeRsp.Merge(dst, src)
}
func (m *EsportCoachesMonthIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_EsportCoachesMonthIncomeRsp.Size(m)
}
func (m *EsportCoachesMonthIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportCoachesMonthIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_EsportCoachesMonthIncomeRsp proto.InternalMessageInfo

func (m *EsportCoachesMonthIncomeRsp) GetList() []*EsportCoachesMonthIncomeInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type EsportConsumeSearchReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	CoachUid             uint32   `protobuf:"varint,3,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid"`
	StartTime            int64    `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime              int64    `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	Page                 uint32   `protobuf:"varint,6,opt,name=page,proto3" json:"page"`
	PageNum              uint32   `protobuf:"varint,7,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportConsumeSearchReq) Reset()         { *m = EsportConsumeSearchReq{} }
func (m *EsportConsumeSearchReq) String() string { return proto.CompactTextString(m) }
func (*EsportConsumeSearchReq) ProtoMessage()    {}
func (*EsportConsumeSearchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{99}
}
func (m *EsportConsumeSearchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportConsumeSearchReq.Unmarshal(m, b)
}
func (m *EsportConsumeSearchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportConsumeSearchReq.Marshal(b, m, deterministic)
}
func (dst *EsportConsumeSearchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportConsumeSearchReq.Merge(dst, src)
}
func (m *EsportConsumeSearchReq) XXX_Size() int {
	return xxx_messageInfo_EsportConsumeSearchReq.Size(m)
}
func (m *EsportConsumeSearchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportConsumeSearchReq.DiscardUnknown(m)
}

var xxx_messageInfo_EsportConsumeSearchReq proto.InternalMessageInfo

func (m *EsportConsumeSearchReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *EsportConsumeSearchReq) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *EsportConsumeSearchReq) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *EsportConsumeSearchReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *EsportConsumeSearchReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *EsportConsumeSearchReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type EsportConsumeDetail struct {
	Date                 int64    `protobuf:"varint,1,opt,name=date,proto3" json:"date"`
	CoachUid             uint32   `protobuf:"varint,2,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid"`
	CoachTtid            string   `protobuf:"bytes,3,opt,name=coach_ttid,json=coachTtid,proto3" json:"coach_ttid"`
	CoachNickname        string   `protobuf:"bytes,4,opt,name=coach_nickname,json=coachNickname,proto3" json:"coach_nickname"`
	Fee                  uint64   `protobuf:"varint,5,opt,name=fee,proto3" json:"fee"`
	Income               uint64   `protobuf:"varint,6,opt,name=income,proto3" json:"income"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportConsumeDetail) Reset()         { *m = EsportConsumeDetail{} }
func (m *EsportConsumeDetail) String() string { return proto.CompactTextString(m) }
func (*EsportConsumeDetail) ProtoMessage()    {}
func (*EsportConsumeDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{100}
}
func (m *EsportConsumeDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportConsumeDetail.Unmarshal(m, b)
}
func (m *EsportConsumeDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportConsumeDetail.Marshal(b, m, deterministic)
}
func (dst *EsportConsumeDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportConsumeDetail.Merge(dst, src)
}
func (m *EsportConsumeDetail) XXX_Size() int {
	return xxx_messageInfo_EsportConsumeDetail.Size(m)
}
func (m *EsportConsumeDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportConsumeDetail.DiscardUnknown(m)
}

var xxx_messageInfo_EsportConsumeDetail proto.InternalMessageInfo

func (m *EsportConsumeDetail) GetDate() int64 {
	if m != nil {
		return m.Date
	}
	return 0
}

func (m *EsportConsumeDetail) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *EsportConsumeDetail) GetCoachTtid() string {
	if m != nil {
		return m.CoachTtid
	}
	return ""
}

func (m *EsportConsumeDetail) GetCoachNickname() string {
	if m != nil {
		return m.CoachNickname
	}
	return ""
}

func (m *EsportConsumeDetail) GetFee() uint64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *EsportConsumeDetail) GetIncome() uint64 {
	if m != nil {
		return m.Income
	}
	return 0
}

type EsportConsumeSearchRsp struct {
	NextPage             bool                   `protobuf:"varint,1,opt,name=next_page,json=nextPage,proto3" json:"next_page"`
	IncomeList           []*EsportConsumeDetail `protobuf:"bytes,2,rep,name=income_list,json=incomeList,proto3" json:"income_list"`
	TotalFee             uint64                 `protobuf:"varint,3,opt,name=total_fee,json=totalFee,proto3" json:"total_fee"`
	TotalIncome          uint64                 `protobuf:"varint,4,opt,name=total_income,json=totalIncome,proto3" json:"total_income"`
	StartTime            int64                  `protobuf:"varint,5,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime              int64                  `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *EsportConsumeSearchRsp) Reset()         { *m = EsportConsumeSearchRsp{} }
func (m *EsportConsumeSearchRsp) String() string { return proto.CompactTextString(m) }
func (*EsportConsumeSearchRsp) ProtoMessage()    {}
func (*EsportConsumeSearchRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{101}
}
func (m *EsportConsumeSearchRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportConsumeSearchRsp.Unmarshal(m, b)
}
func (m *EsportConsumeSearchRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportConsumeSearchRsp.Marshal(b, m, deterministic)
}
func (dst *EsportConsumeSearchRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportConsumeSearchRsp.Merge(dst, src)
}
func (m *EsportConsumeSearchRsp) XXX_Size() int {
	return xxx_messageInfo_EsportConsumeSearchRsp.Size(m)
}
func (m *EsportConsumeSearchRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportConsumeSearchRsp.DiscardUnknown(m)
}

var xxx_messageInfo_EsportConsumeSearchRsp proto.InternalMessageInfo

func (m *EsportConsumeSearchRsp) GetNextPage() bool {
	if m != nil {
		return m.NextPage
	}
	return false
}

func (m *EsportConsumeSearchRsp) GetIncomeList() []*EsportConsumeDetail {
	if m != nil {
		return m.IncomeList
	}
	return nil
}

func (m *EsportConsumeSearchRsp) GetTotalFee() uint64 {
	if m != nil {
		return m.TotalFee
	}
	return 0
}

func (m *EsportConsumeSearchRsp) GetTotalIncome() uint64 {
	if m != nil {
		return m.TotalIncome
	}
	return 0
}

func (m *EsportConsumeSearchRsp) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *EsportConsumeSearchRsp) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

// 获取新增技能审核信息请求
type BatchGetAuditSkillRequest struct {
	GuildId   uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	Ttid      string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid"`
	AuditType []uint32 `protobuf:"varint,3,rep,packed,name=audit_type,json=auditType,proto3" json:"audit_type"`
	OffSet    uint32   `protobuf:"varint,4,opt,name=off_set,json=offSet,proto3" json:"off_set"`
	Limit     uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit"`
	// 查单个详情
	QueryUid             uint32   `protobuf:"varint,6,opt,name=query_uid,json=queryUid,proto3" json:"query_uid"`
	QueryAuditSource     uint32   `protobuf:"varint,7,opt,name=query_audit_source,json=queryAuditSource,proto3" json:"query_audit_source"`
	QueryAuditToken      string   `protobuf:"bytes,8,opt,name=query_audit_token,json=queryAuditToken,proto3" json:"query_audit_token"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetAuditSkillRequest) Reset()         { *m = BatchGetAuditSkillRequest{} }
func (m *BatchGetAuditSkillRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetAuditSkillRequest) ProtoMessage()    {}
func (*BatchGetAuditSkillRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{102}
}
func (m *BatchGetAuditSkillRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAuditSkillRequest.Unmarshal(m, b)
}
func (m *BatchGetAuditSkillRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAuditSkillRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetAuditSkillRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAuditSkillRequest.Merge(dst, src)
}
func (m *BatchGetAuditSkillRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetAuditSkillRequest.Size(m)
}
func (m *BatchGetAuditSkillRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAuditSkillRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAuditSkillRequest proto.InternalMessageInfo

func (m *BatchGetAuditSkillRequest) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *BatchGetAuditSkillRequest) GetAuditType() []uint32 {
	if m != nil {
		return m.AuditType
	}
	return nil
}

func (m *BatchGetAuditSkillRequest) GetOffSet() uint32 {
	if m != nil {
		return m.OffSet
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetQueryUid() uint32 {
	if m != nil {
		return m.QueryUid
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetQueryAuditSource() uint32 {
	if m != nil {
		return m.QueryAuditSource
	}
	return 0
}

func (m *BatchGetAuditSkillRequest) GetQueryAuditToken() string {
	if m != nil {
		return m.QueryAuditToken
	}
	return ""
}

// 获取新增技能审核信息请求
type BatchGetAuditSkillResponse struct {
	GuildId              uint32              `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	Uid                  uint32              `protobuf:"varint,2,opt,name=uid,proto3" json:"uid"`
	AuditType            []uint32            `protobuf:"varint,3,rep,packed,name=audit_type,json=auditType,proto3" json:"audit_type"`
	OffSet               uint32              `protobuf:"varint,4,opt,name=off_set,json=offSet,proto3" json:"off_set"`
	Limit                uint32              `protobuf:"varint,5,opt,name=limit,proto3" json:"limit"`
	List                 []*AuditSkillRecord `protobuf:"bytes,6,rep,name=list,proto3" json:"list"`
	TotalCount           uint32              `protobuf:"varint,7,opt,name=total_count,json=totalCount,proto3" json:"total_count"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatchGetAuditSkillResponse) Reset()         { *m = BatchGetAuditSkillResponse{} }
func (m *BatchGetAuditSkillResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetAuditSkillResponse) ProtoMessage()    {}
func (*BatchGetAuditSkillResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{103}
}
func (m *BatchGetAuditSkillResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAuditSkillResponse.Unmarshal(m, b)
}
func (m *BatchGetAuditSkillResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAuditSkillResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetAuditSkillResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAuditSkillResponse.Merge(dst, src)
}
func (m *BatchGetAuditSkillResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetAuditSkillResponse.Size(m)
}
func (m *BatchGetAuditSkillResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAuditSkillResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAuditSkillResponse proto.InternalMessageInfo

func (m *BatchGetAuditSkillResponse) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetAuditSkillResponse) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchGetAuditSkillResponse) GetAuditType() []uint32 {
	if m != nil {
		return m.AuditType
	}
	return nil
}

func (m *BatchGetAuditSkillResponse) GetOffSet() uint32 {
	if m != nil {
		return m.OffSet
	}
	return 0
}

func (m *BatchGetAuditSkillResponse) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *BatchGetAuditSkillResponse) GetList() []*AuditSkillRecord {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *BatchGetAuditSkillResponse) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type AuditSkillRecord struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Account              string           `protobuf:"bytes,2,opt,name=account,proto3" json:"account"`
	NickName             string           `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name"`
	Ttid                 string           `protobuf:"bytes,4,opt,name=ttid,proto3" json:"ttid"`
	AuditToken           string           `protobuf:"bytes,5,opt,name=audit_token,json=auditToken,proto3" json:"audit_token"`
	AuditType            uint32           `protobuf:"varint,6,opt,name=audit_type,json=auditType,proto3" json:"audit_type"`
	ApplyTime            uint32           `protobuf:"varint,7,opt,name=apply_time,json=applyTime,proto3" json:"apply_time"`
	Skill                []*UserSkillInfo `protobuf:"bytes,8,rep,name=skill,proto3" json:"skill"`
	AuditSource          uint32           `protobuf:"varint,9,opt,name=audit_source,json=auditSource,proto3" json:"audit_source"`
	Reason               string           `protobuf:"bytes,10,opt,name=reason,proto3" json:"reason"`
	GuildId              uint32           `protobuf:"varint,11,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	Sex                  uint32           `protobuf:"varint,12,opt,name=sex,proto3" json:"sex"`
	SignTime             uint32           `protobuf:"varint,13,opt,name=sign_time,json=signTime,proto3" json:"sign_time"`
	SignExpireTime       uint32           `protobuf:"varint,14,opt,name=sign_expire_time,json=signExpireTime,proto3" json:"sign_expire_time"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *AuditSkillRecord) Reset()         { *m = AuditSkillRecord{} }
func (m *AuditSkillRecord) String() string { return proto.CompactTextString(m) }
func (*AuditSkillRecord) ProtoMessage()    {}
func (*AuditSkillRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{104}
}
func (m *AuditSkillRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuditSkillRecord.Unmarshal(m, b)
}
func (m *AuditSkillRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuditSkillRecord.Marshal(b, m, deterministic)
}
func (dst *AuditSkillRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuditSkillRecord.Merge(dst, src)
}
func (m *AuditSkillRecord) XXX_Size() int {
	return xxx_messageInfo_AuditSkillRecord.Size(m)
}
func (m *AuditSkillRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_AuditSkillRecord.DiscardUnknown(m)
}

var xxx_messageInfo_AuditSkillRecord proto.InternalMessageInfo

func (m *AuditSkillRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AuditSkillRecord) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *AuditSkillRecord) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *AuditSkillRecord) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *AuditSkillRecord) GetAuditToken() string {
	if m != nil {
		return m.AuditToken
	}
	return ""
}

func (m *AuditSkillRecord) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

func (m *AuditSkillRecord) GetApplyTime() uint32 {
	if m != nil {
		return m.ApplyTime
	}
	return 0
}

func (m *AuditSkillRecord) GetSkill() []*UserSkillInfo {
	if m != nil {
		return m.Skill
	}
	return nil
}

func (m *AuditSkillRecord) GetAuditSource() uint32 {
	if m != nil {
		return m.AuditSource
	}
	return 0
}

func (m *AuditSkillRecord) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *AuditSkillRecord) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AuditSkillRecord) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *AuditSkillRecord) GetSignTime() uint32 {
	if m != nil {
		return m.SignTime
	}
	return 0
}

func (m *AuditSkillRecord) GetSignExpireTime() uint32 {
	if m != nil {
		return m.SignExpireTime
	}
	return 0
}

// 用户游戏资料信息
type UserSkillInfo struct {
	GameId               uint32         `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	GameName             string         `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name"`
	SkillEvidence        string         `protobuf:"bytes,3,opt,name=skill_evidence,json=skillEvidence,proto3" json:"skill_evidence"`
	Audio                string         `protobuf:"bytes,4,opt,name=audio,proto3" json:"audio"`
	AudioDuration        uint32         `protobuf:"varint,5,opt,name=audio_duration,json=audioDuration,proto3" json:"audio_duration"`
	SectionList          []*SectionInfo `protobuf:"bytes,6,rep,name=section_list,json=sectionList,proto3" json:"section_list"`
	TextDesc             string         `protobuf:"bytes,7,opt,name=text_desc,json=textDesc,proto3" json:"text_desc"`
	GameIcon             string         `protobuf:"bytes,8,opt,name=game_icon,json=gameIcon,proto3" json:"game_icon"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserSkillInfo) Reset()         { *m = UserSkillInfo{} }
func (m *UserSkillInfo) String() string { return proto.CompactTextString(m) }
func (*UserSkillInfo) ProtoMessage()    {}
func (*UserSkillInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{105}
}
func (m *UserSkillInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSkillInfo.Unmarshal(m, b)
}
func (m *UserSkillInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSkillInfo.Marshal(b, m, deterministic)
}
func (dst *UserSkillInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSkillInfo.Merge(dst, src)
}
func (m *UserSkillInfo) XXX_Size() int {
	return xxx_messageInfo_UserSkillInfo.Size(m)
}
func (m *UserSkillInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSkillInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserSkillInfo proto.InternalMessageInfo

func (m *UserSkillInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UserSkillInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *UserSkillInfo) GetSkillEvidence() string {
	if m != nil {
		return m.SkillEvidence
	}
	return ""
}

func (m *UserSkillInfo) GetAudio() string {
	if m != nil {
		return m.Audio
	}
	return ""
}

func (m *UserSkillInfo) GetAudioDuration() uint32 {
	if m != nil {
		return m.AudioDuration
	}
	return 0
}

func (m *UserSkillInfo) GetSectionList() []*SectionInfo {
	if m != nil {
		return m.SectionList
	}
	return nil
}

func (m *UserSkillInfo) GetTextDesc() string {
	if m != nil {
		return m.TextDesc
	}
	return ""
}

func (m *UserSkillInfo) GetGameIcon() string {
	if m != nil {
		return m.GameIcon
	}
	return ""
}

type SectionInfo struct {
	SectionName          string   `protobuf:"bytes,1,opt,name=section_name,json=sectionName,proto3" json:"section_name"`
	ItemList             []string `protobuf:"bytes,2,rep,name=item_list,json=itemList,proto3" json:"item_list"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SectionInfo) Reset()         { *m = SectionInfo{} }
func (m *SectionInfo) String() string { return proto.CompactTextString(m) }
func (*SectionInfo) ProtoMessage()    {}
func (*SectionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{106}
}
func (m *SectionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SectionInfo.Unmarshal(m, b)
}
func (m *SectionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SectionInfo.Marshal(b, m, deterministic)
}
func (dst *SectionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SectionInfo.Merge(dst, src)
}
func (m *SectionInfo) XXX_Size() int {
	return xxx_messageInfo_SectionInfo.Size(m)
}
func (m *SectionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SectionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SectionInfo proto.InternalMessageInfo

func (m *SectionInfo) GetSectionName() string {
	if m != nil {
		return m.SectionName
	}
	return ""
}

func (m *SectionInfo) GetItemList() []string {
	if m != nil {
		return m.ItemList
	}
	return nil
}

// 设置用户技能审核结果
type SetUserSkillAuditTypeRequest struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	AnchorUid            uint32   `protobuf:"varint,2,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid"`
	AuditToken           string   `protobuf:"bytes,3,opt,name=audit_token,json=auditToken,proto3" json:"audit_token"`
	AuditType            uint32   `protobuf:"varint,4,opt,name=audit_type,json=auditType,proto3" json:"audit_type"`
	Reason               string   `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserSkillAuditTypeRequest) Reset()         { *m = SetUserSkillAuditTypeRequest{} }
func (m *SetUserSkillAuditTypeRequest) String() string { return proto.CompactTextString(m) }
func (*SetUserSkillAuditTypeRequest) ProtoMessage()    {}
func (*SetUserSkillAuditTypeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{107}
}
func (m *SetUserSkillAuditTypeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserSkillAuditTypeRequest.Unmarshal(m, b)
}
func (m *SetUserSkillAuditTypeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserSkillAuditTypeRequest.Marshal(b, m, deterministic)
}
func (dst *SetUserSkillAuditTypeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserSkillAuditTypeRequest.Merge(dst, src)
}
func (m *SetUserSkillAuditTypeRequest) XXX_Size() int {
	return xxx_messageInfo_SetUserSkillAuditTypeRequest.Size(m)
}
func (m *SetUserSkillAuditTypeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserSkillAuditTypeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserSkillAuditTypeRequest proto.InternalMessageInfo

func (m *SetUserSkillAuditTypeRequest) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SetUserSkillAuditTypeRequest) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *SetUserSkillAuditTypeRequest) GetAuditToken() string {
	if m != nil {
		return m.AuditToken
	}
	return ""
}

func (m *SetUserSkillAuditTypeRequest) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

func (m *SetUserSkillAuditTypeRequest) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type SetUserSkillAuditTypeResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserSkillAuditTypeResponse) Reset()         { *m = SetUserSkillAuditTypeResponse{} }
func (m *SetUserSkillAuditTypeResponse) String() string { return proto.CompactTextString(m) }
func (*SetUserSkillAuditTypeResponse) ProtoMessage()    {}
func (*SetUserSkillAuditTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{108}
}
func (m *SetUserSkillAuditTypeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserSkillAuditTypeResponse.Unmarshal(m, b)
}
func (m *SetUserSkillAuditTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserSkillAuditTypeResponse.Marshal(b, m, deterministic)
}
func (dst *SetUserSkillAuditTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserSkillAuditTypeResponse.Merge(dst, src)
}
func (m *SetUserSkillAuditTypeResponse) XXX_Size() int {
	return xxx_messageInfo_SetUserSkillAuditTypeResponse.Size(m)
}
func (m *SetUserSkillAuditTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserSkillAuditTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserSkillAuditTypeResponse proto.InternalMessageInfo

type GetGeneralIncomeReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	Guildid              uint32   `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid"`
	PageNum              uint32   `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	PageSize             uint32   `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	BeginTime            int64    `protobuf:"varint,5,opt,name=begin_time,json=beginTime,proto3" json:"begin_time"`
	EndTime              int64    `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGeneralIncomeReq) Reset()         { *m = GetGeneralIncomeReq{} }
func (m *GetGeneralIncomeReq) String() string { return proto.CompactTextString(m) }
func (*GetGeneralIncomeReq) ProtoMessage()    {}
func (*GetGeneralIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{109}
}
func (m *GetGeneralIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGeneralIncomeReq.Unmarshal(m, b)
}
func (m *GetGeneralIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGeneralIncomeReq.Marshal(b, m, deterministic)
}
func (dst *GetGeneralIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGeneralIncomeReq.Merge(dst, src)
}
func (m *GetGeneralIncomeReq) XXX_Size() int {
	return xxx_messageInfo_GetGeneralIncomeReq.Size(m)
}
func (m *GetGeneralIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGeneralIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGeneralIncomeReq proto.InternalMessageInfo

func (m *GetGeneralIncomeReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *GetGeneralIncomeReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *GetGeneralIncomeReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *GetGeneralIncomeReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetGeneralIncomeReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetGeneralIncomeReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~v2的rsp begin
type GuildInitInfoV2Rsp struct {
	GuildName            string   `protobuf:"bytes,1,opt,name=guild_name,json=guildName,proto3" json:"guild_name"`
	Guildid              uint32   `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account"`
	GuildType            uint32   `protobuf:"varint,4,opt,name=guild_type,json=guildType,proto3" json:"guild_type"`
	ServerTime           int64    `protobuf:"varint,5,opt,name=server_time,json=serverTime,proto3" json:"server_time"`
	IsApplication        bool     `protobuf:"varint,6,opt,name=is_application,json=isApplication,proto3" json:"is_application"`
	AudioGuildType       uint32   `protobuf:"varint,7,opt,name=audio_guild_type,json=audioGuildType,proto3" json:"audio_guild_type"`
	MultiGuildType       uint32   `protobuf:"varint,8,opt,name=multi_guild_type,json=multiGuildType,proto3" json:"multi_guild_type"`
	GuildShortId         uint32   `protobuf:"varint,9,opt,name=guild_short_id,json=guildShortId,proto3" json:"guild_short_id"`
	EsportGuildType      uint32   `protobuf:"varint,10,opt,name=esport_guild_type,json=esportGuildType,proto3" json:"esport_guild_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildInitInfoV2Rsp) Reset()         { *m = GuildInitInfoV2Rsp{} }
func (m *GuildInitInfoV2Rsp) String() string { return proto.CompactTextString(m) }
func (*GuildInitInfoV2Rsp) ProtoMessage()    {}
func (*GuildInitInfoV2Rsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{110}
}
func (m *GuildInitInfoV2Rsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildInitInfoV2Rsp.Unmarshal(m, b)
}
func (m *GuildInitInfoV2Rsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildInitInfoV2Rsp.Marshal(b, m, deterministic)
}
func (dst *GuildInitInfoV2Rsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildInitInfoV2Rsp.Merge(dst, src)
}
func (m *GuildInitInfoV2Rsp) XXX_Size() int {
	return xxx_messageInfo_GuildInitInfoV2Rsp.Size(m)
}
func (m *GuildInitInfoV2Rsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildInitInfoV2Rsp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildInitInfoV2Rsp proto.InternalMessageInfo

func (m *GuildInitInfoV2Rsp) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *GuildInitInfoV2Rsp) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *GuildInitInfoV2Rsp) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GuildInitInfoV2Rsp) GetGuildType() uint32 {
	if m != nil {
		return m.GuildType
	}
	return 0
}

func (m *GuildInitInfoV2Rsp) GetServerTime() int64 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *GuildInitInfoV2Rsp) GetIsApplication() bool {
	if m != nil {
		return m.IsApplication
	}
	return false
}

func (m *GuildInitInfoV2Rsp) GetAudioGuildType() uint32 {
	if m != nil {
		return m.AudioGuildType
	}
	return 0
}

func (m *GuildInitInfoV2Rsp) GetMultiGuildType() uint32 {
	if m != nil {
		return m.MultiGuildType
	}
	return 0
}

func (m *GuildInitInfoV2Rsp) GetGuildShortId() uint32 {
	if m != nil {
		return m.GuildShortId
	}
	return 0
}

func (m *GuildInitInfoV2Rsp) GetEsportGuildType() uint32 {
	if m != nil {
		return m.EsportGuildType
	}
	return 0
}

type AmuseTabCheckV2Rsp struct {
	HasAmusement         bool     `protobuf:"varint,1,opt,name=has_amusement,json=hasAmusement,proto3" json:"has_amusement"`
	MonthSixIncome       int64    `protobuf:"varint,2,opt,name=month_six_income,json=monthSixIncome,proto3" json:"month_six_income"`
	ChannelCash          float64  `protobuf:"fixed64,3,opt,name=channel_cash,json=channelCash,proto3" json:"channel_cash"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AmuseTabCheckV2Rsp) Reset()         { *m = AmuseTabCheckV2Rsp{} }
func (m *AmuseTabCheckV2Rsp) String() string { return proto.CompactTextString(m) }
func (*AmuseTabCheckV2Rsp) ProtoMessage()    {}
func (*AmuseTabCheckV2Rsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{111}
}
func (m *AmuseTabCheckV2Rsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseTabCheckV2Rsp.Unmarshal(m, b)
}
func (m *AmuseTabCheckV2Rsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseTabCheckV2Rsp.Marshal(b, m, deterministic)
}
func (dst *AmuseTabCheckV2Rsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseTabCheckV2Rsp.Merge(dst, src)
}
func (m *AmuseTabCheckV2Rsp) XXX_Size() int {
	return xxx_messageInfo_AmuseTabCheckV2Rsp.Size(m)
}
func (m *AmuseTabCheckV2Rsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseTabCheckV2Rsp.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseTabCheckV2Rsp proto.InternalMessageInfo

func (m *AmuseTabCheckV2Rsp) GetHasAmusement() bool {
	if m != nil {
		return m.HasAmusement
	}
	return false
}

func (m *AmuseTabCheckV2Rsp) GetMonthSixIncome() int64 {
	if m != nil {
		return m.MonthSixIncome
	}
	return 0
}

func (m *AmuseTabCheckV2Rsp) GetChannelCash() float64 {
	if m != nil {
		return m.ChannelCash
	}
	return 0
}

type AudioTabCheckV2Rsp struct {
	AudioBasicCash       float64  `protobuf:"fixed64,1,opt,name=audio_basic_cash,json=audioBasicCash,proto3" json:"audio_basic_cash"`
	AudioExtraCash       float64  `protobuf:"fixed64,2,opt,name=audio_extra_cash,json=audioExtraCash,proto3" json:"audio_extra_cash"`
	AudioMonthSixIncome  uint64   `protobuf:"varint,3,opt,name=audio_month_six_income,json=audioMonthSixIncome,proto3" json:"audio_month_six_income"`
	AnchorNum            int32    `protobuf:"varint,4,opt,name=anchor_num,json=anchorNum,proto3" json:"anchor_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AudioTabCheckV2Rsp) Reset()         { *m = AudioTabCheckV2Rsp{} }
func (m *AudioTabCheckV2Rsp) String() string { return proto.CompactTextString(m) }
func (*AudioTabCheckV2Rsp) ProtoMessage()    {}
func (*AudioTabCheckV2Rsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{112}
}
func (m *AudioTabCheckV2Rsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioTabCheckV2Rsp.Unmarshal(m, b)
}
func (m *AudioTabCheckV2Rsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioTabCheckV2Rsp.Marshal(b, m, deterministic)
}
func (dst *AudioTabCheckV2Rsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioTabCheckV2Rsp.Merge(dst, src)
}
func (m *AudioTabCheckV2Rsp) XXX_Size() int {
	return xxx_messageInfo_AudioTabCheckV2Rsp.Size(m)
}
func (m *AudioTabCheckV2Rsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioTabCheckV2Rsp.DiscardUnknown(m)
}

var xxx_messageInfo_AudioTabCheckV2Rsp proto.InternalMessageInfo

func (m *AudioTabCheckV2Rsp) GetAudioBasicCash() float64 {
	if m != nil {
		return m.AudioBasicCash
	}
	return 0
}

func (m *AudioTabCheckV2Rsp) GetAudioExtraCash() float64 {
	if m != nil {
		return m.AudioExtraCash
	}
	return 0
}

func (m *AudioTabCheckV2Rsp) GetAudioMonthSixIncome() uint64 {
	if m != nil {
		return m.AudioMonthSixIncome
	}
	return 0
}

func (m *AudioTabCheckV2Rsp) GetAnchorNum() int32 {
	if m != nil {
		return m.AnchorNum
	}
	return 0
}

type AmuseLastIncomeTrendRsp struct {
	LastDayList          []*DayTrendInfo `protobuf:"bytes,1,rep,name=last_day_list,json=lastDayList,proto3" json:"last_day_list"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AmuseLastIncomeTrendRsp) Reset()         { *m = AmuseLastIncomeTrendRsp{} }
func (m *AmuseLastIncomeTrendRsp) String() string { return proto.CompactTextString(m) }
func (*AmuseLastIncomeTrendRsp) ProtoMessage()    {}
func (*AmuseLastIncomeTrendRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{113}
}
func (m *AmuseLastIncomeTrendRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseLastIncomeTrendRsp.Unmarshal(m, b)
}
func (m *AmuseLastIncomeTrendRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseLastIncomeTrendRsp.Marshal(b, m, deterministic)
}
func (dst *AmuseLastIncomeTrendRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseLastIncomeTrendRsp.Merge(dst, src)
}
func (m *AmuseLastIncomeTrendRsp) XXX_Size() int {
	return xxx_messageInfo_AmuseLastIncomeTrendRsp.Size(m)
}
func (m *AmuseLastIncomeTrendRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseLastIncomeTrendRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseLastIncomeTrendRsp proto.InternalMessageInfo

func (m *AmuseLastIncomeTrendRsp) GetLastDayList() []*DayTrendInfo {
	if m != nil {
		return m.LastDayList
	}
	return nil
}

type AmuseIncomeTrendRsp struct {
	DayList              []*DayTrendInfo `protobuf:"bytes,2,rep,name=day_list,json=dayList,proto3" json:"day_list"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AmuseIncomeTrendRsp) Reset()         { *m = AmuseIncomeTrendRsp{} }
func (m *AmuseIncomeTrendRsp) String() string { return proto.CompactTextString(m) }
func (*AmuseIncomeTrendRsp) ProtoMessage()    {}
func (*AmuseIncomeTrendRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{114}
}
func (m *AmuseIncomeTrendRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseIncomeTrendRsp.Unmarshal(m, b)
}
func (m *AmuseIncomeTrendRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseIncomeTrendRsp.Marshal(b, m, deterministic)
}
func (dst *AmuseIncomeTrendRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseIncomeTrendRsp.Merge(dst, src)
}
func (m *AmuseIncomeTrendRsp) XXX_Size() int {
	return xxx_messageInfo_AmuseIncomeTrendRsp.Size(m)
}
func (m *AmuseIncomeTrendRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseIncomeTrendRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseIncomeTrendRsp proto.InternalMessageInfo

func (m *AmuseIncomeTrendRsp) GetDayList() []*DayTrendInfo {
	if m != nil {
		return m.DayList
	}
	return nil
}

type AmuseIncomeRsp struct {
	TodayIncome          int64    `protobuf:"varint,1,opt,name=today_income,json=todayIncome,proto3" json:"today_income"`
	YesterdayIncome      int64    `protobuf:"varint,2,opt,name=yesterday_income,json=yesterdayIncome,proto3" json:"yesterday_income"`
	ThisMonthIncome      int64    `protobuf:"varint,3,opt,name=this_month_income,json=thisMonthIncome,proto3" json:"this_month_income"`
	LastMonthIncome      int64    `protobuf:"varint,4,opt,name=last_month_income,json=lastMonthIncome,proto3" json:"last_month_income"`
	DayQoq               float32  `protobuf:"fixed32,5,opt,name=day_qoq,json=dayQoq,proto3" json:"day_qoq"`
	MonthQoq             float32  `protobuf:"fixed32,6,opt,name=month_qoq,json=monthQoq,proto3" json:"month_qoq"`
	LastdayQoq           float32  `protobuf:"fixed32,7,opt,name=lastday_qoq,json=lastdayQoq,proto3" json:"lastday_qoq"`
	MonthTonowQoq        float32  `protobuf:"fixed32,8,opt,name=month_tonow_qoq,json=monthTonowQoq,proto3" json:"month_tonow_qoq"`
	LastMonthSamePeriod  int64    `protobuf:"varint,9,opt,name=last_month_same_period,json=lastMonthSamePeriod,proto3" json:"last_month_same_period"`
	MonthSixIncome       int64    `protobuf:"varint,10,opt,name=month_six_income,json=monthSixIncome,proto3" json:"month_six_income"`
	ThisMonthAmuseExtra  int64    `protobuf:"varint,11,opt,name=this_month_amuse_extra,json=thisMonthAmuseExtra,proto3" json:"this_month_amuse_extra"`
	LastMonthAmuseExtra  int64    `protobuf:"varint,12,opt,name=last_month_amuse_extra,json=lastMonthAmuseExtra,proto3" json:"last_month_amuse_extra"`
	NotSettleAmuseExtra  bool     `protobuf:"varint,13,opt,name=not_settle_amuse_extra,json=notSettleAmuseExtra,proto3" json:"not_settle_amuse_extra"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AmuseIncomeRsp) Reset()         { *m = AmuseIncomeRsp{} }
func (m *AmuseIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*AmuseIncomeRsp) ProtoMessage()    {}
func (*AmuseIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{115}
}
func (m *AmuseIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseIncomeRsp.Unmarshal(m, b)
}
func (m *AmuseIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *AmuseIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseIncomeRsp.Merge(dst, src)
}
func (m *AmuseIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_AmuseIncomeRsp.Size(m)
}
func (m *AmuseIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseIncomeRsp proto.InternalMessageInfo

func (m *AmuseIncomeRsp) GetTodayIncome() int64 {
	if m != nil {
		return m.TodayIncome
	}
	return 0
}

func (m *AmuseIncomeRsp) GetYesterdayIncome() int64 {
	if m != nil {
		return m.YesterdayIncome
	}
	return 0
}

func (m *AmuseIncomeRsp) GetThisMonthIncome() int64 {
	if m != nil {
		return m.ThisMonthIncome
	}
	return 0
}

func (m *AmuseIncomeRsp) GetLastMonthIncome() int64 {
	if m != nil {
		return m.LastMonthIncome
	}
	return 0
}

func (m *AmuseIncomeRsp) GetDayQoq() float32 {
	if m != nil {
		return m.DayQoq
	}
	return 0
}

func (m *AmuseIncomeRsp) GetMonthQoq() float32 {
	if m != nil {
		return m.MonthQoq
	}
	return 0
}

func (m *AmuseIncomeRsp) GetLastdayQoq() float32 {
	if m != nil {
		return m.LastdayQoq
	}
	return 0
}

func (m *AmuseIncomeRsp) GetMonthTonowQoq() float32 {
	if m != nil {
		return m.MonthTonowQoq
	}
	return 0
}

func (m *AmuseIncomeRsp) GetLastMonthSamePeriod() int64 {
	if m != nil {
		return m.LastMonthSamePeriod
	}
	return 0
}

func (m *AmuseIncomeRsp) GetMonthSixIncome() int64 {
	if m != nil {
		return m.MonthSixIncome
	}
	return 0
}

func (m *AmuseIncomeRsp) GetThisMonthAmuseExtra() int64 {
	if m != nil {
		return m.ThisMonthAmuseExtra
	}
	return 0
}

func (m *AmuseIncomeRsp) GetLastMonthAmuseExtra() int64 {
	if m != nil {
		return m.LastMonthAmuseExtra
	}
	return 0
}

func (m *AmuseIncomeRsp) GetNotSettleAmuseExtra() bool {
	if m != nil {
		return m.NotSettleAmuseExtra
	}
	return false
}

type AmuseChannelsRsp struct {
	ChannelList          []*GuildChannelInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *AmuseChannelsRsp) Reset()         { *m = AmuseChannelsRsp{} }
func (m *AmuseChannelsRsp) String() string { return proto.CompactTextString(m) }
func (*AmuseChannelsRsp) ProtoMessage()    {}
func (*AmuseChannelsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{116}
}
func (m *AmuseChannelsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseChannelsRsp.Unmarshal(m, b)
}
func (m *AmuseChannelsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseChannelsRsp.Marshal(b, m, deterministic)
}
func (dst *AmuseChannelsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseChannelsRsp.Merge(dst, src)
}
func (m *AmuseChannelsRsp) XXX_Size() int {
	return xxx_messageInfo_AmuseChannelsRsp.Size(m)
}
func (m *AmuseChannelsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseChannelsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseChannelsRsp proto.InternalMessageInfo

func (m *AmuseChannelsRsp) GetChannelList() []*GuildChannelInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type ApplicationInitRsp struct {
	WealthValue          uint32   `protobuf:"varint,1,opt,name=wealth_value,json=wealthValue,proto3" json:"wealth_value"`
	IsApplication        bool     `protobuf:"varint,2,opt,name=is_application,json=isApplication,proto3" json:"is_application"`
	Applied              uint32   `protobuf:"varint,3,opt,name=applied,proto3" json:"applied"`
	AudioApplied         uint32   `protobuf:"varint,4,opt,name=audio_applied,json=audioApplied,proto3" json:"audio_applied"`
	MultiApplied         uint32   `protobuf:"varint,5,opt,name=multi_applied,json=multiApplied,proto3" json:"multi_applied"`
	MultiGuildType       uint32   `protobuf:"varint,6,opt,name=multi_guild_type,json=multiGuildType,proto3" json:"multi_guild_type"`
	AudioGuildType       uint32   `protobuf:"varint,7,opt,name=audio_guild_type,json=audioGuildType,proto3" json:"audio_guild_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplicationInitRsp) Reset()         { *m = ApplicationInitRsp{} }
func (m *ApplicationInitRsp) String() string { return proto.CompactTextString(m) }
func (*ApplicationInitRsp) ProtoMessage()    {}
func (*ApplicationInitRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{117}
}
func (m *ApplicationInitRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationInitRsp.Unmarshal(m, b)
}
func (m *ApplicationInitRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationInitRsp.Marshal(b, m, deterministic)
}
func (dst *ApplicationInitRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationInitRsp.Merge(dst, src)
}
func (m *ApplicationInitRsp) XXX_Size() int {
	return xxx_messageInfo_ApplicationInitRsp.Size(m)
}
func (m *ApplicationInitRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationInitRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationInitRsp proto.InternalMessageInfo

func (m *ApplicationInitRsp) GetWealthValue() uint32 {
	if m != nil {
		return m.WealthValue
	}
	return 0
}

func (m *ApplicationInitRsp) GetIsApplication() bool {
	if m != nil {
		return m.IsApplication
	}
	return false
}

func (m *ApplicationInitRsp) GetApplied() uint32 {
	if m != nil {
		return m.Applied
	}
	return 0
}

func (m *ApplicationInitRsp) GetAudioApplied() uint32 {
	if m != nil {
		return m.AudioApplied
	}
	return 0
}

func (m *ApplicationInitRsp) GetMultiApplied() uint32 {
	if m != nil {
		return m.MultiApplied
	}
	return 0
}

func (m *ApplicationInitRsp) GetMultiGuildType() uint32 {
	if m != nil {
		return m.MultiGuildType
	}
	return 0
}

func (m *ApplicationInitRsp) GetAudioGuildType() uint32 {
	if m != nil {
		return m.AudioGuildType
	}
	return 0
}

type CashInitRsp struct {
	ChannelCash                float64  `protobuf:"fixed64,1,opt,name=channel_cash,json=channelCash,proto3" json:"channel_cash"`
	GameCash                   float64  `protobuf:"fixed64,2,opt,name=game_cash,json=gameCash,proto3" json:"game_cash"`
	LiveCash                   float64  `protobuf:"fixed64,3,opt,name=live_cash,json=liveCash,proto3" json:"live_cash"`
	AudioBasisCash             float64  `protobuf:"fixed64,4,opt,name=audio_basis_cash,json=audioBasisCash,proto3" json:"audio_basis_cash"`
	AudioRewardCash            float64  `protobuf:"fixed64,5,opt,name=audio_reward_cash,json=audioRewardCash,proto3" json:"audio_reward_cash"`
	AmuseThisMonthIncome       uint64   `protobuf:"varint,6,opt,name=amuse_this_month_income,json=amuseThisMonthIncome,proto3" json:"amuse_this_month_income"`
	AudioThisMonthBasisIncome  uint64   `protobuf:"varint,7,opt,name=audio_this_month_basis_income,json=audioThisMonthBasisIncome,proto3" json:"audio_this_month_basis_income"`
	AudioLastMonthRewardIncome uint64   `protobuf:"varint,8,opt,name=audio_last_month_reward_income,json=audioLastMonthRewardIncome,proto3" json:"audio_last_month_reward_income"`
	AmuseExtraCash             float64  `protobuf:"fixed64,9,opt,name=amuse_extra_cash,json=amuseExtraCash,proto3" json:"amuse_extra_cash"`
	ThisMonthAmuseExtra        uint64   `protobuf:"varint,10,opt,name=this_month_amuse_extra,json=thisMonthAmuseExtra,proto3" json:"this_month_amuse_extra"`
	ThisMonthEsportIncome      uint64   `protobuf:"varint,11,opt,name=this_month_esport_income,json=thisMonthEsportIncome,proto3" json:"this_month_esport_income"`
	EsportCash                 float64  `protobuf:"fixed64,12,opt,name=esport_cash,json=esportCash,proto3" json:"esport_cash"`
	XXX_NoUnkeyedLiteral       struct{} `json:"-"`
	XXX_unrecognized           []byte   `json:"-"`
	XXX_sizecache              int32    `json:"-"`
}

func (m *CashInitRsp) Reset()         { *m = CashInitRsp{} }
func (m *CashInitRsp) String() string { return proto.CompactTextString(m) }
func (*CashInitRsp) ProtoMessage()    {}
func (*CashInitRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{118}
}
func (m *CashInitRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CashInitRsp.Unmarshal(m, b)
}
func (m *CashInitRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CashInitRsp.Marshal(b, m, deterministic)
}
func (dst *CashInitRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CashInitRsp.Merge(dst, src)
}
func (m *CashInitRsp) XXX_Size() int {
	return xxx_messageInfo_CashInitRsp.Size(m)
}
func (m *CashInitRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_CashInitRsp.DiscardUnknown(m)
}

var xxx_messageInfo_CashInitRsp proto.InternalMessageInfo

func (m *CashInitRsp) GetChannelCash() float64 {
	if m != nil {
		return m.ChannelCash
	}
	return 0
}

func (m *CashInitRsp) GetGameCash() float64 {
	if m != nil {
		return m.GameCash
	}
	return 0
}

func (m *CashInitRsp) GetLiveCash() float64 {
	if m != nil {
		return m.LiveCash
	}
	return 0
}

func (m *CashInitRsp) GetAudioBasisCash() float64 {
	if m != nil {
		return m.AudioBasisCash
	}
	return 0
}

func (m *CashInitRsp) GetAudioRewardCash() float64 {
	if m != nil {
		return m.AudioRewardCash
	}
	return 0
}

func (m *CashInitRsp) GetAmuseThisMonthIncome() uint64 {
	if m != nil {
		return m.AmuseThisMonthIncome
	}
	return 0
}

func (m *CashInitRsp) GetAudioThisMonthBasisIncome() uint64 {
	if m != nil {
		return m.AudioThisMonthBasisIncome
	}
	return 0
}

func (m *CashInitRsp) GetAudioLastMonthRewardIncome() uint64 {
	if m != nil {
		return m.AudioLastMonthRewardIncome
	}
	return 0
}

func (m *CashInitRsp) GetAmuseExtraCash() float64 {
	if m != nil {
		return m.AmuseExtraCash
	}
	return 0
}

func (m *CashInitRsp) GetThisMonthAmuseExtra() uint64 {
	if m != nil {
		return m.ThisMonthAmuseExtra
	}
	return 0
}

func (m *CashInitRsp) GetThisMonthEsportIncome() uint64 {
	if m != nil {
		return m.ThisMonthEsportIncome
	}
	return 0
}

func (m *CashInitRsp) GetEsportCash() float64 {
	if m != nil {
		return m.EsportCash
	}
	return 0
}

// 新任务列表
type GuildTaskInfoV2 struct {
	Value                uint64   `protobuf:"varint,6,opt,name=value,proto3" json:"value"`
	Ratio                string   `protobuf:"bytes,7,opt,name=ratio,proto3" json:"ratio"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildTaskInfoV2) Reset()         { *m = GuildTaskInfoV2{} }
func (m *GuildTaskInfoV2) String() string { return proto.CompactTextString(m) }
func (*GuildTaskInfoV2) ProtoMessage()    {}
func (*GuildTaskInfoV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{119}
}
func (m *GuildTaskInfoV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildTaskInfoV2.Unmarshal(m, b)
}
func (m *GuildTaskInfoV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildTaskInfoV2.Marshal(b, m, deterministic)
}
func (dst *GuildTaskInfoV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildTaskInfoV2.Merge(dst, src)
}
func (m *GuildTaskInfoV2) XXX_Size() int {
	return xxx_messageInfo_GuildTaskInfoV2.Size(m)
}
func (m *GuildTaskInfoV2) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildTaskInfoV2.DiscardUnknown(m)
}

var xxx_messageInfo_GuildTaskInfoV2 proto.InternalMessageInfo

func (m *GuildTaskInfoV2) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *GuildTaskInfoV2) GetRatio() string {
	if m != nil {
		return m.Ratio
	}
	return ""
}

type GetGuildTaskListV2Rsp struct {
	List                 []*GuildTaskInfoV2 `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	RemainTime           int64              `protobuf:"varint,2,opt,name=remain_time,json=remainTime,proto3" json:"remain_time"`
	BuffTotal            string             `protobuf:"bytes,3,opt,name=buff_total,json=buffTotal,proto3" json:"buff_total"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetGuildTaskListV2Rsp) Reset()         { *m = GetGuildTaskListV2Rsp{} }
func (m *GetGuildTaskListV2Rsp) String() string { return proto.CompactTextString(m) }
func (*GetGuildTaskListV2Rsp) ProtoMessage()    {}
func (*GetGuildTaskListV2Rsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{120}
}
func (m *GetGuildTaskListV2Rsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildTaskListV2Rsp.Unmarshal(m, b)
}
func (m *GetGuildTaskListV2Rsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildTaskListV2Rsp.Marshal(b, m, deterministic)
}
func (dst *GetGuildTaskListV2Rsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildTaskListV2Rsp.Merge(dst, src)
}
func (m *GetGuildTaskListV2Rsp) XXX_Size() int {
	return xxx_messageInfo_GetGuildTaskListV2Rsp.Size(m)
}
func (m *GetGuildTaskListV2Rsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildTaskListV2Rsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildTaskListV2Rsp proto.InternalMessageInfo

func (m *GetGuildTaskListV2Rsp) GetList() []*GuildTaskInfoV2 {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetGuildTaskListV2Rsp) GetRemainTime() int64 {
	if m != nil {
		return m.RemainTime
	}
	return 0
}

func (m *GetGuildTaskListV2Rsp) GetBuffTotal() string {
	if m != nil {
		return m.BuffTotal
	}
	return ""
}

type GetGeneralIncomeV2Rsp struct {
	DateDimenIncome      *GetDateDimenIncomeRsp  `protobuf:"bytes,2,opt,name=date_dimen_income,json=dateDimenIncome,proto3" json:"date_dimen_income"`
	WeekDimenIncome      *GetWeekDimenIncomeRsp  `protobuf:"bytes,3,opt,name=week_dimen_income,json=weekDimenIncome,proto3" json:"week_dimen_income"`
	MonthDimenIncome     *GetMonthDimenIncomeRsp `protobuf:"bytes,4,opt,name=month_dimen_income,json=monthDimenIncome,proto3" json:"month_dimen_income"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetGeneralIncomeV2Rsp) Reset()         { *m = GetGeneralIncomeV2Rsp{} }
func (m *GetGeneralIncomeV2Rsp) String() string { return proto.CompactTextString(m) }
func (*GetGeneralIncomeV2Rsp) ProtoMessage()    {}
func (*GetGeneralIncomeV2Rsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{121}
}
func (m *GetGeneralIncomeV2Rsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGeneralIncomeV2Rsp.Unmarshal(m, b)
}
func (m *GetGeneralIncomeV2Rsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGeneralIncomeV2Rsp.Marshal(b, m, deterministic)
}
func (dst *GetGeneralIncomeV2Rsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGeneralIncomeV2Rsp.Merge(dst, src)
}
func (m *GetGeneralIncomeV2Rsp) XXX_Size() int {
	return xxx_messageInfo_GetGeneralIncomeV2Rsp.Size(m)
}
func (m *GetGeneralIncomeV2Rsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGeneralIncomeV2Rsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGeneralIncomeV2Rsp proto.InternalMessageInfo

func (m *GetGeneralIncomeV2Rsp) GetDateDimenIncome() *GetDateDimenIncomeRsp {
	if m != nil {
		return m.DateDimenIncome
	}
	return nil
}

func (m *GetGeneralIncomeV2Rsp) GetWeekDimenIncome() *GetWeekDimenIncomeRsp {
	if m != nil {
		return m.WeekDimenIncome
	}
	return nil
}

func (m *GetGeneralIncomeV2Rsp) GetMonthDimenIncome() *GetMonthDimenIncomeRsp {
	if m != nil {
		return m.MonthDimenIncome
	}
	return nil
}

type GetGeneralTrendListReq struct {
	Uid                  string    `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	Guildid              uint32    `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid"`
	TypeTime             RangeType `protobuf:"varint,3,opt,name=type_time,json=typeTime,proto3,enum=gold_diamond_logic.RangeType" json:"type_time"`
	BeginTime            int64     `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time"`
	EndTime              int64     `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetGeneralTrendListReq) Reset()         { *m = GetGeneralTrendListReq{} }
func (m *GetGeneralTrendListReq) String() string { return proto.CompactTextString(m) }
func (*GetGeneralTrendListReq) ProtoMessage()    {}
func (*GetGeneralTrendListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{122}
}
func (m *GetGeneralTrendListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGeneralTrendListReq.Unmarshal(m, b)
}
func (m *GetGeneralTrendListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGeneralTrendListReq.Marshal(b, m, deterministic)
}
func (dst *GetGeneralTrendListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGeneralTrendListReq.Merge(dst, src)
}
func (m *GetGeneralTrendListReq) XXX_Size() int {
	return xxx_messageInfo_GetGeneralTrendListReq.Size(m)
}
func (m *GetGeneralTrendListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGeneralTrendListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGeneralTrendListReq proto.InternalMessageInfo

func (m *GetGeneralTrendListReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *GetGeneralTrendListReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *GetGeneralTrendListReq) GetTypeTime() RangeType {
	if m != nil {
		return m.TypeTime
	}
	return RangeType_DAY_RANGE_TYPE
}

func (m *GetGeneralTrendListReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetGeneralTrendListReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetGeneralIncomeRsp struct {
	AnchorInfo              *GetAnchorInfoRsp       `protobuf:"bytes,1,opt,name=anchor_info,json=anchorInfo,proto3" json:"anchor_info"`
	DateDimenIncome         *GetDateDimenIncomeRsp  `protobuf:"bytes,2,opt,name=date_dimen_income,json=dateDimenIncome,proto3" json:"date_dimen_income"`
	WeekDimenIncome         *GetWeekDimenIncomeRsp  `protobuf:"bytes,3,opt,name=week_dimen_income,json=weekDimenIncome,proto3" json:"week_dimen_income"`
	MonthDimenIncome        *GetMonthDimenIncomeRsp `protobuf:"bytes,4,opt,name=month_dimen_income,json=monthDimenIncome,proto3" json:"month_dimen_income"`
	InteractGameExtraIncome *GetMonthDimenIncomeRsp `protobuf:"bytes,5,opt,name=interact_game_extra_income,json=interactGameExtraIncome,proto3" json:"interact_game_extra_income"`
	IsShowInteractGame      bool                    `protobuf:"varint,6,opt,name=is_show_interact_game,json=isShowInteractGame,proto3" json:"is_show_interact_game"`
	XXX_NoUnkeyedLiteral    struct{}                `json:"-"`
	XXX_unrecognized        []byte                  `json:"-"`
	XXX_sizecache           int32                   `json:"-"`
}

func (m *GetGeneralIncomeRsp) Reset()         { *m = GetGeneralIncomeRsp{} }
func (m *GetGeneralIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*GetGeneralIncomeRsp) ProtoMessage()    {}
func (*GetGeneralIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{123}
}
func (m *GetGeneralIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGeneralIncomeRsp.Unmarshal(m, b)
}
func (m *GetGeneralIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGeneralIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *GetGeneralIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGeneralIncomeRsp.Merge(dst, src)
}
func (m *GetGeneralIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_GetGeneralIncomeRsp.Size(m)
}
func (m *GetGeneralIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGeneralIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGeneralIncomeRsp proto.InternalMessageInfo

func (m *GetGeneralIncomeRsp) GetAnchorInfo() *GetAnchorInfoRsp {
	if m != nil {
		return m.AnchorInfo
	}
	return nil
}

func (m *GetGeneralIncomeRsp) GetDateDimenIncome() *GetDateDimenIncomeRsp {
	if m != nil {
		return m.DateDimenIncome
	}
	return nil
}

func (m *GetGeneralIncomeRsp) GetWeekDimenIncome() *GetWeekDimenIncomeRsp {
	if m != nil {
		return m.WeekDimenIncome
	}
	return nil
}

func (m *GetGeneralIncomeRsp) GetMonthDimenIncome() *GetMonthDimenIncomeRsp {
	if m != nil {
		return m.MonthDimenIncome
	}
	return nil
}

func (m *GetGeneralIncomeRsp) GetInteractGameExtraIncome() *GetMonthDimenIncomeRsp {
	if m != nil {
		return m.InteractGameExtraIncome
	}
	return nil
}

func (m *GetGeneralIncomeRsp) GetIsShowInteractGame() bool {
	if m != nil {
		return m.IsShowInteractGame
	}
	return false
}

type GetAnchorInfoRsp struct {
	AnchorNum                uint64   `protobuf:"varint,1,opt,name=anchor_num,json=anchorNum,proto3" json:"anchor_num"`
	ValidAnchorNum           uint64   `protobuf:"varint,2,opt,name=valid_anchor_num,json=validAnchorNum,proto3" json:"valid_anchor_num"`
	ValidAnchorDeadLine      uint64   `protobuf:"varint,3,opt,name=valid_anchor_dead_line,json=validAnchorDeadLine,proto3" json:"valid_anchor_dead_line"`
	MonthValidAnchorNum      uint64   `protobuf:"varint,4,opt,name=month_valid_anchor_num,json=monthValidAnchorNum,proto3" json:"month_valid_anchor_num"`
	MonthValidAnchorDeadLine uint64   `protobuf:"varint,5,opt,name=month_valid_anchor_dead_line,json=monthValidAnchorDeadLine,proto3" json:"month_valid_anchor_dead_line"`
	StarAnchorNum            uint64   `protobuf:"varint,6,opt,name=star_anchor_num,json=starAnchorNum,proto3" json:"star_anchor_num"`
	PotentialAnchorNum       uint64   `protobuf:"varint,8,opt,name=potential_anchor_num,json=potentialAnchorNum,proto3" json:"potential_anchor_num"`
	XXX_NoUnkeyedLiteral     struct{} `json:"-"`
	XXX_unrecognized         []byte   `json:"-"`
	XXX_sizecache            int32    `json:"-"`
}

func (m *GetAnchorInfoRsp) Reset()         { *m = GetAnchorInfoRsp{} }
func (m *GetAnchorInfoRsp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorInfoRsp) ProtoMessage()    {}
func (*GetAnchorInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{124}
}
func (m *GetAnchorInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorInfoRsp.Unmarshal(m, b)
}
func (m *GetAnchorInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorInfoRsp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorInfoRsp.Merge(dst, src)
}
func (m *GetAnchorInfoRsp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorInfoRsp.Size(m)
}
func (m *GetAnchorInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorInfoRsp proto.InternalMessageInfo

func (m *GetAnchorInfoRsp) GetAnchorNum() uint64 {
	if m != nil {
		return m.AnchorNum
	}
	return 0
}

func (m *GetAnchorInfoRsp) GetValidAnchorNum() uint64 {
	if m != nil {
		return m.ValidAnchorNum
	}
	return 0
}

func (m *GetAnchorInfoRsp) GetValidAnchorDeadLine() uint64 {
	if m != nil {
		return m.ValidAnchorDeadLine
	}
	return 0
}

func (m *GetAnchorInfoRsp) GetMonthValidAnchorNum() uint64 {
	if m != nil {
		return m.MonthValidAnchorNum
	}
	return 0
}

func (m *GetAnchorInfoRsp) GetMonthValidAnchorDeadLine() uint64 {
	if m != nil {
		return m.MonthValidAnchorDeadLine
	}
	return 0
}

func (m *GetAnchorInfoRsp) GetStarAnchorNum() uint64 {
	if m != nil {
		return m.StarAnchorNum
	}
	return 0
}

func (m *GetAnchorInfoRsp) GetPotentialAnchorNum() uint64 {
	if m != nil {
		return m.PotentialAnchorNum
	}
	return 0
}

type GetDateDimenIncomeRsp struct {
	TodayIncome          uint64   `protobuf:"varint,1,opt,name=today_income,json=todayIncome,proto3" json:"today_income"`
	YesterdayIncome      uint64   `protobuf:"varint,2,opt,name=yesterday_income,json=yesterdayIncome,proto3" json:"yesterday_income"`
	LastdayQoq           float32  `protobuf:"fixed32,3,opt,name=lastday_qoq,json=lastdayQoq,proto3" json:"lastday_qoq"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDateDimenIncomeRsp) Reset()         { *m = GetDateDimenIncomeRsp{} }
func (m *GetDateDimenIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*GetDateDimenIncomeRsp) ProtoMessage()    {}
func (*GetDateDimenIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{125}
}
func (m *GetDateDimenIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDateDimenIncomeRsp.Unmarshal(m, b)
}
func (m *GetDateDimenIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDateDimenIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *GetDateDimenIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDateDimenIncomeRsp.Merge(dst, src)
}
func (m *GetDateDimenIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_GetDateDimenIncomeRsp.Size(m)
}
func (m *GetDateDimenIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDateDimenIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDateDimenIncomeRsp proto.InternalMessageInfo

func (m *GetDateDimenIncomeRsp) GetTodayIncome() uint64 {
	if m != nil {
		return m.TodayIncome
	}
	return 0
}

func (m *GetDateDimenIncomeRsp) GetYesterdayIncome() uint64 {
	if m != nil {
		return m.YesterdayIncome
	}
	return 0
}

func (m *GetDateDimenIncomeRsp) GetLastdayQoq() float32 {
	if m != nil {
		return m.LastdayQoq
	}
	return 0
}

type GetWeekDimenIncomeRsp struct {
	ThisWeekIncome       uint64   `protobuf:"varint,1,opt,name=this_week_income,json=thisWeekIncome,proto3" json:"this_week_income"`
	LastWeekIncome       uint64   `protobuf:"varint,2,opt,name=last_week_income,json=lastWeekIncome,proto3" json:"last_week_income"`
	CurrentTime          int64    `protobuf:"varint,3,opt,name=current_time,json=currentTime,proto3" json:"current_time"`
	WeekStartTime        int64    `protobuf:"varint,4,opt,name=week_start_time,json=weekStartTime,proto3" json:"week_start_time"`
	WeekEndTime          int64    `protobuf:"varint,5,opt,name=week_end_time,json=weekEndTime,proto3" json:"week_end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeekDimenIncomeRsp) Reset()         { *m = GetWeekDimenIncomeRsp{} }
func (m *GetWeekDimenIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*GetWeekDimenIncomeRsp) ProtoMessage()    {}
func (*GetWeekDimenIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{126}
}
func (m *GetWeekDimenIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeekDimenIncomeRsp.Unmarshal(m, b)
}
func (m *GetWeekDimenIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeekDimenIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *GetWeekDimenIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeekDimenIncomeRsp.Merge(dst, src)
}
func (m *GetWeekDimenIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_GetWeekDimenIncomeRsp.Size(m)
}
func (m *GetWeekDimenIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeekDimenIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeekDimenIncomeRsp proto.InternalMessageInfo

func (m *GetWeekDimenIncomeRsp) GetThisWeekIncome() uint64 {
	if m != nil {
		return m.ThisWeekIncome
	}
	return 0
}

func (m *GetWeekDimenIncomeRsp) GetLastWeekIncome() uint64 {
	if m != nil {
		return m.LastWeekIncome
	}
	return 0
}

func (m *GetWeekDimenIncomeRsp) GetCurrentTime() int64 {
	if m != nil {
		return m.CurrentTime
	}
	return 0
}

func (m *GetWeekDimenIncomeRsp) GetWeekStartTime() int64 {
	if m != nil {
		return m.WeekStartTime
	}
	return 0
}

func (m *GetWeekDimenIncomeRsp) GetWeekEndTime() int64 {
	if m != nil {
		return m.WeekEndTime
	}
	return 0
}

type GetMonthDimenIncomeRsp struct {
	ThisMonthIncome         uint64   `protobuf:"varint,1,opt,name=this_month_income,json=thisMonthIncome,proto3" json:"this_month_income"`
	LastMonthIncome         uint64   `protobuf:"varint,2,opt,name=last_month_income,json=lastMonthIncome,proto3" json:"last_month_income"`
	MonthQoq                float32  `protobuf:"fixed32,3,opt,name=month_qoq,json=monthQoq,proto3" json:"month_qoq"`
	SameLastMonthIncome     uint64   `protobuf:"varint,4,opt,name=same_last_month_income,json=sameLastMonthIncome,proto3" json:"same_last_month_income"`
	LastMonthValidIncomeNum uint64   `protobuf:"varint,5,opt,name=last_month_valid_income_num,json=lastMonthValidIncomeNum,proto3" json:"last_month_valid_income_num"`
	MonthSixIncome          uint64   `protobuf:"varint,6,opt,name=month_six_income,json=monthSixIncome,proto3" json:"month_six_income"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *GetMonthDimenIncomeRsp) Reset()         { *m = GetMonthDimenIncomeRsp{} }
func (m *GetMonthDimenIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*GetMonthDimenIncomeRsp) ProtoMessage()    {}
func (*GetMonthDimenIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{127}
}
func (m *GetMonthDimenIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMonthDimenIncomeRsp.Unmarshal(m, b)
}
func (m *GetMonthDimenIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMonthDimenIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *GetMonthDimenIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMonthDimenIncomeRsp.Merge(dst, src)
}
func (m *GetMonthDimenIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_GetMonthDimenIncomeRsp.Size(m)
}
func (m *GetMonthDimenIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMonthDimenIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMonthDimenIncomeRsp proto.InternalMessageInfo

func (m *GetMonthDimenIncomeRsp) GetThisMonthIncome() uint64 {
	if m != nil {
		return m.ThisMonthIncome
	}
	return 0
}

func (m *GetMonthDimenIncomeRsp) GetLastMonthIncome() uint64 {
	if m != nil {
		return m.LastMonthIncome
	}
	return 0
}

func (m *GetMonthDimenIncomeRsp) GetMonthQoq() float32 {
	if m != nil {
		return m.MonthQoq
	}
	return 0
}

func (m *GetMonthDimenIncomeRsp) GetSameLastMonthIncome() uint64 {
	if m != nil {
		return m.SameLastMonthIncome
	}
	return 0
}

func (m *GetMonthDimenIncomeRsp) GetLastMonthValidIncomeNum() uint64 {
	if m != nil {
		return m.LastMonthValidIncomeNum
	}
	return 0
}

func (m *GetMonthDimenIncomeRsp) GetMonthSixIncome() uint64 {
	if m != nil {
		return m.MonthSixIncome
	}
	return 0
}

type TrendInfo struct {
	Value                uint64   `protobuf:"varint,1,opt,name=value,proto3" json:"value"`
	Key                  string   `protobuf:"bytes,2,opt,name=key,proto3" json:"key"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TrendInfo) Reset()         { *m = TrendInfo{} }
func (m *TrendInfo) String() string { return proto.CompactTextString(m) }
func (*TrendInfo) ProtoMessage()    {}
func (*TrendInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{128}
}
func (m *TrendInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TrendInfo.Unmarshal(m, b)
}
func (m *TrendInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TrendInfo.Marshal(b, m, deterministic)
}
func (dst *TrendInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TrendInfo.Merge(dst, src)
}
func (m *TrendInfo) XXX_Size() int {
	return xxx_messageInfo_TrendInfo.Size(m)
}
func (m *TrendInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TrendInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TrendInfo proto.InternalMessageInfo

func (m *TrendInfo) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *TrendInfo) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type GetIncomeTrendListRsp struct {
	List                 []*TrendInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetIncomeTrendListRsp) Reset()         { *m = GetIncomeTrendListRsp{} }
func (m *GetIncomeTrendListRsp) String() string { return proto.CompactTextString(m) }
func (*GetIncomeTrendListRsp) ProtoMessage()    {}
func (*GetIncomeTrendListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{129}
}
func (m *GetIncomeTrendListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetIncomeTrendListRsp.Unmarshal(m, b)
}
func (m *GetIncomeTrendListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetIncomeTrendListRsp.Marshal(b, m, deterministic)
}
func (dst *GetIncomeTrendListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIncomeTrendListRsp.Merge(dst, src)
}
func (m *GetIncomeTrendListRsp) XXX_Size() int {
	return xxx_messageInfo_GetIncomeTrendListRsp.Size(m)
}
func (m *GetIncomeTrendListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIncomeTrendListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetIncomeTrendListRsp proto.InternalMessageInfo

func (m *GetIncomeTrendListRsp) GetList() []*TrendInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type GetvalidAnchorTrendListRsp struct {
	List                 []*TrendInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetvalidAnchorTrendListRsp) Reset()         { *m = GetvalidAnchorTrendListRsp{} }
func (m *GetvalidAnchorTrendListRsp) String() string { return proto.CompactTextString(m) }
func (*GetvalidAnchorTrendListRsp) ProtoMessage()    {}
func (*GetvalidAnchorTrendListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{130}
}
func (m *GetvalidAnchorTrendListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetvalidAnchorTrendListRsp.Unmarshal(m, b)
}
func (m *GetvalidAnchorTrendListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetvalidAnchorTrendListRsp.Marshal(b, m, deterministic)
}
func (dst *GetvalidAnchorTrendListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetvalidAnchorTrendListRsp.Merge(dst, src)
}
func (m *GetvalidAnchorTrendListRsp) XXX_Size() int {
	return xxx_messageInfo_GetvalidAnchorTrendListRsp.Size(m)
}
func (m *GetvalidAnchorTrendListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetvalidAnchorTrendListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetvalidAnchorTrendListRsp proto.InternalMessageInfo

func (m *GetvalidAnchorTrendListRsp) GetList() []*TrendInfo {
	if m != nil {
		return m.List
	}
	return nil
}

// 额外收益+当月公会任务奖励分成比例 的记录
// 额外收益记录
type ExtraIncome struct {
	MonthRecordIncome    uint64   `protobuf:"varint,1,opt,name=month_record_income,json=monthRecordIncome,proto3" json:"month_record_income"`
	ExtraIncomeRatio     uint32   `protobuf:"varint,2,opt,name=extra_income_ratio,json=extraIncomeRatio,proto3" json:"extra_income_ratio"`
	ExtraIncomeRatioV2   string   `protobuf:"bytes,3,opt,name=extra_income_ratio_v2,json=extraIncomeRatioV2,proto3" json:"extra_income_ratio_v2"`
	Key                  string   `protobuf:"bytes,4,opt,name=key,proto3" json:"key"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExtraIncome) Reset()         { *m = ExtraIncome{} }
func (m *ExtraIncome) String() string { return proto.CompactTextString(m) }
func (*ExtraIncome) ProtoMessage()    {}
func (*ExtraIncome) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{131}
}
func (m *ExtraIncome) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExtraIncome.Unmarshal(m, b)
}
func (m *ExtraIncome) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExtraIncome.Marshal(b, m, deterministic)
}
func (dst *ExtraIncome) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExtraIncome.Merge(dst, src)
}
func (m *ExtraIncome) XXX_Size() int {
	return xxx_messageInfo_ExtraIncome.Size(m)
}
func (m *ExtraIncome) XXX_DiscardUnknown() {
	xxx_messageInfo_ExtraIncome.DiscardUnknown(m)
}

var xxx_messageInfo_ExtraIncome proto.InternalMessageInfo

func (m *ExtraIncome) GetMonthRecordIncome() uint64 {
	if m != nil {
		return m.MonthRecordIncome
	}
	return 0
}

func (m *ExtraIncome) GetExtraIncomeRatio() uint32 {
	if m != nil {
		return m.ExtraIncomeRatio
	}
	return 0
}

func (m *ExtraIncome) GetExtraIncomeRatioV2() string {
	if m != nil {
		return m.ExtraIncomeRatioV2
	}
	return ""
}

func (m *ExtraIncome) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type GetGuildExtraIncomeRsp struct {
	List                 []*ExtraIncome `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetGuildExtraIncomeRsp) Reset()         { *m = GetGuildExtraIncomeRsp{} }
func (m *GetGuildExtraIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*GetGuildExtraIncomeRsp) ProtoMessage()    {}
func (*GetGuildExtraIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{132}
}
func (m *GetGuildExtraIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildExtraIncomeRsp.Unmarshal(m, b)
}
func (m *GetGuildExtraIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildExtraIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *GetGuildExtraIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildExtraIncomeRsp.Merge(dst, src)
}
func (m *GetGuildExtraIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_GetGuildExtraIncomeRsp.Size(m)
}
func (m *GetGuildExtraIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildExtraIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildExtraIncomeRsp proto.InternalMessageInfo

func (m *GetGuildExtraIncomeRsp) GetList() []*ExtraIncome {
	if m != nil {
		return m.List
	}
	return nil
}

type GuildTaskInfo struct {
	Status               uint32   `protobuf:"varint,1,opt,name=status,proto3" json:"status"`
	Finish               uint32   `protobuf:"varint,4,opt,name=finish,proto3" json:"finish"`
	Current              uint32   `protobuf:"varint,5,opt,name=current,proto3" json:"current"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildTaskInfo) Reset()         { *m = GuildTaskInfo{} }
func (m *GuildTaskInfo) String() string { return proto.CompactTextString(m) }
func (*GuildTaskInfo) ProtoMessage()    {}
func (*GuildTaskInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{133}
}
func (m *GuildTaskInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildTaskInfo.Unmarshal(m, b)
}
func (m *GuildTaskInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildTaskInfo.Marshal(b, m, deterministic)
}
func (dst *GuildTaskInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildTaskInfo.Merge(dst, src)
}
func (m *GuildTaskInfo) XXX_Size() int {
	return xxx_messageInfo_GuildTaskInfo.Size(m)
}
func (m *GuildTaskInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildTaskInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildTaskInfo proto.InternalMessageInfo

func (m *GuildTaskInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GuildTaskInfo) GetFinish() uint32 {
	if m != nil {
		return m.Finish
	}
	return 0
}

func (m *GuildTaskInfo) GetCurrent() uint32 {
	if m != nil {
		return m.Current
	}
	return 0
}

type GetGuildTaskListRsp struct {
	List                 []*GuildTaskInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	RemainTime           int64            `protobuf:"varint,2,opt,name=remain_time,json=remainTime,proto3" json:"remain_time"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetGuildTaskListRsp) Reset()         { *m = GetGuildTaskListRsp{} }
func (m *GetGuildTaskListRsp) String() string { return proto.CompactTextString(m) }
func (*GetGuildTaskListRsp) ProtoMessage()    {}
func (*GetGuildTaskListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{134}
}
func (m *GetGuildTaskListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildTaskListRsp.Unmarshal(m, b)
}
func (m *GetGuildTaskListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildTaskListRsp.Marshal(b, m, deterministic)
}
func (dst *GetGuildTaskListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildTaskListRsp.Merge(dst, src)
}
func (m *GetGuildTaskListRsp) XXX_Size() int {
	return xxx_messageInfo_GetGuildTaskListRsp.Size(m)
}
func (m *GetGuildTaskListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildTaskListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildTaskListRsp proto.InternalMessageInfo

func (m *GetGuildTaskListRsp) GetList() []*GuildTaskInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetGuildTaskListRsp) GetRemainTime() int64 {
	if m != nil {
		return m.RemainTime
	}
	return 0
}

type AnchorInfo struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname"`
	Consume              uint64   `protobuf:"varint,3,opt,name=consume,proto3" json:"consume"`
	MonthToNowQoq        float32  `protobuf:"fixed32,4,opt,name=month_to_now_qoq,json=monthToNowQoq,proto3" json:"month_to_now_qoq"`
	Uid                  uint32   `protobuf:"varint,5,opt,name=uid,proto3" json:"uid"`
	Alias                string   `protobuf:"bytes,6,opt,name=alias,proto3" json:"alias"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorInfo) Reset()         { *m = AnchorInfo{} }
func (m *AnchorInfo) String() string { return proto.CompactTextString(m) }
func (*AnchorInfo) ProtoMessage()    {}
func (*AnchorInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{135}
}
func (m *AnchorInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorInfo.Unmarshal(m, b)
}
func (m *AnchorInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorInfo.Marshal(b, m, deterministic)
}
func (dst *AnchorInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorInfo.Merge(dst, src)
}
func (m *AnchorInfo) XXX_Size() int {
	return xxx_messageInfo_AnchorInfo.Size(m)
}
func (m *AnchorInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorInfo proto.InternalMessageInfo

func (m *AnchorInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *AnchorInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *AnchorInfo) GetConsume() uint64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *AnchorInfo) GetMonthToNowQoq() float32 {
	if m != nil {
		return m.MonthToNowQoq
	}
	return 0
}

func (m *AnchorInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AnchorInfo) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

type GetAnchorListRsp struct {
	List                 []*AnchorInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetAnchorListRsp) Reset()         { *m = GetAnchorListRsp{} }
func (m *GetAnchorListRsp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorListRsp) ProtoMessage()    {}
func (*GetAnchorListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{136}
}
func (m *GetAnchorListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorListRsp.Unmarshal(m, b)
}
func (m *GetAnchorListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorListRsp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorListRsp.Merge(dst, src)
}
func (m *GetAnchorListRsp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorListRsp.Size(m)
}
func (m *GetAnchorListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorListRsp proto.InternalMessageInfo

func (m *GetAnchorListRsp) GetList() []*AnchorInfo {
	if m != nil {
		return m.List
	}
	return nil
}

// 获取会长服务号语音直播数据详情
type GetYuyinIncomeDetailReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	Guildid              uint32   `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetYuyinIncomeDetailReq) Reset()         { *m = GetYuyinIncomeDetailReq{} }
func (m *GetYuyinIncomeDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetYuyinIncomeDetailReq) ProtoMessage()    {}
func (*GetYuyinIncomeDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{137}
}
func (m *GetYuyinIncomeDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinIncomeDetailReq.Unmarshal(m, b)
}
func (m *GetYuyinIncomeDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinIncomeDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetYuyinIncomeDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinIncomeDetailReq.Merge(dst, src)
}
func (m *GetYuyinIncomeDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetYuyinIncomeDetailReq.Size(m)
}
func (m *GetYuyinIncomeDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinIncomeDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinIncomeDetailReq proto.InternalMessageInfo

func (m *GetYuyinIncomeDetailReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *GetYuyinIncomeDetailReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type GetYuyinIncomeDetailRsp struct {
	DateDimenIncome      *GetDateDimenIncomeRsp  `protobuf:"bytes,2,opt,name=date_dimen_income,json=dateDimenIncome,proto3" json:"date_dimen_income"`
	WeekDimenIncome      *GetWeekDimenIncomeRsp  `protobuf:"bytes,3,opt,name=week_dimen_income,json=weekDimenIncome,proto3" json:"week_dimen_income"`
	MonthDimenIncome     *GetMonthDimenIncomeRsp `protobuf:"bytes,4,opt,name=month_dimen_income,json=monthDimenIncome,proto3" json:"month_dimen_income"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetYuyinIncomeDetailRsp) Reset()         { *m = GetYuyinIncomeDetailRsp{} }
func (m *GetYuyinIncomeDetailRsp) String() string { return proto.CompactTextString(m) }
func (*GetYuyinIncomeDetailRsp) ProtoMessage()    {}
func (*GetYuyinIncomeDetailRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{138}
}
func (m *GetYuyinIncomeDetailRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinIncomeDetailRsp.Unmarshal(m, b)
}
func (m *GetYuyinIncomeDetailRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinIncomeDetailRsp.Marshal(b, m, deterministic)
}
func (dst *GetYuyinIncomeDetailRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinIncomeDetailRsp.Merge(dst, src)
}
func (m *GetYuyinIncomeDetailRsp) XXX_Size() int {
	return xxx_messageInfo_GetYuyinIncomeDetailRsp.Size(m)
}
func (m *GetYuyinIncomeDetailRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinIncomeDetailRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinIncomeDetailRsp proto.InternalMessageInfo

func (m *GetYuyinIncomeDetailRsp) GetDateDimenIncome() *GetDateDimenIncomeRsp {
	if m != nil {
		return m.DateDimenIncome
	}
	return nil
}

func (m *GetYuyinIncomeDetailRsp) GetWeekDimenIncome() *GetWeekDimenIncomeRsp {
	if m != nil {
		return m.WeekDimenIncome
	}
	return nil
}

func (m *GetYuyinIncomeDetailRsp) GetMonthDimenIncome() *GetMonthDimenIncomeRsp {
	if m != nil {
		return m.MonthDimenIncome
	}
	return nil
}

// DailyIncome 收益日增长信息
// 因为兼容问题暂时不使用
type DailyIncome struct {
	TodayIncome          uint64   `protobuf:"varint,1,opt,name=today_income,json=todayIncome,proto3" json:"today_income"`
	YesterdayIncome      uint64   `protobuf:"varint,2,opt,name=yesterday_income,json=yesterdayIncome,proto3" json:"yesterday_income"`
	DailyQoq             float32  `protobuf:"fixed32,3,opt,name=daily_qoq,json=dailyQoq,proto3" json:"daily_qoq"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DailyIncome) Reset()         { *m = DailyIncome{} }
func (m *DailyIncome) String() string { return proto.CompactTextString(m) }
func (*DailyIncome) ProtoMessage()    {}
func (*DailyIncome) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{139}
}
func (m *DailyIncome) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DailyIncome.Unmarshal(m, b)
}
func (m *DailyIncome) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DailyIncome.Marshal(b, m, deterministic)
}
func (dst *DailyIncome) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DailyIncome.Merge(dst, src)
}
func (m *DailyIncome) XXX_Size() int {
	return xxx_messageInfo_DailyIncome.Size(m)
}
func (m *DailyIncome) XXX_DiscardUnknown() {
	xxx_messageInfo_DailyIncome.DiscardUnknown(m)
}

var xxx_messageInfo_DailyIncome proto.InternalMessageInfo

func (m *DailyIncome) GetTodayIncome() uint64 {
	if m != nil {
		return m.TodayIncome
	}
	return 0
}

func (m *DailyIncome) GetYesterdayIncome() uint64 {
	if m != nil {
		return m.YesterdayIncome
	}
	return 0
}

func (m *DailyIncome) GetDailyQoq() float32 {
	if m != nil {
		return m.DailyQoq
	}
	return 0
}

// WeeklyIncome 收益周增长信息
// 因为兼容问题暂时不使用
type WeeklyIncome struct {
	ThisWeekIncome       uint64   `protobuf:"varint,1,opt,name=this_week_income,json=thisWeekIncome,proto3" json:"this_week_income"`
	LastWeekIncome       uint64   `protobuf:"varint,2,opt,name=last_week_income,json=lastWeekIncome,proto3" json:"last_week_income"`
	CurrentTime          int64    `protobuf:"varint,3,opt,name=current_time,json=currentTime,proto3" json:"current_time"`
	WeekStartTime        int64    `protobuf:"varint,4,opt,name=week_start_time,json=weekStartTime,proto3" json:"week_start_time"`
	WeekEndTime          int64    `protobuf:"varint,5,opt,name=week_end_time,json=weekEndTime,proto3" json:"week_end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeeklyIncome) Reset()         { *m = WeeklyIncome{} }
func (m *WeeklyIncome) String() string { return proto.CompactTextString(m) }
func (*WeeklyIncome) ProtoMessage()    {}
func (*WeeklyIncome) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{140}
}
func (m *WeeklyIncome) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeeklyIncome.Unmarshal(m, b)
}
func (m *WeeklyIncome) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeeklyIncome.Marshal(b, m, deterministic)
}
func (dst *WeeklyIncome) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeeklyIncome.Merge(dst, src)
}
func (m *WeeklyIncome) XXX_Size() int {
	return xxx_messageInfo_WeeklyIncome.Size(m)
}
func (m *WeeklyIncome) XXX_DiscardUnknown() {
	xxx_messageInfo_WeeklyIncome.DiscardUnknown(m)
}

var xxx_messageInfo_WeeklyIncome proto.InternalMessageInfo

func (m *WeeklyIncome) GetThisWeekIncome() uint64 {
	if m != nil {
		return m.ThisWeekIncome
	}
	return 0
}

func (m *WeeklyIncome) GetLastWeekIncome() uint64 {
	if m != nil {
		return m.LastWeekIncome
	}
	return 0
}

func (m *WeeklyIncome) GetCurrentTime() int64 {
	if m != nil {
		return m.CurrentTime
	}
	return 0
}

func (m *WeeklyIncome) GetWeekStartTime() int64 {
	if m != nil {
		return m.WeekStartTime
	}
	return 0
}

func (m *WeeklyIncome) GetWeekEndTime() int64 {
	if m != nil {
		return m.WeekEndTime
	}
	return 0
}

// MonthlyIncome 收益月增长信息
// 因为兼容问题暂时不使用
type MonthlyIncome struct {
	ThisMonthIncome      uint64   `protobuf:"varint,1,opt,name=this_month_income,json=thisMonthIncome,proto3" json:"this_month_income"`
	LastMonthIncome      uint64   `protobuf:"varint,2,opt,name=last_month_income,json=lastMonthIncome,proto3" json:"last_month_income"`
	MonthlyQoq           float32  `protobuf:"fixed32,3,opt,name=monthly_qoq,json=monthlyQoq,proto3" json:"monthly_qoq"`
	SameLastMonthIncome  uint64   `protobuf:"varint,4,opt,name=same_last_month_income,json=sameLastMonthIncome,proto3" json:"same_last_month_income"`
	LastMonthValidIncome uint64   `protobuf:"varint,5,opt,name=last_month_valid_income,json=lastMonthValidIncome,proto3" json:"last_month_valid_income"`
	LastSixMonthIncome   uint64   `protobuf:"varint,6,opt,name=last_six_month_income,json=lastSixMonthIncome,proto3" json:"last_six_month_income"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MonthlyIncome) Reset()         { *m = MonthlyIncome{} }
func (m *MonthlyIncome) String() string { return proto.CompactTextString(m) }
func (*MonthlyIncome) ProtoMessage()    {}
func (*MonthlyIncome) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{141}
}
func (m *MonthlyIncome) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MonthlyIncome.Unmarshal(m, b)
}
func (m *MonthlyIncome) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MonthlyIncome.Marshal(b, m, deterministic)
}
func (dst *MonthlyIncome) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MonthlyIncome.Merge(dst, src)
}
func (m *MonthlyIncome) XXX_Size() int {
	return xxx_messageInfo_MonthlyIncome.Size(m)
}
func (m *MonthlyIncome) XXX_DiscardUnknown() {
	xxx_messageInfo_MonthlyIncome.DiscardUnknown(m)
}

var xxx_messageInfo_MonthlyIncome proto.InternalMessageInfo

func (m *MonthlyIncome) GetThisMonthIncome() uint64 {
	if m != nil {
		return m.ThisMonthIncome
	}
	return 0
}

func (m *MonthlyIncome) GetLastMonthIncome() uint64 {
	if m != nil {
		return m.LastMonthIncome
	}
	return 0
}

func (m *MonthlyIncome) GetMonthlyQoq() float32 {
	if m != nil {
		return m.MonthlyQoq
	}
	return 0
}

func (m *MonthlyIncome) GetSameLastMonthIncome() uint64 {
	if m != nil {
		return m.SameLastMonthIncome
	}
	return 0
}

func (m *MonthlyIncome) GetLastMonthValidIncome() uint64 {
	if m != nil {
		return m.LastMonthValidIncome
	}
	return 0
}

func (m *MonthlyIncome) GetLastSixMonthIncome() uint64 {
	if m != nil {
		return m.LastSixMonthIncome
	}
	return 0
}

// 获取当月日收益趋势
type GetYuyinThisMonthTrendListReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	Guildid              uint32   `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetYuyinThisMonthTrendListReq) Reset()         { *m = GetYuyinThisMonthTrendListReq{} }
func (m *GetYuyinThisMonthTrendListReq) String() string { return proto.CompactTextString(m) }
func (*GetYuyinThisMonthTrendListReq) ProtoMessage()    {}
func (*GetYuyinThisMonthTrendListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{142}
}
func (m *GetYuyinThisMonthTrendListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinThisMonthTrendListReq.Unmarshal(m, b)
}
func (m *GetYuyinThisMonthTrendListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinThisMonthTrendListReq.Marshal(b, m, deterministic)
}
func (dst *GetYuyinThisMonthTrendListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinThisMonthTrendListReq.Merge(dst, src)
}
func (m *GetYuyinThisMonthTrendListReq) XXX_Size() int {
	return xxx_messageInfo_GetYuyinThisMonthTrendListReq.Size(m)
}
func (m *GetYuyinThisMonthTrendListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinThisMonthTrendListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinThisMonthTrendListReq proto.InternalMessageInfo

func (m *GetYuyinThisMonthTrendListReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *GetYuyinThisMonthTrendListReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type GetYuyinThisMonthTrendListRsp struct {
	List                 []*TrendInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetYuyinThisMonthTrendListRsp) Reset()         { *m = GetYuyinThisMonthTrendListRsp{} }
func (m *GetYuyinThisMonthTrendListRsp) String() string { return proto.CompactTextString(m) }
func (*GetYuyinThisMonthTrendListRsp) ProtoMessage()    {}
func (*GetYuyinThisMonthTrendListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{143}
}
func (m *GetYuyinThisMonthTrendListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinThisMonthTrendListRsp.Unmarshal(m, b)
}
func (m *GetYuyinThisMonthTrendListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinThisMonthTrendListRsp.Marshal(b, m, deterministic)
}
func (dst *GetYuyinThisMonthTrendListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinThisMonthTrendListRsp.Merge(dst, src)
}
func (m *GetYuyinThisMonthTrendListRsp) XXX_Size() int {
	return xxx_messageInfo_GetYuyinThisMonthTrendListRsp.Size(m)
}
func (m *GetYuyinThisMonthTrendListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinThisMonthTrendListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinThisMonthTrendListRsp proto.InternalMessageInfo

func (m *GetYuyinThisMonthTrendListRsp) GetList() []*TrendInfo {
	if m != nil {
		return m.List
	}
	return nil
}

// 获取上月日收益趋势
type GetYuyinLastMonthTrendListReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	Guildid              uint32   `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetYuyinLastMonthTrendListReq) Reset()         { *m = GetYuyinLastMonthTrendListReq{} }
func (m *GetYuyinLastMonthTrendListReq) String() string { return proto.CompactTextString(m) }
func (*GetYuyinLastMonthTrendListReq) ProtoMessage()    {}
func (*GetYuyinLastMonthTrendListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{144}
}
func (m *GetYuyinLastMonthTrendListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinLastMonthTrendListReq.Unmarshal(m, b)
}
func (m *GetYuyinLastMonthTrendListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinLastMonthTrendListReq.Marshal(b, m, deterministic)
}
func (dst *GetYuyinLastMonthTrendListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinLastMonthTrendListReq.Merge(dst, src)
}
func (m *GetYuyinLastMonthTrendListReq) XXX_Size() int {
	return xxx_messageInfo_GetYuyinLastMonthTrendListReq.Size(m)
}
func (m *GetYuyinLastMonthTrendListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinLastMonthTrendListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinLastMonthTrendListReq proto.InternalMessageInfo

func (m *GetYuyinLastMonthTrendListReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *GetYuyinLastMonthTrendListReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type GetYuyinLastMonthTrendListRsp struct {
	List                 []*TrendInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetYuyinLastMonthTrendListRsp) Reset()         { *m = GetYuyinLastMonthTrendListRsp{} }
func (m *GetYuyinLastMonthTrendListRsp) String() string { return proto.CompactTextString(m) }
func (*GetYuyinLastMonthTrendListRsp) ProtoMessage()    {}
func (*GetYuyinLastMonthTrendListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{145}
}
func (m *GetYuyinLastMonthTrendListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinLastMonthTrendListRsp.Unmarshal(m, b)
}
func (m *GetYuyinLastMonthTrendListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinLastMonthTrendListRsp.Marshal(b, m, deterministic)
}
func (dst *GetYuyinLastMonthTrendListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinLastMonthTrendListRsp.Merge(dst, src)
}
func (m *GetYuyinLastMonthTrendListRsp) XXX_Size() int {
	return xxx_messageInfo_GetYuyinLastMonthTrendListRsp.Size(m)
}
func (m *GetYuyinLastMonthTrendListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinLastMonthTrendListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinLastMonthTrendListRsp proto.InternalMessageInfo

func (m *GetYuyinLastMonthTrendListRsp) GetList() []*TrendInfo {
	if m != nil {
		return m.List
	}
	return nil
}

// 获取语音消费排行榜
type GetConsumeRankReq struct {
	ConsumeType          RangeType `protobuf:"varint,1,opt,name=consume_type,json=consumeType,proto3,enum=gold_diamond_logic.RangeType" json:"consume_type"`
	Guildid              uint32    `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetConsumeRankReq) Reset()         { *m = GetConsumeRankReq{} }
func (m *GetConsumeRankReq) String() string { return proto.CompactTextString(m) }
func (*GetConsumeRankReq) ProtoMessage()    {}
func (*GetConsumeRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{146}
}
func (m *GetConsumeRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConsumeRankReq.Unmarshal(m, b)
}
func (m *GetConsumeRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConsumeRankReq.Marshal(b, m, deterministic)
}
func (dst *GetConsumeRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConsumeRankReq.Merge(dst, src)
}
func (m *GetConsumeRankReq) XXX_Size() int {
	return xxx_messageInfo_GetConsumeRankReq.Size(m)
}
func (m *GetConsumeRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConsumeRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConsumeRankReq proto.InternalMessageInfo

func (m *GetConsumeRankReq) GetConsumeType() RangeType {
	if m != nil {
		return m.ConsumeType
	}
	return RangeType_DAY_RANGE_TYPE
}

func (m *GetConsumeRankReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type GetConsumeRankRsp struct {
	ConsumeRankList      []*ConsumeRankItem `protobuf:"bytes,1,rep,name=consume_rank_list,json=consumeRankList,proto3" json:"consume_rank_list"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetConsumeRankRsp) Reset()         { *m = GetConsumeRankRsp{} }
func (m *GetConsumeRankRsp) String() string { return proto.CompactTextString(m) }
func (*GetConsumeRankRsp) ProtoMessage()    {}
func (*GetConsumeRankRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{147}
}
func (m *GetConsumeRankRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConsumeRankRsp.Unmarshal(m, b)
}
func (m *GetConsumeRankRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConsumeRankRsp.Marshal(b, m, deterministic)
}
func (dst *GetConsumeRankRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConsumeRankRsp.Merge(dst, src)
}
func (m *GetConsumeRankRsp) XXX_Size() int {
	return xxx_messageInfo_GetConsumeRankRsp.Size(m)
}
func (m *GetConsumeRankRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConsumeRankRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConsumeRankRsp proto.InternalMessageInfo

func (m *GetConsumeRankRsp) GetConsumeRankList() []*ConsumeRankItem {
	if m != nil {
		return m.ConsumeRankList
	}
	return nil
}

// 获取语音直播该月收益情况
type GetYuyinMonthIncomeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetYuyinMonthIncomeReq) Reset()         { *m = GetYuyinMonthIncomeReq{} }
func (m *GetYuyinMonthIncomeReq) String() string { return proto.CompactTextString(m) }
func (*GetYuyinMonthIncomeReq) ProtoMessage()    {}
func (*GetYuyinMonthIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{148}
}
func (m *GetYuyinMonthIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinMonthIncomeReq.Unmarshal(m, b)
}
func (m *GetYuyinMonthIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinMonthIncomeReq.Marshal(b, m, deterministic)
}
func (dst *GetYuyinMonthIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinMonthIncomeReq.Merge(dst, src)
}
func (m *GetYuyinMonthIncomeReq) XXX_Size() int {
	return xxx_messageInfo_GetYuyinMonthIncomeReq.Size(m)
}
func (m *GetYuyinMonthIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinMonthIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinMonthIncomeReq proto.InternalMessageInfo

func (m *GetYuyinMonthIncomeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetYuyinMonthIncomeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetYuyinMonthIncomeRsp struct {
	MonthlyIncome        *MonthlyIncome `protobuf:"bytes,1,opt,name=monthly_income,json=monthlyIncome,proto3" json:"monthly_income"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetYuyinMonthIncomeRsp) Reset()         { *m = GetYuyinMonthIncomeRsp{} }
func (m *GetYuyinMonthIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*GetYuyinMonthIncomeRsp) ProtoMessage()    {}
func (*GetYuyinMonthIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{149}
}
func (m *GetYuyinMonthIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinMonthIncomeRsp.Unmarshal(m, b)
}
func (m *GetYuyinMonthIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinMonthIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *GetYuyinMonthIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinMonthIncomeRsp.Merge(dst, src)
}
func (m *GetYuyinMonthIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_GetYuyinMonthIncomeRsp.Size(m)
}
func (m *GetYuyinMonthIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinMonthIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinMonthIncomeRsp proto.InternalMessageInfo

func (m *GetYuyinMonthIncomeRsp) GetMonthlyIncome() *MonthlyIncome {
	if m != nil {
		return m.MonthlyIncome
	}
	return nil
}

// 获取语音直播按月份额外收益
type GetGuildYuyinExtraIncomeReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	Guildid              uint32   `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid"`
	BeginTime            int64    `protobuf:"varint,5,opt,name=begin_time,json=beginTime,proto3" json:"begin_time"`
	EndTime              int64    `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildYuyinExtraIncomeReq) Reset()         { *m = GetGuildYuyinExtraIncomeReq{} }
func (m *GetGuildYuyinExtraIncomeReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildYuyinExtraIncomeReq) ProtoMessage()    {}
func (*GetGuildYuyinExtraIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{150}
}
func (m *GetGuildYuyinExtraIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildYuyinExtraIncomeReq.Unmarshal(m, b)
}
func (m *GetGuildYuyinExtraIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildYuyinExtraIncomeReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildYuyinExtraIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildYuyinExtraIncomeReq.Merge(dst, src)
}
func (m *GetGuildYuyinExtraIncomeReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildYuyinExtraIncomeReq.Size(m)
}
func (m *GetGuildYuyinExtraIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildYuyinExtraIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildYuyinExtraIncomeReq proto.InternalMessageInfo

func (m *GetGuildYuyinExtraIncomeReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *GetGuildYuyinExtraIncomeReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

func (m *GetGuildYuyinExtraIncomeReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetGuildYuyinExtraIncomeReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetGuildYuyinExtraIncomeRsp struct {
	List                 []*ExtraIncome `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetGuildYuyinExtraIncomeRsp) Reset()         { *m = GetGuildYuyinExtraIncomeRsp{} }
func (m *GetGuildYuyinExtraIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*GetGuildYuyinExtraIncomeRsp) ProtoMessage()    {}
func (*GetGuildYuyinExtraIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{151}
}
func (m *GetGuildYuyinExtraIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildYuyinExtraIncomeRsp.Unmarshal(m, b)
}
func (m *GetGuildYuyinExtraIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildYuyinExtraIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *GetGuildYuyinExtraIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildYuyinExtraIncomeRsp.Merge(dst, src)
}
func (m *GetGuildYuyinExtraIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_GetGuildYuyinExtraIncomeRsp.Size(m)
}
func (m *GetGuildYuyinExtraIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildYuyinExtraIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildYuyinExtraIncomeRsp proto.InternalMessageInfo

func (m *GetGuildYuyinExtraIncomeRsp) GetList() []*ExtraIncome {
	if m != nil {
		return m.List
	}
	return nil
}

// 获取语音直播工会任务列表
type GetGuildYuyinTaskListReq struct {
	Uid                  string   `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid"`
	Guildid              uint32   `protobuf:"varint,2,opt,name=guildid,proto3" json:"guildid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildYuyinTaskListReq) Reset()         { *m = GetGuildYuyinTaskListReq{} }
func (m *GetGuildYuyinTaskListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildYuyinTaskListReq) ProtoMessage()    {}
func (*GetGuildYuyinTaskListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{152}
}
func (m *GetGuildYuyinTaskListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildYuyinTaskListReq.Unmarshal(m, b)
}
func (m *GetGuildYuyinTaskListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildYuyinTaskListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildYuyinTaskListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildYuyinTaskListReq.Merge(dst, src)
}
func (m *GetGuildYuyinTaskListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildYuyinTaskListReq.Size(m)
}
func (m *GetGuildYuyinTaskListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildYuyinTaskListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildYuyinTaskListReq proto.InternalMessageInfo

func (m *GetGuildYuyinTaskListReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *GetGuildYuyinTaskListReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

// 公会任务比例信息
type GuildTaskRatioInfo struct {
	Value                uint64   `protobuf:"varint,1,opt,name=value,proto3" json:"value"`
	Ratio                string   `protobuf:"bytes,2,opt,name=ratio,proto3" json:"ratio"`
	ExtraRatio           string   `protobuf:"bytes,3,opt,name=extra_ratio,json=extraRatio,proto3" json:"extra_ratio"`
	Level                uint32   `protobuf:"varint,4,opt,name=level,proto3" json:"level"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildTaskRatioInfo) Reset()         { *m = GuildTaskRatioInfo{} }
func (m *GuildTaskRatioInfo) String() string { return proto.CompactTextString(m) }
func (*GuildTaskRatioInfo) ProtoMessage()    {}
func (*GuildTaskRatioInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{153}
}
func (m *GuildTaskRatioInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildTaskRatioInfo.Unmarshal(m, b)
}
func (m *GuildTaskRatioInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildTaskRatioInfo.Marshal(b, m, deterministic)
}
func (dst *GuildTaskRatioInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildTaskRatioInfo.Merge(dst, src)
}
func (m *GuildTaskRatioInfo) XXX_Size() int {
	return xxx_messageInfo_GuildTaskRatioInfo.Size(m)
}
func (m *GuildTaskRatioInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildTaskRatioInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildTaskRatioInfo proto.InternalMessageInfo

func (m *GuildTaskRatioInfo) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *GuildTaskRatioInfo) GetRatio() string {
	if m != nil {
		return m.Ratio
	}
	return ""
}

func (m *GuildTaskRatioInfo) GetExtraRatio() string {
	if m != nil {
		return m.ExtraRatio
	}
	return ""
}

func (m *GuildTaskRatioInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

// 公会任务详情
type GuildTaskDetailInfo struct {
	Name                 string                `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	RatioList            []*GuildTaskRatioInfo `protobuf:"bytes,2,rep,name=ratio_list,json=ratioList,proto3" json:"ratio_list"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GuildTaskDetailInfo) Reset()         { *m = GuildTaskDetailInfo{} }
func (m *GuildTaskDetailInfo) String() string { return proto.CompactTextString(m) }
func (*GuildTaskDetailInfo) ProtoMessage()    {}
func (*GuildTaskDetailInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{154}
}
func (m *GuildTaskDetailInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildTaskDetailInfo.Unmarshal(m, b)
}
func (m *GuildTaskDetailInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildTaskDetailInfo.Marshal(b, m, deterministic)
}
func (dst *GuildTaskDetailInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildTaskDetailInfo.Merge(dst, src)
}
func (m *GuildTaskDetailInfo) XXX_Size() int {
	return xxx_messageInfo_GuildTaskDetailInfo.Size(m)
}
func (m *GuildTaskDetailInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildTaskDetailInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildTaskDetailInfo proto.InternalMessageInfo

func (m *GuildTaskDetailInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GuildTaskDetailInfo) GetRatioList() []*GuildTaskRatioInfo {
	if m != nil {
		return m.RatioList
	}
	return nil
}

type GetGuildYuyinTaskListRsp struct {
	List                 []*GuildTask           `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	RemainTime           int64                  `protobuf:"varint,2,opt,name=remain_time,json=remainTime,proto3" json:"remain_time"`
	BuffTotal            string                 `protobuf:"bytes,3,opt,name=buff_total,json=buffTotal,proto3" json:"buff_total"`
	TaskDetail           []*GuildTaskDetailInfo `protobuf:"bytes,4,rep,name=task_detail,json=taskDetail,proto3" json:"task_detail"`
	IsNewGuild           uint32                 `protobuf:"varint,5,opt,name=is_new_guild,json=isNewGuild,proto3" json:"is_new_guild"`
	NewGuildPeriod       string                 `protobuf:"bytes,6,opt,name=new_guild_period,json=newGuildPeriod,proto3" json:"new_guild_period"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetGuildYuyinTaskListRsp) Reset()         { *m = GetGuildYuyinTaskListRsp{} }
func (m *GetGuildYuyinTaskListRsp) String() string { return proto.CompactTextString(m) }
func (*GetGuildYuyinTaskListRsp) ProtoMessage()    {}
func (*GetGuildYuyinTaskListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{155}
}
func (m *GetGuildYuyinTaskListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildYuyinTaskListRsp.Unmarshal(m, b)
}
func (m *GetGuildYuyinTaskListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildYuyinTaskListRsp.Marshal(b, m, deterministic)
}
func (dst *GetGuildYuyinTaskListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildYuyinTaskListRsp.Merge(dst, src)
}
func (m *GetGuildYuyinTaskListRsp) XXX_Size() int {
	return xxx_messageInfo_GetGuildYuyinTaskListRsp.Size(m)
}
func (m *GetGuildYuyinTaskListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildYuyinTaskListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildYuyinTaskListRsp proto.InternalMessageInfo

func (m *GetGuildYuyinTaskListRsp) GetList() []*GuildTask {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetGuildYuyinTaskListRsp) GetRemainTime() int64 {
	if m != nil {
		return m.RemainTime
	}
	return 0
}

func (m *GetGuildYuyinTaskListRsp) GetBuffTotal() string {
	if m != nil {
		return m.BuffTotal
	}
	return ""
}

func (m *GetGuildYuyinTaskListRsp) GetTaskDetail() []*GuildTaskDetailInfo {
	if m != nil {
		return m.TaskDetail
	}
	return nil
}

func (m *GetGuildYuyinTaskListRsp) GetIsNewGuild() uint32 {
	if m != nil {
		return m.IsNewGuild
	}
	return 0
}

func (m *GetGuildYuyinTaskListRsp) GetNewGuildPeriod() string {
	if m != nil {
		return m.NewGuildPeriod
	}
	return ""
}

// 公会任务列表
type GuildTask struct {
	Value                uint64   `protobuf:"varint,6,opt,name=value,proto3" json:"value"`
	Ratio                string   `protobuf:"bytes,7,opt,name=ratio,proto3" json:"ratio"`
	Level                uint32   `protobuf:"varint,8,opt,name=level,proto3" json:"level"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildTask) Reset()         { *m = GuildTask{} }
func (m *GuildTask) String() string { return proto.CompactTextString(m) }
func (*GuildTask) ProtoMessage()    {}
func (*GuildTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamond_logic_2ea30aef7a72f107, []int{156}
}
func (m *GuildTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildTask.Unmarshal(m, b)
}
func (m *GuildTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildTask.Marshal(b, m, deterministic)
}
func (dst *GuildTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildTask.Merge(dst, src)
}
func (m *GuildTask) XXX_Size() int {
	return xxx_messageInfo_GuildTask.Size(m)
}
func (m *GuildTask) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildTask.DiscardUnknown(m)
}

var xxx_messageInfo_GuildTask proto.InternalMessageInfo

func (m *GuildTask) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *GuildTask) GetRatio() string {
	if m != nil {
		return m.Ratio
	}
	return ""
}

func (m *GuildTask) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func init() {
	proto.RegisterType((*OperateInfoT)(nil), "gold_diamond_logic.OperateInfoT")
	proto.RegisterType((*ApplicationCooperateReq)(nil), "gold_diamond_logic.ApplicationCooperateReq")
	proto.RegisterType((*ApplicationCooperateRsp)(nil), "gold_diamond_logic.ApplicationCooperateRsp")
	proto.RegisterType((*CooperateReq)(nil), "gold_diamond_logic.CooperateReq")
	proto.RegisterType((*CooperateRsp)(nil), "gold_diamond_logic.CooperateRsp")
	proto.RegisterType((*ChannelListReq)(nil), "gold_diamond_logic.ChannelListReq")
	proto.RegisterType((*Channelinfot)(nil), "gold_diamond_logic.channelinfot")
	proto.RegisterType((*ChannelListRsp)(nil), "gold_diamond_logic.ChannelListRsp")
	proto.RegisterType((*PendingReq)(nil), "gold_diamond_logic.PendingReq")
	proto.RegisterType((*PendingRsp)(nil), "gold_diamond_logic.PendingRsp")
	proto.RegisterType((*PendingDetailReq)(nil), "gold_diamond_logic.PendingDetailReq")
	proto.RegisterType((*PendingDetail)(nil), "gold_diamond_logic.PendingDetail")
	proto.RegisterType((*PendingDetailRsp)(nil), "gold_diamond_logic.PendingDetailRsp")
	proto.RegisterType((*YuyinPendingDetail)(nil), "gold_diamond_logic.YuyinPendingDetail")
	proto.RegisterType((*YuyinPendingDetailRsp)(nil), "gold_diamond_logic.YuyinPendingDetailRsp")
	proto.RegisterType((*TodayIncomeReq)(nil), "gold_diamond_logic.TodayIncomeReq")
	proto.RegisterType((*TodayIncomeInfo)(nil), "gold_diamond_logic.TodayIncomeInfo")
	proto.RegisterType((*TodayIncomeRsp)(nil), "gold_diamond_logic.TodayIncomeRsp")
	proto.RegisterType((*YuyinTodayIncomeReq)(nil), "gold_diamond_logic.YuyinTodayIncomeReq")
	proto.RegisterType((*YuyinTodayIncomeInfo)(nil), "gold_diamond_logic.YuyinTodayIncomeInfo")
	proto.RegisterType((*YuyinTodayIncomeRsp)(nil), "gold_diamond_logic.YuyinTodayIncomeRsp")
	proto.RegisterType((*MonthIncomeReq)(nil), "gold_diamond_logic.MonthIncomeReq")
	proto.RegisterType((*MonthIncomeInfo)(nil), "gold_diamond_logic.MonthIncomeInfo")
	proto.RegisterType((*MonthIncomeRsp)(nil), "gold_diamond_logic.MonthIncomeRsp")
	proto.RegisterType((*ConsumeSearchReq)(nil), "gold_diamond_logic.ConsumeSearchReq")
	proto.RegisterType((*ConsumeDetail)(nil), "gold_diamond_logic.ConsumeDetail")
	proto.RegisterType((*ConsumeSearchRsp)(nil), "gold_diamond_logic.ConsumeSearchRsp")
	proto.RegisterType((*YuyinConsumeSearchReq)(nil), "gold_diamond_logic.YuyinConsumeSearchReq")
	proto.RegisterType((*YuyinConsumeDetail)(nil), "gold_diamond_logic.YuyinConsumeDetail")
	proto.RegisterType((*YuyinConsumeSearchRsp)(nil), "gold_diamond_logic.YuyinConsumeSearchRsp")
	proto.RegisterType((*ChannelIncomeReq)(nil), "gold_diamond_logic.ChannelIncomeReq")
	proto.RegisterType((*ChannelIncomeInfo)(nil), "gold_diamond_logic.ChannelIncomeInfo")
	proto.RegisterType((*ChannelIncomeRsp)(nil), "gold_diamond_logic.ChannelIncomeRsp")
	proto.RegisterType((*DayTrendInfo)(nil), "gold_diamond_logic.DayTrendInfo")
	proto.RegisterType((*GuildChannelInfo)(nil), "gold_diamond_logic.GuildChannelInfo")
	proto.RegisterType((*GuildInitInfoReq)(nil), "gold_diamond_logic.GuildInitInfoReq")
	proto.RegisterType((*GuildInitInfoRsp)(nil), "gold_diamond_logic.GuildInitInfoRsp")
	proto.RegisterType((*GuildCheckReq)(nil), "gold_diamond_logic.GuildCheckReq")
	proto.RegisterType((*GuildCheckRsp)(nil), "gold_diamond_logic.GuildCheckRsp")
	proto.RegisterType((*GuildConsumeDayQoqReq)(nil), "gold_diamond_logic.GuildConsumeDayQoqReq")
	proto.RegisterType((*GuildConsumeDayQoqRsp)(nil), "gold_diamond_logic.GuildConsumeDayQoqRsp")
	proto.RegisterType((*GuildDayComparisonReq)(nil), "gold_diamond_logic.GuildDayComparisonReq")
	proto.RegisterType((*DayComparisonInfo)(nil), "gold_diamond_logic.DayComparisonInfo")
	proto.RegisterType((*GuildDayComparisonResp)(nil), "gold_diamond_logic.GuildDayComparisonResp")
	proto.RegisterType((*ConsumeRankItem)(nil), "gold_diamond_logic.ConsumeRankItem")
	proto.RegisterType((*ConsumeRankReq)(nil), "gold_diamond_logic.ConsumeRankReq")
	proto.RegisterType((*ConsumeRankRsp)(nil), "gold_diamond_logic.ConsumeRankRsp")
	proto.RegisterType((*RoomConsumeQoqReq)(nil), "gold_diamond_logic.RoomConsumeQoqReq")
	proto.RegisterType((*RoomConsumeQoqRsp)(nil), "gold_diamond_logic.RoomConsumeQoqRsp")
	proto.RegisterType((*GetGuildAnchorListReq)(nil), "gold_diamond_logic.GetGuildAnchorListReq")
	proto.RegisterType((*GuildAnchorSimpleInfo)(nil), "gold_diamond_logic.GuildAnchorSimpleInfo")
	proto.RegisterType((*GetGuildAnchorListRsp)(nil), "gold_diamond_logic.GetGuildAnchorListRsp")
	proto.RegisterType((*GetGuildIdReq)(nil), "gold_diamond_logic.GetGuildIdReq")
	proto.RegisterType((*GetGuildIdRsp)(nil), "gold_diamond_logic.GetGuildIdRsp")
	proto.RegisterType((*ExamUidGuildIdInfo)(nil), "gold_diamond_logic.ExamUidGuildIdInfo")
	proto.RegisterType((*RecommendExamLimitCheckResp)(nil), "gold_diamond_logic.RecommendExamLimitCheckResp")
	proto.RegisterType((*OpenChannelReq)(nil), "gold_diamond_logic.OpenChannelReq")
	proto.RegisterType((*TagInfo)(nil), "gold_diamond_logic.TagInfo")
	proto.RegisterType((*OpenChannelResp)(nil), "gold_diamond_logic.OpenChannelResp")
	proto.RegisterType((*RecommendUsersReq)(nil), "gold_diamond_logic.RecommendUsersReq")
	proto.RegisterType((*UserInfo)(nil), "gold_diamond_logic.UserInfo")
	proto.RegisterType((*SearchMultiLayman)(nil), "gold_diamond_logic.SearchMultiLayman")
	proto.RegisterType((*RecommendUsersResp)(nil), "gold_diamond_logic.RecommendUsersResp")
	proto.RegisterType((*RecommendExamBaseInfo)(nil), "gold_diamond_logic.RecommendExamBaseInfo")
	proto.RegisterType((*ExamTagInfo)(nil), "gold_diamond_logic.ExamTagInfo")
	proto.RegisterType((*GetExamTagListResp)(nil), "gold_diamond_logic.GetExamTagListResp")
	proto.RegisterType((*YuyinExamApplicationInfo)(nil), "gold_diamond_logic.YuyinExamApplicationInfo")
	proto.RegisterType((*YuyinExamGuildOwnerOperationReq)(nil), "gold_diamond_logic.YuyinExamGuildOwnerOperationReq")
	proto.RegisterType((*YuyinExamGuildOwnerOperationResp)(nil), "gold_diamond_logic.YuyinExamGuildOwnerOperationResp")
	proto.RegisterType((*YuyinExamReq)(nil), "gold_diamond_logic.YuyinExamReq")
	proto.RegisterType((*YuyinExamResp)(nil), "gold_diamond_logic.YuyinExamResp")
	proto.RegisterType((*GetYuyinExamReq)(nil), "gold_diamond_logic.GetYuyinExamReq")
	proto.RegisterType((*GetYuyinExamResp)(nil), "gold_diamond_logic.GetYuyinExamResp")
	proto.RegisterType((*GetYuyinExamForGuildResp)(nil), "gold_diamond_logic.GetYuyinExamForGuildResp")
	proto.RegisterType((*GetYuyinExamStatusReq)(nil), "gold_diamond_logic.GetYuyinExamStatusReq")
	proto.RegisterType((*GetYuyinExamStatusResp)(nil), "gold_diamond_logic.GetYuyinExamStatusResp")
	proto.RegisterType((*TagsInfo)(nil), "gold_diamond_logic.TagsInfo")
	proto.RegisterType((*SubmitInfo)(nil), "gold_diamond_logic.SubmitInfo")
	proto.RegisterType((*YuyinExamInfo)(nil), "gold_diamond_logic.YuyinExamInfo")
	proto.RegisterType((*YuyinSubmit)(nil), "gold_diamond_logic.YuyinSubmit")
	proto.RegisterType((*ApplicationRecordForGuildOwnerReq)(nil), "gold_diamond_logic.ApplicationRecordForGuildOwnerReq")
	proto.RegisterType((*ApplicationRecordInfo)(nil), "gold_diamond_logic.ApplicationRecordInfo")
	proto.RegisterType((*ApplicationRecordForGuildOwnerResp)(nil), "gold_diamond_logic.ApplicationRecordForGuildOwnerResp")
	proto.RegisterType((*CheckUidMainReq)(nil), "gold_diamond_logic.CheckUidMainReq")
	proto.RegisterType((*CheckUidMainResp)(nil), "gold_diamond_logic.CheckUidMainResp")
	proto.RegisterType((*AmuseExtraIncomeDetailReq)(nil), "gold_diamond_logic.AmuseExtraIncomeDetailReq")
	proto.RegisterType((*AmuseExtraIncomeDetailResp)(nil), "gold_diamond_logic.AmuseExtraIncomeDetailResp")
	proto.RegisterType((*AmuseExtraIncomeDetailResp_ChannelItem)(nil), "gold_diamond_logic.AmuseExtraIncomeDetailResp.ChannelItem")
	proto.RegisterType((*InteractGameIncomeInfo)(nil), "gold_diamond_logic.InteractGameIncomeInfo")
	proto.RegisterType((*InteractGameIncomeRsp)(nil), "gold_diamond_logic.InteractGameIncomeRsp")
	proto.RegisterType((*InteractGameExtraIncomeReq)(nil), "gold_diamond_logic.InteractGameExtraIncomeReq")
	proto.RegisterType((*InteractGameExtraIncomeResp)(nil), "gold_diamond_logic.InteractGameExtraIncomeResp")
	proto.RegisterType((*InteractGameExtraIncomeDetail)(nil), "gold_diamond_logic.InteractGameExtraIncomeDetail")
	proto.RegisterType((*EsportIncomeInfo)(nil), "gold_diamond_logic.EsportIncomeInfo")
	proto.RegisterType((*EsportIncomeRsp)(nil), "gold_diamond_logic.EsportIncomeRsp")
	proto.RegisterType((*EsportPendingReq)(nil), "gold_diamond_logic.EsportPendingReq")
	proto.RegisterType((*EsportPendingRsp)(nil), "gold_diamond_logic.EsportPendingRsp")
	proto.RegisterType((*EsportCoachesMonthIncomeReq)(nil), "gold_diamond_logic.EsportCoachesMonthIncomeReq")
	proto.RegisterType((*EsportCoachesMonthIncomeInfo)(nil), "gold_diamond_logic.EsportCoachesMonthIncomeInfo")
	proto.RegisterType((*EsportCoachesMonthIncomeRsp)(nil), "gold_diamond_logic.EsportCoachesMonthIncomeRsp")
	proto.RegisterType((*EsportConsumeSearchReq)(nil), "gold_diamond_logic.EsportConsumeSearchReq")
	proto.RegisterType((*EsportConsumeDetail)(nil), "gold_diamond_logic.EsportConsumeDetail")
	proto.RegisterType((*EsportConsumeSearchRsp)(nil), "gold_diamond_logic.EsportConsumeSearchRsp")
	proto.RegisterType((*BatchGetAuditSkillRequest)(nil), "gold_diamond_logic.BatchGetAuditSkillRequest")
	proto.RegisterType((*BatchGetAuditSkillResponse)(nil), "gold_diamond_logic.BatchGetAuditSkillResponse")
	proto.RegisterType((*AuditSkillRecord)(nil), "gold_diamond_logic.AuditSkillRecord")
	proto.RegisterType((*UserSkillInfo)(nil), "gold_diamond_logic.UserSkillInfo")
	proto.RegisterType((*SectionInfo)(nil), "gold_diamond_logic.SectionInfo")
	proto.RegisterType((*SetUserSkillAuditTypeRequest)(nil), "gold_diamond_logic.SetUserSkillAuditTypeRequest")
	proto.RegisterType((*SetUserSkillAuditTypeResponse)(nil), "gold_diamond_logic.SetUserSkillAuditTypeResponse")
	proto.RegisterType((*GetGeneralIncomeReq)(nil), "gold_diamond_logic.GetGeneralIncomeReq")
	proto.RegisterType((*GuildInitInfoV2Rsp)(nil), "gold_diamond_logic.GuildInitInfoV2Rsp")
	proto.RegisterType((*AmuseTabCheckV2Rsp)(nil), "gold_diamond_logic.AmuseTabCheckV2Rsp")
	proto.RegisterType((*AudioTabCheckV2Rsp)(nil), "gold_diamond_logic.AudioTabCheckV2Rsp")
	proto.RegisterType((*AmuseLastIncomeTrendRsp)(nil), "gold_diamond_logic.AmuseLastIncomeTrendRsp")
	proto.RegisterType((*AmuseIncomeTrendRsp)(nil), "gold_diamond_logic.AmuseIncomeTrendRsp")
	proto.RegisterType((*AmuseIncomeRsp)(nil), "gold_diamond_logic.AmuseIncomeRsp")
	proto.RegisterType((*AmuseChannelsRsp)(nil), "gold_diamond_logic.AmuseChannelsRsp")
	proto.RegisterType((*ApplicationInitRsp)(nil), "gold_diamond_logic.ApplicationInitRsp")
	proto.RegisterType((*CashInitRsp)(nil), "gold_diamond_logic.CashInitRsp")
	proto.RegisterType((*GuildTaskInfoV2)(nil), "gold_diamond_logic.GuildTaskInfoV2")
	proto.RegisterType((*GetGuildTaskListV2Rsp)(nil), "gold_diamond_logic.GetGuildTaskListV2Rsp")
	proto.RegisterType((*GetGeneralIncomeV2Rsp)(nil), "gold_diamond_logic.GetGeneralIncomeV2Rsp")
	proto.RegisterType((*GetGeneralTrendListReq)(nil), "gold_diamond_logic.GetGeneralTrendListReq")
	proto.RegisterType((*GetGeneralIncomeRsp)(nil), "gold_diamond_logic.GetGeneralIncomeRsp")
	proto.RegisterType((*GetAnchorInfoRsp)(nil), "gold_diamond_logic.GetAnchorInfoRsp")
	proto.RegisterType((*GetDateDimenIncomeRsp)(nil), "gold_diamond_logic.GetDateDimenIncomeRsp")
	proto.RegisterType((*GetWeekDimenIncomeRsp)(nil), "gold_diamond_logic.GetWeekDimenIncomeRsp")
	proto.RegisterType((*GetMonthDimenIncomeRsp)(nil), "gold_diamond_logic.GetMonthDimenIncomeRsp")
	proto.RegisterType((*TrendInfo)(nil), "gold_diamond_logic.TrendInfo")
	proto.RegisterType((*GetIncomeTrendListRsp)(nil), "gold_diamond_logic.GetIncomeTrendListRsp")
	proto.RegisterType((*GetvalidAnchorTrendListRsp)(nil), "gold_diamond_logic.GetvalidAnchorTrendListRsp")
	proto.RegisterType((*ExtraIncome)(nil), "gold_diamond_logic.ExtraIncome")
	proto.RegisterType((*GetGuildExtraIncomeRsp)(nil), "gold_diamond_logic.GetGuildExtraIncomeRsp")
	proto.RegisterType((*GuildTaskInfo)(nil), "gold_diamond_logic.GuildTaskInfo")
	proto.RegisterType((*GetGuildTaskListRsp)(nil), "gold_diamond_logic.GetGuildTaskListRsp")
	proto.RegisterType((*AnchorInfo)(nil), "gold_diamond_logic.AnchorInfo")
	proto.RegisterType((*GetAnchorListRsp)(nil), "gold_diamond_logic.GetAnchorListRsp")
	proto.RegisterType((*GetYuyinIncomeDetailReq)(nil), "gold_diamond_logic.GetYuyinIncomeDetailReq")
	proto.RegisterType((*GetYuyinIncomeDetailRsp)(nil), "gold_diamond_logic.GetYuyinIncomeDetailRsp")
	proto.RegisterType((*DailyIncome)(nil), "gold_diamond_logic.DailyIncome")
	proto.RegisterType((*WeeklyIncome)(nil), "gold_diamond_logic.WeeklyIncome")
	proto.RegisterType((*MonthlyIncome)(nil), "gold_diamond_logic.MonthlyIncome")
	proto.RegisterType((*GetYuyinThisMonthTrendListReq)(nil), "gold_diamond_logic.GetYuyinThisMonthTrendListReq")
	proto.RegisterType((*GetYuyinThisMonthTrendListRsp)(nil), "gold_diamond_logic.GetYuyinThisMonthTrendListRsp")
	proto.RegisterType((*GetYuyinLastMonthTrendListReq)(nil), "gold_diamond_logic.GetYuyinLastMonthTrendListReq")
	proto.RegisterType((*GetYuyinLastMonthTrendListRsp)(nil), "gold_diamond_logic.GetYuyinLastMonthTrendListRsp")
	proto.RegisterType((*GetConsumeRankReq)(nil), "gold_diamond_logic.GetConsumeRankReq")
	proto.RegisterType((*GetConsumeRankRsp)(nil), "gold_diamond_logic.GetConsumeRankRsp")
	proto.RegisterType((*GetYuyinMonthIncomeReq)(nil), "gold_diamond_logic.GetYuyinMonthIncomeReq")
	proto.RegisterType((*GetYuyinMonthIncomeRsp)(nil), "gold_diamond_logic.GetYuyinMonthIncomeRsp")
	proto.RegisterType((*GetGuildYuyinExtraIncomeReq)(nil), "gold_diamond_logic.GetGuildYuyinExtraIncomeReq")
	proto.RegisterType((*GetGuildYuyinExtraIncomeRsp)(nil), "gold_diamond_logic.GetGuildYuyinExtraIncomeRsp")
	proto.RegisterType((*GetGuildYuyinTaskListReq)(nil), "gold_diamond_logic.GetGuildYuyinTaskListReq")
	proto.RegisterType((*GuildTaskRatioInfo)(nil), "gold_diamond_logic.GuildTaskRatioInfo")
	proto.RegisterType((*GuildTaskDetailInfo)(nil), "gold_diamond_logic.GuildTaskDetailInfo")
	proto.RegisterType((*GetGuildYuyinTaskListRsp)(nil), "gold_diamond_logic.GetGuildYuyinTaskListRsp")
	proto.RegisterType((*GuildTask)(nil), "gold_diamond_logic.GuildTask")
	proto.RegisterEnum("gold_diamond_logic.RangeType", RangeType_name, RangeType_value)
	proto.RegisterEnum("gold_diamond_logic.ErrorCode", ErrorCode_name, ErrorCode_value)
	proto.RegisterEnum("gold_diamond_logic.ExamTagType", ExamTagType_name, ExamTagType_value)
	proto.RegisterEnum("gold_diamond_logic.YuyinExamStatus", YuyinExamStatus_name, YuyinExamStatus_value)
}

func init() {
	proto.RegisterFile("golddiamond-logic.proto", fileDescriptor_golddiamond_logic_2ea30aef7a72f107)
}

var fileDescriptor_golddiamond_logic_2ea30aef7a72f107 = []byte{
	// 6705 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x7d, 0x5d, 0x6c, 0x64, 0xc9,
	0x55, 0xf0, 0x77, 0xfb, 0xc7, 0xee, 0x3e, 0xdd, 0xed, 0x6e, 0x5f, 0x8f, 0xed, 0x9e, 0x99, 0x9d,
	0x9d, 0xd9, 0x3b, 0xfb, 0x33, 0x3b, 0xdf, 0x66, 0xb3, 0xbb, 0xc3, 0x26, 0x51, 0x48, 0xa2, 0xf5,
	0xd8, 0x3d, 0x5e, 0x27, 0x33, 0x9e, 0xcd, 0xb5, 0x67, 0x36, 0x03, 0x44, 0xcd, 0x9d, 0xee, 0xb2,
	0x7d, 0xe5, 0xee, 0x7b, 0x7b, 0xbb, 0x6e, 0x8f, 0xed, 0x20, 0x14, 0x04, 0x52, 0x84, 0x22, 0xa1,
	0xf0, 0x02, 0x08, 0x89, 0x1f, 0x05, 0x1e, 0x22, 0x21, 0x21, 0x45, 0x0a, 0x44, 0x24, 0x44, 0xe2,
	0x0d, 0x09, 0x01, 0x02, 0xc2, 0x13, 0xf0, 0xc0, 0x0b, 0x3c, 0xf2, 0x12, 0xf1, 0x02, 0x2f, 0xa0,
	0x3a, 0xa7, 0xea, 0xde, 0xaa, 0xdb, 0xf7, 0xb6, 0xed, 0xc9, 0x64, 0x41, 0x28, 0x4f, 0xe3, 0x7b,
	0xea, 0xd4, 0xa9, 0xaa, 0x53, 0xe7, 0xaf, 0x4e, 0x9d, 0xea, 0x81, 0xd5, 0xfd, 0x70, 0xd0, 0xef,
	0xfb, 0xde, 0x30, 0x0c, 0xfa, 0x1f, 0x19, 0x84, 0xfb, 0x7e, 0xef, 0xf5, 0xd1, 0x38, 0x8c, 0x42,
	0xdb, 0x16, 0x0d, 0x5d, 0xd9, 0xd2, 0xc5, 0x16, 0xe7, 0x07, 0x16, 0xd4, 0xef, 0x8f, 0xd8, 0xd8,
	0x8b, 0xd8, 0x56, 0xb0, 0x17, 0xee, 0xda, 0x2f, 0x40, 0x7d, 0x18, 0x06, 0xd1, 0x41, 0xd7, 0x0f,
	0x7a, 0xe1, 0x90, 0xb5, 0xad, 0x6b, 0xd6, 0x8d, 0xaa, 0x5b, 0x43, 0xd8, 0x16, 0x82, 0xec, 0xab,
	0x50, 0xa3, 0xc6, 0xae, 0x3f, 0xdc, 0xe7, 0xed, 0xc2, 0xb5, 0xe2, 0x8d, 0xaa, 0x0b, 0x04, 0xda,
	0x1a, 0xee, 0x73, 0xbb, 0x0d, 0xf3, 0xbd, 0x30, 0xe0, 0x93, 0x21, 0x6b, 0x17, 0xb1, 0xbb, 0xfa,
	0x14, 0xd4, 0xe5, 0x9f, 0xd4, 0xb7, 0x84, 0x7d, 0x6b, 0x12, 0x86, 0x9d, 0x2f, 0x41, 0x25, 0x62,
	0xde, 0x90, 0xfb, 0x5f, 0x62, 0xed, 0xf2, 0x35, 0xeb, 0x46, 0xc3, 0x8d, 0xbf, 0xed, 0xe7, 0x01,
	0xbc, 0x5e, 0x2f, 0x1c, 0x8e, 0xbc, 0xe0, 0x84, 0xb7, 0xe7, 0x68, 0xe0, 0x04, 0x62, 0xbf, 0x04,
	0x0b, 0x8c, 0x47, 0xfe, 0xd0, 0x8b, 0x58, 0x17, 0x3b, 0xb5, 0xe7, 0x91, 0x42, 0x43, 0x41, 0x77,
	0x05, 0xd0, 0xf9, 0xcb, 0x32, 0xac, 0xae, 0x8d, 0x46, 0x03, 0xbf, 0xe7, 0x45, 0x7e, 0x18, 0xac,
	0x87, 0x21, 0x71, 0xc0, 0x65, 0x1f, 0x88, 0xb9, 0xef, 0x4f, 0xfc, 0x41, 0xdf, 0xef, 0xe3, 0xd2,
	0x1b, 0xae, 0xfa, 0xb4, 0x5f, 0x85, 0x96, 0x97, 0x74, 0xea, 0x46, 0x27, 0x23, 0xd6, 0x2e, 0x20,
	0x4a, 0x53, 0x83, 0xef, 0x9e, 0x8c, 0x98, 0x7d, 0x1d, 0x1a, 0xa3, 0x81, 0x17, 0xed, 0x85, 0xe3,
	0x61, 0x37, 0xf0, 0x62, 0x36, 0xd4, 0x15, 0x70, 0xdb, 0x1b, 0x32, 0x41, 0x2f, 0x46, 0xea, 0x85,
	0x41, 0xc4, 0x82, 0xa8, 0x5d, 0x42, 0xbc, 0xa6, 0x82, 0xaf, 0x13, 0x58, 0x70, 0x3c, 0x46, 0xf5,
	0xfb, 0x92, 0x2d, 0xa0, 0x40, 0x5b, 0x7d, 0xfb, 0x6d, 0x58, 0x49, 0x68, 0xb1, 0x71, 0xe4, 0xef,
	0xc9, 0xe9, 0x48, 0x26, 0x2d, 0xc7, 0x14, 0xf5, 0x46, 0xfb, 0x65, 0x68, 0x6a, 0x74, 0xbb, 0x3c,
	0x1a, 0x23, 0xc3, 0xaa, 0x6e, 0x23, 0xa1, 0xbd, 0x13, 0x8d, 0xed, 0x2b, 0x00, 0x62, 0x89, 0x27,
	0xb4, 0xe8, 0x0a, 0x0e, 0x5f, 0x45, 0x08, 0x2e, 0xb7, 0x0d, 0xf3, 0x51, 0x84, 0x6c, 0x6a, 0x57,
	0x89, 0x67, 0xf2, 0xd3, 0x7e, 0x1d, 0x96, 0xe2, 0x01, 0x10, 0x42, 0x14, 0x00, 0xb1, 0x16, 0x55,
	0xd3, 0xa6, 0x68, 0x41, 0x4a, 0x3a, 0x4f, 0xd4, 0x36, 0xd4, 0x4c, 0x9e, 0x6c, 0xca, 0xed, 0xf8,
	0x09, 0x6d, 0xc9, 0x44, 0x3a, 0x3c, 0x0a, 0xd8, 0x78, 0xab, 0xdf, 0xae, 0x63, 0x87, 0x0b, 0x46,
	0x87, 0xfb, 0xd4, 0x66, 0x7f, 0x0c, 0x56, 0x53, 0xbd, 0xbc, 0x49, 0x74, 0x80, 0xb2, 0xd8, 0x30,
	0x39, 0x85, 0xdd, 0xd6, 0x64, 0xa3, 0x94, 0x2c, 0xef, 0xf1, 0xc0, 0xe7, 0x07, 0xdd, 0xc8, 0x1f,
	0xb2, 0xf6, 0x02, 0x31, 0x2a, 0x86, 0xee, 0xfa, 0x43, 0x66, 0x7f, 0x12, 0xe6, 0xa5, 0x2c, 0xb5,
	0x9b, 0xd7, 0xac, 0x1b, 0xb5, 0xb7, 0xae, 0xbd, 0x3e, 0xad, 0x74, 0xaf, 0xeb, 0x0a, 0xe7, 0xaa,
	0x0e, 0xf6, 0x73, 0x50, 0x55, 0x62, 0xca, 0xdb, 0x2d, 0xe2, 0x71, 0x0c, 0x90, 0x3a, 0x15, 0x79,
	0xbd, 0xa8, 0xbd, 0x18, 0xeb, 0x94, 0xf8, 0x74, 0x3e, 0xc8, 0x11, 0x66, 0x3e, 0xb2, 0x97, 0x61,
	0xce, 0xe7, 0x5d, 0x6f, 0x34, 0x42, 0x59, 0xae, 0xb8, 0x65, 0x9f, 0xaf, 0x8d, 0x46, 0xf6, 0x45,
	0xa8, 0xf8, 0xbc, 0x3b, 0xf0, 0x87, 0x7e, 0x84, 0x12, 0x5c, 0x71, 0xe7, 0x7d, 0x7e, 0x57, 0x7c,
	0xda, 0x2f, 0xc2, 0x02, 0xc2, 0xbb, 0x2c, 0xe8, 0xd3, 0x3a, 0x85, 0xe8, 0x16, 0xdd, 0x3a, 0x42,
	0x3b, 0x41, 0x5f, 0x2c, 0xd3, 0xb9, 0x0d, 0xf5, 0x33, 0x2a, 0x4d, 0x1b, 0xe6, 0x51, 0x39, 0x58,
	0x5f, 0xea, 0x8a, 0xfa, 0x74, 0x6e, 0xe8, 0x34, 0xf8, 0x48, 0xc7, 0xb4, 0x4c, 0xcc, 0x9b, 0xb0,
	0xb0, 0x7e, 0xe0, 0x05, 0x01, 0x1b, 0xdc, 0xf5, 0x79, 0x34, 0x73, 0x3c, 0xe7, 0xd7, 0x2c, 0xa8,
	0xf7, 0x08, 0xd9, 0x0f, 0xf6, 0xc2, 0x48, 0x70, 0x55, 0x7d, 0x2b, 0xe4, 0x04, 0x20, 0x5a, 0xfb,
	0x3e, 0x1f, 0x0d, 0xbc, 0x13, 0x5f, 0x4d, 0x30, 0x01, 0xa0, 0xb5, 0x22, 0x54, 0x5d, 0x8b, 0x6b,
	0x12, 0x86, 0x4a, 0xfc, 0x32, 0x34, 0x15, 0xca, 0x13, 0x9f, 0x1d, 0x09, 0xed, 0x24, 0x1d, 0x6e,
	0x48, 0xf0, 0x43, 0x9f, 0x1d, 0x6d, 0xf5, 0x9d, 0x07, 0xe6, 0x1a, 0xf8, 0xc8, 0x5e, 0x4f, 0x88,
	0x0f, 0x7c, 0x1e, 0xb5, 0xad, 0x6b, 0xc5, 0x3c, 0x79, 0xd1, 0x17, 0x14, 0x0f, 0x2f, 0xe8, 0x38,
	0x2f, 0x03, 0xbc, 0xc7, 0x82, 0xbe, 0x1f, 0xec, 0xcf, 0x66, 0xcb, 0xd7, 0xad, 0x04, 0x91, 0x8f,
	0x84, 0x34, 0x4b, 0x0b, 0xfe, 0xd8, 0x1b, 0x78, 0x41, 0x8f, 0xcc, 0x7c, 0xd1, 0x6d, 0x10, 0xf4,
	0x36, 0x01, 0x05, 0xbd, 0x21, 0x1b, 0x3e, 0x66, 0x63, 0x8e, 0xbc, 0x29, 0xba, 0xea, 0x33, 0x6d,
	0xe1, 0x8b, 0x89, 0x85, 0xbf, 0x02, 0xc0, 0x23, 0x6f, 0x1c, 0x91, 0xf0, 0x94, 0xb0, 0xb1, 0x8a,
	0x10, 0x54, 0x90, 0x8b, 0x50, 0x89, 0x25, 0xab, 0x4c, 0x3d, 0x99, 0x14, 0xaa, 0x2f, 0x41, 0x4b,
	0x4e, 0x71, 0x83, 0x45, 0x9e, 0x3f, 0x98, 0x2d, 0x58, 0x57, 0x00, 0x3e, 0x98, 0xb0, 0xf1, 0x89,
	0x6e, 0x87, 0xab, 0x08, 0x41, 0x43, 0x62, 0x43, 0x69, 0xe4, 0xed, 0xd3, 0xec, 0x1a, 0x2e, 0xfe,
	0x2d, 0xc6, 0x16, 0xff, 0x76, 0x83, 0xc9, 0x10, 0x27, 0xd6, 0x70, 0xe7, 0xc5, 0xf7, 0xf6, 0x64,
	0xe8, 0xfc, 0xad, 0x05, 0x0d, 0x63, 0x70, 0xbb, 0x05, 0xc5, 0x49, 0x3c, 0xaa, 0xf8, 0xd3, 0x94,
	0xa4, 0x42, 0x5a, 0x92, 0x6c, 0x28, 0x69, 0x32, 0x82, 0x7f, 0x4f, 0xc9, 0x4f, 0x69, 0x5a, 0x7e,
	0x34, 0x46, 0x96, 0x4d, 0x46, 0xae, 0xc0, 0x9c, 0x74, 0xc1, 0x73, 0xd8, 0x20, 0xbf, 0xb2, 0x24,
	0x6e, 0x3e, 0x4b, 0xe2, 0x26, 0x69, 0x76, 0xf2, 0x91, 0xbd, 0x01, 0xf5, 0x11, 0xc1, 0x74, 0x99,
	0x7b, 0x21, 0x4b, 0xe6, 0xcc, 0xbe, 0x35, 0xd9, 0x4d, 0x08, 0x9d, 0x7d, 0x19, 0xaa, 0x01, 0x3b,
	0x8e, 0xba, 0xc8, 0x60, 0xb2, 0x1f, 0x15, 0x01, 0x78, 0xcf, 0xdb, 0x67, 0xce, 0x7f, 0x5a, 0x60,
	0x3f, 0x9a, 0x9c, 0xf8, 0x81, 0xc9, 0x4e, 0xe4, 0xbd, 0xdf, 0xef, 0x26, 0x3c, 0x9d, 0x17, 0xdf,
	0x0f, 0xfc, 0xbe, 0x20, 0xe7, 0x05, 0xbd, 0x83, 0x70, 0xdc, 0x8d, 0xf9, 0x5a, 0x21, 0xc0, 0x16,
	0x36, 0x62, 0x3f, 0x8d, 0xb7, 0x48, 0x08, 0x99, 0x77, 0x15, 0x6a, 0xb2, 0xa7, 0xc6, 0x5e, 0x20,
	0xd0, 0x53, 0x72, 0x57, 0x8d, 0x17, 0x45, 0x31, 0x5f, 0x71, 0xbc, 0xdd, 0xc8, 0xef, 0x6b, 0xe3,
	0x61, 0x73, 0x45, 0x1f, 0x4f, 0x20, 0x38, 0x5f, 0x86, 0xe5, 0xe9, 0xb5, 0x0b, 0xc6, 0x6f, 0x65,
	0x32, 0xfe, 0xe5, 0x2c, 0xc6, 0x67, 0x10, 0x38, 0x3b, 0xf7, 0x6f, 0xc2, 0xc2, 0x6e, 0xd8, 0xf7,
	0x4e, 0x28, 0x52, 0x9b, 0x6d, 0x13, 0xfe, 0xdc, 0x82, 0xa6, 0x86, 0x2c, 0xbc, 0xd1, 0x29, 0xd6,
	0x32, 0x2d, 0xcf, 0x85, 0x4c, 0x79, 0x56, 0x26, 0xa3, 0x98, 0x6b, 0x32, 0x4a, 0x79, 0x7b, 0x51,
	0x3e, 0x4d, 0xd2, 0xe7, 0xb2, 0x24, 0xfd, 0x0b, 0xe6, 0xa2, 0xf9, 0xc8, 0xbe, 0x93, 0x69, 0x5b,
	0xaf, 0x67, 0xb1, 0x3b, 0xc5, 0x01, 0xd3, 0xbc, 0x7e, 0x14, 0x96, 0x70, 0x3b, 0xce, 0xcc, 0xd3,
	0xff, 0xb2, 0xe0, 0x42, 0xba, 0x07, 0x32, 0x36, 0x25, 0xaa, 0x56, 0x96, 0xa8, 0xe6, 0x30, 0xae,
	0x05, 0xc5, 0x3d, 0xa6, 0x98, 0x26, 0xfe, 0xc4, 0x70, 0x70, 0xcc, 0x38, 0x0b, 0xa2, 0xae, 0x68,
	0x21, 0xae, 0x81, 0x04, 0xdd, 0x61, 0x68, 0x84, 0x0f, 0x03, 0x7f, 0xff, 0x80, 0xda, 0x49, 0xc2,
	0xab, 0x04, 0x11, 0xcd, 0x09, 0xc3, 0xe7, 0x0d, 0x86, 0x6b, 0x5b, 0x54, 0x31, 0xb7, 0xe8, 0x06,
	0xb4, 0x9e, 0xf8, 0xe3, 0x68, 0xe2, 0x09, 0x86, 0x3e, 0x61, 0x48, 0xb6, 0x8a, 0x28, 0x0b, 0x12,
	0x7e, 0xd7, 0x7f, 0xc2, 0xee, 0x30, 0xe6, 0xec, 0x64, 0xb0, 0x8c, 0x8f, 0xec, 0x4f, 0x41, 0x49,
	0xdb, 0x89, 0x1b, 0xb9, 0x82, 0x9f, 0xde, 0x0e, 0xec, 0xe5, 0x6c, 0xc1, 0xc2, 0xbd, 0xe4, 0x00,
	0x72, 0xaa, 0x63, 0xa0, 0x03, 0x0c, 0xfa, 0x18, 0xf2, 0x5b, 0x55, 0x84, 0xa0, 0x97, 0xf9, 0x97,
	0x02, 0x34, 0x35, 0x5a, 0xb8, 0x39, 0x97, 0xa1, 0xda, 0xc7, 0x23, 0x83, 0x3f, 0x54, 0x9e, 0xb0,
	0x22, 0x00, 0xe8, 0xb1, 0x34, 0xa6, 0x14, 0x4c, 0xa6, 0xe4, 0x6f, 0x59, 0xc2, 0xe0, 0x92, 0xc1,
	0x60, 0xb9, 0x95, 0xe5, 0xdc, 0xad, 0x9c, 0x3b, 0x65, 0x2b, 0xe7, 0xd3, 0x5b, 0xf9, 0x02, 0xd4,
	0x8f, 0xd8, 0x98, 0x1d, 0x85, 0x83, 0x3d, 0x44, 0xa0, 0x7d, 0xab, 0x29, 0x98, 0x40, 0xb9, 0x09,
	0x8b, 0x7e, 0x10, 0xb1, 0xb1, 0xd7, 0x8b, 0xba, 0xfb, 0xde, 0x50, 0xdf, 0xbc, 0xa6, 0x6a, 0xd8,
	0xf4, 0x86, 0x62, 0xf7, 0x32, 0xf7, 0x19, 0xb2, 0xf6, 0x59, 0xcc, 0x8b, 0xf1, 0x51, 0x38, 0xa6,
	0x79, 0xd5, 0x68, 0x5e, 0x04, 0x11, 0x62, 0xe0, 0x99, 0x3b, 0xc6, 0x47, 0xf6, 0x7d, 0x58, 0xd4,
	0x0f, 0x96, 0xa7, 0x2a, 0x66, 0x6a, 0x93, 0xdc, 0xa6, 0x76, 0x04, 0x45, 0xe5, 0xfc, 0x6b, 0x0b,
	0x5a, 0xeb, 0xb4, 0x15, 0x3b, 0xcc, 0x1b, 0xf7, 0x0e, 0x4e, 0x8f, 0x44, 0x7b, 0xbd, 0x70, 0x12,
	0x44, 0xd2, 0x6e, 0xa9, 0x4f, 0xd3, 0xe8, 0x15, 0xd3, 0x46, 0xef, 0xa9, 0x03, 0x9a, 0x38, 0x06,
	0x99, 0xcb, 0x89, 0x41, 0xe6, 0xcd, 0x18, 0xe4, 0xb7, 0x2d, 0x68, 0xc8, 0xf5, 0x48, 0xa7, 0xa9,
	0x4d, 0xd9, 0x32, 0xa7, 0xac, 0xa2, 0x8d, 0x82, 0x16, 0x6d, 0xe4, 0xc7, 0x64, 0xda, 0xa9, 0x5b,
	0x5b, 0x84, 0x3a, 0x75, 0xe3, 0x5c, 0xd3, 0xa6, 0xbd, 0x3c, 0x65, 0xda, 0x1d, 0x9e, 0x66, 0x37,
	0x1f, 0x99, 0xce, 0xc8, 0x32, 0x9d, 0x91, 0x7d, 0x3b, 0xce, 0x13, 0xe0, 0x5e, 0x17, 0xf2, 0x83,
	0x0d, 0x63, 0xd9, 0x2a, 0x95, 0x80, 0x9b, 0xfc, 0xf7, 0x96, 0x74, 0xa9, 0xcf, 0x64, 0xa7, 0xc5,
	0x39, 0x96, 0xac, 0xf0, 0x24, 0xd9, 0x6a, 0x82, 0x3c, 0xf8, 0x30, 0xb7, 0xfa, 0x1f, 0x54, 0x90,
	0x64, 0xee, 0xf7, 0x0b, 0x50, 0xc7, 0xe0, 0xc3, 0xdc, 0xf4, 0x9a, 0x80, 0xad, 0xc9, 0x15, 0x18,
	0xf1, 0x50, 0x21, 0x15, 0x0f, 0xfd, 0x50, 0x12, 0xf0, 0x12, 0x2c, 0x48, 0xde, 0xa8, 0xe1, 0x49,
	0x06, 0x1a, 0x04, 0x55, 0x13, 0x48, 0x39, 0xb2, 0xb9, 0xb4, 0x23, 0x73, 0x7e, 0x3e, 0x73, 0xc3,
	0x4e, 0x93, 0x95, 0xcd, 0x2c, 0x59, 0xc9, 0x8f, 0x8f, 0xf2, 0x05, 0xe6, 0xfb, 0xc2, 0x2a, 0x90,
	0xd4, 0x9e, 0xc5, 0x5b, 0xcc, 0x0e, 0xea, 0xaf, 0x00, 0x8c, 0xbd, 0x60, 0x9f, 0xd1, 0x21, 0x43,
	0xca, 0x0b, 0x42, 0xf0, 0x90, 0xf1, 0xa1, 0xc9, 0xcb, 0x2f, 0x5a, 0xb0, 0x68, 0x2c, 0x0a, 0xdd,
	0x56, 0x7a, 0x53, 0xad, 0xe9, 0x4d, 0x7d, 0x9a, 0x13, 0x5c, 0x8e, 0xf3, 0x72, 0xfe, 0x64, 0x8a,
	0xb3, 0x74, 0x6a, 0x57, 0x64, 0xac, 0x3c, 0x32, 0x85, 0x74, 0x84, 0x9d, 0x88, 0x41, 0x31, 0x25,
	0x06, 0x0f, 0x60, 0x49, 0x99, 0x21, 0x5d, 0x1c, 0x4a, 0x28, 0x0e, 0x2f, 0x65, 0x9a, 0x8e, 0x34,
	0x5b, 0xdc, 0xc5, 0x9e, 0x0e, 0x42, 0xa1, 0xf8, 0x04, 0xd4, 0x37, 0xbc, 0x93, 0xdd, 0x31, 0x0b,
	0xfa, 0xc8, 0xb9, 0x16, 0x14, 0xfb, 0xde, 0x89, 0x3a, 0xdc, 0xf5, 0xbd, 0x93, 0xbc, 0xd9, 0x3a,
	0xff, 0x64, 0x41, 0x0b, 0x33, 0x41, 0xf1, 0x38, 0xa7, 0x46, 0xc9, 0x3f, 0x74, 0x4e, 0x21, 0x3f,
	0x52, 0x7e, 0x19, 0x9a, 0x32, 0xb6, 0x09, 0x83, 0xf0, 0xa8, 0xfb, 0x41, 0xf8, 0x01, 0x0a, 0x56,
	0xc1, 0x6d, 0x50, 0x80, 0x23, 0xa0, 0x9f, 0x0f, 0x3f, 0x38, 0x73, 0xe4, 0xfc, 0x9a, 0x5c, 0xdc,
	0x56, 0xe0, 0x47, 0xc8, 0xbb, 0x99, 0xc1, 0xed, 0x57, 0x2b, 0x69, 0x74, 0x3e, 0x12, 0x3a, 0x40,
	0x79, 0x34, 0x2d, 0xae, 0xad, 0x22, 0x44, 0xad, 0x45, 0x51, 0x2b, 0xe4, 0x5a, 0xe9, 0xe2, 0x94,
	0x95, 0xd6, 0x72, 0x85, 0x74, 0x52, 0x27, 0x92, 0x2a, 0xdb, 0xa8, 0x12, 0x45, 0x65, 0x23, 0x51,
	0x24, 0x6c, 0x13, 0x67, 0xe3, 0x27, 0x6c, 0x4c, 0xfa, 0x20, 0x83, 0x29, 0x02, 0x29, 0x2f, 0x17,
	0x89, 0x00, 0xb3, 0x6b, 0x84, 0xbf, 0xb5, 0x28, 0x09, 0x3a, 0xed, 0x57, 0xa1, 0x75, 0xc2, 0x78,
	0xc4, 0xc6, 0x1a, 0x1a, 0x05, 0x55, 0xcd, 0x18, 0x2e, 0x51, 0x6f, 0xc2, 0x62, 0x74, 0xe0, 0xf3,
	0xae, 0x91, 0x2f, 0x97, 0x81, 0x95, 0x68, 0xd0, 0x22, 0x18, 0x81, 0x3b, 0xf0, 0x78, 0x64, 0xe2,
	0x52, 0x64, 0xd5, 0x14, 0x0d, 0x3a, 0xee, 0x4f, 0x42, 0x45, 0x0c, 0x8e, 0x92, 0x5f, 0xcb, 0xcf,
	0x0a, 0xe9, 0x12, 0xed, 0xce, 0xf7, 0xbd, 0x13, 0x3c, 0x1e, 0x6e, 0xa6, 0x8e, 0x3e, 0x75, 0x24,
	0xf0, 0x62, 0x16, 0x81, 0xb4, 0x5c, 0x1b, 0x67, 0x1f, 0x5d, 0x50, 0x7b, 0x1e, 0x3f, 0x68, 0x37,
	0xae, 0x59, 0x37, 0xac, 0x18, 0x65, 0xdd, 0xe3, 0x07, 0x42, 0x95, 0x31, 0xa0, 0xc4, 0xf6, 0x05,
	0x6c, 0xaf, 0x08, 0x80, 0x6a, 0xc4, 0x10, 0x12, 0x1b, 0x9b, 0xd4, 0x28, 0x00, 0xd8, 0xb8, 0x0a,
	0x62, 0xc2, 0x28, 0xc0, 0x2d, 0x14, 0xe0, 0xb9, 0xbe, 0x77, 0x22, 0x24, 0xf7, 0x32, 0x50, 0xac,
	0x8e, 0x4d, 0x8b, 0xd8, 0x54, 0x41, 0x80, 0x68, 0xc4, 0x60, 0xd7, 0x1b, 0x44, 0x07, 0xdd, 0x27,
	0xde, 0x60, 0xc2, 0xda, 0x36, 0x6e, 0x7f, 0x8d, 0x60, 0x0f, 0x05, 0x08, 0x33, 0x5b, 0x98, 0xf1,
	0x54, 0xf9, 0xd0, 0xf6, 0x12, 0x9a, 0x98, 0x06, 0x66, 0x3e, 0x15, 0x50, 0x48, 0x8a, 0xe0, 0xba,
	0x9a, 0xc3, 0x05, 0x1c, 0x08, 0x24, 0x48, 0x6a, 0x50, 0x5a, 0xd3, 0x96, 0xb3, 0x34, 0xed, 0x16,
	0xac, 0x68, 0xfb, 0xca, 0x05, 0x37, 0x46, 0x6c, 0xec, 0x87, 0xfd, 0xf6, 0x0a, 0x6e, 0xee, 0x52,
	0xbc, 0xb9, 0x3b, 0xde, 0x90, 0xbd, 0x87, 0x4d, 0xf6, 0x06, 0x34, 0xb0, 0x53, 0xbc, 0xcb, 0xab,
	0x67, 0xdc, 0x65, 0x9c, 0xf4, 0x86, 0xdc, 0xe9, 0x1b, 0xd0, 0xf2, 0x26, 0x7d, 0x3f, 0xec, 0x3e,
	0xf6, 0xb8, 0xdf, 0x23, 0x3e, 0xb7, 0x91, 0xcf, 0x0b, 0x08, 0xbf, 0x2d, 0xc0, 0xc8, 0xed, 0x18,
	0x93, 0x1d, 0x47, 0x63, 0x8f, 0x30, 0x2f, 0x6a, 0x98, 0x1d, 0x01, 0x56, 0x98, 0x72, 0x25, 0xfe,
	0xb1, 0x92, 0xd2, 0x4b, 0x14, 0xff, 0x23, 0x7c, 0xc7, 0x3f, 0x26, 0x21, 0x75, 0x5e, 0x85, 0x86,
	0x94, 0x1f, 0xd6, 0x3b, 0x9c, 0x6d, 0x37, 0xbe, 0x6f, 0x19, 0xb8, 0x7c, 0x64, 0x5f, 0x87, 0xc6,
	0x81, 0xc7, 0xbb, 0xde, 0x70, 0xc2, 0xd9, 0x90, 0xc9, 0x48, 0xa7, 0xe2, 0xd6, 0x0f, 0x3c, 0xbe,
	0xa6, 0x60, 0x38, 0x97, 0xc9, 0x20, 0xf2, 0xf5, 0x8b, 0x03, 0xb2, 0x21, 0x0b, 0x08, 0x4f, 0x6e,
	0x0d, 0xe2, 0xf5, 0x69, 0x98, 0xe4, 0xac, 0x69, 0x7d, 0x09, 0xe6, 0x75, 0x68, 0x10, 0x4d, 0x65,
	0x41, 0xc8, 0xba, 0xd4, 0x11, 0xb8, 0x26, 0xcd, 0xc8, 0x75, 0x68, 0x10, 0x39, 0xd3, 0xcc, 0xd4,
	0x11, 0x28, 0x91, 0x9c, 0x75, 0x58, 0xa6, 0x35, 0xc9, 0x48, 0x04, 0xc5, 0x66, 0x76, 0xac, 0x21,
	0xbd, 0x0e, 0x39, 0x18, 0xf1, 0xa7, 0xf3, 0xc7, 0x56, 0x26, 0x15, 0x0a, 0x96, 0x78, 0xe4, 0x45,
	0xc6, 0x91, 0x54, 0x00, 0x4e, 0x39, 0x92, 0xde, 0x84, 0x45, 0xbc, 0x0c, 0x1b, 0xb3, 0x6e, 0xd2,
	0x9d, 0xfc, 0x7b, 0x53, 0x36, 0xec, 0x28, 0x2a, 0xaf, 0x80, 0x02, 0x75, 0x4d, 0x77, 0xb3, 0x20,
	0xc1, 0x72, 0x52, 0x62, 0xde, 0x89, 0xa7, 0x11, 0x7f, 0x3a, 0x1b, 0x72, 0xda, 0x1b, 0xde, 0xc9,
	0x3a, 0xe2, 0xfa, 0x3c, 0x0c, 0xc4, 0xe2, 0x2f, 0x42, 0x85, 0xf6, 0x20, 0xb5, 0xfa, 0xad, 0xac,
	0xd5, 0xff, 0xb2, 0x05, 0x8b, 0x06, 0x05, 0x75, 0x18, 0x37, 0x57, 0x5e, 0xd2, 0x56, 0x7e, 0x05,
	0x40, 0xb3, 0xcb, 0x05, 0x6c, 0xad, 0x26, 0x16, 0xf9, 0x25, 0x50, 0x73, 0x57, 0x28, 0x45, 0x44,
	0x69, 0x48, 0xe8, 0x56, 0x7c, 0x0c, 0x4f, 0x2d, 0xe8, 0x5f, 0x0b, 0xb0, 0x92, 0xb5, 0xa2, 0xac,
	0x9d, 0xd0, 0xe7, 0x83, 0x21, 0x18, 0x0d, 0x18, 0xa7, 0x1b, 0x4a, 0x22, 0x04, 0x43, 0x18, 0xa2,
	0xbc, 0x0f, 0x17, 0x50, 0xce, 0x51, 0xdb, 0x7b, 0x31, 0x6d, 0x9c, 0x59, 0x4e, 0x4c, 0x33, 0xc5,
	0x14, 0xd7, 0x46, 0x12, 0x06, 0x5c, 0x10, 0x3e, 0x11, 0xb1, 0x70, 0x9a, 0x70, 0xe9, 0x5c, 0x84,
	0x91, 0x84, 0x49, 0xf8, 0x11, 0x2c, 0xcb, 0xa3, 0x7d, 0x8a, 0x72, 0xf9, 0x3c, 0x94, 0x97, 0x88,
	0x86, 0xd1, 0xe0, 0x1c, 0x43, 0x53, 0x4a, 0x95, 0xeb, 0x05, 0x87, 0x5b, 0x11, 0x1b, 0xda, 0x17,
	0xa0, 0xec, 0x0d, 0x7c, 0x8f, 0xcb, 0xd8, 0x81, 0x3e, 0x30, 0x4a, 0xf4, 0x7b, 0x87, 0xc6, 0x39,
	0x47, 0x00, 0x54, 0x50, 0x91, 0x13, 0x3a, 0xe4, 0x86, 0x4e, 0xce, 0x00, 0x16, 0xb4, 0x91, 0x85,
	0xac, 0xbe, 0xa3, 0x85, 0xcf, 0xc2, 0x62, 0x88, 0xf1, 0x17, 0xde, 0xba, 0x92, 0xb5, 0x3a, 0x57,
	0x85, 0xfc, 0x49, 0x74, 0x2d, 0x23, 0x91, 0xec, 0xe0, 0xc6, 0xf1, 0xcc, 0xd1, 0x28, 0xfd, 0xa1,
	0x46, 0x1b, 0x7b, 0xc1, 0xe1, 0xa9, 0xe9, 0x8f, 0x14, 0x9b, 0x84, 0xfa, 0xc6, 0x00, 0x8c, 0x69,
	0xbf, 0x08, 0x8b, 0x6e, 0x18, 0x0e, 0x25, 0xde, 0xa9, 0xc6, 0x67, 0xf6, 0x41, 0x47, 0x2a, 0x67,
	0x31, 0x51, 0xce, 0xbf, 0xb1, 0xa6, 0xe8, 0xff, 0x6f, 0x37, 0x4b, 0xe6, 0x1a, 0xe7, 0x52, 0x6b,
	0x74, 0x7e, 0x16, 0x96, 0x37, 0x59, 0x44, 0xd7, 0xba, 0x78, 0x5c, 0x3d, 0xf5, 0x36, 0x31, 0x3e,
	0xa6, 0x15, 0x72, 0x8e, 0x69, 0x45, 0xf3, 0x98, 0xf6, 0x79, 0x69, 0x16, 0x89, 0xfc, 0x8e, 0x3f,
	0x1c, 0x0d, 0x98, 0x3a, 0x6f, 0xa4, 0x2e, 0x93, 0xb2, 0x12, 0x38, 0x36, 0x94, 0xf0, 0x5e, 0x41,
	0x5e, 0x21, 0x89, 0xbf, 0x9d, 0x87, 0x99, 0x93, 0xe6, 0x23, 0xfb, 0xd3, 0x46, 0x42, 0xf5, 0xd5,
	0xdc, 0xf8, 0x2e, 0x3d, 0x17, 0x99, 0x51, 0x6d, 0x42, 0x43, 0xd1, 0xdd, 0xea, 0xbb, 0xec, 0x03,
	0xc7, 0x37, 0x00, 0x74, 0xb2, 0x3b, 0x77, 0x7e, 0xe5, 0x45, 0x58, 0x20, 0xf3, 0xcf, 0x0f, 0x84,
	0xf9, 0x88, 0x73, 0x2c, 0x75, 0x84, 0xee, 0x08, 0xe0, 0x56, 0xdf, 0x79, 0x07, 0xec, 0xce, 0xb1,
	0x37, 0x7c, 0xe0, 0xf7, 0xe5, 0x70, 0x69, 0x1e, 0x55, 0x89, 0x47, 0xf9, 0xea, 0xf5, 0x09, 0xb8,
	0xec, 0xb2, 0x5e, 0x38, 0x1c, 0xb2, 0xa0, 0x2f, 0x48, 0xe1, 0xdd, 0xb5, 0x8c, 0x44, 0xb8, 0x79,
	0xbf, 0x2d, 0xe7, 0x2e, 0xef, 0xb7, 0x85, 0x19, 0xb8, 0x3f, 0x62, 0x81, 0x8c, 0x7a, 0xc5, 0xee,
	0x9f, 0x63, 0xdc, 0xcc, 0x5b, 0xc5, 0x4b, 0x24, 0x0f, 0x3b, 0xfe, 0x97, 0xd4, 0x59, 0x25, 0xfe,
	0x76, 0xbe, 0x66, 0xc1, 0xfc, 0xae, 0xb7, 0x8f, 0xeb, 0xbb, 0x00, 0xe5, 0xc8, 0xdb, 0x7f, 0xbc,
	0xaf, 0xec, 0x1c, 0x7e, 0x60, 0xe9, 0x84, 0xb7, 0xbf, 0xcb, 0x8e, 0x63, 0x5e, 0xca, 0x4f, 0x89,
	0xbf, 0xa5, 0x58, 0x48, 0x1f, 0x9a, 0x88, 0x6f, 0xa9, 0xe0, 0x25, 0x01, 0xd8, 0xd7, 0x40, 0x3f,
	0x48, 0x66, 0x25, 0xf1, 0xbe, 0x00, 0x4d, 0x63, 0xfd, 0x7c, 0x64, 0x7f, 0xd4, 0x90, 0xa4, 0xcb,
	0x99, 0x97, 0x24, 0xb4, 0x06, 0x92, 0x1d, 0x9c, 0x59, 0x18, 0x79, 0x03, 0xc9, 0x1d, 0xfa, 0x70,
	0x7e, 0x45, 0x18, 0x0c, 0xb5, 0x29, 0x0f, 0x38, 0x1b, 0xf3, 0xf3, 0x72, 0xd7, 0x58, 0x5b, 0x31,
	0xbd, 0x36, 0xc5, 0xfb, 0x52, 0x0e, 0xef, 0xcb, 0x29, 0xde, 0x1f, 0x40, 0x45, 0xcc, 0x22, 0x47,
	0xff, 0xf2, 0x65, 0xf8, 0x12, 0xc4, 0x8e, 0x46, 0x5d, 0x38, 0xc6, 0x8e, 0x27, 0xf6, 0x55, 0x25,
	0xcd, 0x57, 0x39, 0x47, 0xb0, 0x48, 0x59, 0xae, 0x7b, 0x22, 0x8a, 0xbc, 0xeb, 0x9d, 0x0c, 0xbd,
	0xe0, 0x19, 0x2e, 0xbc, 0x0d, 0xf3, 0x87, 0xec, 0xe4, 0x28, 0x1c, 0xab, 0xd2, 0x02, 0xf5, 0xe9,
	0xfc, 0x0c, 0xd8, 0x69, 0x8e, 0xf3, 0x91, 0xfd, 0x86, 0xb1, 0x9f, 0xcf, 0x65, 0xed, 0xa7, 0x62,
	0xcc, 0xcc, 0x0d, 0xfd, 0x37, 0x0b, 0x96, 0x0d, 0x2d, 0xbb, 0xed, 0x71, 0x76, 0x5e, 0x55, 0x15,
	0x2d, 0xa3, 0xc9, 0x78, 0x14, 0x72, 0x16, 0x5b, 0x4b, 0xfa, 0x3c, 0x45, 0x94, 0x9f, 0x07, 0xe8,
	0x4d, 0xc6, 0x63, 0x16, 0x44, 0xbb, 0xde, 0xbe, 0xaa, 0x78, 0x4a, 0x20, 0x82, 0x2e, 0x3b, 0xf6,
	0x86, 0xa2, 0x91, 0x2c, 0xbd, 0xfa, 0x14, 0x1b, 0xc8, 0xc8, 0xbc, 0xf0, 0xf6, 0xfc, 0xb5, 0xa2,
	0x10, 0x0a, 0xf5, 0x4d, 0x02, 0xc3, 0x39, 0x32, 0xb3, 0xa2, 0xb2, 0xa7, 0xf4, 0xed, 0xfc, 0x14,
	0xd4, 0x3a, 0x44, 0x02, 0x17, 0xb9, 0x02, 0x73, 0xa2, 0xdb, 0x96, 0x12, 0x1b, 0xf9, 0x25, 0x16,
	0x1f, 0x79, 0xfb, 0x52, 0x6a, 0xc4, 0x9f, 0x22, 0x08, 0xa4, 0x9a, 0x99, 0x60, 0x32, 0x7c, 0xcc,
	0xc6, 0x72, 0x9d, 0x35, 0x84, 0x6d, 0x23, 0xc8, 0xf9, 0x3c, 0xd8, 0x9b, 0x2c, 0x92, 0xe4, 0xc9,
	0xef, 0xf0, 0x91, 0x38, 0xe8, 0x33, 0xb4, 0x5e, 0xf1, 0x6e, 0x5d, 0xcd, 0xda, 0x2d, 0x6d, 0x56,
	0x6e, 0xdc, 0xc1, 0xf9, 0xad, 0x02, 0xb4, 0x31, 0x17, 0x2a, 0x9a, 0xb5, 0xb3, 0x6d, 0xfe, 0x0e,
	0x3d, 0x2b, 0x81, 0xd7, 0x79, 0x5f, 0x36, 0x79, 0x6f, 0x0b, 0xd9, 0x0b, 0x0e, 0x65, 0xb6, 0x09,
	0xff, 0x16, 0xfb, 0xfc, 0x98, 0xed, 0xfb, 0x81, 0xf0, 0xee, 0x32, 0xb1, 0x99, 0x00, 0x90, 0x16,
	0x25, 0x45, 0x65, 0x5d, 0x59, 0x9c, 0x23, 0x25, 0xd3, 0x88, 0xd3, 0xaa, 0xc6, 0xa6, 0x11, 0x67,
	0xe5, 0x40, 0xdd, 0xe3, 0x9c, 0x71, 0x2e, 0x82, 0x86, 0x09, 0x97, 0xe5, 0x64, 0x06, 0xcc, 0xf9,
	0x23, 0x0b, 0xae, 0xc6, 0xec, 0x49, 0x4a, 0xc0, 0xa8, 0xf4, 0xca, 0xa7, 0xd3, 0xca, 0x34, 0x97,
	0x9e, 0x57, 0x17, 0x04, 0x28, 0x3d, 0xb2, 0xb2, 0x31, 0x81, 0xe8, 0x72, 0x5e, 0x34, 0xe5, 0xbc,
	0x03, 0x35, 0x0c, 0xa5, 0xe5, 0x94, 0x4a, 0x18, 0x4c, 0x5e, 0xcf, 0x4d, 0x60, 0x8b, 0x59, 0x11,
	0xaa, 0xab, 0xf7, 0x73, 0x1c, 0xb8, 0x36, 0x7b, 0xd6, 0x7c, 0xe4, 0xbc, 0x07, 0xf5, 0x18, 0x87,
	0x02, 0xd9, 0x92, 0x1f, 0xec, 0x85, 0xb8, 0x8e, 0xda, 0x5b, 0xaf, 0xcd, 0x1c, 0x33, 0x25, 0x28,
	0x2e, 0xf6, 0x14, 0xd1, 0x80, 0x46, 0x91, 0x8f, 0x9c, 0xeb, 0xd0, 0xdc, 0x64, 0x91, 0x31, 0xca,
	0x14, 0xb3, 0x9c, 0x00, 0x5a, 0x26, 0x12, 0x45, 0x0d, 0xd2, 0xd5, 0xca, 0x33, 0x7d, 0x5c, 0x59,
	0xa6, 0x66, 0x59, 0x78, 0xea, 0x59, 0x8e, 0xa1, 0xad, 0x8f, 0x77, 0x27, 0x1c, 0x23, 0x87, 0x70,
	0xdc, 0x77, 0x0c, 0xa3, 0x77, 0x4e, 0xea, 0x33, 0x8c, 0xe0, 0xab, 0x18, 0x7f, 0xa5, 0xb7, 0x2c,
	0x93, 0x1d, 0x6f, 0xc0, 0x4a, 0x16, 0x2a, 0x1f, 0x09, 0x53, 0xc2, 0x49, 0x2c, 0x04, 0x7a, 0xd9,
	0x95, 0x5f, 0xce, 0xeb, 0x50, 0xd9, 0xf5, 0xf6, 0x39, 0x6a, 0xec, 0x02, 0x14, 0x62, 0x0f, 0x55,
	0xc8, 0x0e, 0x10, 0x9d, 0xbb, 0x00, 0x3b, 0x93, 0xc7, 0x43, 0x4a, 0xbe, 0x2a, 0x43, 0x24, 0x9d,
	0x5a, 0xa4, 0x69, 0x5f, 0x41, 0xd3, 0x3e, 0xad, 0x6e, 0xb0, 0x68, 0xd6, 0x0d, 0x9e, 0x68, 0x9b,
	0x8e, 0x04, 0xdf, 0x80, 0x52, 0xe4, 0xed, 0xf3, 0x59, 0x8e, 0x43, 0x4d, 0xd7, 0x45, 0x4c, 0xfb,
	0x63, 0x30, 0xc7, 0x71, 0x42, 0x72, 0x57, 0x9f, 0xcf, 0xea, 0x93, 0x4c, 0xd9, 0x95, 0xd8, 0x4e,
	0x17, 0x6a, 0x38, 0x34, 0x35, 0x65, 0xe8, 0xa1, 0x66, 0x64, 0x53, 0x6b, 0x2b, 0x66, 0xaf, 0xad,
	0x64, 0xae, 0xed, 0x37, 0x2d, 0x78, 0x41, 0xdb, 0x66, 0xe1, 0xc6, 0xc6, 0x7d, 0x25, 0x30, 0xa8,
	0x52, 0xe7, 0x0d, 0x4e, 0x34, 0x2f, 0x5c, 0x34, 0xbc, 0xf0, 0xb9, 0x03, 0x93, 0x9f, 0x83, 0xe5,
	0xa9, 0xa9, 0x21, 0xff, 0x9f, 0x07, 0x20, 0xfe, 0xec, 0xaa, 0xd3, 0x55, 0xc3, 0xd5, 0x20, 0xf6,
	0x6d, 0x28, 0xfb, 0x11, 0x1b, 0x72, 0x79, 0x3b, 0x76, 0x3e, 0x21, 0xa7, 0xae, 0xce, 0x97, 0xc1,
	0x39, 0x8d, 0x2f, 0x67, 0x3b, 0x5c, 0x64, 0x2e, 0x41, 0xaa, 0x92, 0xf0, 0x28, 0xf2, 0x46, 0x47,
	0x15, 0x74, 0xc5, 0x15, 0x4a, 0xd7, 0xa1, 0x89, 0x81, 0xfa, 0x03, 0xbf, 0x7f, 0xcf, 0xf3, 0xb3,
	0xcd, 0xb0, 0xf3, 0xff, 0xa1, 0x65, 0x22, 0xf1, 0x91, 0xbd, 0x2a, 0x2c, 0x4b, 0x77, 0xe8, 0xf9,
	0x81, 0x72, 0xc8, 0x3e, 0x17, 0x8d, 0xce, 0x3e, 0x5c, 0xc4, 0xa4, 0x21, 0x66, 0x31, 0x29, 0xc3,
	0x93, 0x14, 0x10, 0x4e, 0x6f, 0xb1, 0x9e, 0xa2, 0x2a, 0x98, 0x29, 0xaa, 0x2b, 0x00, 0x27, 0xcc,
	0x1b, 0x53, 0x32, 0x57, 0x05, 0x62, 0x02, 0x82, 0x09, 0x5c, 0xe7, 0x37, 0xe6, 0xe1, 0x52, 0xde,
	0x48, 0x74, 0x13, 0xa2, 0xf5, 0xb6, 0x52, 0xbd, 0xc5, 0xd9, 0x48, 0xbb, 0x2d, 0xd8, 0x63, 0x2a,
	0x59, 0x54, 0x8f, 0xaf, 0x0a, 0xee, 0x30, 0x86, 0xf5, 0xb7, 0x49, 0x3e, 0x59, 0x60, 0x51, 0x06,
	0xab, 0x1e, 0xe7, 0x91, 0x65, 0x49, 0xc7, 0xf4, 0xcd, 0x43, 0x09, 0x11, 0xa7, 0x6e, 0x1e, 0xde,
	0x84, 0xe5, 0x29, 0xdc, 0x6e, 0x2f, 0x38, 0x91, 0xa7, 0x03, 0x3b, 0x85, 0xbf, 0x1e, 0x9c, 0x60,
	0xf9, 0xfa, 0x98, 0xe1, 0x3d, 0xf3, 0x30, 0x0c, 0xd8, 0x09, 0xba, 0xf3, 0x92, 0x5b, 0x97, 0xc0,
	0x7b, 0x02, 0x26, 0xe6, 0x60, 0x20, 0x21, 0xcd, 0x79, 0x59, 0xab, 0xad, 0x21, 0x0a, 0x82, 0x2b,
	0x30, 0x37, 0x66, 0x43, 0x6f, 0x7c, 0x28, 0x83, 0x2e, 0xf9, 0x85, 0x17, 0x08, 0xe3, 0xf0, 0xa8,
	0x8b, 0x05, 0xd3, 0xe4, 0xe4, 0x2b, 0x02, 0xe0, 0x7a, 0x11, 0xb3, 0xbf, 0x98, 0xba, 0xc9, 0x00,
	0x14, 0xc6, 0x4f, 0x66, 0x0a, 0x63, 0xee, 0xae, 0xc4, 0xf7, 0x83, 0x11, 0x1b, 0x1a, 0xf7, 0x1b,
	0x97, 0xfe, 0xa2, 0x08, 0x35, 0xad, 0x51, 0x6c, 0x5f, 0x7c, 0xf5, 0x98, 0xbe, 0xd5, 0xdb, 0xea,
	0xdb, 0xaf, 0x81, 0xad, 0x9a, 0xe5, 0x65, 0x5e, 0x22, 0x40, 0x2d, 0xd9, 0xb2, 0x41, 0x0d, 0x5b,
	0x67, 0xba, 0xe5, 0xbb, 0x1a, 0x9f, 0xd5, 0xba, 0xc2, 0xd4, 0xc9, 0xe2, 0x45, 0x09, 0x12, 0xb1,
	0xd4, 0xb4, 0xc0, 0x94, 0x33, 0x04, 0xe6, 0x15, 0x68, 0x72, 0x16, 0x45, 0x03, 0xcc, 0x99, 0x13,
	0x23, 0x29, 0xf8, 0x5a, 0x48, 0xc0, 0xc8, 0xce, 0x4c, 0x99, 0x99, 0xcf, 0x96, 0x19, 0x5d, 0x47,
	0x2a, 0xa6, 0x8e, 0xdc, 0x80, 0x16, 0x35, 0x69, 0x5c, 0xa0, 0xa2, 0x7f, 0x3a, 0xfa, 0x27, 0x3c,
	0x98, 0x16, 0x65, 0xc8, 0x10, 0x65, 0x43, 0x04, 0x6a, 0x29, 0x11, 0xc8, 0xb8, 0xc7, 0xac, 0x67,
	0xdd, 0x63, 0xfe, 0x9e, 0x05, 0x2b, 0x5b, 0x5a, 0x29, 0xd3, 0x8f, 0xaa, 0xf0, 0x2e, 0xaf, 0x70,
	0xee, 0x3a, 0x34, 0xf0, 0x22, 0xac, 0x3f, 0xa1, 0x30, 0x4d, 0xde, 0x18, 0xd6, 0x05, 0x70, 0x43,
	0xc2, 0x9c, 0xf7, 0x61, 0x79, 0x7a, 0x8e, 0x2e, 0x1f, 0xd9, 0x9f, 0x31, 0xac, 0xed, 0xcd, 0x2c,
	0x01, 0xcf, 0x5e, 0x9c, 0xcc, 0xe5, 0x3c, 0x84, 0x4b, 0x7a, 0xbb, 0xa6, 0x07, 0xa7, 0xa4, 0xe4,
	0xa7, 0x4b, 0xe5, 0x1a, 0x7a, 0xa9, 0xdc, 0x5f, 0x59, 0x70, 0x39, 0x97, 0x30, 0x26, 0x0e, 0x2e,
	0xe0, 0xaa, 0xd5, 0x45, 0x59, 0xe4, 0x0d, 0x70, 0x9b, 0x29, 0x49, 0xbe, 0x28, 0xda, 0xee, 0xd1,
	0x65, 0x59, 0xe4, 0x0d, 0xc4, 0x5e, 0xbf, 0x0d, 0xab, 0x5a, 0x07, 0xba, 0x8c, 0x32, 0x52, 0xf9,
	0x17, 0xe2, 0x3e, 0xda, 0x58, 0x76, 0x47, 0xf2, 0xa7, 0x88, 0xfc, 0x79, 0xf3, 0x34, 0xfe, 0x4c,
	0xdb, 0x01, 0x62, 0xd3, 0x9f, 0x15, 0xe0, 0xca, 0x4c, 0xbc, 0x54, 0x79, 0x50, 0x21, 0x5d, 0x1e,
	0x94, 0x2a, 0xff, 0x2d, 0xa6, 0xcb, 0x7f, 0x33, 0x4a, 0x68, 0x4a, 0x59, 0x25, 0x34, 0xaf, 0x40,
	0x53, 0x89, 0xa4, 0xdf, 0x3b, 0xd4, 0xca, 0xad, 0x64, 0xef, 0x6d, 0x09, 0xd5, 0xe6, 0xc3, 0xd9,
	0xb1, 0x4a, 0x68, 0x12, 0x64, 0x87, 0x1d, 0x63, 0xb6, 0x2d, 0x61, 0xa7, 0x2a, 0x0f, 0x2c, 0x91,
	0xd8, 0xc5, 0x0a, 0x26, 0x14, 0x56, 0x60, 0x49, 0xcb, 0x8f, 0x7a, 0x56, 0xc1, 0x9c, 0x29, 0xf6,
	0x96, 0xfb, 0x29, 0xb4, 0xed, 0x2a, 0xd4, 0x34, 0x4c, 0xd4, 0xea, 0x92, 0x0b, 0x09, 0x92, 0xf3,
	0x45, 0x68, 0x75, 0x30, 0xa9, 0x7f, 0x1e, 0xfd, 0x3a, 0xb3, 0x16, 0x39, 0x9f, 0x83, 0xa6, 0x4e,
	0x5e, 0xa8, 0xc6, 0x27, 0x0c, 0xd5, 0xc8, 0xbc, 0xc5, 0x4e, 0xcf, 0x48, 0xee, 0xf6, 0x47, 0xd4,
	0x5c, 0xb5, 0xf7, 0x11, 0xf9, 0xaa, 0xe0, 0xfc, 0x8e, 0x95, 0xc6, 0xe7, 0xa3, 0x0c, 0x0b, 0x4c,
	0x29, 0x6f, 0xd3, 0x02, 0x67, 0x1a, 0xd6, 0x42, 0x76, 0x19, 0x80, 0x59, 0x31, 0x54, 0x9c, 0x55,
	0x31, 0x54, 0x32, 0x5f, 0x47, 0xec, 0xc3, 0x65, 0x9a, 0xdf, 0x7a, 0xe8, 0xf5, 0x0e, 0x18, 0x4f,
	0xd5, 0xc3, 0xce, 0xd0, 0xf2, 0x73, 0x26, 0xb1, 0xbf, 0x6a, 0xc1, 0x73, 0x79, 0x23, 0xe5, 0x27,
	0xb3, 0x51, 0x23, 0x0a, 0x49, 0xe2, 0x5a, 0x65, 0x15, 0x82, 0x54, 0x56, 0x21, 0x48, 0x89, 0x44,
	0x29, 0x2d, 0x12, 0xe4, 0xe3, 0x94, 0x48, 0xf4, 0x66, 0xac, 0x1a, 0xdf, 0x33, 0xe8, 0xe2, 0xf1,
	0x46, 0xbe, 0x78, 0x64, 0x2f, 0x45, 0x8a, 0xca, 0x77, 0x2c, 0x58, 0x51, 0x68, 0xa9, 0x22, 0xc3,
	0x19, 0x6c, 0xbd, 0x0c, 0xd5, 0x9e, 0xa0, 0xaa, 0x95, 0x12, 0x56, 0x10, 0xf0, 0xa1, 0x56, 0x12,
	0x7e, 0xdb, 0x82, 0x25, 0x63, 0xee, 0xd2, 0x94, 0xd9, 0x50, 0xea, 0x0b, 0x4d, 0x27, 0x81, 0xc5,
	0xbf, 0xcd, 0x19, 0x17, 0xa6, 0x67, 0x4c, 0x8d, 0x9a, 0x6d, 0x23, 0x74, 0x65, 0xda, 0xa8, 0x39,
	0xde, 0x54, 0xf5, 0xcc, 0x49, 0x40, 0xb7, 0x53, 0x3b, 0x5b, 0xce, 0xda, 0xd9, 0x39, 0x63, 0x67,
	0xff, 0x23, 0x87, 0xe9, 0xa7, 0x15, 0x0a, 0xbe, 0x9b, 0x55, 0x28, 0xf8, 0xca, 0xac, 0x9d, 0xcf,
	0xa9, 0x14, 0x14, 0xc3, 0x24, 0x3e, 0x8b, 0xa2, 0xec, 0x4a, 0xa4, 0x5c, 0x15, 0x56, 0x0a, 0x89,
	0x46, 0x23, 0xb8, 0xae, 0x21, 0x2c, 0x53, 0x97, 0xcb, 0xb3, 0xf6, 0x78, 0xce, 0xd4, 0xe5, 0xaf,
	0x15, 0xe0, 0xe2, 0x6d, 0x2f, 0xea, 0x1d, 0x6c, 0xb2, 0x68, 0x6d, 0xd2, 0xf7, 0xa3, 0x9d, 0x43,
	0x7f, 0x20, 0x8e, 0x2b, 0x13, 0xc6, 0xa3, 0x53, 0x54, 0x79, 0x4a, 0xd1, 0x84, 0x93, 0x10, 0x34,
	0x54, 0xd9, 0x43, 0x11, 0x9d, 0x84, 0x80, 0xe0, 0x1d, 0xe5, 0x2a, 0xcc, 0x87, 0x7b, 0x7b, 0x5d,
	0xce, 0x22, 0x79, 0x40, 0x9d, 0x0b, 0xf7, 0xf6, 0x76, 0x18, 0xe6, 0x3b, 0xe8, 0x86, 0x84, 0xce,
	0xa7, 0xf4, 0x21, 0x98, 0x42, 0xcf, 0xaa, 0x26, 0xf1, 0x15, 0x5a, 0x05, 0x01, 0x42, 0x46, 0x5e,
	0x03, 0x9b, 0x1a, 0x69, 0x40, 0x1e, 0x4e, 0xc6, 0x3d, 0x95, 0xd2, 0x6b, 0x61, 0x0b, 0xad, 0x06,
	0xe1, 0xc2, 0x2e, 0xea, 0xd8, 0x51, 0x78, 0xc8, 0x02, 0x19, 0xff, 0x37, 0x13, 0xe4, 0x5d, 0x01,
	0x76, 0x7e, 0x60, 0xc1, 0xa5, 0x2c, 0x8e, 0xf0, 0x51, 0x18, 0x70, 0x76, 0x4a, 0x59, 0x41, 0x22,
	0xce, 0x68, 0x8d, 0x9e, 0x2d, 0x43, 0x94, 0x07, 0x9a, 0xcb, 0xf7, 0x40, 0xfa, 0x84, 0xc5, 0x49,
	0x58, 0x9e, 0x82, 0xaf, 0x02, 0x89, 0x4b, 0x97, 0x42, 0x01, 0x62, 0x13, 0x20, 0x68, 0x5d, 0x40,
	0x9c, 0x6f, 0x17, 0xa1, 0x95, 0xee, 0x7b, 0xae, 0xab, 0x0a, 0xe3, 0x92, 0x3c, 0x9d, 0xba, 0x55,
	0xb2, 0x52, 0xd2, 0x64, 0x45, 0x38, 0x6b, 0x6d, 0x33, 0xca, 0xd2, 0x59, 0xc7, 0xfb, 0x90, 0xe2,
	0x9d, 0x8a, 0x38, 0x62, 0xde, 0x25, 0xef, 0x80, 0xb5, 0x5c, 0x2e, 0xbd, 0x03, 0x16, 0x22, 0xff,
	0x71, 0x28, 0x73, 0xb1, 0x94, 0x76, 0x25, 0xbf, 0xd4, 0xfb, 0x01, 0x67, 0x63, 0x5c, 0x2f, 0x65,
	0x25, 0x10, 0x5f, 0x68, 0x9b, 0x21, 0x52, 0x74, 0xa0, 0xa0, 0xb9, 0x4a, 0x69, 0xc2, 0x23, 0xa4,
	0xc7, 0xc3, 0x00, 0x4f, 0x11, 0x78, 0x84, 0x14, 0x5f, 0x86, 0x68, 0xd4, 0xa6, 0x44, 0x43, 0xc4,
	0x4d, 0x75, 0x62, 0x25, 0x67, 0xc7, 0x78, 0x7d, 0xed, 0xef, 0x07, 0x34, 0xfd, 0x06, 0x49, 0xb7,
	0x00, 0xe0, 0xec, 0x6f, 0x40, 0x0b, 0x1b, 0xd9, 0xf1, 0xc8, 0x57, 0xf5, 0x1c, 0x0b, 0x74, 0xb2,
	0x11, 0xf0, 0x0e, 0x82, 0x51, 0x7f, 0xbf, 0x51, 0x80, 0x86, 0xb1, 0x0e, 0x21, 0x54, 0x14, 0x3a,
	0xc5, 0xb7, 0x05, 0x18, 0x36, 0xf5, 0xe3, 0x12, 0x39, 0xbd, 0x8e, 0x41, 0x00, 0x70, 0x8b, 0x5e,
	0x82, 0x05, 0x5c, 0x7f, 0x97, 0x3d, 0xf1, 0xfb, 0x2c, 0xe8, 0xa9, 0x4d, 0x6c, 0x20, 0xb4, 0x23,
	0x81, 0x98, 0x84, 0x9f, 0xf4, 0xfd, 0x30, 0x4e, 0xc2, 0x8b, 0x0f, 0x0c, 0x36, 0xb1, 0x4a, 0x29,
	0x3e, 0x74, 0x90, 0xdc, 0x52, 0xed, 0x92, 0x3a, 0x75, 0xd8, 0xb7, 0xa1, 0xce, 0x59, 0x0f, 0x5f,
	0xac, 0x6b, 0x72, 0x9c, 0x79, 0xcf, 0xb0, 0x43, 0x78, 0x54, 0x69, 0x26, 0x3b, 0xc5, 0x96, 0x52,
	0x18, 0xe4, 0x3e, 0xe3, 0x3d, 0xf5, 0x28, 0x4e, 0x00, 0x36, 0x18, 0xef, 0xc5, 0x2b, 0xf4, 0x7b,
	0xa1, 0x52, 0x6f, 0x5c, 0xe1, 0x56, 0x2f, 0x0c, 0x9c, 0x7b, 0x50, 0xd3, 0xa8, 0x8a, 0x7d, 0x56,
	0x93, 0xd1, 0xa2, 0x45, 0x35, 0x16, 0xf2, 0xe4, 0x32, 0x54, 0xfd, 0x88, 0x0d, 0x13, 0xeb, 0x5e,
	0x75, 0x2b, 0x02, 0x80, 0x77, 0x1e, 0xdf, 0xb4, 0xe0, 0xb9, 0x1d, 0x16, 0xc5, 0xbc, 0x5f, 0x53,
	0x92, 0x79, 0x06, 0xdb, 0x79, 0x86, 0xe0, 0x5e, 0x53, 0x8d, 0xe2, 0x29, 0xaa, 0x51, 0x4a, 0xab,
	0x46, 0x22, 0x9f, 0x65, 0x5d, 0x3e, 0x9d, 0xab, 0x70, 0x25, 0x67, 0xc6, 0x64, 0xdb, 0x9c, 0x6f,
	0x59, 0xb0, 0xb4, 0xc9, 0xa2, 0x4d, 0x16, 0xb0, 0xb1, 0xa7, 0xd5, 0xac, 0x9f, 0x27, 0x39, 0x99,
	0x1f, 0xce, 0xd1, 0x83, 0x81, 0x7d, 0xd6, 0xe5, 0x19, 0xf7, 0xd3, 0x62, 0x4d, 0x78, 0x13, 0x63,
	0xb8, 0xb0, 0xe4, 0x6e, 0x66, 0x86, 0x0b, 0xfb, 0xf7, 0x02, 0xd8, 0x46, 0x2d, 0xf0, 0xc3, 0xb7,
	0xfe, 0x67, 0xaa, 0x81, 0x53, 0x35, 0xbf, 0xe5, 0xa9, 0x9a, 0xdf, 0xe9, 0x8a, 0xd0, 0xb9, 0xac,
	0x8a, 0xd0, 0xac, 0x1a, 0xc2, 0xf9, 0xcc, 0x1a, 0xc2, 0xac, 0xba, 0xc4, 0x4a, 0x66, 0x5d, 0xe2,
	0x74, 0x39, 0x44, 0x75, 0xba, 0x1c, 0x42, 0xf8, 0x49, 0x59, 0x6e, 0x35, 0xf5, 0x0b, 0x09, 0x4d,
	0x6a, 0x88, 0x29, 0x3a, 0x5f, 0xb1, 0xc0, 0xc6, 0x64, 0xd7, 0xae, 0xf7, 0x18, 0x33, 0xa4, 0xc4,
	0xf6, 0x33, 0xd7, 0x53, 0xa6, 0x6b, 0x3b, 0x0b, 0x59, 0xb5, 0x9d, 0x53, 0xa5, 0xbf, 0xc5, 0xa9,
	0xd2, 0x5f, 0xe7, 0x7b, 0x62, 0x22, 0x82, 0x2f, 0xe6, 0x44, 0xb2, 0x6a, 0x52, 0xad, 0x33, 0xd7,
	0xa4, 0x16, 0x32, 0x6b, 0x52, 0x6f, 0xc1, 0x0a, 0x61, 0x4e, 0xcd, 0x9e, 0x82, 0xb6, 0x25, 0x6c,
	0xbd, 0x67, 0x2e, 0x21, 0xd1, 0x76, 0xf5, 0xda, 0xbb, 0xac, 0xb4, 0x5d, 0x84, 0xcd, 0x5d, 0x58,
	0x45, 0xc6, 0xdc, 0xf5, 0xb8, 0x3c, 0x3a, 0x62, 0x91, 0x2d, 0x9d, 0x29, 0x52, 0xc5, 0xb9, 0xd6,
	0x53, 0x14, 0xe7, 0x3a, 0x2e, 0x2c, 0xe1, 0x00, 0x29, 0xe2, 0x7a, 0x69, 0x77, 0xe1, 0x9c, 0xa5,
	0xdd, 0xce, 0xb7, 0x4a, 0xb0, 0xa0, 0x11, 0x15, 0xf4, 0xd2, 0x05, 0xed, 0xd6, 0xd9, 0x0a, 0xda,
	0x0b, 0xe7, 0x28, 0x68, 0x2f, 0x9e, 0xa3, 0xa0, 0xbd, 0x94, 0x5d, 0xd0, 0xae, 0x55, 0x7b, 0x97,
	0xf3, 0xab, 0xbd, 0xe7, 0x52, 0xd5, 0xde, 0xa9, 0x1a, 0xed, 0xf9, 0xb3, 0xd4, 0x68, 0x57, 0xce,
	0x57, 0xa3, 0x5d, 0xcd, 0xaf, 0xd1, 0xce, 0xd2, 0x16, 0xc8, 0xd4, 0x96, 0x5b, 0xb0, 0xa2, 0x71,
	0x8d, 0x6a, 0x3d, 0x51, 0xa8, 0xe5, 0xab, 0xc8, 0xa5, 0x98, 0x75, 0x49, 0x9a, 0x3a, 0x35, 0x27,
	0xbd, 0x53, 0x3d, 0x35, 0x27, 0xb3, 0x53, 0x10, 0x46, 0x5d, 0x4a, 0xec, 0x1a, 0x9d, 0x1a, 0xa8,
	0xef, 0x4b, 0x41, 0x18, 0xed, 0x60, 0x63, 0xd2, 0xc9, 0xf9, 0x69, 0x68, 0xe1, 0x97, 0xcc, 0x75,
	0x73, 0x21, 0x36, 0x9b, 0x99, 0xef, 0xa3, 0xcf, 0xff, 0x48, 0xc0, 0xf9, 0xf5, 0x02, 0xd8, 0xc6,
	0x4d, 0x93, 0x1f, 0x49, 0xb1, 0x34, 0x0a, 0xf5, 0xad, 0xb3, 0x14, 0xea, 0x17, 0xb2, 0xcc, 0xb2,
	0xf6, 0xd8, 0xa3, 0x68, 0x3e, 0xf6, 0x98, 0xaa, 0xd2, 0x2e, 0x4d, 0x57, 0x69, 0x4f, 0xd7, 0x7b,
	0x97, 0x33, 0xea, 0xbd, 0xb3, 0x0c, 0xfa, 0xdc, 0x99, 0x0b, 0xcd, 0x33, 0x9d, 0x84, 0xf3, 0xed,
	0x12, 0xd4, 0x84, 0xf5, 0xd2, 0x38, 0x62, 0x98, 0x54, 0xeb, 0x94, 0xd7, 0x14, 0x85, 0x59, 0xaf,
	0x29, 0x8a, 0xa9, 0xd7, 0x14, 0x86, 0xd5, 0xe5, 0x84, 0x53, 0x4a, 0x59, 0x5d, 0x8e, 0x98, 0x37,
	0x61, 0x91, 0x30, 0xc7, 0xec, 0xc8, 0x1b, 0xf7, 0x09, 0xb5, 0x8c, 0xa8, 0x4d, 0x6c, 0x70, 0x11,
	0x8e, 0xb8, 0x6f, 0xc3, 0x2a, 0x89, 0xd8, 0xb4, 0x4d, 0xa0, 0xa3, 0x3c, 0xd5, 0x35, 0xef, 0xa6,
	0x0c, 0xc3, 0x3b, 0x70, 0x85, 0x86, 0xd0, 0xba, 0xd1, 0xbc, 0x8c, 0x3b, 0x87, 0x8b, 0x88, 0x14,
	0x77, 0xc6, 0x29, 0x4a, 0x0a, 0xb7, 0xe1, 0x79, 0xa2, 0xa0, 0x69, 0x88, 0x9c, 0xaf, 0xf6, 0x20,
	0xa7, 0xe4, 0x5e, 0x42, 0xac, 0xbb, 0x4a, 0x51, 0x68, 0xea, 0x92, 0x86, 0x60, 0x49, 0xa2, 0x1f,
	0xb4, 0xce, 0xaa, 0x64, 0x49, 0xac, 0x1b, 0xca, 0xbd, 0xe4, 0xa8, 0x2f, 0x5d, 0x57, 0x64, 0xaa,
	0xef, 0xc7, 0xa1, 0xad, 0x75, 0x92, 0xee, 0x5b, 0x4e, 0xae, 0x86, 0xdd, 0x96, 0xe3, 0x6e, 0x7a,
	0x82, 0x52, 0x18, 0x35, 0x89, 0x8d, 0x53, 0xaa, 0xe3, 0x94, 0xe4, 0x4b, 0x6a, 0x74, 0xac, 0x9f,
	0x86, 0x26, 0x49, 0x91, 0xc7, 0x0f, 0x29, 0xae, 0x12, 0xf1, 0x3f, 0xa9, 0x11, 0xb1, 0x9d, 0x3e,
	0x04, 0x14, 0x43, 0x7c, 0x19, 0x90, 0xd3, 0x87, 0xf3, 0xab, 0x56, 0x52, 0x30, 0x2a, 0x48, 0x08,
	0x2d, 0x25, 0xd7, 0xfc, 0x71, 0x23, 0x57, 0x76, 0x3d, 0x57, 0xd7, 0x93, 0x81, 0x93, 0x73, 0xec,
	0x98, 0x0d, 0x3d, 0x15, 0x25, 0x92, 0xef, 0x00, 0x02, 0xa9, 0xa2, 0xfc, 0xc7, 0x93, 0xbd, 0x3d,
	0xba, 0x01, 0x50, 0xa9, 0x23, 0x01, 0xc1, 0xc4, 0xbf, 0xf3, 0xbb, 0x05, 0x9a, 0x92, 0x1e, 0xe0,
	0xd2, 0x94, 0x1e, 0xc0, 0x22, 0xbe, 0xbb, 0xef, 0xfb, 0x43, 0x16, 0xe8, 0xbe, 0x29, 0xaf, 0xa0,
	0x95, 0x45, 0x1b, 0x5e, 0xc4, 0x36, 0x04, 0x7a, 0xec, 0x03, 0xdd, 0x66, 0xdf, 0x84, 0x09, 0xb2,
	0x47, 0x8c, 0x1d, 0x9a, 0x64, 0x8b, 0x33, 0xc9, 0xbe, 0xcf, 0xd8, 0x61, 0x9a, 0xec, 0x91, 0x09,
	0xb3, 0xbf, 0x00, 0x36, 0x6d, 0xb7, 0x41, 0x97, 0xaa, 0xed, 0x6f, 0xe6, 0xd0, 0x45, 0x01, 0x48,
	0x11, 0x26, 0xbf, 0xa2, 0x01, 0x9d, 0xef, 0x5a, 0x58, 0x3a, 0x22, 0x39, 0x84, 0x9e, 0x5f, 0xd5,
	0x26, 0x9f, 0xe7, 0x14, 0xf0, 0x49, 0xa8, 0x0a, 0x8b, 0x94, 0xe4, 0x96, 0x4f, 0xad, 0x66, 0xaf,
	0x08, 0xfc, 0x78, 0x0f, 0x93, 0x93, 0x40, 0x69, 0xd6, 0x49, 0x20, 0xf5, 0xb3, 0x3d, 0xbf, 0x50,
	0xca, 0x38, 0xbf, 0xf0, 0x91, 0xdd, 0x89, 0x2f, 0x06, 0xb4, 0xe2, 0xa4, 0x17, 0x73, 0xf8, 0x44,
	0x55, 0xca, 0xf2, 0x4d, 0xa1, 0xba, 0x3e, 0xc0, 0x23, 0xe3, 0x8f, 0x65, 0x84, 0x28, 0xef, 0xc3,
	0x25, 0xf3, 0x57, 0x1c, 0x8c, 0xeb, 0xb3, 0xf2, 0xb9, 0x47, 0x58, 0xf5, 0xb3, 0xaf, 0xc2, 0xec,
	0x37, 0x61, 0xd9, 0xe7, 0xe2, 0xc4, 0x72, 0xd4, 0x35, 0x06, 0x94, 0xc7, 0x26, 0xdb, 0xe7, 0x3b,
	0x07, 0xe1, 0x91, 0x7e, 0x91, 0xe6, 0xfc, 0x73, 0x01, 0x2b, 0xc1, 0x8c, 0x4d, 0x4c, 0x45, 0xe0,
	0x74, 0x27, 0x98, 0x44, 0xe0, 0xf8, 0x4b, 0x13, 0xde, 0xc0, 0xef, 0x77, 0x35, 0x24, 0xba, 0x04,
	0x5c, 0x40, 0xf8, 0x5a, 0x8c, 0x79, 0x0b, 0x56, 0x0c, 0xcc, 0x3e, 0xf3, 0xfa, 0xdd, 0x81, 0x1f,
	0xc4, 0xf1, 0xbf, 0x86, 0xbf, 0xc1, 0xbc, 0xfe, 0x5d, 0x3f, 0xc0, 0xa0, 0x8c, 0x36, 0x62, 0x6a,
	0x10, 0xca, 0xe4, 0x2e, 0x61, 0xeb, 0x43, 0x73, 0xa4, 0xcf, 0xc0, 0x73, 0x19, 0x9d, 0x92, 0xf1,
	0x28, 0xad, 0xdd, 0x4e, 0x77, 0x8d, 0x07, 0x7d, 0x19, 0x9a, 0x3c, 0xf2, 0xc6, 0xfa, 0x68, 0x64,
	0xb2, 0x1b, 0x02, 0x9c, 0x8c, 0xf3, 0x06, 0x5c, 0x18, 0x85, 0x11, 0x0b, 0x22, 0xdf, 0x1b, 0xe8,
	0xc8, 0xe4, 0xd6, 0xec, 0xb8, 0x2d, 0xee, 0x21, 0xce, 0x7d, 0xcb, 0x99, 0x92, 0x9d, 0x79, 0x02,
	0x28, 0x9d, 0xed, 0x04, 0x50, 0x9a, 0x3e, 0x01, 0xa4, 0x62, 0xee, 0x62, 0x3a, 0xe6, 0x76, 0xfe,
	0x91, 0x26, 0x32, 0xad, 0x0b, 0x62, 0x43, 0xd1, 0x25, 0xa2, 0x5a, 0x19, 0x93, 0xc1, 0x4b, 0x34,
	0xd1, 0x23, 0xf1, 0xcd, 0xe8, 0xd9, 0x75, 0x4c, 0xb9, 0xf5, 0x02, 0xae, 0x61, 0x8a, 0xa8, 0x89,
	0x4a, 0x7e, 0xf5, 0x0b, 0xb3, 0x9a, 0x2a, 0x03, 0xf6, 0xe9, 0x49, 0x34, 0xd2, 0x99, 0xba, 0x6e,
	0x69, 0x08, 0xf0, 0x4e, 0x9c, 0x8e, 0x77, 0x00, 0x01, 0xdd, 0x94, 0x19, 0xab, 0x09, 0xa0, 0xfa,
	0x59, 0xbb, 0x3f, 0x28, 0xa0, 0x1d, 0xce, 0x50, 0x97, 0xec, 0xa3, 0x91, 0x95, 0x5d, 0x3d, 0x91,
	0x79, 0x34, 0x92, 0x0c, 0x4f, 0x1f, 0x8d, 0x8c, 0x13, 0x50, 0x31, 0x75, 0x02, 0xba, 0x05, 0x2b,
	0x78, 0x5a, 0xc9, 0x3e, 0x68, 0x95, 0xdc, 0x25, 0xd1, 0x7a, 0x37, 0x45, 0xf1, 0x53, 0x70, 0x59,
	0xc3, 0x27, 0x49, 0x96, 0x37, 0x26, 0x42, 0xc6, 0x48, 0x86, 0x57, 0xe3, 0x79, 0xa0, 0x1c, 0x53,
	0x57, 0xa9, 0x96, 0x53, 0xc7, 0x1e, 0x92, 0xe1, 0xf4, 0x03, 0xd0, 0x5b, 0x50, 0x4d, 0x1e, 0xd4,
	0xc7, 0x21, 0x8a, 0xa5, 0x87, 0x28, 0x2d, 0x28, 0x1e, 0xb2, 0x13, 0x55, 0x2a, 0x7d, 0xc8, 0x4e,
	0x9c, 0xcf, 0xa2, 0xf4, 0x68, 0x87, 0x62, 0xf5, 0x9c, 0xe5, 0x4d, 0x23, 0x3a, 0xc9, 0x74, 0x5b,
	0xc9, 0x89, 0x98, 0xae, 0xed, 0xee, 0xc3, 0xa5, 0x4d, 0x16, 0x69, 0xca, 0xff, 0xc3, 0x12, 0xfc,
	0xa6, 0x05, 0x35, 0xdd, 0x12, 0xbe, 0x0e, 0x4b, 0x2a, 0xf8, 0xec, 0x85, 0x49, 0xf0, 0x29, 0xcb,
	0x1b, 0x86, 0x14, 0x73, 0x52, 0xe9, 0x1b, 0xe2, 0xbf, 0x06, 0xb6, 0x6e, 0x94, 0xbb, 0x14, 0x9e,
	0xc9, 0x12, 0x21, 0xa6, 0x15, 0x4f, 0x08, 0xb8, 0xb0, 0xb3, 0xd3, 0xd8, 0xdd, 0x27, 0x6f, 0xc9,
	0x00, 0xca, 0x4e, 0x77, 0x78, 0xf8, 0x96, 0xe2, 0x67, 0x29, 0xe1, 0xe7, 0x3d, 0x0a, 0x1c, 0x44,
	0x00, 0xa0, 0x57, 0x67, 0xf0, 0x91, 0x7d, 0xcb, 0x58, 0x7f, 0x4e, 0x5d, 0x79, 0xd2, 0x83, 0x38,
	0xf0, 0x48, 0x3e, 0xd4, 0x55, 0x31, 0x60, 0xaa, 0x72, 0xb5, 0xa1, 0x2a, 0x57, 0x05, 0x7c, 0xcf,
	0x0f, 0x7c, 0x79, 0xce, 0x68, 0xb8, 0xf2, 0x0b, 0x2b, 0x32, 0x49, 0x39, 0x55, 0x65, 0xb8, 0xfc,
	0x74, 0x86, 0x14, 0x25, 0xe8, 0x71, 0xa9, 0x98, 0xe6, 0xdb, 0xc6, 0x34, 0x5f, 0x38, 0x35, 0x2a,
	0x3d, 0x63, 0x4c, 0xea, 0xfc, 0xa1, 0x05, 0x90, 0xf8, 0xa3, 0x19, 0xbf, 0xa4, 0xa3, 0xdf, 0x53,
	0x17, 0x52, 0xf7, 0xd4, 0xa9, 0xdf, 0xc8, 0x28, 0x25, 0x0f, 0xda, 0x5e, 0x51, 0x6a, 0x12, 0x85,
	0x5d, 0x95, 0x7b, 0x28, 0x19, 0xb9, 0x87, 0x6d, 0xca, 0x3d, 0xc8, 0xf8, 0xad, 0x9c, 0x5c, 0xe7,
	0xc4, 0x25, 0xf5, 0x73, 0xfa, 0x1b, 0x92, 0x3b, 0x9a, 0x07, 0x55, 0xbc, 0x79, 0xcb, 0xe0, 0x4d,
	0x66, 0x6d, 0xad, 0xe6, 0x72, 0x69, 0x07, 0x3b, 0xb0, 0xaa, 0x8a, 0x90, 0x4f, 0x2f, 0x85, 0xcc,
	0x7f, 0x60, 0xf5, 0xf5, 0x42, 0x0e, 0x9d, 0x1f, 0x47, 0xed, 0x71, 0xd4, 0x7e, 0x0c, 0xb5, 0x0d,
	0xcf, 0x1f, 0x9c, 0x24, 0xbe, 0xea, 0x19, 0x3a, 0x62, 0xfc, 0x49, 0x32, 0x7f, 0xa0, 0xbb, 0xe1,
	0x0a, 0x02, 0x84, 0x13, 0xfe, 0x3b, 0x0b, 0xea, 0x62, 0xed, 0xf1, 0xd8, 0xff, 0x07, 0x7c, 0xef,
	0x37, 0x0a, 0xd0, 0x40, 0xbe, 0x0f, 0x66, 0x66, 0x23, 0x9f, 0x81, 0xcb, 0xbd, 0x0a, 0xf4, 0x6b,
	0xd6, 0x06, 0x73, 0x41, 0x82, 0x9e, 0xda, 0xed, 0xbe, 0x0d, 0xab, 0x39, 0x6e, 0x57, 0xba, 0xdc,
	0x0b, 0x59, 0x2e, 0x57, 0x78, 0x01, 0xec, 0x26, 0xdc, 0x6d, 0x46, 0x8a, 0xc5, 0x16, 0x8d, 0x3b,
	0xfe, 0xb1, 0x36, 0x92, 0xf3, 0x39, 0xb8, 0xa2, 0x54, 0x33, 0x4e, 0x9f, 0x3c, 0xed, 0x99, 0xd1,
	0x71, 0x67, 0x12, 0x7b, 0x3a, 0x3f, 0xaa, 0x4d, 0x30, 0xe6, 0xd2, 0xb3, 0x98, 0x60, 0x06, 0xb1,
	0xa7, 0x9b, 0x60, 0x08, 0x8b, 0x9b, 0x2c, 0xfa, 0x10, 0x9f, 0x83, 0xf7, 0xa7, 0x06, 0xfc, 0x51,
	0xbc, 0x08, 0xef, 0x24, 0x0f, 0x50, 0x52, 0xd5, 0x61, 0xd3, 0x45, 0x05, 0xf9, 0x55, 0xf0, 0xce,
	0xe3, 0x6c, 0x32, 0x7c, 0x64, 0xbf, 0x0b, 0x0b, 0x4a, 0x73, 0x34, 0x75, 0xcc, 0x71, 0xdb, 0x86,
	0x32, 0x4b, 0xb7, 0xa8, 0x3e, 0x9d, 0x5f, 0xb2, 0xe0, 0xb2, 0x0a, 0x07, 0xe4, 0x93, 0x05, 0xa3,
	0x68, 0xf5, 0x3c, 0x69, 0x8f, 0xa7, 0xbf, 0xc4, 0x74, 0x67, 0x4c, 0xe2, 0x69, 0x43, 0xa8, 0x3b,
	0xf8, 0x48, 0x29, 0xa1, 0x19, 0x07, 0x3b, 0xe7, 0x94, 0xfb, 0x23, 0x79, 0xbf, 0x2a, 0xfa, 0x63,
	0xfc, 0x37, 0x23, 0xce, 0x8e, 0x53, 0x81, 0x05, 0x2d, 0x15, 0x88, 0xa9, 0x46, 0x0c, 0x30, 0xa9,
	0x4d, 0xde, 0x68, 0x23, 0x88, 0x22, 0xd0, 0x0b, 0x50, 0x1e, 0xb0, 0x27, 0x6c, 0x20, 0x63, 0x38,
	0xfa, 0x70, 0x46, 0xb0, 0x14, 0x0f, 0x4c, 0x4e, 0x1f, 0x47, 0x56, 0xef, 0x91, 0x2c, 0xed, 0xc1,
	0x7a, 0x07, 0x80, 0xa2, 0xd6, 0xd3, 0x7e, 0xb3, 0x6d, 0x7a, 0x25, 0x6e, 0x15, 0x7b, 0xa2, 0xdc,
	0xfe, 0x7e, 0x21, 0x8f, 0x67, 0x67, 0x53, 0xef, 0x84, 0xfa, 0x33, 0x49, 0x58, 0xda, 0xef, 0x42,
	0x2d, 0xf2, 0xf8, 0x61, 0xb7, 0x8f, 0xab, 0x97, 0x3f, 0x3e, 0xf6, 0xca, 0xcc, 0x91, 0x13, 0x46,
	0xb9, 0x10, 0xc5, 0xdf, 0xf6, 0x35, 0xa8, 0xfb, 0xbc, 0x1b, 0xb0, 0x23, 0xba, 0x30, 0x50, 0xcf,
	0x58, 0x7d, 0xbe, 0xcd, 0x8e, 0xb0, 0xaf, 0xf0, 0xc7, 0x71, 0xb3, 0xba, 0x95, 0x92, 0xf5, 0xfb,
	0x81, 0xc4, 0xa1, 0x0b, 0x29, 0xe7, 0x1e, 0x54, 0xe3, 0xe1, 0xce, 0x93, 0x12, 0x4e, 0xb6, 0xb9,
	0xa2, 0x6d, 0xf3, 0xcd, 0xb7, 0xa1, 0x1a, 0x9b, 0x31, 0xdb, 0x86, 0x85, 0x8d, 0xb5, 0x47, 0x5d,
	0x77, 0x6d, 0x7b, 0xb3, 0xd3, 0xdd, 0x7d, 0xf4, 0x5e, 0xa7, 0xf5, 0xff, 0xec, 0x0b, 0xd0, 0xba,
	0x77, 0x7f, 0x7b, 0xf7, 0x5d, 0x1d, 0x6a, 0xdd, 0xfc, 0x8a, 0x05, 0xd5, 0xce, 0x78, 0x1c, 0x8e,
	0xd7, 0xc3, 0x3e, 0xb3, 0x57, 0xc0, 0xee, 0xb8, 0xee, 0x7d, 0xb7, 0xbb, 0x7e, 0x7f, 0xa3, 0xd3,
	0xdd, 0xe8, 0xdc, 0x59, 0x7b, 0x70, 0x77, 0x17, 0xfb, 0x36, 0x35, 0xf8, 0x7b, 0x6b, 0xae, 0xdb,
	0xfa, 0xce, 0xaa, 0x7d, 0x11, 0x2e, 0x68, 0xd0, 0x07, 0xdb, 0xf7, 0xd6, 0xdc, 0x9d, 0x77, 0xd7,
	0xee, 0xb6, 0xbe, 0xbb, 0x6a, 0xaf, 0x1a, 0x84, 0xb6, 0xb6, 0x1f, 0xae, 0xdd, 0xdd, 0xda, 0x68,
	0xfd, 0x69, 0xba, 0x61, 0xbb, 0xb3, 0xfb, 0xfe, 0x7d, 0xf7, 0x73, 0xad, 0xef, 0xad, 0xde, 0x7c,
	0x27, 0x7e, 0xad, 0x8b, 0x2b, 0x68, 0x42, 0x6d, 0x77, 0x6d, 0x53, 0x9b, 0xc2, 0x22, 0x34, 0x04,
	0xc0, 0xed, 0xac, 0xdf, 0xbf, 0x77, 0xaf, 0xb3, 0xbd, 0xd1, 0xb2, 0xec, 0x06, 0x54, 0x05, 0xe8,
	0xd1, 0x83, 0x47, 0x5b, 0xdb, 0xad, 0xc2, 0xcd, 0x0d, 0x68, 0xa6, 0x1e, 0xeb, 0xd9, 0x0b, 0x00,
	0x8f, 0xf4, 0x75, 0xb4, 0xa0, 0xfe, 0xa8, 0xd3, 0x15, 0x14, 0xb6, 0x76, 0x77, 0x3b, 0x82, 0x46,
	0x13, 0x6a, 0x8f, 0x3a, 0x5d, 0xb7, 0xf3, 0xd9, 0xce, 0xba, 0x00, 0x14, 0x1e, 0xcf, 0xe1, 0xff,
	0xad, 0x71, 0xeb, 0xbf, 0x03, 0x00, 0x00, 0xff, 0xff, 0xd4, 0x1c, 0x58, 0xe7, 0x76, 0x63, 0x00,
	0x00,
}
