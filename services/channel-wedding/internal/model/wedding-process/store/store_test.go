package store

import (
	"context"
	"fmt"
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"testing"
	"time"
)

var testStore *Store

var (
	testUid = uint32(1)
	ctx     = context.Background()
)

func init() {
	mysqlConfig := &mysqlConnect.MysqlConfig{
		Host:     "*************", //"*************"
		Port:     3306,
		Database: "appsvr",
		Charset:  "utf8",
		UserName: "godman",
		Password: "thegodofman",
	}

	dbCli, err := mysqlConnect.NewClient(context.Background(), mysqlConfig)
	if err != nil {
		return
	}

	testStore = NewStore(dbCli)
}

func TestStore_AddWeddingSchedule(t *testing.T) {
	cid := uint32(2)
	planId := uint32(time.Now().Unix())

	err := testStore.Transaction(ctx, func(tx mysql.Txx) error {
		wid, err := testStore.AddWeddingSchedule(ctx, tx, &WeddingSchedule{
			Cid:       cid,
			ThemeId:   1,
			BrideUid:  1,
			GroomUid:  2,
			BeginTime: time.Now(),
			EndTime:   time.Now().Add(time.Second),
			Status:    WeddingStatusNotStart,
			PlanId:    planId,
			ThemeType: 1,
		})
		if err != nil {
			t.Error(err)
			return err
		}
		return testStore.UpdateWeddingStatus(ctx, tx, wid, WeddingStatusProcessing)
	})
	if err != nil {
		t.Error(err)
		return
	}

	info, ok, err := testStore.GetWeddingScheduleByPlanId(ctx, planId)
	if err != nil {
		t.Error(err)
		return
	}
	if !ok {
		t.Error("GetWeddingScheduleByPlanId not found")
		return
	}
	t.Logf("%+v", info)

	list, err := testStore.GetWeddingScheduleListByCid(ctx, cid, time.Now())
	if err != nil {
		t.Error(err)
		return
	}
	t.Log("GetWeddingScheduleListByCid:")
	for _, v := range list {
		t.Logf("%+v", v)
	}

	list, err = testStore.GetWeddingScheduleList(ctx, 1, 1, time.Now().Add(-time.Minute*2), time.Now().Add(time.Minute))
	if err != nil {
		t.Error(err)
		return
	}
	t.Log("GetWeddingScheduleList:")
	for _, v := range list {
		t.Logf("%+v", v)
	}
}

func TestStore_GetNotStartWeddingScheduleList(t *testing.T) {
	list, err := testStore.GetNotStartWeddingScheduleList(ctx, time.Now())
	if err != nil {
		t.Error(err)
		return
	}

	for _, v := range list {
		t.Logf("%+v", v)
	}
}

func TestStore_BatchInsertAwardLog(t *testing.T) {
	awardList := make([]*AwardLog, 0)
	for i := 0; i < 2; i++ {
		award := &AwardLog{
			OrderId:          time.Now().String() + fmt.Sprint(i),
			Uid:              testUid,
			WeddingLv:        1,
			SuitName:         "suit_name",
			SuitIcon:         "suit_icon",
			AwardDurationSec: 100,
			AwardTime:        time.Now(),
			ThemeType:        uint32(i%2 + 1),
		}
		award.SetSuitItemList([]uint32{})

		awardList = append(awardList, award)
	}

	err := testStore.Transaction(ctx, func(tx mysql.Txx) error {
		return testStore.BatchInsertAwardLog(ctx, tx, time.Now(), awardList)
	})
	if err != nil {
		t.Error(err)
		return
	}

    info, ok, err := testStore.GetAwardLogByOrderId(ctx, time.Now(), awardList[0].OrderId)
    if err != nil {
        t.Error(err)
        return
    }
    if !ok {
        t.Error("GetAwardLogByOrderId not found")
        return
    }
    t.Logf("GetAwardLogByOrderId: %+v, suit items: %v", info, info.GetSuitItemList())

	err = testStore.UpdateAwardLogStatus(ctx, awardList[0].OrderId, AwardDone, time.Now())
	if err != nil {
		t.Error(err)
		return
	}

	list, err := testStore.GetAwardLogList(ctx, time.Now(), AwardNotDone, 2, time.Now().Add(-time.Minute*2), time.Now().Add(time.Minute))
	if err != nil {
		t.Error(err)
		return
	}
	for _, info := range list {
		t.Logf("%+v, %v", info, info.GetSuitItemList())
	}

}

func TestStore_AddReservePresentRecord(t *testing.T) {
	testStore.createReservePresentTable(context.Background(), time.Now())

	err := testStore.AddReservePresentRecord(ctx, &ReservePresentRecord{
		OrderId:   "asdaffffffffff",
		Uid:       1,
		Cid:       2,
		WeddingId: 2,
		Status:    0,
		Ctime:     time.Now(),
		Mtime:     time.Now().Add(time.Minute),
		GiftId:    1000,
	})
	fmt.Println(err)

	testStore.SetReservePresentAwardDone(ctx, "asdaffffffffff", time.Now().Unix(), time.Now().Unix())
	record, found, err := testStore.GetReservePresentRecordByUidWeddingId(context.Background(), 1, 2, time.Now().Unix())
	fmt.Println(record.Id, record.GiftId, found, err)

}
