// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-wedding/internal/model/wedding-process/store (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	mysql "gitlab.ttyuyin.com/tyr/x/middleware/mysql"
	store "golang.52tt.com/services/channel-wedding/internal/model/wedding-process/store"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// AddReservePresentRecord mocks base method.
func (m *MockIStore) AddReservePresentRecord(arg0 context.Context, arg1 *store.ReservePresentRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddReservePresentRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddReservePresentRecord indicates an expected call of AddReservePresentRecord.
func (mr *MockIStoreMockRecorder) AddReservePresentRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddReservePresentRecord", reflect.TypeOf((*MockIStore)(nil).AddReservePresentRecord), arg0, arg1)
}

// AddWeddingSchedule mocks base method.
func (m *MockIStore) AddWeddingSchedule(arg0 context.Context, arg1 mysql.Txx, arg2 *store.WeddingSchedule) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddWeddingSchedule", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddWeddingSchedule indicates an expected call of AddWeddingSchedule.
func (mr *MockIStoreMockRecorder) AddWeddingSchedule(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddWeddingSchedule", reflect.TypeOf((*MockIStore)(nil).AddWeddingSchedule), arg0, arg1, arg2)
}

// BatchInsertAwardLog mocks base method.
func (m *MockIStore) BatchInsertAwardLog(arg0 context.Context, arg1 mysql.Txx, arg2 time.Time, arg3 []*store.AwardLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchInsertAwardLog", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchInsertAwardLog indicates an expected call of BatchInsertAwardLog.
func (mr *MockIStoreMockRecorder) BatchInsertAwardLog(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchInsertAwardLog", reflect.TypeOf((*MockIStore)(nil).BatchInsertAwardLog), arg0, arg1, arg2, arg3)
}

// Close mocks base method.
func (m *MockIStore) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockIStoreMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIStore)(nil).Close))
}

// GetAwardLogByOrderId mocks base method.
func (m *MockIStore) GetAwardLogByOrderId(arg0 context.Context, arg1 time.Time, arg2 string) (*store.AwardLog, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardLogByOrderId", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.AwardLog)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetAwardLogByOrderId indicates an expected call of GetAwardLogByOrderId.
func (mr *MockIStoreMockRecorder) GetAwardLogByOrderId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardLogByOrderId", reflect.TypeOf((*MockIStore)(nil).GetAwardLogByOrderId), arg0, arg1, arg2)
}

// GetAwardLogList mocks base method.
func (m *MockIStore) GetAwardLogList(arg0 context.Context, arg1 time.Time, arg2, arg3 uint32, arg4, arg5 time.Time) ([]*store.AwardLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardLogList", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]*store.AwardLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAwardLogList indicates an expected call of GetAwardLogList.
func (mr *MockIStoreMockRecorder) GetAwardLogList(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardLogList", reflect.TypeOf((*MockIStore)(nil).GetAwardLogList), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetNotStartWeddingScheduleList mocks base method.
func (m *MockIStore) GetNotStartWeddingScheduleList(arg0 context.Context, arg1 time.Time) ([]*store.WeddingSchedule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNotStartWeddingScheduleList", arg0, arg1)
	ret0, _ := ret[0].([]*store.WeddingSchedule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNotStartWeddingScheduleList indicates an expected call of GetNotStartWeddingScheduleList.
func (mr *MockIStoreMockRecorder) GetNotStartWeddingScheduleList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNotStartWeddingScheduleList", reflect.TypeOf((*MockIStore)(nil).GetNotStartWeddingScheduleList), arg0, arg1)
}

// GetReservePresentRecordByUidWeddingId mocks base method.
func (m *MockIStore) GetReservePresentRecordByUidWeddingId(arg0 context.Context, arg1, arg2 uint32, arg3 int64) (*store.ReservePresentRecord, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReservePresentRecordByUidWeddingId", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*store.ReservePresentRecord)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetReservePresentRecordByUidWeddingId indicates an expected call of GetReservePresentRecordByUidWeddingId.
func (mr *MockIStoreMockRecorder) GetReservePresentRecordByUidWeddingId(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReservePresentRecordByUidWeddingId", reflect.TypeOf((*MockIStore)(nil).GetReservePresentRecordByUidWeddingId), arg0, arg1, arg2, arg3)
}

// GetWeddingScheduleByPlanId mocks base method.
func (m *MockIStore) GetWeddingScheduleByPlanId(arg0 context.Context, arg1 uint32) (*store.WeddingSchedule, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingScheduleByPlanId", arg0, arg1)
	ret0, _ := ret[0].(*store.WeddingSchedule)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetWeddingScheduleByPlanId indicates an expected call of GetWeddingScheduleByPlanId.
func (mr *MockIStoreMockRecorder) GetWeddingScheduleByPlanId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingScheduleByPlanId", reflect.TypeOf((*MockIStore)(nil).GetWeddingScheduleByPlanId), arg0, arg1)
}

// GetWeddingScheduleList mocks base method.
func (m *MockIStore) GetWeddingScheduleList(arg0 context.Context, arg1, arg2 uint32, arg3, arg4 time.Time) ([]*store.WeddingSchedule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingScheduleList", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*store.WeddingSchedule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingScheduleList indicates an expected call of GetWeddingScheduleList.
func (mr *MockIStoreMockRecorder) GetWeddingScheduleList(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingScheduleList", reflect.TypeOf((*MockIStore)(nil).GetWeddingScheduleList), arg0, arg1, arg2, arg3, arg4)
}

// GetWeddingScheduleListByCid mocks base method.
func (m *MockIStore) GetWeddingScheduleListByCid(arg0 context.Context, arg1 uint32, arg2 time.Time) ([]*store.WeddingSchedule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingScheduleListByCid", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.WeddingSchedule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingScheduleListByCid indicates an expected call of GetWeddingScheduleListByCid.
func (mr *MockIStoreMockRecorder) GetWeddingScheduleListByCid(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingScheduleListByCid", reflect.TypeOf((*MockIStore)(nil).GetWeddingScheduleListByCid), arg0, arg1, arg2)
}

// PageGetGoingWeddingList mocks base method.
func (m *MockIStore) PageGetGoingWeddingList(arg0 context.Context, arg1, arg2 int64) ([]*store.WeddingSchedule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PageGetGoingWeddingList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.WeddingSchedule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PageGetGoingWeddingList indicates an expected call of PageGetGoingWeddingList.
func (mr *MockIStoreMockRecorder) PageGetGoingWeddingList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PageGetGoingWeddingList", reflect.TypeOf((*MockIStore)(nil).PageGetGoingWeddingList), arg0, arg1, arg2)
}

// SetReservePresentAwardDone mocks base method.
func (m *MockIStore) SetReservePresentAwardDone(arg0 context.Context, arg1 string, arg2, arg3 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetReservePresentAwardDone", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetReservePresentAwardDone indicates an expected call of SetReservePresentAwardDone.
func (mr *MockIStoreMockRecorder) SetReservePresentAwardDone(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetReservePresentAwardDone", reflect.TypeOf((*MockIStore)(nil).SetReservePresentAwardDone), arg0, arg1, arg2, arg3)
}

// Transaction mocks base method.
func (m *MockIStore) Transaction(arg0 context.Context, arg1 func(mysql.Txx) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Transaction", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Transaction indicates an expected call of Transaction.
func (mr *MockIStoreMockRecorder) Transaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Transaction", reflect.TypeOf((*MockIStore)(nil).Transaction), arg0, arg1)
}

// UpdateAwardLogStatus mocks base method.
func (m *MockIStore) UpdateAwardLogStatus(arg0 context.Context, arg1 string, arg2 uint32, arg3 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAwardLogStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAwardLogStatus indicates an expected call of UpdateAwardLogStatus.
func (mr *MockIStoreMockRecorder) UpdateAwardLogStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAwardLogStatus", reflect.TypeOf((*MockIStore)(nil).UpdateAwardLogStatus), arg0, arg1, arg2, arg3)
}

// UpdateWeddingStatus mocks base method.
func (m *MockIStore) UpdateWeddingStatus(arg0 context.Context, arg1 mysql.Txx, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWeddingStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWeddingStatus indicates an expected call of UpdateWeddingStatus.
func (mr *MockIStoreMockRecorder) UpdateWeddingStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWeddingStatus", reflect.TypeOf((*MockIStore)(nil).UpdateWeddingStatus), arg0, arg1, arg2, arg3)
}
