// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-wedding/internal/model/wedding-process/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	redis "github.com/go-redis/redis/v8"
	gomock "github.com/golang/mock/gomock"
	cache "golang.52tt.com/services/channel-wedding/internal/model/wedding-process/cache"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// AddHappiness mocks base method.
func (m *MockICache) AddHappiness(arg0 context.Context, arg1, arg2, arg3 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddHappiness", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddHappiness indicates an expected call of AddHappiness.
func (mr *MockICacheMockRecorder) AddHappiness(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddHappiness", reflect.TypeOf((*MockICache)(nil).AddHappiness), arg0, arg1, arg2, arg3)
}

// AddStageCidQueue mocks base method.
func (m *MockICache) AddStageCidQueue(arg0 context.Context, arg1 uint32, arg2 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddStageCidQueue", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddStageCidQueue indicates an expected call of AddStageCidQueue.
func (mr *MockICacheMockRecorder) AddStageCidQueue(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddStageCidQueue", reflect.TypeOf((*MockICache)(nil).AddStageCidQueue), arg0, arg1, arg2)
}

// BatGetHappiness mocks base method.
func (m *MockICache) BatGetHappiness(arg0 context.Context, arg1 []cache.HappinessKey) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetHappiness", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetHappiness indicates an expected call of BatGetHappiness.
func (mr *MockICacheMockRecorder) BatGetHappiness(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetHappiness", reflect.TypeOf((*MockICache)(nil).BatGetHappiness), arg0, arg1)
}

// BatchGetWeddingInfo mocks base method.
func (m *MockICache) BatchGetWeddingInfo(arg0 context.Context, arg1 []uint32) ([]*cache.WeddingInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetWeddingInfo", arg0, arg1)
	ret0, _ := ret[0].([]*cache.WeddingInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetWeddingInfo indicates an expected call of BatchGetWeddingInfo.
func (mr *MockICacheMockRecorder) BatchGetWeddingInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetWeddingInfo", reflect.TypeOf((*MockICache)(nil).BatchGetWeddingInfo), arg0, arg1)
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// DelStageCidQueue mocks base method.
func (m *MockICache) DelStageCidQueue(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelStageCidQueue", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelStageCidQueue indicates an expected call of DelStageCidQueue.
func (mr *MockICacheMockRecorder) DelStageCidQueue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelStageCidQueue", reflect.TypeOf((*MockICache)(nil).DelStageCidQueue), arg0, arg1)
}

// DelUserWeddingInfo mocks base method.
func (m *MockICache) DelUserWeddingInfo(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserWeddingInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserWeddingInfo indicates an expected call of DelUserWeddingInfo.
func (mr *MockICacheMockRecorder) DelUserWeddingInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserWeddingInfo", reflect.TypeOf((*MockICache)(nil).DelUserWeddingInfo), arg0, arg1)
}

// DelWeddingInfo mocks base method.
func (m *MockICache) DelWeddingInfo(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelWeddingInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelWeddingInfo indicates an expected call of DelWeddingInfo.
func (mr *MockICacheMockRecorder) DelWeddingInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelWeddingInfo", reflect.TypeOf((*MockICache)(nil).DelWeddingInfo), arg0, arg1)
}

// GetHappiness mocks base method.
func (m *MockICache) GetHappiness(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHappiness", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHappiness indicates an expected call of GetHappiness.
func (mr *MockICacheMockRecorder) GetHappiness(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHappiness", reflect.TypeOf((*MockICache)(nil).GetHappiness), arg0, arg1, arg2)
}

// GetHighLightPresentCount mocks base method.
func (m *MockICache) GetHighLightPresentCount(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHighLightPresentCount", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHighLightPresentCount indicates an expected call of GetHighLightPresentCount.
func (mr *MockICacheMockRecorder) GetHighLightPresentCount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHighLightPresentCount", reflect.TypeOf((*MockICache)(nil).GetHighLightPresentCount), arg0, arg1, arg2)
}

// GetRedisClient mocks base method.
func (m *MockICache) GetRedisClient() redis.Cmdable {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisClient")
	ret0, _ := ret[0].(redis.Cmdable)
	return ret0
}

// GetRedisClient indicates an expected call of GetRedisClient.
func (mr *MockICacheMockRecorder) GetRedisClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisClient", reflect.TypeOf((*MockICache)(nil).GetRedisClient))
}

// GetStageExpireCid mocks base method.
func (m *MockICache) GetStageExpireCid(arg0 context.Context, arg1, arg2 int64) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStageExpireCid", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStageExpireCid indicates an expected call of GetStageExpireCid.
func (mr *MockICacheMockRecorder) GetStageExpireCid(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStageExpireCid", reflect.TypeOf((*MockICache)(nil).GetStageExpireCid), arg0, arg1, arg2)
}

// GetUserWeddingInfo mocks base method.
func (m *MockICache) GetUserWeddingInfo(arg0 context.Context, arg1 uint32) (*cache.UserWeddingInfo, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserWeddingInfo", arg0, arg1)
	ret0, _ := ret[0].(*cache.UserWeddingInfo)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserWeddingInfo indicates an expected call of GetUserWeddingInfo.
func (mr *MockICacheMockRecorder) GetUserWeddingInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserWeddingInfo", reflect.TypeOf((*MockICache)(nil).GetUserWeddingInfo), arg0, arg1)
}

// GetWeddingInfo mocks base method.
func (m *MockICache) GetWeddingInfo(arg0 context.Context, arg1 uint32) (*cache.WeddingInfo, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingInfo", arg0, arg1)
	ret0, _ := ret[0].(*cache.WeddingInfo)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetWeddingInfo indicates an expected call of GetWeddingInfo.
func (mr *MockICacheMockRecorder) GetWeddingInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingInfo", reflect.TypeOf((*MockICache)(nil).GetWeddingInfo), arg0, arg1)
}

// IncrHighLightPresentCount mocks base method.
func (m *MockICache) IncrHighLightPresentCount(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrHighLightPresentCount", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrHighLightPresentCount indicates an expected call of IncrHighLightPresentCount.
func (mr *MockICacheMockRecorder) IncrHighLightPresentCount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrHighLightPresentCount", reflect.TypeOf((*MockICache)(nil).IncrHighLightPresentCount), arg0, arg1, arg2)
}

// Lock mocks base method.
func (m *MockICache) Lock(arg0 context.Context, arg1 string, arg2 time.Duration) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Lock", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Lock indicates an expected call of Lock.
func (mr *MockICacheMockRecorder) Lock(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Lock", reflect.TypeOf((*MockICache)(nil).Lock), arg0, arg1, arg2)
}

// SetUserWeddingInfo mocks base method.
func (m *MockICache) SetUserWeddingInfo(arg0 context.Context, arg1 uint32, arg2 *cache.UserWeddingInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserWeddingInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserWeddingInfo indicates an expected call of SetUserWeddingInfo.
func (mr *MockICacheMockRecorder) SetUserWeddingInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserWeddingInfo", reflect.TypeOf((*MockICache)(nil).SetUserWeddingInfo), arg0, arg1, arg2)
}

// SetWeddingInfo mocks base method.
func (m *MockICache) SetWeddingInfo(arg0 context.Context, arg1 uint32, arg2 *cache.WeddingInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWeddingInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWeddingInfo indicates an expected call of SetWeddingInfo.
func (mr *MockICacheMockRecorder) SetWeddingInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWeddingInfo", reflect.TypeOf((*MockICache)(nil).SetWeddingInfo), arg0, arg1, arg2)
}

// Unlock mocks base method.
func (m *MockICache) Unlock(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Unlock", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Unlock indicates an expected call of Unlock.
func (mr *MockICacheMockRecorder) Unlock(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Unlock", reflect.TypeOf((*MockICache)(nil).Unlock), arg0, arg1)
}
