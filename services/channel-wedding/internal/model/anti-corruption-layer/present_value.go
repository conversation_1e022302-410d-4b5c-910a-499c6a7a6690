package anti_corruption_layer

import (
    "context"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "gitlab.ttyuyin.com/tyr/x/compatible/proto"
    "golang.52tt.com/clients/account"
    channelPB "golang.52tt.com/protocol/app/channel"
    "golang.52tt.com/protocol/app/channel_wedding_logic"
    channel_wedding_conf "golang.52tt.com/protocol/services/channel-wedding-conf"
    virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
    "time"
)

// SendMvpUserChangeMsg 发送mvp变更消息
func (m *ACLayer) SendMvpUserChangeMsg(ctx context.Context, cid, weddingId, mvpUid uint32) error {
    if cid == 0 {
        return nil
    }

    msgTy := uint32(channelPB.ChannelMsgType_CHANNEL_WEDDING_MVP_CHANGE)
    opt := &channel_wedding_logic.WeddingPresentValMvpChangeOpt{
        ChannelId: cid,
        WeddingId: weddingId,
        ServerMs:  time.Now().UnixMilli(),
        MvpUid:    mvpUid,
    }

    msg, err := proto.Marshal(opt)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendMvpUserChangeMsg marshal failed. err:%v, %+v", err, opt)
        return err
    }

    seqId, _, err := m.channelMsgApi.ReliablePushToChannel(ctx, 0, cid, msgTy, "mvp变更", msg)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendMvpUserChangeMsg failed to ReliablePushToChannel. cid:%d, weddingId:%d, mvpUid:%d, err:%v",
            cid, weddingId, mvpUid, err)
        return err
    }

    log.InfoWithCtx(ctx, "SendMvpUserChangeMsg success. cid:%d, weddingId:%d, mvpUid:%d, seqId:%d",
        cid, weddingId, mvpUid, seqId)
    return nil
}

// SendMvpSettlementMsg 发送MVP结算消息
func (m *ACLayer) SendMvpSettlementMsg(ctx context.Context, cid, weddingId, mvpUid, themeId uint32) error {
    if cid == 0 || mvpUid == 0 {
        return nil
    }

    confResp, err := m.weddingCfgCli.GetThemeCfg(ctx, &channel_wedding_conf.GetThemeCfgReq{
        ThemeId: themeId,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SendMvpSettlementMsg failed to GetThemeCfg. cid:%d, weddingId:%d, themeId:%d, err:%v", cid, weddingId, themeId, err)
        return err
    }

    mvpRes := confResp.GetThemeCfg().GetMvpSettlementResource()
    if mvpRes == nil {
        log.WarnWithCtx(ctx, "SendMvpSettlementMsg failed, themeId:%d has no mvp settlement resource", themeId)
        return nil
    }

    mvpUser, sErr := m.userProfileCli.GetUserProfileV2(ctx, mvpUid, true)
    if sErr != nil {
        log.ErrorWithCtx(ctx, "SendMvpSettlementMsg failed to GetUserProfileV2. cid:%d, weddingId:%d, mvpUid:%d, err:%v", cid, weddingId, mvpUid, sErr)
        return sErr
    }

    viResp, err := m.virtualImageUser.BatchGetUserInuseItemInfo(ctx, &virtual_image_user.BatchGetUserInuseItemInfoReq{
        UidList: []uint32{mvpUid},
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SendMvpSettlementMsg failed to BatchGetUserInuseItemInfo. cid:%d, weddingId:%d, mvpUid:%d, err:%v",
            cid, weddingId, mvpUid, err)
    }

    inuseItemIdList := make([]uint32, 0)
    if len(viResp.GetUserInuseItemInfo()) > 0 {
        inuseInfo := viResp.GetUserInuseItemInfo()[0]
        for _, item := range inuseInfo.GetItems() {
            inuseItemIdList = append(inuseItemIdList, item.GetCfgId())
        }
    }

    malePose, femalePose := m.bc.GetMvpPoseId()
    mvpPose := femalePose
    if mvpUser.GetSex() == uint32(account.Male) {
        mvpPose = malePose
    }

    msgTy := uint32(channelPB.ChannelMsgType_CHANNEL_WEDDING_MVP_SETTLEMENT)
    opt := &channel_wedding_logic.WeddingMvpUserSettlementNotify{
        ChannelId:         cid,
        WeddingId:         weddingId,
        ServerMs:          time.Now().UnixMilli(),
        MvpUser:           mvpUser,
        ResourceUrl:       mvpRes.GetResourceUrl(),
        ResourceMd5:       mvpRes.GetResourceMd5(),
        UserInuseItemList: inuseItemIdList,
        MvpPoseId:         mvpPose,
    }

    msg, err := proto.Marshal(opt)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendMvpSettlementMsg marshal failed. err:%v, %+v", err, opt)
        return err
    }

    seqId, _, err := m.channelMsgApi.ReliablePushToChannel(ctx, 0, cid, msgTy, "mvp结算", msg)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendMvpSettlementMsg failed to ReliablePushToChannel. cid:%d, weddingId:%d, mvpUid:%d, err:%v",
            cid, weddingId, mvpUid, err)
        return err
    }

    log.InfoWithCtx(ctx, "SendMvpSettlementMsg success. cid:%d, weddingId:%d, mvpUid:%d, seqId:%d",
        cid, weddingId, mvpUid, seqId)
    return nil
}

// SendUserPresentCountChangeMsg 发送麦上用户计数器变更消息
func (m *ACLayer) SendUserPresentCountChangeMsg(ctx context.Context, cid, weddingId, uid, presentVal uint32) error {
    if cid == 0 || uid == 0 {
        return nil
    }

    msgTy := uint32(channelPB.ChannelMsgType_CHANNEL_WEDDING_RECV_PRESENT_VAL_CHANGE)
    opt := &channel_wedding_logic.WeddingRecvPresentValChangeOpt{
        ChannelId: cid,
        WeddingId: weddingId,
        ServerMs:  time.Now().UnixMilli(),
        UserPresentValList: []*channel_wedding_logic.WeddingPresentVal{
            {
                Uid:        uid,
                PresentVal: presentVal,
            },
        },
    }

    msg, err := proto.Marshal(opt)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendUserPresentCountChangeMsg marshal failed. err:%v, %+v", err, opt)
        return err
    }

    seqId, _, err := m.channelMsgApi.ReliablePushToChannel(ctx, 0, cid, msgTy, "麦上用户计数器值变更", msg)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendUserPresentCountChangeMsg failed to ReliablePushToChannel. cid:%d, uid:%d, presentVal:%d, err:%v",
            cid, uid, presentVal, err)
        return err
    }

    log.InfoWithCtx(ctx, "SendUserPresentCountChangeMsg success. cid:%d, uid:%d, presentVal:%d, seqId:%d",
        cid, uid, presentVal, seqId)
    return nil
}

// SendReceivePresentValTopChangeMsg 发送人气王变更消息
func (m *ACLayer) SendReceivePresentValTopChangeMsg(ctx context.Context, cid, weddingId, topUid, lv uint32) error {
    if cid == 0 {
        return nil
    }

    msgTy := uint32(channelPB.ChannelMsgType_CHANNEL_WEDDING_PRESENT_VAL_TOP_CHANGE)
    opt := &channel_wedding_logic.WeddingPresentValTopChangeOpt{
        ChannelId: cid,
        WeddingId: weddingId,
        ServerMs:  time.Now().UnixMilli(),
    }

    // 如果topUid和lv都大于0，则设置TopRecvPresentLv
    if topUid > 0 && lv > 0 {
        opt.TopRecvPresentLv = &channel_wedding_logic.WeddingPresentLevel{
            Uid: topUid,
            Lv:  lv,
        }
    }

    msg, err := proto.Marshal(opt)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendReceivePresentValTopChangeMsg marshal failed. err:%v, %+v", err, opt)
        return err
    }

    seqId, _, err := m.channelMsgApi.ReliablePushToChannel(ctx, 0, cid, msgTy, "人气王变更", msg)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendReceivePresentValTopChangeMsg failed to ReliablePushToChannel. cid:%d, weddingId:%d, topUid:%d, lv:%d, err:%v",
            cid, weddingId, topUid, lv, err)
        return err
    }

    log.InfoWithCtx(ctx, "SendReceivePresentValTopChangeMsg success. cid:%d, weddingId:%d, seqId:%d, topUid:%d, lv:%d",
        cid, weddingId, seqId, topUid, lv)
    return nil
}
