package anti_corruption_layer

import (
    "context"
    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    channelMic "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic"
    micMiddle "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic_middle"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channelol_go"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    protocolgrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app"
    imPB "golang.52tt.com/protocol/app/im"
    pbIMApi "golang.52tt.com/protocol/services/im-api"
    pb "golang.52tt.com/protocol/services/push-notification/v2"
    "time"
)

// SetMicStatus 设置麦位状态
func (m *ACLayer) SetMicStatus(ctx context.Context, cid, status uint32, micIdList []uint32) error {
    _, err := m.micMiddleCli.SetMicStatus(ctx, &micMiddle.SetMicStatusReq{
        Source:    "channel-wedding",
        OpUid:     0,
        Cid:       cid,
        MicIdList: micIdList,
        MicState:  status,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SetMicStatus failed to SetMicStatus. cid:%d, status:%d, micIdList:%v, err:%v", cid, status, micIdList, err)
        return err
    }

    log.InfoWithCtx(ctx, "SetMicStatus success. cid:%d, status:%d, micIdList:%v", cid, status, micIdList)
    return nil
}

func (m *ACLayer) GetOnMicUserList(ctx context.Context, cid uint32) (map[uint32]uint32, error) {
    uid2Mic := make(map[uint32]uint32)
    // 1.获取房间内当前麦位列表
    micResp, err := m.channelMicCli.GetMicrList(ctx, &channelMic.GetMicrListReq{
        ChannelId: cid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetOnMicUserList fail to GetMicrList. channelId:%d, err:%v", cid, err)
        return uid2Mic, err
    }

    for _, mic := range micResp.GetAllMicList() {
        if mic.GetMicUid() > 0 {
            uid2Mic[mic.GetMicUid()] = mic.GetMicId()
        }
    }
    return uid2Mic, nil
}

func (m *ACLayer) GetUserProfile(ctx context.Context, uid uint32) (*app.UserProfile, error) {
    return m.userProfileCli.GetUserProfileV2(ctx, uid, true)
}

func (m *ACLayer) GetUserProfileMap(ctx context.Context, uidList []uint32, replace bool) (map[uint32]*app.UserProfile, error) {
    if len(uidList) == 0 {
        return nil, nil
    }
    return m.userProfileCli.BatchGetUserProfileV2(ctx, uidList, replace)
}

func (m *ACLayer) SimplePushToChannel(ctx context.Context, fromUid, channelId, channelMsgType uint32, content string, pbOptData []byte) error {
    _, err := m.channelMsgApi.SimplePushToChannel(ctx, fromUid, channelId, channelMsgType, content, pbOptData)
    return err
}

// SendIMMsgAsync 发送IM消息
func (m *ACLayer) SendIMMsgAsync(c context.Context, fromUid uint32, toUidList []uint32, content, highLight, jumpUrl string) (err error) {
    if fromUid == 0 || len(toUidList) == 0 || content == "" {
        return nil
    }

    for _, toUid := range toUidList {
        if toUid == 0 {
            continue
        }
        go func(fromUid, toUid uint32) {
            ctx, cancel := protocolgrpc.NewContextWithInfoTimeout(c, time.Second*15)
            defer cancel()

            _, err := m.imApiCli.SimpleSend1V1Text(ctx, fromUid, toUid, content, highLight, jumpUrl)
            if err != nil {
                log.WarnWithCtx(ctx, "SendIMMsgAsync fail to SimpleSend1V1Text. fromUid:%v, toUid:%d, err:%v", fromUid, toUid, err)
                return
            }

            log.InfoWithCtx(ctx, "SendIMMsgAsync success. fromUid:%v, content:%s, toUid:%d", fromUid, content, toUid)

        }(fromUid, toUid)
    }

    return nil
}

// SendIMCommonXmlMsg 发送IM通用XML消息
func (m *ACLayer) SendIMCommonXmlMsg(ctx context.Context, fromUid uint32, toUidList []uint32, commStr string, extData *imPB.IMCommonXmlMsg, msgType uint32) error {
    if extData == nil || fromUid == 0 || len(toUidList) == 0 {
        return nil
    }
    ext, _ := proto.Marshal(extData)

    msgList := make([]*pbIMApi.BatchMsg, 0, len(toUidList))
    for _, toUid := range toUidList {
        msgList = append(msgList, &pbIMApi.BatchMsg{
            From: &pbIMApi.Entity{
                Type: pbIMApi.Entity_USER,
                Id:   fromUid,
            },
            To: &pbIMApi.Entity{
                Type: pbIMApi.Entity_USER,
                Id:   toUid,
            },
            Msg: &pbIMApi.CommonMsg{
                MsgType: msgType,
                Ext:     ext,
                Content: commStr,
            },
        })
    }

    _, err := m.imApiCli.BatchSendCommonMsg(ctx, &pbIMApi.BatchSendCommonMsgReq{
        BatchMsg: msgList,
        Opt: &pbIMApi.SendOption{
            HasMsgRedpoint: pbIMApi.SendOption_BOOL_TRUE,
            HasMsgExposure: pbIMApi.SendOption_BOOL_TRUE,
        },
        Namespace: "婚礼数据",
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SendIMCommonXmlMsg fail to BatchSendCommonMsg. err:%v", err)
        return err
    }
    return nil
}

func (m *ACLayer) SimpleSendTTAssistantText(ctx context.Context, toUid uint32, content, highlight, url string) error {
    _, err := m.imApiCli.SimpleSendTTAssistantText(ctx, toUid, content, highlight, url)
    return err
}

func (m *ACLayer) SimplePushToChannelUsers(ctx context.Context, recvUids []uint32, fromUid, channelId, channelMsgType uint32,
    content string, pbOptData []byte, isReliable bool) error {
    requestIds, err := m.channelMsgApi.SimplePushToUsers(ctx, recvUids, fromUid, channelId, channelMsgType, content, pbOptData, isReliable)
    for _, id := range requestIds {
        log.DebugWithCtx(ctx, "SimplePushToChannelUsers success. requestId:%d, fromUid:%d, channelId:%d, channelMsgType:%d, content:%s, pbOptData:%v", id, fromUid, channelId, channelMsgType, content, pbOptData)
    }
    return err
}

func (m *ACLayer) PushToUsers(ctx context.Context, userIDList []uint32, notification *pb.CompositiveNotification) error {
    return m.pushCli.PushToUsers(ctx, userIDList, notification)
}

func (m *ACLayer) BatchGetUserChannelId(ctx context.Context, uidList []uint32) (map[uint32]uint32, error) {
    userChannelIds, err := m.channelOlCli.BatchGetUserChannelId(ctx, &channelol_go.BatchGetUserChannelIdReq{UidList: uidList})
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetUserChannelId fail to BatchGetUserChannelId. uidList:%v, err:%v", uidList, err)
        return nil, err
    }

    return userChannelIds.GetResults(), nil
}
