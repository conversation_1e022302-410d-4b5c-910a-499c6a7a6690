package anti_corruption_layer

import (
    "context"
    "errors"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/pkg/protocol"
    channelPB "golang.52tt.com/protocol/app/channel"
    "golang.52tt.com/protocol/app/channel_wedding_logic"
    "golang.52tt.com/protocol/app/virtual_image_logic"
    "golang.52tt.com/protocol/common/status"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
    virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
    "golang.52tt.com/services/channel-wedding/internal/model/comm"
    "sort"
    "time"
)

// SendVirtualImageChangeMsg 发送虚拟形象变更消息
func (m *ACLayer) SendVirtualImageChangeMsg(ctx context.Context, opUid, cid uint32, uidList []uint32, uid2Pose map[uint32]*channel_wedding.UserWeddingPose) error {
    if cid == 0 || len(uidList) == 0 {
        return nil
    }

    userVIResp, err := m.virtualImageUser.BatchGetUserInuseItemInfo(ctx, &virtual_image_user.BatchGetUserInuseItemInfoReq{
        UidList: uidList,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "SendVirtualImageChangeMsg failed to BatchGetUserInuseItemInfo. opUid:%d, uidList:%v, err:%v",
            opUid, uidList, err)
        return err
    }

    uid2VI := make(map[uint32]*virtual_image_user.UserInuseItemInfo)
    for _, info := range userVIResp.GetUserInuseItemInfo() {
        uid2VI[info.GetUid()] = info
    }

    opt := &channel_wedding_logic.WeddingClothesChangeOpt{
        Cid:             cid,
        ServerTimeMs:    time.Now().UnixMilli(),
        ClothesInfoList: make([]*channel_wedding_logic.WeddingClothesInfo, 0, len(uidList)),
    }

    for _, uid := range uidList {
        userVI := uid2VI[uid]
        pose := uid2Pose[uid]

        clothesIdList := make([]uint32, 0)
        for _, item := range getUseItemListWithPose(userVI.GetItems(), pose) {
            clothesIdList = append(clothesIdList, item.GetCfgId())
        }

        opt.ClothesInfoList = append(opt.ClothesInfoList, &channel_wedding_logic.WeddingClothesInfo{
            Uid:           uid,
            ClothesIdList: clothesIdList,
            Orientation:   pose.GetOrientation(),
        })
    }

    data, e := proto.Marshal(opt)
    if e != nil {
        log.ErrorWithCtx(ctx, "SendVirtualImageChangeMsg marshal err:%v, %+v", e, opt)
        return e
    }

    err2 := m.SimplePushToChannel(ctx, opUid, cid,
        uint32(channelPB.ChannelMsgType_CHANNEL_WEDDING_MIC_USER_CLOTHES_CHANGE_MSG), "", data)
    if err2 != nil {
        log.ErrorWithCtx(ctx, "SendVirtualImageChangeMsg failed to SimplePushToChannel. err: %s, opUid:%v, info:%+v",
            err2.Error(), opUid, opt)
        return err2
    }

    log.DebugWithCtx(ctx, "SendVirtualImageChangeMsg success. opUid:%v, opt:%+v", opUid, opt)
    return nil
}

// GetVirtualImageResourceMap 批量获取虚拟形象物品配置
func (m *ACLayer) GetVirtualImageResourceMap(ctx context.Context, ids []uint32) (map[uint32]*virtual_image_resource.VirtualImageResourceInfo, error) {
    out := make(map[uint32]*virtual_image_resource.VirtualImageResourceInfo)
    if len(ids) == 0 {
        return out, nil
    }

    resp, err := m.virtualImageResource.GetVirtualImageResourcesByIds(ctx, &virtual_image_resource.GetVirtualImageResourcesByIdsRequest{
        Ids: ids,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetVirtualImageResourceMap failed to GetVirtualImageResourcesByIds. ids:%v, err:%v", ids, err)
        return out, err
    }

    for _, resource := range resp.GetResources() {
        out[resource.GetId()] = resource
    }

    return out, nil
}

// GetUserVirtualImageInuseMap 批量获取用户虚拟形象使用中的物品
func (m *ACLayer) GetUserVirtualImageInuseMap(ctx context.Context, uidList []uint32) (map[uint32][]*virtual_image_user.InuseItemInfo, error) {
    if len(uidList) == 0 {
        return nil, nil
    }

    resp, err := m.virtualImageUser.BatchGetUserInuseItemInfo(ctx, &virtual_image_user.BatchGetUserInuseItemInfoReq{
        UidList: uidList,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserVirtualImageInuseList failed to BatchGetUserInuseItemInfo. uid:%v, err:%v", uidList, err)
        return nil, err
    }

    out := make(map[uint32][]*virtual_image_user.InuseItemInfo, len(uidList))
    for _, info := range resp.GetUserInuseItemInfo() {
        out[info.GetUid()] = info.GetItems()
    }

    return out, nil
}

// CheckUserVirtualImageMicDisplay 检查用户是否开启虚拟形象麦位展示
func (m *ACLayer) CheckUserVirtualImageMicDisplay(ctx context.Context, uid uint32) (bool, error) {
    userVIResp, err := m.virtualImageUser.GetUserDisplaySwitch(ctx, &virtual_image_user.GetUserDisplaySwitchRequest{
        Uid: uid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckUserVirtualImageMicDisplay failed to GetUserDisplaySwitch. uid:%v, err:%v", uid, err)
        return false, err
    }

    for _, v := range userVIResp.GetDisplaySwitch() {
        if v.GetType() == uint32(virtual_image_logic.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_MIC) &&
            v.GetSwitchOn() {
            return true, nil
        }
    }

    return false, nil
}

// SendVirtualImageSuitWithUse 发送虚拟形象套装并使用
func (m *ACLayer) SendVirtualImageSuitWithUse(ctx context.Context, uid uint32, suitInfo *comm.UpgradeSuitInfo,
    allowUseSubCateMap map[uint32]bool) (sendLimit bool, err error) {
    if suitInfo == nil {
        log.WarnWithCtx(ctx, "SendVirtualImageSuitWithUse fail to SendVirtualImageSuitWithUse, uid:%v, suitInfo is nil", uid)
        return
    }

    itemIdList := suitInfo.ClothesList
    orderId := suitInfo.OrderId
    durationSec := suitInfo.Duration
    suitIcon := suitInfo.Icon

    if len(itemIdList) == 0 || len(orderId) == 0 || durationSec == 0 {
        log.ErrorWithCtx(ctx, "SendVirtualImageSuitWithUse fail to SendVirtualImageSuitWithUse, uid:%v, suitInfo:%+v", uid, suitInfo)
        return
    }

    itemList, err := m.GetVirtualImageResourceMap(ctx, itemIdList)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendVirtualImageSuitWithUse failed to GetVirtualImageResourceMap. uid:%v, suitInfo:%+v, err:%v", uid, suitInfo, err)
        return false, err
    }

    useList := make([]*virtual_image_user.ItemInfo, 0, len(itemList))
    giveSuitInfo := &virtual_image_user.SuitGiveInfo{
        OrderId:     fmt.Sprintf("%s", orderId),
        SuitName:    suitInfo.Name,
        SuitIcon:    suitIcon,
        DurationSec: int32(durationSec),
        Items:       make([]*virtual_image_user.ItemInfo, 0),
    }

    for _, item := range itemList {
        if item.GetSubCategory() == 0 {
            log.ErrorWithCtx(ctx, "SendVirtualImageSuitWithUse fail to GetVirtualImageResourceMap, uid:%v, item:%v", uid, item)
            continue
        }
        giveSuitInfo.Items = append(giveSuitInfo.Items, &virtual_image_user.ItemInfo{
            CfgId:       item.GetId(),
            SubCategory: item.GetSubCategory(),
        })

        useList = append(useList, &virtual_image_user.ItemInfo{
            CfgId:       item.GetId(),
            SubCategory: item.GetSubCategory(),
        })
    }

    if len(giveSuitInfo.Items) == 0 {
        log.ErrorWithCtx(ctx, "SendVirtualImageSuitWithUse fail to GiveVirtualImageToUser, uid:%v, suitInfo:%+v", uid, suitInfo)
        return false, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "item cfg not exist")
    }

    if suitInfo.RemainDaysLimit > 0 {
        // 检查用户剩余天数限制
        sendLimit, err = m.checkUserRemainDaysLimit(ctx, uid, suitInfo.RemainDaysLimit, itemIdList)
        if err != nil {
            log.ErrorWithCtx(ctx, "SendVirtualImageSuitWithUse failed to checkUserRemainDaysLimit. uid:%v, suitInfo:%+v, err:%v", uid, suitInfo, err)
            return false, err
        }
    }

    if !sendLimit {
        // 发放
        _, err = m.virtualImageUser.GiveVirtualImageToUser(ctx, &virtual_image_user.GiveVirtualImageToUserReq{
            Uid:         uid,
            Source:      uint32(virtual_image_user.VirtualImageGainSource_VIRTUAL_IMAGE_GAIN_WEDDING),
            OutsideTime: time.Now().Unix(),
            Suits:       []*virtual_image_user.SuitGiveInfo{giveSuitInfo},
            SourceDesc:  "婚礼房",
        })
        if err != nil && protocol.ToServerError(err).Code() != status.ErrVirtualAvatarOrderidExist {
            log.ErrorWithCtx(ctx, "SendVirtualImageSuitWithUse fail to GiveVirtualImageToUser, uid:%v, order:%v, err:%v",
                uid, orderId, err)
            return false, err
        }
    } else {
        // 助手消息提醒
        content := fmt.Sprintf("您已拥有相同套装，剩余天数大于等于限制(%d天)，无需重复发放", suitInfo.RemainDaysLimit)
        err = m.SimpleSendTTAssistantText(ctx, uid, content, "", "")
        if err != nil {
            log.WarnWithCtx(ctx, "SendVirtualImageSuitWithUse fail to SimpleSendTTAssistantText, uid:%v, err:%v", uid, err)
        }
    }

    if suitInfo.InUse {
        // 佩戴
        err = m.setUserVirtualImageInUse(ctx, uid, useList, allowUseSubCateMap)
        if err != nil {
            log.ErrorWithCtx(ctx, "SendVirtualImageSuitWithUse fail to setUserVirtualImageInUse, uid:%v, err:%v", uid, err)
            return false, err
        }
    }

    log.InfoWithCtx(ctx, "SendVirtualImageSuitWithUse success. uid:%v, sendLimit:%v, order:%v, suitInfo:%+v", uid, sendLimit, orderId, suitInfo)
    return sendLimit, nil
}

func (m *ACLayer) checkUserRemainDaysLimit(ctx context.Context, uid, remainDaysLimit uint32, itemIds []uint32) (bool, error) {
    if remainDaysLimit == 0 {
        return false, nil
    }

    sendSuitKey := genSuitUnitKey(itemIds)
    resp, err := m.virtualImageUser.GetUserVirtualImageList(ctx, &virtual_image_user.GetUserVirtualImageListReq{
        Uid: uid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "checkUserRemainDaysLimit failed to GetUserVirtualImageList. uid:%v, err:%v", uid, err)
        return false, err
    }

    now := time.Now().Unix()
    for _, suit := range resp.GetSuits() {
        idList := make([]uint32, 0, len(suit.GetItems()))
        for _, item := range suit.GetItems() {
            idList = append(idList, item.GetCfgId())
        }
        key := genSuitUnitKey(idList)
        if key != sendSuitKey {
            continue
        }

        if suit.GetExpireTime() >= now + int64(remainDaysLimit)*86400 {
            // 用户已有相同套装且剩余天数大于等于限制
            return true, nil
        }
    }

    return false, nil
}

func genSuitUnitKey(itemIds []uint32) string {
    sort.SliceStable(itemIds, func(i, j int) bool {
        return itemIds[i] < itemIds[j]
    })
    return fmt.Sprintf("suit_unit_%v", itemIds)
}

// setUserVirtualImageInUse 使用用户虚拟形象物品
func (m *ACLayer) setUserVirtualImageInUse(ctx context.Context, uid uint32, itemList []*virtual_image_user.ItemInfo, allowUseSubCateMap map[uint32]bool) error {
    useList := make([]*virtual_image_user.ItemInfo, 0, len(itemList))
    useList = append(useList, itemList...)

    var isFullUpdate bool
    if len(allowUseSubCateMap) > 0 {
        userVIResp, err := m.virtualImageUser.BatchGetUserInuseItemInfo(ctx, &virtual_image_user.BatchGetUserInuseItemInfoReq{
            UidList: []uint32{uid},
        })
        if err != nil {
            log.ErrorWithCtx(ctx, "setUserVirtualImageInUse failed to BatchGetUserInuseItemInfo. uid:%v, err:%v", uid, err)
            return err
        }

        userItems := userVIResp.GetUserInuseItemInfo()[0].GetItems()
        for _, item := range matchAllowSubCateItemList(allowUseSubCateMap, userItems) {
            useList = append(useList, &virtual_image_user.ItemInfo{
                CfgId:       item.GetCfgId(),
                SubCategory: item.GetSubCategory(),
            })
        }

        isFullUpdate = true
    }

    _, err := m.virtualImageUser.SetUserVirtualImageInUse(ctx, &virtual_image_user.SetUserVirtualImageInUseReq{
        Uid:          uid,
        Items:        useList,
        IsFullUpdate: isFullUpdate, // 是否全量覆盖更新
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "setUserVirtualImageInUse fail to SetUserVirtualImageInUse, uid:%v, err:%v", uid, err)
        return err
    }

    return nil
}

// UseVirtualImageSuit 使用虚拟形象套装
func (m *ACLayer) UseVirtualImageSuit(ctx context.Context, uid uint32, itemIdList []uint32, allowUseSubCateMap map[uint32]bool) error {
    itemList, err := m.GetVirtualImageResourceMap(ctx, itemIdList)
    if err != nil {
        log.ErrorWithCtx(ctx, "UseVirtualImageSuit failed to GetVirtualImageResourceMap. uid:%v, itemIdList:%v, err:%v", uid, itemIdList, err)
        return err
    }

    useList := make([]*virtual_image_user.ItemInfo, 0, len(itemList))
    for _, item := range itemList {
        if item.GetSubCategory() == 0 {
            log.ErrorWithCtx(ctx, "UseVirtualImageSuit fail to GetVirtualImageResourceMap, uid:%v, item:%v", uid, item)
            continue
        }

        useList = append(useList, &virtual_image_user.ItemInfo{
            CfgId:       item.GetId(),
            SubCategory: item.GetSubCategory(),
        })
    }

    err = m.setUserVirtualImageInUse(ctx, uid, useList, allowUseSubCateMap)
    if err != nil {
        log.ErrorWithCtx(ctx, "UseVirtualImageSuit fail to setUserVirtualImageInUse, uid:%v, err:%v", uid, err)
        return err
    }

    log.InfoWithCtx(ctx, "UseVirtualImageSuit success. uid:%v, itemList:%v, allowUseSubCateMap:%+v", uid, itemIdList, allowUseSubCateMap)
    return nil
}

// matchAllowSubCateItemList 匹配用户使用的物品中允许在婚礼中使用的子分类
func matchAllowSubCateItemList(allowUseSubCateMap map[uint32]bool, useItems []*virtual_image_user.InuseItemInfo) []*virtual_image_user.InuseItemInfo {
    itemList := make([]*virtual_image_user.InuseItemInfo, 0)
    for _, item := range useItems {
        if _, ok := allowUseSubCateMap[item.GetSubCategory()]; !ok {
            continue
        }

        itemList = append(itemList, item)
    }
    return itemList
}

// SendVirtualImageItems 发放虚拟形象
func (m *ACLayer) SendVirtualImageItems(ctx context.Context, uid, durationSec uint32, giveItemList []*comm.GiveVAItemInfo) error {
    if uid == 0 || len(giveItemList) == 0 || durationSec == 0 {
        log.ErrorWithCtx(ctx, "SendVirtualImageItems fail, in Params invalid uid:%v, orderId:%v itemIdList:%v", uid, giveItemList)
        return nil
    }

    itemIdList := make([]uint32, 0, len(giveItemList))
    for _, item := range giveItemList {
        itemIdList = append(itemIdList, item.ItemId)
    }

    itemMap, err := m.GetVirtualImageResourceMap(ctx, itemIdList)
    if err != nil {
        log.ErrorWithCtx(ctx, "SendVirtualImageItems failed to GetVirtualImageResourceMap. uid:%v, itemIdList:%v, err:%v", uid, itemIdList, err)
        return err
    }

    itemList := make([]*virtual_image_user.ItemGiveInfo, 0)
    for _, item := range giveItemList {
        itemInfo, ok := itemMap[item.ItemId]
        if !ok {
            log.ErrorWithCtx(ctx, "SendVirtualImageItems failed to GetVirtualImageResourceMap. uid:%v, itemId:%v, item not exist", uid, item.ItemId, err)
            return errors.New("item not exist")
        }
        if itemInfo.GetSubCategory() == 0 {
            log.ErrorWithCtx(ctx, "SendVirtualImageItems fail to GetVirtualImageResourceMap, uid:%v, item:%v", uid, item)
            continue
        }
        itemList = append(itemList, &virtual_image_user.ItemGiveInfo{
            OrderId:     item.OrderId,
            DurationSec: int32(durationSec),
            Item: &virtual_image_user.ItemInfo{
                CfgId:       itemInfo.GetId(),
                SubCategory: itemInfo.GetSubCategory(),
            },
        })
    }

    if len(itemList) == 0 {
        log.ErrorWithCtx(ctx, "SendVirtualImageItems fail to GiveVirtualImageToUser, uid:%v, item:%v err", uid, giveItemList)
        return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "item cfg not exist")
    }

    // 发放
    _, err = m.virtualImageUser.GiveVirtualImageToUser(ctx, &virtual_image_user.GiveVirtualImageToUserReq{
        Uid:         uid,
        Source:      uint32(virtual_image_user.VirtualImageGainSource_VIRTUAL_IMAGE_GAIN_WEDDING),
        Items:       itemList,
        OutsideTime: time.Now().Unix(),
        SourceDesc:  "婚礼房",
    })
    if err != nil && protocol.ToServerError(err).Code() != status.ErrVirtualAvatarOrderidExist {
        log.ErrorWithCtx(ctx, "SendVirtualImageItems fail to GiveVirtualImageToUser, uid:%v, giveItemList:%v, err:%v",
            uid, giveItemList, err)
        return err
    }

    log.InfoWithCtx(ctx, "SendVirtualImageItems success. uid:%v, giveItemList:%+v", uid, giveItemList)
    return nil
}
