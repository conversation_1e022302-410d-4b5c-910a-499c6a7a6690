// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-wedding/internal/model/wedding-records/store (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	mysql "gitlab.ttyuyin.com/tyr/x/middleware/mysql"
	store "golang.52tt.com/services/channel-wedding/internal/model/wedding-records/store"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// AddSceneClipRecord mocks base method.
func (m *MockIStore) AddSceneClipRecord(arg0 context.Context, arg1 *store.SceneClipRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddSceneClipRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddSceneClipRecord indicates an expected call of AddSceneClipRecord.
func (mr *MockIStoreMockRecorder) AddSceneClipRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSceneClipRecord", reflect.TypeOf((*MockIStore)(nil).AddSceneClipRecord), arg0, arg1)
}

// AddWeddingRecord mocks base method.
func (m *MockIStore) AddWeddingRecord(arg0 context.Context, arg1 *store.WeddingRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddWeddingRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddWeddingRecord indicates an expected call of AddWeddingRecord.
func (mr *MockIStoreMockRecorder) AddWeddingRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddWeddingRecord", reflect.TypeOf((*MockIStore)(nil).AddWeddingRecord), arg0, arg1)
}

// BatchGetWeddingRecordByPlanId mocks base method.
func (m *MockIStore) BatchGetWeddingRecordByPlanId(arg0 context.Context, arg1 []uint32) ([]*store.WeddingRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetWeddingRecordByPlanId", arg0, arg1)
	ret0, _ := ret[0].([]*store.WeddingRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetWeddingRecordByPlanId indicates an expected call of BatchGetWeddingRecordByPlanId.
func (mr *MockIStoreMockRecorder) BatchGetWeddingRecordByPlanId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetWeddingRecordByPlanId", reflect.TypeOf((*MockIStore)(nil).BatchGetWeddingRecordByPlanId), arg0, arg1)
}

// Close mocks base method.
func (m *MockIStore) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockIStoreMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIStore)(nil).Close))
}

// GetLatestWeddingRecord mocks base method.
func (m *MockIStore) GetLatestWeddingRecord(arg0 context.Context, arg1, arg2 uint32) (*store.WeddingRecord, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestWeddingRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].(*store.WeddingRecord)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetLatestWeddingRecord indicates an expected call of GetLatestWeddingRecord.
func (mr *MockIStoreMockRecorder) GetLatestWeddingRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestWeddingRecord", reflect.TypeOf((*MockIStore)(nil).GetLatestWeddingRecord), arg0, arg1, arg2)
}

// GetLatestWeddingRecordByTime mocks base method.
func (m *MockIStore) GetLatestWeddingRecordByTime(arg0 context.Context, arg1, arg2 time.Time) ([]*store.WeddingRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestWeddingRecordByTime", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.WeddingRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestWeddingRecordByTime indicates an expected call of GetLatestWeddingRecordByTime.
func (mr *MockIStoreMockRecorder) GetLatestWeddingRecordByTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestWeddingRecordByTime", reflect.TypeOf((*MockIStore)(nil).GetLatestWeddingRecordByTime), arg0, arg1, arg2)
}

// GetRecordByTimeAndHappinessVal mocks base method.
func (m *MockIStore) GetRecordByTimeAndHappinessVal(arg0 context.Context, arg1 time.Time, arg2, arg3, arg4 uint32) ([]*store.WeddingRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecordByTimeAndHappinessVal", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*store.WeddingRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecordByTimeAndHappinessVal indicates an expected call of GetRecordByTimeAndHappinessVal.
func (mr *MockIStoreMockRecorder) GetRecordByTimeAndHappinessVal(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecordByTimeAndHappinessVal", reflect.TypeOf((*MockIStore)(nil).GetRecordByTimeAndHappinessVal), arg0, arg1, arg2, arg3, arg4)
}

// GetUserFirstWeddingRecord mocks base method.
func (m *MockIStore) GetUserFirstWeddingRecord(arg0 context.Context, arg1, arg2 uint32, arg3, arg4 time.Time) (*store.WeddingRecord, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserFirstWeddingRecord", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*store.WeddingRecord)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserFirstWeddingRecord indicates an expected call of GetUserFirstWeddingRecord.
func (mr *MockIStoreMockRecorder) GetUserFirstWeddingRecord(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserFirstWeddingRecord", reflect.TypeOf((*MockIStore)(nil).GetUserFirstWeddingRecord), arg0, arg1, arg2, arg3, arg4)
}

// GetUserMaxWeddingId mocks base method.
func (m *MockIStore) GetUserMaxWeddingId(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserMaxWeddingId", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserMaxWeddingId indicates an expected call of GetUserMaxWeddingId.
func (mr *MockIStoreMockRecorder) GetUserMaxWeddingId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserMaxWeddingId", reflect.TypeOf((*MockIStore)(nil).GetUserMaxWeddingId), arg0, arg1, arg2)
}

// GetUserSceneClipRecordList mocks base method.
func (m *MockIStore) GetUserSceneClipRecordList(arg0 context.Context, arg1, arg2 uint32) ([]*store.SceneClipRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSceneClipRecordList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.SceneClipRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSceneClipRecordList indicates an expected call of GetUserSceneClipRecordList.
func (mr *MockIStoreMockRecorder) GetUserSceneClipRecordList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSceneClipRecordList", reflect.TypeOf((*MockIStore)(nil).GetUserSceneClipRecordList), arg0, arg1, arg2)
}

// GetUserSceneClipRecordListByWeddingId mocks base method.
func (m *MockIStore) GetUserSceneClipRecordListByWeddingId(arg0 context.Context, arg1, arg2 uint32) ([]*store.SceneClipRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSceneClipRecordListByWeddingId", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*store.SceneClipRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSceneClipRecordListByWeddingId indicates an expected call of GetUserSceneClipRecordListByWeddingId.
func (mr *MockIStoreMockRecorder) GetUserSceneClipRecordListByWeddingId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSceneClipRecordListByWeddingId", reflect.TypeOf((*MockIStore)(nil).GetUserSceneClipRecordListByWeddingId), arg0, arg1, arg2)
}

// GetWeddingRecordByFinishAwardStatus mocks base method.
func (m *MockIStore) GetWeddingRecordByFinishAwardStatus(arg0 context.Context, arg1, arg2 time.Time, arg3, arg4 uint32) ([]*store.WeddingRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingRecordByFinishAwardStatus", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*store.WeddingRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingRecordByFinishAwardStatus indicates an expected call of GetWeddingRecordByFinishAwardStatus.
func (mr *MockIStoreMockRecorder) GetWeddingRecordByFinishAwardStatus(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingRecordByFinishAwardStatus", reflect.TypeOf((*MockIStore)(nil).GetWeddingRecordByFinishAwardStatus), arg0, arg1, arg2, arg3, arg4)
}

// TxUpdateFinishAwardStatus mocks base method.
func (m *MockIStore) TxUpdateFinishAwardStatus(arg0 context.Context, arg1 mysql.Tx, arg2, arg3, arg4 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TxUpdateFinishAwardStatus", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TxUpdateFinishAwardStatus indicates an expected call of TxUpdateFinishAwardStatus.
func (mr *MockIStoreMockRecorder) TxUpdateFinishAwardStatus(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TxUpdateFinishAwardStatus", reflect.TypeOf((*MockIStore)(nil).TxUpdateFinishAwardStatus), arg0, arg1, arg2, arg3, arg4)
}
