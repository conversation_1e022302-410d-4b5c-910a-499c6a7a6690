package store

import(
	context "context"
	mysql "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	time "time"
)

type IStore interface {
	AddSceneClipRecord(ctx context.Context, record *SceneClipRecord) error
	AddWeddingRecord(ctx context.Context, record *WeddingRecord) error
	BatchGetWeddingRecordByPlanId(ctx context.Context, planIdList []uint32) ([]*WeddingRecord,error)
	Close() error
	GetLatestWeddingRecord(ctx context.Context, uidA, uidB uint32) (*WeddingRecord,bool,error)
	GetLatestWeddingRecordByTime(ctx context.Context, beginTime, endTime time.Time) ([]*WeddingRecord,error)
	GetRecordByTimeAndHappinessVal(ctx context.Context, beginTime time.Time, cid, limit, minScore uint32) ([]*WeddingRecord,error)
	GetUserFirstWeddingRecord(ctx context.Context, uidA, uidB uint32, beginTime, endTime time.Time) (*WeddingRecord,bool,error)
	GetUserMaxWeddingId(ctx context.Context, uidA, uidB uint32) (uint32,error)
	GetUserSceneClipRecordList(ctx context.Context, uidA, uidB uint32) ([]*SceneClipRecord,error)
	GetUserSceneClipRecordListByWeddingId(ctx context.Context, weddingId, sceneTy uint32) ([]*SceneClipRecord,error)
	GetWeddingRecordByFinishAwardStatus(ctx context.Context, beginTime, endTime time.Time, status, limit uint32) ([]*WeddingRecord,error)
	TxUpdateFinishAwardStatus(ctx context.Context, tx mysql.Tx, weddingId, oldStatus, newStatus uint32) (bool,error)
}

