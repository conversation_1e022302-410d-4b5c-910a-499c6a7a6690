// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-wedding/internal/model/wedding-records (interfaces: IWeddingRecords)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	mysql "gitlab.ttyuyin.com/tyr/x/middleware/mysql"
	channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
	conf "golang.52tt.com/services/channel-wedding/internal/conf"
	comm "golang.52tt.com/services/channel-wedding/internal/model/comm"
)

// MockIWeddingRecords is a mock of IWeddingRecords interface.
type MockIWeddingRecords struct {
	ctrl     *gomock.Controller
	recorder *MockIWeddingRecordsMockRecorder
}

// MockIWeddingRecordsMockRecorder is the mock recorder for MockIWeddingRecords.
type MockIWeddingRecordsMockRecorder struct {
	mock *MockIWeddingRecords
}

// NewMockIWeddingRecords creates a new mock instance.
func NewMockIWeddingRecords(ctrl *gomock.Controller) *MockIWeddingRecords {
	mock := &MockIWeddingRecords{ctrl: ctrl}
	mock.recorder = &MockIWeddingRecordsMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIWeddingRecords) EXPECT() *MockIWeddingRecordsMockRecorder {
	return m.recorder
}

// AddWeddingRecord mocks base method.
func (m *MockIWeddingRecords) AddWeddingRecord(arg0 context.Context, arg1 uint32, arg2 *comm.WeddingRecordInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddWeddingRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddWeddingRecord indicates an expected call of AddWeddingRecord.
func (mr *MockIWeddingRecordsMockRecorder) AddWeddingRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddWeddingRecord", reflect.TypeOf((*MockIWeddingRecords)(nil).AddWeddingRecord), arg0, arg1, arg2)
}

// BatchGetWeddingHappiness mocks base method.
func (m *MockIWeddingRecords) BatchGetWeddingHappiness(arg0 context.Context, arg1 *channel_wedding.BatchGetWeddingHappinessRequest) (*channel_wedding.BatchGetWeddingHappinessResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetWeddingHappiness", arg0, arg1)
	ret0, _ := ret[0].(*channel_wedding.BatchGetWeddingHappinessResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetWeddingHappiness indicates an expected call of BatchGetWeddingHappiness.
func (mr *MockIWeddingRecordsMockRecorder) BatchGetWeddingHappiness(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetWeddingHappiness", reflect.TypeOf((*MockIWeddingRecords)(nil).BatchGetWeddingHappiness), arg0, arg1)
}

// DelWeddingRankKey mocks base method.
func (m *MockIWeddingRecords) DelWeddingRankKey(arg0 context.Context, arg1 uint32) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "DelWeddingRankKey", arg0, arg1)
}

// DelWeddingRankKey indicates an expected call of DelWeddingRankKey.
func (mr *MockIWeddingRecordsMockRecorder) DelWeddingRankKey(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelWeddingRankKey", reflect.TypeOf((*MockIWeddingRecords)(nil).DelWeddingRankKey), arg0, arg1)
}

// GetChannelWeddingRank mocks base method.
func (m *MockIWeddingRecords) GetChannelWeddingRank(arg0 context.Context, arg1 uint32) (*channel_wedding.GetChannelWeddingRankInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelWeddingRank", arg0, arg1)
	ret0, _ := ret[0].(*channel_wedding.GetChannelWeddingRankInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelWeddingRank indicates an expected call of GetChannelWeddingRank.
func (mr *MockIWeddingRecordsMockRecorder) GetChannelWeddingRank(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelWeddingRank", reflect.TypeOf((*MockIWeddingRecords)(nil).GetChannelWeddingRank), arg0, arg1)
}

// GetChannelWeddingRankFirstPlace mocks base method.
func (m *MockIWeddingRecords) GetChannelWeddingRankFirstPlace(arg0 context.Context, arg1 uint32) (*channel_wedding.GetChannelWeddingRankEntryInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelWeddingRankFirstPlace", arg0, arg1)
	ret0, _ := ret[0].(*channel_wedding.GetChannelWeddingRankEntryInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelWeddingRankFirstPlace indicates an expected call of GetChannelWeddingRankFirstPlace.
func (mr *MockIWeddingRecordsMockRecorder) GetChannelWeddingRankFirstPlace(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelWeddingRankFirstPlace", reflect.TypeOf((*MockIWeddingRecords)(nil).GetChannelWeddingRankFirstPlace), arg0, arg1)
}

// GetPendingWeddingAward mocks base method.
func (m *MockIWeddingRecords) GetPendingWeddingAward(arg0 context.Context, arg1, arg2 time.Time, arg3 uint32) ([]*comm.WeddingRecordInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPendingWeddingAward", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*comm.WeddingRecordInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPendingWeddingAward indicates an expected call of GetPendingWeddingAward.
func (mr *MockIWeddingRecordsMockRecorder) GetPendingWeddingAward(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPendingWeddingAward", reflect.TypeOf((*MockIWeddingRecords)(nil).GetPendingWeddingAward), arg0, arg1, arg2, arg3)
}

// GetUserWeddingCertificate mocks base method.
func (m *MockIWeddingRecords) GetUserWeddingCertificate(arg0 context.Context, arg1 *channel_wedding.GetUserWeddingCertificateReq) (*channel_wedding.WeddingCertificate, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserWeddingCertificate", arg0, arg1)
	ret0, _ := ret[0].(*channel_wedding.WeddingCertificate)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserWeddingCertificate indicates an expected call of GetUserWeddingCertificate.
func (mr *MockIWeddingRecordsMockRecorder) GetUserWeddingCertificate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserWeddingCertificate", reflect.TypeOf((*MockIWeddingRecords)(nil).GetUserWeddingCertificate), arg0, arg1)
}

// GetUserWeddingWeddingClips mocks base method.
func (m *MockIWeddingRecords) GetUserWeddingWeddingClips(arg0 context.Context, arg1, arg2 uint32) (*channel_wedding.GetUserWeddingWeddingClipsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserWeddingWeddingClips", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channel_wedding.GetUserWeddingWeddingClipsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserWeddingWeddingClips indicates an expected call of GetUserWeddingWeddingClips.
func (mr *MockIWeddingRecordsMockRecorder) GetUserWeddingWeddingClips(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserWeddingWeddingClips", reflect.TypeOf((*MockIWeddingRecords)(nil).GetUserWeddingWeddingClips), arg0, arg1, arg2)
}

// GetWeddingRecordByTimeRange mocks base method.
func (m *MockIWeddingRecords) GetWeddingRecordByTimeRange(arg0 context.Context, arg1 *channel_wedding.GetWeddingRecordByTimeRangeReq) (*channel_wedding.GetWeddingRecordByTimeRangeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingRecordByTimeRange", arg0, arg1)
	ret0, _ := ret[0].(*channel_wedding.GetWeddingRecordByTimeRangeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingRecordByTimeRange indicates an expected call of GetWeddingRecordByTimeRange.
func (mr *MockIWeddingRecordsMockRecorder) GetWeddingRecordByTimeRange(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingRecordByTimeRange", reflect.TypeOf((*MockIWeddingRecords)(nil).GetWeddingRecordByTimeRange), arg0, arg1)
}

// ReloadWeddingRank mocks base method.
func (m *MockIWeddingRecords) ReloadWeddingRank(arg0 context.Context, arg1 uint32, arg2 *conf.WeddingRankConf) ([]*channel_wedding.WeddingRankInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReloadWeddingRank", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*channel_wedding.WeddingRankInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReloadWeddingRank indicates an expected call of ReloadWeddingRank.
func (mr *MockIWeddingRecordsMockRecorder) ReloadWeddingRank(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReloadWeddingRank", reflect.TypeOf((*MockIWeddingRecords)(nil).ReloadWeddingRank), arg0, arg1, arg2)
}

// SaveWeddingScenePic mocks base method.
func (m *MockIWeddingRecords) SaveWeddingScenePic(arg0 context.Context, arg1 uint32, arg2 *comm.SaveWeddingScenePicReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveWeddingScenePic", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveWeddingScenePic indicates an expected call of SaveWeddingScenePic.
func (mr *MockIWeddingRecordsMockRecorder) SaveWeddingScenePic(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveWeddingScenePic", reflect.TypeOf((*MockIWeddingRecords)(nil).SaveWeddingScenePic), arg0, arg1, arg2)
}

// Stop mocks base method.
func (m *MockIWeddingRecords) Stop() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Stop")
}

// Stop indicates an expected call of Stop.
func (mr *MockIWeddingRecordsMockRecorder) Stop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockIWeddingRecords)(nil).Stop))
}

// TestWeddingScenePicIm mocks base method.
func (m *MockIWeddingRecords) TestWeddingScenePicIm(arg0 context.Context, arg1 *channel_wedding.TestWeddingScenePicImReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestWeddingScenePicIm", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// TestWeddingScenePicIm indicates an expected call of TestWeddingScenePicIm.
func (mr *MockIWeddingRecordsMockRecorder) TestWeddingScenePicIm(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestWeddingScenePicIm", reflect.TypeOf((*MockIWeddingRecords)(nil).TestWeddingScenePicIm), arg0, arg1)
}

// TxUpdateFinishAwardStatus mocks base method.
func (m *MockIWeddingRecords) TxUpdateFinishAwardStatus(arg0 context.Context, arg1 mysql.Txx, arg2, arg3, arg4 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TxUpdateFinishAwardStatus", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TxUpdateFinishAwardStatus indicates an expected call of TxUpdateFinishAwardStatus.
func (mr *MockIWeddingRecordsMockRecorder) TxUpdateFinishAwardStatus(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TxUpdateFinishAwardStatus", reflect.TypeOf((*MockIWeddingRecords)(nil).TxUpdateFinishAwardStatus), arg0, arg1, arg2, arg3, arg4)
}
