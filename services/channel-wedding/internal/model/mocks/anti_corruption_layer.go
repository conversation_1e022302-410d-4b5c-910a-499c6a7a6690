// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-wedding/internal/model/anti-corruption-layer (interfaces: IACLayer)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	app "golang.52tt.com/protocol/app"
	im "golang.52tt.com/protocol/app/im"
	channel_operate_permission_checker "golang.52tt.com/protocol/services/channel-operate-permission-checker"
	channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
	channel_wedding_conf "golang.52tt.com/protocol/services/channel-wedding-conf"
	channel_wedding_plan "golang.52tt.com/protocol/services/channel-wedding-plan"
	fellow_level_award "golang.52tt.com/protocol/services/fellow-level-award"
	fellow_svr "golang.52tt.com/protocol/services/fellow-svr"
	push_notification "golang.52tt.com/protocol/services/push-notification/v2"
	virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
	virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
	anti_corruption_layer "golang.52tt.com/services/channel-wedding/internal/model/anti-corruption-layer"
	comm "golang.52tt.com/services/channel-wedding/internal/model/comm"
)

// MockIACLayer is a mock of IACLayer interface.
type MockIACLayer struct {
	ctrl     *gomock.Controller
	recorder *MockIACLayerMockRecorder
}

// MockIACLayerMockRecorder is the mock recorder for MockIACLayer.
type MockIACLayerMockRecorder struct {
	mock *MockIACLayer
}

// NewMockIACLayer creates a new mock instance.
func NewMockIACLayer(ctrl *gomock.Controller) *MockIACLayer {
	mock := &MockIACLayer{ctrl: ctrl}
	mock.recorder = &MockIACLayerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIACLayer) EXPECT() *MockIACLayerMockRecorder {
	return m.recorder
}

// AwardDress mocks base method.
func (m *MockIACLayer) AwardDress(arg0 context.Context, arg1, arg2, arg3, arg4, arg5 uint32, arg6, arg7 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AwardDress", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
	ret0, _ := ret[0].(error)
	return ret0
}

// AwardDress indicates an expected call of AwardDress.
func (mr *MockIACLayerMockRecorder) AwardDress(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AwardDress", reflect.TypeOf((*MockIACLayer)(nil).AwardDress), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
}

// BatchGetUserChannelId mocks base method.
func (m *MockIACLayer) BatchGetUserChannelId(arg0 context.Context, arg1 []uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserChannelId", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserChannelId indicates an expected call of BatchGetUserChannelId.
func (mr *MockIACLayerMockRecorder) BatchGetUserChannelId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserChannelId", reflect.TypeOf((*MockIACLayer)(nil).BatchGetUserChannelId), arg0, arg1)
}

// CheckUserVirtualImageMicDisplay mocks base method.
func (m *MockIACLayer) CheckUserVirtualImageMicDisplay(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUserVirtualImageMicDisplay", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUserVirtualImageMicDisplay indicates an expected call of CheckUserVirtualImageMicDisplay.
func (mr *MockIACLayerMockRecorder) CheckUserVirtualImageMicDisplay(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUserVirtualImageMicDisplay", reflect.TypeOf((*MockIACLayer)(nil).CheckUserVirtualImageMicDisplay), arg0, arg1)
}

// CheckUsersInChannel mocks base method.
func (m *MockIACLayer) CheckUsersInChannel(arg0 context.Context, arg1 []uint32, arg2 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUsersInChannel", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUsersInChannel indicates an expected call of CheckUsersInChannel.
func (mr *MockIACLayerMockRecorder) CheckUsersInChannel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUsersInChannel", reflect.TypeOf((*MockIACLayer)(nil).CheckUsersInChannel), arg0, arg1, arg2)
}

// EndGameKickOutMic mocks base method.
func (m *MockIACLayer) EndGameKickOutMic(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EndGameKickOutMic", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// EndGameKickOutMic indicates an expected call of EndGameKickOutMic.
func (mr *MockIACLayerMockRecorder) EndGameKickOutMic(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EndGameKickOutMic", reflect.TypeOf((*MockIACLayer)(nil).EndGameKickOutMic), arg0, arg1)
}

// EndWeddingChairGame mocks base method.
func (m *MockIACLayer) EndWeddingChairGame(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EndWeddingChairGame", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// EndWeddingChairGame indicates an expected call of EndWeddingChairGame.
func (mr *MockIACLayerMockRecorder) EndWeddingChairGame(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EndWeddingChairGame", reflect.TypeOf((*MockIACLayer)(nil).EndWeddingChairGame), arg0, arg1, arg2)
}

// GetAllThemeAwardConfig mocks base method.
func (m *MockIACLayer) GetAllThemeAwardConfig(arg0 context.Context) (*channel_wedding_conf.GetThemeFinishedAwardCfgResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllThemeAwardConfig", arg0)
	ret0, _ := ret[0].(*channel_wedding_conf.GetThemeFinishedAwardCfgResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllThemeAwardConfig indicates an expected call of GetAllThemeAwardConfig.
func (mr *MockIACLayerMockRecorder) GetAllThemeAwardConfig(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllThemeAwardConfig", reflect.TypeOf((*MockIACLayer)(nil).GetAllThemeAwardConfig), arg0)
}

// GetFellowBindByUidPair mocks base method.
func (m *MockIACLayer) GetFellowBindByUidPair(arg0 context.Context, arg1, arg2 uint32) (*fellow_svr.FellowInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFellowBindByUidPair", arg0, arg1, arg2)
	ret0, _ := ret[0].(*fellow_svr.FellowInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFellowBindByUidPair indicates an expected call of GetFellowBindByUidPair.
func (mr *MockIACLayerMockRecorder) GetFellowBindByUidPair(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowBindByUidPair", reflect.TypeOf((*MockIACLayer)(nil).GetFellowBindByUidPair), arg0, arg1, arg2)
}

// GetMyWeddingInfo mocks base method.
func (m *MockIACLayer) GetMyWeddingInfo(arg0 context.Context, arg1 uint32) (*channel_wedding_plan.GetMyWeddingInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyWeddingInfo", arg0, arg1)
	ret0, _ := ret[0].(*channel_wedding_plan.GetMyWeddingInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyWeddingInfo indicates an expected call of GetMyWeddingInfo.
func (mr *MockIACLayerMockRecorder) GetMyWeddingInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyWeddingInfo", reflect.TypeOf((*MockIACLayer)(nil).GetMyWeddingInfo), arg0, arg1)
}

// GetOnMicUserList mocks base method.
func (m *MockIACLayer) GetOnMicUserList(arg0 context.Context, arg1 uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOnMicUserList", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOnMicUserList indicates an expected call of GetOnMicUserList.
func (mr *MockIACLayerMockRecorder) GetOnMicUserList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOnMicUserList", reflect.TypeOf((*MockIACLayer)(nil).GetOnMicUserList), arg0, arg1)
}

// GetUserProfile mocks base method.
func (m *MockIACLayer) GetUserProfile(arg0 context.Context, arg1 uint32) (*app.UserProfile, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserProfile", arg0, arg1)
	ret0, _ := ret[0].(*app.UserProfile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserProfile indicates an expected call of GetUserProfile.
func (mr *MockIACLayerMockRecorder) GetUserProfile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProfile", reflect.TypeOf((*MockIACLayer)(nil).GetUserProfile), arg0, arg1)
}

// GetUserProfileMap mocks base method.
func (m *MockIACLayer) GetUserProfileMap(arg0 context.Context, arg1 []uint32, arg2 bool) (map[uint32]*app.UserProfile, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserProfileMap", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]*app.UserProfile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserProfileMap indicates an expected call of GetUserProfileMap.
func (mr *MockIACLayerMockRecorder) GetUserProfileMap(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProfileMap", reflect.TypeOf((*MockIACLayer)(nil).GetUserProfileMap), arg0, arg1, arg2)
}

// GetUserVirtualImageInuseMap mocks base method.
func (m *MockIACLayer) GetUserVirtualImageInuseMap(arg0 context.Context, arg1 []uint32) (map[uint32][]*virtual_image_user.InuseItemInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserVirtualImageInuseMap", arg0, arg1)
	ret0, _ := ret[0].(map[uint32][]*virtual_image_user.InuseItemInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserVirtualImageInuseMap indicates an expected call of GetUserVirtualImageInuseMap.
func (mr *MockIACLayerMockRecorder) GetUserVirtualImageInuseMap(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserVirtualImageInuseMap", reflect.TypeOf((*MockIACLayer)(nil).GetUserVirtualImageInuseMap), arg0, arg1)
}

// GetVirtualImageResourceMap mocks base method.
func (m *MockIACLayer) GetVirtualImageResourceMap(arg0 context.Context, arg1 []uint32) (map[uint32]*virtual_image_resource.VirtualImageResourceInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualImageResourceMap", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*virtual_image_resource.VirtualImageResourceInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualImageResourceMap indicates an expected call of GetVirtualImageResourceMap.
func (mr *MockIACLayerMockRecorder) GetVirtualImageResourceMap(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualImageResourceMap", reflect.TypeOf((*MockIACLayer)(nil).GetVirtualImageResourceMap), arg0, arg1)
}

// GetWeddingPlanInfo mocks base method.
func (m *MockIACLayer) GetWeddingPlanInfo(arg0 context.Context, arg1 uint32) (*channel_wedding_plan.GetSimpleWeddingPlanInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingPlanInfo", arg0, arg1)
	ret0, _ := ret[0].(*channel_wedding_plan.GetSimpleWeddingPlanInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingPlanInfo indicates an expected call of GetWeddingPlanInfo.
func (mr *MockIACLayerMockRecorder) GetWeddingPlanInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingPlanInfo", reflect.TypeOf((*MockIACLayer)(nil).GetWeddingPlanInfo), arg0, arg1)
}

// GetWeddingThemeCfg mocks base method.
func (m *MockIACLayer) GetWeddingThemeCfg(arg0 context.Context, arg1 uint32) (*channel_wedding_conf.ThemeCfg, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingThemeCfg", arg0, arg1)
	ret0, _ := ret[0].(*channel_wedding_conf.ThemeCfg)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeddingThemeCfg indicates an expected call of GetWeddingThemeCfg.
func (mr *MockIACLayerMockRecorder) GetWeddingThemeCfg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingThemeCfg", reflect.TypeOf((*MockIACLayer)(nil).GetWeddingThemeCfg), arg0, arg1)
}

// KickCommUserOutMicSpace mocks base method.
func (m *MockIACLayer) KickCommUserOutMicSpace(arg0 context.Context, arg1 *channel_wedding.WeddingInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "KickCommUserOutMicSpace", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// KickCommUserOutMicSpace indicates an expected call of KickCommUserOutMicSpace.
func (mr *MockIACLayerMockRecorder) KickCommUserOutMicSpace(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KickCommUserOutMicSpace", reflect.TypeOf((*MockIACLayer)(nil).KickCommUserOutMicSpace), arg0, arg1)
}

// KickOldMvpOutMicSpace mocks base method.
func (m *MockIACLayer) KickOldMvpOutMicSpace(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "KickOldMvpOutMicSpace", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// KickOldMvpOutMicSpace indicates an expected call of KickOldMvpOutMicSpace.
func (mr *MockIACLayerMockRecorder) KickOldMvpOutMicSpace(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KickOldMvpOutMicSpace", reflect.TypeOf((*MockIACLayer)(nil).KickOldMvpOutMicSpace), arg0, arg1, arg2)
}

// PushBreakingNews mocks base method.
func (m *MockIACLayer) PushBreakingNews(arg0 context.Context, arg1, arg2 uint32, arg3 *channel_wedding.WeddingInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushBreakingNews", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushBreakingNews indicates an expected call of PushBreakingNews.
func (mr *MockIACLayerMockRecorder) PushBreakingNews(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushBreakingNews", reflect.TypeOf((*MockIACLayer)(nil).PushBreakingNews), arg0, arg1, arg2, arg3)
}

// PushToUsers mocks base method.
func (m *MockIACLayer) PushToUsers(arg0 context.Context, arg1 []uint32, arg2 *push_notification.CompositiveNotification) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushToUsers", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushToUsers indicates an expected call of PushToUsers.
func (mr *MockIACLayerMockRecorder) PushToUsers(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushToUsers", reflect.TypeOf((*MockIACLayer)(nil).PushToUsers), arg0, arg1, arg2)
}

// SendCPFellowLine mocks base method.
func (m *MockIACLayer) SendCPFellowLine(arg0 context.Context, arg1 *fellow_level_award.AddLevelAwardReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendCPFellowLine", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendCPFellowLine indicates an expected call of SendCPFellowLine.
func (mr *MockIACLayerMockRecorder) SendCPFellowLine(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendCPFellowLine", reflect.TypeOf((*MockIACLayer)(nil).SendCPFellowLine), arg0, arg1)
}

// SendChannelMsg mocks base method.
func (m *MockIACLayer) SendChannelMsg(arg0 context.Context, arg1, arg2 uint32, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendChannelMsg", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendChannelMsg indicates an expected call of SendChannelMsg.
func (mr *MockIACLayerMockRecorder) SendChannelMsg(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendChannelMsg", reflect.TypeOf((*MockIACLayer)(nil).SendChannelMsg), arg0, arg1, arg2, arg3)
}

// SendCommonShelfPresent mocks base method.
func (m *MockIACLayer) SendCommonShelfPresent(arg0 context.Context, arg1 *anti_corruption_layer.SendPresentReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendCommonShelfPresent", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendCommonShelfPresent indicates an expected call of SendCommonShelfPresent.
func (mr *MockIACLayerMockRecorder) SendCommonShelfPresent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendCommonShelfPresent", reflect.TypeOf((*MockIACLayer)(nil).SendCommonShelfPresent), arg0, arg1)
}

// SendGroupPhotoPosChangeMsg mocks base method.
func (m *MockIACLayer) SendGroupPhotoPosChangeMsg(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendGroupPhotoPosChangeMsg", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendGroupPhotoPosChangeMsg indicates an expected call of SendGroupPhotoPosChangeMsg.
func (mr *MockIACLayerMockRecorder) SendGroupPhotoPosChangeMsg(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendGroupPhotoPosChangeMsg", reflect.TypeOf((*MockIACLayer)(nil).SendGroupPhotoPosChangeMsg), arg0, arg1, arg2, arg3)
}

// SendGuestEnterRoomMsg mocks base method.
func (m *MockIACLayer) SendGuestEnterRoomMsg(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendGuestEnterRoomMsg", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendGuestEnterRoomMsg indicates an expected call of SendGuestEnterRoomMsg.
func (mr *MockIACLayerMockRecorder) SendGuestEnterRoomMsg(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendGuestEnterRoomMsg", reflect.TypeOf((*MockIACLayer)(nil).SendGuestEnterRoomMsg), arg0, arg1, arg2, arg3, arg4)
}

// SendHappinessChangeMsg mocks base method.
func (m *MockIACLayer) SendHappinessChangeMsg(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendHappinessChangeMsg", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendHappinessChangeMsg indicates an expected call of SendHappinessChangeMsg.
func (mr *MockIACLayerMockRecorder) SendHappinessChangeMsg(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendHappinessChangeMsg", reflect.TypeOf((*MockIACLayer)(nil).SendHappinessChangeMsg), arg0, arg1, arg2, arg3, arg4)
}

// SendIMCommonXmlMsg mocks base method.
func (m *MockIACLayer) SendIMCommonXmlMsg(arg0 context.Context, arg1 uint32, arg2 []uint32, arg3 string, arg4 *im.IMCommonXmlMsg, arg5 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendIMCommonXmlMsg", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendIMCommonXmlMsg indicates an expected call of SendIMCommonXmlMsg.
func (mr *MockIACLayerMockRecorder) SendIMCommonXmlMsg(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendIMCommonXmlMsg", reflect.TypeOf((*MockIACLayer)(nil).SendIMCommonXmlMsg), arg0, arg1, arg2, arg3, arg4, arg5)
}

// SendIMMsgAsync mocks base method.
func (m *MockIACLayer) SendIMMsgAsync(arg0 context.Context, arg1 uint32, arg2 []uint32, arg3, arg4, arg5 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendIMMsgAsync", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendIMMsgAsync indicates an expected call of SendIMMsgAsync.
func (mr *MockIACLayerMockRecorder) SendIMMsgAsync(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendIMMsgAsync", reflect.TypeOf((*MockIACLayer)(nil).SendIMMsgAsync), arg0, arg1, arg2, arg3, arg4, arg5)
}

// SendLevelChangeMsg mocks base method.
func (m *MockIACLayer) SendLevelChangeMsg(arg0 context.Context, arg1 *channel_wedding.WeddingInfo, arg2 uint32, arg3 map[uint32][]uint32, arg4 []*channel_wedding.UserWeddingPose) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendLevelChangeMsg", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendLevelChangeMsg indicates an expected call of SendLevelChangeMsg.
func (mr *MockIACLayerMockRecorder) SendLevelChangeMsg(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendLevelChangeMsg", reflect.TypeOf((*MockIACLayer)(nil).SendLevelChangeMsg), arg0, arg1, arg2, arg3, arg4)
}

// SendMvpSettlementMsg mocks base method.
func (m *MockIACLayer) SendMvpSettlementMsg(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMvpSettlementMsg", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMvpSettlementMsg indicates an expected call of SendMvpSettlementMsg.
func (mr *MockIACLayerMockRecorder) SendMvpSettlementMsg(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMvpSettlementMsg", reflect.TypeOf((*MockIACLayer)(nil).SendMvpSettlementMsg), arg0, arg1, arg2, arg3, arg4)
}

// SendMvpUserChangeMsg mocks base method.
func (m *MockIACLayer) SendMvpUserChangeMsg(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMvpUserChangeMsg", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMvpUserChangeMsg indicates an expected call of SendMvpUserChangeMsg.
func (mr *MockIACLayerMockRecorder) SendMvpUserChangeMsg(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMvpUserChangeMsg", reflect.TypeOf((*MockIACLayer)(nil).SendMvpUserChangeMsg), arg0, arg1, arg2, arg3)
}

// SendPresentToUser mocks base method.
func (m *MockIACLayer) SendPresentToUser(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendPresentToUser", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendPresentToUser indicates an expected call of SendPresentToUser.
func (mr *MockIACLayerMockRecorder) SendPresentToUser(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPresentToUser", reflect.TypeOf((*MockIACLayer)(nil).SendPresentToUser), arg0, arg1, arg2, arg3, arg4)
}

// SendReceivePresentValTopChangeMsg mocks base method.
func (m *MockIACLayer) SendReceivePresentValTopChangeMsg(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendReceivePresentValTopChangeMsg", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendReceivePresentValTopChangeMsg indicates an expected call of SendReceivePresentValTopChangeMsg.
func (mr *MockIACLayerMockRecorder) SendReceivePresentValTopChangeMsg(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendReceivePresentValTopChangeMsg", reflect.TypeOf((*MockIACLayer)(nil).SendReceivePresentValTopChangeMsg), arg0, arg1, arg2, arg3, arg4)
}

// SendSceneNotifyMsg mocks base method.
func (m *MockIACLayer) SendSceneNotifyMsg(arg0 context.Context, arg1 *anti_corruption_layer.SceneNotifyMsgReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendSceneNotifyMsg", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendSceneNotifyMsg indicates an expected call of SendSceneNotifyMsg.
func (mr *MockIACLayerMockRecorder) SendSceneNotifyMsg(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendSceneNotifyMsg", reflect.TypeOf((*MockIACLayer)(nil).SendSceneNotifyMsg), arg0, arg1)
}

// SendStageChangeMsg mocks base method.
func (m *MockIACLayer) SendStageChangeMsg(arg0 context.Context, arg1 uint32, arg2 *channel_wedding.WeddingInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendStageChangeMsg", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendStageChangeMsg indicates an expected call of SendStageChangeMsg.
func (mr *MockIACLayerMockRecorder) SendStageChangeMsg(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendStageChangeMsg", reflect.TypeOf((*MockIACLayer)(nil).SendStageChangeMsg), arg0, arg1, arg2)
}

// SendUserPresentCountChangeMsg mocks base method.
func (m *MockIACLayer) SendUserPresentCountChangeMsg(arg0 context.Context, arg1, arg2, arg3, arg4 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendUserPresentCountChangeMsg", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendUserPresentCountChangeMsg indicates an expected call of SendUserPresentCountChangeMsg.
func (mr *MockIACLayerMockRecorder) SendUserPresentCountChangeMsg(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendUserPresentCountChangeMsg", reflect.TypeOf((*MockIACLayer)(nil).SendUserPresentCountChangeMsg), arg0, arg1, arg2, arg3, arg4)
}

// SendVirtualImageChangeMsg mocks base method.
func (m *MockIACLayer) SendVirtualImageChangeMsg(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32, arg4 map[uint32]*channel_wedding.UserWeddingPose) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendVirtualImageChangeMsg", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendVirtualImageChangeMsg indicates an expected call of SendVirtualImageChangeMsg.
func (mr *MockIACLayerMockRecorder) SendVirtualImageChangeMsg(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendVirtualImageChangeMsg", reflect.TypeOf((*MockIACLayer)(nil).SendVirtualImageChangeMsg), arg0, arg1, arg2, arg3, arg4)
}

// SendVirtualImageItems mocks base method.
func (m *MockIACLayer) SendVirtualImageItems(arg0 context.Context, arg1, arg2 uint32, arg3 []*comm.GiveVAItemInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendVirtualImageItems", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendVirtualImageItems indicates an expected call of SendVirtualImageItems.
func (mr *MockIACLayerMockRecorder) SendVirtualImageItems(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendVirtualImageItems", reflect.TypeOf((*MockIACLayer)(nil).SendVirtualImageItems), arg0, arg1, arg2, arg3)
}

// SendVirtualImageSuitWithUse mocks base method.
func (m *MockIACLayer) SendVirtualImageSuitWithUse(arg0 context.Context, arg1 uint32, arg2 *comm.UpgradeSuitInfo, arg3 map[uint32]bool) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendVirtualImageSuitWithUse", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendVirtualImageSuitWithUse indicates an expected call of SendVirtualImageSuitWithUse.
func (mr *MockIACLayerMockRecorder) SendVirtualImageSuitWithUse(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendVirtualImageSuitWithUse", reflect.TypeOf((*MockIACLayer)(nil).SendVirtualImageSuitWithUse), arg0, arg1, arg2, arg3)
}

// SendWeddingAfterSurvey mocks base method.
func (m *MockIACLayer) SendWeddingAfterSurvey(arg0 context.Context, arg1, arg2, arg3, arg4, arg5 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendWeddingAfterSurvey", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendWeddingAfterSurvey indicates an expected call of SendWeddingAfterSurvey.
func (mr *MockIACLayerMockRecorder) SendWeddingAfterSurvey(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendWeddingAfterSurvey", reflect.TypeOf((*MockIACLayer)(nil).SendWeddingAfterSurvey), arg0, arg1, arg2, arg3, arg4, arg5)
}

// SendWeddingBridesmaidUpdateMsg mocks base method.
func (m *MockIACLayer) SendWeddingBridesmaidUpdateMsg(arg0 context.Context, arg1 uint32, arg2 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendWeddingBridesmaidUpdateMsg", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendWeddingBridesmaidUpdateMsg indicates an expected call of SendWeddingBridesmaidUpdateMsg.
func (mr *MockIACLayerMockRecorder) SendWeddingBridesmaidUpdateMsg(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendWeddingBridesmaidUpdateMsg", reflect.TypeOf((*MockIACLayer)(nil).SendWeddingBridesmaidUpdateMsg), arg0, arg1, arg2)
}

// SetMicStatus mocks base method.
func (m *MockIACLayer) SetMicStatus(arg0 context.Context, arg1, arg2 uint32, arg3 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMicStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetMicStatus indicates an expected call of SetMicStatus.
func (mr *MockIACLayerMockRecorder) SetMicStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMicStatus", reflect.TypeOf((*MockIACLayer)(nil).SetMicStatus), arg0, arg1, arg2, arg3)
}

// SimplePushToChannel mocks base method.
func (m *MockIACLayer) SimplePushToChannel(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 string, arg5 []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SimplePushToChannel", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// SimplePushToChannel indicates an expected call of SimplePushToChannel.
func (mr *MockIACLayerMockRecorder) SimplePushToChannel(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimplePushToChannel", reflect.TypeOf((*MockIACLayer)(nil).SimplePushToChannel), arg0, arg1, arg2, arg3, arg4, arg5)
}

// SimplePushToChannelUsers mocks base method.
func (m *MockIACLayer) SimplePushToChannelUsers(arg0 context.Context, arg1 []uint32, arg2, arg3, arg4 uint32, arg5 string, arg6 []byte, arg7 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SimplePushToChannelUsers", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
	ret0, _ := ret[0].(error)
	return ret0
}

// SimplePushToChannelUsers indicates an expected call of SimplePushToChannelUsers.
func (mr *MockIACLayerMockRecorder) SimplePushToChannelUsers(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimplePushToChannelUsers", reflect.TypeOf((*MockIACLayer)(nil).SimplePushToChannelUsers), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
}

// SimpleSendTTAssistantText mocks base method.
func (m *MockIACLayer) SimpleSendTTAssistantText(arg0 context.Context, arg1 uint32, arg2, arg3, arg4 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SimpleSendTTAssistantText", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SimpleSendTTAssistantText indicates an expected call of SimpleSendTTAssistantText.
func (mr *MockIACLayerMockRecorder) SimpleSendTTAssistantText(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimpleSendTTAssistantText", reflect.TypeOf((*MockIACLayer)(nil).SimpleSendTTAssistantText), arg0, arg1, arg2, arg3, arg4)
}

// Stop mocks base method.
func (m *MockIACLayer) Stop() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Stop")
}

// Stop indicates an expected call of Stop.
func (mr *MockIACLayerMockRecorder) Stop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockIACLayer)(nil).Stop))
}

// UpdateWeddingPlanStatus mocks base method.
func (m *MockIACLayer) UpdateWeddingPlanStatus(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWeddingPlanStatus", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWeddingPlanStatus indicates an expected call of UpdateWeddingPlanStatus.
func (mr *MockIACLayerMockRecorder) UpdateWeddingPlanStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWeddingPlanStatus", reflect.TypeOf((*MockIACLayer)(nil).UpdateWeddingPlanStatus), arg0, arg1)
}

// UseVirtualImageSuit mocks base method.
func (m *MockIACLayer) UseVirtualImageSuit(arg0 context.Context, arg1 uint32, arg2 []uint32, arg3 map[uint32]bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UseVirtualImageSuit", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UseVirtualImageSuit indicates an expected call of UseVirtualImageSuit.
func (mr *MockIACLayerMockRecorder) UseVirtualImageSuit(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UseVirtualImageSuit", reflect.TypeOf((*MockIACLayer)(nil).UseVirtualImageSuit), arg0, arg1, arg2, arg3)
}

// WeddingPlanMicCheckOperatePermission mocks base method.
func (m *MockIACLayer) WeddingPlanMicCheckOperatePermission(arg0 context.Context, arg1 *channel_operate_permission_checker.CheckOperatePermissionReq) (*channel_operate_permission_checker.CheckOperatePermissionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WeddingPlanMicCheckOperatePermission", arg0, arg1)
	ret0, _ := ret[0].(*channel_operate_permission_checker.CheckOperatePermissionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// WeddingPlanMicCheckOperatePermission indicates an expected call of WeddingPlanMicCheckOperatePermission.
func (mr *MockIACLayerMockRecorder) WeddingPlanMicCheckOperatePermission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WeddingPlanMicCheckOperatePermission", reflect.TypeOf((*MockIACLayer)(nil).WeddingPlanMicCheckOperatePermission), arg0, arg1)
}
