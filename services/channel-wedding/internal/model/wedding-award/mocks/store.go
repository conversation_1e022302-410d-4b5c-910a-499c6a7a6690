// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-wedding/internal/model/wedding-award/store (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	mysql "gitlab.ttyuyin.com/tyr/x/middleware/mysql"
	store "golang.52tt.com/services/channel-wedding/internal/model/wedding-award/store"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// BatchInsertAwardLog mocks base method.
func (m *MockIStore) BatchInsertAwardLog(arg0 context.Context, arg1 mysql.Txx, arg2 time.Time, arg3 []*store.AwardLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchInsertAwardLog", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchInsertAwardLog indicates an expected call of BatchInsertAwardLog.
func (mr *MockIStoreMockRecorder) BatchInsertAwardLog(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchInsertAwardLog", reflect.TypeOf((*MockIStore)(nil).BatchInsertAwardLog), arg0, arg1, arg2, arg3)
}

// Close mocks base method.
func (m *MockIStore) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockIStoreMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIStore)(nil).Close))
}

// GetPendingAwardLog mocks base method.
func (m *MockIStore) GetPendingAwardLog(arg0 context.Context, arg1, arg2 time.Time, arg3 uint32) ([]*store.AwardLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPendingAwardLog", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*store.AwardLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPendingAwardLog indicates an expected call of GetPendingAwardLog.
func (mr *MockIStoreMockRecorder) GetPendingAwardLog(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPendingAwardLog", reflect.TypeOf((*MockIStore)(nil).GetPendingAwardLog), arg0, arg1, arg2, arg3)
}

// Transaction mocks base method.
func (m *MockIStore) Transaction(arg0 context.Context, arg1 func(mysql.Txx) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Transaction", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Transaction indicates an expected call of Transaction.
func (mr *MockIStoreMockRecorder) Transaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Transaction", reflect.TypeOf((*MockIStore)(nil).Transaction), arg0, arg1)
}

// UpdateAwardLogStatus mocks base method.
func (m *MockIStore) UpdateAwardLogStatus(arg0 context.Context, arg1 string, arg2, arg3 uint32, arg4 time.Time) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAwardLogStatus", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAwardLogStatus indicates an expected call of UpdateAwardLogStatus.
func (mr *MockIStoreMockRecorder) UpdateAwardLogStatus(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAwardLogStatus", reflect.TypeOf((*MockIStore)(nil).UpdateAwardLogStatus), arg0, arg1, arg2, arg3, arg4)
}
