package event

import (
    "context"
    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_scheme"
    "gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    channelPb "golang.52tt.com/protocol/app/channel"
    channelSchemePb "golang.52tt.com/protocol/app/channel-scheme"
    "golang.52tt.com/protocol/app/virtual_image_logic"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkasimplemic"
    virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
)

// HandleChannelMicEvent return error,（true-retry, false-no retry）
func (s *KafkaEvent) HandleChannelMicEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
    event := &kafkasimplemic.SimpleMicEvent{}
    err := proto.Unmarshal(msg.Value, event)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleChannelMicEvent Failed to proto.Unmarshal %+v", err)
        return err, false
    }

    if event.GetChannelType() != uint32(channelPb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) &&
        event.GetChannelType() != uint32(channelPb.ChannelType_TEMP_KH_CHANNEL_TYPE) {
        // 只关注pgc和临时房的事件
        return nil, false
    }

    log.DebugWithCtx(ctx, "HandleChannelMicEvent %+v", event)

    schemeResp, err := s.channelSchemeCli.GetCurChannelSchemeInfo(ctx, &channel_scheme.GetCurChannelSchemeInfoReq{
        Cid: event.GetChId(), ChannelType: event.GetChannelType(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleChannelMicEvent GetCurChannelSchemeInfo %+v, err:%v", event, err)
        return err, true
    }

    if schemeResp.GetSchemeInfo().GetSchemeDetailType() != uint32(channelSchemePb.SchemeDetailType_SCHEME_DETAIL_TYPE_WEDDING) {
        // 不是婚礼房
        return nil, false
    }

    uid := event.GetMicUserId()
    cid := event.GetChId()

    // 获取婚礼信息
    weddingInfo, err := s.weddingProcess.GetChannelWeddingInfo(ctx, uid, event.GetChId())
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleChannelMicEvent GetChannelWeddingInfo uid:%d, cid:%d, err:%v", uid, event.GetChId(), err)
        return err, true
    }

    switch event.GetEventType() {
    case uint32(kafkasimplemic.ESIMPLE_MIC_EVENT_TYPE_ENUM_SIMPLE_MIC_EV_HOLD):
        return s.handleUserHoldMic(ctx, weddingInfo, uid, cid, event.GetMicTargetId())

    case uint32(kafkasimplemic.ESIMPLE_MIC_EVENT_TYPE_ENUM_SIMPLE_MIC_EV_RELEASE):
        return s.handleUserReleaseMic(ctx, weddingInfo, uid, cid, event.GetMicTargetId())

    case uint32(kafkasimplemic.ESIMPLE_MIC_EVENT_TYPE_ENUM_SIMPLE_MIC_EV_CHANGE):
        return s.handleUserChangeMic(ctx, weddingInfo, uid, cid, event.GetMicOriginId(), event.GetMicTargetId())
    }

    return nil, false
}

// handleUserHoldMic 处理用户上麦事件
func (s *KafkaEvent) handleUserHoldMic(ctx context.Context, weddingInfo *channel_wedding.WeddingInfo, uid, cid, micId uint32) (error, bool) {
    // 用户婚礼房服装上麦
    err := s.weddingPose.NotifyUserWeddingPoseChange(ctx, uid, cid, []uint32{uid})
    if err != nil {
        log.ErrorWithCtx(ctx, "handleUserHoldMic NotifyUserWeddingPoseChange uid:%d, cid:%d, err:%v", uid, cid, err)
        return err, true
    }

    // 打开用户的虚拟形象麦位外显开关
    _, err = s.virtualImageUserCli.SetUserDisplaySwitch(ctx, &virtual_image_user.SetUserDisplaySwitchRequest{
        Uid: uid,
        DisplaySwitch: &virtual_image_user.DisplaySwitch{
            Type:     uint32(virtual_image_logic.VirtualImageDisplaySwitch_VIRTUAL_IMAGE_DISPLAY_SWITCH_MIC),
            SwitchOn: true,
        },
    })
    if err != nil {
        log.WarnWithCtx(ctx, "handleUserHoldMic SetUserDisplaySwitch uid:%d, cid:%d, err:%v", uid, cid, err)
    }

    if weddingInfo.GetWeddingId() == 0 {
        return nil, false
    }

    if !weddingInfo.GetThemeCfg().GetIsFreeTheme() {
        err = s.weddingPresentVal.OnUserHoldMic(ctx, cid, uint32(weddingInfo.GetWeddingId()), uid, micId)
        if err != nil {
            log.ErrorWithCtx(ctx, "handleUserHoldMic Failed to OnUserHoldMic uid:%d, cid:%d, micId:%d, err:%v", uid, cid, micId, err)
            return err, true
        }
    }

    log.InfoWithCtx(ctx, "handleUserHoldMic success. uid:%d, cid:%d, micId:%d, weddingId:%d", uid, cid, micId, weddingInfo.GetWeddingId())
    return nil, false
}

// handleUserReleaseMic 处理用户下麦事件
func (s *KafkaEvent) handleUserReleaseMic(ctx context.Context, weddingInfo *channel_wedding.WeddingInfo, uid, cid, micId uint32) (error, bool) {
    if weddingInfo.GetWeddingId() == 0 {
        return nil, false
    }

    if !weddingInfo.GetThemeCfg().GetIsFreeTheme() {
        err := s.weddingPresentVal.OnUserReleaseMic(ctx, cid, uint32(weddingInfo.GetWeddingId()), uid, micId)
        if err != nil {
            log.ErrorWithCtx(ctx, "handleUserReleaseMic Failed to OnUserReleaseMic uid:%d, cid:%d, micId:%d, err:%v", uid, cid, micId, err)
            return err, true
        }
    }

    log.InfoWithCtx(ctx, "handleUserReleaseMic success. uid:%d, cid:%d, micId:%d, weddingId:%d", uid, cid, micId, weddingInfo.GetWeddingId())
    return nil, false
}

func (s *KafkaEvent) handleUserChangeMic(ctx context.Context, weddingInfo *channel_wedding.WeddingInfo, uid, cid, fromMicId, toMicId uint32) (error, bool) {
    if weddingInfo.GetWeddingId() == 0 {
        return nil, false
    }

    if !weddingInfo.GetThemeCfg().GetIsFreeTheme() {
        err := s.weddingPresentVal.OnUserChangeMic(ctx, cid, uint32(weddingInfo.GetWeddingId()), uid, fromMicId, toMicId)
        if err != nil {
            log.ErrorWithCtx(ctx, "handleUserChangeMic Failed to OnUserChangeMic uid:%d, cid:%d, fromMicId:%d, toMicId:%d, err:%v", uid, cid, fromMicId, toMicId, err)
            return err, true
        }
    }

    log.InfoWithCtx(ctx, "handleUserChangeMic success. uid:%d, cid:%d, fromMicId:%d, toMicId:%d, weddingId:%d",
        uid, cid, fromMicId, toMicId, weddingInfo.GetWeddingId())
    return nil, false
}
