package event

import (
    "context"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_go"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_scheme"
    "gitlab.ttyuyin.com/tt-infra/middleware/kafka"
    "gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
    "golang.52tt.com/clients/channelol"
    "golang.52tt.com/pkg/config"
    "golang.52tt.com/pkg/log"
    virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
    "golang.52tt.com/services/channel-wedding/internal/conf"
    anti_corruption_layer "golang.52tt.com/services/channel-wedding/internal/model/anti-corruption-layer"
    user_present_value "golang.52tt.com/services/channel-wedding/internal/model/user-present-value"
    wedding_pose "golang.52tt.com/services/channel-wedding/internal/model/wedding-pose"
    wedding_process "golang.52tt.com/services/channel-wedding/internal/model/wedding-process"
)

type KafkaEvent struct {
    presentEventSub     subscriber.Subscriber
    channelMicEventSub  subscriber.Subscriber
    channelEventSub     subscriber.Subscriber
    virtualImageUserSub subscriber.Subscriber

    weddingProcess    wedding_process.IWeddingProcess
    weddingPose       wedding_pose.IWeddingPose
    acLayerMgr        anti_corruption_layer.IACLayer
    weddingPresentVal user_present_value.IUserPresentVal

    channelOlCli        channelol.IClient
    channelSchemeCli    channel_scheme.ChannelSchemeClient
    channelCli          channel_go.ChannelGoClient
    virtualImageUserCli virtual_image_user.VirtualImageUserClient
}

func (s *KafkaEvent) Close() {
    s.presentEventSub.Stop()
    s.channelMicEventSub.Stop()
    s.channelEventSub.Stop()
    s.virtualImageUserSub.Stop()
}

// NewKafkaEvent .
func NewKafkaEvent(sc *conf.StartConfig,
    weddingProcess wedding_process.IWeddingProcess,
    weddingPose wedding_pose.IWeddingPose,
    acLayerMgr anti_corruption_layer.IACLayer,
    weddingPresentVal user_present_value.IUserPresentVal) (ev *KafkaEvent, err error) {

    ev = &KafkaEvent{
        acLayerMgr:        acLayerMgr,
        weddingProcess:    weddingProcess,
        weddingPose:       weddingPose,
        weddingPresentVal: weddingPresentVal,
    }

    ev.channelOlCli = channelol.NewClient()
    ev.channelSchemeCli = channel_scheme.MustNewClient(context.Background())
    ev.channelCli = channel_go.MustNewClient(context.Background())
    ev.virtualImageUserCli = virtual_image_user.MustNewClient(context.Background())

    ev.presentEventSub, err = NewEventLinkSubscriber(sc.PresentKafkaConfig, subscriber.ProcessorContextFunc(ev.handlerEvent))
    if err != nil {
        log.Errorf("NewKafkaEvent failed,config:%+v,err:%v", sc, err)
        return nil, err
    }

    ev.channelMicEventSub, err = NewEventLinkSubscriber(sc.ChannelMicKafkaConfig, subscriber.ProcessorContextFunc(ev.HandleChannelMicEvent))
    if err != nil {
        log.Errorf("NewKafkaEvent failed,config:%+v,err:%v", sc, err)
        return nil, err
    }

    ev.channelEventSub, err = NewEventLinkSubscriber(sc.ChannelKafkaConfig, subscriber.ProcessorContextFunc(ev.HandleChannelEvent))
    if err != nil {
        log.Errorf("NewKafkaEvent failed,config:%+v,err:%v", sc, err)
        return nil, err
    }

    ev.virtualImageUserSub, err = NewEventLinkSubscriber(sc.VirtualImageUserKfk, subscriber.ProcessorContextFunc(ev.HandleVirtualImageUserEvent))
    if err != nil {
        log.Errorf("NewKafkaEvent failed,config:%+v,err:%v", sc, err)
        return nil, err
    }

    log.Infof("NewKafkaEvent success,config:%+v,", sc)
    return ev, nil
}

func NewEventLinkSubscriber(kfkCfg *config.KafkaConfig, processor subscriber.ProcessorContext) (subscriber.Subscriber, error) {
    cfg := kafka.DefaultConfig()
    cfg.ClientID = kfkCfg.ClientID
    cfg.Consumer.Offsets.Initial = kafka.OffsetNewest
    cfg.Consumer.Return.Errors = true

    kafkaSub, err := kafka.NewSubscriber(kfkCfg.BrokerList(), cfg, subscriber.WithMaxRetryTimes(3))
    if err != nil {
        log.ErrorWithCtx(nil, "NewEventLinkSubscriber failed,config:%+v,err:%v", kfkCfg, err)
        return nil, err
    }

    err = kafkaSub.SubscribeContext(kfkCfg.GroupID, []string{kfkCfg.Topics}, processor)
    if err != nil {
        log.ErrorWithCtx(nil, "NewEventLinkSubscriber failed,config:%+v,err:%v", kfkCfg, err)
        return nil, err
    }

    return kafkaSub, nil
}
