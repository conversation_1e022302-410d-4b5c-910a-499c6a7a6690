package event

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
    "sync"
)

const topicPresentEvent = "present_event_v2"

func (s *KafkaEvent) handlerEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	log.Debugf("handlerEvent topic:%s", msg.Topic)
	if msg.Topic == topicPresentEvent {
		return s.handlerPresentEvent(ctx, msg)
	}
	return nil, false
}

func (s *KafkaEvent) handlerPresentEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	presentEvent := &kafkapresent.PresentEvent{}
	err := proto.Unmarshal(msg.Value, presentEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, " handlerPresentEvent Failed to proto.Unmarshal err(%v)", err)
		return err, false
	}

	if presentEvent.GetPriceType() != 2 {
		return nil, false
	}

	log.DebugWithCtx(ctx, "handlerPresentEvent %+v", presentEvent)

	weddingInfo, err := s.weddingProcess.GetChannelWeddingInfo(ctx, presentEvent.GetUid(), presentEvent.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "handlerPresentEvent GetChannelWeddingInfo err(%v)", err)
		return err, false
	}

	if weddingInfo.GetWeddingId() == 0 {
		log.DebugWithCtx(ctx, "handlerPresentEvent weddingInfo.GetWeddingId() == 0 cid：%d", presentEvent.GetChannelId())
		return nil, false
	}

	if weddingInfo.GetStageInfo().GetCurrStage() == 0 {
		log.DebugWithCtx(ctx, "handlerPresentEvent weddingInfo.GetStageInfo().GetCurrStage() == 0, cid：%d", presentEvent.GetChannelId())
		return nil, false
	}

	if weddingInfo.GetThemeCfg().GetIsFreeTheme() {
		// 免费婚礼不计算幸福值
		return nil, false
	}
    wg := &sync.WaitGroup{}
    var goErr error

    totalPrice := presentEvent.GetPrice() * presentEvent.GetItemCount()
	if weddingInfo.GetGroom().GetUid() == presentEvent.GetTargetUid() ||
		weddingInfo.GetBride().GetUid() == presentEvent.GetTargetUid() {

        wg.Add(1)
        go func() {
            err := s.weddingProcess.AddHappiness(ctx, weddingInfo, totalPrice)
            if err != nil {
                log.ErrorWithCtx(ctx, "handlerPresentEvent AddHappiness %+v, err(%v)", presentEvent, err)
                goErr = err
            }

            wg.Done()
        }()
    }

    wg.Add(1)
    go func() {
        // 礼物计数器逻辑
        err := s.weddingPresentVal.AddPresentValue(ctx, presentEvent.GetChannelId(), uint32(weddingInfo.GetWeddingId()),
            presentEvent.GetUid(), presentEvent.GetTargetUid(), totalPrice)
        if err != nil {
            log.ErrorWithCtx(ctx, "handlerPresentEvent AddPresentValue %+v, err(%v)", presentEvent, err)
            goErr = err
        }

        wg.Done()
    }()

    wg.Wait()
    if goErr != nil {
        return goErr, true
    }

	return nil, false
}
