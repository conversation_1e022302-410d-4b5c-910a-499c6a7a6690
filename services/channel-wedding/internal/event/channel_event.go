package event

import (
    "context"
    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    "gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    channelPb "golang.52tt.com/protocol/app/channel"
    "golang.52tt.com/protocol/app/channel_wedding_logic"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkachannalevent"
)

func (s *KafkaEvent) HandleChannelEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
    event := &kafkachannalevent.ChSimpleEvent{}
    err := proto.Unmarshal(msg.Value, event)
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleChannelEvent Failed to proto.Unmarshal %+v", err)
        return err, false
    }

    if event.GetChannelType() != uint32(channelPb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) &&
        event.GetChannelType() != uint32(channelPb.ChannelType_TEMP_KH_CHANNEL_TYPE) {
        return nil, false
    }

    log.DebugWithCtx(ctx, "HandleChannelEvent %+v", event)

    uid := event.GetUid()
    weddingInfo, err := s.weddingProcess.GetChannelWeddingInfo(ctx, uid, event.GetChId())
    if err != nil {
        log.ErrorWithCtx(ctx, "HandleChannelEvent GetChannelWeddingInfo %+v, err:%v", event, err)
        return err, true
    }

    switch event.GetEventType() {
    case uint32(kafkachannalevent.ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_ENTER):
        return s.handleUserEnterRoom(ctx, weddingInfo, uid, event.GetChId())

    case uint32(kafkachannalevent.ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_LEAVE),
        uint32(kafkachannalevent.ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_EXPIRE_QUIT),
        uint32(kafkachannalevent.ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_EXPIRE_NOTIFY):
        return s.handleUserLeaveRoom(ctx, weddingInfo, uid, event.GetChId())
    }

    return nil, false
}

// handleUserLeaveRoom 处理用户离开房间事件
func (s *KafkaEvent) handleUserLeaveRoom(ctx context.Context, weddingInfo *channel_wedding.WeddingInfo, uid, cid uint32) (error, bool) {
    if weddingInfo.GetWeddingId() == 0 {
        return nil, false
    }

    if !weddingInfo.GetThemeCfg().GetIsFreeTheme() {
        err := s.weddingPresentVal.OnUserLeaveChannel(ctx, cid, uint32(weddingInfo.GetWeddingId()), uid)
        if err != nil {
            log.ErrorWithCtx(ctx, "handleUserLeaveRoom Failed to OnUserLeaveChannel uid:%d, cid:%d, err:%v", uid, cid, err)
            return err, true
        }
    }

    log.DebugWithCtx(ctx, "handleUserLeaveRoom uid:%d, cid:%d", uid, cid)
    return nil, false
}

// handleUserEnterRoom 处理用户进入房间事件
func (s *KafkaEvent) handleUserEnterRoom(ctx context.Context, weddingInfo *channel_wedding.WeddingInfo, uid, cid uint32) (error, bool) {
    if weddingInfo.GetWeddingId() == 0 {
        return nil, false
    }

    if !weddingInfo.GetThemeCfg().GetIsFreeTheme() {
        err := s.weddingPresentVal.OnUserEnterChannel(ctx, cid, uint32(weddingInfo.GetWeddingId()), uid)
        if err != nil {
            log.ErrorWithCtx(ctx, "handleUserEnterRoom Failed to OnUserEnterChannel uid:%d, cid:%d, err:%v", uid, cid, err)
            return err, true
        }
    }

    err := s.handleGuestEnterRoom(ctx, weddingInfo, uid, cid)
    if err != nil {
        log.ErrorWithCtx(ctx, "handleUserEnterRoom Failed to handleGuestEnterRoom uid:%d, cid:%d, err:%v", uid, cid, err)
        return err, false
    }

    log.DebugWithCtx(ctx, "handleUserEnterRoom success. uid:%d, cid:%d", uid, cid)
    return nil, false
}

// handleGuestEnterRoom 处理宾客进入房间事件
func (s *KafkaEvent) handleGuestEnterRoom(ctx context.Context, weddingInfo *channel_wedding.WeddingInfo, uid, cid uint32) error {
    if weddingInfo.GetStageInfo().GetCurrStage() != uint32(channel_wedding_logic.WeddingStage_WEDDING_STAGE_WELCOME_GUEST) {
        // 不是欢迎宾客阶段
        return nil
    }

    var guestType uint32
    if weddingInfo.GetGroom().GetUid() == uid {
        guestType = uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_GROOM)
    } else if weddingInfo.GetBride().GetUid() == uid {
        guestType = uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDE)
    } else {
        for _, v := range weddingInfo.GetBridesmaidManList() {
            if v == uid {
                guestType = uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDESMAID)
                break
            }
        }
    }

    if guestType == 0 {
        return nil
    }

    err := s.acLayerMgr.SendGuestEnterRoomMsg(ctx, uid, cid, uid, guestType)
    if err != nil {
        log.ErrorWithCtx(ctx, "handleGuestEnterRoom Failed to SendGuestEnterRoomMsg uid:%d, cid:%d, err:%v", uid, cid, err)
        return err
    }

    return nil
}
