// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-wedding/internal/conf (interfaces: IBusinessConfManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	conf "golang.52tt.com/services/channel-wedding/internal/conf"
)

// MockIBusinessConfManager is a mock of IBusinessConfManager interface.
type MockIBusinessConfManager struct {
	ctrl     *gomock.Controller
	recorder *MockIBusinessConfManagerMockRecorder
}

// MockIBusinessConfManagerMockRecorder is the mock recorder for MockIBusinessConfManager.
type MockIBusinessConfManagerMockRecorder struct {
	mock *MockIBusinessConfManager
}

// NewMockIBusinessConfManager creates a new mock instance.
func NewMockIBusinessConfManager(ctrl *gomock.Controller) *MockIBusinessConfManager {
	mock := &MockIBusinessConfManager{ctrl: ctrl}
	mock.recorder = &MockIBusinessConfManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIBusinessConfManager) EXPECT() *MockIBusinessConfManagerMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockIBusinessConfManager) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIBusinessConfManagerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIBusinessConfManager)(nil).Close))
}

// GetAfterFreeWeddingImXml mocks base method.
func (m *MockIBusinessConfManager) GetAfterFreeWeddingImXml() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAfterFreeWeddingImXml")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetAfterFreeWeddingImXml indicates an expected call of GetAfterFreeWeddingImXml.
func (mr *MockIBusinessConfManagerMockRecorder) GetAfterFreeWeddingImXml() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAfterFreeWeddingImXml", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetAfterFreeWeddingImXml))
}

// GetAllowChangeSubCategoryList mocks base method.
func (m *MockIBusinessConfManager) GetAllowChangeSubCategoryList() []uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllowChangeSubCategoryList")
	ret0, _ := ret[0].([]uint32)
	return ret0
}

// GetAllowChangeSubCategoryList indicates an expected call of GetAllowChangeSubCategoryList.
func (mr *MockIBusinessConfManagerMockRecorder) GetAllowChangeSubCategoryList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllowChangeSubCategoryList", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetAllowChangeSubCategoryList))
}

// GetAwardBusinessInfo mocks base method.
func (m *MockIBusinessConfManager) GetAwardBusinessInfo() (uint32, string) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardBusinessInfo")
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(string)
	return ret0, ret1
}

// GetAwardBusinessInfo indicates an expected call of GetAwardBusinessInfo.
func (mr *MockIBusinessConfManagerMockRecorder) GetAwardBusinessInfo() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardBusinessInfo", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetAwardBusinessInfo))
}

// GetAwardDressRiskAppId mocks base method.
func (m *MockIBusinessConfManager) GetAwardDressRiskAppId() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardDressRiskAppId")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetAwardDressRiskAppId indicates an expected call of GetAwardDressRiskAppId.
func (mr *MockIBusinessConfManagerMockRecorder) GetAwardDressRiskAppId() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardDressRiskAppId", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetAwardDressRiskAppId))
}

// GetBridesmaidSuitRemainDaysLimit mocks base method.
func (m *MockIBusinessConfManager) GetBridesmaidSuitRemainDaysLimit() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBridesmaidSuitRemainDaysLimit")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetBridesmaidSuitRemainDaysLimit indicates an expected call of GetBridesmaidSuitRemainDaysLimit.
func (mr *MockIBusinessConfManagerMockRecorder) GetBridesmaidSuitRemainDaysLimit() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBridesmaidSuitRemainDaysLimit", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetBridesmaidSuitRemainDaysLimit))
}

// GetFinishAwardCheckBeforeHour mocks base method.
func (m *MockIBusinessConfManager) GetFinishAwardCheckBeforeHour() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFinishAwardCheckBeforeHour")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetFinishAwardCheckBeforeHour indicates an expected call of GetFinishAwardCheckBeforeHour.
func (mr *MockIBusinessConfManagerMockRecorder) GetFinishAwardCheckBeforeHour() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFinishAwardCheckBeforeHour", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetFinishAwardCheckBeforeHour))
}

// GetFreeWeddingStageSec mocks base method.
func (m *MockIBusinessConfManager) GetFreeWeddingStageSec() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFreeWeddingStageSec")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetFreeWeddingStageSec indicates an expected call of GetFreeWeddingStageSec.
func (mr *MockIBusinessConfManagerMockRecorder) GetFreeWeddingStageSec() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFreeWeddingStageSec", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetFreeWeddingStageSec))
}

// GetGroupPhotoMaxCnt mocks base method.
func (m *MockIBusinessConfManager) GetGroupPhotoMaxCnt() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupPhotoMaxCnt")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetGroupPhotoMaxCnt indicates an expected call of GetGroupPhotoMaxCnt.
func (mr *MockIBusinessConfManagerMockRecorder) GetGroupPhotoMaxCnt() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupPhotoMaxCnt", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetGroupPhotoMaxCnt))
}

// GetGroupPhotoMicSeatList mocks base method.
func (m *MockIBusinessConfManager) GetGroupPhotoMicSeatList() []uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupPhotoMicSeatList")
	ret0, _ := ret[0].([]uint32)
	return ret0
}

// GetGroupPhotoMicSeatList indicates an expected call of GetGroupPhotoMicSeatList.
func (mr *MockIBusinessConfManagerMockRecorder) GetGroupPhotoMicSeatList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupPhotoMicSeatList", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetGroupPhotoMicSeatList))
}

// GetHappinessConf mocks base method.
func (m *MockIBusinessConfManager) GetHappinessConf() []*conf.HappinessConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHappinessConf")
	ret0, _ := ret[0].([]*conf.HappinessConfig)
	return ret0
}

// GetHappinessConf indicates an expected call of GetHappinessConf.
func (mr *MockIBusinessConfManagerMockRecorder) GetHappinessConf() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHappinessConf", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetHappinessConf))
}

// GetHappinessConfByScore mocks base method.
func (m *MockIBusinessConfManager) GetHappinessConfByScore(arg0 uint32) *conf.HappinessConfig {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHappinessConfByScore", arg0)
	ret0, _ := ret[0].(*conf.HappinessConfig)
	return ret0
}

// GetHappinessConfByScore indicates an expected call of GetHappinessConfByScore.
func (mr *MockIBusinessConfManagerMockRecorder) GetHappinessConfByScore(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHappinessConfByScore", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetHappinessConfByScore), arg0)
}

// GetHappinessLevelByScore mocks base method.
func (m *MockIBusinessConfManager) GetHappinessLevelByScore(arg0 uint32) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHappinessLevelByScore", arg0)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetHappinessLevelByScore indicates an expected call of GetHappinessLevelByScore.
func (mr *MockIBusinessConfManagerMockRecorder) GetHappinessLevelByScore(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHappinessLevelByScore", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetHappinessLevelByScore), arg0)
}

// GetHappinessUpgrade mocks base method.
func (m *MockIBusinessConfManager) GetHappinessUpgrade(arg0, arg1 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHappinessUpgrade", arg0, arg1)
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetHappinessUpgrade indicates an expected call of GetHappinessUpgrade.
func (mr *MockIBusinessConfManagerMockRecorder) GetHappinessUpgrade(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHappinessUpgrade", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetHappinessUpgrade), arg0, arg1)
}

// GetMaxHighLightPresentCount mocks base method.
func (m *MockIBusinessConfManager) GetMaxHighLightPresentCount() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxHighLightPresentCount")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetMaxHighLightPresentCount indicates an expected call of GetMaxHighLightPresentCount.
func (mr *MockIBusinessConfManagerMockRecorder) GetMaxHighLightPresentCount() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxHighLightPresentCount", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetMaxHighLightPresentCount))
}

// GetMvpMinSendValue mocks base method.
func (m *MockIBusinessConfManager) GetMvpMinSendValue() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMvpMinSendValue")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetMvpMinSendValue indicates an expected call of GetMvpMinSendValue.
func (mr *MockIBusinessConfManagerMockRecorder) GetMvpMinSendValue() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMvpMinSendValue", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetMvpMinSendValue))
}

// GetMvpPoseId mocks base method.
func (m *MockIBusinessConfManager) GetMvpPoseId() (uint32, uint32) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMvpPoseId")
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(uint32)
	return ret0, ret1
}

// GetMvpPoseId indicates an expected call of GetMvpPoseId.
func (mr *MockIBusinessConfManagerMockRecorder) GetMvpPoseId() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMvpPoseId", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetMvpPoseId))
}

// GetQuestionnaireUrl mocks base method.
func (m *MockIBusinessConfManager) GetQuestionnaireUrl() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQuestionnaireUrl")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetQuestionnaireUrl indicates an expected call of GetQuestionnaireUrl.
func (mr *MockIBusinessConfManagerMockRecorder) GetQuestionnaireUrl() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQuestionnaireUrl", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetQuestionnaireUrl))
}

// GetSceneImXmlContent mocks base method.
func (m *MockIBusinessConfManager) GetSceneImXmlContent(arg0 uint32) *conf.SceneImXmlContent {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSceneImXmlContent", arg0)
	ret0, _ := ret[0].(*conf.SceneImXmlContent)
	return ret0
}

// GetSceneImXmlContent indicates an expected call of GetSceneImXmlContent.
func (mr *MockIBusinessConfManagerMockRecorder) GetSceneImXmlContent(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSceneImXmlContent", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetSceneImXmlContent), arg0)
}

// GetSingleBoneCfg mocks base method.
func (m *MockIBusinessConfManager) GetSingleBoneCfg() *conf.SingleBoneCfg {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSingleBoneCfg")
	ret0, _ := ret[0].(*conf.SingleBoneCfg)
	return ret0
}

// GetSingleBoneCfg indicates an expected call of GetSingleBoneCfg.
func (mr *MockIBusinessConfManagerMockRecorder) GetSingleBoneCfg() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSingleBoneCfg", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetSingleBoneCfg))
}

// GetStageCfg mocks base method.
func (m *MockIBusinessConfManager) GetStageCfg(arg0 uint32) *conf.StageCfg {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStageCfg", arg0)
	ret0, _ := ret[0].(*conf.StageCfg)
	return ret0
}

// GetStageCfg indicates an expected call of GetStageCfg.
func (mr *MockIBusinessConfManagerMockRecorder) GetStageCfg(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStageCfg", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetStageCfg), arg0)
}

// GetStageCfgList mocks base method.
func (m *MockIBusinessConfManager) GetStageCfgList() []*conf.StageCfg {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStageCfgList")
	ret0, _ := ret[0].([]*conf.StageCfg)
	return ret0
}

// GetStageCfgList indicates an expected call of GetStageCfgList.
func (mr *MockIBusinessConfManagerMockRecorder) GetStageCfgList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStageCfgList", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetStageCfgList))
}

// GetStageSwitchIntervalSec mocks base method.
func (m *MockIBusinessConfManager) GetStageSwitchIntervalSec() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStageSwitchIntervalSec")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetStageSwitchIntervalSec indicates an expected call of GetStageSwitchIntervalSec.
func (mr *MockIBusinessConfManagerMockRecorder) GetStageSwitchIntervalSec() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStageSwitchIntervalSec", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetStageSwitchIntervalSec))
}

// GetSuitSendImCfg mocks base method.
func (m *MockIBusinessConfManager) GetSuitSendImCfg() *conf.SuitSendImCfg {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSuitSendImCfg")
	ret0, _ := ret[0].(*conf.SuitSendImCfg)
	return ret0
}

// GetSuitSendImCfg indicates an expected call of GetSuitSendImCfg.
func (mr *MockIBusinessConfManagerMockRecorder) GetSuitSendImCfg() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSuitSendImCfg", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetSuitSendImCfg))
}

// GetUpgradeClothesPopupDelay mocks base method.
func (m *MockIBusinessConfManager) GetUpgradeClothesPopupDelay() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpgradeClothesPopupDelay")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetUpgradeClothesPopupDelay indicates an expected call of GetUpgradeClothesPopupDelay.
func (mr *MockIBusinessConfManagerMockRecorder) GetUpgradeClothesPopupDelay() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpgradeClothesPopupDelay", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetUpgradeClothesPopupDelay))
}

// GetUpgradeClothesPopupXml mocks base method.
func (m *MockIBusinessConfManager) GetUpgradeClothesPopupXml() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpgradeClothesPopupXml")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetUpgradeClothesPopupXml indicates an expected call of GetUpgradeClothesPopupXml.
func (mr *MockIBusinessConfManagerMockRecorder) GetUpgradeClothesPopupXml() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpgradeClothesPopupXml", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetUpgradeClothesPopupXml))
}

// GetWeddingLpmGroupId mocks base method.
func (m *MockIBusinessConfManager) GetWeddingLpmGroupId() int32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingLpmGroupId")
	ret0, _ := ret[0].(int32)
	return ret0
}

// GetWeddingLpmGroupId indicates an expected call of GetWeddingLpmGroupId.
func (mr *MockIBusinessConfManagerMockRecorder) GetWeddingLpmGroupId() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingLpmGroupId", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetWeddingLpmGroupId))
}

// GetWeddingMaxDuration mocks base method.
func (m *MockIBusinessConfManager) GetWeddingMaxDuration() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingMaxDuration")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetWeddingMaxDuration indicates an expected call of GetWeddingMaxDuration.
func (mr *MockIBusinessConfManagerMockRecorder) GetWeddingMaxDuration() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingMaxDuration", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetWeddingMaxDuration))
}

// GetWeddingPoseCfg mocks base method.
func (m *MockIBusinessConfManager) GetWeddingPoseCfg() *conf.WeddingPoseCfg {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingPoseCfg")
	ret0, _ := ret[0].(*conf.WeddingPoseCfg)
	return ret0
}

// GetWeddingPoseCfg indicates an expected call of GetWeddingPoseCfg.
func (mr *MockIBusinessConfManagerMockRecorder) GetWeddingPoseCfg() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingPoseCfg", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetWeddingPoseCfg))
}

// GetWeddingPresentValLv mocks base method.
func (m *MockIBusinessConfManager) GetWeddingPresentValLv(arg0 uint32) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingPresentValLv", arg0)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetWeddingPresentValLv indicates an expected call of GetWeddingPresentValLv.
func (mr *MockIBusinessConfManagerMockRecorder) GetWeddingPresentValLv(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingPresentValLv", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetWeddingPresentValLv), arg0)
}

// GetWeddingRankConf mocks base method.
func (m *MockIBusinessConfManager) GetWeddingRankConf() *conf.WeddingRankConf {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeddingRankConf")
	ret0, _ := ret[0].(*conf.WeddingRankConf)
	return ret0
}

// GetWeddingRankConf indicates an expected call of GetWeddingRankConf.
func (mr *MockIBusinessConfManagerMockRecorder) GetWeddingRankConf() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeddingRankConf", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetWeddingRankConf))
}

// Reload mocks base method.
func (m *MockIBusinessConfManager) Reload(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Reload", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Reload indicates an expected call of Reload.
func (mr *MockIBusinessConfManagerMockRecorder) Reload(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reload", reflect.TypeOf((*MockIBusinessConfManager)(nil).Reload), arg0)
}

// Watch mocks base method.
func (m *MockIBusinessConfManager) Watch(arg0 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Watch", arg0)
}

// Watch indicates an expected call of Watch.
func (mr *MockIBusinessConfManagerMockRecorder) Watch(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Watch", reflect.TypeOf((*MockIBusinessConfManager)(nil).Watch), arg0)
}
