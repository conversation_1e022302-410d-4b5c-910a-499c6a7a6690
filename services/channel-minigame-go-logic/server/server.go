package server

import (
	"context"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	channelLiveMgr "golang.52tt.com/clients/channel-live-mgr"
	userprivilege "golang.52tt.com/clients/user-privilege"
	"golang.52tt.com/pkg/bylink"
	channel_base "golang.52tt.com/pkg/channel-base"
	"math/rand"
	"strconv"
	"time"

	"golang.52tt.com/clients/account"
	censoring "golang.52tt.com/clients/censoring-proxy"
	"golang.52tt.com/clients/channel"
	channelgame "golang.52tt.com/clients/channel-game"
	channelmsgexpress "golang.52tt.com/clients/channel-msg-express"
	runway "golang.52tt.com/clients/channel-present-runway"
	channel_vote_pk_go "golang.52tt.com/clients/channel-vote-pk-go"
	"golang.52tt.com/clients/channelcommonvotepk"
	channelVipMemSvr "golang.52tt.com/clients/channelmemberVipRank"
	"golang.52tt.com/clients/channelmic"
	greenbabaclient "golang.52tt.com/clients/greenbaba"
	"golang.52tt.com/clients/guild"
	headImage "golang.52tt.com/clients/headimage"
	"golang.52tt.com/clients/nobility"
	numeric "golang.52tt.com/clients/numeric"
	present_count "golang.52tt.com/clients/present-count"
	pushv2 "golang.52tt.com/clients/push-notification/v2"
	userdefinedvotepk "golang.52tt.com/clients/rhythm/user-defined-vote-pk"
	"golang.52tt.com/clients/seqgen/v2"
	topic_channel "golang.52tt.com/clients/topic-channel/channel"
	userprofileapi "golang.52tt.com/clients/user-profile-api"
	youknowwho "golang.52tt.com/clients/you-know-who"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/datacenter"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	basePb "golang.52tt.com/protocol/app"
	ga_base "golang.52tt.com/protocol/app"
	pb "golang.52tt.com/protocol/app/channel"
	channel_mini_game "golang.52tt.com/protocol/app/channel-mini-game"
	"golang.52tt.com/protocol/app/sync"
	"golang.52tt.com/protocol/common/status"
	channel_vote_pk_go2 "golang.52tt.com/protocol/services/channel-vote-pk-go"
	channelMemVipPb "golang.52tt.com/protocol/services/channelmemberVipRank"
	channelsvrPb "golang.52tt.com/protocol/services/channelsvr"
	censoring_proxyPB "golang.52tt.com/protocol/services/cybros/arbiter/v2"
	gpb "golang.52tt.com/protocol/services/greenBaba"
	guildPb "golang.52tt.com/protocol/services/guildsvr"
	nobilitySvrPb "golang.52tt.com/protocol/services/nobilitysvr"
	presentCntPb "golang.52tt.com/protocol/services/present-count-go"
	topicChannelPb "golang.52tt.com/protocol/services/topic_channel/channel"
	"golang.52tt.com/services/channel-minigame-go-logic/conf/business"
	"golang.52tt.com/services/channel-minigame-go-logic/conf/server"
	goctx "golang.org/x/net/context"
)

const (
	getUserInfoErrorMsg = "获取用户信息失败"
)

type ChannelMiniGameGoLogic struct {
	sc                      *server.ServiceConfigT
	channelClient           *channel.Client
	accountClient           *account.Client
	guildClient             *guild.Client
	channelMicClient        *channelmic.Client
	channelGameClient       *channelgame.Client
	pushv2Client            *pushv2.Client
	seqgenClient            *seqgen.Client
	greenbabaClient         *greenbabaclient.Client
	nobilityClient          *nobility.Client
	channelCommonPkCli      *channelcommonvotepk.Client
	channelVotePkGoCli      *channel_vote_pk_go.Client
	censoringClient         *censoring.Client
	headImageCli            *headImage.Client
	presentCntCli           *present_count.Client
	runwayCli               *runway.Client
	ukwCli                  *youknowwho.Client
	userProfileApiCli       *userprofileapi.Client
	channelMsgExpressClient *channelmsgexpress.Client
	udVotePKClient          *userdefinedvotepk.Client
	topicChannelClient      *topic_channel.Client
	channelVipMemClient     *channelVipMemSvr.Client
	numericCli              *numeric.Client
	channelPermissionApi    channel_base.IChannelPermissionChecker
	userPriCli              *userprivilege.Client
	channelLiveMgrCli       channelLiveMgr.IClient
}

func NewChannelMiniGameGoLogic(ctx context.Context, config config.Configer) (*ChannelMiniGameGoLogic, error) {
	sc := &server.ServiceConfigT{}
	err := sc.Parse(config)
	if err != nil {
		log.ErrorWithCtx(ctx, "load config  fail: %v", err)
		return nil, err
	}

	log.InfoWithCtx(ctx, "[%s] service run with config: %v", server.Environment, utils.ToJson(sc))
	//魔法表情配置加载
	err = business.MagicExpresLoad()
	if err != nil {
		log.ErrorWithCtx(ctx, "Load business MagicExpres config fail: %v", err)
		return nil, err
	}

	//贵族特权配置加载
	err = business.NobilityLoad()
	if err != nil {
		log.ErrorWithCtx(ctx, "Load business Nobility config fail: %v", err)
		return nil, err
	}

	rand.Seed(time.Now().Unix())

	tChannelClient := channel.NewClient()
	if nil == tChannelClient {
		log.ErrorWithCtx(ctx, "channel client load fail")
		return nil, errors.New("channel client load fail")
	}

	tAccountClient, _ := account.NewClient()
	if nil == tAccountClient {
		log.ErrorWithCtx(ctx, "account client load fail")
		return nil, errors.New("account client load fail")
	}

	tGuildClient := guild.NewClient()
	if nil == tGuildClient {
		log.ErrorWithCtx(ctx, "guild client load fail")
		return nil, errors.New("guild client load fail")
	}

	tChannelMicClient := channelmic.NewClient()
	if nil == tChannelMicClient {
		log.ErrorWithCtx(ctx, "channelmic client load fail")
		return nil, errors.New("channelmic client load fail")
	}

	tChannelGameClient, _ := channelgame.NewClient()
	if nil == tChannelGameClient {
		log.ErrorWithCtx(ctx, "channelgame client load fail")
		return nil, errors.New("channelgame client load fail")
	}

	tPushV2Client, _ := pushv2.NewClient()
	if nil == tPushV2Client {
		log.ErrorWithCtx(ctx, "pushv2 client load fail")
		return nil, errors.New("pushv2 client load fail")
	}
	tSeqgenClient, _ := seqgen.NewClient()
	if nil == tSeqgenClient {
		log.ErrorWithCtx(ctx, "seqgen client load fail")
		return nil, errors.New("seqgen client load fail")
	}

	tGreenbabaClient := greenbabaclient.NewClient()
	if tGreenbabaClient == nil {
		log.ErrorWithCtx(ctx, "greenbaba client load fail")
		return nil, errors.New("greenbaba client load fail")
	}

	tNobilityClient, _ := nobility.NewClient()
	if tNobilityClient == nil {
		log.ErrorWithCtx(ctx, "nobility client load fail")
		return nil, errors.New("nobility client load fail")
	}

	channelVotePkGoCli, _ := channel_vote_pk_go.NewClient()
	channelcommonvotepkCli := channelcommonvotepk.NewClient()
	CensoringClient := censoring.NewClient()
	headImageCli := headImage.NewClient()

	presentCntCli, _ := present_count.NewClient()
	runwayCli, _ := runway.NewClient()

	channelMsgExpressClient, _ := channelmsgexpress.NewClient()
	udVotePKClient, _ := userdefinedvotepk.NewClient()

	ukwCli, _ := youknowwho.NewClient()

	userProfileApiCli, _ := userprofileapi.NewClient()
	topicChannelClient, _ := topic_channel.NewClient()

	channelVipMemClient := channelVipMemSvr.NewClient()
	numericCli := numeric.NewClient()
	userPriCli, _ := userprivilege.NewClient()

	channelPermissionApi := channel_base.NewChannelPermissionCheckerWithC(tChannelClient, tAccountClient, tGuildClient)

	channelLiveMgrCli := channelLiveMgr.NewIClient()

	// 百灵数据统计 初始化
	bylinkCollect, err := bylink.NewKfkCollector()
	if err != nil {
		log.ErrorWithCtx(ctx, "bylink.NewKfkCollector() failed err:%v", err)
		return nil, err
	}
	bylink.InitGlobalCollector(bylinkCollect)

	return &ChannelMiniGameGoLogic{
		sc:                 sc,
		channelClient:      tChannelClient,
		accountClient:      tAccountClient,
		guildClient:        tGuildClient,
		channelMicClient:   tChannelMicClient,
		channelGameClient:  tChannelGameClient,
		pushv2Client:       tPushV2Client,
		seqgenClient:       tSeqgenClient,
		greenbabaClient:    tGreenbabaClient,
		nobilityClient:     tNobilityClient,
		channelVotePkGoCli: channelVotePkGoCli,
		channelCommonPkCli: channelcommonvotepkCli,
		censoringClient:    CensoringClient,
		headImageCli:       headImageCli,
		presentCntCli:      presentCntCli,
		runwayCli:          runwayCli,
		ukwCli:             ukwCli,
		userProfileApiCli:  userProfileApiCli,
		udVotePKClient:     udVotePKClient,

		channelMsgExpressClient: channelMsgExpressClient,
		topicChannelClient:      topicChannelClient,
		channelVipMemClient:     channelVipMemClient,
		numericCli:              numericCli,
		channelPermissionApi:    channelPermissionApi,
		userPriCli:              userPriCli,
		channelLiveMgrCli:       channelLiveMgrCli,
	}, nil
}

func (s *ChannelMiniGameGoLogic) ShutDown() {}

func (s *ChannelMiniGameGoLogic) IsPgc(channelType uint32) bool {
	if channelType == uint32(pb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) ||
		channelType == uint32(pb.ChannelType_RADIO_LIVE_CHANNEL_TYPE) ||
		channelType == uint32(pb.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) {
		return true
	} else {
		return false
	}
}

func (s *ChannelMiniGameGoLogic) ChannelPresentCount(c goctx.Context, req *pb.ChannelPresentCountReq) (*pb.ChannelPresentCountResp, error) {
	//TODO implement me

	panic("implement me")
}

func (s *ChannelMiniGameGoLogic) ChannelVotePKCancel(ctx context.Context, req *pb.ChannelPkCancelReq) (*pb.ChannelPKCancelResp, error) {
	out := &pb.ChannelPKCancelResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewServerError(status.ErrSys, getUserInfoErrorMsg)
	}
	uid := serviceInfo.UserID
	log.InfoWithCtx(ctx, "ChannelVotePKCancel , req: %+v , service_info:+v", req, serviceInfo)
	err := s.checkPermission(ctx, uid, req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelVotePKCancel  checkLivePkPermission ,uid %d, fail: %v", uid, err)
		return out, protocol.ToServerError(err)
	}

	_, err = s.channelVotePkGoCli.ChannelPkCancel(ctx, &channel_vote_pk_go2.ChannelPkCancelReq{
		Uid:            uid,
		ChannelId:      req.GetChannelId(),
		StartTimestamp: req.GetStartTimestamp(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelVotePKCancel  ChannelPkCancel ,uid %d, fail: %v", uid, err)
		return out, protocol.ToServerError(err)
	}

	return out, nil
}

func (s *ChannelMiniGameGoLogic) ChannelVotePKGetInfo(ctx context.Context, req *pb.GetChannelVotePKInfoReq) (*pb.GetChannelVotePKInfoResp, error) {
	out := &pb.GetChannelVotePKInfoResp{PkInfo: &pb.ChannelVotePKRankInfo{Info: &pb.ChannelPkInfo{}}}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewServerError(status.ErrSys, getUserInfoErrorMsg)
	}
	uid := serviceInfo.UserID
	log.InfoWithCtx(ctx, "ChannelLiveVotePKGetInfo , req: %+v , service_info:+v", req, serviceInfo)
	pkInfo, err := s.channelVotePkGoCli.GetChannelVotePKInfo(ctx, &channel_vote_pk_go2.GetChannelVotePKInfoReq{
		Uid:       uid,
		ChannelId: req.GetChannelId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveVotePKInfo  GetChannelVotePKInfo err ,channelId %d, fail: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}

	uidList := make([]uint32, 0)
	for _, user := range pkInfo.GetPkInfo().GetCompetiterList() {
		uidList = append(uidList, user.GetUid())
	}

	userMap, err := s.accountClient.GetUsersMap(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveVotePKInfo  GetUsersMap err ,channelId %d, fail: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}

	userNameList := make([]string, 0)
	for _, user := range userMap {
		userNameList = append(userNameList, user.GetUsername())
	}

	faceMd5Map, err := s.headImageCli.BatchGetHeadImageMd5(ctx, uid, userNameList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveVotePKInfo  BatchGetHeadImageMd5 err ,channelId %d, fail: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}

	competitorList := make([]*pb.ChannelVotePkCompetitor, 0)
	for _, competitor := range pkInfo.GetPkInfo().GetCompetiterList() {
		competitorList = append(competitorList, &pb.ChannelVotePkCompetitor{
			Uid:      competitor.GetUid(),
			Nickname: userMap[competitor.GetUid()].GetNickname(),
			Account:  userMap[competitor.GetUid()].GetUsername(),
			FaceMd5:  faceMd5Map[userMap[competitor.GetUid()].GetUsername()],
			Vote:     competitor.GetVote(),
			Rank:     competitor.GetRank(),
		})
	}

	out.GetPkInfo().CompetiterList = competitorList
	out.GetPkInfo().Info = &pb.ChannelPkInfo{
		Uid:            pkInfo.GetPkInfo().GetInfo().GetUid(),
		ChannelId:      pkInfo.GetPkInfo().GetInfo().GetChannelId(),
		Type:           pkInfo.GetPkInfo().GetInfo().GetType(),
		DurationMin:    pkInfo.GetPkInfo().GetInfo().GetDurationMin(),
		StartTimestamp: pkInfo.GetPkInfo().GetInfo().GetStartTimestamp(),
		PersonType:     pkInfo.GetPkInfo().GetInfo().GetPersonType(),
		VoteCnt:        pkInfo.GetPkInfo().GetInfo().GetVoteCnt(),
		PkName:         pkInfo.GetPkInfo().GetInfo().GetPkName(),
		ChannelType:    pkInfo.GetPkInfo().GetInfo().GetChannelType(),
		ChannelName:    pkInfo.GetPkInfo().GetInfo().GetChannelName(),
	}

	left, err := s.channelVotePkGoCli.GetUserLeftVoteCnt(ctx, &channel_vote_pk_go2.GetUserLeftVoteCntReq{
		Uid:            uid,
		ChannelId:      req.GetChannelId(),
		StartTimestamp: pkInfo.GetPkInfo().GetInfo().GetStartTimestamp(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelVotePKVote GetUserLeftVoteCnt fail,channel: %d,err: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}
	out.LeftVote = left.GetLeftVoteCnt()
	out.LeftExtraVote = left.GetRemainExtraVote()

	log.DebugWithCtx(ctx, "ChannelLiveVotePKGetInfo , resp: %+v , service_info:+v", req, serviceInfo)
	return out, nil
}

func (s *ChannelMiniGameGoLogic) ChannelVotePKStart(ctx context.Context, req *pb.ChannelVotePkStartReq) (*pb.ChannelVotePkStartResp, error) {
	out := &pb.ChannelVotePkStartResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewServerError(status.ErrSys, getUserInfoErrorMsg)
	}
	uid := serviceInfo.UserID

	log.InfoWithCtx(ctx, "ChannelVotePKStart , req: %+v , service_info:+v", req, serviceInfo)

	err := s.checkPermission(ctx, uid, req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelVotePKStart  ChannelVotePKStart err ,uid %d, err: %v", uid, err)
		return out, protocol.ToServerError(err)
	}

	/* 检查是否有自定义PK */
	has, errr := s.HasUserDefinedVotePK(ctx, req.GetChannelId())
	if errr != nil {
		log.ErrorWithCtx(ctx, "ChannelLiveVotePKStart pkName err , channelId : %d , err: %v", req.GetChannelId(), errr)
		return out, errr
	}
	if has {
		return out, protocol.NewServerError(status.ErrChannelVotePkExist)
	}

	if req.GetPkName() != "" {
		r, err := s.censoringClient.Text().SyncScanText(ctx, &censoring_proxyPB.SyncTextCheckReq{
			Text: req.GetPkName(),
			Context: &censoring_proxyPB.TaskContext{
				Category: "CHANNEL_PK",
				UserInfo: &censoring_proxyPB.User{
					Id: uint64(uid),
				},
				DeviceInfo: &censoring_proxyPB.Device{
					Id: hex.EncodeToString(serviceInfo.DeviceID),
					Ip: serviceInfo.ClientIPAddr().String(),
				},
				BelongObjId: fmt.Sprintf("%d", req.GetChannelId()),
			},
			ChannelId: req.GetChannelId(),
		})

		if censoring_proxyPB.Suggestion_REJECT == censoring_proxyPB.Suggestion(r.GetResult()) || err != nil {
			log.ErrorWithCtx(ctx, "ChannelLiveVotePKStart pkName err , channelId : %d , err: %v", req.GetChannelId(), err)
			return out, protocol.NewServerError(status.ErrChannelVotePkNameInvalid)
		}
	}

	// 禁止神秘人被投票
	ukwInfos, err := s.ukwCli.BatchGetTrueUidByFake(ctx, req.GetUidList())
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelLiveVotePKStart BatchGetUKWPersonInfoOnly err , channelId : %d , err: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}

	cInfo, err := s.channelClient.GetChannelSimpleInfo(ctx, uid, req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelLiveVotePKStart GetChannelSimpleInfo err , channelId : %d , err: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}
	switch pb.ChannelType(cInfo.GetChannelType()) {
	case pb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE, pb.ChannelType_RADIO_LIVE_CHANNEL_TYPE, pb.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE:
		for _, item := range ukwInfos {
			// 如果有fakeUid说明是神秘人
			if item.GetUid() != item.GetReqUid() {
				log.ErrorWithCtx(ctx, "ChannelLiveVotePKStart hit ukw, channelId : %d, uids: %v", req.GetChannelId(), req.GetUidList())
				return out, protocol.NewServerError(status.ErrUkwOpToDenied, "神秘人不可作为被投票方喔")
			}
		}
	}

	resp, err := s.channelVotePkGoCli.ChannelVotePkStart(ctx, &channel_vote_pk_go2.ChannelVotePkStartReq{
		Uid:         uid,
		ChannelId:   req.GetChannelId(),
		DurationMin: req.GetDurationMin(),
		Type:        req.GetType(),
		UidList:     req.GetUidList(),
		VoteCnt:     req.GetVoteCnt(),
		PkName:      req.GetPkName(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelVotePkStart  ChannelVotePkStart err ,channelId %d, err: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}

	out.ChannelId = resp.GetChannelId()
	out.DurationMin = resp.GetDurationMin()
	out.StartTimestamp = resp.GetStartTimestamp()
	out.LeftVote = req.GetVoteCnt()

	return out, protocol.ToServerError(err)
}

func (s *ChannelMiniGameGoLogic) ChannelVotePKVote(ctx context.Context, req *pb.ChannelVotePkVoteReq) (*pb.ChannelVotePkVoteResp, error) {
	out := &pb.ChannelVotePkVoteResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewServerError(status.ErrSys, getUserInfoErrorMsg)
	}

	if req.GetVoteCnt() > 1 {
		log.ErrorWithCtx(ctx, "ChannelVotePKVote voteCnt > 1, req: %+v , service_info:+v", req, serviceInfo)
		return out, protocol.NewExactServerError(nil, status.ErrChannelVotePkArgsFail, "票数异常")
	}

	uid := serviceInfo.UserID
	log.InfoWithCtx(ctx, "ChannelVotePKVote , req: %+v , service_info:+v", req, serviceInfo)
	_, err := s.channelVotePkGoCli.ChannelCommonPkVote(ctx, &channel_vote_pk_go2.ChannelCommonPkVoteReq{
		FromUid:        uid,
		ChannelId:      req.GetChannelId(),
		StartTimestamp: req.GetStartTimestamp(),
		ToUid:          req.GetToUid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelVotePKVote ChannelCommonPkVote fail,channel: %d,err: %v", req.GetChannelId(), err)
		sErr := protocol.ToServerError(err)
		if sErr.Code() == status.ErrChannelVotePkNotLeftVote {
			return out, protocol.NewServerError(status.ErrChannelVotePkNotLeftVote, "你的票数已经用完啦，感谢你的支持哟")
		}
		return out, protocol.ToServerError(err)
	}

	left, err := s.channelVotePkGoCli.GetUserLeftVoteCnt(ctx, &channel_vote_pk_go2.GetUserLeftVoteCntReq{
		Uid:            uid,
		ChannelId:      req.GetChannelId(),
		StartTimestamp: req.GetStartTimestamp(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelVotePKVote GetUserLeftVoteCnt fail,channel: %d,err: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}

	_, err = s.channelVotePkGoCli.ChannelVotePkVote(ctx, &channel_vote_pk_go2.ChannelVotePkVoteReq{
		Uid:            uid,
		ToUid:          req.GetToUid(),
		VoteCnt:        req.GetVoteCnt(),
		ChannelId:      req.GetChannelId(),
		StartTimestamp: req.GetStartTimestamp(),
		SendTimestamp:  uint32(time.Now().Unix()),
		PkType:         uint32(pb.ChannelVotePkType_ChannelVotePkType_VOTE_PK_TYPE),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelVotePKVote ChannelVotePkVote fail,channel: %d,err: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}

	out.LeftVote = left.GetLeftVoteCnt()
	out.RemainExtraVote = left.GetRemainExtraVote()
	return out, nil
}

func (s *ChannelMiniGameGoLogic) SearchPkCandidate(ctx context.Context, req *channel_mini_game.SearchPkCandidateReq) (*channel_mini_game.SearchPkCandidateResp, error) {
	out := &channel_mini_game.SearchPkCandidateResp{}
	out.CandidateInfo = make([]*channel_mini_game.PkCandidateInfo, 0)
	resp, err := s.channelVotePkGoCli.SearchPkCandidate(ctx, &channel_vote_pk_go2.SearchPkCandidateReq{
		ChannelId: req.GetChannelId(),
		KeyWord:   req.GetKeyWord(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchPkCandidate GetVotePkStatus fail,channel: %d,err: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}

	for _, user := range resp.GetCandidateInfo() {
		out.CandidateInfo = append(out.CandidateInfo, &channel_mini_game.PkCandidateInfo{
			Uid:      user.GetUid(),
			Nickname: user.GetNickname(),
			Account:  user.GetAccount(),
			FaceMd5:  user.GetFaceMd5(),
			Sex:      user.GetSex(),
			Alias:    user.GetAlias(),
		})
	}

	return out, nil
}

func (s *ChannelMiniGameGoLogic) GetVotePkStatus(ctx context.Context, req *channel_mini_game.GetVotePkStatusReq) (*channel_mini_game.GetVotePkStatusResp, error) {
	out := &channel_mini_game.GetVotePkStatusResp{}
	resp, err := s.channelVotePkGoCli.GetVotePkStatus(ctx, &channel_vote_pk_go2.GetVotePkStatusReq{
		ChannelId: req.GetChannelId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetVotePkStatus fail,channel: %d,err: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}

	out.ShowPkSwitch = resp.ShowPkSwitch
	return out, nil
}

func (s *ChannelMiniGameGoLogic) ChannelLiveVotePKVote(ctx context.Context, req *pb.ChannelLiveVotePkVoteReq) (*pb.ChannelLiveVotePkVoteResp, error) {
	out := &pb.ChannelLiveVotePkVoteResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewServerError(status.ErrSys, getUserInfoErrorMsg)
	}

	if req.GetVoteCnt() > 1 {
		log.ErrorWithCtx(ctx, "ChannelVotePKVote voteCnt > 1, req: %+v , service_info:+v", req, serviceInfo)
		return out, protocol.NewExactServerError(nil, status.ErrChannelVotePkArgsFail, "票数异常")
	}

	uid := serviceInfo.UserID
	log.InfoWithCtx(ctx, "ChannelLiveVotePKVote , req: %+v , service_info:+v", req, serviceInfo)
	_, err := s.channelVotePkGoCli.ChannelCommonPkVote(ctx, &channel_vote_pk_go2.ChannelCommonPkVoteReq{
		FromUid:        uid,
		ChannelId:      req.GetChannelId(),
		StartTimestamp: req.GetStartTimestamp(),
		ToUid:          req.GetToUid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelLiveVotePKVote ChannelCommonPkVote fail,channel: %d,err: %v", req.GetChannelId(), err)
		sErr := protocol.ToServerError(err)
		if sErr.Code() == status.ErrChannelVotePkNotLeftVote {
			return out, protocol.NewServerError(status.ErrChannelVotePkNotLeftVote, "你的票数已经用完啦，感谢你的支持哟")
		}
		return out, protocol.ToServerError(err)
	}

	left, err := s.channelVotePkGoCli.GetUserLeftVoteCnt(ctx, &channel_vote_pk_go2.GetUserLeftVoteCntReq{
		Uid:            uid,
		ChannelId:      req.GetChannelId(),
		StartTimestamp: req.GetStartTimestamp(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelLiveVotePKVote GetUserLeftVoteCnt fail,channel: %d,err: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}

	_, err = s.channelVotePkGoCli.ChannelVotePkVote(ctx, &channel_vote_pk_go2.ChannelVotePkVoteReq{
		Uid:            uid,
		ToUid:          req.GetToUid(),
		VoteCnt:        req.GetVoteCnt(),
		ChannelId:      req.GetChannelId(),
		StartTimestamp: req.GetStartTimestamp(),
		SendTimestamp:  uint32(time.Now().Unix()),
		PkType:         uint32(pb.ChannelVotePkType_ChannelVotePkType_VOTE_PK_TYPE),
		IsLive:         true,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelLiveVotePKVote ChannelVotePkVote fail,channel: %d,err: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}

	out.LeftVote = left.GetLeftVoteCnt()
	return out, nil
}
func (s *ChannelMiniGameGoLogic) HasUserDefinedVotePK(ctx context.Context, channelid uint32) (bool, error) {
	rsp, err := s.udVotePKClient.HasUserDefinedVotePK(ctx, channelid)
	if err != nil {
		return false, err
	}
	return rsp.GetHasPk(), nil
}
func (s *ChannelMiniGameGoLogic) ChannelLiveVotePKStart(ctx context.Context, req *pb.ChannelLiveVotePkStartReq) (*pb.ChannelLiveVotePkStartResp, error) {
	out := &pb.ChannelLiveVotePkStartResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewServerError(status.ErrSys, getUserInfoErrorMsg)
	}
	uid := serviceInfo.UserID

	log.InfoWithCtx(ctx, "ChannelLiveVotePKStart , req: %+v , service_info:+v", req, serviceInfo)

	err := s.checkLivePkPermission(ctx, uid, req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelLiveVotePKCancel  checkLivePkPermission err ,uid %d, err: %v", uid, err)
		return out, protocol.ToServerError(err)
	}

	/* 检查是否有自定义PK */
	has, errr := s.HasUserDefinedVotePK(ctx, req.GetChannelId())
	if errr != nil {
		log.ErrorWithCtx(ctx, "ChannelLiveVotePKStart pkName err , channelId : %d , err: %v", req.GetChannelId(), errr)
		return out, errr
	}
	if has {
		return out, protocol.NewServerError(status.ErrChannelVotePkExist)
	}

	// 禁止神秘人被投票
	ukwInfos, err := s.ukwCli.BatchGetTrueUidByFake(ctx, req.GetUidList())
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelLiveVotePKStart BatchGetUKWPersonInfoOnly err , channelId : %d , err: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}

	cInfo, err := s.channelClient.GetChannelSimpleInfo(ctx, uid, req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelLiveVotePKStart GetChannelSimpleInfo err , channelId : %d , err: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}
	switch pb.ChannelType(cInfo.GetChannelType()) {
	case pb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE, pb.ChannelType_RADIO_LIVE_CHANNEL_TYPE, pb.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE:
		for _, item := range ukwInfos {
			// 如果有fakeUid说明是神秘人
			if item.GetUid() != item.GetReqUid() {
				log.ErrorWithCtx(ctx, "ChannelLiveVotePKStart hit ukw, channelId : %d, uids: %v", req.GetChannelId(), req.GetUidList())
				return out, protocol.NewServerError(status.ErrUkwOpToDenied, "神秘人不可作为被投票方喔")
			}
		}
	}

	if req.GetPkName() != "" {
		r, err := s.censoringClient.Text().SyncScanText(ctx, &censoring_proxyPB.SyncTextCheckReq{
			Text: req.GetPkName(),
			Context: &censoring_proxyPB.TaskContext{
				Category: "CHANNEL_PK",
				UserInfo: &censoring_proxyPB.User{
					Id: uint64(uid),
				},
				DeviceInfo: &censoring_proxyPB.Device{
					Id: hex.EncodeToString(serviceInfo.DeviceID),
					Ip: serviceInfo.ClientIPAddr().String(),
				},
				BelongObjId: fmt.Sprintf("%d", req.GetChannelId()),
			},
			ChannelId: req.GetChannelId(),
		})

		if censoring_proxyPB.Suggestion_REJECT == censoring_proxyPB.Suggestion(r.GetResult()) || err != nil {
			log.ErrorWithCtx(ctx, "ChannelLiveVotePKStart pkName err , channelId : %d , err: %v", req.GetChannelId(), err)
			return out, protocol.NewServerError(status.ErrChannelVotePkNameInvalid)
		}

	}

	resp, err := s.channelVotePkGoCli.ChannelVotePkStart(ctx, &channel_vote_pk_go2.ChannelVotePkStartReq{
		Uid:         uid,
		ChannelId:   req.GetChannelId(),
		DurationMin: req.GetDurationMin(),
		Type:        req.GetType(),
		UidList:     req.GetUidList(),
		VoteCnt:     req.GetVoteCnt(),
		PkName:      req.GetPkName(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelVotePkStart  ChannelVotePkStart err ,channelId %d, err: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}

	out.ChannelId = resp.GetChannelId()
	out.DurationMin = resp.GetDurationMin()
	out.StartTimestamp = resp.GetStartTimestamp()
	out.LeftVote = req.GetVoteCnt()

	return out, protocol.ToServerError(err)
}

func (s *ChannelMiniGameGoLogic) ChannelLiveVotePKGetInfo(ctx context.Context, req *pb.GetChannelLiveVotePKInfoReq) (*pb.GetChannelLiveVotePKInfoResp, error) {
	out := &pb.GetChannelLiveVotePKInfoResp{PkInfo: &pb.ChannelVotePKRankInfo{Info: &pb.ChannelPkInfo{}}}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewServerError(status.ErrSys, getUserInfoErrorMsg)
	}
	uid := serviceInfo.UserID
	log.InfoWithCtx(ctx, "ChannelLiveVotePKGetInfo , req: %+v , service_info:+v", req, serviceInfo)
	pkInfo, err := s.channelVotePkGoCli.GetChannelVotePKInfo(ctx, &channel_vote_pk_go2.GetChannelVotePKInfoReq{
		Uid:       uid,
		ChannelId: req.GetChannelId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveVotePKInfo  GetChannelVotePKInfo err ,channelId %d, fail: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}

	uidList := make([]uint32, 0)
	for _, user := range pkInfo.GetPkInfo().GetCompetiterList() {
		uidList = append(uidList, user.GetUid())
	}

	userMap, err := s.accountClient.GetUsersMap(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveVotePKInfo  GetUsersMap err ,channelId %d, fail: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}

	userNameList := make([]string, 0)
	for _, user := range userMap {
		userNameList = append(userNameList, user.GetUsername())
	}

	faceMd5Map, err := s.headImageCli.BatchGetHeadImageMd5(ctx, uid, userNameList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveVotePKInfo  BatchGetHeadImageMd5 err ,channelId %d, fail: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}

	competitorList := make([]*pb.ChannelVotePkCompetitor, 0)
	for _, competitor := range pkInfo.GetPkInfo().GetCompetiterList() {
		competitorList = append(competitorList, &pb.ChannelVotePkCompetitor{
			Uid:      competitor.GetUid(),
			Nickname: userMap[competitor.GetUid()].GetNickname(),
			Account:  userMap[competitor.GetUid()].GetUsername(),
			FaceMd5:  faceMd5Map[userMap[competitor.GetUid()].GetUsername()],
			Vote:     competitor.GetVote(),
			Rank:     competitor.GetRank(),
		})
	}

	out.GetPkInfo().CompetiterList = competitorList
	out.GetPkInfo().Info = &pb.ChannelPkInfo{
		Uid:            pkInfo.GetPkInfo().GetInfo().GetUid(),
		ChannelId:      pkInfo.GetPkInfo().GetInfo().GetChannelId(),
		Type:           pkInfo.GetPkInfo().GetInfo().GetType(),
		DurationMin:    pkInfo.GetPkInfo().GetInfo().GetDurationMin(),
		StartTimestamp: pkInfo.GetPkInfo().GetInfo().GetStartTimestamp(),
		PersonType:     pkInfo.GetPkInfo().GetInfo().GetPersonType(),
		VoteCnt:        pkInfo.GetPkInfo().GetInfo().GetVoteCnt(),
		PkName:         pkInfo.GetPkInfo().GetInfo().GetPkName(),
		ChannelType:    pkInfo.GetPkInfo().GetInfo().GetChannelType(),
		ChannelName:    pkInfo.GetPkInfo().GetInfo().GetChannelName(),
	}

	// 剩余票数要额外拿
	left, err := s.channelVotePkGoCli.GetUserLeftVoteCnt(ctx, &channel_vote_pk_go2.GetUserLeftVoteCntReq{
		Uid:            uid,
		ChannelId:      req.GetChannelId(),
		StartTimestamp: pkInfo.GetPkInfo().GetInfo().GetStartTimestamp(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelLiveVotePKGetInfo GetUserLeftVoteCnt fail,channel: %d,err: %v", req.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}
	out.LeftVote = left.GetLeftVoteCnt()

	return out, nil
}

func (s *ChannelMiniGameGoLogic) ChannelLiveVotePKCancel(ctx context.Context, req *pb.ChannelLivePkCancelReq) (*pb.ChannelLivePKCancelResp, error) {
	out := &pb.ChannelLivePKCancelResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewServerError(status.ErrSys, getUserInfoErrorMsg)
	}
	uid := serviceInfo.UserID
	log.InfoWithCtx(ctx, "ChannelLiveVotePKCancel , req: %+v , service_info:+v", req, serviceInfo)
	err := s.checkLivePkPermission(ctx, uid, req.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelLiveVotePKCancel  checkLivePkPermission ,uid %d, fail: %v", uid, err)
		return out, protocol.ToServerError(err)
	}

	_, err = s.channelVotePkGoCli.ChannelPkCancel(ctx, &channel_vote_pk_go2.ChannelPkCancelReq{
		Uid:            uid,
		ChannelId:      req.GetChannelId(),
		StartTimestamp: req.GetStartTimestamp(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelLiveVotePKCancel  ChannelPkCancel ,uid %d, fail: %v", uid, err)
		return out, protocol.ToServerError(err)
	}

	return out, nil
}

const (
	Permission2ChannelManage uint32 = 1 << 1
	channelGameTs                   = 5
)

// 房间小游戏入口:抽签
// Deprecated:已迁移到channelext-logic-go
func (s *ChannelMiniGameGoLogic) ChannelGame(ctx context.Context, in *pb.ChannelGameReq) (out *pb.ChannelGameResp, err error) {
	out = &pb.ChannelGameResp{}
	log.DebugWithCtx(ctx, "ChanneGameReq:%v", utils.ToJson(in))

	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, protocol.NewServerError(status.ErrSys, getUserInfoErrorMsg)
	}

	uid := serviceInfo.UserID
	cid := in.ChannelId
	gameId := in.GameId

	if gameId != uint32(pb.ChannelGameType_GAME_DRAW) {
		return out, protocol.NewServerError(status.ErrSys, "业务调整，该功能目前不可用")
	}

	//ctx2, ctx2Cancel := context.WithTimeout(ctx, time.Second*3)
	ctx2, ctx2Cancel := protogrpc.InheritContextWithInfoTimeout(ctx, time.Second*3)
	defer ctx2Cancel()

	//权限检查
	//err = s.checkPermission(ctx2, uid, cid)
	err = s.channelPermissionApi.CheckUserPermission(ctx2, uid, cid)
	if err != nil {
		return out, protocol.ToServerError(err)
	}

	//获取麦位信息
	micResp, err := s.channelMicClient.GetMicrList(ctx2, cid, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMicrList failed uid %d cid %d fail %v", uid, cid, err)
		return out, protocol.ToServerError(err)
	}

	count := len(micResp.AllMicList)
	if count == 0 {
		log.ErrorWithCtx(ctx, "麦上人数不足 uid %d cid %d count %d", uid, cid, count)
		return out, protocol.NewServerError(status.ErrSys, "麦上人数不足")
	}

	micUidList := make([]uint32, 0)
	micPosList := make([]uint32, 0)
	for _, micInfo := range micResp.AllMicList {
		if micInfo.MicUid > 0 {
			micUidList = append(micUidList, micInfo.MicUid)
			micPosList = append(micPosList, micInfo.MicId)
		}
	}

	//开始房间游戏
	gameResp, err := s.channelGameClient.StartChannelGame(ctx2, cid, gameId, channelGameTs, micPosList)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartChannelGame failed uid %d cid %d fail %v", uid, cid, err)
		return out, protocol.ToServerError(err)
	}

	if gameResp.BIsInTheGame != 0 {
		return out, protocol.NewServerError(status.ErrChannelFungameAlreadyStart, "该房间已经在进行游戏")
	}

	log.DebugWithCtx(ctx, "game succ,result:%v", gameResp.StrGameResult)

	msgId := strconv.FormatUint(uint64(uid), 10) + strconv.FormatUint(uint64(cid), 10) + strconv.FormatInt(time.Now().Unix(), 10)
	jsonResp := GameJsonRespond{
		MsgId:      msgId,
		GameId:     float64(pb.ChannelGameType_GAME_DRAW),
		ResultList: make([]GameResult, len(gameResp.GameResultList)),
		MsgData:    GameMsgData{StrStartGamePre: "房间正在进行小游戏抽签", StrGameResult: gameResp.StrGameResult, Ts: float64(channelGameTs)},
	}

	for i, res := range gameResp.GameResultList {
		jsonResp.ResultList[i] = GameResult{
			MicPosId:  float64(res.MicrophonePos),
			MicResult: float64(res.RandomRet)}
	}

	msg, err := json.Marshal(jsonResp)
	if err != nil {
		log.ErrorWithCtx(ctx, "json.Marshal failed uid %d cid %d err %v", uid, cid, err)
		return out, protocol.NewServerError(status.ErrSys, "json marshal failed")
	}

	logResp, _ := json.MarshalIndent(jsonResp, "", "    ")
	log.DebugWithCtx(ctx, "game jsonResp :%v", string(logResp))

	var channelSimpleInfo *channelsvrPb.ChannelSimpleInfo
	channelSimpleInfo, err = s.channelClient.GetChannelSimpleInfo(ctx2, uid, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelGame GetChannelSimpleInfo failed uid %d cid %d err(%v)", uid, cid, err)
		return out, protocol.ToServerError(err)
	}

	//不可靠广播
	var usersMap map[uint32]*ga_base.UserProfile
	if channelSimpleInfo != nil && s.IsPgc(channelSimpleInfo.GetChannelType()) {
		usersMap, err = s.userProfileApiCli.BatchGetUserProfile(ctx2, []uint32{uid})
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelGame BatchGetUserProfile failed uid %d err(%v)", uid, err)
			return out, protocol.ToServerError(err)
		}
	}
	s.pushChannelMsg(ctx2, uid, cid, uint32(pb.ChannelMsgType_CHANNEL_FUN_GAME_RESULT_MSG), usersMap, msg)

	//对麦上用户进行单播
	s.pushChannel1v1ReliableMsg(ctx2, micUidList, uid, cid, uint32(pb.ChannelMsgType_CHANNEL_FUN_GAME_RESULT_MSG), usersMap, msg)

	//oss上报
	datacenter.StdReport("************", cid, uint32(pb.ChannelGameType_GAME_DRAW))

	return out, protocol.ToServerError(err)
}

// 魔法表情入口
// Deprecated:已迁移到channelext-logic-go
func (s *ChannelMiniGameGoLogic) SendMagicExpression(ctx context.Context, in *pb.SendMagicExpressionReq) (out *pb.SendMagicExpressionResp, err error) {
	out = &pb.SendMagicExpressionResp{}

	log.DebugWithCtx(ctx, "SendMagicExpression:%v", utils.ToJson(in))

	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "grpc.ServiceInfoFromContext failed %v", err)
		return out, protocol.NewServerError(status.ErrSys, getUserInfoErrorMsg)
	}

	uid := serviceInfo.UserID
	cid := in.ChannelId
	magicId := in.MagicId
	optMaxNum := in.OptMaxNum
	magicNumList := in.MagicNumList

	ctx2, ctx2Cancel := context.WithTimeout(ctx, time.Second*3)
	defer ctx2Cancel()

	//封禁检查
	// 检查是否被封禁
	if banned, _ := s.checkChannelBanned(ctx2, uid, cid); banned {
		return out, protocol.NewServerError(status.ErrGreenbabaSanctionChannel)
	}
	if banned, _ := s.checkUserBanned(ctx2, uid); banned {
		return out, protocol.NewServerError(status.ErrGreenbabaSanctionOpUser)
	}

	//房间是否在进行小游戏
	inTheGame, err := s.channelGameClient.CheckChannelGameStatus(ctx2, cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckChannelGameStatus failed cid %d err %v", cid, err)
		return out, protocol.ToServerError(err)
	}

	if inTheGame != 0 {
		log.ErrorWithCtx(ctx, "no NobilityPrivilage userId %d", uid)
		return out, protocol.NewServerError(status.ErrChannelFungameAlreadyStart, "该房间在进行小游戏哦")
	}

	// 摇塞子类型的魔法表情 下架
	if optMaxNum > 0 || len(magicNumList) > 0 {
		return out, protocol.NewServerError(status.ErrSys, "业务调整，该功能目前不可用")
	}

	//互动表情处理
	/*	if pb.MagicExpressionType(in.GetMagicType()) == pb.MagicExpressionType_MAGIC_INTERACTION {
		err = s.sendInteractionExpression(ctx, uid, in)
		return out, err
	}*/

	//获取配置
	conf := business.GetMagicExpresConfig()
	oldMagicId := magicId
	if magicId >= conf.NewMagicIdMin && magicId <= conf.NewMagicIdMax {
		oldMagicId = conf.New2Old[magicId]
	}

	log.InfoWithCtx(ctx, "oldMagic:%d,magicId:%d,min:%d,max:%d,new2old %v", oldMagicId, magicId, conf.NewMagicIdMin, conf.NewMagicIdMax, conf.New2Old)

	//当id属于贵族专属魔法表情时，检查用户是否拥有该贵族特权
	if magicId >= conf.NewMagicIdMin && magicId <= conf.NewMagicIdMax {
		result, err := s.checkNobilityPrivilage(ctx2, uid, uint32(sync.ENobilityPrivilegeType_ENUM_NOBILITY_EXCLUSIVE_MAGICEXPRESSION))
		if err != nil {
			return out, protocol.ToServerError(err)
		}

		if !result {
			log.InfoWithCtx(ctx, "no NobilityPrivilage userId %d", uid)
			return out, protocol.NewServerError(status.ErrNobilityExclusiveMagicexpression)
		}
	}

	//获取麦位信息
	micResp, err := s.channelMicClient.GetMicrList(ctx2, cid, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMicrList failed uid %d cid %d fail %v", uid, cid, err)
		return out, protocol.ToServerError(err)
	}

	var micId uint32 = 0
	recvMicUid := make([]uint32, 0)
	for _, micInfo := range micResp.AllMicList {
		if micInfo.MicUid > 0 {
			recvMicUid = append(recvMicUid, micInfo.MicUid)
			if micInfo.MicUid == uid {
				micId = micInfo.MicId
			}
		}
	}

	//用户没有在麦上
	if micId == 0 {
		log.ErrorWithCtx(ctx, "user not hold microphone userId %d", uid)
		return out, protocol.NewServerError(status.ErrChannelUserNotHoldMicrophone, "用户没有上麦")
	}

	msgId := strconv.FormatUint(uint64(uid), 10) + strconv.FormatUint(uint64(cid), 10) + strconv.FormatInt(time.Now().Unix(), 10)
	jsonResp := MagicExpresRespond{
		MsgId:      msgId,
		MicId:      float64(micId),
		Uid:        float64(uid),
		OldMagicId: float64(oldMagicId),
		NewMagicId: float64(magicId),
		Random:     float64(0),
		ResultList: make([]string, 0),
	}

	ukwUserInfo, err := s.GetUKWUserInfo(ctx, uid, cid)

	if ukwUserInfo == nil {
		log.WarnWithCtx(ctx, "ChannelMiniGameGoLogic.SendMagicExpression GetUKWUserInfo ukwUserInfo is nil,uid is %d,cid is %d", uid, cid)
		return out, nil
	}

	//不可靠广播
	usersMap := make(map[uint32]*ga_base.UserProfile)
	usersMap[uid] = ukwUserInfo

	jsonResp.Uid = float64(ukwUserInfo.GetUid())

	msg, err := json.Marshal(jsonResp)
	if err != nil {
		log.ErrorWithCtx(ctx, "json.Marshal failed uid %d cid %d err %v", uid, cid, err)
		return out, protocol.NewServerError(status.ErrSys, "json marshal failed")
	}

	logResp, _ := json.MarshalIndent(jsonResp, "", "    ")
	log.DebugWithCtx(ctx, "magicexpres jsonResp :%v", string(logResp))

	s.pushChannelMsg(ctx2, uid, cid, uint32(pb.ChannelMsgType_CHANNEL_MAGIC_EXPRESSION_MSG), usersMap, msg)

	//对麦上用户进行单播
	s.pushChannel1v1ReliableMsg(ctx2, recvMicUid, uid, cid, uint32(pb.ChannelMsgType_CHANNEL_MAGIC_EXPRESSION_MSG), usersMap, msg)

	return out, protocol.ToServerError(err)
}

func (s *ChannelMiniGameGoLogic) checkNobilityPrivilage(ctx context.Context, uid, privilageId uint32) (bool, protocol.ServerError) {
	//获取用户等级
	info, err := s.nobilityClient.GetNobilityInfo(ctx, uid, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkNobilityPrivilage failed userId %d err %v", uid, err)
		return false, protocol.NewServerError(status.ErrSys, err.Error())
	}

	conf := business.GetNobilityConfig()
	for _, levelConf := range conf.NLevel {
		if levelConf.Level == info.Level {
			for _, priId := range levelConf.PrivilegeList {
				if privilageId == priId {
					return true, nil
				}
			}
		}
	}

	return false, nil
}

func (s *ChannelMiniGameGoLogic) checkChannelBanned(ctx context.Context, uid, cid uint32) (bool, protocol.ServerError) {
	bannedStat, berr := s.greenbabaClient.GetCurrBannedStatById(ctx, uid, uint32(gpb.ENUM_TARGET_TYPE_E_TARGET_TYPE_CHANNEL), cid)
	if berr != nil {
		log.ErrorWithCtx(ctx, "checkChannelBanned failed channelId(%v) userId(%v) err(%v)", cid, uid, berr)
		//greenbaba服务错误，不影响业务使用
		return false, protocol.NewServerError(status.ErrSys, berr.Error())
	}
	for _, elem := range bannedStat {
		log.DebugWithCtx(ctx, "GetCurrBannedStatById uid(%d) cid(%d) elem(%v)", uid, cid, elem)
		if elem.TargetType != uint32(gpb.ENUM_TARGET_TYPE_E_TARGET_TYPE_CHANNEL) || elem.Id != cid {
			continue
		}
		if elem.GetBannedType() == uint32(gpb.ENUM_BANNED_TYPE_E_BANNED_CHANNEL) && elem.RemainSecond > 0 {
			return true, nil
		}
	}

	return false, nil
}

func (s *ChannelMiniGameGoLogic) checkUserBanned(ctx context.Context, uid uint32) (bool, protocol.ServerError) {
	stat, err := s.greenbabaClient.GetCurrBannedStatById(ctx, uid, uint32(gpb.ENUM_TARGET_TYPE_E_TARGET_TYPE_USER), uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "Fail GetCurrBannedStatById uid(%d) err(%v)", uid, err)
		//greenbaba服务错误，不影响业务使用
		return false, protocol.NewServerError(status.ErrSys, err.Error())
	}
	for _, elem := range stat {
		log.DebugWithCtx(ctx, "GetCurrBannedStatById uid(%d) elem(%v)", uid, elem)
		if elem.TargetType != uint32(gpb.ENUM_TARGET_TYPE_E_TARGET_TYPE_USER) || elem.Id != uid {
			continue
		}
		if elem.GetBannedType() == uint32(gpb.ENUM_BANNED_TYPE_E_BANNED_CHANNEL) && elem.RemainSecond > 0 {
			// 被过滤的用户,超时处理
			log.InfoWithCtx(ctx, "Filter greenbaba uid(%d) elem(%+v)", uid, elem)
			return true, nil

		}
	}

	return false, nil
}

func (s *ChannelMiniGameGoLogic) checkPermission(ctx context.Context, uid, cid uint32) protocol.ServerError {

	simpleInfo, err := s.channelClient.GetChannelSimpleInfo(ctx, uid, cid)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetChannulSimpleInfo uid %d cid %d fail %d", uid, cid, err.Code())
		return err
	}

	channelType := simpleInfo.GetChannelType()
	//公会娱乐房
	if channelType == uint32(pb.ChannelType_GUILD_TYPE) {
		//查用户账号信息
		var guildId uint32
		err = s.accountClient.GetUserGuild(ctx, uid, &guildId)
		if nil != err {
			log.ErrorWithCtx(ctx, "GetUserGuild uid %d cid %d fail %d", uid, cid, err.Code())
			return protocol.NewServerError(err.Code())
		}

		//非公会成员
		if guildId != simpleInfo.GetBindId() {
			log.ErrorWithCtx(ctx, "user not guild member,uid %d cid %d guildid %d bindid %d", uid, cid, guildId, simpleInfo.GetBindId())
			return protocol.NewServerError(status.ErrChannelNoPermission)
		}

		// 普通成员没有权限开始游戏
		ok, err := s.checkGuildPermission(ctx, uid, guildId, Permission2ChannelManage)
		if err != nil {
			log.ErrorWithCtx(ctx, "checkGuildPermission failed,uid %d cid %d guildid %d", uid, cid, guildId)
			return protocol.NewServerError(err.Code())
		}
		if !ok {
			log.ErrorWithCtx(ctx, "no permission,uid %d cid %d guildid %d", uid, cid, guildId)
			return protocol.NewServerError(status.ErrChannelNoPermission)
		}

		return nil
	}

	// 个人娱乐房 和 公会公开娱乐房
	if channelType == uint32(pb.ChannelType_USER_CHANNEL_TYPE) ||
		channelType == uint32(pb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) ||
		channelType == uint32(pb.ChannelType_COMMUNITY_CHANNEL_TYPE) {
		if simpleInfo.GetCreaterUid() == uid {
			return nil
		}

		adminList, err := s.channelClient.GetChannelAdmin(ctx, uid, cid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelAdmin failed,uid %d cid %d", uid, cid)
			return protocol.NewServerError(err.Code())
		}
		for _, admin := range adminList {
			if admin.GetUid() == uid {
				return nil
			}
		}
		//不是管理员没有权限玩游戏
		return protocol.NewServerError(status.ErrChannelNoPermission)
	}

	return protocol.NewServerError(status.ErrChannelNoPermission)
}

func (s *ChannelMiniGameGoLogic) checkGuildPermission(ctx context.Context, uid, guildId, permission uint32) (bool, protocol.ServerError) {
	r, err := s.guildClient.GetGuildMember(ctx, uid, guildId)
	if err != nil {
		return false, protocol.ToServerError(err)
	}

	if r.Role == uint32(guildPb.E_GUILD_ROLE_TYPE_E_GUILD_ROLE_OWNER) {
		return true, nil
	}

	official, err := s.guildClient.GetGuildOfficialByUid(ctx, uid, guildId, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildOfficialByUid uid %d Failed: %v", uid, err)
		return false, protocol.ToServerError(err)
	}

	hasPermission := official.Info.Permission

	return (hasPermission & permission) == permission, nil
}

func (s *ChannelMiniGameGoLogic) checkLivePkPermission(ctx context.Context, uid, cid uint32) protocol.ServerError {

	simpleInfo, err := s.channelClient.GetChannelSimpleInfo(ctx, uid, cid)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetChannulSimpleInfo uid %d cid %d fail %d", uid, cid, err.Code())
		return err
	}

	channelType := simpleInfo.GetChannelType()

	// 直播房
	if channelType == uint32(pb.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		if simpleInfo.GetCreaterUid() == uid {
			return nil
		}

		adminList, err := s.channelClient.GetChannelAdmin(ctx, uid, cid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelAdmin failed,uid %d cid %d", uid, cid)
			return protocol.NewServerError(err.Code())
		}
		for _, admin := range adminList {
			if admin.GetUid() == uid {
				return nil
			}
		}
		//不是管理员没有权限
		return protocol.NewServerError(status.ErrChannelNoPermission)
	}

	return protocol.NewServerError(status.ErrChannelNoPermission)
}

func (s *ChannelMiniGameGoLogic) GetChannelPresentRunwayList(ctx context.Context, in *pb.GetChannelPresentRunwayListReq) (*pb.GetChannelPresentRunwayListResp, error) {
	out := &pb.GetChannelPresentRunwayListResp{}
	channelId := in.GetChannelId()

	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetChannelPresentRunwayList ServiceInfoFromContext fail. in:%+v", in)
		return out, protocol.NewServerError(status.ErrBadRequest)
	}
	opUid := svrInfo.UserID

	if channelId == 0 {
		return out, protocol.NewServerError(status.ErrParam, "房间不存在")
	}

	resp, err := s.runwayCli.GetChannelRunwayList(ctx, opUid, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelPresentRunwayList fail to GetChannelRunwayList,opUid(%d) channelId(%d), err:%v", opUid, in.GetChannelId(), err)
		return out, protocol.ToServerError(err)
	}

	out.RunwayEffect = &pb.ChannelPresentRunwayEffect{
		RunwayPresentBase:   resp.GetRunwayEffect().GetRunwayPresentBase(),
		RunwayAddSeconds:    resp.GetRunwayEffect().GetRunwayAddSeconds(),
		MaxRunwayAddSeconds: resp.GetRunwayEffect().GetMaxRunwayAddSeconds(),
	}
	if len(resp.GetRunwayList()) == 0 {
		return out, nil
	}

	runwayList := resp.GetRunwayList()
	uidList := make([]uint32, 0, len(runwayList))
	for _, item := range runwayList {
		info := item.GetBriefInfo()
		uidList = append(uidList, info.GetUid())
	}

	userMap, err := s.accountClient.BatGetUserByUid(ctx, uidList...)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelPresentRunwayList err BatGetUserByUid:%+v", err)
		return out, protocol.ToServerError(err)
	}

	userNameList := make([]string, 0)
	for _, user := range userMap {
		userNameList = append(userNameList, user.GetUsername())
	}

	// 获取头像
	headImageMap, err := s.headImageCli.BatchGetHeadImageMd5(ctx, opUid, userNameList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelPresentRunwayList err BatchGetHeadImageMd5:%+v, channelId(%d)", err, channelId)
		return out, protocol.ToServerError(err)
	}

	for _, item := range runwayList {
		info := item.GetBriefInfo()
		conf := item.GetRunwayCfg()

		userInfo, ok := userMap[info.GetUid()]
		if !ok {
			log.InfoWithCtx(ctx, "GetChannelRunwayList fail to get userInfo:uid(%d)", info.GetUid())
			continue
		}

		faceImg, ok := headImageMap[userInfo.GetUsername()]
		if !ok {
			log.InfoWithCtx(ctx, "GetChannelRunwayList fail to get headImage:uid(%d)", info.GetUid())
			continue
		}

		lItem := &pb.ChannelPresentRunwayInfo{
			Uid:             info.GetUid(),
			Account:         userInfo.GetUsername(),
			Nickname:        userInfo.GetNickname(),
			FaceMd5:         faceImg,
			ChannelId:       channelId,
			Level:           info.GetRunwayLevel(),
			ExpiredTime:     info.GetExpiredTime(),
			PresentValue:    info.GetCurValue(),
			PresentValueMin: conf.GetBeginValue(),
			PresentValueMax: conf.GetEndValue(),
		}
		s.UKWPHandle(ctx, lItem) // 增加神秘人处理

		out.RunwayList = append(out.RunwayList, lItem)
	}

	log.DebugWithCtx(ctx, "GetChannelPresentRunwayList req: %+v, resp: %+v", in, out)
	return out, nil
}

// UKWPHandle 神秘人处理
func (s *ChannelMiniGameGoLogic) UKWPHandle(ctx context.Context, info *pb.ChannelPresentRunwayInfo) {
	// 判断是否在神秘人生效房间中
	var channelSimpleInfo *channelsvrPb.ChannelSimpleInfo
	channelSimpleInfo, err := s.channelClient.GetChannelSimpleInfo(ctx, info.GetUid(), info.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "UKWPHandle GetChannelSimpleInfo failed uid %d cid %d err(%v)", info.GetUid(), info.GetChannelId(), err)
		return
	}
	if channelSimpleInfo == nil || !s.IsPgc(channelSimpleInfo.GetChannelType()) {
		return
	}

	if s.ukwCli == nil {
		return
	}
	ukwInfo, _ := s.ukwCli.GetUKWInfo(ctx, info.GetUid())

	info.UserUkwInfo = &ga_base.UserUKWInfo{
		Level: ukwInfo.GetUkwPersonInfo().GetLevel(),
		Medal: ukwInfo.GetUkwPersonInfo().GetMedal(),
	}

	info.UserProfile, _ = s.userProfileApiCli.GetUserProfile(ctx, info.GetUid())
	if info.GetUserProfile().GetPrivilege().GetType() == uint32(ga_base.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW) {
		info.Uid = info.GetUserProfile().GetUid()
		info.Account = info.GetUserProfile().GetPrivilege().GetAccount()
		info.Nickname = info.GetUserProfile().GetPrivilege().GetNickname()
	}
}

func (s *ChannelMiniGameGoLogic) ChannelPresentCountInit(ctx context.Context, in *pb.GetChannelPresentCountStateReq) (*pb.GetChannelPresentCountStateResp, error) {
	out := &pb.GetChannelPresentCountStateResp{}
	channelId := in.GetChannelId()

	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetChannelPresentRunwayList ServiceInfoFromContext fail. in:%+v", in)
		return out, protocol.NewServerError(status.ErrBadRequest)
	}
	opUid := svrInfo.UserID

	if channelId == 0 {
		return out, protocol.NewServerError(status.ErrParam, "房间不存在")
	}

	resp, err := s.presentCntCli.GetPresentCountState(ctx, opUid, &presentCntPb.GetPresentCountStateReq{
		ChannelId: channelId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelPresentCountInit fail GetPresentCountState, channelId(%d), err:%v", channelId, err)
		return out, err
	}

	if !resp.GetState() {
		out.Status = false
		return out, nil
	}

	channelInfo, err := s.channelClient.GetChannelSimpleInfo(ctx, opUid, channelId)
	if err != nil {
		log.Errorf("ChannelPresentCountInit fail to GetChannelSimpleInfo uid:%d, cid:%d, err:%v", opUid, channelId, err)
	}

	out.Status = true
	micCountS := resp.GetMicsPresentCount()
	uidList := make([]uint32, 0)
	for _, item := range micCountS {
		uidList = append(uidList, item.GetUid())
	}

	upMap := make(map[uint32]*ga_base.UserProfile)
	if channelInfo.GetChannelType() == uint32(pb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) ||
		channelInfo.GetChannelType() == uint32(pb.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) ||
		channelInfo.GetChannelType() == uint32(pb.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		upMap, err = s.userProfileApiCli.BatchGetUserProfile(ctx, uidList)
		if err != nil {
			log.Errorf("ChannelPresentCountInit fail to BatchGetUserProfile uid:%d, cid:%d, uids:%v, err:%v", opUid, channelId, uidList, err)
		}
	}

	for _, item := range micCountS {
		info := &pb.ChannelMicPresentCountInfo{
			Uid:      item.GetUid(),
			Price:    item.GetPrice(),
			BgSuffix: item.GetBgSuffix(),
			IcSuffix: item.GetIcSuffic(),
		}
		upInfo, ok := upMap[item.GetUid()]
		if ok && upInfo.GetPrivilege() != nil {
			info.Uid = upInfo.GetUid()
		}

		out.PresentCountInfos = append(out.PresentCountInfos, info)
	}

	out.StylePrefix = resp.GetStylePrefix()
	out.BgSuffix = resp.GetBgSuffix()
	out.IcSuffix = resp.GetIcSuffic()
	out.UncompiledPrefix = resp.GetUncompiledPrefix()

	return out, nil
}

func (s *ChannelMiniGameGoLogic) checkPermissionV2(ctx context.Context, uid, cid uint32) (uint32, protocol.ServerError) {

	simpleInfo, err := s.channelClient.GetChannelSimpleInfo(ctx, uid, cid)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetChannelSimpleInfo uid %d cid %d fail %d", uid, cid, err.Code())
		return 0, err
	}

	channelType := simpleInfo.GetChannelType()
	//公会娱乐房
	if channelType == uint32(pb.ChannelType_GUILD_TYPE) ||
		channelType == uint32(pb.ChannelType_GUILD_HOME_CHANNEL_TYPE) {
		//查用户账号信息
		var guildId uint32
		err = s.accountClient.GetUserGuild(ctx, uid, &guildId)
		if nil != err {
			log.ErrorWithCtx(ctx, "GetUserGuild uid %d cid %d fail %d", uid, cid, err.Code())
			return channelType, protocol.NewServerError(err.Code())
		}

		//非公会成员
		if guildId != simpleInfo.GetBindId() {
			log.ErrorWithCtx(ctx, "user not guild member,uid %d cid %d guildid %d bindid %d", uid, cid, guildId, simpleInfo.GetBindId())
			return channelType, protocol.NewServerError(status.ErrChannelNoPermission)
		}

		// 普通成员没有权限开始游戏
		ok, err := s.checkGuildPermission(ctx, uid, guildId, Permission2ChannelManage)
		if err != nil {
			log.ErrorWithCtx(ctx, "checkGuildPermission failed,uid %d cid %d guildid %d", uid, cid, guildId)
			return channelType, protocol.NewServerError(err.Code())
		}
		if !ok {
			log.ErrorWithCtx(ctx, "no permission,uid %d cid %d guildid %d", uid, cid, guildId)
			return channelType, protocol.NewServerError(status.ErrChannelNoPermission)
		}

		return channelType, nil
	}

	// 个人娱乐房 和 公会公开娱乐房等
	if channelType == uint32(pb.ChannelType_USER_CHANNEL_TYPE) ||
		channelType == uint32(pb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) ||
		channelType == uint32(pb.ChannelType_TRIVIA_GAME_CHANNEL_TYPE) ||
		channelType == uint32(pb.ChannelType_RADIO_LIVE_CHANNEL_TYPE) ||
		channelType == uint32(pb.ChannelType_CPL_SUPER_CHANNEL_TYPE) ||
		channelType == uint32(pb.ChannelType_COMMUNITY_CHANNEL_TYPE) {
		if simpleInfo.GetCreaterUid() == uid {
			return channelType, nil
		}

		adminList, err := s.channelClient.GetChannelAdmin(ctx, uid, cid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelAdmin failed,uid %d cid %d", uid, cid)
			return channelType, protocol.NewServerError(err.Code())
		}
		for _, admin := range adminList {
			if admin.GetUid() == uid {
				return channelType, nil
			}
		}
		//不是管理员没有权限玩游戏
		log.ErrorWithCtx(ctx, "no permission,uid %d cid %d ", uid, cid)
		return channelType, protocol.NewServerError(status.ErrChannelNoPermission)
	}

	log.ErrorWithCtx(ctx, "no permission,uid %d cid %d ", uid, cid)
	return channelType, protocol.NewServerError(status.ErrChannelNoPermission)
}

func (s *ChannelMiniGameGoLogic) ChannelPresentCountSwitch(ctx context.Context, in *pb.ChannelPresentCountReq) (*pb.ChannelPresentCountResp, error) {
	var err error
	out := &pb.ChannelPresentCountResp{}
	channelId := in.GetChannelId()

	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "ChannelPresentCountSwitch ServiceInfoFromContext fail. in:%+v", in)
		return out, protocol.NewServerError(status.ErrBadRequest)
	}
	opUid := svrInfo.UserID

	if channelId == 0 {
		return out, protocol.NewServerError(status.ErrParam, "房间不存在")
	}

	// 权限校验
	channelType, sErr := s.checkPermissionV2(ctx, opUid, channelId)
	if sErr != nil {
		return out, protocol.ToServerError(sErr)
	}

	var counterResp *presentCntPb.GetPresentCountStateResp
	if !in.GetOnOrOff() {
		// 数据上报需求：关闭计数器操作前，需要先获取本轮的计数列表
		counterResp, err = s.presentCntCli.GetPresentCountState(ctx, opUid, &presentCntPb.GetPresentCountStateReq{
			ChannelId: channelId,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelPresentCountInit fail GetPresentCountState, channelId(%d), err:%v", channelId, err)
			return out, err
		}

		if !counterResp.GetState() {
			//计数器已经关闭,直接return
			return out, nil
		}
	}

	req := &presentCntPb.PresentCountReq{
		ChannelId:   channelId,
		IsOff:       in.GetOnOrOff(),
		Uid:         opUid,
		ChannelType: channelType,
	}
	if in.GetOnOrOff() {
		// 获取房间麦位用户
		micResp, err := s.channelMicClient.GetMicrList(ctx, channelId, opUid)
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelPresentCountSwitch fail GetMicrList,channelId(%d), err:%v", channelId, err)
			return out, protocol.ToServerError(err)
		}
		micList := micResp.GetAllMicList()
		for _, item := range micList {
			req.MicrUsers = append(req.MicrUsers, item.GetMicUid())
		}
	}

	_, err = s.presentCntCli.SwitchPresentCount(ctx, opUid, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelPresentCountSwitch fail SwitchPresentCount,channelId(%d), err:%v", channelId, err)
		return out, protocol.ToServerError(err)
	}

	if !in.GetOnOrOff() {
		//仅当成功关闭计数器时,上报本场计数
		go func() {
			c, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()

			userListStr := genUserListStr(counterResp.GetMicsPresentCount())
			s.reportCounterOffByLink(c, svrInfo.MarketID, opUid, channelId, channelType, userListStr)
		}()
	}
	return out, nil
}

type ReportDataUserList struct {
	Uid   uint32 `json:"uid"`
	Value uint32 `json:"value"`
}

func genUserListStr(countInfo []*presentCntPb.MicsPresentCountInfo) string {
	if len(countInfo) == 0 {
		return ""
	}

	userList := make([]*ReportDataUserList, 0)
	for _, v := range countInfo {
		userList = append(userList, &ReportDataUserList{
			Uid:   v.GetUid(),
			Value: v.GetPrice(), // T豆单位
		})
	}

	userListStr, err := json.Marshal(userList)
	if err != nil {
		log.Errorf("reportCounterOffByLink genUserListStr err:%v, userList:%v", err, userList)
		return ""
	}

	return string(userListStr)
}

// reportCounterOffByLink 每轮礼物计数-百灵上报
func (s *ChannelMiniGameGoLogic) reportCounterOffByLink(ctx context.Context, marketId, uid, channelId, channelType uint32, userListStr string) {

	tabId, err := s.topicChannelClient.GetChannelPlayModel(ctx, &topicChannelPb.GetChannelPlayModelReq{
		ChannelId: channelId,
	})
	if err != nil {
		log.Errorf("reportCounterOffByLink fail at GetChannelPlayModel,uid:%d,channelId(%d),err:%v,", uid, channelId, err)
		//return //可以接受拿不到tab_id的情况
	}

	// 组装上报数据
	data := map[string]interface{}{
		"app_type":  toAppType(marketId),
		"room_id":   fmt.Sprint(channelId),
		"room_type": fmt.Sprint(channelType),
		"uid_list":  userListStr,
		"tag_id":    tabId.GetTabId(),
		//"tag_name":  "",
	}

	// 上报数据
	byErr := bylink.Track(ctx, uint64(uid), "room_counter_log", data, false)
	if byErr != nil {
		log.ErrorWithCtx(ctx, "reportCounterOffByLink fail to bylink.Track error: %v", byErr)
		return
	}
	bylink.Flush()
	log.Infof("reportCounterOffByLink succ, data:[%+v]", data)
}

func toAppType(marketId uint32) string {
	switch basePb.BaseReq_MarketId(marketId) {
	case basePb.BaseReq_MARKET_HUANYOU:
		return "huanyou"
	case basePb.BaseReq_MARKET_ZAIYA:
		return "zaiya"
	case basePb.BaseReq_MARKET_MAIKE:
		return "maike"
	case basePb.BaseReq_MARKET_MIJING:
		return "mijing"
	case basePb.BaseReq_MARKET_NONE:
		return "ttvoice"
	}
	return ""
}

func (s *ChannelMiniGameGoLogic) GetPresentCountRank(ctx context.Context, req *pb.GetPresentCountRankReq) (*pb.GetPresentCountRankResp, error) {
	log.Debugf("GetPresentCountRank req:%+v", req)
	out := fillCounterRankNilResp()
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetPresentCountRank ServiceInfoFromContext fail. in:%+v", req)
		return out, protocol.NewServerError(status.ErrBadRequest)
	}
	opUid := svrInfo.UserID
	channelId := req.GetChannelId()
	targetUid := req.GetTargetUid()

	// 参数校验
	if channelId == 0 || targetUid == 0 || opUid == 0 {
		log.ErrorWithCtx(ctx, "GetPresentCountRank ServiceInfoFromContext fail. in:%+v", req)
		return out, protocol.NewServerError(status.ErrParam)
	}

	// 获取神秘人榜单处理：反查真实uid
	fakeRealMap := s.ukwCli.BatchGetMapTrueUidByFake(ctx, []uint32{targetUid})
	if fakeRealMap[targetUid] != 0 {
		targetUid = fakeRealMap[targetUid]
	}

	channelInfo, err := s.channelClient.GetChannelSimpleInfo(ctx, opUid, channelId)
	if err != nil {
		log.Errorf("GetPresentCountRank fail to GetChannelSimpleInfo uid:%d, cid:%d, err:%v", opUid, channelId, err)
		return out, err
	}

	// 计数器榜单功能仅支持PGC房
	if channelInfo.GetChannelType() != uint32(pb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
		return out, protocol.NewServerError(status.ErrChannelNoPermission)
	}

	svrResp, err := s.presentCntCli.GetPresentCountRank(ctx, opUid, &presentCntPb.GetPresentCountRankReq{
		Uid:       opUid,
		TargetUid: targetUid,
		ChannelId: channelId,
		Offset:    req.GetOffset(),
		Limit:     req.GetLimit(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentCountRank fail at GetPresentCountRank,req:%+v,err:%v", req, err)
		return out, err
	}

	// 批量获取神秘人信息
	uidList := make([]uint32, 0)
	for _, v := range svrResp.GetRankList() {
		uidList = append(uidList, v.Uid)
	}
	uidList = append(uidList, svrResp.GetTargetUser().GetUid(), svrResp.GetMyInfo().GetUid())
	mapUser, err := s.accountClient.GetUsersMap(ctx, uidList)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetChannelLivePkRankUser GetUsersMap req:%v sz:%v err:%v", req, len(uidList), err)
		return out, err
	}

	// 批量获取贵族信息
	nobilityMap, err := s.nobilityClient.BatchGetNobilityInfo(ctx, opUid, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentCountRank fail at BatchGetNobilityInfo,req:%+v, err:%v", req, err)
		return out, err
	}

	// 批量获取房间vip信息
	vipMemeMap, err := s.channelVipMemClient.BatGetUserConsumeInfo(ctx, opUid, channelId, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentCountRank fail at BatGetUserConsumeInfo,req:%+v, err:%v", req, err)
		return out, err
	}

	// 批量获取用户魅力值、财富值
	numMap, err := s.numericCli.BatchGetPersonalNumeric(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPresentCountRank fail at BatchGetPersonalNumeric,req:%+v, err:%v", req, err)
		return out, err
	}

	// 目标用户信息填充
	targetUkwInfo, err := s.userProfileApiCli.GetUserProfileV2(ctx, svrResp.GetTargetUser().GetUid(), true)
	if err != nil {
		log.Errorf("GetPresentCountRank fail to GetUserProfileV2 uid:%d,req.GetTargetUid:%d,cid:%d err:%v", opUid, svrResp.GetTargetUser().GetUid(), channelId, err)
		return out, err
	}
	out.TargetUserInfo = &pb.CounterTargetUserInfo{
		UserProfile:    If(targetUkwInfo == nil, &ga_base.UserProfile{}, targetUkwInfo),
		Receive:        svrResp.GetTargetUser().GetReceive(),
		NextLevelValue: svrResp.GetTargetUser().GetNextLevelValue(),
		NextLvUrl:      svrResp.GetTargetUser().GetNextLevelUrl(),
	}

	// 榜单用户信息填充
	for _, v := range svrResp.GetRankList() {
		var rich, charm uint32 = 0, 0
		if num, ok := numMap[v.GetUid()]; ok {
			rich = uint32(int(num.GetRich64() / 1000))
			charm = uint32(int(num.GetCharm64() / 1000))
		}

		userInfo, ok := mapUser[v.GetUid()]
		if !ok {
			continue
		}

		item := fillCounterRankUserInfo(userInfo, v.GetUkwInfo(), v, rich, charm, vipMemeMap[v.GetUid()], nobilityMap[v.GetUid()])
		out.MemberList = append(out.MemberList, item)
	}

	// “我的”信息填充
	myInfo := svrResp.GetMyInfo()
	if myInfo.GetUid() == 0 {
		out.MyInfo = &pb.CounterRankUserInfo{
			UserProfile: fillCounterUserProfile(nil, nil),
		}

	} else {
		userInfo, ok := mapUser[myInfo.GetUid()]
		if !ok {
			log.ErrorWithCtx(ctx, "GetPresentCountRank fail at get MyInfo,opUid:%d", myInfo.GetUid())
			return &pb.GetPresentCountRankResp{}, nil
		}
		var rich, charm uint32 = 0, 0
		if num, ok := numMap[myInfo.GetUid()]; ok {
			rich = uint32(int(num.GetRich64() / 1000))
			charm = uint32(int(num.GetCharm64() / 1000))
		}
		out.MyInfo = fillCounterRankUserInfo(userInfo, svrResp.GetMyInfo().GetUkwInfo(), myInfo, rich, charm, vipMemeMap[myInfo.GetUid()], nobilityMap[myInfo.GetUid()])
	}

	out.ChannelId = channelId
	out.NextPageOffset = svrResp.GetNextOffset()
	out.ViewCnt = svrResp.GetViewCnt()

	log.Debugf("GetPresentCountRank out:%+v", out)
	return out, nil
}

func If(isTrue bool, a *ga_base.UserProfile, b *ga_base.UserProfile) *ga_base.UserProfile {
	if isTrue {
		return a
	} else {
		return b
	}
}

func fillCounterRankUserInfo(user *account.User, ukwInfo *presentCntPb.UkwInfo, info *presentCntPb.PresentCountRankUser, rich, charm uint32, vipInfo *channelMemVipPb.ChannelMemberVip,
	nobilityInfo *nobilitySvrPb.NobilityInfo) *pb.CounterRankUserInfo {
	item := &pb.CounterRankUserInfo{
		UserProfile: fillCounterUserProfile(user, ukwInfo),
		SendValue:   info.GetSendValue(),
		Rank:        info.GetRank(),
		DValue:      info.GetDValue(),
	}

	// 神秘人不返回贵族信息，贵族值等
	if item.GetUserProfile().GetPrivilege().GetType() != 0 {
		item.NewRichLevel = 0
		item.NewCharmLevel = 0
		item.VipLevel = &ga_base.ChannelMemberVipLevel{
			CurrVipLevelId: 0,
			CurrVipValue:   0,
			CurrVipName:    "",
			NextVipLevelId: 0,
			NextVipValue:   0,
			NextVipName:    "",
		}
		item.NobilityInfo = &ga_base.NobilityInfo{
			Level:       0,
			Value:       0,
			KeepValue:   0,
			CycleTs:     0,
			Visible:     false,
			Uid:         0,
			InfoVersion: 0,
		}
	} else {
		item.NewRichLevel = rich
		item.NewCharmLevel = charm
		item.VipLevel = fillCounterRankUserVipLevel(vipInfo)
		item.NobilityInfo = fillCounterRankUserNobility(nobilityInfo)
	}
	return item
}

func fillCounterRankUserVipLevel(info *channelMemVipPb.ChannelMemberVip) *ga_base.ChannelMemberVipLevel {
	if info == nil {
		return nil
	} else {
		return &ga_base.ChannelMemberVipLevel{
			CurrVipLevelId: info.GetCurrLevelId(),
			CurrVipValue:   info.GetCurrLevelValue(),
			CurrVipName:    info.GetCurrLevelName(),
			NextVipLevelId: info.GetNextLevelId(),
			NextVipValue:   info.GetNextLevelMinValue(),
			NextVipName:    info.GetNextLevelName(),
		}
	}
}

func fillCounterRankUserNobility(info *nobilitySvrPb.NobilityInfo) *ga_base.NobilityInfo {
	if info == nil {
		return nil
	} else {
		return &ga_base.NobilityInfo{
			Level:   info.GetLevel(),
			Value:   info.GetValue(),
			Visible: info.GetInvisible(),
			Uid:     info.GetUid(),
			FLevel:  info.GetFLevel(),
		}
	}
}

func fillCounterUserProfile(user *account.User, ukwInfo *presentCntPb.UkwInfo) *ga_base.UserProfile {
	log.Debugf("fillCounterUserProfile begin userInfo:%v ukwInfo:%v", user, ukwInfo)

	uid, valAccount, nickName := user.GetUid(), user.GetUsername(), user.GetNickname()
	privilegeType := uint32(ga_base.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UNKNOWN)
	if ukwInfo.GetLevel() > 0 {
		valAccount = ukwInfo.GetAccount()
		nickName = ukwInfo.GetNickname()
		uid = ukwInfo.GetFakeUid()
		privilegeType = uint32(ga_base.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW)
	}

	userProfile := &ga_base.UserProfile{
		Uid:          uid,
		Account:      valAccount,
		Nickname:     nickName,
		AccountAlias: user.GetAlias(),
		Sex:          uint32(user.GetSex()),
		Privilege: &ga_base.UserPrivilege{
			Account:  ukwInfo.GetAccount(),
			Nickname: ukwInfo.GetNickname(),
		},
	}

	userUkwInfo := &ga_base.UserUKWInfo{
		Level:     ukwInfo.GetLevel(),
		Medal:     ukwInfo.GetMedal(),
		HeadFrame: ukwInfo.GetHeadFrame(),
	}

	byteUkwInfo, err := proto.Marshal(userUkwInfo)
	if err != nil {
		log.Errorf("fillCounterUserProfile json.Marshal failed info:%v err:%v", userUkwInfo, err)
		return userProfile
	}

	userProfile.Privilege.Type = privilegeType
	userProfile.Privilege.Options = byteUkwInfo

	log.Debugf("fillCounterUserProfile end userInfo:%v ukwInfo:%v userProfile:%v", user, ukwInfo, userProfile)

	return userProfile
}

func fillCounterRankNilResp() *pb.GetPresentCountRankResp {
	return &pb.GetPresentCountRankResp{
		BaseResp:       nil,
		ChannelId:      0,
		NextPageOffset: 0,
		ViewCnt:        0,
		TargetUserInfo: &pb.CounterTargetUserInfo{
			UserProfile:    fillCounterUserProfile(nil, nil),
			Receive:        0,
			NextLevelValue: 0,
			NextLvUrl:      "",
		},
		MyInfo: &pb.CounterRankUserInfo{
			UserProfile:   fillCounterUserProfile(nil, nil),
			SendValue:     0,
			Rank:          0,
			DValue:        0,
			NewRichLevel:  0,
			NewCharmLevel: 0,
			VipLevel:      nil,
			NobilityInfo:  nil,
		},
		MemberList: nil,
	}
}
