package server

import (
	"context"
	"testing"

	"golang.52tt.com/pkg/config"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/channel"
	_ "golang.52tt.com/services/recommend-dialog/tools/grpc_proxy/enable"
)

var svr *ChannelMiniGameGoLogic

func init() {
	cfg := config.ServerConfig{}
	cfg.InitWithPath("json", "../channel-minigame-go-logic.json")

	svr, _ = NewChannelMiniGameGoLogic(context.Background(), cfg.Configer)
}

func TestChannelMiniGameGoLogic_GetChannelPresentRunwayList(t *testing.T) {
	ctx := protogrpc.WithServiceInfo(context.TODO(), &protogrpc.ServiceInfo{
		SessionKey:    nil,
		DeviceID:      nil,
		ClientType:    0,
		TerminalType:  0,
		UserID:        123,
		ClientIP:      0,
		CommandID:     0,
		ClientVersion: 0,
		MarketID:      0,
		IsRobot:       false,
		RequestID:     "",
		DyeId:         0,
		PrefixDict:    nil,
	})
	svr.GetChannelPresentRunwayList(ctx, &pb.GetChannelPresentRunwayListReq{
		BaseReq:   nil,
		ChannelId: 0,
	})
}
