package server

import (
	"context"
	"errors"
	"fmt"
	context0 "golang.org/x/net/context"
	"time"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/gold-commission"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	"golang.52tt.com/services/gold-commission/conf"
	"golang.52tt.com/services/gold-commission/event"
	"golang.52tt.com/services/gold-commission/manager"
)

type GoldCommissionServer struct {
	sc  *conf.ServiceConfigT
	mgr *manager.Manager

	kafkaKnightSub  *event.KnightEventLinkSub
	kafkaPresentSub *event.PresentEventLinkSub
	kafkaTBeanSub   *event.TBeanEventLinkSub
	kafkaESportSub  *event.ESportEventLinkSub
}

func (s *GoldCommissionServer) GetESportCoachMonthIncomeList(ctx context0.Context, req *pb.GetESportCoachMonthIncomeListReq) (*pb.GetESportCoachMonthIncomeListResp, error) {
	return s.mgr.GetESportCoachMonthIncomeList(ctx, req)
}

func (s *GoldCommissionServer) GetESportCoachDayIncomeList(ctx context0.Context, req *pb.GetESportCoachDayIncomeListReq) (*pb.GetESportCoachDayIncomeListResp, error) {
	return s.mgr.GetESportCoachDayIncomeList(ctx, req)
}

func (s *GoldCommissionServer) GetESportGoldOrderCount(ctx context0.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	return s.mgr.GetESportGoldOrderCount(ctx, req)
}

func (s *GoldCommissionServer) GetESportGoldOrderList(ctx context0.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	return s.mgr.GetESportGoldOrderList(ctx, req)
}

func (s *GoldCommissionServer) ReplaceESportGoldOrder(ctx context0.Context, req *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error) {
	return s.mgr.ReplaceESportGoldOrder(ctx, req)
}

func (s *GoldCommissionServer) GetGuildsESportCoachStat(ctx context0.Context, req *pb.GetGuildsESportCoachStatReq) (*pb.GetGuildsESportCoachStatResp, error) {
	return s.mgr.GetGuildsESportCoachStat(ctx, req)
}

func (s *GoldCommissionServer) GetESportIncomeDetail(ctx context0.Context, req *pb.GetESportIncomeDetailReq) (*pb.GetESportIncomeDetailRsp, error) {
	return s.mgr.GetESportIncomeDetail(ctx, req)
}

func (s *GoldCommissionServer) GetESportDayIncomeList(ctx context0.Context, req *pb.GetGuildDayIncomeListReq) (*pb.GetESportDayIncomeListResp, error) {
	return s.mgr.GetESportDayIncomeList(ctx, req)
}

func (s *GoldCommissionServer) GetESportMonthIncomeList(ctx context0.Context, req *pb.GetGuildMonthIncomeListReq) (*pb.GetESportMonthIncomeListResp, error) {
	return s.mgr.GetESportMonthIncomeList(ctx, req)
}

func (s *GoldCommissionServer) GetESportWeekIncome(ctx context0.Context, req *pb.GetESportWeekIncomeReq) (*pb.GetESportWeekIncomeRsp, error) {
	return s.mgr.GetESportWeekIncome(ctx, req)
}

// 获取互动游戏额外奖励
func (s *GoldCommissionServer) GetInteractGameExtraIncome(ctx context.Context, in *pb.GetInteractGameExtraIncomeReq) (*pb.GetInteractGameExtraIncomeResp, error) {
	return s.mgr.GetInteractGameExtraIncome(ctx, in)
}

func (s *GoldCommissionServer) GetGuildInteractGamePer(ctx context.Context, in *pb.GetGuildInteractGamePerReq) (*pb.GetGuildInteractGamePerResp, error) {
	return s.mgr.GetGuildInteractGamePer(ctx, in)
}

func (s *GoldCommissionServer) GetInteractGameGoldPresentOrderCount(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	return s.mgr.GetInteractGameGoldPresentOrderCount(ctx, req)
}

func (s *GoldCommissionServer) GetInteractGameGoldPresentOrderList(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	return s.mgr.GetInteractGameGoldPresentOrderList(ctx, req)
}

func (s *GoldCommissionServer) ReplaceInteractGameGoldPresentOrder(ctx context.Context, req *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error) {
	return s.mgr.ReplaceInteractGameGoldPresentOrder(ctx, req)
}

func (s *GoldCommissionServer) GetGuildsInteractGameAnchorStat(ctx context.Context, req *pb.GetGuildsInteractGameAnchorStatReq) (*pb.GetGuildsInteractGameAnchorStatResp, error) {
	return s.mgr.GetGuildsInteractGameAnchorStat(ctx, req)
}

func (s *GoldCommissionServer) GetInteractGameWeekIncome(ctx context.Context, req *pb.GetInteractGameWeekIncomeReq) (*pb.GetInteractGameWeekIncomeRsp, error) {
	return s.mgr.GetInteractGameWeekIncome(ctx, req)
}

func (s *GoldCommissionServer) GetInteractGameIncomeDetail(ctx context.Context, req *pb.GetInteractGameIncomeDetailReq) (*pb.GetInteractGameIncomeDetailRsp, error) {
	return s.mgr.GetInteractGameIncomeDetail(ctx, req)
}

func (s *GoldCommissionServer) GetInteractGameDayIncomeList(ctx context.Context, req *pb.GetGuildDayIncomeListReq) (*pb.GetInteractGameDayIncomeListResp, error) {
	return s.mgr.GetInteractGameDayIncomeList(ctx, req)
}

func (s *GoldCommissionServer) GetInteractGameMonthIncomeList(ctx context.Context, req *pb.GetGuildMonthIncomeListReq) (*pb.GetInteractGameMonthIncomeListResp, error) {
	return s.mgr.GetInteractGameMonthIncomeList(ctx, req)
}

func (s *GoldCommissionServer) SettlementYuyinGold(ctx context.Context, req *pb.SettlementYuyinGoldReq) (*pb.SettlementYuyinGoldResp, error) {
	resp := new(pb.SettlementYuyinGoldResp)
	return resp, nil
}

func (s *GoldCommissionServer) GetYuyinGoldPresentOrderCount(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	return s.mgr.GetYuyinGoldPresentOrderCount(ctx, req)
}

func (s *GoldCommissionServer) GetYuyinGoldPresentOrderList(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	return s.mgr.GetYuyinGoldPresentOrderList(ctx, req)
}

func (s *GoldCommissionServer) ReplaceYuyinGoldPresentOrder(ctx context.Context, req *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error) {
	return s.mgr.ReplaceYuyinGoldPresentOrder(ctx, req)
}

func (s *GoldCommissionServer) GetYuyinGoldKnightOrderCount(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	return s.mgr.GetYuyinGoldKnightOrderCount(ctx, req)
}

func (s *GoldCommissionServer) GetYuyinGoldKnightOrderList(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	return s.mgr.GetYuyinGoldKnightOrderList(ctx, req)
}

func (s *GoldCommissionServer) ReplaceYuyinGoldKnightOrder(ctx context.Context, req *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error) {
	return s.mgr.ReplaceYuyinGoldKnightOrder(ctx, req)
}

func (s *GoldCommissionServer) GetGuildsYuyinAnchorStat(ctx context.Context, req *pb.GetGuildsYuyinAnchorStatReq) (*pb.GetGuildsYuyinAnchorStatResp, error) {
	return s.mgr.GetGuildsYuyinAnchorStat(ctx, req)
}

func (s *GoldCommissionServer) GetGuildsAmuseChannelStat(ctx context.Context, req *pb.GetGuildsAmuseChannelStatReq) (*pb.GetGuildsAmuseChannelStatResp, error) {
	return s.mgr.GetGuildsAmuseChannelStat(ctx, req)
}

func (s *GoldCommissionServer) GetGuildsUnSettlementSummary(ctx context.Context, req *pb.GetGuildsUnSettlementSummaryReq) (*pb.GetGuildsUnSettlementSummaryRsp, error) {
	return s.mgr.GetGuildsUnSettlementSummary(ctx, req)
}

func (s *GoldCommissionServer) GetYuyinIncomeDetail(ctx context.Context, req *pb.GetYuyinIncomeDetailReq) (*pb.GetYuyinIncomeDetailRsp, error) {
	return s.mgr.GetYuyinIncomeDetail(ctx, req)
}

func (s *GoldCommissionServer) GetDayIncome(ctx context.Context, req *pb.GetDayIncomeReq) (*pb.GetDayIncomeRsp, error) {
	return s.mgr.GetDayIncome(ctx, req)
}

func (s *GoldCommissionServer) GetYuyinWeekIncome(ctx context.Context, req *pb.GetYuyinWeekIncomeReq) (*pb.GetYuyinWeekIncomeRsp, error) {
	return s.mgr.GetYuyinWeekIncome(ctx, req)
}

func (s *GoldCommissionServer) GetMonthIncome(ctx context.Context, req *pb.GetMonthIncomeReq) (*pb.GetMonthIncomeRsp, error) {
	return s.mgr.GetMonthIncome(ctx, req)
}

func (s *GoldCommissionServer) GetGuildYuyinExtraIncome(ctx context.Context, req *pb.GetGuildYuyinExtraIncomeReq) (*pb.GetGuildYuyinExtraIncomeRsp, error) {
	return s.mgr.GetGuildYuyinExtraIncome(ctx, req)
}

func (s *GoldCommissionServer) GetGuildYuyinTaskList(ctx context.Context, req *pb.GetGuildYuyinTaskListReq) (*pb.GetGuildYuyinTaskListRsp, error) {
	return s.mgr.GetGuildYuyinTaskList(ctx, req)
}

func (s *GoldCommissionServer) GetYuyinGuildDayIncomeList(ctx context.Context, req *pb.GetGuildDayIncomeListReq) (*pb.GetYuyinGuildDayIncomeListResp, error) {
	return s.mgr.GetYuyinGuildDayIncomeList(ctx, req)
}

func (s *GoldCommissionServer) GetYuyinGuildMonthIncomeList(ctx context.Context, req *pb.GetGuildMonthIncomeListReq) (*pb.GetGuildMonthIncomeListResp, error) {
	return s.mgr.GetYuyinGuildMonthIncomeList(ctx, req)
}

func (s *GoldCommissionServer) GetGuildMonthMemberList(ctx context.Context, req *pb.GetGuildMonthMemberListReq) (*pb.GetGuildMonthMemberListResp, error) {
	return s.mgr.GetGuildMonthMemberList(ctx, req)
}

func (s *GoldCommissionServer) SearchYuyinGuildDetail(ctx context.Context, req *pb.SearchYuyinGuildDetailReq) (*pb.SearchYuyinGuildDetailResp, error) {
	return s.mgr.SearchYuyinGuildDetail(ctx, req)
}

func (s *GoldCommissionServer) SearchAmuseGuildDetail(ctx context.Context, req *pb.SearchAmuseGuildDetailReq) (*pb.SearchAmuseGuildDetailResp, error) {
	return s.mgr.SearchAmuseGuildDetail(ctx, req)
}

func (s *GoldCommissionServer) GetGuildDayQoq(ctx context.Context, req *pb.GetGuildDayQoqReq) (*pb.GetGuildDayQoqResp, error) {
	return s.mgr.GetGuildDayQoq(ctx, req)
}

func (s *GoldCommissionServer) GetIncomeTrendList(ctx context.Context, req *pb.GetIncomeTrendListReq) (*pb.GetIncomeTrendListRsp, error) {
	return s.mgr.GetIncomeTrendList(ctx, req)
}

func (s *GoldCommissionServer) GetConsumeRank(ctx context.Context, req *pb.GetConsumeRankReq) (*pb.GetConsumeRankRsp, error) {
	return s.mgr.GetConsumeRank(ctx, req)
}

func (s *GoldCommissionServer) GetAmuseChannelDetail(ctx context.Context, req *pb.GetAmuseChannelDetailReq) (*pb.GetAmuseChannelDetailRsp, error) {
	return s.mgr.GetAmuseChannelDetail(ctx, req)
}

func (s *GoldCommissionServer) GetAmuseIncomeDetail(ctx context.Context, req *pb.GetAmuseIncomeDetailReq) (*pb.GetAmuseIncomeDetailRsp, error) {
	return s.mgr.GetAmuseIncomeDetail(ctx, req)
}

func (s *GoldCommissionServer) GetAmuseGuildRoomIncomeList(ctx context.Context, req *pb.GetAmuseGuildRoomIncomeListReq) (*pb.GetAmuseGuildRoomIncomeListRsp, error) {
	return s.mgr.GetAmuseGuildRoomIncomeList(ctx, req)
}

func (s *GoldCommissionServer) GetAmuseGuildDayIncomeList(ctx context.Context, req *pb.GetGuildDayIncomeListReq) (*pb.GetAmuseGuildDayIncomeListResp, error) {
	return s.mgr.GetAmuseGuildDayIncomeList(ctx, req)
}

func (s *GoldCommissionServer) GetAmuseGuildMonthIncomeList(ctx context.Context, req *pb.GetGuildMonthIncomeListReq) (*pb.GetGuildMonthIncomeListResp, error) {
	return s.mgr.GetAmuseGuildMonthIncomeList(ctx, req)
}

func (s *GoldCommissionServer) GetAmuseGoldPresentOrderCount(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	return s.mgr.GetAmuseGoldPresentOrderCount(ctx, req)
}

func (s *GoldCommissionServer) GetAmuseGoldPresentOrderList(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	return s.mgr.GetAmuseGoldPresentOrderList(ctx, req)
}

func (s *GoldCommissionServer) ReplaceAmuseGoldPresentOrder(ctx context.Context, req *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error) {
	return s.mgr.ReplaceAmuseGoldPresentOrder(ctx, req)
}

func (s *GoldCommissionServer) GetAmuseGoldWerewolfOrderCount(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	return s.mgr.GetAmuseGoldWerewolfOrderCount(ctx, req)
}

func (s *GoldCommissionServer) GetAmuseGoldWerewolfOrderList(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	return s.mgr.GetAmuseGoldWerewolfOrderList(ctx, req)
}

func (s *GoldCommissionServer) ReplaceAmuseGoldWerewolfOrder(ctx context.Context, req *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error) {
	return s.mgr.ReplaceAmuseGoldWerewolfOrder(ctx, req)
}

func (s *GoldCommissionServer) AmuseGuildChannelIncomeList(ctx context.Context, req *pb.AmuseGuildChannelIncomeListReq) (*pb.AmuseGuildChannelIncomeListResp, error) {
	return s.mgr.AmuseGuildChannelIncomeList(ctx, req)
}

func (s *GoldCommissionServer) GetAmuseRoomDayQoqInfo(ctx context.Context, req *pb.GetAmuseRoomDayQoqInfoReq) (*pb.GetAmuseRoomDayQoqInfoRsp, error) {
	return s.mgr.GetAmuseRoomDayQoqInfo(ctx, req)
}

func (s *GoldCommissionServer) GetAmuseExtraIncomeSettleList(ctx context.Context, req *pb.GetAmuseExtraIncomeSettleListReq) (*pb.GetAmuseExtraIncomeSettleListResp, error) {
	return s.mgr.GetAmuseExtraIncomeSettleList(ctx, req)
}

func (s *GoldCommissionServer) GetAmuseSettleGuildList(ctx context.Context, req *pb.GetAmuseSettleGuildListReq) (*pb.GetAmuseSettleGuildListResp, error) {
	return s.mgr.GetAmuseSettleGuildList(ctx, req)
}

func (s *GoldCommissionServer) GetAmuseSettleChannelList(ctx context.Context, req *pb.GetAmuseSettleChannelListReq) (*pb.GetAmuseSettleChannelListResp, error) {
	return s.mgr.GetAmuseSettleChannelList(ctx, req)
}

func (s *GoldCommissionServer) SetAmuseSettleGuild(ctx context.Context, req *pb.SetAmuseSettleGuildReq) (*pb.SetAmuseSettleGuildResp, error) {
	return s.mgr.SetAmuseSettleGuild(ctx, req)
}

func (s *GoldCommissionServer) SetAmuseSettleChannel(ctx context.Context, req *pb.SetAmuseSettleChannelReq) (*pb.SetAmuseSettleChannelResp, error) {
	return s.mgr.SetAmuseSettleChannel(ctx, req)
}

func (s *GoldCommissionServer) GetAmuseGuildIdsByRange(ctx context.Context, req *pb.GetAmuseGuildIdsByRangeReq) (*pb.GetAmuseGuildIdsByRangeResp, error) {
	return s.mgr.GetAmuseGuildIdsByRange(ctx, req)
}

func (s *GoldCommissionServer) GetAmuseExtraDetail(ctx context.Context, req *pb.GetAmuseExtraDetailReq) (*pb.GetAmuseExtraDetailResp, error) {
	return s.mgr.GetAmuseExtraDetail(ctx, req)
}

func (s *GoldCommissionServer) GenYuyinExtraReport(ctx context.Context, req *pb.GenYuyinExtraReportReq) (*pb.GenYuyinExtraReportResp, error) {
	now := time.Now()
	s.mgr.GenerateYuyinExtraIncome(ctx, now, req.GetIsDaily())
	return &pb.GenYuyinExtraReportResp{}, nil
}

func NewGoldCommissionServer(ctx context.Context, cfg config.Configer) (*GoldCommissionServer, error) {

	sc := &conf.ServiceConfigT{}

	cfgPath := ctx.Value("configfile").(string)
	if cfgPath == "" {
		return nil, errors.New("configfile not exist")
	}
	err := sc.Parse(cfgPath)
	if err != nil {
		return nil, err
	}

	// 开启动态配置
	if err = conf.Setup(); err != nil {
		return nil, fmt.Errorf("dynamic config start failed, err:[%+v]", err)
	}

	mgr, err := manager.NewManager(sc)
	if err != nil {
		return nil, err
	}

	s := &GoldCommissionServer{
		sc:  sc,
		mgr: mgr,
	}

	if sc.EnableKafka() {
		s.kafkaKnightSub, err = event.NewKnightEventLinkSub(ctx, sc.GetKafkaKnightConfig(), mgr.HandleKnightEvent)
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to NewKnightEventLinkSub err", err)
			return nil, err
		}

		s.kafkaPresentSub, err = event.NewPresentEventLinkSub(ctx, sc.GetKafkaPresentConfig(), mgr.HandlePresentEvent)
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to NewPresentEventLinkSub err", err)
			return nil, err
		}

		s.kafkaTBeanSub, err = event.NewTBeanEventLinkSub(ctx, sc.GetKafkaTBeanConfig(), mgr.HandleTBeanEvent)
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to NewTBeanEventLinkSub err", err)
			return nil, err
		}

		s.kafkaESportSub, err = event.NewESportEventLinkSub(ctx, sc.GetKafkaESportConfig(), mgr.HandleESportEvent)
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to NewESportEventLinkSub err", err)
			return nil, err
		}
	}

	return s, nil
}

func (s *GoldCommissionServer) ShutDown() {
	s.kafkaKnightSub.Close()
	s.kafkaPresentSub.Close()
	s.kafkaTBeanSub.Close()
	s.kafkaESportSub.Close()
}
