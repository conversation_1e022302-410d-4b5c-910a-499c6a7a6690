package common

import (
	"errors"
	pb "golang.52tt.com/protocol/services/numeric-go"
	"time"
)

// SwitchOp 财富值开关状态
type SwitchOp uint8

const (
	SwitchOpEnable  SwitchOp = 1
	SwitchOpDisable SwitchOp = 2
)

// GeneralNumeric 礼物事件财富值变更
type GeneralNumeric struct {
	Sender     uint32     // require uid
	Receiver   uint32     // require
	OrderId    string     // require
	AddRich    uint64     // require
	AddCharm   uint64     // require
	SendTime   time.Time  // require
	SourceType pb.SourceT // require

	BeforeRich         uint64
	BeforeCharm        uint64
	AfterRich          uint64
	AfterCharm         uint64
	IsRecordRich       bool
	IsReconcile        bool // 来自对账补单
	IsRichLevelChange  bool
	IsCharmLevelChange bool

	GiftId          uint32
	ChannelId       uint32
	ChannelGuildId  uint32
	ReceiverGuildId uint32
	UserGuildId     uint32
	PriceType       uint32
}

func (g *GeneralNumeric) Validate() error {
	if g == nil {
		return errors.New("numeric meta data is nil")
	}
	// check required
	if (g.Sender == 0 && g.Receiver == 0) || g.OrderId == "" || g.SendTime.IsZero() || g.SourceType == 0 {
		return errors.New("sender, receiver, order_id, send_time are required")
	}
	if g.SourceType == 0 {
		return errors.New("source_type is required")
	}
	if g.AddRich == 0 && g.AddCharm == 0 {
		return errors.New("add_rich or add_charm is required")
	}
	if g.AddRich > 0 && g.Sender == 0 {
		return errors.New("sender is required if add_rich > 0")
	}
	if g.AddCharm > 0 && g.Receiver == 0 {
		return errors.New("receiver is required if add_charm > 0")
	}
	return nil
}
