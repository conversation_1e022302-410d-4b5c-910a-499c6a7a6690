package cache

import (
	"context"
	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/log"
	"strconv"
)

const UserRichSwitchKey = "user_rich_switch_enable"

func (c *NumericGoCache) GetRichSwitch(ctx context.Context, uid uint32) (bool, error) {
	ret := c.redisClient.WithContext(ctx).SIsMember(UserRichSwitchKey, strconv.Itoa(int(uid)))
	if ret.Err() != nil {
		if ret.Err() != redis.Nil {
			log.Errorf("GetRichSwitch SIsMember err %s", ret.Err())
			return false, ret.Err()
		}
		return false, nil
	}
	return ret.Val(), nil
}

func (c *NumericGoCache) RichSwitchEnable(ctx context.Context, uid uint32) error {
	ret := c.redisClient.WithContext(ctx).SAdd(UserRichSwitchKey, strconv.Itoa(int(uid)))
	if ret.Err() != nil {
		log.Errorf("RichSwitchEnable SAdd err %s", ret.Err())
		return ret.Err()
	}
	return nil
}

func (c *NumericGoCache) RichSwitchDisable(ctx context.Context, uid uint32) error {
	ret := c.redisClient.WithContext(ctx).SRem(UserRichSwitchKey, strconv.Itoa(int(uid)))
	if ret.Err() != nil {
		log.Errorf("RichSwitchEnable SAdd err %s", ret.Err())
		return ret.Err()
	}
	return nil
}
