package cache

import (
	context "context"
	time "time"

	redis "github.com/go-redis/redis"
	pb "golang.52tt.com/protocol/services/numeric-go"
)

type INumericGoCache interface {
	BatchGetPersonalNumeric(ctx context.Context, userList []uint32) ([]*pb.PersonalNumeric, []uint32, error)
	BatchRemovePersonalNumeric(ctx context.Context, uidList []uint32) error
	BatchSetPersonalNumeric(ctx context.Context, userNumericList []*pb.PersonalNumeric) error
	ConsumerAsyncTask() (string, error)
	DelPersonalNumeric(ctx context.Context, uid uint32) error
	GetDailyLimit(ctx context.Context, t time.Time, tp pb.NumericT, uid uint32) (uint64, error)
	GetGiftEventLock(ctx context.Context, sender, receiver uint32) (bool, error)
	GetPersonalNumeric(ctx context.Context, uid uint32) (*pb.PersonalNumeric, bool, error)
	GetRichSwitch(ctx context.Context, uid uint32) (bool, error)
	IncrDailyLimit(ctx context.Context, t time.Time, tp pb.NumericT, uid uint32, value uint64) error
	IncrUserCharmRank(ctx context.Context, t time.Time, uid uint32, addCharm uint64) error
	IncrUserRichRank(ctx context.Context, t time.Time, uid uint32, addRich uint64) error
	IncrOrderID(ctx context.Context, orderId string) (int64, error)
	ReleaseGiftEventLock(ctx context.Context, sender, receiver uint32)
	RichSwitchDisable(ctx context.Context, uid uint32) error
	RichSwitchEnable(ctx context.Context, uid uint32) error
	SetDailyLimit(ctx context.Context, t time.Time, tp pb.NumericT, uid uint32, value uint64) error
	SetPersonalCharm(ctx context.Context, uid uint32, charm uint64) error
	SetPersonalNumeric(ctx context.Context, userNumeric *pb.PersonalNumeric) error
	SetPersonalRich(ctx context.Context, uid uint32, rich uint64) error
	UpdateCharmMonthRankList(ctx context.Context, t time.Time, uid uint32, charm uint64) error
	UpdateRichMonthRankList(ctx context.Context, t time.Time, uid uint32, rich uint64) error
	ZRevRangeWithScoresLimit(key string, start, limit int64) *redis.ZSliceCmd
	LockUid(ctx context.Context, uid uint32) (bool, error)
	UnlockUid(ctx context.Context, uid uint32) error
	GetRankChangedUidSet(ctx context.Context) ([]uint32, []uint32)
}
