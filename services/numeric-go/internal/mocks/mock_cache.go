// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/numeric-go/internal/cache (interfaces: INumericGoCache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	redis "github.com/go-redis/redis"
	gomock "github.com/golang/mock/gomock"
	numeric_go "golang.52tt.com/protocol/services/numeric-go"
)

// MockINumericGoCache is a mock of INumericGoCache interface.
type MockINumericGoCache struct {
	ctrl     *gomock.Controller
	recorder *MockINumericGoCacheMockRecorder
}

// MockINumericGoCacheMockRecorder is the mock recorder for MockINumericGoCache.
type MockINumericGoCacheMockRecorder struct {
	mock *MockINumericGoCache
}

// NewMockINumericGoCache creates a new mock instance.
func NewMockINumericGoCache(ctrl *gomock.Controller) *MockINumericGoCache {
	mock := &MockINumericGoCache{ctrl: ctrl}
	mock.recorder = &MockINumericGoCacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockINumericGoCache) EXPECT() *MockINumericGoCacheMockRecorder {
	return m.recorder
}

// BatchGetPersonalNumeric mocks base method.
func (m *MockINumericGoCache) BatchGetPersonalNumeric(arg0 context.Context, arg1 []uint32) ([]*numeric_go.PersonalNumeric, []uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPersonalNumeric", arg0, arg1)
	ret0, _ := ret[0].([]*numeric_go.PersonalNumeric)
	ret1, _ := ret[1].([]uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// BatchGetPersonalNumeric indicates an expected call of BatchGetPersonalNumeric.
func (mr *MockINumericGoCacheMockRecorder) BatchGetPersonalNumeric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPersonalNumeric", reflect.TypeOf((*MockINumericGoCache)(nil).BatchGetPersonalNumeric), arg0, arg1)
}

// BatchRemovePersonalNumeric mocks base method.
func (m *MockINumericGoCache) BatchRemovePersonalNumeric(arg0 context.Context, arg1 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchRemovePersonalNumeric", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchRemovePersonalNumeric indicates an expected call of BatchRemovePersonalNumeric.
func (mr *MockINumericGoCacheMockRecorder) BatchRemovePersonalNumeric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchRemovePersonalNumeric", reflect.TypeOf((*MockINumericGoCache)(nil).BatchRemovePersonalNumeric), arg0, arg1)
}

// BatchSetPersonalNumeric mocks base method.
func (m *MockINumericGoCache) BatchSetPersonalNumeric(arg0 context.Context, arg1 []*numeric_go.PersonalNumeric) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetPersonalNumeric", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchSetPersonalNumeric indicates an expected call of BatchSetPersonalNumeric.
func (mr *MockINumericGoCacheMockRecorder) BatchSetPersonalNumeric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetPersonalNumeric", reflect.TypeOf((*MockINumericGoCache)(nil).BatchSetPersonalNumeric), arg0, arg1)
}

// ConsumerAsyncTask mocks base method.
func (m *MockINumericGoCache) ConsumerAsyncTask() (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConsumerAsyncTask")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConsumerAsyncTask indicates an expected call of ConsumerAsyncTask.
func (mr *MockINumericGoCacheMockRecorder) ConsumerAsyncTask() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConsumerAsyncTask", reflect.TypeOf((*MockINumericGoCache)(nil).ConsumerAsyncTask))
}

// DelPersonalNumeric mocks base method.
func (m *MockINumericGoCache) DelPersonalNumeric(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelPersonalNumeric", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelPersonalNumeric indicates an expected call of DelPersonalNumeric.
func (mr *MockINumericGoCacheMockRecorder) DelPersonalNumeric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelPersonalNumeric", reflect.TypeOf((*MockINumericGoCache)(nil).DelPersonalNumeric), arg0, arg1)
}

// GetDailyLimit mocks base method.
func (m *MockINumericGoCache) GetDailyLimit(arg0 context.Context, arg1 time.Time, arg2 numeric_go.NumericT, arg3 uint32) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDailyLimit", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDailyLimit indicates an expected call of GetDailyLimit.
func (mr *MockINumericGoCacheMockRecorder) GetDailyLimit(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDailyLimit", reflect.TypeOf((*MockINumericGoCache)(nil).GetDailyLimit), arg0, arg1, arg2, arg3)
}

// GetGiftEventLock mocks base method.
func (m *MockINumericGoCache) GetGiftEventLock(arg0 context.Context, arg1, arg2 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGiftEventLock", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGiftEventLock indicates an expected call of GetGiftEventLock.
func (mr *MockINumericGoCacheMockRecorder) GetGiftEventLock(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGiftEventLock", reflect.TypeOf((*MockINumericGoCache)(nil).GetGiftEventLock), arg0, arg1, arg2)
}

// GetPersonalNumeric mocks base method.
func (m *MockINumericGoCache) GetPersonalNumeric(arg0 context.Context, arg1 uint32) (*numeric_go.PersonalNumeric, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersonalNumeric", arg0, arg1)
	ret0, _ := ret[0].(*numeric_go.PersonalNumeric)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetPersonalNumeric indicates an expected call of GetPersonalNumeric.
func (mr *MockINumericGoCacheMockRecorder) GetPersonalNumeric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalNumeric", reflect.TypeOf((*MockINumericGoCache)(nil).GetPersonalNumeric), arg0, arg1)
}

// GetRankChangedUidSet mocks base method.
func (m *MockINumericGoCache) GetRankChangedUidSet(arg0 context.Context) ([]uint32, []uint32) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRankChangedUidSet", arg0)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].([]uint32)
	return ret0, ret1
}

// GetRankChangedUidSet indicates an expected call of GetRankChangedUidSet.
func (mr *MockINumericGoCacheMockRecorder) GetRankChangedUidSet(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRankChangedUidSet", reflect.TypeOf((*MockINumericGoCache)(nil).GetRankChangedUidSet), arg0)
}

// GetRichSwitch mocks base method.
func (m *MockINumericGoCache) GetRichSwitch(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRichSwitch", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRichSwitch indicates an expected call of GetRichSwitch.
func (mr *MockINumericGoCacheMockRecorder) GetRichSwitch(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRichSwitch", reflect.TypeOf((*MockINumericGoCache)(nil).GetRichSwitch), arg0, arg1)
}

// IncrDailyLimit mocks base method.
func (m *MockINumericGoCache) IncrDailyLimit(arg0 context.Context, arg1 time.Time, arg2 numeric_go.NumericT, arg3 uint32, arg4 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrDailyLimit", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrDailyLimit indicates an expected call of IncrDailyLimit.
func (mr *MockINumericGoCacheMockRecorder) IncrDailyLimit(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrDailyLimit", reflect.TypeOf((*MockINumericGoCache)(nil).IncrDailyLimit), arg0, arg1, arg2, arg3, arg4)
}

// IncrOrderID mocks base method.
func (m *MockINumericGoCache) IncrOrderID(arg0 context.Context, arg1 string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrOrderID", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrOrderID indicates an expected call of IncrOrderID.
func (mr *MockINumericGoCacheMockRecorder) IncrOrderID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrOrderID", reflect.TypeOf((*MockINumericGoCache)(nil).IncrOrderID), arg0, arg1)
}

// IncrUserCharmRank mocks base method.
func (m *MockINumericGoCache) IncrUserCharmRank(arg0 context.Context, arg1 time.Time, arg2 uint32, arg3 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrUserCharmRank", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrUserCharmRank indicates an expected call of IncrUserCharmRank.
func (mr *MockINumericGoCacheMockRecorder) IncrUserCharmRank(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrUserCharmRank", reflect.TypeOf((*MockINumericGoCache)(nil).IncrUserCharmRank), arg0, arg1, arg2, arg3)
}

// IncrUserRichRank mocks base method.
func (m *MockINumericGoCache) IncrUserRichRank(arg0 context.Context, arg1 time.Time, arg2 uint32, arg3 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrUserRichRank", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrUserRichRank indicates an expected call of IncrUserRichRank.
func (mr *MockINumericGoCacheMockRecorder) IncrUserRichRank(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrUserRichRank", reflect.TypeOf((*MockINumericGoCache)(nil).IncrUserRichRank), arg0, arg1, arg2, arg3)
}

// LockUid mocks base method.
func (m *MockINumericGoCache) LockUid(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LockUid", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LockUid indicates an expected call of LockUid.
func (mr *MockINumericGoCacheMockRecorder) LockUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LockUid", reflect.TypeOf((*MockINumericGoCache)(nil).LockUid), arg0, arg1)
}

// ReleaseGiftEventLock mocks base method.
func (m *MockINumericGoCache) ReleaseGiftEventLock(arg0 context.Context, arg1, arg2 uint32) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ReleaseGiftEventLock", arg0, arg1, arg2)
}

// ReleaseGiftEventLock indicates an expected call of ReleaseGiftEventLock.
func (mr *MockINumericGoCacheMockRecorder) ReleaseGiftEventLock(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseGiftEventLock", reflect.TypeOf((*MockINumericGoCache)(nil).ReleaseGiftEventLock), arg0, arg1, arg2)
}

// RichSwitchDisable mocks base method.
func (m *MockINumericGoCache) RichSwitchDisable(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RichSwitchDisable", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RichSwitchDisable indicates an expected call of RichSwitchDisable.
func (mr *MockINumericGoCacheMockRecorder) RichSwitchDisable(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RichSwitchDisable", reflect.TypeOf((*MockINumericGoCache)(nil).RichSwitchDisable), arg0, arg1)
}

// RichSwitchEnable mocks base method.
func (m *MockINumericGoCache) RichSwitchEnable(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RichSwitchEnable", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RichSwitchEnable indicates an expected call of RichSwitchEnable.
func (mr *MockINumericGoCacheMockRecorder) RichSwitchEnable(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RichSwitchEnable", reflect.TypeOf((*MockINumericGoCache)(nil).RichSwitchEnable), arg0, arg1)
}

// SetDailyLimit mocks base method.
func (m *MockINumericGoCache) SetDailyLimit(arg0 context.Context, arg1 time.Time, arg2 numeric_go.NumericT, arg3 uint32, arg4 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDailyLimit", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDailyLimit indicates an expected call of SetDailyLimit.
func (mr *MockINumericGoCacheMockRecorder) SetDailyLimit(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDailyLimit", reflect.TypeOf((*MockINumericGoCache)(nil).SetDailyLimit), arg0, arg1, arg2, arg3, arg4)
}

// SetPersonalCharm mocks base method.
func (m *MockINumericGoCache) SetPersonalCharm(arg0 context.Context, arg1 uint32, arg2 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPersonalCharm", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPersonalCharm indicates an expected call of SetPersonalCharm.
func (mr *MockINumericGoCacheMockRecorder) SetPersonalCharm(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPersonalCharm", reflect.TypeOf((*MockINumericGoCache)(nil).SetPersonalCharm), arg0, arg1, arg2)
}

// SetPersonalNumeric mocks base method.
func (m *MockINumericGoCache) SetPersonalNumeric(arg0 context.Context, arg1 *numeric_go.PersonalNumeric) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPersonalNumeric", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPersonalNumeric indicates an expected call of SetPersonalNumeric.
func (mr *MockINumericGoCacheMockRecorder) SetPersonalNumeric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPersonalNumeric", reflect.TypeOf((*MockINumericGoCache)(nil).SetPersonalNumeric), arg0, arg1)
}

// SetPersonalRich mocks base method.
func (m *MockINumericGoCache) SetPersonalRich(arg0 context.Context, arg1 uint32, arg2 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPersonalRich", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPersonalRich indicates an expected call of SetPersonalRich.
func (mr *MockINumericGoCacheMockRecorder) SetPersonalRich(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPersonalRich", reflect.TypeOf((*MockINumericGoCache)(nil).SetPersonalRich), arg0, arg1, arg2)
}

// UnlockUid mocks base method.
func (m *MockINumericGoCache) UnlockUid(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnlockUid", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnlockUid indicates an expected call of UnlockUid.
func (mr *MockINumericGoCacheMockRecorder) UnlockUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnlockUid", reflect.TypeOf((*MockINumericGoCache)(nil).UnlockUid), arg0, arg1)
}

// UpdateCharmMonthRankList mocks base method.
func (m *MockINumericGoCache) UpdateCharmMonthRankList(arg0 context.Context, arg1 time.Time, arg2 uint32, arg3 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCharmMonthRankList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateCharmMonthRankList indicates an expected call of UpdateCharmMonthRankList.
func (mr *MockINumericGoCacheMockRecorder) UpdateCharmMonthRankList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCharmMonthRankList", reflect.TypeOf((*MockINumericGoCache)(nil).UpdateCharmMonthRankList), arg0, arg1, arg2, arg3)
}

// UpdateRichMonthRankList mocks base method.
func (m *MockINumericGoCache) UpdateRichMonthRankList(arg0 context.Context, arg1 time.Time, arg2 uint32, arg3 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRichMonthRankList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRichMonthRankList indicates an expected call of UpdateRichMonthRankList.
func (mr *MockINumericGoCacheMockRecorder) UpdateRichMonthRankList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRichMonthRankList", reflect.TypeOf((*MockINumericGoCache)(nil).UpdateRichMonthRankList), arg0, arg1, arg2, arg3)
}

// ZRevRangeWithScoresLimit mocks base method.
func (m *MockINumericGoCache) ZRevRangeWithScoresLimit(arg0 string, arg1, arg2 int64) *redis.ZSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRevRangeWithScoresLimit", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.ZSliceCmd)
	return ret0
}

// ZRevRangeWithScoresLimit indicates an expected call of ZRevRangeWithScoresLimit.
func (mr *MockINumericGoCacheMockRecorder) ZRevRangeWithScoresLimit(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRevRangeWithScoresLimit", reflect.TypeOf((*MockINumericGoCache)(nil).ZRevRangeWithScoresLimit), arg0, arg1, arg2)
}
