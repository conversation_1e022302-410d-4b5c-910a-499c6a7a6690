package mysql

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"golang.52tt.com/services/numeric-go/internal/common"

	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/numeric-go"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var NumericNotFoundErr = errors.New("user numeric not found")

// PersonalRichAndCharm 用户财富值魅力值表
type PersonalRichAndCharm struct {
	Uid   uint32 `db:"uid" json:"uid"`     // uid
	Rich  uint64 `db:"rich" json:"rich"`   // 财富值
	Charm uint64 `db:"charm" json:"charm"` // 魅力值
}

func getPersonalRichAndCharmTbl(uid uint32) string {
	return fmt.Sprintf("personal_rich_and_charm_%02d", getTableIndex(uid))
}

func getTableIndex(guildId uint32) uint32 {
	return guildId % 100
}

// PersonalRichCharmLog 财富值魅力值变更记录表
type PersonalRichCharmLog struct {
	Id          uint32      `db:"id" json:"id"`
	Uid         uint32      `db:"uid" json:"uid"`                                // uid
	Type        pb.NumericT `db:"type" json:"type"`                              // 类型 1财富 2魅力
	BeforeValue uint64      `db:"before_value" json:"before_value"`              // 处理之前的值
	AddValue    int64       `db:"add_value" json:"add_value"`                    // 新增的值
	CreateTime  time.Time   `db:"autoCreateTime;create_time" json:"create_time"` // 时间
	OrderID     string      `db:"order_id" json:"order_id"`                      // 订单号
	SourceType  pb.SourceT  `db:"source_type" json:"source_type"`                // 来源类型 see SourceT
}

func getPersonalRichCharmLogTbl(t time.Time) string {
	return fmt.Sprintf("personal_rich_charm_log_%s", t.Format("200601"))
}

// PersonRichVip  用户VIP等级
type PersonRichVip struct {
	Uid        uint32    `gorm:"primaryKey;column:uid" json:"uid"`
	VipLevel   uint32    `gorm:"column:vip_level" json:"vip_level"`
	CreateTime time.Time `gorm:"autoUpdateTime;column:create_time" json:"create_time"` //  创建时间
	UpdateTime time.Time `gorm:"autoUpdateTime;column:update_time" json:"update_time"` //  更新时间
}

func (PersonRichVip) TableName() string {
	return "person_rich_vip"
}

type GuildRichAndCharmRank struct {
	GuildId uint32 `db:"guild_id" json:"guild_id"` // guild_id
	Uid     uint32 `db:"uid" json:"uid"`           // uid
	Rich    uint64 `db:"rich" json:"rich"`         // 财富值
	Charm   uint64 `db:"charm" json:"charm"`       // 魅力值
	Status  int8   `db:"status" json:"status"`     // 0:normal  1:invalid
}

func getGuildRichAndCharmRankTbl(GuildId uint32) string {
	return fmt.Sprintf("guild_rich_and_charm_rank_%02d", getTableIndex(GuildId))
}

type GuildRich struct {
	GuildId uint32 `db:"guild_id" json:"guild_id"` // guild_id
	Rich    uint64 `db:"rich" json:"rich"`         // 财富总值
}

func getGuildRichTbl(GuildId uint32) string {
	return fmt.Sprintf("guild_rich_%02d", getTableIndex(GuildId))
}

type PersonalRichAndCharmMonthly struct {
	Uid   uint32 `db:"uid" json:"uid" gorm:"primary_key"` // uid
	Rich  uint64 `db:"rich" json:"rich" gorm:"index:rich_idx"`
	Charm uint64 `db:"charm" json:"charm" gorm:"index:charm_idx"`
}

func getPersonalRichAndCharmMonthlyTbl(t time.Time) string {
	return fmt.Sprintf("personal_rich_and_charm_monthly_%s", t.Format("200601"))
}

type RichCardOrder struct {
	ID         uint64    `gorm:"column:id;primaryKey" json:"id"`
	UID        uint32    `gorm:"column:uid" json:"uid"`
	ChannelID  uint32    `gorm:"column:channel_id" json:"channel_id"`
	OrderID    string    `gorm:"column:order_id" json:"order_id"`
	AddRich    uint64    `gorm:"column:add_rich" json:"add_rich"`
	CardID     uint32    `gorm:"column:card_id" json:"card_id"`
	UseCount   uint32    `gorm:"column:use_count" json:"use_count"`
	CardName   string    `gorm:"column:card_name" json:"card_name"`
	Status     bool      `gorm:"column:status" json:"status"`
	CreateTime time.Time `gorm:"column:create_time;autoCreateTime" json:"create_time"`
	UpdateTime time.Time `gorm:"column:update_time;autoUpdateTime" json:"update_time"`
}

func (r RichCardOrder) TableName() string {
	return "rich_card_order"
}

func (r RichCardOrder) IsFinished() bool {
	return r.Status
}

func (s *Store) GetPersonalNumeric(ctx context.Context, uid uint32) (*pb.PersonalNumeric, error) {
	if uid == 0 {
		return &pb.PersonalNumeric{}, nil
	}
	userNumeric := new(PersonalRichAndCharm)
	if err := s.db.Table(getPersonalRichAndCharmTbl(uid)).
		Select("charm, rich").
		Where("uid = ?", uid).
		Find(&userNumeric).Error; err != nil {
		if isNotFound(err) {
			return &pb.PersonalNumeric{Uid: uid}, nil
		}
		log.ErrorWithCtx(ctx, "GetPersonalNumeric get person numeric err: %+v, uid: %d", err, uid)
		return nil, err
	}
	return &pb.PersonalNumeric{
		Uid:   uid,
		Rich:  userNumeric.Rich,
		Charm: userNumeric.Charm,
	}, nil
}

func (s *Store) GetGetPersonalNumericWithError(ctx context.Context, uid uint32) (*pb.PersonalNumeric, error) {
	userNumeric := new(PersonalRichAndCharm)
	if err := s.db.Table(getPersonalRichAndCharmTbl(uid)).
		Select("charm, rich").
		Where("uid = ?", uid).
		Find(&userNumeric).Error; err != nil {
		if isNotFound(err) {
			return nil, NumericNotFoundErr
		}
		log.ErrorWithCtx(ctx, "GetGetPersonalNumericWithError get person numeric err: %+v, uid: %d", err, uid)
		return nil, err
	}
	return &pb.PersonalNumeric{
		Uid:   uid,
		Rich:  userNumeric.Rich,
		Charm: userNumeric.Charm,
	}, nil
}

func (s *Store) BatchGetPersonalNumeric(ctx context.Context, uidList []uint32) ([]*pb.PersonalNumeric, error) {
	list := make([]*pb.PersonalNumeric, 0, len(uidList))
	if len(uidList) == 0 {
		return list, nil
	}
	// 取模 分批
	batches := make(map[int][]uint32)
	for _, uid := range uidList {
		index := int(getTableIndex(uid))
		batches[index] = append(batches[index], uid)
	}

	lock := sync.Mutex{}
	g := new(errgroup.Group)
	for _, batch := range batches {
		us := batch
		g.Go(func() error {
			records := make([]*PersonalRichAndCharm, 0, len(us))
			if err := s.db.Table(getPersonalRichAndCharmTbl(us[0])).
				Select("uid, charm, rich").
				Where("uid in (?)", us).
				Find(&records).Error; err != nil {
				if isNotFound(err) {
					log.ErrorWithCtx(ctx, "BatchGetPersonalNumeric Find not found err: %s, uid: %+v", err, us)
					return nil
				}
				log.ErrorWithCtx(ctx, "BatchGetPersonalNumeric Find: %s, uid: %+v", err, us)
				return err
			}
			batchList := make([]*pb.PersonalNumeric, 0, len(records))
			for _, r := range records {
				batchList = append(batchList, &pb.PersonalNumeric{
					Uid:   r.Uid,
					Rich:  r.Rich,
					Charm: r.Charm,
				})
			}
			lock.Lock()
			list = append(list, batchList...)
			lock.Unlock()
			return nil
		})
	}
	err := g.Wait()
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetPersonalNumeric get person numeric err: %+v, uidList: %v", err, uidList)
		return nil, err
	}
	return list, nil
}

func (s *Store) RecordPersonRichCharm(ctx context.Context, uid uint32, rich, charm uint64) error {
	return s.Transaction(ctx, func(tx *gorm.DB) error {
		if err := s.RecordPersonRich(tx, uid, rich); err != nil {
			return err
		}
		if err := s.RecordPersonCharm(tx, uid, charm); err != nil {
			return err
		}
		return nil
	})
}

// RecordPersonRich 增加个人财富值
func (s *Store) RecordPersonRich(tx *gorm.DB, uid uint32, rich uint64) error {
	if rich == 0 {
		return nil
	}
	if tx == nil {
		tx = s.db
	}
	querySql := fmt.Sprintf(`INSERT INTO %s (uid, rich, charm) VALUES(?, ?, 0) ON DUPLICATE KEY UPDATE rich = rich + VALUES(rich)`,
		getPersonalRichAndCharmTbl(uid))
	params := []interface{}{uid, rich}
	if err := tx.Exec(querySql, params...).Error; err != nil {
		log.Errorf("RecordPersonRich Exec err:%s, sql:%s, params:%v", err, querySql, params)
		return err
	}
	return nil
}

// RecordPersonCharm 增加个人魅力值
func (s *Store) RecordPersonCharm(tx *gorm.DB, uid uint32, charm uint64) error {
	if charm == 0 {
		return nil
	}
	if tx == nil {
		tx = s.db
	}
	querySql := fmt.Sprintf(`INSERT INTO %s (uid, rich, charm) VALUES(?, 0, ?) ON DUPLICATE KEY UPDATE charm = charm + VALUES(charm)`,
		getPersonalRichAndCharmTbl(uid))
	params := []interface{}{uid, charm}
	if err := tx.Exec(querySql, params...).Error; err != nil {
		log.Errorf("RecordPersonCharm Exec err:%s, sql:%s, params:%v", err, querySql, params)
		return err
	}
	return nil
}

// AddPersonRichOrCharmChangeLog 变更记录
func (s *Store) AddPersonRichOrCharmChangeLog(ctx context.Context, t time.Time, numericChangeLogs []*PersonalRichCharmLog) error {
	if len(numericChangeLogs) == 0 {
		return nil
	}
	if err := s.db.Table(getPersonalRichCharmLogTbl(t)).CreateInBatches(numericChangeLogs, 100).Error; err != nil {
		log.ErrorWithCtx(ctx, "AddPersonRichOrCharmChangeLog CreateInBatches err:%s", err.Error())
		return err
	}
	return nil
}

func (s *Store) RecordPersonVip(ctx context.Context, uid, level uint32) error {
	if uid == 0 {
		return nil
	}
	if err := s.db.Model(&PersonRichVip{}).
		Clauses(clause.OnConflict{
			DoUpdates: clause.Assignments(map[string]interface{}{
				"vip_level": level,
			}),
		}).
		Create(&PersonRichVip{Uid: uid, VipLevel: level}).Error; err != nil {
		if isDuplicate(err) {
			return nil
		}
		log.ErrorWithCtx(ctx, "RecordPersonVip Create err:%s", err.Error())
		return err
	}
	return nil
}

func (s *Store) GetPersonVip(ctx context.Context, uid uint32) (*PersonRichVip, error) {
	vip := new(PersonRichVip)
	if uid == 0 {
		return vip, nil
	}
	if err := s.db.Model(&PersonRichVip{}).
		Where(&PersonRichVip{Uid: uid}).
		First(&vip).Error; err != nil {
		if isNotFound(err) {
			return vip, nil
		}
		log.ErrorWithCtx(ctx, "GetPersonVip First err:%s", err.Error())
		return nil, err
	}
	return vip, nil
}

func (s *Store) CreateSwitchOpLog(ctx context.Context, uid, guildId uint32, enable bool, changeType pb.RichSwitchChangeType) error {
	var op common.SwitchOp
	if enable {
		op = common.SwitchOpEnable
	} else {
		op = common.SwitchOpDisable
	}
	if err := s.db.Create(&PersonalRichSwitchLog{
		UID:        uid,
		GuildID:    guildId,
		Op:         op,
		ChangeType: changeType,
		Type:       1,
	}).Error; err != nil {
		log.ErrorWithCtx(ctx, "CreateSwitchOpLog Create err:%v", err)
		return err
	}
	return nil
}

func (s *Store) CreateSwitchOpLogV2(ctx context.Context, record *PersonalRichSwitchLog) error {
	record.Type = 1
	if err := s.db.Model(&PersonalRichSwitchLog{}).Create(record).Error; err != nil {
		log.ErrorWithCtx(ctx, "CreateSwitchOpLogV2 Create err:%v", err)
		return err
	}
	return nil
}

func (s *Store) GetLastSwitchOpLog(ctx context.Context, uid uint32) (*PersonalRichSwitchLog, error) {
	record := new(PersonalRichSwitchLog)
	if err := s.db.Model(&PersonalRichSwitchLog{}).
		Where(&PersonalRichSwitchLog{UID: uid}).
		Order("create_time DESC").
		First(&record).Error; err != nil {
		if isNotFound(err) {
			return record, nil
		}
		log.ErrorWithCtx(ctx, "GetLastSwitchOpLog First err:%s", err.Error())
		return nil, err
	}
	return record, nil
}

// RecordGuildGiftRich 增加房间公会的礼物总消费记录
func (s *Store) RecordGuildGiftRich(tx *gorm.DB, guildId uint32, rich uint64) error {
	if guildId == 0 || rich == 0 {
		return nil
	}
	if tx == nil {
		tx = s.db
	}
	querySql := fmt.Sprintf(`INSERT INTO %s (guild_id, rich) VALUES(?, ?) ON DUPLICATE KEY UPDATE rich = rich + VALUES(rich)`,
		getGuildRichTbl(guildId))
	params := []interface{}{guildId, rich}
	if err := tx.Exec(querySql, params...).Error; err != nil {
		log.Errorf("RecordGuildGiftRich Exec err:%s, sql:%s, params:%v", err, querySql, params)
		return err
	}
	return nil
}

// RecordGuildCharm 增加个人公会的魅力榜的值
func (s *Store) RecordGuildCharm(tx *gorm.DB, guildId, uid uint32, charm uint64) error {
	if guildId == 0 || charm == 0 {
		return nil
	}
	if tx == nil {
		tx = s.db
	}
	querySql := fmt.Sprintf(`INSERT INTO %s (guild_id, uid, rich, charm, status) VALUES (?, ?, 0, ?, 0) 
ON DUPLICATE KEY UPDATE charm = charm + VALUES (charm)`,
		getGuildRichAndCharmRankTbl(guildId))
	params := []interface{}{guildId, uid, charm}
	if err := tx.Exec(querySql, params...).Error; err != nil {
		log.Errorf("RecordGuildCharm Exec err:%s, sql:%s, params:%v", err, querySql, params)
		return err
	}
	return nil
}

// orderUidBySize 按大小排序uid
func orderUidBySize(fromUser, toUser uint32) (firstUser, secondUser uint32) {
	if fromUser < toUser {
		return fromUser, toUser
	}
	return toUser, fromUser
}

// RecordGiftEventChange 记录T豆送礼事件财富值变更
func (s *Store) RecordGiftEventChange(ctx context.Context, n *common.GeneralNumeric) error {
	var err error

	numericChangeLogs := make([]*PersonalRichCharmLog, 0)
	// 开启锁定财富值也记录日志
	numericChangeLogs = append(numericChangeLogs, &PersonalRichCharmLog{
		Uid:         n.Sender,
		Type:        pb.NumericT_Rich,
		BeforeValue: n.BeforeRich,
		AddValue:    int64(n.AddRich),
		CreateTime:  n.SendTime,
		OrderID:     n.OrderId,
		SourceType:  n.SourceType,
	})
	numericChangeLogs = append(numericChangeLogs, &PersonalRichCharmLog{
		Uid:         n.Receiver,
		Type:        pb.NumericT_Charm,
		BeforeValue: n.BeforeCharm,
		AddValue:    int64(n.AddCharm),
		CreateTime:  n.SendTime,
		OrderID:     n.OrderId,
		SourceType:  n.SourceType,
	})

	firstUid, secondUid := orderUidBySize(n.Sender, n.Receiver)

	// 死锁重试兜底
	for i := 0; i < 3; i++ {
		if err = s.Transaction(ctx, func(tx *gorm.DB) error {
			// 固定顺序更新，避免死锁
			for _, uid := range []uint32{firstUid, secondUid} {
				if uid == n.Sender && n.IsRecordRich {
					if err := s.RecordPersonRich(tx, n.Sender, n.AddRich); err != nil {
						log.ErrorWithCtx(ctx, "RecordGiftEventChange RecordPersonRich err:%v", err)
						return err
					}
				}
				if uid == n.Receiver {
					if err := s.RecordPersonCharm(tx, n.Receiver, n.AddCharm); err != nil {
						log.ErrorWithCtx(ctx, "RecordGiftEventChange RecordPersonCharm err:%v", err)
						return err
					}
				}
			}

			// 记录流水
			if len(numericChangeLogs) > 0 {
				if err := tx.Table(getPersonalRichCharmLogTbl(n.SendTime)).CreateInBatches(numericChangeLogs, 100).Error; err != nil {
					log.ErrorWithCtx(ctx, "AddPersonRichOrCharmChangeLog CreateInBatches err:%s", err.Error())
					return err
				}
			}
			return nil
		}); err != nil {
			if isDeadlock(err) {
				log.ErrorWithCtx(ctx, "RecordGiftEventChange Transaction deadlock, retry after %dms, sender:%d, receiver:%d", 50*(i+1), n.Sender, n.Receiver)
				time.Sleep(time.Duration(50*(i+1)) * time.Millisecond)
				continue
			}
			log.ErrorWithCtx(ctx, "RecordGiftEventChange Transaction err:%v, sender:%d, receiver:%d", err, n.Sender, n.Receiver)
			return err
		}
		break
	}

	// 公会贡献处理，这个貌似不重要，不放在事务里
	{
		if n.ChannelId > 0 && n.ChannelGuildId > 0 {
			if n.ChannelGuildId == n.ReceiverGuildId {
				// 增加礼物接收者在自己公会的魅力榜的值
				if err = s.RecordGuildCharm(nil, n.ReceiverGuildId, n.Receiver, n.AddCharm); err != nil {
					log.Errorf("RecordGiftEventChange RecordGuildCharm err:%v", err)
				}
			}
		}
		// 增加房间公会的礼物总消费记录
		if err = s.RecordGuildGiftRich(nil, n.ChannelGuildId, n.AddRich); err != nil {
			log.Errorf("RecordGiftEventChange RecordGuildGiftRich err:%v", err)
		}
	}

	return nil
}

// RecordGiftEventRichChangeV2 记录红钻送礼财富值变更
func (s *Store) RecordGiftEventRichChangeV2(ctx context.Context, gn *common.GeneralNumeric) error {
	var err error
	// 该事务可能引起死锁，暂时移除
	tx := s.db.WithContext(ctx)

	if err = s.RecordPersonRich(tx, gn.Sender, gn.AddRich); err != nil {
		log.Errorf("RecordGiftEventRichChangeV2 RecordPersonRich err:%v, uid:%d, addRich:%d", err, gn.Sender, gn.AddRich)
		return err
	}

	if err = s.db.Table(getPersonalRichCharmLogTbl(gn.SendTime)).
		Create(&PersonalRichCharmLog{
			Uid:         gn.Sender,
			Type:        pb.NumericT_Rich,
			BeforeValue: gn.BeforeRich,
			AddValue:    int64(gn.AddRich),
			CreateTime:  gn.SendTime,
			OrderID:     gn.OrderId,
			SourceType:  gn.SourceType,
		}).Error; err != nil {
		log.ErrorWithCtx(ctx, "RecordGiftEventRichChangeV2 Create log err:%s, uid:%d, addRich:%d", err, gn.Sender, gn.AddRich)
		return err
	}

	if gn.ChannelId > 0 && gn.ChannelGuildId > 0 {
		if err = s.RecordGuildGiftRich(tx, gn.ChannelGuildId, gn.AddRich); err != nil {
			log.Errorf("RecordGiftEventRichChangeV2 RecordGuildGiftRich err:%s, uid:%d, addRich:%d", err, gn.Sender, gn.AddRich)
		}
	}

	return nil
}

// RecordGiftEventCharmChangeV2 记录红钻送礼魅力值变更
func (s *Store) RecordGiftEventCharmChangeV2(ctx context.Context, gn *common.GeneralNumeric) error {
	var err error
	// 该事务可能引起死锁，暂时移除
	tx := s.db.WithContext(ctx)

	if err = s.RecordPersonCharm(tx, gn.Receiver, gn.AddCharm); err != nil {
		log.Errorf("RecordGiftEventCharmChangeV2 RecordPersonCharm err:%s, uid:%d, addCharm:%d", err, gn.Receiver, gn.AddCharm)
	}
	if err = s.db.Table(getPersonalRichCharmLogTbl(gn.SendTime)).
		Create(&PersonalRichCharmLog{
			Uid:         gn.Receiver,
			Type:        pb.NumericT_Charm,
			BeforeValue: gn.BeforeCharm,
			AddValue:    int64(gn.AddCharm),
			CreateTime:  gn.SendTime,
			OrderID:     gn.OrderId,
			SourceType:  gn.SourceType,
		}).Error; err != nil {
		log.ErrorWithCtx(ctx, "RecordGiftEventCharmChangeV2 Create log err:%s, uid:%d, addCharm:%d", err, gn.Receiver, gn.AddCharm)
		return err
	}
	if gn.ChannelId > 0 && gn.ChannelGuildId > 0 && gn.ChannelGuildId == gn.ReceiverGuildId {
		if err = s.RecordGuildCharm(tx, gn.ChannelGuildId, gn.Receiver, gn.AddCharm); err != nil {
			log.Errorf("RecordGiftEventCharmChangeV2 RecordGuildCharm err:%s, uid:%d, addCharm:%d", err, gn.Receiver, gn.AddCharm)
		}
	}

	return nil
}

func (s *Store) PreUseRichCard(ctx context.Context, card *RichCardOrder) error {
	if card.UID == 0 || card.AddRich == 0 || card.OrderID == "" {
		return nil
	}
	return s.db.WithContext(ctx).Model(&RichCardOrder{}).Create(card).Error
}

func (s *Store) CertainUseRichCard(ctx context.Context, card *RichCardOrder, userNumeric *pb.PersonalNumeric) error {
	if card == nil || card.OrderID == "" {
		return nil
	}
	orderID := card.OrderID
	now := time.Now()
	if err := s.Transaction(ctx, func(tx *gorm.DB) (err error) {
		// record rich card order
		if err = tx.Model(&RichCardOrder{}).
			Where(OrderEqual, orderID).
			Update("status", true).Error; err != nil {
			log.Errorf("CertainUseRichCard Update err:%v", err)
			return err
		}

		// record numeric
		if err = tx.Table(getPersonalRichAndCharmTbl(card.UID)).
			Clauses(clause.OnConflict{
				DoUpdates: clause.Assignments(map[string]interface{}{
					"rich": gorm.Expr("rich + ?", card.AddRich),
				}),
			}).
			Create(&PersonalRichAndCharm{
				Uid:  card.UID,
				Rich: card.AddRich,
			}).Error; err != nil {
			log.ErrorWithCtx(ctx, "CertainUseRichCard Upsert err:%s", err)
			return err
		}

		// record numeric log
		if err = tx.Table(getPersonalRichCharmLogTbl(now)).Create(&PersonalRichCharmLog{
			Uid:         card.UID,
			Type:        pb.NumericT_Rich,
			BeforeValue: userNumeric.GetRich(),
			AddValue:    int64(card.AddRich),
			CreateTime:  now,
			OrderID:     orderID,
			SourceType:  pb.SourceT_SourceTRichCard,
		}).Error; err != nil {
			log.ErrorWithCtx(ctx, "CertainUseRichCard Create err:%s", err.Error())
			return err
		}

		return nil
	}); err != nil {
		log.Errorf("CertainUseRichCard Transaction err:%v", err)
		return err
	}
	return nil
}

func (s *Store) CancelUseRichCard(ctx context.Context, orderID string) error {
	if orderID == "" {
		return nil
	}
	if err := s.db.WithContext(ctx).Model(&RichCardOrder{}).
		Where(OrderEqual, orderID).
		Delete(&RichCardOrder{}).Error; err != nil {
		log.Errorf("CancelUseRichCard Delete err:%v", err)
		return err
	}
	return nil
}

func (s *Store) GetRichCardOrder(ctx context.Context, orderID string) (*RichCardOrder, error) {
	card := new(RichCardOrder)
	if err := s.db.WithContext(ctx).Model(&RichCardOrder{}).Where(OrderEqual, orderID).First(card).Error; err != nil {
		log.Errorf("CertainUseRichCard Get order err:%v", err)
		return nil, err
	}
	return card, nil
}

func (s *Store) GetLastSwitchOpLogByTime(ctx context.Context, uid uint32, t time.Time) (*PersonalRichSwitchLog, error) {
	record := new(PersonalRichSwitchLog)
	if err := s.db.Model(&PersonalRichSwitchLog{}).
		Where(&PersonalRichSwitchLog{UID: uid}).
		Where("create_time <= ?", t).
		Order("create_time DESC").
		First(&record).Error; err != nil {
		if isNotFound(err) {
			return record, nil
		}
		log.ErrorWithCtx(ctx, "GetLastRichSwitchOpLog First err:%s", err.Error())
		return nil, err
	}
	return record, nil
}
