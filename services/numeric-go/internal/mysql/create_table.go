package mysql

import (
	"context"
	"fmt"
	mysqlDriver "github.com/go-sql-driver/mysql"
	"golang.52tt.com/pkg/log"
	"time"
)

var CreateLogTableSql = `
CREATE TABLE %s
(
    id           int(10) unsigned     NOT NULL AUTO_INCREMENT,
    uid          int(10) unsigned     NOT NULL COMMENT 'uid',
    type         tinyint(10) unsigned NOT NULL DEFAULT '0' COMMENT '变更类型',
    before_value bigint(20)           NOT NULL DEFAULT '0' COMMENT '变更前的财富值',
    add_value    bigint(20)           NOT NULL DEFAULT '0' COMMENT '增加财富值',
    create_time  timestamp            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    order_id     varchar(64)          NOT NULL DEFAULT '' COMMENT '订单号',
    source_type  tinyint(4)           NOT NULL DEFAULT '0' COMMENT '来源',
    PRIMARY KEY (id),
    KEY idx_uid (uid),
    KEY idx_ct (create_time),
    KEY idx_order_id (order_id),
	KEY idx_source_type (source_type)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;
`

// CreateReconcileTableSql 财富值对账表
const CreateReconcileTableSql = `CREATE TABLE %s (
  id int(10) unsigned NOT NULL AUTO_INCREMENT,
  uid int(10) unsigned NOT NULL COMMENT 'uid',
  type tinyint(10) unsigned NOT NULL DEFAULT '0' COMMENT '变更类型',
  add_value bigint(20) NOT NULL DEFAULT '0' COMMENT '变更值',
  rich_switch tinyint(10) unsigned NOT NULL DEFAULT '0' COMMENT '财富值开关',
  order_id varchar(64) NOT NULL DEFAULT '' COMMENT '订单号',
  source_type tinyint(4) NOT NULL DEFAULT '0' COMMENT '来源',
  status tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态',
  order_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单时间',
  create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY idx_order_id (order_id),
  KEY idx_uid (uid),
  KEY idx_ct (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='财富值对账表'
`

func isTableExist(err error) bool {
	if mysqlErr, ok := err.(*mysqlDriver.MySQLError); ok {
		if mysqlErr.Number == 1050 {
			return true
		}
	}
	return false
}

func (s *Store) CreateLogTable(ctx context.Context) {

	log.InfoWithCtx(ctx, "CreateLogTable start")
	now := time.Now()
	// 本月初始时间
	thisMonthTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	// 下月初始时间
	nextMonthTime := time.Date(thisMonthTime.Year(), thisMonthTime.Month()+1, 1, 0, 0, 0, 0, now.Location())

	createTbl := func(t time.Time) {
		tblPrefix := "personal_rich_charm_log_%s"
		tbl := fmt.Sprintf(tblPrefix, t.Format("200601"))
		sqlGoldTableStr := fmt.Sprintf(CreateLogTableSql, tbl)
		err := s.db.Exec(sqlGoldTableStr).Error
		if err != nil {
			if isTableExist(err) {
				log.InfoWithCtx(ctx, fmt.Sprintf("CreateLogTable table existed：%s", tbl))
				//feishu.SendInfo(fmt.Sprintf("创建财富值月流水表已存在：%s", tbl))
			} else {
				errMsg := fmt.Sprintf("CreateLogTable %s table err: %s", tbl, err.Error())
				log.WarnWithCtx(ctx, errMsg)
				//feishu.SendError(fmt.Sprintf("创建财富值月流水表错误：%+v", errMsg))
			}
		}
	}

	createTbl(thisMonthTime)
	createTbl(nextMonthTime)

	createReconcileTbl := func(t time.Time) {
		tbl := getReconcileTbl(t)
		sqlGoldTableStr := fmt.Sprintf(CreateReconcileTableSql, tbl)
		err := s.db.Exec(sqlGoldTableStr).Error
		if err != nil {
			if isTableExist(err) {
				log.InfoWithCtx(ctx, fmt.Sprintf("createReconcileTbl table existed：%s", tbl))
				//feishu.SendInfo(fmt.Sprintf("创建财富值月流水表已存在：%s", tbl))
			} else {
				errMsg := fmt.Sprintf("createReconcileTbl %s table err: %s", tbl, err.Error())
				log.WarnWithCtx(ctx, errMsg)
				//feishu.SendError(fmt.Sprintf("创建财富值月流水表错误：%+v", errMsg))
			}
		}
	}

	createReconcileTbl(thisMonthTime)
	createReconcileTbl(nextMonthTime)

	log.InfoWithCtx(ctx, "CreateLogTable end")
}
