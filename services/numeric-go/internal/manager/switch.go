package manager

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkaanchorcontract"
	pb "golang.52tt.com/protocol/services/numeric-go"
	"golang.52tt.com/services/numeric-go/internal/common"
	"golang.52tt.com/services/numeric-go/internal/mysql"
	"google.golang.org/grpc/codes"
	"time"
)

// GetUserRichSwitch 获取签约用户财富值开关状态
func (m *Manager) GetUserRichSwitch(ctx context.Context, req *pb.GetUserRichSwitchReq) (*pb.GetUserRichSwitchResp, error) {
	resp := &pb.GetUserRichSwitchResp{}
	enable, err := m.cacheClient.GetRichSwitch(ctx, req.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetUserRichSwitch GetRichSwitch err: %+v", err)
		return resp, err
	}
	return &pb.GetUserRichSwitchResp{Enable: enable}, nil
}

// SetUserRichSwitch 设置签约用户财富值开关状态
func (m *Manager) SetUserRichSwitch(ctx context.Context, req *pb.SetUserRichSwitchReq) (*pb.SetUserRichSwitchResp, error) {
	var err error
	resp := &pb.SetUserRichSwitchResp{}
	uid := req.GetUid()
	enable := req.GetEnable()
	changeType := pb.RichSwitchChangeType_RICH_SWITCH_CHANGE_TYPE_MANUAL

	if uid == 0 {
		log.ErrorWithCtx(ctx, "SetUserRichSwitch uid is 0")
		return resp, protocol.NewExactServerError(codes.OK, status.ErrParam)
	}

	contractResp, sErr := m.anchorContractClient.GetUserContract(ctx, uid, uid)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "SetUserRichSwitch GetUserContract err: %+v, uid:%d", err, uid)
		return resp, sErr
	}
	guildId := contractResp.GetContract().GetGuildId()

	if enable {
		// 签约才能开启
		if guildId == 0 {
			log.ErrorWithCtx(ctx, "SetUserRichSwitch uid %d not contract", uid)
			return resp, protocol.NewExactServerError(codes.OK, status.ErrNumericsUserNoContract)
		}
		if err = m.cacheClient.RichSwitchEnable(ctx, uid); err != nil {
			log.ErrorWithCtx(ctx, "SetUserRichSwitch RichSwitchEnable err: %+v, uid:%d", err, uid)
			return resp, err
		}
		log.InfoWithCtx(ctx, "SetUserRichSwitch uid %d rich switch enable, stop rich incr.", uid)
	} else {
		if err = m.cacheClient.RichSwitchDisable(ctx, uid); err != nil {
			log.ErrorWithCtx(ctx, "SetUserRichSwitch RichSwitchEnable err: %+v, uid:%d", err, uid)
			return resp, err
		}
		log.InfoWithCtx(ctx, "SetUserRichSwitch uid %d rich switch disable, start rich incr.", uid)
	}

	go func() {
		record := &mysql.PersonalRichSwitchLog{
			UID:        uid,
			GuildID:    guildId,
			ChangeType: changeType,
			ChangeDesc: "用户操作",
		}
		record.SetOperation(enable)
		if err = m.mysqlStore.CreateSwitchOpLogV2(context.Background(), record); err != nil {
			log.ErrorWithCtx(ctx, "SetUserRichSwitch CreateSwitchOpLogV2 err:%v, uid:%d", err, uid)
		}
	}()

	return resp, nil
}

// HandleAnchorContractChange 处理签约事件
func (m *Manager) HandleAnchorContractChange(ctx context.Context, e *kafkaanchorcontract.AnchorContractEvent) error {
	switch e.EventType {
	case uint32(kafkaanchorcontract.EVENT_TYPE_EVENT_CANCEL_CONTRACT):
		err := m.handleAnchorContractCancel(ctx, e)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleAnchorContractChange handleAnchorContractCancel err: %+v", err)
			return err
		}
	case uint32(kafkaanchorcontract.EVENT_TYPE_EVENT_SIGN_CONTRACT):
		err := m.handleAnchorContractSign(ctx, e)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleAnchorContractChange handleAnchorContractSign err: %+v", err)
			return err
		}
	default:
	}
	return nil
}

// handleAnchorContractCancel 签约用户解约
func (m *Manager) handleAnchorContractCancel(ctx context.Context, e *kafkaanchorcontract.AnchorContractEvent) error {
	uid := e.GetUid()
	guildId := e.GetGuildId()
	changeType := pb.RichSwitchChangeType_RICH_SWITCH_CHANGE_TYPE_CONTRACT_CANCEL

	enable, err := m.cacheClient.GetRichSwitch(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleAnchorContractCancel GetRichSwitch err: %+v, uid:%d", err, uid)
	}

	if enable {
		if err = m.cacheClient.RichSwitchDisable(ctx, uid); err != nil {
			log.ErrorWithCtx(ctx, "handleAnchorContractCancel RichSwitchEnable err: %+v, uid:%d", err, uid)
			return err
		}
		log.InfoWithCtx(ctx, "handleAnchorContractCancel uid %d rich switch disable, start rich incr.", uid)

		if err = m.mysqlStore.CreateSwitchOpLogV2(ctx, &mysql.PersonalRichSwitchLog{
			UID:        uid,
			GuildID:    guildId,
			ChangeType: changeType,
			ChangeDesc: "解约解锁",
			Op:         common.SwitchOpDisable,
		}); err != nil {
			log.ErrorWithCtx(ctx, "handleAnchorContractCancel CreateSwitchOpLogV2 err:%v, uid:%d", err, uid)
		}
	}

	return nil
}

// handleAnchorContractSign 用户签约，检查解约时是否打开了财富值开关
func (m *Manager) handleAnchorContractSign(ctx context.Context, e *kafkaanchorcontract.AnchorContractEvent) error {
	uid := e.GetUid()
	guildId := e.GetGuildId()
	changeType := pb.RichSwitchChangeType_RICH_SWITCH_CHANGE_TYPE_CONTRACT_RENEW

	enable, err := m.cacheClient.GetRichSwitch(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleAnchorContractSign GetRichSwitch err: %+v, uid:%d", err, uid)
	}
	if enable {
		// 已打开开关，不处理
		log.WarnWithCtx(ctx, "handleAnchorContractSign uid %d rich switch already enable, no need to handle.", uid)
		return nil
	}

	// 获取上一次开关操作记录
	record, err := m.mysqlStore.GetLastSwitchOpLog(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleAnchorContractSign GetLastSwitchOpLog err:%v, uid:%d", err, uid)
		return err
	}
	if record == nil || record.UID == 0 {
		// 无记录，不处理
		log.WarnWithCtx(ctx, "handleAnchorContractSign uid %d no switch op log, no need to handle.", uid)
		return nil
	}

	// 上一次是因解约导致关闭
	if record.ChangeType == pb.RichSwitchChangeType_RICH_SWITCH_CHANGE_TYPE_CONTRACT_CANCEL &&
		record.Op == common.SwitchOpDisable {
		// 恢复开关
		if err = m.cacheClient.RichSwitchEnable(ctx, uid); err != nil {
			log.ErrorWithCtx(ctx, "handleAnchorContractSign RichSwitchEnable err: %+v, uid:%d", err, uid)
			return err
		}
		log.InfoWithCtx(ctx, "handleAnchorContractSign uid %d rich switch enable, start rich incr.", uid)

		if err = m.mysqlStore.CreateSwitchOpLogV2(ctx, &mysql.PersonalRichSwitchLog{
			UID:        uid,
			GuildID:    guildId,
			ChangeType: changeType,
			ChangeDesc: "重新签约",
			Op:         common.SwitchOpEnable,
		}); err != nil {
			log.ErrorWithCtx(ctx, "handleAnchorContractSign CreateSwitchOpLogV2 err:%v, uid:%d", err, uid)
		}
		return nil
	}

	return nil
}

func (m *Manager) GetUserLastRichSwitchStatus(ctx context.Context, uid uint32, t time.Time) (common.SwitchOp, error) {
	switchLog, err := m.mysqlStore.GetLastSwitchOpLogByTime(ctx, uid, t)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserLastRichSwitchStatus GetRichSwitch err: %+v, uid:%d", err, uid)
		return common.SwitchOpDisable, err
	}
	if switchLog != nil {
		return switchLog.Op, nil
	}
	return common.SwitchOpDisable, nil

}
