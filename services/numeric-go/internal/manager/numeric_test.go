package manager

import (
	"context"
	"reflect"
	"testing"
	"time"

	"bou.ke/monkey"
	"github.com/golang/mock/gomock"
	"github.com/panjf2000/ants"
	"golang.52tt.com/clients/account"
	anchorcontract_go "golang.52tt.com/clients/anchorcontract-go"
	"golang.52tt.com/clients/channel"
	"golang.52tt.com/clients/channelol"
	"golang.52tt.com/clients/guild"
	missionTL "golang.52tt.com/clients/missiontimeline"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/seqgen/v2"
	channelMsg "golang.52tt.com/pkg/channel-msg"
	"golang.52tt.com/pkg/datacenter"
	"golang.52tt.com/pkg/protocol"
	channelsvr "golang.52tt.com/protocol/services/channelsvr"
	Mission "golang.52tt.com/protocol/services/missiontimelinesvr"
	pb "golang.52tt.com/protocol/services/numeric-go"
	push_notification "golang.52tt.com/protocol/services/push-notification/v2"
	"golang.52tt.com/services/numeric-go/internal/cache"
	"golang.52tt.com/services/numeric-go/internal/config"
	mocks2 "golang.52tt.com/services/numeric-go/internal/mocks"
	"golang.52tt.com/services/numeric-go/internal/mysql"
)

func TestManager_AddUserNumeric(t *testing.T) {
	now := time.Now()
	uid := uint32(123456)
	rich := uint64(100)
	charm := uint64(100)

	monkey.Patch(time.Now, func() time.Time {
		return now
	})

	numeric := &pb.PersonalNumeric{
		Uid:   uid,
		Rich:  rich,
		Charm: charm,
	}

	resp := &pb.AddUserNumericResp{
		FinalRichValue:  200,
		FinalCharmValue: 200,
	}

	// cache
	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "GetPersonalNumeric", func(c *mysql.Store,
		ctx context.Context, uid uint32) (*pb.PersonalNumeric, error) {
		return numeric, nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "GetRichSwitch", func(c *cache.NumericGoCache,
		ctx context.Context, uid uint32) (bool, error) {
		return false, nil
	})

	// mysql
	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "RecordPersonRichCharm", func(s *mysql.Store,
		ctx context.Context, uid uint32, rich, charm uint64) error {
		return nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "AddPersonRichOrCharmChangeLog", func(s *mysql.Store,
		ctx context.Context, t time.Time, numericChangeLogs []*mysql.PersonalRichCharmLog) error {
		return nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "SetPersonalNumeric", func(c *cache.NumericGoCache,
		ctx context.Context, numeric *pb.PersonalNumeric) error {
		return nil
	})

	type fields struct {
		cacheClient          cache.INumericGoCache
		mysqlStore           mysql.IStore
		anchorContractClient anchorcontract_go.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.AddUserNumericReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.AddUserNumericResp
		wantErr bool
	}{
		{name: "AddUserNumeric",
			fields: fields{
				mysqlStore:  &mysql.Store{},
				cacheClient: &cache.NumericGoCache{},
			},
			args: args{ctx: context.Background(), req: &pb.AddUserNumericReq{
				Uid:        uid,
				RichValue:  rich,
				CharmValue: charm,
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheClient:          tt.fields.cacheClient,
				mysqlStore:           tt.fields.mysqlStore,
				anchorContractClient: tt.fields.anchorContractClient,
			}
			got, err := m.AddUserNumeric(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddUserNumeric() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AddUserNumeric() got = %v, want %v", got, tt.want)
			}
		})
	}
	time.Sleep(1 * time.Second)
}

func TestManager_GetPersonalNumeric(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	rich := uint64(100)

	numeric := &pb.PersonalNumeric{
		Uid:  uid,
		Rich: rich,
	}

	resp := &pb.GetPersonalNumericResp{
		Rich: 100,
	}

	mockStore := mocks2.NewMockIStore(ctl)
	mockCache := mocks2.NewMockINumericGoCache(ctl)

	ctx := context.Background()
	gomock.InOrder(
		mockCache.EXPECT().GetPersonalNumeric(ctx, uid).Return(numeric, true, nil),
	)

	type fields struct {
		cacheClient          cache.INumericGoCache
		mysqlStore           mysql.IStore
		anchorContractClient anchorcontract_go.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetPersonalNumericReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetPersonalNumericResp
		wantErr bool
	}{
		{name: "GetPersonalNumeric",
			fields: fields{
				mysqlStore:  mockStore,
				cacheClient: mockCache,
			},
			args: args{ctx: context.Background(), req: &pb.GetPersonalNumericReq{
				Uid: uid,
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheClient:          tt.fields.cacheClient,
				mysqlStore:           tt.fields.mysqlStore,
				anchorContractClient: tt.fields.anchorContractClient,
			}
			got, err := m.GetPersonalNumeric(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPersonalNumeric() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPersonalNumeric() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetPersonalNumericV2(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	rich := uint64(100)

	numeric := &pb.PersonalNumeric{
		Uid:  uid,
		Rich: rich,
	}

	resp := &pb.GetPersonalNumericV2Resp{
		Rich:       100,
		RichLevel:  TransRichOrCharmLevel(pb.NumericT_Rich, rich),
		CharmLevel: TransRichOrCharmLevel(pb.NumericT_Charm, 0),
		VipLevel:   TransVipLevel(rich),
	}

	mockStore := mocks2.NewMockIStore(ctl)
	mockCache := mocks2.NewMockINumericGoCache(ctl)

	ctx := context.Background()
	gomock.InOrder(
		mockCache.EXPECT().GetPersonalNumeric(ctx, uid).Return(numeric, true, nil),
	)

	type fields struct {
		cacheClient          cache.INumericGoCache
		mysqlStore           mysql.IStore
		anchorContractClient anchorcontract_go.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetPersonalNumericV2Req
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetPersonalNumericV2Resp
		wantErr bool
	}{
		{name: "GetPersonalNumericV2",
			fields: fields{
				mysqlStore:  mockStore,
				cacheClient: mockCache,
			},
			args: args{ctx: context.Background(), req: &pb.GetPersonalNumericV2Req{
				Uid: uid,
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheClient:          tt.fields.cacheClient,
				mysqlStore:           tt.fields.mysqlStore,
				anchorContractClient: tt.fields.anchorContractClient,
			}
			got, err := m.GetPersonalNumericV2(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPersonalNumericV2() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPersonalNumericV2() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_GetVipSince(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	now := time.Now()
	uid := uint32(123456)
	rich := uint64(1000000)

	numeric := &pb.PersonalNumeric{
		Uid:  uid,
		Rich: rich,
	}

	vip := &mysql.PersonRichVip{
		Uid:        uid,
		VipLevel:   1,
		CreateTime: now,
	}

	resp := &pb.GetVipSinceResp{
		VipLevel:  TransVipLevel(rich),
		BeVipTime: uint64(now.Unix()),
		// BeVipDays: 1,
	}

	mockStore := mocks2.NewMockIStore(ctl)
	mockCache := mocks2.NewMockINumericGoCache(ctl)

	ctx := context.Background()
	gomock.InOrder(
		mockCache.EXPECT().GetPersonalNumeric(ctx, uid).Return(numeric, true, nil),
		mockStore.EXPECT().GetPersonVip(ctx, uid).Return(vip, nil),
	)

	type fields struct {
		cacheClient          cache.INumericGoCache
		mysqlStore           mysql.IStore
		anchorContractClient anchorcontract_go.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetVipSinceReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetVipSinceResp
		wantErr bool
	}{
		{name: "GetVipSince",
			fields: fields{
				mysqlStore:  mockStore,
				cacheClient: mockCache,
			},
			args: args{ctx: context.Background(), req: &pb.GetVipSinceReq{
				Uid: uid,
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheClient:          tt.fields.cacheClient,
				mysqlStore:           tt.fields.mysqlStore,
				anchorContractClient: tt.fields.anchorContractClient,
			}
			got, err := m.GetVipSince(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetVipSince() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetVipSince() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_getOnePersonalNumeric(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	rich := uint64(100)

	numeric := &pb.PersonalNumeric{
		Uid:  uid,
		Rich: rich,
	}

	mockStore := mocks2.NewMockIStore(ctl)
	mockCache := mocks2.NewMockINumericGoCache(ctl)

	ctx := context.Background()
	gomock.InOrder(
		mockCache.EXPECT().GetPersonalNumeric(ctx, uid).Return(nil, false, nil),
		mockStore.EXPECT().GetPersonalNumeric(ctx, uid).Return(numeric, nil),
		mockCache.EXPECT().SetPersonalNumeric(ctx, numeric).Return(nil),
	)

	type fields struct {
		cacheClient          cache.INumericGoCache
		mysqlStore           mysql.IStore
		anchorContractClient anchorcontract_go.IClient
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.PersonalNumeric
		wantErr bool
	}{
		{name: "getOnePersonalNumeric",
			fields: fields{
				mysqlStore:  mockStore,
				cacheClient: mockCache,
			},
			args:    args{ctx: context.Background(), uid: uid},
			want:    numeric,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheClient:          tt.fields.cacheClient,
				mysqlStore:           tt.fields.mysqlStore,
				anchorContractClient: tt.fields.anchorContractClient,
			}
			got, err := m.getOnePersonalNumeric(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("getOnePersonalNumeric() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getOnePersonalNumeric() got = %v, want %v", got, tt.want)
			}
		})
	}
	time.Sleep(1 * time.Second)
}

func TestManager_BatchGetPersonalNumeric(t *testing.T) {
	uid1 := uint32(123456)
	uid2 := uint32(456789)
	val := uint64(1000000)
	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "BatchGetPersonalNumeric", func(c *cache.NumericGoCache,
		ctx context.Context, userList []uint32) ([]*pb.PersonalNumeric, []uint32, error) {
		return []*pb.PersonalNumeric{
			{
				Uid:   uid1,
				Charm: val,
				Rich:  val,
			},
		}, []uint32{uid2}, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "BatchSetPersonalNumeric", func(c *cache.NumericGoCache,
		ctx context.Context, userNumericList []*pb.PersonalNumeric) error {
		return nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "BatchGetPersonalNumeric", func(s *mysql.Store,
		ctx context.Context, uidList []uint32) ([]*pb.PersonalNumeric, error) {
		return []*pb.PersonalNumeric{
			{
				Uid:   uid2,
				Charm: val,
				Rich:  val,
			},
		}, nil
	})
	type fields struct {
		cacheClient          cache.INumericGoCache
		mysqlStore           mysql.IStore
		anchorContractClient anchorcontract_go.IClient
		taskPool             *ants.Pool
		configCenter         config.IConfigCenter
	}
	type args struct {
		ctx context.Context
		req *pb.BatchGetPersonalNumericReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.BatchGetPersonalNumericResp
		wantErr bool
	}{
		{name: "BatchGetPersonalNumeric",
			fields: fields{
				mysqlStore:  &mysql.Store{},
				cacheClient: &cache.NumericGoCache{},
			},
			args: args{
				ctx: context.Background(),
				req: &pb.BatchGetPersonalNumericReq{
					UidList: []uint32{uid1, uid2},
				},
			},
			want: &pb.BatchGetPersonalNumericResp{
				NumericList: []*pb.PersonalNumeric{
					{Uid: uid1, Rich: val, Charm: val,
						RichLevel:  TransRichOrCharmLevel(pb.NumericT_Rich, val),
						CharmLevel: TransRichOrCharmLevel(pb.NumericT_Charm, val)},
					{Uid: uid2, Rich: val, Charm: val,
						RichLevel:  TransRichOrCharmLevel(pb.NumericT_Rich, val),
						CharmLevel: TransRichOrCharmLevel(pb.NumericT_Charm, val)},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheClient:          tt.fields.cacheClient,
				mysqlStore:           tt.fields.mysqlStore,
				anchorContractClient: tt.fields.anchorContractClient,
				taskPool:             tt.fields.taskPool,
				configCenter:         tt.fields.configCenter,
			}
			got, err := m.BatchGetPersonalNumeric(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetPersonalNumeric() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchGetPersonalNumeric() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_UseRichCard_process(t *testing.T) {
	orderId := "order_id"
	uid := uint32(123456)
	channelId := uint32(123456)
	addRich := uint64(1000000)
	userNumeric := &pb.PersonalNumeric{
		Uid:   uid,
		Charm: 0,
		Rich:  0,
	}

	monkey.PatchInstanceMethod(reflect.TypeOf(&channelol.Client{}), "GetUserChannelId", func(s *channelol.Client,
		ctx context.Context, uin uint32, uid uint32) (uint32, protocol.ServerError) {
		return channelId, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "GetRichCardOrder", func(s *mysql.Store,
		ctx context.Context, orderId string) (*mysql.RichCardOrder, error) {
		return &mysql.RichCardOrder{
			ID:        0,
			UID:       uid,
			ChannelID: channelId,
			OrderID:   orderId,
			AddRich:   addRich,
			CardID:    1,
			UseCount:  1,
			CardName:  "name",
		}, nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "CertainUseRichCard", func(s *mysql.Store,
		ctx context.Context, card *mysql.RichCardOrder, userNumeric *pb.PersonalNumeric) error {
		return nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "GetPersonalNumeric", func(c *cache.NumericGoCache,
		ctx context.Context, uid uint32) (*pb.PersonalNumeric, bool, error) {
		return userNumeric, true, nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "GetRichSwitch", func(c *cache.NumericGoCache,
		ctx context.Context, uid uint32) (bool, error) {
		return false, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "BatchGetPersonalNumeric", func(c *cache.NumericGoCache,
		ctx context.Context, userList []uint32) ([]*pb.PersonalNumeric, []uint32, error) {
		return []*pb.PersonalNumeric{
			{
				Uid: uid,
			},
		}, []uint32{}, nil
	})

	userNumeric1 := *userNumeric
	userNumeric1.Rich = userNumeric.Rich + addRich
	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "GetPersonalNumeric", func(s *mysql.Store,
		ctx context.Context, uid uint32) (*pb.PersonalNumeric, error) {
		return &userNumeric1, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&Manager{}), "HandleVipLevelChange", func(m *Manager,
		ctx context.Context, sender uint32, beforeRich, afterRich uint64) {
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "SetPersonalNumeric", func(c *cache.NumericGoCache,
		ctx context.Context, u *pb.PersonalNumeric) error {
		return nil
	})

	// datacenter
	monkey.Patch(datacenter.StdReportKV, func(ctx context.Context, bizId string, kv map[string]interface{}) {
		return
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "BatchSetPersonalNumeric", func(c *cache.NumericGoCache,
		ctx context.Context, userNumericList []*pb.PersonalNumeric) error {
		return nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&seqgen.Client{}), "GenerateSequence", func(s *seqgen.Client,
		ctx context.Context, id uint32, ns, key string, incr uint32) (uint64, protocol.ServerError) {
		return 1, nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&missionTL.Client{}), "NumericChanged", func(s *missionTL.Client,
		ctx context.Context, uid uint32, seq uint32, msg *Mission.NumericInfoMessage) protocol.ServerError {
		return nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&PushNotification.Client{}), "PushToUsers", func(s *PushNotification.Client,
		ctx context.Context, userIDList []uint32, notification *push_notification.CompositiveNotification) error {
		return nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&channel.Client{}), "GetChannelSimpleInfo", func(s *channel.Client,
		ctx context.Context, uin uint32, channelId uint32) (*channelsvr.ChannelSimpleInfo, protocol.ServerError) {
		return &channelsvr.ChannelSimpleInfo{
			ChannelId: &channelId,
		}, nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&channelMsg.Sender{}), "SingleCast", func(s *channelMsg.Sender,
		ctx context.Context, msg *channelMsg.PushMessage, uidList []uint32, opt *channelMsg.SingleCastOpt) protocol.ServerError {
		return nil
	})

	type fields struct {
		cacheClient          cache.INumericGoCache
		mysqlStore           mysql.IStore
		anchorContractClient anchorcontract_go.IClient
		guildClient          guild.IClient
		accountClient        account.IClient
		missionTLCli         missionTL.IClient
		pushCli              PushNotification.IClient
		seqGenCli            seqgen.IClient
		channelOlCli         channelol.IClient
		channelCli           channel.IClient
		channelMsgSender     channelMsg.ISender
		taskPool             *ants.Pool
		configCenter         config.IConfigCenter
	}
	type args struct {
		ctx context.Context
		req *pb.UseRichCardReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.UseRichCardResp
		wantErr bool
	}{
		{name: "UseRichCard_process",
			fields: fields{
				mysqlStore:       &mysql.Store{},
				cacheClient:      &cache.NumericGoCache{},
				channelOlCli:     &channelol.Client{},
				channelCli:       &channel.Client{},
				channelMsgSender: &channelMsg.Sender{},
				seqGenCli:        &seqgen.Client{},
				missionTLCli:     &missionTL.Client{},
				pushCli:          &PushNotification.Client{},
			},
			args: args{
				ctx: context.Background(),
				req: &pb.UseRichCardReq{
					OrderId:  orderId,
					Uid:      uid,
					AddRich:  addRich,
					Opt:      pb.UseRichCardReq_OPT_PROCESS,
					CardId:   1,
					UseCount: 1,
					CardName: "财富卡",
				},
			},
			want: &pb.UseRichCardResp{
				AddRich:   addRich,
				FinalRich: addRich,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheClient:          tt.fields.cacheClient,
				mysqlStore:           tt.fields.mysqlStore,
				anchorContractClient: tt.fields.anchorContractClient,
				guildClient:          tt.fields.guildClient,
				accountClient:        tt.fields.accountClient,
				missionTLCli:         tt.fields.missionTLCli,
				pushCli:              tt.fields.pushCli,
				seqGenCli:            tt.fields.seqGenCli,
				channelOlCli:         tt.fields.channelOlCli,
				channelCli:           tt.fields.channelCli,
				channelMsgSender:     tt.fields.channelMsgSender,
				taskPool:             tt.fields.taskPool,
				configCenter:         tt.fields.configCenter,
			}
			got, err := m.UseRichCard(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UseRichCard() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UseRichCard() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_UseRichCard(t *testing.T) {
	orderId := "order_id"
	uid := uint32(123456)
	channelId := uint32(123456)
	addRich := uint64(1000000)

	monkey.PatchInstanceMethod(reflect.TypeOf(&channelol.Client{}), "GetUserChannelId", func(s *channelol.Client,
		ctx context.Context, uin uint32, uid uint32) (uint32, protocol.ServerError) {
		return channelId, nil
	})

	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "PreUseRichCard", func(s *mysql.Store,
		ctx context.Context, card *mysql.RichCardOrder) error {
		return nil
	})

	type fields struct {
		cacheClient          cache.INumericGoCache
		mysqlStore           mysql.IStore
		anchorContractClient anchorcontract_go.IClient
		guildClient          guild.IClient
		accountClient        account.IClient
		missionTLCli         missionTL.IClient
		pushCli              PushNotification.IClient
		seqGenCli            seqgen.IClient
		channelOlCli         channelol.IClient
		taskPool             *ants.Pool
		configCenter         config.IConfigCenter
	}
	type args struct {
		ctx context.Context
		req *pb.UseRichCardReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.UseRichCardResp
		wantErr bool
	}{
		{name: "UseRichCard",
			fields: fields{
				mysqlStore:   &mysql.Store{},
				cacheClient:  &cache.NumericGoCache{},
				channelOlCli: &channelol.Client{},
			},
			args: args{
				ctx: context.Background(),
				req: &pb.UseRichCardReq{
					OrderId:  orderId,
					Uid:      uid,
					AddRich:  addRich,
					Opt:      pb.UseRichCardReq_OPT_PREPROCESS,
					CardId:   1,
					UseCount: 1,
					CardName: "财富卡",
				},
			},
			want:    &pb.UseRichCardResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheClient:          tt.fields.cacheClient,
				mysqlStore:           tt.fields.mysqlStore,
				anchorContractClient: tt.fields.anchorContractClient,
				guildClient:          tt.fields.guildClient,
				accountClient:        tt.fields.accountClient,
				missionTLCli:         tt.fields.missionTLCli,
				pushCli:              tt.fields.pushCli,
				seqGenCli:            tt.fields.seqGenCli,
				channelOlCli:         tt.fields.channelOlCli,
				taskPool:             tt.fields.taskPool,
				configCenter:         tt.fields.configCenter,
			}
			got, err := m.UseRichCard(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UseRichCard() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UseRichCard() got = %v, want %v", got, tt.want)
			}
		})
	}
}
