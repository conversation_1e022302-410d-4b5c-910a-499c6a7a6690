package manager

import (
	"context"
	"errors"
	"math/rand"
	"time"

	"golang.52tt.com/pkg/protocol/grpc"

	"golang.52tt.com/services/numeric-go/internal/common"
	"golang.52tt.com/services/numeric-go/internal/config"
	"golang.52tt.com/services/numeric-go/internal/mysql"

	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/numeric-go"
	"golang.org/x/sync/errgroup"
)

var ErrUidNoLock = errors.New("uid no lock")

// GeneralNumericHandle 通用财富值变更处理
func (m *Manager) GeneralNumericHandle(ctx context.Context, gn common.GeneralNumeric) (*common.GeneralNumeric, error) {

	// validate required
	if err := gn.Validate(); err != nil {
		log.WarnWithCtx(ctx, "GeneralNumericHandle Validate err: %s, order_id:%s", err, gn.OrderId)
		return nil, err
	}

	orderId, sendTime, sender, receiver, maybeAddRich, maybeAddCharm :=
		gn.OrderId, gn.SendTime, gn.Sender, gn.Receiver, gn.AddRich, gn.AddCharm

	// 财富值开关
	var isRecordRich bool
	if gn.IsReconcile {
		// 补单时通过订单时间回溯记录
		switchOp, err := m.GetUserLastRichSwitchStatus(ctx, sender, sendTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "GeneralNumericHandle GetUserLastRichSwitchStatus err: %s, order_id:%s, sender:%d", err, orderId, sender)
			return nil, err
		}
		isRecordRich = switchOp != common.SwitchOpEnable
	} else {
		// 获取财富值开关状态
		richSwitchEnable, err := m.cacheClient.GetRichSwitch(ctx, sender)
		if err != nil {
			// 不忽略错误，确保状态准确，便于对账
			log.ErrorWithCtx(ctx, "GeneralNumericHandle GetRichSwitch err: %s, order_id:%s, sender:%d", err, orderId, sender)
			return nil, err
		}
		isRecordRich = !richSwitchEnable
	}

	// func card acceleration
	// 加速卡在上层处理了

	// check order repeat
	reconcile, err := m.mysqlStore.GetRichReconcile(ctx, orderId, sendTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "GeneralNumericHandle GetRichReconcile err: %s, order_id:%s, sender:%d", err, orderId, sender)
		return nil, err
	}
	if reconcile != nil && reconcile.Status == mysql.ReconcileStatusSuccess {
		log.WarnWithCtx(ctx, "GeneralNumericHandle reconcile success, order_id:%s, sender:%d", orderId, sender)
		return nil, nil
	}

	// 实际增加的财富值与魅力值
	var addRich, addCharm uint64
	if isRecordRich {
		addRich = maybeAddRich
	}
	addCharm = maybeAddCharm

	if addRich == 0 && addCharm == 0 {
		log.WarnWithCtx(ctx, "GeneralNumericHandle NotHandle, AddRich:%d, AddCharm:%d, order_id:%s, sender:%d", addRich, addCharm, orderId, sender)
		return nil, nil
	}

	// reconcile
	if err = m.mysqlStore.CreateRichReconcile(ctx, &gn); err != nil {
		log.ErrorWithCtx(ctx, "GeneralNumericHandle CreateRichReconcile err:%s, order_id:%s, sender:%d", err, orderId, sender)
		return nil, err
	}

	err = m.WithSenderLock(ctx, orderId, sender, func() error {
		// get before rich and charm
		senderNumeric, receiverNumeric, err := m.getPresentUserNumeric(ctx, sender, receiver)
		if err != nil {
			log.ErrorWithCtx(ctx, "GeneralNumericHandle get sender and receiver before numeric err:%s, order_id:%s, sender:%d", err, orderId, sender)
			return err
		}

		// before
		beforeSenderRich, beforeReceiverCharm := senderNumeric.GetRich(), receiverNumeric.GetCharm()
		// after
		afterSenderRich, afterReceiverCharm := beforeSenderRich+addRich, beforeReceiverCharm+addCharm

		// level
		isSenderLevelChange := isRecordRich && checkRichOrCharmLevelChange(beforeSenderRich, afterSenderRich)
		isReceiverLevelChange := checkRichOrCharmLevelChange(beforeReceiverCharm, afterReceiverCharm)

		// 替换为实际增加的财富值
		gn.AddRich = addRich
		gn.AddCharm = addCharm
		gn.IsRecordRich = isRecordRich
		gn.BeforeRich = beforeSenderRich
		gn.BeforeCharm = beforeReceiverCharm
		gn.AfterRich = afterSenderRich
		gn.AfterCharm = afterReceiverCharm
		gn.IsRichLevelChange = isSenderLevelChange
		gn.IsCharmLevelChange = isReceiverLevelChange

		log.InfoWithCtx(ctx, "GeneralNumericHandle start record gn: %+v", gn)

		if err = m.mysqlStore.RecordGiftEventChange(ctx, &gn); err != nil {
			log.ErrorWithCtx(ctx, "GeneralNumericHandle RecordGiftEventChange err:%s, order_id:%s, sender:%d", err, orderId, sender)
			_ = m.mysqlStore.SetRichReconcileStatus(ctx, orderId, sendTime, mysql.ReconcileStatusFailed)
			return err
		}

		wg := new(errgroup.Group)
		wg.Go(func() error {
			if err = m.mysqlStore.SetRichReconcileStatus(ctx, orderId, sendTime, mysql.ReconcileStatusSuccess); err != nil {
				log.ErrorWithCtx(ctx, "GeneralNumericHandle SetRichReconcileStatus to ReconcileStatusSuccess err:%s, order_id:%s, sender:%d", err, orderId, sender)
				return err
			}
			return nil
		})

		// cache
		if isRecordRich {
			wg.Go(func() error {
				if err = m.cacheClient.SetPersonalRich(ctx, sender, afterSenderRich); err != nil {
					log.ErrorWithCtx(ctx, "GeneralNumericHandle SetPersonalRich err: %s, uid: %d, after_rich: %d", err, sender, afterSenderRich)
					_ = m.cacheClient.DelPersonalNumeric(ctx, sender)
				}
				return nil
			})
		}
		wg.Go(func() error {
			if err = m.cacheClient.SetPersonalCharm(ctx, receiver, afterReceiverCharm); err != nil {
				log.ErrorWithCtx(ctx, "GeneralNumericHandle SetPersonalCharm err: %s, uid: %d, after_charm: %d", err, receiver, afterReceiverCharm)
				_ = m.cacheClient.DelPersonalNumeric(ctx, receiver)
			}
			if afterReceiverCharm == 0 { // DEBUG 有个别用户反馈魅力值缓存变0，加日志看下
				log.ErrorWithCtx(ctx, "GeneralNumericHandle err afterReceiverCharm is 0, order_id:%s, sender:%d, receiverNumeric:%+v", orderId, sender, receiverNumeric)
				_, _ = m.reloadNumeric(ctx, receiver)
			}
			return nil
		})

		_ = wg.Wait()

		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GeneralNumericHandle lockSender err: %s, order_id:%s, sender:%d", err, orderId, sender)
		return nil, err
	}

	// 减少推送时差，后面的异步处理
	go func() {
		bgCtx := grpc.NewContextWithInfo(ctx)
		// 榜单
		m.PersonRichRankHandle(bgCtx, sendTime, sender, maybeAddRich) // 重要！榜单不受开关影响
		m.PersonCharmRankHandle(bgCtx, sendTime, receiver, maybeAddCharm)

		// sync
		if gn.IsRichLevelChange {
			m.syncMissionNumeric(bgCtx, sender)
			m.notifyClient(bgCtx, []uint32{sender}, 6)
		}
		if gn.IsCharmLevelChange {
			m.syncMissionNumeric(bgCtx, receiver)
			m.notifyClient(bgCtx, []uint32{receiver}, 6)
		}

		// vip
		m.HandleVipLevelChange(bgCtx, sender, gn.BeforeRich, gn.AfterRich)

		// oss 上报财富魅力值变化
		m.ReportDataCenter(bgCtx, sender, receiver, sendTime, isRecordRich, gn.AfterRich, gn.AfterCharm)
	}()

	return &gn, nil
}

func (m *Manager) WithSenderLock(ctx context.Context, orderId string, sender uint32, fn func() error) error {
	if m.configCenter.DisableSenderLock() {
		return fn()
	}

	const maxRetry = 3
	for i := 0; i < maxRetry; i++ {
		lock, err := m.cacheClient.LockUid(ctx, sender)
		if err == nil && lock {
			fnErr := fn()
			if unlockErr := m.cacheClient.UnlockUid(ctx, sender); unlockErr != nil {
				log.WarnWithCtx(ctx, "WithSenderLock unlock failed, order_id:%s, sender:%d, err:%v", orderId, sender, unlockErr)
			}
			return fnErr
		}
		time.Sleep(time.Duration(10+rand.Intn(20)) * time.Millisecond)
	}

	log.WarnWithCtx(ctx, "WithSenderLock lock failed after retries, order_id:%s, sender:%d", orderId, sender)
	return ErrUidNoLock
}

func (m *Manager) getPresentUserNumeric(ctx context.Context, sender, receiver uint32) (*pb.PersonalNumeric, *pb.PersonalNumeric, error) {
	wg := new(errgroup.Group)
	var err error
	var senderNumeric, receiverNumeric *pb.PersonalNumeric
	wg.Go(func() error {
		senderNumeric, err = m.mysqlStore.GetPersonalNumeric(ctx, sender)
		if err != nil {
			if errors.Is(err, mysql.NumericNotFoundErr) { // 新用户
				senderNumeric = &pb.PersonalNumeric{Uid: sender}
			} else { // 否则报错中止
				log.ErrorWithCtx(ctx, "getPresentUserNumeric GetPersonalNumeric err: %s, sender:%d", err, sender)
				return err
			}
		}
		return nil
	})
	wg.Go(func() error {
		receiverNumeric, err = m.mysqlStore.GetPersonalNumeric(ctx, receiver)
		if err != nil {
			if errors.Is(err, mysql.NumericNotFoundErr) {
				receiverNumeric = &pb.PersonalNumeric{Uid: receiver}
			} else {
				log.ErrorWithCtx(ctx, "getPresentUserNumeric GetPersonalNumeric err: %s, receiver:%d", err, receiver)
				return err
			}
		}
		return nil
	})

	if err := wg.Wait(); err != nil {
		log.ErrorWithCtx(ctx, "getPresentUserNumeric wg.Wait err: %s, sender:%d, receiver:%d", err, sender, receiver)
		return nil, nil, err
	}

	return senderNumeric, receiverNumeric, nil
}

// HandleVipLevelChange handles VIP level changes when rich value changes
func (m *Manager) HandleVipLevelChange(ctx context.Context, sender uint32, beforeRich, afterRich uint64) {
	if afterRich >= config.VipThreshold {
		beforeVipLevel := TransVipLevel(beforeRich)
		afterVipLevel := TransVipLevel(afterRich)
		if afterVipLevel.MainLevel > beforeVipLevel.MainLevel {
			log.InfoWithCtx(ctx, "HandleVipLevelChange sender:%d, beforeVipLevel:%+v, afterVipLevel:%+v", sender, beforeVipLevel, afterVipLevel)
			if err := m.mysqlStore.RecordPersonVip(ctx, sender, afterVipLevel.MainLevel); err != nil {
				log.ErrorWithCtx(ctx, "HandleVipLevelChange RecordPersonVip err: %s, sender:%d", err, sender)
			}
			if err := m.vipPrivilegesGoCli.OnRichChange(ctx, sender, beforeRich, afterRich); err != nil {
				log.ErrorWithCtx(ctx, "HandleVipLevelChange vipPrivilegesGoCli OnRichChange err: %s, sender:%d", err, sender)
			}
		}
	}
}
