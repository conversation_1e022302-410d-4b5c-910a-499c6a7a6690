package manager

import (
	"context"
	"reflect"
	"testing"
	"time"

	"bou.ke/monkey"
	pb "golang.52tt.com/protocol/services/numeric-go"
	"golang.52tt.com/services/numeric-go/internal/cache"
	"golang.52tt.com/services/numeric-go/internal/common"
	"golang.52tt.com/services/numeric-go/internal/mysql"
)

func TestManager_GeneralNumericHandle(t *testing.T) {
	// Mock time.Now() to return a fixed time
	now := time.Now()
	monkey.Patch(time.Now, func() time.Time { return now })

	// Mock GetRichSwitch to return false (meaning we should record rich)
	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "GetRichSwitch", func(c *cache.NumericGoCache, ctx context.Context, uid uint32) (bool, error) {
		return false, nil
	})

	// Mock GetRichReconcile to return nil (no existing reconcile)
	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "GetRichReconcile", func(s *mysql.Store, ctx context.Context, orderId string, sendTime time.Time) (*mysql.RichReconcile, error) {
		return nil, nil
	})

	// Mock CreateRichReconcile to return nil
	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "CreateRichReconcile", func(s *mysql.Store, ctx context.Context, gn *common.GeneralNumeric) error {
		return nil
	})

	// getPresentUserNumeric - BatchGetPersonalNumeric
	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "GetPersonalNumeric", func(s *mysql.Store, ctx context.Context, uid uint32) (*pb.PersonalNumeric, error) {
		if uid == 1001 {
			return &pb.PersonalNumeric{Uid: 1001, Rich: 100, Charm: 200}, nil
		} else if uid == 2001 {
			return &pb.PersonalNumeric{Uid: 2001, Rich: 300, Charm: 400}, nil
		} else {
			return nil, nil
		}
	})

	// Mock RecordGiftEventChange to return nil
	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "RecordGiftEventChange", func(s *mysql.Store, ctx context.Context, gn *common.GeneralNumeric) error {
		return nil
	})

	// Mock SetRichReconcileStatus to return nil
	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "SetRichReconcileStatus", func(s *mysql.Store, ctx context.Context, orderId string, sendTime time.Time, status mysql.ReconcileStatus) error {
		return nil
	})

	// Mock SetPersonalRich and SetPersonalCharm to return nil
	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "SetPersonalRich", func(c *cache.NumericGoCache, ctx context.Context, uid uint32, rich uint64) error {
		return nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "SetPersonalCharm", func(c *cache.NumericGoCache, ctx context.Context, uid uint32, charm uint64) error {
		return nil
	})

	// Mock WithSenderLock to return nil
	monkey.PatchInstanceMethod(reflect.TypeOf(&Manager{}), "WithSenderLock", func(m *Manager, ctx context.Context, orderId string, sender uint32, f func() error) error {
		return f()
	})

	// PersonRichRankHandle
	monkey.PatchInstanceMethod(reflect.TypeOf(&Manager{}), "PersonRichRankHandle", func(m *Manager, ctx context.Context, t time.Time, sender uint32, richVal uint64) {
	})

	// PersonCharmRankHandle
	monkey.PatchInstanceMethod(reflect.TypeOf(&Manager{}), "PersonCharmRankHandle", func(m *Manager, ctx context.Context, t time.Time, sender uint32, charmVal uint64) {
	})

	type fields struct {
		cacheClient cache.INumericGoCache
		mysqlStore  mysql.IStore
	}
	type args struct {
		ctx context.Context
		gn  common.GeneralNumeric
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *common.GeneralNumeric
		wantErr bool
	}{
		{
			name: "normal case - add rich and charm",
			fields: fields{
				cacheClient: &cache.NumericGoCache{},
				mysqlStore:  &mysql.Store{},
			},
			args: args{
				ctx: context.Background(),
				gn: common.GeneralNumeric{
					Sender:     1001,
					Receiver:   2001,
					OrderId:    "test_order_1",
					AddRich:    50,
					AddCharm:   50,
					SendTime:   now,
					SourceType: pb.SourceT_SourceTPresentTBean,
				},
			},
			want: &common.GeneralNumeric{
				Sender:       1001,
				Receiver:     2001,
				OrderId:      "test_order_1",
				AddRich:      50,
				AddCharm:     50,
				SendTime:     now,
				SourceType:   pb.SourceT_SourceTPresentTBean,
				BeforeRich:   100,
				BeforeCharm:  400,
				AfterRich:    150,
				AfterCharm:   450,
				IsRecordRich: true,
			},
			wantErr: false,
		},
		{
			name: "validation error - missing required fields",
			fields: fields{
				cacheClient: &cache.NumericGoCache{},
				mysqlStore:  &mysql.Store{},
			},
			args: args{
				ctx: context.Background(),
				gn:  common.GeneralNumeric{
					// Missing required fields
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "no changes - both addRich and addCharm are 0",
			fields: fields{
				cacheClient: &cache.NumericGoCache{},
				mysqlStore:  &mysql.Store{},
			},
			args: args{
				ctx: context.Background(),
				gn: common.GeneralNumeric{
					Sender:     1001,
					Receiver:   2001,
					OrderId:    "test_order_2",
					AddRich:    0,
					AddCharm:   0,
					AfterRich:  100,
					AfterCharm: 400,
					SendTime:   now,
					SourceType: pb.SourceT_SourceTPresentTBean,
				},
			},
			want:    nil,
			wantErr: true, // add_rich or add_charm is required
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheClient: tt.fields.cacheClient,
				mysqlStore:  tt.fields.mysqlStore,
			}
			got, err := m.GeneralNumericHandle(tt.args.ctx, tt.args.gn)
			if (err != nil) != tt.wantErr {
				t.Errorf("GeneralNumericHandle() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil {
				if got == nil {
					t.Errorf("GeneralNumericHandle() got = nil, want %v", tt.want)
					return
				}
				// Compare relevant fields
				if got.Sender != tt.want.Sender ||
					got.Receiver != tt.want.Receiver ||
					got.OrderId != tt.want.OrderId ||
					got.AddRich != tt.want.AddRich ||
					got.AddCharm != tt.want.AddCharm ||
					got.BeforeRich != tt.want.BeforeRich ||
					got.BeforeCharm != tt.want.BeforeCharm ||
					got.AfterRich != tt.want.AfterRich ||
					got.AfterCharm != tt.want.AfterCharm ||
					got.IsRecordRich != tt.want.IsRecordRich {
					t.Errorf("GeneralNumericHandle() got = %+v, want %+v", got, tt.want)
				}
			} else if got != nil {
				t.Errorf("GeneralNumericHandle() got = %v, want nil", got)
			}
		})
	}
}
