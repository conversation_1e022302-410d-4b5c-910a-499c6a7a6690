package manager

import (
	"context"
	"fmt"
	"math"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	channelMsg "golang.52tt.com/pkg/channel-msg"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	channelGA "golang.52tt.com/protocol/app/channel"
	numericlogic "golang.52tt.com/protocol/app/numeric-logic"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/numeric-go"
	"golang.52tt.com/services/numeric-go/internal/config"
	"golang.52tt.com/services/numeric-go/internal/mysql"
	"golang.52tt.com/services/numeric-go/internal/utils"
	"google.golang.org/grpc/codes"
)

// getOnePersonalNumeric 获取单个用户财富魅力值
func (m *Manager) getOnePersonalNumeric(ctx context.Context, uid uint32) (*pb.PersonalNumeric, error) {
	userNumeric, exist, err := m.cacheClient.GetPersonalNumeric(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "getOnePersonalNumeric GetPersonalNumeric err:%s", err.Error())
		return nil, err
	}
	if !exist {
		log.DebugWithCtx(ctx, "getOnePersonalNumeric GetPersonalNumeric uid no cache hit, get from db, uid:%d", uid)
		userNumeric, err = m.mysqlStore.GetPersonalNumeric(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "getOnePersonalNumeric GetPersonalNumeric err:%s", err.Error())
			return nil, err
		}
		go func() {
			bgCtx := context.Background()
			userNumeric.Uid = uid
			_ = m.cacheClient.SetPersonalNumeric(bgCtx, userNumeric)
		}()
	}
	return userNumeric, nil
}

// GetPersonalNumeric 获取用户财富值魅力值
func (m *Manager) GetPersonalNumeric(ctx context.Context, req *pb.GetPersonalNumericReq) (*pb.GetPersonalNumericResp, error) {
	resp := new(pb.GetPersonalNumericResp)
	userNumeric, err := m.getOnePersonalNumeric(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPersonalNumeric err:%s", err.Error())
		return resp, err
	}
	return &pb.GetPersonalNumericResp{
		Rich:  userNumeric.Rich,
		Charm: userNumeric.Charm,
	}, nil
}

// GetPersonalNumericV2 获取财富值、魅力值、财富等级、魅力等级、VIP等级
func (m *Manager) GetPersonalNumericV2(ctx context.Context, req *pb.GetPersonalNumericV2Req) (*pb.GetPersonalNumericV2Resp, error) {
	resp := new(pb.GetPersonalNumericV2Resp)
	userNumeric, err := m.getOnePersonalNumeric(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPersonalNumeric err:%s", err.Error())
		return resp, err
	}

	return &pb.GetPersonalNumericV2Resp{
		Rich:       userNumeric.Rich,
		Charm:      userNumeric.Charm,
		RichLevel:  TransRichOrCharmLevel(pb.NumericT_Rich, userNumeric.Rich),
		CharmLevel: TransRichOrCharmLevel(pb.NumericT_Charm, userNumeric.Charm),
		VipLevel:   TransVipLevel(userNumeric.Rich),
	}, nil
}

// AddUserNumeric 增加用户财富魅力值
func (m *Manager) AddUserNumeric(ctx context.Context, req *pb.AddUserNumericReq) (*pb.AddUserNumericResp, error) {
	resp := new(pb.AddUserNumericResp)
	uid := req.GetUid()
	richVal := req.GetRichValue()
	charmVal := req.GetCharmValue()
	orderId := req.GetOrderId()
	now := time.Now()

	// 早期接入的业务没有orderId 随便创建一个
	if orderId == "" {
		orderId = fmt.Sprintf("NUM_UNK_%d_%d", uid, now.UnixNano())
	}

	// 变更前的财富值
	userNumeric, err := m.mysqlStore.GetPersonalNumeric(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddUserNumeric GetPersonalNumeric err:%s", err.Error())
		return resp, err
	}

	beforeRich := userNumeric.GetRich()      // 变更前的财富值
	beforeCharm := userNumeric.GetCharm()    // 变更前的魅力值
	afterRich := beforeRich + richVal        // 变更后的财富值
	afterCharm := beforeCharm + charmVal     // 变更后的魅力值
	isRecordRich := m.IsRecordRich(ctx, uid) // 签约用户是否记录财富值
	realRichVal := uint64(0)
	if isRecordRich {
		realRichVal = richVal
	}

	log.InfoWithCtx(ctx, "AddUserNumeric: uid = %d, add_rich = %d, add_charm = %d, before_rich = %d, before_charm = %d, isRecordRich = %v, real_add_rich:%d",
		uid, richVal, charmVal, beforeRich, beforeCharm, isRecordRich, realRichVal)

	if err = m.mysqlStore.RecordPersonRichCharm(ctx, uid, realRichVal, charmVal); err != nil {
		log.ErrorWithCtx(ctx, "AddUserNumeric RecordPersonRichCharm err: %s", err.Error())
		return resp, err
	}

	// 变更记录
	numericChangeLogs := make([]*mysql.PersonalRichCharmLog, 0)
	if isRecordRich && richVal > 0 {
		numericChangeLogs = append(numericChangeLogs, &mysql.PersonalRichCharmLog{
			Uid:         uid,
			Type:        pb.NumericT_Rich,
			BeforeValue: beforeRich,
			AddValue:    int64(richVal),
			CreateTime:  now,
			OrderID:     orderId,
			SourceType:  pb.SourceT_SourceTManual,
		})
	}
	if charmVal > 0 {
		numericChangeLogs = append(numericChangeLogs, &mysql.PersonalRichCharmLog{
			Uid:         uid,
			Type:        pb.NumericT_Charm,
			BeforeValue: beforeCharm,
			AddValue:    int64(charmVal),
			CreateTime:  now,
			OrderID:     orderId,
			SourceType:  pb.SourceT_SourceTManual,
		})
	}
	if err = m.mysqlStore.AddPersonRichOrCharmChangeLog(ctx, now, numericChangeLogs); err != nil {
		log.ErrorWithCtx(ctx, "AddUserNumeric AddPersonRichOrCharmChangeLog err: %s", err.Error())
	}

	// 实际增加财富值
	var realAfterRich uint64
	if isRecordRich {
		realAfterRich = afterRich
	} else {
		realAfterRich = beforeRich // 未增加财富值
	}

	// reload
	_, _ = m.reloadNumeric(ctx, uid)
	m.afterAddNumeric(uid, isRecordRich, beforeRich, realAfterRich, beforeCharm, afterCharm)

	return &pb.AddUserNumericResp{
		FinalRichValue:  realAfterRich,
		FinalCharmValue: afterCharm,
	}, nil
}

func (m *Manager) afterAddNumeric(uid uint32, isRecordRich bool, beforeRich, afterRich, beforeCharm, afterCharm uint64) {
	bgCtx := context.Background()
	go func() {
		// VIP升级
		m.HandleVipLevelChange(bgCtx, uid, beforeRich, afterRich)

		// level change
		isSenderLevelChange := isRecordRich && checkRichOrCharmLevelChange(beforeRich, afterRich)
		isReceiverLevelChange := checkRichOrCharmLevelChange(beforeCharm, afterCharm)

		if isSenderLevelChange || isReceiverLevelChange {
			// sync
			m.syncMissionNumeric(bgCtx, uid)
			// notify
			m.notifyClient(bgCtx, []uint32{uid}, 6)
		}
		// oss 上报财富魅力值变化
		m.ReportDataCenter(bgCtx, uid, uid, time.Now(), isRecordRich, afterRich, afterCharm)
	}()
}

func (m *Manager) GetVipSince(ctx context.Context, req *pb.GetVipSinceReq) (*pb.GetVipSinceResp, error) {
	resp := new(pb.GetVipSinceResp)
	earliestTime := time.Date(2021, 1, 1, 0, 0, 0, 0, time.Local)
	now := time.Now()
	userNumeric, err := m.getOnePersonalNumeric(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddUserNumeric get user numeric err:%s", err.Error())
		return resp, err
	}
	if userNumeric.Rich < config.VipThreshold {
		return resp, nil
	}
	resp.VipLevel = TransVipLevel(userNumeric.Rich)

	vip, err := m.mysqlStore.GetPersonVip(ctx, req.GetUid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetVipSince GetPersonVip err:%s", err.Error())
		return resp, err
	}
	if vip == nil || vip.VipLevel == 0 {
		return resp, nil
	}

	// 2021.01前没有时间记录，统一返回0
	if vip.CreateTime == earliestTime {
		return resp, nil
	}

	if !vip.CreateTime.IsZero() {
		resp.BeVipTime = uint64(vip.CreateTime.Unix())

		if vip.CreateTime.Unix() < now.Unix() {
			resp.BeVipDays = uint32(math.Abs(float64(now.Unix()-vip.CreateTime.Unix()))/86400) + 1
		}
	}

	return resp, nil
}

func (m *Manager) BatchGetPersonalNumeric(ctx context.Context, req *pb.BatchGetPersonalNumericReq) (*pb.BatchGetPersonalNumericResp, error) {
	resp := new(pb.BatchGetPersonalNumericResp)
	resp.NumericList = make([]*pb.PersonalNumeric, 0)
	uidList := utils.CleanRepeatUint32(req.GetUidList())
	if len(uidList) == 0 {
		return resp, nil
	}

	// batch get from cache
	userNumericList, missUid, err := m.cacheClient.BatchGetPersonalNumeric(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetPersonalNumeric from cache err:%s", err.Error())
		return resp, err
	}

	// batch get from mysql
	if len(missUid) > 0 {
		log.WarnWithCtx(ctx, "BatchGetPersonalNumeric from cache miss uid:%v", missUid)
		missUserNumericList, err := m.mysqlStore.BatchGetPersonalNumeric(ctx, missUid)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchGetPersonalNumeric from mysql err:%s", err.Error())
			return resp, err
		}

		foundMissUidMap := make(map[uint32]struct{})
		for _, n := range missUserNumericList {
			foundMissUidMap[n.Uid] = struct{}{}
		}
		for _, uid := range missUid {
			if _, ok := foundMissUidMap[uid]; !ok {
				// 还没有产生财富值的用户也做短缓存
				missUserNumericList = append(missUserNumericList, &pb.PersonalNumeric{Uid: uid})
			}
		}

		userNumericList = append(userNumericList, missUserNumericList...)

		// async set cache
		go func() {
			bgCtx := context.Background()
			log.DebugWithCtx(bgCtx, "BatchGetPersonalNumeric set cache missUserNumericList len:%d", len(missUserNumericList))
			_ = m.cacheClient.BatchSetPersonalNumeric(bgCtx, missUserNumericList)
		}()
	}

	for i, n := range userNumericList {
		userNumericList[i].RichLevel = TransRichOrCharmLevel(pb.NumericT_Rich, n.Rich)
		userNumericList[i].CharmLevel = TransRichOrCharmLevel(pb.NumericT_Charm, n.Charm)
	}

	resp.NumericList = userNumericList
	return resp, nil
}

func (m *Manager) batchGetNumericMap(ctx context.Context, uidList []uint32) (map[uint32]*pb.PersonalNumeric, error) {
	dict := make(map[uint32]*pb.PersonalNumeric)
	if len(uidList) == 0 {
		return dict, nil
	}
	resp, err := m.BatchGetPersonalNumeric(ctx, &pb.BatchGetPersonalNumericReq{UidList: uidList})
	if err != nil {
		log.ErrorWithCtx(ctx, "batchGetNumericMap BatchGetPersonalNumeric err:%s", err.Error())
		return nil, err
	}
	for _, numeric := range resp.GetNumericList() {
		dict[numeric.Uid] = numeric
	}
	return dict, nil
}

// 刷新财富值缓存
func (m *Manager) reloadNumeric(ctx context.Context, uid uint32) (*pb.PersonalNumeric, error) {
	userNumeric, err := m.mysqlStore.GetPersonalNumeric(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "reloadNumeric GetPersonalNumeric err:%s", err.Error())
		return nil, err
	}
	userNumeric.Uid = uid
	_ = m.cacheClient.SetPersonalNumeric(ctx, userNumeric)
	return userNumeric, nil
}

// 批量刷新财富值缓存
func (m *Manager) batchReloadNumeric(ctx context.Context, uidList []uint32) (map[uint32]*pb.PersonalNumeric, error) {
	_ = m.cacheClient.BatchRemovePersonalNumeric(ctx, uidList)
	return m.batchGetNumericMap(ctx, uidList)
}

func (m *Manager) UseRichCard(ctx context.Context, req *pb.UseRichCardReq) (*pb.UseRichCardResp, error) {
	log.InfoWithCtx(ctx, "UseRichCard req:%+v", req)
	resp := new(pb.UseRichCardResp)
	if req.GetUid() == 0 || req.GetAddRich() == 0 || req.GetOrderId() == "" {
		return resp, protocol.NewExactServerError(codes.OK, status.ErrParam)
	}
	uid := req.GetUid()

	// 预执行
	if req.GetOpt() == pb.UseRichCardReq_OPT_PREPROCESS {
		if req.GetUseCount() == 0 || req.GetCardName() == "" {
			log.ErrorWithCtx(ctx, "UseRichCard param err:%+v", req)
			return resp, protocol.NewExactServerError(codes.OK, status.ErrParam)
		}
		channelId, sErr := m.channelOlCli.GetUserChannelId(ctx, uid, uid)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "UseRichCard GetUserChannelId err:%s", sErr.Error())
			return resp, sErr
		}
		if err := m.mysqlStore.PreUseRichCard(ctx, &mysql.RichCardOrder{
			UID:       req.GetUid(),
			ChannelID: channelId,
			OrderID:   req.GetOrderId(),
			AddRich:   req.GetAddRich(),
			CardID:    req.GetCardId(),
			UseCount:  req.GetUseCount(),
			CardName:  req.GetCardName(),
		}); err != nil {
			log.ErrorWithCtx(ctx, "UseRichCard PreUseRichCard err:%s", err.Error())
			return resp, err
		}
		return resp, nil
	}

	richCard, err := m.mysqlStore.GetRichCardOrder(ctx, req.GetOrderId())
	if err != nil {
		log.ErrorWithCtx(ctx, "UseRichCard GetRichCardOrder err:%s", err.Error())
		return resp, err
	}
	if richCard.IsFinished() {
		log.ErrorWithCtx(ctx, "UseRichCard rich card order is finished, order_id:%s", req.GetOrderId())
		return resp, protocol.NewExactServerError(codes.OK, status.ErrNumericsRepeatOp)
	}

	// 回滚
	if req.GetOpt() == pb.UseRichCardReq_OPT_ROLLBACK {
		if err = m.mysqlStore.CancelUseRichCard(ctx, req.GetOrderId()); err != nil {
			log.ErrorWithCtx(ctx, "UseRichCard cancel use rich card err:%s", err.Error())
			return resp, err
		}
		return resp, nil
	}

	// 确保以下是确认执行的逻辑
	if req.GetOpt() != pb.UseRichCardReq_OPT_PROCESS {
		return resp, protocol.NewExactServerError(codes.OK, status.ErrParam)
	}

	originUserNumeric, err := m.getOnePersonalNumeric(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "UseRichCard getOnePersonalNumeric err:%s", err.Error())
		return resp, err
	}

	isRecordRich := m.IsRecordRich(ctx, uid)
	if !isRecordRich {
		log.ErrorWithCtx(ctx, "UseRichCard user is not record rich, uid:%d", uid)
		// 财富值开关已经在logic层检查，这里仅记录一条log便于排查
	}

	if err = m.mysqlStore.CertainUseRichCard(ctx, richCard, originUserNumeric); err != nil {
		log.ErrorWithCtx(ctx, "UseRichCard CertainUseRichCard err:%s", err.Error())
		return resp, err
	}

	// reload
	newUserNumeric, _ := m.reloadNumeric(ctx, uid)
	beforeRich := originUserNumeric.GetRich()
	afterRich := newUserNumeric.GetRich()

	m.afterAddNumeric(uid, isRecordRich, beforeRich, afterRich, 0, 0)

	go func() {
		bgCtx := context.Background()
		// channel push
		FromDesc := fmt.Sprintf("我 使用%d张%s", req.GetUseCount(), req.GetCardName())
		pushMsg := &numericlogic.RichCharmChangeMsg{
			FromType:    numericlogic.FromType_RichCardChange,
			ChangeType:  numericlogic.ChangeType_Rich,
			Value:       uint32(richCard.AddRich),
			Uid:         uid,
			OriginValue: uint32(richCard.AddRich),
			FromDesc:    FromDesc,
		}
		log.InfoWithCtx(bgCtx, "UseRichCard channel_id:%d, pushMsg:%+v", richCard.ChannelID, pushMsg)
		pushMsgBytes, _ := proto.Marshal(pushMsg)
		_ = m.SendChannelReliable1v1Msg(bgCtx, richCard.ChannelID, uid, pushMsgBytes)
	}()

	resp.AddRich = req.GetAddRich()
	resp.FinalRich = afterRich
	return resp, nil
}

func (m *Manager) SendChannelReliable1v1Msg(ctx context.Context, channelId, targetUid uint32, msgBin []byte) error {
	if channelId == 0 || targetUid == 0 {
		log.ErrorWithCtx(ctx, "SendChannelReliable1v1Msg err channelId:%v,targetUid:%d", channelId, targetUid)
		return nil
	}

	chnResp, err := m.channelCli.GetChannelSimpleInfo(ctx, targetUid, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendChannelReliable1v1Msg GetChannelSimpleInfo err:%s", err)
		return err
	}

	pMsg := &channelMsg.PushMessage{
		ChannelId:   channelId,
		ChannelType: chnResp.GetChannelType(),
		OperatorUid: targetUid,
		MsgType:     uint32(channelGA.ChannelMsgType_RICH_CHARM_CHANGE),
		PBContent:   msgBin,
	}

	newCtx, newCancel := grpc.NewContextWithInfoTimeout(ctx, 2*time.Second)
	defer newCancel()
	if err = m.channelMsgSender.SingleCast(newCtx, pMsg, []uint32{targetUid}, nil); err != nil {
		log.ErrorWithCtx(newCtx, "SendChannelReliable1v1Msg SingleCast err:%s", err)
	}

	return nil
}
