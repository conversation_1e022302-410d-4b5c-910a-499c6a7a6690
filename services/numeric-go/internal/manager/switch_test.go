package manager

import (
	"bou.ke/monkey"
	"context"
	"github.com/golang/mock/gomock"
	"github.com/panjf2000/ants"
	"golang.52tt.com/clients/account"
	anchorcontract_go "golang.52tt.com/clients/anchorcontract-go"
	"golang.52tt.com/clients/channel"
	channel_msg_express "golang.52tt.com/clients/channel-msg-express"
	channel_personalization "golang.52tt.com/clients/channel-personalization"
	"golang.52tt.com/clients/channelol"
	"golang.52tt.com/clients/guild"
	missionTL "golang.52tt.com/clients/missiontimeline"
	mockAnchorContractGo "golang.52tt.com/clients/mocks/anchorcontract-go"
	present_extra_conf "golang.52tt.com/clients/present-extra-conf"
	public_notice "golang.52tt.com/clients/public-notice"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	reconcile_present "golang.52tt.com/clients/reconcile-v2-svr/reconcile-present"
	"golang.52tt.com/clients/seqgen/v2"
	user_profile_api "golang.52tt.com/clients/user-profile-api"
	channel_msg "golang.52tt.com/pkg/channel-msg"
	anchorcontractpb "golang.52tt.com/protocol/services/anchorcontract-go"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkaanchorcontract"
	pb "golang.52tt.com/protocol/services/numeric-go"
	"golang.52tt.com/services/numeric-go/internal/cache"
	"golang.52tt.com/services/numeric-go/internal/common"
	"golang.52tt.com/services/numeric-go/internal/config"
	mocks2 "golang.52tt.com/services/numeric-go/internal/mocks"
	"golang.52tt.com/services/numeric-go/internal/mysql"
	"reflect"
	"testing"
	"time"
)

func TestManager_GetUserRichSwitch(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)

	mockStore := mocks2.NewMockIStore(ctl)
	mockCache := mocks2.NewMockINumericGoCache(ctl)

	resp := &pb.GetUserRichSwitchResp{
		Enable: true,
	}

	ctx := context.Background()
	gomock.InOrder(
		mockCache.EXPECT().GetRichSwitch(ctx, uid).Return(true, nil),
	)

	type fields struct {
		cacheClient          cache.INumericGoCache
		mysqlStore           mysql.IStore
		anchorContractClient anchorcontract_go.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.GetUserRichSwitchReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetUserRichSwitchResp
		wantErr bool
	}{
		{name: "GetUserRichSwitch",
			fields: fields{
				mysqlStore:  mockStore,
				cacheClient: mockCache,
			},
			args: args{ctx: context.Background(), req: &pb.GetUserRichSwitchReq{
				Uid: uid,
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheClient:          tt.fields.cacheClient,
				mysqlStore:           tt.fields.mysqlStore,
				anchorContractClient: tt.fields.anchorContractClient,
			}
			got, err := m.GetUserRichSwitch(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserRichSwitch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserRichSwitch() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_HandleAnchorContractCancel(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	guildId := uint32(123)

	mockStore := mocks2.NewMockIStore(ctl)
	mockCache := mocks2.NewMockINumericGoCache(ctl)
	mockContract := mockAnchorContractGo.NewMockIClient(ctl)

	ctx := context.Background()
	gomock.InOrder(
		mockCache.EXPECT().GetRichSwitch(ctx, uid).Return(true, nil),
		mockCache.EXPECT().RichSwitchDisable(ctx, uid).Return(nil),
		mockStore.EXPECT().CreateSwitchOpLogV2(ctx, &mysql.PersonalRichSwitchLog{
			UID:        uid,
			GuildID:    guildId,
			ChangeDesc: "解约解锁",
			Op:         common.SwitchOpDisable,
			ChangeType: pb.RichSwitchChangeType_RICH_SWITCH_CHANGE_TYPE_CONTRACT_CANCEL,
		}).Return(nil),
	)

	type fields struct {
		cacheClient          cache.INumericGoCache
		mysqlStore           mysql.IStore
		anchorContractClient anchorcontract_go.IClient
	}
	type args struct {
		ctx context.Context
		e   *kafkaanchorcontract.AnchorContractEvent
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{name: "handleAnchorContractCancel",
			fields: fields{
				mysqlStore:           mockStore,
				cacheClient:          mockCache,
				anchorContractClient: mockContract,
			},
			args: args{ctx: ctx, e: &kafkaanchorcontract.AnchorContractEvent{
				EventType: uint32(kafkaanchorcontract.EVENT_TYPE_EVENT_CANCEL_CONTRACT),
				Uid:       uid,
				GuildId:   guildId,
			}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheClient:          tt.fields.cacheClient,
				mysqlStore:           tt.fields.mysqlStore,
				anchorContractClient: tt.fields.anchorContractClient,
			}
			if err := m.handleAnchorContractCancel(tt.args.ctx, tt.args.e); (err != nil) != tt.wantErr {
				t.Errorf("handleAnchorContractCancel() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
	time.Sleep(1 * time.Second)
}

func TestManager_SetUserRichSwitch(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	uid := uint32(123456)
	guildId := uint32(123)

	mockStore := mocks2.NewMockIStore(ctl)
	mockCache := mocks2.NewMockINumericGoCache(ctl)
	mockContract := mockAnchorContractGo.NewMockIClient(ctl)

	resp := &pb.SetUserRichSwitchResp{}

	ctx := context.Background()
	gomock.InOrder(
		mockContract.EXPECT().GetUserContract(ctx, uid, uid).
			Return(&anchorcontractpb.GetUserContractResp{
				Contract: &anchorcontractpb.ContractInfo{
					GuildId: guildId,
				},
			}, nil),
		mockCache.EXPECT().RichSwitchEnable(ctx, uid).Return(nil),
		mockStore.EXPECT().CreateSwitchOpLogV2(ctx, &mysql.PersonalRichSwitchLog{
			UID:        uid,
			GuildID:    guildId,
			ChangeDesc: "用户操作",
			Op:         common.SwitchOpEnable,
			ChangeType: pb.RichSwitchChangeType_RICH_SWITCH_CHANGE_TYPE_MANUAL,
		}).Return(nil),
	)

	type fields struct {
		cacheClient          cache.INumericGoCache
		mysqlStore           mysql.IStore
		anchorContractClient anchorcontract_go.IClient
	}
	type args struct {
		ctx context.Context
		req *pb.SetUserRichSwitchReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.SetUserRichSwitchResp
		wantErr bool
	}{
		{name: "SetUserRichSwitch-enable",
			fields: fields{
				mysqlStore:           mockStore,
				cacheClient:          mockCache,
				anchorContractClient: mockContract,
			},
			args: args{ctx: ctx, req: &pb.SetUserRichSwitchReq{
				Uid:    uid,
				Enable: true,
			}},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheClient:          tt.fields.cacheClient,
				mysqlStore:           tt.fields.mysqlStore,
				anchorContractClient: tt.fields.anchorContractClient,
			}
			got, err := m.SetUserRichSwitch(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetUserRichSwitch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SetUserRichSwitch() got = %v, want %v", got, tt.want)
			}
		})
	}
	time.Sleep(1 * time.Second)
}

func TestManager_HandleAnchorContractSign(t *testing.T) {

	ctx := context.Background()
	uid := uint32(123456)
	gid := uint32(123)
	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "GetRichSwitch", func(c *cache.NumericGoCache,
		ctx context.Context, uid uint32) (bool, error) {
		return false, nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericGoCache{}), "RichSwitchEnable", func(c *cache.NumericGoCache,
		ctx context.Context, uid uint32) error {
		return nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "GetLastSwitchOpLog", func(s *mysql.Store,
		ctx context.Context, uid uint32) (*mysql.PersonalRichSwitchLog, error) {
		return &mysql.PersonalRichSwitchLog{
			UID:        uid,
			Op:         common.SwitchOpDisable,
			ChangeType: pb.RichSwitchChangeType_RICH_SWITCH_CHANGE_TYPE_CONTRACT_CANCEL,
		}, nil
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(&mysql.Store{}), "CreateSwitchOpLogV2", func(s *mysql.Store,
		ctx context.Context, record *mysql.PersonalRichSwitchLog) error {
		return nil
	})

	type fields struct {
		cacheClient          cache.INumericGoCache
		mysqlStore           mysql.IStore
		anchorContractClient anchorcontract_go.IClient
		guildClient          guild.IClient
		accountClient        account.IClient
		missionTLCli         missionTL.IClient
		pushCli              PushNotification.IClient
		seqGenCli            seqgen.IClient
		channelOlCli         channelol.IClient
		channelCli           channel.IClient
		channelMsgSender     channel_msg.ISender
		taskPool             *ants.Pool
		configCenter         config.IConfigCenter
		reconcilePresentCli  reconcile_present.IClient
		presentExtraCli      present_extra_conf.IClient
		personalizationCli   channel_personalization.IClient
		expressCli           channel_msg_express.IClient
		userProfileCli       user_profile_api.IClient
		publicNoticeCli      public_notice.IClient
		seqgenCli            seqgen.IClient
	}
	type args struct {
		ctx context.Context
		e   *kafkaanchorcontract.AnchorContractEvent
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "handleAnchorContractSign",
			fields: fields{
				cacheClient:          &cache.NumericGoCache{},
				mysqlStore:           &mysql.Store{},
				anchorContractClient: &anchorcontract_go.Client{},
				taskPool:             &ants.Pool{},
				configCenter:         &config.ConfigCenter{},
			},
			args: args{
				ctx: ctx,
				e: &kafkaanchorcontract.AnchorContractEvent{
					EventType: 0,
					Uid:       uid,
					GuildId:   gid,
					EventTime: 0,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheClient:          tt.fields.cacheClient,
				mysqlStore:           tt.fields.mysqlStore,
				anchorContractClient: tt.fields.anchorContractClient,
				guildClient:          tt.fields.guildClient,
				accountClient:        tt.fields.accountClient,
				missionTLCli:         tt.fields.missionTLCli,
				pushCli:              tt.fields.pushCli,
				seqGenCli:            tt.fields.seqGenCli,
				channelOlCli:         tt.fields.channelOlCli,
				channelCli:           tt.fields.channelCli,
				channelMsgSender:     tt.fields.channelMsgSender,
				taskPool:             tt.fields.taskPool,
				configCenter:         tt.fields.configCenter,
				reconcilePresentCli:  tt.fields.reconcilePresentCli,
				presentExtraCli:      tt.fields.presentExtraCli,
				personalizationCli:   tt.fields.personalizationCli,
				expressCli:           tt.fields.expressCli,
				userProfileCli:       tt.fields.userProfileCli,
				publicNoticeCli:      tt.fields.publicNoticeCli,
				seqgenCli:            tt.fields.seqgenCli,
			}
			if err := m.handleAnchorContractSign(tt.args.ctx, tt.args.e); (err != nil) != tt.wantErr {
				t.Errorf("handleAnchorContractSign() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
