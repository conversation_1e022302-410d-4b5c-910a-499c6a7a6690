package manager

import (
	"context"
	"errors"
	"fmt"
	"math"
	"time"

	"golang.52tt.com/services/numeric-go/internal/common"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	pushclient "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app"
	channelPB_ "golang.52tt.com/protocol/app/channel"
	pushPb "golang.52tt.com/protocol/app/push"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	channelPB "golang.52tt.com/protocol/services/channelsvr"
	pb "golang.52tt.com/protocol/services/numeric-go"
	"golang.52tt.com/protocol/services/presentextraconf"
	publicNoticePb "golang.52tt.com/protocol/services/public-notice"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
)

const (
	// DayRichCharmLimit 红钻送礼每日限制
	DayRichCharmLimit uint64 = 10000
	DataCenterBizID          = "************"
)

// RecordSendGiftEvent 废弃，kafka处理
func (m *Manager) RecordSendGiftEvent(ctx context.Context, req *pb.RecordSendGiftEventReq) (*pb.RecordSendGiftEventResp, error) {
	resp := new(pb.RecordSendGiftEventResp)
	_, _ = ctx, req
	return resp, errors.New("api deprecated")
}

// checkDailyLimit 检查红钻礼物每日限制
func (m *Manager) checkDailyLimit(ctx context.Context, t pb.NumericT, uid uint32, beforeValue, addVal uint64) (newAddVal uint64) {
	now := time.Now()
	newAddVal = addVal
	beforeLevel := level(beforeValue)
	afterLevel := level(beforeValue + addVal)
	actualLevel3 := big2ActualLevel(3) // 大级3对应的小级，即100
	if beforeLevel < actualLevel3 && afterLevel < actualLevel3 {
		// 魅力等级小于第3大级别 不做限制
		return
	}
	// 防止直接在3级以下时 直接一次性送大量的红钻礼物 一次性升很多级
	if beforeLevel < actualLevel3 && afterLevel > actualLevel3 {
		var mustAddValue, maybeAddValue uint64
		var valueLevel3 = actualLevel3 * 1000
		if valueLevel3 > beforeLevel {
			mustAddValue = valueLevel3 - beforeValue
		}
		if addVal > mustAddValue {
			maybeAddValue = addVal - mustAddValue
		}
		if maybeAddValue > DayRichCharmLimit {
			maybeAddValue = DayRichCharmLimit
		}
		newAddVal = mustAddValue + maybeAddValue
		if err := m.cacheClient.SetDailyLimit(ctx, now, t, uid, maybeAddValue); err != nil {
			log.ErrorWithCtx(ctx, "checkDailyLimit SetDailyRichLimit err: %+v", err)
		}
		return
	}

	todayAlreadyValue, err := m.cacheClient.GetDailyLimit(ctx, now, t, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkDailyLimit GetDailyRichLimit err: %+v", err)
		return
	}
	if todayAlreadyValue == 0 {
		if err = m.cacheClient.SetDailyLimit(ctx, now, t, uid, 0); err != nil {
			log.ErrorWithCtx(ctx, "checkDailyLimit SetDailyRichLimit err: %+v", err)
		}
	}
	if todayAlreadyValue >= DayRichCharmLimit {
		newAddVal = 0
		return
	}

	// 当日增加后的值，是否超出限制
	todayWillValue := todayAlreadyValue + addVal
	if todayWillValue > DayRichCharmLimit {
		newAddVal = DayRichCharmLimit - todayAlreadyValue
	}
	if err = m.cacheClient.IncrDailyLimit(ctx, now, t, uid, newAddVal); err != nil {
		log.ErrorWithCtx(ctx, "checkDailyLimit IncrDailyRichLimit err: %+v", err)
	}

	log.Debugf("checkDailyLimit type:%v, before_day_limit:%v, actual_add_val:%v", t, todayAlreadyValue, newAddVal)
	return
}

// checkGiftLimit 检查礼物财富值限制
func (m *Manager) checkGiftLimit(uid, giftId uint32, richVal uint64, t time.Time) (uint64, bool) {
	isCheckRichDailyLimit := true
	newRichVal := richVal
	// 转转礼物不收限制
	if giftLimitConf, ok := m.configCenter.GetGiftLimit(giftId); ok {
		nowUnix := t.Unix()
		if giftLimitConf.BeginTime <= nowUnix && giftLimitConf.EndTime >= nowUnix {
			newRichVal = uint64(float64(richVal) * (float64(giftLimitConf.Ratio) / 100.0))
			isCheckRichDailyLimit = false
			log.Infof("checkGiftLimit unLimit uid:%d, gift_id:%d, add_rich:%d", uid, giftId, newRichVal)
		}
	}
	return newRichVal, isCheckRichDailyLimit
}

// checkRichOrCharmLevelChange 检测判断用户的土豪魅力的UI等级是否有变化
func checkRichOrCharmLevelChange(beforeValue, afterValue uint64) bool {
	// from C++
	// 土豪(魅力)的服务器等级 = 土豪(魅力)值 / 1000
	// 然后服务器等级的总位数为UI显示的大等级 最高位为UI显示的小等级
	// 比如 serverLevel = 0 即用户当前UI等级为特殊的0
	// 比如 serverLevel = 1 即用户当前UI等级为1-(1)
	// 比如 serverLevel = 10 即用户当前UI等级为2-(1.0)
	// 比如 serverLevel = 3122 即用户当前UI等级为4-(3.1.2.2)

	beforeServerLevel := level(beforeValue)
	afterServerLevel := level(afterValue)
	if beforeServerLevel == afterServerLevel {
		return false // 无变化
	} else {
		return true
	}
}

// 财富魅力服务器等级 pb.Level.ServerLevel
func level(val uint64) uint64 {
	return val / 1000
}

// 大级转小级 1->1000, 10->10000, , 100->100000,
func big2ActualLevel(level uint64) uint64 {
	if level == 0 {
		return 0
	}
	return uint64(math.Pow(10, float64(level-1)))
}

// BatchRecordSendGiftEvent 红钻送礼财富值处理
func (m *Manager) BatchRecordSendGiftEvent(ctx context.Context, req *pb.BatchRecordSendGiftEventReq) (*pb.BatchRecordSendGiftEventResp, error) {
	channelID := req.GetChannelId()
	channelGuildID := req.GetChannelGuild()
	priceType := req.GetPriceType()
	giftID := req.GetGiftId()
	senderInfo := req.GiverUserInfo
	sender := senderInfo.GetUid()
	richVal := senderInfo.GetAddValue()
	now := time.Now()
	orderId := req.GetOrderId()

	// to_do 后面将红钻送礼上限的处理提出来放前面统一处理

	isRecordRich := m.IsRecordRich(ctx, sender) // 签约用户是否记录财富值

	// 增加送礼人财富值
	senderGN := &common.GeneralNumeric{
		SourceType:     pb.SourceT_SourceTPresentRedDiamond,
		Sender:         sender,
		GiftId:         giftID,
		PriceType:      priceType,
		OrderId:        orderId,
		AddRich:        richVal,
		ChannelId:      channelID,
		ChannelGuildId: channelGuildID,
		SendTime:       now,
		IsRecordRich:   isRecordRich,
	}
	senderInfoResp, err := m.addRichValue(ctx, senderGN)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchRecordSendGiftEvent addRichValue err:%v", err)
	}

	// 增加收礼人魅力值
	receiverInfos := make([]*pb.UserGiftEventInfo, 0)
	for _, giverInfo := range req.ReceiverUserInfoList {
		receiver := giverInfo.GetUid()

		receiverGN := *senderGN
		receiverGN.Receiver = receiver
		receiverGN.AddCharm = giverInfo.GetAddValue()
		receiverGN.ReceiverGuildId = giverInfo.GetGuildId()

		receiverInfoResp, err := m.addCharmValue(ctx, &receiverGN)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchRecordSendGiftEvent addCharmValue err:%v", err)
			continue
		}
		receiverInfos = append(receiverInfos, receiverInfoResp)
	}

	return &pb.BatchRecordSendGiftEventResp{
		GiverUserInfo:        senderInfoResp,
		ReceiverUserInfoList: receiverInfos,
	}, nil
}

// addRichValue 全麦送礼增加财富值
func (m *Manager) addRichValue(ctx context.Context, gn *common.GeneralNumeric) (
	*pb.UserGiftEventInfo, error) {
	uid := gn.Sender
	richVal := gn.AddRich
	now := gn.SendTime
	// 送礼前的财富值
	userNumeric, err := m.getOnePersonalNumeric(ctx, gn.Sender)
	if err != nil {
		log.ErrorWithCtx(ctx, "addRichValue get user numeric err:%+v", err)
		return nil, err
	}
	beforeRich := userNumeric.GetRich() // 变更前的财富值
	afterRich := beforeRich + richVal   // 变更后的财富值
	isRecordRich := gn.IsRecordRich

	log.InfoWithCtx(ctx, "addRichValue: uid = %d, add_rich = %d, before_rich = %d, isRecordRich = %v",
		uid, richVal, beforeRich, isRecordRich)

	// 红钻礼物检查当日送礼财富值魅力值上限
	if gn.PriceType == uint32(app.PRESENT_PRICE_TYPE_PRESENT_PRICE_RED_DIAMOND) {
		var isCheckRichDailyLimit bool
		// 检查礼物限制
		richVal, isCheckRichDailyLimit = m.checkGiftLimit(uid, gn.GiftId, richVal, now)
		if isCheckRichDailyLimit {
			// 检查每日最大红钻限制，当存在礼物限制时无当日上限
			richVal = m.checkDailyLimit(ctx, pb.NumericT_Rich, uid, beforeRich, richVal)
		}
		// 受当日限制影响，新增值可能变化，更新送礼后的值
		afterRich = beforeRich + richVal
	}

	// 财富等级变化
	levelChange := false
	if isRecordRich && checkRichOrCharmLevelChange(beforeRich, afterRich) {
		levelChange = true
	}

	userInfo := &pb.UserGiftEventInfo{
		Uid:         uid,
		BeforeValue: beforeRich,
		LevelChange: levelChange,
	}

	if isRecordRich {
		userInfo.AddValue = richVal
		userInfo.FinalValue = afterRich
	} else {
		userInfo.FinalValue = beforeRich // 未增加财富值
		return userInfo, nil
	}

	go func() {
		bgCtx := context.Background()

		if !isRecordRich {
			return
		}

		gn.BeforeRich = beforeRich
		gn.AddRich = richVal
		if err = m.mysqlStore.RecordGiftEventRichChangeV2(bgCtx, gn); err != nil {
			log.ErrorWithCtx(bgCtx, "addRichValue RecordGiftEventRichChange err:%v", err)
		}

		// 重载缓存，避免缓存与数据库长期不一致的情况
		if _, err = m.reloadNumeric(bgCtx, uid); err != nil {
			log.ErrorWithCtx(bgCtx, "addRichValue reloadNumeric err: %+v, uid: %d, after_rich: %d", err, uid, afterRich)
			_ = m.cacheClient.DelPersonalNumeric(bgCtx, uid)
		}

		// 月度榜单处理
		m.PersonRichRankHandle(bgCtx, now, uid, richVal)

		m.afterAddNumeric(uid, isRecordRich, beforeRich, afterRich, 0, 0)

		log.DebugWithCtx(bgCtx, "addRichValue userInfo:%+v", userInfo)
	}()

	return userInfo, nil
}

// addCharmValue 全麦送礼增加魅力值
func (m *Manager) addCharmValue(ctx context.Context, gn *common.GeneralNumeric) (
	*pb.UserGiftEventInfo, error) {
	now := gn.SendTime
	uid := gn.Receiver
	charmVal := gn.AddCharm
	// 送礼前的财富值
	userNumeric, err := m.getOnePersonalNumeric(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "addCharmValue get user numeric err:%+v", err)
		return nil, err
	}
	beforeCharm := userNumeric.GetCharm() // 变更前的魅力值
	afterCharm := beforeCharm + charmVal  // 变更后的魅力值

	log.InfoWithCtx(ctx, "addCharmValue: uid = %d, add_charm = %d, before_charm = %d",
		uid, charmVal, beforeCharm)

	// 红钻礼物检查当日送礼财富值魅力值上限
	if gn.PriceType == uint32(app.PRESENT_PRICE_TYPE_PRESENT_PRICE_RED_DIAMOND) {
		charmVal = m.checkDailyLimit(ctx, pb.NumericT_Charm, uid, beforeCharm, charmVal)
		// 受当日限制影响，新增值可能变化，更新送礼后的值
		afterCharm = beforeCharm + charmVal
	}

	levelChange := checkRichOrCharmLevelChange(beforeCharm, afterCharm)

	userInfo := &pb.UserGiftEventInfo{
		Uid:         uid,
		BeforeValue: beforeCharm,
		AddValue:    charmVal,
		FinalValue:  afterCharm,
		LevelChange: levelChange,
	}

	go func() {
		bgCtx := context.Background()

		gn.BeforeCharm = beforeCharm
		gn.AddCharm = charmVal
		if err = m.mysqlStore.RecordGiftEventCharmChangeV2(bgCtx, gn); err != nil {
			log.ErrorWithCtx(bgCtx, "addCharmValue RecordGiftEventRichChange err:%v", err)
		}

		// 重载缓存，避免缓存与数据库长期不一致的情况
		if _, err = m.reloadNumeric(bgCtx, uid); err != nil {
			log.ErrorWithCtx(bgCtx, "addCharmValue reloadNumeric err: %+v, uid: %d, after_charm: %d", err, uid, afterCharm)
			_ = m.cacheClient.DelPersonalNumeric(bgCtx, uid)
		}

		// 月度榜单处理
		m.PersonCharmRankHandle(bgCtx, now, uid, charmVal)

		m.afterAddNumeric(uid, false, 0, 0, beforeCharm, afterCharm)

		log.DebugWithCtx(bgCtx, "addCharmValue userInfo:%+v", userInfo)
	}()

	return userInfo, nil
}

func (m *Manager) TriggerRichLevelBreakingNews(ctx context.Context, req *pb.TriggerRichLevelBreakingNewsReq) (*pb.TriggerRichLevelBreakingNewsResp, error) {
	resp := &pb.TriggerRichLevelBreakingNewsResp{}
	sender := req.GetUid()
	channelId := req.GetChannelId()
	afterRich := req.GetAfterRich()

	log.InfoWithCtx(ctx, "TriggerRichLevelBreakingNews sender:%d, channelId:%d, afterRich:%d", sender, channelId, afterRich)

	if sender == 0 {
		log.WarnWithCtx(ctx, "TriggerRichLevelBreakingNews err: sender uid is 0")
		return resp, fmt.Errorf("sender uid is 0")
	}

	// 财富值必须是kw整数，否则不触发
	kw := uint64(********)
	if afterRich%kw != 0 {
		log.WarnWithCtx(ctx, "TriggerRichLevelBreakingNews afterRich is not kw, afterRich:%d", afterRich)
		return resp, fmt.Errorf("rich is not kw")
	}

	var err error
	var userInfoMap map[uint32]*accountPB.UserResp
	var channelInfo *channelPB.ChannelSimpleInfo

	userInfoMap, err = m.accountClient.BatGetUserByUid(ctx, sender)
	if err != nil {
		log.ErrorWithCtx(ctx, "TriggerRichLevelBreakingNews BatGetUserByUid err:%s", err)
		return resp, err
	}
	if channelId > 0 {
		channelInfo, err = m.channelCli.GetChannelSimpleInfo(ctx, 0, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "TriggerRichLevelBreakingNews GetChannelSimpleInfo err:%s", err)
			return resp, err
		}
	}

	beforeRich := afterRich - 1
	beforeRichLv := TransRichOrCharmLevel(pb.NumericT_Rich, beforeRich)
	afterRichLv := TransRichOrCharmLevel(pb.NumericT_Rich, afterRich)

	err = m.sendRichLevelBreakingNews(ctx, sender, channelId, userInfoMap, channelInfo, beforeRichLv, afterRichLv)
	if err != nil {
		log.ErrorWithCtx(ctx, "TriggerRichLevelBreakingNews sendRichLevelBreakingNews err:%s", err)
		return resp, err
	}

	return resp, nil
}

// sendRichLevelBreakingNews 发送财富等级升级全服公告
func (m *Manager) sendRichLevelBreakingNews(ctx context.Context, sender uint32, channelId uint32, userInfoMap map[uint32]*accountPB.UserResp, channelInfo *channelPB.ChannelSimpleInfo, beforeLv, afterLv *pb.Level) error {
	// 检查是否是财富大事件
	isBreaking, richLevelName := checkIsBreakNews(ctx, beforeLv.ServerLevel, afterLv.ServerLevel)
	if !isBreaking {
		return fmt.Errorf("not breaking")
	}
	log.InfoWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate channel_id:%d uid:%d notify RichLevelUpgrade News: %s",
		channelId, sender, richLevelName)

	// ProcRichUpgradeBreakingNews
	BreakingNewsMessage := &publicNoticePb.CommonBreakingNewsV3{
		FromUid: sender,
		FromUserInfo: &publicNoticePb.UserInfo{
			Nick:    userInfoMap[sender].GetNickname(),
			Account: userInfoMap[sender].GetUsername(),
		},
		ChannelId: channelId,
		ChannelInfo: &publicNoticePb.ChannelInfo{
			ChannelType:      channelInfo.GetChannelType(),
			ChannelName:      channelInfo.GetName(),
			ChannelBindid:    channelInfo.GetBindId(),
			ChannelDisplayid: channelInfo.GetDisplayId(),
		},
		RichLevel: uint32(afterLv.ServerLevel),
		BreakingNewsBaseOpt: &publicNoticePb.CommBreakingNewsBaseOpt{
			TriggerType:   uint32(pushPb.CommBreakingNewsBaseOpt_HERO_UPGRADING),
			RollingCount:  2,
			RollingTime:   10,
			AnnounceScope: uint32(pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL + pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL),
			JumpType:      uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_ClICK_OUTSIDE), JumpPosition: uint32(pushPb.CommBreakingNewsBaseOpt_JUMP_TO_CHANNEL_CLICK),
			AnnouncePosition: uint32(pushPb.CommBreakingNewsBaseOpt_UPPER),
		},
	}
	kingLevel := uint64(100000)            // 至尊帝皇
	universityKingLevel := uint64(1000000) // 永恒帝尊
	if afterLv.ServerLevel >= universityKingLevel {
		BreakingNewsMessage.BreakingNewsBaseOpt.TriggerType = uint32(pushPb.CommBreakingNewsBaseOpt_UNIVERSITY_EMPEROR_UPGRADING)
		BreakingNewsMessage.NewsContent = "财富等级升级为【永恒帝尊】"
	} else if afterLv.ServerLevel >= kingLevel {
		BreakingNewsMessage.BreakingNewsBaseOpt.TriggerType = uint32(pushPb.CommBreakingNewsBaseOpt_EMPEROR_UPGRADING)
	} else {
		log.WarnWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate ignore rich level %d", afterLv.ServerLevel)
	}

	breakingReq := &publicNoticePb.PushBreakingNewsReq{
		CommonBreakingNews: BreakingNewsMessage,
	}

	_, pErr := m.publicNoticeCli.PushBreakingNews(ctx, breakingReq)
	if pErr != nil {
		log.ErrorWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate PushBreakingNews err:%s. uid:%d, channel_id:%d", pErr, sender, channelId)
		return fmt.Errorf("PushBreakingNews err:%s. uid:%d, channel_id:%d", pErr, sender, channelId)
	} else {
		log.InfoWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate PushBreakingNews success. uid:%v, channel_id:%v, rich:%v", sender, channelId, afterLv)
	}
	return nil
}

func (m *Manager) broadcastUserRichOrCharmLevelUpdate(ctx context.Context, gn *common.GeneralNumeric) {
	if gn == nil || (!gn.IsCharmLevelChange && !gn.IsRichLevelChange) {
		return
	}

	sender, receiver, channelId := gn.Sender, gn.Receiver, gn.ChannelId

	userInfoMap, err := m.accountClient.BatGetUserByUid(ctx, sender, receiver)
	if err != nil {
		log.ErrorWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate BatGetUserByUid err:%s, uid:%d, receiver:%d", err, sender, receiver)
		return
	}

	channelInfo, err := m.channelCli.GetChannelSimpleInfo(ctx, 0, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate GetChannelSimpleInfo err:%s, uid:%d, receiver:%d, channel_id:%d", err, sender, receiver, channelId)
		return
	}

	// 神秘人
	ukwMap := make(map[uint32]*app.UserProfile)
	switch channelPB_.ChannelType(channelInfo.GetChannelType()) {
	case channelPB_.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE,
		channelPB_.ChannelType_RADIO_LIVE_CHANNEL_TYPE,
		channelPB_.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE:
		ukwProfileMap, sErr := m.userProfileCli.BatchGetUserProfileV2(ctx, []uint32{sender, receiver}, true)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate BatchGetUserProfileV2 err:%s, uid:%d", sErr, sender)
		}
		ukwMap = ukwProfileMap
	default:
		for uid, user := range userInfoMap {
			up := &app.UserProfile{
				Uid:          uid,
				Account:      user.GetUsername(),
				Nickname:     user.GetNickname(),
				AccountAlias: user.GetAlias(),
				Sex:          uint32(user.GetSex()),
			}
			ukwMap[uid] = up
		}
	}

	// rich level upgrade
	func() {
		beforeLv := TransRichOrCharmLevel(pb.NumericT_Rich, gn.BeforeRich)
		afterLv := TransRichOrCharmLevel(pb.NumericT_Rich, gn.AfterRich)
		isChange := beforeLv.ServerLevel != afterLv.ServerLevel

		if !gn.IsRecordRich || !isChange {
			return
		}

		// 检查跨千万
		kw := uint64(********)
		beforeNkw := gn.BeforeRich / kw
		afterNkw := gn.AfterRich / kw

		log.InfoWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate sender:%d, before_rich:%d, after_rich:%d, beforeLv:%+v, afterLv:%+v, beforeNkw:%d, afterNkw:%d",
			sender, gn.BeforeRich, gn.AfterRich, beforeLv, afterLv, beforeNkw, afterNkw)

		m.ProcUserRichDecoration(ctx, sender, uint32(afterLv.ServerLevel), uint32(beforeLv.ServerLevel))

		_, _ = m.presentExtraCli.NotifyPrivilegeLevelChange(ctx, &presentextraconf.NotifyPrivilegeLevelChangeReq{
			Uid:         sender,
			ValueType:   uint32(presentextraconf.ValueType_ValueTypeRich),
			BeforeValue: gn.BeforeRich,
			AfterValue:  gn.AfterRich,
		})

		isNotify := false
		switch afterLv.MainLevel {
		case
			3, // 10w
			4: // 100w
			if afterLv.MainLevel != beforeLv.MainLevel {
				isNotify = true
				break
			}
		case 5: // kw
			if afterLv.MainLevel != beforeLv.MainLevel || afterLv.SubLevel != beforeLv.SubLevel {
				isNotify = true
				break
			}
		case 6: // e
			if afterLv.MainLevel != beforeLv.MainLevel || afterLv.SubLevel != beforeLv.SubLevel || afterLv.SubLevel2 != beforeLv.SubLevel2 {
				isNotify = true
				break
			}
		case 7, 8:
			if afterNkw != beforeNkw { // 每1000万推送一次
				isNotify = true
				break
			}
		default:
			break
		}

		if !isNotify {
			return
		}

		//获取升级礼物信息 比较麻烦
		decorationName := ""
		decorationConfig, sErr := m.personalizationCli.GetChannelEnterSpecialEffectConfig(ctx)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate personalizationCli err:%s, uid:%v", sErr, sender)
		}
		awardLevel := uint32(0)
		for _, decoration := range decorationConfig.GetRichLevelConfig() {
			if uint64(decoration.MinLevel) <= afterLv.ServerLevel && decoration.MinLevel >= awardLevel {
				decorationName = decoration.GetEffectInfo().GetDetail().GetChannelEnterSpecialEffect().GetName() + "座驾"
				awardLevel = decoration.MinLevel
			}
			log.DebugWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate awardLevel %d", awardLevel)
		}

		memberOpt := channelPB_.ChannelMemberOpt{
			NewRichLevel:         uint32(afterLv.ServerLevel),
			RichCharmLevelUpdate: uint32(channelPB_.RICH_CHARM_LEVEL_UPDATE_TYPE_RICH_LEVEL_UPDATE),
			RichUpgradeAwardList: []*channelPB_.RichUpgradeAward{},
		}
		if afterLv.ServerLevel >= uint64(awardLevel) && beforeLv.ServerLevel < uint64(awardLevel) && decorationName != "" {
			memberOpt.RichUpgradeAwardList = append(memberOpt.RichUpgradeAwardList, &channelPB_.RichUpgradeAward{AwardName: decorationName})
		}

		log.DebugWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate sender:%d, channel_id:%d, rich:%d, memberOpt:%+v", sender, channelId, gn.AfterRich, memberOpt)
		memberOptContent, _ := proto.Marshal(&memberOpt)
		broadcastMsg := &channelPB_.ChannelBroadcastMsg{
			Content:      []byte(""),
			FromAccount:  userInfoMap[sender].GetUsername(),
			FromNick:     userInfoMap[sender].GetNickname(),
			FromUid:      sender,
			Time:         uint64(time.Now().Unix()),
			Type:         uint32(channelPB_.ChannelMsgType_CHANNEL_MEMBER_OPT_INFO_CHANGED),
			ToChannelId:  channelId,
			PbOptContent: memberOptContent,
		}

		broadcastMsg.FromUserProfile = ukwMap[sender]
		senderProfile := broadcastMsg.GetFromUserProfile()
		if senderProfile.GetPrivilege() != nil {
			broadcastMsg.FromUid = senderProfile.GetUid()
			broadcastMsg.FromAccount = senderProfile.GetPrivilege().GetAccount()
			broadcastMsg.FromNick = senderProfile.GetPrivilege().GetNickname()
		}

		_ = m.expressCli.SendChannelBroadcastMsg(ctx, broadcastMsg)

		// 检查是否是财富大事件
		_ = m.sendRichLevelBreakingNews(ctx, sender, channelId, userInfoMap, channelInfo, beforeLv, afterLv)
	}()

	// charm level upgrade
	func() {
		beforeLv := TransRichOrCharmLevel(pb.NumericT_Charm, gn.BeforeCharm)
		afterLv := TransRichOrCharmLevel(pb.NumericT_Charm, gn.AfterCharm)
		isChange := beforeLv.ServerLevel != afterLv.ServerLevel
		if !isChange || afterLv.MainLevel < 3 {
			// 3级以下不推 即<10w
			return
		}
		if beforeLv.MainLevel == afterLv.MainLevel && beforeLv.SubLevel == afterLv.SubLevel {
			// 如果仅仅是subLevel_2 以下的等级有变化的话 不做升级提示的push
			// 说人话：第二位数变更不推，仅第一位数变更推送
			return
		}

		log.InfoWithCtx(ctx, "broadcastUserRichOrCharmLevelUpdate receiver:%d, before_charm:%d, after_charm:%d, beforeLv:%+v, afterLv:%+v",
			receiver, gn.BeforeCharm, gn.AfterCharm, beforeLv, afterLv)

		memberOpt := channelPB_.ChannelMemberOpt{NewCharmLevel: uint32(afterLv.ServerLevel), RichCharmLevelUpdate: uint32(channelPB_.RICH_CHARM_LEVEL_UPDATE_TYPE_CHARM_LEVEL_UPDATE)}
		memberOptContent, _ := proto.Marshal(&memberOpt)
		broadcastMsg := &channelPB_.ChannelBroadcastMsg{
			Content:     []byte(""),
			FromAccount: userInfoMap[receiver].GetUsername(),
			FromNick:    userInfoMap[receiver].GetNickname(),
			FromUid:     receiver,
			Time:        uint64(time.Now().Unix()), Type: uint32(channelPB_.ChannelMsgType_CHANNEL_MEMBER_OPT_INFO_CHANGED),
			ToChannelId: channelId, PbOptContent: memberOptContent,
		}

		broadcastMsg.FromUserProfile = ukwMap[receiver]
		receiverProfile := broadcastMsg.GetFromUserProfile()
		if receiverProfile.GetPrivilege() != nil {
			broadcastMsg.FromUid = receiverProfile.GetUid()
			broadcastMsg.FromAccount = receiverProfile.GetPrivilege().GetAccount()
			broadcastMsg.FromNick = receiverProfile.GetPrivilege().GetNickname()
		}
		_ = m.expressCli.SendChannelBroadcastMsg(ctx, broadcastMsg)
	}()
}

// ReportDataCenter 检查财富等级大事件
// from present-middleware
func checkIsBreakNews(ctx context.Context, beforeLevel uint64, newLevel uint64) (isBreaking bool, strRichLevelName string) {
	knightLevel := uint64(10000)           // 1w
	kingLevel := uint64(100000)            // 10w
	universityKingLevel := uint64(1000000) // 100w

	log.DebugWithCtx(ctx, "checkIsBreakNews coding >> Breaking News check, beforeLevel=%d, newLevel=%d, knightLevel=%d, kingLevel=%d", beforeLevel,
		newLevel, knightLevel, kingLevel)

	if newLevel < knightLevel {
		return
	}

	if newLevel >= universityKingLevel && beforeLevel/knightLevel < newLevel/knightLevel {
		tmpLevel := newLevel / knightLevel

		strRichLevelName = fmt.Sprintf("永恒帝尊%d.%d", tmpLevel/10, tmpLevel%10)
		isBreaking = true
		return
	}

	if newLevel >= kingLevel && beforeLevel/knightLevel < newLevel/knightLevel {
		tmpLevel := newLevel / knightLevel

		strRichLevelName = fmt.Sprintf("至尊帝皇%v.%v", tmpLevel/10, tmpLevel%10)
		isBreaking = true
		return
	}

	if newLevel >= knightLevel && beforeLevel/knightLevel < newLevel/knightLevel {
		strRichLevelName = fmt.Sprintf("枭雄霸王%v", newLevel/knightLevel)
		isBreaking = true
		return
	}

	return
}

// ProcUserRichDecoration from present-middleware
func (m *Manager) ProcUserRichDecoration(ctx context.Context, uid uint32, richAfterSerLv uint32, richBeforeSerLv uint32) {
	resp, err := m.personalizationCli.GetUserDecorations(ctx, uid, richAfterSerLv, 1, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "ProcUserRichDecoration -- GetUserDecorations err %s uid %d rich_level %d pre_rich_level %d",
			err, uid, richAfterSerLv, richBeforeSerLv)
	}
	for _, decoration := range resp.GetUserDecorations() {
		//财富坐骑
		if decoration.GetDecoration().GetDetail().GetChannelEnterSpecialEffect().GetEffectType() == 1 {
			if decoration.GetDecoration().GetDetail().GetChannelEnterSpecialEffect().GetMinLevel() <= richBeforeSerLv {
				return
			}
			if decoration.GetDecoration().GetDetail().GetChannelEnterSpecialEffect().GetName() == "" {
				return
			}
			decorationDetail := decoration.GetDecoration().GetDetail().GetChannelEnterSpecialEffect()
			decorationBytes, _ := proto.Marshal(decorationDetail)

			decorationMsg := pushPb.MyDecorationMsg{
				Uid: uid,
				UserDecoration: &app.UserDecorationInfo{
					Uid: uid,
					DecorationCfg: &app.DecorationConfig{
						Id:        decoration.GetDecoration().GetId(),
						Type:      uint32(decoration.GetDecoration().GetType()),
						Ver:       decoration.GetDecoration().GetVer(),
						RichLevel: richAfterSerLv,
						Detail:    decorationBytes,
					},
				},
				RichLevel: richAfterSerLv,
			}
			content, _ := proto.Marshal(&decorationMsg)
			log.InfoWithCtx(ctx, "ProcUserRichDecoration proto.Marshal decorationDetail %+v uid:%d decorationMsg %+v", decorationDetail, uid, decorationMsg)

			seq, err := m.seqgenCli.GenerateSequence(ctx, uid, "@reliable_push", "0", 1)
			if err != nil {
				log.ErrorWithCtx(ctx, "ProcUserRichDecoration GenerateSequence err:%s uid:%d", err, uid)
			}

			pushMessage := &pushPb.PushMessage{
				Cmd:     uint32(pushPb.PushMessage_MY_DECORATION_MSG),
				Content: content,
				SeqId:   uint32(seq),
			}
			pushMessageBytes, _ := pushMessage.Marshal()

			sErr := m.pushCli.PushToUsers(context.Background(), []uint32{uid}, &pushPB.CompositiveNotification{
				Sequence: uint32(seq),
				//TerminalTypeList: []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
				TerminalTypePolicy: pushclient.DefaultPolicy,
				AppId:              0,
				ProxyNotification: &pushPB.ProxyNotification{
					Type:    uint32(pushPB.ProxyNotification_PUSH),
					Payload: pushMessageBytes,
				},
			})
			if sErr != nil {
				log.ErrorWithCtx(ctx, "ProcUserRichDecoration PushToUsers err:%s uid:%d", sErr, uid)
			}
		}
	}
}
