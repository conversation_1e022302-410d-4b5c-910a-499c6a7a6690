package manager

import (
	"context"
	"reflect"
	"testing"
	"time"

	"golang.52tt.com/services/esport-score/internal/common"

	accountPB "golang.52tt.com/protocol/services/accountsvr"

	"github.com/golang/mock/gomock"
	"golang.52tt.com/protocol/services/esport_score"
	pb "golang.52tt.com/protocol/services/esport_score"
	"golang.52tt.com/services/esport-score/internal/mocks"
)

func Test_manager_AddEsportScore(t *testing.T) {
	orderTime := time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local)
	type args struct {
		ctx         context.Context
		orderDetail *common.EsportOrderDetail
	}
	tests := []struct {
		name     string
		args     args
		want     int64
		wantErr  bool
		initFunc func(m *mgrForTest)
	}{
		{
			name: "测试添加电竞积分",
			args: args{
				ctx: context.Background(),
				orderDetail: &common.EsportOrderDetail{
					Uid:        12345,
					TotalPrice: 100,
					Score:      90,
					ReasonType: esport_score.ReasonType_REASON_FINISH_ORDER,
					Reason:     "test",
					OrderId:    "test-order",
					ServerTime: orderTime,
				},
			},
			want:    90,
			wantErr: false,
			initFunc: func(m *mgrForTest) {
				dbxMock := mocks.NewMockTxx(gomock.NewController(t))
				m.getStoreMock().EXPECT().GetUserScoreDetailByOrderId(gomock.Any(), "test-order", orderTime).Return(nil, nil)
				m.getStoreMock().EXPECT().GetTransaction().Return(dbxMock, nil)
				m.getStoreMock().EXPECT().UpdateUserScore(gomock.Any(), uint32(12345), int64(90), orderTime, dbxMock).Return(int64(90), nil)
				m.getStoreMock().EXPECT().InsertUserScoreDetail(gomock.Any(), gomock.Any(), dbxMock).Return(nil)
				m.getStoreMock().EXPECT().UpdateUserScoreStatistics(gomock.Any(), uint32(12345), int64(90), orderTime, gomock.Any(), dbxMock).Return(nil)
				dbxMock.EXPECT().Commit().Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.AddEsportScore(tt.args.ctx, tt.args.orderDetail)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddEsportScore() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("AddEsportScore() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_manager_GetEsportScore(t *testing.T) {
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name     string
		args     args
		want     int64
		wantErr  bool
		initFunc func(m *mgrForTest)
	}{
		{
			name: "测试获取电竞积分",
			args: args{
				ctx: context.Background(),
				uid: 12345,
			},
			want:    1234,
			wantErr: false,
			initFunc: func(m *mgrForTest) {
				m.getStoreMock().EXPECT().GetUserScore(gomock.Any(), uint32(12345)).Return(int64(1234), nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.GetEsportScore(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetEsportScore() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetEsportScore() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_manager_GetEsportScoreRecordList(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.GetEsportScoreRecordListReq
	}
	tests := []struct {
		name     string
		args     args
		want     []*pb.UserEsportScoreRecord
		want1    uint32
		wantErr  bool
		initFunc func(m *mgrForTest)
	}{
		{
			name: "测试获取电竞积分变更记录",
			args: args{
				ctx: context.Background(),
				req: &pb.GetEsportScoreRecordListReq{
					BeginTs:        time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local).Unix(),
					EndTs:          time.Date(2024, 2, 1, 0, 0, 0, 0, time.Local).Unix(),
					UidList:        []uint32{12345, 12346},
					TtidList:       []string{"test3"},
					MinRemainScore: 100,
					Offset:         0,
					Limit:          10,
				},
			},
			want: []*pb.UserEsportScoreRecord{
				{
					Ttid:                         "test1",
					Uid:                          12345,
					Nickname:                     "t1",
					SignType:                     1,
					SignInfo:                     []*pb.SignInfo{},
					GuildEsportScore:             0,
					LastMonthRemainScore:         100,
					NewScore:                     100,
					TotalScore:                   200,
					ExchangeMoneyScore:           100,
					ExchangeMoneyFailReturnScore: 100,
					ExchangeTbeanScore:           100,
					OfficialReclaimScore:         100,
					OfficialGrantScore:           100,
					RemainScore:                  100,
					RemainExchangeMoney:          0,
				},
				{
					Ttid:                         "test2",
					Uid:                          12346,
					Nickname:                     "t2",
					SignType:                     1,
					SignInfo:                     []*pb.SignInfo{},
					GuildEsportScore:             0,
					LastMonthRemainScore:         100,
					NewScore:                     100,
					TotalScore:                   200,
					ExchangeMoneyScore:           100,
					ExchangeMoneyFailReturnScore: 100,
					ExchangeTbeanScore:           100,
					OfficialReclaimScore:         100,
					OfficialGrantScore:           100,
					RemainScore:                  100,
					RemainExchangeMoney:          0,
				},
				{
					Ttid:     "test3",
					Uid:      12347,
					Nickname: "t3",
					SignType: 2,
					SignInfo: []*pb.SignInfo{
						{
							SignStartTime: **********,
						},
					},
					GuildEsportScore:             100,
					LastMonthRemainScore:         100,
					NewScore:                     10000,
					TotalScore:                   10100,
					ExchangeMoneyScore:           100,
					ExchangeMoneyFailReturnScore: 100,
					ExchangeTbeanScore:           100,
					OfficialReclaimScore:         100,
					OfficialGrantScore:           100,
					RemainScore:                  9900,
					RemainExchangeMoney:          0,
				},
			},
			want1:   3,
			wantErr: false,
			initFunc: func(m *mgrForTest) {
				m.getClientMock().EXPECT().GetUidByAliasList(gomock.Any(), []string{"test3"}).Return([]uint32{12347},
					map[string]uint32{
						"test3": 12347,
					}, nil)
				m.getStoreMock().EXPECT().BatchGetUserScoreDailySum(
					gomock.Any(),
					time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local),
					time.Date(2024, 2, 1, 0, 0, 0, 0, time.Local),
					uint32(0),
					uint32(10),
					[]uint32{12345, 12346, 12347},
					uint32(100)).Return(
					[]*EsportUserScoreDaily{
						{
							Uid:                    12345,
							LastMonthScore:         100,
							NewScore:               100,
							ExchangeMoneyScore:     100,
							ExchangeMoneyFailScore: 100,
							ExchangeTbeanScore:     100,
							OfficialReclaimScore:   100,
							OfficialGrantScore:     100,
							GuildScore:             0,
						},
						{
							Uid:                    12346,
							LastMonthScore:         100,
							NewScore:               100,
							ExchangeMoneyScore:     100,
							ExchangeMoneyFailScore: 100,
							ExchangeTbeanScore:     100,
							OfficialReclaimScore:   100,
							OfficialGrantScore:     100,
							GuildScore:             0,
						},
						{
							Uid:                    12347,
							LastMonthScore:         100,
							NewScore:               10000,
							ExchangeMoneyScore:     100,
							ExchangeMoneyFailScore: 100,
							ExchangeTbeanScore:     100,
							OfficialReclaimScore:   100,
							OfficialGrantScore:     100,
							GuildScore:             100,
						},
					}, uint32(3), nil)
				m.getClientMock().EXPECT().GetUserByUidList(gomock.Any(), []uint32{12345, 12346, 12347}).Return(map[uint32]*accountPB.UserResp{
					12345: {
						Nickname: "t1",
						Alias:    "test1",
					},
					12346: {
						Nickname: "t2",
						Alias:    "test2",
					},
					12347: {
						Nickname: "t3",
						Alias:    "test3",
					},
				}, nil)
				m.getClientMock().EXPECT().BatchGetEsportContractInfo(gomock.Any(), []uint32{12345, 12346, 12347}).Return(map[uint32][]*pb.SignInfo{
					12347: {
						{
							SignStartTime: **********,
							SignEndTime:   0,
						},
					},
				}, nil)
				m.getClientMock().EXPECT().GetGuildSettlementSignTimeWithUidList(gomock.Any(), []uint32{12345, 12346, 12347}).Return(map[uint32]uint32{}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, got1, err := m.GetEsportScoreRecordList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetEsportScoreRecordList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetEsportScoreRecordList() got = [%+v], want [%+v]", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("GetEsportScoreRecordList() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func Test_manager_InitMonthlyScoreDetail(t *testing.T) {
	nowTime := time.Date(2024, 2, 1, 2, 10, 10, 0, time.Local)
	type args struct {
		ctx context.Context
		now time.Time
	}
	tests := []struct {
		name     string
		args     args
		wantErr  bool
		initFunc func(m *mgrForTest)
	}{
		{
			name: "测试初始化月积分明细表",
			args: args{
				ctx: context.Background(),
				now: nowTime,
			},
			wantErr: false,
			initFunc: func(m *mgrForTest) {
				finishKey := getInitMonthlyScoreDetailFinishKey(nowTime)
				m.getStoreMock().EXPECT().ValidHasLock(gomock.Any(), finishKey).Return(false, nil)
				lockKey := getInitMonthlyScoreDetailLockKey(nowTime)
				m.getStoreMock().EXPECT().TryLock(gomock.Any(), lockKey, nowTime.Format("20060102150405"), gomock.Any()).Return(true, nil)
				m.getStoreMock().EXPECT().BatchGetAllUserScore(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]int64{
					12345: 100,
				}, nil)
				lastMonthTime := time.Date(nowTime.Year(), nowTime.Month()-1, 1, 0, 0, 0, 0, time.Local)
				m.getStoreMock().EXPECT().BatchGetUserScoreMonthly(gomock.Any(), []uint32{12345}, lastMonthTime, gomock.Any()).Return(map[uint32]int64{
					12345: 100,
				}, nil)
				m.getStoreMock().EXPECT().UpdateUserScoreStatistics(gomock.Any(), uint32(12345), int64(0), nowTime, gomock.Any(), gomock.Any()).Return(nil)
				m.getStoreMock().EXPECT().TryLock(gomock.Any(), finishKey, nowTime.Format("20060102150405"), gomock.Any()).Return(true, nil)
				m.getStoreMock().EXPECT().Unlock(gomock.Any(), lockKey, nowTime.Format("20060102150405")).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			if err := m.InitMonthlyScoreDetail(tt.args.ctx, tt.args.now); (err != nil) != tt.wantErr {
				t.Errorf("InitMonthlyScoreDetail() error = %v, wantErr %v", err, tt.wantErr)
			}
			time.Sleep(1 * time.Second)
		})
	}
}
