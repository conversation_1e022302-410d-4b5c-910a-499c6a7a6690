package cache

import (
	"github.com/golang/mock/gomock"
	userpresent_go "golang.52tt.com/protocol/services/userpresent-go"
	"golang.52tt.com/services/userpresent-go/internal/store"
	"reflect"
	"sync"
	"testing"
	"time"
)

type cacheForTest struct {
	PresentMemCache
}

func TestCheckPresentEnter(t *testing.T) {
	type args struct {
		cfgNew *userpresent_go.PresentConfigNew
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "测试CheckPresentEnter",
			args: args{
				cfgNew: &userpresent_go.PresentConfigNew{
					BaseConfig: &userpresent_go.PresentBaseConfig{
						ItemId: 1,
					},
					EnterConfig: &userpresent_go.PresentEnterConfig{
						ItemId: 1,
					},
					EffectConfig: &userpresent_go.PresentEffectConfig{
						ItemId: 1,
					},
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := CheckPresentEnter(tt.args.cfgNew); got != tt.want {
				t.Errorf("CheckPresentEnter() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentMemCache_GetConfigById(t *testing.T) {
	storeMock := store.NewMockIStore(gomock.NewController(t))
	//storeMock.EXPECT().GetPresentConfigById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&userpresent_go.StPresentItemConfig{ItemId: 1}, nil).AnyTimes()
	//cacheMock, rdb := GetRedis(t)
	//defer cacheMock.Close()

	type fields struct {
		presentConfigMapNew map[uint32]*userpresent_go.PresentConfigNew
		presentConfigMap    map[uint32]*userpresent_go.StPresentItemConfig
		updateLog           []*store.PresentConfigLog
		lastUpdateTime      uint32
		rwMutex             sync.RWMutex
		store               store.IStore
		ticker              *time.Ticker
		done                chan bool
	}
	type args struct {
		itemId uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *userpresent_go.StPresentItemConfig
	}{
		{
			name: "测试GetConfigById",
			fields: fields{
				presentConfigMapNew: map[uint32]*userpresent_go.PresentConfigNew{
					1: {
						BaseConfig: &userpresent_go.PresentBaseConfig{
							ItemId: 1,
						},
						EnterConfig: &userpresent_go.PresentEnterConfig{
							ItemId: 1,
						},
						EffectConfig: &userpresent_go.PresentEffectConfig{
							ItemId: 1,
						},
					},
				},
				presentConfigMap: map[uint32]*userpresent_go.StPresentItemConfig{
					1: {
						ItemId: 1,
					},
				},
				updateLog: []*store.PresentConfigLog{{
					ItemId:     1,
					UpdateTime: 1000},
				},
				lastUpdateTime: 1000,
				rwMutex:        sync.RWMutex{},
				store:          storeMock,
			},
			args: args{itemId: 1},
			want: &userpresent_go.StPresentItemConfig{ItemId: 1},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentMemCache{
				presentConfigMapNew: tt.fields.presentConfigMapNew,
				presentConfigMap:    tt.fields.presentConfigMap,
				updateLog:           tt.fields.updateLog,
				lastUpdateTime:      tt.fields.lastUpdateTime,
				rwMutex:             tt.fields.rwMutex,
				store:               tt.fields.store,
				ticker:              tt.fields.ticker,
				done:                tt.fields.done,
			}
			if got := s.GetConfigById(tt.args.itemId); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetConfigById() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentMemCache_GetConfigByIdList(t *testing.T) {
	storeMock := store.NewMockIStore(gomock.NewController(t))
	type fields struct {
		presentConfigMapNew map[uint32]*userpresent_go.PresentConfigNew
		presentConfigMap    map[uint32]*userpresent_go.StPresentItemConfig
		updateLog           []*store.PresentConfigLog
		lastUpdateTime      uint32
		rwMutex             sync.RWMutex
		store               store.IStore
		ticker              *time.Ticker
		done                chan bool
	}
	type args struct {
		itemIdList []uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   map[uint32]*userpresent_go.StPresentItemConfig
	}{
		{
			name: "测试GetConfigById",
			fields: fields{
				presentConfigMapNew: map[uint32]*userpresent_go.PresentConfigNew{
					1: {
						BaseConfig: &userpresent_go.PresentBaseConfig{
							ItemId: 1,
						},
						EnterConfig: &userpresent_go.PresentEnterConfig{
							ItemId: 1,
						},
						EffectConfig: &userpresent_go.PresentEffectConfig{
							ItemId: 1,
						},
					},
				},
				presentConfigMap: map[uint32]*userpresent_go.StPresentItemConfig{
					1: {
						ItemId: 1,
					},
				},
				updateLog: []*store.PresentConfigLog{{
					ItemId:     1,
					UpdateTime: 1000},
				},
				lastUpdateTime: 1000,
				rwMutex:        sync.RWMutex{},
				store:          storeMock,
			},
			args: args{itemIdList: []uint32{1}},
			want: map[uint32]*userpresent_go.StPresentItemConfig{1: {ItemId: 1}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentMemCache{
				presentConfigMapNew: tt.fields.presentConfigMapNew,
				presentConfigMap:    tt.fields.presentConfigMap,
				updateLog:           tt.fields.updateLog,
				lastUpdateTime:      tt.fields.lastUpdateTime,
				rwMutex:             tt.fields.rwMutex,
				store:               tt.fields.store,
				ticker:              tt.fields.ticker,
				done:                tt.fields.done,
			}
			if got := s.GetConfigByIdList(tt.args.itemIdList); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetConfigByIdList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentMemCache_GetConfigByIdListNew(t *testing.T) {
	storeMock := store.NewMockIStore(gomock.NewController(t))
	type fields struct {
		presentConfigMapNew map[uint32]*userpresent_go.PresentConfigNew
		presentConfigMap    map[uint32]*userpresent_go.StPresentItemConfig
		updateLog           []*store.PresentConfigLog
		lastUpdateTime      uint32
		rwMutex             sync.RWMutex
		store               store.IStore
		ticker              *time.Ticker
		done                chan bool
	}
	type args struct {
		itemIdList []uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   map[uint32]*userpresent_go.PresentConfigNew
	}{
		{
			name: "测试GetConfigById",
			fields: fields{
				presentConfigMapNew: map[uint32]*userpresent_go.PresentConfigNew{
					1: {
						BaseConfig: &userpresent_go.PresentBaseConfig{
							ItemId: 1,
						},
						EnterConfig: &userpresent_go.PresentEnterConfig{
							ItemId: 1,
						},
						EffectConfig: &userpresent_go.PresentEffectConfig{
							ItemId: 1,
						},
					},
				},
				presentConfigMap: map[uint32]*userpresent_go.StPresentItemConfig{
					1: {
						ItemId: 1,
					},
				},
				updateLog: []*store.PresentConfigLog{{
					ItemId:     1,
					UpdateTime: 1000},
				},
				lastUpdateTime: 1000,
				rwMutex:        sync.RWMutex{},
				store:          storeMock,
			},
			args: args{itemIdList: []uint32{1}},
			want: map[uint32]*userpresent_go.PresentConfigNew{1: {
				BaseConfig:   &userpresent_go.PresentBaseConfig{ItemId: 1},
				EnterConfig:  &userpresent_go.PresentEnterConfig{ItemId: 1},
				EffectConfig: &userpresent_go.PresentEffectConfig{ItemId: 1},
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentMemCache{
				presentConfigMapNew: tt.fields.presentConfigMapNew,
				presentConfigMap:    tt.fields.presentConfigMap,
				updateLog:           tt.fields.updateLog,
				lastUpdateTime:      tt.fields.lastUpdateTime,
				rwMutex:             tt.fields.rwMutex,
				store:               tt.fields.store,
				ticker:              tt.fields.ticker,
				done:                tt.fields.done,
			}
			if got := s.GetConfigByIdListNew(tt.args.itemIdList); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetConfigByIdListNew() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentMemCache_GetConfigByIdNew(t *testing.T) {
	storeMock := store.NewMockIStore(gomock.NewController(t))
	type fields struct {
		presentConfigMapNew map[uint32]*userpresent_go.PresentConfigNew
		presentConfigMap    map[uint32]*userpresent_go.StPresentItemConfig
		updateLog           []*store.PresentConfigLog
		lastUpdateTime      uint32
		rwMutex             sync.RWMutex
		store               store.IStore
		ticker              *time.Ticker
		done                chan bool
	}
	type args struct {
		itemId uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *userpresent_go.PresentConfigNew
	}{
		{
			name: "测试GetConfigById",
			fields: fields{
				presentConfigMapNew: map[uint32]*userpresent_go.PresentConfigNew{
					1: {
						BaseConfig: &userpresent_go.PresentBaseConfig{
							ItemId: 1,
						},
						EnterConfig: &userpresent_go.PresentEnterConfig{
							ItemId: 1,
						},
						EffectConfig: &userpresent_go.PresentEffectConfig{
							ItemId: 1,
						},
					},
				},
				presentConfigMap: map[uint32]*userpresent_go.StPresentItemConfig{
					1: {
						ItemId: 1,
					},
				},
				updateLog: []*store.PresentConfigLog{{
					ItemId:     1,
					UpdateTime: 1000},
				},
				lastUpdateTime: 1000,
				rwMutex:        sync.RWMutex{},
				store:          storeMock,
			},
			args: args{itemId: 1},
			want: &userpresent_go.PresentConfigNew{
				BaseConfig:   &userpresent_go.PresentBaseConfig{ItemId: 1},
				EnterConfig:  &userpresent_go.PresentEnterConfig{ItemId: 1},
				EffectConfig: &userpresent_go.PresentEffectConfig{ItemId: 1},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentMemCache{
				presentConfigMapNew: tt.fields.presentConfigMapNew,
				presentConfigMap:    tt.fields.presentConfigMap,
				updateLog:           tt.fields.updateLog,
				lastUpdateTime:      tt.fields.lastUpdateTime,
				rwMutex:             tt.fields.rwMutex,
				store:               tt.fields.store,
				ticker:              tt.fields.ticker,
				done:                tt.fields.done,
			}
			if got := s.GetConfigByIdNew(tt.args.itemId); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetConfigByIdNew() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentMemCache_GetConfigList(t *testing.T) {
	storeMock := store.NewMockIStore(gomock.NewController(t))
	type fields struct {
		presentConfigMapNew map[uint32]*userpresent_go.PresentConfigNew
		presentConfigMap    map[uint32]*userpresent_go.StPresentItemConfig
		updateLog           []*store.PresentConfigLog
		lastUpdateTime      uint32
		rwMutex             sync.RWMutex
		store               store.IStore
		ticker              *time.Ticker
		done                chan bool
	}
	type args struct {
		updateTime uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   uint32
		want1  []*userpresent_go.StPresentItemConfig
	}{
		{
			name: "测试GetConfigById",
			fields: fields{
				presentConfigMapNew: map[uint32]*userpresent_go.PresentConfigNew{
					1: {
						BaseConfig: &userpresent_go.PresentBaseConfig{
							ItemId: 1,
						},
						EnterConfig: &userpresent_go.PresentEnterConfig{
							ItemId: 1,
						},
						EffectConfig: &userpresent_go.PresentEffectConfig{
							ItemId: 1,
						},
					},
				},
				presentConfigMap: map[uint32]*userpresent_go.StPresentItemConfig{
					1: {
						ItemId: 1,
					},
				},
				updateLog: []*store.PresentConfigLog{{
					ItemId:     1,
					UpdateTime: 1000},
				},
				lastUpdateTime: 1000,
				rwMutex:        sync.RWMutex{},
				store:          storeMock,
			},
			args:  args{},
			want:  1000,
			want1: []*userpresent_go.StPresentItemConfig{{ItemId: 1}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentMemCache{
				presentConfigMapNew: tt.fields.presentConfigMapNew,
				presentConfigMap:    tt.fields.presentConfigMap,
				updateLog:           tt.fields.updateLog,
				lastUpdateTime:      tt.fields.lastUpdateTime,
				rwMutex:             tt.fields.rwMutex,
				store:               tt.fields.store,
				ticker:              tt.fields.ticker,
				done:                tt.fields.done,
			}
			got, got1 := s.GetConfigList(tt.args.updateTime)
			if got != tt.want {
				t.Errorf("GetConfigList() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetConfigList() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestPresentMemCache_GetConfigListNew(t *testing.T) {
	storeMock := store.NewMockIStore(gomock.NewController(t))
	type fields struct {
		presentConfigMapNew map[uint32]*userpresent_go.PresentConfigNew
		presentConfigMap    map[uint32]*userpresent_go.StPresentItemConfig
		updateLog           []*store.PresentConfigLog
		lastUpdateTime      uint32
		rwMutex             sync.RWMutex
		store               store.IStore
		ticker              *time.Ticker
		done                chan bool
	}
	type args struct {
		updateTime uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   uint32
		want1  []*userpresent_go.PresentConfigNew
	}{
		{
			name: "测试GetConfigById",
			fields: fields{
				presentConfigMapNew: map[uint32]*userpresent_go.PresentConfigNew{
					1: {
						BaseConfig: &userpresent_go.PresentBaseConfig{
							ItemId: 1,
						},
						EnterConfig: &userpresent_go.PresentEnterConfig{
							ItemId: 1,
						},
						EffectConfig: &userpresent_go.PresentEffectConfig{
							ItemId: 1,
						},
					},
				},
				presentConfigMap: map[uint32]*userpresent_go.StPresentItemConfig{
					1: {
						ItemId: 1,
					},
				},
				updateLog: []*store.PresentConfigLog{{
					ItemId:     1,
					UpdateTime: 1000},
				},
				lastUpdateTime: 10000,
				rwMutex:        sync.RWMutex{},
				store:          storeMock,
			},
			want: 10000,
			want1: []*userpresent_go.PresentConfigNew{{
				BaseConfig:   &userpresent_go.PresentBaseConfig{ItemId: 1},
				EnterConfig:  &userpresent_go.PresentEnterConfig{ItemId: 1},
				EffectConfig: &userpresent_go.PresentEffectConfig{ItemId: 1},
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentMemCache{
				presentConfigMapNew: tt.fields.presentConfigMapNew,
				presentConfigMap:    tt.fields.presentConfigMap,
				updateLog:           tt.fields.updateLog,
				lastUpdateTime:      tt.fields.lastUpdateTime,
				rwMutex:             tt.fields.rwMutex,
				store:               tt.fields.store,
				ticker:              tt.fields.ticker,
				done:                tt.fields.done,
			}
			got, got1 := s.GetConfigListNew(tt.args.updateTime)
			if got != tt.want {
				t.Errorf("GetConfigListNew() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetConfigListNew() got1 = %+v, want %+v", got1, tt.want1)
			}
		})
	}
}

func TestPresentMemCache_UpdateConfig(t *testing.T) {
	storeMock := store.NewMockIStore(gomock.NewController(t))
	storeMock.EXPECT().GetAllPresentBaseConfig(gomock.Any()).Return([]*store.PresentBaseConfig{{ItemId: 1}}, nil).AnyTimes()
	storeMock.EXPECT().GetAllPresentEnterConfig(gomock.Any()).Return([]*store.PresentEnterConfig{{ItemId: 1}}, nil).AnyTimes()
	storeMock.EXPECT().GetAllPresentEffectConfig(gomock.Any()).Return([]*store.PresentEffectConfig{{ItemId: 1}}, nil).AnyTimes()
	storeMock.EXPECT().GetAllPresentConfigLog(gomock.Any()).Return([]*store.PresentConfigLog{{ItemId: 1, UpdateTime: 1000}}, nil).AnyTimes()

	sceneStoreMock := store.NewMockISceneStore(gomock.NewController(t))
	sceneStoreMock.EXPECT().GetValidNamingPresentInfoList(gomock.Any())

	type fields struct {
		presentConfigMapNew map[uint32]*userpresent_go.PresentConfigNew
		presentConfigMap    map[uint32]*userpresent_go.StPresentItemConfig
		updateLog           []*store.PresentConfigLog
		lastUpdateTime      uint32
		rwMutex             sync.RWMutex
		store               store.IStore
		ticker              *time.Ticker
		done                chan bool
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "测试GetConfigById",
			fields: fields{
				presentConfigMapNew: map[uint32]*userpresent_go.PresentConfigNew{
					1: {
						BaseConfig: &userpresent_go.PresentBaseConfig{
							ItemId: 1,
						},
						EnterConfig: &userpresent_go.PresentEnterConfig{
							ItemId: 1,
						},
						EffectConfig: &userpresent_go.PresentEffectConfig{
							ItemId: 1,
						},
					},
				},
				presentConfigMap: map[uint32]*userpresent_go.StPresentItemConfig{
					1: {
						ItemId: 1,
					},
				},
				updateLog: []*store.PresentConfigLog{{
					ItemId:     1,
					UpdateTime: 1000},
				},
				lastUpdateTime: 10000,
				rwMutex:        sync.RWMutex{},
				store:          storeMock,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentMemCache{
				presentConfigMapNew: tt.fields.presentConfigMapNew,
				presentConfigMap:    tt.fields.presentConfigMap,
				sceneStore:          sceneStoreMock,
				updateLog:           tt.fields.updateLog,
				lastUpdateTime:      tt.fields.lastUpdateTime,
				rwMutex:             tt.fields.rwMutex,
				store:               tt.fields.store,
				ticker:              tt.fields.ticker,
				done:                tt.fields.done,
			}
			s.UpdateConfig()
		})
	}
}

func Test_tranBaseConfigToSvrConfig(t *testing.T) {
	type args struct {
		baseConfig *store.PresentBaseConfig
	}
	tests := []struct {
		name string
		args args
		want *userpresent_go.PresentBaseConfig
	}{
		{
			name: "测试tranBaseConfigToSvrConfig",
			args: args{
				baseConfig: &store.PresentBaseConfig{
					ItemId: 1,
				},
			},
			want: &userpresent_go.PresentBaseConfig{
				ItemId: 1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tranBaseConfigToSvrConfig(tt.args.baseConfig); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("tranBaseConfigToSvrConfig() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_tranEffectConfigToSvrConfig(t *testing.T) {
	type args struct {
		effectConfig *store.PresentEffectConfig
	}
	tests := []struct {
		name string
		args args
		want *userpresent_go.PresentEffectConfig
	}{
		{
			name: "测试tranEffectConfigToSvrConfig",
			args: args{
				effectConfig: &store.PresentEffectConfig{
					ItemId: 1,
				},
			},
			want: &userpresent_go.PresentEffectConfig{
				ItemId:            1,
				VideoEffectUrl:    []byte{},
				CustomText:        []*userpresent_go.CustomText{},
				IosVideoEffectUrl: []byte{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tranEffectConfigToSvrConfig(tt.args.effectConfig); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("tranEffectConfigToSvrConfig() = %+v, want %+v", got, tt.want)
			}
		})
	}
}

func Test_tranEnterConfigToSvrConfig(t *testing.T) {
	type args struct {
		enterConfig *store.PresentEnterConfig
	}
	tests := []struct {
		name string
		args args
		want *userpresent_go.PresentEnterConfig
	}{
		{
			name: "测试tranEnterConfigToSvrConfig",
			args: args{
				enterConfig: &store.PresentEnterConfig{
					ItemId: 1,
				},
			},
			want: &userpresent_go.PresentEnterConfig{
				ItemId: 1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tranEnterConfigToSvrConfig(tt.args.enterConfig); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("tranEnterConfigToSvrConfig() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_tranNewConfigToOldSvrConfig(t *testing.T) {
	type args struct {
		enterConfig *userpresent_go.PresentConfigNew
	}
	tests := []struct {
		name string
		args args
		want *userpresent_go.StPresentItemConfig
	}{
		{
			name: "测试tranNewConfigToOldSvrConfig",
			args: args{
				enterConfig: &userpresent_go.PresentConfigNew{
					BaseConfig: &userpresent_go.PresentBaseConfig{
						ItemId: 1,
					},
					EnterConfig: &userpresent_go.PresentEnterConfig{
						ItemId: 1,
					},
					EffectConfig: &userpresent_go.PresentEffectConfig{
						ItemId: 1,
					},
				},
			},
			want: &userpresent_go.StPresentItemConfig{
				ItemId: 1,
				Extend: &userpresent_go.StPresentItemConfigExtend{
					ItemId: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tranNewConfigToOldSvrConfig(tt.args.enterConfig); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("tranNewConfigToOldSvrConfig() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentMemCache_GetConfigUpdateTime(t *testing.T) {
	type fields struct {
		presentConfigMapNew             map[uint32]*userpresent_go.PresentConfigNew
		presentConfigMap                map[uint32]*userpresent_go.StPresentItemConfig
		namingPresentList               []*userpresent_go.NamingPresentInfo
		updateLog                       []*store.PresentConfigLog
		lastUpdateTime                  uint32
		rwMutex                         sync.RWMutex
		store                           store.IStore
		sceneStore                      store.ISceneStore
		ticker                          *time.Ticker
		done                            chan bool
		flowConfigMap                   map[uint32]*userpresent_go.StPresentFlowConfig
		flowConfigUpdateTime            uint32
		dynamicEffectTemplateMap        map[uint32]*userpresent_go.DynamicEffectTemplate
		dynamicEffectTemplateUpdateTime uint32
		presentEffectTemplateConfigMap  map[uint32]*userpresent_go.PresentEffectTemplateConfig
		presentEffectTemplateUpdateTime uint32
	}
	tests := []struct {
		name   string
		fields fields
		want   uint32
	}{
		{
			name: "测试tranNewConfigToOldSvrConfig",
			fields: fields{
				lastUpdateTime: 10000,
			},
			want: 10000,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentMemCache{
				presentConfigMapNew:             tt.fields.presentConfigMapNew,
				presentConfigMap:                tt.fields.presentConfigMap,
				namingPresentList:               tt.fields.namingPresentList,
				updateLog:                       tt.fields.updateLog,
				lastUpdateTime:                  tt.fields.lastUpdateTime,
				rwMutex:                         tt.fields.rwMutex,
				store:                           tt.fields.store,
				sceneStore:                      tt.fields.sceneStore,
				ticker:                          tt.fields.ticker,
				done:                            tt.fields.done,
				flowConfigMap:                   tt.fields.flowConfigMap,
				flowConfigUpdateTime:            tt.fields.flowConfigUpdateTime,
				dynamicEffectTemplateMap:        tt.fields.dynamicEffectTemplateMap,
				dynamicEffectTemplateUpdateTime: tt.fields.dynamicEffectTemplateUpdateTime,
				presentEffectTemplateConfigMap:  tt.fields.presentEffectTemplateConfigMap,
				presentEffectTemplateUpdateTime: tt.fields.presentEffectTemplateUpdateTime,
			}
			if got := s.GetConfigUpdateTime(); got != tt.want {
				t.Errorf("GetConfigUpdateTime() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentMemCache_GetDynamicEffectTemplateById(t *testing.T) {
	type fields struct {
		presentConfigMapNew             map[uint32]*userpresent_go.PresentConfigNew
		presentConfigMap                map[uint32]*userpresent_go.StPresentItemConfig
		namingPresentList               []*userpresent_go.NamingPresentInfo
		updateLog                       []*store.PresentConfigLog
		lastUpdateTime                  uint32
		rwMutex                         sync.RWMutex
		store                           store.IStore
		sceneStore                      store.ISceneStore
		ticker                          *time.Ticker
		done                            chan bool
		flowConfigMap                   map[uint32]*userpresent_go.StPresentFlowConfig
		flowConfigUpdateTime            uint32
		dynamicEffectTemplateMap        map[uint32]*userpresent_go.DynamicEffectTemplate
		dynamicEffectTemplateUpdateTime uint32
		presentEffectTemplateConfigMap  map[uint32]*userpresent_go.PresentEffectTemplateConfig
		presentEffectTemplateUpdateTime uint32
	}
	type args struct {
		id uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *userpresent_go.DynamicEffectTemplate
	}{
		{
			name: "测试GetDynamicEffectTemplateById",
			fields: fields{
				dynamicEffectTemplateMap: map[uint32]*userpresent_go.DynamicEffectTemplate{
					1: {Id: 1},
				},
			},
			args: args{id: 1},
			want: &userpresent_go.DynamicEffectTemplate{Id: 1},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentMemCache{
				presentConfigMapNew:             tt.fields.presentConfigMapNew,
				presentConfigMap:                tt.fields.presentConfigMap,
				namingPresentList:               tt.fields.namingPresentList,
				updateLog:                       tt.fields.updateLog,
				lastUpdateTime:                  tt.fields.lastUpdateTime,
				rwMutex:                         tt.fields.rwMutex,
				store:                           tt.fields.store,
				sceneStore:                      tt.fields.sceneStore,
				ticker:                          tt.fields.ticker,
				done:                            tt.fields.done,
				flowConfigMap:                   tt.fields.flowConfigMap,
				flowConfigUpdateTime:            tt.fields.flowConfigUpdateTime,
				dynamicEffectTemplateMap:        tt.fields.dynamicEffectTemplateMap,
				dynamicEffectTemplateUpdateTime: tt.fields.dynamicEffectTemplateUpdateTime,
				presentEffectTemplateConfigMap:  tt.fields.presentEffectTemplateConfigMap,
				presentEffectTemplateUpdateTime: tt.fields.presentEffectTemplateUpdateTime,
			}
			if got := s.GetDynamicEffectTemplateById(tt.args.id); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetDynamicEffectTemplateById() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentMemCache_GetDynamicEffectTemplateList(t *testing.T) {
	type fields struct {
		presentConfigMapNew             map[uint32]*userpresent_go.PresentConfigNew
		presentConfigMap                map[uint32]*userpresent_go.StPresentItemConfig
		namingPresentList               []*userpresent_go.NamingPresentInfo
		updateLog                       []*store.PresentConfigLog
		lastUpdateTime                  uint32
		rwMutex                         sync.RWMutex
		store                           store.IStore
		sceneStore                      store.ISceneStore
		ticker                          *time.Ticker
		done                            chan bool
		flowConfigMap                   map[uint32]*userpresent_go.StPresentFlowConfig
		flowConfigUpdateTime            uint32
		dynamicEffectTemplateMap        map[uint32]*userpresent_go.DynamicEffectTemplate
		dynamicEffectTemplateUpdateTime uint32
		presentEffectTemplateConfigMap  map[uint32]*userpresent_go.PresentEffectTemplateConfig
		presentEffectTemplateUpdateTime uint32
	}
	tests := []struct {
		name   string
		fields fields
		want   []*userpresent_go.DynamicEffectTemplate
	}{
		{
			name: "测试GetDynamicEffectTemplateList",
			fields: fields{
				dynamicEffectTemplateMap: map[uint32]*userpresent_go.DynamicEffectTemplate{
					1: {Id: 1},
				},
			},
			want: []*userpresent_go.DynamicEffectTemplate{{Id: 1}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentMemCache{
				presentConfigMapNew:             tt.fields.presentConfigMapNew,
				presentConfigMap:                tt.fields.presentConfigMap,
				namingPresentList:               tt.fields.namingPresentList,
				updateLog:                       tt.fields.updateLog,
				lastUpdateTime:                  tt.fields.lastUpdateTime,
				rwMutex:                         tt.fields.rwMutex,
				store:                           tt.fields.store,
				sceneStore:                      tt.fields.sceneStore,
				ticker:                          tt.fields.ticker,
				done:                            tt.fields.done,
				flowConfigMap:                   tt.fields.flowConfigMap,
				flowConfigUpdateTime:            tt.fields.flowConfigUpdateTime,
				dynamicEffectTemplateMap:        tt.fields.dynamicEffectTemplateMap,
				dynamicEffectTemplateUpdateTime: tt.fields.dynamicEffectTemplateUpdateTime,
				presentEffectTemplateConfigMap:  tt.fields.presentEffectTemplateConfigMap,
				presentEffectTemplateUpdateTime: tt.fields.presentEffectTemplateUpdateTime,
			}
			if got := s.GetDynamicEffectTemplateList(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetDynamicEffectTemplateList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentMemCache_GetDynamicEffectTemplateUpdateTime(t *testing.T) {
	type fields struct {
		presentConfigMapNew             map[uint32]*userpresent_go.PresentConfigNew
		presentConfigMap                map[uint32]*userpresent_go.StPresentItemConfig
		namingPresentList               []*userpresent_go.NamingPresentInfo
		updateLog                       []*store.PresentConfigLog
		lastUpdateTime                  uint32
		rwMutex                         sync.RWMutex
		store                           store.IStore
		sceneStore                      store.ISceneStore
		ticker                          *time.Ticker
		done                            chan bool
		flowConfigMap                   map[uint32]*userpresent_go.StPresentFlowConfig
		flowConfigUpdateTime            uint32
		dynamicEffectTemplateMap        map[uint32]*userpresent_go.DynamicEffectTemplate
		dynamicEffectTemplateUpdateTime uint32
		presentEffectTemplateConfigMap  map[uint32]*userpresent_go.PresentEffectTemplateConfig
		presentEffectTemplateUpdateTime uint32
	}
	tests := []struct {
		name   string
		fields fields
		want   uint32
	}{
		{name: "测试GetDynamicEffectTemplateUpdateTime",
			fields: fields{
				dynamicEffectTemplateUpdateTime: 10000,
			},
			want: 10000,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentMemCache{
				presentConfigMapNew:             tt.fields.presentConfigMapNew,
				presentConfigMap:                tt.fields.presentConfigMap,
				namingPresentList:               tt.fields.namingPresentList,
				updateLog:                       tt.fields.updateLog,
				lastUpdateTime:                  tt.fields.lastUpdateTime,
				rwMutex:                         tt.fields.rwMutex,
				store:                           tt.fields.store,
				sceneStore:                      tt.fields.sceneStore,
				ticker:                          tt.fields.ticker,
				done:                            tt.fields.done,
				flowConfigMap:                   tt.fields.flowConfigMap,
				flowConfigUpdateTime:            tt.fields.flowConfigUpdateTime,
				dynamicEffectTemplateMap:        tt.fields.dynamicEffectTemplateMap,
				dynamicEffectTemplateUpdateTime: tt.fields.dynamicEffectTemplateUpdateTime,
				presentEffectTemplateConfigMap:  tt.fields.presentEffectTemplateConfigMap,
				presentEffectTemplateUpdateTime: tt.fields.presentEffectTemplateUpdateTime,
			}
			if got := s.GetDynamicEffectTemplateUpdateTime(); got != tt.want {
				t.Errorf("GetDynamicEffectTemplateUpdateTime() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentMemCache_GetFlowConfigById(t *testing.T) {
	type fields struct {
		presentConfigMapNew             map[uint32]*userpresent_go.PresentConfigNew
		presentConfigMap                map[uint32]*userpresent_go.StPresentItemConfig
		namingPresentList               []*userpresent_go.NamingPresentInfo
		updateLog                       []*store.PresentConfigLog
		lastUpdateTime                  uint32
		rwMutex                         sync.RWMutex
		store                           store.IStore
		sceneStore                      store.ISceneStore
		ticker                          *time.Ticker
		done                            chan bool
		flowConfigMap                   map[uint32]*userpresent_go.StPresentFlowConfig
		flowConfigUpdateTime            uint32
		dynamicEffectTemplateMap        map[uint32]*userpresent_go.DynamicEffectTemplate
		dynamicEffectTemplateUpdateTime uint32
		presentEffectTemplateConfigMap  map[uint32]*userpresent_go.PresentEffectTemplateConfig
		presentEffectTemplateUpdateTime uint32
	}
	type args struct {
		flowId uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *userpresent_go.StPresentFlowConfig
	}{
		{
			name: "测试GetFlowConfigById",
			fields: fields{
				flowConfigMap: map[uint32]*userpresent_go.StPresentFlowConfig{
					1: {FlowId: 1},
				},
			},
			args: args{flowId: 1},
			want: &userpresent_go.StPresentFlowConfig{FlowId: 1},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentMemCache{
				presentConfigMapNew:             tt.fields.presentConfigMapNew,
				presentConfigMap:                tt.fields.presentConfigMap,
				namingPresentList:               tt.fields.namingPresentList,
				updateLog:                       tt.fields.updateLog,
				lastUpdateTime:                  tt.fields.lastUpdateTime,
				rwMutex:                         tt.fields.rwMutex,
				store:                           tt.fields.store,
				sceneStore:                      tt.fields.sceneStore,
				ticker:                          tt.fields.ticker,
				done:                            tt.fields.done,
				flowConfigMap:                   tt.fields.flowConfigMap,
				flowConfigUpdateTime:            tt.fields.flowConfigUpdateTime,
				dynamicEffectTemplateMap:        tt.fields.dynamicEffectTemplateMap,
				dynamicEffectTemplateUpdateTime: tt.fields.dynamicEffectTemplateUpdateTime,
				presentEffectTemplateConfigMap:  tt.fields.presentEffectTemplateConfigMap,
				presentEffectTemplateUpdateTime: tt.fields.presentEffectTemplateUpdateTime,
			}
			if got := s.GetFlowConfigById(tt.args.flowId); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFlowConfigById() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentMemCache_GetFlowConfigList(t *testing.T) {
	type fields struct {
		presentConfigMapNew             map[uint32]*userpresent_go.PresentConfigNew
		presentConfigMap                map[uint32]*userpresent_go.StPresentItemConfig
		namingPresentList               []*userpresent_go.NamingPresentInfo
		updateLog                       []*store.PresentConfigLog
		lastUpdateTime                  uint32
		rwMutex                         sync.RWMutex
		store                           store.IStore
		sceneStore                      store.ISceneStore
		ticker                          *time.Ticker
		done                            chan bool
		flowConfigMap                   map[uint32]*userpresent_go.StPresentFlowConfig
		flowConfigUpdateTime            uint32
		dynamicEffectTemplateMap        map[uint32]*userpresent_go.DynamicEffectTemplate
		dynamicEffectTemplateUpdateTime uint32
		presentEffectTemplateConfigMap  map[uint32]*userpresent_go.PresentEffectTemplateConfig
		presentEffectTemplateUpdateTime uint32
	}
	tests := []struct {
		name   string
		fields fields
		want   []*userpresent_go.StPresentFlowConfig
	}{
		{
			name: "测试GetFlowConfigList",
			fields: fields{
				flowConfigMap: map[uint32]*userpresent_go.StPresentFlowConfig{
					1: {FlowId: 1},
				},
			},
			want: []*userpresent_go.StPresentFlowConfig{{FlowId: 1}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentMemCache{
				presentConfigMapNew:             tt.fields.presentConfigMapNew,
				presentConfigMap:                tt.fields.presentConfigMap,
				namingPresentList:               tt.fields.namingPresentList,
				updateLog:                       tt.fields.updateLog,
				lastUpdateTime:                  tt.fields.lastUpdateTime,
				rwMutex:                         tt.fields.rwMutex,
				store:                           tt.fields.store,
				sceneStore:                      tt.fields.sceneStore,
				ticker:                          tt.fields.ticker,
				done:                            tt.fields.done,
				flowConfigMap:                   tt.fields.flowConfigMap,
				flowConfigUpdateTime:            tt.fields.flowConfigUpdateTime,
				dynamicEffectTemplateMap:        tt.fields.dynamicEffectTemplateMap,
				dynamicEffectTemplateUpdateTime: tt.fields.dynamicEffectTemplateUpdateTime,
				presentEffectTemplateConfigMap:  tt.fields.presentEffectTemplateConfigMap,
				presentEffectTemplateUpdateTime: tt.fields.presentEffectTemplateUpdateTime,
			}
			if got := s.GetFlowConfigList(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFlowConfigList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentMemCache_GetPresentEffectTemplateConfigById(t *testing.T) {
	type fields struct {
		presentConfigMapNew             map[uint32]*userpresent_go.PresentConfigNew
		presentConfigMap                map[uint32]*userpresent_go.StPresentItemConfig
		namingPresentList               []*userpresent_go.NamingPresentInfo
		updateLog                       []*store.PresentConfigLog
		lastUpdateTime                  uint32
		rwMutex                         sync.RWMutex
		store                           store.IStore
		sceneStore                      store.ISceneStore
		ticker                          *time.Ticker
		done                            chan bool
		flowConfigMap                   map[uint32]*userpresent_go.StPresentFlowConfig
		flowConfigUpdateTime            uint32
		dynamicEffectTemplateMap        map[uint32]*userpresent_go.DynamicEffectTemplate
		dynamicEffectTemplateUpdateTime uint32
		presentEffectTemplateConfigMap  map[uint32]*userpresent_go.PresentEffectTemplateConfig
		presentEffectTemplateUpdateTime uint32
	}
	type args struct {
		id uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *userpresent_go.PresentEffectTemplateConfig
	}{
		{
			name: "测试GetPresentEffectTemplateConfigById",
			fields: fields{
				presentEffectTemplateConfigMap: map[uint32]*userpresent_go.PresentEffectTemplateConfig{
					1: {Id: 1},
				},
			},
			args: args{id: 1},
			want: &userpresent_go.PresentEffectTemplateConfig{Id: 1},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentMemCache{
				presentConfigMapNew:             tt.fields.presentConfigMapNew,
				presentConfigMap:                tt.fields.presentConfigMap,
				namingPresentList:               tt.fields.namingPresentList,
				updateLog:                       tt.fields.updateLog,
				lastUpdateTime:                  tt.fields.lastUpdateTime,
				rwMutex:                         tt.fields.rwMutex,
				store:                           tt.fields.store,
				sceneStore:                      tt.fields.sceneStore,
				ticker:                          tt.fields.ticker,
				done:                            tt.fields.done,
				flowConfigMap:                   tt.fields.flowConfigMap,
				flowConfigUpdateTime:            tt.fields.flowConfigUpdateTime,
				dynamicEffectTemplateMap:        tt.fields.dynamicEffectTemplateMap,
				dynamicEffectTemplateUpdateTime: tt.fields.dynamicEffectTemplateUpdateTime,
				presentEffectTemplateConfigMap:  tt.fields.presentEffectTemplateConfigMap,
				presentEffectTemplateUpdateTime: tt.fields.presentEffectTemplateUpdateTime,
			}
			if got := s.GetPresentEffectTemplateConfigById(tt.args.id); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPresentEffectTemplateConfigById() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentMemCache_GetPresentEffectTemplateConfigList(t *testing.T) {
	type fields struct {
		presentConfigMapNew             map[uint32]*userpresent_go.PresentConfigNew
		presentConfigMap                map[uint32]*userpresent_go.StPresentItemConfig
		namingPresentList               []*userpresent_go.NamingPresentInfo
		updateLog                       []*store.PresentConfigLog
		lastUpdateTime                  uint32
		rwMutex                         sync.RWMutex
		store                           store.IStore
		sceneStore                      store.ISceneStore
		ticker                          *time.Ticker
		done                            chan bool
		flowConfigMap                   map[uint32]*userpresent_go.StPresentFlowConfig
		flowConfigUpdateTime            uint32
		dynamicEffectTemplateMap        map[uint32]*userpresent_go.DynamicEffectTemplate
		dynamicEffectTemplateUpdateTime uint32
		presentEffectTemplateConfigMap  map[uint32]*userpresent_go.PresentEffectTemplateConfig
		presentEffectTemplateUpdateTime uint32
	}
	tests := []struct {
		name   string
		fields fields
		want   []*userpresent_go.PresentEffectTemplateConfig
	}{
		{
			name: "测试GetPresentEffectTemplateConfigList",
			fields: fields{
				presentEffectTemplateConfigMap: map[uint32]*userpresent_go.PresentEffectTemplateConfig{
					1: {Id: 1},
				},
			},
			want: []*userpresent_go.PresentEffectTemplateConfig{{Id: 1}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentMemCache{
				presentConfigMapNew:             tt.fields.presentConfigMapNew,
				presentConfigMap:                tt.fields.presentConfigMap,
				namingPresentList:               tt.fields.namingPresentList,
				updateLog:                       tt.fields.updateLog,
				lastUpdateTime:                  tt.fields.lastUpdateTime,
				rwMutex:                         tt.fields.rwMutex,
				store:                           tt.fields.store,
				sceneStore:                      tt.fields.sceneStore,
				ticker:                          tt.fields.ticker,
				done:                            tt.fields.done,
				flowConfigMap:                   tt.fields.flowConfigMap,
				flowConfigUpdateTime:            tt.fields.flowConfigUpdateTime,
				dynamicEffectTemplateMap:        tt.fields.dynamicEffectTemplateMap,
				dynamicEffectTemplateUpdateTime: tt.fields.dynamicEffectTemplateUpdateTime,
				presentEffectTemplateConfigMap:  tt.fields.presentEffectTemplateConfigMap,
				presentEffectTemplateUpdateTime: tt.fields.presentEffectTemplateUpdateTime,
			}
			if got := s.GetPresentEffectTemplateConfigList(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPresentEffectTemplateConfigList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentMemCache_GetValidNamingPresent(t *testing.T) {
	type fields struct {
		presentConfigMapNew             map[uint32]*userpresent_go.PresentConfigNew
		presentConfigMap                map[uint32]*userpresent_go.StPresentItemConfig
		namingPresentList               []*userpresent_go.NamingPresentInfo
		updateLog                       []*store.PresentConfigLog
		lastUpdateTime                  uint32
		rwMutex                         sync.RWMutex
		store                           store.IStore
		sceneStore                      store.ISceneStore
		ticker                          *time.Ticker
		done                            chan bool
		flowConfigMap                   map[uint32]*userpresent_go.StPresentFlowConfig
		flowConfigUpdateTime            uint32
		dynamicEffectTemplateMap        map[uint32]*userpresent_go.DynamicEffectTemplate
		dynamicEffectTemplateUpdateTime uint32
		presentEffectTemplateConfigMap  map[uint32]*userpresent_go.PresentEffectTemplateConfig
		presentEffectTemplateUpdateTime uint32
	}
	tests := []struct {
		name   string
		fields fields
		want   []*userpresent_go.NamingPresentInfo
	}{
		{
			name: "测试GetValidNamingPresent",
			fields: fields{
				namingPresentList: []*userpresent_go.NamingPresentInfo{
					{Id: 1, GiftId: 1, Uid: 1, EndTs: 1809200215},
				},
			},
			want: []*userpresent_go.NamingPresentInfo{{Id: 1, GiftId: 1, Uid: 1, EndTs: 1809200215}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentMemCache{
				presentConfigMapNew:             tt.fields.presentConfigMapNew,
				presentConfigMap:                tt.fields.presentConfigMap,
				namingPresentList:               tt.fields.namingPresentList,
				updateLog:                       tt.fields.updateLog,
				lastUpdateTime:                  tt.fields.lastUpdateTime,
				rwMutex:                         tt.fields.rwMutex,
				store:                           tt.fields.store,
				sceneStore:                      tt.fields.sceneStore,
				ticker:                          tt.fields.ticker,
				done:                            tt.fields.done,
				flowConfigMap:                   tt.fields.flowConfigMap,
				flowConfigUpdateTime:            tt.fields.flowConfigUpdateTime,
				dynamicEffectTemplateMap:        tt.fields.dynamicEffectTemplateMap,
				dynamicEffectTemplateUpdateTime: tt.fields.dynamicEffectTemplateUpdateTime,
				presentEffectTemplateConfigMap:  tt.fields.presentEffectTemplateConfigMap,
				presentEffectTemplateUpdateTime: tt.fields.presentEffectTemplateUpdateTime,
			}
			if got := s.GetValidNamingPresent(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetValidNamingPresent() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPresentMemCache_UpdateDynamicEffectTemplate(t *testing.T) {
	storeMock := store.NewMockIStore(gomock.NewController(t))
	storeMock.EXPECT().GetPresentEffectTemplateConfigList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*userpresent_go.PresentEffectTemplateConfig{{Id: 1}}, uint32(1), nil).AnyTimes()
	storeMock.EXPECT().GetPresentEffectTemplateConfigUpdateTime(gomock.Any()).Return(uint32(1000), nil).AnyTimes()
	storeMock.EXPECT().GetDynamicEffectTemplateList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*userpresent_go.DynamicEffectTemplate{{Id: 1}}, uint32(1), nil).AnyTimes()
	storeMock.EXPECT().GetDynamicEffectTemplateUpdateTime(gomock.Any()).Return(uint32(1000), nil).AnyTimes()

	type fields struct {
		presentConfigMapNew             map[uint32]*userpresent_go.PresentConfigNew
		presentConfigMap                map[uint32]*userpresent_go.StPresentItemConfig
		namingPresentList               []*userpresent_go.NamingPresentInfo
		updateLog                       []*store.PresentConfigLog
		lastUpdateTime                  uint32
		rwMutex                         sync.RWMutex
		store                           store.IStore
		sceneStore                      store.ISceneStore
		ticker                          *time.Ticker
		done                            chan bool
		flowConfigMap                   map[uint32]*userpresent_go.StPresentFlowConfig
		flowConfigUpdateTime            uint32
		dynamicEffectTemplateMap        map[uint32]*userpresent_go.DynamicEffectTemplate
		dynamicEffectTemplateUpdateTime uint32
		presentEffectTemplateConfigMap  map[uint32]*userpresent_go.PresentEffectTemplateConfig
		presentEffectTemplateUpdateTime uint32
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "测试UpdateDynamicEffectTemplate",
			fields: fields{
				store: storeMock,
				dynamicEffectTemplateMap: map[uint32]*userpresent_go.DynamicEffectTemplate{
					1: {Id: 1},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentMemCache{
				presentConfigMapNew:             tt.fields.presentConfigMapNew,
				presentConfigMap:                tt.fields.presentConfigMap,
				namingPresentList:               tt.fields.namingPresentList,
				updateLog:                       tt.fields.updateLog,
				lastUpdateTime:                  tt.fields.lastUpdateTime,
				rwMutex:                         tt.fields.rwMutex,
				store:                           tt.fields.store,
				sceneStore:                      tt.fields.sceneStore,
				ticker:                          tt.fields.ticker,
				done:                            tt.fields.done,
				flowConfigMap:                   tt.fields.flowConfigMap,
				flowConfigUpdateTime:            tt.fields.flowConfigUpdateTime,
				dynamicEffectTemplateMap:        tt.fields.dynamicEffectTemplateMap,
				dynamicEffectTemplateUpdateTime: tt.fields.dynamicEffectTemplateUpdateTime,
				presentEffectTemplateConfigMap:  tt.fields.presentEffectTemplateConfigMap,
				presentEffectTemplateUpdateTime: tt.fields.presentEffectTemplateUpdateTime,
			}
			s.UpdateDynamicEffectTemplate()
		})
	}
}

func TestPresentMemCache_UpdateFlowConfig(t *testing.T) {
	storeMock := store.NewMockIStore(gomock.NewController(t))
	storeMock.EXPECT().GetPresentFlowConfigList(gomock.Any()).Return([]*userpresent_go.StPresentFlowConfig{{FlowId: 1}}, nil).AnyTimes()
	storeMock.EXPECT().GetPresentFlowConfigUpdateTime(gomock.Any()).Return(uint32(1000), nil).AnyTimes()

	type fields struct {
		presentConfigMapNew             map[uint32]*userpresent_go.PresentConfigNew
		presentConfigMap                map[uint32]*userpresent_go.StPresentItemConfig
		namingPresentList               []*userpresent_go.NamingPresentInfo
		updateLog                       []*store.PresentConfigLog
		lastUpdateTime                  uint32
		rwMutex                         sync.RWMutex
		store                           store.IStore
		sceneStore                      store.ISceneStore
		ticker                          *time.Ticker
		done                            chan bool
		flowConfigMap                   map[uint32]*userpresent_go.StPresentFlowConfig
		flowConfigUpdateTime            uint32
		dynamicEffectTemplateMap        map[uint32]*userpresent_go.DynamicEffectTemplate
		dynamicEffectTemplateUpdateTime uint32
		presentEffectTemplateConfigMap  map[uint32]*userpresent_go.PresentEffectTemplateConfig
		presentEffectTemplateUpdateTime uint32
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "测试UpdateFlowConfig",
			fields: fields{
				store: storeMock,
				flowConfigMap: map[uint32]*userpresent_go.StPresentFlowConfig{
					1: {FlowId: 1},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentMemCache{
				presentConfigMapNew:             tt.fields.presentConfigMapNew,
				presentConfigMap:                tt.fields.presentConfigMap,
				namingPresentList:               tt.fields.namingPresentList,
				updateLog:                       tt.fields.updateLog,
				lastUpdateTime:                  tt.fields.lastUpdateTime,
				rwMutex:                         tt.fields.rwMutex,
				store:                           tt.fields.store,
				sceneStore:                      tt.fields.sceneStore,
				ticker:                          tt.fields.ticker,
				done:                            tt.fields.done,
				flowConfigMap:                   tt.fields.flowConfigMap,
				flowConfigUpdateTime:            tt.fields.flowConfigUpdateTime,
				dynamicEffectTemplateMap:        tt.fields.dynamicEffectTemplateMap,
				dynamicEffectTemplateUpdateTime: tt.fields.dynamicEffectTemplateUpdateTime,
				presentEffectTemplateConfigMap:  tt.fields.presentEffectTemplateConfigMap,
				presentEffectTemplateUpdateTime: tt.fields.presentEffectTemplateUpdateTime,
			}
			s.UpdateFlowConfig()
		})
	}
}
