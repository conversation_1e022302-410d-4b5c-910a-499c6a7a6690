package manager

import (
	"context"
	"fmt"
	"runtime/debug"
	"time"

	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/urrc"
	"golang.52tt.com/protocol/common/status"
	AnchorLevel "golang.52tt.com/protocol/services/anchor-level"
	pb "golang.52tt.com/protocol/services/anchorcontract-go"
	"golang.52tt.com/protocol/services/rcmd/operating_platform"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/cache"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/conf"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql"
)

var (
	tagId2Name = map[uint32]string{
		3001: "音乐",
		3002: "情感",
		3003: "二次元",
		3004: "故事",
	}
	TagIdTypeMusic        = uint32(3001) // 音乐品类
	TagIdTypeEmotion      = uint32(3002) // 情感
	TagIdTypeTwoDimension = uint32(3003) // 二次元
	TagIdTypeStory        = uint32(3004) // 故事
)

// 查询考核认证标识信息
func (m *AnchorContractMgr) ListExamineCert(ctx context.Context, in *pb.ListExamineCertReq) (*pb.ListExamineCertResp, error) {
	out := new(pb.ListExamineCertResp)

	if in.GetIdentityType() != uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_MULTIPLAYER) &&
		in.GetIdentityType() != uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_RADIO_LIVE) {
		return out, nil
	}

	query := ""
	if in.GetItemId() > 0 {
		query = fmt.Sprintf("item_id=%d", in.GetItemId())
	} else if in.GetItemName() != "" {
		query = fmt.Sprintf("item_name='%s'", in.GetItemName())
	} else if in.GetItemType() != "" {
		query = fmt.Sprintf("item_type='%s'", in.GetItemType())
	} else {
		query = ""
	}

	if query == "" {
		log.DebugWithCtx(ctx, "mgr.ListExamineCert query null")
		return out, nil
	}

	query += fmt.Sprintf(" and identity_type=%d ", in.GetIdentityType())

	list, err := m.store.GetExamineCertByQuery(query)
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.ListExamineCert failed to store.GetExamineCertByQuery, err:%s, in:%+v", err.Error(), in)
		return out, err
	}

	certList := make([]*pb.ExamineCertInfo, 0, len(list))
	for _, cert := range list {
		certList = append(certList, &pb.ExamineCertInfo{
			ItemId:       cert.ItemId,
			ParentItemId: cert.ParentItemId,
			IdentityType: cert.IdentityType,
			ItemType:     cert.ItemType,
			ItemName:     cert.ItemName,
			BaseImgurl:   cert.BaseImgurl,
			ItemLevel:    cert.ItemLevel,
			ShadowColor:  cert.ShadowColor,
			StartTime:    uint32(cert.StartTime.Unix()),
			EndTime:      uint32(cert.EndTime.Unix()),
			UpdateTime:   uint32(cert.UpdateTime.Unix()),
		})
	}

	out.List = certList
	log.DebugWithCtx(ctx, "ListExamineCert in:%+v, out:%+v", in, out)
	return out, nil
}

// 分页查询父级考核标识
func (m *AnchorContractMgr) GetParentExamineCertList(ctx context.Context, in *pb.CertOffsetTypeReq) (*pb.ParentExamineCertList, error) {
	out := new(pb.ParentExamineCertList)

	if in.GetIdentityType() != uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_MULTIPLAYER) &&
		in.GetIdentityType() != uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_RADIO_LIVE) {
		return out, nil
	}

	list, err := m.store.GetParentExamineCertList(in.GetOffset(), in.GetLimit(), in.GetIdentityType())
	if err != nil {
		log.ErrorWithCtx(ctx, "server failed to store.GetParentExamineCertList, err:%s, in:%+v", err.Error(), in)
		return out, err
	}

	total, err := m.store.GetParentExamineCertCount(in.GetIdentityType())
	if err != nil {
		log.ErrorWithCtx(ctx, "server failed to store.GetParentExamineCertCount, err:%s, in:%+v", err.Error(), in)
		return out, err
	}

	certList := make([]*pb.ExamineCertInfo, 0, len(list))
	for _, cert := range list {
		certList = append(certList, &pb.ExamineCertInfo{
			ItemId:       cert.ItemId,
			ParentItemId: cert.ParentItemId,
			IdentityType: cert.IdentityType,
			ItemType:     cert.ItemType,
			ItemName:     cert.ItemName,
			BaseImgurl:   cert.BaseImgurl,
			ItemLevel:    cert.ItemLevel,
			ShadowColor:  cert.ShadowColor,
			StartTime:    uint32(cert.StartTime.Unix()),
			EndTime:      uint32(cert.EndTime.Unix()),
			UpdateTime:   uint32(cert.UpdateTime.Unix()),
		})
	}

	out.Total = total
	out.List = certList
	log.DebugWithCtx(ctx, "GetParentExamineCertList in:%+v, out:%+v", in, out)
	return out, nil
}

// 查看父级下面的子考核标识
func (m *AnchorContractMgr) GetChildExamineCertList(ctx context.Context, in *pb.CertItemReq) (*pb.ChildExamineCertList, error) {
	out := new(pb.ChildExamineCertList)

	if in.GetItemId() == 0 {
		return out, nil
	}

	list, err := m.store.GetChildExamineCertList(in.GetItemId())
	if err != nil {
		log.ErrorWithCtx(ctx, "server failed to store.GetChildExamineCertList, err:%s, in:%+v", err.Error(), in)
		return out, err
	}

	certList := make([]*pb.ExamineCertInfo, 0, len(list))
	for _, cert := range list {
		certList = append(certList, &pb.ExamineCertInfo{
			ItemId:       cert.ItemId,
			ParentItemId: cert.ParentItemId,
			IdentityType: cert.IdentityType,
			ItemType:     cert.ItemType,
			ItemName:     cert.ItemName,
			BaseImgurl:   cert.BaseImgurl,
			ItemLevel:    cert.ItemLevel,
			ShadowColor:  cert.ShadowColor,
			StartTime:    uint32(cert.StartTime.Unix()),
			EndTime:      uint32(cert.EndTime.Unix()),
			UpdateTime:   uint32(cert.UpdateTime.Unix()),
		})
	}

	out.List = certList
	log.DebugWithCtx(ctx, "GetChildExamineCertList in:%+v, out:%+v", in, out)
	return out, nil
}

var (
	ErrInvalidParamer      = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "参数不能为空")
	ErrInvalidParentCert   = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "父级标识等级只能为1")
	ErrInvalidChildCert    = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "子级标识等级不能为1")
	ErrInvalidTime         = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "上架/下架时间有误")
	ErrInvalidIdentityType = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "考核标识身份类型错误")
	ErrCertTypeExist       = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "当前配置的标识类型已经存在,不可重复配置该标识类型")
	ErrNoFindParentCert    = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "父级标识不存在")
	ErrNoFindCert          = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "标识不存在")
	ErrNoSameCert          = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "父子级标识类型不一致")
	ErrInvalidGrantTime    = protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "必须在标识上下架时间内发放")
)

// 新增父级考核标识配置
func (m *AnchorContractMgr) AddParentExamineCert(ctx context.Context, in *pb.ExamineCertInfo) (out *pb.CertEmptyMsg, err error) {
	out = new(pb.CertEmptyMsg)
	defer func() {
		log.InfoWithCtx(ctx, "AnchorContractMgr.AddParentExamineCert in:%+v, out:%+v, err:%+v", in, out, err)
	}()

	if in.GetStartTime() == 0 || in.GetEndTime() == 0 || len(in.GetItemType()) == 0 {
		return out, ErrInvalidParamer
	}
	if len(in.GetItemName()) == 0 || len(in.GetShadowColor()) == 0 {
		return out, ErrInvalidParamer
	}
	if in.GetItemLevel() != 1 {
		return out, ErrInvalidParentCert
	}
	if in.GetStartTime() >= in.GetEndTime() || uint32(time.Now().Unix()) >= in.GetEndTime() {
		return out, ErrInvalidTime
	}
	if in.GetIdentityType() != uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_MULTIPLAYER) &&
		in.GetIdentityType() != uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_RADIO_LIVE) {
		return out, ErrInvalidIdentityType
	}

	cnt, err := m.store.GetParentExamineCertByItemType(in.GetItemType())
	if err != nil {
		return out, err
	}
	if cnt > 0 {
		return out, ErrCertTypeExist
	}

	err = m.store.AddParentExamineCert(in.GetIdentityType(), in.GetItemType(), in.GetItemName(),
		in.GetBaseImgurl(), in.GetShadowColor(), in.GetItemLevel(), in.GetStartTime(), in.GetEndTime())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.AddParentExamineCert failed to store.AddParentExamineCert, err:%s, in:%+v", err.Error(), in)
		return out, err
	}

	return out, nil
}

// 修改父级考核标识配置
func (m *AnchorContractMgr) UpdateParentExamineCert(ctx context.Context, in *pb.ExamineCertInfo) (out *pb.CertEmptyMsg, err error) {
	out = new(pb.CertEmptyMsg)
	defer func() {
		log.InfoWithCtx(ctx, "AnchorContractMgr.UpdateParentExamineCert in:%+v, out:%+v, err:%+v", in, out, err)
	}()

	if in.GetItemId() == 0 || in.GetStartTime() == 0 || in.GetEndTime() == 0 {
		return out, ErrInvalidParamer
	}
	if len(in.GetItemName()) == 0 || len(in.GetBaseImgurl()) == 0 || len(in.GetShadowColor()) == 0 {
		return out, ErrInvalidParamer
	}
	if in.GetStartTime() >= in.GetEndTime() {
		return out, ErrInvalidTime
	}

	info, err := m.store.GetExamineCert(in.GetItemId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.UpdateParentExamineCert failed to store.GetExamineCert err:%s, in:%+v", err, in)
		return out, err
	}
	if info.ItemId == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "标识不存在")
	}

	err = m.store.UpdateExamineCert(in.GetItemId(), in.GetItemName(), in.GetBaseImgurl(), in.GetShadowColor(), in.GetStartTime(), in.GetEndTime())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.UpdateParentExamineCert failed to store.UpdateExamineCert err:%s, in:%+v", err, in)
		return out, err
	}

	if uint32(info.StartTime.Unix()) != in.GetStartTime() || uint32(info.EndTime.Unix()) != in.GetEndTime() {
		childList, err := m.store.GetChildExamineCertList(in.GetItemId())
		if err != nil {
			log.ErrorWithCtx(ctx, "server failed to store.GetChildExamineCertList, err:%s, in:%+v", err.Error(), in)
			return out, err
		}
		for _, cert := range childList {
			err = m.store.UpdateExamineCert(cert.ItemId, cert.ItemName, cert.BaseImgurl, cert.ShadowColor, in.GetStartTime(), in.GetEndTime())
			if err != nil {
				log.ErrorWithCtx(ctx, "AnchorContractMgr.UpdateParentExamineCert failed to store.UpdateExamineCert err:%s, in:%+v", err, in)
				return out, err
			}
		}
	}

	_ = m.DelUserExamineCertCache(in.GetItemId())

	return out, nil
}

// 删除父级考核标识配置
func (m *AnchorContractMgr) DeleteParentExamineCert(ctx context.Context, in *pb.CertItemReq) (out *pb.CertEmptyMsg, err error) {
	out = new(pb.CertEmptyMsg)
	defer func() {
		log.InfoWithCtx(ctx, "AnchorContractMgr.DeleteParentExamineCert in:%+v, out:%+v, err:%+v", in, out, err)
	}()

	if in.GetItemId() == 0 {
		return out, ErrInvalidParamer
	}

	list, err := m.store.GetUserExamineCertListByItemId(in.GetItemId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.DeleteParentExamineCert failed to store.GetUserExamineCertListByItemId, err:%s, in:%+v", err.Error(), in)
		return out, err
	}
	if len(list) > 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "该标识有用户正在使用,不能直接删除")
	}

	childList, err := m.store.GetChildExamineCertList(in.GetItemId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.DeleteParentExamineCert failed to store.GetChildExamineCertList, err:%s, in:%+v", err.Error(), in)
		return out, err
	}
	if len(childList) > 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不能越过子级删除父级标识")
	}

	err = m.store.DeleteExamineCert(in.GetItemId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.DeleteParentExamineCert failed to store.DeleteExamineCert, err:%s, in:%+v", err.Error(), in)
		return out, err
	}

	return out, nil
}

// 新增子级考核标识配置
func (m *AnchorContractMgr) AddChildExamineCert(ctx context.Context, in *pb.ExamineCertInfo) (out *pb.CertEmptyMsg, err error) {
	out = new(pb.CertEmptyMsg)
	defer func() {
		log.InfoWithCtx(ctx, "AnchorContractMgr.AddChildExamineCert in:%+v, out:%+v, err:%+v", in, out, err)
	}()

	if in.GetStartTime() == 0 || in.GetEndTime() == 0 || len(in.GetItemType()) == 0 {
		return out, ErrInvalidParamer
	}
	if len(in.GetItemName()) == 0 || in.GetParentItemId() == 0 || len(in.GetShadowColor()) == 0 {
		return out, ErrInvalidParamer
	}
	if in.GetItemLevel() == 1 {
		return out, ErrInvalidChildCert
	}
	if in.GetStartTime() >= in.GetEndTime() || uint32(time.Now().Unix()) >= in.GetEndTime() {
		return out, ErrInvalidTime
	}
	if in.GetIdentityType() != uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_MULTIPLAYER) &&
		in.GetIdentityType() != uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_RADIO_LIVE) {
		return out, ErrInvalidIdentityType
	}

	parentCert, err := m.store.GetExamineCert(in.GetParentItemId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.AddChildExamineCert failed to store.GetExamineCert, err:%s, in:%+v", err.Error(), in)
		return out, err
	}
	if parentCert.ItemId == 0 {
		return out, ErrNoFindParentCert
	}
	if parentCert.IdentityType != in.GetIdentityType() || parentCert.ItemType != in.GetItemType() {
		return out, ErrNoSameCert
	}

	childList, err := m.store.GetChildExamineCertList(in.GetParentItemId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.AddChildExamineCert failed to store.GetChildExamineCertList, err:%s, in:%+v", err.Error(), in)
		return out, err
	}
	if len(childList) > 0 && childList[len(childList)-1].ItemLevel+1 != in.GetItemLevel() {
		log.ErrorWithCtx(ctx, "AddChildExamineCert failed in: %+v, childList:%+v", in, childList)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "子级标识等级错误,该等级已存在")
	}
	if len(childList) == 0 && in.GetItemLevel() != 2 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "子级标识等级错误")
	}

	err = m.store.AddChildExamineCert(in.GetParentItemId(), in.GetIdentityType(),
		in.GetItemLevel(), in.GetItemType(), in.GetItemName(),
		in.GetBaseImgurl(), in.GetShadowColor(), uint32(parentCert.StartTime.Unix()), uint32(parentCert.EndTime.Unix()))
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.AddChildExamineCert failed to store.AddChildExamineCert, err:%s, in:%+v", err.Error(), in)
		return out, err
	}

	return out, nil
}

// 修改子级考核标识配置
func (m *AnchorContractMgr) UpdateChildExamineCert(ctx context.Context, in *pb.ExamineCertInfo) (out *pb.CertEmptyMsg, err error) {
	out = new(pb.CertEmptyMsg)
	defer func() {
		log.InfoWithCtx(ctx, "AnchorContractMgr.UpdateChildExamineCert in:%+v, out:%+v, err:%+v", in, out, err)
	}()

	if in.GetItemId() == 0 || in.GetStartTime() == 0 || in.GetEndTime() == 0 {
		return out, ErrInvalidIdentityType
	}
	if len(in.GetItemName()) == 0 || len(in.GetBaseImgurl()) == 0 || len(in.GetShadowColor()) == 0 {
		return out, ErrInvalidIdentityType
	}
	if in.GetStartTime() >= in.GetEndTime() {
		return out, ErrInvalidTime
	}

	info, err := m.store.GetExamineCert(in.GetItemId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.UpdateChildExamineCert failed to store.GetExamineCert err:%s, in:%+v", err, in)
		return out, err
	}
	if info.ItemId == 0 {
		return out, ErrNoFindCert
	}

	err = m.store.UpdateExamineCert(in.GetItemId(), in.GetItemName(), in.GetBaseImgurl(), in.GetShadowColor(), uint32(info.StartTime.Unix()), uint32(info.EndTime.Unix()))
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.UpdateChildExamineCert failed to store.UpdateExamineCert err:%s, in:%+v", err, in)
		return out, err
	}

	_ = m.DelUserExamineCertCache(in.GetItemId())

	return out, nil
}

// 删除子级考核标识配置
func (m *AnchorContractMgr) DeleteChildExamineCert(ctx context.Context, in *pb.CertItemReq) (out *pb.CertEmptyMsg, err error) {
	out = new(pb.CertEmptyMsg)
	defer func() {
		log.InfoWithCtx(ctx, "AnchorContractMgr.DeleteChildExamineCert in:%+v, out:%+v, err:%+v", in, out, err)
	}()

	if in.GetItemId() == 0 {
		return out, ErrInvalidParamer
	}

	anchorCerts, err := m.store.GetUserExamineCertListByItemId(in.GetItemId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.DeleteParentExamineCert failed to store.GetUserExamineCertListByItemId, err:%s, in:%+v", err.Error(), in)
		return out, err
	}
	if len(anchorCerts) > 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "该标识有用户正在使用,不能直接删除")
	}

	list, err := m.store.GetChildExamineCertListByChildId(in.GetItemId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.DeleteChildExamineCert failed to GetChildExamineCertListByChildId err:%s, in:%+v", err, in)
		return out, err
	}
	if len(list) == 0 {
		return out, ErrNoFindCert
	}
	if list[len(list)-1].ItemId != in.GetItemId() {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "不能越过子级删除父级标识")
	}

	err = m.store.DeleteExamineCert(in.GetItemId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.DeleteChildExamineCert failed to store.DeleteExamineCert, err:%s, in:%+v", err.Error(), in)
		return out, err
	}

	return out, nil
}

// 单个用户发放标识
func (m *AnchorContractMgr) SetUserExamineCert(ctx context.Context, in *pb.UserExamineCertInfo) (out *pb.CertEmptyMsg, err error) {
	out = new(pb.CertEmptyMsg)
	defer func() {
		log.InfoWithCtx(ctx, "AnchorContractMgr.SetUserExamineCert in:%+v, out:%+v, err:%+v", in, out, err)
	}()

	now := uint32(time.Now().Unix())
	if in.GetStartTime() == 0 || in.GetEndTime() == 0 || in.GetItemId() == 0 || in.GetUid() == 0 {
		return out, ErrInvalidParamer
	}
	if in.GetIdentityType() != uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_MULTIPLAYER) &&
		in.GetIdentityType() != uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_RADIO_LIVE) {
		return out, ErrInvalidIdentityType
	}
	if in.GetStartTime() >= in.GetEndTime() || now >= in.GetEndTime() {
		return out, ErrInvalidTime
	}

	info, err := m.store.GetExamineCert(in.GetItemId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.SetUserExamineCert failed to store.GetExamineCert err:%+v, in:%+v", err, in)
		return out, err
	}
	if info.ItemId == 0 {
		return out, ErrNoFindCert
	}
	if info.IdentityType != in.GetIdentityType() {
		return out, ErrNoSameCert
	}
	if in.GetStartTime() < uint32(info.StartTime.Unix()) || in.GetEndTime() >= uint32(info.EndTime.Unix()) {
		return out, ErrInvalidGrantTime
	}

	if m.checkUserExamineCert(in.GetUid(), in.GetIdentityType(), in.GetStartTime(), in.GetEndTime()) {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "同一时间段只允许有一个同类考核标识")
	}

	id, err := m.store.SetUserExamineCert(nil, in.GetUid(), in.GetItemId(), in.GetIdentityType(), "", in.GetHandler(), in.GetStartTime(), in.GetEndTime())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.SetUserExamineCert failed to store.SetUserExamineCert, err:%s, in:%+v", err.Error(), in)
		return out, err
	}

	err = m.cache.PushExamineNotifyQueue([]cache.PushExamineCertMsg{
		{Uid: in.GetUid(), RecordId: id, PushTs: in.GetStartTime(), NotifyType: cache.AnchorCertNotifyGrant},
		{Uid: in.GetUid(), RecordId: id, PushTs: in.GetEndTime(), NotifyType: cache.AnchorCertNotifyRecycle},
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.SetUserExamineCert failed to cache.PushExamineNotifyQueue, err:%s, in:%+v", err.Error(), in)
	}

	return out, nil
}

// 批量检查用户发放标识
func (m *AnchorContractMgr) BatchCheckUserExamineCert(ctx context.Context, in *pb.BatchSetUserExamineCertReq) (out *pb.BatchSetUserExamineCertResp, err error) {
	out = new(pb.BatchSetUserExamineCertResp)
	uids := []uint32{}
	defer func() {
		log.InfoWithCtx(ctx, "AnchorContractMgr.BatchCheckUserExamineCert in:%+v, out:%+v, add_uids:%v, err:%+v", in, out, uids, err)
	}()

	if len(in.GetUidList()) == 0 {
		return out, nil
	}
	if in.GetStartTime() == 0 || in.GetEndTime() == 0 || in.GetItemId() == 0 {
		return out, ErrInvalidParamer
	}
	if in.GetIdentityType() != uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_MULTIPLAYER) &&
		in.GetIdentityType() != uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_RADIO_LIVE) {
		return out, ErrCertTypeExist
	}
	if in.GetStartTime() >= in.GetEndTime() || uint32(time.Now().Unix()) >= in.GetEndTime() {
		return out, ErrInvalidTime
	}

	info, err := m.store.GetExamineCert(in.GetItemId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.BatchCheckUserExamineCert failed to store.GetExamineCert err:%+v, in:%+v", err, in)
		return out, err
	}
	if info.ItemId == 0 {
		return out, ErrNoFindCert
	}
	if info.IdentityType != in.GetIdentityType() {
		return out, ErrNoSameCert
	}
	if in.GetStartTime() < uint32(info.StartTime.Unix()) || in.GetEndTime() >= uint32(info.EndTime.Unix()) {
		return out, ErrInvalidGrantTime
	}

	for _, uid := range in.GetUidList() {
		if uid == 0 {
			continue
		}
		if m.checkUserExamineCert(uid, in.GetIdentityType(), in.GetStartTime(), in.GetEndTime()) {
			log.ErrorWithCtx(ctx, "AnchorContractMgr.BatchCheckUserExamineCert checkUserExamineCert uid %d, identityType %d startTime %d endTime %d",
				uid, in.GetIdentityType(), in.GetStartTime(), in.GetEndTime())
			out.ConflictUids = append(out.ConflictUids, uid)
			//return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "同一时间段只允许有一个同类考核标识")
		}
	}
	if len(out.ConflictUids) > 0 {
		log.InfoWithCtx(ctx, "BatchCheckUserExamineCert ConflictUids=%v", out.ConflictUids)
		//return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "同一时间段只允许有一个同类考核标识")
	}
	return out, nil
}

// 批量用户发放标识
func (m *AnchorContractMgr) BatchSetUserExamineCert(ctx context.Context, in *pb.BatchSetUserExamineCertReq) (*pb.CertEmptyMsg, error) {
	out := new(pb.CertEmptyMsg)
	uids := []uint32{}
	defer func() {
		log.InfoWithCtx(ctx, "AnchorContractMgr.BatchSetUserExamineCert in:%+v, out:%+v, add_uids:%v", in, out, uids)
	}()

	if len(in.GetUidList()) == 0 {
		return out, nil
	}
	if in.GetStartTime() == 0 || in.GetEndTime() == 0 || in.GetItemId() == 0 {
		return out, ErrInvalidParamer
	}
	if in.GetIdentityType() != uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_MULTIPLAYER) &&
		in.GetIdentityType() != uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_RADIO_LIVE) {
		return out, ErrInvalidIdentityType
	}
	if in.GetStartTime() >= in.GetEndTime() || uint32(time.Now().Unix()) >= in.GetEndTime() {
		return out, ErrInvalidTime
	}

	info, err := m.store.GetExamineCert(in.GetItemId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.BatchSetUserExamineCert failed to store.GetExamineCert err:%+v, in:%+v", err, in)
		return out, err
	}
	if info.ItemId == 0 {
		return out, ErrNoFindCert
	}
	if info.IdentityType != in.GetIdentityType() {
		return out, ErrNoSameCert
	}
	if in.GetStartTime() < uint32(info.StartTime.Unix()) || in.GetEndTime() > uint32(info.EndTime.Unix()) {
		return out, ErrInvalidGrantTime
	}

	pushQueue := []cache.PushExamineCertMsg{}
	for _, uid := range in.GetUidList() {
		if uid == 0 {
			continue
		}
		if m.checkUserExamineCert(uid, in.GetIdentityType(), in.GetStartTime(), in.GetEndTime()) {
			log.ErrorWithCtx(ctx, "AnchorContractMgr.BatchSetUserExamineCert checkUserExamineCert uid %d, identityType %d startTime %d endTime %d",
				uid, in.GetIdentityType(), in.GetStartTime(), in.GetEndTime())
			continue
		}

		id, err := m.store.SetUserExamineCert(nil, uid, in.GetItemId(), in.GetIdentityType(), "", in.GetHandler(), in.GetStartTime(), in.GetEndTime())
		if err != nil {
			log.ErrorWithCtx(ctx, "AnchorContractMgr.BatchSetUserExamineCert failed to store.SetUserExamineCert, err:%s, uid:%d, itemId:%d", err.Error(), uid, in.GetItemId())
			return out, err
		}

		uids = append(uids, uid)
		pushQueue = append(pushQueue,
			cache.PushExamineCertMsg{Uid: uid, RecordId: id, PushTs: in.GetStartTime(), NotifyType: cache.AnchorCertNotifyGrant},
			cache.PushExamineCertMsg{Uid: uid, RecordId: id, PushTs: in.GetEndTime(), NotifyType: cache.AnchorCertNotifyRecycle},
		)
	}

	log.InfoWithCtx(ctx, "BatchSetUserExamineCert push %+v", pushQueue)
	err = m.cache.PushExamineNotifyQueue(pushQueue)
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.BatchSetUserExamineCert failed to cache.PushMemToQueue, err:%s, in:%+v", err.Error(), in)
	}

	return out, nil
}

// 编辑用户标识
func (m *AnchorContractMgr) UpdateUserExamineCert(ctx context.Context, in *pb.UserExamineCertInfo) (out *pb.CertEmptyMsg, err error) {
	out = new(pb.CertEmptyMsg)
	defer func() {
		log.InfoWithCtx(ctx, "AnchorContractMgr.UpdateUserExamineCert in:%+v, out:%+v, err:%+v", in, out, err)
	}()

	if in.GetRecordId() == 0 || in.GetItemId() == 0 {
		return out, ErrInvalidParamer
	}

	cert, err := m.store.GetUserExamineCertByRecordId(in.GetRecordId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.UpdateUserExamineCert failed to store.GetUserExamineCertByRecordId, err:%s, recordId:%d", err.Error(), in.GetRecordId())
		return out, err
	}
	if cert.Id == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "用户标识记录不存在")
	}
	if cert.Status != uint32(pb.UserExamineCert_Status_UserExamineCert_Status_Grant) && cert.Status != uint32(pb.UserExamineCert_Status_UserExamineCert_Status_Effect) {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "只允许编辑在【已发放/已生效】状态下的用户标识")
	}

	newCertInfo, err := m.store.GetExamineCert(in.GetItemId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.UpdateUserExamineCert failed to store.GetExamineCert, err:%s, ItemId:%d", err.Error(), in.GetItemId())
		return out, err
	}
	if newCertInfo.ItemId == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "更新后的标识不存在")
	}

	info, err := m.store.GetExamineCert(cert.ItemId)
	if err != nil {
		return out, err
	}
	if newCertInfo.ItemType != info.ItemType {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "标识类型不一致")
	}
	if newCertInfo.ItemLevel == info.ItemLevel {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "标识等级不能一样")
	}

	newItemId := newCertInfo.ItemId
	err = m.store.UpdatetUserExamineCertItemId(in.GetRecordId(), newItemId)
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.UpdateUserExamineCert failed to store.UpdatetUserExamineCertItemId, err:%s, recordId:%d, newItemId:%d",
			err.Error(), in.GetRecordId(), newItemId)
		return out, err
	}

	// 已经生效的才发im
	if cert.Status == uint32(pb.UserExamineCert_Status_UserExamineCert_Status_Effect) {
		err = m.ImPushCertGrant(cert.Uid, cert.IdentityType, newCertInfo.ItemId, newCertInfo.ItemName, time.Now(), cert.EndTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "AnchorContractMgr.UpdateUserExamineCert failed to ImPushCertGrant err:%v, uid:%d, recordId:%d", err, cert.Uid, cert.Id)
		}
	}

	err = m.cache.DelUserExamineCert(cert.Uid, cert.IdentityType)
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.UpdateUserExamineCert failed to cache.DelUserExamineCert err:%v, uid:%d, recordId:%d", err, cert.Uid, cert.Id)
	}

	err = m.cache.Publish()
	if err != nil {
		log.Errorf("AnchorContractMgr.UpdateUserExamineCert failed to cache.Publish err:%v, in:%v", err, in)
	}

	return out, nil
}

// 单个用户回收标识
func (m *AnchorContractMgr) DelUserExamineCert(ctx context.Context, in *pb.DelUserExamineCertReq) (out *pb.CertEmptyMsg, err error) {
	out = new(pb.CertEmptyMsg)
	defer func() {
		log.InfoWithCtx(ctx, "AnchorContractMgr.DelUserExamineCert in:%+v, out:%+v, err:%+v", in, out, err)
	}()

	if in.GetRecordId() == 0 {
		return out, ErrInvalidParamer
	}

	cert, err := m.store.GetUserExamineCertByRecordId(in.GetRecordId())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.DelUserExamineCert failed to store.GetUserExamineCertByRecordId, err:%s, recordId:%d", err.Error(), in.GetRecordId())
		return out, err
	}
	if cert.Id == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "用户标识记录不存在")
	}
	if cert.Status == uint32(pb.UserExamineCert_Status_UserExamineCert_Status_Recycle) {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "该用户标识已回收")
	}
	if cert.Status == uint32(pb.UserExamineCert_Status_UserExamineCert_Status_Overdue) {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "该用户标识已过期")
	}

	err = m.store.DeleteUserExamineCertByRecordId(nil, in.GetRecordId(), in.GetHandler())
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.DelUserExamineCert failed to store.DeleteUserExamineCertByRecordId, err:%s, recordId:%d", err.Error(), in.GetRecordId())
		return out, err
	}

	// 兼容存量
	err = m.cache.RemMemQueue(cache.GenPushExamineCertRecycleKey(), []cache.PushExamineCertMsg{{Uid: cert.Uid, RecordId: in.GetRecordId()}})
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.DelUserExamineCert failed to cache.RemMemQueue, err:%s, recordId:%d, uid:%d", err.Error(), in.GetRecordId(), cert.Uid)
	}

	err = m.cache.RemExamineNotifyDelayQueue(cache.PushExamineCertMsg{Uid: cert.Uid, RecordId: in.GetRecordId()})
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.DelUserExamineCert failed to cache.RemExamineNotifyDelayQueue, err:%s, recordId:%d, uid:%d", err.Error(), in.GetRecordId(), cert.Uid)
	}

	err = m.cache.DelUserExamineCert(cert.Uid, cert.IdentityType)
	if err != nil {
		log.ErrorWithCtx(ctx, "AnchorContractMgr.DelUserExamineCert failed to cache.DelUserExamineCert err %s, recordId:%d, uid:%d", err.Error(), cert.Id, cert.Uid)
	}

	err = m.cache.Publish()
	if err != nil {
		log.Errorf("AnchorContractMgr.DelUserExamineCert failed to cache.Publish err:%v, in:%v", err, in)
	}

	info, err := m.store.GetExamineCert(cert.ItemId)
	if err != nil {
		return out, err
	}

	if cert.Status == uint32(pb.UserExamineCert_Status_UserExamineCert_Status_Effect) {
		err = m.ImPushCertRecycle(cert.Uid, cert.IdentityType, cert.ItemId, info.ItemName)
		if err != nil {
			log.ErrorWithCtx(ctx, "AnchorContractMgr.BatchDelUserExamineCert failed to ImPushCertRecycle err %s, recordId:%d, uid:%d", err.Error(), in.GetRecordId(), cert.Uid)
		}
	}

	log.InfoWithCtx(ctx, "AnchorContractMgr.DelUserExamineCert uid:%d, recordId:%d", cert.Uid, in.GetRecordId())
	return out, nil
}

// 批量用户回收标识
func (m *AnchorContractMgr) BatchDelUserExamineCert(ctx context.Context, in *pb.BatchDelUserExamineCertReq) (out *pb.CertEmptyMsg, err error) {
	out = new(pb.CertEmptyMsg)
	defer func() {
		log.InfoWithCtx(ctx, "BatchDelUserExamineCert in:%+v, out:%+v, err:%+v", in, out, err)
	}()

	if len(in.GetUidList()) == 0 {
		return out, nil
	}

	identityType := in.GetIdentityType()

	// 批量回收将一次性回收当前uid下的全部发放记录
	itemId2Infos := map[uint32]mysql.ExamineCert{}
	for _, uid := range in.GetUidList() {
		anchorCertList, err := m.store.GetUserExamineCertByStatus(uid,
			[]uint32{uint32(pb.UserExamineCert_Status_UserExamineCert_Status_Grant), uint32(pb.UserExamineCert_Status_UserExamineCert_Status_Effect)}, identityType)
		if err != nil {
			log.ErrorWithCtx(ctx, "AnchorContractMgr.BatchDelUserExamineCert failed to store.GetUserExamineCertByStatus err:%+v, uid:%d", err, uid)
			return out, err
		}
		log.Debugf("AnchorContractMgr.BatchDelUserExamineCert got uid %d anchorCertList %+v", uid, anchorCertList)

		for _, anchorCert := range anchorCertList {

			var (
				itemInfo mysql.ExamineCert
				ok       bool
			)
			if itemInfo, ok = itemId2Infos[anchorCert.ItemId]; !ok {
				info, err := m.store.GetExamineCert(anchorCert.ItemId)
				if err != nil {
					return out, err
				}
				if info.ItemId == 0 {
					log.ErrorWithCtx(ctx, "mgr.BatchDelUserExamineCert store.GetExamineCert null itemId:%d, anchorCert:%s", anchorCert.ItemId, anchorCert)
					continue
				}
				itemId2Infos[anchorCert.ItemId] = *info
				itemInfo = *info
			}
			log.DebugWithCtx(ctx, "itemInfo: %s", itemInfo)

			err := m.handleDelUserExamineCert(anchorCert.Id, anchorCert.Uid, anchorCert.IdentityType, anchorCert.Status,
				itemInfo.ItemId, itemInfo.ItemName, in.Handler)
			if err != nil {
				log.Errorf("BatchDelUserExamineCert handleDelUserExamineCert fail %v, uid %d recordId %d", err, anchorCert.Uid, anchorCert.Id)
				return out, err
			}

			log.InfoWithCtx(ctx, "AnchorContractMgr.BatchDelUserExamineCert done uid:%d, recordId:%d", anchorCert.Uid, anchorCert.Id)
		}
	}

	err = m.cache.Publish()
	if err != nil {
		log.Errorf("AnchorContractMgr.BatchDelUserExamineCert failed to cache.Publish err:%v, in:%v", err, in)
	}

	return out, nil
}

func (m *AnchorContractMgr) handleDelUserExamineCert(recordId, uid, identityType, status uint32, itemId uint32, ItemName, handler string) error {
	// db 改状态
	err := m.store.DeleteUserExamineCertByRecordId(nil, recordId, handler)
	if err != nil {
		log.Errorf("AnchorContractMgr.BatchDelUserExamineCert failed to store.DeleteUserExamineCertByRecordId, err:%s, recordId:%d",
			err.Error(), recordId)
		return err
	}

	// 删队列
	err = m.cache.RemExamineNotifyDelayQueue(cache.PushExamineCertMsg{Uid: uid, RecordId: recordId})
	if err != nil {
		log.Errorf("AnchorContractMgr.BatchDelUserExamineCert failed to cache.RemExamineNotifyDelayQueue, err:%s, recordId:%d, uid:%d",
			err.Error(), recordId, uid)
	}

	if status == uint32(pb.UserExamineCert_Status_UserExamineCert_Status_Effect) {
		err = m.ImPushCertRecycle(uid, identityType, itemId, ItemName)
		if err != nil {
			log.Errorf("AnchorContractMgr.BatchDelUserExamineCert failed to ImPushCertRecycle err %s, recordId:%d, uid:%d",
				err.Error(), recordId, uid)
		}
	}
	return nil
}

// 查询用户标识信息
func (m *AnchorContractMgr) ListUserExamineCert(ctx context.Context, in *pb.ListUserExamineCertReq) (out *pb.ListUserExamineCertResp, err error) {
	out = new(pb.ListUserExamineCertResp)

	if in.GetIdentityType() != uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_MULTIPLAYER) &&
		in.GetIdentityType() != uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_RADIO_LIVE) {
		return out, nil
	}
	itemIds := []uint32{}
	query := fmt.Sprintf("identity_type=%d ", in.GetIdentityType())
	if in.GetItemId() > 0 {
		itemIds = append(itemIds, in.GetItemId())
	} else if in.GetItemName() != "" {
		list, err := m.store.GetExamineCertInfoByQuery(fmt.Sprintf("item_name='%s'", in.GetItemName()))
		if err != nil {
			log.ErrorWithCtx(ctx, "mgr.ListUserExamineCert failed to store.GetExamineCertInfoByQuery err %+v", err)
			return out, err
		}
		if len(list) == 0 {
			return out, nil
		}
		itemIds = append(itemIds, list[0].ItemId)
	} else if in.GetItemType() != "" {
		list, err := m.store.GetExamineCertInfoByQuery(fmt.Sprintf("item_type='%s'", in.GetItemType()))
		if err != nil {
			log.ErrorWithCtx(ctx, "mgr.ListUserExamineCert failed to store.GetExamineCertInfoByQuery err %+v", err)
			return out, err
		}
		if len(list) == 0 {
			return out, nil
		}
		for _, info := range list {
			itemIds = append(itemIds, info.ItemId)
		}
	} else if in.GetTtid() != "" {
		uid, _, err := m.accountCli.GetUidByName(ctx, in.GetTtid())
		if err != nil {
			log.ErrorWithCtx(ctx, "mgr.ListUserExamineCert failed to accountCli.GetUidByNameerr %+v", err)
			if err.Code() == status.ErrAccountNotExist {
				err = nil
				return out, nil
			}
			return out, err
		}
		query += fmt.Sprintf("and uid=%d ", uid)
	}

	if _, ok := pb.UserExamineCert_Status_name[int32(in.GetStatus())]; ok {
		query += fmt.Sprintf("and status=%d ", in.GetStatus())
	}
	if len(itemIds) > 0 {
		ids := fmt.Sprintf("%d", itemIds[0])
		for i := 1; i < len(itemIds); i++ {
			ids += fmt.Sprintf(",%d", itemIds[i])
		}
		query += fmt.Sprintf("and item_id in (%s) ", ids)
	}

	anchorCerts, err := m.store.GetUserExamineCertByQuery(query, in.GetOffset(), in.GetLimit())
	if err != nil {
		log.ErrorWithCtx(ctx, "mgr.ListUserExamineCert failed to store.GetUserExamineCertByQuery err %+v", err)
		return out, err
	}
	if len(anchorCerts) == 0 {
		return out, nil
	}

	cnt, err := m.store.GetUserExamineCertCountByQuery(query)
	if err != nil {
		log.ErrorWithCtx(ctx, "mgr.ListUserExamineCert failed to store.GetUserExamineCertCountByQuery err %+v", err)
		return out, err
	}
	out.Total = cnt

	uids := make([]uint32, 0, len(anchorCerts))
	itemIdsStr := ""
	for i := 0; i < len(anchorCerts); i++ {
		uids = append(uids, anchorCerts[i].Uid)
		if i == 0 {
			itemIdsStr = fmt.Sprintf("%d", anchorCerts[0].ItemId)
		} else {
			itemIdsStr += fmt.Sprintf(",%d", anchorCerts[i].ItemId)
		}
	}

	ctx2, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	userMap, err := m.accountCli.BatGetUserByUid(ctx2, uids...)
	if err != nil {
		log.ErrorWithCtx(ctx, "mgr.ListUserExamineCert failed to accountCli.BatGetUserByUid err %+v", err)
		return out, err
	}

	examineCertInfos, err := m.store.GetExamineCertInfoByQuery(fmt.Sprintf("item_id in (%s)", itemIdsStr))
	if err != nil {
		log.ErrorWithCtx(ctx, "mgr.ListUserExamineCert failed to store.GetExamineCertInfoByQuery err %+v", err)
		return out, err
	}
	examineCertInfoMap := map[uint32]*mysql.ExamineCert{}
	for _, info := range examineCertInfos {
		examineCertInfoMap[info.ItemId] = info
	}

	for _, anchorCert := range anchorCerts {
		if _, ok := examineCertInfoMap[anchorCert.ItemId]; !ok {
			log.Warnf("mgr.ListUserExamineCert get null examineCertInfo, item_id:%d, cert:%s", anchorCert.ItemId, anchorCert)
			continue
		}
		out.List = append(out.List, &pb.UserExamineCertInfo{
			RecordId:     anchorCert.Id,
			Uid:          anchorCert.Uid,
			Ttid:         userMap[anchorCert.Uid].GetAlias(),
			Nickname:     userMap[anchorCert.Uid].GetNickname(),
			ItemId:       anchorCert.ItemId,
			ItemName:     examineCertInfoMap[anchorCert.ItemId].ItemName,
			ItemType:     examineCertInfoMap[anchorCert.ItemId].ItemType,
			ItemLevel:    examineCertInfoMap[anchorCert.ItemId].ItemLevel,
			BaseImgurl:   examineCertInfoMap[anchorCert.ItemId].BaseImgurl,
			IdentityType: anchorCert.IdentityType,
			StartTime:    uint32(anchorCert.StartTime.Unix()),
			EndTime:      uint32(anchorCert.EndTime.Unix()),
			ProvideTime:  uint32(anchorCert.GrantTime.Unix()),
			Handler:      anchorCert.Handler,
			Status:       anchorCert.Status,
			ShadowColor:  examineCertInfoMap[anchorCert.ItemId].ShadowColor,
		})
	}
	return out, nil
}

var replaceAnchorLevelCert = func(uid uint32, radioLiveCert *pb.ExamineCertMsg, anchorLevelCert *AnchorLevel.LiveAnchorLevelInfo) *pb.ExamineCertMsg {
	now := uint32(time.Now().Unix())

	if anchorLevelCert.GetLevel() > AnchorLevel.ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_INVALID &&
		anchorLevelCert.GetStartTime() <= now && anchorLevelCert.GetEndTime() > now {

		if radioLiveCert.GetItemName() == "" { // 之前就没有，展示等级标签
			radioLiveCert = &pb.ExamineCertMsg{
				Uid:          uid,
				ItemName:     anchorLevelCert.GetItemName(),
				BaseImgurl:   anchorLevelCert.GetBaseImgurl(),
				ShadowColor:  anchorLevelCert.GetShadowColor(),
				StartTime:    anchorLevelCert.GetStartTime(),
				EndTime:      anchorLevelCert.GetEndTime(),
				IdentityType: uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE),
			}
		} else {
			if (anchorLevelCert.GetLevel() >= AnchorLevel.ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_D &&
				anchorLevelCert.GetLevel() < AnchorLevel.ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_V2_A) ||
				(anchorLevelCert.GetLevel() >= AnchorLevel.ANCHOR_LEVEL_TYPE_ANCHOR_LEVEL_TYPE_V2_D) { // >= x主播， 优先展示等级标签
				radioLiveCert = &pb.ExamineCertMsg{
					Uid:          uid,
					ItemName:     anchorLevelCert.GetItemName(),
					BaseImgurl:   anchorLevelCert.GetBaseImgurl(),
					ShadowColor:  anchorLevelCert.GetShadowColor(),
					StartTime:    anchorLevelCert.GetStartTime(),
					EndTime:      anchorLevelCert.GetEndTime(),
					IdentityType: uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE),
				}
			}
		}
	}
	return radioLiveCert
}

// 获取用户考核认证标识
func (m *AnchorContractMgr) GetUserExamineCert(ctx context.Context, in *pb.GetUserExamineCertReq) (*pb.GetUserExamineCertResp, error) {
	out := &pb.GetUserExamineCertResp{RadioLiveCert: &pb.ExamineCertMsg{}, MultiPlayCert: &pb.ExamineCertMsg{}}
	uid := in.Uid
	uid2certs := m.cache.LocalCache.BatchGetUserExamineCert([]uint32{in.GetUid()})
	if len(uid2certs) > 0 {
		out.RadioLiveCert = uid2certs[in.Uid].RadioLiveCert
		out.MultiPlayCert = uid2certs[in.Uid].MultiPlayCert
	}

	if anchorLevelInfo, err := m.anchorLevelCli.GetLiveAnchorLevelByUid(ctx, []uint32{in.Uid}); err == nil {
		//out.RadioLiveCert = replaceAnchorLevelCert(in.Uid, out.RadioLiveCert, anchorLevelInfo[in.Uid])
		out.LevelCert = &pb.ExamineCertMsg{
			ItemName:    anchorLevelInfo[in.Uid].GetItemName(),
			BaseImgurl:  anchorLevelInfo[in.Uid].GetBaseImgurl(),
			ShadowColor: anchorLevelInfo[in.Uid].GetShadowColor(),
		}

		log.DebugWithCtx(ctx, "GetUserExamineCert replace anchor-level uid %d, cert %+v", in.Uid, out.RadioLiveCert)

		extraCert := m.getAnchorExtraCertUse(uid)
		if extraCert.ItemName != "" {
			out.LevelCert = &pb.ExamineCertMsg{
				ItemName:    extraCert.GetItemName(),
				BaseImgurl:  extraCert.GetBaseImgurl(),
				ShadowColor: extraCert.GetShadowColor(),
			}
			log.DebugWithCtx(ctx, "GetUserExamineCert replace anchor-level uid %d, extraCert=%s", in.Uid, out.LevelCert.String())
		}

	} else {
		log.ErrorWithCtx(ctx, "GetUserExamineCert GetLiveAnchorLevelByUid fail %v, uid %d", err, in.Uid)
	}

	log.DebugfWithCtx(ctx, "GetUserExamineCert from localcache in %+v out info %s", in, out.String())
	return out, nil
}

func (m *AnchorContractMgr) BatchGetLiveAnchorCert(ctx context.Context, in *pb.BatchGetLiveAnchorCertReq) (*pb.BatchGetLiveAnchorCertResp, error) {
	out := &pb.BatchGetLiveAnchorCertResp{
		Uid2LiveAnchorCert: map[uint32]*pb.BatchGetLiveAnchorCertResp_LiveAnchorCertInfo{},
	}
	if len(in.Uids) == 0 {
		return out, nil
	}

	uid2Cert := m.cache.LocalCache.BatchGetUserExamineCert(in.Uids)
	uid2AnchorLevel, err := m.anchorLevelCli.GetLiveAnchorLevelByUid(ctx, in.Uids)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetLiveAnchorCert GetLiveAnchorLevelByUid fail %v, uid %v", err, in)
		return out, err
	}

	for _, uid := range in.Uids {
		baseCert := uid2Cert[uid]
		levelCert := uid2AnchorLevel[uid]

		info := &pb.BatchGetLiveAnchorCertResp_LiveAnchorCertInfo{}
		if baseCert.GetRadioLiveCert().GetItemName() != "" {
			info.ExamineCert = baseCert.GetRadioLiveCert()
		}
		if levelCert.GetItemName() != "" {
			info.LevelCert = &pb.ExamineCertMsg{
				Uid:          uid,
				ItemName:     levelCert.ItemName,
				BaseImgurl:   levelCert.BaseImgurl,
				ShadowColor:  levelCert.ShadowColor,
				StartTime:    levelCert.StartTime,
				EndTime:      levelCert.EndTime,
				IdentityType: uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE),
			}
		}

		extraCert := m.getAnchorExtraCertUse(uid)
		if extraCert.ItemName != "" {
			info.LevelCert = &pb.ExamineCertMsg{
				ItemName:     extraCert.GetItemName(),
				BaseImgurl:   extraCert.GetBaseImgurl(),
				ShadowColor:  extraCert.GetShadowColor(),
				StartTime:    extraCert.StartTime,
				EndTime:      extraCert.EndTime,
				IdentityType: uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE),
			}
			log.DebugWithCtx(ctx, "BatchGetLiveAnchorCert replace anchor-level uid %d, extraCert=%s", uid, info.LevelCert.String())
		}

		if info.ExamineCert != nil || info.LevelCert != nil {
			out.Uid2LiveAnchorCert[uid] = info
		}
	}

	log.DebugWithCtx(ctx, "BatchGetLiveAnchorCert in %v out %s", in.Uids, out.String())
	return out, nil
}

// 批量获取主播考核认证标识, 只返回 有认证的
func (m *AnchorContractMgr) BatchGetUserExamineCert(ctx context.Context, in *pb.BatchGetUserExamineCertReq) (*pb.BatchGetUserExamineCertResp, error) {
	out := &pb.BatchGetUserExamineCertResp{}

	uidList := in.GetUidList()
	size := uint32(len(in.GetUidList()))
	if size == 0 {
		return out, nil
	}

	uid2Cert := m.cache.LocalCache.BatchGetUserExamineCert(uidList)
	uid2AnchorLevel, err := m.anchorLevelCli.GetLiveAnchorLevelByUid(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserExamineCert GetLiveAnchorLevelByUid fail %v, uid %v", err, uidList)
	}

	for _, uid := range uidList {

		cert := &pb.AnchorExamineCertInfo{Uid: uid}

		baseCert := uid2Cert[uid]
		levelCert := uid2AnchorLevel[uid]

		log.DebugWithCtx(ctx, "BatchGetUserExamineCert uid %d baseCert %+v levelCert %+v", uid, baseCert, levelCert)

		cert.RadioLiveCert = baseCert.GetRadioLiveCert()
		cert.MultiPlayCert = baseCert.GetMultiPlayCert()

		// 有问题 暂时屏蔽
		cert.RadioLiveCert = replaceAnchorLevelCert(uid, cert.RadioLiveCert, levelCert)

		extraCert := m.getAnchorExtraCertUse(uid)
		if extraCert.ItemName != "" {
			cert.RadioLiveCert = &pb.ExamineCertMsg{
				ItemName:     extraCert.GetItemName(),
				BaseImgurl:   extraCert.GetBaseImgurl(),
				ShadowColor:  extraCert.GetShadowColor(),
				IdentityType: uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE),
			}
			log.DebugWithCtx(ctx, "BatchGetUserExamineCert replace anchor-level uid %d, extraCert=%s", uid, cert.RadioLiveCert.String())
		}

		if cert.RadioLiveCert != nil || cert.MultiPlayCert != nil {
			out.CertList = append(out.CertList, cert)
		}
	}

	log.DebugfWithCtx(ctx, "BatchGetUserExamineCert from localcache in %+v out size %d, info %s", in, len(out.CertList), out.String())
	return out, nil
}

func (m *AnchorContractMgr) SendImExamineCert(ctx context.Context, in *pb.SendImExamineCertReq) (*pb.SendImExamineCertResp, error) {
	out := &pb.SendImExamineCertResp{}

	item, err := m.store.GetExamineCert(in.GetItemId())
	if err != nil {
		return out, err
	}
	if item.ItemId == 0 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "标识不存在")
	}

	err = m.ImPushCertGrant(in.Uid, item.IdentityType, item.ItemId, item.ItemName, time.Unix(int64(in.StartTime), 0), time.Unix(int64(in.EndTime), 0))
	if err != nil {
		log.ErrorWithCtx(ctx, "SendImExamineCert.ImPushCertGrant fail err %v, in %+v", err, in)
	}

	err = m.ImPushCertOverDue(in.Uid, item.IdentityType, item.ItemId, item.ItemName)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendImExamineCert.ImPushCertOverDue fail err %v, in %+v", err, in)
	}

	err = m.ImPushCertRecycle(in.Uid, item.IdentityType, item.ItemId, item.ItemName)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendImExamineCert.ImPushCertRecycle fail err %v, in %+v", err, in)
	}

	return out, nil
}

// ###########################################

func (m *AnchorContractMgr) DelUserExamineCertCache(itemId uint32) error {
	list, err := m.store.GetUserExamineCertByItemId(itemId, []uint32{uint32(pb.UserExamineCert_Status_UserExamineCert_Status_Grant),
		uint32(pb.UserExamineCert_Status_UserExamineCert_Status_Effect)})
	if err != nil {
		log.Errorf("mgr.DelUserExamineCertCache failed to store.GetUserExamineCertByItemId err:%+v, itemId:%d", err, itemId)
		return err
	}

	for _, cert := range list {
		err = m.cache.DelUserExamineCert(cert.Uid, cert.IdentityType)
		if err != nil {
			log.Errorf("mgr.DelUserExamineCertCache failed to cache.DelUserExamineCert uid:%d IdentityType:%d", cert.Uid, cert.IdentityType)
		}
	}
	return nil
}

func (m *AnchorContractMgr) ImPushCertOverDue(uid uint32, identityType uint32, itemId uint32, itemName string) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	err := error(nil)

	if identityType == uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_RADIO_LIVE) {

		// 直播类型的 改成到期的文案
		cfg := m.dyConfig.GetCertOverdueMsgCfg(itemId, identityType)
		msg := fmt.Sprintf(cfg.FormatMsg, itemName)

		if len(cfg.Url) > 0 {
			err = m.SendIMMsgWithJumpUrl(ctx, uid, msg, "点击链接", cfg.Url)
		} else {
			err = m.SendIMMsg(ctx, uid, msg)
		}

	} else if identityType == uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_MULTIPLAYER) {
		// 多人互动还是用回收的文案
		cfg := m.dyConfig.GetCertRecycleMsgCfg(itemId, identityType)
		msg := fmt.Sprintf(cfg.FormatMsg, itemName)
		err = m.SendIMMsg(ctx, uid, msg)
	} else {
		log.Errorf("invalid identityType %d", identityType)
	}
	return err
}

func (m *AnchorContractMgr) ImPushCertRecycle(uid uint32, identityType uint32, itemId uint32, itemName string) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	err := error(nil)

	cfg := m.dyConfig.GetCertRecycleMsgCfg(itemId, identityType)

	if identityType == uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_RADIO_LIVE) {
		msg := fmt.Sprintf(cfg.FormatMsg, itemName)
		if len(cfg.Url) > 0 {
			err = m.SendIMMsgWithJumpUrl(ctx, uid, msg, "点击链接", cfg.Url)
		} else {
			err = m.SendIMMsg(ctx, uid, msg)
		}

	} else if identityType == uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_MULTIPLAYER) {

		msg := fmt.Sprintf(cfg.FormatMsg, itemName)
		err = m.SendIMMsg(ctx, uid, msg)
	} else {
		log.Errorf("invalid identityType %d", identityType)
	}
	return err
}

func (m *AnchorContractMgr) ImPushCertGrant(uid uint32, identityType uint32, itemId uint32, itemName string, startTime, endTime time.Time) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	err := error(nil)
	cfg := m.dyConfig.GetCertGrantMsgCfg(itemId, identityType)

	if identityType == uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_RADIO_LIVE) {
		msg := fmt.Sprintf(cfg.FormatMsg, itemName,
			startTime.Year(), startTime.Month(), startTime.Day(), startTime.Hour(),
			startTime.Minute(), endTime.Year(), endTime.Month(), endTime.Day(),
			endTime.Hour(), endTime.Minute())
		if len(cfg.Url) > 0 {
			err = m.SendIMMsgWithJumpUrl(ctx, uid, msg, "点击链接", cfg.Url)
		} else {
			err = m.SendIMMsg(ctx, uid, msg)
		}

	} else if identityType == uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_MULTIPLAYER) {
		msg := fmt.Sprintf(cfg.FormatMsg, itemName, endTime.Year(), endTime.Month(), endTime.Day(), endTime.Hour(), endTime.Minute())
		err = m.SendIMMsg(ctx, uid, msg)
	} else {
		log.Errorf("invalid identityType %d", identityType)
	}
	return err
}

func (m *AnchorContractMgr) IsIntersectOfTime(start1, end1 time.Time, start2, end2 time.Time) bool {

	s1, e1, s2, e2 := start1.Unix(), end1.Unix(), start2.Unix(), end2.Unix()
	if s1 == s2 || e1 == e2 {
		return true
	}
	if s1 > s2 && s1 < e2 {
		return true
	}
	if s2 > s1 && s2 < e1 {
		return true
	}
	return false
}

// 检查是否有时间重叠
func (m *AnchorContractMgr) checkUserExamineCert(uid uint32, identityType uint32, startTime, endTime uint32) bool {
	list, err := m.store.GetUserExamineCertListByUid(nil, uid, identityType)
	if err != nil {
		return false
	}

	startTs := time.Unix(int64(startTime), 0)
	endTs := time.Unix(int64(endTime), 0)
	for _, cert := range list {
		if m.IsIntersectOfTime(startTs, endTs, cert.StartTime, cert.EndTime) {
			return true
		}
	}
	return false
}

// ==========================================================================================
func (m *AnchorContractMgr) filterViolation(ctx context.Context, uid uint32, settleMonthTs uint32) (
	monthViolationACnt uint32, monthViolationBCnt uint32, monthViolationCCnt uint32, err error) {

	settleMonthTm := time.Unix(int64(settleMonthTs), 0)
	monthBegin := time.Date(settleMonthTm.Year(), settleMonthTm.Month(), 1, 0, 0, 0, 0, time.Local)
	monthEnd := time.Date(settleMonthTm.Year(), settleMonthTm.Month()+1, 1, 0, 0, 0, 0, time.Local).Add(-time.Second)

	userInfo, serr := m.accountCli.GetUserByUid(ctx, uid)
	if serr != nil {
		log.Errorf("filterViolation GetUsersMap fail %v, uids %v", serr, uid)
		err = serr
		return
	}
	alias := userInfo.GetAlias()
	// 违规次数
	tid2ViolationsInfo, err := urrc.GetViolationsInfoList(ctx, []string{alias}, monthBegin, monthEnd)
	if err != nil {
		log.Errorf("filterViolation urrc.GetViolationsInfoList fail %v", err)
		return
	}
	violationA, violationB, violationC := tid2ViolationsInfo[alias].GetViolation()
	monthViolationACnt, monthViolationBCnt, monthViolationCCnt = uint32(len(violationA)), uint32(len(violationB)), uint32(len(violationC))
	return
}

func (m *AnchorContractMgr) getAnchorCheckLastLevel(ctx context.Context, uid uint32) (lastUpdateTime uint32, lastCheckLevel string, err error) {
	checkResp, err := m.anchorCheckCli.GetAnchorCheckHistory(ctx, uid)
	if err != nil {
		log.Errorf("getAnchorCheckLastLevel fail %v", err)
		return
	}
	if len(checkResp.GetList()) > 0 {
		lastUpdateTime = checkResp.GetList()[0].GetUpdateTime()
		lastCheckLevel = checkResp.GetList()[0].GetLevel()
	}
	return
}

func (m *AnchorContractMgr) GetVCPostList(ctx context.Context, uid uint32, beginTs, endTs uint32) (total uint32, err error) {
	req := &operating_platform.GetVCPostListReq{
		PageNum:   1,
		PageSize:  100,
		BeginDate: beginTs,
		EndDate:   endTs,
		UidList:   []uint32{uid},
		Show:      "true",
	}
	resp, err := m.rcmdOperatingPlatformCli.GetVCPostList(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetVCPostList fail %v, uid=%d", err, uid)
		return 0, err
	}
	log.InfoWithCtx(ctx, "GetVCPostList uid=%d req=%+v, resp=%+v", uid, req, resp)
	return resp.TotalCount, nil
}

// rpc =====================================================
func (m *AnchorContractMgr) GetAnchorCertListByItemId(ctx context.Context, in *pb.GetAnchorCertListByItemIdReq) (*pb.GetAnchorCertListByItemIdResp, error) {
	/*
		- 支持优质主播榜单自动更新，读标识后台中正在生效的主播名单：皓月歌手、初旭歌手、璀璨歌手、燃乐歌手、杰出暖愈师、卓越暖愈师、主持天团、次元大咖、次元超强CV（ID列表：待补充）@李金安
	*/
	out := &pb.GetAnchorCertListByItemIdResp{}
	uid2certs := m.cache.LocalCache.GetAllLiveCerts()
	for _, itemId := range in.ItemIdList {
		for _, cert := range uid2certs {
			if cert.ItemId == itemId {
				out.UidList = append(out.UidList, cert.Uid)
			}
		}
	}
	return out, nil
}

// 白名单手动升级
func (m *AnchorContractMgr) SetAnchorCertWhiteList(ctx context.Context, in *pb.SetAnchorCertWhiteListReq) (*pb.CertEmptyMsg, error) {
	out := &pb.CertEmptyMsg{}
	log.InfoWithCtx(ctx, "SetAnchorCertWhiteList in=%+v", in)

	now := time.Now()
	settleMonthTm := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, time.Local)
	for _, info := range in.List {
		log.InfoWithCtx(ctx, "SetAnchorCertWhiteList begin uid=%d itemid=%d", info.Uid, info.ItemId)

		itemInfo, err := m.store.GetExamineCert(info.ItemId)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetAnchorCertWhiteList GetExamineCertByName fail %v, ItemName=%s, uid=%d", err, info.GetItemName(), info.GetUid())
			return out, err
		}
		itemId := itemInfo.ItemId
		if itemId == 0 {
			log.ErrorWithCtx(ctx, "SetAnchorCertWhiteList GetExamineCertByName no find info=%+v, uid=%d", info, info.GetUid())
			continue
		}
		if itemInfo.IdentityType != uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE) {
			log.ErrorWithCtx(ctx, "SetAnchorCertWhiteList GetExamineCertByName invalid info=%+v, uid=%d", info, info.GetUid())
			continue
		}
		log.InfoWithCtx(ctx, "SetAnchorCertWhiteList uid=%d, itemInfo=%+v", info.Uid, itemInfo)

		uid := info.Uid
		liveResp, serr := m.liveMgrCli.GetChannelLiveInfo(ctx, uid, false)
		if serr != nil {
			if serr.Code() != status.ErrChannelLiveNotAuthority {
				log.ErrorWithCtx(ctx, "SetAnchorCertWhiteList GetChannelLiveInfo fail %v, uid=%d", serr, uid)
				return out, serr
			}
		}
		tagId := liveResp.GetChannelLiveInfo().GetTagId()
		if tagId2Name[tagId] == "" {
			log.ErrorWithCtx(ctx, "SetAnchorCertWhiteList no tag uid=%d", uid)
			continue
		}

		tagType := uint32(0)
		switch tagId {
		case TagIdTypeMusic:
			tagType = uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_Music)
		case TagIdTypeEmotion, TagIdTypeStory:
			tagType = uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_EmotionStory)
		case TagIdTypeTwoDimension:
			tagType = uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_TwoDimensions)
		}

		err = m.ChangeAnchorCertLevel(ctx, uid, tagId, tagType, settleMonthTm, itemInfo.ItemName, itemInfo.ItemLevel, in.Handler)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetAnchorCertWhiteList Transaction fail %v, uid=%d", err, uid)
			return out, err
		}
		log.InfoWithCtx(ctx, "SetAnchorCertWhiteList Transaction end uid=%d tagId=%d tagType=%d itemName=%s", uid, tagId, tagType, itemInfo.ItemName)
	}

	log.InfoWithCtx(ctx, "SetAnchorCertWhiteList end in=%+v", in)
	return out, nil
}

func (m *AnchorContractMgr) ChangeAnchorCertLevel(ctx context.Context, uid uint32, tagId, tagType uint32,
	settleMonthTm time.Time, itemName string, itemLevel uint32, handler string) error {
	err := m.store.Transaction(context.Background(), func(tx *gorm.DB) error {

		info := &mysql.AnchorCertMonthTaskAwardLog{
			Uid:       uid,
			TagType:   tagType,
			YearMonth: uint32(settleMonthTm.Year()*100 + int(settleMonthTm.Month())),
			ItemName:  itemName,
			ItemLevel: itemLevel,
			IsDone:    true,
			Extra:     "",
		}
		oldlevelInfo, err := m.store.GetAnchorCertAwardByUid(tx, uint32(settleMonthTm.Unix()), uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetAnchorCertWhiteList GetAnchorCertAwardByUid fail %v, uid=%d", err, uid)
			return err
		}
		if oldlevelInfo.Uid > 0 {
			info.Extra = oldlevelInfo.Extra
		}

		row, err := m.store.UpdateAnchorCertAwardLevel(tx, info)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetAnchorCertWhiteList UpdateAnchorCertAwardLevel fail %v, uid=%d", err, uid)
			return err
		}
		log.InfoWithCtx(ctx, "SetAnchorCertWhiteList UpdateAnchorCertAwardLevel row %d uid=%d tagId=%d, tagType=%d, ItemName=%s, ItemLevel=%d",
			row, uid, tagId, tagType, itemName, itemLevel)

		err = m.store.RecordAnchorCertChangeLog(tx, uint32(settleMonthTm.Unix()), &mysql.AnchorCertMonthTaskAwardChangeLog{
			Uid:       uid,
			TagType:   tagType,
			ItemName:  itemName,
			ItemLevel: itemLevel,
			Operator:  handler,
			Extra:     info.Extra,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "SetAnchorCertWhiteList RecordAnchorCertChangeLog fail %v, uid=%d", err, uid)
			return err
		}
		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetAnchorCertWhiteList Transaction fail %v, uid=%d", err, uid)
		return err
	}
	return nil
}

// 导入主播内容记录
func (m *AnchorContractMgr) AddAnchorCertContent(ctx context.Context, in *pb.AddAnchorCertContentReq) (*pb.CertEmptyMsg, error) {
	out := &pb.CertEmptyMsg{}
	for _, info := range in.List {
		record := &mysql.AnchorCertCompetitionRecord{
			Uid:                          info.Uid,
			TagType:                      in.TagType,
			CheckCompetitionStatus:       info.GetCheckCompetitionStatus(),
			QuarterCompetitionStatus:     info.GetQuarterCompetitionStatus(),
			BiweeklyCompetitionStatus:    info.GetBiweeklyCompetitionStatus(),
			BurnCompetitionStatus:        info.GetBurnCompetitionStatus(),
			MonthCompetitionStatus:       info.GetMonthCompetitionStatus(),
			DoubleMonthCompetitionStatus: info.GetDoubleMonthCompetitionStatus(),
			Operator:                     info.GetOperator(),
			Remark:                       info.GetRemark(),
		}

		err := m.store.Transaction(context.Background(), func(tx *gorm.DB) error {
			err := m.store.AddAnchorCertCompetitionRecord(tx, record)
			if err != nil {
				log.ErrorWithCtx(ctx, "AddAnchorCertContent fail %v, record=%+v", err, record)
				return err
			}
			err = m.store.AddAnchorCertCompetitionRecordLog(tx, record)
			if err != nil {
				log.ErrorWithCtx(ctx, "AddAnchorCertContent fail %v, record=%+v", err, record)
				return err
			}
			return nil
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "AddAnchorCertContent Transaction fail %v, record=%+v", err, record)
			return out, err
		}
	}
	log.InfoWithCtx(ctx, "AddAnchorCertContent end in=%+v", in)
	return out, nil
}

// 查询主播内容记录
func (m *AnchorContractMgr) ListAnchorCertContent(ctx context.Context, in *pb.ListAnchorCertContentReq) (*pb.ListAnchorCertContentResp, error) {
	out := &pb.ListAnchorCertContentResp{}

	total, list, err := m.store.GetAnchorCertCompetitionRecordList(in.Uid, in.TagType, in.QueryType == 1,
		in.Page*in.PageSize, in.PageSize)
	if err != nil {
		log.ErrorWithCtx(ctx, "ListAnchorCertContent fail %v, in %+v", err, in)
		return out, err
	}
	log.DebugfWithCtx(ctx, "ListAnchorCertContent total=%d, list=%+v, in=%+v", total, list, in)
	for _, info := range list {
		record := &pb.AnchorCertContentInfo{
			Uid:                          info.Uid,
			TagType:                      info.TagType,
			CheckCompetitionStatus:       info.CheckCompetitionStatus,
			QuarterCompetitionStatus:     info.QuarterCompetitionStatus,
			BiweeklyCompetitionStatus:    info.BiweeklyCompetitionStatus,
			BurnCompetitionStatus:        info.BurnCompetitionStatus,
			MonthCompetitionStatus:       info.MonthCompetitionStatus,
			DoubleMonthCompetitionStatus: info.DoubleMonthCompetitionStatus,
			Operator:                     info.Operator,
			Remark:                       info.Remark,
			UpdateTime:                   uint32(info.UpdateTime.Unix()),
		}

		userInfo, serr := m.accountCli.GetUserByUid(ctx, info.Uid)
		if serr != nil {
			log.ErrorWithCtx(ctx, "ListAnchorCertContent GetUserByUid fail %v, uid=%d", serr, info.Uid)
			return out, serr
		}
		record.Ttid = userInfo.GetAlias()
		record.Nickname = userInfo.GetNickname()

		check, err := m.anchorCheckCli.GetAnchorCheckHistory(ctx, info.Uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "ListAnchorCertContent GetAnchorCheckHistory fail %v, uid=%d", err, info.Uid)
			return out, err
		}
		if len(check.GetList()) > 0 {
			record.CheckLevel = check.GetList()[0].GetLevel()
		}

		liveResp, serr := m.liveMgrCli.GetChannelLiveInfo(ctx, info.Uid, false)
		if serr != nil {
			log.ErrorWithCtx(ctx, "ListAnchorCertContent GetChannelLiveInfo fail %v, uid=%d", serr, info.Uid)
			if serr.Code() != status.ErrChannelLiveNotAuthority {
				return out, serr
			}
		}
		tagId := liveResp.GetChannelLiveInfo().GetTagId()
		record.TagName = tagId2Name[tagId]

		out.List = append(out.List, record)
	}

	out.Total = total
	log.DebugfWithCtx(ctx, "ListAnchorCertContent end in=%+v, out=%+v", in, out)
	return out, nil
}

// 编辑主播内容记录
func (m *AnchorContractMgr) UpdateAnchorCertContent(ctx context.Context, in *pb.UpdateAnchorCertContentReq) (*pb.CertEmptyMsg, error) {
	out := &pb.CertEmptyMsg{}
	if in.GetInfo().GetUid() == 0 {
		return out, nil
	}

	info := in.GetInfo()
	err := m.store.Transaction(context.Background(), func(tx *gorm.DB) error {
		record := &mysql.AnchorCertCompetitionRecord{
			Uid:                          info.Uid,
			TagType:                      info.TagType,
			CheckCompetitionStatus:       info.CheckCompetitionStatus,
			QuarterCompetitionStatus:     info.QuarterCompetitionStatus,
			BiweeklyCompetitionStatus:    info.BiweeklyCompetitionStatus,
			BurnCompetitionStatus:        info.BurnCompetitionStatus,
			MonthCompetitionStatus:       info.MonthCompetitionStatus,
			DoubleMonthCompetitionStatus: info.DoubleMonthCompetitionStatus,
			Operator:                     info.Operator,
			Remark:                       info.Remark,
		}
		ok, err := m.store.UpdateAnchorCertCompetitionRecord(tx, record)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateAnchorCertContent fail %v, in %+v, RowsAffected %v", err, in.GetInfo(), ok)
			return err
		}
		err = m.store.AddAnchorCertCompetitionRecordLog(tx, record)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateAnchorCertContent fail %v, record=%+v", err, record)
			return err
		}
		return nil
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAnchorCertContentTransaction fail %v, info=%+v", err, info)
		return out, err
	}

	log.InfoWithCtx(ctx, "UpdateAnchorCertContent end in=%+v", info)
	return out, nil
}

// 新增升级任务
func (m *AnchorContractMgr) AddAnchorCertUpgradeTask(ctx context.Context, in *pb.AddAnchorCertUpgradeTaskReq) (*pb.CertEmptyMsg, error) {
	task := in.GetTask()
	info := &mysql.AnchorCertUpgradeTask{
		TaskType:         task.GetTaskType(),
		TaskName:         task.GetTaskName(),
		TaskImgUrl:       task.GetTaskImgUrl(),
		ChannelId:        task.GetChannelId(),
		TaskInitialLevel: task.GetTaskInitialLevel(),
		TaskTargetLevel:  task.GetTaskTargetLevel(),
		TaskJumpUrl:      task.GetTaskJumpUrl(),
		TaskCheckTime:    task.GetTaskCheckTime(),
		TaskCheckMethod:  task.GetTaskCheckMethod(),
		ShowBeginTime:    task.GetShowBeginTime(),
		ShowEndTime:      task.GetShowEndTime(),
	}
	out := &pb.CertEmptyMsg{}
	err := m.store.AddAnchorCertUpgradeTask(info)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddAnchorCertUpgradeTask fail %v, in %+v", err, in.Task)
		return out, err
	}
	log.InfoWithCtx(ctx, "AddAnchorCertUpgradeTask end %+v", task)
	return out, nil
}

// 编辑升级任务
func (m *AnchorContractMgr) UpdateAnchorCertUpgradeTask(ctx context.Context, in *pb.UpdateAnchorCertUpgradeTaskReq) (*pb.CertEmptyMsg, error) {
	out := &pb.CertEmptyMsg{}
	task := in.GetTask()
	if task.GetTaskId() == 0 {
		return out, nil
	}
	info := &mysql.AnchorCertUpgradeTask{
		Id:               task.GetTaskId(),
		TaskType:         task.GetTaskType(),
		TaskName:         task.GetTaskName(),
		TaskImgUrl:       task.GetTaskImgUrl(),
		ChannelId:        task.GetChannelId(),
		TaskInitialLevel: task.GetTaskInitialLevel(),
		TaskTargetLevel:  task.GetTaskTargetLevel(),
		TaskCheckMethod:  task.GetTaskCheckMethod(),
		TaskJumpUrl:      task.GetTaskJumpUrl(),
		TaskCheckTime:    task.GetTaskCheckTime(),
		ShowBeginTime:    task.GetShowBeginTime(),
		ShowEndTime:      task.GetShowEndTime(),
	}

	ok, err := m.store.UpdateAnchorCertUpgradeTask(info)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAnchorCertUpgradeTask fail %v, in %+v", err, in.Task)
		return out, err
	}
	log.InfoWithCtx(ctx, "UpdateAnchorCertUpgradeTask end %+v, RowsAffected %v", task, ok)
	return out, nil
}

// 查询升级任务
func (m *AnchorContractMgr) ListAnchorCertUpgradeTask(ctx context.Context, in *pb.ListAnchorCertUpgradeTaskReq) (*pb.ListAnchorCertUpgradeTaskResp, error) {
	out := &pb.ListAnchorCertUpgradeTaskResp{}
	total, list, err := m.store.GetAnchorCertUpgradeTaskList(in.GetTaskId(), in.GetTaskType(), in.GetTaskName(), in.GetPage()*in.GetPageSize(), in.GetPageSize())
	if err != nil {
		log.ErrorWithCtx(ctx, "ListAnchorCertUpgradeTask fail %v, in %+v", err, in)
		return out, err
	}
	now := uint32(time.Now().Unix())
	for _, info := range list {
		task := &pb.AnchorCertUpgradeTaskInfo{
			TaskId:           info.Id,
			TaskType:         info.TaskType,
			TaskName:         info.TaskName,
			TaskImgUrl:       info.TaskImgUrl,
			ChannelId:        info.ChannelId,
			TaskInitialLevel: info.TaskInitialLevel,
			TaskTargetLevel:  info.TaskTargetLevel,
			TaskCheckMethod:  info.TaskCheckMethod,
			TaskCheckTime:    info.TaskCheckTime,
			ShowBeginTime:    info.ShowBeginTime,
			ShowEndTime:      info.ShowEndTime,
			TaskJumpUrl:      info.TaskJumpUrl,
		}
		if now < task.ShowBeginTime {
			task.Status = uint32(pb.AnchorCertUpgradeTaskStatus_AnchorCertUpgradeTaskStatus_Disable)
		} else if now >= task.ShowBeginTime && now <= task.ShowEndTime {
			task.Status = uint32(pb.AnchorCertUpgradeTaskStatus_AnchorCertUpgradeTaskStatus_Enable)
		} else if now > task.ShowEndTime {
			task.Status = uint32(pb.AnchorCertUpgradeTaskStatus_AnchorCertUpgradeTaskStatus_Overdue)
		}
		out.TaskList = append(out.TaskList, task)
	}
	out.Total = total
	log.DebugfWithCtx(ctx, "ListAnchorCertUpgradeTask end in %+v, out=%+v", in, out)
	return out, nil
}

// 删除升级任务
func (m *AnchorContractMgr) DelAnchorCertUpgradeTask(ctx context.Context, in *pb.DelAnchorCertUpgradeTaskReq) (*pb.CertEmptyMsg, error) {
	out := &pb.CertEmptyMsg{}
	ok, err := m.store.DelAnchorCertUpgradeTask(in.TaskId)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelAnchorCertUpgradeTask fail %v, TaskId=%d", err, in.TaskId)
		return out, err
	}
	log.InfoWithCtx(ctx, "DelAnchorCertUpgradeTask end TaskId=%d ok %v", in.TaskId, ok)
	return out, nil
}

func (m *AnchorContractMgr) GetAnchorCertTaskInfo(ctx context.Context, in *pb.GetAnchorCertTaskInfoReq) (*pb.GetAnchorCertTaskInfoResp, error) {
	out := &pb.GetAnchorCertTaskInfoResp{}

	uid := in.Uid
	now := time.Now()
	settleMonthTm := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, time.Local)
	certInfo, err := m.store.GetAnchorCertAwardByUid(nil, uint32(settleMonthTm.Unix()), uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorCertTaskInfo GetAnchorCertAwardByUid fail %v uid=%d", err, uid)
		return out, err
	}
	log.DebugfWithCtx(ctx, "GetAnchorCertTaskInfo uid %d GetAnchorCertAwardByUid=%+v", uid, certInfo)

	uid2certs := m.cache.LocalCache.BatchGetUserExamineCert([]uint32{uid})
	out.OutCert = uid2certs[uid].GetRadioLiveCert()

	if certInfo.ItemName != "" {
		itemInfo, err := m.store.GetExamineCertByName(nil, certInfo.ItemName)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAnchorCertTaskInfo GetExamineCertByName fail %v uid=%d ItemName=%s", err, uid, certInfo.ItemName)
			return out, err
		}
		out.CenterCert = &pb.ExamineCertMsg{
			ItemName:    certInfo.ItemName,
			BaseImgurl:  itemInfo.BaseImgurl,
			ShadowColor: itemInfo.ShadowColor,
		}
	}

	liveResp, serr := m.liveMgrCli.GetChannelLiveInfo(ctx, uid, false)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetAnchorCertTaskInfo GetChannelLiveInfo fail %v, uid=%d", serr, uid)
		if serr.Code() != status.ErrChannelLiveNotAuthority {
			return out, err
		}
	}
	tagId := liveResp.GetChannelLiveInfo().GetTagId()
	log.DebugfWithCtx(ctx, "GetAnchorCertTaskInfo uid %d tagId=%d", uid, tagId)

	switch tagId {
	case TagIdTypeMusic, TagIdTypeEmotion, TagIdTypeStory, TagIdTypeTwoDimension:
		{
			certName := out.GetCenterCert().GetItemName()
			upgradeTaskType := conventUpgradeTaskType(tagId)
			upgradeCertInfo, err := m.store.GetAnchorCertUpgradeTask(certName, upgradeTaskType, time.Now())
			if err != nil {
				log.ErrorWithCtx(ctx, "GetAnchorCertTaskInfo GetAnchorCertUpgradeTask fail %v uid=%d", err, uid)
				return out, err
			}
			log.DebugfWithCtx(ctx, "GetAnchorCertTaskInfo uid %d certName=%s, upgradeCertInfo=%+v", uid, certName, upgradeCertInfo)
			out.AnchorCertUpgradeInfo = &pb.AnchorCertUpgradeTaskInfo{
				TaskId:           upgradeCertInfo.Id,
				TaskType:         upgradeCertInfo.TaskType,
				TaskName:         upgradeCertInfo.TaskName,
				TaskImgUrl:       upgradeCertInfo.TaskImgUrl,
				ChannelId:        upgradeCertInfo.ChannelId,
				TaskInitialLevel: upgradeCertInfo.TaskInitialLevel,
				TaskTargetLevel:  upgradeCertInfo.TaskTargetLevel,
				TaskCheckMethod:  upgradeCertInfo.TaskCheckMethod,
				TaskCheckTime:    upgradeCertInfo.TaskCheckTime,
				TaskJumpUrl:      upgradeCertInfo.TaskJumpUrl,
			}
		}
	}

	log.DebugfWithCtx(ctx, "GetAnchorCertTaskInfo end uid %d out=%+v", uid, out)
	return out, nil
}

func (m *AnchorContractMgr) SettleAnchorCertEmotionStoryTimer() {

	//now := time.Now()
	now := time.Now()
	settleMonthTm := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, time.Local)
	//settleMonthTm := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, time.Local)
	log.Infof("SettleAnchorCertEmotionStoryTimer work settleMonthTm %s", settleMonthTm)

	// 结算
	err := m.SettleAnchorCertEmotionStory(settleMonthTm)
	if err != nil {
		log.Errorf("SettleAnchorCertEmotionStoryTimer work settleMonthTm %s fail %v", settleMonthTm, err)
		return
	}

	// 发放标识
	err = m.AwardAnchorCertProc(settleMonthTm, uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_EmotionStory))
	if err != nil {
		log.Errorf("SettleAnchorCertMusicTimer AwardAnchorCertProc fail %v", err)
		return
	}
}

func (m *AnchorContractMgr) SettleAnchorCertMusicTimer() {
	now := time.Now()
	settleMonthTm := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, time.Local)
	log.Infof("SettleAnchorCertMusicTimer settleMonthTm %s", settleMonthTm)

	// 结算
	err := m.SettleAnchorCertMusic(settleMonthTm)
	if err != nil {
		log.Errorf("SettleAnchorCertMusicTimer SettleAnchorCertMusic fail %v", err)
		return
	}

	// 发放标识
	err = m.AwardAnchorCertProc(settleMonthTm, uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_Music))
	if err != nil {
		log.Errorf("SettleAnchorCertMusicTimer AwardAnchorCertProc fail %v", err)
		return
	}

	// 降级处理
	m.DownGradeAnchorCertMusic(settleMonthTm)
}

func (m *AnchorContractMgr) AwardAnchorCertProc(settleMonthTm time.Time, tagType uint32) (err error) {
	msg := fmt.Sprintf("AwardAnchorCertProc begin %s tagType=%d", settleMonthTm.Format("2006-01"), tagType)
	m.reporter.SendInfo(msg)
	defer func() {
		if r := recover(); r != nil {
			msg := fmt.Sprintf("AwardAnchorCertProc tagType=%d panic handler crashed: %+v, stack: %s", tagType, r, string(debug.Stack()))
			log.Errorf(msg)
			m.reporter.SendError(msg)
		}
		if err != nil {
			msg := fmt.Sprintf("AwardAnchorCertProc fail %+v, tagType=%d", err, tagType)
			log.Errorln(msg)
			m.reporter.SendError(msg)
		} else {
			msg := fmt.Sprintf("AwardAnchorCertProc end tagType=%d", tagType)
			m.reporter.SendInfo(msg)
		}
	}()

	awardList, err := m.store.GetAnchorCertAwardList(uint32(settleMonthTm.Unix()), int32(tagType))
	if err != nil {
		log.Errorf("AwardAnchorCertProc GetAnchorCertAwardList fail %v, tagType=%d", err, tagType)
		return
	}

	highAnchorItemInfo, err := m.store.GetExamineCertByName(nil, "优质新主播")
	if err != nil {
		log.Errorf("AwardAnchorCertProc GetExamineCertByName fail %v, tagType=%d", err, tagType)
		return err
	}

	log.Infof("AwardAnchorCertProc begin settleMonthTm=%s, tagType=%d, awardList size=%d, highAnchorItemInfo.ItemId=%d",
		settleMonthTm.Format("200601"), tagType, len(awardList), highAnchorItemInfo.ItemId)

	for _, info := range awardList {
		if info.IsDone {
			log.Infof("AwardAnchorCertProc already award. tagType=%d, info=%+v", tagType, info)
			continue
		}
		err = m.AwardAnchorCert(settleMonthTm, info, highAnchorItemInfo.ItemId)
		if err != nil {
			log.Errorf("AwardAnchorCertProc AwardAnchorCert fail %v, tagType=%d", err, tagType)
			return err
		}
		log.Infof("AwardAnchorCertProc end tagType=%d, info=%+v", tagType, info)
	}
	return
}

func (m *AnchorContractMgr) SettleAnchorCertTwoDimensionsTimer() {

	now := time.Now()
	settleMonthTm := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, time.Local)
	log.Infof("SettleAnchorCertTwoDimensionsTimer work settleMonthTm %s", settleMonthTm)

	// 结算
	err := m.SettleAnchorCertTwoDimensions(settleMonthTm)
	if err != nil {
		log.Errorf("SettleAnchorCertTwoDimensionsTimer work settleMonthTm %s fail %v", settleMonthTm, err)
		return
	}

	// 发放标识
	err = m.AwardAnchorCertProc(settleMonthTm, uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_TwoDimensions))
	if err != nil {
		log.Errorf("SettleAnchorCertTwoDimensionsTimer AwardAnchorCertProc fail %v", err)
		return
	}

	err = m.DownGradeAnchorCertTwoDimension(settleMonthTm)
	if err != nil {
		log.Errorf("SettleAnchorCertTwoDimensionsTimer DownGradeAnchorCertTwoDimension fail %v", err)
		return
	}

}

// 情感+故事品类结算
func (m *AnchorContractMgr) SettleAnchorCertEmotionStory(settleMonthTm time.Time) (err error) {
	msg := fmt.Sprintf("SettleAnchorCertEmotionStory begin %s", settleMonthTm.Format("2006-01"))
	m.reporter.SendInfo(msg)
	defer func() {
		if r := recover(); r != nil {
			msg := fmt.Sprintf("SettleAnchorCertEmotionStory panic handler crashed: %+v, stack: %s", r, string(debug.Stack()))
			log.Errorf(msg)
			m.reporter.SendError(msg)
		}
		if err != nil {
			msg := fmt.Sprintf("SettleAnchorCertEmotionStory fail %+v", err)
			log.Errorln(msg)
			m.reporter.SendError(msg)
		} else {
			m.reporter.SendInfo("SettleAnchorCertEmotionStory end")
		}
	}()

	uids, err := m.GetAllAnchorUidByTagId([]uint32{TagIdTypeEmotion, TagIdTypeStory})
	if err != nil {
		log.Errorf("SettleAnchorCertEmotionStory GetAllAnchorUidByTagId fail err %v", err)
		return err
	}
	log.Infof("SettleAnchorCertEmotionStory emotionTagId=%d total=%d", TagIdTypeEmotion, len(uids))

	taskList := m.dyConfig.GetAnchorCertSettle().EmotionStory
	if len(taskList) == 0 {
		err = fmt.Errorf("结算配置不存在")
		log.Errorf("SettleAnchorCertEmotionStory GetAnchorCertSettle empty fail %v", err)
		return err
	}

	log.Infof("SettleAnchorCertEmotionStory begin settleMonthTm=%v, taskList=%s, uids size=%d",
		settleMonthTm, taskList, len(uids))

	limit := 50
	for i := 0; i < len(uids); i += limit {
		end := i + limit
		if end > len(uids) {
			end = len(uids)
		}

		uidsx := uids[i:end]
		if len(uidsx) == 0 {
			break
		}

		err = m.SettleAnchorCertEmotionStoryProc(uidsx, uint32(settleMonthTm.Unix()), taskList)
		if err != nil {
			return
		}
	}
	return nil
}

func (m *AnchorContractMgr) SettleAnchorCertEmotionStoryProc(uids []uint32, settleMonth uint32, taskList []*conf.AnchorCertSettleConfig) error {
	uid2Stats := map[uint32]*mysql.AnchorCertTaskStats{}
	taskLimit := taskList[0]
	settleMonthTm := time.Unix(int64(settleMonth), 0)
	monthBegin := time.Date(settleMonthTm.Year(), settleMonthTm.Month(), 1, 0, 0, 0, 0, time.Local)
	monthEnd := time.Date(settleMonthTm.Year(), settleMonthTm.Month()+1, 1, 0, 0, 0, 0, time.Local).Add(-time.Second)

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()
	monthStatsResp, serr := m.liveStatsCli.BatchGetAnchorMonthlyStatsByUid(ctx, uids, settleMonth)
	if serr != nil {
		log.Errorf("SettleAnchorCertEmotionStoryProc BatchGetAnchorMonthlyStatsByUid fail %v", serr)
		return serr
	}
	log.DebugfWithCtx(ctx, "SettleAnchorCertEmotionStoryProc BatchGetAnchorMonthlyStatsByUid uids=%v,settleMonth=%d, resp=%+v", uids, settleMonth, monthStatsResp)

	passUids := []uint32{}
	for _, info := range monthStatsResp.GetList() {
		var pass = false
		totalIncome := info.AnchorIncome + info.KnightIncome
		if info.LiveActiveCnt >= taskLimit.MonthActiveDays &&
			info.NewFansCnt >= taskLimit.MonthNewFansCnt &&
			info.ConsumerCnt >= taskLimit.MonthConsumeCnt &&
			totalIncome >= taskLimit.MonthIncome {

			pass = true
			passUids = append(passUids, info.AnchorUid)
			uid2Stats[info.AnchorUid] = &mysql.AnchorCertTaskStats{
				MonthActiveDays: info.LiveActiveCnt,
				MonthNewFansCnt: info.NewFansCnt,
				MonthConsumeCnt: info.ConsumerCnt,
				MonthIncome:     totalIncome,
			}
		}
		log.Infof("SettleAnchorCertEmotionStoryProc step1 monthStats uid %d, pass=%v LiveActiveCnt:[%d-%d], NewFansCnt:[%d-%d], ConsumerCnt:[%d-%d] MonthIncome:[%d(%d+%d)-%d]",
			info.AnchorUid, pass, info.LiveActiveCnt, taskLimit.MonthActiveDays, info.NewFansCnt, taskLimit.MonthNewFansCnt,
			info.ConsumerCnt, taskLimit.MonthConsumeCnt, totalIncome, info.AnchorIncome, info.KnightIncome, taskLimit.MonthIncome)
	}
	if len(passUids) == 0 {
		return nil
	}

	for _, uid := range passUids {
		lastUpdateTime, lastCheckLevel, err := m.getAnchorCheckLastLevel(ctx, uid)
		if err != nil {
			log.Errorf("SettleAnchorCertEmotionStoryProc getAnchorCheckLastLevel fail %v, uid=%d", err, uid)
			return err
		}
		log.Infof("SettleAnchorCertEmotionStoryProc step2 uid %d lastCheckLevel=%s", uid, lastCheckLevel)
		if !(lastCheckLevel == "S" || lastCheckLevel == "A" || lastCheckLevel == "B") {
			log.Infof("SettleAnchorCertEmotionStoryProc step2 no pass. uid %d lastCheckLevel=%s", uid, lastCheckLevel)
			continue
		}

		monthViolationACnt, monthViolationBCnt, monthViolationCCnt, err := m.filterViolation(ctx, uid, settleMonth)
		if err != nil {
			log.Errorf("SettleAnchorCertEmotionStoryProc filterViolation fail %v, uid=%d", err, uid)
			return err
		}
		log.Infof("SettleAnchorCertEmotionStoryProc step3 uid %d monthViolationACnt=%d, monthViolationBCnt=%d, monthViolationCCnt=%d",
			uid, monthViolationACnt, monthViolationBCnt, monthViolationCCnt)
		if !(monthViolationACnt <= taskLimit.MonthViolationACnt &&
			monthViolationBCnt <= taskLimit.MonthViolationBCnt &&
			monthViolationCCnt <= taskLimit.MonthViolationCCnt) {
			log.Infof("SettleAnchorCertEmotionStoryProc step3 no pass. uid %d monthViolationACnt=[%d-%d], monthViolationBCnt=[%d-%d], monthViolationCCnt=[%d-%d]",
				uid, monthViolationACnt, taskLimit.MonthViolationACnt, monthViolationBCnt, taskLimit.MonthViolationBCnt,
				monthViolationCCnt, taskLimit.MonthViolationCCnt)
			continue
		}

		competitionRecord := &mysql.AnchorCertCompetitionRecord{}
		_, competitionRecordList, err := m.store.GetAnchorCertCompetitionRecordList(uid,
			uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_EmotionStory), false, 0, 1)
		if err != nil {
			log.Errorf("SettleAnchorCertEmotionStoryProc GetAnchorCertCompetitionRecordList fail %v,uid=%d", err, uid)
			return err
		}
		if len(competitionRecordList) > 0 {
			competitionRecord = competitionRecordList[0]
		}
		log.Infof("SettleAnchorCertEmotionStoryProc uid=%d competitionRecord=%+v", uid, competitionRecord)

		vcPostCnt, err := m.GetVCPostList(ctx, uid, uint32(monthBegin.Unix()), uint32(monthEnd.Unix()))
		if err != nil {
			log.Errorf("SettleAnchorCertEmotionStoryProc GetVCPostList fail %v, uid=%d", err, uid)
			return err
		}
		log.Infof("SettleAnchorCertEmotionStoryProc GetVCPostList uid=%d vcPostCnt=%d", uid, vcPostCnt)

		uid2Stats[uid].LastCheckLevel = lastCheckLevel
		uid2Stats[uid].LastCheckTime = lastUpdateTime
		uid2Stats[uid].MonthViolationACnt = monthViolationACnt
		uid2Stats[uid].MonthViolationBCnt = monthViolationBCnt
		uid2Stats[uid].MonthViolationCCnt = monthViolationCCnt
		uid2Stats[uid].MonthCompetitionStatus = competitionRecord.MonthCompetitionStatus
		uid2Stats[uid].VoiceRecordCnt = vcPostCnt

		itemLevel, itemName := calcAnchorCertEmotionStory(uid, settleMonth, uid2Stats[uid], taskList)
		log.Infof("SettleAnchorCertEmotionStoryProc done uid=%d itemLevel=%d itemName=%s stats=%+v", uid, itemLevel, itemName, uid2Stats[uid])
		if itemName == "" {
			log.Infof("SettleAnchorCertEmotionStoryProc final no pass uid=%d stats=%s", uid, uid2Stats[uid])
			continue
		}

		settleMonthTm := time.Unix(int64(settleMonth), 0)
		err = m.store.InsertAnchorCertMonthTaskAwardLog(&mysql.AnchorCertMonthTaskAwardLog{
			Uid:       uid,
			YearMonth: uint32(settleMonthTm.Year()*100 + int(settleMonthTm.Month())),
			TagType:   uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_EmotionStory),
			ItemName:  itemName,
			ItemLevel: itemLevel,
			Extra:     uid2Stats[uid].String(),
		})
		if err != nil {
			log.Errorf("SettleAnchorCertEmotionStoryProc InsertAnchorCertMonthTaskAwardLog fail %v, uid=%d", err, uid)
			return err
		}
	}
	return nil
}

// 二次元品类结算
func (m *AnchorContractMgr) SettleAnchorCertTwoDimensions(settleMonthTm time.Time) (err error) {
	msg := fmt.Sprintf("SettleAnchorCertTwoDimensions begin %s", settleMonthTm.Format("2006-01"))
	m.reporter.SendInfo(msg)
	defer func() {
		if r := recover(); r != nil {
			msg := fmt.Sprintf("SettleAnchorCertTwoDimensions panic handler crashed: %+v, stack: %s", r, string(debug.Stack()))
			log.Errorf(msg)
			m.reporter.SendError(msg)
		}
		if err != nil {
			msg := fmt.Sprintf("SettleAnchorCertTwoDimensions fail %+v", err)
			log.Errorln(msg)
			m.reporter.SendError(msg)
		} else {
			m.reporter.SendInfo("SettleAnchorCertTwoDimensions end")
		}
	}()

	uids, err := m.GetAllAnchorUidByTagId([]uint32{TagIdTypeTwoDimension})
	if err != nil {
		log.Errorf("SettleAnchorCertTwoDimensions GetAllAnchorUidByTagId fail err %v", err)
		return err
	}
	log.Infof("SettleAnchorCertTwoDimensions twoDimensionTagId=%d total=%d", TagIdTypeTwoDimension, len(uids))

	taskList := m.dyConfig.GetAnchorCertSettle().TwoDimensions
	if len(taskList) == 0 {
		err = fmt.Errorf("结算配置不存在")
		log.Errorf("SettleAnchorCertTwoDimensions GetAnchorCertSettle empty fail %v", err)
		return err
	}

	log.Infof("SettleAnchorCertTwoDimensions begin settleMonthTm=%v, taskList=%s, uids size=%d",
		settleMonthTm, taskList, len(uids))

	limit := 50
	for i := 0; i < len(uids); i += limit {
		end := i + limit
		if end > len(uids) {
			end = len(uids)
		}

		uidsx := uids[i:end]
		if len(uidsx) == 0 {
			break
		}

		err = m.SettleAnchorCertTwoDimensionsProc(uidsx, uint32(settleMonthTm.Unix()), taskList)
		if err != nil {
			return
		}
	}
	return nil
}

func (m *AnchorContractMgr) SettleAnchorCertTwoDimensionsProc(uids []uint32, settleMonth uint32, taskList []*conf.AnchorCertSettleConfig) error {
	uid2Stats := map[uint32]*mysql.AnchorCertTaskStats{}
	taskLimit := taskList[0]

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()
	monthStatsResp, serr := m.liveStatsCli.BatchGetAnchorMonthlyStatsByUid(ctx, uids, settleMonth)
	if serr != nil {
		log.Errorf("SettleAnchorCertTwoDimensionsProc BatchGetAnchorMonthlyStatsByUid fail %v", serr)
		return serr
	}
	log.DebugfWithCtx(ctx, "SettleAnchorCertTwoDimensionsProc BatchGetAnchorMonthlyStatsByUid uids=%v,settleMonth=%d, resp=%+v", uids, settleMonth, monthStatsResp)

	passUids := []uint32{}
	for _, info := range monthStatsResp.GetList() {
		var pass = false
		totalIncome := info.AnchorIncome + info.KnightIncome
		if info.LiveActiveCnt >= taskLimit.MonthActiveDays &&
			info.NewFansCnt >= taskLimit.MonthNewFansCnt &&
			info.ConsumerCnt >= taskLimit.MonthConsumeCnt &&
			totalIncome >= taskLimit.MonthIncome {

			pass = true
			passUids = append(passUids, info.AnchorUid)
			uid2Stats[info.AnchorUid] = &mysql.AnchorCertTaskStats{
				MonthActiveDays: info.LiveActiveCnt,
				MonthNewFansCnt: info.NewFansCnt,
				MonthConsumeCnt: info.ConsumerCnt,
				MonthIncome:     totalIncome,
			}
		}
		log.Infof("SettleAnchorCertTwoDimensionsProc step1 monthStats uid %d, pass=%v LiveActiveCnt:[%d-%d], NewFansCnt:[%d-%d], ConsumerCnt:[%d-%d] MonthIncome:[%d(%d+%d)-%d]",
			info.AnchorUid, pass, info.LiveActiveCnt, taskLimit.MonthActiveDays, info.NewFansCnt, taskLimit.MonthNewFansCnt,
			info.ConsumerCnt, taskLimit.MonthConsumeCnt, totalIncome, info.AnchorIncome, info.KnightIncome, taskLimit.MonthIncome)
	}
	if len(passUids) == 0 {
		return nil
	}

	for _, uid := range passUids {
		lastUpdateTime, lastCheckLevel, err := m.getAnchorCheckLastLevel(ctx, uid)
		if err != nil {
			log.Errorf("SettleAnchorCertTwoDimensionsProc getAnchorCheckLastLevel fail %v", err)
			return err
		}
		log.Infof("SettleAnchorCertTwoDimensionsProc step2 uid %d lastCheckLevel=%s", uid, lastCheckLevel)
		if !(lastCheckLevel == "S" || lastCheckLevel == "A" || lastCheckLevel == "B") {
			log.Infof("SettleAnchorCertTwoDimensionsProc step2 no pass. uid %d lastCheckLevel=%s", uid, lastCheckLevel)
			continue
		}

		monthViolationACnt, monthViolationBCnt, monthViolationCCnt, err := m.filterViolation(ctx, uid, settleMonth)
		if err != nil {
			log.Errorf("SettleAnchorCertTwoDimensionsProc filterViolation fail %v", err)
			return err
		}
		log.Infof("SettleAnchorCertTwoDimensionsProc step3 uid %d monthViolationACnt=%d, monthViolationBCnt=%d, monthViolationCCnt=%d",
			uid, monthViolationACnt, monthViolationBCnt, monthViolationCCnt)
		if !(monthViolationACnt <= taskLimit.MonthViolationACnt &&
			monthViolationBCnt <= taskLimit.MonthViolationBCnt &&
			monthViolationCCnt <= taskLimit.MonthViolationCCnt) {
			log.Infof("SettleAnchorCertTwoDimensionsProc step3 no pass. uid %d monthViolationACnt=[%d-%d], monthViolationBCnt=[%d-%d], monthViolationCCnt=[%d-%d]",
				uid, monthViolationACnt, taskLimit.MonthViolationACnt, monthViolationBCnt, taskLimit.MonthViolationBCnt,
				monthViolationCCnt, taskLimit.MonthViolationCCnt)
			continue
		}

		competitionRecord := &mysql.AnchorCertCompetitionRecord{}
		_, competitionRecordList, err := m.store.GetAnchorCertCompetitionRecordList(uid,
			uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_TwoDimensions), false, 0, 1)
		if err != nil {
			log.Errorf("SettleAnchorCertTwoDimensionsProc GetAnchorCertCompetitionRecordList fail %v", err)
			return err
		}
		if len(competitionRecordList) > 0 {
			competitionRecord = competitionRecordList[0]
		}
		log.Infof("SettleAnchorCertTwoDimensionsProc uid %d competitionRecord=%+v", uid, competitionRecord)

		uid2Stats[uid].LastCheckLevel = lastCheckLevel
		uid2Stats[uid].LastCheckTime = lastUpdateTime
		uid2Stats[uid].MonthViolationACnt = monthViolationACnt
		uid2Stats[uid].MonthViolationBCnt = monthViolationBCnt
		uid2Stats[uid].MonthViolationCCnt = monthViolationCCnt
		uid2Stats[uid].MonthCompetitionStatus = competitionRecord.MonthCompetitionStatus
		uid2Stats[uid].DoubleMonthCompetitionStatus = competitionRecord.DoubleMonthCompetitionStatus

		itemLevel, itemName := calcAnchorCertTwoDimensions(uid, settleMonth, uid2Stats[uid], taskList)
		log.Infof("SettleAnchorCertTwoDimensionsProc done uid=%d itemLevel=%d itemName=%s stats=%+v", uid, itemLevel, itemName, uid2Stats[uid])
		if itemName == "" {
			log.Infof("SettleAnchorCertTwoDimensionsProc final no pass uid=%d stats=%s", uid, uid2Stats[uid])
			continue
		}

		settleMonthTm := time.Unix(int64(settleMonth), 0)
		err = m.store.InsertAnchorCertMonthTaskAwardLog(&mysql.AnchorCertMonthTaskAwardLog{
			Uid:       uid,
			YearMonth: uint32(settleMonthTm.Year()*100 + int(settleMonthTm.Month())),
			TagType:   uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_TwoDimensions),
			ItemName:  itemName,
			ItemLevel: itemLevel,
			Extra:     uid2Stats[uid].String(),
		})
		if err != nil {
			log.Errorf("SettleAnchorCertTwoDimensionsProc InsertAnchorCertMonthTaskAwardLog fail %v, uid=%d", err, uid)
			return err
		}
	}
	return nil
}

func (m *AnchorContractMgr) DownGradeAnchorCertMusic(settleMonthTm time.Time) (err error) {

	/*
		初旭歌手	连续3个月不保级，则降到皓月歌手（取消通过双周赛的身份）
		璀璨歌手	连续3个月不保级，则降到初旭歌手（取消通过季度赛的身份），再不保级（后连续3个月）则降到皓月歌手；
		todo1
		20230501结算, 结算4月数据
		select * from tbl_anchorcert_month_task_award_log where yearmonth=202301 and item_name='初旭歌手';
		select * from tbl_anchorcert_month_task_award_log where uid=1 and yearmonth in(202302,202303,202304);
	*/

	err = m.DownGradeAnchorCertMusicChuxu(settleMonthTm)
	if err != nil {
		log.Errorf("DownGradeAnchorCertMusicChuxu DownGradeAnchorCertMusicProc fail %v", err)
	}

	err = m.DownGradeAnchorCertMusicCuican1(settleMonthTm)
	if err != nil {
		log.Errorf("DownGradeAnchorCertMusicCuican1 DownGradeAnchorCertMusicProc fail %v", err)
	}

	err = m.DownGradeAnchorCertMusicCuican2(settleMonthTm)
	if err != nil {
		log.Errorf("DownGradeAnchorCertMusicCuican2 DownGradeAnchorCertMusicProc fail %v", err)
	}

	return nil
}

// 璀璨歌手 降级1
func (m *AnchorContractMgr) DownGradeAnchorCertMusicCuican1(settleMonthTm time.Time) (err error) {

	anchorCertConf := m.dyConfig.GetAnchorCertConf()
	musicCuicanName := anchorCertConf.MusicCuicanName
	musicCuicanKeepLevelList := anchorCertConf.MusicCuicanKeepLevelList
	log.Infof("DownGradeAnchorCertMusicCuican1 settleMonthTm %v, %s %v", settleMonthTm, musicCuicanName, musicCuicanKeepLevelList)

	checkMonth := time.Date(settleMonthTm.Year(), settleMonthTm.Month()-3, 1, 0, 0, 0, 0, time.Local)
	list, err := m.store.GetAnchorCertAwardListByItemName(uint32(checkMonth.Unix()), musicCuicanName)
	if err != nil {
		log.Errorf("DownGradeAnchorCertMusicCuican1 GetAnchorCertAwardListByItemName fail %v", err)
		return
	}
	log.Infof("DownGradeAnchorCertMusicCuican1 settleMonthTm=%s checkMonth=%s total=%d", settleMonthTm, checkMonth, len(list))
	for _, info := range list {
		uid := info.Uid
		var isKeep = false
		for i := 1; i <= 3; i++ {
			month := time.Date(checkMonth.Year(), checkMonth.Month()+time.Month(i), 1, 0, 0, 0, 0, time.Local)
			certInfo, err := m.store.GetAnchorCertAwardByUid(nil, uint32(month.Unix()), info.Uid)
			if err != nil {
				log.Errorf("DownGradeAnchorCertMusicCuican1 GetAnchorCertAwardByUid fail %v", err)
				return err
			}
			log.Infof("DownGradeAnchorCertMusicCuican1 uid=%d settleMonthTm=%s month=%s certInfo=%+v",
				uid, settleMonthTm, month, certInfo)

			for _, name := range musicCuicanKeepLevelList {
				if certInfo.ItemName == name {
					isKeep = true
					log.Infof("DownGradeAnchorCertMusicCuican1 is keep. uid=%d settleMonthTm=%s month=%s certInfo=%+v",
						uid, settleMonthTm, month, certInfo)
					break
				}
			}
			/*
				switch certInfo.ItemName {
				case "璀璨歌手", "燃乐歌手":
					isKeep = true
					log.Infof("DownGradeAnchorCertMusicCuican1 is keep. uid=%d settleMonthTm=%s month=%s certInfo=%+v",
						uid, settleMonthTm, month, certInfo)
					continue
				}
			*/
		}

		if !isKeep {
			_, recordList, err := m.store.GetAnchorCertCompetitionRecordList(info.Uid, info.TagType, false, 0, 1)
			if err != nil {
				log.Errorf("DownGradeAnchorCertMusicCuican1 GetAnchorCertCompetitionRecordList fail %v", err)
				return err
			}
			if len(recordList) == 0 {
				log.Infof("DownGradeAnchorCertMusicCuican1 no find record uid=%d", info.Uid)
				continue
			}
			record := recordList[0]
			log.Infof("DownGradeAnchorCertMusicCuican1 no keep record=%+v, QuarterCompetitionStatus=%d", record, record.QuarterCompetitionStatus)

			if record.QuarterCompetitionStatus == 1 {
				record.QuarterCompetitionStatus = 0
				record.Remark = "连续3月不保级，取消季度赛资格"
				record.Operator = "系统"
			} else {
				log.Infof("DownGradeAnchorCertMusicCuican1 no keep uid=%d status ignore", info.Uid)
				continue
			}

			err = m.store.Transaction(context.Background(), func(tx *gorm.DB) error {
				ok, err := m.store.UpdateAnchorCertCompetitionRecord(tx, record)
				if err != nil {
					log.Errorf("DownGradeAnchorCertMusicCuican1 UpdateAnchorCertCompetitionRecord fail %v, uid %d, RowsAffected %v", err, uid, ok)
					return err
				}
				err = m.store.AddAnchorCertCompetitionRecordLog(tx, record)
				if err != nil {
					log.Errorf("DownGradeAnchorCertMusicCuican1 AddAnchorCertCompetitionRecordLog fail %v, record=%+v", err, record)
					return err
				}
				return nil
			})
			if err != nil {
				log.Errorf("DownGradeAnchorCertMusicCuican1 Transaction fail %v, uid=%d", err, info.Uid)
				return err
			}
			log.Infof("DownGradeAnchorCertMusicCuican1 auto change record=%+v", record)
		}
	}
	return nil
}

// 璀璨歌手 降级2
func (m *AnchorContractMgr) DownGradeAnchorCertMusicCuican2(settleMonthTm time.Time) (err error) {

	anchorCertConf := m.dyConfig.GetAnchorCertConf()
	musicCuicanName := anchorCertConf.MusicCuicanName
	musicCuicanKeepLevelList := anchorCertConf.MusicCuicanKeepLevelList
	log.Infof("DownGradeAnchorCertMusicCuican2 settleMonthTm %v, %s %v", settleMonthTm, musicCuicanName, musicCuicanKeepLevelList)

	checkMonth := time.Date(settleMonthTm.Year(), settleMonthTm.Month()-6, 1, 0, 0, 0, 0, time.Local)
	list, err := m.store.GetAnchorCertAwardListByItemName(uint32(checkMonth.Unix()), musicCuicanName)
	if err != nil {
		log.Errorf("DownGradeAnchorCertMusicCuican2 GetAnchorCertAwardListByItemName fail %v", err)
		return
	}
	log.Infof("DownGradeAnchorCertMusicCuican2 settleMonthTm=%s checkMonth=%s total=%d", settleMonthTm, checkMonth, len(list))
	for _, info := range list {
		uid := info.Uid
		var isKeep = false
		for i := 1; i <= 6; i++ {
			month := time.Date(checkMonth.Year(), checkMonth.Month()+time.Month(i), 1, 0, 0, 0, 0, time.Local)
			certInfo, err := m.store.GetAnchorCertAwardByUid(nil, uint32(month.Unix()), info.Uid)
			if err != nil {
				log.Errorf("DownGradeAnchorCertMusicCuican2 GetAnchorCertAwardByUid fail %v", err)
				return err
			}
			log.Infof("DownGradeAnchorCertMusicCuican2 uid=%d settleMonthTm=%s month=%s certInfo=%+v",
				uid, settleMonthTm, month, certInfo)

			for _, name := range musicCuicanKeepLevelList {
				if certInfo.ItemName == name {
					isKeep = true
					log.Infof("DownGradeAnchorCertMusicCuican2 is keep. uid=%d settleMonthTm=%s month=%s certInfo=%+v",
						uid, settleMonthTm, month, certInfo)
					break
				}
			}
			/*
				switch certInfo.ItemName {
				case "璀璨歌手", "燃乐歌手":
					isKeep = true
					log.Infof("DownGradeAnchorCertMusicCuican2 is keep. uid=%d settleMonthTm=%s month=%s certInfo=%+v",
						uid, settleMonthTm, month, certInfo)
					continue
				}
			*/
		}

		if !isKeep {
			_, recordList, err := m.store.GetAnchorCertCompetitionRecordList(info.Uid, info.TagType, false, 0, 1)
			if err != nil {
				log.Errorf("DownGradeAnchorCertMusicCuican2 GetAnchorCertCompetitionRecordList fail %v", err)
				return err
			}
			if len(recordList) == 0 {
				log.Infof("DownGradeAnchorCertMusicCuican2 no find record uid=%d", info.Uid)
				continue
			}
			record := recordList[0]
			log.Infof("DownGradeAnchorCertMusicCuican2 no keep record=%+v", record)

			if record.BiweeklyCompetitionStatus == 1 {
				record.BiweeklyCompetitionStatus = 0
				record.Remark = "连续6月不保级，取消双周赛资格"
				record.Operator = "系统"
			} else {
				log.Infof("DownGradeAnchorCertMusicCuican2 no keep uid=%d status ignore", info.Uid)
				continue
			}

			err = m.store.Transaction(context.Background(), func(tx *gorm.DB) error {
				ok, err := m.store.UpdateAnchorCertCompetitionRecord(tx, record)
				if err != nil {
					log.Errorf("DownGradeAnchorCertMusicCuican2 UpdateAnchorCertCompetitionRecord fail %v, uid %d, RowsAffected %v", err, uid, ok)
					return err
				}
				err = m.store.AddAnchorCertCompetitionRecordLog(tx, record)
				if err != nil {
					log.Errorf("DownGradeAnchorCertMusicCuican2 AddAnchorCertCompetitionRecordLog fail %v, record=%+v", err, record)
					return err
				}
				return nil
			})
			if err != nil {
				log.Errorf("DownGradeAnchorCertMusicCuican2 Transaction fail %v", err)
				return err
			}
			log.Infof("DownGradeAnchorCertMusicCuican2 auto change record=%+v", record)
		}
	}
	return nil
}

// 初旭歌手 降级
func (m *AnchorContractMgr) DownGradeAnchorCertMusicChuxu(settleMonthTm time.Time) (err error) {

	anchorCertConf := m.dyConfig.GetAnchorCertConf()
	musicChuxuName := anchorCertConf.MusicChuxuName
	musicChuxuKeepLevelList := anchorCertConf.MusicChuxuKeepLevelList
	log.Infof("DownGradeAnchorCertMusicChuxu settleMonthTm %v, %s %v", settleMonthTm, musicChuxuName, musicChuxuKeepLevelList)

	checkMonth := time.Date(settleMonthTm.Year(), settleMonthTm.Month()-3, 1, 0, 0, 0, 0, time.Local)
	list, err := m.store.GetAnchorCertAwardListByItemName(uint32(checkMonth.Unix()), musicChuxuName)
	if err != nil {
		log.Errorf("DownGradeAnchorCertMusicChuxu GetAnchorCertAwardListByItemName fail %v", err)
		return
	}
	log.Infof("DownGradeAnchorCertMusicChuxu settleMonthTm=%s checkMonth=%s total=%d", settleMonthTm, checkMonth, len(list))
	for _, info := range list {
		uid := info.Uid
		var isKeep = false
		for i := 1; i <= 3; i++ {
			month := time.Date(checkMonth.Year(), checkMonth.Month()+time.Month(i), 1, 0, 0, 0, 0, time.Local)
			certInfo, err := m.store.GetAnchorCertAwardByUid(nil, uint32(month.Unix()), info.Uid)
			if err != nil {
				log.Errorf("DownGradeAnchorCertMusicChuxu GetAnchorCertAwardByUid fail %v", err)
				return err
			}
			log.Infof("DownGradeAnchorCertMusicChuxu uid=%d settleMonthTm=%s month=%s certInfo=%+v",
				uid, settleMonthTm, month, certInfo)

			for _, name := range musicChuxuKeepLevelList {
				if certInfo.ItemName == name {
					isKeep = true
					log.Infof("DownGradeAnchorCertMusicChuxu is keep. uid=%d settleMonthTm=%s month=%s certInfo=%+v",
						uid, settleMonthTm, month, certInfo)
					break
				}
			}

			/*
				switch certInfo.ItemName {
				case "初旭歌手", "璀璨歌手", "燃乐歌手":
					isKeep = true
					log.Infof("DownGradeAnchorCertMusicChuxu is keep. uid=%d settleMonthTm=%s month=%s certInfo=%+v",
						uid, settleMonthTm, month, certInfo)
					continue
				}
			*/
		}

		if !isKeep {
			_, recordList, err := m.store.GetAnchorCertCompetitionRecordList(info.Uid, info.TagType, false, 0, 1)
			if err != nil {
				log.Errorf("DownGradeAnchorCertMusicChuxu GetAnchorCertCompetitionRecordList fail %v", err)
				return err
			}
			if len(recordList) == 0 {
				log.Infof("DownGradeAnchorCertMusicChuxu no find record uid=%d", info.Uid)
				continue
			}
			record := recordList[0]
			log.Infof("DownGradeAnchorCertMusicChuxu no keep record=%+v", record)

			if record.BiweeklyCompetitionStatus == 1 {
				record.BiweeklyCompetitionStatus = 0
				record.Remark = "连续3月不保级，取消双周赛资格"
				record.Operator = "系统"
			} else {
				log.Infof("DownGradeAnchorCertMusicChuxu no keep uid=%d status ignore", info.Uid)
				continue
			}

			err = m.store.Transaction(context.Background(), func(tx *gorm.DB) error {
				ok, err := m.store.UpdateAnchorCertCompetitionRecord(tx, record)
				if err != nil {
					log.Errorf("DownGradeAnchorCertMusicChuxu UpdateAnchorCertCompetitionRecord fail %v, uid %d, RowsAffected %v", err, uid, ok)
					return err
				}
				err = m.store.AddAnchorCertCompetitionRecordLog(tx, record)
				if err != nil {
					log.Errorf("DownGradeAnchorCertMusicChuxu AddAnchorCertCompetitionRecordLog fail %v, record=%+v", err, record)
					return err
				}
				return nil
			})
			if err != nil {
				log.Errorf("DownGradeAnchorCertMusicChuxu Transaction fail %v", err)
				return err
			}
			log.Infof("DownGradeAnchorCertMusicChuxu auto change record=%+v", record)
		}
	}
	return nil
}

// 二次元 降级
func (m *AnchorContractMgr) DownGradeAnchorCertTwoDimension(settleMonthTm time.Time) (err error) {

	anchorCertConf := m.dyConfig.GetAnchorCertConf()
	twoDimensionName := anchorCertConf.TwoDimensionName
	twoDimensionNameKeepLevelList := anchorCertConf.TwoDimensionNameKeepLevelList
	log.Infof("DownGradeAnchorCertTwoDimension settleMonthTm %v, %s %v", settleMonthTm, twoDimensionName, twoDimensionNameKeepLevelList)

	//const downCertName = "超强CV"
	log.Infof("DownGradeAnchorCertTwoDimension settleMonthTm=%s downCertName=%q", settleMonthTm, twoDimensionName)

	checkMonth := time.Date(settleMonthTm.Year(), settleMonthTm.Month()-3, 1, 0, 0, 0, 0, time.Local)
	list, err := m.store.GetAnchorCertAwardListByItemName(uint32(checkMonth.Unix()), twoDimensionName)
	if err != nil {
		log.Errorf("DownGradeAnchorCertTwoDimension GetAnchorCertAwardListByItemName fail %v", err)
		return
	}
	log.Infof("DownGradeAnchorCertTwoDimension settleMonthTm=%s checkMonth=%s total=%d", settleMonthTm, checkMonth, len(list))
	for _, info := range list {
		uid := info.Uid
		var isKeep = false
		for i := 1; i <= 3; i++ {
			month := time.Date(checkMonth.Year(), checkMonth.Month()+time.Month(i), 1, 0, 0, 0, 0, time.Local)
			certInfo, err := m.store.GetAnchorCertAwardByUid(nil, uint32(month.Unix()), info.Uid)
			if err != nil {
				log.Errorf("DownGradeAnchorCertTwoDimension GetAnchorCertAwardByUid fail %v", err)
				return err
			}
			log.Infof("DownGradeAnchorCertTwoDimension uid=%d settleMonthTm=%s month=%s certInfo=%+v",
				uid, settleMonthTm, month, certInfo)

			for _, name := range twoDimensionNameKeepLevelList {
				if certInfo.ItemName == name {
					isKeep = true
					log.Infof("DownGradeAnchorCertTwoDimension is keep. uid=%d settleMonthTm=%s month=%s certInfo=%+v",
						uid, settleMonthTm, month, certInfo)
					break
				}
			}

			/*
				switch certInfo.ItemName {
				case downCertName:
					isKeep = true
				}
			*/
		}

		if !isKeep {
			_, recordList, err := m.store.GetAnchorCertCompetitionRecordList(info.Uid, info.TagType, false, 0, 1)
			if err != nil {
				log.Errorf("DownGradeAnchorCertTwoDimension GetAnchorCertCompetitionRecordList fail %v", err)
				return err
			}
			if len(recordList) == 0 {
				log.Infof("DownGradeAnchorCertTwoDimension no find record uid=%d", info.Uid)
				continue
			}
			record := recordList[0]
			log.Infof("DownGradeAnchorCertTwoDimension no keep record=%+v", record)

			if record.DoubleMonthCompetitionStatus == 1 {
				record.DoubleMonthCompetitionStatus = 0
				record.Remark = "连续3月不保级，取消双月挑战赛资格"
				record.Operator = "系统"
			} else {
				continue
			}

			err = m.store.Transaction(context.Background(), func(tx *gorm.DB) error {
				ok, err := m.store.UpdateAnchorCertCompetitionRecord(tx, record)
				if err != nil {
					log.Errorf("DownGradeAnchorCertTwoDimension UpdateAnchorCertCompetitionRecord fail %v, uid %d, RowsAffected %v", err, uid, ok)
					return err
				}
				err = m.store.AddAnchorCertCompetitionRecordLog(tx, record)
				if err != nil {
					log.Errorf("DownGradeAnchorCertTwoDimension AddAnchorCertCompetitionRecordLog fail %v, record=%+v", err, record)
					return err
				}
				return nil
			})
			if err != nil {
				log.Errorf("DownGradeAnchorCertTwoDimension Transaction fail %v", err)
				return err
			}
			log.Infof("DownGradeAnchorCertTwoDimension auto change record=%+v", record)
		}
	}
	return nil
}

// 音乐品类结算
func (m *AnchorContractMgr) SettleAnchorCertMusic(settleMonthTm time.Time) (err error) {
	msg := fmt.Sprintf("SettleAnchorCertMusic begin %s", settleMonthTm.Format("2006-01"))
	m.reporter.SendInfo(msg)
	defer func() {
		if r := recover(); r != nil {
			msg := fmt.Sprintf("SettleAnchorCertMusic panic handler crashed: %+v, stack: %s", r, string(debug.Stack()))
			log.Errorf(msg)
			m.reporter.SendError(msg)
		}
		if err != nil {
			msg := fmt.Sprintf("SettleAnchorCertMusic fail %+v", err)
			log.Errorln(msg)
			m.reporter.SendError(msg)
		} else {
			m.reporter.SendInfo("SettleAnchorCertMusic end")
		}
	}()

	uids, err := m.GetAllAnchorUidByTagId([]uint32{TagIdTypeMusic})
	if err != nil {
		log.Errorf("SettleAnchorCertMusic GetAllAnchorUidByTagId fail err %v", err)
		return err
	}
	log.Infof("SettleAnchorCertMusic musicTagId=%d total=%d", TagIdTypeMusic, len(uids))

	taskList := m.dyConfig.GetAnchorCertSettle().Music
	if len(taskList) == 0 {
		err = fmt.Errorf("结算配置不存在")
		log.Errorf("SettleAnchorCertMusic GetAnchorCertSettle empty fail %v", err)
		return err
	}

	taskLimit := taskList[0]
	log.Infof("SettleAnchorCertMusic begin settleMonthTm=%v, taskLimit=%+v, uids size=%d", settleMonthTm, taskLimit, len(uids))

	limit := 50
	for i := 0; i < len(uids); i += limit {
		end := i + limit
		if end > len(uids) {
			end = len(uids)
		}

		uidsx := uids[i:end]
		log.Debugf("SettleAnchorCertMusic %d-%d uids=%v", i, end, uidsx)
		if len(uidsx) == 0 {
			break
		}

		err = m.SettleAnchorCertMusicProc(uidsx, uint32(settleMonthTm.Unix()), taskList)
		if err != nil {
			return
		}
	}
	return nil
}

func (m *AnchorContractMgr) SettleAnchorCertMusicProc(uids []uint32, settleMonth uint32, taskList []*conf.AnchorCertSettleConfig) error {
	uid2Stats := map[uint32]*mysql.AnchorCertTaskStats{}
	taskLimit := taskList[0]

	anchorCertConf := m.dyConfig.GetAnchorCertConf()
	musicXingGuangName := anchorCertConf.MusicXingGuangName
	log.Infof("SettleAnchorCertMusicProc musicXingGuangName=%s", musicXingGuangName)

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()
	monthStatsResp, serr := m.liveStatsCli.BatchGetAnchorMonthlyStatsByUid(ctx, uids, settleMonth)
	if serr != nil {
		log.Errorf("SettleAnchorCertMusic BatchGetAnchorMonthlyStatsByUid fail %v", serr)
		return serr
	}
	log.DebugfWithCtx(ctx, "SettleAnchorCertMusicProc BatchGetAnchorMonthlyStatsByUid uids=%v,settleMonth=%d, resp=%+v", uids, settleMonth, monthStatsResp)

	passUids := []uint32{}
	for _, info := range monthStatsResp.GetList() {
		var pass = false
		totalIncome := info.AnchorIncome + info.KnightIncome
		if info.LiveActiveCnt >= taskLimit.MonthActiveDays &&
			info.NewFansCnt >= taskLimit.MonthNewFansCnt &&
			info.ConsumerCnt >= taskLimit.MonthConsumeCnt &&
			totalIncome >= taskLimit.MonthIncome {

			pass = true
			passUids = append(passUids, info.AnchorUid)
			uid2Stats[info.AnchorUid] = &mysql.AnchorCertTaskStats{
				MonthActiveDays: info.LiveActiveCnt,
				MonthNewFansCnt: info.NewFansCnt,
				MonthConsumeCnt: info.ConsumerCnt,
				MonthIncome:     totalIncome,
			}
		}
		log.Infof("SettleAnchorCertMusic step1 monthStats uid %d, pass=%v month=%q LiveActiveCnt:[%d-%d], NewFansCnt:[%d-%d], ConsumerCnt:[%d-%d] MonthIncome:[%d(%d+%d)-%d]",
			info.AnchorUid, pass, info.Yearmonth, info.LiveActiveCnt, taskLimit.MonthActiveDays, info.NewFansCnt, taskLimit.MonthNewFansCnt,
			info.ConsumerCnt, taskLimit.MonthConsumeCnt, totalIncome, info.AnchorIncome, info.KnightIncome, taskLimit.MonthIncome)
	}
	if len(passUids) == 0 {
		return nil
	}

	for _, uid := range passUids {
		lastUpdateTime, lastCheckLevel, err := m.getAnchorCheckLastLevel(ctx, uid)
		if err != nil {
			log.Errorf("SettleAnchorCertMusic getAnchorCheckLastLevel fail %v", err)
			return err
		}
		log.Infof("SettleAnchorCertMusic step2 uid %d lastCheckLevel=%s", uid, lastCheckLevel)
		if !(lastCheckLevel == "S" || lastCheckLevel == "A" || lastCheckLevel == "B") {
			log.Infof("SettleAnchorCertMusic step2 no pass. uid %d lastCheckLevel=%s", uid, lastCheckLevel)
			continue
		}

		monthViolationACnt, monthViolationBCnt, monthViolationCCnt, err := m.filterViolation(ctx, uid, settleMonth)
		if err != nil {
			log.Errorf("SettleAnchorCertMusic filterViolation fail %v, uid=%d", err, uid)
			return err
		}
		log.Infof("SettleAnchorCertMusic step3 uid %d monthViolationACnt=%d, monthViolationBCnt=%d, monthViolationCCnt=%d",
			uid, monthViolationACnt, monthViolationBCnt, monthViolationCCnt)
		if !(monthViolationACnt <= taskLimit.MonthViolationACnt &&
			monthViolationBCnt <= taskLimit.MonthViolationBCnt &&
			monthViolationCCnt <= taskLimit.MonthViolationCCnt) {
			log.Infof("SettleAnchorCertMusic step3 no pass. uid %d monthViolationACnt=[%d-%d], monthViolationBCnt=[%d-%d], monthViolationCCnt=[%d-%d]",
				uid, monthViolationACnt, taskLimit.MonthViolationACnt, monthViolationBCnt, taskLimit.MonthViolationBCnt,
				monthViolationCCnt, taskLimit.MonthViolationCCnt)
			continue
		}

		competitionRecord := &mysql.AnchorCertCompetitionRecord{}
		_, competitionRecordList, err := m.store.GetAnchorCertCompetitionRecordList(uid,
			uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_Music), false, 0, 1)
		if err != nil {
			log.Errorf("SettleAnchorCertMusic GetAnchorCertCompetitionRecordList fail %v", err)
			return err
		}
		if len(competitionRecordList) > 0 {
			competitionRecord = competitionRecordList[0]
		}
		log.Infof("SettleAnchorCertMusic uid %d competitionRecord=%+v", uid, competitionRecord)

		uid2Stats[uid].LastCheckLevel = lastCheckLevel
		uid2Stats[uid].LastCheckTime = lastUpdateTime
		uid2Stats[uid].MonthViolationACnt = monthViolationACnt
		uid2Stats[uid].MonthViolationBCnt = monthViolationBCnt
		uid2Stats[uid].MonthViolationCCnt = monthViolationCCnt
		uid2Stats[uid].CheckCompetitionStatus = competitionRecord.CheckCompetitionStatus
		uid2Stats[uid].QuarterCompetitionStatus = competitionRecord.QuarterCompetitionStatus
		uid2Stats[uid].BiweeklyCompetitionStatus = competitionRecord.BiweeklyCompetitionStatus
		uid2Stats[uid].BurnCompetitionStatus = competitionRecord.BurnCompetitionStatus
		uid2Stats[uid].MonthCompetitionStatus = competitionRecord.MonthCompetitionStatus
		uid2Stats[uid].DoubleMonthCompetitionStatus = competitionRecord.DoubleMonthCompetitionStatus

		itemLevel, itemName := calcAnchorCertMusicLevel(musicXingGuangName, uid, settleMonth, uid2Stats[uid], taskList)
		log.Infof("SettleAnchorCertMusic done uid=%d itemLevel=%d itemName=%q stats=%+v", uid, itemLevel, itemName, uid2Stats[uid])
		if itemName == "" {
			log.Infof("SettleAnchorCertMusic final no pass uid=%d stats=%s", uid, uid2Stats[uid])
			continue
		}

		settleMonthTm := time.Unix(int64(settleMonth), 0)
		err = m.store.InsertAnchorCertMonthTaskAwardLog(&mysql.AnchorCertMonthTaskAwardLog{
			Uid:       uid,
			YearMonth: uint32(settleMonthTm.Year()*100 + int(settleMonthTm.Month())),
			TagType:   uint32(pb.AnchorCertContentTagType_AnchorCertContentTagType_Music),
			ItemName:  itemName,
			ItemLevel: itemLevel,
			Extra:     uid2Stats[uid].String(),
		})
		if err != nil {
			log.Errorf("SettleAnchorCertMusic InsertAnchorCertMonthTaskAwardLog fail %v, uid=%d", err, uid)
			return err
		}
	}
	return nil
}

/*
  - 称号发放为本月全个自然月
  - 如当前有【优质新主播】标识时间冲突，则回收【优质新主播】标识，并发放认证标识
  - 如当前有其他标识时间冲突，则不发放认证标识，但在主播中心仍显示为当前认证等级
*/
// 发放标识 highAnchorItemId【优质新主播】标识 ID
func (m *AnchorContractMgr) AwardAnchorCert(settleMonthTm time.Time, info *mysql.AnchorCertMonthTaskAwardLog, highAnchorItemId uint32) error {
	now := time.Now()
	monthBegin := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	monthEnd := time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, time.Local).Add(-time.Second)
	log.Infof("AwardAnchorCert begin settleMonthTm=%s uid=%d tagType=%d itemName=%s itemLevel=%d awardTimeRange=[%v-%v]",
		settleMonthTm.Format("200601"), info.Uid, info.TagType, info.ItemName, info.ItemLevel, monthBegin, monthEnd)

	uid := info.Uid
	identityType := uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE)
	conflictCert := &mysql.AnchorExamineCert{}
	recordId := uint32(0)
	var autoRecycle = false
	err := m.store.Transaction(context.Background(), func(tx *gorm.DB) error {
		list, err := m.store.GetUserExamineCertListByUid(tx, uid, identityType)
		if err != nil {
			log.Errorf("AwardAnchorCert GetUserExamineCertListByUid fail %v uid %d", err, uid)
			return err
		}
		log.Infof("AwardAnchorCert GetUserExamineCertListByUid uid %d list=%+v", uid, list)

		startTs := time.Unix(int64(monthBegin.Unix()), 0)
		endTs := time.Unix(int64(monthEnd.Unix()), 0)
		for _, cert := range list {
			if m.IsIntersectOfTime(startTs, endTs, cert.StartTime, cert.EndTime) {
				conflictCert = cert
				break
			}
		}

		// 如果有冲突
		if conflictCert.Id > 0 {
			// 如当前有【优质新主播】标识时间冲突，则回收【优质新主播】标识，并发放认证标识
			if conflictCert.ItemId == highAnchorItemId {
				err = m.store.DeleteUserExamineCertByRecordId(tx, conflictCert.Id, "系统回收")
				if err != nil {
					log.Errorf("AwardAnchorCert DeleteUserExamineCertByRecordId, fail:%v, uid=%d recordId:%d", err, uid, conflictCert.Id)
					return err
				}
				autoRecycle = true
				log.Infof("AwardAnchorCert conflict uid=%d auto recycle ok, conflict=%+v", uid, conflictCert)
			} else {
				// 如当前有其他标识时间冲突，则不发放认证标识
				log.Errorf("AwardAnchorCert conflict ignore. uid=%d, conflict=%+v", uid, conflictCert)
				return nil
			}
		}

		// 查标识ID
		itemInfo, err := m.store.GetExamineCertByName(tx, info.ItemName)
		if err != nil {
			log.Errorf("AwardAnchorCert GetExamineCertByName fail %v, uid=%d", err, uid)
			return err
		}
		if itemInfo.ItemId == 0 {
			err = fmt.Errorf("itemId not exit.")
			log.Errorf("AwardAnchorCert GetExamineCertByName no find %v, ItemName=%s, uid=%d", err, info.ItemName, uid)
			return err
		}

		recordId, err = m.store.SetUserExamineCert(tx, info.Uid, itemInfo.ItemId, uint32(pb.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE),
			"", "系统下发", uint32(monthBegin.Unix()), uint32(monthEnd.Unix()))
		if err != nil {
			log.Errorf("AwardAnchorCert store.SetUserExamineCert fail %v, uid:%d", err, info.Uid)
			return err
		}

		// 状态改为已发放
		rowsAffected, err := m.store.UpdateAnchorCertAwardStatus(tx, uint32(settleMonthTm.Unix()), info.Uid)
		if err != nil {
			log.Errorf("AwardAnchorCert UpdateAnchorCertAwardStatus, fail %v, uid:%d", err, info.Uid)
			return err
		}
		log.Infof("AwardAnchorCert Transaction. uid=%d, rowsAffected=%d", info.Uid, rowsAffected)
		return nil
	})
	if err != nil {
		log.Errorf("AwardAnchorCert Transaction fail %v, uid=%d", err, uid)
		return err
	}

	if autoRecycle {
		err = m.cache.RemExamineNotifyDelayQueue(cache.PushExamineCertMsg{Uid: uid, RecordId: conflictCert.Id})
		if err != nil {
			log.Errorf("AwardAnchorCert failed to cache.RemExamineNotifyDelayQueue, err:%s, conflictCert:%+v, uid:%d", err.Error(), conflictCert, uid)
		}
		err = m.cache.Publish()
		if err != nil {
			log.Errorf("AwardAnchorCert failed to cache.Publish err:%v, conflictCert:%+v", err, conflictCert)
		}

		if conflictCert.Status == uint32(pb.UserExamineCert_Status_UserExamineCert_Status_Effect) {
			itemInfo, err := m.store.GetExamineCert(conflictCert.ItemId)
			if err != nil {
				log.Errorf("AwardAnchorCert GetExamineCert faiil %v, uid=%d", err, uid)
			}
			_ = m.ImPushCertRecycle(uid, conflictCert.IdentityType, conflictCert.ItemId, itemInfo.ItemName)
		}
		log.Infof("AwardAnchorCert autoRecycle uid %d conflictCert=%+v", uid, conflictCert)
	}

	if recordId > 0 {
		err = m.cache.PushExamineNotifyQueue([]cache.PushExamineCertMsg{
			{Uid: info.Uid, RecordId: recordId, PushTs: uint32(monthBegin.Unix()), NotifyType: cache.AnchorCertNotifyGrant},
			{Uid: info.Uid, RecordId: recordId, PushTs: uint32(monthEnd.Unix()), NotifyType: cache.AnchorCertNotifyRecycle},
		})
		if err != nil {
			log.Errorf("AwardAnchorCert PushExamineNotifyQueue, fail %v, uid:%d", err, info.Uid)
		}
	}

	log.Infof("AwardAnchorCert end settleMonthTm=%s uid=%d tagType=%d itemName=%s itemLevel=%d recordId=%d",
		settleMonthTm.Format("200601"), info.Uid, info.TagType, info.ItemName, info.ItemLevel, recordId)
	return nil
}

func calcAnchorCertMusicLevel(musicXingGuangName string, uid uint32, settleMonthTs uint32, stats *mysql.AnchorCertTaskStats, taskList []*conf.AnchorCertSettleConfig) (itemLevel uint32, itemName string) {
	for i := len(taskList) - 1; i >= 0; i-- {
		task := taskList[i]

		if stats.MonthActiveDays >= task.MonthActiveDays &&
			stats.MonthNewFansCnt >= task.MonthNewFansCnt &&
			stats.MonthConsumeCnt >= task.MonthConsumeCnt &&
			stats.MonthIncome >= task.MonthIncome &&
			stats.CheckCompetitionStatus == task.CheckCompetitionStatus &&
			stats.BiweeklyCompetitionStatus == task.BiweeklyCompetitionStatus &&
			stats.QuarterCompetitionStatus == task.QuarterCompetitionStatus &&
			stats.BurnCompetitionStatus == task.BurnCompetitionStatus {

			if task.ItemName != musicXingGuangName {
				itemLevel = task.ItemLevel
				itemName = task.ItemName
				return
			}

			anchorType := getAnchorTypeByCheckTime(uid, settleMonthTs, int64(stats.LastCheckTime))
			needMonthActiveDays := task.Extra.AnchorType2MonthActiveDays[fmt.Sprintf("%d", anchorType)]
			log.Infof("calcAnchorCertMusicLevel 星光歌手 uid=%d settleMonthTs=%d LastCheckTime=%d anchorType=%d needMonthActiveDays=%d stats=%+v",
				uid, settleMonthTs, stats.LastCheckTime, anchorType, needMonthActiveDays, stats)
			if stats.MonthActiveDays >= needMonthActiveDays {
				itemLevel = task.ItemLevel
				itemName = task.ItemName
				return
			}
		}
	}
	return
}

func calcAnchorCertEmotionStory(uid uint32, settleMonthTs uint32, stats *mysql.AnchorCertTaskStats, taskList []*conf.AnchorCertSettleConfig) (itemLevel uint32, itemName string) {
	for i := len(taskList) - 1; i >= 0; i-- {
		task := taskList[i]
		if stats.MonthActiveDays >= task.MonthActiveDays &&
			stats.MonthNewFansCnt >= task.MonthNewFansCnt &&
			stats.MonthConsumeCnt >= task.MonthConsumeCnt &&
			stats.MonthIncome >= task.MonthIncome &&
			stats.VoiceRecordCnt >= task.VoiceRecordCnt &&
			stats.MonthCompetitionStatus == task.MonthCompetitionStatus &&
			stats.MonthViolationACnt <= task.MonthViolationACnt &&
			stats.MonthViolationBCnt <= task.MonthViolationBCnt &&
			stats.MonthViolationCCnt <= task.MonthViolationCCnt {

			itemLevel = task.ItemLevel
			itemName = task.ItemName
			return
		}
	}
	return
}

func calcAnchorCertTwoDimensions(uid uint32, settleMonthTs uint32, stats *mysql.AnchorCertTaskStats, taskList []*conf.AnchorCertSettleConfig) (itemLevel uint32, itemName string) {
	for i := len(taskList) - 1; i >= 0; i-- {
		task := taskList[i]
		if stats.MonthActiveDays >= task.MonthActiveDays &&
			stats.MonthNewFansCnt >= task.MonthNewFansCnt &&
			stats.MonthConsumeCnt >= task.MonthConsumeCnt &&
			stats.MonthIncome >= task.MonthIncome &&
			stats.MonthCompetitionStatus == task.MonthCompetitionStatus &&
			stats.DoubleMonthCompetitionStatus == task.DoubleMonthCompetitionStatus &&
			stats.MonthViolationACnt <= task.MonthViolationACnt &&
			stats.MonthViolationBCnt <= task.MonthViolationBCnt &&
			stats.MonthViolationCCnt <= task.MonthViolationCCnt {

			itemLevel = task.ItemLevel
			itemName = task.ItemName
			return
		}
	}
	return
}

const (
	AnchorTypeByCheckTime_Others              = 0
	AnchorTypeByCheckTime_LastMonthBefore11th = 1
	AnchorTypeByCheckTime_LastMonthAfter11th  = 2
)

/*
	"month_platform_days_enum_desc": {
	          "0": "非类型1,2的主播",
	          "1": "上月通过考核且通过考核时间在10号前（包含10号）的主播",
	          "2": "上月通过考核且通过考核时间在11号后（包含11号）的主播"
	        }
*/
func getAnchorTypeByCheckTime(uid uint32, settleMonthTs uint32, lastCheckTime int64) uint32 {
	settleMonthTm := time.Unix(int64(settleMonthTs), 0)
	settleMonthBegin := time.Date(settleMonthTm.Year(), settleMonthTm.Month(), 1, 0, 0, 0, 0, time.Local).Unix()
	settleMonthEnd := time.Date(settleMonthTm.Year(), settleMonthTm.Month()+1, 1, 0, 0, 0, 0, time.Local).Add(-time.Second).Unix()
	checkTimeStep := time.Date(settleMonthTm.Year(), settleMonthTm.Month(), 11, 0, 0, 0, 0, time.Local).Unix()

	if lastCheckTime >= settleMonthBegin && lastCheckTime <= settleMonthEnd {
		if lastCheckTime < checkTimeStep {
			return AnchorTypeByCheckTime_LastMonthBefore11th
		} else {
			return AnchorTypeByCheckTime_LastMonthAfter11th
		}
	} else {
		return AnchorTypeByCheckTime_Others
	}
}

func (m *AnchorContractMgr) GetAllAnchorUidByTagId(tagIdList []uint32) ([]uint32, error) {
	var page, pageSize uint32 = 1, 1000
	uids := []uint32{}
	tagMap := map[uint32]bool{}
	for _, tag := range tagIdList {
		tagMap[tag] = true
	}
	log.Infof("tagMap=%+v", tagMap)
	for {
		// 全量主播uid channel-live-mgr.GetAnchorList tbl_channel_live
		// select %s from tbl_channel_live where end_time >= now() limit ?,?
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		defer cancel()

		anchorList, err := m.liveMgrCli.GetAnchorList(ctx, page, pageSize)
		if err != nil {
			log.Errorf("getAllAnchorUidByTagId GetAnchorList fail %v", err)
			return nil, err
		}
		if len(anchorList) == 0 {
			return uids, nil
		}

		for _, info := range anchorList {
			if tagMap[info.GetTagId()] {
				uids = append(uids, info.GetUid())
				log.DebugfWithCtx(ctx, "GetAllAnchorUidByTagId uid %d tag %d", info.Uid, info.TagId)
			}
		}
		page++
	}
}

func conventUpgradeTaskType(tagId uint32) uint32 {
	if tagId == TagIdTypeMusic {
		return uint32(pb.AnchorCertUpgradeTaskType_AnchorCertUpgradeTaskType_Music)
	}
	if tagId == TagIdTypeEmotion {
		return uint32(pb.AnchorCertUpgradeTaskType_AnchorCertUpgradeTaskType_EmotionStory)
	}
	if tagId == TagIdTypeStory {
		return uint32(pb.AnchorCertUpgradeTaskType_AnchorCertUpgradeTaskType_EmotionStory)
	}
	if tagId == TagIdTypeTwoDimension {
		return uint32(pb.AnchorCertUpgradeTaskType_AnchorCertUpgradeTaskType_TwoDimension)
	}
	return uint32(pb.AnchorCertUpgradeTaskType_AnchorCertUpgradeTaskType_Invalid)
}

// =====================================================
func (m *AnchorContractMgr) UpdateAnchorExtraCertEffect() {
	list, err := m.store.GetAnchorExtraCertEffect(time.Now())
	if err != nil {
		log.Errorf("updateAnchorCertLimitEffect GetAnchorExtraCertEffect fail %v", err)
		return
	}
	log.Infof("updateAnchorExtraCertEffect total=%d", len(list))
	if len(list) == 0 {
		return
	}

	itemIds := []uint32{}
	for _, cert := range list {
		itemIds = append(itemIds, cert.ItemId)
	}
	itemid2Info, err := m.store.BatchGetExamineCertInfo(itemIds)
	if err != nil {
		log.Errorf("updateAnchorExtraCertEffect BatchGetExamineCertInfo fail %v", err)
		return
	}

	certs := []*pb.ExamineCertMsg{}
	for _, cert := range list {
		if itemInfo, ok := itemid2Info[cert.ItemId]; ok {
			certs = append(certs, &pb.ExamineCertMsg{
				Uid:         cert.Uid,
				ItemId:      cert.ItemId,
				ItemName:    itemInfo.ItemName,
				BaseImgurl:  itemInfo.BaseImgurl,
				ShadowColor: itemInfo.ShadowColor,
				StartTime:   uint32(cert.StartTime.Unix()),
				EndTime:     uint32(cert.EndTime.Unix()),
			})
		} else {
			log.Errorf("updateAnchorExtraCertEffect no find itemId=%d", cert.ItemId)
		}
	}

	m.cache.LocalCache.UpdateAnchorExtraCert(certs)
	log.Infof("updateAnchorExtraCertEffect done total=%d", len(certs))
}

func (m *AnchorContractMgr) getAnchorExtraCertUse(uid uint32) *pb.ExamineCertMsg {
	extraCerts := m.cache.LocalCache.GetAnchorExtraCert(uid)
	nowlevel := uint32(0)
	useCert := &pb.ExamineCertMsg{}
	for _, cert := range extraCerts {
		level := m.dyConfig.GetAnchorExtraCertLevel(cert.ItemId)
		if nowlevel < uint32(level) {
			useCert = cert
			nowlevel = uint32(level)
		}
	}
	log.Debugf("getAnchorExtraCertUse uid=%d, extraCerts=%+v, useCert=%+v", uid, extraCerts, useCert)
	return useCert
}

func (m *AnchorContractMgr) SetAnchorExtraCert(ctx context.Context, in *pb.SetAnchorExtraCertReq) (out *pb.CertEmptyMsg, err error) {
	out = &pb.CertEmptyMsg{}
	defer func() {
		log.InfoWithCtx(ctx, "SetAnchorExtraCert in=%s err=%+v", in.String(), err)
	}()

	if len(in.UidList) == 0 {
		return out, nil
	}
	now := uint32(time.Now().Unix())
	if in.GetStartTime() == 0 || in.GetEndTime() == 0 || in.GetStartTime() >= in.GetEndTime() || now >= in.GetEndTime() {
		return out, ErrInvalidTime
	}
	if in.ItemId == 0 {
		return out, ErrInvalidParamer
	}

	itemInfo, err := m.store.GetExamineCert(in.ItemId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetAnchorExtraCert GetExamineCert fail %v in=%+v", err, in)
		return out, err
	}
	if itemInfo.ItemId == 0 {
		return out, ErrNoFindCert
	}

	if len(in.UidList) > 2000 {
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "批量发放上限2000")
	}

	list := []*mysql.AnchorCertExtraAwardInfo{}
	for _, uid := range in.UidList {
		list = append(list, &mysql.AnchorCertExtraAwardInfo{
			Uid:       uid,
			ItemId:    in.ItemId,
			StartTime: time.Unix(int64(in.StartTime), 0),
			EndTime:   time.Unix(int64(in.EndTime), 0),
			Operator:  in.Handler,
		})
	}
	err = m.store.InsertAnchorExtraCertRecord(list)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetAnchorExtraCert InsertAnchorExtraCertRecord fail %v in=%+v", err, in)
		return out, err
	}
	return out, nil
}

func (m *AnchorContractMgr) GetAnchorExtraCertHistory(ctx context.Context, in *pb.GetAnchorExtraCertHistoryReq) (
	*pb.GetAnchorExtraCertHistoryResp, error) {
	out := &pb.GetAnchorExtraCertHistoryResp{}
	list, err := m.store.GetAnchorExtraCertHistory(in.BeginTime, in.EndTime, in.UidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAnchorExtraCertHistory fail %v", err)
		return out, err
	}
	for _, info := range list {
		out.List = append(out.List, &pb.GetAnchorExtraCertHistoryResp_Info{
			ItemId:     info.ItemId,
			StartTime:  uint32(info.StartTime.Unix()),
			EndTime:    uint32(info.EndTime.Unix()),
			Uid:        info.Uid,
			Handler:    info.Operator,
			CreateTime: uint32(info.CreateTime.Unix()),
		})
	}
	return out, nil
}
