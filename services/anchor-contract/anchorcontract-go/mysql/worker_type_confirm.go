package mysql

import (
	"errors"
	"github.com/jinzhu/gorm"
	"time"
)

// 用来存放需要进行身份验证的用户

type WorkerTypeConfirm struct {
	Uid        uint32    `db:"uid"` // 用户id
	GuildId    uint32    `db:"guild_id"`
	WorkerType uint32    `db:"worker_type"` // 从业者类型
	IsConfirm  bool      `db:"is_confirm"`
	CreateTime time.Time `db:"create_time"`
	//ExpireTime        time.Time `db:"expire_time"` // 过期时间
	UpdateTime time.Time `db:"update_time"` // 更新时间
}

const CreateWorkerTypeConfirmTbl = `CREATE TABLE IF NOT EXISTS worker_type_confirm (
  	uid INT(11) NOT NULL DEFAULT 0 COMMENT '用户id',
  	guild_id INT(11) NOT NULL DEFAULT 0 COMMENT '公会id',
  	worker_type INT(11) NOT NULL DEFAULT 0 COMMENT '从业者类型',
  	is_confirm TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否确认',
  	create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (uid, guild_id)  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='从业者类型确认表';`

func (w *WorkerTypeConfirm) TableName() string {
	return "worker_type_confirm"
}

func (s *Store) AddWorkerTypeConfirm(tx *gorm.DB, info *WorkerTypeConfirm) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	// 如果时间为0
	if info.UpdateTime.IsZero() {
		info.UpdateTime = time.Now()
	}

	if info.CreateTime.IsZero() {
		info.CreateTime = time.Now()
	}

	return db.Create(info).Error
}

func (s *Store) GetWorkerTypeConfirm(tx *gorm.DB, uid, guildId uint32) (*WorkerTypeConfirm, error) {
	db := s.db
	if tx != nil {
		db = tx
	}

	var info WorkerTypeConfirm
	err := db.Where("uid = ? and guild_id = ?", uid, guildId).First(&info).Error
	// 如果没有结果
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	if err != nil {
		return nil, err
	}

	return &info, nil
}

func (s *Store) UpdateWorkerTypeConfirm(tx *gorm.DB, info *WorkerTypeConfirm) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	if info.UpdateTime.IsZero() {
		info.UpdateTime = time.Now()
	}

	return db.Table(info.TableName()).Where("uid = ? AND guild_id = ?", info.Uid, info.GuildId).Updates(info).Error
}
