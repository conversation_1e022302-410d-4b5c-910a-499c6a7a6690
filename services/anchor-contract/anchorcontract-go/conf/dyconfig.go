package conf

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"sort"
	"sync"
	"time"

	"golang.52tt.com/pkg/files"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"

	pb "golang.52tt.com/protocol/services/anchorcontract-go"
)

const DyconfigPath = "/data/oss/conf-center/tt/guild_sign_anchor_cfg.json"

var os2Name = map[protocol.OS]string{
	protocol.ANDROID: "android",
	protocol.IOS:     "ios",
}

type sConfObj struct {
	// 公会签约限额白名单
	GuildSignAnchorLimit    []*GuildSignAnchor `json:"guild_sign_anchor_limit"`
	WhiteBigAnchor          []uint32           `json:"white_big_anchor"`
	ReleaseTs               uint32             `json:"release_ts"` // 客户端发版时间
	GuildId2SignAnchorLimit map[uint32]*GuildSignAnchor
	FeishuNotice            string `json:"feishu_notice"`

	// 公会签约限额等级
	GuildLevel       []*GuildLevelInfo `json:"guild_level"`
	GuildLevel2Limit map[uint32]uint32

	// 主播标识 推送msg
	LiveCertMsgDefaultCfg  *AnchorCertMsgConfig   `json:"live_cert_default_cfg"`
	MulitCertMsgDefaultCfg *AnchorCertMsgConfig   `json:"multi_cert_default_cfg"`
	AnchorCertMsgCfgs      []*AnchorCertMsgConfig `json:"anchor_cert_cfg_list"`
	ItemId2CertMsgCfg      map[uint32]*AnchorCertMsgConfig

	TagInfoList []*TagInfo `json:"tag_info_list"`
	TagId2Name  map[uint32]string

	//map guild_id -> parent_guild_id
	BindGuildMap    map[uint32]uint32
	ParentGuildList []*ParentGuild `json:"parent_guild_list"`

	// 成员中心url配置
	AppnameConfig struct {
		Vals []struct {
			MarketId uint32 `json:"market_id"`
			Value    string `json:"value"`
		} `json:"values"`
	} `json:"marketid_config"`
	MarketId2App         map[uint32]string
	App2Os2Env2CenterUrl map[string]map[string]map[string]string `json:"multiplayer_center_url"`

	AutoFollowPublicSwitch bool
	FollowPublicWhiteUids  []uint32

	*AnchorCertSettle `json:"anchor_cert_settle"`

	AnchorCertAutoSettle bool

	SignAnchorRiskAccount            SignAnchorRiskAccount `json:"sign_anchor_risk_account"`
	AutoPassAnchorApplySignReleaseTs uint32
	AutoPassAnchorApplySignPreTime   uint32

	AutoPassOrRejectMultiPlayerTs uint32 `json:"auto_pass_or_reject_multi_player_ts"`

	GuildSignAnchorLimitDayTh uint32 // 第几天判断日限额上限

	AnchorExtraCert                 AnchorExtraCERT `json:"anchor_extra_cert"`
	AnchorExtraCertId2LevelMap      map[uint32]uint32
	AnchorExtraCertLocalCacheTTLSec uint32

	EnablePush    bool
	WarnFeishuUrl string

	AnchorCertConf AnchorCertConf `json:"anchor_cert_conf"`

	CancalContractCondition CancalContractCondition `json:"CancalContractCondition"`
}

type CancalContractCondition struct {
	LiveAnchorIncomeRMB uint32
	LiveAnchorActiveDay uint32

	MultiPlayerPersonIncomeRMB            uint32
	MultiPlayerGuildChannelFeeRMB         uint32
	MultiPlayerGuildChannelFeeCheckSwitch bool

	LiveAnchorSignMonthLimit uint32
}

type AnchorCertConf struct {
	MusicXingGuangName string

	MusicChuxuName          string
	MusicChuxuKeepLevelList []string

	MusicCuicanName          string
	MusicCuicanKeepLevelList []string

	TwoDimensionName              string
	TwoDimensionNameKeepLevelList []string
}

type AnchorExtraCERT struct {
	List []List `json:"list"`
}

type List struct {
	Level      uint32   `json:"level"`
	ItemIDList []uint32 `json:"item_id_list"`
}

type SignAnchorRiskAccount struct {
	RealnameAge         int64 `json:"realname_age"`
	LatelyDay30Recharge int64 `json:"lately_day30_recharge"`
}

type AnchorCertSettle struct {
	Music         []*AnchorCertSettleConfig `json:"music"`
	EmotionStory  []*AnchorCertSettleConfig `json:"emotion_story"`
	TwoDimensions []*AnchorCertSettleConfig `json:"two_dimensions"`
}

type AnchorCertSettleConfig struct {
	ItemLevel                    uint32                       `json:"item_level"`
	ItemName                     string                       `json:"item_name"`
	MonthViolationACnt           uint32                       `json:"month_violation_a_cnt"`
	MonthViolationBCnt           uint32                       `json:"month_violation_b_cnt"`
	MonthViolationCCnt           uint32                       `json:"month_violation_c_cnt"`
	MonthActiveDays              uint32                       `json:"month_active_days"`
	MonthNewFansCnt              uint32                       `json:"month_new_fans_cnt"`
	MonthConsumeCnt              uint32                       `json:"month_consume_cnt"`
	MonthIncome                  uint32                       `json:"month_income"`
	CheckLevel                   []string                     `json:"check_level"`
	CheckCompetitionStatus       uint32                       `json:"check_competition_status"`
	QuarterCompetitionStatus     uint32                       `json:"quarter_competition_status"`
	BiweeklyCompetitionStatus    uint32                       `json:"biweekly_competition_status"`
	BurnCompetitionStatus        uint32                       `json:"burn_competition_status"`
	MonthCompetitionStatus       uint32                       `json:"month_competition_status"`
	DoubleMonthCompetitionStatus uint32                       `json:"double_month_competition_status"`
	VoiceRecordCnt               uint32                       `json:"voice_record_cnt"`
	Extra                        *AnchorCertSettleConfigExtra `json:"extra"`
}

type AnchorCertSettleConfigExtra struct {
	AnchorType2MonthActiveDays map[string]uint32 `json:"month_active_days"`
}

func (g AnchorCertSettleConfig) String() string {
	s, _ := json.Marshal(g)
	return string(s)
}

type ParentGuild struct {
	GuildId       uint32 `json:"guild_id"`
	ParentGuildId uint32 `json:"parent_guild_id"`
}

type AnchorCertMsgConfig struct {
	ItemId       uint32         `json:"item_id"`
	IdentityType uint32         `json:"identity_type"`
	GrantMsg     *AnchorCertMsg `json:"grant_msg"`
	RecycleMsg   *AnchorCertMsg `json:"recycle_msg"`
	OverdueMsg   *AnchorCertMsg `json:"overdue_msg"`
}

type AnchorCertMsg struct {
	FormatMsg string `json:"format_msg"`
	Url       string `json:"url"`
}

type GuildSignAnchor struct {
	GuildId uint32 `json:"guild_id"`
	Limit   uint32 `json:"limit"`
	BeginTs uint32 `json:"begin_ts"`
	EndTs   uint32 `json:"end_ts"`
}

type TagInfo struct {
	Tagid   uint32 `json:"tag_id"`
	TagName string `json:"tag_name"`
}

type SDyConfigHandler struct {
	confCenterPath string

	confStrLck sync.RWMutex
	confObj    *sConfObj
}

func NewConfigHandler(confPath string) (*SDyConfigHandler, error) {
	if confPath == "" {
		return nil, protocol.NewExactServerError(nil, status.ErrSys)
	}

	tmpConf := &sConfObj{}
	return &SDyConfigHandler{confCenterPath: confPath, confStrLck: sync.RWMutex{}, confObj: tmpConf}, nil
}

func (s *SDyConfigHandler) Start() (err error) {

	err = s.loadConf(s.confCenterPath)
	if nil != err {
		log.Errorf("load conf failed, confPath: %s, err: %s", s.confCenterPath, err)
		return err
	}

	go files.NewFileModifyWatch(s.confCenterPath, time.Second).Start(func() {
		_ = s.loadConf(s.confCenterPath)
	})

	return nil
}

func (s *SDyConfigHandler) loadConf(path string) (err error) {
	defer func() {
		if sEx := recover(); nil != sEx {
			log.Errorf("load filePath: %s, catch ex: %s", path, sEx)
			err = sEx.(error)
		}
	}()

	buffer, err := ioutil.ReadFile(path)
	if err != nil {
		log.Errorf("genPushConfig read file:%s failed, err: %s", path, err)
		return err
	}

	if 0 != len(buffer) {
		s.confStrLck.Lock()
		defer s.confStrLck.Unlock()

		tmpConf := sConfObj{}
		err = json.Unmarshal(buffer, &tmpConf)
		if nil != err {
			log.Errorf("load conf: %s failed, str: %s", path, string(buffer))
		} else {

			if len(tmpConf.GuildLevel) == 0 ||
				tmpConf.LiveCertMsgDefaultCfg == nil ||
				tmpConf.MulitCertMsgDefaultCfg == nil ||
				len(tmpConf.ParentGuildList) == 0 {
				return fmt.Errorf("dyconfig LiveCertMsgDefaultCfg, MulitCertMsgDefaultCfg, ParentGuildList empty, read:%s", buffer)
			}

			s.confObj = &tmpConf

			s.confObj.GuildId2SignAnchorLimit = map[uint32]*GuildSignAnchor{}
			for _, info := range s.confObj.GuildSignAnchorLimit {
				s.confObj.GuildId2SignAnchorLimit[info.GuildId] = info
			}

			sort.Sort(GuildLevelInfoSlice(s.confObj.GuildLevel))
			s.confObj.GuildLevel2Limit = map[uint32]uint32{}
			for _, guildLevelInfo := range s.confObj.GuildLevel {
				s.confObj.GuildLevel2Limit[guildLevelInfo.Level] = guildLevelInfo.SignAnchorLimit
			}

			s.confObj.ItemId2CertMsgCfg = map[uint32]*AnchorCertMsgConfig{}
			for _, info := range s.confObj.AnchorCertMsgCfgs {
				s.confObj.ItemId2CertMsgCfg[info.ItemId] = info
			}

			s.confObj.TagId2Name = map[uint32]string{}
			for _, info := range s.confObj.TagInfoList {
				s.confObj.TagId2Name[info.Tagid] = info.TagName
			}

			s.confObj.BindGuildMap = map[uint32]uint32{}
			for _, info := range s.confObj.ParentGuildList {
				s.confObj.BindGuildMap[info.GuildId] = info.ParentGuildId
			}

			s.confObj.MarketId2App = map[uint32]string{}
			for _, info := range s.confObj.AppnameConfig.Vals {
				s.confObj.MarketId2App[info.MarketId] = info.Value
			}

			s.confObj.AnchorExtraCertId2LevelMap = map[uint32]uint32{}
			for _, info := range s.confObj.AnchorExtraCert.List {
				for _, itemId := range info.ItemIDList {
					s.confObj.AnchorExtraCertId2LevelMap[itemId] = info.Level
				}
			}

			log.Infof("dyconfig change")
			log.Infof("dyconfig AutoFollowPublicSwitch %v %v", s.confObj.AutoFollowPublicSwitch, s.confObj.FollowPublicWhiteUids)

			log.Infof("dyconfig AnchorCertSettle Music=%s", s.confObj.AnchorCertSettle.Music)
			log.Infof("dyconfig AnchorCertSettle EmotionStory=%s", s.confObj.AnchorCertSettle.EmotionStory)
			log.Infof("dyconfig AnchorCertSettle TwoDimensions=%s", s.confObj.AnchorCertSettle.TwoDimensions)
			log.Infof("dyconfig AnchorCertSettle %v", s.confObj.AnchorCertAutoSettle)

			log.Infof("dyconfig SignAnchorRiskAccount %+v", s.confObj.SignAnchorRiskAccount)
			log.Infof("dyconfig AutoPassAnchorApplySignReleaseTs %d", s.confObj.AutoPassAnchorApplySignReleaseTs)
			log.Infof("dyconfig AutoPassAnchorApplySignPreTime %d", s.confObj.AutoPassAnchorApplySignPreTime)

			log.Infof("dyconfig GuildSignAnchorLimitDayTh %d", s.confObj.GuildSignAnchorLimitDayTh)

			log.Infof("dyconfig AnchorExtraCert %+v, map=%+v",
				s.confObj.AnchorExtraCert, s.confObj.AnchorExtraCertId2LevelMap)
			log.Infof("dyconfig AnchorExtraCertLocalCacheTTLSec=%d", s.confObj.AnchorExtraCertLocalCacheTTLSec)

			log.Infof("dyconfig AnchorCertConf %+v", s.confObj.AnchorCertConf)

			// CancalContractCondition
			log.Infof("dyconfig CancalContractCondition=%+v", s.confObj.CancalContractCondition)
			log.Infof("dyconfig AutoPassOrRejectMultiPlayerTs=%+v", s.confObj.AutoPassOrRejectMultiPlayerTs)
		}
	} else {
		log.Errorf("genPushConfig read file: %s empty", path)
		return protocol.NewExactServerError(nil, status.ErrKeyNotFound, status.MessageFromCode(status.ErrKeyNotFound))
	}

	return nil
}

func (s *SDyConfigHandler) GetFeishuNotice() string {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()
	return s.confObj.FeishuNotice

}

func (s *SDyConfigHandler) GetGuildLevelList() []*GuildLevelInfo {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()
	return s.confObj.GuildLevel
}

func (s *SDyConfigHandler) GetGuildLevel(guildId uint32) uint32 {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()
	return s.confObj.GuildLevel2Limit[guildId]
}

func (s *SDyConfigHandler) GetGuildSignWhiteLimit(guildId uint32) (uint32, bool) {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()

	nowTts := uint32(time.Now().Unix())
	if info, ok := s.confObj.GuildId2SignAnchorLimit[guildId]; ok &&
		nowTts >= info.BeginTs && nowTts <= info.EndTs {
		return info.Limit, true
	}
	return 0, false
}

func (s *SDyConfigHandler) IsWhileBigAnchor(uid uint32) bool {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()

	for _, v := range s.confObj.WhiteBigAnchor {
		if v == uid {
			return true
		}
	}
	return false
}

func (g GuildSignAnchor) String() string {
	s, _ := json.Marshal(g)
	return string(s)
}

func (s *SDyConfigHandler) GetReleaseTs() uint32 {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()
	return s.confObj.ReleaseTs
}

func (s *SDyConfigHandler) GetCertGrantMsgCfg(itemId uint32, identityType uint32) *AnchorCertMsg {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()

	if cfg, ok := s.confObj.ItemId2CertMsgCfg[itemId]; ok && cfg.IdentityType == identityType && cfg.GrantMsg != nil && len(cfg.GrantMsg.FormatMsg) > 0 {
		return cfg.GrantMsg
	}

	if identityType == uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_RADIO_LIVE) {
		return s.confObj.LiveCertMsgDefaultCfg.GrantMsg
	} else if identityType == uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_MULTIPLAYER) {
		return s.confObj.MulitCertMsgDefaultCfg.GrantMsg
	}

	return nil
}

func (s *SDyConfigHandler) GetCertRecycleMsgCfg(itemId uint32, identityType uint32) *AnchorCertMsg {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()

	if cfg, ok := s.confObj.ItemId2CertMsgCfg[itemId]; ok && cfg.IdentityType == identityType && cfg.RecycleMsg != nil && len(cfg.RecycleMsg.FormatMsg) > 0 {
		return cfg.RecycleMsg
	}

	if identityType == uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_RADIO_LIVE) {
		return s.confObj.LiveCertMsgDefaultCfg.RecycleMsg
	} else if identityType == uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_MULTIPLAYER) {
		return s.confObj.MulitCertMsgDefaultCfg.RecycleMsg
	}

	return nil
}

func (s *SDyConfigHandler) GetCertOverdueMsgCfg(itemId uint32, identityType uint32) *AnchorCertMsg {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()

	if cfg, ok := s.confObj.ItemId2CertMsgCfg[itemId]; ok && cfg.IdentityType == identityType && cfg.OverdueMsg != nil && len(cfg.OverdueMsg.FormatMsg) > 0 {
		return cfg.OverdueMsg
	}

	if identityType == uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_RADIO_LIVE) {
		return s.confObj.LiveCertMsgDefaultCfg.OverdueMsg
	} else if identityType == uint32(pb.ExamineCert_Identity_Type_ExamineCert_Identity_Type_MULTIPLAYER) {
		return s.confObj.MulitCertMsgDefaultCfg.OverdueMsg
	}

	return nil
}

func (s *SDyConfigHandler) GetTagName(tagId uint32) string {
	return s.confObj.TagId2Name[tagId]
}

func (s *SDyConfigHandler) IsParentChildGuild(contractGuildId uint32, eventGuildId uint32) bool {

	if contractGuildId == 0 || eventGuildId == 0 {
		log.Warnf("IsParentChildGuild guild eq 0, contractGuildId:%d, eventGuildId:%d", contractGuildId, eventGuildId)
		return false
	}

	if contractGuildId == eventGuildId {
		return true
	}

	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()

	// case 1: 用户签约的是母公会
	{
		// 判断送礼事件房间是否为对应子公会
		if s.confObj.BindGuildMap[contractGuildId] == 0 && s.confObj.BindGuildMap[eventGuildId] == contractGuildId {
			log.Debugf("IsParentChildGuild case#1, contractGuildId:%d, eventGuildId:%d", contractGuildId, eventGuildId)
			return true
		}
	}

	// case 2: 用户签约的是子公会
	{
		// 1. 送礼事件房间为母公会 判断是否有子母关系
		if s.confObj.BindGuildMap[contractGuildId] > 0 && s.confObj.BindGuildMap[contractGuildId] == eventGuildId {
			log.Debugf("IsParentChildGuild case#2.1, contractGuildId:%d, eventGuildId:%d", contractGuildId, eventGuildId)
			return true
		}

		// 2. 送礼事件房间为子公会 判断是否有共同的母公会
		if s.confObj.BindGuildMap[contractGuildId] > 0 && s.confObj.BindGuildMap[contractGuildId] == s.confObj.BindGuildMap[eventGuildId] {
			log.Debugf("IsParentChildGuild case#2.2, contractGuildId:%d, eventGuildId:%d", contractGuildId, eventGuildId)
			return true
		}
	}

	return false
}

// 获取子母公会关系组
func (s *SDyConfigHandler) GetParentChildGroup(guildId uint32) []uint32 {
	groupList := []uint32{guildId}

	if guildId == 0 {
		log.Debugf("GetParentChildGroup zero guildId:%d", guildId)
		return groupList
	}

	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()

	bindId := s.confObj.BindGuildMap[guildId]
	if bindId > 0 {
		// 子公会，有子母组
		groupList = append(groupList, bindId)
		for k, v := range s.confObj.BindGuildMap {
			if k != guildId && v == bindId {
				groupList = append(groupList, k)
			}
		}
	} else {
		// 需要判断是不是母公会
		for k, v := range s.confObj.BindGuildMap {
			if v == guildId {
				groupList = append(groupList, k)
			}
		}
	}

	log.Debugf("GetParentChildGroup end guildId:%d groupList:%v", guildId, groupList)
	return groupList
}

func (s *SDyConfigHandler) GetParentGuildId(guildId uint32) uint32 {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()

	if v, ok := s.confObj.BindGuildMap[guildId]; ok && v > 0 {
		return v
	}
	return guildId
}

// pkg/protocol/terminal.go
func (s *SDyConfigHandler) GetMultiPlayerCenterUrl(marketId uint32, osType uint8) string {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()

	app := s.confObj.MarketId2App[marketId]
	os := os2Name[protocol.OS(osType)]

	log.Debugf("marketId %d app %v osType %d os %v", marketId, app, osType, os)

	if len(s.confObj.App2Os2Env2CenterUrl[app]) > 0 &&
		len(s.confObj.App2Os2Env2CenterUrl[app][os]) > 0 &&
		len(s.confObj.App2Os2Env2CenterUrl[app][os][ENV]) > 0 {
		return s.confObj.App2Os2Env2CenterUrl[app][os][ENV]
	}

	return s.confObj.App2Os2Env2CenterUrl["ttvoice"]["android"]["prod"]
}

func (s *SDyConfigHandler) GetFollowPublicSwitch(uid uint32) bool {

	if s.confObj.AutoFollowPublicSwitch {
		return true
	}

	for _, u := range s.confObj.FollowPublicWhiteUids {
		if u == uid {
			return true
		}
	}
	return false
}

func (s *SDyConfigHandler) GetAnchorCertSettle() *AnchorCertSettle {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()
	return s.confObj.AnchorCertSettle
}

func (s *SDyConfigHandler) GetAnchorCertAutoSettle() bool {
	return s.confObj.AnchorCertAutoSettle
}

func (s *SDyConfigHandler) GetSignAnchorRiskAccount() SignAnchorRiskAccount {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()
	return s.confObj.SignAnchorRiskAccount
}
func (s *SDyConfigHandler) GetAutoPassAnchorApplySignConf() (autoPassAnchorApplySignReleaseTs, autoPassAnchorApplySignPreTime uint32) {
	return s.confObj.AutoPassAnchorApplySignReleaseTs, s.confObj.AutoPassAnchorApplySignPreTime
}

func (s *SDyConfigHandler) GetAutoPassOrRejectMultiPlayerTs() uint32 {
	if s.confObj.AutoPassOrRejectMultiPlayerTs == 0 {
		return 3600 * 12
	} else {
		return s.confObj.AutoPassOrRejectMultiPlayerTs
	}
}

func (s *SDyConfigHandler) GetGuildSignAnchorLimitDayTh() uint32 {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()
	return s.confObj.GuildSignAnchorLimitDayTh
}

func (s *SDyConfigHandler) GetAnchorExtraCertLocalCacheTTL() time.Duration {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()
	if s.confObj.AnchorExtraCertLocalCacheTTLSec == 0 {
		return time.Duration(300) * time.Second
	}
	return time.Duration(s.confObj.AnchorExtraCertLocalCacheTTLSec) * time.Second
}

func (s *SDyConfigHandler) GetAnchorExtraCertLevel(itemId uint32) uint32 {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()
	return s.confObj.AnchorExtraCertId2LevelMap[itemId]
}

func (s *SDyConfigHandler) GetWarnFeishuUrl() (bool, string) {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()
	return s.confObj.EnablePush, s.confObj.WarnFeishuUrl
}

func (s *SDyConfigHandler) GetAnchorCertConf() AnchorCertConf {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()
	return s.confObj.AnchorCertConf
}

func (s *SDyConfigHandler) GetCancalContractCondition() CancalContractCondition {
	s.confStrLck.RLock()
	defer s.confStrLck.RUnlock()
	return s.confObj.CancalContractCondition
}
