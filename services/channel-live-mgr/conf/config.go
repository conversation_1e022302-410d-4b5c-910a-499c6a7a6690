package conf

import (
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channellivemgr"
	"io/ioutil"
	"math/rand"
	"time"
)

/*string item_id = 1;
string desc = 2; //道具描述
string icon = 3; //道具icon 资源链接
string effect_url = 4; //触发方效果资源链接
string target_effect_url = 5; //对手方效果链接
string msg = 6; //触发方消息
string target_msg = 7; //对手方消息
ItemType ty = 8; //道具类型
uint32 value = 9; //道具数值*/

type ItemConfig struct {
	ItemId          string `json:"item_id"`
	Icon            string `json:"icon"`
	EffectUrl       string `json:"effect_url"`        //触发方效果资源链接
	TargetEffectUrl string `json:"target_effect_url"` //对手方效果链接
	Type            uint32 `json:"type"`
	Value           uint32 `json:"value"`
	GainMsg         string `json:"gain_msg"`       //获得时的文案
	UseMsg          string `json:"use_msg"`        //使用时的文案
	TargetMsg       string `json:"target_msg"`     //对方放消息
	SvrUseMsg       string `json:"svr_use_msg"`    //服务端拼接用
	SvrTargetMsg    string `json:"svr_target_msg"` //服务端拼接用
	Desc            string `json:"desc"`           //道具描述
	Name            string `json:"name"`           //道具名
}

type StateSec struct {
	State uint32 `json:"state"`
	Sec   uint32 `json:"sec"`
}

type Cnd2Dec struct {
	Cnt      uint32 `json:"cnt"`
	DecScore uint32 `json:"dec_score"`
}

type ItemPer struct {
	ItemID  string `json:"item_id"`
	Percent uint32 `json:"percent"`
}

type HitPer struct {
	Price uint32     `json:"price"`
	Items []*ItemPer `json:"items"`
}

type ServiceConfigT struct {
	RedisConfig *config.RedisConfig `json:"redis"`
	//RedisConfig2           *config.RedisConfig `json:"redis2"`
	RedisConfigQueMic      *config.RedisConfig `json:"redis_quemic"`
	RedisFlagConfig        *config.RedisConfig `json:"redis_flag"`
	MysqlConfig            *config.MysqlConfig `json:"mysql"`
	ReadonlyMysqlConfig    *config.MysqlConfig `json:"mysql_readonly"`
	PresentKafkaConfig     *config.KafkaConfig `json:"present_kafka"`
	ChannelKafkaConfig     *config.KafkaConfig `json:"channel_kafka"`
	ChannelMicKafkaConfig  *config.KafkaConfig `json:"channel_mic_kafka"`
	ChannelLiveKafkaConfig *config.KafkaConfig `json:"channel_live_kafka"`
	PkApplyKafkaConfig     *config.KafkaConfig `json:"pk_apply_kafka"`
	KnightKafkaConfig      *config.KafkaConfig `json:"knight_group_kafka"`
	YkwKafkaConfig         *config.KafkaConfig `json:"ykw_kafka"`
	ItemConfigList         []*ItemConfig       `json:"items"`
	StateSecList           []*StateSec         `json:"states"`    //每个阶段对应的时长，秒为单位
	Cnd2Decs               []*Cnd2Dec          `json:"cnt_2_dec"` //氛围道具对应扣除积分
	JoinGroupGiftID        int64               `json:"join_group_gift_id"`
	DecScore               uint32              `json:"dec_score"` //大于500之后扣除的分数
	DecCnt                 uint32              `json:"dec_cnt"`   //
	Hits                   []*HitPer           `json:"hits"`
	Evn                    string              `json:"environment"`
	BanBPGiftTimeRange     []uint32            `json:"ban_backpack_time_range"` //时间段内屏蔽背包礼物
	BanUids                []uint32            `json:"ban_uids"`                //需要屏蔽背包礼物的UID列表
	ExtGameKfkConfig       *config.KafkaConfig `json:"ext-game-kafka"`
}

/*
"hits":[
         {"price":10,"items":[{"item_id":"tomato_1","percent":50},
         {"item_id":"percent_score_10_1","percent":30},
         {"item_id":"percent_score_20_1","percent":0},
         {"item_id":"percent_score_50_1","percent":0}]}
]
*/

var GConf *ServiceConfigT
var Cnt2Dec = make(map[uint32]uint32)
var Sec2State = make(map[uint32]uint32)
var BanUidMap = make(map[uint32]bool)
var BanTimeRange = make([]uint32, 0)

func (sc *ServiceConfigT) Parse(path string) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(path)
	if err != nil {
		return err
	}
	err = json.Unmarshal(data, &sc)

	//log.Errorf("Parse Unmarshal data:%v", data)

	if err != nil {
		log.Errorf("Parse Unmarshal sc:%v", sc)
		return err
	}

	GConf = sc

	for _, v := range sc.Cnd2Decs {
		Cnt2Dec[v.Cnt] = v.DecScore
	}
	for i := 1; i < 32; i++ {
		Cnt2Dec[uint32(i)*sc.DecCnt] = sc.DecScore
	}

	for _, v := range sc.StateSecList {
		log.Debugf("server_config_evn state %d %d", v.State, v.Sec)
		Sec2State[v.State] = v.Sec
	}

	for _, uid := range sc.BanUids {
		BanUidMap[uid] = true
	}
	BanTimeRange = sc.BanBPGiftTimeRange

	log.Debugf("server_config_evn :%v", sc.Evn)

	return nil
}

func GetGConfig() *ServiceConfigT {
	return GConf
}

func (sc *ServiceConfigT) IsProd() bool {
	return sc.Evn == "production"
}

func (sc *ServiceConfigT) GetPresentKafkaConfig() *config.KafkaConfig {
	return sc.PresentKafkaConfig
}

func (sc *ServiceConfigT) GetChannelKafkaConfig() *config.KafkaConfig {
	return sc.ChannelKafkaConfig
}

func (sc *ServiceConfigT) GetChannelMicKafkaConfig() *config.KafkaConfig {
	return sc.ChannelMicKafkaConfig
}

func (sc *ServiceConfigT) GetChannelLiveKafkaConfig() *config.KafkaConfig {
	return sc.ChannelLiveKafkaConfig
}

func (sc *ServiceConfigT) GetPKApplyKafkaConfig() *config.KafkaConfig {
	return sc.PkApplyKafkaConfig
}

func (sc *ServiceConfigT) GetKnightKafkaConfig() *config.KafkaConfig {
	return sc.KnightKafkaConfig
}

func (sc *ServiceConfigT) GetYkwKafkaConfig() *config.KafkaConfig {
	return sc.YkwKafkaConfig
}

func (sc *ServiceConfigT) GetExtGameKfkConfig() *config.KafkaConfig {
	return sc.ExtGameKfkConfig
}

func (sc *ServiceConfigT) GetMysqlConfig() *config.MysqlConfig {
	return sc.MysqlConfig
}

func (sc *ServiceConfigT) GetMysqlConnectionString() string {
	return sc.MysqlConfig.ConnectionString()
}

func (sc *ServiceConfigT) GetReadonlyMysqlConfig() *config.MysqlConfig {
	return sc.ReadonlyMysqlConfig
}

func (sc *ServiceConfigT) GetReadonlyMysqlConnectionString() string {
	return sc.ReadonlyMysqlConfig.ConnectionString()
}

func (sc *ServiceConfigT) GetRedisConfig() *config.RedisConfig {
	return sc.RedisConfig
}

/*
func (sc *ServiceConfigT) GetRedisConfig2() *config.RedisConfig {
	return sc.RedisConfig2
}
*/

func (sc *ServiceConfigT) GetRedisConfigQueueMic() *config.RedisConfig {
	return sc.RedisConfigQueMic
}

func (sc *ServiceConfigT) GetFlagRedis() *config.RedisConfig {
	return sc.RedisFlagConfig
}

func (sc *ServiceConfigT) GetJoinGroupGiftID() uint32 {
	return uint32(sc.JoinGroupGiftID)
}

func (sc *ServiceConfigT) GetApplyTimeOutSec() uint32 {
	return 10
}

func (sc *ServiceConfigT) GetItemConfByType(itemType pb.ItemType) *ItemConfig {
	for _, item := range sc.ItemConfigList {
		if pb.ItemType(item.Type) == itemType {
			return item
		}
	}
	return nil
}

func (sc *ServiceConfigT) GetItemConf(itemID string) *ItemConfig {
	for _, conf := range sc.ItemConfigList {
		if conf.ItemId == itemID {
			return conf
		}
	}
	item := &ItemConfig{}
	return item
}

func (sc *ServiceConfigT) GetDecScore(cnt uint32) uint32 {

	log.Debugf("SendGiftEffectPushMsg GetDecScore Cnt2Dec:%v cnt:%v", Cnt2Dec, cnt)
	if v, ok := Cnt2Dec[cnt]; ok {
		log.Debugf("SendGiftEffectPushMsg GetDecScore Cnt2Dec:%v cnt:%v v:%v", Cnt2Dec, cnt, v)
		return v
	}
	if cnt > sc.DecCnt && (cnt%sc.DecCnt) == 0 {
		return sc.DecScore
	}
	return 0
}

func (sc *ServiceConfigT) GetState(sec int) uint32 {
	var lastSta uint32 = 0

	if len(Sec2State) == 0 {
		return lastSta
	}
	var sumSec uint32 = 0
	for i := 1; i <= len(Sec2State); i++ {
		sumSec = sumSec + Sec2State[uint32(i)]
		if uint32(sec) < sumSec {
			lastSta = uint32(i)
			break
		}
	}

	log.Debugf("GetState %v %v %v", sec, lastSta, Sec2State)

	return lastSta
}

func (sc *ServiceConfigT) GetCnt2Dec() map[uint32]uint32 {
	return Cnt2Dec
}

func (sc *ServiceConfigT) GetHitItemID(price uint32) string {
	items := make([]*ItemPer, 0)
	tmpPrice := -1
	for _, pItem := range sc.Hits {
		if price >= pItem.Price && tmpPrice < int(pItem.Price) {
			items = pItem.Items
			tmpPrice = int(pItem.Price)
		}
	}

	itemID := ""
	tmp := 0
	r := rand.Int() % 100
	for _, item := range items {
		tmp = tmp + int(item.Percent)
		if tmp >= r {
			itemID = item.ItemID
			break
		}
	}

	log.Debugf("GetHitItemID price:%v tmpPrice:%v r:%v itemID:%v", price, tmpPrice, r, itemID)

	return itemID
}

func GetPkTotalSec() int64 {

	totalSec := Sec2State[uint32(pb.EnumChannelLivePKStatus_BEGIN)] + Sec2State[uint32(pb.EnumChannelLivePKStatus_TOOL)] + Sec2State[uint32(pb.EnumChannelLivePKStatus_LAST)]

	return int64(totalSec)
}

type MatchTimeRange struct {
	begin_time uint32 `json:"begin_time"`
	end_time   uint32 `json:"end_time"`
	match_ty   uint32 `json:"match_ty"`
}

func GetMatchAttr() (uint32, float32, float32, float32) {
	return 7, 0.5, 0.3, 0.2 //n场PK积分，权重1，权重2，权重3
}

type Score2LevelName struct {
	Score     uint32 `json:"score"`
	LevelName string `json:"name"`
}

func GetLevelName(score uint32) string {
	score2levelname := []Score2LevelName{
		{
			Score:     250,
			LevelName: "傲娇青铜",
		},
		{
			Score:     750,
			LevelName: "不屈白银",
		},
		{
			Score:     1500,
			LevelName: "璀璨黄金",
		},
		{
			Score:     2500,
			LevelName: "永恒钻石",
		},
		{
			Score:     4500,
			LevelName: "不朽传说",
		},
		{
			Score:     3000000000,
			LevelName: "巅峰王者",
		},
	}
	for i := 0; i < len(score2levelname); i++ {
		if score <= score2levelname[i].Score {
			return score2levelname[i].LevelName
		}
	}
	return "巅峰王者"
}

func IsPkActivityAnchor(uid uint32) bool {
	ts := uint32(time.Now().Unix())
	for i := 0; i < (len(BanTimeRange) - 1); i = i + 2 {
		if BanTimeRange[i] <= ts && ts <= BanTimeRange[i+1] {
			_, ok := BanUidMap[uid]
			if ok {
				return true
			}
		}
	}
	return false
}
