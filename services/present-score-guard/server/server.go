package server

import (
	"context"
	_ "github.com/go-sql-driver/mysql"
	userscore_go "golang.52tt.com/clients/userscore-go"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/deal_token"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	present_score_guard "golang.52tt.com/protocol/services/present-score-guard"
	pb "golang.52tt.com/protocol/services/userscore"
	"golang.52tt.com/services/present-score-guard/conf"
	"golang.52tt.com/services/present-score-guard/event"
	"google.golang.org/grpc"
)

type PresentScoreGuard struct {
	//store *model.Store
	//userscoreClient *userscore.Client
	userscoreGoClient *userscore_go.Client
}

func (l PresentScoreGuard) AddUserScore(ctx context.Context, req *present_score_guard.AddUserScoreReq) (*present_score_guard.AddUserScoreResp, error) {
	res := &present_score_guard.AddUserScoreResp{}
	if len(req.GetDealToken()) == 0 {
		return res, protocol.NewServerError(status.ErrUserScoreInvalidDealtoken)
	}
	//if event.HandleDealToken(req.GetDealToken(), l.store, req.GetTimeValue()) != 0 {
	//	return res, protocol.NewServerError(status.ErrUserScoreInvalidDealtoken)
	//}
	if deal_token.HandleDealToken(req.GetDealToken()) != 0 {
		return res, protocol.NewServerError(status.ErrUserScoreInvalidDealtoken)
	}
	score, serverError := l.userscoreGoClient.AddUserScore(ctx, req.GetUid(), req.GetOpUid(), req.GetAddScore(), uint32(pb.ScoreChangeReason(req.GetChangeReason())), req.GetOrderId(),
		req.GetOrderDesc(), req.GetDealToken(), req.GetExtend(), req.GetTimeValue(), req.GetScoreType())
	if serverError != nil {
		return res, serverError
	}
	res.FinalScore = score
	return res, nil
}

func NewPresentScoreGuard(ctx context.Context, cfg *config.ServerConfig) (*PresentScoreGuard, error) {
	svr := &PresentScoreGuard{}

	//mysqlConfig := new(config.MysqlConfig)
	//mysqlConfig.Read(cfg.Configer, "mysql")
	//
	//mysqlAddr := mysqlConfig.Description()
	//log.Debugf("Initializing mysql connection pool to %s", mysqlAddr)
	//
	//gdb, err := gorm.Open("mysql", mysqlAddr)
	//if err != nil {
	//	return svr, err
	//}
	//
	//if cfg.Configer.String("server::log_level") == "debug" {
	//	gdb = gdb.LogMode(true)
	//}
	//
	//gdb.DB().SetMaxIdleConns(mysqlConfig.MaxIdleConns)
	//gdb.DB().SetMaxOpenConns(mysqlConfig.MaxOpenConns)

	//svr.userscoreClient = userscore.NewClient(grpc.WithBlock(), grpc.WithInsecure())

	svr.userscoreGoClient = userscore_go.NewClient(grpc.WithBlock(), grpc.WithInsecure())
	//svr.store = &model.Store{Db:gdb}
	//
	//err = svr.store.CreateTables()
	//if err != nil {
	//	return svr, err
	//}

	conf.Init()

	go event.WatchPresentEvent(cfg.Configer, svr.userscoreGoClient)
	//go event.WatchWerwolfEvent(cfg.Configer, svr.userscoreClient)

	//go func() {
	//	for {
	//		err := svr.store.AutoMigrateNextMonthPresentScoreDealToken()
	//		if err != nil {
	//			log.Errorln(err)
	//		}
	//		time.Sleep(time.Hour * 24)
	//	}
	//
	//}()

	return svr, nil
}

func (l PresentScoreGuard) ShutDown() {
}
