package server

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	time_present2 "golang.52tt.com/protocol/app/time_present"
	channel_base_api "golang.52tt.com/protocol/services/channel-base-api"
	"golang.52tt.com/protocol/services/time-present"
	"google.golang.org/grpc/codes"
	"time"

	"github.com/globalsign/mgo/bson"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/configserver"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/ugc/friendship"
	"golang.52tt.com/pkg/datacenter"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	grpcex "golang.52tt.com/pkg/protocol/grpc"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/channel"
	coptpb "golang.52tt.com/protocol/app/channel"
	pb "golang.52tt.com/protocol/app/channel-live-logic"
	gaPush "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/common/status"
	channel_live_pk "golang.52tt.com/protocol/services/channel-live-pk"
	channellivepkPB "golang.52tt.com/protocol/services/channel-live-pk"
	"golang.52tt.com/protocol/services/channelim"
	liveFansPb "golang.52tt.com/protocol/services/channellivefans"
	"golang.52tt.com/protocol/services/channellivemgr"
	serPb "golang.52tt.com/protocol/services/channellivemgr"
	clPush "golang.52tt.com/protocol/services/channellivepush"
	channelMicPB "golang.52tt.com/protocol/services/channelmicsvr"
	cpb "golang.52tt.com/protocol/services/channelsvr"
	configPb "golang.52tt.com/protocol/services/configserver"
	"golang.52tt.com/protocol/services/gnobility"
	masked_pk_live "golang.52tt.com/protocol/services/masked-pk-live"
	"golang.52tt.com/protocol/services/nobilitysvr"
	pushPb "golang.52tt.com/protocol/services/push-notification/v2"
	pushPbV3 "golang.52tt.com/protocol/services/push-notification/v3"
	revenueExtGamePb "golang.52tt.com/protocol/services/revenue-ext-game"
	superPlayerPriPb "golang.52tt.com/protocol/services/super-player-privilege"
	"golang.52tt.com/protocol/services/youknowwho"
	"golang.52tt.com/services/channel-live-logic/conf"
)

var gctx context.Context

const (
	PushStraOssBizId = "************"
)

// 开播权限查询
func (s *ChannelLiveLogic_) GetLiveChannelInfo(ctx context.Context, in *pb.GetLiveChannelInfoReq) (*pb.GetLiveChannelInfoResp, error) {
	out := &pb.GetLiveChannelInfoResp{
		ChannelLiveInfo: &pb.LiveChannelInfo{},
	}

	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID

	log.DebugWithCtx(ctx, "GetLiveChannelInfo begin req:%v uid:%d", in, uid)

	resp, err := s.channelLiveMgrCli.GetChannelLiveInfo(ctx, uid, false)
	if err != nil {
		if err.Code() != status.ErrChannelLiveNotAuthority {
			log.ErrorWithCtx(ctx, "GetChannelLiveInfo fail uid:%u err:%v", in.Uid, err)
		}
		return out, nil
	}

	// 查询是否有虚拟主播权限
	virtualResp, err := s.channelLiveMgrCli.CheckHasVirtualAnchorPer(ctx, &serPb.CheckHasVirtualAnchorPerReq{Uid: uid})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveInfo CheckHasVirtualAnchorPer fail uid:%u err:%v", uid, err)
	}

	out.ChannelLiveInfo = &pb.LiveChannelInfo{
		Uid:           resp.ChannelLiveInfo.Uid,
		ChannelId:     resp.ChannelLiveInfo.ChannelId,
		BeginTime:     resp.ChannelLiveInfo.BeginTime,
		EndTime:       resp.ChannelLiveInfo.EndTime,
		CreateTime:    resp.ChannelLiveInfo.CreateTime,
		OperName:      resp.ChannelLiveInfo.OperName,
		PkAuthority:   true,
		HasVirtualPer: virtualResp.GetHasPer(),
	}

	log.DebugWithCtx(ctx, "GetLiveChannelInfo out:%v", out)
	return out, nil
}

// uint32(coptpb.ChannelMsgType_CHANNEL_KICKED_MIC)
func (s *ChannelLiveLogic_) PushReliableChannelMsg(ctx context.Context, msg []byte, content string, cmd uint32, channelId uint32) error {
	commonMsg := &channelim.ChannelCommonMsg{
		FromUid:      10000,
		FromAccount:  "ttyuyinzhushou",
		FromNick:     "",
		Time:         uint64(time.Now().Unix()),
		ToChannelId:  channelId,
		Type:         cmd,
		Content:      content,
		Origin:       0,
		OptContent:   nil,
		PbOptContent: msg,
	}
	cbin, _ := proto.Marshal(commonMsg)

	err := s.chanenlIMCli.SendChannelMsg(ctx, channelId, channelId, 10000, 1, cmd, 0, cbin)

	if err != nil {
		log.ErrorWithCtx(ctx, "PushReliableChannelMsg err:%v channelId:%v", err, channelId)
		return err
	}

	return nil
}

func (s *ChannelLiveLogic_) PushChannelMsg(ctx context.Context, msg []byte, cmd uint32, channelid_list []uint32) error {
	for _, cid := range channelid_list {
		if cid == 0 {
			log.ErrorWithCtx(ctx, "PushChannelMsg is invalid cid cmd:%d channelid_list:%v msg:%s", cmd, channelid_list, string(msg))
			continue
		}

		bMsg := channel.ChannelBroadcastMsg{
			FromUid:      10000,
			FromAccount:  "ttyuyinzhushou",
			FromNick:     "",
			ToChannelId:  cid,
			Type:         cmd,
			Content:      []byte(""),
			Time:         uint64(time.Now().Unix()),
			PbOptContent: msg,
		}

		log.DebugWithCtx(ctx, "PushChannelMsg len msg %d", len(msg))

		bMsgBin, err := bMsg.Marshal()

		if err != nil {
			log.ErrorWithCtx(ctx, "PushChannelMsg bMsg err:%v", err)
		}

		pmsg := &gaPush.PushMessage{
			Cmd:     uint32(gaPush.PushMessage_CHANNEL_MSG_BRO),
			Content: bMsgBin,
			SeqId:   uint32(time.Now().Unix()) + cid,
		}

		pmsgbin, err := pmsg.Marshal()

		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelLiveLogic_ Marshal err:%v", err)
		}

		err = s.pushCli.PushMulticast(ctx, uint64(cid), fmt.Sprintf("%d@channel", cid), []uint32{}, &pushPb.CompositiveNotification{
			Sequence: uint32(time.Now().Unix()) + cid,
			TerminalTypeList: []uint32{
				protocol.MobileAndroidTT,
				protocol.MobileIPhoneTT,
			},
			TerminalTypePolicy: PushNotification.DefaultPolicy,
			AppId:              uint32(protocol.TT),
			ProxyNotification: &pushPb.ProxyNotification{
				Type:       uint32(pushPb.ProxyNotification_PUSH),
				Payload:    pmsgbin,
				Policy:     pushPb.ProxyNotification_DEFAULT,
				ExpireTime: 600,
			},
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "ChannelLiveLogic_ PushChannelMsg fail cmd:%d cid:%d err:%v", cmd, cid, err)
		}

		log.ErrorWithCtx(ctx, "PushChannelMsg success cid:%d cmd:%d channelid_list:%v msg:%s", cid, cmd, channelid_list, string(msg))
	}

	log.DebugWithCtx(ctx, "PushChannelMsg channel_list:%v", channelid_list)
	return nil
}

func (s *ChannelLiveLogic_) SendPushTask(ctx context.Context, title, msg, url, iconUrl string, userList []uint32) error {
	// terminalList := []uint32{protocol.MobileIPhoneTT}
	taskId := fmt.Sprintf("CL-I-%s", bson.NewObjectId().Hex())

	//iconUrl := fmt.Sprintf("https://zy-api.rzhushou.com/face?account=%v", )

	notification := &pushPbV3.Notification{
		Title:     title,
		Content:   msg,
		JumpUrl:   url,
		Extra:     nil,
		Remark:    "",
		Labels:    nil,
		IconUrl:   iconUrl,
		VoiceType: "",
		IconType:  "small",
	}

	opUser := pushPbV3.OptUser_Human

	s.pushCliv3.AddExtParams(notification, pushPbV3.PushType_LiveStart, opUser)

	_, err := s.pushCliv3.SendPushTask(ctx, &pushPbV3.SendPushTaskReq{
		Notification: notification,
		UidList:      userList,
		AppPlatform:  0,
		ReportType:   0,
		TaskId:       taskId,
	})

	//计划下发量oss上报
	for _, uid := range userList {
		newCtx := grpcex.WithServiceInfo(context.Background(), &grpcex.ServiceInfo{
			TerminalType: protocol.PackTerminalType(protocol.MOBILE, protocol.ANDROID, protocol.TT),
		})
		ossCtx, cancelFunc := context.WithTimeout(newCtx, time.Second)
		ossKVMap := make(map[string]interface{})
		ossKVMap["uid"] = uid
		ossKVMap["taskId"] = taskId
		ossKVMap["title"] = title
		ossKVMap["content"] = msg
		ossKVMap["pushType"] = pushPbV3.PushType_LiveStart
		ossKVMap["createTime"] = time.Now().Format("2006-01-02 15:04:05")
		datacenter.StdReportKV(ossCtx, PushStraOssBizId, ossKVMap)
		cancelFunc()
	}

	return err
}
func (s *ChannelLiveLogic_) PushUserMsg(ctx context.Context, msg []byte, cmd uint32, uidList []uint32, ios bool) error {
	pushMessage := &gaPush.PushMessage{
		Cmd:     cmd, // uint32(gaPush.PushMessage_CHANNEL_LIVE_STATUS_PUSH),
		Content: msg,
	}
	pushMessageBytes, _ := proto.Marshal(pushMessage)

	terminalList := []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT, protocol.WindowsTT}
	if !ios {
		terminalList = []uint32{protocol.MobileAndroidTT, protocol.WindowsTT}
	}

	notification := &pushPb.CompositiveNotification{
		Sequence:           uint32(time.Now().Unix()),
		TerminalTypeList:   terminalList,
		TerminalTypePolicy: PushNotification.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &pushPb.ProxyNotification{
			Type:    uint32(pushPb.ProxyNotification_PUSH),
			Payload: pushMessageBytes,
		},
	}

	perr := s.pushCli.PushToUsers(ctx, uidList, notification)

	if perr != nil {
		log.ErrorWithCtx(ctx, "PushUserMsg perr:%v cmd:%v uids:%v", perr, cmd, uidList)
	}

	return perr
}

// 5S一个心跳包
func (s *ChannelLiveLogic_) ChannelLiveHeartbeat(ctx context.Context, in *pb.ChannelLiveHeartbeatReq) (*pb.ChannelLiveHeartbeatResp, error) {
	out := &pb.ChannelLiveHeartbeatResp{}

	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	in.Uid = serviceInfo.UserID

	log.DebugWithCtx(ctx, "ChannelLiveHeartbeatL req:%v", in)

	if in.ChannelLiveId == 0 {
		log.InfoWithCtx(ctx, "ChannelLiveHeartbeatLE channelliveid==0 in:%v", in)
		return out, nil
	}

	req := serPb.ChannelLiveHeartbeatReq{
		Uid:             in.Uid,
		Account:         in.Account,
		Nick:            in.Nick,
		ChannelId:       in.ChannelId,
		ChannelClientId: in.ChannelClientId,
		ChannelLiveId:   in.ChannelLiveId,
		MicId:           in.MicId,
	}

	/*	chInfo, err := s.channelLiveMgrCli.GetChannelLiveInfo(ctx, in.Uid, false)
		if err != nil {
			log.ErrorWithCtx(ctx,"ChannelLiveHeartbeatLE GetChannelLiveInfo err:%v", err)
			return out, err
		}

		// 主播权限回收后不能设置心跳
		if chInfo == nil || chInfo.ChannelLiveInfo == nil || chInfo.ChannelLiveInfo.Uid != in.Uid {
			log.ErrorWithCtx(ctx,"ChannelLiveHeartbeatLE GetChannelLiveInfo not anchorid %v %v", in.Uid, chInfo.ChannelLiveInfo.Uid)
			return out, nil
		}*/

	_, serr := s.channelLiveMgrCli.ChannelLiveHeartbeat(ctx, req)
	if serr != nil {
		log.ErrorWithCtx(ctx, "ChannelLiveHeartbeatLE serr:%v", serr)
		return out, serr
	}

	// 1号麦为直播麦
	if in.MicId == 1 {
		//isLiving := resp.GetChannelStatus().GetStatus() == serPb.EnumChannelLiveStatus_OPEN
		go func() {
			ctx2, cancel := context.WithTimeout(context.Background(), 3*time.Second)
			defer cancel()
			s.missionCli.IncrActorLiveTimeCnt(ctx2, in.GetUid(), in.GetChannelId(), in.GetChannelLiveId(), true)
		}()
	}

	log.DebugWithCtx(ctx, "ChannelLiveHeartbeatL in:%v out:%v serr:%v", in, out, serr)

	return out, nil
}

func (s *ChannelLiveLogic_) GetItemConfig(ctx context.Context, in *pb.GetItemConfigReq) (*pb.GetItemConfigResp, error) {
	out := &pb.GetItemConfigResp{
		ItemConfList: make([]*pb.ItemConfig, 0),
	}

	log.DebugWithCtx(ctx, "GetItemConfig in:%v", in)

	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	opUid := serviceInfo.UserID

	resp, serr := s.channelLiveMgrCli.GetItemConfig(ctx, &serPb.GetItemConfigReq{
		ItemIdList: in.ItemIdList,
		Uid:        opUid,
	})

	if serr != nil {
		log.ErrorWithCtx(ctx, "GetItemConfig serr:%v", serr)
		return out, serr
	}

	for _, item := range resp.ItemConfList {

		ml := make([]*pb.EffectItemMilestone, 0)
		if item.MilestoneList != nil {
			for _, m := range item.MilestoneList {
				ml = append(ml, &pb.EffectItemMilestone{
					Count:   m.Count,
					Percent: m.Percent,
				})
			}
		}

		out.ItemConfList = append(out.ItemConfList, &pb.ItemConfig{
			ItemId:          item.ItemId,
			Desc:            item.Desc,
			Icon:            item.Icon,
			EffectUrl:       item.EffectUrl,
			TargetEffectUrl: item.TargetEffectUrl,
			Msg:             item.Msg,
			TargetMsg:       item.TargetMsg,
			GainMsg:         item.GainMsg,
			Ty:              pb.ItemType(item.Ty),
			Value:           item.Value,
			Name:            item.Name,
			MilestoneList:   ml,
		})
	}

	log.DebugWithCtx(ctx, "GetItemConfig in:%v out:%v", in, out)

	return out, nil
}

func (s *ChannelLiveLogic_) GetMyToolList(ctx context.Context, in *pb.GetMyToolListReq) (*pb.GetMyToolListResp, error) {
	out := &pb.GetMyToolListResp{
		Items: make([]*pb.ToolItem, 0),
	}

	log.DebugWithCtx(ctx, "GetMyToolList in:%v", in)

	uid, sErr := s.ukwCli.GetTrueUidByFake(ctx, in.GetUid())
	if sErr != nil {
		log.ErrorWithCtx(ctx, "GetMyToolList GetTrueUidByFake in:%v serr:%v", in, sErr)
		return out, nil
	}

	resp, sErr := s.channelLiveMgrCli.GetMyToolList(ctx, &serPb.GetMyToolListReq{
		Uid:       uid,
		ChannelId: in.GetChannelId(),
	})
	if sErr != nil {
		log.ErrorWithCtx(ctx, "GetMyToolList in:%v serr:%v", in, sErr)
		return out, nil
	}

	for _, item := range resp.Items {
		out.Items = append(out.Items, &pb.ToolItem{
			ItemId: item.ItemId,
			BeUsed: item.BeUsed,
			ItemNs: item.ItemNs,
		})
	}

	log.DebugWithCtx(ctx, "GetMyToolList in:%v out:%v", in, out)

	return out, nil
}

// 上麦的时候需要发起一次，有变化也需要发起
func (s *ChannelLiveLogic_) ReportClientIDChange(ctx context.Context, in *pb.ReportClientIDChangeReq) (*pb.ReportClientIDChangeResp, error) {
	out := &pb.ReportClientIDChangeResp{}

	log.DebugWithCtx(ctx, "ReportClientIDChange in:%v", in)

	_, serr := s.channelLiveMgrCli.ReportClientIDChange(ctx, &serPb.ReportClientIDChangeReq{
		Uid:                  in.Uid,
		ChannelId:            in.ChannelId,
		MicId:                in.MicId,
		ChannelLiveId:        in.ChannelLiveId,
		ClientId:             in.ClientId,
		ChannelVideoClientId: in.GetChannelVideoClientId(),
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "ReportClientIDChange serr:%v", serr)
		return out, serr
	}

	return out, nil
}

func (s *ChannelLiveLogic_) HandlerHeartBeatTimeOut() {
	log.Debugf("HandlerHeartBeatTimeOut begin")

	// 没有心跳超过1分钟，设置成暂停状态
	resp2, err := s.channelLiveMgrCli.GetHeartBeatTimeOut(gctx, 30, false)

	if err != nil {
		log.Errorf("HandlerHeartBeatTimeOut err:%v", err)
		return
	}

	for _, uid := range resp2.UidList {
		chInfo, err := s.channelLiveMgrCli.GetChannelLiveInfo(gctx, uid, true)
		if err != nil {
			continue
		}

		chStatus, err := s.channelLiveMgrCli.GetChannelLiveStatus(gctx, serPb.GetChannelLiveStatusReq{
			Uid:       chInfo.ChannelLiveInfo.Uid,
			ChannelId: chInfo.ChannelLiveInfo.ChannelId,
		})

		if err != nil {
			continue
		}

		if chStatus == nil || chStatus.ChannelLiveInfo == nil {
			continue
		}

		oStatus := chStatus.ChannelLiveInfo.ChannelLiveStatus

		if pb.EnumChannelLiveStatus(chStatus.ChannelLiveInfo.ChannelLiveStatus.Status) != pb.EnumChannelLiveStatus_OPEN {
			continue
		}

		log.Infof("HandlerHeartBeatTimeOut pause cid:%d uid:%d oStatus:%v", oStatus.ChannelId, oStatus.Uid, oStatus)

		s.SetChannelLiveStatus(gctx, &pb.SetChannelLiveStatusReq{
			Uid:             chInfo.ChannelLiveInfo.Uid,
			Account:         oStatus.Account,
			Nick:            oStatus.Nickname,
			Sex:             oStatus.Sex,
			ChannelId:       oStatus.ChannelId,
			ChannelLiveId:   oStatus.ChannelLiveId,
			ChannelClientId: "",
			Status:          pb.EnumChannelLiveStatus_PAUSE,
		})
	}

	resp, err := s.channelLiveMgrCli.GetHeartBeatTimeOut(gctx, 5*60, true)

	if err != nil {
		log.Errorf("HandlerHeartBeatTimeOut err:%v", err)
		return
	}

	for _, uid := range resp.UidList {
		chInfo, err := s.channelLiveMgrCli.GetChannelLiveInfo(gctx, uid, true)
		if err != nil {
			continue
		}

		chStatus, err := s.channelLiveMgrCli.GetChannelLiveStatus(gctx, serPb.GetChannelLiveStatusReq{
			Uid:       chInfo.ChannelLiveInfo.Uid,
			ChannelId: chInfo.ChannelLiveInfo.ChannelId,
		})

		if err != nil || chStatus == nil || chStatus.ChannelLiveInfo == nil {
			continue
		}

		oStatus := chStatus.ChannelLiveInfo.ChannelLiveStatus

		if oStatus == nil {
			continue
		}

		if pb.EnumChannelLiveStatus(oStatus.Status) == pb.EnumChannelLiveStatus_CLOSE {
			continue
		}

		log.Infof("HandlerHeartBeatTimeOut close cid:%d uid:%d oStatus:%v", oStatus.ChannelId, oStatus.Uid, oStatus)

		s.SetChannelLiveStatus(gctx, &pb.SetChannelLiveStatusReq{
			Uid:             chInfo.ChannelLiveInfo.Uid,
			Account:         oStatus.Account,
			Nick:            oStatus.Nickname,
			Sex:             oStatus.Sex,
			ChannelId:       oStatus.ChannelId,
			ChannelLiveId:   oStatus.ChannelLiveId,
			ChannelClientId: "",
			Status:          pb.EnumChannelLiveStatus_CLOSE,
		})

		s.DisableAllMic(gctx, chInfo.ChannelLiveInfo.Uid, chInfo.ChannelLiveInfo.ChannelId)
	}
}

// 直播结束后，把所有人踢下麦
func (s *ChannelLiveLogic_) DisableAllMic(ctx context.Context, opduid, chanenlId uint32) {
	resp, err := s.channelMicCli.GetMicrList(ctx, chanenlId, opduid)

	if err != nil {
		log.ErrorWithCtx(ctx, "DisableAllMic GetChannelMicMode err:%v", err)
	}

	targetUidList := make([]uint32, 0)
	for _, mic := range resp.AllMicList {
		targetUidList = append(targetUidList, mic.MicUid)
	}

	log.DebugWithCtx(ctx, "DisableAllMic GetChannelMicMode resp:%v", resp)

	//kerr := s.apiCli.KickoutChannelMember(ctx, opduid, targetUidList, chanenlId, 1, 1, "主播下播")
	_, kerr := s.channelBaseApiCli.KickOutChannelMember(ctx, &channel_base_api.KickOutChannelMemberReq{
		ChannelId:      chanenlId,
		OpUid:          opduid,
		BanEnterSecond: 1,
		TargetUidList:  targetUidList,
		KickType:       1,
		KickText:       "主播下播",
		OpSource:       "channel-live-logic",
	})
	if kerr != nil {
		log.ErrorWithCtx(ctx, "DisableAllMic KickoutChannelMember kerr:%v %v", kerr, chanenlId)
	}

	kickResp, err := s.channelMicCli.KickoutChannelMic(ctx, chanenlId, &channelMicPB.KickoutChannelMicReq{
		OpUid:         opduid,
		ChannelId:     chanenlId,
		TargetUidList: targetUidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "DisableAllMic KickoutChannelMic failed id:%d err:%v", chanenlId, err)
	}

	log.DebugWithCtx(ctx, "DisableAllMic KickoutChannelMic kickResp:%v", kickResp)

	allMicList := make([]*coptpb.SimpleMicrSpace, 0)
	for _, mic := range kickResp.KickoutMicList {
		allMicList = append(allMicList, &coptpb.SimpleMicrSpace{
			MicId:    mic.MicId,
			MicState: mic.MicState,
			Uid:      mic.MicUid,
		})
	}

	pushMsg := &coptpb.ChannelMicOpt{
		AllMicList:               allMicList,
		OpMicUid:                 0,
		OpMicFacemd5:             "",
		OpMicid:                  0,
		OpTimeMs:                 0,
		CurrMicmode:              uint32(channel.EChannelMicMode_LIVE_MIC_SPACE_MODE),
		IsGuildChannelPermission: false,
	}

	for _, mic := range allMicList {
		if mic.Uid == 0 {
			continue
		}
		pushMsg.OpMicUid = mic.Uid
		pushMsg.OpMicid = mic.MicId
		pushBin, _ := proto.Marshal(pushMsg)
		serr := s.PushReliableChannelMsg(ctx, pushBin, "结束", uint32(coptpb.ChannelMsgType_CHANNEL_KICKED_MIC), chanenlId)
		if serr != nil {
			log.ErrorWithCtx(ctx, "DisableAllMic PushReliableChannelMsg serr:%v", serr)
		}
		log.DebugWithCtx(ctx, "DisableAllMic PushReliableChannelMsg %v %v", mic.Uid, mic.MicId)
	}
}

func (s *ChannelLiveLogic_) FillChannelRankData(ctx context.Context, in *pb.SetChannelLiveStatusReq, topThreeUser *[]*pb.SendGiftUserInfo) error {
	subCtx, cancel := context.WithTimeout(ctx, time.Millisecond*600)
	defer cancel()

	channelLiveRank, err := s.channelLiveMgrCli.GetChannelLiveRankUser(subCtx, serPb.GetChannelLiveRankUserReq{
		ChannelId: in.ChannelId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "FillChannelRankData GetChannelLiveRankUser in:%v err:%v", in, err)
		return err
	}

	uidList := make([]uint32, 0)
	for _, rank := range channelLiveRank.UserList {
		uidList = append(uidList, rank.Uid)
	}

	userResult, err := s.accountClient.GetUsersMap(subCtx, uidList)
	if nil != err {
		log.ErrorWithCtx(ctx, "FillChannelRankData GetUsersMap in:%v err:%v", in, err)
		return err
	}

	numResult, err := s.numericClient.BatchGetPersonalNumeric(subCtx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "FillChannelRankData BatchGetPersonalNumeric err:%v", err)
	}

	nobResult, err := s.nobilityClient.BatchGetNobilityInfo(subCtx, in.Uid, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "FillChannelRankData BatchGetNobilityInfo err:%v", err)
	}

	// 移除在线榜
	err = s.memberRank.RemoveLiveChannelOnlineRank(subCtx, in.Uid, in.ChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "FillChannelRankData RemoveLiveChannelOnlineRank err:%v", err)
	}

	fansResult, err := s.fansCli.BatchGetFansInfo(subCtx, in.Uid, uidList)
	if nil != err {
		log.ErrorWithCtx(ctx, "FillChannelRankData BatchGetFansInfo err:%v", err)
	}

	for _, rank := range channelLiveRank.UserList {
		var rich, charm uint64 = 0, 0
		var noLv uint32 = 0
		var fansInfo *liveFansPb.FansInfo
		if res, ok := numResult[rank.Uid]; ok {
			rich = res.Rich64 / 1000
			charm = res.Charm64 / 1000
		}

		if res, ok := nobResult[rank.Uid]; ok {
			noLv = res.Level
		}

		if res, ok := fansResult[rank.Uid]; ok {
			fansInfo = res
		}

		if fansInfo == nil {
			continue
		}

		nickname, account := "", ""
		if res, ok := userResult[rank.Uid]; ok {
			nickname = res.Nickname
			account = res.Username
		}

		*topThreeUser = append(*topThreeUser, &pb.SendGiftUserInfo{
			Uid:            rank.Uid,
			Account:        account,
			Score:          rank.Score,
			Rich:           uint32(rich),
			Charm:          uint32(charm),
			Nickname:       nickname,
			NobilityLevel:  noLv,
			GroupFansLevel: fansInfo.GetFansLevel(),
			GroupName:      fansInfo.GetGroupName(),
			PlateInfo:      s.FillPlateConfigMsg(fansInfo.GetPlateInfo()),
		})

		if len(*topThreeUser) == 3 {
			break
		}
	}

	return nil
}

func (s *ChannelLiveLogic_) FillRecommendChannel(ctx context.Context, in *pb.SetChannelLiveStatusReq, recommendChannel *[]*pb.RecommendChannel) error {

	//取排行榜前3推荐
	hour := time.Now().Hour()
	min := time.Now().Minute()

	if min <= 5 {
		hour = hour - 1
		if hour < 0 {
			hour = 23
		}
	}

	subCtx, cancel := context.WithTimeout(ctx, time.Millisecond*800)
	defer cancel()

	hourRank, err := s.memberRank.GetHourRankList(subCtx, in.Uid, 3000, uint32(hour), 0, 150)

	if err != nil || hourRank == nil || hourRank.RankInfoList == nil {
		log.ErrorWithCtx(ctx, "FillRecommendChannel GetHourRankList err:%v", err)
		return err
	}

	channelIdList := make([]uint32, 0)
	for _, channel := range hourRank.RankInfoList {
		channelIdList = append(channelIdList, channel.Cid)
	}

	chStatusSimple, err := s.channelLiveMgrCli.BatchGetChannelLiveStatusSimple(subCtx, serPb.BatchGetChannelLiveStatusSimpleReq{
		ChannelList: channelIdList,
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "FillRecommendChannel BatchGetChannelLiveStatusSimple err:%v", err)
		return err
	}

	onLineStatus := make(map[uint32]uint32)
	for _, sim := range chStatusSimple.ChannelLiveStatusList {
		if sim.Status == 1 {
			onLineStatus[sim.ChannelId] = sim.Status
		}
	}

	channelMapInfo, err := s.channelCli.BatchGetChannelSimpleInfo(subCtx, in.Uid, channelIdList)

	if err != nil {
		log.ErrorWithCtx(ctx, "FillRecommendChannel BatchGetChannelSimpleInfo err:%v", err)
		return err
	}

	ownerUidList := make([]uint32, 0)
	for _, cinfo := range channelMapInfo {
		ownerUidList = append(ownerUidList, *cinfo.CreaterUid)
	}
	userMap, err := s.accountClient.GetUsersMap(subCtx, ownerUidList)
	if nil != err {
		log.ErrorWithCtx(ctx, "FillRecommendChannel GetUsersMap err:%v", err)
		return err
	}

	for _, channel := range hourRank.RankInfoList {
		if _, ok := onLineStatus[channel.Cid]; !ok {
			continue
		}

		if channel.Cid == in.ChannelId {
			continue
		}

		var ownerUid uint32 = 0
		desc, md5 := "", ""
		if res, ok := channelMapInfo[channel.Cid]; ok {
			ownerUid = *res.CreaterUid
			desc = *res.Name
			md5 = *res.IconMd5
		}

		userInfo, ok := userMap[ownerUid]
		if !ok {
			log.ErrorWithCtx(ctx, "FillRecommendChannel userMap fail ownerUid:%v", ownerUid)
			continue
		}

		enterInfo, err := s.recommendCli.GetChannelTagByChannelId(subCtx, in.Uid, channel.Cid)
		var tagId uint32
		if err == nil {
			tagId = *enterInfo.TagInfo.TagId
		} else {
			log.ErrorWithCtx(ctx, "FillRecommendChannel GetChannelTagByChannelId err:%v", err)
			break
		}

		*recommendChannel = append(*recommendChannel, &pb.RecommendChannel{
			Uid:        ownerUid,
			Account:    userInfo.GetUsername(),
			ChannelId:  channel.Cid,
			TagId:      tagId,
			Desc:       desc,
			ChannelMd5: md5,
		})

		if len(*recommendChannel) == 3 {
			break
		}
	}

	return nil
}

// 开播前置检查
func (s *ChannelLiveLogic_) openLivePreCheck(ctx context.Context, uid uint32) protocol.ServerError {
	//神秘人不允许开播
	ukwInfo, err := s.ukwCli.GetUKWInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "openLivePreCheck GetUKWInfo failed uid:%d err:%v", uid, err)
	}

	if ukwInfo.GetUkwPermissionInfo().GetSwitch() == youknowwho.UKWSwitchType_UKW_SWITCH_ON {
		log.InfoWithCtx(ctx, "openLivePreCheck anchor is ukw uid:%d info:%v", uid, ukwInfo)
		return protocol.NewExactServerError(nil, status.ErrChannelLiveYkwOpenLimit)
	}

	// 贵族上线隐身
	gResp, err := s.gnobilityCli.GetNobilitySwitchFlag(ctx, []uint32{uid})
	if nil != err {
		log.ErrorWithCtx(ctx, "openLivePreCheck GetNobilitySwitchFlag failed uid:%d err:%v", uid, err)
	}

	if gResp != nil && len(gResp.SwitchFlagList) == 1 {
		flag := gResp.SwitchFlagList[0]
		if flag&uint32(gnobility.NobilitySwitch_E_ONLINE) > 0 {
			log.InfoWithCtx(ctx, "openLivePreCheck online switch on uid:%d gResp:%v", uid, gResp)
			return protocol.NewExactServerError(nil, status.ErrChannelLiveNotAuthority, "当前是上线隐身状态，需关闭上线隐身才可继续开启听听")
		}
	}

	//svip上线隐身
	sOnlineSwitch, err := s.superPlayPriCli.GetUserOnlineSwitch(ctx, uid)
	if nil != err {
		log.ErrorWithCtx(ctx, "openLivePreCheck GetUserOnlineSwitch failed uid:%d err:%v", uid, err)
	}

	if sOnlineSwitch == superPlayerPriPb.OnlineSwitch_ENUM_ONLINE_SWITCH_STEALTH {
		log.InfoWithCtx(ctx, "openLivePreCheck svip online switch uid:%d", uid)
		return protocol.NewExactServerError(nil, status.ErrChannelLiveNotAuthority, "当前是上线隐身状态，需关闭上线隐身才可继续开启听听")
	}

	log.DebugWithCtx(ctx, "openLivePreCheck end uid:%d", uid)
	return nil
}

func (s *ChannelLiveLogic_) SetChannelLiveStatus(ctx context.Context, in *pb.SetChannelLiveStatusReq) (*pb.SetChannelLiveStatusResp, error) {
	out := &pb.SetChannelLiveStatusResp{}

	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	if serviceInfo.UserID != 0 {
		in.Uid = serviceInfo.UserID
	}

	log.DebugWithCtx(ctx, "SetChannelLiveStatus req:%v userId:%d", in, serviceInfo.UserID)

	chInfo, err := s.channelLiveMgrCli.GetChannelLiveInfo(ctx, in.Uid, false)
	if err != nil || chInfo == nil || chInfo.ChannelLiveInfo == nil {
		log.InfoWithCtx(ctx, "SetChannelLiveStatus GetChannelLiveInfo uid:%v err:%v", in.Uid, err)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveNotAuthority, "没有听听权限")
	}

	if chInfo.ChannelLiveInfo.Uid == 0 && in.Status != pb.EnumChannelLiveStatus_CLOSE {
		log.InfoWithCtx(ctx, "SetChannelLiveStatus not open permission in:%v chInfo:%v", in, chInfo)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveNotAuthority, "没有听听权限")
	}

	if in.GetStatus() == pb.EnumChannelLiveStatus_OPEN && in.GetAnchorType() == uint32(pb.AnchorType_Anchor_Type_Virtual) {
		// 判断是否有虚拟开播权限
		virtualResp, err := s.channelLiveMgrCli.CheckHasVirtualAnchorPer(ctx, &serPb.CheckHasVirtualAnchorPerReq{Uid: in.GetUid()})
		if err != nil {
			log.ErrorWithCtx(ctx, "SetChannelLiveStatus CheckHasVirtualAnchorPer fail uid:%u err:%v", in.GetUid(), err)
			return out, err
		}

		if !virtualResp.GetHasPer() {
			log.ErrorWithCtx(ctx, "SetChannelLiveStatus no virtual per fail uid:%u err:%v", in.GetUid(), err)
			return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "没有虚拟听听权限")
		}
	}

	reqStatus := in.Status
	if reqStatus == pb.EnumChannelLiveStatus_CONTINUE {
		reqStatus = pb.EnumChannelLiveStatus_OPEN
	}

	if reqStatus == pb.EnumChannelLiveStatus_OPEN {
		subCtx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, time.Millisecond*500)
		defer cancel()

		err = s.openLivePreCheck(subCtx, in.GetUid())
		if err != nil {
			log.ErrorWithCtx(ctx, "SetChannelLiveStatus openLivePreCheck no pass in:%v err:%v", in, err)
			return out, err
		}

		_, err = s.channelMicCli.SetChannelMicSpaceStatus(subCtx, in.Uid, in.Uid, in.ChannelId, 1, 1)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetChannelLiveStatus SetChannelMicSpaceStatus failed err:%v uid:%v", err, in.Uid)
		}
	}

	req := serPb.SetChannelLiveStatusReq{
		Uid:             in.Uid,
		Account:         in.Account,
		Nick:            in.Nick,
		Sex:             in.Sex,
		ChannelId:       in.ChannelId,
		ChannelLiveId:   in.ChannelLiveId,
		ChannelClientId: in.ChannelClientId,
		Status:          serPb.EnumChannelLiveStatus(reqStatus),
		OrigStatus:      serPb.EnumChannelLiveStatus(in.Status),
		AnchorType:      in.GetAnchorType(),
		VirtualLiveInfo: &serPb.VirtualLiveInfo{
			ScreenType: in.GetVirtualLiveInfo().GetScreenType(),
		},
	}

	out.Status = in.Status
	out.ChannelLiveId = int64(in.ChannelLiveId)

	//降级
	{
		subCtx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, time.Millisecond*500)
		defer cancel()

		_, followCnt, err := s.friendShipCli.GetUserCounts(subCtx, in.Uid)
		if nil != err {
			log.ErrorWithCtx(ctx, "SetChannelLiveStatus GetUserCounts failed uid:%v err:%v", in.Uid, err)
		}
		req.FansCnt = followCnt

		groupFansInfo, err := s.fansCli.GetAnchorFansInfo(subCtx, in.Uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetChannelLiveStatus GetAnchorFansInfo failed uid:%v err:%v", in.Uid, err)
		}

		req.GroupFansCnt = groupFansInfo.GetFansCnt()
	}

	recommendChannel := make([]*pb.RecommendChannel, 0)
	topThreeUser := make([]*pb.SendGiftUserInfo, 0)
	channelLiveData := &pb.ChannelLiveData{}

	if in.Status == pb.EnumChannelLiveStatus_CLOSE {
		s.FillChannelRankData(ctx, in, &topThreeUser)
		s.FillRecommendChannel(ctx, in, &recommendChannel)

		liveDataResp, err := s.channelLiveMgrCli.GetChannelLiveData(ctx, serPb.GetChannelLiveDataReq{
			Uid: in.Uid,
		})

		if err != nil {
			log.ErrorWithCtx(ctx, "SetChannelLiveStatus GetChannelLiveData failed in:%v err:%v", in, err)
			return out, err
		}

		channelLiveData.AudienceCnt = liveDataResp.LiveData.AudienceCnt
		// 换成房间内送给主播的礼物值
		channelLiveData.GiftValue = liveDataResp.GetLiveData().GetLiveGiftValue()
		channelLiveData.AnchorGiftValue = liveDataResp.GetLiveData().GetAnchorGiftValue() + liveDataResp.GetLiveData().GetKnightValue() + liveDataResp.GetLiveData().GetGameFee()
		channelLiveData.SendGiftAudienceCnt = liveDataResp.LiveData.SendGiftAudienceCnt
	}

	accountResp, err := s.accountClient.GetUser(ctx, in.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveStatus GetUser failed in:%v err:%v", in, err)
	}

	req.Account = accountResp.GetUsername()
	req.Nick = accountResp.GetNickname()
	req.Sex = uint32(accountResp.GetSex())

	resp, err := s.channelLiveMgrCli.SetChannelLiveStatus(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveStatus SetChannelLiveStatus failed err:%v in:%v", err, in)
		return out, err
	}

	channelLiveData.LiveTime = uint32(time.Now().Unix()) - resp.ChannelLiveStatus.BeginTime
	channelLiveData.AddGroupFans = req.GroupFansCnt - uint32(resp.ChannelLiveStatus.GroupFansCnt)
	channelLiveData.AddFans = req.FansCnt - uint32(resp.ChannelLiveStatus.FansCnt)

	out.Status = in.Status
	out.ChannelLiveId = int64(resp.ChannelLiveStatus.ChannelLiveId)

	chLiveStatusPush := &pb.ChannelLiveStatusPushMsg{
		ChannelLiveInfo: &pb.ChannelLiveInfo{
			ChannelLiveStatus: &pb.ChannelLiveStatus{
				Uid:           in.Uid,
				Account:       in.Account,
				Nickname:      in.Nick,
				Sex:           in.Sex,
				ChannelId:     in.ChannelId,
				ChannelLiveId: uint64(out.ChannelLiveId),
				MicList:       nil,
				Status:        in.Status,
				BeginTime:     resp.ChannelLiveStatus.BeginTime,
				PkMatchState:  pb.EnumPkMatch(resp.ChannelLiveStatus.PkMatchState),
				AnchorType:    resp.GetChannelLiveStatus().GetAnchorType(),
				VirtualLiveInfo: &pb.VirtualLiveInfo{
					ScreenType: resp.GetChannelLiveStatus().GetVirtualLiveInfo().GetScreenType(),
				},
			},
			OtherChannelLiveStatus: nil,
			IsChallenge:            false,
		},
		ChannelList:  recommendChannel,
		TopThreeUser: topThreeUser,
		LiveData:     channelLiveData,
	}

	log.DebugWithCtx(ctx, "SetChannelLiveStatus uid:%d nowTs:%d beginTs:%d chLiveStatusPush:%v", in.GetUid(), time.Now().Unix(), resp.ChannelLiveStatus.BeginTime, chLiveStatusPush)

	subCtx, cancel := protogrpc.NewContextWithInfoTimeout(ctx, time.Millisecond*500)
	defer cancel()

	chLiveStatusPushBin, mErr := proto.Marshal(chLiveStatusPush)
	if mErr != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveStatus Marshal failed in:%v err:%v", in, mErr)
	} else {
		err := s.PushChannelMsg(subCtx, chLiveStatusPushBin, uint32(channel.ChannelMsgType_CHANNEL_LIVE_STATUS_MSG), []uint32{in.ChannelId})
		if err != nil {
			log.ErrorWithCtx(ctx, "SetChannelLiveStatus PushChannelMsg failed err:%v in:%v", err, in)
		}
	}

	// 如果不是开始就不要推送了
	if serPb.EnumChannelLiveStatus(in.Status) != serPb.EnumChannelLiveStatus_OPEN {
		log.DebugWithCtx(ctx, "SetChannelLiveStatus in:%v out:%v resp:%v", in, out, resp)
		return out, nil
	}

	micSpace := &pb.PkMicSpace{
		MicId:           1,
		Uid:             in.Uid,
		Account:         in.Account,
		Nick:            in.Nick,
		ChannelClientId: in.ChannelClientId,
		Sex:             int32(in.Sex),
	}

	micList := make([]*pb.PkMicSpace, 0, 1)
	micList = append(micList, micSpace)

	chSimpleInfo, err := s.channelCli.GetChannelSimpleInfo(subCtx, in.Uid, in.ChannelId)
	if nil != err {
		log.ErrorWithCtx(ctx, "SetChannelLiveStatus GetChannelSimpleInfo failed in:%v err:%v", in, err)
		return out, nil
	}

	//response 中返回需要的数据
	livePushMsg := &pb.LiveStatusPushMsg{
		Uid:           in.Uid,
		Account:       in.Account,
		ChannelId:     in.ChannelId,
		ChannelLiveId: resp.ChannelLiveStatus.ChannelLiveId,
		Status:        in.Status,
		MicList:       micList,
		Nick:          in.Nick,
		Desc:          chSimpleInfo.GetName(),
	}

	livePushMsgBin, _ := proto.Marshal(livePushMsg)

	go func() {
		s.PushAnchorLiveBegin(in, chSimpleInfo, livePushMsgBin)
		s.AuditAnchorLiveBegin(accountResp, chSimpleInfo, serviceInfo)
	}()

	log.DebugWithCtx(ctx, "SetChannelLiveStatus end in:%v out:%v resp:%v", in, out, resp)
	return out, nil
}

func (s *ChannelLiveLogic_) PushAnchorLiveBeginSub(in *pb.SetChannelLiveStatusReq, chInfo *cpb.ChannelSimpleInfo, livePushMsgBin []byte, fUids []uint32) error {
	ctx := context.Background()

	getConfigReq := configPb.GetUsersConfigReq{
		ConfigKey: string(configserver.LiveStart),
		Uids:      fUids,
	}
	getConfigRsp, err := s.configserverClient.GetUsersConfig(ctx, getConfigReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushAnchorLiveBeginSub GetUsersConfig err:%v in:%v", err, getConfigReq)
		return err
	}
	var validUids []uint32
	filter := make(map[uint32]string)
	for _, configInfo := range getConfigRsp.GetConfigInfo() {
		filter[configInfo.Uid] = configInfo.Value
	}
	for _, uid := range fUids {
		if res, ok := filter[uid]; ok {
			if res == "true" {
				validUids = append(validUids, uid)
			}
		} else {
			validUids = append(validUids, uid)
		}
	}

	//主播开播频率限制
	pResp, err := s.channelLiveMgrCli.GetUserPushCnt(ctx, &serPb.GetUserPushCntReq{
		Uid:       in.Uid,
		AnchorUid: in.Uid,
		UidList:   validUids,
		LimitCnt:  2,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "PushAnchorLiveBeginSub GetUserPushCnt err:%v uid:%v", err, in.Uid)
		return err
	}

	validUids = pResp.ValidUidList

	log.InfoWithCtx(ctx, "PushAnchorLiveBeginSub uid:%d ValidUidList:%v fUids:%v getConfigRsp:%v", in.Uid, pResp.ValidUidList, fUids, getConfigRsp)

	if len(validUids) == 0 {
		return nil
	}

	serr := s.PushUserMsg(ctx, livePushMsgBin, uint32(gaPush.PushMessage_CHANNEL_LIVE_STATUS_PUSH), validUids, false)
	if serr != nil {
		log.ErrorWithCtx(ctx, "PushAnchorLiveBeginSub PushUserMsg fail serr:%v in:%v", serr, in)
	}

	iosMsg := fmt.Sprintf("你关注的【%v】正在开播~", in.Nick) //主标题
	url := fmt.Sprintf("tt://m.52tt.com/channel?channel_id=%v&channel_type=%v&channel_enter_source=40", in.ChannelId, 7)
	iconUrl := fmt.Sprintf(conf.HeadIconUrl, in.Account)
	serr = s.SendPushTask(ctx, iosMsg, chInfo.GetName(), url, iconUrl, validUids) //chInfo.GetName() 副标题
	if serr != nil {
		log.ErrorWithCtx(ctx, "PushAnchorLiveBeginSub SendPushTask fail serr:%v in:%v", serr, in)
	}

	log.DebugWithCtx(ctx, "PushAnchorLiveBeginSub end in:%v url:%v account:%v iconUrl:%v", in, conf.HeadIconUrl, in.Account, iconUrl)
	return nil
}

// 主播开播推送
func (s *ChannelLiveLogic_) PushAnchorLiveBegin(in *pb.SetChannelLiveStatusReq, chInfo *cpb.ChannelSimpleInfo, livePushMsgBin []byte) error {
	ctx := context.Background()

	lm := friendship.NewLoadMoreByID(friendship.SortDescending, "")

	for {
		fUids := make([]uint32, 0)

		fls, tmpLm, err := s.friendShipCli.GetUserFollowerList(ctx, in.Uid, false, lm, 2000)
		if err != nil {
			log.ErrorWithCtx(ctx, "PushAnchorLiveBegin GetUserFollowerList in:%v err:%v", err)
			break
		}

		for _, fl := range fls {
			if fl.FromUid%2 == 0 { //AB测试，== 0 推送奇数
				continue
			}
			fUids = append(fUids, fl.FromUid)
		}

		if len(fUids) > 0 {
			perr := s.PushAnchorLiveBeginSub(in, chInfo, livePushMsgBin, fUids)
			if perr != nil {
				log.ErrorWithCtx(ctx, "PushAnchorLiveBegin PushAnchorLiveBeginSub in:%v err:%v", in, perr)
			}
		}

		if tmpLm == nil {
			break
		}
		lm = tmpLm
	}

	return nil
}

func checkClientVersion(ctx context.Context) bool {
	serviceInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return true
	}

	log.InfoWithCtx(ctx, "checkClientVersion serviceInfo %+v %+v %v", serviceInfo, conf.VersionLimit, serviceInfo.ClientVersion)

	if serviceInfo.ClientVersion < conf.VersionLimit[serviceInfo.ClientType] {
		log.ErrorWithCtx(ctx, "checkClientVersion fail serviceInfo %+v %+v %v %v", serviceInfo, conf.VersionLimit, serviceInfo.ClientVersion, serviceInfo.ClientType)
		return false
	}

	return true
}

func (s *ChannelLiveLogic_) GetChannelLiveStatus(ctx context.Context, in *pb.GetChannelLiveStatusReq) (*pb.GetChannelLiveStatusResp, error) {
	out := &pb.GetChannelLiveStatusResp{}
	si, _ := protogrpc.ServiceInfoFromContext(ctx)
	log.DebugWithCtx(ctx, "GetChannelLiveStatus req:%v", in)

	req := serPb.GetChannelLiveStatusReq{
		ChannelId: in.ChannelId,
		Uid:       in.Uid,
	}
	resp, serr := s.channelLiveMgrCli.GetChannelLiveStatus(ctx, req)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveStatus fail serr:%v", serr)
		return out, serr
	}

	channelLiveInfo := resp.ChannelLiveInfo

	if channelLiveInfo == nil || channelLiveInfo.ChannelLiveStatus == nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveStatus resp info invalid serr:%v", serr)
		return out, errors.New("resp info invalid")
	}

	pkChannelList := make([]uint32, 0)
	if resp.GetChannelLiveInfo().GetChannelLiveStatus().GetPkChannelId() != 0 {
		pkChannelList = append(pkChannelList, resp.GetChannelLiveInfo().GetChannelLiveStatus().GetPkChannelId())
	}

	micList := make([]*pb.PkMicSpace, 0)
	for _, mic := range channelLiveInfo.ChannelLiveStatus.ChannelMicList {
		micList = append(micList, &pb.PkMicSpace{
			MicId:                mic.MicId,
			Uid:                  mic.Uid,
			Account:              mic.Account,
			Nick:                 mic.Nick,
			ChannelClientId:      mic.VoiceId,
			ChannelVideoClientId: mic.GetChannelVideoClientId(),
		})
	}

	pkStatus := pb.EnumChannelLivePKStatus(channelLiveInfo.ChannelLiveStatus.PkStatus)

	out.ChannelLiveInfo = &pb.ChannelLiveInfo{
		ChannelLiveStatus: &pb.ChannelLiveStatus{
			Uid:           channelLiveInfo.ChannelLiveStatus.Uid,
			Account:       channelLiveInfo.ChannelLiveStatus.Account,
			Nickname:      channelLiveInfo.ChannelLiveStatus.Nickname,
			Sex:           channelLiveInfo.ChannelLiveStatus.Sex,
			ChannelId:     channelLiveInfo.ChannelLiveStatus.ChannelId,
			ChannelLiveId: channelLiveInfo.ChannelLiveStatus.ChannelLiveId,
			MicList:       micList, // resp.ChannelLiveInfo.ChannelLiveStatus.ChannelMicList
			Status:        pb.EnumChannelLiveStatus(channelLiveInfo.ChannelLiveStatus.Status),
			PkStatus:      pkStatus,
			BeginTime:     channelLiveInfo.ChannelLiveStatus.BeginTime,
			PkMatchState:  pb.EnumPkMatch(channelLiveInfo.ChannelLiveStatus.PkMatchState),
			AnchorType:    channelLiveInfo.GetChannelLiveStatus().GetAnchorType(),
			VirtualLiveInfo: &pb.VirtualLiveInfo{
				ScreenType: channelLiveInfo.GetChannelLiveStatus().GetVirtualLiveInfo().GetScreenType(),
			},
		},
		IsChallenge: in.ChannelId == channelLiveInfo.ChannelLiveStatus.ChannelId,
	}

	if channelLiveInfo.PkChannelLiveStatus != nil {
		micList2 := make([]*pb.PkMicSpace, 0)
		for _, mic := range channelLiveInfo.PkChannelLiveStatus.ChannelMicList {
			micList2 = append(micList2, &pb.PkMicSpace{
				MicId:                mic.MicId,
				Uid:                  mic.Uid,
				Account:              mic.Account,
				Nick:                 mic.Nick,
				ChannelClientId:      mic.VoiceId,
				ChannelVideoClientId: mic.GetChannelVideoClientId(),
			})
		}

		out.ChannelLiveInfo.OtherChannelLiveStatus = &pb.ChannelLiveStatus{
			Uid:           channelLiveInfo.PkChannelLiveStatus.Uid,
			Account:       channelLiveInfo.PkChannelLiveStatus.Account,
			Nickname:      channelLiveInfo.PkChannelLiveStatus.Nickname,
			Sex:           channelLiveInfo.PkChannelLiveStatus.Sex,
			ChannelId:     channelLiveInfo.PkChannelLiveStatus.ChannelId,
			ChannelLiveId: channelLiveInfo.PkChannelLiveStatus.ChannelLiveId,
			MicList:       micList2,
			Status:        pb.EnumChannelLiveStatus(channelLiveInfo.PkChannelLiveStatus.Status),
			PkStatus:      pb.EnumChannelLivePKStatus(channelLiveInfo.PkChannelLiveStatus.Status),
			BeginTime:     channelLiveInfo.PkChannelLiveStatus.BeginTime,
			AnchorType:    channelLiveInfo.GetPkChannelLiveStatus().GetAnchorType(),
			VirtualLiveInfo: &pb.VirtualLiveInfo{
				ScreenType: channelLiveInfo.GetPkChannelLiveStatus().GetVirtualLiveInfo().GetScreenType(),
			},
		}
	}

	if channelLiveInfo.PkCommonInfo != nil {
		out.Status = pb.EnumChannelLivePKStatus(channelLiveInfo.PkCommonInfo.PkStatus)
		out.FinishTime = channelLiveInfo.PkCommonInfo.FinishTime // 当前PK阶段结束时间
	}

	// 先查多人pk状态，如果在多人pk把 单人pk字段置空
	multiPkResp, err := s.channelLivePkCli.GetMultiPkInfo(ctx, &channel_live_pk.GetMultiPkInfoReq{ChannelId: in.ChannelId})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveStatus GetMultiPkInfo fail %v in=%+v", err, in)
		return out, err
	}
	log.InfoWithCtx(ctx, "GetChannelLiveStatus uid=%d %s cid=%d GetMultiPkInfo=%s",
		si.UserID, si.String(), in.ChannelId, multiPkResp.String())
	if multiPkResp.GetPkInfo().GetPkId() > 0 {
		multiPkInfo := multiPkResp.GetPkInfo()
		out.MultiPkInfo = &pb.MultiPkInfo{
			PkId:          multiPkInfo.GetPkId(),
			PkType:        multiPkInfo.GetPkType(),
			PkStatus:      multiPkInfo.GetPkStatus(),
			PkEndTs:       multiPkInfo.GetPkEndTs(),
			PkStatusEndTs: multiPkInfo.GetPkStatusEndTs(),
			IsAutoPkType:  multiPkInfo.GetIsAutoPkType(),
			PkTeamType:    multiPkInfo.GetPkTeamType(),
			PushTs:        multiPkInfo.GetGetTs(),
		}
		// 如果是旧版的ios客户端、版本号低于6.38（*********）并且pk阶段是互动阶段之后的，则PkStatus设置为0
		if si.ClientType == protocol.ClientTypeIOS && multiPkInfo.GetPkStatus() > uint32(pb.EnumMultiPkStatus_Multi_Pk_Interact) && si.ClientVersion < ********* {
			out.MultiPkInfo.PkStatus = 0
		}

		uids, anchorUids := getMultiPkInfoUids(multiPkResp)
		log.DebugWithCtx(ctx, "GetChannelLiveStatus anchorUids=%+v uids=%v", anchorUids, uids)

		userMap, err := s.accountClient.GetUsersMap(ctx, uids)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelLiveStatus GetUsersMap fail %v in=%+v", err, in)
			return out, err
		}

		liveStatsResp, err := s.channelLiveMgrCli.BatchGetChannelLiveStatus(ctx, channellivemgr.BatchGetChannelLiveStatusReq{
			UidList: anchorUids})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelLiveStatus BatchGetChannelLiveStatus anchorUid=%v, err:%v", anchorUids, err)
			return out, err
		}
		uid2LiveStatus := map[uint32]*channellivemgr.ChannelLiveStatus{}
		for _, info := range liveStatsResp.GetChannelLiveInfoList() {
			uid2LiveStatus[info.GetChannelLiveStatus().GetUid()] = info.GetChannelLiveStatus()
		}
		log.InfoWithCtx(ctx, "GetChannelLiveStatus BatchGetChannelLiveStatus anchorUids=%v liveStatsResp=%s",
			anchorUids, liveStatsResp.String())

		if multiPkInfo.GetFirstKillUser().GetUid() > 0 {
			firstKillUserInfo := multiPkInfo.GetFirstKillUser()
			firstKillUid := firstKillUserInfo.GetUid()
			userInfo := userMap[firstKillUid]

			out.MultiPkInfo.FirstKillUser = &pb.MultiPkFirstKillUser{
				UserProfile: genUserProfile(userInfo, firstKillUserInfo.GetUkwInfo()),
				ChannelId:   firstKillUserInfo.GetChannelId(),
			}
		}
		for _, info := range multiPkInfo.GetRoomInfoList() {
			if info.GetChannelId() != in.GetChannelId() {
				pkChannelList = append(pkChannelList, info.GetChannelId())
			}
			uid := info.GetAnchorInfo().GetUid()
			userInfo := userMap[uid]
			teamInfo := &pb.MultiPkRoomInfo{
				TeamId: info.TeamId,
				AnchorInfo: &pb.MultiPkAnchorInfo{
					UserProfile: genUserProfile(userInfo, nil),
					LiveStatus:  uint32(uid2LiveStatus[uid].GetStatus()),
				},
				PkScore:   info.GetPkScore(),
				PkRank:    info.GetPkRank(),
				ChanenlId: info.GetChannelId(),
				IsSponsor: info.GetIsSponsor(),
				PkResult:  info.GetPkResult(),
			}
			for _, user := range info.GetTopUserList() {
				userInfo := userMap[user.Uid]
				teamInfo.TopUserList = append(teamInfo.TopUserList, &pb.MultiPkUserInfo{
					UserProfile: genUserProfile(userInfo, user.GetUkwInfo()),
				})
			}
			for _, user := range info.GetKnightList() {
				userInfo := userMap[user.Uid]
				teamInfo.KnightList = append(teamInfo.KnightList, &pb.MultiPkUserInfo{
					UserProfile: genUserProfile(userInfo, nil),
					KnightLevel: user.GetKnightLevel(),
				})
			}
			out.MultiPkInfo.RoomInfoList = append(out.MultiPkInfo.RoomInfoList, teamInfo)
		}

		for _, info := range multiPkResp.GetKnightList() {
			knightInfo := &pb.MultiPkKnightInfo{
				AnchorUid: info.AnchorUid,
				ChannelId: info.ChannelId,
			}
			for _, knight := range info.GetKnightList() {
				userInfo := userMap[knight.GetUid()]
				knightInfo.KnightList = append(knightInfo.KnightList, &pb.MultiPkUserInfo{
					UserProfile: genUserProfile(userInfo, nil),
					KnightLevel: knight.GetKnightLevel(),
				})
			}
			out.MultiPkKnightList = append(out.MultiPkKnightList, knightInfo)
		}

		out.Status = 0
		log.InfoWithCtx(ctx, "GetChannelLiveStatus multi_pk uid=%d %s out=%s", si.UserID, si.String(), utils.ToJson(out))
		//return out, nil
	}
	if multiPkResp.GetPkMatchInfo().GetMatchStatus() > 0 {
		matchInfo := multiPkResp.GetPkMatchInfo()

		userMap, err := s.accountClient.GetUsersMap(ctx, multiPkResp.GetPkMatchInfo().GetUidList())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelLiveStatus GetUsersMap fail %v in=%+v", err, in)
			return out, err
		}

		out.MultiPkMatchInfo = &pb.MultiPkMatchInfo{
			MatchStatus: matchInfo.GetMatchStatus(),
			SponsorUid:  matchInfo.GetSponsorUid(),
		}
		for _, uid := range multiPkResp.GetPkMatchInfo().GetUidList() {
			out.MultiPkMatchInfo.UserList = append(out.MultiPkMatchInfo.UserList, genUserProfile(userMap[uid], nil))
		}

		out.Status = 0
		log.InfoWithCtx(ctx, "GetChannelLiveStatus multi_pk_match uid=%d %s out=%s", si.UserID, si.String(), utils.ToJson(out))
	}
	// 组装结算信息
	if len(multiPkResp.GetSettleInfo()) > 0 {
		log.DebugWithCtx(ctx, "GetChannelLiveStatus multi_pk_settle cid: %d settle_info: %s", req.GetChannelId(), string(multiPkResp.GetSettleInfo()))
		out.MultiPkSettleInfo = &pb.MultiPkSettleInfo{}
		if err := json.Unmarshal(multiPkResp.GetSettleInfo(), out.MultiPkSettleInfo); err != nil {
			log.ErrorWithCtx(ctx, "GetChannelLiveStatus multi_pk_settle Unmarshal result fail %v in=%+v", err, in)
		}
		log.DebugWithCtx(ctx, "GetChannelLiveStatus multi_pk_settle cid: %d settle_info: %v", req.GetChannelId(), out.MultiPkSettleInfo)
	}

	out.ChannelLiveInfo.ChannelLiveStatus.TimePresentInfo = s.GetLiveIntimatePresentInfo(ctx, in.GetChannelId(), pkChannelList)
	if out.GetMultiPkInfo() != nil {
		out.GetMultiPkInfo().TimePresentInfo = out.ChannelLiveInfo.ChannelLiveStatus.TimePresentInfo
	}

	log.DebugWithCtx(ctx, "GetChannelLiveStatus in:%v out:%v resp:%v serr:%v", in, out, resp, serr)
	return out, nil
}

func (s *ChannelLiveLogic_) relayCheck(ctx context.Context, fromId, toId uint32) error {
	var ids []uint32
	if toId == 0 {
		ids = []uint32{fromId}
	} else {
		ids = []uint32{fromId, toId}
	}

	relayBys, serr := s.officialLiveChannelCli.BatchGetRelayBySchedule(ctx, ids)
	if serr != nil {
		log.ErrorWithCtx(ctx, "BatchGetRelayBySchedule err:%v %v & %v", serr, fromId, toId)
		return serr
	}
	at := time.Now()
	for id, relayBy := range relayBys {
		if relayBy.StartTime < at.Unix() || relayBy.StartTime < at.Unix()+600 {
			var msg string
			if id == fromId {
				if relayBy.RelayStatus == 0 {
					msg = "即将进行官频直播，暂不可PK"
				} else {
					msg = "正在进行官频直播，暂不可PK"
				}
			} else {
				if relayBy.RelayStatus == 0 {
					msg = "对方即将进行官频直播，暂不可PK"
				} else {
					msg = "对方正在进行官频直播，暂不可PK"
				}
			}
			return protocol.NewServerError(status.ErrChannelLiveNotPkAuth, msg)
		}
	}
	return nil
}

func (s *ChannelLiveLogic_) checkIfInMaskedPk(ctx context.Context, opUid, channelId, targetChannelId uint32) error {
	pkStatus, err := s.maskedPkLiveCli.GetLiveChannelMaskedPKStatus(ctx, opUid, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkIfInMaskedPk fail to GetLiveChannelMaskedPKStatus err:%v %v & %v", err, channelId)
		return err
	}

	log.DebugWithCtx(ctx, "checkIfInMaskedPk begin opUid:%d channelId:%d targetChannelId:%d pkStatus:%d", opUid, channelId, targetChannelId, pkStatus)

	// 匹配中或者pk中
	if pkStatus == uint32(masked_pk_live.ChannelMaskedPKStatus_PreMatching) ||
		pkStatus == uint32(masked_pk_live.ChannelMaskedPKStatus_AutoInMatching) ||
		pkStatus == uint32(masked_pk_live.ChannelMaskedPKStatus_ActiveInMatching) ||
		//pkStatus == uint32(masked_pk_live.ChannelMaskedPKStatus_NotMatching) ||
		pkStatus == uint32(masked_pk_live.ChannelMaskedPKStatus_InPKing) {
		return protocol.NewServerError(status.ErrChannelLivePkIng, "正在蒙面pk")
	}

	if targetChannelId == 0 {
		return nil
	}

	pkStatus, err = s.maskedPkLiveCli.GetLiveChannelMaskedPKStatus(ctx, opUid, targetChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkIfInMaskedPk fail to GetLiveChannelMaskedPKStatus err:%v %v & %v", err, targetChannelId)
		return err
	}

	log.DebugWithCtx(ctx, "checkIfInMaskedPk begin opUid:%d channelId:%d targetChannelId:%d pkStatus:%d", opUid, channelId, targetChannelId, pkStatus)

	// 匹配中或者pk中
	if pkStatus == uint32(masked_pk_live.ChannelMaskedPKStatus_PreMatching) ||
		pkStatus == uint32(masked_pk_live.ChannelMaskedPKStatus_AutoInMatching) ||
		pkStatus == uint32(masked_pk_live.ChannelMaskedPKStatus_ActiveInMatching) ||
		//pkStatus == uint32(masked_pk_live.ChannelMaskedPKStatus_NotMatching) ||
		pkStatus == uint32(masked_pk_live.ChannelMaskedPKStatus_InPKing) {
		return protocol.NewServerError(status.ErrChannelLivePkIng, "对方暂不方便接受pk")
	}

	return nil
}

// 发起PK申请
func (s *ChannelLiveLogic_) ApplyPk(ctx context.Context, in *pb.ApplyPkReq) (out *pb.ApplyPkResp, err error) {
	si, _ := protogrpc.ServiceInfoFromContext(ctx)
	out = &pb.ApplyPkResp{
		TargetUid:       in.TargetUid,
		TargetChannelId: in.TargetChannelId,
	}
	defer func() {
		if err != nil {
			log.ErrorWithCtx(ctx, "ApplyPk fail %v, uid=%d targetUid=%d", err, si.UserID, in.TargetUid)
		}
	}()

	if !checkClientVersion(ctx) {
		return out, protocol.NewServerError(status.ErrChannelLiveNotPkAuth, "请更新至最新版进行PK")
	}

	if conf.IsBanUidPk(in.Uid) || conf.IsBanUidPk(in.TargetUid) {
		log.ErrorWithCtx(ctx, "ApplyPk IsBanUidPk uid:%v targetUid:%v", in.Uid, in.TargetUid)
		return out, protocol.NewServerError(status.ErrChannelLiveNotPkAuth, "uid ban pk")
	}

	blackResp, err := s.channelLivePkCli.BatchCheckIfInBlacklist(ctx, &channellivepkPB.BatchCheckIfInBlacklistReq{
		UidList:   []uint32{in.Uid, in.TargetUid},
		BlackType: channellivepkPB.BlacklistType_BLACKLIST_TYPE_PK,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyPk BatchCheckIfInBlacklist fail %v, uid=%d targetUid=%d", err, in.Uid, in.TargetUid)
		return out, err
	}
	if blackResp.GetBlackMap()[in.Uid] {
		log.ErrorWithCtx(ctx, "ApplyPk in blacklist uid:%v", in.Uid)
		return out, protocol.NewExactServerError(codes.OK, status.ErrChannelLiveNotPkAuth, "当前帐号不支持开启PK哦")
	}
	if blackResp.GetBlackMap()[in.TargetUid] {
		log.ErrorWithCtx(ctx, "ApplyPk target in blacklist uid:%v", in.TargetUid)
		return out, protocol.NewExactServerError(codes.OK, status.ErrChannelLiveNotPkAuth, "当前主播暂时不支持参与PK哦")
	}

	if in.Uid == in.TargetUid {
		log.ErrorWithCtx(ctx, "ApplyPk can't pk with self in:%v", in)
		return out, protocol.NewServerError(status.ErrChannelLiveNotPkAuth, "can't pk with self")
	}

	// 检查是否即将开播
	err = s.relayCheck(ctx, in.ChannelId, in.TargetChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyPk relayCheck err:%v in:%v", err, in)
		return out, err
	}

	// 检查是否在进行蒙面pk
	err = s.checkIfInMaskedPk(ctx, in.GetUid(), in.GetChannelId(), in.GetTargetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyPk checkIfInMaskedPk err:%v in:%v", err, in)
		return out, err
	}

	gameResp, err := s.revenueExtGameCli.GetMountExtGame(ctx, &revenueExtGamePb.GetMountExtGameReq{ChannelId: in.GetChannelId()})
	if nil != err {
		log.ErrorWithCtx(ctx, "ApplyPk GetMountExtGame in:%v err:%v", in, err)
		return out, err
	}
	if gameResp.GetGameType() > 0 {
		return out, protocol.NewServerError(status.ErrChannelLivePkAnchorGameLimit)
	}

	gameResp, err = s.revenueExtGameCli.GetMountExtGame(ctx, &revenueExtGamePb.GetMountExtGameReq{ChannelId: in.GetTargetChannelId()})
	if nil != err {
		log.ErrorWithCtx(ctx, "ApplyPk GetMountExtGame in:%v err:%v", in, err)
		return out, err
	}
	if gameResp.GetGameType() > 0 {
		return out, protocol.NewServerError(status.ErrChannelLivePkOtherAnchorGameLimit)
	}

	targetUid := in.TargetUid
	targetCid := in.TargetChannelId
	liveStatus, err := s.channelLiveMgrCli.GetChannelLiveStatus(ctx, channellivemgr.GetChannelLiveStatusReq{Uid: targetUid, ChannelId: targetCid})
	if err != nil {
		log.ErrorWithCtx(ctx, "SerarchPkAnchor GetChannelLiveStatus fail %v, targetUid=%d", err, targetUid)
		return out, err
	}
	log.InfoWithCtx(ctx, "ApplyPk GetChannelLiveStatus opuid=%d targetUid=%d targetCid=%d liveStatus=%s", si.UserID, targetUid, targetCid, liveStatus.String())
	// 在PK状态, 已经包含多人pk
	if liveStatus.GetChannelLiveInfo().GetChannelLiveStatus().GetPkStatus() != channellivemgr.EnumChannelLivePKStatus_IDLE {
		return out, protocol.NewServerError(status.ErrChannelLivePkIng)
	}

	// 多人pk检查
	// 检查下是否在其他人匹配队伍中
	livePkMatchResp, err := s.channelLivePkCli.BatCheckIsInMultiMatch(ctx, &channellivepkPB.BatCheckIsInMultiMatchReq{UidList: []uint32{targetUid}})
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyPk BatCheckIsInMultiMatch fail %v, uid=%d", err, si.UserID)
		return out, err
	}
	log.InfoWithCtx(ctx, "ApplyPk BatCheckIsInMultiMatch opuid=%d targetUid=%d livePkMatchResp=%s", si.UserID, targetUid, livePkMatchResp.String())
	if livePkMatchResp.GetMapIdIsMatch()[targetUid] {
		return out, protocol.NewServerError(status.ErrChannelLivePkIng)
	}

	req := &serPb.ApplyPkReq{
		Uid:             in.Uid,
		ChannelId:       in.ChannelId,
		TargetUid:       in.TargetUid,
		TargetChannelId: in.TargetChannelId,
		ChannelLiveId:   in.ChannelLiveId,
	}

	resp, err := s.channelLiveMgrCli.ApplyPk(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyPk ApplyPk err:%v in:%v", err, in)
		return out, err
	}

	userResp, err := s.accountClient.GetUser(ctx, in.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyPk GetUser serr:%v uid:%v", err, in.Uid)
		return out, err
	}

	//推送给被申请人
	pushMsg := &pb.PkApplyPushMsg{
		MsgTy:     pb.EnumApply_apply,
		Uid:       in.Uid,
		Sex:       userResp.Sex,
		Account:   in.Account,
		ChannelId: in.ChannelId,
		Nickname:  userResp.Nickname,
	}
	pushByte, perr := proto.Marshal(pushMsg)
	if perr != nil {
		log.ErrorWithCtx(ctx, "ApplyPk Marshal err:%v in:%v", perr, in)
		return out, perr
	}

	//推送给被申请人
	perr = s.PushUserMsg(ctx, pushByte, uint32(gaPush.PushMessage_CHANNEL_LIVE_PK_APPLY_PUSH), []uint32{in.TargetUid}, true)
	if perr != nil {
		log.ErrorWithCtx(ctx, "ApplyPk PushUserMsg err:%v in:%v", perr, in)
		return out, perr
	}

	log.DebugWithCtx(ctx, "ApplyPk in:%v out:%v resp:%v", in, out, resp)

	return out, nil
}

func (s *ChannelLiveLogic_) SearchAnchor(ctx context.Context, in *pb.SearchAnchorReq) (*pb.SearchAnchorResp, error) {
	out := &pb.SearchAnchorResp{}

	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	OptUid := serviceInfo.UserID

	log.DebugWithCtx(ctx, "SearchAnchor req:%v", in)

	if len(in.Account) < 2 {
		return out, nil
	}

	uid, _, serr := s.accountClient.GetUidByName(ctx, in.Account)
	if serr != nil {
		log.ErrorWithCtx(ctx, "SearchAnchorL GetUidByName err:%v tmp:%v", serr, in.Account)
	}

	//兼容短号查询
	if uid <= 0 && (len(in.Account) >= 2 && in.Account[0:1] != "tt") {
		tmp := "tt" + in.Account
		uid, _, serr = s.accountClient.GetUidByName(ctx, tmp)
		if serr != nil {
			log.ErrorWithCtx(ctx, "SearchAnchorL GetUidByName err:%v in:%v", serr, tmp)
			return out, nil
		}
	}

	if uid <= 0 {
		return out, nil
	}

	user, serr := s.accountClient.GetUser(ctx, uid)
	if serr != nil {
		log.ErrorWithCtx(ctx, "SearchAnchorL GetUser err:%v", serr)
		return out, serr
	}

	if uid == OptUid {
		log.ErrorWithCtx(ctx, "SearchAnchorL GetUser err:%v", serr)
		return out, serr
	}

	sResp, serr := s.channelLiveMgrCli.SearchAnchor(ctx, &serPb.SearchAnchorReq{
		Uid: uid,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "SearchAnchorL service err:%v", serr)
		return out, nil
	}

	out.AnchorInfo = &pb.AnchorInfo{
		Uid:                 sResp.AnchorInfo.Uid,
		Account:             user.Username,
		Nickname:            user.Nickname,
		Sex:                 user.Sex,
		ChannelId:           sResp.AnchorInfo.ChannelId,
		ChannelLiveStatus:   sResp.AnchorInfo.ChannelLiveStatus,
		ChannelLivePkStatus: sResp.AnchorInfo.ChannelLivePkStatus,
		Alias:               user.Alias,
	}

	log.DebugWithCtx(ctx, "SearchAnchor out:%v in:%v", out, in)

	return out, nil
}

func (s *ChannelLiveLogic_) HandlerApply(ctx context.Context, in *pb.HandlerApplyReq) (*pb.HandlerApplyResp, error) {
	out := &pb.HandlerApplyResp{}

	log.DebugWithCtx(ctx, "HandlerApplyL req:%v", in)

	if !checkClientVersion(ctx) {
		return out, protocol.NewServerError(status.ErrChannelLiveNotPkAuth, "请更新至最新版进行PK")
	}

	blackResp, err := s.channelLivePkCli.BatchCheckIfInBlacklist(ctx, &channellivepkPB.BatchCheckIfInBlacklistReq{
		UidList:   []uint32{in.Uid},
		BlackType: channellivepkPB.BlacklistType_BLACKLIST_TYPE_PK,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "HandlerApply BatchCheckIfInBlacklist fail %v, uid=%d", err, in.Uid)
		return out, err
	}

	if blackResp.GetBlackMap()[in.Uid] {
		log.ErrorWithCtx(ctx, "HandlerApply in blacklist uid:%v", in.Uid)
		return out, protocol.NewExactServerError(codes.OK, status.ErrChannelLiveNotPkAuth, "当前帐号不支持开启PK哦")
	}

	if in.Oper == pb.EnumApply_accept {
		// 检查是否在进行蒙面pk
		serr := s.checkIfInMaskedPk(ctx, in.GetUid(), in.GetChannelId(), in.GetApplyChannelId())
		if serr != nil {
			log.ErrorWithCtx(ctx, "HandlerApply checkIfInMaskedPk err:%v in:%v", serr, in)
			return out, serr
		}
	}

	user, serr := s.accountClient.GetUser(ctx, in.Uid)
	if serr != nil {
		log.ErrorWithCtx(ctx, "HandlerApplyLX GetUser serr:%v uid:%v", serr, in.Uid)
		return out, serr
	}

	applyUser, serr := s.accountClient.GetUser(ctx, in.ApplyUid)
	if serr != nil {
		log.ErrorWithCtx(ctx, "HandlerApplyLX GetUser serr:%v uid:%v", serr, in.ApplyUid)
		return out, serr
	}

	req := &serPb.HandlerApplyReq{
		Uid:       in.Uid,
		ChannelId: in.ChannelId,
		Username:  user.Username,
		Nickname:  user.Nickname,

		ApplyUid:       in.ApplyUid,
		ApplyNickname:  applyUser.Nickname,
		ApplyUsername:  applyUser.Username,
		ApplyChannelId: in.ApplyChannelId,
		Oper:           serPb.EnumApply(in.Oper),
	}
	resp, err := s.channelLiveMgrCli.HandlerApply(ctx, req)

	if err != nil {
		log.ErrorWithCtx(ctx, "HandlerApplyLX err:%v req:%v", err, req)
		return out, err
	}
	// broadcaset
	if req.Oper == serPb.EnumApply_reject {
		// 申请被拒绝，推送给申请人
		applyMsg := &pb.PkApplyPushMsg{
			MsgTy:     pb.EnumApply_reject,
			Uid:       in.Uid,
			Account:   user.Username,
			ChannelId: in.ChannelId,
		}
		msgBin, _ := proto.Marshal(applyMsg)
		s.PushUserMsg(ctx, msgBin, uint32(gaPush.PushMessage_CHANNEL_LIVE_PK_APPLY_PUSH), []uint32{in.ApplyUid}, true)

		log.DebugWithCtx(ctx, "HandlerApplyL applyMsg:%v", applyMsg)
	} else if req.Oper == serPb.EnumApply_cancel {
		applyUser, serr := s.accountClient.GetUser(ctx, in.ApplyUid)
		if serr == nil {
			applyMsg := &pb.PkApplyPushMsg{
				MsgTy:     pb.EnumApply_cancel,
				Uid:       in.ApplyUid,
				Account:   applyUser.Username,
				ChannelId: in.ApplyChannelId,
			}
			msgBin, _ := proto.Marshal(applyMsg)
			s.PushUserMsg(ctx, msgBin, uint32(gaPush.PushMessage_CHANNEL_LIVE_PK_APPLY_PUSH), []uint32{in.Uid}, true)

			log.DebugWithCtx(ctx, "HandlerApplyL applyMsg:%v", applyMsg)
		}
	} else {
		// do nothing
		log.DebugWithCtx(ctx, "HandlerApplyL do nothing req:%v", req)
	}

	log.DebugWithCtx(ctx, "HandlerApplyL in:%v out:%v resp:%v serr:%v", in, out, resp, err)

	return out, nil
}

func (s *ChannelLiveLogic_) CancelPKApply(ctx context.Context, in *pb.CancelPKApplyReq) (*pb.CancelPKApplyResp, error) {
	out := &pb.CancelPKApplyResp{}

	log.DebugWithCtx(ctx, "CancelPKApply req:%v", in)

	cInfo, serr := s.channelLiveMgrCli.GetChannelLiveInfo(ctx, in.Uid, false)
	if serr != nil {
		log.ErrorWithCtx(ctx, "CancelPKApply serr:%v", serr)
		return out, serr
	}

	cTargetInfo, serr := s.channelLiveMgrCli.GetChannelLiveInfo(ctx, in.TargetUid, false)
	if serr != nil {
		log.ErrorWithCtx(ctx, "CancelPKApply serr:%v", serr)
		return out, serr
	}

	_, err := s.channelLiveMgrCli.CancelPKApply(ctx, serPb.CancelPKApplyReq{
		Uid:            in.TargetUid,
		ChannelId:      cTargetInfo.ChannelLiveInfo.ChannelId,
		ApplyUid:       in.Uid,
		ApplyChannelId: cInfo.ChannelLiveInfo.ChannelId,
	})

	user, serr := s.accountClient.GetUser(ctx, in.Uid)
	if serr != nil {
		return out, serr
	}

	applyMsg := &pb.PkApplyPushMsg{
		MsgTy:     pb.EnumApply_cancel,
		Uid:       in.Uid,
		Account:   user.Username,
		ChannelId: cInfo.ChannelLiveInfo.ChannelId,
		Nickname:  user.Nickname,
	}

	applyMsgBin, _ := proto.Marshal(applyMsg)

	s.PushUserMsg(ctx, applyMsgBin, uint32(gaPush.PushMessage_CHANNEL_LIVE_PK_APPLY_PUSH), []uint32{in.TargetUid}, true)

	log.DebugWithCtx(ctx, "CancelPKApply req:%v applyMsg:%v", in, applyMsg)

	return out, err
}

func (s *ChannelLiveLogic_) GetApplyList(ctx context.Context, in *pb.GetApplyListReq) (*pb.GetApplyListResp, error) {
	out := &pb.GetApplyListResp{}

	resp, serr := s.channelLiveMgrCli.GetApplyList(ctx, &serPb.GetApplyListReq{
		Uid:       in.Uid,
		ChannelId: in.ChannelId,
	})

	log.DebugWithCtx(ctx, "GetApplyList in:%+v resp:%+v", in, resp)

	if serr != nil {
		log.ErrorWithCtx(ctx, "GetApplyList serr:%v in:%v", serr, in)
		return out, serr
	}

	out.ApplyList = make([]*pb.Apply, 0)
	for _, v := range resp.ApplyList {
		out.ApplyList = append(out.ApplyList, &pb.Apply{
			ApplyUid:       v.ApplyUid,
			ApplyChannelId: v.ApplyChannelId,
			ApplyTime:      v.ApplyTime,
		})
	}

	log.DebugWithCtx(ctx, "GetApplyList in:%+v out:%+v", in, out)

	return out, nil
}

func (s *ChannelLiveLogic_) BatchGetChannelLiveStatusByAccount(ctx context.Context, in *pb.BatchGetChannelLiveStatusByAccountReq) (*pb.BatchGetChannelLiveStatusByAccountResp, error) {
	out := &pb.BatchGetChannelLiveStatusByAccountResp{}

	log.DebugWithCtx(ctx, "BatchGetChannelLiveStatusByAccount req:%v", in)

	if 0 == len(in.Accounts) {
		return out, nil
	}

	account2uid, serr := s.accountClient.BatchQueryUidList(ctx, in.Accounts)

	if serr != nil {
		log.ErrorWithCtx(ctx, "BatchGetChannelLiveStatusByAccount BatchQueryUidList fail err:%v", serr)
	}

	uidList := make([]uint32, 0)

	for _, account := range account2uid {
		uidList = append(uidList, account.GetUid())
	}

	req := serPb.BatchGetChannelLiveStatusReq{
		UidList: uidList,
		GetAll:  false,
	}

	resp, serr := s.channelLiveMgrCli.BatchGetChannelLiveStatus(ctx, req)

	if serr != nil {
		log.ErrorWithCtx(ctx, "BatchGetChannelLiveStatusByAccount BatchGetGetChannelLiveStatus fail err:%v", serr)
		return out, serr
	}

	for _, chLiveInfo := range resp.ChannelLiveInfoList {
		tmpInfo := &pb.ChannelLiveInfo{
			ChannelLiveStatus: &pb.ChannelLiveStatus{
				Uid:       chLiveInfo.ChannelLiveStatus.Uid,
				Account:   chLiveInfo.ChannelLiveStatus.Account,
				Status:    pb.EnumChannelLiveStatus(chLiveInfo.ChannelLiveStatus.Status),
				ChannelId: chLiveInfo.ChannelLiveStatus.ChannelId,
				PkStatus:  pb.EnumChannelLivePKStatus(chLiveInfo.ChannelLiveStatus.PkStatus),
			},
		}

		out.ChannelLiveInfoList = append(out.ChannelLiveInfoList, tmpInfo)
	}

	log.DebugWithCtx(ctx, "BatchGetChannelLiveStatusByAccount in:%v out:%v", in, out)

	return out, nil
}

func (s *ChannelLiveLogic_) SetPkStatus(ctx context.Context, in *pb.SetPkStatusReq) (*pb.SetPkStatusResp, error) {
	out := &pb.SetPkStatusResp{}

	log.DebugWithCtx(ctx, "SetPkStatusL req:%v", in)

	// 设置结束PK
	resp, err := s.channelLiveMgrCli.SetPkStatus(ctx, serPb.SetPkStatusReq{
		Uid:       in.Uid,
		ChannelId: in.ChannelId,
		Status:    serPb.EnumChannelLivePKStatus(in.Status),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "SetPkStatusLX fail err:%v", err)
		return out, err
	}

	pkStatusPush := &pb.ChannelLivePkStatusPushMsg{
		CommonInfo: &pb.PkCommonInfo{
			BeginTime: resp.BeginTime,
			Status:    pb.EnumChannelLivePKStatus_IDLE,
		},
	}

	pushMsg, _ := proto.Marshal(pkStatusPush)
	s.PushChannelMsg(ctx, pushMsg, uint32(channel.ChannelMsgType_CHANNEL_LIVE_PK_STATUS_MGS), []uint32{in.ChannelId, resp.TargetChannelId})

	log.DebugWithCtx(ctx, "SetPkStatusL push in:%v msg:%v", in, pkStatusPush)

	return out, nil
}

func (s *ChannelLiveLogic_) GetChannelLivePKRecord(ctx context.Context, in *pb.GetChannelLivePKRecordReq) (out *pb.GetChannelLivePKRecordResp, err error) {

	si, _ := protogrpc.ServiceInfoFromContext(ctx)

	out = &pb.GetChannelLivePKRecordResp{
		RecordList: make([]*pb.ChannelLivePKRecord, 0),
		StatusList: make([]*pb.ChannelLiveStatus, 0),
	}
	defer func() {
		log.DebugWithCtx(ctx, "GetChannelLivePKRecord si=%s req:%v out=%s", si.String(), in, out.String())
	}()

	resp, err := s.channelLiveMgrCli.GetChannelLivePKRecord(ctx, serPb.GetChannelLivePKRecordReq{
		Uid: in.Uid,
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLivePKRecord err:%v", err)
		return out, err
	}

	log.DebugWithCtx(ctx, "GetChannelLivePKRecord server resp:%v in:%v", resp, in)

	uids := make([]uint32, 0)
	for _, re := range resp.RecordList {
		targetUid := re.Uid
		if targetUid == in.Uid {
			targetUid = re.TargetUid
		}
		uids = append(uids, targetUid)
	}

	users, _ := s.accountClient.GetUsersMap(ctx, uids)

	//accounts := make([]string, 0)
	for _, re := range resp.RecordList {
		//nickname := re.Nick
		targetUid := re.Uid
		targetChannelId := re.ChannelId

		if re.Uid == in.Uid {
			targetUid = re.TargetUid
			targetChannelId = re.TargetChannelId
		}

		user, ok := users[targetUid]
		if !ok {
			continue
		}

		out.RecordList = append(out.RecordList, &pb.ChannelLivePKRecord{
			Uid:       targetUid,
			Account:   user.Username,
			Nick:      user.Nickname,
			ChannelId: targetChannelId,
			BeginTime: re.CreateTime,
			LiveTime:  0,
			Sex:       uint32(user.Sex),
		})
	}

	log.DebugWithCtx(ctx, "GetChannelLivePKRecord server22 resp:%v in:%v", resp, in)

	resp2, err := s.channelLiveMgrCli.BatchGetChannelLiveStatus(ctx, serPb.BatchGetChannelLiveStatusReq{
		UidList: uids,
		GetAll:  false,
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLivePKRecord BatchGetChannelLiveStatus err:%v", err)
		return out, err
	}

	if resp2.ChannelLiveInfoList == nil {
		log.ErrorWithCtx(ctx, "GetChannelLivePKRecord BatchGetChannelLiveStatus ChannelLiveInfoList nil")
		return out, err
	}
	log.DebugWithCtx(ctx, "GetChannelLivePKRecord uids=%v BatchGetChannelLiveStatus=%s", uids, resp2.String())

	subCtx, cancel := context.WithTimeout(context.Background(), time.Millisecond*300)
	defer cancel()
	uid2IsMultiMatch, err2 := s.BatCheckIsInMultiMatch(subCtx, uids)
	if err2 != nil {
		log.ErrorWithCtx(ctx, "GetChannelLivePKRecord BatCheckIsInMultiMatch fail %v uid=%d", err2, si.UserID)
	}
	log.InfoWithCtx(ctx, "GetChannelLivePKRecord BatCheckIsInMultiMatch uids=%v, out=%v", uids, uid2IsMultiMatch)

	for _, st := range resp2.ChannelLiveInfoList {
		if st.ChannelLiveStatus == nil || st.PkCommonInfo == nil {
			continue
		}

		status := &pb.ChannelLiveStatus{
			Uid:           st.ChannelLiveStatus.Uid,
			Account:       st.ChannelLiveStatus.Account,
			Nickname:      st.ChannelLiveStatus.Nickname,
			Sex:           st.ChannelLiveStatus.Sex,
			ChannelId:     st.ChannelLiveStatus.ChannelId,
			ChannelLiveId: st.ChannelLiveStatus.ChannelLiveId,
			MicList:       nil,
			Status:        pb.EnumChannelLiveStatus(st.ChannelLiveStatus.Status),
			PkStatus:      pb.EnumChannelLivePKStatus(st.PkCommonInfo.PkStatus),
		}

		log.DebugWithCtx(ctx, "GetChannelLivePKRecord StatusList append=%s", status.String())

		// 在多人匹配队伍也算pk中
		if uid2IsMultiMatch[st.ChannelLiveStatus.Uid] {
			status.PkStatus = pb.EnumChannelLivePKStatus_BEGIN
			log.InfoWithCtx(ctx, "GetChannelLivePKRecord op_uid=%d uid=%d in multi match.", si.UserID, st.ChannelLiveStatus.Uid)
		}

		out.StatusList = append(out.StatusList, status)
	}

	log.DebugWithCtx(ctx, "GetChannelLivePKRecord server33 resp:%v in:%v", resp, in)

	log.DebugWithCtx(ctx, "GetChannelLivePKRecord out:%v in:%v", out, in)

	return out, nil
}

func (s *ChannelLiveLogic_) BatCheckIsInMultiMatch(ctx context.Context, uidList []uint32) (map[uint32]bool, error) {

	uid2IsMatch := map[uint32]bool{}

	limit := 100
	for i := 0; i < len(uidList); i += limit {
		end := i + limit
		if end > len(uidList) {
			end = len(uidList)
		}

		uidsx := uidList[i:end]
		log.Debugf("BatCheckIsInMultiMatch %d-%d uids=%v", i, end, uidsx)
		if len(uidsx) == 0 {
			break
		}

		// 检查下是否在其他人匹配队伍中
		livePkMatchResp, err := s.channelLivePkCli.BatCheckIsInMultiMatch(ctx, &channellivepkPB.BatCheckIsInMultiMatchReq{UidList: uidList})
		if err != nil {
			log.ErrorWithCtx(ctx, "BatCheckIsInMultiMatch BatCheckIsInMultiMatch fail %v ", err)
			return uid2IsMatch, err
		}
		for uid := range livePkMatchResp.GetMapIdIsMatch() {
			uid2IsMatch[uid] = true
		}
	}

	log.DebugWithCtx(ctx, "BatCheckIsInMultiMatch uids=%v, out=%v", uidList, uid2IsMatch)
	return uid2IsMatch, nil
}

/*
func getUserAccountInfo(uid uint32, userInfo *account.User, ukwInfo *youknowwho.UKWPersonInfo) (string, string, *app.UserProfile, *app.UserUKWInfo) {
	log.Debugf("getUserAccountInfo begin userInfo:%v ukwInfo:%v", userInfo, ukwInfo)

	account, nickName := userInfo.GetUsername(), userInfo.GetNickname()
	if ukwInfo.GetAccount() != "" {
		account = ukwInfo.GetAccount()
		nickName = ukwInfo.GetNickname()
	}

	userProfile := &app.UserProfile{
		Uid:          uid,
		Account:      userInfo.GetUsername(),
		Nickname:     userInfo.GetNickname(),
		AccountAlias: userInfo.GetAlias(),
		Sex:          uint32(userInfo.GetSex()),
		Privilege: &app.UserPrivilege{
			Account:  ukwInfo.GetAccount(),
			Nickname: ukwInfo.GetNickname(),
		},
	}

	userUkwInfo := &app.UserUKWInfo{
		Level:     ukwInfo.GetLevel(),
		Medal:     ukwInfo.GetMedal(),
		HeadFrame: ukwInfo.GetHeadFrame(),
	}

	byteUkwInfo, err := proto.Marshal(userUkwInfo)
	if err != nil {
		log.Errorf("getUserAccountInfo json.Marshal failed info:%v err:%v", userUkwInfo, err)
		return account, nickName, userProfile, userUkwInfo
	}

	userProfile.Privilege.Type = uint32(app.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW)
	userProfile.Privilege.Options = byteUkwInfo

	return account, nickName, userProfile, userUkwInfo
}
*/

func getUserAccountInfoFromUidInfo(user *account.User, userInfo *serPb.UkwInfo) (uint32, string, string, *app.UserProfile, *app.UserUKWInfo) {
	log.Debugf("getUserAccountInfoFromUidInfo begin userInfo:%v ukwInfo:%v", userInfo, userInfo)

	uid, account, nickName := user.GetUid(), user.GetUsername(), user.GetNickname()
	if userInfo.GetLevel() > 0 {
		account = userInfo.GetAccount()
		nickName = userInfo.GetNickname()
		uid = userInfo.GetFakeUid()
	}

	userProfile := &app.UserProfile{
		Uid:          uid,
		Account:      account,
		Nickname:     nickName,
		AccountAlias: user.GetAlias(),
		Sex:          uint32(user.GetSex()),
		Privilege: &app.UserPrivilege{
			Account:  userInfo.GetAccount(),
			Nickname: userInfo.GetNickname(),
		},
	}

	userUkwInfo := &app.UserUKWInfo{
		Level:     userInfo.GetLevel(),
		Medal:     userInfo.GetMedal(),
		HeadFrame: userInfo.GetHeadFrame(),
	}

	byteUkwInfo, err := proto.Marshal(userUkwInfo)
	if err != nil {
		log.Errorf("getUserAccountInfoFromUidInfo json.Marshal failed info:%v err:%v", userUkwInfo, err)
		return uid, account, nickName, userProfile, userUkwInfo
	}

	userProfile.Privilege.Type = uint32(app.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW)
	userProfile.Privilege.Options = byteUkwInfo

	log.Debugf("getUserAccountInfoFromUidInfo end userInfo:%v ukwInfo:%v userProfile:%v", userInfo, userInfo, userProfile)
	return uid, account, nickName, userProfile, userUkwInfo
}

func (s *ChannelLiveLogic_) GetChannelLivePkRankUser(ctx context.Context, in *pb.GetChannelLivePkRankUserReq) (*pb.GetChannelLivePkRankUserResp, error) {
	out := &pb.GetChannelLivePkRankUserResp{}

	log.DebugWithCtx(ctx, "GetChannelLivePkRankUser begin req:%v", in)

	var off, cnt uint32 = 0, 4
	if in.Off != 0 {
		off = in.Off
	}
	if in.Cnt != 0 {
		if conf.Env != "Debug" {
			cnt = in.Cnt
		}
	}

	resp, err := s.channelLiveMgrCli.GetChanneLivePkRankUser(ctx, serPb.GetChanneLivePkRankUserReq{
		ChannelId: in.ChannelId,
		Off:       off,
		Cnt:       cnt,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLivePkRankUser failed in:%v err:%v", in, err)
		return out, err
	}

	uidList := make([]uint32, 0)
	for _, rank := range resp.UserList {
		uidList = append(uidList, rank.Uid)
	}

	mapUser, err := s.accountClient.GetUsersMap(ctx, uidList)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetChannelLivePkRankUser GetUsersMap in:%v sz:%v err:%v", in, len(uidList), err)
		return out, err
	}

	subCtx, cancel := context.WithTimeout(ctx, time.Millisecond*800)
	defer cancel()

	fansResp, err := s.fansCli.BatchGetFansInfo(subCtx, resp.AnchorUid, uidList)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetChannelLivePkRankUser BatchGetFansInfo failed in:%v sz:%v err:%v", in, len(uidList), err)
	}

	// 贵族信息
	nobilityInfoMap, err := s.nobilityClient.BatchGetNobilityInfo(subCtx, in.ChannelId, uidList)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetChannelLivePkRankUser BatchGetNobilityInfo failed in:%v sz:%v err:%v", in, len(uidList), err)
	}

	//房间成员vip信息
	memberVipMap, err := s.memberRank.BatGetUserConsumeInfo(subCtx, resp.AnchorUid, in.GetChannelId(), uidList)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetChannelLivePkRankUser BatGetUserConsumeInfo failed in:%v sz:%v err:%v", in, len(uidList), err)
	}

	mapNum, err := s.numericClient.BatchGetPersonalNumeric(subCtx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLivePkRankUser BatchGetPersonalNumeric failed in:%v sz:%v err:%v", in, len(uidList), err)
	}

	for _, rank := range resp.UserList {
		user, ok := mapUser[rank.Uid]
		if !ok {
			continue
		}

		resUid, account, nickName, userProfile, ukwInfo := getUserAccountInfoFromUidInfo(user, rank.UkwInfo)

		var rich, charm uint32 = 0, 0
		if num, ok := mapNum[rank.Uid]; ok && ukwInfo.GetLevel() == 0 {
			rich = uint32(int(num.Rich64 / 1000))
			charm = uint32(int(num.Charm64 / 1000))
		}

		var pinfo *app.FansPlateInfo
		groupName := ""
		var fansLv uint32 = 0

		if fansResp != nil {
			fan, ok := fansResp[rank.Uid]
			if ok && ukwInfo.GetLevel() == 0 {
				groupName = fan.GetGroupName()
				plateInfo := fan.GetPlateInfo()
				fansLv = fan.GetFansLevel()
				pinfo = s.FillPlateConfigMsg(plateInfo)
			}
		}

		nobilityLv := nobilityInfoMap[rank.Uid].GetLevel()
		nobilityFlv := nobilityInfoMap[rank.Uid].GetFLevel()
		memLv := memberVipMap[rank.Uid].GetCurrLevelId()
		if ukwInfo.GetLevel() > 0 {
			nobilityLv, memLv, nobilityFlv = 0, 0, 0
		}
		out.UserList = append(out.UserList, &pb.SendGiftUserInfo{
			Uid:             resUid,
			Account:         account,
			Score:           rank.Score,
			Rich:            rich,
			Charm:           charm,
			Nickname:        nickName,
			NobilityLevel:   nobilityLv,
			GroupFansLevel:  fansLv,
			ChannelMemLevel: memLv,
			GroupName:       groupName,
			PlateInfo:       pinfo,
			Sex:             uint32(user.Sex),
			FirstKill:       rank.FirstKill,
			ChannelId:       in.ChannelId,
			UserProfile:     userProfile,
			NobilityInfo: &app.NobilityInfo{
				Level:  nobilityLv,
				FLevel: nobilityFlv,
			},
		})
	}

	out.ChannelId = in.ChannelId

	log.DebugWithCtx(ctx, "GetChannelLivePkRankUser end in:%v out:%v", in, out)
	return out, nil
}

func (s *ChannelLiveLogic_) FillPkRankData(subCtx context.Context, aChannelID, bChannelID uint32) ([]*pb.SendGiftUserInfo, []*pb.SendGiftUserInfo) {
	aPkRank := make([]*pb.SendGiftUserInfo, 0)
	bPkRank := make([]*pb.SendGiftUserInfo, 0)

	aPkRankResp, perr := s.channelLiveMgrCli.GetChanneLivePkRankUser(subCtx, serPb.GetChanneLivePkRankUserReq{
		Uid:       0,
		ChannelId: aChannelID,
		TargetUid: 0,
		Off:       0,
		Cnt:       2,
	})

	if perr != nil {
		log.ErrorWithCtx(subCtx, "FillPkRankData GetChanneLivePkRankUser perr:%v aChannelID:%v", perr, aChannelID)
		return aPkRank, bPkRank
	}

	bPkRankResp, perr := s.channelLiveMgrCli.GetChanneLivePkRankUser(subCtx, serPb.GetChanneLivePkRankUserReq{
		Uid:       0,
		ChannelId: bChannelID,
		TargetUid: 0,
		Off:       0,
		Cnt:       2,
	})

	if perr != nil {
		log.ErrorWithCtx(subCtx, "FillPkRankData GetChanneLivePkRankUser perr:%v bChannelID:%v", perr, bChannelID)
		return aPkRank, bPkRank
	}

	uidList := make([]uint32, 0)
	for _, rank := range aPkRankResp.UserList {
		uidList = append(uidList, rank.Uid)
	}
	for _, rank := range bPkRankResp.UserList {
		uidList = append(uidList, rank.Uid)
	}

	usermap, serr := s.accountClient.GetUsersMap(subCtx, uidList)
	if serr != nil {
		log.ErrorWithCtx(subCtx, "FillPkRankData GetUsersMap serr:%v", serr)
		return aPkRank, bPkRank
	}

	for _, rank := range aPkRankResp.UserList {
		user, ok := usermap[rank.Uid]
		if !ok {
			continue
		}

		resUid, account, nickName, userProfile, _ := getUserAccountInfoFromUidInfo(user, rank.UkwInfo)
		aPkRank = append(aPkRank, &pb.SendGiftUserInfo{
			Uid:         resUid,
			Account:     account,
			Score:       rank.Score,
			Nickname:    nickName,
			Sex:         uint32(user.GetSex()),
			FirstKill:   false,
			ChannelId:   aChannelID,
			UserProfile: userProfile,
		})
	}

	for _, rank := range bPkRankResp.UserList {
		user, ok := usermap[rank.Uid]
		if !ok {
			continue
		}

		resUid, account, nickName, userProfile, _ := getUserAccountInfoFromUidInfo(user, rank.UkwInfo)
		bPkRank = append(bPkRank, &pb.SendGiftUserInfo{
			Uid:         resUid,
			Account:     account,
			Score:       rank.Score,
			Nickname:    nickName,
			Sex:         uint32(user.GetSex()),
			FirstKill:   false,
			ChannelId:   bChannelID,
			UserProfile: userProfile,
		})
	}

	return aPkRank, bPkRank
}

func (s *ChannelLiveLogic_) FillPkMicData(ctx context.Context, aChannelID, bChannelID uint32, micList map[uint32]*serPb.PkMicSpace) ([]*pb.PkMicSpace, []*pb.PkMicSpace) {
	aMicList := make([]*pb.PkMicSpace, 0)
	bMicList := make([]*pb.PkMicSpace, 0)

	aResp, merr := s.channelMicCli.GetMicrList(ctx, aChannelID, aChannelID)
	if merr != nil {
		log.ErrorWithCtx(ctx, "FillPkMicData aChannelID:%v merr:%v", aChannelID, merr)
		return aMicList, bMicList
	}

	bResp, merr := s.channelMicCli.GetMicrList(ctx, bChannelID, bChannelID)
	if merr != nil {
		log.ErrorWithCtx(ctx, "FillPkMicData bChannelID:%v merr:%v", bChannelID, merr)
		return aMicList, bMicList
	}

	uids := make([]uint32, 0)
	for _, mic := range aResp.AllMicList {
		if mic.MicUid == 0 {
			continue
		}
		uids = append(uids, mic.MicUid)
	}
	for _, mic := range bResp.AllMicList {
		if mic.MicUid == 0 {
			continue
		}
		uids = append(uids, mic.MicUid)
	}

	if len(uids) == 0 {
		log.InfoWithCtx(ctx, "FillPkMicData micList is empty %d %d", aChannelID, bChannelID)
		return aMicList, bMicList
	}

	mapUid2Profile, err := s.userProfileCli.BatchGetUserProfileV2(ctx, uids, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "FillPkMicData BatchGetUserProfileV2 failed uidList:%v err:%v", uids, err)
		return aMicList, bMicList
	}

	for _, mic := range aResp.AllMicList {
		if m, ok := micList[mic.MicUid]; ok {
			profile, ok := mapUid2Profile[mic.MicUid]
			if !ok {
				continue
			}

			aMicList = append(aMicList, &pb.PkMicSpace{
				MicId:                mic.MicId,
				Uid:                  profile.GetUid(),
				Account:              profile.GetAccount(),
				Nick:                 profile.GetNickname(),
				ChannelClientId:      m.VoiceId,
				Sex:                  int32(profile.GetSex()),
				UserProfile:          profile,
				ChannelVideoClientId: m.GetChannelVideoClientId(),
			})
		}
	}

	for _, mic := range bResp.AllMicList {
		if m, ok := micList[mic.MicUid]; ok {
			profile, ok := mapUid2Profile[mic.MicUid]
			if !ok {
				continue
			}

			bMicList = append(bMicList, &pb.PkMicSpace{
				MicId:                mic.MicId,
				Uid:                  profile.GetUid(),
				Account:              profile.GetAccount(),
				Nick:                 profile.GetNickname(),
				ChannelClientId:      m.VoiceId,
				Sex:                  int32(profile.GetSex()),
				Alias:                profile.GetAccountAlias(),
				UserProfile:          profile,
				ChannelVideoClientId: m.GetChannelVideoClientId(),
			})
		}
	}
	return aMicList, bMicList
}

func (s *ChannelLiveLogic_) GetPkInfo(ctx context.Context, in *pb.GetPkInfoReq) (*pb.GetPkInfoResp, error) {
	out := &pb.GetPkInfoResp{PkInfo: &pb.PkInfo{}}

	log.DebugWithCtx(ctx, "GetPkInfo req:%v", in)

	resp, serr := s.channelLiveMgrCli.GetPkInfo(ctx, in.AnchorUid, in.ChannelId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetPkInfo in:%v serr:%v", in, serr)
		return out, nil
	}

	if resp.APkInfo.ChannelId == 0 || resp.BPkInfo.ChannelId == 0 {
		log.ErrorWithCtx(ctx, "GetPkInfo pk empty in:%v", in)
		return out, nil
	}

	tmpUidList := []uint32{resp.APkInfo.Uid, resp.BPkInfo.Uid}
	if resp.GetPkCommonInfo().GetFirstKillUid() != 0 {
		tmpUidList = append(tmpUidList, resp.GetPkCommonInfo().GetFirstKillUid())
	}

	users, serr := s.accountClient.GetUsersMap(ctx, tmpUidList)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetPkInfo GetUsersMap in:%v errr:%v", in, serr)
		return out, serr
	}

	log.DebugWithCtx(ctx, "GetPkInfo resp:%v", resp)

	alias, nickname, account := "", "", ""
	var sex int32 = 0
	if u, ok := users[resp.APkInfo.Uid]; ok {
		alias = u.Alias
		sex = u.Sex
		nickname = u.Nickname
		account = u.Username
	}

	status := pb.EnumChannelLiveStatus_OPEN
	chStatusResp, serr := s.channelLiveMgrCli.GetChannelLiveStatus(ctx, serPb.GetChannelLiveStatusReq{
		Uid:       resp.APkInfo.Uid,
		ChannelId: resp.APkInfo.ChannelId,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetPkInfo GetChannelLiveStatus failed resp:%v err:%v", resp, serr)
	}

	if serr == nil {
		if chStatusResp.ChannelLiveInfo != nil && chStatusResp.ChannelLiveInfo.ChannelLiveStatus != nil {
			status = pb.EnumChannelLiveStatus(chStatusResp.ChannelLiveInfo.ChannelLiveStatus.Status)
		}
	}

	out.PkInfo.ChallengeUser = &pb.PkUserInfo{
		Uid:       resp.APkInfo.Uid,
		Account:   account,
		Nickname:  nickname,
		ChannelId: resp.APkInfo.ChannelId,
		Status:    status,
		Sex:       sex,
		Alias:     alias,
		MicFlag:   pb.ChannelLiveOpponentMicFlag(resp.APkInfo.MicFlag),
	}

	if u, ok := users[resp.BPkInfo.Uid]; ok {
		alias = u.Alias
		sex = u.Sex
		account = u.Username
		nickname = u.Nickname
	}

	status = pb.EnumChannelLiveStatus_OPEN
	chStatusResp, serr = s.channelLiveMgrCli.GetChannelLiveStatus(ctx, serPb.GetChannelLiveStatusReq{
		Uid:       resp.BPkInfo.Uid,
		ChannelId: resp.BPkInfo.ChannelId,
	})

	if serr == nil {
		if chStatusResp.ChannelLiveInfo != nil && chStatusResp.ChannelLiveInfo.ChannelLiveStatus != nil {
			status = pb.EnumChannelLiveStatus(chStatusResp.ChannelLiveInfo.ChannelLiveStatus.Status)
		}
	}

	out.PkInfo.BechallengeUser = &pb.PkUserInfo{
		Uid:       resp.BPkInfo.Uid,
		Account:   account,
		Nickname:  nickname,
		ChannelId: resp.BPkInfo.ChannelId,
		Status:    status,
		Sex:       sex,
		Alias:     alias,
		MicFlag:   pb.ChannelLiveOpponentMicFlag(resp.BPkInfo.MicFlag),
	}

	subCtx, cancel := context.WithTimeout(ctx, time.Millisecond*800)
	defer cancel()

	aChannelID, bChannelID := resp.APkInfo.ChannelId, resp.BPkInfo.ChannelId
	aPkRank, bPkRank := s.FillPkRankData(subCtx, aChannelID, bChannelID)

	aPkMicList, bPkMicList := s.FillPkMicData(ctx, resp.APkInfo.ChannelId, resp.BPkInfo.ChannelId, resp.PkCommonInfo.MicList)

	out.PkInfo.ChallengeAnchor = &pb.ChannelPKSingleScore{
		ChannelId:      resp.APkInfo.ChannelId,
		ScoreValue:     resp.APkInfo.PkScore,
		TopScoreUser:   aPkRank,
		UseItemTopUser: nil,
		MicList:        aPkMicList,
		EffectItemCnt:  resp.APkInfo.EffectCnt,
	}

	out.PkInfo.BechallengeAnchor = &pb.ChannelPKSingleScore{
		ChannelId:      resp.BPkInfo.ChannelId,
		ScoreValue:     resp.BPkInfo.PkScore,
		TopScoreUser:   bPkRank,
		UseItemTopUser: nil,
		MicList:        bPkMicList,
		EffectItemCnt:  resp.BPkInfo.EffectCnt,
	}

	out.PkInfo.CommonInfo = &pb.PkCommonInfo{
		BeginTime:       resp.PkCommonInfo.BeginTime,
		Status:          pb.EnumChannelLivePKStatus(resp.PkCommonInfo.PkStatus),
		IsExtraTime:     resp.PkCommonInfo.IsExtraTime,
		PkExtraTimeRule: resp.PkCommonInfo.PkExtraTimeRule,
		ExtraLeftTime:   resp.PkCommonInfo.ExtraLeftTime,
		IsOpenExtraTime: resp.PkCommonInfo.IsOpenExtraTime,
	}

	if resp.PkCommonInfo.FirstKillCid != 0 && resp.PkCommonInfo.FirstKillUid != 0 {
		user, ok := users[resp.GetPkCommonInfo().GetFirstKillUid()]
		if ok {
			resUid, account, nickName, userProfile, _ := getUserAccountInfoFromUidInfo(user, resp.GetPkCommonInfo().GetFirstKillUwkinfo())
			out.PkInfo.CommonInfo.FirstKillUser = &pb.SendGiftUserInfo{
				Uid:         resUid,
				Account:     account,
				Nickname:    nickName,
				Sex:         uint32(user.GetSex()),
				FirstKill:   true,
				ChannelId:   resp.PkCommonInfo.FirstKillCid,
				UserProfile: userProfile,
			}
		}
	}

	log.DebugWithCtx(ctx, "GetPkInfo in:%v out:%v", in, out)

	return out, nil
}

func (s *ChannelLiveLogic_) GetChannelLiveRankUser(ctx context.Context, in *pb.GetChannelLiveRankUserReq) (*pb.GetChannelLiveRankUserResp, error) {
	out := &pb.GetChannelLiveRankUserResp{UserList: make([]*pb.SendGiftUserInfo, 0)}

	liveInfo, err := s.channelLiveMgrCli.GetChannelLiveInfo(ctx, in.Uid, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveRankUser GetChannelLiveInfo err:%v", err)
		return out, err
	}

	rankResp, err := s.channelLiveMgrCli.GetChannelLiveRankUser(ctx, serPb.GetChannelLiveRankUserReq{
		ChannelId: liveInfo.ChannelLiveInfo.ChannelId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveRankUser GetChannelLiveRankUser err:%v", err)
		return out, err
	}

	uidList := make([]uint32, 0)
	for _, rank := range rankResp.UserList {
		uidList = append(uidList, rank.Uid)
	}

	subCtx, cancel := context.WithTimeout(ctx, time.Millisecond*1000)
	defer cancel()

	mapUser, err := s.accountClient.GetUsersMap(subCtx, uidList)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetChannelLiveRankUser GetUsersMap sz:%v err:%v", len(uidList), err)
		return out, err
	}

	//非必要信息降级处理
	numResult, err := s.numericClient.BatchGetPersonalNumeric(subCtx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveRankUser BatchGetPersonalNumeric sz:%v err:%v", len(uidList), err)
	}

	nobResult, err := s.nobilityClient.BatchGetNobilityInfo(subCtx, in.Uid, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveRankUser BatchGetNobilityInfo sz:%v err:%v", len(uidList), err)
	}

	channelVipResult, err := s.memberRank.BatGetUserConsumeInfo(subCtx, in.Uid, liveInfo.ChannelLiveInfo.ChannelId, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveRankUser BatGetUserConsumeInfo sz:%v err:%v", len(uidList), err)
	}

	fansResult, err := s.fansCli.BatchGetFansInfo(subCtx, in.Uid, uidList)
	if nil != err {
		log.ErrorWithCtx(ctx, "GetChannelLiveRankUser BatchGetFansInfo sz:%v err:%v", len(uidList), err)
	}

	for _, rank := range rankResp.UserList {

		user, ok := mapUser[rank.Uid]
		if !ok {
			continue
		}

		var rich, charm uint64 = 0, 0
		var nobLv, channelLv uint32 = 0, 0
		if res, ok := numResult[rank.Uid]; ok {
			rich = res.Rich64 / 1000
			charm = res.Charm64 / 1000
		}

		nobilityInfo := &NobilitySvr.NobilityInfo{}
		if res, ok := nobResult[rank.Uid]; ok {
			nobLv = res.Level
			nobilityInfo = res
		}

		var fLevel uint32 = 0
		gName := ""
		var pInfo *app.FansPlateInfo = nil
		if fansInfo, ok := fansResult[rank.Uid]; ok {
			fLevel = fansInfo.GetFansLevel()
			gName = fansInfo.GetGroupName()
			pInfo = s.FillPlateConfigMsg(fansInfo.GetPlateInfo())
		}

		if res, ok := channelVipResult[rank.Uid]; ok {
			channelLv = res.CurrLevelId
		}

		rankInfo := &pb.SendGiftUserInfo{
			Uid:             rank.Uid,
			Account:         user.Username,
			Score:           rank.Score,
			Rich:            uint32(rich),
			Charm:           uint32(charm),
			NobilityLevel:   nobLv,
			Nickname:        user.Nickname,
			GroupFansLevel:  fLevel,
			ChannelMemLevel: channelLv,
			GroupName:       gName,
			PlateInfo:       pInfo,
			NobilityInfo: &app.NobilityInfo{
				Level:  nobilityInfo.GetLevel(),
				FLevel: nobilityInfo.GetFLevel(),
			},
		}
		out.UserList = append(out.UserList, rankInfo)
	}

	log.DebugWithCtx(ctx, "GetChannelLiveRankUser in:%v out:%v", in, out)

	return out, nil
}

func (s *ChannelLiveLogic_) GetChannelLiveWatchTimeRankUser(ctx context.Context, in *pb.GetChannelLiveWatchTimeRankUserReq) (*pb.GetChannelLiveWatchTimeRankUserResp, error) {
	out := &pb.GetChannelLiveWatchTimeRankUserResp{
		UserList: make([]*pb.WatchUserInfo, 0),
	}

	log.DebugWithCtx(ctx, "GetChannelLiveWatchTimeRankUser req:%v", in)

	chLiveInfo, err := s.channelLiveMgrCli.GetChannelLiveInfo(ctx, in.Uid, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveInfo err:%v", err)
		return out, err
	}

	resp, err := s.channelLiveMgrCli.GetChannelLiveWatchTimeRankUser(ctx, serPb.GetChannelLiveWatchTimeRankUserReq{
		ChannelId: chLiveInfo.ChannelLiveInfo.ChannelId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveWatchTimeRankUser err:%v", err)
		return out, err
	}

	log.DebugWithCtx(ctx, "GetChannelLiveWatchTimeRankUser GetChannelLiveWatchTimeRankUser resp:%v", resp)

	uidList := make([]uint32, 0)
	for _, rank := range resp.UserList {
		uidList = append(uidList, rank.Uid)
	}

	mapUser, err := s.accountClient.GetUsersMap(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveWatchTimeRankUser GetUsersMap sz:%v err:%v", len(uidList), err)
		return out, err
	}

	subCtx, cancel := context.WithTimeout(ctx, time.Millisecond*800)
	defer cancel()

	numResult, err := s.numericClient.BatchGetPersonalNumeric(subCtx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveWatchTimeRankUser BatchGetPersonalNumeric err:%v", err)
	}

	nobResult, err := s.nobilityClient.BatchGetNobilityInfo(subCtx, in.Uid, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveWatchTimeRankUser BatchGetNobilityInfo err:%v", err)
	}

	fansResult, err := s.fansCli.BatchGetFansInfo(subCtx, in.Uid, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveWatchTimeRankUser BatchGetFansInfo in:%v err:%v", in, err)
	}

	channelMemResult, merr := s.memberRank.BatGetUserConsumeInfo(subCtx, in.Uid, chLiveInfo.ChannelLiveInfo.ChannelId, uidList)
	if merr != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveWatchTimeRankUser BatGetUserConsumeInfo err:%v", merr)
	}

	for _, rank := range resp.UserList {

		user, ok := mapUser[rank.Uid]

		if !ok {
			continue
		}

		var rich, charm uint64 = 0, 0
		var nobLv, channelLv uint32 = 0, 0
		if res, ok := numResult[rank.Uid]; ok {
			rich = res.Rich64 / 1000
			charm = res.Charm64 / 1000
		}

		nobilityInfo := &NobilitySvr.NobilityInfo{}
		if res, ok := nobResult[rank.Uid]; ok {
			nobLv = res.Level
			nobilityInfo = res
		}

		var fLevel uint32 = 0
		gName := ""
		var pInfo *app.FansPlateInfo = nil
		if fanInfo, ok := fansResult[rank.Uid]; ok {
			fLevel = fanInfo.GetFansLevel()
			gName = fanInfo.GetGroupName()
			pInfo = s.FillPlateConfigMsg(fanInfo.GetPlateInfo())
		}

		if res, ok := channelMemResult[rank.Uid]; ok {
			channelLv = res.CurrLevelId
		}

		rankInfo := &pb.WatchUserInfo{
			Uid:             rank.Uid,
			Account:         user.Username,
			Score:           rank.Score,
			Rich:            uint32(rich),
			Charm:           uint32(charm),
			NobilityLevel:   nobLv,
			Nickname:        user.Nickname,
			GroupFansLevel:  fLevel,
			ChannelMemLevel: channelLv,
			GroupName:       gName,
			PlateInfo:       pInfo,
			NobilityInfo: &app.NobilityInfo{
				Level:  nobilityInfo.GetLevel(),
				FLevel: nobilityInfo.GetFLevel(),
			},
		}

		out.UserList = append(out.UserList, rankInfo)
	}

	log.DebugWithCtx(ctx, "GetChannelLiveWatchTimeRankUser in:%v out:%v", in, out)

	return out, nil
}

func (s *ChannelLiveLogic_) GetChannelLiveData(ctx context.Context, in *pb.GetChannelLiveDataReq) (*pb.GetChannelLiveDataResp, error) {
	out := &pb.GetChannelLiveDataResp{}

	log.DebugWithCtx(ctx, "GetChannelLiveData req:%v", in)

	resp, err := s.channelLiveMgrCli.GetChannelLiveData(ctx, serPb.GetChannelLiveDataReq{
		ChannelId: in.ChannelId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveData  err:%v", err)
		return out, err
	}

	log.DebugWithCtx(ctx, "GetChannelLiveData resp:%v", resp)

	if in.Uid == 0 {
		chInfoResp, err := s.channelLiveMgrCli.GetChannelLiveInfoByChannelId(ctx, in.ChannelId)
		if err == nil {
			in.Uid = chInfoResp.ChannelLiveInfo.Uid
		}
	}

	resp2, err := s.channelLiveMgrCli.GetChannelLiveStatus(ctx, serPb.GetChannelLiveStatusReq{
		Uid:       in.Uid,
		ChannelId: in.ChannelId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveData GetChannelLiveStatus err:%v", err)
		return out, err
	}

	log.DebugWithCtx(ctx, "GetChannelLiveData GetChannelLiveStatus resp2:%v", resp2)

	if resp2.ChannelLiveInfo == nil {
		return out, errors.New("直播已经结束")
	}

	_, followCnt, err := s.friendShipCli.GetUserCounts(ctx, in.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveData GetUserCounts in:%v err:%v", in, err)
	}

	addFansCnt := followCnt - uint32(resp2.ChannelLiveInfo.ChannelLiveStatus.FansCnt)
	if uint32(resp2.ChannelLiveInfo.ChannelLiveStatus.FansCnt) > followCnt {
		addFansCnt = 0
	}

	fansInfo, err := s.fansCli.GetAnchorFansInfo(ctx, in.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveData GetAnchorFansInfo err:%v", err)
	}

	addGroupFansCnt := fansInfo.GetFansCnt() - uint32(resp2.ChannelLiveInfo.ChannelLiveStatus.GroupFansCnt)

	endTime := uint32(time.Now().Unix())
	liveTime := endTime - resp2.ChannelLiveInfo.ChannelLiveStatus.BeginTime
	if resp2.ChannelLiveInfo.ChannelLiveStatus.BeginTime == 0 || resp2.ChannelLiveInfo.ChannelLiveStatus.BeginTime > endTime {
		liveTime = 0
	}

	out.LiveData = &pb.ChannelLiveData{
		LiveTime:            liveTime,
		AudienceCnt:         resp.LiveData.AudienceCnt,
		GiftValue:           resp.LiveData.LiveGiftValue,
		SendGiftAudienceCnt: resp.LiveData.SendGiftAudienceCnt,
		AddFans:             addFansCnt,
		AddGroupFans:        addGroupFansCnt,
		AnchorGiftValue:     resp.GetLiveData().GetAnchorGiftValue() + resp.GetLiveData().GetKnightValue() + resp.GetLiveData().GetGameFee(),
	}

	log.DebugWithCtx(ctx, "GetChannelLiveData in:%v out:%v", in, out)

	return out, nil
}

func (s *ChannelLiveLogic_) SetChannelLiveOpponentMicFlag(ctx context.Context, req *pb.SetChannelLiveOpponentMicFlagReq) (*pb.SetChannelLiveOpponentMicFlagResp, error) {
	out := &pb.SetChannelLiveOpponentMicFlagResp{}

	log.DebugWithCtx(ctx, "SetChannelLiveOpponentMicFlagL req:%v", req)

	resp, serr := s.channelLiveMgrCli.SetChannelLiveOpponentMicFlag(ctx, req.ChannelId, serPb.ChannelLiveOpponentMicFlag(req.OptMicFlag))
	if serr != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveOpponentMicFlagLX serr:%v", serr)
		return out, serr
	}

	//把状态推送到房间
	micPushMsg := &pb.ChannelLiveOpponentMicFlagPushMsg{
		OptMicFlag:        req.OptMicFlag,
		ChannelId:         req.ChannelId,
		BeBanMicChannelId: resp.TargetChannelId,
	}

	micPushMsgBin, merr := proto.Marshal(micPushMsg)
	if merr != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveOpponentMicFlag Marshal err:%v", merr)
		return out, merr
	}

	s.PushChannelMsg(ctx, micPushMsgBin, uint32(channel.ChannelMsgType_CHANNEL_LIVE_OPT_MIC_FLAG_MSG), []uint32{req.ChannelId, resp.TargetChannelId})

	log.DebugWithCtx(ctx, "SetChannelLiveOpponentMicFlagL out:%v", out)

	return out, nil
}

func (s *ChannelLiveLogic_) StartPkMatch(ctx context.Context, req *pb.StartPkMatchReq) (*pb.StartPkMatchResp, error) {
	out := &pb.StartPkMatchResp{}

	log.DebugWithCtx(ctx, "StartPkMatch begin req:%v", req)

	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID

	if !checkClientVersion(ctx) {
		return out, protocol.NewServerError(status.ErrChannelLiveNotPkAuth, "请更新至最新版进行PK")
	}

	if conf.IsBanUidPk(uid) { //
		log.ErrorWithCtx(ctx, "StartPkMatch ban pk uid:%d req:%v", uid, req)
		return out, errors.New("StartPkMatch IsBanUidPk")
	}
	blackResp, err := s.channelLivePkCli.BatchCheckIfInBlacklist(ctx, &channellivepkPB.BatchCheckIfInBlacklistReq{
		UidList:   []uint32{uid},
		BlackType: channellivepkPB.BlacklistType_BLACKLIST_TYPE_PK,
	})
	if err != nil {
		log.WarnWithCtx(ctx, "StartPkMatch BatchCheckIfInBlacklist uid:%d err:%v", uid, err)
	} else {
		if blackResp.GetBlackMap()[uid] {
			log.ErrorWithCtx(ctx, "StartPkMatch in blacklist uid:%d req:%v", uid, req)
			return out, protocol.NewExactServerError(codes.OK, status.ErrChannelLiveNotPkAuth, "当前帐号不支持开启PK哦")
		}
	}

	liveResp, sErr := s.channelLiveMgrCli.GetChannelLiveInfo(ctx, uid, false)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "StartPkMatch GetChannelLiveInfo uid:%d req:%v sErr:%v", uid, req, sErr)
		return out, sErr
	}

	log.DebugWithCtx(ctx, "StartPkMatch uid:%d req:%v liveResp:%v", uid, req, liveResp)

	if liveResp.GetChannelLiveInfo().GetChannelId() == 0 {
		log.ErrorWithCtx(ctx, "StartPkMatch no live permission uid:%d req:%v liveResp:%v", uid, req, liveResp)
		return out, protocol.NewServerError(status.ErrChannelLiveNotAuthority)
	}

	// 检查官频是否即将排班
	err = s.relayCheck(ctx, liveResp.GetChannelLiveInfo().GetChannelId(), 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartPkMatch relayCheck uid:%d req:%v liveResp:%v err:%v", uid, req, liveResp, err)
		return out, err
	}

	// 检查是否在进行蒙面pk
	if checkMaskPkErr := s.checkIfInMaskedPk(ctx, uid, liveResp.GetChannelLiveInfo().GetChannelId(), 0); checkMaskPkErr != nil {
		log.ErrorWithCtx(ctx, "StartPkMatch checkIfInMaskedPk uid:%d req:%v liveResp:%v err:%v", uid, req, liveResp, checkMaskPkErr)
		return out, checkMaskPkErr
	}

	gameResp, err := s.revenueExtGameCli.GetMountExtGame(ctx, &revenueExtGamePb.GetMountExtGameReq{ChannelId: liveResp.GetChannelLiveInfo().GetChannelId()})
	if nil != err {
		log.ErrorWithCtx(ctx, "StartPkMatch GetMountExtGame in:%v err:%v", req, err)
		return out, err
	}
	if gameResp.GetGameType() > 0 {
		return out, protocol.NewServerError(status.ErrChannelLivePkAnchorGameLimit)
	}

	_, sErr = s.channelLiveMgrCli.StartPkMatch(ctx, uid, liveResp.GetChannelLiveInfo().GetChannelId(), serPb.ChannelLivePKMatchType(req.MatchType))
	if sErr != nil {
		log.ErrorWithCtx(ctx, "StartPkMatch uid:%d req:%v sErr:%v", uid, req, sErr)
		return out, sErr
	}

	log.DebugWithCtx(ctx, "StartPkMatch uid:%d req:%v out:%v liveResp:%v", uid, req, out, liveResp)

	return out, nil
}

func (s *ChannelLiveLogic_) GetPKMatchInfo(ctx context.Context, req *pb.GetPKMatchInfoReq) (*pb.GetPKMatchInfoResp, error) {
	out := &pb.GetPKMatchInfoResp{}

	log.DebugWithCtx(ctx, "GetPKMatchInfo in:%v", req)

	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID

	resp, err := s.channelLiveMgrCli.GetPKMatchInfo(ctx, uid, req.ChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetPKMatchInfo err:%v req:%v", err, req)
		return out, err
	}

	out.PkMatchTy = pb.ChannelLivePKMatchType(resp.PkMatchTy)
	out.CptInfo = &pb.PkCompetitionInfo{
		Level:   resp.CptInfo.Level,
		MyLevel: resp.CptInfo.LevelName,
	}
	out.PkLimitInfo = &pb.PkLimitInfo{
		TimeRange: resp.PkLimitInfo.TimeRange,
		LimitCnt:  resp.PkLimitInfo.LimitCnt,
	}

	log.DebugWithCtx(ctx, "GetPKMatchInfo out:%v", out)
	return out, nil
}

func (s *ChannelLiveLogic_) CancelPkMatch(ctx context.Context, req *pb.CancelPkMatchReq) (*pb.CancelPkMatchResp, error) {
	out := &pb.CancelPkMatchResp{}

	log.DebugWithCtx(ctx, "CancelPkMatchL req:%v", req)

	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID

	_, serr := s.channelLiveMgrCli.CancelPkMatch(ctx, uid, req.ChannelId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "CancelPkMatchLX serr:%v", serr)
		return out, nil
	}

	log.DebugWithCtx(ctx, "CancelPkMatchL out:%v", out)

	return out, nil
}

func (s *ChannelLiveLogic_) ChannelLiveReport(ctx context.Context, req *pb.ChannelLiveReportReq) (*pb.ChannelLiveReportResp, error) {
	out := &pb.ChannelLiveReportResp{}

	log.DebugWithCtx(ctx, "ChannelLiveReport req:%+v", req)

	_, err := s.chLivePushCli.ChannelLiveReport(ctx, &clPush.ChannelLiveReportReq{
		ReportType:  req.ReportType,
		ChannelId:   req.ChannelId,
		AnchorUid:   req.AnchorUid,
		AudienceUid: req.AudienceUid,
	})

	if nil != err {
		log.ErrorWithCtx(ctx, "ChannelLiveReport req:%+v err:%v", req, err)
		return out, err
	}

	return out, nil
}

func (s *ChannelLiveLogic_) AcceptAppointPk(ctx context.Context, req *pb.AcceptAppointPkReq) (*pb.AcceptAppointPkResp, error) {
	out := &pb.AcceptAppointPkResp{}

	log.DebugWithCtx(ctx, "AcceptAppointPk begin req:%v", req)

	_, err := s.channelLiveMgrCli.AcceptAppointPk(ctx, &serPb.AcceptAppointPkReq{
		MyUid:     req.GetMyUid(),
		OtherUid:  req.GetOtherUid(),
		ChannelId: req.GetChannelId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "AcceptAppointPk failed req:%v err:%v", req, err)
		return out, err
	}

	log.DebugWithCtx(ctx, "AcceptAppointPk end req:%v out:%v", req, out)
	return out, nil
}

func (s *ChannelLiveLogic_) ConfirmAppointPkPush(ctx context.Context, req *pb.ConfirmAppointPkPushReq) (*pb.ConfirmAppointPkPushResp, error) {
	out := &pb.ConfirmAppointPkPushResp{}

	log.DebugWithCtx(ctx, "ConfirmAppointPkPush begin req:%v", req)

	_, err := s.channelLiveMgrCli.ConfirmAppointPkPush(ctx, &serPb.ConfirmAppointPkPushReq{
		MyUid:    req.GetMyUid(),
		OtherUid: req.GetOtherUid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "ConfirmAppointPkPush failed req:%v err:%v", req, err)
		return out, err
	}

	log.DebugWithCtx(ctx, "ConfirmAppointPkPush end req:%v out:%v", req, out)
	return out, nil
}

func (s *ChannelLiveLogic_) GetAppointPkInfo(ctx context.Context, req *pb.GetAppointPkInfoReq) (*pb.GetAppointPkInfoResp, error) {
	out := &pb.GetAppointPkInfoResp{
		PushEventInfo: &pb.AppointPkEvent{},
	}

	log.DebugWithCtx(ctx, "GetAppointPkInfo begin req:%v", req)

	infoResp, err := s.channelLiveMgrCli.GetAppointPkInfo(ctx, &serPb.GetAppointPkInfoReq{
		Uid: req.GetUid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAppointPkInfo failed req:%v err:%v", req, err)
		return out, err
	}

	// 进房查询指定pk推送信息，只需要返回应战和等待对方应战事件
	if infoResp.GetPushEventInfo().GetEventType() == uint32(pb.AppointPkEventType_AppointPkInvite) ||
		infoResp.GetPushEventInfo().GetEventType() == uint32(pb.AppointPkEventType_AppointPkWaiting) {
		out.PushEventInfo = &pb.AppointPkEvent{
			EventType:        infoResp.GetPushEventInfo().GetEventType(),
			MyUid:            infoResp.GetPushEventInfo().GetMyUid(),
			OtherUid:         infoResp.GetPushEventInfo().GetOtherUid(),
			OtherAccount:     infoResp.GetPushEventInfo().GetOtherAccount(),
			OtherNickname:    infoResp.GetPushEventInfo().GetOtherNickname(),
			CountDownEndTime: infoResp.GetPushEventInfo().GetCountDownEndTime(),
			OtherSex:         infoResp.GetPushEventInfo().GetOtherSex(),
			CountDownTime:    infoResp.GetPushEventInfo().GetCountDownTime(),
		}
	}

	log.DebugWithCtx(ctx, "GetAppointPkInfo end req:%v out:%v", req, out)

	return out, nil
}

func getMultiPkInfoUids(multiPkInfoResp *channel_live_pk.GetMultiPkInfoResp) (allUids, anchorUids []uint32) {
	multiPkInfo := multiPkInfoResp.GetPkInfo()
	allUids = []uint32{multiPkInfo.GetFirstKillUser().GetUid()}
	anchorUids = []uint32{}
	for _, info := range multiPkInfo.GetRoomInfoList() {
		allUids = append(allUids, info.GetAnchorInfo().GetUid())
		anchorUids = append(anchorUids, info.GetAnchorInfo().GetUid())
		for _, user := range info.GetTopUserList() {
			allUids = append(allUids, user.GetUid())
		}
		for _, info := range multiPkInfoResp.GetKnightList() {
			for _, knight := range info.GetKnightList() {
				allUids = append(allUids, knight.GetUid())
			}
		}
	}
	return
}

func (s *ChannelLiveLogic_) GetLiveIntimatePresentInfo(ctx context.Context, channelId uint32, pkChannelList []uint32) *time_present2.LiveIntimatePresentInfo {
	logicResp := &time_present2.LiveIntimatePresentInfo{}

	resp, err := s.timePresentCli.GetChannelLiveIntimatePresentList(ctx, &time_present.GetChannelLiveIntimatePresentListReq{
		ChannelId:       channelId,
		PkChannelIdList: pkChannelList,
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveIntimatePresentList failed channelId:%v err:%v", channelId, err)
		return logicResp
	}

	log.DebugWithCtx(ctx, "GetChannelLiveIntimatePresentList resp:%v", resp)

	logicResp.LastUpdateTime = resp.GetLiveIntimatePresentInfo().GetLastUpdateTime()
	logicResp.TriggerChannelId = resp.GetLiveIntimatePresentInfo().GetTriggerChannelId()

	for _, item := range resp.GetLiveIntimatePresentInfo().GetChannelInfo() {
		fromUserProfile := &app.UserProfile{}
		err := proto.Unmarshal(item.FromUser, fromUserProfile)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendLiveIntimatePresentListUpdatePush err , content %+v , err %+v", item, err)
		}

		toUserProfile := &app.UserProfile{}
		err = proto.Unmarshal(item.ToUser, toUserProfile)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendLiveIntimatePresentListUpdatePush err , content %+v , err %+v", item, err)
		}

		logicItem := &time_present2.LiveIntimatePresentChannelInfo{
			ChannelId:      item.GetChannelId(),
			PresentId:      item.GetPresentId(),
			FromUser:       fromUserProfile,
			ToUser:         toUserProfile,
			LastChangeTime: item.GetLastChangeTime(),
		}

		logicResp.ChannelInfo = append(logicResp.ChannelInfo, logicItem)
	}

	return logicResp
}
