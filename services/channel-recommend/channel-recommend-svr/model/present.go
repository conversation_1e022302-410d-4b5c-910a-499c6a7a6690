package model

import (
	"context"
	"golang.52tt.com/pkg/active_present/client"
	"golang.52tt.com/pkg/log"
	userpresent "golang.52tt.com/protocol/services/userpresent-go"
	"sync"
	"time"
)

type PresentMemCache struct {
	presentMap     map[uint32]*userpresent.StPresentItemConfig
	lastUpdateTime uint32
	rwMutex        sync.RWMutex

	ticker *time.Ticker
	done   chan bool
}

func (s *PresentMemCache) GetConfigById(itemId uint32) *userpresent.StPresentItemConfig {
	s.rwMutex.RLock()
	defer s.rwMutex.RUnlock()

	if s.presentMap == nil || s.presentMap[itemId] == nil {
		return nil
	}

	return s.presentMap[itemId]
}

func (s *PresentMemCache) GetConfigByIdList(itemIdList []uint32) map[uint32]*userpresent.StPresentItemConfig {
	s.rwMutex.RLock()
	defer s.rwMutex.RUnlock()

	resp := make(map[uint32]*userpresent.StPresentItemConfig)

	for _, item := range itemIdList {
		if s.presentMap != nil && s.presentMap[item] != nil {
			resp[item] = s.presentMap[item]
		}
	}

	return resp
}

func (s *PresentMemCache) GetConfigList() (uint32, map[uint32]*userpresent.StPresentItemConfig) {
	s.rwMutex.RLock()
	defer s.rwMutex.RUnlock()

	if s.presentMap == nil {
		return 0, nil
	}

	log.DebugWithCtx(context.Background(), "GetConfigList len:%d", len(s.presentMap))

	return s.lastUpdateTime, s.presentMap
}

func (s *PresentMemCache) GetConfigUpdateTime() uint32 {
	s.rwMutex.RLock()
	defer s.rwMutex.RUnlock()

	log.DebugWithCtx(context.Background(), "GetConfigUpdateTime len:%d", len(s.presentMap))

	return s.lastUpdateTime
}

func (s *PresentMemCache) UpdateConfig() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	updateTime, err := client.PresentCli.GetPresentConfigUpdateTime(ctx, &userpresent.GetPresentConfigUpdateTimeReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "PresentMemCache GetPresentConfigUpdateTime err , err is %v", err)
		return
	}

	// 没有更新就不用管
	if updateTime.GetUpdateTime() <= s.lastUpdateTime {
		log.InfoWithCtx(ctx, "PresentMemCache get UpdateTime %d , lastUpdateTime %d , no update", updateTime.GetUpdateTime(), s.lastUpdateTime)
		return
	}

	resp, sErr := client.PresentCli.GetPresentConfigListV2ByUpdateTime(ctx, updateTime.GetUpdateTime())
	if sErr != nil {
		log.ErrorWithCtx(ctx, "PresentMemCache UpdateConfig err , err is %v", sErr)
		return
	}

	// 礼物不可能越更新越少，说明这里拿到的礼物配置有问题; 同时再校验updateTime
	if len(s.presentMap) > len(resp.GetItemList()) || resp.GetUpdateTime() <= s.lastUpdateTime {
		log.ErrorWithCtx(ctx, "PresentMemCache new Cfg len %d , new update time %d ;"+
			"old Cfg len %d , old update time %d . config not update", len(resp.GetItemList()), resp.GetUpdateTime(),
			len(s.presentMap), s.lastUpdateTime)
		return
	}

	s.rwMutex.Lock()
	defer s.rwMutex.Unlock()

	s.lastUpdateTime = resp.GetUpdateTime()
	s.presentMap = make(map[uint32]*userpresent.StPresentItemConfig)
	for _, item := range resp.GetItemList() {
		s.presentMap[item.GetItemId()] = item
	}

	log.InfoWithCtx(context.Background(), "UpdateConfig len: %d", len(s.presentMap))
}

func NewPresentMemCache() *PresentMemCache {
	s := &PresentMemCache{}
	_ = client.Setup()
	s.UpdateConfig()
	s.ticker = time.NewTicker(time.Second * 5)
	go s.ConfigUpdateTicker()
	return s
}

func (s *PresentMemCache) ConfigUpdateTicker() {
	defer s.ticker.Stop()

	for range s.ticker.C {
		select {
		case <-s.done:
			return
		default:
			s.UpdateConfig()
		}
	}
}

func (s *PresentMemCache) Stop() {
	s.done <- true
}
