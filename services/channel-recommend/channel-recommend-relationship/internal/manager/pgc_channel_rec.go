package manager

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	pb "golang.52tt.com/protocol/services/channel-recommend-relationship"
	"golang.52tt.com/services/channel-recommend/channel-recommend-relationship/internal/rpc"
	"math/rand" //#nosec
	"time"
)

func (m *Manager) GetUserSex(ctx context.Context, uid uint32) int32 {
	subCtx, cancel := context.WithTimeout(ctx, time.Millisecond*time.Duration(m.dyconf.GetRelationshipConf().GetSexTimeout))
	defer cancel()
	userInfo, err := rpc.AccountCli.GetUser(subCtx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserSex GetUser failed, uid:%d, err:%v", uid, err)
		return 0
	}
	return userInfo.GetSex()
}

func (m *Manager) GetDateTag(tm time.Time, tagId uint32) string {
	return fmt.Sprintf("%04d%02d%02d_%d", tm.Year(), tm.Month(), tm.Day(), tagId)
}

func (m *Manager) GetChannelTitle(tm time.Time, tagId, cid uint32) string {
	m.tagTitleLocker.RLock()
	defer m.tagTitleLocker.RUnlock()

	dateTag := m.GetDateTag(tm, tagId)
	title := ""
	if _, ok := m.mapDateTag2Title[dateTag]; ok {
		title = m.mapDateTag2Title[dateTag][cid]
	}

	log.Debugf("GetChannelTitle tm:%v tagId:%d cid:%d title:%s", tm, tagId, cid, title)
	return title
}

// GetUGCHomeRecommendChannel UGC首页-PGC房推荐逻辑
func (m *Manager) GetUGCHomeRecommendChannel(ctx context.Context, uid, location, num uint32) ([]*pb.RecommendChannelInfo, error) {
	if !m.dyconf.GetRelationshipConf().IsAllowLocation(location) {
		log.ErrorWithCtx(ctx, "GetUGCHomeRecommendChannel invalid uid:%d, location:%d, num:%d", uid, location, num)
		return nil, nil
	}
	sex := m.GetUserSex(ctx, uid)
	switch pb.RecommendChannelLocation(location) {
	case pb.RecommendChannelLocation_RECOMMEND_CHANNEL_LOCATION_HOME:
		return m.recommendChannelOnUGCHome(ctx, uid, num, sex)
	case pb.RecommendChannelLocation_RECOMMEND_CHANNEL_LOCATION_TAB_WANG_ZHE:
		return m.recommendChannelOnUGCWangZheTab(ctx, uid, num, sex)
	default:
		log.ErrorWithCtx(ctx, "GetUGCHomeRecommendChannel invalid uid:%d, location:%d, num:%d", uid, location, num)
	}
	return nil, nil
}

// recommendChannelOnUGCHome UGC首页推荐
func (m *Manager) recommendChannelOnUGCHome(ctx context.Context, uid, num uint32, sex int32) ([]*pb.RecommendChannelInfo, error) {
	recChannelInfos := make([]*pb.RecommendChannelInfo, 0)
	now := time.Now()

	//检查是否可以推荐PGC房间
	if can, err := m.cache.CheckCanPgcForUgcHome(ctx, uid); !can {
		log.ErrorWithCtx(ctx, "recommendChannelOnUGCHome CheckCanPgcForUgcHome failed, uid:%d, err: %v", uid, err)
		return recChannelInfos, nil
	}

	//关系链房改到智能平台推荐
	//获取关系链房间
	//relationshipCids, err := m.cache.GetRelationshipChannelUidsByType(ctx, uid, uint32(channel_recommend_svr.RecommendRelationshipType_RECOMMMENT_RELATIONSHIP_TYPE_FRIEND_MIC))
	//if err != nil {
	//	log.ErrorWithCtx(ctx, "recommendChannelOnUGCHome GetRelationshipChannelUidsByType failed, uid:%d, err: %+v", uid, err)
	//	return nil, err
	//}
	location := uint32(pb.RecommendChannelLocation_RECOMMEND_CHANNEL_LOCATION_HOME)
	//获取过滤列表
	filterCids, err := m.cache.GetPgcFilterChannels(ctx, location, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "recommendChannelOnUGCHome GetPgcFilterChannels failed, uid:%d, err: %+v", uid, err)
		return nil, err
	}
	if filterCids == nil {
		filterCids = make(map[uint32]struct{})
	}

	nowTm := time.Now()
	cids := make([]uint32, 0, num)

	//兜底
	for i := uint32(0); i < num; i++ {
		// 从PGC推荐池中选取
		findCid, tagId := m.recommendPGCFromPool(location, sex, filterCids)
		if findCid > 0 {
			recChannelInfos = append(recChannelInfos, &pb.RecommendChannelInfo{
				ChannelId:       findCid,
				RecommendReason: uint32(pb.RecommendReason_RECOMMEND_REASON_REC_POOL),
				TagId:           tagId,
				ChannelTitle:    m.GetChannelTitle(nowTm, 0, findCid),
			})
			cids = append(cids, findCid)
			filterCids[findCid] = struct{}{}
			continue
		}
	}
	if len(cids) > 0 {
		_ = m.cache.AddPgcFilterChannels(ctx, location, uid, cids)
	} else {
		//全部刷新完，清掉filterCids。重新开始
		needDelNum := len(filterCids)
		filterCids = make(map[uint32]struct{})
		for i := uint32(0); i < num; i++ {
			findCid, tagId := m.recommendPGCFromPool(location, sex, filterCids)
			if findCid > 0 {
				recChannelInfos = append(recChannelInfos, &pb.RecommendChannelInfo{
					ChannelId:       findCid,
					RecommendReason: uint32(pb.RecommendReason_RECOMMEND_REASON_REC_POOL),
					TagId:           tagId,
					ChannelTitle:    m.GetChannelTitle(nowTm, 0, findCid),
				})
				cids = append(cids, findCid)
				filterCids[findCid] = struct{}{}
			}
		}
		if needDelNum > 0 {
			_ = m.cache.DelPgcFilterChannels(ctx, location, uid)
		}
		if len(cids) > 0 {
			_ = m.cache.AddPgcFilterChannels(ctx, location, uid, cids)
		}
		log.InfoWithCtx(ctx, "recommendChannelOnUGCHome empty, uid:%d, num:%d,needDelNum：%d, cids:%v", uid, num, needDelNum, cids)
	}
	log.InfoWithCtx(ctx, "recommendChannelOnUGCHome uid:%d, num:%d, sex:%v, cost:%v, recChannelInfos:%+v", uid, num, sex, time.Since(now), recChannelInfos)
	return recChannelInfos, nil
}

func (m *Manager) GetPGCRecForUGCByTagId(location, tagId uint32, sex int32) []uint32 {
	cids := make([]uint32, 0)
	m.pgcRecPoolsLocker.RLock()
	tagChannels, ok := m.pgcRecPoolsForUgc[location]
	if !ok {
		m.pgcRecPoolsLocker.RUnlock()
		return cids
	}
	sexInfos, ok := tagChannels[tagId]
	if !ok {
		m.pgcRecPoolsLocker.RUnlock()
		return cids
	}

	infos, ok := sexInfos[sex]
	if !ok {
		m.pgcRecPoolsLocker.RUnlock()
		return cids
	}
	m.pgcRecPoolsLocker.RUnlock()

	for _, info := range infos {
		cids = append(cids, info.Cid)
	}
	return cids
}

// recommendPGCFromPool 从PGC推荐池中选取
func (m *Manager) recommendPGCFromPool(location uint32, sex int32, filterCids map[uint32]struct{}) (uint32, uint32) {
	rate := rand.Uint32() % 1000
	allWeights := make(map[uint32]*pb.RecommendPoolTagWeight)
	m.pgcRecWeightsLocker.RLock()
	allWeights = m.pgcRecWeightsForUgc
	m.pgcRecWeightsLocker.RUnlock()
	sum := uint32(0)
	// 先按配置的概率选取
	for _, weight := range allWeights {
		sum += weight.Weight
		if rate < sum {
			cids := m.GetPGCRecForUGCByTagId(location, weight.TagId, sex)
			if len(cids) == 0 {
				continue
			}
			for i := 0; i < len(cids); i++ {
				cid := cids[i]
				if _, ok := filterCids[cid]; ok {
					continue
				}
				return cid, weight.TagId
			}
		}
	}

	allTagRecs := make(map[uint32][]*RecChannelInfo)
	allTags := make([]uint32, 0)
	m.pgcRecPoolsLocker.RLock()
	if tagChannels, ok := m.pgcRecPoolsForUgc[location]; ok {
		for tagId, sexInfos := range tagChannels {
			cids, ok := sexInfos[sex]
			if ok && len(cids) > 0 {
				allTagRecs[tagId] = cids
				allTags = append(allTags, tagId)
			}
		}
	}
	m.pgcRecPoolsLocker.RUnlock()

	if len(allTags) == 0 {
		return 0, 0
	}

	// 先从未配置品类概率中的品类中随机
	noWeightTags := make([]uint32, 0)
	for tagId := range allTagRecs {
		if _, ok := allWeights[tagId]; ok {
			continue
		}
		noWeightTags = append(noWeightTags, tagId)
	}
	if len(noWeightTags) > 0 {
		ridx := rand.Int() % len(noWeightTags)
		tagId := noWeightTags[ridx]
		cidInfos := allTagRecs[tagId]
		for i := 0; i < len(cidInfos); i++ {
			info := cidInfos[i]
			if _, ok := filterCids[info.Cid]; ok {
				continue
			}
			return info.Cid, tagId
		}
	}

	// 从所有品类中随机
	ridx := rand.Int() % len(allTags)
	for i := 0; i < len(allTags); i++ {
		tagId := allTags[(ridx+i)%len(allTags)]
		cidInfos := allTagRecs[tagId]
		if len(cidInfos) == 0 {
			continue
		}
		for j := 0; j < len(cidInfos); j++ {
			info := cidInfos[j]
			if _, ok := filterCids[info.Cid]; ok {
				continue
			}
			return info.Cid, tagId
		}
	}

	return 0, 0
}

// recommendChannelOnUGCWangZheTab UGC首页-王者荣耀tab推荐
func (m *Manager) recommendChannelOnUGCWangZheTab(ctx context.Context, uid, num uint32, sex int32) ([]*pb.RecommendChannelInfo, error) {
	recChannelInfo := make([]*pb.RecommendChannelInfo, 0)
	now := time.Now()
	location := uint32(pb.RecommendChannelLocation_RECOMMEND_CHANNEL_LOCATION_TAB_WANG_ZHE)
	//获取过滤列表
	filterCids, err := m.cache.GetPgcFilterChannels(ctx, location, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "recommendChannelOnUGCWangZheTab GetPgcFilterChannels failed, uid:%d, err: %+v", uid, err)
		return nil, err
	}
	if filterCids == nil {
		filterCids = make(map[uint32]struct{})
	}

	cid2tag := make(map[uint32]uint32)
	allCids := make([]uint32, 0)
	m.pgcRecPoolsLocker.RLock()
	if tagChannels, ok := m.pgcRecPoolsForUgc[location]; ok {
		for tagId, sexInfos := range tagChannels {
			cids, ok := sexInfos[sex]
			if !ok {
				continue
			}
			for _, info := range cids {
				cid2tag[info.Cid] = tagId
				allCids = append(allCids, info.Cid)
			}
		}
	}
	m.pgcRecPoolsLocker.RUnlock()

	recCids := make([]uint32, 0)

	if len(allCids) == 0 {
		log.ErrorWithCtx(ctx, "recommendChannelOnUGCWangZheTab all empty, uid:%d", uid)
		return nil, nil
	}
	//异性优先选择
	for j := 0; j < len(allCids); j++ {
		cid := allCids[j]
		if _, ok := filterCids[cid]; ok {
			continue
		}
		recChannelInfo = append(recChannelInfo, &pb.RecommendChannelInfo{
			ChannelId:       cid,
			RecommendReason: uint32(pb.RecommendReason_RECOMMEND_REASON_REC_POOL),
			TagId:           cid2tag[cid],
		})
		filterCids[cid] = struct{}{}
		recCids = append(recCids, cid)
		if len(recCids) >= int(num) {
			break
		}
	}

	if len(recCids) > 0 {
		_ = m.cache.AddPgcFilterChannels(ctx, location, uid, recCids)
	} else {
		if len(filterCids) > 0 {
			_ = m.cache.DelPgcFilterChannels(ctx, location, uid)
		}
		for j := 0; j < len(allCids); j++ { //全部刷新完，重新再来
			cid := allCids[j]
			recChannelInfo = append(recChannelInfo, &pb.RecommendChannelInfo{
				ChannelId:       cid,
				RecommendReason: uint32(pb.RecommendReason_RECOMMEND_REASON_REC_POOL),
				TagId:           cid2tag[cid],
			})
			recCids = append(recCids, cid)
			if len(recCids) >= int(num) {
				break
			}
		}
		if len(recCids) > 0 {
			_ = m.cache.AddPgcFilterChannels(ctx, location, uid, recCids)
		}
		log.InfoWithCtx(ctx, "recommendChannelOnUGCWangZheTab empty, uid:%d， cids:%v", uid, recCids)
	}
	log.InfoWithCtx(ctx, "recommendChannelOnUGCWangZheTab uid:%d, num:%d, sex:%v, cost:%v, recChannelInfo:%+v", uid, num, sex, time.Since(now), recChannelInfo)
	return recChannelInfo, nil
}
