// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-dating-game-record/internal/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	channel_dating_game_record "golang.52tt.com/protocol/services/channel-dating-game-record"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// DelDatingGameRecordCache mocks base method.
func (m *MockICache) DelDatingGameRecordCache(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelDatingGameRecordCache", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelDatingGameRecordCache indicates an expected call of DelDatingGameRecordCache.
func (mr *MockICacheMockRecorder) DelDatingGameRecordCache(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelDatingGameRecordCache", reflect.TypeOf((*MockICache)(nil).DelDatingGameRecordCache), arg0, arg1, arg2)
}

// GetDatingGameRecordCache mocks base method.
func (m *MockICache) GetDatingGameRecordCache(arg0 context.Context, arg1, arg2 uint32) (*channel_dating_game_record.GetChannelDatingGameRecordResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDatingGameRecordCache", arg0, arg1, arg2)
	ret0, _ := ret[0].(*channel_dating_game_record.GetChannelDatingGameRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDatingGameRecordCache indicates an expected call of GetDatingGameRecordCache.
func (mr *MockICacheMockRecorder) GetDatingGameRecordCache(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDatingGameRecordCache", reflect.TypeOf((*MockICache)(nil).GetDatingGameRecordCache), arg0, arg1, arg2)
}

// SetDatingGameRecordCache mocks base method.
func (m *MockICache) SetDatingGameRecordCache(arg0 context.Context, arg1, arg2 uint32, arg3 *channel_dating_game_record.GetChannelDatingGameRecordResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDatingGameRecordCache", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDatingGameRecordCache indicates an expected call of SetDatingGameRecordCache.
func (mr *MockICacheMockRecorder) SetDatingGameRecordCache(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDatingGameRecordCache", reflect.TypeOf((*MockICache)(nil).SetDatingGameRecordCache), arg0, arg1, arg2, arg3)
}
