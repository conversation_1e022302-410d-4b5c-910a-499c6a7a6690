package manager

import (
	"context"
	"database/sql"
	"github.com/go-redis/redis"
	"github.com/golang/mock/gomock"
	userPresent "golang.52tt.com/clients/userpresent"
	"golang.52tt.com/pkg/config"
	pb "golang.52tt.com/protocol/services/levelup-present"
	"golang.52tt.com/services/levelup-present/cache"
	"golang.52tt.com/services/levelup-present/mocks"
	"golang.52tt.com/services/levelup-present/model"
	"reflect"
	"testing"
	"time"
)


func TestLevelupPresentMgr_AddLevelupBatch(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)

	itemId := uint32(1)
	batchCount := uint32(1)
	effectUrl := "a"
	effectMd5 := "a"
	effectDesc := "a"
	createTime := time.Now().Unix()
	updateTime := time.Now().Unix()

	cacheMap := make(map[string]string)

	gomock.InOrder(
		mockStore.EXPECT().InsertLevelupBatch(ctx, &pb.LevelupPresentBatchData{
			ItemId:     itemId,
			BatchCount: batchCount,
			EffectUrl:  effectUrl,
			EffectMd5:  effectMd5,
			EffectDesc: effectDesc,
			CreateTime: uint32(createTime),
			UpdateTime: uint32(updateTime),
		}).Return(nil),
		mockStore.EXPECT().GetAllLevelupPresentBatch(ctx).Return(&[]pb.LevelupPresentBatchData{{
			ItemId:     itemId,
			BatchCount: batchCount,
			EffectUrl:  effectUrl,
			EffectMd5:  effectMd5,
			EffectDesc: effectDesc,
			CreateTime: uint32(createTime),
			UpdateTime: uint32(updateTime),
		}}, nil),
		mockCache.EXPECT().GetAllLevelupPresentBatch().Return(cacheMap, nil),
		mockCache.EXPECT().SetMultiLevelupPresentBatch(gomock.Any()).Return(nil),
	)

	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx  context.Context
		data *pb.LevelupPresentBatchData
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// Add test cases.
		{
			name:    "TestLevelupPresentMgr_AddLevelupBatch",
			fields:  fields{
				db:         nil,
				store:      mockStore,
				rc:         nil,
				cache:      mockCache,
				presentCli: nil,
			},
			args:    args{
				ctx:  ctx,
				data: &pb.LevelupPresentBatchData{
					ItemId:     itemId,
					BatchCount: batchCount,
					EffectUrl:  effectUrl,
					EffectMd5:  effectMd5,
					EffectDesc: effectDesc,
					CreateTime: uint32(createTime),
					UpdateTime: uint32(updateTime),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			if err := mgr.AddLevelupBatch(tt.args.ctx, tt.args.data); (err != nil) != tt.wantErr {
				t.Errorf("AddLevelupBatch() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLevelupPresentMgr_AddLevelupChildPresent(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)

	itemId := uint32(1)
	parentItemId := uint32(1)
	level := uint32(1)
	exp := uint32(1)
	createTime := time.Now().Unix()
	updateTime := time.Now().Unix()
	presentType := int32(1)
	currentVersion := uint32(1)
	zipUrl := "a"
	color1 := "a"
	color2 := "a"
	levelupBg := "a"
	levelupNumber := "a"
	levelupLevel := "a"

	cacheMap := make(map[string]string)

	gomock.InOrder(
		mockStore.EXPECT().InsertLevelupChildPresent(ctx, &pb.LevelupChildPresentData{
			ItemId:       itemId,
			ParentItemId: parentItemId,
			Level:        level,
			Exp:          exp,
			CreateTime:   uint32(createTime),
			UpdateTime: uint32(updateTime),
		}).Return(nil),
		mockStore.EXPECT().GetAllLevelupChildrenPresent(ctx).Return(&[]*pb.LevelupChildPresentData{{
			ItemId:       itemId,
			ParentItemId: parentItemId,
			Level:        level,
			Exp:          exp,
			CreateTime:   uint32(createTime),
			UpdateTime: uint32(updateTime),
		}}, nil),
		mockCache.EXPECT().GetAllLevelupChildPresent().Return(cacheMap, nil),
		mockStore.EXPECT().GetAllLevelupParentPresent(ctx).Return(&[]pb.LevelupParentPresentData{{
			ParentItemId:   parentItemId,
			PresentType:    uint32(presentType),
			CurrentVersion: currentVersion,
			ZipUrl:         zipUrl,
			CreateTime:     uint32(createTime),
			UpdateTime: uint32(updateTime),
			Color_1:        color1,
			Color_2:        color2,
			LevelupBg:      levelupBg,
			LevelupNumber:  levelupNumber,
			LevelupLevel:   levelupLevel,
		}}, nil),
		mockCache.EXPECT().GetAllLevelupParentPresent().Return(cacheMap, nil),
		mockCache.EXPECT().SetMultiLevelupChildPresent(gomock.Any()).Return(nil),
		mockCache.EXPECT().SetMultiLevelupParentPresent(gomock.Any()).Return(nil),
	)

	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx  context.Context
		data *pb.LevelupChildPresentData
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// Add test cases.
		{
			name:    "TestLevelupPresentMgr_AddLevelupChildPresent",
			fields:  fields{
				db:         nil,
				store:      mockStore,
				rc:         nil,
				cache:      mockCache,
				presentCli: nil,
			},
			args:    args{
				ctx:  ctx,
				data: &pb.LevelupChildPresentData{
					ItemId:       itemId,
					ParentItemId: parentItemId,
					Level:        level,
					Exp:          exp,
					CreateTime:   uint32(createTime),
					UpdateTime: uint32(updateTime),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			if err := mgr.AddLevelupChildPresent(tt.args.ctx, tt.args.data); (err != nil) != tt.wantErr {
				t.Errorf("AddLevelupChildPresent() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLevelupPresentMgr_AddLevelupParentPresent(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)

	itemId := uint32(1)
	parentItemId := uint32(1)
	level := uint32(1)
	exp := uint32(1)
	createTime := time.Now().Unix()
	updateTime := time.Now().Unix()
	presentType := int32(1)
	currentVersion := uint32(1)
	zipUrl := "a"
	color1 := "a"
	color2 := "a"
	levelupBg := "a"
	levelupNumber := "a"
	levelupLevel := "a"

	cacheMap := make(map[string]string)

	gomock.InOrder(
		mockStore.EXPECT().InsertLevelupParentPresent(ctx, &pb.LevelupParentPresentData{
			ParentItemId:         parentItemId,
			PresentType:          uint32(presentType),
			CurrentVersion:       currentVersion,
			ZipUrl:               zipUrl,
			CreateTime:           uint32(createTime),
			UpdateTime: uint32(updateTime),
			Color_1:              color1,
			Color_2:              color2,
			LevelupBg:            levelupBg,
			LevelupNumber:        levelupNumber,
			LevelupLevel:         levelupLevel,
		}).Return(nil),
		mockStore.EXPECT().GetAllLevelupChildrenPresent(ctx).Return(&[]*pb.LevelupChildPresentData{{
			ItemId:       itemId,
			ParentItemId: parentItemId,
			Level:        level,
			Exp:          exp,
			CreateTime:   uint32(createTime),
			UpdateTime: uint32(updateTime),
		}}, nil),
		mockCache.EXPECT().GetAllLevelupChildPresent().Return(cacheMap, nil),
		mockStore.EXPECT().GetAllLevelupParentPresent(ctx).Return(&[]pb.LevelupParentPresentData{{
			ParentItemId:   parentItemId,
			PresentType:    uint32(presentType),
			CurrentVersion: currentVersion,
			ZipUrl:         zipUrl,
			CreateTime:     uint32(createTime),
			UpdateTime: uint32(updateTime),
			Color_1:        color1,
			Color_2:        color2,
			LevelupBg:      levelupBg,
			LevelupNumber:  levelupNumber,
			LevelupLevel:   levelupLevel,
		}}, nil),
		mockCache.EXPECT().GetAllLevelupParentPresent().Return(cacheMap, nil),
		mockCache.EXPECT().SetMultiLevelupChildPresent(gomock.Any()).Return(nil),
		mockCache.EXPECT().SetMultiLevelupParentPresent(gomock.Any()).Return(nil),
	)

	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx  context.Context
		data *pb.LevelupParentPresentData
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// Add test cases.
		{
			name:    "TestLevelupPresentMgr_AddLevelupParentPresent",
			fields:  fields{
				db:         nil,
				store:      mockStore,
				rc:         nil,
				cache:      mockCache,
				presentCli: nil,
			},
			args:    args{ctx: ctx, data: &pb.LevelupParentPresentData{
				ParentItemId:         parentItemId,
				PresentType:          uint32(presentType),
				CurrentVersion:       currentVersion,
				ZipUrl:               zipUrl,
				CreateTime:           uint32(createTime),
				UpdateTime: uint32(updateTime),
				Color_1:              color1,
				Color_2:              color2,
				LevelupBg:            levelupBg,
				LevelupNumber:        levelupNumber,
				LevelupLevel:         levelupLevel,
			}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			if err := mgr.AddLevelupParentPresent(tt.args.ctx, tt.args.data); (err != nil) != tt.wantErr {
				t.Errorf("AddLevelupParentPresent() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLevelupPresentMgr_AddLevelupPerson(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)

	itemId := uint32(1)
	level := uint32(1)
	version := uint32(1)
	rank := uint32(1)
	uid := uint32(1)

	gomock.InOrder(
		mockCache.EXPECT().IncrSend(itemId, version, level).Return(int64(rank), nil),
		mockStore.EXPECT().GetMaxRank(ctx, itemId, level, version).Return(rank, nil),
		mockCache.EXPECT().SetRank(itemId, version, level, rank + 1).Return(nil),
		mockStore.EXPECT().InsertLevelupPresentSendRank(ctx, itemId, level, version, rank+1, uid).Return(nil),
	)

	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx     context.Context
		itemId  uint32
		level   uint32
		version uint32
		uid     uint32
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantRank int64
		wantPush bool
		wantErr  bool
	}{
		// Add test cases.
		{
			name:     "TestLevelupPresentMgr_AddLevelupPerson",
			fields:   fields{
				db:         nil,
				store:      mockStore,
				rc:         nil,
				cache:      mockCache,
				presentCli: nil,
			},
			args:     args{
				ctx:     ctx,
				itemId:  itemId,
				level:   level,
				version: version,
				uid:     uid,
			},
			wantRank: int64(rank + 1),
			wantPush: true,
			wantErr:  false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			gotRank, gotPush, err := mgr.AddLevelupPerson(tt.args.ctx, tt.args.itemId, tt.args.level, tt.args.version, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddLevelupPerson() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotRank != tt.wantRank {
				t.Errorf("AddLevelupPerson() gotRank = %v, want %v", gotRank, tt.wantRank)
			}
			if gotPush != tt.wantPush {
				t.Errorf("AddLevelupPerson() gotPush = %v, want %v", gotPush, tt.wantPush)
			}
		})
	}
}

func TestLevelupPresentMgr_AddLevelupPresentVersion(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	mockStore := mocks.NewMockIStore(ctl)

	itemId := uint32(1)
	version := uint32(1)

	gomock.InOrder(
		mockStore.EXPECT().AddLevelupPresentVersion(ctx, itemId, version).Return(nil),
	)

	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx     context.Context
		itemId  uint32
		version uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// Add test cases.
		{
			name:    "TestLevelupPresentMgr_AddLevelupPresentVersion",
			fields:  fields{
				db:         nil,
				store:      mockStore,
				rc:         nil,
				cache:      nil,
				presentCli: nil,
			},
			args:    args{
				ctx:     ctx,
				itemId:  itemId,
				version: version,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			if err := mgr.AddLevelupPresentVersion(tt.args.ctx, tt.args.itemId, tt.args.version); (err != nil) != tt.wantErr {
				t.Errorf("AddLevelupPresentVersion() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLevelupPresentMgr_AddUserLevelupPresentExp(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)

	itemId := uint32(1)
	version := uint32(1)
	uid := uint32(1)
	strItemId := "1"
	strParentId := "1"
	parentItemId := uint32(1)
	itemCount := uint32(1)
	orderId := "a"
	outLevel := uint32(1)
	outExp := uint32(1)

	gomock.InOrder(
		mockCache.EXPECT().ExistParentId(strItemId).Return(false, nil),
		mockCache.EXPECT().GetLevelupChildPresentById(strItemId).Return(strParentId, nil),
		mockCache.EXPECT().DelUserItemVersionData(uid, parentItemId, version).Return(nil),
		mockStore.EXPECT().AddLevelupPresentUserExp(ctx, uid, parentItemId, itemId, itemCount, version, itemCount, orderId).Return(outLevel, outExp, nil),
		mockCache.EXPECT().DelUserItemVersionData(uid, parentItemId, version),
	)


	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx       context.Context
		uid       uint32
		itemId    uint32
		version   uint32
		itemCount uint32
		orderId   string
	}
	tests := []struct {
		name         string
		fields       fields
		args         args
		wantOutLevel uint32
		wantOutExp   uint32
		wantErr      bool
	}{
		// Add test cases.
		{
			name:         "TestLevelupPresentMgr_AddUserLevelupPresentExp",
			fields:       fields{
				db:         nil,
				store:      mockStore,
				rc:         nil,
				cache:      mockCache,
				presentCli: nil,
			},
			args:         args{
				ctx:       ctx,
				uid:       uid,
				itemId:    itemId,
				version:   version,
				itemCount: itemCount,
				orderId:   orderId,
			},
			wantOutLevel: outLevel,
			wantOutExp:   outExp,
			wantErr:      false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			gotOutLevel, gotOutExp, err := mgr.AddUserLevelupPresentExp(tt.args.ctx, tt.args.uid, tt.args.itemId, tt.args.version, tt.args.itemCount, tt.args.orderId)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddUserLevelupPresentExp() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotOutLevel != tt.wantOutLevel {
				t.Errorf("AddUserLevelupPresentExp() gotOutLevel = %v, want %v", gotOutLevel, tt.wantOutLevel)
			}
			if gotOutExp != tt.wantOutExp {
				t.Errorf("AddUserLevelupPresentExp() gotOutExp = %v, want %v", gotOutExp, tt.wantOutExp)
			}
		})
	}
}

func TestLevelupPresentMgr_DeleteLevelupBatch(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)

	itemId := uint32(1)
	batchCount := uint32(1)
	effectUrl := "a"
	effectMd5 := "a"
	effectDesc := "a"
	createTime := time.Now().Unix()
	updateTime := time.Now().Unix()

	cacheMap := make(map[string]string)

	gomock.InOrder(
		mockStore.EXPECT().DeleteLevelupBatch(ctx, itemId, batchCount).Return(nil),
		mockStore.EXPECT().GetAllLevelupPresentBatch(ctx).Return(&[]pb.LevelupPresentBatchData{{
			ItemId:     itemId,
			BatchCount: batchCount,
			EffectUrl:  effectUrl,
			EffectMd5:  effectMd5,
			EffectDesc: effectDesc,
			CreateTime: uint32(createTime),
			UpdateTime: uint32(updateTime),
		}}, nil),
		mockCache.EXPECT().GetAllLevelupPresentBatch().Return(cacheMap, nil),
		mockCache.EXPECT().SetMultiLevelupPresentBatch(gomock.Any()).Return(nil),
	)


	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx        context.Context
		itemId     uint32
		batchCount uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// Add test cases.
		{
			name:    "TestLevelupPresentMgr_DeleteLevelupBatch",
			fields:  fields{
				db:         nil,
				store:      mockStore,
				rc:         nil,
				cache:      mockCache,
				presentCli: nil,
			},
			args:    args{
				ctx:        ctx,
				itemId:     itemId,
				batchCount: batchCount,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			if err := mgr.DeleteLevelupBatch(tt.args.ctx, tt.args.itemId, tt.args.batchCount); (err != nil) != tt.wantErr {
				t.Errorf("DeleteLevelupBatch() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLevelupPresentMgr_DeleteLevelupChildPresent(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)

	itemId := uint32(1)
	parentItemId := uint32(1)
	level := uint32(1)
	exp := uint32(1)
	createTime := time.Now().Unix()
	updateTime := time.Now().Unix()
	presentType := int32(1)
	currentVersion := uint32(1)
	zipUrl := "a"
	color1 := "a"
	color2 := "a"
	levelupBg := "a"
	levelupNumber := "a"
	levelupLevel := "a"

	cacheMap := make(map[string]string)

	gomock.InOrder(
		mockStore.EXPECT().DeleteLevelupChildPresent(ctx, itemId).Return(nil),
		mockStore.EXPECT().DeleteLevelupPresentBatch(ctx, itemId).Return(nil),
		mockStore.EXPECT().GetAllLevelupChildrenPresent(ctx).Return(&[]*pb.LevelupChildPresentData{{
			ItemId:       itemId,
			ParentItemId: parentItemId,
			Level:        level,
			Exp:          exp,
			CreateTime:   uint32(createTime),
			UpdateTime: uint32(updateTime),
		}}, nil),
		mockCache.EXPECT().GetAllLevelupChildPresent().Return(cacheMap, nil),
		mockStore.EXPECT().GetAllLevelupParentPresent(ctx).Return(&[]pb.LevelupParentPresentData{{
			ParentItemId:   parentItemId,
			PresentType:    uint32(presentType),
			CurrentVersion: currentVersion,
			ZipUrl:         zipUrl,
			CreateTime:     uint32(createTime),
			UpdateTime: uint32(updateTime),
			Color_1:        color1,
			Color_2:        color2,
			LevelupBg:      levelupBg,
			LevelupNumber:  levelupNumber,
			LevelupLevel:   levelupLevel,
		}}, nil),
		mockCache.EXPECT().GetAllLevelupParentPresent().Return(cacheMap, nil),
		mockCache.EXPECT().SetMultiLevelupChildPresent(gomock.Any()).Return(nil),
		mockCache.EXPECT().SetMultiLevelupParentPresent(gomock.Any()).Return(nil),
	)

	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx    context.Context
		itemId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "TestLevelupPresentMgr_DeleteLevelupChildPresent",
			fields:  fields{
				db:         nil,
				store:      mockStore,
				rc:         nil,
				cache:      mockCache,
				presentCli: nil,
			},
			args:    args{
				ctx:    ctx,
				itemId: itemId,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			if err := mgr.DeleteLevelupChildPresent(tt.args.ctx, tt.args.itemId); (err != nil) != tt.wantErr {
				t.Errorf("DeleteLevelupChildPresent() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLevelupPresentMgr_DeleteLevelupParentPresent(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)

	itemId := uint32(1)
	parentItemId := uint32(1)
	level := uint32(1)
	exp := uint32(1)
	createTime := time.Now().Unix()
	updateTime := time.Now().Unix()
	presentType := int32(1)
	currentVersion := uint32(1)
	zipUrl := "a"
	color1 := "a"
	color2 := "a"
	levelupBg := "a"
	levelupNumber := "a"
	levelupLevel := "a"

	cacheMap := make(map[string]string)

	gomock.InOrder(
		mockStore.EXPECT().DeleteLevelupParentPresent(ctx, itemId).Return(nil),
		mockStore.EXPECT().DeleteLevelupPresentBatch(ctx, itemId).Return(nil),
		mockStore.EXPECT().GetAllLevelupChildrenPresent(ctx).Return(&[]*pb.LevelupChildPresentData{{
			ItemId:       itemId,
			ParentItemId: parentItemId,
			Level:        level,
			Exp:          exp,
			CreateTime:   uint32(createTime),
			UpdateTime: uint32(updateTime),
		}}, nil),
		mockCache.EXPECT().GetAllLevelupChildPresent().Return(cacheMap, nil),
		mockStore.EXPECT().GetAllLevelupParentPresent(ctx).Return(&[]pb.LevelupParentPresentData{{
			ParentItemId:   parentItemId,
			PresentType:    uint32(presentType),
			CurrentVersion: currentVersion,
			ZipUrl:         zipUrl,
			CreateTime:     uint32(createTime),
			UpdateTime: uint32(updateTime),
			Color_1:        color1,
			Color_2:        color2,
			LevelupBg:      levelupBg,
			LevelupNumber:  levelupNumber,
			LevelupLevel:   levelupLevel,
		}}, nil),
		mockCache.EXPECT().GetAllLevelupParentPresent().Return(cacheMap, nil),
		mockCache.EXPECT().SetMultiLevelupChildPresent(gomock.Any()).Return(nil),
		mockCache.EXPECT().SetMultiLevelupParentPresent(gomock.Any()).Return(nil),
	)


	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx    context.Context
		itemId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// Add test cases.
		{
			name:    "TestLevelupPresentMgr_DeleteLevelupParentPresent",
			fields:  fields{
				db:         nil,
				store:      mockStore,
				rc:         nil,
				cache:      mockCache,
				presentCli: nil,
			},
			args:    args{
				ctx:    ctx,
				itemId: itemId,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			if err := mgr.DeleteLevelupParentPresent(tt.args.ctx, tt.args.itemId); (err != nil) != tt.wantErr {
				t.Errorf("DeleteLevelupParentPresent() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLevelupPresentMgr_GetAllLevelupBatch(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)

	cacheMap := make(map[string]string)

	gomock.InOrder(
		mockCache.EXPECT().GetAllLevelupPresentBatch().Return(cacheMap, nil),
	)

	resp := &pb.LevelupPresentBatchDataMap{}
	resp.BatchMap = make(map[uint32]*pb.LevelupPresentBatchList)

	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.LevelupPresentBatchDataMap
		wantErr bool
	}{
		// Add test cases.
		{
			name:    "TestLevelupPresentMgr_GetAllLevelupBatch",
			fields:  fields{
				db:         nil,
				store:      mockStore,
				rc:         nil,
				cache:      mockCache,
				presentCli: nil,
			},
			args:    args{ctx: ctx},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			got, err := mgr.GetAllLevelupBatch(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllLevelupBatch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllLevelupBatch() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLevelupPresentMgr_GetAllLevelupParentPresent(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)

	cacheMap := make(map[string]string)

	gomock.InOrder(
		mockCache.EXPECT().GetAllLevelupParentPresent().Return(cacheMap, nil),
	)

	resp := &pb.LevelupPresentParentMap{}
	resp.Map = make(map[uint32]*pb.LevelupParentPresentAllData)


	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.LevelupPresentParentMap
		wantErr bool
	}{
		// Add test cases.
		{
			name:    "TestLevelupPresentMgr_GetAllLevelupParentPresent",
			fields:  fields{
				db:         nil,
				store:      mockStore,
				rc:         nil,
				cache:      mockCache,
				presentCli: nil,
			},
			args:    args{ctx: ctx},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			got, err := mgr.GetAllLevelupParentPresent(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllLevelupParentPresent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllLevelupParentPresent() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLevelupPresentMgr_GetLevelupBatchById(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)

	itemId := uint32(1)
	strItemId := "1"

	gomock.InOrder(
		mockCache.EXPECT().GetLevelupPresentBatchById(strItemId).Return("", nil),
	)

	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx    context.Context
		itemId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.LevelupPresentBatchList
		wantErr bool
	}{
		// Add test cases.
		{
			name: "TestLevelupPresentMgr_GetLevelupBatchById",
			fields: fields{
				db:         nil,
				store:      mockStore,
				rc:         nil,
				cache:      mockCache,
				presentCli: nil,
			},
			args:    args{ctx: ctx, itemId: itemId},
			want:    &pb.LevelupPresentBatchList{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			got, err := mgr.GetLevelupBatchById(tt.args.ctx, tt.args.itemId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLevelupBatchById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLevelupBatchById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLevelupPresentMgr_GetLevelupChildPresentData(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	ctx := context.Background()

	mockCache := mocks.NewMockIRedisCache(ctl)
	mockStore := mocks.NewMockIStore(ctl)

	itemId := uint32(1)
	resp := &pb.LevelupChildPresentData{}

	gomock.InOrder(
		mockStore.EXPECT().GetLevelupChildPresent(ctx, itemId).Return(resp, nil),
	)

	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx    context.Context
		itemId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.LevelupChildPresentData
		wantErr bool
	}{
		// Add test cases.
		{
			name: "TestLevelupPresentMgr_GetLevelupChildPresentData",
			fields: fields{
				db:         nil,
				store:      mockStore,
				rc:         nil,
				cache:      mockCache,
				presentCli: nil,
			},
			args:    args{ctx: ctx, itemId: itemId},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			got, err := mgr.GetLevelupChildPresentData(tt.args.ctx, tt.args.itemId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLevelupChildPresentData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLevelupChildPresentData() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLevelupPresentMgr_GetLevelupChildrenPresent(t *testing.T) {
	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx          context.Context
		parentItemId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.LevelupChildPresentList
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			got, err := mgr.GetLevelupChildrenPresent(tt.args.ctx, tt.args.parentItemId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLevelupChildrenPresent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLevelupChildrenPresent() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLevelupPresentMgr_GetLevelupParentPresent(t *testing.T) {
	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx         context.Context
		offset      uint32
		limit       uint32
		presentType int32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.LevelupParentPresentList
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			got, err := mgr.GetLevelupParentPresent(tt.args.ctx, tt.args.offset, tt.args.limit, tt.args.presentType)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLevelupParentPresent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLevelupParentPresent() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLevelupPresentMgr_GetLevelupParentPresentById(t *testing.T) {
	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx    context.Context
		itemId uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   uint32
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			if got := mgr.GetLevelupParentPresentById(tt.args.ctx, tt.args.itemId); got != tt.want {
				t.Errorf("GetLevelupParentPresentById() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLevelupPresentMgr_GetLevelupParentPresentData(t *testing.T) {
	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx          context.Context
		parentItemId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.LevelupParentPresentAllData
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			got, err := mgr.GetLevelupParentPresentData(tt.args.ctx, tt.args.parentItemId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLevelupParentPresentData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLevelupParentPresentData() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLevelupPresentMgr_GetLevelupPresentVersionList(t *testing.T) {
	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx    context.Context
		itemId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.VersionList
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			got, err := mgr.GetLevelupPresentVersionList(tt.args.ctx, tt.args.itemId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLevelupPresentVersionList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLevelupPresentVersionList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLevelupPresentMgr_GetLevelupUserRank(t *testing.T) {
	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx          context.Context
		parentItemId uint32
		level        uint32
		version      uint32
		offset       uint32
		limit        uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *[]*pb.UserRank
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			if got := mgr.GetLevelupUserRank(tt.args.ctx, tt.args.parentItemId, tt.args.level, tt.args.version, tt.args.offset, tt.args.limit); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLevelupUserRank() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLevelupPresentMgr_GetUserAllLevelupPresentStatus(t *testing.T) {
	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[uint32]*pb.UserLevelExp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			got, err := mgr.GetUserAllLevelupPresentStatus(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserAllLevelupPresentStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserAllLevelupPresentStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLevelupPresentMgr_Init(t *testing.T) {
	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			mgr.Init()
		})
	}
}

func TestLevelupPresentMgr_ShutDown(t *testing.T) {
	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			mgr.ShutDown()
		})
	}
}

func TestLevelupPresentMgr_UpdateLevelupBatch(t *testing.T) {
	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx  context.Context
		data *pb.LevelupPresentBatchData
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			if err := mgr.UpdateLevelupBatch(tt.args.ctx, tt.args.data); (err != nil) != tt.wantErr {
				t.Errorf("UpdateLevelupBatch() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLevelupPresentMgr_UpdateLevelupChildPresent(t *testing.T) {
	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx  context.Context
		data *pb.LevelupChildPresentData
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			if err := mgr.UpdateLevelupChildPresent(tt.args.ctx, tt.args.data); (err != nil) != tt.wantErr {
				t.Errorf("UpdateLevelupChildPresent() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLevelupPresentMgr_UpdateLevelupParentPresent(t *testing.T) {
	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx  context.Context
		data *pb.LevelupParentPresentData
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			if err := mgr.UpdateLevelupParentPresent(tt.args.ctx, tt.args.data); (err != nil) != tt.wantErr {
				t.Errorf("UpdateLevelupParentPresent() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLevelupPresentMgr_getUserLevelupPresentStatusWithParentId(t *testing.T) {
	type fields struct {
		db         *sql.DB
		store      model.IStore
		rc         *redis.Client
		cache      cache.IRedisCache
		presentCli *userPresent.Client
	}
	type args struct {
		ctx          context.Context
		uid          uint32
		parentItemId uint32
		version      uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.UserLevelExp
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := &LevelupPresentMgr{
				db:         tt.fields.db,
				store:      tt.fields.store,
				rc:         tt.fields.rc,
				cache:      tt.fields.cache,
				presentCli: tt.fields.presentCli,
			}
			got, err := mgr.getUserLevelupPresentStatusWithParentId(tt.args.ctx, tt.args.uid, tt.args.parentItemId, tt.args.version)
			if (err != nil) != tt.wantErr {
				t.Errorf("getUserLevelupPresentStatusWithParentId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getUserLevelupPresentStatusWithParentId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewLevelupPresentMgr(t *testing.T) {
	type args struct {
		cfg config.Configer
	}
	tests := []struct {
		name    string
		args    args
		want    ILevelupPresentMgr
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewLevelupPresentMgr(tt.args.cfg)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewLevelupPresentMgr() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewLevelupPresentMgr() got = %v, want %v", got, tt.want)
			}
		})
	}
}
