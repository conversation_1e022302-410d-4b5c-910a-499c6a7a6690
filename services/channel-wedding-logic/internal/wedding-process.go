package internal

import (
    "bytes"
    "context"
    "fmt"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app"
    "golang.52tt.com/protocol/app/channel_wedding_logic"
    pushPb "golang.52tt.com/protocol/app/push"
    "golang.52tt.com/protocol/common/status"
    channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
    channel_wedding_minigame "golang.52tt.com/protocol/services/channel-wedding-minigame"
    channel_wedding_plan "golang.52tt.com/protocol/services/channel-wedding-plan"
    "google.golang.org/grpc/codes"
    "html/template"
    "time"
)

const (
    WeddingMvpMicId = 17
)

// GetGetWeddingInfo 获取婚礼房阶段信息
func (s *Server) GetGetWeddingInfo(ctx context.Context, in *channel_wedding_logic.GetChannelWeddingInfoRequest) (*channel_wedding_logic.GetChannelWeddingInfoResponse, error) {
	out := &channel_wedding_logic.GetChannelWeddingInfoResponse{}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, ErrParamInValid
	}
	opUid := svrInfo.UserID

	if in.GetCid() == 0 {
		log.ErrorWithCtx(ctx, "GetGetWeddingInfo invalid cid, %+v", in)
		return out, ErrParamInValid
	}

	weddingResp, err := s.weddingCli.GetChannelWeddingInfo(ctx, &channel_wedding.GetChannelWeddingInfoReq{
		Uid: opUid, Cid: in.GetCid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWeddingInfo fail to GetChannelWeddingInfo, %+v, err:%v", in, err)
		return out, err
	}

	weddingInfo := weddingResp.GetWeddingInfo()
	if weddingInfo == nil || weddingInfo.GetWeddingId() == 0 {
		return out, nil
	}

	uidList := []uint32{weddingInfo.GetBride().GetUid(), weddingInfo.GetGroom().GetUid()}
	userMap, err := s.userProfile.BatchGetUserProfileV2(ctx, uidList, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWeddingInfo fail to BatchGetUserProfileV2, %+v, uidList:%v, err:%v", in, uidList, err)
		return out, err
	}

	out.WeddingInfo = s.fillWeddingInfo(weddingInfo, userMap)
	out.WelcomeTextPrefix = "欢迎来到"
	out.WelcomeTextSuffix = "的世纪婚礼现场，你的到来为婚礼增添了更多的温馨和欢乐！"
    out.PresentCountInfo = fillWeddingPresentCountInfo(weddingResp.GetPresentCountInfo())

	log.DebugWithCtx(ctx, "GetWeddingInfo success, in:%+v, out:%v", in, out)
	return out, nil
}

// SwitchWeddingStage 切换婚礼房阶段
func (s *Server) SwitchWeddingStage(ctx context.Context, in *channel_wedding_logic.SwitchWeddingStageRequest) (*channel_wedding_logic.SwitchWeddingStageResponse, error) {
	out := &channel_wedding_logic.SwitchWeddingStageResponse{}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, ErrParamInValid
	}
	opUid := svrInfo.UserID

	if in.GetCid() == 0 {
		log.ErrorWithCtx(ctx, "SwitchWeddingStage invalid cid, %+v", in)
		return out, ErrParamInValid
	}

	isHost, err := s.isHost(ctx, opUid, in.GetCid())
	if err != nil {
		log.ErrorWithCtx(ctx, "SwitchWeddingStage fail to isHost, %+v, err:%v", in, err)
		return out, err
	}
	if !isHost {
		log.ErrorWithCtx(ctx, "SwitchWeddingStage not host, %+v", in)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "仅主持人可操作")
	}

	resp, err := s.miniGameCli.GetChairGameInfo(ctx, &channel_wedding_minigame.GetChairGameInfoRequest{
		ChannelId: in.GetCid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SwitchWeddingStage GetChairGameInfo fail,opUid:%d in:%v err:%v", opUid, in, err)
		return out, err
	}
	if resp.GetGameInfo().GetGameId() > 0 {
		if in.GetStage() == uint32(channel_wedding_logic.WeddingStage_WEDDING_STAGE_UNSPECIFIED) {
			return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "抢椅子过程中不支持结束婚礼哦")
		}
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "抢椅子过程中不支持切换到下个阶段哦")
	}

	_, err = s.weddingCli.SwitchWeddingStage(ctx, &channel_wedding.SwitchWeddingStageReq{
		Uid:      opUid,
		Cid:      in.GetCid(),
		Stage:    in.GetStage(),
		SubStage: in.GetSubStage(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SwitchWeddingStage fail to SwitchWeddingStage, %+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "SwitchWeddingStage success, in:%+v, out:%v", in, out)
	return out, nil
}

// TakeWeddingGroupPhoto 拍婚礼合照
func (s *Server) TakeWeddingGroupPhoto(ctx context.Context, in *channel_wedding_logic.TakeWeddingGroupPhotoRequest) (*channel_wedding_logic.TakeWeddingGroupPhotoResponse, error) {
	out := &channel_wedding_logic.TakeWeddingGroupPhotoResponse{}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, ErrParamInValid
	}
	opUid := svrInfo.UserID

	if in.GetCid() == 0 {
		log.ErrorWithCtx(ctx, "TakeWeddingGroupPhoto invalid cid, %+v", in)
		return out, ErrParamInValid
	}

	signature := generateSignature(in.GetPhotoRul(), opUid)
	if signature != in.GetSignature() {
		log.ErrorWithCtx(ctx, "TakeWeddingGroupPhoto fail to generateSignature. req: %+v, signature:%s, err:%v", in, signature, status.ErrRequestParamInvalid)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "签名错误")
	}

	isHost, err := s.isHost(ctx, opUid, in.GetCid())
	if err != nil {
		log.ErrorWithCtx(ctx, "TakeWeddingGroupPhoto fail to isHost, %+v, err:%v", in, err)
		return out, err
	}
	if !isHost {
		log.ErrorWithCtx(ctx, "TakeWeddingGroupPhoto not host, %+v", in)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "仅主持人可操作")
	}

	_, err = s.weddingCli.TakeWeddingGroupPhoto(ctx, &channel_wedding.TakeWeddingGroupPhotoReq{
		Uid:      opUid,
		Cid:      in.GetCid(),
		PhotoRul: in.GetPhotoRul(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "TakeWeddingGroupPhoto fail to TakeWeddingGroupPhoto, %+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "TakeWeddingGroupPhoto success, in:%+v, out:%v", in, out)
	return out, nil
}

// GetWeddingGroupPhotoSeatMap 获取房间合照麦位位置映射请求
func (s *Server) GetWeddingGroupPhotoSeatMap(ctx context.Context, in *channel_wedding_logic.GetWeddingGroupPhotoSeatMapRequest) (*channel_wedding_logic.GetWeddingGroupPhotoSeatMapResponse, error) {
	out := &channel_wedding_logic.GetWeddingGroupPhotoSeatMapResponse{
		SeatList: make([]*channel_wedding_logic.WeddingGroupPhotoSeat, 0),
	}

	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, ErrParamInValid
	}
	opUid := svrInfo.UserID

	resp, err := s.weddingCli.GetWeddingGroupPhotoSeatList(ctx, &channel_wedding.GetWeddingGroupPhotoSeatListReq{
		Cid: in.GetCid(),
		Uid: opUid,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWeddingGroupPhotoSeatMap fail to GetWeddingGroupPhotoSeatList, %+v, err:%v", in, err)
		return out, err
	}

	for _, v := range resp.GetSeatList() {
		out.SeatList = append(out.SeatList, &channel_wedding_logic.WeddingGroupPhotoSeat{
			MicId: v.GetMicId(),
		})
	}

	return out, nil
}

// SetUserWeddingGroupPhotoSeat 设置用户合照位置
func (s *Server) SetUserWeddingGroupPhotoSeat(ctx context.Context, in *channel_wedding_logic.SetUserWeddingGroupPhotoSeatRequest) (*channel_wedding_logic.SetUserWeddingGroupPhotoSeatResponse, error) {
	out := &channel_wedding_logic.SetUserWeddingGroupPhotoSeatResponse{}
	svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		return out, ErrParamInValid
	}
	opUid := svrInfo.UserID

	if in.GetCid() == 0 {
		log.ErrorWithCtx(ctx, "SetUserWeddingGroupPhotoSeat invalid cid, %+v", in)
		return out, ErrParamInValid
	}

	isHost, err := s.isHost(ctx, opUid, in.GetCid())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetUserWeddingGroupPhotoSeat fail to isHost, %+v, err:%v", in, err)
		return out, err
	}
	if !isHost {
		log.ErrorWithCtx(ctx, "SetUserWeddingGroupPhotoSeat not host, %+v", in)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "仅主持人可操作")
	}

	seatList := make([]*channel_wedding.WeddingGroupPhotoSeat, 0)
	for _, v := range in.GetSeatList() {
		seatList = append(seatList, &channel_wedding.WeddingGroupPhotoSeat{
			MicId: v.GetMicId(),
		})
	}

	_, err = s.weddingCli.SetUserWeddingGroupPhotoSeat(ctx, &channel_wedding.SetUserWeddingGroupPhotoSeatReq{
		Uid:      opUid,
		Cid:      in.GetCid(),
		SeatList: seatList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetUserWeddingGroupPhotoSeat fail to SetUserWeddingGroupPhotoSeat, %+v, err:%v", in, err)
		return out, err
	}

	log.InfoWithCtx(ctx, "SetUserWeddingGroupPhotoSeat success, in:%+v, out:%v", in, out)
	return out, nil
}

func (s *Server) fillWeddingInfo(info *channel_wedding.WeddingInfo, userMap map[uint32]*app.UserProfile) *channel_wedding_logic.WeddingInfo {
	out := &channel_wedding_logic.WeddingInfo{
		WeddingId:         info.GetWeddingId(),
		StartTime:         info.GetStartTime(),
		EndTime:           info.GetEndTime(),
		ChairGameEntry:    info.GetChairGameEntry() && !info.GetThemeCfg().GetIsFreeTheme(), // 免费婚礼不支持抢椅子游戏
		BridesmaidManList: info.GetBridesmaidManList(),
		CurrLevel:         info.GetCurrLevel(),
		Bride: &channel_wedding_logic.WeddingCpMemInfo{
			UserProfile: userMap[info.GetBride().GetUid()],
		},
		Groom: &channel_wedding_logic.WeddingCpMemInfo{
			UserProfile: userMap[info.GetGroom().GetUid()],
		},
		StageInfo: fillWeddingStageInfo(info.GetStageInfo()),
		ThemeCfg:  s.fillWeddingRoomThemeCfg(info.GetThemeCfg()),
		WeddingMemorialVideo: &channel_wedding_logic.WeddingMemorialVideo{
			ResourceUrl:  info.GetWeddingMemorialVideo().GetResourceUrl(),
			ResourceMd5:  info.GetWeddingMemorialVideo().GetResourceMd5(),
			ResourceJson: info.GetWeddingMemorialVideo().GetResourceJson(),
			UserPictures: info.GetWeddingMemorialVideo().GetUserPictures(),
		},
		HappinessConfig: fillHappinessConfigInfo(info.GetHappinessConfig()),
		HappinessValue:  info.GetCurrHappinessValue(),
		BoneCfg: &channel_wedding_logic.WeddingBoneCfg{
			MaleBoneId:       info.GetBoneCfg().GetMaleBoneId(),
			FemaleBoneId:     info.GetBoneCfg().GetFemaleBoneId(),
			BaseMaleBoneId:   info.GetBoneCfg().GetBaseMaleBoneId(),
			BaseFemaleBoneId: info.GetBoneCfg().GetBaseFemaleBoneId(),
		},
	}

	return out
}

func (s *Server) fillWeddingRoomThemeCfg(cfg *channel_wedding.WeddingRoomThemeCfg) *channel_wedding_logic.WeddingRoomThemeCfg {
	if cfg == nil {
		return nil
	}

	out := &channel_wedding_logic.WeddingRoomThemeCfg{
		ThemeId:             cfg.GetThemeId(),
		ThemeResource:       cfg.GetThemeResource(),
		ThemeResourceMd5:    cfg.GetThemeResourceMd5(),
		IsFreeTheme:         cfg.GetIsFreeTheme(),
		SceneCfgList:        make([]*channel_wedding_logic.WeddingSceneCfg, 0, len(cfg.GetSceneCfgList())),
		LevelClothesList:    make([]*channel_wedding_logic.WeddingLevelClothes, 0, len(cfg.GetLevelClothesList())),
		LevelBackgroundList: make([]*channel_wedding_logic.WeddingLevelBackgroundCfg, 0, len(cfg.GetLevelBackgroundList())),
		WeddingPreviewResource: &channel_wedding_logic.WeddingResource{
			ResourceUrl:  cfg.GetWeddingPreviewResource().GetResourceUrl(),
			ResourceMd5:  cfg.GetWeddingPreviewResource().GetResourceMd5(),
			CpBoneId:     cfg.GetWeddingPreviewResource().GetCpBoneId(),
			ItemIdList:   cfg.GetWeddingPreviewResource().GetItemIds(),
			BaseCpBoneId: cfg.GetWeddingPreviewResource().GetBaseCpBoneId(),
		},
	}

	for _, v := range cfg.GetSceneCfgList() {
		out.SceneCfgList = append(out.SceneCfgList, &channel_wedding_logic.WeddingSceneCfg{
			Scene:            v.GetScene(),
			SceneResource:    v.GetSceneResource(),
			SceneResourceMd5: v.GetSceneResourceMd5(),
			BoneCfgList:      fillSceneBoneCfgList(v.GetBoneCfgList()),
		})
	}

	for _, v := range cfg.GetLevelClothesList() {
		out.LevelClothesList = append(out.LevelClothesList, &channel_wedding_logic.WeddingLevelClothes{
			Level:             v.GetLevel(),
			GroomClothes:      v.GetGroomClothes(),
			BrideClothes:      v.GetBrideClothes(),
			GroomsmanClothes:  v.GetGroomsmanClothes(),
			BridesmaidClothes: v.GetBridesmaidClothes(),
		})
	}
	for _, v := range cfg.GetLevelBackgroundList() {
		out.LevelBackgroundList = append(out.LevelBackgroundList, &channel_wedding_logic.WeddingLevelBackgroundCfg{
			Level:                    v.GetLevel(),
			BackgroundPicture:        v.GetBackgroundPicture(),
			BackgroundMp4Url:         v.GetBackgroundMp4Url(),
			SpecialBackgroundMp4Url:  v.GetSpecialBackgroundMp4Url(),
			SpecialBackgroundPicture: v.GetSpecialBackgroundPicture(),
		})
	}

	//themeConf := s.bc.GetWeddingThemeCfg(cfg.GetThemeId())
	if cfg.GetChairResCfg() != nil {
		out.ChairResCfg = &channel_wedding_logic.ChairGameResourceCfg{
			ChairPic:            cfg.GetChairResCfg().ChairPic,
			SittingPoseFemaleId: cfg.GetChairResCfg().SittingPoseFemaleId,
			SittingPoseMaleId:   cfg.GetChairResCfg().SittingPoseMaleId,
			StandbyFemaleId:     cfg.GetChairResCfg().StandbyFemaleId,
			StandbyMaleId:       cfg.GetChairResCfg().StandbyMaleId,
			FailFemaleIds:       cfg.GetChairResCfg().FailFemaleIds,
			FailMaleIds:         cfg.GetChairResCfg().FailMaleIds,
		}
	}

	return out
}

func fillSceneBoneCfgList(list []*channel_wedding.WeddingSceneBoneCfg) []*channel_wedding_logic.WeddingSceneBoneCfg {
	out := make([]*channel_wedding_logic.WeddingSceneBoneCfg, 0, len(list))
	for _, v := range list {
		out = append(out, &channel_wedding_logic.WeddingSceneBoneCfg{
			Level:         v.GetLevel(),
			SeqIndex:      v.GetSeqIndex(),
			AnimationName: v.GetAnimationName(),
			BoneId:        v.GetBoneId(),
			BaseBoneId:    v.GetBaseBoneId(),
		})
	}
	return out
}

func fillWeddingStageInfo(info *channel_wedding.WeddingStageInfo) *channel_wedding_logic.WeddingStageInfo {
	if info == nil {
		return nil
	}

	out := &channel_wedding_logic.WeddingStageInfo{
		CurrStage:      info.GetCurrStage(),
		SubStage:       info.GetSubStage(),
		StageBeginTime: info.GetStageStartTs(),
		StageEndTime:   info.GetStageEndTs(),
		StageCfgList:   make([]*channel_wedding_logic.WeddingStageCfg, 0),
	}

	for _, v := range info.GetStageCfgList() {
		out.StageCfgList = append(out.StageCfgList, &channel_wedding_logic.WeddingStageCfg{
			Stage:     v.GetStage(),
			SubStage:  v.GetSubStage(),
			StageName: v.GetStageName(),
		})
	}

	return out
}

func fillHappinessConfigInfo(info *channel_wedding.HappinessConfigInfo) *channel_wedding_logic.HappinessConfigInfo {
	out := &channel_wedding_logic.HappinessConfigInfo{}
	for _, v := range info.GetConfig() {
		out.Config = append(out.Config, &channel_wedding_logic.HappinessLevelInfo{
			Level:      v.GetLevel(),
			LevelValue: v.GetLevelValue(),
		})
	}
	return out
}

func fillWeddingPresentCountInfo(info *channel_wedding.WeddingPresentCountInfo) *channel_wedding_logic.WeddingPresentCountInfo {
    if info == nil {
        return nil
    }

    out := &channel_wedding_logic.WeddingPresentCountInfo{
        UserPresentValList: make([]*channel_wedding_logic.WeddingPresentVal, 0, len(info.GetUserPresentValList())),
        MvpUid:             info.GetMvpUid(),
        TopRecvPresentLv: &channel_wedding_logic.WeddingPresentLevel{
            Uid: info.GetTopRecvPresentLv().GetUid(),
            Lv:  info.GetTopRecvPresentLv().GetLv(),
        },
    }

    for _, v := range info.GetUserPresentValList() {
        out.UserPresentValList = append(out.UserPresentValList, &channel_wedding_logic.WeddingPresentVal{
            Uid:        v.GetUid(),
            PresentVal: v.GetPresentVal(),
        })
    }

    return out
}

func (s *Server) GetWeddingHighLightPresent(ctx context.Context, request *channel_wedding_logic.GetWeddingHighLightPresentRequest) (*channel_wedding_logic.GetWeddingHighLightPresentResponse, error) {
	out := &channel_wedding_logic.GetWeddingHighLightPresentResponse{}
	resp, err := s.weddingCli.GetWeddingHighLightPresent(ctx, &channel_wedding.GetWeddingHighLightPresentRequest{
		Cid:       request.GetCid(),
		WeddingId: request.GetWeddingId(),
	})
	if err != nil {
		return out, err
	}

	out.Toast = resp.GetToast()
	return out, nil
}

func (s *Server) SendWeddingReservePresent(ctx context.Context, request *channel_wedding_logic.SendWeddingReservePresentRequest) (*channel_wedding_logic.SendWeddingReservePresentResponse, error) {
	out := &channel_wedding_logic.SendWeddingReservePresentResponse{}
	serverInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "SetDressInUse ctx:%+v", ctx)
	}

	weddingResp, err := s.weddingCli.GetChannelWeddingInfo(ctx, &channel_wedding.GetChannelWeddingInfoReq{
		Uid: serverInfo.UserID, Cid: request.GetCid(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SendWeddingReservePresent fail to GetChannelWeddingInfo, %+v, err:%v", serverInfo.UserID, err)
		return out, err
	}

	wedding, err := s.weddingPlanCli.GetSimpleWeddingPlanInfo(ctx, &channel_wedding_plan.GetSimpleWeddingPlanInfoRequest{
		WeddingPlanId: weddingResp.GetWeddingInfo().GetPlanId(),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SendWeddingReservePresent fail to GetMyWeddingInfo. req: %+v, err:%v", request, err)
		return out, err
	}

	if serverInfo.UserID != wedding.GetBuyerUid() {
		log.ErrorWithCtx(ctx, "SendWeddingReservePresent fail to GetMyWeddingInfo. req: %+v, wedding:%+v", request, wedding)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "仅婚礼购买方可操作")
	}

	_, err = s.weddingCli.SendWeddingReservePresent(ctx, &channel_wedding.SendWeddingReservePresentReq{
		Cid:       request.GetCid(),
		WeddingId: request.GetWeddingId(),
		Uid:       serverInfo.UserID,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SendWeddingReservePresent fail to SendWeddingReservePresent. req: %+v, err:%v", request, err)
		return out, err
	}
	return out, nil
}

type RemindUserJoinWeddingRoomTmplData struct {
    ChannelId        uint32
    UserInfo         NotifyUserInfo
    TimeRangeStr     string
}

type NotifyUserInfo struct {
    Account    string
    HeadImgMd5 string
    Nickname   string
}

func (s *Server) RemindUserJoinWeddingRoom(ctx context.Context, request *channel_wedding_logic.RemindUserJoinWeddingRoomRequest) (*channel_wedding_logic.RemindUserJoinWeddingRoomResponse, error) {
    out := &channel_wedding_logic.RemindUserJoinWeddingRoomResponse{}
    if request.GetTargetUid() == 0 || request.GetChannelId() == 0 {
        log.ErrorWithCtx(ctx, "RemindUserJoinWeddingRoom invalid params, req:%+v", request)
        return out, ErrParamInValid
    }

    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "RemindUserJoinWeddingRoom ctx:%+v", ctx)
        return out, ErrParamInValid
    }
    opUid := svrInfo.UserID

    resp, err := s.weddingCli.GetChannelWeddingInfo(ctx, &channel_wedding.GetChannelWeddingInfoReq{
        Uid: opUid,
        Cid: request.GetChannelId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "RemindUserJoinWeddingRoom fail to GetChannelWeddingInfo, req:%+v, err:%v", request, err)
        return out, err
    }

    weddingInfo := resp.GetWeddingInfo()
    if weddingInfo.GetStageInfo().GetCurrStage() == 0 {
        log.ErrorWithCtx(ctx, "RemindUserJoinWeddingRoom wedding stage not started, req:%+v", request)
        return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "婚礼未开始")
    }

    user, err := s.userProfile.GetUserProfileV2(ctx, opUid, true)
    if err != nil {
        log.ErrorWithCtx(ctx, "RemindUserJoinWeddingRoom fail to GetUserProfileV2, req:%+v, err:%v", request, err)
        return out, err
    }

    xml := s.dyConfig.GetConfig().EnterRoomReminderXml
    if xml == "" {
        log.WarnWithCtx(ctx, "RemindUserJoinWeddingRoom xml is empty, req:%+v", request)
        return out, nil
    }

    xmlTmpl := template.Must(template.New("enter_room_remind").Parse(xml))
    var imBuf bytes.Buffer
    err = xmlTmpl.Execute(&imBuf, &RemindUserJoinWeddingRoomTmplData{
        ChannelId:      request.GetChannelId(),
        TimeRangeStr:   fmt.Sprintf("%s-%s", time.Unix(weddingInfo.GetStartTime(), 0).Format("15:04"),
            time.Unix(weddingInfo.GetEndTime(), 0).Format("15:04")),
        UserInfo: NotifyUserInfo{
            Account:   user.GetAccount(),
            HeadImgMd5:user.GetHeadImgMd5(),
            Nickname:  user.GetNickname(),
        },
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "RemindUserJoinWeddingRoom failed to Execute. req:%+v, err:%v", request, err)
        return out, err
    }

    xmlContent := imBuf.String()
    opt := &pushPb.CommonTopRichTextDialogNotify{
        Content:         xmlContent,
        AnnounceScope:   uint32(pushPb.CommBreakingNewsBaseOpt_OUTSIDE_CHANNEL+pushPb.CommBreakingNewsBaseOpt_INSIDE_CHANNEL),
        Scene:           "wedding",
        Duration:        10, // 自动收起弹窗时长
    }

    notification := buildNotification(opt, uint32(pushPb.PushMessage_COMMON_TOP_RICH_TEXT_DIALOG_NOTIFY), "婚礼开始进房提醒弹窗")
    err = s.pushCli.PushToUsers(ctx, []uint32{request.GetTargetUid()}, notification)
    if err != nil {
        log.ErrorWithCtx(ctx, "RemindUserJoinWeddingRoom failed to PushToUsers. req:%+v, err:%v", request, err)
        return out, err
    }

    log.DebugWithCtx(ctx, "RemindUserJoinWeddingRoom request: %+v", request)
    return out, nil
}
