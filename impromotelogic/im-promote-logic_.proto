syntax = "proto3";

package ga.impromotelogic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/impromotelogic";

//一起玩客户端透传的消息结构
message ImPlayTogetherMsg {
    uint32 channel_id = 1;
    string cipher_text = 2;        //进房加密数据
    int32 room_type = 3;            //房间类型
    int64 time_out_end_time = 4;     //服务端超时结束时间
    uint32 follow_uid = 5;
    string tab_name = 6;
    string tab_logo_url = 7;
    string bg_color = 8;//背景颜色
}

message ImPromoteLogicReq {
    ga.BaseReq base_req = 1;
}

message ImPromoteLogicResp {
    ga.BaseResp base_resp = 1;
}

//获取免密进房token
message GetImPromoteLogicTokenReq {
    ga.BaseReq base_req = 1;
    string follow_account = 2;//被邀请人
    uint32 channel_id = 3;//房间id
}

message GetImPromoteLogicTokenResp {
    ga.BaseResp base_resp = 1;
    string sign = 2;//返回的token 为一个签名
    int64 time_out = 3;//超时时间 单位是秒 例如600就是10分钟
}

message GetPlayTogetherTabListReq{
    ga.BaseReq base_req = 1;
    string to_account = 2;//发送到的账号
}


message PlayTogetherTab  {
    uint32 tag_id = 1; //游戏iD
    uint32 tab_type = 2; //游戏类型
    string name = 3; //游戏名字
    string image_uri = 4; //游戏logo
    string name_color = 5;//游戏名字颜色
}

message GetPlayTogetherTabListResp{
    ga.BaseResp base_resp = 1;
    repeated PlayTogetherTab list = 2;
}

message GetPlayTogetherMatchConfigReq {
    ga.BaseReq base_req = 1;
}

message PlayTogetherMatchConfigItem {
    PlayTogetherTab tab = 1;
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    repeated string matchItem = 2;
}

message GetPlayTogetherMatchConfigResp {
    ga.BaseResp base_resp = 1;
    repeated PlayTogetherMatchConfigItem config_list = 2;
}


message CueConfig{
    uint32 id = 1;
    string type_name = 2;
    string entrance_pic = 3;
    string guide_text_from = 4;
    string guide_text_to = 5;
    string lottie = 6;
    string msg_pic_left = 7;
    string msg_pic_right = 8;
    string preview_msg = 9;
}

message GetCueConfigReq{
    ga.BaseReq base_req = 1;
}

message GetCueConfigResp{
    ga.BaseResp base_resp = 1;
    repeated CueConfig conf = 2;
}

enum PushButtonType {
    Default = 0;    //未用
    CommentAndAt = 1;    //评论和@
    FANS = 2; //粉丝
    Careful = 3; //关心
    LiveStart = 4; //直播
    Attitude = 5;   //点赞
    DiyRecommend = 6;    //个性化推荐
    EnterRoomNotify = 7; // 房客进房通知
    InviteRoom = 8; //邀请进房
    PalOnlineNotify = 9; // 玩伴上线互动提醒通知
    AttitudeNotify = 10;  //极速pc点赞通知
    FastPcShowProcess = 11; // 极速pc显示进程名称
}

enum OpenPageType {
    MsgType = 0;    //新消息提示页面
    ConcealType = 1; //隐私页面
}

message PushButtonInfo {
    PushButtonType button_type = 1;
    bool on = 2;   //开启 true 关闭 false
    string desc = 3;
    string second_desc = 4; //副文案
    oneof sub_type {
        uint32 fast_pc_show_process = 5[deprecated = true];
    }
    // 根据不同的button_type，sub_type有不同的含义, see PushButtonSubType
    uint32 button_sub_type=6;
}

// 给PushButtonInfo的 button_sub_type列出枚举
enum PushButtonSubType {
    PUSH_BUTTON_SUB_TYPE_UNSPECIFIED = 0;
    PUSH_BUTTON_SUB_TYPE_ALL = 1; // 1:所有人可见
    PUSH_BUTTON_SUB_TYPE_FANS = 2; // 2:粉丝可见
    PUSH_BUTTON_SUB_TYPE_PARTNER = 3; // 3-玩伴可见
}

message PushButtonsReq{
    ga.BaseReq base_req = 1;
    repeated PushButtonInfo push_button_infos = 2;
    OpenPageType page_type = 3;  //哪个页面保存的
}

message PushButtonsRsp{
    ga.BaseResp base_resp = 1;
}

message GetPushButtonsReq{
    ga.BaseReq base_req = 1;
    OpenPageType page_type = 2;  //哪个页面保存的
    repeated PushButtonType buttons = 3; // 指定只返回部分开关数据，默认为空时，返回全部
}

message GetPushButtonsRsp{
    ga.BaseResp base_resp = 1;
    repeated PushButtonInfo push_button_infos = 2;
}
