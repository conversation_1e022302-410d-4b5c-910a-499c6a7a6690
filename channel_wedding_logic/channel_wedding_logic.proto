syntax = "proto3";

/***************婚礼房logic*****************/

package ga.channel_wedding_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel_wedding_logic";

// 婚礼阶段枚举
enum WeddingStage {
    WEDDING_STAGE_UNSPECIFIED = 0;        // 未指定/未开始
    WEDDING_STAGE_WELCOME_GUEST = 1;      // 迎接嘉宾
    WEDDING_STAGE_BRIDE_GROOM_ENTER = 2;  // 新人进场
    WEDDING_STAGE_LOVE_DECLARATION = 3;   // 爱的宣言
    WEDDING_STAGE_EXCHANGE_RING = 4;      // 交换戒指
    WEDDING_STAGE_HIGHLIGHT = 5;          // 高光时刻
    WEDDING_STAGE_GROUP_PHOTO = 6;        // 合影留恋
}

enum GroupPhotoSubStage {
    GROUP_PHOTO_SUB_STAGE_UNSPECIFIED = 0;
    GROUP_PHOTO_SUB_STAGE_PHOTOGRAPH = 1; // 拍合照
}

// 婚礼房阶段配置
message WeddingStageCfg {
    uint32 stage = 1;  // 阶段, see WeddingStage
    string stage_name = 2;   // 阶段名称
    uint32 sub_stage = 3; // 子阶段，例如合影留恋下进入拍合照子阶段
}

// 当前婚礼阶段信息
message WeddingStageInfo {
    repeated WeddingStageCfg stage_cfg_list = 1;  // 阶段配置
    uint32 curr_stage = 2;  // 当前阶段, see WeddingStage
    uint32 sub_stage = 3; // 当前子阶段
    int64 stage_begin_time = 4;  // 阶段开始时间，秒级时间戳
    int64 stage_end_time = 5;    // 阶段结束时间，秒级时间戳
}

// 婚礼等级服装信息
message WeddingLevelClothes {
    uint32 level = 1;           // 等级
    repeated uint32 groom_clothes = 2;   // 新郎婚服虚拟形象物品id列表
    repeated uint32 bride_clothes = 3;   // 新娘婚服虚拟形象物品id列表
    repeated uint32 groomsman_clothes = 4;  // 伴郎婚服虚拟形象物品id列表
    repeated uint32 bridesmaid_clothes = 5;  // 伴娘婚服虚拟形象物品id列表
}

// 婚礼场景动画枚举
enum WeddingScene {
    WEDDING_SCENE_UNSPECIFIED = 0;
    WEDDING_SCENE_BRIDE_GROOM_ENTER = 1;  // 新人进场
    WEDDING_SCENE_EXCHANGE_RING = 2;      // 交换戒指
    WEDDING_SCENE_HIGHLIGHT = 3;          // 高光时刻
    WEDDING_SCENE_GROUP_PHOTO = 4;        // 合影留恋
    WEDDING_SCENE_LOVE_DECLARATION = 5;   // 爱的宣言
}

message WeddingSceneBoneCfg {
    uint32 level = 1;
    uint32 seq_index = 2; // 分镜顺序号
    string animation_name = 3;// 动画名称
    uint32 bone_id = 4;  // 骨骼资源ID
    uint32 base_bone_id = 5; // 基础骨骼资源ID
}

message WeddingSceneCfg {
    uint32 scene = 1;           // 场景, see WeddingScene
    string scene_resource = 2;  // 场景动画资源
    string scene_resource_md5 = 3;
    repeated WeddingSceneBoneCfg bone_cfg_list = 4;  // 骨骼配置
}

// 幸福值等级配置信息
message HappinessLevelInfo{
    uint32 level = 1 ;
    uint32 level_value = 2;      // 当前等级幸福值
    string icon = 3;             // 幸福值图标
    string background = 4;       // 幸福值底板
}

// 幸福值配置信息
message HappinessConfigInfo{
    repeated  HappinessLevelInfo config = 1;   // 幸福值等级配置
    string upgrade_video = 2;        // 幸福值升级动画
}

// 婚礼房等级背景配置
message WeddingLevelBackgroundCfg {
    uint32 level = 1;           // 等级
    string background_picture = 2;  // 背景资源图片
    string background_mp4_url = 3;  // 背景mp4
    string special_background_picture = 4;  // 结婚宣言、互换戒指 需要特殊的背景资源图片
    string special_background_mp4_url = 5;  // 背景mp4
}

message WeddingResource {
    string resource_url = 1;
    string resource_md5 = 2;
    uint32 cp_bone_id = 3;  // 双人骨骼资源id
    repeated uint32 item_id_list = 4;  // 皮肤物品id列表
    uint32 base_cp_bone_id = 5;  // 基础骨骼资源id
}

// 婚礼房主题配置
message WeddingRoomThemeCfg {
    uint32 theme_id = 1;        // 主题ID
    string theme_resource = 2;  // 主题资源
    string theme_resource_md5 = 3;
    repeated WeddingSceneCfg scene_cfg_list = 4;  // 场景动画配置，需预下载资源

    string chair_game_resource = 5[deprecated=true];     // 抢椅子游戏资源
    string chair_game_resource_md5 = 6[deprecated=true];

    repeated WeddingLevelClothes level_clothes_list = 7;  // 等级服装配置， 需预下载虚拟形象组件资源
    repeated WeddingLevelBackgroundCfg level_background_list = 8;  // 等级背景配置

    WeddingResource wedding_preview_resource = 9;  // 婚礼预告资源

    uint32 chair_resource_id = 10[deprecated=true];  // 椅子资源ID

    bool is_free_theme = 11;  // 是否是免费婚礼主题

    ChairGameResourceCfg chair_res_cfg = 12; // 抢椅子 椅子资源配置
}

// 抢椅子 椅子资源配置
message ChairGameResourceCfg {
    string chair_pic = 1;  // 椅子切图
    uint32 sitting_pose_female_id = 2;  // 坐姿ID-女
    uint32 sitting_pose_male_id = 3;    // 坐姿ID-男

    uint32 standby_female_id = 4; // 待机女性物品ID
    uint32 standby_male_id = 5;   // 待机男性物品ID
    repeated uint32 fail_female_ids = 6;    // 女-失败皮肤物品id列表
    repeated uint32 fail_male_ids = 7;     // 男
}

// 婚礼新人信息
message WeddingCpMemInfo {
    UserProfile user_profile = 1; // 用户信息
}

// 新人纪念视频
message WeddingMemorialVideo {
    string resource_url = 1;
    string resource_md5 = 2;
    string resource_json = 3;
    repeated string user_pictures = 4;  // 用户照片列表
}

// 骨骼配置
message WeddingBoneCfg {
    uint32 male_bone_id = 1;    // 男性骨骼物品ID
    uint32 female_bone_id = 2;  // 女性骨骼物品ID
    uint32 base_male_bone_id = 3; // 男性基础骨骼物品ID
    uint32 base_female_bone_id = 4; // 女性基础骨骼物品ID
}

// 婚礼信息
message WeddingInfo {
    int64 wedding_id = 1;               // 婚礼ID
    WeddingStageInfo stage_info = 2;    // 阶段信息
    WeddingRoomThemeCfg theme_cfg = 3;  // 主题配置
    uint32 curr_level = 4;              // 当前等级
    WeddingCpMemInfo bride = 5;         // 新娘信息
    WeddingCpMemInfo groom = 6;         // 新郎信息
    int64 start_time = 7;               // 开始时间
    int64 end_time = 8;                 // 结束时间
    WeddingMemorialVideo wedding_memorial_video = 9;  // 新人纪念视频

    bool chair_game_entry = 10;  // 是否开启椅子游戏入口
    repeated uint32 bridesmaid_man_list = 11;  // 伴郎伴娘uid列表

    HappinessConfigInfo happiness_config = 12; // 幸福值配置
    uint32 happiness_value = 13; // 当前幸福值
    WeddingBoneCfg bone_cfg = 14; // 骨骼配置
}

message WeddingClothesInfo {
    uint32 uid = 1;  // 用户ID
    repeated uint32 clothes_id_list = 2;  // 虚拟形象物品ID列表
    uint32 orientation = 3; // 朝向， see virtual_image_logic_.proto VirtualImageOrientation
}

// 批量获取用户服装信息请求
message BatchGetUserWeddingClothesRequest {
    ga.BaseReq base_req = 1;
    uint32 cid = 2;  // 房间ID
    repeated uint32 uid_list = 3;  // 用户ID列表
}

// 批量获取用户服装信息响应
message BatchGetUserWeddingClothesResponse {
    ga.BaseResp base_resp = 1;
    repeated WeddingClothesInfo clothes_info_list = 2;  // 用户服装信息
}

// 婚礼房麦上用户服装变更通知信息
message WeddingClothesChangeOpt {
    uint32 cid = 1;  // 房间ID
    int64 server_time_ms = 2;  // 服务器时间,单位毫秒
    repeated WeddingClothesInfo clothes_info_list = 3;  // 用户服装信息
}

// 婚礼阶段变化通知信息
message WeddingStageChangeOpt {
    uint32 cid = 1;  // 房间ID
    int64 server_time_ms = 2;  // 服务器时间,单位毫秒
    WeddingInfo wedding_info = 3;  // 婚礼信息
    repeated WeddingClothesInfo clothes_info_list = 4;  // 新人穿戴的服装信息
}

// 婚礼等级变化通知信息
message WeddingLevelChangeOpt {
    uint32 cid = 1;  // 房间ID
    int64 server_time_ms = 2;  // 服务器时间,单位毫秒
    uint32 curr_level = 3;  // 当前等级
    repeated WeddingClothesInfo clothes_info_list = 4;  // 升级后用户服装信息
}

// 婚礼场景动画通知信息
message WeddingSceneNotifyOpt {
    uint32 cid = 1;  // 房间ID
    int64 server_time_ms = 2;  // 服务器时间,单位毫秒
    WeddingSceneCfg scene_cfg = 3;  // 场景动画
    uint32 curr_level = 4;  // 当前等级
    string ext_opt = 5;  // [废弃，使用ext_opt_bytes]
    bytes ext_opt_bytes = 6;  // 扩展信息, 例如合照场景下为 WeddingSceneGroupPhotoOpt 序列化信息
    WeddingReservePresent reserve_present = 7;  // 付费预约礼物
}

// 合影留恋场景附加消息
message WeddingSceneGroupPhotoOpt {
  string photo_url = 1;
}

// 高光时刻抢捧花附加消息
message WeddingSceneHighLightOpt {
    uint32 present_id = 1;  // 捧花礼物ID
    uint32 day_count = 2;   // 礼物天数
}

// 付费预约礼物
message WeddingReservePresent {
    uint32 present_id = 1;  // 付费预约礼物ID
    uint32 buyer_uid = 2;   // 付费用户UID
}


// 婚礼幸福值变化通知信息
message WeddingHappinessChangeOpt {
    uint32 cid = 1;  // 房间ID
    int64 server_time_ms = 2;  // 服务器时间,单位毫秒
    uint32 curr_happiness_value = 3;  // 当前幸福值
    uint32 next_level_tips = 4;  // 差xx值内需要下一级幸福值提示
}

// 获取房间婚礼信息请求
message GetChannelWeddingInfoRequest {
    ga.BaseReq base_req = 1;
    uint32 cid = 2;  // 房间ID
}

// 获取房间婚礼信息响应
message GetChannelWeddingInfoResponse {
    ga.BaseResp base_resp = 1;
    WeddingInfo wedding_info = 2;  // 婚礼信息
    string welcome_text_prefix = 3; // 公屏文案前缀
    string welcome_text_suffix = 4; // 公屏文案后缀
    WeddingPresentCountInfo present_count_info = 5;  // 婚礼收送礼物值信息
}

// 切换婚礼阶段请求
message SwitchWeddingStageRequest {
    ga.BaseReq base_req = 1;
    uint32 cid = 2;  // 房间ID
    uint32 stage = 3;  // 阶段, see WeddingStage
    uint32 sub_stage = 4; // 子阶段，例如合影留恋下进入大合照子阶段
}

// 切换婚礼阶段响应
message SwitchWeddingStageResponse {
    ga.BaseResp base_resp = 1;
}

// 拍合照
message TakeWeddingGroupPhotoRequest {
    ga.BaseReq base_req = 1;
    uint32 cid = 2;  // 房间ID
    string photo_rul = 3; // 合照url
    string signature = 4; // 合照图片签名
}

message TakeWeddingGroupPhotoResponse {
    ga.BaseResp base_resp = 1;
}

// 获取用户婚礼站姿请求
message GetUserWeddingPoseRequest {
    ga.BaseReq base_req = 1;
    uint32 cid = 2;  // 房间ID
}

// 获取用户婚礼站姿响应
message GetUserWeddingPoseResponse {
    ga.BaseResp base_resp = 1;
    repeated uint32 pose_id_list = 2;  // 用户的站姿(虚拟形象物品)ID列表
    uint32 curr_pose_id = 3;  // 当前站姿(虚拟形象物品)ID
    uint32 orientation = 4; // 朝向， see virtual_image_logic_.proto VirtualImageOrientation
}

// 设置用户婚礼站姿请求
message SetUserInuseWeddingPoseRequest {
    ga.BaseReq base_req = 1;
    uint32 cid = 2;  // 房间ID
    uint32 pose_id = 3;  // 站姿(虚拟形象物品)ID
}

// 设置用户婚礼站姿响应
message SetUserInuseWeddingPoseResponse {
    ga.BaseResp base_resp = 1;
}

// 批量获取用户婚礼正在使用的站姿请求
message BatchGetUserInuseWeddingPoseRequest {
    ga.BaseReq base_req = 1;
    uint32 cid = 2;  // 房间ID
    repeated uint32 uid_list = 3;  // 用户ID列表
}

// 设置用户朝向
message SetUserWeddingOrientationRequest {
    ga.BaseReq base_req = 1;
    uint32 cid = 2;  // 房间ID
    uint32 orientation = 3; // 朝向， see virtual_image_logic_.proto VirtualImageOrientation
}

// 设置用户朝向响应
message SetUserWeddingOrientationResponse {
    ga.BaseResp base_resp = 1;
}

message UserWeddingPose {
    uint32 uid = 1;  // 用户ID
    uint32 pose_id = 2;  // 站姿(虚拟形象物品)ID
}

// 批量获取用户婚礼正在使用的站姿响应
message BatchGetUserInuseWeddingPoseResponse {
    ga.BaseResp base_resp = 1;
    repeated UserWeddingPose user_pose = 2;  // 用户正在使用的站姿(虚拟形象物品)
}

// [废弃]，用户婚礼站姿变更通知
message UserWeddingPoseChangeOpt {
    uint32 cid = 1;  // 房间ID
    uint32 uid = 2;  // 用户ID
    uint32 pose_id = 3;  // 站姿(虚拟形象物品)ID
}

// 获取房间合照麦位位置映射请求
message GetWeddingGroupPhotoSeatMapRequest {
    ga.BaseReq base_req = 1;
    uint32 cid = 2;  // 房间ID
}

// 合照麦位位置映射
message WeddingGroupPhotoSeat {
    uint32 mic_id = 1;      // 麦位ID
}

// 获取房间合照麦位位置映射响应
message GetWeddingGroupPhotoSeatMapResponse {
    ga.BaseResp base_resp = 1;
    repeated WeddingGroupPhotoSeat seat_list = 2;  // 全量麦位位置序列
    repeated uint32 pose_confirmed_uid_list = 3;   // 已确认站姿的用户ID列表
}

// 设置用户合照位置请求
message SetUserWeddingGroupPhotoSeatRequest {
    ga.BaseReq base_req = 1;
    uint32 cid = 2;  // 房间ID
    repeated WeddingGroupPhotoSeat seat_list = 3;  // 全量麦位位置序列
}

// 设置用户合照位置响应
message SetUserWeddingGroupPhotoSeatResponse {
    ga.BaseResp base_resp = 1;
}

// 用户合照变化通知
message WeddingGroupPhotoSeatChangeOpt {
    uint32 cid = 1;  // 房间ID
    repeated WeddingGroupPhotoSeat seat_list = 2;  // 全量的麦位位置映射
    repeated uint32 pose_confirmed_uid_list = 3;   // 已确认站姿的用户ID列表
}

// 嘉宾类型
    enum WeddingGuestType {
  WEDDING_GUEST_TYPE_UNSPECIFIED = 0;
  WEDDING_GUEST_TYPE_BRIDE = 1;       // 新娘
  WEDDING_GUEST_TYPE_GROOM = 2;       // 新郎
  WEDDING_GUEST_TYPE_BRIDES = 3;      // 伴郎
  WEDDING_GUEST_TYPE_BRIDESMAID = 4;  // 伴娘
  WEDDING_GUEST_TYPE_FRIENDS = 5;     // 亲友团
}

// 新人/嘉宾进房通知
message WeddingGuestEnterRoomOpt {
    uint32 cid = 1;  // 房间ID
    UserProfile user = 2;  // 进房嘉宾信息
    int64 server_time_ms = 3;  // 服务器时间,单位毫秒
    string public_text_prefix = 4; // 公屏文案前缀
    string public_text_suffix = 5; // 公屏文案后缀
    uint32 guest_type = 6;  // 嘉宾类型, see WeddingGuestType
}

message WeddingRankEntry{
    bool show_entry = 1; // 是否显示婚礼排行榜入口
    string bride_account = 2; // 新娘账号
    string groom_account = 3; // 新郎账号,用户展示头像
    string h5_url = 4; // 婚礼排行榜web 短链
}

message GetWeddingRankEntryRequest{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}
message GetWeddingRankEntryResponse{
    ga.BaseResp base_resp = 1;
    WeddingRankEntry wedding_rank_entry = 2; // 婚礼排行榜入口
}

// 获取婚礼主题列表请求
message GetWeddingThemeCfgListRequest {
    ga.BaseReq base_req = 1;
}

message GetWeddingThemeCfgListResponse {
    ga.BaseResp base_resp = 1;
    repeated WeddingRoomThemeCfg theme_cfg_list = 2;  // 婚礼主题列表
    uint32 download_delay_sec = 3;  // 下载延迟时间，单位秒
}

// =============== 抢椅子游戏 begin ===============
// ---------------- 玩家侧 ----------------- 

// 申请参加
message ApplyToJoinChairGameRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    bool is_cancel = 3;
  }
  
  message ApplyToJoinChairGameResponse {
    ga.BaseResp base_resp = 1;
  }
  
  message ChairGameUserInfo {
      UserProfile user_info = 1;
      bool is_in_channel = 2;   // 是否在房中
      uint32 wedding_guest_type = 3; // 嘉宾类型, see WeddingGuestType, 没身份则为0
  }

  message ChairGamePublicText {
      uint32 uid = 1;  
      string nickname = 2;
      string invite_text = 3; // 邀请文案
  }

  // 申请成功消息
  message ChairGameApplyMsgOpt {
    ChairGamePublicText public_text = 1; // 存在时展示公屏

      uint32 total_apply_num = 2; // 总报名人数
  }

  message ChairGamePlayerOpt {
    repeated uint32 uid_list = 1;  // 仅在列表中的用户展示“已成为本局游戏玩家”的提示动画
  }

  
  // 获取报名列表
  message GetChairGameApplyListRequest
  {
      BaseReq base_req = 1;
      uint32 channel_id = 2;
  }
  
  message GetChairGameApplyListResponse
  {
      BaseResp base_resp = 1;
      repeated ChairGameUserInfo apply_user_list = 2;  // 按报名先后顺序
      uint32 total_apply_num = 3; // 总报名人数
  }

message ChairGameRewardSetting{
    repeated uint32 gift_type = 1;  // 支持配置的礼物类型，see ga_base.proto PresentTagType
    bool support_magic_gift = 2; // 是否支持幸运礼物
    
    uint32 price_limit = 3; // 价格限制,仅展示T豆价值<=price_limit的礼物
}

  // 抢椅子奖励信息
message ChairGameRewardInfo
{
    string icon = 1;        // 奖励图标
    string name = 2;        // 奖励名称
    uint32 value = 3;       // 奖励价值
    string reward_unit = 4; // 奖励单位,"T豆"、"红钻"

    uint32 amount = 5;      // 数量
}

// 新人设置游戏奖励
message SetChairGameRewardRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 award_gift_id = 3;  // 奖励礼物id
    uint32 gift_type = 4;      // 礼物类型，see ga_base.proto PresentTagType
    uint32 award_gift_num = 5; // 奖励礼物数量
    bool is_magic_gift = 6;    // 是否为幸运礼物
}

message SetChairGameRewardResponse {
    ga.BaseResp base_resp = 1;
}

// 获取抢椅子游戏奖励配置信息
message GetChairGameRewardSettingRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}

message GetChairGameRewardSettingResponse {
    ga.BaseResp base_resp = 1;
    ChairGameRewardSetting reward_conf = 2;  // 奖励配置
    repeated ChairGameRewardInfo reward_list = 3;    // 游戏奖励列表
    uint32 sponsor_uid = 4; // 设置奖励的用户uid
}

enum ChairRoundStatus
{
    CHAIR_ROUND_STATUS_UNSPECIFIED = 0;
    CHAIR_ROUND_STATUS_ROUND_BEGIN = 1; // 本轮开始
    CHAIR_ROUND_STATUS_ROUND_GRABBING = 2;    // 抢椅子中
    CHAIR_ROUND_STATUS_ROUND_END = 3;   // 本轮结束
    CHAIR_ROUND_STATUS_GAME_OVER = 4;   // 游戏结束
}

// 游戏进程
message ChairGameProgress
{
    uint32 game_id = 1;
    uint32 cur_round = 2;   // 游戏阶段, 第cur_round轮
    uint32 chair_num = 3;   // 本轮的椅子数量；当chair_num == 1时，该轮为决赛局
    uint32 round_status = 4; // 游戏状态,see ChairRoundStatus
    bool show_round_tip = 5; // 是否显示轮次提示动画
    
    // 本轮参与用户
    repeated uint32 round_palyer_uids = 6; // 参与本轮抢椅子的用户列表
    repeated uint32 round_winner_uids = 7;  // 本轮获胜者uid列表，列表排序表示抢到椅子的先后顺序
  
    uint32 next_round_chair_num = 8; // 下一轮的椅子数量
    int64 server_time_ms = 9;  // 服务器时间,单位毫秒
    int64 host_start_but_duration = 10; // 主持人开始按钮倒计时,秒
    int64 host_button_end_ts = 11; // 主持人倒计时按钮结束时间戳,秒
}

message ChairGameInfo {
    uint32 game_id = 1;
    bool show_game_begin_anim = 2; // 是否显示游戏即将开始动画
    ChairGameProgress game_progress = 3;
    repeated ChairGameRewardInfo reward_list = 4; // 游戏奖励
    repeated UserProfile player_list = 5[deprecated=true]; // 废弃过度字段

    repeated ChairGamePlayerInfo players = 6; // 参与本局抢椅子的用户列表
}

message ChairGamePlayerInfo {
    UserProfile user_info = 1;
    uint32 mic_id = 2;  // 麦位ID
}

// 获取房内抢椅子游戏信息
message GetChairGameInfoRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}

message GetChairGameInfoResponse {
    ga.BaseResp base_resp = 1;
    ChairGameInfo game_info = 2;
}

// 抢座
message GrabChairRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 game_id = 3;
}

message GrabChairResponse {
    ga.BaseResp base_resp = 1;
}

// --------------- 主持人侧 ------------------
// 开启新的一局
message StartChairGameRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    repeated uint32 player_uids = 3; // 参与本轮抢椅子的用户列表
}

message StartChairGameResponse {
    ga.BaseResp base_resp = 1;
}

// 进入下一轮
message SetChairGameToNextRoundRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}

message SetChairGameToNextRoundResponse {
    ga.BaseResp base_resp = 1;
}

// 开抢
message StartGrabChairRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}

message StartGrabChairResponse {
    ga.BaseResp base_resp = 1;
}


// ------------ 抢椅子游戏 end -------------------

// ------------ 预定婚礼,婚前准备 -----------------



// 婚礼安排信息
message SimpleWeddingPlanInfo {
    uint32 wedding_plan_id = 1; // 婚礼ID, 用于预约时间,邀请亲友,伴郎伴娘
    uint32 theme_id = 2;  // 购买的主题ID
    string schedule_wedding_duration_str = 3;  // 计划页婚礼时间, 空则还没预约
    repeated UserProfile groomsman_list = 4;  // 伴娘伴郎列表
    repeated UserProfile family_list = 5;  // 亲友团列表
    uint32 already_upload_big_screen_num = 6; // 已上传大屏图片数量
}

message BigScreenImage {
    string img_url = 1;
    bool is_under_review = 2; // 是否正在审核中
    uint32 upload_by_uid = 3; // 是谁上传的
}

message GetWeddingBigScreenRequest {
    ga.BaseReq base_req = 1;
    uint32 wedding_plan_id = 2; // 婚礼计划ID
}

message GetWeddingBigScreenResponse {
    ga.BaseResp base_resp = 1;
    repeated BigScreenImage img_list = 2; // 图片列表
    int64 server_ts = 3; // 服务器时间戳，用来避免乱序。单位毫秒
}

message SaveWeddingBigScreenRequest {
    enum BigScreenOperation {
        BIG_SCREEN_OPERATION_UNSPECIFIED = 0;
        BIG_SCREEN_OPERATION_ADD = 1;
        BIG_SCREEN_OPERATION_DEL = 2;
    }
    ga.BaseReq base_req = 1;
    uint32 wedding_plan_id = 2; // 婚礼计划ID
    string img_url = 3; // 涉及哪张图片
    BigScreenOperation operation = 4; // 操作类型,see BigScreenOperation
}

message SaveWeddingBigScreenResponse {
    ga.BaseResp base_resp = 1;
    repeated BigScreenImage img_list = 2; // 图片列表
    int64 server_ts = 3; // 服务器时间戳，用来避免乱序。单位毫秒
}

// 婚礼大屏图片变更通知，包括 新增、删除、审核状态变化
message WeddingBigScreenChangeNotify {
    uint32 wedding_plan_id = 1; // 婚礼计划ID
    repeated BigScreenImage img_list = 2; // 图片列表
    int64 server_ts = 3; // 服务器时间戳，用来避免乱序。单位毫秒
}

enum WeddingInviteType {
    WEDDING_INVITE_TYPE_UNSPECIFIED = 0;
    WEDDING_INVITE_TYPE_GROOMSMAN = 1; // 伴郎
    WEDDING_INVITE_TYPE_BRIDESMAID = 2; // 伴娘
    WEDDING_INVITE_TYPE_FRIEND = 3; // 亲友团
}

enum WeddingInviteStatus {
    WEDDING_INVITE_STATUS_UNSPECIFIED = 0;
    WEDDING_INVITE_STATUS_WAITING = 1; // 待处理
    WEDDING_INVITE_STATUS_ACCEPTED = 2; // 已接受
    WEDDING_INVITE_STATUS_REFUSED = 3; // 已拒绝
    WEDDING_INVITE_STATUS_CANCELED = 4; // 已失效
}

message InviteCard {
    uint32 invite_id = 2; // 邀请ID
    uint32 wedding_plan_id = 3; // 婚礼ID
    uint32 theme_id = 4; // 婚礼主题
    UserProfile groom = 5; // 新郎信息
    UserProfile bride = 6; // 新娘信息
    string wedding_datetime = 7; // 婚礼日期时间
    uint32 wedding_start_time = 13; // 婚礼开始时间
    string wedding_channel = 8; // 婚礼房间
    uint32 wedding_channel_id = 12; // 婚礼房间ID
    repeated WeddingGuestGift gift_list = 9; // 礼物列表
    string invite_card_bg = 10; // 邀请卡片背景
    uint32 wedding_invite_type = 11 ; // 邀请类型, see WeddingInviteType
}

message WeddingGuestGift {
    string gift_url = 1; // 礼物图片
    string gift_desc = 2; // 礼物描述
}

// 获取邀请信息(点击im)
message GetWeddingInviteInfoRequest {
    ga.BaseReq base_req = 1;
    uint32 wedding_plan_id = 2; // 婚礼id
    uint32 wedding_invite_type = 3; // 邀请类型, see WeddingInviteType
    uint32 invite_id = 4; // 邀请ID
}

message GetWeddingInviteInfoResponse {
    ga.BaseResp base_resp = 1;
    InviteCard invite_card = 2; // 邀请卡片
    uint32 invite_status = 3; // 邀请状态, see WeddingInviteStatus
}

message HandleWeddingInviteRequest {
    ga.BaseReq base_req = 1;
    uint32 invite_id = 2; // 邀请ID
    bool is_accept = 3; // 是否接受邀请
}

message HandleWeddingInviteResponse {
    ga.BaseResp base_resp = 1;
}

// 购买婚礼请求
message BuyWeddingRequest {
    ga.BaseReq base_req = 1;
    uint32 theme_id = 2;  // 主题ID
    ReserveInfo reserve_info = 3;
    uint32 source_msg_id = 4; // im来源的msgId
}

message ReserveInfo {
    string channel_name = 2; // 房间名称
    uint32 start_time = 3; // 预约开始时间
    uint32 end_time = 4; // 预约结束时间
    uint32 theme_id = 9; // 主题ID
    string theme_name = 10; // 主题名称

    // 支付透传参数
    uint32 channel_id = 1; // 房间ID
    bool is_hot = 5; // 是否热门婚礼
    uint32 gift_id = 6; // 礼物ID
    uint32 reserve_date = 7; // 预约日期, h5侧用来校准时间
    repeated string reserve_time = 8; // 用户选择的预约时段
}

message BuyWeddingResponse {
    ga.BaseResp base_resp = 1;
}

// 获取婚礼计划页请求
message GetWeddingSchedulePageInfoRequest {
    ga.BaseReq base_req = 1;
}

message WeddingDressPreview {
    uint32 level = 1;  // 等级 see WeddingLevelType
    uint32 guest_type = 2;  // 嘉宾类型 see WeddingGuestType
    WeddingAnimationInfo wedding_dress = 3;
    string dress_text = 4; // 服装文案
    string wedding_dress_small_icon = 5; // 服装小图, 免费婚礼没有
}

message WeddingDressPreviewList {
    // 初中高三个等级资源, 免费只有一个
    repeated WeddingDressPreview dress_preview_list = 1;
}


message WeddingScenePreview {
    uint32 level = 1;  // 等级 see WeddingLevelType
    WeddingAnimationInfo scene_animation = 2;  // 场景动画
    string small_icon = 3;  // 下方预览小图， 免费婚礼没有
    string wedding_scene_text = 4[deprecated=true];  // 场景文案
    string virtual_character_url = 5[deprecated=true]; // 虚拟人物
    string virtual_character_md5 = 6[deprecated=true]; // 虚拟人物md5
    WeddingAnimationInfo zoom_animation = 7; // [升级预览] 放大动画
}

message FinishWeddingAward {
    WeddingAnimationInfo award_animation = 1;  // 动画资源
    string top_text = 2; // 顶部文案
    string bottom_text = 3; // 底部文案
}

// 价格类型, 与 PRESENT_PRICE_TYPE 保持一致
enum WeddingPriceType {
    WEDDING_PRICE_TYPE_UNSPECIFIED = 0;
    WEDDING_PRICE_TYPE_RED_DIAMOND = 1;  // 红钻
    WEDDING_PRICE_TYPE_T_BEAN = 2; // T豆
}

message WeddingPriceInfo {
    uint32 price = 1;  // 价格
    uint32 price_type = 2;  // 价格类型, see WeddingPriceType
}

// 婚礼主题
message WeddingTheme {
    string name = 1;  // 主题名称
    uint32 price = 2 [deprecated=true];  // 价格
    repeated WeddingScenePreview preview_resource_list = 3;  // [婚礼主题] 婚礼场景预览资源，免费婚礼只有一个
    uint32 theme_id = 4;  // 主题ID,购买时传入
    map<uint32, WeddingDressPreviewList> dress_preview_map = 5;  // [婚礼服装] 等级服装预览  key: see WeddingGuestType
    uint32 max_show_groomsman_num = 6[deprecated=true];  // 最大展示 伴郎（娘）人数
    uint32 max_show_family_num = 7[deprecated=true];  // 最大展示 亲友团人数
    string selected_theme_title_icon = 8;  // 选中时主题标题图标
    string example_photo = 9;  // [婚礼大屏定制] 示例图片
    string reward_info_desc = 19; // 礼成奖励文案
    repeated FinishWeddingAward reward_info_list = 10; // [礼成奖励]
    uint32 max_big_screen_num = 11; // 最大展示大屏幕图片数量
    WeddingPriceInfo price_info = 12;  // 价格信息
    string theme_background = 13;  // 背景图片, 盖在主题资源预览上边，黑色阴影
    string theme_preview_text = 14;  // 左上角固定文案
    string unselected_theme_title_icon = 15;  // 选中时主题标题图标
    string mail_lady_left_bg_icon = 16;  // 信件女在左边背景图标
    string mail_lady_right_bg_icon = 17;  // 信件女在右边背景图标
    WeddingAnimationInfo big_screen_resource = 18; // 定制大屏资源
}

// 伴侣信息
message WeddingPartner {
    UserProfile user_profile = 1;  // 用户信息
    bool is_accepted = 2;  // 是否已接受邀请(未婚夫/妻)，未接受时，显示催一催按钮
    uint32 invite_auto_reject_day = 3;  // 邀请自动拒绝时间,单位天（客户端固定显示，非倒计时）
    int64 end_relationship_deadline = 4;  // 结束关系截止时间戳,单位秒
    uint32 auto_end_relationship_day = 5;  // 自动结束关系时间,单位天
}


enum WeddingAnimationType {
    WEDDING_ANIMATION_TYPE_UNSPECIFIED = 0;
    WEDDING_ANIMATION_TYPE_STATIC = 1;  // 静态图片
    WEDDING_ANIMATION_TYPE_LOTTIE = 2;  // Lottie动画
    WEDDING_ANIMATION_TYPE_VAP = 3;  // vap
}

// 动画资源
message WeddingAnimationInfo {
    uint32 animation_type = 1;  // 动画类型, see WeddingAnimationType
    string animation_resource = 2;  // 动画资源
    string animation_resource_md5 = 3;
    string animation_png = 4;  // 动画png资源
}

enum WeddingLevelType {
    WEDDING_LEVEL_TYPE_UNSPECIFIED = 0;
    // 初级婚礼
    WEDDING_LEVEL_TYPE_PRIMARY = 1;
    // 中级婚礼
    WEDDING_LEVEL_TYPE_INTERMEDIATE = 2;
    // 高级婚礼
    WEDDING_LEVEL_TYPE_ADVANCED = 3;
}

message GetWeddingSchedulePageInfoResponse {
    ga.BaseResp base_resp = 1;
    UserProfile my_info = 2;  // 我的信息
    WeddingPartner partner = 3;  // 伴侣信息, 空则表示未邀请
    uint32 together_days = 4[deprecated=true];  // 在一起天数
    SimpleWeddingPlanInfo wedding_plan_info = 5;  // [婚礼提前筹备] 婚礼安排信息,购买后才返回
    repeated WeddingTheme theme_list = 6[deprecated=true];  // 主题列表, 已废弃，see GetWeddingPreviewResource
    bool is_buyer = 7;  // 是否购买者,购买者才有取消婚礼按钮
    uint32 limit_cancel_wedding_day = 8; // 限制几天前才能取消婚礼
    string theme_title_selected_icon = 9;  // 选中图标
    string theme_title_bg_icon = 10;  // 主题标题背景图标
    string bless_text = 11;  // 购买后首次进入婚礼页时，且未弹过祝福弹窗的祝福文案

}

// 购买婚礼推送
message WeddingPaidNotify {
    UserProfile my_info = 1;  // 新娘信息
    UserProfile partner_info = 2;  // 新郎信息
    string bless_text = 3;  // 祝福文案
    uint32 wedding_plan_id = 4; // 婚礼ID
}

// 取消婚礼并退款
message CancelWeddingRequest {
    ga.BaseReq base_req = 1;
    uint32 wedding_plan_id = 2;  // 婚礼ID
}

message CancelWeddingResponse {
    ga.BaseResp base_resp = 1;
}

enum WeddingChargeType {
    WEDDING_CHARGE_TYPE_UNSPECIFIED = 0;
    WEDDING_CHARGE_TYPE_PAID = 1; // 付费的
    WEDDING_CHARGE_TYPE_FREE = 2; // 免费的
    WEDDING_CHARGE_TYPE_MIX = 3; // 混合的
}

enum WeddingTimeStatus {
    WEDDING_TIME_STATUS_UNSPECIFIED = 0;
    WEDDING_TIME_STATUS_GOING = 1; // 进行中
    WEDDING_TIME_STATUS_COMING = 2; // 即将开始
}

enum WeddingSubscribeStatus {
    WEDDING_SUBSCRIBE_STATUS_UNSPECIFIED = 0;
    WEDDING_SUBSCRIBE_STATUS_SUBSCRIBED = 1; // 已订阅
    WEDDING_SUBSCRIBE_STATUS_NOT_SUBSCRIBED = 2; // 未订阅
}

message WeddingHallItem {
    uint32 wedding_plan_id = 1; // 婚礼ID，用于列表去重
    oneof wedding_view {
        WeddingHallItemGoingView going_view = 2;
        WeddingHallItemNotStartView not_start_view = 3;
    }
    UserProfile male_user_info = 4;
    UserProfile female_user_info = 5;
    string theme_name = 6;
    string background_picture = 7; // 背景图片
    string frame_picture = 8; // 相框图片

    message WeddingHallItemGoingView {
        uint32 channel_id = 1;
        string channel_name = 2;
        string channel_stage_desc = 3; // 当前环节
        int64 channel_hot_value = 4; // 房间热度值
    }

    message WeddingHallItemNotStartView {
        string start_time = 1;
        WeddingSubscribeStatus subscribe_status = 2;
        uint32 channel_id = 3;
        string channel_name = 4;
        int64 wedding_start_time = 5; // 用于处理倒计时
    }
    string hot_label = 9; // 热门标签
    uint32 charge_type = 10; // 婚礼收费类型, see WeddingChargeType
}

message GetWeddingHallListRequest {
    ga.BaseReq base_req = 1;
    WeddingChargeType charge_type = 2;
    WeddingTimeStatus time_status = 3;
    string load_more = 4; // 首次拉取不传, 加载更多时原封不动地填入上一次Resp中的load_more字段
}

message GetWeddingHallListResponse {
    ga.BaseResp base_resp = 1;
    repeated WeddingHallItem wedding_list = 2;
    string load_more = 3; // 下一次加载更多时, 将load_more原封不动地填入请求Req的load_more中; 如果不包含此字段, 表示已经拉完了
}

message SubscribeWeddingRequest {
    ga.BaseReq base_req = 1;
    uint32 wedding_plan_id = 2;
}

message SubscribeWeddingResponse {
    ga.BaseResp base_resp = 1;
}

message GetWeddingEntrySwitchRequest {
    ga.BaseReq base_req = 1;
}

message GetWeddingEntrySwitchResponse {
    ga.BaseResp base_resp = 1;
    // 开关
    bool show_wedding_tab = 2; // 是否显示 我的-婚礼tab（不包括 娱乐页-婚礼子tab）
    bool show_wedding_hall_entry = 3; // 是否显示仪式大厅入口（包括 我的-婚礼tab-仪式大厅/定制婚礼，娱乐页-婚礼子tab-仪式大厅/定制婚礼）
    bool show_wedding_hall_floating_entry = 4; // 是否显示娱乐页 婚礼浮层入口
    // 仪式大厅入口
    string wedding_hall_entry_background = 5;
    string wedding_hall_entry_lottie = 6;
    string wedding_hall_entry_lottie_md5 = 7;
    // 预约入口
    string wedding_reserve_entry_background = 8;
    string wedding_reserve_entry_lottie = 9;
    string wedding_reserve_entry_lottie_md5 = 10;
    // 仪式大厅
    string wedding_hall_background = 11;
    // 仪式大厅内的预约入口
    string wedding_hall_reserve_entry_background = 12;
    string wedding_hall_reserve_entry_hint = 13;
}

enum EndRelationShipSource {
    END_RELATION_SHIP_SOURCE_UNSPECIFIED = 0;
    END_RELATION_SHIP_SOURCE_WEDDING_PAGE = 1; // 婚礼页
    END_RELATION_SHIP_SOURCE_PERSONAL_HOMEPAGE = 2; // 个人主页
}

message ApplyEndWeddingRelationshipRequest {
    ga.BaseReq base_req = 1;
    uint32 partner_uid = 2; // 伴侣uid
    uint32 source = 3; // 来源, see EndRelationShipSource
}

message ApplyEndWeddingRelationshipResponse {
    ga.BaseResp base_resp = 1;
    int64 end_relationship_deadline = 2;  // 结束关系截止时间戳,单位秒
}


// deprecated
message DirectEndWeddingRelationshipRequest {
    ga.BaseReq base_req = 1;
    uint32 partner_uid = 2; // 伴侣uid
    uint32 source = 3; // 来源, see EndRelationShipSource
}

// deprecated
message DirectEndWeddingRelationshipResponse {
    ga.BaseResp base_resp = 1;
}

message CancelEndWeddingRelationshipRequest {
    ga.BaseReq base_req = 1;
    uint32 partner_uid = 2; // 伴侣uid
    uint32 source = 3; // 来源, see EndRelationShipSource
}

message CancelEndWeddingRelationshipResponse {
    ga.BaseResp base_resp = 1;
}


// 求婚状态枚举 Propose
enum ProposeStatus{
    PROPOSE_STATUS_UNSPECIFIED = 0;  // 未申请
    PROPOSE_STATUS_INVITED = 1;      // 已发送申请等待对方回应
    PROPOSE_STATUS_SUCCESS = 2;      // 求婚成功，对方已同意
    PROPOSE_STATUS_FAILED = 3;       // 求婚失败，对方已拒绝
    PROPOSE_STATUS_CANCELED = 4;     // 求婚已撤销
    PROPOSE_STATUS_TIMEOUT = 5;      // 求婚超时结束
}

//获取求婚列表请求
message GetProposeListRequest {
    ga.BaseReq base_req = 1;  //不分页，暂定200个
    string  account = 2;      // 根据 TTID 查询求婚用户信息
}

// 求婚对象信息
message ProposeUser {
    UserProfile user = 1;           //  用户信息
    uint32 status = 2;              // see ProposeStatus
    uint32 fellow_point = 3;      // 挚友值
    string propose_id = 4;         //求婚邀请函id
}

//获取求婚列表响应
message GetProposeListResponse {
    ga.BaseResp base_resp = 1;
    repeated ProposeUser propose_list = 2; // 求婚列表
    repeated string tips = 4;     //tips
}

message SendProposeRequest {
    ga.BaseReq base_req = 1;
    uint32 target_uid = 2;  // 用户ID
    string tips = 3;        //tips
}

message SendProposeResponse {
    ga.BaseResp base_resp = 1;
}

//处理求婚请求
message HandleProposeRequest{
    ga.BaseReq base_req = 1;
    string propose_id = 2;  //求婚邀请函id
    bool is_accept = 3;    // 是否接受求婚
}

message HandleProposeResponse{
    ga.BaseResp base_resp = 1;
}

// 撤回求婚
message RevokeProposeRequest{
    ga.BaseReq base_req = 1;
}

message RevokeProposeResponse{
    ga.BaseResp base_resp = 1;
}



// 求婚函信息
message WeddingProposeInfo {
    string propose_id = 1;            //求婚邀请函id
    UserProfile from_user = 2;        //  用户信息
    UserProfile target_user = 3;    //  对方用户信息
    uint32 status = 4;              // 求婚函当前状态，req不用填 see ProposeStatus
    uint32 create_time = 5;         // 求婚邀请函发送时间
    uint32 end_time = 6;            // 求婚邀请的到期时间
    uint32 expire_day = 7;         // 过期天数
    string tips = 8;               //tips
}

message GetProposeByIdRequest{
    ga.BaseReq base_req = 1;
    string propose_id = 2; //求婚id
}

message GetProposeByIdResponse{
    ga.BaseResp base_resp = 1;
    WeddingProposeInfo propose = 2; //求婚函信息
}

//我发出的求婚函
message GetSendProposeRequest{
    ga.BaseReq base_req = 1;
}

message GetSendProposeResponse{
    ga.BaseResp base_resp = 1;
    WeddingProposeInfo propose = 2;
}

// 结婚证
message WeddingCertificate {
  uint32 wedding_id = 1; // 婚礼ID
  UserProfile groom = 2; // 新郎信息
  UserProfile bride = 3; // 新娘信息
  int64 wedding_time = 4;// 结婚时间
  uint32 wedding_theme_id = 5; // 婚礼主题id
  string pic_url = 6; // 结婚证图片
}

message WeddingScenePic {
  uint32 scene = 1; // 场景, see WeddingScene
  string pic_url = 2; // 场景图片
  int64 create_time = 3; // 创建时间
  string scene_icon = 4;  // 场景icon
}

// 婚礼片段信息
message WeddingClipInfo {
  uint32 theme_id = 1;    // 婚礼主题id
  string theme_name = 2;  // 婚礼主题名称
  uint32 wedding_id = 3;  // 婚礼ID
  repeated WeddingScenePic scene_pic_list = 4; // 场景图片列表
  string channel_name = 5; // 房间名称
}

// 获取用户婚礼沉淀信息请求
message GetUserWeddingPrecipitationRequest {
  ga.BaseReq base_req = 1;
  uint32 target_uid = 2;
}

// 获取我的婚礼沉淀信息响应
message GetUserWeddingPrecipitationResponse {
  ga.BaseResp base_resp = 1;
  WeddingCertificate wedding_certificate = 2; // 结婚证
  repeated WeddingClipInfo wedding_clip_list = 3; // 婚礼片段列表
  repeated WeddingClipInfo group_photo_list = 4;  // 合影列表

  // 5-8 字段仅主态返回，隐藏状态主态也返回沉淀信息
  bool in_divorce_freeze = 5;       // 是否处于离婚冻结期
  int64 divorce_freeze_end_ts = 6;  // 离婚冻结期结束时间戳，秒级
  uint32 hide_status = 7;    // see HideOpType
  int64 divorce_freeze_day = 8;  // 离婚冻结期时长，单位天
}

// 上报婚礼场景片段图片
message ReportWeddingScenePicRequest {
  ga.BaseReq base_req = 1;
  uint32 cid = 2;
  uint32 wedding_id = 3;  // 婚礼id
  uint32 scene = 4;       // 场景, see WeddingScene
  string pic_url = 5;     // 图片url
  string signature = 6;   // 图片签名
}

message ReportWeddingScenePicResponse {
  ga.BaseResp base_resp = 1;
}

// 婚礼场景片段im opt
message WeddingScenePicOpt {
  repeated string pic_list = 1;    // 图片列表
}

enum HideOpType{
    HIDE_OP_TYPE_UNSPECIFIED = 0;
    HIDE_OP_TYPE_HIDE = 1; // 隐藏
    HIDE_OP_TYPE_SHOW = 2; // 显示
  }

// 手动隐藏婚礼关系
message HideWeddingRelationRequest {
    ga.BaseReq base_req = 1;
    uint32 op_type = 2; // 操作类型, see HideOpType
  }
  
message HideWeddingRelationResponse {
  ga.BaseResp base_resp = 1;
  uint32 hide_status = 2; // see HideOpType
}

message GetWeddingPreviewResourceRequest {
  ga.BaseReq base_req = 1;
}

message GetWeddingPreviewResourceResponse {
  ga.BaseResp base_resp = 1;
  repeated WeddingTheme theme_list = 2;  // 主题列表
}

message GetWeddingHighLightPresentRequest {
    ga.BaseReq base_req = 1;
    uint32 cid = 2;
    uint32 wedding_id = 3;  // 婚礼id
}

message GetWeddingHighLightPresentResponse {
    ga.BaseResp base_resp = 1;
    string toast = 2;    //领取结果提示
}

enum WeddingReserveIMStatus {
    WEDDING_RESERVE_IM_STATUS_UNSPECIFIED = 0;
    WEDDING_RESERVE_IM_STATUS_VALID = 1; // 有效
    WEDDING_RESERVE_IM_STATUS_EXPIRED= 2; // 过期
}

// 咨询婚礼预约IM消息
message ConsultWeddingReserveIMMsg {
    ReserveInfo reserve_info = 1; // 预约信息
    ga.UserProfile groom = 2; // 新郎信息
    ga.UserProfile bride = 3; // 新娘信息
    bool is_arranged = 4; // 是否已安排
    uint32 status = 5; // 状态, see WeddingReserveIMStatus
    uint32 msg_id = 6; // 消息ID
}

// 客服安排婚礼预约IM消息
message ArrangeWeddingReserveIMMsg {
    ReserveInfo reserve_info = 1; // 预约信息
    uint32 price = 2; // 价格
    repeated WeddingGiftInfo gift_list = 3; // 礼物列表
    bool is_paid = 4; // 是否已支付
    uint32 status = 5; // 状态, see WeddingReserveIMStatus
    uint32 msg_id = 6; // 消息ID
    uint32 pay_valid_hour = 7; // 支付有效时间
}

message WeddingGiftInfo {
    string gift_icon = 1; // 礼物icon
    string gift_name = 2; // 礼物名称
    string gift_desc = 3; // 礼物价格
}

message WeddingBridesmaidUpdateOpt {
    repeated uint32 bridesmaid_man_list = 1;  // 伴郎伴娘uid列表
}

message SendWeddingReservePresentRequest {
    ga.BaseReq base_req = 1;
    uint32 cid = 2;
    uint32 wedding_id = 3;  // 婚礼id
}

message SendWeddingReservePresentResponse {
    ga.BaseResp base_resp = 1;
}

// 批量查询用户的在房状态
message GetUserInRoomStatusRequest {
    ga.BaseReq base_req = 1;
    uint32 cid = 2;
    repeated uint32 uid_list = 3; // 用户ID列表
}

message UserInRoomStatus {
    ga.UserProfile user = 1; // 用户信息
    bool in_room = 2; // 是否在房间内
}

message GetUserInRoomStatusResponse {
    ga.BaseResp base_resp = 1;
    repeated UserInRoomStatus user_list = 2; // 用户在房间内状态列表
}

message GetGoingWeddingEntryRequest {
    ga.BaseReq base_req = 1;
    uint32 page = 2; // 分页（结婚是本人或者是好友的情况）
}

message UserInfoWithChannel {
    UserProfile user_info = 1; // 用户信息
    uint32 channel_id = 2; // 房间ID
}

message GetGoingWeddingEntryResponse {
    ga.BaseResp base_resp = 1;
    UserInfoWithChannel bride = 2; // 新娘信息(结婚是本人或者是好友的情况)
    UserInfoWithChannel groom = 3; // 新郎信息(结婚是本人或者是好友的情况)
    repeated UserInfoWithChannel other_user_list = 4; // 其他结婚用户列表（无好友关系）
    string main_title = 5; // 标题
    string sub_title = 6; // 副标题
    uint32 next_page_num = 7; // 下一页, 如果没有下一页, 返回0（结婚是本人或者是好友的情况）
}

// 提醒用户进房
message RemindUserJoinWeddingRoomRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2; // 房间ID
    uint32 target_uid = 3; // 目标用户ID
}

message RemindUserJoinWeddingRoomResponse {
    ga.BaseResp base_resp = 1;
}

/********* 礼物计数器及mvp相关 *********/
// 婚礼收送礼值
message WeddingPresentVal {
  uint32 uid = 1; // 用户ID
  uint32 present_val = 2; // 收礼值
}

message WeddingPresentLevel {
  uint32 uid = 1; // 用户ID
  uint32 lv= 2;   // 等级
}

// 婚礼收送礼物值信息(计数器)
message WeddingPresentCountInfo {
  repeated WeddingPresentVal user_present_val_list = 1; // 用户收礼值列表
  uint32 mvp_uid = 2; // mvp用户
  WeddingPresentLevel top_recv_present_lv = 3; // 收礼值第一名等级
}

// 婚礼收礼值变化通知
message WeddingRecvPresentValChangeOpt {
  uint32 channel_id = 1;
  uint32 wedding_id = 2; // 婚礼ID
  int64 server_ms = 3; // 服务器时间戳，用来避免乱序。单位毫秒
  repeated WeddingPresentVal user_present_val_list = 4; // 变化用户收礼值列表
}

// 婚礼收送礼物值前N名变化通知
message WeddingPresentValTopChangeOpt {
  uint32 channel_id = 1;
  uint32 wedding_id = 2; // 婚礼ID
  int64 server_ms = 3; // 服务器时间戳，用来避免乱序。单位毫秒
  WeddingPresentLevel top_recv_present_lv = 4; // 收礼值第一名等级
}

// 婚礼收送礼物值mvp变化通知
message WeddingPresentValMvpChangeOpt {
  uint32 channel_id = 1;
  uint32 wedding_id = 2; // 婚礼ID
  int64 server_ms = 3; // 服务器时间戳，用来避免乱序。单位毫秒
  uint32 mvp_uid = 4;  // mvp用户
}

// 婚礼结束mvp用户结算通知
message WeddingMvpUserSettlementNotify {
  uint32 channel_id = 1; // 房间ID
  uint32 wedding_id = 2; // 婚礼ID
  int64 server_ms = 3; // 服务器时间戳，用来避免乱序。单位毫秒
  UserProfile mvp_user = 4; // mvp用户
  string resource_url = 5; // mvp资源url
  string resource_md5 = 6; // mvp资源md5
  repeated uint32 user_inuse_item_list = 7; // 用户使用的虚拟形象物品列表
  uint32 mvp_pose_id = 8; // mvp姿势id
}

// 上mvp麦
//message ChannelWeddingHoldMvpMicRequest {
//  ga.BaseReq base_req = 1;
//  uint32 cid = 2;  // 房间ID
//}
//
//message ChannelWeddingHoldMvpMicResponse {
//  ga.BaseResp base_resp = 1;
//}

// ================================ 优化4.0 婚礼前流程 ========================

// 获取婚礼礼物
message GetWeddingPresentRequest {
    ga.BaseReq base_req = 1;
}

message GetWeddingPresentResponse {
    ga.BaseResp base_resp = 1;
    repeated uint32 gift_id_list = 2;
}

// 获取婚礼前进度面板
message GetWeddingPreProgressInfoRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;  // 房间ID
}

message GetWeddingPreProgressInfoResponse {
    ga.BaseResp base_resp = 1;
    WeddingPreProgressInfo pre_progress_info = 2; // 婚礼准备信息
}

enum WeddingPreProgressStage {
    WEDDING_PRE_PROGRESS_STAGE_UNSPECIFIED = 0;
    WEDDING_PRE_PROGRESS_STAGE_ON_MIC = 1; // 上麦
    WEDDING_PRE_PROGRESS_STAGE_SEND_GIFT = 2; // 赠送礼物
    WEDDING_PRE_PROGRESS_STAGE_WEDDING_PREPARE = 3; // 婚礼准备
}

message WeddingPreProgressStageCfg {
    uint32 stage = 1; // see WeddingPreProgressStage
    string stage_name = 2; // 阶段名称
    WeddingPrePreProgressStageDescGroup default_desc = 3; // 默认阶段描述
    WeddingPrePreProgressStageDescGroup done_desc = 4; // 完成阶段描述
}

message WeddingPrePreProgressStageDescGroup {
    WeddingPreProgressStageDesc host_desc = 3; // 主持人描述
    WeddingPreProgressStageDesc newcomers_desc = 4; // 新人描述
    WeddingPreProgressStageDesc audience_desc = 5; // 观众描述
}

message WeddingPreProgressStageDesc {
    string desc_title  = 1; // 阶段描述标题
    string desc = 2; // 阶段描述
}


message WeddingNewcomer {
    UserProfile user = 1; // 新人信息
    uint32 value = 2; // 礼物计数器
}

message WeddingPreProgressInfo {
    uint32 stage = 1; // 阶段, see WeddingPreProgressStage
    uint32 wedding_plan_id = 2; // 婚礼计划ID
    repeated WeddingNewcomer newcomer_list = 3; // 新人列表
    string panel_resource_url = 4; // 面板资源url
    string panel_resource_md5 = 5; // 面板资源md5
    repeated WeddingPreProgressStageCfg stage_cfg_list = 6; // 阶段配置, 仅主动get时候返回, push不返回
}

// 婚礼准备信息变更推送 WEDDING_PRE_PROGRESS_UPDATE_PUSH = 473
message WeddingPreProgressInfoChangeOpt {
    WeddingPreProgressInfo pre_progress_info = 1; // 婚礼准备信息
    uint32 server_ts = 2; // 服务器时间戳，用来避免乱序。单位毫秒
}

message StartWeddingRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2; // 房间ID
    uint32 wedding_plan_id = 3; // 婚礼计划ID
}

message StartWeddingResponse {
    ga.BaseResp base_resp = 1;
}

// 取消婚礼
message CancelPreparedWeddingRequest {
    ga.BaseReq base_req = 1;
    uint32 wedding_plan_id = 2; // 婚礼计划ID
    uint32 channel_id = 3; // 房间ID
}

message CancelPreparedWeddingResponse {
    ga.BaseResp base_resp = 1;
}

// 获取婚礼准备信息
message GetWeddingPrepareInfoRequest {
    ga.BaseReq base_req = 1;
    uint32 wedding_plan_id = 2; // 婚礼计划id
}

message GetWeddingPrepareInfoResponse {
    ga.BaseResp base_resp = 1;
    SimpleWeddingPlanInfo plan_info = 2;
}
