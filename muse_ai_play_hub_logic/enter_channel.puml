@startuml
'https://plantuml.com/sequence-diagram

group 监听用户进房事件
autonumber
Event -> Achieve服务: 接收进房事件
Achieve服务 -> Achieve服务:查询房主社团表
Achieve服务 -> muse_social_community服务:查询用户所在社群
muse_social_community服务 --> Achieve服务: 返回用户所在社群
Achieve服务 -> Achieve服务:遍历用户社群，做社群对应人数的映射
Achieve服务 -> Achieve服务:遍历房主社群列表，将房主社团表对应社群数量+1
Achieve服务 -> Achieve服务:将用户存储到房间用户的存储表
Achieve服务 -> Achieve服务:将用户共有社群存储到房主共同社团表

end
@enduml



@startuml
'https://plantuml.com/sequence-diagram



