 #pragma once

#include "protodef.h"

// 所有命令定义, ;和注释之间必须有SPACE字符, 否则会影响代码生成

/////////////////////////////////////////////
/** CMD_LIST BEGIN **/	// don't remove this

// 500 - 505 为服务器内部命令

const unsigned int CMD_PING		= 1;        // 心跳
const unsigned int CMD_QUIT		= 2;        // 退出登录
const unsigned int CMD_NOTIFY	= 4;		// Server 通知client有更新
const unsigned int CMD_KICKOUT	= 5; 		// Server 通知client被踢出， 请不要重连. 重连将死循环
const unsigned int CMD_PUSH		= 6;		// 主动推送请求

const unsigned int CMD_SessionKeepAlive	= 7;	// 实时语音心跳

// 8是运维公告命令, 跳过
const unsigned int CMD_TransmissionPush = 9;    // 透传推送

const unsigned int CMD_AUTH     = 10;           // 登录
const unsigned int CMD_SENDMSG  = 11;           // 发送IM消息
const unsigned int CMD_REG        = 12;         //注册
const unsigned int CMD_USERREGCOMPLETE  = 13;   //完善注册资料
const unsigned int CMD_AccountVerifyCode = 14;  //获取短信验证码 (登录/注册)
const unsigned int CMD_SubmitVerifyCode = 232;  //提交短信验证码 (登录/注册)
const unsigned int CMD_RestPWD          = 15;   //重置密码
const unsigned int CMD_MODIFYPWD        = 16;   //改密码
const unsigned int CMD_ModifyUserName       = 17; //改账号
const unsigned int CMD_CheckPhoneExist      = 18; //检测手机账号是否存在
const unsigned int CMD_RegExistPhone        = 19; //已存在用户注册的立刻登录(重置密码 + 登录)


const unsigned int CMD_UserUploadFace       = 20;  //上传头像
const unsigned int CMD_GetSmallFace         = 21;  //获取小头像
const unsigned int CMD_GetBigFace           = 22;  //获取大头像
const unsigned int CMD_UploadAttachment     = 23;  //上传附件
const unsigned int CMD_DeleteFriend         = 24;  //删除好友
const unsigned int CMD_DeleteMessage        = 25;  //删除消息
const unsigned int CMD_MarkMsgRead          = 26;      //设置消息已读
const unsigned int CMD_GetUserDetail        = 27;      //GetUserDetail获取用户详细信息
const unsigned int CMD_GetCover             = 28;      //获取用户封面
const unsigned int CMD_GetUserGames         = 29;       //获取用户安装游戏
const unsigned int CMD_AddFriend            = 30;     //添加好友
const unsigned int CMD_VerifyFriend         = 31;   //添加好友验证
const unsigned int CMD_Sync                 = 32;       //同步消息
const unsigned int CMD_RemarkFriend = 33; //备注好友
const unsigned int CMD_BanFriend = 34; //屏蔽好友
const unsigned int CMD_MarkFriendWithStar = 35; //标记好友为星标好友
const unsigned int CMD_SearchContact = 36;  //搜索联系人

const unsigned int CMD_RegResetAccount = 37; //重置账号+登录
const unsigned int CMD_DownloadAttachment = 38;		// 下载附件

const unsigned int CMD_GuildCreate = 					39; //创建公会
const unsigned int CMD_GuildGetInfo = 					40; //获取公会信息
const unsigned int CMD_GuildModifyInfo = 				41; //修改公会信息
const unsigned int CMD_GuildSearch = 					42; //搜索公会
const unsigned int CMD_GuildAlbumCreate = 				43; //创建公会相册
const unsigned int CMD_GuildGetAlbumList = 				44; //获取公会相册列表
const unsigned int CMD_GuildGetPhotoList = 				45; //获取公会相册照片列表
const unsigned int CMD_GuildAddGame = 					47;	//添加主玩游戏
const unsigned int CMD_GuildJoin = 						48;	//申请加入公会
const unsigned int CMD_GuildQuit =                      49; //退出公会
const unsigned int CMD_GuildDismiss = 					50; //解散公会
const unsigned int CMD_GuildHandleJoin = 				51; //审核加入全公会的申请
const unsigned int CMD_GuildGetGameInfo = 				52; //获取公会游戏信息
const unsigned int CMD_GuildGetGameGroupList = 			53; //获取公会游戏群列表
const unsigned int CMD_GuildCreateGameGroup = 			54; //创建公会游戏群
const unsigned int CMD_GuildDismissGameGroup = 			55; //解散公会游戏群
const unsigned int CMD_GuildGetGameGroupInfo = 			56; //获取公会游戏群信息
const unsigned int CMD_GuildJoinGameGroup = 			57; //申请加入公会游戏群
const unsigned int CMD_GuildQuitGameGroup = 			58; //退出公会游戏群
const unsigned int CMD_GuildGetGroupMemberList = 	    59; // [已经下线废弃的业务] 1.8.1 开始不用了
const unsigned int CMD_GuildAddGroupAdmin = 		    60; // 添加公会游戏群管理员
const unsigned int CMD_GuildGroupMute = 			    61; // 禁言公会游戏群成员

const unsigned int CMD_ModifyNickname =					62; //修改用户昵称
const unsigned int CMD_ModifySignature = 				63; //修改用户签名
const unsigned int CMD_ModifyVerify	=					64; //修改加好友需要验证的标志
const unsigned int CMD_GuildGetGamePackageList =        65; //获取公会游戏包列表
const unsigned int CMD_GuildGetGameGuideList =          66; //获取公会游戏攻略列表
const unsigned int CMD_GuildGetApplyStatusList =        67; //获取公会申请状态列表
const unsigned int CMD_ModifyPrefixValid =				68; //用户设置是否使用马甲

const unsigned int CMD_GuildDeleteAlbum =               69; //删除公会相册
const unsigned int CMD_GuildDeletePhoto =               70; //删除公会相册照片
const unsigned int CMD_GuildDeleteGroupAdmin =          71; //删除公会游戏群管理员
const unsigned int CMD_GuildGroupUnmute =               72; // 解除禁言公会游戏群成员

const unsigned int CMD_CheckVerifyCode				= 73; // 校验检验码
const unsigned int CMD_CheckSyncKey					= 74; // 检查所有可用的sync key
const unsigned int CMD_GroupAddMember        		= 75; // 临时群增加成员
const unsigned int CMD_GroupRemoveMember     		= 76; // 临时群移除成员
const unsigned int CMD_GroupModifyName       		= 77; // 修改临时群昵称
const unsigned int CMD_GroupGetMemberList      		= 78; // 取临时群成员列表

const unsigned int CMD_UploadUserGames				= 79; // 上传用户安装的游戏列表

const unsigned int CMD_ModifySex					= 80; //修改用户性别
const unsigned int CMD_GuildCheckin                 = 81; // 公会签到
const unsigned int CMD_GuildGetCheckinList          = 82; // 获取公会签到列表

const unsigned int CMD_HotGames          			= 83; // 获取热门游戏列表
const unsigned int CMD_GuildAddAdmin				= 84; // 添加公会管理员
const unsigned int CMD_GuildDeleteAdmin             = 85; //  删除公会管理员
const unsigned int CMD_GuildSetGroupOwner           = 86; // 设置群主
const unsigned int CMD_GuildApplyJoinGroup          = 87;//申请加入公会群
const unsigned int CMD_GuildHandleJoinGroup         = 88;//处理公会群申请
const unsigned int CMD_GuildAddGroupMember          = 89;//添加公会群成员
const unsigned int CMD_GuildDeleteGroupMember       = 90;//删除公会群成员
const unsigned int CMD_GuildDeleteMember            = 91;//删除公会成员
const unsigned int CMD_GuildGetNoticeList           = 92;//获取公会公告列表
const unsigned int CMD_SearchGame					= 93;//搜索游戏
const unsigned int CMD_GuildGetGroupMember			= 94; // 1.8.1 开始不用了

const unsigned int CMD_FeaturedGames				= 95;//获取推荐游戏列表

const unsigned int CMD_GuildModifyGroupName			= 96;//修改公会群名称
const unsigned int CMD_GuildModifyGroupMemberCard	= 97; //修改公会群成员卡片
const unsigned int CMD_GuildModifyGroupVerify       = 98;//修改公会群验证方式
const unsigned int CMD_GameGuildList				= 99; // 根据游戏id获取公会列表

const unsigned int CMD_GuildPublishNotice			= 100;	//发布公告 已经废弃
const unsigned int CMD_GuildModifyNotice			= 101;	//修改公告
const unsigned int CMD_GuildDeleteNotice			= 102;  //删除公告

const unsigned int CMD_GuildModifyMemberRemark		= 103;  //修改公会成员称号
const unsigned int CMD_GuildEnablePrefix			= 104;  //公会成员打开/关闭公会马甲的显示
const unsigned int CMD_GuildModifyPermission		= 105;  //修改管理员自定义权限
const unsigned int CMD_GuildGroupSetAllMute			= 106;  //公会群全体禁言
const unsigned int CMD_GuildUploadVoice				= 107;  //上传公会语音
const unsigned int CMD_GuildDownloadVoice			= 108;  //下载公会语音

const unsigned int CMD_GuildModifyAlbumName 		= 109; //修改公会相册名称
const unsigned int CMD_GuildGetMyGuildAlbumList 	= 110; //获取我的公会相册列表
const unsigned int CMD_GuildAddNewPhoto 			= 111; //上传公会相册照片
const unsigned int CMD_GuildGetPhotoNewsList 		= 112; //获取公会相册照片列表
const unsigned int CMD_GuildDeletePhotoNews 		= 113; //删除公会相册照片
const unsigned int CMD_GuildModifyGameOrder			= 114; //修改公会游戏排序
const unsigned int CMD_GuildDeleteGroupOwner		= 115; //删除公会群主

const unsigned int CMD_GetMyGuildGiftpkgList		= 116; //查看我的公会的礼包列表
const unsigned int CMD_GetMyGiftpkgList     		= 117; //查看我的礼包列表 (2.9.0以后版本不再更新数据);
const unsigned int CMD_FetchGuildGiftpkg    		= 118; //会员领取公会礼包
const unsigned int CMD_FetchRedpkg     		        = 119; //会员抢红包
const unsigned int CMD_SendGuildRedpkg     		    = 120; //会长发红包
const unsigned int CMD_GetGuildRedpkgDetail     	= 121; //查红包详情
const unsigned int CMD_GetGuildRedpkgFetchDetail    = 122; //查红包领取详情
const unsigned int CMD_GuildTaohao                  = 123; //淘号
const unsigned int CMD_GuildGetAppliableGiftpkg     = 124; //查询公会可申请的礼包
const unsigned int CMD_GuildApplyGiftpkg            = 125; //公会申请礼包
const unsigned int CMD_GetGiftpkgDetail             = 126; //公会查询礼包详情
const unsigned int CMD_GetGiftpkgApplyHistory       = 127; //公会查询申请礼包历史记录
const unsigned int CMD_GuildModifyGameUrl           = 128;
const unsigned int CMD_GetGuildStorageGiftpkgList	= 129;	// 公会查询礼包仓库

const unsigned int CMD_GuildModifyVerify			= 130;	// 修改加入公会是否需要验证的标志
const unsigned int CMD_GuildGetGameDownloadUrl		= 131;	// 设置游戏下载链接
const unsigned int CMD_GuildGetGameList				= 132;	// 查公会游戏列表
const unsigned int CMD_ReportUseTaohao				= 133;	// 上报使用淘号
const unsigned int CMD_GuildSetUseCustomUrl			= 134;	// 开启/关闭自定义下载链接
const unsigned int CMD_GuildOwnerBc					= 135;	// 公会会长广播

const unsigned int CMD_GetGiftpkgApplyDetail        = 136; //查公会礼包申请详情
const unsigned int CMD_GuildQuickJoin               = 137; //快速加公会, 一般在公会专用包上用

const unsigned int CMD_GuildGetDefaultAlbumPhotoList	= 138;	// 查询默认相册的照片列表

const unsigned int CMD_GameMatchUserGameList		= 139;	// 客户端游戏名匹配服务器游戏
const unsigned int CMD_GuildMemberGameDownloadReport = 140;   //公会成员下载公会游戏后上报
const unsigned int CMD_GuildGetMemberGameDownloadList = 141;   //有下载游戏成员列表

const unsigned int CMD_GameCircleGetMyCircle        = 142;        // [圈子业务已经下线]  查询我的游戏圈 //
const unsigned int CMD_GameCircleGetTopList         = 143;        // [圈子业务已经下线] 推荐游戏圈列表
const unsigned int CMD_GameCircleJoin               = 144;        // [圈子业务已经下线]加入游戏圈
const unsigned int CMD_GameCircleQuit               = 145;        // [圈子业务已经下线]  退出游戏圈
const unsigned int CMD_GameCircleGetTopicList       = 146;        // [圈子业务已经下线]获取topic列表
const unsigned int CMD_GameCircleGetTopic           = 147;        // [圈子业务已经下线]获取某条topic详情
const unsigned int CMD_GameCircleSendTopic          = 148;        // [圈子业务已经下线]发表主题
const unsigned int CMD_GameCircleGetCommentList     = 149;        // [圈子业务已经下线]获取主题评论列表
const unsigned int CMD_GameCircleSendComment        = 150;        // [圈子业务已经下线]发表评论
const unsigned int CMD_GameCircleLikeTopic          = 151;        // [圈子业务已经下线]赞/取消赞
const unsigned int CMD_GameCircleReportTopic        = 152;        // [圈子业务已经下线]举报主题
const unsigned int CMD_GameCircleDeleteTopic        = 153;        // [圈子业务已经下线]删除主题
const unsigned int CMD_GameCircleDeleteComment      = 154;        // [圈子业务已经下线]删除评论
const unsigned int CMD_GameCircleGetCircle          = 155;        // [圈子业务已经下线]获取某个游戏圈信息
const unsigned int CMD_GameCircleGetUserTopic       = 156;        // [圈子业务已经下线]获取某人发表的主题
const unsigned int CMD_GameCircleMarkMsgRead        = 157;        // [圈子业务已经下线]标记已读

const unsigned int CMD_ReportCrash                  = 158;        //发崩溃报告
const unsigned int CMD_GuildSetGroupNotRecvMsg      = 159;        //完全不接收某个群的消息
const unsigned int CMD_GameCircleGetLikeUserList    = 160;        //赞列表
const unsigned int CMD_CheckUpgrade					= 161;		  //检测版本升级

const unsigned int CMD_AppReport					= 162;		// 举报
const unsigned int CMD_UploadLog					= 163;			// 上报日志

const unsigned int CMD_GameCircleMuteUser 			= 164;   //  禁言游戏圈
const unsigned int CMD_GameCircleUnmuteUser 		= 165;   //  解除禁言游戏圈

const unsigned int CMD_GetGameConfig            = 166;    //获取浮窗游戏黑白名单
const unsigned int CMD_GuildPlayingGame			= 167;		// 公会正在玩的游戏
const unsigned int CMD_GuildDeleteGuildGame		= 168;		// 删除公会游戏
const unsigned int CMD_GuildModifyGameGroupOrder = 169;		// 修改公会游戏排序
const unsigned int CMD_TopGuildList				= 170;		// 公会排行榜
const unsigned int CMD_GetGuildGiftpkgPrice			= 171;	// 查礼包价格
const unsigned int CMD_FetchGiftpkgCostRedDiamond	= 172;	// 红钻领取礼包
const unsigned int CMD_AppBehaviorReport	        = 173;	// App行为上报

const unsigned int CMD_BatGetUserGrowInfo			= 174;	// 批量获取用户成长信息

const unsigned int CMD_ReportSyncRead				= 175;	// 上报SyncKey已读

// v1.5
////圈子
const unsigned int CMD_GameCircleCheckCircleUpdate		= 176;	// [圈子业务已经下线]检查圈子最新贴子变化
const unsigned int CMD_GameCircleSetMyCircleOrder		= 177;	// [圈子业务已经下线]设置我的圈子顺序
const unsigned int CMD_GameCircleHighlightTopic			= 178;	// [圈子业务已经下线]加精
const unsigned int CMD_GameCircleCancelHighlightTopic 	= 179;	// [圈子业务已经下线]取消加精
const unsigned int CMD_GameCircleManagerDeleteComment	= 180;	// [圈子业务已经下线]管理员删除评论

////公众号
const unsigned int CMD_OfficialAccountUpdateSettings 	= 181;	// 公众号接收消息
const unsigned int CMD_SubscribeOfficialAccount		    = 182;	// 关注公众号（预留）
const unsigned int CMD_UnsubscribeOfficialAccount       = 183;	// 取消关注公众M号
const unsigned int CMD_OfficialAccountGetNewestMessage	= 184;	// 拉取最新消息
const unsigned int CMD_OfficialAccountGetDetail			= 185;	// 获取公众号详情

//勋章尾灯
const unsigned int CMD_SetTaillightMedal			= 191;	// 设置勋章尾灯
const unsigned int CMD_GetMessagePeerReadStatus		= 192;	// 获取im消息的对方已读状态

/* 头像挂饰 */
const unsigned int CMD_HeadwearSetInUse = 193;             // [已经下线废弃的业务]用户设置当前使用挂饰
const unsigned int CMD_HeadwearGetUserHeadwear = 194;      // [已经下线废弃的业务]获取用户拥有的挂饰
const unsigned int CMD_HeadwearRemoveInUse = 195;          // [已经下线废弃的业务]停用挂饰

//删除信息
const unsigned int CMD_BatchDelMsg = 196;//删除信息

// 点击查看个人资料页风控检查
const unsigned int CMD_ClickUserDetailPreCheck = 197; // 点击查看个人资料页风控检查

// 统计
const unsigned int CMD_ClientReportStatis				= 200;	// 客户端上报统计
const unsigned int CMD_IncreaseGamePackageDownloadCount = 201;	//	客户报上报游戏包下载统计(+1)
const unsigned int CMD_GuildGroupGetMuteList			= 202;	// 公会群查禁言列表


/** 第三方登录协议 BEGIN **/
const unsigned int CMD_ThirdPartyAuth               = 210; //三方认证登录
const unsigned int CMD_ThirdPartyReg                = 211; //ThirdPartyReg三方认证注册
const unsigned int CMD_ChinaMobileAuth              = 212; //[已经下线废弃的业务]中国移动一键登录
const unsigned int CMD_ChinaMobileReg               = 213; //[已经下线废弃的业务]中国移动一键注册
const unsigned int CMD_ThirdPartyVerifyCheck        = 214; //WX/QQ/三方 操作验证
const unsigned int CMD_ChinaUnicomAuth              = 215; //创蓝 中国联通一键登录
const unsigned int CMD_ChinaUnicomReg               = 216; //创蓝 中国联通一键注册

/** 第三方登录协议 END **/

/** 实名认证 BEGIN**/

const unsigned int CMD_GetRealNameAuthState         = 220;  // 获取实名认证状态
const unsigned int CMD_GetRealNameAuthToken         = 221;  // 得到faceId token
const unsigned int CMD_ApplyRealNameAuthCheckData        = 222;  // 提交实名认证数据
const unsigned int CMD_GetRealNameFaceIdTokenByUid    = 223;  //已实名用户通过uid获取faceid_token
const unsigned int CMD_CheckUserIdentifyInfo   = 224;   //验证用户的相关实名信息
const unsigned int CMD_GetFaceAuthAliToken = 225;   // 通过阿里接口获取人脸 certifyId token
const unsigned int CMD_GetFaceAuthAliResult = 226;   // 获取阿里人脸识别认证结果
const unsigned int CMD_GetFaceAuthAliTokenByUid = 227;  //已实名用户通过uid获取阿里人脸certifyid token
const unsigned int CMD_CheckFaceByAliToken = 228;        // 验证人脸是否通过
const unsigned int CMD_AuthByTwoElement = 229;        // 二元素实名认证 (身份证和姓名)


/** 实名认证 END**/


const unsigned int CMD_CancelMsg					= 230;  // 删除IM消息
const unsigned int CMD_GetUserCertification         = 231;  //获取用户官方认证
// 232占用
const unsigned int CMD_GetUserCertifyList           = 233;  //获取用户官方认证列表
const unsigned int CMD_SetUserWearCertification     = 234;  //设置用户佩戴的大v认证样式


/** 实时语音协议 BEGIN **/
const unsigned int CMD_SessionCreateInGroup				= 250;	// [已经下线废弃的业务]创建实时语音会话
const unsigned int CMD_SessionJoin						= 251;	// [已经下线废弃的业务]加入实时语音会话
const unsigned int CMD_SessionQuit						= 252;	// [已经下线废弃的业务]退出实时语音会话
const unsigned int CMD_SessionReportUpdate				= 253;	// [已经下线废弃的业务]上报实时语音会话状态
const unsigned int CMD_SessionCreateIn1v1				= 254;	// [已经下线废弃的业务]1V1创建实时语音
const unsigned int CMD_SessionCallUp					= 255;	// [已经下线废弃的业务]开黑语音召集令
/** 实时语音协议 END **/



/** 新圈子协议 【废弃】 BEGIN **/
const unsigned int CMD_CircleGetList                = 300; // [圈子业务已经下线]【废弃】
const unsigned int CMD_CircleGetTopicList           = 303; // [圈子业务已经下线]【废弃】
const unsigned int CMD_CircleGetTopic               = 304; // [圈子业务已经下线]  【废弃】
const unsigned int CMD_CirclePostTopic              = 305; // [圈子业务已经下线]【废弃】
const unsigned int CMD_CircleGetCommentList         = 306; // [圈子业务已经下线] 旧版获取帖子评论列表 【废弃】
const unsigned int CMD_CirclePostComment            = 307; // [圈子业务已经下线]
const unsigned int CMD_CircleGetCircleDetail        = 308; // [圈子业务已经下线]
const unsigned int CMD_CircleGetUserTopicList       = 309; // [圈子业务已经下线]
const unsigned int CMD_CircleGetTopicLikeList       = 310; // [圈子业务已经下线]
const unsigned int CMD_GameCircleMuteReasonList     = 311; // [圈子业务已经下线]
const unsigned int CMD_CircleGetCommentListV2       = 312; // [圈子业务已经下线] 新版获取帖子评论列表 带楼层和回复列表数据
const unsigned int CMD_CircleGetCommentReplyList    = 313; // [圈子业务已经下线] 新版获取帖子内指定评论的回复列表
const unsigned int CMD_CircleGetActivity            = 314; // [圈子业务已经下线] 圈子里的活动 【废弃】
const unsigned int CMD_CircleGetHot                 = 315; // [圈子业务已经下线] 热门圈子
const unsigned int CMD_GameCircleGetAnn             = 316; // [圈子业务已经下线] 游戏里的公告 【废弃】



/** 运营游戏 BEGIN **/
const unsigned int CMD_TopGameGetList		 		= 320; // [已经下线废弃的业务]
const unsigned int CMD_TopGameGetDownloadUrl		= 321; // [已经下线废弃的业务]
const unsigned int CMD_GameTabGetList				= 322; // [已经下线废弃的业务]
const unsigned int CMD_GameTabGetActAdList          = 323; // [已经下线废弃的业务]
const unsigned int CMD_GameGetDiscoveryList         = 324; // [已经下线废弃的业务]
/** 运营游戏 END **/

/** 下载管理器 BEGIN **/
const unsigned int CMD_CheckGameUpgrade 			= 330; // 检查游戏升级
/** 下载管理器 END **/



/** 运营游戏V2 BEGIN **/
const unsigned int CMD_GameTabGetListV2 = 340;            //[已经下线废弃的业务] 游戏中心内容 大杂烩拉取
const unsigned int CMD_GameGetDiscoverPageContent = 341;  //[已经下线废弃的业务] 拉取限时任务 VIP专区这一部分的主副标题
const unsigned int CMD_GameGetGameByTag = 342;            //[已经下线废弃的业务] 根据游戏分类拉取游戏
const unsigned int CMD_GameGetGameTag   = 343;            //[已经下线废弃的业务] 拉取游戏分类
const unsigned int CMD_GameGetPopGame   = 344;            //[已经下线废弃的业务] 开黑及好友热玩
/** 运营游戏V2 END **/


// 以下协议字段未变, 复用旧命令
const unsigned int CMD_CircleJoin                   = CMD_GameCircleJoin;  // [圈子业务已经下线]
const unsigned int CMD_CircleQuit                   = CMD_GameCircleQuit;  // [圈子业务已经下线]
const unsigned int CMD_CircleLikeTopic              = CMD_GameCircleLikeTopic;  // [圈子业务已经下线]
const unsigned int CMD_CircleReportTopic            = CMD_GameCircleReportTopic;// [圈子业务已经下线]
const unsigned int CMD_CircleDeleteTopic            = CMD_GameCircleDeleteTopic;// [圈子业务已经下线]
const unsigned int CMD_CircleDeleteComment          = CMD_GameCircleDeleteComment;// [圈子业务已经下线]
const unsigned int CMD_CircleMarkReaded             = CMD_GameCircleMarkMsgRead;  // [圈子业务已经下线]
const unsigned int CMD_CircleMuteUser               = CMD_GameCircleMuteUser;     // [圈子业务已经下线]
const unsigned int CMD_CircleUnmuteUser             = CMD_GameCircleUnmuteUser;   // [圈子业务已经下线]
const unsigned int CMD_CircleSetMyCircleOrder       = CMD_GameCircleSetMyCircleOrder; // [圈子业务已经下线]
const unsigned int CMD_CircleHighlightTopic         = CMD_GameCircleHighlightTopic;   // [圈子业务已经下线]
const unsigned int CMD_CircleCancelHighlightTopic   = CMD_GameCircleCancelHighlightTopic; // [圈子业务已经下线]
const unsigned int CMD_CircleManagerDeleteComment   = CMD_GameCircleManagerDeleteComment; // [圈子业务已经下线]
const unsigned int CMD_CircleCheckUpdate            = CMD_GameCircleCheckCircleUpdate;    // [圈子业务已经下线]

/** 新圈子协议 END **/

/** 新公会协议 BEGIN **/
const unsigned int CMD_GuildGetBaseInfo        = 349; // 获取公会基本信息

const unsigned int CMD_GuildGetStarLevel	     = 350; // 获取公会星级信息
const unsigned int CMD_GuildGetGroupMemberListV2 = 351; // 1.8.1新增 拉取公会群成员列表
const unsigned int CMD_GuildGetMemberInfos		 = 352;	// 1.8.1新增 批量拉取公会成员信息
const unsigned int CMD_GuildGetGroupMemberV2	 = 353;	// 1.8.1新增 拉取公会群成员信息
const unsigned int CMD_GuildVerifyMembersInGuild = 354;	// 1.8.1新增 判断给定用户是否在指定公会里
const unsigned int CMD_GuildGetGroupMuteMemberList = 355; // 1.8.1新增 获取公会群禁言成员详细信息列表
const unsigned int CMD_GuildGetMemberList 		 = 46;  // 1.8.1复用 拉取公会成员列表


const unsigned int CMD_GuildGetAdv = 356;  // 拉取公会部落广告列表
const unsigned int CMD_GuildGetAnn = 357;  // 拉取公会部落公告列表
const unsigned int CMD_GuildGetStarLevelV3 = 358;	// 星级公会内容V3

const unsigned int CMD_GuildGetMemberContributionList = 360;	// 获取公会成员贡献明细
const unsigned int CMD_GuildGetMemberCard = 361;	// 获取公会个人卡片
const unsigned int CMD_GuildGetMemberListByRankType = 362;	// 获取公会成员列表
const unsigned int CMD_GuildDonate = 363;			// 公会捐献
const unsigned int CMD_GuildGetDonateList = 364;	// 获取公会捐献列表
const unsigned int CMD_GuildGetDonateOption = 365;	// 查询公会捐献选项
const unsigned int CMD_GuildSetMemberTitle = 366;	// 设置公会成员称号
const unsigned int CMD_GuildGetMemberTitleList = 367;	// 获取公会成员称号列表
const unsigned int CMD_GuildGetPresentInfo = 368;	// 获取公会礼物信息


/** 新公会协议 END**/


/** 群组 BEGIN **/
const unsigned int CMD_TGroupCreatePermission	= 370; // 获取兴趣群组创建权限
const unsigned int CMD_SearchTGroup				= 371; // 搜索兴趣群组
const unsigned int CMD_HotGameList				= 372; // 兴趣群组热门游戏
const unsigned int CMD_CreateTGroup				= 373; // 创建兴趣群组
const unsigned int CMD_TGroupRecommended		= 374; // 兴趣群组推荐
const unsigned int CMD_JoinTGroup				= 375; // 加入兴趣群
const unsigned int CMD_QuitTGroup               = 376; //退出兴趣群
const unsigned int CMD_TGroupAddMember			= 377; //兴趣群添加成员
const unsigned int CMD_TGroupRemoveMember       = 378; //TGroupRemoveMember移出兴趣群
const unsigned int CMD_TGroupModifyName			= 379; //修改兴趣群组名称
const unsigned int CMD_TGroupGetMemberList		= 380; //获取兴趣群成员列表
const unsigned int CMD_TGroupMuteMember			= 381; //禁言兴趣群成员
const unsigned int CMD_TGroupApprove			= 382; //兴趣群同意加入
const unsigned int CMD_TGroupGetMuteList		= 383; //获取兴趣群禁言成员列表
const unsigned int CMD_TGroupGetDetailInfo		= 384; //获取兴趣群组详细信息
const unsigned int CMD_SearchTGroupByGameId		= 385; //根据游戏id搜索兴趣群组
const unsigned int CMD_TGroupDismiss			= 386; //解散兴趣群组
const unsigned int CMD_TGroupModifyMyCard		= 387; //修改兴趣群组我的名片
const unsigned int CMD_TGroupModifyGroupDesc	= 388; //修改兴趣群组描述
const unsigned int CMD_TGroupSearchCircleGame	= 389; //兴趣群组搜索游戏
const unsigned int CMD_TGroupSetNeedVerify		= 390; //设置兴趣群组是否需要验证
const unsigned int CMD_TGroupAddAdmin			= 391; //设置兴趣群组管理员
const unsigned int CMD_TGroupRemoveAdmin		= 392; //取消兴趣群组管理员
const unsigned int CMD_TGroupUnmuteMember		= 393; //取消禁言兴趣群成员
const unsigned int CMD_TGroupSetAllMute			= 394; //设置兴趣群组全体禁言
const unsigned int CMD_GroupBatchDelBulletin	= 395; //批量删除群组公告
const unsigned int CMD_TGroupInviteMember       = 396; //邀请兴趣群成员
const unsigned int CMD_TGroupAcceptJoin         = 397; //兴趣群同意加入

/** 群组 END **/

/** 活动协议  **/
const unsigned int CMD_GetMyFirstVoucher			= 400; // 获取我的首充号协议 (2.9.0以后版本不再更新);

// 注册/注销Apns device token
const unsigned int CMD_RegisterApnsDeviceToken	= 401; // 注册/注销Apns device token
const unsigned int CMD_BindPhone = 402;             // 绑定手机号码
const unsigned int CMD_RebindPhone = 403;			// 换绑手机

const unsigned int CMD_BindPhoneBeforeAuth = 404;  // 登录前 绑号
const unsigned int CMD_BindPhoneAfterAuth = 405;   //  登录后 绑号

const unsigned int CMD_AckNotification	= 407;      //上报推送信息状态

/** 用户配置 **/
const unsigned int CMD_UpdateMsgSetting = 410; // 更新消息接收配置设置
const unsigned int CMD_QueryMsgSetting = 411; // 查询消息接收配置设置

const unsigned int CMD_SetLocalAccountClearSwitch = 414;    //设置“退出后清除账号和密码信息”的开关状态
/** 用户配置 **/

/** 注销 **/
const unsigned int CMD_GetUnregApplyAuditStatus = 415; //账号注销申请审核状态
const unsigned int CMD_GetUserStatus = 416; // 注销时获取用户状态
const unsigned int CMD_CheckUserGrant = 417; // 注销时检测用户是否授权

/* rush */
const unsigned int CMD_RushQueue = 418; // rush 排队

/** 开黑房间 BEGIN **/
const unsigned int CMD_CreateChannel             = 420; // 创建房间
const unsigned int CMD_DismissChannel            = 421; // 解散房间
const unsigned int CMD_ModifyChannelName         = 422; //修改房间名字
const unsigned int CMD_EnterChannel              = 423; //进房
const unsigned int CMD_QuitChannel               = 424; //退房
const unsigned int CMD_GetChannelList            = 425; //获取房间列表
const unsigned int CMD_ChannelGetMemberList      = 426; //获取房间成员列表
const unsigned int CMD_ChannelGetMutedMemberList = 427; //获取房间禁言成员列表
const unsigned int CMD_ChannelMuteMember         = 428; //禁言房间成员
const unsigned int CMD_ChannelUnmuteMember       = 429; //取消禁言房间成员
const unsigned int CMD_ChannelHoldMic            = 430; //上麦
const unsigned int CMD_ChannelReleaseMic         = 431; //下麦
const unsigned int CMD_ChannelGetMicList         = 432; //获取麦位列表
const unsigned int CMD_GetChannelDetail          = 433; //获取单个房间详细信息
const unsigned int CMD_SendChannelTextMsg        = 434; //发送房间文本消息
const unsigned int CMD_GetChannelMsg             = 435; //拉取房间消息
const unsigned int CMD_QuickJoinChannel          = 436; //快速进房
const unsigned int CMD_DisableChannelMicEntry    = 437; //禁用麦位
const unsigned int CMD_EnableChannelMicEntry     = 438; //启用麦位
const unsigned int CMD_KickoutChannelMember      = 439; //踢出房间
const unsigned int CMD_KickoutChannelMic         = 440; //踢下麦
const unsigned int CMD_ModifyChannelPasswd       = 441; //修改房间密码
const unsigned int CMD_GetChannelPasswd          = 442; //获取房间密码

const unsigned int CMD_GroupPublishBulletin      = 443; //发布群组公告 (浑蛋~不要占用channel的命令号段！！！！！)
const unsigned int CMD_GroupDeleteBulletin       = 444; //删除群组公告 (浑蛋啊~不要占用channel的命令号段！！！！！)
const unsigned int CMD_GroupGetBulletins         = 445; //获取群组公告 (浑蛋啊啊~不要占用channel的命令号段！！！！！)

const unsigned int CMD_SetChannelMicMode         = 446; //设置开黑房间的麦模式 自由或者有麦位
const unsigned int CMD_GetUserAdminChannelList   = 447; //获取我管理的房间列表
const unsigned int CMD_SearchChannel             = 448; //搜索房间
const unsigned int CMD_BatchGetChannelList       = 449; //批量根据房间ID 获取房间列表


const unsigned int CMD_StartChannelMusic        = 450; // 开始播放音乐(该命令在3.1版本已经废弃)
const unsigned int CMD_StopChannelMusic         = 451; // 停止播放音乐(该命令在3.1版本已经废弃)
const unsigned int CMD_GetChannelCurrMusic      = 452; // 获取房间当前音乐播放信息(该命令在3.1版本已经废弃)
const unsigned int CMD_GetChannelMusicList      = 453; // 获取房间音乐播放列表
const unsigned int CMD_GetChannelMusicInfoByMid = 454; // 根据音乐ID获取下载地址 和 图片地址 等(该命令在3.1版本已经废弃)
const unsigned int CMD_SearchChannelMusic       = 455; // 按关键字搜索音乐(该命令在3.1版本已经废弃)

const unsigned int CMD_SendChannelAttachmentMsg    = 456;  // 发送附件类型的房间消息 比如图片
const unsigned int CMD_DownloadChannelMsgAttachment = 457; // 下载房间消息附件
const unsigned int CMD_ChannelConsumeTopN = 458;           // 房间消费排行榜
const unsigned int CMD_ChannelGetMemberInfo = 459;         // 获取房间 指定用户的信息


/** 开黑房间 END   **/

/** 登录验证码 BEGIN **/
const unsigned int CMD_GetLoginCAPTCHA = 460;	//获取登录验证码
const unsigned int CMD_VerifyLoginCAPTCHA = 461;	//校验登录验证码
/** 登录验证码 END   **/

const unsigned int CMD_GuildCheckinSupplement = 462;    //公会补签
const unsigned int CMD_GuildBlogGetActivityList = 463;  //获取公会部落活动列表 [废弃]
const unsigned int CMD_GuildBlogGetRankList = 464;  //获取公会排行列表
const unsigned int CMD_GuildGetMonthRankList = 465; //获取公会内部月榜

/** 公会游戏招募 BEGIN **/
const unsigned int CMD_PostGuildGameMemberRecruit    = 470;       // [已经下线废弃的业务]创建 修改 删除 游戏公会成员招募
const unsigned int CMD_SupportGuildGameMemberRecruit = 471;       // [已经下线废弃的业务]顶一下 游戏下 某个公会成员招募
const unsigned int CMD_GetGuildGameMemberRecruitList = 472;       // [已经下线废弃的业务]获取 游戏下 公会成员招募列表
const unsigned int CMD_ReportGuildGameMemberRecruit  = 473;       // [已经下线废弃的业务]举报 一个 游戏招募
const unsigned int CMD_GetRecruitRecommendGameList   = 474;       // [已经下线废弃的业务]获取有正在进行招募的推荐游戏列表
/** 公会游戏招募 END   **/


/** 新游戏相关 BEGIN**/
const unsigned int CMD_SearchGameAndCircle = 490; // 搜索游戏和圈子
const unsigned int CMD_SearchGuildPlaying = 491; // 搜索公会正在玩的游戏
const unsigned int CMD_GetBothLikeGame = 492; // 获取我和好友都喜欢的游戏
/** 新游戏相关 END*/

/** 旧版【业务已经下线】 拉红包相关 BEGIN**/
const unsigned int CMD_PullRedPacket = 520; // [已经下线废弃的业务] 拉红包
/** 旧版【业务已经下线】拉红包相关 END**/


// 500 - 508 为服务器内部命令, 跳过500段

/** 用户信息相关  BEGIN**/
const unsigned int CMD_BatchGetSmallFaceUrl = 600; // 批量获取小头像
const unsigned int CMD_GetUserOnlineTerminalList = 601; // 获取用户在线终端列表
const unsigned int CMD_UserKickTerminal = 602; // 踢掉用户在线终端
const unsigned int CMD_GetUserGeoInfo = 603; //用户获取地理信息
const unsigned int CMD_CheckRelatedLoginAccount = 604;  // 检查是否有关联登录账号
const unsigned int CMD_GetRelatedLoginAccount = 605;    // 获取关联登录账号

/** 用户信息相关  END**/


/** 娱乐-语聊-获取轮播榜 BEGIN**/
const unsigned int CMD_GetCharmAndRichLableInfo = 620; // 获取魅力和财富标签信息
/** 娱乐-语聊-获取轮播榜  END**/

/*房间排行榜 BEGIN*/
const unsigned int CMD_GetChannelWeekConsumeTopN = 621;	// 获取房间成员消费周榜

const unsigned int CMD_GetChannelHourRankTopN = 622;	// 获取房间小时榜单
const unsigned int CMD_GetChannelHourRankByCId = 623;	// 根据房间id获取房间小时榜排名信息
const unsigned int CMD_GetChannelHourRankTop1List = 624; // 欢游-获取房间小时榜单Top1列表
/*房间排行榜 END*/

/** 游戏推荐 BEGIN **/
const unsigned int CMD_GetGameRecommendCardList = 701; // 获取游戏推荐卡片列表

/** 游戏推荐 END **/

/**新公会协议2 start**/

const unsigned int CMD_GuildGroupModifyGame = 803;	//修改群组绑定的游戏
const unsigned int CMD_GuildGameListGet = 804;		//获取主打游戏列表
const unsigned int CMD_GuildGameExtraListAdd = 805;	//扩充主打游戏

const unsigned int CMD_GuildStorage = 810; //公会仓库首页
const unsigned int CMD_GuildProductModify = 811; //礼包属性修改（上下架,数量/价格调整）
const unsigned int CMD_GuildProductAllot = 812; //礼包分配
const unsigned int CMD_GuildProductSearch = 813; //礼包搜索
const unsigned int CMD_GuildProductUpLoad = 814; //会长上传
const unsigned int CMD_GuildProductGetExamineLst = 815; //申请审查列表
const unsigned int CMD_GuildGiftExamine = 816; //审查操作
const unsigned int CMD_GuildStorageGetOperRecord = 817; //仓库操作记录
const unsigned int CMD_GuildStorageRemoveProduct = 818; //删除礼包

const unsigned int CMD_GuildGetJoinHistory = 820;	//获取公会的成员加入历史流水记录

const unsigned int CMD_GuildOfficialCreate = 830;		//创建公会职位
const unsigned int CMD_GuildOfficialModify = 831;		//修改公会职位
const unsigned int CMD_GuildOfficialDel = 832;			//删除公会职位
const unsigned int CMD_GuildOfficialMemberGet = 833;  	//获取职位下的成员
const unsigned int CMD_GuildOfficialMemberAdd = 834;	//任命
const unsigned int CMD_GuildOfficialInfoGet = 835;		//获取职位成员列表
const unsigned int CMD_GuildOfficialMemberRemove = 836;	//取消官员
const unsigned int CMD_GuildOfficialInfoGetByUid = 837;	//获取成员的职位信息

//公会礼品卡
const unsigned int CMD_GuildStorageGetGiftCard = 840; //获取礼品卡
const unsigned int CMD_GuildStorageSplitGiftCard = 841; //拆分购物卡(礼品卡上架)
const unsigned int CMD_GuildStorageAllotGiftCard = 842; //礼品卡分配(礼品卡拆分礼包发给其他人)
const unsigned int CMD_GuildStorageRemoveGiftCard = 843; //删除礼品卡

const unsigned int CMD_GuildStorageGetProductDeail  = 846; //获取product详情(包含密码）
const unsigned int CMD_GuildStorageEditProduct		= 847; //修改product

const unsigned int CMD_GeneralSendVerifyCode = 848;		// 获取 通用敏感操作 短信验证码
const unsigned int CMD_GeneralCheckVerifyCode = 849;    // 校验 通用敏感操作 短信验证码
/**新公会协议2 end**/


const unsigned int CMD_GetMyGiftBoxItemList = 850;              /* 我的礼品箱协议获取协议 2.9.0版本 */

const unsigned int CMD_IM_CHECK_GROUPMSG_ATEVERYONE_CNT = 860;  /* 检测@ALL 这种AT全体用户的消息 剩余发送次数 */

const unsigned int CMD_GetImGuideTriggerInfo =  861;  /*获取im引导触发条件所需信息*/

const unsigned int CMD_GuildGetBlackList = 870;		//获取公会黑名单
const unsigned int CMD_GuildRemoveBlackList = 871;	//解除公会黑名单

const unsigned int CMD_GetRedDiamondExchangeItemList = 880;     // 获取红钻兑换商品列表
const unsigned int CMD_ExchangeRedDiamond = 881;                // 兑换红钻
const unsigned int CMD_GetRedDiamondExchangeHistory = 882;      // 获取兑换历史

/* Push */
const unsigned int CMD_GetPushToken = 900;                      // 获取Push token

/** hctran BEGIN **/
const unsigned int CMD_GuildGetLiveSummary = 1000; // 获取直播概要信息
const unsigned int CMD_GuildGetLiveList = 1001; // 获取直播列表
const unsigned int CMD_GetTTLivePublishingList = 1002; // 获取直播列表
/** hctran END **/

/* 在线情况 群在线 好友在线 BGEIN */
const unsigned int CMD_GetGroupOnlineNumInfo = 1100;        // 获取指定群的在线人数信息
const unsigned int CMD_ReportUserOnlineGameActive = 1121;   // 上报用户在线游戏状态
const unsigned int CMD_GetUserOnlineFriendList    = 1122;   // 获取用户在线好友列表
const unsigned int CMD_GetUserOfflineFriendList   = 1123;   // 获取用户离线好友列表
const unsigned int CMD_ReportFollowChannelAuth = 1124;		// 修改频道跟随开关
const unsigned int CMD_GetFollowChannelAuth = 1125;		    // 获取频道跟随开关

const unsigned int CMD_GetUserChannelFollowInfo = 1126;     // 获取当前用户对于指定用户的房间跟随信息
const unsigned int CMD_GetChannelOnlineMemberCnt = 1127;    // 获取房间在线人数，只返回 ugc 房间

/* 在线情况 群在线 好友在线 END */

/* 频道收藏 召集 BGEIN */
const unsigned int CMD_ChannelUpdateCollectStatus         = 1150;   // 更新当前频道的收藏状态
const unsigned int CMD_ChannelGetCollectionList           = 1151;   // 获取收藏房间列表
const unsigned int CMD_ChannelConvene                     = 1152;   // 发起频道召集
const unsigned int CMD_DisableChannelConvene              = 1153;   // 取消频道召集
const unsigned int CMD_ChannelGetConfirmOrEnterMemberList = 1154;	// 获取响应频道召集的用户和已在房间的用户
const unsigned int CMD_ChannelGetUserConveneChannelList   = 1155;	// 获取正在召集指定用户的频道
const unsigned int CMD_ChannelUpdateMemberConfirmStatus   = 1156;	// 更新用户的频道召集状态
const unsigned int CMD_ChannelGetConveneInfo              = 1157;	// 获取频道的召集信息
const unsigned int CMD_BatchRemoveChannelCollect          = 1158;   // 批量取消收藏房间
/* 频道收藏 召集 END */

/* 礼物 BGEIN */
const unsigned int CMD_PresentSend                     = 1165;    // 赠送礼物
const unsigned int CMD_PresentGetUserPresentInfo       = 1166;    // 获取用户的礼物信息
const unsigned int CMD_PresentGetPresentConfigList     = 1167;    // 获取礼物配置列表
const unsigned int CMD_PresentGetConfigById            = 1168;    // 获取指定的礼物配置
const unsigned int CMD_PresentGetUserPresentDetailList = 1169;	  // 获取用户的礼物明细列表
const unsigned int CMD_PresentBatchSend                = 1170;	  // 批量赠送礼物
const unsigned int CMD_PresentSendByIM                 = 1171;	  // IM赠送礼物
const unsigned int CMD_GetNamingPresentConfigList      = 1172;    // 获取冠名礼物配置信息
const unsigned int CMD_GetPresentDynamicTemplateConfig = 1173;    // 获取非全屏礼物送礼动效模板配置
const unsigned int CMD_GetNeedPopUpPresentList         = 1174;    // 获取送礼需要弹窗的礼物列表

const unsigned int CMD_PresentGetFlowConfigList        = 1175;    // 获取礼物流光配置列表
const unsigned int CMD_PresentGetFlowConfigById        = 1176;    // 获取指定的礼物流光配置
const unsigned int CMD_GetDrawPresentPara              = 1177;    // 获取涂鸦礼物的参数信息
const unsigned int CMD_GetImPresentItemIdList          = 1178;    // 获取IM可送礼物列表
const unsigned int CMD_GetStangerImItemIdList          = 1179;    // 获取解除陌生人聊天限制可送礼物的参数信息



const unsigned int CMD_GetUserActPresentArea      = 1190;      // 获取用户活动礼物区域（礼物墙星座活动礼物区域）
const unsigned int CMD_GetPresentExtraConfig      = 1191;      // 获取礼物的额外配置（浮层/闪光特效）
const unsigned int CMD_GetPresentEffectTimeDetail = 1192;      // 获取限时礼物的延期详情
const unsigned int CMD_UnpackPresentBox           = 1193;      // [全服礼物前置特效] 打开礼物盒
const unsigned int CMD_CommonPresentSend          = 1194;    // 新的统一送礼接口，对应之前的1165和1170
const unsigned int CMD_PresentConfigSync          = 1195;    // 新的礼物配置同步命令
const unsigned int CMD_EmperorSetSend             = 1196;    // 赠送帝王套
const unsigned int CMD_GetEmperorSetConfigById    = 1197;    // 获取帝王套配置
const unsigned int CMD_UnpackEmperorBox           = 1198;    // 打开帝王套礼物盒
const unsigned int CMD_GetTimePresentList         = 1199;    // 获取时间礼物列表

/* 礼物 END */

/* 用户推荐 BGEIN*/
const unsigned int CMD_RejectRecommend          = 1180;		// [已经下线废弃的业务] 用户推荐相关
const unsigned int CMD_AddOrUpdateContacts      = 1181;     // 上传用户手机通讯录(路由转发到CMD_NewAddOrUpdateContacts
const unsigned int CMD_GetRecommendFromContacts = 1182;     // 从通讯录信息中匹配好友用户(路由转发到CMD_NewGetRecommendFromContacts)
const unsigned int CMD_GetUserRecommend         = 1183;     // [已经下线废弃的业务] 获取推荐信息(路由转发到CMD_NewGetUserRecommend)
const unsigned int CMD_ChangeRecommendStatus    = 1184;     // 修改是否允许被推荐的状态 (路由转发到CMD_NewChangeRecommendStatus)
const unsigned int CMD_GetRecommendStatus       = 1185;     // 获取用户自己的状态(路由转发到CMD_NewGetRecommendStatus)
/* 用户推荐 END*/

/* 用户相册 BEGIN*/
const unsigned int CMD_UpdatePhotoAlbum = 1200; // 更新用户相册
const unsigned int CMD_GetPhotoAlbum    = 1201; // 获取用户相册
/* 用户相册 END*/

const unsigned int CMD_GetIosAuditStatus = 1210;	// 获取ios的版本审核状态


// 给用户发TT活动礼包
const unsigned int CMD_AwardUserTTGiftPkg = 1220; // TT活动礼包

const unsigned int CMD_CheckFirstRechargeActEntry  = 1221; // 首充活动入口 是否出现的check
const unsigned int CMD_GetMutiLocationActEntry     = 1222; // 获取多用途活动入口配置信息

/* 聊天机器人 BEGIN 1300-1349 */
const unsigned int CMD_GetAIPartnerEntrance = 1300; // 获取聊天机器人入口
const unsigned int CMD_GetAIPartnerEntranceV2 = 1301; // 获取聊天机器人入口V2
const unsigned int CMD_GetAIPartnerEntranceList = 1302; // 获取聊天机器人入口列表
const unsigned int CMD_GetAIRoleInteractiveConfig = 1303; // 获取聊天机器人角色互动配置
const unsigned int CMD_ReportAIPetBehavior = 1304; // 上报聊天机器人行为
/* 聊天机器人 END */

/* WEB IM BEGIN 1350-1399 */
const unsigned int CMD_SendWebImMsg = 1350; // 发送webim消息
const unsigned int CMD_GetWebImMsgList = 1351; // 获取webim消息列表
const unsigned int CMD_ReadWebImMsg = 1352; // 标记webim消息已读
const unsigned int CMD_GetUserWebImMsg = 1353; // 获取webim用户消息
const unsigned int CMD_SendGroupImMsg = 1354; // 发送webim群组消息
const unsigned int CMD_BatchGetGroupLastMsg = 1355; // 批量获取webim群组消息
const unsigned int CMD_GetGroupMsgList = 1356; // 获取webim群组消息列表
/* WEB IM END */

/* 公会圈子 就是新版公会公告 BEGIN*/
const unsigned int CMD_GuildCirclePostTopic = 2000;
const unsigned int CMD_GuildCircleGetTopicList = 2001;
const unsigned int CMD_GuildCircleGetTopic = 2002;
const unsigned int CMD_GuildCirclePostComment = 2003;
const unsigned int CMD_GuildCircleGetCommentReplyList = 2004;
const unsigned int CMD_GuildCircleGetCommentList = 2005;
const unsigned int CMD_GuildCircleLikeTopic = 2006;
const unsigned int CMD_GuildCircleGetTopicLikeList = 2007;
const unsigned int CMD_GuildCircleDeleteTopic = 2008;
const unsigned int CMD_GuildCircleDeleteComment = 2009;
const unsigned int CMD_GuildCircleAddTopicTag = 2010;
/* 公会圈子 END*/

/* 房间背景 BEGIN*/
const unsigned int CMD_GetCurBackgroundInfo = 2020;          // 获取当前房间背景信息
const unsigned int CMD_ChangeCurChannelBackground = 2021;    // 改变当前房间背景
const unsigned int CMD_GetChannelBackgroundInfoList = 2022;  // 获取当前房间背景配置列表
const unsigned int CMD_CheckChannelBackgroundUpdate = 2023;  // 检查房间配置更新，是否要显示红点
const unsigned int CMD_GetRecommendBackground = 2024;        // 获取热门推荐背景
const unsigned int CMD_GetKHBackgroundInfoList = 2025;       // 获取开黑专属背景列表

const unsigned int CMD_GetPclfgCurChannelBackground = 2026;          // pc极速版获取当前房间背景信息
const unsigned int CMD_SetPclfgCurChannelBackground = 2027;    // pc极速版设置当前房间背景
const unsigned int CMD_GetPclfgChannelBackgroundList = 2028;  // pc极速版获取当前房间背景配置列表
/* 房间背景 END*/

/* 开黑房间2 命令区间 2050 - 2100 BEGIN */
const unsigned int CMD_GetChannelHistroy = 2050;     // 获取房间进入历史列表
const unsigned int CMD_GetChannelGiftHistroy = 2051; // 获取房间送的礼物的列表
const unsigned int CMD_GetChannelExtendInfo = 2052;  // 获取房间扩展信息 可以根据需要获取 管理员列表 最高在线信息 前3的房间历史 前3的土豪榜信息
const unsigned int CMD_SetChannelMicrStatus = 2053;  // 操作麦位状态 打开/关闭/禁言 麦位 (可以取代之前的麦位开启和麦位关闭命令)
const unsigned int CMD_OperChannelAdmin    = 2054;   // 设置房间管理员 目前只对个人房有效

const unsigned int CMD_ChannelMusicCommand     = 2055;   // 播放控制(播放、停止、上一首、下一首)
const unsigned int CMD_AddChannelMusic         = 2056;   // 添加音乐
const unsigned int CMD_RemoveChannelMusic      = 2057;   // 删除音乐
const unsigned int CMD_ChannelMusicHeartBeat   = 2058;   // 音乐心跳
const unsigned int CMD_ChannelMusicSetNext     = 2059;   // 设下一首歌曲
const unsigned int CMD_ChannelMusicGetList     = 2060;   // 获取歌曲列表
const unsigned int CMD_ChannelMusicSetPlayMode = 2061;   // 播放模式
const unsigned int CMD_ChannelMusicSetVolume   = 2062;   // 播放音量

const unsigned int CMD_ChannelGame = 2063;               //房间小游戏

const unsigned int CMD_ChannelMusicGetStatus = 2064;     //房间播放器状态
const unsigned int CMD_ChannelMusicSetCanShare = 2065;  //房间是否可分享歌曲
const unsigned int CMD_ChannelMusicPlayTheMusic = 2066; //房间是否可分享歌曲

const unsigned int CMD_ChannelMagicExpression = 2067;   //房间麦上魔法表情

const unsigned int CMD_ChannelChangeMicSpace = 2068;   // 更换麦的位置 只能在空麦位之间移动
const unsigned int CMD_ChannelTakeUserHoldMic = 2069;  // 把指定用户放到指定麦位上
const unsigned int CMD_ChannelModifyExtend = 2070;     // 扩展的房间信息修改接口 包括房间图标 房间描述 房间标签。。。
const unsigned int CMD_GetRecommendChannelList = 2071; // 获取推荐频道

const unsigned int CMD_ChannelMusicSetFreeMode = 2072; // 战歌的自由模式

const unsigned int CMD_GetHotChannelList = 2073;	   // 获取热门房间
const unsigned int CMD_GetChannelShowSwitch = 2074;	   // 获取房间tab的频道列表开关

const unsigned int CMD_GetChannelTagList = 2075;       // 标签列表(第一代房间标签 仅娱乐推荐房沿用)
const unsigned int CMD_GetChannelCardList = 2076;      // [已经下线废弃的业务]卡片列表(第一代房间标签 仅娱乐推荐房沿用)
const unsigned int CMD_GetChannelByTagId = 2077;       // 根据标签获取房间列表(第一代房间标签 仅娱乐推荐房沿用)

const unsigned int CMD_SetChannelTagId = 2078;		    // 设置房间标签(第一代房间标签 4.0.0已经废弃 不允许设置)
const unsigned int CMD_GetChannelTagId = 2079;			// 获取房间标签(第一代房间标签 仅娱乐推荐房沿用)
const unsigned int CMD_RefreshChannelTime = 2080;			// 刷新房间排序时间
const unsigned int CMD_GetChannelAdvList = 2081;			// 房间广告位
const unsigned int CMD_GetChannelRefreshCD = 2082;			// 刷新cd时间
const unsigned int CMD_GetChannelHomeDetail = 2083;			// 首页的广告，随机推荐等

const unsigned int CMD_ChannelPcHelperJoin = 2084;			// [已经下线废弃的业务] Pc助手 加入房间
const unsigned int CMD_ChannelPcHelperMicCheck = 2085;		// [已经下线废弃的业务] Pc助手 检查是否在麦位上

const unsigned int CMD_ChannelGetGameMatchOptions = 2086;       // [已经下线废弃的业务]快速组队信息
const unsigned int CMD_ChannelGetGameMatchListHomePage = 2087;  // [已经下线废弃的业务]组队首页
const unsigned int CMD_ChannelGetGameMatchListByTagId = 2088;   // [已经下线废弃的业务]组队二级标签
const unsigned int CMD_ChannelStartGameMatch = 2089;            // [已经下线废弃的业务]发起快速组队


const unsigned int CMD_LiveChannelFinish              = 2090;  // 主播结束语音直播 FinishChannelLiveReq
const unsigned int CMD_LiveChannelApplyConnectMic     = 2091;  // 游客 发起连麦申请(包括取消申请) LiveChannelMicApplyReq
const unsigned int CMD_LiveChannelHandleConnectMic    = 2092;  // 主播 处理连麦请求 LiveChannelMicHandleReq
const unsigned int CMD_LiveChannelGetApplyMicUserList = 2093;  // 主播 获取当前请求连麦的用户列表 GetLiveChannelMicApplyUserListReq

const unsigned int CMD_LiveChannelGetInfo             = 2094;  // 获取主播的语音直播房信息

const unsigned int CMD_GetRecommendLiveChannel        = 2095;  // 拉取推荐语音直播房
const unsigned int CMD_GetLiveChannelList             = 2096;  // 拉取二级页语音直播房

const unsigned int CMD_CheckAddMusic = 2097;               //审核上传的歌曲的歌名和作者名字

const unsigned int CMD_Channel_Reserved = 2100; // 保留命令字
/* 开黑房间2 命令区间 2050 - 2100 END*/

/* 扩圈 BEGIN */
const unsigned int CMD_FindFriendsGetMyInfo = 2101;			// [已经下线废弃的业务]获取我的信息
const unsigned int CMD_FindFriendsUpdateMyInfo = 2102;		// [已经下线废弃的业务]更新照骗之外的其他信息
const unsigned int CMD_FindFriendsUpdatePhotos = 2103;		// [已经下线废弃的业务]更新照骗
const unsigned int CMD_FindFriendsGetUserCards = 2104;		// [已经下线废弃的业务]获取匹配的卡片列表
const unsigned int CMD_FindFriendsOperateOnCards = 2105;	// [已经下线废弃的业务]操作卡片
const unsigned int CMD_FindFriendsGetGameList = 2106;		// [已经下线废弃的业务]获取可以设置的游戏列表

const unsigned int CMD_GetQuickMatchConfiguration = 2120;	// [已经下线废弃的业务]获取快速匹配配置
const unsigned int CMD_StartQuickMatch = 2121;				// [已经下线废弃的业务]开始快速匹配
const unsigned int CMD_CancelQuickMatch = 2122;				// [已经下线废弃的业务]取消快速匹配
const unsigned int CMD_GetQuickMatchOnlineUsers = 2123;		// [已经下线废弃的业务]获取快速匹配的在线人数
const unsigned int CMD_QuickMatchKeepAlive = 2124;			// [已经下线废弃的业务]快速匹配保活上报

/* 扩圈 END */

/* 检查邀请码 */
const unsigned int CMD_CheckInviteFriendCode  = 2190; // 检查邀请码
const unsigned int CMD_CheckShowAddInviteCode  = 2191;// 是否显示邀请码
/* 检查邀请码 */

/* 背包、功能卡片、碎片 */
const unsigned int CMD_GetUserBackpack  = 2192; // 该命令字全量转发到CMD_NewGetUserBackpack=36900
const unsigned int CMD_UseFuncCard = 2193; // 该命令字全量转发到CMD_NewUseFuncCard = 36901
const unsigned int CMD_GetUserFuncCardUse = 2194; // 该命令字全量转发到CMD_NewGetUserFuncCardUse = 36902
const unsigned int CMD_GetUserFragment = 2195; // 该命令字全量转发到CMD_NewGetUserFragment = 36903
const unsigned int CMD_GetImBackpack = 2196;  //拿IM能显示的背包数据，该命令字全量转发到CMD_NewGetImBackpack = 36904

const unsigned int CMD_CheckGiftComposeEntry = 2197;      //检查黑暗礼物合成接口是否开启
const unsigned int CMD_GetGiftComposeHomePageInfo = 2198; //获取黑暗礼物合成主页info

/* 背包、功能卡片、碎片 */

/* IOS 上报游戏 BEGIN */
const unsigned int CMD_UploadIosUserGame = 2201;	// 上报IOS用户安装的游戏列表
const unsigned int CMD_GetIosNeedUploadConfGameList = 2202;		// 拉取IOS关心的需要上报的游戏KEY列表
/* IOS 上报游戏 END*/

/* LBS 2220 - 2229 预留 */
//附近的人, deprecated, 2020.04.15
const unsigned int CMD_ReportUserGeo       = 2220;  // [已经下线废弃的业务]上报用户地理位置
const unsigned int CMD_GetNearUserList     = 2221;  // [已经下线废弃的业务]获取附近的人
const unsigned int CMD_ModifyUserGeoSwitch = 2222;  // [已经下线废弃的业务]修改附近的人开关
const unsigned int CMD_GetUserGeoSwitch    = 2223;  // [已经下线废弃的业务]获取附近的人开关

//同城玩, 2020.04.15

const unsigned int CMD_ReportUserLocation   = 2225;  // [已经下线废弃的业务]上报用户地理位置
const unsigned int CMD_ListCityUserNear     = 2226;  // [已经下线废弃的业务]获取附近的人
/* LBS */



//找人玩
const unsigned int CMD_GetFindFriendList = 2230; // 获取找人玩列表
const unsigned int CMD_SetFindFriendSwitch = 2231; // 设置找人玩开关

/* Trivia Game 知识问答 */
const unsigned int CMD_ChannelTriviaGameEnter          = 2240;  //  [已经下线废弃的业务]进入答题房间
const unsigned int CMD_ChannelTriviaGameGetPhaseList   = 2241;  //  [已经下线废弃的业务]主持人 拉去阶段列表
const unsigned int CMD_ChannelTriviaGamePhaseForward   = 2242;  //  [已经下线废弃的业务]主持人 阶段切换
const unsigned int CMD_ChannelTriviaGameShowSolution   = 2243;  //  [已经下线废弃的业务]主持人 公布答案
const unsigned int CMD_ChannelTriviaGameAnswer         = 2244;  //  [已经下线废弃的业务]用户 答题
const unsigned int CMD_ChannelTriviaGamePreEnter       = 2245;  //  [已经下线废弃的业务]进入答题房间前的信息页
/* Trivia Game 知识问答 */
const unsigned int CMD_GetCommonWebActiveBreakingInfo  = 2246;  //  活动信息战况 (为2018年度盛典做的战况拉取接口)




/* 公会主打游戏 2260 - 2279 预留 */
const unsigned int CMD_GuildGameListGetV2 = 2260;		// 获取主打游戏列表
const unsigned int CMD_GuildGameChange = 2261;			// 更换主打游戏
const unsigned int CMD_GuildGetRecommendListByGames = 2262;	// 根据游戏获取推荐的公会列表
const unsigned int CMD_GuildGetHotGameList = 2263;		// 获取公会热门游戏
const unsigned int CMD_GuildAddGameV2 = 2264;			// 添加主打游戏
const unsigned int CMD_GuildDeleteGuildGameV2 = 2265;	// 删除主打游戏
/* 公会主打游戏 */

/*公会房间 2300-2319*/
const unsigned int CMD_ChannelGuildGetListSummary = 2300;   //房间列表
const unsigned int CMD_ChannelGuildGetListByType = 2301;    //房间
const unsigned int CMD_ChannelGuildGetTotalMemberCount = 2302;    //公会房间总人数
/*公会房间 end*/

/*游戏招募 BEGIN 2400-2420 【废弃命令】*/
const unsigned int CMD_GetGameRecruitDetail = 2400; //[已经下线废弃的业务]获取招募信息
const unsigned int CMD_CreateGameRecruit    = 2401; //[已经下线废弃的业务]创建招募
const unsigned int CMD_QuitGameRecruit = 2402; //[已经下线废弃的业务]退出招募
const unsigned int CMD_JoinGameRecruit = 2403; //[已经下线废弃的业务]加入招募
const unsigned int CMD_IntoGameRecruitChannel = 2404; //[已经下线废弃的业务]进入招募频道
const unsigned int CMD_InviteGameRecruit = 2405; //[已经下线废弃的业务]邀请加入招募
/*游戏招募 END */

/*相亲房 BEGIN 2420-2459*/
const unsigned int CMD_GetUserLikeBeatVal = 2430; //获取用户心动值
const unsigned int CMD_SelectLikeDatingUser = 2431; //选择心动对象
const unsigned int CMD_OpenLikeDatingUser = 2432; //公开心动对象
const unsigned int CMD_GetAlreadySelectLikeDatingUser = 2433; //获取已经选择心动的用户
const unsigned int CMD_ApplyDatingMic = 2434;            // 申请或取消连麦
const unsigned int CMD_GetApplyDatingMicUserList = 2435; // 获取连麦列表

const unsigned int CMD_SetChannelDatingGamePhase  = 2436;  // 设置相亲游戏的阶段
const unsigned int CMD_CheckChannelDatingGameEntry = 2437; // 检查房间是否有相亲模式的入口
const unsigned int CMD_DatingGameHoldVipMic = 2438;			// 上vip麦
const unsigned int CMD_GetDatingGameInitInfo = 2439;		// 获取相亲房信息 用于进房的时候拉取初始信息（比如麦上用户的心动值 / 当前帽子用户 / 当前土豪位用户 / 当前阶段 ）
/*相亲房 END */

/*用户测试匹配 [功能已经下线] BEGIN 2460-2469*/
const unsigned int CMD_GetFindFriendExamEntrance      = 2460;    // [已经下线废弃的业务]获取测试入口 [功能下线]
const unsigned int CMD_GetFindFriendExamQuestion      = 2461;    // [已经下线废弃的业务]获取测试题目 [功能下线]
const unsigned int CMD_AddUserFindFriendExam          = 2462;    // [已经下线废弃的业务]测试  [功能下线]
const unsigned int CMD_GetUserFindFriendExamResult    = 2463;    // [已经下线废弃的业务]获取测试结果 [功能下线]
const unsigned int CMD_MatchingFriend                 = 2464;    // [已经下线废弃的业务]匹配好友 [功能下线]
const unsigned int CMD_UploadExamInstallRecommendGame = 2465;    // [已经下线废弃的业务]上报用户安装的推荐游戏 [功能下线]
const unsigned int CMD_CheckUserFinishFindFriendExam  = 2466;    // [已经下线废弃的业务]检查用户是否完成测试   [功能下线]
/*用户测试匹配 END */

/*表情包 BEGIN 2500-2519*/
const unsigned int CMD_SaveEmoji = 2500; //另存为表情
const unsigned int CMD_DeleteEmoji = 2501; //删除表情
const unsigned int CMD_GetEmojiPkgList = 2502; //获取表情包列表
const unsigned int CMD_GetEmojiListByPkg = 2503; //通过指定表情包id获取表情列表
const unsigned int CMD_GetHotEmoji = 2511; //获取第三方推荐表情包
const unsigned int CMD_GetSearchEmoji = 2512; //获取搜索表情包
const unsigned int CMD_CheckHotEmojiEntrance = 2513; //是否展示热门表情包入口


/*表情包 END */

/* 家长监护模式 */

const unsigned int CMD_GetParentGuardianState = 2504; //获得家长监护模式开关状态
const unsigned int CMD_ParentGuardianSwitch = 2505; // 开启/关闭家长监护模式
const unsigned int CMD_CheckAppealCntIsOverLimit = 2506; // 忘记密码申诉前检查今日申诉次数有无达到上限
const unsigned int CMD_ParentGuardianCheckFace = 2507; // 忘记密码申诉验证
const unsigned int CMD_ForceOffParentGuardian = 2508; // 申诉成功后强制退出家长模式
const unsigned int CMD_ParentGuardianCheckPassword = 2509; // 核对家长模式密码


/* 家长监护模式 */

const unsigned int CMD_ChannelGetPresentRunwayList = 2510;	// 获取房间火箭跑道列表

/*表情包占用了2500-2519了！！！*/

const unsigned int CMD_ChannelGetUserDecorationList = 2520;		// 获取用户的房间坐骑列表
const unsigned int CMD_ChannelActivateUserDecoration = 2521;	// 激活/取消用户的房间坐骑

/* user-logic 2526 ~ 2549 */
const unsigned int CMD_GetUserContractInfo = 2526;		// 获取用户服务协议信息
const unsigned int CMD_AgreeUserContract = 2527;		// 同意用户服务协议
const unsigned int CMD_GetUserContractInfoNew = 2528;	// 获取用户服务协议信息,增加隐私协议
const unsigned int CMD_AgreeUserContractNew = 2529;		// 同意用户服务协议,增加隐私协议
const unsigned int CMD_SetUgcChannelDenoiseMode = 2530;   // 设置UGC房间降噪模式
const unsigned int CMD_GetUgcChannelDenoiseMode = 2531;   // 获取UGC房间降噪模式
const unsigned int CMD_GetUgcMoreConfigList = 2532;     // 获取UGC房间更多新增配置

/* 异步内容 2550 ~ 2700 */
const unsigned int CMD_SubscribeTopic = 2550; 			// 订阅主题
const unsigned int CMD_UnSubscribeTopic = 2551; 		// 取消订阅主题
const unsigned int CMD_GetTopicList = 2552; 			// 获取主题列表
const unsigned int CMD_GetTopicInfo = 2553; 			// 获取主题详细资料
const unsigned int CMD_GetSubscribeTopicList = 2554; 	// 获取订阅的主题列表
const unsigned int CMD_GetUnSubscribeTopicList = 2555; 	// 获取未订阅的主题列表
const unsigned int CMD_CheckTopicsIsSubscribe = 2556; 	// 判断主题是否已经订阅
const unsigned int CMD_SearchTopic = 2557; 	// 按名字搜索主题




const unsigned int CMD_GetUserUGCInfo = 2560;			// 获取用户的ugc详情
const unsigned int CMD_GetUserFriendships = 2561;  		// 获取用户的好友关系（新）
const unsigned int CMD_FollowUser = 2562; 				// 关注用户
const unsigned int CMD_UnfollowUser = 2563;				// 取消关注用户
const unsigned int CMD_RemoveFollower = 2564;			// 移除粉丝
const unsigned int CMD_QuickCheckFriendship = 2565;		// 快速检查好友关系
const unsigned int CMD_FollowBatchUser = 2566;        //批量添加好友
const unsigned int CMD_RecommendAttentionUser = 2567; //推荐关注用户
const unsigned int CMD_GetShowUserFollow = 2568;	    // 获取用户是否展示关注粉丝信息
const unsigned int CMD_SetShowUserFollow = 2569;		  // 设置用户是否展示关注粉丝信息

const unsigned int CMD_GetNewsFeed       = 2570;  // 拉取Feeds
const unsigned int CMD_RemoveFeed        = 2571;  // 删除Feeds(删除浏览记录，取消收藏)
const unsigned int CMD_ReportVisitRecord = 2572;  // 上报帖子浏览纪录
const unsigned int CMD_AddFavourite      = 2573;  // 收藏

const unsigned int CMD_UgcVotePost       = 2574;  // 帖子投票
const unsigned int CMD_GetGameEntrance   = 2575;  // 获取个人主页游戏入口

const unsigned int CMD_BatchUnFollowUser = 2576;  // 批量取消关注用户

const unsigned int CMD_GetInteractiveMsg = 2580;  // 获取互动消息列表
const unsigned int CMD_UgcMarkRead       = 2581;  // 互动消息，标记已读
const unsigned int CMD_DelInteractiveMsg = 2583;  // 删除互动消息
const unsigned int CMD_PushilyConcern    = 2584;  // 催更啊, 关心你啊

const unsigned int CMD_UpdateAttachmentPrivacy       = 2587;    // 更新附件的隐私设置
const unsigned int CMD_UgcMarkContentSticky          = 2588;    // 置顶帖子/评论
const unsigned int CMD_UgcReportPostMultimediaView   = 2589;    // 上报帖子图片/视频已读
const unsigned int CMD_UgcPostPost                   = 2590;    // 发帖
const unsigned int CMD_UgcMarkPostAttachmentUploaded = 2591;    // Mark发帖的附件上传完毕
const unsigned int CMD_UgcDeletePost                 = 2592;    // 删帖
const unsigned int CMD_UgcGetPostInfo                = 2593;    // 帖子详情
const unsigned int CMD_UgcPostComment                = 2594;    // 发评论/回复评论
const unsigned int CMD_UgcDeleteComment              = 2595;    // 删评论
const unsigned int CMD_UgcGetCommentList             = 2596;    // 评论列表
const unsigned int CMD_UgcReportPostView             = 2597;    // 上报帖子已读
const unsigned int CMD_UgcAttitudeUserList           = 2598;    // 点赞用户列表
const unsigned int CMD_ReportPostShare               = 2599;    // 上报帖子分享

const unsigned int CMD_ExpressAttitude = 2600;			// 点赞/取消点赞

const unsigned int CMD_GetUserIdentityInfo = 2601;                  // 得到认证用户的身份证信息(是否成年等)

const unsigned int CMD_GetUserFollowVisitPost = 2602;			// 用户看关注流的帖子位置

const unsigned int CMD_ReportUserFollowVisitPost = 2603;                  // 用户上报当前看到关注流的帖子位置

const unsigned int CMD_GetTopicAds = 2604;                  // 话题详情页广告位

const unsigned int CMD_GetRealNameAuthStateV2 = 2605;     // 获取实名认证信息v2

const unsigned int CMD_GetMoodDeatil = 2606;     // 获取心情详情页信息
const unsigned int CMD_GetMoodConfig = 2607;     // 获取心情配置
const unsigned int CMD_GetTabConfig         = 2608;     // 获取tab配置
const unsigned int CMD_ReportUnrelatedTopic = 2609;     // 上报帖子与话题无关


/* audio-post */
const unsigned int CMD_AudioPostScriptTabs = 2610;                // 声音动态台本标签
const unsigned int CMD_AudioPostScripts = 2611;                   // 声音动态台本（分页）
const unsigned int CMD_RandomAudioPostScripts = 2612;             // 声音动态台本（随机）
const unsigned int CMD_RandomAudioPostImages = 2613;              // 声音动态背景（随机）
const unsigned int CMD_AudioPostImages = 2614;                    // 声音动态背景（分页）
const unsigned int CMD_RandomAudioPostMusics = 2615;              // 声音动态音乐（随机）
const unsigned int CMD_AudioPostMusics = 2616;                    // 声音动态音乐（分页）
const unsigned int CMD_AudioPostMusicTabs = 2617;                 // 声音动态音乐标签
const unsigned int CMD_XunfeiSignature = 2618;                    // 声音动态，获取科大讯飞 authString
const unsigned int CMD_AudioPostScriptsForMobile = 2619;          // 声音动态台本（tab随机）
const unsigned int CMD_DelRandList = 2620;                        // 声音动态台本（销毁tab随机）
// V2
const unsigned int CMD_AudioPostScriptTabsV2 = 2621;                // 声音动态台本标签
const unsigned int CMD_AudioPostScriptsV2 = 2622;                   // 声音动态台本（分页）
const unsigned int CMD_RandomAudioPostScriptsV2 = 2623;             // 声音动态台本（随机）
const unsigned int CMD_RandomAudioPostImagesV2 = 2624;              // 声音动态背景（随机）
const unsigned int CMD_AudioPostImagesV2 = 2625;                    // 声音动态背景（分页）
const unsigned int CMD_RandomAudioPostMusicsV2 = 2626;              // 声音动态音乐（随机）
const unsigned int CMD_AudioPostMusicsV2 = 2627;                    // 声音动态音乐（分页）
const unsigned int CMD_AudioPostMusicTabsV2 = 2628;                 // 声音动态音乐标签
const unsigned int CMD_XunfeiSignatureV2 = 2629;                    // 声音动态，获取科大讯飞 authString
const unsigned int CMD_AudioPostScriptsForMobileV2 = 2630;          // 声音动态台本（tab随机）
const unsigned int CMD_DelRandListV2 = 2631;                        // 声音动态台本（销毁tab随机）
const unsigned int CMD_PostNonPublicPost=2632;                      // 发布非公开贴
const unsigned int CMD_GetNonPublicPostPublishExtend=2633;          // 获取非公开贴发布扩展信息
const unsigned int CMD_MarkNonPublicPostAttachmentUploaded=2634;    // 标记非公开贴的附件上传完毕
const unsigned int CMD_GetNonPublicNewsFeeds=2635;                  // 获取非公开贴的动态列表
const unsigned int CMD_GetNonPublicPost=2636; // 获取非公开贴详情
const unsigned int CMD_PostNonPublicComment=2637; // 发布非公开贴评论
const unsigned int CMD_GetNonPublicCommentList=2638; // 获取非公开贴评论列表
const unsigned int CMD_NonPublicExpressAttitude=2639; // 点赞/取消点赞非公开贴
const unsigned int CMD_MarkUserStreamRecord=2640; // 标记用户的非公开贴浏览记录
const unsigned int CMD_ReportNonPublicPostShare=2641; // 上报非公开贴分享
const unsigned int CMD_DeleteNonPublicPost=2642; // 删除非公开贴
const unsigned int CMD_DeleteNonPublicComment=2643; // 删除非公开贴评论
const unsigned int CMD_EditNonPublicPost=2644; // 编辑非公开贴
const unsigned int CMD_GetNonPublicEditPostExtend=2645; // 获取非公开贴编辑扩展信息
const unsigned int CMD_BatchConversionNonPublicPost=2646; /*批量转换社群讨论贴*/
const unsigned int CMD_ForceConversionPostPush=2647;   /*强插转换过的帖子推送*/
const unsigned int CMD_NonPublicPostVoteOption=2648; //  投票选项

/* audio-post */

/** 2019 年兽活动 命令已经过期 cmd Begin **/
const unsigned int CMD_NewYearBeatReport = 2700;            // [已经下线废弃的业务]上报新年抓年兽结果
const unsigned int CMD_NewYearBeatGetLotteryResutl = 2701;  // [已经下线废弃的业务]获取新年抓年兽抽奖奖励结果
/** 2019 年兽活动 命令已经过期 cmd End **/

/** 榜单入口配置 **/
const unsigned int CMD_GetYearActRankingListEntry = 2706; // 获取2019年度盛典活动榜单入口
const unsigned int CMD_GetCommonRankingListConfig = 2707;  //  获取通用实时榜单配置
const unsigned int CMD_GetAnchorRankingListConfig = 2708;  // 获取主播实时榜单配置
/** 榜单入口配置 **/

/* 普通房间的排麦 */
const unsigned int CMD_ChannelNormalMicQueueUpApply  = 2711; 	// 申请 或者 取消申请 排麦
const unsigned int CMD_ChannelNormalMicQueueUpGetList = 2712; 	// 获取排麦列表
//const unsigned int CMD_ChannelNormalMicQueueUpKick    = 2713; 	// 管理员从排麦列表中踢出某人
/* 普通房间的排麦*/

/*  新人开黑推荐房，房主设置 在新人推荐位展示*/
const unsigned int CMD_NoviceRecommendChannelStatus = 2720;         // [废弃命令] 查询开黑新人推荐房的展示状态：不是推荐房，展示，不展示
/*  新人开黑推荐房，房主设置 在新人推荐位展示*/

/** 开黑相关数据上报 2721~2760 BEGIN **/
const unsigned int CMD_UserFeedbackNewTab = 2721;       // 用户搜索不到想要的玩法时,反馈新玩法
const unsigned int CMD_ReportNewUserEvent = 2722;       // 上报新用户事件
const unsigned int CMD_GameReportDailyTask = 2723;       // 活动任务上报
const unsigned int CMD_ReportGameActivityEvents = 2724;  // 上报活动事件

/** 开黑相关数据上报 END **/


//领取好友赠送的碎片礼物
const unsigned int CMD_GetFriendDebrisGift = 2802; // 领取好友赠送的碎片礼物

//敲门信息
const unsigned int CMD_Knock = 2800;                        //敲门请求
const unsigned int CMD_HandleKnock = 2801;                  //处理敲门请求

const unsigned int CMD_ReportMoleBeat = 2806;               //打年兽上报

//精选流
const unsigned int CMD_AttentionPeople = 2900;              // 精选达人列表
const unsigned int CMD_HighContentKolExist = 2901;          // 是否Kol用户
const unsigned int CMD_HighContentHide = 2902;              // 是否隐藏精选推荐功能

// 帖子
const unsigned int CMD_UpdatePostPrivacyPolicy = 2903;      // 设置/取消设置帖子私密

/** 主题房间 BEGIN 3000~3200 **/
const unsigned int CMD_ListRecommendTopicChannel = 3000;    //获取【约玩】推荐房间列表
const unsigned int CMD_TabTopicChannel = 3001;              // 获取【约玩】主题房间分类列表
const unsigned int CMD_CreateTopicChannel = 3002;           // 发布主题房
const unsigned int CMD_GetTopicChannelInfo = 3003;          // 通过channel id获取主题房信息
const unsigned int CMD_GetTopicChannelRoomName = 3004;      // 通过channel id获取主题房示例名称
const unsigned int CMD_HideTopicChannel = 3020;             // 通过channel id让房间不在《约玩》大厅显示
const unsigned int CMD_KeepAliveTopicChannel = 3021;        // 房主与主题房保持心跳，20秒调用一次
const unsigned int CMD_GetChannelRoomNameConfig = 3022;     // 获取匹配主题房名配置

const unsigned int CMD_InsertTab = 3005; // InsertTab 用于插入一种主题房间类型。
const unsigned int CMD_UpdateTab = 3006; // UpdateTab 用于更新某个主题房间类型。
const unsigned int CMD_DeleteTab = 3007; // DeleteTab 用于删除某个主题房间类型。
const unsigned int CMD_Tabs = 3008; // Tabs 用于获取所有主题房间类型。
const unsigned int CMD_RearrangeTabs = 3009; // RearrangeTabs 用于重新排序所有主题房间类型。
const unsigned int CMD_GetRoomProxyTip = 3010;  // 获取文明公约
const unsigned int CMD_GetBannerList = 3011;  // 获取首页banner列表
const unsigned int CMD_CreateTopicChannelV2 = 3030;  // 发布主题房v2
const unsigned int CMD_QuickFormTeam = 3031;  // 快速组队
const unsigned int CMD_ListTopicChannelV2 = 3032;  //获取开黑房间列表v2
const unsigned int CMD_GetChannelDialog = 3033;  // 获取新用户进房弹窗
const unsigned int CMD_ListTabBlocks = 3034;  // 获取指定tab的开房填写信息
const unsigned int CMD_GetFormTeamInfo = 3035;  // 获取快速组队显示资料
const unsigned int CMD_SwitchGamePlay = 3036;  // 切换玩法
const unsigned int CMD_GetTabList = 3037;  // 玩法列表
const unsigned int CMD_GetGuideConfig = 3038;  // 获取快速组队引导的配置信息
const unsigned int CMD_GetHomePageCardList = 3039;  // 首页匹配卡片
const unsigned int CMD_GetDialogV2 = 3040;  // 获取怼脸弹窗v2 deprecate
const unsigned int CMD_QuickFormTeamV2 = 3041;  // 快速匹配临时房
const unsigned int CMD_ListFreshmanRecommendedChannel = 3042; // 聊天列表萌新房推荐
const unsigned int CMD_ListPlaymateRecommendedChannel = 3043; // 找玩伴空白列表房间推荐

//const unsigned int CMD_CreateButNotReleaseTopicChannel = 3050;  // 创建但不发布主题房
const unsigned int CMD_CreateAndReleaseTopicChannel = 3051;  // 创建并发布主题房
//const unsigned int CMD_DismissTopicChannel = 3052;  // 取消发布主题房
const unsigned int CMD_DistributionTopicChannel = 3053;  // 下发主题房
const unsigned int CMD_GetTabListWhenCreate = 3054;  // 创建主题房时的玩法列表
const unsigned int CMD_ListTopicChannelV3 = 3055;  //获取开黑房间列表v3
const unsigned int CMD_ShowTopicChannelTabList = 3056;  // 新的 创建主题房时的玩法列表
const unsigned int CMD_GetSubTabList = 3057;  // 获取小众游戏
const unsigned int CMD_GetQuickMatchTabList = 3058;  // 获取快速匹配tab列表信息
const unsigned int CMD_GetNegativeFeedBackOption = 3059;  // 获取负反馈上报选项
const unsigned int CMD_NegativeFeedBack = 3060;  // 开黑列表负反馈上报
const unsigned int CMD_GetLiveTogetherConfig = 3062; // 获取ugc一起看直播配置
const unsigned int CMD_SetLiveTogetherStatus = 3063; // 修改ugc一起看直播状态

const unsigned int CMD_LabelSearch = 3070; // 玩法搜索
const unsigned int CMD_GetGameLabels = 3071; // 获取热门玩法
const unsigned int CMD_GetLabelSearchGuide = 3072; // 获取玩法搜索引导文案


const unsigned int CMD_ListTopicChannel             = 3080; // 新版首页房间列表
const unsigned int CMD_GetSecondaryFilter           = 3081; // 新版指定tab首页筛选器
const unsigned int CMD_GetSecondaryFilterByCategory = 3082; // 新版指定目录首页筛选器
const unsigned int CMD_GetDefaultRoomNameList       = 3083; // 新版获取默认房间名

const unsigned int CMD_PublishGangupChannel = 3084; // 开黑玩法发布
const unsigned int CMD_CancelGangupChannelPublish = 3085; // 开黑玩法取消发布
const unsigned int CMD_GetHomePageHeadConfig = 3086; // 新版首页获取金刚区配置

const unsigned int CMD_GetHotMiniGames = 3087; // 休闲互动专区--重点游戏模块
const unsigned int CMD_GetQuickMiniGames = 3088; // 休闲互动专区--快速匹配模块
const unsigned int CMD_GetPlayQuestions = 3089; // 获取开黑玩法问题

const unsigned int CMD_GameInsertFlowConfig = 3090; // 游戏专区--快速匹配/中长尾创建房间 强插配置
const unsigned int CMD_GetHomePageGuide = 3091; // 金刚区/专区 引导文案
const unsigned int CMD_GetMoreTabConfig = 3092; // 获取更多玩法数据，首页和专区里面同一个接口

const unsigned int CMD_GetFilterItemByEntrance = 3093; // 2023.4 首页，游戏专区，休闲互动专区筛选器
const unsigned int CMD_SetDIYFilterByEntrance = 3094; // 2023.4 首页，游戏专区，休闲互动专区设置自定义筛选器（常玩分类）
const unsigned int CMD_GetDIYFilterByEntrance = 3095; // 2023.4 首页，游戏专区，休闲互动专区获取自定义筛选器（常玩分类）

const unsigned int CMD_GetNegativeFeedBackInRoom = 3096; // 2023.6 获取房间内负反馈原因列表
const unsigned int CMD_ReportNegativeFeedBackInRoom = 3097; // 2023.6 上报房间内负反馈

const unsigned int CMD_GetPublishOptionGuide = 3098; // 2023.7 房间内发布弹窗半屏页，房间标题发布引导词

const unsigned int CMD_GetNewQuickMatchConfig = 3099; // 2023.7 首页，游戏专区，新版快速匹配入口配置

const unsigned int CMD_GetTopicChannelCfgInfo = 3100;          // 获取房间玩法、配置相关信息

const unsigned int CMD_SetUgcChannelPlayMode = 3101;          //设置房间玩法模式（语音，文字）
const unsigned int CMD_TypingStatusBroadcast = 3102;          //文字房用户输入状态广播
const unsigned int CMD_GetChannelPlayModeGuide = 3103;        //文字房引导文案
const unsigned int CMD_ReportDailyTask = 3104;                //开黑每日任务上报
const unsigned int CMD_HomePageHeadConfigEnterCheck = 3105;   //首页专区是否能进入检查判断
const unsigned int CMD_GetChannelListGuideConfigs = 3106;   //首页专区房间列表引导配置
const unsigned int CMD_GetTabInfos = 3107;   //获取玩法信息
const unsigned int CMD_GetRecallPopUp = 3108;   //获取回归弹窗
const unsigned int CMD_SubmitRecallPopUp = 3109;   //提交回归弹窗
const unsigned int CMD_GetRecallTeamUp = 3110;   //获取组队弹窗
const unsigned int CMD_SubmitRecallTeamUp = 3111;   //提交组队弹窗

const unsigned int CMD_GetRecommendGames = 3112;   //获取官方推荐游戏
const unsigned int CMD_RefreshGameLabel = 3113;   //更新客户端本地标签缓存
const unsigned int CMD_GetSupportTabList = 3114;   // 返回某端可展示的玩法列表
const unsigned int CMD_GetChannelMicVolSet = 3115;   // 拉取房间麦位音量设置
const unsigned int CMD_SetChannelMicVol = 3116;   // 设置房间麦位音量

/** 主题房间 END 3000~3200 **/

/* 用户标签 userTag BEGIN 3201 ~ 3300*/
const unsigned int CMD_GetUserTag = 3201;    // GetUserTagReq 获取用户标签
const unsigned int CMD_SetUserTag = 3202;    // SetUserTagReq 设置用户标签
const unsigned int CMD_GetUserTagBaseConfigList = 3203;  // GetUserTagBaseConfigListReq 获取后台配置的标签基本信息
const unsigned int CMD_GetUserTagFullConfigList = 3204;  // GetUserTagFullConfigListReq 获取后台配置的标签全量信息
const unsigned int CMD_GetUserGameTypeTagConfigList = 3205;  // GetUserGameTypeTagConfigListReq 获取后台配置的游戏标签详细信息
const unsigned int CMD_SetUserOneTag = 3206;  // SetUserOneTagReq 设置单个标签
/* 用户标签 userTag END   3201 ~ 3300 */

/* 用户标签匹配 usermatchsvr  BEGIN 3301 ~ 3400*/
const unsigned int CMD_UserTagMatchBegin = 3301;       // [已经下线废弃的业务] 用户发起匹配    [标签匹配用户业务下线 废弃命令]
const unsigned int CMD_GetUserTagMatchUsers = 3302;    // [已经下线废弃的业务] 得到匹配 user   [标签匹配用户业务下线 废弃命令]
const unsigned int CMD_UserTagMatchPick = 3303;        // [已经下线废弃的业务] 点击心，进入 im [标签匹配用户业务下线 废弃命令]
/* 用户标签匹配 usermatchsvr  END 3301 ~ 3400*/

const unsigned int CMD_AppReportOpenApp = 3304;        // app 上报打开app

/* 新用户承接页 3310 ~ 3320 */
const unsigned int CMD_GetNewbiePageConfig = 3310;  /* 获取新用户承接页配置 */
const unsigned int CMD_SetUserNewbiePageTag = 3311;  /* 用户选择兴趣标签 */
const unsigned int CMD_SetUserPageTagListRequest=3312;  /*设置用户标签页列表*/
const unsigned int CMD_GetUserPageTagListRequest=3313; /*获取用户标签列表*/
/* 新用户承接页 END*/

/* profile-logic BEGIN 3401 ~ 3500*/
const unsigned int CMD_UserDecorations = 3401; // 用于获取用户的装饰品
const unsigned int CMD_UserCurrDecoration = 3402; // 用于获取用户当前佩戴的装饰品
const unsigned int CMD_UserAdornDecoration = 3403; // 用于用户佩戴装饰品
const unsigned int CMD_UserRemoveDecoration = 3404; // 用于用户卸下装饰品
/* profile-logic END 3401 ~ 3500*/

const unsigned int CMD_GetActivityRank = 3405; //取年度盛典排行榜数据

const unsigned int CMD_GetEnterTagFlag = 3406; //未成年人娱乐tag开关

// 属于profile-logic, 但是前两个命令号被占用了
const unsigned int CMD_ChangeDecorationCustomText = 3407; // 修改装饰品的自定义文案

/*  麦下用户  3500 ~ 3510 BEGIN*/
const unsigned int CMD_ChannelGetUnderTheMicroList = 3501; // 获取麦下用户列表
/*  麦下用户  3500 ~ 3510 END*/

const unsigned int CMD_ChannelGetMicroUserGameTagList = 3502; // 游戏玩法房获得麦上用户游戏卡片
//语音直播房相关
const unsigned int CMD_GetAnchorValidPlateList         = 3565;  // 获取主播正生效的铭牌列表
const unsigned int CMD_WearAnchorPlate                 = 3566;  // 佩戴粉丝团铭牌
const unsigned int CMD_ChannelLiveAppointPkAccept      = 3567;  // 接受指定pk邀约
const unsigned int CMD_ChannelLiveConfirmAppointPkPush = 3568;  // 确定收到指定pk推送
const unsigned int CMD_ChannelLiveGetAppointPkInfo     = 3569;  // 主播进房获取指定pk相关信息
const unsigned int CMD_ChannelLiveGetPkApllyList       = 3570;  // 获取PK申请列表
const unsigned int CMD_ChannelLivePkInfo               = 3571;  // 获取PK信息
const unsigned int CMD_ChannelLivePkGetMyTools         = 3572;  // 获取我已经获得到道具
const unsigned int CMD_ChannelLivePkItemConfig         = 3573;  // 获取PK道具配置

const unsigned int CMD_ChannelLivePkMicFlag            = 3574;  //直播PK中，设置能否听到对面房间声音
const unsigned int CMD_ChannelLivePkMatch              = 3575;  //开始PK匹配
const unsigned int CMD_ChannelLivePkCancelMatch        = 3576;  //取消PK匹配
const unsigned int CMD_ChannelLivePkMatchInfo          = 3577;  //取PK匹配信息

const unsigned int CMD_ChannelLiveInfo                 = 3580; //查询是否有直播权限
const unsigned int CMD_ChannelLiveHeartbeat            = 3581; //主播客户端，直播心跳
const unsigned int CMD_ChannelLiveSetChannelLiveStatus = 3582; //主播设置直播状态
const unsigned int CMD_ChannelLiveGetChannelLiveStatus = 3583; //查询对应UID的直播房状态
const unsigned int CMD_ChannelLiveApplyPk           = 3584;    //申请PK连麦
const unsigned int CMD_ChannelLiveHandlerApply      = 3585;    //处理PK连麦申请
const unsigned int CMD_ChannelLiveBatchGetStatus    = 3586;    //根据account批量取直播状态信息
const unsigned int CMD_ChannelLiveSetPkStatus       = 3587;    //设置PK状态，比如结束PK
const unsigned int CMD_ChannelLiveCancelPKApply     = 3588;    //取消PK申请
const unsigned int CMD_ChannelLivePKRecord          = 3589;    //取PK历史记录
const unsigned int CMD_ChannelLivePKRank            = 3590;    //取PK送礼榜
const unsigned int CMD_ChannelLiveRank              = 3591;    //直播送礼记录
const unsigned int CMD_ChannelLiveWatchTimeRank     = 3592;    //直播观看时长榜
const unsigned int CMD_ChannelLiveData              = 3593;    //直播数据统计
const unsigned int CMD_ChannelLiveGetFansRankList   = 3594;    //获取粉丝亲密度排行榜
const unsigned int CMD_ChannelLiveGetFansInfo       = 3595;    //获取粉丝信息
const unsigned int CMD_ChannelLiveGetAnchorFansInfo = 3596;    //获取主播粉丝团信息

const unsigned int CMD_ChannelLiveGetAnchorInfo = 3597;      // 获取主播信息
const unsigned int CMD_SearchAnchor             = 3598;      // 搜索主播
const unsigned int CMD_ReportClientIdChange     = 3599;      // 客户端 上报流ID变化

const unsigned int CMD_ChannelLiveGetUserMissionList  = 3600; // 用户任务列表
const unsigned int CMD_ChannelLiveGetFansMissionList  = 3601; // 粉丝任务列表
const unsigned int CMD_ChannelLiveGetActorMissionList = 3602; // 主播任务列表
const unsigned int CMD_ChannelLiveHandleUserMissionAtInterval   = 3603; // 每隔5分钟处理一次用户任务（用于相关观看时长的任务）
const unsigned int CMD_ChannelLiveHandleShareLiveChannelMission = 3604; // 处理每日分享直播间任务
const unsigned int CMD_ChannelLiveGetActorMissionProcess        = 3605; // 获取主播进行中的任务缩略信息
const unsigned int CMD_ChannelLiveHandleFansMissionAtInterval   = 3606; // 每隔5分钟处理一次粉丝任务（用于粉丝相关观看时长的任务）

const unsigned int CMD_ChannelLiveGetAnchorHonorNameplate       = 3610; // 获取主播荣誉铭牌
const unsigned int CMD_ChannelLiveGetRankingList                = 3611; // 获取语音主播榜单

const unsigned int CMD_ChannelLiveGetFansAddedGroupInfo         = 3630; // 获取粉丝加入的粉丝团信息
const unsigned int CMD_ChannelLiveSetFansGroupName              = 3631; // 设置粉丝团名称
const unsigned int CMD_ChannelLiveCheckSetGroupNamePermit       = 3632; // 检测主播是否有设置粉丝团名称权限
const unsigned int CMD_ChannelLiveCheckUserIsFans               = 3633; // 检测用户是否是主播粉丝
const unsigned int CMD_ChannelLiveChatReport                    = 3634; // 直播房公屏聊天上报
const unsigned int CMD_ChannelLiveLeaveFansGroup                = 3635; // 退出粉丝团
const unsigned int CMD_GetVirtualLiveChannelSecret              = 3636; // 获取虚拟主播房间密钥
const unsigned int CMD_GetUserFansGiftPri                       = 3637; // 获取用户粉丝团专属礼物特权




const unsigned int CMD_GetSuperPlayerConf         = 3700; //获取超级会员配置
const unsigned int CMD_GetSuperPlayerInfo         = 3701; //获取超级会员信息
const unsigned int CMD_BatchGetSuperPlayerInfo    = 3702; //匹配获取会员信息
const unsigned int CMD_GetUserSpecialConcern      = 3703; //获取特别关心
const unsigned int CMD_AddUserSpecialConcern      = 3704; //添加特别关心
const unsigned int CMD_DelUserSpecialConcern      = 3705; //移除特别关心
const unsigned int CMD_GetIMPrivilegeCount        = 3706; //获取IM搭讪特权剩余次数
const unsigned int CMD_UseIMPrivilege             = 3707; //使用一次IM搭讪特权
const unsigned int CMD_GetSuperPlayerEntryAdvConf = 3708; //获取会员主入口广告语配置
const unsigned int CMD_GetUserSVIPPrivilegeProfile = 3709; //获取SVIP权益设置
const unsigned int CMD_SetUserSVIPPrivilegeProfile = 3710; //更新SVIP权益设置
const unsigned int CMD_UseSVIPStealthAhead         = 3711; //提前使用SVIP隐身权益天数
const unsigned int CMD_GetRenewalReminder          = 3712; //获取续费提醒
const unsigned int CMD_ReportSneakilyRead          = 3713; //上报开启局部悄悄看

const unsigned int CMD_CheckCouponPopUp            = 3714;    //检查是否弹优惠券弹窗
const unsigned int CMD_GetCouponPopUpLimit         = 3715; //获取优惠券弹窗限制

/*** 3750-3770 会员装扮***/
const unsigned int CMD_GetSuperPlayerDressConf = 3750;                      //获取超级会员装扮配置
const unsigned int CMD_GetSuperPlayerDressConfVersion = 3751;               //获取超级会员装扮配置版本
const unsigned int CMD_GetSuperPlayerChannelCurrDressId = 3752;             //获取超级会员当前房间装扮
const unsigned int CMD_GetSuperPlayerCurrSpecialConcernDressId = 3753;      //获取超级会员特别关心装扮
const unsigned int CMD_RemoveSuperPlayerChannelCurrDressId = 3754;          //取消佩戴超级会员当前房间装扮
const unsigned int CMD_GetUserCurrChatBubblDressId = 3755;          		//获取超级会员当前聊天气泡装扮
const unsigned int CMD_GetUserCurrChatBgDressIdList = 3756;          		//获取超级会员当前聊天背景装扮
/*** 3750-3770 会员装扮***/

/*** begin骑士团 3771-3790 ***/
const unsigned int CMD_JoinKnightGroup = 3771; //加入骑士团
const unsigned int CMD_GetKnightLoveRank = 3772; //取真爱榜
const unsigned int CMD_GetKnightWeekRank = 3773; //取周榜

const unsigned int CMD_GetKnightGroupEntry      = 3774; // 骑士团入口-个人资料卡/主播卡片
const unsigned int CMD_GetKnightGroupDetialInfo = 3775; // 骑士团特权页
const unsigned int CMD_GetKnightGroupOpenStatus = 3776; // 骑士团开通按钮
const unsigned int CMD_GetKnightCardInfo        = 3777; // 房间个人资料卡-获取骑士铭牌

const unsigned int CMD_GetKnightMission        = 3778; // 骑士团-骑士团任务
const unsigned int CMD_GetKnightGroupCampInfo = 3779; //取骑士团营地入口信息
/*** end 骑士团***/


/*** begin 神秘人 ***/
const unsigned int CMD_YouKnowWho_ChangeUKWSwitch   = 3790; // 神秘人开关
const unsigned int CMD_YouKnowWho_GetInfo           = 3791; // 获取神秘人信息
const unsigned int CMD_YouKnowWho_GetUKWUserProfile = 3792; // 指定uid获取神秘人用户信息
const unsigned int CMD_YouKnowWho_ChangeRankSwitch  = 3793; // 神秘人周榜/爱意榜开关

const unsigned int CMD_YouKnowWho_SendShowUpMsg     = 3794; //发送互动消息
const unsigned int CMD_YouKnowWho_GetShowUpMsgList  = 3795; //主态取收到的互动消息
const unsigned int CMD_YouKnowWho_GetShowUpTextList = 3796; //客态取消息文案列表

const unsigned int CMD_YouKnowWho_ExposureUKW       = 3797; // 神秘人现身
const unsigned int CMD_YouKnowWho_UserChangeUKWEnterNotice = 3798; // 切换神秘人是否进房前询问的状态

/***end 神秘人***/

/*** 营收铭牌 3861-3880 ***/
const unsigned int CMD_GetUserNameplateInfo = 3861; // 获取用户铭牌配置
const unsigned int CMD_SetUserNameplateInfo = 3862; // 设置用户铭牌配置
const unsigned int CMD_GetUserAllNameplateList = 3863; // 获取用户所有可配置铭牌

/*** 营收铭牌 end ***/


// 直播多人PK 4001~4050
const unsigned int CMD_GetChannelLiveMultiPkPermission      = 4001; // 获取直播多人PK权限

const unsigned int CMD_SerarchMultiPkAnchor                 = 4002; // 搜索主播
const unsigned int CMD_ApplyChannelLiveMultiPk              = 4003; // 发起多人PK邀请
const unsigned int CMD_MatchMultiPk                         = 4004; // 开始多人PK随机匹配
const unsigned int CMD_CancelMatchMultiPk                   = 4005; // 取消多人PK匹配
const unsigned int CMD_AcceptChannelLiveMultiPk             = 4006; // 处理收到多人PK邀请
const unsigned int CMD_StartChannelLiveMultiPk              = 4007; // 开始多人PK

const unsigned int CMD_GetChannelLiveMultiPkRank            = 4008; // 获取PK火力榜
const unsigned int CMD_GetChannelLiveMultiPkKnightList      = 4009; // 获取在线骑士列表

const unsigned int CMD_GetChannelLiveMultiPkRecordList      = 4011; // 获取最近多人PK的主播列表
const unsigned int CMD_CancelChannelLiveMultiPkTeam         = 4012; // 取消多人PK组队
const unsigned int CMD_StopChannelLiveMultiPk               = 4013; // 提前结束多人PK
const unsigned int CMD_DisinviteChannelLiveMultiPk          = 4014; // 取消邀请
const unsigned int CMD_GetChannelLiveMultiPkTeamInfo        = 4015; // 获取pk队伍信息, 废弃
const unsigned int CMD_InitChannelLiveMultiPkTeam           = 4016; // 初始化多人pk队伍

/** 游戏搭子 4051~5000 BEGIN **/
const unsigned int CMD_CheckPublishCondition = 4051; // 检查发布条件
const unsigned int CMD_GetGamePalCardProps = 4052; // 获取游戏搭子卡属性
const unsigned int CMD_PublishGamePalCard = 4053; // 发布游戏搭子卡
const unsigned int CMD_LightenGamePalCard = 4054; // 点亮游戏搭子卡
const unsigned int CMD_ExtinguishGamePalCard = 4055; // 熄灭游戏搭子卡
const unsigned int CMD_GetGamePalCardList = 4056; // 获取用户所有游戏主题搭子卡列表
const unsigned int CMD_GetGamePalCard = 4057; // 获取用户单个游戏主题搭子卡
const unsigned int CMD_DeleteGamePalCard = 4058; // 删除游戏搭子卡
const unsigned int CMD_GetGamePalFilterByTabId = 4059; // 获取游戏搭子卡筛选项
const unsigned int CMD_GetGamePalList = 4060; // 获取游戏搭子卡列表
const unsigned int CMD_GamePalSuperPublish = 4061; // 游戏搭子超级发布
const unsigned int CMD_ReportGamePalGreeting = 4062; // 上报给游戏搭子打招呼
const unsigned int CMD_GetGamePalSuperPublishProps = 4063; // 获取超级发布属性
const unsigned int CMD_GetGamePalCardByIds = 4064; // 根据搭子卡id，返回列表强插的搭子卡信息
const unsigned int CMD_GetGreetingGamePalUserList = 4065; // 获取一键 say hi 半屏游戏搭子列表
const unsigned int CMD_FilterGreetingGamePalUserList = 4066; // 筛选满足打招呼条件的游戏搭子列表
const unsigned int CMD_GetIMShowGamePalCard = 4067; // 获取IM聊天详情页展示的对方搭子卡信息
const unsigned int CMD_ShowUserGamePalGuide = 4068; // 判断是否展示引导用户填写搭子卡弹窗
const unsigned int CMD_GetImTabGamePalList = 4069; // 获取im tab列表的搭子卡列表
const unsigned int CMD_HandleGamePalCardOnPublishRoom = 4070; // 发布房间时处理擦亮搭子卡/创建引导逻辑
const unsigned int CMD_AssistantPushGamePal = 4071; // 客户端上报触发助手推送搭子卡片，初期有：登陆后n分钟助手推送
/** 游戏搭子 END **/


const unsigned int CMD_ReportBlackRiskCheck = 5001; 	// 上报数美黑产的分析结果

/** channelvotepk BEGIN **/
const unsigned int CMD_ChannelVotePKVote 	= 5002; //投票
const unsigned int CMD_ChannelVotePKStart 	= 5003; //开始PK
const unsigned int CMD_ChannelVotePKCancel 	= 5004; //取消PK
const unsigned int CMD_ChannelVotePKGetInfo = 5005; //拿消息
/** channelvotepk END **/

/** channel draw game BEGIN  **/
const unsigned int CMD_ChannelDrawGameCreate  = 5006; //添加线
const unsigned int CMD_ChannelDrawGameCancel  = 5007; //取消线
const unsigned int CMD_ChannelDrawGameGetPicture = 5008; //拿整图
const unsigned int CMD_ChannelDrawGameRemPicture = 5009; //删除图
const unsigned int CMD_ChannelDrawGameAddPoint   = 5010; //向线中加点
const unsigned int CMD_ChannelDrawGameCancelByUid   = 5011; //取消UID的全部线
const unsigned int CMD_ChannelDrawGameSetBoardStatus   = 5012; //设置房间画布开关,  用于设置房间是否允许使用画板
const unsigned int CMD_ChannelDrawGameSetDrawStatus    = 5013; //用户打开、关闭画布,用于设置用户当前是否在画画
const unsigned int CMD_ChannelDrawGameGetBoardStatus   = 5014; //获取画布开关
const unsigned int CMD_ChannelDrawGameGetDrawStatusList = 5015; //拿正在绘画列表
const unsigned int CMD_ChannelDrawGameGetLineParaList   = 5016; //画笔配置信息
/** channel draw game END  **/


/** 语音验证码begin **/
const unsigned int CMD_AccountVoiceVerifyCode   	= 5050; // 获取语音短信验证码
/** 语音验证码end **/

/**娱乐推荐tag begin**/
const unsigned int CMD_GetRecFeedbackConfig = 5053;   // //获取推荐列表房间反馈配置
const unsigned int CMD_DoRecFeedback = 5054;   //推荐房间反馈
const unsigned int CMD_GetRecLotteryChList = 5055;   // 获取抽奖房间推荐列表
const unsigned int CMD_GetRecChListByPerTagId = 5056;   // 根据个性tagid请求房间数据
const unsigned int CMD_GetWarSongRecommonChannelList = 5057;   // 获取战歌处房间推荐列表
const unsigned int CMD_GetChannelTagInfo = 5058;      // 根据channelId获取房间的标签信息
const unsigned int CMD_GetQuickRecChannelByTagId = 5059;  //根据tagId获取快速入口推荐房间
const unsigned int CMD_GetQuickEntryConfig = 5060;  //获取娱乐tab快速入口场景配置
const unsigned int CMD_GetRecommonChannels = 5061; //拿娱乐推荐房列表
const unsigned int CMD_GetRecommonChannelByTagId = 5062; //根据tag拿推荐房列表
const unsigned int CMD_GetTagConfig        = 5063;       //拿tag配置
const unsigned int CMD_GetHotChannel       = 5064;       //拿热门房间
/**娱乐推荐tag end**/

/** 主播签约 begin **/
const unsigned int CMD_GetContractInfo = 5065;  // 得到合约信息 (路由转发到CMD_NewGetContractInfo-39100)
/** 主播签约 end **/

/**  热词搜索 begin **/
const unsigned int CMD_FetchHotWordGroups = 5067; // 拉取配置的热词组
/** 热词搜索 end **/
/** 贵族系统 begin **/
const unsigned int CMD_ChannelVisibleStatus = 2098;             //设置房间隐身状态
const unsigned int CMD_BatchGetNobilityInfos = 2099;            //批量取聊天列表贵族信息
const unsigned int CMD_GetTrumpetLeftCnt = 5066;                //取剩余小喇叭次数
const unsigned int CMD_GetNobilityInfo = 5068;                 // 获取贵族保级提醒信息，本来想搞成获取贵族信息的通用接口, 由于实现逻辑问题，这个接口只用来获取贵族保级提醒信息
const unsigned int CMD_GetNobilityDetailInfo = 5069;            // 获取贵族相关信息

const unsigned int CMD_SetNobilitySwitch = 5070;            // 设置贵族聊天、在线隐身
const unsigned int CMD_GetNobilitySwitch = 5071;         // 取贵族聊天、在线隐身开关
/** 贵族系统 end **/

/* 新版娱乐TAB begin : 5080-5090 **/
const unsigned int CMD_GetRecommonChannelsV2 = 5080; //新版获取娱乐推荐房列表
const unsigned int CMD_GetRecommonChannelByTagIdV2 = 5081; //新版 根据tag拿推荐房列表
const unsigned int CMD_GetRecChListByPerTagIdV2    = 5082; //新版 个性化标签房列表
const unsigned int CMD_GetQuickEntryConfigV2 = 5083;  //新版 获取娱乐tab快速入口场景配置
const unsigned int CMD_GetChannelTopOverLay = 5084;  //获取房间顶部浮窗
const unsigned int CMD_GetRevenueSwitchHub = 5085;  //获取营收开关
const unsigned int CMD_SetRevenueSwitchHub = 5086;  //设置营收开关
const unsigned int CMD_GetGlobalTopOverLay = 5087;  //获取顶部浮窗

/* 新版娱乐TAB end : 5080-5090 **/

// 开黑红点服务 5091-5100
const unsigned int CMD_GetRedDotInfo = 5091; // 获取红点信息
const unsigned int CMD_MarkRedDotRead =  5092; // 标记红点已读
const unsigned int CMD_BatchMarkRedDotRead =  5093; // 批量清除红点

//开黑游戏动态 5400-5500
const unsigned int CMD_GetGameNewsFeeds = 5400;         // 开黑动态流
const unsigned int CMD_GamePostPost = 5401;         // 开黑专区发帖
const unsigned int CMD_GetSubTabConfigByTabId = 5402;   // 废弃，开黑专区动态tab配置
const unsigned int CMD_GetConfigByTabId = 5403;   // 开黑专区动态tab配置
const unsigned int CMD_GetConfigTabTitle = 5404;   // 开黑发帖获取动态tab的标题数据
const unsigned int CMD_CheckUserIsBannedPost = 5405;   // 开黑专区判断用户是否被禁止发帖
const unsigned int CMD_GetComprehensiveChannelInfo = 5406;   // 开黑专区获取综合频道活动tab配置
const unsigned int CMD_GetGameFeedByIds = 5407;   // 根据帖子id，获取帖子列表信息
const unsigned int CMD_NeedShowPersonalSourceFilter = 5408;   // 是否展示个人页筛选
const unsigned int CMD_IsConfigTabVisible = 5409;   // 判断玩法下二级tab是否显示

//开黑组队大厅 5500-5600
const unsigned int CMD_GetBuildChannelInfo = 5500;         // 获取订阅令牌及频道路径
const unsigned int CMD_GetMsgList = 5501;         // 拉取历史消息
const unsigned int CMD_SendGameImMsg = 5502;         // 发送消息
const unsigned int CMD_GetGameHallTeamList = 5504;    // 拉取组队信息
const unsigned int CMD_CheckHallEnterRoom = 5505;     // 组队大厅是否可进房前置判断
const unsigned int CMD_CheckInviteMemFre = 5506;    // 邀请进房IM发送限制(废弃)
const unsigned int CMD_GetUserUnreadAtMsg = 5507;    // 拉取用户未读的@消息
const unsigned int CMD_MarkAtMsgRead = 5508;    // 标记@消息为已读
const unsigned int CMD_CheckSendInviteRoomCond = 5509;    // 检查用户是否可以发送邀请进房
const unsigned int CMD_UpdateGameHallNotifyStatus = 5510;    // 更改组队大厅消息通知状态
const unsigned int CMD_GetGameHallNotifyStatus = 5511;    // 拉取组队大厅消息通知状态

/** 打boss begin**/
const unsigned int CMD_GetMonsterActivityTimeRange = 5600; //取打boss活动开始时间段列表
const unsigned int CMD_GetMonsterList = 5601; //取当前房间的boss信息列表
const unsigned int CMD_AttackMonster = 5602; //攻击boss
const unsigned int CMD_GetOneMonsterChannel = 5603; //随机取一个有boss的房间
const unsigned int CMD_GetHuntMonsterItem   = 5604; //获取用户打龙道具数量
const unsigned int CMD_GetUserHuntMissionInfo = 5605;   // 获取用户打龙任务信息
/** 打boss end **/


//文字房接口 5700-5750
const unsigned int CMD_CharacterApplyOnMic = 5700;         // 申请上麦
const unsigned int CMD_CharacterAgreeApplyMic = 5701;         // 同意上麦


// 游戏专区群聊接口 5801-5900
const unsigned int CMD_GetGroupChatCreateProp = 5801;  // 获取创建群聊元素数据
const unsigned int CMD_CreateTGroupChat = 5802;         // 创建群聊
const unsigned int CMD_GetGroupChatList = 5803;        // 群聊列表
const unsigned int CMD_GetGroupChatExtraInfo = 5804;   // 专区建群额外信息
const unsigned int CMD_SetGroupChatExtraInfo = 5805;   // 修改用户群标签
const unsigned int CMD_PreviewGroupChatMsg = 5806; // 预览群聊消息


/* game-play-logic接口 5901-6000 begin */
const unsigned int CMD_UserGameRateReport = 5901; // 用户对开黑用户评价上报
const unsigned int CMD_GetUserNotRateCount = 5902; // IM页面获取未评价的数量，展示红点
const unsigned int CMD_SetFirstChatToken = 5903; // IM页面首次聊天上报记录玩法信息
const unsigned int CMD_GetRateReputationInfo = 5904; // 获取互评标签和信誉分信息
const unsigned int CMD_GetUserAcquisitionAndABTestResult = 5905; // 查询用户获客口径和AB实验结果
const unsigned int CMD_TransAudioToText = 5906; // 音频转换文本asr
const unsigned int CMD_GetRegisterPageConfigs = 5907; // 注册页，开黑配置选项
const unsigned int CMD_SaveGameTime = 5908; // 保存游戏时间信息
const unsigned int CMD_GetGameTime = 5909; // 获取游戏时间信息
const unsigned int CMD_SetEnterRoomNotify = 5910; // 设置进房提醒
const unsigned int CMD_CheckUserInRoom = 5912; // 检查用户是否在线/在房
const unsigned int CMD_GetGameTimeList = 5913; // 获取游戏时间列表
const unsigned int CMD_FastPCFeedback = 5914; // 极速PC用户反馈接口
/* game-play-logic接口 5901-6000 end */

/* 开黑invite-room-logic接口 6001-6050 begin */
const unsigned int CMD_GetInviteEntranceInfo = 6001; // 获取开黑邀请进房入口信息
const unsigned int CMD_GetInviteList = 6002; // 获取邀请进房列表
const unsigned int CMD_GetBeInvitedRecord = 6003; // 获取受邀进房记录
const unsigned int CMD_GetFilterLabels = 6004; // 获取邀请列表筛选标签
const unsigned int CMD_UserSendInvite = 6005; // 用户发出邀请
const unsigned int CMD_UserRefuseInvite = 6006; // 用户拒绝邀请
const unsigned int CMD_OpenTodayDontDisturb = 6007; // 开启今日免打扰
const unsigned int CMD_InviteBlockUser = 6008; // 屏蔽该用户
const unsigned int CMD_UserBehaviorStatusReport = 6009; // 用户行为状态数据上报
/* 开黑invite-room-logic 6001-6050 end */


/* 开黑im-guide-logic接口 6051-6100 begin */
const unsigned int CMD_GetGameAppointmentEntrance = 6051; // 获取预约入口信息
const unsigned int CMD_SendGameAppointment = 6052; // 发送邀请
const unsigned int CMD_HandleGameAppointment = 6053; // 处理邀请
//const unsigned int CMD_GetGameAppointmentDetail = 6054; // 获取预约详情信息
const unsigned int CMD_SetGameAppointmentRemind = 6055; // 设置预约提醒
const unsigned int CMD_MsgSourceTypeReport = 6056; // 会话来源上报
const unsigned int CMD_GetImBeginChatSuggestion = 6057; // 获取im开聊匹配文案
const unsigned int CMD_GetImReplyChatSuggestion = 6058; // 获取接话聊天匹配推荐回复
const unsigned int CMD_SetUserOnlineRemind = 6059; // 设置用户上线提醒
const unsigned int CMD_GetOnlineRemindSetting = 6060; // 获取用户上线提醒设置
const unsigned int CMD_IsShowLoginRemindGuide = 6061; // 是否展示上线提醒引导
/* 开黑im-guide-logic 6051-6100 end */

/* 引导推送服务 interact-guide-logic 6101-6150 begin */
const unsigned int CMD_InteractionRemindNotify = 6101; // 互动提醒通知

/* 引导推送服务 interact-guide-logic 6101-6150 end */


/** 新登录相关 begin **/
const unsigned int CMD_GetPcAuthApplyResult = 6600;  // 登录前获取PC授权申请结果
const unsigned int CMD_GetPcAuthApply = 6601; // 获取PC授权申请
const unsigned int CMD_ProcPcAuthApply = 6602; // 登录后处理PC授权申请
const unsigned int CMD_GetPcAuthApplyResultV2 = 6603;  // 登录后获取PC授权结果
const unsigned int CMD_ProcPcAuthApplyBeforeAuth = 6604; // 登录前处理PC授权申请
// const unsigned int CMD_ = 6666;
/** 新登录相关 end **/


//** 签约相关 begin ***//
const unsigned int CMD_CheckUserInteractEntry = 6800;  // 检查用户是否有互动消息查看入口
const unsigned int CMD_GetUserInteractInfo = 6801;  // 获取用户的互动消息
const unsigned int CMD_GetUserInteractViewPer = 6802;  // 获取用户互动资料隐私查看权限
const unsigned int CMD_SetUserInteractViewPer = 6803;  // 设置互动资料隐私查看权限
const unsigned int CMD_ContractClaimObsToken = 6804;  // 签约解约专用的上传token获取
//** 签约相关 end ***//

//*** pgc房间相关 begin**//
const unsigned int CMD_GetUserTicketList = 6900; // 获取用户体验券列表
const unsigned int CMD_CheckTicketUsePer = 6901; // 检测体验券的使用权限
const unsigned int CMD_GetUserTicketRemind = 6902; // 获取券的提醒信息
const unsigned int CMD_GetUserTitledInfo = 6903; // 获取用户的被冠名信息
const unsigned int CMD_GetPgcGloryTitleAnchorRank = 6904; // 获取房间冠名成员榜
const unsigned int CMD_GetPgcGloryTitleInvestorRank = 6905; // 获取房间冠名-金主榜
const unsigned int CMD_GetUserTitleInfo = 6906; // 获取用户的冠名信息
const unsigned int CMD_UserTitlePrivilegeSwitch = 6907; //用户冠名权益开关
const unsigned int CMD_GetChannelTitleInfo = 6908; //获取房间冠名信息
const unsigned int CMD_CheckUserIsCanTitled = 6909; //判断用户是否能被冠名


//*** pgc房间相关 end**//


//*** T豆弹窗 begin**//
const unsigned int CMD_ExchangeGetUserScore = 7000; // 获取用户当前积分余额，并判断是否弹窗
const unsigned int CMD_ExchangeBeginTransaction = 7001; // 兑换积分为T豆

//*** T豆弹窗 end**//


/** Transmission BEGIN **/
const unsigned int CMD_TransmissionMin = 10000;

const unsigned int CMD_TransmissionHappyCity = 10001;   //[已经下线废弃的业务]欢城协议
const unsigned int CMD_TransmissionHappyCity2 = 10002;  //[已经下线废弃的业务]
const unsigned int CMD_TransmissionHappyCity3 = 10003;  //[已经下线废弃的业务]
const unsigned int CMD_TransmissionHappyCity4 = 10004;  //[已经下线废弃的业务]
const unsigned int CMD_TransmissionHappyCity5 = 10005;  //[已经下线废弃的业务]
const unsigned int CMD_TransmissionHappyCity6 = 10006;  //[已经下线废弃的业务]
const unsigned int CMD_TransmissionHappyCity7 = 10007;  //[已经下线废弃的业务]
const unsigned int CMD_TransmissionHappyCity8 = 10008;  //[已经下线废弃的业务]
const unsigned int CMD_TransmissionHappyCity9 = 10009;  //[已经下线废弃的业务]

const unsigned int CMD_TransmissionMax = 19999;
/** Transmission END **/

const unsigned int CMD_PullOfflineMsg = 20000;            //  Push系统拉取离线消息
const unsigned int CMD_ReportMessagesReceivedAck = 20001; //  Push系统 上报确认消息已经收到

/** AvatarLogic BEGIN 20020--20030 预留 **/
const unsigned int CMD_UploadAvatar = 20020;  // http方式上传头像

const unsigned int CMD_GETMSGFROMDB = 28888;  // [已经下线废弃的业务] 客服 拉取 客服与用户的聊天消息


const unsigned int CMD_GetHonorGuildList = 29000;//公会列表
const unsigned int CMD_SetGuildHotChannelList = 29001; //公会热门房间列表
const unsigned int CMD_SetGuildCharmMemberList = 29002; //公会魅力成员列表
const unsigned int CMD_SearchGuildMember = 29003; //公会成员搜索
const unsigned int CMD_GetHonorGuild = 29004; //公会名片

/** ChannelPresentCount BEGIN **/

const unsigned int CMD_ChannelPresentCount = 30000;  // 房间送礼统计开关
const unsigned int CMD_ChannelPresentCountInit = 30001; // 获得房间送礼统计是否开启，是则会返回当前麦上的礼物统计
const unsigned int CMD_ChannelPresentCountRank = 30002; // 房间计数器榜单

/** ChannelPresentCount END **/
const unsigned int CMD_ConfigV2Sync = 30010;
const unsigned int CMD_ConfigV2CheckSync = 30011; //  ConfigV2 CheckSync


/** UserBlackList BEGIN  **/

const unsigned int CMD_GetUserBlackList = 30020; // 获取用户拉黑名单
const unsigned int CMD_AddUserBlackList = 30021; // 添加用户拉黑名单
const unsigned int CMD_DelUserBlackList = 30022; // 删除用户拉黑名单
const unsigned int CMD_CheckIsInBlackList = 30023; // 检查用户是否在黑名单中

/** UserBlackList END  **/

/** SmashEgg BEGIN  **/

const unsigned int CMD_GetConsumeRecord = 30030; // 获取转转消费记录
const unsigned int CMD_GetRecentWinningRecord = 30031; // 获取转转最近中奖记录
const unsigned int CMD_GetWinningRecord = 30032; // 获取转转中奖记录
const unsigned int CMD_Recharge = 30033; // 转转充值
const unsigned int CMD_Smash = 30034; // 转转砸蛋
const unsigned int CMD_GetSmashStatus = 30035; // 转转获取状态
const unsigned int CMD_GetConfig = 30036; // 获取转转配置

const unsigned int CMD_GetSmashNotify  = 30037;                           //获取转转奖池及兑换变更推送

const unsigned int CMD_SmashEggGetResourceConfig = 30038;        // 获取活动主题资源配置
const unsigned int CMD_SmashEggGetUserPropList = 30039;          // 获取用户道具列表
const unsigned int CMD_SmashEggGetUserExpirePropNotify = 30040;  // 获取用户即将过期道具提醒

/** SmashEgg END  **/

//add 小游戏上报 30060 - 30069
const unsigned int CMD_MinGamesJoinReport  = 30060;				 //上报加入小游戏
const unsigned int CMD_MinGamesReadyReport = 30061;				 //上报小游戏准备
const unsigned int CMD_MinGamesLoginReport = 30062;				 //上报小游戏开始
const unsigned int CMD_GetMiniGamesCode    = 30063;				 //获取小游戏code

//add 房间小游戏的设置 30070 - 30079
const unsigned int CMD_GetChannelSupportGameList = 30070;		  //获取房间支持的小游戏列表
const unsigned int CMD_SetChannelLoadingGame     = 30071;		  //设置房间的小游戏
const unsigned int CMD_GetChannelLoadingGame     = 30072;		  //获取房间的小游戏信息
const unsigned int CMD_SetChannelGameRole     	 = 30073;		  //设置小游戏玩家角色，已废弃
const unsigned int CMD_SubmitGameCenterCmd     	 = 30074;		  //透传调用游戏平台
const unsigned int CMD_GetGameActivityReward     = 30075;		  //获取活动奖励
const unsigned int CMD_ReportGameActivityShared  = 30076;		  //上报活动分享
const unsigned int CMD_JoinChannelGameBegin      = 30077;		  //上报加入游戏
const unsigned int CMD_JoinChannelGameEnd        = 30078;		  //上报可以开始游戏
const unsigned int CMD_QueryChannelGameTick      = 30079;		  //查询用户倒计时

//add 查询 首页开黑tab广告位配置 30080  -  30085
const unsigned int CMD_GetGangupTabAdvConf     = 30080;		  //查询 首页开黑tab广告位配置

/* 一键绑定手机 begin 30086-30095*/

const unsigned int CMD_ChinaMobileThirdPartyBindPhone = 30086;	// 中国移动 第三方登录前绑定手机
const unsigned int CMD_ChinaMobileAccountBindPhone = 30087;		// 中国移动 用户账号登录前绑定手机
const unsigned int CMD_ChinaMobileUidBindPhone = 30088;			// 中国移动 用户UId登录后绑定手机
const unsigned int CMD_ChuangLanThirdPartyBindPhone = 30089;	// 创蓝SDK 第三方登录前绑定手机
const unsigned int CMD_ChuangLanAccountBindPhone = 30090;		// 创蓝SDK 用户账号登录前绑定手机
const unsigned int CMD_ChuangLanUidBindPhone = 30091;			// 创蓝SDK 用户UId登录后绑定手机

/*一键绑定手机 end  30086-30095*/

// 获取房间内推荐房  30100
const unsigned int CMD_GetChannelInnerRecommend = 30100;			// 获取房间内推荐房

// 任务系统 -- 打开通知权限
const unsigned int CMD_OpenNotificationPermission = 30101;			// 打开通知权限

// 房间游戏流程控制器 30111-30124
const unsigned int CMD_GetChannelGamePlayerOpenid = 30111;			// 获取游戏用户下的openid和code
const unsigned int CMD_JoinChannelGame = 30112;			            // 加入游戏
const unsigned int CMD_QuitChannelGame = 30113;			            // 退出游戏
const unsigned int CMD_ReadyChannelGame = 30114;			        // 准备游戏
const unsigned int CMD_UnReadyChannelGame = 30115;			        // 取消准备
const unsigned int CMD_GetChannelGameStatusInfo = 30116;			// 获取房间游戏状态数据
const unsigned int CMD_ExitChannelGame = 30117;			            // 退出房间
const unsigned int CMD_StartChannelGame = 30118;			        // 开始游戏
const unsigned int CMD_SetChannelGameModeInfo = 30119;			    // 设置房间游戏模式信息
const unsigned int CMD_SetChannelGamePlayerLoading = 30120;			// 设置房间内用户加载游戏状态
const unsigned int CMD_BatchGetUidByOpenid = 30121;			        // 根据openid和gameid获取账号信息

// ab测试 30125-30135
const unsigned int CMD_AbtestExpSync = 30125;			            // 获取进行中的实验

// 找人玩&&发放玩伴
const unsigned int CMD_ClosePopUp               = 30139; // 关闭房间下发弹窗
const unsigned int CMD_GetPlayer                = 30140; // 找人玩
const unsigned int CMD_PlayerProvided           = 30141; // 发放玩伴
const unsigned int CMD_StrangerCard             = 30142; // 陌生人城市&推荐语列表
const unsigned int CMD_GetPlayerFoundSetting    = 30143; // 找人玩&发放玩伴 get开关
const unsigned int CMD_UpdatePlayerFoundSetting = 30144; // 找人玩&发放玩伴 set开关

// 发布器 话题
const unsigned int CMD_GetPublisherTopic = 30145;			        // 发布器 话题
const unsigned int CMD_GetPosts = 30146;			        // 热门动态
const unsigned int CMD_GetNewestPosts = 30147;			        // 有新动态时插入im
const unsigned int CMD_TopicInRecommendFeed = 30149;			   /*推荐流插入话题*/





// 用户访问记录服务 30150-30159
const unsigned int CMD_ReportUserVisitorRecord         = 30150;     // 上报访客记录
const unsigned int CMD_GetUserVisitorRecordList        = 30151;     // 获取访问记录
const unsigned int CMD_GetUserBeVisitorRecordList      = 30152;     // 获取被访问记录
const unsigned int CMD_GetUserBeVisitorRecordCount     = 30153;     // 获取用户被访问总数
const unsigned int CMD_GetAllTaskStatus                = 30154;     // 获取所有的任务信息和状态
const unsigned int CMD_SetShowUserBeVisitorRecordCount = 30155;     // 设置用户是否隐藏访客数
const unsigned int CMD_GetShowUserBeVisitorRecordCount = 30156;     // 获取用户是否隐藏访客数
const unsigned int CMD_GetHideList                     = 30157;     // 获取用户设置的隐藏列表
const unsigned int CMD_GetHideStatusByUid              = 30158;     // 获取用户对其他用户的隐藏设置
const unsigned int CMD_SetHideStatusByUid              = 30159;     // 设置用户对其他用户的隐藏

// 劣质房过滤服务 30160-30169
const unsigned int CMD_QualityEventReport = 30160;                  // 上报客户端前后端切换

// 无归属的接口 30170-30179
const unsigned int CMD_XunfeiAstSignature = 30170;                  // 获取讯飞语音ast访问签名
const unsigned int CMD_GetExpandEntranceList = 30171;               // 获取扩展入口
const unsigned int CMD_OpenGameAuthorization = 30172;               // 获取游戏授权信息
const unsigned int CMD_SetChannelMediaInfo = 30173;                 // 设置房间多媒体信息
const unsigned int CMD_GetObsUploadToken = 30174;                   // 获取obs附件上传token
const unsigned int CMD_GetBlackWhiteBoxSuperviseSwitch = 30175;     // 获取黑白盒开关
const unsigned int CMD_CheckUserIdentity = 30176;                   // 检查用户身份

// 蒙面聊天服务 30180-30199
const unsigned int CMD_MaskedCallQueryMatchInfo = 30180;            // 查询匹配信息
const unsigned int CMD_MaskedCallStartMatch = 30181;                // 开始匹配
const unsigned int CMD_MaskedCallCancelMatch = 30182;               // 取消匹配
const unsigned int CMD_MaskedCallQueryCallInfo = 30183;             // 获取通话信息
const unsigned int CMD_MaskedCallSetConnectStatus = 30184;          // 设置连接状态
const unsigned int CMD_MaskedCallUnmask = 30185;                    // 公开身份
const unsigned int CMD_MaskedCallInviteUnmask = 30186;              // 邀请公开身份
const unsigned int CMD_MaskedCallComment = 30187;                   // 评价
const unsigned int CMD_MaskedCallRoll = 30188;                      // roll点

const unsigned int CMD_MaskedCallReportAudio = 30189;               // 上报音频数据
const unsigned int CMD_MaskedCallGetAudioUploadToken = 30190;       // 获取音频上传信息

// 亲密互动服务 30200-30204
const unsigned int CMD_BatchGetIntimacyInfo = 30200;             // 批量获取亲密度信息

// 师徒任务服务 30210-?
const unsigned int CMD_MasterApprenticeInvite = 30211;    // [已经下线废弃的业务]师父邀请
const unsigned int CMD_MasterApprenticeEstablish = 30212; // [已经下线废弃的业务]徒弟接受邀请
const unsigned int CMD_MasterApprenticeEntrance = 30213;  // [已经下线废弃的业务]徒弟 入口信息

const unsigned int CMD_RoomMasterApprenticeEntrance = 30214; //[已经下线废弃的业务] 师傅入口信息



//  --用于触发 任务： 发一条动态（不限类型）名称是：发1条带图片的动态
const unsigned int CMD_NewPostWithPic = 30220; // 发1条带图片的动态
const unsigned int CMD_CommentPostForMission = 30221; // 评论3条动态
const unsigned int CMD_AttitudeForMission = 30222; // 点赞10条动态

//一起玩
const unsigned int CMD_GetImPromoteLogicToken = 30300;				//获取一起玩token
const unsigned int CMD_GetPlayTogetherTabList = 30301;				//获取一起玩列表
const unsigned int CMD_GetPlayTogetherMatchConfig = 30302;			//获取一起玩文字匹配列表

//游戏雷达
const unsigned int CMD_InvitePlay = 30306;              //[已经下线废弃的业务]约玩
const unsigned int CMD_InviteSuc = 30307;               //[已经下线废弃的业务]确认约玩
const unsigned int CMD_RadarDisplay = 30308;            //[已经下线废弃的业务]雷达展开页列表
const unsigned int CMD_GetPlaymates = 30309;            //[已经下线废弃的业务]获取玩伴列表
const unsigned int CMD_GetPlaymateTabList = 30310;      //[已经下线废弃的业务]获取游戏选项
const unsigned int CMD_ListPlaymateTabBlocks = 30311;   //[已经下线废弃的业务]获取更多游戏选项

const unsigned int CMD_GetUserRadarStatus = 30312;      //[已经下线废弃的业务]获取用户雷达状态
const unsigned int CMD_GetRadarConfig = 30313;          //[已经下线废弃的业务]获取雷达配置
const unsigned int CMD_StartRadar = 30314;              //[已经下线废弃的业务]开启雷达
const unsigned int CMD_StopRadar = 30315;               //[已经下线废弃的业务]关闭雷达
const unsigned int CMD_GetRecommendNum = 30316;         //[已经下线废弃的业务]拿推荐数

const unsigned int CMD_GetChannelRecommendInRadar = 30317;         // 获取房间下发配置
const unsigned int CMD_GetFrequencyInRadar = 30318;                // 获取【每刷新4次找玩伴Tab，下发一次】的次数
const unsigned int CMD_GetPlaymateNum = 30319;          // 获取玩伴数量

/*CUE 获取配置*/
const unsigned int CMD_GetCUEConfig = 30330;                // CUE 获取配置

/*button配置*/
const unsigned int CMD_SavePushButtonInfo = 30336;  //保存
const unsigned int CMD_GetPushButtonInfo = 30337;  //获取

// 房间节目 30350-30355
const unsigned int CMD_GetChannelPerformance = 30350;             // 获取房间节目单
const unsigned int CMD_SetCurrentChannelPerformanceStage = 30351; // 设置房间当前节目

// 房间小队[已经下线废弃的业务] 30356-30365
const unsigned int CMD_JoinChannelTeam = 30356;               // [已经下线废弃的业务]用户进入小队
const unsigned int CMD_GetChannelTeamApplyList = 30357;       // [已经下线废弃的业务]房主获取申请列表
const unsigned int CMD_AgreeChannelTeamApply = 30358;         // [已经下线废弃的业务]房主同意用户申请入队
const unsigned int CMD_TickChannelTeamMember = 30359;         // [已经下线废弃的业务]用户退出小队或者房主踢用户退出小队
const unsigned int CMD_GetChannelTeamMemberList = 30360;      // [已经下线废弃的业务]获取小队成员信息
const unsigned int CMD_GetGangUpHistory = 30361;              // [已经下线废弃的业务]获取开黑过的用户列表
const unsigned int CMD_GetAllChannelMember = 30362;           // [已经下线废弃的业务]获取所有的房间用户
const unsigned int CMD_SetGameNickname = 30363;               // [已经下线废弃的业务]设置房间小队昵称

//const unsigned int CMD_PushGameNickname = 30364;            // 发送游戏昵称到公屏

// 房间等级 30370-30375
const unsigned int CMD_GetChannelLevelInfo = 30370;           // 获取房间等级信息
const unsigned int CMD_GetChannelLevelSettings = 30371;       // 获取房间等级配置信息

// IM活动中心
const unsigned int CMD_ImActivityCenterEntrance = 30380;      // IM活动中心入口

// OAuth2
const unsigned int CMD_GetScope = 30390;
const unsigned int CMD_GetAuthorizeCode = 30391;

// 房间deeplink推荐
const unsigned int CMD_GetRecommendChannelByRules = 30400;    // 根据规则获取推荐的房间
const unsigned int CMD_GetRecommendBusinessChannel = 30401;    // 匹配Tab-经营房间推荐

// 官方频道 30410-30430
const unsigned int CMD_GetOfficialLiveChannelInfo = 30410;          // 获取官频信息
const unsigned int CMD_GetOfficialLiveChannelRelaySchedule = 30411; // 获取官频转播计划
const unsigned int CMD_CancelOfficialLiveChannelRelay = 30412;      // 取消官频转播
const unsigned int CMD_GetLiveChannelRelay = 30413;                 // 获取直播间被转播信息
const unsigned int CMD_ReportRelayLiveChannelAudio = 30414;         // 上报直播间音频
const unsigned int CMD_GetOfficialChannelDescribe = 30415;          // 获取官频描述信息

// 房间抽奖功能 30431-30440
// -------------------- 官频-抽奖 --------------------
const unsigned int CMD_ShowChannelLotterySetting = 30431;                  // 获取是否有抽奖功能
const unsigned int CMD_GetChannelLotterySetting = 30432;                   // 获取抽奖设置信息
const unsigned int CMD_SetChannelLotteryInfo = 30433;                      // 设置抽奖信息
const unsigned int CMD_JoinChannelLottery = 30434;                         // 点击参与抽奖
const unsigned int CMD_GetChannelLotteryInfoList = 30435;                  // 获取本次排班所有抽奖配置和抽奖结果
const unsigned int CMD_BeginChannelLottery = 30436;                        // 确定并开始抽奖
const unsigned int CMD_SendChannelLotteryPresent = 30437;                  // 确认送礼
const unsigned int CMD_GetChannelLotteryInfo = 30438;                      // 获取抽奖信息
const unsigned int CMD_SearchCustomGifts = 30439;                          // 获取自定义礼物列表

// 客户端自动化push功能 30441-30450
const unsigned int CMD_GetClientAutoGenPushSwitch = 30441;                     //拉取push开关值
const unsigned int CMD_GetClientAutoGenPushDoc    = 30442;                     //拉取push文案
const unsigned int CMD_GetPushFactoryPrivateTemplate = 30443;              //拉取推送的厂商私信模板配置
const unsigned int CMD_GetNewUserPushCntLimit = 30444;                  // 拉取新注册用户push限制数量和限制时间

/*老用户7天打卡  30451-30460  */
const unsigned int CMD_7CardGetEntranceInfo = 30451;                     //入口信息
const unsigned int CMD_7CardReceiveAward = 30452;                     //领奖
const unsigned int CMD_7CardSetDeeplinkSource = 30453;                     //领奖

/*麦位游戏昵称 30461-30470*/
const unsigned int CMD_ChannelGetMicExtInfoList = 30461;                 //麦位扩展信息，例如麦上游戏昵称
const unsigned int CMD_SendChannelGameNick = 30462;                      //房间公屏发送游戏昵称

/*客户端通用配置 30471-30480*/
const unsigned int CMD_GetConfFile = 30471;                        //获取配置文件
const unsigned int CMD_GetConfList = 30472;                        //获取所有需要更新的配置文件列表
const unsigned int CMD_CheckAnnouncementUpdate = 30473;            //检查通告是否有更新

/*邀请进房*/
const unsigned int CMD_SendInvitFromChannelPush = 30480;                        //发邀请进房push
const unsigned int CMD_GetInviteFromChannelList  = 30481;//获取邀请人列表
const unsigned int CMD_ReplyInviteFromChannel  = 30482;//回复邀请

/* 人群匹配查询接口 */
const unsigned int CMD_CheckTagIdMate = 30490;              // 人群匹配查询接口
const unsigned int CMD_CommitAdExposure = 30491;            // 曝光
const unsigned int CMD_CommitAdClick = 30492;               // 点击

const unsigned int CMD_GetChatCardConfig = 30500;            // 获取卡片配置
const unsigned int CMD_GetChatCardStatus = 30501;            // 获取卡片状态
const unsigned int CMD_OpenChatCard = 30502;                 // 开启卡片
const unsigned int CMD_CloseChatCard = 30503;                // 关闭卡片
const unsigned int CMD_GetChatCardList = 30504;              // 获取卡片列表
const unsigned int CMD_SayHi = 30505;                        // 打招呼
const unsigned int CMD_Reply = 30506;                        // 回复
const unsigned int CMD_LikeChatCard = 30507;                 // 点赞
const unsigned int CMD_MarkChatCardMsgRead = 30508;          // 标记消息已读
const unsigned int CMD_GetChatCardMsgUnreadCount = 30509;    // 获取未读数
const unsigned int CMD_GetChatCardMsgList = 30510;           // 获取消息列表
const unsigned int CMD_ReportInterestedChatCardTags = 30511; // 上报感兴趣的标签
const unsigned int CMD_SayHiV2 = 30512;                      // 打招呼（不需要发布卡片）

/* 娱乐厅蒙面pk 30550-30560 begin */
const unsigned int CMD_StartChannelMaskedPK = 30550;    // 开始蒙面PK流程
const unsigned int CMD_CancelChannelMaskedPK = 30551;   // 取消蒙面PK匹配
const unsigned int CMD_GetChannelMaskedPKInfo = 30552;  // 获取房间蒙面PK信息
const unsigned int CMD_GetChannelMaskedPKConf = 30553;  // 获取当前蒙面PK配置信息(仅是符合资格的人能获取到)
const unsigned int CMD_GetChannelMaskedPKAnchorRank = 30554;    // 获取主播本轮战力榜
const unsigned int CMD_GiveUpChannelMaskedPK = 30555;    // 放弃参与本次蒙面PK比赛
const unsigned int CMD_GetChannelQualifiedAnchorList = 30556;    // 获取房间有蒙面pk资格的主播列表
const unsigned int CMD_GetChannelSinglePKAnchorRank = 30557;    // 获取主播本场战力榜
const unsigned int CMD_GetQuickSendPresentConfig = 30558;   // 获取蒙面pk快捷送礼配置

/* 娱乐厅蒙面pk 30550-30560 end */

/* 语音直播厅蒙面pk 30561-30580 begin */
const unsigned int CMD_StartLiveChannelMaskedPK = 30561;    // 开始蒙面PK流程
const unsigned int CMD_CancelLiveChannelMaskedPK = 30562;   // 取消蒙面PK匹配
const unsigned int CMD_GetLiveChannelMaskedPKInfo = 30563;  // 获取房间蒙面PK信息
const unsigned int CMD_GetLiveChannelMaskedPKConf = 30564;  // 获取当前蒙面PK配置信息(仅是符合资格的人能获取到)
const unsigned int CMD_GetLiveChannelMaskedPKAudienceRank = 30565;       // 获取观众火力榜
const unsigned int CMD_GiveUpLiveChannelMaskedPK = 30566;   // 暂不参与本次蒙面PK比赛
const unsigned int CMD_GetLiveQuickSendPresentConfig = 30567;   // 获取蒙面pk快捷送礼配置

const unsigned int CMD_CheckMaskedPkRankEntry = 30570;   // 蒙面pk榜单入口
const unsigned int CMD_MaskedPkGetConsumeTopN = 30571;   // 蒙面pk房间榜单



/* 语音直播厅蒙面pk 30561-30580 end */

/*小纸条 30581～30600*/
const unsigned int CMD_SlipNoteGetSlipNoteConfig = 30581;    /*纸条配置*/
const unsigned int CMD_SlipNotePublishSlipNote = 30582;    /*发布纸条*/
const unsigned int CMD_SlipNoteCloseSlipNote = 30583;    /*关闭纸条*/
const unsigned int CMD_SlipNoteGetSlipNoteStatus = 30584;    /*纸条状态*/
const unsigned int CMD_SlipNotePickSlipNote = 30585;    /*选择一个评论*/
const unsigned int CMD_SlipNoteSetSlipNote = 30586;    /*开关纸条*/
const unsigned int CMD_SlipNoteFetchSlipNote = 30587;    /*捞纸条*/
//const unsigned int CMD_SlipNoteGetIMCountDownRecord = 30588;    /*选择评论后IM的倒计时记录*/
const unsigned int CMD_SlipNoteCommentToSlipNote = 30588;    /*评论小纸条*/
const unsigned int CMD_SlipNotePullSlipNoteCommentList = 30589;    /*拉小纸条评论*/
const unsigned int CMD_ReportSlipNoteStatus = 30590; // 上报纸条miss、有效打开状态
const unsigned int CMD_GetAccountsForSlipNote = 30591; // 获取account，用于轮播

// 一起看 30601-30610
const unsigned int CMD_GetChannelMovieStatus = 30601; // 获取房间播放状态
const unsigned int CMD_PlayChannelMovie = 30602; // 播放视频
const unsigned int CMD_ChangeChannelMovieStatus = 30603; // 暂停/开启
const unsigned int CMD_DragChannelMovieBar = 30604; // 播放条拖动
const unsigned int CMD_GetMovieCategory = 30605;    //获取电影分类
const unsigned int CMD_GetMovieRankList = 30606;    //电影排行榜
const unsigned int CMD_SearchMovie = 30607;         //搜索电影
const unsigned int CMD_GetMovieDetail = 30608;      //电影详情

// 搜索 30621-30630
const unsigned int CMD_UnifiedSearch = 30621; // 统一搜索接口
const unsigned int CMD_UnifiedChannelShowStatus = 30622; // 统一房间内显示功能判断接口
const unsigned int CMD_CommonSearch = 30623; // 统一搜索接口 新
const unsigned int CMD_SearchGuideWords = 30624; // 获取引导词接口
const unsigned int CMD_SearchSuggest = 30625; // 联想词接口

// 新注册标签,只在注册时填写的标签,30631-30633
const unsigned int CMD_GetRegistTagConf = 30631; //标签配置
const unsigned int CMD_SetRegistTag     = 30632;
//新用户标签命令 30634-30640
const unsigned int CMD_GetUserTagV2     = 30634;
const unsigned int CMD_SetUserTagV2     = 30635;
const unsigned int CMD_GetUserAllTagConfigV2 = 30636;

// 获取实名告警规则 30641
const unsigned int CMD_GetRealNameAuthWarningRule = 30641;  // 获取实名告警规则

// 用户充值前置信息查询 30651
const unsigned int CMD_GetUserRechargeFrontInfo = 30651;  // 用户充值前置信息查询


/* 亲密关系 30661-30700 end */

const unsigned int CMD_GetFellowList = 30661;    // 获取挚友名单
const unsigned int CMD_GetFellowCandidateList = 30662;   // 获取可以申请挚友关系的用户名单
const unsigned int CMD_GetFellowCandidateInfo = 30663;   // 获取对应用户的挚友申请相关信息
const unsigned int CMD_SendFellowInvite = 30664;    // 发送挚友申请（邀请函）
const unsigned int CMD_GetFellowInviteInfoById = 30665;    // 获取对应id的邀请函信息
const unsigned int CMD_GetFellowInviteList = 30666;   // 获取待处理的邀请函列表
const unsigned int CMD_HandleFellowInvite = 30667;    // 处理挚友申请
const unsigned int CMD_CheckFellowInvite = 30668;    // 检查邀请栏位限制
const unsigned int CMD_UnlockFellowPosition = 30669;    // 解锁自己的挚友位
const unsigned int CMD_UnboundFellow = 30670; // 发起挚友关系解绑
const unsigned int CMD_CancelUnboundFellow = 30671; // 撤销挚友关系解绑
const unsigned int CMD_CanceFellowInvite = 30672; // 撤销挚友关系申请
const unsigned int CMD_GetFellowPoint = 30673; // 更新IM挚友值
const unsigned int CMD_GetFellowPresentDetail = 30674; // 获取挚友补赠信物信息
const unsigned int CMD_SendFellowPresent = 30675; // 赠送挚友
const unsigned int CMD_GetFellowInfoByUid = 30676; // 获取与对方的挚友信息, 如果其中一方为神秘人则不返回挚友信息
const unsigned int CMD_SendChannelFellowInvite = 30677; // 发送房间内的挚友邀请
const unsigned int CMD_HandleChannelFellowInvite = 30678; // 处理房间内的挚友邀请
const unsigned int CMD_ChannelSendFellowPresent = 30679; // 房间内送挚友礼物
const unsigned int CMD_GetRoomFellowList = 30680; // 获取房间资料卡挚友名单
const unsigned int CMD_GetAllChannelFellowInvite = 30681; // 获取自己在房间内送出/收到的所有挚友邀请
const unsigned int CMD_GetChannelFellowCandidateInfo = 30682; // 获取房间内对应用户的挚友申请相关信息
const unsigned int CMD_GetOnMicFellowList = 30683; // 获取麦上挚友信息
const unsigned int CMD_GetSendInviteList = 30684; // 获取我发出的挚友邀请列表

const unsigned int CMD_GetRareConfig = 30685; // 获取稀缺关系配置
const unsigned int CMD_SetBindRelation = 30686; // 切换绑定的稀缺关系
const unsigned int CMD_GetChannelRareConfig = 30687; // 获取房间稀缺关系配置
const unsigned int CMD_GetRareList = 30688; // 获取稀缺关系列表
const unsigned int CMD_DelRare = 30689; // 删除稀缺关系
const unsigned int CMD_ChangeFellowBindType = 30690; // 换绑关系
const unsigned int CMD_GetFellowHouseInuseList    = 30691;    // 获取用户正在使用挚友小屋列表
const unsigned int CMD_GetFellowHouseEntryInfo   = 30692;    // 获取入口信息
const unsigned int CMD_GetFellowCardInfo   = 30693;    // 获取个人资料卡挚友信息 废弃
const unsigned int CMD_GetExpiringFellowHouseList  = 30694;    // 获取用户即将过期小屋列表


//游戏相关v2:gamelist,小众游戏       30701-30730 end
const unsigned int CMD_GetScanGameListConf = 30701;    //获取扫描gamelist配置列表
const unsigned int CMD_ReportGameScanResult = 30702;   //上报gamelist扫描结果
const unsigned int CMD_GetFastPcShowProcessInfo = 30703; // 获取极速PC展示进程中的游戏/音乐 进程信息
const unsigned int CMD_ReportFastPcShowProcessInfo = 30704; // 极速PC展示进程中的游戏/音乐 上报

// cp战
const unsigned int CMD_CheckChannelCpGameEntry = 30751;    //获取房间cp战模式权限
const unsigned int CMD_SetCpGamePhase = 30752;             //切换阶段
const unsigned int CMD_AddCpGamePhaseEndTime = 30753;      //加时
const unsigned int CMD_GetCurrCpGameInfo = 30754;          //获取房间当前cp战信息
const unsigned int CMD_MvpAutoHoldMic = 30755;             //Mvp用户自动上麦申请
const unsigned int CMD_ChoseCpRareReq = 30756;             //主持选择或确认稀缺关系

// 常用设备相关
const unsigned int CMD_GetFaceAuthCheckResult = 30801;      // 常用设备 - 人脸验证结果
const unsigned int CMD_GetMessageCheckInfo = 30802;         // 常用设备 - 短信验证前置信息
const unsigned int CMD_GetMessageCheckResult = 30803;       // 常用设备 - 短信验证结果


//obsgw, 30860~
const unsigned int CMD_ClaimObsToken = 30861;       // obsgw 获取 token

// 接歌抢唱
const unsigned int CMD_StartSingingGameMatching = 30901; // 开始匹配
const unsigned int CMD_JoinSingingGame = 30902; // 加入游戏（观众加入游戏时使用）
const unsigned int CMD_GetSingingGameCountdown = 30903; // 获取开始倒计时
const unsigned int CMD_StartSingingGame = 30904; // 准备（开始游戏）
const unsigned int CMD_GrabSingingGameMic = 30905; // 抢唱
const unsigned int CMD_AskForSingingHelp = 30906; // 请求帮唱
const unsigned int CMD_AnswerSingingHelp = 30907; // 帮唱
const unsigned int CMD_ReportSingingGameSongScore = 30908; // 上报积分
const unsigned int CMD_GetSingingGameChannelInfo = 30909; // 用户房间状态（客户端重连时使用）
const unsigned int CMD_ExpressSingingGameLike = 30910; // 牛啊
const unsigned int CMD_SingingGameFistBump = 30911; // 碰拳
const unsigned int CMD_AccomplishSingingGameSong = 30912; // 完成演唱
const unsigned int CMD_GetSingAllConfSingingGameSong = 30913; // 获取配置
const unsigned int CMD_GetUserSingImageSingingGameSong = 30914; // 获取用户形象
const unsigned int CMD_SetUserSingImageSingingGameSong = 30915; // 设置用户形象
const unsigned int CMD_GetSingingGameBg = 30916; // 获取默认背景
const unsigned int CMD_SwitchSingingGameType = 30917; // 切换游戏类型
const unsigned int CMD_StartUgcChannelSingingGame = 30918; // 开始游戏（UGC房）
const unsigned int CMD_CancelSingingGamePreparation = 30919; // 取消准备（UGC房）
const unsigned int CMD_GetSingingGame = 30920; // 获取专区列表
const unsigned int CMD_SingingGameVote = 30921; // 投票
const unsigned int CMD_AccomplishLoadingRes = 30922; // 完成资源预加载
const unsigned int CMD_GetSingingGameRankList = 30923; // 获取排行榜
const unsigned int CMD_NotApproveSingResult = 30924; // 客户端不认可接唱结果
const unsigned int CMD_GetSingingGameRecordList = 30925; // 获取通关模式游戏记录
const unsigned int CMD_GetSingingGameUserInfo = 30926; // 获取个人接唱信息

// 房间礼物红包 30951-30970
const unsigned int CMD_GetChannelRedPacketConf = 30951;     // 获取红包配置
const unsigned int CMD_GetChannelRedPacketList = 30952;     // 获取房间当前红包列表
const unsigned int CMD_SendChannelRedPacketConf = 30953;    // 发红包
const unsigned int CMD_ReportChannelRedPacketCnt = 30954;   // 上报红包雨点击成功次数



// 30971 - 31010
const unsigned int CMD_GetChannelKTVSongList          = 30971;   // 获取榜单歌单列表，分页
const unsigned int CMD_GetChannelKTVHistoryList       = 30972;   // 获取已唱历史列表
const unsigned int CMD_GetChannelKTVRecommendList     = 30973;   // 获取推荐歌曲列表
const unsigned int CMD_GetChannelKTVPlayList          = 30974;   // 获取已点列表，分页
const unsigned int CMD_GetChannelKTVGuessLikeSongList = 30975;   // 获取猜你想唱列表
const unsigned int CMD_AddChannelKTVSongToPlayList    = 30976;   // 点歌
const unsigned int CMD_MoveUpChannelKTVSong           = 30977;   // 顶歌
const unsigned int CMD_RemoveChannelKTVSong           = 30978;   // 删歌
const unsigned int CMD_BeginChannelKTVSing            = 30979;   // 伴奏下载完成，开始演唱
const unsigned int CMD_GetChannelKTVInfo              = 30980;   // 获取KTV房的当前状态
const unsigned int CMD_JoinChannelKTVSing             = 30981;   // 加入合唱
const unsigned int CMD_QuitChannelKTVSing             = 30982;   // 退出合唱
const unsigned int CMD_UpdateChannelKTVScore          = 30983;   // 打分心跳
const unsigned int CMD_SwitchChannelKTVBG             = 30984;   // 背景开关
const unsigned int CMD_ChannelKTVHandClap             = 30985;   // 鼓掌
const unsigned int CMD_EndChannelKTVSing              = 30986;   // 结束
const unsigned int CMD_ConfirmKTVHeartBeat            = 30987;   // 确定上跳
const unsigned int CMD_ReportKTVHeartbeat             = 30988;   // 上报ktv心跳
const unsigned int CMD_KickOutKTVMember               = 30989;   // 踢人
const unsigned int CMD_CutChannelKTVSong              = 30990;   // 切歌
const unsigned int CMD_ListChannelKTVSongListType     = 30991;   // 获取歌单类型列表
const unsigned int CMD_GetChannelKTVBG                = 30992;   // 获取背景开关
const unsigned int CMD_GetSpecialEventCopyWriting     = 30993;   // 获取特殊事件的文案
const unsigned int CMD_GetChannelKTVSongListById      = 30994;   // 根据歌单获取歌曲信息
const unsigned int CMD_ChannelKTVBurstLight           = 30995;   // 爆灯
const unsigned int CMD_ChannelKTVHighFive             = 30996;   // 一键击掌
const unsigned int CMD_ChannelKTVGuideClimaxSongList  = 30997;   // 获取引导高潮歌单列表
const unsigned int CMD_ChannelKTVBatchSongClimaxInfos = 30998;   // 批量获取歌曲高潮片段
const unsigned int CMD_ChannelKTVSkipTheIntro         = 30999;   // 跳过前奏
const unsigned int CMD_ChannelKTVFollowTrigger        = 31000;   // 公屏引导回关
const unsigned int CMD_ChannelKTVQuerySong            = 31001;   // 歌曲搜索

// 房间财富榜隐藏 31011
const unsigned int CMD_ChannelHideConsume = 31011;     // 隐藏 / 取消隐藏 财富榜

// 获取用户房间vip统计 31012
const unsigned int CMD_ChannelGetChannelMemberVipStatistics = 31012;     // 获取用户房间vip统计

// 魔法精灵 31150-31200
const unsigned int CMD_GetMagicSpiritConf = 31150;     // 获取魔法精灵配置登录拉取一次，每隔1小时更新一次
const unsigned int CMD_GetMagicSpiritUsable = 31151;    // 获取是否可用魔法精灵 过滤房间UID黑名单
const unsigned int CMD_SendMagicSpirit = 31152;         // 发送魔法精灵
const unsigned int CMD_SendUnpackGift = 31153;          // 开箱礼物
const unsigned int CMD_GetChannelAllUnpackGift = 31154; // 获取房间带开箱礼物信息列表

// 升级礼物 31200-31249
const unsigned int CMD_GetAllLevelupPresentMap = 31200; //获取全部的升级礼物配置（基础+批量）
const unsigned int CMD_GetUserLevelupPresentStatus = 31201; //获取用户全部升级礼物当前版本的状态
const unsigned int CMD_SendLevelupPresent = 31202; //赠送升级礼物，背包或T豆


//游戏卡 以及原来usertaglogic游戏相关的命令迁移过来 31250 -31309
const unsigned int CMD_BatchCreateGameCard = 31250;         // 批量创建游戏卡
const unsigned int CMD_GetGameCardConfByTabIds = 31251; // 新用户承接--根据tab_ids获取游戏卡配置
const unsigned int CMD_GetGameCardConf = 31300;
const unsigned int CMD_ModifyGameCardInChannel = 31301;     //ugc房内修改游戏卡字段
const unsigned int CMD_GetChannelMicroUserGameCard = 31302 ;  //进房获取麦上用户的游戏卡展示信息
const unsigned int CMD_GetAllGameCardConf = 31303 ; // 获取全部游戏卡配置
const unsigned int CMD_GetGameCard = 31304; // 获取游戏卡配置
const unsigned int CMD_SetGameCard = 31305; // 修改游戏卡配置
const unsigned int CMD_CreateGameCard = 31306; // 创建游戏卡
const unsigned int CMD_DeleteGameCard = 31307; // 删除游戏卡
const unsigned int CMD_CreateGameCardInRegister = 31308; // 创建游戏卡
const unsigned int CMD_GetGameCardByTab = 31309;    // 游戏专区，根据用户和玩法获取对应配置


//乐窝相关 31310 -31370
const unsigned int CMD_GetMusicNestReportStayAddTicket = 31310;   // 停留加票

const unsigned int CMD_GetMusicNestCoverAndLiveList        = 31321;  // 获取乐窝封面
const unsigned int CMD_SubMusicNest                        = 31322;  // 关注乐窝
const unsigned int CMD_GetMusicNestHomePage                = 31323;  // 获取乐窝主页
const unsigned int CMD_SubMusicNestActivity                = 31324;  // 关注乐窝活动
const unsigned int CMD_GetMusicNestPerformance             = 31325;  // 获取乐窝房间的节目单
const unsigned int CMD_SetCurrentMusicNestPerformanceStage = 31326;  // 设置乐窝房间的节目环节
const unsigned int CMD_GetPersonalCertification            = 31327;  // 获取个人认证 ( 包括 乐窝主理人 ）
const unsigned int CMD_CloseNestDirectionAct               = 31328;  // 关闭下一场引导
const unsigned int CMD_GetRhythmSwitch                     = 31329;  // 律动现场 开关 管理
const unsigned int CMD_SetRhythmSwitch                     = 31330;  // 律动现场 开关 管理
const unsigned int CMD_GetSpecifiedChannelVisitedSize      = 31331;  // 获取房间灌水值
const unsigned int CMD_UpdateCommonPhotoAlbum              = 31332;  // 更新通用相册
const unsigned int CMD_GetBrandList                        = 31333;  // 获取厂牌列表
const unsigned int CMD_GetBrandInfo                        = 31334;  // 获取厂牌详情
const unsigned int CMD_UpdateBrandInfo                     = 31335;  // 修改厂牌详情
const unsigned int CMD_GetMusicZoneTemplate                = 31336;  // 获取音乐专区基础信息协议
//自定义投票
const unsigned int CMD_UserDefinedVotePKStart =31337;  //开始自定义投票
const unsigned int CMD_GetUserDefinedVotePK   =31338;  //获取全量信息
const unsigned int CMD_UserDefinedVote        =31339;  //用户投票
const unsigned int CMD_UserDefinedVotePKCancel=31340;  //关闭投票
//欢迎弹层
const unsigned int CMD_GetWelcomePop=31341;   //获取欢迎弹层
const unsigned int CMD_UserClickPop =31342;   //用户点击消息触发公屏推送
//乐窝活动汇总
const unsigned int CMD_GetMusicNestLiveInfo=31343;//进房后乐窝活动进行期间的信息获取（和乐窝活动相关的汇总）

/* ai说唱 */
const unsigned int CMD_SetVoiceWatermark        = 31344;   // 设置声音水印
const unsigned int CMD_GetVoiceWatermark        = 31345;   // 获取声音水印
const unsigned int CMD_DelVoiceWatermark        = 31346;   // 删除声音水印
const unsigned int CMD_SendReadVoice            = 31347;   // 读完的音频
const unsigned int CMD_PostAIRapperPost         = 31349;   // 用户发完帖子后将帖子id发给服务端
const unsigned int CMD_GetTabInfo               = 31350;   // 获取Tab信息
const unsigned int CMD_GetLyricInfo             = 31351;   // 根据tab_id获取批量歌词信息
const unsigned int CMD_GetUserAIRapperLevel     = 31352;   // 获取ai说唱用户等级
const unsigned int CMD_Aggregation              = 31353;   // 合成，曝光，分享的聚合接口
const unsigned int CMD_GetQuestionnaire         = 31354;   // 用户体验小问卷
const unsigned int CMD_GetH5UrlWithAICtx        = 31355;   // 分享朋友圈（要服务器记录上传的上下文）
const unsigned int CMD_GetMusicBlockFilter      = 31356;   // 专区改造
const unsigned int CMD_UpgradeUserAIRapperLevel = 31357;   // 提升用户等级
const unsigned int CMD_RhythmGetRecommendRoom   = 31358 ;  // 获取筛选流房间
const unsigned int CMD_RhythmRhythmGetPosts     = 31359 ;  // 获取讨论贴（AB测试）


/* 厂牌扩充命令号 */
const unsigned int CMD_GetBrandChannelBGTopicInfo = 31360; // 是否开启厂牌专属房间背景及话题入口
const unsigned int CMD_GetUserBrandInfo           = 31361; // 获取用户的厂牌信息
/*麦位名称命令号 31362-31364*/
const unsigned int CMD_SwitchMicNameFunc=31362;  //开启或关闭麦位名称功能
const unsigned int CMD_GetMicName=31363;  //获取房间的麦位名称
const unsigned int CMD_SetMicName=31364; //设置麦位名称

/*麦位排序相关命令号*/
const unsigned int CMD_SetMicSort = 31365;          // 设置麦位排序
const unsigned int CMD_GetMicSort = 31366;          // 获取麦位排序
const unsigned int CMD_SwitchMicSort = 31367;          //开启或关闭麦位排序
// 说唱玩法相关 31370 - 31375
const unsigned int CMD_MuseChannelGetInteraction = 31370; // 获取说唱互动信息
const unsigned int CMD_RapGetChannelRespectInfo = 31371; // 获取说唱房间的互动信息
const unsigned int CMD_RapExpressRespect = 31372; // 说唱房间互动
const unsigned int CMD_MuseChannelGetLikeCount = 31373; // 获取说唱房间的点赞数
const unsigned int CMD_MuseChannelAddLike = 31374; // 说唱房间点赞
const unsigned int CMD_MuseChannelGetLikeInfo = 31375; // 获取说唱房间的点赞信息

/* ai说唱-ai外语 相关 31376 - 31385*/
const unsigned int CMD_GetCyberWorldHome = 31376; /* 赛博世界首页 */
const unsigned int CMD_GetBackgroundVideoUrl=31377;/*AI说唱获取视频背景列表*/

// 财富值开关 31400 - 31499
const unsigned int CMD_GetUserRichSwitch = 31400; // 获取签约用户财富值控制开关状态（废弃）
const unsigned int CMD_SetUserRichSwitch = 31401; // 变更签约用户财富值控制开关状态（废弃）
const unsigned int CMD_GetUserNumericLock = 31402; // 获取用户财富值魅力值锁定状态（新）
const unsigned int CMD_SetUserNumericLock = 31403; // 设置用户财富值魅力值锁定状态（新）
const unsigned int CMD_GetUserGloryRank = 31410; // 获取用户荣耀榜单(财富/魅力/活动)
// VIP
const unsigned int CMD_GetUserVipGiftPackageInfo = 31415; // 获取用户vip礼包信息

//兴趣房相关 31500 - 31599
const unsigned int CMD_CreateHobbyChannel = 31500; // 创建兴趣房
const unsigned int CMD_SwitchHobbyChannelSubject = 31501; // 切换房间兴趣玩法
const unsigned int CMD_PublishHobbyChannel = 31502; // 发布兴趣房
const unsigned int CMD_ListHobbyChannel = 31503; // 获取推荐兴趣房
const unsigned int CMD_QuickMatchHobbyChannel = 31504; // 快速加入兴趣房
const unsigned int CMD_KeepHobbyChannelPublish = 31505; // 保持兴趣房发布
const unsigned int CMD_CancelHobbyChannelPublish = 31506; // 取消兴趣房发布
//const unsigned int CMD_GetMusicHomePageView = 31507; // 音乐首页控件下发
const unsigned int CMD_ListMusicHomePageCategory = 31508; // 获取音乐子分类
const unsigned int CMD_ListMusicHomePageBlock = 31509; // 获取音乐子分类选择项
const unsigned int CMD_GetGameHomePageDIYFilter = 31510;  // 获取开黑列表自定义筛选器
const unsigned int CMD_SetGameHomePageDIYFilter = 31511;  // 设置开黑列表自定义筛选器
const unsigned int CMD_GetGameHomePageFilter = 31512;  // 开黑列表首页筛选器
const unsigned int CMD_GetGameTabByCategory = 31513;  // 根据分类id获取游戏玩法标签
const unsigned int CMD_GetMusicHomePageView = 31514; // 音乐首页控件下发
const unsigned int CMD_GetMusicHomePageDialog = 31515; // 音乐首页对话框下发
const unsigned int CMD_GetMusicChannelFilter = 31516; // 音乐首页过滤器
const unsigned int CMD_GetChannelByNewUserTag = 31517; // 新用户承接获取房间接口
const unsigned int CMD_ListHobbyElementConf = 31518; // 获取配置
const unsigned int CMD_GetAllQuickPlayConf = 31519; // 快速开玩页配置
const unsigned int CMD_GetMusicChannelFilterV2 = 31520; // 音乐首页过滤器
const unsigned int CMD_ListHobbyChannelV2 = 31521; // 音乐房列表
const unsigned int CMD_GetMusicHomePageViewV2 = 31522; // 音乐首页view空间配置
const unsigned int CMD_QuickMatchHobbyChannelV2 = 31523; // 快速匹配
const unsigned int CMD_PublishMusicChannel = 31524; // 音乐房发布
const unsigned int CMD_CancelMusicChannelPublish = 31525; // 音乐房取消发布
const unsigned int CMD_GetMusicFilterItemByIds = 31526; // 音乐根据id获取filterItem
const unsigned int CMD_ListMusicChannels = 31527; //获取音乐房间列表
const unsigned int CMD_GetTabPublishHotRcmdReq = 31528; //获取发布热门推荐
const unsigned int CMD_GetResourceConfigByChannelId       = 31529; // 获取挂房听歌资源位配置
const unsigned int CMD_MuseSocialCommunityGetTopicChannelInfo = 31530; /* muse 社群 */
const unsigned int CMD_GetAssociateRevChannels = 31531; /* 退房获取推荐营收房 */
const unsigned int CMD_ListMuseSocialCommunityChannels = 31532; /* 社群房列表 */
const unsigned int CMD_HomePageMixChannelList = 31534; /* TT新版首页混推流 由原<3080>命令号迁移 */
const unsigned int CMD_ListChannelPreferenceKeywords = 31535; /* 发布房间偏好关键词 */


//兴趣内容相关 31560 - 31599
const unsigned int CMD_MuseCommonReport = 31560; // Muse兴趣内容通用上报接口 (用户定位授权上报 客户端触发首页上报)
const unsigned int CMD_GetMuseSwitchHub = 31561; // 兴趣内容相关 开关合集
const unsigned int CMD_SetMuseSwitchHub = 31562; // 兴趣内容相关 开关合集
const unsigned int CMD_GetMusicNestSocialCommunity = 31563; // 获取乐窝配置的社群信息
const unsigned int CMD_CheckJumpSquarePage = 31564; // 判断是否需要跳转到广场页面
const unsigned int CMD_GetMTExperimentStrategy = 31565;    // 获取MT实验策略内容
const unsigned int CMD_GetHomeFollowFloat = 31566; // 获取优先展示的首页跟随进房信息
const unsigned int CMD_FollowChannelHalfScreenUserList = 31567; // 跟随进房半屏页用户列表
const unsigned int CMD_ChannelInviteFriendPreprocessing = 31568; // 音乐房邀请用户进房前置处理
const unsigned int CMD_ChannelSpecialFriendList = 31569; // 音乐房特别好友列表
const unsigned int CMD_GetChannelScreenQuickMsg = 31570; // 新增房间公屏快捷消息
const unsigned int CMD_OneClickInviteAllInfo = 31571; // 一键邀请信息
const unsigned int CMD_OneClickInviteAll = 31572; // 点击“一键邀请”
const unsigned int CMD_CheckIsInOtherUsersBlackList = 31573; //用户是否被某些人拉黑
const unsigned int CMD_GetUserCurrentChannelId = 31574; // 用户当前所在房间
const unsigned int CMD_OneClickInviteAllGetMicId = 31575; // “一键邀请”获取麦位id
const unsigned int CMD_AutoInviteMicPanel = 31576; //自动邀请上麦面板数据请求
const unsigned int CMD_GetUserRelationship = 31577; //获取双方的关注关系
const unsigned int CMD_NonMicUserListOrder = 31578; //获取麦下列表的排序顺序
const unsigned int CMD_GetRoomExitGuideFollowList = 31579; //获取退房关注引导列表
const unsigned int CMD_ShowRoomExitFollowPopup = 31580; //退房关注引导条件判断
const unsigned int CMD_GetRoomIcebreakerPopup = 31581; //房间破冰弹窗信息
const unsigned int CMD_SendPublicMessageDirectly = 31582; //发送不需要审核的公屏消息
const unsigned int CMD_GetHiddenHomePageZoneConfig = 31583; //隐藏首页专区配置
const unsigned int CMD_GetHiddenChannelCategoryTypeConfig = 31584; //隐藏房间分类
const unsigned int CMD_GetMuseRecommendEmojis= 31585; //获取推荐表情包
const unsigned int CMD_GetCustomIcebreakerPopupSwitch = 31586; //获取破冰弹窗开关
const unsigned int CMD_SetCustomIcebreakerPopupSwitch = 31587; //设置破冰弹窗开关
const unsigned int CMD_UpdateCustomIcebreakerPopup = 31588; //更新自定义破冰信息
const unsigned int CMD_GetCustomIcebreakerConfigInfo= 31589; //获取自定义破冰配置信息
const unsigned int CMD_GetPublicScreenShortcutImage = 31590; //获取公屏快捷表情




//用户音乐排行 31600 - 31629
const unsigned int CMD_GetMusicRankUserInfo = 31600; // 获取用户音乐信息
const unsigned int CMD_GetUserMusicRankDialog = 31601; // 获取赛季结算弹窗与称号弹窗
const unsigned int CMD_ListUserMusicRankSingerScore = 31602;//获取歌手列表
const unsigned int CMD_GetUserMusicRankSingerScoreDetail = 31603;//获取歌手歌曲信息
const unsigned int CMD_UserMusicRankDialogConfirm = 31604;//确认显示

// 主播考核 31630-31659
const unsigned int CMD_CheckShowBtn = 31630; //检查是否显示主播考核按钮
const unsigned int CMD_StartRecord = 31631; //开始录制
const unsigned int CMD_StopRecord = 31632; //结束录制


/* rhythm 律动相关 31700-31800 */
/* 推荐流讨论话题 强插 */
const unsigned int CMD_GetForceTopic = 31700; // 推荐流讨论话题 强插

const unsigned int CMD_SetPriorityDisplayCert = 31701; /* 设置优先展示的认证标 */

/* Battle-增加伴奏、开启投票功能 */
const unsigned int CMD_SetBattleStart = 31702; /* 开启battle */
const unsigned int CMD_CancelBattleStart = 31703; /* 关闭battle */
const unsigned int CMD_GetBattleStart = 31704; /* 获取battle */


// t豆消费相关
const unsigned int CMD_GetUserTbeanConsume = 32001; // 获取用户历史T豆消耗的数额接口。【废弃】
const unsigned int CMD_GetUserGroupInfo  = 32002;  // 获取用户分群信息。

const unsigned int CMD_GetUserExamineCert = 32005;  //获取用户考核认证信息
const unsigned int CMD_GetUserComplaintEntry = 32006;  //判断是否展示用户投诉入口
const unsigned int CMD_BatchGetUserExamineCert = 32007; // 批量获取用户考核认证信息

const unsigned int CMD_GetMultiPlayerCenterEntry = 32008;  // 获取娱乐厅从业者中心入口
const unsigned int CMD_GetLiveAnchorTaskEntry = 32009;  // 获取直播间主播任务入口

const unsigned int CMD_GetMultiPlayerHallTaskEntry = 32010;  // 获取多人互动成员大厅任务入口

const unsigned int CMD_GetLiveFansWeekRank = 32011;  // 获取粉丝团周榜

const unsigned int CMD_CheckCanSeeRoiUser = 32012;  // 判断是否可见ROI用户
const unsigned int CMD_GetUserRoiInfo = 32013;  // 获取用户roi信息
const unsigned int CMD_ConfirmRoiHighPotentail = 32014;  // 确认高潜付费用户弹窗显示
const unsigned int CMD_GetRevenueAdPosInfo = 32015;  //获取用户当前营收广告位信息
const unsigned int CMD_GetPersonalizedAdSwitch = 32016;  //获取个性化推荐广告开关状态
const unsigned int CMD_SetPersonalizedAdSwitch = 32017;  //设置个性化推荐广告开关状态
const unsigned int CMD_ProcGuildManageRoleInvite = 32018;  //处理会长经营后台角色邀请
const unsigned int CMD_GetUserQualityInfo = 32019;  // 获取优质用户信息
const unsigned int CMD_ConfirmQualityHighPop = 32020;  // 确认壕用户弹窗显示



// 黑暗礼物奖励 32050~32070
const unsigned int CMD_GetUserDarkGiftBonusRecord = 32050;  // 获取用户黑暗礼物奖励记录。
const unsigned int CMD_CheckIfExistBonusRecord = 32051;  // 检查近两个月有无获奖记录（用于判断是否展示“奖励记录”tab）。

// 新挂房听歌 32071~32100

const unsigned int CMD_CheckListeningChannelTab        = 32076; // 检查挂房听歌房间玩法
const unsigned int CMD_ListeningCheckInV3              = 32077; // 打卡V3
const unsigned int CMD_ListeningCheckInSharedV3        = 32078; // 打卡分享v3
const unsigned int CMD_ListeningDropCardV3             = 32079; // 获取掉落卡片
const unsigned int CMD_Constellation                   = 32080; // 获取星座
const unsigned int CMD_GetChannelListeningSimpleInfo   = 32081; // 挂房听歌信息
const unsigned int CMD_LikeSong                        = 32082; // 喜欢某首歌
const unsigned int CMD_GetBeLikedSongList              = 32083; // 获取被喜欢歌曲列表
const unsigned int CMD_GetLikedSongList                = 32084; // 获取喜欢的歌曲列表
const unsigned int CMD_GetUserFlowerList               = 32085; // 获取花花列表
const unsigned int CMD_GetFlowerDetail                 = 32086; // 获取花花历史详情
const unsigned int CMD_SetListeningCheckInTopic        = 32087; // 设置打卡话题
const unsigned int CMD_ListeningCheckIn                = 32088; // 挂房听歌打卡
const unsigned int CMD_GetListeningUserCheckInInfoList = 32089; // 获取打卡榜
const unsigned int CMD_GetRcmdSongMenu                 = 32090; // 获取推荐歌单
const unsigned int CMD_GetSongByMenuId                 = 32091; // 获取歌曲
const unsigned int CMD_GetTodayInRoomDetail            = 32092; // 获取当天在房详情
const unsigned int CMD_GetAllMoodCfg                   = 32093; // 获取状态配置
const unsigned int CMD_SetListeningUserMood            = 32094; // 设置状态
const unsigned int CMD_GetAllListeningOnMicUserMood    = 32095; // 获取麦上用户状态
const unsigned int CMD_ListeningCheckInV2              = 32096; // 打卡
const unsigned int CMD_ListeningCheckInShared          = 32097; // 打卡分享
const unsigned int CMD_ListeningLikeSongWYY            = 32098; //  网易云 爱心 收藏


// 直播房投票pk 32100~32120
const unsigned int CMD_SearchPkCandidate  = 32100;   // 搜索房间内的候选人
const unsigned int CMD_ChannelLiveVotePKVote 	= 32101; //投票
const unsigned int CMD_ChannelLiveVotePKStart 	= 32102; //开始PK
const unsigned int CMD_ChannelLiveVotePKCancel 	= 32103; //取消PK
const unsigned int CMD_ChannelLiveVotePKGetInfo = 32104; //拿消息
const unsigned int CMD_GetVotePkStatus = 32105; //检查入口状态

// 航海寻宝概率玩法 32121~32140
const unsigned int CMD_OnePieceEntryAndNotify = 32121;      // 是否可见入口，以及浮层显示
const unsigned int CMD_OnePieceLotteryDraw = 32122;         // 抽奖
const unsigned int CMD_OnePieceBuyChance = 32123;           // 购买抽奖机会
const unsigned int CMD_OnePieceGetOnePieceInfo = 32124;     // 获取航海信息
const unsigned int CMD_OnePieceGetWinningRecords = 32125;   // 获取用户中奖纪录
const unsigned int CMD_OnePieceGetRecentWinningRecords = 32126;   // 获取全平台最新的中奖纪录

// 幸运时刻(星际巡航)概率玩法 32141~32160
const unsigned int CMD_StarTrekEntryAndNotify = 32141;      // 星际巡航入口 、浮层信息
const unsigned int CMD_StarTrekGetStatTrekInfo = 32142;     // 星际巡航玩法信息
const unsigned int CMD_StarTrekGetSupplyConf = 32143;       // 获取用户补给配置
const unsigned int CMD_StarTrekDoInvest = 32144;            // 去探险
const unsigned int CMD_StarTrekGetMyTrekRecord = 32145;     // 获取用户巡航记录
const unsigned int CMD_StarTrekGetAllTrekHistory = 32146;   // 往期回顾
const unsigned int CMD_StarTrekGetSupplyValueChange = 32147; // 获取全站、用户补给值变化

//客户端union token 相关接口 33000~33009
const unsigned int CMD_RefreshUnionToken  = 33000;  //获取新union_token

// 客户端grpc 通信配置刷新接口, 预留33010 ~ 33020
const unsigned int CMD_RefreshTransportConfig  = 33010;  //获取新的grpc通信配置

// Pia戏相关 33021-33080
const unsigned int CMD_GetChannelPiaStatus       = 33021; // 获取房间Pia戏开启状态
const unsigned int CMD_SetPiaSwitch              = 33022; // 切换成Pia戏模式
const unsigned int CMD_GetDrama                  = 33023; // 获取剧本
const unsigned int CMD_GetSearchOptionGroup      = 33024; // 获取剧本搜索标签
const unsigned int CMD_GetCurrentPiaInfo         = 33026; // 获取当前Pia戏信息
const unsigned int CMD_SetPiaPhase               = 33027; // 设置Pia戏阶段
const unsigned int CMD_SetPiaProgress            = 33028; // 设置Pia戏进度
const unsigned int CMD_GetPlayingChannel         = 33029; // 获取正在玩的房间
const unsigned int CMD_GetQualityDramaList       = 33030; // 获取优质剧场列表
const unsigned int CMD_PracticeDramaList         = 33031; // 获取排练列表
const unsigned int CMD_SelectDrama               = 33032; // 选本
const unsigned int CMD_SetBgmInfo                = 33033; // 设置Bgm信息
const unsigned int CMD_GetBgmInfo                = 33034; // 获取Bgm信息
const unsigned int CMD_OrderDrama                = 33036; // 点本
const unsigned int CMD_GetOrderDramaList         = 33037; // 获取已点列表
const unsigned int CMD_DeleteOrderDrama          = 33038; // 删除已点记录
const unsigned int CMD_PiaSelectRole             = 33039; // 选择角色
const unsigned int CMD_PiaCancelSelectRole       = 33040; // 角色取消选择
const unsigned int CMD_SelectDramaV2             = 33041; // 选本
const unsigned int CMD_PiaOperateDrama           = 33042; // 走本操作
const unsigned int CMD_PiaGetDramaStatus         = 33043; // 获取当前房间走本详情
const unsigned int CMD_PiaOperateBgm             = 33044; // BGM操作
const unsigned int CMD_GetSearchOptionGroupV2    = 33045; // 获取筛选标签组
const unsigned int CMD_GetDramaList              = 33046; // 分页获取剧本库列表
const unsigned int CMD_GetDramaDetailById        = 33047; // 获取剧本详情
const unsigned int CMD_PiaGetDramaCopyId         = 33048; // 获取剧本副本ID
const unsigned int CMD_PiaCreateDramaCopy        = 33049; // 生成剧本副本
const unsigned int CMD_PiaOperateBgmVol          = 33050; // BGM音量操作
const unsigned int CMD_PiaDoDramaCollect         = 33051; // 收藏/取消收藏剧本
const unsigned int CMD_PiaGetUserDramaCollection = 33052; // 获取用户收藏剧本列表
const unsigned int CMD_GetPlayingChannelV2       = 33053; // 获取正在玩房间v2
const unsigned int CMD_PiaChangePlayType         = 33054; // 切换走本方式
const unsigned int CMD_GetMyDramaPlayingRecord   = 33055; // 获取我的参演记录列表
const unsigned int CMD_PiaBatchDeleteMyPlayingRecord = 33056; // 批量删除我的参演记录
const unsigned int CMD_PiaGetRankingList         = 33057; // 获取排行榜剧本列表
const unsigned int CMD_PiaGetMyPlayingRecordIdList   = 33058; // 根据筛选条件获取参演记录id列表
const unsigned int CMD_GetMyDramaCopyList   = 33059; // 我的副本库列表
const unsigned int CMD_PiaCopyDramaList     = 33060; // 具体某个副本的副本库列表
const unsigned int CMD_PiaConfirmCoverCopy = 33061; // 确认保存副本
const unsigned int CMD_SetDramaCopyStatus   = 33062; // 设置副本状态
const unsigned int CMD_DeleteDramaCopy     = 33063; // 删除副本
const unsigned int CMD_PiaCreateDramaCopyV2 = 33064; // 生成副本V2
const unsigned int CMD_PiaPerformDrama = 33065; // 选择新本演绎
const unsigned int CMD_PiaSendDialogueIndex = 33066; // 发送台词定位
const unsigned int CMD_PiaFollowMic = 33067; // 跟随麦位
const unsigned int CMD_PiaReportDialogueIndex = 33068; // 上报走本段落
const unsigned int CMD_PiaGetPreviousDialogueIndex = 33069; // 获取上个用户的段落记录
const unsigned int CMD_PiaUnFollowMic = 33070; // 取消跟随
const unsigned int CMD_PiaGetMyFollowInfo = 33071; // 获取用户pia戏相关信息
const unsigned int CMD_PiaGetFollowedStatusOfMicList = 33072; // 获取pia戏房间各个麦位的跟随状态

// 赛事中心入口 33081-33085
const unsigned int CMD_GetCompetitionEntrance = 33081; // 获取赛事入口
const unsigned int CMD_GetGameTmpChannelCfg = 33082; // 获取赛事临时房配置
const unsigned int CMD_GetChannelCompetitionEntranceList = 33083; // 获取房间内生效中的赛事入口列表
const unsigned int CMD_CheckShowFloatingComponent = 33084; // 查询是否展示悬浮组件/弹窗

// 盲盒任务 33086-33099
const unsigned int CMD_GetMysteryBoxWin = 33086; // 获取盲盒弹窗
const unsigned int CMD_GetMysteryBoxTask = 33087; // 获取盲盒任务详情

//房间玩法基础ChannelScheme相关  33100 ~ 33119
const unsigned int CMD_GetChannelSchemeInfo = 33100;  //获取当前房间玩法信息
const unsigned int CMD_CheckExitAfterSwitchScheme = 33101; //切换玩法后检查是否需要退房
const unsigned int CMD_GetPgcChannelSchemeList = 33102;    //获取公会公开房的玩法列表
const unsigned int CMD_SwitchChannelScheme = 33103;     //切换公会公开房玩法

//PGC房间玩法tt-rev-channel-mode-mgr相关  33210 ~ 33220
const unsigned int CMD_PGC_GetChannelMode = 33210;  //获取当前房间玩法信息
const unsigned int CMD_PGC_SetChannelMode = 33211; //设置当前房间玩法

// 乐队玩法
const unsigned int CMD_GetConcertSongOpt            = 33300; // 获取歌曲选项
const unsigned int CMD_SearchConcertSong            = 33301; // 搜索歌曲
const unsigned int CMD_GetConcertSongList           = 33302; // 获取歌曲
const unsigned int CMD_GetAllConcertResource        = 33303; // 获取演唱资源
const unsigned int CMD_StartConcertSinging          = 33304; // 点歌
const unsigned int CMD_GetMusicBook                 = 33305; // 获取乐谱
const unsigned int CMD_CompleteDownloadingMusicBook = 33306; // 完成乐谱下载
const unsigned int CMD_StopConcertSinging           = 33307; // 停止演唱
const unsigned int CMD_GetConcertInfo               = 33308; // 获取演唱信息
const unsigned int CMD_UpdateBackingTrackStatus     = 33309; // 更新伴奏开关状态
const unsigned int CMD_ReportConcertSuccCount       = 33310; // 上报成功数量
const unsigned int CMD_JoinConcert                  = 33311; // 中途加入
const unsigned int CMD_GetAllConcertImage           = 33312; // 获取形象配置
const unsigned int CMD_SetConcertUserImage          = 33313; // 设置形象
const unsigned int CMD_GetAllConcertOnMicUserImage  = 33314; // 获取麦上用户形象
const unsigned int CMD_GetConcertSongTaskProgressList  = 33315; // 获取乐响图鉴
const unsigned int CMD_GetConcertSongTaskProgressDetails = 33316; // 获取歌曲任务进度详情
const unsigned int CMD_GetConcertSongById = 33317;           // 通过歌曲id获取歌曲
const unsigned int CMD_GetRecentUploadedSong = 33318;        // 获取最新上架的歌曲

// 碎碎念
const unsigned int CMD_GetMusePostList            = 33320; // 获取留言列表
const unsigned int CMD_GetMuseCommentList         = 33321; // 获取评论列表
const unsigned int CMD_GetMuseContentLikeList     = 33322; // 获取点赞列表
const unsigned int CMD_GetMusePostDetail          = 33323; // 获取留言详情
const unsigned int CMD_MuseAddLike                = 33324; // 点赞
const unsigned int CMD_MuseResetLike              = 33325; // 取消点赞
const unsigned int CMD_MusePublishPost            = 33326; // 发布留言
const unsigned int CMD_MusePublishComment         = 33327; // 发布评论
const unsigned int CMD_MuseHaveNewPost            = 33328; // 是否有新留言或互动消息（小红点）
const unsigned int CMD_MarkMuseInteractiveMsgRead = 33329; // 标记已读
const unsigned int CMD_GetMuseInteractiveMsg      = 33330; // 获取互动消息
const unsigned int CMD_DeleteMuseContent          = 33331; // 删除留言或评论
const unsigned int CMD_CheckMusePublishRight      = 33332; // 检查发布权限
const unsigned int CMD_GetMuseSubCommentList      = 33333; // 获取二级评论列表
const unsigned int CMD_GetMuseUserRecord          = 33334; // 获取个人发布记录
const unsigned int CMD_GetMusePostAtFriends       = 33335; // 搜索有红花的好友

// 新接口 muse-play-sing 33501~33600
const unsigned int CMD_CheckPlaySingStatus = 33501; // 检查歌曲状态，返回默认的音频信息
const unsigned int CMD_GetPlaySingList = 33502; // 获取歌曲列表
const unsigned int CMD_GetPlaySingDetail = 33503; // 获取歌曲详细信息

//迷境首页 36000-36049
const unsigned int CMD_ListScenarioInfo      = 36000; // 获取列表接口
const unsigned int CMD_GetScenarioInfo       = 36001; // 详情页面
const unsigned int CMD_GetLoginTask          = 36002; // 获取登录任务
const unsigned int CMD_ReceiveLoginTaskAward = 36003; // 获取登录任务奖励
const unsigned int CMD_GetBalanceInfo        = 36004; // 获取首页余额半屏信息
const unsigned int CMD_CheckScenarioRoom     = 36005; // 检查能否进别人的密逃房
const unsigned int CMD_Invite2MyRoom         = 36006; // 邀请别人来我的密逃房
const unsigned int CMD_GetInvite2MyRoomResult= 36007; // 获取邀请别人来我的密逃房的结果
const unsigned int CMD_ListRecommendedScenarioSimpleInfo= 36008; // 获取推荐剧本列表缩略信息
const unsigned int CMD_GetRecommendedScenarioDetailInfo= 36009; // 获取剧本推荐详细信息
const unsigned int CMD_GetRoomShareLinkByTabId= 36010; // 获取剧本分享链接
const unsigned int CMD_MysteryPlaceChannelList= 36011; // 谜境房间列表
const unsigned int CMD_GetCommentPageParams = 36012; // 获取评论页参数
const unsigned int CMD_CommentToScenario = 36013; // 评论剧本/玩伴
const unsigned int CMD_PiecingGroupButtonDisplay = 36014; // 显示拼场按钮
const unsigned int CMD_PiecingGroupList = 36015;  //拼场列表
const unsigned int CMD_InvitePiecingGroup = 36016;  //邀请拼场
const unsigned int CMD_GetPingChangNotify = 36018; // 获取拼场通知配置
const unsigned int CMD_SetPingChangNotify = 36019; // 设置拼场通知开关
const unsigned int CMD_GetNewScenarioTip = 36020; // 获取未读提示列表
const unsigned int CMD_MarkNewScenarioTipRead = 36021; // 标记提示已读
const unsigned int CMD_MysteryPlaceClientConfig = 36022; // 谜境客户端配置
const unsigned int CMD_GetPlayedScenarioRecordList = 36023; // 获取剧本记录
const unsigned int CMD_GetScenarioChapterSummary = 36024; // 获取剧本章节概要
const unsigned int CMD_SetPlayedScenarioRecordVisibility = 36025; // 设置记录可见
const unsigned int CMD_GetPlayedScenarioRecordDetail = 36026; // 获取记录详情
const unsigned int CMD_GetRcmdMiJingTab = 36027; // 获取推荐剧本
const unsigned int CMD_GetHomepageTrifleInfo = 36028; // 获取首页谜之小事
const unsigned int CMD_GetTrifleRecordList = 36029; // 获取全部谜之小事
const unsigned int CMD_MarkTrifleRead = 36030; // 标记小事状态已读
const unsigned int CMD_ListRecommendedScenarioDetailInfo = 36031; // 获取推荐剧本列表详细信息
const unsigned int CMD_GetHomeTeam = 36032; // 获取首页小分队
const unsigned int CMD_ReportChannelInviteTeam = 36039; // 上报邀人组队的邀请状态
const unsigned int CMD_HomePageBigTofu = 36040; // 获取首页大豆腐块
const unsigned int CMD_HomePageRightTofu = 36041; //获取首页右侧豆腐块
const unsigned int CMD_IsUserHasScenarioFreeCoupons = 36042; //用户是否有剧本限免券
const unsigned int CMD_GetChannelScenarioInfoReq = 36043; //获取房间剧本信息 (废弃的)
const unsigned int CMD_GetChannelScenarioInfo = 36044; //获取房间剧本信息
const unsigned int CMD_GetActiveTrifleVisualStyle = 36045;  // 获取当前生效的谜之小事视觉风格配置列表
const unsigned int CMD_GetChannelInviteTeam = 36046; // 获取房间邀人组队列表

const unsigned int CMD_ScenarioMatch = 36047;  // 根据剧本/章节/玩本模式做匹配
const unsigned int CMD_ScenarioMatchWithTarget = 36034;  // 根据剧本/章节/玩本模式做匹配
const unsigned int CMD_ScenarioMatchShakeIt = 36035; // 匹配成功互动，晃一下

const unsigned int CMD_ScenarioBind = 36048;  // 根据ScenarioMatch匹配到的结果做绑定
const unsigned int CMD_ScenarioRecommendUserList = 36049;  // 根据用户申请匹配的信息获取推荐用户列表

//通用邀请 36050-36059
const unsigned int CMD_ReportCommonInvitationResult = 36050; // 通用邀请结果上报

//谜境房间 36060-36069
const unsigned int CMD_GetCategoryFilter = 36060; //谜境首页，按分类获取筛选器
const unsigned int CMD_GetTabFilter      = 36061; //谜境首页，按玩法获取筛选器
const unsigned int CMD_MijingChannelPlaymateList = 36062; // 首页拼人
const unsigned int CMD_GetMijingRandomUsers = 36063; // 谜境获取随机用户
const unsigned int CMD_GetCommonExpressionList = 36064; // 获取常用语列表
const unsigned int CMD_ReportScenarioSubscribeStatus = 36065; // 设置预约状态
const unsigned int CMD_CheckChannelMatchLimitation = 36066; // 验证房间是否被匹配限制不允许进入
const unsigned int CMD_GetPlayedScenarioRecordListByID = 36067; // 根据ID获取剧本记录

const unsigned int CMD_CheckRisk = 36068; // 接风控平台

//谜境优惠券 36070-36079
const unsigned int CMD_GetCouponCount = 36070; // 获取优惠券数量
const unsigned int CMD_GetCouponList = 36071;  // 获取优惠券列表
const unsigned int CMD_GetGameTicketOrderQueryOptions = 36072;  // 获取密室券订单查询选项
const unsigned int CMD_GetGameTicketOrderList = 36073;  // 获取密室券订单列表
const unsigned int CMD_IsHasScenarioFreeCoupons = 36074;  // 是否有免费券
const unsigned int CMD_GetMiJingPayChannel = 36075;  // 获取支付渠道
const unsigned int CMD_MiJingPay = 36076;  // 下单
const unsigned int CMD_GetMiJingPayResult = 36077;  // 查询支付结果

//谜境新版首页推荐 36080-36099
const unsigned int CMD_GetRecommendedScenarioForHomePage = 36080;  //获取首页推荐本
const unsigned int CMD_GetMijingScenarioCommentList = 36081;  //获取剧本评价
const unsigned int CMD_GetMijingCommentList  = 36082;  //获取总评价
const unsigned int CMD_GetMijingPartnerCardList  = 36083;  //获取推荐搭子卡片
const unsigned int CMD_BatchGetMijingPartnerCardInfoByCardId  = 36084;  //根据卡片ID获取搭子卡片详情
const unsigned int CMD_BatchGetMijingPartnerCardInfoByUid  = 36085;  //根据uid获取搭子卡片详情
const unsigned int CMD_UpdateMijingPartnerCardInfo  = 36086;  //更新搭子卡片信息
const unsigned int CMD_MijingExposurePartnerCard  = 36087;  //搭子行为曝光
const unsigned int CMD_MijingIncrExposurePartnerCard  = 36088;  //擦亮搭子卡片
const unsigned int CMD_MijingAccostPartnerCard  = 36089;  //搭一下卡片
const unsigned int CMD_GetMijingScenarioGameIdList  = 36090;  //获取剧本对应的游戏id
const unsigned int CMD_CheckMijingGenerateAvatar  = 36091;  //检查用户上传的形象原图
const unsigned int CMD_SubmitMijingGenerateAvatar  = 36092;  //请求生成搭子卡片形象照片
const unsigned int CMD_StopMijingGenerateAvatar  = 36093;  //停止生成搭子卡片形象照片
const unsigned int CMD_GetMijingGenerateAvatar  = 36094;  //获取用户生成搭子形象照片的进度

// 点唱厅(冠歌) 36100-36150
const unsigned int CMD_HasSongMenuPerm = 36100; // 是否有歌单权限
const unsigned int CMD_GetSongMenu     = 36101; // 获取歌单列表
const unsigned int CMD_CreateSongMenu  = 36102; // 新建歌单
const unsigned int CMD_UpdateSongMenu  = 36103; // 更新歌单
const unsigned int CMD_EditSongMenu    = 36104; // 歌单管理
const unsigned int CMD_AddSong         = 36106; // 新增歌曲
const unsigned int CMD_GetSongList     = 36107; // 获取歌曲列表
const unsigned int CMD_EditSongList    = 36108; // 歌曲管理

// 谜境广告 36151 - 36199
const unsigned int CMD_CheckThirdPartyAds = 36151; // 根据广告场景检查用户是否有看广告的权限

// 专属定制礼物 36200 - 36250
const unsigned int CMD_GetCustomizedPresentList = 36200; // 获取专属定制礼物配置列表
const unsigned int CMD_GetCustomizedPresentDetail = 36201; // 获取专属定制礼物配置详情
const unsigned int CMD_ReportCustomOptionChoose = 36202; // 上报用户选项的选择情况

// 谜境通用服务 36251 - 36299
const unsigned int CMD_SendEscapeChannelGameStatusMsg = 36251; // 请求发送密逃房间游戏中 游戏状态公屏消息


// PGC PK 36300 - 36350
const unsigned int CMD_GetPgcChannelPKEntry = 36300; // 获取PGC PK入口
const unsigned int CMD_GetPgcChannelPKChannelList = 36301; // 获取PGC PK房间列表
const unsigned int CMD_SetPgcChannelPKSwitch = 36302; // 设置pk开关
const unsigned int CMD_StartPgcChannelPK = 36303; // 发起PK
const unsigned int CMD_AcceptPgcChannelPK = 36304; // 处理收到PK邀请
const unsigned int CMD_GetPgcChannelPKInfo = 36305; // 获取房间PK信息
const unsigned int CMD_PgcChannelPKReportClientIDChange = 36306; // PGC PK 上报音频流
const unsigned int CMD_SetPgcChannelPKOpponentMicFlag = 36307; // PGC PK 设置是否能听到对面语音
const unsigned int CMD_GetPgcChannelPKSendGiftScore = 36308; // PGC PK获取送礼值
const unsigned int CMD_GetPgcChannelPKAudienceRank = 36309; // PGC PK 获取火力榜
const unsigned int CMD_ChoseInteraction = 36310; // PGC PK 选择互动玩家
const unsigned int CMD_SetPgcChannelPKEnd = 36311; // PGC PK 提前结束


//PGC房小游戏  36351 ~ 36400
const unsigned int CMD_GetGameList = 36351;  //获取PGC房小游戏列表
const unsigned int CMD_SetGamePhase = 36352; //设置PGC房小游戏开始或者结束
const unsigned int CMD_SetNextBombUser = 36353; //甩雷
const unsigned int CMD_GetChannelGameInfo = 36354; //进房获取PGC房游戏信息
const unsigned int CMD_DigitalBombEnroll = 36355; //数字炸弹报名
const unsigned int CMD_DigitalBombSelectGameUser = 36356; //数字炸弹选择参与用户
const unsigned int CMD_DigitalBombSelectNumber = 36357; //数字炸弹选择数字

const unsigned int CMD_AdventureEnroll = 36358; // 大冒险报名
const unsigned int CMD_AdventureSelectGameUser = 36359; // 大冒险选择参与用户
const unsigned int CMD_AdventureRandomSteps = 36360; // 摇步数
const unsigned int CMD_AdventureControlNext = 36361; // 控制下一步操作


// 房间进房控制 36401 - 36410
const unsigned int CMD_ChannelSetEnterControlType = 36401;  // 设置进房控制状态
const unsigned int CMD_ChannelGetEnterControlType = 36402;  // 获取进房控制状态


// 活动宝箱 36411 - 36430
const unsigned int CMD_GetTreasureBoxSource = 36411; // 获取宝箱资源配置
const unsigned int CMD_GetTreasureBoxEntry = 36412; // 获取宝箱入口
const unsigned int CMD_GetMyTreasureBox = 36413; // 获取我的宝箱
const unsigned int CMD_GetTreasureBoxPendant = 36414; // 获取宝箱挂件
const unsigned int CMD_GetTreasureBoxDetail = 36415; // 获取宝箱详情
const unsigned int CMD_GetTreasureBoxConditionStatus = 36416; // 获取用户宝箱领取条件状态
const unsigned int CMD_DrawTreasureBoxGift = 36417; // 领取宝箱奖励

// 谜境首页热度榜
const unsigned int CMD_GetHotRankScenarioList = 36431;
// 谜境剧本详情页是否可评价
const unsigned int CMD_MijingShowCommentEntrance = 36432;

// 开黑发图 36500 - 36520
const unsigned int CMD_GetGameScreenshotSummary = 36500; // 房间首页获取开黑截图摘要
const unsigned int CMD_GetGameScreenshotSetting = 36501; // 房主获取开黑截图设置
const unsigned int CMD_UpdateGameScreenshotSetting = 36502; // 更新开黑截图设置
const unsigned int CMD_GetGameScreenshotHint = 36503; // 获取一键发送截图提示
const unsigned int CMD_GetDiyGameScreenshot = 36504; // 获取自定义截图
const unsigned int CMD_ApplySetDiyGameScreenshot = 36505; // 申请设置自定义截图
const unsigned int CMD_SetDiyGameScreenshotFinish = 36506; // 设置自定义截图完成
const unsigned int CMD_GetGameScreenshotGuide = 36507; // 获取开黑发图引导

// 房间抽奖 拓展 36521 - 36530
const unsigned int CMD_ReportEnterShareLotteryChannel = 36521;  // 上报通过分享链接进抽奖房间
const unsigned int CMD_GetEnterLotteryChannelCnt = 36522;       // 获取抽奖期间进房人数
const unsigned int CMD_GetConditionMissionProgress = 36523;     // 获取用户抽奖任务进度

// 天配房间内玩法 36531 - 36550
const unsigned int CMD_PerfectCoupleGetGameInfo = 36531;    // 获取当前游戏信息
const unsigned int CMD_PerfectCoupleSetGamePhase = 36532;   // 设置游戏阶段
const unsigned int CMD_PerfectCoupleHoldMic = 36533;        // 申请上麦
const unsigned int CMD_PerfectCoupleBlowLight = 36534;      // 为指定玩家爆灯
const unsigned int CMD_PerfectCoupleChooseTheOne = 36535;   // 选择心动对象
const unsigned int CMD_PerfectCoupleGetCoupleClues = 36536; // 查询已有线索
const unsigned int CMD_PerfectCoupleGetMyQuestionnaire = 36537; // 查询我的问卷
const unsigned int CMD_PerfectCoupleGetMyCluesProp = 36538;     // 查询我的道具
const unsigned int CMD_PerfectCoupleUseCluesProp = 36539;       // 使用道具获得线索
const unsigned int CMD_PerfectCouplePublishClues = 36540;       // 主持人发放线索

// 天配匹配阶段  36551-36570
const unsigned int CMD_GetPerfectMatchEntry = 36551;                // 获取天配匹配入口请求
const unsigned int CMD_EnrollPerfectMatch = 36552;                  // 匹配报名请求
const unsigned int CMD_GetPerfectMatchQuestions = 36553;            // 获取答案
const unsigned int CMD_SendPerfectMatchHeartbeat = 36554;           // 匹配心跳
const unsigned int CMD_SendPerfectMatchAnswer = 36555;              // 发送答案
const unsigned int CMD_CancelPerfectMatch = 36556;                  // 取消匹配

// 猫猫餐厅概率玩法 36571~36590
const unsigned int CMD_CatCanteenEntryAndNotify = 36571;      // 是否可见入口，以及浮层显示
const unsigned int CMD_CatCanteenLotteryDraw = 36572;         // 抽奖(经营)
const unsigned int CMD_CatCanteenBuyChance = 36573;           // 购买抽奖机会（鱼干）
const unsigned int CMD_CatCanteenGetGameInfo = 36574;         // 获取游戏信息
const unsigned int CMD_CatCanteenGetWinningRecords = 36575;   // 获取用户中奖纪录
const unsigned int CMD_CatCanteenGetRecentWinningRecords = 36576;   // 获取全平台最新的中奖纪录
const unsigned int CMD_CatCanteenGetUserUserPlayFile = 36577;       // 获取用户游戏存档信息
const unsigned int CMD_CatCanteenGetUserCatPropList = 36578;       // 获取用户道具列表
const unsigned int CMD_CatCanteenGetUserExpireCatPropNotify = 36579;       // 获取用户即将过期道具提醒
const unsigned int CMD_CatCanteenGetCatCanteenResource = 36580;       // 获取资源配置

// 营收外部游戏开放通用logic 36591~36610
const unsigned int CMD_RevenueExtGameMountExtGame = 36591;   // 启动外部游戏挂载
const unsigned int CMD_RevenueExtGameUnmountExtGame = 36592; // 停止外部游戏挂载
const unsigned int CMD_RevenueExtGameGetExtGameCfgList = 36593; // 获取游戏配置列表（仅返回有权限的游戏）
const unsigned int CMD_RevenueExtGameReportUserWantPlay = 36594; // 用户点击“我想玩”上报
const unsigned int CMD_RevenueExtGameGetUserExtGameInfo = 36595; // 获取用户游戏信息
const unsigned int CMD_RevenueExtGameGetChannelExtGameAccess = 36596; // 获取直播间游戏权限入口
const unsigned int CMD_RevenueExtGameGetExtGameScoreRank = 36597;   // 获取游戏积分榜单
const unsigned int CMD_RevenueExtGameGetExtGameRankNameplate = 36598; // 获取游戏榜单外显标识


// 荣耀世界通用logic 36611~36630
const unsigned int CMD_GetGloryEnterInfo = 36611; // 获取荣耀世界入口信息
const unsigned int CMD_ReceiveReward = 36612; // 领取任务奖励
const unsigned int CMD_GetFloatingLayerInfo = 36613; // 获取荣耀世界悬浮层信息


// 周卡logic 36631~36650
const unsigned int CMD_GetPresentWeekCardEnterInfo = 36631; // 获取礼物周卡入口信息
const unsigned int CMD_GetPresentWeekCardDetails = 36632; // 获取礼物周卡详情
const unsigned int CMD_BuyPresentWeekCard = 36633; // 购买礼物周卡
const unsigned int CMD_ReceivePresentWeekCardReward = 36634; // 领取礼物周卡奖励

// 摘星列车logic 36651~36670
const unsigned int CMD_GetStarTrainEntry = 36651;   // 摘星列车入口信息
const unsigned int CMD_GetStarTrainInfo = 36652;    // 获取摘星列车玩法信息，用户打开页面或手动刷新时请求
const unsigned int CMD_GetStarTrainProgress = 36653;// 获取列车进程信息，客户端定时轮询请求
const unsigned int CMD_GetStarTrainBroadcast = 36654;// 获取列车播报数据，打开页面时请求
const unsigned int CMD_GetStarTrainSeatList = 36655;// 获取列车座次表

// 虚拟头像logic 36671~36689
const unsigned int CMD_GetVirtualAvatarEntry = 36671;   // 获取虚拟形象入口状态
const unsigned int CMD_GetUserVirtualAvatarList = 36672; // 获取用户虚拟形象列表
const unsigned int CMD_SetUserVirtualAvatarInUse = 36673; // 用户佩戴虚拟形象
const unsigned int CMD_GetUserVirtualAvatarInUse = 36674; // 获取用户使用中的虚拟形象信息
const unsigned int CMD_SetUserVirtualAvatarUseScope = 36675; // 设置用户虚拟形象使用范围【废弃】
const unsigned int CMD_GetUserVirtualAvatarUseScope = 36676; // 获取用户虚拟形象使用范围【废弃】
const unsigned int CMD_GetUserCurrMicAvatarType = 36677;    // 主态 获取麦位形象类型
const unsigned int CMD_SetUserCurrMicAvatarType = 36678;    // 主态 设置麦位形象类型
const unsigned int CMD_SetUserVirtualAvatarUseScopeV2 = 36679; // 设置用户虚拟形象使用范围
const unsigned int CMD_GetUserVirtualAvatarUseScopeV2 = 36680; // 获取用户虚拟形象使用范围

// 社群logic 36700~36799
const unsigned int CMD_ListMuseSocialCommunityNavBars = 36700;   // 获取导航栏
const unsigned int CMD_ListMuseSocialCommunityNavSecondaryBars = 36701;   // 获取二级导航栏

const unsigned int CMD_GetSocialCommunityChatChannelHistoryMsg = 36702;     /* 社群聊天室历史消息 */
const unsigned int CMD_BatGetMuseSocialCommunityUsersRole = 36703;      /* 获取用户社群角色 */
const unsigned int CMD_JoinSocialCommunityFans=36705;  /*加入社群粉丝团*/
const unsigned int CMD_GetSocialCommunityDetail=36706;  /*获取社群基本信息*/
const unsigned int CMD_GetSocialCommunityProfilePages=36707; /*获取社群资料页*/
const unsigned int CMD_GetMySocialCommunity=36708;   /*获取我的社群信息*/
const unsigned int CMD_GetSocialCommunityUserRoles=36709;/**/
const unsigned int CMD_SendWelcomePush=36710; /*推送欢迎标语*/
const unsigned int CMD_BatGetRankInChannel=36711; /* 上榜社群的公演房增加榜单入口 */
const unsigned int CMD_RemoveSocialCommunityMember=36712 ;   /*开除社群成员*/
const unsigned int  CMD_ExitSocialCommunity=36713;   /*社群成员主动退出社群*/
const unsigned int   CMD_UpdateMemberRole=36714;      /*更改社群核心成员的身份*/
const unsigned int   CMD_GetSocialCommunityRoleLeftNumbers=36715;   /*获取社群中每种角色剩余可设置的数量*/
const unsigned int   CMD_GetChannelAssociateSocialCommunity=36716;   /*获取兴趣讨论区*/
const unsigned int   CMD_GetMySocialCommunityPage=36717 ;   /*获取我的社群页面*/
const unsigned int   CMD_ListCategoryTypes=36718;  /*获取品类类型*/
const unsigned int  CMD_ListCategories=36719; /*获取品类*/
const unsigned int CMD_ApplyCreateSocialCommunity=36720; /*申请创建社群*/
const unsigned int CMD_GetSocialCommunityFloat=36721;/*获取悬浮窗*/
const unsigned int  CMD_JoinSocialCommunity=36722;/*加入核心成员*/
const unsigned int  CMD_ValidateUserHasCreateQualification=36723;/*查看用户是否有资格创建社群*/
const unsigned int  CMD_GetSocialCommunityBase=36724;/*获取社群基本信息接口*/
const unsigned int  CMD_GetUserSocialGroupIds=36725;/* 获取用户当前社群群聊Id*/
const unsigned int CMD_MuseSocialPreviewGroupMessage=36726;/* 获取群预览信息*/
const unsigned int CMD_MuseSocialGroupRemoveAdmin=36727;/*移除管理员*/
const unsigned int  CMD_MuseSocialGroupSetAllMute=36728;/*全部禁言*/
const unsigned int  CMD_MuseSocialGroupMuteMember=36729;/* 禁言*/
const unsigned int  CMD_MuseSocialGroupUnmuteMember=36730;/*解除禁言*/
const unsigned int  CMD_MuseSocialGroupGetMuteList=36731;/*禁言列表*/
const unsigned int  CMD_MuseSocialGroupGetMemberList=36732;/*成员列表*/
const unsigned int  CMD_MuseSocialGroupGetDetailInfo=36733;/*群详情*/
const unsigned int CMD_MuseSocialGroupAddAdmin=36734;/* 加入管理员*/
const unsigned int CMD_GetSocialGroupOnlineMembers=36735;/*获取群聊在线人数*/
const unsigned int CMD_BatGetSocialCommunityKernelMembers=36736;/*获取所有核心成员*/
const unsigned int CMD_GetGroupActiveMembers=36737; /*获取群聊核心成员*/
const unsigned int  CMD_GetSocialCommunityMemberList=36738; /* 获取社群成员列表*/
const unsigned int CMD_GetSocialCommunityAnnounceNewsCount = 36740; // 获取社群通告牌新消息数量
const unsigned int CMD_UpsertMuseSocialAnnounce = 36741; // 创建、修改公告
const unsigned int CMD_ListAnnounceDestinations = 36742; // 公告位置列表
const unsigned int CMD_ListMuseSocialAnnounces = 36743; // 公告列表
const unsigned int CMD_SetMuseSocialAnnounceInterest=36744; //设置感兴趣
const unsigned int CMD_RemoveMuseSocialAnnounce=36745; //删除通告牌
const unsigned int CMD_ListMuseSocialAnnounceInterestUsers=36746; //通告感兴趣的成员列表
const unsigned int CMD_ValidateUserHasCreateAnnouncePermissions=36747;  //验证用户是否有创建权限
const unsigned int CMD_SetCommunityAdditionMode=36748;//设置加群模式
const unsigned int CMD_GetCommunityAdditionMode=36749;//获取加群模式
const unsigned int CMD_ListSocialCommunitySystemMessage=36750;//获取社群系统通知列表
const unsigned int CMD_SubmitApplicationToJoinCommunity=36751;//提交加入请求
const unsigned int CMD_UpsertJoinSocialCommunityMessageStatus=36752;//成员申请加入社群的工单状态变化
const unsigned int CMD_GetSocialCommunityUpdateLevelTip=36753;//获取升级提醒
const unsigned int CMD_GetSocialCommunityLevelDetail=36754;//社团等级页
const unsigned int CMD_SocialCommunityCheckIn=36755;//签到
const unsigned int CMD_GetSocialCommunityContentStreamNewsCount=36756;//我的社群红点展示
const unsigned int CMD_GetSocialCommunityContentStream=36757;//根据品类id或者帖子id获取流信息
const unsigned int CMD_ListMuseSocialCommunityCommentMessage=36758;//获取互动消息评论
const unsigned int CMD_ListMuseSocialCommunityAttitudeMessage=36759;//获取互动消息点赞
const unsigned int CMD_GetSocialCommunityNonPublicUserCard=36760; //获取私域用户卡片信息
const unsigned int CMD_IntroduceSocialCommunityByCategoryId=36761; //根据品类获取推荐社团id
const unsigned int CMD_ListMuseSocialCommunityNavBarsV2=36762; //导航栏
const unsigned int CMD_ListMuseSocialCommunityNavSecondaryBarsV2=36763; //获取二级导航栏
const unsigned int CMD_ListMuseSocialCommunityGroups=36764; //获取群聊列表
const unsigned int CMD_BatchMuseSocialCommunityNavBarsV2=36765; //获取社群导航状态
const unsigned int CMD_UpdateSocialCommunityInfo=36766;   /*编辑社群信息*/
const unsigned int GetSocialCommunityEditableInfo=36767;//获取可编辑的社群信息
const unsigned int CMD_ReportPersonalChannelViewSocialCommunity=36768;//上报用户在个人房点击了社群入口
const unsigned int CMD_GetMemberStatusInTheSocialCommunity =36769;//查询用户是否加入了某个社群
const unsigned int CMD_GetSocialCommunityAssistantMsgCount =36770;// 获取社团助手消息数
const unsigned int CMD_GetSocialCommunityInvitationCodeDetail = 36771; // 获取社群邀请码详情
const unsigned int CMD_GetSocialCommunityInvitationCodeShareText = 36772; // 邀请码分享文本
const unsigned int CMD_SocialCommunitySharePreCheck = 36773; // 社群分享前置风控检查
const unsigned int CMD_CheckSocialCommunityInvitationUser = 36774; // 社群邀请码被邀请人用户检查
const unsigned int CMD_SearchSocialCommunity = 36775; // 搜索社群
const unsigned int CMD_GetUserSocialCommunityList = 36776; // 获取用户的社群
const unsigned int CMD_GetExtendMicPermission = 36777; // 获取麦位扩展权限
const unsigned int CMD_SetExtendMicNumbers = 36778; // 设置麦位数量
const unsigned int CMD_GetExtendMicNumbers = 36779; // 获取已解锁的麦位数量
const unsigned int CMD_GetSocialCommunityCaptain=36780;  //获取社群主理人
const unsigned int CMD_GetSocialCommunityActivityPopup=36781; //获取活动弹窗
const unsigned int CMD_UpdateSocialCommunityMemberRole=36782; //修改社群成员角色

// 谜境用户标签logic 36800-36899
const unsigned int CMD_MijingGetEscapeUserLabelList = 36800;/* 获取密逃用户标签 */

// 背包重构go版本logic 36900-36950
const unsigned int CMD_NewGetUserBackpack  = 36900; // 获取用户背包物品信息
const unsigned int CMD_NewUseFuncCard = 36901; // 使用功能卡
const unsigned int CMD_NewGetUserFuncCardUse = 36902; // 获取用户功能卡使用详情
const unsigned int CMD_NewGetUserFragment = 36903; // 获取用户碎片详情
const unsigned int CMD_NewGetImBackpack = 36904; // 获取IM背包详情

// 营收语音流管理服务 36951~36979
const unsigned int CMD_ReportAudioStream=36951; /* 上报语音流 */
const unsigned int CMD_GetAudioChatInfo=36952; /* 进房拉取连麦信息 */
const unsigned int CMD_SetBlankingStreamStatus=36953; /* 设置闭麦状态 */

//获取发布帖子按钮引导配置
const unsigned int  CMD_GetPostButtonGuide=37001;            //获取发布帖子按钮的引导配置

const unsigned int  CMD_GetMuseAllocateChannel=38001;     // mt相关业务房间下发
const unsigned int  CMD_GetMuseAllocateNonEnterChannel = 38002;     // 请求房间下发承接非进房老用户策略
const unsigned int  CMD_GetMuseAllocateNonEnterChannelInfo = 38003;     // 请求房间下发承接非进房老用户UI数据
const unsigned int CMD_GetQuickEnterChannelModel = 38004; // 首页快速匹配模块列表 新模块 muse-allocate-logic
const unsigned int CMD_GetTodayCoupleList = 38005; // 今日CP列表 新模块 muse-allocate-logic

const unsigned int CMD_GetFlashChatMatchingCondition = 38100; // 获取闪聊匹配条件
const unsigned int CMD_GetFlashChatMatchingResult = 38101; // 获取闪聊匹配结果
const unsigned int CMD_GetFlashChatObjectInfo = 38102; // 获取闪聊对象年龄性别位置信息
const unsigned int CMD_FlashChatEntryPreCheck = 38103;
const unsigned int CMD_ViewPersonalHomepageReport = 38104; // 查看个人主页上报

// muse 闪光点相关 38010--38029
const unsigned int CMD_GetAllShiningPoint = 38010; // 获取全部闪光点配置 muse-shining-point-logic
const unsigned int CMD_UpdateUserShiningPoint = 38011; // 更新用户闪光点 muse-shining-point-logic
const unsigned int CMD_GetUserShiningPoint = 38012; // 获取用户闪光点 muse-shining-point-logic
const unsigned int CMD_ShiningPointFriendCert = 38013; // 闪光点好友认证 muse-shining-point-logic
const unsigned int CMD_BatchShiningPointFriendCert = 38014; // 批量闪光点好友认证 muse-shining-point-logic
const unsigned int CMD_GetUserAddShiningPointGuidance = 38015; // 引导用户添加闪光点页面 muse-shining-point-logic
const unsigned int CMD_GetShiningPointIntroPage = 38016; // 获取闪光点介绍页请求 muse-shining-point-logic


//礼物墙相关
const unsigned int CMD_GetUserPresentWall = 39001 ; // 获取用户新版礼物墙
const unsigned int CMD_GetUserPresentWallMissingItems = 39002 ; // 获取用户新版礼物墙未获得的礼物
const unsigned int CMD_GetPresentNamingInfo = 39003 ; // 获取礼物墙冠名礼物信息
const unsigned int CMD_GetPresentWallSwitch = 39004 ; // 获取礼物墙开关信息
const unsigned int CMD_SetUserNamingSwitch = 39005 ; // 设置用户礼物墙冠名开关状态
const unsigned int CMD_GetUserNamingSwitch = 39006 ; // 获取用户礼物墙冠名开关状态

// userrecommendlogic 重构GO 39100-39150
const unsigned int CMD_NewGetContractInfo        = 39100; // 获取用户合约信息(原CMD: 5065)
const unsigned int CMD_NewChangeRecommendStatus  = 39101; // 修改是否允许被推荐的状态(原CMD: 1184)
const unsigned int CMD_NewGetRecommendStatus     = 39102; // 获取用户自己的状态(原CMD: 1185)
const unsigned int CMD_NewCheckUserFinishFindFriendExam  = 39103;    // 原:CMD_CheckUserFinishFindFriendExam, 仍有流量
const unsigned int CMD_NewAddOrUpdateContacts      = 39104;     // 上传用户手机通讯录(原CMD: 1181)
const unsigned int CMD_NewGetRecommendFromContacts = 39105;     // 从通讯录信息中匹配好友用户(原CMD: 1182)
const unsigned int CMD_NewGetUserRecommend         = 39106;     // [已经下线废弃的业务] 获取推荐信息(原CMD: 1183)

// 谜境NPC 39200~39210
const unsigned int CMD_IsMiJingNPCSupported = 39200; // 剧本是否支持NPC
const unsigned int CMD_MiJingNPCJoinGame = 39201; // 加入游戏
const unsigned int CMD_GetEscapeTipMsg = 39202; // 获取快捷语
const unsigned int CMD_GetEscapeChatBotMsg = 39203; // 获取一条机器人消息
const unsigned int CMD_GetEscapeChatBotInfo = 39204; // 获取聊天机器人信息
const unsigned int CMD_GetEscapeChatBotList = 39205; // 获取聊天机器人信息列表

// 谜境VIP 39211~39220
const unsigned int CMD_GetMiJingVipInfo = 39211; // 获取用户vip信息

//museAI红娘，角色扮演 39221~39249
const unsigned int CMD_GetAICupidDetail = 39221; //获取ai红娘资料卡片
const unsigned int CMD_ChangeAICupidStatus = 39222; // 修改红娘状态
const unsigned int CMD_AIPlayHubFunctionStatus= 39223; //AI相关功能开关
const unsigned int CMD_GetAIInspiration= 39224; //获取灵感回复
const unsigned int CMD_GetRolePlayHomePage = 39225;  // 获取角色扮演首页
const unsigned int CMD_GetRolePlayFeeds = 39226;    // 获取角色扮演流
const unsigned int CMD_RolePlayUserLike= 39227;     // 用户卡片下一个/组CP
const unsigned int CMD_GetRolePlayReceiveCpList = 39228;   //获取收到的cp请求列表
const unsigned int CMD_SayHelloTempRoleCard = 39229;   //临时卡片打招呼
const unsigned int CMD_GetRolePlaySendCpList = 39230;   //我发起的cp请求列表
const unsigned int CMD_HiddenRolePlaySayHelloRecord = 39231;  //隐藏打招呼记录
const unsigned int CMD_ReportRolePlayUserClickedCard = 39232;  //  //上报角色扮演用户已点卡片


// muse热聊挑战赛 39250~39300
const unsigned int CMD_GetMuseChannelHotGame = 39250; // 获取玩法的热聊挑战
const unsigned int CMD_GetMuseChannelHotGameFloat = 39251; // 获取热聊挑战浮窗
const unsigned int CMD_SetMuseChannelHotGameJoinStatus = 39252; // 参加或取消参加热聊



// 礼物套组 39301~39350
const unsigned int CMD_GetPresentSetInfo = 39301; // 获取礼物套组列表
const unsigned int CMD_GetPresentSetDetail = 39302; // 获取礼物套组详情

// 礼物对决 39351~39370
const unsigned int CMD_CheckChannelGiftPkEntry = 39351; // 检查礼物对决入口门槛
const unsigned int CMD_GetChannelGiftPkInfo = 39352; // 获取礼物对决信息
const unsigned int CMD_SponsorChannelGiftPk = 39353; // 发起礼物对决
const unsigned int CMD_CancelChannelGiftPkMatch = 39354; // 取消礼物对决匹配
const unsigned int CMD_ChooseChannelGiftPkSprite = 39355; // 选择对决精灵
const unsigned int CMD_GetRecentlyChannelGiftPkLog = 39356; // 最新对决记录播报
const unsigned int CMD_GetChannelGiftPkRecord = 39357; // 房间内对决记录

// 充值(首充)活动 39371~39380
const unsigned int CMD_GetNewRechargeActEntryInfo = 39371; // 获取充值(首充)活动入口信息
const unsigned int CMD_GetNewRechargeActPopupInfo = 39372; // 获取充值(首充)活动弹窗信息
const unsigned int CMD_GetRechargeBannerInfo = 39373; // 获取充值页banner信息
const unsigned int CMD_CheckCanModifySex = 39374; // 检查能否修改性别

// 礼物相关新 39400 - 39450（1160-1200都用完了）
const unsigned int CMD_GetTimePresentOnShelf = 39400; // 获取当前房间可以展示的时间礼物
const unsigned int CMD_GetChannelLiveIntimatePresentList = 39401; // 获取直播间亲密礼物列表 , 进房时调用
const unsigned int CMD_GetLiveIntimatePresentConfigList = 39402; // 获取直播间亲密礼物额外配置列表，增量同步，登录时获取并定时刷新
const unsigned int CMD_GetLiveIntimatePresentOnShelf = 39403; // 获取当前房间可以展示的直播间亲密礼物


// 电竞指导 40000~40100
const unsigned int CMD_EsportGetGameList = 40000; // 获取电竞游戏列表
const unsigned int CMD_EsportGetSwitch = 40001; // 获取开关状态
const unsigned int CMD_EsportGetESportApplyAccess = 40002; // 获取用户电竞指导身份信息（成为电竞指导入口(非工会入口)）
const unsigned int CMD_EsportGetTopGameList = 40003; // 找人优化 获取顶部游戏列表

const unsigned int CMD_EsportGetInviteOrderRecommend = 40004;   // 获取下单推荐
const unsigned int CMD_EsportGetGamePropertyList = 40005;   // 获取游戏属性(筛选项)
const unsigned int CMD_EsportGetEsportAreaCoachList = 40006;   // 获取电竞专区电竞教练列表
const unsigned int CMD_EsportGetHomePageSkillProductList = 40007;  // 个人主页技能商品列表
const unsigned int CMD_EsportGetIMFloatWindowInfo = 40008;    // im浮窗商品列表/订单状态
const unsigned int CMD_EsportInviteOrder = 40009;    // 邀请下单

const unsigned int CMD_EsportPlayerPayOrder = 40010; // 玩家下单
const unsigned int CMD_EsportPlayerCancelOrder = 40011; // 玩家取消订单
const unsigned int CMD_EsportPlayerFinishOrder = 40012; // 玩家确认完成订单
const unsigned int CMD_EsportCoachReceiveOrder = 40013; // 电竞指导接单
const unsigned int CMD_EsportCoachRefuseOrder = 40014; // 电竞指导拒绝接单
const unsigned int CMD_EsportGetOrderDetail = 40015; // 获取订单详情
const unsigned int CMD_EsportGetOrderList = 40016; // 获取订单记录列表
const unsigned int CMD_EsportDelOrderRecord = 40017; // 删除订单记录
const unsigned int CMD_EsportGetReasonTextList = 40018; // 获取原因文案列表
const unsigned int CMD_EsportCoachNotifyFinishOrder = 40019; // 电竞指导提醒玩家去完成订单
const unsigned int CMD_EsportAcceptRefund = 40020; // 电竞指导接受退款

const unsigned int CMD_EsportHandleInviteOrder = 40021; // 处理下单邀请
const unsigned int CMD_EsportGetEvaluateWordList = 40022; // 获取评价快捷词列表
//const unsigned int CMD_EsportGetEvaluateList = 40023; // 分页获取用户评价列表
const unsigned int CMD_EsportEvaluate = 40024; // 评价

const unsigned int CMD_GetEsportGameCardConfig = 40025; // 新建游戏名片时获取配置
const unsigned int CMD_GetEsportGameCardInfo = 40026; // 获取游戏名片信息
const unsigned int CMD_UpsertEsportGameCardInfo = 40027; // 更新创建游戏名片
const unsigned int CMD_GetEsportGameCardList = 40028; // 获取游戏名片列表
const unsigned int CMD_SendEsportGameCard = 40029; // 发送游戏名片
const unsigned int CMD_DeleteEsportGameCard = 40030; // 删除游戏名片
const unsigned int CMD_GetGameCardGame = 40031; // 获取配置了游戏名片的游戏

const unsigned int CMD_EnterEsportIMPageReport = 40032; // 用户从游戏详情卡进入大神IM页上报
const unsigned int CMD_GetChatListEsportTags = 40033; // 获取IM页电竞tag

const unsigned int CMD_GetEsportQuickReplyList = 40034; // 获取快捷回复列表
const unsigned int CMD_ReportQuickReply = 40035; // 上报快捷回复

const unsigned int CMD_ReportExposeCoach = 40036; // 上报已曝光大神
const unsigned int CMD_PlayerPayOrderPreCheck = 40037; // 玩家下单前检查
const unsigned int CMD_EstimateOrderTotalPrice = 40038; // 估算订单总价
const unsigned int CMD_ContactCustomerService = 40039; // 联系客服
const unsigned int CMD_IsUserACustomer = 40040; // 判断指定用户是否是客服
const unsigned int CMD_GetUGCReListEnt = 40041; // 获取ugc房推荐列表的入口
const unsigned int CMD_GetUGCReCoachCardInfo = 40042; // 获取ugc房推荐大神卡信息接口
const unsigned int CMD_NoMoreReOnUGC = 40043; // 更新不再推荐状态接口
const unsigned int CMD_GetRecommendedGodList = 40044; // 获取推荐大神列表接口
const unsigned int CMD_LoginAppShowCouponRemain = 40045; // 登录获取优惠券弹窗
const unsigned int CMD_ShowManualGrantCoupon = 40046; // 显示手动发放的优惠券
const unsigned int CMD_MarkManualGrantCouponRead = 40047; // 标记手动发放的优惠券已读
const unsigned int CMD_GetCouponEntranceInfo = 40048; // 获取优惠券入口信息
const unsigned int CMD_GetHomeCouponEntranceInfo = 40049; // 获取首页优惠券入口信息
const unsigned int CMD_GetEsportAreaTopBannerList = 40050; // 电竞专区金刚位
const unsigned int CMD_GetEsportCoachMissionInfo = 40051; // 获取电竞指导任务信息
const unsigned int CMD_GetBackRecallReCoach = 40052; // 获取新用户退出挽留展示的推荐大神（复用专区列表的结构）
const unsigned int CMD_GetNewCustomerTabSetting = 40053; // 获取专区新用户承接弹出页的数据œ
const unsigned int CMD_PostNewCustomerTabSetting = 40054; // 提交专区新用户承接弹出页的数据
const unsigned int CMD_GetOneKeyFindCoachEntry = 40055; // 获取一键找大神入口
const unsigned int CMD_GetOneKeyPublishCfg = 40056; // 获取一键发布配置
const unsigned int CMD_PublishOneKeyFindCoach = 40057; // 一键找大神发布
const unsigned int CMD_CancelOneKeyFindCoach = 40058; // 一键找大神取消
const unsigned int CMD_StickOneKeyFindCoach = 40059; // 一键找大神置顶
const unsigned int CMD_GetGoingOneKeyFindCoach = 40060; // 获取进行中的一键找大神
const unsigned int CMD_EsportReportClickIm = 40061; // 电竞指导IM页点击上报
const unsigned int CMD_EsportRegionHeartbeat = 40062; // 电竞专区心跳
const unsigned int CMD_CheckIfCanPublishOneKeyFindCoach = 40063; // 检查是否可以发布一键找大神
const unsigned int CMD_GetGlobalOneKeyFindCfg = 40064; // 获取全局一键找大神配置

// hello
const unsigned int CMD_SayHello = 50001;                            // say hello
const unsigned int CMD_DemoHW_AddAndEcho = 50002;                            // say hello

//团战房 melee-channel 50010-50050
const unsigned int CMD_ApplyToMicWhiteList                  = 50010;         // 申请加入上麦白名单
const unsigned int CMD_HandleMicWhiteListApply              = 50011;         // 处理加入上麦白名单申请（同意或拒绝）
const unsigned int CMD_GetMicWhiteApplyList                 = 50012;         // 获取申请列表
const unsigned int CMD_GetOnMicWhiteList                    = 50013;         // 获取白名单
const unsigned int CMD_AddToMicWhiteList                    = 50014;         // 添加白名单
const unsigned int CMD_RemoveFromWhiteList                  = 50015;         // 删除白名单
const unsigned int CMD_GetSelfOnMicQualifications           = 50016;         // 上麦资格查询
const unsigned int CMD_ApplyOnMicToken                      = 50017;         // 申请上麦凭证
const unsigned int CMD_GetMeleeChannelUserInfoList          = 50018;         // 获取用户房间内用户信息
const unsigned int CMD_SetWhiteListSwitchStatus             = 50019;         // 设置白名单开关
const unsigned int CMD_GetMeleeChannelMicWhiteCandidateList = 50020;         // 获取麦位白名单候选白名单（就是不在白名单内的房间内成员）
const unsigned int CMD_GetMicWhiteApplyCount                = 50021;         // 获取申请列表人数
const unsigned int CMD_GetChannelRoomApplyList              = 50022;         // 获取进房申请列表
const unsigned int CMD_AddChannelRoomApplyList              = 50023;         // 添加进房申请名单
const unsigned int CMD_HandleChannelRoomApply               = 50024;         // 处理进房申请
const unsigned int CMD_GetChannelRoomWhitelist              = 50025;         // 获取可进房白名单
const unsigned int CMD_AddChannelRoomWhiteList              = 50026;         // 添加可进房白名单
const unsigned int CMD_RemoveChannelRoomWhiteList           = 50027;         // 删除可进房白名单
const unsigned int CMD_EnterRoomWhitelistConfig             = 50028;         // 是否展示进房管理入口

//团战新手引导
const unsigned int CMD_GetTeamFightGuide            = 50051; // 获取团战新手引导
const unsigned int CMD_OpenCommonMic                = 50052; // 开启公共麦
const unsigned int CMD_ChannelRoleplayUpsertBoxInfo = 50053; // 创建/修改子频道
const unsigned int CMD_ChannelRoleplayDelBoxInfo    = 50054; // 删除子频道

const unsigned int CMD_GetMarketingUserInfo = 50061;                    // 推广场景的 用户属性

// 多供应商人脸
const unsigned int CMD_GetFaceRecognitionProvider = 50500; // 获取人脸识别供应商
const unsigned int CMD_GetFaceRecognitionCertifyId = 50501; // 获取人脸识别认证ID
const unsigned int CMD_GetFaceRecognitionResult = 50502; // 获取人脸识别结果
const unsigned int CMD_GetFaceRecognitionProvider_NoAuth = 50503; // 获取人脸识别供应商（不鉴权）
const unsigned int CMD_GetFaceRecognitionCertifyId_NoAuth = 50504; // 获取人脸识别认证ID（不鉴权）
const unsigned int CMD_GetFaceRecognitionResult_NoAuth = 50505; // 获取人脸识别结果（不鉴权）

// 房间内置小玩法，ugc房间玩什么
const unsigned int CMD_GetNumBombStatus = 50600; // 获取数字炸弹状态
const unsigned int CMD_OpenNumBomb = 50601; // 开启数字炸弹
const unsigned int CMD_StartNumBomb = 50602;// 开始数字炸弹
const unsigned int CMD_PlayNumBomb = 50603; // 播放数字炸弹
const unsigned int CMD_CloseNumBomb = 50604; // 关闭数字炸弹
const unsigned int CMD_GetPropConfig = 50605; // 获取道具配置


/** 互动表情 **/
const unsigned int CMD_GetInteractionExpressionConf = 50606; //获取互动表情配置
const unsigned int CMD_GetInteractionExpressionPriv = 50607; //获取互动表情权限
const unsigned int CMD_SendInteractionExpression = 50608; //发放互动表情

// 主播心愿单
const unsigned int CMD_GetAnchorWishList = 50700; // 获取主播心愿单
const unsigned int CMD_SetAnchorWishList = 50701; // 设置主播心愿单
const unsigned int CMD_GetWishGratitudeWords = 50702; // 获取选取感谢寄语列表

//挂房听歌 自动播放歌曲 channel-listening-auto-play 50800-50850
const unsigned int CMD_ChannelPlayStatus  = 50800; // 用户进房获取播放状态
const unsigned int CMD_SwitchChannelPlayMode   = 50801; //开关切换自动播放/点歌模式
const unsigned int CMD_CutAutoModeSong   = 50802; //自动模式切歌
const unsigned int CMD_ReportChannelAutoSongProgress   = 50803; //歌曲进度上报
const unsigned int CMD_ChannelRcmdMusicMenu   = 50804; //获取推荐歌曲榜单
const unsigned int CMD_SetVolume    = 50805; //设置音量
const unsigned int CMD_SetChannelPlayMode    = 50806; //切换自动播放/点歌模式
const unsigned int CMD_SetChannelPlayStatus    = 50807; //暂停/播放
const unsigned int CMD_SelectAutoPlayPlayMode    = 50808; /* 自动播放的播放模式选择 顺序、单曲、随机 */
// 挂房听歌 网易云音乐播放
const unsigned int CMD_ListeningChangePlayer = 50809; // 切换播放器的开关，切换网易云播放器
const unsigned int CMD_ListeningGetPlayerStatus = 50810; // 获取播放器状态
const unsigned int CMD_ListeningSearchSongByKey = 50811; // 搜索歌曲
const unsigned int CMD_ListeningGetSongListByType = 50812; // 根据歌单id获取歌单列表
const unsigned int CMD_ListeningGetSongListList = 50813; // 获取歌单列表
const unsigned int CMD_ListeningGetPlayerUserInfo = 50814; // 获取播放器用户信息
const unsigned int CMD_ListeningOrderSong = 50815; // 点歌
const unsigned int CMD_ListeningGetPlayList = 50816; // 获取播放列表
const unsigned int CMD_ListeningGetSongResource = 50817; // 获取歌曲url
const unsigned int CMD_ListeningReportRecord = 50818; // 提供上报接口
const unsigned int CMD_ListeningLogout = 50819; // 退出网易云登录

// 房间基础 50851 ~ 50950
const unsigned int CMD_GetChannelAudioToken = 50851; // 获取房间音频token
const unsigned int CMD_ChannelMicTakeChange = 50852; // 房间内麦位抢麦
const unsigned int CMD_GetChannelMicName = 50853;    // 查询麦位名称信息
const unsigned int CMD_SetChannelMicName = 50854;    // 设置麦位名称信息
const unsigned int CMD_SetChannelMicSwitch = 50855;  // 【废弃，命名有歧义】
const unsigned int CMD_GetChannelOnlineMember = 50856; // 获取房间在线成员列表
const unsigned int CMD_SetChannelMicNameSwitch = 50857;  // 设置麦位名称开关状态
const unsigned int CMD_EnterChannelV2 = 50858;         // 新进房命令，6.34.0版本后
const unsigned int CMD_GetEnterChannelExtInfo = 50859; // 拉取进房信息/扩展信息
const unsigned int CMD_GetChannelGameBaseInfo = 50860; // 获取游戏基本信息，应用于游戏包预下载
const unsigned int CMD_CheckUserStartGamePermission = 50861; // 检查(特殊)用户是否能开始游戏
const unsigned int CMD_ChannelAudioViolationStartJudgment = 50862;   // 发起一轮违规审判
const unsigned int CMD_ChannelAudioViolationJudgmentVote = 50863;    // 违规审判投票
const unsigned int CMD_ChannelAudioViolationJudgmentAppeal = 50864;  // 对审判结果发起申诉
const unsigned int CMD_ChannelAudioViolationReportAudioFile = 50865; // 上报违规的音频文件
const unsigned int CMD_ChannelAudioViolationIsDetectEnabled = 50866; // 房间的违规检测是否开启
const unsigned int CMD_UserGetSwitch = 50867; // 获取用户开关
const unsigned int CMD_UserSetSwitch = 50868; // 设置用户开关
const unsigned int CMD_GetChannelReliableMsg = 50869; // 进房后拉取房间可靠消息(用于推送系统订阅和进房之间时间差的补偿)
const unsigned int CMD_GetAllMicLayoutTemplates = 50870; // 获取全部的麦位布局模板
const unsigned int CMD_GetCurChannelMicLayoutData = 50871; // 获取房间当前麦位布局和模板数据
const unsigned int CMD_SwitchChannelMicLayoutTemplate = 50872; // 切换房间当前麦位布局模板
const unsigned int CMD_SetMicLayoutMirroredMicId = 50873; // 设置镜像麦位id（演唱布局下，重新选麦位id时调该接口）
const unsigned int CMD_GetUserCustomShortcuts = 50874; // 获取用户自定义的房间快捷方式
const unsigned int CMD_SetUserCustomShortcuts = 50875; // 设置用户自定义的房间快捷方式
const unsigned int CMD_ChannelHeartbeatUpdate = 50876; // 房间心跳更新
const unsigned int CMD_GetAudioSamplingConfig = 50877; // 获取音频抽样配置
const unsigned int CMD_IsSampledAudioNeedSave = 50878; // 抽样音频是否需要保存
const unsigned int CMD_ReportSampledAudio = 50879;     // 上报抽样的音频
const unsigned int CMD_GetAudioBitRateUpgradeConfig = 50880; // 获取码率升级配置
const unsigned int CMD_UpgradeAudioBitRate = 50881;          // 升级房间音频码率

// 珍宝馆 50951 ~ 50970
const unsigned int CMD_GetTreasureActivityList = 50951; // 获取珍宝馆活动列表
const unsigned int CMD_GetTreasureActivity = 50952; // 获取珍宝馆活动
const unsigned int CMD_ClaimPresentPermission = 50953; // 领取礼物使用权
const unsigned int CMD_BuyPresentPermission = 50954; // 购买礼物使用权
const unsigned int CMD_GetTreasureActivityUpdateInfo = 50955; // 获取珍宝馆活动更新提醒

//用户召回 50971 ~ 50990
const unsigned int CMD_CheckIsRecall = 50971; // 检查是否是召回用户
const unsigned int CMD_UserRecallGetPrize = 50972; // 被召回用户领奖
const unsigned int CMD_BindInviter = 50973; // 绑定邀请人

const unsigned int CMD_GetUserRecallList = 50974; // 获取可召回好友列表
const unsigned int CMD_SendRecall = 50975; // 发起召回

// 礼物图鉴 50991 ~ 51010
const unsigned int CMD_GetIllustrationSummary = 50991; // 获取图鉴摘要
const unsigned int CMD_GetIllustrationList = 50992; // 获取图鉴列表
const unsigned int CMD_GetIllustrationDetail = 50993; // 获取图鉴详情

// 魔法表情 51051 ~ 51080
const unsigned int CMD_GetMagicExpressionInfoList = 51051; // 获取魔法表情信息列表
const unsigned int CMD_ChannelMagicExpressionPush = 51052; // 推送魔法表情

// 大r生日特权 51100 ~ 51149
const unsigned int CMD_GetRicherBirthdayInfo = 51100; // 获取大R生日信息
const unsigned int CMD_BathGetRicherBirthdayInfo = 51101; // 批量获取大R生日信息
const unsigned int CMD_HideRicherBirthdaySwitch = 51102; // 隐藏大R生日开关
const unsigned int CMD_SendRicherBirthdaySystemMessage = 51103; // 发送大R生日系统消息
const unsigned int CMD_GetRicherBirthdayGiftCfgInRoom = 51104; // 获取生日礼物配置
const unsigned int CMD_GetHideRicherBirthdaySwitch = 51105; // 获取隐藏大R生日开关



// 虚拟形象 51150 ~ 51170
const unsigned int CMD_GetResourceListRequest = 51150;          // 获取资源列表
const unsigned int CMD_GetVirtualImageResourceCategory = 51151; // 获取资源分类
const unsigned int CMD_GetCommodityDataList = 51152;            // 获取商品数据列表
const unsigned int CMD_BuyCommodityData = 51153;                // 购买商品数据
const unsigned int CMD_ComputeCommodityPrice = 51154;           // 计算商品价格
const unsigned int CMD_GetRecommendCommodityDataList = 51155;   //推荐商品列表
const unsigned int CMD_GetRedDotAlertStatus = 51156;         // 获取红点提醒状态
const unsigned int CMD_RedDotAlertReaded = 51157;                // 红点提醒已读
const unsigned int CMD_GetCommodityDataListById = 51159;        // 根据商品id获取商品数据列表
const unsigned int CMD_GetDefaultResourceList = 51158;         // 获取默认穿戴的资源
const unsigned int CMD_GetUserVirtualImage = 51160;          // 获取用户虚拟形象
const unsigned int CMD_SetUserVirtualImageInuse = 51161;    // 设置用户虚拟形象使用
const unsigned int CMD_GetVirtualImageDisplayCfg = 51162;   // 获取用户虚拟形象外显配置
const unsigned int CMD_SetVirtualImageDisplaySwitch = 51163; // 设置用户外显开关
const unsigned int CMD_GetVirtualImageBindInvitableList = 51164;       // 获取可绑定的邀请列表
const unsigned int CMD_SetVirtualImageBindInvite = 51165;              // 发起/取消 关系绑定邀请
const unsigned int CMD_VirtualImageBindConfirmAction = 51166;          // 确认/拒绝绑定
const unsigned int CMD_GetVirtualImageBindInviteStatus = 51167;        // 获取关系绑定邀请状态
const unsigned int CMD_UnbindVirtualImage = 51168;         // 解除绑定
const unsigned int CMD_SetVirtualImageBindInUse = 51169;        // 指定使用双人关系
const unsigned int CMD_GetBindBeInvitedList = 51170;        // 获取用户待处理的关系绑定邀请列表

// muse喊话功能 51171 ~ 51200
const unsigned int CMD_ModifyUserMegaphone = 51171;             // 修改喊话内容
const unsigned int CMD_BlockUserMegaphone = 51172;             // 屏蔽喊话内容
const unsigned int CMD_ElectionUserMegaphone = 51173;          // 选举一个用户的喊话内容
const unsigned int CMD_GetUserMegaphoneInfo = 51174;          // 获取用户的喊话内容

// 虚拟形象plus 51201 ~ 51300
const unsigned int CMD_CheckUserVirtualImageEntrance = 51201;        // 检查用户虚拟形象入口
const unsigned int CMD_BatchGetUserVirtualImageInuse = 51202;        // 批量获取用户使用中的虚拟形象
const unsigned int CMD_GetUserVirtualImageDisplay = 51203;           // 获取用户正在使用的虚拟形象
const unsigned int CMD_SetUserVirtualImageOrientation = 51204;       // 设置用户使用的虚拟形象朝向
const unsigned int CMD_GetVirtualImageCommodityRedDot = 51205; // 获取虚拟形象商品红点
const unsigned int CMD_GetUserVirtualImagePose = 51206; // 获取用户的虚拟形象姿势组件
const unsigned int CMD_GetVirtualImageCommodityRedDotDetail = 51207; // 获取虚拟形象商品红点详情
const unsigned int CMD_GetVirtualImageCardCommonCfg = 51208; // 获取虚拟形象无限卡公共配置
const unsigned int CMD_GetVirtualImageCardEntryStatus = 51209; // 获取虚拟形象无限卡入口信息
const unsigned int CMD_SetVirtualImagePoseType = 51210;        // 设置用户使用的虚拟形象姿势
const unsigned int CMD_GetVirtualImageBeginnerGuide = 51211;   // 获取虚拟形象新手引导
const unsigned int CMD_MarkVirtualImageBeginnerGuideDone = 51212;   // 标记新手引导完成




// 房间内第三方小游戏 51301 ~ 51310
const unsigned int CMD_GetExtGameInfoList = 51301;   // 获取第三方小游戏离线包信息（登陆后请求）
const unsigned int CMD_GetExtGameAccessList = 51302;  // 获取房间内第三方游戏入口
const unsigned int CMD_GetUserExtGameOpenId = 51303;   // 获取第三方小游戏openId
const unsigned int CMD_GetUserExtGameJsCode = 51304;   // 获取第三方小游戏授权JsCode
const unsigned int CMD_GetExtGameWhiteChannel = 51305; // 获取随机的白名单房间
const unsigned int CMD_CheckExtGameChannel = 51306;    // 判断房间是否可参与游戏
const unsigned int CMD_ExtGameLoginCheck = 51307;      // 第三方小游戏入口点击检查

// 婚礼房 51311 ~ 51350
const unsigned int CMD_GetWeddingInfo = 51311; // 获取婚礼房信息
const unsigned int CMD_SwitchWeddingStage = 51312; // 切换婚礼阶段
const unsigned int CMD_TakeWeddingGroupPhoto = 51313; // 拍婚礼合照

// ------ 抢椅子 -------
// 玩家侧
const unsigned int CMD_ApplyToJoinChairGame = 51314;      // 申请参加/取消申请
const unsigned int CMD_GetChairGameApplyList = 51315; // 获取报名列表
const unsigned int CMD_GetChairGameInfo = 51316;          // 获取游戏信息（进程、奖励、参与人员）
const unsigned int CMD_GrabChair = 51317;                 // 玩家抢椅子
// 主持侧
const unsigned int CMD_StartChairGame = 51318;            // 开启新一局抢椅子游戏
const unsigned int CMD_SetChairGameToNextRound = 51319;   // 进入下一轮
const unsigned int CMD_StartGrabChair = 51320;            // 本轮开抢

const unsigned int CMD_GetUserWeddingPose = 51321; // 获取用户的婚礼姿势组件
const unsigned int CMD_SetUserInuseWeddingPose = 51322; // 设置用户使用的婚礼姿势组件
const unsigned int CMD_BatchGetUserInuseWeddingPose = 51323; // 批量获取用户使用中的婚礼姿势组件

const unsigned int CMD_GetWeddingGroupPhotoSeatMap = 51324; // 获取房间合照麦位位置映射请求
const unsigned int CMD_SetUserWeddingGroupPhotoSeat = 51325; // 设置用户合照位置

const unsigned int CMD_SetChairGameReward = 51326; // 新人设置抢椅子游戏奖励

const unsigned int CMD_BatchGetUserWeddingClothes = 51327; // 婚礼房批量获取用户服装信息请求

const unsigned int CMD_GetChairGameRewardSetting = 51328; // 获取抢椅子游戏奖励设置

const unsigned int CMD_SetUserWeddingOrientation = 51329; // 设置用户使用的婚礼形象朝向
const unsigned int CMD_GetWeddingSchedulePageInfo = 51330; // 获取婚礼日程页信息
const unsigned int CMD_BuyWedding = 51331; // 购买婚礼
const unsigned int CMD_CancelWedding = 51332; // 取消婚礼
const unsigned int CMD_GetWeddingHallList = 51333; // 获取仪式大厅列表
const unsigned int CMD_SubscribeWedding = 51334; // 订阅婚礼
const unsigned int CMD_GetWeddingEntrySwitch = 51335; // 获取婚礼房入口开关
const unsigned int CMD_GetWeddingBigScreen = 51336; // 获取婚礼大屏
const unsigned int CMD_SaveWeddingBigScreen = 51337; // 保存婚礼大屏
const unsigned int CMD_GetWeddingInviteInfo = 51338; // 获取婚礼邀请信息
const unsigned int CMD_HandleWeddingInvite = 51339; // 处理婚礼邀请
const unsigned int CMD_ApplyEndWeddingRelationship = 51340; // 申请结束婚礼关系
const unsigned int CMD_CancelEndWeddingRelationship = 51341; // 取消申请结束婚礼关系
const unsigned int CMD_DirectEndWeddingRelationship = 51342; // 直接结束婚礼关系
const unsigned int CMD_GetProposeList = 51343; // 获取求婚用户列表
const unsigned int CMD_SendPropose = 51344; //  发送求婚请求
const unsigned int CMD_HandlePropose = 51345; // 处理求婚请求
const unsigned int CMD_GetProposeById = 51346; // 根据求婚ID获取求婚信息
const unsigned int CMD_GetSendPropose = 51347; // 获取发送or接受到求婚信息

const unsigned int CMD_GetUserWeddingPrecipitation = 51348; // 获取我的婚礼沉淀信息请求
const unsigned int CMD_ReportWeddingScenePic = 51349;     // 上报婚礼场景片段图片
const unsigned int CMD_HideWeddingRelation = 51350;       // 手动隐藏婚礼关系

// 货币中台 51351 ~ 51450
const unsigned int CMD_GetBalance = 51351;                // 获取用户T豆余额
const unsigned int CMD_CreateInAppOrder = 51352;          // 安卓半屏充值
const unsigned int CMD_GetInAppGoodsList = 51353;         // 半屏充值获取商品(明文)
const unsigned int CMD_GetUserAccumulateRecharge = 51354; // 查询用户累计充值总金额
const unsigned int CMD_GetAppstoreProduct = 51355;        // 获取苹果商品信息
const unsigned int CMD_GetAppAccountToken = 51356;        // 获取苹果token
const unsigned int CMD_GetRechargeRecord = 51357;         // 交易明细页
const unsigned int CMD_FaceResult = 51358;                // 人脸认证结果上报
const unsigned int CMD_CreateInIosAppOrder = 51359;       // 苹果充值
const unsigned int CMD_GetIosVoldemortedVersion = 51360;       // Voldemorted
const unsigned int CMD_GetFirstRecharge = 51361;          // 获取首充信息
const unsigned int CMD_ProveBlackUser = 51362;            // 校验黑名单用户的限制行为
const unsigned int CMD_QueryBlackUser = 51363;            // 查询黑名单用户
const unsigned int CMD_ConsumePrepare = 51364;            // 消费预冻结
const unsigned int CMD_ConsumeTBean = 51365;              // 消费T豆（直接消费）
const unsigned int CMD_ConsumeNotify = 51366;             // 预冻结T豆订单回调
const unsigned int CMD_GetTransferBalance = 51367;     // 获取转账记录余额

const unsigned int CMD_GetCoinAgreement = 51380;    //获取用户的货币协议签署情况

// 婚礼房 51460 ~ 51499
const unsigned int CMD_GetWeddingPreviewResource = 51460; // 获取婚礼预览资源
const unsigned int CMD_GetWeddingHighLightPresent = 51461;
const unsigned int CMD_GetWeddingRankEntry = 51462;  // 获取房间婚礼榜单入口信息，仅婚礼模板下请求
const unsigned int CMD_SendWeddingReservePresent = 51463;  //发送付费预约礼物
const unsigned int CMD_GetWeddingThemeCfgList = 51464;  //获取婚礼主题列表请求
const unsigned int CMD_RevokePropose = 51465; // 撤销求婚
const unsigned int CMD_GetUserInRoomStatus = 51466; // 批量查询用户的在房状态
const unsigned int CMD_GetGoingWeddingEntry = 51467; // 获取进行中的婚礼列表
const unsigned int CMD_RemindUserJoinWeddingRoom = 51468; // 提醒用户进房

// 广告中心
const unsigned int CMD_BatchGetAdCenter = 88888;             // 获取广告信息

// 上报服务
const unsigned int CMD_ReportPlayroomEvent = 90000; // 上报在房事件
const unsigned int CMD_RecordNewUserTimeCount = 90001; // 上报新用户统计的时间
const unsigned int CMD_RecordMicStatus = 90002; // 上报开闭麦事件


//udesk api的对接
const unsigned int CMD_GetUdeskUnReadMsg = 95000; // 获取最后未读推送消息时间戳
const unsigned int CMD_CheckVipKefuAccess = 95001; // 检查vip客服入口权限
const unsigned int CMD_AckVipKefuAccess = 95002; // 确认收到vip客服入口可见
const unsigned int CMD_EnterVipKefu = 95003; // 进入vip客服

// 统一风控 95100 ~ 95200
const unsigned int CMD_GetChannelMicSilentUser = 95100; // 3.1 获取房间中的最新静默列表

//创建公会/群组限制
const unsigned int CMD_CreateGuildLimit = 96000; // 创建公会限制


//心愿单
const unsigned int CMD_GetWishList = 96010; //主播自己和其他用户，根据主播UID，channelID获取心愿单列表
const unsigned int CMD_SetWishList = 96011; //主播设置心愿单


//动态头像
const unsigned int CMD_GetHeadDynamicImagePrivilege = 96015; // 查询是否有动态头像权限
const unsigned int CMD_UploadHeadDynamicImage = 96016; // 上传动态头像
const unsigned int CMD_BatchGetHeadDynamicImageMd5 = 96017; // 获取动态头像md5

// 拍卖房
const unsigned int CMD_GetApplyList = 96030; // 获取报名队列
const unsigned int CMD_UserApply = 96031; // 用户报名
const unsigned int CMD_UserCancelApply = 96032; // 用户取消报名
const unsigned int CMD_GetCurOfferingGameInfo = 96033; // 获取当前游戏信息
const unsigned int CMD_GetOfferingConfig = 96034; // 获取拍卖配置
const unsigned int CMD_SubmitOfferingSetting = 96035; // 提交拍卖设置
const unsigned int CMD_NamePriceOnce = 96036; // 出价
const unsigned int CMD_NamePriceMax = 96037; // 一键定拍
const unsigned int CMD_OfferingSet = 96038; // 定拍
const unsigned int CMD_OfferingPass = 96039; // 流拍
const unsigned int CMD_OfferingEnd = 96040; // 结束
const unsigned int CMD_OfferRoomOfferingRelationships = 96041; // 获取关系列表
const unsigned int CMD_OfferRoomDeleteRelationship = 96042; // 删除关系
const unsigned int CMD_OfferRoomOfferingInit = 96043;   // 初始化游戏
const unsigned int CMD_OfferRoomCardInfo = 96044;    // 资料卡关系信息
const unsigned int CMD_OfferRoomNameplateConfig = 96045; // 获取拍卖铭牌资源配置
const unsigned int CMD_OfferRoomGetUserNameplateInfo = 96046; // 获取用户铭牌信息

// 谜境剧本内容标签
const unsigned int CMD_GetScenarioContentLabelList = 96101; // 获取剧本内容标签列表

// !!!!!!!!! 前面的命令号还有很多，不要用100000以上的号段 ！！！

//活动大房 100001-101000 【说了不要用100000以上的号段】
const unsigned int CMD_SuperChannelEnter = 100001;      //活动大房进房
const unsigned int CMD_SuperChannelQuit = 100002;       //活动大房退房
const unsigned int CMD_SuperChannelHoldMic = 100003; // 活动大房上麦
const unsigned int CMD_SuperChannelReleaseMic = 100004; // 活动大房下麦
const unsigned int CMD_SuperChannelSetMicStatus = 100005; // 活动大房设置麦位状态
const unsigned int CMD_SuperChannelSetMicMode = 100006; // 活动大房设置麦位模式
const unsigned int CMD_SuperChannelGetMicList = 100007; // 活动大房获取麦位列表
const unsigned int CMD_SuperChannelGetMemberList = 100008; // 活动大房获取房间成员
const unsigned int CMD_SuperChannelGetExtInfo = 100009; // 活动大房获取房间扩展信息
// !!!!!!!!! 前面的命令号还有很多，不要用100000以上的号段 ！！！

//100010灰度机器人专用！不可使用！已经在后面占用了
//const unsigned int ROBOT_CMD_AUTH = 100010; // 机器人登录

const unsigned int CMD_SuperChannelSendHoldMicInvite = 100015; // 活动大房邀请上麦
const unsigned int CMD_SuperChannelReplyHoldMicInvite = 100011; // 活动大房回应邀请上麦
const unsigned int CMD_SuperChannelChangeMic = 100012; // 活动大房换麦
const unsigned int CMD_SuperChannelGetChannelList = 100013; // 活动大房获取
const unsigned int CMD_CplSearch = 100014;                     // cpl搜索
const unsigned int CMD_GetSuperChannelSchemeInfo = 100016;   //获取cpl玩法信息
// !!!!!!!!! 前面的命令号还有很多，不要用100000以上的号段 ！！！

//预约开黑 102001-102050 【混蛋啊+4 说了不要用100000以上的号段】
const unsigned int CMD_ChannelGuideGetGangUpConf  = 102001; // 获取预约开黑配置
const unsigned int CMD_ChannelGuideAppoinment     = 102002; // 預約
const unsigned int CMD_ChannelGuideJoinCarTeam    = 102003; // 加入车队
const unsigned int CMD_ChannelGuideAppStatus      = 102004; // 上报预约用户的app状态
const unsigned int CMD_ChannelGuideCloseGameGroup = 102005; // 关闭游戏群组引导
// !!!!!!!!! 前面的命令号还有很多，不要用100000以上的号段 ！！！

//召集令 102051-102070 【混蛋啊+4 说了不要用100000以上的号段】
const unsigned int CMD_CreateTelCallGroup        = 102051; // 创建电话组
const unsigned int CMD_GetTelGroupNotifyMsg      = 102052; // 获取通知消息
const unsigned int CMD_JoinTelCallGroup          = 102053; // 加入电话组
const unsigned int CMD_GetTelCallGroupUsers      = 102054; // 获取电话组内人员
const unsigned int CMD_StartCall                 = 102055; // 开启
const unsigned int CMD_GetTelCallGroupUserStatus = 102056; // 获取电话组内人员状态
const unsigned int CMD_GetTelCallGroup           = 102057; // 获取闪电令电话组基本信息
const unsigned int CMD_GetTelCallGroupVerifyCode = 102058; // 获取短信验证码
const unsigned int CMD_GetTelCallGroupAllUsers   = 102059; // 房间全量uid列表
const unsigned int CMD_HandleTelGroupNotifyMsg   = 102060; // 处理申请消息
const unsigned int CMD_BatchRemoveUsers          = 102061; // 批量删除
const unsigned int CMD_GetTelCallGroupUser       = 102062; // 获取房间内用户角色列表
const unsigned int CMD_GetEnterRoomInfo          = 102063; // 获取进房弹窗数据
// !!!!!!!!! 前面的命令号还有很多，不要用100000以上的号段 ！！！

//新版群相关
//群公告【混蛋啊+4 说了不要用100000以上的号段】
const unsigned int CMD_GetGroupAnnouncementList      = 103001; // 获取群公告列表
const unsigned int CMD_PrepareApplyGroupAnnouncement = 103002; // 预提交更新群公告 异步完成审核
const unsigned int CMD_DelGroupAnnouncement          = 103003; // 删除群公告
const unsigned int CMD_CheckGroupAnnouncement        = 103004; // 管理员查看上次更新群公告的状态
const unsigned int CMD_GetGroupAnnouncementByID      = 103005; // 按id获取详情
const unsigned int CMD_GetGroupAnnouncementEditAuth  = 103006; // 获取文字链编辑权限
// !!!!!!!!! 前面的命令号还有很多，不要用100000以上的号段 ！！！

// 用户铭牌 103060 - 103070【混蛋啊+5 说了不要用100000以上的号段】
const unsigned int CMD_GET_USER_NAMEPLATE = 103060;
// !!!!!!!!! 前面的命令号还有很多，不要用100000以上的号段 ！！！

// 剧本杀  104001-104050【混蛋啊+5 说了不要用100000以上的号段】
const unsigned int CMD_ChannelRoleplayGetChannelHoldMicUserRoleList = 104001; // 获取麦上用户角色
const unsigned int CMD_ChannelRoleplaySetMyChannelRole              = 104002; // 修改自己的房间内角色
const unsigned int CMD_ChannelRoleplayEnterBox                      = 104003; // 进包厢
const unsigned int CMD_ChannelRoleplayExitBox                       = 104004; // 进包厢
const unsigned int CMD_ChannelRoleplayHandleApplyBox                = 104005; // 处理申请进包厢
const unsigned int CMD_ChannelRoleplayGetBoxInfo                    = 104006; // 获取房间包厢信息
const unsigned int CMD_ChannelRoleplayGetBoxUserInfoByLimit         = 104007; // 获取房间某包厢用户信息
const unsigned int CMD_ChannelRoleplayGetChannelUserRoleList        = 104008; // 获取房间内用户角色列表
// !!!!!!!!! 前面的命令号还有很多，不要用100000以上的号段 ！！！

/** CMD_LIST END **/ // don't remove this
/////////////////////////////////////////////


//压测专用命令号  999991 - 999999
const unsigned int CMD_PRESSURE_TEST_1 = 999991;
const unsigned int CMD_PRESSURE_TEST_2 = 999992;

inline bool isProxyCmd(unsigned int cmd) {
    return  (cmd == CMD_PING || cmd == CMD_QUIT || cmd == CMD_SessionKeepAlive);
}

inline bool isSvrPushCmd(unsigned int cmd) {
    return (cmd == CMD_NOTIFY || cmd == CMD_KICKOUT || cmd == CMD_PUSH || cmd == CMD_TransmissionPush);
}

namespace protocol {

    const char* GetCmdName(unsigned int cmd);
    unsigned int GetCmdCode(const char* cmd);

}
