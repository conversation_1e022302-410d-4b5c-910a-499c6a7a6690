syntax = "proto3";

/***************财神降临logic*****************/

package ga.wealth_god_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/wealth_god_logic";

enum WealthGodBoxType {
  WEALTH_GOD_BOX_TYPE_UNSPECIFIED = 0;
  WEALTH_GOD_BOX_TYPE_S = 1;
  WEALTH_GOD_BOX_TYPE_A_PLUS = 2;
  WEALTH_GOD_BOX_TYPE_A = 3;
}

message WealthGod {
  string god_id = 1; // 财神ID，用于唯一标识场次
  uint32 channel_id = 2; // 哪个房间触发的
  int64 start_ts = 3; // 开始时间戳，单位秒
  int64 end_ts = 4; // 结束时间戳，单位秒
  int64 mission_end_ts = 5; // 任务结束时间戳，单位秒
}

message WealthGodBox {
  uint32 box_type = 1; // 宝箱类型，see WealthGodBoxType
  repeated WealthGodBoxRewardPreview reward_list = 2; // 奖励列表
}

message GetWealthGodEntryRequest {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
}

message GetWealthGodEntryResponse {
  ga.BaseResp base_resp = 1;
  repeated WealthGod current_channel_god_list = 2;
  int64 last_god_end_ts = 3; // 全网最后一个财神结束时间戳，单位秒
  bool activity_is_open = 4; // 活动是否开启
}

// 获取一个财神降临房间
message GetOneWealthGodChannelReq {
  ga.BaseReq base_req = 1;
}

message GetOneWealthGodChannelResp {
  ga.BaseResp base_resp = 1;
  uint32 channel_id = 2; // 房间ID
}

message GetWealthGodActivityInfoRequest {
  ga.BaseReq base_req = 1;
}

message GetWealthGodActivityInfoResponse {
  message WealthGodTrigger {
    uint32 trigger_type = 1; // 1-礼物 2-帝王套
    uint32 trigger_id = 2; // 对应类型的id，如礼物id，客户端根据id去取对应属性来展示
  }
  ga.BaseResp base_resp = 1;
  repeated WealthGodTrigger trigger_list = 2; // 可以触发的礼物列表
  string playing_instruction = 3; // 玩法说明
  string god_box_title = 4; // 财神宝箱标题
  string god_box_desc = 5; // 财神宝箱描述
  string god_box_icon = 6; // 财神宝箱图标
}

message GetWealthGodDetailRequest {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  string god_id = 3;
}

message GetWealthGodDetailResponse {
  ga.BaseResp base_resp = 1;
  WealthGod god_info = 2; // 财神信息
  WealthGodBox box_info = 3; // 宝箱信息，仅在开奖前会返回
  uint32 available_open_cnt = 4; // 可开奖次数
}

message WealthGodBoxRewardPreview {
  bool is_rare = 1; // 是否稀有
  string title = 2; // 标题
  string sub_title = 3; // 副标题
  string icon_url = 4; // 图标
  string corner_text = 5; // 角标文案
}

message OpenWealthGodBoxRewardRequest {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  string god_id = 3;
}

message OpenWealthGodBoxRewardResponse {
  ga.BaseResp base_resp = 1;
  repeated WealthGodBoxRewardPreview reward_list = 2; // 有多次抽奖机会的话，1次点击抽奖，全部返回
}

message ReportStayRoomMissionFinishRequest {
  ga.BaseReq base_req = 1;
  string god_id = 2;
}

message ReportStayRoomMissionFinishResponse {
  ga.BaseResp base_resp = 1;
}

// 新生成财神通知
message NewWealthGodNotify {
  WealthGod god_info = 1;
  // god_info.mission_end_ts > 本地last_god_end_ts，则更新本地last_god_end_ts
}

// 房间新生成财神宝箱通知
message ChannelNewWealthGodBoxNotify {
  WealthGod god_info = 1; // 财神信息
  WealthGodBox box_info = 2; // 宝箱信息
}

message WealthEntryIcon {
  string bottom_entry_icon = 1; // 底部入口图标
  string dynamic_widget_entry_icon = 2; // 动态挂件入口图标
}

enum WealthMissionType {
  WEALTH_MISSION_TYPE_UNSPECIFIED = 0; // 未知类型
  WEALTH_MISSION_TYPE_STAY_IN_GAME = 1; // 在游戏中停留
  WEALTH_MISSION_TYPE_SEND_TBEAN_GIFT = 2; // 发送T豆礼物
}

// 任务完成时通知
message WealthMissionFinishNotify {
  uint32 available_open_cnt = 1; // 当前可开奖次数
  map<uint32, uint32> mission_current_cnt_map = 2; // 任务完成情况 <WealthMissionType, current_cnt>  客户端透传这个map给web端
  string god_id = 3; // 避免财神对不上
}

// 自动开启财神宝箱奖励结果通知
message AutoOpenWealthGodBoxRewardResultNotify {
  repeated WealthGodBoxRewardPreview reward_list = 1; // 奖励列表
  uint32 channel_id = 2; // 用于区分是在哪个房间触发的
}

// 登录时调用
message GetWealthGodCommonCfgRequest {
  ga.BaseReq base_req = 1;
}


// 通用动画结构
message WealthGodCommonAnimation {
  string animation_url = 1; // 开宝箱动画地址
  string animation_md5 = 2; // 开宝箱动画md5
}

message GetWealthGodCommonCfgResponse {
  ga.BaseResp base_resp = 1;
  ga.RushInfo rush_info = 2; // 财神降临活动的rush信息
  WealthEntryIcon normal_entry_icon = 3; // 普通入口图标(没财神)
  WealthEntryIcon curr_room_god_entry_icon = 4; // 本房间有财神入口图标(有财神)
  WealthEntryIcon other_room_god_entry_icon = 5; // 其他房间有财神入口图标
  map<uint32, WealthGodCommonAnimation> box_open_animation_map = 6; // 宝箱开启动画 <WealthGodBoxType, WealthBoxOpenAnimation>
  uint32 fixed_entry_show_duration = 7; // 固定入口展示时长，单位秒
  WealthEntryIcon doing_mission_entry_icon = 8; // 任务进行中入口图标
  uint32 box_reward_window_show_duration = 9; // 宝箱奖励弹窗展示时长，单位秒
  uint32 need_stay_room_second = 10; // 需要在房间停留的秒数【在房任务】
  WealthGodCommonAnimation box_count_down_animation = 11; // 宝箱倒计时动画
  WealthGodCommonAnimation god_show_animation = 12; // 财神降临动画
  string god_end_picture_url = 13; // 财神降临结束切图
}

message NotifyWealthActivityStart {
}