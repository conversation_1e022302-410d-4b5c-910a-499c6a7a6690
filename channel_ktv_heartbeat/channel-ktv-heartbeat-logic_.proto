syntax = "proto3";

package ga.channel_ktv_heartbeat;
import "ga_base.proto";
import "channel_ktv/channel-ktv-logic_.proto";
import "concert_logic/concert-logic_.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel-ktv-heartbeat";

//上报心跳
message ReportKTVHeartbeatReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}

message ReportKTVHeartbeatResp{
    ga.BaseResp base_resp = 1;
    uint32 report_duration = 2;
}

message ConfirmKTVHeartBeatReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  string song_id = 3;
  uint32 game_id = 4;
}

message ConfirmKTVHeartBeatResp {
  ga.BaseResp base_resp = 1;
  ga.channel_ktv.ChannelKTVUpdate info = 2;
  ga.concert_logic.SingingInfo singing_info = 3;
}
