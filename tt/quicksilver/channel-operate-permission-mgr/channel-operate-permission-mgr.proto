syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-operate-permission-mgr";
package channel_operate_permission_mgr;

import "tt/quicksilver/extension/options/options.proto";

enum ChannelOperate {
    CHANNEL_OPERATE_UNKNOWN = 0 ;
    CHANNEL_OPERATE_ENTER = 1 ;                    //进房
    CHANNEL_OPERATE_ENTER_SKIP_PASSWD_CHECK = 2 ;  //免密进房
    CHANNEL_OPERATE_HOLD_CHAIR_MIC = 3;            //上主持麦
    CHANNEL_OPERATE_CHANGE_CHAIR_MIC = 4;          //换到主持麦
    CHANNEL_OPERATE_TAKE_MIC         = 5;          //抱麦
    CHANNEL_OPERATE_SET_MIC_NAME     = 6;          //设置麦位名称
    CHANNEL_OPERATE_SET_MIC_SWITCH   = 7;          //设置麦位名称开关
    CHANNEL_OPERATE_SKIP_LOCK_SCREEN_CHECK = 8;    //锁公屏时发公屏

    MUTE = 9;                      //禁言
    UNMUTE = 10;                   //解除禁言
    SET_CHANNEL_ENTER_CONTROL_TYPE = 11 ; //设置进房控制类型:白名单，密码等
    GET_CHANNEL_ENTER_CONTROL_TYPE = 12 ; //获取进房控制类型:白名单，密码等
    SET_MIC_STATUS = 13;            //设置麦位状态
    KICK_MIC = 14;                  //踢麦
    SET_PERFORMANCE = 15 ;          //设置房间节目单
    SET_CUR_BACKGROUND = 16;        //设置房间背景
    START_CHANNEL_GAME_DRAW = 17;   //开始房间抽签小游戏
    TAKE_CHANGE_MIC = 18;           //把麦上用户到抱到另外一个麦上
    MODIFY_CHANNEL_NAME = 19;       //修改房间名称
    SWITCH_MIC_LAYOUT = 20; //切换麦位布局
    HOLD_MIC = 21; //上麦
    CHANGE_MIC = 22; //换麦
    QUEUE_MIC = 23; //排麦
    modify_channel_icon = 24; //修改房间头像
    modify_channel_extend = 25; //修改其他房间扩展信息：包括改主题和描述、欢迎语以及各种开关
}

enum BusinessId {
    BUSINESS_ID_UNKNOWN = 0;
    BUSINESS_ID_CHANNEL_ADMIN = 1;          //房间管理员
    BUSINESS_ID_GUILD_ADMIN = 2;            //公会房间管理员
    BUSINESS_ID_MELEE_WHITE_LIST = 3;       //团战白名单
    BUSINESS_ID_CHANNEL_RADIO_LIVE = 4;     //直播房业务
    BUSINESS_ID_CHANNEL_SUPER_ADMIN = 5;    //房间超级管理员

    CHANNEL_ADMIN_OPERATE_TARGET_USER = 6;      //房间管理员操作者能否操作target user
    UKW_CHANNEL_ADMIN_OPERATE_TARGET_USER = 7;  //神秘人体系房间下，房间管理员操作者能否操作target user(target user可能为神秘人)

    NO_CHANNEL_ADMIN_AND_FORBIDDEN_OP = 8; //没有房间管理员且禁止操作
}

service ChannelOperatePermissionMgr {
    option (service.options.service_ext) = {
        service_name: "channel-operate-permission-mgr"
    };

    //操作方调用
    rpc CheckOperatePermission (CheckOperatePermissionReq) returns (CheckOperatePermissionResp) {}


    //业务方调用,设置匹配规则
    /*
    rpc AddBusinessRuleKey(AddBusinessRuleKeyReq) returns (AddBusinessRuleKeyResp) {}
    rpc DelBusinessRuleKey(DelBusinessRuleKeyReq) returns (DelBusinessRuleKeyResp) {}*/

    //全量覆盖SchemeId
    rpc SetBusinessRuleSchemeId(SetBusinessRuleSchemeIdReq) returns (SetBusinessRuleSchemeIdResp) {}

    //查询规则信息
    rpc GetPermissionRuleInfoByBusinessId(GetPermissionRuleInfoByBusinessIdReq) returns (GetPermissionRuleInfoByBusinessIdResp) {}
    rpc GetPermissionRuleInfoByOpt(GetPermissionRuleInfoByOptReq) returns (GetPermissionRuleInfoByOptResp) {}
}

message CheckOperatePermissionReq {
    ChannelOperate opt = 1;          //必填
    uint32 opt_uid = 2;              //必填
    uint32 cid = 3;                  //必填
    uint32 scheme_id = 4;
    uint32 scheme_detail_type = 5;
    uint32 channel_bind_id = 6;
    uint32 channel_type = 7;
    uint32 target_uid = 8;
}

message CheckOperatePermissionResp {
    int32 permission_err_code = 1;  //如果有操作权限，permission_err_code；如果没有操作权限，则以permission_err_code作为错误吗返回给客户端
    string permission_err_msg = 2;   //如果没有操作权限且该值不为空，以该值作为toast,否则用permission_err_code对应的msg做为toast返回给客户端
}

/*
enum RuleKeyType {
    RULE_KEY_TYPE_UNKNOWN = 0;
    RULE_KEY_TYPE_CID = 1;
    RULE_KEY_TYPE_BIND_ID = 2; //有bindid，则必须有channeltype,而且channeltype只能有一个值，因为不同channeltype的bindId表示的含义不同
    RULE_KEY_TYPE_SCHEME_ID = 3;
    //channel_type,scheme_detail_type通过配置就可以了，这两个都是枚举值，值有限
}
//增量
message AddBusinessRuleKeyReq{
    BusinessId business = 1;
    RuleKeyType key_type = 2;
    repeated uint32 value_list = 3;
}
message AddBusinessRuleKeyResp{
}
//删除
message DelBusinessRuleKeyReq{
    BusinessId business = 1;
    RuleKeyType key_type = 2;
    repeated uint32 value_list = 3;
}
message DelBusinessRuleKeyResp{
}*/

//全量覆盖
message SetBusinessRuleSchemeIdReq{
    BusinessId business = 1;
    repeated uint32 scheme_id_list = 3;
    string rule_name = 4;
}
message SetBusinessRuleSchemeIdResp{
}

//查询规则信息
message OperateInfo {
    ChannelOperate opt = 1;
    string opt_name = 2;
    string comment = 3;
    repeated string forbidden_rule_group_list = 4;
}
message RuleDegradePolicy {
    bool permit_call_failed = 1;
    uint32 short_time_out_ms = 2;
}
message RuleMatchInfo {
    repeated uint32  channel_type_list = 1;
    repeated uint32  cid_list = 2;
    repeated uint32  channel_bindid_list = 3;
    repeated uint32  scheme_detail_type_list = 4;
    repeated uint32  scheme_id_list = 5;
}
message BusinessRule {
    string rule_name = 1;
    string comment = 2;
    repeated OperateInfo opt_list = 3;
    RuleMatchInfo rule_info = 4;
    RuleMatchInfo redis_rule_info = 5;
}
message PermissionRuleInfo {
    BusinessId business = 1;
    string business_name = 2 ;
    string comment = 3 ;
    string permission_check_type = 4;
    string server_name = 5;
    RuleDegradePolicy degrade_policy = 6;
    string permission_rule_group = 7;
    repeated BusinessRule rule_info = 8;
}
message GetPermissionRuleInfoByBusinessIdReq {
    BusinessId business_id = 1;
}
message GetPermissionRuleInfoByBusinessIdResp {
    PermissionRuleInfo info = 1;
}
//查询某个操作应用了哪些规则
message GetPermissionRuleInfoByOptReq {
    ChannelOperate opt = 1;
}
message GetPermissionRuleInfoByOptResp {
    repeated PermissionRuleInfo info_list = 1;
}