syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/music-nest";
package music_nest;

enum LevelType
{
    UNKNOW_LEVEL_TYPE=0;
    SUPER_LEVEL = 1;
    A_LEVEL = 2;
    B_LEVEL = 3;
    C_LEVEL = 4;
}

enum LiveStatus
{
    UNKNOWN_STATUS =0;       //已下架
    NOT_START_STATUS = 1;  //未开始
    WARN_UP_STATUS = 2;    //预热中
    LIVING_STATUS = 3;      //开播中
    CLOSED_STATUS = 4;      //已结束
}

enum MaterialType
{
    UNKNOW_MATERIAL_TYPE=0;
    WORD_TYPE = 1;
    IMAGE_TYPE = 2;
    VIDEO_TYPE = 3;
}

enum PatternType
{
    UNKNOW_PATTERN_TYPE = 0;
    CP_TYPE = 1;
    NORMAL_TYPE_1 = 2;
    NORMAL_TYPE_2 = 3;
    BATTLE_TYPE_1V1 = 4;
    BATTLE_TYPE_3V3 = 5;
    BATTLE_HOME_TYPE = 6;
}

service MusicNest {
    //运营平台 分类管理
    rpc CreateCategory (CreateCategoryReq) returns (CreateCategoryResp) {}
    rpc UpdateCategory (UpdateCategoryReq) returns (UpdateCategoryResp) {}
    rpc GetAllCategory(GetAllCategoryReq) returns (GetAllCategoryResp) {}
    rpc MoveCategory (MoveCategoryReq) returns (MoveCategoryResp) {}

    rpc InChannelWhiteList (InChannelWhiteListReq) returns (InChannelWhiteListResp) {}

    //运营平台活动类操作相关
    rpc CreatActivity (CreatActivityReq) returns (CreatActivityResp) {}
    rpc UpdateActivity (UpdateActivityReq) returns (UpdateActivityResp) {}
    rpc UpdateActivityTopic (UpdateActivityTopicReq) returns (UpdateActivityTopicResp) {}
    rpc UpdateMusicListInfo (UpdateMusicListInfoReq) returns (UpdateMusicListInfoResp) {}
    rpc UpdateActivityReview (UpdateActivityReviewReq) returns (UpdateActivityReviewResp) {}
    rpc GetActivityList (GetActivityListReq) returns (GetActivityListResp) {}

    // web我的乐窝
    rpc GetMyMusicNestList (GetMyMusicNestListReq) returns (GetMyMusicNestListResp) {}

    // web活动详情
    rpc GetMusicNestActivityInfo (GetMusicNestActivityInfoReq) returns (GetMusicNestActivityInfoResp) {}

    // 音乐板块的乐窝首页
    rpc GetMusicNestCoverAndLiveList (GetMusicNestCoverAndLiveListReq) returns (GetMusicNestCoverAndLiveListResp) {}

    // 乐窝主页
    rpc GetMusicNestHomePage (GetMusicNestHomePageReq) returns (GetMusicNestHomePageResp) {}

    // 订阅乐窝
    rpc SubMusicNest (SubMusicNestReq) returns (SubMusicNestResp) {}

    // 想作
    rpc SubMusicNestActivity (SubMusicNestActivityReq) returns (SubMusicNestActivityResp) {}

    // 节目清单接口
    rpc AddPerformance (AddPerformanceReq) returns (AddPerformanceResp) {
    }
    // 删除活动
    rpc StopPerformance (StopPerformanceReq) returns (StopPerformanceResp) {
    }
    // 获取此房间所有节目单
    rpc GetPerformance (GetPerformanceReq) returns (GetPerformanceResp) {
    }
    // 设置当前阶段
    rpc SetCurrentPerformanceStage (SetCurrentPerformanceStageReq) returns (SetCurrentPerformanceStageResp) {
    }
    // 获取节目单
    rpc GetPerformanceById (GetPerformanceByIdReq) returns (GetPerformanceByIdResp) {
    }

    /* 下一场引导消失 */
    rpc DisappearNextDirectionAct (DisappearNextDirectionActReq) returns (DisappearNextDirectionActResp) {
    }

    // 已看过获取
    rpc GetSpecifiedChannelVisitedSize (GetSpecifiedChannelVisitedSizeReq) returns (GetSpecifiedChannelVisitedSizeResp) {}

    //获取欢迎层弹层信息
    rpc GetWelcomePop(GetWelcomePopReq)returns (GetWelcomePopResp){}
    //用户点击选择弹层某一选项消息
    rpc UserClickPop(UserClickPopReq)returns (UserClickPopResp){}

    rpc IsMusicNestActChannel (IsMusicNestActChannelReq) returns (IsMusicNestActChannelResp) {}

    //// 进房后乐窝活动进行期间的信息获取 (和乐窝活动相关的汇总）
    rpc GetMusicNestLiveInfo(GetMusicNestLiveInfoReq)returns (GetMusicNestLiveInfoResp){}

    //随机获取指定个数的机器人头像
    rpc RandomGetRobotImage(RandomGetRobotImageReq)returns(RandomGetRobotImageResp){}

    /* 获取近期乐窝活动的加票停留信息 */
    rpc GetAllStayAddTicketActs(GetAllStayAddTicketActsReq)returns(GetAllStayAddTicketActsResp){}

}

/* 是否乐窝活动
- 前置条件
  - 针对部分房间
    - 在运营管理后台-乐窝活动，配置状态处于已上架的活动对应的房间channelID
  - 在指定时间
    - 根据该活动所配置的生效时间（预热~结束时间）范围内
*/
message IsMusicNestActChannelReq {
    uint32 channel_id = 1;
}
message IsMusicNestActChannelResp {
    bool is_in_act = 2;
}


message CategoryInfo {
    uint32 id = 1;
    string main_title = 2;
    string sub_title = 3;
    bool status = 4;
    string image_url = 5;
    uint32 index = 6; //用于分类排序
}

message GuestInfo {
    bool is_main = 1;
    uint32 uid = 2;
    uint32 mic_id = 3; //主一号如果不填，默认给一个最大值FFFFFFFF
    string nickname = 4; // 昵称
    string account = 5; // ttid
}

message GuestInfos {
    repeated GuestInfo guest_infos = 1;
}

message GuestInfoWithTTId{
    bool is_main = 1;
    uint32 uid = 2;
    uint32 mic_id = 3; //主一号如果不填，默认给一个最大值FFFFFFFF
    string nickname = 4; // 昵称
    string ttid = 5;
}

message GuestInfosWithTTId {
    repeated GuestInfoWithTTId guest_infos = 1;
}


message MusicInfo {
    string music_name = 1;
    string singer = 2;
    string music_url = 3;
}

message ReviewInfo {
    MaterialType review_type = 1;
    string name = 2;
    string review_url = 3;
}

message ActivityBaseInfo {
    string topic = 1;
    uint32 category_id = 2;
    LevelType level = 3;
    uint32 status = 4;      // 1:未上架 2：上架 3：下架
    bool is_recommend = 5;
    uint32 live_on_time = 6;
    uint32 live_off_time = 7;
    uint32 warn_up_time = 8;
    uint32 uid = 9;
    string introduction = 10;
    uint32 channel_id = 11;
    bool is_notify = 12;
    LiveStatus live_status = 13;
    string ttid = 14;
    BrandIntegralInfo  brand_integral_info=15;   //与活动相关联的厂牌信息
    string  operator   =16;   //操作人名称
}

message ActivityTopicInfo {
    string frame_color = 1;
    string short_cover_image = 2;
    string long_cover_image = 3;
    string category_image = 4;
    MaterialType material_type = 5;
    string activity_introduction = 6;
}

message CreateCategoryReq {
    string main_title = 1;
    string sub_title = 2;
    string image_url = 3;
}

message CreateCategoryResp {
}

message UpdateCategoryReq {
    uint32 id = 1;
    string main_title = 2;
    string sub_title = 3;
    bool status = 4; //false:下架 true:上架
    string image_url = 5;
}

message UpdateCategoryResp {
}

message GetAllCategoryReq {
}

message GetAllCategoryResp {
    repeated CategoryInfo category_infos = 1;
}

message MoveCategoryReq {
    uint32 id = 1;
    uint32 current_index = 2;
    uint32 target_index = 3;
}

message MoveCategoryResp {
}

message CreatActivityReq {
    ActivityBaseInfo activity_base_info = 1;
}

message CreatActivityResp {
    uint32 id = 1;
}

//message UpdateActivityReq {
//    uint32 activity_id = 1;
//    ActivityBaseInfo activity_base_info = 2;
//    ActivityTopicInfo music_activity_topic_info = 3;
//    PatternType pattern_type = 4;
//    repeated GuestInfos guest_infos_array = 5;
//    repeated MusicInfo music_infos = 6;
//    repeated ReviewInfo review_infos = 7;
//}
//
//message UpdateActivityResp {
//}

message UpdateActivityReq {
    uint32 activity_id = 1;
    ActivityBaseInfo activity_base_info = 2;
}

message UpdateActivityResp {
}
//实现设置，更新，获取欢迎弹层
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message WelcomePop{
    bool   startFlag= 1;
    string topic= 2; // 主题
    repeated string optionOne= 3; //选项
    string pictureUrl=4;   //头图
}
message UpdateActivityTopicReq {
    uint32 activity_id = 1;
    ActivityTopicInfo music_activity_topic_info = 2;
    PatternType pattern_type = 3;
    repeated GuestInfos guest_infos_array = 4;
    WelcomePop  welcome_pop=5;
    CokeStateInfo  coke_state_info=6;
    bool is_stay_add_ticket = 7; /* 是否开启停留加票奖励 */
    uint32 stay_interval = 8; /* 停留时长 加票 单位：分钟 */
    uint32 ticket_cnt = 9; /* 停留加票单次加的票数 */
    uint32 total_add_ticket_cnt = 10; /* 单场活动 单人最多加的票数上限 */

}

/* 获取近期乐窝活动的加票停留信息 */
message GetAllStayAddTicketActsReq{
}
message StayAddTicketActsInfo{
    uint32 activity_id = 1;
    uint32 status = 2;
    uint32 channel_id = 3;
    uint32 live_off_time = 4;
    uint32 warn_up_time = 5;
    bool is_stay_add_ticket = 6; /* 是否开启停留加票奖励 */
    uint32 stay_interval = 7; /* 停留时长 加票 单位：分钟 */
    uint32 ticket_cnt = 8; /* 停留加票单次加的票数 */
    uint32 total_add_ticket_cnt = 9; /* 单场活动 单人最多加的票数上限 */
}
message GetAllStayAddTicketActsResp{
   repeated StayAddTicketActsInfo info_list = 1;
}
enum BrandIntegralStatus{
    UnKnowStatus=0;
    AlreadyAddIntegral=1;
    NoAddIntegral=2;
}

message BrandIntegralInfo{
    repeated BrandInfo brand_info=1;
    int32 count=2;
    uint32 brand_integral_status=3;
}
message BrandInfo{
    string brand_id=1;
    string brand_name=2;
}
message UpdateActivityTopicResp {
}

message UpdateMusicListInfoReq {
    uint32 activity_id = 1;
    repeated MusicInfo music_infos = 2;
}

message UpdateMusicListInfoResp {
}

message UpdateActivityReviewReq {
    uint32 activity_id = 1;
    repeated ReviewInfo review_infos = 2;
}

message UpdateActivityReviewResp {
}

message GetActivityListReq {
    oneof request_type {
        uint32 activity_id = 1; // 活动id
        string topic_name = 2; // 主题名
        uint32 uid = 3;         // 主理人uid
        uint32 channel_id = 4;  // 房间id
    }

    uint32 category_id = 5;  // 0代表不限
    uint32 status = 6;         // 0代表不限
    uint32 live_on_start_time = 7; // 0 代表空
    uint32 live_on_end_time = 8;  // 0 代表空
    uint32 page = 9; // 从0开始
    uint32 limit = 10; // 一页最多多少
}

message GetActivityListResp {
    message AllActivityInfo {
        uint32 activity_id = 1;
        ActivityBaseInfo info = 2;
        ActivityTopicInfo music_activity_topic_info = 3;
        PatternType pattern_type = 4;
        repeated GuestInfosWithTTId guest_infos = 5;
        repeated MusicInfo music_infos = 6;
        repeated ReviewInfo review_infos = 7;
        WelcomePop  welcome_pop=8;
        CokeStateInfo coke_state_info=9;
        bool is_stay_add_ticket = 10; /* 是否开启停留加票奖励 */
        uint32 stay_interval = 11; /* 停留时长 加票 单位：分钟 */
        uint32 ticket_cnt = 12; /* 停留加票单次加的票数 */
        uint32 total_add_ticket_cnt = 13; /* 单场活动 单人最多加的票数上限 */
        BrandIntegralInfo brand_info=14;
    }
    repeated AllActivityInfo all_activity_infos = 1;
    uint32 page = 2;
    uint32 num_activities = 3;
    uint32 total_cnt = 4;
}

message GetMyMusicNestListReq {
    uint32     uid      = 1; // 用户id
    uint32     offset   = 2; // 分页偏移量
    uint32     limit    = 3; // 单页数量
    string     year_month = 4; // 月份 格式yyyyMM
}

message GetMyMusicNestListResp {
    uint32                       next       = 1;
    repeated MyMusicNestActivity activities = 2;
}

message MyMusicNestActivity {
    uint32 id           = 1; // 活动id
    string title        = 2; // 活动标题
    string image        = 3; // 活动图片
    uint32 start_time   = 4; // 活动开始时间
    uint32 end_time     = 5; // 活动结束时间
    uint32 warmup       = 6; // 提前预热
    uint32 participants = 7; // 参与人数
    string tkid         = 8; // TT线上票
    uint32 type         = 9; // 类型 1.已预约 2.看过
    uint32 status       = 10; // 活动状态, 见 LiveStatus
    uint32 channel_id   = 11; //房间id
    bool activity_time_change = 12; // 活动时间修改标志
}

message GetMusicNestActivityInfoReq {
    uint32     uid         = 1; // 用户id
    uint32     activity_id = 2; // 活动id
}

message GetMusicNestActivityInfoResp {
    uint32                           id            = 1;
    string                           title         = 2; // 标题
    string                           poster        = 3; // 海报
    uint32                           start_time    = 4; // 活动开始时间
    uint32                           end_time      = 5; // 活动结束时间
    uint32                           warmup        = 6; // 提前预热
    string                           topic_details = 7; // 主题详情
    repeated MusicInfo               songs         = 8; // 活动歌单
    repeated ReviewInfo              replays       = 9; // 活动回顾
    Director                         director      = 10; // 主理人
    repeated GuestInfo               guests        = 11; // 嘉宾
    PlayTogether                     play_together = 12; // 一起作
    uint32                           channel_id    = 13; // 房间id
    repeated SimpleMusicNestActivity top3_activity = 14; // 60天内前3人气场次
    uint32                           status        = 15; // 活动状态
    SimpleMusicNestActivity          recent_activity = 16; // 预告活动
    uint32                           channel_type  = 17; // 房间类型
    bool                             is_sub        = 18; // 是否关注
    uint32                           topic_type    = 19; // 详情类型 1.文字 2.海报
}

message Director {
    uint32 uid       = 1;
    string nickname  = 2; // 昵称
    string introduce = 3; // 简介
    bool   is_follow = 4; // 是否关注
    string account   = 5; // ttid
}

message SimpleMusicNestActivity {
    uint32 id        = 1; // 活动id
    string cover     = 2; // 封面
    string title     = 3; // 活动标题
    uint32 hold_time = 4; // 举办时间
    uint32 play_num  = 5; // 参与人数
    uint32 status    = 6; // 状态, 见LiveStatus
    uint32 channel_id = 7; // 房间id
    uint32 channel_type = 8; // 房间类型
}

message PlayTogether {
    repeated string accounts = 1; // 轮播头像
    uint32          play_num = 2; // 参与人数
}

message GetMusicNestCoverAndLiveListReq {
    uint32 uid = 1;
}

message GetMusicNestCoverAndLiveListResp {
    message CoverInfo {
        uint32 id = 1; // 活动id
        string short_cover_image = 2; //封面url 短图
    }
    message LiveInfo {
        message SongInfo {
            string song_name = 1;  //歌曲名称
        }
        uint32 id = 1; // 活动id
        string short_cover_image = 2; //封面url
        string category_title = 3; //分类标题
        string background_url = 4; //分类底图
        uint32 channel_id = 6;
        bool is_recommend = 8;  // 是否推荐场次
        string topic = 9; //活动主题
        uint32 live_on_time = 10;    //开播时间
        uint32 warn_up_time = 11;    // 预热时间
        repeated SongInfo song_infos = 12;  //歌名列表
        repeated GuestInfos guest_infos_array = 13; //嘉宾列表
        PatternType pattern_type = 14;  //嘉宾模式
        LevelType level_type = 15; // 活动评级
        bool is_sub_activity = 16; // 用户是否订阅活动
        string long_cover_image = 17; // 活动长图
        uint32 num_audience_joined = 18; // 多少人曾经一起作
        repeated string social_community_list = 19; // 合作社区列表
    }
    repeated CoverInfo cover_infos = 1;
    repeated LiveInfo live_infos = 2;
    string title = 4; /* 标题 可为空 */
    string jump_url = 5; /* 跳转链接 (h5) 可为空 */
}

message GetMusicNestHomePageReq {
    uint32 uid = 1;
}

message GetMusicNestHomePageResp {
    message ActivityInfo {
        uint32 id = 1; //活动id
        string topic = 2; // 活动主题
        uint32 channel_id = 3;
        uint32 num_audience = 4;
        LiveStatus live_status = 5;
        uint32 live_on_time = 6;
        string short_cover_image = 7;  //短图
        string long_cover_image = 8;   //长图
        string frame_color = 9;       //边框颜色
        bool is_recommend = 10;
        uint32 warn_up_time = 11;
        bool is_sub_activity = 12;  // 是否已订阅这场活动
    }
    bool is_sub_music_nest = 1; // 是否已进驻乐窝
    repeated ActivityInfo activity_infos = 2; //乐窝时间线内容
    message CategoryActivityInfo {
        string main_title = 1; //分类主标题
        string sub_title = 2; // 分类副标题
        repeated ActivityInfo activity_infos = 3;
    }
    repeated CategoryActivityInfo category_activity_infos = 3;  //乐窝分类内容
}

message SubMusicNestReq {
    uint32 uid = 1;
    bool is_sub = 2;
}

message SubMusicNestResp {
}

message SubMusicNestActivityReq {
    uint32 uid = 1;
    uint32 activity_id = 2;
}

message SubMusicNestActivityResp {

}

// 节目清单协议
enum StageType
{
    UNKNOWN_STAGE_TYPE = 0;
    SOLO = 1;
    BATTLE = 2;
    INTRODUCTION = 3;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message Guest {
    message UserId {
        uint32 uid = 1;
        string ttid = 2; // 新增节目单时，前端带过来
    }
    repeated UserId uidArray = 1;
    string introduction = 2;
    string pk_icon = 3; /* pk 图 */
}

message Stage {
    StageType stage_type = 1; // 环节标记
    uint32 id = 2; // 环节序号, 必须大于0

    string title = 3; // 环节标题
    string icon = 4; // 环节入口图
    string introduction = 5; // 纯介绍环节的文本介绍
    repeated Guest guest_list = 6; // 嘉宾列表
}

message Performance {
    string id = 13; // 唯一id
    uint32 channel_id = 1; // 房间标识
    int64 visible_begin_time = 2; // 活动可见开始时间
    int64 visible_end_time = 3; // 活动可见截止时间
    string title = 4;  // 节目标题
    string entry_icon = 5; // 节目单入口底图

    uint32 current_stage_id = 6; // 当前节目

    repeated Stage stage_list = 7; // 节目单

    int64 stage_update_time = 8; // 当前节目更新时间戳
    int64 performance_update_time = 9; // 节目单更新时间戳

    string maintainer = 10; // 维护者
    string owner_nick_name = 11; // 房主昵称 前端新增节目单时带上
    uint32 display_id = 12; // 房间的display id  前端带过来
    string channel_view_id=14;//原display_id
}

message AddPerformanceReq {
    Performance performance = 1;
}

message AddPerformanceResp {
    Performance performance = 1;
}

message StopPerformanceReq {
    uint32 channel_id = 1; // 房间标识
    string id = 2; // 活动唯一标识
}

message StopPerformanceResp {
}

message GetPerformanceReq {
    uint32 channel_id = 1; // 房间标识
    bool is_current = 2; // true 代表只获取当前房间正在进行的节目
}
message NextDirectionAct{
    uint32 channel_id = 1; /* 跳转 */
    uint32 id = 2;
    string title = 3; /* 标题 可为空 */
    string category_title = 4; //分类标题
    uint32 expire_time = 5; /* 消失的时间 s */
    string short_cover_image = 6; //封面url
}
enum MusicNestPerformanceType{
    INVALID_MUSIC_NEST_PERFORMANCE_TYPE = 0;
    NORMAL_PERFORMANCE = 1;
    NEXT_ACTIVITY = 2;
}
message GetPerformanceResp {
    repeated Performance performances = 1;
    uint32 performance_type = 3; /* MusicNestPerformanceType */
    NextDirectionAct next_act = 4; /* 下一场引导 */
}
message GetPerformanceByIdReq {
    string id = 1;
}

message GetPerformanceByIdResp {
    Performance performances = 1;
}

message SetCurrentPerformanceStageReq {
    uint32 channel_id = 1; // 房间标识
    uint32 current_stage_id = 2; // 当前节目
    string id = 3; // 节目唯一id
}

message SetCurrentPerformanceStageResp {
}

message DisappearNextDirectionActReq {
    uint32 channel_id = 1; // 房间标识
}
message DisappearNextDirectionActResp {
}

// 已看过
message VisitedInfo {
    uint32 visited_size = 1;
    bool need_use = 2; // 是否需要使用visited_size的值， true为用，false为不用
}

message GetSpecifiedChannelVisitedSizeReq {
    repeated uint32 channel_id_list = 1;
}

message GetSpecifiedChannelVisitedSizeResp {
    map<uint32, VisitedInfo> visited_info_list = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message OptionStruct{
    string optionName= 1;
    uint32  optionId=2;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SendWelcomePop{
    bool   startFlag= 1;
    string topic= 2; // 主题
    repeated OptionStruct optionOne= 3; //选项
    string pictureUrl=4;   //头图
}


//用户进房消息
message GetWelcomePopReq{
    uint32 channel_id = 1;
    uint32  uid=2;
}
message GetWelcomePopResp{
    SendWelcomePop welcome_msg= 1;
}


//用户点击消息触发公屏推送
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message UserClickPopReq{
    uint32 channel_id=1;
    uint32 index=2;    // 用户选择的索引号，从0开始, repeated string optionOne的索引
    OptionStruct optionOne = 3; // 带上内容，后端仅做校验，打印用途
    uint32 uid=4;

}
message UserClickPopResp{

}

//快乐水推送类型
message PushCokeStateInfo{
    CokeAct   info=1; //推送信息
}
// 进房后乐窝活动进行期间的信息获取 (和乐窝活动相关的汇总）
message GetMusicNestLiveInfoReq {
    uint32 channel_id = 1; // 房间标识
    uint32  uid = 2;  //用户uid
}

message GetMusicNestLiveInfoResp {
    uint32 channel_id = 1; // 房间标识
    CokeAct coke_info = 2;//
}
// 乐窝活动节目进行情况信息推送
message CokeRuleInfo {
    uint32 act_id = 1; /* 乐窝活动id */
    uint32 channel_id = 2;
    uint32 stay_ts = 3; // 停留stay_ts可以获得一个快乐水
    uint32 coke_num = 4; /*     - 每获得「Y」个快乐水，将额外获得「Z」票数 */
    uint32 vote_num = 5; /*     - 每获得「Y」个快乐水，将额外获得「Z」票数 */
}

message CokeAct{
    CokeRuleInfo coke_rule_info = 1;
    bool is_open_coke = 2; // true: 开启快乐水 false：关闭快乐水
    uint32 left_coke = 3; // 请求用户剩余的可乐数量
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CokeStateInfo{
    bool startFlag=1;
    uint32 stay_ts = 2; // 停留stay_ts可以获得一个快乐水
    uint32 coke_num = 3; /*     - 每获得「Y」个快乐水，将额外获得「Z」票数 */
    uint32 vote_num = 4; /*     - 每获得「Y」个快乐水，将额外获得「Z」票数 */

}

message RandomGetRobotImageReq{
    int64 limit=1;
}
message RandomGetRobotImageResp{
    repeated string image_url=1;
}

message InChannelWhiteListReq {
    uint32 channel_id = 1;
}

message InChannelWhiteListResp {
    bool is_white = 1;
}