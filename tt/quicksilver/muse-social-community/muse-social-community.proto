syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package muse_social_community;
option go_package = "golang.52tt.com/protocol/services/muse-social-community";

import "tt/quicksilver/ugc/non_public_content.proto";

// buf:lint:ignore SERVICE_PASCAL_CASE
service muse_social_community {
  rpc UpsertCategoryTypes(UpsertCategoryTypesReq)returns(UpsertCategoryTypesResp);
  rpc SearchCategoryTypes(SearchCategoryTypesReq)returns(SearchCategoryTypesResp);//修改时间倒序
  rpc BatchCategoryTypesByIds(BatchCategoryTypesByIdsReq)returns(BatchCategoryTypesByIdsResp);//走缓存

  rpc UpsertCategories(UpsertCategoriesReq)returns(UpsertCategoriesResp);
  rpc SearchCategories(SearchCategoriesReq)returns(SearchCategoriesResp);//修改时间倒序
  rpc BatchCategoriesByIds(BatchCategoriesByIdsReq)returns(BatchCategoriesByIdsResp);//走缓存
  rpc BatchCategoriesByBrandIds(BatchCategoriesByBrandIdsReq)returns(BatchCategoriesByBrandIdsResp);//根据brandid获取

  rpc UpsertBrandTypes(UpsertBrandTypesReq)returns(UpsertBrandTypesResp);
  rpc SearchBrandTypes(SearchBrandTypesReq)returns(SearchBrandTypesResp);//修改时间倒序
  rpc BatchBrandTypesByIds(BatchBrandTypesByIdsReq)returns(BatchBrandTypesByIdsResp);//走缓存

  rpc UpsertBrands(UpsertBrandsReq)returns(UpsertBrandsResp);
  rpc SearchBrands(SearchBrandsReq)returns(SearchBrandsResp);//修改时间倒序
  rpc BatchBrandBackgroundExtras(BatchBrandBackgroundExtrasReq)returns(BatchBrandBackgroundExtrasResp);//只运营后台用
  rpc BatchBrandsByIds(BatchBrandsByIdsReq)returns(BatchBrandsByIdsResp);//走缓存

  rpc UpsertBrandMembers(UpsertBrandMembersReq)returns(UpsertBrandMembersResp);
  rpc SearchBrandMembers(SearchBrandMembersReq)returns(SearchBrandMembersResp);//修改时间倒序
  rpc BatchBrandMembersByUids(BatchBrandMembersByUidsReq)returns(BatchBrandMembersByUidsResp);//走缓存,只返回有效的
  rpc ListBrandMembersByUid(ListBrandMembersByUidReq)returns(ListBrandMembersByUidResp);//获取单个用户在不同厂牌的成员属性，走缓存
  rpc BatchOfflineBrandMembers(BatchOfflineBrandMembersReq)returns(BatchOfflineBrandMembersResp);
  rpc RebuildDao(RebuildDaoReq)returns(RebuildDaoResp);
  rpc JoinSocialCommunityFans(JoinSocialCommunityFansReq) returns (JoinSocialCommunityFansResp);//加入粉丝团
  rpc GetSocialCommunityDetailInfo(GetSocialCommunityDetailInfoReq)returns(GetSocialCommunityDetailInfoResp);//获取社群信息
  //  rpc UpdateSocialCommunityLogo(UpdateSocialCommunityLogoReq)returns(UpdateSocialCommunityLogoResp);//更新logo
  rpc GetSocialCommunityFansCount(GetSocialCommunityFansCountReq)returns(GetSocialCommunityFansCountResp);//获取社群粉丝团人数
  rpc GetSocialCommunityKernelCount(GetSocialCommunityKernelCountReq)returns(GetSocialCommunityKernelCountResp);//获取社群粉丝团人数
  rpc GetSocialCommunityCaption(GetSocialCommunityCaptionReq)returns(GetSocialCommunityCaptionResp);//获取社群主理人
  rpc GetSocialCommunityAllMembersCount(GetSocialCommunityAllMembersCountReq)returns(GetSocialCommunityAllMembersCountResp);
  rpc BatchGetSocialCommunityAllMembersCount(BatchGetSocialCommunityAllMembersCountReq)returns(BatchGetSocialCommunityAllMembersCountResp);

  rpc SearchMemberWorkerOrders(SearchMemberWorkerOrdersReq)returns(SearchMemberWorkerOrdersResp);
  rpc UpsertMemberWorkerOrders(UpsertMemberWorkerOrdersReq)returns(UpsertMemberWorkerOrdersResp);

  rpc BatchBrandChannelByChannelIds(BatchBrandChannelByChannelIdsReq)returns(BatchBrandChannelByChannelIdsResp);
  rpc BatchBrandChannelsByBrandIds(BatchBrandChannelsByBrandIdsReq)returns(BatchBrandChannelsByBrandIdsResp);

  rpc ListMuseSocialCommunityNavSecondaryBars(ListMuseSocialCommunityNavSecondaryBarsReq)returns(ListMuseSocialCommunityNavSecondaryBarsResp);
  rpc ListMuseSocialCommunityNavBars(ListMuseSocialCommunityNavBarsReq)returns(ListMuseSocialCommunityNavBarsResp);
  rpc GetSocialCommunityProfilePages(GetSocialCommunityProfilePagesReq)returns(GetSocialCommunityProfilePagesResp);//获取社群资料页


  /*社群5.0*/
  rpc BatchGetOpenCategoryTypes(BatchGetOpenCategoryTypesReq)returns(BatchGetOpenCategoryTypesResp){}
  rpc BatchGetCategoriesByCategoryTypeId(BatchGetCategoriesByCategoryTypeIdReq)returns(BatchGetCategoriesByCategoryTypeIdResp){}
  rpc ApplyCreateSocialCommunity(ApplyCreateSocialCommunityReq)returns(ApplyCreateSocialCommunityResp){}

  rpc GetUserRecentSocialCommunityOrder(GetUserRecentSocialCommunityOrderReq)returns(GetUserRecentSocialCommunityOrderResp);
  rpc ListUserKernelMembers(ListUserKernelMembersReq)returns(ListUserKernelMembersResp);
  rpc ListUserFansMembers(ListUserFansMembersReq)returns(ListUserFansMembersResp);
  rpc JoinSocialCommunityKernel(JoinSocialCommunityKernelReq)returns(JoinSocialCommunityKernelResp);

  rpc GetSocialCommunityFloat(GetSocialCommunityFloatRequest)returns(GetSocialCommunityFloatResponse);

  rpc AddCategoryEvents(AddCategoryEventsReq) returns(AddCategoryEventsResp);
  rpc AddBrandTypeEvents(AddBrandTypeEventsReq)returns(AddBrandTypeEventsResp);
  rpc AddBrandEvents(AddBrandEventsReq)returns(AddBrandEventsResp);
  rpc AddBrandMemberEvents(AddBrandMemberEventsReq)returns(AddBrandMemberEventsResp);
  rpc AddBrandGroupEvents(AddBrandGroupEventsReq)returns(AddBrandGroupEventsResp);


  /*社群群聊*/
  rpc GetSocialCommunityBase(GetSocialCommunityBaseReq)returns(GetSocialCommunityBaseResp){};
  rpc RebuildGroup(RebuildGroupReq)returns(RebuildGroupResp){};
  rpc RebuildMemberCount(RebuildMemberCountReq)returns(RebuildMemberCountResp){};
  rpc GetGroupMemberLimit(GetGroupMemberLimitReq)returns(GetGroupMemberLimitResp){};


  rpc GetSocialCommunityKernelMembers(GetSocialCommunityKernelMembersReq)returns(GetSocialCommunityKernelMembersResp){}

  rpc MuseSocialPreviewGroupMessage(MuseSocialPreviewGroupMessageRequest)returns(MuseSocialPreviewGroupMessageResponse){}

  /*社群6.38*/
  rpc GetSocialCommunityMemberList(GetSocialCommunityMemberListReq)returns(GetSocialCommunityMemberListResp){};/*获取社群成员列表*/
  /* 获取社群通告牌的新消息数量*/
  rpc GetSocialCommunityAnnounceNewsCountMap(GetSocialCommunityAnnounceNewsCountMapRequest) returns (GetSocialCommunityAnnounceNewsCountMapResponse) {}

  /*创建、修改公告*/
  rpc UpsertMuseSocialAnnounce(UpsertMuseSocialAnnounceRequest) returns (UpsertMuseSocialAnnounceResponse) {}


  /*公告位置列表*/
  rpc ListAnnounceDestinations(ListAnnounceDestinationsRequest) returns (ListAnnounceDestinationsResponse) {}
  /*公告列表*/
  rpc ListMuseSocialAnnounces(ListMuseSocialAnnouncesRequest) returns (ListMuseSocialAnnouncesResponse) {}

  /*设置感兴趣*/
  rpc SetMuseSocialAnnounceInterest(SetMuseSocialAnnounceInterestRequest)returns(SetMuseSocialAnnounceInterestResponse){}

  /*删除公告牌*/
  rpc RemoveMuseSocialAnnounce(RemoveMuseSocialAnnounceRequest)returns(RemoveMuseSocialAnnounceResponse){}

  /*感兴趣的成员列表*/
  rpc ListMuseSocialAnnounceInterestUsers(ListMuseSocialAnnounceInterestUsersRequest)returns(ListMuseSocialAnnounceInterestUsersResponse){}

  /**/
  rpc  BatchMuseSocialAnnounceByAnnounceIds(BatchMuseSocialAnnounceByAnnounceIdsRequest)returns(BatchMuseSocialAnnounceByAnnounceIdsResponse){}

  rpc  NotifyAllMuseGroup(NotifyAllMuseGroupRequest)returns(NotifyAllMuseGroupResponse){}

  rpc ValidateUserHasCreateAnnouncePermissions(ValidateUserHasCreateAnnouncePermissionsRequest)returns(ValidateUserHasCreateAnnouncePermissionsResponse){}

  /*社群权限相关*/
  rpc UpsertApplyJoinCommunityWorkOrder(UpsertApplyJoinCommunityWorkOrderRequest)returns(UpsertApplyJoinCommunityWorkOrderResponse){}

  rpc SearchApplyJoinCommunityWorkOrder(SearchApplyJoinCommunityWorkOrderRequest)returns(SearchApplyJoinCommunityWorkOrderResponse){}

  rpc ListApplyJoinCommunityWorkOrders(ListApplyJoinCommunityWorkOrdersRequest)returns(ListApplyJoinCommunityWorkOrdersResponse){}

  rpc SubmitApplicationToJoinCommunity(SubmitApplicationToJoinCommunityRequest)returns(SubmitApplicationToJoinCommunityResponse){}

  rpc RebuildAdditionMode(RebuildAdditionModeReq)returns(RebuildAdditionModeResp){}

  rpc GetSocialCommunityContentStreamNewsCount(GetSocialCommunityContentStreamNewsCountRequest)returns(GetSocialCommunityContentStreamNewsCountResponse){}

  rpc GetUserCategoryAttitudeCount(GetUserCategoryAttitudeCountRequest)returns(GetUserCategoryAttitudeCountResponse){}

  rpc ListMuseSocialCommunityCommentMessage(ListMuseSocialCommunityCommentMessageRequest)returns(ListMuseSocialCommunityCommentMessageResponse){}
  rpc ListMuseSocialCommunityAttitudeMessage(ListMuseSocialCommunityAttitudeMessageRequest)returns(ListMuseSocialCommunityAttitudeMessageResponse){}
  rpc BatchMuseSocialCommunityMsgCount(BatchMuseSocialCommunityMsgCountRequest)returns(BatchMuseSocialCommunityMsgCountResponse){}

  /*新的社团首页（导航栏）*/
  rpc ListMuseSocialCommunityNavBarsV2(ListMuseSocialCommunityNavBarsV2Request)returns(ListMuseSocialCommunityNavBarsV2Response){}
  rpc ListMuseSocialCommunityNavSecondaryBarsV2(ListMuseSocialCommunityNavSecondaryBarsV2Request)returns(ListMuseSocialCommunityNavSecondaryBarsV2Response){}
  rpc BatchMuseSocialCommunityNavBarsV2(BatchMuseSocialCommunityNavBarsV2Request)returns(BatchMuseSocialCommunityNavBarsV2Response){}
  rpc BatchMuseSocialCommunityActiveChannel(BatchMuseSocialCommunityActiveChannelRequest)returns(BatchMuseSocialCommunityActiveChannelResponse){}
  rpc ListMuseSocialCommunityGroups(ListMuseSocialCommunityGroupsRequest)returns(ListMuseSocialCommunityGroupsResponse){}
  // 新的社团首页（导航栏）
  rpc HasEditableSocialCommunityName(HasEditableSocialCommunityNameRequest)returns(HasEditableSocialCommunityNameResponse){}
  rpc IncreaseEditSocialCommunityNameTimes(IncreaseEditSocialCommunityNameTimesRequest)returns(IncreaseEditSocialCommunityNameTimesResponse){}
  rpc ReportPersonalChannelViewSocialCommunity(ReportPersonalChannelViewSocialCommunityRequest)returns(ReportPersonalChannelViewSocialCommunityResponse){}

  // 根据优先级获取用户社团对应的品类列表
  rpc GetUserCategoryList(GetUserCategoryListRequest)returns(GetUserCategoryListResponse){}

  // 获取社团助手消息数
  rpc GetSocialCommunityAssistantMsgCount(GetSocialCommunityAssistantMsgCountRequest)returns(GetSocialCommunityAssistantMsgCountResponse){}

  rpc GetRespectCount(GetRespectCountRequest) returns (GetRespectCountResponse) {}


    rpc GetSocialCommunitySimpleByName(GetSocialCommunitySimpleByNameRequest)returns(GetSocialCommunitySimpleByNameResponse){}

  rpc  SendSocialCommunityActivityNotice(SendSocialCommunityActivityNoticeReq)returns(SendSocialCommunityActivityNoticeResp){}
  rpc  SendPlaymateActivityNotice(SendPlaymateActivityNoticeReq)returns(SendPlaymateActivityNoticeResp){}
  rpc SearchSocialCommunitySimpleInfoByName(SearchSocialCommunitySimpleInfoByNameRequest)returns(SearchSocialCommunitySimpleInfoByNameResponse){}


  /*-----------运营后台品类合集-----------*/
  rpc ListCategoryCollection(ListCategoryCollectionReq) returns (ListCategoryCollectionResp) {}
  rpc UpsertCategoryCollection(UpsertCategoryCollectionReq) returns (UpsertCategoryCollectionResp) {}
  rpc GetCategoryCollection(GetCategoryCollectionReq) returns (GetCategoryCollectionResp) {}
  rpc CheckCategoryInCollection(CheckCategoryInCollectionReq) returns (CheckCategoryInCollectionResp) {} // 检查品类是否在合集中
  /*-----------运营后台品类合集-----------*/
  rpc RebuildSocialCommunityGroupName(RebuildSocialCommunityGroupNameReq)returns(RebuildSocialCommunityGroupNameResp){}

  /*-----------------普通社群粉丝上限--------------------*/
  rpc BatchSetAmateurSocialCommunityMemberLimit(BatchSetAmateurSocialCommunityMemberLimitRequest)returns(BatchSetAmateurSocialCommunityMemberLimitResponse){}
  rpc DelAmateurSocialCommunityMemberLimit(DelAmateurSocialCommunityMemberLimitRequest)returns(DelAmateurSocialCommunityMemberLimitResponse){}
  rpc GetAmateurSocialCommunityMemberLimit(GetAmateurSocialCommunityMemberLimitRequest)returns(GetAmateurSocialCommunityMemberLimitResponse){}
  rpc RebuildAmateurSocialCommunityMemberLimitOldConfig(AmateurSocialCommunityMemberLimitRequest)returns(AmateurSocialCommunityMemberLimitResponse){}
  //*-----------------普通社群粉丝上限--------------------*/

  // 主分社
  rpc BatGetMainSubCommunity(BatGetMainSubCommunityReq) returns (BatGetMainSubCommunityResp) {}
  rpc UpsertMainSubCommunity(UpsertMainSubCommunityReq) returns (UpsertMainSubCommunityResp) {}


  // 社群通用白名单设置
  rpc SetWhiteIds(SetWhiteIdsRequest) returns (SetWhiteIdsResponse) {}
  rpc DelWhiteIds(DelWhiteIdsRequest) returns (DelWhiteIdsResponse) {}
  rpc ListWhiteIds(ListWhiteIdsRequest) returns (ListWhiteIdsResponse) {}

  rpc GetSocialCommunityChannelUserRole(GetSocialCommunityChannelUserRoleRequest)returns(GetSocialCommunityChannelUserRoleResponse){}


  //社群活动弹窗
  rpc UpsertSocialCommunityActivityPopup(UpsertSocialCommunityActivityPopupRequest)returns(UpsertSocialCommunityActivityPopupResponse){}
  rpc GetSocialCommunityActivityPopup(GetSocialCommunityActivityPopupRequest)returns(GetSocialCommunityActivityPopupResponse){}
  rpc  DelSocialCommunityActivityPopup(DelSocialCommunityActivityPopupRequest)returns(DelSocialCommunityActivityPopupResponse){}
  rpc GetUserSocialCommunityActivityPopup(GetUserSocialCommunityActivityPopupRequest)returns(GetUserSocialCommunityActivityPopupResponse){}


  // 社群限制名单相关接口
  rpc AddLimitedCommunity(AddLimitedCommunityReq)returns(AddLimitedCommunityResp);
  rpc GetLimitedCommunityList(GetLimitedCommunityListReq)returns(GetLimitedCommunityListResp);
  rpc DeleteCommunityLimit(DeleteCommunityLimitReq)returns(DeleteCommunityLimitResp);


  // 社群用户信息查询
  rpc BatGetSimpleSocialCommunityMembers(BatGetSimpleSocialCommunityMembersRequest)returns(BatGetSimpleSocialCommunityMembersResponse){}
  rpc ReShelfSocialCommunity(ReShelfSocialCommunityRequest)returns(ReShelfSocialCommunityResponse){}
  rpc GetAbnormalSocialCommunity(GetAbnormalSocialCommunityRequest)returns(GetAbnormalSocialCommunityResponse){}
  rpc RemoveAbnormalSocialCommunity(RemoveAbnormalSocialCommunityRequest)returns(RemoveAbnormalSocialCommunityResponse){}
}


message ReShelfSocialCommunityRequest {
  repeated string social_community_ids = 1;
}
message ReShelfSocialCommunityResponse {

}


message AddLimitedCommunityReq {
  repeated LimitedCommunity limited_community_list = 1; // 受限社群列表信息
  string creator = 2; // 创建人名称
}

message LimitedCommunity {
  string id = 1;   // 社群ID
  uint32 limit_end_time = 2; // 受限结束时间
}

message AddLimitedCommunityResp {
}

message GetLimitedCommunityListReq {
  string id = 1; // 社群ID，非必需，不传返回全部
}

message GetLimitedCommunityListResp {
  repeated LimitedCommunityInfo limited_community_info = 1;
}

message LimitedCommunityInfo {
  string id = 1; // 社群ID
  string name = 2; // 社群名
  string creator = 3;
}

message DeleteCommunityLimitReq {
  repeated string ids = 1; // 社群ID
}

message DeleteCommunityLimitResp {
}


message GetSocialCommunityChannelUserRoleRequest{
  uint32 uid=1;
  uint32 channel_id=2;
}

message GetSocialCommunityChannelUserRoleResponse{
  BrandMemberRole role=1;
      string role_text=2;
}

message RebuildSocialCommunityGroupNameReq{
  string social_community_id = 1;
  uint32 professional=2;
}

message RebuildSocialCommunityGroupNameResp{

}

message GetSocialCommunitySimpleByNameRequest{
    string name = 1;
}

message SocialCommunitySimple{
  string social_community_id = 1;
  string name = 2;
  string logo=3;
  string category_type_simple_desc=4;
  string category_name=5;
}
message GetSocialCommunitySimpleByNameResponse{
  repeated SocialCommunitySimple social_community_simples = 1;
}

message ListMuseSocialCommunityGroupsRequest{
  string social_community_id = 1;
}

message ListMuseSocialCommunityGroupsResponse{
  repeated MuseSocialGroup groups = 1;
}

message MuseSocialGroup {
  uint32 group_id = 1;
  string logo = 2;
  string name = 3;
  string desc = 4;
}

message BatchMuseSocialCommunityActiveChannelRequest{
  repeated string social_community_ids = 3;
}

message BatchMuseSocialCommunityActiveChannelResponse{
  map<string, MuseSocialCommunityActiveChannelIds> channel_id_map = 1;
}

message MuseSocialCommunityActiveChannelIds{
  repeated uint32 channel_ids = 1;
}

message BatchMuseSocialCommunityNavBarsV2Request{
  uint32 uid = 1;
  repeated string social_community_ids = 2;
}

message BatchMuseSocialCommunityNavBarsV2Response{
  map<string, MuseSocialCommunityNavBarV2>  bars = 1;
  map<uint32, uint32> msg_type_count_map = 2;//0-评论，1-respect&diss
}

message ListMuseSocialCommunityNavSecondaryBarsV2Request{
  uint32 uid = 1;;
  string id = 2;
}

message ListMuseSocialCommunityNavSecondaryBarsV2Response{
  repeated MuseSocialCommunityNavSecondaryBarV2 secondary_bars = 1;
  string category_id = 2;
}

message ListMuseSocialCommunityNavBarsV2Request{
  uint32 uid = 1;
  string social_community_id = 2;
}

message ListMuseSocialCommunityNavBarsV2Response{
  repeated MuseSocialCommunityNavBarV2 bars = 1;
  map<uint32, uint32> msg_type_count_map = 2;//0-评论，1-respect&diss
}

message MuseSocialCommunityNavBarV2{
  string social_community_id = 1;
  string name = 2;
  string logo = 3;
  string bg_logo = 4;   //logo底图
  string category_type_simple_desc = 5;
  Professionalism professionalism_info = 6;
  uint32 level = 7;
  string level_logo = 8;//社团等级logo
  bool has_msg_red_dot = 9;   //社群红点消息
  string channel_status_logo = 10;   //房间状态图片
  repeated MuseSocialCommunityNavSecondaryBarV2 secondary_bars = 11;//只有当前的bar有值
  BrandMember member = 12;//用户角色
  string category_id = 13; //品类id
  map<uint32, bool> white_map = 20;// 是否有白名单 key:WhiteType
}

message Professionalism{
  uint32 brand_professionalism = 1;
  string picture_url = 2;
  string text = 3;
}

message MuseSocialCommunityNavSecondaryBarV2{
  uint32 bar_type = 1;//0-品类圈，1-讨论，2-干货，3-房间，4-群聊，5-通告牌
  string text = 2;
  map<uint32, string> status_logo = 3;//0-未选中，1-选中,可能是静态图，可能是动态图
  uint32 un_read_msg_count = 4;
  bool has_msg_red_dot = 5;   //社群红点消息
  MuseSocialCommunityNavSecondaryBarV2ContentExtra extra = 6;//目前只有品类，讨论，干货有额外参数
}

enum SecondaryBarType{
  SECONDARY_BAR_TYPE_CATEGORY = 0;//品类圈
  SECONDARY_BAR_TYPE_TALK = 1;//讨论
  SECONDARY_BAR_TYPE_KNOWLEDGE = 2;//干货
  SECONDARY_BAR_TYPE_CHANNEL = 3;//房间
  SECONDARY_BAR_TYPE_GROUP = 4;//群聊
  SECONDARY_BAR_TYPE_ANNOUNCE = 5;//通告牌
}

message MuseSocialCommunityNavSecondaryBarV2ContentExtra{
  oneof content_extra {
    MuseSocialCommunityStreamContent stream_permission = 1;
  }
}

message MuseSocialCommunityStreamContent{
  uint32 user_permission = 1; // 权限 ugc_non_public.proto ContentStreamPermission 位运算 0001 0010 0100...，0:无权限
  string scene_id = 2;
  string stream_id = 3;
}

message BatchMuseSocialCommunityMsgCountRequest{
  uint32 uid = 1;
}

message BatchMuseSocialCommunityMsgCountResponse{
  map<uint32, uint32> msg_type_count_map = 2;//0-评论，1-respect&diss
}

message ListMuseSocialCommunityCommentMessageRequest{
  uint32 uid = 1;
  string offset_id = 2;
  uint32 limit = 3;
}

message ListMuseSocialCommunityCommentMessageResponse{
  repeated SocialCommentMessage msgs = 1;
  map<uint32, uint32> msg_type_count_map = 2;//0-评论，1-respect&diss
  bool is_done = 3;
}

message SocialCommentMessage {
  string comment_id = 1;//自身评论id
  uint32 user_id = 2;
  string account = 3;//头像
  uint32 sex = 4;//性别
  string nick_name = 5;
  string post_id = 6;
  string reply_to_comment_id = 7;
  ugc.non_public_content.CommentInfo comment = 8; // 评论
  ugc.non_public_content.CommentInfo reply_to_comment = 9; // 被评论的评论
  ugc.non_public_content.NonPublicPostInfo post = 10; // 帖子的富文本内容
  string scene_id = 11;
  MuseUserRole role = 12;
  string offset_id = 13;
  string stream_id = 14;
  uint32 stream_type = 15;
  string social_community_id = 16;
  int64 create_at = 17;
  bool  has_at_msg=18;  //是否有at消息
}

message MuseUserRole{
  repeated string bg_color = 1;
  repeated string font_color = 2;
  string text = 3;
  string social_community_id = 4;
}

message ListMuseSocialCommunityAttitudeMessageRequest{
  uint32 uid = 1;
  string offset_id = 2;
  uint32 limit = 3;
}

message ListMuseSocialCommunityAttitudeMessageResponse{
  repeated SocialAttitudeMessage msgs = 1;
  map<uint32, uint32> msg_type_count_map = 2;//0-评论，1-respect&diss
  bool is_done = 3;
}

message SocialAttitudeMessage {
  string post_id = 1;
  string comment_id = 2;
  uint32 user_id = 3;
  string account = 4;//头像
  string nick_name = 5;
  uint32 sex = 6;//性别
  uint32 attitude_type = 7;
  uint32 target_user_id = 8;
  uint32 step_type = 9;
  string scene_id = 10;
  ugc.non_public_content.CommentInfo reply_to_comment = 11; // 被评论的评论
  ugc.non_public_content.NonPublicPostInfo post = 12; // 帖子
  MuseUserRole role = 13;
  string offset_id = 14;
  string stream_id = 15;
  uint32 stream_type = 16;
  string social_community_id = 17;
  int64 create_at = 18;
}

message GetSocialCommunityContentStreamNewsCountRequest{
  uint32 uid = 1;
  bool need_default_community_stream = 2;
}

message GetSocialCommunityContentStreamNewsCountResponse{
  int64 sum = 1;
  bool  has_category_circle_msg = 2;  //是否有品类圈消息  false --没有品类圈消息或其他消息数不为0 --true 有品类圈新消息，且其他消息总数为0
  SocialCommunityContentStream default_content_stream = 3;
}

message SocialCommunityContentStream{
  string social_community_id = 1;
  string scene_id = 2;
  string stream_id = 3;
  uint32 stream_type = 4;
  string social_name = 5;
  string social_logo = 6;
}

message NotifyAllMuseGroupRequest{
  string message = 1;
}

message NotifyAllMuseGroupResponse{

}

message ListMuseSocialAnnouncesRequest{
  uint32 uid = 1;
  string social_community_id = 2;
  uint32 limit = 3;
  string offset_id = 4;
  uint32 list_type = 5;//0-全部，1-自己发布，2-感兴趣的
}

enum MuseAnnounceListType{
  MUSE_ANNOUNCE_LIST_TYPE_ALL = 0;
  MUSE_ANNOUNCE_LIST_TYPE_MINE = 1;
  MUSE_ANNOUNCE_LIST_TYPE_INTERESTED = 2;
}

message ListMuseSocialAnnouncesResponse{
  repeated MuseSocialAnnounce announces = 1;
  map<uint32, bool> unreaded_list_type_map = 2; //未读list_type
  string offset_id = 3;
}

message UpsertMuseSocialAnnounceRequest{
  uint32 uid = 1;
  string social_community_id = 2;
  MuseSocialAnnounce announce = 3;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message MuseSocialAnnounce{
  string id = 1;
  string title = 2;//公告标题
  string content = 3;//内容
  int64 start_time = 4;//开始时间
  int64 end_time = 5;//结束时间
  uint32 interest_count = 6;//感兴趣人数
  bool interested = 7;//用户是否已经感兴趣
  bool isUnreaded = 8;//显示红点用，只有自己发布的未读的为true
  uint32 modify_count = 9;//已修改次数
  repeated string image_keys = 10;//图片key
  uint32 creator_uid = 11;
  string social_community_id = 12;

  MuseAnnounceDestinationChoose destination_choose = 13;
}

message ListAnnounceDestinationsRequest{
  uint32 uid = 1;
  string social_community_id = 2;
}

message  ListAnnounceDestinationsResponse{
  repeated  MuseAnnounceDestinationGroup   destination_groups = 1;
}

message MuseAnnounceDestinationGroup {
  string title = 1;
  repeated  MuseAnnounceDestinationChoose   destinations = 2;
}

message MuseAnnounceDestinationChoose{
  string title = 1;
  uint32 destination_type = 2;//0-没有目的地，1-group,2-chat-channel,3-show-channel,4-personal-channel
  uint32 id = 3;//channelId or group id
}

enum MuseAnnounceDestinationType{
  MUSE_ANNOUNCE_DESTINATION_TYPE_NONE = 0;
  MUSE_ANNOUNCE_DESTINATION_TYPE_GROUP = 1;
  MUSE_ANNOUNCE_DESTINATION_TYPE_CHAT_CHANNEL = 2;
  MUSE_ANNOUNCE_DESTINATION_TYPE_SHOW_CHANNEL = 3;
  MUSE_ANNOUNCE_DESTINATION_TYPE_PERSONAL_CHANNEL = 4;
}

message UpsertMuseSocialAnnounceResponse{
}

message MuseSocialPreviewGroupMessageRequest{
  uint32 group_id = 1;
  uint32 uid = 2;
}

message MuseSocialPreviewGroupMessageResponse{
  repeated string  message = 1;
  uint32 group_id = 2;
  string social_community_id = 3;
}

message SocialGroupSimpleInfo{
  uint32 group_id = 1;  //群id
  string group_name = 2;  // 群名称
  uint32 group_type = 3;
}

message RebuildMemberCountReq{

}

message RebuildMemberCountResp{

}

message RebuildGroupReq{

}

message RebuildGroupResp{

}

message AddCategoryEventsReq{
  repeated string category_type_ids = 1;
  repeated string category_ids = 2;
}

message AddCategoryEventsResp{

}

message AddBrandTypeEventsReq{
  repeated string category_ids = 1;
  repeated string brand_type_ids = 2;
}

message AddBrandTypeEventsResp{

}

message AddBrandEventsReq{
  repeated string brand_type_ids = 1;
  repeated string brand_ids = 2;
}

message AddBrandEventsResp{

}

message AddBrandMemberEventsReq{
  repeated string brand_ids = 1;
  repeated uint32 uids = 2;
}

message AddBrandMemberEventsResp{

}

message AddBrandGroupEventsReq{
  repeated string force_brand_ids = 1;
  repeated string brand_ids = 2;
  repeated string member_ids = 3;
}

message AddBrandGroupEventsResp{

}

message GetSocialCommunityFloatRequest{
  uint32 uid = 1;
}

message GetSocialCommunityFloatResponse{
  string icon = 1;
  string desc = 2;
  string temp_desc = 3;//临时文案，只展示六秒
}

message GetUserRecentSocialCommunityOrderReq{
  uint32 uid = 1;
}

message GetUserRecentSocialCommunityOrderResp{
  SocialCommunityOrder community_order = 1;
}

message ListUserKernelMembersReq{
  uint32 uid = 1;
}

message ListUserKernelMembersResp{
  repeated BrandMember members = 1;
}

message ListUserFansMembersReq{
  uint32 uid = 1;
  string offset_id = 2;
  uint32 limit = 3;
}

message ListUserFansMembersResp{
  repeated BrandMember members = 1;
}

message SocialCommunityOrder{
  string name = 1;
  string intro = 2;
  string category_id = 3;
  string brand_type_id = 4;
  string logo = 5;
  string vision = 6;
  SocialCommunityOrderStatus status = 7;
  int64 update_at = 8;
}

enum SocialCommunityOrderStatus{
  Community_Order_Status_None = 0;
  Community_Order_Status_Waiting_Audit = 1;
  Community_Order_Status_Audit = 2;
  Community_Order_Status_Temp_Pass = 3;
  Community_Order_Status_Pass = 4;
  Community_Order_Status_Reject = 5;
}

/*********************************************CategoryType*********************************************/
//message AddBrandMemberEventsByBrandTypesReq{
//  repeated string brand_type_ids = 1;
//}
//
//message AddBrandMemberEventsByBrandTypesResp{
//
//}

message UpsertCategoryTypesReq {
  repeated CategoryType category_types = 1;
}

message UpsertCategoryTypesResp {
  repeated string err_names = 2;
  map<string, string> err_reason = 3;
}

message SearchCategoryTypesReq {
  string offset_id = 1;
  uint32 limit = 2;
  string regex_name = 3;//正则
  CategoryTypeStatus status = 4;//none为全部
}

message SearchCategoryTypesResp {
  repeated CategoryType category_types = 1;
}

message BatchCategoryTypesByIdsReq {
  repeated string ids = 1;
}

message BatchCategoryTypesByIdsResp {
  map<string, CategoryType> category_types = 1;
}

message CategoryType {
  string id = 1;//不可修改
  string name = 2;
  int64 create_time = 3;//不可修改
  int64 update_time = 4;//不可修改
  CategoryTypeStatus status = 5;//不能为None
  string simple_desc = 6;//短文案
  string offset_id = 7;
  string icon = 8;
  string intro = 9;
  bool  open_community = 10;
}

enum CategoryTypeStatus{
  Category_Type_None = 0;
  Category_Type_Online = 1;
  Category_Type_OffLine = 2;
}

/*********************************************Category*********************************************/
message UpsertCategoriesReq {
  repeated Category categories = 1;
}

message UpsertCategoriesResp {
  repeated string err_names = 2;
  map<string, string> err_reason = 3;
}

message SearchCategoriesReq {
  string offset_id = 1;
  uint32 limit = 2;
  string regex_name = 3;//正则
  string category_type_id = 4;
  CategoryStatus status = 5;//none为全部
}

message SearchCategoriesResp {
  repeated Category categories = 1;
}

message BatchCategoriesByIdsReq {
  repeated string ids = 1;
}

message BatchCategoriesByIdsResp {
  map<string, Category> categories = 1;
}

message Category {
  string id = 1;//不可修改
  string name = 2;
  int64 create_time = 3;//不可修改
  int64 update_time = 4;//不可修改
  CategoryStatus status = 5;//不能为None
  string offset_id = 6;
  string category_type_id = 7;
  uint32 show_tab_id = 8;//演出房玩法
  string icon = 9;//品类icon
  string member_text = 10; //普通成员名称
  bool   show_default_name = 11; //默认显示成其他选项
  string category_type_simple_desc = 12;
  string   category_abbreviation = 13; //品类简称
  NewProfileResource new_profile_resource = 14; // 档案页资源包
  bool  open_community = 15;
}

// 档案页资源包
message NewProfileResource{
  string bg_pic_url = 1; // 社团档案页背景图
  uint32 bg_pic_type = 2;//社团档案页背景图类型 0,1：image； 2:video
  string bg_pic_first_frame_pic_url = 3;//社团档案页背景图首帧
  string intro_head_pic_url = 4; // 社团介绍头部图
  string photo_title_pic_url = 5; // 社团相册标题图
  string photo_bg_pic_url = 6; // 社团相册底图
  string member_info_bg_pic_url = 7; // 社团身份底图
  string common_color = 8; // 通用颜色
  string text_color = 9; // 字体颜色
  string kernel_member_head_pic_url = 10; // 核心成员墙头部图
  string kernel_member_bg_pic_url = 11; // 核心成员底图
  string user_card_pic_url =12; // 个人卡片底
  string user_card_im_pic_url = 13; // IM个人卡片底
  string text_shadow_color = 14; // 文字投影颜色

}

enum CategoryStatus{
  Category_None = 0;
  Category_Online = 1;
  Category_OffLine = 2;
}

/*********************************************BrandType*********************************************/

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message UpsertBrandTypesReq {
  repeated BrandType brandTypes = 1;
}

message UpsertBrandTypesResp {
  repeated string err_names = 2;
  map<string, string> err_reason = 3;
}

message SearchBrandTypesReq {
  string offset_id = 1;
  uint32 limit = 2;
  string regex_name = 3;
  string regex_attr = 4;//属性的正则匹配
  BrandTypeStatus status = 5;//none为全部
  string category_id = 6;
  repeated string brand_type_ids = 7;
}

message SearchBrandTypesResp {
  repeated BrandType brand_types = 1;
}

message BatchBrandTypesByIdsReq {
  repeated string ids = 1;
}

message BatchBrandTypesByIdsResp {
  map<string, BrandType>brand_types = 1;

}

message BrandType {
  string id = 1;//不可修改
  string name = 2;
  string type_attr = 3;/* 类型属性 */
  int64 create_time = 4;//不可修改
  int64 update_time = 5;//不可修改
  BrandTypeStatus status = 6;//不能为None,需要洗表
  string offset_id = 7;//需要洗表
  string category_id = 8;
  string cert_type_name = 9;
  bool no_limit_core_member = 10;//不对核心成员进行限制

  uint32 show_tab_id = 11;
  string member_text = 12;
  string category_icon = 13;
  string category_type_simple_desc = 14;
}

enum BrandTypeStatus{
  Brand_Type_None = 0;
  Brand_Type_Online = 1;
  Brand_Type_OffLine = 2;
}

/*********************************************Brand*********************************************/

message UpsertBrandsReq {
  repeated Brand brands = 1;
}

message UpsertBrandsResp {
  repeated string err_names = 2;
  map<string, string> err_reason = 3;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SearchBrandsReq {
  string offset_id = 1;
  uint32 limit = 2;
  string regex_name = 3;
  BrandStatus status = 4;//必须Brand_Online或Brand_OffLine
  uint32 hasChannel = 6;//0-所有，1-有工作室，2-没有
  string brand_type_id = 7;
  string regex_brand_type_attr = 8;
  uint32 brand_professionalism = 9;//1-专业，2-个人
  repeated string ids = 10;
}

message SearchBrandsResp {
  repeated Brand brands = 1;
}

message BatchBrandBackgroundExtrasReq{
  repeated string brand_ids = 1;
}

message BatchBrandBackgroundExtrasResp{
  repeated BrandBackgroundExtra brands = 1;
}

message BrandBackgroundExtra{
  map<uint32, uint32> role_counts = 1;//角色人数
  uint32 sign_count = 2;//认证标流水总和
  uint32 mark_sign_count = 3;/*基础认证额度*/
  uint32 sign_total = 4;//sign_count+mark_sign_count
  uint32 integral = 5;//积分
  string id = 6;//brandId
}

message BatchBrandsByIdsReq {
  repeated string ids = 1;
}

message BatchBrandsByIdsResp {
  map<string, Brand> brands = 1;
}

message Brand {
  string id = 1;//不可修改
  string name = 2;
  int64 create_time = 3;//不可修改
  int64 update_time = 4;//不可修改
  BrandStatus status = 5;
  string offset_id = 6;//需要洗表
  string operator = 7;//
  string intro = 8; /* 简介 */
  string brand_type_id = 9;
  string brand_type_attr_info = 10;
  uint32 mark_sign_count = 11;//只可以创建
  uint32 brand_professionalism = 12;//1-专业，2-个人
  string logo = 13;
  bool has_channel = 14;
  string category_type_simple_desc = 15;
  bool no_limit_core_member = 16;//不对核心成员进行限制
  uint32 tab_id = 17;
  string category_id = 18;
  string vision = 19;
  string member_text = 20;
  string category_icon = 21;
  string cert_type_name = 22;
  repeated uint32 group_ids = 23;
  uint32 addition_mode = 24;
  uint32 level = 25;
  SocialCommunityBackground social_community_background=26;     //社群背景
}

message SocialCommunityBackground{
  string social_community_background_url=1;     //社群背景
  uint32 social_community_background_type=2;  //类型
  string social_community_first_frame=3; //首帧图片
}

enum BrandStatus{
  Brand_None = 0;
  Brand_Online = 1;
  Brand_OffLine = 2;
  Brand_AUDIT = 3;
}
/*********************************************MemberWorkerOrder*********************************************/
message UpsertMemberWorkerOrdersReq{
  repeated MemberWorkerOrder member_worker_orders = 1;
}

message UpsertMemberWorkerOrdersResp{

}

message SearchMemberWorkerOrdersReq {
  string offset_id = 1;
  uint32 limit = 2;
  string brand_id = 3;
  uint32 uid = 4;
  repeated BrandMemberRole role = 5;
  repeated BrandMemberStatus member_status = 6;
  repeated MemberWorkerOrderStatus status = 7;
  repeated BrandMemberScene scene = 8;
}

message SearchMemberWorkerOrdersResp{
  repeated MemberWorkerOrder member_worker_orders = 1;
}

message MemberWorkerOrder {
  string id = 1;
  uint32 uid = 2;
  string brand_id = 3;
  BrandMemberRole role = 4;
  BrandMemberStatus member_status = 5;
  string intro = 6;
  string op_uid = 7;
  MemberWorkerOrderStatus status = 8;
  int64 create_time = 9;
  int64 update_time = 10;
  int64 auto_pass_time = 11;
  string reason = 12;
  string offset_id = 13;
  BrandMemberScene scene = 14;
}

enum MemberWorkerOrderStatus{
  Order_None = 0;
  Order_Audit = 1;
  Order_Pass = 2;
  Order_Rejected = 3;
  Order_Processing = 4;
}


/*********************************************BrandMember*********************************************/
message UpsertBrandMembersReq {
  repeated BrandMember brand_members = 1;
  BrandMemberScene scene = 2;
  string op_uid = 3;
  string reason = 4;
  int64 auto_pass_time = 5;
}

enum BrandMemberScene{
  From_Op = 0;//来自运营
  From_User = 1;//来自用户自身
  From_Manager = 2;//来自社群管理者
}

message UpsertBrandMembersResp {
  repeated string err_names = 2;
  map<string, string> err_reason = 3;
}

message SearchBrandMembersReq {
  string offset_id = 1;
  uint32 limit = 2;
  string brand_id = 3;
  repeated uint32 uid = 4;
  repeated BrandMemberRole role = 5;
  BrandMemberStatus status = 6;
}

message SearchBrandMembersResp {
  repeated BrandMember brand_member = 1;
}

message BatchBrandMembersByUidsReq {
  string brand_id = 1;
  repeated uint32 uids = 2;
}

message BatchBrandMembersByUidsResp {
  map<uint32, BrandMember> uid_member_map = 1;
}


message ListBrandMembersByUidReq{
  uint32 uid = 1;
}

message ListBrandMembersByUidResp{
  repeated BrandMember members = 1;
}

message BrandMember {
  string brand_id = 2;
  uint32 uid = 3;
  int64 create_time = 4;//不可修改
  int64 update_time = 5;//不可修改
  BrandMemberRole role = 6;
  string offset_id = 7;//需要洗表
  BrandMemberStatus status = 8;
  string operator = 9;  // 操作人
  string intro = 10; /* 简介 */
  string member_text = 11;//成员名
  bool has_channel_admin = 12;//是否有房间管理权限
  bool no_limit_core_member = 13;//不对核心成员进行限制
  repeated uint32 group_id = 14; //用户加入的群
    uint32 brand_professionalism=15; //社群类型
}

message BatchOfflineBrandMembersReq{
  repeated BrandMember members = 1;
}

message BatchOfflineBrandMembersResp{
  repeated string err_names = 2;
}

enum BrandMemberRole{
  Brand_Role_None = 0;
  Brand_Role_Captain = 1;//主理人
  Brand_Role_Kernel = 2;//核心成员
  Brand_Role_Vice_Captain = 3;//副主理人
  Brand_Producer = 4;//制作人
  Brand_Fans = 5;//粉丝
}


enum BrandMemberStatus{
  Brand_Status_None = 0;
  Brand_Status_Online = 1;//上架
  Brand_Status_Offline = 2;//下架
}


message RebuildDaoReq{

}

message RebuildDaoResp{

}

/*********************************************BrandChannel*********************************************/
message BatchBrandChannelByChannelIdsReq {
  repeated uint32 channel_ids = 1;
}

message BatchBrandChannelByChannelIdsResp {
  map<uint32, BrandChannel> brand_channel_map = 1;
}

message BatchBrandChannelsByBrandIdsReq{
  repeated string brand_ids = 1;
}

message BatchBrandChannelsByBrandIdsResp{
  map<string, BrandChannels>  brand_channels = 1;
}

message BrandChannels{
  repeated BrandChannel brand_channels = 1;
}

message BrandChannel{
  uint32 channel_id = 1;
  string brand_channel_name = 2;
  uint32 tab_id = 3;
  string brand_id = 4;
  int64 create_time = 5;
  int64 update_time = 6;
  bool is_default = 7;
  BrandChannelType channel_type = 8;
}

enum BrandChannelType{
  None = 0;
  Chat = 1;
  Show = 2;
}

/*--------------------------------------------社群2.0---------------------------------------------------*/

message JoinSocialCommunityFansReq{
  uint32 uid = 1;
  string social_community_id = 2;
  uint32 group_id = 3;
  string invite_code = 4;
  bool is_auto_input_invite_code = 5;
}

message JoinSocialCommunityFansResp{
  repeated uint32 added_group_ids = 1;
}

message SimpleUserInfo{
  uint32 uid = 1;
  string account = 2;    // 用户头像
  string nick_name = 3;  // 用户昵称
  uint32 sex = 4;          //用户性别
}

message SocialCommunityDetail{
  string social_community_id = 1;       //社群ID
  string social_community_logo = 2;      //社群logo
  string social_community_name = 3;      //社群名称
  string category_name = 4;              //品类名称
  SimpleUserInfo captain = 5;               //主理人名称
  string social_community_intro = 6;     //社群介绍
  uint32 member_count = 7;                //社群所有成员数量
  int32 integral_count = 8;            //社群积分数
  int32 sign_count = 9;                //认证数
  string category_type_simple_desc = 10;
  uint32 brand_professionalism = 11; //1-专业，2-非专业
  string vision = 12;  //愿景
  uint32 level = 13;//社团等级
  string level_logo = 14;//社团等级logo
  string category_id = 15;
  int32  respect_count = 16;
  int32 diss_count = 17;
  string   respect_toast = 18;
  SocialCommunityBackground social_community_background=19;     //社群背景
  map<uint32, bool> white_map = 20;// 是否有白名单 key:WhiteType
  uint32 limited_member_count = 21; //社群限制成员数
  uint32 limited_vice_captain_count = 22; //社群限制副主理人数量
}

message   GetSocialCommunityDetailInfoReq{
  uint32 channel_id = 1;
  string social_community_id = 2;
}

message    GetSocialCommunityDetailInfoResp{
  SocialCommunityDetail social_community = 1;
}

//message UpdateSocialCommunityLogoReq{
//  string social_community_logo = 1;
//  string social_community_id = 2;
//}

//message UpdateSocialCommunityLogoResp{
//
//}

message GetSocialCommunityFansCountReq{
  string social_community_id = 1;
}

message GetSocialCommunityFansCountResp{
  int64 count = 1;
}

message GetSocialCommunityKernelCountReq{
  string social_community_id = 1;
}

message GetSocialCommunityKernelCountResp{
  int64 count = 1;
}

message  GetSocialCommunityCaptionReq{
  string social_community_id = 1;
}

message GetSocialCommunityCaptionResp{
  uint32 uid = 1;
}

message BatchCategoriesByBrandIdsReq{
  repeated string social_community_ids = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BatchCategoriesByBrandIdsResp{
  map<string, Category> categoriesMap = 1;
}


message ListMuseSocialCommunityNavSecondaryBarsReq{
  string id = 1;
  uint32 uid = 2;
}

message ListMuseSocialCommunityNavSecondaryBarsResp{
  repeated MuseSocialCommunityNavSecondaryBar secondary_bars = 1;
  map<uint32, uint32> msg_type_count_map = 4;//0-评论，1-respect&diss
}

message MuseSocialCommunityNavSecondaryBar{
  oneof content{
    MuseSocialCommunityNavbarChannel channel = 1;
    MuseSocialCommunityGroup group = 2;
    MuseSocialCommunityAnnounce announce = 3;
    MuseSocialCommunityContentStream content_stream = 4;
  }
}

message MuseSocialCommunityContentStream {
  string  content_stream_id = 1;  //流类型的ID 分为干货or讨论or品类圈 指定前缀拼接社群id或品类id拼接讨论或干货
  uint32 stream_type = 2;  //内容流类型    干货or讨论or品类圈 ugc_non_public_.proto SceneStreamType
  string name = 3;
  string logo = 4;
  int64 unread_msg_count = 5;     //未读消息数量
  bool  is_category_circle = 6;  //是否是品类圈  false--不是   ture ----是品类圈（不展示数字）
  string scene_id = 7;
  uint32 user_permission = 8; // 权限 ugc_non_public.proto ContentStreamPermission 位运算 0001 0010 0100...，0:无权限

}
message MuseSocialCommunityAnnounce  {
  string name = 1;
  string desc = 2;
  string logo = 3;
  string lottie = 4;
  string lottie_md5 = 5;
  string bg_color = 6;//"#****"
  int64 unread_msg_count = 7;  //未读通告总数
}

message MuseSocialCommunityNavbarChannel {
  uint32 channel_id = 1;
  string name = 2;//不是房间名，是导航栏显示的名称
  string desc = 3;//描述文案
  string logo = 4;
  string lottie = 5;
  string lottie_md5 = 6;
  BrandChannelType channel_type = 7;
  string bg_color = 8;//"#****"

}


message MuseSocialCommunityGroup {
  uint32 group_id = 1;
  string name = 2;//不是群聊名，是导航栏显示的名称
  string desc = 3;//描述文案
  string logo = 4;
  string lottie = 5;
  string lottie_md5 = 6;
  string bg_color = 7;//"#****"
}

message ListMuseSocialCommunityNavBarsReq{
  uint32 channel_id = 1;//当前房间
  string extra_nav_id = 2;//上一次进入的社群导航Id
  uint32 uid = 3;
  string social_community_id = 4;
}

message ListMuseSocialCommunityNavBarsResp{
  repeated MuseSocialCommunityNavBar bars = 1;
}

enum MuseSocialCommunityNavBarFlag{
  MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_UNSPECIFIED = 0;
  MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_MINE = 1;
  MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_RECENT = 2;
  MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_TOURIST = 3;
}

message MuseSocialCommunityNavBar{
  string social_community_id = 1;
  string name = 2;
  string logo = 3;
  string bg_logo = 4;   //logo底图
  MuseSocialCommunityNavBarFlag flag = 5;
  string category_type_simple_desc = 6;
  repeated MuseSocialCommunityNavSecondaryBar secondary_bars = 7;//只有当前的bar有值
  uint32 brand_professionalism = 8; //1-专业，2-非专业
  uint32 level = 9; //社群等级
  string level_logo = 10;//社团等级logo
  int64  unread_msg_count = 11;   //每个社群的未读消息总数
}

message GetSocialCommunityProfilePagesReq{
  uint32   uid = 1;
  string     social_community_id = 2;
  uint32 other_user_uid_for_card = 3; //查看别人的社群名片时传入
}

message SocialCommunityMemberInfo{
  SimpleUserInfo user_info = 1;
  string intro = 2; // 成员介绍
  BrandMemberRole  role = 3; /* 角色 BrandMemberRole */
  string member_text = 4;
}

message SocialCommunityPhotoAlbumKeyURL{
  string key = 1;
  string url = 2;
}

message UserInfoInTheCommunity{
  SimpleUserInfo user_info = 1;
  uint32 joining_duration = 2; //加入时长
  BrandMemberRole  role = 3; /* 角色 BrandMemberRole */
  string member_text = 4;
  string intro = 5; // 成员介绍
  string category_type_simple_desc = 6;// 社团品类类型短文案
  PersonalCertForSocialCommunityUser personal_cert = 7; // 自定义认证标
}

message PersonalCertForSocialCommunityUser{
  string icon = 1;
  string text = 2;
  repeated string color = 3;
  string text_shadow_color = 4;
}

message GetSocialCommunityProfilePagesResp{
  repeated MuseSocialCommunityNavSecondaryBar secondary_bars = 2;//只有当前的bar有值
  SocialCommunityDetail  detail_info = 3;
  repeated SocialCommunityMemberInfo  member_list = 4;
  repeated SocialCommunityPhotoAlbumKeyURL photo_list = 5;
  string social_community_background_logo = 6;
  repeated string robot_url = 7;//机器人头像
  string topic_id = 8;//当前厂牌话题的id
  uint32 view_count = 9;//话题热度
  UserInfoInTheCommunity user_info = 10;
  uint32 social_community_owner_status = 13;//0-没有自己的社团，1-拥有自己的社团，2-自己的社团在审核中
  InviteMembersDisplay invite_member_display = 14;
  uint32 remaining_member_seats = 15; //剩余成员席位
  uint32 system_message_count = 16;
  int64 recent_view_count=18;       //最近访问量
  int64 increase_view_count=19;      //新增访问量
  NewProfileResource new_profile_resource = 20; // 档案页资源包
  UserInfoInTheCommunity other_user_info_for_card = 21;/*别人的社群名片*/
  MainSubCommunityInfo main_sub_community_info = 22; //主分社群信息
}

message InviteMembersDisplay{
  int64 dissolution_countdown = 1;//（社群解散倒计时,秒）
  uint32 invited_members = 2;  //已经邀请的成员数量
  uint32 invite_member_total = 3;//需要邀请的成员总数
}

message GetSocialCommunityAllMembersCountReq{
  string social_community_id = 1;
}

message GetSocialCommunityAllMembersCountResp{
  int64 count = 1;
}

message BatchGetSocialCommunityAllMembersCountReq {
  repeated string ids = 1;
}

message BatchGetSocialCommunityAllMembersCountResp {
  map<string, int64> counts = 1;
}


/*--------------------社群3.0--------------------------*/

message RemoveSocialCommunityMemberReq{
  string social_community_id = 1;
  uint32 target_uid = 2;     //被开除成员
  string reason = 3;      //说明理由
}

message RemoveSocialCommunityMemberResp{

}

message ExitSocialCommunityReq{
  string social_community_id = 1;

}

message ExitSocialCommunityResp{
  int64 count = 1;
}


/*社群5.0*/

message BatchGetOpenCategoryTypesReq{

}

message BatchGetOpenCategoryTypesResp{
  repeated CategoryType  category_types = 1;
}

message BatchGetCategoriesByCategoryTypeIdReq{
  string category_type_id = 1;
}

message BatchGetCategoriesByCategoryTypeIdResp{
  repeated Category categories = 1;
}


// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ApplyCreateSocialCommunityReq{
  string categoryId = 1;
  string name = 2;
  string intro = 3;
  string logo = 4;
  string vision = 5;
}

message ApplyCreateSocialCommunityResp{

}

message JoinSocialCommunityKernelReq{
  string social_community_id = 1;
  uint32 uid = 2;
  string invite_code = 3;
  bool is_auto_input_invite_code = 4;
}

message JoinSocialCommunityKernelResp{
  string social_community_id = 1;
}




/*社群群聊*/

message GetSocialCommunityBaseReq{
  uint32  channel_id = 1;
  uint32 group_id = 2;


}


message GetSocialCommunityBaseResp{
  string social_community_id = 1;
  uint32 level = 2;
  string level_logo = 3;
}



message GetGroupMemberLimitReq{
  uint32 group_id = 1;
}

message GetGroupMemberLimitResp{
  uint32 member_limit = 1;

}



message GetSocialCommunityKernelMembersReq{
  repeated string social_community_ids = 1;
}

message GetSocialCommunityKernelMembersResp{
  map<string, BrandMembers> kernel_member_map = 1; /* BrandMemberRoleV2 */

}

message BrandMembers{
  repeated BrandMember members = 1;
}

/*社群6.38*/

message GetSocialCommunityMemberListReq{
  string social_community_id = 1;
  string offset_id = 2;
  uint32 count = 3;
}


message GetSocialCommunityMemberListResp{
  repeated BrandMember kernel = 1;
  repeated BrandMember fans = 2;
}


message GetSocialCommunityAnnounceNewsCountMapRequest{
}

message GetSocialCommunityAnnounceNewsCountMapResponse{
  uint32 count = 1;
  map<string, uint32> brand_news_count_map = 2;
}

message  SetMuseSocialAnnounceInterestRequest{
  uint32 uid = 1;
  string muse_announce_id = 2;
  uint32 interest_type = 3;//0-感兴趣，1-取消感兴趣
}

message  SetMuseSocialAnnounceInterestResponse{
}

message RemoveMuseSocialAnnounceRequest{
  string muse_announce_id = 1;
}

message RemoveMuseSocialAnnounceResponse{


}

message  ListMuseSocialAnnounceInterestUsersRequest{
  string muse_announce_id = 1;
  uint32 limit = 2;
  string offset_id = 3;
}

message  ListMuseSocialAnnounceInterestUsersResponse{
  repeated uint32 uids = 1;
  string next_offset_id = 2;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BatchMuseSocialAnnounceByAnnounceIdsRequest{
  repeated string announceIds = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message BatchMuseSocialAnnounceByAnnounceIdsResponse{
  map<string, MuseSocialAnnounce> announceMap = 1;
}


message ValidateUserHasCreateAnnouncePermissionsRequest{
  string social_community_id = 1;
}

message  ValidateUserHasCreateAnnouncePermissionsResponse{

}


message UpsertApplyJoinCommunityWorkOrderRequest{
  string id = 1;
  string social_community_id = 2;
  uint32 uid = 3;
  string reason = 4;
  uint32 status = 5;
  string invite_code = 6; // 邀请码活动的邀请码
  uint32 invite_code_user_type = 7;// 邀请码活动的用户类型
  bool invite_code_auto_input = 8; // 邀请码是否自动输入
}

message UpsertApplyJoinCommunityWorkOrderResponse{

}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SearchApplyJoinCommunityWorkOrderRequest{
  string id = 1;
  string social_community_id = 2;
  uint32 uid = 3;
  repeated uint32 status = 4;
  string OffsetId = 5;
  uint32 limit = 6;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ApplyToJoinWorkerOrder{
  string id = 1;
  string social_community_id = 2;
  uint32 uid = 3;
  uint32 status = 4;
  string OffsetId = 5;
  uint32 limit = 6;
  string reason = 7;
  int64 create_time = 8;
}
message SearchApplyJoinCommunityWorkOrderResponse{
  repeated ApplyToJoinWorkerOrder order = 1;

}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ListApplyJoinCommunityWorkOrdersRequest{
  string social_community_id = 1;
  string OffsetId = 2;
  uint32 limit = 3;
}

message ListApplyJoinCommunityWorkOrdersResponse{
  repeated ApplyToJoinWorkerOrder order = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SubmitApplicationToJoinCommunityRequest{
  string socialCommunityId = 1;
  string reason = 2;
  uint32 uid = 3;
  string invite_code = 4;
  bool is_auto_input_invite_code = 5;
}

message SubmitApplicationToJoinCommunityResponse{


}

message RebuildAdditionModeReq{

}

message RebuildAdditionModeResp{}



message GetUserCategoryAttitudeCountRequest{
  string category_id = 1;
  uint32 uid = 2;
}

message GetUserCategoryAttitudeCountResponse{
  int32 respect_count = 1;
  int32 diss_count = 2;
}

message HasEditableSocialCommunityNameRequest{
  string social_community_id=1;
}


message HasEditableSocialCommunityNameResponse{
    bool   has_edit=1;
    int64  config_times=2;
}

message IncreaseEditSocialCommunityNameTimesRequest{
  string social_community_id=1;
}

message IncreaseEditSocialCommunityNameTimesResponse{

}

message ReportPersonalChannelViewSocialCommunityRequest{
     uint32 uid=1;
     uint32 channel_id=2;
}

message ReportPersonalChannelViewSocialCommunityResponse{
      bool report_status=1;   //false--未曝光   true  曝光
}

// 根据优先级获取用户社团对应的品类列表
message GetUserCategoryListRequest{
  uint32 uid = 1;
  uint32 limit = 2;
}

message CategoryWithCommunityID{
  string category_id = 1;
  string social_community_id = 2;
}
message GetUserCategoryListResponse{
  repeated CategoryWithCommunityID category_id_with_community_id = 1;
}


// 获取社团助手消息数
message GetSocialCommunityAssistantMsgCountRequest{
  uint32 uid = 1;
}

message GetSocialCommunityAssistantMsgCountResponse{
  uint32 msg_count = 2;
}

message GetRespectCountRequest {
  string social_community_id = 1;
}

message GetRespectCountResponse {
  int64 respect_count = 1;
}

message SendSocialCommunityActivityNoticeReq{
        uint32 uid=1;
        string post_id=2;
        bool  is_group_notice=3;      //是否需要群聊通知
}

message SendSocialCommunityActivityNoticeResp{

}

message SendPlaymateActivityNoticeReq{
  uint32 uid=1;
  string post_id=2;
}


message SendPlaymateActivityNoticeResp{

}

message SearchSocialCommunitySimpleInfoByNameRequest{
  string social_community_name = 1;
  int64 offset=2;
  int64 limit=3;
}

message SearchSocialCommunitySimpleInfoByNameResponse{
   repeated  SocialCommunitySimple social_community_simple=1;

}


// 品类圈合集
message CategoryCollection{
  string collection_id = 1; // 合集ID
  string collection_name = 2; // 合集名称
  string collection_icon = 3; // 合集图标
  string category_type_id = 4; // 品类类型
  repeated string category_id_list = 5; // 品类ID列表
  uint32 status = 6; // 合集状态 CategoryCollectionStatus
  uint32 create_at = 7; // 创建时间
  uint32 update_at = 8; // 更新时间
}

enum CategoryCollectionStatus{
  CategoryCollectionStatusDefault = 0;
  CategoryCollectionStatusNormal = 1; // 正常
  CategoryCollectionStatusInvalid = 2; // 失效
}

message ListCategoryCollectionReq{
  string collection_id = 1;
  string category_id = 2;
  string collection_name = 3;
  uint32 status = 4;
  uint32 offset = 5; // 分页查询的offset
  uint32 limit = 6;
}

message ListCategoryCollectionResp{
  repeated CategoryCollection collection_list = 1;
  uint32 total = 2;
}

// 获取品类圈合集 运营后台用
message GetCategoryCollectionReq{
  string collection_name = 1;
  uint32 status = 2;
}

message GetCategoryCollectionResp{
  map<string, string> collection_map = 1; // key: collection_name, value: collection_id
}

// 更新品类圈合集
message UpsertCategoryCollectionReq{
    CategoryCollection collection = 1;
}

message UpsertCategoryCollectionResp{
}

// CheckCategoryInCollection 检查品类是否在合集中
message CheckCategoryInCollectionReq{
  string category_id = 1;
  string category_type_id = 2;
}

message CheckCategoryInCollectionResp{
  string collection_name = 1;
}

// 主分社管理
message UpsertMainSubCommunityReq{
  string social_community_id = 1; // 社群ID
  MainSubCommunity main_sub_community = 2; // 主分社群
}

message UpsertMainSubCommunityResp{
}

message BindMainSubCommunityRecord{
  string social_community_id = 1;
  repeated string pic_url_list = 3; // 图片地址
}

message MainSubCommunity{
  repeated BindMainSubCommunityRecord main_community_list = 1; // 主社群
  repeated BindMainSubCommunityRecord sub_community_list = 2; // 分社群
}

message BatGetMainSubCommunityReq{
  repeated string social_community_id_list = 1; // 社群ID
}

message BatGetMainSubCommunityResp{
  map<string, MainSubCommunity> main_sub_community_map = 1;
}

// 主分社
message MainSubCommunityInfo{
  repeated SocialCommunitySimple sub_community_list = 1; // 子社群列表
  repeated SocialCommunitySimple main_community_list = 2; // 主社群列表
}

/*-----------------普通社群粉丝上限--------------------*/

message BatchSetAmateurSocialCommunityMemberLimitRequest{
  repeated string social_community_ids=1;
  int32  member_limit=2;
  string operator=3;
}

message BatchSetAmateurSocialCommunityMemberLimitResponse{

}


message DelAmateurSocialCommunityMemberLimitRequest{
  repeated string social_community_ids=1;
}

message DelAmateurSocialCommunityMemberLimitResponse{

}

message GetAmateurSocialCommunityMemberLimitRequest{
  string social_community_id=1;
  string offset_id=2;
  int32 limit=3;

}

message AmateurSocialCommunityMemberLimitConfig{
  string social_community_id=1;
  string social_community_name=2;
  uint32 captain_uid=3;
  int32 member_limit=4;
  int64 create_time=5;
  string operator=6;
  string offset_id=7;
  uint32  brand_status=8;
}

message GetAmateurSocialCommunityMemberLimitResponse{
  repeated AmateurSocialCommunityMemberLimitConfig config=1;
}

message AmateurSocialCommunityMemberLimitRequest{

}

message AmateurSocialCommunityMemberLimitResponse{
  
}


/*-----------------普通社群粉丝上限--------------------*/

// 社群通用白名单设置
// 覆盖设置
message SetWhiteIdsRequest{
  repeated string social_community_id_list = 1;
  uint32 white_type = 2; // WhiteType
  string operator = 3;
}

enum WhiteType{
  WhiteTypeNone = 0;
  WhiteTypeHideProfessionalismIcon = 1; // 隐藏专业标识
}

message SetWhiteIdsResponse{
}

message DelWhiteIdsRequest{
  repeated string social_community_id_list = 1;
  uint32 white_type = 2; // WhiteType
}

message DelWhiteIdsResponse{
}

message ListWhiteIdsRequest{
  repeated string social_community_id_list = 1;
  uint32 white_type = 2; // WhiteType 必填
  string offset_id = 3;
  uint32 limit = 4;
}

message WhiteIdInfo{
  string social_community_id = 1;
  bool is_white = 2;
  string operator = 3;
  string offset_id = 4;
}

message ListWhiteIdsResponse{
  repeated WhiteIdInfo white_id_info_list = 1;
}



//社群活动弹窗
message UpsertSocialCommunityActivityPopupRequest{
  SocialCommunityActivityPopup social_community_activity_popup=1;
}

message UpsertSocialCommunityActivityPopupResponse{
}

message SocialCommunityActivityPopup{
  string id=1;
  int32  group_package_numbers=2;
  string jump_url=3;
  int64  start_time=4;
  int64  end_time=5;
  string picture_url=6;
  uint32 push_frequency_type=7;   //0--仅首次    1---每日一次
  int64  create_time=8;
  string operator=9;
}

message GetSocialCommunityActivityPopupRequest{
  string id=1;
  uint32 status=2;  //0---null  1--未生效  2--生效中 3--已过期
  uint32 offset = 3;
  uint32 limit = 4;
}

enum  SocialCommunityActivityPopupStatus{
  NON_PUBLIC_POST_POPUP_STATUS_NONE=0;
  NON_PUBLIC_POST_POPUP_STATUS_NOT_YET=1;
  NON_PUBLIC_POST_POPUP_STATUS_VALID=2;
  NON_PUBLIC_POST_POPUP_STATUS_EXPIRED=3;
}

message GetSocialCommunityActivityPopupResponse{
  repeated SocialCommunityActivityPopup social_community_activity_popup_list=1;
  uint32 total = 2;
}


message DelSocialCommunityActivityPopupRequest{
  string id=1;
}

message DelSocialCommunityActivityPopupResponse{

}

message GetUserSocialCommunityActivityPopupRequest{
  uint32 uid=1;
}

message GetUserSocialCommunityActivityPopupResponse{
   SocialCommunityActivityPopup social_community_activity_popup=1;
    int32  next_time_popup=2;
}



// 社群用户信息查询
message BatGetSimpleSocialCommunityMembersRequest{
  repeated uint32 uid_list = 1; // limit 20个
}

message SimpleSocialCommunityMemberInfo{
  uint32 uid = 1;
  string social_community_id = 2;
  uint32 role = 3;
  uint32 member_status = 4;
  uint32 brand_professionalism = 5; //1-专业，2-非专业
}

// 返回请求的用户的所有的社群
message BatGetSimpleSocialCommunityMembersResponse{
  repeated SimpleSocialCommunityMemberInfo member_list = 1; // 每人 limit 50个
}

message GetAbnormalSocialCommunityRequest{

}

message GetAbnormalSocialCommunityResponse{
  uint32 count=1;
  repeated string social_community_id_list=2;
  repeated uint32 social_community_captain_uid_list=3;
}

message RemoveAbnormalSocialCommunityRequest{

}

message RemoveAbnormalSocialCommunityResponse{

}