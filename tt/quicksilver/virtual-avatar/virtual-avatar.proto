syntax = "proto3";

package virtual_avatar;
import "tt/quicksilver/extension/options/options.proto";
option go_package = "golang.52tt.com/protocol/services/virtual-avatar";

service VirtualAvatar {
    option (service.options.service_ext) = {
        service_name: "virtual-avatar"
      };

    // 设置虚拟形象配置/存在则更新
    rpc SetVirtualAvatarConfig(SetVirtualAvatarConfigReq) returns (SetVirtualAvatarConfigResp) {}
    // 全量获取虚拟形象配置
    rpc GetAllVirtualAvatarConfig(GetAllVirtualAvatarConfigReq) returns (GetAllVirtualAvatarConfigResp) {}
    // 获取虚拟形象配置
    rpc GetVirtualAvatarConfByVaId(GetVirtualAvatarConfByVaIdReq) returns (GetVirtualAvatarConfByVaIdResp) {}
    // 下架虚拟形象配置
    rpc DelVirtualAvatarConfig(DelVirtualAvatarConfigReq) returns (DelVirtualAvatarConfigResp) {}

    // 发放虚拟形象
    rpc GiveVirtualAvatarToUser(GiveVirtualAvatarToUserReq) returns (GiveVirtualAvatarToUserResp) {}

    // 查询用户关系列表
    rpc GetUserRelationList(GetUserRelationListReq) returns (GetUserRelationListResp) {}
    // 绑定用户关系
    rpc BindUserRelation(BindUserRelationReq) returns (BindUserRelationResp) {}
    // 取消绑定用户关系
    rpc UnBindUserRelation(UnBindUserRelationReq) returns (UnBindUserRelationResp) {}
    
    // 获取用户虚拟形象列表
    rpc GetUserVirtualAvatarList(GetUserVirtualAvatarListReq) returns (GetUserVirtualAvatarListResp) {}

    // 用户佩戴虚拟形象
    rpc SetVirtualAvatarInUse(SetVirtualAvatarInUseReq) returns (SetVirtualAvatarInUseResp) {}
    // 获取用户使用中的虚拟形象
    rpc GetVirtualAvatarInUse(GetVirtualAvatarInUseReq) returns (GetVirtualAvatarInUseResp) {}

    // 设置虚拟形象使用范围
    rpc SetUserVirtualAvatarUseScope(SetUserVirtualAvatarUseScopeReq) returns (SetUserVirtualAvatarUseScopeResp) {}
    // 获取虚拟形象使用范围
    rpc GetVirtualAvatarUseScope(GetVirtualAvatarUseScopeReq) returns (GetVirtualAvatarUseScopeResp) {}

    // 获取用户使用中的虚拟形象麦位资源
    rpc BatGetUserMicSpaceRes(BatGetUserMicSpaceResReq) returns (BatGetUserMicSpaceResResp) {}

    // 获取用户使用中的进房特效资源
    rpc GetUserEnterChannelRes(GetUserEnterChannelResReq) returns (GetUserEnterChannelResResp) {}
}

// 资源人物类型
enum ResCharacterType{
  RES_CHARACTER_TYPE_UNSPECIFIED = 0;
  RES_CHARACTER_TYPE_USER_A = 1;
  RES_CHARACTER_TYPE_USER_B = 2;
}

message VAResourceConf{
    string effect_res = 1;       // 特效资源
    string effect_res_md5 = 2;   // 特效资源md5
    uint32 res_type = 3;         // 资源类型，see VAResourceType
    uint32 va_id = 4;            // 虚拟形象id
}

// 一套资源
message VAResourceSet{  
    uint32 user_item_id = 1;
    uint32 display_type = 2;   // see VADisplayType
    repeated VAResourceConf res_list = 3;    // 虚拟形象资源列表
    bool in_use = 4;           // 正在使用中
    uint32 va_id = 5;          // 虚拟形象id
}

// 用户虚拟形象信息
message UserVirtualAvatarInfo{
    uint32 uid = 1;
    string name = 2;              // 名称
    string base_pic = 3;          // 虚拟形象静态预览图
    VAResourceSet res_set = 4;  // 虚拟形象资源

    uint32 cp_uid = 5; // 双人形象中，对方的用户信息
    string relate_name = 6;  // 关系标签名称
    uint32 user_char = 7;    // see ResCharacterType
    uint32 cp_user_char = 8; // see ResCharacterType

    int64 expire_ts = 10;     // 过期时间,秒级时间戳
    string relation_id = 11;  // 关系标签ID
}


// 虚拟形象配置信息
message VirtualAvatarConfig{
    uint32 va_id = 1;
    string name = 2;              // 名称
    string base_pic_a = 3;  // 缩略图
    string base_pic_b = 4;  // 缩略图
    string personal_page_effect = 5;  // 个人主页/资料卡特效
    string personal_page_effect_md5 = 6;  // 个人主页/资料卡特效md5
    string enter_channel_effect  = 7;  // 进房动效特效
    string enter_channel_effect_md5 = 8;  // 进房动效特效md5
    string mic_space_a_effect  = 9;  // 麦位动效
    string mic_space_a_effect_md5 = 10;  // 麦位动效md5
    string mic_space_b_effect = 11;  // 麦位动效
    string mic_space_b_effect_md5 = 12;  // 麦位动效md5

    string relation_id = 13; // 关系标签ID
}

// 设置虚拟形象配置
message SetVirtualAvatarConfigReq {
    VirtualAvatarConfig va_config = 1;
}

message SetVirtualAvatarConfigResp {
}

// 全量获取虚拟形象配置
message GetAllVirtualAvatarConfigReq {
}

message GetAllVirtualAvatarConfigResp {
    repeated VirtualAvatarConfig va_list = 1;  // 虚拟形象配置列表
}

// 获取虚拟形象配置
message GetVirtualAvatarConfByVaIdReq {
    uint32 va_id = 1;
}

message GetVirtualAvatarConfByVaIdResp {
    VirtualAvatarConfig va_info = 1;
}

// 下架虚拟形象配置
message DelVirtualAvatarConfigReq {
    uint32 va_id = 1;
}

message DelVirtualAvatarConfigResp {
}

// 发放虚拟形象
message GiveVirtualAvatarToUserReq {
    string order_id = 1;
    uint32 uid = 2;
    uint32 va_id = 3;
    uint32 character_type = 4;   // see ResCharacterType
    int32 duration_sec = 5;     // 发放时长(小于0时为回收)，秒级
}

message GiveVirtualAvatarToUserResp {
}

// 获取用户虚拟形象列表
message GetUserVirtualAvatarListReq{
    uint32 uid = 1; 
}

message GetUserVirtualAvatarListResp{
    repeated UserVirtualAvatarInfo va_list = 1;  // 用户虚拟形象列表
    uint32 auto_play_duration_sec = 2;  // 虚拟形象资源自动间隔时间，秒
}


// 用户佩戴虚拟形象
message SetVirtualAvatarInUseReq {
    uint32 uid = 1;     // 用户ID
    uint32 user_item_id = 2;
    uint32 display_type = 3;   // see VADisplayType
}

message SetVirtualAvatarInUseResp {
}

// 获取用户使用中的虚拟形象
message GetVirtualAvatarInUseReq {
    uint32 uid = 1;     // 用户ID
}

message GetVirtualAvatarInUseResp {
    UserVirtualAvatarInfo va_info = 1;  // 用户虚拟形象信息
    repeated uint32 use_scope_list = 2;  // see VAUseScopeType
}

// 设置虚拟形象使用范围
message SetUserVirtualAvatarUseScopeReq {
    uint32 uid = 1;
    uint32 use_scope = 2;   // 使用范围,see logic VAUseScopeType
    uint32 op_type = 3;     // 操作类型,see logic SetVirtualAvatarUseScopeRequest.OpType
}

message SetUserVirtualAvatarUseScopeResp {
}

// 获取虚拟形象使用范围
message GetVirtualAvatarUseScopeReq {
    uint32 uid = 1;
}

message GetVirtualAvatarUseScopeResp {
    repeated uint32 use_scope = 1;   // 使用范围,see logic VAUseScopeType
}

message UserRelation {
  uint32 uid = 1;
  uint32 cp_uid = 2;
  string relation_id = 3;
  string relate_name = 4;  // 关系标签名称
  int64 expire_ts = 5;     // 过期时间,秒级时间戳
}

// 查询用户关系列表
message GetUserRelationListReq {
    uint32 uid = 1;
}

message GetUserRelationListResp {
    repeated UserRelation relation_list = 1;
}

// 绑定用户关系
message BindUserRelationReq {
    uint32 uid_a = 1;
    uint32 uid_b = 2;
    string relation_id = 3;
    string relate_name = 4;  // 关系标签名称
    uint32 duration_sec = 5; // 发放时长，秒级
}

message BindUserRelationResp {
}

// 取消绑定用户关系
message UnBindUserRelationReq {
    uint32 uid_a = 1;
    uint32 uid_b = 2;
    string relation_id = 3;
}

message UnBindUserRelationResp {
}

message UserVAStatusInfo{
    uint32 uid = 1;
    uint32 item_id = 2;
    uint32 display_type = 3;   // see VADisplayType
    uint32 va_id = 4;
    uint32 char_type = 5;
    int64 expire_ts = 6;
    bool in_use = 7;
}

message BatGetUserMicSpaceResReq{
    repeated uint32 uid_list = 1;
}

message BatGetUserMicSpaceResResp{
    map<uint32, VAResourceConf> map_uid_info = 1;  // <uid, VAResourceConf>
}

message GetUserEnterChannelResReq{
    uint32 uid = 1;
    uint32 follow_uid = 2;
}

message GetUserEnterChannelResResp{
    string effect_res = 1;       // 特效资源
    string effect_res_md5 = 2;   // 特效资源md5
    uint32 user_char = 3;       // see ResCharacterType
    uint32 cp_user_char = 4;    // see ResCharacterType
}