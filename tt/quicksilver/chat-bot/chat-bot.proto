syntax = "proto3";

package chat_bot;

import "tt/quicksilver/aigc/aigc-soulmate-middle/aigc-soulmate-middle.proto";

option go_package = "golang.52tt.com/protocol/services/chat-bot";

service ChatBot {
    // 发送 IM 消息给指定用户,真实用户
    rpc SendImMsgToUser(SendImMsgToUserReq) returns (SendImMsgToUserResp) {}
    // 创建聊天机器人
    rpc CreateChatBot(CreateChatBotReq) returns (CreateChatBotResp) {}
    // 标记 IM 消息已读
    rpc MarkImMsgRead(MarkImMsgReadReq) returns (MarkImMsgReadResp) {}
    // AI伴侣统一推送(中台->WEB)
    rpc SendAIPartnerPush(SendAIPartnerPushReq) returns (SendAIPartnerPushResp) {}
    // AI伴侣发消息给用户
    rpc SendImMsgAIToUser(SendImMsgAIToUserReq) returns (SendImMsgAIToUserResp) {}
    // 发送群消息
    rpc SendGroupMsg(SendGroupMsgReq) returns (SendGroupMsgResp) {}
}

// IM 消息类型
enum ImMsgType {
    ImMsgTypeUnknown = 0;
    // 文本
    ImMsgTypeText = 1;
    // CUE
    ImMsgTypeCue = 2;
    // 表情
    ImMsgTypeEmoticon = 3;
    // 沉默(AI伴侣由活跃切为沉默)
    ImMsgTypeSilence = 4;
    // 传送门
    ImMsgTypeAirTicket = 5;
    // AI伴侣统一消息通道
    ImMsgAIPartner = 6;
}

// 触发消息类型
enum ImTriggerMsgType {
    ImTriggerMsgUnknown = 0;
    // 回复用户
    ImTriggerMsgRespUser = 1;
    // 主动触发
    ImTriggerMsgAiTrigger = 2;
    // 附加消息
    ImTriggerMsgRespUserExtraInfo = 3;
    // 玩法命令下发
    ImTriggerMsgPlayCmd = 4;
}

// 送审结果类型
enum AuditResult {
    // 机审无法识别
    AuditResultReview = 0;
    // 通过
    AuditResultPass = 1;
    // 不通过
    AuditResultReject = 2;
}

// used by ImMsgTypeSilent
message ImMsgExtSilence {
    // 点击后变成活跃
    string active_text = 1;
    // 点击后变成沉默
    string silent_text = 2;
}

// used by ImMsgTypeAirTicket 
message ImMsgExtAirTicket {
    uint32 channel_id = 1;
    // 房间麦位模式 see ga.EChannelMicMode
    uint32 channel_mode = 2;
    // 房间类型 see ga.ChannelType
    uint32 channel_type = 3;
    // 房间外显id
    string channel_view_id = 4;
    // 房主account
    string channel_creator_account = 5;
    // 房间名称
    string channel_name = 6;

    // 玩法id
    uint32 tab_id = 7;
    // 玩法名称(用于拼接im消息外显文案)
    string tab_name = 8;

    // 传送门标题
    string title = 9;
    // 传送门内容
    string content = 10;
}

//多人群组开始起用
//内容类型
enum ImMsgContentType {
    IM_MSG_CONTENT_TYPE_UNSPECIFIED = 0;
    // 文本
    IM_MSG_CONTENT_TYPE_TEXT = 1;
    // 文本+语音
    IM_MSG_CONTENT_TYPE_TEXT_VOICE = 2;
    // 表情
    IM_MSG_CONTENT_TYPE_EMOTION = 3;
    // 图片
    IM_MSG_CONTENT_TYPE_IMAGE = 4;
}

//业务类型
enum ImBusiType {
    //未知类型
    IM_BUSI_TYPE_UNSPECIFIED = 0;
    // 单人群聊
    IM_BUSI_TYPE_SINGLE_GROUP = 1;
    // 多人群聊
    IM_BUSI_TYPE_MULTI_GROUP = 2;
    // 多角色聊天
    IM_BUSI_TYPE_MULTI_ROLE = 3;
}

//命令类型 非内容消息需要指定业务类型前缀
enum ImCmdType {
    //未知类型
    IM_CMD_TYPE_UNSPECIFIED = 0;
    // 内容消息
    IM_CMD_TYPE_CONTENT_MSG = 1;
}

message ImMsg {
    // 消息类型
    ImMsgType type = 1;
    // 消息内容
    string content = 2;
    // 扩展消息
    bytes ext = 3;

    // 消息发送时间(毫秒)
    int64 sent_at = 4;
    // 消息唯一标识
    string msg_id = 5;

    // 消息来源类型 see ga.MsgSourceType
    uint32 msg_source_type = 6;

    // 来自文案
    string come_from = 7;
    uint32 seq_id = 8; // 消息序号,用于群组
    uint32 im_busi_type = 9; //业务类型 ImBusiType
    uint32 content_type = 10; //内容类型 ImMsgContentType
    uint32 im_cmd_type = 11; //命令类型 ImCmdType
}

message AIPartnerPushMsg {
    // 推送类型(中台与WEB对接)
    uint32 type = 1;
    // 推送内容
    bytes data = 2;
}

message SendOption {
    // 是否发送离线推送
    bool with_offline_push = 1;
    // 离线推送内容
    string offline_push_content = 2;

    // 是否送审
    bool with_audit = 3;
    // 送审文本内容
    string audit_text = 4;
}

message SendImMsgToUserReq {
    // 发送者 uid
    uint32 sender_uid = 1;
    // 接收者 uid
    uint32 receiver_uid = 2;

    // im 消息内容
    ImMsg msg = 3;

}

message SendImMsgToUserResp {
    // 发送内容审核结果
    AuditResult audit_result = 1;
    // 接收者消息 id
    uint64 receiver_msg_id = 2;
    // 发送者消息 id
    uint64 sender_msg_id = 3;
}

message Bot {
    uint32 uid = 1;
    string username = 2;

    // 克隆用户信息的uid
    uint32 clone_uid = 3;
    // 用户昵称
    string nickname = 4;
    // 用户性别
    int32 sex = 5;
}

message CreateChatBotReq {
    Bot bot = 1;
}

message CreateChatBotResp {
    Bot bot = 1;
}

message MarkImMsgReadReq {
    uint32 uid = 1;
    uint32 msg_id = 2;

    uint32 peer_uid = 3;
    uint32 peer_msg_id = 4;
}

message MarkImMsgReadResp {
    uint32 peer_read_msg_id = 1;
}

message SendImMsgAIToUserReq {
    message Partner {
        string avatar = 1;
        string hint = 2;
        bool show_hint = 3;
    }

    // AI伴侣id
    uint32 partner_id = 1;
    // 用户id
    uint32 uid = 2;

    ImMsg msg = 3;

    // 发送消息可选参数
    SendOption opt = 4;
    // 伴侣附加信息
    Partner extra_partner = 5;

    uint32 msg_round_id = 6; // 消息轮次id
    uint32 text_words_number = 7; // 文本字数
    uint32 segment_cnt = 8; // 消息分段数
    uint32 segment_index = 9; // 消息分段索引

    ImTriggerMsgType trigger_msg_type = 10; // 触发消息类型

    uint32 sentence_type = 11; // 消耗的句数类型，见aigc-soulmate-middle.proto SentenceType

    string req_msg_id = 12;  //应答给的消息id

    map<string, string> ext_map = 13; // 扩展字段
}

message SendImMsgAIToUserResp {
    AuditResult result = 1;
    // 消息唯一标识
    string msg_id = 3;
    // 是否需要发送专属句数消耗完提示
    bool need_send_sentence_hint = 4;
}

message SendAIPartnerPushReq {
    AIPartnerPushMsg msg = 1;

    // 接收推送的用户ID
    uint32 uid = 2;
    // AI伴侣ID
    uint32 partner_id = 3;
}

message SendAIPartnerPushResp {
}

message GroupMsgIndex {
  uint32 total_ai_count = 1; // 回复该消息的AI伴侣数量
  uint32 cur_ai_index = 2; // 当前回复的AI伴侣的编号
  uint32 cur_ai_msg_count = 3; // 当前AI伴侣回复的消息数量
  uint32 cur_ai_msg_index = 4; // 当前AI伴侣回复的消息编号

}

message SendGroupMsgReq {
    aigc_soulmate_middle.GroupTimeLineMsg time_line_msg = 1;
    bool is_trigger = 2; //主动触发

    // 发送消息可选参数
    SendOption opt = 3;

    uint32 req_seq_id = 4;  //应答给的消息id

    map<string, string> ext_map = 5; // 中台透传的扩展字段

    GroupMsgIndex group_msg_index = 6; // 群组消息索引
}

message SendGroupMsgResp {
    AuditResult result = 1;
    // 消息唯一标识
    uint32 seq_id = 2;
}
