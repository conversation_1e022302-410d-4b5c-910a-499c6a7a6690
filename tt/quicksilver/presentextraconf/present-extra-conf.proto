syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/presentextraconf";
package presentextraconf;

service PresentExtraConf {
  rpc GetPopUpPresentList (GetPopUpPresentListReq) returns (GetPopUpPresentListResp) {
  }

  // 按id/名称搜索礼物
  rpc SearchPresent (SearchPresentReq) returns (SearchPresentResp){
  }

  // 获取已有礼物浮层的礼物列表
  rpc GetPresentFloatLayer (GetPresentFloatLayerReq) returns (GetPresentFloatLayerResp){
  }
  // 添加礼物浮层
  rpc AddPresentFloatLayer (AddPresentFloatLayerReq) returns (AddPresentFloatLayerResp){
  }
  //修改礼物浮层
  rpc UpdatePresentFloatLayer (UpdatePresentFloatLayerReq) returns (UpdatePresentFloatLayerResp){
  }
  // 删除礼物浮层
  rpc DelPresentFloatLayer (DelPresentFloatLayerReq) returns (DelPresentFloatLayerResp){
  }

  // 获取闪光效果库
  rpc GetFlashEffectConfig (GetFlashEffectConfigReq) returns (GetFlashEffectConfigResp){
  }
  // 添加闪光效果
  rpc AddFlashEffectConfig (AddFlashEffectConfigReq) returns (AddFlashEffectConfigResp){
  }
  // 更新闪光效果
  rpc UpdateFlashEffectConfig (UpdateFlashEffectConfigReq) returns (UpdateFlashEffectConfigResp){
  }
  // 删除闪光效果
  rpc DelFlashEffectConfig (DelFlashEffectConfigReq) returns (DelFlashEffectConfigResp){
  }

  // 获取已绑定闪光效果的礼物列表
  rpc GetPresentFlashEffect (GetPresentFlashEffectReq) returns (GetPresentFlashEffectResp){
  }
  // 绑定闪光效果(更新、删除都用此接口)
  rpc BoundPresentFlashEffect (BoundPresentFlashEffectReq) returns (BoundPresentFlashEffectResp){
  }

  // 专属定制礼物相关
  rpc GetUserCustomizedInfo (GetUserCustomizedInfoReq) returns (GetUserCustomizedInfoResp){
  }
  rpc GetUserCustomizedInfoByGiftId (GetUserCustomizedInfoByGiftIdReq) returns (GetUserCustomizedInfoByGiftIdResp){
  }
  rpc ReportCustomOptionChoose (ReportCustomOptionChooseReq)returns (ReportCustomOptionChooseResp){
  }
  rpc ReportCustomPresentSend (ReportCustomPresentSendReq)returns (ReportCustomPresentSendResp){
  }
  rpc CheckCustomizedGift (CheckCustomizedGiftReq) returns (CheckCustomizedGiftResp){
  }
  rpc NotifyPrivilegeLevelChange (NotifyPrivilegeLevelChangeReq) returns (NotifyPrivilegeLevelChangeResp){
  }
  rpc GetCustomPresentEffectTime (GetCustomPresentEffectTimeReq) returns (GetCustomPresentEffectTimeResp){
  }

  // 运营后台用
  rpc AddCustomizedPresentConfig (AddCustomizedPresentConfigReq) returns (AddCustomizedPresentConfigResp){
  }
  rpc DelCustomizedPresentConfig (DelCustomizedPresentConfigReq) returns (DelCustomizedPresentConfigResp){
  }
  rpc UpdateCustomizedPresentConfig (UpdateCustomizedPresentConfigReq) returns (UpdateCustomizedPresentConfigResp){
  }
  rpc GetCustomizedPresentConfig (GetAllCustomizedPresentConfigReq) returns (GetAllCustomizedPresentConfigResp){
  }

  // 获取限时礼物信息
  rpc GetPresentEffectTime (GetPresentEffectTimeReq) returns (GetPresentEffectTimeResp){}
  // 获取限时礼物详情
  rpc GetPresentEffectTimeDetail (GetPresentEffectTimeDetailReq) returns (GetPresentEffectTimeDetailResp){}
  // 通知限时礼物送出
  rpc NotifyPresentSend (NotifyPresentSendReq) returns (NotifyPresentSendResp){}
  // 运营后台用
  rpc AddEffectDelayLevel(AddEffectDelayLevelReq) returns (AddEffectDelayLevelResp){}
  rpc GetEffectDelayLevel(GetEffectDelayLevelReq) returns (GetEffectDelayLevelResp){}
  rpc UpdateEffectDelayLevel(UpdateEffectDelayLevelReq) returns (UpdateEffectDelayLevelResp){}
  rpc DelEffectDelayLevel(DelEffectDelayLevelReq) returns (DelEffectDelayLevelResp){}

}



// 获取送礼需要二次弹窗的礼物列表
message GetPopUpPresentListReq {
}
message GetPopUpPresentListResp {
  repeated uint32 id_list = 1;
}


message SearchPresentReq {
  uint32 key_id = 1;
  string key_name = 2;
  uint32 page = 3;  // 页数，从1开始
  uint32 count = 4;  // 每页个数， count =0 返回所有结果
}

message SearchPresentResp {
  repeated PresentBaseConfig present_configs = 1;
  uint32 total = 2;
}

// 获取已有礼物浮层的礼物列表
message GetPresentFloatLayerReq {
  uint32 page = 1;
  uint32 count = 2;
  uint32 key_id = 3;
  string key_name = 4;
  uint32 status = 5;
}

message GetPresentFloatLayerResp {
  repeated PresentFloatInfo layer_infos = 1;
  uint32 total = 2;
  uint32 last_update_time = 3;
}

// 给礼物添加礼物浮层
message AddPresentFloatLayerReq {
  PresentFloatLayer layer = 1;
}

message AddPresentFloatLayerResp {
}

// 更新礼物的礼物浮层
message UpdatePresentFloatLayerReq {
  PresentFloatLayer layer = 1;
}

message UpdatePresentFloatLayerResp {
}

// 删除礼物的礼物浮层
message DelPresentFloatLayerReq {
  uint32 gift_id = 1;
}

message DelPresentFloatLayerResp {
}

message PresentFloatInfo {
  PresentFloatLayer layer_info = 1;
  PresentBaseConfig present_config = 2;
}

message PresentFloatLayer {
  uint32 gift_id = 1;
  string float_image_url = 2; // 浮层图片地址
  string jump_url = 3; // 跳转链接
  bool is_activity_url = 4; // 是否是活动链接
  uint32 effect_begin = 5;
  uint32 effect_end = 6;
  string operator = 7;  // 最后操作人
  uint32 effect_status = 8; // 生效状态 see EffectStatus
  uint32 update_time = 9; // 更新时间
  enum ChannelType{
    INVALID_CHANNEL_TYPE = 0; // 无效值
    GUILD_TYPE = 1;           // 普通公会房
    FREE_TYPE = 2;            // 自由房间
    USER_CHANNEL_TYPE = 3;    // 个人房
    GUILD_PUBLIC_FUN_CHANNEL_TYPE = 4; // 娱乐房
    TRIVIA_GAME_CHANNEL_TYPE = 5;  // 知识问答游戏房
    TEMP_KH_CHANNEL_TYPE = 6;    // 临时开黑房间
    RADIO_LIVE_CHANNEL_TYPE = 7; // 直播房
    GUILD_HOME_CHANNEL_TYPE = 8; // 公会主房间
    OFFICIAL_LIVE_CHANNEL_TYPE = 9; // 官方直播房
    CPL_SUPER_CHANNEL_TYPE = 10; // cpl活动大房
    COMMUNITY_CHANNEL_TYPE = 11; // 社群房间
  }
  repeated ChannelType show_channel_type = 10; // 需要展示的房间类型
  enum AppType {
    APP_TYPE_INVALID = 0; // 无效值
    APP_TYPE_PC = 1;      // PC
    APP_TYPE_TT_ANDROID = 2; // 安卓
    APP_TYPE_TT_IOS = 3;     // IOS
    APP_TYPE_HUANYOU_IOS = 4;      // 欢游ios
    APP_TYPE_HUANYOU_ANDROID = 5;      // 欢游安卓
    APP_TYPE_MAIKE_IOS = 6;      // 麦可ios
    APP_TYPE_MAIKE_ANDROID = 7;      // 麦可安卓
  }
  repeated AppType show_app_type = 11; // 需要展示的客户端和马甲包类型
  repeated AppUrl app_jump_url = 12; // 不同马甲包的链接
  enum ActivityType {
    ACTIVITY_TYPE_INVALID = 0; // 无效值
    ACTIVITY_TYPE_USER = 1; // 用户活动
    ACTIVITY_TYPE_REVENUE = 2; // 营收活动
  }
  ActivityType activity_type = 13; // 活动类型
  enum SubActivityType {
    //     1. 营收福利活动
    //    2. 付费人数活动
    //    3. 主播玩法活动
    //    4. 大型赛事活动
    SUB_ACTIVITY_TYPE_INVALID = 0; // 无效值
    SUB_ACTIVITY_TYPE_REVENUE_BENEFIT= 1; // 营收福利活动
    SUB_ACTIVITY_TYPE_PAY = 2; // 付费人数活动
    SUB_ACTIVITY_TYPE_ANCHOR = 3; // 主播玩法活动
    SUB_ACTIVITY_TYPE_BIG_EVENT = 4; // 大型赛事活动
  }
  SubActivityType sub_activity_type = 14; // 子活动类型
  string activity_name = 15; // 活动名称
}

message AppUrl {
  enum UrlType {
    URL_TYPE_INVALID = 0; // 无效值
    URL_TYPE_TT = 1;      // PC/TT安卓/TT IOS
    URL_TYPE_HUANYOU_IOS = 2;      // 欢游ios
    URL_TYPE_HUANYOU_ANDROID = 3;      // 欢游安卓
    URL_TYPE_MAIKE_IOS = 4;      // 麦可ios
    URL_TYPE_MAIKE_ANDROID = 5;      // 麦可安卓
  }
  UrlType url_type = 1; // 客户端类型
  string url = 2; // 对应的浮层链接
}

message PresentBaseConfig {
  uint32 gift_id = 1;
  string gift_name = 2; // 礼物名
  uint32 price_value = 3; // 礼物价值
  uint32 price_type = 4; // 价值类型 1 红钻 2 t豆
  string gift_image = 5; // 预览图
}

message PresentFlashEffect {
  uint32 gift_id = 1;
  uint32 flash_id = 2; // 光效的id
}

message FlashEffectConfig {
  uint32 flash_id = 1;
  string flash_name = 2; // 特效名
  string flash_url = 3; // 资源包地址
  string flash_md5 = 4; // 资源包md5
  string operator = 5; // 操作人
  uint32 create_time = 6; // 创建时间
  uint32 update_time = 7; // 更新时间
}

message GetFlashEffectConfigReq {
  uint32 page = 1;
  uint32 count = 2;
  string key_name = 3;
}

message GetFlashEffectConfigResp {
  repeated FlashEffectConfig flash_effects = 1;
  uint32 total = 2;
}

message AddFlashEffectConfigReq {
  FlashEffectConfig flash_effect = 1;

}

message AddFlashEffectConfigResp {
}

message UpdateFlashEffectConfigReq {
  FlashEffectConfig flash_effect = 1;
}

message UpdateFlashEffectConfigResp {
}

message DelFlashEffectConfigReq {
  uint32 flash_id = 1;
}

message DelFlashEffectConfigResp {
}

message PresentFlashInfo {
  FlashEffectConfig flash_info = 1;
  PresentBaseConfig present_config = 2;
  uint32 effect_begin = 3;
  uint32 effect_end = 4;
  uint32 create_time = 5;
  string operator = 6;
  uint32 effect_status = 7; // 生效状态 see EffectStatus
  uint32 update_time = 8; // 更新时间
}

message GetPresentFlashEffectReq {
  uint32 page = 1;
  uint32 count = 2;
  string key_effect_name = 3;
  uint32 key_gift_id = 4;
  string key_gift_name = 5;
}

message GetPresentFlashEffectResp {
  repeated PresentFlashInfo present_effects = 1;
  uint32 total = 2;
  uint32 last_update_time = 3;
}

// 绑定接口，新增/修改/解绑都用这个
message BoundPresentFlashEffectReq {
  repeated uint32 gift_id = 1;
  uint32 flash_id = 2;  // =0 则解绑
  uint32 effect_begin = 3;
  uint32 effect_end = 4;
  uint32 create_time = 5;
  string operator = 6;
}

message BoundPresentFlashEffectResp {
}

enum EffectStatus {
  EffectStatus_Unknown = 0;
  EffectStatus_Waiting = 1;  // 待生效
  EffectStatus_Effective = 2;  // 生效
  EffectStatus_Outdated = 3;  // 过期
}

message GetUserCustomizedInfoReq{
  uint32 uid = 1;
}

message GetUserCustomizedInfoResp{
  repeated CustomizedPresentInfo present_info = 1; // 定制礼物相关信息
  uint32 last_update_ts = 2;
  repeated PresentEffectAppend present_effect_append = 3; // 权限发放
}

// 礼物权限发放
message PresentEffectAppend{
  uint32 gift_id = 1;
  uint32 effect_begin = 2;
  uint32 effect_end = 3;
}

// 自定义礼物用户部分的信息
message UserCustomizedInfo{
  uint32 id = 1;  // 定制礼物id，也是其主礼物的礼物id
  repeated CustomOption option = 2; // 使用的组件信息
  uint32 authority_level = 3; // 权限等级
}

// 自定义礼物的信息，部分礼物本身的配置信息可以直接从礼物缓存中获取
message CustomizedPresentInfo{
  uint32 id = 1;  // 定制礼物id，也是其主礼物的礼物id
  repeated string custom_option = 2; // 当前使用的组件名
  uint32 present_id = 3; // 目前组件对应的子礼物id
  string cms_url = 4; // 定制礼物的cms链接地址
  bool has_authority = 5; // 用户是否有此定制礼物权限
  bool has_new_custom = 6; // 用户是否有新的样式可用（显示红点）
  uint32 effect_begin = 7; // 上架时间，因为有权限因素，定制礼物的上下架需要用单独的时间控制
  uint32 effect_end = 8; // 下架时间，因为有权限因素，定制礼物的上下架需要单独的时间控制
}

message GetUserCustomizedInfoByGiftIdReq{
  uint32 uid = 1;
  uint32 id = 2; // 定制礼物id
}

message GetUserCustomizedInfoByGiftIdResp{
  CustomizedPresentDetail present_detail = 1; // 定制礼物详情
}

message CustomizedPresentDetail{
  uint32 id = 1;  // 定制礼物id，也是其主礼物的礼物id
  repeated CustomOption custom_option = 2; // 可用的组件
  //uint32 present_id = 3; // 子礼物id
  // 另一个做法，客户端本地计算，好处延迟低，坏处是逻辑不方便更改：
  map<string, uint32> custom_method = 3;
  //组件选择与子礼物id的对应公式, key: 类似 1_2_3 的字符串，下划线分割，代表组件id1、2、3分别选择样式1、2、3; value: 对应的子礼物id
  uint32 user_level = 4; // 自己拥有的权限等级
  string level_text = 5; // 专属礼物的等级说明
  string colorful_text = 6; // 用于匹配level_text，将匹配到的部分变色
  map<uint32, CustomPresentPreview> preview_map = 7;  // 预览图，key：礼物id value: 预览的类型和url
}

message CustomPresentPreview{
  uint32 preview_type = 1;  // see CustomPreviewType
  string preview_url = 2;
  string preview_md5 = 3;
}

enum CustomPreviewType {
  CustomPreviewImg = 0;  // 图片
  CustomPreviewMp4 = 1;  // 视频
}

message CustomOption {
  uint32 custom_id = 1;  // 部件id
  string custom_name = 2;  // 部件名
  repeated OptionInfo option_info = 3; // 可选样式
  string custom_text = 4;  // 部件说明
}

message OptionInfo {
  uint32 option_id = 1;  // 样式id
  string option_name = 2;  // 样式名
  uint32 option_level = 3; // 样式所需的权限等级
  bool is_new = 4;  // 是否是新的样式（显示红点）
  bool is_active = 5; // 是否选中
}

message CustomOptionConfig {
  uint32 custom_id = 1; // 样式id
  uint32 option_id = 2; // 部件id
}


message ReportCustomOptionChooseReq{
  uint32 uid = 1;  // uid
  uint32 id = 2;  // 定制礼物的id
  repeated CustomPair custom_pair = 3; // 目前的部件样式选择
}

message ReportCustomOptionChooseResp{
}

message CustomPair{
  uint32 custom_id = 1;  // 部件id
  uint32 option_id = 2;  // 样式id
}

// 获取是否是限时礼物，以及是否有限时礼物权限
message CheckCustomizedGiftReq{
  uint32 uid = 1;
  uint32 id = 2; // 礼物id
}

message CheckCustomizedGiftResp{
  bool is_able = 1; // 是否可用
  uint32 primary_gift_id = 2 ;  // 对应的主礼物id
}

// userpresent-api.proto

// 增加礼物配置
message AddCustomizedPresentConfigReq
{
  CustomizedPresentConfig config = 1;
}

message AddCustomizedPresentConfigResp
{
}

message CustomizedPresentConfig{
  uint32 gift_id = 1;       // 专属礼物id， 插入时不用填
  string cmd_url = 2;  // 专属礼物的cms链接
  uint32 max_level = 3; // 最高权限等级
  repeated LevelEffectConfig effect_config = 4; // 不同权限等级对应的上架时间
  repeated CustomConfig custom_config = 5; // 可用的组件与样式
  repeated LevelConfig level_config = 6;   // 不同等级具有的组件权限
  repeated CustomMethod custom_method = 7; // 样式与礼物特效的对应关系
  string level_text = 8; // 专属礼物的等级说明
  uint32 update_time = 9;    // 更新时间
  uint32 create_time = 10;    // 添加时间
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CustomConfig{
  uint32 custom_id = 1; // 部件id
  string custom_name = 2;  // 部件名
  string custom_text = 3;  // 部件说明
  repeated OptionConfig OptionConfig = 4;  // 选项
}

message OptionConfig{
  uint32 option_id = 1; // 样式id
  string option_name = 2;  // 样式名
}

message LevelConfig{
  uint32 level = 1; // 权限等级
  repeated CustomOptionConfig custom_option = 2; // 该等级可用的组件
}

message CustomMethod{
  repeated CustomOptionConfig options = 1;
  uint32 gift_id = 2;
  uint32 preview_type = 3;
  string preview_url = 4;
  string preview_md5 = 5;
}


message LevelEffectConfig{
  uint32 level = 1;   // 权限等级
  uint32 effect_begin = 2;
  uint32 effect_end = 3;
}

// 更新礼物配置
message UpdateCustomizedPresentConfigReq
{
  CustomizedPresentConfig config = 1;
}

// 更新礼物配置
message UpdateCustomizedPresentConfigResp
{
}


// 获取礼物配置
message GetAllCustomizedPresentConfigReq
{

}

message GetAllCustomizedPresentConfigResp{
  repeated CustomizedPresentConfig config = 1;
}

// 删除礼物配置
message DelCustomizedPresentConfigReq
{
  uint32 id = 1; // 要删除的礼物id
}

message DelCustomizedPresentConfigResp{
}

message NotifyPrivilegeLevelChangeReq{
  uint32 uid = 1;
  uint32 value_type = 2;  // see ValueType
  uint64 before_value = 3;
  uint64 after_value = 4;
}

message NotifyPrivilegeLevelChangeResp{
}

enum ValueType{
  ValueTypeNone = 0;
  ValueTypeRich = 1;
}

message GetCustomPresentEffectTimeReq{
  uint32 gift_id = 1;
  uint32 level = 2;
}

message GetCustomPresentEffectTimeResp{
  uint32 begin = 1;
  uint32 end = 2;
}

message GetPresentEffectTimeReq{
  uint32 uid = 1;
}

message GetPresentEffectTimeResp{
  repeated PresentEffectTime present_effect_time_infos = 1;
}

message PresentEffectTime{
  uint32 gift_id = 1;  // 礼物id
  uint32 effect_end = 2; // 新的下架时间, 0代表无限期
  PresentEffectTimeInfo effect_info = 3; // 限时礼物延期的相关信息
  bool is_new_level = 4; // 是否为新等级（新等级需要弹窗）
}

message PresentEffectTimeInfo{
  uint32 now_count = 1;  // 目前已送个数
  uint32 next_level_send_count = 2; // 下个等级所需赠送个数
  uint32 next_level_day_count = 3; // 下个等级延长天数，0为无限期
  uint32 max_level_send_count = 4;  // 最高级所需赠送个数
  bool   is_max_level = 5;  // 是否已经达到最高级
  uint32 no_limit_expire_day_count = 6; // 无限期礼物多久不送会消失
  uint32 last_send_ts = 7; // 最后一次送该礼物的时间戳
  uint32 max_level_day_count = 8;  // 最高级延长天数，0为无限期
  uint32 effect_end_on_shelf = 9;  // 礼物架上展示的下架时间
  uint32 now_level_day_count = 10; // 当前等级的延长天数
  bool notice_no_limit_expire = 11; // 是否提醒用户无限礼物即将过期
}

message GetPresentEffectTimeDetailReq{
  uint32 uid = 1; // 用户uid
  uint32 gift_id = 2;  // 礼物id
}

message GetPresentEffectTimeDetailResp{
  uint32 gift_id = 1;  // 礼物id
  uint32 now_count = 2; // 送出个数
  repeated PresentEffectTimeLevelInfo level_info = 3; // 等级信息
  uint32 no_limit_expire_day_count =4; // 无限延迟权限多久不送过期
}

message PresentEffectTimeLevelInfo{
  uint32 level = 1;  // 级别
  uint32 level_send_count = 2; // 需要送多少个达到该等级
  uint32 level_day_count = 3; // 达到该等级延长多久下架时长; 0代表无限期
}


// 添加限时参数
message AddEffectDelayLevelReq
{
  uint32 gift_id = 1; // 礼物id
  repeated EffectDelayLevelInfo delay_info = 2; // 延迟下架相关参数， effect_end_delay
  uint32 effect_begin = 3;
  uint32 effect_end = 4;
}

message EffectDelayLevelInfo{
  uint32 level = 1;  // 延迟等级
  uint32 send_count = 2;  // 升级所需数量
  uint32 day_count = 3;  // 可以延长的天数（0为无限）
  uint32 expire_day_count = 4; // 如果是无限期的等级，几天不送之后会自动下线
  uint32 notice_day_count = 5; // 离过期几天会提醒
}

message AddEffectDelayLevelResp
{
}

// 获取限时参数
message GetEffectDelayLevelReq
{
  uint32 item_id = 1;
}
message GetEffectDelayLevelResp
{
  repeated EffectLevelConfig delay_config = 1; //
}

message EffectLevelConfig{
  uint32 gift_id = 1; // 礼物id
  repeated EffectDelayLevelInfo delay_info = 2; // 延迟下架相关参数， effect_end_delay
  uint32 effect_begin = 3;
  uint32 effect_end = 4;
}

// 更新限时参数
message UpdateEffectDelayLevelReq
{
  uint32 gift_id = 1; // 礼物id
  repeated EffectDelayLevelInfo delay_info = 2; // 延迟下架相关参数， effect_end_delay
  uint32 effect_begin = 3;
  uint32 effect_end = 4;
}

message UpdateEffectDelayLevelResp
{
}

message NotifyPresentSendReq{
  uint32 uid = 1;
  uint32 gift_id = 2;
  uint32 count = 3;
}

message NotifyPresentSendResp{
}

message DelEffectDelayLevelReq{
  uint32 gift_id = 1;
}

message DelEffectDelayLevelResp{

}

message ReportCustomPresentSendReq{
  uint32 uid = 1;
  uint32 target_uid = 2;
  uint32 channel_id = 3;
  uint32 gift_id = 4;
  uint32 count = 5;
  string order_id = 6;
}

message ReportCustomPresentSendResp{
}