syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package topic_channel.recommendation_gen;

import "tt/quicksilver/rcmd-local/common/common.proto";
import "tt/quicksilver/rcmd-local/common/topic_channel.proto";
import "tt/quicksilver/topic_channel/rcmd_channel_label.proto";
import "tt/quicksilver/topic_channel/recommendation_common.proto";

option go_package = "golang.52tt.com/protocol/services/topic_channel/recommendation_gen";

service GenRecommendation {
    // 获取推荐房列表（个数为1的时候特殊处理，获取最合适的/home/<USER>/go_project/tt19/rcmd/api/rcmd/topic_channel）
    rpc GetRecommendationList (GetRecommendationListReq) returns (GetRecommendationListResp);

    // expose for algorithm 0.2
    rpc RecallChannelList (GetRecommendationListReq) returns (GetRecommendationListResp);

    rpc SetTopicChannelQuality (SetTopicChannelQualityReq) returns (SetTopicChannelQualityResp);

    // rcmd-common config
    rpc GetRCMDCfg (GetRCMDCfgReq) returns (GetRCMDCfgResp);
    // 清空下发过滤器
    rpc ResetFilter(ResetFilterReq) returns(ResetFilterResp);

    //首页混推新接口 -- 从上面原接口GetRecommendationList复制出来的
    rpc GetHomeMixedList (GetHomeMixedListReq) returns (GetHomeMixedListResp);

    // 异步获取推荐理由
    rpc GetRecommendationReason (GetRecommendationReasonReq) returns (GetRecommendationReasonResp);
}

message ResetFilterReq {
    uint32 uid = 1;
}

message ResetFilterResp {
    uint32 code = 1;
    string msg = 2;
}

message GetRCMDCfgReq {
    enum SOURCE {
        SOURCE_DEFAULT = 0;
        SOURCE_TT = 1;
    }
    SOURCE source = 1;
}

message GetRCMDCfgResp {
    repeated uint32 supervisor_white_list = 1; // 监管组白名单
}

message BlockOption {
    uint32 block_id = 1;           //块id
    uint32 elem_id = 2;            //块里面的元素id
}

//IM页房间推荐分组
enum IMChannelListABGroup {
    DEFAULT = 0;
    RegLess72Hour_Exp_A = 1;  //注册时间小于72小时 实验A组
    RegLess72Hour_Exp_B = 2;  //注册时间小于72小时 实验B组
    RegMore72Hour_Exp = 3;  //注册时间大于72小时
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum REGULATORY_LEVEL {
    FREE = 0; //不限制
    SIMPLE_MINOR = 1; // 未成年
}

message RecommendationReqReserve {
    IMChannelListABGroup im_list_exp_group = 1;  //IM页房间推荐分组
}

enum PrefGameLabelType {
    SYSTEM = 0; // 系统标签
    CUSTOM = 1; // 用户自定义标签
}

message PrefGameLabel {
    uint32 id = 1;
    string val = 2;
    PrefGameLabelType type = 3; // 标签类型
}

message PrefGame {
    uint32 tab_id = 1; // 游戏的tab id
    repeated PrefGameLabel labels = 2; // 游戏标签列表
}

message GetRecommendationListReq {
    enum GetListMode{
        DEFAULT = 0;
        NEXTPAGE = 1; // 请求下一页
        REFRESH = 2; // 刷新
        INSERT = 3; // 强插同逻辑房间
    }
    uint32 uid = 1; // 请求用户uid
    uint32 limit = 2; //
    uint32 tab_id = 3; // 请求的房间类型，用于筛选
    repeated BlockOption block_options = 4; // 发布条件的筛选
    GetListMode get_mode = 5;
    uint32 channel_enter_source = 6;    // 参考ChannelEnterReq中的EChannelEnterSource枚举值
    uint32 client_type = 7;             // 参考ServiceInfo中的client_type
    uint32 client_version = 8;          // 参考ServiceInfo中的client_version
    uint32 market_id = 9;               // 参考ServiceInfo中的market_id
    enum Environment {
        production = 0;
        staging = 1;
        test = 2;
    };
    // Deprecated
    Environment env = 10; // 环境, 已废弃，选填
    string channel_package_id = 11; // 渠道包ID, 已废弃

    // Deprecated
    string trace_id = 12; //增加一个spanId 用于算法版v0.2, 选填
    // Deprecated
    string model_version = 13; //训练模型版本，用于算法版本, 选填
    // Deprecated
    uint32 debug_flag = 14; //测试标志, 选填
    // Deprecated
    string reserve = 15; //已废弃，保留字段,结构体RecommendationReqReserve，marshal
    rcmd.common.RcmdBaseReq base_req = 16; // 只需要填写uid
    uint32 sex = 17; //性别过滤 0-不过滤 1-选择Female 2-选择Male
    rcmd.common.RcmdBrowseInfo browse_list = 18; //请求列表曝光信息, 用于支持曝光过滤功能

    REGULATORY_LEVEL regulatory_level = 19; // 监管等级,选填

    bool is_minority_parent_tab = 20; // tab id 是否是小众游戏的父分类,选填
    repeated uint32 category_ids = 22; // 开黑列表-分类筛选,选填
    repeated uint32 tab_ids = 23; // 开黑列表-分类筛选-多选子类型,选填
    // Deprecated
    bool is_qm_framework = 24; // 已废弃，是否使用快速匹配的推荐框架进行推荐
    // Deprecated: 功能已下线
    repeated PrefGame pref_games = 25; // 用户偏好框选择的游戏以及标签, 选填
    repeated topic_channel.rcmd_channel_label.GameLabel labels = 26; // 标签筛选

    //业务筛选字段
    message BusinessFilter{
        //业务block
        BusinessBlockEnum block_type = 1;
        //业务elem
        repeated BusinessElemEnum elem_type = 2;
    }
    repeated BusinessFilter business_filter = 27;// 玩法标签筛选项

    repeated topic_channel.recommendation_common.TcGameSetting game_settings = 28; // 小游戏设置筛选
    // Deprecated
    bool is_fallback_req = 29; // 兜底请求

    repeated string interest_labels = 30;  //兴趣标签
    string delivery_type = 31; // 强插同逻辑房间需要的类型
    // Deprecated
    bool is_enable_game_label = 32; // 是否开启了玩法标签（新版筛选）
    repeated string shield_filter_words = 33;  // 屏蔽词
    repeated  topic_channel.rcmd_channel_label.ClassifyLabelList classify_labels = 34; // 分类标签筛选
}

message GetHomeMixedListReq {
    enum GetListMode{
        DEFAULT = 0;
        NEXTPAGE = 1; // 请求下一页
        REFRESH = 2; // 刷新
        INSERT = 3; // 强插同逻辑房间
    }
    uint32 uid = 1; // 请求用户uid
    uint32 limit = 2; //
    uint32 tab_id = 3; // 请求的房间类型，用于筛选
    repeated BlockOption block_options = 4; // 发布条件的筛选
    GetListMode get_mode = 5;
    uint32 channel_enter_source = 6;    // 由推荐同事定义,参考ChannelEnterReq中的EChannelEnterSource枚举值
    uint32 client_type = 7;             // 客户端设备类型 0 安卓， 1 ios， 2 web, 具体可以查询ga_base.proto  TT_CLIENT_TYPE
    uint32 client_version = 8;          // 客户端版本号
    uint32 market_id = 9;               // 0:tt, 2: 欢游, 5: 麦可, 6:迷境, 具体可以查询ga_base.proto MarketId
    enum Environment {
        production = 0;
        staging = 1;
        test = 2;
    };
    Environment env = 10; // 环境, 已废弃，选填
    string channel_package_id = 11; // 渠道包ID, 已废弃

    string trace_id = 12; //增加一个spanId 用于算法版v0.2, 选填
    string model_version = 13; //训练模型版本，用于算法版本, 选填
    uint32 debug_flag = 14; //测试标志, 选填
    string reserve = 15; //已废弃，保留字段,结构体RecommendationReqReserve，marshal
    rcmd.common.RcmdBaseReq base_req = 16; // 只需要填写uid
    uint32 sex = 17; //性别过滤 0-不过滤 1-选择Female 2-选择Male
    rcmd.common.RcmdBrowseInfo browse_list = 18; //请求列表曝光信息, 用于支持曝光过滤功能

    REGULATORY_LEVEL regulatory_level = 19; // 监管等级,选填

    bool is_minority_parent_tab = 20; // tab id 是否是小众游戏的父分类,选填
    repeated uint32 category_ids = 22; // 开黑列表-分类筛选,选填
    repeated uint32 tab_ids = 23; // 开黑列表-分类筛选-多选子类型,选填
    bool is_qm_framework = 24; // 已废弃，是否使用快速匹配的推荐框架进行推荐

    repeated PrefGame pref_games = 25; // 用户偏好框选择的游戏以及标签, 选填
    repeated topic_channel.rcmd_channel_label.GameLabel labels = 26; // 标签筛选

    //业务筛选字段
    message BusinessFilter{
        //业务block
        BusinessBlockEnum block_type = 1;
        //业务elem
        repeated BusinessElemEnum elem_type = 2;
    }
    repeated BusinessFilter business_filter = 27;// 玩法标签筛选项

    repeated topic_channel.recommendation_common.TcGameSetting game_settings = 28; // 小游戏设置筛选

    bool is_fallback_req = 29; // 兜底请求

    repeated string interest_labels = 30;  //兴趣标签

    string delivery_type = 31; // 强插同逻辑房间需要的类型

}

enum BusinessBlockEnum {
    RoomMode = 0; //房间模式
    GameCondition = 1; //对局状态
}
enum BusinessElemEnum {
    NoLimit = 0; //不限
    Single = 1;  //单人
    Double = 2;  //双人
    Waiting = 3; //等待中
    Started = 4; // 开局中
}

// 关注的人＞一起玩过＞同城标签
enum RCMDLabel {
    None = 0;
    GangUpWithHomeOwner = 1; // 一起玩过
    ChatWithHomeOwner = 2; // 互聊(混推会下发，垂直列表已废弃)
    FollowUserInChannel = 3; // 关注
    LocShow = 4; // 同城
    MtFollowUserInChannel = 5; // 有好友/单关在的房间
    MtEverEnterChannel = 6; // 过去30天进过的房间
    MtManyUsersInChannel = 7; // 超多人围观
    MtManyUsersFollowChannelOwner = 8; // 很多人关注房主
    MtGuessYouLikeChannelOwner = 9; // 最近关注人的相似房主推荐
    MtRecentLikeTabChannelDouDi = 10; // 最近进过房间同玩法兜底
    MtHotGameHotChannelDouDi = 11; // 热聊挑战上榜房间兜底
    MtFriendOfFriend = 12; // 好友的好友
    MtRecentFollow = 13; // 最近关注
}

message ChannelInfo {
    uint32 tag_id = 1; // 娱乐房tag id（区别于游戏卡片tag id）
    uint32 recall_flag = 2; // 召回标识
    rcmd.common.LocationInfo loc = 3;
    repeated RCMDLabel rcmd_labels = 4; // 推荐标签
    LocShowType loc_show_type = 5; // 建议显示的IP信息
    enum LocShowType {
        LocShowType_DEFAULT = 0; // 默认
        LocShowType_PROVINCE = 2; // 建议显示省份IP位置信息
        LocShowType_CITY = 3; // 建议显示城市IP位置信息
    }
    // Deprecated: 萌新承接房逻辑已下线
    bool is_new_user_undertake = 6; // 是否萌新承接房
    repeated uint32 follow_uid_list = 7; // 在房关注用户uid，去除房主（后续废弃，使用display_uid_list代替）
    repeated uint32 play_uid_list = 8; // 在房一起玩过用户uid，去除房主（后续废弃，使用display_uid_list代替）
    repeated uint32 follow_uid_list_v2 = 9; // 新版首页在房关注用户uid
    bool is_ever_enter = 10; // 是否为过去停留时长>x的房间
    string delivery_type = 11; // 下发的房间类型
    string display_content = 12; // 房间中部区域展示文案
    repeated uint32 display_uid_list = 13; // 展示的麦上用户uid列表
}

message GetRecommendationListResp {
    repeated uint32 channel_id = 1; // 下发的房间列表
    bool bottom_reached = 2; // 是否已经到底了，即下一页将没有数据
    map<uint32, ChannelInfo> channel_info_map = 3;
    string trace_id = 4;
    map<string, string> debug_info_map = 5; //测试打点信息
    rcmd.common.LocationInfo self_loc = 6;
    repeated PrefGame pref_games = 7; // 偏好游戏以及标签
    uint32 insert_pos = 8; // 偏好游戏弹框插入的位置， 比如 insert_pos = 2, 意味着 [0 1 2 3 4 ] -> [0 1 insert_item 2 3 4 ]

    enum NotifyType{
        DEFAULT = 0;
        APPOINTMENT = 1;
        RefreshSucc = 2;
    }
    repeated NotifyType notify_list = 9; // 通知事件
    string footprint = 10;// 推荐trace id
    BaseRCMDResp base_resp = 11; //基础的resp
    map<uint32, rcmd.common.ChannelInfo> common_channel_info_map = 12;
    repeated topic_channel.rcmd_channel_label.GameLabel labels = 13; // 筛选标签
}

message GetHomeMixedListResp {
    repeated uint32 channel_id = 1; // 下发的房间列表
    bool bottom_reached = 2; // 是否已经到底了，即下一页将没有数据
    map<uint32, ChannelInfo> channel_info_map = 3;
    string trace_id = 4;
    map<string, string> debug_info_map = 5; //测试打点信息
    rcmd.common.LocationInfo self_loc = 6;
    repeated PrefGame pref_games = 7; // 偏好游戏以及标签
    uint32 insert_pos = 8; // 偏好游戏弹框插入的位置， 比如 insert_pos = 2, 意味着 [0 1 2 3 4 ] -> [0 1 insert_item 2 3 4 ]

    enum NotifyType{
        DEFAULT = 0;
        APPOINTMENT = 1;
        RefreshSucc = 2;
    }
    repeated NotifyType notify_list = 9; // 通知事件
    string footprint = 10;// 推荐trace id
    BaseRCMDResp base_resp = 11; //基础的resp
}

message BaseRCMDResp {
    bool ok = 1;
    uint64 code = 2;
    string msg = 3;
    enum PlanBStrategy{
        PlanBStrategy_INVALID = 0; // TT业务兜底
        PlanBStrategy_TT = 1; // TT业务兜底
        PlanBStrategy_RANDOM = 2; // 随机兜底
    }
    PlanBStrategy plan_b_strategy = 4; // 兜底策略, ok=false时，才会使用该字段
    bool is_algo = 5; // 是否走算法版
}

message SetTopicChannelQualityReq {
    enum TopicChannelQuality{
        DEFAULT = 0;
        LOW = 1;
    }
    repeated uint32 channel_id = 1;
    TopicChannelQuality quality = 2;
}

message SetTopicChannelQualityResp {
}

message CompanionChannelEvent {
    repeated uint32 channel_id_list = 1;    // 陪玩房间id
}


message NegativeFeedEvent {
    uint32 uid = 1;
    map<uint32, int64> user_map = 2;
    map<uint32, int64> tab_map = 3;
    map<string, int64> channel_cond_map = 4;
    map<string, int64> channel_name_map = 5;
    enum UpdateType {
        Invalid = 0;
        User = 1;
        Tab = 2;
        ChannelCond = 3;
        ChannelName = 4;
    }
    repeated UpdateType update_type_list = 6;
}

message UserNegativeFeed{
    map<uint32, int64> user_map = 1;
    map<uint32, int64> tab_map = 2;
    map<string, int64> channel_cond_map = 3;
    map<string, int64> channel_name_map = 4;
}

message ChannelTitleTokenizeEvent {
    uint32 cid = 1; // 房间id
    repeated string tags = 2; // 房间类型相关的标题分词结果
}


message PlaymateIntentionLimitEvent {
    uint32 channel_id = 1;
    int64 limit_time = 2;
}

message GetRecommendationReasonReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
}

enum RCMDReason {
    Reason_None = 0;
    Reason_HotGameTop = 1; // 热聊挑战前分位
}

message GetRecommendationReasonResp {
    uint32 reason = 1;
}
