syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package topic_channel.tab;

option go_package = "golang.52tt.com/protocol/services/topic_channel/tab";

service TopicChannelTab {

  /* -------运营后台------- */
  rpc GetTabByName (GetTabByNameReq) returns (GetTabByNameResp);

  // InsertTab 用于插入一种类型。
  rpc InsertTab (InsertTabReq) returns (InsertTabResp);

  // UpdateTab 用于更新某个类型。
  rpc UpdateTab (UpdateTabReq) returns (UpdateTabResp);

  // 创建游戏后关联更新玩法中的u_game_id字段
  rpc UpdateTabUGameId (UpdateTabUGameIdReq) returns (UpdateTabUGameIdResp);

  // 同步数据更新玩法中的mini_game_id字段
  rpc UpdateTabMiniGameId (UpdateTabMiniGameIdReq) returns (UpdateTabMiniGameIdResp);

  // 根据u_game_id查找玩法
  rpc GetTabByUGameId (GetTabByUGameIdReq) returns (GetTabByUGameIdResp);

  // DeleteTab 用于删除某个类型。
  rpc DeleteTab (DeleteTabReq) returns (DeleteTabResp);

  // InsertBlock 用于插入一个 Block。
  rpc InsertBlock (InsertBlockReq) returns (InsertBlockResp);

  // UpdateBlock 用于更新某个 Block。
  rpc UpdateBlock (UpdateBlockReq) returns (UpdateBlockResp);

  // DeleteBlock 用于删除某个 Block。
  rpc DeleteBlock (DeleteBlockReq) returns (DeleteBlockResp);

  // InsertElem 用于插入一个 Elem。
  rpc InsertElem (InsertElemReq) returns (InsertElemResp);

  // UpdateElem 用于更新某个 Elem。
  rpc UpdateElem (UpdateElemReq) returns (UpdateElemResp);

  // DeleteElem 用于删除某个 Elem。
  rpc DeleteElem (DeleteElemReq) returns (DeleteElemResp);

  // RearrangeTabs 用于重新排序所有的类型。
  rpc RearrangeTabs (RearrangeTabsReq) returns (RearrangeTabsResp);

  // TabsForTT 用于获取所有的类型。
  rpc TabsForTT (TabsForTTReq) returns (TabsForTTResp);

  // 只能给运营后台用
  rpc GetTabsForManagementOp(GetTabsForManagementOpReq) returns (GetTabsForManagementOpResp);

  rpc SetSimpleBanner (SetSimpleBannerReq) returns (SetSimpleBannerResp);

  rpc GetSimpleBanner (GetSimpleBannerReq) returns (GetSimpleBannerResp);

  rpc GetCategoryTitleListForTT (GetCategoryTitleListForTTReq) returns (GetCategoryTitleListForTTResp);

  rpc InsertRelationOfEtChannel (InsertRelationOfEtChannelReq) returns (InsertRelationOfEtChannelResp);

  rpc GetRelationOfEtChannel (GetRelationOfEtChannelReq) returns (GetRelationOfEtChannelResp);

  /*-------------------------------分类相关-------------------------------------------*/
  rpc AddCategoryTitle (AddCategoryTitleReq) returns (AddCategoryTitleResp);

  rpc UpdateCategoryTitle (UpdateCategoryTitleReq) returns (UpdateCategoryTitleResp);

  rpc GetCategoryTitleList (GetCategoryTitleReq) returns (GetCategoryTitleResp);

  // 用于重新排序玩法标题
  rpc ReSortCategoryTitle (ReSortCategoryTitleReq) returns (ReSortCategoryTitleResp);

  // 删除分类
  rpc DeleteCategoryTitle (DeleteCategoryTitleReq) returns (DeleteCategoryTitleResp);

  //*--------------------------------多级block和elem相关--------------------------------------------------*/
  rpc AddMultilevelTitle (AddMultilevelTitleReq) returns (AddMultilevelTitleResp);
  rpc UpdateMultilevelTitle (UpdateMultilevelTitleReq) returns (UpdateMultilevelTitleResp);
  rpc GetMultilevelTitle (GetMultilevelTitleReq) returns (GetMultilevelTitleResp);
  rpc GetMultilevelTitleForTT (GetMultilevelTitleForTTReq) returns (GetMultilevelTitleForTTResp);
  rpc DeleteMultilevelTitle (DeleteMultilevelTitleReq) returns (DeleteMultilevelTitleResp);

  //按条件搜索tabs
  rpc SearchTabs (SearchTabsReq) returns (SearchTabsResp);
  /* ---------APP--------- */

  // Tabs 用于获取所有的类型。
  rpc Tabs (TabsReq) returns (TabsResp);

  // Blocks 用于获取某个 tab 的所有 block。
  rpc Blocks (BlocksReq) returns (BlocksResp);

  // 批量获取指定 tab 的 blocks
  rpc BatchGetBlocks (BatchGetBlocksReq) returns (BatchGetBlocksResp);

  // FiniteTabs 用于获取指定的类型。
  rpc FiniteTabs (FiniteTabsReq) returns (FiniteTabsResp);

  // GetTabsByCategoryEnum 根据分类枚举获取玩法信息，可用于获取一起分类或其他类目下的所有玩法
  rpc GetTabsByCategoryEnum(GetTabsByCategoryEnumReq) returns (GetTabsByCategoryEnumResp);

  // FiniteTabsByTags 通过指定tag id获取相应的类型。
  rpc FiniteTabsByTags (FiniteTabsByTagsReq) returns (FiniteTabsByTagsResp);

  /* ---------tab名称配置--------- */
  rpc GetRoomNameConfigure (GetRoomNameConfigureReq) returns (GetRoomNameConfigureResp);

  rpc SetRoomNameConfigure (SetRoomNameConfigureReq) returns (SetRoomNameConfigureResp);

  // 根据category的id获取到挂在该分类下所有tab
  rpc GetAllTabOfCategory (GetAllTabOfCategoryReq) returns (GetAllTabOfCategoryResp);

  //获取小众游戏信息
  rpc GetMinorityGameTabs (GetMinorityGameTabsReq) returns (GetMinorityGameTabsResp);

  //按分类批量获取玩法
  rpc GetTabsByCategoryIds (GetTabsByCategoryIdsReq) returns (GetTabsByCategoryIdsResp);

  //修改房间首页配置
  rpc ModifyTabHomePageConfig(ModifyTabHomePageConfigReq) returns (ModifyTabHomePageConfigResp);
  //修改房间首页排序
  rpc SortTabHomePageConfig(SortTabHomePageConfigReq) returns (SortTabHomePageConfigResp);
  //房间首页配置列表
  rpc ListTabHomePageConfig(ListTabHomePageConfigReq) returns (ListTabHomePageConfigResp);

  //房间发布属性配置
  rpc SetReleaseCondition (SetReleaseConditionReq) returns (SetReleaseConditionResp);
  rpc GetReleaseConditionForTT (GetReleaseConditionForTTReq) returns (GetReleaseConditionForTTResp);
  rpc ListReleaseConditionForTT (ListReleaseConditionForTTReq) returns (ListReleaseConditionForTTResp);

  rpc ListReleaseCondition (ListReleaseConditionReq) returns (ListReleaseConditionResp);

  //业务筛选器
  rpc BatchGetBusinessBlockInfo (BatchGetBusinessBlockInfoReq) returns (BatchGetBusinessBlockInfoResp);

  rpc SetOfficialRoomNameConfig(SetOfficialRoomNameConfigReq)returns(SetOfficialRoomNameConfigResp);
  rpc DelOfficialRoomNameConfig(DelOfficialRoomNameConfigReq)returns(DelOfficialRoomNameConfigResp);
  rpc ListOfficialRoomNameConfig(ListOfficialRoomNameConfigReq)returns(ListOfficialRoomNameConfigResp);
  rpc RearrangeOfficialRoomNameConfig(RearrangeOfficialRoomNameConfigReq)returns(RearrangeOfficialRoomNameConfigResp);
  rpc BatchGetOfficialRoomNameConfig(BatchGetOfficialRoomNameConfigReq)returns(BatchGetOfficialRoomNameConfigResp);

  rpc UpdateBusinessConf (UpdateBusinessConfReq) returns (UpdateBusinessConfResp);

  // 首页金刚区配置(后续有运营后台再用)
  rpc HomePageHeadConfig (HomePageHeadConfigReq) returns (HomePageHeadConfigResp);
  rpc UpsertHeadConfig (UpsertHeadConfigReq) returns (UpsertHeadConfigResp);
  rpc DelPageHeadConfig (DelPageHeadConfigReq) returns (DelPageHeadConfigResp);

  //批量获取玩法筛选器
  rpc BatchGetGameLabelItems (BatchGetGameLabelItemsReq) returns (BatchGetGameLabelItemsResp);

  //运营后台-发布房间配置-管理所有字段 写接口
  rpc SetBlockInfos(SetBlockInfosReq) returns (SetBlockInfosResp);
  //运营后台-发布房间配置-管理所有字段 读接口
  rpc GetBlockInfos(GetBlockInfosReq) returns (GetBlockInfosResp);
  //批量获取玩法的block关联关系
  rpc BatchGetBlockRelations(BatchGetBlockRelationsReq) returns (BatchGetBlockRelationsResp);

  // 快速匹配相关新接口 旧的首页玩法卡GetHomePagePlayCards相关和GetDetailTabInfoOfScene，GetSceneTabsForTT都不用了，后面统一使用新的
  rpc GetQuickMatchConfig(GetQuickMatchConfigReq) returns (GetQuickMatchConfigResp);
  rpc UpdateQuickMatchConfig(UpdateQuickMatchConfigReq) returns (UpdateQuickMatchConfigResp);
  rpc DeleteQuickMatchConfig(DeleteQuickMatchConfigReq) returns (DeleteQuickMatchConfigResp);
  rpc ResortQuickMatchConfig(ResortQuickMatchConfigReq) returns (ResortQuickMatchConfigResp);

  // 新版首页快速匹配入口配置 运营后台
  rpc UpsertNewQuickMatchConfig(UpsertNewQuickMatchConfigReq) returns (UpsertNewQuickMatchConfigResp);
  rpc BatchGetNewQuickMatchConfig(BatchGetNewQuickMatchConfigReq) returns (BatchGetNewQuickMatchConfigResp);
  rpc DelNewQuickMatchConfig(DelNewQuickMatchConfigReq) returns (DelNewQuickMatchConfigResp);
  // 客户端获取配置
  rpc GetNewQuickMatchConfig(GetNewQuickMatchConfigReq) returns (GetNewQuickMatchConfigResp);

  // 玩法数据扩展
  rpc GetAllTabInfoExt (GetAllTabInfoExtReq) returns (GetAllTabInfoExtResp);
  rpc GetTabInfoExt (GetTabInfoExtReq) returns (GetTabInfoExtResp);
  rpc UpsertTabInfoExt (UpsertTabInfoExtReq) returns (UpsertTabInfoExtResp);
  rpc DelTabInfoExt (DelTabInfoExtReq) returns (DelTabInfoExtResp);
  rpc ResortTabInfoExt (ResortTabInfoExtReq) returns (ResortTabInfoExtResp);

  rpc GetCache(GetCacheReq) returns (GetCacheResp);

  // 屏蔽规则设置
  rpc SetShieldSwitch(SetShieldSwitchReq) returns (SetShieldSwitchResp);
  rpc BatchGetShieldSwitchByTabIds(BatchGetShieldSwitchByTabIdsReq) returns (BatchGetShieldSwitchByTabIdsResp);

  // 获取玩法问题配置
  rpc GetTabQuestionConfig(GetTabQuestionConfigReq) returns (GetTabQuestionConfigResp);

  // filterId映射虚拟玩法id
  rpc FindFilterMixTabIds(FindFilterMixTabIdsReq) returns (FindFilterMixTabIdsResp);
  // 根据mixTabId获取filterId
  rpc FindFilterIdsByMixTabIds(FindFilterIdsByMixTabIdsReq) returns (FindFilterIdsByMixTabIdsResp);
}

// 快速匹配相关的
enum QuickMatchConfigType {
  Invalid = 0;
  NormalQuickMatchList = 1; //快速匹配半屏兜底列表
  MoreCardTabList = 2; //快速匹配更多默认列表
}

message SceneInfo {
  string id = 1;
  uint32 tab_id = 2;
  TabType tab_type = 3;
  uint32 category_id = 4;
  QuickMatchConfigType config_type = 5;    // 1-快速匹配半屏兜底列表；2-快速匹配更多默认列表
}

message GetQuickMatchConfigReq {
  QuickMatchConfigType config_type = 1;
  uint32 page = 2;
  uint32 limit = 3;
}

message GetQuickMatchConfigResp {
  repeated SceneInfo scene_info = 1;
}

message UpdateQuickMatchConfigReq {
  SceneInfo scene_info = 1;
}

message UpdateQuickMatchConfigResp {

}

message DeleteQuickMatchConfigReq {
  string id = 1;
}

message DeleteQuickMatchConfigResp {

}

message ResortQuickMatchConfigReq {
  repeated string ids = 1;
}

message ResortQuickMatchConfigResp {

}

message TabQuestion {
  string title = 1;
  repeated string labels = 2;
}

// Tab 是主题房间类型。
message Tab {
  uint32 id = 1; // id 是类型的唯一标识。
  uint32 weight = 2; // weight 代表每种类型对应的权重，weight越小权重越高。
  string name = 3; // name 是主题房间类型名。
  enum TabType {
    NORMAL = 0; //普通分类
    GAME = 1; //游戏分类
    MINIGAME = 2; //小游戏
    MUSIC = 3;
  }
  TabType tab_type = 4; // tab_type 代表主题房类型所属的父级分类。
  // deprecated，新的使用对应mini_game_id和game_info 旧逻辑：tag_id 代表用户所填游戏卡对应类型的ID(包括小游戏id也是这个字段)
  uint32 tag_id = 5;
  string image_uri = 6; // image_uri 代表背景用图的URI。
  repeated string room_name = 7; //deprecate
  uint32 version = 8; // version 代表版本。
  enum RoomNameType {
    DEFAULT = 0; //默认推荐文案
    SPLICE = 1; //需要拼接
  }
  RoomNameType room_name_type = 9;
  uint32 room_name_version = 10;
  string newcomer_image_uri = 11; // newcomer_image_uri 在新注册用户停留30分钟后使用。
  string newcomer_color = 12; // newcomer_color 是 newcomer_image_uri 使用的颜色。
  repeated string newcomer_welcome = 13; // newcomer_welcome 是 newcomer_image_uri 使用的欢迎语。
  string show_image_uri = 14;
  repeated uint32 block_ids = 15;
  string newcomer_font_color = 16; // newcomer_font_color 是 newcomer_image_uri 使用的欢迎语字体的颜色。
  string newcomer_font_background_uri = 17; // newcomer_font_background_uri 是 newcomer_image_uri 使用欢迎语背景图的 URI。
  uint32 mic_mod = 18; // 房间模式
  string follow_label_img = 19; // 跟随好友头像标头jpg图
  string follow_label_text = 20; // 跟随好友头像标头文案
  uint32 category_id = 21; // 玩法分类
  uint32 mini_game_num = 22; // 小游戏上限人数
  // 以下是小游戏灰度策略字段
  string client_ver = 23; //客户端版本号下限
  string uid_tail_num = 24; // 用户id尾号
  string uid_white_list = 25; // 用户白名单
  string client_ver_upper_limit = 53; //客户端版本上限

  string find_playing_text = 26; //找人玩标签
  string find_playing_img = 27; //找人玩标签
  repeated string channel_package_id = 28; //对应渠道包id

  enum LabelType {
    NOLABEL = 0; //默认没有标签
    NEW = 1; //新发布标签
    HOT = 2; // 热门标签
    ALPHATEST = 3;//内测标签
  }
  LabelType tab_label = 30;
  string cards_image_url = 31; //其实就是玩法卡图片，新增加字段因为图片尺寸和之前不一致，不确定能否兼容
  string mask_layer = 32; //tab图片遮罩颜色
  string room_label = 33; //房间卡标签
  enum MatchType {
    QUICK = 0; // 快速匹配，即旧匹配。
    TEMPORARYHOUSE = 1; // 临时房匹配
  }
  MatchType match_type = 34; //快速匹配类型

  enum TemMatchPolicy {
    NOPOLICY = 0;
    RECOMMENDPOLICY = 1; // 推荐策略
    GUARANTEEPOLICY = 2; // 兜底策略
  }
  TemMatchPolicy tem_match_policy = 35; // 临时房匹配策略类型
  uint32 link_id = 36; // 临时房匹配游戏和开黑房匹配游戏关联替换
  PlatformType platform_type = 37;
  uint32 category_sort = 38; //特殊类别分类排序，目前只有王者1，吃鸡2，这俩tab在5.5.0创建房间时优先于其他category
  bool display_elem = 39; //是否特殊分类,在category被选中时直接展示elem

  string radar_distribution_image = 40;       //雷达房间下发底图
  string room_distribution_image = 41;       //房间转移底图
  string bottom_text_color = 42;             //按钮文字颜色
  string welcome_text_color = 43;            //招募文字颜色
  string small_card_url = 44;                //v5.5.0之后首页六格专用
  string new_tab_category_url = 45;         //v5.5.0之后创建房间时

  uint32 u_game_id = 46; // 平台统一的游戏id
  bool is_minority_game = 47; //是否小众游戏
  uint32 sort = 48; // sort 代表每种类型在对应分类下的权重，sort越小权重越高。移动端的
  uint32 pc_sort = 49; // pc_sort 代表每种类型在对应分类下的权重，pc_sort越小权重越高。PC端的

  HomePageType home_page_type = 50;
  ViewType view_type = 51;
  uint32 home_sort = 52;    //首页排序
  repeated uint32 shield_market_ids = 54;  //需要屏蔽的包id

  string default_channel_condition = 55; //房间默认状态

  map<uint32, ChannelCondition> special_channel_condition = 56; //根据发布字段设置的个性化房间状态，key block_id

  ChannelViewType topic_channel_view_type = 57;//首页房间列表视图

  string background_img_url = 58;//迷境房间列表背景图
  repeated uint32  minor_certification_market_ids = 59;//需要未成年认证的包id

  bool show_publish_button = 60; //是否展示发布按钮
  bool hide_filter = 61; // 是否隐藏发布条件筛选入口

  bool show_question = 62;
  repeated TabQuestion questions = 63;

  string tab_alias_name = 64; //玩法简称别名

  uint32 category_mapping = 65; //分类映射关系，见topic_channel_.proto CategoryType枚举，1-一起开黑

  uint32 mini_game_id = 66;  //小游戏id
  GameInfoItem game_info = 67;  // 游戏列表详情，游戏卡相关数据
  uint32 scheme_detail_type = 68;  //房间基础的子类型，用于识玩法组的页面布局形式
  string unique_name = 69; //唯一名称，主要用于推荐

  //只有Tabs, TabsForTT,FiniteTabs,等从缓存获取数据的接口才会有这个字段
  ShieldRuleForTab shield_rule = 70; //屏蔽规则
  // 新增PC极速版首页背景图
  string fast_pc_background_img_url = 71;
  // 所属极速版pc的类目信息
  repeated FastPCCategoryInfo fast_pc_category_infos = 72;
  // 新增PC极速版大厅房间背景图
  string fast_pc_room_background_img_url = 73;
}

message ChannelCondition{
  map<uint32, string> condition = 1; //key elem_id value 状态
}
enum PlatformType {
  UNKNOWN = 0;
  ALL = 1;    // 全平台
  ANDROID_IOS = 2;
  PC = 3;
}

enum ChannelViewType{
  ChannelView_Default = 0;
  ChannelView_MOBA = 1;
  ChannelView_Ktv = 2;
  ChannelView_MysteryEscape = 3;
}

enum TabRelatedType {
  TOPIC_CHANNEL = 0;      //主题房的标签
  ENTERTAINMENT = 1;      //娱乐房的标签
}


message GetTabByNameReq {
  string tab_name = 1;
}

message GetTabByNameResp {
  repeated Tab tab_infos = 1;
}

message InsertTabReq {
  Tab tab = 1; // tab 代表要插入的类型，插入时需要填写的字段包括name、tab_type、tag_id、image_uri和room_name。
}

message InsertTabResp {
  bool result = 1; // result 反应插入是否成功。
  uint32 id = 2; // id 是类型的唯一标识。
  uint32 category_mapping = 3; //分类映射关系，见topic_channel_.proto CategoryType枚举
}

message UpdateTabReq {
  Tab tab = 1; // tab 代表要更新的类型，更新时需要将不参与更新的字段一并填充（未更新字段填充旧值）。
}

message UpdateTabResp {
  bool result = 1; // result 反应更新是否成功。
  uint32 id = 2; // id 是类型的唯一标识。
}

message DeleteTabReq {
  Tab tab = 1; // tab 代表要删除的类型，删除时只需要id字段。
}

message DeleteTabResp {
  bool result = 1; // result 反应删除是否成功。
  uint32 id = 2; // id 是类型的唯一标识。
}

message RearrangeTabsReq {
  repeated Tab tabs = 1; // tabs 包含所有类型，它们被编排为新的顺序。
  PlatformType platform_type = 2; //平台类型，排序
}

message RearrangeTabsResp {
  bool result = 1; // result 反应重新排序是否成功。
}
message ReSortPlayBlockReq {
  repeated uint32 block_id = 1; // 玩法分类标签id数组
}

message ReSortPlayBlockResp {
  bool result = 1;
}

message TabsForTTReq {
  uint32 skip = 1; // skip 表示上一次请求得到的类型总数，首次填0即可。
  uint32 limit = 2; // limit 表示本次请求希望获得的类型数量。
  uint32 category_id = 3;//分类id，传0为全部
}

message TabsForTTResp {
  repeated Tab tabs = 1; // tabs 代表本次请求返回的所有类型。
  uint32 skip = 2; // skip 表示已请求得到的类型总数（包括本次请求）。
  uint32 limit = 3; // limit 表示本次请求实际获得的类型数量。
  uint32 total = 4; // total 表示数据库中保存类型的总量。
}

enum RequestSource {
  default = 0;
  rcmd = 1;
}

message TabsReq {
  uint32 skip = 1; // skip 表示上一次请求得到的类型总数，首次填0即可。
  uint32 limit = 2; // limit 表示本次请求希望获得的类型数量。
  RequestSource source = 3; // 区分流量来源，中台为1
}

message TabsResp {
  repeated Tab tabs = 1; // tabs 代表本次请求返回的所有类型。
  uint32 skip = 2; // skip 表示已请求得到的类型总数（包括本次请求）。
  uint32 limit = 3; // limit 表示本次请求实际获得的类型数量。
  uint32 total = 4; // total 表示数据库中保存类型的总量。
}

//查询条件id和name 二选一，两个条件都传的话以id为准
message SearchTabsReq {
  repeated uint32 ids = 1; // id 是类型的唯一标识。
  repeated string tab_names = 2; // name 是主题房间类型名。
}

message SearchTabsResp {
  repeated Tab tabs = 1; // tabs 代表本次请求返回的所有类型。
}

// Contact 联系，某个元素与其他栏目的联系
message Contact {
  uint32 block_id = 1;
  uint32 before = 2;
  uint32 after = 3;
}

// Elem 元素，多个元素构成一个栏目
message Elem {
  uint32 id = 1;
  string title = 2;
  Contact contacts = 3;
  string mini_game_model = 4;
  uint32 team_size = 5;

  enum PublicFlag {
    PublicFlagUnknown = 0;
    PublicFlagQuestion = 1;
    PublicFlagFilter = 2;
    PublishFlagMiniGameMode = 3;
    PublishFlagTopCardMode = 4;  // 输入框类型才有，房间卡片顶部展示
    PublishFlagMiddleCardMode = 5;  // 输入框类型才有 首页卡片中部展示
    PublishFlagHotFlag = 6;  //热门标签
  }

  // 控制 Elem 在哪些场景展示
  repeated PublicFlag public_flags = 6;
  uint32 priority = 7; //当前block内elem的排序优先级
  uint32 min_num = 8;  // block mode = INPUT = 2; 可填写最小值
  uint32 max_num = 9;  // block mode = INPUT = 2; 可填写最大值

}


// Block 栏目，由多个元素构成
message Block {
  uint32 id = 1;
  string title = 2;
  // Mode 是 Block 的选择模式
  enum Mode {
    SINGLE = 0;
    MULTI = 1;
    USER_INPUT = 2;    // 用户可输入类型
  }
  Mode mode = 3;
  repeated Elem elems = 4;
  uint32 show_row = 5;
  uint32 block_type = 6;
  bool control_team_size = 7;
  // 新增最多选N个字段，当most_select_num为0，按照mode设置单选或不限
  uint32 most_select_num = 8;
  //是否只在新版本6.28.0以后展示，是则旧版本不展示
  bool only_show_after_628 = 9;
  string sub_title = 10; // 提示文案
  enum ShowPosition {
    Normal = 0; // 不置顶
    TopAfterTabName = 1; // 置顶在玩法名后，每个玩法最多一个
    Top = 2; // 置顶
  }
  // 在首页房间列表中的发布字段展示位置
  ShowPosition show_position = 11;
  // 是否将发布选项文案设为麦位名称
  bool set_mic_name = 12;
}

message BlocksReq {
  uint32 tab_id = 1;
}

message BlocksResp {
  repeated Block blocks = 1;
}

message BatchGetBlocksReq {
  repeated uint32 tab_id = 1;
}

message BatchGetBlocksResp {
  map<uint32, BlocksResp> data = 1;
}

message GetOptionBlockListReq {
  enum BlockType {
    GAMEBLOCK = 0; // 老标题，比如游戏、区服、段位
    PLAYBLOCK = 1; // 玩法分类标题
  }
  BlockType block_type = 1; // 标题类型
  uint32 page = 2;
  uint32 count = 3;
}

message GetOptionBlockListResp {
  repeated Block block_list = 1;
}

message FiniteTabsReq {
  repeated Tab tabs = 1; // tabs 包含指定的所有类型，请求时只需填写id字段。
  RequestSource source = 2; // 区分流量来源，中台为1
}

message FiniteTabsResp {
  repeated Tab tabs = 1; // tabs 代表所有指定的类型。
}


message FiniteTabsByTagsReq {
  repeated uint32 tag_id = 1;
}

message FiniteTabsByTagsResp {
  repeated Tab tabs = 1; // tabs 代表所有指定的类型。
}

//

message TagOption {
  message TagItem {
    string src_string = 1; //客户端的显示的标签字符串
    string splice_string = 2; //选中后拼接用的标签字符串
    uint32 item_id = 3;
  }

  uint32 option_id = 1; //
  string source_title = 2; //对应原来游戏标签的标题
  string client_title = 3; //客户端显示的标题
  repeated TagItem tag_items = 4; //标签的对应关系
}

message TagContent {
  message SequenceItem {
    enum ItemType {//拼接类型
      SELF = 0; //使用自身文案(text)拼接
      TAG = 1; //使用标签的名称拼接
      TEXT = 2; //使用指定文案作分隔符拼接
    }
    uint32 option_id = 1; //对应的标签id
    string split_text = 2; //对应的分隔文案
    ItemType type = 3; //类型
  }
  string text = 1; //文案
  repeated SequenceItem sequence = 2; //拼接顺序
  uint32 content_id = 3;
}

message RoomNameConfigure {
  uint32 tab_id = 1; //主题房的tab id
  repeated TagOption tag_option = 2; //标签选项数据、“选填部分”
  repeated TagContent tag_content = 3; //文案数据、
  uint32 version = 10; //主题房对应类型取名配置的版本号
}

message GetRoomNameConfigureReq {
  uint32 tab_id = 1;
  bool need_origin_data = 2; //true获取源数据，false会返回缓存数据
}

message GetRoomNameConfigureResp {
  RoomNameConfigure configure = 1;
  bool is_data_null = 2;
}

message SetRoomNameConfigureReq {
  RoomNameConfigure configure = 1;
}

message SetRoomNameConfigureResp {

}

message GetAllTabOfCategoryReq {
  uint32 category_id = 1;
  RequestSource source = 2; // 区分流量来源，中台为1
}

message GetAllTabOfCategoryResp {
  repeated Tab tab_info = 1;
}

message SimpleBanner {
  string name = 1;
  string image_uri = 2; //图片链接
  string jump_uri = 3; //跳转链接
}

message SetSimpleBannerReq {
  repeated SimpleBanner banner_list = 1;
}

message SetSimpleBannerResp {

}

message GetSimpleBannerReq {
  bool need_cache = 1;
}

message GetSimpleBannerResp {
  repeated SimpleBanner banner_list = 1;
}

message InsertBlockReq {
  string title = 1;
  // Mode 是 Block 的选择模式
  enum Mode {
    SINGLE = 0;
    MULTI = 1;
  }
  Mode mode = 2;
  uint32 show_row = 3;
  repeated uint32 elem_ids = 4;
}

message InsertBlockResp {
  uint32 block_id = 1;
}

message UpdateBlockReq {
  uint32 id = 1;
  string title = 2;
  // Mode 是 Block 的选择模式
  enum Mode {
    SINGLE = 0;
    MULTI = 1;
  }
  Mode mode = 3;
  uint32 show_row = 4;
  repeated uint32 elem_ids = 5;
}

message UpdateBlockResp {
  bool result = 1;
}

message DeleteBlockReq {
  uint32 id = 1;
}

message DeleteBlockResp {
  bool result = 1;
}

message InsertElemReq {
  string title = 1;
  Contact contacts = 2;
}

message InsertElemResp {

}

message UpdateElemReq {
  uint32 id = 1;
  string title = 2;
  Contact contacts = 3;
}

message UpdateElemResp {

}

message DeleteElemReq {
  uint32 id = 1;
}

message DeleteElemResp {

}
/* ----------------------分类相关-------------------------- */
// buf:lint:ignore ENUM_PASCAL_CASE
enum categoryType {
  ITEM_DEFAULT = 0; // 默认类型兼容之前主题房类型
  MORE = 1; // 更多也没分类类型
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message Category {
  uint32 category_id = 1;   // 类目id
  string title = 2;         // 名称
  uint32 sort_desc = 3;     // 排序权重
  int64 update_time = 4;    // 更新时间
  categoryType category_type = 5;    // 类型，取默认枚举值0
  PlatformType platform_type = 6;     // 平台类型
  string new_category_title = 7;     //v5.5.0版本分类名称
  string image_url = 8;     //v5.5.0版本发布房间时选择分类的图片
  string icon = 9;           //房间发布筛选器小图标
  string mask_layer = 10;     //遮罩颜色
  uint32 can_select_num = 11;    //可选数量 0为可全选
  repeated uint32  minor_certification_market_ids = 12;//需要未成年认证的马甲包
  string category_alias_name = 13; //分类简称别名
  uint32 specialCategoryMapping = 14; //特殊分类标识，标识当前category是哪个分类，值见topic-channel_.proto CategoryType枚举
}

message AddCategoryTitleReq {
  string title = 1; // 分类标题
  categoryType category_type = 2;
  PlatformType platform_type = 3;
  string new_category_title = 4;  //v5.5.0版本分类名称
  string image_url = 5;     //v5.5.0版本发布房间时选择分类的图片
  string icon = 6;           //房间发布筛选器小图标
  string mask_layer = 7;     //遮罩颜色
  uint32 can_select_num = 8;    //可选数量 0为可全选
  repeated uint32  minor_certification_market_ids = 9;//需要未成年认证的马甲包

  string category_alias_name = 10; //分类首页简称外显名
}

message AddCategoryTitleResp {
  uint32 category_id = 1;
}

message UpdateCategoryTitleReq {
  uint32 category_id = 1;
  string title = 2;
  categoryType category_type = 3;
  PlatformType platform_type = 4;
  string new_category_title = 5;  //v5.5.0版本分类名称
  string image_url = 6;     //v5.5.0版本发布房间时选择分类的图片
  string icon = 7;           //房间发布筛选器小图标
  string mask_layer = 8;     //遮罩颜色
  uint32 can_select_num = 9;    //可选数量 0为可全选
  repeated uint32  minor_certification_market_ids = 10;//需要未成年认证的马甲包

  string category_alias_name = 11; //分类首页简称外显名

}
message UpdateCategoryTitleResp {
  bool result = 1;
}

message GetCategoryTitleReq {
  uint32 skip = 1;
  uint32 limit = 2;
  RequestSource source = 3; // 区分流量来源，中台为1

}
message GetCategoryTitleResp {
  repeated Category category_list = 1;
}
message GetCategoryTitleListForTTReq {
  uint32 skip = 1;
  uint32 limit = 2;
  RequestSource source = 3; // 区分流量来源，中台为1

}
message GetCategoryTitleListForTTResp {
  repeated Category category_list = 1;
}

message ReSortCategoryTitleReq {
  repeated uint32 category_id = 1; // 需要更新的分类id
}
message ReSortCategoryTitleResp {
  bool result = 1;
}

message DeleteCategoryTitleReq {
  repeated uint32 category_id = 1;
}
message DeleteCategoryTitleResp {
  bool result = 1;
}

message RelationOfEtChannel {
  string channel_room_type = 1;
  string find_playing_text = 2;
  string find_playing_img = 3;
}
message InsertRelationOfEtChannelReq {
  RelationOfEtChannel roc = 1;
}

message InsertRelationOfEtChannelResp {

}

message GetRelationOfEtChannelReq {

}

message GetRelationOfEtChannelResp {
  repeated RelationOfEtChannel rocs = 1;
}
//-------------------------首页玩法卡相关------------------------

enum CardType {
  DEFAULT = 0;
  MINIGAMECARD = 1;
  UGCCARD = 2;
  ADCARD = 3;
  PGCCARD = 4;
}

enum TabType {
  NORMAL = 0; //普通分类
  GAME = 1; //游戏分类
  MINIGAME = 2; //小游戏
  MUSIC = 3;
}

message MultilevelTitle {
  uint32 tab_id = 1;
  repeated Block block = 2;
  // repeated Block block = 4;
}
message AddMultilevelTitleReq {
  uint32 tab_id = 1;
  repeated Elem elem = 2;
}

message AddMultilevelTitleResp {
  bool result = 1;
}

message UpdateMultilevelTitleReq {
  uint32 tab_id = 1;
  uint32 block_id = 2;
  repeated Elem elem = 3; // 默认的模式elem，一定要放到最后，为了不影响其他elem顺序
}
message UpdateMultilevelTitleResp {
  bool result = 1;
}

message GetMultilevelTitleReq {
  repeated uint32 tab_ids = 1;
}

message GetMultilevelTitleResp {
  repeated MultilevelTitle multilevel_title = 1;
}

message GetMultilevelTitleForTTReq {
  uint32 skip = 1;
  uint32 limit = 2;
  TabType tab_type = 3; // 目前只是小游戏，传小游戏即可。
}
message GetMultilevelTitleForTTResp {
  repeated MultilevelTitle multilevel_title = 1;
}

message DeleteMultilevelTitleReq {
  uint32 tab_id = 1;
}
message DeleteMultilevelTitleResp {
  bool result = 1;
}

message GetMinorityGameTabsReq {

}
message GetMinorityGameTabsResp {
  Tab parent_tab = 1;
  repeated Tab child_tab = 2;
}

message TabArray{
  repeated Tab tabs = 1;
}
message GetTabsByCategoryIdsReq{
  repeated uint32 category_ids = 1;
  RequestSource source = 2; // 区分流量来源，中台为1
}
message GetTabsByCategoryIdsResp {
  map<uint32, TabArray> category_tab_map = 1;
}

enum HomePageType{
  HomePageTypeNone = 0; //未选择
  HomePageTypeGAME = 1; //游戏
  HomePageTypeMUSIC = 2; //音乐
}

message GameTabHomePageConfig{
  //    string small_card_url = 1;                //v5.5.0之后首页六格专用
  Tab.LabelType tab_label = 1;
  //    string mask_layer = 3; //tab图片遮罩颜色
  string room_label = 2; //房间卡标签

  string default_channel_condition = 3; //房间默认状态
  map<uint32, ChannelCondition> special_channel_condition = 4; //根据发布字段设置的个性化房间状态，key block_id

  string background_img_url = 5; //背景图片配图

  ChannelViewType game_view_type = 6;//游戏首页view

}

enum ViewType {
  ViewType_Default = 0; // 默认
  ViewType_Sing_A_Song = 1; // 你行你唱
  ViewType_KTV = 2; // 一起K歌
  ViewType_Leisure = 3; // 挂房听歌
  ViewType_Rap = 4;   //  rap
  ViewType_Chat = 5; //  聊天通用模板，如边唱边聊
}

message MusicTabHomePageConfig{
  ViewType view_type = 1;
  string default_channel_condition = 3; //房间默认状态
  map<uint32, ChannelCondition> special_channel_condition = 4; //根据发布字段设置的个性化房间状态，key block_id
}

message ModifyTabHomePageConfigReq{
  uint32 tab_id = 1;
  HomePageType home_page_type = 2;
  GameTabHomePageConfig game_tab_home_page_config = 3;
  MusicTabHomePageConfig music_tab_home_page_config = 4;

}

message ModifyTabHomePageConfigResp{

}

message ListTabHomePageConfigReq{
  uint32 category_id = 1;
  HomePageType home_page_type = 2;
}

message ListTabHomePageConfigResp{
  repeated TabHomePagePageListItem tab_items = 1;
}

message TabHomePagePageListItem{
  uint32 tab_id = 1;
  uint32 category_id = 2;
  string tab_name = 3;
  HomePageType home_page_type = 4;
}

message SortTabHomePageConfigReq{
  uint32 category_id = 1;
  HomePageType home_page_type = 2;
  repeated uint32 tab_ids = 3;
}

message SortTabHomePageConfigResp{

}

message ReleaseConditionItem{
  uint32 tab_id = 1;
  string tab_name = 2;
  repeated Block blocks = 3;
  uint32 release_duration = 4;
  TabType tab_type = 5;
  string  cards_image_url = 6;
  bool hide_filter = 7; // 是否隐藏发布条件筛选入口

  repeated GameLabelItem items = 8; //玩法关联项

  repeated DisplayBlockInfo display_block_infos = 9; //客户端外显管理

  HomePageType home_page_type = 10; //玩法属于哪个业务
}

//发布二级字段绑定的一级字段id
message ElemBindBlockInfo {
  uint32 elem_id = 1; //二级字段id
  message BlockElemGroup {
    uint32 related_block_id = 1; //关联的blockId
    repeated uint32 related_elem_ids = 2; //需要展示的elemId
  }
  repeated BlockElemGroup bind_block_groups = 2;//二级字段绑定的一级字段信息，客户端选中该二级字段后，需要展示的关联的一级字段部分筛选项
}

//客户端外显发布字段关系
message DisplayBlockInfo {
  uint32 block_id = 1; //一级字段id
  repeated ElemBindBlockInfo elem_bind_block_infos = 2; //该block下的二级字段的关联信息
}


message SetReleaseConditionReq{
  uint32 tab_id = 1;
  //deprecated 发布字段单独使用 SetBlockInfos去保存
  repeated Block blocks = 2;
  uint32 release_duration = 3;
  bool hide_filter = 4; // 是否隐藏发布条件筛选入口
  repeated GameLabelItem items = 5; // 玩法筛选项
  repeated DisplayBlockInfo display_block_infos = 6; //客户端外显管理

}

message SetReleaseConditionResp{

}


// 运营后台，发布房间配置，配置所有字段单独保存
message SetBlockInfosReq{
  uint32 tab_id = 1;
  repeated Block blocks = 2;
}

message SetBlockInfosResp{

}

// 运营后台，发布房间配置，获取指定tabId的所有发布字段信息
message GetBlockInfosReq{
  uint32 tab_id = 1;
}

message GetBlockInfosResp{
  repeated Block blocks = 1;
}

message GetReleaseConditionForTTReq{
  uint32 tab_id = 1;
}

message GetReleaseConditionForTTResp{
  ReleaseConditionItem item = 1;
}

message ListReleaseConditionForTTReq{
  string tab_name = 1;
  uint32 limit = 2;
  uint32 page = 3;
}

message ListReleaseConditionForTTResp{
  repeated ReleaseConditionItem items = 1;
  uint32 count = 2;
}

message ListReleaseConditionReq{

}

message ListReleaseConditionResp{
  repeated ReleaseConditionItem items = 1;
}

message BusinessBlock{
  enum FilterType{
    Base_Block_Filter = 0;       //基础筛选项，tab关联的发布字段
    Mystery_Game_Condition_Filter = 1;  //迷境游戏属性相关过滤项，目前包括房间模式、对局状态
  }
  FilterType filter_type = 1;
  uint32 id = 2;
  string title = 3;
  // Mode 是 Block 的选择模式
  enum Mode {
    SINGLE = 0;     //单选
    MULTI = 1;      //不限
    SETUP_NUMBER = 2;    //最多选N个，扩展用
  }
  Mode mode = 4;
  repeated BusinessElem elems = 5;
  uint32 most_select_num = 6; //当Mode=SETUP_NUMBER时有效

  enum Source {
    SourceHome = 0; // 首页
    SourceScreen = 1; // 半屏
  }

  Source source = 7;
}
// 业务筛选器Elem 元素，多个元素构成一个栏目
message BusinessElem {
  uint32 id = 1;
  string title = 2;
  uint32 mode = 3; //0 普通， 1推荐， 2不限

}

//批量获取业务筛选器信息
message BatchGetBusinessBlockInfoReq{

}
message BatchGetBusinessBlockInfoResp{
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  message BussBlockInfo{
    uint32 id = 1;
    enum BindType{
      Bind_Tab = 0;
      Bind_Category = 1;
    }
    BindType bind_type = 2;
    repeated BusinessBlock businessBlockInfo = 3;
  }
  repeated BussBlockInfo business_blocks = 1;
}

message OfficialRoomNameConfig{
  uint32 id = 1;
  uint32 tab_id = 2;
  string name = 3;
  uint32 elem_id = 4;
}

message SetOfficialRoomNameConfigReq{
  OfficialRoomNameConfig room_name_config = 1;
}
message SetOfficialRoomNameConfigResp{
}

message DelOfficialRoomNameConfigReq{
  uint32 id = 1;
  uint32 tab_id = 2;
}
message DelOfficialRoomNameConfigResp{
}

message ListOfficialRoomNameConfigReq{
  uint32 tab_id = 1;
  repeated uint32 elem_ids = 2;
}
message ListOfficialRoomNameConfigResp{
  repeated OfficialRoomNameConfig room_name_config_list = 1;
}

message RearrangeOfficialRoomNameConfigReq {
  repeated uint32 ids = 1; // 只给id就行
  uint32 tab_id = 2; // 玩法id
}
message RearrangeOfficialRoomNameConfigResp {
}

//批量获取所有的官方房间名信息，用作logic层缓存
message BatchGetOfficialRoomNameConfigReq{
  repeated uint32 tab_ids = 1;
}

message BatchGetOfficialRoomNameConfigResp{
  message RoomNameConfigForOneTab{
    uint32 tab_id = 1;
    repeated OfficialRoomNameConfig room_name_config_list = 2;
  }
  repeated RoomNameConfigForOneTab room_name_config_for_tabs = 1;
}

message UpdateBusinessConfReq {
  int32 id = 1;
  string tab_id_array = 2;
  string category_id_array = 3;
}

message UpdateBusinessConfResp {
}

enum HomePageHeadConfigEnum {
  CONFIG_GAME_TYPE = 0; // 游戏专区
  CONFIG_CASUAL_GAME_TYPE = 1; // 休闲互动游戏专区
  CONFIG_ESCAPE_ROOM_TYPE = 2; // 密室逃脱
  CONFIG_CHAT_TYPE = 3; // 扩列聊天
  CONFIG_MUSIC_TYPE = 4; // 音乐
  CONFIG_SOUND_SCENE_TYPE = 5; // 声动现场
  CONFIG_ACTIVITY_CENTER_TYPE = 6; // 活动中心
  CONFIG_COMPETITION_CENTER_TYPE = 7; // 赛事中心
}

message HomePageHeadConfigItem {
  string config_id = 1; // 配置id，用于识别新用户引导匹配
  HomePageHeadConfigEnum config_type = 2; // 专区类型
  string title = 3;// 主标题
  string sub_title = 4;// 副标题
  string background = 5;// 底图
  Vap vap = 6;// 动画效果配置
  string ad_tag = 7;// 广告标签，字数-最长配置7个字
  string jump_link = 8;// 专区客户端跳转短链
  bool is_replace = 9; // 是否可以被替换赛事中心
  string small_background = 10; // 金刚区收起时展示的小图
  GuideContent guide_content = 11;     // 展示的引导配置
  HomePageSubmodule submodule = 12;    // 子模块的配置
}

message Vap {
  string url = 1;
  string md5 = 2;
}

message GuideContent {
  string title = 1;    // 引导文案:主标题
  string sub_title = 2;    // 引导文案:副标题，为空不展示
}

message HomePageSubmodule {
  GuideContent guide_content = 1;
}

message HomePageHeadConfigReq {
}

message HomePageHeadConfigResp {
  repeated HomePageHeadConfigItem configs = 1;
}

message UpsertHeadConfigReq {
  repeated HomePageHeadConfigItem configs = 1;
}

message UpsertHeadConfigResp {
}

message DelPageHeadConfigReq {
  string config_id = 1;
}

message DelPageHeadConfigResp {
}

//批量获取玩法筛选器
message BatchGetGameLabelItemsReq {
  repeated uint32 tab_ids = 1;
}

message BatchGetGameLabelItemsResp {
  repeated TabGameLabelItem items = 1;
}

message TabGameLabelItem{
  uint32 tab_id = 1;
  repeated GameLabelItem items = 2;
}

// 玩法筛选项
message GameLabelItem {
  string display_name = 1; //筛选项外显名
  bool show_hot = 2; // 是否展示热门标识
  repeated string related_labels = 3; // 关联的玩法标签
}

message UpdateTabUGameIdReq {
  uint32 tab_id = 1;
  uint32 u_game_id = 2;
}

message UpdateTabUGameIdResp {
}

message UpdateTabMiniGameIdReq {
  uint32 tab_id = 1;
  uint32 mini_game_id = 2;
}

message UpdateTabMiniGameIdResp {
}

message GetTabByUGameIdReq {
  uint32 u_game_id = 1;
}

message GetTabByUGameIdResp {
  Tab tab = 1;
}

message GameInfoItem {
  uint32 u_game_id = 1;
  uint32 game_card_id = 2;
  string game_card_name = 3;
  string game_name = 4;
}

message GetTabsForManagementOpReq {
  uint32 skip = 1; // skip 表示上一次请求得到的类型总数，首次填0即可。
  uint32 limit = 2; // limit 表示本次请求希望获得的类型数量。
  uint32 category_id = 3;//分类id，传0为全部
}

message GetTabsForManagementOpResp {
  repeated Tab tabs = 1; // tabs 代表本次请求返回的所有类型。
  uint32 skip = 2; // skip 表示已请求得到的类型总数（包括本次请求）。
  uint32 limit = 3; // limit 表示本次请求实际获得的类型数量。
  uint32 total = 4; // total 表示数据库中保存类型的总量。
}

message BatchGetBlockRelationsReq{
  repeated uint32 tab_id = 1;
}

message BatchGetBlockRelationsResp{
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  message BlockRelations {
    uint32 tab_id = 1;
    repeated DisplayBlockInfo displayBlockInfo = 2;
  }
  map<uint32, BlockRelations> relations = 1;
}

message NewQuickMatchConfig {
  string id = 1;
  uint32 tab_id = 2;  // 玩法id, 每个玩法对应一个配置
  string tab_name = 3;
  string title = 4; // 标题
  string button_text = 5; // 按钮文案
  uint32 position = 6; // 展示位置, 快速匹配入口展示在列表第position个位置
  int64 updated_at = 7; // 更新时间, 单位:ms
}

message UpsertNewQuickMatchConfigReq {
  NewQuickMatchConfig config = 1;
}

message UpsertNewQuickMatchConfigResp {
}

message BatchGetNewQuickMatchConfigReq {
  uint32 tab_id = 1;  // 如果传了tab_id则按这个条件过滤
}

message BatchGetNewQuickMatchConfigResp {
  repeated NewQuickMatchConfig configs = 1;
}

message DelNewQuickMatchConfigReq {
  string id = 1;
}

message DelNewQuickMatchConfigResp {
}

message GetNewQuickMatchConfigReq {
  uint32 tab_id = 1;
}

message GetNewQuickMatchConfigResp {
  NewQuickMatchConfig config = 1;
}

// 配置玩法信息扩展内容，不同业务使用不同的结构体映射处理，往后新增
enum TabInfoExtEnum {
  TAB_INFO_EXT_NO_TYPE = 0; // 默认无
  TAB_INFO_EXT_HOME_PAGE_HEAD_CONFIG = 1; // 游戏专区绑定配置，see TabInfoExtHomePageHeadConfig
}

message GetAllTabInfoExtReq {
  TabInfoExtEnum ext_type = 1;
}

message TabInfoExtItem {
  uint32 tab_id = 1;
  TabInfoExtEnum ext_type = 2;
  string ext_info = 3;    // 保存的默认json格式，后续有需要再扩展其它
  int64 update_ts = 4;    // 更新时间，单位:ms
}

message GetAllTabInfoExtResp {
  repeated TabInfoExtItem items = 1;
}

message GetTabInfoExtReq {
  uint32 tab_id = 1;
  TabInfoExtEnum ext_type = 2;
}

message GetTabInfoExtResp {
  TabInfoExtItem item = 1;
}

message UpsertTabInfoExtReq {
  TabInfoExtItem item = 1;
}

message UpsertTabInfoExtResp {

}

message DelTabInfoExtReq {
  uint32 tab_id = 1;
  TabInfoExtEnum ext_type = 2;
}

message DelTabInfoExtResp {

}

// 金刚区的配置结果映射
message TabInfoExtHomePageHeadConfig {
  string tag_title = 1;// 轮播对应配置文案 运营后台第一行
  string tag_sub_title = 2;// 轮播对应配置文案 运营后台第二行
  string pic = 3; // 小图片url
  uint32 tab_id = 4;
  string tag_title_v2 = 5; // 6.49.0版本后使用的轮播配置主标题
  string button_text = 6; // 6.49.0版本后使用的轮播配置按钮文案
  repeated RichTagInfo rich_tag_infos = 7; // 6.56.5版本后使用的轮播配置
  string config_tab_id = 8; // 跳转到的config_tab_id
}

message RichTagInfo {
  string background_color = 1; // 底色
  string label_text = 2; // 标签文案
  string main_tag_text = 3; // 主标文案
}

message ResortTabInfoExtReq {
  TabInfoExtEnum ext_type = 1;
  repeated uint32 tab_ids = 2;
}

message ResortTabInfoExtResp {

}

message CacheData {
  repeated uint32 ids = 1;
}

message CacheBlockData {
  uint32 block_id = 1;
  repeated uint32 elem_ids = 2;
}

message CacheTabData {
  uint32 tab_id = 1;
  repeated CacheBlockData block_datas = 2;
}

enum CacheType {
  Unknow = 0;
  FromCache = 1;
  FromDb = 2;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CacheInfo {
  repeated uint32 categoryIds = 1;
  map<uint32, CacheData> category_tabs_map = 2;
  repeated uint32 tab_ids = 3;
  repeated uint32 min_game_tab_ids = 4;
  repeated CacheTabData tab_display_block_ids = 5;
  repeated CacheTabData tab_block_ids = 6;
  map<uint32, CacheData> GameCardTabsMap = 7;
  CacheType cache_type = 8;
}

message GetCacheReq {

}


message GetCacheResp {
  repeated CacheInfo infos = 1;
}

enum Scene {
  NO_DEFAULT = 0;
  MORE_TABS = 1;
  // TODO
}

message GetTabsByCategoryEnumReq{
  uint32 category_mapping = 1; //分类映射关系，见topic_channel_.proto CategoryType枚举，现有的枚举值如下
  //  enum CategoryType {
  //    Invalid_type = 0;
  //    Gangup_type = 1;  // 一起开黑类型
  //    CHAT_TYPE = 2;  // 旧版扩列聊天类型
  //    FUN_GAME_TYPE = 3; // 趣味玩法
  //    CASUAL_INTERACTION_TYPE = 4; // 休闲互动
  //    MUSIC_TYPE = 5; // 听歌唱歌
  //    MELEE_TYPE = 6; // 团战
  //    ESCAPE_ROOM_TYPE = 7; // 密室逃脱
  //    GROUP_CHAT_TYPE = 8; // 群聊派对
  //    NEW_CHAT_TYPE = 9; //新扩列聊天分类
  //  }
}

message GetTabsByCategoryEnumResp{
  repeated Tab tabs = 1;
}

enum ShieldTargetEnum {
  SHIELD_TARGET_INVALID = 0;
  SHIELD_TARGET_TT = 1; //tt马甲包
  SHIELD_TARGET_HUANYOU = 2; //欢游马甲包
  SHIELD_TARGET_MAIKE = 3; //麦客马甲包
  SHIELD_TARGET_MIJING = 4; //迷境马甲包
  SHIELD_TARGET_PC = 5; //PC -> 主播端PC
  SHIELD_TARGET_FAST_PC = 6; // 开黑端PC
}

// 全局配置，设置玩法屏蔽控制配置
message SetShieldSwitchReq {
  uint32 tab_id = 1; // 玩法id
  map<uint32, ShieldRule> shield_rule_map = 2; // key 马甲包类型见ShieldTargetEnum value 屏蔽规则
  string uid_white_list = 3; // 用户白名单,同tab表中的uid_white_list，写入tab表同个字段
  string uid_tail_num = 4; // 用户id尾号, 同tab表中的uid_tail_num，写入tab表同个字段
}

message ShieldRule{
  ShieldTargetEnum target = 1; // 屏蔽对象
  repeated Rules rules = 2; // 屏蔽规则
}

// 屏蔽的客户端类型
enum ShieldClientType {
  SHIELD_CLIENT_TYPE_INVALID = 0; // 无效
  SHIELD_CLIENT_TYPE_ANDROID = 1; // 安卓
  SHIELD_CLIENT_TYPE_IOS = 2; // ios
  SHIELD_CLIENT_TYPE_PC = 3; // pc
  SHIELD_CLIENT_TYPE_FAST_PC = 4; // 极速版 pc
}

message Rules {
  ShieldClientType client_type = 1; //需要屏蔽的客户端类型
  // 目前字段2-6运营后台只会填其中一项，代码中逻辑是所有字段满足其中一项即会屏蔽
  bool is_shield_all_version = 2; //是否屏蔽所有版本
  string higher_than_version = 3; //屏蔽高于该版本号的客户端
  string lower_than_version = 4; //屏蔽低于该版本号的客户端
  repeated string equal_versions = 5; //屏蔽指定版本号的客户端
  repeated string not_equal_versions = 6; //屏蔽版本号不等于该值的客户端
}

message SetShieldSwitchResp {
}

message BatchGetShieldSwitchByTabIdsReq {
  repeated uint32 tab_ids = 1; // 玩法id
  bool no_use_cache = 2; // 是否不使用缓存
}

message ShieldRuleForTab {
  uint32 tab_id = 1; // 玩法id
  map<uint32, ShieldRule> shield_rule_map = 2; // key 马甲包类型见ShieldTargetEnum value 屏蔽规则
}

message BatchGetShieldSwitchByTabIdsResp {
  map<uint32, ShieldRuleForTab> tab_rule_map = 1; // key tabId value 屏蔽控制配置
}

message TabQuestionConfig {
  // 0表示通用配置
  uint32 tab_id = 1;
  // 问题列表
  repeated TabQuestion questions = 3;
  // 年龄人群标签
  repeated string age_group_labels = 4;
  // 年龄触发关键词
  repeated string age_keywords = 5;
  // 性别触发关键词
  repeated string gender_keywords = 6;
}

message GetTabQuestionConfigReq {
  uint32 tab_id = 1;
}

message GetTabQuestionConfigResp {
  TabQuestionConfig config = 1;
}

message FastPCCategoryInfo {
  uint32 id = 1;
  string name = 2;
}

// 查询音乐filter映射的虚拟tabID,不存在则新建一个
message FindFilterMixTabIdsReq {
  repeated string filter_ids = 1;
}

message FindFilterMixTabIdsResp {
  map<string, uint32> mix_tab_map = 1; // key: filterId, value: mix_tab_id
}

message FindFilterIdsByMixTabIdsReq {
  repeated uint32 mix_tab_ids = 1; // mix_tab_id
}

message FindFilterIdsByMixTabIdsResp {
  map<uint32, string> mix_tab_filter_map = 1; // key: mix_tab_id, value: filter_ids
}