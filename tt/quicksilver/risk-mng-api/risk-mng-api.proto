syntax = "proto3";

package risk_mng_api;

option go_package = "golang.52tt.com/protocol/services/risk-mng-api";

service RiskMngApi {
  // 全功能检查风控，通过场景配置风控策略，对调用方透明
  rpc Check(CheckReq) returns (CheckResp) {}

  // 检查用户黑产，包含批量，默认查 darkserver
  rpc CheckUserBlack(CheckUserBlackReq) returns (CheckUserBlackResp) {}

  // 检查用户封禁，包含批量
  rpc CheckUserBan(CheckUserBanReq) returns (CheckUserBanResp) {}

  // 检查房间风控，包含批量
  rpc CheckChannel(CheckChannelReq) returns (CheckChannelResp) {}

  // 拦截层风控检查
  rpc Intercept(InterceptReq) returns (InterceptResp) {}

  // 全功能批量检查风控，通过场景配置风控策略，对调用方透明
  rpc BatchCheck(BatchCheckReq) returns (BatchCheckResp) {}

  // 设置用户行为规则，例如禁止上麦等
  rpc SetUserActionRule(SetUserActionRuleReq) returns (SetUserActionRuleResp) {}
  // 获取用户行为规则
  rpc GetUserActionRule(GetUserActionRuleReq) returns (GetUserActionRuleResp) {}

  // 设置风险属性，例如黑产、封禁
  rpc SetRiskAttr(SetRiskAttrReq) returns (SetRiskAttrResp) {}
}

message FaceAuthInfo {
  // 统一人脸结果token
  string result_token = 1;
  // 人脸结果token
  string token = 5;
  // 人脸供应商，废弃
  string provider_code = 6;
  // 人脸供应商结果数据，废弃
  string provider_result_data = 7;
}

message Entity {
  // uid
  uint32 uid = 1;
  // 房间内部id
  uint32 channel_id = 2;
  // 设备id base64编码
  string device_id_b64 = 3;
  // 客户端ip
  string client_ip = 4;
  // 手机号
  string phone = 5;
  // 人脸认证信息
  FaceAuthInfo face_auth_info = 6;
  // 终端类型
  uint32 terminal_type = 7;
  // 群组id
  uint32 group_id = 8;
  // 公会id
  uint32 guild_id = 9;
  // 房间外显id
  string channel_view_id = 10;
  // 设备id
  bytes device_id_raw = 11;
  // 客户端版本号
  uint32 client_version_code = 12;
  // 客户端版本 1.1.0
  string client_version = 13;
  // 设备id hex 编码
  string device_id_hex = 14;
  // 客户端直接上报的设备id，不同端的格式不同，对该字段会自动转换成统一格式
  string client_device_id = 15;
}

message RiskCommInfo {
  // 用户已确认，忽略风控告警
  bool ignore_warning = 1;
  // 风控检查目标文本
  string target_text = 2;
  // 风控检查目标类型
  string target_type = 3;
  // 风控token
  string token = 4;
}

message ChannelOpt {
  // 房主UID
  uint32 channel_owner_uid = 1;
  // 房间类型，see ga.ChannelType
  uint32 channel_type = 2;
  // 房间名
  string channel_name = 3;
  // 当前房间人数（不包含当前用户）
  uint32 channel_user_cnt_no_actor = 4;
  // 进房方式，see ga.EChannelEnterSource
  uint32 enter_source = 5;
  //玩法ID
  uint32 scheme_id = 6;
  //玩法类型
  uint32 scheme_detail_type = 7;
}

message GroupOpt {
  // 群主UID
  uint32 group_owner_uid = 1;
  // 群组类型，see ga.GuildGroup.GroupType
  uint32 group_type = 2;
  // 群组名称
  string group_name = 3;
  // 当前群聊人数（不包含当前用户）
  uint32 group_user_cnt_no_actor = 4;
  // 群创建人UID
  uint32 group_creator_uid = 5;
}

message GuildOpt {
  // 会长UID
  uint32 guild_owner_uid = 1;
}

message CheckUserBlackReq {
  // 场景
  string scene = 1;
  // uid列表
  repeated uint32 uid_list = 2;
}

message CheckUserBlackResp {
  // 黑产结果
  map<uint32, bool> user_black_map = 1;
}

message CheckUserBanReq {
  // 场景
  string scene = 1;
  // 实体列表
  repeated Entity id_entity_list = 2;
}

message CheckUserBanResp {
  // 封禁结果
  map<uint32, bool> user_ban_map = 1;
}

message CheckChannelReq {
  // 场景
  string scene = 1;
  // 房间id列表
  repeated uint32 channel_id_list = 2;
}

message CheckChannelResp {
  // 命中结果
  map<uint32, bool> channel_hit_map = 1;
}

message CheckReq {
  // 场景
  string scene = 1;
  // 实体
  Entity source_entity = 2;
  // 目标实体
  Entity target_entity = 3;
  // 房间选项
  ChannelOpt channel_opt = 4;
  // 风控通用信息
  RiskCommInfo risk_comm_info = 5;
  // 群组选项
  GroupOpt group_opt = 6;
  // 公会选项
  GuildOpt guild_opt = 7;
  // 自定义参数
  map<string, string> custom_params = 8;
  // 内审请求
  AuditReq audit_req = 9;
}

enum RiskActionType {
  // 未指定
  RISK_ACTION_TYPE_UNSPECIFIED = 0;
  // 通过
  RISK_ACTION_TYPE_PASS = 1;
  // 拦截
  RISK_ACTION_TYPE_REJECT = 2;
  // 用户风控验证
  RISK_ACTION_TYPE_VERIFY = 3;
}

message CheckResp {
  // ga.BaseResp.ret
  int32 err_code = 1;
  // ga.BaseResp.err_msg
  string err_msg = 2;
  // ga.BaseResp.err_info
  bytes err_info = 3;
  // 风控令牌
  string risk_token = 4;
  // 风控结果动作分类
  RiskActionType action_type = 5;
  // 内审结果
  AuditResp audit_resp = 6;
}

message BatchCheckReq {
  // 请求列表
  map<uint32, CheckReq> check_req_list = 1;
}

message BatchCheckResp {
  // 应答列表
  map<uint32, CheckResp> check_resp_list = 1;
}

message InterceptReq {
  // 场景
  string scene = 1;
  // 实体
  Entity entity = 2;
  // 风控通用信息
  RiskCommInfo risk_comm_info = 3;
}

message InterceptResp {
  // ga.BaseResp.ret
  int32 err_code = 1;
  // ga.BaseResp.err_msg
  string err_msg = 2;
  // ga.BaseResp.err_info
  bytes err_info = 3;
}

enum OpType {
  // 未指定
  OP_TYPE_UNSPECIFIED = 0;
  // 开启
  OP_TYPE_ENABLE = 1;
  // 关闭
  OP_TYPE_DISABLE = 2;
}

// 行为规则处置
enum ActionRuleType {
  // 未指定
  ACTION_RULE_TYPE_UNSPECIFIED = 0;
  // 禁止发布房间
  ACTION_RULE_TYPE_PUBLISH_ROOM = 1;
  // 用户在房间内禁止发公屏消息
  ACTION_RULE_TYPE_ROOM_PUBLISH_MSG = 2;
  // 用户在房间内禁止上麦
  ACTION_RULE_TYPE_ROOM_CLICK_MIC = 3;
  // 禁止进入娱乐房(包括 公会公开房、语音直播房)
  ACTION_RULE_TYPE_ENTER_ENTERTAINMENT_ROOM = 4;
  // 禁止进入约玩房(包括 除了 公会公开房、语音直播房 之外的其他房间)
  ACTION_RULE_TYPE_ENTER_NOT_ENTERTAINMENT_ROOM = 5;
  // 用户在房间内禁止上麦
  ACTION_RULE_TYPE_ROOM_CLICK_MIC_V2 = 6;
  // IM场景下违规内容自见
  ACTION_RULE_TYPE_IM_NOT_SEEN = 7;
  // 房间公屏场景下违规内容自见
  ACTION_RULE_TYPE_CHANNEL_IM_NOT_SEEN = 8;
  // 用户在房间内禁止上麦，仅个人房
  ACTION_RULE_TYPE_ROOM_CLICK_MIC_V3 = 9;
  // 用户头像
  ACTION_RULE_TYPE_USER_AVATAR = 10;
  // 用户昵称
  ACTION_RULE_TYPE_USER_NICKNAME = 11;
  // 房间头像
  ACTION_RULE_TYPE_CHANNEL_AVATAR = 12;
  // 房间名称
  ACTION_RULE_TYPE_CHANNEL_NAME = 13;
  // 自限黑产
  ACTION_RULE_TYPE_AUTO_BLACK = 14;
  // 禁止修改签名
  ACTION_RULE_TYPE_MODIFY_SIGNATURE = 15;
  // 禁止修改房间欢迎语
  ACTION_RULE_TYPE_CHANNEL_MODIFY_WELCOMEMSG = 16;
  // 禁止修改房间话题
  ACTION_RULE_TYPE_CHANNEL_MODIFY_TOPIC = 17;
  // 禁止修改房间麦位名称
  ACTION_RULE_TYPE_CHANNEL_MODIFY_MIC_NAME = 18;
  // mt房间点赞
  ACTION_RULE_TYPE_MT_CHANNEL_LIKE = 19;
  // 广场动态评论
  ACTION_RULE_TYPE_ADD_CONTENT_COMMENT = 20;
  // 广场动态
  ACTION_RULE_TYPE_ADD_CONTENT_POST = 21;
  // 社团讨论贴发布/编辑
  ACTION_RULE_TYPE_ADD_CONTENT_SOCIAL_COMMUNITY_POST = 22;
  // 社团讨论贴评论
  ACTION_RULE_TYPE_ADD_CONTENT_SOCIAL_COMMUNITY_COMMENT = 23;
  // 开麦静默处置
  ACTION_RULE_TYPE_CHANNEL_MIC_SILENT_USER = 24;
}

message SetUserActionRuleReq {
  // uid
  uint32 uid = 1;
  // 规则类型
  ActionRuleType rule_type = 2;
  // 操作类型
  OpType op_type = 3;
  // 开始时间戳，秒
  int64 begin_at = 4;
  // 结束时间戳，秒
  int64 end_at = 5;
  // 操作原因
  string reason = 6;
  // 操作人
  string operator_name = 7;
  // 生效的马甲包id列表，为空时表示全部生效
  repeated uint32 market_id_list = 8;
  // 生效的终端类型列表，为空时表示全部生效
  repeated uint32 client_type_list = 9;
  // 自定义参数
  map<string, string> custom_params = 10;
}

message SetUserActionRuleResp {}

message UserActionRule {
  // 记录id
  uint32 id = 1;
  // uid
  uint32 uid = 2;
  // 规则类型
  ActionRuleType rule_type = 3;
  // 操作类型
  OpType op_type = 4;
  // 开始时间戳，秒
  int64 begin_at = 5;
  // 结束时间戳，秒
  int64 end_at = 6;
  // 更新时间戳，秒
  int64 update_at = 7;
  // 操作原因
  string reason = 8;
  // 操作人
  string operator_name = 9;
  // 生效的马甲包id
  int32 market_id = 10;
  // 生效的终端类型
  int32 client_type = 11;
}

message GetUserActionRuleReq {
  // uid
  uint32 uid = 1;
}

message GetUserActionRuleResp {
  // 行为规则列表
  repeated UserActionRule rule_list = 1;
}

message SetRiskAttrReq {}

message SetRiskAttrResp {}

enum RiskAttrType {
  // 未指定
  RISK_ATTR_TYPE_UNSPECIFIED = 0;
  // 黑产
  RISK_ATTR_TYPE_BLACK = 1;
  // 封禁
  RISK_ATTR_TYPE_BAN = 2;
}

enum EntityType {
  // 未指定
  ENTITY_TYPE_UNSPECIFIED = 0;
  // 用户: uid
  ENTITY_TYPE_USER = 1;
  // 房间: channelId
  ENTITY_TYPE_CHANNEL = 2;
}

//topic:risk_mng_event
message RiskEvent {
  // 用户、房间等
  EntityType entity_type = 1;
  // 实体id
  string entity_id = 2;

  // 黑产、封禁等
  RiskAttrType risk_attr_type = 3;
  // 禁言、禁房等
  ActionRuleType action_rule_type = 4;

  // 操作类型
  OpType op_type = 5;
  // 开始时间戳，秒
  int64 begin_at = 6;
  // 结束时间戳，秒，为 0 表示永久
  int64 end_at = 7;
  // 更新时间戳，秒
  int64 update_at = 8;
  // 操作原因
  string reason = 9;
  // 操作人
  string operator_name = 10;
  // 生效的马甲包id，为空则默认全部生效
  repeated int32 market_id_list = 11;
  // 生效的终端类型，为空则默认全部生效
  repeated int32 client_type_list = 12;
}

message Text {
  // 内容
  string content = 1;
}

message Content {
  // 内容类型
  oneof content_type {
    // 文本
    Text text = 1;
  }
}

message AuditReq {
  // 审核内容
  repeated Content content_list = 1;
}

enum AuditResultType {
  // 未指定
  AUDIT_RESULT_UNSPECIFIED = 0;
  // 通过
  AUDIT_RESULT_PASS = 1;
  // 拒绝
  AUDIT_RESULT_REJECT = 2;
  // 疑似、待人审
  AUDIT_RESULT_REVIEW = 3;
}

message AuditResult {
  // 审核结果
  AuditResultType result_type = 1;
}

message AuditResp {
  // 审核结果
  repeated AuditResult result = 1;
}
