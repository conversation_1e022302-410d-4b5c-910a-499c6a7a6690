syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/unified-search";

package unified_search;

service UnifiedSearch {
    //开黑列表自定义筛选器
    rpc AddRisky(AddRiskyReq) returns (AddRiskyResp);
    rpc RemoveRisky(RemoveRiskyReq) returns (RemoveRiskyResp);
    rpc CheckRisky(CheckRiskyReq) returns (CheckRiskyResp);
    rpc CheckRiskyById(CheckRiskyByIdReq) returns (CheckRiskyByIdResp);

    //
    rpc BatchAddAlreadyVisited(BatchAddAlreadyVisitedReq)returns(BatchAddAlreadyVisitedResp);
    rpc BatchDeleteAlreadyVisitedObjectTypes(BatchDeleteAlreadyVisitedObjectTypesReq)returns(BatchDeleteAlreadyVisitedObjectTypesResp);
    rpc BatchFilterAlreadyVisited(BatchFilterAlreadyVisitedReq)returns(BatchFilterAlreadyVisitedResp);
    
}


message VisitedItem {
    repeated string ids = 1;
    ObjectType object_type = 2;
}

message BatchAddAlreadyVisitedReq {
    uint32 uid = 1;
    repeated VisitedItem items = 2;
}


message BatchAddAlreadyVisitedResp {
    
}

message BatchDeleteAlreadyVisitedObjectTypesReq {
    uint32 uid = 1;
    repeated ObjectType object_type = 3;
}

message BatchDeleteAlreadyVisitedObjectTypesResp {
   
}

message BatchFilterAlreadyVisitedReq {
    uint32 uid = 1;
    repeated VisitedItem items = 2;
}

message BatchFilterAlreadyVisitedResp {
    repeated VisitedItem items = 1;
}


enum ObjectType {
    UNKNOWN = 0;
    USER = 1;
    CHANNEL = 2;
    GUILD = 3;
}

message RiskyObject {
    uint32 id = 1;
    ObjectType object_type = 2;
    uint32 expire_seconds = 3;
}

message AddRiskyReq{
    repeated RiskyObject objects = 1;

    enum SceneType {
        UNKNOWN = 0;
        EGG = 1; // 转转
        OnePiece = 2;   // 航海
        StarTrek = 3;   // 星级巡航
        CatCanteen = 4; // 猫猫餐厅
        StarTrain = 5;  // 摘星列车
        // web 营收活动
        WebRevenueActivity = 6;
    }
    SceneType scene = 2; // expire_at为0时使用对应场景的默认淘汰时间

    // 场景详情
    string scene_detail = 3;
}

message AddRiskyResp{
}

message RemoveRiskyReq{
    repeated RiskyObject objects = 1;
}
message RemoveRiskyResp{
}

message CheckRiskyReq{
    repeated RiskyObject objects = 1;
}
message CheckRiskyResp{
    repeated bool hits = 1;
}

message CheckRiskyByIdReq{
    ObjectType object_type = 1;
    repeated uint32 ids = 2;
}
message CheckRiskyByIdResp{
    repeated bool hits = 1;
    // 命中结果， key 为对象id， value 为命中结果， 检查命中才存在 map.
    map<uint32, RiskCheckResult> check_result_map = 2;
}

message RiskCheckResult {
    // 命中场景类型， see AddRiskyReq.SceneType
    uint32 scene_type = 1;
    // 命中场景详情
    string scene_detail = 2;
}