syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-operate-permission-checker";
package channel_operate_permission_checker;

message CheckOperatePermissionReq {
    uint32 business_id = 1;    //业务方id,参考channel-operate-permission-mgr的BusinessId枚举
    uint32 opt = 2;            //房间操作类型，参考channel-operate-permission-mgr的ChannelOperate枚举
    uint32 opt_uid = 3;
    uint32 cid = 4;
    uint32 scheme_id = 5;
    uint32 scheme_detail_type = 6;
    uint32 channel_bind_id = 7;
    uint32 channel_type = 8;
    uint32 target_uid = 9;
    string rule_name = 10;     //一个业务方下，可以有多条rule，该字段用于匹配到是哪条rule
    bytes extend_pb_data = 11; //房间操作的扩展信息，根据opt值反序列化出不同的扩展消息(如果只是为了填操作的请求信息，应该用下面的original_request字段)
    bytes original_request = 12; //房间操作的原始请求(客户端发起的请求)
}
message CheckOperatePermissionResp {
    int32 permission_err_code = 1;  //如果有操作权限，permission_err_code；如果没有操作权限，则以permission_err_code作为错误吗返回给客户端
    string permission_err_msg = 2;   //如果没有操作权限且该值不为空，以该值作为toast,否则用permission_err_code对应的msg做为toast返回给客户端
}

// 麦位相关操作携带扩展字段
message MicOperateExtendInfo {
  uint32 mic_id = 1;
  uint32 mic_state = 2;   // 设置麦位状态操作才会携带
}

//业务方实现该接口，自定义操作权限逻辑
service ChannelOperatePermissionChecker {
    //如果没有操作权限，应该把对应的错误码填到resp的permission_err_code里，不能直接在接口里返回没有操作权限的错误,接口里返回的错误会用来做熔断降级
    rpc CheckOperatePermission(CheckOperatePermissionReq) returns(CheckOperatePermissionResp) {}
}