syntax = "proto3";

package channel_wedding;
import "tt/quicksilver/extension/options/options.proto";
option go_package = "golang.52tt.com/protocol/services/channel-wedding";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service ChannelWedding {
  option (service.options.service_ext) = {
    service_name: "channel-wedding"
  };

  // 获取房间婚礼信息
  rpc GetChannelWeddingInfo(GetChannelWeddingInfoReq) returns (GetChannelWeddingInfoResp) {}

  // 获取用户正在进行的婚礼信息
  rpc GetUserChannelWeddingInfo(GetUserChannelWeddingInfoReq) returns (GetUserChannelWeddingInfoResp) {}

  // 批量获取房间婚礼简略信息
  rpc BatchGetChannelWeddingSimpleInfo(BatchGetChannelWeddingSimpleInfoReq) returns (BatchGetChannelWeddingSimpleInfoResp) {}

  // 预约婚礼
  rpc ReserveWedding(ReserveWeddingReq) returns (ReserveWeddingResp) {}

  // 婚礼进行中上报新增伴郎伴娘
  rpc ReportWeddingBridesmaidMan(ReportWeddingBridesmaidManReq) returns (ReportWeddingBridesmaidManResp) {}

  // 切换婚礼阶段
  rpc SwitchWeddingStage(SwitchWeddingStageReq) returns (SwitchWeddingStageResp) {}

  // 拍合照
  rpc TakeWeddingGroupPhoto(TakeWeddingGroupPhotoReq) returns (TakeWeddingGroupPhotoResp) {}

  // 获取用户可切换的姿势列表
  rpc GetUserWeddingPoseList(GetUserWeddingPoseListReq) returns (GetUserWeddingPoseListResp) {}

  // 切换姿势
  rpc SetUserWeddingPose(SetUserWeddingPoseReq) returns (SetUserWeddingPoseResp) {}

  // 切换朝向
  rpc SetUserWeddingOrientation(SetUserWeddingOrientationReq) returns (SetUserWeddingOrientationResp) {}

  // 批量获取用户姿势
  rpc BatchGetUserWeddingPose(BatchGetUserWeddingPoseReq) returns (BatchGetUserWeddingPoseResp) {}

  // 获取房间合照麦位位置
  rpc GetWeddingGroupPhotoSeatList(GetWeddingGroupPhotoSeatListReq) returns (GetWeddingGroupPhotoSeatListResp) {}

  // 设置用户合照位置
  rpc SetUserWeddingGroupPhotoSeat(SetUserWeddingGroupPhotoSeatReq) returns (SetUserWeddingGroupPhotoSeatResp) {}

  // 增加婚礼伴郎伴娘
  rpc AddWeddingBridesmaidMan(AddWeddingBridesmaidManReq) returns (AddWeddingBridesmaidManResp) {}

  // 根据开始时间范围获取婚礼场次列表
  rpc GetWeddingScheduleList(GetWeddingScheduleListReq) returns (GetWeddingScheduleListResp) {}

  // 获取用户结婚证
  rpc GetUserWeddingCertificate(GetUserWeddingCertificateReq) returns (GetUserWeddingCertificateResp) {}

  // 获取用户婚礼沉淀信息
  rpc GetUserWeddingWeddingClips(GetUserWeddingWeddingClipsReq) returns (GetUserWeddingWeddingClipsResp) {}

  // 上报婚礼场景片段图片
  rpc ReportWeddingScenePic(ReportWeddingScenePicReq) returns (ReportWeddingScenePicResp) {}

  // 分页获取进行中的婚礼列表
  rpc PageGetGoingWeddingList (PageGetGoingWeddingListReq) returns (PageGetGoingWeddingListResp) {}

  // 根据给定时间范围获取结婚证列表
  rpc GetWeddingRecordByTimeRange(GetWeddingRecordByTimeRangeReq) returns (GetWeddingRecordByTimeRangeResp) {}

  // 测试婚礼场景im图片
  rpc TestWeddingScenePicIm(TestWeddingScenePicImReq) returns (TestWeddingScenePicImResp) {}

  // 获取婚礼高光时刻礼物
  rpc GetWeddingHighLightPresent(GetWeddingHighLightPresentRequest) returns (GetWeddingHighLightPresentResponse) {}

  rpc BatchGetWeddingHappiness(BatchGetWeddingHappinessRequest) returns (BatchGetWeddingHappinessResponse) {}

  // 获取房间爱侣榜信息
  rpc GetChannelWeddingRankInfo(GetChannelWeddingRankInfoReq) returns (GetChannelWeddingRankInfoResp) {}

  // 获取房间爱侣榜入口信息
  rpc GetChannelWeddingRankEntryInfo(GetChannelWeddingRankEntryInfoReq) returns (GetChannelWeddingRankEntryInfoResp) {}

  // 发送预约礼物
  rpc SendWeddingReservePresent(SendWeddingReservePresentReq) returns (SendWeddingReservePresentResp) {}

  // 测试免费婚礼付费引流IM
  rpc TestAfterFreeWeddingImXml(TestAfterFreeWeddingImXmlReq) returns (TestAfterFreeWeddingImXmlResp) {}

  // ===================== 预约礼物对账 =========================
  //婚礼房预约礼物->礼物
  rpc ReservePresentTimeRangeCount (ReconcileV2.TimeRangeReq ) returns (ReconcileV2.CountResp ) {}

  // 获取礼物订单
  rpc ReservePresentTimeRangeOrderIds (ReconcileV2.TimeRangeReq ) returns (ReconcileV2.OrderIdsResp ) {}

  //补单
  rpc FixReservePresentCountOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}
}

// 婚礼阶段枚举
enum WeddingStage {
  WEDDING_STAGE_UNSPECIFIED = 0;        // 未指定/未开始
  WEDDING_STAGE_WELCOME_GUEST = 1;      // 迎接嘉宾
  WEDDING_STAGE_BRIDE_GROOM_ENTER = 2;  // 新人进场
  WEDDING_STAGE_LOVE_DECLARATION = 3;   // 爱的宣言
  WEDDING_STAGE_EXCHANGE_RING = 4;      // 交换戒指
  WEDDING_STAGE_HIGHLIGHT = 5;          // 高光时刻
  WEDDING_STAGE_GROUP_PHOTO = 6;        // 合影留恋
}

// 合影留恋子阶段枚举
enum GroupPhotoSubStage {
  GROUP_PHOTO_SUB_STAGE_UNSPECIFIED = 0;
  GROUP_PHOTO_SUB_STAGE_PHOTOGRAPH = 1; // 拍合照
}

// 婚礼房阶段配置
message WeddingStageCfg {
  uint32 stage = 1;  // 阶段, see WeddingStage
  string stage_name = 2;   // 阶段名称
  uint32 sub_stage = 3;    // 子阶段
}

// 当前婚礼阶段信息
message WeddingStageInfo {
  repeated WeddingStageCfg stage_cfg_list = 1;  // 阶段配置
  uint32 curr_stage = 2;  // 当前阶段, see WeddingStage
  uint32 sub_stage = 3;    // 子阶段
  int64 stage_start_ts = 4;  // 阶段开始时间,秒级时间戳
  int64 stage_end_ts = 5;    // 阶段结束时间,秒级时间戳
}

// [废弃]婚礼等级信息
message WeddingLevelInfo {
  uint32 level = 1;           // 等级

  repeated uint32 groom_clothes = 2;   // 新郎婚服虚拟形象物品id列表
  repeated uint32 bride_clothes = 3;   // 新娘婚服虚拟形象物品id列表
}


message WeddingSceneBoneCfg {
  uint32 level = 1;
  uint32 seq_index = 2; // 分镜顺序号
  string animation_name = 3;// 动画名称
  uint32 bone_id = 4;  // 骨骼资源ID
  uint32 base_bone_id = 5;  // 基础骨骼资源ID
}

// 婚礼场景动画枚举
enum WeddingScene {
  WEDDING_SCENE_UNSPECIFIED = 0;
  WEDDING_SCENE_BRIDE_GROOM_ENTER = 1;  // 新人进场
  WEDDING_SCENE_EXCHANGE_RING = 2;      // 交换戒指
  WEDDING_SCENE_HIGHLIGHT = 3;          // 高光时刻
  WEDDING_SCENE_GROUP_PHOTO = 4;        // 合影留恋
}

message WeddingSceneCfg {
  uint32 scene = 1;           // 场景, see WeddingScene
  string scene_resource = 2;  // 场景动画资源
  string scene_resource_md5 = 3;
  repeated WeddingSceneBoneCfg bone_cfg_list = 4;  // 骨骼配置
}

message WeddingLevelClothes {
  uint32 level = 1;           // 等级
  repeated uint32 groom_clothes = 2;   // 新郎婚服虚拟形象物品id列表
  repeated uint32 bride_clothes = 3;   // 新娘婚服虚拟形象物品id列表
  repeated uint32 groomsman_clothes = 4;  // 伴郎婚服虚拟形象物品id列表
  repeated uint32 bridesmaid_clothes = 5;  // 伴娘婚服虚拟形象物品id列表
}

// 婚礼房等级背景配置
message WeddingLevelBackgroundCfg {
  uint32 level = 1;           // 等级
  string background_picture = 2;  // 背景资源图片
  string background_mp4_url = 3;  // 背景mp4
  string special_background_picture = 4;  // 结婚宣言、互换戒指 需要特殊的背景资源图片
  string special_background_mp4_url = 5;  // 背景mp4
}

message WeddingResource {
  string resource_url = 1;
  string resource_md5 = 2;
  uint32 cp_bone_id = 3;  // 双人骨骼资源id
  repeated uint32 item_ids = 4;  // 皮肤物品id列表
  uint32 base_cp_bone_id = 5;
}

// 婚礼房主题配置
message WeddingRoomThemeCfg {
  uint32 theme_id = 1;        // 主题ID
  string theme_resource = 2;  // 主题资源
  string theme_resource_md5 = 3;
  repeated WeddingSceneCfg scene_cfg_list = 4;  // 场景动画配置，需预下载资源

  string chair_game_resource = 5;     // 抢椅子游戏资源
  string chair_game_resource_md5 = 6;

  repeated WeddingLevelClothes level_clothes_list = 7;  // 等级服装配置， 需预下载虚拟形象组件资源
  repeated WeddingLevelBackgroundCfg level_background_list = 8;  // 等级背景配置

  WeddingResource wedding_preview_resource = 9;  // 婚礼预告资源
  uint32 chair_resource_id = 10;  // 椅子资源ID
  bool is_free_theme = 11;  // 是否是免费婚礼主题
  ChairGameResourceCfg chair_res_cfg = 12;  // 抢椅子游戏资源配置
}

// 抢椅子 椅子资源配置
message ChairGameResourceCfg {
    string chair_pic = 1;  // 椅子切图
    uint32 sitting_pose_female_id = 2;  // 坐姿ID-女
    uint32 sitting_pose_male_id = 3;    // 坐姿ID-男

    uint32 standby_female_id = 4; // 待机女性物品ID
    uint32 standby_male_id = 5;   // 待机男性物品ID
    repeated uint32 fail_female_ids = 6;    // 女-失败皮肤物品id列表
    repeated uint32 fail_male_ids = 7;     // 男
}

// 婚礼新人信息
message WeddingCpMemInfo {
  uint32 uid = 1;
}

// 新人纪念视频
message WeddingMemorialVideo {
  string resource_url = 1;
  string resource_md5 = 2;
  string resource_json = 3;
  repeated string user_pictures = 4;  // 用户照片列表
}

// 骨骼配置
message WeddingBoneCfg {
  uint32 male_bone_id = 1;    // 男性骨骼物品ID
  uint32 female_bone_id = 2;  // 女性骨骼物品ID
  uint32 base_male_bone_id = 3;
  uint32 base_female_bone_id = 4;
}

// 幸福值等级配置信息
message HappinessLevelInfo{
  uint32 level = 1 ;
  uint32 level_value = 2;      // 当前等级幸福值最大值
}

// 幸福值配置信息
message HappinessConfigInfo{
  repeated  HappinessLevelInfo config = 1;   // 幸福值等级配置
}

// 婚礼信息
message WeddingInfo {
  uint32 cid = 1;                     // 房间ID
  int64 wedding_id = 2;               // 婚礼ID
  WeddingStageInfo stage_info = 3;    // 阶段信息
  WeddingRoomThemeCfg theme_cfg = 4;  // 主题配置
  WeddingCpMemInfo bride = 5;         // 新娘信息
  WeddingCpMemInfo groom = 6;         // 新郎信息
  int64 start_time = 7;               // 开始时间
  int64 end_time = 8;                 // 结束时间
  WeddingMemorialVideo wedding_memorial_video = 9;  // 新人纪念视频

  bool chair_game_entry = 10;  // 是否开启椅子游戏入口
  repeated uint32 bridesmaid_man_list = 11;  // 伴郎伴娘uid列表
  uint32 curr_level = 12;              // 当前等级

  HappinessConfigInfo happiness_config = 13;  // 幸福值配置
  uint32 curr_happiness_value = 14;  // 当前幸福值

  WeddingBoneCfg bone_cfg = 15; // 骨骼配置

  int64 reserve_time = 16; // 预约时间
  uint32 plan_id = 17; // 计划ID
}

// 获取房间婚礼信息请求
message GetChannelWeddingInfoReq {
  uint32 uid = 1;
  uint32 cid = 2;  // 房间ID
}

// 获取房间婚礼信息响应
message GetChannelWeddingInfoResp {
  WeddingInfo wedding_info = 1;  // 婚礼信息
  WeddingPresentCountInfo present_count_info = 2; // 婚礼收送礼物值信息(计数器)
}

// 获取新人正在进行的婚礼信息请求
message GetUserChannelWeddingInfoReq {
  uint32 uid = 1;
}

// 获取新人正在进行的婚礼信息响应
message GetUserChannelWeddingInfoResp {
  WeddingInfo wedding_info = 1;  // 婚礼信息
}

message SimpleWeddingInfo {
  uint32 cid = 1;                     // 房间ID
  int64 wedding_id = 2;               // 婚礼ID
  int64 start_time = 3;               // 开始时间
  int64 end_time = 4;                 // 结束时间
  uint32 curr_stage = 5;              // 当前阶段
  uint32 plan_id = 6;                 // 计划ID
}

// 批量获取房间婚礼信息请求
message BatchGetChannelWeddingSimpleInfoReq {
  uint32 op_uid = 1;
  repeated uint32 cid_list = 2;  // 房间ID列表
}

// 批量获取房间婚礼信息响应
message BatchGetChannelWeddingSimpleInfoResp {
  repeated SimpleWeddingInfo wedding_info_list = 1;  // 婚礼信息列表
}

enum WeddingThemeType {
  WEDDING_THEME_TYPE_UNSPECIFIED = 0;
  WEDDING_THEME_TYPE_FREE = 1;  // 免费
  WEDDING_THEME_TYPE_PAY = 2;   // 付费
}

// 预约婚礼请求
message ReserveWeddingReq {
  uint32 uid = 1;  // 预约人/购买人uid
  uint32 cid = 2;  // 房间ID
  int64 start_time = 3;  // 开始时间
  string theme_id = 4 [deprecated=true];   // 废弃，使用 wedding_theme_id
  uint32 bride_uid = 5;  // 新娘uid
  uint32 groom_uid = 6;  // 新郎uid
  uint32 wedding_theme_id = 7;   // 主题ID
  int64 end_time = 8;  // 结束时间

  uint32 plan_id = 9; // 前置流程的计划ID
  uint32 theme_type = 10; // see WeddingThemeType
  repeated uint32 bridesmaid_uid_list = 11;  // 伴郎伴娘uid列表

  uint32 suit_extra_send_sec = 12; // 新人婚服额外发放时长(单位秒)
  uint32 init_happiness_value = 13; // 初始幸福值
}

// 预约婚礼响应
message ReserveWeddingResp {
  int64 wedding_id = 1;  // 婚礼ID
}

// 婚礼进行中上报新增伴郎伴娘请求
message ReportWeddingBridesmaidManReq {
  uint32 plan_id = 1;
  uint32 cid = 2;  // 房间ID
  repeated uint32 bridesmaid_uid_list = 3;  // 伴郎伴娘uid列表
  uint32 buyer_uid = 4;  // 购买人uid
}

// 婚礼进行中新增伴郎伴娘响应
message ReportWeddingBridesmaidManResp {}

// 切换婚礼阶段请求
message SwitchWeddingStageReq {
  uint32 uid = 1;
  uint32 cid = 2;  // 房间ID
  uint32 stage = 3;  // 阶段, see WeddingStage
  uint32 sub_stage = 4;  // 子阶段
}

// 切换婚礼阶段响应
message SwitchWeddingStageResp {}

// 拍合照
message TakeWeddingGroupPhotoReq {
  uint32 uid = 1;
  uint32 cid = 2;  // 房间ID
  string photo_rul = 3; // 合照url
}

message TakeWeddingGroupPhotoResp {}

// 获取用户可切换的姿势列表
message GetUserWeddingPoseListReq {
  uint32 uid = 1;
  uint32 cid = 2;  // 房间ID
  uint32 sex = 3;  // 性别
}

message GetUserWeddingPoseListResp {
  repeated uint32 pose_list = 1;  // 姿势列表
  uint32 curr_pose = 2;  // 当前姿势
  uint32 orientation = 3;  // 朝向 see virtual_image_logic_.proto VirtualImageOrientation
}

// 切换姿势
message SetUserWeddingPoseReq {
  uint32 uid = 1;
  uint32 cid = 2;  // 房间ID
  uint32 pose = 3;  // 姿势
}

message SetUserWeddingPoseResp {}

// 切换朝向
message SetUserWeddingOrientationReq {
  uint32 uid = 1;
  uint32 cid = 2;  // 房间ID
  uint32 orientation = 3;  // 朝向 see virtual_image_logic_.proto VirtualImageOrientation
}

message SetUserWeddingOrientationResp {}

message UserWeddingPose {
  uint32 uid = 1;
  uint32 pose = 2;  // 姿势
  uint32 orientation = 3;  // 朝向 see virtual_image_logic_.proto VirtualImageOrientation
  uint32 pose_bone_id = 4; // 姿势骨骼资源ID
  uint32 base_pose_bone_id = 5; // 基础姿势骨骼资源ID
}

// 批量获取用户姿势
message BatchGetUserWeddingPoseReq {
  uint32 uid = 1;
  uint32 cid = 2;  // 房间ID
  repeated uint32 uid_list = 3;  // 用户ID列表
}

message BatchGetUserWeddingPoseResp {
  repeated UserWeddingPose user_pose_list = 1;  // 用户姿势列表
}


// 获取房间合照麦位位置映射请求
message GetWeddingGroupPhotoSeatListReq {
  uint32 uid = 1;
  uint32 cid = 2;  // 房间ID
}

// 合照麦位位置映射
message WeddingGroupPhotoSeat {
  uint32 mic_id = 1;      // 麦位ID
}

// 获取房间合照麦位位置映射响应
message GetWeddingGroupPhotoSeatListResp {
  repeated WeddingGroupPhotoSeat seat_list = 1;  // 全量麦位位置序列
  repeated uint32 pose_confirmed_uid_list = 2;   // 已确认站姿的用户ID列表
}

// 设置用户合照位置请求
message SetUserWeddingGroupPhotoSeatReq {
  uint32 uid = 1;
  uint32 cid = 2;  // 房间ID
  repeated WeddingGroupPhotoSeat seat_list = 3;  // 全量麦位位置序列
}

// 设置用户合照位置响应
message SetUserWeddingGroupPhotoSeatResp {}

// 增加婚礼伴郎伴娘请求
message AddWeddingBridesmaidManReq {
  uint32 wedding_id = 1;
  uint32 cid = 2;  // 房间ID
  repeated uint32 bridesmaid_uid_list = 3;  // 伴郎伴娘uid列表
}

// 增加婚礼伴郎伴娘响应
message AddWeddingBridesmaidManResp {}

message WeddingSchedule {
  uint32 plan_id = 1;  // 计划ID
  uint32 wedding_id = 2; // 婚礼场次ID
  uint32 bride_uid = 3;  // 新娘uid
  uint32 groom_uid = 4;  // 新郎uid
  int64 start_time = 5;  // 开始时间
  int64 end_time = 6;    // 结束时间
}

message GetWeddingScheduleListReq {
  int64 min_begin_time = 1;
  int64 max_begin_time = 2;
  uint32 uid = 3;   // 可选，新郎或者新娘uid
}

message GetWeddingScheduleListResp {
  repeated WeddingSchedule schedule_list = 1;  // 婚礼场次列表
}

// 结婚证
message WeddingCertificate {
  uint32 wedding_id = 1; // 婚礼ID
  uint32 groom_uid = 2; // 新郎信息
  uint32 bride_uid = 3; // 新娘信息
  int64 wedding_time = 4;// 结婚时间
  uint32 wedding_theme_id = 5; // 婚礼主题id
  string pic_url = 6; // 结婚证图片
  uint32 wedding_lv = 7; // 婚礼等级
}

message GetUserWeddingCertificateReq {
  uint32 uid_a = 1;
  uint32 uid_b = 2;
  int64 begin_time = 3;
}

message GetUserWeddingCertificateResp {
  WeddingCertificate wedding_certificate = 1; // 结婚证
}

message WeddingScenePic {
  uint32 scene = 1; // 场景, see WeddingScene
  string pic_url = 2; // 场景图片
  int64 create_time = 3; // 创建时间
  string scene_icon = 4;  // 场景icon
}

// 婚礼片段信息
message WeddingClipInfo {
  uint32 theme_id = 1;    // 婚礼主题id
  string theme_name = 2;  // 婚礼主题名称
  uint32 wedding_id = 3;  // 婚礼ID
  repeated WeddingScenePic scene_pic_list = 4; // 场景图片列表
  uint32 cid = 5; // 房间id
}

// 获取用户婚礼沉淀信息请求
message GetUserWeddingWeddingClipsReq {
  uint32 uid_a = 1;
  uint32 uid_b = 2;
}

// 获取用户婚礼沉淀信息响应
message GetUserWeddingWeddingClipsResp {
  repeated WeddingClipInfo wedding_clip_list = 1; // 婚礼片段列表
  repeated WeddingClipInfo group_photo_list = 2;  // 合影列表
}

// 上报婚礼场景片段图片
message ReportWeddingScenePicReq {
  uint32 op_uid = 1;
  uint32 cid = 2;
  uint32 wedding_id = 3;  // 婚礼id
  uint32 scene = 4;       // 场景, see WeddingScene
  string pic_url = 5;     // 图片url
}

message ReportWeddingScenePicResp {}


message PageGetGoingWeddingListReq {
  uint32 page_num = 1;
  uint32 page_size = 2;
}

message PageGetGoingWeddingListResp {
  repeated GoingWeddingInfo wedding_list = 1;
  bool has_more = 2;
}

message GoingWeddingInfo {
  uint32 wedding_id = 1;
  uint32 wedding_plan_id = 2;
  uint32 channel_id = 3;
  uint32 theme_id = 4;
  uint32 theme_type = 5;
  uint32 curr_level = 6;
  uint32 curr_stage = 7;
}

message GetWeddingRecordByTimeRangeReq {
  int64 begin_time = 1;
  int64 end_time = 2;
}

message GetWeddingRecordByTimeRangeResp {
  repeated WeddingCertificate certificate_list = 1;
}

message TestWeddingScenePicImReq {
  uint32 scene = 1;       // 场景, see WeddingScene
  repeated string pic_url_list = 2;     // 图片url
  uint32 from_uid = 3;
  uint32 to_uid = 4;
  uint32 wedding_theme_id = 5;
}

message TestWeddingScenePicImResp {

}


message GetWeddingHighLightPresentRequest {
   uint32 cid = 1;  // 房间ID
  uint32 wedding_id = 2;  // 婚礼id
}

message GetWeddingHighLightPresentResponse {
   string toast = 1;    //领取结果提示
}

message BatchGetWeddingHappinessRequest {
  repeated uint32 plan_id_list = 1;
}

message BatchGetWeddingHappinessResponse {
  map<uint32, uint32> happiness = 1;
}

message GetChannelWeddingRankInfoReq{
  uint32 channel_id = 1; // 房间ID
}

message GetChannelWeddingRankInfoResp{
  repeated WeddingRankInfo rank_list = 1; // 爱侣榜信息
  string rank_desc = 2; // 榜单排名说明
}

message WeddingRankList{
  repeated WeddingRankInfo rank_list = 1; // 爱侣榜信息列表
  uint32 near_n_day = 2; // 最近n天
  uint32 limit = 3; // 榜单人数
  uint32 min_score = 4; // 榜单最小分数
}

message WeddingRankInfo{
  uint32 bride_uid = 1; // 新娘uid
  uint32 groom_uid = 2; // 新郎uid
  uint32 happiness_val = 3; // 幸福值
  uint32 theme_id = 4; // 婚礼主题id
  string theme_name = 5; // 婚礼主题名称
  string wedding_bg_url = 6; // 婚礼背景图片

  string bride_account = 7; // 新娘账号
  string groom_account = 8; // 新郎账号

  uint32 wedding_lv = 9; // 婚礼等级
}

message WeddingRankFirstPlaceInfo{
  WeddingRankInfo info = 1; // 榜首信息
  uint32 near_n_day = 2; // 最近n天
  uint32 min_score = 3; // 榜单最小分数
}

message GetChannelWeddingRankEntryInfoReq{
  uint32 channel_id = 1; // 房间ID
}
message GetChannelWeddingRankEntryInfoResp{
  bool is_show = 1; // 是否展示爱侣榜入口
  // 榜单榜首账号信息
  WeddingRankInfo rank_info = 2; // 榜首信息
  string h5_url = 3;
}

message TestAfterFreeWeddingImXmlReq{
  uint32 bride_uid = 1;
  uint32 groom_uid = 2;
}
message TestAfterFreeWeddingImXmlResp{
  string xml = 1; // im xml
}

message SendWeddingReservePresentReq {
  uint32 cid = 1;
  uint32 wedding_id = 2;  // 婚礼id
  uint32 uid = 3;         // 预约人uid
}

message SendWeddingReservePresentResp {
}

// 婚礼收送礼值
message WeddingPresentVal {
    uint32 uid = 1; // 用户ID
    uint32 present_val = 2; // 收礼值
}

message WeddingPresentLevel {
    uint32 uid = 1; // 用户ID
    uint32 lv= 2;   // 等级
}

// 婚礼收送礼物值信息(计数器)
message WeddingPresentCountInfo {
    repeated WeddingPresentVal user_present_val_list = 1; // 用户收礼值列表
    uint32 mvp_uid = 2; // mvp用户
    WeddingPresentLevel top_recv_present_lv = 3; // 收礼值第一名等级
}