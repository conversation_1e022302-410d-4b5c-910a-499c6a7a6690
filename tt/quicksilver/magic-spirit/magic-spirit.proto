syntax = "proto3";

package magic_spirit;
option go_package = "golang.52tt.com/protocol/services/magic-spirit";
import "tt/quicksilver/present-middleware/present-middleware.proto";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

service MagicSpiritBackend {
  // 添加幸运礼物
  rpc AddMagicSpirit(AddMagicSpiritReq) returns(AddMagicSpiritResp) {}

  // 删除幸运礼物
  rpc DelMagicSpirit(DelMagicSpiritReq) returns(DelMagicSpiritResp) {}

  // 查询幸运礼物
  rpc GetMagicSpirit(GetMagicSpiritReq) returns(GetMagicSpiritResp) {}

  // 修改幸运礼物
  rpc UpdateMagicSpirit(UpdateMagicSpiritReq) returns (UpdateMagicSpiritResp) {}

  // 添加奖池礼物
  rpc AddMagicSpiritPond(AddMagicSpiritPondReq) returns(AddMagicSpiritPondResp) {}

  // 获取礼物池列表
  rpc GetMagicSpiritPond(GetMagicSpiritPondReq) returns(GetMagicSpiritPondResp) {}

  // 添加黑名单
  rpc AddMagicSpiritBlacklist(AddMagicSpiritBlacklistReq) returns(AddMagicSpiritBlacklistResp) {}

  // 获取黑名单
  rpc GetMagicSpiritBlacklist(GetMagicSpiritBlackListReq) returns(GetMagicSpiritBlackListResp) {}

  // 删除黑名单
  rpc DelMagicSpiritBlacklist(DelMagicSpiritBlacklistReq) returns(DelMagicSpiritBlacklistResp) {}

  // 设置通用配置, 单账号送礼数限制, 每日金额限制, 单笔礼物数目限制
  rpc SetCommonConf(SetCommonConfReq) returns(SetCommonConfResp) {}

  // 获取通用配置
  rpc GetCommonConf(GetCommonConfReq) returns(GetCommonConfResp) {}

  // 赠送送魔法精灵
  rpc SendMagicSpirit(SendMagicSpiritReq) returns (SendMagicSpiritResp) {}

  // 开箱礼物
  rpc SendUnpackGift(SendUnpackGiftReq) returns (SendUnpackGiftResp) {}

  // 获取房间带开箱礼物信息列表
  rpc GetChannelAllUnpackGift(GetChannelAllUnpackGiftReq) returns (GetChannelAllUnpackGiftResp) {}

  // 客户端幸运礼物列表
  rpc GetMagicSpiritForCli(GetMagicSpiritForCliReq) returns(GetMagicSpiritForCliResp) {}

  // 是否可玩幸运礼物
  rpc GetMagicSpiritUsable(GetMagicSpiritUsableReq) returns(GetMagicSpiritUsableResp) {}

  // 获取订单汇总信息(对账用)
  rpc GetMagicSpiritOrderTotal(GetMagicSpiritOrderTotalReq) returns (GetMagicSpiritOrderTotalResp) {}
  // 获取奖励汇总信息(对账用)
  rpc GetMagicSpiritAwardTotal(GetMagicSpiritAwardTotalReq) returns (GetMagicSpiritAwardTotalResp) {}

  // 获取未开始的幸运礼物/奖池配置
  rpc GetMagicSpiritConfTmp(GetMagicSpiritConfTmpReq) returns(GetMagicSpiritConfTmpResp){}

  // 检查是否可以发特殊来源的幸运礼物
  rpc CheckIfSendMagicWithSource(CheckIfSendMagicWithSourceReq) returns(CheckIfSendMagicWithSourceResp){}
  // 发特殊来源的幸运礼物
  rpc SendMagicWithSource(SendMagicWithSourceReq) returns(SendMagicWithSourceResp){}

  // 豁免条件值
  rpc GetMagicSpiritExemptValue(GetMagicSpiritExemptValueReq) returns(GetMagicSpiritExemptValueResp){}

  /*********对账接口V2**********/
  // 发放包裹数据对账
  rpc GetAwardTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetAwardOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  // T豆消费数据对账
  rpc GetConsumeTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetConsumeOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  // 补单
  rpc ReissueMagicOrder(ReconcileV2.ReplaceOrderReq) returns (ReconcileV2.EmptyResp) {}

  rpc GenFinancialFile(ReconcileV2.GenFinancialFileReq) returns (ReconcileV2.GenFinancialFileResp) {}
}

message TimeRange {
    int64 start_time = 1; // 开始时间（秒级时间戳）
    int64 end_time = 2;   // 结束时间（秒级时间戳）
}

message MagicSpirit {
  uint32 magic_spirit_id    = 1;
  string name               = 2;  // 名称
  string icon_url           = 3;  // 图标url
  uint32 price              = 4;  // 价格
  uint32 rank               = 5;  // 排名
  uint32 effect_begin       = 6;  // 上架时间
  uint32 effect_end         = 7;  // 下架时间
  string describe_image_url = 8;  // 礼物介绍浮层
  string describe           = 9;  // 礼物介绍
  uint32 junior_lighting    = 10; // 礼物触发初级光效个数
  uint32 middle_lighting    = 11; // 礼物触发中级光效个数
  string vfx_resource       = 12; // 特效资源
  string vfx_resource_md5   = 13; // 特效资源md5
  uint32 update_time        = 14; // 更新时间
  float  rank_float         = 15; // float的排序，新版用，与rank不一致

  // 6.36.0 增加活动链接字段
  string desc_activity_url = 16;  // 礼物介绍活动链接， 6.56.3 废弃
  repeated uint32 channel_type_list = 17; // 适用的房间类型  see channel_.proto ChannelType

  // 6.56.3 新增
  bool show_effect_end = 18;    // 是否展示下架时间
  MagicSpiritActivityCfg activity_cfg = 19;  // 活动配置

  // 6.66.5 支持多时段上线礼物/奖池
  repeated TimeRange magic_effect_time_list = 20; // 礼物上线时间段
  SpecialPondConf special_pond_conf = 21; // 特殊奖池配置
}

// 特殊奖池配置
message SpecialPondConf {
  string describe_image_url = 1; // 特殊奖池浮层图片
  string describe_jump_url  = 2; // 特殊奖池介绍
  
  repeated TimeRange effect_time_list = 3; // 特殊奖池上线时间段
}

message MagicSpiritActivityCfg {
  string activity_name = 1;  // 活动名称
  int64 begin_time = 2;  // 活动开始时间
  int64 end_time = 3;    // 活动结束时间
  string image_url = 4;  // 活动浮层

  string jump_url_tt = 5;  // tt活动跳转链接
  string jump_url_hc_android = 6;  // 欢游安卓跳转链接
  string jump_url_hc_ios = 7;  // 欢游ios跳转链接
  string jump_url_mike_android = 8;  // 麦可安卓跳转链接
  string jump_url_mike_ios = 9;  // 麦可ios跳转链接
}

// 更新幸运礼物配置
message MagicSpiritTmp{
    uint32 id = 1;
    uint32 effect_time = 2;
    MagicSpirit conf = 3;
}

message AddMagicSpiritReq {
  repeated MagicSpirit magic_spirit = 1;
}

message AddMagicSpiritResp {
  repeated uint32 magic_spirit_id = 1;
}

message DelMagicSpiritReq {
  repeated uint32 magic_spirit_ids = 1;
}

message DelMagicSpiritResp {
}

message GetMagicSpiritReq {
}

message GetMagicSpiritResp {
  repeated MagicSpirit magic_spirit = 1;
}

message UpdateMagicSpiritReq {
  repeated MagicSpiritTmp magic_spirit = 1;
}

message UpdateMagicSpiritResp {
}

enum PrizeLeverConst {
  NONE_PRIZE        = 0; // 无特效
  SMALL_PRIZE       = 1; // 小奖特效
  NORMAL_PRIZE      = 2; // 中奖特效
  BIG_PRIZE         = 3; // 大奖特效
  FULL_SERVER_PRIZE = 4; // 全服特效
}

message MagicSpiritPondItem {
  uint32 item_id         = 1;
  uint32 magic_spirit_id = 2; // 魔法精灵ID
  uint32 present_id      = 3; // 礼物ID
  uint32 weight          = 4; // 权重
  uint32 prize_level     = 5; // 中奖等级
  uint32 update_time     = 8; // 更新时间, anno: 新增/更新时无需填写
  uint32 effect_time     = 9; // 生效时间, anno: 新增/更新时无需填写
  uint32 breaking_new_id = 10; // 全服公告id
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message AddMagicSpiritPondReq {
  repeated MagicSpiritPondItem MagicSpiritPondItems = 1;
  enum PondType {
    NONE_POND = 0; // 普通奖池
    SPECIAL_POND = 1; // 特殊奖池
  }
  uint32 pond_type = 2; // 奖池类型
  int64 effect_time = 3; // 生效时间，设置普通奖池时需要填写；设置特殊奖池时不需要填写
}

message AddMagicSpiritPondResp {
  repeated uint32 magic_spirit_ids = 1[deprecated=true];
}

// deprecated
message UpdateMagicSpiritPondReq {
  uint32 item_id     = 1;
  uint32 present_id  = 2; // 礼物ID
  uint32 weight      = 3; // 权重
  uint32 prize_level = 5; // 中奖等级
}
// deprecated
message UpdateMagicSpiritPondResp {
}

message GetMagicSpiritPondReq {
  uint32 magic_spirit_id = 1;
}

message GetMagicSpiritPondResp {
  repeated MagicSpiritPondItem magic_spirit_pond = 1;
  repeated MagicSpiritPondItem special_magic_spirit_pond = 2; // 特殊奖池
}

message DelMagicSpiritPondReq {
  uint32          magic_spirit_id = 1;
  repeated uint32 item_ids        = 2;
}

message DelMagicSpiritPondResp {
}

// ============= 新增未开始tab ====================
message GetMagicSpiritConfTmpReq{
}

enum PendingConfType {
    PENDING_CONF_INVALID = 0; // 无效字段
    PENDING_CONF_MAGIC_SPIRIT = 1; // 魔法精灵
    PENDING_CONF_MAGIC_SPIRIT_POND = 2; // 魔法精灵普通奖池
}

message MagicSpiritConfTmp{
    MagicSpiritTmp magic_spirit_tmp = 1;
    bool has_pool = 2;
    repeated MagicSpiritPondItem pool = 3;
    repeated MagicSpiritPondItem special_pool = 4; // 特殊奖池
    repeated uint32 pending_conf_type_list  = 5; // 待生效配置列表。see PenddingConfType
}

message GetMagicSpiritConfTmpResp{
    repeated MagicSpiritConfTmp conf_list = 1;
}
// ==================================================

message MagicSpiritBlacklist {
  uint32 blacklist_id = 1;
  uint32 channel_id   = 2;
  uint32 room_id      = 3; // 房间ID
  string room_name    = 4; // 房间名
  uint32 ttid         = 5; // 房主ID
  string owner        = 6; // 房主名
  uint32 create_time  = 7; // 创建时间
}

message AddMagicSpiritBlacklistReq {
  repeated MagicSpiritBlacklist blacklist = 1;
}

message AddMagicSpiritBlacklistResp {
  repeated uint32 blacklist_id = 1;
}

message GetMagicSpiritBlackListReq {
  uint32 page_num   = 1;
  uint32 page_size  = 2;
  uint32 channel_id = 3;
}

message GetMagicSpiritBlackListResp {
  uint32                        total     = 1;
  repeated MagicSpiritBlacklist blacklist = 2;
}

message DelMagicSpiritBlacklistReq {
  repeated uint32 channel_ids = 1;
}

message DelMagicSpiritBlacklistResp {
}

enum CommonConfType {
  NONE                         = 0;
  DAILY_SEND_MONEY_LIMIT       = 1; // 每日送礼金额限制
  DAILY_PREVENT_EXCHANGE_LIMIT = 2; // 每日同账号送礼金额限制
  PER_ORDER_COUNT_LIMIT        = 3; // 单笔订单数量限制
}

message CommonConf {
  uint32 conf_type = 1;
  uint32 value     = 2;
}

message GetCommonConfReq {
}

message GetCommonConfResp {
  repeated CommonConf common_conf = 1;
}

message SetCommonConfReq {
  repeated CommonConf common_conf = 1;
}

message SetCommonConfResp {
}

// 连击信息
message CombInfo {
  uint32 duration   = 1;      // 倒计时光圈持续时长
  uint32 comb_level = 2;      // 根据次数触发的连击等级
  string level_name = 3;      // 等级名称
  bool   level_up   = 4;                // 是否升级
}

// 魔法精灵中奖类型
enum MagicSpiritEffectType
{
  MAGIC_SPIRIT_EFFECT_NONE = 0;
  MAGIC_SPIRIT_EFFECT_LV1  = 1;  // 小奖前置特效
  MAGIC_SPIRIT_EFFECT_LV2  = 2;  // 中奖前置特效
  MAGIC_SPIRIT_EFFECT_LV3  = 3;  // 500元以下的大奖前置特效
  MAGIC_SPIRIT_EFFECT_LV4  = 4;  // 超级大奖带全服公告
  MAGIC_SPIRIT_EFFECT_LV3_MORE_500 = 5; // 500元以上的大奖前置特效
}

// 实际送礼信息
message PresentSendInfo {
  string present_order_id = 1;
  uint32 target_uid       = 2;
  uint32 gift_id          = 3;
  uint32 cnt              = 4;
  uint32 award_effect     = 5;      // 中奖特效 see MagicSpiritEffectType

  uint32 total_price      = 6;
  string deal_token       = 7;
}

message SendMagicSpiritReq {
  uint32          uid             = 1;      // 送礼方
  uint32          magic_spirit_id = 2;      // 魔法精灵ID
  repeated uint32 target_uid_list = 3;      // 收礼uid列表
  uint32          average_cnt     = 4;      // 每个收礼方的礼物数量
  uint32          channel_id      = 5;      // 房间ID
  uint32          comb_cnt        = 6;      // 当前连击次数
  bool separate_unpack_gift = 7;  // 是否分离出开箱礼物模式（5.9.0版本使用）
}

message SendMagicSpiritResp {
  repeated PresentSendInfo send_info_list = 1;
  CombInfo                 comb_info      = 2;
  uint32                   send_time      = 3;    // 送礼时间
  string                   magic_order_id = 4;    // 消费订单号
  MagicSpirit              magic_conf     = 5;
}

message CheckIfSendMagicWithSourceReq {
  uint32 uid = 1;
  uint32 channel_id = 2;
  uint32 magic_spirit_id = 3;
  uint32 amount = 4;
  uint32 source = 5; // 来源 see SendMagicSource
}

message CheckIfSendMagicWithSourceResp {
  //bool can_send = 1;
}

enum SendMagicSource {
  Common = 0;
  ChannelLottery = 1; // 直播抽奖
  GrabChairGame = 2; // 抢椅子游戏
}

message SendMagicWithSourceReq {
  uint32 uid = 1;                     // 送礼方
  uint32 magic_spirit_id = 2;         // 魔法精灵ID
  repeated uint32 target_uid_list = 3;// 收礼uid列表
  uint32 average_cnt = 4;             // 每个收礼方的礼物数量
  uint32 channel_id = 5;              // 房间ID
  string pay_order_id = 6;            // 支付订单号
  string deal_token = 7;              // 上下文链路token
  uint32 source = 8;                  // 来源 see SendMagicSource
  int64 outside_time = 9;
}

message SendMagicWithSourceResp {}

// 开箱
message SendUnpackGiftReq {
  uint32 uid = 1;
  uint32 channel_id = 2;
  string item_order_id = 3; // 订单号
}

message SendUnpackGiftResp {
  present_middleware.SendMagicSpiritOpt send_opt = 1; // see present-middleware.proto
}

message MagicSpiritForCli {
  uint32          magic_spirit_id    = 1;
  string          name               = 2;  // 名称
  string          icon_url           = 3;  // 图标url
  uint32          price              = 4;  // 价格
  uint32          rank               = 5;  // 排名
  uint32          effect_begin       = 6;  // 上架时间
  uint32          effect_end         = 7;  // 下架时间
  string          describe_image_url = 8;  // 礼物介绍浮层
  string          describe_jump_url  = 9;  // 礼物介绍
  string          vfx_resource       = 10; // 特效资源
  string          vfx_resource_md5   = 11; // 特效资源md5
  repeated uint32 present_ids        = 12; // 礼物池中礼物id
  float           rank_float         = 13; // float的排序，新版用，与rank不一致
  
  // 6.36.0 增加活动链接字段
  string desc_activity_url = 14;  // 礼物介绍活动链接

  // 6.56.3 新增
  bool show_effect_end = 15;    // 是否展示下架时间
  MagicSpiritActivityCfg activity_cfg = 16;  // 活动配置
}

message GetMagicSpiritForCliReq {
  uint32 current_version = 1; // 当前版本
}

message GetMagicSpiritForCliResp {
  repeated MagicSpiritForCli magic_spirits   = 1;
  uint32                     current_version = 2;
}

message GetMagicSpiritUsableReq {
  uint32 channel_id = 1;
  uint32 uid        = 2;
  bool old_version = 3; // version before 6.36.0
}

message GetMagicSpiritUsableResp{
  uint32 channel_id   = 1;
//  uint32 exp_level    = 2; // 使用这个礼物的最低经验等级
//  uint32 wealth       = 3; // 使用这个礼物的最低财富值
//  uint32 charm        = 4; // 使用这个礼物的最低魅力值
//  uint32 pupil_usable = 5; // 未成年人是否可以使用这个礼物
  uint32 usable       = 6; // see MagicSpiritUsable  房间等其他服务器判断条件
  uint32 check_interval_sec = 7;  // 轮询请求间隔时长

  repeated uint32 magic_spirit_ids = 8;  //可见幸运礼物id列表
}

message GetMagicSpiritOrderTotalReq {
  uint32 begin_time = 1;
  uint32 end_time   = 2;
}

message GetMagicSpiritOrderTotalResp {
  uint32 order_cnt   = 1; // 订单数
  uint64 total_price = 2; // 总价值
  uint64 total_num = 3;   // 总个数
}

message GetMagicSpiritAwardTotalReq {
  uint32 begin_time = 1;
  uint32 end_time   = 2;
}

message GetMagicSpiritAwardTotalResp {
  uint32 order_cnt   = 1; // 订单数
  uint64 total_price = 2; // 总价值
  uint64 total_num = 3;   // 总个数
}

// 待开箱礼物信息
message UnpackGiftInfo {
  string item_order_id = 1;   // 唯一订单号
  uint32 magic_spirit_id = 2; // 魔法精灵id
  uint32 item_id = 3;         // 礼物id
  uint32 channel_id = 4;
  uint32 send_uid = 5;
  uint32 target_uid = 6;
  uint32 end_ts = 7;      // 到期时间戳（秒）
}

message UnpackGiftSimple {
  string item_order_id = 1;
  uint32 end_ts = 2;
}

// 用户待开箱列表推送 opt
message UserUnpackGiftList {
  uint32 channel_id = 1;
  uint32 uid = 2;
  repeated UnpackGiftSimple list = 3;
}

// 获取房间当前待开箱礼物列表
message GetChannelAllUnpackGiftReq {
  uint32 uid = 1;
  uint32 channel_id = 2;
}

message GetChannelAllUnpackGiftResp {
  repeated UnpackGiftInfo list = 1;
  UserUnpackGiftList user_unpack_opt = 2;  // 用户个人待开箱列表
  uint32 server_ts = 3;
}

message GetMagicSpiritExemptValueReq{
    uint32 uid =1;
    repeated uint32 magic_spirit_id = 2;
}

message MagicSpiritExemptValue{
    uint32 magic_spirit_id = 1;
    bool send_flag =2;
}

message GetMagicSpiritExemptValueResp{
    repeated MagicSpiritExemptValue value_list = 1;
}