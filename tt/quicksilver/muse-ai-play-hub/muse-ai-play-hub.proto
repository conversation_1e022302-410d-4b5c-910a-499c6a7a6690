syntax = "proto3";

package muse_ai_play_hub;

option go_package = "golang.52tt.com/protocol/services/muse-ai-play-hub";

import "tt/quicksilver/muse-ai-play-hub/muse-role-play.proto";
import "tt/quicksilver/extension/options/options.proto";
import "tt/quicksilver/muse-ai-play-hub/muse-ai-cupid.proto";

service MuseAiPlayHub {
  option (service.options.service_ext) = {
    service_name: "muse-ai-play-hub"
  };

  /*------------AI红娘------------*/
  rpc HandleEvent(HandleEventRequest) returns (HandleEventResponse) {}
  rpc IsHitChattingSource(IsHitChattingSourceRequest) returns (IsHitChattingSourceResponse) {}   //是否命中聊天来源
  rpc GetLastedHistoryMessage(GetLastedHistoryMessageRequest) returns (GetLastedHistoryMessageResponse) {}  //获取最近的历史下线
  rpc IncAndGetInspirationUsedTimes(IncAndGetInspirationUsedTimesRequest) returns (IncAndGetInspirationUsedTimesResponse) {}  //自增并获取已使用次数
  rpc GetAiCupidSwitchStatus(GetAiCupidSwitchStatusRequest) returns (GetAiCupidSwitchStatusResponse) {}//获取AI红娘功能状态
  rpc UpdateAIPlayHubStatus(UpdateAIPlayHubStatusRequest) returns (UpdateAIPlayHubStatusResponse) {}//更新AI红娘功能状态
  rpc GetAiInspirationRequestInterval(GetAiInspirationRequestIntervalRequest) returns (GetAiInspirationRequestIntervalResponse) {}//获取灵感回复时间间隔
  rpc SetAiInspirationRequestInterval(SetAiInspirationRequestIntervalRequest) returns (SetAiInspirationRequestIntervalResponse) {}//获取灵感回复时间间隔
  rpc GetInspirationUsedTimes(GetInspirationUsedTimesRequest)returns(GetInspirationUsedTimesResponse){} //获取灵感回复已使用次数
  rpc AIPlayHubTest(AIPlayHubTestRequest) returns (AIPlayHubTestResponse) {} // 测试接口
  rpc BatchFirstChatList(BatchFirstChatListRequest) returns (BatchFirstChatListResponse) {}
  rpc BatchGuideFollowChatList(BatchGuideFollowChatListRequest) returns (BatchGuideFollowChatListResponse) {}
  rpc GetChatHistoryMessageCount(GetChatHistoryMessageCountRequest)returns(GetChatHistoryMessageCountResponse){}
  rpc GetInspirationChatSource(GetInspirationChatSourceRequest) returns (GetInspirationChatSourceResponse){}
  rpc RemFirstChatList(RemFirstChatListRequest) returns (RemFirstChatListResponse) {}
  /*------------AI红娘------------*/

  /*------------角色扮演------------*/
  /*运营后台*/
  //编辑更新CP关系
  rpc UpsertRolePlayCp(UpsertRolePlayCpRequest) returns (UpsertRolePlayCpResponse) {}
  //获取cp关系列表
  rpc GetRolePlayCpList(GetRolePlayCpListRequest) returns (GetRolePlayCpListResponse) {}
  //移动cp位置
  rpc MoveRolePlayCp(MoveRolePlayCpRequest) returns (MoveRolePlayCpResponse) {}

  rpc GetRolePlayPlotLibrary(GetRolePlayPlotLibraryRequest)returns(GetRolePlayPlotLibraryResponse){}

  rpc DelRolePlayPlotLibrary(DelRolePlayPlotLibraryRequest)returns(DelRolePlayPlotLibraryResponse){}

  rpc UpsertRolePlayPlotLibrary(UpsertRolePlayPlotLibraryRequest)returns(UpsertRolePlayPlotLibraryResponse){}


  /*运营后台*/

  //获取可用的cp关系列表
  rpc GetAvailableRolePlayCpList(GetAvailableRolePlayCpListRequest) returns (GetAvailableRolePlayCpListResponse) {}
  // 创建我的角色卡
  rpc CreateMyRoleCard(CreateMyRoleCardRequest) returns (CreateMyRoleCardResponse) {}
  // 获取我的角色卡
  rpc GetMyRoleCard(GetMyRoleCardRequest) returns (GetMyRoleCardResponse) {}
  // 删除我的角色卡
  rpc DeleteMyRoleCard(DeleteMyRoleCardRequest) returns (DeleteMyRoleCardResponse) {}
  // 更新我的角色卡
  rpc UpdateMyRoleCard(UpdateMyRoleCardRequest) returns (UpdateMyRoleCardResponse) {}
  // 生成角色卡片介绍
  rpc GenRoleCardIntroduce(GenRoleCardIntroduceRequest) returns (GenRoleCardIntroduceResponse) {}
  // 生成角色卡片图片
  rpc GenRoleCardPic(GenRoleCardPicRequest) returns (GenRoleCardPicResponse) {}
  // 获取我的简介/形象生成结果
  rpc GetMyRoleCardGenResult(GetMyRoleCardGenResultRequest) returns (GetMyRoleCardGenResultResponse) {}
  // 获取创建角色卡信息
  rpc GetCreateRoleCardInfo(GetCreateRoleCardInfoRequest) returns (GetCreateRoleCardInfoResponse) {}
  rpc BatchGetRoleCard(BatchGetRoleCardRequest) returns (BatchGetRoleCardResponse) {}
  //用户卡片下一个/组CP请求
  rpc RolePlayUserLike(RolePlayUserLikeRequest)returns(RolePlayUserLikeResponse){}

  rpc GetRolePlayReceiveCpList(GetRolePlayReceiveCpListRequest)returns(GetRolePlayReceiveCpListResponse){}

  rpc GetRolePlayNewReceiveCPMessage(GetRolePlayNewReceiveCPMessageRequest)returns(GetRolePlayNewReceiveCPMessageResponse){}

  rpc GetRolePlayUserCpRelation(GetRolePlayUserCpRelationRequest)returns(GetRolePlayUserCpRelationResponse){}

  rpc GetRolePlayBindCpRelationBackgroundPic(GetRolePlayBindCpRelationBackgroundPicRequest)returns(GetRolePlayBindCpRelationBackgroundPicResponse){}

  rpc GetRolePlaySendCpList(GetRolePlaySendCpListRequest)returns(GetRolePlaySendCpListResponse){}
  rpc GetUserSayHelloRemainTimes( GetUserSayHelloRemainTimesRequest)returns(GetUserSayHelloRemainTimesResponse){}


  rpc CreateTempRoleCard(CreateTempRoleCardRequest) returns (CreateTempRoleCardResponse) {} //临时卡片打招呼

  rpc HiddenRolePlaySayHelloRecord(HiddenRolePlaySayHelloRecordRequest)returns(HiddenRolePlaySayHelloRecordResponse){}

  rpc ReportRolePlayUserClickedCard(ReportRolePlayUserClickedCardRequest)returns(ReportRolePlayUserClickedCardResponse){}

  rpc RebuildRolePlayUserUnClickedStatus(RebuildRolePlayUserUnClickedStatusRequest)returns(RebuildRolePlayUserUnClickedStatusResponse){}

  /*------------角色扮演------------*/

  rpc DelCPRelationChatting(DelCPRelationChattingRequest) returns ( DelCPRelationChattingResponse){}

}


// 处理IM等各种事件
message HandleEventRequest{
  bytes event_value = 1;
  uint32 event_type = 2;
}

enum EventType{
  EVENT_TYPE_UNSPECIFIED = 0;
  EVENT_TYPE_IM = 1; // IM消息
  EVENT_TYPE_IM_RCMD_MT_PROXY_RSP = 2; // AI红娘AI推荐返回消息
  EVENT_TYPE_IM_MARK_IM_READ = 3; // 标记消息已读
  EVENT_TYPE_CYBROS_AUDIT = 4; // 审核事件
  EVENT_TYPE_ROLE_PLAY_INTRODUCE = 5; // ai生成角色卡片简介
  EVENT_TYPE_ROLE_PLAY_GUIDE_FOLLOW = 6;  //关注事件
}

message HandleEventResponse{
  repeated HandleEventRespData resp_data = 1;
}

message HandleEventRespData{
  bytes resp_data = 1;
  uint32 resp_data_type = 2; // RespDataType
}

enum RespDataType{
  RESP_DATA_TYPE_UNSPECIFIED = 0;
  RESP_DATA_TYPE_IM_AI_CUPID_ADD = 1; // RespDataAiCupidAdd
  RESP_DATA_TYPE_IM_AI_CUPID_AT= 2;   //ai红娘at消息
}

// 返回的AI红娘加入内容
message RespDataAiCupidAdd{
  uint32 from_uid = 1;
  uint32 to_uid = 2;
  string content = 3;
  string ctx_id = 4;
  string cupid_nickname = 5;
  repeated string finished_topics = 6; //已完成的话题
  uint32 gap_time = 7; //间隔时间
}

// 返回的AI红娘@消息
message RespDataAiCupidAt{
  uint32 from_uid = 1;
  uint32 to_uid = 2;
  string at_content = 3;
  string ctx_id = 4;
  repeated string finished_topics = 5; //已完成的话题
  uint32 gap_time = 6; //间隔时间
}

// 返回的AI红娘引导关注消息
message RespDataAiCupidGuideFollow{
  uint32 from_uid = 1;
  uint32 to_uid = 2;
  string content = 3;
  string ctx_id = 4;
  repeated string finished_topics = 5; //已完成的话题
  uint32 gap_time = 6; //间隔时间
}

message RespDataRolePlayIntroduce {
  uint32 uid = 1; // 用户ID
  string cp_id = 2; // cp_id
  string cp_name = 3; // cp类型名称
  string introduce = 4; // 介绍
  string role_id = 5; // 角色id
  string role_name = 6; // 角色名
  string card_id = 7; // 角色卡id
  string ctx_id = 8;
}

message CreateTempRoleCardRequest {
  uint32 uid = 1;
  string cp_id = 2;
  string role_id = 3;
}

message CreateTempRoleCardResponse {
  string card_id = 1;
  uint32 remain_num = 2;
}

message DelCPRelationChattingRequest{
    repeated  uint32 uid_list = 1;
}

message DelCPRelationChattingResponse{

}





