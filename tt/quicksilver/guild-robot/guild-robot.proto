syntax = "proto3";

package guild_robot;

service GuildRobot {
  rpc UploadKnowledgeFileUrl(UploadKnowledgeFileUrlReq) returns (UploadKnowledgeFileUrlResp) {}
  rpc GetKnowledgeFileList(GetKnowledgeFileListReq) returns (GetKnowledgeFileListResp) {}
  rpc DeleteKnowledgeFile(DeleteKnowledgeFileReq) returns (DeleteKnowledgeFileResp) {}
}

// 知识类型
enum KnowledgeType {
  KNOWLEDGE_TYPE_UNSPECIFIED = 0; // 未知类型
  KNOWLEDGE_TYPE_RULES_AND_POLICIES = 1; // 规则政策 废弃
  KNOWLEDGE_TYPE_ACTIVITY = 2; // 活动 废弃
  KNOWLEDGE_TYPE_DATA_ASSESSMENT = 3; // 数据考核 废弃
  KNOWLEDGE_TYPE_VIOLATION_HANDLING = 4; // 违规处理 废弃
  KNOWLEDGE_TYPE_AMUSE = 5; // 娱乐厅
  KNOWLEDGE_TYPE_ACTIVITY_AND_FUNCTION = 6; // 活动与功能
  KNOWLEDGE_TYPE_ASSESSMENT_AND_POINTS = 7; // 考核与积分
  KNOWLEDGE_TYPE_ECOLOGICAL_SECURITY = 8; // 生态安全
  KNOWLEDGE_TYPE_ANCHOR = 9; // 主播
  KNOWLEDGE_TYPE_GUILD = 10; // 公会
}

// 机器人类型
enum RobotType {
  ROBOT_TYPE_UNSPECIFIED = 0; // 未知类型
  ROBOT_TYPE_AMUSE = 4; // 多人互动
  ROBOT_TYPE_YUYIN = 7; // 语音直播
  ROBOT_TYPE_ESPORT = 8; // 电竞
}

message KnowledgeFile {
  RobotType robot_type = 1; // 机器人类型，详见 RobotType
  uint32 id = 2; // 知识文件ID
  string file_name = 3; // 文件名
  KnowledgeType knowledge_type = 4; // 知识类型，详见 KnowledgeType
  string file_url = 5; // 文件URL
  uint64 create_time = 6; // 创建时间
  string creator = 7; // 创建者
  uint64 update_time = 8; // 更新时间
  string updater = 9; // 更新者
}

message UploadKnowledgeFileUrlReq {
  RobotType robot_type = 1; // 机器人类型，详见 RobotType
  uint32 id = 2; // 知识文件ID （若为更新填对应条目ID，若新建则不填）
  string file_name = 3; // 文件名
  KnowledgeType knowledge_type = 4; // 知识类型，详见 KnowledgeType
  string file_url = 5; // 文件URL
  string operator = 6; // 操作者
  uint64 update_time = 7; // 更新时间
}

message UploadKnowledgeFileUrlResp {
  KnowledgeFile knowledge_file = 1; // 上传或更新后的知识文件信息
}

message GetKnowledgeFileListReq {
  RobotType robot_type = 1; // 机器人类型，详见 RobotType
  KnowledgeType knowledge_type = 2; // 知识类型，选填
  uint32 page = 3; // 页码，从1开始
  uint32 page_size = 4; // 每页大小
  string search_query = 5; // 搜索查询，按文件名模糊搜索
}

message GetKnowledgeFileListResp {
  repeated KnowledgeFile knowledge_files = 1; // 知识文件列表
  uint32 total_count = 2; // 总条目数
}

message DeleteKnowledgeFileReq {
  RobotType robot_type = 1; // 机器人类型，详见 RobotType
  uint32 id = 2; // 知识文件ID
}

message DeleteKnowledgeFileResp {
  bool success = 1; // 删除是否成功
  string message = 2; // 错误信息或提示
}