syntax = "proto3";
package channel_play_middle;

//import "common/common.proto";
//import "common/topic_channel.proto";
import "tt/quicksilver/rcmd-local/common/common.proto";
import "tt/quicksilver/rcmd-local/common/topic_channel.proto";
import "google/protobuf/any.proto";


option go_package = "golang.52tt.com/protocol/services/channel-play-middle";

service ChannelPlayMiddle {
  rpc BatGetGameChannelViewMap(BatGetGameChannelViewMapReq) returns(BatGetGameChannelViewMapResp);
  rpc FilterTab(FilterTabReq) returns(FilterTabResp);
  rpc GetFastPcSupportTabList(GetFastPcSupportTabListReq) returns(GetFastPcSupportTabListResp);
}

enum ReqSource {
  REQ_SOURCE_UNSPECIFIED = 0;
  REQ_SOURCE_SEARCH_BY_NAME = 1; // 按文本搜索
  REQ_SOURCE_SEARCH_BY_ID = 2; // 按id搜索

  REQ_SOURCE_LIST = 3; // 垂直列表

}
message BatGetGameChannelViewMapReq {
  uint32 req_source = 1;
  repeated uint32 channel_ids = 2;
  RcmdInfo recommend_info = 3; // 推荐信息
  SourceListParams list_params = 4; // 首页房间列表才需要的参数

}

message SourceListParams{
  //区分首页入口,见channel-play_.proto ChannelListEnterSource
  uint32 channel_list_enter_source = 1;
  //房间列表样式,见channel-play_.proto ChannelListStyleType
  uint32 list_style_type = 2;
  //提供view的来源标识，见channel-play_.proto GenViewSource
  repeated uint32 gen_view_source = 3;
  string channel_package_id = 4;  //渠道包id
  uint32 tab_id = 5;
  string tab_ids_str = 6;
  string category_ids_str = 7;
}

message RcmdInfo {
  rcmd.common.LocationInfo self_location = 1;
  map<uint32, rcmd.common.ChannelInfo> channel_info_map = 3;
}

// Any 使用channel-play_.proto 的TopicChannelItem解析
message BatGetGameChannelViewMapResp {
  map<uint32, google.protobuf.Any> channel_view_map = 1;
}

message FilterTabReq {
    uint32 market_id = 1;
    uint32 client_type = 2;
    uint32 terminal_type = 3;
    uint32 client_version = 4;
    uint32 uid = 5;
    string channel_pkg_id = 6;
    repeated uint32 tab_ids = 7;
    repeated uint32 category_ids = 8;
}

message FilterTabResp {
    repeated uint32 tab_ids = 1;
    repeated uint32 category_ids = 2;
}

message GetFastPcSupportTabListReq {
  // 是否需要走玩法的过滤
  bool need_tab_filter = 1; // 是否需要过滤
}

message GetFastPcSupportTabListResp {
  message TabInfo {
    uint32 tab_id = 1; // 玩法id
    string tab_name = 2; // 玩法名称
  }
  repeated TabInfo tabs = 1; // 具体玩法内容
}