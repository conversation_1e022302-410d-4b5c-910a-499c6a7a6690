syntax = "proto3";

package channel_ext_game;
import "tt/quicksilver/extension/options/options.proto";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";
option go_package = "golang.52tt.com/protocol/services/channel-ext-game";

service ChannelExtGame {
    option (service.options.service_ext) = {
        service_name: "channel-ext-game"
      };

    // 获取用户登陆jsCode
    rpc GetUserExtGameJsCode(GetUserExtGameJsCodeReq) returns (GetUserExtGameJsCodeResp) {}
    // 注销jsCode
    rpc CancelUserExtGameJsCode(CancelUserExtGameJsCodeReq) returns (CancelUserExtGameJsCodeResp) {}

    // 获取用户openid
    rpc GetUserExtGameOpenid(GetUserExtGameOpenidReq) returns (GetUserExtGameOpenidResp) {}

    // simple query openId
    rpc SimpleQueryOpenid(SimpleQueryOpenidReq) returns (SimpleQueryOpenidResp) {}

    // 第三方游戏消费货币
    rpc ExtGameConsume(ExtGameConsumeReq) returns (ExtGameConsumeResp) {}

    // 第三方游戏发放奖励
    rpc ExtGameAward(ExtGameAwardReq) returns (ExtGameAwardResp) {}

    // 根据openid获取uid
    rpc GetUidByOpenid(GetUidByOpenidReq) returns (GetUidByOpenidResp) {}
    // 批量获取uid
    rpc BatchGetUidByOpenIds(BatchGetUidByOpenIdsReq) returns (BatchGetUidByOpenIdsResp) {}

    // 判断用户游戏入口权限
    rpc CheckUserGameAccess(CheckUserGameAccessReq) returns (CheckUserGameAccessResp) {}

    // 判断房间是否黑名单房间
    rpc CheckChannelBlackList(CheckChannelBlackListReq) returns (CheckChannelBlackListResp) {}

    // 随机获取一个白名单房间
    rpc GetWhiteChannelRandomly(GetWhiteChannelRandomlyReq) returns (GetWhiteChannelRandomlyResp) {}

    // 通过JsCode获取信息
    rpc GetAuthInfoByJsCode(GetAuthInfoByJsCodeReq) returns (GetAuthInfoByJsCodeResp) {}

    // 获取游戏信息列表
    rpc GetExtGameInfoList(GetExtGameInfoListReq) returns (GetExtGameInfoListResp) {}

    // 发放平台系统消息、房间公屏
    rpc SendPlatformMsg(SendPlatformMsgReq) returns (SendPlatformMsgResp) {}

    // 移除房间白名单
    rpc RemoveWhiteList(RemoveWhiteListReq) returns (RemoveWhiteListResp) {}

    // 批量新增房间白名单
    rpc AddWhiteList(AddWhiteListReq) returns (AddWhiteListResp) {}

    // 检查房间是否在白名单中
    rpc CheckWhiteList(CheckWhiteListReq) returns (CheckWhiteListResp) {}

     // TestChannelCommonHighLightIm
     rpc TestChannelCommonHighLightIm(TestChannelCommonHighLightImReq) returns (TestChannelCommonHighLightImResp) {}

     // DailyConsumeTotalCntPush
    rpc DailyConsumeTotalCntPush(DailyConsumeTotalCntPushReq) returns (DailyConsumeTotalCntPushResp) {}

    // 获取session版本号
    rpc GetSessionVersion(GetSessionVersionReq) returns (GetSessionVersionResp) {}
    // 更新session版本号
    rpc IncrSessionVersion(IncrSessionVersionReq) returns (IncrSessionVersionResp) {}
    // 注册房间映射关系
    rpc RegisterChannelMap(RegisterChannelMapReq) returns (RegisterChannelMapResp) {}
    // 批量获取房间映射关系
    rpc BatchGetChannelMap(BatchGetChannelMapReq) returns (BatchGetChannelMapResp) {}


    /*********对账接口**********/
    // T豆消费数据对账
    rpc GetConsumeTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
    rpc GetConsumeOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
    // 发放包裹数据对账
    rpc GetAwardTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
    rpc GetAwardOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

}

message GetUserExtGameJsCodeReq {
    uint32 uid = 1;
    uint32 channel_id = 2[deprecated = true];
    uint32 game_id = 3[deprecated = true]; // 内部映射成appid
    string channel_view_id = 4;
    string app_id = 5; // 游戏AppId
}

message GetUserExtGameJsCodeResp {
    string js_code = 1;
    string openid = 2;
}

message GetUserExtGameOpenidReq{
    uint32 uid =1;
}

message GetUserExtGameOpenidResp{
    string openid = 1;
}

message ExtGameConsumeReq {
    string openid = 1;
    string appid = 2;
    string order_id = 3;
    uint32 amount = 4;     // T豆数量
    int64 outside_ts = 5;  // 外部订单时间戳
    uint32 uid = 6;        // 用户ID
}

message ExtGameConsumeResp {
}

message ExtGameAwardReq {
    enum RewardType {
        REWARD_TYPE_UNSPECIFIED = 0;
        REWARD_TYPE_TBEAN = 1;        // T豆
        REWARD_TYPE_RED_DIAMONDD = 2; // 红钻
        REWARD_TYPE_HEADWEAR = 3;     // 麦位框
        REWARD_TYPE_NAMEPLATE= 4;     // 个人铭牌
        REWARD_TYPE_HORSE= 5;         // 虚拟装扮（天）
        REWARD_TYPE_OFFICIAL_CERT= 6; // 大v认证
        REWARD_TYPE_USER_DECORATION= 7; // 主页飘
        REWARD_TYPE_MEDAL= 8;         // 勋章
    }
    string uid = 1[deprecated = true];
    string openid =2;
    string appid = 3;
    string order_id = 4;
    string reward_id = 5;   // 平台奖励ID
    uint32 reward_type = 6; // 平台奖励类型
    uint32 amount = 7;      // 奖励发放数量

    uint32 user_id = 8;      // 用户ID,用户uid
}

message ExtGameAwardResp {
}

message CancelUserExtGameJsCodeReq {
    string js_code = 1;
}

message CancelUserExtGameJsCodeResp {
}

message GetUidByOpenidReq {
    string openid = 1;
}

message GetUidByOpenidResp {
    uint32 uid = 1;
}

message CheckUserGameAccessReq {
    uint32 uid = 1;
    uint32 game_id = 2[deprecated = true];
    uint32 channel_id = 3;
    string app_id = 4; // 游戏AppId
}

message CheckUserGameAccessResp {
    bool access = 1;
}

message CheckChannelBlackListReq {
    uint32 channel_id = 1;
}

message CheckChannelBlackListResp {
    bool is_black = 1;
}

message GetWhiteChannelRandomlyReq {
    uint32 count = 1; // 随机获取的房间数量
}

message GetWhiteChannelRandomlyResp {
    uint32 channel_id = 1[deprecated = true]; 
    repeated uint32 cid_list = 2; // 白名单房间列表
}

message GetAuthInfoByJsCodeReq {
    string js_code = 1;
}

message GetAuthInfoByJsCodeResp {
    string openid = 1; // openid
    string uid = 2; // 通过openid获取的uid
    string appid = 3; // 内部映射成game_id
    uint32 channel_display_id = 4 [deprecated=true]; // 游戏方joinRoom接口传入的room_id（对应我们平台的display_cid）
    string channel_view_id = 5;
    uint32 client_type = 6; // 客户端类型
    uint32 market_id = 7; // 马甲包ID
}

// 获取游戏信息列表
message GetExtGameInfoListReq {
}

// 获取游戏信息列表
message GetExtGameInfoListResp {
    repeated ExtGameInfo game_info = 1; // 游戏信息
}


// 游戏包信息
message ExtGameInfo{
    uint32 game_id = 1[deprecated = true]; // 游戏标识
    string name = 2;    // 游戏名称
    string content = 3; // 更新描述
    string version = 4; // 游戏版本
    uint32 build = 5;   // 游戏build号
    string zip = 6; // zip包名称
    string h5_url = 7;  // 游戏地址
    string full_url = 8; // 全量资源URL地址,下载压缩包
    string md5 = 9;    // md5
    uint32 size = 10; // zip包大小
    string app_id = 11; // 游戏AppId
}

message BatchGetUidByOpenIdsReq {
    repeated string open_id_list = 1; // 查询的用户open_id列表
}


message BatchGetUidByOpenIdsResp {
    map<string, uint32> uid_map = 1;
}


message SendPlatformMsgReq{
    string app_id = 1; // 游戏AppId
    repeated string open_id_list = 2; // 待发放的用户列表
    uint32 template_id = 3; // 模板ID
    uint32 send_type = 4; // 发送类型
}

message SendPlatformMsgResp{
}

message RemoveWhiteListReq {
    uint32 channel_id = 1;
    uint32 uid = 2; // 用户ID
}

message RemoveWhiteListResp{
}

message AddWhiteListReq{
    repeated uint32 cid_list = 1; // 房间列表
    repeated uint32 uid_list = 2; 
}

message AddWhiteListResp{
}

message CheckWhiteListReq{
    uint32 channel_id = 1; // 房间ID
    uint32 uid = 2; // uid
}

message CheckWhiteListResp{
    bool cid_white = 1;
    bool uid_white = 2;
}

message TestChannelCommonHighLightImReq {
    uint32 uid = 1;
    uint32 cid = 2;
    string content = 3;
    string high_light_content = 4;  // 高亮的内容仅支持匹配第一个
    string jump_url = 5;
    string font_color = 6;     // 字体颜色
    string border_color = 7;   // 边框颜色
    string high_light_color = 8;   // 高亮字体颜色
    string background_color = 9;   // 背景颜色
}

message TestChannelCommonHighLightImResp {
}

message DailyConsumeTotalCntPushReq {
    int64 begin_ts = 1; // 开始时间戳
    int64 end_ts = 2;   // 结束时间戳
    bool push_lark = 3[deprecated=true]; // 是否推送到lark
    string title = 4; // 推送标题
}

message DailyConsumeTotalCntPushResp {
    uint32 total_cnt = 1; // 消费总数
    uint32 value = 2; // 消费总金额
}

// 只查询，不创建openId
message SimpleQueryOpenidReq {
    uint32 uid = 1;
    string ttid = 2;
}

message SimpleQueryOpenidResp {
    string openid = 1;
}

message GetSessionVersionReq {
    string open_id = 1; // 用户的openId
}
message GetSessionVersionResp {
    int64 version = 1; // 会话版本
}

message IncrSessionVersionReq {
    string open_id = 1; // 用户的openId
}

message IncrSessionVersionResp {
    int64 version = 1; // 会话版本
}


// 覆盖更新
message RegisterChannelMapReq {
    uint32 channel_id = 1;
    string channel_view_id = 2; // 外显ID
}

message RegisterChannelMapResp {
}

message BatchGetChannelMapReq {
    repeated string channel_view_id_list = 1; // 房间ID列表
}

message BatchGetChannelMapResp {
    map<string, uint32> channel_map = 1; // key: channel_view_id, value: channel_id
}