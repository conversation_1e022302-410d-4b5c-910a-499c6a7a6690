syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/ad-center";
package ad_center;
/*
ad_id 类别
1	开黑tab
2	登录弹窗
3	开屏页
4	房间内悬浮框
5	开黑tab公关
6	活动配置中心
7	发布器气泡提示
8	IM跟随入口
9	开黑tab顶部
10 新人广告位
11 房间广告位 
12 pia戏聚合页广告位 
13 pia戏房间广告位 
14 开黑tab筛选列表
15 tab列表强插位置
16 乐窝广告位
17 活动动态资源位
18 专区banner位
19 专区金刚区
20 活动中心入口文案
21 谜境首页右上角
22 谜境首页推荐右边
23 剧本杀预约
24 房间任务挂件
*/

service AdCenter {
  rpc BatchGetAd (BatchGetAdReq) returns (BatchGetAdResp) {
  }
  rpc GetAd (GetAdReq) returns (GetAdResp) {
  }
  rpc SetAd (SetAdReq) returns (SetAdResp) {
  }
  rpc SearchAds (SearchAdsReq) returns (SearchAdsResp) {
  }
  rpc GetCampaign (GetCampaignReq) returns (GetCampaignResp) {
  }
  rpc SetCampaign (SetCampaignReq) returns (SetCampaignResp) {
  }
  rpc BatchSetCampaign (BatchSetCampaignReq) returns (BatchSetCampaignResp) {
  }

  rpc SearchCampaigns (SearchCampaignsReq) returns (SearchCampaignsResp) {
  }
  rpc GetFilter (GetFilterReq) returns (GetFilterResp) {
  }
  rpc SetFilter (SetFilterReq) returns (SetFilterResp) {
  }
  rpc SearchFilters (SearchFiltersReq) returns (SearchFiltersResp) {
  }
  rpc CoincidenceCount (CoincidenceCountReq) returns (CoincidenceCountResp) {
  }
  rpc CheckTagIdMate (CheckTagIdMateReq) returns (CheckTagIdMateResp) {
  }
  rpc DiagnosisCampaign (DiagnosisCampaignReq) returns (DiagnosisCampaignResp) {
  }
  rpc CommitAdExposure (CommitAdExposureReq) returns (CommitAdExposureResp) {
  }
  rpc CommitAdClick (CommitAdClickReq) returns (CommitAdClickResp) {
  }
  rpc QueryData (QueryDataReq) returns (QueryDataResp) {
  }
  rpc GetPolicy (GetPolicyReq) returns (GetPolicyResp) {
  }
  rpc SetPolicy (SetPolicyReq) returns (SetPolicyResp) {
  }
  rpc SearchPolicies (SearchPoliciesReq) returns (SearchPoliciesResp) {
  }
  rpc GetABTestInfo (GetABTestInfoReq) returns (GetABTestInfoResp) {
  }
  rpc SetABTestInfo (SetABTestInfoReq) returns (SetABTestInfoResp) {
  }
  rpc GeneralMsg (GeneralMsgReq) returns (GeneralMsgResp) {
  }
  rpc CheckABTest (CheckABTestReq) returns (CheckABTestResp) {
  }

  //专区相关 专区和广告位绑定 新建专区就是新建广告位
  rpc GetAreaInfoList (GetAreaInfoListReq) returns (GetAreaInfoListResp) {  
  }
  rpc SetAreaInfo (SetAreaInfoReq) returns (SetAreaInfoResp) {
  }
  //推小红点
  rpc RecommendCampaign (RecommendCampaignReq) returns (RecommendCampaignResp) {}

  //检查活动是否有相同优先级
  rpc CheckCampaignSamePriority (CheckCampaignSamePriorityReq) returns (CheckCampaignSamePriorityResp) {}

}

message CheckCampaignSamePriorityReq {
  CampaignFilter campaign = 1;
}

message CheckCampaignSamePriorityResp {
  bool is_same_priority = 1;
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum Ad_Mic_Mode_Type {
  // 非法
  INVALID = 0;
  // 娱乐模式(派对，点唱厅，扩列，踢保，小故事)
  PGC_Fun = 1;
  // CP战
  PGC_CP = 2;
  // 狼人杀
  PGC_WarewolvesGame = 3;
  // pia戏
  PGC_PIA = 4;
  // 相亲交友 
  PGC_Dating = 5;
}

// 批量获取广告展示数据 
message BatchGetAdReq {
  uint32 uid = 1;
  repeated uint32 ad_id_list = 2; 
  uint32 channel_id = 3;
  uint32 app_id = 4;
  uint32 market_id = 5;
  uint32 client_ip = 6;
  uint32 client_type = 7;
  uint32 client_version = 8;
  // 平台
  uint32 platform = 9;
  uint32 os = 10;

  uint32 channel_type = 11;
  bytes device_id = 12;
  uint32 channel_tab_id = 13;//如果是0且ugc房 旧版的情况 也不进行返回了

  uint32 area_id = 14;//专区id 
  uint32 mic_mode = 15;//麦位模式 Ad_Mic_Mode_Type  pgc的 自定义的 与channel_.proto 不完全一样 
  uint32 mic_count = 16;//麦位数
  uint32 channel_tag_id = 17;//房间标签
  string market_channel_id = 18;//渠道包ID
  // buf:lint:ignore ENUM_PASCAL_CASE
  enum Ad_Config_Extra_Type {
    // 非法
    INVALID = 0;
    //活动中心
    ActivityArea = 1;
  }
  uint32 get_config_extra_type = 19;//获取活动之外的配置 
  
  
}
message ConfigExtra {
  repeated AreaInfo area_infos = 1;//额外活动专区列表
}
message BatchGetAdResp {
  map<uint32, CampaignList> ad_campaign_map = 1;
  AreaInfo area_info = 2;
  ConfigExtra extra = 3;//额外的配置
}

message CampaignList {
  repeated Campaign campaign_list = 1;
}
enum VisibleUserType{
  VisibleUserType_UNDEFINED = 0;
  VisibleUserType_ALL = 1; // 所有用户
  VisibleUserType_FRESHMAN = 2; // 新用户
}
message VisibleUser{
  uint32 type = 1;
  string desc = 2; // 描述
  uint32 period = 3; // 新用户可见时间，单位：天
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum Area_Type {
  Music = 0;//音乐专区
  Activity = 1;//活动专区
}



message AreaInfo {
  uint32 id = 1;
  string name = 2;
  int64 update_time = 3;
  int64 create_time = 4;
  string operator = 5;//操作人
  repeated uint32 ad_ids = 6;//包括的广告位id 音乐专区:ad_ids[0]为banner ad_ids[1]为金刚区 活动版块:ad_ids[0]为活动广告位
  bool delete = 7;//删除
  uint32 type = 8;//专区类型 0为 音乐专区 1为活动版块
  repeated string channels = 9; // 渠道号
  VisibleUser user = 10; // 对于特定渠道号用户生效
  uint32 area_priority = 27;//专区排序优先级
  
}

message GetAreaInfoListReq {
  uint32 type = 1;//专区类型 Area_Type
  
}

message GetAreaInfoListResp {
  repeated AreaInfo area_list = 1;
}

message SetAreaInfoReq {
  repeated AreaInfo area_infos = 1;//专区信息
}

message SetAreaInfoResp {

}
// ============================== 广告
// 广告位
message Ad {
  uint32 id = 1;
  string name = 2;
  uint32 height = 3;
  uint32 width = 4;
  uint32 type = 5; // CampaignCreativeType
  uint32 status = 6; // 是否开启，AdCenterStatus
  int64 update_time = 7;
  int64 create_time = 8;
  bool delete = 9;
}
// 获取广告位数据
message GetAdReq {
  uint32 id = 1;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message GetAdResp {
  Ad Ad = 1;
}

// 设置广告位数据
message SetAdReq {
  Ad ad = 1;
}
message SetAdResp {
  uint32 id = 1;
}

// 查询广告位数据
message SearchAdsReq {
  uint32 offset = 1;
  uint32 limit = 2;
  int64 begin_time = 3;
  int64 end_time = 4;
  string name = 5;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SearchAdsResp {
  repeated Ad Ads = 1;
}

// 活动
message Campaign {
  uint32 id = 1;
  string name = 2;
  // 图片列表
  repeated string img_url_list = 3;
  // 视频地址
  string video_url = 4;
  // 内容
  string text = 5;
  string text_color = 6;
  // 跳转链接
  string jump_url = 7;
  // CampaignCreativeType
  uint32 type = 8;
  // 开屏广告持续时间
  uint32 duration = 9;

  uint32 status = 10; // 是否开启，AdCenterStatus
  int64 update_time = 11;
  int64 create_time = 12;
  bool delete = 13;

  int64 begin_time = 14;
  int64 end_time = 15;
  // 额外信息参数，json
  string extra = 16;

  repeated Material materials = 17;

  uint32 policy_id = 18;
  uint32 policy_type = 19;
  enum PolicyType {
    // 普通
    NORMAL = 0;
    // 赛马
    OPTIMIZE = 1;
    // ab测
    ABTEST = 2;
    // 随机类型
    RANDOM = 3;
  }
  //群组链接类型
  uint32 type_jump_url = 20;
  repeated uint32 tgroup_ids = 21;
  uint32 tablist_insert_n = 22;//推荐列表广告位的出现位置
  
  string content_url = 24;//资源位内容链接
  uint32 skip_when_n = 25;//配置 连续n次不弹出 取消展示该弹窗
  int64 last_push_time = 26;
  uint32 area_id = 27;//专区排序优先级
  uint32 mysteryplace_id = 28;//剧本杀id
  string tab_alias_name = 29;//短别名

  repeated ContentInfo content_info_list = 30;
  // 备注
  string remark = 31;
  // 浮层图标链接
  string float_layer_url = 32;

  // 活动类型 see ActivityType
  uint32 activity_type = 33;
  // 子活动类型 see SubActivityType
  uint32 sub_activity_type = 34;
}

enum ActivityType {
  // 未定义
  ACTIVITY_TYPE_UNSPECIFIED = 0;
  // 用户类型活动
  ACTIVITY_TYPE_USER = 1;
  // 营收类型活动
  ACTIVITY_TYPE_REVENUE = 2;
}

enum SubActivityType {
  // 未定义
  SUB_ACTIVITY_TYPE_UNSPECIFIED = 0;
  // 营收福利活动（有奖销售类）
  SUB_ACTIVITY_TYPE_REVENUE_BENEFIT = 1;
  // 付费人数活动（如0.1元、会员活动等）
  SUB_ACTIVITY_TYPE_PAY = 2;
  // 主播玩法活动（待补充）
  SUB_ACTIVITY_TYPE_ANCHOR = 3;
  // 大型赛事活动（如周年庆、赛事等）
  SUB_ACTIVITY_TYPE_BIG_EVENT = 4;
}

message ContentInfo {
  // 平台； 1 安卓 2 ios 3 pc ； 同 Filter.Platform
  uint32 platform = 1;
  // 马甲包 ； 0. TT、 2. 欢游、 5.麦可、 6. 谜镜 ； 同 Filter.MarketId
  uint32 market_id = 2;
  // 展示内容链接
  string content_url = 3;
}

message Material {
  uint32 id = 1;
  // 图片列表
  repeated string img_url_list = 2;
  // 视频地址
  string video_url = 3;
  // 内容
  repeated string text_list = 4;
  repeated string text_color_list = 5;

  int32 optimize_index = 6;
  uint32 lab_id = 7;

  string jump_url = 8;
  string web_id = 9;
  bool is_hided = 10;
  repeated uint32 tgroup_ids = 11;
  //要展示的人群 视角
  uint32 uid_view_type = 12;//用户视角 1 签约视角2 会长视角3
  uint32 skip_when_n = 13;//配置 连续n次不弹出 取消展示该弹窗
  string content_url = 14;//资源位内容链接
}

// 获取广告投放数据
message GetCampaignReq {
  uint32 id = 1;
}
message GetCampaignResp {
  CampaignFilter campaign = 1;
}

// 设置广告投放数据
message SetCampaignReq {
  CampaignFilter campaign = 1;
}
message SetCampaignResp {
  uint32 id = 1;
}

message BatchSetCampaignReq {
  repeated CampaignFilter campaign = 1;
}
message BatchSetCampaignResp {
  repeated uint32 ids = 1;//如果部分结束 就是前n个id
}

// 查询广告投放数据
message SearchCampaignsReq {
  uint32 offset = 1;
  uint32 limit = 2;
  int64 begin_time = 3;
  int64 end_time = 4;
  string name = 5;
  uint32 ad_id = 6;
  int32 update_time_order_by = 7; // -1 倒排，0 默认，1 正排
  int32 begin_time_order_by = 8; // -1 倒排，0 默认，1 正排
  int32 priority_order_by = 9; // -1 倒排，0 默认，1 正排
  CampaignFilterStatusDesc status_desc = 10;
  enum CampaignFilterStatusDesc {
    // 未知
    UNKNOW = 0;
    // 未生效
    CLOSE = 1;
    // 生效中
    OPEN = 2;
    // 已过期
    EXPIRED = 3;
  }
  //房间类型 全部就是不传
  repeated uint32 channel_types = 11;
  enum ChannelType {
    INVALID_CHANNEL_TYPE = 0;
    PGC = 1;
    UGC = 2;
    LIVE = 3;
    GUILD_PGC = 4; //公会开黑房
  }
  //主题类型 全部就是不传
  repeated uint32 channel_tab_ids = 12;
  //类型
  repeated uint32 push_marketid_list  = 13;
  //插入位置 大于1的进行筛选
  repeated uint32 tablist_insert_ns = 14;
  //专区id 版块管理 
  repeated uint32 area_ids  = 15;//area_ids=[0]&area_type=1为无板块活动
  uint32 area_type = 16;//0为音乐专区 1为活动中心

  // 活动 id
  uint32 campaign_id = 17;
}
message SearchCampaignsResp {
  repeated CampaignFilter campaigns = 1;
  uint32 count = 2;
}

message CampaignFilter {
  Campaign campaign = 1;
  Filter filter = 2;
  CampaignFilterStatusDesc status_desc = 3;
  enum CampaignFilterStatusDesc {
    // 未知
    UNKNOW = 0;
    // 未生效
    CLOSE = 1;
    // 生效中
    OPEN = 2;
    // 已过期
    EXPIRED = 3;
  }
}
//
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message Filter {
  uint32 id = 1;
  repeated uint32 campaign_ids = 2;
  string name = 3;
  int64 begin_time = 4;
  int64 end_time = 5;
  repeated uint32 AdIdList = 6;
  // 人群id ，-1 新用户，0 全部用户，正数 人群id
  int32 tag_id = 7;
  
  
  string tag_name = 8;
  // 优先级
  uint32 priority = 9;
  // 关联活动id
  string web_id = 10;
  // 设备过滤
  uint32 platform = 11;
  enum Platform {
    // 全部
    ALL = 0;
    // android
    ANDROID = 1;
    // ios
    IOS = 2;
    // pc
    PC = 3;
  }
  //platform 安卓1 ios 2  全部3

  // 房间类型过滤
  uint32 channel_type = 12;
  enum ChannelType {
    INVALID_CHANNEL_TYPE = 0;
    PGC = 1;
    UGC = 2;
    LIVE = 3;
    GUILD_PGC = 4;
    
  }

  uint32 hour_frequency = 13;
  uint32 day_frequency = 14;

  //    uint32 gender = 12;
  //    uint32 min_age = 13;
  //    uint32 max_age = 14;
  //    string ip = 15;
  //    repeated uint32 province_id = 16;
  //    repeated uint32 city_id = 17;

  int64 update_time = 15;
  int64 create_time = 16;
  bool delete = 17;
  uint32 new_user_by_day = 18; // 注册X天的新设备新用户
  enum MarketId {
    MARKET_NONE = 0;  // TT原版
    MARKET_LITE = 1;  // 轻量版（不知道干嘛的），删除某些功能
    MARKET_HUANYOU = 2;     // 欢游，TT的紧急替代版本。
    MARKET_ZAIYA = 3;       // 在呀，TT的紧急替代版本。
    MARKET_TOP_SPEED = 4;   // 急速版，TT的紧急替代版本。 T次元
    MARKET_MAIKE = 5;   // 麦可

    MARKET_GAME_SDK = 12; // sdk 客户端, 值与其 appid 相同

    MARKET_ALL      = 65534;
    MARKET_MAX      = 65535;
  }
  repeated uint32 push_marketid_list  = 19;//新版本都会有值 兼容老版本 查询为空 默认tt的
  //房间主题  会有房间主题 房间主题id 
  message ChannelInfo {
    uint32 tab_id = 1;
    string tab_name = 2;
    uint32 mic_mode = 3;// Ad_Mic_Mode_Type
    uint32 channel_type = 5;//pcg ugc live
    uint32 category_id = 6;//pcg ugc live
    repeated uint32 mic_count = 7;//活动的麦位数量
    uint32 tag_id = 8;// tag_id
    string tag_name = 9;//tag_name
  }
  repeated ChannelInfo channel_infos = 20;
  int32 tag_id_count = 21;
  
  uint32 tablist_insert_n = 22;//推荐列表广告位的出现位置

  uint32 campaign_activity_type = 23;//活动类型
  uint32 stat_type = 24;//活动统计类型 1 房间维度 2 个人维度

  uint32 area_id = 25;//专区id 

  uint32 mysteryplace_id = 26;//剧本杀id

  // 人群包列表
  repeated int32 user_group_id_list = 27;
  // 房间包 id
  repeated int32 room_group_id_list = 28;
  // 平台列表； see Platform
  repeated uint32 platform_list = 29;
  
}

message TGroupInfo {
  uint32 tgroup_mem_cnt = 1;//群人数
  uint32 tgroup_id = 2;//群id
  string name = 3;//群名字
}
// 获取广告过滤数据
message GetFilterReq {
  uint32 id = 1;
}
message GetFilterResp {
  Filter filter = 1;
}

// 设置广告过滤数据
message SetFilterReq {
  Filter filter = 1;
}
message SetFilterResp {
  uint32 id = 1;
}

// 查询广告过滤数据
message SearchFiltersReq {
  uint32 offset = 1;
  uint32 limit = 2;
  int64 begin_time = 3;
  int64 end_time = 4;
  string name = 5;
}
message SearchFiltersResp {
  repeated Filter filters = 1;
}

// 获取策略数据
message GetPolicyReq {
  uint32 id = 1;
}
message GetPolicyResp {
  Policy policy = 1;
}

// 设置策略数据
message SetPolicyReq {
  Policy policy = 1;
}
message SetPolicyResp {
  uint32 id = 1;
}

// 查询策略数据
message SearchPoliciesReq {
  uint32 offset = 1;
  uint32 limit = 2;
  string name = 3;
  uint32 id = 4;
  int32 type_jump_url = 5;//-1 为全部 0为非群组  1是群组的
}
message SearchPoliciesResp {
  repeated Policy policies = 1;
  uint32 count = 2;
}


message Policy {
  uint32 id = 1;
  string name = 2;
  uint32 type = 3; // PolicyType
  bool delete = 4;
  uint32 ab_test_id = 5;
  enum PolicyType {
    // 普通
    NORMAL = 0;
    // 赛马
    OPTIMIZE = 1;
    // ab测
    ABTEST = 2;
  }
  int64 update_time = 6;
  int64 create_time = 7;
  repeated Lab labs = 8;
  string normal_policy_url = 9;//无实验类型时的url
  //群组链接类型
  uint32 type_jump_url = 10;
  //群组所依赖的主题
  repeated TGroupInfo tgroup_infos = 11;
}

// 查询ab测试的实验信息
message GetABTestInfoReq {
  uint32 id = 1;
}
message GetABTestInfoResp {
  uint32 id = 1;
  string name = 2;
  repeated Lab labs = 3;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message Lab {
  uint32 id = 1;
  string name = 2;
  uint32 AllocRatio = 3;
  string policy_url = 4;//活动链接
  repeated TGroupInfo tgroup_infos = 5;
}

message SetABTestInfoReq {
  uint32 id = 1;
  string name = 2;
  repeated Lab labs = 3;
}
message SetABTestInfoResp {
  uint32 id = 1;
}

message GeneralMsgReq {
  string msg = 1;
}
message GeneralMsgResp {
  string msg = 1;
}

enum CampaignCreativeType {
  // 图文
  TELETEXT = 0;
  // 轮播图
  SLIDESHOW = 1;
  // 信息流
  FLOW = 3;
  // 视频
  VIDEO = 4;
  
}

enum AdCenterStatus {
  // 关闭
  CLOSE = 0;
  // 开启
  OPEN = 1;
}

// 重合度计算
message CoincidenceCountReq {
  uint32 ad_id = 1;
  int64 begin_time = 2;
  int64 end_time = 3;
  uint32 channel_type = 4;
  int32 tag_id = 5;
  uint32 filter_id = 6;
}
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CoincidenceCount{
  string jumpURL = 1;//活动链接url
  string thanGroupId = 2;//冲突的人群包id
  string thanGroupName = 3;//冲突的人群包名称
  uint32 priority = 4;//优先级
  uint32 count = 5;//重合人数
  string policy_name = 6;//活动名称
  uint32 campaign_id = 7;//广告id
}
message CoincidenceCountResp {
  repeated string toast_list  = 1;
  repeated CoincidenceCount coincidence_list = 2;
}

message CheckTagIdMateReq {
  uint32 uid = 1;
  uint32 tag_id = 2;
}

message CheckTagIdMateResp {
  bool flag = 1;
  uint32 tag_id = 2;
}

message DiagnosisCampaignReq {
  uint32 campaign_id = 1;
  uint32 minute = 2;
}

message DiagnosisCampaignResp {
}

message CommitAdExposureReq {
  uint32 uid = 1;
  uint32 ad_id = 2;
  uint32 channel_id = 3;
  uint32 app_id = 4;
  uint32 market_id = 5;
  uint32 client_ip = 6;
  uint32 client_type = 7;
  uint32 client_version = 8;
  // 平台
  uint32 platform = 9;
  uint32 os = 10;

  uint32 channel_type = 11;
  uint32 campaign_id = 12;
  uint32 material_id = 13;
}

message CommitAdExposureResp {
}

message CommitAdClickReq {
  uint32 uid = 1;
  uint32 ad_id = 2;
  uint32 channel_id = 3;
  uint32 app_id = 4;
  uint32 market_id = 5;
  uint32 client_ip = 6;
  uint32 client_type = 7;
  uint32 client_version = 8;
  // 平台
  uint32 platform = 9;
  uint32 os = 10;

  uint32 channel_type = 11;
  uint32 campaign_id = 12;
  uint32 material_id = 13;
}

message CommitAdClickResp {
}

message QueryDataReq {
  uint32 ad_id = 1;
  uint32 campaign_id = 2;
  int64 from_date = 3;
  int64 to_date = 4;
  string unit = 5;
}

message QueryDataResp {
  repeated string quotas = 1;
  repeated Metric metrics = 2;
  message Metric {
    repeated string metric = 1;
  }
}

message CheckABTestReq {
  string tag = 1;
  uint32 uid = 2;
}

message CheckABTestResp {
  int64 lab_id = 2;
}

message RecommendCampaignReq{
  uint32 campaign_id = 1;
}
message RecommendCampaignResp{}


