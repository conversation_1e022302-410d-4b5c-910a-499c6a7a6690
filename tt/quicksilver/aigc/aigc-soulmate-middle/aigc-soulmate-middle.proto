syntax = "proto3";

package aigc_soulmate_middle;

option go_package = "golang.52tt.com/protocol/services/aigc/aigc-soulmate-middle";

service AigcSoulmateMiddle {
  // 获取群组消息列表
  rpc GetGroupMsgList(GetGroupMsgListReq) returns(GetGroupMsgListResp) {}
  // 批量获取群组最新消息列表
  rpc BatchGetGroupLastMsgList(BatchGetGroupLastMsgListReq) returns(BatchGetGroupLastMsgListResp) {}
  //写timeline消息
  rpc SendGroupImMsg(SendGroupImMsgReq) returns(SendGroupImMsgResp) {}

  // 群聊模板运营后台
  rpc CreateGroupTemplate(CreateGroupTemplateReq) returns(CreateGroupTemplateResp) {}
  rpc UpdateGroupTemplate(UpdateGroupTemplateReq) returns(UpdateGroupTemplateResp) {}
  rpc DeleteGroupTemplate(DeleteGroupTemplateReq) returns(DeleteGroupTemplateResp) {}
  rpc GetGroupTemplateById(GetGroupTemplateByIdReq) returns(GetGroupTemplateByIdResp) {}
  rpc GetGroupTemplateByPage(GetGroupTemplateByPageReq) returns(GetGroupTemplateByPageResp) {}

  // 句子类型剩余数量
  rpc GetSentenceAvailableCount(GetSentenceAvailableCountReq) returns(GetSentenceAvailableCountResp) {}
  // 获取用户当日使用的专属句数总数
  rpc GetUserSentenceCount(GetUserSentenceCountReq) returns(GetUserSentenceCountResp) {}

  // 是否达到句数上限
  rpc IsReachLimit(IsReachLimitReq) returns(IsReachLimitResp) {}
  // 消耗句数
  rpc ConsumeSentenceCount(ConsumeSentenceCountReq) returns(ConsumeSentenceCountResp) {}
  // 获取句数提示需要的配置
  rpc GetTipsCount(GetTipsCountRequest) returns(GetTipsCountResponse) {}
}

enum ImMsgType {
    ImMsgTypeUnknown = 0;
    // 文本
    ImMsgTypeText = 1;
    // CUE
    ImMsgTypeCue = 2;
    // 表情
    ImMsgTypeEmoticon = 3;
    // 沉默(AI伴侣由活跃切为沉默)
    ImMsgTypeSilence = 4;
    // 传送门
    ImMsgTypeAirTicket = 5;
    // AI伴侣统一消息通道
    ImMsgAIPartner = 6;
}

//多人群组开始起用
//内容类型
//改动需同步到web_im_logic
enum ImMsgContentType {
    IM_MSG_CONTENT_TYPE_UNSPECIFIED = 0;
    // 文本
    IM_MSG_CONTENT_TYPE_TEXT = 1;
    // 文本+语音
    IM_MSG_CONTENT_TYPE_TEXT_VOICE = 2;
    // 表情
    IM_MSG_CONTENT_TYPE_EMOTION = 3;
    // 图片
    IM_MSG_CONTENT_TYPE_IMAGE = 4;
}

//业务类型
//改动需同步到web_im_logic
enum ImBusiType {
    //未知类型
    IM_BUSI_TYPE_UNSPECIFIED = 0;
    // 单人群聊
    IM_BUSI_TYPE_SINGLE_GROUP = 1;
    // 多人群聊
    IM_BUSI_TYPE_MULTI_GROUP = 2;
    // 多角色聊天
    IM_BUSI_TYPE_MULTI_ROLE = 3;
}

//命令类型 非内容消息需要指定业务类型前缀
//改动需同步到web_im_logic
enum ImCmdType {
    //未知类型
    IM_CMD_TYPE_UNSPECIFIED = 0;
    // 内容消息
    IM_CMD_TYPE_CONTENT_MSG = 1;
    // 句数提示
    IM_CMD_TYPE_SENTENCE_TIP = 2;
}

message ImMsg {
    // 消息类型
    ImMsgType type = 1;
    // 消息内容
    string content = 2;
    // 扩展消息
    bytes ext = 3;

    // 消息发送时间(毫秒)
    int64 sent_at = 4;

    uint32 seq_id = 5; // 消息序号

    uint32 im_busi_type = 6; //业务类型 ImBusiType
    uint32 content_type = 7; //内容类型 ImMsgContentType
    uint32 im_cmd_type = 8; //命令类型 ImCmdType

    string transparent_ext_msg = 9; //透传消息扩展信息,base64编码
}

enum GroupSendType {
    GroupSendTypeUnknown = 0;
    // 用户发AI
    GroupSendTypeUser2AI = 1;
    // AI发用户
    GroupSendTypeAI2User = 2;
}

message GroupTimeLineMsg {
    // 群实例id
    uint32 group_id = 1;
    uint32 group_template_id = 2;
    repeated uint32 role_ids = 3;  //如果ai发往用户这里只有一个角色，用户发往ai是个at列表
    // 用户id
    uint32 uid = 4;

    ImMsg msg = 5;

    GroupSendType group_send_type = 6;
    repeated uint32 at_uids = 7; //用户at用户，角色at用户
    repeated uint32 at_role_ids = 8; //多人群聊(角色at角色，用户at角色)
}

message SendOption {
    // 是否发送离线推送
    bool with_offline_push = 1;
    // 离线推送内容
    string offline_push_content = 2;

    // 是否送审
    bool with_audit = 3;
    // 送审文本内容
    string audit_text = 4;
}

message SendGroupImMsgReq {
    GroupTimeLineMsg time_line_msg = 1;
    // 发送消息可选参数
    SendOption opt = 2;
}

message SendGroupImMsgResp {
    // 发送时间(毫秒)
    int64 sent_at = 1;
    // 消息唯一标识
    uint32 seq_id = 2;
}

message BatchGetGroupLastMsgListReq {
    repeated uint32 group_ids = 1;
}

message GroupLastMsg {
    uint32 group_id = 1;
    GroupTimeLineMsg time_line_msg = 2;
}

message BatchGetGroupLastMsgListResp {
    repeated GroupLastMsg group_msgs = 1;
}

message GetGroupMsgListReq {
    uint32 group_id = 1;
    uint32 seq_id = 2;

    // 一次拉取的数量
    uint32 limit = 3;
}

message GetGroupMsgListResp {
    repeated GroupTimeLineMsg time_line_msgs = 2;
}

message GroupTemplateInfo {
    uint32 id = 1;
    // 群名称
    string name = 2;
    // 群聊描述
    string character = 3;
    // 群标签
    repeated string tags = 4;
    // 群头像
    string avatar = 5;
    // 群性别0:女 1:男 2：其他
    int32 sex = 6;
    // 聊天页背景图
    string chat_background_img = 7;
    // 首页背景图
    string home_background_img = 8;
    // 群聊icon
    string group_icon = 9;
    // 是否展示/曝光到首页 true:展示 false:不展示
    bool exposed = 10;
    // 强插位置
    uint32 insert_pos = 11;
    // 角标 url
    string corner_icon = 12;
    // 群组所属分类ID
    string category_id = 13;
    // 配置点赞数
    int32 config_like_num = 14;

    message TemplateRole {
      uint32 role_id = 1;
      // 名称
      string name = 2;
      // 头像
      string avatar = 3;
      // 音色
      string timbre = 4;
      // 性别 0:女 1:男 2:其他
      int32 sex = 5;
      // 外显用，角色描述
      string display_character = 6;
      // 调用ai用，角色设定
      string ai_character = 7;
      // 调用ai用，角色群聊描述
      string chat_role_desc = 8;
      // 调用ai用，关系描述
      string relation_character = 9;

      // 群开场白
      message AIGroupPrologue {
        // 开场白文本内容
        string text = 1;
        // 开场白语音链接
        string audio = 2;
        // 开场白顺序
        uint32 priority = 3;
      }
      repeated AIGroupPrologue prologues = 10;


    }
    // 配置的角色id列表
    repeated TemplateRole template_roles = 15;
    // 展示在IMtab的标签
    string im_tab_tags = 16;
    // 类型，见aigc-group.proto GroupType
    uint32 group_type = 17;
    // 多人剧本玩法额外字段
    ScriptInfo script_info = 18;
    repeated int32 suitable_sex = 19; // 适合性别 0:女 1:男 2：其他

    repeated TemplateRole.AIGroupPrologue default_prologues = 20; // 默认开场白
}

// 用户扮演的角色信息
message PlayRole {
    uint32 id = 1; // 角色id
    string avatar = 2; // 角色头像
    string name = 3; // 角色名称
    repeated int32 suitable_sex = 4; // 适合性别 0:女 1:男 2：其他
    string character = 5; // 角色简介
}

message ScriptInfo {
    repeated PlayRole play_roles = 1; // 用户扮演的角色列表
    string button_display_text = 2; //  按钮文案
    string background_music = 3; // 背景音乐
    uint32 user_num = 4; // 剧本内的用户数量
    uint32 male_num = 6; // 男性用户数量
    uint32 female_num = 7; // 女性用户数量
    uint32 sort = 8; // 排序
    uint32 any_num = 9; // 不限男女的数量
}


// 创建群模板
message CreateGroupTemplateReq {

    GroupTemplateInfo group_template = 1;
}

message CreateGroupTemplateResp {
    uint32 template_id = 1;
}

// 更新群模板
message UpdateGroupTemplateReq {
    GroupTemplateInfo group_template = 1;
}

message UpdateGroupTemplateResp {
}

// 删除群模板
message DeleteGroupTemplateReq {
    uint32 id = 1;
}

message DeleteGroupTemplateResp {
}

// 根据id获取群模板列表
message GetGroupTemplateByIdReq {
    uint32 id = 1;
}

message GetGroupTemplateByIdResp {
    GroupTemplateInfo template_info = 1;
}

// 分页获取群模板
message GetGroupTemplateByPageReq {
    int64 page = 1;     // 页数
    int64 size = 2;     // 页面大小
    repeated uint32 group_type = 3; // 群模板类型，见aigc-group.proto GroupType
}

message GetGroupTemplateByPageResp {
    repeated GroupTemplateInfo list = 1;
    int64 total = 2;
}

// 目前只有角色专属句数有记录，其他的枚举先定
enum SentenceType {
    SENTENCE_TYPE_UNSPECIFIED = 0;
    SENTENCE_TYPE_ROLE_SPECIFIED = 1; // 角色专属句数
    SENTENCE_TYPE_CUR_DAY = 2; // 当日专属句数
    SENTENCE_TYPE_EXTRA = 3; // 额外句数
}

message Entity {
    enum Type {
      TYPE_UNSPECIFIED = 0;
      TYPE_PARTNER = 1;
      TYPE_MUTI_GROUP = 2; // 多人群聊
      TYPE_SINGLE_GROUP = 3; // 单人群聊
    }
    //e.g:partner_id
    uint32 id = 1;
    // 亲密度主体类型
    Type type = 2;
}

message GetSentenceAvailableCountReq {
    // 句子类型
    repeated SentenceType type = 1;
    Entity entity = 2;
    uint32 uid = 3;
}

message GetSentenceAvailableCountResp {
    message SentenceCount {
        uint32 config_num = 1;
        uint32 available_num = 2;
    }
    map<uint32, SentenceCount> available_count_map = 1; // key: SentenceType value: 剩余数量
}

message GetUserSentenceCountReq {
    uint32 uid = 1;
    SentenceType type = 2;
    Entity.Type entity_type = 3;
}

message GetUserSentenceCountResp {
    uint32 count = 1;
}

message IsReachLimitReq{
    uint32 uid = 1;
    Entity entity = 2; // 专属句数有效
    uint32 business_type = 3; // 见 aigc-trigger BusinessType
}

message IsReachLimitResp{
    bool is_reach = 1;
}

message ConsumeSentenceCountReq {
    uint32 uid = 1;
    Entity entity = 2; // 专属句数有效
    uint32 role_type = 3; // 见aigc-soulmate-middle AIRoleType
    uint32 business_type = 4; // 见 aigc-trigger BusinessType
}

message ConsumeSentenceCountResp {
    bool success = 1;
    bool need_special_tip = 2; // 发专属句数上限提示
    bool need_cur_day_tip = 3; // 发今日句数上限提示
    bool need_extra_tip = 4; // 发额外句数（所有句数）消耗完提示
    SentenceType used_type = 5; // 消耗的句子类型
}

// 发句数提示需要的参数
message GetTipsCountRequest {
    uint32 uid = 1; // 用户id
    Entity entity = 2; // 专属句数有效
    repeated uint32 sentence_type = 3; // deprecated
    uint32 business_type = 4; // 见 aigc-trigger BusinessType
    uint32 role_type = 5; // 见aigc-soulmate AIRoleType
    uint32 tip_type = 6; // 提示类型 见aigc-push.proto TipType
}

message GetTipsCountResponse {
    uint32 cur_day_cfg_num = 1; // 今日句数配置
    uint32 extra_available_num = 2; // 剩余的额外句数数量
    uint32 role_specified_cfg_num = 3; // 专属句数配置数量
    uint32 available_cur_day_num = 4; // 今日句数剩余数量
}