syntax = "proto3";

package aigc_common;

option go_package = "golang.52tt.com/protocol/services/aigc/aigc-common";
import "tt/quicksilver/aigc/aigc-common/aigc-common.proto";

// 点赞事件
message AigcAttitudeEvent {
  enum Action {
    ACTION_INVALID = 0;
    ACTION_LIKE = 1; // 点赞
    ACTION_UNLIKE = 2; // 取消点赞
  }
  enum ObjectType {
    OBJECT_TYPE_UNSPECIFIED = 0;
    OBJECT_TYPE_GROUP_TEMPLATE = 1;
  }
  uint32 object_type = 1;
  uint32 object_id = 2;
  Action action = 3;
  int64  happen_time = 4;
  uint32 user_id = 5; // 点赞人uid
}

// 延时消息事件
message DelayMsgEvent {
  aigc_common.PushTo push_to = 1; // 业务类型
  bytes push_req = 2; // 消息请求体
  int64 should_send_time = 3; // 应该发送的时间戳
  bytes receive_msg = 4; // 收到的用户消息见 web_im_logic.proto ImMsg 用proto unmarshal
}