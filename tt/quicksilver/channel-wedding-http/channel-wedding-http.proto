syntax = "proto3";

package channel_wedding_http;
option go_package = "channel-wedding-http";


// 获取我的预约信息  url: /tt-revenue-http-logic/channel_wedding/get_my_wedding_reserve_info
message GetMyWeddingReserveInfoRequest {
  uint32 wedding_plan_id = 1; // 婚礼计划ID
}

message GetMyWeddingReserveInfoResponse {
  uint32 reserve_date = 1; // 预约日期
  uint32 channel_id = 2; // 房间ID
  repeated string reserve_time_section = 3; // 预约时段信息
  uint32 remain_change_times = 4; // 剩余修改次数
  uint32 change_limit_time = 5; // 修改限制时间, 婚礼开始前x小时不能修改预约
  bool   in_change_time = 6; // 是否在可修改时间内
}

// 获取可预约信息 url: /tt-revenue-http-logic/channel_wedding/get_wedding_reserve_info
message GetWeddingReserveInfoRequest {
  uint32 theme_type = 1; // 主题类型 1:免费 2:付费
  uint32 reserve_date = 2; // 预约日期
  uint32 channel_id = 3; // 房间ID
  uint32 theme_id = 4; // 主题ID
}

// 预约房间信息
message WeddingChannelInfo {
  uint32 channel_id = 1; // 房间ID
  string channel_name = 2; // 房间名称
  uint32 guild_id = 3; // 公会id
  string manager_ttid = 4; // 管理员ttid
  string manager_nickname = 5; // 管理员昵称
}

// 预约时段信息
message ReserveTimeInfo {
  string reserve_time_section = 1; // 预约时段
  bool is_fully_reserved = 2; // 是否已满
  UserProfile groom = 3; // 新郎信息
  UserProfile bride = 4; // 新娘信息
  bool is_hot = 5; // 是否热门
}

message GiftInfo {
  uint32 gift_id = 1; // 礼物ID
  string gift_name = 2; // 礼物名称
  string gift_desc = 3; // 礼物描述
  string gift_icon = 4; // 礼物图标
  uint32 worth = 5; // 礼物价值
  uint32 buy_price = 6; // 购买价格
}

message GetWeddingReserveInfoResponse {
  uint32 cur_channel_id = 1; // 当前房间ID
  uint32 cur_reserve_date = 2; // 当前预约日期
  repeated WeddingChannelInfo reserveable_channel_list = 3; // 可预约房间列表
  repeated ReserveTimeInfo reserve_time_info_list = 4; // 预约时段信息
  uint32 min_reserve_date = 5; // 最小预约日期
  uint32 max_reserve_date = 6; // 最大预约日期
  uint32 max_reserve_num = 7; // 最大预约数量
  repeated GiftInfo hot_time_gift_list = 8; // 热门时段礼物列表
  repeated GiftInfo normal_time_gift_list = 9; // 非热门房间礼物列表
  LabelInfo hot_label = 10; // 热门标签
}

message LabelInfo {
  string label_name = 1; // 标签名称
  string label_icon = 2; // 标签图标
  string label_desc = 3; // 标签ID
}

// 保存预约信息 url: /tt-revenue-http-logic/channel_wedding/save_wedding_reserve
message SaveWeddingReserveRequest {
  uint32 wedding_plan_id = 1; // 婚礼计划ID
  uint32 reserve_date = 2; // 预约日期
  uint32 channel_id = 3; // 房间ID
  repeated string reserve_time = 4; // 预约时段, 连续
}

message SaveWeddingReserveResponse {}

// 修改预约信息 url: /tt-revenue-http-logic/channel_wedding/change_wedding_reserve
message ChangeWeddingReserveRequest {
  uint32 wedding_plan_id = 1; // 婚礼计划ID
  uint32 reserve_date = 2; // 预约日期
  uint32 channel_id = 3; // 房间ID
  repeated string reserve_time = 4; // 预约时段, 连续
}

message ChangeWeddingReserveResponse {}

message UserProfile {
  uint32 uid = 1;
  string account = 2; //账号
  string nickname = 3; // 昵称
  string account_alias = 4; // 靓号，预留字段，暂时未赋值
  uint32 sex = 5; //用户性别

  string head_img_md5 = 6; //用户头像md5，如果有填该值，客户端必须用该值加上account去获取用户头像，避免获取头像回源到服务端
  string head_dy_img_md5 = 7; // 动态头像
}

message WeddingGuestInfo {
  UserProfile user_profile = 1; // 用户信息
  uint32 invite_uid = 2; // 邀请用户ID
  uint32 fellow_val = 3; // 挚友值
}

// 获取伴郎伴娘信息 url: /tt-revenue-http-logic/channel_wedding/get_groomsman_and_bridesmaid_info
message GetGroomsmanAndBridesmaidInfoRequest {
  uint32 wedding_plan_id = 1; // 婚礼方案ID
}

message GetGroomsmanAndBridesmaidInfoResponse {
  repeated WeddingGuestInfo bridesmaid_list = 1; // 伴娘列表
  uint32 max_bridesmaid_num = 2; // 最大伴娘数量
  repeated WeddingGuestInfo groomsman_list = 3; // 伴郎列表
  uint32 max_groomsman_num = 4; // 最大伴郎数量
  repeated WeddingGuestInfo invited_list = 5; // 已邀请列表
  repeated WeddingGuestInfo agreed_list = 6; // 已接受列表
  repeated WeddingGuestInfo refused_list = 7; // 已拒绝列表
}

// 获取亲友团信息 url: /tt-revenue-http-logic/channel_wedding/get_wedding_friend_info
message GetWeddingFriendInfoRequest {
  uint32 wedding_plan_id = 1; // 婚礼方案ID
}

message GetWeddingFriendInfoResponse {
  repeated WeddingGuestInfo friend_list = 1; // 亲友团列表
  repeated WeddingGuestInfo invited_list = 2; // 已邀请列表
  repeated WeddingGuestInfo agreed_list = 3; // 已接受列表
  repeated WeddingGuestInfo refused_list = 4; // 已拒绝列表
  uint32 max_friend_num = 5;                  // 最大亲友团人数
}

// 获取婚礼邀请列表 url: /tt-revenue-http-logic/channel_wedding/get_play_mate_list
message GetPlaymateListRequest {
  uint32 wedding_plan_id = 1; // 婚礼方案ID
  uint32 page_num = 2; // 页码
  uint32 page_size = 3; // 每页数量
  string account = 4; // 根据账号搜索
}

message GetPlaymateListResponse {
  repeated WeddingPlaymateInfo playmate_list = 2; // 挚友列表
}

message WeddingPlaymateInfo {
  UserProfile user_profile = 1; // 用户信息
  uint32 playmate_status = 2; // 状态, 1: 未邀请, 2: 已邀请, 3: 已接受, 4: 已拒绝
  uint32 fellow_val = 3; // 挚友值
}

enum WeddingGuestType {
  UNKNOWN = 0;
  GROOMSMAN = 1;
  BRIDESMAID = 2;
  FRIENDS = 3;
}

// 邀请嘉宾 url: /tt-revenue-http-logic/channel_wedding/invite_wedding_guest
message InviteWeddingGuestRequest {
  uint32 wedding_plan_id = 1; // 婚礼计划ID
  uint32 wedding_guest_type = 2; // 嘉宾类型, see WeddingGuestType
  uint32 target_uid = 3; // 用户ID
  bool is_cancel = 4; // 是否取消邀请
}

message InviteWeddingGuestResponse {}

// 删除嘉宾 url: /tt-revenue-http-logic/channel_wedding/del_wedding_guest
message DelWeddingGuestRequest {
  uint32 wedding_plan_id = 1; // 婚礼计划ID
  uint32 wedding_guest_type = 2; // 嘉宾类型, see WeddingGuestType
  uint32 target_uid = 3; // 用户ID
}

message DelWeddingGuestResponse {}

// 获取基础信息 url: /tt-revenue-http-logic/channel_wedding/get_wedding_plan_base_info
message GetWeddingPlanBaseInfoRequest {}

message GetWeddingPlanBaseInfoResponse {
  bool has_mate = 1; // 有无对象
  bool is_pay = 2; // 是否付费
  bool is_reserve = 3; // 是否预约
}

// 房间内获取婚礼预约列表 url: /tt-revenue-http-logic/channel_wedding/get_inner_channel_wedding_reserved_list
message GetInnerChannelWeddingReservedListRequest {
  uint32 channel_id = 1; // 房间ID
  uint32 page_num = 2; // 页码
  uint32 page_size = 3; // 每页数量
}

message InnerChannelWeddingReservedInfo {
  UserProfile groom = 1; // 新郎信息
  UserProfile bride = 2; // 新娘信息
  string theme_name = 3; // 主题名称
  uint32 reserve_start_time = 4; // 预约开始时间
  uint32 reserve_end_time = 5; // 预约结束时间
}

message GetInnerChannelWeddingReservedListResponse {
  repeated InnerChannelWeddingReservedInfo reserved_list = 1; // 预约列表
}


// 获取婚礼爱侣榜单 url: /tt-revenue-http-logic/channel_wedding/get_wedding_rank
message GetWeddingRankRequest {
    uint32 cid = 1; // 房间ID
}

message WeddingRankInfo {
    UserProfile groom = 1; // 新郎信息
    UserProfile bride = 2; // 新娘信息
    string theme_name = 3; // 婚礼名称
    string theme_bg = 4; // 婚礼背景图
    uint32 happiness_val = 5; // 幸福值
}

message GetWeddingRankResponse {
    repeated WeddingRankInfo rank_list = 1; // 榜单列表
    string rank_desc = 2; // 榜单排名说明
}

// 客服安排婚礼预约 url: /tt-revenue-http-logic/channel_wedding/arrange_wedding_reserve
message ArrangeWeddingReserveRequest {
  uint32 channel_id = 1; // 房间ID
  uint32 reserve_date = 5; // 预约日期
  repeated string reserve_time = 2; // 预约时段, 连续
  bool is_hot = 3; // 是否热门
  uint32 gift_id = 4; // 礼物id
  uint32 source_msg_id = 6; // 原消息id
  uint32 theme_id = 7; // 主题id
  uint32 target_uid = 8; // 目标用户ID
}

message ArrangeWeddingReserveResponse {
}

// 咨询婚礼预约 url: /tt-revenue-http-logic/channel_wedding/consult_wedding_reserve
message ConsultWeddingReserveRequest {
  uint32 channel_id = 1; // 房间ID
  uint32 reserve_date = 2; // 预约日期
  repeated string reserve_time = 3; // 预约时段, 连续
  uint32 theme_id = 4; // 主题id
  string manager_ttid = 5; // 客服ttid
}

message ConsultWeddingReserveResponse {
}

enum ThemeType {
  THEME_TYPE_UNKNOWN = 0;
  THEME_TYPE_FREE = 1; // 免费
  THEME_TYPE_PAY = 2; // 付费
}


// 获取可预约信息 url: /tt-revenue-http-logic/channel_wedding/get_channel_reserved_info
message GetChannelReservedInfoRequest {
  uint32 reserve_date = 1; // 预约日期
  uint32 channel_id = 2; // 房间ID
  uint32 theme_type = 3; // 主题类型 1.免费 2.付费 see ThemeType
}

message GetChannelReservedInfoResponse {
  repeated ChannelReserveTimeInfo reserve_time_info_list = 1; // 预约时段信息
  uint32 min_reserve_date = 2; // 最小可预约日期
  uint32 max_reserve_date = 3; // 最大可预约日期
  bool is_guild = 4; // 是否公会视角
}

// 预约时段信息
message ChannelReserveTimeInfo {
  string reserve_time = 1; // 预约时段, 连续
  UserProfile groom = 2; // 新郎信息w
  UserProfile bride = 3; // 新娘信息
  bool is_hot = 4; // 是否热门
  string theme_name = 5; // 主题名称
}

// 获取婚礼大屏信息 url: /tt-revenue-http-logic/channel_wedding/get_wedding_big_screen_info
message GetWeddingBigScreenInfoRequest {
  uint32 wedding_plan_id = 1; // 婚礼id
}

message GetWeddingBigScreenInfoResponse {
  uint32 max_num = 1; // 最大数量
  repeated BigScreenItem big_screen_list = 2; // 大屏图片列表
}

enum ReviewStatus {
  REVIEW_STATUS_REVIEWING = 0; // 审核中
  REVIEW_STATUS_PASS = 1; // 通过
  REVIEW_STATUS_REJECT = 2; // 拒绝
}

message BigScreenItem {
  string img_url = 1;
  uint32 review_status = 2;
  uint32 upload_by_uid = 3;
}

// 是否展示资料卡片 url: /tt-revenue-http-logic/channel_wedding/is_show_info_card
message IsShowInfoCardRequest {
  uint32 target_uid = 1; // 用户ID
  uint32 channel_id = 2; // 房间ID
}

message IsShowInfoCardResponse {
  bool is_show = 1; // 是否展示资料卡片
}
