syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-recommend-svr";
package channelrecommend;


service ChannelRecommendSvr {
    // 获取流量卡限额配置列表
    rpc GetFlowCardLimitConfList(GetFlowCardLimitConfListReq) returns (GetFlowCardLimitConfListResp) {}

    //增加流量卡限额配置
    rpc AddFlowCardLimitConf(AddFlowCardLimitConfReq) returns (AddFlowCardLimitConfResp) {}

    //更新流量卡限额配置
    rpc UpdateFlowCardLimitConf(UpdateFlowCardLimitConfReq) returns (UpdateFlowCardLimitConfResp) {}

    //删除流量卡限额配置
    rpc DelFlowCardLimitConf(DelFlowCardLimitConfReq) returns (DelFlowCardLimitConfResp) {}

    // 获取流量卡列表
    rpc GetGrantFlowCardList(GetGrantFlowCardListReq) returns (GetGrantFlowCardListResp) {}

    // 发放流量卡
    rpc GrantFlowCard(GrantFlowCardReq) returns (GrantFlowCardResp) {}

    // 批量发放流量卡
    rpc BatGrantFlowCard(BatGrantFlowCardReq) returns (BatGrantFlowCardResp) {}

    //回收流量卡
    rpc ReclaimGrantedFlowCard(ReclaimGrantedFlowCardReq) returns (ReclaimGrantedFlowCardResp) {}

    //禁用流量卡
    rpc BanGrantedFlowCard(BanGrantedFlowCardReq) returns (BanGrantedFlowCardResp) {}

    // 获取小时流量卡使用的剩余数量
    rpc GetFlowCardHourRemainCnt(GetFlowCardHourRemainCntReq) returns (GetFlowCardHourRemainCntResp) {}

    // 主播或者公会直接使用流量卡
    rpc UseFlowCard(UseFlowCardReq) returns (UseFlowCardResp) {}

    // 公会发放主播流量卡
    rpc GrantAnchorFlowCardByGuild(GrantAnchorFlowCardByGuildReq) returns (GrantAnchorFlowCardByGuildResp) {}

    // 根据类型获取公会或者主播的流量卡列表, 现在只支持主播的
    rpc GetFlowCardListByType(GetFlowCardListByTypeReq) returns (GetFlowCardListByTypeResp) {}

    // 查询所有使用流量卡的主播
    rpc GetAllUseFlowCardAnchor(GetAllUseFlowCardAnchorReq) returns (GetAllUseFlowCardAnchorResp) {}

    // 获取抽奖房间推荐列表
    rpc GetRecLotteryChList(GetRecLotteryChListReq) returns (GetRecLotteryChListResp) {}

    // 根据id获取抽奖房间推荐信息
    rpc GetLotteryChannelRecInfo(GetLotteryChannelRecInfoReq) returns (GetLotteryChannelRecInfoResp) {}

    // 获取推荐房间列表
    rpc GetRecommendChannel(GetRecommendChannelReq) returns (GetRecommendChannelResp) {}

    // 新版推荐房间列表
    rpc GetRecommendChannelV2(GetRecommendChannelV2Req) returns (GetRecommendChannelV2Resp){}

    // 根据TAGID-获取推荐房间列表
    rpc GetChannelByTagId(GetChannelByTagIdReq) returns (GetChannelByTagIdResp) {}
    // 根据个性tagid请求房间数据
    rpc GetRecChListByPerTagId(GetRecChListByPerTagIdReq) returns (GetRecChListByPerTagIdResp) {}

    // 获取推荐库列表
    rpc GetPrepareChannelList(GetPrepareChannelListReq) returns (GetPrepareChannelListResp) {}

    // 设置推荐库房间信息
    rpc SetPrepareChannel(SetPrepareChannelReq) returns (SetPrepareChannelResp) {}

    //删除推荐库房间信息
    rpc DelPrepareChannel(DelPrepareChannelReq) returns (DelPrepareChannelResp) {}

    // 获取推荐库备份列表
    rpc GetPrepareBackupList(GetPrepareBackupListReq) returns (GetPrepareBackupListResp) {}

    // 获取操作记录列表
    rpc GetPrepareOperRecordList(GetPrepareOperRecordListReq) returns (GetPrepareOperRecordListResp) {}

    // 娱乐tab封面配置
    rpc GetDisplaySceneInfoList(GetDisplaySceneInfoListReq) returns (GetDisplaySceneInfoListResp) {}
    rpc AddDisplaySceneInfo(AddDisplaySceneInfoReq) returns (AddDisplaySceneInfoResp) {}
    rpc DelDisplaySceneInfo(DelDisplaySceneInfoReq) returns (DelDisplaySceneInfoResp) {}

    // 获取娱乐tab封面配置
    rpc GetQuickEntryConfigList(GetQuickEntryConfigListReq) returns (GetQuickEntryConfigListResp) {}

    // 批量获取直播间的歌手标签
    rpc BatGetChannelSoundLabel(BatGetChannelSoundLabelReq) returns (BatGetChannelSoundLabelResp) {}

    //触发定时任务
    rpc TriggerTimer(TriggerTimerReq) returns (TriggerTimerResp) {}

    //根据短链中的TagId获取快速进度推荐房间id
    rpc GetQuickRecChannelByTagId (GetQuickRecChannelByTagIdReq) returns (GetQuickRecChannelByTagIdResp) {}


    // 获取用户roi信息
    rpc GetUserRoiInfo(GetUserRoiInfoReq) returns (GetUserRoiInfoResp) {}
    //确认高潜付费用户弹窗显示
    rpc ConfirmRoiHighPotentail(ConfirmRoiHighPotentailReq) returns (ConfirmRoiHighPotentailResp) {}

    // 房间组资源位配置列表
    // 获取房间组资源位配置列表
    rpc GetChannelGroupResourceList(GetChannelGroupResourceListReq) returns (GetChannelGroupResourceListResp) {}
    // 增加房间组资源位配置
    rpc AddChannelGroupResource(AddChannelGroupResourceReq) returns (AddChannelGroupResourceResp) {}
    // 更新房间组资源位配置
    rpc UpdateChannelGroupResource(UpdateChannelGroupResourceReq) returns (UpdateChannelGroupResourceResp) {}
    // 删除房间组资源位配置
    rpc DelChannelGroupResource(DelChannelGroupResourceReq) returns (DelChannelGroupResourceResp) {}
    // 检查房间组资源位配置有效性
    rpc CheckChannelGroupResource(CheckChannelGroupResourceReq) returns (CheckChannelGroupResourceResp) {}
    //批量获取房间的大礼物信息
    rpc BatchGetChannelBigGiftInfo(BatchGetChannelBigGiftInfoReq) returns (BatchGetChannelBigGiftInfoResp) {}

    //设置开关
    rpc SetRevenueSwitchHub(SetRevenueSwitchHubReq) returns (SetRevenueSwitchHubResp) {}
    //获取单个用户开关
    rpc GetRevenueSwitchHub(GetRevenueSwitchHubReq) returns (GetRevenueSwitchHubResp) {}
    //批量获取用户开关
    rpc BatchGetRevenueSwitchHub(BatchGetRevenueSwitchHubReq) returns (BatchGetRevenueSwitchHubResp) {}

    // 获取优质用户信息
    rpc GetUserQualityInfo(GetUserQualityInfoReq) returns (GetUserQualityInfoResp) {}

    //确认壕用户弹窗显示
    rpc ConfirmQualityHighPop(ConfirmQualityHighPopReq) returns (ConfirmQualityHighPopResp) {}

    //获取顶部浮窗房间信息
    rpc GetTopWinChannelInfo(GetTopWinChannelInfoReq) returns (GetTopWinChannelInfoResp){}

    //清掉某个用户的顶部浮窗过滤数据
    rpc CleanTopWinFilter(CleanTopWinFilterReq) returns (CleanTopWinFilterResp){}

    // 获取快速进房权重配置列表
    rpc GetQuickWeightConfList(GetQuickWeightConfListReq) returns (GetQuickWeightConfListResp) {}

    // 获取标签配置
    rpc GetTagConfigInfoList(GetTagConfigInfoListReq) returns (GetTagConfigInfoListResp) {}

    // 获取快速进房在线房间列表
    rpc GetQuickRecChannelList(GetQuickRecChannelListReq) returns (GetQuickRecChannelListResp) {}

    // 获取顶部浮窗房间
    rpc GetTopOverLayChannel(GetTopOverLayChannelReq) returns (GetTopOverLayChannelResp) {}

    // 获取推荐列表房间反馈配置
    rpc GetRecFeedbackConfig(GetRecFeedbackConfigReq) returns (GetRecFeedbackConfigResp) {}

    // 推荐房间反馈
    rpc DoRecFeedback(DoRecFeedbackReq) returns (DoRecFeedbackResp) {}

    // 过滤推荐房间反馈
    rpc FilteringRecFeedBack(FilteringRecFeedBackReq) returns (FilteringRecFeedBackResp) {}

    // 检查用户是否能看到婚礼房
    rpc CheckUidIsInWeddingGroup(CheckUidIsInWeddingGroupReq) returns (CheckUidIsInWeddingGroupResp) {}
}


// 推荐等级
enum RecommendLevel
{
    Recommend_Invalid = 0;
    Recommend_Level_S = 1;   // 超级推荐
    Recommend_Level_A = 2;   // 热门推荐
    Recommend_Level_B = 3;   // 普通推荐
}

// 流量卡限额配置
message LevelConf {
   uint32 level = 1;    // see  RecommendLevel
   uint32 cnt = 2;    // 数额
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message LimitConf {
   uint32 hourCnt = 1;   // 一天的第几个小时 从 0 开始
   repeated LevelConf conf_list = 2;
}

message FlowCardLimitConf {
   uint32 id = 1;
   uint32 begin_ts = 2;
   uint32 end_ts = 3;
   repeated LimitConf conf_list = 4;
}

message GetFlowCardLimitConfListReq {
   uint32 page = 1; // 从 1开始
   uint32 page_size = 2;
   uint32 begin_ts = 3;  // 不为0时根据时间段筛选
   uint32 end_ts = 4;
}
message GetFlowCardLimitConfListResp {
   repeated FlowCardLimitConf conf_list = 1;
   uint32 next_page = 2;  // 0 表示结束
   uint32 total_cnt = 3;
}

//增加流量卡限额配置
message AddFlowCardLimitConfReq {
   FlowCardLimitConf conf = 1;
}
message AddFlowCardLimitConfResp {
}

//更新流量卡限额配置
message UpdateFlowCardLimitConfReq {
   FlowCardLimitConf conf = 1;
}
message UpdateFlowCardLimitConfResp {
}

//删除流量卡限额配置
message DelFlowCardLimitConfReq {
   uint32 id = 1;
}
message DelFlowCardLimitConfResp {
}

// 发放类型
enum FlowCardGrantType {
    GrantGuild = 0;  // 公会
    GrantAnchor = 1; // 主播
}

message FlowCardGrantInfo {
    uint32 grant_id = 1;
    uint32 id = 2;  // 公会或者主播id
    uint32 level = 3; // see RecommendLevel
    uint32 expirt_ts = 4;
    uint32 cnt = 5;  // 数量
    string remark = 6;  // 备注
    uint32 ban_begin_ts = 7;  // 禁用开始时间
    uint32 ban_end_ts = 8;  // 禁用结束时间
    uint32 used_cnt = 9;  // 使用数量
    uint32 update_ts = 10;
    uint32 create_ts = 11;
    uint32 guild_grant_id = 12; // 公会发放id
    string order_id = 13;
}

// 获取流量卡列表 
message GetGrantFlowCardListReq {
   uint32 grant_type = 1;  // see FlowCardGrantType
   uint32 id= 2;   // id不为0，指定id查询
   uint32 page = 3;   // 从1开始
   uint32 page_size = 4;
   uint32 guild_grant_id = 5;  // 公会发放id
   uint32 level = 6;   // see RecommendLevel
}
message GetGrantFlowCardListResp {
   repeated FlowCardGrantInfo  info_list = 1;
   uint32  next_page = 2;  // 为0 结束
   uint32  total_cnt = 3;
}

// 发放流量卡
message GrantFlowCardReq {
   uint32  grant_type = 1;  // see FlowCardGrantType
   FlowCardGrantInfo info = 2;
}
message GrantFlowCardResp {
}

message GrantErrorMsg {
   string order_id = 1;
   int32  err_code = 2;
   string err_msg = 3;
   uint32 id = 4;  // 主播id或者公会id
}

// 批量发放流量卡
message BatGrantFlowCardReq {
   uint32  grant_type = 1;  // see FlowCardGrantType
   repeated FlowCardGrantInfo info_list = 2;
}
message BatGrantFlowCardResp {
   repeated GrantErrorMsg err_list = 1; // 失败列表
}

// 回收流量卡
message ReclaimGrantedFlowCardReq {
   uint32 grant_type = 1;  // see FlowCardGrantType
   uint32 grant_id = 2;
   uint32 reclaim_cnt = 3;  // 回收数量
}
message ReclaimGrantedFlowCardResp {
}

// 禁用流量卡
message BanGrantedFlowCardReq {
   uint32 grant_type = 1;  // see FlowCardGrantType
   uint32 grant_id = 2;
   uint32 begin_ts = 3;
   uint32 end_ts = 4;
}
message BanGrantedFlowCardResp {
}

// 获取小时流量卡使用的剩余数量
message GetFlowCardHourRemainCntReq {
    uint32 ts = 1;
    uint32 level = 2;   // see RecommendLevel
}
message GetFlowCardHourRemainCntResp {
    uint32 remain_cnt = 1; // 剩余数量
}

// 主播或者公会直接使用流量卡
message UseFlowCardReq {
    uint32 grant_id = 1;
    uint32 anchor_uid = 2;
    uint32 begin_ts = 3;
    uint32 guild_id = 4;
}
message UseFlowCardResp {
}


// 公会发放主播流量卡
message GrantAnchorFlowCardByGuildReq {
   uint32 guild_id = 1;
   uint32 grant_id = 2;
   repeated FlowCardGrantInfo info_list = 3;
}
message GrantAnchorFlowCardByGuildResp {
   repeated GrantErrorMsg err_list = 1; // 失败列表
}

// 根据类型获取公会或者主播的流量卡列表, 现在只支持主播的
message GetFlowCardListByTypeReq {
   enum GetType {
      NotUseAll = 0;   // 没有使用完
   }
   uint32 grant_type = 1; // FlowCardGrantType
   uint32 type = 2;  // see GetType
   uint32 level = 3;
   uint32 id = 4;  // 公会或者主播id
}
message GetFlowCardListByTypeResp {
   repeated FlowCardGrantInfo info_list = 1;
}


// 查询所有使用流量卡的主播
message GetAllUseFlowCardAnchorReq {
   uint32 ts = 1;
}
message GetAllUseFlowCardAnchorResp {
   map<uint32, uint32> map_cid_lv = 1;
}

// 奖品信息 
message GiftInfo {
   uint32 gift_id = 1;
   string gift_name = 2;
   string gift_icon = 3;  // 礼物图
   uint32 gift_price = 4;  // 礼物数量
   uint32 gift_price_type = 5;  // 礼物价格类型
}

// 推荐抽奖房间信息
message RecLotteryChInfo {
    uint32 channel_id = 1;
    uint32 lottery_end_ts = 2;  // 抽奖结束时间
    uint32 award_cnt = 3;  // 奖品数量
    GiftInfo gift_info = 4;  // 奖品信息

}

// 获取抽奖房间推荐列表
message GetRecLotteryChListReq {
   uint32 page = 1;  // 从 1开始
   uint32 page_size = 2;
}
message GetRecLotteryChListResp {
   repeated RecLotteryChInfo ch_list = 1;
   uint32 next_page = 2;  // 为0表示结束
}

// 根据id获取抽奖房间推荐信息
message GetLotteryChannelRecInfoReq {
   uint32 channel_id = 1;
}
message GetLotteryChannelRecInfoResp {
   bool is_rec = 1;  // 是否推荐
}

enum UserCategory
{
	NewUser_Type = 0;
	OldUser_Type = 1;
	BothUser_Type = 2;   // 所有类型
	QuickEnterUser_Type = 3;   // 快速进房用户类型
}

enum ChannelCategory
{
	Activity_BIG = 0; //大活动房  官方活动房
	Activity_SAMLL = 1; //小活动房  公会活动房
	Used_Enter = 2; //曾经进入过
	Normal_Level = 3; //普通等级房
	Hot_Channel  = 4; //热门房
	Roi_Channel = 5;  // roi承接房间
  Guild_Channel = 6;     //公会房区域
  Relationship_Channel = 7; //关注链房
  HighQualityPgc_Channel = 8; //指定优质PGC房
}

enum ChannelLevel
{
	Channel_Invalid = 0;
	Channel_Level_S = 1;
	Channel_Level_A = 2;
	Channel_Level_B = 3;
	Channel_Level_C = 4;
	Channel_Level_D = 5;
	Channel_Level_E = 6;
	Channel_Level_F = 7;
	Channel_Level_G = 8;
}

//房间关系链类型
enum RecommendRelationshipType {
  RECOMMMENT_RELATIONSHIP_TYPE_NONE = 0;           //无关系
  RECOMMMENT_RELATIONSHIP_TYPE_FRIEND_MIC = 1;     //玩伴在麦
  RECOMMMENT_RELATIONSHIP_TYPE_FRIEND_CHANNEL = 2; //玩伴在房
  RECOMMMENT_RELATIONSHIP_TYPE_COLLECT = 3;        //我的收藏
  RECOMMMENT_RELATIONSHIP_TYPE_FOLLOW = 4;        //我的关注
  RECOMMMENT_RELATIONSHIP_TYPE_USED_ENTER = 5;    //曾经来过
}

message TimeSection
{
   uint32 begin_time = 1;
   uint32 end_time = 2;
}


//推荐流房间信息
message ChannelRecommendSimpleInfo
{
    uint32 channel_id = 1;
    uint32 category = 2;  //enum ChannelCategory

    uint32 tag_id   = 3;  //tag_id
    string sub_tag  = 4;  //配置的标签，TOP10什么鬼的

    uint32 channel_level = 5; //enum ChannelLevel
    uint32 score = 6;

    string tag_name = 7;
    string label = 8;

    string roi_url  = 9;  //roi承接房间标识

    uint32 relation_ship = 10;  //关系链类型RecommendRelationshipType. category=Relationship_Channel 时有值

    uint32 roi_url_type = 11;  // roi承接房间标识类型 RoiChannelCertType

    uint32 rec_poor_source = 12; // 推荐池来源 see channel_.proto ERecPoorSourceV2
}

enum RoiChannelCertType {
  // roi承接房间标识定义，移位定义
  RoiChannelCertTypeNone = 0;  // 无
  RoiChannelCertTypeFriend = 1; // 玩伴在麦上
  RoiChannelCertTypeFollow = 2 ; // 关注
  RoiChannelCertTypeCollect = 4; // 收藏
  RoiChannelCertTypeNew = 8; // 新人
}

message GetRecommendChannelReq
{
	uint32 user_category = 1; //enum UserCategory
	uint32 start = 2;
    uint32 count = 3;
    uint32 home_type = 4;   //首页类型  see channel_.proto HomeType
    uint32 uid = 5;
    string device_id = 6;   //用于roi用户判断
}

message GetRecommendChannelResp
{
	repeated ChannelRecommendSimpleInfo channel_list = 1;
    bool is_end = 2; //是否到头了
}

message GetRecommendChannelV2Req
{
  uint32 uid = 1;
  uint32 user_category = 2; //enum UserCategory
  uint32 start = 3;
  uint32 count = 4;
  string device_id = 5;   //用于roi用户判断
  int32  sex = 6;
}

message GetRecommendChannelV2Resp
{
  repeated ChannelRecommendSimpleInfo channel_list = 1;
  bool is_end = 2; //是否到头了
}

// 推荐库配置类型
enum PrepareType
{
    Prepare_Type_Invalid = 0;  // 无效
    Prepare_Type_Manual = 1;  // 手动
    Prepare_Type_Auto = 2;  //自动
}

//推荐库信息
message PrepareChannelInfo
{
	uint32 channel_id = 1;
	uint32 tag_id = 2;
    uint32 new_level = 3; //新用户等级 enum ChannelLevel
	TimeSection new_section = 4;  // 新用户等级生效时间段
	uint32 old_level = 5; //旧玩家等级 enum ChannelLevel
	TimeSection old_section = 6;  // 旧用户等级生效时间段
	uint32 quick_level = 7;  // 快速进房入口等级 enum ChannelLevel
	TimeSection quick_section = 8; // 快速进房入口等级生效时间
    uint32 update_ts = 9; //更新时间
    uint32 prepare_type = 10; // 配置类型 see PrepareType
    uint32 id = 11;  // 记录id
    string operator = 12;  // 操作人
}

// 获取推荐库列表
message GetPrepareChannelListReq{
    uint32 channel_id = 1;  // 指定房间查询
    uint32 prepare_type = 2; // 指定配置类型 see PrepareType
}
message GetPrepareChannelListResp{
  repeated PrepareChannelInfo prepare_channel_list = 1;
}

// 设置推荐库房间信息
message SetPrepareChannelReq {
   repeated PrepareChannelInfo info_list = 1;
   string operator = 2;  // 操作人
}
message SetPrepareChannelResp {
}

//删除推荐库房间信息
message DelPrepareChannelReq {
   uint32 channel_id = 1;
   string operator = 2;  // 操作人
}
message DelPrepareChannelResp {
}

// 获取推荐库备份列表
message GetPrepareBackupListReq{
   int64 version = 1;  // 版本号
}
message GetPrepareBackupListResp{
  repeated PrepareChannelInfo info_list = 1;
}


// 推荐库操作类型
enum PrepareOperType
{
    Prepare_Oper_Type_Invalid = 0;  // 无效
    Prepare_Oper_Type_Add = 1;  // 增加
    Prepare_Oper_Type_Update = 2;  //更新
    Prepare_Oper_Type_Del = 3;  //删除
}

// 房间推荐信息
message PrepareOperRecord {
   uint32 oper_type = 1; // 操作类型 see PrepareOperType
   PrepareChannelInfo info =2;
}

// 获取操作记录列表
message GetPrepareOperRecordListReq {
   uint32 page = 1; // 从1开始
   uint32 page_size = 2; // 页数
}
message GetPrepareOperRecordListResp {
   repeated PrepareOperRecord record_list = 1;
   uint32 total_cnt = 2; // 总数
}


// 配置状态
enum DisplaySceneStatusType
{
    Display_Scene_Status_Invalid = 0;  // 无效
    Display_Scene_Status_Future = 1;  // 未生效
    Display_Scene_Status_Valid = 2;  // 生效中
    Display_Scene_Status_Expire = 3;  // 过期
}

// 配置时间类型
enum DisplaySceneTimeType
{
    Display_Scene_Time_Invalid = 0;  // 无效
    Display_Scene_Time_Regular = 1;  // 固定时间生效
    Display_Scene_Time_Forever = 2;  // 永久生效
}

// 快速进房豆腐块配置
message DisplaySceneInfo {
   uint32 id = 1;  // 记录id
   uint32 type = 2;  //配置类型 see channel_.proto EQuickEntryType
   string title = 3;  // 标题
   uint32 scene_id = 4;  //位置id
   repeated uint32 market_id_list = 5; // 产品id列表
   uint32 platform = 6;   // 平台 0 all   1 android  2 ios
   string min_version = 7;  // 客户端最小版本 空表示不限制版本
   uint32 begin_ts = 8; // 开始时间
   uint32 end_ts = 9;  // 结束时间
   uint32 scene_type = 10;  //场景配置类型 see  channel_.proto EEntrySceneType
   uint32 tag_id = 11;  // 标签id
   string jump_url = 12;  // 活动跳转链接
   uint32 weight = 13;  // 权重排序
   string bg_img = 14;   // 背景图
   string small_img = 15;  // 小图
   string static_img = 16;  // 静态图
   string dynamic_url = 17;  // 动效资源url
   string dynamic_md5 = 18;  // 动效资源md5
   string dynamic_json_key = 19;  // 动效json key
   string operator = 20;  // 操作人
   uint32 update_ts = 21;  //更新时间
   uint32 status = 22;  // 配置状态 see DisplaySceneStatusType
   uint32 time_type = 23;  // 配置时间类型 see DisplaySceneTimeType
   uint32 u_min_version = 24;  // 客户端最小版本(整数)
   repeated string sub_titles = 25; //副标题
   uint32 dynamic_type = 26;        //动效类型0: lottie, 1: vap
}

message QuickEntryConfig {
   repeated DisplaySceneInfo scene_info_list = 1;
}

// 查询列表
message GetDisplaySceneInfoListReq {
   uint32 type = 1;  //配置类型 see channel_.proto EQuickEntryType
   uint32 scene_id = 2;  //位置id
   uint32 market_id = 3; // 产品id 有默认值0，所以前端传market_id是加1的
   uint32 status = 4;  // 配置状态 see DisplaySceneStatusType
   uint32 begin_ts = 5; //生效开始时间
   uint32 end_ts = 6;  // 生效结束时间
   uint32 page = 7;  // 从1开始
   uint32 page_size = 8;
}
message GetDisplaySceneInfoListResp {
   repeated DisplaySceneInfo info_list = 1;
   uint32 total_cnt = 2;
}

// 增加配置
message AddDisplaySceneInfoReq {
   DisplaySceneInfo info = 1;
}
message AddDisplaySceneInfoResp {
}

// 删除配置
message DelDisplaySceneInfoReq {
   uint32 id = 1;
}
message DelDisplaySceneInfoResp {
}


message GetQuickEntryConfigListReq {
}
message GetQuickEntryConfigListResp {
   map<uint32,QuickEntryConfig> map_type_conf = 1; // type see channel_.proto EQuickEntryType
}


//批量获取直播间的歌手标签
message BatGetChannelSoundLabelReq{
   repeated uint32 cid_list = 1;
}
message BatGetChannelSoundLabelResp{
   map<uint32, string> map_cid_label = 1;
}


//触发定时任务
message TriggerTimerReq {
   // 定时任务类型
   enum TimerType
   {
      Timer_Type_Invalid = 0;  // 无效
      Timer_Type_AutoGenPgcPrepareLevel = 1;  // 自动评级定时任务
      Timer_Type_LoadPgcPrepareLevel = 2;  // 生效评级库等级
   }

   TimerType timer_type = 1; // 定时任务类型
}
message TriggerTimerResp {
}



//根据短链中的TagId获取快速进度推荐房间id
message GetQuickRecChannelByTagIdReq {
    uint32 uid = 1;
    uint32 tag_id = 2;
}
message GetQuickRecChannelByTagIdResp {
    uint32 channel_id = 1;
}

enum QuickRecChannelType {
	Normal = 0;        //普通-原纯随机的方式
	MicSex = 1;        //优先随机跳转当前麦上异性人数≥N人的房间, 如不存在，则纯随机
}

// 获取用户roi信息
message GetUserRoiInfoReq {
  uint32 uid = 1;
  string device_id = 2; // 设备id，登录查询接口需要填充，千人千面策略数据是设备维度
}
message GetUserRoiInfoResp {
  bool is_roi = 1; // 是否是roi用户
  string roi_high_cert = 2;  //roi高潜用户标识
  string roi_high_pop_url =3;  // 高潜用户弹窗
}

//确认高潜付费用户弹窗显示
message ConfirmRoiHighPotentailReq{
   uint32 uid = 1;
}
message ConfirmRoiHighPotentailResp{
}

// 房间组资源位后台配置

// 房间组资源基本结构

message ChannelGroupResource {
    uint32 id = 1;  // 房间组id
    string name = 2;  // 房间组名称
    string sub_name = 3;  // 房间组子名称
    string background = 4;  // 背景图
    string jump_url = 5;  // 跳转链接
    double rank = 6;  // 排序
    uint32 begin_ts = 7;  // 开始时间
    uint32 update_ts = 8;  // 更新时间
    uint32 status = 9;  // 状态  1未生效 2生效中 3已过期
    repeated uint32 channel_id_list = 10; // 房间id列表
    uint32 end_ts = 11;  // 结束时间
    uint32 url_type = 12;  // 跳转链接类型 1:活动链接 2:短链
}

// 房间组资源位配置列表请求
message GetChannelGroupResourceListReq {
  uint32 page = 1; // 从 1开始
  uint32 page_size = 2;
  uint32 id = 3;
  string name = 4;
  uint32 status = 5;
}

// 房间组资源位配置列表响应
message GetChannelGroupResourceListResp {
    repeated ChannelGroupResource list = 1;  // 房间组资源位配置列表
    uint32 total_cnt = 2;  // 总数
}

// 房间组资源位配置添加请求
message AddChannelGroupResourceReq {
    ChannelGroupResource info = 1;  // 房间组资源位配置
}

// 房间组资源位配置添加响应
message AddChannelGroupResourceResp {
}

// 房间组资源位配置更新请求
message UpdateChannelGroupResourceReq {
    ChannelGroupResource info = 1;  // 房间组资源位配置
}

// 房间组资源位配置更新响应
message UpdateChannelGroupResourceResp {
}

// 房间组资源位配置删除请求
message DelChannelGroupResourceReq {
    uint32 id = 1;  // 房间组id
}

// 房间组资源位配置删除响应
message DelChannelGroupResourceResp {
}

// 检查房间组资源位配置有效性
message CheckChannelGroupResourceReq {
    ChannelGroupResource info = 1;
}

// 检查房间组资源位配置有效性
message CheckChannelGroupResourceResp {
     bool is_valid = 1;  // 是否有效
}

//根据TAGID获取推荐房间
message GetChannelByTagIdReq
{
  uint32 tag_id = 1;
  uint32 start = 2;
  uint32 count = 3;
  uint32 user_category = 4;
  uint32 uid = 5;
}
message GetChannelByTagIdResp
{
  repeated ChannelRecommendSimpleInfo channel_list = 1;
  bool is_end = 2;
}

//根据个性tagid请求房间数据
message GetRecChListByPerTagIdReq
{
  uint32 personality_tag_id = 1;
  uint32 start = 2;
  uint32 count = 3;
  int32 sex = 4; // 性别
  uint32 uid = 5;
}
message GetRecChListByPerTagIdResp
{
  repeated ChannelRecommendSimpleInfo channel_list = 1;
  bool is_end = 2;
}

// 批量获取房间的大礼物信息
message BatchGetChannelBigGiftInfoReq {
   repeated uint32 cid_list = 1;
}


message BatchGetChannelBigGiftInfoResp {
   map<uint32, BigGiftInfo> map_cid_gift = 1;
}

message BigGiftInfo {
   uint32 gift_id = 1;
   string gift_name = 2;
   string gift_icon = 3;  // 礼物图
}
// 营收相关 开关合集
message BatchGetRevenueSwitchHubReq{
    repeated uint32  uid = 1;
    uint32 switch_type = 2; /* RevenueSwitchHubType */
}

message BatchGetRevenueSwitchHubResp{
    map<uint32,bool> is_open_map = 2; // uid to  is_open true：开 false：关
}

message GetRevenueSwitchHubReq {
    uint32  uid = 1;
}

message GetRevenueSwitchHubResp{
    map<uint32,bool> is_open_map = 2; // RevenueSwitchHubType to is_open  true：开 false：关
}

// 设置营收开关
message SetRevenueSwitchHubReq {
    uint32 uid = 1;
    uint32 switch_type = 2; /* RevenueSwitchHubType */
    bool is_open = 3; // true 开
}

message SetRevenueSwitchHubResp{
}


// 获取优质用户信息
message GetUserQualityInfoReq {
   // 查询场景
   enum QueryScene
   {
      Query_Scene_Invalid = 0;  // 无效
      Query_Scene_Login = 1;  // 登录查询
      Query_Scene_Channel = 2;  // 房间里查询
   }
   uint32 scene_type = 1;  // see QueryScene
   uint32 uid = 2;
}
message GetUserQualityInfoResp {
  bool is_quality = 1; // 是否是优质用户
  string high_cert_url =2;  // 壕标识
  string hign_pop_url = 3; // 壕用户弹窗
}

//确认壕用户弹窗显示
message ConfirmQualityHighPopReq{
   uint32 uid = 1;
}
message ConfirmQualityHighPopResp{
}


//获取顶部浮窗房间信息
message GetTopWinChannelInfoReq {
   uint32 uid = 1;
}

message TopWinChannelInfo {
    uint32 channel_id = 1;
    uint32 tag_id = 2;
    string tag_name = 3;
    uint32 rec_poor_source = 4;// 推荐池来源 see channel_.proto ERecPoorSourceV2
}
message GetTopWinChannelInfoResp {
    TopWinChannelInfo info = 1;
    bool is_hit_abtest = 2; //是否命中AB测试
}

message CleanTopWinFilterReq {
    uint32 uid = 1;
}
message CleanTopWinFilterResp {
}


//获取顶部浮窗房间信息
message GetTopOverLayChannelReq {
    uint32 uid = 1;
}
message GetTopOverLayChannelResp {
    uint32 channel_id = 1;
    uint32 tag_id = 2;
    string tag_name = 3;
    uint32 recommend_pool = 4;
}

// 快速进房权重配置
message QuickWeightConf {
   uint32 tag_id = 1; // 标签id
   map<uint32,uint32> map_lv_weight = 2;   // lv see enum ChannelLevel
}

// 获取快速进房权重配置列表
message GetQuickWeightConfListReq{
}
message GetQuickWeightConfListResp{
   repeated QuickWeightConf conf_list = 1;
}


// 获取快速进房在线房间列表
message GetQuickRecChannelListReq{
   uint32 tag_id = 1; // 标签id
   uint32 level = 2;  // see  enum ChannelLevel
   uint32 page = 3;  // 页码 从1开始
   uint32 page_size = 4; 
}
message GetQuickRecChannelListResp{
   repeated uint32 cid_list = 1;  //  cid列表
}

//二级标签配置
message SubTagConfig
{
    uint32 tag_id = 1;
    string name = 2;
}

message TagConfigInfo
{
    string root_tag_name = 1;   //一级tag名称
    uint32 root_tag_id = 2;     //一级tagId
    repeated SubTagConfig sub_tag_list = 3;  //二级标签配置
}

// 获取标签配置列表
message GetTagConfigInfoListReq {
}
message GetTagConfigInfoListResp {
    repeated TagConfigInfo tag_list = 1; 
}

//获取推荐列表房间反馈配置
message GetRecFeedbackConfigReq {
}
message GetRecFeedbackConfigResp {
  repeated string reason_list = 1;   // 原因列表
}

//推荐房间反馈
message DoRecFeedbackReq {
  uint32 uid = 1;
  uint32 channel_id = 2;
  repeated string reason_list = 3;   // 原因列表
}
message DoRecFeedbackResp {
}

message FilteringRecFeedBackReq {
  uint32 uid = 1;
  repeated uint32 channel_id_list = 2; // 需要过滤的房间id列表
}

message FilteringRecFeedBackResp {
  repeated uint32 channel_id_list = 1; // 过滤后的房间id列表
}

// 获取婚礼房间列表
message CheckUidIsInWeddingGroupReq {
    uint32 uid = 1;
}

message CheckUidIsInWeddingGroupResp {
    bool is_in_wedding_group = 1; // 是否在婚礼房间组
}
