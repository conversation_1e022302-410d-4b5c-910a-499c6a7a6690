syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/configserver";
package configserver;

service ConfigServer {
    rpc GetConfig (GetConfigReq) returns (GetConfigRsp) {}
    rpc BatchSaveConfig (BatchSaveConfigReq) returns (BatchSaveConfigRsp) {}
    rpc BatchGetUserConfig (BatchGetUserConfigReq) returns (BatchGetUserConfigRsp) {}
    rpc GetUsersConfig (GetUsersConfigReq) returns (GetUsersConfigRsp) {}
    // 创建/修改ugc房间更多配置
    rpc UpsertUgcMoreConfig(UpsertUgcMoreConfigReq) returns (UpsertUgcMoreConfigResp) {}
    // 删除ugc房间更多配置
    rpc DelUgcMoreConfig(DelUgcMoreConfigReq) returns (DelUgcMoreConfigResp) {}
    // 获取ugc房间更多配置列表
    rpc GetUgcMoreConfigList(GetUgcMoreConfigListReq) returns (GetUgcMoreConfigListResp) {}
    // 获取业务兜底开关配置列表
    rpc GetFallBackSwitchByTypes(GetFallBackSwitchByTypesReq) returns (GetFallBackSwitchByTypesResp) {}
    // 修改业务兜底开关
    rpc SetSwitchStatusByBusinessType(SetSwitchStatusByBusinessTypeReq) returns (SetSwitchStatusByBusinessTypeResp) {}
}

// config_key，fast_pc_show_process中value枚举值
enum FastPcShowProcessType {
    FAST_PC_SHOW_PROCESS_TYPE_UNSPECIFIED = 0; // 0:所有人可见
    FAST_PC_SHOW_PROCESS_TYPE_FANS = 1; // 1:粉丝可见
    FAST_PC_SHOW_PROCESS_TYPE_PARTNER = 2; // 2-玩伴可见
}

message ConfigInfo {
    string config_key = 1;
    uint32 uid = 2;
    uint32 config_type = 3;
    string value = 4;
    //子类型，根据不同的config_key，目前fast_pc_show_process有效，0:所有人可见， 1:粉丝可见，2-玩伴可见
    uint32 sub_type = 5[deprecated = true];
}

message BatchSaveConfigReq {
    repeated ConfigInfo config_info = 1;
}

message BatchSaveConfigRsp {
}

message BatchGetUserConfigReq {
    repeated string config_keys = 1;
    uint32 uid = 2;
}

message BatchGetUserConfigRsp {
    repeated ConfigInfo config_info = 1;
}

message GetConfigReq {
    string config_key = 1;
    uint32 uid = 2;
}

message GetConfigRsp {
    ConfigInfo config_info = 1;
    bool no_exist = 2;
}

message GetUsersConfigReq {
    string config_key = 1;
    repeated uint32 uids = 2;
}

message GetUsersConfigRsp {
    repeated ConfigInfo config_info = 1;
}

message UgcMoreConfig {
    string id = 1;
    uint32 tab_id = 2;            // 玩法id
    string icon_img = 3;          // icon图片
    string icon_name = 4;         // icon名称, 最大长度为6
    string link = 5;              // 链接
    bool only_roomer_visible = 6; // 是否仅房主可见, 默认false
    int64 updated_at = 7;         // 更新时间, 单位: ms
}

message UpsertUgcMoreConfigReq {
    UgcMoreConfig config = 1;
}

message UpsertUgcMoreConfigResp {
}

message DelUgcMoreConfigReq {
    string id = 1;
}

message DelUgcMoreConfigResp {
}

message GetUgcMoreConfigListReq {
    uint32 tab_id = 1;      // 玩法id
}

message GetUgcMoreConfigListResp {
    repeated UgcMoreConfig configs = 1;
}

enum SwitchBusinessType {
    INVALID_TYPE = 0;
    GAME_MIX_CHANNEL_LIST = 1;//开黑房间列表&混推
    GAME_UGC_QUICK_MATCH =2; //开黑个人房快速匹配
    MUSIC_CHANNEL_LIST = 3; //音乐房间列表
    GAME_TEMP_QUICK_MATCH = 4; //临时房快速匹配
}

enum SwitchEnv {
    SWITCH_DEFAULT = 0;  //开发，测试，生产，主要用于区分出灰度环境
    SWITCH_STAGING = 1;
}

//批量获取兜底开关配置
message GetFallBackSwitchByTypesReq {
    repeated SwitchBusinessType types = 1; //请求的业务开关列表
    string req_source = 2; //请求来源，可填写同步开关的服务名，用于打印日志
    SwitchEnv switch_env = 3;
}

message GetFallBackSwitchByTypesResp {
    map<int32, bool> switch_map = 1; // key,业务类型， value开关状态,当对应开关打开时有值
}

//修改业务开关
message SetSwitchStatusByBusinessTypeReq {
    SwitchBusinessType types = 1; //业务类型
    string operator = 2; //操作人
    uint32 status = 3; //开关状态， 0关闭，1打开
    SwitchEnv switch_env = 4;
}

message SetSwitchStatusByBusinessTypeResp {
}