syntax = "proto3";

// namespace
package timelinev2;

option go_package = "golang.52tt.com/protocol/services/timeline-v2";

//////////////////
service TimeLineSvr {
  // 写单条timeline
  rpc WriteTimelineMsg(WriteTimelineMsgReq) returns (WriteTimelineMsgResp) {}
  // 拉多条timeline，建议数量控制在500条以下
  rpc PullTimelineMsg(PullTimelineMsgReq) returns (PullTimelineMsgResp) {}
  // 批量写多条相同Id和suffix的timeline
  rpc BatchWriteTimelineMsg(BatchWriteTimelineMsgReq) returns (BatchWriteTimelineMsgResp) {}
  // 批量写多条不同id和suffix的timeline
  rpc BatchWriteTimelineMsgV2(BatchWriteTimelineMsgV2Req) returns (BatchWriteTimelineMsgV2Resp) {}
  // 删除单条timeline（业务勿用，仅限于消灭脏数据）
  rpc DeleteTimelineMsg(DeleteTimelineMsgReq) returns (DeleteTimelineMsgResp) {}
  // 批量删除timeline
  rpc BatchDeleteTimelineMsg(BatchDeleteTimelineMsgReq) returns (BatchDeleteTimelineMsgResp) {}
  // 批量获取消息
  rpc BatchGetTimelineMsg(BatchGetTimelineMsgReq) returns(BatchGetTimelineMsgResp) {}
  rpc CheckMsgKeyExist(CheckMsgKeyExistReq) returns(CheckMsgKeyExistResp) {}
  rpc SetMsgKey(SetMsgKeyReq) returns (SetMsgKeyResp) {}
}

//------------------------------------------
// timeline的通用存储结构，所有消息都是包含在这个结构体里面。
//------------------------------------------
message TimelineMsg {
  enum TYPE {
    INVALID = 0;
    IM_MSG = 1;
    GUILD_APPLY = 2;
    DEL_IM_MSG = 3;
    GUILD_QUIT = 4;
    IM_GUILD_GROUP_MEM_MODIFY = 5;
    GROUP_TL_INDEX = 6;
    MULTI_PUBLISH_MSG = 7; // 通用频道消息，multi_publisher.Message
    AI_GROUP = 8; // AI群消息
  }
  uint32 type = 1;
  uint32 seqid = 2;
  bytes msg_bin = 3;
}

message WriteTimelineMsgReq {
  uint32 id = 1;
  string suffix = 2;
  TimelineMsg msg = 3;
}

message WriteTimelineMsgResp {
}

message BatchWriteTimelineMsgReq {
  uint32 id = 1;
  string suffix = 2;
  repeated TimelineMsg msg_list = 3;
}

message BatchWriteTimelineMsgResp {
}

message PullTimelineMsgReq {
  uint32 id = 1;
  string suffix = 2;
  uint32 start_seqid = 3;  //第一次获取不需要用填，默认0即可
  uint32 limit = 4;
  bool skip_cache = 5;
  bool reverse = 6;    // 顺序是1-2-3-4-5-6-7, start_seqid=4传true会获取到3-2-1，false会获取到5-6-7
  bool raw = 7; // 是否返回原始数据，默认false
}

message PullTimelineMsgResp {
  repeated TimelineMsg msg_list = 1;
  repeated bytes raw_msg_list = 2; // 如果raw为true，返回原始数据
}

message BatchWriteTimelineMsgV2Req {
  repeated WriteTimelineMsgReq msg_list = 1;
}

message BatchWriteTimelineMsgV2Resp {
}

message DeleteTimelineMsgReq {
  uint32 id = 1;
  string suffix = 2;
  uint32 seq_id = 3;
  string tl_key = 4; // or tikv key
}

message DeleteTimelineMsgResp {
}

message BatchDeleteTimelineMsgReq {
  repeated DeleteTimelineMsgReq req_list = 1;
}

message BatchDeleteTimelineMsgResp {
}

message GetTimelineMsgReq {
  string id = 1;
  string suffix = 2;
  uint64 seq_id = 3;
}

message BatchGetTimelineMsgReq {
  repeated GetTimelineMsgReq req_list = 1;
}

message BatchGetTimelineMsgResp {
  repeated TimelineMsg msg_list = 1;
}

// ------------------------------------------------------------------------------------------------

// 已读状态 API
service ReadStatus {
  rpc SetPeerReadStatus(SetPeerReadStatusReq) returns (SetPeerReadStatusResp) {}
  rpc BatchGetPeerReadStatus(BatchGetPeerReadStatusReq) returns (BatchGetPeerReadStatusResp) {}
}

message SetPeerReadStatusReq {
  uint32 peer_uid = 1;
  string self_account = 2;
  uint32 msg_id = 3;  // peer已读的msg_id(self的id空间)
}

message SetPeerReadStatusResp {
  uint32 peer_seq_id = 1;
}

message BatchGetPeerReadStatusReq {
  uint32 self_uid = 1;
  repeated string peer_account_list = 2;
  uint32 self_seq_id = 3; // 只获取比seq_id大的
}

message BatchGetPeerReadStatusResp {
  repeated string peer_account_list = 1;
  repeated PeerReadStatus status_list = 2; // 与peer_account_list一一对应
}

message PeerReadStatus {
  uint32 msg_id = 1;
  uint32 seq_id = 2;
}

message CheckMsgKeyExistReq {
  string key = 1;
}

message CheckMsgKeyExistResp {
  bytes value = 1;
}

message SetMsgKeyReq {
  string key = 1;
  bytes value = 2;
  uint32 ttl = 3;
}

message SetMsgKeyResp {
}
