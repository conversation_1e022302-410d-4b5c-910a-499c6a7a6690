syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-apicenter";

package channel_apicenter;

//背景所属客户端类型
enum ChannelBackgroundClientType {
  ChannelBackgroundClientType_default = 0; //默认，非仅开黑极速版
  ChannelBackgroundClientType_only_pclfg = 1; //仅开黑极速版
}
message AddChannelBackgroundConfReq {
  ChannelBackgroundInfo background_info = 1;
  ChannelBackgroundClientType client_type = 2; //背景所属客户端类型
}

message AddChannelBackgroundConfResp {
}

message UpdateChannelBackgroundConfReq {
  ChannelBackgroundInfo background_info = 1;
  ChannelBackgroundClientType client_type = 2; //背景所属客户端类型
}

message UpdateChannelBackgroundConfResp {
}

message DelChannelBackgroundConfReq {
  uint64 background_id = 1;
  ChannelBackgroundClientType client_type = 2; //背景所属客户端类型
}

message DelChannelBackgroundConfResp {
}


message GetChannelBackgroundConfReq {
}

message GetChannelBackgroundConfResp {
  repeated ChannelBackgroundInfo background_list = 1;
}

message GetChannelBackgroundConfV2Req {
  uint32 page = 1; // 页号
  uint32 size = 2; // 页大小
  string name = 3; // 背景名称，模糊查询
  repeated BackgroundBackstageLiveType backstage_live_type_list = 4;
  BackgroundSubTypeSelectType select_type = 5; // 查询过滤类型
  ChannelBackgroundClientType client_type = 6; //背景所属客户端类型
}

message GetChannelBackgroundConfV2Resp {
  repeated ChannelBackgroundInfo background_list = 1;
  uint32 total = 2; // 总的数据量
  uint64 total_page = 3; // 总页数
}

message BatchGiveChannelBgReq {
  ChannelBgInfo infos = 1;
  ChannelBackgroundClientType client_type = 2; //背景所属客户端类型
  ChannelBgInfoV2 v2_infos = 3;                //优先使用v2
}

message BatchGiveChannelBgResp {
  repeated uint32 fail_list = 1; // 发放失败的房间display id, 如果请求传参为channel_id, 则为channel_id列表
  repeated string fail_channel_view_id_list = 2; // 发放失败的channel_view_id
}

message UpdateGivenChannelBgReq {
  int64 id = 1; // 下发记录id
  uint32 channel_id = 2; // 房间id
  uint64 background_id = 3; // 背景id
  int64 expire_sec = 4;  // 设定一个过期时长（秒），覆盖现有时长
  string operator = 5; // 操作人
}

message UpdateGivenChannelBgResp {

}

message DeleteGivenChannelBgReq {
  repeated int64 id = 1; // 下发记录id
  string operator = 2; // 操作人
  ChannelBackgroundClientType client_type = 3; //背景所属客户端类型
}

message DeleteGivenChannelBgResp {

}

message ListGivenChannelBgReq {
  uint32 page = 1; // 页号
  uint32 size = 2; // 页大小
  uint32 channel_display_id = 3; // 房间display id, 不建议使用
  uint32 channel_id = 4;         // 房间id，唯一id
  string channel_view_id = 5;    // 新房间显示id，建议后续使用channel_id查询
  uint32 ga_channel_type = 6;    // 房间类型 see channel_.proto -> ChannelType
  ChannelBackgroundClientType client_type = 7; //背景所属客户端类型
}

message ListGivenChannelBgResp {
  repeated GivenChannelGgInfo data = 1; // 下发记录
  uint32 total = 2; // 总的数据量
  uint64 total_page = 3; // 总页数
}

message GivenChannelGgInfo {
  int64 id = 1; // 下发记录id
  uint32 channel_id = 2; // 房间id
  uint32 channel_display_id = 3; // 房间display id, 不建议使用
  uint64 background_id = 4; // 背景id
  string static_picture = 5; // 背景静态图
  string background_name = 6; // 背景名称
  uint64 create_timestamp = 7; // 创建时间，时间戳，秒
  uint64 expire_time = 8 ; // 剩余有效时间，秒
  uint64 guild_id = 9; // 工会id
  uint32 guild_display_id = 10; // 工会短号id
  ChannelType channel_type = 11; // 历史，无意义的字段，不是真正的房间类型
  string channel_name = 12; // 房间名称
  uint64 update_time = 13; // 更新时间，时间戳，秒
  string channel_view_id = 14; // 新房间显示id，替代channel_display_id
  uint32 background_resource_type = 15; // 背景资源类型，see channelbackground.proto -> BackgroundResourceType
  string operator = 16; // 操作人
  BackgroundLevel background_level = 17; // 背景等级，用于运营侧的透传记录，无业务意义，历史的背景默认为等级2
}


enum ChannelType {
  CHANNEL_TYPE_UNKNOWN = 0;
  MULTIPLAYER = 1;
  DATING = 2;
}

message ChannelBgInfo {
  uint64 background_id = 1; // 背景id
  repeated uint32 channel_display_id = 2; // 房间id
  int64 expire_sec = 3;  // 过期时长（秒）
  ChannelType channel_type = 4; // 历史，无意义的字段，不是真正的房间类型
  repeated string channel_view_id_list = 5;
  int64 incr_expire_sec = 6; // 在原有过期时间上，叠加的过期时间（秒）；若设置了expire_sec，该字段被忽略
  string operator = 7; // 操作人
}

message GivenTarget {
  uint64 background_id = 1;   // 背景id列表，与channel_view_id对应
  string channel_view_id = 2; // 房间显示id列表，与background_id对应
  int64 expire_sec = 3;       // 过期时长（秒）
  int64 incr_expire_sec = 4;  // 在原有过期时间上，叠加的过期时间（秒）；若设置了expire_sec，该字段被忽略
  uint32 channel_id = 5;      // 房间cid，传cid则忽略channel_view_id
}
message UserGroupGivenTarget {
  uint64 background_id = 1;  // 背景id
  string user_group_id = 2;  // 人群包ID
  int64 expire_sec = 3;      // 过期时长（秒）
  int64 incr_expire_sec = 4; // 在原有过期时间上，叠加的过期时间（秒）；若设置了expire_sec，该字段被忽略
}
message ChannelBgInfoV2 {
  repeated GivenTarget batch_given_target = 1;      // 批量发放
  UserGroupGivenTarget user_group_given_target = 2; // 人群包发放
  string operator = 5;                              // 操作人
}


enum BackgroundType {
  Forever = 0;
  TimeLimit = 1;
  Default = 2;
  LevelLimit = 3;
  DIYLimit = 4;       // 用户限时
}

enum BackgroundResourceType {
  UNKNOWN = 0; // 未知
  PHOTO = 1; // 图片
  VIDEO = 2; // 视频mp4
}

enum BackgroundLevel {
  BackgroundLevel_Unknown = 0;
  BackgroundLevel_Level1 = 1;
  BackgroundLevel_Level2 = 2;
  BackgroundLevel_Level3 = 3;
  BackgroundLevel_Level4 = 4;
  BackgroundLevel_Level5 = 5;
}

//房间背景从运营后台发放给哪些房间类型
enum BackgroundBackstageLiveType {
  BackgroundBackstageLiveType_DEFAULT = 0; //不区分类型
  BackgroundBackstageLiveType_ONLY_UGC= 1; //仅发放给UGC
  BackgroundBackstageLiveType_REVENUE_UGC = 2; //营收UGC
  BackgroundBackstageLiveType_REVENUE_PGC = 3; //营收PGC
}

enum BackgroundSubType {
  BackgroundSubType_DIY_Default = 0; // 通用类型
  BackgroundSubType_DIY_KH = 1;      // 开黑, 仅开黑房使用
}

message ChannelBackgroundInfo {
  uint64 background_id = 1;
  string background_name = 2;
  BackgroundType background_type = 3;
  string background_url = 4;
  string md5_sum = 5;    // 图片MD5值
  int64 create_time = 6; // 背景图后台配置时间
  int64 start_time = 7;  // 限时背景开始时间
  int64 end_time = 8;    // 限时背景结束时间
  bool is_force = 9;     // 是否强制应用到所有房间（用于限时背景）

  repeated uint32 access_level_list = 10; // 可访问的房间等级列表逗号分隔，仅 background_type = LevelLimit 时有效
  BackgroundResourceType resource_type = 11; // 背景资源类型
  string resource_url = 12;          // 资源url
  BackgroundLevel background_level = 13; // 背景等级，用于运营侧的透传记录，无业务意义，历史的背景默认为等级2
  BackgroundBackstageLiveType backstage_live_type = 14 ; //从后台可以发放给哪些房间

  BackgroundSubType sub_type = 15; // 背景分类，目前仅DIY背景有分类，对背景进一步分类: 通用背景(默认)，开黑
  repeated uint64 sub_type_relate_id = 16;  // 子类型关联id，如开黑背景关联的玩法scheme_id，sub_type = BackgroundSubType_DIY_KH 时，为空表示关联所有开黑玩法
  string description = 17;         // 背景介绍
  int64 desc_show_time_begin = 18; // 背景介绍展示开始时间
  int64 desc_show_time_end = 19;   // 背景介绍展示结束时间
  bool is_hide = 20;               // 是否隐藏，隐藏后只在用户持有背景列表展示
  ChannelBackgroundClientType client_type = 21; //背景所属客户端类型
}

// 更新开黑背景列表请求
message UpdateDiyKHBackgroundSortReq {
  // 背景id列表, 按照列表顺序排序
  repeated uint64 background_id_list = 1;
}

message UpdateDiyKHBackgroundSortResp {
}

// 推荐背景可见类型
enum RecommendBgVisibleType {
  // 默认，全部可见
  RecommendBgVisibleType_Default = 0;
  // 人群包可见
  RecommendBgVisibleType_SpecPacket = 1;
}

// 热门房间背景推荐配置
message RecommendBackgroundInfo {
  // 背景id
  ChannelBackgroundInfo background_info = 1;
  // 排序key, 越小越靠前
  uint32 sort_key = 2;
  // 推荐背景可见类型
  RecommendBgVisibleType visible_type = 3;
  // 推荐背景可见类型描述，可见类型为人群包是人群包ID
  string visible_value = 4;
  // 编辑时间
  int64 modify_time = 5;
}

message GetRecommendBackgroundReq {
}

message GetRecommendBackgroundResp {
  // 热门房间背景推荐列表
  repeated RecommendBackgroundInfo recommend_background_list = 1;
}

message UpdateRecommendBackgroundReq {
  // 热门房间背景推荐列表
  repeated RecommendBackgroundInfo recommend_background_list = 1;
}

message UpdateRecommendBackgroundResp {
}

message DeleteRecommendBackgroundReq {
  // 背景id
  repeated uint64 background_id_list = 1;
}

message DeleteRecommendBackgroundResp {
}

enum BackgroundSubTypeSelectType {
  BackgroundSubTypeSelectType_All = 0;        // 全部
  BackgroundSubTypeSelectType_DIY_Common = 1; // 通用
  BackgroundSubTypeSelectType_DIY_KH = 2;     // 开黑
}

message GetChannelBackgroundConfByIdListReq {
  repeated uint64 background_id_list = 1;      // 背景id列表
  ChannelBackgroundClientType client_type = 2; // 背景所属客户端类型
}

message GetChannelBackgroundConfByIdListResp {
  repeated ChannelBackgroundInfo background_list = 1; // 背景列表
}