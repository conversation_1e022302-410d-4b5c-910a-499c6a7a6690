syntax = "proto3";

import "tt/quicksilver/channel-apicenter/channelbackground-api.proto";

option go_package = "golang.52tt.com/protocol/services/channel-apicenter";

package channel_apicenter;

service ChannelApicenter {
  // 添加背景配置
  rpc AddChannelBackgroundConf (AddChannelBackgroundConfReq) returns (AddChannelBackgroundConfResp) {
  }
  // 删除背景配置
  rpc DelChannelBackgroundConf (DelChannelBackgroundConfReq) returns (DelChannelBackgroundConfResp) {
  }
  // 获取背景配置
  rpc GetChannelBackgroundConf (GetChannelBackgroundConfReq) returns (GetChannelBackgroundConfResp) {
  }
  // 添加背景配置V2
  rpc AddChannelBackgroundConfV2(AddChannelBackgroundConfReq) returns (AddChannelBackgroundConfResp) {
  }
  // 更新背景配置V2
  rpc UpdateChannelBackgroundConfV2(UpdateChannelBackgroundConfReq) returns (UpdateChannelBackgroundConfResp) {
  }
  // 删除背景配置V2
  rpc DelChannelBackgroundConfV2(DelChannelBackgroundConfReq) returns (DelChannelBackgroundConfResp) {
  }
  // 获取背景配置V2
  rpc GetChannelBackgroundConfV2(GetChannelBackgroundConfV2Req) returns (GetChannelBackgroundConfV2Resp) {
  }
  // 批量下发背景
  rpc BatchGiveChannelBg(BatchGiveChannelBgReq) returns (BatchGiveChannelBgResp) {
  }
  // 更新下发的背景信息
  rpc UpdateGivenChannelBg(UpdateGivenChannelBgReq) returns (UpdateGivenChannelBgResp) {
  }
  // 删除下发的背景信息
  rpc DeleteGivenChannelBg(DeleteGivenChannelBgReq) returns (DeleteGivenChannelBgResp) {
  }
  // 查询下发的背景信息
  rpc ListGivenChannelBg(ListGivenChannelBgReq) returns (ListGivenChannelBgResp) {
  }
  // 更新开黑背景排序
  rpc UpdateDiyKHBackgroundSort(UpdateDiyKHBackgroundSortReq) returns (UpdateDiyKHBackgroundSortResp) {
  }
  // 获取热门房间推荐背景
  rpc GetRecommendBackground(GetRecommendBackgroundReq) returns (GetRecommendBackgroundResp) {
  }
  // 更新热门房间推荐背景
  rpc UpdateRecommendBackground(UpdateRecommendBackgroundReq) returns (UpdateRecommendBackgroundResp) {
  }
  // 删除热门房间推荐背景
  rpc DeleteRecommendBackground(DeleteRecommendBackgroundReq) returns (DeleteRecommendBackgroundResp) {
  }
  // 根据背景ID批量获取背景配置
  rpc GetChannelBackgroundConfByIdList(GetChannelBackgroundConfByIdListReq) returns (GetChannelBackgroundConfByIdListResp) {
  }
  // 发房间广播消息
  rpc PushMsgToChannel(PushMsgToChannelReq) returns (PushMsgToChannelResp) {
  }
}

// 推送房间消息
message PushMsgToChannelReq{
  // 业务标识，必填
  string business_mark = 1;
  // 业务信息，必填
  string business_detail_info = 2;
  // 推送类型，必填，channel_.proto 的 ChannelMsgType
  uint32 type = 3;
  // 用户uid，选填，需要主体则填充
  uint32 uid = 4;
  // 房间id，必填
  uint32 channel_id = 5;
  // pb序列化消息体，选填，有扩展字段时填充
  bytes pb_data = 6;
  // 推送消息的文本明文，选填
  string content = 7;
}
message PushMsgToChannelResp{
  // 该消息唯一标识，用于跟踪推送链路
  string request_id = 1;
}