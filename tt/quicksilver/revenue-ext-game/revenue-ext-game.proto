syntax = "proto3";

package revenue_ext_game;
option go_package = "golang.52tt.com/protocol/services/revenue-ext-game";

service RevenueExtGame {
  // 游戏开始时设置操作区域配置
  rpc SetExtGameOpCfg(SetExtGameOpCfgReq) returns(SetExtGameOpCfgResp){}
  // 设置用户阵营
  rpc SetUserCamp(SetUserCampReq) returns(SetUserCampResp){}
  // 获取用户游戏信息
  rpc GetUserExtGameInfo(GetUserExtGameInfoReq) returns(GetUserExtGameInfoResp){}
  // 获取游戏配置列表
  rpc GetExtGameCfgList(GetExtGameCfgListReq) returns(GetExtGameCfgListResp){}
  // 启动小游戏挂载
  rpc MountExtGame(MountExtGameReq) returns(MountExtGameResp){}
  // 获取房间挂载的游戏
  rpc GetMountExtGame(GetMountExtGameReq) returns(GetMountExtGameResp){}
  // 批量获取房间挂载的游戏
  rpc BatchGetMountExtGame(BatchGetMountExtGameReq) returns(BatchGetMountExtGameResp){}
  // 停止小游戏挂载
  rpc UnmountExtGame(UnmountExtGameReq) returns(UnmountExtGameResp){}
  // 根据口令获取房间id
  rpc GetChannelBySerial(GetChannelBySerialReq) returns(GetChannelBySerialResp) {}
  // 启动游戏数据推送
  rpc StartDataReport(StartDataReportReq)returns(StartDataReportResp){}
  // 停止游戏数据推送
  rpc StopDataReport(StopDataReportReq)returns(StopDataReportResp){}
  // 查询任务状态
  rpc GetDataReportTaskStatus(GetDataReportTaskStatusReq) returns(GetDataReportTaskStatusResp){}
  // “我想玩”上报
  rpc ReportUserWantPlay(ReportUserWantPlayReq) returns(ReportUserWantPlayResp){}
  // “游戏结束”上报
  rpc ReportGameEnd(ReportGameEndReq) returns(ReportGameEndResp){}


  // 任务数据手动推送（测试用）
  rpc ManualDataReportDemo(ManualDataReportDemoReq) returns(ManualDataReportDemoResp){}

  // 查询房间入口权限状态
  rpc GetChannelExtGameAccessStatus(GetChannelExtGameAccessStatusReq) returns(GetChannelExtGameAccessStatusResp){}

  // ============= 6.34.0 积分榜单 =============
  // 设置游戏榜单
  rpc SetGameScoresRank(SetGameScoresRankReq) returns (SetGameScoresRankResp){}
  // 获取游戏榜单
  rpc GetExtGameScoreRank(GetExtGameScoreRankReq) returns (GetExtGameScoreRankResp){}
  // 获取榜单荣誉信息
  rpc GetExtGameRankHonorInfo(GetExtGameRankHonorInfoReq) returns (GetExtGameRankHonorInfoResp){}
  // 获取用户榜单铭牌信息
  rpc GetExtGameRankNameplate(GetExtGameRankNameplateReq) returns (GetExtGameRankNameplateResp){}

  // ============= 6.34.0 连麦pk =============
  // 开始连麦pk
  rpc StartChatPk(StartChatPkReq) returns (StartChatPkResp){}
  // 结束连麦pk
  rpc StopChatPk(StopChatPkReq) returns (StopChatPkResp){}
  // 连麦pk心跳
  rpc ChatPkHeartbeat(ChatPkHeartbeatReq) returns (ChatPkHeartbeatResp){}

  /************运营后台接口***************/
  // 批量发放直播间互动游戏入口权限
  rpc BatSetChannelExtGameAccessConf(BatSetChannelExtGameAccessConfReq) returns(BatSetChannelExtGameAccessConfResp){}
  // 分页获取直播间互动游戏入口权限信息
  rpc GetChannelExtGameAccessConf(GetChannelExtGameAccessConfReq) returns(GetChannelExtGameAccessConfResp){}
  // 批量回收直播间互动游戏入口权限
  rpc BatDelChannelExtGameAccess(BatDelChannelExtGameAccessReq) returns(BatDelChannelExtGameAccessResp){}
  // 修改直播间互动游戏入口权限信息
  rpc UpdateChannelExtGameAccessConf(UpdateChannelExtGameAccessConfReq) returns(UpdateChannelExtGameAccessConfResp){}
}

// 外部游戏类型
enum ExtGameType {
    EXT_GAME_TYPE_UNSPECIFIED = 0;
    EXT_GAME_TYPE_CUTE_PETS_WAR = 1; // 萌宠宠之战
    EXT_GAME_TYPE_SHEEP_WOLVES_WAR = 2; // 羊羊抗狼
    EXT_GAME_TYPE_SIEGE_OF_CITY = 3; // 兵临城下
    EXT_GAME_TYPE_WEI_SHU_WU = 4;   // 魏蜀吴
    EXT_GAME_TYPE_SKIING = 5;       // 滑雪
    EXT_GAME_TYPE_GONG_DI_WAR = 6; // 工地大赛
    EXT_GAME_TYPE_FANG_KUAI_CHONG = 7; // 方块冲冲冲
}

enum GameRankType {
    GAME_RANK_TYPE_UNSPECIFIED = 0;
    GAME_RANK_TYPE_WORLD_RANK_LAST_N_DAYS = 1;  // n日世界榜
}

message CampButtonCfg {
    string button_color = 1;    // 按钮颜色
    string button_text = 2;     // 按钮文案
    string join_text = 3;       // 加入阵营发送的公屏文案
}

// 操作区域的配置
message ExtGameOpCfg {
    repeated CampButtonCfg camp_button_list = 1;    // 阵营加入按钮
    bool quick_gift_enable = 2;                     // 快捷送礼开关
}

// 游戏专属礼物
message ExtGameGiftCfg {
    uint32 gift_id = 1; // 礼物id
    string desc = 2;    // 礼物说明
}

message ExtGameCfg {
    ExtGameType game_type = 1; // 小游戏类型
    string name = 2;           // 小游戏名字
    string desc = 3;           // 游戏简介
    string pic_url = 4;        // 游戏图标
    string cms_url = 5;        // 规则链接
    repeated ExtGameGiftCfg gift_list = 6;  // 该游戏支持的礼物列表（在送礼栏展示）
    bool resource_display_enable = 7;       // 资源展示位（“活动进度挂件”+“活动资源位” ）,为true时展示
    uint32 rank_type = 8;
}

message SetExtGameOpCfgReq {
    ExtGameType game_type = 1; // 小游戏类型
    uint32 channel_id = 2;
    string round_id = 3;       // 游戏轮次id
    ExtGameOpCfg op_cfg = 4;   // 操作区域的配置
}

message SetExtGameOpCfgResp {}

message ReportGameEndReq {
    ExtGameType game_type = 1; // 小游戏类型
    uint32 channel_id = 2;
    string round_id = 3;       // 游戏轮次id

    message RankInfo {
        uint32 uid = 1;
        uint32 fake_uid = 2;
        uint64 score = 3;
    }
    repeated RankInfo info_list = 4;
}

message ReportGameEndResp {}

message SetUserCampReq {
    ExtGameType game_type = 1; // 小游戏类型
    uint32 channel_id = 2;
    string round_id = 3;       // 游戏轮次id
    uint32 uid = 4;
    uint32 recode_uid = 5;  // 如果是神秘人，则填fadeUid,否则填真实uid
    string camp = 6;        // 阵营
}

message SetUserCampResp {}

// 获取用户的游戏信息
message GetUserExtGameInfoReq {
    uint32 uid = 1;
    uint32 channel_id = 2;     // 直播间id
}

message GetUserExtGameInfoResp {
    string round_id = 1;        // 游戏轮次id
    ExtGameCfg game_conf = 2;   // 游戏基础配置
    ExtGameOpCfg op_conf = 3;   // 操作配置
    string user_camp = 4;       // 用户所加入的阵营，若为空则未加入（在加入阵营后，隐藏“加入阵营”按钮，再判断是否展示快捷送礼）
}

// 获取游戏配置列表
message GetExtGameCfgListReq{
    uint32 uid = 1;
    uint32 channel_id = 2;     // 直播间id
    //repeated ExtGameType game_type_list = 3; // 要获取的小游戏列表
}

message GetExtGameCfgListResp {
    repeated ExtGameCfg conf_list = 1;
}

message MountExtGameReq{
    uint32 uid = 1;     // 挂载游戏的主播id
    uint32 channel_id = 2;     // 直播间id
    ExtGameType game_type = 3; // 小游戏类型
}

message MountExtGameResp{
    string serial = 1;         // 挂载成功时返回的启动口令参数
}

message GetMountExtGameReq{
    uint32 channel_id = 1;     // 直播间id
}

message GetMountExtGameResp{
    ExtGameType game_type = 1; // 小游戏类型
}

message BatchGetMountExtGameReq{
    repeated uint32 channel_id_list = 1;     // 直播间id
}

message BatchGetMountExtGameResp{
    map<uint32, uint32> cid_to_game = 1; // key: 直播间id, value: 小游戏类型(ExtGameType)
}

message UnmountExtGameReq{
    uint32 uid = 1;        // 挂载游戏的主播id
    uint32 channel_id = 2;
    uint32 game_type = 3;  // 游戏类型
}

message UnmountExtGameResp{
}

message GetChannelBySerialReq{
    ExtGameType game_type = 1; // 小游戏类型
    string serial = 2;         // 挂载成功时返回的启动口令参数
}

message GetChannelBySerialResp{
    uint32 channel_id = 1;     // 直播间id
}

message StartDataReportReq{
    ExtGameType game_type = 1;
    uint32 channel_id = 2;
    repeated string task_type_list = 3; //任务类型,live_gift：送礼数据；live_comment:评论数据
}
message StartDataReportResp{
}

message StopDataReportReq{
    ExtGameType game_type = 1;
    uint32 channel_id = 2;
    repeated string task_type_list = 3; //任务类型,live_gift：送礼数据；live_comment:评论数据
}
message StopDataReportResp{
}

message GetDataReportTaskStatusReq{
    ExtGameType game_type = 1;
    uint32 channel_id = 2;
    string task_type = 3; //任务类型,live_gift：送礼数据；live_comment:评论数据
}
message GetDataReportTaskStatusResp{
    enum Status {
        STATUS_UNSPECIFIED = 0;
        STATUS_RUNNING = 1;     // 进行中
        STATUS_PENDING = 2;     // 任务未启动
        STATUS_NOT_EXIST = 3;   // 不存在
    }
    Status status = 1;
}

message GiftInfo{
    string gift_id = 1;
    uint32 gift_price = 2;
    uint32 gift_amount = 3;
}

// 测试用
message ManualDataReportDemoReq{
    string msg_type = 1;  //任务类型,live_gift：送礼数据；live_comment:评论数据
    GiftInfo gift_info = 2;
    string public_text = 3;
    uint32 channel_id = 4;
    uint32 continuous_num = 5;
}

message ManualDataReportDemoResp{
}

// 用户点击“我想玩”上报
message ReportUserWantPlayReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    ExtGameType game_type = 3; // 小游戏类型
}

message ReportUserWantPlayResp {}

message GetChannelExtGameAccessStatusReq{
    uint32 channel_id = 1;
    uint32 uid = 2;
}

message GetChannelExtGameAccessStatusResp{
    bool access_status = 1;
    enum StreamType {
        STREAM_TYPE_UNSPECIFIED = 0;
        STREAM_TYPE_RTC = 1;
        STREAM_TYPE_L3 = 2;
        STREAM_TYPE_CDN = 3;
    }
    StreamType stream_type = 2; // 视频流类型
    string stream_url = 3;
}


// ===== 6.34.0 迭代增加游戏积分榜单 ===================

message SetGameScoresRankReq{
    ExtGameType game_type = 1;
    uint32 rank_type = 2;       // see GameRankType
    string rank_name = 3;       // 榜单名字
    message RankInfo {
        uint32 uid = 1;
        uint32 record_uid = 2;
        uint64 scores = 3;
    }
    repeated RankInfo info_list = 4;
}

message SetGameScoresRankResp{
}

message UKWProfile {
    uint32 uid = 1;         // 真uid
    string account = 2;     // 神秘人account
    string nickname = 3;    // 神秘人nickname
    uint32 level = 4;       // 神秘人等级
    string medal = 5;       // 神秘人勋章
    string head_frame = 6;  // 神秘人头像框
    uint32 fake_uid = 7;    // 假uid
}

message UserRankInfo{
    uint32 uid = 1;     // 可能为0，用户为神秘人时为0
    uint64 scores = 2;  // 积分
    uint32 rank = 3;    // 排名 rank==0||scores==0 表示用户未上榜

    UKWProfile ukw_info = 4; // 仅uid==0时有值，神秘人信息
    repeated string nameplate_url_list= 5;  // 荣誉资源信息列表
}

// 获取游戏世界榜单
message GetExtGameScoreRankReq{
    uint32 uid = 1;
    uint32 fake_uid = 2;
    bool is_ukw = 3;       // 当前是否是神秘人
    uint32 game_type = 4;  // 小游戏类型 see ExtGameType
    uint32 rank_type = 5;  // see GameRankType
    string page_token = 6;
    uint32 page_size = 7;
}

message GetExtGameScoreRankResp{
    repeated UserRankInfo rank_list = 1;
    UserRankInfo user_rank = 2;              // 吸底用户排名
    string next_page_token = 3;
    string rank_name = 4;                     // 榜单名称
    string rank_desc = 5;                     // 榜单说明，如："根据近xxx累计游戏积分排名，实时变动"; 其中"xxx" 为占位符，使用rank_desc_high_light替换
    string rank_desc_high_light = 6;          // 榜单说明高亮内容
    string rank_cms_url_suffix = 7;           // 榜单规则链接后缀，前缀域名由客户端根据马甲包去拼接
}


message UserRank {
    uint32 uid = 1;
    uint32 rank = 2;
}

message GameRankHonorInfo{
    uint32 game_type = 1;   // see ExtGameType
    uint32 rank_type = 2;   // see GameRankType
    string game_name = 3;   // 游戏名称
    string rank_name = 4;   // 榜单名称
    repeated UserRank info_list = 5;
}

// 荣誉外显获取榜单信息
message GetExtGameRankHonorInfoReq{
    uint32 game_type = 1;   // 指定游戏类型 see ExtGameType
    uint32 rank_type = 2;   // 指定榜单类型 see GameRankType
    uint32 top_n = 3;       // 指定获取topn榜单信息
}

message GetExtGameRankHonorInfoResp{
    GameRankHonorInfo info = 1;
}

message GetExtGameRankNameplateReq{
    uint32 uid = 1;
    enum ShowType {
        SHOW_TYPE_UNSPECIFIED = 0;
        SHOW_TYPE_CHANNEL_USER_CARD = 1;  // 房间资料卡(房间主播资料卡)
        SHOW_TYPE_PERSONAL_PAGE = 2;      // 个人主页
    }
    ShowType show_type = 2;       // 铭牌展示位置，see ShowType
}

message GetExtGameRankNameplateResp{
    repeated string info_list = 1; //铭牌资源列表
}

message ChatPkMem {
    uint32 cid = 1;
    repeated uint32 mem_layout = 2; // 当前cid视角下，pk成员的先后顺序
}

message StartChatPkReq {
    uint32 game_type = 1;   // see ExtGameType
    string pk_id = 2;
    uint32 cid = 3;
    repeated ChatPkMem mem_list = 4;
}

message StartChatPkResp {}

message StopChatPkReq {
    uint32 game_type = 1;   // see ExtGameType
    string pk_id = 2;
}

message StopChatPkResp {}

message ChatPkHeartbeatReq {
    uint32 game_type = 1;   // see ExtGameType
    string pk_id = 2;
}

message ChatPkHeartbeatResp {}

//----------------------------- 运营后台相关 begin ----------------------------------------------
// 新增记录时，使用BatSetChannelExtGameAccessConfReq中的begin_time和end_time
// ChannelExtGameAccessConf中的begin_time和end_time仅在get配置时返回

message ChannelExtGameAccessConf{
    uint32 conf_id = 1;      // 配置唯一id，set时不需注意
    uint32 channel_id = 2;   // 直播间channel_id
    uint32 uid = 3;          // 主播uid
    uint32 channel_type = 4; // 房间类型
    uint32 begin_time = 5;   // 生效开始时间
    uint32 end_time = 6;     // 生效结束时间
    uint32 update_time = 7;  // 更新时间
    uint32 game_type = 8;    // 游戏类型 see enum ExtGameType
}

message BatSetChannelExtGameAccessConfReq{
    //uint32 begin_time = 1; // 生效开始时间 废弃
    //uint32 end_time = 2;   // 生效结束时间 废弃
    repeated ChannelExtGameAccessConf conf_list = 3;
    string op_id = 4;    // 操作人ID
    string op_name = 5;    // 操作人名字
}
message BatSetChannelExtGameAccessConfResp{
    repeated uint32 time_conflict_cid_list = 1;  // 因为开发时间段冲突而添加失败的channel_id列表
}

/*
    uid != 0 时，默认通过主播uid进行查询
    uid==0且channel_id<>0时，通过channel_id 进行查询
    uid==0且channel_id==0时，按更新时间降序进行分页查询
*/
message GetChannelExtGameAccessConfReq{
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 page_limit = 3; // 每页返回记录的条数，最大不超过100
    uint32 page = 4;       // 当前查询第几页
}
message GetChannelExtGameAccessConfResp{
    uint32 total_cnt = 1;   // 总记录条数
    repeated ChannelExtGameAccessConf conf_list = 3;
}

message BatDelChannelExtGameAccessReq{
    repeated uint32 conf_id_list = 1;
    string op_id = 2;    // 操作人ID
    string op_name = 3;    // 操作人名字
}
message BatDelChannelExtGameAccessResp{

}

message UpdateChannelExtGameAccessConfReq{
    ChannelExtGameAccessConf conf_info = 1;
    string op_id = 2;    // 操作人ID
    string op_name = 3;    // 操作人名字
}
message UpdateChannelExtGameAccessConfResp{

}

//----------------------------- 运营后台相关 end ----------------------------------------------