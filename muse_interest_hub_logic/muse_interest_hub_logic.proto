syntax = "proto3";

package ga.muse_interest_hub_logic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/muse-interest-hub-logic";

// Muse兴趣内容通用上报接口
message MuseCommonReportRequest{
  BaseReq base_req = 1;
  message UserLocationAuth{
    bool is_open_auth = 1;
  }
  // 用户停留在房间内时长上报 客户端默认1分钟上报一次
  message UserStayInChannel{
    uint32 channel_id = 1;
  }
  message UserOpenMicAndVoice{
    uint32 channel_id = 1;
    repeated MuseMicUserInfo user_list=2; //麦位用户列表
  }
  // 今日CP和即时闪聊定位数据上报
  message UserLocationFlashChatCpToday {
    bool is_open_auth = 1;
    float longitude = 2; // 经度
    float latitude = 3; // 纬度
    string city = 4;
    string province = 5;
    double longitude_v2 = 6; // 经度
    double latitude_v2 = 7; // 纬度
    bool is_open_app_auth = 8;
    string country = 9;
  }

  //社群的房间任务
  message SocialCommunityChannelTask{
    uint32 channel_id=1;
  }
message SocialCommunityOpenMicTask{
  uint32 channel_id=1;
  repeated MuseMicUserInfo mic_user_list=2; //麦位用户列表
}

  oneof report_info {
    UserLocationAuth user_location_auth = 2; // 用户定位授权上报 客户端触发首页上报
    UserStayInChannel user_stay_in_channel = 3; // 用户停留在房间内时长上报
    UserOpenMicAndVoice user_open_mic_and_voice = 4; // 用户打开麦克风和语音上报
    UserLocationFlashChatCpToday user_location_flash_chat_cp_today = 5; // 今日CP和即时闪聊用户定位数据上报
    SocialCommunityChannelTask social_community_channel_task= 6; // 社群的房间任务
    SocialCommunityOpenMicTask social_community_open_mic_task=7; // 社群的开麦任务
  }


}

message MuseMicUserInfo{
  uint32 uid =1;
  bool open_mic=2;  //开麦状态
}

message MuseCommonReportResponse{
  BaseResp base_resp = 1;
  bool is_stop_report = 2; // 是否停止上报
}

// 兴趣内容相关 开关合集
message GetMuseSwitchHubRequest{
  ga.BaseReq base_req = 1;
}

enum SwitchHubType{
  SWITCH_HUB_TYPE_UNSPECIFIED = 0;
  SWITCH_HUB_TYPE_SAME_CITY_VISIBLE = 1; // 隐私中 房间同城信息对他人可见
  SWITCH_HUB_TYPE_CHANNEL_ALLOCATE = 2; // 隐私中 【主题房-房间下发】
  SWITCH_HUB_TYPE_AGE_TAG=3;//隐私中  年龄标签是否隐藏

}

message GetMuseSwitchHubResponse{
  ga.BaseResp base_resp = 1;
  map<uint32, bool> is_open_map = 2; // SwitchHubType true：开 false：关
}

message SetMuseSwitchHubRequest{
  ga.BaseReq base_req = 1;
  uint32 switch_type = 2; /* SwitchHubType */
  bool is_open = 3; // true 开
}

message SetMuseSwitchHubResponse{
  ga.BaseResp base_resp = 1;
}

// 麦上用户城市信息
message UserCityInChannel{
  uint32 uid = 1;
  string city_name = 2;
}

message TopicChannelSameCityInfo{
  string city_name = 1;
  repeated UserCityInChannel user_city_list = 2;
}

message CheckJumpSquarePageRequest {
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
}

message CheckJumpSquarePageResponse {
  ga.BaseResp base_resp = 1;
  bool need_jump = 2;
}

// 获取MT实验策略内容
message GetMTExperimentStrategyRequest {
  ga.BaseReq base_req = 1;
  repeated uint32 strategy_list = 2; // STRATEGY_TYPE
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum STRATEGY_TYPE{
  STRATEGY_TYPE_UNSPECIFIED = 0;
  STRATEGY_TYPE_INVITE_IN_CHANNEL_MT = 1; // 房间内邀请气泡&邀请列表
  STRATEGY_TYPE_HOME_MIX_CHANNEL_RCMD = 2; // 首页混推流前两位推荐好友房间
  STRATEGY_TYPE_HOME_FOLLOW_FLOAT = 3; // 首页跟随进房浮层优化
  STRATEGY_TYPE_HOME_PGC_RCMD_CHANNEL_HIDE = 4; // 首页混推流PGC推荐位隐藏
  STRATEGY_TYPE_PUBLIC_SCREEN_QUICK_MSG = 5;   // 公屏快捷消息
  STRATEGY_TYPE_PUBLIC_SCREEN_SEND_EMOJI_PACK = 6;   // 公屏支持发送表情包
  STRATEGY_TYPE_PUBLIC_SCREEN_MSG_PLUS_ONE = 7;  // 优化“公屏消息+1”
  STRATEGY_TYPE_PUBLIC_SCREEN_GREETING_MSG = 8; // 优化打招呼、欢迎消息
  STRATEGY_TYPE_AUTO_INVITE_ON_MIC = 9;
  STRATEGY_TYPE_ONE_CLICK_INVITE_ON_MIC = 10;
  STRATEGY_TYPE_CHANNEL_GUIDE_FOLLOW=11; //引导关注
//  STRATEGY_TYPE_NEW_HOT_GAME = 12; // 新热聊挑战
  STRATEGY_TYPE_HOME_MIX_SAME_TYPE_CHANNEL_INSERT=12;//首页混推流房间强插
  STRATEGY_TYPE_GUIDE_ON_MIC = 13; // 进房抱上麦
  STRATEGY_TYPE_USER_MEGAPHONE = 14; // 用户喊话功能
  STRATEGY_TYPE_ICE_BREAKER_POPUP=15; // 破冰弹窗
  STRATEGY_TYPE_SHINING_POINT = 16; // 闪光点
  STRATEGY_TYPE_TODAY_COUPLE = 17; // 今日CP
  STRATEGY_TYPE_CHANNEL_PREFERENCE_KEYWORD=18; //房间偏好关键词
  STRATEGY_TYPE_CHANNEL_CATEGORY_TYPE=19; //隐藏房间分类
  STRATEGY_TYPE_PUBLIC_SCREEN_USER_BASE_MSG=20;//用户进房公屏消息新增用户基础信息
  STRATEGY_TYPE_PUBLIC_SCREEN_SHORTCUT_IMAGE=21;//公屏图片附着快捷表情消息
  STRATEGY_TYPE_PUBLIC_ICE_BREAKER_POPUP=22; // 发布破冰弹窗
  STRATEGY_TYPE_NEW_VERSION_ICE_BREAKER_POPUP=23; // 新版破冰弹窗
  STRATEGY_TYPE_ICE_BREAKER_POPUP_PUSH=24; // 破冰推送
  STRATEGY_TYPE_AI_CUPID=25; // AI红娘
  STRATEGY_TYPE_AI_Inspiration=26; // AI灵感回复
  STRATEGY_TYPE_FLASH_CHAT=27; // 即时闪聊
  STRATEGY_TYPE_ROLE_PLAY = 28; // 角色扮演
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum STRATEGY_TYPE_VALUE{
  STRATEGY_TYPE_VALUE_UNSPECIFIED = 0;
  STRATEGY_TYPE_VALUE_CLOSE = 1; // 关闭
  STRATEGY_TYPE_VALUE_OPEN = 2; // 开启
}

message GetMTExperimentStrategyResponse {
  ga.BaseResp base_resp = 1;
  map<uint32, uint32> strategy_value_map = 2; // STRATEGY_TYPE_VALUE
  uint32 next_update_internal = 3; // 下次更新时间间隔
}


// 获取优先展示的首页跟随进房信息
message GetHomeFollowFloatRequest {
  ga.BaseReq base_req = 1;
  repeated FollowUserChannelInfo uid_list = 2 [deprecated = true]; // 在房好友用户uid列表;
  repeated FollowUserChannelInfoV2 uid_list_v2 = 3; // 在房好友用户uid列表 ;
}

message UserSimpleInfo{
  uint32 uid = 1;
  string account = 2;
  string nickname = 3;
  uint32 gender = 4;
  uint32 follow_status=5; //see FOLLOW_STATUS_TYPE
}

message GetHomeFollowFloatResponse {
  ga.BaseResp base_resp = 1;
  UserSimpleInfo user = 2;
  string find_playing_text = 3; // “扩列中” tabs里的findPlayingText
}

message FollowUserChannelInfo{
  uint32 uid = 1;
  string channel_id = 2;
}

message FollowUserChannelInfoV2{
  uint32 uid = 1;
  uint32 channel_id = 2;
}

// 跟随进房半屏页用户列表
message FollowChannelHalfScreenUserListRequest {
  ga.BaseReq base_req = 1;
  repeated FollowUserChannelInfo uid_list = 2 [deprecated = true]; // 在房好友用户uid列表;
  repeated FollowUserChannelInfoV2 uid_list_v2 = 3; // 在房好友用户uid列表 ;
}

// 自定义认证
message InterestHubPersonalCert{
  string icon = 1;
  string text = 2;
  repeated string color = 3;
  string text_shadow_color = 4;
}

// 房间玩法信息
message FollowFriendsChannelSimpleInfo{
  uint32 channel_id = 1;
  string channel_name = 2;
  string channel_tab_icon = 3; // 房间tab图标
  string channel_tab_text = 4; // 房间tab文案
  uint32 member_count = 5; // 房间人数
  bool is_member_count_red = 6; // 是否标红
}

message FriendRcmdInfo{
  string rcmd_text = 1; // 推荐文案
}

message UserSimpleMegaphoneInfo {
  string megaphone_id = 1;
  string content = 2;
}

message FollowChannelHalfScreenUserList {
  UserSimpleInfo user = 1;
  InterestHubPersonalCert personal_cert = 2; // 自定义认证
  FollowFriendsChannelSimpleInfo channel_info = 3; // 房间玩法信息
  FriendRcmdInfo rcmd_info = 4; // 推荐文案
  UserSimpleMegaphoneInfo user_megaphone_info = 5; // 用户喊话信息
}

message FollowChannelHalfScreenUserListResponse {
  ga.BaseResp base_resp = 1;
  repeated FollowChannelHalfScreenUserList user_list = 2;
}

message ChannelInviteFriendPreprocessingRequest {
  ga.BaseReq base_req = 1;
  uint32 friend = 2;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  uint32 channelId = 3;
}

message ChannelInviteFriendPreprocessingResponse {
  ga.BaseResp base_resp = 1;
}

message ChannelSpecialFriendListRequest {
  ga.BaseReq base_req = 1;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  uint32 channelId = 2;
}

message ChannelSpecialFriendListResponse {
  ga.BaseResp base_resp = 1;
  string tips = 2;
  repeated uint32 uid_list = 3;
}

message OneClickInviteAllInfoRequest {
  ga.BaseReq base_req = 1;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  uint32 channelId = 2;
}

message OneClickInviteAllInfoResponse {
  ga.BaseResp base_resp = 1;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  uint32 channelId = 2;
  uint32 remain_count = 3;
  string tips = 4;
  string background = 5;
}

message OneClickInviteAllRequest {
  ga.BaseReq base_req = 1;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  uint32 channelId = 2;
}

message OneClickInviteAllResponse {
  ga.BaseResp base_resp = 1;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  uint32 channelId = 2;
  uint32 remain_count = 3;
}

message OneClickInviteMsg {
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  uint32 channelId = 1;
  string context = 2;
  uint32 show_seconds = 3; // 展示多久
}


message CheckIsInOtherUsersBlackListRequest{
  BaseReq base_req = 1;
  repeated uint32 uids=2;
}

message CheckIsInOtherUsersBlackListResponse{
  BaseResp base_resp = 1;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  map<uint32,bool> blackList_user_map=2;
}


message GetUserCurrentChannelIdRequest{
  BaseReq base_req = 1;
  repeated uint32 uids=2;
}

message GetUserCurrentChannelIdResponse{
  BaseResp base_resp = 1;
  map<uint32 ,uint32> user_current_channel_map=2;

}

message OneClickInviteAllGetMicIdRequest {
  ga.BaseReq base_req = 1;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  uint32 channelId = 2;
  uint32 begin_mic_id = 3;
  uint32 end_mic_id = 4;
}

message OneClickInviteAllGetMicIdResponse {
  ga.BaseResp base_resp = 1;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  uint32 channelId = 2;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  uint32 micId = 3;
  string mic_token = 4;
}

message AutoInviteMicPanelRequest {
  ga.BaseReq base_req = 1;
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  uint32 channelId = 2;
  uint32 manager_uid = 3;
}

message AutoInviteMicPanelResponse {
  ga.BaseResp base_resp = 1;
  UserSimpleInfo owner = 2;
  string channel_owner_role = 3;
  repeated string tags = 4;
}

enum MuseCommonXMLMsgType {
  MUSE_COMMON_XML_MSG_TYPE_UNSPECIFIED = 0;
  MUSE_COMMON_XML_MSG_TYPE_COMMON_MSG = 1; // 通用xml消息
  MUSE_COMMON_XML_MSG_TYPE_HOT_GAME_FLOW_WEIGHT_ENTER_CHANNEL_TIPS = 2; // 热聊挑战流量扶持进房提示
}

enum MuseCommonXMLMsgContentType {
  MUSE_COMMON_XML_MSG_CONTENT_TYPE_UNSPECIFIED = 0;
  MUSE_COMMON_XML_MSG_CONTENT_TYPE_HOT_GAME_SYSTEM_AWARD = 1; // 恭喜！当前热聊值达到X，获得系统流量奖励
  MUSE_COMMON_XML_MSG_CONTENT_TYPE_HOT_GAME_FLOW_WEIGHT_ENTER_CHANNEL_TIPS = 2; // 热聊挑战流量扶持进房提示
  MUSE_COMMON_XML_MSG_CONTENT_TYPE_SOCIAL_COMMUNITY_LIKE_ACTIVITY = 3; // 社群点赞活动
}

// 兴趣内容通用xml消息通知
message MuseCommonXMLMsgNotify {
  uint32 msg_type = 1; // 消息类型 MuseCommonXMLMsgType
  string xml_msg = 2; // xml消息
  uint32 channel_id = 3; // 房间id
  uint32 msg_content_type = 4; // 消息内容类型 MuseCommonXMLMsgContentType 用来区分，上报
}

message HoldUserOnMicMsg {
  UserSimpleInfo target = 1;
  string context = 2;
}

message GetUserRelationshipRequest {
  ga.BaseReq base_req = 1;
  uint32 uid = 2;
  uint32 target_uid = 3;
}

message GetUserRelationshipResponse {
  ga.BaseResp base_resp = 1;
  bool is_follow_target = 2;
  bool target_is_follow = 3;
  string relation_text = 4;
  bool is_hit_rule = 5; // 是否实验组
}

message NonMicUserListOrderRequest {
  ga.BaseReq base_req = 1;
  repeated uint32 non_mic_uids = 2;
  repeated uint32 invite_uids = 3;
  uint32 channel_id = 4;
}

message NonMicUserListOrderResponse {
  ga.BaseResp base_resp = 1;
  message User {
    UserSimpleInfo user_info = 1;
    string channel_role_text = 2;
    string reunion_text = 3;
    string friend_text = 4;
    bool in_abtest = 5;
  }
  repeated User non_mic_users = 2;
  repeated User invite_users = 3;
}


message ShowRoomExitFollowPopupRequest{
  ga.BaseReq base_req = 1;
  repeated uint32 uids=2;
}

message ShowRoomExitFollowPopupResponse{
  ga.BaseResp base_resp = 1;
  bool show_follow_popup=2;     //是否展示关注弹窗
  // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
  int32 DailyPopupLimit=3; //每日弹窗最大值
}


message GetRoomExitGuideFollowListRequest{
  ga.BaseReq base_req = 1;
  repeated uint32 uids=2;
  uint32 channel_id=3;
}

message GetRoomExitGuideFollowListResponse{
  ga.BaseResp base_resp = 1;
  repeated GuideFollowUser guide_follow_user=2;
}

message GuideFollowUser{
  UserSimpleInfo user_info = 1;  //用户信息
  AdminInfo admin=2;             //用户权限
  repeated string tags=3;  //用户标签
  string relation_text=4;   //用户重逢关系

}



message AdminInfo{
  string admin_text=1;
  uint32 admin_role=2;     // see CHANNEL_ADMIN_TYPE
}


// buf:lint:ignore ENUM_PASCAL_CASE
enum CHANNEL_ADMIN_TYPE{
  CHANNEL_ADMIN_TYPE_UNSPECIFIED=0;
  CHANNEL_ADMIN_TYPE_OWNER=1;       //房主
  CHANNEL_ADMIN_TYPE_ADMIN=2;       //管理员
  CHANNEL_ADMIN_TYPE_NORMAL=3;      //普通用户
  CHANNEL_ADMIN_TYPE_SUPER=4;       //超管
  CHANNEL_ADMIN_TYPE_CAPTAIN=5;     //主理人
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum FOLLOW_STATUS_TYPE{
    FOLLOW_STATUS_TYPE_UNSPECIFIED=0;//未关注
    FOLLOW_STATUS_TYPE_FOLLOW=1;   //关注
    FOLLOW_STATUS_TYPE_FOLLOWED=2;  //被关注（回关）
}




message GetRoomIcebreakerPopupRequest{
  ga.BaseReq base_req = 1;
  uint32 channel_id=2;
  uint32 owner_uid=3;
}

message QnAPair{
  string question=1;
  repeated string answer=2;
  repeated AnswerInfo answer_info=3;
}

message AnswerInfo{
  int32 id=1;
  string name=2;
}


message GetRoomIcebreakerPopupResponse{
  ga.BaseResp base_resp = 1;
  QnAPair qna_pair=2;
  string location_tag=3;
  string releation_tag=4;
  int32 age=5  [deprecated = true];
  bool owner_in_room=6;
  uint32 follow_status=7 [deprecated=true]; //see FOLLOW_STATUS_TYPE
  string birthday=8;
  bool   is_follow_target=9;       //是否关注了目标
  bool   is_followed_by_target=10;  //是否被目标关注
  bool   custom_icebreaker_switch_status=11;
  int32 ice_breaker_question_type=12;   //IceBreakerQuestionType
}

message PushLikeActivityMsg{
    string like_activity_url=1;
    string social_community_id=2;
}


message SendPublicMessageDirectlyRequest{
  ga.BaseReq base_req = 1;
  uint32 from_uid = 2;
  uint32 channel_id = 3;
  string content=4;

}

message SendPublicMessageDirectlyResponse{
    ga.BaseResp base_resp = 1;
}

message GetPublicScreenShortcutImageRequest{
  ga.BaseReq base_req = 1;
}

message GetPublicScreenShortcutImageResponse{
  ga.BaseResp base_resp = 1;
  repeated ShortcutImageMsg shortcut_image_list = 2;  //快捷表情信息
}

message ShortcutImageMsg{
  string image_url = 1;
  string image_text = 2;
}


message GetHiddenHomePageZoneConfigRequest{
  ga.BaseReq base_req = 1;
}

message GetHiddenHomePageZoneConfigResponse{
  ga.BaseResp base_resp = 1;
  repeated string zone_ids =2;
}

message GetHiddenChannelCategoryTypeConfigRequest{
  ga.BaseReq base_req = 1;

}

message GetHiddenChannelCategoryTypeConfigResponse{
  ga.BaseResp base_resp = 1;
  repeated uint32 category_ids=2;
}


message GetMuseRecommendEmojisRequest{
  ga.BaseReq base_req = 1;
  uint32 req_source=2;         //im.proto MsgSourceType   MSG_SOURCE_FROM_TODAY_CP=111
}

message GetMuseRecommendEmojisResponse{
  ga.BaseResp base_resp = 1;
  repeated CommonEmoji emojis = 2; // 表情列表
}

message CommonEmoji{
  string emoji_id = 1; // 表情包id
  string md5 = 2; // 表情包md4
  BaseEmoji origin_emoji_info = 3; // 原图表情
  BaseEmoji thumb_emoji_info = 4; // 缩略图表情
  bool is_cache = 5; // 是否缓存
  string obs_key = 6; // obs key
}

// 表情信息
message BaseEmoji {
  string url = 1; // 图url
  uint32 height = 2; // 高度
  uint32 width = 3; // 宽度
}


message GetCustomIcebreakerPopupSwitchRequest{
  ga.BaseReq base_req = 1;
  uint32 channel_id=2;
}

message GetCustomIcebreakerPopupSwitchResponse{
  ga.BaseResp base_resp = 1;
  bool switch_status=2;
  string question=3;
  string intro=4;
  bool is_already_set=5;
  IceBreakerPublicSwitchInfo ice_breaker_public_switch_info=6;
  int32 ice_breaker_question_type=7;   //IceBreakerQuestionType
}


message SetCustomIcebreakerPopupSwitchRequest{
  ga.BaseReq base_req = 1;
  bool switch_status=2;
  uint32 channel_id=3;
}

message SetCustomIcebreakerPopupSwitchResponse{
  ga.BaseResp base_resp = 1;
}

message UpdateCustomIcebreakerPopupRequest{
  ga.BaseReq base_req = 1;
  QnAPair qna_pair=2;
  uint32 channel_id=3;
}

message UpdateCustomIcebreakerPopupResponse{
  ga.BaseResp base_resp = 1;
  string question=2;
  bool switch_status=3;
  bool is_already_set=4;
  IceBreakerPublicSwitchInfo ice_breaker_public_switch_info=5;
}



message GetCustomIcebreakerConfigInfoRequest{
  ga.BaseReq base_req = 1;
  uint32 channel_id=2;
}

message GetCustomIcebreakerConfigInfoResponse{
  ga.BaseResp base_resp = 1;
  QnAPair current_qna_pair=2;
  repeated string config_question=3;
  repeated string config_answer=4;
  uint32 configurable_answers_number=5;
  uint32 recommended_answers_display_number=6;
  repeated AnswerInfo config_answer_info=7;
  int32 ice_breaker_question_type=8;   //IceBreakerQuestionType
  bool is_already_set=9;
}

message IceBreakerPushMsg{
   int32 source=1;
   string content=2;

}

enum IceBreakerPushSource{
  ICE_BREAKER_PUSH_SOURCE_UNSPECIFIED=0;
  ICE_BREAKER_PUSH_SOURCE_PUBLIC_CHANNEL=1;
  ICE_BREAKER_PUSH_SOURCE_AUDIT_REJECT=2;
  ICE_BREAKER_PUSH_SOURCE_OWNER_ENTER_CHANNEL=3;
}

message IceBreakerPublicSwitchInfo{
  bool switch_status=2;
  string question=3;
  string intro=4;
  bool is_already_set=5;
}

enum IceBreakerQuestionType{
  ICE_BREAKER_QUESTION_TYPE_UNSPECIFIED=0;
  ICE_BREAKER_QUESTION_TYPE_SYSTEM_QUESTION=1;
  ICE_BREAKER_QUESTION_TYPE_CUSTOM_QUESTION=2;
}


