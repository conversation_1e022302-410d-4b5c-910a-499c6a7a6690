syntax="proto2";

package ga.backpack;
import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/backpack";

enum PackageItemType
{
    UNKNOW_ITEM_TYPE=0;
    BACKPACK_PRESENT=1;                            // 礼物
    BACKPACK_CARD_RICH_EXP=2;                      // 财富经验卡
    BACKPACK_CARD_CHARM_EXP=3;                     // 魅力经验卡
    BACKPACK_LOTTERY_FRAGMENT = 4;                 // 抽奖碎片,砸蛋获得
    BACKPACK_CARD_RICH_INCR = 5;                   // 财富卡 (直接增加财富值)
    BACKPACK_CARD_KNIGHT    = 6;                   // 骑士体验卡
}

// 功能卡片配置
message FuncCardCfg
{
    required uint32 card_id = 1;                   // 卡片id      
    required uint32 card_type = 2;                 // 卡片类型
    required string card_name = 3;                 // 卡片名称
    required string card_url = 4;                  // 卡片图片url
    required uint32 card_times = 5;                // 卡片倍数
    required uint32 valid_time = 6;                // 有效时间
    optional string card_desc = 7;                 // 卡片描述
    optional uint32 is_del = 8;                    // 是否已删除
    optional uint64 card_value = 9;                // 数值（财富卡）
    optional bool rich_lock = 10;                  // 是否开启财富值开关
}

enum FragmentType
{
    UNKNOW_FRAGMENT_TYPE = 0;
    ENERGY_STONE_TYPE= 1; //
    GLORY_WORLD_TYPE = 2; // 荣耀世界碎片
}

// 背包中的碎片
message FragmentCfg
{
    required uint32 fragment_id = 1;
    required uint32 fragment_type = 2;              // 预留字段，填0; enum FragmentType
    required string fragment_name = 3;
    required string fragment_desc = 4;
    required string fragment_url = 5;
    required uint32 is_del = 6;
    required uint32 fragment_price = 7;             // 后台配置的价格
    optional uint32 fragment_price_type = 8;        // see ga::PRESENT_PRICE_TYPE
    optional uint32 is_show_expire_hint = 9; //是否展示过期提醒
}




// 用户背包项
message UserPackageItem
{
    required uint32 item_type = 1;                  // 背包物品类型
    required uint32 user_item_id = 2;               // 用户背包物品唯一标识
    required uint32 item_count = 3;                 // 背包物品数量
    required uint32 fin_time = 4;                   // 截止使用时间
    optional uint32 present_id = 5;                 // 背包礼物id
    optional FuncCardCfg card = 6;                  // 背包卡片
    optional FragmentCfg fragment = 7;              // 背包碎片
    optional string  backup_text= 8;                // 兜底文案
    optional uint32 obtain_time=9;                  // 物品获取时间
    optional uint32 business_type = 10;             // T豆包裹类型 PresentBusinessType
}

// 获取用户背包物品
message GetUserBackpackReq
{
    required BaseReq base_req = 1;
}

message GetUserBackpackResp
{
    required BaseResp base_resp = 1;
    repeated UserPackageItem user_backpack_list = 2;
    optional int64 last_gain_item_ts = 3;        // 最后获得物品的时间

    repeated UserPackageItem user_backpack_detail_list = 4; //道具详情列表
}

// 使用背包卡片
message UseFuncCardReq
{
    required BaseReq base_req = 1;
    required uint32 card_type = 2;
    required uint32 card_id = 3;
    required uint32 user_item_id = 4;           // 唯一标识用户背包物品
    optional uint32 use_count = 5;              // 使用数量, 默认1
    optional uint32 target_uid = 6;             // 目标uid
    optional uint32 channel_id = 7;             // 房间ID
}
message UseFuncCardResp
{
    required BaseResp base_resp = 1;
    required uint32 user_item_id = 2;
    required uint32 remain = 3;
    optional string success_info=4;
}

// 获取正在使用的背包卡片
message GetUserFuncCardUseReq
{
    required BaseReq base_req = 1;
    optional uint32 viewed_uid = 2;            // 被查看用户的uid
}

message GetUserFuncCardUseResp
{
    required BaseResp base_resp = 1;
    repeated FuncCardCfg card_list = 2;
}

message GetUserFragmentReq
{
    required BaseReq base_req = 1;
    /*optional uint32 viewed_uid = 2;            // 被查看用户的uid*/
}

message GetUserFragmentResp
{
    required BaseResp base_resp = 1;
    repeated UserPackageItem item_list = 2;
    optional int64 last_gain_item_ts = 3;        // 最后获得物品的时间

    repeated UserPackageItem item_detail_list = 4; //道具详细信息列表
}


//获取背包礼物配置

message GetImBackpackReq
{
    required BaseReq base_req = 1;
}

message GetImBackpackResp
{
    required BaseResp base_resp = 1;
    repeated UserPackageItem item_list = 2;
    optional int64 last_gain_item_ts = 3;       // 最后获得物品的时间

    repeated UserPackageItem item_detail_list = 4; //道具明细信息，没有合并（新增）
}