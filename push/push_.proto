syntax="proto2";

package ga.push;

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/push";

import "ga_base.proto";

//------------------------push message--------------------------------
message PushMessage{
    enum CMD_TYPE {
        PULL_LOG = 1;
        CIRCLE_UPDATE = 2;
        CHANNEL_MSG = 3;               // 房间带seq的广播消息 协议定义在 channel_.proto ChannelMsg
        BULLETIN = 5;                  //运维公告
        TT_ACTIVITY_MULTI_MESSAGE = 6;	// tt活动推送消息
        TT_COMMON_SYSTEM_MULTI_MESSAGE = 7;	// tt通用系统栏消息通知 TTCommonSystemMultiMessage
        RAID_RICHES_UPDATE = 8;
        CHANNEL_CONVENE = 9;	    // 频道召集
        ONLINE_EVENT_PUSH = 10;	    // 在线事件通知 包含的协议定义在 friendol_.proto OnlineEventPushMsg
        MESSAGE_READ_BY_PEER = 11;  // 消息被对方已读通知, 对应MessageReadByPeerMessage(in im.proto)

        CHANNEL_MSG_BRO = 12;   // 房间广播消息 协议定义在 channel_.proto ChannelBroadcastMsg
		USERRECOMMEND_MSG = 13;	// 用户推荐 消息推送
		PRESENT_MSG = 14;		// 用户收到礼物的个人消息推送

        TTLIVE_PUBLISHING_MSG = 15;  // 直播通知

		PRESENT_BREAKING_EVENT= 16;	 // 礼物大事件 礼物大喇叭 用于全网推送 ChannelPresentBreakingNews
		MY_HEADWEAR_MSG = 17;	     // 我的头像框装饰事件 当用户新获得头像框 或者 头像框升级时 推送给目标用户 MyHeadwareMsg
		COMMON_BREAKING_EVENT= 18;	 // 通用大事件 大喇叭 用于全网推送 CommonBreakingNews

        QUICK_MATCH_RESULT_MSG = 19; // 快速匹配结果通知 find_friends_.proto QuickMatchResultNotification

        TBEAN_UPDATE_MSG = 20;          // 用户T豆变化

		TT_NOTIFY_BAR_MESSAGE = 21;	// tt通知栏 通知 TTNotifyBarMessage

		GAME_RECRUIT_INVITE = 22;	//游戏招募邀请(content消息里面包含了team_.proto里 GameRecruitDetail定义的内容)

		GAME_RECRUIT_MEMBER_CHANGE = 23; // 队员变更通知 (content消息里面包含了team_.proto里 GameRecruitMemberChange定义的内容)

		GAME_RECRUIT_INTO_CHANNEL = 24; //  you房间跳转通知(content消息里面包含了team_.proto里 GameRecruitChannelInfo定义的内容)

        TBEAN_RECHARGE_MSG = 25;        // 首充活动

		COMMON_POP_UP_MSG = 26;	// 通用弹窗消息(content消息里面包含了 CommonPopUpMsg 定义的内容)

        DATING_BREAKING_EVENT= 27;	    // 相亲游戏大事件 礼物大喇叭 用于全网推送 DatingBreakingNews
        RICH_CHARM_RANKING_CHANGE = 28; // 我的 财务榜/魅力榜 变化 RankChangeMsg
		COMMON_BREAKING_EVENT_V2 = 29;	// 通用全服 大喇叭v2 用于全网推送 CommonBreakingNewsV2

		COMMON_WEB_ACTIIVE_BREAKING_REPORT = 30; // 全网活动 事件播报  CommonWebActiveBreakingReport
		COMMON_WEB_ACTIIVE_BREAKING_TOPN = 31;	 // 全网活动 排名播报  CommonWebActiveBreakingTopN

		MY_DECORATION_MSG = 32;	     // 我的房间装饰事件 当用户新获得房间坐骑时 推送给目标用户 MyDecorationMsg

        KNOCK_PUSH = 33;   //敲门时推送给客户端

	    COMMON_BREAKING_EVENT_V3 = 34;	// 通用全服 大喇叭v3 用于全网推送 CommonBreakingNewsV3

        SMASH_EGGS_BREAKING_EVENT = 35;	// 魔力转转大事件

        YEAR_ACT_POP_UP_MSG = 36;  // 年度盛典活动弹窗  YearActPopUpMsg
        YEAR_ACT_RANKING_LIST_MSG = 37; // 年度盛典榜单排名信息 YearActRankingListMsg
        ACT_RANKING_LIST_MSG = 38;       // 活动榜单排名推送信息 PushRankingListMsg
        // 年度盛典预留

        FOLLOW_LABEL_UPDATE = 40;  // 跟随标签变化 friendol_.proto: message FollowLabelUpdate
        NEW_FOLLOWER_MSG = 41;     // 新的粉丝通知 ugc_.proto: NewFollowerMessage

        Channel_Follow_Switch_Tag = 42;// 频道跟随切换玩法 channel_opt_.proto SwitchChannelTagMsg

        CHANNEL_LIVE_STATUS_PUSH = 43; //直播开播push
        CHANNEL_LIVE_FANS_STATUS_PUSH = 44;   // 语音直播粉丝铭牌重新点亮推送
        CHANNEL_LIVE_OPEN_PERMISSION = 45; //直播房权限变化，开通，关闭 ChannelLivePermissionPush

        MASKED_CALL_MATCH_RESULT = 46; // 蒙面通话匹配结果推送，消息的content字段中会携带masked-call_.proto中定义的
        CHANNEL_LIVE_PK_APPLY_PUSH = 47  ;// PK申请相关推送，关联  channel_live_logic_.PkApplyPushMsg结构

        INTERACTION_INTIMACY_UPDATE = 48; // 通知更新亲密度，消息的content字段中会携带interaction-intimacy_.proto中定义的InteractionIntimacyUpdateMsg

        USER_TERMINAL_ONLINE_STATUS = 49; //用户终端在线状态

        ANTISPAM_VIOLATE_PUSH = 50; //昵称头像违规推送

        HUNT_MONSTER_PUSH = 51; //打boss服务奖励相关

        FARM_RED_PUSH = 52; //庄园游戏小红点

        HUNT_MONSTER_PROPS_POP_UP_MSG = 53;  // 打龙获得道具弹窗 HuntMonsterPropsPopUpMsg，废弃
        HUNT_MONSTER_PROPS_POP_UP_MSG_v2 = 54;  // 打龙获得道具弹窗 HuntMonsterPropsPopUpMsg
        HUNT_MONSTER_PUSH_v2 = 55; //打boss服务奖励相关
        GAME_RADAR_INVITE_PLAY = 56; //游戏雷达约玩通知

        CHANNEL_LEVEL_EXP_CHANGE = 57; // 房间经验变更，推给房主，消息的content字段中会携带channel-level_.proto中定义的ChannelExpChangeMsg

        UDESK_UNREAD_MSG = 58;//udesk未读信息push 消息的content字段中会携带UdeskUnreadPush内容

        INVITE_FROM_CHANNEL = 59; //5.5.7新增的在房间内邀请进房的 content是   InviteFromChannelMsg
        REPLY_INVITE_FROM_CHANNEL = 60; //5.5.7新增的 在房间内邀请进房的的回应  content是   ReplyInviteFromChannelMsg

        INVITE_PLAYER_ONLINE = 61;//5.5.7新增的 邀请的玩伴在线触发的提示 content是 InvitePlayerOnlineMsg


        SUPER_PLAYER_MSG = 62; //超级会员push; super-player-logic_.proto SuperPlayerInfoPushMsg

        USER_CHAT_CARD_STATUS = 63; // 扩列卡片状态

        SUPER_PLAYER_SPECIAL_CONCERN_CHANGE = 64; // 超级会员，特别关心用户变动推送；super-player super-player-logic_.proto SpecialConcernPushMsg

        USER_PRESENT_AREA_INFO_UPDATE = 65;  // 用户礼物墙信息更新推送

        SUPER_CHANNEL_CREATE = 66; // 活动大房创建推送

        SLIP_NOTE = 67; // 纸条
        SLIP_NOTE_EXAMINE_FAILED = 68; // 纸条审核失败
        SLIP_NOTE_COMMENT = 69; // 纸条评论
        USER_CHAT_CARD_MSG_COUNT = 70; // 扩列卡片消息数量
        BALANCE_CHANGE = 71; // 红钻/t豆余额变动

        CHANNEL_LIVE_APPOINT_PK_PUSH_EVENT = 72;     // 语音直播指定pk事件推送 channel-live-logic_.proto AppointPkEvent

      CHANNEL_RED_PACKET_AWARD = 73;  // 房间礼物红包奖励推送 see channel-red-packet-logic_.proto RedPacketAwardOpt
      AWARD_SING_IMAGE_TO_USER = 74;  // 抢唱活动新解锁形象 third-party/tt-protocol/app/sing-a-round-logic_.proto  UserNewImageUnlockNotify

      FELLOW_INVITE_MSG = 75;  // 房间绑定挚友 - 邀请函推送  see fellow-logic_.proto FellowInviteInfo
      FELLOW_RESULT_MSG = 76;  // 房间绑定挚友 - 结果推送  see fellow-logic_.proto FellowInviteInfo
      FELLOW_CHANGE_MSG = 77;  // 挚友关系变化推送  see fellow-logic_.proto FellowInfo
      APP_REPORT_AUDIO_MSG = 78;  // 客户端上报音频文件  see third-party/tt-protocol/app/sing-a-round-logic_.proto AppReportAudioNotify
      CHANNEL_GUIDE_CARTEAM_PUSH = 79;   //车队推送给客户端

      DARK_GIFT_BONUS_MSG = 80; // 黑暗礼物奖励（概率玩法保底奖励）推送，see dark-gift-bonus-logic_.proto DarkGiftBonusNotifyOpt
      CHANNEL_GUIDE_CARTEAM_ENTER_ROOM_PUSH = 81;   //车队进房推送给客户端
	  CHANNEL_ROLE_PUSH = 82; // 角色扮演房间-角色信息推送
      GROUP_ANNOUNCEMENT = 83;//群组公告的push
      DING_CALL_NOTIFY = 84;//召集令的打电话通知
      DING_ENTER_ROOM_NOTIFY = 85;  //召集令的强弹窗通知

	    DING_NEW_MSG_NOTIFY = 86; // 闪电召集-新通知消息推送
	    DING_AUDIT_MSG = 87; // 闪电召集-审核消息推送

	    MYSTERY_BOX_TASK_NOTIFY = 88; // 盲盒任务推送

      STAR_TREK_AWARD_RESULT_NOTIFY = 89;// 星级巡航奖励通知 see star-trek-logic_.proto StarTrekResultOpt

      COMMON_POP_UP_WINDOW_PUSH = 90;    // 统一端内弹窗推送
      VOTE_PK_ADD_USER_TICKET = 91; /* 房间投票增加额外票数推送 AddVotePkUserTicketNotify */
      VOICE_WATERMARK_RESULT_NOTIFY = 92; /* 声音水印结果推送 rhythm_.proto VoiceWatermarkResultNotify */
      VOICE_PARTIAL_QUALITY_NOTIFY = 93; /* 中台返回的声音质量分数&干音音频 rhythm_.proto VoicePartialQualityNotify */
      RAPPER_VOICE_NOTIFY = 94; /* 中台返回的说唱音频 rhythm_.proto RapperVoiceNotify */
      COMMON_TEXT_TOAST_PUSH = 95; // 纯文本Toast推送
      VIP_KEFU_PUSH = 96; // vip客服事件推送 see udesk-api-logic_.proto VipKefuPush 内容
      UKW_PERMISSION_CHANGE_PUSH = 97; // 神秘人变更推送 you-know-who-logic_.proto UKWPermissionChangeMsg
      UKW_SHOW_UP_MSG_PUSH     =  98; //神秘人互动消息推送 you-know-who-logic_.proto ShowUpMsg

        COMMON_POP_UP_WINDOW_ANNOUNCEMENT_PUSH = 99; // 统一弹窗公告通知推送

      PRESENT_EFFECT_TIME_CHANGE_PUSH = 100; // 限时礼物下架时间变动通知  present-go-logic_.proto
      TOPICCHANNEL_PUSH = 101; //发布主题房推送
      PRESENT_CUSTOM_PRESENT_CHANGE_PUSH = 102; // 专属礼物属性变更通知  present-go-logic_.proto CustomizedPresentChange

      ENTER_ROOM_APPLY_SUC_PUSH = 103;// 申请进房后，限定时间内同意的，通知用户进房
      FOLLOW_ENTER_ROOM_OWNER_PUSH = 104;// 跟随进房 房主侧push 结构 FollowEnterRoomOwnerPush

      PC_AUTH_APPLY_PUSH = 105; // PC登录申请通知 auth.proto PcAuthApplyInfo

      TREASURE_BOX_AWARD_PUSH = 106; // 活动宝箱奖励弹窗推送 treasure-box-logic_.proto BoxAwardResultOpt
      TREASURE_BOX_SUMMARY_AWARD_PUSH = 107; // 活动宝箱奖励汇总推送 treasure-box-logic_.proto BoxAwardSummaryOpt

      ACTIVITY_POP_WINDOW_PUSH = 108; // 活动弹窗通知 activity-push_.proto ActivityPopWindowBaseChannelMsg

      INVITE_2_MY_ROOM_RESULT_PUSH = 109; // 邀请别人来我的密逃房，结果通知 mystery-place-logic_.proto Invite2MyRoomResultPush
      COMMON_INVITATION_PUSH = 110; // 通用邀请函推送 interact-proxy-logic_.proto CommonInvitationPush
      DECORATION_INFO_CHANGE = 111; // 装扮变化的推送， 目前只有文案审核通过之后发送  profile_.proto DecorationInfoChangePush
      CHANNEL_ENTER_EXTRA_PUSH = 112; // 正在发布的房间，打开进房通知开关时，额外推送 channel_.proto ChannelEnterSimpleMsg
      STAY_ADD_USER_EXTRA_TICKET = 113; /*推送用户额外票 StayAddTicketInfo */

      ACTIVITY_POP_WINDOW_PUSH_NEW = 114; // 活动弹窗通知（新版） activity_push_.proto ActivityPushMsg
      PIA_DIALOGUE_INDEX_LOCATION_PUSH = 115; // pia戏段落定位推送 pia_.proto PiaDialogueIndexLocationMsg
      PIA_DIALOGUE_INDEX_FOLLOW_PUSH = 116; // pia戏段落跟随推送 pia_.proto PiaDialogueIndexFollowMsg

      PERFECT_COUPLE_GAME_CLUES_PROP_RESULT_PUSH = 117; // 天配房间玩法 使用线索道具结果推送 see perfect_couple_match_logic.proto CluesProp
      MIJING_INVITE_TO_PLAY = 118; // 拼场邀请弹窗
      PERFECT_MATCH_MATCH_RESULT_PUSH = 119; // 天配玩法匹配结果推送
      PERFECT_MATCH_MATCH_INFO_CHANGE_PUSH = 120; // 天配玩法匹配信息更新推送

      WEB_IM_PUSH = 121; // web im 消息统一推送

      REVENUE_EXT_GAME_JOIN_CAMP = 122; //  营收外部互动游戏-用户加入阵营通知 see app\revenue_ext_game_logic\revenue_ext_game_logic.proto ExtGameJoinCampOpt

      SIGN_MULTIPLAYER_HALL_TASK_PUSH = 123;  //  多人互动成员大厅任务进度更新推送 contract.proto MultiPlayerHallTaskListMsg

      JOIN_FANS_WELCOME_TEXT_PUSH=124;   //加入粉丝团欢迎文案   muse_social_community_logic.proto

      PGC_TICKET_LIST_USER_PUSH = 125;   // 用户pgc房间可用体验券信息推送 see pgc_channel_logic.proto的 UserTicketListPushMsg

      HOME_TEAM_PUSH = 126;  // 谜境首页小分队推送 mijing_playmate_logic.proto -> HomeTeamPush

      MIJING_NEW_TRIFLE_RECORD_PUSH = 127; // 谜境新小事记录解锁推送 mijing_trifle_logic.proto -> NewTrifleRecordPush

      CHANNEL_LIVE_MULTI_PK_APPLY_PUSH = 128; // 直播间多人pk邀请推送 see channel_live_proto_.proto MultiPkApplyPushMsg
      CHANNEL_LIVE_MULTI_PK_APPLY_RES_PUSH = 129; // 直播间多人pk邀请结果推送 see channel_live_proto_.proto MultiPkApplyResPushMsg
      CHANNEL_LIVE_MULTI_PK_APPLY_KNIGHT_PUSH = 130; // [废弃]直播间多人pk骑士在房信息推送 see channel_live_proto_.proto MultiPkKnightInfo

      CHANNEL_LIVE_MULTI_PK_MATCH_PUSH = 131; // 直播间多人pk匹配信息推送 see channel_live_proto_.proto MultiPkMatchInfo

      TOPIC_CHANNEL_USER_WARN_PUSH = 132; // 主题房个性化处罚：连带麦上用户限制进房5min&限制发布主题房  TopicChannelUserWarnNotifyMsg
      MIJING_PAY_ORDER_STATUS_PUSH = 133; // 支付订单状态通知

      AI_PARTNER_PUSH = 134; // AI伴侣统一推送

      ACTIVITY_BIG_RACE_POP_WINDOW_PUSH = 135; // 大型赛事活动弹窗通知 activity-push_.proto ActivityPushMsg

      MIJING_SCENARIO_MATCH_PUSH = 136; // 谜境快速匹配用户匹配结果推送
      MIJING_SCENARIO_MATCH_BIND_PUSH = 137; // 谜境快速匹配用户匹配完成确认结果推送

      PRESENT_WALL_SWITCH_CHANGE = 138; // 礼物墙开关状态改变 see present_wall_logic.proto PresentWallSwitchChange
      PRESENT_ILLUSTRATION_SWITCH_CHANGE = 139; //礼物图鉴开关状态变更 present_illustration_logic.proto PresentIllustrationSwitchChange

      GAME_PAL_CARD_STATUS_PUSH = 140; //搭子卡熄灭推送
      MIJING_PARTNER_CARD_MATCH_PUSH = 141; // 谜境找搭子匹配成功推送
      MUSE_SOCIAL_COMMUNITY_GROUP_TASK_DONE = 142;//兴趣社团群聊任务完成推送

      SCENARIO_MATCH_SHAKE_IT_PUSH = 143; // 谜境快速匹配用户匹配成功后双方交互

      ESPORT_ORDER_CHANGE_PUSH = 144; // 电竞指导订单变更 see esport_logic.proto OrderChangeNotify

      GAME_PAL_REMIND_REPLY_PUSH = 145; // 提醒回复游戏搭子消息推送

      ESPORT_SWITCH_STATUS = 146; // 电竞指导 开关状态
      MIJING_PARTNER_CARD_PROMPT_AVATAR_PUSH = 147; // 谜境搭子泼墨体生成虚拟形象进度结果推送

      USER_GAME_RATE_PUSH = 148; // 开黑用户评价提醒推送

      ROI_HIGH_POTENTIAL_PAY_USER_PUSH = 149; // roi高潜付费用户弹窗推送 see revenue_base.proto RoiHighPotentailNotify

      SMASH_EGGS_BREAKING_EVENT_V2 = 150;	// 魔力转转大事件v2 see SmashEggBreakingNews

      SVIP_PRIVILEGE_STEALTH_PUSH = 151; // svip权益在线隐身状态变更推送 see SVIPPrivilegeStealthPushMsg

      TBEAN_PRESENT_DOYEN_VERIFY_PUSH  = 152; // 新版本首次收T豆礼物的达人认证弹窗提示 see TbeanPresentDoyenVerifyPushMsg

      INVITE_ROOM_STATUS_PUSH = 153; // 邀请进房状态变更推送

      STAR_TRAIN_USER_JOIN_CHANGE = 154;      // 摘星列车用户参与推送, see star_train_logic.proto StarTrainUserJoinNotify
      STAR_TRAIN_BINGO_NOTIFY = 155;          // 摘星列车用户中奖弹窗, see star_train_logic.proto StarTrainAwardNotify

      INVITE_ROOM_PUSH = 156; // 邀请进房推送

      REVENUE_ROOM_RECOMMEND_RELATIONSHIP_MSG_PUSH = 157;  // 废弃，不用 营收房推荐关系链消息推送 see channel_.proto RevenueRoomRecRelationPushMsg

      SEND_GAME_APPOINTMENT_PUSH = 158; //触发预约邀请推送 see im_guide_logic.proto SendAppointmentPush
      RECEIVE_GAME_APPOINTMENT_PUSH = 159; //收到预约邀请推送 see im_guide_logic.proto ReceiveAppointmentPush
      GAME_APPOINTMENT_RESULT_PUSH = 160; //预约结果推送，包括超时 see im_guide_logic.proto see AppointmentResultPush
      GAME_APPOINTMENT_REMIND_PUSH = 161; //预约提醒在线推送 see im_guide_logic.proto see AppointmentRemindPush

      VIRTUAL_AVATAR_GAIN_NEW_NOTIFY = 162; // 虚拟形象获得推送 see virtual_avatar_logic.proto VirtualAvatarGainNewNotify

      PRESENT_SET_UNLOCK_PUSH = 163; // 礼物设置解锁推送 see present_go_logic_.proto PresentSetUnlockMsg

      CHANNEL_FOLLOW_FLOAT_MSG_PUSH = 164;  //  房间跟随浮层推送 see channel_recommend_logic_.proto ChannelFollowFloatMsg

      ESPORT_COUPON_PUSH = 165; // 电竞指导优惠券变更推送 see esport_logic.proto CouponChangePushMsg
      EMPEROR_SET_PRESENT_PUSH = 166; // 帝王套礼物推送 see present_go_logic_.proto EmperorSetPresentInfo

      GAME_HALL_MSG_PUSH = 167; // 组队大厅消息推送
      GAME_HALL_JOIN_TEAM_PUSH = 168; // 组队大厅参与组队推送
      GAME_HALL_AT_MSG_PUSH = 169; // 组队大厅@消息推送
      GAME_RED_DOT_UPDATE_PUSH = 170; // 红点信息更新推送 see game_red_dot_logic.proto RedDotUpdateNotify
      NON_PUBLIC_POST_AUDIT_RESULT_PUSH=171;  //讨论贴审核结果推送  see ugc_non_public.proto
      CHANNEL_GIFT_PK_INFO_CHANGE = 172; // 房间礼物PK信息变更推送 see channel_gift_pk.proto ChannelGiftPkInfoChangeOpt
      //CHANNEL_GIFT_PK_MATCH_TIMEOUT = 173; // 房间礼物对决匹配超时  see channel_gift_pk.proto ChannelGiftPkMatchTimeoutOpt

      VIP_PACK_UPDATE_PUSH = 174; // vip礼包更新推送 see numeric-logic_.proto VipPackUpdatePushMsg
      PUSH_NOTIFY = 175; // 推送通知，pushd实现旧通道的notify消息
      NewFirstRechargeFinPush = 176; // 新首充完成推送 see tt_rev_common_logic.proto NewFirstRechargeFinNotify

      NewRicherBirthdayPush = 177; // 新大R生日推送 see richer_birthday_logic.proto PushNewRicherBirthday

      PGC_TITLE_REMIND_CHANNEL_IM = 178;  // pgc用户冠名公屏提醒 see pgc_channel_logic.proto UserTitleImRemindInfo
      PGC_TITLE_REMIND_CHANNEL_POP = 179;  // pgc用户冠名弹窗提醒 see pgc_channel_logic.proto UserTitlePopRemindInfo
      PGC_TITLE_USER_CHANNEL_ENTER_PUSH = 180;  // pgc冠名用户进房提示 see pgc_channel_logic.proto TitleUserEnterChannelMsg

      GAME_PAL_CARD_AUDIT_RESULT_PUSH = 181; // 开黑搭子卡审核结果推送

      USER_ONLINE_REMIND_PUSH = 182; // 用户上线提醒推送 see im_guide_logic.proto UserOnlineRemindPush

      FRIEND_RETURN_ONLINE_REMIND_PUSH = 183; // 好友回流上线提醒推送 see interact_guide_logic.proto FriendReturnOnlineRemindPush

      USER_ENTER_ROOM_PUSH = 184; // 进房提醒推送 see game_play_logic.proto UserEnterRoomPush

      LIVE_AWARD_CHANNEL_INFO_CARD_GAIN_PUSH = 185; // 直播奖励房间信息卡片获得推送 see profile_.proto LiveAwardInfoCardNotify

      VIRTUAL_IMAGE_DISPLAY_STATUS_NOTIFY_POP = 186;  // 虚拟形象二期用户外显状态提醒弹窗 see virtual_image_logic.proto VirtualImageDisplayStatusPop
      VIRTUAL_IMAGE_DISPLAY_STATUS_CHANNEL_IM = 187; // 虚拟形象二期用户外显状态提醒房间公屏 see virtual_image_logic.proto VirtualImageDisplayStatusChannelNotify

      GAME_COMMON_H5_PUSH = 188; // 开黑通用H5推送 see web_im_logic.proto CommonH5PushMsg
      WEB_COMMON_H5_PUSH = 189; // web H5通用推送  see web_common_h5_logic.proto WebCommonH5PushMsg
      ONE_KEY_FIND_COACH_SUCCESS_NOTIFY = 190; // 一键找教练成功通知 see esport_logic.proto OneKeyFindCoachSuccessNotify
      COMMON_TOP_RICH_TEXT_DIALOG_NOTIFY = 191; // 通用顶部富文本弹窗通知 see CommonTopRichTextDialogNotify
      ONE_KEY_FIND_COACH_NOTIFY = 192; // 一键找人相关通知 see esport_logic.proto OneKeyFindCoachNotify
      PC_AUTH_MOBILE_PUSH = 193; // PC-移动端授权 see auth.proto PcAuthMobilePushInfo
      FLASH_CHAT_CP_TODAY_PUSH = 194; // 闪聊CP今日推送 see muse_allocate_logic.proto ViewPersonalHomepageReport
      AI_CUPID_RECALL_PUSH=195;//AI红娘次日召回消息
      AI_CUPID_STATUS_CHANGE_PUSH=196;//AI红娘状态变更通知
      AI_INSPIRATION_PUSH=197;//AI灵感回复推送 inspirations通知
      WEDDING_PAID_NOTIFY = 198; // 婚礼付费通知 see wedding_logic.proto WeddingPaidNotify
      
      WEB_GROUP_PUSH = 199; // web 群组 消息推送
      WEDDING_PROPOSE_PUSH = 200; //求婚弹窗  see channel_wedding_logic.proto WeddingProposeInfo

      VIRTUAL_IMAGE_MALL_FREE_COMMODITY_GAIN_PUSH = 201; // 虚拟形象商城免费商品获得弹窗 see virtual_image_logic.proto FreeCommodityGainNotify
      WEDDING_BIG_SCREEN_CHANGE_NOTIFY = 202; // 婚礼大屏变更通知 see channel_wedding_logic.proto WeddingBigScreenChangeNotify
      MUSE_GREETING_PUSH = 203; // muse问候推送 see muse_role_play_logic.proto MuseGreetingPush
      MUSE_ROLE_PLAY_SUCCESS_MATCH_CP_PUSH=204;//muse角色扮演成功匹配CP推送   SEE muse_role_play_logic.proto SuccessMatchCPMsg
      MUSE_ROLE_PLAY_SEND_MATCH_CP_REQUEST_PUSH=205; // muse角色扮演发送匹配CP请求推送 see muse_role_play_logic.proto MatchCpRequestSentSuccessfullyMsg
      MUSE_ROLE_PLAY_RECEIVE_MATCH_CP_REQUEST_PUSH=206; // muse角色扮演收到匹配CP请求推送 see muse_role_play_logic.proto ReceviedMatchCpRequestMsg
      MUSE_ROLE_PLAY_CP_RELATION_CHANGE_PUSH=207; // muse角色扮演CP关系变更推送 see muse_role_play_logic.proto AIRolePlayChangeStatusPush
      MUSE_ROLE_PLAY_MY_ROLE_CARD_CHANGE_PUSH=208; // muse角色扮演我的角色卡变更推送 see muse_role_play_logic.proto MyRoleCardChangePush
      COMMON_RICH_TEXT_POPUP =209; // 通用富文本弹窗通知 see CommonRichTextPopup
      COMMON_HIGHLIGHT_CHANNEL_IM = 210; // 通用高亮房间公屏通知 see CommonHighLightChannelIm
      UGC_POST_ATTITUDE_PUSH = 211;//帖子点赞推送   see  ugc_.proto UgcPostAttitudeNotify(目前只有PC接入)
      VIRTUAL_IMAGE_CARD_STATUS_CHANGE_NOTIFY = 212; // 购买虚拟形象无限卡推送 see virtual_image_logic.proto VirtualImageCardStatusChangeNotify
      COMMON_RMB_PAY_RESULT_NOTIFY = 213; // 通用rmb支付结果推送 see CommonRmbPayResultNotify
      MUSE_ROLE_PLAY_SAY_HELLO_REMAIN_TIMES_NOTIFY = 214; //角色扮演剩余打招呼次数推送 see muse_role_play_logic.proto SayHelloRemainTimesNotify
      NEW_WEALTH_GOD_NOTIFY = 215; // 新生成财神通知 see wealth_god_logic.proto NewWealthGodNotify
      AUTO_OPEN_WEALTH_GOD_BOX_REWARD_RESULT_NOTIFY = 216; // 自动开启财神宝箱奖励结果通知 see wealth_god_logic.proto AutoOpenWealthGodBoxRewardResultNotify
      WEALTH_MISSION_FINISH_NOTIFY = 217; // 财神任务完成通知 see wealth_god_logic.proto WealthMissionFinishNotify
      BACKPACK_INTIMATE_PRESENT_CONTRACT_PUSH = 218; // 首次收到背包亲密礼物提醒签约推送 see userpresent_.proto BackpackIntimatePresentContractPush
      NOTIFY_WEALTH_ACTIVITY_START = 219; // 财神活动开始通知 see wealth_god_logic.proto NotifyWealthActivityStart
    }
    required uint32 cmd = 1;
    required bytes content = 2;
    optional uint32 seq_id = 3;         // for Reliable Push
    optional bytes message_id = 4;      // message unique id
    optional uint64 server_time = 5;    // nanosecond
    optional string request_id = 6;     // push 通道
}

// 通知消息，Pushd发给客户端兼容旧通道的通知类消息
// CMD: PUSH_NOTIFY
message PushNotify {
    required uint32 sync_type = 1;    // 等同于sync.proto的SyncReq.SyncType
    optional bytes extend_multi = 2;  // 兼容旧代码追加NotifyExtendMulti内容的场景，新功能禁止使用，需要携带其余信息请改用push消息
}


// Reliable Push
message MessagesReceivedAckReq {
    required BaseReq base_req = 1;
    repeated uint32 seq_id_list = 2;
}

message MessagesReceivedAckResp {
    required BaseResp base_resp = 1;
}

message PullOfflineMessagesReq {
    required BaseReq base_req = 1;
    required uint32 begin_seq_id = 2;//闭区间[begin, end]
    required uint32 end_seq_id = 3;//闭区间[begin, end]
}

message PullOfflineMessagesResp {
    required BaseResp base_resp = 1;
    repeated PushMessage messages = 2;
    required uint32 latest_seq_id = 3;
}

//////////////////////////////////////////////////////////
// Push Message
// cmd: PULL_LOG
message PullLogMessage {
    optional string start_time = 1;	// 时间区间起始
    optional string end_time = 2;	// 时间区间结束
    optional bool force = 3;        // 强制拉取
    optional bytes pull_log_context = 4;    // 客户端上报时原样带回
    optional string original_command = 5;   // 原始拉取命令
}

// 游戏圈有新信息流notify
// cmd: CIRCLE_UPDATE
message CircleUpdateMessage {
    required TopicUpdateInfo topic_update_info = 1 ;
}

//cmd: CHANNEL_MSG
//@see channel_proto # ChannelMsg 只是用于自己被踢出频道、踢下麦的通知


//cmd: BULLETIN
//
message BulletinMessage{
    enum BulletinType{
        SERVER_MAINTENANC_FINISH = 0; //服务器已完成维护
        SERVER_WILL_MAINTENANC   = 1; //服务器即将要进行维护
    }
    required uint32 type = 1;
    required uint32 eventtime = 2;      //事件唯一时间戳，同一时间戳表示同一事件
    optional string eventsubject = 3;		//运维公告标题摘要, type为1时才带有
    optional string eventurl = 4;		    //特别指定运维公告地址
}

//cmd: CHANNEL_CONVENE
//
message ChannelConveneMessage{
    enum ChannelConveneType{
        START_CONVENT = 1;	//开启频道召集(发给已在频道的用户)
        CANCEL_CONVENE = 2;	//取消频道召集(发给已在频道/已响应频道召集的用户)
        CONFIRM_COUNT = 3; 	//召集人数变化(发给已在频道的用户)

        USER_CONVENE = 10;	//用户被召集(发给已收藏频道的用户)
        USER_URGE = 11;		//用户被“催一下”(发给已收藏频道的用户)
    }

    required uint32 type = 1;	//ChannelConveneType
    required uint32 channel_id = 2;
    optional uint32 confirm_count = 3;	//已召集人数
    optional uint32 convene_ts = 4;	// 当次召集时间
    optional uint32 valid_convene_ts = 5;	//可发起召集时间
}

message TTActivityMessage {
    enum PLATFORM {
        ANDROID = 0;
        IOS = 1;
    }
    enum TYPE_MASK {
        NORMAL = 0;					// 普通状态
        DISAPPEAR_ONLY_CLICK = 1;	// 仅点击才会消失（滑动不消失）
    }
    required uint32 act_id = 1;
    required string act_title = 2;
    required string act_url = 3;
    optional uint32 platform = 4; // SEE PLATFORM
    optional uint32 type_mask = 5;
    optional string act_sub_title = 6;
    optional string act_pic_url = 7;

    optional string push_type = 8;
    optional string opt_user = 9;
    optional string task_id = 10;
    repeated string labels = 11;

    optional string icon_url = 12;   // 推送图片url
    optional string voice_type = 13; // 推送声音
    optional string icon_type = 15;  // 推送图片url类型 eg: small , big
    optional string content_id = 16; // 推送文案id
}

message TTActivityMultiMessage {
    repeated TTActivityMessage msg_list = 1;
}

message TTCommonSystemMultiMessage {
    repeated TTActivityMessage msg_list = 1;
}

message TTNotifyBarMessage {
	optional uint32 app_id = 1;  // see EAppID. ex: 0=TT 11=HC
    repeated TTActivityMessage msg_list = 2;
}

// 全服礼物大事件
message ChannelPresentBreakingNews {
    required uint32 from_uid        = 1;    // 送礼者
    required string from_account    = 2;
    required string from_nick       = 3;

    optional uint32 target_uid      = 4;   // 收礼者
    optional string target_account	= 5;
    optional string target_nick		= 6;

	optional uint32 channel_id = 7;           // 涉及大事件的 房间ID
	optional uint32 channel_displayid = 8;    // 涉及大事件的 房间displya_ID
	optional uint32 channel_bindid = 9;       // 涉及大事件的 房间bind ID
	optional uint32 channel_type = 10;        // 涉及大事件的 房间类型
	optional string channel_name = 11;        // 涉及大事件的 房间名称

	optional string gift_name = 12;         // 涉及大事件的 礼物名称
	optional uint32 gift_id = 13;           // 涉及大事件的 礼物ID
	optional string news_prefix = 14;		// 涉及大事件的	文案前缀

	optional string from_face_md5 = 15;		// 送礼者的头像md5 如果为空 表明 这个用户没有设置头像
	optional string target_face_md5 = 16;	// 收礼者的头像md5 如果为空 表明 这个用户没有设置头像
	optional uint32 delay_secs = 17;		 // 延迟n秒后再播放大事件（客户端那边还没实现，坑货产品又说暂时不用做。。。）

}

// 我的头像装饰框消息（获得 or 升级）
message MyHeadwareMsg {

	required uint32 uid        = 1;
	optional UserHeadwearInfo headwrar_info = 2;

}

// 全服通用大事件
message CommonBreakingNews {

    required string title		= 1;  // 标题
	optional string jump_url 	= 2;  // 跳转地址

}

// 相亲全服大事件
message DatingBreakingNews
{
    required uint32 from_uid        = 1;
    required string from_account    = 2;
    required string from_nick       = 3;
	optional string from_face_md5   = 4;		// 头像md5 如果为空 表明 这个用户没有设置头像


    optional uint32 target_uid      = 5;
    optional string target_account	= 6;
    optional string target_nick		= 7;
	optional string target_face_md5 = 8;	// 头像md5 如果为空 表明 这个用户没有设置头像

	optional string dating_scene_name = 9;    // 相亲场景

	optional uint32 channel_id = 10;           // 涉及大事件的 房间ID
	optional uint32 channel_displayid = 11;    // 涉及大事件的 房间displya_ID
	optional uint32 channel_bindid = 12;       // 涉及大事件的 房间bind ID
	optional uint32 channel_type = 13;        // 涉及大事件的 房间类型
	optional string channel_name = 14;        // 涉及大事件的 房间名称
	optional uint32 delay_secs = 15;		 // 延迟n秒后再播放大事件（客户端那边还没实现，坑货产品又说暂时不用做。。。）

}

// 魔力转转大事件
message SmashEggBreakingNews
{
	enum MORPH_FLAG {
        READY = 0;
        START = 1;
    }

    optional uint32 current_hits    = 1;

    optional uint32 morph_hits    	= 2;
	optional uint32 morph_flag    	= 3;
	optional uint32 morph_end_time  = 4;
  optional uint32 cur_activity_type = 5;    // 当前活动版本 A/B,see smash-egg-logic_.proto CurActivityType
  optional string content = 6;    // 提醒文案
  optional string icon_url = 7;   // 图标
}

// 通用全服大事件
message CommonBreakingNewsV2
{
    optional uint32 from_uid        = 1;
    optional string from_account    = 2;
    optional string from_nick       = 3;
	optional string from_face_md5   = 4;	  // 头像md5 如果为空 表明 这个用户没有设置头像

	optional uint32 channel_id = 5;           //  涉及大事件的 房间ID
	optional uint32 channel_displayid = 6;    //  涉及大事件的 房间displya_ID
	optional uint32 channel_bindid = 7;       //  涉及大事件的 房间bind ID
	optional uint32 channel_type = 8;         //  涉及大事件的 房间类型
	optional string channel_name = 9;         //  涉及大事件的 房间名称

	optional string news_prefix = 10;		 // 涉及大事件的 文案前缀
	optional string news_content = 11;		 // 涉及大事件的 文案

	optional string jump_url = 12;           // 跳转URL
	optional uint32 delay_secs = 13;		 // 延迟n秒后再播放大事件（客户端那边还没实现，坑货产品又说暂时不用做。。。）
    optional uint32 nobility_level = 14; //贵族等级
    optional string nobility_level_name = 15; //贵族等级名
}

message CommBreakingNewsBaseOpt
{
    //触发条件
    enum TRIGGER_TYPE {
      SEND_CASTLE = 1;                //送城堡
      SEND_TEN_THOUSAND_PRESENT = 2;  //一次送礼物达1万元
      LIST_OF_ANNUAL_EVENTS = 3;      //年度盛典榜单每赛段结算 未使用
      ROCKET_FIRST_FLY = 4;           //火箭第一次起飞
      ROCKET_MORE_FLY = 5;          //火箭第二次及以上起飞
      TOPEST_DATING_SCENE = 6;        //最高级相亲牵手场景
      EMPEROR_UPGRADING  = 7;         //帝皇升级优化
      HERO_UPGRADING = 8;             //枭雄升级
      SMASH_EGGS_GET_HONOUR_PRESENT = 9;   //砸蛋抽中荣耀水晶礼物
      SEND_HONOUR_PRESENT = 10;       //送出荣耀水晶礼物

      LIST_OF_ANNUAL_EVENTS_RICHER = 11;   //年度盛典榜单结算 神壕榜
      LIST_OF_ANNUAL_EVENTS_PERSONAL = 12; //年度盛典榜单结算 个人榜
      LIST_OF_ANNUAL_EVENTS_CP = 13;       //年度盛典榜单结算 CP榜
      LIST_OF_ANNUAL_EVENTS_GUILD = 14;    //年度盛典榜单结算 公会榜
      TOPEST_DATING_SCENE_LV6 = 15;        //lv6 相亲牵手场景
      UNIVERSITY_EMPEROR_UPGRADING  = 16;         //苍穹之主升级优化
      MOLE_BEAT_BIGAWARD_INFO  = 17;         //打年兽游戏完后中大奖全服通告
      CHANNEL_HOUR_RANK_EVENT = 18;       // 房间小时榜结算（仅推送全网房间内的用户）
      CHANNEL_NOBILITY_LEVEL_UP = 19;     //贵族升级全服广播
      CHANNEL_NOBILITY_KEEP_SUCCESS = 20; //贵族保级成功
      COMMON = 21;                        //通用样式，CommonBreakingNewsV3中 is_old_deal 需填 1
      TOPEST_DATING_SCENE_LV7 = 22;        //lv7 相亲牵手场景
      SMASH_EGG_BINGO = 23;             // （6.47.0 废弃）新版转转中水晶播报，不可点击
      LIST_OF_CP_520 = 24;        //520活动 CP榜结算
      KAIXUE_BIGAWARD_INFO  = 25;         //开学季游戏完后中大奖全服通告
      MASKED_PK = 26;               //蒙面pk
      CHANNEL_NOBILITY_EXTEND_TIME = 27; //9级贵族延长时间成功
      ANNUAL_EVENTS_BONUS_BUFF_LIVE = 28;  //年度盛典守擂成功获得积分buff 直播房
      CHANNEL_NOBILITY_SPECIAL_GIFT = 29; //贵族特权礼物推送
      CHANNEL_NOBILITY_GODLEVEL_LEVELUP = 30; //贵族神王升级

      LIST_OF_ANNUAL_EVENTS_MVP = 31;  //年度盛典榜单结算 mvp榜

      MAGIC_PRESENT_SUPER_AWARD = 32;  //幸运礼物超级大奖

      SMASH_GOLD_EGG_BINGO = 33; //金色转转中水晶，已废弃

      CHANNEL_CP_GAME_BIG_SCORE = 34;  //cp战结算pk值达到金额100wT豆
      CHANNEL_CP_GAME_SUPER_SCORE = 35;  //cp战结算pk值达到金额200wT豆

	  LEVELUP_PRESENT_CHANNEL_LEVELUP = 36;	//升级礼物当用户礼物升级，在当前房间内出现横幅公告
	  LEVELUP_PRESENT_ALL_BEFORE_50 = 37;	//升级礼物每个礼物，每个等级前50名先升级的用户，给予全服公告
	  LEVELUP_PRESENT_ALL_BATCH_1W = 38;	//升级礼物批量送出大于1万人民币时，触发该全服

      SEND_DARK_GODDESS = 39;	//送出黑暗女神礼物
      COMPOSE_DARK_GODDESS = 40;  //合成黑暗女神礼物
      ONE_PIECE_BINGO_AWARD = 41; // 航海寻宝大奖全服
	    SEND_ONE_PIECE_AWARD = 42; // 送出航海寻宝全服礼物

      STAR_TREK_BINGO_AWARD = 43; // 星级巡航大奖全服
      ACTIVITY_COMMON_USER = 44;  // 活动用户通用全服样式
      ACTIVITY_COMMON_CHANNEL = 45;  // 活动房间通用全服样式

      OPEN_YOU_KNOW_WHO = 46; // 开通神秘人全服公告
      SEND_STAR_TREK_AWARD = 47; // 送出星级巡航大奖全服
      JOIN_KNIGHT_GROUP = 48; //加入骑士团全服推送

      ACTIVITY_HALLOWEEN_USER = 49;  // 万圣节用户全服样式
      ACTIVITY_HALLOWEEN_CHANNEL = 50;  //万圣节房间全服样式

      LIVE_CHANNEL_ANNUAL_GUILD = 51;  //语音年度公会
      SEND_CUSTOM_PRESENT = 52;  //送专属礼物

      PGC_CHANNEL_PK_GIFT_MAX = 53; // 跨房pk 全服公告

      ACTIVITY_SPRING_FESTIVAL_USER = 54; // 春节用户全服样式
      ACTIVITY_SPRING_FESTIVAL_COUPLE = 55; // 春节cp全服样式

      ACTIVITY_SPRING_FESTIVAL_2023 = 56; //2023春节全服公告 直播PK赛的全服

      TOPEST_DATING_SCENE_LV_1314 = 57;        //相亲牵手场景1314 全服公告

      CAT_CANTEEN_BINGO_AWARD = 58; // 猫猫餐厅大奖全服
      SEND_CAT_CANTEEN_AWARD = 59;  // 猫猫餐厅寻宝全服礼物
      FELLOW_PRESENT_SEND = 60; // 赠送挚友礼物

      LIVE_ANNIVERSARY_ANCHOR_COMPETITION_TOP3 = 61; // 直播周年庆主播赛TOP3
      LIVE_ANNIVERSARY_CONFERENCE_TOP3 = 62; // 直播周年庆公会赛（TOP3公会)
      LIVE_ANNIVERSARY_MVP_ANCHOR = 63; // 直播周年庆mvp主播
      RECREATION_CONFERENCE_COMPETITION_OF_POP_CP_PERSONAL = 64; // 娱乐公会赛 人气榜/cp榜/个人榜全服公告
      RECREATION_CONFERENCE_COMPETITION_OF_GUILD_GOD = 65; // 娱乐公会赛 公会榜/神豪榜全服公告

      SMASH_EGG_BINGO_WITH_RICH_TEXT_NEWS = 66;             // 转转大奖全服（带富文本消息）（6.47.0新增）

      HOUR_MEMBER_RANK_NEWS = 67; // 小时成员榜全服公告

      STAR_TRAIN_BINGO_WITH_RICH_TEXT_NEWS = 68; // 摘星列车中奖全服（带富文本消息）

      COMMON_RICH_TEXT_NEWS = 999; // 通用富文本消息,非特殊业务要求后续全服都走这个
    }

    //公告范围       枚举值采用二进制的向左移一位方式                 1(1), 10(2), 100(4)
    enum ANNOUNCE_SCOPE {
        INSIDE_CHANNEL = 1;              //房间内
        OUTSIDE_CHANNEL = 2;             //房间外
        PUBLIC_GUILD_CHANNEL = 4;        //公会公开房
        THIS_CHANNEL = 8;                //本房间内
        UGC_CHANNEL = 16;                //ugc房
    }

    //公告位置
    enum ANNOUNCE_POSITION {
        UPPER = 1;           //房间内的上面
        MIDDLE = 2;         //房间内的中间
    }

    //跳转的位置
    enum JUMP_POSITION {
       JUMP_TO_CHANNEL_CLICK = 1;           //点击跳转到对应房间
       JUMP_GIVEN_PAGE_CLICK = 2;           //点击跳转到指定页面
       JUMP_SMASH_EGGS_UI_CLICK = 3;        //点击跳转到对应房间砸蛋界面
       JUMP_ONE_PIECE_UI_CLICK = 4;         // 航海寻宝玩法半屏页
       JUMP_STAR_TREK_UI_CLICK = 5;         // 星级巡航玩法半屏页
       JUMP_CAT_CANTEEN_UI_CLICK = 6;       // 猫猫餐厅玩法半屏页
    }

    //点击跳转类型        3:房间内外     枚举值采用二进制的向左移一位方式                 1(1), 10(2), 100(4)
    enum JUMP_TYPE  {
        NO_JUMP_CLICK  = 0;                  //点击不跳转
        JUMP_CLICK_INSIDE = 1;               //房间内点击可跳转
        JUMP_ClICK_OUTSIDE = 2;              //房间外点击可跳转
    }

    optional uint32 trigger_type = 1;       //触发条件      see enum: TRIGGER_TYPE
	optional uint32 rolling_count = 2;      //滚动次数
	optional uint32 rolling_time = 3;       //每次滚动时长          sec
	optional uint32 announce_scope = 4;     //公告范围      see enum: ANNOUNCE_SCOPE
	optional uint32 announce_position = 5;  //公告位置      see enum: ANNOUNCE_POSITION
    optional uint32 jump_type = 6;          //点击跳转类型        see enum: JUMP_TYPE
    optional uint32 jump_position = 7;      //跳转的位置       see  enum: JUMP_POSITION
}

//送礼的一些基本属性
message PresentBreakingNewsBaseOpt
{
    optional string gift_name = 1;         // 涉及大事件的 礼物名称
	optional uint32 gift_id = 2;           // 涉及大事件的 礼物ID
	optional uint32 gift_count = 3;       //涉及大事件的 礼物数量
	optional string gift_icon_url = 4;    //涉及大事件的 礼物缩略图url
	optional uint32 gift_worth = 5;       //  涉及大事件的 礼物价值
  optional uint32 magic_id = 6;       //  幸运礼物 - 开出该礼物的幸运精灵id
  optional string magic_name = 7;       //  幸运礼物 - 开出该礼物的幸运精灵名称
  optional string magic_icon = 8;       //  幸运礼物 - 开出该礼物的幸运精灵icon
}

// 丢弃
message MonsterInfo {
    optional int64 monster_id = 1;
    optional string icon_url = 2;
    optional int64 count_down = 3; // 其他房间有龙的有效时间
}

message MonsterInfoV2 {
    optional int64  monster_id = 1;
    optional int64  count_down = 2;    // 其他房间有龙的有效时间
}

//通用全服大事件V3
message CommonBreakingNewsV3
{
	optional uint32 from_uid        = 1;
	optional string from_account    = 2;
	optional string from_nick       = 3;
	optional string from_face_md5   = 4;		// 头像md5 如果为空 表明 这个用户没有设置头像

	optional uint32 target_uid      = 5;
	optional string target_account	= 6;
	optional string target_nick		= 7;
	optional string target_face_md5 = 8;	// 头像md5 如果为空 表明 这个用户没有设置头像

	optional uint32 channel_id = 9;           // 涉及大事件的 房间ID
	optional uint32 channel_displayid = 10;    // 涉及大事件的 房间displya_ID
	optional uint32 channel_bindid = 11;       // 涉及大事件的 房间bind ID
	optional uint32 channel_type = 12;        // 涉及大事件的 房间类型
	optional string channel_name = 13;        // 涉及大事件的 房间名称

	optional string news_prefix = 14;		 // 涉及大事件的 文案前缀
	optional string news_content = 15;		 // 涉及大事件的 文案

	optional string jump_url = 16;           // 跳转URL
	optional uint32 delay_secs = 17;		 // 延迟n秒后再播放大事件

	optional string dating_scene_name = 18;    // 相亲场景

	optional CommBreakingNewsBaseOpt breaking_news_base_opt = 19;    //通用全服公告的一些基本属性

	optional PresentBreakingNewsBaseOpt present_news_base_opt = 20;   //送礼大事件的一些基本属性

	optional uint32 rich_level = 21;            //财富等级

	optional uint32 is_old_deal = 22;           //旧版客户端是否推送新的全服通知，旧版处理的话可以推送旧版文案内容 0：不处理                                      1：处理
	optional string old_news_content = 23;      //旧版文案内容

	optional uint32 guild_id = 24;          // 公会id
	optional string guild_name = 25;        // 公会名称
	optional string guild_face_md5 = 26;    // 公会头像md5
	optional uint32 guild_display_id = 27;  // 公会靓号id

	optional uint32 tag_id = 28;        // 房间所在分类tag的tag_id
	optional string channel_icon_md5 = 29; //房间头像md5

	optional uint32 nobility_level = 30; //贵族等级
	optional string nobility_level_name = 31; //贵族等级名
	optional MonsterInfo monster_info = 32; //打龙信息  丢弃
	optional RushInfo rush_info = 33;  // rush信息
	optional MonsterInfoV2 monster_info_v2 = 34;  // 打boss信息
	optional uint32 nobility_extent_cnt = 35; //贵族神王第几次延长
	optional bytes opt_data = 36;	//自定义pb序列化数据
    // 神秘人操作者信息 ga_base.proto
    optional UserProfile from_user_profile = 37;
    // 神秘人被操作者信息
    optional UserProfile target_user_profile = 38;
    optional string hard_url = 39;    // 支持区分不同马甲包链接，不为空时由客户端拼接域名进行跳转， 为空时使用jump_url跳转

  optional string channel_view_id = 40 ;  //新的房间显示id
  optional uint32 news_id = 41;                  // 富文本全服Id
  optional float rank = 42; // 播放排序, 越小优先级越高
}

message TBeanUpdateMsg{
    required uint32 uid     = 1;
    optional uint32 amount   = 2; // T豆数量
}

message RankChangeMsg {
    enum RankChangeTypeMask {
        DAY_CHARM = 1;
        DAY_RICH = 2;
    }
    required uint32 type = 1; // mask
}

// 通用弹窗消息
message CommonPopUpMsg{
	enum POP_UP_MSG_TYPE {
        MISSION_DAILY_CHECKIN = 1;		// 每日签到任务
    }

    required uint32 uid     = 1;
	required uint32 msg_type = 2;			// POP_UP_MSG_TYPE
    required string title   = 3; 			// 标题
	optional string award_icon_url = 4; 	// 标题下面的图标url
	optional string award_msg_up = 5;		// 奖励描述1
	optional string award_msg_down = 6;		// 奖励描述2
	optional string bonus_award_msg = 7;	// 额外的奖励描述(经验奖励下面的那行黄字)

	// 另外的特殊奖励（如每日签到的连签奖励）
	optional string extra_award_msg = 8;		// 特殊奖励的描述(已废弃)
	optional string extra_award_icon_url = 9;	// 特殊奖励的图标url(已废弃)
	optional uint32 extra_award_count = 10;		// 特殊奖励的数量(已废弃)
	optional uint32 extend_value = 11;			// (已废弃)

	optional string main_page_icon_url = 12;	// 主页图标url（每日签到中，先展示主页，用户点击后再显示奖励页）
	optional string background_url = 13;		// 背景url
	optional string award_button_url = 14;	    // 奖励按钮url

	// 原有的奖励描述要加上颜色。。。
	optional string award_msg_up_color = 15;		// 奖励描述1
	optional string award_msg_down_color = 16;		// 奖励描述2
	optional string bonus_award_msg_color = 17;		// 额外的奖励描述
}

message ActiveBreakingReportMsg
{
	optional uint32 timestamp = 1;
	optional string msg = 2;       //

}
message CommonWebActiveBreakingReport
{
	repeated ActiveBreakingReportMsg msg_list = 1;       //

	optional string back_img_url = 2;       // 底图URL
	optional string icon_img_url = 3;       // ICON URL

	optional string jump_url = 4;           // 跳转URL
}

message CommonWebActiveBreakingTopN
{
	repeated GenericMember topn_member_list = 1;      //
	optional string back_img_url = 2;       // 底图URL
	optional string icon_img_url = 3;       // ICON URL

	optional string jump_url = 4;           // 跳转URL
}

message GetCommonWebActiveBreakingInfoReq {
	required BaseReq base_req = 1;
}

message GetCommonWebActiveBreakingInfoResp {
	required BaseResp base_resp = 1;
	optional CommonWebActiveBreakingReport report_info = 2;
	optional CommonWebActiveBreakingTopN rank_topn_info = 3;
}

message MyDecorationMsg
{
	required uint32 uid = 1;
	optional UserDecorationInfo user_decoration = 2;
	optional uint32 rich_level = 3;
}

message AckNotificationReq {
    enum Event {
        ARRIVAL = 0;    //消息到达传
        CLICK = 1;      //消息到达点击传
        SHOW = 2;  //消息展示
    }
    enum PushChannel {
        UNKNOW = 0;
        IOS = 1;
        UPUSH = 2;
        OPPO = 3;
        VIVO = 4;
        GETUI_OFFLINE = 5;
        GETUI_ONLINE = 6;
        TT = 7;
    }

    required BaseReq base_req = 1;
    required string task_id = 2;        //推送消息体带的task id
    required Event event = 3;
    optional string device_id = 4;      //
    optional string push_type = 6;
    optional string opt_user = 7;

    optional int32 push_channel= 8; // 详见 quicksilver/push-server.proto [enum PushChannel] ios 1 getui offline/online 5/6 tt 7
    optional string device_model = 9; // 手机厂商信息
}

message AckNotificationResp {
    required BaseResp base_resp = 1;
}

// 年度盛典-活动弹窗
message YearActPopUpMsg{
    enum POP_UP_MSG_TYPE {
        FINISH_ENTRY_CHANNEL_MISSION = 1;     // 完成进任意房间任务的弹窗
        FINISH_OTHER_MISSION = 2;             // 完成其他任务的房间弹窗
    }

    required uint32 uid     = 1;
	required uint32 msg_type = 2;			// POP_UP_MSG_TYPE
    required string content   = 3; 			// 文本内容
    optional string button_jump_url = 4;    // 按钮跳转url
    optional string title = 5;              // 标题
}

// 年度盛典-实时榜单排名数据相关
// cp榜较特殊，单独定义结构
message CPItem{
    required uint32 uid = 1;
    required string nickname = 2;// 昵称
    required string account = 3; // 账号
    optional uint32 cid = 4;     // 所在房间id
}

message CPRankingItem{
    required CPItem invite_user = 1; // 邀请用户（放cp榜单左边）
    required CPItem cp_user = 2;     // 被邀请用户（放cp榜单右边）
    required string ranking = 3;    // 排名
    required string value = 4;      // 榜上分数
}

message RankingItem{
    required uint32 uid = 1;
    required string nickname = 2;   // 昵称
    required string account = 3;    // 账号
    required string value = 4;      // 榜上分数
    required string ranking = 5;    // 排名
    optional uint32 cid = 6;        // 所在房间id
}

message YearActRankingInfo{
    enum RANKING_TYPE{
        CP_RANKING = 1;                 // CP榜
        MALE_GOD_RANKING = 2;           // 男大神榜
        FEMALE_GOD_RANKING = 3;         // 女大神榜
        MALE_POPULARITY_RANKING = 4;    // 男人气榜
        FEMALE_POPULARITY_RANKING = 5;  // 女人气榜
        MALE_SINGER_RANKING = 6;        // 男歌手榜
        FEMALE_SINGER_RANKING = 7;      // 女歌手榜
        RICHER_RANKING = 8;             // 土豪榜
        KING_GUILD_RANKING = 9;         // 王者公会榜
        RISING_STAR_GUILD_RANKING = 10; // 新秀公会榜
    }
    required uint32 type = 1;           // 榜单类型 see RANKING_TYPE
    repeated RankingItem list = 2;    // 仅当type不为CP_RANKING时有效
    repeated CPRankingItem cplist = 3;  // 仅当type为CP_RANKING时有效
}

message YearActRankingListMsg{
    repeated YearActRankingInfo rankings = 1;
    optional uint32 version = 2;    // 版本号，填时间戳
}



// *****  实时榜单信息相关 begin ***** //

// 个人榜
message PersonalRankingInfo{
    required uint32 uid = 1;        //uid
    required string nickname = 2;   //昵称
    required string account = 3;    //账号
    required string value = 4;      //榜上分数
    required string ranking = 5;    //排名
    required uint32 cid = 6;     //所在房间
}
message PersonalRankingInfoList{
    repeated PersonalRankingInfo list = 1;
}

message CPInfo{
    required uint32 uid = 1;        //uid
    required string nickname = 2;   //昵称
    required string account = 3;    //账号
    required uint32 cid = 4;        //所在房间
}

// cp榜
message CpRankingInfo{
    required CPInfo invite_user = 1;  //邀请用户放左边
    required CPInfo cp_user = 2;      //被邀请用户 放右边
    required string ranking = 3;    //排名
    required string value = 4;      //榜上分数
}

message CpRankingInfoList{
    repeated CpRankingInfo list = 1;
}

// 工会榜
message GuildRankingInfo{
    required uint32 guildId = 1;       //工会id
    required uint32 displayId = 2;     //显示id
    required string name = 3;          //名称
    required string icon = 4;          //icon
    required string value = 5;         //榜上分数
    required string ranking = 6;       //排名
}

message GuildRankingInfoList{
    repeated GuildRankingInfo list = 1;
}

message Link{
    optional string text = 1;
    optional string url = 2;
    optional string color = 3;
}

// 直播房榜单样式
enum ANCHOR_RANKING_STYLE{
    COMMON_STYLE = 1;       // 常规样式
    QUALIFYING_STYLE = 2;   // 排位赛样式
}

// 榜单基本信息
message RankingListMsg{
    enum SUB_RANK_TYPE {
       REGULAR_PERSONAL_RANKING = 1;   //常规个人榜
       CP_RANKING = 2;       // cp榜
       GUILD_RANKING = 3;    // 工会榜
    }
    optional string name = 1;  // 榜单名字
    optional string id = 2;    // 榜单id
    optional string describe = 3;  //榜单描述
    optional uint32 type = 4;  // see enum SUB_RANK_TYPE
    optional bytes  data = 5;  //序列化后的相关榜单信息, type 1: PersonalRankingInfoList 2: CpRankingInfoList 3:GuildRankingInfoList
}
// 榜单推送结构
message PushRankingListMsg{
    enum RANK_TYPE {
       COMMON_RANKING_LIST = 1;    // 通用榜单类型
       ANCHOR_RANKING_LIST = 2;    // 主播榜单类型
    }
    optional uint32 type = 1;        // see RANK_TYPE
    optional uint32 version = 2;
    repeated RankingListMsg rankings = 3; //榜单信息
    optional Link link = 4;
}

message MyRankItem{
    optional string ranking = 1;  //排名
    optional string value = 2;    //榜单值
    optional string describe = 3;  //排名描述
}

//语音直播榜单信息
message LiveRoomRankingDetailItem{
    optional string id = 1;
    optional string name = 2;
    optional MyRankItem myRank = 3;
}

// 排位赛信息
message QualifyingInfo{
    optional string desc1 = 1;       //描述1
    optional string desc2 = 2;       //描述2
}

//语音直播榜单推送结构
message LiveRoomRankingPushMsg{
    optional uint32 style = 1;       //see ANCHOR_RANKING_STYLE
    repeated LiveRoomRankingDetailItem rankings = 2;//常规榜单
    optional QualifyingInfo qualifyingInfo = 3; //排位赛信息
    optional uint32 version = 4;
}


// *****  实时榜单信息相关 end ***** //

// cmd:ANTISPAM_VIOLATE_PUSH
message AntispamViolatePushMsg {
    enum ANTISPAM_TYPE{
        USER_NICKNAME = 1;     // 用户昵称
        USER_PROFILE = 2;      // 用户头像
        CHANNEL_NAME = 3;      // 房间名
        CHANNEL_PROFILE = 4;   // 房间头像
        CHANNEL_IM = 5;        // 房间公屏
    }
    required uint32 type = 1; // ANTISPAM_TYPE
    required uint32 errDef = 2;
    optional string data = 3; // md5 or nickname
    optional string content = 4;
}


// 打龙获得道具弹窗信息
message HuntMonsterPropsPopUpMsg {
   required uint32 uid = 1;
   required string mission_name = 2;     // 任务名称
   required string props_name = 3;      // 道具名称
   required uint32 props_cnt = 4;        // 本次获得道具数量
   required string jump_url = 5;         // 跳转任务页的url
   optional uint32 mission_type = 6;    // see ga_base.proto HuntMonsterMissionType
   optional bool   is_finish = 7;        // 任务是否已完成
   optional int64  update_ms = 8;        // 毫秒
}

enum InviteFromChannelInviteSource{
    InviteFromChannelInviteSource_DEFAULT = 0;
    InviteFromChannelInviteSource_MIJING_PIECING_GROUP = 1;
}

//房间内邀请消息
message InviteFromChannelMsg {
    required string from_account =1;//对方
    required string msg = 2;//文案
    required uint32 channel_id = 3;//房间号
    required uint32 channel_type = 4;//房间类型
    required string from_name = 5;//发起方名字
    optional uint32 invite_source = 6; //邀请来源，数据上报用 见InviteFromChannelInviteSource
    optional uint32 tab_id = 7; //玩法id数据上报用
    optional string trace_id = 8; //推荐追踪id 数据上报用
    optional uint32 from_uid = 9; //邀请者uid 数据上报用
}

//接受邀请消息
message ReplyInviteFromChannelMsg {
    required string account =1;//对方账号
    required bool accept = 2;//对方是否接受
    required int64 ts = 3;//此时时间
}

//玩伴在线push
message InvitePlayerOnlineMsg {
    required string account =1;//发出邀请的人的account
    required uint32 uid = 2;
    required string nickname = 3;//昵称
    optional bool is_special_friend = 4;//是否是特殊好友
    optional string special_friend_text = 5;//特殊好友文案
}

// 余额变动push
message BalanceChangeMessage {
  enum CHANGE_REASON{
    FELLOW_REFUND = 1;     // 挚友邀请被拒绝退款
  }
  required uint32 reason = 1; //CHANGE_REASON
  required int64 currency = 2; // 红钻余额 ， 负数为未发生变动
  required bool is_tbean_change = 3; // t豆余额是否变更
}

//公告信息
message GroupAnnounceMessage {
   required uint32 from_id  = 1;
   required string from_nick = 2;
   required string from_name = 3;
   required uint32 to_id  = 4;
   required string to_nick = 5;
   required string to_name = 6;//@Group
   required string content = 7;

}

// 震动类型
enum ShakeType {
  NotShake = 0;            // 默认不震动
  GeneralSingleShake = 1;  // 普通单次震动
  ImportantMultiShake = 2; // 重要多次震动
}

// 统一端内弹窗推送
message TTCommPopUpWindowPush {
  optional uint32 app_id = 1;  // see EAppID. ex: 0=TT 11=HC
  repeated TTActivityMessage msg_list = 2;
  optional CommPopUpWindowExtra extra = 3;    // 扩展
  optional PopUpWindowRetainInfo retain_info = 4;  // 挽回弹窗信息
}

// 弹窗二次挽留信息
message PopUpWindowRetainInfo {
  optional string retain_title = 1;     // 挽回标题
  optional string retain_content = 2;   // 挽回文案
  optional string retain_icon_url = 3;  // 挽回配置图
  optional string retain_button_content = 4; // 挽回按钮文案
  optional string retain_cancel_button = 5; // 挽回取消按钮
}

message CommPopUpWindowExtra {
  optional uint32 req_source = 1;    // 调用弹窗接口来源  enum: PopUpWindowReqSource
  optional string button_content = 2;  // 按钮文案
  repeated string highlight_content_list = 3;     // 高亮的内容
  optional string highlight_color = 4;     // 高亮的颜色
  optional uint32 show_time = 5;          // 单位 秒
  optional string background_image_url = 6;  //背景图
  optional string button_background_color = 7;  // 按钮背景颜色
  optional string button_content_color = 8;    //按钮文字颜色
  optional string push_content_color = 9;      // 推送内容颜色
  optional ShakeType shake_type = 10;          // 震动类型，默认不震动
  optional string business_identity = 11; // 业务标识，非必填
}

message CommonPlainTextToastPush {
  required uint32 uid = 1;
  required string content = 2;
}


message FollowEnterRoomOwnerPush {
    required string nickname = 1;//昵称
    required string intro_text = 2;    //文案
}

message TbeanPresentDoyenVerifyPushMsg{
   required string content = 1; // 文案
}

// 通用顶部富文本弹窗
message CommonTopRichTextDialogNotify {
    required string content = 1; // 富文本内容
    required uint32 announce_scope = 2; // 展示范围 see enum: ANNOUNCE_SCOPE
    optional uint32 duration = 3; // 显示秒数
    optional bool is_show_count_down = 4; // 是否显示倒计时
    optional string end_action_link = 5; // 结束动作链接
    optional string scene = 6; // 场景, 原则上不用于业务区分, 只做问题定位
}

// 通用富文本弹窗
message CommonRichTextPopup {
    required string content = 1; // 富文本内容
    required uint32 announce_scope = 2; // 展示范围 see enum: ANNOUNCE_SCOPE
    required string scene = 3; // 场景, 便于定位问题不做业务区分
}

// 通用rmb支付结果推送，客户端收到后回调给前端
message CommonRmbPayResultNotify {
  enum BusinessType {
      BUSINESS_TYPE_UNSPECIFIED = 0;
      BUSINESS_TYPE_VIRTUAL_IMAGE_CARD = 1; // 无限换装卡
  }
  required BusinessType business_type = 1; // 业务类型
  required bool result = 2; // true支付成功，false失败
  optional string reason = 3; // 如果失败，失败的理由
  optional string ext_msg = 4; // 扩展信息，是一个json，由客户端透传给前端
}
